apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeSecurityPolicy
metadata:
  name: ingress-ip-throttle
  namespace: security
  annotations:
    cnrm.cloud.google.com/deletion-policy: abandon
    # absent means CNRM won't take values from Google APIs
    # and stick them in the k8s object. This might work
    # better with lists (like spec rule) than the default
    # merge policy. We were seeing duplicated rules when
    # the policy was merge. Time will tell whether absent
    # fixes this. Another plus of absent is that we won't
    # apply changes every time, even when there isn't a problem.
    cnrm.cloud.google.com/state-into-spec: absent
spec:
  rule:
    # see https://www.notion.so/GeoIp-Blocking-1a0bba10175a80adb1f3d3d67d8ba008?pvs=4
    # check for region blocking before throttling
    - action: deny(403)
      description: Block traffic from Cuba, Iran, North Korea, Russia, Syria
      priority: 96
      match:
        expr:
          # see https://cloud.google.com/armor/docs/configure-security-policies and https://cloud.google.com/armor/docs/rules-language-reference
          # note: According to <PERSON>, the IP addresses originating from occupied regions of Ukraine as mapped to RU
          expression: origin.region_code == 'CU' || origin.region_code == 'IR'|| origin.region_code ==
            'KP' || origin.region_code == 'RU' || origin.region_code == 'SY'
    - action: throttle
      description: Throttle 1000 requests per 10 seconds from each IP
      match:
        config:
          srcIpRanges:
            - '*'
        versionedExpr: SRC_IPS_V1
      preconfiguredWafConfig: {}
      preview: false
      priority: 100
      rateLimitOptions:
        conformAction: allow
        enforceOnKey: IP
        exceedAction: deny(429)
        rateLimitThreshold:
          count: 1000
          intervalSec: 10
    - action: allow
      description: Default rule, higher priority overrides it
      match:
        config:
          srcIpRanges:
            - '*'
        versionedExpr: SRC_IPS_V1
      preconfiguredWafConfig: {}
      preview: false
      priority: 2147483647

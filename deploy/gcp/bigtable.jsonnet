local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
function(cloud)
  local instances = {
    // [suffix, namespace_name, minNodes, maxNodes]
    GCP_US_CENTRAL1_PROD: [
      ['central', 'central', 16, 128],
      ['central-staging', 'central-staging', 2, 8],
    ],
    GCP_EU_WEST4_PROD: [
      ['central', 'central', 2, 8],
      ['central-staging', 'central-staging', 2, 2],
    ],
    GCP_US_CENTRAL1_GSC_PROD: [
      // Originally created to support genie in the cluster
      ['central', 'central', 2, 2],
      // Instance name is too long - it's not even
      // being created.
      // ['central-staging', 'central-staging', 1, 1],
    ],
    GCP_US_CENTRAL1_DEV: [
      ['central-dev', 'central-dev', 1, 1],
      // Note this is intentional-- separate bigtable instance, same namespace
      ['central-test', 'central-dev', 1, 1],
    ],
  };

  // Create Bigtable instance for a cluster in the region of the cloud.
  //
  // We do not use shared bigtable instances with multiple clusters to avoid the automatic (and unwanted)
  // replication of data between clusters.
  local createInstance = function(cloud, suffix, namespace_name, minNodes, maxNodes)
    // maxNodes must be at most 10 times minNodes.
    assert maxNodes <= minNodes * 10;

    local displayName = if cloudInfo.isLeadProdCluster(cloud) || cloud == 'GCP_US_CENTRAL1_DEV' then
      'bigtable-%s' % suffix else
      if cloudInfo.isDevCluster(cloud) then 'bt-%s-%s' % [cloudInfo[cloud].shortName, suffix]
      else 'bigtable-%s-%s' % [cloudInfo[cloud].shortName, suffix];
    [
      {
        apiVersion: 'bigtable.cnrm.cloud.google.com/v1beta1',
        kind: 'BigtableInstance',
        metadata: {
          name: displayName,
          namespace: namespace_name,
          annotations: {
            'cnrm.cloud.google.com/deletion-policy': 'abandon',
            // Observed an interesting series of events:
            //    - created a bigtable instance using this k8s object
            //    - deleted the bigtable instance using the GCP console (not k8s)
            //    - config connector tried to recreate the bigtable instance
            //    - config connector issues an invalid createinstance command to bigtable admin APIs
            //
            // The createInstance command is invalid because it's passing both serveNodes
            // and autoscalingConfig to BigTable Admin. It's passing serveNodes because
            // spec.cluster.numNodes is set to 1. But how did it get set to 1? We never did that.
            //
            // I believe that config connector is syncing fields from the GCP object back to the
            // the k8s object. We should consider disabling that for bigtable instances using
            //'cnrm.cloud.google.com/state-into-spec': 'absent',
            //
            // Unfortunately, state-into-spec is immutable (you get an error if you try to change it).
            // Setting it on existing objects is not possible - they have to be deleted and recreated.
            // Figuring out how to do this is a bigger project than describing the problem. One idea
            // would be to create new BigTableInstance objects with new metadata.name but the same displayName,
            // and tombstone the old ones. Doesn't seem worth the effort as I write this...
          },
        },
        spec: {
          displayName: displayName,
          cluster: [
            {
              clusterId: 'bigtable-%s' % suffix,
              zone: cloudInfo[cloud].mainZone,
              storageType: 'SSD',
              // Uncommenting the next line MAY help if we ever run into problems
              // syncing after changing the autoscalingConfig.
              // numNodes: null,
              autoscalingConfig: {
                cpuTarget: 80,
                minNodes: minNodes,
                maxNodes: maxNodes,
              },
            },
          ],
        },
      },
    ];
  std.flatMap(
    function(n) createInstance(cloud, n[0], n[1], n[2], n[3]),
    instances[cloud],
  )

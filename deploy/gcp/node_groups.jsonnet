local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
function(cloud)
  local info = cloudInfo[cloud];
  local isDev = cloudInfo.isDevCluster(cloud);
  local isMainDev = cloudInfo.isMainDevCluster(cloud);
  local extraObjects = [
    if cloudInfo.isLeadCluster(cloud) then
      {
        apiVersion: 'compute.cnrm.cloud.google.com/v1beta1',
        kind: 'ComputeSSLPolicy',
        metadata: {
          name: 'restricted-ssl-policy',
          namespace: 'devtools',
        },
        spec: {
          description: 'An SSL Policy with a RESTRICTED encryption profile, supporting several modern methods of encryption for TLS 1.2 and up.',
          minTlsVersion: 'TLS_1_2',
          profile: 'RESTRICTED',
        },
      }
    else null,
    if cloudInfo.isLeadProdCluster(cloud) then
      {
        apiVersion: 'compute.cnrm.cloud.google.com/v1beta1',
        kind: 'ComputeProjectMetadata',
        metadata: {
          name: 'projectmetadata',
          namespace: 'devtools',
        },
        spec: {
          metadata: {
            'enable-oslogin': 'TRUE',
            'enable-oslogin-2fa': 'TRUE',
            // see https://cloud.google.com/compute/docs/connect/restrict-ssh-keys#resthttps://cloud.google.com/compute/docs/connect/restrict-ssh-keys#rest
            'block-project-ssh-keys': 'TRUE',
          },
        },
      } else null,
  ];
  // Fields that are the same for all node types (currently)
  local oauthScopes = [
    'https://www.googleapis.com/auth/logging.write',
    'https://www.googleapis.com/auth/monitoring',
    'https://www.googleapis.com/auth/devstorage.read_only',
    'https://www.googleapis.com/auth/cloud-platform',
    'https://www.googleapis.com/auth/trace.append',
  ];
  local management = {
    autoUpgrade: true,
    autoRepair: true,
  };
  // Tests use persistent volumes. Persistent volumes are bound to a zone. If there are no GPUs available in a zone,
  // then the test will not start. Use fewer zones in the hope of reducing the chance this will happen. Note we need to delete
  // the persistent volumes after changing this.
  local l4ZonesMap = {
    GCP_US_CENTRAL1_DEV: ['us-central1-a'],
    GCP_US_CENTRAL1_PROD: ['us-central1-a', 'us-central1-b', 'us-central1-c'],
    GCP_US_CENTRAL1_GSC_PROD: [],
    GCP_EU_WEST4_PROD: [],  // no L4 available in europe-west4
  };
  local l4Zones = l4ZonesMap[cloud];
  local c3ZonesMap = {
    // us-central1-f doesn't have c3 VMs (and we limit GCP_US_CENTRAL1_DEV zones for testing as described above).
    GCP_US_CENTRAL1_DEV: ['us-central1-a'],
    GCP_US_CENTRAL1_PROD: ['us-central1-a', 'us-central1-b', 'us-central1-c'],
    GCP_US_CENTRAL1_GSC_PROD: [],
    GCP_EU_WEST4_PROD: ['europe-west4-a', 'europe-west4-b', 'europe-west4-c'],
  };
  local c3Zones = c3ZonesMap[cloud];
  local h100ZonesMap = {
    // 2 from reservation: https://console.cloud.google.com/compute/reservations/detail/us-central1-a/shared-res?project=system-services-dev
    // 4 map to 1/4 the reservation: https://console.cloud.google.com/compute/reservations/detail/us-central1-c/shared-res-3?project=system-services-dev
    GCP_US_CENTRAL1_DEV: [
      { locations: ['us-central1-a'], count: 4, name: 'h100-gpu-pool' },
      { locations: ['us-central1-c'], count: 4, name: 'h100-gpu-c-pool' },
    ],
    // 12 map to 3/4 of the reservation: https://console.cloud.google.com/compute/reservations/detail/us-central1-c/shared-res-3?project=system-services-dev
    GCP_US_CENTRAL1_PROD: [
      { locations: ['us-central1-a'], count: 12, name: 'h100-gpu-pool' },
      { locations: ['us-central1-c'], count: 12, name: 'h100-gpu-c-pool' },
    ],
    // H100 node pools in GCP_US_CENTRAL1_GSC_PROD are managed via
    // reseach infra.
    GCP_US_CENTRAL1_GSC_PROD: [],
    // 3 from https://console.cloud.google.com/compute/reservations/detail/europe-west4-b/reservation-1?project=system-services-prod
    // 1 from https://console.cloud.google.com/compute/reservations/detail/europe-west4-b/shared-res-4?project=system-services-dev
    GCP_EU_WEST4_PROD: [
      { locations: ['europe-west4-b'], count: 4, name: 'h100-gpu-pool' },
    ],
  };
  local nodeGroups =
    [
      // Standard CPU node pool
      {
        apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
        kind: 'ContainerNodePool',
        metadata: {
          name: 'pool-cpu',
          namespace: 'devtools',
        },
        spec: {
          location: info.region,
          clusterRef: {
            external: info.clusterName,
          },
          nodeConfig: {
            machineType: 'e2-standard-16',
            diskSizeGb: 100,
            oauthScopes: oauthScopes,
            imageType: 'COS_CONTAINERD',
            shieldedInstanceConfig: {
              enableSecureBoot: true,
              enableIntegrityMonitoring: true,
            },
          },
          // config connector doesn't support auto-scaling with total counts
          autoscaling: {
            locationPolicy: if isDev then 'BALANCED' else 'ANY',
            minNodeCount: 0,
            // Keep this in sync with lowE2Spec in tools/monitoring/monitoring.jsonnet
            maxNodeCount: if isDev then 64 else 512,
          },
          management: management,
        },
      },
      // Premium (modern arch, fast SIMD, currently for embeddings-search-cpu) CPU node pool
      if std.length(c3Zones) > 0 then
        {
          apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
          kind: 'ContainerNodePool',
          metadata: {
            name: 'premium-cpu-pool-v2',
            namespace: 'devtools',
          },
          spec: {
            location: info.region,
            nodeLocations: c3Zones,
            clusterRef: {
              external: info.clusterName,
            },
            nodeConfig: {
              taint: [
                {
                  effect: 'NO_SCHEDULE',
                  key: 'cpu-avx512fp16',
                  value: 'present',
                },
              ],
              // 8 vCPUs, 16 GB RAM
              machineType: 'c3-highcpu-8',
              diskSizeGb: 100,
              oauthScopes: oauthScopes,
              imageType: 'COS_CONTAINERD',
              shieldedInstanceConfig: {
                enableSecureBoot: true,
                enableIntegrityMonitoring: true,
              },
            },
            // config connector doesn't support auto-scaling with total counts
            autoscaling: {
              locationPolicy: if isDev then 'BALANCED' else 'ANY',
              minNodeCount: 0,
              maxNodeCount: 1,  // This group is deprecated!
            },
            management: management,
          },
        } else null,
      // Premium (modern arch, fast SIMD, currently for embeddings-search-cpu) CPU node pool
      // Replacement for premium-cpu-pool-v2, copied from there; difference is memory usage
      if std.length(c3Zones) > 0 then
        {
          apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
          kind: 'ContainerNodePool',
          metadata: {
            name: 'premium-cpu-pool-v3',
            namespace: 'devtools',
          },
          spec: {
            location: info.region,
            nodeLocations: c3Zones,
            clusterRef: {
              external: info.clusterName,
            },
            nodeConfig: {
              taint: [
                {
                  effect: 'NO_SCHEDULE',
                  key: 'cpu-avx512fp16',
                  value: 'present',
                },
              ],
              // 8 vCPUs, 32 GB RAM
              machineType: 'c3-standard-8',
              diskSizeGb: 100,
              oauthScopes: oauthScopes,
              imageType: 'COS_CONTAINERD',
              shieldedInstanceConfig: {
                enableSecureBoot: true,
                enableIntegrityMonitoring: true,
              },
            },
            // config connector doesn't support auto-scaling with total counts
            autoscaling: {
              locationPolicy: if isDev then 'BALANCED' else 'ANY',
              minNodeCount: 0,
              maxNodeCount: if isDev then 64 else 128,
            },
            management: management,
          },
        } else null,
      // Premium (modern arch, fast SIMD, currently for embeddings-search-cpu) CPU node pool
      // Possible replacement for premium-cpu-pool-v3, copied from there; difference is memory usage
      if std.length(c3Zones) > 0 then
        {
          apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
          kind: 'ContainerNodePool',
          metadata: {
            name: 'premium-cpu-pool-v4',
            namespace: 'devtools',
          },
          spec: {
            location: info.region,
            nodeLocations: c3Zones,
            clusterRef: {
              external: info.clusterName,
            },
            nodeConfig: {
              taint: [
                {
                  effect: 'NO_SCHEDULE',
                  key: 'cpu-avx512fp16-highmem',
                  value: 'present',
                },
              ],
              // 8 vCPUs, 64 GB RAM
              machineType: 'c3-highmem-8',
              diskSizeGb: 100,
              oauthScopes: oauthScopes,
              imageType: 'COS_CONTAINERD',
              shieldedInstanceConfig: {
                enableSecureBoot: true,
                enableIntegrityMonitoring: true,
              },
            },
            // config connector doesn't support auto-scaling with total counts
            autoscaling: {
              locationPolicy: if isDev then 'BALANCED' else 'ANY',
              minNodeCount: 0,
              maxNodeCount: if isDev then 32 else 128,
            },
            management: management,
          },
        } else null,
      // L4 non-spot node group
      // L4 is not available in EU-WEST4
      if std.length(l4Zones) > 0 then {
        apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
        kind: 'ContainerNodePool',
        metadata: {
          name: 'l4-gpu-pool',
          namespace: 'devtools',
        },
        spec: {
          location: info.region,
          nodeLocations: l4Zones,
          clusterRef: {
            external: info.clusterName,
          },
          nodeConfig: {
            taint: if isDev then [
              // We want to allow Augment CPU workloads onto the L4 nodes in dev
              // But we want to keep our third-party k8s deployments (like jaeger, cert-manager)
              // from ending up on the L4 nodes where they are more likely to be
              // preempted. If we change our mind about that, we can remove this taint.
              {
                effect: 'NO_SCHEDULE',
                key: 'nvidia.com/gpu',
                value: 'present',
              },
            ] else [
              {
                effect: 'NO_SCHEDULE',
                key: 'nvidia.com/gpu',
                value: 'present',
              },
              {
                effect: 'NO_SCHEDULE',
                key: 'gpu',
                value: '1l4',
              },
            ],
            machineType: 'g2-standard-12',
            diskSizeGb: 100,
            oauthScopes: oauthScopes,
            imageType: 'COS_CONTAINERD',
            guestAccelerator: [
              {
                count: 1,
                type: 'nvidia-l4',
                gpuDriverInstallationConfig: {
                  gpuDriverVersion: 'LATEST',
                },
              },
            ],
            shieldedInstanceConfig: {
              enableSecureBoot: true,
              enableIntegrityMonitoring: true,
            },
          },
          autoscaling: {
            locationPolicy: if isMainDev then 'BALANCED' else 'ANY',
            // config connector doesn't support auto-scaling with total counts
            // DEV: see https://console.cloud.google.com/compute/reservations/detail/us-central1-a/reservation-5?project=system-services-dev
            // DEV: see https://console.cloud.google.com/compute/reservations/detail/us-central1-a/res-64-l4s-20240923-171105?project=system-services-dev
            minNodeCount: if isMainDev then 128 else 0,
            // Prod quota is current 128, but 64 per zone * 3 zones = 192. The hope is if one zone
            // is full, we can use more than 128/3 in another zone.
            maxNodeCount: 128,
          },
          management: management,
        },
      } else null,
      // H100 GPU node pool
      [{
        apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
        kind: 'ContainerNodePool',
        metadata: {
          name: h100Config.name,
          namespace: 'devtools',
          annotations: if std.objectHas(h100Config, 'stateIntoSpec') then {
            'cnrm.cloud.google.com/state-into-spec': h100Config.stateIntoSpec,
          } else {},
        },
        spec: {
          location: info.region,
          nodeLocations: h100Config.locations,
          clusterRef: {
            external: info.clusterName,
          },
          upgradeSettings: {
            maxUnavailable: 1,
            maxSurge: 0,
          },
          nodeConfig: std.prune({
            taint: [
              {
                effect: 'NO_SCHEDULE',
                key: 'gpu',
                value: '8h100',
              },
              {
                effect: 'NO_SCHEDULE',
                key: 'nvidia.com/gpu',
                value: 'present',
              },
            ],
            machineType: std.get(h100Config, 'machineType', 'a3-highgpu-8g'),
            diskSizeGb: 1000,
            oauthScopes: oauthScopes,
            imageType: 'COS_CONTAINERD',
            guestAccelerator: [
              {
                count: 8,
                type: std.get(h100Config, 'guestAccelerator', 'nvidia-h100-80gb'),
                gpuDriverInstallationConfig: {
                  gpuDriverVersion: 'LATEST',
                },
              },
            ],
            shieldedInstanceConfig: {
              enableSecureBoot: true,
              enableIntegrityMonitoring: true,
            },
            reservationAffinity: if std.objectHas(h100Config, 'specificReservation') then {
              consumeReservationType: 'SPECIFIC_RESERVATION',
              key: 'compute.googleapis.com/reservation-name',
              values: [h100Config.specificReservation],
            },
          }),
          nodeCount: h100Config.count,
          // the initial node count cannot be changed, but must be provided
          initialNodeCount: if std.objectHas(h100Config, 'initialNodeCount') then h100Config.initialNodeCount else h100Config.count,
          management: management,
        },
      } for h100Config in h100ZonesMap[cloud]],
    ] + if isDev then [
      // spot node groups (only in dev)
      {
        apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
        kind: 'ContainerNodePool',
        metadata: {
          name: 'pool-cpu-spot',
          namespace: 'devtools',
        },
        spec: {
          location: info.region,
          clusterRef: {
            external: info.clusterName,
          },
          nodeConfig: {
            spot: true,
            machineType: 'e2-standard-16',
            diskSizeGb: 100,
            oauthScopes: oauthScopes,
            imageType: 'COS_CONTAINERD',
            shieldedInstanceConfig: {
              enableSecureBoot: true,
              enableIntegrityMonitoring: true,
            },
          },
          // config connector doesn't support auto-scaling with total counts
          autoscaling: {
            locationPolicy: if isDev then 'BALANCED' else 'ANY',
            minNodeCount: 0,
            maxNodeCount: 32,
          },
          management: management,
        },
      },
      // spot L4 GPU node pool
      if std.length(l4Zones) > 0 then
        {
          apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
          kind: 'ContainerNodePool',
          metadata: {
            name: 'l4-gpu-pool-spot',
            namespace: 'devtools',
          },
          spec: {
            location: info.region,
            nodeLocations: l4Zones,
            clusterRef: {
              external: info.clusterName,
            },
            nodeConfig: {
              taint: [
                {
                  effect: 'NO_SCHEDULE',
                  key: 'gpu',
                  value: '1l4',
                },
                {
                  effect: 'NO_SCHEDULE',
                  key: 'nvidia.com/gpu',
                  value: 'present',
                },
              ],
              spot: true,
              machineType: 'g2-standard-12',
              diskSizeGb: 100,
              oauthScopes: oauthScopes,
              imageType: 'COS_CONTAINERD',
              guestAccelerator: [
                {
                  count: 1,
                  type: 'nvidia-l4',
                  gpuDriverInstallationConfig: {
                    gpuDriverVersion: 'LATEST',
                  },
                },
              ],
              shieldedInstanceConfig: {
                enableSecureBoot: true,
                enableIntegrityMonitoring: true,
              },
            },
            autoscaling: {
              locationPolicy: if isDev then 'BALANCED' else 'ANY',
              // config connector doesn't support auto-scaling with total counts
              minNodeCount: 0,
              // Note (dirk) For reasons that are not clear, we only appear to be using instances in us-central1-a
              maxNodeCount: 128,
            },
            management: management,
          },
        } else null,
      // L4 multi-GPU node pool
      if std.length(l4Zones) > 0 then
        {
          apiVersion: 'container.cnrm.cloud.google.com/v1beta1',
          kind: 'ContainerNodePool',
          metadata: {
            name: 'l4-multi-gpu-pool',
            namespace: 'devtools',
          },
          spec: {
            location: info.region,
            nodeLocations: l4Zones,
            clusterRef: {
              external: info.clusterName,
            },
            nodeConfig: {
              taint: [
                {
                  effect: 'NO_SCHEDULE',
                  key: 'gpu',
                  value: '4l4',
                },
                {
                  effect: 'NO_SCHEDULE',
                  key: 'nvidia.com/gpu',
                  value: 'present',
                },
              ],
              machineType: 'g2-standard-48',
              diskSizeGb: 100,
              oauthScopes: oauthScopes,
              imageType: 'COS_CONTAINERD',
              guestAccelerator: [
                {
                  count: 4,
                  type: 'nvidia-l4',
                  gpuDriverInstallationConfig: {
                    gpuDriverVersion: 'LATEST',
                  },
                },
              ],
              shieldedInstanceConfig: {
                enableSecureBoot: true,
                enableIntegrityMonitoring: true,
              },
            },
            autoscaling: {
              locationPolicy: if isMainDev then 'BALANCED' else 'ANY',
              // config connector doesn't support auto-scaling with total counts
              // We were having trouble allocating a multi-GPU node for tests, so ask for a baseline of 4
              // so we can make progress.
              minNodeCount: if isMainDev then 4 else 0,
              maxNodeCount: 8,
            },
            management: management,
          },
        } else null,
    ] else [];
  lib.flatten([extraObjects, nodeGroups])

load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
    "jsonnet_to_json",
    "jsonnet_to_json_test",
)
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl/helm:helm_template.bzl", "helm_template")

jsonnet_library(
    name = "cloud_info",
    srcs = [
        "cloud_info.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_to_json(
    name = "cloud_info_json",
    src = "cloud_info.jsonnet",
    outs = ["cloud_info.json"],
    visibility = [
        "//deploy/tenants:__subpackages__",
        "//services/lib/grpc:__subpackages__",
        "//services/tenant_watcher/util:__subpackages__",
        "//services/token_exchange/util:__subpackages__",
        "//tools/notion_tenant_sync:__subpackages__",
    ],
)

filegroup(
    name = "cloud_info-files",
    srcs = [
        "cloud_info.jsonnet",
    ],
    visibility = [
        "//tools/deploy_runner/control:__subpackages__",
    ],
)

jsonnet_library(
    name = "dynamic-feature-flags-lib",
    srcs = [
        "dynamic-feature-flags-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//deploy/gcp:gcp-lib",
    ],
)

jsonnet_library(
    name = "eng-lib",
    srcs = [
        "eng.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_to_json(
    name = "eng_json",
    src = "eng.jsonnet",
    outs = ["eng.json"],
    visibility = [
        "//tools:__subpackages__",
    ],
)

jsonnet_to_json_test(
    name = "eng-test",
    src = "eng-test.jsonnet",
    deps = [":eng-lib"],
)

jsonnet_library(
    name = "namespaces-lib",
    srcs = [
        "namespaces-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":cloud_info",
        ":eng-lib",
    ],
)

jsonnet_library(
    name = "eng-namespaces-lib",
    srcs = [
        "eng-namespaces.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":cloud_info",
        ":eng-lib",
        "//deploy/tenants:namespaces",
    ],
)

filegroup(
    name = "node-lib-files",
    srcs = [
        "node-lib.jsonnet",
    ],
    visibility = [
        "//tools/bazel_runner/control:__subpackages__",
        "//tools/deploy_runner/control:__subpackages__",
    ],
)

jsonnet_library(
    name = "lib",
    srcs = [
        "lib.jsonnet",
    ],
    visibility = ["//visibility:public"],
)

jsonnet_to_json_test(
    name = "lib-test",
    src = "lib-test.jsonnet",
    deps = [":lib"],
)

jsonnet_library(
    name = "mounts-lib",
    srcs = [
        "mounts-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":cloud_info",
    ],
)

jsonnet_library(
    name = "node-lib",
    srcs = [
        "node-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_library(
    name = "lock-lib",
    srcs = [
        "lock-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_library(
    name = "cert-lib",
    srcs = [
        "cert-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_library(
    name = "config-map-lib",
    srcs = [
        "config-map-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

jsonnet_library(
    name = "grpc-lib",
    srcs = [
        "grpc-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":cloud_info",
    ],
)

jsonnet_library(
    name = "cmk-lib",
    srcs = [
        "cmk_lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":cloud_info",
    ],
)

jsonnet_library(
    name = "telemetry-lib",
    srcs = [
        "telemetry-lib.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        ":dynamic-feature-flags-lib",
    ],
)

jsonnet_library(
    name = "tenant-auth-config",
    srcs = [
        "tenant-auth-config.jsonnet",
    ],
    visibility = [
        "//deploy:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
)

kubecfg(
    name = "kubecfg_mounts",
    src = "mounts.jsonnet",
    cluster_wide = True,
    deps = [
        ":cloud_info",
        ":eng-namespaces-lib",
        ":lib",
        ":mounts-lib",
    ],
)

kubecfg(
    name = "kubecfg_namespace",
    src = "namespaces.jsonnet",
    cluster_wide = True,
    deps = [
        ":cloud_info",
        ":eng-namespaces-lib",
        ":lib",
        ":namespaces-lib",
    ],
)

kubecfg(
    name = "kubecfg_external_dns",
    src = "external-dns.jsonnet",
    cluster_wide = True,
    deps = [
        "lib",
        ":cloud_info",
    ],
)

kubecfg(
    name = "kubecfg_eng_auth",
    src = "eng-auth.jsonnet",
    cluster_wide = True,
    deps = [
        ":cloud_info",
        ":eng-lib",
        ":lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg_roles",
    src = "roles.jsonnet",
    cluster_wide = True,
    deps = [
        "lib",
        ":cloud_info",
        ":eng-namespaces-lib",
    ],
)

kubecfg(
    name = "kubecfg_certs",
    src = "certs.jsonnet",
    cluster_wide = True,
    deps = [
        "lib",
        ":cert-lib",
        ":cloud_info",
        ":eng-namespaces-lib",
    ],
)

kubecfg(
    name = "kubecfg_cert_manager",
    src = "cert-manager.jsonnet",
    cluster_wide = True,
    deps = [":cloud_info"],
)

# HELM template for the cert manager for system-services-dev.
helm_template(
    name = "cert-manager-dev",
    chart_tar = "@cert-manager//:chart.tgz",
    namespace = "cert-manager",
    release_name = "cert-manager",
    values = {
        "extraArgs": "\\{--dns01-recursive-nameservers-only,--dns01-recursive-nameservers=8.8.8.8:53\\,1.1.1.1:53\\}",
    },
    values_yaml_file = ":cert-manager-values-dev.yaml",
)

# HELM template for the cert manager for system-services-prod.
helm_template(
    name = "cert-manager-prod",
    chart_tar = "@cert-manager//:chart.tgz",
    namespace = "cert-manager",
    release_name = "cert-manager",
    values = {
        "extraArgs": "\\{--dns01-recursive-nameservers-only,--dns01-recursive-nameservers=8.8.8.8:53\\,1.1.1.1:53\\}",
    },
    values_yaml_file = ":cert-manager-values-prod.yaml",
)

helm_template(
    name = "cert-manager-prod-gsc",
    chart_tar = "@cert-manager//:chart.tgz",
    namespace = "cert-manager",
    release_name = "cert-manager",
    values = {
        "extraArgs": "\\{--dns01-recursive-nameservers-only,--dns01-recursive-nameservers=8.8.8.8:53\\,1.1.1.1:53\\}",
    },
    values_yaml_file = ":cert-manager-values-gsc-prod.yaml",
)

kubecfg(
    name = "kubecfg_cert_manager_dev",
    src = ":cert-manager-dev",
    cluster_wide = True,
    lint = False,
    norewrite = True,
)

kubecfg(
    name = "kubecfg_cert_manager_prod",
    src = ":cert-manager-prod",
    cluster_wide = True,
    lint = False,
    norewrite = True,
)

kubecfg(
    name = "kubecfg_cert_manager_prod_gsc",
    src = ":cert-manager-prod-gsc",
    cluster_wide = True,
    lint = False,
    norewrite = True,
)

kubecfg(
    name = "kubecfg_cert_manager_issuer",
    src = "cert-manager-issuer.jsonnet",
    cluster_wide = True,
    deps = [
        ":cloud_info",
    ],
)

kubecfg(
    name = "kubecfg_cert_manager_crds",
    src = ":cert-manager-crds.yaml",
    cluster_wide = True,
    norewrite = True,
)

# HELM template for the keda
helm_template(
    name = "keda",
    chart_tar = "@keda//:chart.tgz",
    namespace = "keda",
    values_yaml_file = ":keda-values.yaml",
)

# KEDA doesn't apply without --server-side nor
# does it work with the stamping/rewriting.
kubecfg(
    name = "kubecfg_keda",
    src = ":keda",
    cluster_wide = True,
    # https://github.com/kedacore/keda/issues/4740
    extra_kubectl_args = ["--server-side"],
    lint = False,
    norewrite = True,
)

# HELM template for the reloader
helm_template(
    name = "reloader",
    chart_tar = "@reloader//:chart.tgz",
    namespace = "kube-system",
    values_yaml_file = "reloader-values.yaml",
)

kubecfg(
    name = "kubecfg_reloader",
    src = ":reloader",
    cluster_wide = True,
    lint = False,
)

kubecfg(
    name = "kubecfg_sealed_secrets",
    src = ":sealed_secrets.yaml",
    cluster_wide = True,
    lint = False,
    norewrite = True,
)

kubecfg(
    name = "kubecfg_security_audit",
    src = "security_audit.jsonnet",
    cluster_wide = True,
    deps = [
        ":cloud_info",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg_cert_manager_crds",
        ":kubecfg_cert_manager_dev",
        ":kubecfg_cert_manager_issuer",
        ":kubecfg_cert_manager_prod",
        ":kubecfg_certs",
        ":kubecfg_eng_auth",
        ":kubecfg_external_dns",
        ":kubecfg_keda",
        ":kubecfg_mounts",
        ":kubecfg_namespace",
        ":kubecfg_reloader",
        ":kubecfg_roles",
        ":kubecfg_security_audit",
    ],
)

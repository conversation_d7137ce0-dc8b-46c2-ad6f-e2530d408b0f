local flatten = function(arr)
  if std.isArray(arr) then
    std.flatMap(flatten, arr)
  else [arr];
local contains(arr, i) = std.count(arr, i) > 0;
{
  // flatten nested lists into a single list, e.g.
  // [1, [[2]], [[3], 4]] -> [1, 2, 3, 4]
  flatten:: flatten,
  // similar to std.contain, but in jsonnet 0.20
  contains:: contains,
  // Define the tier options that are available for tenants
  tenant_tier_options:: ['ENTERPRISE', 'PROFESSIONAL-TEAM', 'COMMUNITY'],
}

// file containing Kubernetes configuration for the augment NFS mount
local cloudInfo = import 'cloud_info.jsonnet';
local namespaces = import 'eng-namespaces.jsonnet';
local lib = import 'lib.jsonnet';
local mountLib = import 'mounts-lib.jsonnet';

function(cloud)
  local info = cloudInfo[cloud];
  local priorityClasses = [
    {
      apiVersion: 'scheduling.k8s.io/v1',
      description: 'This priority class should be used for staging pods only.',
      kind: 'PriorityClass',
      metadata: {
        name: 'staging',
      },
      preemptionPolicy: 'PreemptLowerPriority',
      value: 1000000,
    },
    {
      apiVersion: 'scheduling.k8s.io/v1',
      description: 'This priority class should be used for staging pods that require a GPU only.',
      kind: 'PriorityClass',
      metadata: {
        name: 'staging-gpu',
      },
      preemptionPolicy: 'PreemptLowerPriority',
      value: 2100000,  // higher than prod so that GPU pods can preempt non-GPU pods. CPU pods can migrate to different nods, GPU pods can't.
    },
    {
      apiVersion: 'scheduling.k8s.io/v1',
      description: 'This priority class should be used for prod pods only.',
      kind: 'PriorityClass',
      metadata: {
        name: 'prod',
      },
      preemptionPolicy: 'PreemptLowerPriority',
      value: 2000000,
    },
    {
      apiVersion: 'scheduling.k8s.io/v1',
      description: 'This priority class is used by pods without a priority class.',
      kind: 'PriorityClass',
      metadata: {
        name: 'workaround-priority',
      },
      // globalDefault to true means pods without a priority class get this
      // priority. Some important pods like csi-secret-store-gke don't have the
      // ability to specify a priority class yet (for csi-secret-store-gke, the
      // issue according to Google support is https://issuetracker.google.com/396581151)
      // Until we can specify a priority class for all important pods, use globalDefault
      // to raise their priority.
      globalDefault: true,
      preemptionPolicy: 'PreemptLowerPriority',
      value: 2001000,
    },
    {
      apiVersion: 'scheduling.k8s.io/v1',
      description: 'This priority class should be used for prod pods that require a GPU only.',
      kind: 'PriorityClass',
      metadata: {
        name: 'prod-gpu',
      },
      preemptionPolicy: 'PreemptLowerPriority',
      value: 2500000,
    },
  ];
  local filestore = if cloudInfo.isLeadDevCluster(cloud) then [] else [
    // GCP_US_CENTRAL1_DEV has a filestore that was manually created
    // GCP_US_CENTRAL1_PROD has a filestore in the default namespace with the default resourceId
    {
      apiVersion: 'filestore.cnrm.cloud.google.com/v1beta1',
      kind: 'FilestoreInstance',
      metadata: {
        name: 'checkpoint-fs',
        // GCP_US_CENTRAL1_PROD was set up in the default namespace
        namespace: {
          GCP_US_CENTRAL1_PROD: 'default',
          GCP_EU_WEST4_PROD: 'central',
          GCP_US_CENTRAL1_GSC_PROD: 'central',
        }[cloud],
      },
      spec: {
        projectRef: {
          external: 'projects/%s' % info.projectId,
        },
        description: 'filestore for checkpoints',
        fileShares: [
          {
            capacityGb: 10000,
            name: 'checkpoint_share',
          },
        ],
        // us-central1 was first, so it's called checkpoint-fs
        resourceID: if info.region == 'us-central1' then 'checkpoint-fs' else 'checkpoint-fs-%s' % info.region,
        location: info.mainZone,
        networks: [
          {
            networkRef: {
              external: {
                GCP_US_CENTRAL1_PROD: 'default',
                GCP_EU_WEST4_PROD: 'default',
                GCP_US_CENTRAL1_GSC_PROD: 'prod-gsc-vpc0',
              }[cloud],
            },
          },
        ],
        tier: 'BASIC_SSD',
      },
    },
  ];

  local n = namespaces(cloud);
  local mountNamespaces = if cloudInfo.isLeadDevCluster(cloud) then
    n.deployment + n.eng + [n.info('devtools')]
  else if cloudInfo.isDevCluster(cloud) then
    n.deployment + n.eng + [n.info('devtools')]
  else if cloudInfo.isProjectLeadProdCluster(cloud) then
    n.central + [n.info('devtools')]
  else
    n.central + [n.info('devtools')];
  lib.flatten(
    [
      std.flatMap(function(ns) mountLib.createNamespaceMounts(cloud, ns.name), mountNamespaces),
      priorityClasses,
      filestore,
    ]
  )

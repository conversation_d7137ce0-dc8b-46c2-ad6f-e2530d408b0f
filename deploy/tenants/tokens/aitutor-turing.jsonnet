// The file contrains sha256 of authentication tokens for contractors from the Turing vendor working on the data collection.
// Refer to https://www.notion.so/Runbook-How-to-generate-api-tokens-a7ede88059604149867f03c2cf6f434b?pvs=4
// for how to generate new tokens.
// You can generate JSON file by running
// $ bazel run //tools:jsonnet -- $(pwd)/deploy/tenants/_kubecfg/aitutor-turing.jsonnet > /tmp/aitutor-turing.json
(import 'deploy/tenants/tokens/internal_users.jsonnet')(tenant_name='aitutor-turing') + std.map(function(t) t + { tenant_name: 'aitutor-turing' }, [
  {
    user_id: 'mukul-kumar-turing-engineer',
    token_sha256: '4b6d14e935aa388c75459a596db2f11a52df42607cefcb9748862ce19da819ec',  // pragma: allowlist secret
  },
  {
    user_id: 'al-mamun-sarkar-turing-engineer',
    token_sha256: '4acd672f9c9f14827f9c41d2987d1f88a7669b5c229b3d1b41951ea9c4dc3bf3',  // pragma: allowlist secret
  },
  {
    user_id: 'kaidu',
    token_sha256: '290b4e078b8397be0d56697a870475198dfdf6d0df78095a5ff8307cb8f67f92',  // pragma: allowlist secret
  },
  {
    user_id: 'vijaykrishnan',
    token_sha256: '62508450c7c99dbc20a98f0b0307d8937411b5faac70537d4b923411e0962b1c',  // pragma: allowlist secret
  },
  {
    user_id: 'satayu-mehta-turing-engineer',
    token_sha256: 'ea5b410fec3d53133a6664ac7707e4399a5789cd02a712cea4068af33902f7a2',  // pragma: allowlist secret
  },
])

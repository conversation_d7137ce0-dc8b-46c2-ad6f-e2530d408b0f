// The file contrains sha256 of authentication tokens for contractors from the Pareto vendor working on the data collection.
// Refer to https://www.notion.so/Runbook-How-to-generate-api-tokens-a7ede88059604149867f03c2cf6f434b?pvs=4
// for how to generate new tokens.
// You can generate JSON file by running
// $ bazel run //tools:jsonnet -- $(pwd)/deploy/tenants/_kubecfg/aitutor-pareto.jsonnet > /tmp/aitutor-pareto.json
(import 'deploy/tenants/tokens/internal_users.jsonnet')(tenant_name='aitutor-pareto') + std.map(function(t) t + { tenant_name: 'aitutor-pareto' }, [
  {
    user_id: 'aakansha-doshi-pareto-contractor',
    token_sha256: 'f57d5fa4db497aaabb51c49bd07011dbbba329531e7e8014f5fe9dd1867f28ed',  // pragma: allowlist secret
  } +
  {
    user_id: 'arseny-kapoulkine-pareto-contractor',
    token_sha256: '0e78ad1b58d67f1fa56ee4eff6acb53d1713f269449e973680a7a5224c420c48',  // pragma: allowlist secret
  },
  {
    user_id: 'bram-kragten-pareto-contractor',
    token_sha256: '242860a8dad480930909ca6cddf86e71125fcafe6a52be08aaa88d4f18681556',  // pragma: allowlist secret
  },
  {
    user_id: 'xudong-zhao-pareto-contractor',
    token_sha256: '46581de3e5b238c9663e102fb8c44b31984b65c3355d50e7d8eac678e8643d60',  // pragma: allowlist secret
  },
  {
    user_id: 'valentin-palkovic-pareto-contractor',
    token_sha256: 'b9ad8f60ebe2030632db561efa3a0f5ca857435781ef1610d6004f21ebb0e20b',  // pragma: allowlist secret
  },
  {
    user_id: 'marcelo-trylesinski-pareto-contractor',
    token_sha256: 'e8c4f2b4ad6cf8df53aefda61d323c48ea51f07c4d1cafb8f78ae1e2c5fb36d7',  // pragma: allowlist secret
  },
  {
    user_id: 'maya-barnes-pareto-contractor',
    token_sha256: 'e2e5546c87e854e05a58bb33be239f8600fce7c28a1f0392e3ebf8253eb54e91',  // pragma: allowlist secret
  },
  {
    user_id: 'hasan-ramezani-pareto-contractor',
    token_sha256: 'a4907a7b25e8f92050e37dc93ebbf4629772425160dfe970a9bb0fb5f474d8fc',  // pragma: allowlist secret
  },
  {
    user_id: 'tee-ming-pareto-contractor',
    token_sha256: 'ad7cfbfd82f23bd0ff016097fab1cfb1b0bf605c64ba2aabac54070ee8f94732',  // pragma: allowlist secret
  },
  {
    user_id: 'rodney-osodo-pareto-contractor',
    token_sha256: 'f9004ed19aac4986dd4d9a07fe73a7b9979f7d5292d6a47d2001dfee4663345f',  // pragma: allowlist secret
  },
  {
    user_id: 'phoebeyao-pareto-official',
    token_sha256: 'a92d610fa4464cc60324772954b3ed98a266b769e5aed61c0ba3ee1b540cf33e',  // pragma: allowlist secret
  },
])

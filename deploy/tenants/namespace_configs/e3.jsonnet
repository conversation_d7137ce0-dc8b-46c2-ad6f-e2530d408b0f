local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';

config + {
  api_tokens: [],
  flags: config.flags
         .override('migratedTenantIds', ['12b3909516a76d3a17cf9be33953c701', '141b696f5e1d5326431f048e15326c74', '1566aa1da5ea960c74610efb97ec7e32', '1650799d5415d0eea69d732c0a18a759', '1851276269206ca93ce2f7eb954176a3', '1ad4a687ab5b21e5352de86efd580a13', '1b643d25b48a050b4f53d8608a3c1c3a', '1ee30d9d1d7b88fcd9a6ed237ffd9605', '251ad8179845458b210beac3da3d1790', '27cdd70f893f98a16f5065413fcf1ef4', '28eec4647f01f1b9f5c6bb60bef2d97f', '290db9899aabc84abeaf58aefb8e7057', '2b57f9a20e3b9bb3e774fd87a6e49ebf', '2c0fad32e57c1259436b010311412f90', '2fb4574ad1e4815a4debe48814c3198c', '305af7a818d696a69f7337ccf6214957', '33b5eba7b90c84093a167db667aa49c', '33d23a400262ea03be1326f7a4652878', '3425ef6c92ef055d22dce73bdf595b1b', '34e44ca21a4da6133a221b9b0a6ced80', '35819016f3a18f7f13fdf51a24f1d092', '359931dfbd2d029e327ed1c9f3368010', '3697964028aebfdc0b113c4d55b07860', '36fde30fb306115e6dabe3fac8e31be8', '37fe705a00079c59375baedb7e69e510', '3b5df3c7a4aade156edbad98d84de95e', '3baf18e4116a739555be09d16ed5bc6', '3c7c07699dc5667f8a3bd5c2f31b604a', '3dc08413d762382ba3c711d94b9ef51f', '3f2828189d84c9396c17347adbfe2fc', '420910609d4376b362f10c637b5ebf88', '43aba7bcd09ff0df4d82a27bb138d8d5', '4433d0d827953a82ce4327465bbf5bdd', '454bbc45b8eb994190430c972b006bfe', '497f00ab4cdef4b9cf86210621508c07', '53a08e6df7cdb1e45d4d8c498bc60618', '5551272009f84c404d7411047f51a83d', '594e9713a1cd2118e363fa2bd14c2ffc', '599782ac6f7256e52e7bd75266b1727f', '5a5ea25a95ae134e9760138047d60c3d', '5a984666a362089420c3aa9db40cec6c', '5bdf6b0f1e7109c1c0661e63c94abafd', '60f609532f4ab71d6d37d1026831efc3', '62808012b5b5fb402a082f1cf0ca3e43', '647bf32928a068b183b83cd1849e9709', '71534d8cfae567fdb773cc14b73be652', '721f4082d0fab448049a943647be31a1', '726a2fcbae26f6ed6b5bda266549d3f', '791a8a58221bb9256d8bc8f38112f60e', '7ba23c50514e338ae00bbed9e3c09d38', '7c42a6c28ae8c9b04b27449ad8bf797d', '7ce1b41f34bdd1aad48f9df5905db92d', '7f6baf9ac014431b0ed7f01f920b4bfa', '7ff613711d7f32e11c61ec3cfc0ca4a7', '81ece1369bd9431b355c572406a2a833', '878f852950eeadb65752bd2cb78d52e', '87d79a64d2f1f0d95d65eab914e3063c', '8ab34255ea2b684095af05fcb5cb0b7a', '8ed5604174cc5b33c5d9e8ae59291f66', '9807c2ee60e8fa155feabf510eb9fa48', '9c5e7b825a54f540cb019b3ee5b51756', '9cf0274b497e003dc70fd3b9a2824c57', '9f1e3ddfae50f41536d9b4b9e806ef78', 'a34bdf9c8a4e8e27ac3fcb10ba4edffe', 'a3e242ef7b43d59d1d5956567a286383', 'a4c173575ba7e962b6f66be2be65a9ff', 'a92d12043d497477fa79ea178e7ecd84', 'a93ed860ac26721ce8ec507946bd17c7', 'aa9270e71d40c32c09f48dffbe9e7ed0', 'afac81f764bf4df89711b8244c34ab74', 'afe0b5f0cbb16557f4039d8968e93350', 'b0ae011b97b93cdecda7ee2f15076890', 'b35473fbf27e62aa2adc3cc7dff9c04b', 'b46ee938bca0ccc1c40f6a7431d2082d', 'b90d8573870cfea9a2ed2e5a51ad1bb5', 'bb8a770a78bb6a46af3822d2db1f21b5', 'bdd6b50b5e0eb64955f647d8812a270a', 'c12baf80c98d45a527200d69b2240ff5', 'c1937564a9552ffab81f6d16e13dd0c1', 'ca7001eda168d937e4edbfa951ed771', 'd14b4db65441c23201a89055bf59463a', 'd27271ed5823c22aacb83ec09f31126b', 'd4c1540d361493f19fec16aa2f87dce8', 'e7b2cb210b5ef8ecbc0bdcbebbaeabbd', 'ebcad01d8dbd9b44f1ce714347dfcd97', 'efe67602920c72f6f60b12e04a9a6538', 'f0308cf9433e7757d155fb3f17419a53', 'f31796c2e7d0af6a7ff000038eecf667', 'f563fd06eddaf3f392fd6f867912ee6e', 'f63f42101df1d5c384c503b9d773f26c', 'f6d082380161f9fe61af121d94f04e11', 'fc7e494cc037db1c0e4c414b1fbc59dd', 'fd758ec4342978f4a3aa84462a6607e9', 'feec9f1aa5d48fa7e57bf78653015fa3'])
         .override('passthroughBigtableProxy', false)
         .override('bigtableProxyMinReplicas', 4)
         .override('usePremiumCpuHighmem', true)  // Give embeddings search extra memory
         .override('completionHostReplicaScale', 3.0),
}

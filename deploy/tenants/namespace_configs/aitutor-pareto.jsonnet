// AI-tutors-pareto is a project for contractors from the Pareto vendor to generate
// data for AI training.
local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';
local apiTokens = import 'deploy/tenants/tokens/aitutor-pareto.jsonnet';

local sharedVendorBucket = {
  enabled: true,
  adminEmails: [
    // NOTE(arun, 01/16/2024): Added per conversation on Slack with <PERSON>.
    '<EMAIL>',
    // NOTE(arun, 01/30/2024): Added per conversation on Slack with <PERSON>.
    '<EMAIL>',
  ],
  accessEmails: [
    // NOTE(arun, 01/16/2024): Added per conversation on Slack with <PERSON>.
    'i<PERSON><PERSON>@pareto.ai',
  ],
};
local exportAnnotations = {
  enabled: true,
  // NOTE(aswin, 03/05/2024): Add per conversation on Slack with <PERSON>
  webhook_url: 'https://api.pareto.ai/webhook/external/callback',
};

config + {
  api_tokens: apiTokens,
  flags: config.flags
         .override('exportFullData', true)
         .override('request_insight_event_retention_days', 365)
         .override('sharedVendorBucket', sharedVendorBucket)
         .override('exportAnnotations', exportAnnotations)
         .override('deployGlean', false),  // glean is not deployed for the same reason as above
}

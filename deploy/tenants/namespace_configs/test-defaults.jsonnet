local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config + {
  flags: config.flags
         // creating and deleting ingresses takes a long time, so we disable them
         .override('loadBalancerType', '')
         .override('useFakeSlack', true)
         .override('passthroughBigtableProxy', false)
         // Use a fixed secret name for the Stripe webhook test
         .override('stripeWebhookSecretOverride', {
    name: 'dev-stripe-webhook-signing-secret-test',
    version: 'latest',
  }),
}

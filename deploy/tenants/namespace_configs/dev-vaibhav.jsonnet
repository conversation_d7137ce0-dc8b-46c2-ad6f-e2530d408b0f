local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

// config
//   .flags.override('authQueryTokenCacheTtlSecs', 5)
//   .withDeployFlags({
//     remoteAgentImageTag: '{namespace}',
//   })
//   .withFakeFeatureFlags({
//     next_edit_stream_mux: true,
//     enforce_usage_credits: true,
//   })

config + {
  flags: config.flags
         .override('authQueryTokenCacheTtlSecs', 5)
         .override(
    'extraFakeFeatureFlags', config.flags.extraFakeFeatureFlags + {
      enforce_usage_credits: true,
      enable_billing_for_remote_agents: true,
    }
  ),
  defaultTenantFlags: config.defaultTenantFlags.override('supportAccessControl', false),
}

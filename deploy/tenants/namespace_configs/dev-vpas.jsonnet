local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

config
.withDeployFlags({
  useSharedDevRequestInsightBigquery: false,
  exportFullData: true,
  remoteAgentImageTag: '{namespace}',
})
.withFakeFeatureFlags({
  model_registry: '{  "Claude Opus 4": "claude-opus-4-0-200k-v5-c4-p2-agent", "Gemini 2.5 Pro": "gemini2-5-pro-200k-v3-2-c4-p2-agent", "Claude 3.7 Sonnet (Load Balanced)": "claude-sonnet-3-7-200k-v3-balanced-c4-p2-agent",  "Claude 3.7 Sonnet (Backup)": "claude-sonnet-3-7-200k-v3-c4-p2-agent"}',
})

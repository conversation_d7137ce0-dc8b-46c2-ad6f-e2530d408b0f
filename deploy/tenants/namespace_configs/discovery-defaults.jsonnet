local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';
config + {
  api_tokens: [],
  flags: config.flags
         .override('passthroughBigtableProxy', false)
         .override('bigtableProxyMinReplicas', 4)
         .override('useBigEmbeddingsSearch', true)  // treat this like pstg size
         .override('usePremiumCpuHighmem', true)  // Give embeddings search extra memory
         .override('embeddingsSearchPartitions', 4)  // Provide extra instances of embeddings search
         .override('enablePartitionedEmbeddingsSearch', true)
         .override('deployGlean', false)  // glean is not deployed for the same reason as above
         .override('userTier', 'PROFESSIONAL_TIER')
         .override('authQueryTokenCacheTtlSecs', 60),  // reducing auth query cache ttl to 1 minute so we fetch user's subscription status more frequently
}

{
  name: 'cigna',
  namespace: 'e3',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  domain: 'cigna.com',
  username_domains: ['cigna.com', 'evernorth.com'],
  email_address_domains: ['cigna.com', 'evernorth.com'],
  tier: 'ENTERPRISE',
  tenantId: '517a9a5463abaa660bba024b3052a247',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true),
}

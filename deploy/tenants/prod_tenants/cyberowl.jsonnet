{
  name: 'cyberowl',
  namespace: 'd2',
  other_namespace: 'e8',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  tier: 'PROFESSIONAL',
  tenantId: '144d021824e5ae8a35ce78421cdc03e9',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true)
               .override('isSelfServeTeam', true)
               .override('isLegacySelfServeTeam', true),
}

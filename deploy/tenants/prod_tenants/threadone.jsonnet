{
  name: 'threadone',
  tier: 'PROFESSIONAL',
  namespace: 'd18',
  other_namespace: 'e3',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  tenantId: 'efe67602920c72f6f60b12e04a9a6538',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true)
               .override('isSelfServeTeam', true)
               .override('isLegacySelfServeTeam', true),
}

{
  name: 'redexeco',
  namespace: 'd11',
  other_namespace: 'e5',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  tier: 'PROFESSIONAL',
  tenantId: '4880de2ed88c83569097d13c0df2bfd8',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true)
               .override('isSelfServeTeam', true)
               .override('isLegacySelfServeTeam', true),
}

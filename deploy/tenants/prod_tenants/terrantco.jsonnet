{
  name: 'terrant<PERSON>',
  namespace: 'd11',
  other_namespace: 'e3',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  tier: 'PROFESSIONAL',
  tenantId: '5a984666a362089420c3aa9db40cec6c',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true)
               .override('isSelfServeTeam', true)
               .override('isLegacySelfServeTeam', true),
}

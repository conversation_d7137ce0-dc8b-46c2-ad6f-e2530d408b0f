{
  name: 'npixelcokr',
  namespace: 'd20',
  other_namespace: 'e6',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  tier: 'PROFESSIONAL',
  tenantId: '5a03af9993ac31c9f382efd51500fae',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true)
               .override('isSelfServeTeam', true)
               .override('isLegacySelfServeTeam', true),
}

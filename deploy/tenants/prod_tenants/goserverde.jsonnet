{
  name: 'goserverde',
  namespace: 'd18',
  other_namespace: 'e3',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  tenantId: 'd4c1540d361493f19fec16aa2f87dce8',
  tier: 'PROFESSIONAL',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true)
               .override('isSelfServeTeam', true)
               .override('isLegacySelfServeTeam', true),
}

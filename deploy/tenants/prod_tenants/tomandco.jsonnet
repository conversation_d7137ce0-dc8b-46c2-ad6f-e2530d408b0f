{
  name: 'tomand<PERSON>',
  namespace: 'd8',
  other_namespace: 'e8',
  env: 'PROD',
  cloud: 'GCP_US_CENTRAL1_PROD',
  tier: 'PROFESSIONAL',
  tenantId: '886abe03cd049b53c9e6fd05118d3d1d',
  tenantFlags: tenantFlags
               .override('supportTenant', false)
               .override('multiTenantAllowed', true)
               .override('isSelfServeTeam', true)
               .override('isLegacySelfServeTeam', true),
}

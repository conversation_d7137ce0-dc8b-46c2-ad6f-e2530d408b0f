# Tenant and Tenant Namespace Configuration

## Terms

- A tenant allows a customer to use the product. Most restricted information is not accessible outside the tenant.
- A namespace is in general a Kubernetes concept to separate objects
- A shard namespace is a Kubernetes namespace that is used to run services for tenants, e.g. the api proxy
- A tenant is always assigned to a shard
- In general, multi tenants can be assinged to a shard
- If the tenant has the flag multiTenantAllowed set to false, the tenant is running in a shard namespace that is not shared with other tenants. We call this a single-tenant shard namespace.
- Single tenant shard namespaces are implicitly created for a tenant. They do not need to be added to shard_namespaces.jsonnet

## tenants.jsonnet

Add new customer tenants to this file, e.g.

```
  {
    namespace: 'e0',
    env: 'PROD',
    name: 'nicename',
    cloud: 'GCP_US_CENTRAL1_PROD',
    domain: 'nicename.ai',
  },
```

If single-tenancy tenants, the namespace HAS to be identical to the name.
For multi-tenancy tenants, the namespace has to be in shard_namespaces.jsonnet.

## shard_namespaces.jsonnet

To add a new shard namespace, add it to this file.

```
 {
   namespace: 'shard-0',
   env: 'STAGING',
   cloud: 'GCP_US_CENTRAL1_PROD',
 },
```

## Links

- https://www.notion.so/Deploy-time-Feature-Flags-077e53d3e0a54197a6cc54ec80aff6ae?pvs=4
- https://www.notion.so/Runbook-How-to-configure-a-new-tenant-da6a5866570341caa8b1d154877ef9d1?pvs=4

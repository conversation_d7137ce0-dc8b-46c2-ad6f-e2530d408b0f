// This file contains the defintion of all multi-tenant shard namespaces.
// A multi-tenant shard namespace is a namespace that contains multiple tenants.
// Example:
//  {
//    namespace: 'shard-0',
//    env: 'STAGING',
//    cloud: 'GCP_US_CENTRAL1_PROD',
//  },
[
  {
    namespace: 'staging-shard-0',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    namespace: 'eu-staging-0',
    env: 'STAGING',
    cloud: 'GCP_EU_WEST4_PROD',
  },
  // shards for regular customers
  {
    // shard 0 for enterprise customers (short e0)
    // Avoid using e0 for new tenants
    namespace: 'e0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 1 for enterprise customers (short e1)
    // This is the current default namespace for new tenants outside Europe
    namespace: 'e1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 2 for enterprise customers (short e2)
    // Preallocated namespace in preparation of product launch
    namespace: 'e2',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 3 for enterprise customers (short e3)
    // Preallocated namespace in preparation of product launch
    namespace: 'e3',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 4 for enterprise customers (short e4)
    // Preallocated namespace in preparation of product launch
    namespace: 'e4',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 5 for enterprise customers (short e5)
    // Preallocated namespace in preparation of product launch
    namespace: 'e5',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 6 for enterprise customers (short e6)
    // Preallocated namespace in preparation of product launch
    namespace: 'e6',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 7 for enterprise customers (short e7)
    // Preallocated namespace in preparation of product launch
    namespace: 'e7',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 8 for enterprise customers (short e8)
    // Preallocated namespace in preparation of product launch
    namespace: 'e8',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 9 for enterprise customers (short e9)
    // Preallocated namespace in preparation of product launch
    namespace: 'e9',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 10 for enterprise customers (short e10)
    // Preallocated namespace in preparation of product launch
    namespace: 'e10',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 11 for enterprise customers (short e11)
    // Preallocated namespace in preparation of product launch
    namespace: 'e11',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // dedicated shard for rubrik for now
    namespace: 'xlb',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // dedicated shard for snowflake
    namespace: 'xlc',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // dedicated shard for cisco
    namespace: 'xld',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // dedicated shard for rubrik with CMK
    namespace: 'xle',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // dedicated shard for intuit
    namespace: 'xlf',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // dedicated shard for remote agents pentesters
    namespace: 'pentestra',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 0 for enterprise customers in Europe (short e0-eu)
    // This is the current default namespace for new tenants in Europe
    namespace: 'e0-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
  },
  {
    // shard 1 for enterprise customers in Europe (short e1-eu)
    // Preallocated namespace in preparation of product launch
    namespace: 'e1-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
  },
  {
    // shard 2 for enterprise customers in Europe (short e2-eu)
    // Preallocated namespace in preparation of product launch
    namespace: 'e2-eu',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
  },
  {
    // Dedicated namespace for shv, separate from other regular customers
    // Want all dogfood features, so putting in STAGING
    namespace: 'shv-staging',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  // shard for individual users (vanguard)
  {
    // shard 0 for individual users (short i0)
    namespace: 'i0',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    // shard 1 for individual users (short i1)
    namespace: 'i1',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
] + [
  {
    // shards for discovery users (short d0)
    namespace: 'd%s' % i,
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  }
  for i in std.range(0, 20)
]

local tenantFlags = import 'deploy/tenants/tenant_flags.jsonnet';
local tenants = import 'deploy/tenants/tenants.jsonnet';
local standardIdentityProviders = (import 'deploy/tenants/standard_identity_providers.jsonnet').list;

local defaultTenantFlags = {
  PROD: tenantFlags,
  STAGING: tenantFlags,
};
{
  local this = self,

  processTenants:: function(tenants)
    std.map(function(t)
      assert std.objectHas(t, 'tenantId') : 'tenantId must be set' + std.manifestJson(t);

      t + {
        tenantFlags: this.getTenantFlags(t, namespace_configs=null),
        allowed_identity_providers: if std.objectHas(t, 'allowed_identity_providers') then
          t.allowed_identity_providers
        else
          standardIdentityProviders,
      }, tenants),

  // We will need to get rid of `tenants` if we want to go directly to K8s to create tenants.
  // Going forward, this will not be a complete list of tenants. See the visibility rules for
  // the tenants-lib target in BUILD for a list of services that still depend on this.
  tenants: this.processTenants(tenants.stagingTenants + tenants.prodTenants),

  dogfoodTenants: [tenants.stagingTenants[0]],

  tenantsWithoutSupportAccessControl: [
    t
    for t in this.tenants
    if !t.tenantFlags.supportAccessControl
  ],

  getTenantFlags:: function(tenant, namespace_configs)
    local baseFlags = if namespace_configs != null && std.objectHas(namespace_configs, 'defaultTenantFlags')
    then namespace_configs.defaultTenantFlags
    else defaultTenantFlags[tenant.env];
    if std.objectHas(tenant, 'tenantFlags') then
      // TODO: This does not actually work, because tenantFlags is usually a
      // copy of baseFlags with some overrides
      baseFlags + tenant.tenantFlags
    else
      baseFlags,
  relevantTenants:: function(env, namespace, cloud, namespace_config)
    if env == 'DEV' then namespace_config.tenants(
      namespace=namespace,
      cloud=cloud,
    ) else [t for t in this.tenants if t.env == env],
  getTenant:: function(env, namespace, cloud, namespace_config, tenantName)
    local relevantTenants = this.relevantTenants(env, namespace, cloud, namespace_config);
    local t = std.filter(function(t) t.name == tenantName, relevantTenants)[0];
    t,
  singleTenantNamespaces: std.filterMap(function(t) !t.tenantFlags.multiTenantAllowed,
                                        function(t) {
                                          namespace: t.namespace,
                                          cloud: t.cloud,
                                          env: t.env,
                                        },
                                        this.tenants),
}

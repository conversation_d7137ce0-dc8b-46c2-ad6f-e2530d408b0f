// this file contains information for all namespaces that run tenants based on new shard namespaces and legacy namespaces.
local lib = import 'deploy/common/lib.jsonnet';
local shardNamespaces = import 'shard_namespaces.jsonnet';
local tenants = import 'tenants_lib.jsonnet';

local aiTutorNamespaceNames = ['aitutor-turing', 'aitutor-mercor'];
local dogfoodNamespaceNames = ['staging-shard-0', 'shv-staging'];
local vanguardNamespaceNames = ['i0', 'i1'];
local discoveryNamespaceNames = ['d%s' % i for i in std.range(0, 20)];

local isShardNamespace(env, namespace, cloud) = std.length(std.filter(function(sns) namespace == sns.namespace && env == sns.env && cloud == sns.cloud, shardNamespaces)) > 0;
{
  local this = self,
  namespaces: shardNamespaces + tenants.singleTenantNamespaces,
  stagingNamespaces: [ns for ns in this.namespaces if ns.env == 'STAGING'],
  prodNamespaces: [ns for ns in this.namespaces if ns.env == 'PROD'],
  dogfoodNamespaces: [ns for ns in this.namespaces if lib.contains(dogfoodNamespaceNames, ns.namespace)],
  vanguardNamespaces: [ns for ns in this.namespaces if lib.contains(vanguardNamespaceNames, ns.namespace)],
  aiTutorNamespaces: [ns for ns in this.namespaces if lib.contains(aiTutorNamespaceNames, ns.namespace)],
  discoveryNamespaces: [ns for ns in this.namespaces if lib.contains(discoveryNamespaceNames, ns.namespace)],
  isShardNamespace:: isShardNamespace,
}

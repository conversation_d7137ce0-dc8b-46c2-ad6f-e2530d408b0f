// This file contains consistency tests for the tenant and namespace configuration.
local lib = import 'deploy/common/lib.jsonnet';
local namespaces = import 'namespaces.jsonnet';
local shardNamespaces = import 'shard_namespaces.jsonnet';
local tenantFlags = import 'tenant_flags.jsonnet';
local tenants = import 'tenants_lib.jsonnet';

// Import the aggregated prod tenants that are generated by the more_prod_tenants genrule
local moreProdTenants = import 'more_prod_tenants.jsonnet';

// Combine all tenants for validation
local allTenants = tenants.tenants + moreProdTenants;

// Function to check if a string starts with a letter
local startsWithLetter(str) =
  // Convert the first character of the string to its Unicode code point
  local firstCharCode = std.codepoint(str[0]);
  // Check if the Unicode code point falls within the range of letters a-z
  firstCharCode >= 97 && firstCharCode <= 122;

// list of allowed legacy namespaces
// do not add to this list starting 7/3/2024
local legacyNamespaces = [
  'pre-prod',
  'advisor360',
  'aitutor-mercor',
  'aitutor-pareto',
  'aitutor-turing',
  'ampsortation',
  'augmentdemo',
  'collective',
  'eikon',
  'flume',
  'gopigment',
  'lemonade',
  'lmnt',
  'meinauto',
  'montecarlodata',
  'newfront',
  'nucleus',
  'observeinc',
  'onebuild',
  'onelineage',
  'paystone',
  'pocketlaw',
  'pollyex',
  'profiq',
  'purestorage',
  'realtor',
  'reprally',
  'reveart',
  'roboto',
  'samaya',
  'trilogy',
  'viaduct-eu',
  'webflow',
];

local validateTenant(t) =
  assert std.length(t.namespace) < 16 : 'namespace must be less than 16 characters';
  assert startsWithLetter(t.namespace) : 'namespace must start with a letter';
  assert startsWithLetter(t.name) : 'name must start with a letter';
  assert std.objectHas(t, 'tier') : 'tier must be set for tenant: ' + t.name;
  assert std.member(['ENTERPRISE', 'PROFESSIONAL', 'COMMUNITY'], t.tier) : 'tier must be one of: ENTERPRISE, PROFESSIONAL, COMMUNITY. Got: ' + t.tier;

  if t.tenantFlags.multiTenantAllowed then
    assert lib.contains([ns.namespace for ns in shardNamespaces], t.namespace) : 'namespace must be in shard namespaces';

    if t.tenantFlags.supportTenant then
      assert t.namespace == t.name : 'name and namespace must be equal';
      true
    else
      true
  else
    assert t.tenantFlags.supportTenant == false : 'supportTenant must be false in legacy tenants';
    assert t.name == t.namespace : 'name and namespace must be equal';
    assert lib.contains(legacyNamespaces, t.namespace) : 'namespace must be in legacy namespaces';
    true;

local hasSupportTenant(ns) =
  assert std.length(std.filter(function(t) t.name == ns.namespace && t.namespace == ns.namespace, allTenants)) > 0;
  true;

local validateNamespaceName(ns) =
  assert std.length(ns.namespace) < 16 : 'namespace must be less than 16 characters';
  assert startsWithLetter(ns.namespace) : 'namespace must start with a letter';
  true;

local validateNamespace(ns) =
  hasSupportTenant(ns);

local validateNamespaces() =
  // namespaces should be unique
  assert std.length(std.set(std.map(function(c) c.namespace, namespaces.namespaces))) == std.length(namespaces.namespaces);
  // the 10 char prefix needs to be unique.
  // some objects like IAM service accounts and DNS names have size limits, see gpc-lib.jsonnet for details
  assert std.length(std.set(std.map(function(c) std.substr(c.namespace, 0, 10), namespaces.namespaces))) == std.length(namespaces.namespaces);

  // validate that each namespace
  assert std.length(std.filter(function(c) validateNamespace(c), namespaces.namespaces)) == std.length(namespaces.namespaces);
  true;

local validateTenantIds() =
  local tenantsWithIdsSet = std.filter(function(t) t.tenantId != null, allTenants);
  assert std.length(std.set(std.map(function(c) c.tenantId, tenantsWithIdsSet))) == std.length(tenantsWithIdsSet) : 'tenant ids must be unique';
  true;

local validateTenantNames() =
  // For now, tenant names must be unique (and also should not change). Tenant
  // names are mainly used in the auth-central DB today, and are also used in
  // bigquery.
  assert std.length(std.set(std.map(function(c) c.name, allTenants))) == std.length(allTenants) : 'tenant names must be unique';
  true;

local validateLegacySelfServeTeamsDomains() =
  // Legacy self serve team tenants should not have domain attributes
  local legacySelfServeTeams = std.filter(function(t)
    std.objectHas(t, 'tenantFlags') &&
    std.objectHas(t.tenantFlags, 'isLegacySelfServeTeam') &&
    t.tenantFlags.isLegacySelfServeTeam, allTenants);
  local legacySelfServeTeamsWithDomain = std.filter(function(t)
    std.objectHas(t, 'domain'), legacySelfServeTeams);
  assert std.length(legacySelfServeTeamsWithDomain) == 0 :
         'Legacy self serve team tenants should not have domain attributes. Found: ' +
         std.toString([t.name for t in legacySelfServeTeamsWithDomain]);
  true;

local validateSelfServeTeamsOnProfessionalNamespaces() =
  // Self serve teams must be on professional namespaces
  local selfServeTeams = std.filter(function(t)
    std.objectHas(t, 'tenantFlags') &&
    std.objectHas(t.tenantFlags, 'isSelfServeTeam') &&
    t.tenantFlags.isSelfServeTeam &&
    t.env != 'DEV', allTenants);
  local professionalNamespaces = std.set([t.namespace for t in allTenants if t.tier == 'PROFESSIONAL']);
  local invalidSelfServeTeams = std.filter(function(t)
    local defaultNamespaceIsProfessional = std.member(professionalNamespaces, t.namespace);
    !defaultNamespaceIsProfessional, selfServeTeams);
  assert std.length(invalidSelfServeTeams) == 0 :
         'Self serve teams must be on professional namespaces. Found teams without professional default namespace: ' +
         std.toString([t.name for t in invalidSelfServeTeams]);
  true;

local validate() =
  [validateTenant(t) for t in allTenants] + [
    validateTenantIds(),
    validateTenantNames(),
    validateNamespaces(),
    validateLegacySelfServeTeamsDomains(),
    validateSelfServeTeamsOnProfessionalNamespaces(),
  ];

// Run validation on all tenants
validate()

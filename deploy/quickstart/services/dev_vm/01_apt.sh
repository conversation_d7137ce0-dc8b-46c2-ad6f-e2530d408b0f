#!/bin/bash -x

set -euxo pipefail

# Check if the script is run as root
if [ "$(id -u)" -eq 0 ]; then
	echo "Error: This script should not be run as root."
	exit 1
fi

sudo -i bash <<'EOF'
    set -euxo pipefail

    if lsb_release -cs | grep -q "focal"; then
        apt-get update
        DEBIAN_FRONTEND=noninteractive apt-get -y install \
            docker.io \
            nfs-common \
            unzip \
            zip \
            libssl-dev \
            libtinfo5 \
            libhwloc-dev \
            xvfb \
            tzdata
    else
        apt-get update
        DEBIAN_FRONTEND=noninteractive apt-get -y install \
            docker.io \
            nfs-common \
            unzip \
            zip \
            gcc \
            g++ \
            libasound2 \
            libatk1.0-0 \
            libatspi2.0-0 \
            libatk-bridge2.0-0 \
            libcairo2 \
            libgtk-3-0 \
            libgbm1 \
            libpango-1.0-0 \
            libxcomposite1 \
            libxdamage1 \
            libxkbcommon0 \
            libxrandr2 \
            libssl-dev \
            libtinfo5 \
            libhwloc-dev \
            libssl-dev \
            libstdc++6-12-dbg \
            pkg-config \
            xvfb \
            tzdata
    fi
EOF

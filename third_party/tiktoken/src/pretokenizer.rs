// This file contains special handling of different pre-tokenization patterns.
// These are typically done with regexes, but that is slow.
use fancy_regex::Regex;
use fancy_regex::RegexBuilder;

use std::num::NonZeroU64;
use std::thread;

use pyo3::exceptions;
use pyo3::prelude::*;
// use pyo3::types::{PyBytes, PyList, PyTuple};
use pyo3::PyResult;

const GPT2_PATTERN: &str =
    r"'s|'t|'re|'ve|'m|'ll|'d| ?[\p{L}]+| ?[\p{N}]+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+";
// This is a pattern semantically equivalent to the GPT2 pattern, but executes faster.
const ALTERNATE_GPT2_PATTERN: &str =
    r"'(?:[sdmt]|ll|ve|re)| ?\p{L}++| ?\p{N}++| ?[^\s\p{L}\p{N}]++|\s++$|\s+(?!\S)|\s";
const QWEN25CODER_PATTERN: &str = r"(?i:'s|'t|'re|'ve|'m|'ll|'d)|[^\r\n\p{L}\p{N}]?\p{L}+|\p{N}| ?[^\s\p{L}\p{N}]+[\r\n]*|\s*[\r\n]+|\s+(?!\S)|\s+";

// Copied from tiktoken
pub struct FakeThreadId(NonZeroU64);

// Copied from tiktoken
fn _hash_current_thread() -> usize {
    // It's easier to use unsafe than to use nightly. Rust has this nice u64 thread id counter
    // that works great for our use case of avoiding collisions in our array. Unfortunately,
    // it's private. However, there are only so many ways you can layout a u64, so just transmute
    // https://github.com/rust-lang/rust/issues/67939
    const _: [u8; 8] = [0; std::mem::size_of::<thread::ThreadId>()];
    const _: [u8; 8] = [0; std::mem::size_of::<FakeThreadId>()];
    let x =
        unsafe { std::mem::transmute::<thread::ThreadId, FakeThreadId>(thread::current().id()).0 };
    u64::from(x) as usize
}

// Copied from https://huggingface.co/Qwen/Qwen2.5-Coder-7B/blob/main/tokenizer.json
// Pattern: r"""(?i:'s|'t|'re|'ve|'m|'ll|'d)|[^\r\n\p{L}\p{N}]?\p{L}+|\p{N}| ?[^\s\p{L}\p{N}]+[\r\n]*|\s*[\r\n]+|\s+(?!\S)|\s+"""
// Cases:
// 1.  (?i:'s|'t|'re|'ve|'m|'ll|'d)
// 2.  [^\r\n\p{L}\p{N}]?\p{L}+
// 3.  \p{N}
// 4.   ?[^\s\p{L}\p{N}]+[\r\n]*
// 5.  \s*[\r\n]+
// 6.  \s+(?!\S)
// 7.  \s+
#[allow(clippy::single_char_pattern)]
fn split_qwen25coder_pattern(text: &str) -> Vec<&str> {
    let mut tokens = Vec::new();
    let mut pos = 0;

    while pos < text.len() {
        let remaining_str = &text[pos..];

        // Case 1: (?i:'s|'t|'re|'ve|'m|'ll|'d)
        // In contrast to the GPT-2 pattern, this pattern is case-INsensitive
        // Doing this in ASCII vs unicode chars gives a small speedup.
        // The rest of this function uses unicode chars.
        let first_byte = text.as_bytes()[pos];
        if first_byte == b'\'' {
            // Using a separate chars iterator here because we need a separate lookahead.
            // Skip the apostrophe and get remaining bytes
            let remaining_bytes = &remaining_str.as_bytes()[1..];

            // Need at least 1 byte after apostrophe
            let contraction_len = if !remaining_bytes.is_empty() {
                match (
                    remaining_bytes[0] | 32,
                    remaining_bytes.get(1).map(|b| *b | 32),
                ) {
                    // Two letter contractions ('ll, 've, 're)
                    (b'l', Some(b'l')) | (b'v', Some(b'e')) | (b'r', Some(b'e')) => Some(3),
                    // Single letter contractions ('s, 'd, 'm, 't)
                    (b's', _) | (b'd', _) | (b'm', _) | (b't', _) => Some(2),
                    _ => None,
                }
            } else {
                None
            };

            if let Some(len) = contraction_len {
                tokens.push(&remaining_str[..len]);
                pos += len;
                continue;
            }
        }

        let mut match_length: usize = 0;
        // Leading space may need to be skipped before determining which case we are in
        let mut remaining = remaining_str.chars().peekable();
        // We want to match at least one character
        let first_char = remaining.next().unwrap(); // pops the first character
        match_length += first_char.len_utf8();
        let next_char = match remaining.peek() {
            Some(c) => *c,
            None => {
                // We are at the end of the string and want to return
                // the last character no matter what case we are in.
                // This simplifies the logic below, as we can assume to
                // know the next character.
                pos += match_length;
                let token_str = &remaining_str[..match_length];
                tokens.push(token_str);
                continue;
            }
        };

        // Case 3: \p{N}
        if first_char.is_numeric() {
            pos += match_length;
            let token_str = &remaining_str[..match_length];
            tokens.push(token_str);
            continue;
        }

        // Case 2: [^\r\n\p{L}\p{N}]?\p{L}+
        //
        // It is okay to invert the order of case 2 and 3, because we can
        // distinguish between case 2 and 3 by checking if the first character
        // is a digit or not.
        if first_char.is_alphabetic()
            || !first_char.is_alphabetic()
                && !first_char.is_numeric()
                && first_char != '\n'
                && first_char != '\r'
                && next_char.is_alphabetic()
        {
            for c in remaining {
                if c.is_alphabetic() {
                    match_length += c.len_utf8();
                } else {
                    break;
                }
            }
            pos += match_length;
            let token_str = &remaining_str[..match_length];
            tokens.push(token_str);
            continue;
        }

        // Case 4: " ?[^\s\p{L}\p{N}]+[\r\n]*"
        if (first_char == ' '
            && !next_char.is_alphabetic()
            && !next_char.is_numeric()
            && !next_char.is_whitespace())
            || (!first_char.is_alphabetic()
                && !first_char.is_numeric()
                && !first_char.is_whitespace())
        {
            let mut seen_trailing_newline = false;
            for c in remaining {
                if !seen_trailing_newline
                    && !c.is_alphabetic()
                    && !c.is_numeric()
                    && !c.is_whitespace()
                {
                    match_length += c.len_utf8();
                } else if c == '\n' || c == '\r' {
                    seen_trailing_newline = true;
                    match_length += c.len_utf8();
                } else {
                    break;
                }
            }

            pos += match_length;
            let token_str = &remaining_str[..match_length];
            tokens.push(token_str);
            continue;
        }

        // All three remaining cases are whitespace only. We don't need to distinguish between them,
        // but we do need to stop at the right time.
        // Case 5: \s*[\r\n]+  // whitespace ending in line break has priority over case 6, which is a negative lookahead.
        // Case 6: \s+(?!\S)
        // Case 7: \s+
        //
        // The tricky case is the interaction of case 5 and 6. Case 6 backtracks by
        // 1 position if the next character is a non-whitespace character, but case 5
        // limits this behavior to scenarios where the last (whitespace) character
        // was a newline.
        //
        let mut is_case_5 = first_char == '\n' || first_char == '\r';
        let mut last_case_5_stopping_point = 0;
        if is_case_5 {
            last_case_5_stopping_point = match_length;
        }
        let mut last_char_length = 0;
        for c in remaining {
            if !c.is_whitespace() {
                if !is_case_5 {
                    // remove the last character, this is simulating the (?!\S) lookahead
                    match_length -= last_char_length;
                }
                break;
            }
            last_char_length = c.len_utf8();
            match_length += last_char_length;
            if c == '\n' || c == '\r' {
                is_case_5 = true;
                last_case_5_stopping_point = match_length;
            }
        }
        if is_case_5 {
            match_length = last_case_5_stopping_point;
        }
        assert!(match_length > 0, "failed to find any matching token");
        pos += match_length;
        let token_str = &remaining_str[..match_length];
        tokens.push(token_str);
    }
    tokens
}

// Splits a string in the same way as the GPT-2 tokenizer pattern.
// r"""'s|'t|'re|'ve|'m|'ll|'d| ?[\p{L}]+| ?[\p{N}]+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+"""
//
// This is the same pattern string used by HuggingFace tokenizer.
//
// This is what this regex magic does:
// `'s|'t|'re|'ve|'m|'ll|'d`: Matches common English contractions.
// ` ?\p{L}+`: Matches optional space character followed by one or more Unicode letters (\p{L}+).
// ` ?\p{N}+`: Matches optional space character followed by one or more digits.
// ` ?[^\s\p{L}\p{N}]+`: Matches optional space character followed by anything other than digits or unicode letters (so punctuation, etc.).
// `\s+(?!\S)`: Matches whitespace that isn't followed by a non-whitespace character.
// `\s+`: Matches any remaining whitespace.
#[allow(clippy::single_char_pattern)]
pub fn split_gpt2_pattern(text: &str) -> Vec<&str> {
    let mut tokens = Vec::new();
    let mut pos = 0;

    while pos < text.len() {
        let remaining_str = &text[pos..];
        let first_char = text.as_bytes()[pos];

        // Scan for contractions 's | 't | 're | 've | 'm | 'll | 'd
        if first_char == b'\'' {
            // Only checking ASCII apostrophe for English contractions.
            // Doing this in ASCII vs unicode chars gives a small speedup.
            let contraction_len = match &remaining_str[1..] {
                s if s.starts_with("ll") || s.starts_with("ve") || s.starts_with("re") => Some(3),
                s if s.starts_with("s")
                    || s.starts_with("d")
                    || s.starts_with("m")
                    || s.starts_with("t") =>
                {
                    Some(2)
                }
                _ => None,
            };

            if let Some(len) = contraction_len {
                pos += len;
                tokens.push(&remaining_str[..len]);
                continue;
            }
        }

        let mut match_length: usize = 0;
        // Leading space may need to be skipped before determining which case we are in
        let mut remaining = remaining_str.chars().peekable();
        let mut match_case_char = *remaining.peek().unwrap(); // safe because "while pos < text.len()"
        if first_char == b' ' && pos + 1 < text.len() {
            match_length += 1;
            remaining.next(); // skip the space
            match_case_char = *remaining.peek().unwrap();
        }

        match match_case_char {
            m if m.is_alphabetic() => {
                // UnicodeLetter
                for c in remaining {
                    if c.is_alphabetic() {
                        match_length += c.len_utf8();
                    } else {
                        break;
                    }
                }
            }
            m if m.is_numeric() => {
                // Digit
                for c in remaining {
                    if c.is_numeric() {
                        match_length += c.len_utf8();
                    } else {
                        break;
                    }
                }
            }
            m if m.is_whitespace() => {
                // Whitespace
                let mut last_char_length = 0;
                for c in remaining {
                    if c.is_whitespace() {
                        last_char_length = c.len_utf8();
                        match_length += last_char_length;
                    } else {
                        if match_length > last_char_length {
                            // remove the last character, this is simulating the (?!\S) lookahead
                            match_length -= last_char_length;
                        }
                        break;
                    }
                }
            }
            _ => {
                // NonLetterNonDigit
                for c in remaining {
                    if !c.is_alphabetic() && !c.is_numeric() && !c.is_whitespace() {
                        match_length += c.len_utf8();
                    } else {
                        break;
                    }
                }
            }
        }
        assert!(match_length > 0, "failed to find any matching token");
        pos += match_length;
        let token_str = &remaining_str[..match_length];
        tokens.push(token_str);
    }

    tokens
}

#[derive(Debug, PartialEq, Eq)]
enum PreTokenizerPattern {
    Qwen25Coder,
    Gpt2,
    Regex, // fallback
}

pub struct PreTokenizer {
    // pattern: Option<String>  // not needed
    regex_tls: Option<Vec<Regex>>,
    pattern_type: PreTokenizerPattern,
}

impl PreTokenizer {
    pub fn new_with_threads(pattern: &str, max_num_threads: usize) -> PyResult<Self> {
        let pattern_type = match pattern {
            QWEN25CODER_PATTERN => PreTokenizerPattern::Qwen25Coder,
            GPT2_PATTERN => PreTokenizerPattern::Gpt2,
            ALTERNATE_GPT2_PATTERN => PreTokenizerPattern::Gpt2,
            _ => PreTokenizerPattern::Regex,
        };
        let regex = RegexBuilder::new(pattern)
            .backtrack_limit(10_000) // limits worst-case behavior
            .build()
            .map_err(|e| PyErr::new::<exceptions::PyValueError, _>(e.to_string()))?;
        let regex_tls = (0..max_num_threads).map(|_| regex.clone()).collect();
        Ok(PreTokenizer {
            regex_tls: Some(regex_tls),
            pattern_type,
        })
    }

    pub fn split<'s, 't>(
        &'s self,
        text: &'t str,
        force_regex: bool,
    ) -> Box<dyn Iterator<Item = &'t str> + 't>
    where
        's: 't, // self must outlive text
    {
        let pattern_type = if force_regex {
            &PreTokenizerPattern::Regex
        } else {
            &self.pattern_type
        };
        match pattern_type {
            PreTokenizerPattern::Qwen25Coder => {
                Box::new(split_qwen25coder_pattern(text).into_iter())
            }
            PreTokenizerPattern::Gpt2 => Box::new(split_gpt2_pattern(text).into_iter()),
            PreTokenizerPattern::Regex => {
                let reg_exp = &self.regex_tls.as_ref().unwrap()  // safe because we checked pattern_type
                [_hash_current_thread() % self.regex_tls.as_ref().unwrap().len()];
                Box::new(reg_exp.find_iter(text).map(|m| m.unwrap().as_str()))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use rand::seq::SliceRandom;
    use rand::Rng;
    use rand::SeedableRng;

    fn generate_random_test_string(rng: &mut impl rand::Rng, length: usize) -> String {
        const CHARS: &[&str] = &[
            "1", " ", "a", "\r", "\n", "\t", "世", "界", ";", "_", "'", "m", "M", "L",
        ];
        let mut result = String::with_capacity(length * 2); // Extra capacity for multi-byte chars

        for _ in 0..length {
            result.push_str(CHARS.choose(rng).unwrap()); // Using choose instead of choose_mut
        }

        result
    }

    #[test]
    fn test_random_strings_qwen25coder() {
        let mut rng = rand::rngs::StdRng::seed_from_u64(42); // Fixed seed for reproducibility
        let pattern = QWEN25CODER_PATTERN;
        let re = Regex::new(pattern).unwrap();

        for i in 0..100000 {
            let len = rng.gen_range(1..=100);
            let random_str = generate_random_test_string(&mut rng, len);
            println!("Random case: {:?}", i);

            // Get expected tokens using regex
            let mut pos = 0;
            let mut expected = Vec::new();
            for m in re.find_iter(&random_str).filter_map(|m| m.ok()) {
                if m.start() > pos {
                    expected.push(random_str[pos..m.start()].to_string());
                }
                expected.push(m.as_str().to_string());
                pos = m.end();
            }
            if pos < random_str.len() {
                expected.push(random_str[pos..].to_string());
            }

            // Get tokens using non-regex implementation
            let result = split_qwen25coder_pattern(&random_str);

            assert_eq!(
                result, expected,
                "\nInput: {:?}\nRegex splits: {:?}\nNon-regex splits: {:?}",
                random_str, expected, result
            );
        }
    }

    #[test]
    fn test_random_strings_gpt2() {
        let mut rng = rand::rngs::StdRng::seed_from_u64(42); // Fixed seed for reproducibility
        let pattern = GPT2_PATTERN;
        let re = Regex::new(pattern).unwrap();

        for i in 0..100000 {
            let len = rng.gen_range(1..=100);
            let random_str = generate_random_test_string(&mut rng, len);
            println!("Random case: {:?}", i);

            // Get expected tokens using regex
            let mut pos = 0;
            let mut expected = Vec::new();
            for m in re.find_iter(&random_str).filter_map(|m| m.ok()) {
                if m.start() > pos {
                    expected.push(random_str[pos..m.start()].to_string());
                }
                expected.push(m.as_str().to_string());
                pos = m.end();
            }
            if pos < random_str.len() {
                expected.push(random_str[pos..].to_string());
            }

            // Get tokens using non-regex implementation
            let result = split_gpt2_pattern(&random_str);

            assert_eq!(
                result, expected,
                "\nInput: {:?}\nRegex splits: {:?}\nNon-regex splits: {:?}",
                random_str, expected, result
            );
        }
    }

    const TEST_CASES: &[&str] = &[
        "m\n \t  ",
        "\r\t\r\n",
        "123",
        "I'm",
        "I'M",
        ";",
        ";;",
        " ;",
        "; ",
        ";\n",
        " ;\n",
        ";\n ",
        " ;\r\r\n",
        ";\r\r \n ",
        " \nn",
        " \n\nn",
        "  #",
        " #",
        "I'm",
        "She'll",
        "She'LL",
        "She'lL",
        "She'Ll",
        ";word",
        "snake_case",
        "camelCase",
        "Hello   world!",
        "Testing123",
        "!@#$%",
        "Line1\nLine2",
        "fn test_example(x: i32) -> String { x.to_string() }",
        "def calculate_sum(numbers: List[int]) -> int:\n    total = sum(num for num in numbers)\n    return total  # Return the final sum",
        "",
        " ",
        "\n",
        "  ",
        "   ",
        "    ",
        "123",
        "_123",
        "123_",
        "123.456",
        "123.456.789",
        "123.456_789",
        "asdf_123",
        "asdf _123",
        "asdf_ 123",
        "asdf_123 ",
        "asdf_123\n",
        "asdf_123\n\n",
        "\n",
        "\n\n",
        "a b",
        "a\tb",
        "a\nb",
        "a\u{0020}b",
        "a\u{00A0}b",
        "a\u{1680}b",
        "a\u{2000}b",
        "a\u{2001}b",
        " \n ",
        "\t\n\t",
        "\t ",
        " \t",
        " \t \n",
        " word",
        "word ",
        "word\n",
        "word\nword",
        "\nword",
        " ;",
        "; ",
        "  word",
        "word  ",
        "'ve",
        "'",
        "'v",
        " 've",
        "'ve ",
        "\0",
        "\0\0",
        "\0\0\0",
        "世界",
        "a😃b𐐷 a😃b𐐷 ",
    ];

    #[test]
    fn test_split_gpt2_pattern() {
        let pattern = GPT2_PATTERN;
        let re = Regex::new(pattern).unwrap();

        for input in TEST_CASES {
            println!("\n\nTesting input: {}", input);
            // Get expected tokens using regex
            let mut pos = 0;
            let mut expected = Vec::new();
            for m in re.find_iter(input).filter_map(|m| m.ok()) {
                if m.start() > pos {
                    expected.push(input[pos..m.start()].to_string());
                }
                expected.push(m.as_str().to_string());
                pos = m.end();
            }
            if pos < input.len() {
                expected.push(input[pos..].to_string());
            }

            let result = split_gpt2_pattern(input);
            assert_eq!(
                result, expected,
                "\nInput: '{}'\nFailed to match regex pattern",
                input
            );
        }
    }

    #[allow(clippy::assertions_on_constants)]
    #[test]
    fn test_split_gpt2_pattern_performance() {
        let pattern = ALTERNATE_GPT2_PATTERN;
        let re = Regex::new(pattern).unwrap();

        // Use a subset of test cases for performance testing
        let perf_test_cases = &[
        "I'm going to the store. She'll be there at 5:30. Hello   world! Testing123 !@#$% Line1\nLine2 It's don't won't 42 is the answer Hello, 世界!",
    ];

        for input in perf_test_cases {
            let input = &input.repeat(1000);
            println!("Testing input length: {}", input.len());
            // and now with the regex
            let start = std::time::Instant::now();
            let _result = re
                .find_iter(input)
                .filter_map(|m| m.ok())
                .collect::<Vec<_>>();
            let elapsed = start.elapsed();
            println!("Time taken: {:?}", elapsed);

            let start = std::time::Instant::now();
            let _result = split_gpt2_pattern(input);
            let elapsed = start.elapsed();
            println!("Time taken: {:?}", elapsed);
        }
        // assert!(false); // Uncomment to see the output of the test
    }

    #[test]
    fn test_split_qwen25coder_pattern() {
        let pattern = QWEN25CODER_PATTERN;
        let re = Regex::new(pattern).unwrap();

        for input in TEST_CASES {
            println!("Testing input: {}", input);
            // Get expected tokens using regex
            let mut pos = 0;
            let mut expected = Vec::new();
            for m in re.find_iter(input).filter_map(|m| m.ok()) {
                if m.start() > pos {
                    expected.push(input[pos..m.start()].to_string());
                }
                expected.push(m.as_str().to_string());
                pos = m.end();
            }
            if pos < input.len() {
                expected.push(input[pos..].to_string());
            }

            let result = split_qwen25coder_pattern(input);
            assert_eq!(
                result, expected,
                "\nInput: '{}'\nFailed to match regex pattern",
                input
            );
        }
    }

    #[allow(clippy::assertions_on_constants)]
    #[test]
    fn test_split_qwen25coder_pattern_performance() {
        let pattern = QWEN25CODER_PATTERN;
        let re = Regex::new(pattern).unwrap();

        // Use a subset of test cases for performance testing
        let perf_test_cases = &[
        "I'm going to the store. She'll be there at 5:30. Hello   world! Testing123 !@#$% Line1\nLine2 It's don't won't 42 is the answer Hello, 世界!",
    ];

        for input in perf_test_cases {
            let input = &input.repeat(1000);
            println!("Testing input length: {}", input.len());
            // and now with the regex
            let start = std::time::Instant::now();
            let _result = re
                .find_iter(input)
                .filter_map(|m| m.ok())
                .collect::<Vec<_>>();
            let elapsed = start.elapsed();
            println!("Time for regex: {:?}", elapsed);

            let start = std::time::Instant::now();
            let _result = split_qwen25coder_pattern(input);
            let elapsed = start.elapsed();
            println!("Time for custom split: {:?}", elapsed);
        }
        // assert!(false); // Uncomment to see the output of the test
    }
}

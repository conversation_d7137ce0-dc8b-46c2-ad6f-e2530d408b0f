// This check is new and seems buggy (possibly with PyO3 interaction)
#![allow(clippy::borrow_deref_ref)]
mod pretokenizer;

use std::collections::{BTreeMap, BTreeSet, HashSet};
use std::iter::successors;
use std::num::NonZeroU64;
use std::thread;

use crate::pretokenizer::PreTokenizer;
use fancy_regex::Regex;
use pyo3::exceptions;
use pyo3::prelude::*;
use pyo3::types::{PyBytes, PyList, PyTuple};
use pyo3::PyResult;
use rustc_hash::FxHashMap as HashMap;

type Rank = u32;

const LARGE_ENCODER_CHARACTER_LIMIT: usize = 500;

fn _byte_pair_merge(piece: &[u8], ranks: &HashMap<Vec<u8>, Rank>) -> Vec<(usize, Rank)> {
    if piece.len() <= LARGE_ENCODER_CHARACTER_LIMIT {
        _byte_pair_merge_small(piece, ranks) // Quadratic, but lightweight
    } else {
        _byte_pair_merge_large(piece, ranks) // Linearithmic, but heavy
    }
}

fn _byte_pair_merge_small(piece: &[u8], ranks: &HashMap<Vec<u8>, Rank>) -> Vec<(usize, Rank)> {
    // This is a vector of (start, rank).
    // The rank is of the byte pair starting at position start.
    // The rank of the last item in the vector is not a valid value.
    let mut parts: Vec<(usize, Rank)> = Vec::with_capacity(piece.len() + 1);

    // Note that we hash bytes when indexing into `ranks`, not token pairs. As long as we train BPE
    // the way we currently do, this is equivalent. An easy way to break this would be to decouple
    // merge priority from token index or to prevent specific token merges.
    let mut min_rank: (Rank, usize) = (Rank::MAX, usize::MAX);
    for i in 0..piece.len() - 1 {
        let rank = *ranks.get(&piece[i..i + 2]).unwrap_or(&Rank::MAX);
        if rank < min_rank.0 {
            min_rank = (rank, i);
        }
        parts.push((i, rank));
    }
    parts.push(((piece.len() - 1), Rank::MAX));
    parts.push((piece.len(), Rank::MAX));

    let get_rank = {
        #[inline(always)]
        |parts: &Vec<(usize, Rank)>, i: usize| -> Rank {
            if (i + 3) < parts.len() {
                // Similar to `piece[i..i + 2]` above. The +3 is because we haven't yet deleted
                // parts[i + 1], see comment in the main loop.
                *ranks
                    .get(&piece[parts[i].0..parts[i + 3].0])
                    .unwrap_or(&Rank::MAX)
            } else {
                Rank::MAX
            }
        }
    };

    // If you have n parts and m merges, this does O(mn) work.
    // We could do something with a heap and do O(m log n) work.
    // n is often very small so considerations like cache-locality outweigh the algorithmic
    // complexity downsides of the `parts` vector.
    while min_rank.0 != Rank::MAX {
        let i = min_rank.1;
        // Update parts[i] and parts[i - 1] before removing parts[i + 1], since
        // `parts.remove(i + 1)` will thrash the cache.
        if i > 0 {
            parts[i - 1].1 = get_rank(&parts, i - 1);
        }
        parts[i].1 = get_rank(&parts, i);
        parts.remove(i + 1);

        min_rank = (Rank::MAX, usize::MAX);
        for (i, &(_, rank)) in parts[..parts.len() - 1].iter().enumerate() {
            if rank < min_rank.0 {
                min_rank = (rank, i);
            }
        }
    }
    parts
}

fn _byte_pair_merge_large(piece: &[u8], ranks: &HashMap<Vec<u8>, Rank>) -> Vec<(usize, Rank)> {
    let mut rank_indexes = BTreeMap::<Rank, BTreeSet<usize>>::new();
    let mut index_rank = vec![Rank::MAX; piece.len() + 1];
    let mut index_prev = vec![usize::MAX; piece.len() + 1];
    let mut index_next = vec![usize::MAX; piece.len() + 1];

    let get_rank = |start_idx: usize, end_idx: usize| -> Rank {
        *piece
            .get(start_idx..end_idx)
            .and_then(|p| ranks.get(p))
            .unwrap_or(&Rank::MAX)
    };

    let mut prev_node = None;
    for i in 0..=piece.len() {
        let rank = get_rank(i, i + 2);
        index_rank[i] = rank;
        if let Some(prev) = prev_node {
            index_prev[i] = prev;
            index_next[prev] = i;
        }
        prev_node = Some(i);

        rank_indexes.entry(rank).or_default().insert(i);
    }

    while rank_indexes.len() > 1 {
        let mut skip_next = false;
        if let Some((_, nodes)) = rank_indexes.pop_first() {
            for &min_node in &nodes {
                if skip_next {
                    skip_next = false;
                    continue;
                }

                let min_rank = index_rank[min_node];

                let prev_node = index_prev[min_node];
                let next_node = index_next[min_node];
                let next_next_node = index_next[next_node];
                let next_next_next_node = index_next[next_next_node];
                if prev_node != usize::MAX {
                    let new_rank = get_rank(prev_node, next_next_node);
                    if index_rank[prev_node] != new_rank {
                        rank_indexes
                            .get_mut(&index_rank[prev_node])
                            .unwrap()
                            .remove(&prev_node);
                        index_rank[prev_node] = new_rank;
                        rank_indexes.entry(new_rank).or_default().insert(prev_node);
                    }
                }

                let new_rank = get_rank(min_node, next_next_next_node);
                index_rank[min_node] = new_rank;
                rank_indexes.entry(new_rank).or_default().insert(min_node);

                index_next[min_node] = next_next_node;
                index_prev[next_next_node] = min_node;

                let next_node_rank = index_rank[next_node];
                if next_node_rank == min_rank {
                    skip_next = true;
                } else if next_node_rank != Rank::MAX {
                    rank_indexes
                        .get_mut(&next_node_rank)
                        .unwrap()
                        .remove(&next_node);
                }
            }
        }
    }

    successors(Some(0), |&n| {
        index_next.get(n).filter(|&&x| x != usize::MAX).copied()
    })
    .map(|n: usize| (n, Rank::MAX))
    .collect()
}

pub fn byte_pair_encode(piece: &[u8], ranks: &HashMap<Vec<u8>, Rank>) -> Vec<Rank> {
    if piece.len() == 1 {
        return vec![ranks[piece]];
    }
    _byte_pair_merge(piece, ranks)
        .windows(2)
        .filter_map(|part| {
            let slice = &piece[part[0].0..part[1].0];
            ranks.get(slice).copied()
        })
        .collect()
}

pub fn byte_pair_split<'a>(piece: &'a [u8], ranks: &HashMap<Vec<u8>, Rank>) -> Vec<&'a [u8]> {
    assert!(piece.len() > 1);
    _byte_pair_merge(piece, ranks)
        .windows(2)
        .map(|part| &piece[part[0].0..part[1].0])
        .collect()
}

// Various performance notes:
//
// Regex
// =====
// Most of the time is spent in regex. The easiest way to speed this up is by using less fancy
// regex features. For instance, using a regex parse-able by `regex` crate is 3x faster than
// the usual regex we use.
//
// However, given that we're using a regex parse-able by `regex`, there isn't much difference
// between using the `regex` crate and using the `fancy_regex` crate.
//
// There is an important interaction between threading, `regex` and `fancy_regex`.
// When using `fancy_regex`, we hit `regex.find_at`. It turns out that this causes contention on
// some mutable scratch space inside of `regex`. This absolutely kills performance. When using plain
// old `regex`, we don't hit this, because `find_iter` has a different code path.
// Related: https://github.com/rust-lang/regex/blob/master/PERFORMANCE.md
// Anyway, the way we get around this is with having a (mostly) thread local clone of the regex for
// each thread.
//
// Threading
// =========
// I tried using `rayon`. It wasn't really faster than using Python threads and releasing the GIL.
// So goodbye `rayon`! Let thread count etc be in control of our Python users.
//
// Caching
// =======
// The reference tokeniser has an lru cache over the equivalent of `byte_pair_encode`.
// Originally, we had one too! Without it, we were only vaguely faster than Python.
// I used an RWLock to protect the cache. This didn't seem to hurt single threaded performance
// noticeably, but it did affect multi-threaded performance. Weirdly, it seemed to affect
// multi-threaded performance even when I only had readers (maybed I messed something up?).
// Anyway, I realised that we could get rid of the cache, if we treat the set of tokens as a cache!
// These are exactly the set or merges that are likely to be hot. And now we don't have to think
// about interior mutability, memory use, or cloning.
//
// Hashing
// =======
// We use FxHashMap instead of the standard HashMap. This is maybe like a 5-10% win?
// The current implementation ends up doing a lot of hashing of bytes. In theory, this could be made
// to be hashing of two-tuples of ints, which looks like it may also be a couple percent faster.

pub struct FakeThreadId(NonZeroU64);

fn hash_current_thread() -> usize {
    // It's easier to use unsafe than to use nightly. Rust has this nice u64 thread id counter
    // that works great for our use case of avoiding collisions in our array. Unfortunately,
    // it's private. However, there are only so many ways you can layout a u64, so just transmute
    // https://github.com/rust-lang/rust/issues/67939
    const _: [u8; 8] = [0; std::mem::size_of::<thread::ThreadId>()];
    const _: [u8; 8] = [0; std::mem::size_of::<FakeThreadId>()];
    let x =
        unsafe { std::mem::transmute::<thread::ThreadId, FakeThreadId>(thread::current().id()).0 };
    u64::from(x) as usize
}

const MAX_NUM_THREADS: usize = 128;
#[pyclass]
struct CoreBPE {
    encoder: HashMap<Vec<u8>, Rank>,
    special_tokens_encoder: HashMap<String, Rank>,
    decoder: HashMap<Rank, Vec<u8>>,
    special_tokens_decoder: HashMap<Rank, Vec<u8>>,
    pretokenizer: PreTokenizer,
    special_regex_tls: Vec<Regex>,
    sorted_token_bytes: Vec<Vec<u8>>,
}

impl CoreBPE {
    fn _get_tl_special_regex(&self) -> &Regex {
        &self.special_regex_tls[hash_current_thread() % MAX_NUM_THREADS]
    }

    fn _decode_native(&self, tokens: &[Rank]) -> Vec<u8> {
        let mut ret = Vec::with_capacity(tokens.len() * 2);
        for token in tokens {
            let token_bytes = self
                .decoder
                .get(token)
                .unwrap_or_else(|| &self.special_tokens_decoder[token]);
            ret.extend(token_bytes);
        }
        ret
    }

    /// Decode `tokens` into a UTF-8 string with start character offsets for each token.
    ///
    /// If `tokens` decodes into an invalid UTF-8 string, this method will replace
    /// the invalid bytes with the UTF-8 REPLACEMENT_CHARACTER. In Python, this
    /// corresponds to `bytes.decode(errors="replace")`.
    ///
    /// If a token maps to part of a unicode sequence (i.e. it isn't a valid character)
    /// by itself, we map the token to the start of the that character.
    fn _decode_with_offsets_native(&self, tokens: &[Rank]) -> (String, Vec<usize>) {
        let mut decoded_str = String::with_capacity(tokens.len() * 2);
        let mut offsets = Vec::with_capacity(tokens.len());

        // A buffer to keep track of torn UTF-8 bytes. We only use this buffer if some
        // token maps to a partial unicode sequence, and we try to move the bytes from
        // this buffer into decoded_str as soon as possible.
        // The buffer is initialized with a capacity of 256 because that's the largest
        // size of characters measured in one mapping table (StarCoder's).
        let mut torn_byte_buffer = Vec::with_capacity(256);
        let mut cur_offset = 0;
        for token in tokens {
            offsets.push(cur_offset);

            let token_bytes = self
                .decoder
                .get(token)
                .unwrap_or_else(|| &self.special_tokens_decoder[token]);

            let mut input;
            if torn_byte_buffer.is_empty() {
                // We don't have any previously torn bytes, so we can use token bytes
                // without copying them.
                input = token_bytes.as_slice();
            } else {
                // We have some previously torn bytes, so append into the byte buffer.
                torn_byte_buffer.extend(token_bytes);
                input = torn_byte_buffer.as_slice();
            }

            // This implementation is a modification of str::from_utf8_lossy to include
            // offset tracking and manage a single decoded_str.
            loop {
                match std::str::from_utf8(input) {
                    Ok(decoded_part) => {
                        // We were able to fully decode the byte buffer.
                        decoded_str.push_str(decoded_part);
                        cur_offset += decoded_part.chars().count();
                        input = &[];
                        break;
                    }
                    Err(error) => {
                        // Get the part of the buffer that's valid and push it
                        // to decoded_str.
                        let (valid, after_valid) = input.split_at(error.valid_up_to());
                        // Safety: We just found out this region was valid.
                        let valid_part;
                        unsafe {
                            valid_part = std::str::from_utf8_unchecked(valid);
                        }
                        cur_offset += valid_part.chars().count();
                        decoded_str.push_str(valid_part);

                        if let Some(invalid_sequence_length) = error.error_len() {
                            // We know for a fact that this part is invalid UTF-8,
                            // so we can just replace it with the REPLACEMENT CHARACTER.
                            decoded_str.push('\u{FFFD}');
                            cur_offset += 1;
                            input = &after_valid[invalid_sequence_length..]
                        } else {
                            // error_len returns None if the end of the sequence is
                            // invalid and less than 4 bytes, anticipating streaming
                            // use-cases like ours.
                            input = after_valid;
                            break;
                        }
                    }
                }
            }
            if input.is_empty() {
                torn_byte_buffer.clear();
            } else {
                // `input` is a slice into torn_byte_buffer, so we need to copy it first.
                torn_byte_buffer.splice(0.., input.to_vec());
            }
        }
        // Finally, if we finished reading our stream but still have some bytes that
        // couldn't be converted to UTF-8: replace them with the REPLACEMENT CHARACTER.
        if !torn_byte_buffer.is_empty() {
            decoded_str.push('\u{FFFD}');
        }

        (decoded_str, offsets)
    }

    fn _encode_ordinary_native(&self, text: &str) -> Vec<Rank> {
        // This is the core of the encoding logic; the other functions in here
        // just make things complicated :-)
        let mut ret = vec![];
        for mat in self.pretokenizer.split(text, false) {
            let piece = mat.as_bytes();
            match self.encoder.get(piece) {
                Some(token) => ret.push(*token),
                None => ret.extend(&byte_pair_encode(piece, &self.encoder)),
            }
        }
        ret
    }

    fn _encode_native(
        &self,
        text: &str,
        allowed_special: &HashSet<&str>,
        force_regex_pretokenizer: bool,
    ) -> (Vec<Rank>, usize) {
        let special_regex = self._get_tl_special_regex();
        let mut ret = vec![];

        let mut start = 0;
        let mut last_piece_token_len = 0;

        loop {
            let mut next_special;
            let mut start_find = start;
            loop {
                // Find the next allowed special token, if any
                next_special = special_regex.find_from_pos(text, start_find).unwrap();
                match next_special {
                    Some(m) => {
                        if allowed_special.contains(&text[m.start()..m.end()]) {
                            break;
                        }
                        start_find = m.start() + 1;
                    }
                    None => break,
                }
            }
            let end = next_special.map_or(text.len(), |m| m.start());

            // Okay, here we go, compare this logic to _encode_ordinary_native
            for mat in self
                .pretokenizer
                .split(&text[start..end], force_regex_pretokenizer)
            {
                let piece = mat.as_bytes();
                if piece.len() == 1 {
                    // For single bytes, skip if not found in encoder
                    if let Some(token) = self.encoder.get(piece) {
                        last_piece_token_len = 1;
                        ret.push(*token);
                    }
                    continue;
                }
                let tokens = byte_pair_encode(piece, &self.encoder);
                last_piece_token_len = tokens.len();
                ret.extend(&tokens);
            }

            match next_special {
                // And here we push the special token
                Some(m) => {
                    let piece = m.as_str();
                    let token = self.special_tokens_encoder[piece];
                    ret.push(token);
                    start = m.end();
                    last_piece_token_len = 0;
                }
                None => break,
            }
        }

        // last_piece_token_len is how many tokens came from the last regex split. This is used
        // for determining unstable tokens, since you can't merge across (stable) regex splits
        (ret, last_piece_token_len)
    }

    fn _increase_last_piece_token_len(
        &self,
        tokens: Vec<Rank>,
        mut last_piece_token_len: usize,
    ) -> (Vec<Rank>, usize) {
        // Unfortunately, the locations where our regex splits can be unstable.
        // For the purposes of determining unstable tokens, unstable regex splitting
        // is only a problem if a split that was present disappears, since this can
        // lead to merging of tokens otherwise thought to be stable.
        // cl100k_base makes our life hard by including the \s*[\r\n]+
        // pattern. This can e.g. cause "\n" + " " to become "\n \n".
        // Here is a quick and dirty fix:
        {
            let token_is_all_space = |token| {
                self.decoder
                    .get(token)
                    .map(|token_bytes| {
                        token_bytes
                            .iter()
                            .rev()
                            .all(|&b| [b' ', b'\n', b'\t'].contains(&b))
                    })
                    .unwrap_or(false)
            };
            if last_piece_token_len > 0
                && token_is_all_space(&tokens[tokens.len() - last_piece_token_len])
            {
                while (last_piece_token_len < tokens.len())
                    && token_is_all_space(&tokens[tokens.len() - last_piece_token_len - 1])
                {
                    last_piece_token_len += 1;
                }
            }
        }
        debug_assert!(last_piece_token_len <= tokens.len());

        (tokens, last_piece_token_len)
    }

    fn _encode_unstable_native(
        &self,
        text: &str,
        allowed_special: &HashSet<&str>,
    ) -> (Vec<Rank>, HashSet<Vec<Rank>>) {
        let (tokens, last_piece_token_len) = self._encode_native(text, allowed_special, false);
        if last_piece_token_len == 0 {
            // If last_piece_token_len is zero, the last token was a special token and we have
            // no unstable bytes
            return (tokens, HashSet::new());
        }
        let (mut tokens, last_piece_token_len) =
            self._increase_last_piece_token_len(tokens, last_piece_token_len);

        let unstable_bytes = self._decode_native(&tokens[tokens.len() - last_piece_token_len..]);
        tokens.truncate(tokens.len() - last_piece_token_len);

        // TODO: we should try harder to find additional stable tokens
        // This would reduce the amount of retokenising when determining completions
        // Refer to the logic in an older version of this file

        let mut completions = HashSet::new();
        if unstable_bytes.is_empty() {
            return (tokens, completions);
        }

        // This is the easy bit. Just find all single tokens that start with unstable_bytes
        // (including tokens that exactly match unstable_bytes)
        // Separating this from the loop below helps with performance in a common case.
        let mut point = self
            .sorted_token_bytes
            .partition_point(|x| x.as_slice() < unstable_bytes.as_slice());
        while point < self.sorted_token_bytes.len()
            && self.sorted_token_bytes[point].starts_with(&unstable_bytes)
        {
            completions.insert(vec![
                self.encoder[self.sorted_token_bytes[point].as_slice()],
            ]);
            point += 1;
        }

        // Now apply even more brute force. At every (other) possible position for the straddling
        // token, concatenate additional bytes from that token (if any) to unstable_bytes,
        // and retokenise the whole thing and see what we get.
        for i in 1..unstable_bytes.len() {
            let prefix = &unstable_bytes[..i];
            let suffix = &unstable_bytes[i..];
            let mut point = self
                .sorted_token_bytes
                .partition_point(|x| x.as_slice() < suffix);
            // TODO: Perf optimisation if suffix starts with " "?
            while point < self.sorted_token_bytes.len()
                && self.sorted_token_bytes[point].starts_with(suffix)
            {
                let possibility = [prefix, self.sorted_token_bytes[point].as_slice()].concat();
                let encoded = match std::str::from_utf8(&possibility) {
                    // Morally, this is byte_pair_encode(&possibility, &self.encoder)
                    // But we might have introduced a regex split which would prevent merges.
                    // (particularly possible in the presence of unstable regex splits)
                    // So convert to UTF-8 and do regex splitting.
                    // E.g. with cl100k_base "  !" gets split to " " + " !",
                    // but byte_pair_encode("  !") != byte_pair_encode(" ")
                    Ok(s) => self._encode_ordinary_native(s),

                    // Technically, whether or not this arm is correct depends on whether there
                    // would be a regex split before the UTF-8 truncation point.
                    // Probably niche enough that no one will ever notice (after all, people didn't
                    // notice all the big holes in the previous unstable token implementation)
                    Err(_) => byte_pair_encode(&possibility, &self.encoder),
                    // Something like the following is intriguing but incorrect:
                    // Err(e) => self._encode_ordinary_native(unsafe {
                    //     std::str::from_utf8_unchecked(&possibility[..e.valid_up_to()])
                    // }),
                };
                let mut seq = Vec::new();
                let mut seq_len = 0;
                for token in encoded {
                    seq.push(token);
                    seq_len += self.decoder[&token].len();
                    if seq_len >= unstable_bytes.len() {
                        break;
                    }
                }
                completions.insert(seq);
                point += 1;
            }
        }

        // This is also not straightforward. While we generally assume that regex splits are stable,
        // unfortunately, they are not. That is, if adding bytes were to make a split appear in
        // unstable_bytes, this could make tokens possible which our logic would otherwise think
        // would be merged.
        // For example, with gpt2, the use of \s+(?!\S) means that "\n\n" could
        // develop a split, e.g. "\n\n0" splits into "\n"+"\n"+"0", making "\n" a possible token.
        // Here is a quick and dirty fix:
        // This isn't right if we ever remove \s+(?!\S)
        if unstable_bytes.len() > 1 {
            let last_decoded = bstr::decode_last_utf8(unstable_bytes.as_slice());
            if unstable_bytes.len() - last_decoded.1 > 0
                && last_decoded.0.map_or(false, |c| c.is_whitespace())
            {
                let mut reencoded = byte_pair_encode(
                    &unstable_bytes[..unstable_bytes.len() - last_decoded.1],
                    &self.encoder,
                );
                reencoded.extend(byte_pair_encode(
                    &unstable_bytes[unstable_bytes.len() - last_decoded.1..],
                    &self.encoder,
                ));
                completions.insert(reencoded);
            }
        }

        (tokens, completions)
    }
}

#[pymethods]
impl CoreBPE {
    #[new]
    fn new(
        encoder: HashMap<Vec<u8>, Rank>,
        special_tokens_encoder: HashMap<String, Rank>,
        pattern: &str,
    ) -> PyResult<Self> {
        let special_regex = {
            let mut sorted_special_tokens: Vec<&String> = special_tokens_encoder.keys().collect();
            sorted_special_tokens.sort_by_key(|k| -(k.len() as isize));
            let _parts = sorted_special_tokens
                .iter()
                .map(|s| fancy_regex::escape(s))
                .collect::<Vec<_>>();
            Regex::new(&_parts.join("|"))
                .map_err(|e| PyErr::new::<exceptions::PyValueError, _>(e.to_string()))?
        };

        let decoder: HashMap<Rank, Vec<u8>> =
            encoder.iter().map(|(k, v)| (*v, k.clone())).collect();

        assert!(
            encoder.len() == decoder.len(),
            "Encoder and decoder must be of equal length; maybe you had duplicate token indices in your encoder?"
        );

        let special_tokens_decoder: HashMap<Rank, Vec<u8>> = special_tokens_encoder
            .iter()
            .map(|(k, v)| (*v, k.as_bytes().to_vec()))
            .collect();

        // Clone because I don't know how to tell Rust I'm not going to change the map
        let mut sorted_token_bytes: Vec<Vec<u8>> = encoder.keys().cloned().collect();
        sorted_token_bytes.sort();

        let pretokenizer = PreTokenizer::new_with_threads(pattern, MAX_NUM_THREADS)?;

        Ok(CoreBPE {
            encoder,
            special_tokens_encoder,
            decoder,
            special_tokens_decoder,
            pretokenizer,
            special_regex_tls: (0..MAX_NUM_THREADS)
                .map(|_| special_regex.clone())
                .collect(),
            sorted_token_bytes,
        })
    }

    // ====================
    // Encoding
    // ====================

    fn encode_ordinary(&self, py: Python, text: &str) -> Vec<Rank> {
        py.allow_threads(|| self._encode_ordinary_native(text))
    }

    fn encode(
        &self,
        py: Python,
        text: &str,
        allowed_special: HashSet<&str>,
        force_regex_pretokenizer: bool,
    ) -> Vec<Rank> {
        py.allow_threads(|| {
            self._encode_native(text, &allowed_special, force_regex_pretokenizer)
                .0
        })
    }

    fn _encode_bytes(&self, py: Python, bytes: &[u8]) -> Vec<Rank> {
        py.allow_threads(|| {
            match std::str::from_utf8(bytes) {
                Ok(text) => self._encode_ordinary_native(text),
                Err(e) => {
                    let text = unsafe { std::str::from_utf8_unchecked(&bytes[..e.valid_up_to()]) };
                    let (tokens, last_piece_token_len) =
                        self._encode_native(text, &HashSet::new(), false);
                    let (mut tokens, last_piece_token_len) =
                        self._increase_last_piece_token_len(tokens, last_piece_token_len);
                    if !tokens.is_empty() && last_piece_token_len > 0 {
                        // Lop off the tokens from the last piece and run BPE on the remaining bytes
                        // Somewhat niche, but this may not be correct if we'd have had a regex
                        // split between the valid UTF-8 and the invalid bytes, which is why this
                        // method is private
                        let mut unstable_bytes =
                            self._decode_native(&tokens[tokens.len() - last_piece_token_len..]);
                        unstable_bytes.extend_from_slice(&bytes[e.valid_up_to()..]);

                        tokens.truncate(tokens.len() - last_piece_token_len);
                        match self.encoder.get(&unstable_bytes) {
                            Some(token) => tokens.push(*token),
                            None => {
                                tokens.extend(&byte_pair_encode(&unstable_bytes, &self.encoder))
                            }
                        }
                    }
                    tokens
                }
            }
        })
    }

    fn encode_with_unstable(
        &self,
        py: Python,
        text: &str,
        allowed_special: HashSet<&str>,
    ) -> Py<PyTuple> {
        let (tokens, completions) =
            py.allow_threads(|| self._encode_unstable_native(text, &allowed_special));
        let py_completions =
            PyList::new(py, completions.iter().map(|seq| PyList::new(py, &seq[..])));

        (tokens, py_completions).into_py(py)
    }

    fn encode_single_token(&self, piece: &[u8]) -> PyResult<Rank> {
        if let Some(token) = self.encoder.get(piece).copied() {
            return Ok(token);
        }
        if let Ok(piece_str) = std::str::from_utf8(piece) {
            if let Some(token) = self.special_tokens_encoder.get(piece_str).copied() {
                return Ok(token);
            }
        }
        Err(PyErr::new::<exceptions::PyKeyError, _>(piece.to_owned()))
    }

    fn encode_single_piece(&self, piece: &[u8]) -> Vec<Rank> {
        if let Some(token) = self.encoder.get(piece) {
            return vec![*token];
        }
        byte_pair_encode(piece, &self.encoder)
    }

    // ====================
    // Decoding
    // ====================

    fn decode_bytes(&self, py: Python, tokens: Vec<Rank>) -> Py<PyBytes> {
        let bytes = py.allow_threads(|| self._decode_native(&tokens));
        PyBytes::new(py, &bytes).into()
    }

    fn decode_with_offsets(&self, py: Python, tokens: Vec<Rank>) -> Py<PyTuple> {
        let (decoded, offsets) = py.allow_threads(|| self._decode_with_offsets_native(&tokens));
        (decoded, offsets).into_py(py)
    }

    fn decode_single_token_bytes(&self, py: Python, token: Rank) -> PyResult<Py<PyBytes>> {
        if let Some(bytes) = self.decoder.get(&token) {
            return Ok(PyBytes::new(py, bytes).into());
        }
        if let Some(bytes) = self.special_tokens_decoder.get(&token) {
            return Ok(PyBytes::new(py, bytes).into());
        }
        Err(PyErr::new::<exceptions::PyKeyError, _>(token.to_string()))
    }

    // ====================
    // Miscellaneous
    // ====================

    fn token_byte_values(&self, py: Python) -> Vec<Py<PyBytes>> {
        self.sorted_token_bytes
            .iter()
            .map(|x| PyBytes::new(py, x).into())
            .collect()
    }
}

#[pymodule]
fn tiktoken(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_class::<CoreBPE>()?;
    Ok(())
}

#[cfg(test)]
mod tests {
    use fancy_regex::RegexBuilder;
    use rustc_hash::FxHashMap as HashMap;

    use crate::{byte_pair_split, Rank};

    #[test]
    fn very_simple_test() {
        let mut ranks = HashMap::default();
        ranks.insert(b"ab".to_vec(), 1);
        ranks.insert(b"cd".to_vec(), 2);

        let res = byte_pair_split(b"abcd", &ranks);
        assert_eq!(res, vec![b"ab", b"cd"]);
    }

    fn setup_ranks() -> HashMap<Vec<u8>, Rank> {
        HashMap::from_iter([(b"ab".to_vec(), 0), (b"cd".to_vec(), 1)])
    }

    #[test]
    fn test_simple_characters() {
        let ranks = setup_ranks();
        let res = byte_pair_split(b"abcd", &ranks);
        assert_eq!(res, vec![b"ab", b"cd"]);
    }

    #[test]
    fn test_repeated_characters() {
        let ranks = setup_ranks();
        let res = byte_pair_split(b"abab", &ranks);
        assert_eq!(res, vec![b"ab", b"ab"]);
    }

    #[test]
    fn test_effect_of_backtrack_limit() {
        let regex = RegexBuilder::new(r"(a|b|ab)*(?=c)")
            .backtrack_limit(10)
            .build()
            .expect("Failed to build regex")
            .clone();
        let input = "ab".repeat(100) + "c";
        assert!(regex.is_match(&input).is_err(), "Should throw");
    }
}

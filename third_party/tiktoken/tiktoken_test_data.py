from pathlib import Path


# Source: https://github.com/openai/tiktoken/pull/239
# The pattern in the original GPT-2 release is:
# r"""'s|'t|'re|'ve|'m|'ll|'d| ?[\p{L}]+| ?[\p{N}]+| ?[^\s\p{L}\p{N}]+|\s+(?!\S)|\s+"""
# This is equivalent, but executes faster:
GPT2_PAT_STR = r"""'(?:[sdmt]|ll|ve|re)| ?\p{L}++| ?\p{N}++| ?[^\s\p{L}\p{N}]++|\s++$|\s+(?!\S)|\s"""

# end of text
END_OF_TEXT = "<|endoftext|>"

# fill-in-the-middle
FIM_SEP = "<|fim-sep|>"
FIM_EOS = "<|fim-eos|>"

# retrieval end of doc
RET_EOD = "<|ret-endofdoc|>"

# preference signals
PREF_REPO_SMALL = "<|pref-repo-small|>"
PREF_REPO_LARGE = "<|pref-repo-large|>"
PREF_REPO_STARS_LOW = "<|pref-repo-stars-low|>"
PREF_REPO_STARS_MED = "<|pref-repo-stars-med|>"
PREF_REPO_STARS_HIGH = "<|pref-repo-stars-high|>"

# special tokens shared between the huggingface and the tiktoken
SHARED_SPECIAL_TOKENS = [
    FIM_SEP,
    FIM_EOS,
    RET_EOD,
    PREF_REPO_SMALL,
    PREF_REPO_LARGE,
    PREF_REPO_STARS_LOW,
    PREF_REPO_STARS_MED,
    PREF_REPO_STARS_HIGH,
]

WHITESPACE_TOKENS = [" " * n for n in reversed(range(2, 32))]
TAB_TOKENS = ["\t" * n for n in reversed(range(2, 10))]

# Test cases

SIMPLE_DEF = "    def"
SIMPLE_FUNCTION_NAME = "    def test_create(self):"
SIMPLE_PYTHON = r"""
from __future__ import unicode_literals

import six
from django.core.management import call_command

from waffle.models import Flag, Sample, Switch
from waffle.tests.base import TestCase

class WaffleFlagManagementCommandTests(TestCase):
    def test_create(self):
        ''' The command should create a new flag.'''
        name = 'test'
        percent = 20
        call_command('waffle_flag', name, everyone=True, percent=percent, superusers=True, staff=True,
                     authenticated=True, rollout=True, create=True)

        flag = Flag.objects.get(name=name)
        self.assertEqual(flag.percent, percent)
        self.assertTrue(flag.everyone)
        self.assertTrue(flag.superusers)
        self.assertTrue(flag.staff)
        self.assertTrue(flag.authenticated)
        self.assertTrue(flag.rollout)
"""


def read_bpe_ranks() -> dict[bytes, int]:
    # NB: do not add caching to this function
    rank_to_intbyte = [b for b in range(2**8) if chr(b).isprintable() and chr(b) != " "]

    data_gym_byte_to_byte = {chr(b): b for b in rank_to_intbyte}
    n = 0
    for b in range(2**8):
        if b not in rank_to_intbyte:
            rank_to_intbyte.append(b)
            data_gym_byte_to_byte[chr(2**8 + n)] = b
            n += 1
    assert len(rank_to_intbyte) == 2**8

    # vocab_bpe contains the merges along with associated ranks
    vocab_bpe_contents = Path("../openai_gpt2_vocab/file/downloaded").read_text(
        encoding="utf-8"
    )
    bpe_merges = [
        tuple(merge_str.split()) for merge_str in vocab_bpe_contents.split("\n")[1:-1]
    ]

    def decode_data_gym(value: str) -> bytes:
        return bytes(data_gym_byte_to_byte[b] for b in value)

    # add the single byte tokens
    bpe_ranks = {bytes([b]): i for i, b in enumerate(rank_to_intbyte)}
    # add the merged tokens
    n = len(bpe_ranks)
    for first, second in bpe_merges:
        bpe_ranks[decode_data_gym(first) + decode_data_gym(second)] = n
        n += 1

    return bpe_ranks

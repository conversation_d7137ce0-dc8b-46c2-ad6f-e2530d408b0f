load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_shared_library", "rust_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

rust_shared_library(
    name = "tiktoken_rs",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ),
)

rust_test(
    name = "tiktoken_rs_test",
    crate = ":tiktoken_rs",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

genrule(
    name = "tiktoken_so",
    srcs = [":tiktoken_rs"],
    outs = ["tiktoken.so"],
    cmd = "cp $< $@",
    visibility = [
        "//base/tokenizers:__subpackages__",
    ],
)

py_library(
    name = "tiktoken",
    srcs = ["tiktoken.pyi"],
    data = [":tiktoken_so"],
)
py_library(
    name = "tiktoken_test_data",
    srcs = ["tiktoken_test_data.py"],
    data = ["@openai_gpt2_vocab//file"],
    visibility = ["//visibility:public"],
)
pytest_test(
    name = "tiktoken_test",
    srcs = ["tiktoken_test.py"],
    tags = ["manual"],  # requires internet network accesss
    deps = [
        ":tiktoken",
        ":tiktoken_test_data",
        requirement("requests"),
        requirement("transformers"),
    ],
)

py_binary(
    name = "tiktoken_benchmark",
    srcs = ["tiktoken_benchmark.py"],
    tags = ["manual"],  # requires internet network accesss
    deps = [
        ":tiktoken",
        ":tiktoken_test_data",
        requirement("requests"),
        requirement("transformers"),
    ],
)

# Installs tiktoken.so in the source tree third_party/tiktoken
# For interop with stuff run outside of bazel run and bazel test.
#
# Use "bazel run -c opt" for optimization to be applied
sh_binary(
    name = "install",
    srcs = ["install.sh"],
    data = [":tiktoken_so"],
    visibility = ["//base:__subpackages__"],
)

from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
import timeit
from typing import Sequence
from third_party.tiktoken.tiktoken import CoreBP<PERSON>

from tiktoken_test_data import (
    END_OF_TEXT,
    GPT2_PAT_STR,
    WHITESPACE_TOKENS,
    TAB_TOKENS,
    SHARED_SPECIAL_TOKENS,
    read_bpe_ranks,
    SIMPLE_DEF,
    SIMPLE_FUNCTION_NAME,
    SIMPLE_PYTHON,
)


def _setup_core_bpe(
    bpe_ranks: dict[bytes, int],
    special_tokens: Sequence[str],
) -> CoreBPE:
    start_index = max(bpe_ranks.values()) + 1
    special_tokens_encoder = {w: start_index + i for i, w in enumerate(special_tokens)}

    return CoreBPE(bpe_ranks, special_tokens_encoder, GPT2_PAT_STR)


def tiktoken_timing(
    case_name: str, inp_str: str, executor: ThreadPoolExecutor, num_threads=1
):
    tiktoken_special = (
        [END_OF_TEXT] + WHITESPACE_TOKENS + TAB_TOKENS + SHARED_SPECIAL_TOKENS
    )
    core_bpe = _setup_core_bpe(read_bpe_ranks(), tiktoken_special)

    def encode_task():
        return core_bpe.encode(inp_str, allowed_special=set(tiktoken_special))

    # Warmup run to avoid first-time initialization costs
    for _ in range(5):
        encode_task()

    def run_parallel():
        # Submit num_threads tasks to the existing thread pool
        futures = [executor.submit(encode_task) for _ in range(num_threads)]
        # Wait for all tasks to complete
        results = [f.result() for f in futures]
        return results[0]  # Return first result for token counting

    # Use timeit to measure execution time
    number = 1000  # Number of times to run the code
    execution_time = timeit.timeit(
        run_parallel,
        number=number,
    )

    # Run the encode function once to get the output for potential further testing
    t_out = encode_task()
    total_chars = len(inp_str) * num_threads
    total_tokens = len(t_out) * num_threads
    print(
        f"Execution time for case {case_name} with {num_threads} threads: {execution_time:.6f} milliseconds"
    )
    print(f"Characters: {len(inp_str)}, tokens: {len(t_out)}")
    characters_per_millisecond = total_chars / execution_time
    print(f"Characters per millisecond: {characters_per_millisecond:.2f}")
    tokens_per_millisecond = total_tokens / execution_time
    print(f"Tokens per millisecond: {tokens_per_millisecond:.2f}")


samples: dict[str, str] = {
    "def": SIMPLE_DEF,
    "function_name": SIMPLE_FUNCTION_NAME,
    "python": SIMPLE_PYTHON,
    "1000_null": "\0" * 1000,
    "10000_null": "\0" * 10000,
    "1000_a": "a" * 1000,
    "10000_a": "a" * 10000,
}

if __name__ == "__main__":
    thread_counts = [1, 2, 4, 8, 16]  # Test different thread counts

    max_threads = max(thread_counts)
    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        for name, inp_str in samples.items():
            for thread_count in thread_counts:
                tiktoken_timing(name, inp_str, executor, thread_count)

r"""Tests `tiktoken` and compares its behavior with `CodeGenTokenizer`."""

import logging
from pathlib import Path
from typing import Mapping, Sequence

import pytest
from tiktoken_test_data import (
    END_OF_TEXT,
    GPT2_PAT_STR,
    SHARED_SPECIAL_TOKENS,
    TAB_TOKENS,
    WHITESPACE_TOKENS,
    SIMPLE_DEF,
    SIMPLE_FUNCTION_NAME,
    SIMPLE_PYTHON,
    read_bpe_ranks,
)
from third_party.tiktoken.tiktoken import CoreBPE

# remove the warning that pytorch is not available.
logging.disable(level=logging.WARN)
from transformers import (  # noqa: E402 module level import not at top of file
    GPT2TokenizerFast,
)

# back to default
logging.disable(level=logging.DEBUG)


def _setup_core_bpe(
    bpe_ranks: dict[bytes, int],
    special_tokens: Sequence[str],
) -> CoreBPE:
    start_index = max(bpe_ranks.values()) + 1
    special_tokens_encoder = {w: start_index + i for i, w in enumerate(special_tokens)}

    return CoreBPE(bpe_ranks, special_tokens_encoder, GPT2_PAT_STR)


@pytest.mark.parametrize(
    "inp_str",
    [
        pytest.param(SIMPLE_DEF, id="v0"),
        pytest.param(SIMPLE_FUNCTION_NAME, id="v1"),
        pytest.param(SIMPLE_PYTHON, id="v2"),
    ],
)
def test_huggingface_vs_tiktoken(inp_str: str):
    hf_tok = GPT2TokenizerFast.from_pretrained("gpt2", model_max_length=int(1e20))
    hf_tok.add_tokens(WHITESPACE_TOKENS, special_tokens=False)
    hf_tok.add_tokens(TAB_TOKENS, special_tokens=False)
    hf_tok.add_tokens(SHARED_SPECIAL_TOKENS, special_tokens=True)
    hf_tok.padding_side = "left"

    c_out = hf_tok.encode(inp_str)

    bpe_ranks = read_bpe_ranks()
    tiktoken_special = (
        [END_OF_TEXT] + WHITESPACE_TOKENS + TAB_TOKENS + SHARED_SPECIAL_TOKENS
    )
    core_bpe = _setup_core_bpe(bpe_ranks, tiktoken_special)

    t_out = core_bpe.encode(inp_str, allowed_special=set(tiktoken_special))

    assert c_out == t_out

    t_str = core_bpe.decode_bytes(c_out).decode()
    assert t_str == inp_str


@pytest.mark.parametrize(
    "input_bytes",
    [
        pytest.param(b"\x41", id="v0"),
        pytest.param(b"\xf1\xb2\xbf\xb7", id="v1"),
    ],
)
def test_missing_byte_in_ranks(input_bytes: bytes):
    inp_str = input_bytes.decode("utf-8")

    bpe_ranks = read_bpe_ranks()
    # remove the first byte in the input from the ranks
    del bpe_ranks[input_bytes[0:1]]  # type: ignore

    tiktoken_special = (
        [END_OF_TEXT] + WHITESPACE_TOKENS + TAB_TOKENS + SHARED_SPECIAL_TOKENS
    )
    core_bpe = _setup_core_bpe(bpe_ranks, tiktoken_special)

    tokens = core_bpe.encode(inp_str, allowed_special=set(tiktoken_special))
    assert core_bpe.decode_bytes(tokens) == input_bytes[1:]


@pytest.mark.parametrize(
    "input_str, expected_offsets",
    [
        pytest.param("hello world!", [0, 5, 11], id="hello_world"),
        pytest.param(
            "void quicksort(std::vector<int> & i)",
            [
                0,  # void
                4,  # _qu
                7,  # _icks
                11,  # ort
                14,  # (
                15,  # std
                18,  # ::
                20,  # vector
                26,  # <
                27,  # int
                30,  # >
                31,  # _&
                33,  # _i
                35,  # )
            ],
            id="quicksort",
        ),
        pytest.param(
            "a😃b𐐷",
            [
                0,  # a
                1,  # 😃 - first 3 bytes
                1,  # 😃 - last byte
                2,  # b
                3,  # 𐐷 - 1st byte
                3,  # 𐐷 - 2nd byte
                3,  # 𐐷 - 3rd byte
                3,  # 𐐷 - 4th byte
            ],
            id="unicode",
        ),
    ],
)
def test_decode_with_offsets(input_str: str, expected_offsets: list[int]):
    tiktoken_special = (
        [END_OF_TEXT] + WHITESPACE_TOKENS + TAB_TOKENS + SHARED_SPECIAL_TOKENS
    )
    core_bpe = _setup_core_bpe(read_bpe_ranks(), tiktoken_special)

    tokens = core_bpe.encode(input_str, allowed_special=set(tiktoken_special))
    output_str, offsets = core_bpe.decode_with_offsets(tokens)
    assert output_str == input_str
    assert offsets == expected_offsets

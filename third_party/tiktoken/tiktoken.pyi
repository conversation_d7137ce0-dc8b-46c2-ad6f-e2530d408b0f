"""The Tiktoken Python interface.

The implementation is in src/lib.rs. This interface exposes the subset of the Python
methods that seem appropriate for a public interface.
"""

class CoreBPE:
    """The tiktoken Byte-Pair Encoding (BPE) class."""

    def __init__(
        self,
        encoder: dict[bytes, int],
        special_tokens_encoder: dict[str, int],
        pattern: str,
    ):
        """Create the BPE implementation.

        Args:
            encoder: A mapping from bytes to token id for the encoder.
            special_tokens_encoder: A mapping from _strings_ to token ids for the
                encoder.
            pattern: The regex pattern used to split heuristically text before
                tokenization.
        """
        pass

    def encode(self, text: str, allowed_special: set[str]) -> list[int]:
        """Encode a text into a set of tokens.

        Args:
            text: The text to encode.
            allowed_special: A set of special tokens that are allowed to be
                encoded.

        Returns:
            Token ids for the encoded text.
        """
        pass

    def decode_bytes(self, tokens: list[int]) -> bytes:
        """Decode a list of token ids to bytes.

        Args:
            tokens: The list of token ids to decode.

        Returns:
            The decoded tokens in _bytes_.
        """
        pass

    def decode_with_offsets(self, tokens: list[int]) -> tuple[str, list[int]]:
        """Decode a list of tokens into a string with start character offsets.

        Args:
            tokens: The list of token ids to decode.

        Returns:
            A tuple with the decoded text as a UTF-8 string, and the start
            character offsets for each token id. For token ids that represent part of a
            UTF-8 character, the offset is the start of that character.
        """
        pass

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_library")

rust_library(
    name = "tracing-stackdriver",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    features = ["opentelemetry"],
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//visibility:public"],
    deps = all_crate_deps(
        normal = True,
    ),
)

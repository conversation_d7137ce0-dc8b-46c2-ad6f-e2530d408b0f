#pragma once
#include <memory>

#include "rust/cxx.h"

struct ScannRsSearchResult;
struct ScannRsStatus;
struct ScannRsRestrictAllowList;
struct ScannRsSerialization;
struct ScannRsAsset;

struct ScannRsIndex {
    ScannRsIndex();

    ScannRsStatus status() const;

    ScannRsSearchResult search(rust::Slice<const float> query, size_t final_nn,
                               size_t pre_reorder_nn, size_t num_leaves_to_search,
                               ScannRsRestrictAllowList restrict_allowlist) const;

    ScannRsSerialization serialize() const;

    struct impl;
    std::shared_ptr<impl> impl;
};

struct ScannRsFactory {
    std::unique_ptr<ScannRsIndex> build(rust::Slice<const float> database,
                                        size_t num_datapoints) const;

    ScannRsStatus status() const;

    void score_brute_force();

    void score_ah(size_t dims_per_block, float anisotropic_quantization_threshold,
                  size_t training_sample_size);

    void reorder(size_t reordering_num_neighbors);

    ScannRsFactory();

    struct impl;
    std::shared_ptr<impl> impl;
};

std::unique_ptr<ScannRsFactory> create_scann_factory(size_t ndims, size_t training_threads);

std::unique_ptr<ScannRsIndex> create_scann_index_from_assets(rust::Vec<ScannRsAsset> assets, size_t training_threads);
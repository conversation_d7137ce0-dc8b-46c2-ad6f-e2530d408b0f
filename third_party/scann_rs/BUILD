load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_cc//cc:defs.bzl", "cc_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl:rust_cxx_bridge.bzl", "rust_cxx_bridge")

cc_library(
    name = "scann_rs_include",
    hdrs = ["scann_rs.h"],
    deps = [
        "//third_party/scann/scann/scann_ops/cc:scann",
        "@cxx.rs//:core",
    ],
)

cc_library(
    name = "scann_rs_sys",
    srcs = ["scann_rs.cc"],
    deps = [
        ":bridge/include",
        ":scann_rs_include",
        "@fmt",
    ],
)

rust_library(
    name = "scann_rs",
    srcs = ["scann_rs.rs"],
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//visibility:public"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":bridge",
        ":scann_rs_sys",
    ],
)

rust_test(
    name = "scann_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":scann_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

rust_cxx_bridge(
    name = "bridge",
    src = "scann_rs.rs",
    deps = [":scann_rs_include"],
)

#include "third_party/scann_rs/scann_rs.h"

#include <fmt/format.h>

#include <cstdint>
#include <memory>

#include "absl/memory/memory.h"
#include "scann_rs.h"
#include "third_party/scann/scann/scann_ops/cc/scann.h"
#include "third_party/scann/scann/tree_x_hybrid/tree_x_params.h"
#include "third_party/scann/scann/utils/io_npy.h"
#include "third_party/scann_rs/scann_rs.rs.h"

std::unique_ptr<research_scann::DenseDataset<float>> init_dataset(
    research_scann::ConstSpan<float> dataset, research_scann::DatapointIndex n_points,
    research_scann::DimensionIndex n_dim = research_scann::kInvalidDimension) {
    if (dataset.empty() && n_dim == research_scann::kInvalidDimension) return nullptr;

    std::vector<float> dataset_vec(dataset.data(), dataset.data() + dataset.size());
    auto ds =
        std::make_unique<research_scann::DenseDataset<float>>(std::move(dataset_vec), n_points);
    if (n_dim != research_scann::kInvalidDimension) {
        ds->set_dimensionality(n_dim);
    }
    return ds;
}

struct ScannRsIndex::impl {
    friend ScannRsIndex;

    research_scann::SearchParameters get_search_parameters(
        int final_nn, int pre_reorder_nn, int leaves, ScannRsRestrictAllowList allowlist) const;

    size_t dataset_size_;
    std::unique_ptr<research_scann::SingleMachineSearcherBase<float>> scann_;
    research_scann::ScannConfig config_;

    ScannRsStatus status_;

    void initialize(std::shared_ptr<research_scann::DenseDataset<float>> dataset,
                    research_scann::SingleMachineFactoryOptions opts);

    ScannRsSearchResult search(rust::Slice<const float> query, size_t final_nn,
                               size_t pre_reorder_nn, size_t num_leaves_to_search,
                               ScannRsRestrictAllowList allowlist) const;

    ScannRsSerialization serialize() const;

    ScannRsStatus status() const;
};

ScannRsIndex::ScannRsIndex() : impl(new struct ScannRsIndex::impl) {}

void clear_bit(size_t* bitmap, size_t i) {
    size_t elem_index = i / (sizeof(size_t) * 8);
    size_t bit_index = i % (sizeof(size_t) * 8);
    bitmap[elem_index] &= ~((size_t)1 << bit_index);
}

research_scann::SearchParameters ScannRsIndex::impl::get_search_parameters(
    int final_nn, int pre_reorder_nn, int leaves, ScannRsRestrictAllowList allowlist) const {
    research_scann::SearchParameters params;
    bool has_reordering = config_.has_exact_reordering();
    int post_reorder_nn = -1;
    if (has_reordering) {
        post_reorder_nn = final_nn;
    } else {
        pre_reorder_nn = final_nn;
    }
    params.set_pre_reordering_num_neighbors(pre_reorder_nn);
    params.set_post_reordering_num_neighbors(post_reorder_nn);
    if (leaves > 0) {
        auto tree_params = std::make_shared<research_scann::TreeXOptionalParameters>();
        tree_params->set_num_partitions_to_search_override(leaves);
        params.set_searcher_specific_optional_parameters(tree_params);
    }
    if (allowlist.enabled) {
        CHECK(this->dataset_size_ > 0);
        params.EnableRestricts(this->dataset_size_, true);
        auto mut = params.mutable_restrict_whitelist();
        CHECK(mut);
        auto mut_data = mut->data();
        for (auto id : allowlist.restrict) {
            CHECK(id < this->dataset_size_);
            clear_bit(mut_data, id);
        }
    }
    return params;
}

void ScannRsIndex::impl::initialize(std::shared_ptr<research_scann::DenseDataset<float>> dataset,
                                    research_scann::SingleMachineFactoryOptions opts) {
    fmt::print("Building index with config {}", config_.DebugString());

    dataset_size_ = research_scann::kInvalidDatapointIndex;
    if (dataset) {
        dataset_size_ = dataset->size();
        LOG(INFO) << "Dataset size: " << dataset_size_;
    } else {
        CHECK(opts.hashed_dataset != nullptr);
        dataset_size_ = opts.hashed_dataset->size();
        LOG(INFO) << "Hashed dataset size: " << dataset_size_;
    }
    auto searcher_status =
        research_scann::SingleMachineFactoryScann<float>(config_, dataset, std::move(opts));
    if (!searcher_status.ok()) {
        auto status = std::move(searcher_status).status();
        status_ = ScannRsStatus{static_cast<int>(status.code()),
                                rust::String(status.message().data(), status.message().size())};
        return;
    }
    auto searcher = std::move(searcher_status).value();
    searcher->MaybeReleaseDataset();
    scann_ = std::move(searcher);
    status_ = ScannRsStatus{0, rust::String("OK")};
}

ScannRsStatus ScannRsIndex::status() const { return impl->status_; }

ScannRsSearchResult ScannRsIndex::impl::search(rust::Slice<const float> query, size_t final_nn,
                                               size_t pre_reorder_nn, size_t num_leaves_to_search,
                                               ScannRsRestrictAllowList allowlist) const {
    research_scann::DatapointPtr ptr(nullptr, query.data(), query.size(), query.size());
    research_scann::NNResultsVector res;

    research_scann::SearchParameters params =
        get_search_parameters(final_nn, pre_reorder_nn, num_leaves_to_search, std::move(allowlist));
    scann_->SetUnspecifiedParametersToDefaults(&params);
    auto status = scann_->FindNeighbors(ptr, params, &res);
    if (!status.ok()) {
        ScannRsSearchResult r;
        r.status.code = static_cast<int>(status.code());
        r.status.message = rust::String(status.message().data(), status.message().size());
        return r;
    }

    ScannRsSearchResult r;
    r.indices.reserve(res.size());
    r.distances.reserve(res.size());
    for (int i = 0; i < res.size(); i++) {
        r.indices.push_back(res[i].first);
        r.distances.push_back(res[i].second * -1.0);
    }
    r.status.code = 0;
    r.status.message = rust::String("OK");
    return r;
}

template <typename T>
absl::Status span_to_npy(research_scann::ConstSpan<T> data,
                         research_scann::ConstSpan<size_t> dim_sizes,
                         research_scann::DimensionIndex last_dim, rust::Vec<uint8_t>* vec) {
    CHECK(vec);
    std::string shape_str = "(";
    size_t dim_prod = 1;
    for (size_t dim_size : dim_sizes) {
        dim_prod *= dim_size;
        shape_str += std::to_string(dim_size) + ",";
    }
    if (last_dim != research_scann::kInvalidDimension) {
        shape_str += std::to_string(last_dim) + ",)";
    } else {
        if (dim_prod == 0 || data.size() % dim_prod != 0)
            return absl::InvalidArgumentError("Size of data isn't compatible with given shape");
        shape_str += std::to_string(data.size() / dim_prod) + ",)";
    }

    if (shape_str.size() > 65000)
        return absl::InvalidArgumentError("Shape string is too large for npy format: " + shape_str);

    std::string pt1("\x93NUMPY\x01\x00  ", 10);
    std::string dict = absl::StrFormat("{'descr':%s, 'fortran_order':False, 'shape':%s}",
                                       research_scann::numpy_type_name<T>(), shape_str);
    dict.resize(dict.size() + (63 - (pt1.size() + dict.size()) % 64), ' ');
    dict += "\n";
    pt1[8] = dict.size() % 256;
    pt1[9] = dict.size() / 256;
    const std::string header = pt1 + dict;

    std::copy(header.begin(), header.end(), std::back_inserter(*vec));
    for (auto d : data) {
        std::copy(reinterpret_cast<const uint8_t*>(&d),
                  reinterpret_cast<const uint8_t*>(&d) + sizeof(T), std::back_inserter(*vec));
    }
    return absl::OkStatus();
}

template <typename T>
absl::Status dataset_to_npy(const research_scann::DenseDataset<T>& data, rust::Vec<uint8_t>* vec) {
    return span_to_npy(data.data(), {data.size()}, data.dimensionality(), vec);
}

template <typename T>
absl::StatusOr<std::pair<std::vector<T>, std::vector<size_t>>> numpy_to_vector_and_span(
    rust::Vec<uint8_t> const& data) {
    std::istringstream iss(std::string(data.begin(), data.end()));
    std::string header;
    if (!std::getline(iss, header)) {
        return iss.bad() ? absl::InternalError("I/O error")
                         : absl::OutOfRangeError("File too short");
    }

    size_t word_size;
    std::vector<size_t> shape;
    bool fortran_order;
    cnpy::parse_npy_header(reinterpret_cast<const unsigned char*>(header.c_str()), word_size, shape,
                           fortran_order);
    if (fortran_order) return absl::FailedPreconditionError("Numpy file isn't C-style");
    if (word_size != sizeof(T)) return absl::FailedPreconditionError("word_size != sizeof(T)");

    size_t total_size = 1;
    for (size_t s : shape) total_size *= s;
    std::vector<T> buffer(total_size);
    if (!iss.read(reinterpret_cast<char*>(buffer.data()), total_size * sizeof(T))) {
        return iss.bad() ? absl::InternalError("I/O error")
                         : absl::OutOfRangeError("File too short");
    }
    return std::make_pair(std::move(buffer), std::move(shape));
}

void write_protobuf_to_vec(const google::protobuf::Message& message, rust::Vec<uint8_t>* vec) {
    CHECK(vec);
    vec->clear();
    std::vector<uint8_t> vec_tmp(message.ByteSizeLong());
    auto r = message.SerializeToArray(vec_tmp.data(), vec_tmp.size());
    CHECK(r);

    vec->reserve(vec_tmp.size());
    std::copy(vec_tmp.begin(), vec_tmp.end(), std::back_inserter(*vec));
}

ScannRsSerialization ScannRsIndex::impl::serialize() const {
    auto opt_status = scann_->ExtractSingleMachineFactoryOptions();
    if (!opt_status.ok()) {
        ScannRsSerialization r;
        r.status.code = static_cast<int>(opt_status.status().code());
        r.status.message = rust::String(opt_status.status().message().data(),
                                        opt_status.status().message().size());
        return r;
    }
    auto opts = std::move(opt_status).value();
    ScannRsSerialization r;

    // config
    ScannRsAsset config_asset;
    config_asset.asset_name = "scann_config.pb";
    write_protobuf_to_vec(config_, &config_asset.data);
    r.assets.push_back(std::move(config_asset));

    // ah codebook
    if (opts.ah_codebook != nullptr) {
        ScannRsAsset ah_codebook_asset;
        ah_codebook_asset.asset_name = "ah_codebook.pb";
        write_protobuf_to_vec(*opts.ah_codebook, &ah_codebook_asset.data);
        r.assets.push_back(std::move(ah_codebook_asset));
    }

    CHECK(opts.serialized_partitioner == nullptr) << "Partitioner not supported";
    CHECK(opts.datapoints_by_token == nullptr) << "Datapoints by token not supported";

    if (opts.hashed_dataset != nullptr) {
        ScannRsAsset hashed_dataset_asset;
        hashed_dataset_asset.asset_name = "hashed_dataset.npy";
        auto status = dataset_to_npy(*opts.hashed_dataset, &hashed_dataset_asset.data);
        if (!status.ok()) {
            LOG(WARNING) << "Failed to serialize hashed dataset: " << status;
            r.status.code = static_cast<int>(status.code());
            r.status.message = rust::String(status.message().data(), status.message().size());
            return r;
        }
        r.assets.push_back(std::move(hashed_dataset_asset));
    }

    CHECK(opts.soar_hashed_dataset == nullptr) << "SOAR not supported";
    CHECK(opts.bfloat16_dataset == nullptr) << "Bfloat16 not supported";
    CHECK(opts.pre_quantized_fixed_point == nullptr) << "Pre-quantized fixed point not supported";

    auto dataset_status = scann_->SharedFloatDatasetIfNeeded();
    if (!dataset_status.ok()) {
        r.status.code = static_cast<int>(dataset_status.status().code());
        r.status.message = rust::String(dataset_status.status().message().data(),
                                        dataset_status.status().message().size());
        return r;
    }
    auto dataset = std::move(dataset_status).value();
    if (dataset != nullptr) {
        ScannRsAsset dataset_asset;
        dataset_asset.asset_name = "dataset.npy";
        auto status = dataset_to_npy(*dataset, &dataset_asset.data);
        if (!status.ok()) {
            LOG(WARNING) << "Failed to serialize dataset: " << status;
            r.status.code = static_cast<int>(status.code());
            r.status.message = rust::String(status.message().data(), status.message().size());
            return r;
        }
        r.assets.push_back(std::move(dataset_asset));
    }

    r.status.code = 0;
    r.status.message = rust::String("OK");
    return r;
}

ScannRsSearchResult ScannRsIndex::search(rust::Slice<const float> query, size_t final_nn,
                                         size_t pre_reorder_nn, size_t num_leaves_to_search,
                                         ScannRsRestrictAllowList allowlist) const {
    return impl->search(query, final_nn, pre_reorder_nn, num_leaves_to_search,
                        std::move(allowlist));
}

ScannRsSerialization ScannRsIndex::serialize() const { return impl->serialize(); }

struct ScannRsFactory::impl {
    ScannRsStatus status;
    std::shared_ptr<research_scann::ThreadPool> parallel_training_pool;
    size_t ndims;
    research_scann::ScannConfig config;
};

ScannRsFactory::ScannRsFactory() : impl(new struct ScannRsFactory::impl) {}

std::unique_ptr<ScannRsFactory> create_scann_factory(size_t ndims, size_t num_training_threads) {
    auto parallel_training_pool =
        research_scann::StartThreadPool("ScannTrainingPool", num_training_threads);

    auto f = std::make_unique<ScannRsFactory>();
    f->impl->ndims = ndims;
    f->impl->status = ScannRsStatus{0, rust::String("OK")};
    f->impl->parallel_training_pool = std::move(parallel_training_pool);

    return f;
}

std::unique_ptr<ScannRsIndex> create_scann_index_from_assets(rust::Vec<ScannRsAsset> assets,
                                                             size_t num_training_threads) {
    auto parallel_training_pool =
        research_scann::StartThreadPool("ScannTrainingPool", num_training_threads);
    auto f = std::make_unique<ScannRsIndex>();

    bool has_config = false;
    research_scann::SingleMachineFactoryOptions opts;
    std::unique_ptr<research_scann::DenseDataset<float>> dataset;
    for (auto& asset : assets) {
        LOG(INFO) << "Loading asset " << asset.asset_name;
        if (asset.asset_name == "scann_config.pb") {
            auto r = f->impl->config_.ParseFromArray(asset.data.data(), asset.data.size());
            if (!r) {
                f->impl->status_ =
                    ScannRsStatus{static_cast<int>(absl::StatusCode::kInvalidArgument),
                                  rust::String("Failed to parse scann_config.pb")};
                return f;
            }
            has_config = true;
        } else if (asset.asset_name == "ah_codebook.pb") {
            opts.ah_codebook = std::make_unique<research_scann::CentersForAllSubspaces>();
            auto r = opts.ah_codebook->ParseFromArray(asset.data.data(), asset.data.size());
            if (!r) {
                f->impl->status_ =
                    ScannRsStatus{static_cast<int>(absl::StatusCode::kInvalidArgument),
                                  rust::String("Failed to parse ah_codebook.pb")};
                return f;
            }
        } else if (asset.asset_name == "hashed_dataset.npy") {
            auto status = numpy_to_vector_and_span<uint8_t>(asset.data);
            if (!status.ok()) {
                LOG(WARNING) << "Failed to parse hashed_dataset.npy: " << status.status();
                f->impl->status_ = ScannRsStatus{static_cast<int>(status.status().code()),
                                                 rust::String(status.status().message().data(),
                                                              status.status().message().size())};
                return f;
            }
            auto vector_and_shape = std::move(status).value();
            opts.hashed_dataset = std::make_shared<research_scann::DenseDataset<uint8_t>>(
                std::move(vector_and_shape.first), vector_and_shape.second[0]);
        } else if (asset.asset_name == "dataset.npy") {
            auto status = numpy_to_vector_and_span<float>(asset.data);
            if (!status.ok()) {
                LOG(WARNING) << "Failed to parse dataset.npy: " << status.status();
                f->impl->status_ = ScannRsStatus{static_cast<int>(status.status().code()),
                                                 rust::String(status.status().message().data(),
                                                              status.status().message().size())};
                return f;
            }
            auto vector_and_shape = std::move(status).value();
            dataset = std::make_unique<research_scann::DenseDataset<float>>(
                std::move(vector_and_shape.first), vector_and_shape.second[0]);
        } else {
            LOG(ERROR) << "Unknown asset name: " << asset.asset_name;
            f->impl->status_ = ScannRsStatus{static_cast<int>(absl::StatusCode::kInvalidArgument),
                                             rust::String("Unknown asset name")};
            return f;
        }
    }
    CHECK(has_config) << "scann_config.pb is required";

    f->impl->initialize(std::move(dataset), opts);
    return f;
}

ScannRsStatus ScannRsFactory::status() const { return impl->status; }

void ScannRsFactory::reorder(size_t reordering_num_neighbors) {
    // based on third_party/scann/scann/scann_ops/py/scann_builder.py
    auto reorder = impl->config.mutable_exact_reordering();
    reorder->set_approx_num_neighbors(reordering_num_neighbors);
}

void ScannRsFactory::score_brute_force() {
    // based on third_party/scann/scann/scann_ops/py/scann_builder.py
    research_scann::ScannConfig& config = impl->config;
    config.clear_hash();
    config.set_num_neighbors(10);
    config.mutable_distance_measure()->set_distance_measure("DotProductDistance");
    config.mutable_brute_force();
}

void ScannRsFactory::score_ah(size_t dims_per_block, float anisotropic_quantization_threshold,
                              size_t training_sample_size) {
    // based on third_party/scann/scann/scann_ops/py/scann_builder.py
    research_scann::ScannConfig& config = impl->config;
    config.clear_brute_force();
    config.set_num_neighbors(10);
    config.mutable_distance_measure()->set_distance_measure("DotProductDistance");
    auto h = config.mutable_hash();
    auto ah = h->mutable_asymmetric_hash();
    ah->set_lookup_type(research_scann::AsymmetricHasherConfig::INT8_LUT16);
    ah->set_num_clusters_per_block(16);
    ah->set_noise_shaping_threshold(anisotropic_quantization_threshold);
    ah->set_expected_sample_size(training_sample_size);
    ah->set_max_clustering_iterations(10);
    ah->mutable_fixed_point_lut_conversion_options()->set_float_to_int_conversion_method(
        research_scann::AsymmetricHasherConfig_FixedPointLUTConversionOptions::ROUND);
    ah->mutable_quantization_distance()->set_distance_measure("SquaredL2Distance");
    auto proj = ah->mutable_projection();
    proj->set_projection_type(research_scann::ProjectionConfig::CHUNK);
    proj->set_num_dims_per_block(dims_per_block);
    proj->set_input_dim(impl->ndims);
}

std::unique_ptr<ScannRsIndex> ScannRsFactory::build(rust::Slice<const float> database,
                                                    size_t num_datapoints) const {
    auto db = std::make_unique<ScannRsIndex>();
    research_scann::SingleMachineFactoryOptions opts;
    opts.parallelization_pool = impl->parallel_training_pool;
    research_scann::DimensionIndex n_dim = research_scann::kInvalidDimension;

    db->impl->config_ = impl->config;

    auto dataset = init_dataset(database, num_datapoints, n_dim);
    auto dim_status = opts.ComputeConsistentDimensionality(db->impl->config_.hash(), dataset.get());
    if (!dim_status.ok()) {
        auto status = std::move(dim_status).status();
        db->impl->status_ =
            ScannRsStatus{static_cast<int>(status.code()),
                          rust::String(status.message().data(), status.message().size())};
        return db;
    }

    db->impl->initialize(std::move(dataset), opts);

    return db;
}

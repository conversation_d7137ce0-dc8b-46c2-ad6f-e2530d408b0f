#[cxx::bridge]
mod ffi {

    // ffi version of a tonic::Status
    struct ScannRsStatus {
        code: i32,
        message: String,
    }

    struct ScannRsRestrictAllowList {
        // if true, restrict the search to the allow list
        // if enable, by default every index is in the allow list
        pub enabled: bool,
        // list of indices to disallow
        pub restrict: Vec<u32>,
    }

    struct ScannRsSearchResult {
        indices: Vec<usize>,
        distances: Vec<f32>,
        // status of the search encodes as integer
        status: ScannRsStatus,
    }

    struct ScannRsAsset {
        asset_name: String,
        data: Vec<u8>,
    }

    struct ScannRsSerialization {
        assets: Vec<ScannRsAsset>,
        // status of the serialization
        status: ScannRsStatus,
    }

    // C++ types and signatures exposed to Rust.
    unsafe extern "C++" {
        include!("third_party/scann_rs/scann_rs.h");

        type ScannRsIndex;
        type ScannRsFactory;

        // build an index
        //
        // the index will copy the dataset
        fn build(
            self: &ScannRsFactory,
            db: &[f32],
            num_datapoints: usize,
        ) -> UniquePtr<ScannRsIndex>;

        fn status(self: &ScannRsFactory) -> ScannRsStatus;

        fn score_brute_force(self: Pin<&mut ScannRsFactory>);

        fn score_ah(
            self: Pin<&mut ScannRsFactory>,
            dims_per_block: usize,
            anisotropic_quantization_threshold: f32,
            training_sample_size: usize,
        );

        fn reorder(self: Pin<&mut ScannRsFactory>, reordering_num_neighbors: usize);

        fn create_scann_factory(
            ndims: usize,
            num_training_threads: usize,
        ) -> UniquePtr<ScannRsFactory>;

        fn create_scann_index_from_assets(
            assets: Vec<ScannRsAsset>,
            num_training_threads: usize,
        ) -> UniquePtr<ScannRsIndex>;

        fn status(self: &ScannRsIndex) -> ScannRsStatus;

        fn search(
            self: &ScannRsIndex,
            query: &[f32],
            final_nn: usize,
            pre_reorder_nn: usize,
            leaves_to_search: usize,
            allow_list: ScannRsRestrictAllowList,
        ) -> ScannRsSearchResult;

        fn serialize(self: &ScannRsIndex) -> ScannRsSerialization;

    }
}

unsafe impl Send for ffi::ScannRsIndex {}
unsafe impl Sync for ffi::ScannRsIndex {}
unsafe impl Send for ffi::ScannRsFactory {}
unsafe impl Sync for ffi::ScannRsFactory {}

fn scann_rs_status_to_tonic_status(status: ffi::ScannRsStatus) -> tonic::Status {
    let code: tonic::Code = status.code.into();
    tonic::Status::new(code, status.message)
}

pub struct ScannRsAsset {
    pub asset_name: String,
    pub data: Vec<u8>,
}

#[derive(Debug)]
pub struct ScannRsSearchResult {
    pub indices: Vec<usize>,
    pub distances: Vec<f32>,
}

pub struct ScannRsIndex {
    inner: cxx::UniquePtr<ffi::ScannRsIndex>,
}

#[derive(Debug, Clone)]
pub struct RestrictAllowList {
    pub restrict: Vec<u32>,
}

impl RestrictAllowList {
    fn into_ffi(self) -> ffi::ScannRsRestrictAllowList {
        ffi::ScannRsRestrictAllowList {
            enabled: true,
            restrict: self.restrict,
        }
    }
}

#[derive(Debug, Clone)]
pub struct SearchParameters {
    pub final_nn: usize,
    pub pre_reorder_nn: usize,
    pub leaves_to_search: usize,
    pub allow_list: Option<RestrictAllowList>,
}

impl SearchParameters {
    pub fn new(final_nn: usize, pre_reorder_nn: usize, leaves_to_search: usize) -> Self {
        Self {
            final_nn,
            pre_reorder_nn,
            leaves_to_search,
            allow_list: None,
        }
    }
}

impl ScannRsIndex {
    pub fn new_from_assets(
        assets: Vec<ScannRsAsset>,
        training_threads: usize,
    ) -> tonic::Result<Self> {
        let assets = assets
            .into_iter()
            .map(|a| ffi::ScannRsAsset {
                asset_name: a.asset_name,
                data: a.data,
            })
            .collect();
        let inner = ffi::create_scann_index_from_assets(assets, training_threads);
        let status = inner.status();
        if status.code != 0 {
            return Err(scann_rs_status_to_tonic_status(status));
        }
        Ok(Self { inner })
    }

    pub fn search(
        &self,
        query: &[f32],
        search_parameters: SearchParameters,
    ) -> tonic::Result<ScannRsSearchResult> {
        let final_nn = search_parameters.final_nn;
        let pre_reorder_nn = search_parameters.pre_reorder_nn;
        let leaves_to_search = search_parameters.leaves_to_search;
        let allow_list = search_parameters.allow_list;
        let scann_allow_list =
            allow_list
                .map(|a| a.into_ffi())
                .unwrap_or_else(|| ffi::ScannRsRestrictAllowList {
                    enabled: false,
                    restrict: vec![],
                });
        let r = self.inner.search(
            query,
            final_nn,
            pre_reorder_nn,
            leaves_to_search,
            scann_allow_list,
        );
        if r.status.code != 0 {
            return Err(scann_rs_status_to_tonic_status(r.status));
        }
        Ok(ScannRsSearchResult {
            indices: r.indices,
            distances: r.distances,
        })
    }

    pub fn serialize(&self) -> tonic::Result<Vec<ScannRsAsset>> {
        let r = self.inner.serialize();
        if r.status.code != 0 {
            return Err(scann_rs_status_to_tonic_status(r.status));
        }
        let assets = r
            .assets
            .iter()
            .map(|a| ScannRsAsset {
                asset_name: a.asset_name.clone(),
                data: a.data.clone(),
            })
            .collect();
        Ok(assets)
    }
}

pub struct ScannFactory {
    _inner: cxx::UniquePtr<ffi::ScannRsFactory>,
}

impl ScannFactory {
    pub fn new(ndims: usize, training_threads: usize) -> tonic::Result<Self> {
        let _inner = ffi::create_scann_factory(ndims, training_threads);
        let status = _inner.status();
        if status.code != 0 {
            return Err(scann_rs_status_to_tonic_status(status));
        }
        Ok(Self { _inner })
    }

    pub fn reorder(&mut self, reordering_num_neighbors: usize) {
        let inner = self._inner.pin_mut();
        inner.reorder(reordering_num_neighbors);
    }

    // configures for brute force search
    //
    // either this or score_ah must be called before build
    pub fn score_brute_force(&mut self) {
        // pin
        let inner = self._inner.pin_mut();
        inner.score_brute_force();
    }

    // configures for anisotropic hashing search
    //
    // either this or score_brute_force must be called before build
    pub fn score_ah(
        &mut self,
        dims_per_block: usize,
        anisotropic_quantization_threshold: f32,
        training_sample_size: usize,
    ) {
        let inner = self._inner.pin_mut();
        inner.score_ah(
            dims_per_block,
            anisotropic_quantization_threshold,
            training_sample_size,
        );
    }

    pub fn build(&self, db: &[f32], num_datapoints: usize) -> tonic::Result<ScannRsIndex> {
        let inner = self._inner.build(db, num_datapoints);
        let status = inner.status();
        if status.code != 0 {
            return Err(scann_rs_status_to_tonic_status(status));
        }
        Ok(ScannRsIndex { inner })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_search_brute_force() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        factory.score_brute_force();
        let db = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let num_datapoints = 4;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let query = vec![1.0, 2.0];
        let search = SearchParameters::new(2, 2, 1);
        let result = scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![3, 2]);
        assert_eq!(result.distances, vec![23.0, 17.0]);
    }

    #[test]
    fn test_search_brute_force_restrict() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        factory.score_brute_force();
        let db = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0];
        let num_datapoints = 4;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let query = vec![1.0, 2.0];
        let mut search = SearchParameters::new(2, 2, 1);
        search.allow_list = Some(RestrictAllowList { restrict: vec![2] });
        let result = scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![3, 1]);
        assert_eq!(result.distances, vec![23.0, 11.0]);
    }

    #[test]
    fn test_search_ah() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        factory.score_ah(2, 0.8, 10_000);
        // it has to be at least 16 to even trigger ah
        let db = vec![
            1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0,
            2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0,
        ];
        let num_datapoints = 16;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let query = vec![1.0, 2.0];
        let search = SearchParameters::new(2, 2, 1);
        let result = scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![3, 7]);
        assert_eq!(result.distances, vec![23.0, 23.0]);
    }

    #[test]
    fn test_search_ah_restrict() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        factory.score_ah(2, 0.8, 10_000);
        // it has to be at least 16 to even trigger ah
        let db = vec![
            1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0,
            2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0,
        ];
        let num_datapoints = 16;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let query = vec![1.0, 2.0];
        let mut search = SearchParameters::new(2, 2, 1);
        search.allow_list = Some(RestrictAllowList {
            restrict: vec![3, 11],
        });
        let result = scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![7, 15]);
        assert_eq!(result.distances, vec![23.0, 23.0]);
    }

    #[test]
    fn test_search_ah_reordering() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        factory.reorder(2);
        factory.score_ah(2, 0.8, 10_000);
        // it has to be at least 16 to even trigger ah
        let db = vec![
            1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0,
            2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0,
        ];
        let num_datapoints = 16;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let query = vec![1.0, 2.0];
        let search = SearchParameters::new(2, 2, 1);
        let result = scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![3, 7]);
        assert_eq!(result.distances, vec![23.0, 23.0]);
    }

    #[test]
    fn test_search_ah_reordering_restrict() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        factory.reorder(2);
        factory.score_ah(2, 0.8, 10_000);
        // it has to be at least 16 to even trigger ah
        let db = vec![
            1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0,
            2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0,
        ];
        let num_datapoints = 16;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let query = vec![1.0, 2.0];
        let mut search = SearchParameters::new(2, 2, 1);
        search.allow_list = Some(RestrictAllowList {
            restrict: vec![3, 11],
        });
        let result = scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![7, 15]);
        assert_eq!(result.distances, vec![23.0, 23.0]);
    }

    #[test]
    fn test_search_ah_serialize() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        factory.reorder(2);
        factory.score_ah(2, 0.8, 10_000);
        // it has to be at least 16 to even trigger ah
        let db = vec![
            1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0,
            2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0,
        ];
        let num_datapoints = 16;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let assets = scann.serialize();
        assert!(assets.is_ok());
        let assets = assets.unwrap();
        assets.iter().for_each(|a| {
            println!("{}: {}", a.asset_name, a.data.len());
        });

        let restored_index = ScannRsIndex::new_from_assets(assets, 4);
        if let Err(e) = &restored_index {
            println!("Error: {}", e);
        }
        assert!(restored_index.is_ok());
        let restored_scann = restored_index.unwrap();
        let query = vec![1.0, 2.0];
        let search = SearchParameters::new(2, 2, 1);
        let result = restored_scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![3, 7]);
        assert_eq!(result.distances, vec![23.0, 23.0]);
    }

    #[test]
    fn test_search_ah_serialize_no_dataset() {
        let factory = ScannFactory::new(2, 1);
        if let Err(e) = &factory {
            println!("Error: {}", e);
        }
        let mut factory = factory.unwrap();
        // no reordering
        factory.score_ah(2, 0.8, 10_000);
        // it has to be at least 16 to even trigger ah
        let db = vec![
            1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0,
            2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0,
        ];
        let num_datapoints = 16;
        let scann = factory.build(&db, num_datapoints);
        if let Err(e) = &scann {
            println!("Error: {}", e);
        }

        assert!(scann.is_ok());
        let scann = scann.unwrap();
        let assets = scann.serialize();
        assert!(assets.is_ok());
        let assets = assets.unwrap();
        assets.iter().for_each(|a| {
            println!("{}: {}", a.asset_name, a.data.len());
        });
        assert_eq!(assets.len(), 3); // no dataset.npy

        let restored_index = ScannRsIndex::new_from_assets(assets, 4);
        if let Err(e) = &restored_index {
            println!("Error: {}", e);
        }
        assert!(restored_index.is_ok());
        let restored_scann = restored_index.unwrap();
        let query = vec![1.0, 2.0];
        let search = SearchParameters::new(2, 2, 1);
        let result = restored_scann.search(&query, search);
        if let Err(e) = &result {
            println!("Error: {}", e);
        }
        println!("{:?}", result);
        assert!(result.is_ok());
        let result = result.unwrap();
        assert_eq!(result.indices, vec![3, 7]);
        assert_eq!(result.distances, vec![23.0, 23.0]);
    }
}

# Using llama.cpp

Main notion page: <https://www.notion.so/Using-llama-cpp-0a3623c708694b498c352f8f2ef88b9b?pvs=4>.

## Version control

llama.cpp in our monorepo is managed as a [git subtree](https://www.atlassian.com/git/tutorials/git-subtree). This makes it easy to pull in updates from upstream, which is frequently needed as new model support gets added.

Basic commands for handling upstream changes:

```bash
# Update to latest upstream
cd $AUGMENT_ROOT
git subtree pull --prefix third_party/llama.cpp https://github.com/ggerganov/llama.cpp.git master --squash

# Add the subtree from scratch (this should rarely be needed)
cd $AUGMENT_ROOT
git subtree add --prefix third_party/llama.cpp https://github.com/ggerganov/llama.cpp.git master --squash
```

## Building

```bash
pip3 install -r requirements.txt
make LLAMA_CUBLAS=1
```

## Basic serving and inference

```bash
# 7b model with 4-bit quantization
MODEL_FILE=/mnt/efs/augment/checkpoints/llama/CodeLlama-7b/ggml-model-q4_0.gguf

# generate 128 tokens
#
# --n-gpu-layers X: move up to X layers to the GPU.
# without this, inference is done on CPU.
./main --model $MODEL_FILE --n-predict 128 --n-gpu-layers 1000

# serve the model
# see examples/server/README.md for more info
./server --model $MODEL_FILE --n-gpu-layers 1000 --ctx-size 4096 [--port PORT]

# do a simple completion request
curl --request POST \
    --url http://localhost:8080/completion \
    --header "Content-Type: application/json" \
    --data '{"prompt": "Building a website can be done in 10 simple steps:", "n_predict": 128}'
```

{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1694529238, "narHash": "sha256-zsNZZGTGnMOf9YpHKJqMSsa0dXbfmxeoJ7xHlrt+xmY=", "owner": "numtide", "repo": "flake-utils", "rev": "ff7b65b44d01cf9ba6a71320833626af21126384", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1698318101, "narHash": "sha256-gUihHt3yPD7bVqg+k/UVHgngyaJ3DMEBchbymBMvK1E=", "owner": "NixOS", "repo": "nixpkgs", "rev": "63678e9f3d3afecfeafa0acead6239cdb447574c", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "nixpkgs": "nixpkgs"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}
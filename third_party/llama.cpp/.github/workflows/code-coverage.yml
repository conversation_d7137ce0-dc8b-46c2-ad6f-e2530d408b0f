name: Code Coverage
on: [push, pull_request]

env:
  GGML_NLOOP: 3
  GGML_N_THREADS: 1

jobs:
  run:
    runs-on: ubuntu-20.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install build-essential gcc-8 lcov

      - name: Build
        run: CC=gcc-8 make -j LLAMA_CODE_COVERAGE=1 tests

      - name: Run tests
        run: CC=gcc-8 make test

      - name: Generate coverage report
        run: |
          make coverage
          make lcov-report

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        env:
           CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        with:
          files: lcov-report/coverage.info

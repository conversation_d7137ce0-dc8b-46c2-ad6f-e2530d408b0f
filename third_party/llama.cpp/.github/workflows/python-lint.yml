name: flake8 Lint

on: [push, pull_request]

jobs:
  flake8-lint:
    runs-on: ubuntu-latest
    name: Lint
    steps:
      - name: Check out source repository
        uses: actions/checkout@v3
      - name: Set up Python environment
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
      - name: flake8 <PERSON>t
        uses: py-actions/flake8@v2
        with:
            ignore: "E203,E211,E221,E225,E231,E241,E251,E261,E266,E501,E701,E704"
            exclude: "examples/*,examples/*/**,*/**/__init__.py"

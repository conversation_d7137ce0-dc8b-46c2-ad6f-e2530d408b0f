[tool.poetry]
name = "gguf"
version = "0.4.5"
description = "Write ML models in GGUF for GGML"
authors = ["GGML <<EMAIL>>"]
packages = [
    {include = "gguf"},
    {include = "gguf/py.typed"},
]
readme = "README.md"
homepage = "https://ggml.ai"
repository = "https://github.com/ggerganov/llama.cpp"
keywords = ["ggml", "gguf", "llama.cpp"]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[tool.poetry.dependencies]
python = ">=3.8"
numpy = ">=1.17"

[tool.poetry.dev-dependencies]
pytest = "^5.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

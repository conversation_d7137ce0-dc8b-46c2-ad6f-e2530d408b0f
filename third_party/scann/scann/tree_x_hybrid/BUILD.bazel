# Description:
#   Build file for tree-X hybrids.
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "tree_x_params",
    srcs = ["tree_x_params.cc"],
    hdrs = ["tree_x_params.h"],
    deps = [
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "leaf_searcher_optional_parameter_creator",
    hdrs = ["leaf_searcher_optional_parameter_creator.h"],
    deps = [
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils:common",
    ],
)

scann_cc_library(
    name = "mutator",
    hdrs = ["mutator.h"],
    deps = [
        "//third_party/scann/scann/base:health_stats_collector",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:searcher",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/partitioning:kmeans_tree_partitioner",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:parallel_for",
        "//third_party/scann/scann/utils:single_machine_autopilot",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:zip_sort",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/container:flat_hash_map",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/strings:str_format",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "tree_x_hybrid_smmd",
    srcs = ["tree_x_hybrid_smmd.cc"],
    hdrs = ["tree_x_hybrid_smmd.h"],
    deps = [
        ":leaf_searcher_optional_parameter_creator",
        ":mutator",
        ":tree_x_params",
        "//third_party/scann/scann/base:health_stats_collector",
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/brute_force:scalar_quantized_brute_force",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/partitioning:kmeans_tree_partitioner",
        "//third_party/scann/scann/partitioning:partitioner_base",
        "//third_party/scann/scann/partitioning:partitioner_cc_proto",
        "//third_party/scann/scann/tree_x_hybrid/internal:batching",
        "//third_party/scann/scann/tree_x_hybrid/internal:utils",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:hash_leaf_helpers",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/fixed_point:pre_quantized_fixed_point",
        "@abseil-cpp//absl/algorithm:container",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/container:node_hash_map",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/time",
        "@abseil-cpp//absl/types:span",
        "@googletest//:gtest",
    ],
)

scann_cc_library(
    name = "tree_ah_hybrid_residual",
    srcs = ["tree_ah_hybrid_residual.cc"],
    hdrs = ["tree_ah_hybrid_residual.h"],
    deps = [
        ":mutator",
        ":tree_x_params",
        "//third_party/scann/scann/base:health_stats_collector",
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:indexing",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:querying",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:searcher",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:serialization",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_options",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_serialize",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/partitioning:kmeans_tree_like_partitioner",
        "//third_party/scann/scann/partitioning:kmeans_tree_partitioner",
        "//third_party/scann/scann/partitioning:projecting_decorator",
        "//third_party/scann/scann/projection:projection_base",
        "//third_party/scann/scann/projection:projection_factory",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/tree_x_hybrid/internal:batching",
        "//third_party/scann/scann/tree_x_hybrid/internal:utils",
        "//third_party/scann/scann/trees/kmeans_tree",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/time",
    ],
)

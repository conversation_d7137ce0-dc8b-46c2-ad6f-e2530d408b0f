# Utilities shared by multiple flavors of tree-X.
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "utils",
    srcs = ["utils.cc"],
    hdrs = ["utils.h"],
    deps = [
        "//third_party/scann/scann/base:restrict_allowlist",
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:searcher",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/restricts:restrict_allowlist",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:zip_sort",
        "//third_party/scann/scann/utils/intrinsics:flags",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "batching",
    hdrs = ["batching.h"],
    deps = [
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
    ],
)

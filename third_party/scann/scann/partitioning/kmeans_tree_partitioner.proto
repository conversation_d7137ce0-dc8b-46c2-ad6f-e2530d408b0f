// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package research_scann;

import "third_party/scann/scann/trees/kmeans_tree/kmeans_tree.proto";

message SerializedKMeansTreePartitioner {
  optional SerializedKMeansTree kmeans_tree = 1;

  optional SerializedKMeansTreePartitioner next_bottom_up_level = 6;

  reserved 2, 3, 4, 5;
}

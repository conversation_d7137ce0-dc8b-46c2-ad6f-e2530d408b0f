# Description:
#   This package contains ScaNN partitioners.

load("//third_party/scann/scann/oss_wrappers:build_defs.bzl", "scann_py_proto_library")
load("//third_party/scann:scann.bzl", "scann_cc_library")
load("//tools/bzl:cc.bzl", "cc_proto_library")


package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# Binaries
# ==============================================================================

# Libraries
# ==============================================================================

scann_cc_library(
    name = "anisotropic",
    srcs = ["anisotropic.cc"],
    hdrs = ["anisotropic.h"],
    deps = [
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils/linear_algebra:eigen_utils",
        "//third_party/scann/scann/utils/linear_algebra:types",
        "@eigen",
    ],
)

scann_cc_library(
    name = "partitioner_base",
    srcs = ["partitioner_base.cc"],
    hdrs = ["partitioner_base.h"],
    deps = [
        ":partitioner_cc_proto",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "//third_party/scann/scann/utils:zip_sort",
        "@abseil-cpp//absl/base",
    ],
)

scann_cc_library(
    name = "orthogonality_amplification_utils",
    hdrs = ["orthogonality_amplification_utils.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "kmeans_tree_like_partitioner",
    hdrs = ["kmeans_tree_like_partitioner.h"],
    deps = [
        ":partitioner_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/trees/kmeans_tree",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/status",
    ],
)

scann_cc_library(
    name = "kmeans_tree_partitioner",
    srcs = [
        "kmeans_tree_partitioner.cc",
        "kmeans_tree_partitioner_helper.cc",
    ],
    hdrs = ["kmeans_tree_partitioner.h"],
    deps = [
        ":kmeans_tree_like_partitioner",
        ":kmeans_tree_partitioner_cc_proto",
        ":orthogonality_amplification_utils",
        ":partitioner_base",
        ":partitioner_cc_proto",
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/brute_force",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures/many_to_many",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:indexing",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:querying",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:searcher",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_options",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/trees/kmeans_tree",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:parallel_for",
        "//third_party/scann/scann/utils:reordering_helper",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:zip_sort",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/synchronization",
    ],
)

scann_cc_library(
    name = "tree_brute_force_second_level_wrapper",
    srcs = ["tree_brute_force_second_level_wrapper.cc"],
    hdrs = ["tree_brute_force_second_level_wrapper.h"],
    deps = [
        ":kmeans_tree_like_partitioner",
        ":kmeans_tree_partitioner_cc_proto",
        "//third_party/scann/scann/brute_force",
        "//third_party/scann/scann/brute_force:bfloat16_brute_force",
        "//third_party/scann/scann/brute_force:scalar_quantized_brute_force",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/tree_x_hybrid:tree_x_hybrid_smmd",
        "//third_party/scann/scann/trees/kmeans_tree",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/log:check",
    ],
)

scann_cc_library(
    name = "kmeans_tree_partitioner_utils",
    hdrs = ["kmeans_tree_partitioner_utils.h"],
    deps = [
        ":kmeans_tree_partitioner",
        ":partitioner_base",
        ":tree_brute_force_second_level_wrapper",
        "//third_party/scann/scann/distance_measures",
    ],
)

scann_cc_library(
    name = "projecting_decorator",
    srcs = ["projecting_decorator.cc"],
    hdrs = ["projecting_decorator.h"],
    deps = [
        ":kmeans_tree_like_partitioner",
        ":partitioner_base",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/projection:projection_base",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/memory",
    ],
)

scann_cc_library(
    name = "partitioner_factory_base",
    srcs = ["partitioner_factory_base.cc"],
    hdrs = ["partitioner_factory_base.h"],
    deps = [
        ":kmeans_tree_partitioner_utils",
        ":partitioner_base",
        ":projecting_decorator",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/projection:projection_base",
        "//third_party/scann/scann/projection:projection_factory",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/random",
        "@abseil-cpp//absl/time",
    ],
)

scann_cc_library(
    name = "partitioner_factory",
    srcs = ["partitioner_factory.cc"],
    hdrs = ["partitioner_factory.h"],
    deps = [
        ":kmeans_tree_partitioner_cc_proto",
        ":kmeans_tree_partitioner_utils",
        ":partitioner_base",
        ":partitioner_cc_proto",
        ":partitioner_factory_base",
        ":projecting_decorator",
        ":tree_brute_force_second_level_wrapper",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_random",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/projection:projection_base",
        "//third_party/scann/scann/projection:projection_factory",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/trees/kmeans_tree",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "//third_party/scann/scann/utils:weak_ptr_cache",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/numeric:int128",
        "@abseil-cpp//absl/random",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/time",
    ],
)

proto_library(
    name = "partitioner_proto",
    srcs = ["partitioner.proto"],
    deps = [
        ":kmeans_tree_partitioner_proto",
        ":linear_projection_tree_proto",
        "//third_party/scann/scann/proto:projection_proto",
    ],
)

cc_proto_library(
    name = "partitioner_cc_proto",
    deps = [":partitioner_proto"],
    extra_deps = [
        ":kmeans_tree_partitioner_cc_proto",
        ":linear_projection_tree_cc_proto",
        "//third_party/scann/scann/proto:projection_cc_proto",
    ],
)

scann_py_proto_library(
    name = "partitioner_py_pb2",
    srcs = ["partitioner.proto"],
    proto_deps = [":partitioner_proto"],
    py_proto_deps = [
        ":kmeans_tree_partitioner_py_pb2",
        ":linear_projection_tree_py_pb2",
    ],
)

proto_library(
    name = "kmeans_tree_partitioner_proto",
    srcs = ["kmeans_tree_partitioner.proto"],
    deps = [
        "//third_party/scann/scann/proto:partitioning_proto",
        "//third_party/scann/scann/trees/kmeans_tree:kmeans_tree_proto",
    ],
)

cc_proto_library(
    name = "kmeans_tree_partitioner_cc_proto",
    deps = [":kmeans_tree_partitioner_proto"],
    extra_deps = ["//third_party/scann/scann/trees/kmeans_tree:kmeans_tree_cc_proto"],
)

scann_py_proto_library(
    name = "kmeans_tree_partitioner_py_pb2",
    srcs = ["kmeans_tree_partitioner.proto"],
    proto_deps = [":kmeans_tree_partitioner_proto"],
    py_proto_deps = [
        "//third_party/scann/scann/proto:partitioning_py_pb2",
        "//third_party/scann/scann/trees/kmeans_tree:kmeans_tree_py_pb2",
    ],
)

proto_library(
    name = "linear_projection_tree_proto",
    srcs = ["linear_projection_tree.proto"],
    deps = [
    ],
)

scann_py_proto_library(
    name = "linear_projection_tree_py_pb2",
    srcs = ["linear_projection_tree.proto"],
    proto_deps = [":linear_projection_tree_proto"],
)

cc_proto_library(
    name = "linear_projection_tree_cc_proto",
    deps = [":linear_projection_tree_proto"],
)

# Tests
# =============================================================================

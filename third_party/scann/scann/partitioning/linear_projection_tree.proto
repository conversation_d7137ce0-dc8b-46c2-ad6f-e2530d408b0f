// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package research_scann;

message SerializedLinearProjectionTree {
  message Node {
    message NonLeafFields {
      repeated float projection_vector = 1;

      optional float threshold = 2;

      optional Node left = 3;
      optional Node right = 4;
    }

    message LeafFields {
      optional int32 leaf_number = 5;
    }

    oneof leaf_or_non_leaf {
      NonLeafFields non_leaf = 1;
      LeafFields leaf = 2;
    }
  }

  optional Node root = 1;
}

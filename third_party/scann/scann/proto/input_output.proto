// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package research_scann;

import "third_party/scann/scann/proto/incremental_updates.proto";

message InputOutputConfig {
  enum InMemoryTypes {
    INT8 = 0;
    UINT8 = 1;
    INT16 = 2;
    INT32 = 4;
    UINT32 = 5;
    INT64 = 6;
    FLOAT = 8;
    DOUBLE = 9;

    IN_MEMORY_DATA_TYPE_NOT_SPECIFIED = 255;

    UINT16 = 3 [deprecated = true];
    UINT64 = 7 [deprecated = true];
  }

  optional InMemoryTypes in_memory_data_type = 2
      [default = IN_MEMORY_DATA_TYPE_NOT_SPECIFIED];

  optional bool parallelize_disk_reads_on_startup = 27 [default = false];

  optional string database_wildcard = 3;

  optional bool allow_two_pass_read = 15 [default = false];

  optional string query_wildcard = 8;

  oneof IncrementalUpdateMetadataOneof {
    string incremental_update_metadata_location = 19;
    IncrementalUpdateMetadata incremental_update_metadata = 22;
  }

  optional string output_sstable_wildcard = 11;

  optional string preprocessed_artifacts_dir = 18;

  enum ArtifactsNamingOption {
    HASHED = 0;

    NO_HASH = 1;
  }
  optional ArtifactsNamingOption artifacts_naming_option = 23
      [default = HASHED];

  message PureDynamicConfig {
    optional int32 num_shards = 1 [default = 1];

    enum VectorType {
      UNSPECIFIED_VECTOR_TYPE = 0;

      SPARSE = 1;

      DENSE = 2;
    }

    optional VectorType vector_type = 2 [default = UNSPECIFIED_VECTOR_TYPE];

    optional uint64 dimensionality = 3;
  }

  optional PureDynamicConfig pure_dynamic_config = 21;

  enum FeatureNorm {
    NONE = 0;
    UNITL2NORM = 1;
    STDGAUSSNORM = 2;
    UNITL1NORM = 3;
  }

  optional FeatureNorm norm_type = 5 [default = NONE];

  reserved 20;
  reserved 25;

  optional bool non_negative = 6 [default = false];

  optional bool is_dense = 7;

  message PreprocessingFunction {
    optional string name = 1;

    optional string config_ascii = 2;
  }

  optional PreprocessingFunction query_preprocessing_function = 16;

  optional string hashed_database_wildcard = 12;
  optional string fixed_point_database_wildcard = 17;
  optional string tokenized_database_wildcard = 14;
  optional string memory_consumption_estimate_filename = 24;

  reserved 1;
  reserved "input_handler";
  optional bool saturate = 10 [default = false, deprecated = true];
  optional uint64 dimensionality = 4 [deprecated = true];

  reserved 13;
}

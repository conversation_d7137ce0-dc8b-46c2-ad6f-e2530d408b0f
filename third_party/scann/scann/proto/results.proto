// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package research_scann;

import "third_party/scann/scann/data_format/features.proto";

message NearestNeighbors {
  required bytes docid = 1;

  optional bytes metadata = 3;

  optional string retrieved_version = 4;

  repeated Neighbor neighbor = 2;

  message Neighbor {
    required bytes docid = 3;

    optional double distance = 4;

    optional bytes metadata = 5;

    optional int64 crowding_attribute = 6;

    optional GenericFeatureVector gfv = 7;
  }
}

message NearestNeighborsFast {
  repeated fixed64 neighbor_ids = 2 [packed = true];

  repeated float neighbor_distances = 3 [packed = true];

  repeated int64 neighbor_crowding_attributes = 4 [packed = true];
}

// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package research_scann;

import "third_party/scann/scann/data_format/features.proto";

message HashedItem {
  optional bytes data_id_str = 1;

  required bytes indicator_vars = 2;

  optional bytes soar_indicator_vars = 6;

  optional int32 soar_partition_idx = 7 [default = -1];

  reserved 4;

  repeated int64 deprecated_token_membership = 3;

  enum PackingStrategy {
    NONE = 0;
    NIBBLE = 1;
    BINARY = 2;
  }

  optional PackingStrategy packing_strategy = 5 [default = BINARY];
}

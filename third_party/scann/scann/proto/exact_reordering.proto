// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



syntax = "proto2";

package research_scann;

import "third_party/scann/scann/proto/distance_measure.proto";

message ExactReordering {
  optional int32 approx_num_neighbors = 1 [default = **********];

  optional float approx_epsilon_distance = 2 [default = inf];

  optional DistanceMeasureConfig approx_distance_measure = 3;

  oneof Quantization {
    FixedPoint fixed_point = 5;
    Bfloat16 bfloat16 = 7;
  }

  optional NeighborSelectionOverrideHeuristics
      neighbor_selection_override_heuristics = 6;

  optional bool use_fixed_point_if_possible = 4
      [default = false, deprecated = true];
}

message FixedPoint {
  optional bool enabled = 1 [default = false];

  optional float fixed_point_multiplier = 2 [default = nan];

  optional string multipliers_filename = 7;

  optional double noise_shaping_threshold = 8 [default = nan];

  optional float fixed_point_multiplier_quantile = 6 [default = 1.0];

  optional string mr_jobname_prefix = 5;

  optional string offline_quantization_cell = 3;

  optional int32 num_machines = 4;
}

message Bfloat16 {
  optional bool enabled = 1 [default = false];

  optional double noise_shaping_threshold = 2 [default = nan];
}

message NeighborSelectionOverrideHeuristics {
  optional float approx_num_neighbors_multiplier = 1 [default = 2.0];

  optional float approx_epsilon_distance_multiplier = 2 [default = 1.2];
}

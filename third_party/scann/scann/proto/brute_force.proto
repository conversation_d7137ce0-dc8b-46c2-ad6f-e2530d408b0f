// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



syntax = "proto2";

package research_scann;

import "third_party/scann/scann/proto/exact_reordering.proto";

message BruteForceConfig {
  optional bool scalar_quantized = 1 [default = false, deprecated = true];

  oneof Quantization {
    FixedPoint fixed_point = 4;

    Bfloat16 bfloat16 = 5;
  }

  optional float scalar_quantization_multiplier_quantile = 2
      [default = 1.0, deprecated = true];

  optional float scalar_quantization_noise_shaping_threshold = 3
      [default = nan];
}

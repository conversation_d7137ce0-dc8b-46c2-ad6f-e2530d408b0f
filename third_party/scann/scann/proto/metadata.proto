// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



syntax = "proto2";

package research_scann;

import "third_party/scann/scann/proto/distance_measure.proto";

message MetadataConfig {
  oneof metadata_type {
    SparseIntersectionConfig sparse_intersection = 1;

    DistanceMeasureConfig exact_distance = 2;

    UserInfoMetadataConfig userinfo = 3;

    ClassLabelMetadataConfig class_label = 6;

    DatabaseVectorMetadataConfig database_vector = 5;

    bytes custom_metadata = 4;
  }
}

message SparseIntersectionConfig {
  optional uint32 max_to_return = 1 [default = **********];
}

message SparseIntersectionResult {
  repeated uint64 intersecting_dimension = 1;

  repeated double value = 2;

  optional uint32 num_intersecting_dimensions = 3;
}

message UserInfoMetadataConfig {
  optional bool set_user_info_for_query = 1 [default = false];

  optional bool set_user_info_for_neighbor = 2 [default = true];
}

message ClassLabelMetadataConfig {}

message DatabaseVectorMetadataConfig {
  optional bool return_database_vector = 1 [default = false];

  optional bool verbatim_gfv = 2 [default = false];
}

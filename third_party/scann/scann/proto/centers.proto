// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



syntax = "proto2";

package research_scann;

import "third_party/scann/scann/data_format/features.proto";
import "third_party/scann/scann/proto/hash.proto";
import "third_party/scann/scann/proto/projection.proto";

message CentersForAllSubspaces {
  repeated CentersForSubspace subspace_centers = 1;
  optional AsymmetricHasherConfig.QuantizationScheme quantization_scheme = 2
      [default = PRODUCT];

  optional SerializedProjection serialized_projection = 3;
}

message CentersForSubspace {
  repeated GenericFeatureVector center = 1;
}

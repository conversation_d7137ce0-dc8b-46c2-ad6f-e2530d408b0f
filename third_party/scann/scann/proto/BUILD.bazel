load("//third_party/scann/scann/oss_wrappers:build_defs.bzl", "scann_py_proto_library")
load("//tools/bzl:cc.bzl", "cc_proto_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# Generated Files
# ==================================================================

proto_library(
    name = "scann_proto",
    srcs = ["scann.proto"],
    deps = [
        ":auto_tuning_proto",
        ":brute_force_proto",
        ":crowding_proto",
        ":disjoint_restrict_token_proto",
        ":distance_measure_proto",
        ":exact_reordering_proto",
        ":hash_proto",
        ":input_output_proto",
        ":metadata_proto",
        ":min_distance_proto",
        ":partitioning_proto",
    ],
)

cc_proto_library(
    name = "scann_cc_proto",
    deps = [":scann_proto"],
    extra_deps = [
        ":auto_tuning_cc_proto",
        ":brute_force_cc_proto",
        ":crowding_cc_proto",
        ":disjoint_restrict_token_cc_proto",
        ":distance_measure_cc_proto",
        ":exact_reordering_cc_proto",
        ":hash_cc_proto",
        ":input_output_cc_proto",
        ":metadata_cc_proto",
        ":min_distance_cc_proto",
        ":partitioning_cc_proto",
    ],
)

scann_py_proto_library(
    name = "scann_py_pb2",
    srcs = ["scann.proto"],
    proto_deps = [":scann_proto"],
    py_proto_deps = [
        ":auto_tuning_py_pb2",
        ":brute_force_py_pb2",
        ":crowding_py_pb2",
        ":disjoint_restrict_token_py_pb2",
        ":distance_measure_py_pb2",
        ":exact_reordering_py_pb2",
        ":hash_py_pb2",
        ":input_output_py_pb2",
        ":metadata_py_pb2",
        ":min_distance_py_pb2",
        ":partitioning_py_pb2",
    ],
)

proto_library(
    name = "crowding_proto",
    srcs = ["crowding.proto"],
    deps = [
    ],
)

cc_proto_library(
    name = "crowding_cc_proto",
    deps = [":crowding_proto"],
)

scann_py_proto_library(
    name = "crowding_py_pb2",
    srcs = ["crowding.proto"],
    proto_deps = [":crowding_proto"],
)

proto_library(
    name = "input_output_proto",
    srcs = ["input_output.proto"],
    deps = [
        ":incremental_updates_proto",
    ],
)

cc_proto_library(
    name = "input_output_cc_proto",
    extra_deps = [":incremental_updates_cc_proto"],
    deps = [":input_output_proto"],
)

scann_py_proto_library(
    name = "input_output_py_pb2",
    srcs = ["input_output.proto"],
    proto_deps = [":input_output_proto"],
    py_proto_deps = [
        ":incremental_updates_py_pb2",
    ],
)

proto_library(
    name = "hash_proto",
    srcs = ["hash.proto"],
    deps = [
        ":distance_measure_proto",
        ":projection_proto",
    ],
)

cc_proto_library(
    name = "hash_cc_proto",
    deps = [":hash_proto"],
    extra_deps = [":distance_measure_cc_proto", ":projection_cc_proto"],
)

scann_py_proto_library(
    name = "hash_py_pb2",
    srcs = ["hash.proto"],
    proto_deps = [":hash_proto"],
    py_proto_deps = [
        ":distance_measure_py_pb2",
        ":projection_py_pb2",
    ],
)

proto_library(
    name = "metadata_proto",
    srcs = ["metadata.proto"],
    deps = [
        ":distance_measure_proto",
    ],
)

scann_py_proto_library(
    name = "metadata_py_pb2",
    srcs = ["metadata.proto"],
    proto_deps = [":metadata_proto"],
    py_proto_deps = [
        ":distance_measure_py_pb2",
    ],
)

cc_proto_library(
    name = "metadata_cc_proto",
    deps = [":metadata_proto"],
    extra_deps = [":distance_measure_cc_proto"],
)

proto_library(
    name = "brute_force_proto",
    srcs = ["brute_force.proto"],
    deps = [
        ":exact_reordering_proto",
    ],
)

scann_py_proto_library(
    name = "brute_force_py_pb2",
    srcs = ["brute_force.proto"],
    proto_deps = [":brute_force_proto"],
    py_proto_deps = [
        ":exact_reordering_py_pb2",
    ],
)

cc_proto_library(
    name = "brute_force_cc_proto",
    extra_deps = [":exact_reordering_cc_proto"],
    deps = [":brute_force_proto"],
)

proto_library(
    name = "exact_reordering_proto",
    srcs = ["exact_reordering.proto"],
    deps = [
        ":distance_measure_proto",
    ],
)

cc_proto_library(
    name = "exact_reordering_cc_proto",
    deps = [":exact_reordering_proto"],
    extra_deps = [":distance_measure_cc_proto"],
)

scann_py_proto_library(
    name = "exact_reordering_py_pb2",
    srcs = ["exact_reordering.proto"],
    proto_deps = [":exact_reordering_proto"],
    py_proto_deps = [
        ":distance_measure_py_pb2",
    ],
)

proto_library(
    name = "partitioning_proto",
    srcs = ["partitioning.proto"],
    deps = [
        ":distance_measure_proto",
        ":exact_reordering_proto",
        ":projection_proto",
        "@protobuf//:duration_proto",
    ],
)

cc_proto_library(
    name = "partitioning_cc_proto",
    deps = [":partitioning_proto"],
    extra_deps = [
        ":distance_measure_cc_proto",
        ":exact_reordering_cc_proto",
        ":projection_cc_proto",
    ],
)

scann_py_proto_library(
    name = "partitioning_py_pb2",
    srcs = [":partitioning.proto"],
    proto_deps = [":partitioning_proto"],
    py_proto_deps = [
        ":distance_measure_py_pb2",
        ":exact_reordering_py_pb2",
        ":input_output_py_pb2",
        ":projection_py_pb2",
    ],
)

proto_library(
    name = "projection_proto",
    srcs = ["projection.proto"],
    deps = [
        "//third_party/scann/scann/data_format:features_proto",
    ],
)

cc_proto_library(
    name = "projection_cc_proto",
    extra_deps = ["//third_party/scann/scann/data_format:features_cc_proto"],
    deps = [":projection_proto"],
)

scann_py_proto_library(
    name = "projection_py_pb2",
    srcs = ["projection.proto"],
    proto_deps = [":projection_proto"],
)

proto_library(
    name = "distance_measure_proto",
    srcs = ["distance_measure.proto"],
    deps = [
    ],
)

scann_py_proto_library(
    name = "distance_measure_py_pb2",
    srcs = ["distance_measure.proto"],
    proto_deps = [":distance_measure_proto"],
)

cc_proto_library(
    name = "distance_measure_cc_proto",
    deps = [":distance_measure_proto"],
)

proto_library(
    name = "results_proto",
    srcs = ["results.proto"],
    deps = [
        "//third_party/scann/scann/data_format:features_proto",
    ],
)

cc_proto_library(
    name = "results_cc_proto",
    extra_deps = ["//third_party/scann/scann/data_format:features_cc_proto"],
    deps = [":results_proto"],
)

proto_library(
    name = "centers_proto",
    srcs = ["centers.proto"],
    deps = [
        ":hash_proto",
        ":projection_proto",
        "//third_party/scann/scann/data_format:features_proto",
    ],
)

cc_proto_library(
    name = "centers_cc_proto",
    deps = [":centers_proto"],
    extra_deps = [
        ":hash_cc_proto",
        ":projection_cc_proto",
        "//third_party/scann/scann/data_format:features_cc_proto",
    ],
)

scann_py_proto_library(
    name = "centers_py_pb2",
    srcs = ["centers.proto"],
    proto_deps = [":centers_proto"],
    py_proto_deps = [
        ":hash_py_pb2",
        "//third_party/scann/scann/data_format:features_py_pb2",
    ],
)

proto_library(
    name = "hashed_proto",
    srcs = ["hashed.proto"],
    deps = [
        "//third_party/scann/scann/data_format:features_proto",
    ],
)

cc_proto_library(
    name = "hashed_cc_proto",
    deps = [":hashed_proto"],
    extra_deps = ["//third_party/scann/scann/data_format:features_cc_proto"],
)

proto_library(
    name = "disjoint_restrict_token_proto",
    srcs = ["disjoint_restrict_token.proto"],
    deps = [
    ],
)

scann_py_proto_library(
    name = "disjoint_restrict_token_py_pb2",
    srcs = ["disjoint_restrict_token.proto"],
    proto_deps = [":disjoint_restrict_token_proto"],
)

cc_proto_library(
    name = "disjoint_restrict_token_cc_proto",
    deps = [":disjoint_restrict_token_proto"],
)

proto_library(
    name = "incremental_updates_proto",
    srcs = ["incremental_updates.proto"],
    deps = [
        "@protobuf//:duration_proto",
        "@protobuf//:timestamp_proto",
    ],
)

cc_proto_library(
    name = "incremental_updates_cc_proto",
    deps = [":incremental_updates_proto"],
)

scann_py_proto_library(
    name = "incremental_updates_py_pb2",
    srcs = ["incremental_updates.proto"],
    proto_deps = [":incremental_updates_proto"],
)

proto_library(
    name = "auto_tuning_proto",
    srcs = ["auto_tuning.proto"],
)

cc_proto_library(
    name = "auto_tuning_cc_proto",
    deps = [":auto_tuning_proto"],
)

scann_py_proto_library(
    name = "auto_tuning_py_pb2",
    srcs = ["auto_tuning.proto"],
    proto_deps = [":auto_tuning_proto"],
)

proto_library(
    name = "min_distance_proto",
    srcs = ["min_distance.proto"],
)

cc_proto_library(
    name = "min_distance_cc_proto",
    deps = [":min_distance_proto"],
)

scann_py_proto_library(
    name = "min_distance_py_pb2",
    srcs = ["min_distance.proto"],
    proto_deps = [":min_distance_proto"],
)

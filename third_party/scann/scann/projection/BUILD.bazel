load("//third_party/scann:scann.bzl", "scann_cc_library")
# Description:
#   This package contains projection methods for vectors.

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# Libraries
# ========================================================================

scann_cc_library(
    name = "projection_base",
    srcs = ["projection_base.cc"],
    hdrs = ["projection_base.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
    ],
)

scann_cc_library(
    name = "chunking_projection",
    srcs = ["chunking_projection.cc"],
    hdrs = ["chunking_projection.h"],
    deps = [
        ":identity_projection",
        ":projection_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "pca_projection",
    srcs = ["pca_projection.cc"],
    hdrs = ["pca_projection.h"],
    deps = [
        ":projection_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:pca_utils",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "eigenvalue_opq_projection",
    srcs = ["eigenvalue_opq_projection.cc"],
    hdrs = ["eigenvalue_opq_projection.h"],
    deps = [
        ":projection_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:pca_utils",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "identity_projection",
    srcs = ["identity_projection.cc"],
    hdrs = ["identity_projection.h"],
    deps = [
        ":projection_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "projection_factory",
    srcs = select({
        "//conditions:default": ["projection_factory.cc"],
    }),
    hdrs = ["projection_factory.h"],
    deps = select({
        "//conditions:default": [
        ],
    }) + [
        ":chunking_projection",
        ":eigenvalue_opq_projection",
        ":identity_projection",
        ":pca_projection",
        ":projection_base",
        ":truncate_projection",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "truncate_projection",
    srcs = ["truncate_projection.cc"],
    hdrs = ["truncate_projection.h"],
    deps = [
        ":projection_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:types",
    ],
)

# Unit Tests
# ========================================================================

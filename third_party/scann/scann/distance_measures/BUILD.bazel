load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "distance_measure_base",
    srcs = ["distance_measure_base.cc"],
    hdrs = ["distance_measure_base.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "distance_measures",
    srcs = ["distance_measure_factory.cc"],
    hdrs = [
        "distance_measure_base.h",
        "distance_measure_factory.h",
        "distance_measures.h",
    ],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures/one_to_one:cosine_distance",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/distance_measures/one_to_one:hamming_distance",
        "//third_party/scann/scann/distance_measures/one_to_one:jaccard_distance",
        "//third_party/scann/scann/distance_measures/one_to_one:l1_distance",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/distance_measures/one_to_one:limited_inner_product",
        "//third_party/scann/scann/distance_measures/one_to_one:nonzero_intersect_distance",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/utils:types",
    ],
)

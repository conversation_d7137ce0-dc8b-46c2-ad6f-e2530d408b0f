load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "many_to_many_templates",
    hdrs = ["many_to_many_templates.h"],
    textual_hdrs = ["many_to_many_impl.inc"],
    deps = [
        ":fp8_transposed",
        ":many_to_many_common",
        ":many_to_many_flags",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:fma",
        "//third_party/scann/scann/utils/intrinsics:highway",
        "//third_party/scann/scann/utils/intrinsics:horizontal_sum",
        "//third_party/scann/scann/utils/intrinsics:simd",
    ],
)

# Abridged version of the many_to_many library that only supports float and double.  Useful for
# clients who don't need orthogonality amplification or FP8 support, and can't accept the larger
# binary size.
#
# NOTE:  Forcing low-level modules to always compile in optimized mode to
# avoid excessive stack usage due to lack of compiler optimization.  This
# avoids stack overflows in partner tests.
scann_cc_library(
    name = "many_to_many_floating_point",
    srcs = [
        "many_to_many_double.cc",
        "many_to_many_float.cc",
    ],
    hdrs = [
        "many_to_many_floating_point.h",
    ],
    copts = [
        "-O3",
        "-Wno-pass-failed",
    ],
    textual_hdrs = ["many_to_many_impl.inc"],
    deps = [
        ":many_to_many_common",
        ":many_to_many_flags",
        ":many_to_many_templates",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:fma",
        "//third_party/scann/scann/utils/intrinsics:highway",
        "//third_party/scann/scann/utils/intrinsics:horizontal_sum",
        "//third_party/scann/scann/utils/intrinsics:simd",
    ],
)

# NOTE:  Forcing low-level modules to always compile in optimized mode to
# avoid excessive stack usage due to lack of compiler optimization.  This
# avoids stack overflows in partner tests.
scann_cc_library(
    name = "many_to_many",
    srcs = [
        "many_to_many_fixed8.cc",
        "many_to_many_orthogonality_amplification.cc",
    ],
    hdrs = [
        "many_to_many.h",
        "many_to_many_templates.h",
    ],
    copts = [
        "-O3",
        "-Wno-pass-failed",
    ],
    textual_hdrs = ["many_to_many_impl.inc"],
    deps = [
        ":fp8_transposed",
        ":many_to_many_common",
        ":many_to_many_flags",
        ":many_to_many_floating_point",  # buildcleaner: export
        ":many_to_many_templates",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:fma",
        "//third_party/scann/scann/utils/intrinsics:highway",
        "//third_party/scann/scann/utils/intrinsics:horizontal_sum",
        "//third_party/scann/scann/utils/intrinsics:simd",
    ],
)

scann_cc_library(
    name = "many_to_many_flags",
    srcs = ["many_to_many_flags.cc"],
    hdrs = ["many_to_many_flags.h"],
    deps = [
        "@abseil-cpp//absl/flags:flag",
    ],
)

scann_cc_library(
    name = "many_to_many_common",
    hdrs = ["many_to_many_common.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:highway",
        "//third_party/scann/scann/utils/intrinsics:simd",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:prefetch",
        "@abseil-cpp//absl/synchronization",
    ],
)

scann_cc_library(
    name = "fp8_transposed",
    srcs = ["fp8_transposed.cc"],
    hdrs = ["fp8_transposed.h"],
    deps = [
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:flags",
        "//third_party/scann/scann/utils/intrinsics:highway",
    ],
)

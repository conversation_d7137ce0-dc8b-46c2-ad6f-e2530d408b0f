load("//tools/bzl:cc.bzl", "cc_proto_library")
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "one_to_many_helpers",
    hdrs = ["one_to_many_helpers.h"],
    deps = [
        ":scale_encoding_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:scalar_quantization_helpers",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "one_to_many",
    srcs = [
        "one_to_many_asymmetric.cc",
        "one_to_many_symmetric.cc",
    ],
    hdrs = [
        "one_to_many.h",
        "one_to_many_asymmetric.h",
        "one_to_many_symmetric.h",
    ],
    textual_hdrs = [
        "one_to_many_asymmetric_impl.inc",
        "one_to_many_impl_highway.inc",
    ],
    deps = [
        ":one_to_many_helpers",
        ":scale_encoding_cc_proto",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/utils:bfloat16_helpers",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:scalar_quantization_helpers",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/internal:avx2_funcs",
        "//third_party/scann/scann/utils/internal:avx_funcs",
        "//third_party/scann/scann/utils/intrinsics:flags",
        "//third_party/scann/scann/utils/intrinsics:fma",
        "//third_party/scann/scann/utils/intrinsics:highway",
        "//third_party/scann/scann/utils/intrinsics:horizontal_sum",
        "//third_party/scann/scann/utils/intrinsics:simd",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/base:prefetch",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/synchronization",
        "@highway//:hwy",
    ],
)

proto_library(
    name = "scale_encoding_proto",
    srcs = ["scale_encoding.proto"],
)

cc_proto_library(
    name = "scale_encoding_cc_proto",
    deps = [":scale_encoding_proto"],
)

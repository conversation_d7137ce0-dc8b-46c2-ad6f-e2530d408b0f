// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "third_party/scann/scann/distance_measures/one_to_one/dot_product_avx2.h"

#include <cstdint>

#include "absl/log/check.h"
#include "third_party/scann/scann/utils/intrinsics/attributes.h"

#ifdef __x86_64__

#include "third_party/scann/scann/data_format/datapoint.h"
#include "third_party/scann/scann/utils/internal/avx2_funcs.h"

namespace research_scann {
namespace dp_internal {

#define SCANN_SIMD_ATTRIBUTE SCANN_AVX2
#include "third_party/scann/scann/distance_measures/one_to_one/dot_product_impl.inc"
#undef SCANN_SIMD_ATTRIBUTE

SCANN_AVX2_OUTLINE double DenseDotProductAvx2(const DatapointPtr<int8_t>& a,
                                              const DatapointPtr<float>& b) {
  DCHECK_EQ(a.nonzero_entries(), b.nonzero_entries());
  DCHECK(a.IsDense());
  DCHECK(b.IsDense());
  return DenseDotProductInt8FloatAvxImpl<AvxFunctionsAvx2Fma>(
      a.values(), b.values(), a.nonzero_entries());
}

SCANN_AVX2_OUTLINE double DenseDotProductAvx2(const DatapointPtr<int8_t>& a,
                                              const DatapointPtr<float>& b,
                                              const DatapointPtr<float>& c) {
  DCHECK_EQ(a.nonzero_entries(), b.nonzero_entries());
  DCHECK_EQ(a.nonzero_entries(), c.nonzero_entries());
  DCHECK(a.IsDense());
  DCHECK(b.IsDense());
  DCHECK(c.IsDense());
  return DenseDotProductInt8FloatFloatAvxImpl<AvxFunctionsAvx2Fma>(
      a.values(), b.values(), c.values(), a.nonzero_entries());
}

SCANN_AVX2_OUTLINE double DenseDotProductAvx2(const DatapointPtr<int8_t>& a,
                                              const DatapointPtr<int8_t>& b,
                                              const DatapointPtr<float>& c) {
  DCHECK_EQ(a.nonzero_entries(), b.nonzero_entries());
  DCHECK_EQ(a.nonzero_entries(), c.nonzero_entries());
  DCHECK(a.IsDense());
  DCHECK(b.IsDense());
  DCHECK(c.IsDense());
  return DenseDotProductInt8Int8FloatAvxImpl<AvxFunctionsAvx2Fma>(
      a.values(), b.values(), c.values(), a.nonzero_entries());
}

}  // namespace dp_internal
}  // namespace research_scann

#endif

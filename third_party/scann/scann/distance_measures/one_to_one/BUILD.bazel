load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "common",
    hdrs = ["common.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "binary_distance_measure_base",
    srcs = ["binary_distance_measure_base.cc"],
    hdrs = ["binary_distance_measure_base.h"],
    deps = [
        ":common",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/numeric:bits",
    ],
)

scann_cc_library(
    name = "cosine_distance",
    srcs = ["cosine_distance.cc"],
    hdrs = ["cosine_distance.h"],
    deps = [
        ":binary_distance_measure_base",
        ":common",
        ":dot_product",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/numeric:bits",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "dot_product",
    srcs = ["dot_product.cc"],
    hdrs = ["dot_product.h"],
    deps = [
        ":binary_distance_measure_base",
        ":common",
        ":dot_product_avx1",
        ":dot_product_avx2",
        ":dot_product_highway",
        ":dot_product_sse4",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:reduction",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:flags",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "dot_product_impl",
    textual_hdrs = ["dot_product_impl.inc"],
    deps = ["//third_party/scann/scann/data_format:datapoint"],
    alwayslink = 1,
)

scann_cc_library(
    name = "dot_product_avx1",
    srcs = ["dot_product_avx1.cc"],
    hdrs = ["dot_product_avx1.h"],
    deps = [
        ":dot_product_impl",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils/internal:avx_funcs",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "@abseil-cpp//absl/log:check",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "dot_product_avx2",
    srcs = ["dot_product_avx2.cc"],
    hdrs = ["dot_product_avx2.h"],
    deps = [
        ":dot_product_impl",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils/internal:avx2_funcs",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "@abseil-cpp//absl/log:check",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "dot_product_highway",
    srcs = ["dot_product_highway.cc"],
    hdrs = ["dot_product_highway.h"],
    deps = [
        ":dot_product_impl",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/log:check",
        "@highway//:hwy",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "dot_product_sse4",
    srcs = ["dot_product_sse4.cc"],
    hdrs = ["dot_product_sse4.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils/intrinsics:attributes",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "hamming_distance",
    srcs = ["hamming_distance.cc"],
    hdrs = ["hamming_distance.h"],
    deps = [
        ":binary_distance_measure_base",
        ":common",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:reduction",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "jaccard_distance",
    srcs = ["jaccard_distance.cc"],
    hdrs = ["jaccard_distance.h"],
    deps = [
        ":binary_distance_measure_base",
        ":common",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:reduction",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/numeric:bits",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "l1_distance",
    srcs = ["l1_distance.cc"],
    hdrs = ["l1_distance.h"],
    deps = [
        ":common",
        ":l1_distance_sse4",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:reduction",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:flags",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "l1_distance_sse4",
    srcs = ["l1_distance_sse4.cc"],
    hdrs = ["l1_distance_sse4.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "@abseil-cpp//absl/log:check",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "l2_distance",
    srcs = ["l2_distance.cc"],
    hdrs = ["l2_distance.h"],
    deps = [
        ":common",
        ":l2_distance_avx1",
        ":l2_distance_sse4",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:reduction",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:flags",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "l2_distance_avx1",
    srcs = ["l2_distance_avx1.cc"],
    hdrs = ["l2_distance_avx1.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "@abseil-cpp//absl/log:check",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "l2_distance_sse4",
    srcs = ["l2_distance_sse4.cc"],
    hdrs = ["l2_distance_sse4.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils/intrinsics:attributes",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "limited_inner_product",
    srcs = ["limited_inner_product.cc"],
    hdrs = ["limited_inner_product.h"],
    copts = ["-fno-tree-vectorize"],
    deps = [
        ":common",
        ":dot_product",
        ":l2_distance",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
    ],
    alwayslink = 1,
)

scann_cc_library(
    name = "nonzero_intersect_distance",
    srcs = ["nonzero_intersect_distance.cc"],
    hdrs = ["nonzero_intersect_distance.h"],
    deps = [
        ":common",
        ":dot_product",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:reduction",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
    ],
    alwayslink = 1,
)

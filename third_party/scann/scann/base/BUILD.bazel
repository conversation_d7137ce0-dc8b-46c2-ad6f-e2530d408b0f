load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "single_machine_factory_scann",
    srcs = ["single_machine_factory_scann.cc"],
    hdrs = ["single_machine_factory_scann.h"],
    deps = [
        ":reordering_helper_factory",
        ":single_machine_base",
        ":single_machine_factory_options",
        "//third_party/scann/scann/base/internal:single_machine_factory_impl",
        "//third_party/scann/scann/base/internal:tree_x_hybrid_factory",
        "//third_party/scann/scann/brute_force",
        "//third_party/scann/scann/brute_force:bfloat16_brute_force",
        "//third_party/scann/scann/brute_force:scalar_quantized_brute_force",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/partitioning:kmeans_tree_like_partitioner",
        "//third_party/scann/scann/partitioning:kmeans_tree_partitioner",
        "//third_party/scann/scann/partitioning:partitioner_base",
        "//third_party/scann/scann/partitioning:partitioner_factory",
        "//third_party/scann/scann/partitioning:partitioner_factory_base",
        "//third_party/scann/scann/partitioning:projecting_decorator",
        "//third_party/scann/scann/proto:brute_force_cc_proto",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:exact_reordering_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "//third_party/scann/scann/tree_x_hybrid:mutator",
        "//third_party/scann/scann/tree_x_hybrid:tree_ah_hybrid_residual",
        "//third_party/scann/scann/tree_x_hybrid:tree_x_hybrid_smmd",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:factory_helpers",
        "//third_party/scann/scann/utils:hash_leaf_helpers",
        "//third_party/scann/scann/utils:scalar_quantization_helpers",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/memory",
    ],
)

scann_cc_library(
    name = "reordering_helper_factory",
    srcs = ["reordering_helper_factory.cc"],
    hdrs = ["reordering_helper_factory.h"],
    deps = [
        ":single_machine_factory_options",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_model",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/projection:projection_factory",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:exact_reordering_cc_proto",
        "//third_party/scann/scann/utils:factory_helpers",
        "//third_party/scann/scann/utils:reordering_helper",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
    ],
)

scann_cc_library(
    name = "single_machine_factory_options",
    srcs = ["single_machine_factory_options.cc"],
    hdrs = ["single_machine_factory_options.h"],
    deps = [
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/partitioning:partitioner_cc_proto",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:input_data_utils",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/fixed_point:pre_quantized_fixed_point",
    ],
)

scann_cc_library(
    name = "single_machine_base",
    srcs = [
        "single_machine_base.cc",
        "single_machine_base_mutator.cc",
        "//third_party/scann/scann/brute_force:brute_force.cc",
        "//third_party/scann/scann/brute_force:brute_force_mutator.cc",
    ],
    hdrs = [
        "single_machine_base.h",
        "//third_party/scann/scann/brute_force:brute_force.h",
    ],
    deps = [
        ":restrict_allowlist",
        ":search_parameters",
        ":single_machine_factory_options",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/data_format:docid_collection_interface",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/distance_measures/many_to_many:many_to_many_floating_point",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/metadata:metadata_getter",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:results_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:factory_helpers",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:reordering_helper_interface",
        "//third_party/scann/scann/utils:single_machine_autopilot",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "//third_party/scann/scann/utils:zip_sort",
        "//third_party/scann/scann/utils/intrinsics:sse4",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/synchronization",
    ],
)

scann_cc_library(
    name = "search_parameters",
    srcs = ["search_parameters.cc"],
    hdrs = ["search_parameters.h"],
    deps = [
        ":restrict_allowlist",
        "//third_party/scann/scann/data_format:features_cc_proto",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    # Backcompat shim
    name = "restrict_allowlist",
    hdrs = ["restrict_allowlist.h"],
    deps = [
        "//third_party/scann/scann/restricts:restrict_allowlist",
    ],
)

scann_cc_library(
    name = "health_stats_collector",
    hdrs = ["health_stats_collector.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/partitioning:kmeans_tree_partitioner",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/status:statusor",
        "@abseil-cpp//absl/types:span",
    ],
)

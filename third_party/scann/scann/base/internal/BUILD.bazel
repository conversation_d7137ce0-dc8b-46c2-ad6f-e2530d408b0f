load("//third_party/scann:scann.bzl", "scann_cc_library")
# Implementation details of SingleMachineFactory.

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "single_machine_factory_impl",
    srcs = ["single_machine_factory_impl.cc"],
    hdrs = ["single_machine_factory_impl.h"],
    deps = [
        "//third_party/scann/scann/base:reordering_helper_factory",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/proto:crowding_cc_proto",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:factory_helpers",
        "//third_party/scann/scann/utils:hash_leaf_helpers",
        "//third_party/scann/scann/utils:scann_config_utils",
        "//third_party/scann/scann/utils:single_machine_autopilot",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/fixed_point:pre_quantized_fixed_point",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "tree_x_hybrid_factory",
    srcs = ["tree_x_hybrid_factory.cc"],
    hdrs = ["tree_x_hybrid_factory.h"],
    deps = [
        ":single_machine_factory_impl",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/brute_force:scalar_quantized_brute_force",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:searcher",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_model",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_options",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/partitioning:kmeans_tree_like_partitioner",
        "//third_party/scann/scann/partitioning:kmeans_tree_partitioner",
        "//third_party/scann/scann/partitioning:partitioner_base",
        "//third_party/scann/scann/partitioning:partitioner_factory",
        "//third_party/scann/scann/partitioning:projecting_decorator",
        "//third_party/scann/scann/partitioning:tree_brute_force_second_level_wrapper",
        "//third_party/scann/scann/proto:brute_force_cc_proto",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:exact_reordering_cc_proto",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "//third_party/scann/scann/tree_x_hybrid:tree_ah_hybrid_residual",
        "//third_party/scann/scann/tree_x_hybrid:tree_x_hybrid_smmd",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:factory_helpers",
        "//third_party/scann/scann/utils:hash_leaf_helpers",
        "//third_party/scann/scann/utils:parallel_for",
        "//third_party/scann/scann/utils:scalar_quantization_helpers",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/fixed_point:pre_quantized_fixed_point",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/strings",
    ],
)

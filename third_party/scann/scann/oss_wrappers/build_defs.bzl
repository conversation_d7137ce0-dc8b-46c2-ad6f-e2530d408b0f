""".bzl file for ScaNN open source build configs."""

load("//tools/bzl:python.bzl", "py_proto_library")

def scann_py_proto_library(
        name,
        srcs,
        py_proto_deps = [],
        proto_deps = None,
        visibility = ["//visibility:public"],
        **kwargs):
    """Generates py_proto_library for ScaNN open source version.

    Args:
      name: the name of the py_proto_library.
      srcs: the .proto files of the py_proto_library for Bazel use.
      py_proto_deps: a list of dependency labels for Bazel use; must be
        py_proto_library.
      proto_deps: a list of dependency labels for internal use.
      **kwargs: other keyword arguments that are passed to py_proto_library.
    """
    py_proto_library(
        name = name,
        protos = proto_deps,
        deps = py_proto_deps,
        visibility = visibility,
 #       more_deps = ["@protobuf//:well_known_types_py_pb2_genproto"],
        **kwargs
    )

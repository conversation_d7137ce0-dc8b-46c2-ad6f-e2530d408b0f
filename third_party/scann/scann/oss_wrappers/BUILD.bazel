load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "tf_dependency",
    deps = [
        "@tf_archive//:libtensorflow_framework",
        "@tf_archive//:tf_header_lib",
    ],
)

scann_cc_library(
    name = "scann_aligned_malloc",
    srcs = ["scann_aligned_malloc.cc"],
    hdrs = ["scann_aligned_malloc.h"],
)

scann_cc_library(
    name = "scann_down_cast",
    hdrs = ["scann_down_cast.h"],
    deps = [
        "@abseil-cpp//absl/base",
    ],
)

scann_cc_library(
    name = "scann_castops",
    hdrs = ["scann_castops.h"],
)

scann_cc_library(
    name = "scann_cpu_info",
    srcs = ["scann_cpu_info.cc"],
    hdrs = ["scann_cpu_info.h"],
    deps = [
        "@abseil-cpp//absl/base",
    ],
)

scann_cc_library(
    name = "scann_bits",
    srcs = ["scann_bits.cc"],
    hdrs = ["scann_bits.h"],
    deps = [
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/numeric:bits",
    ],
)

scann_cc_library(
    name = "scann_malloc_extension",
    hdrs = ["scann_malloc_extension.h"],
    deps = [
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/types:optional",
    ],
)

scann_cc_library(
    name = "scann_random",
    hdrs = ["scann_random.h"],
)

scann_cc_library(
    name = "scann_status",
    srcs = ["scann_status.cc"],
    hdrs = ["scann_status.h"],
    deps = [
        ":scann_status_builder",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "scann_status_builder",
    srcs = ["scann_status_builder.cc"],
    hdrs = ["scann_status_builder.h"],
    deps = [
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/status:statusor",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/strings:cord",
    ],
)

scann_cc_library(
    name = "scann_serialize",
    srcs = ["scann_serialize.cc"],
    hdrs = ["scann_serialize.h"],
    deps = [
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:endian",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "scann_threadpool",
    srcs = ["scann_threadpool.cc"],
    hdrs = ["scann_threadpool.h"],
    deps = [
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/strings",
        "@eigen",
    ],
)

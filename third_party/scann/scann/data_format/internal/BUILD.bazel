load("//third_party/scann:scann.bzl", "scann_cc_library")
# Description:
#   Internal data structures for data_format, that are not intended to be part
#   of ScaNN's public C++ API.

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "short_string_optimized_string",
    hdrs = ["short_string_optimized_string.h"],
    deps = [
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_malloc_extension",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/types:optional",
    ],
)

scann_cc_library(
    name = "string_view32",
    hdrs = ["string_view32.h"],
    deps = [
        "//third_party/scann/scann/utils:types",
    ],
)

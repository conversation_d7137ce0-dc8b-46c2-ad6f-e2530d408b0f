# This package defines the in-memory representation of ScaNN datasets and
# datapoints.
load("//third_party/scann:scann.bzl", "scann_cc_library")
load("//third_party/scann/scann/oss_wrappers:build_defs.bzl", "scann_py_proto_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# Generated Files
# ==================================================================

proto_library(
    name = "features_proto",
    srcs = ["features.proto"],
    features = ["-proto_dynamic_mode_static_link"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

cc_proto_library(
    name = "features_cc_proto",
    deps = [":features_proto"],
)

scann_py_proto_library(
    name = "features_py_pb2",
    srcs = ["features.proto"],
    proto_deps = [":features_proto"],
    py_proto_deps = [],
    visibility = ["//visibility:public"],
)

# Libraries
# ==================================================================

scann_cc_library(
    name = "gfv_conversion",
    hdrs = ["gfv_conversion.h"],
    deps = [
        ":features_cc_proto",
        ":gfv_properties",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "datapoint",
    srcs = ["datapoint.cc"],
    hdrs = ["datapoint.h"],
    deps = [
        ":features_cc_proto",
        ":gfv_conversion",
        ":gfv_properties",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/proto:hashed_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:infinite_one_array",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:zip_sort",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "dataset",
    srcs = [
        "dataset.cc",
        "dataset_mutator.cc",
    ],
    hdrs = ["dataset.h"],
    deps = [
        ":datapoint",
        ":docid_collection",
        ":docid_collection_interface",
        ":features_cc_proto",
        ":gfv_properties",
        ":sparse_low_level",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/proto:hashed_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:iterators",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "@abseil-cpp//absl/base:prefetch",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "docid_collection_interface",
    hdrs = ["docid_collection_interface.h"],
    deps = [
        ":docid_lookup",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "docid_collection",
    srcs = ["docid_collection.cc"],
    hdrs = ["docid_collection.h"],
    deps = [
        ":docid_collection_interface",
        "//third_party/scann/scann/data_format/internal:short_string_optimized_string",
        "//third_party/scann/scann/data_format/internal:string_view32",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_serialize",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:memory_logging",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/container:flat_hash_map",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "docid_lookup",
    hdrs = ["docid_lookup.h"],
    deps = [
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/functional:any_invocable",
    ],
)

scann_cc_library(
    name = "gfv_properties",
    srcs = ["gfv_properties.cc"],
    hdrs = ["gfv_properties.h"],
    deps = [
        ":features_cc_proto",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/log:check",
    ],
)

scann_cc_library(
    name = "sparse_low_level",
    hdrs = ["sparse_low_level.h"],
    deps = [
        "//third_party/scann/scann/utils:types",
    ],
)

# Unit Tests
# ==================================================================

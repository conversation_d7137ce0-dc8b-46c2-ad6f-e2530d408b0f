// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.



syntax = "proto2";

package research_scann;

import "google/protobuf/timestamp.proto";

message GenericFeatureVector {
  extensions 1000 to max;

  enum FeatureType {
    UNKNOWN = 0;
    INT64 = 1;
    FLOAT = 2;
    DOUBLE = 3;
    STRING = 4;
    BINARY = 5;
  }

  required FeatureType feature_type = 1;

  optional bytes data_id_str = 2;

  repeated float feature_value_float = 4 [packed = true];

  repeated double feature_value_double = 5 [packed = true];

  repeated int64 feature_value_int64 = 3 [packed = true];

  optional bytes feature_value_string = 13;

  repeated uint64 feature_index = 6 [packed = true];

  enum FeatureNorm {
    NONE = 0;
    UNITL2NORM = 1;
    STDGAUSSNORM = 2;
    UNITL1NORM = 3;
  }

  optional FeatureNorm norm_type = 9 [default = NONE];

  optional string class_label = 16;

  message Crowding {
    optional int64 crowding_attribute = 1;
  }

  optional Crowding crowding = 18;

  reserved 15;

  message FixedPointMetadata {
    optional float squared_l2_norm = 1;
  }

  optional FixedPointMetadata fixed_point_metadata = 19;

  optional bytes userinfo = 17 [ctype = CORD];

  optional google.protobuf.Timestamp expiration_timestamp = 20;

  optional uint64 feature_dim = 10 [default = 0xFFFFFFFFFFFFFFFF];

  optional float weight = 8 [deprecated = true];

  repeated int32 tokens = 14 [packed = true, deprecated = true];
}

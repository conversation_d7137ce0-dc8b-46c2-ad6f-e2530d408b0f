load("@pybind11_bazel//:build_defs.bzl", "pybind_library")
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "scann",
    srcs = ["scann.cc"],
    hdrs = ["scann.h"],
    deps = [
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/base:single_machine_factory_scann",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/partitioning:partitioner_cc_proto",
        "//third_party/scann/scann/proto:brute_force_cc_proto",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "//third_party/scann/scann/scann_ops:scann_assets_cc_proto",
        "//third_party/scann/scann/tree_x_hybrid:tree_x_params",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:io_npy",
        "//third_party/scann/scann/utils:io_oss_wrapper",
        "//third_party/scann/scann/utils:scann_config_utils",
        "//third_party/scann/scann/utils:single_machine_retraining",
        "//third_party/scann/scann/utils:threads",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/container:node_hash_set",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/status:statusor",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/synchronization",
        "@abseil-cpp//absl/types:span",
        "@protobuf//:protobuf",
    ],
)

pybind_library(
    name = "scann_npy",
    srcs = ["scann_npy.cc"],
    hdrs = ["scann_npy.h"],
    deps = [
        ":scann",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:io_oss_wrapper",
        "//third_party/scann/scann/utils:single_machine_autopilot",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/synchronization",
        "@abseil-cpp//absl/types:span",
    ],
)

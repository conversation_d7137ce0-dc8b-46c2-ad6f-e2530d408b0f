// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package research_scann;

message ScannAsset {
  enum AssetType {
    UNSPECIFIED_TYPE = 0;
    DATASET = 1;
    INT8_DATASET = 2;
    AH_DATASET = 3;
    TOKENIZATION = 4;

    REORDERING_INT8_MULTIPLIERS = 5;
    BRUTE_FORCE_INT8_MULTIPLIERS = 6;
    AH_CENTERS = 7;
    PARTITIONER = 8;

    DATASET_NPY = 9;
    INT8_DATASET_NPY = 10;
    AH_DATASET_NPY = 11;
    AH_DATASET_SOAR_NPY = 16;
    TOKENIZATION_NPY = 12;
    INT8_MULTIPLIERS_NPY = 13;
    INT8_NORMS_NPY = 14;
    BF16_DATASET_NPY = 15;
  }
  optional AssetType asset_type = 1;
  optional string asset_path = 2;
}

message ScannAssets {
  repeated ScannAsset assets = 1;

  optional bool trained_on_the_fly = 2 [default = true];
}

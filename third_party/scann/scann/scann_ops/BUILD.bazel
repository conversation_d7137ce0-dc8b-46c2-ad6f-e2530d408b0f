load("//third_party/scann/scann/oss_wrappers:build_defs.bzl", "scann_py_proto_library")
load("//tools/bzl:cc.bzl", "cc_proto_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

proto_library(
    name = "scann_assets_proto",
    srcs = ["scann_assets.proto"],
)

cc_proto_library(
    name = "scann_assets_cc_proto",
    deps = [":scann_assets_proto"],
)

scann_py_proto_library(
    name = "scann_assets_py_pb2",
    srcs = ["scann_assets.proto"],
    proto_deps = [":scann_assets_proto"],
)

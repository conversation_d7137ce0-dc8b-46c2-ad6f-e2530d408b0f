load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_test")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

py_library(
    name = "scann",
    srcs = [],
    deps = [
        ":scann_builder",
        ":scann_ops_pybind",
    ],
)

py_library(
    name = "scann_builder",
    srcs = ["scann_builder.py"],
    deps = [],
)

py_library(
    name = "scann_ops_pybind",
    srcs = ["scann_ops_pybind.py"],
    data = [
        "//third_party/scann/scann/scann_ops/cc/python:scann_pybind.so",
    ],
    deps = [
        ":scann_builder",
        ":scann_ops_pybind_backcompat",
        requirement("numpy"),
    ],
)

py_test(
    name = "scann_ops_pybind_test",
    size = "medium",
    timeout = "long",
    srcs = ["scann_ops_pybind_test.py"],
    shard_count = 8,
    tags = ["manual"],
    deps = [
        ":scann_ops_pybind",
        requirement("absl-py"),
    ],
)

py_test(
    name = "scann_ops_pybind_updates_test",
    size = "medium",
    timeout = "long",
    srcs = ["scann_ops_pybind_updates_test.py"],
    shard_count = 8,
    tags = ["manual"],
    deps = [
        ":scann_ops_pybind",
        requirement("absl-py"),
    ],
)

py_library(
    name = "scann_ops_pybind_backcompat",
    srcs = ["scann_ops_pybind_backcompat.py"],
    deps = [
        "//third_party/scann/scann/scann_ops:scann_assets_py_pb2",
        requirement("numpy"),
    ],
)

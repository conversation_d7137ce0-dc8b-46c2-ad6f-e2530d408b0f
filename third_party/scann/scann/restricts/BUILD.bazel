# This directory contains code for restricts, which filter results returned by ScaNN based on
# Boolean logic.
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "restrict_allowlist",
    srcs = ["restrict_allowlist.cc"],
    hdrs = ["restrict_allowlist.h"],
    deps = [
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/utils:bit_iterator",
        "//third_party/scann/scann/utils:bits",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@googletest//:gtest",
    ],
)

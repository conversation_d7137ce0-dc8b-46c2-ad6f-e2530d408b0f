load("//third_party/scann:scann.bzl", "scann_cc_library")
# Description:
#   Brute force KNN/ENN search.

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# We build BruteForceSearcher as part of SingleMachineSearcherBase's build target so that some
# member functions of SingleMachineSearcherBase can return a BruteForceSearcher.
exports_files([
    "brute_force.h",
    "brute_force.cc",
    "brute_force_mutator.cc",
])

# This target is kept for backward compatibility reasons.  Historically, BruteForceSearcher and
# SingleMachineSearcherBase were built as separate targets.  Some users may still depend on
# this target.
scann_cc_library(
    name = "brute_force",
    hdrs = [
        "brute_force.h",
    ],
    deps = [
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/many_to_many:many_to_many_floating_point",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "scalar_quantized_brute_force",
    srcs = [
        "scalar_quantized_brute_force.cc",
        "scalar_quantized_brute_force_mutator.cc",
    ],
    hdrs = ["scalar_quantized_brute_force.h"],
    deps = [
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/data_format:docid_collection",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/tree_x_hybrid:leaf_searcher_optional_parameter_creator",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:scalar_quantization_helpers",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/fixed_point:pre_quantized_fixed_point",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/strings:str_format",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "bfloat16_brute_force",
    srcs = [
        "bfloat16_brute_force.cc",
        "bfloat16_brute_force_mutator.cc",
    ],
    hdrs = ["bfloat16_brute_force.h"],
    deps = [
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/utils:bfloat16_helpers",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/strings",
    ],
)

// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef SCANN_UTILS_INPUT_DATA_UTILS_H_
#define SCANN_UTILS_INPUT_DATA_UTILS_H_

#include <cstdint>

#include "third_party/scann/scann/data_format/dataset.h"
#include "third_party/scann/scann/proto/hash.pb.h"
#include "third_party/scann/scann/utils/fixed_point/pre_quantized_fixed_point.h"

namespace research_scann {

StatusOr<DatapointIndex> ComputeConsistentNumPointsFromIndex(
    const Dataset* dataset, const DenseDataset<uint8_t>* hashed_dataset,
    const PreQuantizedFixedPoint* pre_quantized_fixed_point,
    const DenseDataset<int16_t>* bfloat16_dataset,
    const vector<int64_t>* crowding_attributes);

StatusOr<DimensionIndex> ComputeConsistentDimensionalityFromIndex(
    const HashConfig& config, const Dataset* dataset,
    const DenseDataset<uint8_t>* hashed_dataset,
    const PreQuantizedFixedPoint* pre_quantized_fixed_point,
    const DenseDataset<int16_t>* bfloat16_dataset);

}  // namespace research_scann

#endif

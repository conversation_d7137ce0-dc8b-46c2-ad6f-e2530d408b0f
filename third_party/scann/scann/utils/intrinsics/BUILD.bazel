package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)
load("//third_party/scann:scann.bzl", "scann_cc_library")

################################################################################
#  PUBLIC HEADER
################################################################################

scann_cc_library(
    name = "simd",
    hdrs = ["simd.h"],
    deps = [
        ":attributes",
        ":avx1",
        ":avx2",
        ":avx512",
        ":fallback",
        ":flags",
        ":highway",
        ":sse4",
    ],
)

################################################################################
#  FMA (FusedMultiplyAdd)
################################################################################

scann_cc_library(
    name = "fma",
    hdrs = ["fma.h"],
    textual_hdrs = ["fma.inc"],
    deps = [
        ":highway",
        ":simd",
        "//third_party/scann/scann/utils:index_sequence",
    ],
)

################################################################################
#  Horizontal sum
################################################################################

scann_cc_library(
    name = "horizontal_sum",
    hdrs = ["horizontal_sum.h"],
    deps = [
        ":highway",
        ":simd",
        "@highway//:hwy",
    ],
)

################################################################################
#  Shared impl helpers.
################################################################################

scann_cc_library(
    name = "flags",
    srcs = ["flags.cc"],
    hdrs = ["flags.h"],
    deps = [
        "//third_party/scann/scann/oss_wrappers:scann_cpu_info",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/flags:flag",
        "@highway//:hwy",
    ],
)

scann_cc_library(
    name = "attributes",
    hdrs = ["attributes.h"],
)

################################################################################
#  fallback
################################################################################

scann_cc_library(
    name = "fallback",
    hdrs = ["fallback.h"],
    deps = [
        ":attributes",
        ":flags",
        "//third_party/scann/scann/utils:index_sequence",
        "//third_party/scann/scann/utils:types",
    ],
)

################################################################################
#  sse4
################################################################################

scann_cc_library(
    name = "sse4",
    hdrs = ["sse4.h"],
    deps = [
        ":attributes",
        ":flags",
        "//third_party/scann/scann/utils:index_sequence",
        "//third_party/scann/scann/utils:types",
    ],
)

################################################################################
#  avx1
################################################################################

scann_cc_library(
    name = "avx1",
    hdrs = ["avx1.h"],
    deps = [
        ":attributes",
        ":flags",
        ":sse4",
        "//third_party/scann/scann/utils:index_sequence",
        "//third_party/scann/scann/utils:types",
    ],
)

################################################################################
#  avx2
################################################################################

scann_cc_library(
    name = "avx2",
    hdrs = ["avx2.h"],
    deps = [
        ":attributes",
        ":avx1",
        ":flags",
        "//third_party/scann/scann/utils:index_sequence",
        "//third_party/scann/scann/utils:types",
    ],
)

################################################################################
#  avx512
################################################################################

scann_cc_library(
    name = "avx512",
    hdrs = ["avx512.h"],
    deps = [
        ":attributes",
        ":avx2",
        ":flags",
        "//third_party/scann/scann/utils:index_sequence",
        "//third_party/scann/scann/utils:types",
    ],
)

################################################################################
#  highway
################################################################################

scann_cc_library(
    name = "highway",
    hdrs = ["highway.h"],
    deps = [
        ":attributes",
        ":fallback",
        ":flags",
        "//third_party/scann/scann/utils:index_sequence",
        "//third_party/scann/scann/utils:types",
        "@highway//:hwy",
    ],
)

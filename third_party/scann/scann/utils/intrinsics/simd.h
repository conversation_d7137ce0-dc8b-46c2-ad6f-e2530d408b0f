// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef SCANN_UTILS_INTRINSICS_SIMD_H_
#define SCANN_UTILS_INTRINSICS_SIMD_H_

#include "third_party/scann/scann/utils/intrinsics/attributes.h"
#include "third_party/scann/scann/utils/intrinsics/avx1.h"
#include "third_party/scann/scann/utils/intrinsics/avx2.h"
#include "third_party/scann/scann/utils/intrinsics/avx512.h"
#include "third_party/scann/scann/utils/intrinsics/fallback.h"
#include "third_party/scann/scann/utils/intrinsics/flags.h"
#include "third_party/scann/scann/utils/intrinsics/highway.h"
#include "third_party/scann/scann/utils/intrinsics/sse4.h"

#endif

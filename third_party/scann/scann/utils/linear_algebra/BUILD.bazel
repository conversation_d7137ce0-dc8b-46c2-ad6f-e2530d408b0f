package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)
load("//third_party/scann:scann.bzl", "scann_cc_library")

scann_cc_library(
    name = "eigen_utils",
    srcs = ["eigen_utils.cc"],
    hdrs = ["eigen_utils.h"],
    deps = [
        ":types",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/utils:common",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/strings:str_format",
        "@abseil-cpp//absl/types:span",
        "@eigen",
    ],
)

scann_cc_library(
    name = "types",
    hdrs = ["types.h"],
    deps = ["@eigen"],
)

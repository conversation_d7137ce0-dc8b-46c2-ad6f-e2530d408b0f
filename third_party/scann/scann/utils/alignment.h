// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef SCANN_UTILS_ALIGNMENT_H_
#define SCANN_UTILS_ALIGNMENT_H_

#include <cstdint>

#include "third_party/scann/scann/utils/common.h"

namespace research_scann {

struct AlignedBuffer {
  unique_ptr<uint8_t[]> storage;
  uint8_t* ptr;
};

AlignedBuffer MakeCacheAlignedCopy(ConstSpan<uint8_t> span);

}  // namespace research_scann

#endif

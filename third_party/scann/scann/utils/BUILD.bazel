# Description:
#   This is for the utility functions to be used across ScaNN.
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# Libraries
# ========================================================================

scann_cc_library(
    name = "alignment",
    srcs = ["alignment.cc"],
    hdrs = ["alignment.h"],
    deps = [
        ":common",
    ],
)

scann_cc_library(
    name = "types",
    srcs = ["types.cc"],
    hdrs = ["types.h"],
    deps = [
        ":common",
        "//third_party/scann/scann/proto:input_output_cc_proto",
        "//third_party/scann/scann/proto:incremental_updates_cc_proto",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/flags:flag",
    ],
)

scann_cc_library(
    name = "datapoint_utils",
    hdrs = ["datapoint_utils.h"],
    deps = [
        ":reduction",
        ":types",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/proto:input_output_cc_proto",
        "//third_party/scann/scann/utils/intrinsics:flags",
    ],
)

scann_cc_library(
    name = "hwy-compact",
    srcs = ["hwy-compact.cc"],
    hdrs = ["hwy-compact.h"],
    deps = [
        ":common",
        "@abseil-cpp//absl/log:check",
        "@highway//:hwy",
    ],
)

scann_cc_library(
    name = "fast_top_neighbors",
    srcs = ["fast_top_neighbors.cc"],
    hdrs = ["fast_top_neighbors.h"],
    textual_hdrs = ["fast_top_neighbors_impl.inc"],
    deps = [
        ":bits",
        ":common",
        ":hwy-compact",
        ":types",
        ":util_functions",
        ":zip_sort",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "//third_party/scann/scann/utils/intrinsics:flags",
        "//third_party/scann/scann/utils/intrinsics:simd",
        "//third_party/scann/scann/utils/intrinsics:sse4",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/numeric:bits",
        "@abseil-cpp//absl/numeric:int128",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "gmm_utils",
    srcs = ["gmm_utils.cc"],
    hdrs = ["gmm_utils.h"],
    deps = [
        ":common",
        ":datapoint_utils",
        ":parallel_for",
        ":top_n_amortized_constant",
        ":types",
        ":util_functions",
        ":zip_sort",
        "//third_party/scann/scann/base:restrict_allowlist",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/many_to_many",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/oss_wrappers:scann_random",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "@abseil-cpp//absl/base:endian",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/numeric:bits",
        "@abseil-cpp//absl/random",
        "@abseil-cpp//absl/random:distributions",
        "@abseil-cpp//absl/time",
        "@eigen",
    ],
)

scann_cc_library(
    name = "infinite_one_array",
    hdrs = ["infinite_one_array.h"],
    deps = [":types"],
)

scann_cc_library(
    name = "bits",
    hdrs = ["bits.h"],
    deps = [
        ":types",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "@abseil-cpp//absl/numeric:bits",
    ],
)

scann_cc_library(
    name = "index_sequence",
    hdrs = ["index_sequence.h"],
)

scann_cc_library(
    name = "pca_utils",
    srcs = ["pca_utils.cc"],
    hdrs = ["pca_utils.h"],
    deps = [
        ":datapoint_utils",
        ":types",
        ":zip_sort",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "@abseil-cpp//absl/log:check",
        "@eigen",
    ],
)

scann_cc_library(
    name = "reduction",
    hdrs = ["reduction.h"],
    deps = [
        ":types",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
    ],
)

scann_cc_library(
    name = "util_functions",
    srcs = ["util_functions.cc"],
    hdrs = ["util_functions.h"],
    deps = [
        ":common",
        ":parallel_for",
        ":reduction",
        ":types",
        ":zip_sort",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:features_cc_proto",
        "//third_party/scann/scann/data_format/internal:short_string_optimized_string",
        "//third_party/scann/scann/partitioning:partitioner_cc_proto",
        "//third_party/scann/scann/proto:exact_reordering_cc_proto",
        "//third_party/scann/scann/proto:input_output_cc_proto",
        "//third_party/scann/scann/proto:results_cc_proto",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/container:flat_hash_map",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/container:node_hash_set",
        "@abseil-cpp//absl/strings",
        "@protobuf//:protobuf",
    ],
)

scann_cc_library(
    name = "scann_config_utils",
    srcs = ["scann_config_utils.cc"],
    hdrs = ["scann_config_utils.h"],
    deps = [
        ":common",
        ":types",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:features_cc_proto",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/partitioning:partitioner_cc_proto",
        "//third_party/scann/scann/proto:brute_force_cc_proto",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:exact_reordering_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/proto:incremental_updates_cc_proto",
        "//third_party/scann/scann/proto:input_output_cc_proto",
        "//third_party/scann/scann/proto:metadata_cc_proto",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/status:statusor",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/strings:str_format",
    ],
)

scann_cc_library(
    name = "threads",
    srcs = ["threads.cc"],
    hdrs = ["threads.h"],
    deps = [
        ":types",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "@abseil-cpp//absl/functional:any_invocable",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/time",
    ],
)

scann_cc_library(
    name = "memory_logging",
    srcs = ["memory_logging.cc"],
    hdrs = ["memory_logging.h"],
    deps = [
        ":types",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_malloc_extension",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/container:node_hash_set",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "bit_iterator",
    hdrs = ["bit_iterator.h"],
    deps = [
        ":types",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
    ],
)

scann_cc_library(
    name = "reordering_helper",
    srcs = ["reordering_helper.cc"],
    hdrs = ["reordering_helper.h"],
    deps = [
        ":bfloat16_helpers",
        ":common",
        ":datapoint_utils",
        ":reordering_helper_interface",
        ":scalar_quantization_helpers",
        ":types",
        ":util_functions",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/brute_force",
        "//third_party/scann/scann/brute_force:bfloat16_brute_force",
        "//third_party/scann/scann/brute_force:scalar_quantized_brute_force",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/utils/fixed_point:pre_quantized_fixed_point",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/base:prefetch",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/status:statusor",
        "@abseil-cpp//absl/synchronization",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "reordering_helper_interface",
    hdrs = ["reordering_helper_interface.h"],
    deps = [
        ":common",
        ":types",
        ":util_functions",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
    ],
)

scann_cc_library(
    name = "factory_helpers",
    srcs = ["factory_helpers.cc"],
    hdrs = ["factory_helpers.h"],
    deps = [
        ":common",
        ":types",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:exact_reordering_cc_proto",
        "//third_party/scann/scann/proto:min_distance_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
    ],
)

scann_cc_library(
    name = "hash_leaf_helpers",
    srcs = ["hash_leaf_helpers.cc"],
    hdrs = ["hash_leaf_helpers.h"],
    deps = [
        ":factory_helpers",
        ":types",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:indexing",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:querying",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:searcher",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_options",
        "//third_party/scann/scann/projection:projection_factory",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "@abseil-cpp//absl/synchronization",
    ],
)

scann_cc_library(
    name = "io_oss_wrapper",
    srcs = ["io_oss_wrapper.cc"],
    hdrs = ["io_oss_wrapper.h"],
    deps = [
        ":common",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "@abseil-cpp//absl/strings:string_view",
        "@protobuf//:protobuf",
    ],
)

scann_cc_library(
    name = "io_npy",
    srcs = ["io_npy.cc"],
    hdrs = ["io_npy.h"],
    deps = [
        ":common",
        ":io_oss_wrapper",
        ":types",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "@cnpy",
        "@abseil-cpp//absl/strings:str_format",
        "@abseil-cpp//absl/strings:string_view",
    ],
)

scann_cc_library(
    name = "input_data_utils",
    srcs = ["input_data_utils.cc"],
    hdrs = ["input_data_utils.h"],
    deps = [
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils/fixed_point:pre_quantized_fixed_point",
    ],
)

scann_cc_library(
    name = "zip_sort",
    hdrs = [
        "zip_sort.h",
        "zip_sort_impl.h",
    ],
    deps = [
        ":types",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "@abseil-cpp//absl/base:prefetch",
    ],
)

scann_cc_library(
    name = "top_n_amortized_constant",
    srcs = ["top_n_amortized_constant.cc"],
    hdrs = ["top_n_amortized_constant.h"],
    deps = [
        ":types",
        ":util_functions",
        ":zip_sort",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "parallel_for",
    hdrs = ["parallel_for.h"],
    deps = [
        ":common",
        ":threads",
        ":types",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/functional:any_invocable",
        "@abseil-cpp//absl/functional:function_ref",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/synchronization",
    ],
)

scann_cc_library(
    name = "dataset_sampling",
    hdrs = [
        "dataset_sampling.h",
        "sampled_index_list.h",
    ],
    deps = [
        ":types",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_random",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/random",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/types:variant",
    ],
)

scann_cc_library(
    name = "iterators",
    hdrs = ["iterators.h"],
    deps = [
        ":types",
    ],
)

scann_cc_library(
    name = "scalar_quantization_helpers",
    srcs = ["scalar_quantization_helpers.cc"],
    hdrs = ["scalar_quantization_helpers.h"],
    deps = [
        ":common",
        ":noise_shaping_utils",
        ":parallel_for",
        ":top_n_amortized_constant",
        ":types",
        ":util_functions",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "bfloat16_helpers",
    srcs = ["bfloat16_helpers.cc"],
    hdrs = ["bfloat16_helpers.h"],
    deps = [
        ":common",
        ":noise_shaping_utils",
        ":types",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
    ],
)

# Binaries
# =========================================================================

# Tests
# =========================================================================

scann_cc_library(
    name = "common",
    srcs = ["common.cc"],
    hdrs = ["common.h"],
    deps = [
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_serialize",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/base:prefetch",
        "@abseil-cpp//absl/container:flat_hash_map",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/container:node_hash_map",
        "@abseil-cpp//absl/container:node_hash_set",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/memory",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/status:statusor",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/strings:str_format",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "noise_shaping_utils",
    hdrs = ["noise_shaping_utils.h"],
    deps = [
        ":datapoint_utils",
        ":types",
        "//third_party/scann/scann/data_format:datapoint",
    ],
)

scann_cc_library(
    name = "weak_ptr_cache",
    hdrs = ["weak_ptr_cache.h"],
    deps = [
        ":types",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/numeric:int128",
        "@abseil-cpp//absl/synchronization",
    ],
)

scann_cc_library(
    name = "single_machine_retraining",
    srcs = ["single_machine_retraining.cc"],
    hdrs = ["single_machine_retraining.h"],
    deps = [
        ":common",
        ":scann_config_utils",
        ":types",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/base:single_machine_factory_scann",
        "//third_party/scann/scann/base/internal:single_machine_factory_impl",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/synchronization",
    ],
)

scann_cc_library(
    name = "single_machine_autopilot",
    srcs = ["single_machine_autopilot.cc"],
    hdrs = ["single_machine_autopilot.h"],
    deps = [
        ":common",
        ":types",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/proto:auto_tuning_cc_proto",
        "//third_party/scann/scann/proto:distance_measure_cc_proto",
        "//third_party/scann/scann/proto:exact_reordering_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/proto:scann_cc_proto",
        "@abseil-cpp//absl/log",
    ],
)

# Builds libraries for ahead-of-time fixed-point preprocessing.
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "pre_quantized_fixed_point",
    hdrs = ["pre_quantized_fixed_point.h"],
    deps = [
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/types:span",
    ],
)

# Description:
#   AVX utility function classes.
load("//third_party/scann:scann.bzl", "scann_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "avx_funcs",
    hdrs = ["avx_funcs.h"],
    deps = [
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:avx1",
    ],
)

scann_cc_library(
    name = "avx2_funcs",
    hdrs = ["avx2_funcs.h"],
    deps = [
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:avx2",
    ],
)

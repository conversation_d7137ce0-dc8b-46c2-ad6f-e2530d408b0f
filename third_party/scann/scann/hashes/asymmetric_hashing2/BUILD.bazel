load("//third_party/scann:scann.bzl", "scann_cc_library")

# Description:
#   Version 2 of the asymmetric hashing public API.

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

# Libraries
##########################################################################

# If only indexing is required, we should not need to link to training, but
# only its headers.
scann_cc_library(
    name = "training_options_base",
    srcs = ["training_options_base.cc"],
    hdrs = ["training_options_base.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/projection:chunking_projection",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "training_options",
    srcs = ["training_options.cc"],
    hdrs = ["training_options.h"],
    deps = [
        ":training_options_base",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/projection:chunking_projection",
        "//third_party/scann/scann/projection:projection_factory",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "training_model",
    srcs = ["training_model.cc"],
    hdrs = ["training_model.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/projection:chunking_projection",
        "//third_party/scann/scann/projection:projection_factory",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/proto:projection_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "training",
    hdrs = ["training.h"],
    deps = [
        ":training_model",
        ":training_options",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/hashes/internal:asymmetric_hashing_impl",
        "//third_party/scann/scann/hashes/internal:stacked_quantizers",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
    ],
)

scann_cc_library(
    name = "indexing",
    srcs = ["indexing.cc"],
    hdrs = ["indexing.h"],
    deps = [
        ":training_model",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/distance_measures/one_to_one:l1_distance",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/hashes/internal:asymmetric_hashing_impl",
        "//third_party/scann/scann/hashes/internal:stacked_quantizers",
        "//third_party/scann/scann/oss_wrappers:scann_serialize",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/projection:chunking_projection",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "querying",
    srcs = ["querying.cc"],
    hdrs = ["querying.h"],
    deps = [
        ":training_model",
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/hashes/internal:asymmetric_hashing_impl",
        "//third_party/scann/scann/hashes/internal:asymmetric_hashing_lut16",
        "//third_party/scann/scann/hashes/internal:asymmetric_hashing_postprocess",
        "//third_party/scann/scann/hashes/internal:lut16_args",
        "//third_party/scann/scann/hashes/internal:lut16_interface",
        "//third_party/scann/scann/projection:chunking_projection",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/restricts:restrict_allowlist",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "@abseil-cpp//absl/status",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/types:span",
    ],
)

scann_cc_library(
    name = "searcher",
    srcs = [
        "searcher.cc",
        "searcher_mutator.cc",
    ],
    hdrs = ["searcher.h"],
    deps = [
        ":indexing",
        ":querying",
        ":serialization",
        ":training",
        "//third_party/scann/scann/base:search_parameters",
        "//third_party/scann/scann/base:single_machine_base",
        "//third_party/scann/scann/base:single_machine_factory_options",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/hashes/internal:asymmetric_hashing_postprocess",
        "//third_party/scann/scann/oss_wrappers:scann_serialize",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
        "//third_party/scann/scann/tree_x_hybrid:leaf_searcher_optional_parameter_creator",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "//third_party/scann/scann/utils/intrinsics:flags",
        "@abseil-cpp//absl/base:nullability",
        "@abseil-cpp//absl/strings",
    ],
)

scann_cc_library(
    name = "serialization",
    hdrs = ["serialization.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/proto:centers_cc_proto",
        "//third_party/scann/scann/proto:hash_cc_proto",
    ],
)

# Tests
##########################################################################

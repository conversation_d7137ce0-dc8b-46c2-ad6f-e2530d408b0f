load("//third_party/scann:scann.bzl", "scann_cc_library")
# Description:
#   Low-level implementation of asymmetric hashing.  These are used by
#   both the old, soon-to-be-deprecated API and the new, much more modular
#   API.

load(":template_sharding.bzl", "batch_size_sharder")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

scann_cc_library(
    name = "asymmetric_hashing_impl_omit_frame_pointer",
    srcs = ["asymmetric_hashing_impl_omit_frame_pointer.cc"],
    hdrs = ["asymmetric_hashing_impl.h"],
    copts = [
        "-fomit-frame-pointer",
    ],
    deps = [
        ":asymmetric_hashing_postprocess",
        "//third_party/scann/scann/base:restrict_allowlist",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_options_base",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
    ],
)

scann_cc_library(
    name = "asymmetric_hashing_impl",
    srcs = ["asymmetric_hashing_impl.cc"],
    hdrs = ["asymmetric_hashing_impl.h"],
    deps = [
        ":asymmetric_hashing_impl_omit_frame_pointer",
        ":asymmetric_hashing_postprocess",
        "//third_party/scann/scann/base:restrict_allowlist",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_options_base",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_random",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/projection:chunking_projection",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:gmm_utils",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/random",
        "@abseil-cpp//absl/random:distributions",
        "@abseil-cpp//absl/status",
    ],
    # The linker gets confused by explicitly instantiated templates  This
    # probably shouldn't be needed but is empirically necessary to get
    # in_memory_ah_benchmark to link in non-optimized mode.
    alwayslink = 1,
)

batch_size_sharder(
    name = "lut16_sse4_batches",
    max_batch_size = 9,
    template = "bazel_templates/lut16_sse4.tpl.cc",
)

scann_cc_library(
    name = "lut16_sse4",
    srcs = [":lut16_sse4_batches"],
    hdrs = ["lut16_sse4.h"],
    # We build this library at -O3 even for fastbuild and dbg
    # builds.  With inlining, functions in this library generate
    # enormous stack frames in debug mode, causing stack overflows
    # in unit tests.  Forcing -O3 forces stack slot recycling.
    copts = [
        "-O3",
    ],
    textual_hdrs = ["lut16_sse4.inc"],
    deps = [
        ":lut16_args",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/utils:bits",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "//third_party/scann/scann/utils/intrinsics:sse4",
        "@abseil-cpp//absl/base:prefetch",
    ],
)

batch_size_sharder(
    name = "lut16_avx2_batches",
    max_batch_size = 9,
    template = "bazel_templates/lut16_avx2.tpl.cc",
)

scann_cc_library(
    name = "lut16_avx2",
    srcs = [":lut16_avx2_batches"],
    hdrs = ["lut16_avx2.h"],
    # We build this library at -O3 even for fastbuild and dbg
    # builds.  With inlining, functions in this library generate
    # enormous stack frames in debug mode, causing stack overflows
    # in unit tests.  Forcing -O3 forces stack slot recycling.
    copts = [
        "-O3",
    ],
    textual_hdrs = ["lut16_avx2.inc"],
    deps = [
        ":lut16_args",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/utils:bits",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "//third_party/scann/scann/utils/intrinsics:avx2",
        "@abseil-cpp//absl/base:prefetch",
    ],
)

batch_size_sharder(
    name = "lut16_avx512_prefetch",
    max_batch_size = 9,
    template = "bazel_templates/lut16_avx512_prefetch.tpl.cc",
)

batch_size_sharder(
    name = "lut16_avx512_smart",
    max_batch_size = 9,
    template = "bazel_templates/lut16_avx512_smart.tpl.cc",
)

batch_size_sharder(
    name = "lut16_avx512_noprefetch",
    max_batch_size = 9,
    template = "bazel_templates/lut16_avx512_noprefetch.tpl.cc",
)

scann_cc_library(
    name = "lut16_avx512",
    srcs = [
        "lut16_avx512_swizzle.cc",
        ":lut16_avx512_noprefetch",
        ":lut16_avx512_prefetch",
        ":lut16_avx512_smart",
    ],
    hdrs = [
        "lut16_avx512.h",
        "lut16_avx512_swizzle.h",
    ],
    # We build this library at -O3 even for fastbuild and dbg
    # builds.  With inlining, functions in this library generate
    # enormous stack frames in debug mode, causing stack overflows
    # in unit tests.  Forcing -O3 forces stack slot recycling.
    copts = [
        "-O3",
    ],
    textual_hdrs = ["lut16_avx512.inc"],
    deps = [
        ":lut16_args",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/utils:bits",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "//third_party/scann/scann/utils/intrinsics:avx512",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:prefetch",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
    ],
)

batch_size_sharder(
    name = "lut16_highway_batches",
    max_batch_size = 9,
    template = "bazel_templates/lut16_highway.tpl.cc",
)

scann_cc_library(
    name = "lut16_highway",
    srcs = [":lut16_highway_batches"],
    hdrs = ["lut16_highway.h"],
    # We build this library at -O3 even for fastbuild and dbg
    # builds.  With inlining, functions in this library generate
    # enormous stack frames in debug mode, causing stack overflows
    # in unit tests.  Forcing -O3 forces stack slot recycling.
    copts = [
        "-O3",
    ],
    textual_hdrs = ["lut16_highway.inc"],
    deps = [
        ":lut16_args",
        "//third_party/scann/scann/oss_wrappers:scann_bits",
        "//third_party/scann/scann/utils:bits",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils/intrinsics:attributes",
        "//third_party/scann/scann/utils/intrinsics:highway",
        "@abseil-cpp//absl/base:prefetch",
        "@highway//:hwy",
    ],
)

scann_cc_library(
    name = "write_distances_to_topn",
    srcs = ["write_distances_to_topn.cc"],
    hdrs = ["write_distances_to_topn.h"],
    deps = [
        ":asymmetric_hashing_postprocess",
        "//third_party/scann/scann/base:restrict_allowlist",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:sse4",
    ],
)

scann_cc_library(
    name = "asymmetric_hashing_lut16",
    hdrs = ["asymmetric_hashing_lut16.h"],
    deps = [
        ":asymmetric_hashing_postprocess",
        ":lut16_interface",
        ":write_distances_to_topn",
        "//third_party/scann/scann/base:restrict_allowlist",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/utils:top_n_amortized_constant",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "asymmetric_hashing_postprocess",
    hdrs = ["asymmetric_hashing_postprocess.h"],
    deps = [
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
    ],
)

scann_cc_library(
    name = "lut16_args",
    hdrs = ["lut16_args.h"],
    deps = [
        "//third_party/scann/scann/base:restrict_allowlist",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
    ],
)

scann_cc_library(
    name = "lut16_interface",
    srcs = ["lut16_interface.cc"],
    hdrs = ["lut16_interface.h"],
    deps = [
        ":lut16_args",
        ":lut16_avx2",
        ":lut16_avx512",
        ":lut16_highway",
        ":lut16_sse4",
        "//third_party/scann/scann/utils:alignment",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils/intrinsics:flags",
    ],
)

scann_cc_library(
    name = "stacked_quantizers",
    srcs = ["stacked_quantizers.cc"],
    hdrs = ["stacked_quantizers.h"],
    deps = [
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/data_format:docid_collection",
        "//third_party/scann/scann/distance_measures",
        "//third_party/scann/scann/distance_measures/many_to_many:many_to_many_floating_point",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:dot_product",
        "//third_party/scann/scann/hashes/asymmetric_hashing2:training_options_base",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:dataset_sampling",
        "//third_party/scann/scann/utils:gmm_utils",
        "//third_party/scann/scann/utils:types",
        "@abseil-cpp//absl/log",
    ],
)

load("//third_party/scann/scann/oss_wrappers:build_defs.bzl", "scann_py_proto_library")
load("//tools/bzl:cc.bzl", "cc_proto_library")

#
package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)
load("//third_party/scann:scann.bzl", "scann_cc_library")

# Protos
# =========================================================

proto_library(
    name = "kmeans_tree_proto",
    srcs = ["kmeans_tree.proto"],
    deps = ["//third_party/scann/scann/proto:partitioning_proto"],
)

cc_proto_library(
    name = "kmeans_tree_cc_proto",
    deps = [":kmeans_tree_proto"],
    extra_deps = ["//third_party/scann/scann/proto:partitioning_cc_proto"],
)

scann_py_proto_library(
    name = "kmeans_tree_py_pb2",
    srcs = ["kmeans_tree.proto"],
    proto_deps = [":kmeans_tree_proto"],
    py_proto_deps = [
        "//third_party/scann/scann/proto:partitioning_py_pb2",
    ],
)

# Library
# =========================================================
scann_cc_library(
    name = "kmeans_tree",
    srcs = [
        "kmeans_tree.cc",
        "kmeans_tree_node.cc",
        "training_options.cc",
    ],
    hdrs = [
        "kmeans_tree.h",
        "kmeans_tree_node.h",
        "training_options.h",
    ],
    deps = [
        ":kmeans_tree_cc_proto",
        "//third_party/scann/scann/data_format:datapoint",
        "//third_party/scann/scann/data_format:dataset",
        "//third_party/scann/scann/distance_measures:distance_measure_base",
        "//third_party/scann/scann/distance_measures/one_to_many",
        "//third_party/scann/scann/distance_measures/one_to_one:l2_distance",
        "//third_party/scann/scann/oss_wrappers:scann_aligned_malloc",
        "//third_party/scann/scann/oss_wrappers:scann_castops",
        "//third_party/scann/scann/oss_wrappers:scann_down_cast",
        "//third_party/scann/scann/oss_wrappers:scann_random",
        "//third_party/scann/scann/oss_wrappers:scann_status",
        "//third_party/scann/scann/oss_wrappers:scann_threadpool",
        "//third_party/scann/scann/partitioning:anisotropic",
        "//third_party/scann/scann/proto:partitioning_cc_proto",
        "//third_party/scann/scann/utils:common",
        "//third_party/scann/scann/utils:datapoint_utils",
        "//third_party/scann/scann/utils:fast_top_neighbors",
        "//third_party/scann/scann/utils:gmm_utils",
        "//third_party/scann/scann/utils:scalar_quantization_helpers",
        "//third_party/scann/scann/utils:types",
        "//third_party/scann/scann/utils:util_functions",
        "//third_party/scann/scann/utils:zip_sort",
        "@abseil-cpp//absl/base",
        "@abseil-cpp//absl/base:core_headers",
        "@abseil-cpp//absl/container:flat_hash_set",
        "@abseil-cpp//absl/flags:flag",
        "@abseil-cpp//absl/log",
        "@abseil-cpp//absl/log:check",
        "@abseil-cpp//absl/random",
        "@abseil-cpp//absl/strings",
        "@abseil-cpp//absl/time",
    ],
)

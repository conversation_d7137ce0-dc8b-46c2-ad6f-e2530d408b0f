// Copyright 2024 The Google Research Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package research_scann;

import "third_party/scann/scann/proto/partitioning.proto";

message SerializedKMeansTree {
  message Center {
    repeated double dimension = 1 [packed = true];

    repeated float float_dimension = 2 [packed = true];
  }

  message Node {
    repeated Center centers = 1;

    repeated uint32 indices = 2;

    repeated Node children = 3;

    optional double learned_spilling_threshold = 4;

    optional int32 leaf_id = 5 [default = -1];

    repeated double residual_stdevs = 6 [packed = true, deprecated = true];
  }

  optional Node root = 1;

  optional DatabaseSpillingConfig.SpillingType learned_spilling_type = 3
      [default = NO_SPILLING];

  optional uint32 max_centers_for_learned_spilling = 4 [default = **********];
}

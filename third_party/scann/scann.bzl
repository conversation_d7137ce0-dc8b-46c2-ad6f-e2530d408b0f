load("@rules_cc//cc:defs.bzl", "cc_library")

def scann_cc_library(name, copts = [], **kwargs):
    cc_library(
        name = name,
        # silence scann compiler warnings
        copts = copts + [
            "-Wno-implicit-const-int-float-conversion",
            "-Wno-deprecated-declarations",
            "-Wno-unused-private-field",
            "-Wno-c++20-extensions",
            "-Wno-final-dtor-non-final-class",
            "-Wno-macro-redefined",
            "-Wno-potentially-evaluated-expression",
            "-Wno-mismatched-tags",
            "-Wno-unused-lambda-capture",
            "-Wno-enum-compare-switch",
            "-Wno-unused-variable",
            "-Wno-inconsistent-missing-override",
            "-Wno-unused-private-field",
            "-Wno-final-dtor-non-final-class",
            "-Wno-unqualified-std-cast-call",
        ],
        **kwargs
    )

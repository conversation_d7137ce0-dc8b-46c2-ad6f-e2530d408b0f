[package]
name = "custom-root-span"
version = "0.1.0"
authors = ["LukeMathWalker <<EMAIL>>"]
edition = "2018"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-web = "4"
opentelemetry = { version = "0.19", features = ["rt-tokio-current-thread"] }
opentelemetry-jaeger = { version = "0.18", features = ["rt-tokio-current-thread"] }
tracing-opentelemetry = { version = "0.19" }
tracing = "0.1.19"
tracing-subscriber = { version = "0.3", features = ["registry", "env-filter"] }
tracing-bunyan-formatter = "0.3"
tracing-actix-web = { path = "../.." }

[package]
name = "tracing-actix-web"
version = "0.7.5"
authors = ["<PERSON> <<EMAIL>>"]
edition = "2018"

license = "MIT/Apache-2.0"

repository = "https://github.com/LukeMathWalker/tracing-actix-web"
documentation = "https://docs.rs/tracing-actix-web/"
readme = "README.md"

description = "Structured logging middleware for actix-web."

keywords = ["http", "actix-web", "tracing", "logging"]
categories = ["asynchronous", "web-programming"]

[features]
default = ["emit_event_on_error"]
opentelemetry_0_13 = ["opentelemetry_0_13_pkg", "tracing-opentelemetry_0_12_pkg"]
opentelemetry_0_14 = ["opentelemetry_0_14_pkg", "tracing-opentelemetry_0_13_pkg"]
opentelemetry_0_15 = ["opentelemetry_0_15_pkg", "tracing-opentelemetry_0_14_pkg"]
opentelemetry_0_16 = ["opentelemetry_0_16_pkg", "tracing-opentelemetry_0_16_pkg"]
opentelemetry_0_17 = ["opentelemetry_0_17_pkg", "tracing-opentelemetry_0_17_pkg"]
opentelemetry_0_18 = ["opentelemetry_0_18_pkg", "tracing-opentelemetry_0_18_pkg"]
opentelemetry_0_19 = ["opentelemetry_0_19_pkg", "tracing-opentelemetry_0_19_pkg"]
emit_event_on_error = []

[dependencies]
actix-web = { version = "4", default-features = false }
pin-project = "1.0.0"
tracing = "0.1.36"
uuid = { version = "1", features = ["v4"] }
opentelemetry_0_13_pkg = { package = "opentelemetry", version = "0.13", optional = true }
opentelemetry_0_14_pkg = { package = "opentelemetry", version = "0.14", optional = true }
opentelemetry_0_15_pkg = { package = "opentelemetry", version = "0.15", optional = true }
opentelemetry_0_16_pkg = { package = "opentelemetry", version = "0.16", optional = true }
opentelemetry_0_17_pkg = { package = "opentelemetry", version = "0.17", optional = true }
opentelemetry_0_18_pkg = { package = "opentelemetry", version = "0.18", optional = true }
opentelemetry_0_19_pkg = { package = "opentelemetry", version = "0.19", optional = true }
tracing-opentelemetry_0_12_pkg = { package = "tracing-opentelemetry",version = "0.12", optional = true }
tracing-opentelemetry_0_13_pkg = { package = "tracing-opentelemetry", version = "0.13", optional = true }
tracing-opentelemetry_0_14_pkg = { package = "tracing-opentelemetry",version = "0.14", optional = true }
tracing-opentelemetry_0_16_pkg = { package = "tracing-opentelemetry",version = "0.16", optional = true }
tracing-opentelemetry_0_17_pkg = { package = "tracing-opentelemetry",version = "0.17", optional = true }
tracing-opentelemetry_0_18_pkg = { package = "tracing-opentelemetry",version = "0.18", optional = true }
tracing-opentelemetry_0_19_pkg = { package = "tracing-opentelemetry",version = "0.19", optional = true }

[dev-dependencies]
actix-web = { version = "4", default-features = false, features = ["macros"] }
tracing-subscriber = { version = "0.3.0", features = ["registry", "env-filter"] }
tracing-bunyan-formatter = "0.3.0"
tracing-log = "0.1.1"

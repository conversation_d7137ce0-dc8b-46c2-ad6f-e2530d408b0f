package(
    default_visibility = ["//visibility:public"],
)

cc_library(
    name = "cuda_headers",
    deps = [
        "@cuda_cudart-linux-x86_64//:hdrs",
        "@cuda_nvcc-linux-x86_64//:hdrs",
    ],
)

cc_library(
    name = "cuda_stub",
    srcs = ["@cuda_cudart-linux-x86_64//:lib/stubs/libcuda.so"],
    linkopts = [
        "-ldl",
        "-lpthread",
        "-lrt",
    ],
)

cc_import(
    name = "cudart_so",
    shared_library = "@cuda_cudart-linux-x86_64//:lib/libcudart.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cudart_12_so",
    shared_library = "@cuda_cudart-linux-x86_64//:lib/libcudart.so.12",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "curand_so",
    shared_library = "@libcurand-linux-x86_64//:lib/libcurand.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "curand_10_so",
    shared_library = "@libcurand-linux-x86_64//:lib/libcurand.so.10",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cuda_nvprof_so",
    shared_library = "@cuda_nvprof-linux-x86_64//:lib/libcuinj64.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cuda_nvprof_12_1_so",
    shared_library = "@cuda_nvprof-linux-x86_64//:lib/libcuinj64.so.12.1",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cupti_so",
    shared_library = "@cuda_cupti-linux-x86_64//:lib/libcupti.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cupti_12_so",
    shared_library = "@cuda_cupti-linux-x86_64//:lib/libcupti.so.12",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "libcusparse_so",
    shared_library = "@libcusparse-linux-x86_64//:lib/libcusparse.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "libcusparse_12_so",
    shared_library = "@libcusparse-linux-x86_64//:lib/libcusparse.so.12",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "libcusolver_so",
    shared_library = "@libcusolver-linux-x86_64//:lib/libcusolver.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "libcusolver_11_so",
    shared_library = "@libcusolver-linux-x86_64//:lib/libcusolver.so.11",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_library(
    name = "cudadevrt_a",
    srcs = ["@cuda_cudart-linux-x86_64//:lib/libcudadevrt.a"],
    target_compatible_with = ["@platforms//os:linux"],
    alwayslink = 1,
)

# Note: do not use this target directly, use the configurable label_flag
# @rules_cuda//cuda:runtime instead.
cc_library(
    name = "cuda_runtime",
    linkopts = [
        "-ldl",
        "-lpthread",
        "-lrt",
    ],
    deps = [
        "@cuda_cudart-linux-x86_64//:hdrs",
        "@cuda_nvcc-linux-x86_64//:hdrs",
    ] + [
        # devrt is require for jit linking when rdc is enabled
        ":cudadevrt_a",
        ":cudart_so",
        ":cudart_12_so",
    ],
    alwayslink = 1,
)

# Note: do not use this target directly, use the configurable label_flag
# @rules_cuda//cuda:runtime instead.
cc_library(
    name = "cuda_runtime_static",
    srcs = ["@cuda_cudart-linux-x86_64//:lib/libcudart_static.a"],
    linkopts = [
        "-ldl",
        "-lpthread",
        "-lrt",
    ],
    deps = [
        ":cuda_headers",
        ":cudadevrt_a",
    ],
      target_compatible_with = ["@platforms//os:linux"],
)

cc_library(
    name = "no_cuda_runtime",
)

cc_import(
    name = "cuda_so",
    shared_library = "@cuda_cudart-linux-x86_64//:lib/stubs/libcuda.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cuda_lib",
    interface_library = "cuda/lib/x64/cuda.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "cuda",
    deps = [
        ":cuda_headers",
    ] + [
        ":cuda_so",
    ],
)

cc_import(
    name = "cublas_so",
    shared_library = "@libcublas-linux-x86_64//:lib/libcublas.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cublas_12_so",
    shared_library = "@libcublas-linux-x86_64//:lib/libcublas.so.12",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cublasLt_so",
    shared_library = "@libcublas-linux-x86_64//:lib/libcublasLt.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cublasLt_12_so",
    shared_library = "@libcublas-linux-x86_64//:lib/libcublasLt.so.12",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvjitlink_so",
    shared_library = "@libnvjitlink-linux-x86_64//:lib/libnvJitLink.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvjitlink_12_so",
    shared_library = "@libnvjitlink-linux-x86_64//:lib/libnvJitLink.so.12",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvjitlink_12_4_127_so",
    shared_library = "@libnvjitlink-linux-x86_64//:lib/libnvJitLink.so.12.4.127",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_library(
    name = "cublas",
    deps = [
        ":cuda_runtime",
        "@libcublas-linux-x86_64//:hdrs",
    ] + [
        ":cublasLt_12_so",
        ":cublasLt_so",
        ":cublas_12_so",
        ":cublas_so",
    ],
)

cc_library(
    name = "cuda_profiler_api",
    deps = [
        ":cuda_runtime",
        "@cuda_profiler_api-linux-x86_64//:hdrs",
    ],
)

cc_library(
    name = "cupti_headers",
    hdrs = glob(["cuda/extras/CUPTI/include/*.h"]),
    includes = ["cuda/extras/CUPTI/include"],
)

# nvperf
cc_import(
    name = "nvperf_host_so",
    shared_library = "cuda/lib64/libnvperf_host.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvperf_host_lib",
    interface_library = "cuda/extras/CUPTI/lib64/nvperf_host.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "nvperf_host",
    deps = [
        ":cuda_headers",
    ] + [
        ":nvperf_host_so",
    ],
)

cc_import(
    name = "nvperf_target_so",
    shared_library = "cuda/lib64/libnvperf_target.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvperf_target_lib",
    interface_library = "cuda/extras/CUPTI/lib64/nvperf_target.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "nvperf_target",
    deps = [
        ":cuda_headers",
    ] + [
        ":nvperf_target_so",
    ],
)

# NVML
cc_import(
    name = "nvidia-ml_so",
    shared_library = "cuda/lib64/stubs/libnvidia-ml.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvml_lib",
    interface_library = "cuda/lib/x64/nvml.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "nvml",
    deps = [
        ":cuda_headers",
    ] + [
        ":nvidia-ml_so",
    ],
)

# curand
cc_library(
    name = "curand",
    deps = [
        "@libcurand-linux-x86_64//:hdrs",
    ] + [
        ":curand_10_so",
        ":curand_so",
    ],
)

# curand
cc_library(
    name = "cuda_nvprof",
    deps = [
        "@cuda_nvprof-linux-x86_64//:hdrs",
    ] + [
        ":cuda_nvprof_12_1_so",
        ":cuda_nvprof_so",
        ":cuda_profiler_api",
    ],
)

cc_library(
    name = "libcusparse",
    deps = [
        "@libcusparse-linux-x86_64//:hdrs",
    ] + [
        ":libcusparse_12_so",
        ":libcusparse_so",
    ],
)

cc_library(
    name = "libcusolver",
    deps = [
        "@libcusolver-linux-x86_64//:hdrs",
    ] + [
        ":libcusolver_11_so",
        ":libcusolver_so",
    ],
)

# curand
cc_library(
    name = "cupti",
    deps = [
        "@cuda_cupti-linux-x86_64//:hdrs",
    ] + [
        ":cupti_12_so",
        ":cupti_so",
    ],
)

# nvptxcompiler
cc_import(
    name = "nvptxcompiler_so",
    static_library = "cuda/lib64/libnvptxcompiler_static.a",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvptxcompiler_lib",
    interface_library = "cuda/lib/x64/nvptxcompiler_static.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "nvptxcompiler",
    srcs = [],
    hdrs = glob([
        "cuda/include/fatbinary_section.h",
        "cuda/include/nvPTXCompiler.h",
        "cuda/include/crt/*",
    ]),
    includes = [
        "cuda/include",
    ],
    visibility = ["//visibility:public"],
    deps = [] + [
        ":nvptxcompiler_so",
    ],
)

# cufft
cc_import(
    name = "cufft_so",
    shared_library = "cuda/lib64/libcufft.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cufft_lib",
    interface_library = "cuda/lib/x64/cufft.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_import(
    name = "cufftw_so",
    shared_library = "cuda/lib64/libcufftw.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cufftw_lib",
    interface_library = "cuda/lib/x64/cufftw.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "cufft",
    deps = [
        ":cuda_headers",
    ] + [
        ":cufft_so",
        ":cufftw_so",
    ],
)

# cusolver
cc_library(
    name = "cusolver",
    deps = [
        "@cuda_nvprof-linux-x86_64//:hdrs",
    ] + [
        ":cusolver_so",
    ],
)

# cusparse
cc_import(
    name = "cusparse_so",
    shared_library = "cuda/lib64/libcusparse.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "cusparse_lib",
    interface_library = "cuda/lib/x64/cusparse.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "cusparse",
    deps = [
        ":cuda_headers",
    ] + [
        ":cusparse_so",
    ],
)

# nvtx
cc_import(
    name = "nvtx_so",
    shared_library = "cuda/lib64/libnvToolsExt.so",
    target_compatible_with = ["@platforms//os:linux"],
)

cc_import(
    name = "nvtx_lib",
    interface_library = "cuda/lib/x64/libnvToolsExt.lib",
    target_compatible_with = ["@platforms//os:windows"],
)

cc_library(
    name = "nvtx",
    deps = [
        ":cuda_headers",
    ] + [
        ":nvtx_so",
    ],
)

{"release_date": "2024-04-03", "release_label": "12.4.1", "release_product": "cuda", "cuda_cccl": {"name": "CXX Core Compute Libraries", "license": "CUDA Toolkit", "license_path": "cuda_cccl/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_cccl/linux-x86_64/cuda_cccl-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "e1636f27a142d24e73dfd831c54bbf5575b498fd5900648d7372fae46f824fdf", "md5": "70fcec0e14bd2e47d0758425695bf55c", "size": "1157180"}, "linux-ppc64le": {"relative_path": "cuda_cccl/linux-ppc64le/cuda_cccl-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "3304e563ed089be1129f79d8e45c9badc8eeba155c8b672f9659f223494edcd9", "md5": "c70add0951817bb00664fbf7c0c7f897", "size": "1157244"}, "linux-sbsa": {"relative_path": "cuda_cccl/linux-sbsa/cuda_cccl-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "171073fd6557360b9db7f8559e17b1bb55679aadd5158681318ed9be67e54667", "md5": "305d1dfe78ba55728ca1afe0759f2842", "size": "1156520"}, "windows-x86_64": {"relative_path": "cuda_cccl/windows-x86_64/cuda_cccl-windows-x86_64-12.4.127-archive.zip", "sha256": "908742d8f3c6fdd6d1d6316a7b919b5c506474f9551f491aa7335cb4f50bffbd", "md5": "93501754092630bf266b2a26e80c5385", "size": "3171223"}, "linux-aarch64": {"relative_path": "cuda_cccl/linux-aarch64/cuda_cccl-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "23ddd81e43f1522247fcdc1948f3473d2f474a048527c1f20fb3bec345807e2c", "md5": "ab7f4b2c99f5af27f006669706b16969", "size": "1157400"}}, "cuda_compat": {"name": "CUDA compat L4T", "license": "CUDA Toolkit", "license_path": "cuda_compat/LICENSE.txt", "version": "12.4.35753180", "linux-aarch64": {"relative_path": "cuda_compat/linux-aarch64/cuda_compat-linux-aarch64-12.4.35753180-archive.tar.xz", "sha256": "7b8a09396c61ccf94a55a418c6a16e371f4e8b9fc4bdae7e2c33cbabcb7a97f3", "md5": "91bb5bc8c790d31cb43fa2cca1b690d6", "size": "19191916"}}, "cuda_cudart": {"name": "CUDA Runtime (cudart)", "license": "CUDA Toolkit", "license_path": "cuda_cudart/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_cudart/linux-x86_64/cuda_cudart-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "0483bff9a36e7a44465db3cd42874f6f70f019297dcf803fbefcbf58d7448c8f", "md5": "5c452d73cb03a42c1711f8655fa2fb8a", "size": "1099680"}, "linux-ppc64le": {"relative_path": "cuda_cudart/linux-ppc64le/cuda_cudart-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "f5e636944c7817b4f178070daa1259f25b3e84ebd092305d32aa189b21ae37e3", "md5": "1400b00bf1ee2d51eff22ab82f516cd3", "size": "1077184"}, "linux-sbsa": {"relative_path": "cuda_cudart/linux-sbsa/cuda_cudart-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "fb96abcff3544384440b9259f9aeab9bf222a5775348546ef87c68ee02eee379", "md5": "bb5fdb01994b0aaf329fb8c7cd825750", "size": "1090256"}, "windows-x86_64": {"relative_path": "cuda_cudart/windows-x86_64/cuda_cudart-windows-x86_64-12.4.127-archive.zip", "sha256": "6a1c32e68ee1a95ca17334691ff9ad1ffe7f352c24a083d55e4c96b8063b2bcb", "md5": "9b8b0f24f6b0777ef3b0823f8a0ff2bb", "size": "2474721"}, "linux-aarch64": {"relative_path": "cuda_cudart/linux-aarch64/cuda_cudart-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "0fc5240b1c2a27169b3fefb1e7390cdb0177cb86469fd545f580cd4cc69c7fde", "md5": "cf6075294bf72afdb93e45a75a5d0797", "size": "1149332"}}, "cuda_cuobjdump": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "CUDA Toolkit", "license_path": "cuda_cuobjdump/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_cuobjdump/linux-x86_64/cuda_cuobjdump-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "ae4f9bcb5333b78a84a3babe23feeb8d48c00b6faa21b31477ce4c19f22f39a4", "md5": "10c7a28d9de1ea74fca768694afebb81", "size": "222176"}, "linux-ppc64le": {"relative_path": "cuda_cuobjdump/linux-ppc64le/cuda_cuobjdump-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "f80c713d690d4f0eef21648d45e47b6de08212a5b3291747f35950564fb37196", "md5": "45b83401550b4afead5609244c9c3e9f", "size": "262464"}, "linux-sbsa": {"relative_path": "cuda_cuobjdump/linux-sbsa/cuda_cuobjdump-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "d9076a8261d5c7bd49e7892c76760ddbff0bed9c84e20d86d472d3ac33535495", "md5": "0d12b4a609ba4f980ed752d0c4d007c5", "size": "213492"}, "windows-x86_64": {"relative_path": "cuda_cuobjdump/windows-x86_64/cuda_cuobjdump-windows-x86_64-12.4.127-archive.zip", "sha256": "8a8eacddc1c0b9e6530a149e60d33219a18d5be586a5c9dab0996ed1214ab602", "md5": "761a8e18757c4729f24a611131a79675", "size": "4172726"}, "linux-aarch64": {"relative_path": "cuda_cuobjdump/linux-aarch64/cuda_cuobjdump-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "a7724c73b8a072886aab25ec80a279fb500d9995a4c02f5e06e4ce2e03b116c4", "md5": "df4feb7e5129cf6294403e77d3830188", "size": "196000"}}, "cuda_cupti": {"name": "CUPTI", "license": "CUDA Toolkit", "license_path": "cuda_cupti/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_cupti/linux-x86_64/cuda_cupti-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "232f4677a30335f4bafcd4a42b61d4cb65060680067e0dce32966d663f4224ac", "md5": "e423452138a44bd5ef21592c9aa740c8", "size": "20753628"}, "linux-ppc64le": {"relative_path": "cuda_cupti/linux-ppc64le/cuda_cupti-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "6e86197149b0c0bd131ad925c165251fff1c054ae477c10b5c45e052789e1a2d", "md5": "84af4ecc8d12d8a62e597a061859a122", "size": "12038720"}, "linux-sbsa": {"relative_path": "cuda_cupti/linux-sbsa/cuda_cupti-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "62f3a245ade16dfb17c56eaaad4df5f339acaa34d2e05ddc15f49fc30e78663c", "md5": "558b13e34d57f2e81d2ffd669553efd1", "size": "11152548"}, "windows-x86_64": {"relative_path": "cuda_cupti/windows-x86_64/cuda_cupti-windows-x86_64-12.4.127-archive.zip", "sha256": "6ad8df634d60e4bc96cf6914af71a5708f6460a25a39d0183780c6bdebfa60c7", "md5": "6ea8d481af3a4cdceb7620ccfcfe1cbc", "size": "14921079"}, "linux-aarch64": {"relative_path": "cuda_cupti/linux-aarch64/cuda_cupti-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "93275f4069a05e2342f4648130e039de20900bc3e81d797a7239032ddec5556e", "md5": "2feb723c8e3130da2ffa3df424fb67fc", "size": "7871848"}}, "cuda_cuxxfilt": {"name": "CUDA cuxxfilt (demangler)", "license": "CUDA Toolkit", "license_path": "cuda_cuxxfilt/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_cuxxfilt/linux-x86_64/cuda_cuxxfilt-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "5c9a74786bc84dee6f2ea941f3933ce77bfa26a9135a8d38ee8c3a7183b9b200", "md5": "02808e685235763526236ef1b1d935cf", "size": "187628"}, "linux-ppc64le": {"relative_path": "cuda_cuxxfilt/linux-ppc64le/cuda_cuxxfilt-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "3bbf33a7a4757c82900500940e8cfe8b1a42a4a7e48e9cd732c5fb94f3433704", "md5": "e373f3e1c62adb1e1ee33803ee4ed0b0", "size": "182780"}, "linux-sbsa": {"relative_path": "cuda_cuxxfilt/linux-sbsa/cuda_cuxxfilt-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "e8bb04577e3271477b43c6948c5a39686c93d22f87ddeefd838682298ee399ef", "md5": "0214bb4c524aa6ac3bf74575526a752d", "size": "176408"}, "windows-x86_64": {"relative_path": "cuda_cuxxfilt/windows-x86_64/cuda_cuxxfilt-windows-x86_64-12.4.127-archive.zip", "sha256": "646d523d25f8cfa0feb731b2220da15065ecc793b2a830a81bf537e16d273dd9", "md5": "a62a20fc730cee85bc3ceba23b68768f", "size": "170572"}, "linux-aarch64": {"relative_path": "cuda_cuxxfilt/linux-aarch64/cuda_cuxxfilt-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "88683921e0c5624b7d416fe0f9b225ace448cf7dceb450afbcd25a3b3d8e3421", "md5": "60aa1555031d20bc243d57c674e2d011", "size": "170320"}}, "cuda_demo_suite": {"name": "CUDA Demo Suite", "license": "CUDA Toolkit", "license_path": "cuda_demo_suite/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_demo_suite/linux-x86_64/cuda_demo_suite-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "288bb5fe5e5e531de4b6e6724f1044da1390ddce65dcc8ebe70e6bca45ce1370", "md5": "a41d1d89724a4078bc9e17c60e3de387", "size": "4003068"}, "windows-x86_64": {"relative_path": "cuda_demo_suite/windows-x86_64/cuda_demo_suite-windows-x86_64-12.4.127-archive.zip", "sha256": "374cb13ff18f8fb8c84a44c8b5e3ec0e28f63df434105d475defb536af38b491", "md5": "7a56556c59cce5d39c780398c1253444", "size": "5063926"}}, "cuda_documentation": {"name": "CUDA Documentation", "license": "CUDA Toolkit", "license_path": "cuda_documentation/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_documentation/linux-x86_64/cuda_documentation-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "fe2f8351cab18853abf3d2d2ed4d5ce7419de6731676b88a2dafa07060cc2257", "md5": "c4db0370537a4d5fb55dd1bd54df13eb", "size": "67172"}, "linux-ppc64le": {"relative_path": "cuda_documentation/linux-ppc64le/cuda_documentation-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "cf2262b7c928fd5e32305e4ca9d2d789915c95e3be0f2b497a1b8ec459415ac8", "md5": "75c6b4510dcaa980e15be334aa86df91", "size": "67128"}, "linux-sbsa": {"relative_path": "cuda_documentation/linux-sbsa/cuda_documentation-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "af65e904d6ebce8bfde257ecfd8f7d4e052cea60c9e99e98eec726a41227d99c", "md5": "fcb181b3b6966497bca279ab4c59f6ca", "size": "67120"}, "windows-x86_64": {"relative_path": "cuda_documentation/windows-x86_64/cuda_documentation-windows-x86_64-12.4.127-archive.zip", "sha256": "2215bee2262cf71e40f63ad28bf12a3a53f8f456789dda5bc5ff5131e7901793", "md5": "5dc869c80f00efcfc807d5d596d4e22b", "size": "105674"}, "linux-aarch64": {"relative_path": "cuda_documentation/linux-aarch64/cuda_documentation-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "c18c0f6db3b26c90820597fd105dd6a38e785213a0d32a36480ddaf96dc94b51", "md5": "8d65c666a26394a6d70b000dc589d8f0", "size": "67184"}}, "cuda_gdb": {"name": "CUDA GDB", "license": "CUDA Toolkit", "license_path": "cuda_gdb/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_gdb/linux-x86_64/cuda_gdb-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "b882e12dd05dd40e4e742c4f77dfbbb493efab87949548896c84ced4ad90ee08", "md5": "d91efd01374adc4d997b494775079414", "size": "44100580"}, "linux-ppc64le": {"relative_path": "cuda_gdb/linux-ppc64le/cuda_gdb-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "0194505308ef1db8d6699ae8a9bb90928f72ecc0f58a4b3fa9daed162ac0c551", "md5": "6a1c1b37cdfa436bc7456fb360a038ec", "size": "43767748"}, "linux-sbsa": {"relative_path": "cuda_gdb/linux-sbsa/cuda_gdb-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "99fb61a444bc05709bab18288661cd87104c08671e66b61cb7abe096cd008c64", "md5": "da367eb0bc8b162c913ad913620303fe", "size": "43751576"}, "linux-aarch64": {"relative_path": "cuda_gdb/linux-aarch64/cuda_gdb-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "5a0ef8bb17b30b9be7ff36f5dd78108c943d555f57c81af94a5a6032e97e9b61", "md5": "af4e139a8c31a5e61896e19f11f6fc24", "size": "43698380"}}, "cuda_nsight": {"name": "Nsight Eclipse Edition Plugin", "license": "CUDA Toolkit", "license_path": "cuda_nsight/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nsight/linux-x86_64/cuda_nsight-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "f1ee8c2c7990225b992e82d4df7334cf112450f273e8ef641b7edf6d66932936", "md5": "2cbd09ed5c9a799a0ee8564a9d5f60eb", "size": "118684472"}, "linux-ppc64le": {"relative_path": "cuda_nsight/linux-ppc64le/cuda_nsight-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "2e402c471dedcc41bb0be2517a836bdadcc539ce799fb7599304c5ffdc7dc566", "md5": "865b786b8f63efa218b98f2ecf73bf21", "size": "118684500"}}, "cuda_nvcc": {"name": "CUDA NVCC", "license": "CUDA Toolkit", "license_path": "cuda_nvcc/LICENSE.txt", "version": "12.4.131", "linux-x86_64": {"relative_path": "cuda_nvcc/linux-x86_64/cuda_nvcc-linux-x86_64-12.4.131-archive.tar.xz", "sha256": "7ffba1ada0e4b8c17e451ac7a60d386aa2642ecd08d71202a0b100c98bd74681", "md5": "66486841dab183168d79684c74df1928", "size": "51184484"}, "linux-ppc64le": {"relative_path": "cuda_nvcc/linux-ppc64le/cuda_nvcc-linux-ppc64le-12.4.131-archive.tar.xz", "sha256": "2934e83a4df2e0f4182e148753cfd1f29af226a280ea459008819531e9edb5b9", "md5": "087420e4fc0c753c524d4d6c1a4b1475", "size": "45950148"}, "linux-sbsa": {"relative_path": "cuda_nvcc/linux-sbsa/cuda_nvcc-linux-sbsa-12.4.131-archive.tar.xz", "sha256": "83f130dab0325e12b90fdf1279c0cbbd88acf638ef0a7e0cad72d50855a4f44a", "md5": "bf47b6c3a39a6dce68cacf98cc381fa5", "size": "44923460"}, "windows-x86_64": {"relative_path": "cuda_nvcc/windows-x86_64/cuda_nvcc-windows-x86_64-12.4.131-archive.zip", "sha256": "3b14cf8dd9dda4a3b1a9682270d46eef775f018e17650187a8a448a06111f2b8", "md5": "8656529f78c412e33aab0612c140fd3f", "size": "63662970"}, "linux-aarch64": {"relative_path": "cuda_nvcc/linux-aarch64/cuda_nvcc-linux-aarch64-12.4.131-archive.tar.xz", "sha256": "9e7a26fb7acd86ec8d4b67799a329d9bc6bd48bbf27a89f87df4385eb7fe5758", "md5": "688da9b43e479fefc91e353a967f8836", "size": "46315392"}}, "cuda_nvdisasm": {"name": "CUDA nvdisasm", "license": "CUDA Toolkit", "license_path": "cuda_nvdisasm/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nvdisasm/linux-x86_64/cuda_nvdisasm-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "44018a76ee5977b603b0a1f3176a8398eaa893008dadf23e82d01733c20b5542", "md5": "a147cccaf45fe9c28b27985163df3e2a", "size": "49880772"}, "linux-ppc64le": {"relative_path": "cuda_nvdisasm/linux-ppc64le/cuda_nvdisasm-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "8407848aea496afd7708c9ed61c6a1d19c56362798341e1adcfeb77d34a5fea7", "md5": "c2d1c6a5c88a53baddfdc16a4069f802", "size": "49881368"}, "linux-sbsa": {"relative_path": "cuda_nvdisasm/linux-sbsa/cuda_nvdisasm-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "ec0480837e789f14803ae148f5ada2cfbd02216afc832c23d6a38037b88be381", "md5": "a54c74a03e1e836365cc3004ac427996", "size": "49808680"}, "windows-x86_64": {"relative_path": "cuda_nvdisasm/windows-x86_64/cuda_nvdisasm-windows-x86_64-12.4.127-archive.zip", "sha256": "62a15a4ef3c0641352d7e5c184243d20f9e4992f040538c0f14cd40ec7e5ef7b", "md5": "2abd28024cf6d9e407eceac3c716e4ed", "size": "50146842"}, "linux-aarch64": {"relative_path": "cuda_nvdisasm/linux-aarch64/cuda_nvdisasm-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "db97528d051cc8ee4a00e33a5dc8ca098196295862d3dcd264d61ac5ed1ebd2d", "md5": "917fe51b82ccd7fa801cde9380b437dd", "size": "49831444"}}, "cuda_nvml_dev": {"name": "CUDA NVML Headers", "license": "CUDA Toolkit", "license_path": "cuda_nvml_dev/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nvml_dev/linux-x86_64/cuda_nvml_dev-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "15af54c7d909f6e24db8b557e34276d70778fcb26f1969b7a9fe42abaa919265", "md5": "9fad58bd78317fdee3e565253c8717c9", "size": "142744"}, "linux-ppc64le": {"relative_path": "cuda_nvml_dev/linux-ppc64le/cuda_nvml_dev-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "e3e2c040a2edc8238bc72c0b600d1b7639b240223d5f3f1fb419162a4c1afb14", "md5": "e116f3e0eaafad6001f3a5961205f667", "size": "139468"}, "linux-sbsa": {"relative_path": "cuda_nvml_dev/linux-sbsa/cuda_nvml_dev-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "81820623c4ad794efbd00a38649af7847b951cd5fce2659d001a525e6f6bb72d", "md5": "d70cd674f5744b6c46f023421acdbe1f", "size": "143480"}, "windows-x86_64": {"relative_path": "cuda_nvml_dev/windows-x86_64/cuda_nvml_dev-windows-x86_64-12.4.127-archive.zip", "sha256": "8240b666b0da2aebc58c861410c70f4d5ffe157e7071586314ba0d79200f7519", "md5": "8eb77d343eb128112809134777d57183", "size": "127269"}, "linux-aarch64": {"relative_path": "cuda_nvml_dev/linux-aarch64/cuda_nvml_dev-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "1ccaf8990080ac498f39799f081af770b476983bc0a24f40f168985379029519", "md5": "ce2dd6abf5a8d94cfc3f4d70b2346610", "size": "144148"}}, "cuda_nvprof": {"name": "CUDA nvprof", "license": "CUDA Toolkit", "license_path": "cuda_nvprof/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nvprof/linux-x86_64/cuda_nvprof-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "5ac35eb3edf867ba757c3c04a6fd4b79cae38de47bd3f384af9ab5615b1d0083", "md5": "a47051ced76211bdfb10f7b72d40199d", "size": "2433916"}, "linux-ppc64le": {"relative_path": "cuda_nvprof/linux-ppc64le/cuda_nvprof-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "6bfc9e12fb06176c093659cc108fd9397e4b5168697609660b309f42811ff806", "md5": "112aa9c78185c05ab2ed0cb2342abf52", "size": "2116424"}, "windows-x86_64": {"relative_path": "cuda_nvprof/windows-x86_64/cuda_nvprof-windows-x86_64-12.4.127-archive.zip", "sha256": "bd434d759948b4ef9d9e663b89f5019ea104afec6bf7a17267c624b0bfbc1a03", "md5": "f43bdf3d1a27cc2568e24b5e0de8686d", "size": "1701799"}}, "cuda_nvprune": {"name": "CUDA nvprune", "license": "CUDA Toolkit", "license_path": "cuda_nvprune/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nvprune/linux-x86_64/cuda_nvprune-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "85d348b08d1e652ac2fd6377d1f837aa1aeaf7f784aa451175fd5ea58fd3b28b", "md5": "aa09d8f50c302bfc907836cd8fa9b697", "size": "56380"}, "linux-ppc64le": {"relative_path": "cuda_nvprune/linux-ppc64le/cuda_nvprune-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "28b297f44bf568b7e477ede212457216c215ffceb53e0610d942e573b95fec05", "md5": "52ad7660a488716512321ab8914da89c", "size": "57020"}, "linux-sbsa": {"relative_path": "cuda_nvprune/linux-sbsa/cuda_nvprune-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "ec6b7ed538496507b99274b5361f36cf84ad18048892e0c6ebf74336c0181b15", "md5": "bc81cb6ff80f92b19e0da829bb3771ca", "size": "48352"}, "windows-x86_64": {"relative_path": "cuda_nvprune/windows-x86_64/cuda_nvprune-windows-x86_64-12.4.127-archive.zip", "sha256": "a322bb849e8974e759d1287977dd237d4ca240159e70fe3e131df600a7cf4d32", "md5": "705bccfbebf390d5163a21be69ffface", "size": "146240"}, "linux-aarch64": {"relative_path": "cuda_nvprune/linux-aarch64/cuda_nvprune-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "ffd1d4c587056be277885b696077fd744890a6875d0c27577aed33bd75f43b03", "md5": "801667ee9291f7b8e9c8fe9be27b76ec", "size": "50164"}}, "cuda_nvrtc": {"name": "CUDA NVRTC", "license": "CUDA Toolkit", "license_path": "cuda_nvrtc/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nvrtc/linux-x86_64/cuda_nvrtc-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "b5e7a984fcf05d3123684d7926e595306d31fbf99f9b19e9a0d268a02fc75827", "md5": "3e5625687340658034d0fb41e1e29e44", "size": "34123316"}, "linux-ppc64le": {"relative_path": "cuda_nvrtc/linux-ppc64le/cuda_nvrtc-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "cae11cf45b9443643e82bdaecd51aec9f827db7cdab9498df388471532255480", "md5": "a5f5e3280ebd65ad906c579833eab982", "size": "31421316"}, "linux-sbsa": {"relative_path": "cuda_nvrtc/linux-sbsa/cuda_nvrtc-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "f9e4bd972ffee5951577b45524b656eda681407a3c761c57978acec26a3acc25", "md5": "c0f103fe3cc1f589d7769afd45efd099", "size": "31422784"}, "windows-x86_64": {"relative_path": "cuda_nvrtc/windows-x86_64/cuda_nvrtc-windows-x86_64-12.4.127-archive.zip", "sha256": "f140545e06d0d10780c1382a577db2e2c242db7a2d94970f0e6026b2d01aeb1b", "md5": "2804f543c452c09ada1cb5705a1cc932", "size": "101865624"}, "linux-aarch64": {"relative_path": "cuda_nvrtc/linux-aarch64/cuda_nvrtc-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "9fb6f14168c3194225f29f85467d95150ea65ea676f86303c5ea47ef42ff302f", "md5": "a97fd2007d18388be8d1415d6c1bcca8", "size": "32669152"}}, "cuda_nvtx": {"name": "CUDA NVTX", "license": "CUDA Toolkit", "license_path": "cuda_nvtx/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nvtx/linux-x86_64/cuda_nvtx-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "f3afccda5ff1e7521cca74153e92708349148294bc75559afe64d57c43763454", "md5": "df06d96b86197ba1a3163303445fef40", "size": "48560"}, "linux-ppc64le": {"relative_path": "cuda_nvtx/linux-ppc64le/cuda_nvtx-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "772b01a6bfe9d0191f8fdd7f75462563af1926afa2637dd816cc2e9747181536", "md5": "2c738d4a6deeb34dceae9719a8a8cd38", "size": "48616"}, "linux-sbsa": {"relative_path": "cuda_nvtx/linux-sbsa/cuda_nvtx-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "c1d60013e97108fd69caf29e5e7d9b0a1562508517fc1a2bab65c294990bd7e3", "md5": "78a1206060af66b3b1bd88a8d82487ad", "size": "49148"}, "windows-x86_64": {"relative_path": "cuda_nvtx/windows-x86_64/cuda_nvtx-windows-x86_64-12.4.127-archive.zip", "sha256": "95b7f96bdb2701d764d969cdadfbc439b4ab995d0a3695682da5284e14f66d21", "md5": "bc7c949a95ac8d1ab24926c9dad82a6f", "size": "65879"}, "linux-aarch64": {"relative_path": "cuda_nvtx/linux-aarch64/cuda_nvtx-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "d7fbb08e056f11bacae8d4165d5bd4ec4cc601ab326dc36e76678eec6971f612", "md5": "eb65bc05a8f93ffced733221ef9010e2", "size": "51792"}}, "cuda_nvvp": {"name": "CUDA NVVP", "license": "CUDA Toolkit", "license_path": "cuda_nvvp/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_nvvp/linux-x86_64/cuda_nvvp-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "94c8b79180f382edaf13d93c3a4dadff43833d0dd624448968eec4f3685c2982", "md5": "e4da126db63c381319e1f0a7d5d9affa", "size": "114592132"}, "linux-ppc64le": {"relative_path": "cuda_nvvp/linux-ppc64le/cuda_nvvp-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "83ea88a78128acbf1d488f43c6207c1babbef5a5ef06f7604fbefbec0ac01f87", "md5": "828fc847d197c0c16408e5986fa2bacb", "size": "117200208"}, "windows-x86_64": {"relative_path": "cuda_nvvp/windows-x86_64/cuda_nvvp-windows-x86_64-12.4.127-archive.zip", "sha256": "80593019abbd1aec121d2048ec9e6ffa0adc3a7eeb5f6451ceb593af54ac2c03", "md5": "360fb70106eb44e81723eea128e7118b", "size": "120343803"}}, "cuda_opencl": {"name": "CUDA OpenCL", "license": "CUDA Toolkit", "license_path": "cuda_opencl/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_opencl/linux-x86_64/cuda_opencl-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "34648ff01acb49ec0247e4cb240470b93e9d0c9ee0641091a7bfee0698f09ed9", "md5": "d26623cc9aac8d85af54ba7216d8fc5f", "size": "91476"}, "windows-x86_64": {"relative_path": "cuda_opencl/windows-x86_64/cuda_opencl-windows-x86_64-12.4.127-archive.zip", "sha256": "4b5be4ad11ded8c33e83f757435a6c3fa5ff309339b13e18da9f520fb349bf3b", "md5": "fc73bc5396fcca98afe7c5bc8b03a2c6", "size": "137051"}}, "cuda_profiler_api": {"name": "CUDA Profiler API", "license": "CUDA Toolkit", "license_path": "cuda_profiler_api/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_profiler_api/linux-x86_64/cuda_profiler_api-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "fa11c08e39ab35453e07dd5012adc0767408955431fc4acdf64ca4cdd7692646", "md5": "0d594036001b8fae7a1bd2b8b2353caf", "size": "16176"}, "linux-ppc64le": {"relative_path": "cuda_profiler_api/linux-ppc64le/cuda_profiler_api-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "71b6102a07676635ec9fd0a34250bbe1c646e5e72d79e57e9072fef411f0d52f", "md5": "76348fc9fab4a1507504c309d6da555c", "size": "16180"}, "linux-sbsa": {"relative_path": "cuda_profiler_api/linux-sbsa/cuda_profiler_api-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "aaab63b12aba7278c4d72072dc71bbdb7773137af5428c7bb0414496ae78632a", "md5": "94c5f7c70b274ff6c0e66854eb1d4907", "size": "16168"}, "windows-x86_64": {"relative_path": "cuda_profiler_api/windows-x86_64/cuda_profiler_api-windows-x86_64-12.4.127-archive.zip", "sha256": "8c0c81125d2f0ef6f42bc46723f2c5565863731cf3a3de3ea3e738ea2d7a938f", "md5": "2dec7aeff036c224f4299c82dda201bb", "size": "20232"}, "linux-aarch64": {"relative_path": "cuda_profiler_api/linux-aarch64/cuda_profiler_api-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "9ec80b22522f8491425f4194f86ac4f74a990207bd1fa85169829be5dd450076", "md5": "f58a3f980d3d5c5495e7b57997e5c04a", "size": "16180"}}, "cuda_sanitizer_api": {"name": "CUDA Compute Sanitizer API", "license": "CUDA Toolkit", "license_path": "cuda_sanitizer_api/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "cuda_sanitizer_api/linux-x86_64/cuda_sanitizer_api-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "b719c6b2a8cd330ab0e4361e41dda5493946fb7fa0c4b04c8dbd776fadc3b11b", "md5": "1558561259291e2013f810a72727e377", "size": "8236716"}, "linux-ppc64le": {"relative_path": "cuda_sanitizer_api/linux-ppc64le/cuda_sanitizer_api-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "5ceae6170471d7a9e4540796760642747d10d7d4f609dacede90f84927e59618", "md5": "c92e3dfd851d20cca60974bfa7be4718", "size": "7741432"}, "linux-sbsa": {"relative_path": "cuda_sanitizer_api/linux-sbsa/cuda_sanitizer_api-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "0e5ff35535ecb1ed0b482a2ac5e3a20e94722736f023fc802cbfd4900e590746", "md5": "3a3966496aab1485f4833dd161e1ec93", "size": "6367224"}, "windows-x86_64": {"relative_path": "cuda_sanitizer_api/windows-x86_64/cuda_sanitizer_api-windows-x86_64-12.4.127-archive.zip", "sha256": "f15613b7c3088299659356456abfe2c3a98eed7eea9d8292fe0ec46726f5ec73", "md5": "d67f646c3661714b53bc6b67ea468ef8", "size": "14136485"}, "linux-aarch64": {"relative_path": "cuda_sanitizer_api/linux-aarch64/cuda_sanitizer_api-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "d93b5d59c0a18753678fbcce9fa0d83c6b2ff008cebbcb226a2e138b26305aa9", "md5": "b7adbb31f0e824b1a74123fe525e7d30", "size": "3728732"}}, "fabricmanager": {"name": "NVIDIA Fabric Manager", "license": "NVIDIA Driver", "license_path": "fabricmanager/LICENSE.txt", "version": "550.54.15", "linux-x86_64": {"relative_path": "fabricmanager/linux-x86_64/fabricmanager-linux-x86_64-550.54.15-archive.tar.xz", "sha256": "73396a744821f280168090175f06efc26b86ce3a4ac3c88b3bf39dd8d9e4c978", "md5": "293115e77e87c5f73141487194f1194b", "size": "5785060"}, "linux-sbsa": {"relative_path": "fabricmanager/linux-sbsa/fabricmanager-linux-sbsa-550.54.15-archive.tar.xz", "sha256": "c8a8534875816cb9bfe30f151bf86e8a22fffe017c1715b91698f098950f9547", "md5": "7e3ff10fc4a71e0731fd65babf435fa1", "size": "5218672"}}, "imex": {"name": "Imex", "license": "NVIDIA Proprietary", "license_path": "imex/LICENSE.txt", "version": "550.54.15", "linux-x86_64": {"relative_path": "imex/linux-x86_64/imex-linux-x86_64-550.54.15-archive.tar.xz", "sha256": "0212d562b487ed599d1b4a20d1d4ff0a7d4ded6e2e593e8d464f199b7e93e9bc", "md5": "c2ef7e81023ce30d52f0cced72f92bd5", "size": "7300824"}, "linux-sbsa": {"relative_path": "imex/linux-sbsa/imex-linux-sbsa-550.54.15-archive.tar.xz", "sha256": "5b2782a58b267cb83bc3efcd62c3d5546044caee06f56afa8cad496123a312c9", "md5": "bdb8e5328489449fb09f710f6696ce56", "size": "6498388"}}, "libcublas": {"name": "CUDA cuBLAS", "license": "CUDA Toolkit", "license_path": "libcublas/LICENSE.txt", "version": "********", "linux-x86_64": {"relative_path": "libcublas/linux-x86_64/libcublas-linux-x86_64-********-archive.tar.xz", "sha256": "c267c4d2cd42065ae2c30ca41a43ddce4e0ecc5484de23f299e3c397b5506eda", "md5": "3c0fe94786ee5288bf6a117cfcc6c9fe", "size": "468331916"}, "linux-ppc64le": {"relative_path": "libcublas/linux-ppc64le/libcublas-linux-ppc64le-********-archive.tar.xz", "sha256": "40726bd8bb106dacf5c1aef8815f3561078b13433671fed40dff431d13a34ff1", "md5": "83cd04d43526fb78beccfd0c82318e80", "size": "360246616"}, "linux-sbsa": {"relative_path": "libcublas/linux-sbsa/libcublas-linux-sbsa-********-archive.tar.xz", "sha256": "e946460149f1970e8c07472bab447f30054dfcc809eb2809bcd9b0090b0b876f", "md5": "323b840b5f3e281a90acd29bd128a2ab", "size": "466990080"}, "windows-x86_64": {"relative_path": "libcublas/windows-x86_64/libcublas-windows-x86_64-********-archive.zip", "sha256": "698140f12da055a3709eee2e022fcfe7bc8edf31f30115e3f7a5c877a9491de5", "md5": "6edf7e3134dccd20636c70cae849800d", "size": "391538487"}, "linux-aarch64": {"relative_path": "libcublas/linux-aarch64/libcublas-linux-aarch64-********-archive.tar.xz", "sha256": "1d3886c1da442325d21424f5c021957bdb0c9c8b0b8aef7bf5ca5a449f60490b", "md5": "b9461271cad612b581771495ce96d9fa", "size": "432576828"}}, "libcudla": {"name": "cuDLA", "license": "CUDA Toolkit", "license_path": "libcudla/LICENSE.txt", "version": "12.4.127", "linux-aarch64": {"relative_path": "libcudla/linux-aarch64/libcudla-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "ed418c2104eeb8edd640c8e7694c548e57f42e3f92b53486b16c61db1613232a", "md5": "ea7dd261fd92379f057fc82035c0f7fc", "size": "38692"}}, "libcufft": {"name": "CUDA cuFFT", "license": "CUDA Toolkit", "license_path": "libcufft/LICENSE.txt", "version": "********", "linux-x86_64": {"relative_path": "libcufft/linux-x86_64/libcufft-linux-x86_64-********-archive.tar.xz", "sha256": "0963b4b57fb7fe9217e97b494159ff4719f1abdc407b2dc215d71bae0576bb02", "md5": "3269400f8ccdffc37e84541ee7d13d97", "size": "509571792"}, "linux-ppc64le": {"relative_path": "libcufft/linux-ppc64le/libcufft-linux-ppc64le-********-archive.tar.xz", "sha256": "5d020dc916a7c5f901802d21ca10ad4d40eaa21c4a1d487ad13a40cab60b4467", "md5": "dce4e38e22bebf2f77a2c4f801b2489a", "size": "511052864"}, "linux-sbsa": {"relative_path": "libcufft/linux-sbsa/libcufft-linux-sbsa-********-archive.tar.xz", "sha256": "4087e27f2429d5f1f820ec49ac400391cbfe84c4abfadb95aaf90705ae84e725", "md5": "7e93f2d42f1735d3f77a2fe9e95f169c", "size": "509862516"}, "windows-x86_64": {"relative_path": "libcufft/windows-x86_64/libcufft-windows-x86_64-********-archive.zip", "sha256": "df1594afd3de4e23779511eb8eccc1f77faacd9fa64e6154828b9bdd68d9a785", "md5": "be165c0565ed09389d687683c30ac98e", "size": "209047054"}, "linux-aarch64": {"relative_path": "libcufft/linux-aarch64/libcufft-linux-aarch64-********-archive.tar.xz", "sha256": "538735221a110b052d21ebfe57f710572814b4d8a820c125ce63db621f6b973b", "md5": "c2a65ffbb2dea600778c79aaae31ee6d", "size": "509581888"}}, "libcufile": {"name": "CUDA cuFile", "license": "CUDA Toolkit", "license_path": "libcufile/LICENSE.txt", "version": "*******", "linux-x86_64": {"relative_path": "libcufile/linux-x86_64/libcufile-linux-x86_64-*******-archive.tar.xz", "sha256": "5bf067c142e0e78d6b5eb9904f0703a1f5f814a27c44cff596f54630582bb2a9", "md5": "01288701dc7a350e9a3a9e24e3377b4a", "size": "41837868"}, "linux-sbsa": {"relative_path": "libcufile/linux-sbsa/libcufile-linux-sbsa-*******-archive.tar.xz", "sha256": "5db80b1905b3fe25a07f29462c8694af1375834c1d8e7b6bf4cf4ffbb8c0b934", "md5": "3e5fa16a3d5e0c0a697a8ec9fc06fed0", "size": "41281928"}, "linux-aarch64": {"relative_path": "libcufile/linux-aarch64/libcufile-linux-aarch64-*******-archive.tar.xz", "sha256": "90e691a5eec221701231401aa645b95c25c7ec9c509f00717bfdea6399d8c678", "md5": "c4de445d63a79b1d30adca9ccfb3a58a", "size": "41264512"}}, "libcurand": {"name": "CUDA cuRAND", "license": "CUDA Toolkit", "license_path": "libcurand/LICENSE.txt", "version": "**********", "linux-x86_64": {"relative_path": "libcurand/linux-x86_64/libcurand-linux-x86_64-**********-archive.tar.xz", "sha256": "f3752abef1ffae7bc9180bd9885905e971cc9c28d7f4d5860731a012e07638a3", "md5": "d89df6d57ccac1caf85775a12de1d809", "size": "81720172"}, "linux-ppc64le": {"relative_path": "libcurand/linux-ppc64le/libcurand-linux-ppc64le-**********-archive.tar.xz", "sha256": "42ed22ceebcb1840b391cb4369a41e6ed1ae31c5aae0b66fe15ba242db06c4d9", "md5": "c31a0ecf60e6ca0eb75f0bfb1921da14", "size": "81770160"}, "linux-sbsa": {"relative_path": "libcurand/linux-sbsa/libcurand-linux-sbsa-**********-archive.tar.xz", "sha256": "ee337fa4c53136e336f974ba5d1a9be2a1ec5da674be3bb972a8d645051bbfae", "md5": "72683f5c992721a868973018bbfcd01d", "size": "81710072"}, "windows-x86_64": {"relative_path": "libcurand/windows-x86_64/libcurand-windows-x86_64-**********-archive.zip", "sha256": "24c3b0fb7063e49ccc0ac1bff387c5f4fd9617b72aab3fa3b642f08607770ad3", "md5": "b6950f92d0607f4f223eda34cd93741e", "size": "55087990"}, "linux-aarch64": {"relative_path": "libcurand/linux-aarch64/libcurand-linux-aarch64-**********-archive.tar.xz", "sha256": "3be3a69ab2b242cfc78ef2f85a14c3d02b04d4e64a4345c75f565d981ae99415", "md5": "e6cfa09022835d1bf619112c5665a64b", "size": "83938360"}}, "libcusolver": {"name": "CUDA cuSOLVER", "license": "CUDA Toolkit", "license_path": "libcusolver/LICENSE.txt", "version": "********", "linux-x86_64": {"relative_path": "libcusolver/linux-x86_64/libcusolver-linux-x86_64-********-archive.tar.xz", "sha256": "0a35a16e3bc02ba6fe0393e916da087bac079eb998a01666c79533db4f0d37f6", "md5": "4413cd12f780d1f12955f8d0a3e0119c", "size": "126742916"}, "linux-ppc64le": {"relative_path": "libcusolver/linux-ppc64le/libcusolver-linux-ppc64le-********-archive.tar.xz", "sha256": "55c3e49dce2cdfadaec23aeb408383d713bc0838e882f22b41931599525af795", "md5": "e7f8fb988c3966a28f8239d876618dd5", "size": "127205496"}, "linux-sbsa": {"relative_path": "libcusolver/linux-sbsa/libcusolver-linux-sbsa-********-archive.tar.xz", "sha256": "3344980ec35d4d850cf10909a2b0f5c1fdea7f2c1af0c6ad9b72dbd188391c3c", "md5": "51bc8fa6c57206fa1e055363e96de0bc", "size": "126349932"}, "windows-x86_64": {"relative_path": "libcusolver/windows-x86_64/libcusolver-windows-x86_64-********-archive.zip", "sha256": "e1f98fab494b099beaf39b533a725c241afad5b885a88a01e21c5e0aa8bbf978", "md5": "b9dcf7794aa18130fda2a4e3bdc728cf", "size": "123611055"}, "linux-aarch64": {"relative_path": "libcusolver/linux-aarch64/libcusolver-linux-aarch64-********-archive.tar.xz", "sha256": "82050419231f3ab6252cf4edb6673504adcdd712cb34ab519f7913beadb8526e", "md5": "c4677035f9b09f9bc1dd9fe22cd72fe4", "size": "138196596"}}, "libcusparse": {"name": "CUDA cuSPARSE", "license": "CUDA Toolkit", "license_path": "libcusparse/LICENSE.txt", "version": "**********", "linux-x86_64": {"relative_path": "libcusparse/linux-x86_64/libcusparse-linux-x86_64-**********-archive.tar.xz", "sha256": "21b7e5c3afd8c3e761471c644b8bbf36cd528dd0f9d274becbeb09b79dd9bec5", "md5": "7f9fc64c7cb961fcd8dac565bc51a8fc", "size": "223377616"}, "linux-ppc64le": {"relative_path": "libcusparse/linux-ppc64le/libcusparse-linux-ppc64le-**********-archive.tar.xz", "sha256": "8f52b2849dcc9a95fb7b2e2eea46afe5c1b459d372f3ce9eff1769845f33e0a5", "md5": "955338a7dff201e885713b8f79ada264", "size": "223489508"}, "linux-sbsa": {"relative_path": "libcusparse/linux-sbsa/libcusparse-linux-sbsa-**********-archive.tar.xz", "sha256": "098169df5ee6ad441ca22ca7d1168c4adedada57e32238b68db42a9934347534", "md5": "d59f4d0e3e00af8d6f4a1bd59baa685d", "size": "222984764"}, "windows-x86_64": {"relative_path": "libcusparse/windows-x86_64/libcusparse-windows-x86_64-**********-archive.zip", "sha256": "d1cef671112537a290ef9282f5258b86bc1ae55e76b1995e330d356b6a140d4e", "md5": "332d7ff19a0f4e03ec029edb11431669", "size": "202432148"}, "linux-aarch64": {"relative_path": "libcusparse/linux-aarch64/libcusparse-linux-aarch64-**********-archive.tar.xz", "sha256": "0ae1b89c7c26aa1fc1c59e4b5fd65b37bbefe0914cae3b36e057e8a66bd3c349", "md5": "b295b235acb09fc7914cc2b1e358ee87", "size": "238279416"}}, "libnpp": {"name": "CUDA NPP", "license": "CUDA Toolkit", "license_path": "libnpp/LICENSE.txt", "version": "*********", "linux-x86_64": {"relative_path": "libnpp/linux-x86_64/libnpp-linux-x86_64-*********-archive.tar.xz", "sha256": "9062bec77b9844663692459255f35fe70cf826bce2fc8065278410fc5f27023f", "md5": "5d36e45d04e314b5ab9a28e2d905a5d4", "size": "184801936"}, "linux-ppc64le": {"relative_path": "libnpp/linux-ppc64le/libnpp-linux-ppc64le-*********-archive.tar.xz", "sha256": "2ada6d5ec9f0a963de3bf2eb1a5e8d431a40fc4c616e1e51e04d96bbbc2604f8", "md5": "57bb46f42d1633c3e5f9fe5f04a96ea7", "size": "185183912"}, "linux-sbsa": {"relative_path": "libnpp/linux-sbsa/libnpp-linux-sbsa-*********-archive.tar.xz", "sha256": "e91905f9a23d749fb8389f8671df67f71f49401d19f955d5029c7ebc3d839f73", "md5": "571fd9cdeb13651df5f06a1930c12b91", "size": "184329432"}, "windows-x86_64": {"relative_path": "libnpp/windows-x86_64/libnpp-windows-x86_64-*********-archive.zip", "sha256": "56d1a741a3c4f5c4b2fe73dd4eb104ab1defaea3382ed5a65e4b5fe202e9c5b1", "md5": "5e0986267a14179faedb717489719bf7", "size": "156823745"}, "linux-aarch64": {"relative_path": "libnpp/linux-aarch64/libnpp-linux-aarch64-*********-archive.tar.xz", "sha256": "c3eaaa8ac7e566d0a0452b1451c52d453f4509a236164b2b231cd194dcd2d7a6", "md5": "769e4226352ea9edb78626228685d49e", "size": "202127304"}}, "libnvfatbin": {"name": "NVIDIA compiler library for fatbin interaction", "license": "CUDA Toolkit", "license_path": "libnvfatbin/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "libnvfatbin/linux-x86_64/libnvfatbin-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "1278f5f6562ed1ae78c3c0bfa665033c986472858bf38ab31e7a1e3091efae0e", "md5": "7fbf212c3ab289387856b5cab4b04d72", "size": "888520"}, "linux-ppc64le": {"relative_path": "libnvfatbin/linux-ppc64le/libnvfatbin-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "071ecdda624aa570fbc45220253b149070a993e1797dc7c2ea8d662e20fae4c9", "md5": "b1153dab90745f19d6a144aa70873aa5", "size": "856144"}, "linux-sbsa": {"relative_path": "libnvfatbin/linux-sbsa/libnvfatbin-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "585d89846cf12cd6862e27e53d98aa54d357ea3d153c219a3efb1e1ecc918ffd", "md5": "9ca67e355b7699e475256ea1bf6056e9", "size": "788256"}, "windows-x86_64": {"relative_path": "libnvfatbin/windows-x86_64/libnvfatbin-windows-x86_64-12.4.127-archive.zip", "sha256": "9a8f4d18626733b221bdbdc823a75a0c86f8555ab1f5201b44faf70fb47580e1", "md5": "6173341f2290fa30426502f4e167cf0e", "size": "1501167"}, "linux-aarch64": {"relative_path": "libnvfatbin/linux-aarch64/libnvfatbin-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "271247df10e84047646e67b4ae2ded4f9e562bbec085c556f44490f77855d32c", "md5": "26001074c8f1cfa562f477ee8822f5ab", "size": "762748"}}, "libnvidia_nscq": {"name": "NVIDIA NSCQ API", "license": "NVIDIA Driver", "license_path": "libnvidia_nscq/LICENSE.txt", "version": "550.54.15", "linux-x86_64": {"relative_path": "libnvidia_nscq/linux-x86_64/libnvidia_nscq-linux-x86_64-550.54.15-archive.tar.xz", "sha256": "170788919ae9ef6a026a5a784df47e23bb25dcae3b22b1a68dfe91c41f8fe165", "md5": "c11e142a82675466a340d496a06e57f6", "size": "352864"}, "linux-sbsa": {"relative_path": "libnvidia_nscq/linux-sbsa/libnvidia_nscq-linux-sbsa-550.54.15-archive.tar.xz", "sha256": "bd31f2ca27a24538805ae2476ccd20edde0a1e03cb050b650f563a65859ce64d", "md5": "6386fbbabd76d64ebd25bf5d3f643963", "size": "319136"}}, "libnvjitlink": {"name": "NVIDIA compiler library for JIT LTO functionality", "license": "CUDA Toolkit", "license_path": "libnvjitlink/LICENSE.txt", "version": "12.4.127", "linux-x86_64": {"relative_path": "libnvjitlink/linux-x86_64/libnvjitlink-linux-x86_64-12.4.127-archive.tar.xz", "sha256": "0e0ec59ee56d3dfa29c66bd4e225b1a6330d42b36545ab4377880009d42b675c", "md5": "40957c683f43bd4110094bb2ec248088", "size": "29161664"}, "linux-ppc64le": {"relative_path": "libnvjitlink/linux-ppc64le/libnvjitlink-linux-ppc64le-12.4.127-archive.tar.xz", "sha256": "a08da1a0b990ef7eb845655eb51ddc9fc920921da14ee2b32ad8daaebeb1c68b", "md5": "6cf7c3bcd4a9344933e878a6fd86fb8a", "size": "26724712"}, "linux-sbsa": {"relative_path": "libnvjitlink/linux-sbsa/libnvjitlink-linux-sbsa-12.4.127-archive.tar.xz", "sha256": "c85f5c50f18e1b8249a318432dbefdbb752ec81d374a59ab452f8d74acaf6017", "md5": "d6a38db7370ce315e9db1517d220db38", "size": "26668060"}, "windows-x86_64": {"relative_path": "libnvjitlink/windows-x86_64/libnvjitlink-windows-x86_64-12.4.127-archive.zip", "sha256": "d8f4086215f482263dbfbe47a3580e88acbcdacbb284f8aa0c21a8fe408e671d", "md5": "3e58db25554c0b86fdbf1e1bd3c33d0b", "size": "91513391"}, "linux-aarch64": {"relative_path": "libnvjitlink/linux-aarch64/libnvjitlink-linux-aarch64-12.4.127-archive.tar.xz", "sha256": "104229ab8ed7f585339a4b4e43f7926d8f7478e9b376a376613bbe402dfd5e5b", "md5": "47f822beab403cd6f0ee0728955f4fc9", "size": "27916004"}}, "libnvjpeg": {"name": "CUDA nvJPEG", "license": "CUDA Toolkit", "license_path": "libnvjpeg/LICENSE.txt", "version": "**********", "linux-x86_64": {"relative_path": "libnvjpeg/linux-x86_64/libnvjpeg-linux-x86_64-**********-archive.tar.xz", "sha256": "9f297a160b8a934e8095c4eb2ee1674a3497e06ca6a0a7d6f2cdea983d443c96", "md5": "09dae899f2e5b84818c4abe84bc88f8c", "size": "2580832"}, "linux-ppc64le": {"relative_path": "libnvjpeg/linux-ppc64le/libnvjpeg-linux-ppc64le-**********-archive.tar.xz", "sha256": "501efffdb76a9bbeab0bab66834dec310346a73dc16ae839854258f5b2e520fa", "md5": "72fc5540ee21f3679a92781a70428e20", "size": "2624724"}, "linux-sbsa": {"relative_path": "libnvjpeg/linux-sbsa/libnvjpeg-linux-sbsa-**********-archive.tar.xz", "sha256": "c1c2eeff199aa55b678acd59c0a76a3b44ca99f730e52becfd0373a55c2df2e3", "md5": "d1ef94c997f3547bdec2e5685e2288e8", "size": "2417676"}, "windows-x86_64": {"relative_path": "libnvjpeg/windows-x86_64/libnvjpeg-windows-x86_64-**********-archive.zip", "sha256": "63101a25268a70380a129c9b05cd5fcfe8cafaf8594b6b358a5e082ce482b679", "md5": "5c689be0c0212422f05ad60486e95393", "size": "2833408"}, "linux-aarch64": {"relative_path": "libnvjpeg/linux-aarch64/libnvjpeg-linux-aarch64-**********-archive.tar.xz", "sha256": "9950455d771f18ba4dc4f60de75ed072e04b19d9ea6c01f69b40315b10709194", "md5": "2b3fe4f6edc867704c0244d4dd40ab9d", "size": "2562292"}}, "nsight_compute": {"name": "Nsight Compute", "license": "NVIDIA SLA", "license_path": "nsight_compute/LICENSE.txt", "version": "2024.1.1.4", "linux-x86_64": {"relative_path": "nsight_compute/linux-x86_64/nsight_compute-linux-x86_64-2024.1.1.4-archive.tar.xz", "sha256": "b6aee577e61db6823a2fe44fcce4902962c7ea90fbb29800ffcceb3df7ea8d47", "md5": "a27549e05b460ea4cf3c000f610e91b1", "size": "599288172"}, "linux-ppc64le": {"relative_path": "nsight_compute/linux-ppc64le/nsight_compute-linux-ppc64le-2024.1.1.4-archive.tar.xz", "sha256": "267c884f074163e5ffde8fe2a798f795b5d375e0fb1df4439e143a992e7e5f6d", "md5": "af2a15b4d0e300449c0bdfde48222ca4", "size": "114721700"}, "linux-sbsa": {"relative_path": "nsight_compute/linux-sbsa/nsight_compute-linux-sbsa-2024.1.1.4-archive.tar.xz", "sha256": "5e3b489eaffe329cc3b204a31e9b9c9bd231203ddfd89f57e04fc59c3119527e", "md5": "f209d70764ce909ca0a76a2a365528fb", "size": "260307276"}, "windows-x86_64": {"relative_path": "nsight_compute/windows-x86_64/nsight_compute-windows-x86_64-2024.1.1.4-archive.zip", "sha256": "a8e497fd4a9dbf12e328e961984f5fad445045efc2568b0cb594ff51da977ba0", "md5": "40e93cd6fdd528e7592c1048befa49f7", "size": "545282883"}, "linux-aarch64": {"relative_path": "nsight_compute/linux-aarch64/nsight_compute-linux-aarch64-2024.1.1.4-archive.tar.xz", "sha256": "122981317d978e5ab98ccb4aa1a922f496d23c2e45ecc3fab2e1524879eb899c", "md5": "e189006ab0b8b281ed6a6484d430b18f", "size": "551145232"}}, "nsight_systems": {"name": "Nsight Systems", "license": "NVIDIA SLA", "license_path": "nsight_systems/LICENSE.txt", "version": "2023.4.4.54", "linux-x86_64": {"relative_path": "nsight_systems/linux-x86_64/nsight_systems-linux-x86_64-2023.4.4.54-archive.tar.xz", "sha256": "25ecbd3e670eb976abafdbdec688008b7eec90ddc230b74d7c276d9f8fdcf076", "md5": "397a7063c1a06aa49265bb4838af9076", "size": "220434032"}, "linux-ppc64le": {"relative_path": "nsight_systems/linux-ppc64le/nsight_systems-linux-ppc64le-2023.4.4.54-archive.tar.xz", "sha256": "d59ade067dcd4747faf9499f4399749d67ca570b03ecc50b22a8fc97d621b756", "md5": "2ebe95985f4b57380e34e8c7ca733355", "size": "58502288"}, "linux-sbsa": {"relative_path": "nsight_systems/linux-sbsa/nsight_systems-linux-sbsa-2023.4.4.54-archive.tar.xz", "sha256": "d681d18a5bde7815f7bf0cd28581905b156aac52413fc8caaef472af63e80096", "md5": "45c98835ee2edf014795e8e54fa56989", "size": "189600532"}, "windows-x86_64": {"relative_path": "nsight_systems/windows-x86_64/nsight_systems-windows-x86_64-2023.4.4.54-archive.zip", "sha256": "319ac041502f4f62e3c55ad01146f2b8f51e96811bb944fcf3549b8649b67baf", "md5": "81278ddcc510f2e133c2854d617c818f", "size": "336162677"}}, "nsight_vse": {"name": "Nsight Visual Studio Edition (VSE)", "license": "NVIDIA SLA", "license_path": "nsight_vse/LICENSE.txt", "version": "2024.1.1.24072", "windows-x86_64": {"relative_path": "nsight_vse/windows-x86_64/nsight_vse-windows-x86_64-2024.1.1.24072-archive.zip", "sha256": "7d9a5f4175d4cc8eff2722be71bca3ff7a4e28e85a5cedd8fa283abfc3eb114e", "md5": "4bdada6c38106fd9e69a172f3e8a83eb", "size": "510208957"}}, "nvidia_driver": {"name": "NVIDIA Linux Driver", "license": "NVIDIA Driver", "license_path": "nvidia_driver/LICENSE.txt", "version": "550.54.15", "linux-x86_64": {"relative_path": "nvidia_driver/linux-x86_64/nvidia_driver-linux-x86_64-550.54.15-archive.tar.xz", "sha256": "e1416ebc1e7b4a14d393ca584e3f9c5e22f9750f4ba502a8ba2c5c5ad943bbb5", "md5": "4c757f62a0aa0d7025bd290f0f62b1f2", "size": "352810260"}, "linux-ppc64le": {"relative_path": "nvidia_driver/linux-ppc64le/nvidia_driver-linux-ppc64le-550.54.15-archive.tar.xz", "sha256": "ae53576c73f16084f3975aad254ee756e524525b1ca6d72d49d6e4b33083d818", "md5": "af099e16e7493fb762a45223ec1e3d4b", "size": "99127276"}, "linux-sbsa": {"relative_path": "nvidia_driver/linux-sbsa/nvidia_driver-linux-sbsa-550.54.15-archive.tar.xz", "sha256": "b39a8cb837fe3846284d89ab2202e93b14804349d3fc9aa8a8384fd2759ec2f5", "md5": "d5704afba3f1144108125cdb19718ce3", "size": "268726204"}}, "nvidia_fs": {"name": "NVIDIA filesystem", "license": "CUDA Toolkit", "license_path": "nvidia_fs/LICENSE.txt", "version": "2.19.7", "linux-x86_64": {"relative_path": "nvidia_fs/linux-x86_64/nvidia_fs-linux-x86_64-2.19.7-archive.tar.xz", "sha256": "8a38bc4167f7978e850dcb30311bd3e9789b7a3c12b1fcb9e112cd42b4671c52", "md5": "dd263b808034a918483a6e1f8c7ca211", "size": "58860"}, "linux-sbsa": {"relative_path": "nvidia_fs/linux-sbsa/nvidia_fs-linux-sbsa-2.19.7-archive.tar.xz", "sha256": "4c686aa77c837c1ff6d3e280fd93ff512ca73835f5fedee2d986b587629efa9a", "md5": "6daab1d6388500fe7da8652e981af65d", "size": "58880"}, "linux-aarch64": {"relative_path": "nvidia_fs/linux-aarch64/nvidia_fs-linux-aarch64-2.19.7-archive.tar.xz", "sha256": "8da41452c44e4e3e392ec30c9c2bf0da76179d4e7ea077ab9023ed89138af992", "md5": "46ffa52c9bc008b42d36624ea9241024", "size": "58888"}}, "visual_studio_integration": {"name": "CUDA Visual Studio Integration", "license": "CUDA Toolkit", "license_path": "visual_studio_integration/LICENSE.txt", "version": "12.4.127", "windows-x86_64": {"relative_path": "visual_studio_integration/windows-x86_64/visual_studio_integration-windows-x86_64-12.4.127-archive.zip", "sha256": "c89fd04ac409b93ae0d575626d5d956e3f3d9a21656765ce1c0b25e36c2a103d", "md5": "43687b1d572ae7d462b96f712cc1ea6c", "size": "518984"}}}
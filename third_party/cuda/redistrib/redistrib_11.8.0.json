{"release_date": "2022-10-03", "cuda_cccl": {"name": "CXX Core Compute Libraries", "license": "CUDA Toolkit", "version": "11.8.89", "linux-x86_64": {"relative_path": "cuda_cccl/linux-x86_64/cuda_cccl-linux-x86_64-11.8.89-archive.tar.xz", "sha256": "99d77d9e4c75d5e4663e473577f1871e65bca4ea0b9023f544a3556f0c1776c7", "md5": "01bef0511cad90660a0ff50bbb4615fe", "size": "1006416"}, "linux-ppc64le": {"relative_path": "cuda_cccl/linux-ppc64le/cuda_cccl-linux-ppc64le-11.8.89-archive.tar.xz", "sha256": "6d40a8f268ddf8befea453a827a140d6ecd1e02a437eb4ddf4fe1d7d35b66918", "md5": "ea0ba182ff91a9b641b12ea627c593e0", "size": "1006640"}, "linux-sbsa": {"relative_path": "cuda_cccl/linux-sbsa/cuda_cccl-linux-sbsa-11.8.89-archive.tar.xz", "sha256": "b7cdd513d4ee079f3ebe78ae1e156b678fa4f7df096459ae5bea8dc63db8a4f4", "md5": "708f4d01e5b5bbc2d0e8bcdea443424e", "size": "1006188"}, "windows-x86_64": {"relative_path": "cuda_cccl/windows-x86_64/cuda_cccl-windows-x86_64-11.8.89-archive.zip", "sha256": "548fe5e0cf6a64568a61713cdb475306ce7445d98dfbbe7f910fd78a7f6b616c", "md5": "b345dfa53a38008bf54ddc47af3594f7", "size": "2570742"}, "linux-aarch64": {"relative_path": "cuda_cccl/linux-aarch64/cuda_cccl-linux-aarch64-11.8.89-archive.tar.xz", "sha256": "454c6f6e30176e82590b130971b8d49931db4d16c8cd127eb7bc225e348114bd", "md5": "c401a3d74db67fa342e017f041d73736", "size": "1006656"}}, "cuda_compat": {"name": "CUDA compat L4T", "license": "CUDA Toolkit", "version": "11.8.31339915", "linux-aarch64": {"relative_path": "cuda_compat/linux-aarch64/cuda_compat-linux-aarch64-11.8.31339915-archive.tar.xz", "sha256": "7aa1b62da35b52eaa13e254d1072aff10c907416604e5e5cc1ddcebbfe341dc7", "md5": "41cba7b241724ad04234dc3f20526525", "size": "15780868"}}, "cuda_cudart": {"name": "CUDA Runtime (cudart)", "license": "CUDA Toolkit", "version": "11.8.89", "linux-x86_64": {"relative_path": "cuda_cudart/linux-x86_64/cuda_cudart-linux-x86_64-11.8.89-archive.tar.xz", "sha256": "56129e0c42df03ecb50a7bb23fc3285fa39af1a818f8826b183cf793529098bb", "md5": "1087b1284b033511c34ac3f1d42e1ecd", "size": "913876"}, "linux-ppc64le": {"relative_path": "cuda_cudart/linux-ppc64le/cuda_cudart-linux-ppc64le-11.8.89-archive.tar.xz", "sha256": "8c0cc24e09e015079accc3c37c8fffd7bbeb04a688c9958a672785ffb785ffac", "md5": "2ab98046768706eb1818c83a1dcc2bf6", "size": "855176"}, "linux-sbsa": {"relative_path": "cuda_cudart/linux-sbsa/cuda_cudart-linux-sbsa-11.8.89-archive.tar.xz", "sha256": "88f496a2f96f5bb2a9cb351e6704dfe6a45e713e571c958a3924b2a02e7adea0", "md5": "ca730f28308a18a0311f0167338455b0", "size": "855196"}, "windows-x86_64": {"relative_path": "cuda_cudart/windows-x86_64/cuda_cudart-windows-x86_64-11.8.89-archive.zip", "sha256": "988cc9e7d3785d4b1975521f312c57c6814cbf15e73a2b7941d961835f2a945e", "md5": "5b6c4db1e2c621c0061994156d35b64a", "size": "2987306"}, "linux-aarch64": {"relative_path": "cuda_cudart/linux-aarch64/cuda_cudart-linux-aarch64-11.8.89-archive.tar.xz", "sha256": "e7622a46261df6424e8cd892e1631ef3bbfae90d0aace4a63fd35cdcffa9c788", "md5": "aea3364b82bc403d589f1a62f461e8a8", "size": "819640"}}, "cuda_cuobjdump": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_cuobjdump/linux-x86_64/cuda_cuobjdump-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "28218273db8ffeb3ae4b31bfb4e4d90f0ae3373454c7970703c063dfd0377ba7", "md5": "60c880a2a3f13ce47b13d093b23bef55", "size": "162092"}, "linux-ppc64le": {"relative_path": "cuda_cuobjdump/linux-ppc64le/cuda_cuobjdump-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "c982c7dd7b6b8f9e8328ae0b67c9d7507ea58b64c893374766f77be3ce58ac6c", "md5": "3a18aab2c893cc93c27a5b84766b6438", "size": "205016"}, "linux-sbsa": {"relative_path": "cuda_cuobjdump/linux-sbsa/cuda_cuobjdump-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "a630e95396437d0a8643d0184e95ac10a7c85488eff23955c94d1270dd45af2e", "md5": "09d2c9c7b11e8f492b8ca0faabd542b7", "size": "171160"}, "windows-x86_64": {"relative_path": "cuda_cuobjdump/windows-x86_64/cuda_cuobjdump-windows-x86_64-11.8.86-archive.zip", "sha256": "9961e1770fdde91844938a7046d03d7dfa3c3ff7271f77e9e859ca84d631ebf4", "md5": "83ad84a30f896afa36d7a385776b3b75", "size": "3777109"}, "linux-aarch64": {"relative_path": "cuda_cuobjdump/linux-aarch64/cuda_cuobjdump-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "9ef1314c2e9b0149c3ffb07559cf1226bfd716515c92e6dbaf400863b3f4d44c", "md5": "4e530c57a7f4dc4c38bb982790f7b76e", "size": "170944"}}, "cuda_cupti": {"name": "CUPTI", "license": "CUDA Toolkit", "version": "11.8.87", "linux-x86_64": {"relative_path": "cuda_cupti/linux-x86_64/cuda_cupti-linux-x86_64-11.8.87-archive.tar.xz", "sha256": "b2ebc5672aa7b896b5986200d132933c37e72df6b0bf5ac25c9cb18c2c03057f", "md5": "5fc2edc95353ab45f29a411823176ca9", "size": "18049564"}, "linux-ppc64le": {"relative_path": "cuda_cupti/linux-ppc64le/cuda_cupti-linux-ppc64le-11.8.87-archive.tar.xz", "sha256": "48e3bd8f14d5846e0fff88bcd712a6bf0fc9566095ff24462bccdf43506f5d6a", "md5": "c2e083b0a944afabd0dc1432284b0cc6", "size": "9535008"}, "linux-sbsa": {"relative_path": "cuda_cupti/linux-sbsa/cuda_cupti-linux-sbsa-11.8.87-archive.tar.xz", "sha256": "d53c7e5da57d1e9df1f5bb3009e4964fbbcc8382906f64153ba4fab2ddeae607", "md5": "6c9ba6e9045d95a667fe623f9a7f9347", "size": "9307904"}, "windows-x86_64": {"relative_path": "cuda_cupti/windows-x86_64/cuda_cupti-windows-x86_64-11.8.87-archive.zip", "sha256": "a243ffc6b5cfd9ba469bc3dd02208186c4f1956e91d54e9bca295757edd9dafa", "md5": "d4fdbcf3bb3e75c334f9a6b21d4cdf5f", "size": "13045751"}, "linux-aarch64": {"relative_path": "cuda_cupti/linux-aarch64/cuda_cupti-linux-aarch64-11.8.87-archive.tar.xz", "sha256": "a7d2b993dcfdec7bf24cd8e7cee292679bc351d95bc58e99e3394593f708fa80", "md5": "a5041dd165f9ca49c16912a0bf586000", "size": "6976012"}}, "cuda_cuxxfilt": {"name": "CUDA cuxxfilt (demangler)", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_cuxxfilt/linux-x86_64/cuda_cuxxfilt-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "42e41e765fa0577c32706c9fd50016230d06e54dacb760f41ded7093923927af", "md5": "165cd45c312f49edf66d30004e242aa8", "size": "185836"}, "linux-ppc64le": {"relative_path": "cuda_cuxxfilt/linux-ppc64le/cuda_cuxxfilt-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "00699d77a701372fb06d5c0d1eb6c51858f2b1aa97ae103589f434aebaa4999f", "md5": "3159fa2ede95d25c22a15819d3265896", "size": "179528"}, "linux-sbsa": {"relative_path": "cuda_cuxxfilt/linux-sbsa/cuda_cuxxfilt-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "c3c5802ff0c9fe96db03b49be6da4298258459e067138b868378f067cf31ea65", "md5": "f48b56257116197573daddb3b8c2f78e", "size": "172016"}, "windows-x86_64": {"relative_path": "cuda_cuxxfilt/windows-x86_64/cuda_cuxxfilt-windows-x86_64-11.8.86-archive.zip", "sha256": "a852b129290c1f9084ca7b626d5972d32fe5ec190ad55878c1c0993566d369c1", "md5": "818838b992877c87396c390369143506", "size": "168499"}, "linux-aarch64": {"relative_path": "cuda_cuxxfilt/linux-aarch64/cuda_cuxxfilt-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "0f838658f46e86cddf37af16928a9f971335d03d79ddb53d71b3329e5c1834ce", "md5": "86b33cc615f1af37a45a998224e4680a", "size": "171664"}}, "cuda_demo_suite": {"name": "CUDA Demo Suite", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_demo_suite/linux-x86_64/cuda_demo_suite-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "18cd11f6b846a855f34b949aa87477f5978d1462bc4c354e6a39af929f505b72", "md5": "791ea9fa085582efac7e68b795f33f0d", "size": "3993532"}, "windows-x86_64": {"relative_path": "cuda_demo_suite/windows-x86_64/cuda_demo_suite-windows-x86_64-11.8.86-archive.zip", "sha256": "35ebaba27ba4c91962e069847ab8c355305b76139a342ac0945173658a4cbf40", "md5": "f38e557fd705098963ddd65cf407c9d5", "size": "5050011"}}, "cuda_documentation": {"name": "CUDA Documentation", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_documentation/linux-x86_64/cuda_documentation-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "45355431a1cc1edd78db903aba6e50f548cbf34dc1a77f9c56ac7c294ddd0799", "md5": "dfc70528af84c65b7262f824ee8c1289", "size": "67156"}, "linux-ppc64le": {"relative_path": "cuda_documentation/linux-ppc64le/cuda_documentation-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "7594463c636373abd1f09581b5da6767eca7d7f5218f96c700b932d9fb3ba8d3", "md5": "cee8eaafed9470a7b12da8515d77187b", "size": "67052"}, "linux-sbsa": {"relative_path": "cuda_documentation/linux-sbsa/cuda_documentation-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "41958cbfc53e170ed60140d2501a6fa00a0c2c6aa5496594ee6ee76c93b2da75", "md5": "7898fc3e98473293441ea75bf438214d", "size": "67076"}, "windows-x86_64": {"relative_path": "cuda_documentation/windows-x86_64/cuda_documentation-windows-x86_64-11.8.86-archive.zip", "sha256": "1f7b0c60be931debf0bbf1ff6ddecd8c61ae19c27ed370fabda0cbcfa2075ba5", "md5": "df6b8628ac018257fdd23c7fc8646f97", "size": "105364"}, "linux-aarch64": {"relative_path": "cuda_documentation/linux-aarch64/cuda_documentation-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "9879ba1dc577e22670d4575de80a64dd86cd02a78644af84c8aaab5f31972df2", "md5": "46f135b33cad414f6e74cfab19874a27", "size": "67100"}}, "cuda_gdb": {"name": "CUDA GDB", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_gdb/linux-x86_64/cuda_gdb-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "15252a58df4c2b09dfd3c4bf91c3aebdb2bbb84a61573d92690076ee5066bdff", "md5": "008e94bb7b3f4e0208ceea015a962262", "size": "64334476"}, "linux-ppc64le": {"relative_path": "cuda_gdb/linux-ppc64le/cuda_gdb-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "2f79d874373af9f7ff6898f28b5ef8269f2182e03ce12cd716c56dda0bad0cdd", "md5": "a13c9ea95b13bf3b70ac1d79fab1750f", "size": "64179404"}, "linux-sbsa": {"relative_path": "cuda_gdb/linux-sbsa/cuda_gdb-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "acca29e2e8d341d058bb4cad76ec8c565fe15f39205aba72f5e92d360e49a360", "md5": "e86e497ef3e6fd6b5099ba11e71c5ae5", "size": "64001800"}, "linux-aarch64": {"relative_path": "cuda_gdb/linux-aarch64/cuda_gdb-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "b4e7dde3b001019a1e4ac7646cbae48e66a9642376745335a8bc245ad91b3a2c", "md5": "827911d9bb2f98068c55111e4a6564f0", "size": "63936148"}}, "cuda_memcheck": {"name": "CUDA Memcheck", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_memcheck/linux-x86_64/cuda_memcheck-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "539ce6b3cf03593f72f7830217145c87f94246b1c8c056fde2da82234aba2a3e", "md5": "b3c4d2321f005cd7f4a2be2f647ebf5b", "size": "139812"}, "linux-ppc64le": {"relative_path": "cuda_memcheck/linux-ppc64le/cuda_memcheck-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "9f5a8ce507b2fa401180d3ca2213765069f8c5ea387f4164ea29cc32b22c9497", "md5": "19ff70b8373e4c6e545427f1733ca64f", "size": "147964"}, "windows-x86_64": {"relative_path": "cuda_memcheck/windows-x86_64/cuda_memcheck-windows-x86_64-11.8.86-archive.zip", "sha256": "387339972a16daefb5aca029d9d8d9c5f2fc8d823ccd4f4b89d2a2767f19dc2d", "md5": "d9deb261404f40461099d814c8699d2c", "size": "172894"}}, "cuda_nsight": {"name": "Nsight Eclipse Edition Plugin", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_nsight/linux-x86_64/cuda_nsight-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "4568af4eb961fba800b629b9456e4bed82eebf6e4c0c152f83e415b23983699d", "md5": "ea71a5e487e05343fda0f8317c681be3", "size": "118607548"}, "linux-ppc64le": {"relative_path": "cuda_nsight/linux-ppc64le/cuda_nsight-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "67d1a47e1b39c0969201a45bac527e597ec1fc0f268ab3a78ab0a94363be58f2", "md5": "6bbdefd52ed09fce18d909fd6f18479e", "size": "118607576"}}, "cuda_nvcc": {"name": "CUDA NVCC", "license": "CUDA Toolkit", "version": "11.8.89", "linux-x86_64": {"relative_path": "cuda_nvcc/linux-x86_64/cuda_nvcc-linux-x86_64-11.8.89-archive.tar.xz", "sha256": "7ee8450dbcc16e9fe5d2a7b567d6dec220c5894a94ac6640459e06231e3b39a5", "md5": "ea3b1b2afc8cfa824328adbe998a4a76", "size": "43230952"}, "linux-ppc64le": {"relative_path": "cuda_nvcc/linux-ppc64le/cuda_nvcc-linux-ppc64le-11.8.89-archive.tar.xz", "sha256": "16fcfac1ef89584a36bf725b1706c51ecf9754acc712600f5f3e70f6ba119c8c", "md5": "bee55785b363cbec80cafd90d750aae8", "size": "40307408"}, "linux-sbsa": {"relative_path": "cuda_nvcc/linux-sbsa/cuda_nvcc-linux-sbsa-11.8.89-archive.tar.xz", "sha256": "17d30d924a1d6ff5748a0b6a4e7c25fd39644250cef9184fba520362f70ff798", "md5": "dbaf022f1014ce621935c8bbb96113f0", "size": "39022020"}, "windows-x86_64": {"relative_path": "cuda_nvcc/windows-x86_64/cuda_nvcc-windows-x86_64-11.8.89-archive.zip", "sha256": "4cdd7555f31186e5af0b14ab761838bbc8b5e6441589f5bb326930c7a502dcd3", "md5": "240a8b9fca8d478aed61d9863e2cf4d3", "size": "57346486"}, "linux-aarch64": {"relative_path": "cuda_nvcc/linux-aarch64/cuda_nvcc-linux-aarch64-11.8.89-archive.tar.xz", "sha256": "e6cd1a039b5318cabc848840f0e660c4e0971186ae84ff0b2a56246b898ace1e", "md5": "e3974c22515f9f20c44d9225de994696", "size": "39063696"}}, "cuda_nvdisasm": {"name": "CUDA nvdisasm", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_nvdisasm/linux-x86_64/cuda_nvdisasm-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "9c1a92d012ec7e63334863a70f8c48b25d3a7df13253813318454358eeaa4400", "md5": "76f004fb938f650841744b54fba3e0a1", "size": "50769012"}, "linux-ppc64le": {"relative_path": "cuda_nvdisasm/linux-ppc64le/cuda_nvdisasm-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "af86ce4c1a69be36b3d3363cbf2c47d752e916bf2690b7d7a845d78da10a02c0", "md5": "3892df811a27b3566f447617b333aba9", "size": "50762364"}, "linux-sbsa": {"relative_path": "cuda_nvdisasm/linux-sbsa/cuda_nvdisasm-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "8e6f10a708937283919ebd57ba55a5a3575a751c92f63ac9a99f5bcfda8ac1dc", "md5": "ab35abb462f5eed823244a54341f966f", "size": "50707044"}, "windows-x86_64": {"relative_path": "cuda_nvdisasm/windows-x86_64/cuda_nvdisasm-windows-x86_64-11.8.86-archive.zip", "sha256": "56888ecebbac419f1d5e91bff33ea1268fda12a3ce8818b0c6f360521cf07152", "md5": "f6fc3655bed1293c8ff9bc96f06ecab9", "size": "51000989"}, "linux-aarch64": {"relative_path": "cuda_nvdisasm/linux-aarch64/cuda_nvdisasm-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "578604e16d2c687a41fe1beb9eff44a72ad7e0ae9acc147fe28c37e1d3962f8a", "md5": "67ae5c58f02a38a90159563438f8bf4b", "size": "50697028"}}, "cuda_nvml_dev": {"name": "CUDA NVML Headers", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_nvml_dev/linux-x86_64/cuda_nvml_dev-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "2a03b591f7e6714811f34f807a76be1dea7d68788c898ab4a21ec2ccecf2e368", "md5": "03ab04f1f7ff9557e4eafa22d3600cee", "size": "78320"}, "linux-ppc64le": {"relative_path": "cuda_nvml_dev/linux-ppc64le/cuda_nvml_dev-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "b6b067595b9721409092b44d1fc0b5373a0368faed984150aa27545f96adc1dd", "md5": "0f93570ff9c5ab184755dc4be71aa7e9", "size": "78388"}, "linux-sbsa": {"relative_path": "cuda_nvml_dev/linux-sbsa/cuda_nvml_dev-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "4b759ba07830b6394cf6d28c0e0e1a3e8bf88adfd5df575812dc1e1f9308f6d5", "md5": "930827da97dd8f43a17bdf395e8bfb7e", "size": "78948"}, "windows-x86_64": {"relative_path": "cuda_nvml_dev/windows-x86_64/cuda_nvml_dev-windows-x86_64-11.8.86-archive.zip", "sha256": "8eb977d7ed61eaa70a32963f1c2bd63ef92710a5a6486800125dec4ed8ebd6fb", "md5": "8b4e968ead1fd332feedacb692009c57", "size": "110045"}, "linux-aarch64": {"relative_path": "cuda_nvml_dev/linux-aarch64/cuda_nvml_dev-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "233c4f3ed5429930284b32c2b755ca01c4f2899e1dbb9036c738af85c874d53b", "md5": "eca97d5c09108fcccc8e5ce10e9dedee", "size": "78916"}}, "cuda_nvprof": {"name": "CUDA nvprof", "license": "CUDA Toolkit", "version": "11.8.87", "linux-x86_64": {"relative_path": "cuda_nvprof/linux-x86_64/cuda_nvprof-linux-x86_64-11.8.87-archive.tar.xz", "sha256": "cc01bc16f11b3aca89539a750c458121a4390d7694842627ca0221cc0b537107", "md5": "a55fb3f318f5ea9fbdbfeb775952554f", "size": "1955928"}, "linux-ppc64le": {"relative_path": "cuda_nvprof/linux-ppc64le/cuda_nvprof-linux-ppc64le-11.8.87-archive.tar.xz", "sha256": "8e3ec9c4da81e88033e1ce013a995ac51a7c5e158c7fbbae8383e706356c244a", "md5": "adf1828636a2c57333434d62aa725767", "size": "1608680"}, "windows-x86_64": {"relative_path": "cuda_nvprof/windows-x86_64/cuda_nvprof-windows-x86_64-11.8.87-archive.zip", "sha256": "24f0cdf3692241efb8948230ea82b57245ae9654fafdcbea31314b06a7527580", "md5": "b1ffe59994228212c4d58189a9e9cd31", "size": "1599731"}}, "cuda_nvprune": {"name": "CUDA nvprune", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_nvprune/linux-x86_64/cuda_nvprune-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "6165a58e3b17dba210eb7fa6bab0b7c82aa83d6584e21adc54e9ce820f4a02b2", "md5": "f6bb6d9a16863a54c12c79796c711dee", "size": "55788"}, "linux-ppc64le": {"relative_path": "cuda_nvprune/linux-ppc64le/cuda_nvprune-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "ee82495f51873831b5448b6181c05d1d8ef3abb7aa5d9e93c7e4f47fd1e0ee49", "md5": "850be2894997205633df0f20d651b488", "size": "56360"}, "linux-sbsa": {"relative_path": "cuda_nvprune/linux-sbsa/cuda_nvprune-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "c113d2340e4c91f7ee32e123f6a7736a070b79521bf33787a066fbb626790954", "md5": "56578ad334bc57ee224eba840f6e055f", "size": "48008"}, "windows-x86_64": {"relative_path": "cuda_nvprune/windows-x86_64/cuda_nvprune-windows-x86_64-11.8.86-archive.zip", "sha256": "75f77f308dfd216925e3ec02b2a2a0631d3cc72e023ba52b29b902f508dc6bf0", "md5": "12512ae51bfedba3cb6767eff3435d7a", "size": "145633"}, "linux-aarch64": {"relative_path": "cuda_nvprune/linux-aarch64/cuda_nvprune-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "090030bc5e4b65cb2d64cdb10964ae555b1db2f3a1c9446db17bf901c303b3f1", "md5": "8e6be3ba89e40ba208e4c6959ad11564", "size": "47924"}}, "cuda_nvrtc": {"name": "CUDA NVRTC", "license": "CUDA Toolkit", "version": "11.8.89", "linux-x86_64": {"relative_path": "cuda_nvrtc/linux-x86_64/cuda_nvrtc-linux-x86_64-11.8.89-archive.tar.xz", "sha256": "4bde6bdd6550110b91a5b8e442579c26ddf3a4bc9d380bed03daee8bf70a5286", "md5": "f09fddad27e3d6896f472fcd37df2e61", "size": "29507552"}, "linux-ppc64le": {"relative_path": "cuda_nvrtc/linux-ppc64le/cuda_nvrtc-linux-ppc64le-11.8.89-archive.tar.xz", "sha256": "c4c305c31b38afb66e69c522263e6c04e8a08425330eebf7323a9f9d489d5a58", "md5": "86bcf8a01a3fb1e4d00f2ea706ef189f", "size": "27515068"}, "linux-sbsa": {"relative_path": "cuda_nvrtc/linux-sbsa/cuda_nvrtc-linux-sbsa-11.8.89-archive.tar.xz", "sha256": "d81246bc36adb4664a816ebebd2a572b92a74b3a36a830454fc91a13bdad7d18", "md5": "700eff66b08ad3fcb727abd8ca9cf814", "size": "27381644"}, "windows-x86_64": {"relative_path": "cuda_nvrtc/windows-x86_64/cuda_nvrtc-windows-x86_64-11.8.89-archive.zip", "sha256": "e5d571247e71e0b0922a929516175844efa9e7ac424ed3c1b764bffb4899d3c9", "md5": "b10471319dd70571927accc50a739781", "size": "95854990"}, "linux-aarch64": {"relative_path": "cuda_nvrtc/linux-aarch64/cuda_nvrtc-linux-aarch64-11.8.89-archive.tar.xz", "sha256": "89f3f8067b1a5812b0c46a24b4a82864516bf7026c951f8ccfe91c2b7c430596", "md5": "7dc9f9c8419d26b6c4c7d8a6322e9bc7", "size": "27383920"}}, "cuda_nvtx": {"name": "CUDA NVTX", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_nvtx/linux-x86_64/cuda_nvtx-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "d08af53e4116d5535112680c6f8a6774744c625a260bc5a64399a3be35700201", "md5": "34a29024041db12d6c39c4db19276674", "size": "48184"}, "linux-ppc64le": {"relative_path": "cuda_nvtx/linux-ppc64le/cuda_nvtx-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "e0162a4e404079650b2cdcfb21a77eca69a70a9670a68cb368bb7b567a6a78d5", "md5": "a95cb8d1ff95be59223602c44fff060d", "size": "48148"}, "linux-sbsa": {"relative_path": "cuda_nvtx/linux-sbsa/cuda_nvtx-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "b5f1835ef51e7584a0ec16ff2c573c59f91fac4defbfc78de31e93514d50e5ff", "md5": "487458d132db455e585369653d712ff7", "size": "48800"}, "windows-x86_64": {"relative_path": "cuda_nvtx/windows-x86_64/cuda_nvtx-windows-x86_64-11.8.86-archive.zip", "sha256": "133c8c61904c06f1273dac35c0d602765e6a9f14175c9572b8c76b8b3d052105", "md5": "ee20c858be84a6eb61830693f0c9d5a2", "size": "65690"}, "linux-aarch64": {"relative_path": "cuda_nvtx/linux-aarch64/cuda_nvtx-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "780c37fd80f25f15efb72827d7d439d70618b3ead5ea6ff99727b9656ef3d6ef", "md5": "0df92af46da66b19e5e488bb5130f401", "size": "48092"}}, "cuda_nvvp": {"name": "CUDA NVVP", "license": "CUDA Toolkit", "version": "11.8.87", "linux-x86_64": {"relative_path": "cuda_nvvp/linux-x86_64/cuda_nvvp-linux-x86_64-11.8.87-archive.tar.xz", "sha256": "68a1ff1118220c7e1d3852de52110b36251045635dd7c4a42eae9a6a3e31116c", "md5": "0316f5eb34c2597a21b984b32a2130fc", "size": "117590868"}, "linux-ppc64le": {"relative_path": "cuda_nvvp/linux-ppc64le/cuda_nvvp-linux-ppc64le-11.8.87-archive.tar.xz", "sha256": "1188a21ebb4f4d8a2cddffea5d6317b1863fce8ef9c9cffba678b37552e4f511", "md5": "c078f22f422a4da514a66528eea3cb42", "size": "117029624"}, "windows-x86_64": {"relative_path": "cuda_nvvp/windows-x86_64/cuda_nvvp-windows-x86_64-11.8.87-archive.zip", "sha256": "8e0f1da8541612ad5f21936a4c237fdce97d1fb4e8bc234698c15f2052db170a", "md5": "b30aaf5036a5069ffc6a796e2af0692f", "size": "120361858"}}, "cuda_profiler_api": {"name": "CUDA Profiler API", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_profiler_api/linux-x86_64/cuda_profiler_api-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "0845942ac7f6fac6081780c32e0d95c883c786638b54d5a8eda05fde8089d532", "md5": "b45edeb69dee2eea33e63517b52c1242", "size": "16140"}, "linux-ppc64le": {"relative_path": "cuda_profiler_api/linux-ppc64le/cuda_profiler_api-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "cd0d212a4a49ee1d709fcd1d46eed5b34087d91d2465e342622caf7b173b1e34", "md5": "478ec6b63bbcd8298fe6d9e8e231a98d", "size": "16144"}, "linux-sbsa": {"relative_path": "cuda_profiler_api/linux-sbsa/cuda_profiler_api-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "472bbce7395e259ac609b6591cf4f01f2c7aae0af2562f77bf1433a3f578c6ee", "md5": "56075a87654f2d9742a4a2c14618ebc2", "size": "16144"}, "windows-x86_64": {"relative_path": "cuda_profiler_api/windows-x86_64/cuda_profiler_api-windows-x86_64-11.8.86-archive.zip", "sha256": "64f9ff04d1660ca0d611c8ac60ace7124f3e647519d67f78f681277e1c9221cc", "md5": "ebd55b552f4fa46887cc9184495c40e1", "size": "20587"}, "linux-aarch64": {"relative_path": "cuda_profiler_api/linux-aarch64/cuda_profiler_api-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "567818017d8eed04c7ea5bd3d7aacadc3008e32d33773feef55260c6473f9920", "md5": "a7958e6be9d55cedbab6b245f58c950d", "size": "16144"}}, "cuda_sanitizer_api": {"name": "CUDA Compute Sanitizer API", "license": "CUDA Toolkit", "version": "11.8.86", "linux-x86_64": {"relative_path": "cuda_sanitizer_api/linux-x86_64/cuda_sanitizer_api-linux-x86_64-11.8.86-archive.tar.xz", "sha256": "d5536917cbb0e2a1a5287e57e7c47e8645117a5a514cdbfd0da686986db71e75", "md5": "5ca11ca504fae4bb3578a7ac04a3dff6", "size": "8274596"}, "linux-ppc64le": {"relative_path": "cuda_sanitizer_api/linux-ppc64le/cuda_sanitizer_api-linux-ppc64le-11.8.86-archive.tar.xz", "sha256": "b76e464506821e4643d536f79c650e07a6c42de075d124fa885e449b138f21d4", "md5": "bfbcbf2d8167824b82d74eaabe4260f6", "size": "7715068"}, "linux-sbsa": {"relative_path": "cuda_sanitizer_api/linux-sbsa/cuda_sanitizer_api-linux-sbsa-11.8.86-archive.tar.xz", "sha256": "00975421bfa738b026ee1d89d41b76456d221cfe5737399604aca473f89ff922", "md5": "c4030e1425847287f84b58a444af19e8", "size": "6459140"}, "windows-x86_64": {"relative_path": "cuda_sanitizer_api/windows-x86_64/cuda_sanitizer_api-windows-x86_64-11.8.86-archive.zip", "sha256": "24fdaaa3a80dc1faea90a49213bef2098f0abbad8bd5108fada5b77d7ad00dcc", "md5": "14aab57c462477036ba60f88e59fc415", "size": "13572050"}, "linux-aarch64": {"relative_path": "cuda_sanitizer_api/linux-aarch64/cuda_sanitizer_api-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "0b1ec1096f87a796a0352188b89ac85bce19e97af504b72a2684f254de667d1e", "md5": "6dfc8e796940d22fabd195c74d4f2b78", "size": "3320104"}}, "fabricmanager": {"name": "NVIDIA Fabric Manager", "license": "NVIDIA Driver", "version": "520.61.05", "linux-x86_64": {"relative_path": "fabricmanager/linux-x86_64/fabricmanager-linux-x86_64-520.61.05-archive.tar.xz", "sha256": "a3c29b9a483ba9ccca41c95a1af1325cdcc4396abd6694199fdb3279f7e71221", "md5": "7f90460c03ed9cbe4a50bdfb0bc8adf3", "size": "1612804"}, "linux-sbsa": {"relative_path": "fabricmanager/linux-sbsa/fabricmanager-linux-sbsa-520.61.05-archive.tar.xz", "sha256": "9333e7c4584b6edd73c497f1666afd4d1c8c4a36e2de8c9ef36aeebf22cd2b07", "md5": "54fa3cce18980ef9b3f764a9ba0b51cf", "size": "1494656"}}, "libcublas": {"name": "CUDA cuBLAS", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcublas/linux-x86_64/libcublas-linux-x86_64-*********-archive.tar.xz", "sha256": "045e6455c9f8789b1c7ced19957c7904d23c221f4d1d75bb574a2c856aebae98", "md5": "86f56e585870e5a95d173ab30d866d9c", "size": "500681532"}, "linux-ppc64le": {"relative_path": "libcublas/linux-ppc64le/libcublas-linux-ppc64le-*********-archive.tar.xz", "sha256": "27b07d1fa375404ed0f7ce37573de1c8a5ff8c313b9f388ee7b4ff41d4a8409f", "md5": "c6b15c77cbd467d4fa3dc4c97dbf2aaa", "size": "377908948"}, "linux-sbsa": {"relative_path": "libcublas/linux-sbsa/libcublas-linux-sbsa-*********-archive.tar.xz", "sha256": "38fe90cbbc7da3dbdcd8c29e0fcd60f69baf580d9b3f71a4ee102e3c7fc30b3d", "md5": "87306fc3764e990423d21bfe4153bcc8", "size": "377934916"}, "windows-x86_64": {"relative_path": "libcublas/windows-x86_64/libcublas-windows-x86_64-*********-archive.zip", "sha256": "67b0934a6359e4ee26fff823c356021589d392c4fd49ca12624f570edc08e2b9", "md5": "1915e7979597f6b877f24f03364eb0ca", "size": "420850025"}, "linux-aarch64": {"relative_path": "libcublas/linux-aarch64/libcublas-linux-aarch64-*********-archive.tar.xz", "sha256": "05252a76ee24a73b4def52a52c3a4d08e790f3956b020dfaba56af0cc169b08a", "md5": "e87d3390d507b22b8bafe94fb79fa110", "size": "288337012"}}, "libcudla": {"name": "cuDLA", "license": "CUDA Toolkit", "version": "11.8.86", "linux-aarch64": {"relative_path": "libcudla/linux-aarch64/libcudla-linux-aarch64-11.8.86-archive.tar.xz", "sha256": "2fedefe9ebd567767e0079e168155f643100b7bf4ff6331c14f791290c932614", "md5": "14b0a2506fa1377d54b5fefe3acf5420", "size": "65508"}}, "libcufft": {"name": "CUDA cuFFT", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcufft/linux-x86_64/libcufft-linux-x86_64-*********-archive.tar.xz", "sha256": "eadca0b30a4a2c1f741fde88d6dd611604e488fdb51c676861eabc08d2c4612f", "md5": "3bca3ded75663fa9c1924ba09c3cdc14", "size": "274730492"}, "linux-ppc64le": {"relative_path": "libcufft/linux-ppc64le/libcufft-linux-ppc64le-*********-archive.tar.xz", "sha256": "c2203e0e48733acf40b76a7a3ff15d105d8c2f02dc8bb2865eb814e091ba0c5a", "md5": "1f488aeeef7a93c08ac547b101c042e1", "size": "274679080"}, "linux-sbsa": {"relative_path": "libcufft/linux-sbsa/libcufft-linux-sbsa-*********-archive.tar.xz", "sha256": "e2bec93081e31ee2f0234d2fa93c2b501de29d2143fae287fe729c3318811e56", "md5": "23319a56cc345c5ebe2bf5c4d7cbe46e", "size": "212419228"}, "windows-x86_64": {"relative_path": "libcufft/windows-x86_64/libcufft-windows-x86_64-*********-archive.zip", "sha256": "a4071a85e3983bf42ea7a2e9bebe3b0b3c9ac258668580adc32ee1c385f7556f", "md5": "8d2069024c2bc29a2a0f84645a76f76a", "size": "168982770"}, "linux-aarch64": {"relative_path": "libcufft/linux-aarch64/libcufft-linux-aarch64-*********-archive.tar.xz", "sha256": "7337babe858b3b9d267603207da5e450d24d7fdd8173c4c5d303f6586e83611c", "md5": "ff1d058b48df190318f44004ae1d5013", "size": "264578816"}}, "libcufile": {"name": "CUDA cuFile", "license": "CUDA Toolkit", "version": "********", "linux-x86_64": {"relative_path": "libcufile/linux-x86_64/libcufile-linux-x86_64-********-archive.tar.xz", "sha256": "c926846769a63f6626c3f0006cc4d82306850ec8aa3be3216458116a551fe76a", "md5": "8bf5d11a64b95bbf53ccee02830358c3", "size": "39957500"}, "linux-aarch64": {"relative_path": "libcufile/linux-aarch64/libcufile-linux-aarch64-********-archive.tar.xz", "sha256": "bf434cf2ac47911daf10ee837ee7f9cc91cb2bbc83ad4ec004765b9c264d39ae", "md5": "8af06935ae790bff51775615e546a398", "size": "40473296"}}, "libcurand": {"name": "CUDA cuRAND", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcurand/linux-x86_64/libcurand-linux-x86_64-*********-archive.tar.xz", "sha256": "9d30be251c1a0463b52203f6514dac5062844c606d13e234d1386e80c83db279", "md5": "60021684fd162fbf75db4b687de5debc", "size": "83523868"}, "linux-ppc64le": {"relative_path": "libcurand/linux-ppc64le/libcurand-linux-ppc64le-*********-archive.tar.xz", "sha256": "7349ddfc41ceb2f80cd13bc0e26447f05eaf540ae55110cf8b8774ed2860228b", "md5": "cc395eea8203f26e186eadff339d0be7", "size": "83574916"}, "linux-sbsa": {"relative_path": "libcurand/linux-sbsa/libcurand-linux-sbsa-*********-archive.tar.xz", "sha256": "3df3571103b056ab354e616f1c0737b1b2a25a7875b98b1b9bf32dee94449699", "md5": "402182e6ca2bbbdebc618c8a38141873", "size": "83497320"}, "windows-x86_64": {"relative_path": "libcurand/windows-x86_64/libcurand-windows-x86_64-*********-archive.zip", "sha256": "aaccf56d68a63378edc05109c233ed47e185237c8d334f9df136923440a9a6b7", "md5": "f693dc58062505b2f40e9255ff920b4d", "size": "56863367"}, "linux-aarch64": {"relative_path": "libcurand/linux-aarch64/libcurand-linux-aarch64-*********-archive.tar.xz", "sha256": "56411f5ce1f7c8a0a6a9db0b50f3454321226ad82abf6a189b176efd86587b77", "md5": "01ef8ebea1eb265284382245ebdb72f1", "size": "82232816"}}, "libcusolver": {"name": "CUDA cuSOLVER", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcusolver/linux-x86_64/libcusolver-linux-x86_64-*********-archive.tar.xz", "sha256": "ed136d960d28001fef1fe896aab56ea3e6a886970ab732274c9306e1bec88c96", "md5": "ce3c0bb9a696bbec942b0f3ba542fe08", "size": "85082320"}, "linux-ppc64le": {"relative_path": "libcusolver/linux-ppc64le/libcusolver-linux-ppc64le-*********-archive.tar.xz", "sha256": "fa3bcc0a9b1fb8c9c4d9c866284c561be765f101175a37aaaf6b6c25e584dfa1", "md5": "b4dc0b612c07f60fa06f411ac4522c67", "size": "85064564"}, "linux-sbsa": {"relative_path": "libcusolver/linux-sbsa/libcusolver-linux-sbsa-*********-archive.tar.xz", "sha256": "554a404bc39eb8369b5ad90cc7bb45fdb33dae509bd9a34cb0cbeff831d8569a", "md5": "bbb50591bf44a4de96932ddf97072ebb", "size": "84284636"}, "windows-x86_64": {"relative_path": "libcusolver/windows-x86_64/libcusolver-windows-x86_64-*********-archive.zip", "sha256": "965298e47640b643827cd6fe4ca5cab3c5a97b4bedc5357e9b6551298d1f9c2c", "md5": "23ba061f4482a84e72abcf7df8b544ec", "size": "120198030"}, "linux-aarch64": {"relative_path": "libcusolver/linux-aarch64/libcusolver-linux-aarch64-*********-archive.tar.xz", "sha256": "25010c938487032feb4ff8efbe9e60d9cc2fe68e865ce263be0a2542929d16be", "md5": "a440363c729a49b30551888d3c816ed5", "size": "70468000"}}, "libcusparse": {"name": "CUDA cuSPARSE", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcusparse/linux-x86_64/libcusparse-linux-x86_64-*********-archive.tar.xz", "sha256": "9250fe539d4bd6a378581dc0b528e8cfc418b57f28545bf39d70cae762075df7", "md5": "93b1c707413b5de5257190faf793047e", "size": "227085840"}, "linux-ppc64le": {"relative_path": "libcusparse/linux-ppc64le/libcusparse-linux-ppc64le-*********-archive.tar.xz", "sha256": "1072e26dc118cbf9d6f061eddbff45f2da2eef6c87c2b8a64fd1586af91a2735", "md5": "1bfca7f1de356eea5da55adc425bf3f5", "size": "227171492"}, "linux-sbsa": {"relative_path": "libcusparse/linux-sbsa/libcusparse-linux-sbsa-*********-archive.tar.xz", "sha256": "191ae1f26b15955b743f6c4978c8562b973b16671a9f684368d501919f906ce5", "md5": "1804e51f97d6d5cd8e9b8692202efa15", "size": "226831828"}, "windows-x86_64": {"relative_path": "libcusparse/windows-x86_64/libcusparse-windows-x86_64-*********-archive.zip", "sha256": "b51a46f4f6bb9476ffe433a1dedad2c94a546c8a92e70dfed63207b64ff57e50", "md5": "e8914191f10a4df1e9c869431c9aed0c", "size": "201218990"}, "linux-aarch64": {"relative_path": "libcusparse/linux-aarch64/libcusparse-linux-aarch64-*********-archive.tar.xz", "sha256": "a7795a1f97ea1b7c1e5753294e7ddaecc3e99e18da29e1e9adcbd73e61946365", "md5": "de93b81f33cd3887fe79970c1ab28e7c", "size": "192532892"}}, "libnpp": {"name": "CUDA NPP", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libnpp/linux-x86_64/libnpp-linux-x86_64-*********-archive.tar.xz", "sha256": "1aaacf07e4df2dda5f6246763fc89c1bb9af9d4723596f4530826bcae148f9b4", "md5": "d30d8c48b7d0a836fc2386ebc9d0b471", "size": "200538792"}, "linux-ppc64le": {"relative_path": "libnpp/linux-ppc64le/libnpp-linux-ppc64le-*********-archive.tar.xz", "sha256": "cc559ab9c4972e331b1b90b7ee6ab909c80f818a6f522885109f800ed6d9db1e", "md5": "af75693a60e88ae68fee15e622bfb32a", "size": "200739960"}, "linux-sbsa": {"relative_path": "libnpp/linux-sbsa/libnpp-linux-sbsa-*********-archive.tar.xz", "sha256": "cb5608a2a52fbe316bf89373e47c5b801ee4cbdbe8eaea777f8c4dcf4225c65e", "md5": "6d4cb57caf765c53beba968626c56a10", "size": "199830000"}, "windows-x86_64": {"relative_path": "libnpp/windows-x86_64/libnpp-windows-x86_64-*********-archive.zip", "sha256": "5cd2ba50234b0743242bab212bf742c114e274fd639ec9d79fd62e1295756d32", "md5": "e931a386b39e10e7ab335841823f3e6e", "size": "160781198"}, "linux-aarch64": {"relative_path": "libnpp/linux-aarch64/libnpp-linux-aarch64-*********-archive.tar.xz", "sha256": "1ef920c64610e03933ed94fc1dd34377c298c35bca83b9e864114dd7ad512c58", "md5": "f58c5fd842623e8babc6b46a5dd83a1e", "size": "174774196"}}, "libnvidia_nscq": {"name": "NVIDIA NSCQ API", "license": "NVIDIA Driver", "version": "520.61.05", "linux-x86_64": {"relative_path": "libnvidia_nscq/linux-x86_64/libnvidia_nscq-linux-x86_64-520.61.05-archive.tar.xz", "sha256": "3041cc4b7486e320467bab16350cf51acb84055b36df518b835dd801e18a1ec6", "md5": "dfc5430590addc9cc5727d57739d78be", "size": "339124"}, "linux-sbsa": {"relative_path": "libnvidia_nscq/linux-sbsa/libnvidia_nscq-linux-sbsa-520.61.05-archive.tar.xz", "sha256": "c28cc5574846305479a3a2438278cf9ef67c640105a24f25c2cb2b92eebc16f0", "md5": "4fd3355154c6bc70769189f9218ded28", "size": "307812"}}, "libnvjpeg": {"name": "CUDA nvJPEG", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libnvjpeg/linux-x86_64/libnvjpeg-linux-x86_64-*********-archive.tar.xz", "sha256": "2dd496ef4f974cf73ef293fd3de3b5b5abcaaf36150304c4f7bd0228e3e34e9d", "md5": "0efa17e6a939eaf65268580725fff407", "size": "2084164"}, "linux-ppc64le": {"relative_path": "libnvjpeg/linux-ppc64le/libnvjpeg-linux-ppc64le-*********-archive.tar.xz", "sha256": "2f3b7468c0b20a8de9fe6c93456cf5405f1eab70482964e35bf732aaa09ccaf0", "md5": "8aa8bb109c68e9e9b9db4393cceb6f0c", "size": "2098644"}, "linux-sbsa": {"relative_path": "libnvjpeg/linux-sbsa/libnvjpeg-linux-sbsa-*********-archive.tar.xz", "sha256": "bab943ceddc0d7103b0777953dca8cfb66db35b54fcee75187397345ada6e112", "md5": "853fde580b85d0e5d095a9dc76d72f25", "size": "1929336"}, "windows-x86_64": {"relative_path": "libnvjpeg/windows-x86_64/libnvjpeg-windows-x86_64-*********-archive.zip", "sha256": "caddf3d887a5bfb7db32757016fce18922838c889c80e910d91edd0644039116", "md5": "876752c9a9da6109f5419ff4d1b1324d", "size": "2054090"}}, "nsight_compute": {"name": "Nsight Compute", "license": "NVIDIA SLA", "version": "2022.3.0.22", "linux-x86_64": {"relative_path": "nsight_compute/linux-x86_64/nsight_compute-linux-x86_64-2022.3.0.22-archive.tar.xz", "sha256": "1ce06d1f7fb5b9124570db1e12a7caf0caa61d60f757c8d0bcb233f818cd3e0c", "md5": "16f6fd94b2c477c6b4c4038bd79ddc3f", "size": "578530596"}, "linux-ppc64le": {"relative_path": "nsight_compute/linux-ppc64le/nsight_compute-linux-ppc64le-2022.3.0.22-archive.tar.xz", "sha256": "e7eb2794136cec15cbfcb2d69e230e1b28164091eee886cb17182000e4ffff8b", "md5": "b0a5ae542e09a0c8f6b954804562f4ef", "size": "179631780"}, "linux-sbsa": {"relative_path": "nsight_compute/linux-sbsa/nsight_compute-linux-sbsa-2022.3.0.22-archive.tar.xz", "sha256": "95f817d0526e60a16dc918e9240bc2b4155216833b7beecde5308687d8aaaead", "md5": "e19f502868ba6a20fb6de760313f7177", "size": "336218564"}, "windows-x86_64": {"relative_path": "nsight_compute/windows-x86_64/nsight_compute-windows-x86_64-2022.3.0.22-archive.zip", "sha256": "e72b239b8be0801f6377204949fb4696bf3cc8b86327f428f4bb8cbd55f7f110", "md5": "564365913c7c6e107f7d970e573e5378", "size": "477847800"}, "linux-aarch64": {"relative_path": "nsight_compute/linux-aarch64/nsight_compute-linux-aarch64-2022.3.0.22-archive.tar.xz", "sha256": "bd1b3770c183bab6ef27e018d26db480a7d52495df1bb517b785b1732b083782", "md5": "7795118b5706d4597bfd7ee65e2abd17", "size": "697905636"}}, "nsight_nvtx": {"name": "Nsight NVTX", "license": "CUDA Toolkit", "version": "1.21018621", "windows-x86_64": {"relative_path": "nsight_nvtx/windows-x86_64/nsight_nvtx-windows-x86_64-1.21018621-archive.zip", "sha256": "d99b015bfb1308206f9d7c16ea401bf426fed3a5a99953b855fe4e68be5ed2d1", "md5": "34ee04d45cfca1c4e3cbfba0ec8f6f80", "size": "315692"}}, "nsight_systems": {"name": "Nsight Systems", "license": "NVIDIA SLA", "version": "2022.4.2.1", "linux-x86_64": {"relative_path": "nsight_systems/linux-x86_64/nsight_systems-linux-x86_64-2022.4.2.1-archive.tar.xz", "sha256": "372808c5d4e2c4b99ffe324f8947ae4f2b31ab406fd835409b3032f23198ed26", "md5": "793910c8b14cd7471a1dc7d5a5a0b3f0", "size": "196239560"}, "linux-ppc64le": {"relative_path": "nsight_systems/linux-ppc64le/nsight_systems-linux-ppc64le-2022.4.2.1-archive.tar.xz", "sha256": "ab34e5818b6bbcdd1726509738e727b9500144868c8a8f48f348824bdf3c3ce2", "md5": "dbd5ac2f2e5a72a033575e0de505de9b", "size": "52933936"}, "linux-sbsa": {"relative_path": "nsight_systems/linux-sbsa/nsight_systems-linux-sbsa-2022.4.2.1-archive.tar.xz", "sha256": "f7b1a917f279bf47caf87af1db1cf1681734fdfd00fe8fccd5bd7a2cfe6ade91", "md5": "1621ec2e6dc63821288b1196d202450e", "size": "185788748"}, "windows-x86_64": {"relative_path": "nsight_systems/windows-x86_64/nsight_systems-windows-x86_64-2022.4.2.1-archive.zip", "sha256": "0a0df11d7cb449c82d7bcf96960740df482da78e698903941e0a9643af3c7b22", "md5": "a5aa599af3a04405575f363139b52c43", "size": "384428030"}}, "nsight_vse": {"name": "Nsight Visual Studio Edition (VSE)", "license": "NVIDIA SLA", "version": "2022.3.0.22245", "windows-x86_64": {"relative_path": "nsight_vse/windows-x86_64/nsight_vse-windows-x86_64-2022.3.0.22245-archive.zip", "sha256": "0de65ab3e57a42d63422fcb52d8cc813aed70cfa6603847508475775442e778c", "md5": "385a2882cb154f2bd5e03ddd61ef1faf", "size": "535810712"}}, "nvidia_driver": {"name": "NVIDIA Linux Driver", "license": "NVIDIA Driver", "version": "520.61.05", "linux-x86_64": {"relative_path": "nvidia_driver/linux-x86_64/nvidia_driver-linux-x86_64-520.61.05-archive.tar.xz", "sha256": "c28127087bfd4a865f3c7fcf16c6e5b0c21318d19268b5289c5d825e615888b7", "md5": "3ecd427f21946334d48cca97f6c7587d", "size": "409484656"}, "linux-ppc64le": {"relative_path": "nvidia_driver/linux-ppc64le/nvidia_driver-linux-ppc64le-520.61.05-archive.tar.xz", "sha256": "0485cef7466d694900c9a6b990380c5be4504e3470dc89c6e667b7b0a6837c3c", "md5": "282e06b3fa823b133f5174dc784067bf", "size": "97602224"}, "linux-sbsa": {"relative_path": "nvidia_driver/linux-sbsa/nvidia_driver-linux-sbsa-520.61.05-archive.tar.xz", "sha256": "e8de6ffdac2be419d7b940a00a0482de63a147db0acbc5265f27027c1b8f603a", "md5": "b161fc8992e3b94a330bfc40b67a53a5", "size": "260589160"}}, "nvidia_fs": {"name": "NVIDIA filesystem", "license": "CUDA Toolkit", "version": "2.13.5", "linux-x86_64": {"relative_path": "nvidia_fs/linux-x86_64/nvidia_fs-linux-x86_64-2.13.5-archive.tar.xz", "sha256": "f3962442f26be807b358c307cba5ffc45a7d8219a532d6152e66db238d778dbf", "md5": "46ae5fef3efcb780a910f27877578117", "size": "67792"}, "linux-aarch64": {"relative_path": "nvidia_fs/linux-aarch64/nvidia_fs-linux-aarch64-2.13.5-archive.tar.xz", "sha256": "7970d053e76366e2e68aec2e61cd4eb39a749664345721742244b77f8ccbb151", "md5": "6c15f64e3c1881f344e9d6aaa4a37a69", "size": "67760"}}, "visual_studio_integration": {"name": "CUDA Visual Studio Integration", "license": "CUDA Toolkit", "version": "11.8.86", "windows-x86_64": {"relative_path": "visual_studio_integration/windows-x86_64/visual_studio_integration-windows-x86_64-11.8.86-archive.zip", "sha256": "67c847a57cc8664b2180ecbdd06b399b50cfcb871c9d04bad3ce1510485aee36", "md5": "08c19db58ba62ebc15af19f52b63a71c", "size": "517053"}}}
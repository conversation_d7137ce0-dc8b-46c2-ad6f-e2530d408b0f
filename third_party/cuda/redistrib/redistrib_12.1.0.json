{"release_date": "2023-02-28", "cuda_cccl": {"name": "CXX Core Compute Libraries", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_cccl/linux-x86_64/cuda_cccl-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "7d791721d700a9a083554eb29b9bb7ea21d2164710b7a28f40f611387047c102", "md5": "c4397c5a7e0416620ad6bbcf4ecd310f", "size": "1026000"}, "linux-ppc64le": {"relative_path": "cuda_cccl/linux-ppc64le/cuda_cccl-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "d7a7238b017bb78c0261b16b5ac43e6c389861107aefe3f84432b04edd035f7a", "md5": "7d3c9a5aace073c19391a10d05610eef", "size": "1026200"}, "linux-sbsa": {"relative_path": "cuda_cccl/linux-sbsa/cuda_cccl-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "c8a205b66e0504b89a7936015b915a237ede8ee3addddc07531b46130de00689", "md5": "882824d1a37fc2e6314ded8907f47ad7", "size": "1025632"}, "windows-x86_64": {"relative_path": "cuda_cccl/windows-x86_64/cuda_cccl-windows-x86_64-12.1.55-archive.zip", "sha256": "0ae9578c8192dbef80cf438f35dd5fa3bacf7aca4d3b9d6d447391bd1749a89f", "md5": "7aa6d86b41a5eb48f8b282f172d8ef15", "size": "2608382"}, "linux-aarch64": {"relative_path": "cuda_cccl/linux-aarch64/cuda_cccl-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "38ac6fe042fe1a511767bc22f6b2897ad570b5ba253e1d6f587251bedb5646cf", "md5": "2d8ffb6d1d50b51d8501fd57be75d81b", "size": "1026100"}}, "cuda_compat": {"name": "CUDA compat L4T", "license": "CUDA Toolkit", "version": "12.1.32432504", "linux-aarch64": {"relative_path": "cuda_compat/linux-aarch64/cuda_compat-linux-aarch64-12.1.32432504-archive.tar.xz", "sha256": "db4c19f54311d983d8759fb5762125c56c0e7b232b3235c09e3d1d006f9d194b", "md5": "2bfa2bdb0ec12599d76defaec8f21e73", "size": "16294976"}}, "cuda_cudart": {"name": "CUDA Runtime (cudart)", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_cudart/linux-x86_64/cuda_cudart-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "1c53db747aaed314a44af3ff155581ad09e4c59cc86c59284c437092f54d8211", "md5": "1e87d0f71a505f2f8ba786cc2be49c14", "size": "984008"}, "linux-ppc64le": {"relative_path": "cuda_cudart/linux-ppc64le/cuda_cudart-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "56421c7b9c2c79b491144762409aaddebf85ddaff95dce26d81b6780a203b280", "md5": "d7debf0f7eafeddfa22a71f31faf070b", "size": "974928"}, "linux-sbsa": {"relative_path": "cuda_cudart/linux-sbsa/cuda_cudart-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "f5ea90abba86167f7e7e9ac69e733d99eb6e26b0081ffd60d6c2855ef9ca390b", "md5": "514160d64197d104bc5f12a21d25326b", "size": "975512"}, "windows-x86_64": {"relative_path": "cuda_cudart/windows-x86_64/cuda_cudart-windows-x86_64-12.1.55-archive.zip", "sha256": "7fcd387cc5682b0c9caacaf64acf6707a576594a9242b2a41aee70a6ea90a3e9", "md5": "2fd7de5438d358245c134a4d3443850c", "size": "2388594"}, "linux-aarch64": {"relative_path": "cuda_cudart/linux-aarch64/cuda_cudart-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "3c19d6532bd882df575e2e28de5fa8dba4f77133328bb3d3e7ad8ecc1d0abae9", "md5": "f3f549e34ccabf6687d8bb4132e3def3", "size": "1039548"}}, "cuda_cuobjdump": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_cuobjdump/linux-x86_64/cuda_cuobjdump-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "78e9404758e923bfa12f0d21ae8eddd035c1f1cab85f4bf0e9623a603afcd0d3", "md5": "1396fb313c1732b92f1acc5ff6ed4fea", "size": "167040"}, "linux-ppc64le": {"relative_path": "cuda_cuobjdump/linux-ppc64le/cuda_cuobjdump-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "30c5a3bf662eafecccab308e42aea99d798fb8d3e3fb58a72c6295db09583e49", "md5": "4f7f1bf7b3aef73f2c8e3d9185c12506", "size": "209276"}, "linux-sbsa": {"relative_path": "cuda_cuobjdump/linux-sbsa/cuda_cuobjdump-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "8b60ffec9947580dcc8d1b3f6547ddb32d78336e2c2e7e9a5efeaa5a254ec163", "md5": "3f6d84a70e745aef9fb7ae4db90c80ed", "size": "176396"}, "windows-x86_64": {"relative_path": "cuda_cuobjdump/windows-x86_64/cuda_cuobjdump-windows-x86_64-12.1.55-archive.zip", "sha256": "a4845a4014c9c47c35649f9d444b99d5c8c960ddc222398241ab268090ec2fee", "md5": "27230b71f991f07e65543e366ea4c253", "size": "3937669"}, "linux-aarch64": {"relative_path": "cuda_cuobjdump/linux-aarch64/cuda_cuobjdump-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "df2957db2e3cc3d266b25adf1901c355d87676e3fdf92a2d922c63a3435bb3b6", "md5": "51f719af466a090da32aa3bdebe77730", "size": "176432"}}, "cuda_cupti": {"name": "CUPTI", "license": "CUDA Toolkit", "version": "12.1.62", "linux-x86_64": {"relative_path": "cuda_cupti/linux-x86_64/cuda_cupti-linux-x86_64-12.1.62-archive.tar.xz", "sha256": "e7fbcaad27ccc72643bc2c2b48aa42b886f2493cf687477e6b05411b1ef997bd", "md5": "87d9e5765c1fa2adad0c08c6052533b7", "size": "19166416"}, "linux-ppc64le": {"relative_path": "cuda_cupti/linux-ppc64le/cuda_cupti-linux-ppc64le-12.1.62-archive.tar.xz", "sha256": "508067fa4543c467cdee02289522a1c45d143579d1f4081895fb444c452ebd33", "md5": "e0ddbb38ce8fb8ae99640a74254a9949", "size": "9972348"}, "linux-sbsa": {"relative_path": "cuda_cupti/linux-sbsa/cuda_cupti-linux-sbsa-12.1.62-archive.tar.xz", "sha256": "d4ae74cf653fc32635528b62995fdf264c9cc5766a750048bae66e11828b2b64", "md5": "ba8ee4531a2d5bf1de026b321bab6ade", "size": "9787212"}, "windows-x86_64": {"relative_path": "cuda_cupti/windows-x86_64/cuda_cupti-windows-x86_64-12.1.62-archive.zip", "sha256": "12b909d676d252c43790fd453b1f74787aff8289f3b4e3d7a829d3cc573edd0c", "md5": "7b245dba4d50c338907a68fbc81a91a1", "size": "13247542"}, "linux-aarch64": {"relative_path": "cuda_cupti/linux-aarch64/cuda_cupti-linux-aarch64-12.1.62-archive.tar.xz", "sha256": "34406c7f0212784ede005fb76471c08bed07672d7f7b74d403c9c6d25c862ac7", "md5": "f518790d613d8aac4bf2269fbb6b31c1", "size": "7540444"}}, "cuda_cuxxfilt": {"name": "CUDA cuxxfilt (demangler)", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_cuxxfilt/linux-x86_64/cuda_cuxxfilt-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "9f92af653b35b9b8a521413ec51554ee6d4af4c6ac2a89f5611ca31d2f8b2c88", "md5": "f8521fec3ef7be57f35cef3c720bdb2b", "size": "188272"}, "linux-ppc64le": {"relative_path": "cuda_cuxxfilt/linux-ppc64le/cuda_cuxxfilt-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "1a6bb909913d1d814b3fe40cab55eed6acac341ba25830547b598089e389c634", "md5": "2fc2c1d3e7bfce7196b6643cde979bc1", "size": "181760"}, "linux-sbsa": {"relative_path": "cuda_cuxxfilt/linux-sbsa/cuda_cuxxfilt-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "deffff51efc58cb0935312b956944c4860a51eabf9d86a9136f7039b2a6cf01a", "md5": "1c36a0b84315c3c17443a927b58cca25", "size": "174004"}, "windows-x86_64": {"relative_path": "cuda_cuxxfilt/windows-x86_64/cuda_cuxxfilt-windows-x86_64-12.1.55-archive.zip", "sha256": "a0ca92d67969d0ce53f8d9d5dcba6aaa06a9695750e98f2db09dc912c97695f4", "md5": "865ff27c8d4219ca5ad5418617baa004", "size": "168617"}, "linux-aarch64": {"relative_path": "cuda_cuxxfilt/linux-aarch64/cuda_cuxxfilt-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "a4785c9511a86812e2183364ce15a3641071c55b24f0651fb2ef3a83778af7ae", "md5": "9a482ad390a510c766aed047b85f4e3c", "size": "173340"}}, "cuda_demo_suite": {"name": "CUDA Demo Suite", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_demo_suite/linux-x86_64/cuda_demo_suite-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "d7457d1dbe36c504a059530611209e2ddcdfa139e095f836f12057b4436f2327", "md5": "cb8e8407276836a81582f28b1eb4b2f4", "size": "3992168"}, "windows-x86_64": {"relative_path": "cuda_demo_suite/windows-x86_64/cuda_demo_suite-windows-x86_64-12.1.55-archive.zip", "sha256": "cc94f88607bbededfa685b93bb8c6e1888dbabe36ab76df514d8c2ae9013fe3c", "md5": "065100fa228e194464407ed44ba68d23", "size": "5048500"}}, "cuda_documentation": {"name": "CUDA Documentation", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_documentation/linux-x86_64/cuda_documentation-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "e2625aca349907304ce7e2cf22bd141ee8cea91cc1e4ae286cdcd6473ec8a500", "md5": "de27c75a7f0e4de24c38187ad21bbd54", "size": "67192"}, "linux-ppc64le": {"relative_path": "cuda_documentation/linux-ppc64le/cuda_documentation-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "4b4cfe1adecd56d4edad6b3f3908c88d7d3ab380e4f590cdd37bc7608ebf50d9", "md5": "071415aaaa0e1f8702b24846e84271ed", "size": "67080"}, "linux-sbsa": {"relative_path": "cuda_documentation/linux-sbsa/cuda_documentation-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "50d390a51bc69700fbd4fcd43b32d938cb9ffbea07c2cbe589970c5ac682ad41", "md5": "b6442736376be7834b7af64501bf6ea8", "size": "67104"}, "windows-x86_64": {"relative_path": "cuda_documentation/windows-x86_64/cuda_documentation-windows-x86_64-12.1.55-archive.zip", "sha256": "351b4f1382148776bb5c132d289e617c6f01e3efb391ef3cba8d6f529ccced2f", "md5": "96de2efa18a44d01fedaa7d93ff730f3", "size": "105364"}, "linux-aarch64": {"relative_path": "cuda_documentation/linux-aarch64/cuda_documentation-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "60b431af3a08b7675b4517cfeddc15c59ae8f999c696143e355410647deeb002", "md5": "26d5b20530b924cf588de5912a323a04", "size": "66948"}}, "cuda_gdb": {"name": "CUDA GDB", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_gdb/linux-x86_64/cuda_gdb-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "938c49827e3b175e7cb7d5673dc0a71de3f75e6cae9373917def831e1e88e763", "md5": "03c3ffeff0a66cfae750b41da6e45287", "size": "65678572"}, "linux-ppc64le": {"relative_path": "cuda_gdb/linux-ppc64le/cuda_gdb-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "9de74168cf7cb18f7194401b194e3cdfd3319236fac368a9d432a6098851cc95", "md5": "573d5a8c5a01d64e90d711a989b480c5", "size": "65465824"}, "linux-sbsa": {"relative_path": "cuda_gdb/linux-sbsa/cuda_gdb-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "d31f9bd5f4091af3ef6cf173dd6fd6f9ac131e88accc296bb27cf3c7382a8e65", "md5": "b32663b0ce14bb191e80691e48b6c9fd", "size": "65407956"}, "linux-aarch64": {"relative_path": "cuda_gdb/linux-aarch64/cuda_gdb-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "aef81508c4f49503b5fbb232930d5e143340674315aaabefb005ac432c9a0d05", "md5": "ebac62ab2c4d5a4498583cf2c133b28d", "size": "65291324"}}, "cuda_nsight": {"name": "Nsight Eclipse Edition Plugin", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_nsight/linux-x86_64/cuda_nsight-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "856abcd42acd10b672f3e3839864043d6b6bb7dfd7f03d2c9072460d724d7009", "md5": "7dad61e0627e5162ee4a9e582f92fbb1", "size": "118608376"}, "linux-ppc64le": {"relative_path": "cuda_nsight/linux-ppc64le/cuda_nsight-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "014ddf3fd5d4a69d138f76af288a89e7871557b6bd703a37cbd1ba0bf22ec526", "md5": "a053402dfd98ee912c53a3a1cebd6c48", "size": "118608388"}}, "cuda_nvcc": {"name": "CUDA NVCC", "license": "CUDA Toolkit", "version": "12.1.66", "linux-x86_64": {"relative_path": "cuda_nvcc/linux-x86_64/cuda_nvcc-linux-x86_64-12.1.66-archive.tar.xz", "sha256": "e24bbb861edfba8ca487f72bfbe7797a3120ccac73d7986610bbaa8352a0bdf3", "md5": "2d223ba21af531b26a9b04b8de243972", "size": "44446320"}, "linux-ppc64le": {"relative_path": "cuda_nvcc/linux-ppc64le/cuda_nvcc-linux-ppc64le-12.1.66-archive.tar.xz", "sha256": "22719d621a9c350a3d27a5fcb2594315cdfef2c4d57854ce6616437193f80fba", "md5": "1ed8431ddb090761cbf89b7249c6e94e", "size": "41303540"}, "linux-sbsa": {"relative_path": "cuda_nvcc/linux-sbsa/cuda_nvcc-linux-sbsa-12.1.66-archive.tar.xz", "sha256": "5a822d8d9e0536a041eaf9c9d0d0a6db205d9b407f0703bff6fa6e2f2259ce5c", "md5": "c46aa2e4e3c529e2777d84b28c72b194", "size": "40130120"}, "windows-x86_64": {"relative_path": "cuda_nvcc/windows-x86_64/cuda_nvcc-windows-x86_64-12.1.66-archive.zip", "sha256": "020c64acc35bc95daf51e1d65aa8c39e29bfaf4c36f3edffe29be1dbef7553f6", "md5": "2a697c1305fa0dba90647871e11f5cfc", "size": "59613253"}, "linux-aarch64": {"relative_path": "cuda_nvcc/linux-aarch64/cuda_nvcc-linux-aarch64-12.1.66-archive.tar.xz", "sha256": "510b09b94b560dc99274c47a2ce79e71a3711e6f0c39b5e5af6b3161d3c04be2", "md5": "105628ac3827882bf794234f600b6eae", "size": "40155716"}}, "cuda_nvdisasm": {"name": "CUDA nvdisasm", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_nvdisasm/linux-x86_64/cuda_nvdisasm-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "5fbc8f2b12b212aecaaf4594405f878dabd10b3e647bc904d8494c3c44c5b9be", "md5": "07377f82ddb4343cb88f2831575ccad4", "size": "49881416"}, "linux-ppc64le": {"relative_path": "cuda_nvdisasm/linux-ppc64le/cuda_nvdisasm-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "1acd2a6d10915378afb02ab1c0df08b6eabde52c0571fe4f1e096aeba26c56e2", "md5": "76315083297eeac84a26d9aaf767ba0e", "size": "49870112"}, "linux-sbsa": {"relative_path": "cuda_nvdisasm/linux-sbsa/cuda_nvdisasm-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "e29fe6120021619f0a5e051bee3d3b4b457c5512d1c40dc23d51a1aa054357a7", "md5": "4d0a75eb5f7a5451775810e77c29bb76", "size": "49810544"}, "windows-x86_64": {"relative_path": "cuda_nvdisasm/windows-x86_64/cuda_nvdisasm-windows-x86_64-12.1.55-archive.zip", "sha256": "3849e70e58c73aa42d109da7d096cda4f56ce2392f27840055d5c52279987e38", "md5": "40b062836291c6f07f2ecbbc1cbca798", "size": "50121380"}, "linux-aarch64": {"relative_path": "cuda_nvdisasm/linux-aarch64/cuda_nvdisasm-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "b87496692aec3a0dbd36009482de5ca763dfa15edf90c616b6850ee3aee7c729", "md5": "619622391738af1f01b0ea137a049f5d", "size": "49811012"}}, "cuda_nvml_dev": {"name": "CUDA NVML Headers", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_nvml_dev/linux-x86_64/cuda_nvml_dev-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "493170141aab83e96ab306c0dcc8dc21c884c4f02f6ec57549a386e2fba88301", "md5": "ef29a2a92bf5583bf7180cdee958dc27", "size": "80692"}, "linux-ppc64le": {"relative_path": "cuda_nvml_dev/linux-ppc64le/cuda_nvml_dev-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "31575998cbdedfdd1e3f17ac25a3311fef6bae968eb74d42c4cafa9bff7f939b", "md5": "31264507cdf023ffe5031de366685fcb", "size": "80608"}, "linux-sbsa": {"relative_path": "cuda_nvml_dev/linux-sbsa/cuda_nvml_dev-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "5f6002411fe4b510f7542fce2753d2784586177a51a8ab0b20489b5ca0b000c9", "md5": "a0e02adeafcbe989f53226980d2afceb", "size": "80808"}, "windows-x86_64": {"relative_path": "cuda_nvml_dev/windows-x86_64/cuda_nvml_dev-windows-x86_64-12.1.55-archive.zip", "sha256": "5c7fbe4161f804a644f5665d9511d11363327fd40c16ec79216a5f12a5faf6e3", "md5": "3cdd642e05157b70e0af4e4655e3fc7a", "size": "112623"}, "linux-aarch64": {"relative_path": "cuda_nvml_dev/linux-aarch64/cuda_nvml_dev-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "70086f6d6d32f166e1503e8d37d578319b3878c97bf7a7ac618033407b9f5c73", "md5": "aaa67ae5ea60aa49781c551d9e95bf7d", "size": "80864"}}, "cuda_nvprof": {"name": "CUDA nvprof", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_nvprof/linux-x86_64/cuda_nvprof-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "490aa1a406d8d04ffbec5746bd4fcf79548c4782bf4edbb52cebbe118931c317", "md5": "65ad12d1ae13892c9c370b1945f410f1", "size": "2439076"}, "linux-ppc64le": {"relative_path": "cuda_nvprof/linux-ppc64le/cuda_nvprof-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "f69209741beb30fd80f5e04634317e4ae7746c322d218b4c245a5d538e2c26f4", "md5": "bd250e677b7ae0c71f5f64885661f32d", "size": "2116096"}, "windows-x86_64": {"relative_path": "cuda_nvprof/windows-x86_64/cuda_nvprof-windows-x86_64-12.1.55-archive.zip", "sha256": "d00f9a20388197662df5eb19c99e68d350b4a54f3cc27dc77af051803ba2d777", "md5": "ee81b8f02b79777093eae6a36003dce8", "size": "1699164"}}, "cuda_nvprune": {"name": "CUDA nvprune", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_nvprune/linux-x86_64/cuda_nvprune-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "c0766b464eb5f3cb0ddf72e5d97568d90b7e5b994561dc85274b3e95fc093078", "md5": "ceab89130ac5826621ed40500bc97ee1", "size": "56100"}, "linux-ppc64le": {"relative_path": "cuda_nvprune/linux-ppc64le/cuda_nvprune-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "0ec082de1ff352a672ad056e1abf507ffb1783f155272528ca83a0ca8d757140", "md5": "43af53508f35591e6241f2490dacf6f9", "size": "57044"}, "linux-sbsa": {"relative_path": "cuda_nvprune/linux-sbsa/cuda_nvprune-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "aa6555b40597b2b84e8c1bcb91af1c432070afcad5d47fe225508d6b3034d30e", "md5": "ad56d25cf4347b048e37bdf5d1ecf3e6", "size": "48188"}, "windows-x86_64": {"relative_path": "cuda_nvprune/windows-x86_64/cuda_nvprune-windows-x86_64-12.1.55-archive.zip", "sha256": "ab22313fedec76723208dff4437ed5c36811e4980748a290f169226f21e9ba0c", "md5": "fececd0ec20dc94a1991d0bf15751bc9", "size": "145868"}, "linux-aarch64": {"relative_path": "cuda_nvprune/linux-aarch64/cuda_nvprune-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "eff326efb7b8b6e9af307d72be2a7b6740f6df162f608edcb896d03333d2f5a1", "md5": "e43a57a1c8562dd4deb084764b72742b", "size": "48192"}}, "cuda_nvrtc": {"name": "CUDA NVRTC", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_nvrtc/linux-x86_64/cuda_nvrtc-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "1be7da20ea352261d1bf5df80a3b62b4c6fcdf4702f4292c36f1d42137358ac0", "md5": "f832265be4d7bbcd6263d09e58d5afdf", "size": "30298648"}, "linux-ppc64le": {"relative_path": "cuda_nvrtc/linux-ppc64le/cuda_nvrtc-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "6ce232c171422bbe3b450a39d4aafb043ae453f8466acdebc666cb34f1333d47", "md5": "fc61f6c644335984034e7468c3e63d2a", "size": "27963684"}, "linux-sbsa": {"relative_path": "cuda_nvrtc/linux-sbsa/cuda_nvrtc-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "c586bbbefe95be8ebb86b5d272b7e745eb1e482e839f6ac503df5e973a3da1f8", "md5": "476055affa98c6b5423d6fc6afb6b6c4", "size": "27945772"}, "windows-x86_64": {"relative_path": "cuda_nvrtc/windows-x86_64/cuda_nvrtc-windows-x86_64-12.1.55-archive.zip", "sha256": "ad705b0186f5c43bc8aafb020d8d2fde381d7d6e540b73078ced79029c7d86bb", "md5": "6c424209a3e8ae3d237bb90cd5339a3b", "size": "97078398"}, "linux-aarch64": {"relative_path": "cuda_nvrtc/linux-aarch64/cuda_nvrtc-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "67f278ee8651ff3ee90a3fc23f4a73b0770057d470e994dbc026ffc35ae18a78", "md5": "f3cf27ca7d7f5dca7b4b4dd6d9d494aa", "size": "27941504"}}, "cuda_nvtx": {"name": "CUDA NVTX", "license": "CUDA Toolkit", "version": "12.1.66", "linux-x86_64": {"relative_path": "cuda_nvtx/linux-x86_64/cuda_nvtx-linux-x86_64-12.1.66-archive.tar.xz", "sha256": "f77b1e530ef93d8009267b070d052588fca8ccec6d4aed9bd9cc75c2e42eb0b9", "md5": "74c27b6f7448456e7382556484f7cb0e", "size": "48424"}, "linux-ppc64le": {"relative_path": "cuda_nvtx/linux-ppc64le/cuda_nvtx-linux-ppc64le-12.1.66-archive.tar.xz", "sha256": "8db8a850af0ecf19eceea01d11805db74a0c7f7e8b4df30da5474c7b06571d89", "md5": "9c8abc183f20e1d47c818656cb1452b8", "size": "48452"}, "linux-sbsa": {"relative_path": "cuda_nvtx/linux-sbsa/cuda_nvtx-linux-sbsa-12.1.66-archive.tar.xz", "sha256": "c5ca8344343eaca0afb84d71887ecc53e66469751ed88192b2b415fe37d9f9c8", "md5": "c77468a3f967a6e37b32491377922fbf", "size": "48956"}, "windows-x86_64": {"relative_path": "cuda_nvtx/windows-x86_64/cuda_nvtx-windows-x86_64-12.1.66-archive.zip", "sha256": "edc801e8d89f30bb80b6259c6ef5f40f726cbe347802a4e941b7c137bfc453fb", "md5": "a6445ccb30222b5cef90e1ae9339c471", "size": "65690"}, "linux-aarch64": {"relative_path": "cuda_nvtx/linux-aarch64/cuda_nvtx-linux-aarch64-12.1.66-archive.tar.xz", "sha256": "a27356534e8de6ab230a90b0c217a239d7dd121745aa310230e0ec0aba0a7c87", "md5": "8930a39d6223ee156e495945523e9cff", "size": "48876"}}, "cuda_nvvp": {"name": "CUDA NVVP", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_nvvp/linux-x86_64/cuda_nvvp-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "3d680edbb379bf9527013125f8c94e4a54320ea2811a03054068cd88344130db", "md5": "553630fd7e77b181f7fe3aca911132ee", "size": "117694464"}, "linux-ppc64le": {"relative_path": "cuda_nvvp/linux-ppc64le/cuda_nvvp-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "078f13a137e4ed6d637e905208e6e65e8911083bc304d3c9f99079790e960baa", "md5": "b7e6d4ad4ec22b25946f46c1bec0d905", "size": "117115144"}, "windows-x86_64": {"relative_path": "cuda_nvvp/windows-x86_64/cuda_nvvp-windows-x86_64-12.1.55-archive.zip", "sha256": "4f15b48b9c943dfa8090df7c9258eebc328e4ced076850620d5baa1fc534958a", "md5": "326f9291827648cf8bc9919476ab7019", "size": "120358146"}}, "cuda_opencl": {"name": "CUDA OpenCL", "license": "CUDA Toolkit", "version": "12.1.56", "linux-x86_64": {"relative_path": "cuda_opencl/linux-x86_64/cuda_opencl-linux-x86_64-12.1.56-archive.tar.xz", "sha256": "496ff52a6c9a9cc4d19f2d8419f72331816fae6c07b657dc0079a890e9a5eb8d", "md5": "aa72a4d0444a3ca81186e39891e8b2cd", "size": "74968"}, "windows-x86_64": {"relative_path": "cuda_opencl/windows-x86_64/cuda_opencl-windows-x86_64-12.1.56-archive.zip", "sha256": "d42f287c2355cec03dd072b0e7d747689efb8dac75c0ca360d730a6966c4e67b", "md5": "8c5d64c811217115d0e57cd705862eaf", "size": "111826"}}, "cuda_profiler_api": {"name": "CUDA Profiler API", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_profiler_api/linux-x86_64/cuda_profiler_api-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "d029f836910814ae32337bea7b341513e322f8f8e63e2fb4353e98f34bacd35c", "md5": "296633f872796dcbac8f0c9d5fa3b2ff", "size": "16060"}, "linux-ppc64le": {"relative_path": "cuda_profiler_api/linux-ppc64le/cuda_profiler_api-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "212854ade5a2e055be1559510f2b865587cc978c5a04fcd008dd43645c1d345d", "md5": "120845d7529b0886b91a7f67ed8b91f8", "size": "16056"}, "linux-sbsa": {"relative_path": "cuda_profiler_api/linux-sbsa/cuda_profiler_api-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "4016484909978e2143f25fa1e6e309f447b5965aba8447324cd0675236fd1c52", "md5": "585cda7773626d5ced210a3e95a9ae17", "size": "16052"}, "windows-x86_64": {"relative_path": "cuda_profiler_api/windows-x86_64/cuda_profiler_api-windows-x86_64-12.1.55-archive.zip", "sha256": "9cd3d797aeecdc401141dd43df0be35b3422f0fb815e0665f5d8bed487153bc6", "md5": "9b4083bbe74799aa1ed5b6662b5caf68", "size": "20075"}, "linux-aarch64": {"relative_path": "cuda_profiler_api/linux-aarch64/cuda_profiler_api-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "948045f8548b0916c3fea1362d8b3e9538327bc5cd7a9c43e46babdc9cb53b03", "md5": "b9b882cf36007ac98a2b1ac115cd0bbb", "size": "16056"}}, "cuda_sanitizer_api": {"name": "CUDA Compute Sanitizer API", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "cuda_sanitizer_api/linux-x86_64/cuda_sanitizer_api-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "20a985f25c23840f6d758ddea0fc4cf67ac0dff712c0ece4e857b6d1ae228717", "md5": "8af17099788d2401903bec132fbab8a8", "size": "8139544"}, "linux-ppc64le": {"relative_path": "cuda_sanitizer_api/linux-ppc64le/cuda_sanitizer_api-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "971e2a66121a9ff6f9889e8911657be7e5a3965489da95bc7e781f428638e6c1", "md5": "2a1f47514865102e2002548daab72faa", "size": "7505112"}, "linux-sbsa": {"relative_path": "cuda_sanitizer_api/linux-sbsa/cuda_sanitizer_api-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "c12bbec61e5a7f7d6119bf496354be5ad3f7e05b4c291e6b90334d6f678d861c", "md5": "cb4d9d5c47c6c46d54e6a45acaad31b9", "size": "6129640"}, "windows-x86_64": {"relative_path": "cuda_sanitizer_api/windows-x86_64/cuda_sanitizer_api-windows-x86_64-12.1.55-archive.zip", "sha256": "e9d7976243b20c9797ad009ed05d2c748298f4158aa21c5abb4f35af987594ec", "md5": "4c1f2739dd055b8f406f55379b5a5b54", "size": "13777144"}, "linux-aarch64": {"relative_path": "cuda_sanitizer_api/linux-aarch64/cuda_sanitizer_api-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "fe505ec93f3206f862984bd4e38643743183cd40b1a113edfaf62cba0d8485fc", "md5": "ef997df5e7aeab969d5272b677e0ed8a", "size": "3490512"}}, "fabricmanager": {"name": "NVIDIA Fabric Manager", "license": "NVIDIA Driver", "version": "530.30.02", "linux-x86_64": {"relative_path": "fabricmanager/linux-x86_64/fabricmanager-linux-x86_64-530.30.02-archive.tar.xz", "sha256": "8829106dc8f6fbd43555c91bee42b24fd83f03a8866f9832a0d39762e7075e18", "md5": "c09a93d9534e3fb624c99de82992ff75", "size": "1640772"}, "linux-sbsa": {"relative_path": "fabricmanager/linux-sbsa/fabricmanager-linux-sbsa-530.30.02-archive.tar.xz", "sha256": "279bcd0d2a0d9bc01a64bfea7b0c307a462c9e89724a3735d9a77a670ab9f56c", "md5": "dc5346ae2383541e954c8b9ffffe131c", "size": "1517396"}}, "libcublas": {"name": "CUDA cuBLAS", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcublas/linux-x86_64/libcublas-linux-x86_64-*********-archive.tar.xz", "sha256": "ab208b528b16f05a76d7dac47311bf8b41e175abcce87e629218d48399b1b8bb", "md5": "2922dab44e6bd7d8ad3131c3153f4bda", "size": "459154876"}, "linux-ppc64le": {"relative_path": "libcublas/linux-ppc64le/libcublas-linux-ppc64le-*********-archive.tar.xz", "sha256": "db2b167cf134c861c4c694fc8af45b84b036fbeea462834830fa9f8f1090ca53", "md5": "8faa15a33f8fd8dbddfc6033317307e4", "size": "382198576"}, "linux-sbsa": {"relative_path": "libcublas/linux-sbsa/libcublas-linux-sbsa-*********-archive.tar.xz", "sha256": "490f9d77c4d428e8a3a05719618680ffd6dd5fb4663d2911faa99e3fcff820ae", "md5": "4a4be40ac311558b41b6317786034d0e", "size": "456809552"}, "windows-x86_64": {"relative_path": "libcublas/windows-x86_64/libcublas-windows-x86_64-*********-archive.zip", "sha256": "b4266203281047c65528318d80b168272c0be0dc1f220e452b9375f32c1534bc", "md5": "97c8a0ce4a7c996c436dfdcbbee89ac6", "size": "390181512"}, "linux-aarch64": {"relative_path": "libcublas/linux-aarch64/libcublas-linux-aarch64-*********-archive.tar.xz", "sha256": "ee75f6ccbd25bb6aebebf88861b31a84dd21d7694195ca5467c2474f559a4cbb", "md5": "c2d0b186f301262a40697830aeae6239", "size": "437144256"}}, "libcudla": {"name": "cuDLA", "license": "CUDA Toolkit", "version": "12.1.55", "linux-aarch64": {"relative_path": "libcudla/linux-aarch64/libcudla-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "e35d68a6e2d379dc09094f63204ae43f2338263809a47a61e3c0ce31c1bd8975", "md5": "1025db45db894e73fffd417618554a55", "size": "38752"}}, "libcufft": {"name": "CUDA cuFFT", "license": "CUDA Toolkit", "version": "********", "linux-x86_64": {"relative_path": "libcufft/linux-x86_64/libcufft-linux-x86_64-********-archive.tar.xz", "sha256": "8707e214978209ba85bbb33491e7e94f61273e008f5ffbb98e87d708ad8f0025", "md5": "e23d6733dc49b156f884d5cbd61b752c", "size": "168605528"}, "linux-ppc64le": {"relative_path": "libcufft/linux-ppc64le/libcufft-linux-ppc64le-********-archive.tar.xz", "sha256": "a37900ca938430b2aef02a4eed2f8c66dc20022f3c575f5334197ff3f4fed387", "md5": "4a880a49d6ba5bb7190db26b17c22791", "size": "168798168"}, "linux-sbsa": {"relative_path": "libcufft/linux-sbsa/libcufft-linux-sbsa-********-archive.tar.xz", "sha256": "3d6740239c7ef30dd20d8446b431e75642f517563fed970a49423a27e8b8028a", "md5": "0da94f1779e32396461e6643f8e4ace9", "size": "168119044"}, "windows-x86_64": {"relative_path": "libcufft/windows-x86_64/libcufft-windows-x86_64-********-archive.zip", "sha256": "349a61145e973842f246d82fcf45c0d060412bb5b0c41e6f86273b1bb449b9ed", "md5": "8864e55009976e71932125d1a3538382", "size": "119759256"}, "linux-aarch64": {"relative_path": "libcufft/linux-aarch64/libcufft-linux-aarch64-********-archive.tar.xz", "sha256": "83512271c6f3659e4a852e60cc55a038d0d78bf373e3ba4612fab78c847c22f8", "md5": "756adbebc454a72ff567d487c5edaf32", "size": "168762956"}}, "libcufile": {"name": "CUDA cuFile", "license": "CUDA Toolkit", "version": "********", "linux-x86_64": {"relative_path": "libcufile/linux-x86_64/libcufile-linux-x86_64-********-archive.tar.xz", "sha256": "f5766f72849a337f4d265e0503bd6d99b7cb757f9bd8781e81191b1e601c879c", "md5": "b80c08544f877d59ddf72a9a070b9489", "size": "40932608"}, "linux-aarch64": {"relative_path": "libcufile/linux-aarch64/libcufile-linux-aarch64-********-archive.tar.xz", "sha256": "437f72f70008d6acbd4199934f5984e3cd39d5c29ee0a9d17e469c9170d83833", "md5": "36c5a596defff3520d18ca3747887878", "size": "40602748"}}, "libcurand": {"name": "CUDA cuRAND", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcurand/linux-x86_64/libcurand-linux-x86_64-*********-archive.tar.xz", "sha256": "c1af95bac38e4849615b1eb717e0e7b20b79482cebeb8a9e426565492a786647", "md5": "3bbc2d642aa81a0ea4b1ea86401be3ac", "size": "81956236"}, "linux-ppc64le": {"relative_path": "libcurand/linux-ppc64le/libcurand-linux-ppc64le-*********-archive.tar.xz", "sha256": "848108b7d0b118d80cbeb84a10947b3187c8ffd01b8d4cf4f500c7e5ca41fc9b", "md5": "fe100f211fd8b39e376cf4354efd3bf3", "size": "82002280"}, "linux-sbsa": {"relative_path": "libcurand/linux-sbsa/libcurand-linux-sbsa-*********-archive.tar.xz", "sha256": "c42fd0a1260e1b69010819e4706a8faeda82ae25fe55dba5fad884e668937832", "md5": "80bc6f26ea750e9fe5bae48cf69a5363", "size": "81945492"}, "windows-x86_64": {"relative_path": "libcurand/windows-x86_64/libcurand-windows-x86_64-*********-archive.zip", "sha256": "e7f8eec9ae8d843270dc1090c232537f921c602838d9bd1a90147d3101788031", "md5": "87da0c59cd60579875139987e9b495a1", "size": "55282409"}, "linux-aarch64": {"relative_path": "libcurand/linux-aarch64/libcurand-linux-aarch64-*********-archive.tar.xz", "sha256": "281a459521d593b63175229b92b39379a5e89e10bfc97fb40b70262d36c1f208", "md5": "899b829bd4bffd12c0e0b6f850a7b7b3", "size": "84133236"}}, "libcusolver": {"name": "CUDA cuSOLVER", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcusolver/linux-x86_64/libcusolver-linux-x86_64-*********-archive.tar.xz", "sha256": "3b70e5ae4af33c6410de8ef1951ab440492a8c968c66d4d07b9009d32dff5c1c", "md5": "ed654121c4c426248e86aa743042348a", "size": "82522564"}, "linux-ppc64le": {"relative_path": "libcusolver/linux-ppc64le/libcusolver-linux-ppc64le-*********-archive.tar.xz", "sha256": "4e6f7c22664c476570bcdcc5733514eff22ab604a8681f57b5a1207b99e8e698", "md5": "feaa9a8f1fddb33e1475840b00968166", "size": "82246224"}, "linux-sbsa": {"relative_path": "libcusolver/linux-sbsa/libcusolver-linux-sbsa-*********-archive.tar.xz", "sha256": "5ce0e37263b30138dc270adc3807a5d706319a2524d18513994633b2030c4b5f", "md5": "d0cd11511154cbb3ec2a9435c12dd9a8", "size": "81697824"}, "windows-x86_64": {"relative_path": "libcusolver/windows-x86_64/libcusolver-windows-x86_64-*********-archive.zip", "sha256": "fb91de7436461da653f255602fd5ed21de92aa633fcb5f5c3eb6f772cf640b17", "md5": "22263f6002e1fe33ea6b11e73d90aa1e", "size": "122673653"}, "linux-aarch64": {"relative_path": "libcusolver/linux-aarch64/libcusolver-linux-aarch64-*********-archive.tar.xz", "sha256": "013cab76ebab03f272ed3e04802d11cc462f751bca429514d54a1dbc4b81d498", "md5": "5bf0e13472b216e24cef45efae61f23e", "size": "84951560"}}, "libcusparse": {"name": "CUDA cuSPARSE", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libcusparse/linux-x86_64/libcusparse-linux-x86_64-*********-archive.tar.xz", "sha256": "466a419b2cd0d543a93245f91d79d20a984073a63c644d2d96d490546eeb0647", "md5": "85b80796bb4b5f1871d661ae89283e43", "size": "204704340"}, "linux-ppc64le": {"relative_path": "libcusparse/linux-ppc64le/libcusparse-linux-ppc64le-*********-archive.tar.xz", "sha256": "5910b5776a0493dcb2506666e1282601c77a7546e674c14669b6c78ea3e382e9", "md5": "65eab72efe9309736507c34c375ea2ef", "size": "204843680"}, "linux-sbsa": {"relative_path": "libcusparse/linux-sbsa/libcusparse-linux-sbsa-*********-archive.tar.xz", "sha256": "47e811aa3dedc9c16d845b3ac25c1d3c57c1c6b32ddfd2f7e02345cfe61b81cd", "md5": "af222605497ff1d9b9299ce5696474cb", "size": "204338536"}, "windows-x86_64": {"relative_path": "libcusparse/windows-x86_64/libcusparse-windows-x86_64-*********-archive.zip", "sha256": "a89c64fdc849559b3855217eaf30c1c941e0bde9d10255b2536cc5813834c863", "md5": "57540dc62e06e08835e8d954b8e793f8", "size": "186093083"}, "linux-aarch64": {"relative_path": "libcusparse/linux-aarch64/libcusparse-linux-aarch64-*********-archive.tar.xz", "sha256": "dd69181761e93f81b100e9e7d6847bcb9737e4db121a7b4b46596c6ff616519f", "md5": "2ce5d3c2fb612db8054b611f92dde458", "size": "219544820"}}, "libnpp": {"name": "CUDA NPP", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libnpp/linux-x86_64/libnpp-linux-x86_64-*********-archive.tar.xz", "sha256": "5c5bdad9f137029633e2c539d7fa56196681675a45b924afdf12a1be77e1cd53", "md5": "d47ae71bcb2a14f9006c57eb34693394", "size": "183663652"}, "linux-ppc64le": {"relative_path": "libnpp/linux-ppc64le/libnpp-linux-ppc64le-*********-archive.tar.xz", "sha256": "350d5da4802b156bec05c5645d2fc967651a4b4f585968f4a536c7cab723ce48", "md5": "e73b061b1d64882fc3dfb9c4000461af", "size": "184189484"}, "linux-sbsa": {"relative_path": "libnpp/linux-sbsa/libnpp-linux-sbsa-*********-archive.tar.xz", "sha256": "048c452c6f4bd5e5ba3f015e642248f845c9b6d84da0fd12872bba3c00c81dd5", "md5": "36ac53dd60a71f6143f6ccc7dff59300", "size": "183285584"}, "windows-x86_64": {"relative_path": "libnpp/windows-x86_64/libnpp-windows-x86_64-*********-archive.zip", "sha256": "a6f0943115a582f1d78cb9b53bb699d265337536929b94d454a137cab0ed17fd", "md5": "fd3fd16652f3409d69899524abe6bb8f", "size": "152888932"}, "linux-aarch64": {"relative_path": "libnpp/linux-aarch64/libnpp-linux-aarch64-*********-archive.tar.xz", "sha256": "22cec165e4d3fbe07b704339ecbc9ec1cfc86e0ea3e74bcc55a63494adcae4e4", "md5": "91eb3a8ea5fba80a59dd3a3914ef14a2", "size": "201916596"}}, "libnvidia_nscq": {"name": "NVIDIA NSCQ API", "license": "NVIDIA Driver", "version": "530.30.02", "linux-x86_64": {"relative_path": "libnvidia_nscq/linux-x86_64/libnvidia_nscq-linux-x86_64-530.30.02-archive.tar.xz", "sha256": "92f04a09ad4abea1f3abddd79a93bbea96fe860a0d71ec1cf8a1d0560ed4983f", "md5": "9335a0980c022961a17585579bc47856", "size": "566496"}, "linux-sbsa": {"relative_path": "libnvidia_nscq/linux-sbsa/libnvidia_nscq-linux-sbsa-530.30.02-archive.tar.xz", "sha256": "876558b581ab341c5cc70af4482ef022fd93e417563465b403372f5f8fa076d5", "md5": "32c1adeb59c19d98f6199608dbeeab4b", "size": "522780"}}, "libnvjitlink": {"name": "NVIDIA compiler library for JIT LTO functionality", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "libnvjitlink/linux-x86_64/libnvjitlink-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "f9b34cc464bd5c96b034c3e33a784063a3b639248b7a401298280b8bf8bce159", "md5": "e3d350ce969d16591632b50cd42db817", "size": "25847244"}, "linux-ppc64le": {"relative_path": "libnvjitlink/linux-ppc64le/libnvjitlink-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "f9d0120357365a32a2ff597c8b3d0b00138c25cdda4ca9ffb74173208f7c6ea0", "md5": "3059833234e6b0dad9a1d893b5746417", "size": "23778368"}, "linux-sbsa": {"relative_path": "libnvjitlink/linux-sbsa/libnvjitlink-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "bdf3fbd87bd60f3c23ac576e0256226bc9982b7e55829dfa87b706dc4b2549f2", "md5": "0d87dc930e8ec9cb704e8be68bfa1b8d", "size": "23707148"}, "windows-x86_64": {"relative_path": "libnvjitlink/windows-x86_64/libnvjitlink-windows-x86_64-12.1.55-archive.zip", "sha256": "929c90a99e406c2c3ce5e1236c1d36dcc65bc5eb2d5ef3593404a4ca46a619e7", "md5": "6a18b4008717407fa428b682621228be", "size": "86687092"}, "linux-aarch64": {"relative_path": "libnvjitlink/linux-aarch64/libnvjitlink-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "6f2b06593b29dae594f7471368c23aa1b9f31239e5c3fc2d54c5dbd792bbc2a7", "md5": "393080e45b06603bd1af94b29bc13cd6", "size": "23709000"}}, "libnvjpeg": {"name": "CUDA nvJPEG", "license": "CUDA Toolkit", "version": "*********", "linux-x86_64": {"relative_path": "libnvjpeg/linux-x86_64/libnvjpeg-linux-x86_64-*********-archive.tar.xz", "sha256": "af239661f192b22a594dc54c2b8e7baf50988c3a53c9f165f0e26439be3cc03f", "md5": "68aa09e3df5421f5a7d34efb33652cb4", "size": "2161400"}, "linux-ppc64le": {"relative_path": "libnvjpeg/linux-ppc64le/libnvjpeg-linux-ppc64le-*********-archive.tar.xz", "sha256": "4167fac475e2b7d3df8b49f1afa6fa15a8718bd3ee241f103ec3a4f9aaf6cf81", "md5": "57ea365e6bb3d69602e6a26eb7259175", "size": "2166328"}, "linux-sbsa": {"relative_path": "libnvjpeg/linux-sbsa/libnvjpeg-linux-sbsa-*********-archive.tar.xz", "sha256": "cfd029a50d856d194741d0108067d0ecdc60b8920d7fe0968a509bcef3728cb8", "md5": "9d3a07684d56a7d2d4caef867b367731", "size": "1984352"}, "windows-x86_64": {"relative_path": "libnvjpeg/windows-x86_64/libnvjpeg-windows-x86_64-*********-archive.zip", "sha256": "4e237ff61b4bd025a8ba3436c949a61a318d38e44f7dba8096479a5450d8661f", "md5": "66b7d7ccfca2d3a76c66367e3acd9f23", "size": "2269259"}}, "libnvvm_samples": {"name": "NVVM library samples", "license": "CUDA Toolkit", "version": "12.1.55", "linux-x86_64": {"relative_path": "libnvvm_samples/linux-x86_64/libnvvm_samples-linux-x86_64-12.1.55-archive.tar.xz", "sha256": "764249fd635d44ca3648b121071f4dfc7b8ed6c65e16dfa5145ca54fcb819737", "md5": "7dd3b599feb41ba1b760a4482acde98b", "size": "28992"}, "linux-ppc64le": {"relative_path": "libnvvm_samples/linux-ppc64le/libnvvm_samples-linux-ppc64le-12.1.55-archive.tar.xz", "sha256": "4a9e5f309630c7a917949b198cd1c0405e28cb336fa645d7e5aece8f78e339ed", "md5": "367eca2dd6ddac784c3a5b2040f8259e", "size": "28996"}, "linux-sbsa": {"relative_path": "libnvvm_samples/linux-sbsa/libnvvm_samples-linux-sbsa-12.1.55-archive.tar.xz", "sha256": "247ba9265492f10b410b95639b6b875f8597a7b54ea545c1cf5dbdde623003e5", "md5": "6637ffbadb5cbada39f37ee59791d985", "size": "28904"}, "windows-x86_64": {"relative_path": "libnvvm_samples/windows-x86_64/libnvvm_samples-windows-x86_64-12.1.55-archive.zip", "sha256": "b91e25cf2b3fbcae10f173399c29dadf7d01904e45fd7df1e60ddca06478b399", "md5": "846b0f59a2879d4a2eccff2e72fa08de", "size": "44383"}, "linux-aarch64": {"relative_path": "libnvvm_samples/linux-aarch64/libnvvm_samples-linux-aarch64-12.1.55-archive.tar.xz", "sha256": "ab142679e37c4bf61ca5a51bcf9d0ad542cf5940f66bb08251490ea8d2e46a5b", "md5": "645013ea3132ffca211dd44232eebb85", "size": "28988"}}, "nsight_compute": {"name": "Nsight Compute", "license": "NVIDIA SLA", "version": "2023.1.0.15", "linux-x86_64": {"relative_path": "nsight_compute/linux-x86_64/nsight_compute-linux-x86_64-2023.1.0.15-archive.tar.xz", "sha256": "4d33b2f6995221f1eec076623a385e6381c7fa7129ef007217df3121b9106a59", "md5": "9151b7661e7fc42cca20d2dfe9b0338c", "size": "708339860"}, "linux-ppc64le": {"relative_path": "nsight_compute/linux-ppc64le/nsight_compute-linux-ppc64le-2023.1.0.15-archive.tar.xz", "sha256": "7c22b7179c3de128bfc8ce8f49a5a63d0187ea2192ce6556e1ec0cdff6d2ddc7", "md5": "93fae98c3d850bcf750dc3a2ccb98bd7", "size": "181151824"}, "linux-sbsa": {"relative_path": "nsight_compute/linux-sbsa/nsight_compute-linux-sbsa-2023.1.0.15-archive.tar.xz", "sha256": "0da9f3ecf8da306f52d0b1590b2eea3eefd6a0f2d505be44abf7e21d644d2bbf", "md5": "75e80b12647a8f5033e9418db9c0b270", "size": "344107160"}, "windows-x86_64": {"relative_path": "nsight_compute/windows-x86_64/nsight_compute-windows-x86_64-2023.1.0.15-archive.zip", "sha256": "4c92003dfc170b74c1e435c58ac175bb344cf4efca18c4621c3979578b7b9946", "md5": "76ba782f135a7bc745df81757e880fab", "size": "638346242"}, "linux-aarch64": {"relative_path": "nsight_compute/linux-aarch64/nsight_compute-linux-aarch64-2023.1.0.15-archive.tar.xz", "sha256": "5944d9c49a2e64d5639ef9b2d2a3311b8ef7bdc61abf200abfc23ac9a3b8beff", "md5": "bc16117f4f4eb17a0f6d245df7275e95", "size": "732153268"}}, "nsight_systems": {"name": "Nsight Systems", "license": "NVIDIA SLA", "version": "2023.1.2.43", "linux-x86_64": {"relative_path": "nsight_systems/linux-x86_64/nsight_systems-linux-x86_64-2023.1.2.43-archive.tar.xz", "sha256": "db7beae0d1cfd58468a24a682f268828adfb49320e11b3ac2d3b508bce4b2792", "md5": "57f29d05eed0940150c6a1fbcc18ccba", "size": "207237352"}, "linux-ppc64le": {"relative_path": "nsight_systems/linux-ppc64le/nsight_systems-linux-ppc64le-2023.1.2.43-archive.tar.xz", "sha256": "8798acb9760a5bb60936e0a209fe3d07835a8c9534a884568f0143bfa5be85f8", "md5": "1113a98ac43d1d3f711a6763c1bc0d66", "size": "60829540"}, "linux-sbsa": {"relative_path": "nsight_systems/linux-sbsa/nsight_systems-linux-sbsa-2023.1.2.43-archive.tar.xz", "sha256": "5cd6af8100a17a36a423606f349fe81521982edff3a787486fb97d38bb444883", "md5": "f02c7aa4aa44ece30ade62dcc87bb27b", "size": "195592688"}, "windows-x86_64": {"relative_path": "nsight_systems/windows-x86_64/nsight_systems-windows-x86_64-2023.1.2.43-archive.zip", "sha256": "8d70dcca4d105e42d8c7afe37d81b3dabd73ce4111068b04e379c5c4f1f8776f", "md5": "570964242a20a8bfa24f558891f9f27b", "size": "326985615"}}, "nsight_vse": {"name": "Nsight Visual Studio Edition (VSE)", "license": "NVIDIA SLA", "version": "2023.1.0.23041", "windows-x86_64": {"relative_path": "nsight_vse/windows-x86_64/nsight_vse-windows-x86_64-2023.1.0.23041-archive.zip", "sha256": "d38f5b906196b7e9b90ef4b7c78e3412852b04ae22c92b6044959570916c086e", "md5": "d8502cf0fe12094fb3d821609733574b", "size": "528310360"}}, "nvidia_driver": {"name": "NVIDIA Linux Driver", "license": "NVIDIA Driver", "version": "530.30.02", "linux-x86_64": {"relative_path": "nvidia_driver/linux-x86_64/nvidia_driver-linux-x86_64-530.30.02-archive.tar.xz", "sha256": "db30bba7b44458b2de2e0fb13d335b9eec5886034bde5999dffba23bc4e198ee", "md5": "bfec08e43cc45f45aa07616d9856d2a5", "size": "416508828"}, "linux-ppc64le": {"relative_path": "nvidia_driver/linux-ppc64le/nvidia_driver-linux-ppc64le-530.30.02-archive.tar.xz", "sha256": "a5b51d096c36b962c424038bae2279975bc2ccd3caaf7c68fabd67302973b1cc", "md5": "6c8073bc881f177fab52913cd9e95b6e", "size": "98199608"}, "linux-sbsa": {"relative_path": "nvidia_driver/linux-sbsa/nvidia_driver-linux-sbsa-530.30.02-archive.tar.xz", "sha256": "a16bfd2ac3cbe16bee5da5c85254a64352cc7bd6a17400db1b65ad692b6ad100", "md5": "1c9513fb5563f4d58507af0d4705a0db", "size": "267452360"}}, "nvidia_fs": {"name": "NVIDIA filesystem", "license": "CUDA Toolkit", "version": "2.15.1", "linux-x86_64": {"relative_path": "nvidia_fs/linux-x86_64/nvidia_fs-linux-x86_64-2.15.1-archive.tar.xz", "sha256": "c55a0e43f6157286eaf31cf8ac536884ad018a47a428386fb815103b6e23590c", "md5": "c1e12b53269be0c6b08d59635ad368cc", "size": "57144"}, "linux-aarch64": {"relative_path": "nvidia_fs/linux-aarch64/nvidia_fs-linux-aarch64-2.15.1-archive.tar.xz", "sha256": "dab95239077aa39065593ea40b262c0c2f7f9efd4854b62e18c372ade75e7dc3", "md5": "4786cf68b1c58df6771c9e1bef7088a0", "size": "57128"}}, "visual_studio_integration": {"name": "CUDA Visual Studio Integration", "license": "CUDA Toolkit", "version": "12.1.55", "windows-x86_64": {"relative_path": "visual_studio_integration/windows-x86_64/visual_studio_integration-windows-x86_64-12.1.55-archive.zip", "sha256": "45339458f17ee380258aee1e014e1349b6e86a6931f2e7b5ad49627897a052d3", "md5": "ebd1647c27f9e91644c1ee1299c791b5", "size": "517853"}}}
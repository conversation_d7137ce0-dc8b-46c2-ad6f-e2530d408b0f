exports_files(
    [
        "bin/nvcc",
        "bin/nvcc.profile",
        "bin/nvlink",
        "bin/crt/link.stub",
        "bin/bin2c",
        "bin/fatbinary",
        "bin/cudafe++",
        "bin/ptxas",
        "nvvm/bin/cicc",
        "lib/libcudart.so",
        "lib/libcudart.so.12",
        "lib/libcudart_static.a",
        "lib/libcublasLt.so",
        "lib/libcublasLt.so.12",
        "lib/libcublas.so",
        "lib/libcublas.so.12",
        "lib/libcudadevrt.a",
        "lib/stubs/libcuda.so",
        "lib/libcurand.so",
        "lib/libcurand.so.10",
        "lib/libcuinj64.so",
        "lib/libcuinj64.so.12.4",
        "lib/libcusparse.so",
        "lib/libcusparse.so.12",
        "lib/libcusolver.so",
        "lib/libcusolver.so.11",
        "lib/libcupti.so",
        "lib/libcupti.so.12",
        "lib/libnvJitLink.so",
        "lib/libnvJitLink.so.12",
        "lib/libnvJitLink.so.12.4.127",
    ],
    visibility = ["//visibility:public"],
)

filegroup(
    name = "includes",
    srcs = glob(
        [
            "include/**",
            "nvvm/include/**",
        ],
        allow_empty = True,
    ),
    visibility = ["//visibility:public"],
)

filegroup(
    name = "nvvm",
    srcs = glob(
        ["nvvm/**"],
        allow_empty = True,
        exclude = ["nvvm/libnvvm-samples"],
    ),
    visibility = ["//visibility:public"],
)

cc_library(
    name = "hdrs",
    hdrs = glob(
        [
            "include/**",
            "nvvm/include/**",
        ],
        allow_empty = True,
    ),
    includes = ["include"],
    visibility = ["//visibility:public"],
    deps = ["@libcudacxx"],
)

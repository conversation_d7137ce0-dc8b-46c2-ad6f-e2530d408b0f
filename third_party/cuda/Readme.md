# Hermetric CUDA toolchain

based on https://github.com/bazel-contrib/rules_cuda/pull/72

The Remote CUDA toolchain will download the specified CUDA version from NVIDIA
and make it available as Bazel targets, esp. for `cc_library` and `cuda_library` calls

## Important targets

- `@rules_cuda//cuda:runtime`: CUDA Runtime
- `@thrust`: [https://github.com/NVIDIA/thrust](C++ Parallel Algorithm Library)
- `@remote_cuda_toolchain//:cublas`: CUBLAS: Linear Algebra
- `@remote_cuda_toolchain//:curand`: CURAND: Random Number Generation
- `@remote_cuda_toolchain//:cuda_nvprof`: CUDA Profiling Library


## Steps to Update CUDA version

- Add CUDA version under `redistrib` if applicable. The files can be found at [https://developer.download.nvidia.com/compute/cuda/redist/].
- Change the version in `cuda.bzl`
- Update the base containers uses for deployment
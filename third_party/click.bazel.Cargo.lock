{"checksum": "0a7d41178701732df812925c0d8f0e8a09c0118c402f8a02b394c4ed60cd2da8", "crates": {"aho-corasick 0.7.19": {"name": "aho-<PERSON><PERSON><PERSON>", "version": "0.7.19", "repository": {"Http": {"url": "https://static.crates.io/crates/aho-corasick/0.7.19/download", "sha256": "b4f55bd91a0978cbfd91c457a164bab8b4001c833b7f323132c0a4e1922dd44e"}}, "targets": [{"Library": {"crate_name": "aho_corasick", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "aho_corasick", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "deps": {"common": [{"id": "memchr 2.5.0", "target": "memchr"}], "selects": {}}, "edition": "2018", "version": "0.7.19"}, "license": "Unlicense/MIT"}, "android_system_properties 0.1.5": {"name": "android_system_properties", "version": "0.1.5", "repository": {"Http": {"url": "https://static.crates.io/crates/android_system_properties/0.1.5/download", "sha256": "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"}}, "targets": [{"Library": {"crate_name": "android_system_properties", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "android_system_properties", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2018", "version": "0.1.5"}, "license": "MIT/Apache-2.0"}, "atomicwrites 0.3.1": {"name": "atomicwrites", "version": "0.3.1", "repository": {"Http": {"url": "https://static.crates.io/crates/atomicwrites/0.3.1/download", "sha256": "eb8f2cd6962fa53c0e2a9d3f97eaa7dbd1e3cbbeeb4745403515b42ae07b3ff6"}}, "targets": [{"Library": {"crate_name": "atomicwrites", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "atomicwrites", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "tempfile 3.3.0", "target": "tempfile"}], "selects": {"cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2015", "version": "0.3.1"}, "license": "MIT"}, "atty 0.2.14": {"name": "atty", "version": "0.2.14", "repository": {"Http": {"url": "https://static.crates.io/crates/atty/0.2.14/download", "sha256": "d9b39be18770d11421cdb1b9947a45dd3f37e93092cbf377614828a319d5fee8"}}, "targets": [{"Library": {"crate_name": "atty", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "atty", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(target_os = \"hermit\")": [{"id": "hermit-abi 0.1.19", "target": "hermit_abi"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2015", "version": "0.2.14"}, "license": "MIT"}, "autocfg 1.1.0": {"name": "autocfg", "version": "1.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/autocfg/1.1.0/download", "sha256": "d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa"}}, "targets": [{"Library": {"crate_name": "autocfg", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "autocfg", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "1.1.0"}, "license": "Apache-2.0 OR MIT"}, "base64 0.13.0": {"name": "base64", "version": "0.13.0", "repository": {"Http": {"url": "https://static.crates.io/crates/base64/0.13.0/download", "sha256": "904dfeac50f3cdaba28fc6f57fdcddb75f49ed61346676a78c4ffe55877802fd"}}, "targets": [{"Library": {"crate_name": "base64", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "base64", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "std"], "selects": {}}, "edition": "2018", "version": "0.13.0"}, "license": "MIT/Apache-2.0"}, "bitflags 1.3.2": {"name": "bitflags", "version": "1.3.2", "repository": {"Http": {"url": "https://static.crates.io/crates/bitflags/1.3.2/download", "sha256": "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"}}, "targets": [{"Library": {"crate_name": "bitflags", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "bitflags", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "edition": "2018", "version": "1.3.2"}, "license": "MIT/Apache-2.0"}, "block-buffer 0.10.3": {"name": "block-buffer", "version": "0.10.3", "repository": {"Http": {"url": "https://static.crates.io/crates/block-buffer/0.10.3/download", "sha256": "69cce20737498f97b993470a6e536b8523f0af7892a4f928cceb1ac5e52ebe7e"}}, "targets": [{"Library": {"crate_name": "block_buffer", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "block_buffer", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "generic-array 0.14.6", "target": "generic_array"}], "selects": {}}, "edition": "2018", "version": "0.10.3"}, "license": "MIT OR Apache-2.0"}, "block-padding 0.3.2": {"name": "block-padding", "version": "0.3.2", "repository": {"Http": {"url": "https://static.crates.io/crates/block-padding/0.3.2/download", "sha256": "0a90ec2df9600c28a01c56c4784c9207a96d2451833aeceb8cc97e4c9548bb78"}}, "targets": [{"Library": {"crate_name": "block_padding", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "block_padding", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "generic-array 0.14.6", "target": "generic_array"}], "selects": {}}, "edition": "2021", "version": "0.3.2"}, "license": "MIT OR Apache-2.0"}, "bumpalo 3.11.0": {"name": "<PERSON><PERSON>", "version": "3.11.0", "repository": {"Http": {"url": "https://static.crates.io/crates/bumpalo/3.11.0/download", "sha256": "c1ad822118d20d2c234f427000d5acc36eabe1e29a348c89b63dd60b13f28e5d"}}, "targets": [{"Library": {"crate_name": "<PERSON><PERSON>", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "<PERSON><PERSON>", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "edition": "2021", "version": "3.11.0"}, "license": "MIT/Apache-2.0"}, "bytes 1.2.1": {"name": "bytes", "version": "1.2.1", "repository": {"Http": {"url": "https://static.crates.io/crates/bytes/1.2.1/download", "sha256": "ec8a7b6a70fde80372154c65702f00a0f56f3e1c36abbc6c440484be248856db"}}, "targets": [{"Library": {"crate_name": "bytes", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "bytes", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "edition": "2018", "version": "1.2.1"}, "license": "MIT"}, "cbc 0.1.2": {"name": "cbc", "version": "0.1.2", "repository": {"Http": {"url": "https://static.crates.io/crates/cbc/0.1.2/download", "sha256": "26b52a9543ae338f279b96b0b9fed9c8093744685043739079ce85cd58f289a6"}}, "targets": [{"Library": {"crate_name": "cbc", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "cbc", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["block-padding", "default"], "selects": {}}, "deps": {"common": [{"id": "cipher 0.4.3", "target": "cipher"}], "selects": {}}, "edition": "2021", "version": "0.1.2"}, "license": "MIT OR Apache-2.0"}, "cc 1.0.73": {"name": "cc", "version": "1.0.73", "repository": {"Http": {"url": "https://static.crates.io/crates/cc/1.0.73/download", "sha256": "2fff2a6927b3bb87f9595d67196a70493f627687a71d87a0d692242c33f58c11"}}, "targets": [{"Library": {"crate_name": "cc", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "cc", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.0.73"}, "license": "MIT/Apache-2.0"}, "cfg-if 1.0.0": {"name": "cfg-if", "version": "1.0.0", "repository": {"Http": {"url": "https://static.crates.io/crates/cfg-if/1.0.0/download", "sha256": "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"}}, "targets": [{"Library": {"crate_name": "cfg_if", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "cfg_if", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.0.0"}, "license": "MIT/Apache-2.0"}, "chrono 0.4.22": {"name": "chrono", "version": "0.4.22", "repository": {"Http": {"url": "https://static.crates.io/crates/chrono/0.4.22/download", "sha256": "bfd4d1b31faaa3a89d7934dbded3111da0d2ef28e3ebccdb4f0179f5929d1ef1"}}, "targets": [{"Library": {"crate_name": "chrono", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "chrono", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "clock", "default", "iana-time-zone", "js-sys", "oldtime", "serde", "std", "time", "wasm-bindgen", "<PERSON><PERSON><PERSON>", "<PERSON>ap<PERSON>"], "selects": {}}, "deps": {"common": [{"id": "iana-time-zone 0.1.48", "target": "iana_time_zone"}, {"id": "num-integer 0.1.45", "target": "num_integer"}, {"id": "num-traits 0.2.15", "target": "num_traits"}, {"id": "serde 1.0.144", "target": "serde"}, {"id": "time 0.1.44", "target": "time"}], "selects": {"cfg(all(target_arch = \"wasm32\", not(any(target_os = \"emscripten\", target_os = \"wasi\"))))": [{"id": "js-sys 0.3.60", "target": "js_sys"}, {"id": "wasm-bindgen 0.2.83", "target": "wasm_bindgen"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.4.22"}, "license": "MIT/Apache-2.0"}, "cipher 0.4.3": {"name": "cipher", "version": "0.4.3", "repository": {"Http": {"url": "https://static.crates.io/crates/cipher/0.4.3/download", "sha256": "d1873270f8f7942c191139cb8a40fd228da6c3fd2fc376d7e92d47aa14aeb59e"}}, "targets": [{"Library": {"crate_name": "cipher", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "cipher", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "block-padding"], "selects": {}}, "deps": {"common": [{"id": "crypto-common 0.1.6", "target": "crypto_common"}, {"id": "inout 0.1.3", "target": "inout"}], "selects": {}}, "edition": "2021", "version": "0.4.3"}, "license": "MIT OR Apache-2.0"}, "clap 3.2.22": {"name": "clap", "version": "3.2.22", "repository": {"Http": {"url": "https://static.crates.io/crates/clap/3.2.22/download", "sha256": "86447ad904c7fb335a790c9d7fe3d0d971dc523b8ccd1561a520de9a85302750"}}, "targets": [{"Library": {"crate_name": "clap", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "clap", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["atty", "cargo", "color", "default", "once_cell", "std", "strsim", "suggestions", "termcolor"], "selects": {}}, "deps": {"common": [{"id": "atty 0.2.14", "target": "atty"}, {"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "clap_lex 0.2.4", "target": "clap_lex"}, {"id": "indexmap 1.9.1", "target": "indexmap"}, {"id": "once_cell 1.14.0", "target": "once_cell"}, {"id": "strsim 0.10.0", "target": "strsim"}, {"id": "termcolor 1.1.3", "target": "termcolor"}, {"id": "textwrap 0.15.1", "target": "textwrap"}], "selects": {}}, "edition": "2021", "version": "3.2.22"}, "license": "MIT OR Apache-2.0"}, "clap_lex 0.2.4": {"name": "clap_lex", "version": "0.2.4", "repository": {"Http": {"url": "https://static.crates.io/crates/clap_lex/0.2.4/download", "sha256": "2850f2f5a82cbf437dd5af4d49848fbdfc27c157c3d010345776f952765261c5"}}, "targets": [{"Library": {"crate_name": "clap_lex", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "clap_lex", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "os_str_bytes 6.3.0", "target": "os_str_bytes"}], "selects": {}}, "edition": "2021", "version": "0.2.4"}, "license": "MIT OR Apache-2.0"}, "click 0.6.2": {"name": "click", "version": "0.6.2", "repository": null, "targets": [], "library_target_name": null, "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "atomicwrites 0.3.1", "target": "atomicwrites"}, {"id": "base64 0.13.0", "target": "base64"}, {"id": "bytes 1.2.1", "target": "bytes"}, {"id": "chrono 0.4.22", "target": "chrono"}, {"id": "clap 3.2.22", "target": "clap"}, {"id": "comfy-table 6.1.0", "target": "comfy_table"}, {"id": "crossterm 0.25.0", "target": "crossterm"}, {"id": "ctrlc 3.2.3", "target": "ctrlc"}, {"id": "dirs 4.0.0", "target": "dirs"}, {"id": "duct 0.13.5", "target": "duct"}, {"id": "duct_sh 0.13.5", "target": "duct_sh"}, {"id": "env_logger 0.9.1", "target": "env_logger"}, {"id": "humantime 2.1.0", "target": "humantime"}, {"id": "k8s-openapi 0.14.0", "target": "k8s_openapi"}, {"id": "lazy_static 1.4.0", "target": "lazy_static"}, {"id": "os_pipe 1.0.1", "target": "os_pipe"}, {"id": "p12 0.6.3", "target": "p12"}, {"id": "pem 1.1.0", "target": "pem"}, {"id": "regex 1.6.0", "target": "regex"}, {"id": "reqwest 0.11.12", "target": "reqwest"}, {"id": "rustls 0.20.6", "target": "rustls"}, {"id": "rustyline 10.0.0", "target": "rustyline"}, {"id": "serde 1.0.144", "target": "serde"}, {"id": "serde_json 1.0.85", "target": "serde_json"}, {"id": "serde_with 2.0.1", "target": "serde_with"}, {"id": "serde_yaml 0.9.13", "target": "serde_yaml"}, {"id": "strfmt 0.2.2", "target": "strfmt"}, {"id": "tempdir 0.3.7", "target": "tempdir"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "url 2.3.1", "target": "url"}, {"id": "yasna 0.5.0", "target": "yasna"}], "selects": {}}, "edition": "2021", "proc_macro_deps": {"common": [{"id": "derivative 2.2.0", "target": "derivative"}, {"id": "serde_derive 1.0.144", "target": "serde_derive"}], "selects": {}}, "version": "0.6.2"}, "license": "Apache-2.0"}, "clipboard-win 4.4.2": {"name": "clipboard-win", "version": "4.4.2", "repository": {"Http": {"url": "https://static.crates.io/crates/clipboard-win/4.4.2/download", "sha256": "c4ab1b92798304eedc095b53942963240037c0516452cb11aeba709d420b2219"}}, "targets": [{"Library": {"crate_name": "clipboard_win", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "clipboard_win", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(windows)": [{"id": "error-code 2.3.1", "target": "error_code"}, {"id": "str-buf 1.0.6", "target": "str_buf"}, {"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "4.4.2"}, "license": "BSL-1.0"}, "comfy-table 6.1.0": {"name": "comfy-table", "version": "6.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/comfy-table/6.1.0/download", "sha256": "85914173c2f558d61613bfbbf1911f14e630895087a7ed2fafc0f5319e1536e7"}}, "targets": [{"Library": {"crate_name": "comfy_table", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "comfy_table", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["crossterm", "default", "tty"], "selects": {}}, "deps": {"common": [{"id": "crossterm 0.25.0", "target": "crossterm"}, {"id": "strum 0.24.1", "target": "strum"}, {"id": "unicode-width 0.1.10", "target": "unicode_width"}], "selects": {}}, "edition": "2021", "proc_macro_deps": {"common": [{"id": "strum_macros 0.24.3", "target": "strum_macros"}], "selects": {}}, "version": "6.1.0"}, "license": "MIT"}, "core-foundation 0.9.3": {"name": "core-foundation", "version": "0.9.3", "repository": {"Http": {"url": "https://static.crates.io/crates/core-foundation/0.9.3/download", "sha256": "194a7a9e6de53fa55116934067c844d9d749312f75c6f6d0980e8c252f8c2146"}}, "targets": [{"Library": {"crate_name": "core_foundation", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "core_foundation", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "core-foundation-sys 0.8.3", "target": "core_foundation_sys"}, {"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2015", "version": "0.9.3"}, "license": "MIT / Apache-2.0"}, "core-foundation-sys 0.8.3": {"name": "core-foundation-sys", "version": "0.8.3", "repository": {"Http": {"url": "https://static.crates.io/crates/core-foundation-sys/0.8.3/download", "sha256": "5827cebf4670468b8772dd191856768aedcb1b0278a04f989f7766351917b9dc"}}, "targets": [{"Library": {"crate_name": "core_foundation_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "core_foundation_sys", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "core-foundation-sys 0.8.3", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "version": "0.8.3"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT / Apache-2.0"}, "cpufeatures 0.2.5": {"name": "cpufeatures", "version": "0.2.5", "repository": {"Http": {"url": "https://static.crates.io/crates/cpufeatures/0.2.5/download", "sha256": "28d997bd5e24a5928dd43e46dc529867e207907fe0b239c3477d924f7f2ca320"}}, "targets": [{"Library": {"crate_name": "cpufeatures", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "cpufeatures", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"aarch64-apple-darwin": [{"id": "libc 0.2.133", "target": "libc"}], "aarch64-linux-android": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(all(target_arch = \"aarch64\", target_os = \"linux\"))": [{"id": "libc 0.2.133", "target": "libc"}]}}, "edition": "2018", "version": "0.2.5"}, "license": "MIT OR Apache-2.0"}, "crossterm 0.25.0": {"name": "crossterm", "version": "0.25.0", "repository": {"Http": {"url": "https://static.crates.io/crates/crossterm/0.25.0/download", "sha256": "e64e6c0fbe2c17357405f7c758c1ef960fce08bdfb2c03d88d2a18d7e09c4b67"}}, "targets": [{"Library": {"crate_name": "crossterm", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "crossterm", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["bracketed-paste", "default"], "selects": {}}, "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "parking_lot 0.12.1", "target": "parking_lot"}], "selects": {"cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "mio 0.8.4", "target": "mio"}, {"id": "signal-hook 0.3.14", "target": "signal_hook"}, {"id": "signal-hook-mio 0.2.3", "target": "signal_hook_mio"}], "cfg(windows)": [{"id": "crossterm_winapi 0.9.0", "target": "crossterm_winapi"}, {"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2021", "version": "0.25.0"}, "license": "MIT"}, "crossterm_winapi 0.9.0": {"name": "crossterm_winapi", "version": "0.9.0", "repository": {"Http": {"url": "https://static.crates.io/crates/crossterm_winapi/0.9.0/download", "sha256": "2ae1b35a484aa10e07fe0638d02301c5ad24de82d310ccbd2f3693da5f09bf1c"}}, "targets": [{"Library": {"crate_name": "crossterm_winapi", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "crossterm_winapi", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.9.0"}, "license": "MIT"}, "crypto-common 0.1.6": {"name": "crypto-common", "version": "0.1.6", "repository": {"Http": {"url": "https://static.crates.io/crates/crypto-common/0.1.6/download", "sha256": "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"}}, "targets": [{"Library": {"crate_name": "crypto_common", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "crypto_common", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["std"], "selects": {}}, "deps": {"common": [{"id": "generic-array 0.14.6", "target": "generic_array"}, {"id": "typenum 1.15.0", "target": "typenum"}], "selects": {}}, "edition": "2018", "version": "0.1.6"}, "license": "MIT OR Apache-2.0"}, "ctrlc 3.2.3": {"name": "ctrlc", "version": "3.2.3", "repository": {"Http": {"url": "https://static.crates.io/crates/ctrlc/3.2.3/download", "sha256": "1d91974fbbe88ec1df0c24a4f00f99583667a7e2e6272b2b92d294d81e462173"}}, "targets": [{"Library": {"crate_name": "ctrlc", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "ctrlc", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(unix)": [{"id": "nix 0.25.0", "target": "nix"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "3.2.3"}, "license": "MIT/Apache-2.0"}, "darling 0.14.1": {"name": "darling", "version": "0.14.1", "repository": {"Http": {"url": "https://static.crates.io/crates/darling/0.14.1/download", "sha256": "4529658bdda7fd6769b8614be250cdcfc3aeb0ee72fe66f9e41e5e5eb73eac02"}}, "targets": [{"Library": {"crate_name": "darling", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "darling", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "suggestions"], "selects": {}}, "deps": {"common": [{"id": "darling_core 0.14.1", "target": "darling_core"}], "selects": {}}, "edition": "2018", "proc_macro_deps": {"common": [{"id": "darling_macro 0.14.1", "target": "darling_macro"}], "selects": {}}, "version": "0.14.1"}, "license": "MIT"}, "darling_core 0.14.1": {"name": "darling_core", "version": "0.14.1", "repository": {"Http": {"url": "https://static.crates.io/crates/darling_core/0.14.1/download", "sha256": "649c91bc01e8b1eac09fb91e8dbc7d517684ca6be8ebc75bb9cafc894f9fdb6f"}}, "targets": [{"Library": {"crate_name": "darling_core", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "darling_core", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["strsim", "suggestions"], "selects": {}}, "deps": {"common": [{"id": "fnv 1.0.7", "target": "fnv"}, {"id": "ident_case 1.0.1", "target": "ident_case"}, {"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "strsim 0.10.0", "target": "strsim"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2018", "version": "0.14.1"}, "license": "MIT"}, "darling_macro 0.14.1": {"name": "darling_macro", "version": "0.14.1", "repository": {"Http": {"url": "https://static.crates.io/crates/darling_macro/0.14.1/download", "sha256": "ddfc69c5bfcbd2fc09a0f38451d2daf0e372e367986a83906d1b0dbc88134fb5"}}, "targets": [{"ProcMacro": {"crate_name": "darling_macro", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "darling_macro", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "darling_core 0.14.1", "target": "darling_core"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2018", "version": "0.14.1"}, "license": "MIT"}, "derivative 2.2.0": {"name": "derivative", "version": "2.2.0", "repository": {"Http": {"url": "https://static.crates.io/crates/derivative/2.2.0/download", "sha256": "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"}}, "targets": [{"ProcMacro": {"crate_name": "derivative", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "derivative", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2015", "version": "2.2.0"}, "license": "MIT/Apache-2.0"}, "des 0.8.1": {"name": "des", "version": "0.8.1", "repository": {"Http": {"url": "https://static.crates.io/crates/des/0.8.1/download", "sha256": "ffdd80ce8ce993de27e9f063a444a4d53ce8e8db4c1f00cc03af5ad5a9867a1e"}}, "targets": [{"Library": {"crate_name": "des", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "des", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cipher 0.4.3", "target": "cipher"}], "selects": {}}, "edition": "2021", "version": "0.8.1"}, "license": "MIT OR Apache-2.0"}, "digest 0.10.5": {"name": "digest", "version": "0.10.5", "repository": {"Http": {"url": "https://static.crates.io/crates/digest/0.10.5/download", "sha256": "adfbc57365a37acbd2ebf2b64d7e69bb766e2fea813521ed536f5d0520dcf86c"}}, "targets": [{"Library": {"crate_name": "digest", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "digest", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "block-buffer", "core-api", "default", "mac", "std", "subtle"], "selects": {}}, "deps": {"common": [{"id": "block-buffer 0.10.3", "target": "block_buffer"}, {"id": "crypto-common 0.1.6", "target": "crypto_common"}, {"id": "subtle 2.4.1", "target": "subtle"}], "selects": {}}, "edition": "2018", "version": "0.10.5"}, "license": "MIT OR Apache-2.0"}, "dirs 4.0.0": {"name": "dirs", "version": "4.0.0", "repository": {"Http": {"url": "https://static.crates.io/crates/dirs/4.0.0/download", "sha256": "ca3aa72a6f96ea37bbc5aa912f6788242832f75369bdfdadcb0e38423f100059"}}, "targets": [{"Library": {"crate_name": "dirs", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "dirs", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "dirs-sys 0.3.7", "target": "dirs_sys"}], "selects": {}}, "edition": "2015", "version": "4.0.0"}, "license": "MIT OR Apache-2.0"}, "dirs-next 2.0.0": {"name": "dirs-next", "version": "2.0.0", "repository": {"Http": {"url": "https://static.crates.io/crates/dirs-next/2.0.0/download", "sha256": "b98cf8ebf19c3d1b223e151f99a4f9f0690dca41414773390fc824184ac833e1"}}, "targets": [{"Library": {"crate_name": "dirs_next", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "dirs_next", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "dirs-sys-next 0.1.2", "target": "dirs_sys_next"}], "selects": {}}, "edition": "2018", "version": "2.0.0"}, "license": "MIT OR Apache-2.0"}, "dirs-sys 0.3.7": {"name": "dirs-sys", "version": "0.3.7", "repository": {"Http": {"url": "https://static.crates.io/crates/dirs-sys/0.3.7/download", "sha256": "1b1d1d91c932ef41c0f2663aa8b0ca0342d444d842c06914aa0a7e352d0bada6"}}, "targets": [{"Library": {"crate_name": "dirs_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "dirs_sys", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(target_os = \"redox\")": [{"id": "redox_users 0.4.3", "target": "redox_users"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2015", "version": "0.3.7"}, "license": "MIT OR Apache-2.0"}, "dirs-sys-next 0.1.2": {"name": "dirs-sys-next", "version": "0.1.2", "repository": {"Http": {"url": "https://static.crates.io/crates/dirs-sys-next/0.1.2/download", "sha256": "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"}}, "targets": [{"Library": {"crate_name": "dirs_sys_next", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "dirs_sys_next", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(target_os = \"redox\")": [{"id": "redox_users 0.4.3", "target": "redox_users"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.1.2"}, "license": "MIT OR Apache-2.0"}, "duct 0.13.5": {"name": "duct", "version": "0.13.5", "repository": {"Http": {"url": "https://static.crates.io/crates/duct/0.13.5/download", "sha256": "0fc6a0a59ed0888e0041cf708e66357b7ae1a82f1c67247e1f93b5e0818f7d8d"}}, "targets": [{"Library": {"crate_name": "duct", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "duct", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "once_cell 1.14.0", "target": "once_cell"}, {"id": "os_pipe 0.9.2", "target": "os_pipe"}, {"id": "shared_child 0.3.5", "target": "shared_child"}], "selects": {"cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}]}}, "edition": "2018", "version": "0.13.5"}, "license": "MIT"}, "duct_sh 0.13.5": {"name": "duct_sh", "version": "0.13.5", "repository": {"Http": {"url": "https://static.crates.io/crates/duct_sh/0.13.5/download", "sha256": "cc81dfdc61d3d7a268e8f2768c4eca9cf2ff7ffc287f92f3dd89cb1f4ffb13a9"}}, "targets": [{"Library": {"crate_name": "duct_sh", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "duct_sh", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "duct 0.13.5", "target": "duct"}], "selects": {}}, "edition": "2015", "version": "0.13.5"}, "license": "MIT"}, "encoding_rs 0.8.31": {"name": "encoding_rs", "version": "0.8.31", "repository": {"Http": {"url": "https://static.crates.io/crates/encoding_rs/0.8.31/download", "sha256": "9852635589dc9f9ea1b6fe9f05b50ef208c85c834a562f0c6abb1c475736ec2b"}}, "targets": [{"Library": {"crate_name": "encoding_rs", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "encoding_rs", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default"], "selects": {}}, "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "encoding_rs 0.8.31", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.8.31"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "(Apache-2.0 OR MIT) AND BSD-3-<PERSON><PERSON>"}, "endian-type 0.1.2": {"name": "endian-type", "version": "0.1.2", "repository": {"Http": {"url": "https://static.crates.io/crates/endian-type/0.1.2/download", "sha256": "c34f04666d835ff5d62e058c3995147c06f42fe86ff053337632bca83e42702d"}}, "targets": [{"Library": {"crate_name": "endian_type", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "endian_type", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.1.2"}, "license": "MIT"}, "env_logger 0.9.1": {"name": "env_logger", "version": "0.9.1", "repository": {"Http": {"url": "https://static.crates.io/crates/env_logger/0.9.1/download", "sha256": "c90bf5f19754d10198ccb95b70664fc925bd1fc090a0fd9a6ebc54acc8cd6272"}}, "targets": [{"Library": {"crate_name": "env_logger", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "env_logger", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["atty", "default", "humantime", "regex", "termcolor"], "selects": {}}, "deps": {"common": [{"id": "atty 0.2.14", "target": "atty"}, {"id": "humantime 2.1.0", "target": "humantime"}, {"id": "log 0.4.17", "target": "log"}, {"id": "regex 1.6.0", "target": "regex"}, {"id": "termcolor 1.1.3", "target": "termcolor"}], "selects": {}}, "edition": "2018", "version": "0.9.1"}, "license": "MIT OR Apache-2.0"}, "errno 0.2.8": {"name": "errno", "version": "0.2.8", "repository": {"Http": {"url": "https://static.crates.io/crates/errno/0.2.8/download", "sha256": "f639046355ee4f37944e44f60642c6f3a7efa3cf6b78c78a0d989a8ce6c396a1"}}, "targets": [{"Library": {"crate_name": "errno", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "errno", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(target_os = \"dragonfly\")": [{"id": "errno-dragonfly 0.1.2", "target": "errno_dragonfly"}], "cfg(target_os = \"hermit\")": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(target_os = \"wasi\")": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2015", "version": "0.2.8"}, "license": "MIT/Apache-2.0"}, "errno-dragonfly 0.1.2": {"name": "errno-dragonfly", "version": "0.1.2", "repository": {"Http": {"url": "https://static.crates.io/crates/errno-dragonfly/0.1.2/download", "sha256": "aa68f1b12764fab894d2755d2518754e71b4fd80ecfb822714a1206c2aab39bf"}}, "targets": [{"Library": {"crate_name": "errno_dragonfly", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "errno_dragonfly", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "errno-dragonfly 0.1.2", "target": "build_script_build"}, {"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2018", "version": "0.1.2"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "cc 1.0.73", "target": "cc"}], "selects": {}}}, "license": "MIT"}, "error-code 2.3.1": {"name": "error-code", "version": "2.3.1", "repository": {"Http": {"url": "https://static.crates.io/crates/error-code/2.3.1/download", "sha256": "64f18991e7bf11e7ffee451b5318b5c1a73c52d0d0ada6e5a3017c8c1ced6a21"}}, "targets": [{"Library": {"crate_name": "error_code", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "error_code", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "str-buf 1.0.6", "target": "str_buf"}], "selects": {}}, "edition": "2018", "version": "2.3.1"}, "license": "BSL-1.0"}, "fastrand 1.8.0": {"name": "fastrand", "version": "1.8.0", "repository": {"Http": {"url": "https://static.crates.io/crates/fastrand/1.8.0/download", "sha256": "a7a407cfaa3385c4ae6b23e84623d48c2798d06e3e6a1878f7f59f17b3f86499"}}, "targets": [{"Library": {"crate_name": "fastrand", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "fastrand", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(target_arch = \"wasm32\")": [{"id": "instant 0.1.12", "target": "instant"}]}}, "edition": "2018", "version": "1.8.0"}, "license": "Apache-2.0 OR MIT"}, "fd-lock 3.0.6": {"name": "fd-lock", "version": "3.0.6", "repository": {"Http": {"url": "https://static.crates.io/crates/fd-lock/3.0.6/download", "sha256": "e11dcc7e4d79a8c89b9ab4c6f5c30b1fc4a83c420792da3542fd31179ed5f517"}}, "targets": [{"Library": {"crate_name": "fd_lock", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "fd_lock", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}], "selects": {"cfg(unix)": [{"id": "rustix 0.35.9", "target": "rustix"}], "cfg(windows)": [{"id": "windows-sys 0.36.1", "target": "windows_sys"}]}}, "edition": "2018", "version": "3.0.6"}, "license": "MIT OR Apache-2.0"}, "fnv 1.0.7": {"name": "fnv", "version": "1.0.7", "repository": {"Http": {"url": "https://static.crates.io/crates/fnv/1.0.7/download", "sha256": "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"}}, "targets": [{"Library": {"crate_name": "fnv", "crate_root": "lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "fnv", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "edition": "2015", "version": "1.0.7"}, "license": "Apache-2.0 / MIT"}, "foreign-types 0.3.2": {"name": "foreign-types", "version": "0.3.2", "repository": {"Http": {"url": "https://static.crates.io/crates/foreign-types/0.3.2/download", "sha256": "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"}}, "targets": [{"Library": {"crate_name": "foreign_types", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "foreign_types", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "foreign-types-shared 0.1.1", "target": "foreign_types_shared"}], "selects": {}}, "edition": "2015", "version": "0.3.2"}, "license": "MIT/Apache-2.0"}, "foreign-types-shared 0.1.1": {"name": "foreign-types-shared", "version": "0.1.1", "repository": {"Http": {"url": "https://static.crates.io/crates/foreign-types-shared/0.1.1/download", "sha256": "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"}}, "targets": [{"Library": {"crate_name": "foreign_types_shared", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "foreign_types_shared", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.1.1"}, "license": "MIT/Apache-2.0"}, "form_urlencoded 1.1.0": {"name": "form_urlencoded", "version": "1.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/form_urlencoded/1.1.0/download", "sha256": "a9c384f161156f5260c24a097c56119f9be8c798586aecc13afbcbe7b7e26bf8"}}, "targets": [{"Library": {"crate_name": "form_urlencoded", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "form_urlencoded", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "percent-encoding 2.2.0", "target": "percent_encoding"}], "selects": {}}, "edition": "2018", "version": "1.1.0"}, "license": "MIT OR Apache-2.0"}, "fuchsia-cprng 0.1.1": {"name": "fuchsia-cprng", "version": "0.1.1", "repository": {"Http": {"url": "https://static.crates.io/crates/fuchsia-cprng/0.1.1/download", "sha256": "a06f77d526c1a601b7c4cdd98f54b5eaabffc14d5f2f0296febdc7f357c6d3ba"}}, "targets": [{"Library": {"crate_name": "fuchsia_cprng", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "fuchsia_cprng", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "0.1.1"}, "license": null}, "futures-channel 0.3.24": {"name": "futures-channel", "version": "0.3.24", "repository": {"Http": {"url": "https://static.crates.io/crates/futures-channel/0.3.24/download", "sha256": "30bdd20c28fadd505d0fd6712cdfcb0d4b5648baf45faef7f852afb2399bb050"}}, "targets": [{"Library": {"crate_name": "futures_channel", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "futures_channel", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "std"], "selects": {}}, "deps": {"common": [{"id": "futures-channel 0.3.24", "target": "build_script_build"}, {"id": "futures-core 0.3.24", "target": "futures_core"}], "selects": {}}, "edition": "2018", "version": "0.3.24"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "futures-core 0.3.24": {"name": "futures-core", "version": "0.3.24", "repository": {"Http": {"url": "https://static.crates.io/crates/futures-core/0.3.24/download", "sha256": "4e5aa3de05362c3fb88de6531e6296e85cde7739cccad4b9dfeeb7f6ebce56bf"}}, "targets": [{"Library": {"crate_name": "futures_core", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "futures_core", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "std"], "selects": {}}, "deps": {"common": [{"id": "futures-core 0.3.24", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.3.24"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "futures-io 0.3.24": {"name": "futures-io", "version": "0.3.24", "repository": {"Http": {"url": "https://static.crates.io/crates/futures-io/0.3.24/download", "sha256": "bbf4d2a7a308fd4578637c0b17c7e1c7ba127b8f6ba00b29f717e9655d85eb68"}}, "targets": [{"Library": {"crate_name": "futures_io", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "futures_io", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["std"], "selects": {}}, "edition": "2018", "version": "0.3.24"}, "license": "MIT OR Apache-2.0"}, "futures-sink 0.3.24": {"name": "futures-sink", "version": "0.3.24", "repository": {"Http": {"url": "https://static.crates.io/crates/futures-sink/0.3.24/download", "sha256": "21b20ba5a92e727ba30e72834706623d94ac93a725410b6a6b6fbc1b07f7ba56"}}, "targets": [{"Library": {"crate_name": "futures_sink", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "futures_sink", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "std"], "selects": {}}, "edition": "2018", "version": "0.3.24"}, "license": "MIT OR Apache-2.0"}, "futures-task 0.3.24": {"name": "futures-task", "version": "0.3.24", "repository": {"Http": {"url": "https://static.crates.io/crates/futures-task/0.3.24/download", "sha256": "a6508c467c73851293f390476d4491cf4d227dbabcd4170f3bb6044959b294f1"}}, "targets": [{"Library": {"crate_name": "futures_task", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "futures_task", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "std"], "selects": {}}, "deps": {"common": [{"id": "futures-task 0.3.24", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.3.24"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "futures-util 0.3.24": {"name": "futures-util", "version": "0.3.24", "repository": {"Http": {"url": "https://static.crates.io/crates/futures-util/0.3.24/download", "sha256": "44fb6cb1be61cc1d2e43b262516aafcf63b241cffdb1d3fa115f91d9c7b09c90"}}, "targets": [{"Library": {"crate_name": "futures_util", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "futures_util", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "futures-io", "io", "memchr", "slab", "std"], "selects": {}}, "deps": {"common": [{"id": "futures-core 0.3.24", "target": "futures_core"}, {"id": "futures-io 0.3.24", "target": "futures_io"}, {"id": "futures-task 0.3.24", "target": "futures_task"}, {"id": "futures-util 0.3.24", "target": "build_script_build"}, {"id": "memchr 2.5.0", "target": "memchr"}, {"id": "pin-project-lite 0.2.9", "target": "pin_project_lite"}, {"id": "pin-utils 0.1.0", "target": "pin_utils"}, {"id": "slab 0.4.7", "target": "slab"}], "selects": {}}, "edition": "2018", "version": "0.3.24"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "generic-array 0.14.6": {"name": "generic-array", "version": "0.14.6", "repository": {"Http": {"url": "https://static.crates.io/crates/generic-array/0.14.6/download", "sha256": "bff49e947297f3312447abdca79f45f4738097cc82b06e72054d2223f601f1b9"}}, "targets": [{"Library": {"crate_name": "generic_array", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "generic_array", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["more_lengths"], "selects": {}}, "deps": {"common": [{"id": "generic-array 0.14.6", "target": "build_script_build"}, {"id": "typenum 1.15.0", "target": "typenum"}], "selects": {}}, "edition": "2015", "version": "0.14.6"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "version_check 0.9.4", "target": "version_check"}], "selects": {}}}, "license": "MIT"}, "getrandom 0.2.7": {"name": "getrandom", "version": "0.2.7", "repository": {"Http": {"url": "https://static.crates.io/crates/getrandom/0.2.7/download", "sha256": "4eb1a864a501629691edf6c15a593b7a51eebaa1e8468e9ddc623de7c9b58ec6"}}, "targets": [{"Library": {"crate_name": "getrandom", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "getrandom", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}], "selects": {"cfg(target_os = \"wasi\")": [{"id": "wasi 0.11.0+wasi-snapshot-preview1", "target": "wasi"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}]}}, "edition": "2018", "version": "0.2.7"}, "license": "MIT OR Apache-2.0"}, "h2 0.3.14": {"name": "h2", "version": "0.3.14", "repository": {"Http": {"url": "https://static.crates.io/crates/h2/0.3.14/download", "sha256": "5ca32592cf21ac7ccab1825cd87f6c9b3d9022c44d086172ed0966bec8af30be"}}, "targets": [{"Library": {"crate_name": "h2", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "h2", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "bytes 1.2.1", "target": "bytes"}, {"id": "fnv 1.0.7", "target": "fnv"}, {"id": "futures-core 0.3.24", "target": "futures_core"}, {"id": "futures-sink 0.3.24", "target": "futures_sink"}, {"id": "futures-util 0.3.24", "target": "futures_util"}, {"id": "http 0.2.8", "target": "http"}, {"id": "indexmap 1.9.1", "target": "indexmap"}, {"id": "slab 0.4.7", "target": "slab"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "tokio-util 0.7.4", "target": "tokio_util"}, {"id": "tracing 0.1.36", "target": "tracing"}], "selects": {}}, "edition": "2018", "version": "0.3.14"}, "license": "MIT"}, "hashbrown 0.12.3": {"name": "hashbrown", "version": "0.12.3", "repository": {"Http": {"url": "https://static.crates.io/crates/hashbrown/0.12.3/download", "sha256": "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"}}, "targets": [{"Library": {"crate_name": "hashbrown", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "hashbrown", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["raw"], "selects": {}}, "edition": "2021", "version": "0.12.3"}, "license": "MIT OR Apache-2.0"}, "heck 0.4.0": {"name": "heck", "version": "0.4.0", "repository": {"Http": {"url": "https://static.crates.io/crates/heck/0.4.0/download", "sha256": "2540771e65fc8cb83cd6e8a237f70c319bd5c29f78ed1084ba5d50eeac86f7f9"}}, "targets": [{"Library": {"crate_name": "heck", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "heck", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "edition": "2018", "version": "0.4.0"}, "license": "MIT OR Apache-2.0"}, "hermit-abi 0.1.19": {"name": "hermit-abi", "version": "0.1.19", "repository": {"Http": {"url": "https://static.crates.io/crates/hermit-abi/0.1.19/download", "sha256": "62b467343b94ba476dcb2500d242dadbb39557df889310ac77c5d99100aaac33"}}, "targets": [{"Library": {"crate_name": "hermit_abi", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "hermit_abi", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2018", "version": "0.1.19"}, "license": "MIT/Apache-2.0"}, "hex 0.4.3": {"name": "hex", "version": "0.4.3", "repository": {"Http": {"url": "https://static.crates.io/crates/hex/0.4.3/download", "sha256": "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"}}, "targets": [{"Library": {"crate_name": "hex", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "hex", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "0.4.3"}, "license": "MIT OR Apache-2.0"}, "hmac 0.12.1": {"name": "hmac", "version": "0.12.1", "repository": {"Http": {"url": "https://static.crates.io/crates/hmac/0.12.1/download", "sha256": "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"}}, "targets": [{"Library": {"crate_name": "hmac", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "hmac", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "digest 0.10.5", "target": "digest"}], "selects": {}}, "edition": "2018", "version": "0.12.1"}, "license": "MIT OR Apache-2.0"}, "http 0.2.8": {"name": "http", "version": "0.2.8", "repository": {"Http": {"url": "https://static.crates.io/crates/http/0.2.8/download", "sha256": "75f43d41e26995c17e71ee126451dd3941010b0514a81a9d11f3b341debc2399"}}, "targets": [{"Library": {"crate_name": "http", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "http", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "bytes 1.2.1", "target": "bytes"}, {"id": "fnv 1.0.7", "target": "fnv"}, {"id": "itoa 1.0.3", "target": "itoa"}], "selects": {}}, "edition": "2018", "version": "0.2.8"}, "license": "MIT OR Apache-2.0"}, "http-body 0.4.5": {"name": "http-body", "version": "0.4.5", "repository": {"Http": {"url": "https://static.crates.io/crates/http-body/0.4.5/download", "sha256": "d5f38f16d184e36f2408a55281cd658ecbd3ca05cce6d6510a176eca393e26d1"}}, "targets": [{"Library": {"crate_name": "http_body", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "http_body", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "bytes 1.2.1", "target": "bytes"}, {"id": "http 0.2.8", "target": "http"}, {"id": "pin-project-lite 0.2.9", "target": "pin_project_lite"}], "selects": {}}, "edition": "2018", "version": "0.4.5"}, "license": "MIT"}, "httparse 1.8.0": {"name": "httparse", "version": "1.8.0", "repository": {"Http": {"url": "https://static.crates.io/crates/httparse/1.8.0/download", "sha256": "d897f394bad6a705d5f4104762e116a75639e470d80901eed05a860a95cb1904"}}, "targets": [{"Library": {"crate_name": "httparse", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "httparse", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "deps": {"common": [{"id": "httparse 1.8.0", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "1.8.0"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT/Apache-2.0"}, "httpdate 1.0.2": {"name": "httpdate", "version": "1.0.2", "repository": {"Http": {"url": "https://static.crates.io/crates/httpdate/1.0.2/download", "sha256": "c4a1e36c821dbe04574f602848a19f742f4fb3c98d40449f11bcad18d6b17421"}}, "targets": [{"Library": {"crate_name": "httpdate", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "httpdate", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.0.2"}, "license": "MIT/Apache-2.0"}, "humantime 2.1.0": {"name": "humantime", "version": "2.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/humantime/2.1.0/download", "sha256": "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"}}, "targets": [{"Library": {"crate_name": "humantime", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "humantime", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "2.1.0"}, "license": "MIT/Apache-2.0"}, "hyper 0.14.20": {"name": "hyper", "version": "0.14.20", "repository": {"Http": {"url": "https://static.crates.io/crates/hyper/0.14.20/download", "sha256": "02c929dc5c39e335a03c405292728118860721b10190d98c2a0f0efd5baafbac"}}, "targets": [{"Library": {"crate_name": "hyper", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "hyper", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["client", "h2", "http1", "http2", "runtime", "socket2", "tcp"], "selects": {}}, "deps": {"common": [{"id": "bytes 1.2.1", "target": "bytes"}, {"id": "futures-channel 0.3.24", "target": "futures_channel"}, {"id": "futures-core 0.3.24", "target": "futures_core"}, {"id": "futures-util 0.3.24", "target": "futures_util"}, {"id": "h2 0.3.14", "target": "h2"}, {"id": "http 0.2.8", "target": "http"}, {"id": "http-body 0.4.5", "target": "http_body"}, {"id": "httparse 1.8.0", "target": "httparse"}, {"id": "httpdate 1.0.2", "target": "httpdate"}, {"id": "itoa 1.0.3", "target": "itoa"}, {"id": "pin-project-lite 0.2.9", "target": "pin_project_lite"}, {"id": "socket2 0.4.7", "target": "socket2"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "tower-service 0.3.2", "target": "tower_service"}, {"id": "tracing 0.1.36", "target": "tracing"}, {"id": "want 0.3.0", "target": "want"}], "selects": {}}, "edition": "2018", "version": "0.14.20"}, "license": "MIT"}, "hyper-rustls 0.23.0": {"name": "hyper-rustls", "version": "0.23.0", "repository": {"Http": {"url": "https://static.crates.io/crates/hyper-rustls/0.23.0/download", "sha256": "d87c48c02e0dc5e3b849a2041db3029fd066650f8f717c07bf8ed78ccb895cac"}}, "targets": [{"Library": {"crate_name": "hyper_rustls", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "hyper_rustls", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "http 0.2.8", "target": "http"}, {"id": "hyper 0.14.20", "target": "hyper"}, {"id": "rustls 0.20.6", "target": "rustls"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "tokio-rustls 0.23.4", "target": "tokio_rustls"}], "selects": {}}, "edition": "2018", "version": "0.23.0"}, "license": "Apache-2.0/ISC/MIT"}, "hyper-tls 0.5.0": {"name": "hyper-tls", "version": "0.5.0", "repository": {"Http": {"url": "https://static.crates.io/crates/hyper-tls/0.5.0/download", "sha256": "d6183ddfa99b85da61a140bea0efc93fdf56ceaa041b37d553518030827f9905"}}, "targets": [{"Library": {"crate_name": "hyper_tls", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "hyper_tls", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "bytes 1.2.1", "target": "bytes"}, {"id": "hyper 0.14.20", "target": "hyper"}, {"id": "native-tls 0.2.10", "target": "native_tls"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "tokio-native-tls 0.3.0", "target": "tokio_native_tls"}], "selects": {}}, "edition": "2018", "version": "0.5.0"}, "license": "MIT/Apache-2.0"}, "iana-time-zone 0.1.48": {"name": "iana-time-zone", "version": "0.1.48", "repository": {"Http": {"url": "https://static.crates.io/crates/iana-time-zone/0.1.48/download", "sha256": "237a0714f28b1ee39ccec0770ccb544eb02c9ef2c82bb096230eefcffa6468b0"}}, "targets": [{"Library": {"crate_name": "iana_time_zone", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "iana_time_zone", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["fallback"], "selects": {}}, "deps": {"common": [], "selects": {"cfg(any(target_os = \"macos\", target_os = \"ios\"))": [{"id": "core-foundation-sys 0.8.3", "target": "core_foundation_sys"}], "cfg(target_arch = \"wasm32\")": [{"id": "js-sys 0.3.60", "target": "js_sys"}, {"id": "wasm-bindgen 0.2.83", "target": "wasm_bindgen"}], "cfg(target_os = \"android\")": [{"id": "android_system_properties 0.1.5", "target": "android_system_properties"}, {"id": "once_cell 1.14.0", "target": "once_cell"}], "cfg(target_os = \"windows\")": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.1.48"}, "license": "MIT OR Apache-2.0"}, "ident_case 1.0.1": {"name": "ident_case", "version": "1.0.1", "repository": {"Http": {"url": "https://static.crates.io/crates/ident_case/1.0.1/download", "sha256": "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"}}, "targets": [{"Library": {"crate_name": "ident_case", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "ident_case", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "1.0.1"}, "license": "MIT/Apache-2.0"}, "idna 0.3.0": {"name": "idna", "version": "0.3.0", "repository": {"Http": {"url": "https://static.crates.io/crates/idna/0.3.0/download", "sha256": "e14ddfc70884202db2244c223200c204c2bda1bc6e0998d11b5e024d657209e6"}}, "targets": [{"Library": {"crate_name": "idna", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "idna", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "unicode-bidi 0.3.8", "target": "unicode_bidi"}, {"id": "unicode-normalization 0.1.22", "target": "unicode_normalization"}], "selects": {}}, "edition": "2018", "version": "0.3.0"}, "license": "MIT OR Apache-2.0"}, "indexmap 1.9.1": {"name": "indexmap", "version": "1.9.1", "repository": {"Http": {"url": "https://static.crates.io/crates/indexmap/1.9.1/download", "sha256": "10a35a97730320ffe8e2d410b5d3b69279b98d2c14bdb8b70ea89ecf7888d41e"}}, "targets": [{"Library": {"crate_name": "indexmap", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "indexmap", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["std"], "selects": {}}, "deps": {"common": [{"id": "hashbrown 0.12.3", "target": "hashbrown"}, {"id": "indexmap 1.9.1", "target": "build_script_build"}, {"id": "serde 1.0.144", "target": "serde"}], "selects": {}}, "edition": "2021", "version": "1.9.1"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "autocfg 1.1.0", "target": "autocfg"}], "selects": {}}}, "license": "Apache-2.0 OR MIT"}, "inout 0.1.3": {"name": "inout", "version": "0.1.3", "repository": {"Http": {"url": "https://static.crates.io/crates/inout/0.1.3/download", "sha256": "a0c10553d664a4d0bcff9f4215d0aac67a639cc68ef660840afe309b807bc9f5"}}, "targets": [{"Library": {"crate_name": "inout", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "inout", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["block-padding"], "selects": {}}, "deps": {"common": [{"id": "block-padding 0.3.2", "target": "block_padding"}, {"id": "generic-array 0.14.6", "target": "generic_array"}], "selects": {}}, "edition": "2021", "version": "0.1.3"}, "license": "MIT OR Apache-2.0"}, "instant 0.1.12": {"name": "instant", "version": "0.1.12", "repository": {"Http": {"url": "https://static.crates.io/crates/instant/0.1.12/download", "sha256": "7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c"}}, "targets": [{"Library": {"crate_name": "instant", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "instant", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}], "selects": {}}, "edition": "2018", "version": "0.1.12"}, "license": "BSD-3-<PERSON><PERSON>"}, "io-lifetimes 0.7.3": {"name": "io-lifetimes", "version": "0.7.3", "repository": {"Http": {"url": "https://static.crates.io/crates/io-lifetimes/0.7.3/download", "sha256": "1ea37f355c05dde75b84bba2d767906ad522e97cd9e2eef2be7a4ab7fb442c06"}}, "targets": [{"Library": {"crate_name": "io_lifetimes", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "io_lifetimes", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "io-lifetimes 0.7.3", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.7.3"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"}, "ipnet 2.5.0": {"name": "ipnet", "version": "2.5.0", "repository": {"Http": {"url": "https://static.crates.io/crates/ipnet/2.5.0/download", "sha256": "879d54834c8c76457ef4293a689b2a8c59b076067ad77b15efafbb05f92a592b"}}, "targets": [{"Library": {"crate_name": "ipnet", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "ipnet", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "edition": "2018", "version": "2.5.0"}, "license": "MIT OR Apache-2.0"}, "itoa 1.0.3": {"name": "itoa", "version": "1.0.3", "repository": {"Http": {"url": "https://static.crates.io/crates/itoa/1.0.3/download", "sha256": "6c8af84674fe1f223a982c933a0ee1086ac4d4052aa0fb8060c12c6ad838e754"}}, "targets": [{"Library": {"crate_name": "itoa", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "itoa", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.0.3"}, "license": "MIT OR Apache-2.0"}, "js-sys 0.3.60": {"name": "js-sys", "version": "0.3.60", "repository": {"Http": {"url": "https://static.crates.io/crates/js-sys/0.3.60/download", "sha256": "49409df3e3bf0856b916e2ceaca09ee28e6871cf7d9ce97a692cacfdb2a25a47"}}, "targets": [{"Library": {"crate_name": "js_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "js_sys", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "wasm-bindgen 0.2.83", "target": "wasm_bindgen"}], "selects": {}}, "edition": "2018", "version": "0.3.60"}, "license": "MIT/Apache-2.0"}, "k8s-openapi 0.14.0": {"name": "k8s-openapi", "version": "0.14.0", "repository": {"Http": {"url": "https://static.crates.io/crates/k8s-openapi/0.14.0/download", "sha256": "0489fc937cc7616a9abfa61bf39c250d7e32e1325ef028c8d9278dd24ea395b3"}}, "targets": [{"Library": {"crate_name": "k8s_openapi", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "k8s_openapi", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["api", "default", "http", "percent-encoding", "url", "v1_23"], "selects": {}}, "deps": {"common": [{"id": "base64 0.13.0", "target": "base64"}, {"id": "bytes 1.2.1", "target": "bytes"}, {"id": "chrono 0.4.22", "target": "chrono"}, {"id": "http 0.2.8", "target": "http"}, {"id": "k8s-openapi 0.14.0", "target": "build_script_build"}, {"id": "percent-encoding 2.2.0", "target": "percent_encoding"}, {"id": "serde 1.0.144", "target": "serde"}, {"id": "serde-value 0.7.0", "target": "serde_value"}, {"id": "serde_json 1.0.85", "target": "serde_json"}, {"id": "url 2.3.1", "target": "url"}], "selects": {}}, "edition": "2018", "version": "0.14.0"}, "build_script_attrs": {"data_glob": ["**"], "links": "k8s-openapi-0.14.0"}, "license": "Apache-2.0"}, "lazy_static 1.4.0": {"name": "lazy_static", "version": "1.4.0", "repository": {"Http": {"url": "https://static.crates.io/crates/lazy_static/1.4.0/download", "sha256": "e2abad23fbc42b3700f2f279844dc832adb2b2eb069b2df918f455c4e18cc646"}}, "targets": [{"Library": {"crate_name": "lazy_static", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "lazy_static", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "1.4.0"}, "license": "MIT/Apache-2.0"}, "libc 0.2.133": {"name": "libc", "version": "0.2.133", "repository": {"Http": {"url": "https://static.crates.io/crates/libc/0.2.133/download", "sha256": "c0f80d65747a3e43d1596c7c5492d95d5edddaabd45a7fcdb02b95f644164966"}}, "targets": [{"Library": {"crate_name": "libc", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "libc", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {"aarch64-apple-darwin": ["extra_traits"], "aarch64-apple-ios": ["extra_traits"], "aarch64-apple-ios-sim": ["extra_traits"], "aarch64-fuchsia": ["extra_traits"], "aarch64-linux-android": ["extra_traits"], "aarch64-unknown-linux-gnu": ["extra_traits"], "aarch64-unknown-nixos-gnu": ["extra_traits"], "aarch64-unknown-nto-qnx710": ["extra_traits"], "arm-unknown-linux-gnueabi": ["extra_traits"], "armv7-linux-androideabi": ["extra_traits"], "armv7-unknown-linux-gnueabi": ["extra_traits"], "i686-apple-darwin": ["extra_traits"], "i686-linux-android": ["extra_traits"], "i686-unknown-freebsd": ["extra_traits"], "i686-unknown-linux-gnu": ["extra_traits"], "powerpc-unknown-linux-gnu": ["extra_traits"], "s390x-unknown-linux-gnu": ["extra_traits"], "x86_64-apple-darwin": ["extra_traits"], "x86_64-apple-ios": ["extra_traits"], "x86_64-fuchsia": ["extra_traits"], "x86_64-linux-android": ["extra_traits"], "x86_64-unknown-freebsd": ["extra_traits"], "x86_64-unknown-linux-gnu": ["extra_traits"], "x86_64-unknown-nixos-gnu": ["extra_traits"]}}, "deps": {"common": [{"id": "libc 0.2.133", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "version": "0.2.133"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "linux-raw-sys 0.0.46": {"name": "linux-raw-sys", "version": "0.0.46", "repository": {"Http": {"url": "https://static.crates.io/crates/linux-raw-sys/0.0.46/download", "sha256": "d4d2456c373231a208ad294c33dc5bff30051eafd954cd4caae83a712b12854d"}}, "targets": [{"Library": {"crate_name": "linux_raw_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "linux_raw_sys", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["general", "no_std"], "selects": {"aarch64-unknown-linux-gnu": ["errno", "ioctl"], "aarch64-unknown-nixos-gnu": ["errno", "ioctl"], "arm-unknown-linux-gnueabi": ["errno", "ioctl"], "armv7-unknown-linux-gnueabi": ["errno", "ioctl"], "i686-unknown-linux-gnu": ["errno", "ioctl"], "x86_64-unknown-linux-gnu": ["errno", "ioctl"], "x86_64-unknown-nixos-gnu": ["errno", "ioctl"]}}, "edition": "2018", "version": "0.0.46"}, "license": "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"}, "lock_api 0.4.9": {"name": "lock_api", "version": "0.4.9", "repository": {"Http": {"url": "https://static.crates.io/crates/lock_api/0.4.9/download", "sha256": "435011366fe56583b16cf956f9df0095b405b82d76425bc8981c0e22e60ec4df"}}, "targets": [{"Library": {"crate_name": "lock_api", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "lock_api", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "lock_api 0.4.9", "target": "build_script_build"}, {"id": "scopeguard 1.1.0", "target": "scopeguard"}], "selects": {}}, "edition": "2018", "version": "0.4.9"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "autocfg 1.1.0", "target": "autocfg"}], "selects": {}}}, "license": "MIT OR Apache-2.0"}, "log 0.4.17": {"name": "log", "version": "0.4.17", "repository": {"Http": {"url": "https://static.crates.io/crates/log/0.4.17/download", "sha256": "abb12e687cfb44aa40f41fc3978ef76448f9b6038cad6aef4259d3c095a2382e"}}, "targets": [{"Library": {"crate_name": "log", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "log", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["std"], "selects": {}}, "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "log 0.4.17", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "version": "0.4.17"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "memchr 2.5.0": {"name": "memchr", "version": "2.5.0", "repository": {"Http": {"url": "https://static.crates.io/crates/memchr/2.5.0/download", "sha256": "2dffe52ecf27772e601905b7522cb4ef790d2cc203488bbd0e2fe85fcb74566d"}}, "targets": [{"Library": {"crate_name": "memchr", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "memchr", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "deps": {"common": [{"id": "memchr 2.5.0", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "2.5.0"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "Unlicense/MIT"}, "mime 0.3.16": {"name": "mime", "version": "0.3.16", "repository": {"Http": {"url": "https://static.crates.io/crates/mime/0.3.16/download", "sha256": "2a60c7ce501c71e03a9c9c0d35b861413ae925bd979cc7a4e30d060069aaac8d"}}, "targets": [{"Library": {"crate_name": "mime", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "mime", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.3.16"}, "license": "MIT/Apache-2.0"}, "mio 0.8.4": {"name": "mio", "version": "0.8.4", "repository": {"Http": {"url": "https://static.crates.io/crates/mio/0.8.4/download", "sha256": "57ee1c23c7c63b0c9250c339ffdc69255f110b298b901b9f6c82547b7b87caaf"}}, "targets": [{"Library": {"crate_name": "mio", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "mio", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "net", "os-ext", "os-poll"], "selects": {}}, "deps": {"common": [{"id": "log 0.4.17", "target": "log"}], "selects": {"cfg(target_os = \"wasi\")": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "wasi 0.11.0+wasi-snapshot-preview1", "target": "wasi"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "windows-sys 0.36.1", "target": "windows_sys"}]}}, "edition": "2018", "version": "0.8.4"}, "license": "MIT"}, "native-tls 0.2.10": {"name": "native-tls", "version": "0.2.10", "repository": {"Http": {"url": "https://static.crates.io/crates/native-tls/0.2.10/download", "sha256": "fd7e2f3618557f980e0b17e8856252eee3c97fa12c54dff0ca290fb6266ca4a9"}}, "targets": [{"Library": {"crate_name": "native_tls", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "native_tls", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "native-tls 0.2.10", "target": "build_script_build"}], "selects": {"cfg(any(target_os = \"macos\", target_os = \"ios\"))": [{"id": "lazy_static 1.4.0", "target": "lazy_static"}, {"id": "libc 0.2.133", "target": "libc"}, {"id": "security-framework 2.7.0", "target": "security_framework"}, {"id": "security-framework-sys 2.6.1", "target": "security_framework_sys"}, {"id": "tempfile 3.3.0", "target": "tempfile"}], "cfg(not(any(target_os = \"windows\", target_os = \"macos\", target_os = \"ios\")))": [{"id": "log 0.4.17", "target": "log"}, {"id": "openssl 0.10.41", "target": "openssl"}, {"id": "openssl-probe 0.1.5", "target": "openssl_probe"}, {"id": "openssl-sys 0.9.75", "target": "openssl_sys"}], "cfg(target_os = \"windows\")": [{"id": "schannel 0.1.20", "target": "schannel"}]}}, "edition": "2015", "version": "0.2.10"}, "build_script_attrs": {"data_glob": ["**"], "link_deps": {"common": [], "selects": {"cfg(not(any(target_os = \"windows\", target_os = \"macos\", target_os = \"ios\")))": [{"id": "openssl-sys 0.9.75", "target": "openssl_sys"}]}}}, "license": "MIT/Apache-2.0"}, "nibble_vec 0.1.0": {"name": "nibble_vec", "version": "0.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/nibble_vec/0.1.0/download", "sha256": "77a5d83df9f36fe23f0c3648c6bbb8b0298bb5f1939c8f2704431371f4b84d43"}}, "targets": [{"Library": {"crate_name": "nibble_vec", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "nibble_vec", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "smallvec 1.9.0", "target": "smallvec"}], "selects": {}}, "edition": "2018", "version": "0.1.0"}, "license": "MIT"}, "nix 0.24.2": {"name": "nix", "version": "0.24.2", "repository": {"Http": {"url": "https://static.crates.io/crates/nix/0.24.2/download", "sha256": "195cdbc1741b8134346d515b3a56a1c94b0912758009cfd53f99ea0f57b065fc"}}, "targets": [{"Library": {"crate_name": "nix", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "nix", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["fs", "ioctl", "poll", "process", "signal", "term"], "selects": {}}, "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2018", "version": "0.24.2"}, "license": "MIT"}, "nix 0.25.0": {"name": "nix", "version": "0.25.0", "repository": {"Http": {"url": "https://static.crates.io/crates/nix/0.25.0/download", "sha256": "e322c04a9e3440c327fca7b6c8a63e6890a32fa2ad689db972425f07e0d22abb"}}, "targets": [{"Library": {"crate_name": "nix", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "nix", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["fs", "process", "signal"], "selects": {}}, "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2018", "version": "0.25.0"}, "license": "MIT"}, "num-integer 0.1.45": {"name": "num-integer", "version": "0.1.45", "repository": {"Http": {"url": "https://static.crates.io/crates/num-integer/0.1.45/download", "sha256": "225d3389fb3509a24c93f5c29eb6bde2586b98d9f016636dff58d7c6f7569cd9"}}, "targets": [{"Library": {"crate_name": "num_integer", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "num_integer", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "num-integer 0.1.45", "target": "build_script_build"}, {"id": "num-traits 0.2.15", "target": "num_traits"}], "selects": {}}, "edition": "2015", "version": "0.1.45"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "autocfg 1.1.0", "target": "autocfg"}], "selects": {}}}, "license": "MIT OR Apache-2.0"}, "num-traits 0.2.15": {"name": "num-traits", "version": "0.2.15", "repository": {"Http": {"url": "https://static.crates.io/crates/num-traits/0.2.15/download", "sha256": "578ede34cf02f8924ab9447f50c28075b4d3e5b269972345e7e0372b38c6cdcd"}}, "targets": [{"Library": {"crate_name": "num_traits", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "num_traits", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["std"], "selects": {}}, "deps": {"common": [{"id": "num-traits 0.2.15", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "version": "0.2.15"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "autocfg 1.1.0", "target": "autocfg"}], "selects": {}}}, "license": "MIT OR Apache-2.0"}, "num_cpus 1.13.1": {"name": "num_cpus", "version": "1.13.1", "repository": {"Http": {"url": "https://static.crates.io/crates/num_cpus/1.13.1/download", "sha256": "19e64526ebdee182341572e50e9ad03965aa510cd94427a4549448f285e957a1"}}, "targets": [{"Library": {"crate_name": "num_cpus", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "num_cpus", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(all(any(target_arch = \"x86_64\", target_arch = \"aarch64\"), target_os = \"hermit\"))": [{"id": "hermit-abi 0.1.19", "target": "hermit_abi"}], "cfg(not(windows))": [{"id": "libc 0.2.133", "target": "libc"}]}}, "edition": "2015", "version": "1.13.1"}, "license": "MIT OR Apache-2.0"}, "num_threads 0.1.6": {"name": "num_threads", "version": "0.1.6", "repository": {"Http": {"url": "https://static.crates.io/crates/num_threads/0.1.6/download", "sha256": "2819ce041d2ee131036f4fc9d6ae7ae125a3a40e97ba64d04fe799ad9dabbb44"}}, "targets": [{"Library": {"crate_name": "num_threads", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "num_threads", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(any(target_os = \"macos\", target_os = \"ios\", target_os = \"freebsd\"))": [{"id": "libc 0.2.133", "target": "libc"}]}}, "edition": "2015", "version": "0.1.6"}, "license": "MIT OR Apache-2.0"}, "once_cell 1.14.0": {"name": "once_cell", "version": "1.14.0", "repository": {"Http": {"url": "https://static.crates.io/crates/once_cell/1.14.0/download", "sha256": "2f7254b99e31cad77da24b08ebf628882739a608578bb1bcdfc1f9c21260d7c0"}}, "targets": [{"Library": {"crate_name": "once_cell", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "once_cell", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "race", "std"], "selects": {}}, "edition": "2018", "version": "1.14.0"}, "license": "MIT OR Apache-2.0"}, "openssl 0.10.41": {"name": "openssl", "version": "0.10.41", "repository": {"Http": {"url": "https://static.crates.io/crates/openssl/0.10.41/download", "sha256": "618febf65336490dfcf20b73f885f5651a0c89c64c2d4a8c3662585a70bf5bd0"}}, "targets": [{"Library": {"crate_name": "openssl", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "openssl", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "foreign-types 0.3.2", "target": "foreign_types"}, {"id": "libc 0.2.133", "target": "libc"}, {"id": "once_cell 1.14.0", "target": "once_cell"}, {"id": "openssl 0.10.41", "target": "build_script_build"}, {"id": "openssl-sys 0.9.75", "target": "openssl_sys", "alias": "ffi"}], "selects": {}}, "edition": "2018", "proc_macro_deps": {"common": [{"id": "openssl-macros 0.1.0", "target": "openssl_macros"}], "selects": {}}, "version": "0.10.41"}, "build_script_attrs": {"data_glob": ["**"], "link_deps": {"common": [{"id": "openssl-sys 0.9.75", "target": "openssl_sys", "alias": "ffi"}], "selects": {}}}, "license": "Apache-2.0"}, "openssl-macros 0.1.0": {"name": "openssl-macros", "version": "0.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/openssl-macros/0.1.0/download", "sha256": "b501e44f11665960c7e7fcf062c7d96a14ade4aa98116c004b2e37b5be7d736c"}}, "targets": [{"ProcMacro": {"crate_name": "openssl_macros", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "openssl_macros", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2018", "version": "0.1.0"}, "license": "MIT/Apache-2.0"}, "openssl-probe 0.1.5": {"name": "openssl-probe", "version": "0.1.5", "repository": {"Http": {"url": "https://static.crates.io/crates/openssl-probe/0.1.5/download", "sha256": "ff011a302c396a5197692431fc1948019154afc178baf7d8e37367442a4601cf"}}, "targets": [{"Library": {"crate_name": "openssl_probe", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "openssl_probe", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.1.5"}, "license": "MIT/Apache-2.0"}, "openssl-sys 0.9.75": {"name": "openssl-sys", "version": "0.9.75", "repository": {"Http": {"url": "https://static.crates.io/crates/openssl-sys/0.9.75/download", "sha256": "e5f9bd0c2710541a3cda73d6f9ac4f1b240de4ae261065d309dbe73d9dceb42f"}}, "targets": [{"Library": {"crate_name": "openssl_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_main", "crate_root": "build/main.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "openssl_sys", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "openssl-sys 0.9.75", "target": "build_script_main"}], "selects": {}}, "edition": "2015", "version": "0.9.75"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "autocfg 1.1.0", "target": "autocfg"}, {"id": "cc 1.0.73", "target": "cc"}, {"id": "pkg-config 0.3.25", "target": "pkg_config"}], "selects": {"cfg(target_env = \"msvc\")": [{"id": "vcpkg 0.2.15", "target": "vcpkg"}]}}, "links": "openssl"}, "license": "MIT"}, "ordered-float 2.10.0": {"name": "ordered-float", "version": "2.10.0", "repository": {"Http": {"url": "https://static.crates.io/crates/ordered-float/2.10.0/download", "sha256": "7940cf2ca942593318d07fcf2596cdca60a85c9e7fab408a5e21a4f9dcd40d87"}}, "targets": [{"Library": {"crate_name": "ordered_float", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "ordered_float", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "deps": {"common": [{"id": "num-traits 0.2.15", "target": "num_traits"}], "selects": {}}, "edition": "2018", "version": "2.10.0"}, "license": "MIT"}, "os_pipe 0.9.2": {"name": "os_pipe", "version": "0.9.2", "repository": {"Http": {"url": "https://static.crates.io/crates/os_pipe/0.9.2/download", "sha256": "fb233f06c2307e1f5ce2ecad9f8121cffbbee2c95428f44ea85222e460d0d213"}}, "targets": [{"Library": {"crate_name": "os_pipe", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "os_pipe", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(not(windows))": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.9.2"}, "license": "MIT"}, "os_pipe 1.0.1": {"name": "os_pipe", "version": "1.0.1", "repository": {"Http": {"url": "https://static.crates.io/crates/os_pipe/1.0.1/download", "sha256": "2c92f2b54f081d635c77e7120862d48db8e91f7f21cef23ab1b4fe9971c59f55"}}, "targets": [{"Library": {"crate_name": "os_pipe", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "os_pipe", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(not(windows))": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "1.0.1"}, "license": "MIT"}, "os_str_bytes 6.3.0": {"name": "os_str_bytes", "version": "6.3.0", "repository": {"Http": {"url": "https://static.crates.io/crates/os_str_bytes/6.3.0/download", "sha256": "9ff7415e9ae3fff1225851df9e0d9e4e5479f947619774677a63572e55e80eff"}}, "targets": [{"Library": {"crate_name": "os_str_bytes", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "os_str_bytes", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["raw_os_str"], "selects": {}}, "edition": "2021", "version": "6.3.0"}, "license": "MIT OR Apache-2.0"}, "p12 0.6.3": {"name": "p12", "version": "0.6.3", "repository": {"Http": {"url": "https://static.crates.io/crates/p12/0.6.3/download", "sha256": "d4873306de53fe82e7e484df31e1e947d61514b6ea2ed6cd7b45d63006fd9224"}}, "targets": [{"Library": {"crate_name": "p12", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "p12", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cbc 0.1.2", "target": "cbc"}, {"id": "cipher 0.4.3", "target": "cipher"}, {"id": "des 0.8.1", "target": "des"}, {"id": "getrandom 0.2.7", "target": "getrandom"}, {"id": "hmac 0.12.1", "target": "hmac"}, {"id": "lazy_static 1.4.0", "target": "lazy_static"}, {"id": "rc2 0.8.1", "target": "rc2"}, {"id": "sha1 0.10.5", "target": "sha1"}, {"id": "yasna 0.5.0", "target": "yasna"}], "selects": {}}, "edition": "2021", "version": "0.6.3"}, "license": "MIT OR Apache-2.0"}, "parking_lot 0.12.1": {"name": "parking_lot", "version": "0.12.1", "repository": {"Http": {"url": "https://static.crates.io/crates/parking_lot/0.12.1/download", "sha256": "3742b2c103b9f06bc9fff0a37ff4912935851bee6d36f3c02bcc755bcfec228f"}}, "targets": [{"Library": {"crate_name": "parking_lot", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "parking_lot", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "deps": {"common": [{"id": "lock_api 0.4.9", "target": "lock_api"}, {"id": "parking_lot_core 0.9.3", "target": "parking_lot_core"}], "selects": {}}, "edition": "2018", "version": "0.12.1"}, "license": "MIT OR Apache-2.0"}, "parking_lot_core 0.9.3": {"name": "parking_lot_core", "version": "0.9.3", "repository": {"Http": {"url": "https://static.crates.io/crates/parking_lot_core/0.9.3/download", "sha256": "09a279cbf25cb0757810394fbc1e359949b59e348145c643a939a525692e6929"}}, "targets": [{"Library": {"crate_name": "parking_lot_core", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "parking_lot_core", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "parking_lot_core 0.9.3", "target": "build_script_build"}, {"id": "smallvec 1.9.0", "target": "smallvec"}], "selects": {"cfg(target_os = \"redox\")": [{"id": "redox_syscall 0.2.16", "target": "syscall"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "windows-sys 0.36.1", "target": "windows_sys"}]}}, "edition": "2018", "version": "0.9.3"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "pem 1.1.0": {"name": "pem", "version": "1.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/pem/1.1.0/download", "sha256": "03c64931a1a212348ec4f3b4362585eca7159d0d09cbdf4a7f74f02173596fd4"}}, "targets": [{"Library": {"crate_name": "pem", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "pem", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "base64 0.13.0", "target": "base64"}], "selects": {}}, "edition": "2018", "version": "1.1.0"}, "license": "MIT"}, "percent-encoding 2.2.0": {"name": "percent-encoding", "version": "2.2.0", "repository": {"Http": {"url": "https://static.crates.io/crates/percent-encoding/2.2.0/download", "sha256": "478c572c3d73181ff3c2539045f6eb99e5491218eae919370993b890cdbdd98e"}}, "targets": [{"Library": {"crate_name": "percent_encoding", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "percent_encoding", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default"], "selects": {}}, "edition": "2018", "version": "2.2.0"}, "license": "MIT OR Apache-2.0"}, "pin-project-lite 0.2.9": {"name": "pin-project-lite", "version": "0.2.9", "repository": {"Http": {"url": "https://static.crates.io/crates/pin-project-lite/0.2.9/download", "sha256": "e0a7ae3ac2f1173085d398531c705756c94a4c56843785df85a60c1a0afac116"}}, "targets": [{"Library": {"crate_name": "pin_project_lite", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "pin_project_lite", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "0.2.9"}, "license": "Apache-2.0 OR MIT"}, "pin-utils 0.1.0": {"name": "pin-utils", "version": "0.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/pin-utils/0.1.0/download", "sha256": "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"}}, "targets": [{"Library": {"crate_name": "pin_utils", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "pin_utils", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "0.1.0"}, "license": "MIT OR Apache-2.0"}, "pkg-config 0.3.25": {"name": "pkg-config", "version": "0.3.25", "repository": {"Http": {"url": "https://static.crates.io/crates/pkg-config/0.3.25/download", "sha256": "1df8c4ec4b0627e53bdf214615ad287367e482558cf84b109250b37464dc03ae"}}, "targets": [{"Library": {"crate_name": "pkg_config", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "pkg_config", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.3.25"}, "license": "MIT OR Apache-2.0"}, "proc-macro2 1.0.43": {"name": "proc-macro2", "version": "1.0.43", "repository": {"Http": {"url": "https://static.crates.io/crates/proc-macro2/1.0.43/download", "sha256": "0a2ca2c61bc9f3d74d2886294ab7b9853abd9c1ad903a3ac7815c58989bb7bab"}}, "targets": [{"Library": {"crate_name": "proc_macro2", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "proc_macro2", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "proc-macro"], "selects": {}}, "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "build_script_build"}, {"id": "unicode-ident 1.0.4", "target": "unicode_ident"}], "selects": {}}, "edition": "2018", "version": "1.0.43"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "quote 1.0.21": {"name": "quote", "version": "1.0.21", "repository": {"Http": {"url": "https://static.crates.io/crates/quote/1.0.21/download", "sha256": "bbe448f377a7d6961e30f5955f9b8d106c3f5e449d493ee1b125c1d43c2b5179"}}, "targets": [{"Library": {"crate_name": "quote", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "quote", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "proc-macro"], "selects": {}}, "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "1.0.21"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "radix_trie 0.2.1": {"name": "radix_trie", "version": "0.2.1", "repository": {"Http": {"url": "https://static.crates.io/crates/radix_trie/0.2.1/download", "sha256": "c069c179fcdc6a2fe24d8d18305cf085fdbd4f922c041943e203685d6a1c58fd"}}, "targets": [{"Library": {"crate_name": "radix_trie", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "radix_trie", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "endian-type 0.1.2", "target": "endian_type"}, {"id": "nibble_vec 0.1.0", "target": "nibble_vec"}], "selects": {}}, "edition": "2018", "version": "0.2.1"}, "license": "MIT"}, "rand 0.4.6": {"name": "rand", "version": "0.4.6", "repository": {"Http": {"url": "https://static.crates.io/crates/rand/0.4.6/download", "sha256": "552840b97013b1a26992c11eac34bdd778e464601a4c2054b5f0bff7c6761293"}}, "targets": [{"Library": {"crate_name": "rand", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rand", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "libc", "std"], "selects": {}}, "deps": {"common": [], "selects": {"cfg(target_env = \"sgx\")": [{"id": "rand_core 0.3.1", "target": "rand_core"}, {"id": "rdrand 0.4.0", "target": "rdrand"}], "cfg(target_os = \"fuchsia\")": [{"id": "fuchsia-cprng 0.1.1", "target": "fuchsia_cprng"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2015", "version": "0.4.6"}, "license": "MIT/Apache-2.0"}, "rand_core 0.3.1": {"name": "rand_core", "version": "0.3.1", "repository": {"Http": {"url": "https://static.crates.io/crates/rand_core/0.3.1/download", "sha256": "7a6fdeb83b075e8266dcc8762c22776f6877a63111121f5f8c7411e5be7eed4b"}}, "targets": [{"Library": {"crate_name": "rand_core", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rand_core", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "rand_core 0.4.2", "target": "rand_core"}], "selects": {}}, "edition": "2015", "version": "0.3.1"}, "license": "MIT/Apache-2.0"}, "rand_core 0.4.2": {"name": "rand_core", "version": "0.4.2", "repository": {"Http": {"url": "https://static.crates.io/crates/rand_core/0.4.2/download", "sha256": "9c33a3c44ca05fa6f1807d8e6743f3824e8509beca625669633be0acbdf509dc"}}, "targets": [{"Library": {"crate_name": "rand_core", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rand_core", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.4.2"}, "license": "MIT/Apache-2.0"}, "rc2 0.8.1": {"name": "rc2", "version": "0.8.1", "repository": {"Http": {"url": "https://static.crates.io/crates/rc2/0.8.1/download", "sha256": "62c64daa8e9438b84aaae55010a93f396f8e60e3911590fcba770d04643fc1dd"}}, "targets": [{"Library": {"crate_name": "rc2", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rc2", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cipher 0.4.3", "target": "cipher"}], "selects": {}}, "edition": "2021", "version": "0.8.1"}, "license": "MIT OR Apache-2.0"}, "rdrand 0.4.0": {"name": "rdrand", "version": "0.4.0", "repository": {"Http": {"url": "https://static.crates.io/crates/rdrand/0.4.0/download", "sha256": "678054eb77286b51581ba43620cc911abf02758c91f93f479767aed0f90458b2"}}, "targets": [{"Library": {"crate_name": "rdrand", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rdrand", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "rand_core 0.3.1", "target": "rand_core"}], "selects": {}}, "edition": "2015", "version": "0.4.0"}, "license": "ISC"}, "redox_syscall 0.2.16": {"name": "redox_syscall", "version": "0.2.16", "repository": {"Http": {"url": "https://static.crates.io/crates/redox_syscall/0.2.16/download", "sha256": "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a"}}, "targets": [{"Library": {"crate_name": "syscall", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "syscall", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}], "selects": {}}, "edition": "2018", "version": "0.2.16"}, "license": "MIT"}, "redox_users 0.4.3": {"name": "redox_users", "version": "0.4.3", "repository": {"Http": {"url": "https://static.crates.io/crates/redox_users/0.4.3/download", "sha256": "b033d837a7cf162d7993aded9304e30a83213c648b6e389db233191f891e5c2b"}}, "targets": [{"Library": {"crate_name": "redox_users", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "redox_users", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "getrandom 0.2.7", "target": "getrandom"}, {"id": "redox_syscall 0.2.16", "target": "syscall"}, {"id": "thiserror 1.0.35", "target": "thiserror"}], "selects": {}}, "edition": "2018", "version": "0.4.3"}, "license": "MIT"}, "regex 1.6.0": {"name": "regex", "version": "1.6.0", "repository": {"Http": {"url": "https://static.crates.io/crates/regex/1.6.0/download", "sha256": "4c4eb3267174b8c6c2f654116623910a0fef09c4753f8dd83db29c48a0df988b"}}, "targets": [{"Library": {"crate_name": "regex", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "regex", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["aho-<PERSON><PERSON><PERSON>", "default", "memchr", "perf", "perf-cache", "perf-dfa", "perf-inline", "perf-literal", "std", "unicode", "unicode-age", "unicode-bool", "unicode-case", "unicode-gencat", "unicode-perl", "unicode-script", "unicode-segment"], "selects": {}}, "deps": {"common": [{"id": "aho-corasick 0.7.19", "target": "aho_corasick"}, {"id": "memchr 2.5.0", "target": "memchr"}, {"id": "regex-syntax 0.6.27", "target": "regex_syntax"}], "selects": {}}, "edition": "2018", "version": "1.6.0"}, "license": "MIT OR Apache-2.0"}, "regex-syntax 0.6.27": {"name": "regex-syntax", "version": "0.6.27", "repository": {"Http": {"url": "https://static.crates.io/crates/regex-syntax/0.6.27/download", "sha256": "a3f87b73ce11b1619a3c6332f45341e0047173771e8b8b73f87bfeefb7b56244"}}, "targets": [{"Library": {"crate_name": "regex_syntax", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "regex_syntax", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "unicode", "unicode-age", "unicode-bool", "unicode-case", "unicode-gencat", "unicode-perl", "unicode-script", "unicode-segment"], "selects": {}}, "edition": "2018", "version": "0.6.27"}, "license": "MIT OR Apache-2.0"}, "remove_dir_all 0.5.3": {"name": "remove_dir_all", "version": "0.5.3", "repository": {"Http": {"url": "https://static.crates.io/crates/remove_dir_all/0.5.3/download", "sha256": "3acd125665422973a33ac9d3dd2df85edad0f4ae9b00dafb1a05e43a9f5ef8e7"}}, "targets": [{"Library": {"crate_name": "remove_dir_all", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "remove_dir_all", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2015", "version": "0.5.3"}, "license": "MIT/Apache-2.0"}, "reqwest 0.11.12": {"name": "reqwest", "version": "0.11.12", "repository": {"Http": {"url": "https://static.crates.io/crates/reqwest/0.11.12/download", "sha256": "431949c384f4e2ae07605ccaa56d1d9d2ecdb5cadd4f9577ccfab29f2e5149fc"}}, "targets": [{"Library": {"crate_name": "reqwest", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "reqwest", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["__rustls", "__tls", "blocking", "default", "default-tls", "hyper-rustls", "hyper-tls", "json", "native-tls", "native-tls-crate", "rustls", "rustls-pemfile", "rustls-tls", "rustls-tls-webpki-roots", "serde_json", "tokio-native-tls", "tokio-rustls", "webpki-roots"], "selects": {}}, "deps": {"common": [{"id": "base64 0.13.0", "target": "base64"}, {"id": "bytes 1.2.1", "target": "bytes"}, {"id": "http 0.2.8", "target": "http"}, {"id": "serde 1.0.144", "target": "serde"}, {"id": "serde_json 1.0.85", "target": "serde_json"}, {"id": "serde_urlencoded 0.7.1", "target": "serde_urlencoded"}, {"id": "tower-service 0.3.2", "target": "tower_service"}, {"id": "url 2.3.1", "target": "url"}], "selects": {"cfg(not(target_arch = \"wasm32\"))": [{"id": "encoding_rs 0.8.31", "target": "encoding_rs"}, {"id": "futures-core 0.3.24", "target": "futures_core"}, {"id": "futures-util 0.3.24", "target": "futures_util"}, {"id": "h2 0.3.14", "target": "h2"}, {"id": "http-body 0.4.5", "target": "http_body"}, {"id": "hyper 0.14.20", "target": "hyper"}, {"id": "hyper-rustls 0.23.0", "target": "hyper_rustls"}, {"id": "hyper-tls 0.5.0", "target": "hyper_tls"}, {"id": "ipnet 2.5.0", "target": "ipnet"}, {"id": "log 0.4.17", "target": "log"}, {"id": "mime 0.3.16", "target": "mime"}, {"id": "native-tls 0.2.10", "target": "native_tls", "alias": "native_tls_crate"}, {"id": "once_cell 1.14.0", "target": "once_cell"}, {"id": "percent-encoding 2.2.0", "target": "percent_encoding"}, {"id": "pin-project-lite 0.2.9", "target": "pin_project_lite"}, {"id": "rustls 0.20.6", "target": "rustls"}, {"id": "rustls-pemfile 1.0.1", "target": "rustls_pemfile"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "tokio-native-tls 0.3.0", "target": "tokio_native_tls"}, {"id": "tokio-rustls 0.23.4", "target": "tokio_rustls"}, {"id": "webpki-roots 0.22.4", "target": "webpki_roots"}], "cfg(target_arch = \"wasm32\")": [{"id": "js-sys 0.3.60", "target": "js_sys"}, {"id": "wasm-bindgen 0.2.83", "target": "wasm_bindgen"}, {"id": "wasm-bindgen-futures 0.4.33", "target": "wasm_bindgen_futures"}, {"id": "web-sys 0.3.60", "target": "web_sys"}], "cfg(windows)": [{"id": "winreg 0.10.1", "target": "winreg"}]}}, "edition": "2018", "version": "0.11.12"}, "license": "MIT/Apache-2.0"}, "ring 0.16.20": {"name": "ring", "version": "0.16.20", "repository": {"Http": {"url": "https://static.crates.io/crates/ring/0.16.20/download", "sha256": "3053cf52e236a3ed746dfc745aa9cacf1b791d846bdaf412f60a8d7d6e17c8fc"}}, "targets": [{"Library": {"crate_name": "ring", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "ring", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "dev_urandom_fallback", "once_cell"], "selects": {}}, "deps": {"common": [{"id": "ring 0.16.20", "target": "build_script_build"}, {"id": "untrusted 0.7.1", "target": "untrusted"}], "selects": {"cfg(all(target_arch = \"wasm32\", target_vendor = \"unknown\", target_os = \"unknown\", target_env = \"\"))": [{"id": "web-sys 0.3.60", "target": "web_sys"}], "cfg(any(target_arch = \"x86\", target_arch = \"x86_64\", all(any(target_arch = \"aarch64\", target_arch = \"arm\"), any(target_os = \"android\", target_os = \"fuchsia\", target_os = \"linux\"))))": [{"id": "spin 0.5.2", "target": "spin"}], "cfg(any(target_os = \"android\", target_os = \"linux\"))": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "once_cell 1.14.0", "target": "once_cell"}], "cfg(any(target_os = \"dragonfly\", target_os = \"freebsd\", target_os = \"illumos\", target_os = \"netbsd\", target_os = \"openbsd\", target_os = \"solaris\"))": [{"id": "once_cell 1.14.0", "target": "once_cell"}], "cfg(target_os = \"windows\")": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.16.20"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "cc 1.0.73", "target": "cc"}], "selects": {}}, "links": "ring-asm"}, "license": null}, "rustix 0.35.9": {"name": "rustix", "version": "0.35.9", "repository": {"Http": {"url": "https://static.crates.io/crates/rustix/0.35.9/download", "sha256": "72c825b8aa8010eb9ee99b75f05e10180b9278d161583034d7574c9d617aeada"}}, "targets": [{"Library": {"crate_name": "rustix", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rustix", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "fs", "io-lifetimes", "libc", "std", "use-libc-auxv"], "selects": {}}, "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "io-lifetimes 0.7.3", "target": "io_lifetimes"}, {"id": "rustix 0.35.9", "target": "build_script_build"}], "selects": {"cfg(all(any(target_os = \"android\", target_os = \"linux\"), any(rustix_use_libc, miri, not(all(target_os = \"linux\", any(target_arch = \"x86\", all(target_arch = \"x86_64\", target_pointer_width = \"64\"), all(target_endian = \"little\", any(target_arch = \"arm\", all(target_arch = \"aarch64\", target_pointer_width = \"64\"), target_arch = \"powerpc64\", target_arch = \"riscv64\", target_arch = \"mips\", target_arch = \"mips64\"))))))))": [{"id": "linux-raw-sys 0.0.46", "target": "linux_raw_sys"}], "cfg(all(not(rustix_use_libc), not(miri), target_os = \"linux\", any(target_arch = \"x86\", all(target_arch = \"x86_64\", target_pointer_width = \"64\"), all(target_endian = \"little\", any(target_arch = \"arm\", all(target_arch = \"aarch64\", target_pointer_width = \"64\"), target_arch = \"powerpc64\", target_arch = \"riscv64\", target_arch = \"mips\", target_arch = \"mips64\")))))": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "linux-raw-sys 0.0.46", "target": "linux_raw_sys"}], "cfg(any(rustix_use_libc, miri, not(all(target_os = \"linux\", any(target_arch = \"x86\", all(target_arch = \"x86_64\", target_pointer_width = \"64\"), all(target_endian = \"little\", any(target_arch = \"arm\", all(target_arch = \"aarch64\", target_pointer_width = \"64\"), target_arch = \"powerpc64\", target_arch = \"riscv64\", target_arch = \"mips\", target_arch = \"mips64\")))))))": [{"id": "errno 0.2.8", "target": "errno", "alias": "libc_errno"}, {"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "windows-sys 0.36.1", "target": "windows_sys"}]}}, "edition": "2018", "version": "0.35.9"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"}, "rustls 0.20.6": {"name": "rustls", "version": "0.20.6", "repository": {"Http": {"url": "https://static.crates.io/crates/rustls/0.20.6/download", "sha256": "5aab8ee6c7097ed6057f43c187a62418d0c05a4bd5f18b3571db50ee0f9ce033"}}, "targets": [{"Library": {"crate_name": "rustls", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rustls", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["dangerous_configuration", "default", "log", "logging", "tls12"], "selects": {}}, "deps": {"common": [{"id": "log 0.4.17", "target": "log"}, {"id": "ring 0.16.20", "target": "ring"}, {"id": "rustls 0.20.6", "target": "build_script_build"}, {"id": "sct 0.7.0", "target": "sct"}, {"id": "webpki 0.22.0", "target": "<PERSON><PERSON><PERSON>"}], "selects": {}}, "edition": "2018", "version": "0.20.6"}, "build_script_attrs": {"data_glob": ["**"], "link_deps": {"common": [{"id": "ring 0.16.20", "target": "ring"}], "selects": {}}}, "license": "Apache-2.0/ISC/MIT"}, "rustls-pemfile 1.0.1": {"name": "rustls-pemfile", "version": "1.0.1", "repository": {"Http": {"url": "https://static.crates.io/crates/rustls-pemfile/1.0.1/download", "sha256": "0864aeff53f8c05aa08d86e5ef839d3dfcf07aeba2db32f12db0ef716e87bd55"}}, "targets": [{"Library": {"crate_name": "rustls_pemfile", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rustls_pemfile", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "base64 0.13.0", "target": "base64"}], "selects": {}}, "edition": "2018", "version": "1.0.1"}, "license": "Apache-2.0 OR ISC OR MIT"}, "rustversion 1.0.9": {"name": "rustversion", "version": "1.0.9", "repository": {"Http": {"url": "https://static.crates.io/crates/rustversion/1.0.9/download", "sha256": "97477e48b4cf8603ad5f7aaf897467cf42ab4218a38ef76fb14c2d6773a6d6a8"}}, "targets": [{"ProcMacro": {"crate_name": "rustversion", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build/build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rustversion", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "rustversion 1.0.9", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "1.0.9"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "rustyline 10.0.0": {"name": "rustyline", "version": "10.0.0", "repository": {"Http": {"url": "https://static.crates.io/crates/rustyline/10.0.0/download", "sha256": "1d1cd5ae51d3f7bf65d7969d579d502168ef578f289452bd8ccc91de28fda20e"}}, "targets": [{"Library": {"crate_name": "rustyline", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "rustyline", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["custom-bindings", "default", "dirs-next", "radix_trie", "with-dirs"], "selects": {}}, "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "dirs-next 2.0.0", "target": "dirs_next"}, {"id": "fd-lock 3.0.6", "target": "fd_lock"}, {"id": "libc 0.2.133", "target": "libc"}, {"id": "log 0.4.17", "target": "log"}, {"id": "memchr 2.5.0", "target": "memchr"}, {"id": "radix_trie 0.2.1", "target": "radix_trie"}, {"id": "unicode-segmentation 1.10.0", "target": "unicode_segmentation"}, {"id": "unicode-width 0.1.10", "target": "unicode_width"}], "selects": {"cfg(unix)": [{"id": "nix 0.24.2", "target": "nix"}, {"id": "utf8parse 0.2.0", "target": "utf8parse"}], "cfg(windows)": [{"id": "clipboard-win 4.4.2", "target": "clipboard_win"}, {"id": "scopeguard 1.1.0", "target": "scopeguard"}, {"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "10.0.0"}, "license": "MIT"}, "ryu 1.0.11": {"name": "ryu", "version": "1.0.11", "repository": {"Http": {"url": "https://static.crates.io/crates/ryu/1.0.11/download", "sha256": "4501abdff3ae82a1c1b477a17252eb69cee9e66eb915c1abaa4f44d873df9f09"}}, "targets": [{"Library": {"crate_name": "ryu", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "ryu", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.0.11"}, "license": "Apache-2.0 OR BSL-1.0"}, "schannel 0.1.20": {"name": "schannel", "version": "0.1.20", "repository": {"Http": {"url": "https://static.crates.io/crates/schannel/0.1.20/download", "sha256": "88d6731146462ea25d9244b2ed5fd1d716d25c52e4d54aa4fb0f3c4e9854dbe2"}}, "targets": [{"Library": {"crate_name": "schannel", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "schannel", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "lazy_static 1.4.0", "target": "lazy_static"}, {"id": "windows-sys 0.36.1", "target": "windows_sys"}], "selects": {}}, "edition": "2018", "version": "0.1.20"}, "license": "MIT"}, "scopeguard 1.1.0": {"name": "scopeguard", "version": "1.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/scopeguard/1.1.0/download", "sha256": "d29ab0c6d3fc0ee92fe66e2d99f700eab17a8d57d1c1d3b748380fb20baa78cd"}}, "targets": [{"Library": {"crate_name": "scopeguard", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "scopeguard", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": [], "selects": {"aarch64-pc-windows-msvc": ["default", "use_std"], "i686-pc-windows-msvc": ["default", "use_std"], "x86_64-pc-windows-msvc": ["default", "use_std"]}}, "edition": "2015", "version": "1.1.0"}, "license": "MIT/Apache-2.0"}, "sct 0.7.0": {"name": "sct", "version": "0.7.0", "repository": {"Http": {"url": "https://static.crates.io/crates/sct/0.7.0/download", "sha256": "d53dcdb7c9f8158937a7981b48accfd39a43af418591a5d008c7b22b5e1b7ca4"}}, "targets": [{"Library": {"crate_name": "sct", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "sct", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "ring 0.16.20", "target": "ring"}, {"id": "untrusted 0.7.1", "target": "untrusted"}], "selects": {}}, "edition": "2018", "version": "0.7.0"}, "license": "Apache-2.0/ISC/MIT"}, "security-framework 2.7.0": {"name": "security-framework", "version": "2.7.0", "repository": {"Http": {"url": "https://static.crates.io/crates/security-framework/2.7.0/download", "sha256": "2bc1bb97804af6631813c55739f771071e0f2ed33ee20b68c86ec505d906356c"}}, "targets": [{"Library": {"crate_name": "security_framework", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "security_framework", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["OSX_10_9", "default"], "selects": {}}, "deps": {"common": [{"id": "bitflags 1.3.2", "target": "bitflags"}, {"id": "core-foundation 0.9.3", "target": "core_foundation"}, {"id": "core-foundation-sys 0.8.3", "target": "core_foundation_sys"}, {"id": "libc 0.2.133", "target": "libc"}, {"id": "security-framework-sys 2.6.1", "target": "security_framework_sys"}], "selects": {}}, "edition": "2021", "version": "2.7.0"}, "license": "MIT OR Apache-2.0"}, "security-framework-sys 2.6.1": {"name": "security-framework-sys", "version": "2.6.1", "repository": {"Http": {"url": "https://static.crates.io/crates/security-framework-sys/2.6.1/download", "sha256": "0160a13a177a45bfb43ce71c01580998474f556ad854dcbca936dd2841a5c556"}}, "targets": [{"Library": {"crate_name": "security_framework_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "security_framework_sys", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["OSX_10_9", "default"], "selects": {}}, "deps": {"common": [{"id": "core-foundation-sys 0.8.3", "target": "core_foundation_sys"}, {"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2018", "version": "2.6.1"}, "license": "MIT OR Apache-2.0"}, "serde 1.0.144": {"name": "serde", "version": "1.0.144", "repository": {"Http": {"url": "https://static.crates.io/crates/serde/1.0.144/download", "sha256": "0f747710de3dcd43b88c9168773254e809d8ddbdf9653b84e2554ab219f17860"}}, "targets": [{"Library": {"crate_name": "serde", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "derive", "serde_derive", "std"], "selects": {}}, "deps": {"common": [{"id": "serde 1.0.144", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "proc_macro_deps": {"common": [{"id": "serde_derive 1.0.144", "target": "serde_derive"}], "selects": {}}, "version": "1.0.144"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "serde-value 0.7.0": {"name": "serde-value", "version": "0.7.0", "repository": {"Http": {"url": "https://static.crates.io/crates/serde-value/0.7.0/download", "sha256": "f3a1a3341211875ef120e117ea7fd5228530ae7e7036a779fdc9117be6b3282c"}}, "targets": [{"Library": {"crate_name": "serde_value", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde_value", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "ordered-float 2.10.0", "target": "ordered_float"}, {"id": "serde 1.0.144", "target": "serde"}], "selects": {}}, "edition": "2018", "version": "0.7.0"}, "license": "MIT"}, "serde_derive 1.0.144": {"name": "serde_derive", "version": "1.0.144", "repository": {"Http": {"url": "https://static.crates.io/crates/serde_derive/1.0.144/download", "sha256": "94ed3a816fb1d101812f83e789f888322c34e291f894f19590dc310963e87a00"}}, "targets": [{"ProcMacro": {"crate_name": "serde_derive", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde_derive", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "serde_derive 1.0.144", "target": "build_script_build"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2015", "version": "1.0.144"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "serde_json 1.0.85": {"name": "serde_json", "version": "1.0.85", "repository": {"Http": {"url": "https://static.crates.io/crates/serde_json/1.0.85/download", "sha256": "e55a28e3aaef9d5ce0506d0a14dbba8054ddc7e499ef522dd8b26859ec9d4a44"}}, "targets": [{"Library": {"crate_name": "serde_json", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde_json", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "std"], "selects": {}}, "deps": {"common": [{"id": "itoa 1.0.3", "target": "itoa"}, {"id": "ryu 1.0.11", "target": "ryu"}, {"id": "serde 1.0.144", "target": "serde"}, {"id": "serde_json 1.0.85", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "1.0.85"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "serde_urlencoded 0.7.1": {"name": "serde_urlencoded", "version": "0.7.1", "repository": {"Http": {"url": "https://static.crates.io/crates/serde_urlencoded/0.7.1/download", "sha256": "d3491c14715ca2294c4d6a88f15e84739788c1d030eed8c110436aafdaa2f3fd"}}, "targets": [{"Library": {"crate_name": "serde_urlencoded", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde_urlencoded", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "form_urlencoded 1.1.0", "target": "form_urlencoded"}, {"id": "itoa 1.0.3", "target": "itoa"}, {"id": "ryu 1.0.11", "target": "ryu"}, {"id": "serde 1.0.144", "target": "serde"}], "selects": {}}, "edition": "2018", "version": "0.7.1"}, "license": "MIT/Apache-2.0"}, "serde_with 2.0.1": {"name": "serde_with", "version": "2.0.1", "repository": {"Http": {"url": "https://static.crates.io/crates/serde_with/2.0.1/download", "sha256": "368f2d60d049ea019a84dcd6687b0d1e0030fe663ae105039bdf967ed5e6a9a7"}}, "targets": [{"Library": {"crate_name": "serde_with", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde_with", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "macros", "std"], "selects": {}}, "deps": {"common": [{"id": "serde 1.0.144", "target": "serde"}], "selects": {}}, "edition": "2021", "proc_macro_deps": {"common": [{"id": "serde_with_macros 2.0.1", "target": "serde_with_macros"}], "selects": {}}, "version": "2.0.1"}, "license": "MIT OR Apache-2.0"}, "serde_with_macros 2.0.1": {"name": "serde_with_macros", "version": "2.0.1", "repository": {"Http": {"url": "https://static.crates.io/crates/serde_with_macros/2.0.1/download", "sha256": "1ccadfacf6cf10faad22bbadf55986bdd0856edfb5d9210aa1dcf1f516e84e93"}}, "targets": [{"ProcMacro": {"crate_name": "serde_with_macros", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde_with_macros", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "darling 0.14.1", "target": "darling"}, {"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2021", "version": "2.0.1"}, "license": "MIT OR Apache-2.0"}, "serde_yaml 0.9.13": {"name": "serde_yaml", "version": "0.9.13", "repository": {"Http": {"url": "https://static.crates.io/crates/serde_yaml/0.9.13/download", "sha256": "8613d593412a0deb7bbd8de9d908efff5a0cb9ccd8f62c641e7b2ed2f57291d1"}}, "targets": [{"Library": {"crate_name": "serde_yaml", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "serde_yaml", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "indexmap 1.9.1", "target": "indexmap"}, {"id": "itoa 1.0.3", "target": "itoa"}, {"id": "ryu 1.0.11", "target": "ryu"}, {"id": "serde 1.0.144", "target": "serde"}, {"id": "unsafe-libyaml 0.2.4", "target": "unsafe_libyaml"}], "selects": {}}, "edition": "2021", "version": "0.9.13"}, "license": "MIT OR Apache-2.0"}, "sha1 0.10.5": {"name": "sha1", "version": "0.10.5", "repository": {"Http": {"url": "https://static.crates.io/crates/sha1/0.10.5/download", "sha256": "f04293dc80c3993519f2d7f6f511707ee7094fe0c6d3406feb330cdb3540eba3"}}, "targets": [{"Library": {"crate_name": "sha1", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "sha1", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "digest 0.10.5", "target": "digest"}], "selects": {"cfg(any(target_arch = \"aarch64\", target_arch = \"x86\", target_arch = \"x86_64\"))": [{"id": "cpufeatures 0.2.5", "target": "cpufeatures"}]}}, "edition": "2018", "version": "0.10.5"}, "license": "MIT OR Apache-2.0"}, "shared_child 0.3.5": {"name": "shared_child", "version": "0.3.5", "repository": {"Http": {"url": "https://static.crates.io/crates/shared_child/0.3.5/download", "sha256": "6be9f7d5565b1483af3e72975e2dee33879b3b86bd48c0929fccf6585d79e65a"}}, "targets": [{"Library": {"crate_name": "shared_child", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "shared_child", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(not(windows))": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.3.5"}, "license": "MIT"}, "signal-hook 0.3.14": {"name": "signal-hook", "version": "0.3.14", "repository": {"Http": {"url": "https://static.crates.io/crates/signal-hook/0.3.14/download", "sha256": "a253b5e89e2698464fc26b545c9edceb338e18a89effeeecfea192c3025be29d"}}, "targets": [{"Library": {"crate_name": "signal_hook", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "signal_hook", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["channel", "default", "iterator"], "selects": {}}, "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "signal-hook 0.3.14", "target": "build_script_build"}, {"id": "signal-hook-registry 1.4.0", "target": "signal_hook_registry"}], "selects": {}}, "edition": "2018", "version": "0.3.14"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "Apache-2.0/MIT"}, "signal-hook-mio 0.2.3": {"name": "signal-hook-mio", "version": "0.2.3", "repository": {"Http": {"url": "https://static.crates.io/crates/signal-hook-mio/0.2.3/download", "sha256": "29ad2e15f37ec9a6cc544097b78a1ec90001e9f71b81338ca39f430adaca99af"}}, "targets": [{"Library": {"crate_name": "signal_hook_mio", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "signal_hook_mio", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["mio-0_8", "support-v0_8"], "selects": {}}, "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "mio 0.8.4", "target": "mio", "alias": "mio_0_8"}, {"id": "signal-hook 0.3.14", "target": "signal_hook"}], "selects": {}}, "edition": "2018", "version": "0.2.3"}, "license": "Apache-2.0/MIT"}, "signal-hook-registry 1.4.0": {"name": "signal-hook-registry", "version": "1.4.0", "repository": {"Http": {"url": "https://static.crates.io/crates/signal-hook-registry/1.4.0/download", "sha256": "e51e73328dc4ac0c7ccbda3a494dfa03df1de2f46018127f60c693f2648455b0"}}, "targets": [{"Library": {"crate_name": "signal_hook_registry", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "signal_hook_registry", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}], "selects": {}}, "edition": "2015", "version": "1.4.0"}, "license": "Apache-2.0/MIT"}, "slab 0.4.7": {"name": "slab", "version": "0.4.7", "repository": {"Http": {"url": "https://static.crates.io/crates/slab/0.4.7/download", "sha256": "4614a76b2a8be0058caa9dbbaf66d988527d86d003c11a94fbd335d7661edcef"}}, "targets": [{"Library": {"crate_name": "slab", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "slab", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "deps": {"common": [{"id": "slab 0.4.7", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.4.7"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "autocfg 1.1.0", "target": "autocfg"}], "selects": {}}}, "license": "MIT"}, "smallvec 1.9.0": {"name": "smallvec", "version": "1.9.0", "repository": {"Http": {"url": "https://static.crates.io/crates/smallvec/1.9.0/download", "sha256": "2fd0db749597d91ff862fd1d55ea87f7855a744a8425a64695b6fca237d1dad1"}}, "targets": [{"Library": {"crate_name": "smallvec", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "smallvec", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.9.0"}, "license": "MIT OR Apache-2.0"}, "socket2 0.4.7": {"name": "socket2", "version": "0.4.7", "repository": {"Http": {"url": "https://static.crates.io/crates/socket2/0.4.7/download", "sha256": "02e2d2db9033d13a1567121ddd7a095ee144db4e1ca1b1bda3419bc0da294ebd"}}, "targets": [{"Library": {"crate_name": "socket2", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "socket2", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["all"], "selects": {}}, "deps": {"common": [], "selects": {"cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.4.7"}, "license": "MIT OR Apache-2.0"}, "spin 0.5.2": {"name": "spin", "version": "0.5.2", "repository": {"Http": {"url": "https://static.crates.io/crates/spin/0.5.2/download", "sha256": "6e63cff320ae2c57904679ba7cb63280a3dc4613885beafb148ee7bf9aa9042d"}}, "targets": [{"Library": {"crate_name": "spin", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "spin", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.5.2"}, "license": "MIT"}, "str-buf 1.0.6": {"name": "str-buf", "version": "1.0.6", "repository": {"Http": {"url": "https://static.crates.io/crates/str-buf/1.0.6/download", "sha256": "9e08d8363704e6c71fc928674353e6b7c23dcea9d82d7012c8faf2a3a025f8d0"}}, "targets": [{"Library": {"crate_name": "str_buf", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "str_buf", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.0.6"}, "license": "BSL-1.0"}, "strfmt 0.2.2": {"name": "strfmt", "version": "0.2.2", "repository": {"Http": {"url": "https://static.crates.io/crates/strfmt/0.2.2/download", "sha256": "26cdabcdab6da7e8c2ac1160e917ec83e78bbe3e10325e17d532718c67a4828f"}}, "targets": [{"Library": {"crate_name": "strfmt", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "strfmt", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.2.2"}, "license": "MIT"}, "strsim 0.10.0": {"name": "strsim", "version": "0.10.0", "repository": {"Http": {"url": "https://static.crates.io/crates/strsim/0.10.0/download", "sha256": "73473c0e59e6d5812c5dfe2a064a6444949f089e20eec9a2e5506596494e4623"}}, "targets": [{"Library": {"crate_name": "strsim", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "strsim", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.10.0"}, "license": "MIT"}, "strum 0.24.1": {"name": "strum", "version": "0.24.1", "repository": {"Http": {"url": "https://static.crates.io/crates/strum/0.24.1/download", "sha256": "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"}}, "targets": [{"Library": {"crate_name": "strum", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "strum", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "edition": "2018", "version": "0.24.1"}, "license": "MIT"}, "strum_macros 0.24.3": {"name": "strum_macros", "version": "0.24.3", "repository": {"Http": {"url": "https://static.crates.io/crates/strum_macros/0.24.3/download", "sha256": "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"}}, "targets": [{"ProcMacro": {"crate_name": "strum_macros", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "strum_macros", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "heck 0.4.0", "target": "heck"}, {"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2018", "proc_macro_deps": {"common": [{"id": "rustversion 1.0.9", "target": "rustversion"}], "selects": {}}, "version": "0.24.3"}, "license": "MIT"}, "subtle 2.4.1": {"name": "subtle", "version": "2.4.1", "repository": {"Http": {"url": "https://static.crates.io/crates/subtle/2.4.1/download", "sha256": "6bdef32e8150c2a081110b42772ffe7d7c9032b606bc226c8260fd97e0976601"}}, "targets": [{"Library": {"crate_name": "subtle", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "subtle", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "2.4.1"}, "license": "BSD-3-<PERSON><PERSON>"}, "syn 1.0.100": {"name": "syn", "version": "1.0.100", "repository": {"Http": {"url": "https://static.crates.io/crates/syn/1.0.100/download", "sha256": "52205623b1b0f064a4e71182c3b18ae902267282930c6d5462c91b859668426e"}}, "targets": [{"Library": {"crate_name": "syn", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "syn", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["clone-impls", "default", "derive", "extra-traits", "full", "parsing", "printing", "proc-macro", "quote", "visit"], "selects": {}}, "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "build_script_build"}, {"id": "unicode-ident 1.0.4", "target": "unicode_ident"}], "selects": {}}, "edition": "2018", "version": "1.0.100"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "tempdir 0.3.7": {"name": "tempdir", "version": "0.3.7", "repository": {"Http": {"url": "https://static.crates.io/crates/tempdir/0.3.7/download", "sha256": "15f2b5fb00ccdf689e0149d1b1b3c03fead81c2b37735d812fa8bddbbf41b6d8"}}, "targets": [{"Library": {"crate_name": "tempdir", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tempdir", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "rand 0.4.6", "target": "rand"}, {"id": "remove_dir_all 0.5.3", "target": "remove_dir_all"}], "selects": {}}, "edition": "2015", "version": "0.3.7"}, "license": "MIT/Apache-2.0"}, "tempfile 3.3.0": {"name": "tempfile", "version": "3.3.0", "repository": {"Http": {"url": "https://static.crates.io/crates/tempfile/3.3.0/download", "sha256": "5cdb1ef4eaeeaddc8fbd371e5017057064af0911902ef36b39801f67cc6d79e4"}}, "targets": [{"Library": {"crate_name": "tempfile", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tempfile", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "fastrand 1.8.0", "target": "fastrand"}, {"id": "remove_dir_all 0.5.3", "target": "remove_dir_all"}], "selects": {"cfg(any(unix, target_os = \"wasi\"))": [{"id": "libc 0.2.133", "target": "libc"}], "cfg(target_os = \"redox\")": [{"id": "redox_syscall 0.2.16", "target": "syscall"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "3.3.0"}, "license": "MIT OR Apache-2.0"}, "termcolor 1.1.3": {"name": "termcolor", "version": "1.1.3", "repository": {"Http": {"url": "https://static.crates.io/crates/termcolor/1.1.3/download", "sha256": "bab24d30b911b2376f3a13cc2cd443142f0c81dda04c118693e35b3835757755"}}, "targets": [{"Library": {"crate_name": "termcolor", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "termcolor", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(windows)": [{"id": "winapi-util 0.1.5", "target": "winapi_util"}]}}, "edition": "2018", "version": "1.1.3"}, "license": "Unlicense OR MIT"}, "textwrap 0.15.1": {"name": "textwrap", "version": "0.15.1", "repository": {"Http": {"url": "https://static.crates.io/crates/textwrap/0.15.1/download", "sha256": "949517c0cf1bf4ee812e2e07e08ab448e3ae0d23472aee8a06c985f0c8815b16"}}, "targets": [{"Library": {"crate_name": "textwrap", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "textwrap", "common_attrs": {"compile_data_glob": ["**"], "edition": "2021", "version": "0.15.1"}, "license": "MIT"}, "thiserror 1.0.35": {"name": "thiserror", "version": "1.0.35", "repository": {"Http": {"url": "https://static.crates.io/crates/thiserror/1.0.35/download", "sha256": "c53f98874615aea268107765aa1ed8f6116782501d18e53d08b471733bea6c85"}}, "targets": [{"Library": {"crate_name": "thiserror", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "thiserror", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "thiserror 1.0.35", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "proc_macro_deps": {"common": [{"id": "thiserror-impl 1.0.35", "target": "thiserror_impl"}], "selects": {}}, "version": "1.0.35"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "thiserror-impl 1.0.35": {"name": "thiserror-impl", "version": "1.0.35", "repository": {"Http": {"url": "https://static.crates.io/crates/thiserror-impl/1.0.35/download", "sha256": "f8b463991b4eab2d801e724172285ec4195c650e8ec79b149e6c2a8e6dd3f783"}}, "targets": [{"ProcMacro": {"crate_name": "thiserror_impl", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "thiserror_impl", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2018", "version": "1.0.35"}, "license": "MIT OR Apache-2.0"}, "time 0.1.44": {"name": "time", "version": "0.1.44", "repository": {"Http": {"url": "https://static.crates.io/crates/time/0.1.44/download", "sha256": "6db9e6914ab8b1ae1c260a4ae7a49b6c5611b40328a735b21862567685e73255"}}, "targets": [{"Library": {"crate_name": "time", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "time", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "libc 0.2.133", "target": "libc"}], "selects": {"cfg(target_os = \"wasi\")": [{"id": "wasi 0.10.0+wasi-snapshot-preview1", "target": "wasi"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2015", "version": "0.1.44"}, "license": "MIT/Apache-2.0"}, "time 0.3.14": {"name": "time", "version": "0.3.14", "repository": {"Http": {"url": "https://static.crates.io/crates/time/0.3.14/download", "sha256": "3c3f9a28b618c3a6b9251b6908e9c99e04b9e5c02e6581ccbb67d59c34ef7f9b"}}, "targets": [{"Library": {"crate_name": "time", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "time", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "itoa 1.0.3", "target": "itoa"}, {"id": "serde 1.0.144", "target": "serde"}], "selects": {"cfg(target_family = \"unix\")": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "num_threads 0.1.6", "target": "num_threads"}]}}, "edition": "2021", "version": "0.3.14"}, "license": "MIT OR Apache-2.0"}, "tinyvec 1.6.0": {"name": "tinyvec", "version": "1.6.0", "repository": {"Http": {"url": "https://static.crates.io/crates/tinyvec/1.6.0/download", "sha256": "87cc5ceb3875bb20c2890005a4e226a4651264a5c75edb2421b52861a0a0cb50"}}, "targets": [{"Library": {"crate_name": "tinyvec", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tinyvec", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "default", "tinyvec_macros"], "selects": {}}, "deps": {"common": [{"id": "tinyvec_macros 0.1.0", "target": "tinyvec_macros"}], "selects": {}}, "edition": "2018", "version": "1.6.0"}, "license": "Zlib OR Apache-2.0 OR MIT"}, "tinyvec_macros 0.1.0": {"name": "tinyvec_macros", "version": "0.1.0", "repository": {"Http": {"url": "https://static.crates.io/crates/tinyvec_macros/0.1.0/download", "sha256": "cda74da7e1a664f795bb1f8a87ec406fb89a02522cf6e50620d016add6dbbf5c"}}, "targets": [{"Library": {"crate_name": "tinyvec_macros", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tinyvec_macros", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "0.1.0"}, "license": "MIT OR Apache-2.0 OR Zlib"}, "tokio 1.21.1": {"name": "tokio", "version": "1.21.1", "repository": {"Http": {"url": "https://static.crates.io/crates/tokio/1.21.1/download", "sha256": "0020c875007ad96677dcc890298f4b942882c5d4eb7cc8f439fc3bf813dc9c95"}}, "targets": [{"Library": {"crate_name": "tokio", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tokio", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["bytes", "default", "fs", "full", "io-std", "io-util", "libc", "macros", "memchr", "mio", "net", "num_cpus", "once_cell", "parking_lot", "process", "rt", "rt-multi-thread", "signal", "signal-hook-registry", "socket2", "sync", "time", "tokio-macros"], "selects": {"aarch64-pc-windows-msvc": ["<PERSON>ap<PERSON>"], "i686-pc-windows-msvc": ["<PERSON>ap<PERSON>"], "x86_64-pc-windows-msvc": ["<PERSON>ap<PERSON>"]}}, "deps": {"common": [{"id": "bytes 1.2.1", "target": "bytes"}, {"id": "memchr 2.5.0", "target": "memchr"}, {"id": "mio 0.8.4", "target": "mio"}, {"id": "num_cpus 1.13.1", "target": "num_cpus"}, {"id": "once_cell 1.14.0", "target": "once_cell"}, {"id": "parking_lot 0.12.1", "target": "parking_lot"}, {"id": "pin-project-lite 0.2.9", "target": "pin_project_lite"}, {"id": "tokio 1.21.1", "target": "build_script_build"}], "selects": {"cfg(not(any(target_arch = \"wasm32\", target_arch = \"wasm64\")))": [{"id": "socket2 0.4.7", "target": "socket2"}], "cfg(unix)": [{"id": "libc 0.2.133", "target": "libc"}, {"id": "signal-hook-registry 1.4.0", "target": "signal_hook_registry"}], "cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "proc_macro_deps": {"common": [{"id": "tokio-macros 1.8.0", "target": "tokio_macros"}], "selects": {}}, "version": "1.21.1"}, "build_script_attrs": {"data_glob": ["**"], "deps": {"common": [{"id": "autocfg 1.1.0", "target": "autocfg"}], "selects": {}}}, "license": "MIT"}, "tokio-macros 1.8.0": {"name": "tokio-macros", "version": "1.8.0", "repository": {"Http": {"url": "https://static.crates.io/crates/tokio-macros/1.8.0/download", "sha256": "9724f9a975fb987ef7a3cd9be0350edcbe130698af5b8f7a631e23d42d052484"}}, "targets": [{"ProcMacro": {"crate_name": "tokio_macros", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tokio_macros", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}], "selects": {}}, "edition": "2018", "version": "1.8.0"}, "license": "MIT"}, "tokio-native-tls 0.3.0": {"name": "tokio-native-tls", "version": "0.3.0", "repository": {"Http": {"url": "https://static.crates.io/crates/tokio-native-tls/0.3.0/download", "sha256": "f7d995660bd2b7f8c1568414c1126076c13fbb725c40112dc0120b78eb9b717b"}}, "targets": [{"Library": {"crate_name": "tokio_native_tls", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tokio_native_tls", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "native-tls 0.2.10", "target": "native_tls"}, {"id": "tokio 1.21.1", "target": "tokio"}], "selects": {}}, "edition": "2018", "version": "0.3.0"}, "license": "MIT"}, "tokio-rustls 0.23.4": {"name": "tokio-rustls", "version": "0.23.4", "repository": {"Http": {"url": "https://static.crates.io/crates/tokio-rustls/0.23.4/download", "sha256": "c43ee83903113e03984cb9e5cebe6c04a5116269e900e3ddba8f068a62adda59"}}, "targets": [{"Library": {"crate_name": "tokio_rustls", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tokio_rustls", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "logging", "tls12"], "selects": {}}, "deps": {"common": [{"id": "rustls 0.20.6", "target": "rustls"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "webpki 0.22.0", "target": "<PERSON><PERSON><PERSON>"}], "selects": {}}, "edition": "2018", "version": "0.23.4"}, "license": "MIT/Apache-2.0"}, "tokio-util 0.7.4": {"name": "tokio-util", "version": "0.7.4", "repository": {"Http": {"url": "https://static.crates.io/crates/tokio-util/0.7.4/download", "sha256": "0bb2e075f03b3d66d8d8785356224ba688d2906a371015e225beeb65ca92c740"}}, "targets": [{"Library": {"crate_name": "tokio_util", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tokio_util", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["codec", "default", "tracing"], "selects": {}}, "deps": {"common": [{"id": "bytes 1.2.1", "target": "bytes"}, {"id": "futures-core 0.3.24", "target": "futures_core"}, {"id": "futures-sink 0.3.24", "target": "futures_sink"}, {"id": "pin-project-lite 0.2.9", "target": "pin_project_lite"}, {"id": "tokio 1.21.1", "target": "tokio"}, {"id": "tracing 0.1.36", "target": "tracing"}], "selects": {}}, "edition": "2018", "version": "0.7.4"}, "license": "MIT"}, "tower-service 0.3.2": {"name": "tower-service", "version": "0.3.2", "repository": {"Http": {"url": "https://static.crates.io/crates/tower-service/0.3.2/download", "sha256": "b6bc1c9ce2b5135ac7f93c72918fc37feb872bdc6a5533a8b85eb4b86bfdae52"}}, "targets": [{"Library": {"crate_name": "tower_service", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tower_service", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "0.3.2"}, "license": "MIT"}, "tracing 0.1.36": {"name": "tracing", "version": "0.1.36", "repository": {"Http": {"url": "https://static.crates.io/crates/tracing/0.1.36/download", "sha256": "2fce9567bd60a67d08a16488756721ba392f24f29006402881e43b19aac64307"}}, "targets": [{"Library": {"crate_name": "tracing", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tracing", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["std"], "selects": {}}, "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "pin-project-lite 0.2.9", "target": "pin_project_lite"}, {"id": "tracing-core 0.1.29", "target": "tracing_core"}], "selects": {}}, "edition": "2018", "version": "0.1.36"}, "license": "MIT"}, "tracing-core 0.1.29": {"name": "tracing-core", "version": "0.1.29", "repository": {"Http": {"url": "https://static.crates.io/crates/tracing-core/0.1.29/download", "sha256": "5aeea4303076558a00714b823f9ad67d58a3bbda1df83d8827d21193156e22f7"}}, "targets": [{"Library": {"crate_name": "tracing_core", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "tracing_core", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["once_cell", "std"], "selects": {}}, "deps": {"common": [{"id": "once_cell 1.14.0", "target": "once_cell"}], "selects": {}}, "edition": "2018", "version": "0.1.29"}, "license": "MIT"}, "try-lock 0.2.3": {"name": "try-lock", "version": "0.2.3", "repository": {"Http": {"url": "https://static.crates.io/crates/try-lock/0.2.3/download", "sha256": "59547bce71d9c38b83d9c0e92b6066c4253371f15005def0c30d9657f50c7642"}}, "targets": [{"Library": {"crate_name": "try_lock", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "try_lock", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.2.3"}, "license": "MIT"}, "typenum 1.15.0": {"name": "typenum", "version": "1.15.0", "repository": {"Http": {"url": "https://static.crates.io/crates/typenum/1.15.0/download", "sha256": "dcf81ac59edc17cc8697ff311e8f5ef2d99fcbd9817b34cec66f90b6c3dfd987"}}, "targets": [{"Library": {"crate_name": "typenum", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_main", "crate_root": "build/main.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "typenum", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "typenum 1.15.0", "target": "build_script_main"}], "selects": {}}, "edition": "2018", "version": "1.15.0"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "unicode-bidi 0.3.8": {"name": "unicode-bidi", "version": "0.3.8", "repository": {"Http": {"url": "https://static.crates.io/crates/unicode-bidi/0.3.8/download", "sha256": "099b7128301d285f79ddd55b9a83d5e6b9e97c92e0ea0daebee7263e932de992"}}, "targets": [{"Library": {"crate_name": "unicode_bidi", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "unicode_bidi", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "hardcoded-data", "std"], "selects": {}}, "edition": "2018", "version": "0.3.8"}, "license": "MIT OR Apache-2.0"}, "unicode-ident 1.0.4": {"name": "unicode-ident", "version": "1.0.4", "repository": {"Http": {"url": "https://static.crates.io/crates/unicode-ident/1.0.4/download", "sha256": "dcc811dc4066ac62f84f11307873c4850cb653bfa9b1719cee2bd2204a4bc5dd"}}, "targets": [{"Library": {"crate_name": "unicode_ident", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "unicode_ident", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.0.4"}, "license": "(MIT OR Apache-2.0) AND Unicode-DFS-2016"}, "unicode-normalization 0.1.22": {"name": "unicode-normalization", "version": "0.1.22", "repository": {"Http": {"url": "https://static.crates.io/crates/unicode-normalization/0.1.22/download", "sha256": "5c5713f0fc4b5db668a2ac63cdb7bb4469d8c9fed047b1d0292cc7b0ce2ba921"}}, "targets": [{"Library": {"crate_name": "unicode_normalization", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "unicode_normalization", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "deps": {"common": [{"id": "tinyvec 1.6.0", "target": "tinyvec"}], "selects": {}}, "edition": "2018", "version": "0.1.22"}, "license": "MIT/Apache-2.0"}, "unicode-segmentation 1.10.0": {"name": "unicode-segmentation", "version": "1.10.0", "repository": {"Http": {"url": "https://static.crates.io/crates/unicode-segmentation/1.10.0/download", "sha256": "0fdbf052a0783de01e944a6ce7a8cb939e295b1e7be835a1112c3b9a7f047a5a"}}, "targets": [{"Library": {"crate_name": "unicode_segmentation", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "unicode_segmentation", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "1.10.0"}, "license": "MIT/Apache-2.0"}, "unicode-width 0.1.10": {"name": "unicode-width", "version": "0.1.10", "repository": {"Http": {"url": "https://static.crates.io/crates/unicode-width/0.1.10/download", "sha256": "c0edd1e5b14653f783770bce4a4dabb4a5108a5370a5f5d8cfe8710c361f6c8b"}}, "targets": [{"Library": {"crate_name": "unicode_width", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "unicode_width", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "edition": "2015", "version": "0.1.10"}, "license": "MIT/Apache-2.0"}, "unsafe-libyaml 0.2.4": {"name": "unsafe-libyaml", "version": "0.2.4", "repository": {"Http": {"url": "https://static.crates.io/crates/unsafe-libyaml/0.2.4/download", "sha256": "c1e5fa573d8ac5f1a856f8d7be41d390ee973daf97c806b2c1a465e4e1406e68"}}, "targets": [{"Library": {"crate_name": "unsafe_libyaml", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "unsafe_libyaml", "common_attrs": {"compile_data_glob": ["**"], "edition": "2021", "version": "0.2.4"}, "license": "MIT"}, "untrusted 0.7.1": {"name": "untrusted", "version": "0.7.1", "repository": {"Http": {"url": "https://static.crates.io/crates/untrusted/0.7.1/download", "sha256": "a156c684c91ea7d62626509bce3cb4e1d9ed5c4d978f7b4352658f96a4c26b4a"}}, "targets": [{"Library": {"crate_name": "untrusted", "crate_root": "src/untrusted.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "untrusted", "common_attrs": {"compile_data_glob": ["**"], "edition": "2018", "version": "0.7.1"}, "license": "ISC"}, "url 2.3.1": {"name": "url", "version": "2.3.1", "repository": {"Http": {"url": "https://static.crates.io/crates/url/2.3.1/download", "sha256": "0d68c799ae75762b8c3fe375feb6600ef5602c883c5d21eb51c09f22b83c4643"}}, "targets": [{"Library": {"crate_name": "url", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "url", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "deps": {"common": [{"id": "form_urlencoded 1.1.0", "target": "form_urlencoded"}, {"id": "idna 0.3.0", "target": "idna"}, {"id": "percent-encoding 2.2.0", "target": "percent_encoding"}], "selects": {}}, "edition": "2018", "version": "2.3.1"}, "license": "MIT OR Apache-2.0"}, "utf8parse 0.2.0": {"name": "utf8parse", "version": "0.2.0", "repository": {"Http": {"url": "https://static.crates.io/crates/utf8parse/0.2.0/download", "sha256": "936e4b492acfd135421d8dca4b1aa80a7bfc26e702ef3af710e0752684df5372"}}, "targets": [{"Library": {"crate_name": "utf8parse", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "utf8parse", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default"], "selects": {}}, "edition": "2018", "version": "0.2.0"}, "license": "Apache-2.0 OR MIT"}, "vcpkg 0.2.15": {"name": "vcpkg", "version": "0.2.15", "repository": {"Http": {"url": "https://static.crates.io/crates/vcpkg/0.2.15/download", "sha256": "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"}}, "targets": [{"Library": {"crate_name": "vcpkg", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "vcpkg", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.2.15"}, "license": "MIT/Apache-2.0"}, "version_check 0.9.4": {"name": "version_check", "version": "0.9.4", "repository": {"Http": {"url": "https://static.crates.io/crates/version_check/0.9.4/download", "sha256": "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"}}, "targets": [{"Library": {"crate_name": "version_check", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "version_check", "common_attrs": {"compile_data_glob": ["**"], "edition": "2015", "version": "0.9.4"}, "license": "MIT/Apache-2.0"}, "want 0.3.0": {"name": "want", "version": "0.3.0", "repository": {"Http": {"url": "https://static.crates.io/crates/want/0.3.0/download", "sha256": "1ce8a968cb1cd110d136ff8b819a556d6fb6d919363c61534f6860c7eb172ba0"}}, "targets": [{"Library": {"crate_name": "want", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "want", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "log 0.4.17", "target": "log"}, {"id": "try-lock 0.2.3", "target": "try_lock"}], "selects": {}}, "edition": "2018", "version": "0.3.0"}, "license": "MIT"}, "wasi 0.10.0+wasi-snapshot-preview1": {"name": "wasi", "version": "0.10.0+wasi-snapshot-preview1", "repository": {"Http": {"url": "https://static.crates.io/crates/wasi/0.10.0+wasi-snapshot-preview1/download", "sha256": "1a143597ca7c7793eff794def352d41792a93c481eb1042423ff7ff72ba2c31f"}}, "targets": [{"Library": {"crate_name": "wasi", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasi", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "edition": "2018", "version": "0.10.0+wasi-snapshot-preview1"}, "license": "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"}, "wasi 0.11.0+wasi-snapshot-preview1": {"name": "wasi", "version": "0.11.0+wasi-snapshot-preview1", "repository": {"Http": {"url": "https://static.crates.io/crates/wasi/0.11.0+wasi-snapshot-preview1/download", "sha256": "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"}}, "targets": [{"Library": {"crate_name": "wasi", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasi", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "edition": "2018", "version": "0.11.0+wasi-snapshot-preview1"}, "license": "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"}, "wasm-bindgen 0.2.83": {"name": "wasm-bindgen", "version": "0.2.83", "repository": {"Http": {"url": "https://static.crates.io/crates/wasm-bindgen/0.2.83/download", "sha256": "eaf9f5aceeec8be17c128b2e93e031fb8a4d469bb9c4ae2d7dc1888b26887268"}}, "targets": [{"Library": {"crate_name": "wasm_bindgen", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasm_bindgen", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "spans", "std"], "selects": {}}, "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "wasm-bindgen 0.2.83", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "proc_macro_deps": {"common": [{"id": "wasm-bindgen-macro 0.2.83", "target": "wasm_bindgen_macro"}], "selects": {}}, "version": "0.2.83"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT/Apache-2.0"}, "wasm-bindgen-backend 0.2.83": {"name": "wasm-bindgen-backend", "version": "0.2.83", "repository": {"Http": {"url": "https://static.crates.io/crates/wasm-bindgen-backend/0.2.83/download", "sha256": "4c8ffb332579b0557b52d268b91feab8df3615f265d5270fec2a8c95b17c1142"}}, "targets": [{"Library": {"crate_name": "wasm_bindgen_backend", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasm_bindgen_backend", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["spans"], "selects": {}}, "deps": {"common": [{"id": "bumpalo 3.11.0", "target": "<PERSON><PERSON>"}, {"id": "log 0.4.17", "target": "log"}, {"id": "once_cell 1.14.0", "target": "once_cell"}, {"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}, {"id": "wasm-bindgen-shared 0.2.83", "target": "wasm_bindgen_shared"}], "selects": {}}, "edition": "2018", "version": "0.2.83"}, "license": "MIT/Apache-2.0"}, "wasm-bindgen-futures 0.4.33": {"name": "wasm-bindgen-futures", "version": "0.4.33", "repository": {"Http": {"url": "https://static.crates.io/crates/wasm-bindgen-futures/0.4.33/download", "sha256": "23639446165ca5a5de86ae1d8896b737ae80319560fbaa4c2887b7da6e7ebd7d"}}, "targets": [{"Library": {"crate_name": "wasm_bindgen_futures", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasm_bindgen_futures", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "cfg-if 1.0.0", "target": "cfg_if"}, {"id": "js-sys 0.3.60", "target": "js_sys"}, {"id": "wasm-bindgen 0.2.83", "target": "wasm_bindgen"}], "selects": {"cfg(target_feature = \"atomics\")": [{"id": "web-sys 0.3.60", "target": "web_sys"}]}}, "edition": "2018", "version": "0.4.33"}, "license": "MIT/Apache-2.0"}, "wasm-bindgen-macro 0.2.83": {"name": "wasm-bindgen-macro", "version": "0.2.83", "repository": {"Http": {"url": "https://static.crates.io/crates/wasm-bindgen-macro/0.2.83/download", "sha256": "052be0f94026e6cbc75cdefc9bae13fd6052cdcaf532fa6c45e7ae33a1e6c810"}}, "targets": [{"ProcMacro": {"crate_name": "wasm_bindgen_macro", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasm_bindgen_macro", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["spans"], "selects": {}}, "deps": {"common": [{"id": "quote 1.0.21", "target": "quote"}, {"id": "wasm-bindgen-macro-support 0.2.83", "target": "wasm_bindgen_macro_support"}], "selects": {}}, "edition": "2018", "version": "0.2.83"}, "license": "MIT/Apache-2.0"}, "wasm-bindgen-macro-support 0.2.83": {"name": "wasm-bindgen-macro-support", "version": "0.2.83", "repository": {"Http": {"url": "https://static.crates.io/crates/wasm-bindgen-macro-support/0.2.83/download", "sha256": "07bc0c051dc5f23e307b13285f9d75df86bfdf816c5721e573dec1f9b8aa193c"}}, "targets": [{"Library": {"crate_name": "wasm_bindgen_macro_support", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasm_bindgen_macro_support", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["spans"], "selects": {}}, "deps": {"common": [{"id": "proc-macro2 1.0.43", "target": "proc_macro2"}, {"id": "quote 1.0.21", "target": "quote"}, {"id": "syn 1.0.100", "target": "syn"}, {"id": "wasm-bindgen-backend 0.2.83", "target": "wasm_bindgen_backend"}, {"id": "wasm-bindgen-shared 0.2.83", "target": "wasm_bindgen_shared"}], "selects": {}}, "edition": "2018", "version": "0.2.83"}, "license": "MIT/Apache-2.0"}, "wasm-bindgen-shared 0.2.83": {"name": "wasm-bindgen-shared", "version": "0.2.83", "repository": {"Http": {"url": "https://static.crates.io/crates/wasm-bindgen-shared/0.2.83/download", "sha256": "1c38c045535d93ec4f0b4defec448e4291638ee608530863b1e2ba115d4fff7f"}}, "targets": [{"Library": {"crate_name": "wasm_bindgen_shared", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "wasm_bindgen_shared", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "wasm-bindgen-shared 0.2.83", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.2.83"}, "build_script_attrs": {"data_glob": ["**"], "links": "wasm_bindgen"}, "license": "MIT/Apache-2.0"}, "web-sys 0.3.60": {"name": "web-sys", "version": "0.3.60", "repository": {"Http": {"url": "https://static.crates.io/crates/web-sys/0.3.60/download", "sha256": "bcda906d8be16e728fd5adc5b729afad4e444e106ab28cd1c7256e54fa61510f"}}, "targets": [{"Library": {"crate_name": "web_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "web_sys", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["Blob", "BlobPropertyBag", "EventTarget", "File", "FormData", "Headers", "Request", "RequestCredentials", "RequestInit", "RequestMode", "Response", "ServiceWorkerGlobalScope", "Window", "WorkerGlobalScope"], "selects": {"wasm32-unknown-unknown": ["Crypto"]}}, "deps": {"common": [{"id": "js-sys 0.3.60", "target": "js_sys"}, {"id": "wasm-bindgen 0.2.83", "target": "wasm_bindgen"}], "selects": {}}, "edition": "2018", "version": "0.3.60"}, "license": "MIT/Apache-2.0"}, "webpki 0.22.0": {"name": "<PERSON><PERSON><PERSON>", "version": "0.22.0", "repository": {"Http": {"url": "https://static.crates.io/crates/webpki/0.22.0/download", "sha256": "f095d78192e208183081cc07bc5515ef55216397af48b873e5edcd72637fa1bd"}}, "targets": [{"Library": {"crate_name": "<PERSON><PERSON><PERSON>", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "<PERSON><PERSON><PERSON>", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["alloc", "std"], "selects": {}}, "deps": {"common": [{"id": "ring 0.16.20", "target": "ring"}, {"id": "untrusted 0.7.1", "target": "untrusted"}], "selects": {}}, "edition": "2018", "version": "0.22.0"}, "license": null}, "webpki-roots 0.22.4": {"name": "webpki-roots", "version": "0.22.4", "repository": {"Http": {"url": "https://static.crates.io/crates/webpki-roots/0.22.4/download", "sha256": "f1c760f0d366a6c24a02ed7816e23e691f5d92291f94d15e836006fd11b04daf"}}, "targets": [{"Library": {"crate_name": "webpki_roots", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "webpki_roots", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "webpki 0.22.0", "target": "<PERSON><PERSON><PERSON>"}], "selects": {}}, "edition": "2018", "version": "0.22.4"}, "license": "MPL-2.0"}, "winapi 0.3.9": {"name": "<PERSON>ap<PERSON>", "version": "0.3.9", "repository": {"Http": {"url": "https://static.crates.io/crates/winapi/0.3.9/download", "sha256": "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"}}, "targets": [{"Library": {"crate_name": "<PERSON>ap<PERSON>", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "<PERSON>ap<PERSON>", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["accctrl", "aclapi", "activation", "basetsd", "<PERSON><PERSON><PERSON><PERSON>", "consoleapi", "err<PERSON><PERSON><PERSON><PERSON>", "fileapi", "<PERSON>ap<PERSON>", "impl-debug", "impl-default", "knownfolders", "minwinbase", "minwindef", "<PERSON><PERSON><PERSON><PERSON>", "ntdef", "ntsecapi", "objbase", "processenv", "processthreadsapi", "profileapi", "roapi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "std", "<PERSON><PERSON><PERSON>t", "synchapi", "sysin<PERSON><PERSON><PERSON>", "threadpoollegacyapiset", "timezoneapi", "winbase", "wincon", "winerror", "winnt", "winreg", "winstring", "winuser", "ws2ipdef", "ws2tcpip", "wtypesbase"], "selects": {}}, "deps": {"common": [{"id": "winapi 0.3.9", "target": "build_script_build"}], "selects": {"i686-pc-windows-gnu": [{"id": "winapi-i686-pc-windows-gnu 0.4.0", "target": "winapi_i686_pc_windows_gnu"}], "x86_64-pc-windows-gnu": [{"id": "winapi-x86_64-pc-windows-gnu 0.4.0", "target": "winapi_x86_64_pc_windows_gnu"}]}}, "edition": "2015", "version": "0.3.9"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT/Apache-2.0"}, "winapi-i686-pc-windows-gnu 0.4.0": {"name": "winapi-i686-pc-windows-gnu", "version": "0.4.0", "repository": {"Http": {"url": "https://static.crates.io/crates/winapi-i686-pc-windows-gnu/0.4.0/download", "sha256": "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"}}, "targets": [{"Library": {"crate_name": "winapi_i686_pc_windows_gnu", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "winapi_i686_pc_windows_gnu", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "winapi-i686-pc-windows-gnu 0.4.0", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "version": "0.4.0"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT/Apache-2.0"}, "winapi-util 0.1.5": {"name": "winapi-util", "version": "0.1.5", "repository": {"Http": {"url": "https://static.crates.io/crates/winapi-util/0.1.5/download", "sha256": "70ec6ce85bb158151cae5e5c87f95a8e97d2c0c4b001223f33a334e3ce5de178"}}, "targets": [{"Library": {"crate_name": "winapi_util", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "winapi_util", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [], "selects": {"cfg(windows)": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}]}}, "edition": "2018", "version": "0.1.5"}, "license": "Unlicense/MIT"}, "winapi-x86_64-pc-windows-gnu 0.4.0": {"name": "winapi-x86_64-pc-windows-gnu", "version": "0.4.0", "repository": {"Http": {"url": "https://static.crates.io/crates/winapi-x86_64-pc-windows-gnu/0.4.0/download", "sha256": "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"}}, "targets": [{"Library": {"crate_name": "winapi_x86_64_pc_windows_gnu", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "winapi_x86_64_pc_windows_gnu", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "winapi-x86_64-pc-windows-gnu 0.4.0", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "version": "0.4.0"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT/Apache-2.0"}, "windows-sys 0.36.1": {"name": "windows-sys", "version": "0.36.1", "repository": {"Http": {"url": "https://static.crates.io/crates/windows-sys/0.36.1/download", "sha256": "ea04155a16a59f9eab786fe12a4a450e75cdb175f9e0d80da1e17db09f55b8d2"}}, "targets": [{"Library": {"crate_name": "windows_sys", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "windows_sys", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["Win32", "Win32_Foundation", "Win32_Networking", "Win32_Networking_WinSock", "Win32_Security", "Win32_Security_Authentication", "Win32_Security_Authentication_Identity", "Win32_Security_Credentials", "Win32_Security_Cryptography", "Win32_Storage", "Win32_Storage_FileSystem", "Win32_System", "Win32_System_IO", "Win32_System_LibraryLoader", "Win32_System_Memory", "Win32_System_Pipes", "Win32_System_SystemServices", "Win32_System_WindowsProgramming", "default"], "selects": {}}, "deps": {"common": [], "selects": {"aarch64-pc-windows-msvc": [{"id": "windows_aarch64_msvc 0.36.1", "target": "windows_aarch64_msvc"}], "aarch64-uwp-windows-msvc": [{"id": "windows_aarch64_msvc 0.36.1", "target": "windows_aarch64_msvc"}], "i686-pc-windows-gnu": [{"id": "windows_i686_gnu 0.36.1", "target": "windows_i686_gnu"}], "i686-pc-windows-msvc": [{"id": "windows_i686_msvc 0.36.1", "target": "windows_i686_msvc"}], "i686-uwp-windows-gnu": [{"id": "windows_i686_gnu 0.36.1", "target": "windows_i686_gnu"}], "i686-uwp-windows-msvc": [{"id": "windows_i686_msvc 0.36.1", "target": "windows_i686_msvc"}], "x86_64-pc-windows-gnu": [{"id": "windows_x86_64_gnu 0.36.1", "target": "windows_x86_64_gnu"}], "x86_64-pc-windows-msvc": [{"id": "windows_x86_64_msvc 0.36.1", "target": "windows_x86_64_msvc"}], "x86_64-uwp-windows-gnu": [{"id": "windows_x86_64_gnu 0.36.1", "target": "windows_x86_64_gnu"}], "x86_64-uwp-windows-msvc": [{"id": "windows_x86_64_msvc 0.36.1", "target": "windows_x86_64_msvc"}]}}, "edition": "2018", "version": "0.36.1"}, "license": "MIT OR Apache-2.0"}, "windows_aarch64_msvc 0.36.1": {"name": "windows_aarch64_msvc", "version": "0.36.1", "repository": {"Http": {"url": "https://static.crates.io/crates/windows_aarch64_msvc/0.36.1/download", "sha256": "9bb8c3fd39ade2d67e9874ac4f3db21f0d710bee00fe7cab16949ec184eeaa47"}}, "targets": [{"Library": {"crate_name": "windows_aarch64_msvc", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "windows_aarch64_msvc", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "windows_aarch64_msvc 0.36.1", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.36.1"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "windows_i686_gnu 0.36.1": {"name": "windows_i686_gnu", "version": "0.36.1", "repository": {"Http": {"url": "https://static.crates.io/crates/windows_i686_gnu/0.36.1/download", "sha256": "180e6ccf01daf4c426b846dfc66db1fc518f074baa793aa7d9b9aaeffad6a3b6"}}, "targets": [{"Library": {"crate_name": "windows_i686_gnu", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "windows_i686_gnu", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "windows_i686_gnu 0.36.1", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.36.1"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "windows_i686_msvc 0.36.1": {"name": "windows_i686_msvc", "version": "0.36.1", "repository": {"Http": {"url": "https://static.crates.io/crates/windows_i686_msvc/0.36.1/download", "sha256": "e2e7917148b2812d1eeafaeb22a97e4813dfa60a3f8f78ebe204bcc88f12f024"}}, "targets": [{"Library": {"crate_name": "windows_i686_msvc", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "windows_i686_msvc", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "windows_i686_msvc 0.36.1", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.36.1"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "windows_x86_64_gnu 0.36.1": {"name": "windows_x86_64_gnu", "version": "0.36.1", "repository": {"Http": {"url": "https://static.crates.io/crates/windows_x86_64_gnu/0.36.1/download", "sha256": "4dcd171b8776c41b97521e5da127a2d86ad280114807d0b2ab1e462bc764d9e1"}}, "targets": [{"Library": {"crate_name": "windows_x86_64_gnu", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "windows_x86_64_gnu", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "windows_x86_64_gnu 0.36.1", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.36.1"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "windows_x86_64_msvc 0.36.1": {"name": "windows_x86_64_msvc", "version": "0.36.1", "repository": {"Http": {"url": "https://static.crates.io/crates/windows_x86_64_msvc/0.36.1/download", "sha256": "c811ca4a8c853ef420abd8592ba53ddbbac90410fab6903b3e79972a631f7680"}}, "targets": [{"Library": {"crate_name": "windows_x86_64_msvc", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "windows_x86_64_msvc", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "windows_x86_64_msvc 0.36.1", "target": "build_script_build"}], "selects": {}}, "edition": "2018", "version": "0.36.1"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT OR Apache-2.0"}, "winreg 0.10.1": {"name": "winreg", "version": "0.10.1", "repository": {"Http": {"url": "https://static.crates.io/crates/winreg/0.10.1/download", "sha256": "80d0f4e272c85def139476380b12f9ac60926689dd2e01d4923222f40580869d"}}, "targets": [{"Library": {"crate_name": "winreg", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}, {"BuildScript": {"crate_name": "build_script_build", "crate_root": "build.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "winreg", "common_attrs": {"compile_data_glob": ["**"], "deps": {"common": [{"id": "winapi 0.3.9", "target": "<PERSON>ap<PERSON>"}, {"id": "winreg 0.10.1", "target": "build_script_build"}], "selects": {}}, "edition": "2015", "version": "0.10.1"}, "build_script_attrs": {"data_glob": ["**"]}, "license": "MIT"}, "yasna 0.5.0": {"name": "yasna", "version": "0.5.0", "repository": {"Http": {"url": "https://static.crates.io/crates/yasna/0.5.0/download", "sha256": "346d34a236c9d3e5f3b9b74563f238f955bbd05fa0b8b4efa53c130c43982f4c"}}, "targets": [{"Library": {"crate_name": "yasna", "crate_root": "src/lib.rs", "srcs": ["**/*.rs"]}}], "library_target_name": "yasna", "common_attrs": {"compile_data_glob": ["**"], "crate_features": {"common": ["default", "std"], "selects": {}}, "edition": "2018", "version": "0.5.0"}, "license": "MIT OR Apache-2.0"}}, "binary_crates": [], "workspace_members": {"click 0.6.2": ""}, "conditions": {"aarch64-apple-darwin": ["aarch64-apple-darwin"], "aarch64-apple-ios": ["aarch64-apple-ios"], "aarch64-apple-ios-sim": ["aarch64-apple-ios-sim"], "aarch64-fuchsia": ["aarch64-fuchsia"], "aarch64-linux-android": ["aarch64-linux-android"], "aarch64-pc-windows-msvc": ["aarch64-pc-windows-msvc"], "aarch64-unknown-linux-gnu": ["aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu"], "aarch64-unknown-nto-qnx710": ["aarch64-unknown-nto-qnx710"], "aarch64-uwp-windows-msvc": [], "arm-unknown-linux-gnueabi": ["arm-unknown-linux-gnueabi"], "armv7-linux-androideabi": ["armv7-linux-androideabi"], "armv7-unknown-linux-gnueabi": ["armv7-unknown-linux-gnueabi"], "cfg(all(any(target_arch = \"x86_64\", target_arch = \"aarch64\"), target_os = \"hermit\"))": [], "cfg(all(any(target_os = \"android\", target_os = \"linux\"), any(rustix_use_libc, miri, not(all(target_os = \"linux\", any(target_arch = \"x86\", all(target_arch = \"x86_64\", target_pointer_width = \"64\"), all(target_endian = \"little\", any(target_arch = \"arm\", all(target_arch = \"aarch64\", target_pointer_width = \"64\"), target_arch = \"powerpc64\", target_arch = \"riscv64\", target_arch = \"mips\", target_arch = \"mips64\"))))))))": ["aarch64-linux-android", "armv7-linux-androideabi", "i686-linux-android", "powerpc-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-linux-android"], "cfg(all(not(rustix_use_libc), not(miri), target_os = \"linux\", any(target_arch = \"x86\", all(target_arch = \"x86_64\", target_pointer_width = \"64\"), all(target_endian = \"little\", any(target_arch = \"arm\", all(target_arch = \"aarch64\", target_pointer_width = \"64\"), target_arch = \"powerpc64\", target_arch = \"riscv64\", target_arch = \"mips\", target_arch = \"mips64\")))))": ["aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "arm-unknown-linux-gnueabi", "armv7-unknown-linux-gnueabi", "i686-unknown-linux-gnu", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu"], "cfg(all(target_arch = \"aarch64\", target_os = \"linux\"))": ["aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu"], "cfg(all(target_arch = \"wasm32\", not(any(target_os = \"emscripten\", target_os = \"wasi\"))))": ["wasm32-unknown-unknown"], "cfg(all(target_arch = \"wasm32\", target_vendor = \"unknown\", target_os = \"unknown\", target_env = \"\"))": ["wasm32-unknown-unknown"], "cfg(any(rustix_use_libc, miri, not(all(target_os = \"linux\", any(target_arch = \"x86\", all(target_arch = \"x86_64\", target_pointer_width = \"64\"), all(target_endian = \"little\", any(target_arch = \"arm\", all(target_arch = \"aarch64\", target_pointer_width = \"64\"), target_arch = \"powerpc64\", target_arch = \"riscv64\", target_arch = \"mips\", target_arch = \"mips64\")))))))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-pc-windows-msvc", "aarch64-unknown-nto-qnx710", "armv7-linux-androideabi", "i686-apple-darwin", "i686-linux-android", "i686-pc-windows-msvc", "i686-unknown-freebsd", "powerpc-unknown-linux-gnu", "riscv32imc-unknown-none-elf", "riscv64gc-unknown-none-elf", "s390x-unknown-linux-gnu", "thumbv7em-none-eabi", "thumbv8m.main-none-eabi", "wasm32-unknown-unknown", "wasm32-wasi", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-pc-windows-msvc", "x86_64-unknown-freebsd", "x86_64-unknown-none"], "cfg(any(target_arch = \"aarch64\", target_arch = \"x86\", target_arch = \"x86_64\"))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-pc-windows-msvc", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "i686-apple-darwin", "i686-linux-android", "i686-pc-windows-msvc", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-pc-windows-msvc", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu", "x86_64-unknown-none"], "cfg(any(target_arch = \"x86\", target_arch = \"x86_64\", all(any(target_arch = \"aarch64\", target_arch = \"arm\"), any(target_os = \"android\", target_os = \"fuchsia\", target_os = \"linux\"))))": ["aarch64-fuchsia", "aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-apple-darwin", "i686-linux-android", "i686-pc-windows-msvc", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-pc-windows-msvc", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu", "x86_64-unknown-none"], "cfg(any(target_os = \"android\", target_os = \"linux\"))": ["aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-linux-android", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-linux-android", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu"], "cfg(any(target_os = \"dragonfly\", target_os = \"freebsd\", target_os = \"illumos\", target_os = \"netbsd\", target_os = \"openbsd\", target_os = \"solaris\"))": ["i686-unknown-freebsd", "x86_64-unknown-freebsd"], "cfg(any(target_os = \"macos\", target_os = \"ios\"))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "i686-apple-darwin", "x86_64-apple-darwin", "x86_64-apple-ios"], "cfg(any(target_os = \"macos\", target_os = \"ios\", target_os = \"freebsd\"))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "i686-apple-darwin", "i686-unknown-freebsd", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-unknown-freebsd"], "cfg(any(unix, target_os = \"wasi\"))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-apple-darwin", "i686-linux-android", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "s390x-unknown-linux-gnu", "wasm32-wasi", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu"], "cfg(not(any(target_arch = \"wasm32\", target_arch = \"wasm64\")))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-pc-windows-msvc", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-apple-darwin", "i686-linux-android", "i686-pc-windows-msvc", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "riscv32imc-unknown-none-elf", "riscv64gc-unknown-none-elf", "s390x-unknown-linux-gnu", "thumbv7em-none-eabi", "thumbv8m.main-none-eabi", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-pc-windows-msvc", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu", "x86_64-unknown-none"], "cfg(not(any(target_os = \"windows\", target_os = \"macos\", target_os = \"ios\")))": ["aarch64-fuchsia", "aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-linux-android", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "riscv32imc-unknown-none-elf", "riscv64gc-unknown-none-elf", "s390x-unknown-linux-gnu", "thumbv7em-none-eabi", "thumbv8m.main-none-eabi", "wasm32-unknown-unknown", "wasm32-wasi", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu", "x86_64-unknown-none"], "cfg(not(target_arch = \"wasm32\"))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-pc-windows-msvc", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-apple-darwin", "i686-linux-android", "i686-pc-windows-msvc", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "riscv32imc-unknown-none-elf", "riscv64gc-unknown-none-elf", "s390x-unknown-linux-gnu", "thumbv7em-none-eabi", "thumbv8m.main-none-eabi", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-pc-windows-msvc", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu", "x86_64-unknown-none"], "cfg(not(windows))": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-apple-darwin", "i686-linux-android", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "riscv32imc-unknown-none-elf", "riscv64gc-unknown-none-elf", "s390x-unknown-linux-gnu", "thumbv7em-none-eabi", "thumbv8m.main-none-eabi", "wasm32-unknown-unknown", "wasm32-wasi", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu", "x86_64-unknown-none"], "cfg(target_arch = \"wasm32\")": ["wasm32-unknown-unknown", "wasm32-wasi"], "cfg(target_env = \"msvc\")": ["aarch64-pc-windows-msvc", "i686-pc-windows-msvc", "x86_64-pc-windows-msvc"], "cfg(target_env = \"sgx\")": [], "cfg(target_family = \"unix\")": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-apple-darwin", "i686-linux-android", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu"], "cfg(target_feature = \"atomics\")": [], "cfg(target_os = \"android\")": ["aarch64-linux-android", "armv7-linux-androideabi", "i686-linux-android", "x86_64-linux-android"], "cfg(target_os = \"dragonfly\")": [], "cfg(target_os = \"fuchsia\")": ["aarch64-fuchsia", "x86_64-fuchsia"], "cfg(target_os = \"hermit\")": [], "cfg(target_os = \"redox\")": [], "cfg(target_os = \"wasi\")": ["wasm32-wasi"], "cfg(target_os = \"windows\")": ["aarch64-pc-windows-msvc", "i686-pc-windows-msvc", "x86_64-pc-windows-msvc"], "cfg(unix)": ["aarch64-apple-darwin", "aarch64-apple-ios", "aarch64-apple-ios-sim", "aarch64-fuchsia", "aarch64-linux-android", "aarch64-unknown-linux-gnu", "aarch64-unknown-nixos-gnu", "aarch64-unknown-nto-qnx710", "arm-unknown-linux-gnueabi", "armv7-linux-androideabi", "armv7-unknown-linux-gnueabi", "i686-apple-darwin", "i686-linux-android", "i686-unknown-freebsd", "i686-unknown-linux-gnu", "powerpc-unknown-linux-gnu", "s390x-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-apple-ios", "x86_64-fuchsia", "x86_64-linux-android", "x86_64-unknown-freebsd", "x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu"], "cfg(windows)": ["aarch64-pc-windows-msvc", "i686-pc-windows-msvc", "x86_64-pc-windows-msvc"], "i686-apple-darwin": ["i686-apple-darwin"], "i686-linux-android": ["i686-linux-android"], "i686-pc-windows-gnu": [], "i686-pc-windows-msvc": ["i686-pc-windows-msvc"], "i686-unknown-freebsd": ["i686-unknown-freebsd"], "i686-unknown-linux-gnu": ["i686-unknown-linux-gnu"], "i686-uwp-windows-gnu": [], "i686-uwp-windows-msvc": [], "powerpc-unknown-linux-gnu": ["powerpc-unknown-linux-gnu"], "riscv32imc-unknown-none-elf": ["riscv32imc-unknown-none-elf"], "riscv64gc-unknown-none-elf": ["riscv64gc-unknown-none-elf"], "s390x-unknown-linux-gnu": ["s390x-unknown-linux-gnu"], "thumbv7em-none-eabi": ["thumbv7em-none-eabi"], "thumbv8m.main-none-eabi": ["thumbv8m.main-none-eabi"], "wasm32-unknown-unknown": ["wasm32-unknown-unknown"], "wasm32-wasi": ["wasm32-wasi"], "x86_64-apple-darwin": ["x86_64-apple-darwin"], "x86_64-apple-ios": ["x86_64-apple-ios"], "x86_64-fuchsia": ["x86_64-fuchsia"], "x86_64-linux-android": ["x86_64-linux-android"], "x86_64-pc-windows-gnu": [], "x86_64-pc-windows-msvc": ["x86_64-pc-windows-msvc"], "x86_64-unknown-freebsd": ["x86_64-unknown-freebsd"], "x86_64-unknown-linux-gnu": ["x86_64-unknown-linux-gnu", "x86_64-unknown-nixos-gnu"], "x86_64-unknown-none": ["x86_64-unknown-none"], "x86_64-uwp-windows-gnu": [], "x86_64-uwp-windows-msvc": []}, "direct_deps": ["atomicwrites 0.3.1", "base64 0.13.0", "bytes 1.2.1", "chrono 0.4.22", "clap 3.2.22", "comfy-table 6.1.0", "crossterm 0.25.0", "ctrlc 3.2.3", "derivative 2.2.0", "dirs 4.0.0", "duct 0.13.5", "duct_sh 0.13.5", "env_logger 0.9.1", "humantime 2.1.0", "k8s-openapi 0.14.0", "lazy_static 1.4.0", "os_pipe 1.0.1", "p12 0.6.3", "pem 1.1.0", "regex 1.6.0", "reqwest 0.11.12", "rustls 0.20.6", "rustyline 10.0.0", "serde 1.0.144", "serde_derive 1.0.144", "serde_json 1.0.85", "serde_with 2.0.1", "serde_yaml 0.9.13", "strfmt 0.2.2", "tempdir 0.3.7", "tokio 1.21.1", "url 2.3.1", "yasna 0.5.0"], "direct_dev_deps": []}
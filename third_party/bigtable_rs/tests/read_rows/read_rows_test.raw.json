{"readRowsTests": [{"description": "invalid - no commit", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}], "results": [{"error": true}]}, {"description": "invalid - no cell key before commit", "chunks": [{"commitRow": true}], "results": [{"error": true}]}, {"description": "invalid - no cell key before value", "chunks": [{"timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}], "results": [{"error": true}]}, {"description": "invalid - new col family must specify qualifier", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"familyName": "B", "timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"error": true}]}, {"description": "bare commit implies ts=0", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}, {"commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C"}]}, {"description": "simple row with timestamp", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}]}, {"description": "missing timestamp, implied ts=0", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "value": "value-VAL"}]}, {"description": "empty cell value", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C"}]}, {"description": "two unsplit cells", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "98", "value": "value-VAL_2"}]}, {"description": "two qualifiers", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"qualifier": "RA==", "timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK", "familyName": "A", "qualifier": "D", "timestampMicros": "98", "value": "value-VAL_2"}]}, {"description": "two families", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"familyName": "B", "qualifier": "RQ==", "timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK", "familyName": "B", "qualifier": "E", "timestampMicros": "98", "value": "value-VAL_2"}]}, {"description": "with labels", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "labels": ["L_1"], "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"timestampMicros": "98", "labels": ["L_2"], "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1", "label": "L_1"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "98", "value": "value-VAL_2", "label": "L_2"}]}, {"description": "split cell, bare commit", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dg==", "valueSize": 9, "commitRow": false}, {"value": "YWx1ZS1WQUw=", "commitRow": false}, {"commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C"}]}, {"description": "split cell", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dg==", "valueSize": 9, "commitRow": false}, {"value": "YWx1ZS1WQUw=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}]}, {"description": "split four ways", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "labels": ["L"], "value": "dg==", "valueSize": 9, "commitRow": false}, {"value": "YQ==", "valueSize": 9, "commitRow": false}, {"value": "bA==", "valueSize": 9, "commitRow": false}, {"value": "dWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL", "label": "L"}]}, {"description": "two split cells", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMQ==", "commitRow": false}, {"timestampMicros": "98", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMg==", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "98", "value": "value-VAL_2"}]}, {"description": "multi-qualifier splits", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMQ==", "commitRow": false}, {"qualifier": "RA==", "timestampMicros": "98", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMg==", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK", "familyName": "A", "qualifier": "D", "timestampMicros": "98", "value": "value-VAL_2"}]}, {"description": "multi-qualifier multi-split", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YQ==", "valueSize": 11, "commitRow": false}, {"value": "bHVlLVZBTF8x", "commitRow": false}, {"qualifier": "RA==", "timestampMicros": "98", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YQ==", "valueSize": 11, "commitRow": false}, {"value": "bHVlLVZBTF8y", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK", "familyName": "A", "qualifier": "D", "timestampMicros": "98", "value": "value-VAL_2"}]}, {"description": "multi-family split", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMQ==", "commitRow": false}, {"familyName": "B", "qualifier": "RQ==", "timestampMicros": "98", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMg==", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK", "familyName": "B", "qualifier": "E", "timestampMicros": "98", "value": "value-VAL_2"}]}, {"description": "invalid - no commit between rows", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}], "results": [{"error": true}]}, {"description": "invalid - no commit after first row", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"error": true}]}, {"description": "invalid - last row missing commit", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}, {"error": true}]}, {"description": "invalid - duplicate row key", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}, {"rowKey": "UktfMQ==", "familyName": "B", "qualifier": "RA==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}, {"error": true}]}, {"description": "invalid - new row missing row key", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}, {"timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}, {"error": true}]}, {"description": "two rows", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}, {"rowKey": "RK_2", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}]}, {"description": "two rows implicit timestamp", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "value": "dmFsdWUtVkFM", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "value": "value-VAL"}, {"rowKey": "RK_2", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}]}, {"description": "two rows empty value", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C"}, {"rowKey": "RK_2", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}]}, {"description": "two rows, one with multiple cells", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "B", "qualifier": "RA==", "timestampMicros": "97", "value": "dmFsdWUtVkFMXzM=", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "98", "value": "value-VAL_2"}, {"rowKey": "RK_2", "familyName": "B", "qualifier": "D", "timestampMicros": "97", "value": "value-VAL_3"}]}, {"description": "two rows, multiple cells", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"qualifier": "RA==", "timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "B", "qualifier": "RQ==", "timestampMicros": "97", "value": "dmFsdWUtVkFMXzM=", "commitRow": false}, {"qualifier": "Rg==", "timestampMicros": "96", "value": "dmFsdWUtVkFMXzQ=", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK_1", "familyName": "A", "qualifier": "D", "timestampMicros": "98", "value": "value-VAL_2"}, {"rowKey": "RK_2", "familyName": "B", "qualifier": "E", "timestampMicros": "97", "value": "value-VAL_3"}, {"rowKey": "RK_2", "familyName": "B", "qualifier": "F", "timestampMicros": "96", "value": "value-VAL_4"}]}, {"description": "two rows, multiple cells, multiple families", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"familyName": "B", "qualifier": "RQ==", "timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "M", "qualifier": "Tw==", "timestampMicros": "97", "value": "dmFsdWUtVkFMXzM=", "commitRow": false}, {"familyName": "N", "qualifier": "UA==", "timestampMicros": "96", "value": "dmFsdWUtVkFMXzQ=", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1"}, {"rowKey": "RK_1", "familyName": "B", "qualifier": "E", "timestampMicros": "98", "value": "value-VAL_2"}, {"rowKey": "RK_2", "familyName": "M", "qualifier": "O", "timestampMicros": "97", "value": "value-VAL_3"}, {"rowKey": "RK_2", "familyName": "N", "qualifier": "P", "timestampMicros": "96", "value": "value-VAL_4"}]}, {"description": "two rows, four cells, 2 labels", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "99", "labels": ["L_1"], "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "B", "qualifier": "RA==", "timestampMicros": "97", "labels": ["L_3"], "value": "dmFsdWUtVkFMXzM=", "commitRow": false}, {"timestampMicros": "96", "value": "dmFsdWUtVkFMXzQ=", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "99", "value": "value-VAL_1", "label": "L_1"}, {"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "98", "value": "value-VAL_2"}, {"rowKey": "RK_2", "familyName": "B", "qualifier": "D", "timestampMicros": "97", "value": "value-VAL_3", "label": "L_3"}, {"rowKey": "RK_2", "familyName": "B", "qualifier": "D", "timestampMicros": "96", "value": "value-VAL_4"}]}, {"description": "two rows with splits, same timestamp", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMQ==", "commitRow": true}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dg==", "valueSize": 11, "commitRow": false}, {"value": "YWx1ZS1WQUxfMg==", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_1"}, {"rowKey": "RK_2", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_2"}]}, {"description": "invalid - bare reset", "chunks": [{"resetRow": true}], "results": [{"error": true}]}, {"description": "invalid - bad reset, no commit", "chunks": [{"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}], "results": [{"error": true}]}, {"description": "invalid - missing key after reset", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}, {"resetRow": true}, {"timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"error": true}]}, {"description": "no data after reset", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}, {"resetRow": true}]}, {"description": "simple reset", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}]}, {"description": "reset to new val", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_2"}]}, {"description": "reset to new qual", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "RA==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "D", "timestampMicros": "100", "value": "value-VAL_1"}]}, {"description": "reset with splits", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"timestampMicros": "98", "value": "dmFsdWUtVkFMXzI=", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_2"}]}, {"description": "reset two cells", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzI=", "commitRow": false}, {"timestampMicros": "97", "value": "dmFsdWUtVkFMXzM=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_2"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "97", "value": "value-VAL_3"}]}, {"description": "two resets", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzI=", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzM=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_3"}]}, {"description": "reset then two cells", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"resetRow": true}, {"rowKey": "Uks=", "familyName": "B", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzI=", "commitRow": false}, {"qualifier": "RA==", "timestampMicros": "97", "value": "dmFsdWUtVkFMXzM=", "commitRow": true}], "results": [{"rowKey": "RK", "familyName": "B", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_2"}, {"rowKey": "RK", "familyName": "B", "qualifier": "D", "timestampMicros": "97", "value": "value-VAL_3"}]}, {"description": "reset to new row", "chunks": [{"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": false}, {"resetRow": true}, {"rowKey": "UktfMg==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzI=", "commitRow": true}], "results": [{"rowKey": "RK_2", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_2"}]}, {"description": "reset in between chunks", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "labels": ["L"], "value": "dg==", "valueSize": 10, "commitRow": false}, {"value": "YQ==", "valueSize": 10, "commitRow": false}, {"resetRow": true}, {"rowKey": "UktfMQ==", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFMXzE=", "commitRow": true}], "results": [{"rowKey": "RK_1", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL_1"}]}, {"description": "invalid - reset with chunk", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "labels": ["L"], "value": "dg==", "valueSize": 10, "commitRow": false}, {"value": "YQ==", "valueSize": 10, "resetRow": true}], "results": [{"error": true}]}, {"description": "invalid - commit with chunk", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "labels": ["L"], "value": "dg==", "valueSize": 10, "commitRow": false}, {"value": "YQ==", "valueSize": 10, "commitRow": true}], "results": [{"error": true}]}, {"description": "empty cell chunk", "chunks": [{"rowKey": "Uks=", "familyName": "A", "qualifier": "Qw==", "timestampMicros": "100", "value": "dmFsdWUtVkFM", "commitRow": false}, {"commitRow": false}, {"commitRow": true}], "results": [{"rowKey": "RK", "familyName": "A", "qualifier": "C", "timestampMicros": "100", "value": "value-VAL"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C"}, {"rowKey": "RK", "familyName": "A", "qualifier": "C"}]}]}
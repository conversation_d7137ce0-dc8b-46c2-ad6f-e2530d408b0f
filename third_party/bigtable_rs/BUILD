load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_library")

rust_library(
    name = "bigtable_rs",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    data = ["src/roots.pem"],
    edition = "2021",
    features = [
        "default",
        "emit_event_on_error",
    ],
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//visibility:public"],
    deps = all_crate_deps(
        normal = True,
    ),
)

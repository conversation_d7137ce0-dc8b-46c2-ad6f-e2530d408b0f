[package]
name = "bigtable_rs"
description = "A very simple Google Bigtable client lib in Rust"
version = "0.2.16"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
edition = "2021"
license = "MIT"
repository = "https://github.com/liufuyang/bigtable_rs/"
keywords = ["bigtable"]
readme = "../README.md"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# tonic, prost, and prost-types are need for build generated rs files
http = "1.1.0"
hyper-util = { version = "0.1.7", features = ["tokio"] }
tokio = { version = "1.25.0", features = ["rt-multi-thread"] }
tonic = { version = "0.12.2", features = ["tls", "transport"] }
tower = { version = "0.5.1", features = ["util"] }
prost = "0.13.1"
prost-types = "0.13.1"
prost-wkt = { version = "0.6.0" }
prost-wkt-types = { version = "0.6.0" }
serde = { version = "1.0.192", features = ["derive"] }
serde_with = { version = "3.4.0", features = ["base64"] }
# end of above part
futures-util = "0.3.31"
gcp_auth = "0.12.2"
log = "0.4.20"
thiserror = "2.0.3"

[dev-dependencies]
env_logger = "0.11.1"
serde_json = "1.0.85"
serde_path_to_error = "0.1.8"

[build-dependencies]
tonic-build = { version = "0.12.2", features = ["cleanup-markdown"] }
prost-build = "0.13.1"
prost-wkt-build = { version = "0.6.0" }

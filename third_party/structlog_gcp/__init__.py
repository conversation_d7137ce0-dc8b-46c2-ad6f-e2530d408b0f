from __future__ import annotations
from structlog.typing import Processor

from third_party.structlog_gcp import errors, processors


def build_processors(
    service: str | None = None,
    version: str | None = None,
) -> list[Processor]:
    procs = []

    procs.extend(processors.CoreCloudLogging().setup())
    procs.extend(processors.LogSeverity().setup())
    procs.extend(processors.CodeLocation().setup())
    procs.extend(errors.ReportException().setup())
    # including ERROR here causes logging.error events to go to GCP Error Reporting
    procs.extend(errors.ReportError(["ERROR", "CRITICAL"]).setup())
    procs.extend(errors.ServiceContext(service, version).setup())
    procs.extend(processors.FormatAsCloudLogging().setup())

    return procs
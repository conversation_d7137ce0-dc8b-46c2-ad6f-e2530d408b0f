load("//tools/bzl:python.bzl", "py_grpc_library", "py_library", "py_proto_library")
load("//tools/bzl:go.bzl", "go_proto_library", "go_grpc_library")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")


proto_library(
    name = "bes_proto",
    srcs = [
        "build_events.proto",
        "build_status.proto",
        "publish_build_event.proto",
    ],
    deps = [
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/api:client_proto",
        "@googleapis//google/api:field_behavior_proto",
        "@googleapis//google/api:http_proto",
        "@googleapis//google/api:launch_stage_proto",
        "@protobuf//:any_proto",
        "@protobuf//:descriptor_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:empty_proto",
        "@protobuf//:timestamp_proto",
        "@protobuf//:wrappers_proto",
    ],
)

py_grpc_library(
    name = "bes_py_proto",
    protos = [
        ":bes_proto",
    ],
    visibility = ["//tools:__subpackages__"],
)


go_grpc_library(
    name = "bes_go_proto",
    proto = ":bes_proto",
    visibility = ["//tools:__subpackages__"],
    deps = [
        "@googleapis//google/api:annotations_go_proto",
        "@googleapis//google/api:client_go_proto",
        "@googleapis//google/api:field_behavior_go_proto",
    ]
)

proto_library(
    name = "build_event_stream_proto",
    srcs = [
        "build_event_stream.proto",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        "@bazel//src/main/protobuf:command_line_proto",
        "@bazel//src/main/protobuf:failure_details_proto",
        "@bazel//src/main/protobuf:invocation_policy_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "build_event_stream_py_proto_lib",
    output_mode = "NO_PREFIX_FLAT",
    protos = ["build_event_stream_proto"],
    imports = ["."],
)

go_proto_library(
    name = "build_event_stream_go_proto",
    proto = "build_event_stream_proto",
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":command_line_go_proto",
        ":failure_details_go_proto",
        ":invocation_policy_go_proto",
        ":option_filters_go_proto",
    ],
)

go_proto_library(
    name = "command_line_go_proto",
    proto = "@bazel//src/main/protobuf:command_line_proto",
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":option_filters_go_proto",
    ],
    output_files = [
        "command_line.pb.go",
    ],
)

go_proto_library(
    name = "failure_details_go_proto",
    proto = "@bazel//src/main/protobuf:failure_details_proto",
    visibility = ["//tools:__subpackages__"],
    output_files = [
        "failure_details.pb.go",
    ],
)

go_proto_library(
    name = "invocation_policy_go_proto",
    proto = "@bazel//src/main/protobuf:invocation_policy_proto",
    visibility = ["//tools:__subpackages__"],
    output_files = [
        "invocation_policy.pb.go",
    ],
)

go_proto_library(
    name = "option_filters_go_proto",
    proto = "@bazel//src/main/protobuf:option_filters_proto",
    visibility = ["//tools:__subpackages__"],
    output_files = [
        "option_filters.pb.go",
    ],
)

py_proto_library(
    name = "bazel_py_proto",
    output_mode = "NO_PREFIX",
    protos = [
        "@bazel//src/main/protobuf:command_line_proto",
        "@bazel//src/main/protobuf:failure_details_proto",
        "@bazel//src/main/protobuf:invocation_policy_proto",
        "@bazel//src/main/protobuf:option_filters_proto",
    ],
)

py_library(
    name = "build_event_stream_py_proto",
    srcs = [],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":bazel_py_proto",
        ":build_event_stream_py_proto_lib",
        requirement("protobuf"),
        requirement("delimited-protobuf"),
    ],
)

proto_library(
    name = "bytestream_proto",
    srcs = [
        "bytestream.proto",
    ],
    visibility = ["//tools:__subpackages__"],
)

py_grpc_library(
    name = "bytestream_py_proto",
    protos = ["bytestream_proto"],
    visibility = ["//tools:__subpackages__"],
)

py_proto_library(
    name = "googleapis_py_proto",
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/api:client_proto",
        "@googleapis//google/api:field_behavior_proto",
        "@googleapis//google/api:http_proto",
        "@googleapis//google/api:launch_stage_proto",
    ],
    visibility = ["//tools:__subpackages__"],
)

load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_proto_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

py_proto_library(
    name = "googleapis_annotations_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:annotations_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_http_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:http_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_client_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:client_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_field_behavior_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:field_behavior_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_launch_state_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:launch_stage_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_resource_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:resource_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_routing_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/api:routing_proto",
    ],
    visibility = ["//visibility:public"],
)

py_grpc_library(
    name = "googleapis_longrunning_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/longrunning:operations_proto",
    ],
    visibility = ["//tools:__subpackages__"],
    deps = [
        ":googleapis_annotations_py_proto",
        ":googleapis_client_py_proto",
        ":googleapis_http_py_proto",
        ":googleapis_launch_state_py_proto",
        ":googleapis_status_py_proto",
    ],
)

py_proto_library(
    name = "googleapis_status_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/rpc:status_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_rpc_code_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/rpc:code_proto",
    ],
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "googleapis_rpc_error_details_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/rpc:error_details_proto",
    ],
    visibility = ["//visibility:public"],
)

py_grpc_library(
    name = "googleapis_bigtable_v2_py_proto",
    imports = ["."],
    output_mode = "NO_PREFIX",
    protos = [
        "@googleapis//google/bigtable/v2:bigtable_proto",
        "@googleapis//google/type:date_proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":googleapis_annotations_py_proto",
        ":googleapis_client_py_proto",
        ":googleapis_field_behavior_py_proto",
        ":googleapis_http_py_proto",
        ":googleapis_launch_state_py_proto",
        ":googleapis_resource_py_proto",
        ":googleapis_routing_py_proto",
        ":googleapis_rpc_code_py_proto",
        ":googleapis_rpc_error_details_py_proto",
        ":googleapis_status_py_proto",
    ],
)

# Note(zhewei): this is a workaround for generating external proto libraries for typescript.
# Using "@googleapis//google/rpc:status_proto" doesn't work here in ts_proto_library due to:
#   Error in declare_file: the output artifact
#   'external/googleapis~/google/rpc/status_pb.js' is not under package directory
#   'third_party/proto' for target
#   '//third_party/proto:__googleapis_status_ts_proto.ts_protoc'
# It's a bug that ts_proto_library tries to save outputs under the external directory, rather
# than the directory of the current BUILD file.
proto_library(
    name = "googleapis_status_proto",
    srcs = ["status.proto"],
    visibility = ["//visibility:private"],
    deps = [
        "@protobuf//:any_proto",
    ],
)

ts_proto_library(
    name = "googleapis_status_ts_proto",
    node_modules = "//:node_modules",
    proto = ":googleapis_status_proto",
    visibility = ["//visibility:public"],
)

# use this in `deps`. Exposes JSInfo provider.
js_library(
    name = "googleapis_status_js_proto",
    srcs = ["//:googleapis_status_ts_proto"],
    visibility = ["//visibility:public"],
)

# Why Keep Third-Party Protos Here?

Using `status.proto` as an example, if we attempt to directly build the TypeScript
proto library from an external dependency `@googleapis//google/rpc:status_proto`
like this:
```
ts_proto_library(
    name = "googleapis_status_ts_proto",
    node_modules = "//:node_modules",
    proto = "@googleapis//google/rpc:status_proto",
    visibility = ["//visibility:public"],
)
```

We encounter the following error:
```
Error in declare_file: the output artifact 'external/googleapis~/google/rpc/status_pb.js'
is not under package directory 'third_party/proto' for target
'//third_party/proto:__googleapis_status_ts_proto.ts_protoc'
```

This is a known issue with `aspect-build/rules_ts`, as reported in
https://github.com/aspect-build/rules_ts/issues/466, but it remains unresolved.

To work around this, we download and store a few third-party protos in this directory.
We then explicitly build the corresponding proto libraries locally. Additionally, we
include build logic (in the root BUILD file) to copy the generated proto files to the
appropriate package paths, ensuring that other protos can access these generated files.

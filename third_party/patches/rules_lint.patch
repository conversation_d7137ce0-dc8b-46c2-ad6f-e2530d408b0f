diff --git a/format/defs.bzl b/format/defs.bzl
index f521810..ad6c50a 100644
--- a/format/defs.bzl
+++ b/format/defs.bzl
@@ -32,9 +32,8 @@ def _format_attr_factory(target_name, lang, toolname, tool_label, mode, disable_
     return {
         "name": target_name + (".check" if mode in "check" else ""),
         ("env" if mode == "test" else "environment"): {
-            # NB: can't use str(Label(target_name)) here because b<PERSON><PERSON><PERSON> makes it
-            # the apparent repository, starts with @@aspect_rules_lint~override
-            "FIX_TARGET": "//{}:{}".format(native.package_name(), target_name),
+            # augment specific target
+            "FIX_TARGET": "//:format",
             "tool": "$(rlocationpaths %s)" % tool_label,
             "lang": lang,
             "flags": FIX_FLAGS[toolname] if mode == "fix" else CHECK_FLAGS[toolname],
--- a/dh/x25519/BUILD.bazel
+++ b/dh/x25519/BUILD.bazel
@@ -13,6 +13,8 @@
         "key.go",
         "table.go",
+        "//math/fp25519:fp_amd64.h",
     ],
+    cgo = True,
     importpath = "github.com/cloudflare/circl/dh/x25519",
     visibility = ["//visibility:public"],
     deps = [
@@ -25,6 +27,8 @@
     }),
 )

+exports_files(["fp_amd64.h"], ["//visibility:public"])
+
 alias(
     name = "go_default_library",
     actual = ":x25519",

--- a/dh/x448/BUILD.bazel
+++ b/dh/x448/BUILD.bazel
@@ -13,6 +13,8 @@
         "key.go",
         "table.go",
+        "//math/fp448:fp_amd64.h",
     ],
+    cgo = True,
     importpath = "github.com/cloudflare/circl/dh/x448",
     visibility = ["//visibility:public"],
     deps = [
@@ -25,6 +27,8 @@
     }),
 )

+exports_files(["fp_amd64.h"], ["//visibility:public"])
+
 alias(
     name = "go_default_library",
     actual = ":x448",

--- a/math/fp25519/BUILD.bazel
+++ b/math/fp25519/BUILD.bazel
@@ -10,6 +10,7 @@
         "fp_generic.go",
         "fp_noasm.go",
     ],
+    cgo=True,
     importpath = "github.com/cloudflare/circl/math/fp25519",
     visibility = ["//visibility:public"],
     deps = [
@@ -22,6 +23,8 @@
     }),
 )

+exports_files(["fp_amd64.h"], ["//visibility:public"])
+
 alias(
     name = "go_default_library",
     actual = ":fp25519",

--- a/math/fp448/BUILD.bazel
+++ b/math/fp448/BUILD.bazel
@@ -10,6 +10,7 @@
         "fp_generic.go",
         "fp_noasm.go",
     ],
+    cgo=True,
     importpath = "github.com/cloudflare/circl/math/fp448",
     visibility = ["//visibility:public"],
     deps = [
@@ -22,6 +23,8 @@
     }),
 )

+exports_files(["fp_amd64.h"], ["//visibility:public"])
+
 alias(
     name = "go_default_library",
     actual = ":fp448",

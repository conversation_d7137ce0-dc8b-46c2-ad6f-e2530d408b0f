# third_party dependencies

This directory contains code dependencies that for some reason or the other cannot be included as external Bazel repository, e.g. via
`tools/bzl/deps/cc_deps.bzl` or pip dependency `tools/python_deps`.

It also contains the BUILD files for external dependencies that require special handling.

Notes:
- Prefer external repositories instead of vendoring in code if possible.
- Strongly try to avoid unmanaged dependencies, e.g. using OS libraries. At this point, we only do that for CUDA.
- For C++ dependencies: If bazel is used, just use <PERSON><PERSON>, if cmake or make is used use [https://github.com/bazelbuild/rules_foreign_cc](rules_foreign_cc). If neither Ba<PERSON>, cmake or make is used, some special handling might be required.
- For Python: see tools/python_deps/Readme.md
- For Rust: Use Cargo and `rules_rust` to manage dependencies
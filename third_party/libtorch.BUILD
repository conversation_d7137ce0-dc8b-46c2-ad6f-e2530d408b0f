cc_library(
    name = "torch",
    srcs = glob(
        ["torch/lib/*"],
        exclude = ["torch/lib/libcu*"],
    ),
    hdrs = glob([
        "torch/include/**/*.h",
        "torch/include/**/*.cuh",
        "torch/include/**/*.hpp",
    ]),
    copts = ["-D_GLIBCXX_USE_CXX11_ABI=0"],
    data = glob(
        ["torch/lib/*"],
        exclude = ["torch/lib/libcu*"],
    ),
    includes = [
        "torch/include",
        "torch/include/TH",
        "torch/include/THC",
        "torch/include/torch/csrc/api/include",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@cub",
        "@remote_cuda_toolchain//:cublas",
        "@remote_cuda_toolchain//:libcusolver",
        "@remote_cuda_toolchain//:libcusparse",
        "@rules_cuda//cuda:runtime",
        "@thrust",
    ],
)

The index.js, index.d.ts, and LICENCE-MIT files in this directory are taken from
https://github.com/kaelzhang/node-ignore, which is a package for parsing
.gitignore files. They are based on b00eaa3db955da3473265b14997f3cbbaa918b99,
which was top-of-trunk at the time of this writing.

That version of the  package has a bug, which is documented here:
https://github.com/kaelzhang/node-ignore/issues/116.

As of this writing, that bug has not been addressed (or even acknowledged). The
index.js file here has a fix for that bug. The other files are unmodified. If the
bug is ever fixed, this directory should be removed.

For posterity, this is the diff:

======== begin ========
diff --git a/index.js b/index.js
index c559806..9a9af44 100644
--- a/index.js
+++ b/index.js
@@ -560,12 +560,14 @@ class Ignore {
       slices
     )

-    // If the path contains a parent directory, check the parent first
-    return cache[path] = parent.ignored
+    if (parent.ignored) {
       // > It is not possible to re-include a file if a parent directory of
       // >   that file is excluded.
-      ? parent
-      : this._testOne(path, checkUnignored)
+      return cache[path] = parent
+    }
+
+    const curr = this._testOne(path, checkUnignored)
+    return cache[path] = {ignored: curr.ignored, unignored: parent.unignored || curr.unignored}
   }

   ignores (path) {
======== end ========

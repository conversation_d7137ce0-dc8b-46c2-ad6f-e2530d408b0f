"""Integration tests for remote agents API.

This test suite covers the basic functionality of the remote agents API:
- Creating remote agents
- Listing remote agents
- Chatting with remote agents
- Getting chat history
- Deleting remote agents

These tests interact with
- a live deployment of the remote agents service.
- a running beachhead instance.
- agent service that supports beachhead.
"""

import time
import urllib3
import pytest
import subprocess
from pathlib import Path
from dataclasses import asdict
from typing import Tuple, Optional, Any, Union, List

from base.augment_client.client import (
    AugmentClient,
    GithubCommitRef,
    RemoteAgentWorkspaceSetup,
)
from services.api_proxy.public_api_pb2 import (
    RemoteAgentStatus,
    RemoteAgentWorkspaceStatus,
)
import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper

# Suppress InsecureRequestWarning when using verify=False
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


@pytest.fixture(scope="session")
def beachhead_paths(request):
    """Get the paths to the beachhead dependencies.

    This fixture gets the paths from the command-line arguments.

    Returns:
        dict: Dictionary of paths to beachhead dependencies
    """
    return {
        "namespace": request.config.getoption("--namespace"),
        "binary_path": request.config.getoption("--beachhead-img-deploy-path"),
        "oci_innie": request.config.getoption("--beachhead-oci-innie-path"),
        "oci_outie": request.config.getoption("--beachhead-oci-outie-path"),
        "jsonnet": request.config.getoption("--beachhead-jsonnet-path"),
        "clusters": request.config.getoption("--beachhead-clusters-path"),
        "crane": request.config.getoption("--beachhead-crane-path"),
    }


# Override the kubecfg script to use the remote agents kubecfg
@pytest.fixture(scope="session")
def kubecfg_script():
    """Returns the kubecfg script to use."""
    return Path("services/test/remote_agents_kubecfg.sh")


@pytest.fixture(scope="session")
def build_and_push_beachhead_image_fixture(beachhead_paths):
    """Builds and pushes the beachhead image for remote agent testing.

    This fixture uses the deploy-dev binary to build and push the beachhead image
    to the dev environment with a custom tag based on the current Kubernetes namespace.

    Args:
        img_deploy_path: Path to the beachhead deploy binary, provided by the --beachhead-img-deploy-path argument
        beachhead_paths: Dictionary of paths to beachhead dependencies

    Returns:
        str: The tag used for the image
    """
    tag = k8s_test_helper.get_dev_namespace()
    cmd = [
        beachhead_paths["binary_path"],
        "push",
        "--env=DEV",
        f"--tag={tag}",
    ]

    # Add the paths to the dependencies if they are provided
    if beachhead_paths["oci_innie"]:
        cmd.append(f"--oci-innie={beachhead_paths['oci_innie']}")
    if beachhead_paths["oci_outie"]:
        cmd.append(f"--oci-outie={beachhead_paths['oci_outie']}")
    if beachhead_paths["jsonnet"]:
        cmd.append(f"--jsonnet={beachhead_paths['jsonnet']}")
    if beachhead_paths["clusters"]:
        cmd.append(f"--clusters={beachhead_paths['clusters']}")
    if beachhead_paths["crane"]:
        cmd.append(f"--crane={beachhead_paths['crane']}")

    print(f"Building and pushing beachhead image with tag: {tag}")
    print(f"Command: {' '.join(cmd)}")

    try:
        result = subprocess.run(
            cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )
        print(f"Successfully built and pushed beachhead image: {result.stdout}")
        return tag
    except subprocess.CalledProcessError as e:
        print(f"Failed to build and push beachhead image: {e.stderr}")
        raise


# Helper functions for common test operations


def cleanup_existing_agents(augment_client: AugmentClient) -> None:
    """Delete all existing agents to avoid max limit errors."""
    existing_agents = augment_client.list_remote_agents()
    for agent in existing_agents.remote_agents:
        print(f"Deleting existing agent {agent.remote_agent_id}")
        try:
            augment_client.delete_remote_agent(agent.remote_agent_id)
        except Exception as e:
            print(f"Failed to delete agent {agent.remote_agent_id}: {e}")


def create_default_workspace_setup() -> Tuple[RemoteAgentWorkspaceSetup, list]:
    """Create the default workspace setup and initial request nodes used by all tests."""
    # Create a simple workspace with GitHub repo reference
    github_ref = GithubCommitRef(
        repository_url="https://github.com/augmentcode/augment-swebench-agent",
        git_ref="main",
    )

    # Create a simple workspace setup with GitHub repo reference
    workspace_setup = RemoteAgentWorkspaceSetup()
    workspace_setup.starting_files = {"github_commit_ref": asdict(github_ref)}

    # Create a simpler initial request nodes structure
    initial_request_nodes = [
        {
            "id": 1,
            "type": 0,  # TEXT type (using 0 instead of 1 to match ChatRequestNodeType.TEXT)
            "text_node": {"content": "Summarize the repository."},
        }
    ]

    return workspace_setup, initial_request_nodes


def create_and_verify_agent(
    augment_client: AugmentClient,
    workspace_setup: RemoteAgentWorkspaceSetup,
    initial_request_nodes: list,
) -> str:
    """Create a remote agent and verify it was created successfully."""
    # Create a remote agent
    create_response = augment_client.create_remote_agent(
        workspace_setup=workspace_setup, initial_request_nodes=initial_request_nodes
    )
    agent_id = create_response.remote_agent_id

    # Verify the agent was created by listing agents
    response = augment_client.list_remote_agents()
    agent_found = False
    agent_status = None
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            agent_found = True
            agent_status = agent.status
            assert agent.started_at is not None, "Agent started_at should not be None"
            assert agent.updated_at is not None, "Agent updated_at should not be None"
            break
    assert agent_found, f"Created agent {agent_id} not found in list response"
    # Initial status could be STARTING, RUNNING, or IDLE depending on timing
    assert agent_status in [
        RemoteAgentStatus.AGENT_STARTING,
        RemoteAgentStatus.AGENT_RUNNING,
        RemoteAgentStatus.AGENT_IDLE,
    ], f"Initial agent status is {agent_status}, expected STARTING, RUNNING, or IDLE"

    return agent_id


def wait_for_agent_idle(
    augment_client: AugmentClient, agent_id: str, max_wait_time: int = 240
) -> None:
    """Wait for the agent to become IDLE (ready to receive chat messages)."""
    wait_interval = 5  # Check every 5 seconds
    wait_time = 0
    agent_ready = False

    while wait_time < max_wait_time:
        # List agents to check status
        response = augment_client.list_remote_agents()
        for agent in response.remote_agents:
            if agent.remote_agent_id == agent_id:
                if agent.status == RemoteAgentStatus.AGENT_IDLE:
                    agent_ready = True
                    break

        if agent_ready:
            break

        time.sleep(wait_interval)
        wait_time += wait_interval

    assert agent_ready, f"Agent did not become IDLE within {max_wait_time} seconds"


def find_agent_status(augment_client: AugmentClient, agent_id: str) -> Optional[Any]:
    """Find and return the status of a specific agent."""
    response = augment_client.list_remote_agents()
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            return agent.status
    return None


def find_agent_workspace_status(
    augment_client: AugmentClient, agent_id: str
) -> Optional[Any]:
    """Find and return the status of a specific agent."""
    response = augment_client.list_remote_agents()
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            return agent.workspace_status
    return None


def delete_and_verify_agent(augment_client: AugmentClient, agent_id: str) -> None:
    """Delete an agent and verify it was deleted successfully."""
    # Delete the agent
    augment_client.delete_remote_agent(agent_id)

    # Verify the agent was deleted by listing agents
    response = augment_client.list_remote_agents()
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            raise Exception(f"Agent {agent_id} still found after deletion")


def wait_for_agent_status(
    augment_client: AugmentClient,
    agent_id: str,
    expected_status: Union[Any, List[Any]],
    max_wait_time: int = 60,
) -> None:
    """Wait for the agent to reach a specific status or one of multiple statuses.

    Args:
        augment_client: The client to use for API calls
        agent_id: The agent ID to check
        expected_status: A single status or list of acceptable statuses
        max_wait_time: Maximum time to wait in seconds
    """
    wait_interval = 5  # Check every 5 seconds
    wait_time = 0
    status_reached = False

    # Convert single status to list for uniform handling
    if not isinstance(expected_status, list):
        expected_statuses = [expected_status]
    else:
        expected_statuses = expected_status

    while wait_time < max_wait_time:
        agent_status = find_agent_status(augment_client, agent_id)
        if agent_status in expected_statuses:
            status_reached = True
            break

        time.sleep(wait_interval)
        wait_time += wait_interval

    assert (
        status_reached
    ), f"Agent did not reach any of {expected_statuses} within {max_wait_time} seconds"


def wait_for_agent_workspace_status(
    augment_client: AugmentClient,
    agent_id: str,
    expected_status: Union[Any, List[Any]],
    max_wait_time: int = 240,  # Longer wait time for resume
) -> None:
    """Wait for the agent to reach a specific status or one of multiple statuses.

    Args:
        augment_client: The client to use for API calls
        agent_id: The agent ID to check
        expected_status: A single status or list of acceptable statuses
        max_wait_time: Maximum time to wait in seconds
    """
    wait_interval = 5  # Check every 5 seconds
    wait_time = 0
    status_reached = False

    # Convert single status to list for uniform handling
    if not isinstance(expected_status, list):
        expected_statuses = [expected_status]
    else:
        expected_statuses = expected_status

    while wait_time < max_wait_time:
        agent_status = find_agent_workspace_status(augment_client, agent_id)
        if agent_status in expected_statuses:
            status_reached = True
            break

        time.sleep(wait_interval)
        wait_time += wait_interval

    assert (
        status_reached
    ), f"Agent did not reach any of {expected_statuses} within {max_wait_time} seconds"


def verify_agent_status(
    augment_client: AugmentClient, agent_id: str, expected_status: Any
) -> None:
    """Verify that an agent has the expected status."""
    agent_status = find_agent_status(augment_client, agent_id)
    assert agent_status is not None, f"Agent {agent_id} not found in list response"
    assert (
        agent_status == expected_status
    ), f"Agent status is {agent_status}, expected {expected_status}"


def setup_test_agent(augment_client: AugmentClient, image_tag: str) -> str:
    """Common setup for all tests: cleanup, create agent, and wait for IDLE.

    Args:
        augment_client: The client to use for API calls
        image_tag: The beachhead image tag to use

    Returns:
        str: The created agent ID
    """
    print(f"Using beachhead image with tag: {image_tag}")

    # Clean up existing agents
    cleanup_existing_agents(augment_client)

    # Create workspace setup and initial request nodes
    workspace_setup, initial_request_nodes = create_default_workspace_setup()

    # Create and verify the agent
    agent_id = create_and_verify_agent(
        augment_client, workspace_setup, initial_request_nodes
    )

    # Wait for the agent to become IDLE
    wait_for_agent_idle(augment_client, agent_id)

    return agent_id


def test_create_agent_and_chat(
    augment_client: AugmentClient, build_and_push_beachhead_image_fixture
):
    """Tests the create, chat, list, and delete remote agent endpoints.

    This test:
    1. Creates a remote agent
    2. Sends a chat message to the agent
    3. Verifies the chat appears in history
    4. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
        build_and_push_beachhead_image_fixture: Fixture that builds and pushes the beachhead image
    """
    # Setup test agent (cleanup, create, wait for IDLE)
    agent_id = setup_test_agent(augment_client, build_and_push_beachhead_image_fixture)

    # Define the chat message
    chat_message = "What package management tool does it use?"

    # Send a chat message to the agent
    augment_client.remote_agent_chat(
        remote_agent_id=agent_id,
        message=chat_message,
    )

    # Wait for the chat to be processed
    time.sleep(10)

    # Get and verify chat history
    response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)

    # Verify that the chat message appears in the history
    message_found = False
    for exchange in response.chat_history:
        # Convert the entire exchange to string and check if the message is in it
        exchange_str = str(exchange.exchange)
        if chat_message in exchange_str:
            message_found = True
            break

    assert message_found, "Chat message not found in chat history"

    # Verify that the agent's updated_at field has been updated
    response = augment_client.list_remote_agents()
    for agent in response.remote_agents:
        if agent.remote_agent_id == agent_id:
            assert agent.updated_at is not None, "Agent updated_at should not be None"
            break

    # Wait before deleting the agent
    time.sleep(5)

    # Delete and verify the agent was deleted
    delete_and_verify_agent(augment_client, agent_id)


def test_pause_and_resume_agent(
    augment_client: AugmentClient, build_and_push_beachhead_image_fixture
):
    """Tests the pause and resume remote agent endpoints.

    This test:
    1. Creates a remote agent
    2. Pauses the agent
    3. Verifies the agent is paused
    4. Resumes the agent
    5. Verifies the agent is resumed
    6. Deletes the remote agent

    Args:
        augment_client: The client to use for API calls
        build_and_push_beachhead_image_fixture: Fixture that builds and pushes the beachhead image
    """
    # Setup test agent (cleanup, create, wait for IDLE)
    agent_id = setup_test_agent(augment_client, build_and_push_beachhead_image_fixture)

    # Pause the agent
    augment_client.pause_remote_agent(agent_id)

    # Wait for and verify the agent is paused
    wait_for_agent_workspace_status(
        augment_client,
        agent_id,
        RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED,
    )

    # Resume the agent
    augment_client.resume_remote_agent(agent_id)

    # Wait for and verify the agent is resumed
    wait_for_agent_workspace_status(
        augment_client,
        agent_id,
        RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING,
    )

    # Delete and verify the agent was deleted
    delete_and_verify_agent(augment_client, agent_id)


def test_chat_to_paused_agent_resumes_workspace(
    augment_client: AugmentClient, build_and_push_beachhead_image_fixture
):
    """Tests that chatting to a paused agent resumes the workspace.
    This test:
    1. Creates a remote agent
    2. Pauses the agent
    3. Sends a chat message to the paused agent
    4. Verifies the agent resumes (becomes IDLE or RUNNING)

    Args:
        augment_client: The client to use for API calls
        build_and_push_beachhead_image_fixture: Fixture that builds and pushes the beachhead image
    """
    # Setup test agent (cleanup, create, wait for IDLE)
    agent_id = setup_test_agent(augment_client, build_and_push_beachhead_image_fixture)

    # Pause the agent
    augment_client.pause_remote_agent(agent_id)

    # Wait for and verify the agent is paused
    wait_for_agent_workspace_status(
        augment_client,
        agent_id,
        RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED,
    )

    # Send a chat message to the agent
    # TODO: investigate and make timeout stricter
    chat_message = "Hello, paused agent."
    augment_client.remote_agent_chat(
        remote_agent_id=agent_id,
        message=chat_message,
        timeout=240,
    )

    # Verify the agent is resumed (becomes IDLE or RUNNING)
    # We'll check for either status since the agent might be processing the chat
    wait_for_agent_workspace_status(
        augment_client,
        agent_id,
        RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING,
    )

    # Delete and verify the agent was deleted
    delete_and_verify_agent(augment_client, agent_id)

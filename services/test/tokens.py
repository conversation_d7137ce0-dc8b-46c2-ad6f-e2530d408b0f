"""Utilities for adding api tokens in the E2E test."""

import time
import json
import base64
import kubernetes

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
import services.api_proxy.access_token as access_token


def _get_secret_data(
    api_client: kubernetes.client.ApiClient | None, namespace, object_name, data_key
):
    """Get a datum from a Kubernetes secret."""
    v1 = kubernetes.client.CoreV1Api(api_client)
    secret = v1.read_namespaced_secret(name=object_name, namespace=namespace)
    return (
        base64.b64decode(secret.data[data_key]).decode(),
        int(secret.metadata.resource_version),
    )


def _update_secret(
    api_client: kubernetes.client.ApiClient | None,
    namespace,
    object_name,
    version,
    data_key,
    data,
):
    print("Update secret", object_name, "in", namespace, "at", version, flush=True)
    v1 = kubernetes.client.CoreV1Api(api_client)
    v1.patch_namespaced_secret(
        name=object_name,
        namespace=namespace,
        body={
            "metadata": {
                "resourceVersion": str(version),
            },
            "data": {
                data_key: base64.b64encode(data.encode()).decode(),
            },
        },
    )


class TokenUtil:
    """Utility for adding tokens to the auth-query secret."""

    def __init__(self, deploy: k8s_test_helper.DeployInfo):
        self.deploy = deploy

    def add_token(self, user_id: str, tenant_name: str) -> str:
        """Add a token to the auth-query secret.

        Args:
            user_id: The user ID to add the token for
            tenant_id: The tenant name to add the token for

        Returns:
            The token
        """
        print(f"Add token for user_id={user_id}, tenant_name={tenant_name}", flush=True)
        token = access_token.generate_token()

        new_entry = access_token.token_entry_from_token(
            token=token,
            user_id=user_id,
            tenant_name=tenant_name,
        )

        secrets_json, version = _get_secret_data(
            self.deploy.kubectl.api_client,
            namespace=self.deploy.namespace,
            object_name="auth-query-secret",
            data_key="secrets.json",
        )

        secrets = json.loads(secrets_json)

        found = False

        for entry in secrets["api_tokens"]:
            if entry["user_id"] == user_id:
                entry.update(new_entry[0])
                found = True
                break

        if not found:
            secrets["api_tokens"].extend(new_entry)

        _update_secret(
            self.deploy.kubectl.api_client,
            namespace=self.deploy.namespace,
            object_name="auth-query-secret",
            version=version,
            data_key="secrets.json",
            data=json.dumps(secrets),
        )
        self.deploy.kubectl.rollout("auth-query")
        time.sleep(10)
        return token

"""Utilities for agent integration tests."""

import json
import typing
import uuid
from collections.abc import Callable
from unittest.mock import <PERSON><PERSON><PERSON>

from base.blob_names import blob_names_pb2
from base.test_utils.synchronous_executor import SynchronousExecutor
from services.agents.tool import E as ExtraT
from services.agents.tool import T as InputT
from services.agents.tool import ValidatedTool
from services.chat_host import chat_pb2
from services.chat_host.server.chat_third_party_handler import Chat<PERSON>hird<PERSON><PERSON>y<PERSON>andler
from services.chat_host.server.handler import Chat<PERSON><PERSON>ult
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


def run_chat_stream(
    chat_handler: Chat<PERSON>hird<PERSON><PERSON>y<PERSON>and<PERSON>,
    chat_request: chat_pb2.ChatRequest,
    request_id: uuid.UUID | None = None,
    session_id: uuid.UUID | None = None,
) -> tuple[typing.Iterable[<PERSON><PERSON><PERSON><PERSON><PERSON>], RequestContext, AuthInfo]:
    if request_id is None:
        request_id = uuid.UUID("3b608c4f-b7da-4c05-8368-b7414fcd54b7")
    if session_id is None:
        session_id = uuid.UUID("e02cf196-4384-4c56-98c7-a144199038b8")

    request_context = RequestContext(
        request_id=str(request_id),
        request_session_id=str(session_id),
        request_source="unknown",
    )

    executor = SynchronousExecutor()
    auth_info = MagicMock()
    chat_results = chat_handler.chat_stream(
        chat_request,
        request_context,
        auth_info=auth_info,
        executor=executor,
    )
    return chat_results, request_context, auth_info


def run_chat(
    chat_handler: ChatThirdPartyHandler,
    chat_request: chat_pb2.ChatRequest,
    request_id: uuid.UUID | None = None,
    session_id: uuid.UUID | None = None,
) -> tuple[ChatResult, RequestContext, AuthInfo]:
    chat_results, request_context, auth_info = run_chat_stream(
        chat_handler, chat_request, request_id, session_id
    )
    full_response = ""
    unknown_blob_names = []
    checkpoint_not_found = False
    workspace_file_chunks = []
    incorporated_external_sources = []
    nodes = []
    status_code = None
    for output in chat_results:
        full_response += output.text
        unknown_blob_names.extend(output.unknown_blob_names)
        checkpoint_not_found = checkpoint_not_found or output.checkpoint_not_found
        workspace_file_chunks.extend(output.workspace_file_chunks)
        incorporated_external_sources.extend(output.incorporated_external_sources)
        nodes.extend(output.nodes)
        status_code = output.status_code
    chat_result = ChatResult(
        full_response,
        unknown_blob_names,
        checkpoint_not_found,
        workspace_file_chunks,
        incorporated_external_sources,
        nodes,
        status_code,
    )
    return chat_result, request_context, auth_info


def assert_result_contains_text(chat_result: ChatResult, text: str):
    assert (
        text in chat_result.text
    ), f"Expected {text} in chat result: {chat_result.text}"


def assert_result_has_tool_call(
    chat_result: ChatResult,
    tool_name: str,
    tool_input: dict[str, typing.Any] | Callable[[dict[str, typing.Any]], bool],
) -> str:
    for node in chat_result.nodes:
        if node.type == chat_pb2.ChatResultNodeType.TOOL_USE:
            assert node.tool_use is not None
            assert node.tool_use.name == tool_name
            if callable(tool_input):
                assert tool_input(node.tool_use.input)
            else:
                assert node.tool_use.input == tool_input
            return node.tool_use.tool_use_id
    raise AssertionError(f"Did not find tool call for {tool_name}")


class ChatBuilder:
    """Helper class to build chat requests while managing chat history."""

    def __init__(
        self,
        chat_handler: ChatThirdPartyHandler,
        base_request: chat_pb2.ChatRequest | None = None,
    ):
        self._chat_handler = chat_handler
        self.base_request = base_request or chat_pb2.ChatRequest(
            prefix="",
            suffix="",
            selected_code="",
            chat_history=[],
            blobs=[blob_names_pb2.Blobs()],
            position=chat_pb2.ChatPosition(
                prefix_begin=0,
                suffix_end=0,
                blob_name="",
            ),
            lang="",
            tool_definitions=[],
        )

    def _add_message(self, message: str) -> chat_pb2.ChatRequest:
        request = chat_pb2.ChatRequest()
        request.CopyFrom(self.base_request)
        request.nodes.append(
            chat_pb2.ChatRequestNode(
                type=chat_pb2.ChatRequestNodeType.TEXT,
                text_node=chat_pb2.ChatRequestText(content=message),
            )
        )
        self.base_request.chat_history.append(
            chat_pb2.Exchange(request_nodes=list(request.nodes))
        )
        return request

    def _add_tool_response(
        self, tool_use_id: str, tool_response: str
    ) -> chat_pb2.ChatRequest:
        request = chat_pb2.ChatRequest()
        request.CopyFrom(self.base_request)
        request.nodes.append(
            chat_pb2.ChatRequestNode(
                type=chat_pb2.ChatRequestNodeType.TOOL_RESULT,
                tool_result_node=chat_pb2.ChatRequestToolResult(
                    tool_use_id=tool_use_id,
                    content=tool_response,
                    is_error=False,
                ),
            )
        )

        self.base_request.chat_history.append(
            chat_pb2.Exchange(request_nodes=list(request.nodes))
        )
        return request

    def _add_chat_result(self, chat_result: ChatResult) -> None:
        chat_result_pb2 = chat_result.to_chat_response_proto()
        # NOTE(arun): For reasons, we don't support certain chat result nodes in
        # the chat history. Instead of using an allow-list, use a deny-list to ensure
        # new node types are included by default.
        unsupported_node_types = [
            chat_pb2.ChatResultNodeType.SUGGESTED_QUESTIONS,
            chat_pb2.ChatResultNodeType.MAIN_TEXT_FINISHED,
            chat_pb2.ChatResultNodeType.WORKSPACE_FILE_CHUNKS,
            chat_pb2.ChatResultNodeType.RELEVANT_SOURCES,
        ]
        self.base_request.chat_history[-1].response_nodes.extend(
            [
                node
                for node in chat_result_pb2.nodes
                if node.type not in unsupported_node_types
            ]
        )

    def send_message(self, message: str) -> ChatResult:
        chat_request = self._add_message(message)
        chat_result, _, _ = run_chat(self._chat_handler, chat_request)
        self._add_chat_result(chat_result)
        return chat_result

    def send_tool_response(self, tool_use_id: str, tool_response: str) -> ChatResult:
        chat_request = self._add_tool_response(tool_use_id, tool_response)
        chat_result, _, _ = run_chat(self._chat_handler, chat_request)
        self._add_chat_result(chat_result)
        return chat_result

    def print_chat_history(self):
        for exchange in self.base_request.chat_history:
            for node in exchange.request_nodes:
                if node.type == chat_pb2.ChatRequestNodeType.TEXT:
                    print("User:", node.text_node.content)
                elif node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT:
                    print(
                        "Tool Result:",
                        node.tool_result_node.tool_use_id,
                        node.tool_result_node.content,
                    )
                else:
                    print("Unknown request node type:", node.type)
            for node in exchange.response_nodes:
                if node.type == chat_pb2.ChatResultNodeType.RAW_RESPONSE:
                    print("Assistant:", node.content)
                elif node.type == chat_pb2.ChatResultNodeType.TOOL_USE:
                    print(
                        "Tool Use:",
                        node.tool_use.tool_use_id,
                        node.tool_use.tool_name,
                        node.tool_use.input_json,
                    )
                else:
                    print("Unknown response node type:", node.type)

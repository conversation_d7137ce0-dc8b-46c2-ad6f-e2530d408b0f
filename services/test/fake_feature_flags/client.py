"""Client to update fake feature flags."""

import json
import time
from typing import Generator
import typing

import requests


class FakeFeatureFlagsClient:
    """Client to update fake feature flags."""

    def __init__(self, url: str):
        self.url = url

    def update(self, key: str, value: str, wait: int = 0):
        """Update a flag.

        Args:
            key: The key of the flag to update.
            value: The value to set the flag to as JSON.
            wait: The number of seconds to wait before returning.
        """

        v = json.loads(value)
        data = {"key": key, "value": v}
        data = json.dumps(data)
        response = requests.post(f"{self.url}/update", data=data, timeout=10)
        response.raise_for_status()
        if wait > 0:
            time.sleep(wait)

    def clear(self, wait: int = 0):
        """Clear all flags."""
        response = requests.post(f"{self.url}/clear", timeout=10)
        response.raise_for_status()
        if wait > 0:
            time.sleep(wait)

    def stream(self, timeout: int) -> typing.Generator[typing.Any, None, None]:
        """Stream all flags.

        This function is useful to see the current state of the flags.

        Returns:
            A generator of flags.
        """
        response = requests.get(f"{self.url}/all", stream=True, timeout=timeout)
        response.raise_for_status()
        mode = None
        for line in response.iter_lines():
            if line == b"event: put":
                mode = "put"
            elif line == b"event: patch":
                mode = "patch"
            elif mode == "put":
                assert line.startswith(b"data: ")
                data = line[len("data: ") :]
                d = json.loads(data)
                yield d
                mode = None
            elif mode == "patch":
                assert line.startswith(b"data: ")
                data = line[len("data: ") :]
                d = json.loads(data)
                yield d
                mode = None

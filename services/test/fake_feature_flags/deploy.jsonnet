local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  assert env == 'DEV' : 'only deployable in dev';

  local appName = 'fake-feature-flags';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: appName + '-svc',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      ports: [
        {
          name: 'http',
          port: 80,
          targetPort: 8080,
        },
      ],
      selector: {
        app: appName,
      },
    },
  };
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=false, abandon=true);

  local role = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'Role',
    metadata: {
      name: appName + '-role',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    rules: [
      {
        apiGroups: [
          '',
        ],
        resources: [
          'configmaps',
        ],
        verbs: [
          'get',
          'create',
          'update',
        ],
      },
    ],
  };
  local config = {
    defaults: namespace_config.flags.extraFakeFeatureFlags,
  };
  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);
  local roleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: appName + '-role-binding',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      kind: 'Role',
      name: appName + '-role',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
        namespace: namespace,
      },
    ],
  };
  local container =
    {
      name: appName,
      target: {
        name: '//services/test/fake_feature_flags:image',
        dst: appName,
      },
      args: [
        '--config-map-name',
        'fake-feature-flags-config',
        '--config',
        configMap.filename,
      ],
      env: lib.flatten([
        telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
      ]),
      volumeMounts: [
        configMap.volumeMountDef,
      ],
      ports: [
        {
          containerPort: 8080,
          name: 'http-svc',
        },
      ],
      resources: {
        limits: {
          cpu: 0.2,
          memory: '512Mi',
        },
      },
    };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    service,
    deployment,
    serviceAccount.objects,
    role,
    configMap.objects,
    roleBinding,
  ])

# Fake Feature Flags

This service provides a simple fake feature flag service for testing. It is intended to be used in conjunction with the `base.feature_flags` library.

This is not a production level feature flag service. It is intended to be used for testing.

See `examples/server` for examples of how to use integrate this into a service.

This allow fully hermetic testing of feature flags.

## Usage

### Setup

Set the namespace feature flag service to use the fake service:

```
override('useFakeFeatureFlags', true)
```

in your namespace config

### Update a flag

```sh
bazel run //services/test/fake_feature_flags:util -- update --key my_flag --json 'true'
```

`--json` will be directly parsed with `json.loads` and used as the value for the
key. This means setting string values requires passing double quotes as part of
the value:
```
bazel run //services/test/fake_feature_flags:util -- update --key vscode_agent_mode_min_version --json '"999.0.0"'
```

### Clear all flags

```sh
bazel run //services/test/fake_feature_flags:util -- clear
```

### Usage in E2E test

e.e. when part of the api E2E test:

```python
def test_with_feature_flag(
    augment_client: AugmentClient,
    feature_flags_client: FakeFeatureFlagsClient | None,
):
    if feature_flags_client is None:
        pytest.skip("Feature flags not enabled")
    feature_flags_client.update("some_flag", "false")

    # run command and observe behaviour
```

The feature flags are reset after each test.

See test `test_search_external_sources_disabled` as example

## Alternative Designs

Instead of a fake feature flag server, we could create a similar RPC service that updates LaunchDarkly to add/remove targetting of a feature flag variation to a namespace. The service would only update the "test" environment in LaunchDarkly. That approach has a couple downsides:

- it needs to co-ordinate its definition of variations with feature_flags/flags.jsonnet
- it needs to authenticate those requesting updates to make sure they are Augment developers or automated Augment software the rules could get pretty verbose
- Things like clear-ing all flags for a namespace would involve a lot of updates need to see if the rules for test get sent to production machines too

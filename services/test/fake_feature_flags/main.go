package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/augmentcode/augment/base/logging"
	"github.com/rs/zerolog/log"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// a simple representation of the LaunchDarkly object model
type Target struct {
	Values    []string `json:"values"`
	Variation int      `json:"variation"`
}

// a simple representation of the LaunchDarkly object model
//
// don't modify any Flags object after it has been created,
// but create a copy
type Flag struct {
	Key           string        `json:"key"`
	On            bool          `json:"on"`
	Version       int           `json:"version"`
	Targets       []Target      `json:"targets"`
	Variation     interface{}   `json:"variations"`
	OffVariation  int           `json:"offVariation"`
	Value         interface{}   `json:"value"`
	Rules         []interface{} `json:"rules"`
	Prerequisites []interface{} `json:"prerequisites"`
	Fallthrough   interface{}   `json:"fallthrough"`
	Salt          string        `json:"salt"`
}

// FlagStore is a simple in-memory store of flags
//
// It will load and store flags to a ConfigMap
type FlagStore struct {
	// clientset is the Kubernetes client
	clientset *kubernetes.Clientset

	// configMapName is the name of the ConfigMap to use
	configMapName string

	// namespace is the namespace to use
	namespace string

	// mu is a lock for the flags
	mu sync.RWMutex

	// flags is the map of flags
	flags map[string]*Flag

	defaults map[string]interface{}
}

// NewFlagStore creates a new FlagStore
func NewFlagStore(clientset *kubernetes.Clientset, configMapName string, namespace string, defaults map[string]interface{}) *FlagStore {
	return &FlagStore{
		clientset:     clientset,
		configMapName: configMapName,
		namespace:     namespace,
		mu:            sync.RWMutex{}, flags: getDefaultFlags(defaults),
		defaults: defaults,
	}
}

func getDefaultFlags(defaults map[string]interface{}) map[string]*Flag {
	r := make(map[string]*Flag)
	for k, v := range defaults {
		flag := getFlag(k, v)
		if flag == nil {
			continue
		}
		r[k] = flag
	}
	return r
}

// Get returns the flag with the given key
func (f *FlagStore) Get(ctx context.Context, key string) (*Flag, bool) {
	f.mu.RLock()
	defer f.mu.RUnlock()
	flag, exists := f.flags[key]
	return flag, exists
}

// storeToConfigMap stores the flags to a ConfigMap
// should already have the lock
func (f *FlagStore) storeToConfigMap(ctx context.Context) error {
	if f.clientset == nil {
		return nil
	}
	flagsJSON, err := json.Marshal(f.flags)
	if err != nil {
		return fmt.Errorf("failed to marshal flags: %v", err)
	}

	cm := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name: f.configMapName,
		},
		Data: map[string]string{
			"flags": string(flagsJSON),
		},
	}

	_, err = f.clientset.CoreV1().ConfigMaps(f.namespace).Create(ctx, cm, metav1.CreateOptions{})
	if err != nil {
		// If the ConfigMap already exists, update it
		_, err = f.clientset.CoreV1().ConfigMaps(f.namespace).Update(ctx, cm, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("failed to update ConfigMap: %v", err)
		}
	}
	return nil
}

func (f *FlagStore) loadFromConfigMap(ctx context.Context) error {
	if f.clientset == nil {
		return nil
	}
	cm, err := f.clientset.CoreV1().ConfigMaps(f.namespace).Get(ctx, f.configMapName, metav1.GetOptions{})
	if err != nil {
		// ignore if the ConfigMap doesn't exist
		if err.Error() == "configmaps \""+f.configMapName+"\" not found" {
			log.Info().Msgf("ConfigMap %s not found, skipping load", f.configMapName)
			return nil
		}
		return fmt.Errorf("failed to get ConfigMap: %v", err)
	}

	f.mu.Lock()
	defer f.mu.Unlock()
	err = json.Unmarshal([]byte(cm.Data["flags"]), &f.flags)
	if err != nil {
		return fmt.Errorf("failed to unmarshal flags: %v", err)
	}
	log.Info().Msgf("Loaded flags from config map: %v", f.flags)
	return nil
}

func getFlag(key string, value interface{}) *Flag {
	if value == nil {
		return nil
	}
	// Serve the flag in "Off" mode which will serve the "Off variation." described in the flag.
	// The "off variation" can be different from the "fallback value" - which is the value hard-coded into
	// the server in case LaunchDarkly is not used. The "fallthrough variation" is what is returned if the flag
	// is On and none of the rules matches.
	//
	// also see https://docs.launchdarkly.com/sdk/concepts/flag-evaluation-rules
	flag := &Flag{}
	flag.Key = key
	flag.On = false
	// milliseconds since epoch
	flag.Version = int(time.Now().UnixNano() / 1000000)
	flag.Value = value
	flag.OffVariation = 0
	flag.Targets = []Target{}
	flag.Variation = []interface{}{value}
	flag.Rules = []interface{}{}
	flag.Prerequisites = []interface{}{}
	flag.Fallthrough = map[string]interface{}{"variation": 0}
	flag.Salt = "00000000000000000000000000000000"
	return flag
}

func (f *FlagStore) Update(ctx context.Context, key string, value interface{}) (bool, error) {
	f.mu.Lock()
	defer f.mu.Unlock()
	_, exists := f.flags[key]
	newFlag := getFlag(key, value)
	f.flags[key] = newFlag
	err := f.storeToConfigMap(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error storing flags to config map")
		return false, err
	}
	return exists, nil
}

func (f *FlagStore) Clear(ctx context.Context) {
	f.mu.Lock()
	defer f.mu.Unlock()
	f.flags = getDefaultFlags(f.defaults)
	err := f.storeToConfigMap(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error storing flags to config map")
	}
}

type Client chan string

func handleNotFound(w http.ResponseWriter, req *http.Request) {
	log.Warn().Msgf("Not found: %s", req.URL.Path)
	w.WriteHeader(http.StatusNotFound)
	w.Header().Set("Content-Type", "application/json")
	response := `{"error": "Endpoint not found"}`
	_, err := w.Write([]byte(response))
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
	}
}

func handleDiagnostic(w http.ResponseWriter, req *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Header().Set("Content-Type", "application/json")
	response := `{"status": "ok"}`
	_, err := w.Write([]byte(response))
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
	}
}

// handleBulk is a dummy handler for the bulk endpoint
// otherwised, the go client will print error messages on a regular basis
func handleBulk(w http.ResponseWriter, req *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("date", time.Now().Format(time.RFC1123Z))
	response := `{"status": "ok"}`
	_, err := w.Write([]byte(response))
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
	}
}

var (
	configFile = flag.String("config", "", "Path to config file")

	configMapName = flag.String("config-map-name", "", "Name of the config map to use")
	// skipLoad will not load flags from the config map, but they will still be stored to it
	skipLoad = flag.Bool("skip-load", false, "Skip loading flags from config map")
	port     = flag.Int("port", 8080, "Port to listen on")
)

type Config struct {
	Defaults map[string]interface{}
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	var flagStore *FlagStore

	if *configMapName == "" {
		flagStore = NewFlagStore(nil, "", "", config.Defaults)
		log.Fatal().Msg("Missing config map name")
	} else {
		kubeConfig, err := rest.InClusterConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating in-cluster config")
		}

		clientset, err := kubernetes.NewForConfig(kubeConfig)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating Kubernetes client")
		}

		namespace, exists := os.LookupEnv("POD_NAMESPACE")
		if !exists {
			log.Fatal().Msg("POD_NAMESPACE environment variable not set")
		}

		flagStore = NewFlagStore(clientset, *configMapName, namespace, config.Defaults)
	}

	handler := NewHandler(flagStore)
	if !*skipLoad {
		err = flagStore.loadFromConfigMap(context.Background())
		if err != nil {
			log.Fatal().Err(err).Msg("Error loading flags from config map")
		}
	}

	http.HandleFunc("GET /all", handler.handleStreaming)
	http.HandleFunc("POST /update", handler.handleUpdate)
	http.HandleFunc("POST /clear", handler.handleClear)
	// without it the go client, will print error messages on a regular
	http.HandleFunc("POST /diagnostic", handleDiagnostic)
	http.HandleFunc("POST /bulk", handleBulk)
	http.HandleFunc("/", handleNotFound)

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", *port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
		os.Exit(1)
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	// Create server
	server := &http.Server{}

	// Start server in a goroutine
	go func() {
		if err := server.Serve(lis); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting server")
		}
	}()

	// Wait for shutdown signal
	sig := <-sigChan
	log.Info().Msgf("Received signal %s, initiating graceful shutdown", sig)

	// Create shutdown context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Initiate graceful shutdown
	if err := server.Shutdown(ctx); err != nil {
		log.Error().Err(err).Msg("Error during server shutdown")
	}
	log.Info().Msg("Server has shut down gracefully")
}

type AllData struct {
	Flags    map[string]*Flag       `json:"flags"`
	Segments map[string]interface{} `json:"segments"`
}

type Event struct {
	Path string  `json:"path"`
	Data AllData `json:"data"`
}

type UpdateEvent struct {
	Path string `json:"path"`
	Data *Flag  `json:"data"`
}

type Handler struct {
	flagStore  *FlagStore
	clients    map[Client]struct{}
	clientsMux sync.Mutex
}

func NewHandler(flagStore *FlagStore) *Handler {
	return &Handler{
		flagStore:  flagStore,
		clients:    make(map[Client]struct{}),
		clientsMux: sync.Mutex{},
	}
}

func (h *Handler) handleStreaming(w http.ResponseWriter, r *http.Request) {
	log.Info().Msg("Streaming:")
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.Error(w, "Streaming unsupported!", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")

	client := make(Client, 128)
	h.clientsMux.Lock()
	h.clients[client] = struct{}{}
	h.clientsMux.Unlock()

	defer func() {
		h.clientsMux.Lock()
		delete(h.clients, client)
		h.clientsMux.Unlock()
	}()

	// Send initial state
	h.flagStore.mu.RLock()
	event := Event{Data: AllData{Flags: h.flagStore.flags, Segments: map[string]interface{}{}}, Path: "/"}
	msg, err := json.Marshal(&event)
	h.flagStore.mu.RUnlock()
	if err != nil {
		http.Error(w, "Error marshalling initial state", http.StatusInternalServerError)
		return
	}
	message := fmt.Sprintf("event: put\ndata: %s\n\n", msg)
	log.Info().Msgf("Sending: %s", message)
	fmt.Fprint(w, message)
	flusher.Flush()

	// Create a context that's cancelled when the client disconnects
	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()

	// Keep connection alive
	go func() {
		ticker := time.NewTicker(15 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				fmt.Fprintf(w, ":keepalive\n\n")
				flusher.Flush()
			case <-ctx.Done():
				return
			}
		}
	}()

	for {
		select {
		case msg := <-client:
			// sent all events to the clients
			fmt.Fprint(w, msg)
			flusher.Flush()
		case <-ctx.Done():
			log.Info().Msg("Client disconnected")
			return
		}
	}
}

func (h *Handler) handleUpdate(w http.ResponseWriter, r *http.Request) {
	var update struct {
		Key   string      `json:"key"`
		Value interface{} `json:"value"`
	}
	if err := json.NewDecoder(r.Body).Decode(&update); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	log.Info().Msgf("Updating flag %s to '%v'\n", update.Key, update.Value)

	_, err := h.flagStore.Update(r.Context(), update.Key, update.Value)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	var message string
	h.flagStore.mu.RLock()
	event := UpdateEvent{Data: h.flagStore.flags[update.Key], Path: fmt.Sprintf("/flags/%s", update.Key)}
	msg, err := json.Marshal(&event)
	h.flagStore.mu.RUnlock()
	if err != nil {
		http.Error(w, "Error marshalling initial state", http.StatusInternalServerError)
		return
	}
	message = fmt.Sprintf("event: patch\ndata: %s\n\n", msg)

	log.Info().Msgf("Sending: %s", message)
	h.clientsMux.Lock()
	for client := range h.clients {
		select {
		case client <- message:
		default:
			log.Warn().Msg("Client buffer full, dropping message")
			close(client)
			delete(h.clients, client)
		}
	}
	h.clientsMux.Unlock()

	w.WriteHeader(http.StatusOK)
}

func (h *Handler) handleClear(w http.ResponseWriter, r *http.Request) {
	log.Info().Msg("Clearing all flags")
	h.flagStore.Clear(r.Context())

	// Notify all clients
	h.flagStore.mu.RLock()
	event := Event{Data: AllData{Flags: h.flagStore.flags, Segments: map[string]interface{}{}}, Path: "/"}
	msg, err := json.Marshal(&event)
	h.flagStore.mu.RUnlock()
	if err != nil {
		http.Error(w, "Error marshalling initial state", http.StatusInternalServerError)
		return
	}
	message := fmt.Sprintf("event: put\ndata: %s\n\n", msg)

	log.Info().Msgf("Sending: %s", message)
	h.clientsMux.Lock()
	for client := range h.clients {
		client <- message
	}
	h.clientsMux.Unlock()

	w.WriteHeader(http.StatusOK)
}

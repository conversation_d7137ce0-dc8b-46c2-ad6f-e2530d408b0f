"""A small tool to issue requests against the fake feature flags API."""

import argparse
import contextlib
import pathlib
import typing
import logging

from base.cloud.k8s.kubectl import get_dev_namespace
from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.logging.console_logging import setup_console_logging
from services.test.fake_feature_flags.client import FakeFeatureFlagsClient


def do_update(client: FakeFeatureFlagsClient, args):
    """Update a flag."""
    client.update(args.key, args.json)
    logging.info("Updated flag %s", args.key)


def do_clear(client: FakeFeatureFlagsClient, args):
    """Clear all flags."""
    del args
    client.clear()
    logging.info("Cleared all flags")


def do_stream(client: FakeFeatureFlagsClient, args):
    """Stream all flags until timeout.

    This is useful to see the current state of the flags.
    It effectively blocks until the timeout.
    """
    for line in client.stream(args.timeout):
        logging.info(line)


@contextlib.contextmanager
def setup(
    args: argparse.Namespace,
) -> typing.Generator[FakeFeatureFlagsClient, None, None]:
    """Setup the client."""
    if args.url:
        yield FakeFeatureFlagsClient(url=args.url)
    else:
        kubectl_factory = create_kubectl_factory(args.kube_config_file)
        kubectl_instance = kubectl_factory("GCP_US_CENTRAL1_DEV")
        with kubectl_instance.port_forward(
            args.namespace,
            "deployment/fake-feature-flags",
            remote_port=8080,
            local_port=8080,
        ) as local_port:
            url = f"http://localhost:{local_port}"
            yield FakeFeatureFlagsClient(url)


def main():
    """Main function."""
    parser = argparse.ArgumentParser()
    # this assume you have a port-forward to the fake feature flags service
    parser.add_argument("--url", default="")
    parser.add_argument(
        "--kube-config-file",
        help="Kubeconfig to use",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube/config"),
    )
    parser.add_argument("--namespace", default=get_dev_namespace())

    subparsers = parser.add_subparsers()
    update_parser = subparsers.add_parser("update", help="Update a flag")
    update_parser.set_defaults(action=do_update)
    update_parser.add_argument("--key", required=True)
    update_parser.add_argument("--json", required=True)
    clear_parser = subparsers.add_parser("clear", help="Clear all flags")
    clear_parser.set_defaults(action=do_clear)
    stream_parser = subparsers.add_parser(
        "stream", help="Stream all flags until timeout"
    )
    stream_parser.set_defaults(action=do_stream)
    stream_parser.add_argument("--timeout", type=int, default=10)
    args = parser.parse_args()

    setup_console_logging()

    with setup(args) as client:
        args.action(client, args)


if __name__ == "__main__":
    main()

"""Integration tests for the agent.

This test covers the agent's behavior of formatting conversations as prompts to
Claude and Anthropic APIs.

When running locally, consider using `pytest.mark.only` to only run a single test and
speed things up.
"""

import json
import tempfile
from typing import Iterator
from unittest.mock import MagicMock, patch

import pytest
from google.cloud import secretmanager

from base.blob_names import blob_names_pb2
from services.agents.tool import Tool
from services.chat_host import chat_pb2
from services.chat_host.server.chat_handler_metrics import ChatHandlerMetrics
from services.chat_host.server.chat_third_party_handler import (
    Chat<PERSON>hirdPartyHandler,
    ChatThirdPartyHandlerConfig,
    ChatTokenApportionment,
    create_chat_third_party_handler,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.test.agent_integration_mock_tools import (
    CLIENT_TOOLS,
    MockGitHubAPITool,
    MockLinearSearchIssuesTool,
    MockWebSearchTool,
)
from services.test.agent_integration_test_utils import (
    Chat<PERSON><PERSON>er,
    assert_result_contains_text,
    assert_result_has_tool_call,
    run_chat,
)

PROJECT_ID = "1035750215372"  # this is system-services-dev
SECRET_ID = "bazel-runner-claude-api-key"
VERSION_ID = "1"


@pytest.fixture(scope="session")
def anthropic_api_key_path() -> Iterator[str]:
    """Get the Claude API key from the environment."""
    client = secretmanager.SecretManagerServiceClient()
    response = client.access_secret_version(
        name=f"projects/{PROJECT_ID}/secrets/{SECRET_ID}/versions/{VERSION_ID}"
    )
    api_key = response.payload.data.decode("UTF-8")
    with tempfile.NamedTemporaryFile(mode="w") as f:
        f.write(api_key)
        f.flush()
        yield f.name


@pytest.fixture(scope="session")
def agent_chat_handler(anthropic_api_key_path: str) -> ChatThirdPartyHandler:
    """Factory function for creating chat handler."""
    handler_config = ChatThirdPartyHandlerConfig(
        gcp_project_id="system-services-dev",
        gcp_region="us-east5",
        anthropic_api_key_path=anthropic_api_key_path,
        openai_api_key_path="",
        xai_api_key_path="",
        fireworks_api_key_path="",
        client_type="anthropic_direct",
        # TODO(arun): We really should get this from the deploy config.
        # Corresponds to 'claude-sonnet-3-5-200k-v2-direct-agent' in chatanol3_router_third_party_chat_deploy.jsonnet
        model_name="claude-3-5-sonnet-20241022",
        prompt_formatter_name="agent-binks-claude-v1",
        temperature=0,
        max_output_tokens=1024 * 8,
        token_apportionment=ChatTokenApportionment(
            prefix_len=1024 * 2,
            suffix_len=1024 * 2,
            path_len=256,
            message_len=-1,  # Deprecated field
            selected_code_len=-1,  # Deprecated field
            chat_history_len=1024 * 4,
            retrieval_len_per_each_user_guided_file=2000,
            retrieval_len_for_user_guided=3000,
            retrieval_len=-1,  # Fill the rest of the input prompt with retrievals
            max_prompt_len=1024 * 12,  # 12k for prompt
            inject_current_file_into_retrievals=True,
            retrieval_as_tool=True,
        ),
        generate_commit_message_token_apportionment=None,
        slackbot_message_token_apportionment=None,
    )

    return create_chat_third_party_handler(
        handler_config.to_dict(),  # type: ignore
        "test_namespace",
        MagicMock(RequestInsightPublisher),
        ChatHandlerMetrics(),
        content_manager_client=MagicMock(),
        retriever=MagicMock(),
    )


@pytest.fixture(scope="session")
def tools() -> dict[str, Tool]:
    tools = [
        MockWebSearchTool(),
        MockGitHubAPITool(),
        MockLinearSearchIssuesTool(),
    ]

    # TODO(arun): We should mock the client tools as well.

    return {tool.id: tool for tool in tools}


@pytest.fixture(scope="session")
def tool_definitions(tools: dict[str, Tool]):
    """Factory function for creating chat handler."""
    return [
        chat_pb2.ToolDefinition(
            name=tool.name,
            description=tool.description,
            input_schema_json=json.dumps(tool.input_schema),
        )
        for _, tool in tools.items()
    ] + CLIENT_TOOLS


def test_hello_world(agent_chat_handler: ChatThirdPartyHandler):
    """Test that the agent model can generate a hello world program."""
    chat_builder = ChatBuilder(agent_chat_handler)
    chat_result = chat_builder.send_message("Write a hello world program in Python")
    assert_result_contains_text(chat_result, "print")


def test_agent_calls_ls(
    agent_chat_handler: ChatThirdPartyHandler,
    tool_definitions: list[chat_pb2.ToolDefinition],
):
    """Test that the agent can call the ls tool."""
    chat_builder = ChatBuilder(agent_chat_handler)
    chat_builder.base_request.tool_definitions.extend(tool_definitions)

    chat_result = chat_builder.send_message(
        "What's the longest filename in this directory? "
        + "Use just the ls command, with no pipes, and answer baed on the response."
    )
    tool_use_id = assert_result_has_tool_call(
        chat_result,
        "shell",
        lambda args: args["command_type"] == "simple"
        and "ls" in args["simple_command"],
    )
    chat_result = chat_builder.send_tool_response(
        tool_use_id,
        """\
total 16
drwxr-xr-x  2 <USER> <GROUP> 4096 Oct 29 10:00 .
drwxr-xr-x 10 <USER> <GROUP> 4096 Oct 29 09:59 ..
-rw-r--r--  1 <USER> <GROUP>  107 Oct 29 10:00 .gitignore
-rw-r--r--  1 <USER> <GROUP>  107 Oct 29 10:00 a_long_long_file.txt
-rw-r--r--  1 <USER> <GROUP>  107 Oct 29 10:00 short_file.txt
""",
    )
    assert_result_contains_text(chat_result, "a_long_long_file.txt")
    # Print the chat history for debugging.
    chat_builder.print_chat_history()

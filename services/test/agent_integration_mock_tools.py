"""Mock tools for agent integration tests."""

import json

from services.chat_host import chat_pb2
from services.integrations.github.agent_tools.github_api_tool import GitHub<PERSON><PERSON>ool
from services.integrations.google_search.agent_tools.web_search_tool import (
    WebSearchTool,
)
from services.integrations.linear.agent_tools.linear_tools import (
    LinearSearchIssuesTool,
)

# TODO(arun): This is the list of tools we get on a typical query. It should NOT be
# hard-coded here in the tests, but the tool definitions are currently spread across
# multiple files in the client and backend. This is a temporary solution.
CLIENT_TOOLS = [
    chat_pb2.ToolDefinition(
        name="save-file",
        description="Save a file. Use this tool to create new files.  It cannot modify existing files.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path of the file to save.",
                    },
                    "file_content": {
                        "type": "string",
                        "description": "The content of the file to save.",
                    },
                    "add_last_line_newline": {
                        "type": "boolean",
                        "description": "Whether to add a newline at the end of the file (default: true).",
                    },
                },
                "required": ["file_path", "file_content"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="read-file",
        description="Read a file.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path of the file to read.",
                    }
                },
                "required": ["file_path"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="edit-file",
        description="""
Edit a file. Accepts a file path and a description of the edit.
This tool can edit whole files.
The description should be detailed and precise, and include all required information to perform the edit.
It can include both natural language and code. It can include multiple code snippets to described different
edits in the file. It can include descriptions of how to perform these edits precisely.

All the contents that should go in a file should be placed in a markdown code block, like this:

<begin-example>
Add a function called foo.

```
def foo():
    ...
```
</end-example>

This includes all contents, even if it's not code.

Be precise or I will take away your toys.

Prefer to use this tool when editing parts of a file.
""",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "The path of the file to edit.",
                    },
                    "edit_summary": {
                        "type": "string",
                        "description": "A brief description of the edit to be made. 1-2 sentences.",
                    },
                    "detailed_edit_description": {
                        "type": "string",
                        "description": "A detailed and precise description of the edit. Can include natural language and code snippets.",
                    },
                },
                "required": ["file_path", "edit_summary", "detailed_edit_description"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="remember",
        description="Call this tool when user asks you:\n- to remember something\n- to create memory/memories\n\nUse this tool only with information that can be useful in the long-term.\nDo not use this tool for temporary information.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "memory": {
                        "type": "string",
                        "description": "The concise (1 sentence) memory to remember.",
                    }
                },
                "required": ["memory"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="codebase-retrieval",
        description="Use this tool to request information from the codebase.\nIt will return relevant snippets for the requested information.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "information_request": {
                        "type": "string",
                        "description": "A description of the information you need.",
                    }
                },
                "required": ["information_request"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="launch-process",
        description="""\
Launch a new process with a shell command. A process can be waiting (`wait=true`) or non-waiting (`wait=false`, which is default).

If `wait=true`, launches the process in an interactive terminal, and waits for the process to complete up to
`wait_seconds` seconds (default: 60). If the process ends
during this period, the tool call returns. If the timeout expires, the process will continue running in the
background but the tool call will return. You can then interact with the process using the other process tools.

Note: Only one waiting process can be running at a time. If you try to launch a process with `wait=true`
while another is running, the tool will return an error.

If `wait=false`, launches a background process in a separate terminal. This returns immediately, while the
process keeps running in the background.

Notes:
- Use `wait=true` processes when the command is expected to be short, or when you can't
proceed with your task until the process is complete. Use `wait=false` for processes that are
expected to run in the background, such as starting a server you'll need to interact with, or a
long-running process that does not need to complete before proceeding with the task.
- If this tool returns while the process is still running, you can continue to interact with the process
using the other available tools. You can wait for the process, read from it, write to it, kill it, etc.
- You can use this tool to interact with the user's local version control system. Do not use the
retrieval tool for that purpose.
- If there is a more specific tool available that can perform the function, use that tool instead of
this one.

The OS is darwin. The shell is `bash`.""",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The shell command to execute.",
                    },
                    "wait": {
                        "type": "boolean",
                        "description": "Optional: whether to wait for the command to complete (default false).",
                    },
                    "wait_seconds": {
                        "type": "integer",
                        "description": "Optional: number of seconds to wait for the command to complete (default 60). Only relevant when wait=true.",
                    },
                    "cwd": {
                        "type": "string",
                        "description": "Working directory for the command. If not supplied, uses the current working directory.",
                    },
                },
                "required": ["command_type"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="kill-process",
        description="Kill a process by its process ID.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "process_id": {
                        "type": "integer",
                        "description": "Process ID to kill.",
                    }
                },
                "required": ["process_id"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="read-process",
        description="Read output from a running process.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "process_id": {
                        "type": "integer",
                        "description": "Process ID to read from.",
                    }
                },
                "required": ["process_id"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="write-process",
        description="Write input to a process's stdin.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "process_id": {
                        "type": "integer",
                        "description": "Process ID to write to.",
                    },
                    "input_text": {
                        "type": "string",
                        "description": "Text to write to the process's stdin.",
                    },
                },
                "required": ["process_id", "input_text"],
            }
        ),
    ),
    chat_pb2.ToolDefinition(
        name="list-processes",
        description="List all known processes and their states.",
        input_schema_json=json.dumps(
            {"type": "object", "properties": {}, "required": []}
        ),
    ),
    chat_pb2.ToolDefinition(
        name="shell",
        description="Execute system commands with validation for simple commands and support for complex pipelines.\nThe OS is linux. For complex pipelines, the shell is 'bash'.",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "command_type": {
                        "type": "string",
                        "enum": ["simple", "complex"],
                        "description": "Whether this is a simple command or a complex pipeline",
                    },
                    "simple_command": {
                        "type": "array",
                        "description": "Single executable without pipes/redirections/shell operators",
                        "pattern": "^[^|><;&`$]+$",
                    },
                    "complex_command": {
                        "type": "string",
                        "description": "Full command string including pipes and redirections",
                    },
                },
                "required": ["command_type"],
            }
        ),
    ),
]


class MockWebSearchTool(WebSearchTool):
    """Mock web search tool."""

    def __init__(self):
        # Don't call super() because it calls get_search_credentials()
        pass


class MockGitHubAPITool(GitHubAPITool):
    """Mock GitHub API tool."""

    def __init__(self):
        # Don't call super() because it calls get_search_credentials()
        pass


class MockLinearSearchIssuesTool(LinearSearchIssuesTool):
    """Mock Linear search issues tool."""

    def __init__(self):
        # Don't call super() because it calls get_search_credentials()
        pass

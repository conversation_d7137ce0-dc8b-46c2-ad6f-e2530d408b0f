"""conftest.py is a special file that pytest will automatically load."""

import pytest
from google.cloud import secretmanager

PROJECT_ID = "1035750215372"  # this is system-services-dev
SECRET_ID = "bazel-runner-dogfood-shard-api-key"
VERSION_ID = "1"


def get_api_key_from_secret_manager() -> str:
    """Get the api key from the secret manager."""
    client = secretmanager.SecretManagerServiceClient()

    # Build the resource name of the secret version
    name = f"projects/{PROJECT_ID}/secrets/{SECRET_ID}/versions/{VERSION_ID}"

    # Access the secret version
    response = client.access_secret_version(request={"name": name})

    # Return the payload as a string
    key = response.payload.data.decode("UTF-8")
    assert key
    return key


def pytest_addoption(parser):
    parser.addoption(
        "--api_key",
        action="store",
        default=None,
        help="the path to the Augment api key file",
    )
    parser.addoption(
        "--endpoint",
        action="store",
        default="https://staging-shard-0.api.augmentcode.com",
        help="the endpoint to use for the API calls",
    )


@pytest.fixture(scope="session")
def endpoint(request):
    """Return the endpoint to use for the API calls."""
    return request.config.getoption("--endpoint")


@pytest.fixture(scope="session")
def api_key(request) -> str:
    """Return the api key to use for the API calls."""
    key = request.config.getoption("--api_key")
    if key is None:
        key = get_api_key_from_secret_manager()
    return key

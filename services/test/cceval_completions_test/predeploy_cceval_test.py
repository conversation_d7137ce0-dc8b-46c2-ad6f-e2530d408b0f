"""
This test is designed to run before deployment. It checks to ensure that the staging model does
not significantly underperform the baseline.
"""

import datetime
from datetime import datetime as dt
import csv
import io
from base.augment_client.remote_lib import (
    AugmentClientConfig,
    RemoteCompletionConfig,
    RemoteRetrieverConfig,
)

from services.test.cceval_completions_test.cceval import CCEval

from google.cloud import storage


def test_predeploy_cceval(api_key, endpoint):
    """Runs a shortened version of the CCEval test before deployment."""

    gcp_bucket = "augment-cceval-data"

    score_archive = "cceval_scores.csv"

    client = storage.Client()
    bucket = client.bucket(gcp_bucket)

    if not bucket.get_blob(score_archive):
        blob = bucket.blob(score_archive)

        blob.upload_from_string("datetime,score")

    augment_client_config = AugmentClientConfig(
        url=endpoint,
        api_token=api_key,
        timeout=60,
        retry_count=10,
        retry_sleep=10.0,
    )

    remote_completion_config = RemoteCompletionConfig(
        retry_count=16,
        retry_sleep_secs=10.0,
    )
    remote_retriever_config = RemoteRetrieverConfig(
        wait_indexing_retry_sleep_secs=10.0,
        wait_indexing_retry_count=16,
    )

    cceval = CCEval(limit=1000)

    current_time = dt.now(datetime.timezone.utc)

    # Run the default model and tokenizer
    results = cceval.run(
        augment_client_config,
        remote_retriever_config,
        remote_completion_config,
        "",
    )

    score_blob = bucket.get_blob(score_archive)
    assert score_blob is not None

    score_csv = score_blob.download_as_bytes()
    score_csv_str = score_csv.decode("utf-8")

    with io.StringIO(score_csv_str) as score_csv_file:
        reader = csv.DictReader(score_csv_file)

        # Get the best score from the past week
        scores = [
            float(row["score"])
            for row in reader
            if dt.strptime(row["datetime"], "%Y-%m-%d %H:%M:%S").astimezone()
            + datetime.timedelta(days=7)
            > current_time.astimezone()
        ]

    best_score = max(scores) if len(scores) > 0 else 0.0

    current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
    score_csv_str += (
        f"\n{current_time_str},{results['metrics']['total']['exact_match_strict']}"
    )

    score_blob.upload_from_string(score_csv_str)

    # Pass if scores have not regressed by more than 5% over the last week's best results
    assert results["metrics"]["total"]["exact_match_strict"] > best_score - 0.05

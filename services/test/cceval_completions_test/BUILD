load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

py_library(
    name = "cceval",
    srcs = ["cceval.py"],
    visibility = [
        "//services/test/cceval_completions_test:__subpackages__",
    ],
    deps = [
        ":utils",
        "//base/augment_client:client",
        "//base/augment_client:remote_completion_system",
        "//base/augment_client:remote_lib",
        "//base/blob_names/python:blob_names",
        "//base/languages:unit_test_guesser",
        "//base/static_analysis:parsing",
        requirement("google-cloud-storage"),
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "utils",
    srcs = ["utils.py"],
    visibility = [
        "//services/test/cceval_completions_test:__subpackages__",
    ],
)

pytest_test(
    name = "predeploy_cceval_test",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "predeploy_cceval_test.py",
    ],
    tags = [
        "exclusive",
        "manual",
    ],
    deps = [
        ":cceval",
        requirement("google-cloud-storage"),
        requirement("google-cloud-secret-manager"),
    ],
)

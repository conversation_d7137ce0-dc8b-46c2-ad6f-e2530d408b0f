"""The CCEval Task.

A benchmark of code generation tasks where the code the complete includes an api
defined in an external file. It includes patch sites for ~1000 repos across 4
different languages: C#, Java, Python, and TypeScript.

This task is adapted from that described in the paper: CROSSCODEEVAL: A Diverse
and Multilingual Benchmark for Cross-File Code Completion
https://arxiv.org/pdf/2310.11248.pdf
"""

import logging
import random
import time
from collections import defaultdict
from dataclasses import asdict, dataclass
from dataclasses_json import dataclass_json
from pathlib import PurePath
from typing import Iterable, NamedTuple, Optional

from base.blob_names.python.blob_names import get_blob_name
from base.augment_client.client import UploadContent
from base.augment_client.remote_lib import (
    AugmentClientConfig,
    RemoteCompletionConfig,
    RemoteRetrieverConfig,
    RemoteCompletionInput,
    RemoteCompletionResult,
)
from base.augment_client.remote_completion_system import RemoteCompletionSystem

from services.test.cceval_completions_test.utils import (
    reads_jsonl,
    get_first_n_lines,
    compute_exact_match,
)

from google.cloud import storage

logger = logging.getLogger("tasks.cceval")


DocsType = list[UploadContent]


@dataclass
class CCEvalScore:
    """The metrics of the EM Task should look like the output of HumanEval."""

    exact_match: float
    """Exact match after normalizing on end-of-statement criteria."""

    exact_match_strict: float
    """Line-by-line exact match score with minimal post-processing."""

    exact_match_1_line: float
    """Exact match after truncating to one line."""

    samples: int
    """The number of samples run for this eval."""


@dataclass
class CCEvalOutput:
    """The output of the CCEval Task.

    One such record will be produced per evaluated patch.
    """

    prefix: str
    """The prefix string for the task."""

    suffix: str
    """The suffix string for the task."""

    prompt: str
    """The actual prompt used for generation.

    The prefix, suffix, and chunks may be changed to form the actual prompt,
    based on budget constraints.
    """

    generation: str
    """The full output text from generation."""

    ground_truth: str
    """The ground truth text."""

    repository: str
    """The repository name from which this task originates."""

    metrics: CCEvalScore
    """The scores for this completions."""


@dataclass_json
@dataclass
class CCEvalInput:
    r"""Input type.

    Example format:
    {"prompt": "// Copyright (c) ... } else",
     "groundtruth": " if (Date.now() > start + defaultTimeout * 0.9) {",
     "right_context": "\n            throw new Error(`Timed out ...    });\n}\n",
     "metadata": {
        "task_id": "project_cc_typescript/4267",
        "repository": "Azure/azure-functions-nodejs-e2e-tests",
        "file": "src/global.test.ts",
        "context_start_lineno": 0,
        "groundtruth_start_lineno": 69,
        "right_context_start_lineno": 70
        }
    }
    """

    @dataclass_json
    @dataclass
    class Meta:
        """Metadata section of the input record."""

        task_id: str
        repository: str
        file: str
        context_start_lineno: int
        groundtruth_start_lineno: int
        right_context_start_lineno: int

    prompt: str
    """The prompt prefix."""

    right_context: str
    """The prompt suffix"""

    groundtruth: str
    """The groundtruth."""

    metadata: Meta
    """Metadata section"""


def get_bracket_lang_statement(completion):
    end_idx = None
    for i in range(len(completion)):
        if completion[i] in [";", "}", "{"]:
            end_idx = i
            break
    return completion[: end_idx + 1] if end_idx else completion


def postprocess_code_lines(completion: str, lang: str) -> str:
    if lang in ["java", "csharp", "typescript"]:
        line = get_bracket_lang_statement(completion)
        return " ".join(line.split())
    elif lang == "python":
        # NOTE: the original test suite used a complicated procedure for
        # normalizing the python code. But taking the first line was equivalent
        # and much simpler. If we extend this dataset to include multi-line
        # completions for python, this logic will need to change.
        return completion.splitlines()[0] if completion else completion
    else:
        raise ValueError(f"Unknown language {lang}")


def extract_lang_from_rec(rec: CCEvalInput) -> str:
    """Extract the language key from the task.

    The language key is encoded in the task-id.
    """
    return rec.metadata.task_id.split("project_cc_")[1].split("/")[0]


InternalResultT = tuple[
    CCEvalInput, RemoteCompletionInput, RemoteCompletionResult, CCEvalScore
]


class _PerRepoResult(NamedTuple):
    rec: CCEvalInput
    mi: RemoteCompletionInput
    result: RemoteCompletionResult


def _prepare_dataset(gcp_bucket: str):
    """Returns the dataset and prompts in a deterministic order.

    Returns a tuple of ('prompts', 'documents'), where 'prompts' maps repo names
    to a list of input prompts, and 'documents' maps repo names to a list of
    documents for retrieval.
    """

    all_patches_by_repo: dict[str, list[CCEvalInput]] = {}
    all_documents_by_repo: dict[str, list[UploadContent]] = {}

    logger.info(f"Load data from {gcp_bucket}")

    client = storage.Client()
    bucket = client.bucket(gcp_bucket)

    for blob in bucket.list_blobs(match_glob="**/*_patches.jsonl"):
        file = reads_jsonl(blob.download_as_bytes())

        patches = [CCEvalInput.from_dict(x) for x in file]  # type: ignore

        all_repo_names = [patch.metadata.repository for patch in patches]
        org_repo = blob.name.rsplit("_patches.jsonl")[0]
        assert set(all_repo_names) == set(
            [org_repo]
        ), "The dataset should only contain one repo"

        docs_filename = blob.name.replace("_patches.jsonl", "_retrieval_db.jsonl")

        docs_blob = bucket.get_blob(docs_filename)
        assert docs_blob is not None

        documents = reads_jsonl(docs_blob.download_as_bytes())
        documents = [UploadContent(doc["text"], doc["path"]) for doc in documents]

        all_patches_by_repo.update({org_repo: patches})
        all_documents_by_repo.update({org_repo: documents})

    # Ensure a consistent ordering of the repos by a deterministic shuffle.
    # People may be running this task for a limited number of patches, so
    # changing the order will affect their results.
    random.seed(42)
    ordered_repo_names = sorted(all_patches_by_repo.keys())
    random.shuffle(ordered_repo_names)

    # Note that insertion order is preserved in Python 3.7+
    all_patches_by_repo = {
        repo: all_patches_by_repo[repo] for repo in ordered_repo_names
    }
    all_documents_by_repo = {
        repo: all_documents_by_repo[repo] for repo in ordered_repo_names
    }

    # Filter out files in .git directories
    for repo in all_documents_by_repo:
        orig_docs = all_documents_by_repo[repo]
        documents = _filter_out_git_files(orig_docs)
        count = len(orig_docs) - len(documents)
        logger.info(
            f"Repo {repo}: filtered {count} .git files from {len(orig_docs)} docs, {len(documents)} remaining."
        )
        all_documents_by_repo[repo] = documents

    return all_patches_by_repo, all_documents_by_repo


def _filter_out_git_files(documents: Iterable[UploadContent]) -> list[UploadContent]:
    """Filter out those documents in .git directories.

    Note the order of documents is preserved.
    """
    # Returns list of documents without .git directories
    docs_without_git = []
    for doc in documents:
        logger.debug(f"Document has path {doc.path_name}")
        assert doc.path_name is not None
        if ".git" in PurePath(doc.path_name).parts:
            logger.debug(f"Skipping {doc.path_name}")
            continue
        docs_without_git.append(doc)

    return docs_without_git


class CCEval:
    """The CCEval Task.

    Given a set of patches and a retrieval database, generate a set of documents.
    Score the generation using exact match and edit similarity.
    """

    def __init__(
        self,
        limit: Optional[int] = None,
        gcp_bucket: str = "augment-cceval-data",
        result_labels: Optional[dict[str, str]] = None,
    ):
        self.limit = limit
        self.result_labels = result_labels
        all_patches_by_repo, all_documents_by_repo = _prepare_dataset(gcp_bucket)

        # Flatten the patches into a single list, to simplify applying limits
        all_patches = [
            (repo, rec) for repo, recs in all_patches_by_repo.items() for rec in recs
        ]
        self.all_patches_by_repo = all_patches_by_repo
        self.all_documents_by_repo = all_documents_by_repo
        logger.info(f"Loaded {len(all_patches)} patches in total.")
        if self.limit is not None:
            all_patches = all_patches[: self.limit]
            logger.info(
                f"Kept {len(all_patches)} patches after the limit={self.limit} filter."
            )
        self.all_patches = all_patches

    def __getitem__(self, index: int) -> tuple[RemoteCompletionInput, DocsType]:
        """Get the index-th example in this task."""
        # TODO(Xuanyi): returns exactly what should be passed to system.
        logger.warning(
            "CCEval's __getitem__ is not returning the same documents and retrieval chunks as run."
            " You need to process the doc_ids outside of this function to get the same results."
        )
        repo, rec = self.all_patches[index]
        documents = self.all_documents_by_repo[repo]
        model_input: RemoteCompletionInput = self.create_model_input(rec)
        return model_input, documents

    def __len__(self) -> int:
        """Return the total number of examples in this task."""
        return len(self.all_patches)

    def run(
        self,
        augment_client_config: AugmentClientConfig,
        retriever_config: RemoteRetrieverConfig,
        completion_config: RemoteCompletionConfig,
        model_name: str,
    ) -> dict:
        """Run the task with the given system and save the results into output_path/output_prefix_xxx."""

        system = RemoteCompletionSystem(
            augment_client_config,
            retriever_config,
            completion_config,
            model_name,
        )

        system.load()

        start_timestamp = time.time()
        start_time = time.monotonic()

        all_patches = self.all_patches

        per_repo_results = defaultdict[str, list[_PerRepoResult]](list)
        documents: list[UploadContent] = []
        last_repo = None
        for idx, (repo, rec) in enumerate(all_patches):
            if last_repo != repo:
                logger.info(f"Indexing repo {repo}...")
                # TODO(rich): Consider having a retriever context that automatically
                # clears the retriever, since it's error prone to remember to clear
                # the retriever on every loop.
                documents = self.all_documents_by_repo[repo]
                system.clear_retriever()
                system.add_docs(documents)
                last_repo = repo

            # Add modified prompt document to the system
            logger.debug(f"Processing patch with path {rec.metadata.file}")
            new_content = rec.prompt + rec.right_context
            new_doc = UploadContent(content=new_content, path_name=rec.metadata.file)
            system.add_docs([new_doc])

            # Replace the original document with our modified prompt doc in the
            # list of documents to retrieve from
            new_doc_ids = {
                get_blob_name(x.path_name, x.content.encode("utf8"))
                for x in documents
                if x.path_name != new_doc.path_name
            }
            new_doc_ids.add(
                get_blob_name(new_doc.path_name, new_doc.content.encode("utf8"))
            )
            assert len(new_doc_ids) == len(documents)
            # Ensure that we're including only those doc ids that have been
            # added and not filtered due to size.
            new_doc_ids.intersection_update(system.get_doc_ids())

            # Specify the doc list from which to retrieve
            model_input: RemoteCompletionInput = self.create_model_input(rec)

            result: RemoteCompletionResult = system.generate(
                model_input, list(new_doc_ids)
            )

            out = _PerRepoResult(rec, model_input, result)
            per_repo_results[repo].append(out)
            # Logging the progress
            logger.info(f"Processed {idx+1}/{len(all_patches)} patches")

        per_repo_scores: dict[str, list[InternalResultT]] = {}
        for repo in per_repo_results:
            all_results: list[InternalResultT] = []
            for rec, mi, result in per_repo_results[repo]:
                assert rec.groundtruth is not None

                score_em_strict = compute_exact_match(
                    str(rec.groundtruth), [result.generated_text]
                )

                lang = extract_lang_from_rec(rec)
                norm_generated = postprocess_code_lines(result.generated_text, lang)
                norm_gt = postprocess_code_lines(rec.groundtruth, lang)
                score_em = norm_generated == norm_gt
                score_em_1_line = get_first_n_lines(norm_generated, n=1).rstrip(
                    "\n"
                ) == (get_first_n_lines(norm_gt, n=1).rstrip("\n"))

                score = CCEvalScore(
                    exact_match=score_em,
                    exact_match_strict=score_em_strict,
                    exact_match_1_line=score_em_1_line,
                    samples=1,
                )
                all_results.append((rec, mi, result, score))

            per_repo_scores[repo] = all_results
            all_results = []

        per_lang_scores: dict[str, list[CCEvalScore]] = {}
        for repo in per_repo_scores:
            for rec, _, _, score in per_repo_scores[repo]:
                lang = extract_lang_from_rec(rec)
                if lang not in per_lang_scores:
                    per_lang_scores[lang] = []
                per_lang_scores[lang].append(score)

        def calc_avg(scores: list[float]):
            return sum(scores) / len(scores)

        def calc_avg_scores(scores: list[CCEvalScore]) -> CCEvalScore:
            return CCEvalScore(
                exact_match=calc_avg([x.exact_match for x in scores]),
                exact_match_strict=calc_avg([x.exact_match_strict for x in scores]),
                exact_match_1_line=calc_avg([x.exact_match_1_line for x in scores]),
                samples=len(scores),
            )

        # Aggregate scores per language
        agg_scores_per_language = {}
        for lang in per_lang_scores:
            agg_scores_per_language[lang] = calc_avg_scores(per_lang_scores[lang])

        def formatted_score(lang, score: CCEvalScore) -> str:
            return " ".join(
                [
                    f" {lang:<10}",
                    f"em={score.exact_match:.3f}",
                    f"em_strict={score.exact_match_strict:.3f}",
                ]
            )

        # Log aggregated scores per language
        logger.info("Scores")
        for lang in sorted(agg_scores_per_language.keys()):
            logger.info(formatted_score(lang, agg_scores_per_language[lang]))

        total = []
        for lang in per_lang_scores:
            total.extend(per_lang_scores[lang])
        total_score = calc_avg_scores(total)
        logger.info(formatted_score("Total", total_score))

        output_results = list[CCEvalOutput]()
        for repo in per_repo_scores:
            for rec, mi, result, score in per_repo_scores[repo]:
                assert rec.groundtruth is not None

                output_results.append(
                    CCEvalOutput(
                        prefix=mi.prefix,
                        suffix=mi.suffix,
                        prompt=mi.prefix,
                        generation=result.generated_text,
                        ground_truth=rec.groundtruth,
                        repository=rec.metadata.repository,
                        metrics=score,
                    )
                )

        end_time = time.monotonic()

        return {
            "metrics": {
                "per_language": {
                    lang: asdict(score)
                    for lang, score in agg_scores_per_language.items()
                },
                "total": asdict(total_score),
            },
            "limit": self.limit,
            "start_time": start_timestamp,
            "run_duration": end_time - start_time,
            "labels": self.result_labels if self.result_labels else {},
        }

    @classmethod
    def from_yaml_config(cls, config: dict) -> "CCEval":
        """Returns a Task object constructed using a config dictionary."""
        limit: Optional[int] = config.get("limit", None)
        result_labels: Optional[dict[str, str]] = config.get("result_labels", None)
        return CCEval(limit=limit, result_labels=result_labels)

    def create_model_input(self, rec: CCEvalInput) -> RemoteCompletionInput:
        # Generate the patch
        model_input = RemoteCompletionInput(
            prefix=rec.prompt,
            path=rec.metadata.file,
            suffix=rec.right_context,
            cursor_position=len(rec.prompt),
            recency_info=None,
        )
        return model_input

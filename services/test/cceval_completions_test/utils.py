"""Utility functions for cceval and other regression tests."""

import io
import json


def reads_jsonl(input_file: bytes) -> list[dict]:
    """Return list of deserialized data from jsonl encoded records."""
    with io.BytesIO(input_file) as f:
        return [json.loads(line) for line in f]


def get_first_n_lines(doc: str, n: int = 1):
    """Get the first N lines of a given document."""
    if n < 0:
        raise ValueError(f"Invalid parameter n={n}, which should < 0.")
    elif n == 0:
        return ""
    else:
        lines = doc.splitlines(True)
        return "".join(lines[:n])


def whitespace_norm(code: str):
    """Normalize the code by calling strip on each line and dropping empty lines."""
    lines = [line.strip() for line in code.splitlines() if line.strip()]
    return "\n".join(lines)


def compute_exact_match(
    target: str, predictions: list[str], normalize_whitespace: bool = True
) -> float:
    """Return whether any predicted string equals the target.

    The strings are normalized such that empty lines shouldn't be counted.
    """
    if normalize_whitespace:
        target = whitespace_norm(target)
        predictions = [whitespace_norm(p) for p in predictions]

    return float(any(target == prediction for prediction in predictions))

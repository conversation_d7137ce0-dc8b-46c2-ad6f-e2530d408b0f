"""Tests for smart_paste_stream_processor.py."""

import pytest
from typing import Iterator, List

from services.edit_host.smart_paste_stream_processor import (
    group_stream_by_line,
    compare_with_original_lines,
)


def test_group_stream_by_line_single_chunk():
    input_stream = iter(["Line 1\nLine 2\nLine 3\n"])
    result = list(group_stream_by_line(input_stream))
    assert result == ["Line 1\n", "Line 2\n", "Line 3\n"]


def test_group_stream_by_line_multiple_chunks():
    input_stream = iter(["Line 1\nLi", "ne 2\nLine ", "3\n"])
    result = list(group_stream_by_line(input_stream))
    assert result == ["Line 1\n", "Line 2\n", "Line 3\n"]


def test_group_stream_by_line_no_final_newline():
    input_stream = iter(["Line 1\nLine 2\nLine 3"])
    result = list(group_stream_by_line(input_stream))
    assert result == ["Line 1\n", "Line 2\n", "Line 3"]


def test_group_stream_by_line_empty_input():
    input_stream = iter([])
    result = list(group_stream_by_line(input_stream))
    assert result == []


def test_group_stream_by_line_only_newlines():
    input_stream = iter(["\n", "\n", "\n"])
    result = list(group_stream_by_line(input_stream))
    assert result == ["\n", "\n", "\n"]


def test_compare_with_original_lines_no_changes():
    lines = ["Line 1\n", "Line 2\n", "Line 3\n"]
    input_stream = iter(lines)
    result = list(compare_with_original_lines(input_stream, lines))
    assert result == [("Line 1\n", True), ("Line 2\n", True), ("Line 3\n", True)]


def test_compare_with_original_lines_change_in_middle():
    lines = ["Line 1\n", "Line 2\n", "Line 3\n"]
    input_stream = iter(["Line 1\n", "Changed Line 2\n", "Line 3\n"])
    result = list(compare_with_original_lines(input_stream, lines))
    assert result == [
        ("Line 1\n", True),
        ("Changed Line 2\n", False),
        ("Line 3\n", False),
    ]


def test_compare_with_original_lines_all_changed():
    lines = ["Line 1\n", "Line 2\n", "Line 3\n"]
    input_stream = iter(["Changed 1\n", "Changed 2\n", "Changed 3\n"])
    result = list(compare_with_original_lines(input_stream, lines))
    assert result == [
        ("Changed 1\n", False),
        ("Changed 2\n", False),
        ("Changed 3\n", False),
    ]


def test_compare_with_original_lines_empty_input():
    lines = ["Line 1\n", "Line 2\n", "Line 3\n"]
    input_stream = iter([])
    result = list(compare_with_original_lines(input_stream, lines))
    assert result == []


def test_compare_with_original_lines_empty_comparison():
    lines = []
    input_stream = iter(["Line 1\n", "Line 2\n", "Line 3\n"])
    result = list(compare_with_original_lines(input_stream, lines))
    assert result == [("Line 1\n", False), ("Line 2\n", False), ("Line 3\n", False)]


@pytest.mark.parametrize(
    "input_chunks, original_lines, expected_output",
    [
        # Test 1: Basic functionality with partial chunks
        (
            ["Line 1\nLi", "ne 2\nLine ", "3\nLine 4\n"],
            ["Line 1\n", "Line 2\n", "Line 3\n", "Old Line 4\n"],
            [
                ("Line 1\n", True),
                ("Line 2\n", True),
                ("Line 3\n", True),
                ("Line 4\n", False),
            ],
        ),
        # Test 2: No changes in a large input
        (
            ["Line " + str(i) + "\n" for i in range(1, 101)],
            ["Line " + str(i) + "\n" for i in range(1, 101)],
            [("Line " + str(i) + "\n", True) for i in range(1, 101)],
        ),
        # Test 3: Changes after many unchanged lines
        (
            ["Line " + str(i) + "\n" for i in range(1, 99)]
            + ["New Line 99\n", "New Line 100\n"],
            ["Line " + str(i) + "\n" for i in range(1, 101)],
            [("Line " + str(i) + "\n", True) for i in range(1, 99)]
            + [("New Line 99\n", False), ("New Line 100\n", False)],
        ),
        # Test 4: Empty and whitespace inputs
        (
            ["\n", "  \n", "\t\n", "Line 1\n"],
            ["\n", "\n", "\n", "Old Line 1\n"],
            [("\n", True), ("  \n", False), ("\t\n", False), ("Line 1\n", False)],
        ),
        # Test 5: Partial line changes
        (
            ["Line 1\n", "Line 2 with change\n", "Line 3\n"],
            ["Line 1\n", "Line 2\n", "Line 3\n"],
            [("Line 1\n", True), ("Line 2 with change\n", False), ("Line 3\n", False)],
        ),
    ],
)
def test_combined_stream_processing(input_chunks, original_lines, expected_output):
    result = list(
        compare_with_original_lines(
            group_stream_by_line(iter(input_chunks)), original_lines
        )
    )
    assert result == expected_output


# # Additional test for delayed processing
def test_delayed_processing():
    input_chunks = ["Line 1\n", "Line 2\n", "Line 3\n"]
    original_lines = ["Line 1\n", "Old Line 2\n", "Line 3\n"]
    result = compare_with_original_lines(
        group_stream_by_line(iter(input_chunks)), original_lines
    )

    # The processing shouldn't happen until we start consuming the result
    first_line = next(result)
    assert first_line == ("Line 1\n", True)

    second_line = next(result)
    assert second_line == ("Line 2\n", False)

    # The rest of the changes should be available
    assert list(result) == [("Line 3\n", False)]

"""Unit tests for the GRPC edit server."""

from unittest.mock import MagicMock, create_autospec

import grpc

# We use chat prompt formatter in instructions for now, it raises this ExceedContextLength
from base.prompt_format_chat.prompt_formatter import ExceedContextLength

from services.lib.retrieval import retriever_factory
from services.edit_host import edit_pb2
from services.edit_host.server.edit_server import AuthConfig, Config, EditServices
from services.edit_host.server.handler import (
    EditHandlerProtocol,
    InstructionResultStatusCode,
)
from services.working_set.client.client import WorkingSetClient

mock_config: Config = Config(
    port=1234,
    model_name="my_model",
    inference_host_endpoints={"default": "str"},
    third_party_arbiter_endpoint="str",
    content_manager_endpoint="str",
    working_set_endpoint=None,
    client_mtls=None,
    central_client_mtls=None,
    server_mtls=None,
    retrieval=retriever_factory.RetrievalConfig(),
    handler_type="EditHandler",
    handler_config={},
    max_handler_workers=4,
    max_server_workers=32,
    auth_config=AuthConfig(
        token_exchange_endpoint="str",
    ),
)


def test_edit_server():
    """Tests the edit server."""
    mock_handler = MagicMock(spec=EditHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = edit_pb2.EditRequest(model_name="my_model")
    service = EditServices(
        config=mock_config,
        handler=mock_handler,
        working_set_client=mock_working_set_client,
    )

    mock_response = edit_pb2.EditResponse(text="Mocked Response")
    mock_handler.edit.return_value = mock_response

    response = service.Edit(mock_request, mock_context)
    assert response is not None and response.text == "Mocked Response"


def test_instruction_exceed_context_length():
    """Respond with correct status_code when exceed context length."""
    mock_handler = MagicMock(spec=EditHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = edit_pb2.InstructionRequest(model_name="my_model")
    service = EditServices(
        config=mock_config,
        handler=mock_handler,
        working_set_client=mock_working_set_client,
    )

    mock_handler.instruction_stream.side_effect = ExceedContextLength(
        "Exceed context length"
    )

    instruction_stream_response = service.InstructionStream(mock_request, mock_context)
    assert (
        next(instruction_stream_response).status_code
        == InstructionResultStatusCode.EXCEED_CONTEXT_LENGTH.value
    ), "Expected error code for exceed context length"

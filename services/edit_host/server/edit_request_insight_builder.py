"""Helper to build a request insight events during an edit request."""

import uuid
from typing import Optional, Sequence

from base.prompt_format_edit.prompt_formatter import EditPromptOutput
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import Request<PERSON>ontext
from base.tokenizers import Tokenizer
from services.edit_host import edit_pb2
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher


# TODO: due to python pass-by-assignment we may want to inline the Tokenization field setting
def _get_tokenization_proto(
    tokenizer: Tokenizer,
    token_ids: Sequence[int],
    log_probs: Optional[Sequence[float]],
) -> request_insight_pb2.Tokenization:
    text, offsets = tokenizer.detokenize_with_offsets(token_ids)
    return request_insight_pb2.Tokenization(
        token_ids=token_ids,
        text=text,
        offsets=offsets,
        log_probs=log_probs or [],
    )


class EditRequestInsightBuilder:
    """Class to log a request insight events during an edit request."""

    def __init__(self, ri_publisher: request_insight_publisher.RequestInsightPublisher):
        self.ri_publisher = ri_publisher

    def record_request(
        self,
        prompt_output: EditPromptOutput,
        tokenizer: Tokenizer,
        request: edit_pb2.EditRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a code edit request event."""
        request_event = request_insight_publisher.new_event()
        ri_request = request_insight_pb2.RIEditRequest(request=request)
        ri_request.tokenization.CopyFrom(
            _get_tokenization_proto(tokenizer, prompt_output.tokens, None)
        )
        chunks = [
            request_insight_pb2.RetrievalChunk(
                text=chunk.text,
                path=chunk.path,
                char_offset=chunk.char_start,
                char_end=chunk.char_end,
                blob_name=chunk.blob_name,
                chunk_index=idx,
                origin=chunk.origin,
            )
            for idx, chunk in enumerate(prompt_output.retrieved_chunks_in_prompt)
        ]
        ri_request.retrieved_chunks.extend(chunks)
        # The final logging
        request_event.edit_host_request.MergeFrom(ri_request)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [request_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_response(
        self,
        response: edit_pb2.EditResponse,
        tokenizer: Tokenizer,
        truncated_output_tokens: Sequence[int],
        truncated_log_probs: Optional[Sequence[float]],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        if truncated_log_probs is None:
            truncated_log_probs = [0.0] * len(truncated_output_tokens)
        response_event = request_insight_publisher.new_event()
        ri_response = request_insight_pb2.RIEditResponse(response=response)
        ri_response.tokenization.CopyFrom(
            _get_tokenization_proto(
                tokenizer, truncated_output_tokens, truncated_log_probs
            )
        )

        response_event.edit_host_response.MergeFrom(ri_response)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

"""Tests for the post_processing module."""

import pytest

from services.edit_host.server.post_processing import PostprocessingConfig, post_process


@pytest.mark.parametrize(
    "response,expected",
    [
        ("abc\n```\n", "abc"),
        ("abc```\n```\n", "abc```"),
        ("hello world\n", "hello world\n"),
    ],
)
def test_remove_backticks(response: str, expected: str):
    assert (
        post_process(
            response, PostprocessingConfig(remove_triple_backticks_at_end=True)
        )
        == expected
    )

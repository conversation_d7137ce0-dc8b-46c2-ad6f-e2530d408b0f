load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "handler",
    srcs = ["handler.py"],
    deps = [
        "//base/feature_flags:feature_flags_py",
        "//services/edit_host:edit_proto_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        requirement("grpcio"),
    ],
)

py_library(
    name = "instruction_handler_metrics",
    srcs = ["instruction_handler_metrics.py"],
    deps = [
        requirement("prometheus-client"),
    ],
)

DEPS = [
    ":handler",
    ":edit_handler",
    ":instruction_handler",
    "//base/feature_flags:feature_flags_py",
    "//base/python/opentelemetry_utils:traced_threadpool",
    "//base/tokenizers",
    "//base/python/grpc:client_options",
    "//base/logging:struct_logging",
    "//base/python/signal_handler",
    "//base/tracing:tracing_py",
    "//services/edit_host:edit_proto_py_proto",
    "//services/inference_host/client",
    "//services/inference_host/client:multiplex",
    "//services/lib/retrieval:retriever_factory",
    "//services/lib/grpc/auth:service_auth",
    "//services/lib/grpc/auth:service_token_auth",
    "//services/lib/grpc/auth:service_auth_interceptor",
    "//services/lib/grpc/metrics:metrics",
    "//services/lib/grpc/tls_config:grpc_tls_config_py",
    "//services/token_exchange/client:client_py",
    "//services/working_set/client:client_py",
    requirement("dataclasses_json"),
    requirement("grpcio-reflection"),
    requirement("grpcio"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("protobuf"),
    requirement("prometheus-client"),
    "//services/lib/balanced_third_party_clients",
    "//services/third_party_arbiter/client:client_py",
]

py_binary(
    name = "edit_server",
    srcs = [
        "edit_server.py",
    ],
    deps = DEPS,
)

pytest_test(
    name = "edit_server_test",
    size = "small",
    srcs = ["edit_server_test.py"],
    deps = [
        ":edit_server",
        requirement("pytest-grpc"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":edit_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

py_library(
    name = "edit_handler",
    srcs = [
        "edit_handler.py",
    ],
    deps = [
        ":edit_request_insight_builder",
        ":handler",
        ":post_processing",
        "//base/logging:struct_logging",
        "//base/prompt_format_edit",
        "//base/prompt_format_retrieve",
        "//base/tokenizers",
        "//services/inference_host:infer_py_proto",
        "//services/inference_host/client",
        "//services/inference_host/client:multiplex",
        requirement("opentelemetry-api"),
        # Dependency needed for reusing retrievers.
        "//services/lib/retrieval:retriever",
    ],
)

pytest_test(
    name = "edit_handler_test",
    srcs = ["edit_handler_test.py"],
    deps = [
        ":edit_handler",
        "//base/test_utils:synchronous_executor",
    ],
)

py_library(
    name = "instruction_handler",
    srcs = [
        "instruction_handler.py",
    ],
    deps = [
        ":instruction_request_insight_builder",
        ":smart_paste_request_insight_builder",
        ":handler",
        ":instruction_handler_metrics",
        "//base/logging:struct_logging",
        "//base/prompt_format_chat",
        "//base/prompt_format_chat:instruction_prompt_formatter",
        "//base/prompt_format_chat:smart_paste_prompt_formatter",
        "//base/prompt_format_smart_paste:forger_prompt_formatter",
        "//base/prompt_format_retrieve",
        "//base/third_party_clients:clients",
        "//services/lib/balanced_third_party_clients",
        "//services/third_party_arbiter/client:client_py",
        "//base/tokenizers",
        "//base/tokenizers:stream_utils",
        "//services/edit_host:instruction_stream_aggregator",
        "//services/edit_host:smart_paste_stream_processor",
        "//services/inference_host:infer_py_proto",
        "//services/inference_host/client",
        "//services/inference_host/client:multiplex",
        "//services/request_insight:request_insight_py_proto",
        requirement("opentelemetry-api"),
        # Dependency needed for reusing retrievers.
        "//services/lib/retrieval:retriever",
    ],
)

pytest_test(
    name = "instruction_handler_test",
    srcs = ["instruction_handler_test.py"],
    deps = [
        ":instruction_handler",
        "//base/prompt_format_smart_paste:forger_prompt_formatter",
        "//base/test_utils:synchronous_executor",
        "//base/tokenizers",
        "//base/tokenizers:test_utils",
        "//services/inference_host:infer_py_proto",
        "//services/inference_host/client",
    ],
)

py_library(
    name = "edit_request_insight_builder",
    srcs = [
        "edit_request_insight_builder.py",
    ],
    deps = [
        ":handler",
        "//base/prompt_format_edit",
        "//base/tokenizers",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
    ],
)

pytest_test(
    name = "edit_request_insight_builder_test",
    srcs = ["edit_request_insight_builder_test.py"],
    deps = [
        ":edit_request_insight_builder",
    ],
)

py_library(
    name = "smart_paste_request_insight_builder",
    srcs = [
        "smart_paste_request_insight_builder.py",
    ],
    deps = [
        ":handler",
        "//base/prompt_format_chat:smart_paste_prompt_formatter",
        "//base/prompt_format_smart_paste:forger_prompt_formatter",
        "//base/tokenizers",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/publisher:publisher_py",
    ],
)

pytest_test(
    name = "smart_paste_request_insight_builder_test",
    srcs = ["smart_paste_request_insight_builder_test.py"],
    deps = [
        ":smart_paste_request_insight_builder",
    ],
)

py_library(
    name = "instruction_request_insight_builder",
    srcs = [
        "instruction_request_insight_builder.py",
    ],
    deps = [
        ":handler",
        "//base/prompt_format_chat:instruction_prompt_formatter",
        "//base/prompt_format_smart_paste:forger_prompt_formatter",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/publisher:publisher_py",
    ],
)

pytest_test(
    name = "instruction_request_insight_builder_test",
    srcs = ["instruction_request_insight_builder_test.py"],
    deps = [
        ":instruction_request_insight_builder",
        "//base/prompt_format:common",
    ],
)

py_library(
    name = "post_processing",
    srcs = [
        "post_processing.py",
    ],
    deps = [
        requirement("dataclasses_json"),
        requirement("typing_extensions"),
    ],
)

pytest_test(
    name = "post_processing_test",
    srcs = ["post_processing_test.py"],
    deps = [
        ":post_processing",
    ],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

"""Helper to build a request insight events during an instruction request."""

from base.prompt_format_chat.instruction_prompt_formatter import InstructionPromptOutput

from services.lib.request_context.request_context import RequestContext
from services.edit_host import edit_pb2
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher
from services.lib.grpc.auth.service_auth import AuthInfo


def _stringify_structured_instruction_prompt_output(
    structured_prompt: InstructionPromptOutput,
):
    prompt = ""
    prompt += f"""\
{'-' * 40}
system_prompt:
{'-' * 40}
{structured_prompt.system_prompt}

"""

    prompt += f"""\
{'-' * 40}
current message:
{'-' * 40}
{structured_prompt.message}

{'-' * 40}
chat history (from most recent to least recent):
{'-' * 40}
"""

    for exchange in structured_prompt.chat_history:
        prompt += f"""\
    {'-' * 40}
    user:
    {'-' * 40}
    {exchange.request_message}

    {'-' * 40}
    assistant:
    {'-' * 40}
    {exchange.response_text}

"""

    promptlines = prompt.splitlines(keepends=True)
    # limit to 500k characters (~2 bytes / character => ~1MB max) to avoid
    # bigtable/grpc issues with large messages,
    # and to ease readability.
    prompt = prompt[:500_000]

    # Create sorted list of unique paths of chunks
    filepaths_in_prompt = "\n    ".join(
        sorted(
            set(
                [
                    chunk.path
                    for chunk in structured_prompt.retrieved_chunks_in_prompt
                    if not chunk.path.startswith("docset://")
                ]
            )
        )
    )

    # Prepend metadata to start of "prompt"
    prompt = (
        f"""\
{'-' * 40}
Metadata:
    Chunks in prompt: {len(list(structured_prompt.retrieved_chunks_in_prompt))}
    Lines in prompt: {len(promptlines)}

Paths in prompt:
    {filepaths_in_prompt}

{'-' * 40}
"""
        + prompt
    )

    return prompt


def _get_tokenization_proto_from_structured_prompt(
    prompt: InstructionPromptOutput,
) -> request_insight_pb2.Tokenization:
    return request_insight_pb2.Tokenization(
        token_ids=[0],
        text=_stringify_structured_instruction_prompt_output(prompt),
        offsets=[0],
        log_probs=[1.0],
    )


def _get_tokenization_proto_from_str(
    text: str,
) -> request_insight_pb2.Tokenization:
    return request_insight_pb2.Tokenization(
        token_ids=[0],
        text=text,
        offsets=[0],
        log_probs=[1.0],
    )


class InstructionRequestInsightBuilder:
    """Class to log a request insight events during a chat request."""

    def __init__(self, ri_publisher: request_insight_publisher.RequestInsightPublisher):
        self.ri_publisher = ri_publisher

    def record_request(
        self,
        prompt_output: InstructionPromptOutput,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a chat request event."""
        request_event = request_insight_publisher.new_event()
        ri_request = request_insight_pb2.RIInstructionRequest(request=request)
        ri_request.tokenization.CopyFrom(
            _get_tokenization_proto_from_structured_prompt(prompt_output)
        )

        chunks = [
            request_insight_pb2.RetrievalChunk(
                text=chunk.text,
                path=chunk.path,
                char_offset=chunk.char_start,
                char_end=chunk.char_end,
                blob_name=chunk.blob_name or "",
                chunk_index=idx,
                origin=chunk.origin,
            )
            for idx, chunk in enumerate(prompt_output.retrieved_chunks_in_prompt)
        ]
        ri_request.retrieved_chunks.extend(chunks)
        # The final logging
        request_event.instruction_host_request.MergeFrom(ri_request)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [request_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_response(
        self,
        response: edit_pb2.InstructionAggregateResponse,
        truncated_output: str | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
        error_message: str | None = None,
    ):
        response_event = request_insight_publisher.new_event()
        ri_response = request_insight_pb2.RIInstructionResponse(
            response=response, error_message=error_message
        )

        # We can't call isinstance on the generic type Sequence[int], so this is a workaround for the typing.
        if isinstance(truncated_output, str):
            ri_response.tokenization.CopyFrom(
                _get_tokenization_proto_from_str(truncated_output)
            )

        response_event.instruction_host_response.MergeFrom(ri_response)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

"""Instruction Handler handles Edit/Instruction requests via third party models."""

import concurrent.futures
import pathlib
from dataclasses import dataclass
from typing import Iterator

import opentelemetry.trace
import structlog
from dataclasses_json import dataclass_json

import base.feature_flags
from base.blob_names.python.blob_names import Blobs
from base.prompt_format.common import Exchange as PromptExchange
from base.prompt_format_chat.instruction_prompt_formatter import (
    InstructionPromptFormatterV2,
    InstructionPromptInput,
    InstructionPromptOutput,
    InstructionTokenApportionment,
)
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON>ou<PERSON>
from base.prompt_format_chat.smart_paste_prompt_formatter import (
    SmartPastePromptFormatter,
    SmartPastePromptInput,
    SmartPastePromptOutput,
    SmartPasteTokenApportionment,
)
from base.prompt_format_retrieve.prompt_formatter import InstructRetrieverPromptInput
from base.prompt_format_smart_paste.forger_prompt_formatter import (
    Forger<PERSON>romptFormatter,
    ForgerSmartPastePromptOutput,
    ForgerSmartPasteTokenApportionment,
    LineEnding,
)
from base.third_party_clients import anthropic_tool_response_handler
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from services.lib.balanced_third_party_clients.anthropic_balanced import (
    AnthropicBalancedClient,
)
from base.third_party_clients.third_party_model_client import (
    ReplaceTextResponse,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
)
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.stream_utils import detokenize_stream
from base.tokenizers.tokenizer import Tokenizer
from services.edit_host import edit_pb2
from services.edit_host.instruction_stream_aggregator import InstructionStreamAggregator
from services.edit_host.server.handler import (
    EditHandlerProtocol,
    EditResult,
    InstructionResult,
    ReplaceText,
)
from services.edit_host.server.instruction_handler_metrics import (
    InstructionHandlerMetrics,
)
from services.edit_host.server.instruction_request_insight_builder import (
    InstructionRequestInsightBuilder,
)
from services.edit_host.server.smart_paste_request_insight_builder import (
    SmartPasteRequestInsightBuilder,
)
from services.edit_host.smart_paste_stream_processor import (
    compare_with_original_lines,
)
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.inference_host.client.multiplex import InferenceStubFactoryProtocol
from services.inference_host.infer_pb2_grpc import InfererStub
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.third_party_arbiter.client.client import ThirdPartyArbiterClient

logger = structlog.get_logger("InstructionHandler")
tracer = opentelemetry.trace.get_tracer(__name__)

_SMARTPASTE_WITH_PURE_ADDITIONS = base.feature_flags.BoolFlag(
    "smartpaste_with_pure_additions", False
)
_SMARTPASTE_FORCE_FUZZY_SEARCH = base.feature_flags.BoolFlag(
    "smartpaste_force_fuzzy_search", False
)


def detokenize_text_stream(
    tokenized_iterator: concurrent.futures.Future[
        Iterator[InferenceClientProtocol.Reply]
    ],
    tokenizer: Tokenizer,
    end_token_ids: set[int],
) -> Iterator[str]:
    """Detokenize a stream of tokens into text.

    Uses the common detokenize_stream utility to handle multi-token characters properly.
    """

    # Convert InferenceClientProtocol.Reply stream to token stream
    def token_stream():
        for response in tokenized_iterator.result():
            tokens = []
            for token in response.output_tokens:
                if token in end_token_ids:
                    break
                tokens.append(token)
            yield tokens

    return detokenize_stream(token_stream(), tokenizer, end_token_ids)


def _wrap_third_party_response(
    start_line_number: int | None = None,
    end_line_number: int | None = None,
    text: str | None = None,
):
    """
    Wrap the response in a ThirdPartyModelResponse object.

    This function creates a response object with either line numbers or text content.
    It ensures that exactly one of the arguments is provided.

    Args:
        start_line_number: The starting line number (0-based).
        end_line_number: The ending line number (0-based).
        text: The text content to be included in the response.

    Returns:
        ThirdPartyModelResponse: A wrapped response object.
    """
    # Convert to 1-based line numbers for front-end compatibility
    if start_line_number is not None:
        start_line_number += 1
        assert end_line_number is None and text is None
        text = ""
    elif end_line_number is not None:
        end_line_number += 1
        assert start_line_number is None and text is None
        text = ""
    else:
        assert (
            start_line_number is None and end_line_number is None and text is not None
        )

    return ThirdPartyModelResponse(
        text="",
        replace_text_response=ReplaceTextResponse(
            old_text=None,
            replacement_text=text,
            start_line_number=start_line_number,
            end_line_number=end_line_number,
            sequence_id=0,  # Forger only supports single replace text block
        ),
    )


def postprocess_replace_text_stream(
    tokenized_iterator: concurrent.futures.Future[
        Iterator[InferenceClientProtocol.Reply]
    ],
    tokenizer: Tokenizer,
    end_token_ids: set[int],
    prompt_output: ForgerSmartPastePromptOutput,
) -> Iterator[ThirdPartyModelResponse]:
    """
    Process the tokenized stream and yield ThirdPartyModelResponse objects.

    This function handles the streaming response, detokenizing it and comparing
    with the original content to determine which lines have changed.

    Args:
        tokenized_iterator: A future containing an iterator of tokenized responses.
        tokenizer: The tokenizer used for detokenization.
        end_token_ids: A set of token IDs that signify the end of the response.
        prompt_output: The output from the prompt formatter containing context information.

    Yields:
        ThirdPartyModelResponse objects representing the processed stream.
    """
    detokenized_stream = detokenize_text_stream(
        tokenized_iterator, tokenizer, end_token_ids
    )
    start_line_number = prompt_output.start_line_number
    has_sent_line_numbers, has_produced_any_output = False, False

    for line, is_unchanged in compare_with_original_lines(
        detokenized_stream,
        prompt_output.used_lines,
    ):
        has_produced_any_output = True
        if is_unchanged:
            start_line_number += 1
        else:
            if not has_sent_line_numbers:
                # Send the start line number before the first changed line
                yield _wrap_third_party_response(start_line_number=start_line_number)
                has_sent_line_numbers = True
            yield _wrap_third_party_response(text=line)

    if not has_sent_line_numbers:
        if has_produced_any_output:
            # Model generated output identical to the original code
            # Send empty replacement at the end of the file
            yield _wrap_third_party_response(
                start_line_number=prompt_output.end_line_number
            )
            yield _wrap_third_party_response(text="")
        else:
            # Model suggests deleting the code completely
            # Send empty replacement for the entire original range
            yield _wrap_third_party_response(
                start_line_number=prompt_output.start_line_number
            )
            yield _wrap_third_party_response(text="")
    # Send the end line number at the very end
    yield _wrap_third_party_response(end_line_number=prompt_output.end_line_number)


@dataclass_json
@dataclass(frozen=True)
class InstructionHandlerThirdPartyConfig:
    """Config for third-party instruction model."""

    gcp_project_id: str

    gcp_region: str

    anthropic_api_key_path: str

    openai_api_key_path: str

    xai_api_key_path: str

    fireworks_api_key_path: str

    client_type: str
    """ Type of third party client, e.g. 'anthropic_vertexai' """

    model_name: str
    """ Third party model name, e.g. 'claude-3-5-sonnet-20240620' """

    temperature: float

    max_output_length: int

    instruction_token_apportionment: InstructionTokenApportionment

    smart_paste_token_apportionment: SmartPasteTokenApportionment


@dataclass_json
@dataclass(frozen=True)
class SamplingParams:
    """The sampling parameters for the code edit model. See infer.proto for meaning."""

    top_k: int = 0
    """Top-k sampling parameter."""

    top_p: float = 0.0
    """Top-p sampling parameter."""

    temperature: float = 0.0
    """Temperature sampling parameter."""

    seed: int = 0
    """Random seed that is passed to inference host."""

    inference_timeout_s: float = 60.0
    """Timeout that is passed to inference host."""

    def __post_init__(self):
        assert 0.0 <= self.top_p <= 1.0, "top_p must be [0, 1]."
        assert self.top_k >= 0, "top_k must be non-negative."
        assert self.temperature >= 0, "temperature must be non-negative."
        assert self.seed >= 0, "seed must be non-negative."
        assert self.inference_timeout_s > 0, "inference_timeout_s must be positive."


@dataclass_json
@dataclass(frozen=True)
class InstructionHandlerTokenizedConfig:
    """Config for tokenized instruction model."""

    tokenizer_name: str

    token_apportionment: ForgerSmartPasteTokenApportionment

    max_context_length: int

    max_output_length: int

    sampling_params: SamplingParams

    post_process_config: dict

    prompt_formatter_name: str | None  # Not currently used


def _last_whitespace_index(s: str):
    for i in range(len(s) - 1, -1, -1):
        if s[i].isspace():
            return i
    return -1


class InstructionHandler(EditHandlerProtocol):
    """Handles edit requests."""

    def __init__(
        self,
        client: ThirdPartyModelClient | InfererClient,
        instruction_prompt_formatter: InstructionPromptFormatterV2 | None,
        smart_paste_prompt_formatter: SmartPastePromptFormatter | ForgerPromptFormatter,
        namespace: str,
        ri_publisher: RequestInsightPublisher,
        metrics: InstructionHandlerMetrics,
        retriever: Retriever,
        max_output_length: int,
        sampling_params: SamplingParams | None,
        tokenizer: Tokenizer | None,
    ):
        self.client = client
        self.namespace = namespace
        self.retriever = retriever
        self.instruction_ri_builder = InstructionRequestInsightBuilder(ri_publisher)
        self.smart_paste_ri_builder = SmartPasteRequestInsightBuilder(ri_publisher)
        self.metrics = metrics
        self.max_output_length = max_output_length
        self.sampling_params = sampling_params
        self.instruction_prompt_formatter = instruction_prompt_formatter
        self.smart_paste_prompt_formatter = smart_paste_prompt_formatter
        self.end_token_ids = None
        self.tokenizer = tokenizer
        if isinstance(smart_paste_prompt_formatter, ForgerPromptFormatter):
            self.tokenizer = smart_paste_prompt_formatter.tokenizer
            self.end_token_ids = {
                smart_paste_prompt_formatter.tokenizer.special_tokens.eos
            }

    def _retrieve(
        self,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> RetrievalResult:
        if not request.HasField("blobs"):
            logger.error(
                "Received request %s without blobs", request_context.request_id
            )
            raise ValueError("Received request without blobs")
        blobs = Blobs.from_proto(request.blobs)

        retrieval_input = RetrievalInput(
            InstructRetrieverPromptInput(
                prefix=request.prefix,
                suffix=request.suffix,
                path=request.path,
                selected_code=request.selected_text,
                instruction=request.instruction,
                chat_history=[
                    PromptExchange(
                        request_message=item.request_message,
                        response_text=item.response_text,
                    )
                    for item in request.chat_history
                ],
            ),
            blobs=[blobs],
        )

        if retrieval_input.workspace_empty():
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=(), checkpoint_not_found=False
            )
        with self.metrics.edit_retrieval_latency.labels(
            request.model_name,
            request_context.request_source,
            auth_info.tenant_name,
        ).time():
            retrieval_result = self.retriever.retrieve(
                input_=retrieval_input,
                request_context=request_context,
                auth_info=auth_info,
                executor=executor,
            )
        return retrieval_result

    def edit(
        self,
        request: edit_pb2.EditRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> EditResult:
        raise NotImplementedError(
            "InstructionHandler does not support legacy edit requests"
        )

    def _record_request(
        self,
        prompt_output: InstructionPromptOutput
        | SmartPastePromptOutput
        | ForgerSmartPastePromptOutput,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ):
        if isinstance(prompt_output, InstructionPromptOutput):
            executor.submit(
                self.instruction_ri_builder.record_request,
                prompt_output=prompt_output,
                request=request,
                request_context=request_context,
                auth_info=auth_info,
            )
        elif isinstance(prompt_output, SmartPastePromptOutput):
            executor.submit(
                self.smart_paste_ri_builder.record_request,
                prompt_output=prompt_output,
                tokenizer=None,
                request=request,
                request_context=request_context,
                auth_info=auth_info,
            )
        elif isinstance(prompt_output, ForgerSmartPastePromptOutput):
            executor.submit(
                self.smart_paste_ri_builder.record_request,
                prompt_output=prompt_output,
                tokenizer=self.tokenizer,
                request=request,
                request_context=request_context,
                auth_info=auth_info,
            )
        else:
            raise ValueError(f"Unknown prompt output type: {type(prompt_output)}")

    def _record_response(
        self,
        prompt_output: InstructionPromptOutput
        | SmartPastePromptOutput
        | ForgerSmartPastePromptOutput,
        ri_response: edit_pb2.InstructionAggregateResponse,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
        error_message: str | None = None,
    ):
        if isinstance(prompt_output, InstructionPromptOutput):
            executor.submit(
                self.instruction_ri_builder.record_response,
                ri_response,
                truncated_output=ri_response.text,
                request_context=request_context,
                auth_info=auth_info,
                error_message=error_message,
            )
        elif isinstance(prompt_output, SmartPastePromptOutput) or isinstance(
            prompt_output, ForgerSmartPastePromptOutput
        ):
            executor.submit(
                self.smart_paste_ri_builder.record_response,
                ri_response,
                request_context=request_context,
                auth_info=auth_info,
                error_message=error_message,
            )
        else:
            raise ValueError(f"Unknown prompt output type: {type(prompt_output)}")

    def _format_prompt(
        self,
        request: edit_pb2.InstructionRequest,
        retrieval_result: RetrievalResult | None,
    ):
        retrieved_chunks = []
        if retrieval_result:
            retrieved_chunks = [
                x.to_prompt_chunk() for x in retrieval_result.get_retrieved_chunks()
            ]
        chat_history = [
            PromptExchange(
                request_message=item.request_message,
                response_text=item.response_text,
                request_id=item.request_id if item.HasField("request_id") else None,
            )
            for item in request.chat_history
        ]
        if request.code_block:
            prompt_input = SmartPastePromptInput(
                path=request.path,
                prefix=request.prefix,
                selected_code=request.selected_text,
                suffix=request.suffix,
                code_block=request.code_block,
                chat_history=chat_history,
                prefix_begin=request.position.prefix_begin,
                suffix_end=request.position.suffix_end,
                retrieved_chunks=retrieved_chunks,
                target_path=request.path,
                target_file_content=request.target_file_content,
                context_code_exchange_request_id=request.context_code_exchange_request_id,
            )
            if isinstance(self.smart_paste_prompt_formatter, ForgerPromptFormatter):
                force_fuzzy_search = _SMARTPASTE_FORCE_FUZZY_SEARCH.get(
                    base.feature_flags.get_global_context()
                )
                return self.smart_paste_prompt_formatter.format_prompt(
                    prompt_input,
                    force_fuzzy_search=force_fuzzy_search,
                )
            elif type(self.smart_paste_prompt_formatter) is SmartPastePromptFormatter:
                return self.smart_paste_prompt_formatter.format_prompt(
                    prompt_input,
                    _SMARTPASTE_WITH_PURE_ADDITIONS.get(
                        base.feature_flags.get_global_context()
                    ),
                )
            else:
                raise ValueError(
                    f"Unknown smart paste prompt formatter type: {type(self.smart_paste_prompt_formatter)}"
                )
        else:
            assert self.instruction_prompt_formatter is not None
            prompt_input = InstructionPromptInput(
                path=request.path,
                prefix=request.prefix,
                selected_code=request.selected_text,
                suffix=request.suffix,
                instruction=request.instruction,
                chat_history=chat_history,
                prefix_begin=request.position.prefix_begin,
                suffix_end=request.position.suffix_end,
                retrieved_chunks=retrieved_chunks,
                user_guidelines=request.user_guidelines,
                workspace_guidelines=request.workspace_guidelines,
            )
            return self.instruction_prompt_formatter.format_prompt(
                prompt_input,
            )

    def _generate_stream_from_client(
        self,
        request: edit_pb2.InstructionRequest,
        prompt_output: InstructionPromptOutput
        | SmartPastePromptOutput
        | ForgerSmartPastePromptOutput,
        retrieval_result: RetrievalResult | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> Iterator[InstructionResult]:
        current_trace_span = tracer.start_span("first_token")
        if isinstance(self.client, InfererClient):
            assert isinstance(prompt_output, ForgerSmartPastePromptOutput)
            assert self.sampling_params is not None
            assert self.end_token_ids is not None
            assert self.tokenizer is not None
            response_iterator_tokenized = executor.submit(
                self.client.infer_stream,
                input_tokens=prompt_output.tokens,
                max_output_length=self.max_output_length,
                end_token_ids=self.end_token_ids,
                top_k=self.sampling_params.top_k,
                top_p=self.sampling_params.top_p,
                temperature=self.sampling_params.temperature,
                random_seed=self.sampling_params.seed,
                request_context=request_context,
                timeout_s=self.sampling_params.inference_timeout_s,
                sequence_id=request.sequence_id,
            )
            response_iterator_result = postprocess_replace_text_stream(
                response_iterator_tokenized,
                self.tokenizer,
                self.end_token_ids,
                prompt_output,
            )
        else:
            assert isinstance(prompt_output, InstructionPromptOutput) or isinstance(
                prompt_output, SmartPastePromptOutput
            )

            if request.code_block:
                raise NotImplementedError(
                    "request.code_block was used for a previous version of instructions via Claude."
                    "Not supported via third party client anymore."
                )
            is_instruction_request = len(request.code_block) == 0

            response_iterator = executor.submit(
                self.client.generate_response_stream,
                model_caller="edit-host",
                cur_message=prompt_output.message,
                chat_history=list(prompt_output.chat_history),
                system_prompt=prompt_output.system_prompt,
                tools=list(prompt_output.tools or []),
                prefill=prompt_output.prefill,
                request_context=request_context,
            )

            response_iterator_result = response_iterator.result()

            if is_instruction_request:
                response_iterator_result = anthropic_tool_response_handler.stream_code_instructions_v2_on_third_party_model_response(
                    response_iterator_result
                )

        self._record_request(
            prompt_output, request, request_context, auth_info, executor
        )

        text_buffer = ""  # Aggregates text for batching stream

        checkpoint_not_found = False
        aggregator = InstructionStreamAggregator()

        mid_stream_error = None
        is_first_token = True
        try:
            for response in response_iterator_result:
                if is_first_token:
                    # Replace first token span with stream span
                    current_trace_span.end()
                    current_trace_span = tracer.start_span("stream")
                    is_first_token = False

                    # If we have retrieval, yield unknown_blob_names and checkpoint_not_found first
                    if retrieval_result is not None:
                        unknown_blob_names = list(
                            retrieval_result.get_missing_blob_names()
                        )
                        checkpoint_not_found = (
                            retrieval_result.get_checkpoint_not_found()
                        )
                        if len(unknown_blob_names) > 0:
                            instruction_result = InstructionResult(
                                text="",
                                unknown_blob_names=unknown_blob_names,
                                checkpoint_not_found=checkpoint_not_found,
                            )
                            aggregator.add(instruction_result.to_proto())
                            yield instruction_result

                    # For some prompt formatters, it's possible that line range is known before we start generating response.
                    # So in this case we yield start_line at the very beginning and end_line at the very end.
                    if (
                        isinstance(prompt_output, InstructionPromptOutput)
                        and prompt_output.selection_line_range is not None
                    ):
                        instruction_result = InstructionResult(
                            text="",
                            unknown_blob_names=[],
                            checkpoint_not_found=checkpoint_not_found,
                            replace_text=ReplaceText(
                                old_text="",
                                text="",
                                start_line=prompt_output.selection_line_range[0],
                                end_line=None,
                                sequence_id=0,
                            ),
                        )
                        aggregator.add(instruction_result.to_proto())
                        yield instruction_result

                # For a tool response, yield it immediately
                # IMPORTANT: both instructions and smartpaste is
                # using this branch. Including fixing line endings for windows.
                if response.replace_text_response:
                    replacement_text = response.replace_text_response.replacement_text
                    # For stable version of instructions we use only replacement_text
                    # So doing postprocessing only for it
                    if (
                        replacement_text
                        and prompt_output.original_line_ending == LineEnding.CRLF
                    ):
                        replacement_text = replacement_text.replace("\n", "\r\n")

                    text_buffer += response.text
                    instruction_result = InstructionResult(
                        text=response.text,
                        unknown_blob_names=[],
                        checkpoint_not_found=checkpoint_not_found,
                        replace_text=ReplaceText(
                            old_text=response.replace_text_response.old_text,
                            text=replacement_text,
                            start_line=response.replace_text_response.start_line_number,
                            end_line=response.replace_text_response.end_line_number,
                            sequence_id=response.replace_text_response.sequence_id,
                        ),
                    )
                    aggregator.add(instruction_result.to_proto())
                    yield instruction_result
                    continue

                # Handle standard text responses
                text_buffer += response.text
                cut_index = _last_whitespace_index(text_buffer)
                if cut_index >= 0:
                    yield InstructionResult(
                        text=text_buffer[: cut_index + 1],
                        unknown_blob_names=[],
                        checkpoint_not_found=checkpoint_not_found,
                    )
                    text_buffer = text_buffer[cut_index + 1 :]
        except Exception as e:
            mid_stream_error = (
                f"{type(e).__qualname__}: {str(e)}"  # Store for RI logging in finally
            )
            raise e
        finally:
            # Close current span (must be either first_token or stream)
            current_trace_span.end()

            # Input stream is done, we can yield any remaining text in the buffer, if we have any
            if len(text_buffer):
                instruction_result = InstructionResult(
                    text=text_buffer,
                    unknown_blob_names=[],
                    checkpoint_not_found=checkpoint_not_found,
                )
                aggregator.add(instruction_result.to_proto())
                yield instruction_result

        if (
            isinstance(prompt_output, InstructionPromptOutput)
            and prompt_output.selection_line_range is not None
        ):
            instruction_result = InstructionResult(
                text="",
                unknown_blob_names=[],
                checkpoint_not_found=checkpoint_not_found,
                replace_text=ReplaceText(
                    old_text=None,
                    text=None,
                    start_line=None,
                    end_line=prompt_output.selection_line_range[1],
                    sequence_id=0,
                ),
            )
            aggregator.add(instruction_result.to_proto())
            yield instruction_result

        self._record_response(
            prompt_output,
            aggregator.build(),
            request_context,
            auth_info,
            executor,
            error_message=mid_stream_error,
        )

    def instruction_stream(
        self,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> Iterator[InstructionResult]:
        with tracer.start_as_current_span("retrieval"):
            if request.code_block:
                # No retrieval needed for smartpaste
                retrieval_result = RetrievalResult(
                    retrieved_chunks=(),
                    missing_blob_names=(),
                    checkpoint_not_found=False,
                )
            else:
                retrieval_result = self._retrieve(
                    request, request_context, auth_info, executor
                )

        with tracer.start_as_current_span("instruction_format_prompt"):
            prompt_output = self._format_prompt(
                request,
                retrieval_result,
            )

        with tracer.start_as_current_span("instruction_inference"):
            return self._generate_stream_from_client(
                request,
                prompt_output,
                retrieval_result=retrieval_result,
                request_context=request_context,
                auth_info=auth_info,
                executor=executor,
            )


def create_instruction_handler_third_party(
    config: dict,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    metrics: InstructionHandlerMetrics,
    retriever: Retriever,
    arbiter_client: ThirdPartyArbiterClient | None = None,
) -> InstructionHandler:
    handler_config: InstructionHandlerThirdPartyConfig = (
        InstructionHandlerThirdPartyConfig.schema().load(config)  # type: ignore
    )
    client_type = handler_config.client_type.lower()
    if client_type == "anthropic_vertexai":
        client = AnthropicVertexAiClient(
            project_id=handler_config.gcp_project_id,
            region=handler_config.gcp_region,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_length,
        )
    elif client_type == "anthropic_direct":
        anthropic_api_key = (
            pathlib.Path(handler_config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicDirectClient(
            api_key=anthropic_api_key,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_length,
        )
    elif client_type == "anthropic_balanced":
        if arbiter_client is None:
            raise ValueError(
                "arbiter_client is required for anthropic_balanced client type"
            )
        anthropic_api_key = (
            pathlib.Path(handler_config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicBalancedClient(
            arbiter_client=arbiter_client,
            anthropic_api_key=anthropic_api_key,
            gcp_project_id=handler_config.gcp_project_id,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_length,
        )
    else:
        raise ValueError(f"Unknown model type: {client_type}")

    instruction_prompt_formatter = InstructionPromptFormatterV2(
        ClaudeTokenCounter(),
        handler_config.instruction_token_apportionment,
    )
    smart_paste_prompt_formatter = SmartPastePromptFormatter(
        ClaudeTokenCounter(),
        handler_config.smart_paste_token_apportionment,
    )
    sampling_params = None
    if isinstance(handler_config, InstructionHandlerTokenizedConfig):
        sampling_params = handler_config.sampling_params
    instruction_handler = InstructionHandler(
        client=client,
        instruction_prompt_formatter=instruction_prompt_formatter,
        smart_paste_prompt_formatter=smart_paste_prompt_formatter,
        namespace=namespace,
        ri_publisher=ri_publisher,
        metrics=metrics,
        retriever=retriever,
        max_output_length=handler_config.max_output_length,
        sampling_params=sampling_params,
        tokenizer=None,
    )

    return instruction_handler


def create_instruction_handler_tokenized(
    config: dict,
    inference_stub_factory: InferenceStubFactoryProtocol,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    metrics: InstructionHandlerMetrics,
    retriever: Retriever,
) -> InstructionHandler:
    handler_config: InstructionHandlerTokenizedConfig = (
        InstructionHandlerTokenizedConfig.schema().load(config)  # type: ignore
    )
    tokenizer = create_tokenizer_by_name(handler_config.tokenizer_name)
    smart_paste_prompt_formatter = ForgerPromptFormatter(
        tokenizer,
        handler_config.token_apportionment,
    )

    instruction_handler = InstructionHandler(
        client=InfererClient(inference_stub_factory),
        instruction_prompt_formatter=None,
        smart_paste_prompt_formatter=smart_paste_prompt_formatter,
        namespace=namespace,
        ri_publisher=ri_publisher,
        metrics=metrics,
        retriever=retriever,
        max_output_length=handler_config.max_output_length,
        sampling_params=handler_config.sampling_params,
        tokenizer=tokenizer,
    )
    return instruction_handler

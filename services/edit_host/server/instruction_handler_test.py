"""Unit Tests for InstructionHandler."""

import concurrent
import typing
import uuid
from collections import namedtuple
from typing import Iterable, Mapping, Optional, Sequence
from unittest.mock import AN<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, create_autospec, patch
import pathlib

import pytest

from base import feature_flags
from base.blob_names import blob_names_pb2
from base.blob_names.python.blob_names import Blobs
from base.prompt_format_chat.smart_paste_prompt_formatter import (
    SmartPastePromptInput,
)
from base.prompt_format_smart_paste.forger_prompt_formatter import (
    ForgerPromptFormatter,
    ForgerSmartPastePromptOutput,
    LineEnding,
)
from base.test_utils.synchronous_executor import SynchronousExecutor
from base.third_party_clients.third_party_model_client import (
    ReplaceTextResponse,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
)
from services.third_party_arbiter.client.client import ThirdPartyArbiterClient
from services.third_party_arbiter.client.mock_client import MockThirdPartyArbiterClient
from services.lib.balanced_third_party_clients.anthropic_balanced import (
    AnthropicBalancedClient,
)
from base.tokenizers.test_utils import MultiTokenTestTokenizer
from base.tokenizers.tokenizer import SpecialTokens, Tokenizer
from services.edit_host import edit_pb2
from services.edit_host.server.handler import InstructionResult, ReplaceText
from services.edit_host.server.instruction_handler import (
    InstructionHandler,
    SamplingParams,
    create_instruction_handler_third_party,
    postprocess_replace_text_stream,
)
from services.edit_host.server.instruction_handler_metrics import (
    InstructionHandlerMetrics,
)
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)

# from services.inference_host.infer_pb2_grpc import InferenceClientProtocol
from services.inference_host.infer_pb2 import InferResponse
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalResult,
    Retriever,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

feature_flags.unit_test_setup()


@pytest.fixture()
def feature_flags_context() -> (
    typing.Generator[feature_flags.LocalFeatureFlagSetter, None, None]
):
    yield from feature_flags.feature_flag_fixture()


def _update_nested_dict(d: dict, u: dict) -> dict:
    for k, v in u.items():
        if isinstance(v, dict):
            d[k] = _update_nested_dict(d.get(k, {}), v)
        else:
            d[k] = v
    return d


def _get_instruction_handler_third_party(
    updates_for_handler_config: Optional[dict] = None,
    retrieved_chunks: Optional[list[RetrievalChunk]] = None,
    responses: Iterable[ThirdPartyModelResponse] | None = None,
    arbiter_client: Optional[ThirdPartyArbiterClient] = None,
) -> InstructionHandler:
    """Factory function for creating instruction handler for tests.

    Args:
        updates_for_handler_config: Modifies default values of handler config.
    """
    handler_config = {
        "gcp_project_id": "test-project",
        "gcp_region": "us-central1",
        "anthropic_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "openai_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "xai_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "fireworks_api_key_path": "test-key-path/key.txt",  # pragma: allowlist secret
        "client_type": "anthropic_vertexai",
        "model_name": "claude-3-5-sonnet-v2@20241022",
        "temperature": 0.6,
        "max_output_length": 8192,
        "instruction_token_apportionment": {
            "prefix_len": 1024,
            "suffix_len": 1024,
            "path_len": 100,
            "chat_history_len": 2048,
            "retrieval_len": 2048,
            "max_prompt_len": 16384,
        },
        "smart_paste_token_apportionment": {
            "prefix_len": 1024,
            "suffix_len": 1024,
            "path_len": 100,
            "chat_history_len": 2048,
            "target_file_path_len": 100,
            "target_file_content_len": 2048,
            "max_prompt_len": 16384,
        },
    }

    if updates_for_handler_config is not None:
        _update_nested_dict(handler_config, updates_for_handler_config)

    ri_publisher = MagicMock(RequestInsightPublisher)
    retriever = MagicMock(Retriever)
    retriever.retrieve.return_value = RetrievalResult(
        retrieved_chunks=retrieved_chunks or [],
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    metrics = InstructionHandlerMetrics()
    handler = create_instruction_handler_third_party(
        handler_config,
        "test-namespace",
        ri_publisher,
        metrics,
        retriever,
        arbiter_client=arbiter_client,
    )
    handler.client = MagicMock(ThirdPartyModelClient, auto_spec=True)
    handler.client.generate_response_stream.return_value = responses or []

    return handler


def _get_smart_paste_request(
    prefix: str = "",
    selected_text: str = "",
    suffix: str = "",
    prefix_begin: int = 0,
    suffix_end: int = 0,
    path: str = "src/foo.py",
    blob_name: str = "blob0",
    lang: str = "python",
    blobs: blob_names_pb2.Blobs | None = Blobs.from_fake_blob_names(
        ["blob1", "blob2"]
    ).to_proto(),
    context_code_exchange_request_id: str = "new",
    code_block: str = "def foo():\n    return 1\n",
    target_file_path: str = "src/foo.py",
    target_file_content: str = "def foo():\n    pass\n",
) -> edit_pb2.InstructionRequest:
    """Factory function for creating instruction request."""

    edit_position = edit_pb2.EditPosition(
        prefix_begin=prefix_begin,
        suffix_end=suffix_end,
        blob_name=blob_name,
    )

    instruction_request = edit_pb2.InstructionRequest(
        model_name="test_model",
        path=path,
        prefix=prefix,
        selected_text=selected_text,
        suffix=suffix,
        position=edit_position,
        lang=lang,
        sequence_id=0,
        blobs=blobs,
        code_block=code_block,
        target_file_path=target_file_path,
        target_file_content=target_file_content,
        context_code_exchange_request_id=context_code_exchange_request_id,
    )

    return instruction_request


def _get_instruction_request(
    prefix: str = "",
    selected_text: str = "def foo():\n    return True\n",
    suffix: str = "",
    instruction: str = "Do something",
    prefix_begin: int = 0,
    suffix_end: int = 0,
    path: str = "src/foo.py",
    blob_name: str = "blob0",
    lang: str = "python",
    blobs: blob_names_pb2.Blobs | None = Blobs.from_fake_blob_names(
        ["blob1", "blob2"]
    ).to_proto(),
) -> edit_pb2.InstructionRequest:
    """Factory function for creating instruction request."""

    edit_position = edit_pb2.EditPosition(
        prefix_begin=prefix_begin,
        suffix_end=suffix_end,
        blob_name=blob_name,
    )

    instruction_request = edit_pb2.InstructionRequest(
        model_name="test_model",
        path=path,
        prefix=prefix,
        selected_text=selected_text,
        suffix=suffix,
        instruction=instruction,
        position=edit_position,
        lang=lang,
        sequence_id=0,
        blobs=blobs,
    )

    return instruction_request


def _run_instruction_stream(
    handler: InstructionHandler,
    instruction_request: edit_pb2.InstructionRequest,
    request_id: uuid.UUID | None = None,
    session_id: uuid.UUID | None = None,
) -> tuple[
    Iterable[InstructionResult],
    RequestContext,
    AuthInfo,
]:
    if request_id is None:
        request_id = uuid.UUID("3b608c4f-b7da-4c05-8368-b7414fcd54b7")
    if session_id is None:
        session_id = uuid.UUID("e02cf196-4384-4c56-98c7-a144199038b8")

    request_context = RequestContext(
        request_id=str(request_id),
        request_session_id=str(session_id),
        request_source="unknown",
    )
    auth_info = MagicMock()

    executor = SynchronousExecutor()
    instruction_result_iterator = handler.instruction_stream(
        instruction_request,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    return instruction_result_iterator, request_context, auth_info


@patch("services.edit_host.server.instruction_handler.AnthropicBalancedClient")
@patch("pathlib.Path.read_text")
def test_create_anthropic_balanced_client(
    mock_read_text,
    MockAnthropicBalancedClientClass,
):
    """Tests that AnthropicBalancedClient is initialized correctly."""
    mock_read_text.return_value = "dummy-api-key"
    mock_arbiter_client = typing.cast(
        ThirdPartyArbiterClient, MockThirdPartyArbiterClient()
    )

    _ = (
        _get_instruction_handler_third_party(
            updates_for_handler_config={"client_type": "anthropic_balanced"},
            arbiter_client=mock_arbiter_client,
        ),
    )
    MockAnthropicBalancedClientClass.assert_called_once()
    _, called_kwargs = MockAnthropicBalancedClientClass.call_args

    assert called_kwargs["arbiter_client"] == mock_arbiter_client


def test_create_anthropic_balanced_client_no_arbiter():
    """Tests that ValueError is raised if arbiter_client is not provided for anthropic_balanced."""
    with pytest.raises(ValueError) as excinfo:
        _get_instruction_handler_third_party(
            updates_for_handler_config={"client_type": "anthropic_balanced"},
            arbiter_client=None,  # Explicitly set to None
        )
    assert "arbiter_client is required" in str(excinfo.value)


@patch("services.edit_host.server.instruction_handler.InstructionRequestInsightBuilder")
def test_basic_instruction_ri(InstructionRequestInsightBuilder_Mock):
    """Tests the basic usage of request insight for instructions."""
    instruction_request = _get_instruction_request()
    instruction_response = ThirdPartyModelResponse(
        text="",
        replace_text_response=ReplaceTextResponse(
            old_text="def foo(a, b):\n",
            replacement_text="def bar(c, d):\n",
            start_line_number=1,
            end_line_number=1,
            sequence_id=0,
        ),
    )
    handler = _get_instruction_handler_third_party(responses=[instruction_response])
    instruction_result_iterator, request_context, auth_info = _run_instruction_stream(
        handler, instruction_request
    )
    result_list = list(instruction_result_iterator)
    assert result_list == [
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text="",
                text="",
                start_line=1,
                end_line=None,
                sequence_id=0,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text="def foo(a, b):\n",
                text="def bar(c, d):\n",
                start_line=1,
                end_line=1,
                sequence_id=0,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text=None,
                text=None,
                start_line=None,
                end_line=3,
                sequence_id=0,
            ),
        ),
    ]

    assert InstructionRequestInsightBuilder_Mock.call_count == 1
    InstructionRequestInsightBuilder_Mock.return_value.record_request.assert_called_once()
    InstructionRequestInsightBuilder_Mock.return_value.record_response.assert_called_once()

    InstructionRequestInsightBuilder_Mock.return_value.record_request.assert_called_with(
        prompt_output=ANY,
        request=instruction_request,
        request_context=request_context,
        auth_info=auth_info,
    )
    expected_instruction_aggregate_response = edit_pb2.InstructionAggregateResponse(
        text="",
        replace_text=[
            edit_pb2.ReplaceText(
                old_text="def foo(a, b):\n",
                text="def bar(c, d):\n",
                start_line=1,
                end_line=3,
                sequence_id=0,
            )
        ],
    )
    InstructionRequestInsightBuilder_Mock.return_value.record_response.assert_called_with(
        expected_instruction_aggregate_response,
        truncated_output="",
        request_context=request_context,
        auth_info=auth_info,
        error_message=None,
    )


@pytest.mark.parametrize("newline", ["\r\n", "\n"])
def test_newline_handling(feature_flags_context, newline):
    """Tests that we correctly handle new lines"""

    instruction_request = _get_instruction_request(
        selected_text="def foo():\n    return True\n".replace("\n", newline)
    )
    instruction_response = ThirdPartyModelResponse(
        text="",
        replace_text_response=ReplaceTextResponse(
            old_text="def foo(a, b):\n",
            replacement_text="def bar(c, d):\n",
            start_line_number=1,
            end_line_number=1,
            sequence_id=0,
        ),
    )

    handler = _get_instruction_handler_third_party(responses=[instruction_response])
    instruction_result_iterator, request_context, auth_info = _run_instruction_stream(
        handler, instruction_request
    )
    result_list = list(instruction_result_iterator)
    assert result_list == [
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text="",
                text="",
                start_line=1,
                end_line=None,
                sequence_id=0,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text="def foo(a, b):\n",
                text="def bar(c, d):\n".replace("\n", newline),
                start_line=1,
                end_line=1,
                sequence_id=0,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text=None,
                text=None,
                start_line=None,
                end_line=3,
                sequence_id=0,
            ),
        ),
    ]


@patch("services.edit_host.server.instruction_handler.InstructionRequestInsightBuilder")
def test_multiple_replace_text_responses_agg_ri(InstructionRequestInsightBuilder_Mock):
    """Tests the basic usage of request insight for instructions."""
    instruction_request = _get_instruction_request()
    instruction_responses = [
        ThirdPartyModelResponse(
            text="",
            replace_text_response=ReplaceTextResponse(
                old_text="def foo(a, b):\n",
                replacement_text="def bar(c, d):\n",
                start_line_number=1,
                end_line_number=2,
                sequence_id=0,
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            replace_text_response=ReplaceTextResponse(
                old_text="def foo(x, y):\n",
                replacement_text=None,
                start_line_number=None,
                end_line_number=None,
                sequence_id=1,
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            replace_text_response=ReplaceTextResponse(
                old_text=None,
                replacement_text=None,
                start_line_number=3,
                end_line_number=None,
                sequence_id=1,
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            replace_text_response=ReplaceTextResponse(
                old_text=None,
                replacement_text=None,
                start_line_number=None,
                end_line_number=4,
                sequence_id=1,
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            replace_text_response=ReplaceTextResponse(
                old_text=None,
                replacement_text="def bar",
                start_line_number=3,
                end_line_number=4,
                sequence_id=1,
            ),
        ),
        ThirdPartyModelResponse(
            text="",
            replace_text_response=ReplaceTextResponse(
                old_text=None,
                replacement_text="(z, w):\n",
                start_line_number=None,
                end_line_number=None,
                sequence_id=1,
            ),
        ),
    ]
    handler = _get_instruction_handler_third_party(responses=instruction_responses)
    instruction_result_iterator, request_context, auth_info = _run_instruction_stream(
        handler, instruction_request
    )
    result_list = list(instruction_result_iterator)

    expected_results = [
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text="",
                text="",
                start_line=1,
                end_line=None,
                sequence_id=0,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text="def foo(a, b):\n",
                text="def bar(c, d):\n",
                start_line=1,
                end_line=2,
                sequence_id=0,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text="def foo(x, y):\n",
                text=None,
                start_line=None,
                end_line=None,
                sequence_id=1,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text=None,
                text=None,
                start_line=3,
                end_line=None,
                sequence_id=1,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text=None,
                text=None,
                start_line=None,
                end_line=4,
                sequence_id=1,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text=None,
                text="def bar",
                start_line=3,
                end_line=4,
                sequence_id=1,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text=None,
                text="(z, w):\n",
                start_line=None,
                end_line=None,
                sequence_id=1,
            ),
        ),
        InstructionResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=False,
            replace_text=ReplaceText(
                old_text=None,
                text=None,
                start_line=None,
                end_line=3,
                sequence_id=0,
            ),
        ),
    ]

    assert result_list == expected_results
    assert InstructionRequestInsightBuilder_Mock.call_count == 1
    InstructionRequestInsightBuilder_Mock.return_value.record_request.assert_called_once()
    InstructionRequestInsightBuilder_Mock.return_value.record_response.assert_called_once()
    InstructionRequestInsightBuilder_Mock.return_value.record_request.assert_called_with(
        prompt_output=ANY,
        request=instruction_request,
        request_context=request_context,
        auth_info=auth_info,
    )
    expected_instruction_aggregate_response = edit_pb2.InstructionAggregateResponse(
        text="",
        replace_text=[
            edit_pb2.ReplaceText(
                old_text="def foo(a, b):\n",
                text="def bar(c, d):\n",
                start_line=1,
                end_line=3,
                sequence_id=0,
            ),
            edit_pb2.ReplaceText(
                old_text="def foo(x, y):\n",
                text="def bar(z, w):\n",
                start_line=3,
                end_line=4,
                sequence_id=1,
            ),
        ],
    )
    InstructionRequestInsightBuilder_Mock.return_value.record_response.assert_called_with(
        expected_instruction_aggregate_response,
        truncated_output="",
        request_context=request_context,
        auth_info=auth_info,
        error_message=None,
    )


class TestSpecialTokens(SpecialTokens):
    def __init__(self):
        self.padding = -1
        self.eos = -2
        self.newline = 0  # Special newline token


class TestTokenizer(Tokenizer):
    def __init__(self):
        self._special_tokens = TestSpecialTokens()
        self._vocab = {
            i: chr(i) for i in range(1, 128)
        }  # Simple vocab mapping using ASCII symbols
        self._vocab[self._special_tokens.newline] = "\n"
        self._inv_vocab = {v: k for k, v in self._vocab.items()}

    @property
    def vocab_size(self) -> int:
        return len(self._vocab)

    @property
    def vocab(self) -> Mapping[bytes, int]:
        return {v.encode(): k for k, v in self._vocab.items()}

    def tokenize_safe(self, text: str) -> list[int]:
        return [self._inv_vocab[c] for c in text]

    def tokenize_unsafe(self, text: str) -> list[int]:
        return self.tokenize_safe(text)  # For simplicity, same as safe tokenization

    def detokenize(self, token_ids: Sequence[int]) -> str:
        return "".join(self._vocab[token_id] for token_id in token_ids)

    def detokenize_with_offsets(
        self, token_ids: Sequence[int]
    ) -> tuple[str, list[int]]:
        text = self.detokenize(token_ids)
        offsets = list(range(len(text)))
        return text, offsets

    @property
    def special_tokens(self) -> TestSpecialTokens:
        return self._special_tokens


class MockForgerPromptFormatter(ForgerPromptFormatter):
    def __init__(
        self, used_lines: list[str], start_line_number: int, end_line_number: int
    ):
        self.tokenizer = TestTokenizer()
        self.used_lines = used_lines
        self.start_line_number = start_line_number
        self.end_line_number = end_line_number

    def format_prompt(
        self,
        prompt_input: SmartPastePromptInput,
        force_fuzzy_search: bool = False,
    ) -> ForgerSmartPastePromptOutput:
        return ForgerSmartPastePromptOutput(
            tokens=[1, 2, 3],  # Example tokens
            used_lines=self.used_lines,
            start_line_number=self.start_line_number,
            end_line_number=self.end_line_number,
            unused_prefix="",
            unused_suffix="",
            original_line_ending=LineEnding.LF,
        )


def _get_instruction_handler_inferer(
    used_lines: list[str],
    start_line_number: int,
    end_line_number: int,
    responses: list[InferResponse],
) -> InstructionHandler:
    """Factory function for creating instruction handler with InfererClient for tests."""
    handler_config = {
        "tokenizer_name": "test_tokenizer",
        "max_output_length": 1000,
        "sampling_params": SamplingParams(
            top_k=0, top_p=0.95, temperature=0.7, seed=0, inference_timeout_s=60.0
        ),
        "token_apportionment": {
            "prefix_len": 1024,
            "suffix_len": 1024,
            "path_len": 100,
            "chat_history_len": 2048,
            "retrieval_len": 2048,
            "max_prompt_len": 16384,
        },
        "post_process_config": {},
    }

    ri_publisher = MagicMock(RequestInsightPublisher)
    retriever = MagicMock(Retriever)
    retriever.retrieve.return_value = RetrievalResult(
        retrieved_chunks=[],
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    metrics = InstructionHandlerMetrics()
    tokenizer = TestTokenizer()

    inferer_client = MagicMock(InfererClient)
    if responses:
        inferer_client.infer_stream.return_value = iter(responses)

    mock_formatter = MockForgerPromptFormatter(
        used_lines=used_lines,
        start_line_number=start_line_number,
        end_line_number=end_line_number,
    )
    handler = InstructionHandler(
        client=inferer_client,
        instruction_prompt_formatter=None,
        smart_paste_prompt_formatter=mock_formatter,
        namespace="test-namespace",
        ri_publisher=ri_publisher,
        metrics=metrics,
        retriever=retriever,
        max_output_length=handler_config["max_output_length"],
        sampling_params=handler_config["sampling_params"],
        tokenizer=tokenizer,
    )

    return handler


@pytest.mark.parametrize(
    "old_text, replacement_text, start_line_number, end_line_number, new_text, expected_start_line_number, expected_end_line_number",
    [
        ("123\n123\n", "456\n", 0, 2, "456\n", 0, 2),
        ("123\n123\n", "456\n", 1, 2, "456\n", 1, 2),
        ("123\n123", "456", 1, 2, "456", 1, 2),
        ("123\n123", "456\n", 1, 2, "456\n", 1, 2),
        ("123\n123\n123\n", "123\n456\n", 1, 3, "456\n", 2, 3),
        ("", "123\n", 0, 0, "123\n", 0, 0),
        ("123\n456\n789\n", "", 1, 3, "", 1, 3),
        ("123\n456\n789\n", "", 1, 2, "", 1, 2),
        ("123\n456\n789\n", "123\n456\n123\n", 0, 3, "123\n", 2, 3),
        ("123\n456\n789\n", "123\n456\n123\n456", 0, 3, "123\n456", 2, 3),
        ("123\n456\n789\n", "123\n456\n789\n", 0, 3, "", 3, 3),
        ("123\n456\n789\n", "123\n456\n\n789\n", 0, 3, "\n789\n", 2, 3),
        ("123\n456\n789\n", "123\n456\n\n789", 0, 3, "\n789", 2, 3),
        (
            "123\n456\n789\n",
            "111\n222\n",
            0,
            2,
            "111\n222\n",
            0,
            2,
        ),  # Replace first two lines
        ("123\n456\n789\n", "111\n", 1, 2, "111\n", 1, 2),  # Replace middle line
        (
            "123\n456\n789\n",
            "111\n222\n333\n",
            0,
            3,
            "111\n222\n333\n",
            0,
            3,
        ),  # Replace all lines
        (
            "123\n456\n789\n",
            "111\n456\n789\n",
            0,
            1,
            "111\n456\n789\n",
            0,
            1,
        ),  # Insert more lines at the beginning
        (
            "123\n456\n789\n",
            "123\n456\n111\n",
            2,
            3,
            "123\n456\n111\n",
            2,
            3,
        ),  # Replace last line only
        (
            "123\n456\n789\n",
            "123\n456\n111\n",
            0,
            3,
            "111\n",
            2,
            3,
        ),
        (
            "123\n456\n789\n",
            "123\n111\n222\n789\n",
            1,
            2,
            "123\n111\n222\n789\n",
            1,
            2,
        ),
        (
            "123\n456\n789\n",
            "123\n111\n222\n789\n",
            0,
            3,
            "111\n222\n789\n",
            1,
            3,
        ),  # Insert extra line in middle
        (
            "123\n456\n789\n",
            "111\n123\n456\n789\n",
            0,
            1,
            "111\n123\n456\n789\n",
            0,
            1,
        ),
    ],
)
def test_instruction_handler_with_inferer_client(
    old_text,
    replacement_text,
    start_line_number,
    end_line_number,
    new_text,
    expected_start_line_number,
    expected_end_line_number,
):
    """Test InstructionHandler with InfererClient for smart paste."""

    request = _get_smart_paste_request()

    tokenizer = TestTokenizer()
    MockReply = namedtuple("MockReply", ["output_tokens", "log_probabilities"])

    new_tokens = []
    for new_line in replacement_text:
        new_tokens.extend(tokenizer.tokenize_safe(new_line))

    responses = [
        MockReply(
            output_tokens=new_tokens,
            log_probabilities=[0.0 for _ in new_tokens],
        )
    ]
    used_lines = old_text.splitlines(True)[start_line_number:end_line_number]
    handler = _get_instruction_handler_inferer(
        used_lines=used_lines,
        start_line_number=start_line_number,
        end_line_number=end_line_number,
        responses=responses,
    )

    instruction_result_iterator, _, _ = _run_instruction_stream(handler, request)
    result_list = list(instruction_result_iterator)

    if len(new_text) == 0:
        new_lines = [""]
    else:
        new_lines = new_text.splitlines(True)

    assert len(result_list) == len(new_lines) + 2

    # 1. Check the start line number.
    assert result_list[0].replace_text is not None
    assert result_list[0].replace_text.start_line == expected_start_line_number + 1
    # End line number should be sent in a separate response.
    assert result_list[0].replace_text.end_line is None
    assert result_list[0].replace_text.text == ""

    # 2. Check the end line number.
    assert result_list[-1].replace_text is not None
    assert result_list[-1].replace_text.start_line is None
    # End line number should be sent in a separate response.
    assert result_list[-1].replace_text.end_line == expected_end_line_number + 1
    assert result_list[-1].replace_text.text == ""

    for i, new_line in enumerate(new_lines):
        current_result = result_list[i + 1].replace_text
        assert current_result is not None
        assert current_result.start_line is None
        assert current_result.end_line is None
        assert current_result.text == new_line

    # Verify that the InfererClient's infer_stream method was called
    assert isinstance(handler.client, InfererClient)
    assert isinstance(handler.client.infer_stream, MagicMock)
    handler.client.infer_stream.assert_called_once()


@pytest.mark.parametrize(
    "line_ending_type,expected_newline",
    [
        # We fix line endings later in instruction_handler,
        # so expected_newline is "\n" in both cases here.
        (LineEnding.CRLF, "\n"),
        (LineEnding.LF, "\n"),
    ],
)
def test_postprocess_replace_text_stream_line_endings(
    line_ending_type, expected_newline
):
    """Tests the postprocess_replace_text_stream function with different line endings."""
    tokenizer = TestTokenizer()

    # Create two separate replies for each line
    mock_reply1 = Mock(spec=InferenceClientProtocol.Reply)
    mock_reply1.output_tokens = tokenizer.tokenize_safe("def new_func():\n")

    mock_reply2 = Mock(spec=InferenceClientProtocol.Reply)
    mock_reply2.output_tokens = tokenizer.tokenize_safe("    return True\n")

    future1 = concurrent.futures.Future()
    future1.set_result(iter([mock_reply1, mock_reply2]))

    responses = list(
        postprocess_replace_text_stream(
            future1,
            tokenizer,
            set([]),
            ForgerSmartPastePromptOutput(
                tokens=[],
                unused_prefix="",
                unused_suffix="",
                used_lines=["def old_func():\n", "    return False\n"],
                original_line_ending=line_ending_type,
                start_line_number=2,  # 0-based line number
                end_line_number=4,  # 0-based line number
            ),
        )
    )

    assert len(responses) == 4
    assert responses[0].replace_text_response is not None
    assert responses[1].replace_text_response is not None
    assert responses[2].replace_text_response is not None
    assert responses[3].replace_text_response is not None

    # First response sets start line
    assert (
        responses[0].replace_text_response.start_line_number == 3
    )  # 1-based line number
    assert responses[0].replace_text_response.end_line_number is None
    # Second response has first line of code
    assert (
        responses[1].replace_text_response.replacement_text
        == f"def new_func():{expected_newline}"
    )
    # Third response has second line of code
    assert (
        responses[2].replace_text_response.replacement_text
        == f"    return True{expected_newline}"
    )
    # Fourth response sets end line
    assert responses[3].replace_text_response.end_line_number == 5


def test_multi_token_character_handling():
    """Test that multi-token characters are properly handled in the instruction stream.

    The test simulates a Unicode character that requires multiple tokens to represent.
    Without proper buffering, the character would be emitted as a replacement character
    when the first token is received. With proper buffering, it should wait for all
    required tokens before reconstructing the character.
    """
    # Create a stream that splits our test character into two tokens
    mock_replies = [
        Mock(spec=InferenceClientProtocol.Reply, output_tokens=[1000]),  # First part
        Mock(spec=InferenceClientProtocol.Reply, output_tokens=[1001]),  # Second part
    ]

    future = concurrent.futures.Future()
    future.set_result(iter(mock_replies))

    responses = list(
        postprocess_replace_text_stream(
            future,
            MultiTokenTestTokenizer(),
            set([]),
            ForgerSmartPastePromptOutput(
                tokens=[],
                unused_prefix="",
                unused_suffix="",
                used_lines=["old text\n"],
                original_line_ending=LineEnding.LF,
                start_line_number=1,
                end_line_number=2,
            ),
        )
    )

    # Reconstruct the text from all responses
    reconstructed_text = ""
    for response in responses:
        if (
            response.replace_text_response
            and response.replace_text_response.replacement_text
        ):
            reconstructed_text += response.replace_text_response.replacement_text

    # Without proper buffering, we would see a replacement character
    # With proper buffering, we should see the actual character
    assert "⌘" in reconstructed_text, "Test character not properly reconstructed"
    assert "\ufffd" not in reconstructed_text, "Found replacement character in output"

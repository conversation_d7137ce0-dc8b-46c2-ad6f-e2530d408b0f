"""Helper to build a request insight events during an instruction request."""

from typing import Sequence
from base.prompt_format_chat.smart_paste_prompt_formatter import SmartPastePromptOutput

from base.prompt_format_smart_paste.forger_prompt_formatter import (
    ForgerSmartPastePromptOutput,
)
from base.tokenizers.tokenizer import Tokenizer
from services.lib.request_context.request_context import RequestContext
from services.edit_host import edit_pb2
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher
from services.lib.grpc.auth.service_auth import AuthInfo


def _stringify_structured_smart_paste_prompt_output(
    structured_prompt: SmartPastePromptOutput,
):
    prompt = ""
    prompt += f"""\
{'-' * 40}
system_prompt:
{'-' * 40}
{structured_prompt.system_prompt}

"""

    prompt += f"""\
{'-' * 40}
current message:
{'-' * 40}
{structured_prompt.message}

{'-' * 40}
chat history (from most recent to least recent):
{'-' * 40}
"""

    for exchange in structured_prompt.chat_history:
        prompt += f"""\
    {'-' * 40}
    user:
    {'-' * 40}
    {exchange.request_message}

    {'-' * 40}
    assistant:
    {'-' * 40}
    {exchange.response_text}

"""

    promptlines = prompt.splitlines(keepends=True)
    # limit to 500k characters (~2 bytes / character => ~1MB max) to avoid
    # bigtable/grpc issues with large messages,
    # and to ease readability.
    prompt = prompt[:500_000]

    # Prepend metadata to start of "prompt"
    prompt = (
        f"""\
{'-' * 40}
Metadata:
    Lines in prompt: {len(promptlines)}

{'-' * 40}
"""
        + prompt
    )

    return prompt


def _get_tokenization_proto_from_structured_prompt(
    prompt: SmartPastePromptOutput,
) -> request_insight_pb2.Tokenization:
    return request_insight_pb2.Tokenization(
        token_ids=[0],
        text=_stringify_structured_smart_paste_prompt_output(prompt),
        offsets=[0],
        log_probs=[1.0],
    )


def _get_tokenization_proto_from_token_prompt(
    tokenizer: Tokenizer,
    token_ids: Sequence[int],
    log_probs: Sequence[float] | None,
) -> request_insight_pb2.Tokenization:
    text, offsets = tokenizer.detokenize_with_offsets(token_ids)
    return request_insight_pb2.Tokenization(
        token_ids=token_ids,
        text=text,
        offsets=offsets,
        log_probs=log_probs or [],
    )


def _get_tokenization_proto_from_str(
    text: str,
) -> request_insight_pb2.Tokenization:
    return request_insight_pb2.Tokenization(
        token_ids=[0],
        text=text,
        offsets=[0],
        log_probs=[1.0],
    )


class SmartPasteRequestInsightBuilder:
    """Class to log a request insight events during a chat request."""

    def __init__(self, ri_publisher: request_insight_publisher.RequestInsightPublisher):
        self.ri_publisher = ri_publisher

    def record_request(
        self,
        prompt_output: SmartPastePromptOutput | ForgerSmartPastePromptOutput,
        tokenizer: Tokenizer | None,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a chat request event."""
        request_event = request_insight_publisher.new_event()
        ri_request = request_insight_pb2.RIInstructionRequest(request=request)
        if isinstance(prompt_output, ForgerSmartPastePromptOutput):
            assert tokenizer is not None
            ri_request.tokenization.CopyFrom(
                _get_tokenization_proto_from_token_prompt(
                    tokenizer, prompt_output.tokens, None
                )
            )
        else:
            ri_request.tokenization.CopyFrom(
                _get_tokenization_proto_from_structured_prompt(prompt_output)
            )

        # The final logging
        request_event.instruction_host_request.MergeFrom(ri_request)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [request_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_response(
        self,
        response: edit_pb2.InstructionAggregateResponse,
        request_context: RequestContext,
        auth_info: AuthInfo,
        error_message: str | None = None,
    ):
        response_event = request_insight_publisher.new_event()
        ri_response = request_insight_pb2.RIInstructionResponse(
            response=response, error_message=error_message
        )

        ri_response.tokenization.CopyFrom(
            _get_tokenization_proto_from_str(response.text)
        )

        response_event.instruction_host_response.MergeFrom(ri_response)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

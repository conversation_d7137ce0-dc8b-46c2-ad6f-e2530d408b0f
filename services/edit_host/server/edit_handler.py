"""Contains the core logic of handling edit requests."""

import concurrent.futures
from dataclasses import dataclass
from typing import Iterator, Optional, Sequence

import opentelemetry.trace
from services.inference_host.client.multiplex import InferenceStubFactoryProtocol
import structlog
from dataclasses_json import dataclass_json

from base.blob_names.python.blob_names import Blobs
from base.prompt_format_completion.prompt_formatter import <PERSON><PERSON><PERSON><PERSON>
from base.prompt_format_edit import get_code_edit_prompt_formatter_by_name
from base.prompt_format_edit.prompt_formatter import (
    EditPromptFormatter,
    EditPromptInput,
    EditTokenApportionment,
)
from base.prompt_format_retrieve.prompt_formatter import InstructRetrieverPromptInput
from base.tokenizers import Tokenizer, create_tokenizer_by_name
from services.lib.retrieval.retriever import (
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.edit_host import edit_pb2
from services.edit_host.server.edit_request_insight_builder import (
    EditRequestInsightBuilder,
)
from services.edit_host.server.handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    InstructionR<PERSON>ult,
)
from services.edit_host.server.post_processing import PostprocessingConfig, post_process
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.inference_host.infer_pb2_grpc import InfererStub
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

logger = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)


@dataclass_json
@dataclass(frozen=True)
class SamplingParams:
    """The sampling parameters for the code edit model. See infer.proto for meaning."""

    top_k: int = 0
    """Top-k sampling parameter."""

    top_p: float = 0.0
    """Top-p sampling parameter."""

    temperature: float = 0.0
    """Temperature sampling parameter."""

    seed: int = 0
    """Random seed that is passed to inference host."""

    inference_timeout_s: float = 30.0
    """Timeout that is passed to inference host."""

    def __post_init__(self):
        assert 0.0 <= self.top_p <= 1.0, "top_p must be [0, 1]."
        assert self.top_k >= 0, "top_k must be non-negative."
        assert self.temperature >= 0, "temperature must be non-negative."
        assert self.seed >= 0, "seed must be non-negative."
        assert self.inference_timeout_s > 0, "inference_timeout_s must be positive."


@dataclass_json
@dataclass(frozen=True)
class EditHandlerConfig:
    """Config for edit model."""

    tokenizer_name: str

    prompt_formatter_name: str

    token_apportionment: EditTokenApportionment

    max_context_length: int
    """The maximal length of the context for the code edit model."""

    sampling_params: SamplingParams
    """The sampling parameters for the code edit model."""

    post_process_config: PostprocessingConfig
    """The post-processing configuration."""

    max_output_length: int | None = None
    """The maximal length of the output for the code edit model."""


@dataclass(frozen=True)
class TrimmedOutputTokens:
    """Result of _trim_output_tokens."""

    truncated_tokens: Sequence[int]
    is_truncated: bool
    truncated_log_probs: Optional[Sequence[float]]


def _trim_output_tokens(
    eos_to_check: set[int], tokens: Sequence[int], log_probs: Optional[Sequence[float]]
) -> TrimmedOutputTokens:
    """Trims tokens and log_probs."""

    is_truncated = False
    for i, token in enumerate(tokens):
        if token in eos_to_check:
            tokens = tokens[:i]
            is_truncated = True
            if log_probs is not None:
                log_probs = log_probs[:i]
            break

    return TrimmedOutputTokens(
        truncated_tokens=tokens,
        is_truncated=is_truncated,
        truncated_log_probs=log_probs,
    )


class EditHandler(EditHandlerProtocol):
    """Handles edit requests."""

    def __init__(
        self,
        stub_factory: InferenceStubFactoryProtocol,
        tokenizer: Tokenizer,
        prompt_formatter: EditPromptFormatter,
        max_context_length: int,
        namespace: str,
        sampling_params: SamplingParams,
        post_process_config: PostprocessingConfig,
        ri_publisher: RequestInsightPublisher,
        retriever: Optional[Retriever[InstructRetrieverPromptInput]] = None,
    ):
        self.client = InfererClient(stub_factory)
        self.tokenizer = tokenizer
        self.prompt_formatter = prompt_formatter
        self.max_context_length = max_context_length
        self.namespace = namespace
        self.sampling_params = sampling_params
        self.post_process_config = post_process_config
        self.retriever = retriever
        self.end_token_ids = {self.tokenizer.special_tokens.eos}
        self.ri_builder = EditRequestInsightBuilder(ri_publisher)

    def edit(
        self,
        request: edit_pb2.EditRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> EditResult:
        with tracer.start_as_current_span("retrieval"):
            retrieval_result = self._retrieve(
                request, request_context, auth_info, executor
            )
            retrieved_chunks = [
                x.to_prompt_chunk() for x in retrieval_result.get_retrieved_chunks()
            ]

        with tracer.start_as_current_span("format_prompt"):
            prompt_input = EditPromptInput(
                path=request.path,
                prefix=request.prefix,
                selected_code=request.selected_text,
                suffix=request.suffix,
                instruction=request.instruction,
                prefix_begin=request.position.prefix_begin,
                suffix_end=request.position.suffix_end,
                retrieved_chunks=retrieved_chunks,
            )
            prompt_output = self.prompt_formatter.format_prompt(prompt_input)
            logger.debug(
                f"Received {len(retrieved_chunks)} chunks, and #final-prompt-tokens={len(prompt_output.tokens)}"
            )

        with tracer.start_as_current_span("inference"):
            reply_future = executor.submit(
                self._infer,
                prompt_output.tokens,
                request_context,
                request,
            )

            executor.submit(
                self.ri_builder.record_request,
                prompt_output,
                self.tokenizer,
                request,
                request_context=request_context,
                auth_info=auth_info,
            )

            reply = reply_future.result()

        with tracer.start_as_current_span("post_process"):
            trim_result = _trim_output_tokens(
                eos_to_check=self.end_token_ids,
                tokens=reply.output_tokens,
                log_probs=reply.log_probs,
            )
            output_text = self.tokenizer.detokenize(trim_result.truncated_tokens)
            output_text = post_process(output_text, self.post_process_config)
            edit_result = EditResult(
                output_text,
                list(retrieval_result.get_missing_blob_names()),
                retrieval_result.get_checkpoint_not_found(),
            )
            executor.submit(
                self.ri_builder.record_response,
                edit_result.to_edit_response_proto(),
                tokenizer=self.tokenizer,
                truncated_output_tokens=trim_result.truncated_tokens,
                truncated_log_probs=trim_result.truncated_log_probs,
                request_context=request_context,
                auth_info=auth_info,
            )

        return edit_result

    def instruction_stream(
        self,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> Iterator[InstructionResult]:
        raise NotImplementedError(
            "EditHandler does not support instruction stream requests"
        )

    def _infer(
        self,
        input_tokens: TokenList,
        request_context: RequestContext,
        request: edit_pb2.EditRequest,
    ) -> InferenceClientProtocol.Reply:
        assert len(input_tokens) < self.max_context_length
        reply = self.client.infer(
            input_tokens=input_tokens,
            max_output_length=self.max_context_length - len(input_tokens),
            end_token_ids=self.end_token_ids,
            top_k=self.sampling_params.top_k,
            top_p=self.sampling_params.top_p,
            temperature=self.sampling_params.temperature,
            random_seed=self.sampling_params.seed,
            request_context=request_context,
            timeout_s=self.sampling_params.inference_timeout_s,
            sequence_id=request.sequence_id,
        )

        return reply

    def _retrieve(
        self,
        request: edit_pb2.EditRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> RetrievalResult:
        if not request.HasField("blobs"):
            logger.error(
                "Received request %s without blobs", request_context.request_id
            )
            raise ValueError("Received request without blobs")
        blobs = Blobs.from_proto(request.blobs)

        retrieval_input = RetrievalInput(
            prompt_input=InstructRetrieverPromptInput(
                prefix=request.prefix,
                suffix=request.suffix,
                path=request.path,
                selected_code=request.selected_text,
                instruction=request.instruction,
            ),
            blobs=[blobs],
            namespace=self.namespace,
        )

        if (self.retriever is None) or retrieval_input.workspace_empty():
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=(), checkpoint_not_found=False
            )

        retrieval_result = self.retriever.retrieve(
            input_=retrieval_input,
            request_context=request_context,
            auth_info=auth_info,
            executor=executor,
        )

        return retrieval_result


def create_edit_handler(
    config: dict,
    inference_stub_factory: InferenceStubFactoryProtocol,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    retriever: Optional[Retriever[InstructRetrieverPromptInput]] = None,
) -> EditHandler:
    handler_config: EditHandlerConfig = EditHandlerConfig.schema().load(config)  # type: ignore

    tokenizer = create_tokenizer_by_name(handler_config.tokenizer_name)
    prompt_formatter = get_code_edit_prompt_formatter_by_name(
        handler_config.prompt_formatter_name,
        tokenizer,
        handler_config.token_apportionment,
    )

    edit_handler = EditHandler(
        inference_stub_factory,
        tokenizer,
        prompt_formatter,
        handler_config.max_context_length,
        namespace,
        handler_config.sampling_params,
        handler_config.post_process_config,
        ri_publisher,
        retriever,
    )

    return edit_handler

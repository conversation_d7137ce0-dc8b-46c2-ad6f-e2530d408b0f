"""Defines the EditHandler protocol."""

import concurrent.futures
from dataclasses import dataclass
from enum import Enum
from typing import Iterator, Protocol, Sequence

from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.edit_host import edit_pb2


@dataclass
class EditResult:
    """Class storing the result of a edit call."""

    text: str
    """The edit text to be inserted / human readable version of response."""

    unknown_blob_names: Sequence[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool = False
    """True if the blobs checkpoint id is not available."""

    def to_edit_response_proto(self) -> edit_pb2.EditResponse:
        return edit_pb2.EditResponse(
            text=self.text,
            unknown_blob_names=self.unknown_blob_names,
            checkpoint_not_found=self.checkpoint_not_found,
        )


@dataclass
class ReplaceText:
    old_text: str | None
    """The old text to replace."""

    text: str | None
    """The new text to paste."""

    start_line: int | None
    """The line in which to start the replacement, relative to sent code (incl. prefix)."""

    end_line: int | None
    """The line in which to end the replacement, relative to sent code (incl. suffix)."""

    sequence_id: int
    """The sequence id of the replace text block currently streaming."""

    def to_proto(self) -> edit_pb2.ReplaceText:
        return edit_pb2.ReplaceText(
            old_text=self.old_text,
            text=self.text,
            start_line=self.start_line,
            end_line=self.end_line,
            sequence_id=self.sequence_id,
        )


class InstructionResultStatusCode(Enum):
    OK = 0  # No error
    EXCEED_CONTEXT_LENGTH = 1


@dataclass
class InstructionResult:
    """Class storing the result of a edit call."""

    text: str
    """human readable version of response."""

    unknown_blob_names: Sequence[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool = False
    """True if the blobs checkpoint id is not available."""

    replace_text: ReplaceText | None = None

    status_code: InstructionResultStatusCode | None = None

    def to_proto(self) -> edit_pb2.InstructionResponse:
        r = edit_pb2.InstructionResponse(
            text=self.text,
            unknown_blob_names=self.unknown_blob_names,
            checkpoint_not_found=self.checkpoint_not_found,
            replace_text=self.replace_text.to_proto() if self.replace_text else None,
        )
        if self.status_code:
            r.status_code = edit_pb2.InstructionResponseStatusCode.Value(
                self.status_code.name
            )
        return r


class EditHandlerProtocol(Protocol):
    """Handler for Edit.

    Contains the main business logic of edits.
    """

    def edit(
        self,
        request: edit_pb2.EditRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> EditResult:
        """Edits the given request.

        Args:
            request: The edit request.
            request_context: The request context for the request.
            executor: An executor pool for the handler. Exclusive per handler.

        Returns:
            A edit result.
        """
        raise NotImplementedError()

    def instruction_stream(
        self,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> Iterator[InstructionResult]:
        """Edits the given request.

        Args:
            request: The edit request.
            request_context: The request context for the request.
            executor: An executor pool for the handler. Exclusive per handler.

        Returns:
            A edit result.
        """
        raise NotImplementedError()

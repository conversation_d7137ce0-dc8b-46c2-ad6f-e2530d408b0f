"""Tests for Instruction
RequestInsightBuilder."""

from unittest.mock import MagicMock

from base.blob_names import blob_names_pb2
from base.prompt_format.common import PromptChunk
from base.prompt_format_chat.instruction_prompt_formatter import InstructionPromptOutput
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.edit_host import edit_pb2
from services.edit_host.server.instruction_request_insight_builder import (
    InstructionRequestInsightBuilder,
)
from services.edit_host.server.handler import ReplaceText
from services.request_insight import request_insight_pb2


auth_info = AuthInfo(
    tenant_id="test_tenant_id",
    tenant_name="test_tenant",
    shard_namespace="test_shard_namespace",
    cloud="test_cloud",
)


def _test_update_request(
    request_id: str, events: list[request_insight_pb2.RequestEvent], auth_info: AuthInfo
):
    del auth_info
    return request_insight_pb2.UpdateRequestInfoRequest(
        request_id=request_id,
        events=events,
    )


def test_record_request():
    """Tests that record_request correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = InstructionRequestInsightBuilder(ri_publisher)
    instruction_request = edit_pb2.InstructionRequest(
        model_name="test_model",
        path="test_path",
        prefix="test_prefix",
        selected_text="test_selected_text",
        suffix="test_suffix",
        instruction="test_instruction",
        position=edit_pb2.EditPosition(
            prefix_begin=1, blob_name="test_blob_name", suffix_end=2
        ),
        lang="test_lang",
        sequence_id=0,
        blobs=blob_names_pb2.Blobs(
            baseline_checkpoint_id="test_baseline_checkpoint_id",
            added=[b"test_added_blob"],
            deleted=[b"test_deleted_blob"],
        ),
        code_block="test_code_block",
        target_file_path="test_target_file_path",
        target_file_content="test_target_file_content",
        context_code_exchange_request_id="new",
    )

    prompt_chunk = PromptChunk(
        text="test_text",
        path="test_path",
        char_start=0,
        char_end=1,
        blob_name="test_blob_name",
        origin="test_origin",
    )
    request_context = RequestContext.create()
    ri_builder.record_request(
        prompt_output=InstructionPromptOutput(
            system_prompt="test_system_prompt",
            chat_history=[],
            message="void quicksort",
            retrieved_chunks_in_prompt=[prompt_chunk],
            tools=["replace_text"],
        ),
        request=instruction_request,
        request_context=request_context,
        auth_info=auth_info,
    )

    assert len(ri_publisher.publish_request_insight.call_args.args) == 1
    request = ri_publisher.publish_request_insight.call_args[0][0]
    print(2, request)
    assert request.request_id == request_context.request_id
    assert len(request.events) == 1
    assert request.events[
        0
    ].instruction_host_request == request_insight_pb2.RIInstructionRequest(
        request=instruction_request,
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                text="test_text",
                path="test_path",
                char_offset=0,
                char_end=1,
                blob_name="test_blob_name",
                chunk_index=0,
                origin="test_origin",
            )
        ],
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[0],
            text="""\
----------------------------------------
Metadata:
    Chunks in prompt: 1
    Lines in prompt: 13

Paths in prompt:
    test_path

----------------------------------------
----------------------------------------
system_prompt:
----------------------------------------
test_system_prompt

----------------------------------------
current message:
----------------------------------------
void quicksort

----------------------------------------
chat history (from most recent to least recent):
----------------------------------------
""",
            offsets=[0],
            log_probs=[1],
        ),
    )


def test_record_response():
    """Tests that record_response correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = InstructionRequestInsightBuilder(ri_publisher)
    replace_text_result = ReplaceText(
        old_text="old_text",
        text="new_text",
        start_line=1,
        end_line=1,
        sequence_id=0,
    )
    response_proto = edit_pb2.InstructionAggregateResponse(
        text="test_text",
        unknown_blob_names=[],
        replace_text=[replace_text_result.to_proto()],
    )

    request_context = RequestContext.create()
    ri_builder.record_response(
        response_proto,
        truncated_output="test_text",
        request_context=request_context,
        auth_info=auth_info,
    )

    assert len(ri_publisher.publish_request_insight.call_args.args) == 1
    request = ri_publisher.publish_request_insight.call_args[0][0]
    assert request.request_id == str(request_context.request_id)
    assert len(request.events) == 1
    assert request.events[
        0
    ].instruction_host_response == request_insight_pb2.RIInstructionResponse(
        response=response_proto,
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[0],
            text="test_text",
            offsets=[0],
            log_probs=[1],
        ),
    )

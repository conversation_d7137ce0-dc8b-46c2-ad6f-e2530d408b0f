from prometheus_client import Histogram

INF = float("inf")
retrieval_latency_buckets = tuple(
    [(1.3**i - 1) for i in range(10)] + [INF]
)  # Buckets going up to 9.6s

_edit_retrieval_latency = Histogram(
    "au_edit_retrieval_latency",
    "Latency of the retrieval processing (in the edit host) in seconds",
    ["model", "request_source", "tenant_name"],
    buckets=retrieval_latency_buckets,
)


class InstructionHandlerMetrics:
    """Common metrics for instruction handlers."""

    def __init__(self):
        self.edit_retrieval_latency = _edit_retrieval_latency

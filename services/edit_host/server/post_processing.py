"""Post-processing library for the code edit hosts."""

import re
from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass(frozen=True)
class PostprocessingConfig:
    """Configuration for the code edit post-processing."""

    remove_triple_backticks_at_end: bool = False


def post_process(
    response: str,
    config: PostprocessingConfig,
) -> str:
    """Post-process an code edit suggestion.

    Args:
        response: The code edit suggestion.
        config: The post-processing configuration.

    Returns:
        The post-processed suggestion.
    """
    if config.remove_triple_backticks_at_end:
        match = re.search(r"^(.*)\n```\n*$", response, re.DOTALL)
        response = match.group(1) if match else response
    return response

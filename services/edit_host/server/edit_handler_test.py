"""Unit Tests for <PERSON><PERSON>andler."""

import typing
import uuid
from unittest.mock import ANY, Magic<PERSON>ock, patch

from base.blob_names import blob_names_pb2
from base.blob_names.python.blob_names import Blobs
from base.prompt_format_edit.prompt_formatter import EditPromptInput, EditPromptOutput
from base.prompt_format_retrieve.prompt_formatter import InstructRetrieverPromptInput
from base.test_utils.synchronous_executor import SynchronousExecutor
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.edit_host import edit_pb2
from services.edit_host.server.edit_handler import <PERSON><PERSON>and<PERSON>, create_edit_handler
from services.edit_host.server.handler import EditResult
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.inference_host.infer_pb2_grpc import InfererStub
from services.lib.request_context.request_context import RequestContext
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)


def _update_nested_dict(d: dict, u: dict) -> dict:
    for k, v in u.items():
        if isinstance(v, dict):
            d[k] = _update_nested_dict(d.get(k, {}), v)
        else:
            d[k] = v
    return d


def _get_edit_handler(
    updates_for_handler_config: typing.Optional[dict] = None,
) -> EditHandler:
    """Factory function for creating edit handler.

    Args:
        updates_for_handler_config: Modifies default values of handler config.
    """

    handler_config = {
        "tokenizer_name": "deepseek_coder_instruct",
        "prompt_formatter_name": "droid",
        "token_apportionment": {
            "prefix_len": 1024,
            "suffix_len": 1024,
            "max_context_len": 8192,
            # TODO (c-flaherty, AU-4764): Deprecate by 2024-04-05
            "dynamic_resizing": False,
            "path_len": 100,
            "instruction_len": 100,
            "selected_code_len": 1024,
            "max_prompt_len": 4096,
        },
        "sampling_params": {},
        "post_process_config": {
            "remove_triple_backticks_at_end": True,
        },
        "max_context_length": 8096,
    }

    if updates_for_handler_config is not None:
        _update_nested_dict(handler_config, updates_for_handler_config)

    stub = MagicMock(InfererStub)
    ri_publisher = MagicMock(RequestInsightPublisher)
    retriever = MagicMock(Retriever)
    handler = create_edit_handler(
        handler_config, stub, "test-namespace", ri_publisher, retriever
    )
    handler.client = MagicMock(InfererClient)

    return handler


def _get_edit_request(
    prefix: str = "def ",
    selected_text: str = "bar",
    suffix: str = "(a, b)",
    instruction: str = "Rename bar to foo",
    prefix_begin: typing.Optional[int] = None,
    suffix_end: typing.Optional[int] = None,
    path: str = "src/foo.py",
    blob_name: str = "blob0",
    lang: str = "python",
    blobs: blob_names_pb2.Blobs = Blobs.from_fake_blob_names(
        ["blob1", "blob2"]
    ).to_proto(),
) -> edit_pb2.EditRequest:
    """Factory function for creating edit request."""

    if prefix_begin is None:
        prefix_begin = 0
    if suffix_end is None:
        suffix_end = len(prefix) + len(selected_text) + len(suffix) - 1

    edit_position = edit_pb2.EditPosition(
        prefix_begin=prefix_begin,
        suffix_end=suffix_end,
        blob_name=blob_name,
    )

    edit_request = edit_pb2.EditRequest(
        model_name="test_model",
        path=path,
        prefix=prefix,
        selected_text=selected_text,
        suffix=suffix,
        instruction=instruction,
        position=edit_position,
        lang=lang,
        sequence_id=0,
        blobs=blobs,
    )

    return edit_request


def _run_edit(
    edit_handler: EditHandler,
    edit_request: edit_pb2.EditRequest,
    request_id: typing.Optional[uuid.UUID] = None,
    session_id: typing.Optional[uuid.UUID] = None,
) -> tuple[EditResult, RequestContext]:
    if request_id is None:
        request_id = uuid.UUID("3b608c4f-b7da-4c05-8368-b7414fcd54b7")
    if session_id is None:
        session_id = uuid.UUID("e02cf196-4384-4c56-98c7-a144199038b8")
    request_context = RequestContext(
        request_id=str(request_id),
        request_session_id=str(session_id),
        request_source="unknown",
    )

    with SynchronousExecutor() as executor:
        output = edit_handler.edit(
            edit_request,
            request_context,
            auth_info=None,
            executor=executor,
        )

    return output, request_context


def _set_inference_reply(output: typing.Union[list[int], str], handler: EditHandler):
    if isinstance(output, str):
        tokens = handler.tokenizer.tokenize_unsafe(output)
    else:
        tokens = output
    reply = InferenceClientProtocol.Reply(tokens)
    handler.client.infer.return_value = reply


def test_basic():
    """Tests the basic usage."""
    edit_handler = _get_edit_handler()
    _set_inference_reply("def foo(a, b)", edit_handler)
    output, _ = _run_edit(edit_handler, _get_edit_request())
    assert output.text == "def foo(a, b)"


def test_eos_token_trimming():
    """Tests how handler trims eos tokens."""
    edit_handler = _get_edit_handler()

    eos_token = edit_handler.tokenizer.special_tokens.eos
    for number_of_eos in [1, 2]:
        _set_inference_reply(
            edit_handler.tokenizer.tokenize_unsafe("def foo(a, b)")
            + [eos_token] * number_of_eos,
            edit_handler,
        )
        output, _ = _run_edit(edit_handler, _get_edit_request())
        assert output.text == "def foo(a, b)"


def test_inference_call():
    """Tests that handler correctly calls inference host."""
    edit_handler = _get_edit_handler()
    edit_request = _get_edit_request("a", "b", "c", "d")
    _ = _run_edit(edit_handler, edit_request)

    edit_prompt_input = EditPromptInput("src/foo.py", "a", "b", "c", "d", 0, 2, [])
    edit_prompt_output = edit_handler.prompt_formatter.format_prompt(edit_prompt_input)

    expected_InfererClient_kwargs = {
        "input_tokens": edit_prompt_output.tokens,
        "max_output_length": 8041,
        "end_token_ids": {edit_handler.tokenizer.special_tokens.eos},
        "top_k": 0,
        "top_p": 0.0,
        "temperature": 0.0,
        "random_seed": 0,
        "request_context": ANY,
        "timeout_s": 30.0,
        "sequence_id": 0,
    }
    edit_handler.client.infer.assert_called_with(**expected_InfererClient_kwargs)


def test_prompt_formatter_call():
    """Test that handler correctly calls prompt formatter."""
    edit_handler = _get_edit_handler()
    edit_request = _get_edit_request("a", "b", "c", "d")

    edit_handler.prompt_formatter = MagicMock(edit_handler.prompt_formatter)
    edit_handler.prompt_formatter.format_prompt.return_value = EditPromptOutput(
        tokens=[1, 2, 3], retrieved_chunks_in_prompt=[]
    )

    expected_format_prompt_args = [
        EditPromptInput("src/foo.py", "a", "b", "c", "d", 0, 2, [])
    ]
    _ = _run_edit(edit_handler, edit_request)

    edit_handler.prompt_formatter.format_prompt.assert_called_with(
        *expected_format_prompt_args
    )


@patch("services.edit_host.server.edit_handler.EditRequestInsightBuilder")
def test_basic_ri(EditRequestInsightBuilder_Mock):
    """Tests the basic usage of request insight."""
    edit_handler = _get_edit_handler()
    edit_handler.retriever.retrieve.return_value = RetrievalResult(
        [], [], checkpoint_not_found=False
    )
    edit_request = _get_edit_request()
    edit_response = edit_pb2.EditResponse(
        text="def foo(a, b)", unknown_blob_names=[], checkpoint_not_found=False
    )
    _set_inference_reply("def foo(a, b)", edit_handler)
    _, request_context = _run_edit(edit_handler, edit_request)

    assert EditRequestInsightBuilder_Mock.call_count == 1
    EditRequestInsightBuilder_Mock.return_value.record_request.assert_called_once()
    EditRequestInsightBuilder_Mock.return_value.record_response.assert_called_once()

    EditRequestInsightBuilder_Mock.return_value.record_request.assert_called_with(
        ANY, ANY, edit_request, request_context=request_context, auth_info=None
    )
    EditRequestInsightBuilder_Mock.return_value.record_response.assert_called_with(
        edit_response,
        tokenizer=ANY,
        truncated_output_tokens=[1551, 24531, 7, 64, 11, 270, 8],
        truncated_log_probs=None,
        request_context=request_context,
        auth_info=None,
    )


def test_general_retrieve():
    """Tests basic usage of _retrieve()."""
    edit_handler = _get_edit_handler()
    edit_request = _get_edit_request(prefix_begin=5, suffix_end=15, path="/p1")

    chunks = [
        RetrievalChunk(
            text="a",
            path="/p1",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
        ),
        RetrievalChunk(
            text="b",
            path="/p1",
            char_start=30,
            char_end=40,
            blob_name=None,
            chunk_index=None,
        ),
        RetrievalChunk(
            text="c",
            path="/p3",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
        ),
    ]

    missing_blob_names = ["b1", "b2"]

    assert edit_handler.retriever is not None
    edit_handler.retriever.retrieve.return_value = RetrievalResult(
        chunks, missing_blob_names, checkpoint_not_found=False
    )

    with SynchronousExecutor() as executor:
        retrieval_output = edit_handler._retrieve(
            edit_request,
            RequestContext.create(),
            auth_info=None,
            executor=executor,
        )

    retrieved_chunks = list(retrieval_output.get_retrieved_chunks())
    output_missing_blob_names = set(retrieval_output.get_missing_blob_names())
    assert len(retrieved_chunks) == 3
    assert output_missing_blob_names == set(missing_blob_names)
    assert retrieved_chunks == list(chunks)


def test_retrieve_call():
    """Tests that handler's .edit() correctly calls _retrieve()."""
    edit_handler = _get_edit_handler()
    edit_request = _get_edit_request()

    edit_handler._retrieve = MagicMock(edit_handler._retrieve)

    request_context = RequestContext.create()

    executor = SynchronousExecutor()
    edit_handler.edit(edit_request, request_context, auth_info=None, executor=executor)

    edit_handler._retrieve.assert_called_with(
        edit_request, request_context, auth_info=None, executor=executor
    )

    executor.shutdown()


def test_retriever_call():
    """Tests that Retriever.retrieve is called correctly."""
    edit_handler = _get_edit_handler()
    edit_request = _get_edit_request()

    request_context = RequestContext.create()

    executor = SynchronousExecutor()
    _ = edit_handler.edit(
        edit_request, request_context, auth_info=None, executor=executor
    )

    expected_retrieval_args = {
        "input_": RetrievalInput(
            prompt_input=InstructRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                instruction="Rename bar to foo",
            ),
            blobs=[Blobs.from_fake_blob_names(["blob1", "blob2"])],
            namespace="test-namespace",
        ),
        "request_context": request_context,
        "auth_info": None,
        "executor": executor,
    }

    assert edit_handler.retriever is not None
    edit_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)

    executor.shutdown()


def test_retriever_call_with_checkpoint():
    """Tests that Retriever.retrieve is called correctly with a checkpoint."""
    edit_handler = _get_edit_handler()
    blobs = blob_names_pb2.Blobs(
        baseline_checkpoint_id=None, added=[b"blob3", b"blob4"]
    )
    edit_request = _get_edit_request(blobs=blobs)

    request_context = RequestContext.create()

    executor = SynchronousExecutor()
    _ = edit_handler.edit(
        edit_request, request_context, auth_info=None, executor=executor
    )

    expected_retrieval_args = {
        "input_": RetrievalInput(
            prompt_input=InstructRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                instruction="Rename bar to foo",
            ),
            blobs=[Blobs.from_fake_blob_names(["blob3", "blob4"])],
            namespace="test-namespace",
        ),
        "request_context": request_context,
        "auth_info": None,
        "executor": executor,
    }

    assert edit_handler.retriever is not None
    edit_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)

    executor.shutdown()


def test_empty_blob_list():
    """Tests that retriever is not called when blob list is empty."""
    edit_handler = _get_edit_handler()
    edit_request = _get_edit_request(
        blobs=blob_names_pb2.Blobs(baseline_checkpoint_id=None, added=[], deleted=[]),
    )

    with SynchronousExecutor() as executor:
        retrieval_output = edit_handler._retrieve(
            edit_request,
            RequestContext.create(),
            auth_info=None,
            executor=executor,
        )

    assert len(list(retrieval_output.get_retrieved_chunks())) == 0
    assert len(list(retrieval_output.get_missing_blob_names())) == 0
    assert edit_handler.retriever is not None
    edit_handler.retriever.retrieve.assert_not_called()

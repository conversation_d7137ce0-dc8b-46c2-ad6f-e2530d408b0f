"""Tests for EditRequestInsightBuilder."""

from unittest.mock import <PERSON>Mock

from base.prompt_format_edit.prompt_formatter import EditPromptOutput
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import Request<PERSON>ontext
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer
from services.edit_host import edit_pb2
from services.edit_host.server.edit_request_insight_builder import (
    EditRequestInsightBuilder,
)
from services.edit_host.server.handler import EditResult
from services.request_insight import request_insight_pb2


def _test_update_request(
    request_id: str, events: list[request_insight_pb2.RequestEvent], auth_info: AuthInfo
):
    del auth_info
    return request_insight_pb2.UpdateRequestInfoRequest(
        request_id=request_id,
        events=events,
    )


def test_record_request():
    """Tests that record_request correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = EditRequestInsightBuilder(ri_publisher)
    edit_request = edit_pb2.EditRequest(model_name="test_model")

    request_context = RequestContext.create()
    tokenizer = DeepSeekCoderInstructTokenizer()
    # These tokens refers to "void quicksort"
    ri_builder.record_request(
        prompt_output=EditPromptOutput(
            tokens=[4563, 445, 6388, 439], retrieved_chunks_in_prompt=[]
        ),
        tokenizer=tokenizer,
        request=edit_request,
        request_context=request_context,
        auth_info=None,
    )

    assert len(ri_publisher.publish_request_insight.call_args.args) == 1
    request = ri_publisher.publish_request_insight.call_args[0][0]
    print(2, request)
    assert request.request_id == request_context.request_id
    assert len(request.events) == 1
    assert request.events[0].edit_host_request == request_insight_pb2.RIEditRequest(
        request=edit_request,
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[4563, 445, 6388, 439],
            text="void quicksort",
            offsets=[0, 4, 7, 11],
            log_probs=[],
        ),
    )


def test_record_response():
    """Tests that record_response correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = EditRequestInsightBuilder(ri_publisher)
    edit_result = EditResult(text="test_text", unknown_blob_names=[])
    edit_response = edit_pb2.EditResponse(text="test_text")

    request_context = RequestContext.create()
    ri_builder.record_response(
        edit_result.to_edit_response_proto(),
        tokenizer=DeepSeekCoderInstructTokenizer(),
        truncated_output_tokens=[4563],
        truncated_log_probs=[0.7],
        request_context=request_context,
        auth_info=None,
    )

    assert len(ri_publisher.publish_request_insight.call_args.args) == 1
    request = ri_publisher.publish_request_insight.call_args[0][0]
    assert request.request_id == str(request_context.request_id)
    assert len(request.events) == 1
    assert request.events[0].edit_host_response == request_insight_pb2.RIEditResponse(
        response=edit_response,
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[4563],
            text="void",
            offsets=[0],
            log_probs=[0.7],
        ),
    )


def test_edit_result_to_response_proto():
    """Tests edit_result_to_response_proto function."""
    edit_result = EditResult(
        text="test_text", unknown_blob_names=["a"], checkpoint_not_found=True
    )
    edit_response = edit_pb2.EditResponse(
        text="test_text", unknown_blob_names=["a"], checkpoint_not_found=True
    )

    assert edit_result.to_edit_response_proto() == edit_response

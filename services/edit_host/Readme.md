# Edit Host

The Edit Host works on Code Edits

### Local testing

To test the edit host locally, you can run the following commands:

```bash
kubectl port-forward deployment/edit-droid-33b-fp8-r1-edit 50051:50051 -n "dev-$USER"
```

```bash
bazel run services/edit_host/util -- --endpoint "127.0.0.1:50051" --insecure edit \
    --instruction "replace log by print" \
    --selected-text "  log(\"Hello World\")\n" \
    --prefix "def hello_world() -> str:\n" --suffix "if __name__ == \"__main__\":\n"
```

from services.edit_host.edit_pb2 import (
    InstructionResponse,
    InstructionAggregateResponse,
    ReplaceText,
)


class InstructionStreamAggregator:
    """
    Combines instruction response chunks coming from a stream
    into a single aggregated response.
    """

    def __init__(self):
        self.aggregated_replacements: dict[int, ReplaceText] = {}
        self.aggregated_text = ""

    def add(self, instruction_result: InstructionResponse):
        if instruction_result.text:
            self.aggregated_text += instruction_result.text
        if instruction_result.replace_text:
            replace_text = instruction_result.replace_text
            sequence_id = replace_text.sequence_id
            if sequence_id not in self.aggregated_replacements.keys():
                self.aggregated_replacements[sequence_id] = ReplaceText(
                    old_text="",
                    text="",
                    start_line=None,
                    end_line=None,
                    sequence_id=sequence_id,
                )
            current_replace_text = self.aggregated_replacements[sequence_id]
            if replace_text.old_text is not None:
                assert current_replace_text.old_text is not None
                current_replace_text.old_text += replace_text.old_text
            if replace_text.text is not None:
                assert current_replace_text.text is not None
                current_replace_text.text += replace_text.text

            # `replace_text.start_line` won't work because it returns 0 if field is not set
            if replace_text.HasField("start_line"):
                current_replace_text.start_line = replace_text.start_line
            if replace_text.HasField("end_line"):
                current_replace_text.end_line = replace_text.end_line

    def build(self) -> InstructionAggregateResponse:
        return InstructionAggregateResponse(
            text=self.aggregated_text,
            replace_text=self.aggregated_replacements.values(),
        )

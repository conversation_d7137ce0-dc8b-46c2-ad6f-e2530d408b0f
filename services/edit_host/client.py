"""A Python client library call a edit service."""

import logging
from typing import Optional, Iterable

import grpc

from base.python.grpc import client_options
from services.edit_host.instruction_stream_aggregator import InstructionStreamAggregator
from services.lib.request_context.request_context import RequestContext
from services.edit_host import edit_pb2, edit_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> edit_pb2_grpc.EditStub:
    """Setup the client stub for a edit service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = edit_pb2_grpc.EditStub(channel)
    return stub


class EditClient:
    """Class to call edit APIs remotely."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        self.stub = setup_stub(endpoint, credentials, options=options)

    def edit(
        self,
        request: edit_pb2.EditRequest,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> edit_pb2.EditResponse:
        """Generates the memory content for the given blob."""
        response = self.stub.Edit(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

    def instruction_stream(
        self,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        timeout: float = 120,
    ) -> Iterable[edit_pb2.InstructionResponse]:
        response = self.stub.InstructionStream(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

    def instruction(
        self,
        request: edit_pb2.InstructionRequest,
        request_context: RequestContext,
        timeout: float = 120,
    ) -> edit_pb2.InstructionAggregateResponse:
        """Generates the memory content for the given blob."""
        response_stream = self.stub.InstructionStream(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        aggregator = InstructionStreamAggregator()
        for response in response_stream:
            aggregator.add(response)
        return aggregator.build()

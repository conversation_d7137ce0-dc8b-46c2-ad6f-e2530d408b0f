"""Smart paste stream processor."""

from typing import Iterator


def group_stream_by_line(
    response_iterator: Iterator[str],
) -> Iterator[str]:
    text_buffer = ""
    for response in response_iterator:
        text_buffer += response
        new_line_index = text_buffer.find("\n")
        while new_line_index >= 0:
            yield text_buffer[: new_line_index + 1]
            text_buffer = text_buffer[new_line_index + 1 :]
            new_line_index = text_buffer.find("\n")
    if len(text_buffer):
        yield text_buffer


def compare_with_original_lines(
    response_iterator: Iterator[str], lines: list[str]
) -> Iterator[tuple[str, bool]]:
    all_unchanged = True
    for line_index, line in enumerate(group_stream_by_line(response_iterator)):
        if line_index < len(lines) and line == lines[line_index]:
            yield line, all_unchanged
        else:
            all_unchanged = False
            yield line, all_unchanged

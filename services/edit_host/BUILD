load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library", "pytest_test")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "edit_proto",
    srcs = ["edit.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
    ],
)

py_grpc_library(
    name = "edit_proto_py_proto",
    protos = [":edit_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
    ],
)

go_grpc_library(
    name = "edit_proto_go_proto",
    importpath = "github.com/augmentcode/augment/services/edit_host/proto",
    proto = ":edit_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
    ],
)

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":edit_proto_py_proto",
        ":instruction_stream_aggregator",
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
    ],
)

py_library(
    name = "instruction_stream_aggregator",
    srcs = ["instruction_stream_aggregator.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":edit_proto_py_proto",
    ],
)

pytest_test(
    name = "instruction_stream_aggregator_test",
    srcs = ["instruction_stream_aggregator_test.py"],
    deps = [
        ":instruction_stream_aggregator",
    ],
)

pytest_test(
    name = "smart_paste_stream_processor_test",
    srcs = ["smart_paste_stream_processor_test.py"],
    deps = [
        ":smart_paste_stream_processor",
    ],
)

py_library(
    name = "smart_paste_stream_processor",
    srcs = ["smart_paste_stream_processor.py"],
    visibility = ["//services:__subpackages__"],
    deps = [],
)

# a small CLI utility to issue requests against the edit host API
py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        ":client",
        "//base/logging:console_logging",
        "//services/lib/request_context:request_context_py",
    ],
)

ts_proto_library(
    name = "edit_host_ts_proto",
    data = [
        "//base/blob_names:blob_names_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":edit_proto",
    visibility = ["//services:__subpackages__"],
)

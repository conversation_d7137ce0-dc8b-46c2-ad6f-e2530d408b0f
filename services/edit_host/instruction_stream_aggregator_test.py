from services.edit_host.edit_pb2 import (
    InstructionResponse,
    InstructionAggregateResponse,
    ReplaceText,
)
from services.edit_host.instruction_stream_aggregator import (
    InstructionStreamAggregator,
)


def test_full_replacements():
    aggregator = InstructionStreamAggregator()
    aggregator.add(
        InstructionResponse(
            text="",
            replace_text=ReplaceText(
                old_text="old_text",
                text="new_text",
                start_line=1,
                end_line=1,
                sequence_id=0,
            ),
        )
    )
    aggregator.add(
        InstructionResponse(
            text="",
            replace_text=ReplaceText(
                old_text="old_text2",
                text="new_text2",
                start_line=2,
                end_line=2,
                sequence_id=1,
            ),
        )
    )
    assert aggregator.build() == InstructionAggregateResponse(
        text="",
        replace_text=[
            ReplaceText(
                old_text="old_text",
                text="new_text",
                start_line=1,
                end_line=1,
                sequence_id=0,
            ),
            ReplaceText(
                old_text="old_text2",
                text="new_text2",
                start_line=2,
                end_line=2,
                sequence_id=1,
            ),
        ],
    )


def test_multi_chunk_replacement():
    aggregator = InstructionStreamAggregator()
    aggregator.add(
        InstructionResponse(
            text="",
            replace_text=ReplaceText(
                old_text="old_text1",
                sequence_id=0,
            ),
        )
    )
    aggregator.add(
        InstructionResponse(
            text="",
            replace_text=ReplaceText(
                old_text="\nold_text2\n",
                sequence_id=0,
            ),
        )
    )
    aggregator.add(
        InstructionResponse(
            text="",
            replace_text=ReplaceText(
                start_line=1,
                sequence_id=0,
            ),
        )
    )
    aggregator.add(
        InstructionResponse(
            text="",
            replace_text=ReplaceText(
                end_line=2,
                sequence_id=0,
            ),
        )
    )

    aggregator.add(
        InstructionResponse(
            text="",
            replace_text=ReplaceText(
                text="new_text",
                sequence_id=0,
            ),
        )
    )
    assert aggregator.build() == InstructionAggregateResponse(
        text="",
        replace_text=[
            ReplaceText(
                old_text="old_text1\nold_text2\n",
                text="new_text",
                start_line=1,
                end_line=2,
                sequence_id=0,
            )
        ],
    )

syntax = "proto3";
package edit;

import "base/blob_names/blob_names.proto";

service Edit {
  rpc Edit(EditRequest) returns (EditResponse);
  rpc InstructionStream(InstructionRequest) returns (stream InstructionResponse);
}

// A single exchange between the user and the model.
message Exchange {
  string request_message = 1;
  optional string response_text = 2;
  optional string request_id = 3;
}

// Describe where edit happens in the current file.
// Needed in particular for retrieval.
message EditPosition {
  // The offset in characters where prefix begins, relative to the beginning of file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 prefix_begin = 1;

  // The offset in characters where suffix ends, relative to the beginning of file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 suffix_end = 2;

  // The (last known) blob name of the current file.
  string blob_name = 3;
}

message EditRequest {
  // The name of the model to use.
  string model_name = 1;

  // the path to the file to be edited.
  string path = 2 [debug_redact = true];

  // the prefix prompt at which the edit should be inserted
  string prefix = 3 [debug_redact = true];

  // the text selected in the current buffer
  string selected_text = 4 [debug_redact = true];

  // the text after the selcted text
  string suffix = 5 [debug_redact = true];

  // the instruction to be used for the prompt
  string instruction = 6 [debug_redact = true];

  EditPosition position = 7;

  reserved 8;

  // Programming language of the current file.
  string lang = 9;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  uint32 sequence_id = 10;

  // Blobs to include in the retrieval corpus, specified in the compact
  // delta form. Replaces the blob_names field.
  base.blob_names.Blobs blobs = 11;
}

message EditResponse {
  // The edit text to be inserted
  string text = 1 [debug_redact = true];

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;
}

message InstructionRequest {
  // The name of the model to use.
  string model_name = 1;

  // the path to the file to be edited.
  string path = 2 [debug_redact = true];

  // the prefix prompt at which the edit should be inserted
  string prefix = 3 [debug_redact = true];

  // the text selected in the current buffer
  string selected_text = 4 [debug_redact = true];

  // the text after the selcted text
  string suffix = 5 [debug_redact = true];

  // the instruction to be used for the prompt.
  // If empty, smart paste will be applied
  string instruction = 6 [debug_redact = true];

  EditPosition position = 7;

  // Programming language of the current file.
  string lang = 8;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  uint32 sequence_id = 9;

  // Blobs to include in the retrieval corpus, specified in the compact
  // delta form. Replaces the blob_names field.
  base.blob_names.Blobs blobs = 10;

  // Instruction history
  repeated Exchange chat_history = 11;

  // (For smart paste) - Selected Code block to paste
  optional string code_block = 12 [debug_redact = true];

  // (For smart paste) - Target file path in which paste should happen
  optional string target_file_path = 13 [debug_redact = true];

  // (For smart paste) - Target file content to paste into
  optional string target_file_content = 14 [debug_redact = true];

  // (For smart paste) - Request ID of the exchange to which the context code should be added
  optional string context_code_exchange_request_id = 15;

  // User guidelines
  optional string user_guidelines = 16 [debug_redact = true];

  // Workspace guidelines
  optional string workspace_guidelines = 17 [debug_redact = true];
}

enum InstructionResponseStatusCode {
  OK = 0; // Default
  EXCEED_CONTEXT_LENGTH = 1;
}

message InstructionResponse {
  // Human readable explanation of the changes suggested
  string text = 1 [debug_redact = true];

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;

  // Should only exist when requested to use the replace_text tool
  optional ReplaceText replace_text = 4;

  // Status code of the response
  optional InstructionResponseStatusCode status_code = 5;
}

message ReplaceText {
  optional string text = 1 [debug_redact = true];

  optional uint32 start_line = 2;

  optional uint32 end_line = 3;

  optional string old_text = 4 [debug_redact = true];

  // Indicates which replace text block is currently streaming
  uint32 sequence_id = 5;
}

message InstructionAggregateResponse {
  // Human readable explanation of the changes suggested
  string text = 1 [debug_redact = true];

  // The subset of the requested blobs that are unknown to the server
  repeated string unknown_blob_names = 2;

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 3;

  // Multiple replace_text calls are possible
  repeated ReplaceText replace_text = 4;
}

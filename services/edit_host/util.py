"""A small tool to issue requests against the edit host API."""

import argparse
import logging
import sys
from pathlib import Path

import grpc
import pydantic

from base.blob_names import blob_names_pb2
from base.logging.console_logging import setup_console_logging
from services.lib.request_context.request_context import Request<PERSON>ontext
from services.edit_host import edit_pb2
from services.edit_host.client import EditClient


def _get_client(args) -> EditClient:
    if args.insecure:
        credentials = None
    else:
        if not args.ca_cert or not args.client_key or not args.client_cert:
            logging.error(
                "--ca-cert, --client-key, --client-cert need to be set "
                "unless --insecure is used"
            )
            sys.exit(1)
        credentials = grpc.ssl_channel_credentials(
            root_certificates=args.ca_cert.read_bytes(),
            private_key=args.client_key.read_bytes(),
            certificate_chain=args.client_cert.read_bytes(),
        )
    rpc_client = EditClient(args.endpoint, credentials=credentials)
    return rpc_client


def _edit(client: EditClient, args):
    if not args.instruction:
        logging.error("--instruction must be set.")
        return 1
    instruction: str = args.instruction
    assert args.max_instruction_len > 0
    if len(instruction) > args.max_instruction_len:
        logging.info("Truncating instruction")
        instruction = instruction[: args.max_instruction_len]
    # TODO: replace the fake blob names with real ones later.
    request_pb = edit_pb2.EditRequest(
        model_name=args.model or "",
        instruction=instruction,
        prefix=args.prefix,
        suffix=args.suffix,
        selected_text=args.selected_text,
        position=edit_pb2.EditPosition(
            prefix_begin=0,
            suffix_end=len(args.prefix + args.selected_text + args.suffix),
            blob_name="my-blob-name",
        ),
        blobs=blob_names_pb2.Blobs(
            baseline_checkpoint_id="fake-checkpoint-id",
            added=[
                "fake-blob-name-1".encode("utf-8"),
                "fake-blob-name-2".encode("utf-8"),
            ],
            deleted=[],
        ),
        lang="python",
    )

    auth_token = None
    if args.auth_token_file:
        auth_token = pydantic.SecretStr(
            args.auth_token_file.read_text(encoding="utf-8").strip()
        )
    request_context = RequestContext.create(auth_token=auth_token)
    resp = client.edit(request_pb, request_context=request_context)
    logging.info("\n%s", resp.text)

    return 0


def main():
    """Main entrypoint."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--endpoint", required=True, help="Endpoint to connect to, e.g. localhost:50051"
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="If true, the request will be in plaintext instead of a secure connection",
    )
    parser.add_argument("--ca-cert", type=Path, help="Path to the ca certificate")
    parser.add_argument("--client-key", type=Path, help="Path to the TLS client key")
    parser.add_argument(
        "--client-cert", type=Path, help="Path to the TLS client certificate"
    )

    subparsers = parser.add_subparsers(help="Commands", required=True)

    edit_parser = subparsers.add_parser("edit", help="Code Edit")
    edit_parser.set_defaults(action="edit")
    edit_parser.add_argument(
        "--model", help="The name of the model to use. Can be empty"
    )
    edit_parser.add_argument(
        "--instruction",
        help="The instruction prompt, needs to be set",
    )
    edit_parser.add_argument(
        "--max-instruction-len",
        type=int,
        default=500,
        help="The maximal length of the instruction in characters, default 500. "
        "If longer, it is truncated.",
    )
    edit_parser.add_argument(
        "--suffix",
        default="",
        help="A suffix that will be used for fill-in-the-middle.",
    )
    edit_parser.add_argument(
        "--prefix",
        default="",
        help="A prefix that will be used for fill-in-the-middle.",
    )
    edit_parser.add_argument(
        "--selected-text",
        default="",
        help="Selected text to invoke the instruction on",
    )
    edit_parser.add_argument(
        "--auth-token-file",
        type=Path,
        help="Path to file containing authentication token",
    )

    args = parser.parse_args()
    client = _get_client(args)
    if args.action == "edit":
        sys.exit(_edit(client, args))
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()

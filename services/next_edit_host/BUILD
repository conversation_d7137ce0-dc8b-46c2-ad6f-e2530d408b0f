load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "next_edit_proto",
    srcs = ["next_edit.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//base/diff_utils:edit_events_proto",
        "//services/completion_host:completion_proto",
        "//services/lib/grpc/stream_mux:stream_mux_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "next_edit_proto_py_proto",
    protos = [":next_edit_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/diff_utils:edit_events_py_proto",
        "//services/completion_host:completion_proto_py_proto",
    ],
)

go_grpc_library(
    name = "next_edit_proto_go_proto",
    importpath = "github.com/augmentcode/augment/services/next_edit_host/proto",
    proto = ":next_edit_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/diff_utils:edit_events_go_proto",
        "//services/completion_host:completion_host_go_proto",
        "//services/lib/grpc/stream_mux:stream_mux_go_proto",
    ],
)

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":next_edit_proto_py_proto",
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
    ],
)

# a small CLI utility to issue requests against the next_edit host API
py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        ":client",
        "//base/logging:console_logging",
        "//services/lib/request_context:request_context_py",
    ],
)

ts_proto_library(
    name = "next_edit_host_ts_proto",
    data = [
        "//base/blob_names:blob_names_ts_proto",
        "//base/diff_utils:edit_events_ts_proto",
        "//services/completion_host:completion_host_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":next_edit_proto",
    visibility = ["//services:__subpackages__"],
)

py_library(
    name = "lru_dict",
    srcs = ["lru_dict.py"],
    visibility = ["//services:__subpackages__"],
)

pytest_test(
    name = "lru_dict_test",
    srcs = ["lru_dict_test.py"],
    deps = [":lru_dict"],
)

from pathlib import Path

import pytest

from services.next_edit_host.server.next_edit_handler import (
    NextEditHandlerConfig,
)


def test_decode_default_config():
    # Get the default config json from file
    config_path = Path(__file__).parent / "handler_config_default.json"
    with open(config_path, "r") as f:
        default_config_json = f.read()

    # Decode the config
    config = NextEditHandlerConfig.from_json(default_config_json)

    # Check that the config is not None
    assert config is not None

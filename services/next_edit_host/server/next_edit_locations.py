"""A retriever that returns ranked locations for WORKSPACE scope."""

import concurrent.futures
import dataclasses
from contextlib import contextmanager
from itertools import islice, chain
import math
from collections.abc import Iterable, Generator
from dataclasses import dataclass
from threading import Semaphore
from typing import Sequence

from base.caching.lru_cache import lru_cache
from base.datasets.itertools import groupby
from base.languages.languages import LanguageId
from base.logging.secret_logging import get_safe_logger
from base.ranges.line_map import LineMap
import opentelemetry.trace
import structlog

from base.blob_names.python.blob_names import BlobName, Blobs

from base.prompt_format.common import TokenList
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
)
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenPromptFormatter,
    EditGenPromptInput,
    EditGenPromptOutput,
)

from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>, LineRange
from base.retrieval.chunking.line_based_chunking import LineChunkContents
from base.retrieval.chunking.smart_chunking import (
    SmartChunker,
)
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    Retriever,
)
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.lib.file_retriever.file_retriever import FileRetriever
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.next_edit_metrics import (
    record_reranker_inference_duration_context,
    _next_edit_diagnostics_count,
    record_lock_timeout,
)
from services.next_edit_host.server.next_edit_request_insight_builder import (
    NextEditResponseBuilder,
)
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.next_edit_host.server.next_edit_utils import (
    RecentChangeTracker,
    SamplingParams,
    extend_region_to_line_boundaries,
    is_ignored_file_path,
    yield_and_execute,
)

logger = structlog.get_logger(__name__)
tracer = opentelemetry.trace.get_tracer(__name__)

RESULT_TIMEOUT_SECONDS = 5  # Maximum time to wait for results per request


@dataclass(frozen=True)
class LocationsServiceConfig:
    """Config for next edit location retriever."""

    max_prompt_tokens: int
    """The maximum number of tokens used by the input prompt."""

    edit_end_token_ids: set[int]
    """The set of token ids that mark the end of a location."""

    sampling_params: SamplingParams
    """The sampling parameters for the edit generation model."""

    max_smart_chunk_size: int
    """The maximum size of a smart chunk for selected range expansion."""

    has_change_token_id: int
    """The token id that marks the score of a location."""

    max_locations_per_file_request: int
    """The maximum number of locations to _consider_ per file scoped request."""

    max_locations_per_workspace_request: int
    """The maximum number of locations to rerank in workspace mode."""

    reranker_filter_threshold: float | None
    """Locations with a reranker score below this threshold will be filtered out."""

    forced_mode_expand_lines: int = 10
    """The number of lines to expand around the cursor in forced mode requests with
    an empty selection."""

    secret_logs_enabled: bool = False
    """This should only be enabled in staging or dev."""


class NextEditLocationsService:
    """A service that returns ranked locations for a request."""

    # Reference to the global semaphore from NextEditHandler to limit concurrent calls to format_input_prompt
    # This will be set during initialization

    def __init__(
        self,
        namespace: str,
        location_retriever: Retriever[LocalizationNextEditPromptInput],
        edit_prompt_formatter: EditGenPromptFormatter,
        file_retriever: FileRetriever,
        config: LocationsServiceConfig,
        edit_gen_ranker_client: InfererClient | None,
        format_prompt_semaphore: Semaphore,
    ):
        self.namespace = namespace
        self.location_retriever = location_retriever
        self.edit_prompt_formatter = edit_prompt_formatter
        self.file_retriever = file_retriever
        self.config = config
        self.edit_gen_ranker_client = edit_gen_ranker_client
        self._format_prompt_semaphore = format_prompt_semaphore
        chunker = SmartChunker(
            max_chunk_chars=self.config.max_smart_chunk_size, max_headers=0
        )
        self.chunker = chunker
        self.safe_logger = get_safe_logger(logger, config.secret_logs_enabled)

        @lru_cache(maxsize=128)
        def cached_split_chunks(
            text: str, lang: LanguageId | None
        ) -> list[LineChunkContents]:
            return chunker.split_chunks(text, lang)

        self._cached_split_chunks = cached_split_chunks

    def _infer_edit_gen_ranker(
        self,
        input_tokens: TokenList,
        request: NextEditRequest,
    ) -> InferenceClientProtocol.Reply:
        max_context_length = self.config.max_prompt_tokens
        assert (
            len(input_tokens) < max_context_length
        ), f"{len(input_tokens)=} must be less than {max_context_length=}"

        cur_sampling_params = self.config.sampling_params
        cur_seed = cur_sampling_params.seed
        match request.proto.mode:
            case next_edit_pb2.NextEditMode.BACKGROUND:
                priority = 0
            case next_edit_pb2.NextEditMode.FOREGROUND:
                priority = 1
            case next_edit_pb2.NextEditMode.FORCED:
                # same as foreground since both trump background but not each other
                priority = 1
            case _:
                raise ValueError(f"Unknown mode: {request.proto.mode}")

        assert self.edit_gen_ranker_client is not None
        with tracer.start_as_current_span(
            "edit_gen_ranker_inference"
        ), record_reranker_inference_duration_context(request):
            reply = self.edit_gen_ranker_client.infer(
                input_tokens=input_tokens,
                max_output_length=1,
                end_token_ids=self.config.edit_end_token_ids,
                top_k=cur_sampling_params.top_k,
                top_p=cur_sampling_params.top_p,
                temperature=cur_sampling_params.temperature,
                random_seed=cur_seed,
                request_context=request.context,
                timeout_s=cur_sampling_params.inference_timeout_s,
                sequence_id=request.proto.sequence_id,
                priority=priority,
            )
        return reply

    def _score_location(
        self,
        prompt_output: EditGenPromptOutput,
        request: NextEditRequest,
        semaphore: Semaphore,  # Mutual exclusion on the inference
    ) -> float | None:
        with semaphore:
            generation_output = self._infer_edit_gen_ranker(
                prompt_output.tokens, request
            )

        if generation_output.output_tokens and generation_output.log_probs:
            prob = math.exp(generation_output.log_probs[0])
            if generation_output.output_tokens[0] == self.config.has_change_token_id:
                return prob
            else:
                # the model is trained to output either has_change or no_change as the
                # first token, so P(has_change_token) = 1.0 - P(no_change_token)
                return 1 - prob
        else:
            return None

    @contextmanager
    def _acquire_format_prompt_semaphore(self):
        """Acquire the format prompt semaphore with timeout and proper logging.

        Yields:
            bool: True if the semaphore was acquired, False otherwise.
        """
        acquired = self._format_prompt_semaphore.acquire(timeout=RESULT_TIMEOUT_SECONDS)
        if not acquired:
            self.safe_logger.warn("Timeout acquiring format_prompt_semaphore")
            record_lock_timeout("locations_format_prompt_semaphore")
            yield False
            return

        try:
            yield True
        finally:
            self._format_prompt_semaphore.release()

    def _rerank_current_locations(
        self,
        current_locations: Iterable[RetrievalChunk],
        prompt_input: LocalizationNextEditPromptInput,
        request: NextEditRequest,
        change_tracker: RecentChangeTracker,
        executor: concurrent.futures.Executor,
    ) -> Iterable[RetrievalChunk]:
        """Rerank locations using the edit generation reranker model."""
        logger = self.safe_logger
        if self.edit_gen_ranker_client is None:
            # No reranker configured, return original locations without reranking
            logger.info("No reranker configured, skip reranking.")
            yield from current_locations
            return

        current_locations = list(current_locations)
        logger.info(f"Reranking {len(current_locations)} locations.")

        # Pipelining mechanism to avoid inference GPU work waiting on prompt formatting CPU work
        # or vice versa. We just spawn off a series of futures to wrap the GPU work and run that
        # work in a semaphore to ensure it doesn't cancel itself and give it pipeline-like
        # execution properties. Since we wait for all the work to be done before yielding any of
        # it, there's no need for more sophisticated behavior here yet.
        reranker_semaphore = Semaphore(1)
        score_futures: list[concurrent.futures.Future[float | None]] = []

        with tracer.start_as_current_span("rerank_locations"):
            # First loop: format prompt and enqueue inference work
            for index, location in enumerate(current_locations):
                logger.info(f"Reranking location {index}.")
                logger.secret_info(f"{location.path=}, {location.origin=}")
                if location.blob_name is None:
                    logger.warn(f"Skipping location {index} with no blob name.")
                    continue
                location_file = change_tracker.get_current_file(
                    location.blob_name, path=location.path
                )
                if location_file is None:
                    logger.error(
                        f"Can't find full file for location: {location.blob_name}."
                    )
                    continue
                location_prompt_input = EditGenPromptInput(
                    current_file=location_file,
                    edit_region=location.crange,
                    instruction=request.proto.instruction,
                    recent_changes=prompt_input.recent_changes,
                    retrieval_chunks=[],
                )

                with tracer.start_as_current_span(
                    "score_location_prompt_formatting"
                ), self._acquire_format_prompt_semaphore() as acquired:
                    # Acquire the semaphore to limit concurrent calls to format_input_prompt

                    if not acquired:
                        continue

                    location_prompt_output = (
                        self.edit_prompt_formatter.format_input_prompt(
                            location_prompt_input
                        )
                    )

                # keep io operations out of the lock
                logger.info(
                    f"Formatted location {index} reranker prompt with {len(location_prompt_output.tokens)} tokens. Timing: {location_prompt_output.timing}"
                )

                score_futures.append(
                    executor.submit(
                        self._score_location,
                        prompt_output=location_prompt_output,
                        request=request,
                        semaphore=reranker_semaphore,
                    )
                )

            scored_locations: list[RetrievalChunk] = []
            # Second loop: wait on inference work and produce results
            for index, (location, score_future) in enumerate(
                zip(current_locations, score_futures)
            ):
                score = score_future.result(timeout=RESULT_TIMEOUT_SECONDS)
                if score is None:
                    self.safe_logger.secret_error(
                        f"Failed to score location: {location.path}:{location.crange}"
                    )
                    score = 0.0
                logger.info(
                    f"Location {index} has a reranker score of {score:.4g}.",
                )
                # Putting reranker score in the score field.
                # TODO: Have both retrieval score and reranker score for the RetrievalChunk
                location = dataclasses.replace(location, score=score)
                scored_locations.append(location)

            scored_locations.sort(key=lambda loc: loc.score or 0, reverse=True)
            logger.info(f"Reranked {len(scored_locations)} locations.")

        yield from scored_locations

    def _ignored_file_filter(
        self, locations: Iterable[RetrievalChunk]
    ) -> Iterable[RetrievalChunk]:
        for location in locations:
            if is_ignored_file_path(location.path):
                continue
            yield location

    def _retrieve_indexed_locations(
        self,
        prompt_input: LocalizationNextEditPromptInput,
        request: NextEditRequest,
        missing_blobs: set[BlobName],
        change_tracker: RecentChangeTracker,
    ) -> Iterable[RetrievalChunk]:
        """Call the location model to get indexed locations."""

        # Perform retrieval with appropriate scope
        match request.proto.scope:
            case next_edit_pb2.NextEditScope.CURSOR:
                # no need to do any retrieval
                return
            case next_edit_pb2.NextEditScope.FILE:
                # Guard against clearly invalid blob names
                if request.proto.blob_name:
                    location_blobs = Blobs.from_blob_names([request.proto.blob_name])
                else:
                    self.safe_logger.info(
                        "Request has no blob name. Locations limited to current file."
                    )
                    location_blobs = Blobs.from_blob_names([])
            case next_edit_pb2.NextEditScope.WORKSPACE:
                # Trust these blob names were formatted correctly
                location_blobs = Blobs.from_proto(request.proto.blobs)
            case _:
                raise ValueError(f"Unknown scope: {request.proto.scope}")

        with tracer.start_as_current_span("retrieve_locations"):
            location_result = self.location_retriever.retrieve(
                RetrievalInput(
                    prompt_input=prompt_input,
                    blobs=[location_blobs],
                    namespace=self.namespace,
                ),
                request_context=request.context,
                auth_info=request.auth_info,
            )
        # The checkpoint has already been resolved in this handler and should
        # never be missing in the location retriever.
        assert (
            location_result.get_checkpoint_not_found() is False
        ), f"Checkpoint not found: {location_blobs.baseline_checkpoint_id=}"
        # Report missing blobs in the location retriever.
        location_missing_blobs = location_result.get_missing_blob_names()
        missing_blobs.update(location_missing_blobs)
        if request.proto.blob_name in location_missing_blobs:
            self.safe_logger.error(
                "The current file's blob is missing during location retrieval."
            )

        # Retrieve all files we will need to update the chunks.
        with tracer.start_as_current_span("location_blob_fetch"):
            chunks = list(location_result.get_retrieved_chunks())
            logger.info(f"Retrieved {len(chunks)} locations.")
            indexed_files = self.file_retriever.retrieve_indexed_files(
                blob_name_to_file_path={
                    chunk.blob_name: chunk.path for chunk in chunks if chunk.blob_name
                },
                auth_info=request.auth_info,
                missing_blobs=missing_blobs,
                request_context=request.context,
                expected=True,  # since the chunk was just retrieved, we expect it to be in CM
            )
            change_tracker.add_indexed_files(indexed_files.values())
        # Retrieved chunks are already sorted from highest to lowest score.
        yield from chunks

    def _get_chunks_for_file_sorted(
        self,
        prompt_input: LocalizationNextEditPromptInput,
        scope: next_edit_pb2.NextEditScope.ValueType,
    ) -> Iterable[RetrievalChunk]:
        """Get chunks for the current file, sorted by cursor distance."""
        if scope == next_edit_pb2.NextEditScope.WORKSPACE:
            return

        current_file = prompt_input.current_file
        chunks = self.chunker.split_chunks(current_file.contents, None)
        max_chunk_chars = self.chunker.max_chunk_chars
        self.safe_logger.info(
            f"Current file has {len(chunks)} smart chunks ({max_chunk_chars=})."
        )
        # note that Python's sort is guaranteed to be stable
        mid_point = CharRange.point(
            (prompt_input.edit_region.start + prompt_input.edit_region.stop) // 2
        )
        chunks = sorted(
            chunks,
            key=lambda chunk: chunk.crange().distance(mid_point),
        )
        if scope == next_edit_pb2.NextEditScope.CURSOR:
            # cursor mode only returns the closest chunk
            chunks = chunks[:1]
        for chunk in chunks:
            crange = chunk.crange()
            file_chunk = RetrievalChunk(
                text=prompt_input.current_file.contents[crange.to_slice()],
                path=prompt_input.current_file.path,
                char_start=crange.start,
                char_end=crange.stop,
                blob_name=prompt_input.current_file.blob_name,
                chunk_index=None,
                origin="file",
            )
            yield file_chunk

    def _filter_blocked_locations(
        self,
        current_chunks: Iterable[RetrievalChunk],
        path_to_blocked_ranges: dict[str, set[CharRange]],
        response_ri: NextEditResponseBuilder,
        executor: concurrent.futures.Executor,
    ) -> Iterable[RetrievalChunk]:
        """Filter out locations contained in blocked ranges.

        Args:
            current_chunks: The current chunks to be filtered.
            path_to_blocked_ranges: The blocked ranges to be used for filtering.
            response_ri: The NextEditResponseBuilder to be used for recording blocked locations.
            executor: The executor to be used for publishing blocked locations.

        Returns:
            An iterable over non-blocked locations.

        """

        for chunk in current_chunks:
            blocked_ranges = path_to_blocked_ranges.get(chunk.path, set())
            if any(
                blocked_range.contains(chunk.crange) for blocked_range in blocked_ranges
            ):
                # log information about the blocked location
                logger.info(f"Blocking location: {chunk.blob_name=} {chunk.crange=}")
                self.safe_logger.secret_info(f"{chunk.path=}")
                # publish the blocked location to request insight
                ri_builder = response_ri.record_location_retrieval()
                ri_builder.add_blocked_location(chunk)
                executor.submit(
                    yield_and_execute,
                    ri_builder.publish_locations,
                )
                continue

            yield chunk

    def _rechunk_current_locations(
        self,
        chunks: Iterable[RetrievalChunk],
        change_tracker: RecentChangeTracker,
    ) -> Iterable[RetrievalChunk]:
        """Expand chunks to smart chunks."""
        chunks_for_file = dict[BlobName, list[LineChunkContents]]()
        for chunk in chunks:
            if not chunk.blob_name:
                self.safe_logger.error("Chunk with no blob name in _rechunk_locations.")
                self.safe_logger.secret_error(f"{chunk.path=}, {chunk.origin=}")
                continue

            source_file = change_tracker.get_current_file(
                chunk.blob_name, path=chunk.path
            )
            if source_file is None:
                self.safe_logger.error(
                    f"File not found during rechunking: {chunk.blob_name=}"
                )
                self.safe_logger.secret_error(f"{chunk.path=}, {chunk.origin=}")
                continue
            if chunk.blob_name not in chunks_for_file:
                chunks_for_file[chunk.blob_name] = self._cached_split_chunks(
                    source_file.contents, None
                )
            chunks_for_current_file = chunks_for_file[chunk.blob_name]
            for chunk_for_file in chunks_for_current_file:
                if chunk_for_file.crange().overlaps(chunk.crange):
                    # Use smart chunk range and contents
                    yield dataclasses.replace(
                        chunk,
                        text=chunk_for_file.text,
                        char_start=chunk_for_file.crange().start,
                        char_end=chunk_for_file.crange().stop,
                    )

    def _dedup_disjoint_locations(
        self,
        locations: Iterable[RetrievalChunk],
    ) -> Iterable[RetrievalChunk]:
        """Deduplicate locations by path and character range.

        These locations can be either duplicated or non-overlapping. Partially
        overlapping locations will be dropped and cause an error to be logged.
        """
        # Add warning on partial overlap
        path_to_ranges = dict[str, set[CharRange]]()
        for location in locations:
            location_crange = location.crange
            prior_ranges = path_to_ranges.setdefault(location.path, set())
            if location_crange in prior_ranges:
                continue
            bad_overlap = False
            for prior_range in prior_ranges:
                if prior_range.overlaps(location_crange):
                    self.safe_logger.error(
                        f"Partial overlap between location chunks: {location_crange=}, {prior_range=}"
                    )
                    self.safe_logger.secret_error(
                        f"{location.path=}, {location.origin=}"
                    )
                    bad_overlap = True
                    break
            if bad_overlap:
                # drop this location entirely
                continue

            prior_ranges.add(location_crange)
            yield location

    def _get_current_locations_cursor(
        self,
        prompt_input: LocalizationNextEditPromptInput,
        request: NextEditRequest,
        path_to_blocked_ranges: dict[str, set[CharRange]],
        response_ri: NextEditResponseBuilder,
        executor: concurrent.futures.Executor,
    ) -> Iterable[RetrievalChunk]:
        """Get update-to-date candidate chunks for a cursor scope request."""
        active_file = prompt_input.current_file

        if (
            request.proto.mode != next_edit_pb2.NextEditMode.BACKGROUND
            and prompt_input.edit_region
        ):
            # As a special case, for non-background requests with a nonempty selection,
            # we only expand the selection to line boundaries.
            new_range = extend_region_to_line_boundaries(
                active_file.contents,
                prompt_input.edit_region,
            )
            locations = [
                RetrievalChunk(
                    text=active_file.contents[new_range.to_slice()],
                    path=active_file.path,
                    char_start=new_range.start,
                    char_end=new_range.stop,
                    blob_name=active_file.blob_name,
                    chunk_index=None,
                    origin="cursor",
                )
            ]
        elif (
            request.proto.mode == next_edit_pb2.NextEditMode.FORCED
            and prompt_input.edit_region.is_point()
        ):
            # For forced mode with no selection, we expand to N lines above and below
            # the cursor.
            lmap = LineMap(active_file.contents)
            cursor_line = lmap.get_line_number(prompt_input.edit_region.start)
            new_lrange = LineRange(
                max(0, cursor_line - self.config.forced_mode_expand_lines),
                min(
                    lmap.size_lines(),
                    cursor_line + self.config.forced_mode_expand_lines + 1,
                ),
            )
            new_range = lmap.lrange_to_crange(new_lrange)
            locations = [
                RetrievalChunk(
                    text=active_file.contents[new_range.to_slice()],
                    path=active_file.path,
                    char_start=new_range.start,
                    char_end=new_range.stop,
                    blob_name=active_file.blob_name,
                    chunk_index=None,
                    origin="cursor",
                )
            ]
        else:
            # Otherwise, expand the cursor to smart chunks.
            locations = list(
                self._get_chunks_for_file_sorted(prompt_input, request.proto.scope)
            )
            # Keep only the one closest to the cursor
            locations = locations[:1]

        if request.proto.mode != next_edit_pb2.NextEditMode.FORCED:
            # in forced mode nothing is filtered
            locations = list(
                self._filter_blocked_locations(
                    locations,
                    path_to_blocked_ranges,
                    response_ri,
                    executor,
                )
            )

        logger.info(f"Got {len(locations)} cursor locations after filtering.")
        yield from locations

    def _get_current_locations_file_or_workspace(
        self,
        prompt_input: LocalizationNextEditPromptInput,
        request: NextEditRequest,
        missing_blobs: set[BlobName],
        change_tracker: RecentChangeTracker,
        executor: concurrent.futures.Executor,
        path_to_blocked_ranges: dict[str, set[CharRange]],
        response_ri: NextEditResponseBuilder,
    ) -> Generator[RetrievalChunk, None, None]:
        """Get update-to-date candidate chunks for a file or workspace scope request."""
        scope = request.proto.scope
        if scope not in (
            next_edit_pb2.NextEditScope.FILE,
            next_edit_pb2.NextEditScope.WORKSPACE,
        ):
            scope_name = next_edit_pb2.NextEditScope.Name(scope)
            logger.critical(f"Unexpected scope: {scope_name}")
            return

        # In file scope, get all smart chunks from the current file, sorted by distance
        file_locations = (
            list(self._get_chunks_for_file_sorted(prompt_input, request.proto.scope))
            if scope == next_edit_pb2.NextEditScope.FILE
            else []
        )

        if (
            scope == next_edit_pb2.NextEditScope.FILE
            and self.edit_gen_ranker_client is not None
            and len(file_locations) <= self.config.max_locations_per_file_request
        ):
            # Skip the location retrieval work if there are only a few chunks in the
            # current file if we will rerank them anyway.
            logger.info(
                f"Skipping location retrieval since the file only has {len(file_locations)} locations."
            )
            locations = file_locations
        else:
            # Otherwise, use the location retriever to sort and pre-filter locations.
            ret_locations = self._retrieve_indexed_locations(
                prompt_input=prompt_input,
                request=request,
                missing_blobs=missing_blobs,
                change_tracker=change_tracker,
            )

            # Ignore locations from ignored files
            ret_locations = self._ignored_file_filter(ret_locations)

            # Update indexed chunks to be the current version
            ret_locations = (
                change_tracker.update_indexed_chunk_to_current(chunk, self.safe_logger)
                for chunk in ret_locations
            )

            # Rechunk all retrieval locations to be smart chunks.
            locations = self._rechunk_current_locations(ret_locations, change_tracker)

        # Filter out any blocked locations
        if request.proto.mode != next_edit_pb2.NextEditMode.FORCED:
            locations = self._filter_blocked_locations(
                locations,
                path_to_blocked_ranges,
                response_ri,
                executor,
            )

        # Limit the number of locations to be reranked
        if scope == next_edit_pb2.NextEditScope.FILE:
            max_locations = self.config.max_locations_per_file_request
        elif request.proto.mode == next_edit_pb2.NextEditMode.BACKGROUND:
            max_locations = self.config.max_locations_per_workspace_request
        else:
            # in foreground workspace mode, we don't limit the number of locations
            max_locations = None

        self.safe_logger.info(f"Limiting to {max_locations=}")
        locations = islice(locations, max_locations)

        # Retrieve all diagnostic locations from the request
        diagnostic_locations = self._fetch_diagnostic_locations(
            request.proto, change_tracker
        )

        # Filter out any diagnostic locations from ignored files
        diagnostic_locations = self._ignored_file_filter(diagnostic_locations)

        # Convert to lists
        diagnostic_locations = list(diagnostic_locations)
        locations = list(locations)

        # Make the swaps (if any)
        locations = self._swap_with_diagnostic_locations(
            locations, diagnostic_locations, change_tracker
        )

        # Rerank locations if the reranker is set
        locations = self._rerank_current_locations(
            current_locations=locations,
            prompt_input=prompt_input,
            request=request,
            change_tracker=change_tracker,
            executor=executor,
        )

        yield from locations

    def get_current_locations(
        self,
        prompt_input: LocalizationNextEditPromptInput,
        request: NextEditRequest,
        response_ri: NextEditResponseBuilder,
        executor: concurrent.futures.Executor,
        missing_blobs: set[BlobName],
        change_tracker: RecentChangeTracker,
        path_to_blocked_ranges: dict[str, set[CharRange]],
    ) -> Generator[RetrievalChunk, None, None]:
        """Get update-to-date location chunks to edit."""
        if request.proto.scope == next_edit_pb2.NextEditScope.CURSOR:
            locations = self._get_current_locations_cursor(
                prompt_input,
                request,
                path_to_blocked_ranges,
                response_ri,
                executor,
            )
        elif (
            request.proto.scope == next_edit_pb2.NextEditScope.FILE
            and request.proto.api_version >= 3
        ):
            # In api version 3 and above, we don't fuse the cursor-scope results
            # with the file-scope results.
            locations = self._get_current_locations_file_or_workspace(
                prompt_input,
                request,
                missing_blobs,
                change_tracker,
                executor,
                path_to_blocked_ranges,
                response_ri,
            )
        elif request.proto.scope == next_edit_pb2.NextEditScope.FILE:
            # Since file scope requests will always be fired by the front end before
            # a cursor scope request, we stream back both the cursor result and file
            # results as an optimization.
            cursor_locations = self._get_current_locations_cursor(
                prompt_input,
                request,
                path_to_blocked_ranges,
                response_ri,
                executor,
            )
            # file locations is a lazy generator
            file_locations = self._get_current_locations_file_or_workspace(
                prompt_input,
                request,
                missing_blobs,
                change_tracker,
                executor,
                path_to_blocked_ranges,
                response_ri,
            )
            locations = chain(cursor_locations, file_locations)
            # Deduplicate locations while keeping the order
            locations = self._dedup_disjoint_locations(locations)
        elif request.proto.scope == next_edit_pb2.NextEditScope.WORKSPACE:
            locations = self._get_current_locations_file_or_workspace(
                prompt_input,
                request,
                missing_blobs,
                change_tracker,
                executor,
                path_to_blocked_ranges,
                response_ri,
            )
        else:
            raise ValueError(f"Unknown scope: {request.proto.scope}")

        for index, loc in enumerate(locations):
            location = dataclasses.replace(loc, chunk_index=index)
            # We need to publish to RI as we go because the stream
            # could be interrupted between any two locations
            ri_builder = response_ri.record_location_retrieval()
            ri_builder.add_returned_location(location)
            executor.submit(
                yield_and_execute,
                ri_builder.publish_locations,
            )

            # only yield this to be processed later if it's above the threshold
            # we filter here because we want to publish the location to RI regardless
            # of whether it's filtered out or not
            # Both FOREGROUND and BACKGROUND modes use the same threshold
            should_filter = (
                request.proto.mode != next_edit_pb2.NextEditMode.FORCED
                and self.config.reranker_filter_threshold is not None
                and location.score is not None
                and location.score < self.config.reranker_filter_threshold
            )
            if should_filter:
                self.safe_logger.warn(
                    f"Filtering out location {index} with reranker score {location.score:.4g}."
                )
                self.safe_logger.secret_warn(
                    f"location={location.path}:{location.char_start}-{location.char_end}"
                )
            else:
                yield location

    def _fetch_diagnostic_locations(
        self,
        req: next_edit_pb2.NextEditRequest,
        change_tracker: RecentChangeTracker,
    ) -> Iterable[RetrievalChunk]:
        """Fetch diagnostic locations from the diagnostics in the request, and convert to RetrievalChunks."""
        # Backwards compatibility: don't use diagnostic locations for old clients (this should be removed after a while)

        if req.api_version < 2:
            self.safe_logger.info(
                f"Skipping diagnostic locations for request with API version {req.api_version}."
            )
            return []

        for diagnostic in req.diagnostics:
            file = change_tracker.get_current_file(
                BlobName(diagnostic.blob_name), path=diagnostic.location.path
            )
            if file is None:
                continue

            yield RetrievalChunk(
                text=file.contents[diagnostic.char_start : diagnostic.char_end],
                path=file.path,
                char_start=diagnostic.char_start,
                char_end=diagnostic.char_end,
                blob_name=file.blob_name,
                chunk_index=None,
                origin="diagnostic",
            )

    def _swap_with_diagnostic_locations(
        self,
        locations: Sequence[RetrievalChunk],
        diagnostic_locations: Sequence[RetrievalChunk],
        change_tracker: RecentChangeTracker,
    ) -> Iterable[RetrievalChunk]:
        """Swap locations with diagnostic locations if they are closer to the cursor."""
        # group the locations by file
        # If we have no locations or no diagnostics, just return the original locations
        if not locations or not diagnostic_locations:
            yield from locations
            return

        # Yield all but the last location, since we might swap it out
        yield from locations[:-1]

        locations_by_file: dict[str, list[RetrievalChunk]] = groupby(
            locations, lambda loc: loc.path
        )

        for diagnostic_location in diagnostic_locations:
            # do not use if diagnostic location touches any of the retrieved locations
            if diagnostic_location.path not in locations_by_file or not any(
                CharRange(location.char_start, location.char_end).touches(
                    CharRange(
                        diagnostic_location.char_start, diagnostic_location.char_end
                    )
                )
                for location in locations_by_file[diagnostic_location.path]
            ):
                # Add a log and update the metric
                self.safe_logger.secret_info(
                    f"Swapping in diagnostic at {diagnostic_location.path}:{diagnostic_location.char_start}-{diagnostic_location.char_end} for {locations[-1].path}:{locations[-1].char_start}-{locations[-1].char_end}"
                )
                _next_edit_diagnostics_count.inc()

                yield from islice(
                    self._rechunk_current_locations(
                        [diagnostic_location], change_tracker
                    ),
                    1,
                )
                return
        self.safe_logger.secret_info(
            f"No diagnostic location to swap in for {locations[-1].path}"
        )
        yield from locations[-1:]

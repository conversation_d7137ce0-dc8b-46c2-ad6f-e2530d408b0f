{
  edit_generation_config: {
    tokenizer_name: 'starcoder2',
    prompt_formatter_name: 'raven_edit',
    prompt_formatter_config: {
      diff_context_lines: 9,
      max_prompt_tokens: 10200,
      section_budgets: {
        suffix_tks: 1200,
        prefix_tks: 2800,
        diff_tks: 3700,
        filename_tks: 100,
        instruction_tks: 200,
        retrieval_tks: 2000,
      },
    },
    ranker_gen_prompt_formatter_config: {
      diff_context_lines: 5,
      max_prompt_tokens: 6800,
      section_budgets: {
        suffix_tks: 800,
        prefix_tks: 1800,
        diff_tks: 2400,
        filename_tks: 50,
        instruction_tks: 200,
        retrieval_tks: 1200,
      },
    },
    max_output_length: 320,
    sampling_params: {
      top_k: 0,
      top_p: 0.0,
      temperature: 0.0,
      seed: 0,
      inference_timeout_s: 5.0,
    },
  },
  content_cache_size_mb: 1024,
  description_generation_config: {
    tokenizer_name: 'llama3_instruct',
    chat_prompt_formatter_name: 'llama3',
    prompt_formatter_name: 'raven_describe',
    prompt_formatter_config: {
      max_prompt_tokens: 2048,
      diff_context_lines: 5,
    },
    sampling_params: {
      top_k: 0,
      top_p: 0.0,
      temperature: 0.0,
      seed: 0,
      inference_timeout_s: 5.0,
    },
    max_output_length: 128,
  },
  description_heuristic_config: {},
  reranker_filter_threshold: null,
  max_smart_chunk_size: 3000,
  change_probability_min: {
    FOREGROUND: 0.65,  // Using the same threshold as BACKGROUND mode
    BACKGROUND: 0.65,
    FORCED: 0.0,
  },
  max_filter_score: null,
  validate_blob_state: true,
}

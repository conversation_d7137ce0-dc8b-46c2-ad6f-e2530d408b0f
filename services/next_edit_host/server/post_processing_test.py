"""Tests for the post_processing module."""

import pytest

from base.diff_utils import changes
from base.diff_utils.diff_utils import File
from base.diff_utils.str_diff import precise_line_diff
from base.ranges.range_types import <PERSON>r<PERSON><PERSON>e
from services.next_edit_host.server.post_processing import (
    contains_inserted_todo,
    is_exact_undo_prev_spans,
    is_exact_undo_recent_changes,
)


@pytest.mark.parametrize(
    "previous_before,previous_after,existing_code,suggested_code,expected",
    [
        # No change
        ("abc", "xyz", "no change", "no change", False),
        # Undos a deletion.
        ("a", "", "", "a", True),
        # Undos a insertion.
        ("", "a", "a", "", True),
        ("a", "", "a", "", False),
        ("", "a", "", "a", False),
        # Undos a modification.
        ("a", "b", "b", "a", True),
        (
            # Modify line 1, 2, 3.
            """
        line 1
        line 2
        line 3
        """,
            """
        modified line 1
        modified line 2
        modified line 3
        """,
            # Undoing line 1 and line 3.
            """
        modified line 1
        modified line 2
        modified line 3
        """,
            """
        line 1
        modified line 2
        line 3
        """,
            True,
        ),
        # not an undo because ranges don't match
        (
            "line1\nline2\n",
            "line1\nline2\nline3\n",
            "line2\nline3\n",
            "line2\n",
            False,
        ),
        # but is an undo if ranges match
        (
            "line1\nline2\n",
            "line1\nline2\nline3\n",
            "line1\nline2\nline3\n",
            "line1\nline2\n",
            True,
        ),
        # not an undo as the code is being moved
        (
            "line1\nline2\nline3\n",
            "line2\nline3\n",
            "line2\nline3\n",
            "line2\nline3\nline1\n",
            False,
        ),
    ],
)
def test_undo_previous(
    previous_before: str,
    previous_after: str,
    existing_code: str,
    suggested_code: str,
    expected: bool,
):
    prev_diff = precise_line_diff(previous_before, previous_after)
    suggestion_diff = precise_line_diff(existing_code, suggested_code)
    assert (
        is_exact_undo_prev_spans(
            list(zip(prev_diff.spans, prev_diff.span_ranges_in_after)),
            list(zip(suggestion_diff.spans, suggestion_diff.span_ranges_in_before)),
        )
        == expected
    )


def test_is_exact_undo_recent_changes():
    previous = "".join([f"line {i}\n" for i in range(100)])
    current = previous.replace("line 99", "modified line 99")
    selection_range = CharRange(782, 799)
    assert current[selection_range.to_slice()] == "modified line 99\n"
    # Recent change: line 99 -> modified line 99
    # Model suggests: modified line 99 -> line 99
    # Therefore, the model suggestion undoes the recent change.
    assert is_exact_undo_recent_changes(
        recent_changes=[
            changes.Modified(File("file.py", previous), File("file.py", current))
        ],
        selection_range=selection_range,
        existing_code="modified line 99\n",
        suggested_code="line 99\n",
        current_path="file.py",
    )


@pytest.mark.parametrize(
    "existing_code,suggested_code,expected",
    [
        ("", "a", False),
        ("", "TODO(): do something", True),
        ("", "todo: do something", True),
        ("def foo():", "def fooTODO():", False),
        ("TODO: do something", "TODO: do another", False),
        ("TODO(): do something", "", False),
        (
            """
            # TODO(): do something
            hello world
            """,
            """
            # TODO(): do something
            hello world
            # TODO(): do another
            """,
            True,
        ),
        (
            """
            # TODO(): do something
            hello world
            # TODO(): do another
            """,
            """
            # TODO(): do something
            hello world
            """,
            False,
        ),
    ],
)
def test_contains_inserted_todo(
    existing_code: str, suggested_code: str, expected: bool
):
    assert contains_inserted_todo(existing_code, suggested_code) == expected

"""Unit tests for the GRPC next edit server."""

from unittest.mock import MagicMock, create_autospec

import concurrent.futures
import grpc
import pytest
from typing import Iterator, List

from services.lib.grpc.auth.service_token_auth import ServiceTokenAuth
from services.lib.grpc.stream_mux import stream_mux_pb2
from services.lib.retrieval import retriever_factory
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.handler import (
    NextEditHandlerProtocol,
    NextEditResult,
    ScoredFileHunk,
)
from services.next_edit_host.server.next_edit_server import (
    AuthConfig,
    Config,
    NextEditServices,
)
from services.working_set.client.client import WorkingSetClient

mock_config: Config = Config(
    port=1234,
    model_name="my_model",
    edit_gen_host_endpoints={"default": "str"},
    edit_gen_ranker_host_endpoints={"default": "str"},
    description_host_endpoints={"default": "str"},
    content_manager_endpoint="str",
    working_set_endpoint=None,
    generation_retrieval=retriever_factory.RetrievalConfig(),
    secret_logs_enabled=False,
    location_retrieval=retriever_factory.RetrievalConfig(),
    handler_type="NextEditHandler",
    handler_config={},
    auth_config=AuthConfig(
        token_exchange_endpoint="str",
    ),
    max_handler_workers=4,
    max_server_workers=32,
)


def test_next_edit_server_stream():
    """Test next edit server stream response"""
    mock_handler = MagicMock(spec=NextEditHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = next_edit_pb2.NextEditRequest(model_name="my_model")
    service = NextEditServices(
        config=mock_config,
        handler=mock_handler,
        working_set_client=mock_working_set_client,
        service_auth=MagicMock(),
    )

    num_responses = 5

    def response_generator():
        for i in range(num_responses):
            yield NextEditResult(
                suggested_edit=ScoredFileHunk(
                    path="foo/bar.py",
                    blob_name="blob",
                    char_start=0,
                    char_end=10,
                    existing_code="test",
                    suggested_code=f"passes {i}",
                    localization_score=0.0,
                    editing_score=0.5,
                    truncation_char=None,
                    diff_spans=[],
                ),
                unknown_blob_names=[],
                checkpoint_not_found=False,
            )

    mock_handler.next_edit_stream.return_value = response_generator()

    stream_response = service.NextEditStream(mock_request, mock_context)
    assert stream_response is not None
    count = 0
    for response in stream_response:
        assert response.suggested_edit.suggested_code == f"passes {count}"
        count += 1
    assert count == num_responses, "Expected {} responses, got {}".format(
        num_responses, count
    )


def test_next_edit_server_mid_stream_cancellation():
    """Test next edit server stream response mid-stream cancellation"""
    mock_handler = MagicMock(spec=NextEditHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = next_edit_pb2.NextEditRequest(model_name="my_model")
    service = NextEditServices(
        config=mock_config,
        handler=mock_handler,
        working_set_client=mock_working_set_client,
        service_auth=MagicMock(),
    )

    num_responses = 5

    def response_generator():
        for i in range(num_responses):
            yield NextEditResult(
                suggested_edit=ScoredFileHunk(
                    path="foo/bar.py",
                    blob_name="blob",
                    char_start=0,
                    char_end=10,
                    existing_code="test",
                    suggested_code=f"passes {i}",
                    localization_score=0.0,
                    editing_score=0.5,
                    truncation_char=None,
                    diff_spans=[],
                ),
                unknown_blob_names=[],
                checkpoint_not_found=False,
            )
        e = grpc.RpcError("Mid-Stream cancellation")
        # See https://stackoverflow.com/questions/61726226
        e.code = lambda: grpc.StatusCode.CANCELLED  # pylint: disable=no-member # type: ignore
        e.details = lambda: "Mid-Stream cancellation"  # pylint: disable=no-member # type: ignore
        raise e

    mock_handler.next_edit_stream.return_value = response_generator()

    stream_response = service.NextEditStream(mock_request, mock_context)
    assert stream_response is not None

    count = 0
    for response in stream_response:
        assert response.suggested_edit.suggested_code == f"passes {count}"
        count += 1
    assert count == num_responses, "Expected {} responses before error, got {}".format(
        num_responses, count
    )
    mock_context.abort.assert_called_once()


def test_next_edit_server_mid_stream_error():
    """Test next edit server stream response mid-stream error"""
    mock_handler = MagicMock(spec=NextEditHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = next_edit_pb2.NextEditRequest(model_name="my_model")
    service = NextEditServices(
        config=mock_config,
        handler=mock_handler,
        working_set_client=mock_working_set_client,
        service_auth=MagicMock(),
    )

    num_responses = 5

    def response_generator():
        for i in range(num_responses):
            yield NextEditResult(
                suggested_edit=ScoredFileHunk(
                    path="foo/bar.py",
                    blob_name="blob",
                    char_start=0,
                    char_end=10,
                    existing_code="test",
                    suggested_code=f"passes {i}",
                    localization_score=0.0,
                    editing_score=0.5,
                    truncation_char=None,
                    diff_spans=[],
                ),
                unknown_blob_names=[],
                checkpoint_not_found=False,
            )
        raise Exception("Unknown exception")

    mock_handler.next_edit_stream.return_value = response_generator()

    stream_response = service.NextEditStream(mock_request, mock_context)
    assert stream_response is not None

    count = 0
    with pytest.raises(Exception):
        for response in stream_response:
            assert response.suggested_edit.suggested_code == f"passes {count}"
            count += 1
    assert count == num_responses, "Expected {} responses before error, got {}".format(
        num_responses, count
    )

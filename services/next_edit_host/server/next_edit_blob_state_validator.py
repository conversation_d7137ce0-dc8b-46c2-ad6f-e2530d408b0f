from typing import Sequence
from base.blob_names import blob_names_pb2
from base.diff_utils import edit_events_pb2
from base.logging.secret_logging import Secret<PERSON>ogger
from services.completion_host import completion_pb2
from services.content_manager.client.content_manager_client import (
    ContentManagerException,
    ContentManagerClient,
)
from services.lib.file_retriever.file_retriever import BlobName
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from base.blob_names.python.blob_names import FilePath


class NextEditBlobStateValidationError(Exception):
    """Raised when the input to the next edit service is invalid."""


class NextEditBlobStateValidator:
    """Validates the input to the next edit service."""

    def __init__(self, content_manager_client: ContentManagerClient):
        self.content_manager_client = content_manager_client

    def _resolve_blobs_to_blob_names(
        self,
        blobs: blob_names_pb2.Blobs,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> list[BlobName]:
        """Resolves the given blobs to blob names.

        Args:
            blobs: The blobs to resolve.
            request_context: The request context for the request.
            auth_info: The auth info for the request.

        Returns:
            A list of blob names.
        """

        if blobs.baseline_checkpoint_id is None:
            blob_names: set[bytes] = set(blobs.added) - set(blobs.deleted)
            return [name.hex() for name in blob_names]

        try:
            checkpoint_blob_names: list[bytes] | None = (
                self.content_manager_client.get_all_blobs_from_checkpoint(
                    baseline_checkpoint_id=blobs.baseline_checkpoint_id,
                    request_context=request_context,
                    tenant_id=auth_info.tenant_id,
                )
            )
        except ContentManagerException as e:
            print(f"Error getting checkpoint blob names: {e}")
            return []

        if checkpoint_blob_names is None:
            blob_names: set[bytes] = set(blobs.added) - set(blobs.deleted)
            return [name.hex() for name in blob_names]

        blob_names: set[bytes] = set(checkpoint_blob_names) | set(blobs.added) - set(
            blobs.deleted
        )
        return [name.hex() for name in blob_names]

    def validate(
        self,
        blobs: blob_names_pb2.Blobs,
        request_context: RequestContext,
        auth_info: AuthInfo,
        replacement_texts: Sequence[completion_pb2.ReplacementText],
        edit_events: Sequence[edit_events_pb2.GranularEditEvent],
        secret_logger: SecretLogger,
    ):
        """Validates the input to the next edit service.

        Args:
            blobs: The blobs object to resolve into a list of blobnames.
            request_context: The request context.
            auth_info: The auth info for the request.
            replacement_texts: The replacement texts used to reconstruct files.
            edit_events: The edit events for which to validate blob names.
            secret_logger: The secret logger to use.

        Validates:
            - Every final blobname from the edit events must be available in the blobs object or through the replacement texts.

        Returns:
            Raises an exception if the input is invalid.
        """

        # Get the blob names from the blobs object
        request_blob_names: set[BlobName] = set(
            self._resolve_blobs_to_blob_names(blobs, request_context, auth_info)
        )

        # Get the blob names from the replacement texts
        replacement_blob_names: set[BlobName] = {
            rt.expected_blob_name
            for rt in replacement_texts
            if rt.HasField("expected_blob_name")
        }

        # the available blob names is the union of the blobs object and the replacements
        available_blob_names: set[BlobName] = (
            request_blob_names | replacement_blob_names
        )

        # Get the latest blob name for each path mentioned by edit events
        path_to_latest_blob_name: dict[FilePath, BlobName] = {
            event.path: event.after_blob_name for event in edit_events
        }

        for path in path_to_latest_blob_name:
            latest_blob_name = path_to_latest_blob_name[path]

            # If newest blob names are not available, raise an error
            if latest_blob_name not in available_blob_names:
                secret_logger.secret_error(
                    f"Missing latest blob name: {latest_blob_name} for {path}"
                )
                raise NextEditBlobStateValidationError(
                    f"Edit event with unavailable blob name: {latest_blob_name}."
                )

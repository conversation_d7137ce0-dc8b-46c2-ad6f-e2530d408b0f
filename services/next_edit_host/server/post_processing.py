"""Post-processing library for the next edit host."""

import re
from dataclasses import dataclass
from typing import Sequence

import opentelemetry.trace
import structlog
import xgboost as xgb
from dataclasses_json import dataclass_json

from base.next_edit_filter.extract_next_edit_filter_features import FeatureExtractorV1
from base.tokenizers import NextEditGenSpecialTokens, Tokenizer

from base.next_edit_filter.rule_based_filters import (
    is_exact_undo_prev_spans,
    is_exact_undo_recent_changes,
)

__reexported__ = [is_exact_undo_prev_spans, is_exact_undo_recent_changes]

log = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)

_TODO_PATTERN = re.compile(r"\bTODO\b", re.IGNORECASE)


@dataclass_json
@dataclass
class LowQualityFilterConfig:
    """Configuration for the next edit filter."""

    checkpoint_path: str
    """Path to the next edit filter model."""


class LowQualityFilter:
    """Filter to predict low quality next edit suggestions."""

    def __init__(
        self,
        config: LowQualityFilterConfig,
        tokenizer: Tokenizer[NextEditGenSpecialTokens] | None = None,
    ):
        self.feature_extractor = FeatureExtractorV1(tokenizer)
        self._model = xgb.Booster(model_file=config.checkpoint_path)

    def set_tokenizer(self, tokenizer: Tokenizer[NextEditGenSpecialTokens]):
        """Set the tokenizer."""
        self.feature_extractor.set_tokenizer(tokenizer)

    @tracer.start_as_current_span("low_quality_filter")
    def __call__(
        self,
        log_probs: Sequence[float],
        output_tokens: Sequence[int],
        prompt_token_ids: Sequence[int],
    ) -> float:
        """Returns a quality score.

        The higher the score, the more likely the suggestion is low quality.

        Args:
            log_probs: The log probabilities of the output tokens.
            output_tokens: The output tokens.
            prompt: The input prompt.
        """
        try:
            features = self.feature_extractor.extract_features(
                log_probs=log_probs,
                token_ids=output_tokens,
                prompt_token_ids=prompt_token_ids,
            )
            data = self._to_dmatrix(features)
        except ValueError as e:
            log.warning(f"Failed to extract features: {e}")
            return 0.0
        return float(self._model.predict(data)[0])

    def _to_dmatrix(self, features: dict) -> xgb.DMatrix:
        """Converts features into an XGBoost DMatrix."""
        feature_names = self.feature_extractor.get_features_name()
        return xgb.DMatrix(
            [[features[key] for key in feature_names]],
            feature_names=feature_names,
        )


def contains_inserted_todo(existing_code: str, suggested_code: str) -> bool:
    """Check if the suggestion inserts a new TODO."""
    existing_todo_count = len(_TODO_PATTERN.findall(existing_code))
    suggested_todo_count = len(_TODO_PATTERN.findall(suggested_code))
    return suggested_todo_count > existing_todo_count

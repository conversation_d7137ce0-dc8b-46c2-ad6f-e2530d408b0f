"""Contains the core logic of handling next edit requests."""

import concurrent.futures
import dataclasses
import enum
import json
import logging
import math
import queue
import multiprocessing
import threading
import uuid
from contextlib import contextmanager
from collections.abc import Iterable, Sequence
from dataclasses import asdict, dataclass, field
from queue import Queue, Empty
from threading import Semaphore
from typing import Iterator, Union

import grpc
import opentelemetry.trace
from services.inference_host.client import inference_host_client
import structlog
from dataclasses_json import dataclass_json
from typing_extensions import override

from base import feature_flags
from base.blob_names.python.blob_names import BlobName, Blobs
from base.diff_utils.apply_replacements_to_files import FileReplacementError
from base.diff_utils.changes import Changed
from base.diff_utils.diff_utils import File
from base.diff_utils.edit_events import (
    GranularEditEvent,
    SquashableEdits,
    grouped_events_to_file_changes,
)
from base.diff_utils.git_conflict_utils import contains_git_conflict_marker
from base.diff_utils.proto_wrapper import from_proto
from base.diff_utils.str_diff import (
    align_spans_to_word_boundaries,
    precise_char_diff,
    precise_line_diff,
)
from base.logging.secret_logging import SecretLogger, get_safe_logger
from base.next_edit_filter.rule_based_filters import is_deleting_imports
from base.prompt_format.common import TokenList
from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name
from base.prompt_format_chat.prompt_formatter import StructToTokensPromptFormatter
from base.prompt_format_next_edit.common import NextEditPromptInput
from base.prompt_format_next_edit.description_prompt_formatter import (
    EditDescriptionPromptFormatter,
    RavenDescribePromptFormatter,
)
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    EditGenPromptFormatter,
    EditGenPromptInput,
    fix_truncation_newline,
)
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
)
from base.prompt_format_next_edit.retrieval_prompt_formatter import (
    EditGenRetrievalPromptInput,
)
from base.ranges.range_types import CharRange
from base.tokenizers import create_tokenizer_by_name
from services.lib.retrieval.multi_retriever import (
    multi_retriever_find_missing,
)
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    Retriever,
)
from services.content_manager.client import content_cache
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.inference_host.infer_pb2_grpc import InfererStub
from services.lib.file_retriever.file_retriever import FileRetriever
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server import post_processing
from services.next_edit_host.server.file_reconstruction import (
    get_current_version_of_outdated_files,
)
from services.next_edit_host.server.handler import (
    NextEditHandlerProtocol,
    NextEditResult,
    ScoredFileHunk,
)
from services.next_edit_host.server.next_edit_blob_state_validator import (
    NextEditBlobStateValidationError,
    NextEditBlobStateValidator,
)
from services.next_edit_host.server.next_edit_descriptions import (
    DescriptionGenerationConfig,
    DescriptionHeuristicConfig,
    NextEditDescriptionService,
)
from services.next_edit_host.server.next_edit_locations import (
    LocationsServiceConfig,
    NextEditLocationsService,
)
from services.next_edit_host.server.next_edit_metrics import (
    record_change_location_index,
    record_context_processing_duration_context,
    record_diff_events_problems,
    record_first_change_location_index,
    record_generation_formatting_duration_context,
    record_generation_inference_duration_context,
    record_generation_retrieval_duration_context,
    record_inference_cache_hit,
    record_inference_cache_miss,
    record_location_retrieval_duration_context,
    record_lock_timeout,
    record_low_quality_filter_duration_context,
    record_post_processing_generation,
    record_post_processing_hunk,
    record_suggestions_queue_timeout,
    record_thread_not_done,
    record_thread_result_timeout,
)
from services.next_edit_host.server.next_edit_request_insight_builder import (
    GenerationEventBuilder,
    NextEditRequestInsightBuilder,
    NextEditResponseBuilder,
)
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.next_edit_host.server.next_edit_utils import (
    FilePath,
    NextEditCancelledException,
    CancellationReason,
    RecentChangeTracker,
    SamplingParams,
    is_ignored_file_path,
    safeTrue,
    yield_and_execute,
)
from services.request_insight import request_insight_pb2
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.next_edit_host.lru_dict import LRUDict


logger: logging.Logger = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)

_NEXT_EDIT_LOW_QUALITY_THRESHOLD_STRICT = feature_flags.StringFlag(
    "next_edit_low_quality_threshold_strict", "1.0"
)

MAX_CHANGES_PER_FILE_REQUEST: int = 3
"""The maximum # of changes to return per file scoped background request."""

MAX_LOCATIONS_PER_FILE_REQUEST: int = 8
"""The maximum number of locations to _consider_ per file scoped request."""

MAX_CHANGES_PER_WORKSPACE_REQUEST: int = 1
"""The maximum # of changes to return per workspace scoped background request."""

MAX_LOCATIONS_PER_WORKSPACE_REQUEST: int = 32
"""The maximum number of locations to rerank in workspace mode."""

DIFF_CONTEXT_LINES: int = 2
"""The number of context lines to use when grouping changes in returned hunks."""

RESULT_TIMEOUT_SECONDS = 5  # Maximum time to wait for results per request


@dataclass_json
@dataclass(frozen=True)
class EditGenerationConfig:
    """Configuration for edit generation inference calls."""

    tokenizer_name: str
    """Name of the tokenizer to use."""

    prompt_formatter_name: str
    """Name of the prompt formatter to use."""

    prompt_formatter_config: EditGenFormatterConfig
    """Configuration for the prompt formatter."""

    ranker_gen_prompt_formatter_config: EditGenFormatterConfig
    """Configuration for the prompt formatter."""

    max_output_length: int
    """The maximal number of tokens in the output.

    If not set, max_sequence_length will be used to compute this bound.
    """

    sampling_params: SamplingParams
    """The sampling parameters for the edit generation model."""


@dataclass_json
@dataclass(frozen=True)
class NextEditHandlerConfig:
    """Config for next edit model.

    The source of truth for defaults in this and child structures is handler_config_default.json.
    If you need to add a new override to a deploy, please make sure the overridden field is also
    present in the default config; otherwise the kubecfg task should fail an assert. We check the
    default config against the python schema in the next_edit_config_test.py unit test.
    """

    edit_generation_config: EditGenerationConfig
    """Config for the edit generation model."""

    content_cache_size_mb: int
    """Size in MB of the LRU content cache, containing retrieved blobs.

    This includes files that are part of any recent changes so we can compute diffs.
    """

    description_generation_config: DescriptionGenerationConfig
    """Config for the description generation model."""

    description_heuristic_config: DescriptionHeuristicConfig | None
    """Config for the heuristic descriptions.

    If None, will disable heuristic descriptions.
    """

    reranker_filter_threshold: float | None
    """Locations with a reranker score below this threshold will be filtered out.

    None means no filtering by reranker.
    """

    max_smart_chunk_size: int
    """The max smart chunk size used to chunk locations.

    This should only be overridden during unit tests.
    """

    ignore_requests_with_no_file_changes: bool = True
    """Whether to ignore requests with an empty list of reconstructed file changes.

    Should only be set to false in tests.
    """

    change_probability_min: dict[str, float] = field(default_factory=dict)
    """Minimum change probability for requests of each mode."""

    max_filter_score: dict[str, float] = field(default_factory=dict)
    """Maximum filter score for requests of each mode. This config is mutually exclusive
    with `change_probability_min`.
    """

    validate_blob_state: bool = False
    """Whether to validate the blob state of the request."""

    def __post_init__(self):
        if self.change_probability_min and self.max_filter_score:
            raise ValueError(
                "'change_probability_min' and 'max_filter_score' are mutually exclusive. Only one should be set."
            )


@dataclass
class NextEditRequestState:
    """State for a single next edit request."""

    request: NextEditRequest
    """Immutable state for the request."""

    response_ri: NextEditResponseBuilder
    """The request insight builder for the response."""

    executor: concurrent.futures.Executor
    """The executor to use for this request."""

    missing_blobs: set[BlobName]
    """The set of blobs that are missing."""

    checkpoint_not_found: bool
    """Whether the checkpoint was not found."""

    reported_blobs: set[BlobName]
    """The set of blobs that have been reported as missing."""

    total_threads_semaphore: Semaphore
    """A semaphore to limit the total number of threads used for this request."""

    retrieval_semaphore: Semaphore
    """A semaphore to limit concurrent retrieval calls."""

    generation_semaphore: Semaphore
    """A semaphore to limit concurrent generation calls."""

    description_semaphore: Semaphore
    """A semaphore to limit concurrent description calls."""

    suggestion_count: int
    """The number of suggestions generated so far."""

    change_count: int
    """The number of changes generated so far."""

    change_tracker: RecentChangeTracker
    """The tracker to manage recent changes."""

    # TODO: keep consistent casing
    responseQueue: Queue[NextEditResult | None]
    """The queue to use for responses."""

    path_to_blocked_ranges: dict[str, set[CharRange]]
    """A mapping from file paths to blocked ranges."""


class _QualityScoreType(enum.Enum):
    PROBABILITY = "probability"
    QUALITY = "quality"


@dataclass(frozen=True)
class InferenceCallInput:
    prompt: tuple[int, ...]
    max_output_tokens: int


class NextEditHandler(NextEditHandlerProtocol):
    """Handles next edit requests."""

    def __init__(
        self,
        config: NextEditHandlerConfig,
        edit_gen_client: InfererClient,
        edit_prompt_formatter: EditGenPromptFormatter,
        edit_gen_ranker_client: InfererClient | None,
        edit_gen_ranker_prompt_formatter: EditGenPromptFormatter,
        description_gen_client: InfererClient | None,
        description_prompt_formatter: EditDescriptionPromptFormatter | None,
        chat_prompt_formatter: StructToTokensPromptFormatter | None,
        namespace: str,
        ri_publisher: RequestInsightPublisher,
        content_manager_client: ContentManagerClient,
        generation_retriever: Retriever[EditGenRetrievalPromptInput],
        location_retriever: Retriever[NextEditPromptInput],
        secret_logs_enabled: bool = False,
        low_quality_filter: post_processing.LowQualityFilter | None = None,
    ):
        """Initialize the handler.

        Args:
            config: The configuration for the handler.
            edit_gen_stub: The stub for the edit generation model.
            edit_prompt_formatter: The prompt formatter for the edit generation model.
            description_gen_stub: The stub for the description generation model.
            description_prompt_formatter: The prompt formatter for the description generation model.
            chat_prompt_formatter: The prompt formatter for the chat model.
            namespace: The namespace in which this handler is running.
            ri_publisher: The request insight publisher.
            content_manager_client: The content manager client.
            generation_retriever: The retriever used for edit generation.
            location_retriever: If provided, the retriever used for edit localization.
        """
        self.config = config
        self.edit_gen_client = edit_gen_client
        self.edit_prompt_formatter = edit_prompt_formatter
        self.namespace = namespace
        self.generation_retriever = generation_retriever
        self.location_retriever = location_retriever
        self.content_manager_client = content_manager_client
        self.ri_builder = NextEditRequestInsightBuilder(ri_publisher)
        self.safe_logger = get_safe_logger(logger, secret_logs_enabled)
        self.low_quality_filter = low_quality_filter
        self._format_prompt_semaphore = threading.Semaphore(multiprocessing.cpu_count())

        self.content_cache = content_cache.LRUContentCache(
            content_manager=self.content_manager_client,
            transform_from_bytes=lambda _, bytes: bytes.decode("utf-8"),
            max_size=self.config.content_cache_size_mb * (2**20),
            max_elem_size=None,
            max_missing_entries_to_download=None,
        )

        special_tokens = self.edit_prompt_formatter.special_tokens
        self.edit_end_token_ids = {
            special_tokens.eos,
            special_tokens.pause,
            special_tokens.no_change,
        }

        self.file_retriever = FileRetriever(
            content_manager_client,
            config.content_cache_size_mb,
        )

        self.blob_state_validator: NextEditBlobStateValidator | None = (
            NextEditBlobStateValidator(content_manager_client=content_manager_client)
            if config.validate_blob_state
            else None
        )

        locations_service_config = LocationsServiceConfig(
            max_smart_chunk_size=config.max_smart_chunk_size,
            max_prompt_tokens=config.edit_generation_config.prompt_formatter_config.max_prompt_tokens,
            sampling_params=config.edit_generation_config.sampling_params,
            edit_end_token_ids=self.edit_end_token_ids,
            has_change_token_id=self.edit_prompt_formatter.special_tokens.has_change,
            max_locations_per_file_request=MAX_LOCATIONS_PER_FILE_REQUEST,
            max_locations_per_workspace_request=MAX_LOCATIONS_PER_WORKSPACE_REQUEST,
            reranker_filter_threshold=config.reranker_filter_threshold,
            secret_logs_enabled=secret_logs_enabled,
        )

        self.next_edit_locations_service = NextEditLocationsService(
            namespace=namespace,
            location_retriever=location_retriever,
            edit_prompt_formatter=edit_gen_ranker_prompt_formatter,
            file_retriever=self.file_retriever,
            config=locations_service_config,
            edit_gen_ranker_client=edit_gen_ranker_client,
            format_prompt_semaphore=self._format_prompt_semaphore,
        )

        self._lock = threading.Lock()
        self.session_id_to_timestamp: dict[str, float] = LRUDict(1024)
        # assuming an average prompt length of 10K tokens, this should use at most
        # ~40 MB of memory
        self._inference_cache = LRUDict[
            InferenceCallInput, InferenceClientProtocol.Reply
        ](max_size=512)

        if (
            config.description_generation_config is None
            or description_gen_client is None
            or description_prompt_formatter is None
            or chat_prompt_formatter is None
        ):
            logger.info(
                "Description generation service not configured. One of the following is missing: "
                f"\n\tconfig.description_generation_config: {config.description_generation_config is None}"
                f"\n\tdescription_gen_client: {description_gen_client is None}"
                f"\n\tdescription_prompt_formatter: {description_prompt_formatter is None}"
                f"\n\tchat_prompt_formatter: {chat_prompt_formatter is None}"
            )
            self.next_edit_description_service = None
        else:
            self.next_edit_description_service = NextEditDescriptionService(
                description_generation_config=config.description_generation_config,
                heuristic_config=config.description_heuristic_config,
                description_gen_client=description_gen_client,
                description_prompt_formatter=description_prompt_formatter,
                chat_prompt_formatter=chat_prompt_formatter,
            )

    def _infer_edit_gen(
        self,
        input_tokens: TokenList,
        request: NextEditRequest,
    ) -> InferenceClientProtocol.Reply:
        config = self.config.edit_generation_config
        max_context_length = config.prompt_formatter_config.max_prompt_tokens
        assert (
            len(input_tokens) < max_context_length
        ), f"{len(input_tokens)=} must be less than {max_context_length=}"
        remaining_context_length = max_context_length - len(input_tokens)
        if (max_output_length := config.max_output_length) is None:
            max_output_length = remaining_context_length
        else:
            max_output_length = min(remaining_context_length, max_output_length)

        cur_sampling_params = config.sampling_params
        cur_seed = cur_sampling_params.seed

        infer_input = InferenceCallInput(
            prompt=tuple(input_tokens), max_output_tokens=max_output_length
        )

        mode = request.proto.mode
        match mode:
            case next_edit_pb2.NextEditMode.BACKGROUND:
                priority = 0
            case next_edit_pb2.NextEditMode.FOREGROUND:
                priority = 1
            case next_edit_pb2.NextEditMode.FORCED:
                # same as foreground since both trump background but not each other
                priority = 1
            case _:
                raise ValueError(f"Unknown mode: {mode}")

        with self._lock:
            cached_output = self._inference_cache.get(infer_input)
        if cached_output is not None:
            logger.info("[Inference cache] Using cached inference output.")
            record_inference_cache_hit(request)
            return cached_output

        logger.info(
            "[Inference cache] No cached inference output, calling inference server."
        )
        record_inference_cache_miss(request)
        reply = self.edit_gen_client.infer(
            input_tokens=input_tokens,
            max_output_length=max_output_length,
            end_token_ids=self.edit_end_token_ids,
            top_k=cur_sampling_params.top_k,
            top_p=cur_sampling_params.top_p,
            temperature=cur_sampling_params.temperature,
            random_seed=cur_seed,
            request_context=request.context,
            timeout_s=cur_sampling_params.inference_timeout_s,
            sequence_id=request.proto.sequence_id,
            priority=priority,
        )
        with self._lock:
            self._inference_cache[infer_input] = reply

        return reply

    @contextmanager
    def _acquire_format_prompt_semaphore(self):
        """Acquire the format prompt semaphore with timeout and proper logging.

        Yields:
            bool: True if the semaphore was acquired, False otherwise.
        """
        acquired = self._format_prompt_semaphore.acquire(timeout=RESULT_TIMEOUT_SECONDS)
        if not acquired:
            self.safe_logger.warn("Timeout acquiring format_prompt_semaphore")
            record_lock_timeout("format_prompt_semaphore")
            yield False
            return

        try:
            yield True
        finally:
            self._format_prompt_semaphore.release()

    def _generate_edits_for_location(
        self,
        state: NextEditRequestState,
        prompt_input: EditGenPromptInput,
        must_change: bool,
        quality_score_type: _QualityScoreType | None,
        quality_score_threshold: float | None,
        squashable_edits: SquashableEdits,
        ri_builder: GenerationEventBuilder,
        change_tracker: RecentChangeTracker,
        location_index: int,
    ) -> Iterable[ScoredFileHunk]:  # TODO: consider changing return type to list.
        with tracer.start_as_current_span(
            "edit_gen_retrieval",
            attributes={"generation_id": ri_builder.get_generation_id()},
        ), record_generation_retrieval_duration_context(state.request):
            retrieval_prompt_input = RetrievalInput(
                EditGenRetrievalPromptInput(
                    current_file=prompt_input.current_file,
                    edit_region=prompt_input.edit_region,
                    instruction=prompt_input.instruction,
                    recent_changes=prompt_input.recent_changes,
                ),
                blobs=[Blobs.from_proto(state.request.proto.blobs)],
                namespace=self.namespace,
            )

            if not state.retrieval_semaphore.acquire(timeout=RESULT_TIMEOUT_SECONDS):
                logger.warning("Timeout acquiring retrieval_semaphore")
                record_lock_timeout("retrieval_semaphore")
                return []
            try:
                logger.info("Calling content retriever")
                ret_result = self.generation_retriever.retrieve(
                    retrieval_prompt_input,
                    state.request.context,
                    state.request.auth_info,
                )
            finally:
                state.retrieval_semaphore.release()
            retrieved_chunks = list(ret_result.get_retrieved_chunks())

            indexed_files = self.file_retriever.retrieve_indexed_files(
                blob_name_to_file_path={
                    chunk.blob_name: chunk.path
                    for chunk in retrieved_chunks
                    if chunk.blob_name
                },
                auth_info=state.request.auth_info,
                missing_blobs=state.missing_blobs,
                request_context=state.request.context,
                expected=True,  # since the chunk was just retrieved, we expect it to be in CM
            )
            change_tracker.add_indexed_files(indexed_files.values())

            retrieved_chunks = [
                change_tracker.update_indexed_chunk_to_current(chunk, self.safe_logger)
                for chunk in retrieved_chunks
            ]

            state.missing_blobs.update(ret_result.get_missing_blob_names())
            state.checkpoint_not_found |= ret_result.get_checkpoint_not_found()
            prompt_input = dataclasses.replace(
                prompt_input,
                retrieval_chunks=retrieved_chunks,
            )
            logger.info(
                "Received %d chunks from generation retriever",
                len(retrieved_chunks),
            )
            ri_builder.add_retrieved_chunks(retrieved_chunks)

        with tracer.start_as_current_span(
            "format_prompt",
            attributes={"generation_id": ri_builder.get_generation_id()},
        ), record_generation_formatting_duration_context(
            state.request
        ), self._acquire_format_prompt_semaphore() as acquired:
            if not acquired:
                return []

            try:
                prompt_output = self.edit_prompt_formatter.format_input_prompt(
                    prompt_input
                )
                logger.info(
                    f"Formatted location {location_index} generation prompt with {len(prompt_output.tokens)} tokens. Timing: {prompt_output.timing}"
                )
            finally:
                # Always release the semaphore, even if an exception occurs
                self._format_prompt_semaphore.release()

            has_change_id = self.edit_prompt_formatter.special_tokens.has_change
            # We force the model to make a change by adding the has_change token to the prompt.
            if must_change:
                prompt_output.tokens.append(has_change_id)
            ri_builder.add_generation_prompt(
                self.edit_prompt_formatter.tokenizer,
                prompt_output.tokens,
            )

            if not state.generation_semaphore.acquire(timeout=RESULT_TIMEOUT_SECONDS):
                logger.warning("Timeout acquiring generation_semaphore")
                record_lock_timeout("generation_semaphore")
                return []
            try:
                if (
                    state.request.proto.mode == next_edit_pb2.NextEditMode.BACKGROUND
                    and state.change_count
                    >= (
                        MAX_CHANGES_PER_WORKSPACE_REQUEST
                        if state.request.proto.scope
                        == next_edit_pb2.NextEditScope.WORKSPACE
                        # note this applies to both FILE and CURSOR scopes at the moment
                        else MAX_CHANGES_PER_FILE_REQUEST
                    )
                ):
                    logger.info(
                        f"Stopping before generation on {state.change_count=} reached"
                    )
                    return []

                logger.info("Generating edit for location")
                with tracer.start_as_current_span(
                    "edit_gen_inference",
                    attributes={"generation_id": ri_builder.get_generation_id()},
                ), record_generation_inference_duration_context(
                    state.request
                ) as set_has_change:
                    generation_output = self._infer_edit_gen(
                        prompt_output.tokens,
                        state.request,
                    )
                    set_has_change(
                        must_change
                        or generation_output.output_tokens[0] == has_change_id
                    )
                    ri_builder.add_generation_output(
                        self.edit_prompt_formatter.tokenizer,
                        generation_output.output_tokens,
                        generation_output.log_probs,
                    )

                with tracer.start_as_current_span(
                    "post_process",
                    attributes={"generation_id": ri_builder.get_generation_id()},
                ):
                    # reply has the generated output tokens and probabilities... make use of them to form response.
                    output_tokens = list(generation_output.output_tokens)
                    log_probs = (
                        list(generation_output.log_probs)
                        if generation_output.log_probs
                        else []
                    )

                    if must_change:
                        prob_changed = 1.0
                        diff_sequence_prob = math.exp(sum(log_probs, 0.0))
                        # we prepend the has_change token to output to pretend it was generated
                        output_tokens = [has_change_id] + output_tokens
                    else:
                        if output_tokens[0] == has_change_id:
                            prob_changed = math.exp(log_probs[0])
                        else:
                            prob_changed = 1 - math.exp(log_probs[0])
                        diff_sequence_prob = math.exp(sum(log_probs[1:], 0.0))

                    decoded = self.edit_prompt_formatter.decode_output_tokens(
                        output_tokens,
                        prompt_input.selected_code,
                    )
                    decoded = fix_truncation_newline(decoded)

                    editing_score = diff_sequence_prob
                    generated_text = decoded.replacement
                    truncation_char = decoded.truncation_char
                    if decoded.changed and not must_change:
                        if self.low_quality_filter is not None:
                            # Use filter score for `editing_score`.
                            with record_low_quality_filter_duration_context(
                                state.request
                            ):
                                editing_score = self.low_quality_filter(
                                    log_probs, output_tokens, prompt_output.tokens
                                )
                                logger.info(
                                    "Low quality filter score: %s", editing_score
                                )
                        ri_builder.add_editing_score(editing_score)
                        if quality_score_type == _QualityScoreType.PROBABILITY:
                            quality_score = prob_changed
                        elif quality_score_type == _QualityScoreType.QUALITY:
                            quality_score = editing_score
                        else:
                            logger.info("No quality score threshold configured.")
                            quality_score = None

                        if _should_filter_generation(
                            prompt_input=prompt_input,
                            replacement=decoded.replacement,
                            squashable_edits=squashable_edits,
                            quality_score=quality_score,
                            quality_threshold=quality_score_threshold,
                            quality_score_type=quality_score_type,
                            request=state.request,
                            ri_builder=ri_builder,
                            safe_logger=self.safe_logger,
                        ):
                            self.safe_logger.warn("Filtering out edit.")
                            location_path = prompt_input.current_path
                            location_range = prompt_input.edit_region
                            self.safe_logger.secret_warn(
                                f"location={str(location_path)}:{location_range.start}-{location_range.stop}"
                            )
                            generated_text = prompt_input.selected_code
                            truncation_char = None

                    context = (
                        feature_flags.get_global_context()
                        .bind_attribute(
                            "tenant_name",
                            state.request.auth_info.tenant_name,
                        )
                        .bind_attribute(
                            "model_name",
                            state.request.proto.model_name,
                        )
                    )
                    editing_score_threshold = float(
                        _NEXT_EDIT_LOW_QUALITY_THRESHOLD_STRICT.get(context)
                    )

                    total_change = ScoredFileHunk(
                        path=prompt_input.current_file.path,
                        blob_name=prompt_input.current_file.blob_name,
                        char_start=prompt_input.edit_region.start,
                        char_end=prompt_input.edit_region.stop,
                        existing_code=prompt_input.selected_code,
                        suggested_code=generated_text,
                        localization_score=prob_changed,
                        editing_score=editing_score,
                        editing_score_threshold=editing_score_threshold,
                        truncation_char=truncation_char,
                        diff_spans=[],  # will be computed by `_split_changes_into_hunks`
                    )
                    edit_suggestions = list[ScoredFileHunk]()
                    logger.info("splitting change to hunks")
                    for hunk in _split_changes_into_hunks(
                        total_change, n_context_lines=DIFF_CONTEXT_LINES
                    ):
                        hunk.post_process_result = _post_process_hunk(
                            hunk,
                            state.request,
                            state.path_to_blocked_ranges,
                            self.safe_logger,
                        )
                        if hunk.post_process_result not in (
                            request_insight_pb2.RINextEditSuggestion.PostProcessResult.NOOP,
                            request_insight_pb2.RINextEditSuggestion.PostProcessResult.UNKNOWN,
                        ):
                            # turn the hunk into a no-op since the front end still wants the
                            # range to avoid resending unnecessary requests.
                            hunk = hunk.to_no_change()
                        edit_suggestions.append(hunk)

                    change_count = sum(
                        1
                        for edit_suggestion in edit_suggestions
                        if edit_suggestion.has_change
                    )
                    if change_count > 0 and state.change_count == 0:  # first change
                        record_first_change_location_index(
                            state.request,
                            location_index,
                        )
                    record_change_location_index(
                        state.request,
                        location_index,
                        has_change=(change_count > 0),
                    )
                    # Useful to update this under the semaphore so races between different locations
                    # can't cause us to exceed the desired change count
                    state.change_count += change_count
                return edit_suggestions
            finally:
                state.generation_semaphore.release()

    def _edit_location(
        self,
        state: NextEditRequestState,
        location: RetrievalChunk,
        recent_changes: Sequence[Changed[File]],
        squashable_edits: SquashableEdits,
        location_index: int,
    ) -> None:
        """Generate edits for a single location."""
        ri_generation = state.response_ri.record_generation()
        structlog.contextvars.bind_contextvars(
            location_index=location_index, thread_name=threading.current_thread().name
        )  # automatically cleaned by structlog

        ri_generation.add_location(location)
        logger.info(
            f"Generating edits for location: {location.chunk_index=}; "
            f"{location.blob_name=}:{location.crange=} ({location.origin=})."
        )

        try:
            if location.blob_name is None:
                self.safe_logger.error("Location has no blob name.")
                self.safe_logger.secret_error(f"{location.path=}, {location.origin=}")
                return
            location_file = state.change_tracker.get_current_file(
                location.blob_name, location.path
            )
            if location_file is None:
                self.safe_logger.error(
                    f"Can't find full file for location: {location.blob_name}."
                )
                self.safe_logger.secret_error(f"{location.path=}, {location.origin=}")
                return

            self.safe_logger.info(
                f"Starting to generate edits for location {location.blob_name=}:{location.crange=}"
            )
            self.safe_logger.secret_info(f"{location.path=}, {location.origin=}")

            # Derive mode and prob_change_min from the request
            mode = state.request.proto.mode
            match mode:
                case next_edit_pb2.NextEditMode.BACKGROUND:
                    must_change = False
                case next_edit_pb2.NextEditMode.FOREGROUND:
                    must_change = False
                case next_edit_pb2.NextEditMode.FORCED:
                    must_change = True
                case _:
                    raise ValueError(f"Unknown mode: {mode}")
            mode_name = next_edit_pb2.NextEditMode.Name(mode)
            # TODO: clean. create a filter object and pass it to _generate_edits_for_location
            if self.config.change_probability_min:
                quality_score_threshold = self.config.change_probability_min.get(
                    mode_name
                )
                quality_score_type = _QualityScoreType.PROBABILITY
            elif self.config.max_filter_score:
                quality_score_threshold = self.config.max_filter_score.get(mode_name)
                quality_score_type = _QualityScoreType.QUALITY
            else:
                logger.info("No quality score threshold configured.")
                quality_score_type = None
                quality_score_threshold = None
            if state.request.proto.HasField("change_probability_override"):
                # Overrides `quality_score_threshold`.
                logger.info(
                    "Overriding change probability minimum for this request: %s",
                    state.request.proto.change_probability_override,
                )
                quality_score_threshold = (
                    state.request.proto.change_probability_override
                )

            with tracer.start_as_current_span(
                "next_edit_generation",
                attributes={"generation_id": ri_generation.get_generation_id()},
            ):
                location_prompt_input = EditGenPromptInput(
                    current_file=location_file,
                    edit_region=location.crange,
                    instruction=state.request.proto.instruction,
                    recent_changes=recent_changes,
                    retrieval_chunks=[],
                )

                edit_suggestions = list(
                    self._generate_edits_for_location(
                        state=state,
                        prompt_input=location_prompt_input,
                        must_change=must_change,
                        quality_score_type=quality_score_type,
                        quality_score_threshold=quality_score_threshold,
                        squashable_edits=squashable_edits,
                        ri_builder=ri_generation,
                        change_tracker=state.change_tracker,
                        location_index=location_index,
                    )
                )
                logger.info(f"Got {len(edit_suggestions)} edit suggestions.")
                state.executor.submit(
                    yield_and_execute,  # TODO: review if we still need this.
                    ri_generation.publish,
                )

                for edit_suggestion in edit_suggestions:
                    suggestion_id = str(uuid.uuid4())
                    logger.info(
                        f"Generating description for edit suggestion {suggestion_id}"
                    )
                    with tracer.start_as_current_span(
                        "next_edit_suggestion",
                        attributes={"suggestion_id": suggestion_id},
                    ):
                        ri_suggestion = state.response_ri.record_suggestion(
                            ri_generation.get_generation_id(),
                            state.suggestion_count,
                        )
                        if self.next_edit_description_service is None:
                            logger.info(
                                "No description generation service configured, "
                                "skipping description generation."
                            )
                            change_description = ""
                            markdown_description = ""
                        else:
                            if not state.description_semaphore.acquire(
                                timeout=RESULT_TIMEOUT_SECONDS
                            ):
                                logger.warning(
                                    "Timeout acquiring description_semaphore"
                                )
                                record_lock_timeout("description_semaphore")
                                return None  # or appropriate error handling
                            try:
                                (
                                    change_description,
                                    markdown_description,
                                ) = self.next_edit_description_service.get_description_for_edit(
                                    edit_file=location_file,  # all edit suggestions come from the same location
                                    edit_suggestion=edit_suggestion,
                                    edit_suggestion_id=suggestion_id,
                                    request=state.request,
                                    ri_builder=ri_suggestion,
                                )
                            finally:
                                state.description_semaphore.release()
                        edit_suggestion = dataclasses.replace(
                            edit_suggestion,
                            change_description=change_description,  # no markdown
                            markdown_change_description=markdown_description,
                            suggestion_id=suggestion_id,  # TODO(moogi): suggestion_id is probably an important field, and we don't want to miss it. Consider generating it at the source.
                        )

                        report_blobs = state.missing_blobs - state.reported_blobs
                        state.reported_blobs.update(report_blobs)
                        next_edit_result = NextEditResult(
                            suggested_edit=edit_suggestion,
                            unknown_blob_names=list(report_blobs),
                            checkpoint_not_found=state.checkpoint_not_found,
                        )

                        ri_suggestion.add_response(next_edit_result)
                        state.executor.submit(
                            yield_and_execute,
                            ri_suggestion.publish,
                        )
                        state.suggestion_count += 1
                        logger.info(
                            f"Putting edit suggestion {suggestion_id} to streaming queue"
                        )
                        state.responseQueue.put(next_edit_result)
        except grpc.RpcError as e:
            if e.code() != grpc.StatusCode.CANCELLED:  # pylint: disable=no-member # type: ignore
                logger.error(f"Error generating edits for location: {e}")
            raise
        finally:
            state.total_threads_semaphore.release()
        logger.info("Finished generating edits for location.")

    def generate_suggestions(
        self,
        state: NextEditRequestState,
        prompt_input: LocalizationNextEditPromptInput,
        squashable_edits: SquashableEdits,
    ):
        """Process all locations, allowing N to be in progress at a time."""

        locations = self.next_edit_locations_service.get_current_locations(
            prompt_input,
            state.request,
            state.response_ri,
            state.executor,
            state.missing_blobs,
            state.change_tracker,
            state.path_to_blocked_ranges,
        )

        futures: list[concurrent.futures.Future[None]] = []
        i = 0
        # Artificial iteration pattern so that we can properly trace and time the generator work
        for _ in safeTrue(
            100, warning_message="Next Edit generation is taking longer than expected."
        ):
            with tracer.start_as_current_span(
                "get_location",
            ), record_location_retrieval_duration_context(state.request):
                location = next(locations, None)
            if location is None:
                break

            if not state.total_threads_semaphore.acquire(
                timeout=RESULT_TIMEOUT_SECONDS
            ):
                logger.warning("Timeout acquiring total_threads_semaphore")
                record_lock_timeout("total_threads_semaphore")
                break
            futures.append(
                state.executor.submit(
                    self._edit_location,
                    state,
                    location,
                    prompt_input.recent_changes,
                    squashable_edits,
                    i,
                )
            )

            if (
                state.request.proto.mode == next_edit_pb2.NextEditMode.BACKGROUND
                and i + 1 > MAX_LOCATIONS_PER_FILE_REQUEST
            ):
                logger.info(f"Stopping after location #{i} reached")
                break

            i += 1

        # Adding a larger buffer since there will be multiple futures in flight.
        # timed out tasks will not throw an error here.
        # the second return value is the list of futures that did not complete.
        # we simply ignore them.
        done, not_done = concurrent.futures.wait(
            futures, timeout=RESULT_TIMEOUT_SECONDS * 3
        )
        if len(not_done) > 0:
            logger.info(
                f"Not all futures completed. {len(not_done)} futures did not complete."
            )
            record_thread_not_done(state.request, len(not_done))
        logger.info("Putting None to signal end of suggestions.")
        state.responseQueue.put(None)
        for future in done:
            try:
                future.result(timeout=RESULT_TIMEOUT_SECONDS)  # raise exception if any
            except concurrent.futures.TimeoutError as e:  # pylint: disable=broad-except
                logger.info(f"A thread has timed out error: {e}")
                record_thread_result_timeout(state.request)
        logger.info("Verified all threads done.")

    def next_edit_stream(
        self,
        request: NextEditRequest,
        executor: concurrent.futures.Executor,
    ) -> Iterator[NextEditResult]:
        """Get a stream of edit results with the location retriever."""
        # Update the sequence id for the session.
        with self._lock:
            # Check if the request is already stale.
            self._cancel_request_if_needed(
                request, "Cancelled before starting any work."
            )
            self.session_id_to_timestamp[request.context.request_session_id] = (
                request.proto.client_created_at.ToDatetime().timestamp()
            )

        scope_name = next_edit_pb2.NextEditScope.Name(request.proto.scope)
        mode_name = next_edit_pb2.NextEditMode.Name(request.proto.mode)
        logger.info(
            f"Next edit stream started with scope {scope_name} and mode {mode_name}"
        )

        current_span = opentelemetry.trace.get_current_span()
        current_span.set_attribute("next_edit_mode", mode_name)
        current_span.set_attribute("next_edit_scope", scope_name)

        executor.submit(
            yield_and_execute,
            self.ri_builder.record_request,
            request=request.proto,
            request_context=request.context,
            auth_info=request.auth_info,
        )

        # decide if request should be ignored
        if _should_ignore_request(
            request.proto,
        ):
            record_post_processing_generation(request, "pre_process_filter")
            return

        if self.blob_state_validator is not None:
            try:
                self.blob_state_validator.validate(
                    blobs=request.proto.blobs,
                    request_context=request.context,
                    auth_info=request.auth_info,
                    replacement_texts=request.proto.recent_changes,
                    edit_events=request.proto.edit_events,
                    secret_logger=self.safe_logger,
                )
            except NextEditBlobStateValidationError as e:
                logger.error(f"Error validating blob state: {e}")
                raise

        # sort blocked locations by path and merge overlapping ranges
        try:
            path_to_blocked_ranges = self._get_path_to_blocked_ranges(
                request.proto.blocked_locations
            )
        except ValueError as e:
            logger.error("Invalid blocked location: %s", e)
            return

        # TODO: add documentation on how this is used. Document the flow.
        state = NextEditRequestState(
            request=request,
            response_ri=self.ri_builder.record_response(
                request.context, request.auth_info
            ),
            executor=executor,
            missing_blobs=set(),
            checkpoint_not_found=False,
            reported_blobs=set(),
            total_threads_semaphore=Semaphore(1),
            retrieval_semaphore=Semaphore(2),
            generation_semaphore=Semaphore(1),
            description_semaphore=Semaphore(1),
            suggestion_count=0,
            change_count=0,
            change_tracker=RecentChangeTracker(),
            responseQueue=Queue[NextEditResult | None](),
            path_to_blocked_ranges=path_to_blocked_ranges,
        )

        # reconstruct the file the user was actively editing when the request was sent
        active_file = self._get_active_file(state)

        # reconstruct the current version of all files that have changed since the last indexed repo state
        reconstruced_files: list[File] = []
        # TODO: pass to function only what we need (remove logger)
        for file_or_error in get_current_version_of_outdated_files(
            request,
            state.missing_blobs,
            active_file,
            self.safe_logger,
            self.file_retriever,
        ):
            if isinstance(file_or_error, FileReplacementError):
                logger.error(
                    f"Reconstrution error: {file_or_error.error_type=}, blob_name={file_or_error.file_blob_name=}, path="
                )
                self.safe_logger.secret_error(f"{file_or_error.file_path=}")
                # return an empty response if we see a file reconstruction error
                return

            reconstruced_files.append(file_or_error)

        # add all reconstructed files to the change tracker
        state.change_tracker.add_changed_files(reconstruced_files)

        # NOTE: We have seen cases where a path has a newer blob name in content
        # manager than in the request. To catch newer blobs in the content manager,
        # we will also ask content manager for each path's latest blob name as specified
        # by the edit events in the request.
        # NOTE: This is a temporary workaround. We will revisit this after updating
        # open file manager and depreacating replacement texts.
        current_blob_names_from_edit_events = get_files_for_edit_events(
            from_proto(request.proto.edit_events),
            self.file_retriever,
            request.auth_info,
            request.context,
            state.missing_blobs,
        )
        state.change_tracker.add_changed_files(current_blob_names_from_edit_events)

        # As a temporary workaround, we override the current file since the recency
        # chunk based reconstrunction logic occasionally fails to reconstruct correctly.
        # MUST be added after the reconstruced_files to the change tracker to override the recent change reconstruction
        state.change_tracker.add_changed_files([active_file])

        # convert the edit events to a list of Modified[File]
        # modifies the change tracker with the indexed files touched by edit events
        squashable_edits = self._get_squashable_edits(
            from_proto(request.proto.edit_events, list[GranularEditEvent]),
            state.change_tracker,
            state.missing_blobs,
            request,
        )

        # These are for prompt formatting only
        recently_edited_files = squashable_edits.convert_edit_events_to_modified_files(
            self.safe_logger,
            group_sizes=[1],
        )

        if (
            state.request.proto.mode != next_edit_pb2.NextEditMode.FORCED
            and self.config.ignore_requests_with_no_file_changes
            and not recently_edited_files
        ):
            logger.warning(
                "No recently edited files, skipping request: %s",
                request.context.request_id,
            )
            record_diff_events_problems(request, "no_file_changes")
            return

        prompt_input = LocalizationNextEditPromptInput(
            current_file=active_file,
            edit_region=CharRange(
                state.request.proto.selection_begin_char,
                state.request.proto.selection_end_char,
            ),
            instruction=state.request.proto.instruction,
            # TODO: It is confusing that `prompt_input.recent_changes` reflects
            # `request.edit_events` instead of `request.recent_changes`.
            # Consider refactoring the code to improve readability.
            recent_changes=tuple(recently_edited_files),
        )

        # Dispatch suggestion generation to a separate thread
        # This thread will return the results of the processing in other threads
        suggestion_generation_future = executor.submit(
            self.generate_suggestions,
            state,
            prompt_input,
            squashable_edits,
        )

        for _ in safeTrue(100):
            # Check if the request should be cancelled.
            self._cancel_request_if_needed(
                state.request, "Cancelled while yielding results in next_edit_stream."
            )
            try:
                result = state.responseQueue.get(
                    block=True, timeout=RESULT_TIMEOUT_SECONDS
                )
            except Empty:
                logger.warning(
                    "Timeout waiting for result from suggestion generation thread."
                )
                record_suggestions_queue_timeout(state.request)
                break
            # None signals work is finished.
            if result is None:
                suggestion_generation_future.result(
                    timeout=RESULT_TIMEOUT_SECONDS
                )  # raise exception if any
                break
            yield result

    # TODO: document the function
    # TODO: Send only what we need
    # TODO: rename function to reflect what it's doing
    # TODO: document assumptions
    def _get_active_file(self, state: NextEditRequestState) -> File:
        """Reconstruct the file the user was actively editing when the request was sent.
        We assume `prefix + selected_text + suffix` includes all the content of the file.
        This mechanism has been more reliable than relying on replacement text.
        """
        proto = state.request.proto

        # Currently require prefix = selected_text + suffix to be the full current file.
        if proto.selection_begin_char != len(proto.prefix):
            logger.warning(
                "Prefix does not start at beginning of file: prefix len=%d, selection begin char=%d",
                len(proto.prefix),
                proto.selection_begin_char,
            )
        if proto.selection_end_char != len(proto.prefix) + len(proto.selected_text):
            logger.warning(
                "Suffix does not begin at end of selection: suffix begin=%d, selection end char=%d",
                len(proto.prefix) + len(proto.selected_text),
                proto.selection_end_char,
            )
        # Can't confirm if suffix reached EOF.

        # reconstruct file from request
        active_file_content = proto.prefix + proto.selected_text + proto.suffix

        active_file = File(proto.path, active_file_content)

        return active_file

    def _get_path_to_blocked_ranges(
        self, blocked_locations: Sequence[next_edit_pb2.FileRegion]
    ) -> dict[str, set[CharRange]]:
        """Transform the blocked locations into a form useful for filtering."""
        path_to_blocked_ranges = dict[str, set[CharRange]]()
        for blocked_location in blocked_locations:
            try:
                next_blocked_range = CharRange(
                    blocked_location.char_start, blocked_location.char_end
                )
            except ValueError:
                # if start > end, the range is invalid and initilizing will raise a ValueError
                self.safe_logger.secret_error(
                    f"Invalid blocked location: {blocked_location=}"
                )
                raise

            path_to_blocked_ranges.setdefault(blocked_location.path, set()).add(
                next_blocked_range
            )

        for path, blocked_ranges in path_to_blocked_ranges.items():
            merged_blocked_ranges = set(CharRange.merge_touching(blocked_ranges))
            path_to_blocked_ranges[path] = merged_blocked_ranges

        return path_to_blocked_ranges

    def _get_squashable_edits(
        self,
        edit_events: list[GranularEditEvent],
        change_tracker: RecentChangeTracker,
        missing_blobs: set[BlobName],
        request: NextEditRequest,
    ) -> SquashableEdits:
        """
        Retrieve and process recently edited files into squashable edits.

        This method converts edit events into `_SquashableEdits`, which is then ready to be
        converted as a sequence of changed files representing the recent changes made
        to the files.

        Args:
            edit_events: List of GranularEditEvent objects representing the edit events.
            change_tracker: RecentChangeTracker object to manage recent changes.
            missing_blobs: Set of BlobName objects representing missing blobs.
            request: NextEditRequest object containing the request details.

        Returns:
            An _SquashableEdits object ready to be converted as a sequence of modified files
            representing the recently edited files with their before and after states.
        """
        with tracer.start_as_current_span(
            "convert_edit_events_to_recently_changed_files"
        ), record_context_processing_duration_context(request):
            # events are sorted from oldest to newest
            # this will map the path to the newest blob name for that path
            path_to_after_blob_name: dict[FilePath, BlobName] = {
                event.path: event.after_blob_name for event in edit_events
            }

            # the file retriever wants a mapping of blob name to path
            blob_name_to_path: dict[BlobName, FilePath] = {
                blob_name: path for path, blob_name in path_to_after_blob_name.items()
            }

            # add the indexed blobs to the change tracker
            change_tracker.add_indexed_files(
                self.file_retriever.retrieve_indexed_files(
                    blob_name_to_file_path=blob_name_to_path,
                    missing_blobs=missing_blobs,
                    request_context=request.context,
                    auth_info=request.auth_info,
                    expected=False,  # we don't expect all the edit event blobs to be indexed
                ).values()
            )

            # map the path to the current content of the file using the change tracker
            path_to_current_content: dict[FilePath, str] = {
                path: file.contents
                for path, blob_name in path_to_after_blob_name.items()
                if (file := change_tracker.get_current_file(blob_name, path))
            }

            return SquashableEdits(
                edit_events=edit_events,
                path_to_current_content=path_to_current_content,
            )

    def _cancel_request_if_needed(
        self,
        request: NextEditRequest,
        msg: str,
    ) -> NextEditCancelledException | None:
        """If a new request with a smaller timestamp is received, the request should be cancelled."""
        if (
            request.proto.client_created_at.ToDatetime().timestamp()
            < self.session_id_to_timestamp.get(request.context.request_session_id, 0)
        ):
            # Forced requests are not cancelled.
            # Better to wait for the result than to cancel, since the user is waiting for it.
            if request.proto.mode == next_edit_pb2.NextEditMode.FORCED:
                logger.info("Not canceling forced request")
                return
            raise NextEditCancelledException(msg, CancellationReason.STALE_REQUEST)

    @override
    def find_missing(
        self,
        request: next_edit_pb2.FindMissingRequest,
        request_context: RequestContext,
        executor: concurrent.futures.Executor,
    ) -> next_edit_pb2.FindMissingResponse:
        retrievers = []
        if self.generation_retriever is not None:
            retrievers.append(self.generation_retriever)

        if self.location_retriever is not None:
            retrievers.append(self.location_retriever)
        result = multi_retriever_find_missing(
            retrievers,
            request.blob_names,
            request_context,
            executor,
        )

        all_missing_blobs = list(result.missing_blob_names)
        if not all_missing_blobs:
            return next_edit_pb2.FindMissingResponse(
                missing_blob_names=[], nonindexed_blob_names=[]
            )

        # The indexed content might be missing because the raw content is missing. Consult the
        # content manager to get the list of blobs for which this is the case.
        missing_raw_blobs = self.content_manager_client.find_missing(
            blob_names=all_missing_blobs,
            request_context=request_context,
        )

        # Partition the contents of all_missing_blobs into unknown and nonindexed.
        nonindexed_blobs = list(set(all_missing_blobs) - set(missing_raw_blobs))

        return next_edit_pb2.FindMissingResponse(
            missing_blob_names=missing_raw_blobs,
            nonindexed_blob_names=nonindexed_blobs,
        )


# End of NextEditHandler


RuleName = str
UNDO_FILTER_EVENTS = 5
"""How many events to look back for undo detection."""


def _should_filter_generation(
    prompt_input: EditGenPromptInput,
    replacement: str,
    squashable_edits: SquashableEdits,
    quality_score: float | None,
    quality_threshold: float | None,
    quality_score_type: _QualityScoreType | None,
    request: NextEditRequest,
    ri_builder: GenerationEventBuilder,
    safe_logger: SecretLogger,
) -> bool:
    """Filter generation based on rules."""
    if (
        quality_score is not None
        and quality_threshold is not None
        and quality_score_type is not None
    ):
        # Filter out low probability changes based on configured threshold.
        if quality_score_type == _QualityScoreType.PROBABILITY:
            low_quality = quality_score < quality_threshold
        elif quality_score_type == _QualityScoreType.QUALITY:
            low_quality = quality_score > quality_threshold
        else:
            raise ValueError(f"Invalid quality score type: {quality_score_type}")
        if low_quality:
            record_post_processing_generation(request, "low_prob_changed")
            ri_builder.add_post_process_result(
                request_insight_pb2.RINextEditGeneration.PostProcessResult.LOW_PROB_CHANGED
            )
            logger.warning(
                f"Filtering out low probability change {request.context.request_id}: {quality_score:.2%} < {quality_threshold:.2%}."
            )
            return True
    # Compares the generated change to the squash of last k edits for k = 1 to 5.
    # If the generation is an exact undo of any of these squashes, we filter it out.
    same_file_events = [
        event
        for event in squashable_edits.edit_events
        if event.path == prompt_input.current_file.path
    ]
    for k in range(1, 1 + min(UNDO_FILTER_EVENTS, len(same_file_events))):
        last_k_edits = same_file_events[-k:]
        file_changes = grouped_events_to_file_changes(
            [last_k_edits], squashable_edits.path_to_current_content, safe_logger
        ).changes

        if post_processing.is_exact_undo_recent_changes(
            recent_changes=file_changes,
            selection_range=prompt_input.edit_region,
            existing_code=prompt_input.selected_code,
            suggested_code=replacement,
            current_path=prompt_input.current_file.path,
        ):
            record_post_processing_generation(request, "undo_recent_changes")
            ri_builder.add_post_process_result(
                request_insight_pb2.RINextEditGeneration.PostProcessResult.UNDO_RECENT_CHANGES
            )
            logger.warning(
                f"Filtering out change that undoes recent changes: ({k=}) {request.context.request_id}"
            )
            return True

    record_post_processing_generation(request, "noop")
    return False


def _post_process_hunk(
    hunk: ScoredFileHunk,
    request: NextEditRequest,
    path_to_blocked_ranges: dict[str, set[CharRange]],
    safe_logger: SecretLogger,
) -> request_insight_pb2.RINextEditSuggestion.PostProcessResult.ValueType:
    """Returns the post-processing result based on rules."""
    if not hunk.has_change:
        # never filter unchanged hunks
        return request_insight_pb2.RINextEditSuggestion.PostProcessResult.NOOP

    if request.proto.mode == next_edit_pb2.NextEditMode.FORCED:
        # never filter hunks in forced mode
        return request_insight_pb2.RINextEditSuggestion.PostProcessResult.NOOP

    before_lines = len(hunk.existing_code.splitlines())
    after_lines = len(hunk.suggested_code.splitlines())
    if before_lines - after_lines > 5:
        record_post_processing_hunk(request, "too_many_removed_lines")
        logger.warning(
            "Filtering out hunk that removes too many lines"
            f" ({before_lines=}, {after_lines=}): {request.context.request_id}"
        )
        safe_logger.secret_warn(f"{hunk.path=}, {hunk.crange=}")
        return request_insight_pb2.RINextEditSuggestion.PostProcessResult.TOO_MANY_REMOVED_LINES
    inserted_todo = post_processing.contains_inserted_todo(
        hunk.existing_code, hunk.suggested_code
    )
    if inserted_todo:
        record_post_processing_hunk(request, "inserted_todo")
        logger.warning(
            f"Filtering out hunk that inserts todo: {request.context.request_id}"
        )
        safe_logger.secret_warn(f"{hunk.path=}, {hunk.crange=}")
        return request_insight_pb2.RINextEditSuggestion.PostProcessResult.INSERTED_TODO

    if any(
        blocked_range.touches(hunk.crange)
        for blocked_range in path_to_blocked_ranges.get(hunk.path, set())
    ):
        record_post_processing_hunk(request, "blocked_location")
        logger.warning(
            f"Filtering out hunk that overlaps blocked location: {request.context.request_id}"
        )
        safe_logger.secret_warn(f"{hunk.path=}, {hunk.crange=}")
        return (
            request_insight_pb2.RINextEditSuggestion.PostProcessResult.BLOCKED_LOCATION
        )

    current_code = (
        request.proto.prefix + request.proto.selected_text + request.proto.suffix
    )
    after_code = (
        current_code[: hunk.char_start]
        + hunk.suggested_code
        + current_code[hunk.char_end :]
    )
    if is_deleting_imports(hunk.path, current_code, after_code):
        record_post_processing_hunk(request, "deleted_imports")
        logger.warning(
            f"Filtering out hunk that deletes imports: {request.context.request_id}"
        )
        safe_logger.secret_warn(f"{hunk.path=}, {hunk.crange=}")
        return (
            request_insight_pb2.RINextEditSuggestion.PostProcessResult.DELETED_IMPORTS
        )

    record_post_processing_hunk(request, "noop")
    return request_insight_pb2.RINextEditSuggestion.PostProcessResult.NOOP


def create_next_edit_handler(
    config: Union[NextEditHandlerConfig, dict],
    edit_gen_client: InfererClient,
    edit_gen_ranker_client: InfererClient | None,
    description_gen_client: InfererClient | None,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    content_manager_client: ContentManagerClient,
    generation_retriever: Retriever[EditGenRetrievalPromptInput],
    location_retriever: Retriever[NextEditPromptInput],
    description_gen_stub_factory: inference_host_client.InferenceStubFactoryProtocol
    | None = None,
    secret_logs_enabled: bool = False,
    low_quality_filter: post_processing.LowQualityFilter | None = None,
) -> NextEditHandler:
    """Create a next edit handler."""
    if isinstance(config, dict):
        handler_config: NextEditHandlerConfig
        handler_config = NextEditHandlerConfig.schema().load(config)  # type: ignore
    else:
        handler_config = config

    edit_gen_tokenizer = create_tokenizer_by_name(
        handler_config.edit_generation_config.tokenizer_name
    )
    edit_gen_prompt_formatter = EditGenPromptFormatter(
        edit_gen_tokenizer,
        config=handler_config.edit_generation_config.prompt_formatter_config,
        # since we are using granular diffs now, we don't need additional filtering
        diff_filter=lambda path: path.suffix != ".ipynb",
    )

    edit_gen_ranker_prompt_formatter = EditGenPromptFormatter(
        edit_gen_tokenizer,
        config=handler_config.edit_generation_config.ranker_gen_prompt_formatter_config,
        # since we are using granular diffs now, we don't need additional filtering
        diff_filter=lambda path: path.suffix != ".ipynb",
    )

    if low_quality_filter:
        low_quality_filter.set_tokenizer(edit_gen_tokenizer)

    if description_gen_client is None:
        description_gen_prompt_formatter = None
        chat_prompt_formatter = None
    else:
        assert handler_config.description_generation_config is not None
        description_gen_tokenizer = create_tokenizer_by_name(
            handler_config.description_generation_config.tokenizer_name
        )
        description_gen_prompt_formatter = RavenDescribePromptFormatter(
            description_gen_tokenizer,
            config=handler_config.description_generation_config.prompt_formatter_config,
        )
        chat_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(
            handler_config.description_generation_config.chat_prompt_formatter_name,
            description_gen_tokenizer,
        )

    logger.info(
        "Creating next edit handler with config: %s",
        json.dumps(asdict(handler_config)),
    )

    next_edit_handler = NextEditHandler(
        config=handler_config,
        edit_gen_client=edit_gen_client,
        edit_prompt_formatter=edit_gen_prompt_formatter,
        edit_gen_ranker_client=edit_gen_ranker_client,
        edit_gen_ranker_prompt_formatter=edit_gen_ranker_prompt_formatter,
        description_gen_client=description_gen_client,
        description_prompt_formatter=description_gen_prompt_formatter,
        chat_prompt_formatter=chat_prompt_formatter,
        namespace=namespace,
        ri_publisher=ri_publisher,
        content_manager_client=content_manager_client,
        generation_retriever=generation_retriever,
        location_retriever=location_retriever,
        secret_logs_enabled=secret_logs_enabled,
        low_quality_filter=low_quality_filter,
    )

    return next_edit_handler


def _split_changes_into_hunks(
    change: ScoredFileHunk,
    n_context_lines: int,
) -> Iterable[ScoredFileHunk]:
    """Split a change diff into multiple hunks."""
    assert n_context_lines > 0, "context_lines must be positive"
    line_level_hunks = precise_line_diff(
        change.existing_code, change.suggested_code
    ).group_into_hunks(n_context_lines - 1)

    offset_in_after = 0
    for hunk, hunk_span_before in zip(
        line_level_hunks.spans, line_level_hunks.span_ranges_in_before
    ):
        hunk_start = offset_in_after
        hunk_stop = hunk_start + len(hunk.after)
        # fix truncation_char to be relative to the hunk's start in `suggested_code`
        if (
            change.truncation_char is not None
            and hunk_start <= change.truncation_char <= hunk_stop
        ):
            truncation_char_relative = change.truncation_char - hunk_start
        else:
            truncation_char_relative = None
        yield ScoredFileHunk(
            path=change.path,
            blob_name=change.blob_name,
            char_start=change.char_start + hunk_span_before.start,
            char_end=change.char_start + hunk_span_before.stop,
            existing_code=hunk.before,
            suggested_code=hunk.after,
            diff_spans=align_spans_to_word_boundaries(
                precise_char_diff(hunk.before, hunk.after).spans
            ),
            localization_score=change.localization_score,
            editing_score=change.editing_score,
            editing_score_threshold=change.editing_score_threshold,
            truncation_char=truncation_char_relative,
            change_description=change.change_description,
            markdown_change_description=change.markdown_change_description,
        )
        offset_in_after = hunk_stop


def _should_ignore_request(
    request: next_edit_pb2.NextEditRequest,
) -> bool:
    """Whether to ignore a request or not."""

    if is_ignored_file_path(request.path):
        logger.warning("Ignoring request for ignored file.")
        return True
    current_code = request.prefix + request.selected_text + request.suffix
    if contains_git_conflict_marker(current_code):
        logger.warning("Ignoring request with git conflict marker.")
        return True
    return False


def get_files_for_edit_events(
    edit_events: Sequence[GranularEditEvent],
    file_retriever: FileRetriever,
    auth_info: AuthInfo,
    request_context: RequestContext,
    missing_blobs: set[BlobName],
) -> list[File]:
    """
    This function retrieves the files for the edit events.
    """
    # map each path to the blob name of the newest edit event in that path
    # edit events are assumed to be ordered from oldest -> newest
    path_to_newest_blob_name: dict[FilePath, BlobName] = {
        event.path: event.after_blob_name for event in edit_events
    }

    # file retrieval needs a mapping of blob name to path
    blob_name_to_path: dict[BlobName, FilePath] = {
        blob_name: path for path, blob_name in path_to_newest_blob_name.items()
    }

    # attempt retreiving the latest version of the files mentioned by the edit events
    # we don't expect to find all the files -- some are files are likely not uploaded yet
    blobname_to_file: dict[BlobName, File | None] = (
        file_retriever.retrieve_indexed_files(
            blob_name_to_file_path=blob_name_to_path,
            auth_info=auth_info,
            missing_blobs=missing_blobs,
            request_context=request_context,
            expected=False,  # we don't expect all the edit event blobs to be indexed
        )
    )

    return [file for file in blobname_to_file.values() if file is not None]

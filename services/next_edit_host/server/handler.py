"""Defines the NextEditHandler protocol."""

import concurrent.futures
import copy
from dataclasses import dataclass
from typing import Iterator, Protocol, Sequence

from base.diff_utils.str_diff import DiffSpan, NoopSpan, StrDiff
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from services.lib.request_context.request_context import Request<PERSON>ontext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.request_insight.request_insight_pb2 import RINextEditSuggestion


@dataclass
class ScoredFileHunk:
    """Stores edit information."""

    path: str
    """Path of the file."""

    blob_name: str
    """Blob name of the file."""

    char_start: int
    """Character start of the edit (inclusive). Based on last known state of the file."""

    char_end: int
    """Character end of the edit (exclusive). Based on last known state of the file."""

    existing_code: str
    """Existing code."""

    suggested_code: str
    """Updated code."""

    localization_score: float
    """Localization score."""

    editing_score: float
    """Editing score."""

    diff_spans: Sequence[DiffSpan]
    """Character-level diff information."""

    truncation_char: int | None = None
    """The character offset in `suggested_code` where the change was truncated.

    None means that the change was not truncated.
    """

    change_description: str = ""
    """If non-empty, a natural language description of the change in suggested code."""

    markdown_change_description: str = ""
    """If non-empty, a markdown version of the natural language change description."""

    suggestion_id: str = ""
    """If non-empty, a unique identifier for the suggestion."""

    editing_score_threshold: float = 1.0
    """Default threshold for `editing_score`.

    If `editing_score` > `editing_score_threshold`, the suggestion is considered of low
    quality.
    """

    # The following field is not to be added to the proto `ScoredFileHunk`. Therefore,
    # it won't be returned to the client.
    post_process_result: RINextEditSuggestion.PostProcessResult.ValueType = (
        RINextEditSuggestion.PostProcessResult.NOOP
    )
    """The result of post-processing this suggestion."""

    @property
    def crange(self) -> CharRange:
        return CharRange(self.char_start, self.char_end)

    @property
    def has_change(self) -> bool:
        return self.existing_code != self.suggested_code

    def to_file_hunk_proto(self) -> next_edit_pb2.ScoredFileHunk:
        diff_spans = [
            next_edit_pb2.DiffSpan(
                original=next_edit_pb2.CharRange(
                    start=before_span.start,
                    stop=before_span.stop,
                ),
                updated=next_edit_pb2.CharRange(
                    start=after_span.start,
                    stop=after_span.stop,
                ),
            )
            for before_span, after_span in StrDiff(tuple(self.diff_spans)).span_ranges()
        ]

        return next_edit_pb2.ScoredFileHunk(
            path=self.path,
            blob_name=self.blob_name,
            char_start=self.char_start,
            char_end=self.char_end,
            existing_code=self.existing_code,
            suggested_code=self.suggested_code,
            localization_score=self.localization_score,
            editing_score=self.editing_score,
            editing_score_threshold=self.editing_score_threshold,
            truncation_char=self.truncation_char,
            diff_spans=diff_spans,
            change_description=self.change_description,
            markdown_change_description=self.markdown_change_description,
            suggestion_id=self.suggestion_id,
        )

    def to_no_change(self) -> "ScoredFileHunk":
        hunk = copy.copy(self)
        hunk.suggested_code = hunk.existing_code
        hunk.diff_spans = [NoopSpan(hunk.existing_code)]
        hunk.truncation_char = None
        hunk.change_description = ""
        hunk.markdown_change_description = ""
        return hunk

    def __repr__(self) -> str:
        diff_str = ", ".join(
            f"{repr(span.before)} -> {repr(span.after)}"
            for span in self.diff_spans
            if span.before != span.after
        )
        return f"ScoredFileHunk(path={self.path}:{self.char_start}:{self.char_end}, diff=[{diff_str}])"


@dataclass
class NextEditResult:
    """Class storing the result of a next edit call."""

    suggested_edit: ScoredFileHunk
    """Location and content predicted to come next."""

    unknown_blob_names: Sequence[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool = False
    """True if the blobs checkpoint id is not available."""

    def to_next_edit_response_proto(self) -> next_edit_pb2.NextEditResponse:
        return next_edit_pb2.NextEditResponse(
            suggested_edit=self.suggested_edit.to_file_hunk_proto(),
            unknown_blob_names=self.unknown_blob_names,
            checkpoint_not_found=self.checkpoint_not_found,
        )

    def __repr__(self) -> str:
        not_found_test = (
            ", checkpoint_not_found=True" if self.checkpoint_not_found else ""
        )
        return (
            f"NextEditResult(suggested_edit={self.suggested_edit}, "
            f"n_unknown_blobs={len(self.unknown_blob_names)}{not_found_test})"
        )


class NextEditHandlerProtocol(Protocol):
    """Handler for NextEdit.

    Contains the main business logic of next edits.
    """

    def next_edit_stream(
        self,
        request: NextEditRequest,
        executor: concurrent.futures.Executor,
    ) -> Iterator[NextEditResult]:
        """Find several next edits.

        Args:
            request: The next edit request.
            request_id: The request id.
            request_context: The request context for the request.
            executor: An executor pool for the handler. Exclusive per handler.

        Returns:
            An iterator of any number of next edit responses.
        """
        raise NotImplementedError()

    def find_missing(
        self,
        request: next_edit_pb2.FindMissingRequest,
        request_context: RequestContext,
        executor: concurrent.futures.Executor,
    ) -> next_edit_pb2.FindMissingResponse:
        """Probes the model to determine which raw and indexed blobs are present.

        Args:
            request: The find-missing request.

        Returns:
            A find-missing response containing two lists of blobs, the first containing names
            of unknown blobs, the second containing names of blobs that are not indexed for
            one or more retrievers used by this model.
        """
        raise NotImplementedError()

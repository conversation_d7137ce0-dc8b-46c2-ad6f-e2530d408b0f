{"learner": {"attributes": {}, "feature_names": ["has_change_prob", "plus_mean", "minus_mean", "plus_token_prob_min", "plus_token_prob_median", "plus_token_prob_max", "minus_token_prob_min", "minus_token_prob_median", "minus_token_prob_max", "num_insert_lines", "num_delete_lines", "num_lines_delta", "plus_moving_average_median", "minus_moving_average_median", "num_recent_insert_lines", "num_recent_delete_lines", "num_recent_lines_delta", "max_contiguous_insert", "max_contiguous_delete", "number_changed_lines"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.08937869, 0.28170514, -0.49796057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [185.87326, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97183436, 0.28170514, -0.49796057], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1224.0051, 641.59955, 582.4055], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.039004635, 0.17944397, -0.25673312], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [55.323936, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.91452044, 0.17944397, -0.25673312], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1161.2156, 579.737, 581.4786], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.011447889, -0.39778566, 0.09665728], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [47.445675, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.39778566, 0.09665728], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1134.0916, 247.37228, 886.71924], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0092395535, 0.20537393, -0.17071293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [39.249176, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.85721624, 0.20537393, -0.17071293], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1130.5779, 485.29837, 645.27954], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.007657373, 0.30246153, -0.08543692], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [27.007288, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [8.0, 0.30246153, -0.08543692], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1117.5917, 223.51491, 894.0768], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0050583184, 0.03597124, -0.49901912], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [22.57847, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9997967, 0.03597124, -0.49901912], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1112.1656, 1027.7273, 84.4383], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0065691844, -0.25970158, 0.059838086], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [18.561754, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.25970158, 0.059838086], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1102.2955, 228.47601, 873.8196], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040264446, 0.19238827, -0.08687028], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [17.871693, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.90932155, 0.19238827, -0.08687028], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1096.2994, 324.832, 771.46747], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026952052, -0.3030532, 0.052512314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [17.936213, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99994946, -0.3030532, 0.052512314], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1079.7065, 166.94553, 912.76105], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027946036, -0.017321195, 0.97917444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [15.513678, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [65.0, -0.017321195, 0.97917444], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1085.3708, 1070.5167, 14.854146], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013734325, 0.14368556, -0.08596202], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [13.295604, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99849814, 0.14368556, -0.08596202], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1081.55, 398.1217, 683.4283], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00239893, -0.048723117, 0.328713], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [16.53756, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [11.0, -0.048723117, 0.328713], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1076.1296, 944.8003, 131.32935], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.002780142, 0.11651324, -0.10351107], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [12.92238, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.11651324, -0.10351107], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1073.3796, 491.34003, 582.0396], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024801975, -0.06504613, 0.12960416], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [8.882141, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9771395, -0.06504613, 0.12960416], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1072.7815, 728.30457, 344.47696], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021031215, 0.20067835, -0.053187914], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [11.280707, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9862106, 0.20067835, -0.053187914], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1086.9409, 218.1328, 868.80817], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014669723, 0.023293635, -0.36789504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [9.786268, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98154986, 0.023293635, -0.36789504], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1076.6719, 1009.4002, 67.27167], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0028840094, 0.0071712607, -0.8081996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [8.69518, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [104.0, 0.0071712607, -0.8081996], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1072.0728, 1059.8307, 12.242083], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022660494, -0.22030126, 0.034582283], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [8.621653, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.22030126, 0.034582283], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1071.1663, 154.138, 917.02826], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.001790406, -0.2257333, 0.033524938], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [8.48128, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-5.0, -0.2257333, 0.033524938], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1070.4526, 145.07922, 925.37335], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016853961, -0.016009673, 0.48154172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [7.42712, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [25.0, -0.016009673, 0.48154172], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1070.8782, 1040.987, 29.891058], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005264028, -0.0055880286, 1.1613619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [6.2909594, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99958616, -0.0055880286, 1.1613619], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1067.5977, 1063.9579, 3.6398134], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025241525, 0.45474875, -0.012475016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [5.9474096, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-37.0, 0.45474875, -0.012475016], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1067.4083, 26.976322, 1040.432], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000480288, 0.009764275, -0.70657396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [7.724069, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [111.0, 0.009764275, -0.70657396], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.8475, 1051.5765, 14.270929], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016831472, 0.013014287, -0.36515617], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [5.709412, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9992388, 0.013014287, -0.36515617], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1066.8641, 1026.3276, 40.5365], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0020518047, -0.060339518, 0.075039476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [4.7839456, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9172036, -0.060339518, 0.075039476], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1062.6354, 605.2392, 457.39615], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00095148955, 0.0058360198, -0.5769692], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [4.18627, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9951018, 0.0058360198, -0.5769692], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1068.8711, 1057.401, 11.470025], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016263009, 0.026538214, -0.1336391], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [3.9764605, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, 0.026538214, -0.1336391], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1067.5402, 880.4895, 187.05069], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00083944446, -0.019684544, 0.26116773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [5.279489, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [33.0, -0.019684544, 0.26116773], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1067.2113, 996.4646, 70.746735], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00071687886, 0.11379088, -0.0328807], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [3.933736, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8774182, 0.11379088, -0.0328807], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1066.0614, 233.2216, 832.8398], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004273009, -0.0762574, 0.07342725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [5.941663, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.531096, -0.0762574, 0.07342725], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1058.9359, 522.4639, 536.472], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011249571, -0.02942297, 0.125333], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [3.8337352, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [36.0, -0.02942297, 0.125333], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1069.2914, 874.3924, 194.89903], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00064797705, 0.019462438, -0.28748655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [6.1758633, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [64.0, 0.019462438, -0.28748655], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1068.6593, 999.51483, 69.1444], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.001369861, 0.16631533, -0.0229183], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [3.869441, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7049634, 0.16631533, -0.0229183], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1068.8174, 120.94351, 947.87384], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00040443754, -0.011478252, 0.307356], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [3.6364331, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9972408, -0.011478252, 0.307356], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1064.9675, 1028.9082, 36.059395], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00055564864, 0.0047025024, -0.73547226], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [4.1255794, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99995995, 0.0047025024, -0.73547226], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.718, 1059.1338, 6.5842457], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012956066, 0.08242812, -0.03642242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [3.1408904, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99537206, 0.08242812, -0.03642242], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.9648, 314.65274, 751.31213], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005325862, -0.12923443, 0.03547921], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [4.9404306, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.27125728, -0.12923443, 0.03547921], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1063.9568, 232.04984, 831.9069], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011280957, 0.044727724, -0.067283385], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [3.2266693, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.63316053, 0.044727724, -0.067283385], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1061.6497, 627.21606, 434.43356], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006788323, -0.05676001, 0.0603356], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [3.6260333, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.88237125, -0.05676001, 0.0603356], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1057.6971, 551.16534, 506.53177], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007003842, 0.09991625, -0.038718514], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [4.07037, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9940667, 0.09991625, -0.038718514], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1062.0665, 290.8065, 771.2601], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00069476874, -0.006118913, 0.54456353], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [3.1430016, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [107.0, -0.006118913, 0.54456353], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1060.5742, 1051.1068, 9.467468], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00030645996, 0.057758808, -0.056924112], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [3.4949493, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [13.0, 0.057758808, -0.056924112], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1061.0961, 523.8411, 537.255], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00067298504, -0.11275893, 0.040106457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [4.854835, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.11275893, 0.040106457], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1060.1514, 282.34253, 777.80884], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000912926, -0.7748394, 0.0024228864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [2.7471807, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-22.0, -0.7748394, 0.0024228864], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1062.3812, 3.5668795, 1058.8143], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00097308523, -0.031997226, 0.09302905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [3.1050239, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.031997226, 0.09302905], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1062.6797, 799.48126, 263.19843], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00053265603, 0.019873157, -0.1815795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [3.930272, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [9.0, 0.019873157, -0.1815795], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1061.8668, 955.1069, 106.75998], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007030401, -0.17724435, 0.01771374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [3.46642, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9991878, -0.17724435, 0.01771374], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1064.1912, 99.7145, 964.4767], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00062808045, 0.18429396, -0.037203927], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [7.225545, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.24506053, 0.18429396, -0.037203927], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1066.2723, 175.40617, 890.86615], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007207791, -0.15973204, 0.015768629], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [2.7788165, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9991878, -0.15973204, 0.015768629], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1057.8467, 98.57524, 959.2714], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005229125, 0.06405854, -0.04685083], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [3.1762445, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [24.0, 0.06405854, -0.04685083], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1059.6028, 442.44647, 617.1563], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005878796, -0.04151427, 0.09598604], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [4.189929, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [26.0, -0.04151427, 0.09598604], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1058.0835, 743.5498, 314.5337], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007663816, 0.013258371, -0.23597364], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [3.4966915, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [103.0, 0.013258371, -0.23597364], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1058.0652, 999.4164, 58.648777], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008923936, 0.117283635, -0.025318557], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [3.05877, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.0, 0.117283635, -0.025318557], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1057.6206, 180.50745, 877.1132], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041998105, 0.023156604, -0.15386137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [3.8245418, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99999547, 0.023156604, -0.15386137], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.2123, 915.4072, 139.80511], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00092492707, -0.013259823, 0.31509465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [4.1163654, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99984056, -0.013259823, 0.31509465], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.9293, 1015.25977, 38.66964], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012512146, -0.010384236, 0.27474627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [2.674852, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [17.0, -0.010384236, 0.27474627], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1059.0262, 1026.0361, 32.990143], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-5.153335e-05, -0.0314475, 0.08507249], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [2.8292367, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7216811, -0.0314475, 0.08507249], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1056.6268, 772.3825, 284.24432], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00060134626, 0.02510569, -0.13317262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [3.621588, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9946602, 0.02510569, -0.13317262], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1060.6868, 889.0926, 171.59425], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011053596, -0.02032666, 0.15820894], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [3.2358043, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9663978, -0.02032666, 0.15820894], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.6328, 941.86865, 112.764206], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00084765477, 0.048943665, -0.049567547], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [2.5754104, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, 0.048943665, -0.049567547], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1059.6625, 524.0664, 535.59607], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00050160446, -0.00850074, 0.28248808], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [2.3944387, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, -0.00850074, 0.28248808], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.7064, 1027.6289, 28.07754], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025843192, 0.0073200506, -0.3112347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [2.4930024, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99949276, 0.0073200506, -0.3112347], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.8567, 1031.6909, 24.165815], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010873183, -0.15767291, 0.013894743], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [2.4764726, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99768335, -0.15767291, 0.013894743], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.6923, 91.181465, 962.51074], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00046313254, -0.016782338, 0.12463785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [2.1586175, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [85.0, -0.016782338, 0.12463785], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.3186, 934.30585, 121.012726], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00032589352, 0.042118583, -0.080397256], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [3.593257, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [51.0, 0.042118583, -0.080397256], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.2833, 690.0, 365.28336], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007381699, -0.05306255, 0.05733764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [3.210976, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.05306255, 0.05733764], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.6647, 554.84973, 499.8149], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006172656, 0.023604626, -0.13569726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [3.4575942, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [21.0, 0.023604626, -0.13569726], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.7776, 895.09814, 159.67943], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00075516745, -0.020890545, 0.17096244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [3.6540868, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [57.0, -0.020890545, 0.17096244], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.7948, 944.87805, 109.916794], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00056977826, 0.02279854, -0.13091011], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [3.2178104, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [49.0, 0.02279854, -0.13091011], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.4827, 894.8694, 159.6133], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000706035, -0.03510556, 0.06612441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [2.4289205, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [25.0, -0.03510556, 0.06612441], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.5317, 696.4987, 358.03296], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00045122567, 0.073682144, -0.0362178], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [2.8025315, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, 0.073682144, -0.0362178], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.9556, 342.98685, 711.96875], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004868731, -0.0073706913, 0.27176997], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [1.9784998, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95770746, -0.0073706913, 0.27176997], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.6003, 1028.5667, 25.03364], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00038803602, 0.0065258723, -0.35897204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [2.6216571, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9678145, 0.0065258723, -0.35897204], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.5089, 1036.5056, 19.00321], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010847475, -0.017230673, 0.11075649], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [1.9069899, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9925511, -0.017230673, 0.11075649], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.9908, 921.76624, 132.22456], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000359166, 0.13019572, -0.015031131], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [2.0253005, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7454473, 0.13019572, -0.015031131], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.3024, 105.81951, 949.48285], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00014440365, -0.067733064, 0.031803403], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [2.277037, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [14.0, -0.067733064, 0.031803403], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1052.5232, 337.4645, 715.0587], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004401072, -0.03456469, 0.053145446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [1.9316324, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99916875, -0.03456469, 0.053145446], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.3494, 644.36017, 409.98923], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004004172, 0.026006179, -0.093305685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [2.603632, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9933086, 0.026006179, -0.093305685], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1059.2832, 825.399, 233.88426], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00065351906, 0.13621935, -0.017845722], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [2.4906685, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.13621935, -0.017845722], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1056.4089, 117.112656, 939.29626], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00030664986, -0.15226665, 0.011264594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [1.8584131, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-18.0, -0.15226665, 0.011264594], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.9221, 73.78446, 981.13763], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004658248, 0.4591252, -0.0051448755], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [2.2735581, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-73.0, 0.4591252, -0.0051448755], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.1494, 9.655267, 1045.4941], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00033426125, 0.021309514, -0.082636], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [1.8364816, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99133587, 0.021309514, -0.082636], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.242, 841.30054, 211.94138], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005460066, -0.036387853, 0.0838841], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [3.180094, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7489654, -0.036387853, 0.0838841], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1048.8693, 736.69867, 312.17065], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007181401, 0.061819423, -0.03805199], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [2.4686902, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9294159, 0.061819423, -0.03805199], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.3524, 394.2658, 661.0866], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00038104574, -0.047416724, 0.047602575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [2.370639, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9996186, -0.047416724, 0.047602575], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1048.3765, 529.42365, 518.95276], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004571764, -0.55266637, 0.002712757], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [1.8434435, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-86.0, -0.55266637, 0.002712757], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1051.2592, 5.010857, 1046.2483], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00057577144, -0.13078728, 0.011649739], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [1.6780208, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.5354357, -0.13078728, 0.011649739], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1052.1407, 89.47389, 962.6668], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00031970473, -0.014354502, 0.11658645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [1.7324805, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [57.0, -0.014354502, 0.11658645], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.885, 941.7084, 112.1766], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000232633, 0.027099844, -0.07879221], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [2.2665906, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [34.0, 0.027099844, -0.07879221], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.5948, 782.1307, 271.46417], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004943298, -0.033476464, 0.06675658], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [2.340495, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [26.0, -0.033476464, 0.06675658], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.1829, 706.9652, 346.21762], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00043310956, 0.04094509, -0.04143505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [1.7910521, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [13.0, 0.04094509, -0.04143505], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.6798, 524.4343, 529.2455], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000347316, -0.020871472, 0.1010692], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [2.1956732, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [39.0, -0.020871472, 0.1010692], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1052.8439, 876.2975, 176.5464], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00033667026, 0.006127974, -0.2912024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [1.9823828, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [120.0, 0.006127974, -0.2912024], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1052.3174, 1030.3953, 21.922146], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007208989, -0.005296569, 0.36319035], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [1.7547994, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [101.0, -0.005296569, 0.36319035], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1051.691, 1039.605, 12.086118], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-8.937353e-05, 0.0038862175, -0.48095998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [2.0147629, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [107.0, 0.0038862175, -0.48095998], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1051.9092, 1044.2677, 7.6415496], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00090400747, 0.01891129, -0.07604207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [1.5686796, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9909004, 0.01891129, -0.07604207], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1051.6289, 832.7624, 218.86655], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "2.7336732E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "20", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 0]}
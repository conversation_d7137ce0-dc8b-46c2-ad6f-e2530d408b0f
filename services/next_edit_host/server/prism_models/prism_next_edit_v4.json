{"learner": {"attributes": {}, "feature_names": ["has_change_prob", "plus_mean", "minus_mean", "plus_token_prob_min", "plus_token_prob_median", "plus_token_prob_max", "minus_token_prob_min", "minus_token_prob_median", "minus_token_prob_max", "num_insert_lines", "num_delete_lines", "num_lines_delta", "plus_moving_average_median", "minus_moving_average_median", "num_recent_insert_lines", "num_recent_delete_lines", "num_recent_lines_delta", "max_contiguous_insert", "max_contiguous_delete", "number_changed_lines"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.010088369, -1.0232081, 0.21449313], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [454.81738, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -1.0232081, 0.21449313], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [2151.343, 354.62994, 1796.7131], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0118328445, 0.2623521, -0.56039417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [292.62253, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9996615, 0.2623521, -0.56039417], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [2039.2343, 1418.6814, 620.5529], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0162929, 0.37531862, -0.07247188], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [64.05755, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.891484, 0.37531862, -0.07247188], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [2008.1835, 397.43896, 1610.7445], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.005145698, 0.05461148, -0.4277402], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [42.429485, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9926838, 0.05461148, -0.4277402], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1979.388, 1777.1833, 202.20456], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011608405, 0.11453229, -0.1900815], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [42.63543, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, 0.11453229, -0.1900815], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1964.4456, 1233.5681, 730.8774], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0017726932, 0.122427545, -0.13787791], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [32.64938, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.66225183, 0.122427545, -0.13787791], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1935.7015, 1038.5457, 897.1558], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0016891264, -0.5923731, 0.040352162], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [44.032482, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9991878, -0.5923731, 0.040352162], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1915.0618, 116.14581, 1798.9159], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.005085644, -0.14187866, 0.073614955], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [19.502224, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.14187866, 0.073614955], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1934.3666, 614.80914, 1319.5574], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00094066095, 0.23929593, -0.045585655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [21.38831, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9863008, 0.23929593, -0.045585655], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1926.6646, 313.98248, 1612.682], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0018306897, -0.09028097, 0.17633232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [30.801908, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99967295, -0.09028097, 0.17633232], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.3094, 1253.2546, 661.0548], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00089749234, 0.96788543, -0.007909783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [16.60888, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-72.0, 0.96788543, -0.007909783], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1948.2943, 16.601934, 1931.6924], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0017821664, 0.07481492, -0.098820575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [14.313009, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.66095966, 0.07481492, -0.098820575], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1946.0587, 1127.6763, 818.38245], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007918271, -0.018625734, 0.29072177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [10.975899, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99785084, -0.018625734, 0.29072177], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1947.6687, 1826.2916, 121.377], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00072039396, 0.078410655, -0.06606774], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [10.113176, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.078410655, -0.06606774], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1947.0457, 899.9818, 1047.0638], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00056374865, -0.03070711, 0.22816658], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [13.848963, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [12.0, -0.03070711, 0.22816658], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1943.8226, 1709.7776, 234.04506], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007834918, 0.0111941, -0.47201228], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [9.580067, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [84.0, 0.0111941, -0.47201228], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1944.2672, 1903.3335, 40.933655], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00010414263, -0.028704021, 0.20100217], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [11.250177, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [84.0, -0.028704021, 0.20100217], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1941.8771, 1699.0898, 242.78719], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007793662, 0.0990709, -0.06880902], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [13.280048, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [24.0, 0.0990709, -0.06880902], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1939.5453, 803.79083, 1135.7545], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006950858, -0.06850845, 0.08184833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [10.927115, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [15.0, -0.06850845, 0.08184833], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1943.681, 1049.1624, 894.51874], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006138344, 0.034940124, -0.1926011], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [12.871611, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [20.0, 0.034940124, -0.1926011], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1938.7162, 1646.942, 291.77423], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007530569, -0.017685246, 0.309582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [11.064812, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [73.0, -0.017685246, 0.309582], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1941.183, 1832.7059, 108.47703], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000984629, 0.01641313, -0.40114102], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [12.032359, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [112.0, 0.01641313, -0.40114102], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1937.3292, 1866.6693, 70.659996], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005205266, -0.14583868, 0.034249656], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [9.57211, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.250872, -0.14583868, 0.034249656], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1937.0092, 362.16403, 1574.8451], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00022398686, 0.057110395, -0.10465151], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [11.4635515, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9866058, 0.057110395, -0.10465151], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1912.6505, 1235.0292, 677.6214], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00044997974, -0.18413143, 0.025992693], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [8.966902, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99995756, -0.18413143, 0.025992693], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1899.8838, 230.19537, 1669.6885], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00044864128, 0.18153827, -0.029252687], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [10.232426, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9180302, 0.18153827, -0.029252687], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1900.4447, 267.0603, 1633.3844], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009810744, -0.15914463, 0.02173906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [6.293478, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.15914463, 0.02173906], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1891.3684, 216.28601, 1675.0824], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [7.5394535e-05, 0.08300223, -0.054778345], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [8.58556, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [11.0, 0.08300223, -0.054778345], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1885.4148, 750.4244, 1134.9905], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00044810143, -0.0057168575, 0.49137852], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [5.723359, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [65.0, -0.0057168575, 0.49137852], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1889.1127, 1866.66, 22.45261], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007997244, 0.0066956384, -0.50237817], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [5.604585, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [110.0, 0.0066956384, -0.50237817], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1887.0349, 1866.1552, 20.87971], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000119038516, -0.0047042216, 0.79648846], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [7.2561364, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [147.0, -0.0047042216, 0.79648846], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1887.1039, 1876.7314, 10.372447], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007083834, -0.019918503, 0.14990635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [5.813977, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9823356, -0.019918503, 0.14990635], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1887.2244, 1658.7638, 228.46057], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00017685197, 0.0045325765, -0.8688795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [7.1716933, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99944323, 0.0045325765, -0.8688795], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1892.5414, 1884.0931, 8.448321], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007712456, 0.06556147, -0.045765873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [5.6440735, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95257735, 0.06556147, -0.045765873], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1889.0499, 763.3033, 1125.7466], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00029147207, -0.015210748, 0.23378378], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [6.862265, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99632096, -0.015210748, 0.23378378], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1893.8547, 1776.8213, 117.03337], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0001917924, -0.113360405, 0.029863948], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [6.40301, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999969, -0.113360405, 0.029863948], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1898.3712, 392.70612, 1505.6652], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00028883936, 0.008438824, -0.4249442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [6.582873, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99541366, 0.008438824, -0.4249442], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1897.4321, 1862.7117, 34.72044], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002651965, 0.12687914, -0.024761619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [5.902875, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.0, 0.12687914, -0.024761619], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1893.23, 305.16187, 1588.0681], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00061099825, -0.1399384, 0.018715924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [4.8003616, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-7.0, -0.1399384, 0.018715924], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1884.4336, 214.27527, 1670.1583], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00020419239, 0.090686664, -0.053791974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [9.215467, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.090686664, -0.053791974], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1884.211, 703.9344, 1180.2767], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00051065284, -0.1192746, 0.034083318], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [7.5868373, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.1192746, 0.034083318], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1884.5532, 412.00204, 1472.5513], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00021332577, 0.025012579, -0.11551017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [5.392939, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.025012579, -0.11551017], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1877.1602, 1546.5272, 330.633], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00034096744, -0.2063705, 0.01360384], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [5.16185, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.2063705, 0.01360384], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1880.7719, 112.519165, 1768.2527], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00052596966, 0.09610707, -0.0342932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [6.2876463, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9960102, 0.09610707, -0.0342932], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1887.2955, 503.47113, 1383.8243], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0004935031, -0.034462553, 0.08309467], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [5.433083, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999757, -0.034462553, 0.08309467], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1879.6537, 1321.1416, 558.51215], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00031376383, 0.038049083, -0.063640885], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [4.5410466, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9271551, 0.038049083, -0.063640885], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1879.6335, 1182.3901, 697.24347], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000216406, -0.042808842, 0.06703787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [5.4020624, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9995605, -0.042808842, 0.06703787], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1876.9733, 1142.0106, 734.9626], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00023049893, 0.027473887, -0.09353068], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [4.827711, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99506, 0.027473887, -0.09353068], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1887.9734, 1463.4563, 424.5171], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [9.744071e-05, 0.3110824, -0.0075839143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [4.4961777, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-37.0, 0.3110824, -0.0075839143], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1880.2142, 44.3699, 1835.8444], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0004927675, -0.24678756, 0.017691292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [7.9994316, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-18.0, -0.24678756, 0.017691292], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1878.9281, 121.31481, 1757.6133], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00032714626, -0.017831054, 0.14492825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [4.9379654, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [57.0, -0.017831054, 0.14492825], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1878.6472, 1669.8353, 208.81192], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00040074694, 0.042231344, -0.08068141], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [6.3707266, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [51.0, 0.042231344, -0.08068141], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1876.3154, 1238.0697, 638.2457], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0003482546, -0.05361357, 0.063990906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [6.458106, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [15.0, -0.05361357, 0.063990906], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1878.4877, 1016.6447, 861.843], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0003618386, 0.03712806, -0.07159605], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [4.9648404, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [22.0, 0.03712806, -0.07159605], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1874.6217, 1241.0187, 633.6031], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00027025162, -0.02193688, 0.13834284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [5.7603836, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [42.0, -0.02193688, 0.13834284], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1876.6843, 1617.3899, 259.29443], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00037070617, 0.05304775, -0.04423155], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [4.413176, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.05304775, -0.04423155], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1876.3362, 860.20746, 1016.1287], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00024606625, 0.0055215023, -0.37017184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [3.6658335, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [105.0, 0.0055215023, -0.37017184], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1873.9089, 1848.567, 25.341919], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [6.771501e-05, 0.0028101364, -0.6805633], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [3.5024192, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [122.0, 0.0028101364, -0.6805633], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1874.3615, 1867.8313, 6.530058], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003054167, -0.023840705, 0.07595898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [3.3662517, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9532982, -0.023840705, 0.07595898], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1873.44, 1432.1606, 441.2792], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00015976427, -0.004678754, 0.38538444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [3.502027, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [21.0, -0.004678754, 0.38538444], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1876.8864, 1854.5802, 22.306137], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00040926758, 0.0038351116, -0.6109939], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [3.9349995, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [37.0, 0.0038351116, -0.6109939], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1876.5468, 1867.0787, 9.467979], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [2.0034393e-05, -0.009644221, 0.1693007], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [3.0739982, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [105.0, -0.009644221, 0.1693007], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1877.0063, 1776.5272, 100.47914], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00022687629, 0.01379836, -0.16956344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [4.3307853, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [58.0, 0.01379836, -0.16956344], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1877.4159, 1739.3102, 138.1058], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00013346563, -0.016853673, 0.109186195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [3.4803188, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [81.0, -0.016853673, 0.109186195], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1876.7274, 1624.5212, 252.20615], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0002704085, 0.024647947, -0.06787156], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [3.1182182, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [28.0, 0.024647947, -0.06787156], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1875.1558, 1381.5498, 493.60605], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0001552627, -0.057011735, 0.035755392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [3.821723, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [9.0, -0.057011735, 0.035755392], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1875.8541, 719.64325, 1156.2108], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00017860201, 0.05475188, -0.034472704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [3.5455022, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [22.0, 0.05475188, -0.034472704], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1872.902, 727.1359, 1145.7661], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00019189024, -0.038244434, 0.045923438], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [3.2981887, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [15.0, -0.038244434, 0.045923438], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1874.3676, 1018.5028, 855.86475], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00018664164, -0.0024439397, 0.6109645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [3.0099912, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999832, -0.0024439397, 0.6109645], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1871.4694, 1864.4353, 7.034005], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00027227984, 0.004411382, -0.37443158], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [2.9074366, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99991846, 0.004411382, -0.37443158], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1872.5667, 1853.0851, 19.481562], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-9.228876e-05, -0.005057961, 0.43467912], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [4.0454197, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99942625, -0.005057961, 0.43467912], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1871.787, 1851.6273, 20.1597], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00039369738, 0.015192778, -0.10147349], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [2.8252769, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [23.0, 0.015192778, -0.10147349], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1872.0739, 1635.3448, 236.72908], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000113679256, -0.013950133, 0.20014384], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [5.272308, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [33.0, -0.013950133, 0.20014384], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1872.1483, 1750.0363, 122.11208], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00042367494, 0.0060214843, -0.27431673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [2.8807642, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [54.0, 0.0060214843, -0.27431673], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1871.0522, 1834.6495, 36.402725], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00010637432, -0.0025810988, 0.7509836], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [3.781857, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [99.0, -0.0025810988, 0.7509836], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1872.1362, 1866.4526, 5.683679], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00066403777, 0.038010057, -0.03757831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [2.6760607, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [10.0, 0.038010057, -0.03757831], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1871.7302, 946.96576, 924.7645], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00014356068, -0.038121678, 0.05389108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [3.8540218, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [15.0, -0.038121678, 0.05389108], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1871.9222, 1093.6179, 778.3043], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00022044149, 0.029981019, -0.05716136], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [3.1976078, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [51.0, 0.029981019, -0.05716136], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1870.4438, 1231.9705, 638.4733], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00016722655, 0.0125689935, -0.1257257], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [2.9253254, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9814815, 0.0125689935, -0.1257257], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1871.6414, 1704.6188, 167.02257], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-2.4302894e-05, -0.009249357, 0.2211852], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [3.814872, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.96451217, -0.009249357, 0.2211852], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1867.4193, 1793.5802, 73.8391], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00011044603, -0.024991525, 0.07086823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [3.3264866, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.024991525, 0.07086823], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1870.8557, 1381.4282, 489.4275], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00024171722, 0.04282753, -0.031004187], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [2.4876144, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, 0.04282753, -0.031004187], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1867.5002, 790.17737, 1077.3229], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00014624925, -0.13759525, 0.018277531], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [4.663218, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999485, -0.13759525, 0.018277531], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1865.1997, 216.19553, 1649.0042], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00020769864, 0.13819565, -0.013253247], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [3.4690788, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.77384806, 0.13819565, -0.013253247], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1865.6724, 164.99942, 1700.673], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0004153273, -0.15734328, 0.010757137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [3.0390027, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.15734328, 0.010757137], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1860.6562, 113.5961, 1747.0602], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00027685732, 0.098352194, -0.013627263], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [2.5463705, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.24315347, 0.098352194, -0.013627263], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1865.3352, 230.85838, 1634.4768], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00030978638, 0.9074862, -0.0011348751], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [2.4393837, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-40.0, 0.9074862, -0.0011348751], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1859.5396, 1.9594128, 1857.5802], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000573674, 0.0052568, -0.2617423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [2.2869427, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99984646, 0.0052568, -0.2617423], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1859.5157, 1827.8629, 31.652868], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-6.825644e-05, -0.008990061, 0.14979078], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [2.4874249, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999995, -0.008990061, 0.14979078], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1858.4272, 1754.8905, 103.53677], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0001859811, 0.012335282, -0.10904865], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [2.469684, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99999833, 0.012335282, -0.10904865], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1858.9152, 1673.6549, 185.26027], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [7.377011e-05, -0.035091706, 0.033015393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [2.155211, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99909633, -0.035091706, 0.033015393], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1858.494, 898.87335, 959.62067], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000117035655, 0.045135424, -0.033702407], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [2.840974, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9987381, 0.045135424, -0.033702407], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1863.9977, 799.4645, 1064.5332], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00017586048, -0.035630338, 0.033699207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [2.232808, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99909633, -0.035630338, 0.033699207], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1858.1403, 898.44775, 959.6925], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00012088313, 0.009760342, -0.13035375], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [2.346505, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9998619, 0.009760342, -0.13035375], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1863.6924, 1736.337, 127.35529], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-9.6455195e-05, -0.011175426, 0.19840832], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [4.092003, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99996996, -0.011175426, 0.19840832], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1858.6455, 1761.288, 97.3575], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [6.5109874e-05, 0.0138565255, -0.101468004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [2.6122327, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99993515, 0.0138565255, -0.101468004], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1863.4961, 1641.4048, 222.09128], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "6.198561E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "20", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 0]}
{"learner": {"attributes": {}, "feature_names": ["has_change_prob", "plus_mean", "minus_mean", "plus_token_prob_min", "plus_token_prob_median", "plus_token_prob_max", "minus_token_prob_min", "minus_token_prob_median", "minus_token_prob_max", "num_insert_lines", "num_delete_lines", "num_lines_delta", "plus_moving_average_median", "minus_moving_average_median", "num_recent_insert_lines", "num_recent_delete_lines", "num_recent_lines_delta", "max_contiguous_insert", "max_contiguous_delete", "number_changed_lines"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.17915645, 0.5447631, -0.22925593], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [194.30338, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95116854, 0.5447631, -0.22925593], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1299.4333, 685.472, 613.9614], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0539854, -0.56752145, 0.168751], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [84.00428, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.56752145, 0.168751], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1175.382, 182.59634, 992.7856], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.021933472, 0.20122953, -0.4969254], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [106.76107, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6753136, 0.20122953, -0.4969254], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1145.5315, 851.7978, 293.7337], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.026252352, 0.09368337, -0.5753537], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [45.98861, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999161, 0.09368337, -0.5753537], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1131.3197, 1018.0553, 113.264465], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0078015653, 0.20829667, -0.07674108], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [18.65682, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.43346792, 0.20829667, -0.07674108], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1098.7294, 325.45068, 773.2787], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0054491297, -0.30069256, 0.06634611], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [20.209642, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99999034, -0.30069256, 0.06634611], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1081.9578, 178.85898, 903.09875], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0056539625, 0.015049725, -1.3747398], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [14.152707, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [112.0, 0.015049725, -1.3747398], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1088.6052, 1082.228, 6.3771753], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011545668, 0.02787734, -0.4053712], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [11.789211, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9961391, 0.02787734, -0.4053712], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1083.1732, 1017.2371, 65.936165], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0019401086, -0.015276196, 0.87246853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [16.19866, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [15.0, -0.015276196, 0.87246853], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1078.9377, 1058.9769, 19.96077], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0047672936, 0.08250462, -0.13722305], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [11.923717, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, 0.08250462, -0.13722305], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1078.2217, 697.029, 381.1927], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0028502233, 0.14559312, -0.069262415], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [11.099052, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9961318, 0.14559312, -0.069262415], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1076.2717, 360.89044, 715.3813], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.003089386, -0.027719304, 0.33172053], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [10.806086, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [60.0, -0.027719304, 0.33172053], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.3917, 974.9109, 90.48081], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0025490872, 0.043165524, -0.20830055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [9.145857, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [18.0, 0.043165524, -0.20830055], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.8971, 894.40173, 171.49535], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0020429066, -0.026016632, 0.40158734], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [11.956809, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [65.0, -0.026016632, 0.40158734], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1064.5897, 995.60443, 68.98525], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0030097184, -0.10826941, 0.0781649], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [8.920449, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95386106, -0.10826941, 0.0781649], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1064.6188, 428.9903, 635.62854], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0023090232, 0.12063241, -0.0799838], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [10.515521, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.526963, 0.12063241, -0.0799838], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1077.9457, 441.98245, 635.96313], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0027825094, 0.022975275, -0.36643168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [7.9632163, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, 0.022975275, -0.36643168], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.9767, 1011.58936, 54.387264], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011451821, -0.04792304, 0.14146516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [7.322834, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.73304594, -0.04792304, 0.14146516], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1061.5688, 787.0169, 274.55197], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0014574934, 0.4776937, -0.014411244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [8.097108, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-6.0, 0.4776937, -0.014411244], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1069.5225, 33.55005, 1035.9724], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0024480738, 0.11300926, -0.07942323], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [9.690643, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.82173663, 0.11300926, -0.07942323], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1068.5863, 454.4732, 614.1131], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.002421955, -0.099892706, 0.071950704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [7.577539, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95386106, -0.099892706, 0.071950704], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1063.1768, 429.99023, 633.1866], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0019578224, 0.023400329, -0.28701118], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [6.6754313, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [31.0, 0.023400329, -0.28701118], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1075.2555, 1001.83484, 73.420616], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0013559852, -0.013510717, 0.6558102], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [10.468194, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [53.0, -0.013510717, 0.6558102], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1074.0042, 1051.1064, 22.897772], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.002888274, 0.053702675, -0.112569146], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [6.3137393, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [24.0, 0.053702675, -0.112569146], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1074.1323, 746.23773, 327.89462], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0014600587, -0.06994717, 0.08998787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [6.790492, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [16.0, -0.06994717, 0.08998787], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1072.19, 593.5986, 478.59137], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.001639305, 0.08828236, -0.06470156], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [6.179593, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99823916, 0.08828236, -0.06470156], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1073.0967, 465.20065, 607.89606], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0016043258, -0.07053954, 0.0922309], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [6.9699435, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.07053954, 0.0922309], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1064.0469, 592.5585, 471.48837], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.001681386, -0.050113432, 0.10492942], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [5.7004337, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6792821, -0.050113432, 0.10492942], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1063.9739, 708.8772, 355.09668], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011967958, 0.034687184, -0.29059145], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [10.489995, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9700733, 0.034687184, -0.29059145], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1071.4321, 961.9089, 109.52326], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0012703849, -0.025187153, 0.21069127], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [5.894465, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999242, -0.025187153, 0.21069127], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1061.8789, 943.5532, 118.325645], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006955692, 0.5730505, -0.008016027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [5.350486, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, 0.5730505, -0.008016027], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1071.1538, 15.08798, 1056.0658], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0018660239, 0.01145508, -0.45755848], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [4.7266126, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, 0.01145508, -0.45755848], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1070.7122, 1049.7764, 20.935747], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005918795, -0.01863494, 0.35465696], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [7.2906866, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [17.0, -0.01863494, 0.35465696], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1069.0005, 1014.839, 54.161503], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0019931318, 0.07061356, -0.06963316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [5.2649536, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.85472345, 0.07061356, -0.06963316], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1069.1948, 546.0625, 523.1323], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0012654006, -0.05223339, 0.13347125], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [7.536624, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7118815, -0.05223339, 0.13347125], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1063.5848, 757.6123, 305.9726], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0015280662, 0.056874532, -0.07444884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [4.515711, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [15.0, 0.056874532, -0.07444884], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1071.8701, 620.27405, 451.5961], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0010626514, -0.17794675, 0.030932086], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [5.7343955, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.17794675, 0.030932086], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1070.4397, 152.36273, 918.0769], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0012936651, 0.030503845, -0.19880147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [6.269724, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [46.0, 0.030503845, -0.19880147], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1070.6608, 935.01373, 135.64711], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0013707844, -0.07425097, 0.082766786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [6.5998206, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [17.0, -0.07425097, 0.082766786], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1070.2178, 554.8327, 515.385], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0015846372, 0.16084121, -0.02795673], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [5.047472, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.89278525, 0.16084121, -0.02795673], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1070.9116, 166.87096, 904.04065], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0016410698, -0.2573301, 0.01912576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [4.833187, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.90474904, -0.2573301, 0.01912576], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.3058, 66.50861, 998.7971], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0014597493, 0.18209347, -0.033935476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [6.8499327, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.0, 0.18209347, -0.033935476], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1069.4115, 174.5385, 894.87305], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.001984814, 0.02206826, -0.2364379], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [5.1133633, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999995, 0.02206826, -0.2364379], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1065.7861, 983.8217, 81.96441], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00073751056, -0.0037161459, 0.95856935], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [4.537316, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [132.0, -0.0037161459, 0.95856935], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1061.801, 1057.8783, 3.9227252], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0019099852, -0.1878663, 0.025657132], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [4.7942314, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-5.0, -0.1878663, 0.025657132], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1061.7443, 117.31395, 944.43024], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009991432, 0.12075769, -0.06277462], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [8.1168995, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.12075769, -0.06277462], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1060.783, 368.2901, 692.49286], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0019715053, -0.04073937, 0.1035616], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [4.612213, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9883497, -0.04073937, 0.1035616], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1060.9941, 747.3778, 313.6163], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0010787974, 0.115337916, -0.05693317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [7.0565515, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.87734115, 0.115337916, -0.05693317], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1062.6035, 357.497, 705.10657], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0018199903, -0.02958628, 0.14013329], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [4.6092224, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [33.0, -0.02958628, 0.14013329], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1059.1237, 863.7755, 195.3481], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011460793, 0.010764707, -0.5030857], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [5.1473827, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [87.0, 0.010764707, -0.5030857], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1059.1956, 1040.3291, 18.866465], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00037676105, -0.051643252, 0.076796025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [4.2090516, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9997883, -0.051643252, 0.076796025], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1056.7952, 628.9687, 427.82645], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009920625, 0.072297454, -0.07173809], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [5.505067, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.85301226, 0.072297454, -0.07173809], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1059.514, 535.0002, 524.5138], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0013153722, -0.0038536324, 0.7391339], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [4.0293674, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999794, -0.0038536324, 0.7391339], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1054.7798, 1048.4294, 6.3503036], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0010827725, -0.006801179, 0.41669753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [3.464839, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99927926, -0.006801179, 0.41669753], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.5566, 1036.8715, 18.685165], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011297339, 0.011482487, -0.3032171], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [3.3320286, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999774, 0.011482487, -0.3032171], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1055.4038, 1021.61456, 33.789276], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-1.719315e-05, 0.015386907, -0.20837766], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [3.3789535, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [32.0, 0.015386907, -0.20837766], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1050.7639, 979.291, 71.472885], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00056083594, -0.010027274, 0.28524822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [3.1710691, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9983772, -0.010027274, 0.28524822], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1050.0591, 1013.3358, 36.723267], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000729394, 0.009724793, -0.47960207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [4.55389, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999006, 0.009724793, -0.47960207], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1051.8733, 1033.4983, 18.375067], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00028450854, -0.15005, 0.020089652], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [3.1315863, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.15005, 0.020089652], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1049.7743, 121.433685, 928.34064], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006086168, 0.0726538, -0.051267747], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [3.9294405, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [11.0, 0.0726538, -0.051267747], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1049.375, 439.12445, 610.25055], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00095482747, -0.05568911, 0.07182505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [4.219941, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, -0.05568911, 0.07182505], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1049.2129, 583.2539, 465.95898], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0010122872, -0.0049696653, 0.4838367], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [3.0372696, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999883, -0.0049696653, 0.4838367], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1049.7688, 1037.8994, 11.869348], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00072568184, -0.009235199, 0.25443408], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [2.661145, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [9.0, -0.009235199, 0.25443408], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1051.0885, 1012.3078, 38.780758], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000928846, 0.0033498027, -1.1464524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [2.923919, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [56.0, 0.0033498027, -1.1464524], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1050.2349, 1049.0184, 1.2163323], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00049986196, -0.016957195, 0.15741001], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [2.732911, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.892028, -0.016957195, 0.15741001], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1049.5878, 951.3327, 98.25507], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005256533, 0.024559673, -0.13429657], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [3.4206104, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9962836, 0.024559673, -0.13429657], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.622, 894.9092, 158.71272], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000417662, -0.02812571, 0.16296224], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [4.869562, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95794135, -0.02812571, 0.16296224], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1047.5839, 891.8066, 155.77725], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009029933, 0.019950278, -0.15665625], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [3.166811, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9700733, 0.019950278, -0.15665625], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1053.1837, 940.3754, 112.8083], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00030049554, -0.012915859, 0.20185737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [2.7958398, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [7.0, -0.012915859, 0.20185737], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1047.5709, 983.98553, 63.58533], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0008345779, 0.009370175, -0.40559104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [3.6413414, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, 0.009370175, -0.40559104], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1047.559, 1026.9679, 20.59105], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00037566118, -0.0053003943, 0.5350316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [3.181597, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [15.0, -0.0053003943, 0.5350316], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1046.4585, 1036.4454, 10.013104], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0013521812, 0.03244478, -0.08885442], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [2.9396727, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [25.0, 0.03244478, -0.08885442], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1046.0746, 778.41064, 267.66388], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006339589, -0.016506577, 0.26594532], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [4.760638, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [57.0, -0.016506577, 0.26594532], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.8857, 982.358, 62.5278], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.001208143, -0.035410643, 0.07072545], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [2.6675322, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.66225183, -0.035410643, 0.07072545], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1045.8993, 685.36725, 360.53198], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00058217515, 0.045479164, -0.07475021], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [3.5591826, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.90176564, 0.045479164, -0.07475021], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1050.3235, 658.3518, 391.97168], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007488515, -0.06440047, 0.046858344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [3.1444933, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95386106, -0.06440047, 0.046858344], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.7634, 432.8216, 611.9418], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007961042, 0.04960789, -0.082298376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [4.2758746, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97956854, 0.04960789, -0.082298376], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1052.2057, 663.09186, 389.11386], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0008778999, -0.04602739, 0.07157738], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [3.4699457, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9997883, -0.04602739, 0.07157738], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.3751, 628.0481, 416.32703], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00081535353, 0.004731647, -0.5937218], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [2.4424183, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9998634, 0.004731647, -0.5937218], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1046.7703, 1040.9058, 5.86454], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00054584915, 0.016037673, -0.14016972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [2.422409, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [42.0, 0.016037673, -0.14016972], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.2202, 934.1534, 110.06689], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0004474058, -0.03097969, 0.14054933], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [4.6067405, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [33.0, -0.03097969, 0.14054933], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.285, 853.5901, 190.69498], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011406243, 0.030370219, -0.09089301], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [2.8172097, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [26.0, 0.030370219, -0.09089301], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1045.2227, 793.788, 251.43459], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006021725, -0.04071341, 0.060422067], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [2.58565, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [17.0, -0.04071341, 0.060422067], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.1938, 617.8116, 426.38223], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00062791543, 0.06340678, -0.04430372], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [2.9523492, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [11.0, 0.06340678, -0.04430372], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.6555, 435.60846, 609.047], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007232811, 0.116840824, -0.024729045], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [3.0920308, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.0, 0.116840824, -0.024729045], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.2314, 187.093, 857.1385], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0008318775, -0.16296202, 0.022866234], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [3.77421, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-4.0, -0.16296202, 0.022866234], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1043.7177, 122.99917, 920.7185], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00077423325, 0.18101853, -0.015766514], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [3.1166239, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-12.0, 0.18101853, -0.015766514], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1043.4071, 86.86766, 956.5395], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0008373874, -0.21119323, 0.013973496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [2.9129755, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-17.0, -0.21119323, 0.013973496], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1043.7955, 60.014885, 983.7807], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0004803882, -0.014985302, 0.19177821], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [3.0918262, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [58.0, -0.014985302, 0.19177821], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1043.0754, 965.9072, 77.168205], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00088119827, -0.54927486, 0.005332131], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [2.5596814, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-68.0, -0.54927486, 0.005332131], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1043.1234, 7.3891025, 1035.7343], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00014594028, -0.006206682, 0.595738], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [3.9533331, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [67.0, -0.006206682, 0.595738], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1042.8909, 1032.8639, 10.027012], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0014508149, 0.0043711835, -0.8224076], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [2.5155964, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [150.0, 0.0043711835, -0.8224076], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1043.0701, 1040.3768, 2.6931767], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005531495, -0.0033047814, 0.8250137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [2.370488, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [168.0, -0.0033047814, 0.8250137], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1041.3065, 1038.84, 2.4664788], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.001086527, -0.0024850818, 0.574827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [2.1393096, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9940061, -0.0024850818, 0.574827], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1042.2881, 1036.8293, 5.4587283], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00064642733, 0.006471523, -0.32953408], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [2.0103378, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [91.0, 0.006471523, -0.32953408], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1043.1277, 1026.0071, 17.120577], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00018161011, 0.7739453, -0.0030666005], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [2.6259754, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-97.0, 0.7739453, -0.0030666005], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1042.8676, 3.367717, 1039.4998], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "7.693594E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "20", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 0]}
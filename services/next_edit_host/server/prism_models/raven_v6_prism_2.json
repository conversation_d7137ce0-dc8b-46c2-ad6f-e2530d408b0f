{"learner": {"attributes": {}, "feature_names": ["has_change_prob", "plus_mean", "minus_mean", "plus_token_prob_min", "plus_token_prob_median", "plus_token_prob_max", "minus_token_prob_min", "minus_token_prob_median", "minus_token_prob_max", "num_insert_lines", "num_delete_lines", "num_lines_delta", "plus_moving_average_median", "minus_moving_average_median", "num_recent_insert_lines", "num_recent_delete_lines", "num_recent_lines_delta", "max_contiguous_insert", "max_contiguous_delete", "number_changed_lines"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.018155878, 0.43311813, -0.28564113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [130.05275, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.86881506, 0.43311813, -0.28564113], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1075.3811, 399.97116, 675.41], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.01640004, -0.10081013, 0.75225776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [67.89403, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.10081013, 0.75225776], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1044.2467, 941.7025, 102.544136], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0065746866, 0.05374727, -0.79172266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [48.810318, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97604656, 0.05374727, -0.79172266], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1028.6885, 956.1595, 72.529015], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.009239349, 0.048530806, -0.4758653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [27.5539, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9430188, 0.048530806, -0.4758653], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1020.2839, 908.6815, 111.602394], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.005018243, -0.20010275, 0.15772627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [32.199432, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.20010275, 0.15772627], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1012.1954, 460.25302, 551.9424], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.004353524, -0.76557297, 0.017212858], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [16.522846, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.76557297, 0.017212858], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1004.66003, 26.728739, 977.9313], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0033545839, -0.38824543, 0.041945953], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [17.504747, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-16.0, -0.38824543, 0.041945953], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1002.02246, 104.718994, 897.30347], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027559882, 0.37343335, -0.048007615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [17.052538, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.62823325, 0.37343335, -0.048007615], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [999.6715, 106.55961, 893.11194], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-2.7799639e-05, -0.0064960266, 2.6159725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [16.723652, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99500763, -0.0064960266, 2.6159725], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [986.3396, 984.9019, 1.4377158], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012649825, 0.027783224, -0.5371737], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [15.391268, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9932783, 0.027783224, -0.5371737], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [986.74066, 936.90515, 49.83552], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036363935, 0.043398548, -0.27790233], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [12.69218, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9107021, 0.043398548, -0.27790233], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [981.94855, 838.9206, 143.02802], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018743488, -0.07702824, 0.22945741], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [17.08154, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.07702824, 0.22945741], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [980.5009, 740.57404, 239.92691], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014960457, -0.8786133, 0.011791163], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [11.35353, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-5.0, -0.8786133, 0.011791163], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [972.29175, 13.537351, 958.7544], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.001125845, 0.024642529, -0.383869], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [9.62548, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.96694547, 0.024642529, -0.383869], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [973.99176, 913.43024, 60.561527], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024106815, -0.009047004, 1.1149744], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [7.2047234, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [14.0, -0.009047004, 1.1149744], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [969.23834, 964.50195, 4.736423], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005395631, -0.08947326, 0.07102385], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [6.1523046, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8730677, -0.08947326, 0.07102385], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [967.70667, 424.86203, 542.84467], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006895474, 0.053877, -0.15044576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [8.023672, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97160244, 0.053877, -0.15044576], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [979.89594, 718.67365, 261.2223], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0015190715, 0.07831398, -0.07786648], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [5.9348493, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [8.0, 0.07831398, -0.07786648], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [971.7163, 475.00217, 496.71414], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00075725786, -0.18340214, 0.04497243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [8.124672, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.18340214, 0.04497243], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [970.7598, 193.78197, 776.97784], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0013366744, 0.13374618, -0.060293507], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [7.7257276, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.13374618, -0.060293507], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [968.0618, 293.74954, 674.31226], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.001019881, -0.98688614, 0.0045762993], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [5.36353, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-110.0, -0.98688614, 0.0045762993], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [970.34875, 4.4872665, 965.8615], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.002031012, -0.012559192, 0.77535766], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [7.952565, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [81.0, -0.012559192, 0.77535766], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [969.47327, 957.4898, 11.983446], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00046189647, 0.028687408, -0.18398269], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [5.195571, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97759205, 0.028687408, -0.18398269], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [969.2383, 837.11914, 132.11914], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00084354496, -0.006323665, 1.2002381], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [6.389885, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9995716, -0.006323665, 1.2002381], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [968.6484, 965.2391, 3.409317], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00015839345, 0.0057242154, -1.1692873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [6.318608, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999956, 0.0057242154, -1.1692873], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [968.732, 965.1337, 3.5983207], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023242077, -0.45532486, 0.0087045785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [4.846768, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-4.0, -0.45532486, 0.0087045785], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [968.32715, 22.05717, 946.26996], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005714676, -0.017255254, 0.3131725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [5.0863433, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.017255254, 0.3131725], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [969.67554, 921.6125, 48.06307], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00034971835, -0.83402306, 0.0054386402], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [4.1052523, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-108.0, -0.83402306, 0.0054386402], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [964.7703, 4.8610997, 959.90924], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0016817685, 0.007326228, -0.38045847], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [3.295996, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99980694, 0.007326228, -0.38045847], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [964.17883, 942.73944, 21.439394], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012120539, -0.0064867144, 0.9525642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [4.849541, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99943525, -0.0064867144, 0.9525642], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [961.73315, 957.43146, 4.301676], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00020337566, 0.007282168, -0.54680264], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [3.732189, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99994576, 0.007282168, -0.54680264], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [961.8281, 950.5142, 11.313896], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0014524347, -0.016679695, 0.23516479], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [3.4682755, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99657315, -0.016679695, 0.23516479], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [960.51044, 903.3085, 57.201984], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00037927195, 0.021432813, -0.18009813], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [3.777407, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.021432813, -0.18009813], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [961.6291, 858.3355, 103.293564], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00010202311, -0.03794852, 0.11549861], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [4.2431912, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.03794852, 0.11549861], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [967.8531, 729.646, 238.20709], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025646223, 0.01855333, -0.19512783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [3.5362172, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99488616, 0.01855333, -0.19512783], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [962.74475, 878.822, 83.922745], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007734744, -0.0045397463, 1.111561], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [4.03519, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.0045397463, 1.111561], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [960.9965, 958.7462, 2.2503164], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007622704, 0.0060453257, -0.6408499], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [3.2628417, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99975306, 0.0060453257, -0.6408499], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [960.4402, 953.5789, 6.861222], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012187369, -0.030938284, 0.12122339], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [3.501246, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.953508, -0.030938284, 0.12122339], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [960.13434, 773.2065, 186.92786], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003514084, 0.033296958, -0.13590726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [4.387211, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.87370074, 0.033296958, -0.13590726], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [959.8554, 769.58057, 190.27486], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00067895686, 0.019501971, -0.17897478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [3.4583707, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.999998, 0.019501971, -0.17897478], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [959.17566, 862.4477, 96.72801], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004231128, -0.022478791, 0.1494297], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [3.1827602, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9516645, -0.022478791, 0.1494297], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [960.9667, 838.4166, 122.55008], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00034349627, -0.21596809, 0.015083781], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [3.2065742, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-2.0, -0.21596809, 0.015083781], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [961.969, 63.36249, 898.6065], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00037102756, -0.012767955, 0.2787455], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [3.3426023, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [80.0, -0.012767955, 0.2787455], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [963.98914, 923.9081, 40.08101], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [1.469751e-05, 0.009134394, -0.70020014], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [6.159522, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [106.0, 0.009134394, -0.70020014], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [962.57184, 951.17065, 11.401225], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011928253, 0.09494232, -0.033451863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [2.9930406, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [18.0, 0.09494232, -0.033451863], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [963.0907, 241.48831, 721.60236], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00032873827, -0.11234695, 0.07346579], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [7.979322, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [30.0, -0.11234695, 0.07346579], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [963.2815, 382.35455, 580.92694], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010656201, 0.026089951, -0.17827052], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [4.6355486, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [45.0, 0.026089951, -0.17827052], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [961.3444, 834.3397, 127.004715], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00062545505, -0.03468521, 0.12699781], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [4.1913533, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [75.0, -0.03468521, 0.12699781], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [962.2217, 760.0972, 202.12445], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003453758, 0.07605854, -0.07294019], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [5.3367834, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [8.0, 0.07605854, -0.07294019], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [960.1842, 467.79547, 492.38873], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00065923826, -0.009190565, 0.4508095], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [3.7027433, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [110.0, -0.009190565, 0.4508095], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [959.2694, 942.43994, 16.829483], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-4.7339974e-05, 0.0064965407, -0.51810944], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [3.258478, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [125.0, 0.0064965407, -0.51810944], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [959.1713, 948.1818, 10.989462], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00087663735, -0.062503606, 0.048296634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [2.913112, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [21.0, -0.062503606, 0.048296634], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [959.30005, 425.61816, 533.68195], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003774841, 0.12377964, -0.041811027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [4.9430947, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [18.0, 0.12377964, -0.041811027], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [958.8874, 239.43231, 719.4551], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000577438, -0.13976173, 0.033427134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [4.551563, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.13976173, 0.033427134], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [959.69824, 187.82022, 771.878], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00077684363, 0.0501522, -0.0767617], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [3.7134008, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [50.0, 0.0501522, -0.0767617], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [957.58093, 573.5186, 384.06232], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000466023, -0.064577654, 0.046739593], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [2.9078848, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [13.0, -0.064577654, 0.046739593], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [958.83295, 406.4503, 552.3826], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00038930608, 0.075765885, -0.040188596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [2.908478, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [10.0, 0.075765885, -0.040188596], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [957.59686, 328.3677, 629.2291], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003484012, -0.011243419, 0.24697347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [2.5887415, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [80.0, -0.011243419, 0.24697347], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [958.6926, 919.1565, 39.53612], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [6.308189e-05, -0.028267007, 0.08699568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [2.3626409, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.028267007, 0.08699568], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [957.32947, 722.5391, 234.79033], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00011367997, 0.011818276, -0.19317946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [2.2007341, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [68.0, 0.011818276, -0.19317946], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [953.333, 898.7281, 54.604893], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003895017, -0.06627957, 0.040258974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [2.5604572, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [29.0, -0.06627957, 0.040258974], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [953.99365, 363.74402, 590.24963], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00036503148, 0.08602456, -0.031237982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [2.5457883, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, 0.08602456, -0.031237982], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [952.50745, 250.30638, 702.20105], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00027810805, -0.002964296, 0.8303293], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [2.13083, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [198.0, -0.002964296, 0.8303293], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [952.9259, 950.8473, 2.0786128], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00075434794, 0.0032328703, -0.8234423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [1.9502712, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999956, 0.0032328703, -0.8234423], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [952.40656, 950.5442, 1.862397], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012392951, -0.0061368537, 0.4046204], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [1.8974085, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95770746, -0.0061368537, 0.4046204], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [952.3151, 941.9336, 10.381548], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-5.9663605e-05, 0.02119811, -0.11699863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [2.3745644, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.46323016, 0.02119811, -0.11699863], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [953.23, 807.2945, 145.93547], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00042015687, 0.019420205, -0.113618806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [2.1440957, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.61488, 0.019420205, -0.113618806], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [952.6876, 811.3164, 141.37123], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00044882495, -0.056267634, 0.045398463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [2.438476, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.056267634, 0.045398463], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [950.8512, 428.69287, 522.1583], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003369915, -0.048617054, 0.039668113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [1.8328902, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8824969, -0.048617054, 0.039668113], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [946.9731, 429.00912, 517.9639], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00021155474, 0.09127244, -0.031155277], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [2.7024515, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.76972747, 0.09127244, -0.031155277], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [952.6368, 240.28706, 712.3497], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-7.76432e-05, -0.12853794, 0.015142025], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [1.8536943, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.12853794, 0.015142025], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [946.12604, 99.432144, 846.6939], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004102726, 0.002606666, -0.58402526], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [1.6673148, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [124.0, 0.002606666, -0.58402526], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.0803, 941.2103, 3.8699582], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007230428, -0.005830776, 0.49261945], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [2.387292, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [143.0, -0.005830776, 0.49261945], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.25024, 936.5421, 8.708139], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00036573107, 0.008447471, -0.25882906], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [1.98253, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [66.0, 0.008447471, -0.25882906], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.3865, 916.76886, 27.61761], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000501965, 0.3497693, -0.008979705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [2.8107662, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-55.0, 0.3497693, -0.008979705], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.48474, 21.368156, 923.1166], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-8.9204994e-05, -0.16319422, 0.013092619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [2.035663, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-21.0, -0.16319422, 0.013092619], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.81665, 69.79758, 875.01904], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00048030543, -0.023743918, 0.07512601], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [1.6637439, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, -0.023743918, 0.07512601], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [943.899, 722.3287, 221.57028], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00015991635, 0.04340001, -0.07443523], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [3.0604708, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [33.0, 0.04340001, -0.07443523], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [943.92645, 595.249, 348.67746], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041916766, -0.05923085, 0.046441436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [2.605987, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [21.0, -0.05923085, 0.046441436], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [943.5873, 418.31876, 525.2685], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00034122437, 0.115284674, -0.016187614], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [1.7319568, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [8.0, 0.115284674, -0.016187614], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [943.24225, 112.93289, 830.3094], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00016341245, -0.044322327, 0.031487335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [1.3222888, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.86542785, -0.044322327, 0.031487335], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.073, 393.98602, 550.087], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00014476979, 0.032473166, -0.056327105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [1.7429132, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9495588, 0.032473166, -0.056327105], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [949.08734, 600.7375, 348.34982], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00029770224, -0.029448733, 0.0636686], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [1.7654722, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9602829, -0.029448733, 0.0636686], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.78894, 649.38684, 295.40207], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00026250383, 0.017698184, -0.1090792], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [1.858533, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98672223, 0.017698184, -0.1090792], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [948.9493, 815.2294, 133.71994], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00048056166, -0.004652009, 0.3680498], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [1.457145, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99330205, -0.004652009, 0.3680498], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.7431, 936.1342, 9.608859], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [5.6875735e-05, 0.005073089, -0.41109934], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [1.9560685, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999745, 0.005073089, -0.41109934], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [946.4099, 935.9784, 10.4315195], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00089263445, -0.007908022, 0.35136768], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [2.3417726, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, -0.007908022, 0.35136768], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.485, 927.98145, 17.503525], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00012601179, 0.017726947, -0.11006655], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [1.8366169, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9932177, 0.017726947, -0.11006655], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.9517, 815.5276, 129.4242], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00030853171, -0.015480011, 0.17499508], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [2.5195377, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9514166, -0.015480011, 0.17499508], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.31323, 870.8574, 74.4558], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00027449048, 0.010987243, -0.23780511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [2.5377362, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99851596, 0.010987243, -0.23780511], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [946.7072, 904.7645, 41.942688], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000680237, 0.027425447, -0.071269676], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [1.879993, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8713642, 0.027425447, -0.071269676], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.6089, 676.7626, 268.8463], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025624983, -0.03429628, 0.06462972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [2.094814, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.94654983, -0.03429628, 0.06462972], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [946.42523, 621.0734, 325.35187], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00018984641, 0.027166268, -0.0717818], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [1.8523302, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.45962527, 0.027166268, -0.0717818], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [943.8053, 683.321, 260.4843], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025295647, -0.020999506, 0.08311993], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [1.6376152, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95141566, -0.020999506, 0.08311993], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.7536, 757.1037, 187.64992], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00013708211, 0.014348127, -0.15003917], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [2.0553474, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99997926, 0.014348127, -0.15003917], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.57886, 862.1706, 82.40824], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00042573363, -0.0070005823, 0.26405954], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [1.6453756, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99988437, -0.0070005823, 0.26405954], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.1257, 922.17487, 21.950844], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "3.5626844E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "20", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 0]}
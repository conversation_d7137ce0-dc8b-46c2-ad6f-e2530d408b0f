{"learner": {"attributes": {}, "feature_names": ["has_change_prob", "plus_mean", "minus_mean", "plus_token_prob_min", "plus_token_prob_median", "plus_token_prob_max", "minus_token_prob_min", "minus_token_prob_median", "minus_token_prob_max", "num_insert_lines", "num_delete_lines", "num_lines_delta", "plus_moving_average_median", "minus_moving_average_median", "num_recent_insert_lines", "num_recent_delete_lines", "num_recent_lines_delta", "max_contiguous_insert", "max_contiguous_delete", "number_changed_lines"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.65460783, 0.20018582, -0.86769354], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [65.857834, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.76972747, 0.20018582, -0.86769354], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [359.6159, 71.77021, 287.8457], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.300588, 0.4366039, -0.4384725], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [23.380226, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.63563865, 0.4366039, -0.4384725], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [227.12985, 35.447144, 191.68271], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.08826772, -0.22692187, 0.44654536], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [13.385975, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.22692187, 0.44654536], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [178.14926, 141.92888, 36.220387], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.033134643, 0.1453774, -0.6434301], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [18.72538, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.87007356, 0.1453774, -0.6434301], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [170.02045, 132.13316, 37.88729], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.04199314, 0.08285388, -0.6006027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [11.938919, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97970104, 0.08285388, -0.6006027], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [169.47647, 139.21431, 30.262169], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.030604497, -0.18252324, 0.34594452], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [9.573221, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9216931, -0.18252324, 0.34594452], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [165.246, 118.10985, 47.136158], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.021467796, 0.05571229, -0.71282], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [8.822963, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.96199787, 0.05571229, -0.71282], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [163.60788, 148.0046, 15.603288], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.021366555, 0.07038668, -0.64105237], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [9.363465, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [45.0, 0.07038668, -0.64105237], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [162.8875, 142.65222, 20.235262], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.021914821, -0.1323525, 0.5558883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [10.501147, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [37.0, -0.1323525, 0.5558883], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [162.41283, 136.99869, 25.414137], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.022818783, 0.7041023, -0.11167892], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [10.603154, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.7041023, -0.11167892], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [161.93265, 16.884571, 145.04808], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.022855021, 0.036675494, -0.84347], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [7.9694, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99800307, 0.036675494, -0.84347], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [161.50153, 151.46872, 10.032807], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.020201085, 0.03951523, -0.84765875], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [8.011094, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [62.0, 0.03951523, -0.84765875], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [160.44827, 150.53653, 9.911739], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.020104924, -0.2689939, 0.14377439], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [6.59542, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.2689939, 0.14377439], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [159.75279, 63.171185, 96.581604], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.015291491, -0.45976228, 0.090953216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [7.578626, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-6.0, -0.45976228, 0.090953216], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [158.60152, 29.955675, 128.64584], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.01756454, -0.05377802, 1.1562358], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [6.8500285, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, -0.05377802, 1.1562358], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [158.6863, 154.86275, 3.8235598], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0027201877, 0.6554623, -0.04543266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [4.419639, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.6554623, -0.04543266], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [155.15273, 8.580753, 146.57198], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0072167264, -0.20283027, 0.13978049], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [4.535767, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.20283027, 0.13978049], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [155.75433, 66.66347, 89.09087], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.010732759, 0.07569852, -0.50184387], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [6.64478, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.92155415, 0.07569852, -0.50184387], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [154.64671, 132.22255, 22.424156], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.016053401, 0.047000986, -0.49695557], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [4.752529, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.62946147, 0.047000986, -0.49695557], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [154.95992, 137.79494, 17.164976], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.013693696, 0.03560048, -0.45592496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [3.3847752, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [45.0, 0.03560048, -0.45592496], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [153.52443, 138.95503, 14.569397], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.009904919, -0.16631351, 0.12830292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [3.341555, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.75336677, -0.16631351, 0.12830292], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [152.59361, 71.48795, 81.10565], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.007130178, 0.15405765, -0.26124543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [6.477276, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8885503, 0.15405765, -0.26124543], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [156.15292, 95.78763, 60.3653], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.016202174, -0.20601077, 0.094970316], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [3.2730317, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [17.0, -0.20601077, 0.094970316], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [153.18182, 56.265217, 96.9166], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.007944683, -0.099669285, 0.27308443], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [3.9692762, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.78417873, -0.099669285, 0.27308443], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [151.92758, 115.028854, 36.898724], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0074647963, 0.03234155, -0.57529575], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [3.4532127, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [46.0, 0.03234155, -0.57529575], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.95172, 141.94414, 9.007589], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.010275212, 0.04701454, -0.37573612], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [3.1981409, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98881495, 0.04701454, -0.37573612], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.90575, 131.20879, 19.696968], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00819882, 0.22189854, -0.09216334], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [2.9567034, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, 0.22189854, -0.09216334], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.97983, 39.9253, 111.05453], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.005808819, -0.18276627, 0.114330195], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [3.242881, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.18276627, 0.114330195], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.55513, 60.670246, 89.88488], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00764963, -0.08640874, 0.29309538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [3.6048136, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [25.0, -0.08640874, 0.29309538], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.11989, 119.53004, 30.589848], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0062523116, 0.1390281, -0.1878753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [3.9822757, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, 0.1390281, -0.1878753], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [148.93239, 82.87507, 66.05732], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.008979234, -0.046223592, 0.5400038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [3.1020513, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [49.0, -0.046223592, 0.5400038], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.4946, 140.85448, 8.640113], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0038362697, 0.06324623, -0.33697516], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [3.3718789, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [41.0, 0.06324623, -0.33697516], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [148.92834, 124.64031, 24.28804], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.008250357, -0.10067922, 0.24823694], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [3.6006486, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [29.0, -0.10067922, 0.24823694], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.82812, 110.584785, 39.24334], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0069698337, 0.056170616, -0.27015695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [2.5086281, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [39.0, 0.056170616, -0.27015695], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.04755, 120.843025, 28.20452], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.006257557, -0.14758673, 0.114490785], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [2.5850372, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.75336677, -0.14758673, 0.114490785], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.48933, 68.7726, 80.716736], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0055575133, 0.72341156, -0.029053066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [2.6519961, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.5147559, 0.72341156, -0.029053066], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [152.6111, 3.8350854, 148.776], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0017853499, -0.03858927, 0.31197444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [1.9098083, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.5373564, -0.03858927, 0.31197444], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.53345, 133.97118, 16.562271], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.002753234, 0.01714153, -0.8264741], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [2.5070703, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999597, 0.01714153, -0.8264741], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [151.1201, 148.51237, 2.6077273], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00922469, 0.028915795, -0.3132661], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [1.7785679, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99984455, 0.028915795, -0.3132661], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [151.59279, 135.4999, 16.092894], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.005951893, -0.058193594, 0.25843576], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [2.110158, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8362519, -0.058193594, 0.25843576], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.6876, 126.47639, 24.211222], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036789717, -0.07645186, 0.15925957], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [1.8100418, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [59.0, -0.07645186, 0.15925957], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.62257, 104.4867, 46.135876], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.003432636, 0.115518905, -0.12017522], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [2.1109045, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [16.0, 0.115518905, -0.12017522], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.00925, 74.30686, 75.702385], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.004755868, -0.11479851, 0.12667695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [2.2034583, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [11.0, -0.11479851, 0.12667695], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.34367, 81.899536, 68.444145], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0048703095, 0.020204496, -0.49724662], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [1.8726565, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99106795, 0.020204496, -0.49724662], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.86456, 143.51488, 6.349687], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.007456244, -0.71417385, 0.008325583], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [1.6843821, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.5067753, -0.71417385, 0.008325583], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.48793, 2.298688, 147.18924], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.005560847, 0.15027781, -0.07731242], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [1.6991948, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [9.0, 0.15027781, -0.07731242], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.9235, 46.92082, 103.00267], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034670648, -0.16572696, 0.08970446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [2.2981312, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.16572696, 0.08970446], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [150.02953, 54.440933, 95.5886], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0056335935, -0.46393377, 0.020635474], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [1.8257238, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-33.0, -0.46393377, 0.020635474], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.8541, 7.220563, 142.63353], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.006439564, -0.0324403, 0.3706468], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [1.4902772, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999995, -0.0324403, 0.3706468], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.77258, 140.96666, 8.805917], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00012760691, 0.04961712, -0.3160566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [2.3485498, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999976, 0.04961712, -0.3160566], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [148.08597, 128.7733, 19.312668], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.005735895, -0.05546502, 0.21504565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [1.6657982, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9883497, -0.05546502, 0.21504565], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [149.63573, 122.73868, 26.897043], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023422383, 0.046760015, -0.31824663], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [2.3340013, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9172061, 0.046760015, -0.31824663], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [148.50858, 129.26793, 19.240639], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0066963523, -0.051629204, 0.201459], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [1.4112623, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.94857407, -0.051629204, 0.201459], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [148.77643, 122.98138, 25.79505], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019349831, 0.011165943, -0.684298], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [1.3370297, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9993784, 0.011165943, -0.684298], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.70807, 145.8907, 1.8173702], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.006698564, 0.03640983, -0.19464631], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [1.2139843, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.886036, 0.03640983, -0.19464631], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.96057, 121.01126, 26.949314], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0034483094, 0.08637792, -0.08600635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [1.1123272, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [39.0, 0.08637792, -0.08600635], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.99089, 70.85343, 77.13745], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024870103, -0.052681852, 0.20798834], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [1.5862917, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [37.0, -0.052681852, 0.20798834], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [148.11195, 120.19675, 27.915209], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022663986, 0.050005272, -0.22775812], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [1.7613384, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [39.0, 0.050005272, -0.22775812], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.46649, 120.34689, 27.119606], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0046532615, -0.20396861, 0.040441085], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [1.3484012, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.20396861, 0.040441085], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [148.1047, 26.675743, 121.428955], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0040416988, 0.21581584, -0.047468856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [1.4314644, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [9.0, 0.21581584, -0.047468856], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.85332, 23.732714, 124.1206], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021848034, -0.05558169, 0.17216668], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [1.3941547, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [22.0, -0.05558169, 0.17216668], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.72302, 113.61003, 34.113], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0021872914, 0.009882002, -0.6703819], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [1.2017926, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [73.0, 0.009882002, -0.6703819], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.19861, 145.55473, 1.6438777], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0063048247, -0.01898841, 0.6276521], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [1.2053539, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [71.0, -0.01898841, 0.6276521], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.42166, 145.48106, 1.9405937], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0015087428, 0.04069115, -0.24004705], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [1.4091071, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [36.0, 0.04069115, -0.24004705], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.84766, 127.06773, 19.779922], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0041405354, -0.050615493, 0.18879291], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [1.3417735, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [37.0, -0.050615493, 0.18879291], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [147.57599, 119.52238, 28.05361], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018602613, 0.018238913, -0.5227972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [1.5567849, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [62.0, 0.018238913, -0.5227972], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.77365, 142.25023, 4.523415], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0066821175, -0.026404498, 0.34492284], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [1.0342046, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [45.0, -0.026404498, 0.34492284], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.82594, 139.90334, 6.9226165], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [8.480973e-06, -0.6335698, 0.016969962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [1.5913873, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-63.0, -0.6335698, 0.016969962], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.0848, 2.8610184, 143.22377], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0067674792, 0.03181278, -0.18377297], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [1.0139061, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [39.0, 0.03181278, -0.18377297], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.61635, 121.05202, 25.564325], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030061426, -0.029291462, 0.35878867], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [1.4139187, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [49.0, -0.029291462, 0.35878867], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.57362, 137.50273, 9.070888], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006721041, 0.023820609, -0.42591074], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [1.5425225, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [55.0, 0.023820609, -0.42591074], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.12825, 139.06258, 7.065684], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0058813985, 0.05004142, -0.13325827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [1.0564398, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9131818, 0.05004142, -0.13325827], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.37204, 102.137375, 44.234673], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0032787302, -0.041733466, 0.2911326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [1.6618819, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9742152, -0.041733466, 0.2911326], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [144.7166, 128.75714, 15.959455], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0031776829, 0.008194626, -0.6424565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [1.0738659, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99427915, 0.008194626, -0.6424565], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [145.98625, 144.40459, 1.581672], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.006034007, 0.025293961, -0.21778688], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [0.98107266, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.87007356, 0.025293961, -0.21778688], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [146.05989, 128.00293, 18.056957], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0037170593, -0.041266993, 0.23842092], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [1.3395995, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95141524, -0.041266993, 0.23842092], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [145.25185, 126.46903, 18.782816], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0018630415, 0.051674917, -0.16364187], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [1.2757757, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8679367, 0.051674917, -0.16364187], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [145.31964, 109.69765, 35.62199], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0035160019, -0.06109363, 0.111346625], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [0.97534037, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9216931, -0.06109363, 0.111346625], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [145.44821, 97.195015, 48.2532], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017470251, 0.043229215, -0.13132599], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [0.8545105, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99635977, 0.043229215, -0.13132599], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [144.6481, 107.87247, 36.77563], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023810468, -0.012767405, 0.6094647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [0.9351594, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.997619, -0.012767405, 0.6094647], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [144.93236, 143.47592, 1.4564359], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0019444315, 0.012269276, -0.6121841], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [0.9321893, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9993784, 0.012269276, -0.6121841], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [144.83032, 143.39948, 1.4308375], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0055791805, -0.015144189, 0.5407008], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [0.77297586, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.90430737, -0.015144189, 0.5407008], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [145.36597, 143.82005, 1.5459182], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0018894596, 0.020175971, -0.41022804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [1.1077508, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95936745, 0.020175971, -0.41022804], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [144.89252, 139.64713, 5.245391], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00521468, 0.24466784, -0.034958776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [1.094532, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-1.0, 0.24466784, -0.034958776], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [145.11162, 14.667023, 130.4446], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007782411, 0.037288353, -0.13217399], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [0.73193556, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.94979066, 0.037288353, -0.13217399], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [144.3494, 112.479294, 31.870104], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00254568, -0.09988113, 0.07406398], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [1.0853268, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.75336677, -0.09988113, 0.07406398], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.55585, 63.0917, 80.46414], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.002397322, 0.080097504, -0.09131647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [1.0814648, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.81617624, 0.080097504, -0.09131647], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [145.4344, 75.49399, 69.940414], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0025569515, 0.00943372, -0.4256919], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [0.7381391, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.00943372, -0.4256919], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.69269, 140.68375, 3.0089486], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.004097064, -0.017461045, 0.44144484], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [0.8697092, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [8.0, -0.017461045, 0.44144484], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.77162, 140.51762, 3.2540011], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0013822832, 0.030141478, -0.17607829], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [0.7410826, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [34.0, 0.030141478, -0.17607829], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.16724, 123.915665, 19.251575], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026016699, -0.024969615, 0.30815423], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [1.0116994, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [49.0, -0.024969615, 0.30815423], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.44095, 134.66736, 8.773594], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [1.7995824e-05, 0.029980037, -0.20377794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [0.88503385, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [36.0, 0.029980037, -0.20377794], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [142.94106, 125.3631, 17.577967], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.002933112, 0.017897636, -0.24842975], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [0.7427642, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.017897636, -0.24842975], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.37532, 133.01584, 10.359491], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0026850728, -0.04389155, 0.19502027], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [1.1883541, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999883, -0.04389155, 0.19502027], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.81796, 119.65667, 24.16129], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.001201974, 0.03125603, -0.23052059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [1.0780041, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999976, 0.03125603, -0.23052059], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [142.86224, 125.90519, 16.957058], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.003174591, -0.02492135, 0.31882375], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [1.0208255, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999995, -0.02492135, 0.31882375], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [143.64714, 135.42366, 8.223485], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.499688E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "20", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 0]}
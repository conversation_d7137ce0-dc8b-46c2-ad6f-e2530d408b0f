{"learner": {"attributes": {}, "feature_names": ["has_change_prob", "plus_mean", "minus_mean", "plus_token_prob_min", "plus_token_prob_median", "plus_token_prob_max", "minus_token_prob_min", "minus_token_prob_median", "minus_token_prob_max", "num_insert_lines", "num_delete_lines", "num_lines_delta", "plus_moving_average_median", "minus_moving_average_median", "num_recent_insert_lines", "num_recent_delete_lines", "num_recent_lines_delta", "max_contiguous_insert", "max_contiguous_delete", "number_changed_lines"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.037518136, 0.38054287, -0.49591026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [210.46375, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9717727, 0.38054287, -0.49591026], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1096.2634, 573.44354, 522.8199], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.03652271, -0.61658645, 0.15882912], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [119.437775, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.61658645, 0.15882912], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1052.1549, 264.528, 787.6269], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.019505346, 0.1748096, -0.30918273], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [58.410564, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.91146576, 0.1748096, -0.30918273], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1035.7368, 620.1429, 415.59396], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.010170052, 0.29985097, -0.13222805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [38.607594, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9101379, 0.29985097, -0.13222805], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1018.2228, 287.22626, 730.9965], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.004144902, 0.1454963, -0.19009745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [27.600374, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, 0.1454963, -0.19009745], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [989.89185, 548.6198, 441.27206], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0044381814, 0.042915296, -0.5733089], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [26.643404, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98154986, 0.042915296, -0.5733089], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [987.1504, 912.1468, 75.003624], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0068574343, -0.05867143, 0.39360765], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [20.340427, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [12.0, -0.05867143, 0.39360765], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [978.16296, 866.8584, 111.304596], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029121027, -0.018957762, 1.1092542], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [17.44824, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [65.0, -0.018957762, 1.1092542], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [975.5637, 962.658, 12.90569], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [9.42313e-05, 0.20618641, -0.08348335], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [16.751537, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.20618641, -0.08348335], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [970.53156, 279.6014, 690.9302], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.002574977, -0.29226938, 0.04853419], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [14.401787, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999511, -0.29226938, 0.04853419], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [970.7386, 144.87077, 825.8678], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017820523, 0.14874949, -0.13696262], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [19.884584, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, 0.14874949, -0.13696262], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [975.17926, 461.34454, 513.8347], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0030942315, 0.10836753, -0.11770881], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [12.356661, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9069606, 0.10836753, -0.11770881], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [965.24396, 489.38016, 475.8638], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019874347, 0.009062883, -0.9139756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [9.665076, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [104.0, 0.009062883, -0.9139756], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [957.2282, 946.74677, 10.481417], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0023034012, -0.17023177, 0.051237434], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [8.619962, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.27768996, -0.17023177, 0.051237434], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [956.75946, 230.77252, 725.98694], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.001838252, 0.15045278, -0.0855489], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [12.137112, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.81517714, 0.15045278, -0.0855489], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [950.04175, 336.70044, 613.3413], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017702897, -0.04725179, 0.2018971], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [8.794887, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7980485, -0.04725179, 0.2018971], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [947.42413, 775.10205, 172.32208], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.001876848, 0.027866527, -0.37350985], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [10.577293, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99979675, 0.027866527, -0.37350985], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [954.9659, 885.0561, 69.9098], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0036074526, -0.08455047, 0.15185599], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [11.93007, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97702485, -0.08455047, 0.15185599], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [946.03845, 622.42505, 323.61337], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024718945, 0.19593349, -0.04981802], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [9.081821, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98618037, 0.19593349, -0.04981802], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [964.7574, 185.26411, 779.4933], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00052538095, -0.39927235, 0.040434193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [15.607677, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9992678, -0.39927235, 0.040434193], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [953.6319, 88.01789, 865.614], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012382268, 0.038971145, -0.1924603], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [7.4047003, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9948949, 0.038971145, -0.1924603], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [961.0598, 794.7412, 166.31863], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00209907, -0.018584583, 0.39809206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [6.289183, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9982239, -0.018584583, 0.39809206], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [951.167, 914.45056, 36.716404], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009410936, 0.19017652, -0.037459724], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [6.659917, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.0, 0.19017652, -0.037459724], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [952.21045, 152.0837, 800.1267], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00053194736, -0.014623961, 0.42136952], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [5.6438446, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [17.0, -0.014623961, 0.42136952], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [947.23615, 917.5541, 29.682068], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00011534576, -0.0468147, 0.10284259], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [4.5544934, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7489654, -0.0468147, 0.10284259], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.25903, 650.6737, 294.58533], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009345026, 0.124862924, -0.041370932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [4.866974, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9915824, 0.124862924, -0.041370932], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [954.7697, 231.74011, 723.02966], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00031400786, -0.19700406, 0.03547336], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [6.672588, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99995923, -0.19700406, 0.03547336], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.94995, 144.92523, 801.0247], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00095054094, 0.07705718, -0.068189465], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [4.9866505, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.07705718, -0.068189465], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [948.7138, 439.11978, 509.59406], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007714553, -0.046145435, 0.12826174], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [5.556394, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.046145435, 0.12826174], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [947.02936, 701.1243, 245.90503], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00079236785, 0.006987358, -0.61009794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [4.496565, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [111.0, 0.006987358, -0.61009794], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [946.697, 935.7379, 10.959142], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011108582, 0.49447727, -0.014151511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [6.1341867, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-37.0, 0.49447727, -0.014151511], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [947.0715, 23.335276, 923.7362], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [9.354688e-06, -0.22002994, 0.031897984], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [6.6421223, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-6.0, -0.22002994, 0.031897984], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [944.609, 118.82028, 825.78876], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011308837, 0.12147082, -0.04026861], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [4.546619, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7712323, 0.12147082, -0.04026861], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [945.5184, 228.28761, 717.2308], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00044481372, 0.021271795, -0.19624215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [4.008201, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [9.0, 0.021271795, -0.19624215], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [940.6688, 847.55475, 93.11408], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006229596, 0.010395699, -0.38788706], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [4.0298615, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99947774, 0.010395699, -0.38788706], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [942.451, 917.324, 25.127068], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017261667, -0.023462184, 0.19050726], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [3.9321682, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [57.0, -0.023462184, 0.19050726], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [939.00305, 844.4035, 94.59956], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00035201677, 0.03406306, -0.1642665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [5.299451, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [19.0, 0.03406306, -0.1642665], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [937.43896, 775.42487, 162.0141], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00090938126, -0.014187232, 0.34186986], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [4.2784643, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9972566, -0.014187232, 0.34186986], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [937.97284, 903.9175, 34.055412], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00063732755, 0.007573945, -0.53759974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [4.150737, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9944135, 0.007573945, -0.53759974], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [939.46936, 926.29034, 13.179011], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017886084, -0.0063055987, 0.8948568], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [3.8063335, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99958616, -0.0063055987, 0.8948568], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [937.41016, 933.69946, 3.7106879], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005020927, -0.01658745, 0.24474832], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [3.9170914, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [33.0, -0.01658745, 0.24474832], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [936.46497, 876.0978, 60.36716], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00020166396, 0.014908351, -0.23032205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [3.2583077, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [64.0, 0.014908351, -0.23032205], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [935.08264, 878.34454, 56.738075], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072229846, -0.038793206, 0.17462905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [6.2557764, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [36.0, -0.038793206, 0.17462905], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [935.06995, 768.9092, 166.16077], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00077159295, 0.054730512, -0.107089445], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [5.522176, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [51.0, 0.054730512, -0.107089445], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [933.83154, 613.85846, 319.97308], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009032896, -0.024702912, 0.17459826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [3.909067, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [83.0, -0.024702912, 0.17459826], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [933.85156, 823.092, 110.75959], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00040777616, 0.0061046006, -0.4746143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [2.8871477, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [110.0, 0.0061046006, -0.4746143], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [932.9549, 921.28973, 11.665136], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000794618, 0.60264874, -0.0062539172], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [3.0819757, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-72.0, 0.60264874, -0.0062539172], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [933.3819, 7.387752, 925.9942], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00082529895, -0.2030765, 0.015901135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [2.8680487, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-18.0, -0.2030765, 0.015901135], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [930.95465, 63.2344, 867.7203], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00060487824, 0.09481511, -0.04840346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [4.257785, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, 0.09481511, -0.04840346], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [931.52606, 310.56454, 620.9615], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006002435, -0.15564236, 0.025963845], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [3.8411384, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, -0.15564236, 0.025963845], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [930.6617, 135.41994, 795.24176], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007213218, 0.08203394, -0.05293551], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [4.029827, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99849814, 0.08203394, -0.05293551], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [930.60974, 359.7941, 570.8156], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005791831, -0.13410728, 0.036787853], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [4.645631, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.13410728, 0.036787853], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [929.08356, 202.58246, 726.5011], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011416973, 0.083824135, -0.038235296], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [2.9152477, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99537206, 0.083824135, -0.038235296], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.96265, 280.10352, 642.85913], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00041923998, -0.04927497, 0.091607064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [4.1593194, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9132577, -0.04927497, 0.091607064], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [923.10913, 603.2925, 319.81665], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007561238, 0.0039879344, -0.68439704], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [3.0206022, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999571, 0.0039879344, -0.68439704], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [929.5139, 924.0954, 5.418502], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011456157, -0.0111888265, 0.31580588], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [2.9652355, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [25.0, -0.0111888265, 0.31580588], "split_indices": [19, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [929.4135, 901.8029, 27.610641], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00011188138, 0.0073398803, -0.7723411], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [5.1893754, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [20.0, 0.0073398803, -0.7723411], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [927.43243, 919.816, 7.6164074], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0011389569, -0.010485712, 0.32269654], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [2.8166523, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999999, -0.010485712, 0.32269654], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [928.4496, 903.3443, 25.105303], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00017752982, 0.025859354, -0.1645225], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [3.9814696, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99999535, 0.025859354, -0.1645225], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [928.46643, 802.2155, 126.25094], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0008948607, -0.03919734, 0.1162166], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [4.165968, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [3.0, -0.03919734, 0.1162166], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [926.71484, 698.82306, 227.8918], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005392981, -0.009408831, 0.3330639], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [2.7447696, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95770746, -0.009408831, 0.3330639], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [925.57117, 902.54694, 23.024282], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005558518, 0.01718168, -0.19993168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [3.2894976, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99340445, 0.01718168, -0.19993168], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [928.2019, 853.20966, 74.99223], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009362814, 0.11741006, -0.024818856], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [2.622348, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.91624093, 0.11741006, -0.024818856], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [925.76904, 154.79427, 770.97473], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-5.851817e-05, -0.2368592, 0.021601189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [4.7307816, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9984677, -0.2368592, 0.021601189], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [920.35583, 76.295784, 844.06006], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00052465004, 0.15290777, -0.029236006], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [4.079046, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.24506053, 0.15290777, -0.029236006], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [923.93567, 144.95828, 778.97736], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-4.2526597e-05, -0.019802475, 0.1449764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [2.6324959, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.96592605, -0.019802475, 0.1449764], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [916.6632, 807.4987, 109.16449], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006274947, 0.032263212, -0.112956084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [3.4130006, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9909666, 0.032263212, -0.112956084], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [921.8027, 713.575, 208.22768], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00094644696, -0.051143605, 0.08967864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [4.1686926, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.97702485, -0.051143605, 0.08967864], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [914.36487, 588.71295, 325.65192], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00082729536, 0.025079241, -0.091050886], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [2.1653209, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9909666, 0.025079241, -0.091050886], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [924.4109, 718.75244, 205.65843], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006235466, -0.0123298755, 0.2555648], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [2.760212, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99984056, -0.0123298755, 0.2555648], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [918.31903, 879.1011, 39.217976], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00080325705, 0.00984831, -0.2549696], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [2.5042531, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99919504, 0.00984831, -0.2549696], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [923.0832, 886.8773, 36.205875], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010894876, -0.021524435, 0.12880947], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [2.4468586, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.95537215, -0.021524435, 0.12880947], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [919.7406, 795.4408, 124.29981], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00056607294, 0.009357087, -0.22170405], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [2.0331202, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [72.0, 0.009357087, -0.22170405], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [924.56384, 885.77405, 38.789795], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00054727955, -0.0058299406, 0.47126925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [2.3092315, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [107.0, -0.0058299406, 0.47126925], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [924.3895, 915.131, 9.258556], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-4.784761e-05, -0.040241763, 0.059033822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [2.2012365, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [8.0, -0.040241763, 0.059033822], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [924.9439, 550.64966, 374.29422], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00032858626, 0.024685146, -0.16831174], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [3.8948853, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [23.0, 0.024685146, -0.16831174], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [924.9486, 805.8114, 119.137245], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072759815, 0.023264278, -0.08797962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [1.9410683, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.999135, 0.023264278, -0.08797962], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [925.28125, 726.3018, 198.9794], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00056434306, -0.02268964, 0.11875827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [2.4334497, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98159206, -0.02268964, 0.11875827], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [919.7239, 776.5437, 143.18015], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004524266, 0.027420184, -0.095013805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [2.4377038, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9933086, 0.027420184, -0.095013805], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.9003, 713.34656, 209.55374], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005647903, -0.011398474, 0.20449017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [2.047835, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [73.0, -0.011398474, 0.20449017], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [919.77606, 874.517, 45.25901], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [4.0086998e-05, 0.020310504, -0.12309504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [2.296597, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [49.0, 0.020310504, -0.12309504], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [918.1091, 789.05115, 129.05797], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00042827489, -0.015817866, 0.13325652], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [1.8945361, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99964577, -0.015817866, 0.13325652], "split_indices": [12, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [918.8358, 824.7711, 94.06471], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004703302, -0.27139282, 0.0070856162], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [1.8943837, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6583828, -0.27139282, 0.0070856162], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [923.4702, 24.109066, 899.36115], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00043228306, 0.005889035, -0.30556825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [1.7866235, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.967342, 0.005889035, -0.30556825], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [924.32513, 906.5259, 17.799248], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00087064557, -0.0054388796, 0.39365494], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [1.6667953, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99931294, -0.0054388796, 0.39365494], "split_indices": [13, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.6352, 913.0492, 9.586034], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00014582812, 0.0026490865, -0.7268478], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [1.6823833, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [132.0, 0.0026490865, -0.7268478], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.40314, 920.23083, 2.1722763], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00093688554, -0.014508091, 0.1132302], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [1.4328033, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [56.0, -0.014508091, 0.1132302], "split_indices": [15, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.696, 825.44684, 97.24913], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-8.199509e-05, -0.30155158, 0.0059777414], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [1.6874323, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-44.0, -0.30155158, 0.0059777414], "split_indices": [16, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [921.7094, 17.20104, 904.50836], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00032313698, 0.05006994, -0.037674613], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [1.7415786, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [24.0, 0.05006994, -0.037674613], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [923.2592, 392.87198, 530.3872], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025730114, -0.034195364, 0.07688934], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [2.4208565, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [25.0, -0.034195364, 0.07688934], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.61926, 641.1317, 281.48752], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00032589195, 0.04189982, -0.04389783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [1.7001835, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [13.0, 0.04189982, -0.04389783], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.0853, 468.29623, 453.78906], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00026942513, -0.08675913, 0.031188207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [2.5142744, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.08675913, 0.031188207], "split_indices": [17, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.11237, 245.46692, 676.64545], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00043294134, 0.08450764, -0.024945734], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [1.9253943, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [8.0, 0.08450764, -0.024945734], "split_indices": [14, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [922.71045, 206.09886, 716.6116], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00021361595, -0.044847358, 0.049298782], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [2.041434, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, -0.044847358, 0.049298782], "split_indices": [18, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [921.7574, 484.81097, 436.9464], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00031583512, -0.15628017, 0.012635263], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [1.865315, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, -0.15628017, 0.012635263], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [921.4871, 69.80381, 851.68335], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025024323, 0.18242384, -0.019657463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [3.2823281, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7444553, 0.18242384, -0.019657463], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [923.8408, 87.91592, 835.9249], "tree_param": {"num_deleted": "0", "num_feature": "20", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "3.2168934E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "20", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 0]}
This directory contains [XGBoost saved model](https://xgboost.readthedocs.io/en/stable/tutorials/saving_model.html) for next edit filtering. The saved models are json files.

# Model Releases

## [ prism_next_edit_v1.json ] - 2024-10-07
Train Data: `/mnt/efs/augment/data/prism/next_edit/2024-09-25_2024-10-01`

Test Data: `/mnt/efs/augment/data/prism/next_edit/2024-10-01_2024-10-07`

For training and testing, we use BACKGROUND mode suggests that are 1) accepted 2) rejected 3) hover shown 4) not hover shown that are valid for at least 1.8 sec and within 10 lines from the cursor. The model is trained to predict the probability of a suggest being rejected. The higher the probability, the more likely the suggest is rejected.


|  | 2024-09-25_2024-10-01_Train | 2024-09-25_2024-10-01_Test |
|----------|----------|----------|
| AUCROC   | 0.751  | 0.705   |

| Accept Rate | Hover Show Rate | Accept Recall | Not Accept Recall | Filter Rate | Threshold |
|-------------|-----------------|---------------|-------------------|-------------|-----------|
| 0.210       | 0.624           | 1.000         | 0.975             | 0.020       | 0.967     |
| 0.214       | 0.627           | 0.994         | 0.951             | 0.040       | 0.957     |
| 0.217       | 0.629           | 0.989         | 0.927             | 0.060       | 0.947     |
| 0.219       | 0.633           | 0.977         | 0.904             | 0.081       | 0.941     |
| 0.221       | 0.631           | 0.966         | 0.882             | 0.101       | 0.936     |
| 0.224       | 0.628           | 0.954         | 0.860             | 0.121       | 0.931     |
| 0.228       | 0.630           | 0.948         | 0.836             | 0.141       | 0.927     |
| 0.233       | 0.631           | 0.948         | 0.810             | 0.161       | 0.923     |
| 0.237       | 0.637           | 0.943         | 0.787             | 0.181       | 0.918     |
| 0.240       | 0.642           | 0.931         | 0.764             | 0.201       | 0.912     |
| 0.245       | 0.645           | 0.925         | 0.740             | 0.222       | 0.906     |
| 0.250       | 0.644           | 0.920         | 0.716             | 0.242       | 0.903     |
| 0.250       | 0.644           | 0.897         | 0.697             | 0.262       | 0.896     |
| 0.256       | 0.642           | 0.891         | 0.673             | 0.282       | 0.891     |
| 0.258       | 0.643           | 0.874         | 0.652             | 0.302       | 0.887     |
| 0.262       | 0.645           | 0.862         | 0.630             | 0.322       | 0.883     |
| 0.263       | 0.649           | 0.839         | 0.610             | 0.342       | 0.878     |
| 0.268       | 0.649           | 0.828         | 0.588             | 0.363       | 0.870     |
| 0.274       | 0.656           | 0.822         | 0.564             | 0.383       | 0.863     |
| 0.282       | 0.669           | 0.816         | 0.540             | 0.403       | 0.858     |
| 0.290       | 0.669           | 0.810         | 0.516             | 0.423       | 0.853     |
| 0.294       | 0.670           | 0.793         | 0.496             | 0.443       | 0.846     |
| 0.300       | 0.678           | 0.782         | 0.473             | 0.463       | 0.841     |
| 0.305       | 0.674           | 0.764         | 0.452             | 0.483       | 0.834     |
| 0.310       | 0.678           | 0.747         | 0.431             | 0.504       | 0.829     |
| 0.318       | 0.679           | 0.736         | 0.409             | 0.524       | 0.823     |
| 0.327       | 0.683           | 0.724         | 0.387             | 0.544       | 0.817     |
| 0.334       | 0.688           | 0.707         | 0.366             | 0.564       | 0.809     |
| 0.330       | 0.689           | 0.667         | 0.351             | 0.584       | 0.806     |
| 0.332       | 0.695           | 0.638         | 0.333             | 0.604       | 0.799     |
| 0.331       | 0.700           | 0.603         | 0.316             | 0.624       | 0.795     |
| 0.337       | 0.713           | 0.580         | 0.297             | 0.645       | 0.785     |
| 0.350       | 0.731           | 0.569         | 0.275             | 0.665       | 0.778     |
| 0.346       | 0.726           | 0.529         | 0.260             | 0.685       | 0.767     |
| 0.357       | 0.743           | 0.511         | 0.239             | 0.705       | 0.755     |
| 0.366       | 0.746           | 0.489         | 0.219             | 0.725       | 0.736     |
| 0.377       | 0.758           | 0.466         | 0.200             | 0.745       | 0.724     |
| 0.374       | 0.773           | 0.425         | 0.185             | 0.765       | 0.716     |
| 0.370       | 0.785           | 0.385         | 0.170             | 0.786       | 0.702     |
| 0.378       | 0.793           | 0.356         | 0.152             | 0.806       | 0.691     |
| 0.395       | 0.789           | 0.333         | 0.133             | 0.826       | 0.673     |
| 0.415       | 0.785           | 0.310         | 0.113             | 0.846       | 0.658     |
| 0.442       | 0.779           | 0.287         | 0.094             | 0.866       | 0.638     |
| 0.479       | 0.781           | 0.264         | 0.075             | 0.886       | 0.626     |

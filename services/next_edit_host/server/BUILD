load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

filegroup(
    name = "prism_models",
    srcs = glob(["prism_models/**"]),
    visibility = ["//services/next_edit_host/server:__subpackages__"],
)

py_library(
    name = "handler",
    srcs = [
        "handler.py",
    ],
    deps = [
        ":next_edit_types",
        "//base/diff_utils:proto_wrapper",
        "//base/feature_flags:feature_flags_py",
        "//base/ranges",
        "//services/next_edit_host:lru_dict",
        "//services/next_edit_host:next_edit_proto_py_proto",
        "//services/request_insight:request_insight_py_proto",
        requirement("grpcio"),
    ],
)

py_library(
    name = "next_edit_types",
    srcs = [
        "next_edit_types.py",
    ],
    deps = [
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        "//services/next_edit_host:next_edit_proto_py_proto",
        requirement("dataclasses_json"),
    ],
)

DEPS = [
    ":handler",
    ":next_edit_handler",
    "//base/python/opentelemetry_utils:traced_threadpool",
    "//base/feature_flags:feature_flags_py",
    "//base/tokenizers",
    "//base/prompt_format_completion",
    "//base/python/grpc:client_options",
    "//base/logging:struct_logging",
    "//base/python/signal_handler",
    "//base/tracing:tracing_py",
    "//services/next_edit_host:next_edit_proto_py_proto",
    "//services/inference_host/client",
    "//services/inference_host/client:multiplex",
    "//services/lib/retrieval:retriever_factory",
    "//services/lib/grpc/auth:service_token_auth",
    "//services/lib/grpc/auth:service_auth_interceptor",
    "//services/lib/grpc/metrics:metrics",
    "//services/lib/grpc/tls_config:grpc_tls_config_py",
    "//services/token_exchange/client:client_py",
    "//services/working_set/client:client_py",
    requirement("dataclasses_json"),
    requirement("grpcio-reflection"),
    requirement("grpcio"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("protobuf"),
    requirement("prometheus-client"),
    "//services/lib/grpc/stream_mux:server",
]

py_binary(
    name = "next_edit_server",
    srcs = [
        "next_edit_server.py",
    ],
    data = [
        ":prism_models",
    ],
    deps = DEPS,
)

pytest_test(
    name = "next_edit_server_test",
    size = "small",
    srcs = ["next_edit_server_test.py"],
    deps = [
        ":next_edit_server",
        requirement("pytest-grpc"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":next_edit_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

py_library(
    name = "next_edit_utils",
    srcs = [
        "next_edit_utils.py",
    ],
    deps = [
        "//base/caching:lru_cache",
        "//base/diff_utils",
        "//base/ranges",
        "//base/retrieval/chunking",
        "//services/lib/request_context:request_context_py",
        requirement("opentelemetry-api"),
        "//services/lib/retrieval:retriever",
        "//services/lib/retrieval:retriever_factory",
    ],
)

py_library(
    name = "next_edit_metrics",
    srcs = [
        "next_edit_metrics.py",
    ],
    deps = [
        requirement("prometheus-client"),
        ":next_edit_types",
        ":next_edit_utils",
        "//base/caching:cache_metrics",
        "//base/diff_utils",
        "//services/next_edit_host:next_edit_proto_py_proto",
    ],
)

py_library(
    name = "file_reconstruction",
    srcs = [
        "file_reconstruction.py",
    ],
    deps = [
        ":next_edit_metrics",
        ":next_edit_types",
        "//base/caching:cache_metrics",
        "//base/datasets:recency_info_conversion",
        "//base/diff_utils",
        "//base/diff_utils:proto_wrapper",
        "//services/lib/file_retriever",
        requirement("opentelemetry-api"),
        requirement("deepdiff"),
    ],
)

pytest_test(
    name = "file_reconstruction_test",
    srcs = ["file_reconstruction_test.py"],
    deps = [
        ":file_reconstruction",
    ],
)

py_library(
    name = "next_edit_handler",
    srcs = [
        "next_edit_blob_state_validator.py",
        "next_edit_descriptions.py",
        "next_edit_handler.py",
        "next_edit_locations.py",
        "//base/feature_flags:feature_flags_py",
    ],
    deps = [
        ":file_reconstruction",
        ":next_edit_metrics",
        ":next_edit_utils",
        ":next_edit_request_insight_builder",
        ":handler",
        ":next_edit_types",
        ":post_processing",
        "//base/datasets:itertools",
        "//services/lib/request_context:request_context_py",
        "//services/lib/file_retriever",
        "//base/logging:struct_logging",
        "//base/caching:lru_cache",
        "//base/diff_utils",
        "//base/next_edit_filter:rule_based_filters",
        "//base/retrieval/chunking",
        "//base/tokenizers",
        "//base/logging:secret_logging",
        "//base/prompt_format_next_edit:gen_prompt_formatter",
        "//base/prompt_format_next_edit:description_prompt_formatter",
        "//base/prompt_format_next_edit:location_prompt_formatter",
        "//base/prompt_format_next_edit:retrieval_prompt_formatter",
        "//base/prompt_format_retrieve",
        "//base/datasets:recency_info_conversion",
        "//services/content_manager/client",
        "//services/content_manager/client:content_cache",
        "//services/inference_host:infer_py_proto",
        "//services/inference_host/client",
        "//services/inference_host/client:multiplex",
        requirement("opentelemetry-api"),
        requirement("prometheus-client"),
        # Dependency needed for reusing retrievers.
        "//services/lib/retrieval:retriever",
        "//services/lib/retrieval:retriever_factory",
    ],
)

jsonnet_library(
    name = "handler_config_default",
    srcs = ["handler_config_default.jsonnet"],
    visibility = ["//services/deploy/next_edit:__subpackages__"],
)

jsonnet_to_json(
    name = "handler_config_default_json",
    src = "handler_config_default.jsonnet",
    outs = ["handler_config_default.json"],
)

pytest_test(
    name = "next_edit_config_test",
    srcs = ["next_edit_config_test.py"],
    data = [
        ":handler_config_default_json",
    ],
    deps = [
        ":next_edit_handler",
    ],
)

pytest_test(
    name = "next_edit_descriptions_test",
    srcs = ["next_edit_descriptions_test.py"],
    deps = [
        ":next_edit_handler",
        "//base/python/opentelemetry_utils:traced_threadpool",
        "//services/inference_host:infer_py_proto",
    ],
)

pytest_test(
    name = "next_edit_handler_test",
    srcs = ["next_edit_handler_test.py"],
    data = [
        ":handler_config_default_json",
    ],
    deps = [
        ":next_edit_handler",
        "//base/python/opentelemetry_utils:traced_threadpool",
        "//base/test_utils:synchronous_executor",
        "//services/inference_host:infer_py_proto",
    ],
)

py_library(
    name = "next_edit_request_insight_builder",
    srcs = [
        "next_edit_request_insight_builder.py",
    ],
    deps = [
        ":handler",
        "//base/prompt_format_next_edit:gen_prompt_formatter",
        "//base/tokenizers",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
        requirement("opentelemetry-api"),
        # Dependency needed for reusing retrievers.
        "//services/lib/retrieval:retriever",
    ],
)

pytest_test(
    name = "next_edit_request_insight_builder_test",
    srcs = ["next_edit_request_insight_builder_test.py"],
    deps = [
        ":next_edit_request_insight_builder",
    ],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

py_library(
    name = "post_processing",
    srcs = [
        "post_processing.py",
    ],
    deps = [
        "//base/caching:lru_cache",
        "//base/diff_utils",
        "//base/logging:struct_logging",
        "//base/next_edit_filter:extract_next_edit_filter_features",
        "//base/next_edit_filter:rule_based_filters",
        requirement("xgboost"),
        requirement("structlog"),
        requirement("dataclasses_json"),
        requirement("opentelemetry-api"),
    ],
)

pytest_test(
    name = "post_processing_test",
    srcs = ["post_processing_test.py"],
    deps = [
        ":post_processing",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

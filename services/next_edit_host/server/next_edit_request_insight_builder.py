"""Helper to build a request insight events during a next edit request."""

import logging
import threading
from typing import Iterable, Optional, Sequence
import uuid

import opentelemetry.trace

from base.tokenizers import Tokenizer
from services.lib.retrieval.retriever import RetrievalChunk
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.handler import NextEditResult
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher
from google.protobuf.timestamp_pb2 import Timestamp

opentelemetry_tracer = opentelemetry.trace.get_tracer(__name__)


# TODO: due to python pass-by-assignment we may want to inline the Tokenization field setting
def _get_tokenization_proto(
    tokenizer: Tokenizer,
    token_ids: Sequence[int],
    log_probs: Optional[Sequence[float]],
) -> request_insight_pb2.Tokenization:
    text, offsets = tokenizer.detokenize_with_offsets(token_ids)
    return request_insight_pb2.Tokenization(
        token_ids=token_ids,
        text=text,
        offsets=offsets,
        log_probs=log_probs or [],
    )


def _get_retrieval_chunk_proto(
    chunk: RetrievalChunk,
    idx: int = 0,
) -> request_insight_pb2.RetrievalChunk:
    return request_insight_pb2.RetrievalChunk(
        text=chunk.text,
        path=chunk.path,
        char_offset=chunk.char_start,
        char_end=chunk.char_end,
        blob_name=chunk.blob_name or "",
        chunk_index=chunk.chunk_index if chunk.chunk_index is not None else idx,
        origin=chunk.origin,
        score=chunk.score or 0.0,
    )


class NextEditRequestInsightBuilder:
    def __init__(
        self,
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        self.ri_publisher = ri_publisher

    def _record_event(
        self,
        event: request_insight_pb2.RequestEvent,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )
        # Should yield to main thread before making remote call
        with opentelemetry_tracer.start_as_current_span("publish_request_insight"):
            self.ri_publisher.publish_request_insight(update_request)

    def record_request(
        self,
        request: next_edit_pb2.NextEditRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> None:
        """Record a Next Edit request event."""
        with opentelemetry_tracer.start_as_current_span("create_request_event"):
            request_event = request_insight_publisher.new_event()
            ri_request = request_insight_pb2.RINextEditRequest(
                request=request,
            )
            # The final logging
            request_event.next_edit_host_request.MergeFrom(ri_request)
            self._record_event(request_event, request_context, auth_info)

    def record_response(
        self,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> "NextEditResponseBuilder":
        """Record a Next Edit response event.

        Returns a builder by which location, generation, and suggestion events can be built and recorded.
        """
        return NextEditResponseBuilder(self, request_context, auth_info)


class NextEditResponseBuilder:
    def __init__(
        self,
        outer_builder: NextEditRequestInsightBuilder,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        self.outer_builder = outer_builder
        self.request_context = request_context
        self.auth_info = auth_info
        self.next_event_milliseconds: int = 0
        self.throttle_lock = threading.Lock()

    def _new_event(self) -> request_insight_pb2.RequestEvent:
        """Creates a new request insight event.

        Events are metered out to not have timestamps within 1 ms of each other.
        This prevents events from recording too closely together and getting lost within Big Table due to timestamp collisions.
        """
        with self.throttle_lock:
            event = request_insight_publisher.new_event()
            if event.time.ToMilliseconds() < self.next_event_milliseconds:
                updated_milliseconds = self.next_event_milliseconds
                updated_timestamp = Timestamp()
                updated_timestamp.FromMilliseconds(updated_milliseconds)
                event.time.CopyFrom(updated_timestamp)
            # Next event timestamp must be different than last at ms resolution.
            self.next_event_milliseconds = event.time.ToMilliseconds() + 1
        return event

    def _record_event(
        self,
        event: request_insight_pb2.RequestEvent,
    ):
        self.outer_builder._record_event(event, self.request_context, self.auth_info)

    def record_location_retrieval(
        self,
    ) -> "LocationEventBuilder":
        """Record a Next Edit location retrieval event.

        This event is expected to only have location retrieval data.
        Additional events will have edit generation and suggestion data."""
        return LocationEventBuilder(self)

    def record_generation(
        self,
    ) -> "GenerationEventBuilder":
        """Record a Next Edit generation event.

        This event is expected to only have generation data.
        Additional events will have edit retrieval and suggestion data."""
        return GenerationEventBuilder(self)

    def record_suggestion(
        self,
        generation_id: str,
        suggestion_order: int,
    ) -> "SuggestionEventBuilder":
        """Build a Next Edit suggestion event.

        This event is expected to only have suggestion data.
        Additional events will have edit retrieval and generation data."""
        return SuggestionEventBuilder(self, generation_id, suggestion_order)


class LocationEventBuilder:
    def __init__(
        self,
        outer_builder: NextEditResponseBuilder,
    ):
        self.outer_builder = outer_builder
        self.ri_response = request_insight_pb2.RINextEditResponse()
        self.returned_locations: list[RetrievalChunk] = []
        self.blocked_locations: list[RetrievalChunk] = []

    def add_returned_location(
        self,
        returned_location: RetrievalChunk,
    ):
        self.returned_locations.append(returned_location)

    def add_blocked_location(
        self,
        blocked_location: RetrievalChunk,
    ):
        self.blocked_locations.append(blocked_location)

    def publish_locations(self):
        with opentelemetry_tracer.start_as_current_span(
            "create_response_location_event"
        ):
            self.ri_response.retrieved_locations.extend(
                [
                    _get_retrieval_chunk_proto(chunk, idx)
                    for idx, chunk in enumerate(self.returned_locations)
                ]
            )
            self.ri_response.blocked_locations.extend(
                [
                    _get_retrieval_chunk_proto(chunk, idx)
                    for idx, chunk in enumerate(self.blocked_locations)
                ]
            )
            response_event = self.outer_builder._new_event()
            response_event.next_edit_host_response.MergeFrom(self.ri_response)
            self.outer_builder._record_event(response_event)


class GenerationEventBuilder:
    def __init__(
        self,
        outer_builder: NextEditResponseBuilder,
    ):
        self.outer_builder = outer_builder
        self.generation_event = request_insight_pb2.RINextEditGeneration()
        self.generation_event.generation_id = str(uuid.uuid4())
        self.location_chunk = request_insight_pb2.RetrievalChunk()
        self.prompt_tokenizer = None
        self.prompt_tokens = None
        self.output_tokenizer = None
        self.output_tokens = None
        self.output_log_probs = None

    def get_generation_id(self) -> str:
        return self.generation_event.generation_id

    def add_location(
        self,
        location: RetrievalChunk,
    ):
        self.generation_event.location_chunk.CopyFrom(
            _get_retrieval_chunk_proto(location)
        )

    def add_retrieved_chunks(
        self,
        retrieved_chunks: Iterable[RetrievalChunk],
    ):
        self.generation_event.retrieved_chunks.extend(
            [
                _get_retrieval_chunk_proto(
                    chunk, chunk.chunk_index if chunk.chunk_index is not None else idx
                )
                for idx, chunk in enumerate(retrieved_chunks)
            ]
        )

    def add_generation_prompt(
        self,
        tokenizer: Tokenizer,
        prompt_tokens: Sequence[int],
    ):
        self.prompt_tokenizer = tokenizer
        self.prompt_tokens = prompt_tokens

    def add_generation_output(
        self,
        tokenizer: Tokenizer,
        output_tokens: Sequence[int],
        log_probs: Optional[Sequence[float]],
    ):
        self.output_tokenizer = tokenizer
        self.output_tokens = output_tokens
        self.output_log_probs = log_probs

    def add_post_process_result(
        self,
        post_process_result: request_insight_pb2.RINextEditGeneration.PostProcessResult.ValueType,
    ):
        self.generation_event.post_process_result = post_process_result

    def add_editing_score(
        self,
        editing_score: float,
    ):
        self.generation_event.editing_score = editing_score

    def publish(self):
        with opentelemetry_tracer.start_as_current_span(
            "create_response_generation_event",
            attributes={"generation_id": self.get_generation_id()},
        ):
            if self.prompt_tokenizer and self.prompt_tokens:
                self.generation_event.generation_prompt.CopyFrom(
                    _get_tokenization_proto(
                        self.prompt_tokenizer, self.prompt_tokens, None
                    )
                )
            if self.output_tokenizer and self.output_tokens and self.output_log_probs:
                self.generation_event.generation_output.CopyFrom(
                    _get_tokenization_proto(
                        self.output_tokenizer, self.output_tokens, self.output_log_probs
                    )
                )
            ri_response = request_insight_pb2.RINextEditResponse()
            ri_response.generation.append(self.generation_event)
            response_event = self.outer_builder._new_event()
            response_event.next_edit_host_response.MergeFrom(ri_response)
            self.outer_builder._record_event(response_event)


class SuggestionEventBuilder:
    def __init__(
        self,
        outer_builder: NextEditResponseBuilder,
        generation_id: str,
        suggestion_order: int,
    ):
        self.outer_builder = outer_builder
        self.suggestion_event = request_insight_pb2.RINextEditSuggestion()
        self.suggestion_event.generation_id = generation_id
        self.suggestion_event.suggestion_order = suggestion_order
        self.prompt_tokenizer = None
        self.prompt_tokens = None
        self.output_tokenizer = None
        self.output_tokens = None
        self.output_log_probs = None
        self.response = None

    def add_description_prompt(
        self,
        tokenizer: Tokenizer,
        prompt_tokens: Sequence[int],
    ):
        self.prompt_tokenizer = tokenizer
        self.prompt_tokens = prompt_tokens

    def add_description_output(
        self,
        tokenizer: Tokenizer,
        output_tokens: Sequence[int],
        log_probs: Optional[Sequence[float]],
    ):
        self.output_tokenizer = tokenizer
        self.output_tokens = output_tokens
        self.output_log_probs = log_probs

    def add_response(
        self,
        response: NextEditResult,
    ):
        self.response = response

    def publish(self):
        with opentelemetry_tracer.start_as_current_span(
            "create_response_suggestion_event",
            attributes={
                "suggestion_id": self.response.suggested_edit.suggestion_id
                if self.response
                else "unknown"
            },
        ):
            if self.prompt_tokenizer and self.prompt_tokens:
                self.suggestion_event.description_prompt.CopyFrom(
                    _get_tokenization_proto(
                        self.prompt_tokenizer, self.prompt_tokens, None
                    )
                )
            if self.output_tokenizer and self.output_tokens and self.output_log_probs:
                self.suggestion_event.description_output.CopyFrom(
                    _get_tokenization_proto(
                        self.output_tokenizer, self.output_tokens, self.output_log_probs
                    )
                )
            if self.response:
                self.suggestion_event.result.CopyFrom(
                    self.response.to_next_edit_response_proto()
                )
                self.suggestion_event.post_process_result = (
                    self.response.suggested_edit.post_process_result
                )

            ri_response = request_insight_pb2.RINextEditResponse()
            ri_response.suggestions.append(self.suggestion_event)
            response_event = self.outer_builder._new_event()
            response_event.next_edit_host_response.MergeFrom(ri_response)
            self.outer_builder._record_event(response_event)

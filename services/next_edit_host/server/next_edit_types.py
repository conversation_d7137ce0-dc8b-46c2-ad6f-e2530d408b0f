from services.next_edit_host import next_edit_pb2
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from dataclasses import dataclass


@dataclass(frozen=True)
class NextEditRequest:
    """Encapsulates a request as it comes into the server: proto fields plus headers."""

    proto: next_edit_pb2.NextEditRequest
    """The request."""

    context: RequestContext
    """The request context."""

    auth_info: AuthInfo
    """The auth info."""

from enum import Enum
from collections.abc import Iterable
import dataclasses
from functools import wraps
from base.caching.lru_cache import lru_cache
from base.diff_utils.str_diff import StrDiff, line_diff
from base.logging.secret_logging import SecretLogger
from base.retrieval.chunking.smart_chunking import expand_point_by_smart_chunks
import opentelemetry.trace
import structlog
import logging
import traceback

from base.blob_names.python.blob_names import B<PERSON>bName, FilePath
from base.diff_utils.diff_utils import File
from base.ranges.line_map import LineMap
from base.ranges.range_types import CharRange
from services.lib.retrieval.retriever import RetrievalChunk
from dataclasses import dataclass
from dataclasses_json import dataclass_json

logger: logging.Logger = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)


def extend_region_to_line_boundaries(
    full_text: str, selected_range: CharRange
) -> CharRange:
    """Return the expanded char range based on chunking policy.
    Point ranges will be expanded to a smart chunk.
    All ranges will expand to line boundaries.

    Args:
        full_text: the full text of the file
        selected_range: the range of the selected text
        smart_chunk_size: the maximum size of a smart chunk
    """
    # always expand the selected range to line boundaries
    lmap = LineMap(full_text)
    lrange = lmap.crange_to_lrange(selected_range)
    expanded_range = lmap.lrange_to_crange(lrange)

    if selected_range != expanded_range:
        logger.info(
            "Expanded selection range from %s to %s",
            selected_range,
            expanded_range,
        )

    return expanded_range


# Helper functions for next edit generation.
def _generate_expanded_range(
    full_text: str, selected_range: CharRange, smart_chunk_size: int
) -> CharRange:
    """Return the expanded char range based on chunking policy.
    Point ranges will be expanded to a smart chunk.
    All ranges will expand to line boundaries.

    Args:
        full_text: the full text of the file
        selected_range: the range of the selected text
        smart_chunk_size: the maximum size of a smart chunk
    """
    expanded_range = selected_range
    if selected_range.is_point():
        # expand by smart chunks
        expanded_range = expand_point_by_smart_chunks(
            full_text,
            expanded_range.start,
            smart_chunk_size,
        )
    return extend_region_to_line_boundaries(full_text, expanded_range)


class RecentChangeTracker:
    """Responsible for tracking and updating files based on recent changes.

    The contract for this class (per request):
    - All files mentioned by location and retrieval chunks need to be added to this
      class using `add_indexed_files`.
    - All current files mentioned by recency chunks need to be added to this class
      using `add_changed_files`.
    This class is then able to:
    - Update retrieval and location chunks to their current state via
      `update_indexed_chunk_to_current`.
    - Get the current file for a given blob name and path via `get_current_file`.
    """

    def __init__(self):
        self._indexed_files: dict[BlobName, File] = {}
        self._changed_files: dict[FilePath, File] = {}

    def add_indexed_files(self, files: Iterable[File | None]):
        """Add an indexed file to the tracker."""
        for file in files:
            if file is None:
                continue
            self._indexed_files[file.blob_name] = file

    def add_changed_files(self, files: Iterable[File]):
        """Add a recent changed file's current state to the tracker."""
        for file in files:
            self._changed_files[file.path] = file

    def get_current_file(self, blob_name: BlobName, path: FilePath) -> File | None:
        """Get the current file for the given blob name and path."""
        file = self._changed_files.get(path)
        if file is None:
            # assuming the file has not been modified since it was indexed.
            file = self._indexed_files.get(blob_name)
        return file

    def update_indexed_chunk_to_current(
        self,
        chunk: RetrievalChunk,
        secret_logger: SecretLogger,
    ) -> RetrievalChunk:
        """Update a retrieval chunk by applying tracked recent changes.

        Args:
            chunk: The indexed location chunk to be updated.
            secret_logger: The secret logger to use.

        Returns:
            The updated location chunk, or the original chunk if cannot be updated.
        """

        current_file = self._changed_files.get(chunk.path)
        if current_file is None:
            # this chunk does not need an update
            return chunk

        secret_logger.secret_info(
            f"starting update of chunk: {chunk.path=}, {chunk.crange=}"
        )

        # we can't update chunks without a blob name bc we can't get the indexed version
        assert chunk.blob_name is not None

        if chunk.blob_name == current_file.blob_name:
            # the file has not changed, so we can return the chunk as is
            secret_logger.secret_info(
                f"File has not changed, returning original chunk: {chunk.path=}, {chunk.crange=}"
            )
            return chunk

        indexed_file = self._indexed_files.get(chunk.blob_name)

        # These files should never be none since the chunk was retrieved from an indexed file
        if indexed_file is None:
            secret_logger.error(
                f"Indexed file not found for updating chunk: {chunk.blob_name=}"
            )
            secret_logger.secret_error(f"{chunk.path=}, {chunk.crange=}")
            return chunk

        # If the file has not changed, return the chunk as is
        if indexed_file.blob_name == current_file.blob_name:
            secret_logger.secret_warn(
                f"Current file created when indexed existed: {chunk.path=}, {chunk.crange=}"
            )
            return chunk

        # Create a StrDiff to map indexed chunk range to current chunk range
        # This is potentially expensive so we use the cached version
        indexed_current_str_diff = cached_line_diff(
            indexed_file.contents, current_file.contents
        )

        if indexed_current_str_diff == StrDiff.empty():
            secret_logger.secret_warn(
                f"Indexed and current have different blobnames but not diff: {chunk.path=}, {chunk.crange=}"
            )
            return chunk

        current_range = indexed_current_str_diff.before_range_to_after(chunk.crange)

        secret_logger.secret_info(
            f"Updating chunk range from {chunk.crange=} to {current_range=}"
        )

        # Create the new chunk
        current_chunk = dataclasses.replace(
            chunk,
            char_start=current_range.start,
            char_end=current_range.stop,
            path=current_file.path,
            text=current_file.contents[current_range.to_slice()],
            blob_name=current_file.blob_name,
        )

        return current_chunk


@lru_cache(maxsize=128)
def cached_line_diff(before: str, after: str):
    return line_diff(before, after)


def is_ignored_file_path(path: str) -> bool:
    """Whether the given path is ignored.
    We ignore the following types of files:
        - Python notebooks: Ignored due to technical difficulties editing them.
    """
    return path.endswith(".ipynb")


# TODO: Similar dataclasses exist for chat and completions. Unify and put with inference host client?
@dataclass_json
@dataclass(frozen=True)
class SamplingParams:
    """The sampling parameters for the chat model. See infer.proto for their meaning."""

    top_k: int
    """Top-k sampling parameter."""

    top_p: float
    """Top-p sampling parameter."""

    temperature: float
    """Temperature sampling parameter."""

    seed: int
    """Random seed that is passed to inference host."""

    inference_timeout_s: float
    """Timeout that is passed to inference host."""

    def __post_init__(self):
        assert 0.0 <= self.top_p <= 1.0, f"{self.top_p=} must be [0, 1]."
        assert self.top_k >= 0, f"{self.top_k=} must be non-negative."
        assert self.temperature >= 0, f"{self.temperature=} must be non-negative."
        assert self.seed >= 0, f"{self.seed} must be non-negative."
        assert (
            self.inference_timeout_s > 0
        ), f"{self.inference_timeout_s} must be positive."


def yield_and_execute(func, *args, **kwargs):
    """Yield control before executing a function asynchronously.

    This function is useful for ensuring that the main thread is not blocked by long-running
    remote calls.

    Steps:
        1. Build a generator function to wrap the target function.
        2. Advance the generator to the yield point. This will return control to the caller.
        3. When control returns, consume from the generator.
        4. Generator function returns the result of the wrapped function call.
        5. Return the result of the wrapped function call.
    """

    @wraps(func)
    def wrapper():
        yield
        return func(*args, **kwargs)

    gen = wrapper()
    next(gen)
    return next(gen, None)


def safeTrue(
    limit: int,
    warning_message: str = "Next edit result processing is taking longer than expected.",
):
    """Helper function to make sure we don't run forever."""
    for i in range(limit):
        if i > 0.9 * limit:
            # Log a warning with a stack trace
            logger.warning(
                f"{warning_message}:  {i} iterations. Trace: {traceback.format_stack()}"
            )
        yield True
    yield False


class CancellationReason(Enum):
    GRPC_QUEUE_TIMEOUT = 0
    STALE_REQUEST = 1
    GRPC_CANCEL = 2


class NextEditCancelledException(Exception):
    """Exception to indicate that the next edit request was cancelled."""

    def __init__(self, message: str, reason: CancellationReason):
        self.reason = reason
        super().__init__(message)

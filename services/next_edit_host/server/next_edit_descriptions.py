from enum import Enum
import json
import re

from base.caching.lru_cache import LRUCache
from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name
from base.tokenizers import create_tokenizer_by_name
import opentelemetry.trace
from dataclasses_json import dataclass_json

from base.prompt_format_chat.prompt_formatter import (
    StructToTokensPromptFormatter,
)

from dataclasses import asdict, dataclass, field
from typing import Iterable, Literal, Optional, Sequence

import structlog
from services.next_edit_host.server.next_edit_metrics import (
    description_cache_metrics,
)

from base.diff_utils.diff_utils import File
from base.diff_utils.str_diff import (
    AddedSpan,
    DeletedSpan,
    DiffSpan,
    ModSpan,
    NoopSpan,
    align_spans_to_word_boundaries,
    combine_spans_on_same_line,
    precise_char_diff,
)
from base.prompt_format_next_edit.description_prompt_formatter import (
    EditDescriptionPromptFormatter,
    EditDescriptionPromptInput,
    RavenDescribePromptFormatter,
)
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.inference_host.infer_pb2_grpc import InfererStub
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.handler import (
    ScoredFileHunk,
)
from services.next_edit_host.server.next_edit_metrics import (
    record_description_inference_duration_context,
)
from services.next_edit_host.server.next_edit_request_insight_builder import (
    SuggestionEventBuilder,
)
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.next_edit_host.server.next_edit_utils import (
    SamplingParams,
)

logger = structlog.get_logger(__name__)
tracer = opentelemetry.trace.get_tracer(__name__)


@dataclass_json
@dataclass(frozen=True)
class DescriptionGenerationConfig:
    """Configuration for description generation inference calls."""

    tokenizer_name: str
    """Name of the tokenizer to use."""

    chat_prompt_formatter_name: str
    """Name of the prompt formatter to use for the chat model."""

    # TODO(arun): At some point, generalize this to support more than `raven_describe`.
    prompt_formatter_name: Literal["raven_describe"]
    """Name of the prompt formatter to use to create a chat prompt."""

    prompt_formatter_config: RavenDescribePromptFormatter.Config
    """Configuration for the description generation prompt formatter."""

    sampling_params: SamplingParams
    """The sampling parameters for the description model."""

    max_output_length: Optional[int]
    """The maximal number of tokens in the output.

    If not set, max_sequence_length will be used to compute this bound.
    """


@dataclass_json
@dataclass(frozen=True)
class DescriptionHeuristicConfig:
    delete_start: str = "Delete: "
    add_start: str = "Add: "
    mod_start: str = "Change: "
    mod_middle: str = " ➜ "
    whitespace_change_description: str = "Fix whitespace"
    markdown_code_delimiter: str = r"`"
    char_limit: int = 40


class DescriptionStyle(Enum):
    NO_STYLE = 1
    MARKDOWN = 2


class NextEditDescriptionService:
    def __init__(
        self,
        description_generation_config: DescriptionGenerationConfig,
        heuristic_config: DescriptionHeuristicConfig | None,
        description_prompt_formatter: EditDescriptionPromptFormatter,
        chat_prompt_formatter: StructToTokensPromptFormatter,
        description_gen_client: InfererClient,
    ):
        self.description_generation_config = description_generation_config
        # fall back to default config if none is provided
        self.heuristic_config = heuristic_config or DescriptionHeuristicConfig()
        self.description_gen_client = description_gen_client
        self.description_prompt_formatter = description_prompt_formatter
        self.chat_prompt_formatter = chat_prompt_formatter
        self.chat_end_token_ids = (
            {self.chat_prompt_formatter.tokenizer.special_tokens.eos}
            if self.chat_prompt_formatter
            else set()
        )

        self.description_cache: LRUCache[tuple[int, ...], tuple[int, ...], ...] = (
            LRUCache(
                get_missing_fn=self._get_description_missing_from_cache,
                max_size=10000,
                name="DescriptionCache",
            )
        )

        self.description_cache_metrics = description_cache_metrics
        self.description_cache_metrics.setup_listeners(self.description_cache)

    def get_description_for_edit(
        self,
        edit_file: File,
        edit_suggestion: ScoredFileHunk,
        edit_suggestion_id: str,
        request: NextEditRequest,
        ri_builder: SuggestionEventBuilder,
    ) -> tuple[str, str]:
        """Get a description for an edit suggestion.

        This method generates a description for the given edit suggestion. It first attempts to create a description
        using heuristics, and if that fails, it falls back to using an LLM trained for this task.

        Args:
            edit_file (File): The file being edited.
            edit_suggestion (ScoredFileHunk): The suggested edit.
            edit_suggestion_id (str): A unique identifier for this edit suggestion.
            request (NextEditRequest): The original edit request.
            ri_builder (SuggestionEventBuilder): A builder for recording request insights.

        Returns:
            tuple[str, str]: A tuple containing two strings:
                1. A plain text description of the edit.
                2. An optional markdown-formatted description of the edit (may be None).
                    This markdown-formatted description is only generated by the heuristic, not by the LLM.
        """

        logger.info(f"Generating description for edit {edit_suggestion_id}")

        # first we try to apply our heuristics
        no_style_description, markdown_description = self._get_heuristic_descriptions(
            edit_suggestion
        )

        # if the heuristic gave back a description, we're done and can return
        if no_style_description is not None:
            # we only check the no-style description
            # if the markdown description is None, we return an empty string
            return no_style_description, markdown_description or ""

        # otherwise, we fall back to the LLM
        with tracer.start_as_current_span(
            "description formatting", attributes={"suggestion_id": edit_suggestion_id}
        ):
            prompt_output = self.chat_prompt_formatter.format_prompt(
                self.description_prompt_formatter.format_input(
                    EditDescriptionPromptInput(
                        current_file=edit_file,
                        edit_region=edit_suggestion.crange,
                        replacement_text=edit_suggestion.suggested_code,
                        instruction="",  # instructions are not used in desciption prompt
                        recent_changes=[],  # we don't use recent changes for the description prompt
                    ),
                )
            ).tokens
            ri_builder.add_description_prompt(
                self.chat_prompt_formatter.tokenizer, prompt_output
            )

        with tracer.start_as_current_span(
            "description inference", attributes={"suggestion_id": edit_suggestion_id}
        ), record_description_inference_duration_context(request):
            chat_result = list(
                self.description_cache.get(
                    keys=[tuple(prompt_output)],  # our cache key is the prompt output
                    request_context=request.context,
                    request=request.proto,
                    ri_builder=ri_builder,
                )
            )

            if len(chat_result) != 1:
                logger.warning(
                    f"{len(chat_result)=} != 1, description generation failed."
                )
                return "", ""

            chat_tokens = chat_result[0]

            if chat_tokens is None:
                logger.warning("Description generation returned None result")
                return "", ""

            change_description = self.chat_prompt_formatter.tokenizer.detokenize(
                chat_tokens
            )

            return change_description, ""

    def _get_description_missing_from_cache(
        self,
        keys: Iterable[tuple[int, ...]],  # tuple to cache
        request_context: RequestContext,
        request: next_edit_pb2.NextEditRequest,
        ri_builder: SuggestionEventBuilder,
    ) -> list[tuple[int, ...]]:
        chat_tokens_list: list[tuple[int, ...]] = []
        for key in keys:
            chat_result = self._infer_description_gen(
                key,
                request_context,
                request.sequence_id,
            )
            chat_tokens = chat_result.output_tokens
            ri_builder.add_description_output(
                self.chat_prompt_formatter.tokenizer,
                chat_tokens,
                chat_result.log_probs,
            )

            # Drop the "end of text" token if we have it (true, most of the time).
            if chat_tokens[-1] in self.chat_end_token_ids:
                chat_tokens = chat_tokens[:-1]

            chat_tokens_list.append(tuple(chat_tokens))

        return chat_tokens_list

    def _infer_description_gen(
        self,
        tuple_input_tokens: tuple[int, ...],  # tuple to cache
        request_context: RequestContext,
        sequence_id: int,
    ) -> InferenceClientProtocol.Reply:
        input_tokens = list(tuple_input_tokens)
        if not self.description_gen_client:
            logger.info(
                "No description generation model configured, "
                "skipping description generation."
            )
            return InferenceClientProtocol.Reply([], [])
        assert self.description_prompt_formatter is not None
        assert self.description_generation_config is not None

        max_context_length = (
            self.description_generation_config.prompt_formatter_config.max_prompt_tokens
        )

        assert (
            len(input_tokens) < max_context_length
        ), f"{len(input_tokens)=} must be less than {max_context_length=}"
        remaining_context_length = max_context_length - len(input_tokens)
        if (
            max_output_length := self.description_generation_config.max_output_length
        ) is None:
            max_output_length = remaining_context_length
        else:
            max_output_length = min(remaining_context_length, max_output_length)

        cur_sampling_params = self.description_generation_config.sampling_params
        cur_seed = cur_sampling_params.seed

        reply = self.description_gen_client.infer(
            input_tokens=input_tokens,
            max_output_length=max_output_length,
            end_token_ids=self.chat_end_token_ids,
            top_k=cur_sampling_params.top_k,
            top_p=cur_sampling_params.top_p,
            temperature=cur_sampling_params.temperature,
            random_seed=cur_seed,
            request_context=request_context,
            timeout_s=cur_sampling_params.inference_timeout_s,
            sequence_id=sequence_id,
        )

        return reply

    def _get_heuristic_descriptions(
        self,
        edit_suggestion: ScoredFileHunk,
    ) -> tuple[Optional[str], Optional[str]]:
        """
        Generate heuristic descriptions for the given edit suggestion.

        Args:
            edit_suggestion (ScoredFileHunk): The edit suggestion to generate descriptions for.

        Returns:
            tuple[Optional[str], Optional[str]]: A tuple containing two descriptions:
                1. A plain text description without any styling.
                2. A markdown-formatted description.
        """

        # we need diff spans to generate the heuristic description
        if not edit_suggestion.diff_spans:
            edit_suggestion.diff_spans = align_spans_to_word_boundaries(
                precise_char_diff(
                    edit_suggestion.existing_code, edit_suggestion.suggested_code
                ).spans
            )

        heuristic_no_style_description = self._format_heuristic_description(
            edit_suggestion.diff_spans,
            DescriptionStyle.NO_STYLE,
        )

        heuristic_markdown_description = self._format_heuristic_description(
            edit_suggestion.diff_spans,
            DescriptionStyle.MARKDOWN,
        )

        return heuristic_no_style_description, heuristic_markdown_description

    def apply_style(self, text: str, style: DescriptionStyle) -> str:
        if style is DescriptionStyle.NO_STYLE:
            return text
        if style is DescriptionStyle.MARKDOWN:
            escaped_code = text.replace("`", r"\`")
            return f"{self.heuristic_config.markdown_code_delimiter}{escaped_code}{self.heuristic_config.markdown_code_delimiter}"
        else:
            raise ValueError(f"Unknown style: {style}")

    def _format_heuristic_description(
        self,
        diff_spans: Sequence[DiffSpan],
        style: DescriptionStyle,
    ) -> str | None:
        """
        Format a heuristic description for the given diff spans.

        Args:
            diff_spans (Sequence[DiffSpan]): The diff spans to format the description for.
            style (DescriptionHeuristicConfig.DescriptionStyle): The style to use for the description.

        Returns:
            str | None: The formatted description, or None if no description could be generated.
        """

        # Check for no change
        if all(isinstance(diff_span, NoopSpan) for diff_span in diff_spans):
            return ""

        # Check for whitespace only change
        only_whitespace = True
        for span in diff_spans:
            if isinstance(span, NoopSpan):
                continue
            elif isinstance(span, ModSpan):
                # if the change is different modulo whitespace, then we have a non-whitespace change
                if re.sub(r"\s+", "", span.before) != re.sub(r"\s+", "", span.after):
                    only_whitespace = False
                    break
            elif span.after.strip() or span.before.strip():
                only_whitespace = False
                break

        if only_whitespace:
            # return the whitespace change description
            return self.heuristic_config.whitespace_change_description

        combined_spans = combine_spans_on_same_line(diff_spans)

        if len(combined_spans) > 3:
            return None  # for a single line change, we will have at most 3 spans [Noop, Change, Noop]

        # TODO: Get the number of available characters on the line...

        # check for only deletion
        if len(combined_spans) == 1 and isinstance(combined_spans[0], DeletedSpan):
            before_text = combined_spans[0].before

            num_full_lines_deleted = before_text.count("\n")

            if num_full_lines_deleted > 1:
                return f"Delete {num_full_lines_deleted} lines"

            before_text = before_text.strip()
            delete_description = f"{self.heuristic_config.delete_start}{self.apply_style(before_text, style)}"

            return delete_description

        # Get rid of the noop spans
        spans_to_describe = list(
            filter(
                lambda x: not isinstance(x, NoopSpan)
                and (x.after.strip() or x.before.strip()),
                combined_spans,
            )
        )

        if len(spans_to_describe) != 1:
            # we should have one and only one span to describe
            return None

        span_to_describe = spans_to_describe[0]

        match span_to_describe:
            case AddedSpan(after):
                lines_to_add = [
                    line.strip() for line in after.split("\n") if line.strip() != ""
                ]

                if len(lines_to_add) != 1:
                    return None

                description = f"{self.heuristic_config.add_start}{self.apply_style(lines_to_add[0], style)}"

                return description

            case DeletedSpan(before):
                lines_to_delete = [
                    line.strip() for line in before.split("\n") if line.strip() != ""
                ]

                if len(lines_to_delete) != 1:
                    return None

                description = f"{self.heuristic_config.delete_start}{self.apply_style(lines_to_delete[0], style)}"

                return description

            case ModSpan(before, after):
                lines_to_add = [
                    line.strip() for line in after.split("\n") if line.strip() != ""
                ]

                lines_to_delete = [
                    line.strip() for line in before.split("\n") if line.strip() != ""
                ]

                if len(lines_to_add) != 1 or len(lines_to_delete) != 1:
                    return None

                description = f"{self.heuristic_config.mod_start}{self.apply_style(lines_to_delete[0], style)}{self.heuristic_config.mod_middle}{self.apply_style(lines_to_add[0], style)}"

                if len(description) > self.heuristic_config.char_limit:
                    # we don't truncate a modification -- fall back to the model
                    return None

                return description


def create_next_edit_description_service(
    generation_config: DescriptionGenerationConfig,
    heuristic_config: DescriptionHeuristicConfig | None,
    description_gen_client: InfererClient,
) -> NextEditDescriptionService:
    description_gen_tokenizer = create_tokenizer_by_name(
        generation_config.tokenizer_name
    )
    description_gen_prompt_formatter = RavenDescribePromptFormatter(
        description_gen_tokenizer,
        config=generation_config.prompt_formatter_config,
    )
    chat_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(
        generation_config.chat_prompt_formatter_name,
        description_gen_tokenizer,
    )

    logger.info(
        "Creating next edit description service with generation config: %s and heuristic config: %s",
        json.dumps(asdict(generation_config)),
        json.dumps(asdict(heuristic_config) if heuristic_config else None),
    )

    next_edit_handler = NextEditDescriptionService(
        heuristic_config=heuristic_config,
        description_generation_config=generation_config,
        description_gen_client=description_gen_client,
        description_prompt_formatter=description_gen_prompt_formatter,
        chat_prompt_formatter=chat_prompt_formatter,
    )

    return next_edit_handler

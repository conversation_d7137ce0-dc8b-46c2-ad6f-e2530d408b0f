from contextlib import contextmanager
import time
from base.diff_utils.apply_replacements_to_files import FileReplacementErrorType
from base.diff_utils.edit_events import ReplayEditErrorType
from prometheus_client import Counter, Gauge, Histogram
from base.caching.cache_metrics import CacheMetrics
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.next_edit_host.server.next_edit_utils import CancellationReason
from enum import Enum

INF = float("inf")
# buckets for the latency histogram, from 1ms to ~30s
latency_buckets = [(1.2**i - 1) / 100.0 for i in range(45)] + [INF]
_metric_label_names = [
    "model",
    "next_edit_mode",
    "next_edit_scope",
    "tenant_name",
    "request_source",
]


def _metric_labels(request: NextEditRequest):
    return [
        request.proto.model_name,
        next_edit_pb2.NextEditMode.Name(request.proto.mode),
        next_edit_pb2.NextEditScope.Name(request.proto.scope),
        request.auth_info.tenant_name,
        request.context.request_source,
    ]


_next_edit_latency = Histogram(
    "au_next_edit_host_latency_seconds",
    labelnames=_metric_label_names,
    documentation="Latency of a next edit request to first change suggestion (in the next edit host)",
    buckets=latency_buckets,
)

_blob_name_buckets = [0] + [2**i for i in range(16)]  # Buckets go up to 2^15 = 32,768
_next_edit_find_missing_blob_name_count_summary = Histogram(
    "au_next_edit_host_find_missing_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=_blob_name_buckets,
    documentation="Number of blob names in a find-missing request",
)
_next_edit_find_missing_missing_blob_name_count_summary = Histogram(
    "au_next_edit_host_find_missing_missing_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=_blob_name_buckets,
    documentation="Number of missing raw blob names in a find-missing request",
)
_next_edit_find_missing_nonindexed_blob_name_count_summary = Histogram(
    "au_next_edit_host_find_missing_nonindexed_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=_blob_name_buckets,
    documentation="Number of nonindexed blob names in a find-missing request",
)
_next_edit_diagnostics_count = Gauge(
    "au_next_edit_host_diagnostics_count",
    documentation="Number of diagnostics swapped in for a next edit request",
)
_next_edit_find_missing_latency = Histogram(
    "au_next_edit_host_find_missing_latency_seconds",
    labelnames=["model", "status_code", "request_source", "tenant_name"],
    documentation="Latency of a find-missing request (in next-edit host)",
    buckets=latency_buckets,
)


def record_find_missing_latecy(
    model_name: str,
    status_code: str,
    request_source: str,
    tenant_name: str,
    latency_seconds: float,
):
    _next_edit_find_missing_latency.labels(
        model_name,
        status_code,
        request_source,
        tenant_name,
    ).observe(latency_seconds)


def record_find_missing_count(
    model_name: str,
    request_source: str,
    tenant_name: str,
    requested_blob_name_count: int,
    missing_count: int,
    nonindexed_count: int,
):
    _next_edit_find_missing_blob_name_count_summary.labels(
        model_name,
        request_source,
        tenant_name,
    ).observe(requested_blob_name_count)
    _next_edit_find_missing_missing_blob_name_count_summary.labels(
        model_name,
        request_source,
        tenant_name,
    ).observe(missing_count)
    _next_edit_find_missing_nonindexed_blob_name_count_summary.labels(
        model_name,
        request_source,
        tenant_name,
    ).observe(nonindexed_count)


def record_first_change_latency(
    request: NextEditRequest,
    latency_seconds: float,
):
    _next_edit_latency.labels(*_metric_labels(request)).observe(latency_seconds)


_next_edit_suggestion_latency = Histogram(
    "au_next_edit_host_suggestion_latency_seconds",
    labelnames=_metric_label_names + ["has_change"],
    documentation="Latency of a next edit request to each suggestion (in the next edit host)",
    buckets=latency_buckets,
)


def record_suggestion_latency(
    request: NextEditRequest,
    latency_seconds: float,
    has_change: bool,
):
    _next_edit_suggestion_latency.labels(
        *_metric_labels(request), str(has_change)
    ).observe(latency_seconds)


_next_edit_suggestion_interval = Histogram(
    "au_next_edit_host_suggestion_interval_seconds",
    labelnames=_metric_label_names,
    documentation="Interval between successive suggestions (in the next edit host)",
    buckets=latency_buckets,
)


def record_suggestion_interval(
    request: NextEditRequest,
    interval_seconds: float,
):
    _next_edit_suggestion_interval.labels(*_metric_labels(request)).observe(
        interval_seconds
    )


_next_edit_host_context_processing_duration = Histogram(
    "au_next_edit_host_context_processing_duration_seconds",
    labelnames=_metric_label_names,
    documentation="Duration of context processing (in the next edit host)",
    buckets=latency_buckets,
)


def record_context_processing_duration(
    request: NextEditRequest,
    duration_seconds: float,
):
    _next_edit_host_context_processing_duration.labels(
        *_metric_labels(request)
    ).observe(duration_seconds)


@contextmanager
def record_context_processing_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    try:
        yield
    finally:
        duration_seconds = time.time() - start_time
        record_context_processing_duration(request, duration_seconds)


_next_edit_host_location_retrieval_duration = Histogram(
    "au_next_edit_host_location_retrieval_duration_seconds",
    labelnames=_metric_label_names,
    documentation="Duration of location retrieval processing (in the next edit host)",
    buckets=latency_buckets,
)


def record_location_retrieval_duration(
    request: NextEditRequest,
    duration_seconds: float,
):
    _next_edit_host_location_retrieval_duration.labels(
        *_metric_labels(request)
    ).observe(duration_seconds)


@contextmanager
def record_location_retrieval_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    try:
        yield
    finally:
        duration_seconds = time.time() - start_time
        record_location_retrieval_duration(request, duration_seconds)


_next_edit_generation_retrieval_duration = Histogram(
    "au_next_edit_host_generation_retrieval_duration_seconds",
    labelnames=_metric_label_names,
    documentation="Duration of next edit generation retrieval processing (in the next edit host)",
    buckets=latency_buckets,
)


def record_generation_retrieval_duration(
    request: NextEditRequest,
    duration_seconds: float,
):
    _next_edit_generation_retrieval_duration.labels(*_metric_labels(request)).observe(
        duration_seconds
    )


@contextmanager
def record_generation_retrieval_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    try:
        yield
    finally:
        duration_seconds = time.time() - start_time
        record_generation_retrieval_duration(request, duration_seconds)


_next_edit_generation_inference_duration = Histogram(
    "au_next_edit_host_generation_inference_duration_seconds",
    labelnames=_metric_label_names + ["has_change"],
    documentation="Duration of next edit generation inference (in the next edit host)",
    buckets=latency_buckets,
)


def record_generation_inference_duration(
    request: NextEditRequest,
    duration_seconds: float,
    has_change: bool,
):
    _next_edit_generation_inference_duration.labels(
        *_metric_labels(request), str(has_change)
    ).observe(duration_seconds)


@contextmanager
def record_generation_inference_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    has_change = [False]  # Use a list to make it mutable

    def set_has_change(value: bool):
        has_change[0] = value

    try:
        yield set_has_change
    finally:
        duration_seconds = time.time() - start_time
        record_generation_inference_duration(request, duration_seconds, has_change[0])


_next_edit_generation_formatting_duration = Histogram(
    "au_next_edit_host_generation_formatting_duration_seconds",
    labelnames=_metric_label_names,
    documentation="Duration of next edit generation formatting (in the next edit host)",
    buckets=latency_buckets,
)


def record_generation_formatting_duration(
    request: NextEditRequest,
    duration_seconds: float,
):
    _next_edit_generation_formatting_duration.labels(*_metric_labels(request)).observe(
        duration_seconds
    )


@contextmanager
def record_generation_formatting_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    try:
        yield
    finally:
        duration_seconds = time.time() - start_time
        record_generation_formatting_duration(request, duration_seconds)


_next_edit_description_inference_duration = Histogram(
    "au_next_edit_host_description_inference_duration_seconds",
    labelnames=_metric_label_names,
    documentation="Duration of next edit description inference (in the next edit host)",
    buckets=latency_buckets,
)


def record_description_inference_duration(
    request: NextEditRequest,
    duration_seconds: float,
):
    _next_edit_description_inference_duration.labels(*_metric_labels(request)).observe(
        duration_seconds
    )


@contextmanager
def record_reranker_inference_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    try:
        yield
    finally:
        duration_seconds = time.time() - start_time
        record_reranker_inference_duration(request, duration_seconds)


_next_edit_reranker_inference_duration = Histogram(
    "au_next_edit_host_reranker_inference_duration_seconds",
    labelnames=_metric_label_names,
    documentation="Duration of next edit reranker inference (in the next edit host)",
    buckets=latency_buckets,
)


def record_reranker_inference_duration(
    request: NextEditRequest,
    duration_seconds: float,
):
    _next_edit_reranker_inference_duration.labels(*_metric_labels(request)).observe(
        duration_seconds
    )


@contextmanager
def record_description_inference_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    try:
        yield
    finally:
        duration_seconds = time.time() - start_time
        record_description_inference_duration(request, duration_seconds)


_next_edit_low_quality_filter_duration = Histogram(
    "au_next_edit_host_low_quality_filter_duration_seconds",
    labelnames=_metric_label_names,
    documentation="Duration of low quality filter (in the next edit host)",
    buckets=latency_buckets,
)


def record_low_quality_filter_duration(
    request: NextEditRequest,
    duration_seconds: float,
):
    _next_edit_low_quality_filter_duration.labels(*_metric_labels(request)).observe(
        duration_seconds
    )


@contextmanager
def record_low_quality_filter_duration_context(
    request: NextEditRequest,
):
    start_time = time.time()
    try:
        yield
    finally:
        duration_seconds = time.time() - start_time
        record_low_quality_filter_duration(request, duration_seconds)


_next_edit_requests = Counter(
    "au_next_edit_host_requests",
    "Next Edit requests made",
    labelnames=_metric_label_names,
)


def record_request(
    request: NextEditRequest,
):
    _next_edit_requests.labels(*_metric_labels(request)).inc()


_next_edit_suggestions_queue_timeout = Counter(
    "au_next_edit_host_suggestions_queue_timeout",
    "Next Edit requests timed out",
    labelnames=_metric_label_names,
)


def record_suggestions_queue_timeout(
    request: NextEditRequest,
):
    _next_edit_suggestions_queue_timeout.labels(*_metric_labels(request)).inc()


_next_edit_thread_not_done = Counter(
    "au_next_edit_host_thread_not_done",
    "Next Edit threads not done",
    labelnames=_metric_label_names,
)


def record_thread_not_done(
    request: NextEditRequest,
    count: int = 1,
):
    _next_edit_thread_not_done.labels(*_metric_labels(request)).inc(count)


_next_edit_thread_result_timeout = Counter(
    "au_next_edit_host_thread_result_timeout",
    "Next Edit threads result timeout",
    labelnames=_metric_label_names,
)


def record_thread_result_timeout(
    request: NextEditRequest,
):
    _next_edit_thread_result_timeout.labels(*_metric_labels(request)).inc()


_next_edit_requests_cancelled = Counter(
    "au_next_edit_host_requests_cancelled",
    "Next Edit requests cancelled",
    labelnames=_metric_label_names + ["reason"],
)


def record_request_cancelled(
    request: NextEditRequest,
    reason: CancellationReason,
):
    _next_edit_requests_cancelled.labels(*_metric_labels(request), reason.name).inc()


_next_edit_requests_exception = Counter(
    "au_next_edit_host_requests_exception",
    "Next Edit request exception",
    labelnames=_metric_label_names + ["exception_type"],
)


def record_request_exception(
    request: NextEditRequest,
    exception_type: str,
):
    _next_edit_requests_exception.labels(*_metric_labels(request), exception_type).inc()


_next_edit_lock_timeouts = Gauge(
    "au_next_edit_host_lock_timeouts",
    "Number of times a lock was not acquired in the next edit host",
    labelnames=["lock_name"],
)


def record_lock_timeout(lock_name: str):
    _next_edit_lock_timeouts.labels(lock_name).inc()


_next_edit_diff_events_problems = Counter(
    "au_next_edit_host_diff_events_problems",
    "Number of problems with diff events",
    labelnames=_metric_label_names + ["problem_type"],
)


def record_diff_events_problems(
    request: NextEditRequest,
    problem_type: str,
):
    _next_edit_diff_events_problems.labels(*_metric_labels(request), problem_type).inc()


_post_processing_generation_counter = Counter(
    "au_next_edit_host_post_processing_generation_counter",
    "Number of times postprocessing has changed the generation.",
    labelnames=_metric_label_names + ["rule_name"],
)

_post_processing_hunk_counter = Counter(
    "au_next_edit_host_post_processing_hunk_counter",
    "Number of times postprocessing has changed the hunk.",
    labelnames=_metric_label_names + ["rule_name"],
)


def record_post_processing_generation(
    request: NextEditRequest,
    rule_name: str,
):
    _post_processing_generation_counter.labels(
        *_metric_labels(request), rule_name
    ).inc()


def record_post_processing_hunk(
    request: NextEditRequest,
    rule_name: str,
):
    _post_processing_hunk_counter.labels(*_metric_labels(request), rule_name).inc()


description_cache_metrics = CacheMetrics(
    prefix="au_next_edit_description_cache", name="random"
)

_location_idx_buckets = [float(i) for i in range(32)] + [INF]
_next_edit_suggest_location_index = Histogram(
    "au_next_edit_host_suggest_location_index",
    labelnames=_metric_label_names + ["has_change"],
    documentation="Indexes of locations with change suggested by the next edit host",
    buckets=_location_idx_buckets,
)


def record_change_location_index(
    request: NextEditRequest,
    location_idx: int,
    has_change: bool,
):
    _next_edit_suggest_location_index.labels(
        *_metric_labels(request), str(has_change)
    ).observe(location_idx)


_next_edit_first_location_change_index = Histogram(
    "au_next_edit_host_first_location_change_index",
    labelnames=_metric_label_names,
    documentation="Index of first location with a suggested change by the next edit host",
    buckets=_location_idx_buckets,
)


def record_first_change_location_index(
    request: NextEditRequest,
    location_idx: int,
):
    _next_edit_first_location_change_index.labels(*_metric_labels(request)).observe(
        location_idx
    )


class FileReconstructionEvent(Enum):
    ERROR_REPLACEMENT_TEXT = "error_replacement_text"
    ERROR_EDIT_EVENTS = "error_edit_events"
    WARNING_EDIT_EVENTS = "warn_edit_events"
    SUCCESS_REPLACEMENT_TEXT = "success_replacement_text"
    SUCCESS_EDIT_EVENTS = "success_edit_events"


class EditEventsWarningType(Enum):
    MISSING_CONTENT = "missing_content"
    FINAL_BLOB_NAME_MISMATCH = "final_blob_name_mismatch"


_file_reconstruction_counter = Counter(
    "au_next_edit_host_file_reconstruction_counter",
    "Counts related to file reconstruction.",
    labelnames=_metric_label_names
    + ["event", "is_blocking", "error_type", "v2_enabled"],
)


def record_file_reconstruction_event(
    request: NextEditRequest,
    event: FileReconstructionEvent,
    is_blocking: bool = False,
    error_type: FileReplacementErrorType
    | ReplayEditErrorType
    | EditEventsWarningType
    | None = None,
):
    v2_enabled = len(request.proto.unindexed_edit_events) > 0
    _file_reconstruction_counter.labels(
        *_metric_labels(request),
        event.value,
        str(is_blocking),
        error_type.value if error_type else "",
        str(v2_enabled),
    ).inc()


_file_reconstruction_diff_result = Counter(
    "au_next_edit_host_file_reconstruction_diff_result",
    "Breakdown of file reconstruction diff results.",
    labelnames=_metric_label_names + ["diff_result"],
)


def record_file_reconstruction_diff_result(
    request: NextEditRequest,
    diff_result: str,
):
    _file_reconstruction_diff_result.labels(*_metric_labels(request), diff_result).inc()


_inference_cache_hit_counter = Counter(
    "au_next_edit_host_inference_cache_hit_counter",
    "Number of inference cache hits",
    labelnames=_metric_label_names,
)


_inference_cache_miss_counter = Counter(
    "au_next_edit_host_inference_cache_miss_counter",
    "Number of inference cache misses",
    labelnames=_metric_label_names,
)


def record_inference_cache_hit(request: NextEditRequest):
    """Record a hit in the inference cache."""
    _inference_cache_hit_counter.labels(*_metric_labels(request)).inc()


def record_inference_cache_miss(request: NextEditRequest):
    """Record a miss in the inference cache."""
    _inference_cache_miss_counter.labels(*_metric_labels(request)).inc()

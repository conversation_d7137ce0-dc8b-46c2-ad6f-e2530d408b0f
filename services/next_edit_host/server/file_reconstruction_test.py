from unittest.mock import MagicMock
import logging
from base.logging.secret_logging import <PERSON><PERSON><PERSON><PERSON><PERSON>
from services.next_edit_host.server.file_reconstruction import (
    _reconstruct_using_edit_events,
)
from services.next_edit_host.server.next_edit_types import NextEditRequest
from base.diff_utils.diff_utils import File
from base.diff_utils.edit_events_pb2 import GranularEditEvent, SingleEdit
from services.lib.file_retriever.file_retriever import FileRetriever
from services.next_edit_host import next_edit_pb2
from services.completion_host import completion_pb2
from base.blob_names.python.blob_names import get_blob_name

logger = UnsafeLogger(logging.getLogger(__name__))


def create_mock_retrieve_indexed_files(available_blob_to_file: dict[str, File]):
    """Implements a retrieve_indexed_files function that returns the available files. Otherwise populates missing_blobs."""

    def retrieve(blob_name_to_file_path, missing_blobs, **kwargs):
        logger.info("mock file retriever called with {blob_name_to_file_path=}")
        result = {}
        for blob_name in blob_name_to_file_path:
            if blob_name in available_blob_to_file:
                result[blob_name] = available_blob_to_file[blob_name]
            else:
                missing_blobs.add(blob_name)
        return result

    return retrieve


def test_reconstruct_using_edit_events():
    """Sanity test for reconstructing with edit events."""
    # Mock objects
    request_proto: next_edit_pb2.NextEditRequest = next_edit_pb2.NextEditRequest()
    mock_file_retriever = MagicMock(spec=FileRetriever)
    mock_auth_info = MagicMock()
    mock_context = MagicMock()

    # Set up test data
    file_path = "test_file.py"
    initial_content = "def hello():\n    print('Hello')\n"
    edited_content = "def hello():\n    print('Hello, World!')\n"
    blob_name = get_blob_name(file_path, edited_content.encode("utf-8"))

    request_proto.unindexed_edit_events_base_blob_names.append("before_blob")
    request_proto.unindexed_edit_events.extend(
        [
            GranularEditEvent(
                path=file_path,
                before_blob_name="before_blob",
                after_blob_name=blob_name,
                edits=[
                    SingleEdit(
                        before_start=29,
                        after_start=29,
                        before_text="",
                        after_text=", World!",
                    )
                ],
            )
        ]
    )

    # Mock file retriever to return the initial content
    mock_file_retriever.retrieve_indexed_files.side_effect = (
        create_mock_retrieve_indexed_files(
            {"before_blob": File(file_path, initial_content)}
        )
    )

    result = list(
        _reconstruct_using_edit_events(
            NextEditRequest(
                proto=request_proto,
                auth_info=mock_auth_info,
                context=mock_context,
            ),
            set(),
            File(file_path, ""),
            logger,
            mock_file_retriever,
        )
    )

    assert len(result) == 1
    assert isinstance(result[0], File)
    assert result[0].path == file_path
    assert result[0].contents == edited_content
    mock_file_retriever.retrieve_indexed_files.assert_called_once()


def test_reconstructing_new_file_not_yet_indexed():
    """Asserts that new files that were not indexed yet are handled and don't raise errors"""

    request_proto: next_edit_pb2.NextEditRequest = next_edit_pb2.NextEditRequest()
    mock_file_retriever = MagicMock(spec=FileRetriever)
    mock_auth_info = MagicMock()
    mock_context = MagicMock()

    file_path = "test_file.py"
    edited_content = "def hello():\n    print('Hello, World!')\n"
    blob_name = get_blob_name(file_path, edited_content.encode("utf-8"))

    ## DO NOT add to unindexed_edit_events_base_blob_names
    request_proto.unindexed_edit_events.extend(
        [
            GranularEditEvent(
                path=file_path,
                before_blob_name="before_blob",  # assume before_blob exists
                after_blob_name=blob_name,
                edits=[
                    SingleEdit(
                        before_start=29,
                        after_start=29,
                        before_text="",
                        after_text=", World!",
                    )
                ],
            )
        ]
    )

    # Mock file retriever to simulate the file not uploaded yet.
    mock_file_retriever.retrieve_indexed_files.side_effect = (
        create_mock_retrieve_indexed_files({})
    )

    result = list(
        _reconstruct_using_edit_events(
            NextEditRequest(
                proto=request_proto,
                auth_info=mock_auth_info,
                context=mock_context,
            ),
            set(),
            File(file_path, ""),
            logger,
            mock_file_retriever,
        )
    )

    assert len(result) == 0
    mock_file_retriever.retrieve_indexed_files.assert_called_once()

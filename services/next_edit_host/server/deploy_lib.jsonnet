// K8S deployment file for a next edit server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

// function to create a next edit host
//    env: environemnt
//    namespace: namespace to use
//    model config: model config (from //services/deploy/configs) object
//    generationRetrievalConfigs: configuration of the retrievals
//    overrideConfig: object that overrides a next edit host configuration
//    lowQualityFilterConfig: object that configures the low quality filter
function(
  env,
  namespace,
  namespace_config,
  cloud,
  name,
  handlerConfig,
  inferenceServices,
  rerankerServices,
  descriptionServices,
  generationRetrievalConfigs,
  locationRetrievalConfigs,
  inferenceMtls,
  mtls,
  overrideConfig=null,
  lowQualityFilterConfig=null,
)
  local appName = name;
  // The next edit model names are quite long and tend to share prefixes, but we want
  // unique short names because IAM service accounts have a 30 character limit.
  // To get around this, use a hash to generate a short name.
  local shortAppName = 'next-%s' % std.substr(std.md5('%s-next-edit' % name), 0, 7);

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(cloud=cloud, env=env, namespace=namespace, appName=appName);
  local requestInsightPublisher = requestInsightPublisherLib(cloud, env, namespace, appName=appName);
  local services = grpcLib.grpcService(name, namespace=namespace);

  local configMap =
    local config = {
      port: 50051,
      model_name: name,
      edit_gen_host_endpoints: std.mapWithKey(function(k, v) '%s:50051' % v, inferenceServices),
      edit_gen_ranker_host_endpoints: if rerankerServices != null then std.mapWithKey(function(k, v) '%s:50051' % v, rerankerServices) else null,
      description_host_endpoints: if descriptionServices != null then std.mapWithKey(function(k, v) '%s:50051' % v, descriptionServices) else null,
      content_manager_endpoint: 'content-manager-svc:50051',
      working_set_endpoint: if namespace_config.flags.workingSetEndpoint != '' then namespace_config.flags.workingSetEndpoint else null,
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      generation_retrieval: { retrieval_configs: generationRetrievalConfigs },
      location_retrieval: { retrieval_configs: locationRetrievalConfigs },
      secret_logs_enabled: std.member(['DEV', 'STAGING'], env),  // only enable in dev and staging for now
      handler_type: 'NextEditHandler',
      handler_config: handlerConfig,
      auth_config: {
        token_exchange_endpoint: 'token-exchange-central-svc.%s:50051' % cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud),
      },
      low_quality_filter: lowQualityFilterConfig,
    } + if !mtls then {} else {
      client_mtls: {
        ca_path: '/client-certs/ca.crt',
        key_path: '/client-certs/tls.key',
        cert_path: '/client-certs/tls.crt',
      },
      server_mtls: {
        ca_path: '/certs/ca.crt',
        key_path: '/certs/tls.key',
        cert_path: '/certs/tls.crt',
      },
    } + if !inferenceMtls then {} else {
      central_client_mtls: {
        ca_path: '/inference-client-certs/ca.crt',
        key_path: '/inference-client-certs/tls.key',
        cert_path: '/inference-client-certs/tls.crt',
      },
    };
    local overriddenConfig = if overrideConfig == null then config else std.mergePatch(config, overrideConfig);
    configMapLib.createConfigMap(appName=appName, namespace=namespace, config=overriddenConfig);
  // create a client TLS certificate to securely access the content manager
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % name,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  local inferenceClientCert = certLib.createCentralClientCert(
    name='%s-infer-client-cert' % name,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='inference-client-certs',
    dnsNames=grpcLib.grpcServiceNames(name, namespace=namespace),
  );
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(
    name='%s-server-certificate' % name,
    namespace=namespace,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(name),
    volumeName='certs',
  );

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName, env=env, cloud=cloud, namespace=namespace, iam=true, overridePrefix=shortAppName,
  );
  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];
  local retrievalObjects = (
    // names need to be unique here
    std.map(function(r) r.getRetrievalObjects(name + '-content', namespace), generationRetrievalConfigs) +
    std.map(function(r) r.getRetrievalObjects(name + '-location', namespace), locationRetrievalConfigs)
  );
  local container =
    {
      name: 'next-edit',
      target: {
        name: '//services/next_edit_host/server:image',
        dst: 'next_edit',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv('next-edit-host', telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      volumeMounts: lib.flatten([
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        inferenceClientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        [r.volumeMountDef for r in retrievalObjects if r.volumeMountDef != null],
      ]),
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 2,
          // AU-6834 - increasing memory due to OOM crashes. We will establish a baseline and work to reduce.
          memory: '4Gi',
        },
      },
    };
  local pod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: lib.flatten([
        configMap.podVolumeDef,
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        inferenceClientCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
        [r.podVolumeDef for r in retrievalObjects if r.podVolumeDef != null],
      ]),
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
  lib.flatten([
    clientCert.objects,
    inferenceClientCert.objects,
    serverCert.objects,
    configMap.objects,
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: name,
        namespace: namespace,
        labels: {
          app: name,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else 4,
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: name,
          },
        },
        template: {
          metadata: {
            labels: {
              app: name,
            },
          },
          spec: pod + {
            affinity: affinity,
            tolerations: tolerations,
            priorityClassName: cloudInfo.envToPriorityClass(env),
          },
        },
      },
    },
    services,
    [r.objects for r in retrievalObjects],
    serviceAccountObjects,
    dynamicFeatureFlags.k8s_objects,
    pbd,
  ])

from services.next_edit_host import next_edit_pb2
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.completion_host import completion_pb2
from base.diff_utils.apply_replacements_to_files import (
    FileReplacementError,
    FileReplacementErrorType,
    apply_replacements_to_files,
)
from base.diff_utils.diff_utils import File
from base.blob_names.python.blob_names import B<PERSON>bName, FilePath
from base.logging.secret_logging import SecretLogger
from services.lib.file_retriever.file_retriever import FileRetriever
from base.datasets.recency_info_conversion import from_replacement_text_proto
from collections.abc import Iterable
from services.next_edit_host.server.next_edit_metrics import (
    record_file_reconstruction_event,
    record_file_reconstruction_diff_result,
    FileReconstructionEvent,
    EditEventsWarningType,
)
from base.diff_utils.edit_events import GranularEditEvent, MultiEdits, apply_edits
from base.diff_utils.proto_wrapper import from_proto
from base.blob_names.python.blob_names import get_blob_name
from pprint import pformat
from deepdiff.diff import DeepDiff
from typing import List

import opentelemetry.trace
import structlog

logger = structlog.get_logger(__name__)
tracer = opentelemetry.trace.get_tracer(__name__)


def _print_diff(
    request: NextEditRequest,
    old_reconstruction: List[File | FileReplacementError],
    new_reconstruction: List[File | FileReplacementError],
    safe_logger: SecretLogger,
):
    diff = DeepDiff(old_reconstruction, new_reconstruction, verbose_level=2)
    if diff:
        new_paths = [
            file.path if isinstance(file, File) else None for file in new_reconstruction
        ]
        old_paths = [
            file.path if isinstance(file, File) else None for file in old_reconstruction
        ]

        old_errors = any(
            isinstance(file, FileReplacementError) for file in old_reconstruction
        )
        new_errors = any(
            isinstance(file, FileReplacementError) for file in new_reconstruction
        )

        safe_logger.secret_info(
            f"[reconstruction-diff] Difference between reconstruction methods. {len(old_reconstruction)=} {len(new_reconstruction)=} {new_paths=} {old_paths=} {request.context.request_id=} {old_errors=} {new_errors=}"
        )
        record_file_reconstruction_diff_result(request, "different")
    else:
        record_file_reconstruction_diff_result(request, "same")
        logger.info("[reconstruction-diff]No difference between reconstruction methods")
    diff: dict = DeepDiff(old_reconstruction, new_reconstruction, verbose_level=2)
    safe_logger.secret_info(f"[reconstruction-diff] {pformat(diff, width=350)}")
    # New mechanism needs to at least be as good as the old one.
    # So we remove "iterable_item_added" which signifies cases where new mechanism has more files.
    added_items = diff.pop("iterable_item_added", None)
    if added_items:
        safe_logger.info(
            "[reconstruction-diff] New mechanism has more files than the old one. This is fine."
        )
        paths_new_mechanism_succeeded = []
        for item in added_items.values():
            if isinstance(item, FileReplacementError):
                safe_logger.secret_info(
                    f"[reconstruction-diff] New mechanism had a problem: {item=}"
                )
            else:
                paths_new_mechanism_succeeded.append(item.path)
        if paths_new_mechanism_succeeded:
            safe_logger.secret_info(
                f"[reconstruction-diff] New mechanism succeeded for these files: {paths_new_mechanism_succeeded}"
            )
        safe_logger.secret_info(
            f"[reconstruction-diff] Added items = {pformat(added_items, width=350)}"
        )
    if diff:
        safe_logger.secret_info(
            f"[reconstruction-diff] Diff = {pformat(diff, width=350)}"
        )
    else:
        safe_logger.info(
            "[reconstruction-diff] No more differences between reconstruction methods"
        )


def _reconstruct_using_edit_events(
    request: NextEditRequest,
    missing_blobs: set[BlobName],
    active_file: File,
    safe_logger: SecretLogger,
    file_retriever: FileRetriever,
) -> Iterable[File | FileReplacementError]:
    """Reconstruct the current version of files using edit events."""

    # build a map between Path and Expected Blob Name in replacement text
    logger.info(f"[reconstruct-using-edit-events] {request.context.request_id=}")

    unindexed_edit_events = request.proto.unindexed_edit_events
    unindexed_base_blob_names = request.proto.unindexed_edit_events_base_blob_names
    path_to_expected_blob_name = {
        item.path: item.after_blob_name for item in unindexed_edit_events
    }

    base_blob_names_to_path = {
        item.before_blob_name: item.path
        for item in unindexed_edit_events
        if item.before_blob_name
        in unindexed_base_blob_names  # no need to check "after" because we assume event is removed once indexed
    }

    paths_to_retrieve = set(base_blob_names_to_path.values())

    # in case a file is new, it might not be indexed yet.
    # if a path's first blob name is not in the base blob names, we will add it to the map
    for unindexed_event in unindexed_edit_events:
        if unindexed_event.path not in paths_to_retrieve:
            safe_logger.secret_info(
                f"[reconstruct-using-edit-events] Adding new path to retrieve: {unindexed_event.path=}"
            )
            base_blob_names_to_path[unindexed_event.before_blob_name] = (
                unindexed_event.path
            )
            paths_to_retrieve.add(unindexed_event.path)

    retrieval_missing_blobs = set()
    file_content = file_retriever.retrieve_indexed_files(
        blob_name_to_file_path=base_blob_names_to_path,
        auth_info=request.auth_info,
        missing_blobs=retrieval_missing_blobs,
        request_context=request.context,
        expected=True,
    )
    for blob_name, path in base_blob_names_to_path.items():
        if blob_name in retrieval_missing_blobs:
            safe_logger.secret_warn(
                f"[reconstruct-using-edit-events] Retrieval reported missing blob: {path} {request.context.request_id} {blob_name=}"
            )

    missing_blobs.update(retrieval_missing_blobs)

    granular_diffs: list[GranularEditEvent] = []
    for event in unindexed_edit_events:
        granular_diffs.append(from_proto(event, GranularEditEvent))

    # TODO: use groupby
    path_to_granular_diffs: dict[str, list[GranularEditEvent]] = dict[
        str, list[GranularEditEvent]
    ]()
    for item in granular_diffs:
        path_to_granular_diffs.setdefault(item.path, []).append(item)

    # assert that len of path_to_granular_diffs is same as len of path_to_expected_blob_name
    if len(path_to_granular_diffs) != len(path_to_expected_blob_name):
        safe_logger.secret_warn(
            f"[reconstruct-using-edit-events] Number of paths in granular diffs and expected blob names differ: {len(path_to_granular_diffs)=} {len(path_to_expected_blob_name)=}"
        )

    for path, diffs in path_to_granular_diffs.items():
        first_blob_name = diffs[0].before_blob_name
        uploaded_content = file_content.get(first_blob_name, None)
        if uploaded_content is None:
            record_file_reconstruction_event(
                request,
                FileReconstructionEvent.WARNING_EDIT_EVENTS,
                error_type=EditEventsWarningType.MISSING_CONTENT,
            )
            safe_logger.secret_warn(
                f"[reconstruct-using-edit-events] Missing base content for reconstruction: {path} {request.context.request_id} {first_blob_name=}"
            )
            continue

        edits = [diff.edits for diff in diffs]

        # find all edit events for this path

        apply_result = apply_edits(uploaded_content.contents, edits)
        if apply_result.error:
            is_blocking = _is_blocking_reconstruction_error(
                FileReplacementError(
                    file_blob_name=first_blob_name,
                    file_path=path,
                    error_type=FileReplacementErrorType.EDIT_EVENTS_FAILURE,
                ),
                active_file.blob_name,
                request.proto.scope,
            )
            safe_logger.secret_info(
                f"[reconstruct-using-edit-events] Failed to apply edit events for reconstruction: {path} {request.context.request_id} {apply_result.error}"
            )
            record_file_reconstruction_event(
                request,
                FileReconstructionEvent.ERROR_EDIT_EVENTS,
                is_blocking,
                apply_result.error.error_type,
            )
            if is_blocking:
                yield FileReplacementError(
                    file_blob_name=first_blob_name,
                    file_path=path,
                    error_type=FileReplacementErrorType.EDIT_EVENTS_FAILURE,
                )
            continue

        # check blob name is as expected
        blob_name = get_blob_name(path, apply_result.text)
        if blob_name != path_to_expected_blob_name[path]:
            record_file_reconstruction_event(
                request,
                FileReconstructionEvent.WARNING_EDIT_EVENTS,
                error_type=EditEventsWarningType.FINAL_BLOB_NAME_MISMATCH,
            )
            safe_logger.secret_error(  # this indicates a problem with the edit events mechanism.
                f"[reconstruct-using-edit-events] Blob name mismatch for reconstruction: {path} {request.context.request_id} {blob_name=} {path_to_expected_blob_name[path]=}"
            )
            safe_logger.secret_info(apply_result.text)
            continue

        record_file_reconstruction_event(
            request, FileReconstructionEvent.SUCCESS_EDIT_EVENTS
        )
        safe_logger.secret_info(
            f"[reconstruct-using-edit-events] Successfully reconstructed file: {path} {request.context.request_id}"
        )
        safe_logger.secret_info(f"[reconstruct-using-edit-events] yielding {path=}")
        yield File(path, apply_result.text)


# determine if a reconstruction error is blocking
def _is_blocking_reconstruction_error(
    error: FileReplacementError,
    active_blob_name: BlobName,
    request_scope: next_edit_pb2.NextEditScope.ValueType,
) -> bool:
    # missing expected blob name is never a blocking error
    if error.error_type == FileReplacementErrorType.MISSING_EXPECTED_BLOB_NAME:
        return False

    # current file reconstruction failure is a blocking error
    if error.file_blob_name == active_blob_name:
        return True

    # any file reconstruction failure is a blocking error if the scope is workspace
    if request_scope == next_edit_pb2.NextEditScope.WORKSPACE:
        return True

    return False


def _reconstruct_using_replcement_text(
    request: NextEditRequest,
    missing_blobs: set[BlobName],
    active_file: File,
    safe_logger: SecretLogger,
    file_retriever: FileRetriever,
) -> Iterable[File | FileReplacementError]:
    """This function applies the replacements in recent_changes
    to the indexed files touched by the recent_changes. This is
    crucial to avoid suggesting edits to old versions of files.

    Args:
        request: the request.
        missing_blobs: the set of missing blobs.
        active_file: the file in focus (currently edited) when the request was sent.

    Returns:
        An iterable over files with replacements applied. If we fail to apply
        replacements for a file, we yield a FileReplacementError.

        Note: We only yield blocking errors, allowing the caller to decide what to do in this case.
            For non-blocking errors, we log a warning and continue.

    """

    # filter out replacements that are already present
    replacements: list[completion_pb2.ReplacementText] = [
        change for change in request.proto.recent_changes if not change.present_in_blob
    ]

    # map each path to the blob name of the oldest non-present replacement in that path
    # replacements are assumed to be ordered from oldest -> newest
    path_to_oldest_blob_name: dict[FilePath, BlobName] = {
        replacement.path: replacement.blob_name for replacement in replacements[::-1]
    }

    # file retrieval needs a mapping of blob name to path
    blob_name_to_path: dict[BlobName, FilePath] = {
        blob_name: path for path, blob_name in path_to_oldest_blob_name.items()
    }

    # these files will be the starting points for applying the replacements
    blob_name_to_file: dict[BlobName, File | None] = (
        file_retriever.retrieve_indexed_files(
            blob_name_to_file_path=blob_name_to_path,
            auth_info=request.auth_info,
            missing_blobs=missing_blobs,
            request_context=request.context,
            expected=True,  # we expect all starting point files to be present in the content manager
        )
    )

    # filter out missing files
    files_before_replacements: list[File] = []
    for blob_name, file in blob_name_to_file.items():
        if file is None:
            safe_logger.info(f"Missing starting point file: {blob_name=}")
            safe_logger.secret_info(f"{blob_name_to_path[blob_name]=}")
        else:
            files_before_replacements.append(file)

    # apply the replacements and yield the results
    for file_or_error in apply_replacements_to_files(
        [from_replacement_text_proto(replacement) for replacement in replacements],
        files_before_replacements,
        safe_logger,
    ):
        if isinstance(file_or_error, File):
            record_file_reconstruction_event(
                request, FileReconstructionEvent.SUCCESS_REPLACEMENT_TEXT
            )
            yield file_or_error

        if isinstance(file_or_error, FileReplacementError):
            is_blocking = _is_blocking_reconstruction_error(
                file_or_error, active_file.blob_name, request.proto.scope
            )
            # record all errors
            record_file_reconstruction_event(
                request,
                FileReconstructionEvent.ERROR_REPLACEMENT_TEXT,
                is_blocking,
                file_or_error.error_type,
            )

            # yield the error if it is blocking to let the caller decide what to do
            if is_blocking:
                yield file_or_error

            # if not blocking, log a warning and continue
            else:
                safe_logger.info(f"Ignoring {file_or_error.error_type} for file: ")
                safe_logger.secret_info(
                    f"{file_or_error.file_path=}, {file_or_error.file_blob_name=}"
                )


def get_current_version_of_outdated_files(
    request: NextEditRequest,
    missing_blobs: set[BlobName],
    active_file: File,
    safe_logger: SecretLogger,
    file_retriever: FileRetriever,
) -> Iterable[File | FileReplacementError]:
    """
    Temporary wrapper around different mechanisms to reconstruct files.
    """

    by_replacement_text = _reconstruct_using_replcement_text(
        request, missing_blobs, active_file, safe_logger, file_retriever
    )

    if request.proto.unindexed_edit_events:
        by_edit_events_list: list[File | FileReplacementError] = list(
            _reconstruct_using_edit_events(
                request, missing_blobs, active_file, safe_logger, file_retriever
            )
        )

        by_replacement_text_list: list[File | FileReplacementError] = list(
            by_replacement_text
        )

        _print_diff(request, by_replacement_text_list, by_edit_events_list, safe_logger)
        return by_replacement_text

    return by_replacement_text

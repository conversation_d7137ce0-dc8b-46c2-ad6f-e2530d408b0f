"""Server of a next edit host."""

import argparse
import logging
import os
import pathlib
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass
from typing import Any, Iterable, Iterator, Optional
import queue

import concurrent.futures
import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
from services.inference_host.client.multiplex import MultiplexInferenceStubFactory
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import start_http_server

import base.feature_flags
import base.tracing
import services.lib.grpc.stream_mux.server as stream_mux_server
from services.lib.grpc.stream_mux import stream_mux_pb2
import services.lib.grpc.tls_config.tls_config as tls_config
from base.languages.languages import default_languages
from base.logging.struct_logging import setup_struct_logging
from base.python.grpc import client_options
from base.python.opentelemetry_utils.traced_threadpool import TracedThr<PERSON><PERSON><PERSON><PERSON>xecutor
from base.python.signal_handler.signal_handler import <PERSON>ful<PERSON>ignalHand<PERSON>
from services.lib.retrieval import retriever_factory
from services.lib.retrieval.retriever_request_insight_builder import (
    RetrieverRequestInsightBuilder,
)
from services.lib.retrieval.retriever import Retriever
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host import infer_pb2_grpc
from services.inference_host.client import inference_host_client
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2, next_edit_pb2_grpc
from google.rpc import status_pb2
from services.next_edit_host.server.next_edit_metrics import (
    record_find_missing_count,
    record_find_missing_latecy,
    record_first_change_latency,
    record_request,
    record_request_cancelled,
    record_request_exception,
    record_suggestion_interval,
    record_suggestion_latency,
)
from services.next_edit_host.server.handler import NextEditHandlerProtocol
from services.next_edit_host.server.next_edit_handler import create_next_edit_handler
from services.next_edit_host.server import post_processing
from services.next_edit_host.server.next_edit_types import NextEditRequest
from services.next_edit_host.server.next_edit_utils import (
    NextEditCancelledException,
    CancellationReason,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.token_exchange.client import client as token_exchange_client
from services.working_set.client.client import get_working_set_client, WorkingSetClient

log = structlog.get_logger()
tracer = base.tracing.setup_opentelemetry()


# feature flag to enable multiplexing over multiple inference hosts
#
# Example
# {"default": 0.5, "gsc": 0.5}
#
# This assumes that the inference hosts are named "default" and "gsc"
_NEXT_EDIT_MULTIPLEX = base.feature_flags.StringFlag("next_edit_inferer_multiplex", "")

_GRPC_QUEUE_TIMEOUT_SECONDS = 5


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for a next edit server."""

    port: int

    # name of the model.
    #
    # this has to match the name of the model instance config
    model_name: str

    secret_logs_enabled: bool

    # endpoints for the edit generation hosts
    # key is the name of the host, value is the endpoint
    #
    # , e.g.
    # {
    #   "default": "infer-wizard-svc.central:50051",
    #   "gsc": "infer-wizard.central.t.us-central1-gsc.prod.augmentcode.com:50051"
    # }
    edit_gen_host_endpoints: dict[str, str]
    edit_gen_ranker_host_endpoints: Optional[dict[str, str]]
    description_host_endpoints: Optional[dict[str, str]]
    content_manager_endpoint: str
    working_set_endpoint: str | None

    generation_retrieval: retriever_factory.RetrievalConfig
    location_retrieval: retriever_factory.RetrievalConfig
    handler_type: str
    handler_config: dict

    auth_config: AuthConfig

    max_handler_workers: int = 8
    """Maximum number of workers for the handlers."""
    max_server_workers: int = 64  # increased from 32
    """Maximum number of workers for the server."""

    feature_flags_sdk_key_path: Optional[str] = None

    client_mtls: Optional[tls_config.ClientConfig] = None
    central_client_mtls: Optional[tls_config.ClientConfig] = None
    server_mtls: Optional[tls_config.ServerConfig] = None

    low_quality_filter: Optional[post_processing.LowQualityFilterConfig] = None

    shutdown_grace_period_s: float = 20.0


class NextEditServices(next_edit_pb2_grpc.NextEditServicer):
    """Services to implement the next edit service API."""

    def __init__(
        self,
        config: Config,
        handler: NextEditHandlerProtocol,
        working_set_client: WorkingSetClient,
        service_auth: ServiceTokenAuth,
    ):
        self.config = config
        self.handler = handler
        self.working_set_client = working_set_client
        self.token_auth = service_auth
        self.background_pool = TracedThreadPoolExecutor(
            thread_name_prefix="background-notify-"
        )

    def NextEditStream(
        self,
        request: next_edit_pb2.NextEditRequest,
        context: grpc.ServicerContext,
    ) -> Iterator[next_edit_pb2.NextEditResponse]:
        """Request for a stream of next edit suggestions.

        Args:
            request: The next edit request.
            context: The gRPC context.

        Returns:
            The next edit suggestions
        """

        # Extract context and auth info
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        try:
            yield from self._NextEditStream(request, request_context, auth_info)
        except grpc.RpcError as ex:
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
        except NextEditCancelledException:
            context.cancel()

    def NextEditSession(
        self,
        request_iterator: Iterable[next_edit_pb2.SessionRequest],
        context: grpc.ServicerContext,
    ) -> Iterator[next_edit_pb2.SessionResponse]:
        """Request for a stream of next edit suggestions.

        Args:
            request_iterator: The next edit request stream.
            context: The gRPC context.

        Returns:
            The next edit suggestions
        """
        # Extract stream context
        stream_context = RequestContext.from_grpc_context(context)

        # create a separate thread pool for the generator requests
        generator_pool = TracedThreadPoolExecutor(
            max_workers=8, thread_name_prefix="next-edit-generator-"
        )

        # Make a shared response queue for all the generators
        response_queue: queue.Queue[next_edit_pb2.SessionResponse | None] = (
            queue.Queue()
        )

        @stream_mux_server.open_telemetry_span(tracer, "NextEditSession/single_request")
        def run_single_request(
            request: next_edit_pb2.SessionRequest,
        ) -> None:
            """
            Run a single request.

            It will get responses from _NextEditStream and add it to the shared response queue.
            """
            code: grpc.StatusCode = grpc.StatusCode.UNKNOWN
            message: str = ""

            request_context = stream_mux_server.extract_context(
                stream_context, request.context
            )

            try:
                auth_info = self.token_auth.validate_access(
                    [],
                    request_context.auth_token,
                    "/next_edit_host.NextEdit/NextEditSession",
                )

                # Check if the request.request is the empty
                # This means it is the initial dummy request
                if not request.HasField("request"):
                    resp = next_edit_pb2.SessionResponse(
                        context=stream_mux_pb2.MuxedResponse(
                            request_id=request_context.request_id,
                            routing_id=request.context.routing_id,
                            is_stream_end=False,
                        )
                    )
                    resp.context.status.CopyFrom(  # pylint: disable=no-member # type: ignore
                        status_pb2.Status(code=grpc.StatusCode.OK.value[0], message="")  # pylint: disable=no-member # type: ignore
                    )
                    response_queue.put(resp)
                    return

                for response in self._NextEditStream(
                    request.request, request_context, auth_info
                ):
                    curr_resp = next_edit_pb2.SessionResponse(
                        context=stream_mux_pb2.MuxedResponse(
                            request_id=request_context.request_id,
                            routing_id=request.context.routing_id,
                            is_stream_end=False,
                        )
                    )
                    code = grpc.StatusCode.OK
                    curr_resp.response.CopyFrom(response)
                    curr_resp.context.status.CopyFrom(  # pylint: disable=no-member # type: ignore
                        status_pb2.Status(code=code.value[0], message=message)  # pylint: disable=no-member # type: ignore
                    )
                    response_queue.put(curr_resp)
            except grpc.RpcError as ex:
                code = ex.code()  # pylint: disable=no-member # type: ignore
                message = ex.details() or ""  # pylint: disable=no-member # type: ignore
            except NextEditCancelledException:
                code = grpc.StatusCode.CANCELLED
            except Exception:  # pylint: disable=broad-exception-caught
                code = grpc.StatusCode.UNKNOWN
            finally:
                # Finally send an EOS
                resp = next_edit_pb2.SessionResponse(
                    context=stream_mux_pb2.MuxedResponse(
                        request_id=request_context.request_id,
                        routing_id=request.context.routing_id,
                        is_stream_end=True,
                    )
                )
                resp.context.status.CopyFrom(  # pylint: disable=no-member # type: ignore
                    status_pb2.Status(code=code.value[0], message=message)  # pylint: disable=no-member # type: ignore
                )
                stream_mux_server.ensure_backwards_compatibility(resp)
                response_queue.put(resp)

        request_iterator = iter(request_iterator)

        # Long polls for new requests and submits each request to the generator pool.
        def receive_request():
            futures = []
            for req in request_iterator:
                futures.append(generator_pool.submit(run_single_request, req))
            # Ensure that all the futures are done before we exit
            concurrent.futures.wait(futures)
            response_queue.put(None)

        # We need one additional thread receiving requests from the client
        # Start the request receiver thread
        receiver_thread = threading.Thread(target=receive_request)
        receiver_thread.start()

        # Run a loop to keep checking for responses
        while True:
            try:
                res = response_queue.get()
                if res is None:
                    break
                yield res
            except grpc.RpcError as e:
                # This is an errors with the receiver, since the others are handled in
                # run_single_request, we can just return here
                # Typically RpcError on the client dropping
                log.warn(f"Error while getting response from response queue: {e}")
                break
            except Exception as e:
                log.error(f"Error while getting response from response queue: {e}")
                raise e

        # Cleanup
        generator_pool.shutdown()
        receiver_thread.join()

    def _NextEditStream(
        self,
        request: next_edit_pb2.NextEditRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> Iterator[next_edit_pb2.NextEditResponse]:
        request_start_time = time.time()
        last_suggestion_time = None
        status_code = grpc.StatusCode.UNKNOWN
        next_edit_request = NextEditRequest(
            proto=request,
            context=request_context,
            auth_info=auth_info,
        )

        try:
            # Bind the context logging to the request context and auth info
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            time_in_queue = (
                request_start_time
                - next_edit_request.proto.client_created_at.ToDatetime().timestamp()
            )
            if (
                next_edit_request.proto.client_created_at.ToDatetime().timestamp() != 0
                and time_in_queue > _GRPC_QUEUE_TIMEOUT_SECONDS
            ):
                raise NextEditCancelledException(
                    f"Request waiting in queue for {time_in_queue:.2f}s.",
                    CancellationReason.GRPC_QUEUE_TIMEOUT,
                )

            log.info("next edit content: model_name=%s", request.model_name)

            # register workingset in background
            self.background_pool.submit(
                self.working_set_client.register_working_set,
                request.blobs,
                request_context,
            )
            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"next-edit-{request_context.request_id[-8:]}-",
            ) as executor:
                record_request(next_edit_request)
                generator = self.handler.next_edit_stream(
                    request=next_edit_request,
                    executor=executor,
                )
                first_change_found = False
                last_suggestion_time = time.time()
                for response in generator:
                    has_change = (
                        response.suggested_edit.suggested_code
                        != response.suggested_edit.existing_code
                    )
                    record_suggestion_latency(
                        next_edit_request,
                        time.time() - request_start_time,
                        has_change,
                    )
                    if last_suggestion_time is not None:
                        record_suggestion_interval(
                            next_edit_request,
                            time.time() - last_suggestion_time,
                        )
                    last_suggestion_time = time.time()
                    if has_change:
                        if not first_change_found:
                            record_first_change_latency(
                                next_edit_request,
                                time.time() - request_start_time,
                            )
                            first_change_found = True
                    yield response.to_next_edit_response_proto()

            status_code = grpc.StatusCode.OK
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.CANCELLED:
                log.info("Next Edit cancelled")
                record_request_cancelled(
                    next_edit_request, CancellationReason.GRPC_CANCEL
                )
            else:
                log.error("Next Edit failed: %s", ex)
                log.exception(ex)
            raise
        except NextEditCancelledException as ex:
            log.info("Next Edit force cancelled: %s", ex)
            record_request_cancelled(next_edit_request, ex.reason)
            raise
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("Next Edit failed: %s", ex)
            log.exception(ex)
            record_request_exception(next_edit_request, ex.__class__.__name__)
            raise

    def FindMissing(
        self,
        request: next_edit_pb2.FindMissingRequest,
        context: grpc.ServicerContext,
    ) -> next_edit_pb2.FindMissingResponse:
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            log.info(
                "find_missing content: model_name=%s, blob_count=%d",
                request.model_name,
                len(request.blob_names),
            )

            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=self.config.max_handler_workers,
                thread_name_prefix=f"find-missing-{request_context.request_id[-8:]}-",
            ) as executor:
                output = self.handler.find_missing(
                    request=request,
                    request_context=request_context,
                    executor=executor,
                )

            response = next_edit_pb2.FindMissingResponse()
            response.missing_blob_names.extend(output.missing_blob_names)
            response.nonindexed_blob_names.extend(output.nonindexed_blob_names)

            record_find_missing_count(
                request.model_name,
                request_context.request_source,
                auth_info.tenant_name,
                len(request.blob_names),
                len(output.missing_blob_names),
                len(output.nonindexed_blob_names),
            )

            status_code = grpc.StatusCode.OK
            return response
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.CANCELLED:
                log.error("FindMissing cancelled")
            else:
                log.error("FindMissing failed: %s", ex)
            log.exception(ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            raise  # Should not get here, context.abort should have raised
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("FindMissing failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            record_find_missing_latecy(
                request.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.tenant_name,
                latency,
            )


def _get_handler(
    config: Config,
    edit_gen_client: inference_host_client.InfererClient,
    edit_gen_ranker_client: inference_host_client.InfererClient | None,
    description_gen_client: inference_host_client.InfererClient | None,
    generation_retriever: Retriever,
    location_retriever: Retriever,
    content_manager_client: ContentManagerClient,
    ri_publisher: RequestInsightPublisher,
    low_quality_filter: post_processing.LowQualityFilter | None = None,
) -> NextEditHandlerProtocol:
    if config.handler_type == "NextEditHandler":
        if "POD_NAMESPACE" not in os.environ:
            raise ValueError("POD_NAMESPACE environment variable must be set.")
        namespace = os.environ["POD_NAMESPACE"]
        return create_next_edit_handler(
            config=config.handler_config,
            edit_gen_client=edit_gen_client,
            edit_gen_ranker_client=edit_gen_ranker_client,
            description_gen_client=description_gen_client,
            namespace=namespace,
            ri_publisher=ri_publisher,
            content_manager_client=content_manager_client,
            generation_retriever=generation_retriever,
            location_retriever=location_retriever,
            secret_logs_enabled=config.secret_logs_enabled,
            low_quality_filter=low_quality_filter,
        )
    else:
        raise ValueError(f"Unsupported handler type: {config.handler_type=}")


def get_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


def _get_inference_stub_factory(
    inferer_endpoints: dict[str, str],
    credentials: Optional[grpc.ChannelCredentials],
    model_name: str,
) -> inference_host_client.InferenceStubFactoryProtocol:
    """Returns a client to the inference host or inference hosts."""

    def get_stub() -> infer_pb2_grpc.InfererStub:
        rpc_clients: dict[str, infer_pb2_grpc.InfererStub] = {}
        for name, endpoint in inferer_endpoints.items():
            options = client_options.get_grpc_client_options(
                client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
            )

            rpc_client = inference_host_client.create_inference_stub(
                endpoint, credentials=credentials, options=options
            )

            rpc_clients[name] = rpc_client
        if "default" not in rpc_clients:
            raise ValueError("No default client")

        if len(rpc_clients) > 1:
            multiplex = MultiplexInferenceStubFactory(
                rpc_clients=rpc_clients,
                model_name=model_name,
                feature_flag=_NEXT_EDIT_MULTIPLEX,
            )
            return multiplex()  # selects the stub based on the feature flag
        else:
            return list(rpc_clients.values())[0]

    return get_stub


def run(
    config: Config,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    content_manager_client = get_content_manager_client(config)

    ri_builder = RetrieverRequestInsightBuilder(ri_publisher)
    generation_retriever = retriever_factory.create_retriever(
        config.generation_retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        ri_builder,
        search_timeout_ms=5000,
    )
    location_retriever = retriever_factory.create_retriever(
        config.location_retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        ri_builder,
        search_timeout_ms=5000,
    )

    central_creds = tls_config.get_client_tls_creds(config.central_client_mtls)
    edit_gen_client = inference_host_client.InfererClient(
        _get_inference_stub_factory(
            config.edit_gen_host_endpoints,
            model_name=config.model_name,
            credentials=central_creds,
        ),
        sticky_session_seconds=30.0,
        sticky_session_count=64,
        use_stub_cycling=True,
    )
    description_gen_client = (
        inference_host_client.InfererClient(
            _get_inference_stub_factory(
                config.description_host_endpoints,
                credentials=central_creds,
                model_name=config.model_name,
            ),
            sticky_session_seconds=30.0,
            sticky_session_count=64,
            use_stub_cycling=True,
        )
        if config.description_host_endpoints is not None
        else None
    )
    edit_gen_ranker_client = (
        inference_host_client.InfererClient(
            _get_inference_stub_factory(
                config.edit_gen_ranker_host_endpoints,
                credentials=tls_config.get_client_tls_creds(config.central_client_mtls),
                model_name=config.model_name,
            ),
            sticky_session_seconds=30.0,
            sticky_session_count=64,
            use_stub_cycling=True,
        )
        if config.edit_gen_ranker_host_endpoints is not None
        else None
    )

    handler = _get_handler(
        config=config,
        edit_gen_client=edit_gen_client,
        edit_gen_ranker_client=edit_gen_ranker_client,
        description_gen_client=description_gen_client,
        generation_retriever=generation_retriever,
        location_retriever=location_retriever,
        content_manager_client=content_manager_client,
        ri_publisher=ri_publisher,
        low_quality_filter=(
            post_processing.LowQualityFilter(config.low_quality_filter)
            if config.low_quality_filter
            else None
        ),
    )

    # Load the default languages to ensure they are cached before we receive the first request.
    _ = default_languages()

    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)
    working_set_client = get_working_set_client(config)

    thread_pool = ThreadPoolExecutor(
        max_workers=config.max_server_workers, thread_name_prefix="server-"
    )
    server = grpc.server(
        thread_pool,
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    next_edit_pb2_grpc.add_NextEditServicer_to_server(
        NextEditServices(config, handler, working_set_client, service_auth), server
    )
    service_names = (
        next_edit_pb2.DESCRIPTOR.services_by_name["NextEdit"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)

    if server_credentials is not None:
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")

    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # type: ignore # pylint: disable=no-member
        config_file.read_text(),
    )


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)

    logging.info("Config %s", config)

    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)

    context = base.feature_flags.Context.setup(path)
    base.feature_flags.set_global_context(context)

    ri_publisher = RequestInsightPublisher.create_from_args(args)

    run(config, namespace, ri_publisher, handler.get_shutdown_event())


if __name__ == "__main__":
    main()

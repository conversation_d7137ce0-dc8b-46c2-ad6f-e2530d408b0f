"""A Python client library call a next edit service."""

import logging
from typing import Iterable, Optional

import grpc

from base.python.grpc import client_options
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2, next_edit_pb2_grpc


def setup_stub(
    endpoint: str, credentials: Optional[grpc.ChannelCredentials]
) -> next_edit_pb2_grpc.NextEditStub:
    """Setup the client stub for a next edit service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create()
        )
    stub = next_edit_pb2_grpc.NextEditStub(channel)
    return stub


class NextEditClient:
    """Class to call next edit APIs remotely."""

    def __init__(self, endpoint: str, credentials: Optional[grpc.ChannelCredentials]):
        self.stub = setup_stub(endpoint, credentials)

    def next_edit_stream(
        self,
        request: next_edit_pb2.NextEditRequest,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> Iterable[next_edit_pb2.NextEditResponse]:
        """Predict the next edit."""
        response = self.stub.NextEditStream(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

    def find_missing(
        self,
        request: next_edit_pb2.FindMissingRequest,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> next_edit_pb2.FindMissingResponse:
        """Find missing blobs."""
        response = self.stub.FindMissing(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

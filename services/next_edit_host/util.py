"""A small tool to issue requests against the next edit host API."""

import argparse
import json
import logging
import sys
from pathlib import Path

import grpc
import pydantic
from google.protobuf.json_format import ParseDict, MessageToJson
from base.blob_names import blob_names_pb2
from base.logging.console_logging import setup_console_logging
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.client import NextEditClient

import os

base_dir = (
    os.environ["BUILD_WORKSPACE_DIRECTORY"]
    if "BUILD_WORKSPACE_DIRECTORY" in os.environ
    else "."
)


def _get_client(args) -> NextEditClient:
    if args.insecure:
        credentials = None
    else:
        if not args.ca_cert or not args.client_key or not args.client_cert:
            logging.error(
                "--ca-cert, --client-key, --client-cert need to be set "
                "unless --insecure is used"
            )
            sys.exit(1)
        credentials = grpc.ssl_channel_credentials(
            root_certificates=args.ca_cert.read_bytes(),
            private_key=args.client_key.read_bytes(),
            certificate_chain=args.client_cert.read_bytes(),
        )
    rpc_client = NextEditClient(args.endpoint, credentials=credentials)
    return rpc_client


def _next_edit(client: NextEditClient, args):
    request_pb = next_edit_pb2.NextEditRequest()
    request_file_full_path = Path(base_dir) / args.request_file
    ParseDict(
        json.loads(request_file_full_path.read_text(encoding="utf-8")), request_pb
    )

    # TODO: replace the fake blob names with real ones later.

    auth_token = None
    if args.auth_token_file:
        auth_token_full_path = Path(base_dir) / args.auth_token_file
        auth_token = pydantic.SecretStr(
            auth_token_full_path.read_text(encoding="utf-8").strip()
        )
    request_context = RequestContext.create(auth_token=auth_token)
    resp = client.next_edit_stream(request_pb, request_context=request_context)

    for r in resp:
        # Fine to log this to the console
        logging.info("\n%s", MessageToJson(r, indent=2))

    return 0


def main():
    """Main entrypoint."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--endpoint", required=True, help="Endpoint to connect to, e.g. localhost:50051"
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="If true, the request will be in plaintext instead of a secure connection",
    )
    parser.add_argument("--ca-cert", type=Path, help="Path to the ca certificate")
    parser.add_argument("--client-key", type=Path, help="Path to the TLS client key")
    parser.add_argument(
        "--client-cert", type=Path, help="Path to the TLS client certificate"
    )

    subparsers = parser.add_subparsers(help="Commands", required=True)

    next_edit_parser = subparsers.add_parser("next_edit", help="Find you next edit")
    next_edit_parser.set_defaults(action="next_edit")
    next_edit_parser.add_argument(
        "--request-file",
        type=Path,
        required=True,
        help="Path to a json file with the request content",
    )

    next_edit_parser.add_argument(
        "--auth-token-file",
        type=Path,
        required=False,
        help="Path to file containing authentication token",
    )

    args = parser.parse_args()
    client = _get_client(args)
    if args.action == "next_edit":
        sys.exit(_next_edit(client, args))
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()

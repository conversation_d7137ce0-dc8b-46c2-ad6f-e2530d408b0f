"""Tests for LRUDict."""

from services.next_edit_host.lru_dict import LRUDict
import pytest


def test_basic_insertion():
    lru = LRUDict(max_size=2)
    lru["a"] = 1
    assert lru["a"] == 1
    assert len(lru) == 1


def test_max_size_enforcement():
    lru = LRUDict(max_size=2)
    lru["a"] = 1
    lru["b"] = 2
    lru["c"] = 3  # This should evict 'a'

    assert len(lru) == 2
    assert "a" not in lru
    assert "b" in lru
    assert "c" in lru
    assert lru["b"] == 2
    assert lru["c"] == 3


def test_access_updates_order():
    lru = LRUDict(max_size=2)
    lru["a"] = 1
    lru["b"] = 2

    # Access 'a', making it most recent
    _ = lru["a"]

    # Add new item, should evict 'b' instead of 'a'
    lru["c"] = 3

    assert "a" in lru
    assert "b" not in lru
    assert "c" in lru


def test_update_existing_key():
    lru = LRUDict(max_size=2)
    lru["a"] = 1
    lru["b"] = 2
    lru["a"] = 3  # Update existing key

    assert lru["a"] == 3
    assert len(lru) == 2

    # Add new item, should evict 'b' since 'a' was recently updated
    lru["c"] = 4
    assert "a" in lru
    assert "b" not in lru
    assert "c" in lru


def test_zero_size_dict():
    lru = LRUDict(max_size=0)
    with pytest.raises(KeyError):
        lru["a"] = 1

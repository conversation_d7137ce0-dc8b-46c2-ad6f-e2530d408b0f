syntax = "proto3";
package next_edit;

import "base/blob_names/blob_names.proto";
import "base/diff_utils/edit_events.proto";
import "google/protobuf/timestamp.proto";
import "services/completion_host/completion.proto"; // should probably move out
import "services/lib/grpc/stream_mux/stream_mux.proto";

service NextEdit {
  rpc NextEditStream(NextEditRequest) returns (stream NextEditResponse);
  rpc NextEditSession(stream SessionRequest) returns (stream SessionResponse);
  rpc FindMissing(FindMissingRequest) returns (FindMissingResponse);
}

message FileLocation {
  // The path of the file.
  string path = 1 [debug_redact = true];

  // Based on current state of the file. (inclusive)
  uint32 line_start = 2;

  // Based on current state of the file. (exclusive)
  uint32 line_end = 3;
}

message FileRegion {
  // The path of the file.
  string path = 1 [debug_redact = true];

  // Based on current state of the file. (inclusive)
  uint32 char_start = 2;

  // Based on current state of the file. (exclusive)
  uint32 char_end = 3;
}

enum DiagnosticSeverity {
  // Something not allowed by the rules of a language or other means.
  ERROR = 0;
  // Something suspicious but allowed.
  WARNING = 1;
  // Something to inform about but not a problem.
  INFORMATION = 2;
  // Something to hint to a better way of doing it, like proposing a refactoring.
  HINT = 3;
}

message Diagnostic {
  FileLocation location = 1;

  string message = 2 [debug_redact = true];

  DiagnosticSeverity severity = 3;

  // The current version the IDE sees but might not be indexed yet
  optional string current_blob_name = 4;

  // The latest indexed blob name
  optional string blob_name = 5;

  // Char offset within the blob of the start of the diagnostic.
  optional uint32 char_start = 6;

  // Char offset within the blob of the end of the diagnostic.
  optional uint32 char_end = 7;
}

enum ChangeType {
  ADDED = 0;
  DELETED = 1;
  MODIFIED = 2;
  RENAMED = 3;
}

message WorkingDirectoryChange {
  optional string before_path = 1 [debug_redact = true];

  optional string after_path = 2 [debug_redact = true];

  ChangeType change_type = 3;

  // The before version in HEAD.
  optional string head_blob_name = 4;

  // The indexed version in the current workspace.
  optional string indexed_blob_name = 5;

  // The current version the IDE sees but might not be indexed yet.
  optional string current_blob_name = 6;
}

message VCSChange {
  repeated WorkingDirectoryChange working_directory_changes = 1;
}

message CharRange {
  // The character start of the range (inclusive).
  uint32 start = 1;

  // The character end of the range (exclusive).
  uint32 stop = 2;
}

// A span of text in a diff, relating the original and replacement ranges of text.
message DiffSpan {
  // The character range of the original text.
  CharRange original = 1;

  // The character range of the updated text.
  CharRange updated = 2;
}

message ScoredFileHunk {
  // The path of the file.
  string path = 1 [debug_redact = true];

  string blob_name = 2;

  // character offset within the blob of the start of edit
  // Based on last known state of the file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  uint32 char_start = 3;

  // character offset within the blob of the end of the edit (exclusive).
  // Based on last known state of the file.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  uint32 char_end = 4;

  // The existing code.
  string existing_code = 5 [debug_redact = true];

  // The suggested code.
  string suggested_code = 6 [debug_redact = true];

  float localization_score = 7;

  float editing_score = 8;

  // truncation character for the suggested edit
  optional int32 truncation_char = 9;

  // Diff spans for edit suggestion, both changed and unchanged spans
  repeated DiffSpan diff_spans = 10;

  // Description of suggested code change.
  string change_description = 11 [debug_redact = true];

  // Globally Unique suggestion ID
  string suggestion_id = 12;

  // Default threshold for `editing_score`. If `editing_score` > `editing_score_threshold`,
  // the suggestion is considered of low quality.
  float editing_score_threshold = 13;

  // Description of suggested code change in markdown format.
  string markdown_change_description = 14;
}

enum NextEditMode {
  UNKNOWN_NEXT_EDIT_MODE = 0;
  // Background NextEdit mode with no user trigger
  BACKGROUND = 1;
  // Foreground NextEdit mode with user trigger
  FOREGROUND = 2;
  // Forced foreground mode with user trigger
  FORCED = 3;
}

enum NextEditScope {
  UNKNOWN_NEXT_EDIT_SCOPE = 0;
  // Return an edit immediately around the cursor or selected text region, if found.
  // The selected region may be expanded in the backend. Returns 0-1 results.
  CURSOR = 1;
  // Return edits found in the current file. Each edit is a single "hunk". Returns
  // 0-N results.
  FILE = 2;
  // Return edits found in the current workspace. Each edit is a single "hunk". Returns
  // 0-N results.
  WORKSPACE = 3;
}

message NextEditRequest {
  string model_name = 1;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  uint32 sequence_id = 2;

  // programming laguage that the prompt is in.
  //
  // the name should be one of the language names given to the client
  // in the lanuages field of the `Model`.
  // if not set (or empty), the server might detect a language based on the path
  // or use an undefined default.
  optional string lang = 3;

  // the message to be used for the prompt
  string instruction = 4 [debug_redact = true];

  // Blobs to include in the retrieval corpus, specified in the compact delta form.
  base.blob_names.Blobs blobs = 5;

  // Recent events from the client.
  repeated completion.ReplacementText recent_changes = 6 [debug_redact = true];

  // The changes made to the files since the last commit
  VCSChange vcs_change = 7 [deprecated = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  optional string path = 8 [debug_redact = true];

  // blob name of the blob in which the edit is requested, if known.
  optional string blob_name = 9;

  // character offset within the blob of the start of the selected text, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 selection_begin_char = 10;

  // character offset within the blob of the end of the selected text, if known
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  optional int32 selection_end_char = 11;

  // the prefix prompt at which the edit should be inserted
  string prefix = 12 [debug_redact = true];

  // the text selected in the current buffer
  string selected_text = 13 [debug_redact = true];

  // the text after the selcted text
  string suffix = 14 [debug_redact = true];

  repeated Diagnostic diagnostics = 15;

  // Next edit mode. Determines service parameters for this request.
  NextEditMode mode = 16;

  // Next edit scope. Determines the scope of edits returned for this request.
  NextEditScope scope = 18;

  // Response change probability override.
  // Change suggestions will not be returned if probability too low.
  // When not provided, configured probability threshold for the mode will be used.
  optional float change_probability_override = 17;

  // More granular events representing the most recent edits.
  repeated base.diff_utils.GranularEditEvent edit_events = 19;

  // A list of locations that the system should not return in the result.
  repeated FileRegion blocked_locations = 20;

  /** The API version used by the client.

     See the change log in `services/api_proxy/public_api.proto`.
  */
  uint32 api_version = 21;

  // Client-side request creation timestamp used for hindsight training analysis.
  // Note: This is when the request was created, not when it was sent.
  optional google.protobuf.Timestamp client_created_at = 22;

  // Unindexed changes
  //
  // Use this fields `unindexed_edit_events` + `unindexed_edit_events_base_blob_names` to reconstruct current state of files
  repeated base.diff_utils.GranularEditEvent unindexed_edit_events = 23;
  repeated string unindexed_edit_events_base_blob_names = 24;
}

message NextEditResponse {
  ScoredFileHunk suggested_edit = 1;

  repeated string unknown_blob_names = 2;

  bool checkpoint_not_found = 3;
}

message FindMissingRequest {
  // The model to probe for missing and non-indexed blobs.
  string model_name = 1;

  // The collection of blob names.
  repeated string blob_names = 2;
}

message FindMissingResponse {
  // (deprecated, call ContentManager instead) The subset of blob_names that are completely unknown.
  repeated string missing_blob_names = 1 [deprecated = true];

  // The subset of blob names that are not indexed for the given model.
  repeated string nonindexed_blob_names = 2;
}

message SessionRequest {
  stream_mux.MuxedRequest context = 1;
  // The request.
  NextEditRequest request = 2;
}

message SessionResponse {
  stream_mux.MuxedResponse context = 1;
  // The response.
  NextEditResponse response = 2;
}

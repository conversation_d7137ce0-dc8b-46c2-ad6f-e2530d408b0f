from collections import OrderedDict
from typing import TypeVar, Generic

K = TypeVar("K")  # Key type
V = TypeVar("V")  # Value type


class LRUDict(OrderedDict, Generic[K, V]):
    """A dictionary-like container that stores up to `max_size` items.

    If an item is added when the cache is at capacity, the oldest item is discarded to make space.
    """

    def __init__(self, max_size):
        self.max_size = max_size
        super().__init__()

    def __setitem__(self, key: K, value: V):
        if self.max_size <= 0:
            raise KeyError("Cannot add items to a zero-sized LRUDict")

        if key in self:
            # Move the existing key to the end to show that it was recently used
            del self[key]
        elif len(self) >= self.max_size:
            # Remove the oldest item
            self.popitem(last=False)
        super().__setitem__(key, value)

    def __getitem__(self, key: K) -> V:
        value = super().__getitem__(key)
        # Move the accessed item to the end
        del self[key]
        super().__setitem__(key, value)
        return value

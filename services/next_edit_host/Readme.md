# Next Edit Host

The Next Edit Host predicts future code changes.

### Local testing

To test the next edit host locally, you can run the following commands:

```bash
kubectl port-forward deployment/next-edit-raven-1b-mock-next-edit 50051:50051 -n "dev-$USER"
```

```bash
bazel run //services/next_edit_host:util -- --endpoint "127.0.0.1:50051" --insecure next_edit \
--request-file services/next_edit_host/next_edit_request_example.json
```

Note that you will need a JWT token. Follow [this guide](https://www.notion.so/Manually-connecting-to-prod-services-1a6d2843f64c4e46aab7af5c80847ac9)

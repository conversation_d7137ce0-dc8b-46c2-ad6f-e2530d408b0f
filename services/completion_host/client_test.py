"""Tests for the inference_host_client module."""

import queue
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from unittest.mock import create_autospec
from typing import Iterable

import grpc
import pydantic
import pytest

from services.completion_host.client import CompletionStreamClient, setup_stub
import services.completion_host.completion_pb2 as completion_pb2
from services.completion_host.completion_pb2 import (
    CompletionRequest,
    CompletionResponse,
    SessionRequest,
    SessionResponse,
)
from services.lib.grpc.stream_mux import stream_mux_pb2
from services.lib.request_context.request_context import RequestContext


def test_completion_stream_client():
    # Enable the test to deliver results from the "server"
    # at time and order of our choosing
    response_queue = queue.SimpleQueue()

    def response_gen() -> Iterable:
        while True:
            resp = response_queue.get()
            if resp is None:
                return
            yield resp

    stub = setup_stub("http://localhost:8000", credentials=None)
    mock = create_autospec(stub.CompleteSession, return_value=response_gen())
    stub.CompleteSession = mock

    client_request_context = RequestContext.create()
    client = CompletionStreamClient(stub, client_request_context, 30.0)

    # Generate 3 more request IDs, but sharing the same session
    req_contexts = [
        RequestContext.create(
            request_session_id=client_request_context.request_session_id,
            timeout=i + 1,
            auth_token=pydantic.SecretStr("token"),
        )
        for i in range(3)
    ]
    assert len(set([rc.request_id for rc in req_contexts])) == 3

    requests = mock.call_args.args[0]
    assert mock.call_args.kwargs["timeout"] == 30.0
    assert mock.call_args.kwargs["metadata"][0][1] == client_request_context.request_id

    tpool = ThreadPoolExecutor(3)
    futures = []
    routing_ids = []
    for i in range(3):
        futures.append(
            tpool.submit(
                client.complete,
                CompletionRequest(model_name=f"model{i}"),
                request_context=req_contexts[i],
                timeout=i + 1,
            )
        )
        # Wait to see the request from the mock input to ensure the request is waiting for
        # a response. We do this between submissions to control the order of submissions to
        # the "server".
        request = next(requests)
        assert isinstance(request, SessionRequest)
        assert request.context.request_id == req_contexts[i].request_id
        assert request.context.request_session_id == req_contexts[i].request_session_id
        # Some time has passed since we created the request context with timeout of 'i+1' seconds
        assert i < request.context.timeout.ToNanoseconds() / 10**9 <= i + 1
        assert request.request.model_name == f"model{i}"
        assert request.context.routing_id
        routing_ids.append(request.context.routing_id)

    # All requests are still blocked waiting for a response
    assert all(not f.done() for f in futures)

    # Send responses in different order than requests were made
    response_queue.put(
        SessionResponse(
            context=stream_mux_pb2.MuxedResponse(
                routing_id=routing_ids[1],
                request_id=req_contexts[1].request_id,
                status_code=stream_mux_pb2.StatusCode.CANCELLED,
            ),
        )
    )
    with pytest.raises(grpc.RpcError) as exc:
        futures[1].result()
    assert exc.value.code() == grpc.StatusCode.CANCELLED
    assert not futures[0].done()
    assert not futures[2].done()

    response_queue.put(
        SessionResponse(
            context=stream_mux_pb2.MuxedResponse(
                routing_id=routing_ids[0],
                request_id=req_contexts[0].request_id,
                status_code=stream_mux_pb2.StatusCode.OK,
            ),
            response=CompletionResponse(text="your code has arrived"),
        )
    )
    resp = futures[0].result()
    assert resp.text == "your code has arrived"
    assert not futures[2].done()

    response_queue.put(
        SessionResponse(
            context=stream_mux_pb2.MuxedResponse(
                routing_id=routing_ids[2],
                request_id=req_contexts[2].request_id,
                status_code=stream_mux_pb2.StatusCode.ERROR,
                status_detail="something went wrong",
            ),
        )
    )
    with pytest.raises(grpc.RpcError) as exc:
        futures[2].result()
    assert exc.value.code() == grpc.StatusCode.UNKNOWN
    assert exc.value.details() == "something went wrong"

    tpool.shutdown()
    # To teardown the client's response thread, we need the server to respond with end-of-RPC-stream
    # or throw an error from the RPC stream.
    response_queue.put(None)

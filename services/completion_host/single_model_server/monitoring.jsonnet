local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local streamsExhausted(cloud, displayName, severity, threshold, durationSeconds) =
    local spec = {
      displayName: displayName,
      conditionPrometheusQueryLanguage: {
        duration: '%ds' % (durationSeconds),
        evaluationInterval: '60s',
        labels: { severity: severity },
        query: |||
          (sum by(cluster, namespace) (
            increase(au_completion_rpc_streams_exhausted_total[1m])
          )) /
          (sum by(cluster, namespace) (
            increase(au_completion_requests_received_total[1m])
          )) > %(threshold)f
        ||| % { threshold: threshold },
      },
    };
    local name = std.asciiLower(std.strReplace(displayName, ' ', '-'));
    monitoringLib.alertPolicy(cloud, spec, name, 'Namespace %s cluster %s exhausted completion RPC streams' % [
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
    ], team='completion');
  [
    // Alert if 2% of requests are impacted for 5 minutes, or 10% for 1 minute.
    streamsExhausted(cloud, 'Completion streams exhausted warning', 'warning', 0.02, 300),
    streamsExhausted(cloud, 'Completion streams exhausted error', 'error', 0.10, 60),
  ]

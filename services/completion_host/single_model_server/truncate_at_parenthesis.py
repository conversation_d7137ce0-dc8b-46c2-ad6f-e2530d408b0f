"""Module for truncating completions at open parentheses."""

import hashlib
import structlog
from dataclasses import dataclass
from typing import Set

import base.feature_flags
from base.languages import guess_language, LanguageId

log = structlog.get_logger()

# Feature flags
_ENABLE_TRUNCATE_AT_PARENTHESIS = base.feature_flags.BoolFlag(
    "enable_truncate_at_parenthesis", False
)

_TRUNCATE_AT_PARENTHESIS_RANDOMIZE = base.feature_flags.<PERSON>olFlag(
    "truncate_at_parenthesis_randomize", False
)

# Languages to exclude from truncation
_EXCLUDED_LANGUAGES: Set[LanguageId] = {
    "HTML",
    "XML",
    "Markdown",
    "Plain Text",
    "YAML",
    "JSON",
}


@dataclass
class TruncationResult:
    """Result of truncation at parenthesis."""

    text: str
    """The truncated text."""

    was_truncated: bool
    """Whether the text was truncated."""

    could_have_truncated: bool
    """Whether the text could have been truncated (met all criteria)."""


def should_apply_for_session_id(session_id: str) -> bool:
    """Determine whether to apply truncation for a given session ID.

    Uses a deterministic hash of the session ID to ensure consistent behavior
    for the same session across multiple requests.

    Args:
        session_id: The session ID to check.

    Returns:
        True if truncation should be applied for this session ID, False otherwise.
    """
    # Use MD5 to get a consistent hash value from the session ID
    # We're not using this for security, just for deterministic randomness
    hash_object = hashlib.md5(session_id.encode())  # nosec
    hash_hex = hash_object.hexdigest()

    # Convert the first 8 hex characters to an integer and use modulo 2
    # to get a deterministic 0 or 1 (50% chance for each)
    int_value = int(hash_hex[:8], 16)
    return int_value % 2 == 0  # Even values -> apply truncation


def should_truncate_for_language(path: str) -> bool:
    """Determine if the language of the file should be considered for truncation.

    Args:
        path: The file path.

    Returns:
        True if the language should be considered for truncation, False otherwise.
    """
    language = guess_language(path)
    if language is None:
        return False

    return language not in _EXCLUDED_LANGUAGES


def truncate_at_parenthesis(
    completion: str,
    path: str,
    session_id: str,
    context: base.feature_flags.Context,
) -> TruncationResult:
    """Truncate the completion at the first open parenthesis where conditions are met, if any are met.

    Conditions:
    1. Feature flag is enabled
    2. The language is not excluded
    3. The text contains an open parenthesis that meets the following criteria:
        - there are at least three characters before the parenthesis
        - the character before the parenthesis is not whitespace
        - the character after the parenthesis is a newline character
        - it is not the last two character in the text

    Args:
        completion: The completion text to potentially truncate.
        path: The file path.
        session_id: The session ID for deterministic selection.
        context: The feature flag context.

    Returns:
        A TruncationResult with the truncated text and metadata.
    """
    # Check if feature flag is enabled
    if not _ENABLE_TRUNCATE_AT_PARENTHESIS.get(context):
        return TruncationResult(
            text=completion,
            was_truncated=False,
            could_have_truncated=False,
        )

    # Check if language should be considered
    if not should_truncate_for_language(path):
        return TruncationResult(
            text=completion,
            was_truncated=False,
            could_have_truncated=False,
        )

    # Find the first open parenthesis that meets our criteria for truncation:
    # 1. There are at least three characters before the parenthesis
    # 2. The character before the parenthesis is not whitespace
    # 3. There is a character after the parenthesis, and it is a newline character
    # 4. The last two characters in the text are not a parenthesis followed by a newline
    paren_index = -1
    for i in range(3, len(completion) - 2):
        if (
            completion[i] == "("
            and not completion[i - 1].isspace()
            and completion[i + 1] == "\n"
        ):
            paren_index = i
            break

    if paren_index == -1:
        return TruncationResult(
            text=completion,
            was_truncated=False,
            could_have_truncated=False,
        )

    # At this point we've found a valid parenthesis for truncation
    could_have_truncated = True

    # Check if we should randomly apply truncation
    was_truncated = True
    if _TRUNCATE_AT_PARENTHESIS_RANDOMIZE.get(context):
        was_truncated = should_apply_for_session_id(session_id)

    if not was_truncated:
        return TruncationResult(
            text=completion,
            was_truncated=False,
            could_have_truncated=could_have_truncated,
        )

    # Truncate at the open parenthesis (include the parenthesis)
    truncated_text = completion[: paren_index + 1]

    log.info(
        "Truncated completion at open parenthesis",
        original_length=len(completion),
        truncated_length=len(truncated_text),
        session_id=session_id,
    )

    return TruncationResult(
        text=truncated_text,
        was_truncated=True,
        could_have_truncated=could_have_truncated,
    )

# Mono Model Completion Host Server

The mono model completion host server hosts model-specific code for completions.
Such a completion host is responsible for

- tokenization
- prompt formatting
- retrieval
- pre- and post processing

The completion host will form a prompt and sent the prompt to an inference
host. After the inference host response with generates tokens, it will post-process
the response and form a text response.

A mono model completion host has a 1:1 mapping to an inference host that does the
token generation.

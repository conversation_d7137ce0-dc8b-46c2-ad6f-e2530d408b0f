Model Information
=================

Data Source Information
------------------------
Base directories:
  - /mnt/efs/augment/data/prism/completion/2024-07-01_2024-07-31_elden
  - /mnt/efs/augment/data/prism/completion/2024-08-01_2024-08-31_elden
Data files:
  - /mnt/efs/augment/data/prism/completion/2024-07-01_2024-07-31_elden/data_dogfood.jsonl.zst.jsonl.zst
  - /mnt/efs/augment/data/prism/completion/2024-07-01_2024-07-31_elden/data_dogfood_shard.jsonl.zst.jsonl.zst
  - /mnt/efs/augment/data/prism/completion/2024-08-01_2024-08-31_elden/data_dogfood.jsonl.zst.jsonl.zst
  - /mnt/efs/augment/data/prism/completion/2024-08-01_2024-08-31_elden/data_dogfood_shard.jsonl.zst.jsonl.zst

Model Features
--------------
Features: token_prob_min, token_prob_median, token_prob_max, first_token_low, num_very_low_prob_tokens, num_low_prob_tokens, low_prob_token_ratio, high_prob_token_ratio, leading_average_1, leading_average_3, leading_average_5, moving_average_min, moving_average_median, moving_average_max, mean, num_lines, token_diversity, num_complexities, num_spaces, num_chars, num_numbers, is_comment, completion_geq_1_duplicate_line, completion_geq_4_duplicate_line, any_completion_line_matches_last_prefix_line, first_long_non_print_line_matches_recent_prefix, is_py, is_ipynb, is_ts, is_tsx, is_go, is_rs, is_proto, is_yaml, is_json, is_java, is_misc

Data Information
-----------------
Eval Set 1:
  Test Data: 4847 samples
  Eval Date Range (Inclusive): 2024-07-01 to 2024-07-02
Eval Set 2:
  Test Data: 6258 samples
  Eval Date Range (Inclusive): 2024-08-01 to 2024-08-02
Eval Set 3:
  Test Data: 3994 samples
  Eval Date Range (Inclusive): 2024-08-30 to 2024-08-31

Evaluation Results
-------------------------
New model checkpoint: /mnt/efs/augment/checkpoints/prism/completion/prism_eldenv4/model.json
Old model checkpoint: services/completion_host/single_model_server/prism_models/prism_eldenv3.json

Format of each cell is "EVAL_AUC (EVAL_SIZE)"
+-----------------+---------------------------+---------------------------+---------------------------+---------------+
| Overall AUC     | 2024-07-01 - 2024-07-02   | 2024-08-01 - 2024-08-02   | 2024-08-30 - 2024-08-31   | Average       |
+=================+===========================+===========================+===========================+===============+
| New Model AUC   | 0.7213 (4847)             | 0.7016 (6258)             | 0.7340 (3994)             | 0.7190 (5033) |
+-----------------+---------------------------+---------------------------+---------------------------+---------------+
| Old Model AUC   | 0.7032 (4847)             | 0.6694 (6258)             | 0.7085 (3994)             | 0.6937 (5033) |
+-----------------+---------------------------+---------------------------+---------------------------+---------------+
| AUC Improvement | 0.0181 (4847)             | 0.0322 (6258)             | 0.0255 (3994)             | 0.0253 (5033) |
+-----------------+---------------------------+---------------------------+---------------------------+---------------+

Each category below represents a subset of the evaluation data where a specific feature is present.
This analysis helps identify the new model's performance on samples with particular characteristics.
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| Filter Category AUC Improvement (Old->New Model)   | 2024-07-01 - 2024-07-02   | 2024-08-01 - 2024-08-02   | 2024-08-30 - 2024-08-31   | Average       |
+====================================================+===========================+===========================+===========================+===============+
| any_completion_line_matches_last_prefix_line       | 0.0364 (27)               | 0.1677 (58)               | 0.2318 (32)               | 0.1453 (39)   |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| completion_geq_1_duplicate_line                    | -0.1071 (26)              | -0.0294 (26)              | -0.0972 (12)              | -0.0779 (21)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| completion_geq_4_duplicate_line                    | -1.0000 (2)               | N/A                       | N/A                       | -1.0000 (2)   |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| first_long_non_print_line_matches_recent_prefix    | 0.0316 (41)               | 0.0501 (56)               | 0.0375 (54)               | 0.0397 (50)   |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_comment                                         | 0.0203 (458)              | 0.0034 (386)              | -0.0100 (328)             | 0.0046 (391)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_go                                              | 0.0079 (416)              | 0.0302 (291)              | 0.0133 (146)              | 0.0171 (284)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_ipynb                                           | 0.0127 (475)              | 0.0248 (911)              | 0.0168 (519)              | 0.0181 (635)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_java                                            | N/A                       | N/A                       | -0.5714 (8)               | -0.5714 (8)   |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_json                                            | -0.0264 (42)              | 0.0833 (16)               | -0.0476 (10)              | 0.0031 (23)   |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_misc                                            | 0.0275 (915)              | 0.0498 (787)              | 0.0200 (866)              | 0.0324 (856)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_proto                                           | 0.0473 (74)               | 0.0281 (64)               | -0.0200 (15)              | 0.0185 (51)   |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_py                                              | 0.0127 (1720)             | 0.0228 (1947)             | 0.0309 (1665)             | 0.0221 (1777) |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_rs                                              | 0.0261 (214)              | 0.0546 (633)              | -0.0142 (54)              | 0.0222 (300)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_ts                                              | 0.0255 (738)              | 0.0301 (1571)             | 0.0259 (667)              | 0.0272 (992)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_tsx                                             | -0.0432 (184)             | 0.1083 (19)               | -0.0667 (8)               | -0.0005 (70)  |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+
| is_yaml                                            | -0.0255 (60)              | 0.7500 (5)                | 0.0681 (36)               | 0.2642 (34)   |
+----------------------------------------------------+---------------------------+---------------------------+---------------------------+---------------+

True Positive / False Positive / Threshold Tables
------------------------------------------------
Eval Set 1:
+-----------------+------------------+-------------+
|   True Positive |   False Positive |   Threshold |
+=================+==================+=============+
|           0     |            0     |     inf     |
+-----------------+------------------+-------------+
|           0.013 |            0.001 |       0.958 |
+-----------------+------------------+-------------+
|           0.025 |            0.001 |       0.95  |
+-----------------+------------------+-------------+
|           0.05  |            0.002 |       0.93  |
+-----------------+------------------+-------------+
|           0.06  |            0.003 |       0.924 |
+-----------------+------------------+-------------+
|           0.072 |            0.005 |       0.916 |
+-----------------+------------------+-------------+
|           0.083 |            0.008 |       0.908 |
+-----------------+------------------+-------------+
|           0.096 |            0.008 |       0.9   |
+-----------------+------------------+-------------+
|           0.108 |            0.011 |       0.893 |
+-----------------+------------------+-------------+
|           0.122 |            0.014 |       0.885 |
+-----------------+------------------+-------------+
|           0.133 |            0.017 |       0.879 |
+-----------------+------------------+-------------+
|           0.144 |            0.022 |       0.874 |
+-----------------+------------------+-------------+
|           0.158 |            0.024 |       0.867 |
+-----------------+------------------+-------------+
|           0.169 |            0.029 |       0.862 |
+-----------------+------------------+-------------+
|           0.185 |            0.031 |       0.856 |
+-----------------+------------------+-------------+
|           0.197 |            0.033 |       0.851 |
+-----------------+------------------+-------------+
|           0.208 |            0.038 |       0.847 |
+-----------------+------------------+-------------+
|           0.218 |            0.043 |       0.844 |
+-----------------+------------------+-------------+
|           0.231 |            0.045 |       0.84  |
+-----------------+------------------+-------------+
|           0.242 |            0.048 |       0.836 |
+-----------------+------------------+-------------+
|           0.253 |            0.054 |       0.832 |
+-----------------+------------------+-------------+
|           0.264 |            0.06  |       0.828 |
+-----------------+------------------+-------------+
|           0.277 |            0.062 |       0.824 |
+-----------------+------------------+-------------+
|           0.289 |            0.067 |       0.819 |
+-----------------+------------------+-------------+
|           0.3   |            0.072 |       0.815 |
+-----------------+------------------+-------------+
|           0.311 |            0.079 |       0.812 |
+-----------------+------------------+-------------+
|           0.322 |            0.082 |       0.808 |
+-----------------+------------------+-------------+
|           0.333 |            0.086 |       0.804 |
+-----------------+------------------+-------------+
|           0.346 |            0.091 |       0.8   |
+-----------------+------------------+-------------+
|           0.377 |            0.114 |       0.789 |
+-----------------+------------------+-------------+
|           0.408 |            0.131 |       0.778 |
+-----------------+------------------+-------------+
|           0.439 |            0.149 |       0.768 |
+-----------------+------------------+-------------+
|           0.471 |            0.171 |       0.756 |
+-----------------+------------------+-------------+
|           0.504 |            0.197 |       0.741 |
+-----------------+------------------+-------------+
|           0.534 |            0.222 |       0.729 |
+-----------------+------------------+-------------+
|           0.565 |            0.249 |       0.715 |
+-----------------+------------------+-------------+
|           0.597 |            0.279 |       0.7   |
+-----------------+------------------+-------------+
|           0.627 |            0.301 |       0.685 |
+-----------------+------------------+-------------+
|           0.657 |            0.338 |       0.666 |
+-----------------+------------------+-------------+
|           0.688 |            0.382 |       0.647 |
+-----------------+------------------+-------------+
|           0.718 |            0.415 |       0.629 |
+-----------------+------------------+-------------+
|           0.749 |            0.447 |       0.609 |
+-----------------+------------------+-------------+
|           0.779 |            0.494 |       0.586 |
+-----------------+------------------+-------------+
|           0.809 |            0.54  |       0.554 |
+-----------------+------------------+-------------+
|           0.84  |            0.584 |       0.524 |
+-----------------+------------------+-------------+
|           0.871 |            0.639 |       0.488 |
+-----------------+------------------+-------------+
|           0.902 |            0.699 |       0.453 |
+-----------------+------------------+-------------+
|           0.932 |            0.764 |       0.422 |
+-----------------+------------------+-------------+
|           0.963 |            0.847 |       0.384 |
+-----------------+------------------+-------------+
|           0.993 |            0.96  |       0.295 |
+-----------------+------------------+-------------+

Eval Set 2:
+-----------------+------------------+-------------+
|   True Positive |   False Positive |   Threshold |
+=================+==================+=============+
|           0     |            0     |     inf     |
+-----------------+------------------+-------------+
|           0.011 |            0     |       0.924 |
+-----------------+------------------+-------------+
|           0.026 |            0.003 |       0.901 |
+-----------------+------------------+-------------+
|           0.037 |            0.004 |       0.894 |
+-----------------+------------------+-------------+
|           0.047 |            0.008 |       0.887 |
+-----------------+------------------+-------------+
|           0.058 |            0.009 |       0.879 |
+-----------------+------------------+-------------+
|           0.069 |            0.013 |       0.873 |
+-----------------+------------------+-------------+
|           0.081 |            0.018 |       0.866 |
+-----------------+------------------+-------------+
|           0.092 |            0.02  |       0.861 |
+-----------------+------------------+-------------+
|           0.103 |            0.025 |       0.856 |
+-----------------+------------------+-------------+
|           0.113 |            0.027 |       0.852 |
+-----------------+------------------+-------------+
|           0.124 |            0.029 |       0.848 |
+-----------------+------------------+-------------+
|           0.135 |            0.03  |       0.844 |
+-----------------+------------------+-------------+
|           0.147 |            0.034 |       0.839 |
+-----------------+------------------+-------------+
|           0.159 |            0.037 |       0.836 |
+-----------------+------------------+-------------+
|           0.17  |            0.039 |       0.831 |
+-----------------+------------------+-------------+
|           0.181 |            0.043 |       0.828 |
+-----------------+------------------+-------------+
|           0.191 |            0.047 |       0.825 |
+-----------------+------------------+-------------+
|           0.202 |            0.052 |       0.821 |
+-----------------+------------------+-------------+
|           0.212 |            0.056 |       0.818 |
+-----------------+------------------+-------------+
|           0.223 |            0.062 |       0.815 |
+-----------------+------------------+-------------+
|           0.234 |            0.066 |       0.812 |
+-----------------+------------------+-------------+
|           0.246 |            0.07  |       0.809 |
+-----------------+------------------+-------------+
|           0.258 |            0.076 |       0.805 |
+-----------------+------------------+-------------+
|           0.268 |            0.082 |       0.803 |
+-----------------+------------------+-------------+
|           0.278 |            0.084 |       0.8   |
+-----------------+------------------+-------------+
|           0.29  |            0.09  |       0.796 |
+-----------------+------------------+-------------+
|           0.301 |            0.095 |       0.793 |
+-----------------+------------------+-------------+
|           0.332 |            0.116 |       0.782 |
+-----------------+------------------+-------------+
|           0.362 |            0.136 |       0.769 |
+-----------------+------------------+-------------+
|           0.392 |            0.155 |       0.758 |
+-----------------+------------------+-------------+
|           0.423 |            0.171 |       0.747 |
+-----------------+------------------+-------------+
|           0.454 |            0.194 |       0.735 |
+-----------------+------------------+-------------+
|           0.484 |            0.215 |       0.724 |
+-----------------+------------------+-------------+
|           0.515 |            0.241 |       0.71  |
+-----------------+------------------+-------------+
|           0.545 |            0.263 |       0.699 |
+-----------------+------------------+-------------+
|           0.576 |            0.292 |       0.685 |
+-----------------+------------------+-------------+
|           0.606 |            0.315 |       0.672 |
+-----------------+------------------+-------------+
|           0.637 |            0.345 |       0.654 |
+-----------------+------------------+-------------+
|           0.667 |            0.375 |       0.639 |
+-----------------+------------------+-------------+
|           0.698 |            0.409 |       0.62  |
+-----------------+------------------+-------------+
|           0.728 |            0.451 |       0.597 |
+-----------------+------------------+-------------+
|           0.759 |            0.491 |       0.569 |
+-----------------+------------------+-------------+
|           0.789 |            0.527 |       0.543 |
+-----------------+------------------+-------------+
|           0.82  |            0.569 |       0.52  |
+-----------------+------------------+-------------+
|           0.85  |            0.623 |       0.49  |
+-----------------+------------------+-------------+
|           0.88  |            0.661 |       0.462 |
+-----------------+------------------+-------------+
|           0.91  |            0.714 |       0.429 |
+-----------------+------------------+-------------+
|           0.941 |            0.782 |       0.4   |
+-----------------+------------------+-------------+
|           0.971 |            0.871 |       0.358 |
+-----------------+------------------+-------------+

Eval Set 3:
+-----------------+------------------+-------------+
|   True Positive |   False Positive |   Threshold |
+=================+==================+=============+
|           0     |            0     |     inf     |
+-----------------+------------------+-------------+
|           0.012 |            0     |       0.935 |
+-----------------+------------------+-------------+
|           0.031 |            0.004 |       0.911 |
+-----------------+------------------+-------------+
|           0.042 |            0.005 |       0.904 |
+-----------------+------------------+-------------+
|           0.054 |            0.006 |       0.895 |
+-----------------+------------------+-------------+
|           0.065 |            0.01  |       0.89  |
+-----------------+------------------+-------------+
|           0.085 |            0.014 |       0.879 |
+-----------------+------------------+-------------+
|           0.108 |            0.02  |       0.87  |
+-----------------+------------------+-------------+
|           0.118 |            0.021 |       0.866 |
+-----------------+------------------+-------------+
|           0.133 |            0.023 |       0.861 |
+-----------------+------------------+-------------+
|           0.143 |            0.026 |       0.858 |
+-----------------+------------------+-------------+
|           0.157 |            0.03  |       0.854 |
+-----------------+------------------+-------------+
|           0.167 |            0.033 |       0.85  |
+-----------------+------------------+-------------+
|           0.178 |            0.037 |       0.846 |
+-----------------+------------------+-------------+
|           0.189 |            0.04  |       0.842 |
+-----------------+------------------+-------------+
|           0.202 |            0.045 |       0.838 |
+-----------------+------------------+-------------+
|           0.215 |            0.049 |       0.835 |
+-----------------+------------------+-------------+
|           0.225 |            0.054 |       0.832 |
+-----------------+------------------+-------------+
|           0.235 |            0.058 |       0.829 |
+-----------------+------------------+-------------+
|           0.248 |            0.064 |       0.825 |
+-----------------+------------------+-------------+
|           0.258 |            0.069 |       0.822 |
+-----------------+------------------+-------------+
|           0.272 |            0.077 |       0.818 |
+-----------------+------------------+-------------+
|           0.287 |            0.083 |       0.814 |
+-----------------+------------------+-------------+
|           0.299 |            0.086 |       0.812 |
+-----------------+------------------+-------------+
|           0.31  |            0.09  |       0.809 |
+-----------------+------------------+-------------+
|           0.341 |            0.112 |       0.801 |
+-----------------+------------------+-------------+
|           0.371 |            0.13  |       0.793 |
+-----------------+------------------+-------------+
|           0.402 |            0.145 |       0.785 |
+-----------------+------------------+-------------+
|           0.433 |            0.163 |       0.776 |
+-----------------+------------------+-------------+
|           0.464 |            0.18  |       0.766 |
+-----------------+------------------+-------------+
|           0.497 |            0.196 |       0.756 |
+-----------------+------------------+-------------+
|           0.527 |            0.207 |       0.746 |
+-----------------+------------------+-------------+
|           0.558 |            0.232 |       0.736 |
+-----------------+------------------+-------------+
|           0.593 |            0.261 |       0.721 |
+-----------------+------------------+-------------+
|           0.623 |            0.291 |       0.709 |
+-----------------+------------------+-------------+
|           0.653 |            0.32  |       0.697 |
+-----------------+------------------+-------------+
|           0.684 |            0.346 |       0.682 |
+-----------------+------------------+-------------+
|           0.714 |            0.377 |       0.666 |
+-----------------+------------------+-------------+
|           0.746 |            0.401 |       0.648 |
+-----------------+------------------+-------------+
|           0.776 |            0.443 |       0.627 |
+-----------------+------------------+-------------+
|           0.806 |            0.48  |       0.606 |
+-----------------+------------------+-------------+
|           0.838 |            0.518 |       0.58  |
+-----------------+------------------+-------------+
|           0.868 |            0.573 |       0.55  |
+-----------------+------------------+-------------+
|           0.898 |            0.625 |       0.517 |
+-----------------+------------------+-------------+
|           0.93  |            0.688 |       0.479 |
+-----------------+------------------+-------------+
|           0.962 |            0.775 |       0.419 |
+-----------------+------------------+-------------+
|           0.992 |            0.934 |       0.318 |
+-----------------+------------------+-------------+

Feature Importance
------------------
+-------------------------------------------------+--------------------+
| Feature                                         |   Importance Score |
+=================================================+====================+
| leading_average_5                               |          180.241   |
+-------------------------------------------------+--------------------+
| leading_average_3                               |           68.0016  |
+-------------------------------------------------+--------------------+
| moving_average_min                              |           32.9202  |
+-------------------------------------------------+--------------------+
| completion_geq_4_duplicate_line                 |           21.7334  |
+-------------------------------------------------+--------------------+
| token_prob_min                                  |           13.8739  |
+-------------------------------------------------+--------------------+
| first_long_non_print_line_matches_recent_prefix |           12.0656  |
+-------------------------------------------------+--------------------+
| any_completion_line_matches_last_prefix_line    |           11.6674  |
+-------------------------------------------------+--------------------+
| is_java                                         |           11.4778  |
+-------------------------------------------------+--------------------+
| high_prob_token_ratio                           |           10.7937  |
+-------------------------------------------------+--------------------+
| mean                                            |           10.3635  |
+-------------------------------------------------+--------------------+
| num_very_low_prob_tokens                        |            9.73194 |
+-------------------------------------------------+--------------------+
| is_json                                         |            9.40417 |
+-------------------------------------------------+--------------------+
| is_comment                                      |            9.20862 |
+-------------------------------------------------+--------------------+
| is_misc                                         |            8.17702 |
+-------------------------------------------------+--------------------+
| is_tsx                                          |            7.93955 |
+-------------------------------------------------+--------------------+
| num_lines                                       |            7.7405  |
+-------------------------------------------------+--------------------+
| num_spaces                                      |            7.44064 |
+-------------------------------------------------+--------------------+
| num_low_prob_tokens                             |            7.3924  |
+-------------------------------------------------+--------------------+
| is_ipynb                                        |            7.2981  |
+-------------------------------------------------+--------------------+
| token_diversity                                 |            6.97402 |
+-------------------------------------------------+--------------------+
| is_ts                                           |            6.75647 |
+-------------------------------------------------+--------------------+
| is_py                                           |            6.74462 |
+-------------------------------------------------+--------------------+
| leading_average_1                               |            6.61718 |
+-------------------------------------------------+--------------------+
| is_go                                           |            6.60728 |
+-------------------------------------------------+--------------------+
| num_chars                                       |            6.44781 |
+-------------------------------------------------+--------------------+
| num_complexities                                |            6.41017 |
+-------------------------------------------------+--------------------+
| moving_average_median                           |            6.29874 |
+-------------------------------------------------+--------------------+
| is_rs                                           |            5.74695 |
+-------------------------------------------------+--------------------+
| token_prob_max                                  |            5.49148 |
+-------------------------------------------------+--------------------+
| num_numbers                                     |            5.41238 |
+-------------------------------------------------+--------------------+
| moving_average_max                              |            5.35721 |
+-------------------------------------------------+--------------------+
| low_prob_token_ratio                            |            5.28776 |
+-------------------------------------------------+--------------------+
| token_prob_median                               |            4.96499 |
+-------------------------------------------------+--------------------+
| is_yaml                                         |            4.65353 |
+-------------------------------------------------+--------------------+
| completion_geq_1_duplicate_line                 |            3.94922 |
+-------------------------------------------------+--------------------+
| first_token_low                                 |            3.4508  |
+-------------------------------------------------+--------------------+
| is_proto                                        |            2.89614 |
+-------------------------------------------------+--------------------+

Model Hyperparameters
---------------------
learner: {'generic_param': {'device': 'cpu', 'fail_on_invalid_gpu_id': '0', 'n_jobs': '0', 'nthread': '0', 'random_state': '0', 'seed': '0', 'seed_per_iteration': '0', 'validate_parameters': '1'}, 'gradient_booster': {'gbtree_model_param': {'num_parallel_tree': '1', 'num_trees': '1000'}, 'gbtree_train_param': {'process_type': 'default', 'tree_method': 'auto', 'updater': 'grow_quantile_histmaker', 'updater_seq': 'grow_quantile_histmaker'}, 'name': 'gbtree', 'specified_updater': False, 'tree_train_param': {'alpha': '2', 'cache_opt': '1', 'colsample_bylevel': '1', 'colsample_bynode': '1', 'colsample_bytree': '1', 'eta': '0.00499999989', 'gamma': '0.100000001', 'grow_policy': 'depthwise', 'interaction_constraints': '', 'lambda': '1', 'learning_rate': '0.00499999989', 'max_bin': '256', 'max_cat_threshold': '64', 'max_cat_to_onehot': '4', 'max_delta_step': '0', 'max_depth': '8', 'max_leaves': '0', 'min_child_weight': '1', 'min_split_loss': '0.100000001', 'monotone_constraints': '()', 'refresh_leaf': '1', 'reg_alpha': '2', 'reg_lambda': '1', 'sampling_method': 'uniform', 'sketch_ratio': '2', 'sparse_threshold': '0.20000000000000001', 'subsample': '1'}, 'updater': [{'hist_train_param': {'debug_synchronize': '0', 'max_cached_hist_node': '65536'}, 'name': 'grow_quantile_histmaker'}]}, 'learner_model_param': {'base_score': '6.53612E-1', 'boost_from_average': '1', 'num_class': '0', 'num_feature': '37', 'num_target': '1'}, 'learner_train_param': {'booster': 'gbtree', 'disable_default_eval_metric': '0', 'multi_strategy': 'one_output_per_tree', 'objective': 'binary:logistic'}, 'metrics': [{'name': 'logloss'}], 'objective': {'name': 'binary:logistic', 'reg_loss_param': {'scale_pos_weight': '1'}}}
version: [2, 1, 0]

Model File
----------
Model saved at: /mnt/efs/augment/checkpoints/prism/completion/prism_eldenv4/model.json

Threshold Information
---------------------
Threshold Value: 0.875

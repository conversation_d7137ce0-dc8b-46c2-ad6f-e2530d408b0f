{"learner": {"attributes": {}, "feature_names": ["token_prob_min", "token_prob_median", "token_prob_max", "leading_average_1", "leading_average_3", "leading_average_5", "moving_average_min", "moving_average_median", "moving_average_max", "mean", "num_lines", "token_diversity"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [-0.0, 0.5071982, -0.7654596], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [874.3159, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.2265625, 0.5071982, -0.7654596], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [2250.0, 1353.5, 896.5], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.008575825, 0.49157748, -0.14781952], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [142.70665, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6184934, 0.49157748, -0.14781952], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [2047.0679, 445.24597, 1601.8219], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.007962443, 0.08315437, -0.28717396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [43.483997, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.972433, 0.08315437, -0.28717396], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1957.3796, 1560.5232, 396.85645], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0029821722, -0.12297629, 0.19816624], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [45.914993, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.22939046, -0.12297629, 0.19816624], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1900.2859, 1190.4922, 709.7937], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0017263276, 0.4257437, -0.04606747], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [37.94136, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.18354186, 0.4257437, -0.04606747], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1999.6718, 187.12218, 1812.5497], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0034410998, 0.07875535, -0.13249902], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [20.140963, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9098898, 0.07875535, -0.13249902], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1965.2122, 1264.8655, 700.3466], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006783221, -0.35122418, 0.044316325], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [30.280054, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.833087, -0.35122418, 0.044316325], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1917.791, 217.3838, 1700.4072], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0020931393, -0.013894191, 0.5328716], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [16.514845, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99973303, -0.013894191, 0.5328716], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1944.3175, 1888.4114, 55.906136], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0009855281, 0.09658526, -0.09697923], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [18.310638, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.81220067, 0.09658526, -0.09697923], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1952.9739, 968.5279, 984.44604], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [1.7007713e-05, -0.06927688, 0.1130497], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [14.996795, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.22939046, -0.06927688, 0.1130497], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1912.6943, 1186.0078, 726.6865], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005556844, 0.024108749, -0.34149384], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [16.542477, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-2.0662943e-05, 0.024108749, -0.34149384], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1965.2448, 1833.5312, 131.71356], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00078397826, 0.21101399, -0.027950794], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [11.270172, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.362176, 0.21101399, -0.027950794], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1956.6846, 221.6772, 1735.0073], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011304943, -0.050016284, 0.08351395], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [8.15945, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0952381, -0.050016284, 0.08351395], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1934.442, 1193.7251, 740.717], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [3.6596102e-05, 0.005567214, -0.7240977], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [7.742152, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99998516, 0.005567214, -0.7240977], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1931.1584, 1917.5056, 13.65275], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0012044787, 0.021623623, -0.16985194], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [7.425125, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.4736842, 0.021623623, -0.16985194], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1926.6992, 1697.7621, 228.93709], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-3.07505e-05, -0.0060040234, 0.4705777], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [5.4239974, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.4615386, -0.0060040234, 0.4705777], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1927.5066, 1904.3229, 23.183681], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00032341562, 0.3019908, -0.0070776376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [4.305637, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-1.0625, 0.3019908, -0.0070776376], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1926.5243, 45.180023, 1881.3442], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006870324, -0.29015583, 0.016271153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [8.709532, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.5855778, -0.29015583, 0.016271153], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1919.519, 96.72597, 1822.793], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00063758116, 0.024105359, -0.08409544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [3.8421118, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.84373075, 0.024105359, -0.08409544], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1930.1506, 1512.0786, 418.07193], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00035233644, -0.043832738, 0.071981005], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [6.012364, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.22379558, -0.043832738, 0.071981005], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1909.6678, 1192.96, 716.7079], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00021653874, 0.043123282, -0.051249098], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [4.298074, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.85317767, 0.043123282, -0.051249098], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1941.2987, 1049.8553, 891.4433], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-5.5504468e-05, -0.016371148, 0.18025969], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [5.647251, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99833816, -0.016371148, 0.18025969], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1917.552, 1759.2749, 158.27705], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00027398026, 0.005541789, -0.47254148], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [5.294454, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9996472, 0.005541789, -0.47254148], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1925.6868, 1903.2375, 22.449278], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00072587526, -0.004564409, 0.49118862], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [3.6324852, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999604, -0.004564409, 0.49118862], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1921.5636, 1907.6683, 13.8953], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00010970041, -0.44995338, 0.0034136344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [2.861777, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.28206295, -0.44995338, 0.0034136344], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1922.5253, 13.025312, 1909.4999], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [7.265807e-05, 0.35919708, -0.008816515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [6.1516676, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-1.0625, 0.35919708, -0.008816515], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1925.0294, 45.546177, 1879.4832], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00088863063, -0.28054476, 0.007725341], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [3.692935, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.19691168, -0.28054476, 0.007725341], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1917.2002, 44.519474, 1872.6807], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00042783527, 0.47593853, -0.0024803914], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [2.6650174, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.110457435, 0.47593853, -0.0024803914], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1925.2782, 10.7147, 1914.5635], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006072148, -0.00111353, 0.8080097], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [2.6731372, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, -0.00111353, 0.8080097], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1922.3949, 1919.3031, 3.0918186], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00058817613, -0.040701468, 0.032334603], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [2.5213943, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.025675455, -0.040701468, 0.032334603], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1921.5519, 835.1141, 1086.4377], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [1.0027349e-05, 0.022490049, -0.09539826], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [4.1405587, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.0073598227, 0.022490049, -0.09539826], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1928.5277, 1561.397, 367.1308], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.000294036, -0.015379072, 0.09016835], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [2.6159766, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9873849, -0.015379072, 0.09016835], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.9724, 1641.9923, 272.9801], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00019259594, 0.07335489, -0.023477871], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [3.3032117, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9795815, 0.07335489, -0.023477871], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1926.7944, 462.8172, 1463.9773], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00010046302, -0.04576733, 0.04023767], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [3.5398338, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9993441, -0.04576733, 0.04023767], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1920.7721, 896.3292, 1024.4429], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [2.6085713e-06, 0.009996854, -0.16073823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [3.0962286, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9964814, 0.009996854, -0.16073823], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1925.333, 1813.5135, 111.819466], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00043434193, 0.009726175, -0.16137615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [3.136738, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.7037038, 0.009726175, -0.16137615], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1916.2389, 1803.3313, 112.90766], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-6.2816456e-05, -0.009860099, 0.16056375], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [3.0191078, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.00093078613, -0.009860099, 0.16056375], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1916.4664, 1807.1777, 109.28867], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0002267572, 0.0047776867, -0.2858089], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [2.752781, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.00015974045, 0.0047776867, -0.2858089], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1924.1709, 1891.9994, 32.171482], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00045919025, -0.30355448, 0.003979446], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [2.5853975, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.37076476, -0.30355448, 0.003979446], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1919.8597, 26.736702, 1893.123], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00010873033, 0.273612, -0.007188488], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [3.8421872, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.33495805, 0.273612, -0.007188488], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1923.1384, 49.02857, 1874.1099], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000596366, -0.3935139, 0.0050459127], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [3.3645654, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.25883552, -0.3935139, 0.0050459127], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1916.5164, 20.419935, 1896.0964], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00024908755, 0.3025595, -0.0037350901], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [2.3160205, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.2528396, 0.3025595, -0.0037350901], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1920.9348, 24.012081, 1896.9227], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00042446842, -0.0013266412, 0.597416], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [2.006348, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [5.0, -0.0013266412, 0.597416], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1917.4619, 1912.8489, 4.613036], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00029941677, 0.0077422913, -0.122142486], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [1.7492393, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99999917, 0.0077422913, -0.122142486], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1917.4196, 1808.4275, 108.992096], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001440333, -0.021924086, 0.053730126], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [2.2488196, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.1875, -0.021924086, 0.053730126], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.5243, 1363.7751, 550.74915], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [2.8981984e-05, 0.021421991, -0.08004046], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [3.2800605, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.2857143, 0.021421991, -0.08004046], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1912.8861, 1510.1387, 402.74744], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-1.1434111e-05, -0.01703303, 0.103171535], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [3.3651145, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.4090909, -0.01703303, 0.103171535], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1913.9819, 1643.6688, 270.31317], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [4.538458e-05, 0.017035091, -0.06341981], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [2.0656788, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.2857143, 0.017035091, -0.06341981], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1913.759, 1510.2065, 403.5525], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-1.3615847e-05, -0.021424573, 0.052944392], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [2.173133, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.1875, -0.021424573, 0.052944392], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.5426, 1363.7664, 550.7762], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [2.8409291e-05, -0.11493911, 0.008394624], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [1.8418729, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.17377394, -0.11493911, 0.008394624], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1912.9382, 128.89806, 1784.0402], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00017669715, 0.0653625, -0.022995308], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [2.9050834, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7216811, 0.0653625, -0.022995308], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1921.2825, 503.38257, 1417.8999], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00019403285, -0.09729755, 0.020595873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [3.7945902, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.69267976, -0.09729755, 0.020595873], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1905.7732, 329.14798, 1576.6252], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00022608058, 0.061128262, -0.021345923], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [2.5236115, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98497725, 0.061128262, -0.021345923], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1918.8822, 501.4245, 1417.4578], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [7.255448e-05, 0.5929696, -0.0017986993], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [2.1255045, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.0505711, 0.5929696, -0.0017986993], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1913.8381, 5.0274677, 1908.8107], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005603133, -0.024812158, 0.04122303], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [1.9749529, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.22939046, -0.024812158, 0.04122303], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1912.2539, 1177.7552, 734.49866], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-6.628385e-05, 0.022399312, -0.067940444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [2.9456284, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.79416126, 0.022399312, -0.067940444], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1929.7725, 1450.3821, 479.3904], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00024552568, -0.0125678405, 0.12992617], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [3.0685565, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.01310734, -0.0125678405, 0.12992617], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1911.0273, 1746.5946, 164.43271], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00030760007, 0.008401765, -0.12652695], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [2.116195, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98901343, 0.008401765, -0.12652695], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1923.0885, 1799.8303, 123.25814], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00030897502, -0.0020332348, 0.58699805], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [1.9414914, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-2.0424524e-05, -0.0020332348, 0.58699805], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1915.0222, 1910.41, 4.6121864], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [3.0793813e-05, 0.002102846, -0.51471823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [2.04662, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999094, 0.002102846, -0.51471823], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1916.8348, 1910.1417, 6.6931005], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005029701, -0.0040129838, 0.35536742], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [2.3948176, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.0002670288, -0.0040129838, 0.35536742], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1915.0778, 1897.3525, 17.725206], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00012098576, 0.0052778847, -0.1627924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [1.6865163, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9991325, 0.0052778847, -0.1627924], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1918.354, 1857.6677, 60.68623], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00025779163, -0.0061583007, 0.159649], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [1.8078744, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99634457, -0.0061583007, 0.159649], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.0302, 1846.8436, 67.18645], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00014435705, 0.005804777, -0.12095069], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [1.3810378, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9974398, 0.005804777, -0.12095069], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1919.6194, 1830.4313, 89.18804], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00022212975, -0.015960485, 0.052461118], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [1.588818, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9633837, -0.015960485, 0.052461118], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.1965, 1474.4293, 439.76724], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-9.21188e-05, 0.035999075, -0.02837489], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [1.9679265, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.82438046, 0.035999075, -0.02837489], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1925.9045, 846.0284, 1079.8762], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [3.3820997e-05, -0.068090685, 0.020452032], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [2.660677, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.22265625, -0.068090685, 0.020452032], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1910.8057, 440.09897, 1470.7067], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000104984116, 0.07996438, -0.011576026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [1.7939786, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.54743093, 0.07996438, -0.011576026], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1921.1475, 244.40202, 1676.7455], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00020972258, -0.0030707642, 0.22641924], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [1.4197979, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-2.9404957e-06, -0.0030707642, 0.22641924], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1911.3378, 1884.9882, 26.349644], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [1.2689613e-05, 0.015702792, -0.05865898], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [1.7629259, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.2857143, 0.015702792, -0.05865898], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1913.0482, 1509.9789, 403.06937], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-1.5507136e-05, -0.08208495, 0.010553621], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [1.6616694, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.49235025, -0.08208495, 0.010553621], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1913.6858, 217.56029, 1696.1255], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [9.297946e-05, 0.05424769, -0.014437371], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [1.5129188, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.37865958, 0.05424769, -0.014437371], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1920.6685, 405.7399, 1514.9286], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00011098776, -0.02752029, 0.028007401], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [1.4752042, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.09736761, -0.02752029, 0.028007401], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1911.8276, 960.485, 951.3427], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-2.2736665e-06, 0.021249954, -0.057014346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [2.3309686, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.944531, 0.021249954, -0.057014346], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1921.8234, 1400.42, 521.4033], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00016978304, -0.023506334, 0.0370714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [1.6580575, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.22939046, -0.023506334, 0.0370714], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1905.8263, 1171.8655, 733.9607], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-5.0064165e-05, 0.033019368, -0.021470787], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [1.3626935, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7757645, 0.033019368, -0.021470787], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1921.6992, 755.2298, 1166.4694], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [4.2919703e-05, -0.061693493, 0.01193253], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [1.4027737, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6098328, -0.061693493, 0.01193253], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1909.0759, 307.6136, 1601.4623], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [8.165084e-05, 0.17786832, -0.004424104], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [1.5375433, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.33495805, 0.17786832, -0.004424104], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1917.396, 46.441612, 1870.9545], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00028595678, -0.23199211, 0.0037373854], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [1.5353442, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.37076476, -0.23199211, 0.0037373854], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1913.0502, 27.04038, 1886.0098], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [2.5057547e-05, 0.18239647, -0.0039624716], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [1.3943733, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-1.0625, 0.18239647, -0.0039624716], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1915.4313, 40.027214, 1875.404], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0002660995, -0.27083227, 0.0035078998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [1.6819085, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.25883552, -0.27083227, 0.0035078998], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1911.6863, 21.61446, 1890.0719], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [7.102633e-05, 0.25600496, -0.0023684353], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [1.1966563, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-1.4661459, 0.25600496, -0.0023684353], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.6998, 17.096462, 1897.6034], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0002733921, 0.0021166028, -0.32449833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [1.14618, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99998516, 0.0021166028, -0.32449833], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1912.5479, 1902.7426, 9.80535], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00032626148, -0.1195499, 0.0045806193], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [1.1189344, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.86621094, -0.1195499, 0.0045806193], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1910.7194, 74.607185, 1836.1122], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [4.8948415e-05, 0.1119542, -0.008676452], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [1.8711475, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.48546103, 0.1119542, -0.008676452], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1914.3436, 137.61166, 1776.7319], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00022531336, -0.00594087, 0.09997168], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [1.1746666, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.00093078613, -0.00594087, 0.09997168], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1907.8926, 1797.7015, 110.190994], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-6.315358e-05, 0.009793961, -0.08266135], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [1.5586382, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.002513806, 0.009793961, -0.08266135], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1912.3715, 1709.2716, 203.09987], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00016672927, -0.016884822, 0.04065239], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [1.3018454, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.93863446, -0.016884822, 0.04065239], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1905.6896, 1352.3857, 553.30383], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-6.146888e-05, 0.016413284, -0.049825594], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [1.5735563, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.79416126, 0.016413284, -0.049825594], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1917.3208, 1440.9535, 476.36734], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00013894038, -0.0081850225, 0.08269953], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [1.2701622, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.01310734, -0.0081850225, 0.08269953], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1903.6321, 1735.9235, 167.70866], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001088083, 0.012312216, -0.07502759], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [1.7802874, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98500407, 0.012312216, -0.07502759], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1911.1265, 1640.0526, 271.07388], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00020561414, -0.022970362, 0.03660833], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [1.5945932, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.90682185, -0.022970362, 0.03660833], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1900.7156, 1174.6931, 726.0224], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-4.1138133e-05, 0.016945712, -0.033391822], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [1.0853661, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.91975486, 0.016945712, -0.033391822], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1913.8386, 1268.3225, 645.51605], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-5.8970312e-05, -0.010313646, 0.0667216], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [1.3041382, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9890733, -0.010313646, 0.0667216], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1902.3661, 1649.8624, 252.50365], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-8.5083084e-05, 0.0040027658, -0.12678781], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [0.99030155, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [2.0, 0.0040027658, -0.12678781], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1910.0146, 1851.2554, 58.759212], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-5.696952e-05, -0.009629343, 0.0582722], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [1.0676855, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.4090909, -0.009629343, 0.0582722], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [1910.2153, 1641.6412, 268.5741], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "5E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "12", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 0]}
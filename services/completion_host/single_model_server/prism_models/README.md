This directory contains [XGBoost saved model](https://xgboost.readthedocs.io/en/stable/tutorials/saving_model.html) for completion filtering. The saved models are json files.

The XGboost saved models are stored in this directory, as the completion host service does not have access to the persistent storage.

# Model Releases

## [ prism_roguesl_farpref_16B.json ] - 2024-03-22
This model is trained on the data collected from dogfood with the accept/reject labels. It is designed to be used with roguesl models.

## [ prism_eldenv3.json ] - 2024-07-12

Command to train model: `python research/prism/train.py   --tokenizer_name=starcoder   --train_data=/mnt/efs/augment/data/prism/2024-06-15_2024-07-03_elden/train.jsonl --ckpt_path=services/completion_host/single_model_server/prism_models/prism_eldenv3.json --no_filter_data`

This model is trained on:
`/mnt/efs/augment/data/prism/2024-06-15_2024-07-03_elden/`. Evaluation results on test sets:
|  | 2024-06-15_2024-07-03_elden | 2024-07-03_2024-07-11_elden_20k |
|----------|----------|----------|
| AUCROC   | 0.678  | 0.681   |


ROC curve (TPR vs FPR )

| True Positive Rate | False Positive Rate | Threshold |
|----------|----------|----------|
| 0.020 | 0.002 | 0.859 |
| 0.040 | 0.005 | 0.835 |
| 0.061 | 0.008 | 0.811 |
| 0.081 | 0.018 | 0.794 |
| 0.101 | 0.029 | 0.773* |
| 0.121 | 0.040 | 0.757 |
| 0.141 | 0.046 | 0.740 |
| 0.162 | 0.054 | 0.721 |
| 0.182 | 0.065 | 0.702 |
| 0.202 | 0.073 | 0.681 |
| 0.222 | 0.083 | 0.666 |
| 0.242 | 0.090 | 0.649 |
| 0.262 | 0.100 | 0.637 |

*: Recommended value for production.

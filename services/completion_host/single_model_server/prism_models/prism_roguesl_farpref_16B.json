{"learner": {"attributes": {}, "feature_names": ["token_prob_min", "token_prob_median", "token_prob_max", "leading_average_1", "leading_average_3", "leading_average_5", "moving_average_min", "moving_average_median", "moving_average_max", "mean", "num_lines", "token_diversity"], "feature_types": [], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "96"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [0.013635127, 0.74698466, -0.5940883], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 0, "left_children": [1, -1, -1], "loss_changes": [287.69943, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7805313, 0.74698466, -0.5940883], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [643.5426, 291.52527, 352.01733], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.06493424, 0.23592342, -0.33267844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 1, "left_children": [1, -1, -1], "loss_changes": [40.29209, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99306995, 0.23592342, -0.33267844], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [590.48553, 413.19986, 177.28564], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00499052, 0.11874166, -0.31351537], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 2, "left_children": [1, -1, -1], "loss_changes": [20.33193, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, 0.11874166, -0.31351537], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [559.1566, 412.47324, 146.68343], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0021506294, -0.05361089, 0.71463555], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 3, "left_children": [1, -1, -1], "loss_changes": [22.009735, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.6153846, -0.05361089, 0.71463555], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [552.029, 512.8188, 39.210205], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0039459644, 0.74959093, -0.022880968], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 4, "left_children": [1, -1, -1], "loss_changes": [11.088727, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.10874496, 0.74959093, -0.022880968], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [552.4856, 18.251446, 534.23413], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0062574334, 0.027324455, -0.6908778], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 5, "left_children": [1, -1, -1], "loss_changes": [8.077417, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [6.0, 0.027324455, -0.6908778], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [547.7015, 532.5684, 15.133102], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0007901087, 0.021956721, -0.54963064], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 6, "left_children": [1, -1, -1], "loss_changes": [6.830864, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.00038651624, 0.021956721, -0.54963064], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [545.1861, 524.41174, 20.774378], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0022452855, -0.03397481, 0.38953936], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 7, "left_children": [1, -1, -1], "loss_changes": [6.737887, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-3.4491222e-05, -0.03397481, 0.38953936], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [539.9521, 500.34393, 39.608154], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00040017956, 0.2685528, -0.035529196], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 8, "left_children": [1, -1, -1], "loss_changes": [5.261128, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.93440753, 0.2685528, -0.035529196], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [544.0782, 63.52157, 480.55664], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.002898606, -0.16221292, 0.10332544], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 9, "left_children": [1, -1, -1], "loss_changes": [8.93413, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8221516, -0.16221292, 0.10332544], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [536.78613, 202.7803, 334.00586], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0022122401, 0.07354449, -0.21257147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 10, "left_children": [1, -1, -1], "loss_changes": [8.492944, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9600192, 0.07354449, -0.21257147], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [552.31287, 415.1081, 137.20479], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00027437514, -0.008143201, 1.1311648], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 11, "left_children": [1, -1, -1], "loss_changes": [4.825839, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999908, -0.008143201, 1.1311648], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [540.0058, 537.2621, 2.7436962], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0019724888, 0.014159811, -0.7668257], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 12, "left_children": [1, -1, -1], "loss_changes": [5.0860653, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999584, 0.014159811, -0.7668257], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [540.668, 533.19714, 7.47089], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0024374619, -0.011841278, 0.95373946], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 13, "left_children": [1, -1, -1], "loss_changes": [4.854014, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9995109, -0.011841278, 0.95373946], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [537.576, 533.31854, 4.257469], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00048855884, -0.10719013, 0.05429473], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 14, "left_children": [1, -1, -1], "loss_changes": [3.1400518, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.77823764, -0.10719013, 0.05429473], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [539.96515, 179.58383, 360.3813], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0010371525, 0.27491063, -0.04718598], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 15, "left_children": [1, -1, -1], "loss_changes": [7.31851, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6251733, 0.27491063, -0.04718598], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [552.155, 81.96286, 470.19217], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0040717563, -0.4166508, 0.029125351], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 16, "left_children": [1, -1, -1], "loss_changes": [5.7247972, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.46558234, -0.4166508, 0.029125351], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [540.96686, 29.525059, 511.44177], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00293397, 0.12063547, -0.060379885], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 17, "left_children": [1, -1, -1], "loss_changes": [4.1005874, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.71886754, 0.12063547, -0.060379885], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [548.27924, 191.4553, 356.82394], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0013140785, 0.0071163042, -0.8652134], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 18, "left_children": [1, -1, -1], "loss_changes": [2.7222023, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99994946, 0.0071163042, -0.8652134], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [539.20685, 536.6055, 2.601297], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0019586093, -0.044591986, 0.1348999], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 19, "left_children": [1, -1, -1], "loss_changes": [3.1506755, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9644622, -0.044591986, 0.1348999], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [537.9546, 410.6923, 127.26233], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [9.3684735e-05, 0.058685917, -0.1167501], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 20, "left_children": [1, -1, -1], "loss_changes": [3.747476, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.032396, 0.058685917, -0.1167501], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [545.3846, 363.5682, 181.81639], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [7.5649106e-05, -0.10660296, 0.058239292], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 21, "left_children": [1, -1, -1], "loss_changes": [3.341457, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.054024298, -0.10660296, 0.058239292], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [536.52576, 189.01617, 347.50955], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000818042, 0.21941234, -0.043657627], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 22, "left_children": [1, -1, -1], "loss_changes": [5.3068385, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.94340646, 0.21941234, -0.043657627], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [543.8663, 91.28324, 452.58304], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0024555167, -0.10568701, 0.056371816], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 23, "left_children": [1, -1, -1], "loss_changes": [3.1391501, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.77823764, -0.10568701, 0.056371816], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [536.36633, 178.12741, 358.23895], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009875721, 0.12041269, -0.060264904], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 24, "left_children": [1, -1, -1], "loss_changes": [4.0238247, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.47961426, 0.12041269, -0.060264904], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [548.0802, 185.48016, 362.60004], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0013256226, -0.20866978, 0.03366175], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 25, "left_children": [1, -1, -1], "loss_changes": [3.6728091, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.5950898, -0.20866978, 0.03366175], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [538.8451, 71.174515, 467.6706], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0016617883, 0.20747034, -0.03271142], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 26, "left_children": [1, -1, -1], "loss_changes": [3.884868, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6251733, 0.20747034, -0.03271142], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [547.193, 77.58991, 469.6031], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0023571118, 0.021603249, -0.2548539], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 27, "left_children": [1, -1, -1], "loss_changes": [2.677563, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [4.0, 0.021603249, -0.2548539], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [538.77484, 502.11917, 36.65569], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003726742, -0.16531944, 0.03323402], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 28, "left_children": [1, -1, -1], "loss_changes": [2.9855497, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.4229126, -0.16531944, 0.03323402], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [536.59436, 90.15935, 446.43503], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0012241384, 0.19382973, -0.027451258], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 29, "left_children": [1, -1, -1], "loss_changes": [3.0181062, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.23891662, 0.19382973, -0.027451258], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [544.4939, 69.81361, 474.68027], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.001620577, -0.0977444, 0.041578487], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 30, "left_children": [1, -1, -1], "loss_changes": [2.1493177, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.3769714, -0.0977444, 0.041578487], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [539.30945, 154.25958, 385.04987], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005705488, 0.06745376, -0.09630529], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 31, "left_children": [1, -1, -1], "loss_changes": [3.5438683, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9033144, 0.06745376, -0.09630529], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [544.94415, 322.55524, 222.38889], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00031033406, -0.059856974, 0.103152566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 32, "left_children": [1, -1, -1], "loss_changes": [3.3226123, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9891946, -0.059856974, 0.103152566], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [534.9692, 337.77417, 197.19504], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0002993218, 0.52947444, -0.007696144], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 33, "left_children": [1, -1, -1], "loss_changes": [2.3086638, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.2516149, 0.52947444, -0.007696144], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [543.6911, 7.1217313, 536.5694], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0021869177, -0.66594046, 0.009940428], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 34, "left_children": [1, -1, -1], "loss_changes": [2.8197963, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.14791274, -0.66594046, 0.009940428], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [542.0495, 5.244414, 536.80505], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00071647833, 0.12234185, -0.033695143], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 35, "left_children": [1, -1, -1], "loss_changes": [2.2866423, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.66385907, 0.12234185, -0.033695143], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [544.3625, 119.48748, 424.87503], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0010359966, 0.022728037, -0.18066634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 36, "left_children": [1, -1, -1], "loss_changes": [2.1304836, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.4666667, 0.022728037, -0.18066634], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [538.48505, 481.83716, 56.647903], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [2.8669867e-05, -0.29486766, 0.015014797], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 37, "left_children": [1, -1, -1], "loss_changes": [2.3865693, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.4385775, -0.29486766, 0.015014797], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [538.02484, 25.116064, 512.9088], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009940477, 0.47432983, -0.009197346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 38, "left_children": [1, -1, -1], "loss_changes": [2.6255667, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-1.5761719, 0.47432983, -0.009197346], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [542.3728, 10.471793, 531.901], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0022846134, -0.031065652, 0.10400677], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 39, "left_children": [1, -1, -1], "loss_changes": [1.838989, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.2083334, -0.031065652, 0.10400677], "split_indices": [11, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [540.1287, 407.2905, 132.83821], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0003641338, 0.043429427, -0.11958712], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 40, "left_children": [1, -1, -1], "loss_changes": [2.805415, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [1.0, 0.043429427, -0.11958712], "split_indices": [10, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [541.07587, 398.60526, 142.47063], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00012016922, -0.014309881, 0.19565582], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 41, "left_children": [1, -1, -1], "loss_changes": [1.5230877, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999989, -0.014309881, 0.19565582], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [537.80554, 501.70755, 36.097965], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00036841581, 0.024190988, -0.17208691], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 42, "left_children": [1, -1, -1], "loss_changes": [2.2229898, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.0031929016, 0.024190988, -0.17208691], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [539.08, 474.40637, 64.67365], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005496302, -0.02515682, 0.19614066], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 43, "left_children": [1, -1, -1], "loss_changes": [2.595182, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9992402, -0.02515682, 0.19614066], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [534.1756, 475.553, 58.622566], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [4.245193e-05, 0.011181117, -0.34307715], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 44, "left_children": [1, -1, -1], "loss_changes": [2.066263, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.00031503043, 0.011181117, -0.34307715], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [538.63477, 522.63586, 15.998876], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010674422, -0.016134085, 0.25577793], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 45, "left_children": [1, -1, -1], "loss_changes": [2.0819786, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99214983, -0.016134085, 0.25577793], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [535.9407, 507.1295, 28.811214], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [9.723914e-05, 0.01952927, -0.24240541], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 46, "left_children": [1, -1, -1], "loss_changes": [2.5498931, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99671143, 0.01952927, -0.24240541], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [539.1075, 499.9642, 39.14327], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00080385455, -0.039487027, 0.08694892], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 47, "left_children": [1, -1, -1], "loss_changes": [1.8227491, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.77217436, -0.039487027, 0.08694892], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [534.9511, 371.66415, 163.28694], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00017791029, 0.08123877, -0.05855417], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 48, "left_children": [1, -1, -1], "loss_changes": [2.5810761, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.83776885, 0.08123877, -0.05855417], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [540.14435, 226.77322, 313.37115], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006823275, -0.09229633, 0.061220054], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 49, "left_children": [1, -1, -1], "loss_changes": [3.0021105, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8237295, -0.09229633, 0.061220054], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.3523, 209.32684, 322.02545], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00070389884, 0.06461583, -0.073318444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 50, "left_children": [1, -1, -1], "loss_changes": [2.5624828, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8897985, 0.06461583, -0.073318444], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [539.6454, 289.66855, 249.97685], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0003711891, -0.02995358, 0.105723664], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 51, "left_children": [1, -1, -1], "loss_changes": [1.7043825, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.98936814, -0.02995358, 0.105723664], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.49695, 413.25955, 118.23738], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00017659218, 0.051651392, -0.08120129], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 52, "left_children": [1, -1, -1], "loss_changes": [2.2496748, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9497617, 0.051651392, -0.08120129], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [535.05365, 327.9667, 207.08691], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00018483015, -0.07416095, 0.049321283], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 53, "left_children": [1, -1, -1], "loss_changes": [1.9414334, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8237295, -0.07416095, 0.049321283], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.4487, 210.47733, 318.97144], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00044538127, 0.08720551, -0.04292496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 54, "left_children": [1, -1, -1], "loss_changes": [2.0246935, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.47961426, 0.08720551, -0.04292496], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [536.08453, 178.33128, 357.75323], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007007658, -0.08783785, 0.039524756], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 55, "left_children": [1, -1, -1], "loss_changes": [1.8277488, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7550239, -0.08783785, 0.039524756], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.7107, 161.08707, 368.62363], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00059388555, 0.06683995, -0.04747443], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 56, "left_children": [1, -1, -1], "loss_changes": [1.7189646, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.83776885, 0.06683995, -0.04747443], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [537.82227, 225.9859, 311.83633], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00047146247, -0.02670085, 0.13132927], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 57, "left_children": [1, -1, -1], "loss_changes": [1.8933609, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.92546993, -0.02670085, 0.13132927], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [530.49854, 439.94165, 90.556885], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000110262714, 0.011101684, -0.24689548], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 58, "left_children": [1, -1, -1], "loss_changes": [1.45676, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9988839, 0.011101684, -0.24689548], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [534.5614, 512.7019, 21.859486], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0006528049, -0.0060583935, 0.49401972], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 59, "left_children": [1, -1, -1], "loss_changes": [1.4292046, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9995109, -0.0060583935, 0.49401972], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [532.3634, 527.58594, 4.7775006], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0007365978, -0.0055480017, 0.355666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 60, "left_children": [1, -1, -1], "loss_changes": [1.1937565, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-5.801519e-06, -0.0055480017, 0.355666], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [533.2908, 524.9795, 8.311242], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006362164, 0.008772444, -0.36536637], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 61, "left_children": [1, -1, -1], "loss_changes": [1.5959916, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99992687, 0.008772444, -0.36536637], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [533.87256, 523.21747, 10.655079], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0010017579, 0.1801527, -0.012571714], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 62, "left_children": [1, -1, -1], "loss_changes": [1.1192557, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.7112306, 0.1801527, -0.012571714], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.9284, 31.058887, 500.86954], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00093607773, -0.15641914, 0.026007244], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 63, "left_children": [1, -1, -1], "loss_changes": [2.0964441, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.5950898, -0.15641914, 0.026007244], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.3762, 72.033066, 457.34317], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00084038556, 0.15879141, -0.018468373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 64, "left_children": [1, -1, -1], "loss_changes": [1.6382606, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.49316406, 0.15879141, -0.018468373], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [535.2016, 57.512173, 477.6894], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011043659, -0.22857608, 0.013334341], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 65, "left_children": [1, -1, -1], "loss_changes": [1.4973466, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.4385775, -0.22857608, 0.013334341], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [530.9708, 25.949333, 505.0215], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005032131, 0.2955651, -0.01164036], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 66, "left_children": [1, -1, -1], "loss_changes": [1.9207326, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.3777084, 0.2955651, -0.01164036], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [534.0923, 20.18964, 513.90265], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0016214411, -0.1690288, 0.019379267], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 67, "left_children": [1, -1, -1], "loss_changes": [1.616357, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.3769714, -0.1690288, 0.019379267], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.3038, 49.273525, 482.03027], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0005735703, 0.14066987, -0.02969663], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 68, "left_children": [1, -1, -1], "loss_changes": [2.2782118, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.4967308, 0.14066987, -0.02969663], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [535.2349, 94.4509, 440.78403], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0011861029, -0.57462454, 0.0058087436], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 69, "left_children": [1, -1, -1], "loss_changes": [1.4158889, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.24410515, -0.57462454, 0.0058087436], "split_indices": [1, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.68195, 3.236423, 526.44556], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0001493638, 0.38202232, -0.006809302], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 70, "left_children": [1, -1, -1], "loss_changes": [1.3564372, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-1.2529297, 0.38202232, -0.006809302], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [530.90906, 8.128092, 522.78094], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0014707803, -0.109584644, 0.026766738], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 71, "left_children": [1, -1, -1], "loss_changes": [1.4932398, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.645247, -0.109584644, 0.026766738], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.4992, 97.61471, 431.8845], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.000562346, 0.08165795, -0.033651542], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 72, "left_children": [1, -1, -1], "loss_changes": [1.4906636, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.23858373, 0.08165795, -0.033651542], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [535.2639, 158.4086, 376.85532], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006079576, -0.058304157, 0.050252113], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 73, "left_children": [1, -1, -1], "loss_changes": [1.5547491, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.8560796, -0.058304157, 0.050252113], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.6014, 242.11363, 287.48773], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00031038318, 0.035118416, -0.0903457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 74, "left_children": [1, -1, -1], "loss_changes": [1.6993841, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.10293833, 0.035118416, -0.0903457], "split_indices": [6, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [536.53094, 388.12158, 148.40935], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-6.565671e-05, -0.014826808, 0.20575674], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 75, "left_children": [1, -1, -1], "loss_changes": [1.620257, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9976123, -0.014826808, 0.20575674], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.29517, 496.60745, 34.687683], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00011154834, 0.018283168, -0.14574675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 76, "left_children": [1, -1, -1], "loss_changes": [1.4211597, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9921619, 0.018283168, -0.14574675], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [534.1839, 475.78348, 58.400387], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0004201257, -0.039372712, 0.08831783], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 77, "left_children": [1, -1, -1], "loss_changes": [1.8406259, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.77217436, -0.039372712, 0.08831783], "split_indices": [0, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [530.49426, 369.05115, 161.4431], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00018018029, 0.05888328, -0.042399965], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 78, "left_children": [1, -1, -1], "loss_changes": [1.343824, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.83776885, 0.05888328, -0.042399965], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [535.6196, 225.01707, 310.60257], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0003696126, -0.08864489, 0.028023742], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 79, "left_children": [1, -1, -1], "loss_changes": [1.3076124, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.16647339, -0.08864489, 0.028023742], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.19104, 124.91213, 404.27893], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00038038343, 0.12990114, -0.01999831], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 80, "left_children": [1, -1, -1], "loss_changes": [1.4141505, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.6251733, 0.12990114, -0.01999831], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [533.78766, 71.8373, 461.95032], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0009665551, -0.032685872, 0.070082374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 81, "left_children": [1, -1, -1], "loss_changes": [1.2347183, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.96849406, -0.032685872, 0.070082374], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [528.8679, 356.03964, 172.82828], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0001531746, 0.041920036, -0.06807556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 82, "left_children": [1, -1, -1], "loss_changes": [1.5229663, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9526064, 0.041920036, -0.06807556], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [532.42896, 330.49747, 201.93152], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00010646316, -0.1687873, 0.015015106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 83, "left_children": [1, -1, -1], "loss_changes": [1.3343283, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.7942582, -0.1687873, 0.015015106], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [527.9143, 41.98317, 485.93112], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00047810902, 0.11047391, -0.018294495], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 84, "left_children": [1, -1, -1], "loss_changes": [1.101866, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9275057, 0.11047391, -0.018294495], "split_indices": [2, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.6364, 76.79289, 454.84354], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006792734, -0.033810828, 0.07135057], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 85, "left_children": [1, -1, -1], "loss_changes": [1.2918417, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.96849406, -0.033810828, 0.07135057], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [528.0054, 355.18436, 172.821], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00015982038, 0.023907863, -0.11994075], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 86, "left_children": [1, -1, -1], "loss_changes": [1.5221721, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99514806, 0.023907863, -0.11994075], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.68677, 444.57904, 87.10774], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00018375133, -0.02124848, 0.14899902], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 87, "left_children": [1, -1, -1], "loss_changes": [1.6674999, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9974988, -0.02124848, 0.14899902], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [528.6219, 463.96692, 64.654945], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00019087856, 0.009359509, -0.3189394], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 88, "left_children": [1, -1, -1], "loss_changes": [1.5606849, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9998554, 0.009359509, -0.3189394], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.3672, 517.4709, 13.896296], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0005996736, -0.005537528, 0.5272782], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 89, "left_children": [1, -1, -1], "loss_changes": [1.3877314, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-6.906191e-05, -0.005537528, 0.5272782], "split_indices": [7, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [530.2745, 526.3405, 3.9339643], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0006712261, 0.023759713, -0.088782355], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 90, "left_children": [1, -1, -1], "loss_changes": [1.1017334, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.96703583, 0.023759713, -0.088782355], "split_indices": [9, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.4159, 422.97736, 108.43852], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.00018644414, -0.030109122, 0.11332776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 91, "left_children": [1, -1, -1], "loss_changes": [1.7973232, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9708502, -0.030109122, 0.11332776], "split_indices": [5, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [527.1415, 417.75485, 109.38666], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [4.9150523e-05, 0.01874301, -0.12499703], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 92, "left_children": [1, -1, -1], "loss_changes": [1.2492523, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.99171853, 0.01874301, -0.12499703], "split_indices": [4, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [532.41534, 463.91257, 68.50279], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [-0.0003189457, -0.0043511833, 0.5725081], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 93, "left_children": [1, -1, -1], "loss_changes": [1.2266303, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [0.9999908, -0.0043511833, 0.5725081], "split_indices": [3, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [528.9821, 526.27, 2.7121098], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.0012333705, -0.020352393, 0.08757635], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 94, "left_children": [1, -1, -1], "loss_changes": [0.98970747, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.00031379858, -0.020352393, 0.08757635], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [529.0655, 423.86377, 105.20175], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}, {"base_weights": [0.00014670743, 0.033611916, -0.07591839], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0], "id": 95, "left_children": [1, -1, -1], "loss_changes": [1.357537, 0.0, 0.0], "parents": [2147483647, 0, 0], "right_children": [2, -1, -1], "split_conditions": [-0.0012626648, 0.033611916, -0.07591839], "split_indices": [8, 0, 0], "split_type": [0, 0, 0], "sum_hessian": [531.29895, 369.35678, 161.94218], "tree_param": {"num_deleted": "0", "num_feature": "12", "num_nodes": "3", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "6.3167447E-1", "boost_from_average": "1", "num_class": "0", "num_feature": "12", "num_target": "1"}, "objective": {"name": "binary:logistic", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 0, 3]}
"""Script to profile prism.

Usage:

bazel run services/completion_host/single_model_server/prism_models/profiling:profile_prism -- \
  -c "services/completion_host/single_model_server/prism_models/prism_roguesl_farpref_16B.json" \
  -fe "feature_extractor_v1" \
  -l 128
"""

import argparse
import timeit

import base.tokenizers
from services.completion_host.single_model_server.post_processing import (
    LowQualityFilter,
    LowQualityFilterConfig,
)
from services.lib.request_context.request_context import RequestContext

# pylint: disable=protected-access


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Simple script to profile Starcoder.")
    parser.add_argument("-c", "--checkpoint_path", type=str)
    parser.add_argument(
        "-fe",
        "--feature_extractor_version",
        type=str,
        help="The version of the feature extractor to use. For now, only feature_extractor_v1 is supported.",
    )
    parser.add_argument("-l", "--target_length", type=int)
    parser.set_defaults(target_length=128)
    args = parser.parse_args()

    config = LowQualityFilterConfig(
        checkpoint_path=args.checkpoint_path,
        feature_extractor_version=args.feature_extractor_version,
        default_threshold=1.0,
    )
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder2")
    filter_ = LowQualityFilter(config, tokenizer)
    request_context = RequestContext.create()

    token_ids = list(range(args.target_length))
    log_probs = [0.5] * args.target_length
    prefix = "def foo():\n"
    path = "services/completion_host/single_model_server/post_processing_test.py"
    filter_threshold = 0.8
    repeat = 1000

    time_taken = (
        timeit.timeit(
            lambda: filter_._create_features(token_ids, log_probs, prefix, path),
            number=repeat,
        )
        / repeat
        * 1000
    )
    print(
        f"Average execution time for feature engineering: {time_taken:.8f} milliseconds"
    )

    features = filter_._create_features(token_ids, log_probs, prefix, path)
    time_taken = (
        timeit.timeit(lambda: filter_._to_dmatrix(features), number=repeat)
        / repeat
        * 1000
    )
    print(f"Average execution time for DMatrix creation: {time_taken:.8f} milliseconds")

    time_taken = (
        timeit.timeit(
            lambda: filter_(
                request_context, token_ids, log_probs, prefix, path, filter_threshold
            ),
            number=repeat,
        )
        / repeat
        * 1000
    )
    print(
        f"Average execution time for feature engineering and prediction: {time_taken:.8f} milliseconds"
    )


if __name__ == "__main__":
    main()

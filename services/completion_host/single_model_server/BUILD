load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

filegroup(
    name = "prism_models",
    srcs = glob(["prism_models/**"]),
    visibility = ["//services/completion_host/single_model_server:__subpackages__"],
)

py_library(
    name = "completion_request_insight_builder",
    srcs = [
        "completion_request_insight_builder.py",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/tokenizers",
        "//services/completion_host:completion_proto_py_proto",
        "//services/lib/retrieval:retriever",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/publisher:publisher_py",
        requirement("opentelemetry-api"),
        requirement("prometheus-client"),
    ],
)

py_library(
    name = "handler",
    srcs = ["handler.py"],
    deps = [
        "//services/completion_host:completion_proto_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        requirement("grpcio"),
    ],
)

py_library(
    name = "handler_metrics",
    srcs = ["handler_metrics.py"],
    deps = [
        requirement("prometheus-client"),
    ],
)

py_library(
    name = "single_round_handler",
    srcs = [
        "single_round_handler.py",
    ],
    deps = [
        ":completion_request_insight_builder",
        ":handler",
        ":handler_metrics",
        ":post_processing",
        ":truncate_at_parenthesis",
        "//base/diff_utils:proto_wrapper",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//base/prompt_format_completion",
        "//base/prompt_format_retrieve",
        "//base/tokenizers",
        "//services/completion_host:completion_proto_py_proto",
        "//services/content_manager/client",
        "//services/inference_host:infer_py_proto",
        "//services/inference_host/client",
        "//services/inference_host/client:multiplex",
        "//services/lib/retrieval:retriever",
        requirement("dataclasses_json"),
        requirement("opentelemetry-api"),
        requirement("prometheus-client"),
        requirement("typing_extensions"),
    ],
)

pytest_test(
    name = "single_round_handler_test",
    srcs = ["single_round_handler_test.py"],
    data = [
        ":prism_models/prism_roguesl_farpref_16B.json",
    ],
    deps = [
        ":single_round_handler",
        "//base/python/opentelemetry_utils:traced_threadpool",
        "//services/content_manager/client",
        "//services/inference_host:infer_py_proto",
        "//services/lib/retrieval:null_retriever",
    ],
)

py_library(
    name = "truncate_at_parenthesis",
    srcs = [
        "truncate_at_parenthesis.py",
    ],
    deps = [
        "//base/feature_flags:feature_flags_py",
        "//base/languages",
        "//base/logging:struct_logging",
    ],
)

pytest_test(
    name = "truncate_at_parenthesis_test",
    size = "small",
    srcs = ["truncate_at_parenthesis_test.py"],
    deps = [
        ":truncate_at_parenthesis",
        "//base/feature_flags:feature_flags_py",
    ],
)

py_library(
    name = "post_processing",
    srcs = [
        "post_processing.py",
    ],
    visibility = [
        "//services/completion_host/single_model_server/prism_models/profiling:__subpackages__",
    ],
    deps = [
        ":completion_request_insight_builder",
        "//base/completion_filter:extract_completion_filter_features",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//base/tokenizers",
        requirement("dataclasses_json"),
        requirement("prometheus-client"),
        requirement("typing_extensions"),
        requirement("numpy"),
        requirement("xgboost"),
    ],
)

pytest_test(
    name = "post_processing_test",
    size = "small",
    srcs = ["post_processing_test.py"],
    data = [":prism_models"],
    deps = [
        ":post_processing",
        "//base/feature_flags:feature_flags_py",
        "//base/test_utils:synchronous_executor",
    ],
)

DEPS = [
    ":handler",
    "//services/lib/retrieval:retriever",
    "//services/lib/retrieval:retriever_factory",
    "//services/lib/retrieval:retrieval_collector",
    ":single_round_handler",
    "//base/feature_flags:feature_flags_py",
    "//base/python/opentelemetry_utils:traced_threadpool",
    "//base/tokenizers",
    "//base/tracing:tracing_py",
    "//base/prompt_format_completion",
    "//base/python/grpc:client_options",
    "//base/logging:struct_logging",
    "//base/python/signal_handler",
    "//services/working_set/client:client_py",
    "//services/completion_host:completion_proto_py_proto",
    "//services/inference_host/client",
    "//services/lib/grpc/auth:service_auth",
    "//services/lib/grpc/auth:service_token_auth",
    "//services/lib/grpc/auth:service_auth_interceptor",
    "//services/lib/grpc/metrics:metrics",
    "//services/lib/grpc/tls_config:grpc_tls_config_py",
    "//services/token_exchange/client:client_py",
    requirement("dataclasses_json"),
    requirement("grpcio-reflection"),
    requirement("grpcio"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("protobuf"),
    requirement("prometheus-client"),
    "//services/lib/grpc/stream_mux:server",
]

py_binary(
    name = "completion_server",
    srcs = [
        "completion_server.py",
    ],
    data = [
        ":prism_models",
        # it would be better to mount these in via OCI images, but that is only available in k8s 1.31
        "@prism_eldenv4_model//:all",
    ],
    deps = DEPS,
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":completion_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":monitoring_kubecfg",
    ],
)

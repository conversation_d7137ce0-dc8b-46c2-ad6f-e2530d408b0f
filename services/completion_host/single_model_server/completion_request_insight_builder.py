"""Helper to build request insight events during a completion request."""

from typing import Collection, Optional, Sequence

from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
import opentelemetry.trace

import base.tokenizers
from services.completion_host import completion_pb2
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher

opentelemetry_tracer = opentelemetry.trace.get_tracer(__name__)


# TODO: due to python pass-by-assignment we may want to inline the Tokenization field setting
def _get_tokenization_proto(
    tokenizer: base.tokenizers.Tokenizer,
    token_ids: Sequence[int],
    log_probs: Optional[Sequence[float]],
) -> request_insight_pb2.Tokenization:
    text, offsets = tokenizer.detokenize_with_offsets(token_ids)
    return request_insight_pb2.Tokenization(
        token_ids=token_ids,
        text=text,
        offsets=offsets,
        log_probs=log_probs or [],
    )


class CompletionRequestInsightBuilder:
    """Builder to log request insight events during a completion request."""

    def __init__(
        self,
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        self.ri_publisher = ri_publisher

    def record_request(
        self,
        input_tokens: Sequence[int],
        seed: int,
        eos_token_ids: Collection[int],
        tokenizer: base.tokenizers.Tokenizer,
        request: completion_pb2.CompletionRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        top_k: int,
        top_p: float,
        temperature: float,
    ):
        """Record a completion request event."""
        with opentelemetry_tracer.start_as_current_span("create_request_event"):
            request_event = request_insight_publisher.new_event()
            # this should set fields in CompletionHostRequest in request_insight.proto
            completion_host_request = request_event.completion_host_request

            completion_host_request.model = request.model_name
            completion_host_request.prefix = request.prefix
            completion_host_request.suffix = request.suffix
            completion_host_request.path = request.path or ""

            if request.HasField("blobs"):
                completion_host_request.blobs.CopyFrom(request.blobs)

            completion_host_request.lang = request.lang
            completion_host_request.top_k = top_k
            completion_host_request.top_p = top_p
            completion_host_request.temperature = temperature
            completion_host_request.output_len = request.max_tokens
            completion_host_request.probe_only = request.probe_only
            if request.HasField("filter_threshold"):
                completion_host_request.user_filter_threshold = request.filter_threshold

            completion_host_request.recency_info.recent_changes.extend(
                request.recency_info.recent_changes
            )

            completion_host_request.recency_info.tab_switch_events.extend(
                request.recency_info.tab_switch_events
            )

            completion_host_request.recency_info.git_diff_file_info.extend(
                request.recency_info.git_diff_file_info
            )

            completion_host_request.edit_events.extend(request.edit_events)

            if request.HasField("position"):
                target_pos = completion_host_request.position
                src_pos = request.position
                target_pos.blob_name = src_pos.blob_name
                target_pos.cursor_position = src_pos.cursor_position
                target_pos.prefix_begin = src_pos.prefix_begin
                target_pos.suffix_end = src_pos.suffix_end

            completion_host_request.seed = seed
            completion_host_request.eos_token_id.extend(eos_token_ids)

            completion_host_request.tokenization.CopyFrom(
                _get_tokenization_proto(tokenizer, input_tokens, None)
            )

        update_request_info_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [request_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request_info_request)

    def record_response(
        self,
        output_tokens: Sequence[int],
        output_text: str,
        truncated_output_tokens: Sequence[int],
        unknown_blob_names: Sequence[str],
        skipped_suffix: str,
        suffix_replacement_text: str,
        truncated_log_probs: Optional[Sequence[float]],
        log_probs: Optional[Sequence[float]],
        tokenizer: base.tokenizers.Tokenizer,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a completion response event."""
        if log_probs is None:
            log_probs = []
        if truncated_log_probs is None:
            truncated_log_probs = []
        inference_response_event = request_insight_publisher.new_event()
        inference_host_response = inference_response_event.inference_host_response
        # set inference response request insight data
        inference_host_response.tokenization.CopyFrom(
            _get_tokenization_proto(tokenizer, output_tokens, log_probs)
        )

        event = request_insight_publisher.new_event()

        event.completion_host_response.text = output_text
        event.completion_host_response.unknown_blob_names.extend(unknown_blob_names)
        event.completion_host_response.skipped_suffix = skipped_suffix
        event.completion_host_response.suffix_replacement_text = suffix_replacement_text

        event.completion_host_response.tokenization.CopyFrom(
            _get_tokenization_proto(
                tokenizer, truncated_output_tokens, truncated_log_probs
            )
        )

        update_request_info_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [inference_response_event, event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request_info_request)

    def record_completion_post_process(
        self,
        filter_score: Optional[float],
        applied_filter_threshold: Optional[float],
        filter_reason: request_insight_pb2.CompletionPostProcess.FilterReason.ValueType,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a completion post process event."""
        event = request_insight_publisher.new_event()
        event.completion_post_process.filter_score = filter_score or 0.0
        if applied_filter_threshold is not None:
            event.completion_post_process.applied_filter_threshold = (
                applied_filter_threshold
            )
        event.completion_post_process.filter_reason = filter_reason

        update_request_info_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request_info_request)

    def record_parenthesis_truncation(
        self,
        original_text: str,
        truncated_text: str,
        was_truncated: bool,
        could_have_truncated: bool,
        path: str,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record parenthesis truncation data for analysis.

        Args:
            original_text: The original text before any truncation
            truncated_text: The text after truncation (if applied)
            was_truncated: Whether truncation was actually applied
            could_have_truncated: Whether truncation could have been applied
            request_context: The request context
            auth_info: Authentication information
        """
        event = request_insight_publisher.new_event()

        event.parenthesis_truncation.original_text = original_text
        event.parenthesis_truncation.truncated_text = truncated_text
        event.parenthesis_truncation.was_truncated = was_truncated
        event.parenthesis_truncation.could_have_truncated = could_have_truncated
        event.parenthesis_truncation.path = path

        update_request_info_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request_info_request)

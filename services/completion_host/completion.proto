syntax = "proto3";
package completion;

import "base/blob_names/blob_names.proto";
import "base/diff_utils/edit_events.proto";
import "services/lib/grpc/stream_mux/stream_mux.proto";

// Completion RPC

service Completion {
  rpc Complete(CompletionRequest) returns (CompletionResponse);

  // Create a persistent connection to perform multiple completion requests
  // to the same server.
  // Server responds to each request with exactly one response, or an error.
  rpc CompleteSession(stream SessionRequest) returns (stream SessionResponse);

  rpc FindMissing(FindMissingRequest) returns (FindMissingResponse);
}

// Complete specification of where the completion happens in the user's current file.
// Some properties you may want to know about:
// - the current file's content can always be reconstructed by loading `blob_name` and
//   replacing its text between `prefix_begin` and `suffix_end` with `prefix + suffix`.
message CompletionPosition {
  // the offset in characters where the prefix begins relative to `blob_name`.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 prefix_begin = 1;

  // the offset in characters where the prefix begins relative to the current
  // file. This is equivalent to `prefix_begin + len(prefix)`.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 cursor_position = 2;

  // the offset in characters where the suffix ends relative to `blob_name`.
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  int32 suffix_end = 3;

  // the (last known) blob name of the current file
  string blob_name = 4;
}

message RNGSeed {
  // the seed for the random number generator
  uint64 value = 1;
}

// tab-switch event on the client
message TabSwitchEvent {
  // The path that was switched to
  string path = 1 [debug_redact = true];

  // The blob name of the file that was switched to
  string file_blob_name = 2;
}

// git-diff output for one file on the client
message GitDiffFileInfo {
  // The blob name that contains the contents of the diff
  string content_blob_name = 1;

  // The blob name of the file that the diff affects
  string file_blob_name = 2;
}

// recent content written by the client
message ReplacementText {
  // The blob name and path that this change applies to
  string blob_name = 1;
  string path = 2 [debug_redact = true];
  // The start and end of the modified region of the blob
  // TODO(AU-863): This is currently actually the number of UTF-16 code units,
  // which can differ from the number of Unicode characters.
  uint32 char_start = 3;
  uint32 char_end = 4;
  // The new content of the modified region
  string replacement_text = 5 [debug_redact = true];
  // Indicates whether the blob already contains this change
  bool present_in_blob = 6;
  // The expected blob name of the file after applying all replacement text
  optional string expected_blob_name = 7;
}

// recent client events
message RecencyInfo {
  repeated TabSwitchEvent tab_switch_events = 1;
  repeated GitDiffFileInfo git_diff_file_info = 2;
  repeated ReplacementText recent_changes = 3;
}

message CompletionRequest {
  string model_name = 1;

  // the prefix prompt at which the completion should be inserted
  string prefix = 2 [debug_redact = true];

  // relative file path to a given relevant root (e.g. the .augmentroot file).
  string path = 3 [debug_redact = true];

  // an optional suffix for fill-in-the-middle.
  // usually the text after the cursor location at which the completion should
  // be inserted
  string suffix = 4 [debug_redact = true];

  reserved 5, 6, 7, 8;

  // maximal number of tokens to generate
  // the server is free to generate less tokens than the value provided.
  int32 max_tokens = 9;

  // programming laguage that the prompt is in.
  //
  // the language should be one of the languages supported by the model.
  // if not set (or empty), the completion host might detect a language based on the path
  // or use an undefined default.
  string lang = 10;

  CompletionPosition position = 11;

  // truncation contains text strings at which the output text should
  // be truncated, e.g. a newline, even if the model generated additional
  // text.
  repeated string truncation = 12;

  // random seed for the completion
  // Use default value if not set.
  RNGSeed seed = 13;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  uint32 sequence_id = 14;

  // probe the server to return the current indexing status;
  // when true, CompletionResponse will only include unknown_blob_names and text will always be
  // empty
  bool probe_only = 15;

  // Blobs to include in the retrieval corpus, specified in the compact
  // delta form. Replaces the blob_names field.
  base.blob_names.Blobs blobs = 16;

  // recent events from the client
  RecencyInfo recency_info = 17;

  // A user filter threshold (undefined, 0.0 to 1.0) for filtering low quality completions.
  optional float filter_threshold = 18;

  // More granular events representing the most recent edits.
  repeated base.diff_utils.GranularEditEvent edit_events = 19;
}

// IMPORTANT:
// If the CHECKPOINT_NOT_FOUND field is set to True, retrieval failed to resolve
// the given baseline_checkpoint_id. This condition will persist until the client
// either regenerates the checkpoint or retries with a different checkpoint_id.
message CompletionResponse {
  // the completion text to be inserted
  string text = 1 [debug_redact = true];
  // these are un-indexed blob names
  repeated string unknown_blob_names = 2;

  // Skip token handling
  // for details see comments in public_api.proto
  string skipped_suffix = 3 [debug_redact = true];
  string suffix_replacement_text = 4 [debug_redact = true];

  // Set to True if the checkpoint was not found.
  bool checkpoint_not_found = 5;

  // A score representing the likelihood of rejection (0.0 to 1.0), based on the completions filter.
  optional float filter_score = 6;
}

message SessionRequest {
  stream_mux.MuxedRequest context = 1;
  CompletionRequest request = 2;
}

message SessionResponse {
  stream_mux.MuxedResponse context = 1;
  // If context.status_code OK, the result of the completion request
  CompletionResponse response = 2;
}

message FindMissingRequest {
  // The model to probe for missing and non-indexed blobs.
  string model_name = 1;

  // The collection of blob names.
  repeated string blob_names = 2;
}

message FindMissingResponse {
  // (deprecated, call ContentManager instead) The subset of blob_names that are completely unknown.
  repeated string missing_blob_names = 1 [deprecated = true];

  // The subset of blob names that are not indexed for the given model.
  repeated string nonindexed_blob_names = 2;
}

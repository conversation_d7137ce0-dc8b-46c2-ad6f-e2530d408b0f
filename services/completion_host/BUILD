load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

exports_files(
    ["deploy.jsonnet"],
    visibility = ["//services/deploy:__subpackages__"],
)

proto_library(
    name = "completion_proto",
    srcs = ["completion.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//base/diff_utils:edit_events_proto",
        "//services/lib/grpc/stream_mux:stream_mux_proto",
        "@protobuf//:duration_proto",
    ],
)

py_grpc_library(
    name = "completion_proto_py_proto",
    protos = [":completion_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/diff_utils:edit_events_py_proto",
        "//services/lib/grpc/stream_mux:stream_mux_py_proto",
    ],
)

go_grpc_library(
    name = "completion_host_go_proto",
    importpath = "github.com/augmentcode/augment/services/completion_host/proto",
    proto = ":completion_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/diff_utils:edit_events_go_proto",
        "//services/lib/grpc/stream_mux:stream_mux_go_proto",
    ],
)

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":completion_proto_py_proto",
        "//base/python/grpc:client_options",
        "//services/lib/grpc/stream_mux:client",
        "//services/lib/request_context:request_context_py",
        requirement("opentelemetry-api"),
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["client_test.py"],
    deps = [
        ":client",
    ],
)

# a small CLI utility to issue requests against the completion host API
py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        ":client",
        "//base/logging:console_logging",
        "//services/lib/request_context:request_context_py",
    ],
)

ts_proto_library(
    name = "completion_host_ts_proto",
    node_modules = "//:node_modules",
    proto = ":completion_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_ts_proto",
        "//base/diff_utils:edit_events_ts_proto",
        "//services/lib/grpc/stream_mux:stream_mux_ts_proto",
    ],
)

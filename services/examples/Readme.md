# Service Examples

Examples for GRPC clients and servers in different programming languages
using the [route guide service](https://github.com/grpc/grpc/blob/master/examples/protos/route_guide.proto).

The examples use Augment specific setups, e.g. with structured logging, tracing, etc.

When setting up a new service, please use the examples as a starting point.

# Protobuf

The examples are based on the [gRPC Python Quickstart](https://grpc.io/docs/languages/python/quickstart/).

This directory contains the protobuf definitions for the route guide service

## Python

The protobuf definitions are available via the `py_library` target `//services/examples:route_guide_py_proto`.

Run `//tools/generate_proto_typestubs` to generate the type stubs.

## Go

The protobuf definitions are available via the `go_proto_library` target `//services/examples:route_guide_go_proto`.

Run `//services/examples:route_guide_go_proto.generate_stubs` to generate the type stubs.

## Typescript

The protobuf definitions are available via the `ts_proto_library` target `//services/examples:route_guide_ts_proto`.

The typescript stubs are checked into the repo as generated code.

Run `//services/examples:route_guide_ts_proto.copy` to update the generated code.
Note that the generated code is checked into the repo as ".d.ts" files.

## Rust

The generated code for Rust is generated by `build.rs` and exposed as a `rust_library` target `route_guide_rs_proto`.
Users of the the library should depend on this target and use the crate `route_guide_rs_proto`, see e.g.
`//services/examples/rust/server:server`.

# Route Guide Client

A client for the route guide service in different programming languages.

The client is based on the [gRPC Python Quickstart](https://grpc.io/docs/languages/python/quickstart/).

## Python

### Usage

```python
from services.examples.python.client import client

client = client.RouteGuideClient.create_for_endpoint(
    endpoint="localhost:50051",
    credentials=None,
)

feature = client.get_feature(
    latitude=37.4219983,
    longitude=-122.0851590,
)
```

## Go

### Usage

```go
client, err := client.NewRouteGuideClient(
    endpoint="localhost:50051",
    credentials=nil,
)

feature, err := client.GetFeature(
    ctx,
    &pb.Point{Latitude: 374219983, Longitude: -1220851590},
    requestContext,
)
```

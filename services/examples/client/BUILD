load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services/examples:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/examples:route_guide_py_proto",
        "//services/lib/grpc:stub_cycler",
        "//services/lib/request_context:request_context_py",
    ],
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/examples/client",
    deps = [
        "//services/examples:route_guide_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

# Build test to ensure that the client code can be built.
build_test(
    name = "client_build_test",
    targets = [
        ":client_py",
        ":client_go",
    ],
)

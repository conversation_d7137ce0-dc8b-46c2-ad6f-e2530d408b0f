package client

// Package client provides a client for the route guide service.

import (
	"context"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/examples/proto"
)

// RouteGuideClient interface for the route guide service. See route_guide.proto for details.
type RouteGuideClient interface {
	GetFeature(ctx context.Context, point *pb.Point, requestContext *requestcontext.RequestContext) (*pb.Feature, error)
	ListFeatures(ctx context.Context, rectangle *pb.Rectangle, requestContext *requestcontext.RequestContext) (<-chan *pb.Feature, error)
	RecordRoute(ctx context.Context, points []*pb.Point, requestContext *requestcontext.RequestContext) (*pb.RouteSummary, error)
	RouteChat(ctx context.Context, notes []*pb.RouteNote, requestContext *requestcontext.RequestContext) (pb.RouteGuide_RouteChatClient, error)

	// This should be called to cleanup resources for this client
	Close()
}

// RouteGuideClientImpl implementation of the RouteGuideClient interface.
type RouteGuideClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	client pb.RouteGuideClient
}

// NewRouteGuideClient creates a new RouteGuideClient.
//
// endpoint: The endpoint of the route guide service.
// credentials: The credentials to use for the channel (optional)
//
// Returns: The client stub for the route guide or an error if the client could not be created.
func NewRouteGuideClient(endpoint string, credentials credentials.TransportCredentials) (RouteGuideClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := pb.NewRouteGuideClient(conn)
	return &RouteGuideClientImpl{conn: conn, client: client}, nil
}

func (c *RouteGuideClientImpl) GetFeature(ctx context.Context, point *pb.Point, requestContext *requestcontext.RequestContext) (*pb.Feature, error) {
	req := &pb.GetFeatureRequest{Point: point}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetFeature(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Feature, nil
}

func (c *RouteGuideClientImpl) ListFeatures(ctx context.Context, rectangle *pb.Rectangle, requestContext *requestcontext.RequestContext) (<-chan *pb.Feature, error) {
	req := &pb.ListFeaturesRequest{Rectangle: rectangle}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.ListFeatures(ctx, req)
	if err != nil {
		return nil, err
	}
	ch := make(chan *pb.Feature)
	go func() {
		for {
			r, err := stream.Recv()
			if err != nil {
				close(ch)
				return
			}
			ch <- r.Feature
		}
	}()
	return ch, nil
}

func (c *RouteGuideClientImpl) RecordRoute(ctx context.Context, points []*pb.Point, requestContext *requestcontext.RequestContext) (*pb.RouteSummary, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.RecordRoute(ctx)

	for _, point := range points {
		resp.Send(point)
	}
	if err != nil {
		return nil, err
	}
	return resp.CloseAndRecv()
}

func (c *RouteGuideClientImpl) RouteChat(ctx context.Context, notes []*pb.RouteNote, requestContext *requestcontext.RequestContext) (pb.RouteGuide_RouteChatClient, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.RouteChat(ctx)
}

func (c *RouteGuideClientImpl) Close() {
	c.conn.Close()
}

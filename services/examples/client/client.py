"""A Python client library call a route guide service."""

import logging
import typing
from typing import Callable, Optional

import grpc

from base.python.grpc import client_options
from services.examples import route_guide_pb2, route_guide_pb2_grpc
from services.lib.grpc import stub_cycler
from services.lib.request_context.request_context import Request<PERSON>ontext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> route_guide_pb2_grpc.RouteGuideStub:
    """Setup the client stub for a completion service.

    Args:
        endpoint: The endpoint of the route guide service.
        credentials: The credentials to use for the channel (optional)

    Returns:
        The client stub for the route guide.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = route_guide_pb2_grpc.RouteGuideStub(channel)
    return stub


class RouteGuideClient:
    """Class to call route guide APIs remotely."""

    def __init__(self, stub_factory: Callable[[], route_guide_pb2_grpc.RouteGuideStub]):
        """Constructs a new content manager."""
        self.stub_cycler = stub_cycler.StubCycler(stub_factory)

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new content manager client from endpoint and credentials.

        Args:
            endpoint: The endpoint of the route guide service.
            credentials: The credentials to use for the channel (optional)
            options: The options to use for the channel (optional)

        Returns:
            The client stub for the route guide.
        """

        def _stub_factory():
            return setup_stub(endpoint, credentials, options=options)

        return cls(_stub_factory)

    def get_feature(
        self,
        point: route_guide_pb2.Point,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> route_guide_pb2.Feature:
        """Get a feature.

        Args:
            point: The point to get the feature for.
            request_context: The request context to use.
            timeout: The timeout to use.

        Returns:
            The feature.
        """
        response = self.stub_cycler.get_stub().GetFeature(
            route_guide_pb2.GetFeatureRequest(point=point),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.feature

    def list_features(
        self,
        rectangle: route_guide_pb2.Rectangle,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> typing.Iterable[route_guide_pb2.Feature]:
        """List features.

        Args:
            rectangle: The rectangle to list features for.
            request_context: The request context to use.
            timeout: The timeout to use.

        Returns:
            The features.
        """
        for response in self.stub_cycler.get_stub().ListFeatures(
            route_guide_pb2.ListFeaturesRequest(rectangle=rectangle),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        ):
            yield response.feature

    def record_route(
        self,
        points: typing.Iterable[route_guide_pb2.Point],
        request_context: RequestContext,
        timeout: float = 30,
    ) -> route_guide_pb2.RouteSummary:
        """Record a route.

        Args:
            points: The points to record.
            request_context: The request context to use.
            timeout: The timeout to use.

        Returns:
            The route summary.
        """
        response = self.stub_cycler.get_stub().RecordRoute(
            points, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

    def route_chat(
        self,
        notes: typing.Iterable[route_guide_pb2.RouteNote],
        request_context: RequestContext,
        timeout: float = 30,
    ) -> typing.Iterable[route_guide_pb2.RouteNote]:
        """Route chat.

        Args:
            notes: The notes to route chat for.
            request_context: The request context to use.
            timeout: The timeout to use.

        Returns:
            The route notes.
        """
        for response in self.stub_cycler.get_stub().RouteChat(
            notes, timeout=timeout, metadata=request_context.to_metadata()
        ):
            yield response

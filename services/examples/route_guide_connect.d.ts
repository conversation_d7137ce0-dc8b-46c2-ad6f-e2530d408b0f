// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/examples/route_guide.proto (package routeguide, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetFeatureRequest, GetFeatureResponse, ListFeaturesRequest, ListFeaturesResponse, Point, RouteNote, RouteSummary } from "./route_guide_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service routeguide.RouteGuide
 */
export declare const RouteGuide: {
  readonly typeName: "routeguide.RouteGuide",
  readonly methods: {
    /**
     * @generated from rpc routeguide.RouteGuide.GetFeature
     */
    readonly getFeature: {
      readonly name: "GetFeature",
      readonly I: typeof GetFeatureRequest,
      readonly O: typeof GetFeatureResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc routeguide.RouteGuide.ListFeatures
     */
    readonly listFeatures: {
      readonly name: "ListFeatures",
      readonly I: typeof ListFeaturesRequest,
      readonly O: typeof ListFeaturesResponse,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * @generated from rpc routeguide.RouteGuide.RecordRoute
     */
    readonly recordRoute: {
      readonly name: "RecordRoute",
      readonly I: typeof Point,
      readonly O: typeof RouteSummary,
      readonly kind: MethodKind.ClientStreaming,
    },
    /**
     * @generated from rpc routeguide.RouteGuide.RouteChat
     */
    readonly routeChat: {
      readonly name: "RouteChat",
      readonly I: typeof RouteNote,
      readonly O: typeof RouteNote,
      readonly kind: MethodKind.BiDiStreaming,
    },
  }
};


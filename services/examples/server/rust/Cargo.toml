[package]
name = "example_server"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "example_server"
path = "main.rs"

[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
tonic = { workspace = true , features = ["tls", "gzip", "tls-roots"] }
tonic-build = { workspace = true }
async-trait = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
async-std = "*"
futures = { workspace = true }
async-rwlock = { workspace = true }
itertools = { workspace = true }
clap = { workspace = true }
prometheus = { workspace = true }
lazy_static = { workspace = true }
uuid = { workspace = true }
tracing = { workspace = true }
prost = {workspace = true}
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
async-lock = { workspace = true }
pin-project = { workspace = true }
tonic-health = { workspace = true }
tonic-reflection = { workspace = true }
chrono = { workspace = true }
tracing-tonic = { path = "../../../../base/rust/tracing-tonic" }
hyper = { workspace = true }
tower = { workspace = true }
struct_logging = { path = "../../../../base/logging" }
metrics-server = { path = "../../../../base/metrics_server/rust" }
grpc_metrics = { path = "../../../lib/grpc/metrics" }
grpc_auth = { path = "../../../lib/grpc/auth" }
grpc_tls_config = { path = "../../../lib/grpc/tls_config" }
token_exchange_client = { path = "../../../token_exchange/client" }
request_context = { path = "../../../lib/request_context" }
route_guide_rs_proto = { path = "../.." }
feature-flags = { path = "../../../../base/feature_flags" }

[dev-dependencies]
actix-rt =  { workspace = true }
assert_unordered =  { workspace = true }
tonic-build = { workspace = true }
prost-build = {workspace = true}

[build-dependencies]
tonic-build = { workspace = true }

use std::{net::SocketAddr, sync::Arc, time::Duration};

use clap::Parser;
use config::CliArguments;
use grpc_auth::GrpcAuthMiddlewareLayer;
use grpc_metrics::MetricsMiddlewareLayer;
use grpc_tls_config::{get_client_tls_creds, get_server_tls_creds};
use struct_logging::setup_struct_logging;
use token_exchange_client::{TokenExchangeClientImpl, TokenGrpcAuth};
use tokio::signal::unix::{signal, SignalKind};
use tonic::transport::Server;

use crate::config::Config;
use crate::metrics::{ACTIVE_REQUESTS_COLLECTOR, RESPONSE_LATENCY_COLLECTOR};

mod config;
mod metrics;
mod route_guide_service;

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    // Load config
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    // setup feature flags
    // Remove underscores from feature flags stuff when it's actually used
    let _feature_flags = feature_flags::setup(
        "route_guide",
        "0.0.0",
        args.launch_darkly_secrets_file.as_ref(),
        config.dynamic_feature_flags_endpoint.as_deref(),
    )
    .await;
    let _registry = feature_flags::new_registry();

    let namespace = match std::env::var("POD_NAMESPACE") {
        Ok(name) => name,
        Err(_) => panic!("POD_NAMESPACE environment variable must be set."),
    };

    // setup health service
    let (_health_reporter, health_service) = tonic_health::server::health_reporter();

    metrics_server::setup_default_metrics();

    // setup metrics server
    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    // setup server
    let server_tls_config =
        get_server_tls_creds(&config.server_mtls_config).expect("Failed to create TLS config");

    let addr: SocketAddr = config.bind_address.parse()?;
    let server = match server_tls_config {
        None => Server::builder(),
        Some(server_tls_config) => Server::builder()
            .tls_config(server_tls_config)
            .expect("Failed to create rpc server"),
    };

    // setup token exchange client
    let central_client_tls_config = get_client_tls_creds(&config.central_client_mtls_config)
        .expect("Failed to create TLS config");

    // setup token exchange client
    let token_exchange_client = Arc::new(TokenExchangeClientImpl::new(
        &config.auth_config.token_exchange_endpoint,
        namespace.to_string(),
        central_client_tls_config,
        Duration::from_secs_f32(config.auth_config.token_exchange_request_timeout_s),
    ));

    // setup grpc auth
    let grpc_auth = Arc::new(TokenGrpcAuth::new(
        token_exchange_client.clone(),
        vec![request_context::TokenScope::ContentR],
    ));

    // setup route guide service
    let route_guide_service = route_guide_service::RouteGuideImpl::new(config);

    let reflection_service: tonic_reflection::server::ServerReflectionServer<_> =
        tonic_reflection::server::Builder::configure()
            .register_encoded_file_descriptor_set(route_guide_rs_proto::FILE_DESCRIPTOR_SET)
            .build_v1()?;

    // setup server
    let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

    let listener = tokio::net::TcpListener::bind(addr)
        .await
        .expect("Failed to bind");
    tracing::info!(
        "Listening on {:?}",
        listener.local_addr().expect("Failed to get local address")
    );

    let server = server
        .timeout(Duration::from_secs(300))
        .trace_fn(tracing_tonic::server::trace_fn)
        // layers
        // - metrics
        // - grpc auth
        .layer(
            tower::ServiceBuilder::new()
                .layer(MetricsMiddlewareLayer::new(
                    &RESPONSE_LATENCY_COLLECTOR,
                    &ACTIVE_REQUESTS_COLLECTOR,
                ))
                .layer(GrpcAuthMiddlewareLayer::new(grpc_auth))
                .into_inner(),
        )
        .add_service(health_service)
        .add_service(route_guide_service.new_server())
        .add_service(reflection_service)
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(listener),
            async move {
                sigterm_notifier.recv().await;
            },
        );

    // wait for server to finish
    let res = futures::future::join(server, metrics_server).await;
    panic!("servers done: {res:?}");
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}

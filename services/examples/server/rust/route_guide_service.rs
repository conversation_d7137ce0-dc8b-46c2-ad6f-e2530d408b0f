use futures::Future;
use futures::StreamExt;
use grpc_auth::tenant_info_from_grpc_req;
use request_context::{RequestContext, TokenScope};
use route_guide_rs_proto::route_guide::route_guide_server::RouteGuide;
use route_guide_rs_proto::route_guide::route_guide_server::RouteGuideServer;
use tokio::sync::mpsc;
use tokio_stream::wrappers::ReceiverStream;
use tracing::Instrument;

use crate::config::Config;

// log an rpc response either as debug or error depending on the status
pub async fn log_response_fn<ResponseT, F, Fut>(f: F) -> Result<ResponseT, tonic::Status>
where
    ResponseT: std::fmt::Debug,
    F: FnOnce() -> Fut,
    Fut: Future<Output = Result<ResponseT, tonic::Status>>,
{
    let resp = f().await;
    log_response::<ResponseT>(resp)
}

// log an rpc response either as debug or error depending on the status
pub fn log_response<ResponseT>(
    resp: Result<ResponseT, tonic::Status>,
) -> Result<ResponseT, tonic::Status>
where
    ResponseT: std::fmt::Debug,
{
    match &resp {
        Err(status) => {
            tracing::error!("response: err {:?}", status);
        }
        Ok(resp) => {
            tracing::debug!("response: {:?}", resp);
        }
    }
    resp
}

// RouteGuideImpl is the implementation of the RouteGuide service
#[derive(Clone)]
pub struct RouteGuideImpl {
    _config: Config,
}

impl RouteGuideImpl {
    // Create a new RouteGuide service
    pub fn new(config: Config) -> Self {
        RouteGuideImpl {
            _config: config.clone(),
        }
    }

    // Create a new RouteGuide server
    pub fn new_server(self) -> RouteGuideServer<Self> {
        RouteGuideServer::new(self)
    }
}

#[tonic::async_trait]
impl RouteGuide for RouteGuideImpl {
    async fn get_feature(
        &self,
        request: tonic::Request<route_guide_rs_proto::route_guide::GetFeatureRequest>,
    ) -> Result<tonic::Response<route_guide_rs_proto::route_guide::GetFeatureResponse>, tonic::Status>
    {
        // get request context
        let request_context = RequestContext::try_from(request.metadata())?;

        // get tenant info
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        // create span for logging
        let span = tracing::info_span!("get feature", request_id = %request_context.request_id());

        log_response_fn(|| async {
            let inner_request = request.into_inner();

            // validate scope
            // we only need read access
            // Replace with your own scope if required
            tenant_info.validate_scope(TokenScope::ContentR)?;

            let point = inner_request
                .point
                .ok_or_else(|| tonic::Status::invalid_argument("point must be set"))?;

            // just as an example, we only support a single point
            if point.latitude != 374219983 || point.longitude != -1220851590 {
                return Err(tonic::Status::not_found("Failed to find feature"));
            }

            let feature = route_guide_rs_proto::route_guide::Feature {
                name: "Augment HQ".to_string(),
                location: Some(point),
            };
            let reply = route_guide_rs_proto::route_guide::GetFeatureResponse {
                feature: Some(feature),
            };
            Ok(tonic::Response::new(reply))
        })
        .instrument(span)
        .await
    }

    type ListFeaturesStream =
        ReceiverStream<tonic::Result<route_guide_rs_proto::route_guide::ListFeaturesResponse>>;

    async fn list_features(
        &self,
        request: tonic::Request<route_guide_rs_proto::route_guide::ListFeaturesRequest>,
    ) -> Result<tonic::Response<Self::ListFeaturesStream>, tonic::Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span = tracing::info_span!("list features", request_id = %request_context.request_id());
        log_response_fn(|| async {
            tenant_info.validate_scope(TokenScope::ContentR)?;
            let inner_request = request.into_inner();
            let rect = inner_request
                .rectangle
                .ok_or_else(|| tonic::Status::invalid_argument("rectangle must be set"))?;
            tracing::info!("ListFeatures: {:?}", rect);

            // create a channel
            let (tx, rx) = mpsc::channel::<
                tonic::Result<route_guide_rs_proto::route_guide::ListFeaturesResponse>,
            >(10);

            // spawn a task to send the response
            tokio::spawn(async move {
                let feature = route_guide_rs_proto::route_guide::Feature {
                    name: "Augment HQ".to_string(),
                    location: Some(route_guide_rs_proto::route_guide::Point {
                        latitude: 374219983,
                        longitude: -1220851590,
                    }),
                };
                let resp = route_guide_rs_proto::route_guide::ListFeaturesResponse {
                    feature: Some(feature),
                };
                tx.send(Ok(resp)).await.unwrap();
            });
            Ok(tonic::Response::new(ReceiverStream::new(rx)))
        })
        .instrument(span)
        .await
    }

    async fn record_route(
        &self,
        request: tonic::Request<tonic::Streaming<route_guide_rs_proto::route_guide::Point>>,
    ) -> Result<tonic::Response<route_guide_rs_proto::route_guide::RouteSummary>, tonic::Status>
    {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span = tracing::info_span!("record route", request_id = %request_context.request_id());
        log_response_fn(|| async {
            tenant_info.validate_scope(TokenScope::ContentRw)?;
            let mut point_count = 0;
            let mut stream = request.into_inner();
            // loop through the stream
            while let Some(point) = stream.next().await {
                tracing::info!("RecordRoute: {:?}", point);
                point_count += 1;
            }
            let reply = route_guide_rs_proto::route_guide::RouteSummary {
                point_count,
                feature_count: 0,
                distance: 0,
                elapsed_time: 0,
            };
            Ok(tonic::Response::new(reply))
        })
        .instrument(span)
        .await
    }

    type RouteChatStream =
        ReceiverStream<tonic::Result<route_guide_rs_proto::route_guide::RouteNote>>;

    async fn route_chat(
        &self,
        request: tonic::Request<tonic::Streaming<route_guide_rs_proto::route_guide::RouteNote>>,
    ) -> Result<tonic::Response<Self::RouteChatStream>, tonic::Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
        let span = tracing::info_span!("route chat", request_id = %request_context.request_id());
        log_response_fn(|| async {
            tenant_info.validate_scope(TokenScope::ContentRw)?;
            // create a channel
            let (tx, rx) = mpsc::channel(10);
            tokio::spawn(async move {
                tx.send(Ok(route_guide_rs_proto::route_guide::RouteNote {
                    message: "Augment HQ".to_string(),
                    location: Some(route_guide_rs_proto::route_guide::Point {
                        latitude: 374219983,
                        longitude: -1220851590,
                    }),
                }))
                .await
                .unwrap();
            });
            Ok(tonic::Response::new(ReceiverStream::new(rx)))
        })
        .instrument(span)
        .await
    }
}

use clap::Parser;
use grpc_tls_config::TlsConfig;
use serde::{Deserialize, Serialize};
use std::{fs::File, path::Path};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AuthConfig {
    pub token_exchange_endpoint: String,
    pub token_exchange_request_timeout_s: f32,
}

/// structure representing the configuration information in the configuration file, i.e.
/// the configmap of the embeddings search pod.
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Config {
    // the address to bind the rpc server to, e.g. 0.0.0.0:50051
    pub bind_address: String,

    // Configure the HTTP server that returns Prometheus metrics.
    pub metrics_server_bind_address: String,
    pub metrics_server_port: u16,

    // populated if server MTLS should be used.
    pub server_mtls_config: Option<TlsConfig>,
    // populated if central client MTLS should be used.
    pub central_client_mtls_config: Option<TlsConfig>,
    // populated if client MTLS should be used.
    pub client_mtls_config: Option<TlsConfig>,

    // the configuration for the token authentication
    pub auth_config: AuthConfig,

    pub dynamic_feature_flags_endpoint: Option<String>,
}

#[cfg(test)]
impl Default for Config {
    fn default() -> Self {
        Self {
            bind_address: "0.0.0.0:50051".to_string(),
            metrics_server_bind_address: "0.0.0.0".to_string(),
            metrics_server_port: 8080,

            server_mtls_config: None,
            central_client_mtls_config: None,
            client_mtls_config: None,

            auth_config: AuthConfig {
                token_exchange_endpoint: "".to_string(),
                token_exchange_request_timeout_s: 10.0,
            },
            dynamic_feature_flags_endpoint: None,
        }
    }
}

impl Config {
    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<Config, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: Config =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(config)
    }
}

/// Search for a pattern in a file and display the lines that contain it.
#[derive(Parser, Debug)]
pub struct CliArguments {
    /// path to the configuration file
    #[arg(long)]
    pub config_file: std::path::PathBuf,

    /// optional path to the launch darkly secrets
    #[arg(long)]
    pub launch_darkly_secrets_file: Option<std::path::PathBuf>,
}

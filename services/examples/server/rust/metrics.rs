use lazy_static::lazy_static;
use prometheus::{register_histogram_vec, register_int_gauge_vec, HistogramVec, IntGaugeVec};

lazy_static! {
    pub static ref RESPONSE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_latency_histogram",
        "Histogram of RPC latencies",
        &["service", "endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");
    pub static ref ACTIVE_REQUESTS_COLLECTOR: IntGaugeVec = register_int_gauge_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_active_requests_gauge",
        "The number of currently active requests",
        &["service", "endpoint", "tenant_name"],
    )
    .expect("metric can be created");
}

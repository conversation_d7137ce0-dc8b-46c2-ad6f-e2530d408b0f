package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	proto "github.com/augmentcode/augment/services/examples/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpc_metrics "github.com/augmentcode/augment/services/lib/grpc/metrics"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs" // Set GOMAXPROCS to container limits on startup
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"
)

var (
	configFile = flag.String("config", "", "Path to config file")

	disableGetFeature = featureflags.NewBoolFlag("example_disable_get_feature", false)
)

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	Namespace string `json:"namespace"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// the path to the feature flag sdk key
	FeatureFlagsSdkKeyPath string `json:"feature_flags_sdk_key_path"`

	// the endpoint for the dynamic feature flags service or None if not used
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`
}

type routeGuideServer struct {
	featureFlagHandle featureflags.FeatureFlagHandle
}

func (s *routeGuideServer) GetFeature(ctx context.Context, req *proto.GetFeatureRequest) (*proto.GetFeatureResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)
	claims, _ := auth.GetAugmentClaims(ctx)
	ctx = claims.AnnotateLogContext(ctx)

	if req.Point == nil {
		return nil, status.Error(codes.InvalidArgument, "point must be set")
	}
	log.Ctx(ctx).Info().Msgf("GetFeature: %s/%s", req.Point.Latitude, req.Point.Longitude)

	if disableGetFeature, _ := disableGetFeature.Get(s.featureFlagHandle); disableGetFeature {
		return nil, status.Error(codes.Unimplemented, "GetFeature is disabled")
	}

	// just as an example, we only support a single point
	point := req.Point
	if point.Latitude != 374219983 || point.Longitude != -1220851590 {
		return nil, fmt.Errorf("Failed to find feature")
	}

	feature := &proto.Feature{
		Name:     "Augment HQ",
		Location: point,
	}
	resp := &proto.GetFeatureResponse{
		Feature: feature,
	}
	return resp, nil
}

func (s *routeGuideServer) ListFeatures(rect *proto.ListFeaturesRequest, stream proto.RouteGuide_ListFeaturesServer) error {
	requestContext, _ := requestcontext.FromGrpcContext(stream.Context())
	ctx := requestContext.AnnotateLogContext(stream.Context())
	claims, _ := auth.GetAugmentClaims(stream.Context())
	ctx = claims.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msg("ListFeatures")
	feature := &proto.Feature{
		Name: "Augment HQ",
		Location: &proto.Point{
			Latitude:  374219983,
			Longitude: -1220851590,
		},
	}
	resp := &proto.ListFeaturesResponse{
		Feature: feature,
	}
	// Call call Send() multiple times to send multiple responses.
	return stream.Send(resp)
}

func (s *routeGuideServer) RecordRoute(stream proto.RouteGuide_RecordRouteServer) error {
	return nil
}

func (s *routeGuideServer) RouteChat(stream proto.RouteGuide_RouteChatServer) error {
	return nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	wg := sync.WaitGroup{}

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}
	_ = clientCreds // this is just to suppress unused variable warning. Remove the code is not used

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	featureFlagHandle, err := featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	grpcMetricsInterceptor := grpc_metrics.NewMetricsInterceptor()
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
		grpcMetricsInterceptor.UnaryInterceptor,
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
		grpcMetricsInterceptor.StreamInterceptor,
	))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()
	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	server := routeGuideServer{
		featureFlagHandle: featureFlagHandle,
	}

	proto.RegisterRouteGuideServer(grpcServer, &server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	grpcServer.GracefulStop()
	wg.Wait()
	log.Info().Msg("Server stopped")
}

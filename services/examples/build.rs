// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path, path::PathBuf};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../").canonicalize().unwrap();
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());

    let protos = vec![root.join("services/examples/route_guide.proto")];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();
        let includes = vec![proto_dir, root.as_ref()];

        tonic_build::configure()
            .file_descriptor_set_path(out_dir.join("routeguide_descriptor.bin"))
            .compile_protos(&[proto_path], &includes)?;
    }
    Ok(())
}

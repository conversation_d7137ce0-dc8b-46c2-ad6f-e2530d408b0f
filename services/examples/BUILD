load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:rust.bzl", "rust_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "route_guide_proto",
    srcs = ["route_guide.proto"],
    visibility = ["//services/examples:__subpackages__"],
)

py_grpc_library(
    name = "route_guide_py_proto",
    protos = [":route_guide_proto"],
    visibility = ["//services/examples:__subpackages__"],
)

go_grpc_library(
    name = "route_guide_go_proto",
    importpath = "github.com/augmentcode/augment/services/examples/proto",
    proto = ":route_guide_proto",
    visibility = [
        "//services/examples:__subpackages__",
    ],
)

ts_proto_library(
    name = "route_guide_ts_proto",
    node_modules = "//:node_modules",
    proto = ":route_guide_proto",
    visibility = ["//services/examples:__subpackages__"],
)

rust_library(
    name = "route_guide_rs_proto",
    srcs = ["route_guide_proto.rs"],
    aliases = aliases(),
    crate_name = "route_guide_rs_proto",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services/examples:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":route_guide_rs_proto_gen",
    ],
)

cargo_build_script(
    name = "route_guide_rs_proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        ":route_guide_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

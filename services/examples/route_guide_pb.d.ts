// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/examples/route_guide.proto (package routeguide, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message routeguide.Point
 */
export declare class Point extends Message<Point> {
  /**
   * @generated from field: int32 latitude = 1;
   */
  latitude: number;

  /**
   * @generated from field: int32 longitude = 2;
   */
  longitude: number;

  constructor(data?: PartialMessage<Point>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.Point";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Point;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Point;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Point;

  static equals(a: Point | PlainMessage<Point> | undefined, b: Point | PlainMessage<Point> | undefined): boolean;
}

/**
 * @generated from message routeguide.Rectangle
 */
export declare class Rectangle extends Message<Rectangle> {
  /**
   * @generated from field: routeguide.Point lo = 1;
   */
  lo?: Point;

  /**
   * @generated from field: routeguide.Point hi = 2;
   */
  hi?: Point;

  constructor(data?: PartialMessage<Rectangle>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.Rectangle";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Rectangle;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Rectangle;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Rectangle;

  static equals(a: Rectangle | PlainMessage<Rectangle> | undefined, b: Rectangle | PlainMessage<Rectangle> | undefined): boolean;
}

/**
 * @generated from message routeguide.Feature
 */
export declare class Feature extends Message<Feature> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: routeguide.Point location = 2;
   */
  location?: Point;

  constructor(data?: PartialMessage<Feature>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.Feature";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Feature;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Feature;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Feature;

  static equals(a: Feature | PlainMessage<Feature> | undefined, b: Feature | PlainMessage<Feature> | undefined): boolean;
}

/**
 * @generated from message routeguide.RouteNote
 */
export declare class RouteNote extends Message<RouteNote> {
  /**
   * @generated from field: routeguide.Point location = 1;
   */
  location?: Point;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  constructor(data?: PartialMessage<RouteNote>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.RouteNote";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RouteNote;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RouteNote;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RouteNote;

  static equals(a: RouteNote | PlainMessage<RouteNote> | undefined, b: RouteNote | PlainMessage<RouteNote> | undefined): boolean;
}

/**
 * @generated from message routeguide.RouteSummary
 */
export declare class RouteSummary extends Message<RouteSummary> {
  /**
   * @generated from field: int32 point_count = 1;
   */
  pointCount: number;

  /**
   * @generated from field: int32 feature_count = 2;
   */
  featureCount: number;

  /**
   * @generated from field: int32 distance = 3;
   */
  distance: number;

  /**
   * @generated from field: int32 elapsed_time = 4;
   */
  elapsedTime: number;

  constructor(data?: PartialMessage<RouteSummary>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.RouteSummary";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RouteSummary;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RouteSummary;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RouteSummary;

  static equals(a: RouteSummary | PlainMessage<RouteSummary> | undefined, b: RouteSummary | PlainMessage<RouteSummary> | undefined): boolean;
}

/**
 * @generated from message routeguide.GetFeatureRequest
 */
export declare class GetFeatureRequest extends Message<GetFeatureRequest> {
  /**
   * @generated from field: routeguide.Point point = 1;
   */
  point?: Point;

  constructor(data?: PartialMessage<GetFeatureRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.GetFeatureRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFeatureRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFeatureRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFeatureRequest;

  static equals(a: GetFeatureRequest | PlainMessage<GetFeatureRequest> | undefined, b: GetFeatureRequest | PlainMessage<GetFeatureRequest> | undefined): boolean;
}

/**
 * @generated from message routeguide.GetFeatureResponse
 */
export declare class GetFeatureResponse extends Message<GetFeatureResponse> {
  /**
   * @generated from field: routeguide.Feature feature = 1;
   */
  feature?: Feature;

  constructor(data?: PartialMessage<GetFeatureResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.GetFeatureResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFeatureResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFeatureResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFeatureResponse;

  static equals(a: GetFeatureResponse | PlainMessage<GetFeatureResponse> | undefined, b: GetFeatureResponse | PlainMessage<GetFeatureResponse> | undefined): boolean;
}

/**
 * @generated from message routeguide.ListFeaturesRequest
 */
export declare class ListFeaturesRequest extends Message<ListFeaturesRequest> {
  /**
   * @generated from field: routeguide.Rectangle rectangle = 1;
   */
  rectangle?: Rectangle;

  constructor(data?: PartialMessage<ListFeaturesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.ListFeaturesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListFeaturesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListFeaturesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListFeaturesRequest;

  static equals(a: ListFeaturesRequest | PlainMessage<ListFeaturesRequest> | undefined, b: ListFeaturesRequest | PlainMessage<ListFeaturesRequest> | undefined): boolean;
}

/**
 * @generated from message routeguide.ListFeaturesResponse
 */
export declare class ListFeaturesResponse extends Message<ListFeaturesResponse> {
  /**
   * @generated from field: routeguide.Feature feature = 1;
   */
  feature?: Feature;

  constructor(data?: PartialMessage<ListFeaturesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "routeguide.ListFeaturesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListFeaturesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListFeaturesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListFeaturesResponse;

  static equals(a: ListFeaturesResponse | PlainMessage<ListFeaturesResponse> | undefined, b: ListFeaturesResponse | PlainMessage<ListFeaturesResponse> | undefined): boolean;
}


"""A small tool to issue requests against the route guide API."""

import argparse
import logging
import sys

import pydantic

from base.logging.console_logging import setup_console_logging
from services.examples import route_guide_pb2
from services.lib.grpc import grpc_args_parser, token_parser
from services.lib.request_context.request_context import RequestContext
from services.examples.client import client as route_guide_client


def _get_request_context(args) -> RequestContext:
    """Get the request context.

    If this returns an auth token, it will be used to authenticate the request to the content manager.
    The token will be valid for a short period of time.
    """
    if args.auth_token_file:
        return RequestContext.create(
            auth_token=pydantic.SecretStr(args.auth_token_file.read_text())
        )
    else:
        return RequestContext.create()


def get_feature(
    args: argparse.Namespace, rpc_client: route_guide_client.RouteGuideClient
):
    """Get a feature.

    Args:
        args: The parsed command line arguments.
        rpc_client: The RPC client.

    Returns:
        None
    """
    request_context = _get_request_context(args)
    response = rpc_client.get_feature(
        route_guide_pb2.Point(
            latitude=int(args.latitude * 1e7), longitude=int(args.longitude * 1e7)
        ),
        request_context=request_context,
    )
    logging.info("%s", response.name)


def list_feature(
    args: argparse.Namespace, rpc_client: route_guide_client.RouteGuideClient
):
    """List features.

    Args:
        args: The parsed command line arguments.
        rpc_client: The RPC client.

    Returns:
        None
    """
    request_context = _get_request_context(args)
    for response in rpc_client.list_features(
        route_guide_pb2.Rectangle(
            lo=route_guide_pb2.Point(
                latitude=int(args.latitude * 1e7), longitude=int(args.longitude * 1e7)
            ),
            hi=route_guide_pb2.Point(
                latitude=int(args.latitude * 1e7), longitude=int(args.longitude * 1e7)
            ),
        ),
        request_context=request_context,
    ):
        logging.info("%s", response.name)


def main():
    """Main function."""
    setup_console_logging(add_timestamp=False)
    parser = argparse.ArgumentParser()

    # Add arguments
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)

    # Add subparsers
    subparsers = parser.add_subparsers()
    get_feature_parser = subparsers.add_parser("get-feature")
    get_feature_parser.set_defaults(action=get_feature)
    get_feature_parser.add_argument("--latitude", type=float, required=True)
    get_feature_parser.add_argument("--longitude", type=float, required=True)

    list_features_parser = subparsers.add_parser("list-features")
    list_features_parser.set_defaults(action=list_feature)
    list_features_parser.add_argument("--latitude", type=float, required=True)
    list_features_parser.add_argument("--longitude", type=float, required=True)

    parser.set_defaults(action=None)

    args = parser.parse_args()
    try:
        # Create the client by setting up a port forwarding to the service.
        with grpc_args_parser.create_client(
            args,
            route_guide_client.RouteGuideClient.create_for_endpoint,
            default_service_name="route-guide-svc",
            default_endpoint="route-guide-svc:50051",
        ) as rpc_client:
            token_parser.get_token(args)
            if not args.action:
                parser.print_help()
            else:
                args.action(args, rpc_client)
    except KeyboardInterrupt:
        sys.exit(1)
    except argparse.ArgumentError as e:
        logging.error("%s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()

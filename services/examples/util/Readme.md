# Route Guide Util

A simple CLI tool to access the route guide API for debugging and manual testing.

Usually, the ports of the route guide will be forwarded to the local machine with `kubectl forward`.

## Usage

```python
bazel run //services/examples/python/util -- --namespace dev-dirk --cloud GCP_US_CENTRAL1_DEV --tenant-name augment get-feature --latitude 37.4219983 --longitude -122.0851590
```

# API proxy service

The API proxy is the public interface of the backend to the extensions.

The API proxy service:

- It serves the public API (See public_api.proto) as HTTPS+JSON interface and translates the requests into gRPC requests to internal services.
- performs API token based authentication and exchanges the API token for a service token JWT.
- It finds all generation models and exposes them via the model finder API (see model_finder.proto)

# Dependencies

The api proxy service depends on large number of services incl.
- content manager
- all generations hosts

## Security considerations

### Where should this run

The api proxy service runs in each shard namespace proving services
to all tenants in that namespace.

### Authentication and authorization

The api proxy service authenticates each request.
However, the requests are not a-priori authenticated.
The api proxy is the central interface to the public Internet (after a load balancer).

### Permissions and secrets

The api proxy is allowed to perform token-less calls to auth-query to authenticate public API tokens
and to token exchange to exchange API tokens for service tokens.

## Persistence

The api proxy service does not persist any data of its own.

## Observability

The api proxy uses logging, metrics and tracing to provide observability.
In fact, it is the starting point for most tracing calls.

## Error Handling

The api proxy services usually doesn't handle any errors, but forwards errors to the client.
In that it translates GRPC errors into HTTP errors.

## Links

- [https://www.notion.so/Replacing-the-arc-app-b69bcf17b07f4c008aefdf1cf5c6bc86]

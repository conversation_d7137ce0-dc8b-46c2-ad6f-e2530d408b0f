{"model": "", "prompt": "def main():\n    print(\"hello world\")\n    ", "suffix": "\n\n\n\nif __name__ == \"__main__\":\n    main()\n", "path": "another.py", "memories": ["6ede0dc03c1e0db3fb99ab4f321bc1bf098344cca2ba70791e5dbec16f90ad8f", "c1aa222a1ec1732bb63d136f14e92d600bef7655b7cf34b7b28cc04e02cbe1f3"], "top_k": null, "top_p": null, "temperature": null, "blob_name": "c1aa222a1ec1732bb63d136f14e92d600bef7655b7cf34b7b28cc04e02cbe1f3", "prefix_begin": 0, "cursor_position": 41, "suffix_end": 4642, "lang": "python", "blobs": {"checkpoint_id": null, "added_blobs": [], "deleted_blobs": []}, "recency_info": {"tab_switch_events": [{"path": "another.py", "file_blob_name": "c1aa222a1ec1732bb63d136f14e92d600bef7655b7cf34b7b28cc04e02cbe1f3"}], "git_diff_file_info": [], "recent_changes": [{"blob_name": "c1aa222a1ec1732bb63d136f14e92d600bef7655b7cf34b7b28cc04e02cbe1f3", "path": "another.py", "replacement_text": "\n\n\n\nif __name__ == \"__main__\":\n    main()\n", "char_start": 0, "char_end": 10, "present_in_blob": false}]}, "probe_only": null}
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "grpc_client",
    srcs = [
        "grpc_client.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/api_proxy:model_finder_py_proto",
        requirement("grpcio"),
        requirement("protobuf"),
    ],
)

py_library(
    name = "memorize_path",
    srcs = ["memorize_path.py"],
    visibility = [
        "//services:__subpackages__",
        "//tools/bazel_runner/review_edit_bot:__subpackages__",
    ],
    deps = [
        "//base/augment_client:client",
        "//base/blob_names/python:blob_names",
        requirement("gitignore-parser"),
    ],
)

pytest_test(
    name = "memorize_path_test",
    srcs = ["memorize_path_test.py"],
    data = [
    ],
    deps = [
        ":memorize_path",
    ],
)

# a small CLI utility to issue requests against the api-proxy API
py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        ":memorize_path",
        "//base/augment_client:client",
        "//base/logging:console_logging",
    ],
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/api_proxy/client",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/api_proxy:model_finder_go_proto",
        "//services/deploy/model_instance:model_instance_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

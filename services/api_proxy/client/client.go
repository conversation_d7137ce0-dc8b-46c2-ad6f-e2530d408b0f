package client

import (
	"context"

	modelfinderproto "github.com/augmentcode/augment/services/api_proxy/model_finder_proto"
	modelinstanceproto "github.com/augmentcode/augment/services/deploy/model_instance/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
)

type ModelFinderClient interface {
	GetGenerationModels(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*modelinstanceproto.ModelInstanceConfig, error)
	Close()
}

type ModelFinderClientImpl struct {
	conn *grpc.ClientConn
	stub modelfinderproto.ModelFinderClient
}

func NewModelFinderClient(endpoint string, creds credentials.TransportCredentials) (ModelFinderClient, error) {
	conn, err := grpc.NewClient(endpoint,
		grpc.WithTransportCredentials(creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	)
	if err != nil {
		return nil, err
	}
	stub := modelfinderproto.NewModelFinderClient(conn)
	return &ModelFinderClientImpl{conn: conn, stub: stub}, nil
}

func (c *ModelFinderClientImpl) GetGenerationModels(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*modelinstanceproto.ModelInstanceConfig, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	req := &modelfinderproto.GetModelsRequest{}
	resp, err := c.stub.GetGenerationModels(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Models, nil
}

func (c *ModelFinderClientImpl) Close() {
	c.conn.Close()
}

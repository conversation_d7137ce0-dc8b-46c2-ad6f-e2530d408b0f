# Augment API Client

Python client for the Augment API.

Includes a Python client and a CLI utility for manual testing against a deployed server

## Authentication Prerequirements

set your token at `~/.config/augment/token`

## Sample usage of CLI util

#### Get Models
```sh
bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com get-models
```

#### Resolve Edit
```sh
bazel run services/api_proxy/client/util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com resolve-edit --request-id  b8568259-5e49-4eb7-9e1f-9dae2222f91a --emit-time "2024-01-01 15:00:00" --resolve-time "2024-01-01 15:00:01" --is-accepted true
```

#### Checkpoint Blobs
```sh
bazel run services/api_proxy/client/util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com checkpoint-blobs --added "[\"8e4bf1c4d51c3bb6aef4bca5b84ddfa216a3c020ede8838dc1b719eff8f6176a\",\"224038788919b2190737305ccdd347b7d9401fef1a3ef9f6f73b2e0402385858\",\"63e4156d686571b2a690d19ff834594a4fbc305f955b82be64a6d8c09fad61cf\"]"
```

#### Completion
```sh
bazel run services/api_proxy/client/util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com completion --request-file $(pwd)/services/api_proxy/client/completion_request_example.json
```

#### Chat
```sh
export n=$'\n'

bazel run services/api_proxy/client/util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com chat --model binks-1B-BF16-chatanol1-18-ug-chat --chat-history='[{"request_id":"c906670d-47a8-4f02-b596-db90e1a6a7bf","request_message":"What are the external dependencies in the codebase?","response_text":"The codebase has three external dependencies: JSON, enum, RIVKA"}]' --message "Tell me details about these three dependencies" --selected-code "${n}from typing import Optional${n}${n}from base.prompt_format_chat.prompt_formatter import (${n}    ChatPromptFormatter,${n}    ChatPromptInput,${n}    ChatPromptOutput,${n}    ChatTokenApportionment,${n}    ExceedContextLength,${n}    PromptChunk,${n}    TokenList,${n}    filter_overlapping_retrieved_chunks,${n})${n}from base.prompt_format_completion.util import (${n}    concatenate_retrieved_chunks,${n}    head_n,${n}    trailing_n,${n})${n}from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer${n}${n}${n}class BinksChatPromptFormatter(ChatPromptFormatter):${n}" --path "base/prompt_format_chat/binks_prompt_formatter.py"
```

#### Chat Stream
Same as chat, just adding the flag: `--stream=true`, for example:
```sh
bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com chat --message "Say hello" --stream=true
```

#### Chat Feedback
```sh
bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com chat-feedback --request-id c906670d-47a8-4f02-b596-db90e1a6a7bf --rating 1 --note "Beautiful chat response"
```

#### Next Edit
```sh
bazel run services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com next-edit --request-file $(pwd)/services/api_proxy/client/next_edit_request_example.json
```

#### Instruction
```sh
export n=$'\n'

bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com instruction --instruction "Replace with implemented function" --selected-text "# Following is a simple quicksort implementation for a number array${n}def quicksort(arr):${n}    # TODO: Implement${n}"
```

#### Smart Paste
```sh
export n=$'\n'

bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com smart-paste --code-block "def quick_sort(arr):${n}    print(f\"Actually sorting {arr} for real\")${n}" --target-file-content "# This file includes various sorting functions${n}${n}def bubble_sort(arr):${n}    print(f\"Sorting: {arr}\")${n}${n}def bogo_sort(arr):${n}    print(f\"Sorting: {arr}\")${n}${n}def quick_sort(arr):${n}"
```

#### Resolve Instruction
```sh
bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com resolve-instruction --request-id b8568259-5e49-4eb7-9e1f-9dae2222f91a --is-accepted-chunks "[true, false, true]" --is-accept-all false --is-reject-all false --emit-time "2024-10-10 15:00:22" --resolve-time "2024-10-10 15:00:55"
```

#### Resolve Smart Paste
```sh
bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com resolve-smart-paste --request-id b8568259-5e49-4eb7-9e1f-9dae2222f91a --is-accepted-chunks "[true, false, true]" --is-accept-all false --is-reject-all false --initial-request-time "2024-01-01 15:00:00" --stream-finish-time "2024-01-01 15:00:10" --apply-time "2024-01-01 15:00:20" --resolve-time "2024-01-01 15:00:50"
```

#### Client Metrics
```sh
bazel run //services/api_proxy/client:util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com client-metrics --metrics '[["webview__chat__chat-codeblock-create", 1]]'
```

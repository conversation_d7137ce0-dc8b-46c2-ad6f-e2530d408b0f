load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_proto_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "model_finder_proto",
    srcs = ["model_finder.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/deploy/model_instance:model_instance_proto",
    ],
)

py_grpc_library(
    name = "model_finder_py_proto",
    protos = [":model_finder_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/deploy/model_instance:model_instance_py_proto",
    ],
)

go_grpc_library(
    name = "model_finder_go_proto",
    importpath = "github.com/augmentcode/augment/services/api_proxy/model_finder_proto",
    proto = ":model_finder_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/deploy/model_instance:model_instance_go_proto",
    ],
)

proto_library(
    name = "public_api_proto",
    srcs = ["public_api.proto"],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//services/chat_host:chat_proto",
        "//services/request_insight:request_insight_proto",
        "@googleapis//google/api:annotations_proto",
        "@protobuf//:descriptor_proto",
        "@protobuf//:empty_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "public_api_go_proto",
    importpath = "github.com/augmentcode/augment/services/api_proxy/public_api",
    proto = ":public_api_proto",
    visibility = [
        "//base/augment_client:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//services/request_insight:request_insight_go_proto",
        "@org_golang_google_genproto_googleapis_api//annotations:go_default_library",
    ],
)

py_proto_library(
    name = "public_api_py_proto",
    protos = [":public_api_proto"],
    visibility = [
        "//base/augment_client:__subpackages__",
        "//base/datasets:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//services/chat_host:chat_proto_py_proto",
        "//services/request_insight:request_insight_py_proto",
        "//third_party/proto:googleapis_annotations_py_proto",
        "//third_party/proto:googleapis_http_py_proto",
    ],
)

ts_proto_library(
    name = "public_api_ts_proto",
    node_modules = "//:node_modules",
    proto = ":public_api_proto",
    visibility = ["//clients:__subpackages__"],
    deps = [
        "//services/request_insight:request_insight_ts_proto",
    ],
)

syntax = "proto3";
package model_finder;

import "services/deploy/model_instance/model_instance.proto";

// the model finder exposes all currently registered generation models
service ModelFinder {
  // returns all configured inference models
  rpc GetInferenceModels(GetModelsRequest) returns (GetModelsResponse) {
    // deprecated
    option deprecated = true;
  }

  rpc GetGenerationModels(GetModelsRequest) returns (GetModelsResponse);
}

message GetModelsRequest {}

message GetModelsResponse {
  // list of all models available.
  repeated augment.model_instance_config.ModelInstanceConfig models = 1;
}

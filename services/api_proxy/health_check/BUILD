load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

py_binary(
    name = "health_check",
    srcs = [
        "health_check.py",
    ],
    deps = [
        "//base/augment_client:client",
        "//base/blob_names/python:blob_names",
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//services/api_proxy/client:grpc_client",
        requirement("prometheus_client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    base = "//tools/docker:ubuntu_base_image",
    binary = "health_check",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":monitoring_kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

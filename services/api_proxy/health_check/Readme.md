# Api Proxy Health Check

This container implements a service health check.

It performs API requests (including completions against all configured models) against the api proxy.
It determines if the service is deemed available or not.

## Metrics

- `api_proxy_health_check_status` (Counter): health check samples for the overall service, tagged as success/failure
- `api_proxy_health_check_model_status`: (Counter): health check samples for each model, tagged as success/failure
- `api_proxy_health_check_agent_status`: (Counter): health check samples for agents, tagged as success/failure
- `api_proxy_health_check_remote_agent_status`: (Counter): health check samples for remote agents, tagged as success/failure

## Testing changes

### Running locally

To test the health check locally, use port forwarding, e.g.:

```sh
kubectl port-forward --namespace=dev-$USER svc/model-finder-svc 50051:50051
```

Then run the healthcheck with `bazel`:

```sh
bazel run services/api_proxy/health_check:health_check -- --endpoint https://dev-$USER.us-central.api.augmentcode.com/ --model-finder-endpoint localhost:50051
```

### Deploying your changes to dev

Deploy your dev
- Remember to include `api-proxy-healthcheck` in the service selection

```sh
bazel run //services/deploy:dev_deploy
```

The healthcheck pod will automatically run. Find it:

```sh
$ kubectl get -n dev-$USER pods | grep api-proxy-health
api-proxy-health-check-fd88874b9-snnzl                   1/1     Running            0          8m3s
```

Then we can check the logs, preferably piped into `jq` for nicer formatting:

```sh
kubectl logs -n dev-$USER api-proxy-health-check-fd88874b9-snnzl --tail 100 | jq .
```

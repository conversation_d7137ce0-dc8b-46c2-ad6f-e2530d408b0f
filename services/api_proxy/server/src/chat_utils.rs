use crate::chat;
use crate::public_api_proto;
use crate::request_insight_util::RequestInsightPublisher;
use request_context::{RequestContext, TenantInfo};
use request_insight_publisher::request_insight;
use std::sync::Arc;

/// List of keywords that indicate a message is not a user message when found in text nodes.
/// These are typically special instructions or commands used by the system.
const NON_USER_MESSAGE_KEYWORDS: &[&str] = &[
    "\"worthRemembering\":",
    "Please generate a commit message based on the diff of my staged and unstaged changes.",
    "The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation.",
];

/// Checks if a ChatRequest is a user message.
///
/// This function determines if a request is a user message by checking:
/// 1. It does not have any tool result nodes
/// 2. For text nodes, none of them contain any of the keywords defined in NON_USER_MESSAGE_KEYWORDS
///
/// # Arguments
///
/// * `chat_request` - The ChatRequest to check
///
/// # Returns
///
/// * `true` if the request is a user message, `false` otherwise
pub fn is_user_message(chat_request: &chat::ChatRequest) -> bool {
    // Check if there are any tool result nodes
    let has_tool_result_nodes = chat_request
        .nodes
        .iter()
        .any(|node| node.r#type == chat::ChatRequestNodeType::ToolResult as i32);

    // If there are tool result nodes, it's not a user message
    if has_tool_result_nodes {
        return false;
    }

    // if the request is marked as silent, it's not a user message
    if chat_request.silent {
        tracing::info!("Not a user message - request is marked as silent.");
        return false;
    }

    // Check if any text nodes contain any of the keywords that indicate it's not a user message
    let has_non_user_keywords = chat_request.nodes.iter().any(|node| {
        if node.r#type == chat::ChatRequestNodeType::Text as i32 {
            if let Some(text_node) = &node.text_node {
                return NON_USER_MESSAGE_KEYWORDS
                    .iter()
                    .any(|keyword| text_node.content.contains(keyword));
            }
        }
        false
    });

    // If any text node contains any of the non-user keywords, it's not a user message
    !has_non_user_keywords
}

/// Checks if a ChatResponse contains meaningful content.
///
/// This function determines if a response has any tool use nodes or non-empty text nodes.
/// End-of-turn nodes that have no content don't count as meaningful.
/// Whitespace-only content is considered meaningful.
///
/// # Arguments
///
/// * `response` - The ChatResponse to check
///
/// # Returns
///
/// * `true` if the response contains meaningful content, `false` otherwise
pub fn has_meaningful_content(response: &chat::ChatResponse) -> bool {
    // Check if the response text is non-empty (including whitespace)
    if !response.text.is_empty() {
        return true;
    }

    // Check if there are any tool use nodes
    let has_tool_use = response.nodes.iter().any(|node| {
        node.r#type == chat::ChatResultNodeType::ToolUse as i32 && node.tool_use.is_some()
    });

    has_tool_use
}

/// Emits a ChatUserMessage event for a user message (without tool result nodes)
///
/// This function calculates message statistics and emits a ChatUserMessage event
/// to the request insight publisher.
///
/// # Arguments
///
/// * `request_insight_publisher` - The publisher to emit events to
/// * `request_context` - The request context
/// * `tenant_info` - The tenant information
/// * `chat_request` - The chat request
/// * `front_chat_mode` - The chat mode
/// * `resolved_model_name` - The resolved model name (if available), used when the original model name is empty/default
pub async fn emit_chat_user_message_event(
    request_insight_publisher: &Arc<dyn RequestInsightPublisher + Send + Sync>,
    request_context: &RequestContext,
    tenant_info: &TenantInfo,
    chat_request: &chat::ChatRequest,
    front_chat_mode: public_api_proto::ChatMode,
    resolved_model_name: Option<&str>,
) {
    // If the chat request is marked as silent, don't emit an event
    // The silent field defaults to false if not set
    if chat_request.silent {
        tracing::info!("Not emitting ChatUserMessage event - request is marked as silent");
        return;
    }

    // Calculate message stats
    let image_node_count = chat_request
        .nodes
        .iter()
        .filter(|node| node.r#type == chat::ChatRequestNodeType::Image as i32)
        .count() as u32;

    // Calculate character and line counts from text nodes
    let mut character_count = 0;
    let mut line_count = 0;
    for node in &chat_request.nodes {
        if node.r#type == chat::ChatRequestNodeType::Text as i32 {
            if let Some(text_node) = &node.text_node {
                character_count += text_node.content.chars().count() as u32;
                line_count += text_node.content.lines().count() as u32;
            }
        }
    }

    // Map front_chat_mode to ChatUserMessage::ChatMode
    use request_insight::chat_user_message::ChatMode;
    let chat_user_message_chat_mode = match front_chat_mode {
        public_api_proto::ChatMode::Chat => ChatMode::Chat,
        public_api_proto::ChatMode::Agent => ChatMode::Agent,
        public_api_proto::ChatMode::Memories => ChatMode::Memories,
        public_api_proto::ChatMode::Orientation => ChatMode::Orientation,
        public_api_proto::ChatMode::MemoriesCompression => ChatMode::MemoriesCompression,
        public_api_proto::ChatMode::RemoteAgent => ChatMode::RemoteAgent,
    };

    // Get the opaque_user_id from the tenant_info and convert it to the right type
    // Don't use map which might fail if opaque_user_id is not available
    let opaque_user_id = if let Some(id) = tenant_info.opaque_user_id.clone() {
        // Convert from request_context::UserId to request_insight_publisher::auth_entities::UserId
        Some(request_insight_publisher::auth_entities::UserId {
            user_id_type: id.user_id_type,
            user_id: id.user_id,
        })
    } else {
        tracing::warn!("opaque_user_id is missing in emit_chat_user_message_event");
        None
    };

    // Determine the correct model name to use
    // If the original model name is empty (default), use the resolved model name
    // Otherwise, use the original model name from the request
    let model_name_to_use = if chat_request.model_name.is_empty() {
        resolved_model_name.unwrap_or("").to_string()
    } else {
        chat_request.model_name.clone()
    };

    // Create ChatUserMessage event
    let chat_user_message_event = request_insight::RequestEvent {
        time: Some(prost_wkt_types::Timestamp::from(
            std::time::SystemTime::now(),
        )),
        event_id: Some(uuid::Uuid::new_v4().to_string()),
        event: Some(request_insight::request_event::Event::ChatUserMessage(
            request_insight::ChatUserMessage {
                chat_mode: chat_user_message_chat_mode as i32,
                chat_history_length: chat_request.chat_history.len() as u32,
                image_node_count,
                character_count,
                line_count,
                opaque_user_id,
                model_name: Some(model_name_to_use),
            },
        )),
    };

    // Emit the event
    request_insight_publisher
        .record_request_events(request_context, tenant_info, vec![chat_user_message_event])
        .await;

    tracing::info!(
        "ChatUserMessage event emitted with chat_mode={:?}, chars={}, lines={}, images={}",
        front_chat_mode,
        character_count,
        line_count,
        image_node_count
    );
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_user_message_with_no_tool_result_nodes() {
        let mut chat_request = chat::ChatRequest::default();
        let text_node = chat::ChatRequestNode {
            id: 1,
            r#type: chat::ChatRequestNodeType::Text as i32,
            text_node: Some(chat::ChatRequestText {
                content: "Hello, this is a user message".to_string(),
            }),
            tool_result_node: None,
            image_node: None,
            ide_state_node: None,
            edit_events_node: None,
        };

        chat_request.nodes.push(text_node);
        assert!(is_user_message(&chat_request));
    }

    #[test]
    fn test_is_user_message_with_tool_result_nodes() {
        let mut chat_request = chat::ChatRequest::default();
        let tool_result_node = chat::ChatRequestNode {
            id: 1,
            r#type: chat::ChatRequestNodeType::ToolResult as i32,
            text_node: None,
            tool_result_node: Some(chat::ChatRequestToolResult {
                tool_use_id: "test_id".to_string(),
                content: "Tool result".to_string(),
                is_error: false,
                request_id: None,
                content_nodes: vec![],
            }),
            image_node: None,
            ide_state_node: None,
            edit_events_node: None,
        };

        chat_request.nodes.push(tool_result_node);
        assert!(!is_user_message(&chat_request));
    }

    #[test]
    fn test_is_user_message_with_non_user_keywords() {
        // Test each keyword in the NON_USER_MESSAGE_KEYWORDS list
        for keyword in NON_USER_MESSAGE_KEYWORDS {
            let mut chat_request = chat::ChatRequest::default();
            let text_node = chat::ChatRequestNode {
                id: 1,
                r#type: chat::ChatRequestNodeType::Text as i32,
                text_node: Some(chat::ChatRequestText {
                    content: format!("This contains {}", keyword),
                }),
                tool_result_node: None,
                image_node: None,
                ide_state_node: None,
                edit_events_node: None,
            };

            chat_request.nodes.push(text_node);
            assert!(
                !is_user_message(&chat_request),
                "Failed for keyword: {}",
                keyword
            );
        }
    }

    #[test]
    fn test_is_user_message_with_mixed_nodes() {
        let mut chat_request = chat::ChatRequest::default();

        // Regular text node
        let text_node = chat::ChatRequestNode {
            id: 1,
            r#type: chat::ChatRequestNodeType::Text as i32,
            text_node: Some(chat::ChatRequestText {
                content: "Hello, this is a user message".to_string(),
            }),
            tool_result_node: None,
            image_node: None,
            ide_state_node: None,
            edit_events_node: None,
        };

        // Image node
        let image_node = chat::ChatRequestNode {
            id: 2,
            r#type: chat::ChatRequestNodeType::Image as i32,
            text_node: None,
            tool_result_node: None,
            image_node: Some(chat::ChatRequestImage {
                image_data: "".to_string(),
                format: 1,
            }),
            ide_state_node: None,
            edit_events_node: None,
        };

        chat_request.nodes.push(text_node);
        chat_request.nodes.push(image_node);
        assert!(is_user_message(&chat_request));
    }

    #[test]
    fn test_has_meaningful_content_with_tool_use() {
        let mut response = chat::ChatResponse::default();
        let tool_use = chat::ChatResultToolUse {
            tool_use_id: "test_id".to_string(),
            tool_name: "test_tool".to_string(),
            input_json: "{}".to_string(),
            is_partial: false,
        };

        let node = chat::ChatResultNode {
            id: 1,
            r#type: chat::ChatResultNodeType::ToolUse as i32,
            content: "".to_string(),
            tool_use: Some(tool_use),
        };

        response.nodes.push(node);
        // Keep text empty to test tool use fallback
        response.text = "".to_string();

        assert!(has_meaningful_content(&response));
    }

    #[test]
    fn test_has_meaningful_content_with_raw_response() {
        let mut response = chat::ChatResponse::default();
        let node = chat::ChatResultNode {
            id: 1,
            r#type: chat::ChatResultNodeType::RawResponse as i32,
            content: "This is a response".to_string(),
            tool_use: None,
        };

        response.nodes.push(node);
        // Set the text field to match our new implementation
        response.text = "This is a response".to_string();

        assert!(has_meaningful_content(&response));
    }

    #[test]
    fn test_has_meaningful_content_with_main_text_finished() {
        let mut response = chat::ChatResponse::default();
        let node = chat::ChatResultNode {
            id: 1,
            r#type: chat::ChatResultNodeType::MainTextFinished as i32,
            content: "This is a response".to_string(),
            tool_use: None,
        };

        response.nodes.push(node);
        // Set the text field to match our new implementation
        response.text = "This is a response".to_string();

        assert!(has_meaningful_content(&response));
    }

    #[test]
    fn test_has_meaningful_content_with_empty_nodes() {
        let mut response = chat::ChatResponse::default();
        let node = chat::ChatResultNode {
            id: 1,
            r#type: chat::ChatResultNodeType::MainTextFinished as i32,
            content: "".to_string(),
            tool_use: None,
        };

        response.nodes.push(node);
        // Keep text empty to match our new implementation
        response.text = "".to_string();

        assert!(!has_meaningful_content(&response));
    }

    #[test]
    fn test_has_meaningful_content_with_whitespace_only_nodes() {
        let mut response = chat::ChatResponse::default();
        let node = chat::ChatResultNode {
            id: 1,
            r#type: chat::ChatResultNodeType::RawResponse as i32,
            content: "   \n  \t  ".to_string(),
            tool_use: None,
        };

        response.nodes.push(node);
        // Set text to whitespace to match our new implementation
        response.text = "   \n  \t  ".to_string();

        // We now consider whitespace as meaningful content
        assert!(has_meaningful_content(&response));
    }

    #[test]
    fn test_has_meaningful_content_with_mixed_nodes() {
        let mut response = chat::ChatResponse::default();

        // Empty node
        let empty_node = chat::ChatResultNode {
            id: 1,
            r#type: chat::ChatResultNodeType::MainTextFinished as i32,
            content: "".to_string(),
            tool_use: None,
        };

        // Non-empty node
        let non_empty_node = chat::ChatResultNode {
            id: 2,
            r#type: chat::ChatResultNodeType::RawResponse as i32,
            content: "This is a response".to_string(),
            tool_use: None,
        };

        response.nodes.push(empty_node);
        response.nodes.push(non_empty_node);
        // Set the text field to match our new implementation
        response.text = "This is a response".to_string();

        assert!(has_meaningful_content(&response));
    }

    #[test]
    fn test_emit_chat_user_message_event_model_name_resolution() {
        // Test that when the original model name is empty, the resolved model name is used
        let chat_request = chat::ChatRequest {
            model_name: "".to_string(), // Empty model name (default)
            ..Default::default()
        };

        // Simulate the model name resolution logic
        let resolved_model_name = Some("claude-3-5-sonnet-20241022");

        // The function should use the resolved model name when the original is empty
        let model_name_to_use = if chat_request.model_name.is_empty() {
            resolved_model_name
                .map(|s| s.to_string())
                .unwrap_or_default()
        } else {
            chat_request.model_name.clone()
        };

        assert_eq!(model_name_to_use, "claude-3-5-sonnet-20241022");
    }

    #[test]
    fn test_emit_chat_user_message_event_model_name_preservation() {
        // Test that when the original model name is not empty, it's preserved
        let chat_request = chat::ChatRequest {
            model_name: "user-specified-model".to_string(),
            ..Default::default()
        };

        // Even if we have a resolved model name, the original should be used
        let resolved_model_name = Some("claude-3-5-sonnet-20241022");

        let model_name_to_use = if chat_request.model_name.is_empty() {
            resolved_model_name
                .map(|s| s.to_string())
                .unwrap_or_default()
        } else {
            chat_request.model_name.clone()
        };

        assert_eq!(model_name_to_use, "user-specified-model");
    }

    #[test]
    fn test_emit_chat_user_message_event_no_resolved_model() {
        // Test that when both original and resolved model names are empty/None
        let chat_request = chat::ChatRequest {
            model_name: "".to_string(),
            ..Default::default()
        };

        let resolved_model_name: Option<&str> = None;

        let model_name_to_use = if chat_request.model_name.is_empty() {
            resolved_model_name
                .map(|s| s.to_string())
                .unwrap_or_default()
        } else {
            chat_request.model_name.clone()
        };

        assert_eq!(model_name_to_use, "");
    }

    // We'll skip the full emit_chat_user_message_event test for now as it requires more complex mocking
}

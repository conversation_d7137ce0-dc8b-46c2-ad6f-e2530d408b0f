use std::sync::Arc;

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;

use crate::agents;
use request_context::RequestContext;
use tracing_tonic::client::TracingService;

pub const LLM_GENERATE_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("llm_generate_timeout_ms", 120 * 1000);

// We definitely want to bring this down, but it's a generation model
// call followed by N retrieval calls, and any missing blobs we try
// to download on each retrieval.
pub const CODEBASE_RETRIEVAL_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("codebase_retrieval_timeout_ms", 40 * 1000);

pub const EDIT_FILE_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("edit_file_timeout_ms", 90 * 1000);

pub const LIST_REMOTE_TOOLS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("list_remote_tools_timeout_ms", 10 * 1000);

pub const RUN_REMOTE_TOOL_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("run_remote_tool_timeout_ms", 120 * 1000);

pub const CHECK_TOOL_SAFETY_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("check_tool_safety_timeout_ms", 10 * 1000);

pub const REVOKE_TOOL_ACCESS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("revoke_tool_access_timeout_ms", 10 * 1000);

pub const TEST_TOOL_CONNECTION_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("test_tool_connection_timeout_ms", 10 * 1000);

#[async_trait]
pub trait AgentsClient: Send + Sync {
    async fn llm_generate(
        &self,
        request_context: &RequestContext,
        request: agents::LlmGenerateRequest,
    ) -> Result<agents::LlmGenerateResponse, tonic::Status>;

    async fn codebase_retrieval(
        &self,
        request_context: &RequestContext,
        request: agents::CodebaseRetrievalRequest,
    ) -> Result<agents::CodebaseRetrievalResponse, tonic::Status>;

    async fn edit_file(
        &self,
        request_context: &RequestContext,
        request: agents::EditFileRequest,
    ) -> Result<agents::EditFileResponse, tonic::Status>;

    async fn list_remote_tools(
        &self,
        request_context: &RequestContext,
        request: agents::ListRemoteToolsRequest,
    ) -> Result<agents::ListRemoteToolsResponse, tonic::Status>;

    async fn run_remote_tool(
        &self,
        request_context: &RequestContext,
        request: agents::RunRemoteToolRequest,
    ) -> Result<agents::RunRemoteToolResponse, tonic::Status>;

    async fn check_tool_safety(
        &self,
        request_context: &RequestContext,
        request: agents::CheckToolSafetyRequest,
    ) -> Result<agents::CheckToolSafetyResponse, tonic::Status>;

    async fn revoke_tool_access(
        &self,
        request_context: &RequestContext,
        request: agents::RevokeToolAccessRequest,
    ) -> Result<agents::RevokeToolAccessResponse, tonic::Status>;

    async fn test_tool_connection(
        &self,
        request_context: &RequestContext,
        request: agents::TestToolConnectionRequest,
    ) -> Result<agents::TestToolConnectionResponse, tonic::Status>;
}

pub struct AgentsClientImpl {
    endpoint: String,
    tls_config: Option<tonic::transport::ClientTlsConfig>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    client: Arc<Mutex<Option<agents::agents_client::AgentsClient<TracingService>>>>,
}

impl AgentsClientImpl {
    pub fn new(
        endpoint: &str,
        tls_config: Option<tonic::transport::ClientTlsConfig>,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            feature_flags,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<agents::agents_client::AgentsClient<TracingService>, tonic::transport::Error> {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            Some(c) => Ok(c.clone()),
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = agents::agents_client::AgentsClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
        }
    }
}

#[async_trait]
impl AgentsClient for AgentsClientImpl {
    async fn llm_generate(
        &self,
        request_context: &RequestContext,
        request: agents::LlmGenerateRequest,
    ) -> Result<agents::LlmGenerateResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = LLM_GENERATE_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.llm_generate(request).await?;
        Ok(response.into_inner())
    }

    async fn codebase_retrieval(
        &self,
        request_context: &RequestContext,
        request: agents::CodebaseRetrievalRequest,
    ) -> Result<agents::CodebaseRetrievalResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = CODEBASE_RETRIEVAL_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.codebase_retrieval(request).await?;
        Ok(response.into_inner())
    }

    async fn edit_file(
        &self,
        request_context: &RequestContext,
        request: agents::EditFileRequest,
    ) -> Result<agents::EditFileResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = EDIT_FILE_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.edit_file(request).await?;
        Ok(response.into_inner())
    }

    async fn list_remote_tools(
        &self,
        request_context: &RequestContext,
        request: agents::ListRemoteToolsRequest,
    ) -> Result<agents::ListRemoteToolsResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = LIST_REMOTE_TOOLS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.list_remote_tools(request).await?;
        Ok(response.into_inner())
    }

    async fn run_remote_tool(
        &self,
        request_context: &RequestContext,
        request: agents::RunRemoteToolRequest,
    ) -> Result<agents::RunRemoteToolResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = RUN_REMOTE_TOOL_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.run_remote_tool(request).await?;
        Ok(response.into_inner())
    }

    async fn check_tool_safety(
        &self,
        request_context: &RequestContext,
        request: agents::CheckToolSafetyRequest,
    ) -> Result<agents::CheckToolSafetyResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = CHECK_TOOL_SAFETY_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.check_tool_safety(request).await?;
        Ok(response.into_inner())
    }

    async fn revoke_tool_access(
        &self,
        request_context: &RequestContext,
        request: agents::RevokeToolAccessRequest,
    ) -> Result<agents::RevokeToolAccessResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = REVOKE_TOOL_ACCESS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.revoke_tool_access(request).await?;
        Ok(response.into_inner())
    }

    async fn test_tool_connection(
        &self,
        request_context: &RequestContext,
        request: agents::TestToolConnectionRequest,
    ) -> Result<agents::TestToolConnectionResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("agents not ready")
        })?;
        let timeout_ms = TEST_TOOL_CONNECTION_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(timeout_ms));
        request_context.annotate(request.metadata_mut());
        let response = client.test_tool_connection(request).await?;
        Ok(response.into_inner())
    }
}

pub struct MockAgentsClient;

impl Default for MockAgentsClient {
    fn default() -> Self {
        Self::new()
    }
}

impl MockAgentsClient {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl AgentsClient for MockAgentsClient {
    async fn llm_generate(
        &self,
        _request_context: &RequestContext,
        _request: agents::LlmGenerateRequest,
    ) -> Result<agents::LlmGenerateResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }

    async fn codebase_retrieval(
        &self,
        _request_context: &RequestContext,
        _request: agents::CodebaseRetrievalRequest,
    ) -> Result<agents::CodebaseRetrievalResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }

    async fn edit_file(
        &self,
        _request_context: &RequestContext,
        _request: agents::EditFileRequest,
    ) -> Result<agents::EditFileResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }

    async fn list_remote_tools(
        &self,
        _request_context: &RequestContext,
        _request: agents::ListRemoteToolsRequest,
    ) -> Result<agents::ListRemoteToolsResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }

    async fn run_remote_tool(
        &self,
        _request_context: &RequestContext,
        _request: agents::RunRemoteToolRequest,
    ) -> Result<agents::RunRemoteToolResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }

    async fn check_tool_safety(
        &self,
        _request_context: &RequestContext,
        _request: agents::CheckToolSafetyRequest,
    ) -> Result<agents::CheckToolSafetyResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }

    async fn revoke_tool_access(
        &self,
        _request_context: &RequestContext,
        _request: agents::RevokeToolAccessRequest,
    ) -> Result<agents::RevokeToolAccessResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }

    async fn test_tool_connection(
        &self,
        _request_context: &RequestContext,
        _request: agents::TestToolConnectionRequest,
    ) -> Result<agents::TestToolConnectionResponse, tonic::Status> {
        Err(tonic::Status::unavailable("agents not ready"))
    }
}

pub fn register_client_flags(registry: &feature_flags::RegistryHandle) {
    LLM_GENERATE_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering LLM_GENERATE_TIMEOUT_MS_FLAG");
    CODEBASE_RETRIEVAL_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CODEBASE_RETRIEVAL_TIMEOUT_MS_FLAG");
    EDIT_FILE_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering EDIT_FILE_TIMEOUT_MS_FLAG");
    LIST_REMOTE_TOOLS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering LIST_REMOTE_TOOLS_TIMEOUT_MS_FLAG");
    RUN_REMOTE_TOOL_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering RUN_REMOTE_TOOL_TIMEOUT_MS_FLAG");
    CHECK_TOOL_SAFETY_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CHECK_TOOL_SAFETY_TIMEOUT_MS_FLAG");
    REVOKE_TOOL_ACCESS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering REVOKE_TOOL_ACCESS_TIMEOUT_MS_FLAG");
    TEST_TOOL_CONNECTION_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering TEST_TOOL_CONNECTION_TIMEOUT_MS_FLAG");
}

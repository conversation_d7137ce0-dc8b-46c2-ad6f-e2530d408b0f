use actix_web::{HttpRequest, HttpResponse};
use tracing_actix_web::RootSpan;

use crate::handler_utils::{request_context_from_req, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>};
use crate::model_registry::ModelRegistry;
use crate::public_api_proto;
use content_manager_client::ContentManagerClient;

use github_processor_client::github_processor;

pub const LIST_GITHUB_REPOS_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("list_github_repos_timeout_ms", 30 * 1000);

pub const LIST_GITHUB_BRANCHES_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("list_github_branches_timeout_ms", 30 * 1000);

pub const GET_GITHUB_REPO_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("get_github_repo_timeout_ms", 30 * 1000);

pub const IS_USER_GITHUB_CONFIGURED_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("is_user_github_configured_timeout_ms", 30 * 1000);

impl From<github_processor::GithubRepo> for public_api_proto::GithubRepo {
    fn from(repo: github_processor::GithubRepo) -> Self {
        Self {
            owner: repo.owner,
            name: repo.name,
            html_url: Some(repo.html_url),
            created_at: Some(repo.created_at),
            updated_at: Some(repo.updated_at),
            default_branch: Some(repo.default_branch),
        }
    }
}

impl From<&public_api_proto::GithubRepo> for github_processor::GithubRepo {
    fn from(repo: &public_api_proto::GithubRepo) -> Self {
        Self {
            owner: repo.owner.clone(),
            name: repo.name.clone(),
            html_url: repo.html_url.clone().unwrap_or_default(),
            created_at: repo.created_at.clone().unwrap_or_default(),
            updated_at: repo.updated_at.clone().unwrap_or_default(),
            default_branch: repo.default_branch.clone().unwrap_or_default(),
        }
    }
}

impl From<github_processor::ListGithubReposForAuthenticatedUserResponse>
    for public_api_proto::ListGithubReposForAuthenticatedUserResponse
{
    fn from(response: github_processor::ListGithubReposForAuthenticatedUserResponse) -> Self {
        Self {
            repos: response.repos.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<github_processor::GithubBranchCommit> for public_api_proto::GithubBranchCommit {
    fn from(commit: github_processor::GithubBranchCommit) -> Self {
        Self {
            sha: commit.sha,
            url: commit.url,
        }
    }
}

impl From<github_processor::GithubBranch> for public_api_proto::GithubBranch {
    fn from(branch: github_processor::GithubBranch) -> Self {
        Self {
            name: branch.name,
            commit: branch.commit.map(|commit| commit.into()),
            protected: branch.protected,
        }
    }
}

impl From<github_processor::ListGithubRepoBranchesResponse>
    for public_api_proto::ListGithubRepoBranchesResponse
{
    fn from(response: github_processor::ListGithubRepoBranchesResponse) -> Self {
        Self {
            branches: response.branches.into_iter().map(Into::into).collect(),
            has_next_page: response.has_next_page,
            next_page: response.next_page,
        }
    }
}

impl From<github_processor::GithubUser> for public_api_proto::GithubUser {
    fn from(user: github_processor::GithubUser) -> Self {
        Self {
            login: user.login,
            avatar_url: user.avatar_url,
            html_url: user.html_url,
        }
    }
}

impl From<github_processor::GithubPullRequest> for public_api_proto::GithubPullRequest {
    fn from(pr: github_processor::GithubPullRequest) -> Self {
        Self {
            number: pr.number,
            title: pr.title,
            body: pr.body,
            state: pr.state,
            html_url: pr.html_url,
            created_at: pr.created_at,
            updated_at: pr.updated_at,
            merged_at: pr.merged_at,
            closed_at: pr.closed_at,
            user: pr.user.map(Into::into),
            head_ref: pr.head_ref,
            base_ref: pr.base_ref,
            head_sha: pr.head_sha,
            base_sha: pr.base_sha,
            merged: pr.merged,
            draft: pr.draft,
            comments: pr.comments,
            commits: pr.commits,
            additions: pr.additions,
            deletions: pr.deletions,
            changed_files: pr.changed_files,
        }
    }
}

impl From<public_api_proto::ListGithubReposForAuthenticatedUserRequest>
    for github_processor::ListGithubReposForAuthenticatedUserRequest
{
    fn from(_: public_api_proto::ListGithubReposForAuthenticatedUserRequest) -> Self {
        Self {}
    }
}

impl From<public_api_proto::ListGithubRepoBranchesRequest>
    for github_processor::ListGithubRepoBranchesRequest
{
    fn from(request: public_api_proto::ListGithubRepoBranchesRequest) -> Self {
        Self {
            repo: request.repo.as_ref().map(Into::into),
            page: request.page,
        }
    }
}

impl From<public_api_proto::GetGithubRepoRequest> for github_processor::GetRepoRequest {
    fn from(request: public_api_proto::GetGithubRepoRequest) -> Self {
        Self {
            repo: request.repo.as_ref().map(Into::into),
        }
    }
}

impl From<github_processor::GetRepoResponse> for public_api_proto::GetGithubRepoResponse {
    fn from(response: github_processor::GetRepoResponse) -> Self {
        Self {
            repo: response.repo.map(Into::into),
        }
    }
}

impl From<public_api_proto::IsUserGithubConfiguredRequest>
    for github_processor::IsUserOAuthConfiguredRequest
{
    fn from(_: public_api_proto::IsUserGithubConfiguredRequest) -> Self {
        Self {}
    }
}

impl From<public_api_proto::CreatePullRequestRequest>
    for github_processor::CreatePullRequestRequest
{
    fn from(request: public_api_proto::CreatePullRequestRequest) -> Self {
        Self {
            repo: request.repo.as_ref().map(Into::into),
            title: request.title,
            body: request.body,
            head: request.head,
            base: request.base,
            draft: request.draft,
        }
    }
}

impl From<github_processor::CreatePullRequestResponse>
    for public_api_proto::CreatePullRequestResponse
{
    fn from(response: github_processor::CreatePullRequestResponse) -> Self {
        Self {
            pull_request: response.pull_request.map(Into::into),
        }
    }
}

impl From<github_processor::IsUserOAuthConfiguredResponse>
    for public_api_proto::IsUserGithubConfiguredResponse
{
    fn from(response: github_processor::IsUserOAuthConfiguredResponse) -> Self {
        Self {
            is_configured: response.is_configured,
            oauth_url: String::new(), // Empty string until proto is regenerated
            configured_but_needs_update: response.configured_but_needs_update,
            updated_scopes: response.updated_scopes,
        }
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::ListGithubReposForAuthenticatedUserRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::ListGithubReposForAuthenticatedUserRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("list github repos request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let _feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        let timeout_ms = LIST_GITHUB_REPOS_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;

        let inner_request = request.into();

        let inner_result = self
            .github_processor_client
            .list_github_repos_for_authenticated_user(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;

        let result: public_api_proto::ListGithubReposForAuthenticatedUserResponse =
            inner_result.into();

        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::ListGithubRepoBranchesRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::ListGithubRepoBranchesRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("list github branches request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let _feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        let timeout_ms = LIST_GITHUB_BRANCHES_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;

        let inner_request = request.into();

        let inner_result = self
            .github_processor_client
            .list_branches_for_repo(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;

        let result: public_api_proto::ListGithubRepoBranchesResponse = inner_result.into();

        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::GetGithubRepoRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::GetGithubRepoRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("get github repo request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let _feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        let timeout_ms = GET_GITHUB_REPO_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;

        let inner_request = request.into();

        let inner_result = self
            .github_processor_client
            .get_repo(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;

        let result: public_api_proto::GetGithubRepoResponse = inner_result.into();

        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::IsUserGithubConfiguredRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::IsUserGithubConfiguredRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("is user github configured request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let _feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        let timeout_ms =
            IS_USER_GITHUB_CONFIGURED_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;

        let inner_request = request.into();

        let inner_result = self
            .github_processor_client
            .is_user_oauth_configured(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;

        let result: public_api_proto::IsUserGithubConfiguredResponse = inner_result.into();

        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::CreatePullRequestRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::CreatePullRequestRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("create github pull request request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let _feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        let timeout_ms = CREATE_PULL_REQUEST_TIMEOUT_MS_FLAG.get_from(&self.feature_flags) as u64;

        let inner_request = request.into();

        let inner_result = self
            .github_processor_client
            .create_pull_request(
                &request_context,
                inner_request,
                std::time::Duration::from_millis(timeout_ms),
            )
            .await?;

        let result: public_api_proto::CreatePullRequestResponse = inner_result.into();

        Ok(HttpResponse::Ok().json(result))
    }
}

pub const CREATE_PULL_REQUEST_TIMEOUT_MS_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("create_pull_request_timeout_ms", 30 * 1000);

pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {
    LIST_GITHUB_REPOS_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering LIST_GITHUB_REPOS_TIMEOUT_MS_FLAG");
    LIST_GITHUB_BRANCHES_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering LIST_GITHUB_BRANCHES_TIMEOUT_MS_FLAG");
    GET_GITHUB_REPO_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering GET_GITHUB_REPO_TIMEOUT_MS_FLAG");
    IS_USER_GITHUB_CONFIGURED_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering IS_USER_GITHUB_CONFIGURED_TIMEOUT_MS_FLAG");

    CREATE_PULL_REQUEST_TIMEOUT_MS_FLAG
        .register(registry)
        .expect("Registering CREATE_PULL_REQUEST_TIMEOUT_MS_FLAG");
}

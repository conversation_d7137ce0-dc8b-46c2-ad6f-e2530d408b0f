// Stateful Circuit Breaker
// Trips when some percent of requests failed with unavailable in the last few minutes
// Resets all state a few minutes later

use std::collections::HashMap;
use std::time::{Duration, Instant};

pub struct CircuitBreaker {
    history: HashMap<Instant, bool>, // true for success, false for failure
    trip_time: Option<Instant>,      // Trip time if tripped, None otherwise
    failure_threshold: f64,          // Ratio of failures to total requests to trip
    minimum_requests: u64,           // Minimum number of requests to consider for tripping
    failure_window: Duration,        // Window of time to consider for failures
    reset_threshold: Duration,       // Time to wait before resetting after tripping
}

impl CircuitBreaker {
    pub fn new(
        failure_threshold: f64,
        minimum_requests: u64,
        failure_window_seconds: u64,
        reset_threshold_seconds: u64,
    ) -> Self {
        Self {
            history: HashMap::new(),
            trip_time: None,
            failure_threshold,
            minimum_requests,
            failure_window: Duration::from_secs(failure_window_seconds),
            reset_threshold: Duration::from_secs(reset_threshold_seconds),
        }
    }

    pub fn is_tripped(&mut self) -> bool {
        if let Some(trip_time) = self.trip_time {
            if trip_time.elapsed() > self.reset_threshold {
                self.reset();
            }
        }
        self.trip_time.is_some()
    }

    /*
        Record success/failure and updates trip state
        Returns most current trip state
    */
    pub fn record_request(&mut self, success: bool) -> bool {
        // Reset if past reset threshold
        if let Some(trip_time) = self.trip_time {
            if trip_time.elapsed() > self.reset_threshold {
                self.reset();
            }
        }
        // Add success/failure timestamp to history
        self.history.insert(Instant::now(), success);
        // Check if still tripped
        if self.trip_time.is_some() {
            return false;
        }
        // Count failures in window
        let mut failures: u64 = 0;
        let mut successes: u64 = 0;
        for (time, result) in self.history.clone() {
            if time.elapsed() > self.failure_window {
                self.history.remove(&time);
            } else if result {
                successes += 1;
            } else {
                failures += 1;
            }
        }
        let total = successes + failures;
        if total < self.minimum_requests {
            return false;
        }
        if failures as f64 / total as f64 > self.failure_threshold {
            self.trip_time = Some(Instant::now());
            tracing::info!(
                "CBF tripped, {} failures out of {} requests in last {} seconds",
                failures,
                total,
                self.failure_window.as_secs()
            );
            true
        } else {
            false
        }
    }

    // Only used in tests at the moment
    #[allow(dead_code)]
    pub fn clear_history(&mut self) {
        self.history.clear();
    }

    pub fn reset(&mut self) {
        tracing::info!("CBF reset");
        self.trip_time = None;
    }

    pub fn set_failure_threshold(&mut self, threshold: f64) {
        self.failure_threshold = threshold;
    }

    pub fn set_minimum_requests(&mut self, min_requests: u64) {
        self.minimum_requests = min_requests;
    }

    pub fn set_failure_window(&mut self, window_secs: u64) {
        self.failure_window = Duration::from_secs(window_secs);
    }

    pub fn set_reset_threshold(&mut self, threshold_secs: u64) {
        self.reset_threshold = Duration::from_secs(threshold_secs);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_circuit_breaker_sanity() {
        let mut cb = CircuitBreaker::new(0.5, 3, 1, 1);
        assert!(!cb.record_request(false)); // Does not trip before hitting 3 requests
        assert!(!cb.record_request(true)); // Does not trip before hitting 3 requests
        assert!(!cb.record_request(true));
        assert!(!cb.record_request(true));
        assert!(!cb.record_request(true));
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false));
        assert!(cb.record_request(false)); // Trips, 5 out of 9 failed > 50%
        assert!(cb.is_tripped());

        // Wait one second to allow time to reset
        std::thread::sleep(std::time::Duration::from_secs(1));
        assert!(!cb.is_tripped());
        assert!(!cb.record_request(false)); // Does not trip before hitting 3 requests
        assert!(!cb.record_request(true)); // Does not trip before hitting 3 requests
        assert!(!cb.record_request(true)); // Does not trip before hitting 3 requests
        assert!(!cb.record_request(false));
        assert!(cb.record_request(false)); // Trips, 3 out of 5 failed > 50%
        assert!(cb.is_tripped());
    }

    #[test]
    fn test_circuit_breaker_updates() {
        let mut cb = CircuitBreaker::new(0.1, 20, 100, 100);
        cb.set_minimum_requests(1);
        cb.set_failure_threshold(0.9);
        cb.set_failure_window(1);
        cb.set_reset_threshold(1);
        assert!(cb.record_request(false)); // Trips immedately (100% fail)

        // Reset works in 1s after updating reset threshold
        std::thread::sleep(std::time::Duration::from_secs(1));
        assert!(!cb.is_tripped());
        assert!(!cb.record_request(true));
        assert!(!cb.record_request(false)); // 50% failure
        assert!(!cb.record_request(false)); // 66% failure
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false));
        assert!(!cb.record_request(false)); // 90% failure
        assert!(cb.record_request(false)); // Over 90% failure - trips
    }
}

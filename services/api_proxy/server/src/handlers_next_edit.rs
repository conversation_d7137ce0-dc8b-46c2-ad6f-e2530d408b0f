use std::convert::TryFrom;
use std::time::Duration;

use crate::api_auth::User;
use crate::augment::model_instance_config::ModelType;
use crate::base::diff_utils::GranularEditEvent;
use crate::generation_clients::Client;
use crate::handler_utils::{
    convert_blobs_and_names, convert_replacement_text, gate_on_circuit_breaker, get_model,
    request_context_from_req, streaming_http_response_from_receiver, Hand<PERSON>,
};
use crate::model_registry;
use crate::next_edit::{self, NextEditRequest, NextEditResponse};
use crate::public_api_proto;
use crate::request_insight_util::extract_request_metadata;
use content_manager_client::ContentManagerClient;
use tracing_actix_web::RootSpan;

use actix_web::{web, HttpRequest, HttpResponse};
use model_registry::ModelRegistry;
use request_context::{RequestContext, TenantInfo};
use request_insight_publisher::request_insight;
use serde::{Deserialize, Deserializer};
use tokio::sync::mpsc::Receiver;

pub const MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("next_edit_model", "");

pub const NEXT_EDIT_USE_STREAM_MUX_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("next_edit_stream_mux", false);

pub const NEXT_EDIT_SLOW_PAYLOAD_THRESHOLD_MS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_slow_payload_threshold_ms_next_edit", 1000);

pub const CB_NEXT_EDIT: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_next_edit", false);

pub fn deserialize_change_type<'de, D>(deserializer: D) -> Result<i32, D::Error>
where
    D: Deserializer<'de>,
{
    let s: &str = Deserialize::deserialize(deserializer)?;
    match public_api_proto::ChangeType::from_str_name(s) {
        Some(c) => Ok(c.into()),
        None => Err(serde::de::Error::custom(format!(
            "invalid change type: {s}"
        ))),
    }
}

pub fn deserialize_severity<'de, D>(deserializer: D) -> Result<i32, D::Error>
where
    D: Deserializer<'de>,
{
    let s: &str = Deserialize::deserialize(deserializer)?;
    match public_api_proto::DiagnosticSeverity::from_str_name(s) {
        Some(c) => Ok(c.into()),
        None => Err(serde::de::Error::custom(format!(
            "invalid diagnostic severity: {s}"
        ))),
    }
}

pub fn deserialize_next_edit_scope<'de, D>(deserializer: D) -> Result<i32, D::Error>
where
    D: Deserializer<'de>,
{
    let s: &str = Deserialize::deserialize(deserializer)?;
    match public_api_proto::NextEditScope::from_str_name(s) {
        Some(c) => Ok(c.into()),
        None => Err(serde::de::Error::custom(format!(
            "invalid next edit scope: {s}"
        ))),
    }
}

pub fn deserialize_next_edit_mode<'de, D>(deserializer: D) -> Result<i32, D::Error>
where
    D: Deserializer<'de>,
{
    let s: &str = Deserialize::deserialize(deserializer)?;
    match public_api_proto::NextEditMode::from_str_name(s) {
        Some(c) => Ok(c.into()),
        None => Err(serde::de::Error::custom(format!(
            "invalid next edit mode: {s}"
        ))),
    }
}

pub fn deserialize_next_edit_diagnostics<'de, D>(
    deserializer: D,
) -> Result<Vec<public_api_proto::Diagnostic>, D::Error>
where
    D: Deserializer<'de>,
{
    let s: Option<Vec<public_api_proto::Diagnostic>> = Deserialize::deserialize(deserializer)?;
    Ok(s.unwrap_or_default())
}

pub fn deserialize_next_edit_recent_changes<'de, D>(
    deserializer: D,
) -> Result<Vec<public_api_proto::ReplacementText>, D::Error>
where
    D: Deserializer<'de>,
{
    let s: Option<Vec<public_api_proto::ReplacementText>> = Deserialize::deserialize(deserializer)?;
    Ok(s.unwrap_or_default())
}

fn convert_vcs_change(
    vcs_change: &public_api_proto::VcsChange,
) -> Result<next_edit::VcsChange, tonic::Status> {
    Ok(next_edit::VcsChange {
        working_directory_changes: vcs_change
            .working_directory_changes
            .iter()
            .map(|wdc| next_edit::WorkingDirectoryChange {
                before_path: wdc.before_path.clone(),
                after_path: wdc.after_path.clone(),
                change_type: wdc.change_type,
                head_blob_name: wdc.head_blob_name.clone(),
                indexed_blob_name: wdc.indexed_blob_name.clone(),
                current_blob_name: wdc.current_blob_name.clone(),
            })
            .collect(),
    })
}

fn convert_file_location(location: &public_api_proto::FileLocation) -> next_edit::FileLocation {
    next_edit::FileLocation {
        path: location.path.clone(),
        line_start: location.line_start,
        line_end: location.line_end,
    }
}

fn convert_file_region(location: &public_api_proto::FileRegion) -> next_edit::FileRegion {
    next_edit::FileRegion {
        path: location.path.clone(),
        char_start: location.char_start,
        char_end: location.char_end,
    }
}

fn convert_diagnostics(
    diagnostics: &Option<Vec<public_api_proto::Diagnostic>>,
) -> Result<Vec<next_edit::Diagnostic>, tonic::Status> {
    match diagnostics {
        Some(diagnostics) => Ok(diagnostics
            .iter()
            .map(|d| next_edit::Diagnostic {
                location: d.location.as_ref().map(convert_file_location),
                message: d.message.clone(),
                severity: d.severity,
                current_blob_name: d.current_blob_name.clone(),
                blob_name: d.blob_name.clone(),
                char_start: d.char_start,
                char_end: d.char_end,
            })
            .collect()),
        None => Ok(vec![]),
    }
}

impl TryFrom<(public_api_proto::NextEditRequest, &String)> for NextEditRequest {
    type Error = tonic::Status;

    /// convert the frontend completion request into the modelhost api
    fn try_from(
        from: (public_api_proto::NextEditRequest, &String),
    ) -> Result<NextEditRequest, tonic::Status> {
        let (req, model_name) = from;

        // When converting the frontend request to the backend request, if
        // there is a workspace specified in delta format, try to convert it
        // into the proto version. This may fail if the blob ids are not
        // valid hex strings.
        let blobs = convert_blobs_and_names(&req.blobs, None)?;

        #[allow(deprecated)]
        let vcs_change = convert_vcs_change(&req.vcs_change.unwrap_or_default())?;
        let recent_changes = convert_replacement_text(&req.recent_changes);
        let diagnostics = convert_diagnostics(&Some(req.diagnostics))?;
        let blocked_locations: Vec<next_edit::FileRegion> = req
            .blocked_locations
            .into_iter()
            .map(|location| convert_file_region(&location))
            .collect();

        #[allow(deprecated)]
        Ok(NextEditRequest {
            model_name: model_name.clone(),
            instruction: req.instruction,
            vcs_change: Some(vcs_change),
            blobs: Some(blobs),
            recent_changes,
            diagnostics,
            lang: req.lang.clone(),
            sequence_id: req.sequence_id,
            path: req.path,
            blob_name: req.blob_name,
            selection_begin_char: req.selection_begin_char,
            selection_end_char: req.selection_end_char,
            prefix: req.prefix.unwrap_or_default(),
            selected_text: req.selected_text.unwrap_or_default(),
            suffix: req.suffix.unwrap_or_default(),
            mode: req.mode,
            scope: req.scope,
            change_probability_override: req.change_probability_override,
            edit_events: req
                .edit_events
                .into_iter()
                .map(TryFrom::try_from)
                .collect::<Result<Vec<GranularEditEvent>, _>>()
                .ok()
                .unwrap_or_default(),
            blocked_locations,
            client_created_at: req.client_created_at,
            api_version: req.api_version.unwrap_or(0),
            unindexed_edit_events: req
                .unindexed_edit_events
                .into_iter()
                .map(TryFrom::try_from)
                .collect::<Result<Vec<GranularEditEvent>, _>>()
                .ok()
                .unwrap_or_default(),
            unindexed_edit_events_base_blob_names: req.unindexed_edit_events_base_blob_names,
        })
    }
}

fn convert_diff_spans(diff_spans: Vec<next_edit::DiffSpan>) -> Vec<public_api_proto::DiffSpan> {
    diff_spans
        .into_iter()
        .map(|d| public_api_proto::DiffSpan {
            original: d.original.map(|r| public_api_proto::CharRange {
                start: r.start,
                stop: r.stop,
            }),
            updated: d.updated.map(|r| public_api_proto::CharRange {
                start: r.start,
                stop: r.stop,
            }),
        })
        .collect()
}

fn convert_next_edit_result(
    suggested_edit: Option<next_edit::ScoredFileHunk>,
) -> Option<public_api_proto::NextEditResult> {
    suggested_edit.map(|l| public_api_proto::NextEditResult {
        path: l.path.clone(),
        blob_name: l.blob_name.clone(),
        char_start: l.char_start,
        char_end: l.char_end,
        existing_code: l.existing_code.clone(),
        suggested_code: l.suggested_code.clone(),
        truncation_char: l.truncation_char,
        diff_spans: convert_diff_spans(l.diff_spans.clone()),
        change_description: l.change_description.clone(),
        suggestion_id: l.suggestion_id.clone(),
        localization_score: l.localization_score,
        editing_score: l.editing_score,
        editing_score_threshold: l.editing_score_threshold,
        markdown_change_description: l.markdown_change_description.clone(),
    })
}

impl From<next_edit::NextEditResponse> for public_api_proto::NextEditResponse {
    /// transfer from a next edit response to the public API next edit response.
    fn from(resp: next_edit::NextEditResponse) -> Self {
        let next_edit = convert_next_edit_result(resp.suggested_edit);
        public_api_proto::NextEditResponse {
            next_edit,
            unknown_blob_names: resp.unknown_blob_names.clone(),
            checkpoint_not_found: resp.checkpoint_not_found,
        }
    }
}

pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {
    MODEL_FLAG
        .register(registry)
        .expect("Registering next edit MODEL_FLAG");
}

async fn next_edit_stream<MR: ModelRegistry, CNC: ContentManagerClient>(
    data: web::Data<Handler<MR, CNC>>,
    req: &HttpRequest,
    user: &User,
    tenant_info: &TenantInfo,
    request_context: &RequestContext,
    front_next_edit_request: public_api_proto::NextEditRequest,
    root_span: RootSpan,
) -> Result<Receiver<tonic::Result<next_edit::NextEditResponse>>, tonic::Status> {
    tracing::info!(
        "next_edit request model={:?}",
        front_next_edit_request.model,
    );
    root_span.record("tenant_name", &tenant_info.tenant_name.clone());
    root_span.record("opaque_user_id", &user.opaque_user_id.user_id);

    let feature_flags = data.get_feature_flags(user, tenant_info, Some(req))?;
    gate_on_circuit_breaker(&CB_NEXT_EDIT, &feature_flags, req, tenant_info)?;

    let metadata = extract_request_metadata(
        request_context,
        request_insight::RequestType::NextEdit,
        user,
        req,
    );
    data.request_insight_publisher
        .record_request_metadata(request_context, tenant_info, metadata)
        .await;

    let (com, mi) = get_model::<MR>(
        front_next_edit_request.model.as_ref(),
        &data.model_registry,
        &feature_flags,
        &MODEL_FLAG,
        ModelType::NextEdit,
    )
    .await?;

    let com = match com {
        Client::NextEdit(c) => c.clone(),
        _ => {
            // If user specified a model, it's their fault (400)
            // If we chose the model from feature flags, it's our fault (500)
            return if front_next_edit_request.model.is_some() {
                Err(tonic::Status::invalid_argument(
                    "Specified model is not a next edit model",
                ))
            } else {
                Err(tonic::Status::internal(
                    "Default model is not a next edit model",
                ))
            };
        }
    };
    let next_edit_request: NextEditRequest = (front_next_edit_request, &mi.name).try_into()?;
    com.next_edit_stream(
        request_context,
        next_edit_request,
        NEXT_EDIT_USE_STREAM_MUX_FLAG.get_from(&feature_flags),
    )
    .await
}

// TODO: This and chat_stream_api_auth are very similar. Future refactoring should reuse shared logic.
pub async fn next_edit_stream_api_auth<MR: ModelRegistry, CNC: ContentManagerClient>(
    data: web::Data<Handler<MR, CNC>>,
    req: HttpRequest,
    item: web::Json<public_api_proto::NextEditRequest>,
    root_span: RootSpan,
) -> HttpResponse {
    let Ok((user, tenant_info, request_context, start_time)) = request_context_from_req(&req)
    else {
        return HttpResponse::InternalServerError().finish();
    };

    // check for slow payload
    let Ok(feature_flags) = data.get_feature_flags(&user, &tenant_info, Some(&req)) else {
        return HttpResponse::InternalServerError().finish();
    };
    let slow_payload_threshold_ms =
        NEXT_EDIT_SLOW_PAYLOAD_THRESHOLD_MS.get_from(&feature_flags) as u64;
    if slow_payload_threshold_ms > 0
        && start_time.elapsed() > Duration::from_millis(slow_payload_threshold_ms)
    {
        tracing::warn!(
            "Next edit request payload took more than {}ms to arrive",
            slow_payload_threshold_ms
        );
        return HttpResponse::RequestTimeout().finish(); // HTTP 408
    }

    let request_insight_publisher = data.request_insight_publisher.clone();
    let receiver_result: Result<Receiver<Result<NextEditResponse, tonic::Status>>, tonic::Status> =
        next_edit_stream(
            data,
            &req,
            &user,
            &tenant_info,
            &request_context,
            item.into_inner(),
            root_span,
        )
        .await;
    streaming_http_response_from_receiver::<NextEditResponse, public_api_proto::NextEditResponse>(
        "next-edit-stream",
        receiver_result,
        request_context,
        tenant_info,
        request_insight_publisher,
        &feature_flags,
    )
    .await
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use crate::augment::model_instance_config::ModelType;
    use crate::base::blob_names::Blobs;
    use crate::handler_utils::tests::{
        create_model_instance_config, new_root_span, setup_app_state, setup_req,
    };
    use crate::model_registry::tests::FakeClientFactory;
    use crate::model_registry::{DynamicModelRegistry, ModelRegistry};
    use crate::next_edit::{NextEditMode, NextEditRequest, NextEditScope, VcsChange};
    use crate::public_api_proto;
    use actix_web::body::MessageBody;
    use actix_web::{body, http, rt::pin, web, HttpResponse};
    use futures::future;

    use super::next_edit_stream_api_auth;

    async fn add_next_edit_model(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![create_model_instance_config(
                "model1NextEdit",
                ModelType::NextEdit,
                1,
            )])
            .await
            .unwrap();
    }

    async fn run_next_edit_stream(
        request: &public_api_proto::NextEditRequest,
    ) -> (NextEditRequest, Vec<public_api_proto::NextEditResponse>) {
        let (resp, fake_client_factory) = run_next_edit_stream_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        let mut results = Vec::new();
        let body = resp.into_body();
        pin!(body);

        while let Some(chunk) = future::poll_fn(|cx| body.as_mut().poll_next(cx)).await {
            let chunk = chunk.expect("Error while reading response chunk");
            if let Ok(response) =
                serde_json::from_slice::<public_api_proto::NextEditResponse>(&chunk)
            {
                results.push(response);
            }
        }

        let modelhost_request = get_last_next_edit_request(fake_client_factory).await;
        (modelhost_request, results)
    }

    async fn run_next_edit_stream_raw(
        request: &public_api_proto::NextEditRequest,
    ) -> (HttpResponse, Arc<FakeClientFactory>) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_next_edit_model(&registry).await;
        }
        let root_span = new_root_span();
        let resp = next_edit_stream_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        (resp, fake_client_factory)
    }

    async fn get_last_next_edit_request(
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> NextEditRequest {
        match fake_client_factory
            .fake_next_edit_clients
            .lock()
            .unwrap()
            .last()
        {
            Some(client) => client.get_last_next_edit_request(),
            None => panic!("No client"),
        }
    }

    #[actix_web::test]
    async fn test_next_edit_model_not_specified() {
        #[allow(deprecated)]
        let (modelhost_request, responses) =
            run_next_edit_stream(&public_api_proto::NextEditRequest {
                model: None,
                sequence_id: 0,
                lang: None,
                instruction: "Add useful comments".to_string(),
                blobs: Some(public_api_proto::Blobs::default()),
                recent_changes: vec![],
                vcs_change: Some(public_api_proto::VcsChange::default()),
                path: Some("foo/bar.txt".to_string()),
                blob_name: None,
                selection_begin_char: None,
                selection_end_char: None,
                prefix: Some("".to_string()),
                selected_text: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
                suffix: Some("".to_string()),
                diagnostics: vec![],
                mode: public_api_proto::NextEditMode::Background.into(),
                scope: public_api_proto::NextEditScope::Cursor.into(),
                change_probability_override: None,
                edit_events: vec![],
                blocked_locations: vec![],
                api_version: None,
                client_created_at: None,
                unindexed_edit_events: vec![],
                unindexed_edit_events_base_blob_names: vec![],
            })
            .await;
        assert!(responses.first().unwrap().next_edit.is_some());

        #[allow(deprecated)]
        let expected_request = NextEditRequest {
            model_name: "model1NextEdit".to_string(),
            sequence_id: 0,
            lang: None,
            instruction: "Add useful comments".to_string(),
            blobs: Some(Blobs::default()),
            recent_changes: vec![],
            vcs_change: Some(VcsChange::default()),
            path: Some("foo/bar.txt".to_string()),
            blob_name: None,
            selection_begin_char: None,
            selection_end_char: None,
            prefix: "".to_string(),
            selected_text: "fn sort(&vec: Vec<u8>) {}".to_string(),
            suffix: "".to_string(),
            diagnostics: vec![],
            mode: NextEditMode::Background.into(),
            scope: NextEditScope::Cursor.into(),
            change_probability_override: None,
            edit_events: vec![],
            blocked_locations: vec![],
            api_version: 0,
            client_created_at: None,
            unindexed_edit_events: vec![],
            unindexed_edit_events_base_blob_names: vec![],
        };
        assert_eq!(modelhost_request, expected_request);
    }

    #[actix_web::test]
    async fn test_next_edit_model_with_blobs() {
        #[allow(deprecated)]
        let default_frontend_req = public_api_proto::NextEditRequest {
            model: None,
            sequence_id: 0,
            lang: None,
            instruction: "Add useful comments".to_string(),
            blobs: None,
            recent_changes: vec![],
            vcs_change: Some(public_api_proto::VcsChange::default()),
            path: Some("foo/bar.txt".to_string()),
            blob_name: None,
            selection_begin_char: None,
            selection_end_char: None,
            prefix: Some("".to_string()),
            selected_text: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            suffix: Some("".to_string()),
            diagnostics: vec![],
            mode: public_api_proto::NextEditMode::Background.into(),
            scope: public_api_proto::NextEditScope::Cursor.into(),
            change_probability_override: None,
            edit_events: vec![],
            blocked_locations: vec![],
            api_version: None,
            client_created_at: None,
            unindexed_edit_events: vec![],
            unindexed_edit_events_base_blob_names: vec![],
        };

        #[allow(deprecated)]
        let default_next_edit_req = NextEditRequest {
            model_name: "model1NextEdit".to_string(),
            sequence_id: 0,
            lang: None,
            instruction: "Add useful comments".to_string(),
            blobs: None,
            recent_changes: vec![],
            vcs_change: Some(VcsChange::default()),
            path: Some("foo/bar.txt".to_string()),
            blob_name: None,
            selection_begin_char: None,
            selection_end_char: None,
            prefix: "".to_string(),
            selected_text: "fn sort(&vec: Vec<u8>) {}".to_string(),
            suffix: "".to_string(),
            diagnostics: vec![],
            mode: NextEditMode::Background.into(),
            scope: NextEditScope::Cursor.into(),
            change_probability_override: None,
            edit_events: vec![],
            blocked_locations: vec![],
            api_version: 0,
            client_created_at: None,
            unindexed_edit_events: vec![],
            unindexed_edit_events_base_blob_names: vec![],
        };

        for use_blobs in [true, false] {
            let blobs = if use_blobs {
                Some(public_api_proto::Blobs {
                    checkpoint_id: None,
                    added_blobs: vec!["cafe01".to_string()],
                    deleted_blobs: vec![],
                })
            } else {
                None
            };

            let blobs_out = Blobs {
                baseline_checkpoint_id: None,
                added: vec![hex::decode("cafe01").unwrap()],
                deleted: vec![],
            };

            let frontend_req = public_api_proto::NextEditRequest {
                blobs,
                ..default_frontend_req.clone()
            };

            let next_edit_req = NextEditRequest {
                blobs: Some(blobs_out),
                ..default_next_edit_req.clone()
            };

            if !use_blobs {
                // In this case, there are no blobs, which is invalid
                let (resp, _) = run_next_edit_stream_raw(&frontend_req).await;
                assert_eq!(resp.status(), http::StatusCode::BAD_REQUEST);
                continue;
            };

            let (modelhost_request, _responses) = run_next_edit_stream(&frontend_req).await;
            assert_eq!(modelhost_request, next_edit_req);
        }
    }

    #[actix_web::test]
    async fn test_next_edit_stream() {
        #[allow(deprecated)]
        let request = &public_api_proto::NextEditRequest {
            model: None,
            sequence_id: 0,
            lang: None,
            instruction: "Add useful comments".to_string(),
            blobs: Some(public_api_proto::Blobs::default()),
            recent_changes: vec![],
            vcs_change: Some(public_api_proto::VcsChange::default()),
            path: Some("foo/bar.txt".to_string()),
            blob_name: None,
            selection_begin_char: None,
            selection_end_char: None,
            prefix: Some("".to_string()),
            selected_text: Some("fn sort(&vec: Vec<u8>) {}".to_string()),
            suffix: Some("".to_string()),
            diagnostics: vec![],
            mode: public_api_proto::NextEditMode::Background.into(),
            scope: public_api_proto::NextEditScope::Cursor.into(),
            change_probability_override: None,
            edit_events: vec![],
            blocked_locations: vec![],
            api_version: None,
            client_created_at: None,
            unindexed_edit_events: vec![],
            unindexed_edit_events_base_blob_names: vec![],
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) =
            run_next_edit_stream_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        // Read the stream body from response
        let box_body: body::BoxBody = resp.into_body();
        pin!(box_body);

        // first suggestion
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        let front_next_edit_response =
            serde_json::from_slice::<public_api_proto::NextEditResponse>(&bytes).unwrap();
        assert!(front_next_edit_response.next_edit.is_some());
        assert_eq!(front_next_edit_response.unknown_blob_names.len(), 0);
        assert!(!front_next_edit_response.checkpoint_not_found);

        // second suggestion
        let bytes: web::Bytes = future::poll_fn(|cx| box_body.as_mut().poll_next(cx))
            .await
            .unwrap()
            .unwrap();
        let front_next_edit_response =
            serde_json::from_slice::<public_api_proto::NextEditResponse>(&bytes).unwrap();
        assert!(front_next_edit_response.next_edit.is_some());
        assert_eq!(front_next_edit_response.unknown_blob_names.len(), 0);
        assert!(!front_next_edit_response.checkpoint_not_found);
    }

    #[test]
    fn test_deserialize_change_type() {
        let json_data = "{ \"before_path\": \"foo\", \"change_type\": \"MODIFIED\" }";
        let vcs_change: public_api_proto::WorkingDirectoryChange =
            serde_json::from_str(json_data).unwrap();
        assert_eq!(
            vcs_change,
            public_api_proto::WorkingDirectoryChange {
                before_path: Some("foo".to_string()),
                after_path: None,
                change_type: public_api_proto::ChangeType::Modified.into(),
                head_blob_name: None,
                indexed_blob_name: None,
                current_blob_name: None,
            }
        );
    }

    #[test]
    fn test_deserialize_severity() {
        let json_data = "{ \"message\": \"foo\", \"severity\": \"WARNING\", \"current_blob_name\": \"bar\", \"blob_name\": \"baz\", \"char_start\": 0, \"char_end\": 1 }";
        let diagnostic: public_api_proto::Diagnostic = serde_json::from_str(json_data).unwrap();
        assert_eq!(
            diagnostic,
            public_api_proto::Diagnostic {
                message: "foo".to_string(),
                severity: public_api_proto::DiagnosticSeverity::Warning.into(),
                location: None,
                current_blob_name: Some("bar".to_string()),
                blob_name: Some("baz".to_string()),
                char_start: Some(0),
                char_end: Some(1),
            }
        );
    }

    #[test]
    fn test_deserialize_next_edit_request() {
        let json_data = r#"{ "mode": "FORCED", "scope": "CURSOR", "instruction": "foo", "selected_text": "bar", "sequence_id": 1 }"#;
        let next_edit_request: public_api_proto::NextEditRequest =
            serde_json::from_str(json_data).unwrap();
        assert_eq!(
            next_edit_request,
            public_api_proto::NextEditRequest {
                instruction: "foo".to_string(),
                selected_text: Some("bar".to_string()),
                sequence_id: 1,
                mode: public_api_proto::NextEditMode::Forced.into(),
                scope: public_api_proto::NextEditScope::Cursor.into(),
                ..Default::default()
            }
        );
    }

    #[test]
    fn test_deserialize_next_edit_request_with_null_fields() {
        // Test that `null` fields that are nonstandard according to the proto JSON spec, but were
        // allowed by the old type definition, are deserialized reasonably with serde(default)
        let json_data = r#"{ "recent_changes": null, "diagnostics": null }"#;
        let next_edit_request: public_api_proto::NextEditRequest =
            serde_json::from_str(json_data).unwrap();
        assert_eq!(
            next_edit_request,
            public_api_proto::NextEditRequest {
                ..Default::default()
            }
        );
    }
}

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:rust.bzl", "rust_binary", "rust_oci_image", "rust_test")

rust_binary(
    name = "api_proxy_server",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/blob_names:blob_names_rs_proto",
        "//base/blob_names/rust:blob_names",
        "//base/feature_flags:feature_flags_rs",
        "//base/logging:struct_logging_rs",
        "//base/metrics_server/rust:metrics_server",
        "//base/rust/tracing-tonic",
        "//services/auth/central/server:auth_entities_rs_proto",
        "//services/auth/query/client:client_rs",
        "//services/bigtable_proxy/client:client_rs",
        "//services/content_manager:content_manager_rs_proto",
        "//services/content_manager/client:client_rs",
        "//services/integrations/docset/client:client_rs",
        "//services/integrations/github/processor/client:client_rs",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/grpc/stream_mux:grpc_stream_mux_rs",
        "//services/lib/request_context:request_context_rs",
        "//services/memstore/client:client_rs",
        "//services/remote_agents/client:client_rs",
        "//services/request_insight/publisher:publisher_rs",
        "//services/share/client:client_rs",
        "//services/tenant_watcher/client:client_rs",
        "//third_party/tracing-actix-web",
    ],
)

rust_oci_image(
    name = "api_proxy_server-image",
    package_name = package_name(),
    binary = "api_proxy_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":api_proxy_server-image",
    ],
    lint_allow_multiple_apps = True,
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/common:lib",
        "//deploy/gcp:monitoring-lib",
        "//deploy/tenants:namespaces",
    ],
)

rust_test(
    name = "api_proxy_server_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":api_proxy_server",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/agents:agents_proto",
        "//services/api_proxy:model_finder_proto",
        "//services/api_proxy:public_api_proto",
        "//services/chat_host:chat_proto",
        "//services/completion_host:completion_proto",
        "//services/deploy/model_instance:model_instance_proto",
        "//services/edit_host:edit_proto",
        "//services/lib/grpc/stream_mux:stream_mux_proto",
        "//services/next_edit_host:next_edit_proto",
        "//services/share:share_proto",
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:descriptor_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":monitoring_kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

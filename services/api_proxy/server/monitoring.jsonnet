local lib = import 'deploy/common/lib.jsonnet';
local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

function(cloud)
  local endpoints = [
    'chat',
    'chat-stream',
    'edit',
    'find-missing',
    'memorize',
    'batch-upload',
    'resolve-completions',
    'resolve-edit',
    'record-preference-sample',
    'next-edit',
    'next-edit-stream',
    'instruction-stream',
    'smart-paste-stream',
    'agents/llm-generate',
    'agents/codebase-retrieval',
    'agents/edit-file',
    'agents/list-remote-tools',
    'agents/run-remote-tool',
    'github/list-repos',
    'github/list-branches',
    'github/get-repo',
    'github/is-user-configured',
    'remote-agents/create',
    'remote-agents/delete',
    'remote-agents/list',
    'remote-agents/chat',
    'remote-agents/chat-history',
    'remote-agents/agent-history-stream',
    'remote-agents/interrupt',
    'remote-agents/logs',
    'remote-agents/add-ssh-key',
    'remote-agents/report-status',
    'remote-agents/report-chat-history',
    'remote-agents/poll-update',
    'remote-agents/stream',
    'remote-agents/report-setup-logs',
  ];
  // Error codes to filter out of both numerator and denominator of
  // API response queries.
  // We don't include 200 here, as 200 is not an error and should
  // only be filtered out of the numerator.
  local ignored_error_code_regex_warning = {
    _: '4..',
  };
  local ignored_error_code_regex_error = {
    _: '401|402|403|408|413|429|499',
    memorize: '402|403',
    'batch-upload': '402|403|408',
    'find-missing': '402|403|408',
    'resolve-completions': '402|403',
    'resolve-edit': '402|403',
    'record-preference-sample': '402|403',
    'next-edit': '402|403|408',
    'next-edit-stream': '402|403|408',
  };
  local remote_agents_management_endpoints = [
    'remote-agents/create',
    'remote-agents/delete',
    'remote-agents/interrupt',
    'remote-agents/chat',
  ];

  local warning_thresholds = {
    'chat-stream': [
      { window: '5m', error_rate: 0.20, min_requests: 20 },
      { window: '1h', error_rate: 0.20, min_requests: 50 },
    ],
    _: [
      { window: '2m', error_rate: 0.05, min_requests: 50 },
      { window: '30m', error_rate: 0.05, min_requests: 200 },
    ],
  } + {
    [endpoint]: [{ window: '1h', error_rate: 0.60, min_requests: 2 }]
    for endpoint in remote_agents_management_endpoints
  };
  local error_thresholds = {
    'chat-stream': [
      { window: '5m', error_rate: 0.90, min_requests: 20 },
      { window: '1h', error_rate: 0.90, min_requests: 50 },
    ],
    _: [
      { window: '2m', error_rate: 0.90, min_requests: 50 },
      { window: '30m', error_rate: 0.90, min_requests: 200 },
    ],
  } + {
    [endpoint]: [{ window: '1h', error_rate: 0.90, min_requests: 5 }]
    for endpoint in remote_agents_management_endpoints
  };
  local team_owner = {
    _: 'default',
    completion: 'completion',
    'next-edit': 'next-edit',
    'next-edit-stream': 'next-edit',
    chat: 'chat',
    'chat-stream': 'chat',
    'agents/llm-generate': 'chat',
    'agents/codebase-retrieval': 'chat',
    'agents/edit-file': 'chat',
    'agents/list-remote-tools': 'chat',
    'agents/run-remote-tool': 'chat',
  } + {
    [endpoint]: 'remote-agents'
    for endpoint in endpoints
    // The github public APIs are all primarily used for the remote agent UI
    if std.startsWith(endpoint, 'remote-agents/') || std.startsWith(endpoint, 'github/')
  };
  local envs = {
    STAGING: tenantNamespaces.stagingNamespaces,
    PROD: tenantNamespaces.prodNamespaces,
  };

  // Checks if the error rate is above the given percentage for the given window
  // and there are at least the minimum requests in that window.
  // Threshold requires the fields `window`, `error_rate`, and `min_requests`.
  // Example: {window: '5m', error_rate: 0.10, min_requests: 100}
  local promQL(metric, endpoint, ignored_error_code_regex, threshold) =
    assert std.length(std.findSubstr('200', ignored_error_code_regex)) == 0;
    |||
      (
      sum by (tenant_name, namespace, cluster) (increase(%(metric)s{status_code!~"%(status_code)s", endpoint=~"/%(endpoint)s.*"}[%(window)s])) > %(min_requests)s and
      sum by (tenant_name, namespace, cluster) (increase(%(metric)s{status_code!~"200|%(status_code)s", endpoint=~"/%(endpoint)s.*"}[%(window)s])) /
          sum by (tenant_name, namespace, cluster) (increase(%(metric)s{status_code!~"%(status_code)s", endpoint=~"/%(endpoint)s.*"}[%(window)s])) > %(error_rate)s
      )
    ||| % {
      metric: metric,
      endpoint: endpoint,
      status_code: ignored_error_code_regex,
      window: threshold.window,
      error_rate: threshold.error_rate,
      min_requests: threshold.min_requests,
    };
  local locationString = 'tenant_name %s namespace %s cluster %s' % [monitoringLib.label('tenant_name'), monitoringLib.label('namespace'), monitoringLib.label('cluster')];
  local warningPolicy(endpoint) =
    local ignored_error_code_regex = if endpoint in ignored_error_code_regex_warning then ignored_error_code_regex_warning[endpoint] else ignored_error_code_regex_warning._;
    local thresholds = if endpoint in warning_thresholds then warning_thresholds[endpoint] else warning_thresholds._;
    local queries = std.map(
      function(threshold)
        promQL(
          'au_api_proxy_response_code',
          endpoint,
          ignored_error_code_regex,
          threshold
        ),
      thresholds,
    );
    monitoringLib.alertPolicy(
      cloud=cloud,
      condition={
        displayName: 'API Proxy %s error rate warning' % endpoint,
        conditionPrometheusQueryLanguage: {
          duration: '300s',
          evaluationInterval: '60s',
          labels: { severity: 'warning' },
          query: std.join(' or ', queries),
        },
      },
      name='api-proxy-%s-error-rate-warning' % std.strReplace(endpoint, '/', '-'),
      description="api-proxy's %s error rate over 5%% for more than 5 minutes in %s." % [endpoint, locationString],
      team=if endpoint in team_owner then team_owner[endpoint] else team_owner._,
    );

  local errorPolicy(endpoint) =
    local ignored_error_code_regex = if endpoint in ignored_error_code_regex_error then ignored_error_code_regex_error[endpoint] else ignored_error_code_regex_error._;
    local thresholds = if endpoint in error_thresholds then error_thresholds[endpoint] else error_thresholds._;
    local queries = std.map(
      function(threshold)
        promQL(
          'au_api_proxy_response_code',
          endpoint,
          ignored_error_code_regex,
          threshold
        ),
      thresholds,
    );
    monitoringLib.alertPolicy(
      cloud=cloud,
      condition={
        displayName: 'API Proxy %s error rate error' % endpoint,
        conditionPrometheusQueryLanguage: {
          duration: '300s',
          evaluationInterval: '60s',
          labels: { severity: 'error' },
          query: std.join(' or ', queries),
        },
      },
      name='api-proxy-%s-error-rate-error' % std.strReplace(endpoint, '/', '-'),
      description="api-proxy's %s error rate over 90%% for more than 5 minutes in %s." % [endpoint, locationString],
      team=if endpoint in team_owner then team_owner[endpoint] else team_owner._,
    );

  // The following alerts are based on global metrics. Currently, these are only used by completions.
  local makeNamespaceRegex(namespaces) =
    std.join('|', std.map(function(ns) ns.namespace, namespaces));

  // Single burn rate query
  // (https://sre.google/workbook/alerting-on-slos/#4-alert-on-burn-rate)
  // Checks if the error rate is above the given percentage for the given window
  // and there are at least the minimum requests in that window.
  // Threshold requires the fields `window`, `error_rate`, and `min_requests`.
  // Example: {window: '5m', error_rate: 0.10, min_requests: 100}
  local availabilityPromQL(
    metric, endpoint, ignored_error_code_regex, namespace_regex, threshold
        ) =
    assert std.length(std.findSubstr('200', ignored_error_code_regex)) == 0;
    // We put the error rate before the request check so that the alert shows the error rate.
    |||
      sum (
        increase(
          %(metric)s{
            status_code!~"200|%(status_code)s",
            endpoint=~"/%(endpoint)s.*",
            namespace=~"%(namespace)s"
          }[%(window)s]
        )
      )
      /
      sum (
        increase(
          %(metric)s{
            status_code!~"%(status_code)s",
            endpoint=~"/%(endpoint)s.*",
            namespace=~"%(namespace)s"
          }[%(window)s]
        )
      ) > %(error_rate)s
      and
      sum (
        increase(%(metric)s{
          status_code!~"%(status_code)s",
          endpoint=~"/%(endpoint)s.*",
          namespace=~"%(namespace)s"
        }[%(window)s])
      ) > %(min_requests)s
    ||| % {
      metric: metric,
      endpoint: endpoint,
      status_code: ignored_error_code_regex,
      namespace: namespace_regex,
      window: threshold.window,
      error_rate: threshold.error_rate,
      min_requests: threshold.min_requests,
    };

  // Multi burn rate query
  // (https://sre.google/workbook/alerting-on-slos/#5-multiple-burn-rate-alerts)
  local availabilityMultiPromQL(
    endpoint, ignored_error_code_regex, namespace_regex, thresholds
        ) =
    local queries = std.map(
      function(threshold)
        '(\n%s\n)' % [
          availabilityPromQL(
            'au_api_proxy_response_code',
            endpoint,
            ignored_error_code_regex,
            namespace_regex,
            threshold
          ),
        ],
      thresholds
    );
    std.join(' or ', queries);

  // Availability policy based on a multi burn rate query.
  local availabilityPolicy(severity, endpoint, error_type, ignored_error_code_regex, env, thresholds) =
    assert std.objectHas(envs, env);
    assert lib.contains(['info', 'warning', 'error', 'critical'], severity);
    local lowestErrorRateThreshold = std.foldl(
      function(acc, threshold)
        if threshold.error_rate <= acc.error_rate then threshold else acc,
      thresholds,
      thresholds[0]
    );
    monitoringLib.alertPolicy(
      cloud=cloud,
      condition={
        displayName: 'API Proxy %s %s error rate %s in %s' % [endpoint, error_type, severity, env],
        conditionPrometheusQueryLanguage: {
          duration: '300s',
          evaluationInterval: '60s',
          labels: { severity: severity },
          query: availabilityMultiPromQL(
            endpoint=endpoint,
            ignored_error_code_regex=ignored_error_code_regex,
            namespace_regex=makeNamespaceRegex(envs[env]),
            thresholds=thresholds,
          ),
        },
      },
      name='api-proxy-%s-%s-error-rate-%s-%s' % [endpoint, error_type, severity, std.asciiLower(env)],
      description='API Proxy %(endpoint)s %(error_type)s error rate in %(env)s exceeding %(error_rate).1f%% over %(window)s.\n\n' % {
        endpoint: endpoint,
        error_type: error_type,
        env: env,
        error_rate: lowestErrorRateThreshold.error_rate * 100,
        window: lowestErrorRateThreshold.window,
      } + |||
        For more information, see [Runbook: Completions](https://www.notion.so/Runbook-Completions-15bbba10175a802db7afefd8fe4d4709?pvs=4#1f2bba10175a80eaa497f6ee0f4dea59).

        Debugging steps:
        1. Find the specific `status_code` and `namespace`. This can be done by adding `(namespace, status_code)` to the query url linked in the alert. Or adding "namespace" to the groupby in the completions dashboard. [Use this link](https://console.cloud.google.com/monitoring/metrics-explorer;duration=PT12H?pageState=%7B%22xyChart%22:%7B%22constantLines%22:%5B%5D,%22dataSets%22:%5B%7B%22plotType%22:%22LINE%22,%22pointConnectionMethod%22:%22GAP_DETECTION%22,%22prometheusQuery%22:%22sum%20by%20(namespace,%20status_code)%20(%5Cn%20%20increase(%5Cn%20%20%20%20au_api_proxy_response_code%7B%5Cn%20%20%20%20%20%20status_code!~%5C%22200%7C401%7C402%7C403%7C408%7C413%7C429%7C499%5C%22,%5Cn%20%20%20%20%20%20endpoint%3D~%5C%22%2Fcompletion.*%5C%22%5Cn%20%20%20%20%7D%5B5m%5D%5Cn%20%20)%5Cn)%22,%22targetAxis%22:%22Y1%22,%22unitOverride%22:%22%22%7D%5D,%22options%22:%7B%22mode%22:%22COLOR%22%7D,%22y1Axis%22:%7B%22label%22:%22%22,%22scale%22:%22LINEAR%22%7D%7D%7D&project=system-services-prod).
        2. See if you can identify the component via [traces](https://console.cloud.google.com/traces/explorer;query=%7B%22plotType%22:%22HEATMAP%22,%22pointConnectionMethod%22:%22GAP_DETECTION%22,%22targetAxis%22:%22Y1%22,%22traceQuery%22:%7B%22resourceContainer%22:%22projects%2Fsystem-services-prod%2Flocations%2Fglobal%2FtraceScopes%2F_Default%22,%22spanDataValue%22:%22SPAN_DURATION%22,%22spanFilters%22:%7B%22attributes%22:%5B%7B%22key%22:%22%2Fhttp%2Fstatus_code%22,%22value%22:%5B%22504%22%5D%7D,%7B%22key%22:%22http.target%22,%22value%22:%5B%22%2Fcompletion%22%5D%7D%5D,%22displayNames%22:%5B%5D,%22isRootSpan%22:false,%22kinds%22:%5B%5D,%22maxDuration%22:%22%22,%22minDuration%22:%22%22,%22services%22:%5B%5D,%22status%22:%5B%5D%7D%7D%7D;duration=P2D?project=system-services-prod&pageState=(%22traceFilter%22:(%22chips%22:%22%255B%257B_22k_22_3A_22service.namespace_22_2C_22t_22_3A10_2C_22v_22_3A_22_5C_22thirdwave_5C_22_22_2C_22i_22_3A_22service.namespace_22%257D_2C%257B_22k_22_3A_22Method_22_2C_22t_22_3A10_2C_22v_22_3A_22_5C_22POST_5C_22_22_2C_22s_22_3Atrue_2C_22i_22_3A_22%252Fhttp%252Fmethod_22%257D_2C%257B_22k_22_3A_22g.co%252Fr%252Fk8s_pod%252Fpod_name_22_2C_22t_22_3A10_2C_22v_22_3A_22_5C_22completion-eldenv4-0c-15b-755c959849-5jnch_5C_22_22_2C_22i_22_3A_22g.co%252Fr%252Fk8s_pod%252Fpod_name_22%257D_2C%257B_22k_22_3A_22augment.client_session_id_22_2C_22t_22_3A10_2C_22v_22_3A_22_5C_22fefcca54-f1c1-43c8-a70b-bff6879a3c19_5C_22_22%257D%255D%22))&tid=49a976598e552f25148287055474a471&start=1731024571712&end=1731024719988&spanId=9cabc52cf2461a89&inv=1&invt=Abw8rA) or [logging](https://console.cloud.google.com/logs/query;duration=P2D?project=system-services-prod&inv=1&invt=AbxPKw).
      |||,
      team=if endpoint in team_owner then team_owner[endpoint] else team_owner._,
    );

  // Single burn rate query for latency
  // (https://sre.google/workbook/alerting-on-slos/#4-alert-on-burn-rate)
  // Checks if successful requests exceeding the given latency exceeds the given percentage
  // for the given window and there are at least the minimum requests in that window.
  // Threshold requires the fields `window`, `error_rate`, and `min_requests`.
  // Example: {window: '5m', error_rate: 0.10, min_requests: 100}
  // NOTE: implementation-wise, we actually calculate the latency at the percentile and
  // compare to the threshold, e.g. if p90 latency > 0.5s, as this is equivalent and works
  // with the histogram metrics we export.
  local latencyPromQL(metric, endpoint, latency_s, namespace_regex, threshold) =
    |||
      histogram_quantile(
        %(percentile)s,
        sum by (le) (
          rate(
            %(metric)s{
              endpoint=~"/%(endpoint)s.*",
              status_code="200",
              request_source="client",
              namespace=~"%(namespace)s"
            }[%(window)s]
          )
        )
      ) > %(latency_s)s
      and
      sum (
        increase(
          %(metric)s{
            endpoint=~"/%(endpoint)s.*",
            status_code="200",
            request_source="client",
            le="+Inf",
            namespace=~"%(namespace)s"
          }[%(window)s]
        )
      ) >= %(min_requests)s
    ||| % {
      metric: metric,
      endpoint: endpoint,
      percentile: 1.0 - threshold.error_rate,
      latency_s: latency_s,
      window: threshold.window,
      min_requests: threshold.min_requests,
      namespace: namespace_regex,
    };

  // Multi burn rate query for latency
  // (https://sre.google/workbook/alerting-on-slos/#5-multiple-burn-rate-alerts)
  local latencyMultiPromQL(endpoint, latency_s, namespace_regex, thresholds) =
    local queries = std.map(
      function(threshold)
        '(\n%s\n)' % [
          latencyPromQL(
            'au_api_proxy_handler_latency_bucket',
            endpoint,
            latency_s,
            namespace_regex,
            threshold
          ),
        ],
      thresholds
    );
    std.join(' or ', queries);

  // Latency policy based on a multi burn rate query.
  // NOTE: This uses the au_api_proxy_handler_latency metric, which does not
  // include middleware or the time it takes the client to upload the payload.
  // It's good to exclude internet lag, less good to exclude middleware, but
  // this reduces the number of false positives we get more than the number of
  // false negatives, at least from what I've seen.
  local latencyPolicy(severity, endpoint, latency_s, env, thresholds) =
    assert std.objectHas(envs, env);
    assert lib.contains(['info', 'warning', 'error', 'critical'], severity);
    monitoringLib.alertPolicy(
      cloud=cloud,
      condition={
        displayName: 'API Proxy %s latency %s in %s' % [endpoint, severity, env],
        conditionPrometheusQueryLanguage: {
          duration: '300s',
          evaluationInterval: '60s',
          labels: { severity: severity },
          query: latencyMultiPromQL(
            endpoint=endpoint,
            latency_s=latency_s,
            namespace_regex=makeNamespaceRegex(envs[env]),
            thresholds=thresholds,
          ),
        },
      },
      name='api-proxy-%s-latency-%s-%s' % [endpoint, severity, std.asciiLower(env)],
      description="api-proxy's %s high latency in %s." % [endpoint, env],
      team=if endpoint in team_owner then team_owner[endpoint] else team_owner._,
    );

  // Alert on 500s observed by the ingress, to capture situations where api-proxy is down and
  // therefore not reporting errors itself. This has a high threshold because we generally want to
  // leave error reporting to the endpoint-level metrics.
  local ingressErrorPolicy = monitoringLib.alertPolicy(
    cloud=cloud,
    condition={
      displayName: 'API Proxy ingress error rate warning',
      conditionPrometheusQueryLanguage: {
        duration: '300s',
        evaluationInterval: '60s',
        labels: { severity: 'warning' },
        query: |||
          sum by (target_proxy_name)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*api-proxy.*"}[5m])) > 50 and
          sum by (target_proxy_name)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*api-proxy.*",response_code_class="500"}[5m])) /
            sum by (target_proxy_name)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*api-proxy.*"}[5m])) > 0.5
        |||,
      },
    },
    name='api-proxy-ingress-error-rate-warning',
    description="api-proxy's ingress error rate over 50%% for more than 5 minutes for ingress %s." % monitoringLib.label('target_proxy_name'),
  );

  // Low quality policy based on a multi burn rate query.
  local lowQualityPromQL(namespace_regex, threshold) =
    // We put the filter rate before the request check so that the alert shows the filter rate.
    |||
      (
        sum (
          increase(
            au_completion_host_post_processing_counter_total{
              namespace=~"%(namespace)s",
              request_source="client",
              rule_name="filter_low_quality_completion"
            }[%(window)s]
          )
        )
        /
        sum (
          increase(
            au_completion_host_post_processing_counter_total{
              namespace=~"%(namespace)s",
              request_source="client",
              rule_name="post_process_calls"
            }[%(window)s]
          )
        )
      ) > %(filter_rate)s
      and
      sum (
        increase(
          au_completion_host_post_processing_counter_total{
            namespace=~"%(namespace)s",
            request_source="client",
            rule_name="post_process_calls"
          }[%(window)s]
        )
      ) >= %(min_requests)s
    ||| % {
      namespace: namespace_regex,
      window: threshold.window,
      filter_rate: threshold.filter_rate,
      min_requests: threshold.min_requests,
    };


  local lowQualityMultiPromQL(namespace_regex, thresholds) =
    local queries = std.map(
      function(threshold)
        '(\n%s\n)' % [
          lowQualityPromQL(
            namespace_regex,
            threshold
          ),
        ],
      thresholds
    );
    std.join(' or ', queries);

  // Low quality policy based on a multi burn rate query.
  local lowQualityPolicy(severity, env, thresholds) =
    assert std.objectHas(envs, env);
    assert lib.contains(['info', 'warning', 'error', 'critical'], severity);
    monitoringLib.alertPolicy(
      cloud=cloud,
      condition={
        displayName: 'Low quality completion filter rate %s in %s' % [severity, env],
        conditionPrometheusQueryLanguage: {
          duration: '300s',
          evaluationInterval: '60s',
          labels: { severity: severity },
          query: lowQualityMultiPromQL(
            namespace_regex=makeNamespaceRegex(envs[env]),
            thresholds=thresholds,
          ),
        },
      },
      name='completion-low-quality-filter-rate-%s-%s' % [severity, std.asciiLower(env)],
      description='completion low quality high filter rate in %s.' % [env],
      team=team_owner.completion,
    );

  // Ignored error codes for server errors (500s).
  local completion_server_regex = '4..';

  // Ignored error codes for client errors (400s). In particular, allows:
  // 400, 404, 413, and 429.
  local completion_client_regex = '5..|401|402|403|408|499';

  // Negative alert: Alert when no users have been blocked for subscription status for more than two hours
  // This helps detect if subscription blocking is working properly (prod only)
  local noSubscriptionBlockingPolicy = monitoringLib.alertPolicy(
    cloud=cloud,
    condition={
      displayName: 'No subscription blocking for over 2 hours',
      conditionPrometheusQueryLanguage: {
        duration: '300s',
        evaluationInterval: '60s',
        labels: { severity: 'error' },
        query: |||
          # Alert if no subscription blocks or trial ended messages have occurred in the last two hours in prod
          # Sum both metrics to catch any subscription-related blocking activity across all prod shard namespaces
          (
            sum(increase(au_subscription_block_total{namespace=~"%(namespace)s"}[2h])) +
            sum(increase(au_trial_ended_message_total{namespace=~"%(namespace)s"}[2h]))
          ) == 0
        ||| % { namespace: makeNamespaceRegex(envs.PROD) },
      },
    },
    name='api-proxy-no-subscription-blocking-alert',
    description='No users have been blocked for subscription status or shown trial ended messages in the last two hours. This may indicate subscription blocking is not working properly.',
    team='insights'
  );

  lib.flatten([
    std.map(warningPolicy, endpoints),
    std.map(errorPolicy, endpoints),
    // Server error (5xx) availability policies for completion.
    // Current SLO is 99.9% availability.
    // TODO(jeff): consider lowering STAGING error threshold if below issue is fixed.
    // STAGING can have spikes in unavailability due to PROD preempting our only embedders.
    availabilityPolicy(
      severity='error', endpoint='completion', error_type='server', ignored_error_code_regex=completion_server_regex, env='STAGING', thresholds=[
        { window: '1h', error_rate: 0.05, min_requests: 1000 },
      ]
    ),
    availabilityPolicy(
      severity='error', endpoint='completion', error_type='server', ignored_error_code_regex=completion_server_regex, env='PROD', thresholds=[
        { window: '5m', error_rate: 0.05, min_requests: 200 },
        { window: '1h', error_rate: 0.01, min_requests: 1000 },
      ]
    ),
    // TODO(jeff): These are temporarily raised to 0.3% from 0.1% in order to allow us time
    // to address known issues, e.g. cancellation.
    availabilityPolicy(
      severity='warning', endpoint='completion', error_type='server', ignored_error_code_regex=completion_server_regex, env='STAGING', thresholds=[
        { window: '1h', error_rate: 0.015, min_requests: 2000 },
        { window: '1d', error_rate: 0.003, min_requests: 10000 },
      ]
    ),
    availabilityPolicy(
      severity='warning', endpoint='completion', error_type='server', ignored_error_code_regex=completion_server_regex, env='PROD', thresholds=[
        { window: '1h', error_rate: 0.015, min_requests: 2000 },
        { window: '1d', error_rate: 0.003, min_requests: 10000 },
      ]
    ),
    // Client error (4xx) availability policies for completion
    // No SLOs are defined, since we cannot control the inputs of (e.g. fraudulent) clients.
    // Therefore, we only have warning policies with relaxed thresholds.
    // TODO(jeff): Consider defining an SLO, e.g. if we exceed 5% client error rate, then
    // that means either too many legitimate errors, or >5% fraudulent client requests.
    // See https://github.com/augmentcode/augment/pull/26051#discussion_r2096566058.
    availabilityPolicy(
      severity='warning', endpoint='completion', error_type='client', ignored_error_code_regex=completion_client_regex, env='STAGING', thresholds=[
        { window: '1h', error_rate: 0.005, min_requests: 2000 },
        { window: '1d', error_rate: 0.001, min_requests: 10000 },
      ]
    ),
    // TODO(jeff): This is temporarily raised to 0.5% from 0.2% in order to allow time for
    // new clients vscode@0.461.0 to roll out, which includes
    // (https://github.com/augmentcode/augment/commit/31dab6f9a8b9cd20b05c5f14709083ee91daf48a)
    availabilityPolicy(
      severity='warning', endpoint='completion', error_type='client', ignored_error_code_regex=completion_client_regex, env='PROD', thresholds=[
        { window: '1h', error_rate: 0.025, min_requests: 2000 },
        { window: '1d', error_rate: 0.005, min_requests: 10000 },
      ]
    ),

    // Latency policies for completion
    // Current SLO is 90% of requests below 500ms.
    latencyPolicy('error', 'completion', 0.5, 'STAGING', thresholds=[
      { window: '5m', error_rate: 0.30, min_requests: 200 },
      { window: '1h', error_rate: 0.20, min_requests: 1000 },
    ]),
    latencyPolicy('error', 'completion', 0.5, 'PROD', thresholds=[
      { window: '5m', error_rate: 0.30, min_requests: 200 },
      { window: '1h', error_rate: 0.20, min_requests: 1000 },
    ]),
    latencyPolicy('warning', 'completion', 0.5, 'STAGING', thresholds=[
      { window: '1h', error_rate: 0.15, min_requests: 2000 },
      { window: '1d', error_rate: 0.10, min_requests: 10000 },
    ]),
    latencyPolicy('warning', 'completion', 0.5, 'PROD', thresholds=[
      { window: '1h', error_rate: 0.15, min_requests: 2000 },
      { window: '1d', error_rate: 0.10, min_requests: 10000 },
    ]),

    // Availability policies for chat
    availabilityPolicy(
      severity='error', endpoint='chat-stream', error_type='total', ignored_error_code_regex=ignored_error_code_regex_error._, env='STAGING', thresholds=[
        { window: '5m', error_rate: 0.10, min_requests: 50 },
        { window: '1h', error_rate: 0.05, min_requests: 250 },
      ]
    ),
    availabilityPolicy(
      severity='error', endpoint='chat-stream', error_type='total', ignored_error_code_regex=ignored_error_code_regex_error._, env='PROD', thresholds=[
        { window: '5m', error_rate: 0.10, min_requests: 50 },
        { window: '1h', error_rate: 0.05, min_requests: 250 },
      ]
    ),
    availabilityPolicy(
      severity='warning', endpoint='chat-stream', error_type='total', ignored_error_code_regex=ignored_error_code_regex_warning._, env='STAGING', thresholds=[
        { window: '1h', error_rate: 0.01, min_requests: 500 },
        { window: '1d', error_rate: 0.005, min_requests: 2500 },
      ]
    ),
    availabilityPolicy(
      severity='warning', endpoint='chat-stream', error_type='total', ignored_error_code_regex=ignored_error_code_regex_warning._, env='PROD', thresholds=[
        { window: '1h', error_rate: 0.01, min_requests: 500 },
        { window: '1d', error_rate: 0.005, min_requests: 2500 },
      ]
    ),

    // Ingress error policy
    ingressErrorPolicy,

    // Low quality policies for completion
    lowQualityPolicy('warning', 'STAGING', thresholds=[
      { window: '1h', filter_rate: 0.30, min_requests: 2000 },
      { window: '1d', filter_rate: 0.12, min_requests: 20000 },
    ]),
    lowQualityPolicy('warning', 'PROD', thresholds=[
      { window: '1h', filter_rate: 0.30, min_requests: 2000 },
      { window: '1d', filter_rate: 0.12, min_requests: 20000 },
    ]),

    // Subscription blocking negative alert
    noSubscriptionBlockingPolicy,
  ])

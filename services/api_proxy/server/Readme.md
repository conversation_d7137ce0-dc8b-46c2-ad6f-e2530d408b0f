# api proxy server

The api proxy takes requests (e.g. from the vscode client), performs authentication and forwards the requests to internal services, e.g. the modelhosts.

The api proxy server is implemented in Rust and assumes a valid rustc/cargo installation.

## Metrics

- au_api_proxy_response_code: A counter for each HTTP request
- au_api_proxy_request_latency: A histogram for the latency of each HTTP request


## Build

```
cargo build
```

or
```
bazel build //services/api_proxy/server/...
```

## Test

```
cargo test
```

or

```
bazel test //services/api_proxy/server/...
```

## Run Locally

```
cargo run
```

## Usage in vscode

To use the [https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer](rust analyzer plugin), please add the following to your `.settings.json` file:

```
    "rust-analyzer.linkedProjects": ["./services/api_proxy/server/Cargo.toml"]
```

## Sealed secrets

To regenerate the sealed secret, first down the secret into a json file or create a secret
using kubectl.

Download and install kubeseal from the releases page associated with kubeseal.

Then run:

```
kubeseal --context <context for cluster> -f <secret in json file> --scope cluster-wide > <output-file>
```

Open the output file and delete the namespace lines (there are at least two).

Make the sealed secret a jsonnet file that takes a namespace parameter.

Other edits to the jsonnet file:
  - metadata.namespace should come from namespace
  - spec.template.metadata.name should be "launch-darkly-sdk-key"
  - spec.encryptedData.sdk_key should be the base64 encoded sealed secret

Modify deploy.jsonnet to deploy the sealedsecret into the namespaces.

syntax = "proto3";

package settings;

import "google/protobuf/timestamp.proto";
import "services/integrations/glean/glean.proto";
import "services/integrations/supabase/supabase.proto";

// A service to store and retrieve tenants (and later user) settings.
//
// A setting here is defined as small state and a per-tenant and per-user settings.
// Unless otherwise noted settings are usually restricted
//
// todo: in the future, we will add field masks to retrieve or update the settings
// see https://netflixtechblog.com/practical-api-design-at-netflix-part-1-using-protobuf-fieldmask-35cfdc606518
service Settings {
  // returns the settings of the tenant
  //
  // if no settings are found, an empty settings is returned
  rpc GetTenantSettings(GetTenantSettingsRequest) returns (GetTenantSettingsResponse);

  //  update the settings of the tenant
  //
  // if the version is set, the update is not applied if the current version doesn't match matches the version given.
  rpc UpdateTenantSettings(UpdateTenantSettingsRequest) returns (UpdateTenantSettingsResponse);

  // returns the settings of the user
  //
  // if no settings are found, an empty settings is returned
  rpc GetUserSettings(GetUserSettingsRequest) returns (GetUserSettingsResponse);

  //  update the settings of the user
  //
  // if the version is set, the update is not applied if the current version doesn't match matches the version given.
  rpc UpdateUserSettings(UpdateUserSettingsRequest) returns (UpdateUserSettingsResponse);
}

message RepoInformation {
  // the owner of the repo on github
  string repo_owner = 1 [debug_redact = true];

  // the name of the repo on github
  string repo_name = 2 [debug_redact = true];
}

message GithubSettings {
  // the github installation id for the given tenant
  // the installation is used to create github api token to operate as bot on the tenant's behalf
  int64 installation_id = 1;

  // the list of repos that we index for the tenant
  repeated RepoInformation repos = 2;
}

message RepoMapping {
  // mappping from repo name to repo information
  repeated RepoInformation repos = 1;
}

message ChannelMappings {
  // mappping from channel id to the repos to use
  map<string, RepoMapping> channel_mappings = 1;
}

// settings for slack
message SlackSettings {
  // the enterprise id of the tenant in Slack
  // only set if the tenant is part of an Slack Enterprise Grid (https://api.slack.com/enterprise)
  string enterprise_id = 1;

  // the team id of the tenant in Slack
  string team_id = 2;

  // the oauth token for the tenant in Slack
  string oauth_token = 3 [debug_redact = true];

  // the user id of the bot in the tenant's Slack workspace
  string bot_user_id = 4;

  // the mapping from channel id to repo information
  ChannelMappings channel_mappings = 5;

  // whether the slack installation is using the new assistant flow
  bool assistant_flow_enabled = 6;
}

message GleanJWTKeyPairInfo {
  // JWT algorithm identifier (e.g., 'RS256', 'ES256').
  string algorithm = 1;

  // A string identifying this private key (and
  // thus what public key it corresponds to), e.g.: 'augment-kid-0'.
  string key_id = 2;

  // PEM encoded private key.
  string private_key = 3 [debug_redact = true];

  // PEM encoded public key.
  string public_key = 4;
}

// settings for Glean
message GleanTenantSettings {
  // the client id for the tenant OAuth in Glean
  string oauth_client_id = 1 [debug_redact = true];

  // the client secret for the tenant OAuth in Glean
  string oauth_client_secret = 2 [debug_redact = true];

  // the subdomain for the tenant OAuth in Glean
  string oauth_subdomain = 3;

  // the provider for the tenant OAuth in Glean
  glean.Provider oauth_provider = 4;

  // The base URL for the customer's Glean API.
  // ex. https://augment-be.glean.com
  string glean_domain = 5;

  // The JWT key pair info for JWT authentication to Glean.
  GleanJWTKeyPairInfo glean_jwt_key_pair_info = 6;
}

message NotionSettings {}

// per tenant settings
//
// on update, only the field that are set to non-empty values are updated
message TenantSettings {
  // settings for github
  GithubSettings github_settings = 1;

  // settings for slack
  SlackSettings slack_settings = 2;

  // settings for Glean
  GleanTenantSettings glean_tenant_settings = 3;

  // settings for Notion -- currently we only use Notion User settings but we may add tenant level settings
  NotionSettings notion_settings = 4;
}

// get the settings for the tenant
message GetTenantSettingsRequest {}

message GetTenantSettingsResponse {
  // the settings for the tenant
  TenantSettings settings = 1;

  // the current version of the settings.
  //
  // the version can be given to UpdateTenantSettings to ensure that the update is not applied concurrently.
  string version = 2;
}

message UpdateTenantSettingsRequest {
  // the settings to update - currently an all or nothing update until field masks are added
  TenantSettings settings = 1;

  // if set the update is not applied if the current version matches the version given.
  // this is to ensure that the update is not applied concurrently.
  //
  // We allow the version to be empty, in which case the update is always applied. This is
  // mostly for backward compatibility. It is highly recommended to set the version.
  //
  // the RPC will fail with ABORTED (see https://grpc.github.io/grpc/core/md_doc_statuscodes.html)
  string expected_version = 2;
}

message UpdateTenantSettingsResponse {
  // the version of the settings after the update.
  string version = 1;
}

// per user settings
//
// on update, only the field that are set to non-empty values are updated
message UserSettings {
  // settings for Glean
  GleanUserSettings glean_user_settings = 1;

  // Settings for Linear
  LinearUserSettings linear_user_settings = 5;

  //settings for Notion
  NotionUserSettings notion_user_settings = 2;

  // settings for Atlassian
  AtlassianUserSettings atlassian_user_settings = 3;

  // settings for Github user authorizations
  GithubUserSettings github_user_settings = 4;

  // settings for Supabase
  SupabaseUserSettings supabase_user_settings = 6;
}

// get the settings for the user
message GetUserSettingsRequest {
  // the user id of the user (if not set, the user id is extracted from the token)
  string user_id = 1;
}

message GetUserSettingsResponse {
  // the settings for the user
  UserSettings settings = 1;

  // the version of the settings.
  string version = 2;
}

message GithubUserSettings {
  // the user access token based on OAuth to access Github APIs
  string access_token = 1 [debug_redact = true];

  google.protobuf.Timestamp access_token_expiration = 2;

  // the user refresh token based on OAuth to refresh the user token
  string refresh_token = 3 [debug_redact = true];

  google.protobuf.Timestamp refresh_token_expiration = 4;

  // the token type of the user token
  string token_type = 5;

  // GitHub user information
  string github_login = 6 [debug_redact = true];
  string github_name = 7 [debug_redact = true];
  string github_email = 8 [debug_redact = true];
}

message GleanUserSettings {
  // the user access token based on OAuth to access Glean APIs
  string access_token = 1 [debug_redact = true];

  // the user refresh token based on OAuth to refresh the user token
  string refresh_token = 2 [debug_redact = true];

  // the token type of the user token
  string token_type = 3;
}

message NotionUserSettings {
  // the user access token based on OAuth to access Notion APIs
  string access_token = 1 [debug_redact = true];

  // the Notion bot id for the access token
  string bot_id = 2 [debug_redact = true];

  // the Notion workspace name for the access token
  string workspace_name = 3;

  // the Notion workspace id for the access token
  string workspace_id = 4;
}

message AtlassianOAuthSettings {
  // the user access token based on OAuth to access Atlassian APIs
  string access_token = 1 [debug_redact = true];

  // the user refresh token based on OAuth to refresh the user token
  string refresh_token = 2 [debug_redact = true];

  // the cloud id of the Atlassian instance
  string cloud_id = 3;
}

message AtlassianUserSettings {
  // the OAuth settings for connecting to Jira
  AtlassianOAuthSettings jira_settings = 1;

  // the OAuth settings for connecting to Confluence
  AtlassianOAuthSettings confluence_settings = 2;

  // the atlassian account id of the user
  string atlassian_account_id = 3;
}

message LinearUserSettings {
  // Access token for accessing Linear API.
  string access_token = 1 [debug_redact = true];

  // The scopes that this token has access to
  string scope = 2;
}

message SupabaseUserSettings {
  // Access token for accessing Supabase API
  string access_token = 1 [debug_redact = true];

  // Refresh token for refreshing the access token
  string refresh_token = 2 [debug_redact = true];

  // The expiration time of the access token
  google.protobuf.Timestamp access_token_expiration = 5;

  // List of Supabase projects the user has access to
  repeated supabase.SupabaseProject projects = 7;
}

message UpdateUserSettingsRequest {
  // the settings to update - currently an all or nothing update until field masks are added
  UserSettings settings = 1;

  // When set to a non-empty value, the update is ONLY applied if the version in
  // bigtable matches `expected_version`. This is to ensure that the update is
  // applied serially and atomically with respect to other updates.
  //
  // We allow the version to be empty, in which case the update is always
  // applied. This is mostly for backward compatibility. It is highly
  // recommended to set the version.
  //
  // the RPC will fail with ABORTED (see https://grpc.github.io/grpc/core/md_doc_statuscodes.html)
  string expected_version = 2;

  // the user id of the user (if not set, the user id is extracted from the token)
  string user_id = 3;
}

message UpdateUserSettingsResponse {
  // the version of the settings after the update.
  string version = 1;
}

package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/reflection"

	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
)

var configFile = flag.String("config", "", "Path to config file")

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	Namespace string `json:"namespace"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	BigtableProxyEndpoint string `json:"bigtable_proxy_endpoint"`

	UserTier string `json:"user_tier"`
}

func run(config *Config, grpcServer *grpc.Server) error {
	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		return err
	}
	proxyClient, err := bigtableproxy.NewBigtableProxyClient(config.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		return err
	}

	// Parse the UserTier string from config to the enum value
	userTierValue, exists := publicapiproto.GetModelsResponse_UserTier_value[config.UserTier]
	if !exists {
		log.Fatal().Msgf("Invalid user tier: %s", config.UserTier)
	}
	userTier := publicapiproto.GetModelsResponse_UserTier(userTierValue)

	server := NewSettingsServer(proxyClient, userTier)

	settingsproto.RegisterSettingsServer(grpcServer, server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}

	go func() {
		// Wait for either a shutdown signal or an OS signal
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		grpcServer.GracefulStop()
	}()

	log.Info().Msgf("Listening on %v", lis.Addr())
	err = grpcServer.Serve(lis)
	if err != nil && err != grpc.ErrServerStopped {
		log.Fatal().Err(err).Msg("Error serving")
	}
	log.Info().Msg("gRPC server closed")
	return nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating central client credentials")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()
	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	err = run(&config, grpcServer)
	if err != nil {
		log.Fatal().Err(err).Msg("Error serving")
	}
}

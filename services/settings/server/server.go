package main

import (
	"context"
	"fmt"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	"github.com/google/uuid"

	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

const (
	familyName    = "Settings"
	versionColumn = "version"

	// Tenant settings
	githubSettingsColumn      = "github_settings"
	gleanTenantSettingsColumn = "glean_tenant_settings"
	linearSettingsColumn      = "linear_settings"
	notionSettingsColumn      = "notion_settings"
	slackSettingsColumn       = "slack_settings"

	// User settings
	atlassianUserSettingsColumn = "atlassian_user_settings"
	githubUserSettingsColumn    = "github_user_settings"
	gleanUserSettingsColumn     = "glean_user_settings"
	linearUserSettingsColumn    = "linear_user_settings"
	notionUserSettingsColumn    = "notion_user_settings"
	supabaseUserSettingsColumn  = "supabase_user_settings"
)

type SettingsServer struct {
	ProxyClient bigtableproxy.BigtableProxyClient
	UserTier    publicapiproto.GetModelsResponse_UserTier
}

func NewSettingsServer(proxyClient bigtableproxy.BigtableProxyClient, userTier publicapiproto.GetModelsResponse_UserTier) *SettingsServer {
	return &SettingsServer{
		ProxyClient: proxyClient,
		UserTier:    userTier,
	}
}

func (s *SettingsServer) tenantCellToSettings(cell *bigtableproxy.RowCell, settings *settingsproto.TenantSettings) error {
	if string(cell.Qualifier) == githubSettingsColumn {
		settings.GithubSettings = &settingsproto.GithubSettings{}
		err := proto.Unmarshal(cell.Value, settings.GithubSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == slackSettingsColumn {
		settings.SlackSettings = &settingsproto.SlackSettings{}
		err := proto.Unmarshal(cell.Value, settings.SlackSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == gleanTenantSettingsColumn {
		settings.GleanTenantSettings = &settingsproto.GleanTenantSettings{}
		err := proto.Unmarshal(cell.Value, settings.GleanTenantSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == notionSettingsColumn {
		settings.NotionSettings = &settingsproto.NotionSettings{}
		err := proto.Unmarshal(cell.Value, settings.NotionSettings)
		if err != nil {
			return err
		}
		return nil
	}
	log.Error().Msgf("Unknown column: %s", cell.Qualifier)
	return status.Error(codes.Internal, "Unknown column")
}

func (s *SettingsServer) tenantRequestToMutations(req *settingsproto.UpdateTenantSettingsRequest) ([]*bigtableproto.Mutation, error) {
	mutations := []*bigtableproto.Mutation{}
	if req.Settings.GithubSettings != nil {
		githubSettingsBytes, err := proto.Marshal(req.Settings.GithubSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(githubSettingsColumn),
					Value:           githubSettingsBytes,
				},
			},
		})
	}
	if req.Settings.SlackSettings != nil {
		slackSettingsBytes, err := proto.Marshal(req.Settings.SlackSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(slackSettingsColumn),
					Value:           slackSettingsBytes,
				},
			},
		})
	}
	if req.Settings.GleanTenantSettings != nil {
		gleanTenantSettingsBytes, err := proto.Marshal(req.Settings.GleanTenantSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(gleanTenantSettingsColumn),
					Value:           gleanTenantSettingsBytes,
				},
			},
		})
	}
	if req.Settings.NotionSettings != nil {
		notionSettingsBytes, err := proto.Marshal(req.Settings.NotionSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(notionSettingsColumn),
					Value:           notionSettingsBytes,
				},
			},
		})
	}
	return mutations, nil
}

func (s *SettingsServer) GetTenantSettings(ctx context.Context, req *settingsproto.GetTenantSettingsRequest) (*settingsproto.GetTenantSettingsResponse, error) {
	if s.UserTier != publicapiproto.GetModelsResponse_ENTERPRISE_TIER {
		log.Ctx(ctx).Error().Msgf("tenant settings are not supported in this tier: %s", s.UserTier)
		return nil, fmt.Errorf("tenant settings are not supported in this tier: %s", s.UserTier)
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetTenantSettings: tenant_id=%s", authInfo.TenantID)

	rows, err := s.ProxyClient.ReadRows(
		ctx,
		authInfo.TenantID,
		proxyproto.TableName_SETTINGS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{[]byte("settings")},
		},
		&bigtableproto.RowFilter{},
		1,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Msgf("GetTenantSettings: failed to read rows: %v", err)
		return nil, err
	}
	if len(rows) > 1 {
		return nil, fmt.Errorf("expected at most one row, got %d", len(rows))
	}

	settings := &settingsproto.TenantSettings{}
	version := ""
	// if there is no row, return an empty settings
	if len(rows) == 1 {
		for _, cell := range rows[0].Cells {
			if cell.FamilyName != familyName {
				continue
			}
			if string(cell.Qualifier) == versionColumn {
				version = string(cell.Value)
			} else {
				if err := s.tenantCellToSettings(cell, settings); err != nil {
					return nil, err
				}
			}
		}
	}
	return &settingsproto.GetTenantSettingsResponse{Settings: settings, Version: version}, nil
}

func (s *SettingsServer) UpdateTenantSettings(ctx context.Context, req *settingsproto.UpdateTenantSettingsRequest) (*settingsproto.UpdateTenantSettingsResponse, error) {
	if s.UserTier != publicapiproto.GetModelsResponse_ENTERPRISE_TIER {
		log.Ctx(ctx).Error().Msgf("tenant settings are not supported in this tier: %s", s.UserTier)
		return nil, fmt.Errorf("tenant settings are not supported in this tier: %s", s.UserTier)
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	ctx = authInfo.AnnotateLogContext(ctx)
	if req.Settings == nil {
		return nil, status.Error(codes.InvalidArgument, "settings is required")
	}
	log.Ctx(ctx).Info().Msgf("UpdateTenantSettings: tenant_id=%s", authInfo.TenantID)

	mutations, err := s.tenantRequestToMutations(req)
	if err != nil {
		return nil, err
	}

	// Every time we update the settings, we update the version to a new UUID.
	// We use this to implement optimistic concurrency control: clients pass the
	// version they read the settings with, and if it doesn't match the current
	// version when we try to update, the update is rejected.
	version := uuid.New().String()
	mutations = append(mutations,
		&bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(versionColumn),
					Value:           []byte(version),
				},
			},
		},
	)

	var filter *bigtableproto.RowFilter
	if req.ExpectedVersion != "" {
		filter = bigtableproxy.ChainFilters(
			bigtableproxy.FamilyFilter(familyName),
			bigtableproxy.ColumnFilter(versionColumn),
			bigtableproxy.ValueFilter(req.ExpectedVersion),
		).Proto()
	}

	if filter == nil {
		entries := []*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey:    []byte("settings"),
				Mutations: mutations,
			},
		}
		mutateRowsResponse, err := s.ProxyClient.MutateRows(
			ctx,
			authInfo.TenantID,
			proxyproto.TableName_SETTINGS,
			entries,
			requestContext,
		)
		if err != nil {
			return nil, err
		}
		for _, mutateRowsResponse := range mutateRowsResponse {
			for _, entry := range mutateRowsResponse.Entries {
				if entry.Status.Code != int32(codes.OK) {
					return nil, status.Error(codes.Internal, "Failed to write row")
				}
			}
		}
	} else {
		checkAndMutateRowResponse, err := s.ProxyClient.CheckAndMutateRow(
			ctx,
			authInfo.TenantID,
			proxyproto.TableName_SETTINGS,
			[]byte("settings"),
			filter,
			mutations,
			nil,
			requestContext,
		)
		if err != nil {
			return nil, err
		}
		if !checkAndMutateRowResponse.PredicateMatched {
			return nil, status.Error(codes.Aborted, "Version mismatch")
		}
	}
	return &settingsproto.UpdateTenantSettingsResponse{
		Version: version,
	}, nil
}

func (s *SettingsServer) getUserRowKey(ctx context.Context, authInfo *auth.AugmentClaims, reqUserId string) (string, error) {
	rowKey := ""
	if opaqueUserID := authInfo.GetOpaqueUserID(); opaqueUserID != nil {
		// TODO: Currently the type is not currently in the row key - should it
		// be? In practice this is fine so far because none of the ID types have
		// overlapping values, but maybe eventually we'll want to do something
		// like augment:<id>, slack:<id>, token:<id>, etc.
		if reqUserId != "" && opaqueUserID.GetUserId() != reqUserId {
			log.Ctx(ctx).Warn().Msgf("Auth claims user ID %s does not match requested user ID %s", opaqueUserID.GetUserId(), reqUserId)
			return "", status.Error(codes.PermissionDenied, "Access denied")
		}
		rowKey = fmt.Sprintf("user#%s#settings", opaqueUserID.GetUserId())
	} else {
		if reqUserId == "" {
			return "", status.Error(codes.InvalidArgument, "user_id is required")
		}
		rowKey = fmt.Sprintf("user#%s#settings", reqUserId)
	}
	log.Ctx(ctx).Info().Msgf("getUserRowKey: row_key=%s", rowKey)
	return rowKey, nil
}

func (s *SettingsServer) GetUserSettings(ctx context.Context, req *settingsproto.GetUserSettingsRequest) (*settingsproto.GetUserSettingsResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetUserSettings: tenant_id=%s", authInfo.TenantID)

	rowKey, err := s.getUserRowKey(ctx, authInfo, req.UserId)
	if err != nil {
		return nil, err
	}

	rows, err := s.ProxyClient.ReadRows(
		ctx,
		authInfo.TenantID,
		proxyproto.TableName_SETTINGS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{[]byte(rowKey)},
		},
		&bigtableproto.RowFilter{},
		1,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Msgf("GetUserSettings: failed to read rows: %v", err)
		return nil, err
	}
	if len(rows) > 1 {
		return nil, fmt.Errorf("expected at most one row, got %d", len(rows))
	}

	settings := &settingsproto.UserSettings{}
	version := ""
	// if there is no row, return an empty settings
	if len(rows) == 1 {
		for _, cell := range rows[0].Cells {
			if cell.FamilyName != familyName {
				continue
			}
			if string(cell.Qualifier) == versionColumn {
				version = string(cell.Value)
			} else if err := s.userCellToSettings(cell, settings); err != nil {
				return nil, err
			}
		}
	}
	return &settingsproto.GetUserSettingsResponse{Settings: settings, Version: version}, nil
}

func (s *SettingsServer) userCellToSettings(cell *bigtableproxy.RowCell, settings *settingsproto.UserSettings) error {
	if string(cell.Qualifier) == githubUserSettingsColumn {
		settings.GithubUserSettings = &settingsproto.GithubUserSettings{}
		err := proto.Unmarshal(cell.Value, settings.GithubUserSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == gleanUserSettingsColumn {
		settings.GleanUserSettings = &settingsproto.GleanUserSettings{}
		err := proto.Unmarshal(cell.Value, settings.GleanUserSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == linearUserSettingsColumn {
		settings.LinearUserSettings = &settingsproto.LinearUserSettings{}
		err := proto.Unmarshal(cell.Value, settings.LinearUserSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == notionUserSettingsColumn {
		settings.NotionUserSettings = &settingsproto.NotionUserSettings{}
		err := proto.Unmarshal(cell.Value, settings.NotionUserSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == atlassianUserSettingsColumn {
		settings.AtlassianUserSettings = &settingsproto.AtlassianUserSettings{}
		err := proto.Unmarshal(cell.Value, settings.AtlassianUserSettings)
		if err != nil {
			return err
		}
		return nil
	} else if string(cell.Qualifier) == supabaseUserSettingsColumn {
		settings.SupabaseUserSettings = &settingsproto.SupabaseUserSettings{}
		err := proto.Unmarshal(cell.Value, settings.SupabaseUserSettings)
		if err != nil {
			return err
		}
		return nil
	}
	log.Error().Msgf("Unknown column: %s", cell.Qualifier)
	return status.Error(codes.Internal, "Unknown column")
}

func (s *SettingsServer) userRequestToMutations(req *settingsproto.UpdateUserSettingsRequest) ([]*bigtableproto.Mutation, error) {
	mutations := []*bigtableproto.Mutation{}
	if req.Settings.GithubUserSettings != nil {
		githubUserSettingsBytes, err := proto.Marshal(req.Settings.GithubUserSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(githubUserSettingsColumn),
					Value:           githubUserSettingsBytes,
				},
			},
		})
	}
	if req.Settings.GleanUserSettings != nil {
		gleanUserSettingsBytes, err := proto.Marshal(req.Settings.GleanUserSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(gleanUserSettingsColumn),
					Value:           gleanUserSettingsBytes,
				},
			},
		})
	}
	if req.Settings.LinearUserSettings != nil {
		linearUserSettingsBytes, err := proto.Marshal(req.Settings.LinearUserSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(linearUserSettingsColumn),
					Value:           linearUserSettingsBytes,
				},
			},
		})
	}
	if req.Settings.NotionUserSettings != nil {
		notionUserSettingsBytes, err := proto.Marshal(req.Settings.NotionUserSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(notionUserSettingsColumn),
					Value:           notionUserSettingsBytes,
				},
			},
		})
	}
	if req.Settings.AtlassianUserSettings != nil {
		atlassianUserSettingsBytes, err := proto.Marshal(req.Settings.AtlassianUserSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(atlassianUserSettingsColumn),
					Value:           atlassianUserSettingsBytes,
				},
			},
		})
	}
	if req.Settings.SupabaseUserSettings != nil {
		supabaseUserSettingsBytes, err := proto.Marshal(req.Settings.SupabaseUserSettings)
		if err != nil {
			return nil, err
		}
		mutations = append(mutations, &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(supabaseUserSettingsColumn),
					Value:           supabaseUserSettingsBytes,
				},
			},
		})
	}
	return mutations, nil
}

func (s *SettingsServer) UpdateUserSettings(ctx context.Context, req *settingsproto.UpdateUserSettingsRequest) (*settingsproto.UpdateUserSettingsResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	if req.Settings == nil {
		return nil, status.Error(codes.InvalidArgument, "settings is required")
	}
	log.Ctx(ctx).Info().Msgf("UpdateUserSettings: tenant_id=%s", authInfo.TenantID)

	mutations, err := s.userRequestToMutations(req)
	if err != nil {
		return nil, err
	}

	version := uuid.New().String()

	mutations = append(mutations,
		&bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      familyName,
					ColumnQualifier: []byte(versionColumn),
					Value:           []byte(version),
				},
			},
		},
	)

	rowKey, err := s.getUserRowKey(ctx, authInfo, req.UserId)
	if err != nil {
		return nil, err
	}

	var filter *bigtableproto.RowFilter
	if req.ExpectedVersion != "" {
		filter = bigtableproxy.ChainFilters(
			bigtableproxy.FamilyFilter(familyName),
			bigtableproxy.ColumnFilter(versionColumn),
			bigtableproxy.ValueFilter(req.ExpectedVersion),
		).Proto()
	}

	if filter == nil {
		entries := []*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey:    []byte(rowKey),
				Mutations: mutations,
			},
		}
		mutateRowsResponse, err := s.ProxyClient.MutateRows(
			ctx,
			authInfo.TenantID,
			proxyproto.TableName_SETTINGS,
			entries,
			requestContext,
		)
		if err != nil {
			return nil, err
		}
		for _, mutateRowsResponse := range mutateRowsResponse {
			for _, entry := range mutateRowsResponse.Entries {
				if entry.Status.Code != int32(codes.OK) {
					return nil, status.Error(codes.Internal, "Failed to write row")
				}
			}
		}
	} else {
		checkAndMutateRowResponse, err := s.ProxyClient.CheckAndMutateRow(
			ctx,
			authInfo.TenantID,
			proxyproto.TableName_SETTINGS,
			[]byte(rowKey),
			filter,
			mutations,
			nil,
			requestContext,
		)
		if err != nil {
			return nil, err
		}
		if !checkAndMutateRowResponse.PredicateMatched {
			return nil, status.Error(codes.Aborted, "Version mismatch")
		}
	}
	return &settingsproto.UpdateUserSettingsResponse{
		Version: version,
	}, nil
}

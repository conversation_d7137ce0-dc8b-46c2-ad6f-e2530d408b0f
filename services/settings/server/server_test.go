package main

import (
	"context"
	"testing"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	server "github.com/augmentcode/augment/services/settings/server"
	"github.com/rs/zerolog/log"

	"github.com/augmentcode/augment/base/go/secretstring"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	fakebigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client/fake_client"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
)

func TestUpdateTenantSettings(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	githubSettings := &settingsproto.GithubSettings{
		InstallationId: 1,
		Repos: []*settingsproto.RepoInformation{
			{
				RepoOwner: "test-repo-owner",
				RepoName:  "test-repo-name",
			},
		},
	}

	slackSettings := &settingsproto.SlackSettings{
		EnterpriseId: "test-enterprise-id",
		TeamId:       "test-team-id",
		OauthToken:   "test-oauth-token",
	}

	// Create a test request
	req := &settingsproto.UpdateTenantSettingsRequest{
		Settings: &settingsproto.TenantSettings{
			GithubSettings: githubSettings,
			SlackSettings:  slackSettings,
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	fakeBigtable.Dump()

	settings, err := settingsServer.GetTenantSettings(ctx, &settingsproto.GetTenantSettingsRequest{})
	if err != nil {
		t.Errorf("GetTenantSettings returned error: %v", err)
	}

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetTenantSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if resp.Version != settings.Version {
		t.Errorf("GetTenantSettings returned incorrect version, expected: %v, got: %v", resp.Version, settings.Version)
	}
}

func TestOverwriteUpdateTenantSettings(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	githubSettings := &settingsproto.GithubSettings{
		InstallationId: 1,
		Repos: []*settingsproto.RepoInformation{
			{
				RepoOwner: "test-repo-owner",
				RepoName:  "test-repo-name",
			},
		},
	}

	slackSettings := &settingsproto.SlackSettings{
		EnterpriseId: "test-enterprise-id",
		TeamId:       "test-team-id",
		OauthToken:   "test-oauth-token",
	}

	// Create a test request
	req := &settingsproto.UpdateTenantSettingsRequest{
		Settings: &settingsproto.TenantSettings{
			GithubSettings: githubSettings,
			SlackSettings:  slackSettings,
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	fakeBigtable.Dump()

	// Create a test request
	req.Settings.GithubSettings.InstallationId = 2
	req.Settings.SlackSettings.OauthToken = "test-oauth-token-2"

	requestId = requestcontext.NewRandomRequestId()
	requestContext = requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err = settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	settings, err := settingsServer.GetTenantSettings(ctx, &settingsproto.GetTenantSettingsRequest{})
	if err != nil {
		t.Errorf("GetTenantSettings returned error: %v", err)
	}

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetTenantSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if resp.Version != settings.Version {
		t.Errorf("GetTenantSettings returned incorrect version, expected: %v, got: %v", resp.Version, settings.Version)
	}
}

func TestPartialOverwriteUpdateTenantSettings(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	githubSettings := &settingsproto.GithubSettings{
		InstallationId: 1,
		Repos: []*settingsproto.RepoInformation{
			{
				RepoOwner: "test-repo-owner",
				RepoName:  "test-repo-name",
			},
		},
	}

	slackSettings := &settingsproto.SlackSettings{
		EnterpriseId: "test-enterprise-id",
		TeamId:       "test-team-id",
		OauthToken:   "test-oauth-token",
	}

	// Create a test request
	req := &settingsproto.UpdateTenantSettingsRequest{
		Settings: &settingsproto.TenantSettings{
			GithubSettings: githubSettings,
			SlackSettings:  slackSettings,
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	fakeBigtable.Dump()

	// Create a test request
	oldGithubSettings := req.Settings.GithubSettings
	req.Settings.GithubSettings = nil
	req.Settings.SlackSettings.OauthToken = "test-oauth-token-2"

	requestId = requestcontext.NewRandomRequestId()
	requestContext = requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err = settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	settings, err := settingsServer.GetTenantSettings(ctx, &settingsproto.GetTenantSettingsRequest{})
	if err != nil {
		t.Errorf("GetTenantSettings returned error: %v", err)
	}

	// restore the old github settings
	req.Settings.GithubSettings = oldGithubSettings

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetTenantSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if resp.Version != settings.Version {
		t.Errorf("GetTenantSettings returned incorrect version, expected: %v, got: %v", resp.Version, settings.Version)
	}
}

func TestOverwriteUpdateTenantSettingsWithVersion(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	githubSettings := &settingsproto.GithubSettings{
		InstallationId: 1,
		Repos: []*settingsproto.RepoInformation{
			{
				RepoOwner: "test-repo-owner",
				RepoName:  "test-repo-name",
			},
		},
	}

	slackSettings := &settingsproto.SlackSettings{
		EnterpriseId: "test-enterprise-id",
		TeamId:       "test-team-id",
		OauthToken:   "test-oauth-token",
	}

	// Create a test request
	req := &settingsproto.UpdateTenantSettingsRequest{
		Settings: &settingsproto.TenantSettings{
			GithubSettings: githubSettings,
			SlackSettings:  slackSettings,
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	fakeBigtable.Dump()

	// Create a test request
	req.Settings.GithubSettings.InstallationId = 2
	req.Settings.SlackSettings.OauthToken = "test-oauth-token-2"
	req.ExpectedVersion = resp.Version

	requestId = requestcontext.NewRandomRequestId()
	requestContext = requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err = settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	settings, err := settingsServer.GetTenantSettings(ctx, &settingsproto.GetTenantSettingsRequest{})
	if err != nil {
		t.Errorf("GetTenantSettings returned error: %v", err)
	}

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetTenantSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if resp.Version != settings.Version {
		t.Errorf("GetTenantSettings returned incorrect version, expected: %v, got: %v", resp.Version, settings.Version)
	}
}

func TestOverwriteUpdateTenantSettingsWithWrongVersion(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	githubSettings := &settingsproto.GithubSettings{
		InstallationId: 1,
		Repos: []*settingsproto.RepoInformation{
			{
				RepoOwner: "test-repo-owner",
				RepoName:  "test-repo-name",
			},
		},
	}

	slackSettings := &settingsproto.SlackSettings{
		EnterpriseId: "test-enterprise-id",
		TeamId:       "test-team-id",
		OauthToken:   "test-oauth-token",
	}

	// Create a test request
	req := &settingsproto.UpdateTenantSettingsRequest{
		Settings: &settingsproto.TenantSettings{
			GithubSettings: githubSettings,
			SlackSettings:  slackSettings,
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateTenantSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateTenantSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateTenantSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateTenantSettings returned empty version")
	}

	fakeBigtable.Dump()

	// Create a test request
	req.Settings.GithubSettings.InstallationId = 2
	req.Settings.SlackSettings.OauthToken = "test-oauth-token-2"
	req.ExpectedVersion = "wrong-version"

	requestId = requestcontext.NewRandomRequestId()
	requestContext = requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	_, err = settingsServer.UpdateTenantSettings(ctx, req)
	if status.Code(err) != codes.Aborted {
		t.Errorf("GetTenantSettings returned wrong error: %v", err)
	}
}

func TestGetTenantSettingsMissing(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	log.Info().Msgf("fakeBigtable: %v", fakeBigtable)
	fakeBigtable.Dump()

	settings, err := settingsServer.GetTenantSettings(ctx, &settingsproto.GetTenantSettingsRequest{})
	if err != nil {
		t.Errorf("GetTenantSettings returned error: %v", err)
	}
	log.Info().Msgf("settings: %v", settings)
	if !proto.Equal(settings.Settings, &settingsproto.TenantSettings{}) {
		t.Errorf("GetTenantSettings returned non-nil settings: %v", settings.Settings)
	}
	if settings.Version != "" {
		t.Errorf("GetTenantSettings returned non-empty version: %v", settings.Version)
	}
}

func TestUpdateUserSettings(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{
			GleanUserSettings: &settingsproto.GleanUserSettings{
				AccessToken:  "test-access-token",
				RefreshToken: "test-refresh-token",
			},
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Fatalf("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateUserSettings returned empty version")
	}

	fakeBigtable.Dump()

	settings, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Errorf("GetUserSettings returned error: %v", err)
	}

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetUserSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if resp.Version != settings.Version {
		t.Errorf("GetUserSettings returned incorrect version, expected: %v, got: %v", resp.Version, settings.Version)
	}
}

func TestUpdateAndGetUserSettingsWithLinear(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "test-user-id",
		OpaqueUserID:     "test-opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "test-tenant-id",
		TenantName:       "test-tenant-name",
		ShardNamespace:   "test-shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	// Create a test request with Linear user settings
	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{
			LinearUserSettings: &settingsproto.LinearUserSettings{
				AccessToken: "test-linear-access-token",
			},
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateUserSettings method
	resp, err := settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Fatalf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Fatal("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Error("UpdateUserSettings returned empty version")
	}

	// Get the settings and verify they were updated correctly
	settings, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Fatalf("GetUserSettings returned error: %v", err)
	}
	if settings.Settings.LinearUserSettings == nil {
		t.Error("GetUserSettings returned nil LinearUserSettings")
	}
	if settings.Settings.LinearUserSettings.AccessToken != "test-linear-access-token" {
		t.Errorf("GetUserSettings returned incorrect LinearUserSettings.AccessToken: got %s, want %s",
			settings.Settings.LinearUserSettings.AccessToken, "test-linear-access-token")
	}
}

func TestGetUserSettingsDifferentUser(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{
			GleanUserSettings: &settingsproto.GleanUserSettings{
				AccessToken:  "test-access-token",
				RefreshToken: "test-refresh-token",
			},
		},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateUserSettings returned empty version")
	}

	fakeBigtable.Dump()

	claims = &auth.AugmentClaims{
		UserID:           "user-id2",
		OpaqueUserID:     "opaque-user-id2",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	settings, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Errorf("GetUserSettings returned error: %v", err)
	}

	expectedSettings := &settingsproto.UserSettings{}

	if !proto.Equal(expectedSettings, settings.Settings) {
		t.Errorf("GetUserSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if settings.Version != "" {
		t.Errorf("GetUserSettings returned incorrect version, expected: %v, got: %v", "", settings.Version)
	}
}

func TestGetUserSettingsIncorrectUser(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	_, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{
		UserId: "user-id2",
	})
	if err == nil {
		t.Errorf("GetUserSettings returned nil error")
	}
}

func TestUpdateUserSettingsIncorrectUser(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{},
		UserId:   "user-id2",
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	_, err := settingsServer.UpdateUserSettings(ctx, req)
	if err == nil {
		t.Errorf("GetUserSettings returned nil error")
	}
}

func TestUpdateUserSettingsAdminUser(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "",
		OpaqueUserID:     "",
		OpaqueUserIDType: "",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{},
		UserId:   "user-id2",
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	_, err := settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("GetUserSettings returned error: %v", err)
	}

	claims = &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	settings, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Errorf("GetUserSettings returned error: %v", err)
	}

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetUserSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if settings.Version != "" {
		t.Errorf("GetUserSettings returned incorrect version, expected: %v, got: %v", "", settings.Version)
	}
}

func TestOverwriteUpdateUserSettings(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	// Create a test request
	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateUserSettings returned empty version")
	}

	fakeBigtable.Dump()

	requestId = requestcontext.NewRandomRequestId()
	requestContext = requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	resp, err = settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateUserSettings returned empty version")
	}

	settings, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Errorf("GetUserSettings returned error: %v", err)
	}

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetUserSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if resp.Version != settings.Version {
		t.Errorf("GetUserSettings returned incorrect version, expected: %v, got: %v", resp.Version, settings.Version)
	}
}

func TestOverwriteUpdateUserSettingsWithVersion(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateUserSettings method
	resp, err := settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateUserSettings returned empty version")
	}

	fakeBigtable.Dump()

	req.ExpectedVersion = resp.Version

	requestId = requestcontext.NewRandomRequestId()
	requestContext = requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	resp, err = settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateUserSettings returned empty version")
	}

	settings, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Errorf("GetUserSettings returned error: %v", err)
	}

	if !proto.Equal(req.Settings, settings.Settings) {
		t.Errorf("GetUserSettings returned incorrect settings, expected: %v, got: %v", req.Settings, settings.Settings)
	}
	if resp.Version != settings.Version {
		t.Errorf("GetUserSettings returned incorrect version, expected: %v, got: %v", resp.Version, settings.Version)
	}
}

func TestOverwriteUpdateUserSettingsWithWrongVersion(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	// Create a test request
	req := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{},
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the UpdateTenantSettings method
	resp, err := settingsServer.UpdateUserSettings(ctx, req)
	if err != nil {
		t.Errorf("UpdateUserSettings returned error: %v", err)
	}
	if resp == nil {
		t.Errorf("UpdateUserSettings returned nil response")
	}
	if resp.Version == "" {
		t.Errorf("UpdateUserSettings returned empty version")
	}

	fakeBigtable.Dump()

	// Create a test request
	req.ExpectedVersion = "wrong-version"

	requestId = requestcontext.NewRandomRequestId()
	requestContext = requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	_, err = settingsServer.UpdateUserSettings(ctx, req)
	if status.Code(err) != codes.Aborted {
		t.Errorf("GetTenantSettings returned wrong error: %v", err)
	}
}

func TestGetUserSettingsMissing(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	log.Info().Msgf("fakeBigtable: %v", fakeBigtable)
	fakeBigtable.Dump()

	settings, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Errorf("GetUserSettings returned error: %v", err)
	}
	if !proto.Equal(settings.Settings, &settingsproto.UserSettings{}) {
		t.Errorf("GetUserSettings returned non-nil settings: %v", settings.Settings)
	}
	if settings.Version != "" {
		t.Errorf("GetUserSettings returned non-empty version: %v", settings.Version)
	}
}

func TestGetTenantSettingsCommunity(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_COMMUNITY_TIER)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Call the GetTenantSettings method
	_, err := settingsServer.GetTenantSettings(ctx, &settingsproto.GetTenantSettingsRequest{})

	// Expected error
	if err == nil {
		t.Errorf("GetTenantSettings returned nil error")
	}
}

func TestUpdateOnlyGithubUserSettings(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:           "user-id",
		OpaqueUserID:     "opaque-user-id",
		OpaqueUserIDType: "AUGMENT",
		ServiceName:      "service-name",
		TenantID:         "tenant-id",
		TenantName:       "tenant-name",
		ShardNamespace:   "shard-namespace",
		Scope:            []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SETTINGS"})
	// Create a test server
	settingsServer := server.NewSettingsServer(fakeBigtable, publicapiproto.GetModelsResponse_ENTERPRISE_TIER)

	// Create initial settings with multiple setting types
	initialSettings := &settingsproto.UserSettings{
		GleanUserSettings: &settingsproto.GleanUserSettings{
			AccessToken:  "initial-glean-access-token",
			RefreshToken: "initial-glean-refresh-token",
			TokenType:    "bearer",
		},
		LinearUserSettings: &settingsproto.LinearUserSettings{
			AccessToken: "initial-linear-access-token",
			Scope:       "initial-linear-scope",
		},
		GithubUserSettings: &settingsproto.GithubUserSettings{
			AccessToken:  "initial-github-access-token",
			RefreshToken: "initial-github-refresh-token",
			TokenType:    "bearer",
			GithubLogin:  "initial-github-login",
			GithubName:   "initial-github-name",
			GithubEmail:  "initial-github-email",
		},
		NotionUserSettings: &settingsproto.NotionUserSettings{
			AccessToken:   "initial-notion-access-token",
			BotId:         "initial-notion-bot-id",
			WorkspaceName: "initial-notion-workspace-name",
			WorkspaceId:   "initial-notion-workspace-id",
		},
	}

	initialReq := &settingsproto.UpdateUserSettingsRequest{
		Settings: initialSettings,
	}

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Set initial settings
	initialResp, err := settingsServer.UpdateUserSettings(ctx, initialReq)
	if err != nil {
		t.Fatalf("Initial UpdateUserSettings returned error: %v", err)
	}
	if initialResp == nil || initialResp.Version == "" {
		t.Fatalf("Initial UpdateUserSettings returned invalid response")
	}

	// Verify initial settings were set correctly
	initialGetResp, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Fatalf("Initial GetUserSettings returned error: %v", err)
	}
	if !proto.Equal(initialSettings, initialGetResp.Settings) {
		t.Fatalf("Initial settings not set correctly, expected: %v, got: %v", initialSettings, initialGetResp.Settings)
	}

	// Now update only the GithubUserSettings
	updatedGithubSettings := &settingsproto.GithubUserSettings{
		AccessToken:  "updated-github-access-token",
		RefreshToken: "updated-github-refresh-token",
		TokenType:    "updated-token-type",
		GithubLogin:  "updated-github-login",
		GithubName:   "updated-github-name",
		GithubEmail:  "updated-github-email",
	}

	updateReq := &settingsproto.UpdateUserSettingsRequest{
		Settings: &settingsproto.UserSettings{
			GithubUserSettings: updatedGithubSettings,
		},
		ExpectedVersion: initialResp.Version,
	}

	// Update only GitHub settings
	updateResp, err := settingsServer.UpdateUserSettings(ctx, updateReq)
	if err != nil {
		t.Fatalf("UpdateUserSettings returned error: %v", err)
	}
	if updateResp == nil || updateResp.Version == "" {
		t.Fatalf("UpdateUserSettings returned invalid response")
	}

	// Get the updated settings
	updatedGetResp, err := settingsServer.GetUserSettings(ctx, &settingsproto.GetUserSettingsRequest{})
	if err != nil {
		t.Fatalf("GetUserSettings returned error: %v", err)
	}

	// Verify that only GitHub settings were updated
	// 1. GitHub settings should match the updated values
	if !proto.Equal(updatedGithubSettings, updatedGetResp.Settings.GithubUserSettings) {
		t.Errorf("GitHub settings not updated correctly, expected: %v, got: %v",
			updatedGithubSettings, updatedGetResp.Settings.GithubUserSettings)
	}

	// 2. Other settings should remain unchanged
	if !proto.Equal(initialSettings.GleanUserSettings, updatedGetResp.Settings.GleanUserSettings) {
		t.Errorf("Glean settings were unexpectedly changed, expected: %v, got: %v",
			initialSettings.GleanUserSettings, updatedGetResp.Settings.GleanUserSettings)
	}

	if !proto.Equal(initialSettings.LinearUserSettings, updatedGetResp.Settings.LinearUserSettings) {
		t.Errorf("Linear settings were unexpectedly changed, expected: %v, got: %v",
			initialSettings.LinearUserSettings, updatedGetResp.Settings.LinearUserSettings)
	}

	if !proto.Equal(initialSettings.NotionUserSettings, updatedGetResp.Settings.NotionUserSettings) {
		t.Errorf("Notion settings were unexpectedly changed, expected: %v, got: %v",
			initialSettings.NotionUserSettings, updatedGetResp.Settings.NotionUserSettings)
	}
}

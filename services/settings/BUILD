load("//tools/bzl:go.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "settings_proto",
    srcs = ["settings.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/integrations/glean:glean_proto",
        "//services/integrations/supabase:supabase_proto",
        "@protobuf//:field_mask_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_proto_library(
    name = "settings_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "github.com/augmentcode/augment/services/settings/proto",
    proto = ":settings_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/integrations/glean:glean_go_proto",
        "//services/integrations/supabase:supabase_go_proto",
    ],
)

py_grpc_library(
    name = "settings_py_proto",
    protos = [":settings_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/integrations/glean:glean_py_proto",
        "//services/integrations/supabase:supabase_py_proto",
    ],
)

ts_proto_library(
    name = "settings_ts_proto",
    data = [
        "//services/integrations/glean:glean_ts_proto",
        "//services/integrations/supabase:supabase_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":settings_proto",
    visibility = ["//services:__subpackages__"],
)

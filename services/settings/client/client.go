package client

import (
	"context"
	"fmt"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/settings/proto"
)

// SettingsClient is a client for the settings service.
type SettingsClient interface {
	GetTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext) (*pb.GetTenantSettingsResponse, error)
	UpdateTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext, settings *pb.TenantSettings, expectedVersion string) (*pb.UpdateTenantSettingsResponse, error)
	GetUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string) (*pb.GetUserSettingsResponse, error)
	UpdateUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string, settings *pb.UserSettings, expectedVersion string) (*pb.UpdateUserSettingsResponse, error)
	Close() error
}

type settingsClientImpl struct {
	endpoint string
	conn     *grpc.ClientConn
	client   pb.SettingsClient
}

// NewSettingsClient creates a new SettingsClient.
func NewSettingsClient(endpoint string, creds credentials.TransportCredentials) (SettingsClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to settings service: %v", err)
	}

	client := pb.NewSettingsClient(conn)
	return &settingsClientImpl{
		endpoint: endpoint,
		conn:     conn,
		client:   client,
	}, nil
}

// Close closes the client connection.
func (c *settingsClientImpl) Close() error {
	return c.conn.Close()
}

// GetTenantSettings retrieves the settings for a tenant.
func (c *settingsClientImpl) GetTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext) (*pb.GetTenantSettingsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	req := &pb.GetTenantSettingsRequest{}
	return c.client.GetTenantSettings(ctx, req)
}

// UpdateTenantSettings updates the settings for a tenant.
func (c *settingsClientImpl) UpdateTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext, settings *pb.TenantSettings, expectedVersion string) (*pb.UpdateTenantSettingsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	req := &pb.UpdateTenantSettingsRequest{
		Settings: settings,
	}
	if expectedVersion != "" {
		req.ExpectedVersion = expectedVersion
	}

	return c.client.UpdateTenantSettings(ctx, req)
}

func (c *settingsClientImpl) GetUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string) (*pb.GetUserSettingsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	req := &pb.GetUserSettingsRequest{}
	if userId != nil {
		req.UserId = *userId
	}
	return c.client.GetUserSettings(ctx, req)
}

func (c *settingsClientImpl) UpdateUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string, settings *pb.UserSettings, expectedVersion string) (*pb.UpdateUserSettingsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	req := &pb.UpdateUserSettingsRequest{
		Settings: settings,
	}
	if expectedVersion != "" {
		req.ExpectedVersion = expectedVersion
	}
	if userId != nil {
		req.UserId = *userId
	}

	return c.client.UpdateUserSettings(ctx, req)
}

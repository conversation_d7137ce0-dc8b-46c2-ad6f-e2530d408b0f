"""A Python client library for the settings service."""

import logging
from typing import Optional

import grpc

from base.python.grpc import client_options
from services.settings import settings_pb2, settings_pb2_grpc

from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> settings_pb2_grpc.SettingsStub:
    """Setup the client stub for the settings service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials=credentials, options=client_options.create(options)
        )
    return settings_pb2_grpc.SettingsStub(channel)


class SettingsClient:
    """Client for the settings service."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Initialize the settings client."""
        self.stub = setup_stub(endpoint, credentials, options)

    def get_user_settings(
        self,
        request_context: RequestContext,
        user_id: str | None = None,
        timeout: float = 30,
    ) -> settings_pb2.GetUserSettingsResponse:
        """Get settings for the specified user in the given tenant."""

        request = settings_pb2.GetUserSettingsRequest()
        if user_id is not None:
            request.user_id = user_id

        return self.stub.GetUserSettings(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )

    def update_user_settings(
        self,
        request_context: RequestContext,
        request: settings_pb2.UpdateUserSettingsRequest,
        timeout: float = 30,
    ) -> settings_pb2.UpdateUserSettingsResponse:
        """Update settings for the specified user in the given tenant."""

        return self.stub.UpdateUserSettings(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )

    def get_tenant_settings(
        self,
        request_context: RequestContext,
        request: settings_pb2.GetTenantSettingsRequest,
        timeout: float = 30,
    ) -> settings_pb2.GetTenantSettingsResponse:
        """Get settings for the specified tenant."""

        return self.stub.GetTenantSettings(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )

    def update_tenant_settings(
        self,
        request_context: RequestContext,
        request: settings_pb2.UpdateTenantSettingsRequest,
        timeout: float = 30,
    ) -> settings_pb2.UpdateTenantSettingsResponse:
        """Update settings for the specified tenant."""

        return self.stub.UpdateTenantSettings(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )

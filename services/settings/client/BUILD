load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/settings/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "//services/settings:settings_go_proto",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//credentials:go_default_library",
        "@org_golang_google_grpc//metadata:go_default_library",
    ],
)

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
        "//services/settings:settings_py_proto",
    ],
)

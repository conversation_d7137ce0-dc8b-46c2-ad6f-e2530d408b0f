load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "remote_agents_proto",
    srcs = ["remote_agents.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/chat_host:chat_proto",
        "@protobuf//:timestamp_proto",
    ],
)

ts_proto_library(
    name = "remote_agents_ts_proto",
    node_modules = "//:node_modules",
    proto = ":remote_agents_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/chat_host:chat_host_ts_proto",
    ],
)

go_grpc_library(
    name = "remote_agents_go_proto",
    importpath = "github.com/augmentcode/augment/services/remote_agents/proto",
    proto = ":remote_agents_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/chat_host:chat_host_go_proto",
    ],
)

py_grpc_library(
    name = "remote_agents_py_proto",
    protos = [":remote_agents_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/chat_host:chat_proto_py_proto",
    ],
)

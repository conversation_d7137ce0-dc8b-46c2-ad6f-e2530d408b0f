[package]
name = "remote_agents_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "remote_agents_client"
path = "client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
blob_names = { path = "../../../base/blob_names/rust" }
grpc_client = { path = "../../lib/grpc/client" }
prost = { workspace = true }
prost-wkt-types = { workspace = true }
serde = {workspace = true}
serde_json = {workspace = true}
serde_urlencoded = {workspace = true}
tonic = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
request_context = { path = "../../lib/request_context" }
feature-flags = { path = "../../../base/feature_flags" }

[build-dependencies]
tonic-build = { workspace = true }
prost-build = {workspace = true}
prost-wkt-build = {workspace = true}

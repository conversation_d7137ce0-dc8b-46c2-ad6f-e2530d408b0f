use std::sync::Arc;

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;

use request_context::RequestContext;
use tracing_tonic::client::TracingService;

pub mod remote_agents {
    tonic::include_proto!("remote_agents");
}

pub mod chat {
    tonic::include_proto!("chat");
}

pub mod base {
    pub mod blob_names {
        tonic::include_proto!("base.blob_names");
    }
}

#[async_trait]
pub trait RemoteAgentsClient: Send + Sync {
    async fn create_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::CreateAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::CreateAgentResponse, tonic::Status>;

    async fn list_agents(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ListAgentsRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ListAgentsResponse, tonic::Status>;

    async fn chat(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ChatRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ChatResponse, tonic::Status>;

    async fn chat_history(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ChatHistoryRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ChatHistoryResponse, tonic::Status>;

    async fn agent_history_stream(
        &self,
        request_context: &RequestContext,
        request: remote_agents::AgentHistoryStreamRequest,
    ) -> Result<
        tonic::Response<tonic::Streaming<remote_agents::AgentHistoryStreamResponse>>,
        tonic::Status,
    >;

    async fn interrupt_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::InterruptAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::InterruptAgentResponse, tonic::Status>;

    async fn delete_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::DeleteAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::DeleteAgentResponse, tonic::Status>;

    async fn add_ssh_key(
        &self,
        request_context: &RequestContext,
        request: remote_agents::AddSshKeyRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::AddSshKeyResponse, tonic::Status>;

    async fn resume_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ResumeAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ResumeAgentResponse, tonic::Status>;

    async fn pause_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::PauseAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::PauseAgentResponse, tonic::Status>;

    async fn workspace_report_status(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceReportStatusRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportStatusResponse, tonic::Status>;

    async fn workspace_report_chat_history(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceReportChatHistoryRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportChatHistoryResponse, tonic::Status>;

    async fn workspace_poll_update(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspacePollUpdateRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspacePollUpdateResponse, tonic::Status>;

    async fn workspace_stream(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceStreamRequest,
    ) -> Result<
        tonic::Response<tonic::Streaming<remote_agents::WorkspaceStreamResponse>>,
        tonic::Status,
    >;

    async fn workspace_logs(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceLogsRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceLogsResponse, tonic::Status>;

    async fn workspace_report_setup_logs(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceReportSetupLogsRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportSetupLogsResponse, tonic::Status>;
}

pub struct RemoteAgentsClientImpl {
    endpoint: String,
    tls_config: Option<tonic::transport::ClientTlsConfig>,
    client:
        Arc<Mutex<Option<remote_agents::remote_agents_client::RemoteAgentsClient<TracingService>>>>,
}

impl RemoteAgentsClientImpl {
    pub fn new(endpoint: &str, tls_config: Option<tonic::transport::ClientTlsConfig>) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        remote_agents::remote_agents_client::RemoteAgentsClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            Some(c) => Ok(c.clone()),
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = remote_agents::remote_agents_client::RemoteAgentsClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
        }
    }
}

#[async_trait]
impl RemoteAgentsClient for RemoteAgentsClientImpl {
    async fn create_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::CreateAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::CreateAgentResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.create_agent(request).await?;
        Ok(response.into_inner())
    }

    async fn list_agents(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ListAgentsRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ListAgentsResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.list_agents(request).await?;
        Ok(response.into_inner())
    }

    async fn chat(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ChatRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ChatResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.chat(request).await?;
        Ok(response.into_inner())
    }

    async fn chat_history(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ChatHistoryRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ChatHistoryResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.chat_history(request).await?;
        Ok(response.into_inner())
    }

    async fn agent_history_stream(
        &self,
        request_context: &RequestContext,
        request: remote_agents::AgentHistoryStreamRequest,
    ) -> Result<
        tonic::Response<tonic::Streaming<remote_agents::AgentHistoryStreamResponse>>,
        tonic::Status,
    > {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request_context.annotate(request.metadata_mut());
        client.agent_history_stream(request).await
    }

    async fn interrupt_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::InterruptAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::InterruptAgentResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.interrupt_agent(request).await?;
        Ok(response.into_inner())
    }

    async fn delete_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::DeleteAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::DeleteAgentResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.delete_agent(request).await?;
        Ok(response.into_inner())
    }

    async fn add_ssh_key(
        &self,
        request_context: &RequestContext,
        request: remote_agents::AddSshKeyRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::AddSshKeyResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.add_ssh_key(request).await?;
        Ok(response.into_inner())
    }

    async fn resume_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::ResumeAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::ResumeAgentResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.resume_agent(request).await?;
        Ok(response.into_inner())
    }

    async fn pause_agent(
        &self,
        request_context: &RequestContext,
        request: remote_agents::PauseAgentRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::PauseAgentResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.pause_agent(request).await?;
        Ok(response.into_inner())
    }

    async fn workspace_report_status(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceReportStatusRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportStatusResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.workspace_report_status(request).await?;
        Ok(response.into_inner())
    }

    async fn workspace_report_chat_history(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceReportChatHistoryRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportChatHistoryResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.workspace_report_chat_history(request).await?;
        Ok(response.into_inner())
    }

    async fn workspace_poll_update(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspacePollUpdateRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspacePollUpdateResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.workspace_poll_update(request).await?;
        Ok(response.into_inner())
    }

    async fn workspace_stream(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceStreamRequest,
    ) -> Result<
        tonic::Response<tonic::Streaming<remote_agents::WorkspaceStreamResponse>>,
        tonic::Status,
    > {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request_context.annotate(request.metadata_mut());
        client.workspace_stream(request).await
    }

    async fn workspace_logs(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceLogsRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceLogsResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.workspace_logs(request).await?;
        Ok(response.into_inner())
    }

    async fn workspace_report_setup_logs(
        &self,
        request_context: &RequestContext,
        request: remote_agents::WorkspaceReportSetupLogsRequest,
        timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportSetupLogsResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("remote agents client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("remote agents not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.workspace_report_setup_logs(request).await?;
        Ok(response.into_inner())
    }
}

pub struct MockRemoteAgentsClient;

impl Default for MockRemoteAgentsClient {
    fn default() -> Self {
        Self::new()
    }
}

impl MockRemoteAgentsClient {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl RemoteAgentsClient for MockRemoteAgentsClient {
    async fn create_agent(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::CreateAgentRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::CreateAgentResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn list_agents(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::ListAgentsRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::ListAgentsResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn chat(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::ChatRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::ChatResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn chat_history(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::ChatHistoryRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::ChatHistoryResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn agent_history_stream(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::AgentHistoryStreamRequest,
    ) -> Result<
        tonic::Response<tonic::Streaming<remote_agents::AgentHistoryStreamResponse>>,
        tonic::Status,
    > {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn interrupt_agent(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::InterruptAgentRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::InterruptAgentResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn delete_agent(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::DeleteAgentRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::DeleteAgentResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn add_ssh_key(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::AddSshKeyRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::AddSshKeyResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn resume_agent(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::ResumeAgentRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::ResumeAgentResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn pause_agent(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::PauseAgentRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::PauseAgentResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn workspace_report_status(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::WorkspaceReportStatusRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportStatusResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn workspace_report_chat_history(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::WorkspaceReportChatHistoryRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportChatHistoryResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn workspace_poll_update(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::WorkspacePollUpdateRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspacePollUpdateResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn workspace_stream(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::WorkspaceStreamRequest,
    ) -> Result<
        tonic::Response<tonic::Streaming<remote_agents::WorkspaceStreamResponse>>,
        tonic::Status,
    > {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn workspace_logs(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::WorkspaceLogsRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceLogsResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }

    async fn workspace_report_setup_logs(
        &self,
        _request_context: &RequestContext,
        _request: remote_agents::WorkspaceReportSetupLogsRequest,
        _timeout: std::time::Duration,
    ) -> Result<remote_agents::WorkspaceReportSetupLogsResponse, tonic::Status> {
        Err(tonic::Status::unavailable("remote agents not ready"))
    }
}

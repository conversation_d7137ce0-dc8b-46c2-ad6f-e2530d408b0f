package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/augmentcode/augment/services/remote_agents/server/ws"
	tenantwatcher "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

var (
	enableAutoPauseFlag  = featureflags.NewBoolFlag("remote_agents_enable_auto_pause", false)
	autoPauseSoftTTLFlag = featureflags.NewIntFlag("remote_agents_auto_pause_soft_ttl_minutes", 15)
	autoPauseHardTTLFlag = featureflags.NewIntFlag("remote_agents_auto_pause_hard_ttl_minutes", 24*60)
)

// RemoteAgentsCronConfig contains all the configuration and dependencies
// needed for the remote agents cron tasks.
type RemoteAgentsCronConfig struct {
	BigtableProxyClient bigtableproxy.BigtableProxyClient
	TenantWatcherClient tenantwatcher.TenantWatcherClient
	TokenExchangeClient tokenexchange.TokenExchangeClient
	WorkspaceController ws.RemoteWorkspaceController
	FeatureFlagHandle   featureflags.FeatureFlagHandle
	NumWorkers          int
	TableReadBatchSize  int
}

// CronTaskFunc defines the signature for functions executed by CronTask.
type CronTaskFunc func(ctx context.Context)

// CronTask is a generic task that executes a given function periodically.
// It implements the Task interface for SingletonJob.
type CronTask struct {
	name      string // Name of the cron task for logging
	mu        sync.Mutex
	isRunning bool
	ticker    *time.Ticker
	cancel    context.CancelFunc
	doneCh    chan struct{}
	taskFunc  CronTaskFunc  // The function to execute periodically
	interval  time.Duration // The interval for this specific task
}

// NewCronTask creates a new CronTask.
func NewCronTask(name string, interval time.Duration, taskFunc CronTaskFunc) *CronTask {
	if interval <= 0 {
		log.Fatal().Str("task_name", name).Dur("interval", interval).Msg("CronTask: Invalid or zero interval")
	}
	if taskFunc == nil {
		log.Fatal().Str("task_name", name).Msg("CronTask: taskFunc is nil")
	}
	return &CronTask{
		name:     name,
		interval: interval,
		taskFunc: taskFunc,
	}
}

// Start is called by SingletonJob when leadership is acquired.
// It starts a goroutine that periodically calls the configured taskFunc.
func (t *CronTask) Start(ctx context.Context) { // taskInterval (from SingletonJob) is ignored by this CronTask, which uses its own t.interval.
	t.mu.Lock()
	if t.isRunning {
		t.mu.Unlock()
		log.Ctx(ctx).Warn().Str("task_name", t.name).Msg("CronTask: Start called but already running")
		return
	}

	runCtx, cancel := context.WithCancel(ctx)
	t.cancel = cancel
	t.doneCh = make(chan struct{})
	t.isRunning = true
	t.mu.Unlock()

	// Log uses t.interval (the task's own configured interval)
	log.Ctx(ctx).Info().Str("task_name", t.name).Dur("configuredInterval", t.interval).Msg("CronTask: Starting with its configured interval")

	go func() {
		defer func() {
			log.Ctx(runCtx).Info().Str("task_name", t.name).Msg("CronTask: Goroutine stopping")
			if t.ticker != nil {
				t.ticker.Stop()
			}
			t.mu.Lock()
			t.isRunning = false
			t.cancel = nil
			t.mu.Unlock()
			close(t.doneCh)
		}()

		t.ticker = time.NewTicker(t.interval) // Use t.interval

		for {
			select {
			case <-runCtx.Done():
				log.Ctx(runCtx).Info().Str("task_name", t.name).Msg("CronTask: Context done, exiting run loop")
				return
			case <-t.ticker.C:
				log.Ctx(runCtx).Info().Str("task_name", t.name).Msg("CronTask: Ticker fired, executing taskFunc")
				t.taskFunc(runCtx)
			}
		}
	}()
}

// Stop is called by SingletonJob when leadership is lost or the job is stopped.
// It signals the task's goroutine to terminate.
func (t *CronTask) Stop() {
	t.mu.Lock()
	if !t.isRunning {
		t.mu.Unlock()
		return
	}

	log.Info().Str("task_name", t.name).Msg("CronTask: Attempting to stop...")
	t.cancel()
	t.mu.Unlock()

	if t.doneCh != nil {
		<-t.doneCh
		log.Info().Str("task_name", t.name).Msg("CronTask: Goroutine confirmed stopped.")
	}
	log.Info().Str("task_name", t.name).Msg("CronTask: Stopped")
}

func (t *CronTask) Name() string {
	return t.name
}

type remoteAgentCronTask struct {
	Agent               *AgentEntity
	TenantID            string
	TenantName          string
	RequestContext      *requestcontext.RequestContext
	WorkspaceController ws.RemoteWorkspaceController
}

func NewScanRemoteAgentsCronTask(config *RemoteAgentsCronConfig) CronTaskFunc {
	return func(ctx context.Context) {
		scanRemoteAgents(ctx, config)
	}
}

func scanRemoteAgents(ctx context.Context, config *RemoteAgentsCronConfig) error {
	startTime := time.Now()
	ctx = log.Ctx(ctx).With().Time("task_start_time", startTime).Logger().WithContext(ctx)

	// Map for tracking remote agents by tenant, agent status, and workspace status
	agentCounts := make(map[string]map[string]map[string]int) // tenant_id -> agent_status -> ws_status -> count

	// Helper function to increment agent count for given tenant, agent status, and workspace status
	incrementAgentCount := func(tenantID, agentStatus, wsStatus string) {
		if agentCounts[tenantID] == nil {
			agentCounts[tenantID] = make(map[string]map[string]int)
		}
		if agentCounts[tenantID][agentStatus] == nil {
			agentCounts[tenantID][agentStatus] = make(map[string]int)
		}
		agentCounts[tenantID][agentStatus][wsStatus]++
	}

	defer func() {
		RemoteAgentsScanJobRuns.Inc()
		duration := time.Since(startTime)
		RemoteAgentsScanJobDurationSeconds.Observe(duration.Seconds())
		for tenantID, statusCounts := range agentCounts {
			for agentStatus, wsStatusCounts := range statusCounts {
				for wsStatus, count := range wsStatusCounts {
					RemoteAgentsCronJobObservedTotal.WithLabelValues(tenantID, agentStatus, wsStatus).Set(float64(count))
				}
			}
		}
	}()

	log.Ctx(ctx).Info().Msg("CronTask: Scanning remote agents")
	// Get all tenants from tenant server with GetTenants
	tenants, err := config.TenantWatcherClient.GetTenants(ctx, "")
	if err != nil {
		log.Error().Err(err).Msg("Failed to get tenants")
		return err
	}

	// start a group of workers
	taskChan := make(chan remoteAgentCronTask, 10000)
	var wg sync.WaitGroup
	wg.Add(config.NumWorkers)
	for i := 0; i < config.NumWorkers; i++ {
		go remoteAgentTaskWorker(ctx, taskChan, config, &wg)
	}

	for _, tenant := range tenants {
		tenantCtx := log.Ctx(ctx).With().Str("tenant_id", tenant.Id).Str("tenant_name", tenant.Name).Logger().WithContext(ctx)
		// Get a service token from token exchange for this namespace and tenant
		token, err := config.TokenExchangeClient.GetSignedTokenForService(
			tenantCtx,
			tenant.Id,
			[]tokenexchangeproto.Scope{tokenexchangeproto.Scope_CONTENT_RW},
		)
		if err != nil {
			log.Error().Err(err).Str("tenant_id", tenant.Id).Msg("Failed to get service token")
			continue
		}

		// Pack the service token into a request context
		requestContext := requestcontext.New(
			requestcontext.RequestId(uuid.New().String()),
			requestcontext.RequestSessionId(uuid.New().String()),
			"remote-agents-cron",
			token,
		)
		lastStartRowKey := ""
		for {
			agentBatch, err := readRemoteAgentsBatchForTenant(tenantCtx, requestContext, config.BigtableProxyClient, tenant.Id, lastStartRowKey, config.TableReadBatchSize)
			if err != nil {
				log.Error().Err(err).Str("tenant_id", tenant.Id).Msg("Failed to read remote agents batch")
				break
			}
			if len(agentBatch) == 0 {
				break
			}
			fmt.Println("Read batch of", len(agentBatch), "agents")
			for _, agent := range agentBatch {
				agent := agent

				agentStatus := agent.Status.Status.String()
				wsStatus := agent.Status.WorkspaceStatus.String()

				incrementAgentCount(tenant.Id, agentStatus, wsStatus)

				taskChan <- remoteAgentCronTask{
					Agent:               agent,
					TenantID:            tenant.Id,
					TenantName:          tenant.Name,
					RequestContext:      requestContext,
					WorkspaceController: config.WorkspaceController,
				}
			}
			lastStartRowKey = agentBatch[len(agentBatch)-1].AgentID
		}
	}
	log.Ctx(ctx).Info().Msg("CronTask: Scan complete, closing task channel, waiting for workers to finish")
	close(taskChan)
	wg.Wait()
	log.Ctx(ctx).Info().Msg("CronTask: Scan complete, all workers finished")
	return nil
}

func remoteAgentTaskWorker(ctx context.Context, taskChan <-chan remoteAgentCronTask, config *RemoteAgentsCronConfig, wg *sync.WaitGroup) {
	defer wg.Done()
	for task := range taskChan {
		taskCtx := log.Ctx(ctx).With().Str("tenant_id", task.TenantID).Str("tenant_name", task.TenantName).Str("remote_agent_id", task.Agent.AgentID).Logger().WithContext(ctx)
		log.Ctx(taskCtx).Info().Msg("Processing remote agent")

		err := backgroundPauseAgent(taskCtx, task, config.BigtableProxyClient, config.WorkspaceController, config.FeatureFlagHandle)
		if err != nil {
			log.Ctx(taskCtx).Error().Err(err).Msg("Failed to pause agent")
		}
	}
}

func backgroundPauseAgent(ctx context.Context, task remoteAgentCronTask, bigtableProxyClient bigtableproxy.BigtableProxyClient, workspaceController ws.RemoteWorkspaceController, featureFlagHandle featureflags.FeatureFlagHandle) error {
	// Check if pause functionality is enabled via feature flag
	enablePause, err := enableAutoPauseFlag.Get(featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get enable pause feature flag")
	}
	agentEntity, err := readRemoteAgentEntity(ctx, task.RequestContext, bigtableProxyClient, task.TenantID, task.Agent.AgentID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent entity from BigTable")
		return err
	}
	autoPauseSoftTTL, err := autoPauseSoftTTLFlag.Get(featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get pause threshold feature flag")
	}
	autoPauseHardTTL, err := autoPauseHardTTLFlag.Get(featureFlagHandle)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get pause threshold feature flag")
	}
	// Pause:
	// Hard pausing TTL:
	now := nowFunc()
	autoPauseHardTTLThreshold := now.Add(-time.Duration(autoPauseHardTTL) * time.Minute)
	autoPauseSoftTTLThreshold := now.Add(-time.Duration(autoPauseSoftTTL) * time.Minute)
	if agentEntity.Status.LastUserUpdateReceivedAt.AsTime().Before(autoPauseHardTTLThreshold) &&
		agentEntity.Status.LastAgentUpdateReceivedAt.AsTime().Before(autoPauseHardTTLThreshold) &&
		(agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING || agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED) {

		log.Ctx(ctx).Info().
			Time("last_user_update_received_at", agentEntity.Status.LastUserUpdateReceivedAt.AsTime()).
			Time("last_agent_update_received_at", agentEntity.Status.LastAgentUpdateReceivedAt.AsTime()).
			Time("auto_pause_hard_ttl_threshold", autoPauseHardTTLThreshold).
			Time("now", now).
			Bool("enable_pause", enablePause).
			Msg("Auto pausing agent with hard ttl")
		if !enablePause {
			return nil
		}
		RemoteAgentsCronJobPausedTotal.WithLabelValues(task.TenantID, "hard").Inc()
		err = pauseAgentWorkspace(ctx, agentEntity, task.RequestContext, task.TenantID, workspaceController, bigtableProxyClient, "hard ttl (user, agent activity)")
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to pause agent")
			RemoteAgentsCronJobPauseErrors.WithLabelValues(task.TenantID, "hard").Inc()
		}
	} else if agentEntity.Status.LastUserUpdateReceivedAt.AsTime().Before(autoPauseSoftTTLThreshold) &&
		agentEntity.Status.LastAgentUpdateReceivedAt.AsTime().Before(autoPauseSoftTTLThreshold) &&
		(agentEntity.Status.LastSshActivityObservedAt == nil || agentEntity.Status.LastSshActivityObservedAt.AsTime().Before(autoPauseSoftTTLThreshold)) &&
		(agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING || agentEntity.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED) {

		log.Ctx(ctx).Info().
			Time("last_user_update_received_at", agentEntity.Status.LastUserUpdateReceivedAt.AsTime()).
			Time("last_agent_update_received_at", agentEntity.Status.LastAgentUpdateReceivedAt.AsTime()).
			Time("last_ssh_activity_observed_at", agentEntity.Status.LastSshActivityObservedAt.AsTime()).
			Time("auto_pause_soft_ttl_threshold", autoPauseSoftTTLThreshold).
			Time("now", now).
			Bool("enable_pause", enablePause).
			Msg("Auto pausing agent with soft ttl")
		if !enablePause {
			return nil
		}
		RemoteAgentsCronJobPausedTotal.WithLabelValues(task.TenantID, "soft").Inc()
		err = pauseAgentWorkspace(ctx, agentEntity, task.RequestContext, task.TenantID, workspaceController, bigtableProxyClient, "soft ttl (user, agent, ssh activity)")
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to pause agent")
			RemoteAgentsCronJobPauseErrors.WithLabelValues(task.TenantID, "soft").Inc()
		}
	}

	// TODO: auto delete

	// TODO: handle agent stuck in resuming and pausing states
	return nil
}

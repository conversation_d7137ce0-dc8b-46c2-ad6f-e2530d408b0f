package main

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Bigtable constants
	configFamilyName      = "Config"
	configColumn          = "config"
	statusFamilyName      = "Status"
	statusColumn          = "status"
	userMappingFamilyName = "UserMapping"
	userMappingColumn     = "mapping"
	outputFamilyName      = "Output"
	outputColumn          = "output"
	timestampFamilyName   = "Timestamp"
	timestampColumn       = "timestamp"
	logsColumn            = "logs"
	sequenceIDColumn      = "sequence_id"
)

type AgentEntity struct {
	// The orinal config anfd status value right after reading from bigtable
	// These non-exported values are used to verify the state transition and
	// can not be modified by the caller.
	configInStorage *entitiesproto.AgentConfig
	statusInStorage *entitiesproto.AgentStatus
	// Transaction predicate to compare against
	statusCell []byte

	Config  *entitiesproto.AgentConfig
	Status  *entitiesproto.AgentStatus
	AgentID string
}

func (a *AgentEntity) ToPublicAgent(expirationDays uint32) *remoteagentsproto.Agent {
	lastUpdate := a.Config.CreatedAt
	if a.Status.LastUserUpdateReceivedAt != nil {
		lastUpdate = a.Status.LastUserUpdateReceivedAt
	}
	if a.Status.LastAgentUpdateReceivedAt != nil && (a.Status.LastAgentUpdateReceivedAt.Seconds > lastUpdate.Seconds) {
		lastUpdate = a.Status.LastAgentUpdateReceivedAt
	}

	agent := &remoteagentsproto.Agent{
		RemoteAgentId:   a.AgentID,
		Status:          a.Status.Status,
		WorkspaceStatus: a.Status.WorkspaceStatus,
		Config:          a.Config.InputConfig,
		UpdatedAt:       lastUpdate,
		CreatedAt:       a.Config.CreatedAt,
		SshConfig:       a.Config.SshConfig,
		HasUpdates:      &a.Status.HasUpdates,
		ExpiresAt:       timestamppb.New(lastUpdate.AsTime().Add(time.Duration(expirationDays) * 24 * time.Hour)),
	}
	return agent
}

func getRemoteAgentRowRange(userID string) *bigtableproto.RowSet {
	startKey := []byte(fmt.Sprintf("RemoteAgent#%s#", userID))
	endKey := []byte(fmt.Sprintf("RemoteAgent#%s$", userID))
	return &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{{
			StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: startKey},
			EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: endKey},
		}},
	}
}

// New function that doesn't include userID in the row key
func getRemoteAgentRowKey(remoteAgentID string) []byte {
	rowKey := fmt.Sprintf("RemoteAgent#%s", remoteAgentID)
	return []byte(rowKey)
}

func getUserAgentMappingRowKey(userID string) []byte {
	rowKey := fmt.Sprintf("UserAgentMapping#%s", userID)
	return []byte(rowKey)
}

func getRemoteAgentChatHistoryRowKey(remoteAgentID string, sequenceID uint32) []byte {
	// left pad sequence ID with zeros to 20 digits to ensure lexicographic order
	// is also numeric order. uint32 goes up to 4.2 billion, so 10 digits is plenty.
	// 20 digits for forward compatibility to uint64
	rowKey := fmt.Sprintf("ExchangeHistory#%s#%020d", remoteAgentID, sequenceID)
	return []byte(rowKey)
}

// getRemoteAgentChatHistoryRowRange returns a row range for all chat history rows for a specific agent
func getRemoteAgentChatHistoryRowRange(remoteAgentID string, startingSequenceId uint32) *bigtableproto.RowSet {
	startKey := []byte(fmt.Sprintf("ExchangeHistory#%s#%020d", remoteAgentID, startingSequenceId))
	endKey := []byte(fmt.Sprintf("ExchangeHistory#%s$", remoteAgentID))
	return &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{{
			StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: startKey},
			EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: endKey},
		}},
	}
}

func getRemoteAgentUpdateSequenceIDKey(remoteAgentID string) []byte {
	rowKey := fmt.Sprintf("UpdateSequenceID#%s", remoteAgentID)
	return []byte(rowKey)
}

func getRemoteAgentPendingUpdatesRowKey(remoteAgentID string, sequenceID uint32) []byte {
	// left pad sequence ID with zeros to 20 digits to ensure lexicographic order
	// is also numeric order. uint32 goes up to 4.2 billion, so 10 digits is plenty.
	// 20 digits for forward compatibility to uint64
	rowKey := fmt.Sprintf("PendingUpdates#%s#%020d", remoteAgentID, sequenceID)
	return []byte(rowKey)
}

func getRemoteAgentPendingUpdatesRowRange(remoteAgentID string) *bigtableproto.RowSet {
	startKey := []byte(fmt.Sprintf("PendingUpdates#%s#", remoteAgentID))
	endKey := []byte(fmt.Sprintf("PendingUpdates#%s$", remoteAgentID))
	return &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{{
			StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: startKey},
			EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: endKey},
		}},
	}
}

func getRemoteAgentWorkspaceLogsRowKey(remoteAgentID string, numSteps uint32, lastSequenceID uint32) []byte {
	// left pad sequence ID with zeros to 20 digits to ensure lexicographic order
	// is also numeric order. uint32 goes up to 4.2 billion, so 10 digits is plenty.
	// 20 digits for forward compatibility to uint64
	rowKey := fmt.Sprintf("WorkspaceLogs#%s#%010d#%020d", remoteAgentID, numSteps, lastSequenceID)
	return []byte(rowKey)
}

func getRemoteAgentWorkspaceLogsRowRange(remoteAgentID string, lastProcessedStep uint32, lastProcessedSequenceId uint32) *bigtableproto.RowSet {
	startKey := []byte(fmt.Sprintf("WorkspaceLogs#%s#%010d#%020d", remoteAgentID, lastProcessedStep, lastProcessedSequenceId))
	endKey := []byte(fmt.Sprintf("WorkspaceLogs#%s$", remoteAgentID))
	return &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{{
			StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: startKey},
			EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: endKey},
		}},
	}
}

func agentIDFromRowKey(rowKey []byte) (string, error) {
	parts := strings.Split(string(rowKey), "#")
	if len(parts) != 3 || parts[0] != "RemoteAgent" {
		// Try the new format
		return agentIDFromNewRowKey(rowKey)
	}
	return parts[2], nil
}

// New function to extract agent ID from the new row key format
func agentIDFromNewRowKey(rowKey []byte) (string, error) {
	parts := strings.Split(string(rowKey), "#")
	if len(parts) != 2 || parts[0] != "RemoteAgent" {
		return "", fmt.Errorf("Invalid row key: %s", rowKey)
	}
	return parts[1], nil
}

// writeRemoteAgentAtCreation atomically checks if agent exists and creates it if not, using CheckAndMutateRow.
// This ensures duplication safety with deterministic agent IDs, but is not truly idempotent.
// Also updates the user-to-agent mapping and initializes the update sequence ID.
func writeRemoteAgentAtCreation(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, config *remoteagentsproto.AgentConfig, namespace string) error {
	// Create timestamp for creation time
	createdAt := timestamppb.Now()

	configProto := &entitiesproto.AgentConfig{
		InputConfig: config,
		UserId:      userId,
		CreatedAt:   createdAt,
		Namespace:   namespace,
	}
	statusProto := &entitiesproto.AgentStatus{
		// TODO: Update initial status to Pending
		Status:                   remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING,
		WorkspaceStatus:          remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
		HasUpdates:               false,
		LastUserUpdateReceivedAt: timestamppb.New(nowFunc().In(time.UTC)),
	}
	configProtoValue, err := proto.Marshal(configProto)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal agent config")
		return errors.New("Failed to marshal agent config")
	}
	statusProtoValue, err := proto.Marshal(statusProto)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal agent status")
		return errors.New("Failed to marshal agent status")
	}

	// Create a predicate filter that checks if the row doesn't exist
	// We use a filter that will match if the row exists, and then apply mutations only if it doesn't match
	// This is effectively checking for row non-existence
	predicateFilter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(configFamilyName),
		bigtableproxy.ColumnFilter(configColumn),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	// Create mutations for the false case (when row doesn't exist)
	falseMutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      configFamilyName,
					ColumnQualifier: []byte(configColumn),
					Value:           configProtoValue,
				},
			},
		},
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      statusFamilyName,
					ColumnQualifier: []byte(statusColumn),
					Value:           statusProtoValue,
				},
			},
		},
	}

	// Perform the check-and-mutate operation
	resp, err := bigtableProxyClient.CheckAndMutateRow(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		getRemoteAgentRowKey(agentId),
		predicateFilter,
		nil, // No mutations for the true case (when row exists)
		falseMutations,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to check and mutate agent config in BigTable")
		return err
	}

	// If the predicate matched, it means the row already exists
	if resp.PredicateMatched {
		log.Ctx(ctx).Warn().Msgf("Agent with ID %s already exists, not creating a new one", agentId)
		return status.Error(codes.AlreadyExists, "Agent already exists")
	}

	// Update the user agent mapping
	_, retryErr := retryWithExponentialBackoff[any](ctx, maxExponentialBackoffAttempts, func() (any, error) {
		err := writeUserAgentMapping(ctx, requestContext, bigtableProxyClient, tenantID, userId, agentId)
		return nil, err
	})
	if retryErr != nil {
		log.Ctx(ctx).Error().Err(retryErr).Msg("Failed to update user agent mapping after retries")
		return retryErr
	}

	// Initialize the update sequence ID
	err = writeInitialUpdateSequenceID(ctx, requestContext, bigtableProxyClient, tenantID, agentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to initialize update sequence ID")
		return err
	}

	return nil
}

// ValidateStateTransition validates the state transition from the current (agent_status, workspace_status) to the new
// (agent_status, workspace_status)
func (a *AgentEntity) ValidateStateTransition() error {
	newWorkspaceStatus := a.Status.GetWorkspaceStatus()
	oldWorkspaceStatus := a.statusInStorage.GetWorkspaceStatus()
	if oldWorkspaceStatus == newWorkspaceStatus {
		return nil
	}
	switch oldWorkspaceStatus {
	case remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING:
		if newWorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING {
			return fmt.Errorf("invalid workspace status transition from %s to %s", oldWorkspaceStatus, newWorkspaceStatus)
		}
		break
	case remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING:
		if newWorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED {
			return fmt.Errorf("invalid workspace status transition from %s to %s", oldWorkspaceStatus, newWorkspaceStatus)
		}
		break
	case remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED:
		if newWorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING {
			return fmt.Errorf("invalid workspace status transition from %s to %s", oldWorkspaceStatus, newWorkspaceStatus)
		}
		break
	case remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING:
		if newWorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING {
			return fmt.Errorf("invalid workspace status transition from %s to %s", oldWorkspaceStatus, newWorkspaceStatus)
		}
		break
	case remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED:
		if newWorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING {
			return fmt.Errorf("invalid workspace status transition from %s to %s", oldWorkspaceStatus, newWorkspaceStatus)
		}
		break
	default:
		return fmt.Errorf("unhandled workspace status transition from %s to %s", oldWorkspaceStatus, newWorkspaceStatus)
	}
	// validate agent status, we should only accept a subset of statues
	// status paused, pausing should only be converted and delivered to public
	newAgentStatus := a.Status.GetStatus()
	switch newAgentStatus {
	case remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED:
		return nil
	default:
		return fmt.Errorf("invalid agent status transition to %s", newAgentStatus)
	}
}

// AtomicSave atomically saves the agent entity with transactional assertion on status column
func (a *AgentEntity) AtomicSave(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID string) error {
	if a.statusCell == nil || a.configInStorage == nil || a.statusInStorage == nil {
		return errors.New("status cell is not set, likely this object is already saved")
	}
	// validate state transition
	if err := a.ValidateStateTransition(); err != nil {
		return err
	}
	// Create a predicate filter that checks if both config and status match
	var predicateFilter *bigtableproto.RowFilter
	predicateFilter = bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(statusFamilyName),
		bigtableproxy.ColumnFilter(statusColumn),
		bigtableproxy.LatestNFilter(1),
		bigtableproxy.ValueFilter(regexp.QuoteMeta(string(a.statusCell))),
	).Proto()

	// Create mutations for the true case (when predicate matches)
	trueMutations := []*bigtableproto.Mutation{}
	updatedAgentValue, err := proto.Marshal(a.Config)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal updated agent config")
		return errors.New("Failed to marshal updated agent config")
	}
	trueMutations = append(trueMutations, &bigtableproto.Mutation{
		Mutation: &bigtableproto.Mutation_SetCell_{
			SetCell: &bigtableproto.Mutation_SetCell{
				FamilyName:      configFamilyName,
				ColumnQualifier: []byte(configColumn),
				Value:           updatedAgentValue,
			},
		},
	})
	updatedStatusValue, err := proto.Marshal(a.Status)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal updated agent status")
		return errors.New("Failed to marshal updated agent status")
	}
	trueMutations = append(trueMutations, &bigtableproto.Mutation{
		Mutation: &bigtableproto.Mutation_SetCell_{
			SetCell: &bigtableproto.Mutation_SetCell{
				FamilyName:      statusFamilyName,
				ColumnQualifier: []byte(statusColumn),
				Value:           updatedStatusValue,
			},
		},
	})

	if len(trueMutations) == 0 {
		return fmt.Errorf("no updated agent config or status provided for atomic update")
	}

	// Perform the check-and-mutate operation
	resp, err := bigtableProxyClient.CheckAndMutateRow(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		getRemoteAgentRowKey(a.AgentID),
		predicateFilter,
		trueMutations,
		nil, // No mutations for the false case (when predicate doesn't match)
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to update agent status in BigTable")
		return err
	}

	// If the predicate didn't match, it means the values were modified concurrently
	if !resp.PredicateMatched {
		log.Ctx(ctx).Warn().Msg("Agent config or status was modified concurrently")
		return fmt.Errorf("%w; %w", retriableErr, status.Errorf(codes.Aborted, "agent config or status was modified concurrently"))
	}
	// destroy the AgentEntity to make sure it's not savable again
	a.configInStorage = nil
	a.statusInStorage = nil
	a.statusCell = nil
	return nil
}

// readRemoteAgent reads the remote agent in public API format
func readRemoteAgent(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, expirationDays uint32) (*remoteagentsproto.Agent, error) {
	agentEntity, err := readRemoteAgentEntity(ctx, requestContext, bigtableProxyClient, tenantID, agentId)
	if err != nil {
		return nil, err
	}

	return agentEntity.ToPublicAgent(expirationDays), nil
}

// readRemoteAgent reads both config and status from bigtable and returns a complete Agent object
func readRemoteAgentEntity(ctx context.Context, requestContext *requestcontext.RequestContext,
	bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, agentId string,
) (*AgentEntity, error) {
	// Read both config and status from bigtable
	filter := bigtableproxy.LatestNFilter(1)
	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{getRemoteAgentRowKey(agentId)},
		},
		filter.Proto(),
		0,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read agent from BigTable")
		return nil, err
	}

	// Check if we have any rows
	if len(rows) == 0 {
		log.Ctx(ctx).Warn().Msg("No agent found in BigTable")
		err = fmt.Errorf("agent not found")
		return nil, err
	}
	var configProto *entitiesproto.AgentConfig
	var statusProto *entitiesproto.AgentStatus
	var rawStatusCellValue []byte

	// Process the row to extract config and status
	for _, cell := range rows[0].Cells {
		cellValue := cell.Value
		if cell.FamilyName == configFamilyName && string(cell.Qualifier) == configColumn {
			configProto = &entitiesproto.AgentConfig{}
			if err := proto.Unmarshal(cell.Value, configProto); err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal agent config")
				return nil, errors.New("Failed to unmarshal agent config")
			}
		} else if cell.FamilyName == statusFamilyName && string(cell.Qualifier) == statusColumn {
			rawStatusCellValue = cellValue
			statusProto = &entitiesproto.AgentStatus{}
			if err := proto.Unmarshal(cell.Value, statusProto); err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal agent status")
				return nil, errors.New("Failed to unmarshal agent status")
			}
		}
	}

	// Check if we have both config and status
	if configProto == nil {
		log.Ctx(ctx).Warn().Msg("No agent config found in BigTable row")
		return nil, err
	}

	if statusProto == nil {
		log.Ctx(ctx).Warn().Msg("No agent status found in BigTable row")
		err = fmt.Errorf("agent status not found")
		return nil, err
	}

	agentEntity := &AgentEntity{
		// deepcopy config and status
		configInStorage: proto.Clone(configProto).(*entitiesproto.AgentConfig),
		statusInStorage: proto.Clone(statusProto).(*entitiesproto.AgentStatus),

		Config:     configProto,
		Status:     statusProto,
		AgentID:    agentId,
		statusCell: rawStatusCellValue,
	}
	return agentEntity, nil
}

// writeUserAgentMapping adds an agent ID to the user's agent mapping.
// Atomically handles concurrent updates using CheckAndMutate. Idempotent.
func writeUserAgentMapping(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string) error {
	// First, try to read the existing mapping to check if it exists
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(userMappingFamilyName),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{getUserAgentMappingRowKey(userId)},
		},
		filter,
		0,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read user agent mapping from BigTable")
		return err
	}

	// If no mapping exists yet, create a new one with just this agent ID
	if len(rows) == 0 {
		// Create a new mapping with just this agent ID
		mappingProto := &entitiesproto.UserAgentMapping{
			AgentIds: []string{agentId},
		}
		protoValue, err := proto.Marshal(mappingProto)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal user agent mapping")
			return errors.New("Failed to marshal user agent mapping")
		}

		// Create a predicate filter that checks if the row exists
		// We'll use a filter that matches if the row exists, and apply mutations only if it doesn't match
		predicateFilter := bigtableproxy.ChainFilters(
			bigtableproxy.FamilyFilter(userMappingFamilyName),
			bigtableproxy.ColumnFilter(userMappingColumn),
			bigtableproxy.LatestNFilter(1),
		).Proto()

		// Create mutations for the false case (when row doesn't exist)
		falseMutations := []*bigtableproto.Mutation{
			{
				Mutation: &bigtableproto.Mutation_SetCell_{
					SetCell: &bigtableproto.Mutation_SetCell{
						FamilyName:      userMappingFamilyName,
						ColumnQualifier: []byte(userMappingColumn),
						Value:           protoValue,
					},
				},
			},
		}

		// Perform the check-and-mutate operation
		resp, err := bigtableProxyClient.CheckAndMutateRow(
			ctx,
			tenantID,
			proxyproto.TableName_REMOTE_AGENTS,
			getUserAgentMappingRowKey(userId),
			predicateFilter,
			nil, // No mutations for the true case (when row exists)
			falseMutations,
			requestContext,
		)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to write user agent mapping to BigTable")
			return err
		}
		// If the predicate matched, it means the row already exists
		// This is unexpected since we already checked that the row doesn't exist
		// But it could happen if another request created the row concurrently
		if resp.PredicateMatched {
			log.Ctx(ctx).Warn().Msg("User agent mapping was created concurrently, retrying")
			return fmt.Errorf("%w; %w", retriableErr, status.Errorf(codes.AlreadyExists, "user agent mapping was created concurrently"))
		}

		return nil
	}

	// Mapping exists, extract the current agent IDs
	row := rows[0]
	mappingProto := &entitiesproto.UserAgentMapping{}
	var currentValue []byte
	for _, cell := range row.Cells {
		if cell.FamilyName == userMappingFamilyName && string(cell.Qualifier) == userMappingColumn {
			currentValue = cell.Value
			if err := proto.Unmarshal(cell.Value, mappingProto); err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal user agent mapping")
				return err
			}
			break
		}
	}

	// Check if the agent ID is already in the list
	found := false
	for _, id := range mappingProto.AgentIds {
		if id == agentId {
			found = true
			break
		}
	}

	// If agent ID is already in the list, no need to update
	if found {
		return nil
	}

	// Create a predicate filter that checks if the current value matches exactly
	// This ensures atomicity - we only update if the value hasn't changed since we read it
	predicateFilter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(userMappingFamilyName),
		bigtableproxy.ColumnFilter(userMappingColumn),
		bigtableproxy.LatestNFilter(1),
		// Use a direct string comparison
		bigtableproxy.ValueFilter(regexp.QuoteMeta(string(currentValue))),
	).Proto()

	// Create the updated mapping with the new agent ID added
	updatedAgentIds := append(mappingProto.AgentIds, agentId)
	updatedMappingProto := &entitiesproto.UserAgentMapping{
		AgentIds: updatedAgentIds,
	}
	updatedValue, err := proto.Marshal(updatedMappingProto)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal updated user agent mapping")
		return errors.New("Failed to marshal updated user agent mapping")
	}

	// Create mutations for the true case (when predicate matches)
	trueMutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      userMappingFamilyName,
					ColumnQualifier: []byte(userMappingColumn),
					Value:           updatedValue,
				},
			},
		},
	}

	// Perform the check-and-mutate operation
	resp, err := bigtableProxyClient.CheckAndMutateRow(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		getUserAgentMappingRowKey(userId),
		predicateFilter,
		trueMutations,
		nil, // No mutations for the false case (when predicate doesn't match)
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to update user agent mapping in BigTable")
		return err
	}

	// If the predicate didn't match, it means the value was modified concurrently
	// We need to retry the operation
	if !resp.PredicateMatched {
		log.Ctx(ctx).Warn().Msg("User agent mapping was modified concurrently, retrying")
		return fmt.Errorf("%w; %w", retriableErr, status.Errorf(codes.Aborted, "user agent mapping was modified concurrently"))
	}

	return nil
}

// readUserAgentMapping reads the list of agent IDs for a user
func readUserAgentMapping(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId string) ([]string, error) {
	// Read the mapping from bigtable
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(userMappingFamilyName),
		bigtableproxy.LatestNFilter(1),
	)

	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{getUserAgentMappingRowKey(userId)},
		},
		filter.Proto(),
		0,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read user agent mapping from BigTable")
		return nil, err
	}
	// If no mapping exists yet, return an empty list
	if len(rows) == 0 {
		return []string{}, nil
	}
	if len(rows) > 1 {
		log.Ctx(ctx).Error().Msgf("Expected exactly one row, got %d", len(rows))
		return nil, fmt.Errorf("Expected exactly one row, got %d", len(rows))
	}

	row := rows[0]
	mappingProto := &entitiesproto.UserAgentMapping{}
	for _, cell := range row.Cells {
		if cell.FamilyName != userMappingFamilyName || string(cell.Qualifier) != userMappingColumn {
			continue
		}
		if err := proto.Unmarshal(cell.Value, mappingProto); err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal user agent mapping")
			return nil, err
		}
		break
	}

	return mappingProto.AgentIds, nil
}

// readAllRemoteAgentsForUser reads both config and status from bigtable for all agents
// and returns a map of complete Agent objects
func readAllRemoteAgentsForUser(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId string, expirationDays uint32) (map[string]*remoteagentsproto.Agent, error) {
	// First, try to get the agent IDs from the user mapping
	agentIds, err := readUserAgentMapping(ctx, requestContext, bigtableProxyClient, tenantID, userId)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to read user agent mapping")
		return nil, err
	}

	// If we have agent IDs from the mapping, read each agent
	agents := make(map[string]*remoteagentsproto.Agent)
	if len(agentIds) > 0 {
		// Read all the agents one by one
		// We could optimize this to read in batches, but for simplicity we'll read one at a time
		for _, agentId := range agentIds {
			agent, err := readRemoteAgent(ctx, requestContext, bigtableProxyClient, tenantID, userId, agentId, expirationDays)
			if err != nil {
				log.Ctx(ctx).Warn().Err(err).Msgf("Failed to read agent %s, skipping", agentId)
				continue
			}
			agents[agentId] = agent
		}
	}
	return agents, nil
}

// readRemoteAgentsBatchForTenant reads a batch of remote agents
// will read from startRemoteAgentIDOpen (exclusive) to the end of the table or pageSize, whichever is smaller
func readRemoteAgentsBatchForTenant(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID string, startRemoteAgentIDNonInclusive string, pageSize int) ([]*AgentEntity, error) {
	// Define the row range
	var rowSet *bigtableproto.RowSet
	rowSet = &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{{
			EndKey: &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("RemoteAgent$")},
		}},
	}
	if startRemoteAgentIDNonInclusive == "" {
		// If startRowKey is empty, read from the beginning of the table
		rowSet.RowRanges[0].StartKey = &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("RemoteAgent#")}
	} else {
		startKey := getRemoteAgentRowKey(startRemoteAgentIDNonInclusive)
		rowSet.RowRanges[0].StartKey = &bigtableproto.RowRange_StartKeyOpen{StartKeyOpen: []byte(startKey)}
	}
	// Read rows from bigtable
	filter := bigtableproxy.LatestNFilter(1)
	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		rowSet,
		filter.Proto(),
		int64(pageSize),
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read remote agents batch from BigTable")
		return nil, err
	}

	agents := make([]*AgentEntity, 0, len(rows))
	for _, row := range rows {
		agentId, err := agentIDFromNewRowKey(row.RowKey)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("rowKey", string(row.RowKey)).Msg("Failed to parse agent ID from row key, skipping row")
			continue
		}

		var configProto *entitiesproto.AgentConfig
		var statusProto *entitiesproto.AgentStatus
		var statusCell []byte

		for _, cell := range row.Cells {
			if cell.FamilyName == configFamilyName && string(cell.Qualifier) == configColumn {
				configProto = &entitiesproto.AgentConfig{}
				if err := proto.Unmarshal(cell.Value, configProto); err != nil {
					log.Ctx(ctx).Error().Err(err).Str("agentId", agentId).Msg("Failed to unmarshal agent config")
					// Skip this agent if config is unmarshalable
					configProto = nil
					break
				}
			} else if cell.FamilyName == statusFamilyName && string(cell.Qualifier) == statusColumn {
				statusCell = cell.Value
				statusProto = &entitiesproto.AgentStatus{}
				if err := proto.Unmarshal(cell.Value, statusProto); err != nil {
					log.Ctx(ctx).Error().Err(err).Str("agentId", agentId).Msg("Failed to unmarshal agent status")
					// Skip this agent if status is unmarshalable
					statusProto = nil
					break
				}
			}
		}

		if configProto == nil || statusProto == nil {
			log.Ctx(ctx).Warn().Str("agentId", agentId).Msg("Missing config or status for agent, skipping")
			continue
		}

		agents = append(agents, &AgentEntity{
			Config:     configProto,
			Status:     statusProto,
			AgentID:    agentId,
			statusCell: statusCell,
		})
	}

	return agents, nil
}

func deleteRemoteAgent(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string) error {
	// Delete the agent config from bigtable using the new row key format
	_, err := bigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentRowKey(agentId),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromRow_{
							DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
						},
					},
				},
			},
		},
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete agent config from BigTable")
		return err
	}

	// Remove the agent ID from the user mapping
	// First, read the current mapping to get both the agent IDs and the current value for atomic update
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(userMappingFamilyName),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{getUserAgentMappingRowKey(userId)},
		},
		filter,
		0,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to read user agent mapping for deletion, ignoring")
	} else if len(rows) == 0 {
		log.Ctx(ctx).Warn().Msg("No user agent mapping found for deletion, ignoring")
	} else {
		// Extract the current mapping and raw value for atomic comparison
		mappingProto := &entitiesproto.UserAgentMapping{}
		var currentValue []byte
		for _, cell := range rows[0].Cells {
			if cell.FamilyName == userMappingFamilyName && string(cell.Qualifier) == userMappingColumn {
				currentValue = cell.Value
				if err := proto.Unmarshal(cell.Value, mappingProto); err != nil {
					log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal user agent mapping for deletion")
					break
				}
			}
		}

		if currentValue != nil {
			// Create updated list without the agent ID
			updatedAgentIds := make([]string, 0, len(mappingProto.AgentIds))
			for _, id := range mappingProto.AgentIds {
				if id != agentId {
					updatedAgentIds = append(updatedAgentIds, id)
				}
			}

			// If the list changed, update it
			if len(updatedAgentIds) != len(mappingProto.AgentIds) {
				updatedMappingProto := &entitiesproto.UserAgentMapping{
					AgentIds: updatedAgentIds,
				}
				protoValue, err := proto.Marshal(updatedMappingProto)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal user agent mapping for deletion")
				} else {
					predicateFilter := bigtableproxy.ChainFilters(
						bigtableproxy.FamilyFilter(userMappingFamilyName),
						bigtableproxy.ColumnFilter(userMappingColumn),
						bigtableproxy.LatestNFilter(1),
						// Use a direct string comparison with the current value
						bigtableproxy.ValueFilter(regexp.QuoteMeta(string(currentValue))),
					).Proto()

					trueMutations := []*bigtableproto.Mutation{
						{
							Mutation: &bigtableproto.Mutation_SetCell_{
								SetCell: &bigtableproto.Mutation_SetCell{
									FamilyName:      userMappingFamilyName,
									ColumnQualifier: []byte(userMappingColumn),
									Value:           protoValue,
								},
							},
						},
					}

					resp, err := bigtableProxyClient.CheckAndMutateRow(
						ctx,
						tenantID,
						proxyproto.TableName_REMOTE_AGENTS,
						getUserAgentMappingRowKey(userId),
						predicateFilter,
						trueMutations,
						nil, // No mutations for the false case (when predicate doesn't match)
						requestContext,
					)

					if err == nil && !resp.PredicateMatched {
						log.Ctx(ctx).Warn().Msg("User agent mapping was modified concurrently, retrying")
						return fmt.Errorf("%w; %w", retriableErr, status.Errorf(codes.Aborted, "user agent mapping was modified concurrently"))
					}

					if err != nil {
						// Deletion is best effort
						log.Ctx(ctx).Error().Err(err).Msg("Failed to update user agent mapping for deletion")
					}
				}
			}
		}
	}

	// Delete the update sequence ID row
	_, err = bigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentUpdateSequenceIDKey(agentId),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromRow_{
							DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
						},
					},
				},
			},
		},
		requestContext,
	)
	if err != nil {
		// Deletion is best effort
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete update sequence ID during deletion")
	}

	// Delete the chat history rows
	historyRowRange := getRemoteAgentChatHistoryRowRange(agentId, 0)
	historyRows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		historyRowRange,
		nil,
		0,
		requestContext,
	)
	if err != nil {
		// Deletion is best effort
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read chat history rows for deletion")
	} else {
		entries := make([]*bigtableproto.MutateRowsRequest_Entry, 0, len(historyRows))
		for _, row := range historyRows {
			entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
				RowKey: row.RowKey,
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromRow_{
							DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
						},
					},
				},
			})
		}

		_, err = bigtableProxyClient.MutateRows(
			ctx,
			tenantID,
			proxyproto.TableName_REMOTE_AGENTS,
			entries,
			requestContext,
		)
		if err != nil {
			// Deletion is best effort
			log.Ctx(ctx).Error().Err(err).Msg("Failed to delete chat history rows during deletion")
		}
	}

	// Delete workspace logs
	logsRowRange := getRemoteAgentWorkspaceLogsRowRange(agentId, 0, 0)
	logsRows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		logsRowRange,
		nil,
		0,
		requestContext,
	)
	if err != nil {
		// Deletion is best effort
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read workspace logs rows for deletion")
	} else {
		entries := make([]*bigtableproto.MutateRowsRequest_Entry, 0, len(logsRows))
		for _, row := range logsRows {
			entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
				RowKey: row.RowKey,
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromRow_{
							DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
						},
					},
				},
			})
		}

		_, err = bigtableProxyClient.MutateRows(
			ctx,
			tenantID,
			proxyproto.TableName_REMOTE_AGENTS,
			entries,
			requestContext,
		)
		if err != nil {
			// Deletion is best effort
			log.Ctx(ctx).Error().Err(err).Msg("Failed to delete workspace logs rows during deletion")
		}
	}

	// Delete update sequence ID row
	_, err = bigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentUpdateSequenceIDKey(agentId),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromRow_{
							DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
						},
					},
				},
			},
		},
		requestContext,
	)
	if err != nil {
		// Deletion is best effort
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete update sequence ID during deletion")
	}

	return nil
}

// writeChatHistoryChunk writes a list of ChatHistoryExchange objects to BigTable
// The exchanges are stored in the Output column family with the exchange_history row key format
// Each exchange is identified by its sequence_id
// Uses CheckAndMutateRow to prevent race conditions with timestamps stored in the Timestamp column family
func writeChatHistoryChunk(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, chatHistory []*remoteagentsproto.ChatHistoryExchange) error {
	for _, exchange := range chatHistory {
		sequenceID := exchange.SequenceId

		if exchange.FinishedAt == nil {
			log.Ctx(ctx).Error().Uint32("sequenceID", sequenceID).Msg("Exchange is missing a finished_at timestamp")
			return fmt.Errorf("exchange %d is missing a finished_at timestamp", sequenceID)
		}

		rowKey := getRemoteAgentChatHistoryRowKey(agentId, sequenceID)

		newOutputProto := &entitiesproto.AgentOutput{
			Exchange: []*remoteagentsproto.ChatHistoryExchange{exchange},
		}
		newSerializedExchangePayload, err := proto.Marshal(newOutputProto)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Uint32("sequenceID", sequenceID).Msg("Failed to marshal new chat history exchange")
			return fmt.Errorf("failed to marshal new chat history exchange for sequence %d: %w", sequenceID, err)
		}

		// Convert finished_at timestamp to RFC3339 string for storage
		// RFC3339 format maintains chronological order when compared lexicographically
		finishedAtString := exchange.FinishedAt.AsTime().Format(time.RFC3339Nano)

		// Read existing rows - this reads all columns
		readFilter := bigtableproxy.LatestNFilter(1).Proto()

		rows, err := bigtableProxyClient.ReadRows(
			ctx,
			tenantID,
			proxyproto.TableName_REMOTE_AGENTS,
			&bigtableproto.RowSet{RowKeys: [][]byte{rowKey}},
			readFilter,
			0, // No row limit needed, we are reading a single key
			requestContext,
		)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Uint32("sequenceID", sequenceID).Msg("Failed to read existing chat history exchange")
			return fmt.Errorf("failed to read existing chat history for sequence %d: %w", sequenceID, err)
		}

		initialReadRowExists := len(rows) > 0 && len(rows[0].Cells) > 0

		var existingTimestampValue []byte
		var predicateFilterProto *bigtableproto.RowFilter
		var trueMutations, falseMutations []*bigtableproto.Mutation

		// Create mutations for both output and timestamp columns
		outputMutation := &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      outputFamilyName,
					ColumnQualifier: []byte(outputColumn),
					Value:           newSerializedExchangePayload,
				},
			},
		}

		timestampMutation := &bigtableproto.Mutation{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      timestampFamilyName,
					ColumnQualifier: []byte(timestampColumn),
					Value:           []byte(finishedAtString),
				},
			},
		}

		if !initialReadRowExists {
			// Case 1: No existing row
			// If the row doesn't exist, CheckAndMutateRow's predicate_filter won't match (as there's no row to match against).
			// So, falseMutations will be applied.
			predicateFilterProto = bigtableproxy.PassAllFilter().Proto()
			trueMutations = nil
			falseMutations = []*bigtableproto.Mutation{outputMutation, timestampMutation}
		} else {
			// Case 2: Existing timestamp found
			foundTimestampCell := false
			for _, cell := range rows[0].Cells {
				if cell.FamilyName == timestampFamilyName && string(cell.Qualifier) == timestampColumn {
					existingTimestampValue = cell.Value
					foundTimestampCell = true
					break // We only care about the timestamp column
				}
			}

			// Backward compatibility: if timestamp column doesn't exist, write the new data
			// TODO(mike): Remove this backward compatibility logic after migration is complete
			if !foundTimestampCell {
				predicateFilterProto = bigtableproxy.ChainFilters(
					bigtableproxy.FamilyFilter(timestampFamilyName),
					bigtableproxy.ColumnFilter(regexp.QuoteMeta(timestampColumn)),
					bigtableproxy.LatestNFilter(1),
				).Proto()
				trueMutations = nil
				falseMutations = []*bigtableproto.Mutation{outputMutation, timestampMutation}
			} else {
				existingTimestampString := string(existingTimestampValue)
				// Compare timestamps lexicographically (RFC3339 format maintains chronological order)
				if finishedAtString <= existingTimestampString {
					continue // Skip to the next exchange
				}

				// New data is newer, prepare to update if timestamp is unchanged
				predicateFilterProto = bigtableproxy.ChainFilters(
					bigtableproxy.FamilyFilter(timestampFamilyName),
					bigtableproxy.ColumnFilter(regexp.QuoteMeta(timestampColumn)),
					bigtableproxy.ValueFilter(regexp.QuoteMeta(existingTimestampString)),
					bigtableproxy.LatestNFilter(1),
				).Proto()
				trueMutations = []*bigtableproto.Mutation{outputMutation, timestampMutation}
				falseMutations = nil
			}
		}

		resp, err := bigtableProxyClient.CheckAndMutateRow(
			ctx,
			tenantID,
			proxyproto.TableName_REMOTE_AGENTS,
			rowKey,
			predicateFilterProto,
			trueMutations,
			falseMutations,
			requestContext,
		)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Uint32("sequenceID", sequenceID).Msg("Failed to CheckAndMutateRow for chat history exchange")
			return retriableErr
		}

		// case 1: Row did not exist initially
		// We intended to create a new row (falseMutations were set)
		if !initialReadRowExists {
			if resp.PredicateMatched {
				// Unexpected: Row didn't exist initially, but predicate matched.
				// This implies the row was created between our ReadRows and CheckAndMutateRow.
				// Our falseMutations (the write) were NOT applied. This is a concurrency issue.
				log.Ctx(ctx).Warn().Uint32("sequenceID", sequenceID).Msg("Chat history exchange was modified concurrently (row created), write skipped. Retrying.")
				return retriableErr
			}
		} else {
			// case 2: Row existed initially
			// We intended to operate on an existing row (initialReadRowExisted was true)
			if len(trueMutations) > 0 && !resp.PredicateMatched {
				// Case 2.1: Intended to update an existing, valid, older row
				// Predicate (ValueFilter) did NOT match. Row was changed concurrently.
				log.Ctx(ctx).Warn().Uint32("sequenceID", sequenceID).Msg("Chat history exchange was modified concurrently (value changed), update skipped. Retrying.")
				return retriableErr
			} else if len(falseMutations) > 0 && resp.PredicateMatched {
				// Case 2.2: Backwards compatibility: Intended to insert a value for an existing row without a timestamp
				// Predicate (ValueFilter) DID match. Row was changed concurrently.
				log.Ctx(ctx).Warn().Uint32("sequenceID", sequenceID).Msg("Chat history exchange was modified concurrently (value changed for backwards compat), update skipped. Retrying.")
				return retriableErr
			}
		}
	}
	return nil
}

// readChatHistory reads chat history exchanges for a specific agent with pagination support
func readChatHistory(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, agentId string, pageSize uint32, lastProcessedSequenceId uint32) ([]*remoteagentsproto.ChatHistoryExchange, error) {
	// Read all chat history from bigtable
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.LatestNFilter(1),
	)

	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		// Skip all rows up to and including lastProcessedSequenceId
		getRemoteAgentChatHistoryRowRange(agentId, lastProcessedSequenceId+1),
		filter.Proto(),
		int64(pageSize),
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read chat history from BigTable")
		return nil, err
	}

	// Collect all exchanges
	allExchanges := make([]*remoteagentsproto.ChatHistoryExchange, 0)
	for _, row := range rows {
		for _, cell := range row.Cells {
			if cell.FamilyName != outputFamilyName || string(cell.Qualifier) != outputColumn {
				continue
			}

			output := &entitiesproto.AgentOutput{}
			if err := proto.Unmarshal(cell.Value, output); err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal chat history")
				continue
			}

			allExchanges = append(allExchanges, output.Exchange...)

			// Verify sequence IDs are greater than or equal to lastProcessedSequenceId
			for _, exchange := range output.Exchange {
				if exchange.SequenceId < lastProcessedSequenceId {
					log.Ctx(ctx).Error().Msgf(
						"Failed to unmarshal chat history: sequence ID is less than last processed sequence ID. Expected >= %d, got %d",
						lastProcessedSequenceId,
						exchange.SequenceId,
					)
				}
			}
		}
	}

	// Sort exchanges by sequence ID
	sort.Slice(allExchanges, func(i, j int) bool {
		return allExchanges[i].SequenceId < allExchanges[j].SequenceId
	})

	return allExchanges, nil
}

// writeAgentInterrupt is a convenience function to write an interrupt update to BigTable
func writeAgentInterrupt(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, requestId uuid.UUID) error {
	// Create a WorkspacePollUpdateResponse with an interrupt
	pendingUpdate := &remoteagentsproto.WorkspacePollUpdateResponse{
		Updates: []*remoteagentsproto.WorkspaceUpdate{
			{
				Update: &remoteagentsproto.WorkspaceUpdate_Interrupt{
					Interrupt: &remoteagentsproto.WorkspaceInterruptRequest{},
				},
			},
		},
	}

	// Write the update to BigTable
	return writePendingAgentUpdate(ctx, requestContext, bigtableProxyClient, tenantID, userId, agentId, pendingUpdate, requestId)
}

// writeAgentChatRequest is a convenience function to write a chat request update to BigTable
func writeAgentChatRequest(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, requestDetails *remoteagentsproto.ChatRequestDetails, requestId uuid.UUID) error {
	pendingUpdate := &remoteagentsproto.WorkspacePollUpdateResponse{
		Updates: []*remoteagentsproto.WorkspaceUpdate{
			{
				Update: &remoteagentsproto.WorkspaceUpdate_ChatRequest{
					ChatRequest: &remoteagentsproto.WorkspaceChatRequest{
						RequestDetails: requestDetails,
					},
				},
			},
		},
	}

	// Write the update to BigTable
	return writePendingAgentUpdate(ctx, requestContext, bigtableProxyClient, tenantID, userId, agentId, pendingUpdate, requestId)
}

// writePendingAgentUpdate writes a pending update (like an interrupt) to BigTable
// The update is stored with the pending_updates row key format and includes a request_id for ordering and deduplication
// TODO(mike): fix in next PR - use request_id for idempotency
func writePendingAgentUpdate(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, update *remoteagentsproto.WorkspacePollUpdateResponse, requestId uuid.UUID) error {
	// Get a new sequence ID for this update
	seqID, err := retryWithExponentialBackoff[uint32](ctx, maxExponentialBackoffAttempts, func() (uint32, error) {
		return readAndIncrementUpdateSequenceID(ctx, requestContext, bigtableProxyClient, tenantID, agentId)
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get sequence ID for pending agent update")
		return err
	}

	// Create a single WorkspaceUpdate from the WorkspacePollUpdateResponse
	var workspaceUpdate *remoteagentsproto.WorkspaceUpdate
	if len(update.Updates) > 0 {
		workspaceUpdate = update.Updates[0]
		// Set the sequence ID for this update
		workspaceUpdate.SequenceId = seqID
	} else {
		log.Ctx(ctx).Warn().Msg("No updates found in WorkspacePollUpdateResponse")
		return errors.New("No updates found in WorkspacePollUpdateResponse")
	}

	pendingUpdate := &entitiesproto.PendingAgentUpdate{
		SequenceId: seqID,
		Update:     workspaceUpdate,
	}
	protoValue, err := proto.Marshal(pendingUpdate)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal pending agent update")
		return errors.New("Failed to marshal pending agent update")
	}
	_, err = bigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentPendingUpdatesRowKey(agentId, seqID),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_SetCell_{
							SetCell: &bigtableproto.Mutation_SetCell{
								FamilyName:      outputFamilyName,
								ColumnQualifier: []byte(outputColumn),
								Value:           protoValue,
							},
						},
					},
				},
			},
		},
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write pending agent update to BigTable")
		return err
	}

	return nil
}

// deletePendingAgentUpdate deletes a specific pending update from BigTable
func deletePendingAgentUpdate(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, sequenceID uint32) error {
	_, err := bigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentPendingUpdatesRowKey(agentId, sequenceID),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromRow_{
							DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
						},
					},
				},
			},
		},
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to delete pending agent update from BigTable")
		return err
	}

	return nil
}

// readPendingAgentUpdates reads all pending updates for a specific agent
func readPendingAgentUpdates(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string) ([]*entitiesproto.PendingAgentUpdate, error) {
	// Read all pending updates from bigtable
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.LatestNFilter(1),
	)

	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		getRemoteAgentPendingUpdatesRowRange(agentId),
		filter.Proto(),
		0,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read pending agent updates from BigTable")
		return nil, err
	}

	// Collect all updates
	allUpdates := make([]*entitiesproto.PendingAgentUpdate, 0)
	for _, row := range rows {
		for _, cell := range row.Cells {
			if cell.FamilyName != outputFamilyName || string(cell.Qualifier) != outputColumn {
				continue
			}

			pendingUpdate := &entitiesproto.PendingAgentUpdate{}
			if err := proto.Unmarshal(cell.Value, pendingUpdate); err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal pending agent update")
				continue
			}

			allUpdates = append(allUpdates, pendingUpdate)
		}
	}

	// Sort updates by sequence ID
	sort.Slice(allUpdates, func(i, j int) bool {
		return allUpdates[i].SequenceId < allUpdates[j].SequenceId
	})

	return allUpdates, nil
}

// readAndIncrementUpdateSequenceID reads the current sequence ID for an agent and atomically increments it
// Returns the new sequence ID and nil if successful, or 0 and an error if unsuccessful
func readAndIncrementUpdateSequenceID(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, agentID string) (uint32, error) {
	// First, try to read the current sequence ID
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.LatestNFilter(1),
	).Proto()
	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{getRemoteAgentUpdateSequenceIDKey(agentID)},
		},
		filter,
		0,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to read sequence ID for agent %s", agentID)
		return 0, err
	}
	// If no rows
	if len(rows) != 1 {
		log.Ctx(ctx).Error().Msgf("Race condition risk! No sequence ID initialized for agent %s, creating one", agentID)
		return 0, errors.New("No sequence ID initialized for agent")
	}

	// Extract the current sequence ID from the row
	var currentSeqID uint32
	var currentValue []byte
	for _, cell := range rows[0].Cells {
		if cell.FamilyName == outputFamilyName && string(cell.Qualifier) == sequenceIDColumn {
			// Unmarshal the proto
			seqIDProto := &entitiesproto.UpdateSequenceID{}
			if err := proto.Unmarshal(cell.Value, seqIDProto); err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal sequence ID")
				return 0, errors.New("Failed to unmarshal sequence ID")
			}
			currentSeqID = seqIDProto.Value
			currentValue = cell.Value
			break
		}
	}

	// Check if we have a valid current value
	// Create predicate filter to check if the current value matches exactly
	// This is critical for concurrency - we need to ensure the value hasn't changed
	// since we read it, otherwise we'd overwrite another process's update
	predicateFilter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.ColumnFilter(sequenceIDColumn),
		bigtableproxy.LatestNFilter(1),
		// Use a direct string comparison
		bigtableproxy.ValueFilter(regexp.QuoteMeta(string(currentValue))),
	).Proto()

	newSeqID := currentSeqID + 1
	newSeqIDProto := &entitiesproto.UpdateSequenceID{
		Value: newSeqID,
	}
	newValue, err := proto.Marshal(newSeqIDProto)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal new sequence ID")
		return 0, errors.New("Failed to marshal new sequence ID")
	}

	// Create mutations for true case (when predicate matches)
	// Use a cleaner approach with explicit timestamp
	trueMut := &bigtableproto.Mutation{
		Mutation: &bigtableproto.Mutation_SetCell_{
			SetCell: &bigtableproto.Mutation_SetCell{
				FamilyName:      outputFamilyName,
				ColumnQualifier: []byte(sequenceIDColumn),
				Value:           newValue,
			},
		},
	}
	trueMutations := []*bigtableproto.Mutation{trueMut}
	// Perform the check-and-mutate operation
	resp, err := bigtableProxyClient.CheckAndMutateRow(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		getRemoteAgentUpdateSequenceIDKey(agentID),
		predicateFilter,
		trueMutations,
		nil, // No mutations for the false case (when predicate doesn't match)
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to update sequence ID for agent %s", agentID)
		return 0, err
	}

	// Check if the predicate matched and the mutation was applied
	if !resp.PredicateMatched {
		log.Ctx(ctx).Warn().Msgf("Sequence ID for agent %s was modified concurrently", agentID)
		return 0, errors.New("sequence ID was modified concurrently")
	}

	return newSeqID, nil
}

// writeInitialUpdateSequenceID writes a sequence ID of 0 for a new agent
// this function is idempotent and can be called multiple times safely
func writeInitialUpdateSequenceID(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, agentID string) error {
	// Create the sequence ID proto with initial value 0
	seqIDProto := &entitiesproto.UpdateSequenceID{
		Value: 0,
	}
	protoValue, err := proto.Marshal(seqIDProto)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal sequence ID")
		return errors.New("Failed to marshal sequence ID")
	}

	// Create a predicate filter that checks if the row exists
	// We'll use a filter that matches if the row exists, and apply mutations only if it doesn't match
	predicateFilter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.ColumnFilter(sequenceIDColumn),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	// No mutations for the true case (when row exists)
	trueMutations := []*bigtableproto.Mutation{}

	// Create mutations for the false case (when row doesn't exist)
	falseMutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      outputFamilyName,
					ColumnQualifier: []byte(sequenceIDColumn),
					Value:           protoValue,
				},
			},
		},
	}

	// Perform the check-and-mutate operation
	resp, err := bigtableProxyClient.CheckAndMutateRow(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		getRemoteAgentUpdateSequenceIDKey(agentID),
		predicateFilter,
		trueMutations,
		falseMutations,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write initial sequence ID to BigTable")
		return err
	}

	// If the predicate matched, it means the row already exists
	// This is not an error condition, as it could happen during concurrent operations
	if resp.PredicateMatched {
		log.Ctx(ctx).Info().Msgf("Sequence ID for agent %s was already initialized", agentID)
	}

	return nil
}

// The incrementStep flag is used to determine if we should increment the step number
// This helps us offset the step number by 1 (may increase in the future) to account for the "Container Setup Status" step
func writeRemoteAgentLogs(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, userId, agentId string, workspaceSetupStatus *remoteagentsproto.WorkspaceSetupStatus, incrementStep bool) error {
	// for each step, write a row with just that step
	mutations := make([]*bigtableproto.MutateRowsRequest_Entry, 0, len(workspaceSetupStatus.Steps))
	for _, step := range workspaceSetupStatus.Steps {
		if incrementStep {
			step.StepNumber++
		}
		protoValue, err := proto.Marshal(step)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal workspace step")
			return errors.New("Failed to marshal workspace step")
		}

		mutations = append(mutations, &bigtableproto.MutateRowsRequest_Entry{
			RowKey: getRemoteAgentWorkspaceLogsRowKey(agentId, step.StepNumber, step.SequenceId),
			Mutations: []*bigtableproto.Mutation{
				{
					Mutation: &bigtableproto.Mutation_SetCell_{
						SetCell: &bigtableproto.Mutation_SetCell{
							FamilyName:      outputFamilyName,
							ColumnQualifier: []byte(logsColumn),
							Value:           protoValue,
						},
					},
				},
			},
		})
	}

	_, err := bigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		mutations,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to write workspace logs to BigTable")
		return err
	}

	return nil
}

func readRemoteAgentWorkspaceLogs(ctx context.Context, requestContext *requestcontext.RequestContext, bigtableProxyClient bigtableproxy.BigtableProxyClient, tenantID, agentId string, lastProcessedStep, lastProcessedSequenceId uint32) (*remoteagentsproto.WorkspaceLogsResponse, error) {
	// Read all logs from bigtable
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.LatestNFilter(1),
	)
	rows, err := bigtableProxyClient.ReadRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		getRemoteAgentWorkspaceLogsRowRange(agentId, lastProcessedStep, lastProcessedSequenceId),
		filter.Proto(),
		0,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read workspace logs from BigTable")
		return nil, err
	}
	stepsToSend := make([]*remoteagentsproto.WorkspaceSetupStep, 0)
	len_logs := 0
	for _, row := range rows {
		for _, cell := range row.Cells {
			if cell.FamilyName != outputFamilyName || string(cell.Qualifier) != logsColumn {
				continue
			}

			// Try to unmarshal as the new WorkspaceSetupStep format first
			step := &remoteagentsproto.WorkspaceSetupStep{}
			if err := proto.Unmarshal(cell.Value, step); err == nil {
				// The first step is always "Container Setup Status". This is a hack to mark it as success once we have more steps
				// TODO (remove): Temporary till we can grab more information from VM
				if step.StepNumber == 0 && len(rows) > 1 {
					step.Status = remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_SUCCESS
				}

				// Successfully unmarshaled as the new format
				len_logs += len(step.Logs)
				// Apply log size limit - if logs exceed maxLogChars, then do not add this Step
				if len_logs > maxLogChars {
					return &remoteagentsproto.WorkspaceLogsResponse{
						RemoteAgentId: agentId,
						WorkspaceSetupStatus: &remoteagentsproto.WorkspaceSetupStatus{
							Steps: stepsToSend,
						},
					}, nil
				}

				stepsToSend = append(stepsToSend, step)
			} else {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal workspace logs")
			}
		}
	}

	return &remoteagentsproto.WorkspaceLogsResponse{
		RemoteAgentId: agentId,
		WorkspaceSetupStatus: &remoteagentsproto.WorkspaceSetupStatus{
			Steps: stepsToSend,
		},
	}, nil
}

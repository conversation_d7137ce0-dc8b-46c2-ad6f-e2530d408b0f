package main

import (
	"context"
	"fmt"
	"sync/atomic"
	"testing"
	"time"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
	wsmock "github.com/augmentcode/augment/services/remote_agents/server/ws/mocks"
	tenantwatchermocks "github.com/augmentcode/augment/services/tenant_watcher/client/mocks"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangemocks "github.com/augmentcode/augment/services/token_exchange/client/mocks"
	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

func TestNewCronTask_NotStartedYet(t *testing.T) {
	taskFunc := func(ctx context.Context) {}
	task := NewCronTask("test-task", 5*time.Second, taskFunc)

	assert.Equal(t, "test-task", task.name)
	assert.Equal(t, 5*time.Second, task.interval)
	assert.NotNil(t, task.taskFunc)
	assert.False(t, task.isRunning)
}

func TestCronTaskStart(t *testing.T) {
	// Create a context with a short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// Create a counter to track how many times the task function is called
	var counter int32

	// Create a task function that increments the counter
	taskFunc := func(ctx context.Context) {
		atomic.AddInt32(&counter, 1)
	}

	// Create a CronTask with a short interval
	task := NewCronTask("test-task", 10*time.Millisecond, taskFunc)
	task.Start(ctx)
	// Wait for the context to timeout
	<-ctx.Done()
	task.Stop()
	assert.GreaterOrEqual(t, atomic.LoadInt32(&counter), int32(5))
}

func TestCronTaskStop(t *testing.T) {
	// Create a context
	ctx := context.Background()
	called := make(chan struct{})

	// Create a task function that signals when it's called
	taskFunc := func(ctx context.Context) {
		select {
		case called <- struct{}{}:
		default:
		}
	}

	// Create a CronTask with a short interval
	task := NewCronTask("test-task", 100*time.Millisecond, taskFunc)
	task.Start(ctx)
	// Wait for the task function to be called
	select {
	case <-called:
	case <-time.After(1 * time.Second):
		t.Fatal("Task function was not called within timeout")
	}

	task.Stop()
	assert.False(t, task.isRunning)
}

func TestCronTaskStartAlreadyRunning(t *testing.T) {
	// Create a context
	ctx := context.Background()

	// Create a task function
	taskFunc := func(ctx context.Context) {}
	// Create a CronTask
	task := NewCronTask("test-task", 100*time.Millisecond, taskFunc)
	// Start 2 times
	task.Start(ctx)
	task.Start(ctx)

	task.Stop()
	assert.False(t, task.isRunning)
}

func TestCronTaskStopNotRunning(t *testing.T) {
	// Create a task function
	taskFunc := func(ctx context.Context) {}

	// Create a CronTask
	task := NewCronTask("test-task", 100*time.Millisecond, taskFunc)

	// Stop the task without starting it
	task.Stop()
}

func TestScanRemoteAgents_Metrics(t *testing.T) {
	deleteAllRowsInTable(context.Background(), "test-tenant-1", requestcontext.New(
		requestcontext.RequestId(uuid.New().String()),
		requestcontext.RequestSessionId("test-session-id"),
		"test-runner-metrics",
		secretstring.New("fake-token-t1"),
	))
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTenantWatcher := tenantwatchermocks.NewMockTenantWatcherClient(ctrl)
	mockTokenExchange := tokenexchangemocks.NewMockTokenExchangeClient(ctrl)

	// Prepare dummy data
	tenant1ID := "test-tenant-1"
	tenant2ID := "test-tenant-2"
	dummyTenants := []*tenantproto.Tenant{
		{Id: tenant1ID},
		{Id: tenant2ID},
	}

	// Mock calls
	mockTenantWatcher.EXPECT().GetTenants(gomock.Any(), "").Return(dummyTenants, nil)

	mockTokenExchange.EXPECT().GetSignedTokenForService(gomock.Any(), tenant1ID, gomock.Any()).Return(secretstring.New("fake-token-t1"), nil).AnyTimes()
	mockTokenExchange.EXPECT().GetSignedTokenForService(gomock.Any(), tenant2ID, gomock.Any()).Return(secretstring.New("fake-token-t2"), nil).AnyTimes()

	agentConfig := createTestAgentConfig()
	numAgents := 99

	for i := 0; i < numAgents/2; i++ {
		reqCtx := requestcontext.New(
			requestcontext.RequestId(uuid.New().String()),
			requestcontext.RequestSessionId("test-session-id"),
			"test-runner-metrics",
			secretstring.New("fake-token-t1"),
		)
		agentID := fmt.Sprintf("test-agent-%d", i)
		err := writeRemoteAgentAtCreation(ctx, reqCtx, realBigtableProxyClient, tenant1ID, "user1", agentID, agentConfig, "test-namespace")
		require.NoError(t, err, "Failed to write agent %d", i)
	}
	for i := numAgents / 2; i < numAgents; i++ {
		reqCtx := requestcontext.New(
			requestcontext.RequestId(uuid.New().String()),
			requestcontext.RequestSessionId("test-session-id"),
			"test-runner-metrics",
			secretstring.New("fake-token-t2"),
		)
		agentID := fmt.Sprintf("test-agent-%d", i)
		err := writeRemoteAgentAtCreation(ctx, reqCtx, realBigtableProxyClient, tenant2ID, "user2", agentID, agentConfig, "test-namespace")
		require.NoError(t, err, "Failed to write agent %d", i)
	}

	// Create config for the test
	mockFeatureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
	config := &RemoteAgentsCronConfig{
		BigtableProxyClient: realBigtableProxyClient,
		TenantWatcherClient: mockTenantWatcher,
		TokenExchangeClient: mockTokenExchange,
		WorkspaceController: nil,                   // Not needed for this test
		FeatureFlagHandle:   mockFeatureFlagHandle, // Needed to prevent nil pointer dereference
		NumWorkers:          20,
		TableReadBatchSize:  5000,
	}

	err := scanRemoteAgents(ctx, config)
	assert.NoError(t, err)

	// Verify the new observed agents metric
	// All agents created with writeRemoteAgentAtCreation have AGENT_STATUS_STARTING by default
	expectedAgentStatus := remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING.String()
	// All agents created with writeRemoteAgentAtCreation have WORKSPACE_STATUS_RUNNING by default
	expectedWorkspaceStatus := remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING.String()

	// Verify tenant1 metric
	tenant1Count := testutil.ToFloat64(RemoteAgentsCronJobObservedTotal.WithLabelValues(tenant1ID, expectedAgentStatus, expectedWorkspaceStatus))
	assert.Equal(t, float64(numAgents/2), tenant1Count, "Expected %d agents for tenant1", numAgents/2)

	// Verify tenant2 metric
	tenant2Count := testutil.ToFloat64(RemoteAgentsCronJobObservedTotal.WithLabelValues(tenant2ID, expectedAgentStatus, expectedWorkspaceStatus))
	assert.Equal(t, float64(numAgents-numAgents/2), tenant2Count, "Expected %d agents for tenant2", numAgents-numAgents/2)
}

// createAgentFixture creates an agent in BigTable with the specified state
func createAgentFixture(t *testing.T, testCtx testContext, lastUserUpdate, lastAgentUpdate time.Time, agentStatus remoteagentsproto.AgentStatus, workspaceStatus remoteagentsproto.WorkspaceStatus) {
	// Create agent config
	configEntity := &entitiesproto.AgentConfig{
		InputConfig: createTestAgentConfig(),
		UserId:      testCtx.UserID,
		CreatedAt:   timestamppb.Now(),
	}
	configData, err := proto.Marshal(configEntity)
	require.NoError(t, err)

	// Create agent status
	statusEntity := &entitiesproto.AgentStatus{
		Status:                    agentStatus,
		WorkspaceStatus:           workspaceStatus,
		LastUserUpdateReceivedAt:  timestamppb.New(lastUserUpdate),
		LastAgentUpdateReceivedAt: timestamppb.New(lastAgentUpdate),
	}
	statusData, err := proto.Marshal(statusEntity)
	require.NoError(t, err)

	// Create mutations to write the agent
	mutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      configFamilyName,
					ColumnQualifier: []byte("config"),
					Value:           configData,
				},
			},
		},
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      statusFamilyName,
					ColumnQualifier: []byte("status"),
					Value:           statusData,
				},
			},
		},
	}

	// Write the agent to BigTable
	rowKey := fmt.Sprintf("RemoteAgent#%s", testCtx.AgentID)
	entries := []*bigtableproto.MutateRowsRequest_Entry{
		{
			RowKey:    []byte(rowKey),
			Mutations: mutations,
		},
	}
	_, err = realBigtableProxyClient.MutateRows(
		testCtx.Ctx,
		testCtx.TenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		entries,
		testCtx.RequestCtx,
	)
	require.NoError(t, err)
}

// TestBackgroundPauseAgent tests the backgroundPauseAgent function with various scenarios
func TestBackgroundPauseAgent(t *testing.T) {
	// Mock the time function to make the test deterministic
	fixedTime := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	originalNowFunc := nowFunc
	nowFunc = func() time.Time { return fixedTime }
	defer func() { nowFunc = originalNowFunc }()

	// Setup test context
	testCtx := setupTestParameters()
	defer deleteAllRowsInTable(testCtx.Ctx, testCtx.TenantID, testCtx.RequestCtx)

	// Create feature flag handle
	mockFeatureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	t.Run("FeatureFlagDisabled", func(t *testing.T) {
		// Create an agent that would normally be paused (inactive for 20 minutes)
		inactiveTime := fixedTime.Add(-20 * time.Minute)
		createAgentFixture(t, testCtx, inactiveTime, inactiveTime, remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING)

		// Read the agent to create the task
		agentEntity, err := readRemoteAgentEntity(testCtx.Ctx, testCtx.RequestCtx, realBigtableProxyClient, testCtx.TenantID, testCtx.AgentID)
		require.NoError(t, err)

		task := remoteAgentCronTask{
			Agent:          agentEntity,
			TenantID:       testCtx.TenantID,
			RequestContext: testCtx.RequestCtx,
		}

		// Get initial metric value before pausing
		initialPausedCount := testutil.ToFloat64(RemoteAgentsCronJobPausedTotal.WithLabelValues(testCtx.TenantID, "soft"))
		initialErrorCount := testutil.ToFloat64(RemoteAgentsCronJobPauseErrors.WithLabelValues(testCtx.TenantID, "soft"))

		// Call backgroundPauseAgent with feature flag disabled (default)
		err = backgroundPauseAgent(testCtx.Ctx, task, realBigtableProxyClient, nil, mockFeatureFlagHandle)

		// Should return nil (no error) and not attempt to pause since feature flag is disabled
		assert.NoError(t, err)

		// Verify agent is still in RUNNING state
		updatedAgent, err := readRemoteAgentEntity(testCtx.Ctx, testCtx.RequestCtx, realBigtableProxyClient, testCtx.TenantID, testCtx.AgentID)
		require.NoError(t, err)
		assert.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, updatedAgent.Status.WorkspaceStatus)

		// Verify the pause metric was not incremented (since feature flag is disabled)
		finalPausedCount := testutil.ToFloat64(RemoteAgentsCronJobPausedTotal.WithLabelValues(testCtx.TenantID, "soft"))
		assert.Equal(t, initialPausedCount, finalPausedCount, "Expected pause metric to remain unchanged")

		// Verify the error metric was not incremented
		finalErrorCount := testutil.ToFloat64(RemoteAgentsCronJobPauseErrors.WithLabelValues(testCtx.TenantID, "soft"))
		assert.Equal(t, initialErrorCount, finalErrorCount, "Expected pause error metric to remain unchanged")
	})

	t.Run("FeatureFlagEnabled_AgentInactive_RealPause", func(t *testing.T) {
		// Enable the feature flag
		mockFeatureFlagHandle.Set("remote_agents_enable_auto_pause", true)

		// Create an agent that is inactive (last update 20 minutes ago, exceeds 15-minute threshold)
		inactiveTime := fixedTime.Add(-20 * time.Minute)
		createAgentFixture(t, testCtx, inactiveTime, inactiveTime, remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING)

		// Read the agent to create the task
		agentEntity, err := readRemoteAgentEntity(testCtx.Ctx, testCtx.RequestCtx, realBigtableProxyClient, testCtx.TenantID, testCtx.AgentID)
		require.NoError(t, err)

		task := remoteAgentCronTask{
			Agent:          agentEntity,
			TenantID:       testCtx.TenantID,
			RequestContext: testCtx.RequestCtx,
		}

		// Get initial metric value before pausing
		initialPausedCount := testutil.ToFloat64(RemoteAgentsCronJobPausedTotal.WithLabelValues(testCtx.TenantID, "soft"))
		initialErrorCount := testutil.ToFloat64(RemoteAgentsCronJobPauseErrors.WithLabelValues(testCtx.TenantID, "soft"))

		// Create a mock workspace controller
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockWorkspaceController := wsmock.NewMockRemoteWorkspaceController(ctrl)

		// Expect StopWorkspace to be called with the correct parameters
		mockWorkspaceController.EXPECT().
			StopWorkspace(gomock.Any(), testCtx.UserID, testCtx.AgentID).
			Return(nil).
			Times(1)

		// Call backgroundPauseAgent with the mock workspace controller
		err = backgroundPauseAgent(testCtx.Ctx, task, realBigtableProxyClient, mockWorkspaceController, mockFeatureFlagHandle)

		// Should return nil (no error) since pause was successful
		assert.NoError(t, err)

		// Verify agent transitioned to PAUSED state
		updatedAgent, err := readRemoteAgentEntity(testCtx.Ctx, testCtx.RequestCtx, realBigtableProxyClient, testCtx.TenantID, testCtx.AgentID)
		require.NoError(t, err)
		assert.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, updatedAgent.Status.WorkspaceStatus)

		// Verify the pause metric was incremented (since pause was attempted)
		finalPausedCount := testutil.ToFloat64(RemoteAgentsCronJobPausedTotal.WithLabelValues(testCtx.TenantID, "soft"))
		assert.Equal(t, initialPausedCount+1, finalPausedCount, "Expected pause metric to be incremented by 1")

		// Verify the error metric was not incremented (since pause was successful)
		finalErrorCount := testutil.ToFloat64(RemoteAgentsCronJobPauseErrors.WithLabelValues(testCtx.TenantID, "soft"))
		assert.Equal(t, initialErrorCount, finalErrorCount, "Expected pause error metric to remain unchanged")
	})
}

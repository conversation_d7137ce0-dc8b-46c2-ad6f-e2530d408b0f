package main

import (
	"testing"

	"github.com/stretchr/testify/assert"

	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
)

func TestWorkspaceProcessor_ProcessWorkspaceUpdates(t *testing.T) {
	tests := []struct {
		name                    string
		lastProcessedSequenceId uint32
		pendingUpdates          []*entitiesproto.PendingAgentUpdate
		expectedUpdates         []*remoteagentsproto.WorkspaceStreamUpdate
	}{
		{
			name:                    "No pending updates",
			lastProcessedSequenceId: 0,
			pendingUpdates:          []*entitiesproto.PendingAgentUpdate{},
			expectedUpdates:         []*remoteagentsproto.WorkspaceStreamUpdate{},
		},
		{
			name:                    "One pending interrupt update",
			lastProcessedSequenceId: 0,
			pendingUpdates: []*entitiesproto.PendingAgentUpdate{
				{
					SequenceId: 1,
					Update: &remoteagentsproto.WorkspaceUpdate{
						SequenceId: 1,
						Update: &remoteagentsproto.WorkspaceUpdate_Interrupt{
							Interrupt: &remoteagentsproto.WorkspaceInterruptRequest{},
						},
					},
				},
			},
			expectedUpdates: []*remoteagentsproto.WorkspaceStreamUpdate{
				{
					Type:       remoteagentsproto.WorkspaceUpdateType_WORKSPACE_UPDATE_INTERRUPT,
					SequenceId: 1,
					Interrupt:  &remoteagentsproto.WorkspaceInterruptRequest{},
				},
			},
		},
		{
			name:                    "One pending chat request update",
			lastProcessedSequenceId: 0,
			pendingUpdates: []*entitiesproto.PendingAgentUpdate{
				{
					SequenceId: 1,
					Update: &remoteagentsproto.WorkspaceUpdate{
						SequenceId: 1,
						Update: &remoteagentsproto.WorkspaceUpdate_ChatRequest{
							ChatRequest: &remoteagentsproto.WorkspaceChatRequest{
								RequestDetails: &remoteagentsproto.ChatRequestDetails{
									UserGuidelines: stringPtr("test guidelines"),
								},
							},
						},
					},
				},
			},
			expectedUpdates: []*remoteagentsproto.WorkspaceStreamUpdate{
				{
					Type:       remoteagentsproto.WorkspaceUpdateType_WORKSPACE_UPDATE_CHAT_REQUEST,
					SequenceId: 1,
					ChatRequest: &remoteagentsproto.WorkspaceChatRequest{
						RequestDetails: &remoteagentsproto.ChatRequestDetails{
							UserGuidelines: stringPtr("test guidelines"),
						},
					},
				},
			},
		},
		{
			name:                    "Multiple pending updates",
			lastProcessedSequenceId: 0,
			pendingUpdates: []*entitiesproto.PendingAgentUpdate{
				{
					SequenceId: 1,
					Update: &remoteagentsproto.WorkspaceUpdate{
						SequenceId: 1,
						Update: &remoteagentsproto.WorkspaceUpdate_Interrupt{
							Interrupt: &remoteagentsproto.WorkspaceInterruptRequest{},
						},
					},
				},
				{
					SequenceId: 2,
					Update: &remoteagentsproto.WorkspaceUpdate{
						SequenceId: 2,
						Update: &remoteagentsproto.WorkspaceUpdate_ChatRequest{
							ChatRequest: &remoteagentsproto.WorkspaceChatRequest{
								RequestDetails: &remoteagentsproto.ChatRequestDetails{
									UserGuidelines: stringPtr("test guidelines"),
								},
							},
						},
					},
				},
			},
			expectedUpdates: []*remoteagentsproto.WorkspaceStreamUpdate{
				{
					Type:       remoteagentsproto.WorkspaceUpdateType_WORKSPACE_UPDATE_INTERRUPT,
					SequenceId: 1,
					Interrupt:  &remoteagentsproto.WorkspaceInterruptRequest{},
				},
				{
					Type:       remoteagentsproto.WorkspaceUpdateType_WORKSPACE_UPDATE_CHAT_REQUEST,
					SequenceId: 2,
					ChatRequest: &remoteagentsproto.WorkspaceChatRequest{
						RequestDetails: &remoteagentsproto.ChatRequestDetails{
							UserGuidelines: stringPtr("test guidelines"),
						},
					},
				},
			},
		},
		{
			name:                    "Skip already processed updates",
			lastProcessedSequenceId: 1,
			pendingUpdates: []*entitiesproto.PendingAgentUpdate{
				{
					SequenceId: 1,
					Update: &remoteagentsproto.WorkspaceUpdate{
						SequenceId: 1,
						Update: &remoteagentsproto.WorkspaceUpdate_Interrupt{
							Interrupt: &remoteagentsproto.WorkspaceInterruptRequest{},
						},
					},
				},
				{
					SequenceId: 2,
					Update: &remoteagentsproto.WorkspaceUpdate{
						SequenceId: 2,
						Update: &remoteagentsproto.WorkspaceUpdate_ChatRequest{
							ChatRequest: &remoteagentsproto.WorkspaceChatRequest{
								RequestDetails: &remoteagentsproto.ChatRequestDetails{
									UserGuidelines: stringPtr("test guidelines"),
								},
							},
						},
					},
				},
			},
			expectedUpdates: []*remoteagentsproto.WorkspaceStreamUpdate{
				{
					Type:       remoteagentsproto.WorkspaceUpdateType_WORKSPACE_UPDATE_CHAT_REQUEST,
					SequenceId: 2,
					ChatRequest: &remoteagentsproto.WorkspaceChatRequest{
						RequestDetails: &remoteagentsproto.ChatRequestDetails{
							UserGuidelines: stringPtr("test guidelines"),
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor := NewWorkspaceProcessor(tt.lastProcessedSequenceId)
			updates := processor.ProcessWorkspaceUpdates(tt.pendingUpdates)
			if len(tt.expectedUpdates) == 0 {
				assert.Empty(t, updates)
			} else {
				assert.Equal(t, tt.expectedUpdates, updates)
			}
		})
	}
}

func stringPtr(s string) *string {
	return &s
}

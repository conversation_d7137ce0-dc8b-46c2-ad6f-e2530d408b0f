// Package main implements the Remote Agents server functionality.
//
// This file contains the WorkspaceStreamer, which is responsible for streaming
// workspace updates to clients. It implements a server-side streaming gRPC endpoint
// that efficiently delivers updates as they become available.
//
// Instead of clients repeatedly polling for updates, the server maintains a long-lived
// connection and streams updates as they occur.
//
// Features of the implementation:
//  1. Polling of BigTable for new workspace updates
//  2. Support for reconnection scenarios through sequence ID tracking
//  3. Periodic forced responses to verify client connection liveness
package main

import (
	"context"
	"errors"
	"time"

	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/rs/zerolog/log"
)

const (
	// WorkspacePollingInterval is the interval at which we poll for new workspace updates
	WorkspacePollingInterval = 1 * time.Second

	// WorkspaceMaxSkippedIterations is the maximum number of polling iterations we'll skip
	// before sending an empty response to verify the connection is still alive.
	// This is important because some proxies and load balancers will close idle connections.
	// By sending an empty response every N iterations, we ensure the connection stays alive.
	WorkspaceMaxSkippedIterations = 30 // Send a keepalive every ~30 seconds
)

// WorkspaceStreamer defines the interface for streaming workspace updates
type WorkspaceStreamer interface {
	// StreamWorkspaceUpdates streams workspace updates for a specific agent
	StreamWorkspaceUpdates(
		ctx context.Context,
		stream remoteagentsproto.RemoteAgents_WorkspaceStreamServer,
		req *remoteagentsproto.WorkspaceStreamRequest,
		userId string,
		tenantID string,
		requestContext *requestcontext.RequestContext,
	) error
}

// bigTableWorkspaceStreamer implements WorkspaceStreamer using BigTable
type bigTableWorkspaceStreamer struct {
	bigtableProxyClient bigtableproxy.BigtableProxyClient
	memstoreClient      memstoreclient.MemstoreClient
}

// NewWorkspaceStreamer creates a new WorkspaceStreamer
func NewWorkspaceStreamer(bigtableProxyClient bigtableproxy.BigtableProxyClient, memstoreClient memstoreclient.MemstoreClient) WorkspaceStreamer {
	return &bigTableWorkspaceStreamer{
		bigtableProxyClient: bigtableProxyClient,
		memstoreClient:      memstoreClient,
	}
}

// StreamWorkspaceUpdates implements WorkspaceStreamer.StreamWorkspaceUpdates
func (s *bigTableWorkspaceStreamer) StreamWorkspaceUpdates(
	ctx context.Context,
	stream remoteagentsproto.RemoteAgents_WorkspaceStreamServer,
	req *remoteagentsproto.WorkspaceStreamRequest,
	userId string,
	tenantID string,
	requestContext *requestcontext.RequestContext,
) error {
	// Create a processor to handle the business logic
	processor := NewWorkspaceProcessor(req.LastProcessedSequenceId)

	// Keep track of skipped iterations to periodically verify the connection is still alive
	skippedIterations := 0

	// Timer for polling - start with immediate execution
	timer := time.NewTimer(0) // 0 duration for immediate first execution
	defer timer.Stop()

	// Create a channel for the Pub/Sub subscription
	var messageChan chan []byte
	var cancelSubscription func()
	var err error

	// Subscribe to the Pub/Sub channel
	messageChan, cancelSubscription, err = SubscribeToPendingUpdateNotifications(ctx, requestContext, s.memstoreClient, req.RemoteAgentId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to subscribe to pending update notifications for agent %s", req.RemoteAgentId)
		// Continue without Pub/Sub, falling back to polling
	} else if cancelSubscription != nil {
		defer cancelSubscription()
	}

	for {
		select {
		case <-ctx.Done():
			err := context.Cause(ctx)
			if errors.Is(err, context.Canceled) {
				log.Ctx(ctx).Info().
					Str("error", err.Error()).
					Uint32("last_processed_sequence_id", processor.lastProcessedSequenceId).
					Msg("Workspace stream ended due to context cancellation")
			} else {
				log.Ctx(ctx).Warn().
					Str("error", err.Error()).
					Uint32("last_processed_sequence_id", processor.lastProcessedSequenceId).
					Msg("Workspace stream ended due to context error")
			}
			return err
		case message := <-messageChan:
			// Process the Pub/Sub notification
			err := processWorkspaceNotification(
				ctx,
				message,
				req.RemoteAgentId,
				tenantID,
				userId,
				requestContext,
				s.bigtableProxyClient,
				processor,
				stream,
				&skippedIterations,
			)
			if err != nil {
				return err
			}

		case <-timer.C:
			// Reset the timer for the next iteration
			timer.Reset(WorkspacePollingInterval)

			// Process the timer event using the same function as Pub/Sub notifications
			// We pass nil for the message since we're not processing a notification
			err := processWorkspaceNotification(
				ctx,
				nil, // No message for timer events
				req.RemoteAgentId,
				tenantID,
				userId,
				requestContext,
				s.bigtableProxyClient,
				processor,
				stream,
				&skippedIterations,
			)
			if err != nil {
				return err
			}

			// If we have no meaningful updates and we've reached the max skipped iterations,
			// send an empty response to verify the connection is still alive
			if skippedIterations > WorkspaceMaxSkippedIterations {
				// Create an empty response
				emptyResponse := &remoteagentsproto.WorkspaceStreamResponse{
					Updates: []*remoteagentsproto.WorkspaceStreamUpdate{},
				}

				// Send the empty response
				if err := stream.Send(emptyResponse); err != nil {
					log.Ctx(ctx).Warn().Err(err).Msgf("Failed to send empty workspace update for agent %s", req.RemoteAgentId)
					return err
				}

				// Reset the counter after sending a response
				skippedIterations = 0
			}
		}
	}
}

// processWorkspaceNotification processes a Pub/Sub notification for workspace updates
func processWorkspaceNotification(
	ctx context.Context,
	message []byte,
	agentId string,
	tenantID string,
	userId string,
	requestContext *requestcontext.RequestContext,
	bigtableProxyClient bigtableproxy.BigtableProxyClient,
	processor *WorkspaceProcessor,
	stream remoteagentsproto.RemoteAgents_WorkspaceStreamServer,
	skippedIterations *int,
) error {
	// Check if this is a Pub/Sub notification or a timer event
	if message != nil {
		// We received a notification via Pub/Sub
		log.Ctx(ctx).Debug().Msgf("Received Pub/Sub notification for agent %s", agentId)

		// Parse the message to get the sequence ID and request ID
		notification, err := UnmarshallPendingUpdateNotification(message)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to parse Pub/Sub notification for agent %s", agentId)
			return nil
		}

		// Check if we need to fetch this update
		if notification.SequenceID <= processor.lastProcessedSequenceId {
			log.Ctx(ctx).Debug().Msgf("Skipping already processed sequence ID %d", notification.SequenceID)
			return nil
		}
	} else {
		// This is a timer event, not a Pub/Sub notification
		log.Ctx(ctx).Debug().Msgf("Processing timer event for agent %s", agentId)

		// Increment the counter for iterations without meaningful updates
		*skippedIterations++
	}

	// Read pending updates from BigTable
	pendingUpdates, err := readPendingAgentUpdates(
		ctx,
		requestContext,
		bigtableProxyClient,
		tenantID,
		userId,
		agentId,
	)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msgf("Failed to read pending updates for agent %s", agentId)
		return nil
	}

	// Process workspace updates
	updates := processor.ProcessWorkspaceUpdates(pendingUpdates)

	// Check if we have any meaningful updates to send
	if len(updates) == 0 {
		// For Pub/Sub notifications, we just return
		if message != nil {
			return nil
		}

		// For timer events, we check if we need to send a keepalive
		// This is handled by the caller, so we just return
		return nil
	}

	// Create a single response with all updates
	combinedResponse := &remoteagentsproto.WorkspaceStreamResponse{
		Updates: updates,
	}

	// Send the combined response
	if err := stream.Send(combinedResponse); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msgf("Failed to send combined workspace update for agent %s", agentId)
		return err
	}

	// Reset the counter after a response
	*skippedIterations = 0

	return nil
}

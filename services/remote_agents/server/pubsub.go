package main

import (
	"context"
	"encoding/json"
	"fmt"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	"github.com/rs/zerolog/log"
)

// AgentHistoryNotification represents a notification about new chat history
type AgentHistoryNotification struct {
	SequenceID uint32 `json:"sequence_id"`
}

// PendingUpdateNotification represents a notification about a new pending update
type PendingUpdateNotification struct {
	SequenceID uint32 `json:"sequence_id"`
}

// PublishAgentHistoryNotification publishes a notification about new chat history
// This is called when new chat history is written to BigTable
func PublishAgentHistoryNotification(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	memstoreClient memstoreclient.MemstoreClient,
	agentId string,
	sequenceId uint32,
) {
	if memstoreClient == nil || sequenceId == 0 {
		return
	}

	pubsubChannel := fmt.Sprintf("RemoteAgents#AgentHistory#%s", agentId)
	notification := AgentHistoryNotification{
		SequenceID: sequenceId,
	}
	notificationBytes, err := json.Marshal(notification)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal notification")
		return
	}

	_, err = memstoreClient.Publish(ctx, requestContext, pubsubChannel, notificationBytes)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to publish notification to channel %s", pubsubChannel)
	} else {
		log.Ctx(ctx).Debug().Msgf("Published notification to channel %s with sequence ID %d", pubsubChannel, sequenceId)
	}
}

// PublishPendingUpdateNotification publishes a notification about a new pending update
// This is called when a new pending update is written to BigTable
func PublishPendingUpdateNotification(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	memstoreClient memstoreclient.MemstoreClient,
	agentId string,
	sequenceId uint32,
) {
	if memstoreClient == nil {
		return
	}

	pubsubChannel := fmt.Sprintf("RemoteAgents#PendingUpdates#%s", agentId)
	notification := PendingUpdateNotification{
		SequenceID: sequenceId,
	}
	notificationBytes, err := json.Marshal(notification)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal notification")
		return
	}

	_, err = memstoreClient.Publish(ctx, requestContext, pubsubChannel, notificationBytes)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to publish notification to channel %s", pubsubChannel)
	} else {
		log.Ctx(ctx).Debug().Msgf("Published notification to channel %s with sequence ID %d", pubsubChannel, sequenceId)
	}
}

// SubscribeToAgentHistoryNotifications subscribes to notifications about new chat history
// Returns a channel that will receive notifications and a function to cancel the subscription
func SubscribeToAgentHistoryNotifications(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	memstoreClient memstoreclient.MemstoreClient,
	agentId string,
) (chan []byte, func(), error) {
	if memstoreClient == nil {
		return nil, func() {}, nil
	}

	pubsubChannel := fmt.Sprintf("RemoteAgents#AgentHistory#%s", agentId)
	return memstoreClient.Subscribe(ctx, requestContext, pubsubChannel, true)
}

// SubscribeToPendingUpdateNotifications subscribes to notifications about new pending updates
// Returns a channel that will receive notifications and a function to cancel the subscription
func SubscribeToPendingUpdateNotifications(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	memstoreClient memstoreclient.MemstoreClient,
	agentId string,
) (chan []byte, func(), error) {
	if memstoreClient == nil {
		return nil, func() {}, nil
	}

	pubsubChannel := fmt.Sprintf("RemoteAgents#PendingUpdates#%s", agentId)
	return memstoreClient.Subscribe(ctx, requestContext, pubsubChannel, true)
}

// UnmarshallAgentHistoryNotification unmarshalls a raw notification message into an AgentHistoryNotification
func UnmarshallAgentHistoryNotification(notificationBytes []byte) (*AgentHistoryNotification, error) {
	var notification AgentHistoryNotification
	err := json.Unmarshal(notificationBytes, &notification)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal agent history notification: %w", err)
	}
	return &notification, nil
}

// UnmarshallPendingUpdateNotification unmarshalls a raw notification message into a PendingUpdateNotification
func UnmarshallPendingUpdateNotification(notificationBytes []byte) (*PendingUpdateNotification, error) {
	var notification PendingUpdateNotification
	err := json.Unmarshal(notificationBytes, &notification)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal pending update notification: %w", err)
	}
	return &notification, nil
}

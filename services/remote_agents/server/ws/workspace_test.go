package ws

import (
	"testing"

	"github.com/augmentcode/augment/infra/lib/k8s"
)

func wsT(t *testing.T, id string, sec, sts, pod string) *RemoteWorkspaceImpl {
	secObj := func() *k8s.Secret {
		if sec == "" {
			return nil
		}
		obj := k8s.NewSecret(nil)
		if err := obj.FromJSON(sec); err != nil {
			t.Fatalf("Failed to parse secret JSON: %v.", err)
		}
		return obj
	}()
	stsObj := func() *k8s.StatefulSet {
		if sts == "" {
			return nil
		}
		obj := k8s.NewStatefulSet(nil)
		if err := obj.FromJSON(sts); err != nil {
			t.Fatalf("Failed to parse statefulset JSON: %v.", err)
		}
		return obj
	}()
	podObj := func() *k8s.Pod {
		if pod == "" {
			return nil
		}
		obj := k8s.NewPod(nil)
		if err := obj.FromJSON(pod); err != nil {
			t.Fatalf("Failed to parse pod JSON: %v.", err)
		}
		return obj
	}()
	return &RemoteWorkspaceImpl{
		id:  id,
		sec: secObj,
		sts: stsObj,
		pod: podObj,
		k8s: nil,
	}
}

func TestID(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		wsID   string
		wsSec  string
		wsSts  string
		wsPod  string
		wantID string
	}{
		"empty": {
			wsID:   "",
			wantID: "",
		},
		"id0": {
			wsID:   "id0",
			wantID: "id0",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			ws := wsT(t, tc.wsID, tc.wsSec, tc.wsSts, tc.wsPod)
			if got, want := ws.ID(), tc.wantID; got != want {
				t.Errorf("ID(): got %v, want %v.", got, want)
			}
		})
	}
}

func TestAssertOwner(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		wsID    string
		wsSec   string
		wsSts   string
		wsPod   string
		inUser  string
		wantErr string // exact match, not a substring
	}{
		"empty": {
			wsID:    "id0",
			inUser:  "user0",
			wantErr: "workspace id0 ownership unknown",
		},
		"owner-secret": {
			wsID:   "id0",
			wsSec:  `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user0"}}}`,
			wsSts:  `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user1"}}}`,
			wsPod:  `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user2"}}}`,
			inUser: "user0",
		},
		"owner-statefulset": {
			wsID:   "id0",
			wsSts:  `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user0"}}}`,
			wsPod:  `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user1"}}}`,
			inUser: "user0",
		},
		"owner-pod": {
			wsID:   "id0",
			wsPod:  `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user0"}}}`,
			inUser: "user0",
		},
		"not-owner-secret": {
			wsID:    "id0",
			wsSec:   `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user1"}}}`,
			wsSts:   `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user0"}}}`,
			wsPod:   `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user0"}}}`,
			inUser:  "user0",
			wantErr: "workspace id0 is owned by another user",
		},
		"not-owner-statefulset": {
			wsID:    "id0",
			wsSts:   `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user1"}}}`,
			wsPod:   `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user0"}}}`,
			inUser:  "user0",
			wantErr: "workspace id0 is owned by another user",
		},
		"not-owner-pod": {
			wsID:    "id0",
			wsPod:   `{"metadata":{"labels":{"aug.remote-agent-workspace.owner":"user1"}}}`,
			inUser:  "user0",
			wantErr: "workspace id0 is owned by another user",
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			ws := wsT(t, tc.wsID, tc.wsSec, tc.wsSts, tc.wsPod)
			gotErr := ws.AssertOwner(tc.inUser)
			if got, want := gotErr == nil, tc.wantErr == ""; got != want {
				t.Errorf("ownership: got %v, want %v.", got, want)
			}
			if gotErr != nil {
				if got, want := gotErr.Error(), tc.wantErr; got != want {
					t.Errorf("error: -want +got\n-%s\n+%s", want, got)
				}
			}
		})
	}
}

package ws

import (
	"bytes"
	"compress/gzip"
	"io"
	"strconv"
	"strings"

	"google.golang.org/protobuf/encoding/protojson"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/intstr"
	appsv1a "k8s.io/client-go/applyconfigurations/apps/v1"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"
	metav1a "k8s.io/client-go/applyconfigurations/meta/v1"

	"github.com/augmentcode/augment/infra/lib/k8s"
	rapb "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/rs/zerolog/log"
)

const (
	k8sNewPrefix     = "raws"
	systemNS         = "aug-system"
	clusterRole      = "rwc-namespace-controller"
	sshPortName      = "ssh"
	npgAffinityLabel = "raws.augmentcode.com/pool-group"
	npgLabel         = "aug.remote-agent-workspace.pool-group"
)

func buildName(id string) string {
	return k8sNewPrefix + "-" + id
}

func buildPodName(id string) string {
	return buildName(id) + "-0"
}

func buildServiceName(id string) string {
	return buildName(id)
}

func buildServiceAccountName(namespace string) string {
	return k8sNewPrefix + "-" + namespace + "-sa"
}

////////////////////////////////////////////////////////////////////////////////
//
// Namespace-wide resources
//
// These are created with elevated permissions at startup.
//

func buildNamespace(namespace string) *k8s.NamespaceApplyConfig {
	ns := k8s.NewNamespaceApplyConfig(namespace)
	ns.WithLabels(map[string]string{
		"aug.remote-agent-workspace": "true",
	})
	return ns
}

func buildServiceAccount(namespace string) *k8s.ServiceAccountApplyConfig {
	name := buildServiceAccountName(namespace)
	sa := k8s.NewServiceAccountApplyConfig(name, systemNS)
	sa.WithLabels(map[string]string{
		"aug.remote-agent-workspace": "true",
	})
	return sa
}

func buildServiceAccountToken(namespace string) *k8s.SecretApplyConfig {
	sa := buildServiceAccountName(namespace)
	name := sa + "-tok"
	sec := k8s.NewSecretApplyConfig(name, systemNS)
	sec.WithLabels(map[string]string{
		"aug.remote-agent-workspace": "true",
	})
	sec.WithAnnotations(map[string]string{
		"kubernetes.io/service-account.name": sa,
	})
	sec.WithType("kubernetes.io/service-account-token")
	return sec
}

func getPublisherServiceAccount(namespace string) string {
	if strings.HasPrefix(namespace, "dev-") {
		return "<EMAIL>"
	}
	return "<EMAIL>"
}

func buildDefaultServiceAccount(namespace string) *k8s.ServiceAccountApplyConfig {
	name := "default"
	sa := k8s.NewServiceAccountApplyConfig(name, namespace)
	sa.WithLabels(map[string]string{
		"aug.remote-agent-workspace": "true",
	})
	sa.WithAnnotations(map[string]string{
		"iam.gke.io/gcp-service-account": getPublisherServiceAccount(namespace),
	})
	return sa
}

func buildRoleBinding(namespace, sa string) *k8s.RoleBindingApplyConfig {
	name := k8sNewPrefix + "-" + namespace + "-role-binding"
	rb := k8s.BuildRoleBindingApplyConfigForSA(name, namespace, sa, systemNS, "ClusterRole", clusterRole, map[string]string{
		"aug.remote-agent-workspace": "true",
	})
	return rb
}

////////////////////////////////////////////////////////////////////////////////
//
// Per-workspace resources
//
// These are created with fine-grained permissions at runtime.
//

func buildStatefulSet(img, namespace, user, id, secName, svcName, rootVS string, sshPort int, grps []string, iflags map[string]string) *k8s.StatefulSetApplyConfig {
	name := buildName(id)

	if t := iflags["img_tag"]; t != "" {
		parts := strings.Split(img, ":")
		parts[len(parts)-1] = t
		img = strings.Join(parts, ":")
	}
	labels := map[string]string{
		"aug.remote-agent-workspace":       "true",
		"aug.remote-agent-workspace.id":    id,
		"aug.remote-agent-workspace.owner": user,
		npgLabel:                           strings.Join(grps, "."), // `,` isn't valid in a label value
	}

	mult := func() int64 {
		if m := iflags["mult"]; m == "" {
			return 1
		} else if i, err := strconv.ParseInt(m, 10, 64); err != nil {
			return 1
		} else if i < 1 || i > 16 {
			return 1
		} else {
			return i
		}
	}()

	// StatefulSet

	sts := k8s.NewStatefulSetApplyConfig(name, namespace)
	sts.WithLabels(labels)

	// StatefulSetSpec

	stsspec := appsv1a.StatefulSetSpec()
	stsspec.WithReplicas(1)
	stsspec.WithServiceName(svcName)
	stsspec.WithSelector(metav1a.LabelSelector().WithMatchLabels(map[string]string{"aug.remote-agent-workspace.id": id}))

	pvcpolicy := appsv1a.StatefulSetPersistentVolumeClaimRetentionPolicy()
	pvcpolicy.WithWhenDeleted(appsv1.DeletePersistentVolumeClaimRetentionPolicyType)
	pvcpolicy.WithWhenScaled(appsv1.RetainPersistentVolumeClaimRetentionPolicyType)
	stsspec.WithPersistentVolumeClaimRetentionPolicy(pvcpolicy)

	rootPVC := corev1a.PersistentVolumeClaim("vm-root", namespace)
	rootPVC.WithLabels(labels)
	rootPVCSpec := corev1a.PersistentVolumeClaimSpec().
		WithAccessModes(corev1.ReadWriteOnce).
		WithResources(corev1a.VolumeResourceRequirements().
			WithRequests(corev1.ResourceList{
				corev1.ResourceStorage: *resource.NewQuantity(32*1024*1024*1024, resource.BinarySI),
			}),
		).
		WithVolumeMode(corev1.PersistentVolumeBlock)
	if rootVS != "" {
		rootPVCSpec.WithDataSource(
			corev1a.TypedLocalObjectReference().
				WithAPIGroup("snapshot.storage.k8s.io").
				WithKind("VolumeSnapshot").
				WithName(rootVS),
		)
	}
	rootPVC.WithSpec(rootPVCSpec)

	pvc := corev1a.PersistentVolumeClaim("persist", namespace)
	pvc.WithLabels(labels)
	pvc.WithSpec(corev1a.PersistentVolumeClaimSpec().
		WithAccessModes(corev1.ReadWriteOnce).
		WithResources(corev1a.VolumeResourceRequirements().
			WithRequests(corev1.ResourceList{
				corev1.ResourceStorage: *resource.NewQuantity(16*1024*1024*1024, resource.BinarySI),
			}),
		).
		WithVolumeMode(corev1.PersistentVolumeBlock),
	)

	// Pod

	pod := corev1a.PodTemplateSpec()
	pod.WithLabels(labels)

	// PodSpec
	podspec := corev1a.PodSpec()
	podspec.WithEnableServiceLinks(false)
	podspec.WithAffinity(
		corev1a.Affinity().WithNodeAffinity(
			corev1a.NodeAffinity().WithRequiredDuringSchedulingIgnoredDuringExecution(
				corev1a.NodeSelector().WithNodeSelectorTerms(
					corev1a.NodeSelectorTerm().WithMatchExpressions(
						corev1a.NodeSelectorRequirement().WithKey(npgAffinityLabel).WithOperator(corev1.NodeSelectorOpIn).WithValues(grps...),
					),
				),
			),
		),
	)
	for _, g := range grps {
		podspec.WithTolerations(
			corev1a.Toleration().WithEffect(corev1.TaintEffectNoSchedule).WithKey(npgAffinityLabel).WithOperator(corev1.TolerationOpEqual).WithValue(g),
		)
	}

	// Add pod affinity to try to schedule pods with any remote agent together
	podAffinity := corev1a.PodAffinity().WithPreferredDuringSchedulingIgnoredDuringExecution(
		corev1a.WeightedPodAffinityTerm().
			WithWeight(50).
			WithPodAffinityTerm(
				corev1a.PodAffinityTerm().
					WithTopologyKey("kubernetes.io/hostname").
					WithNamespaceSelector(metav1a.LabelSelector()). // Empty selector matches all namespaces
					WithLabelSelector(
						metav1a.LabelSelector().
							WithMatchExpressions(
								metav1a.LabelSelectorRequirement().
									WithKey("aug.remote-agent-workspace").
									WithOperator("Exists"),
							),
					),
			),
	)

	// Add the pod affinity to the existing affinity configuration
	if podspec.Affinity == nil {
		podspec.WithAffinity(corev1a.Affinity().WithPodAffinity(podAffinity))
	} else {
		podspec.Affinity.WithPodAffinity(podAffinity)
	}

	// Volume(s)
	volCfg := corev1a.Volume()
	volCfg.WithName("workspace-config")
	volCfg.WithSecret(corev1a.SecretVolumeSource().WithSecretName(secName))

	// Container
	container := corev1a.Container()
	container.WithName(name)
	container.WithImage(img)
	container.WithImagePullPolicy(corev1.PullAlways)

	container.WithSecurityContext(
		corev1a.SecurityContext().WithPrivileged(true).WithRunAsUser(0).WithRunAsGroup(0),
	)
	container.WithCommand(
		"/root/startup.sh",
	)
	container.WithArgs(
		"--id="+id,
		"--port="+strconv.Itoa(httpPort),
		"--ssh-port="+strconv.Itoa(sshPort),
		"--persist=/mnt/persist", // volmountCfg.MountPath
	)

	res := corev1.ResourceList{
		corev1.ResourceCPU:    *resource.NewMilliQuantity((4000*mult)+500, resource.DecimalSI),       // 4 CPU base + 1/2 for overhead (e.g., firecracker)
		corev1.ResourceMemory: *resource.NewQuantity((16*mult+2)*1000*1000*1000, resource.DecimalSI), // 16 GB base + 2 GB for overhead (e.g., firecracker)
	}
	container.WithResources(
		corev1a.ResourceRequirements().WithRequests(res).WithLimits(res),
	)

	// VolumeMount
	volmountCfg := corev1a.VolumeMount()
	volmountCfg.WithName("workspace-config")
	volmountCfg.WithMountPath("/run/secrets/workspace-config")
	container.WithVolumeMounts(volmountCfg)

	container.WithVolumeDevices(
		corev1a.VolumeDevice().WithName("vm-root").WithDevicePath("/dev/vm-root"),
	)
	container.WithVolumeDevices(
		corev1a.VolumeDevice().WithName("persist").WithDevicePath("/dev/persist"),
	)

	// Health/Readiness checks
	startup := corev1a.Probe()
	startup.WithInitialDelaySeconds(10)
	startup.WithTimeoutSeconds(1)
	startup.WithPeriodSeconds(5)
	startup.WithFailureThreshold(10 * 60 / 5) // 10m at 1 probe per 5 seconds
	startup.WithSuccessThreshold(1)

	startupHTTP := corev1a.HTTPGetAction()
	startupHTTP.WithPort(intstr.FromInt(httpPort))
	startupHTTP.WithPath("/health")
	startupHTTP.WithScheme(corev1.URISchemeHTTP)

	// NOTE(mattm): Some of these curiously insert by value so they need to be called last.
	startup.WithHTTPGet(startupHTTP)
	container.WithStartupProbe(startup)
	podspec.WithVolumes(volCfg)
	podspec.WithContainers(container)
	pod.WithSpec(podspec)
	stsspec.WithTemplate(pod)
	if rootPVC != nil {
		stsspec.WithVolumeClaimTemplates(rootPVC)
	}
	stsspec.WithVolumeClaimTemplates(pvc)
	sts.WithSpec(stsspec)

	return sts
}

func buildService(namespace, user, id string, sshPort int) *k8s.ServiceApplyConfig {
	name := buildServiceName(id)
	svc := k8s.NewServiceApplyConfig(name, namespace)
	svc.WithLabels(map[string]string{
		"aug.remote-agent-workspace":       "true",
		"aug.remote-agent-workspace.id":    id,
		"aug.remote-agent-workspace.owner": user,
	})

	spec := corev1a.ServiceSpec()
	spec.WithType(corev1.ServiceTypeClusterIP)
	spec.WithSelector(map[string]string{"aug.remote-agent-workspace.id": id})
	spec.WithPorts(
		corev1a.ServicePort().WithName(httpPortName).WithPort(httpPort),
		corev1a.ServicePort().WithName(sshPortName).WithPort(int32(sshPort)),
	)
	// This service will always be for one pod, and we don't want the pod-level health checks to delay service's backend IP.
	spec.WithPublishNotReadyAddresses(true)

	svc.WithSpec(spec)
	return svc
}

func buildConfigSecret(namespace, user, id string, wsCfg *rapb.WorkspaceAgentConfig, revTunKubecfg string) (*k8s.SecretApplyConfig, error) {
	name := buildName(id)
	sec := k8s.NewSecretApplyConfig(name, namespace)
	sec.WithLabels(map[string]string{
		"aug.remote-agent-workspace":       "true",
		"aug.remote-agent-workspace.id":    id,
		"aug.remote-agent-workspace.owner": user,
	})

	if buf, err := workspaceAgentConfigToJsonGz(wsCfg); err != nil {
		return nil, err
	} else {
		sec.WithData(map[string][]byte{
			"workspace-agent-config.json.gz": buf,
		})
	}

	// TODO(mattm): Remove these, github info has been moved to the WorkspaceAgentConfig
	if ghUsername := wsCfg.GetGithubUsername(); ghUsername != "" {
		sec.WithData(map[string][]byte{
			"github-username": []byte(ghUsername),
		})
	}
	if ghUserToken := wsCfg.GetGithubUserToken(); ghUserToken != "" {
		sec.WithData(map[string][]byte{
			"github-user-token": []byte(ghUserToken),
		})
	}

	if revTunKubecfg != "" {
		sec.WithData(map[string][]byte{
			"revtun_kubeconfig": []byte(revTunKubecfg),
		})
	}

	return sec, nil
}

// TODO(mike): remove after two weeks for backward compatibility
func cfgFromJsonGz(buf []byte) (*rapb.AgentConfig, error) {
	log.Info().Msg("Parsing agent config from json.gz using cfgFromJsonGz")

	// Try to parse as WorkspaceAgentConfig first
	wsCfg, err := workspaceAgentConfigFromJsonGz(buf)
	if err == nil && wsCfg != nil {
		// If successful, return the config from the WorkspaceAgentConfig
		return wsCfg.Config, nil
	}

	// Fall back to parsing as AgentConfig for backward compatibility
	gz, err := gzip.NewReader(bytes.NewReader(buf))
	if err != nil {
		return nil, err
	}
	defer gz.Close()

	json := bytes.Buffer{}
	if _, err := io.Copy(&json, gz); err != nil {
		return nil, err
	}

	cfg := &rapb.AgentConfig{}
	if err := (protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}).Unmarshal(json.Bytes(), cfg); err != nil {
		return nil, err
	}
	return cfg, nil
}

func workspaceAgentConfigToJsonGz(wsCfg *rapb.WorkspaceAgentConfig) ([]byte, error) {
	json, err := (protojson.MarshalOptions{Multiline: true, UseProtoNames: true}).Marshal(wsCfg)
	if err != nil {
		return nil, err
	}

	// protojson should include a trailing newline on multi-line output.
	json = append(json, '\n')

	jsongz := bytes.Buffer{}
	gz := gzip.NewWriter(&jsongz)
	if _, err := gz.Write(json); err != nil {
		return nil, err
	} else if err := gz.Close(); err != nil {
		return nil, err
	}

	return jsongz.Bytes(), nil
}

func workspaceAgentConfigFromJsonGz(buf []byte) (*rapb.WorkspaceAgentConfig, error) {
	gz, err := gzip.NewReader(bytes.NewReader(buf))
	if err != nil {
		return nil, err
	}
	defer gz.Close()

	json := bytes.Buffer{}
	if _, err := io.Copy(&json, gz); err != nil {
		return nil, err
	}

	wsCfg := &rapb.WorkspaceAgentConfig{}
	if err := (protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}).Unmarshal(json.Bytes(), wsCfg); err != nil {
		return nil, err
	}
	return wsCfg, nil
}

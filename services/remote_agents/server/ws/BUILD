load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@io_bazel_rules_go//extras:gomock.bzl", "gomock")

package(default_visibility = [
    "//services/remote_agents:__subpackages__",
])

go_library(
    name = "ws",
    srcs = [
        "build.go",
        "controller.go",
        "workspace.go",
    ],
    importpath = "github.com/augmentcode/augment/services/remote_agents/server/ws",
    deps = [
        "//infra/lib/k8s",
        "//infra/lib/logger",
        "//services/remote_agents:remote_agents_go_proto",
        "@com_github_rs_zerolog//log",
        "@io_k8s_api//apps/v1:apps",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/resource",
        "@io_k8s_apimachinery//pkg/util/intstr",
        "@io_k8s_client_go//applyconfigurations/apps/v1:apps",
        "@io_k8s_client_go//applyconfigurations/core/v1:core",
        "@io_k8s_client_go//applyconfigurations/meta/v1:meta",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "ws_test",
    srcs = [
        "build_test.go",
        "workspace_test.go",
    ],
    embed = [":ws"],
    deps = [
        "@com_github_google_go_cmp//cmp",
        "@org_golang_google_protobuf//testing/protocmp",
    ],
)

# Generate mocks for interfaces
gomock(
    name = "mock_remote_workspace",
    out = "mocks/mock_remote_workspace.go",
    interfaces = ["RemoteWorkspace"],
    library = ":ws",
    package = "mocks",
    visibility = ["//visibility:public"],
)

gomock(
    name = "mock_remote_workspace_controller",
    out = "mocks/mock_remote_workspace_controller.go",
    interfaces = ["RemoteWorkspaceController"],
    library = ":ws",
    package = "mocks",
    visibility = ["//visibility:public"],
)

# Library for mock files
go_library(
    name = "mocks",
    srcs = [
        "mocks/mock_remote_workspace.go",
        "mocks/mock_remote_workspace_controller.go",
    ],
    importpath = "github.com/augmentcode/augment/services/remote_agents/server/ws/mocks",
    visibility = ["//visibility:public"],
    deps = [
        ":ws",
        "//services/remote_agents:remote_agents_go_proto",
        "@com_github_golang_mock//gomock",
    ],
)

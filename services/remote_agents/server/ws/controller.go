package ws

import (
	"context"
	"io"
	"os"
	"sort"

	"github.com/rs/zerolog/log"
	"golang.org/x/sync/errgroup"

	"github.com/augmentcode/augment/infra/lib/k8s"
	rapb "github.com/augmentcode/augment/services/remote_agents/proto"
)

const (
	fieldManager   = "r.augmentcode.com/remote-workspace-controller"
	defaultKubecfg = "/run/augment/secrets/remote-workspace-controller/kubeconfig"
	httpPort       = 9999
	httpPortName   = "http"
	saExpiration   = int64(8 * 7 * 24 * 3600) // 8 weeks
)

// RemoteWorkspaceController defines the interface for managing remote agent workspaces
type RemoteWorkspaceController interface {
	// Returns the namespace this controller operates in.
	Namespace() string

	// ApplyNamespace creates the target namespace if it doesn't exist
	ApplyNamespace(ctx context.Context) error

	// DropPrivileges works by replacing the controller's original, "superuser", K8s client with a
	// new client that uses the fine-grained ServiceAccount from `ApplyNamespace()`.
	//
	// NOTE(mattm): This for production safety/sanity more than security. Technically, the original credentials
	// are still available in the mounted secret. It's possible to drop them more thoroughly: mount them in an init container,
	// copy to a shared emptyDir, shred after reading.
	DropPrivileges(ctx context.Context) error

	// ApplyWorkspace creates or updates a workspace for the given user and agent ID
	ApplyWorkspace(ctx context.Context, user, id string, wsCfg *rapb.WorkspaceAgentConfig) (RemoteWorkspace, error)

	// ListWorkspaces lists all workspaces for the given user
	ListWorkspaces(ctx context.Context, user string) ([]RemoteWorkspace, error)

	// GetWorkspace gets a workspace for the given user and agent ID
	GetWorkspace(ctx context.Context, user, id string) (RemoteWorkspace, error)

	// InterruptWorkspace interrupts a running workspace
	InterruptWorkspace(ctx context.Context, user, id string) error

	// DeleteWorkspace deletes a workspace
	DeleteWorkspace(ctx context.Context, user, id string) error

	// StopWorkspace stops a running workspace
	StopWorkspace(ctx context.Context, user, id string) error

	// ResumeWorkspace resumes a stopped workspace
	ResumeWorkspace(ctx context.Context, user, id string) error
}

// NewRemoteWorkspaceController creates a new RemoteWorkspaceController
func NewRemoteWorkspaceController(stderr io.Writer, kubeconfigPath, img, sshHN string, sshPort int, nodePoolGroups []string, revTunKubecfgTmpl string) (RemoteWorkspaceController, error) {
	if kubeconfigPath == "" {
		kubeconfigPath = defaultKubecfg
	}
	k, err := k8s.NewFromKubeconfig("", kubeconfigPath)
	if err != nil {
		return nil, err
	}
	k.SetDefaultFieldManager(fieldManager)

	tmpl, revk, err := func() (string, *k8s.Client, error) {
		if path := revTunKubecfgTmpl; path == "" {
			return "", nil, nil
		} else if buf, err := os.ReadFile(path); err != nil {
			return "", nil, err
		} else if k, err := k8s.NewFromInCluster(); err != nil {
			return "", nil, err
		} else {
			return string(buf), k, nil
		}
	}()
	if err != nil {
		return nil, err
	}

	return &remoteWorkspaceControllerImpl{
		k8s:     k,
		img:     img,
		sshHN:   sshHN,
		sshPort: sshPort,
		grps:    nodePoolGroups,

		revTunK8s:         revk,
		revTunKubecfgTmpl: tmpl,
	}, nil
}

type remoteWorkspaceControllerImpl struct {
	k8s     *k8s.Client
	img     string
	sshHN   string
	sshPort int
	grps    []string

	revTunK8s         *k8s.Client
	revTunKubecfgTmpl string
}

// revTunKubecfg generates a kubeconfig for the Reverse Tunnel. It expects the kubeconfig template
// to set the server IP and certificate; it also expects the user to be the name of the service account
// to get a token for.
func (c *remoteWorkspaceControllerImpl) revTunKubecfg(ctx context.Context) (string, error) {
	// Noop if we don't have a reverse tunnel client or template.
	if c.revTunK8s == nil || c.revTunKubecfgTmpl == "" {
		return "", nil
	}

	kc, err := k8s.KubeConfigFromYAML(c.revTunKubecfgTmpl)
	if err != nil {
		return "", err
	}
	saName := kc.CurrentUser()
	tok, err := c.revTunK8s.CreateToken(ctx, saName, saExpiration)
	if err != nil {
		return "", err
	}
	if err := kc.SetTokenForUser(saName, tok.Token()); err != nil {
		return "", err
	}
	return kc.YAML()
}

func (c *remoteWorkspaceControllerImpl) Namespace() string {
	return c.k8s.Namespace()
}

func (c *remoteWorkspaceControllerImpl) ApplyNamespace(ctx context.Context) error {
	nsAC := buildNamespace(c.k8s.Namespace())
	saAC := buildServiceAccount(c.k8s.Namespace())
	tkAC := buildServiceAccountToken(c.k8s.Namespace())
	rbAC := buildRoleBinding(c.k8s.Namespace(), saAC.Name())
	dfSA := buildDefaultServiceAccount(c.k8s.Namespace())

	if _, err := c.k8s.ApplyNamespace(ctx, nsAC); err != nil {
		return err
	} else if _, err := c.k8s.InNamespace(systemNS).ApplyServiceAccount(ctx, saAC); err != nil {
		return err
	} else if _, err := c.k8s.InNamespace(systemNS).ApplySecret(ctx, tkAC); err != nil {
		return err
	} else if _, err := c.k8s.InNamespace(c.k8s.Namespace()).ApplyServiceAccount(ctx, dfSA); err != nil {
		return err
	} else if _, err := c.k8s.ApplyRoleBinding(ctx, rbAC); err != nil {
		return err
	}
	return nil
}

func (c *remoteWorkspaceControllerImpl) DropPrivileges(ctx context.Context) error {
	if ssr, err := c.k8s.WhoAmI(ctx); err != nil {
		return err
	} else {
		log.Info().Msgf("DropPrivileges(): Starting With: %s", ssr.UserInfoString())
	}

	k, err := c.k8s.AsServiceAccountStaticToken(ctx, systemNS, buildServiceAccountName(c.k8s.Namespace()))
	if err != nil {
		return err
	}

	if ssr, err := k.WhoAmI(ctx); err != nil {
		return err
	} else {
		log.Info().Msgf("DropPrivileges(): Dropped To: %s", ssr.UserInfoString())
	}

	c.k8s = k
	return err
}

func (c *remoteWorkspaceControllerImpl) ApplyWorkspace(ctx context.Context, user, id string, wsCfg *rapb.WorkspaceAgentConfig) (RemoteWorkspace, error) {
	/// Check permissions if workspace already exists.

	if _, err := c.GetWorkspace(ctx, user, id); k8s.NotFoundOK(err) != nil {
		return nil, err
	}

	/// Build the ApplyConfigurations first (Secret, Service, StatefulSet).
	revTunKubecfg, err := c.revTunKubecfg(ctx)
	if err != nil {
		log.Warn().Msgf("Failed to generate reverse tunnel kubeconfig: %v.", err)
	}
	secAC, err := buildConfigSecret(c.k8s.Namespace(), user, id, wsCfg, revTunKubecfg)
	if err != nil {
		return nil, err
	}

	svcAC := buildService(c.k8s.Namespace(), user, id, c.sshPort)
	stsAC := buildStatefulSet(c.img, c.k8s.Namespace(), user, id, secAC.Name(), svcAC.Name(), wsCfg.GetExperiments()["rootvs"], c.sshPort, c.grps, wsCfg.GetExperiments())

	/// Apply the Secret first so we can use it as the Owner of the others.

	secObj, err := c.k8s.ApplySecret(ctx, secAC, k8s.ApplyForce(true))
	if err != nil {
		return nil, err
	}

	svcAC.WithOwner(&secObj.Object)
	stsAC.WithOwner(&secObj.Object)

	/// Apply everything else.

	if _, err := c.k8s.ApplyService(ctx, svcAC, k8s.ApplyForce(true)); err != nil {
		return nil, err
	}
	stsObj, err := c.k8s.ApplyStatefulSet(ctx, stsAC, k8s.ApplyForce(true))
	if err != nil {
		return nil, err
	}

	return NewRemoteWorkspace(id, secObj, stsObj, nil, c.k8s, c.sshHN, c.sshPort), nil
}

func (c *remoteWorkspaceControllerImpl) ListWorkspaces(ctx context.Context, user string) ([]RemoteWorkspace, error) {
	/// List out all object types in parallel, index by ID.

	secs := map[string]*k8s.Secret{}
	stss := map[string]*k8s.StatefulSet{}
	pods := map[string]*k8s.Pod{}

	labels := map[string]string{
		"aug.remote-agent-workspace":       "true",
		"aug.remote-agent-workspace.owner": user,
	}

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		if objs, err := c.k8s.ListSecrets(gctx, k8s.ListMatchLabels(labels)); err != nil {
			return err
		} else {
			for _, obj := range objs {
				secs[obj.Label("aug.remote-agent-workspace.id")] = obj
			}
		}
		return nil
	})
	grp.Go(func() error {
		if objs, err := c.k8s.ListStatefulSets(gctx, k8s.ListMatchLabels(labels)); err != nil {
			return err
		} else {
			for _, obj := range objs {
				stss[obj.Label("aug.remote-agent-workspace.id")] = obj
			}
		}
		return nil
	})
	grp.Go(func() error {
		if objs, err := c.k8s.ListPods(gctx, k8s.ListMatchLabels(labels)); err != nil {
			return err
		} else {
			for _, obj := range objs {
				pods[obj.Label("aug.remote-agent-workspace.id")] = obj
			}
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	/// Collect storted set of seen IDs.

	idset := map[string]bool{}
	for id := range secs {
		idset[id] = true
	}
	for id := range stss {
		idset[id] = true
	}
	for id := range pods {
		idset[id] = true
	}
	ids := []string{}
	for id := range idset {
		ids = append(ids, id)
	}
	sort.Strings(ids)

	/// Aggregate results

	rss := []RemoteWorkspace{}
	for _, id := range ids {
		rss = append(rss, NewRemoteWorkspace(id, secs[id], stss[id], pods[id], c.k8s, c.sshHN, c.sshPort))
	}
	return rss, nil
}

func (c *remoteWorkspaceControllerImpl) GetWorkspace(ctx context.Context, user, id string) (RemoteWorkspace, error) {
	ws, err := func() (RemoteWorkspace, error) {
		if sts, err := c.k8s.GetStatefulSet(ctx, buildName(id)); err != nil {
			return nil, err
		} else if sec, err := c.k8s.GetSecret(ctx, buildName(id)); err != nil {
			return nil, err
		} else if pod, err := c.k8s.GetPod(ctx, buildPodName(id)); k8s.NotFoundOK(err) != nil {
			return nil, err
		} else {
			return NewRemoteWorkspace(id, sec, sts, pod, c.k8s, c.sshHN, c.sshPort), nil
		}
	}()
	if err != nil {
		return nil, err
	} else if err := ws.AssertOwner(user); err != nil {
		return nil, err
	} else {
		return ws, nil
	}
}

func (c *remoteWorkspaceControllerImpl) getSecret(ctx context.Context, id string) (*k8s.Secret, error) {
	return c.k8s.GetSecret(ctx, buildName(id))
}

func (c *remoteWorkspaceControllerImpl) getStatefulSet(ctx context.Context, id string) (*k8s.StatefulSet, error) {
	return c.k8s.GetStatefulSet(ctx, buildName(id))
}

func (c *remoteWorkspaceControllerImpl) getPod(ctx context.Context, id string) (*k8s.Pod, error) {
	return c.k8s.GetPod(ctx, buildPodName(id))
}

func (c *remoteWorkspaceControllerImpl) InterruptWorkspace(ctx context.Context, user, id string) error {
	if ws, err := c.GetWorkspace(ctx, user, id); err != nil {
		return err
	} else {
		_, err := ws.Interrupt(ctx)
		return err
	}
}

func (c *remoteWorkspaceControllerImpl) DeleteWorkspace(ctx context.Context, user, id string) error {
	if ws, err := c.GetWorkspace(ctx, user, id); err != nil {
		return err
	} else {
		return ws.Delete(ctx)
	}
}

func (c *remoteWorkspaceControllerImpl) StopWorkspace(ctx context.Context, user, id string) error {
	// Get the workspace to verify ownership
	if _, err := c.GetWorkspace(ctx, user, id); err != nil {
		return err
	}

	// Get the StatefulSet
	sts, err := c.getStatefulSet(ctx, id)
	if err != nil {
		return err
	}

	// Scale the StatefulSet to 0 replicas
	_, err = c.k8s.PatchStatefulSetReplicas(ctx, sts.Name(), 0)
	if err != nil {
		return err
	}

	return nil
}

func (c *remoteWorkspaceControllerImpl) ResumeWorkspace(ctx context.Context, user, id string) error {
	// Get the workspace to verify ownership
	if _, err := c.GetWorkspace(ctx, user, id); err != nil {
		return err
	}

	// Get the StatefulSet
	sts, err := c.getStatefulSet(ctx, id)
	if err != nil {
		return err
	}

	// Scale the StatefulSet to 1 replica
	_, err = c.k8s.PatchStatefulSetReplicas(ctx, sts.Name(), 1)
	if err != nil {
		return err
	}

	return nil
}

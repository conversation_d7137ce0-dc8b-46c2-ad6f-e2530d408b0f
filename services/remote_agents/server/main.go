package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	grpcmetrics "github.com/augmentcode/augment/services/lib/grpc/metrics"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	githubprocessorclient "github.com/augmentcode/augment/services/integrations/github/processor/client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	proto "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/augmentcode/augment/services/remote_agents/server/ws"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
)

var (
	configFile                    = flag.String("config", "", "Path to config file")
	requestInsightPublisherConfig = flag.String("request-insight-publisher-config-file", "", "Path to request insight publisher config file.")
	scanIntervalFlag              = featureflags.NewIntFlag("remote_agents_scan_interval_seconds", 300)
)

// Config represents the main application configuration
type Config struct {
	// The gRPC server port
	Port int `json:"port"`
	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// TLS configuration
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	TokenExchangeEndpoint   string `json:"token_exchange_endpoint"`
	TenantWatcherEndpoint   string `json:"tenant_watcher_endpoint"`
	BigtableProxyEndpoint   string `json:"bigtable_proxy_endpoint"`
	GithubProcessorEndpoint string `json:"github_processor_endpoint"`
	MemstoreEndpoint        string `json:"memstore_endpoint"`

	// Feature flag configuration
	FeatureFlagsSdkKeyPath      string `json:"feature_flags_sdk_key_path,omitempty"`
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint,omitempty"`

	RequestInsightTopicName string `json:"request_insight_topic_name"`

	// Remote Workspace configuration
	RemoteWorkspaces struct {
		KubeconfigPath   string   `json:"kubeconfig_path"`
		Image            string   `json:"image"`
		SshProxyHostname string   `json:"ssh_proxy_hostname"`
		SshProxyPort     int      `json:"ssh_proxy_port"`
		NodePoolGroups   []string `json:"node_pool_groups"`

		EnableInstructionFlags bool   `json:"enable_instruction_flags"`
		RevTunKubecfgTmpl      string `json:"rev_tunnel_kubeconfig_tmpl,omitempty"`
	} `json:"remote_workspaces"`
	// Leader election configuration
	LeaderElection LeaderElectionConfig `json:"leader_election"`
}

// LeaderElectionConfig holds configuration for leader election
type LeaderElectionConfig struct {
	Namespace            string `json:"namespace"`
	LockName             string `json:"lock_name"`
	LeaseDurationSeconds int    `json:"lease_duration_seconds,omitempty"`
	RenewDeadlineSeconds int    `json:"renew_deadline_seconds,omitempty"`
	RetryPeriodSeconds   int    `json:"retry_period_seconds,omitempty"`
}

func createRemoteAgentsServer(config *Config, clientCreds credentials.TransportCredentials, featureFlagHandle featureflags.FeatureFlagHandle, riPublisher ripublisher.RequestInsightPublisher) (*RemoteAgentsServer, error) {
	bigtableProxyClient, err := bigtableproxy.NewBigtableProxyClient(config.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		return nil, err
	}

	// Create the RemoteWorkspaceController implementation
	wcfg := config.RemoteWorkspaces
	rwc, err := ws.NewRemoteWorkspaceController(nil, wcfg.KubeconfigPath, wcfg.Image, wcfg.SshProxyHostname, wcfg.SshProxyPort, wcfg.NodePoolGroups, wcfg.RevTunKubecfgTmpl)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to create remote workspace controller")
		return nil, err
	}

	githubProcessorClient, err := githubprocessorclient.New(config.GithubProcessorEndpoint, grpc.WithTransportCredentials(clientCreds))
	if err != nil {
		return nil, err
	}

	// Create the memstore client
	memstoreClient, err := memstoreclient.NewMemstoreClient(config.MemstoreEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to create memstore client")
		return nil, err
	}

	// Create the RemoteAgentsServer with the controller
	remoteAgentsServer, err := NewRemoteAgentsServer(rwc, bigtableProxyClient, githubProcessorClient, riPublisher, featureFlagHandle, config.RemoteWorkspaces.EnableInstructionFlags, config.RequestInsightTopicName, memstoreClient)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to create remote agents server")
		return nil, err
	}
	return remoteAgentsServer, nil
}

func setupLeaderElectionAndTasks(appConfig *Config, ctx context.Context, clientCreds credentials.TransportCredentials, tenantWatcherClient tenantwatcherclient.TenantWatcherClient, tokenExchangeClient tokenexchange.TokenExchangeClient, featureFlagHandle featureflags.FeatureFlagHandle) (*SingletonTaskManager, error) {
	bigtableProxyClient, err := bigtableproxy.NewBigtableProxyClient(appConfig.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		return nil, err
	}
	log.Info().Msgf("Setting up leader election with lock %s", appConfig.LeaderElection.LockName)

	// Create the RemoteWorkspaceController for the cron task
	wcfg := appConfig.RemoteWorkspaces
	workspaceController, err := ws.NewRemoteWorkspaceController(nil, wcfg.KubeconfigPath, wcfg.Image, wcfg.SshProxyHostname, wcfg.SshProxyPort, wcfg.NodePoolGroups, wcfg.RevTunKubecfgTmpl)
	if err != nil {
		return nil, fmt.Errorf("failed to create workspace controller for cron task: %w", err)
	}

	// Create config for remote agents cron tasks
	cronConfig := &RemoteAgentsCronConfig{
		BigtableProxyClient: bigtableProxyClient,
		TenantWatcherClient: tenantWatcherClient,
		TokenExchangeClient: tokenExchangeClient,
		WorkspaceController: workspaceController,
		FeatureFlagHandle:   featureFlagHandle,
		NumWorkers:          20,
		TableReadBatchSize:  5000,
	}

	// Get scan interval from feature flag
	scanIntervalSeconds, err := scanIntervalFlag.Get(featureFlagHandle)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get scan interval from feature flag, using default")
	}
	scanInterval := time.Duration(scanIntervalSeconds) * time.Second
	log.Info().Dur("scan_interval", scanInterval).Msg("Using scan interval for remote agents task")

	// create tasks for singleton job
	scanAgentsTask := NewCronTask("ScanAgentsTask", scanInterval,
		NewScanRemoteAgentsCronTask(cronConfig),
	)
	tasksToRun := []Task{scanAgentsTask}

	jobConfig := SingletonTaskManagerConfig{
		LockName:  appConfig.LeaderElection.LockName,
		Namespace: appConfig.LeaderElection.Namespace,
	}
	if appConfig.LeaderElection.LeaseDurationSeconds > 0 {
		jobConfig.LeaseDuration = time.Duration(appConfig.LeaderElection.LeaseDurationSeconds) * time.Second
	}
	if appConfig.LeaderElection.RenewDeadlineSeconds > 0 {
		jobConfig.RenewDeadline = time.Duration(appConfig.LeaderElection.RenewDeadlineSeconds) * time.Second
	}
	if appConfig.LeaderElection.RetryPeriodSeconds > 0 {
		jobConfig.RetryPeriod = time.Duration(appConfig.LeaderElection.RetryPeriodSeconds) * time.Second
	}

	currentSingletonJob, err := NewSingletonTaskManager(jobConfig, tasksToRun)
	if err != nil {
		return nil, fmt.Errorf("failed to create singleton job: %w", err)
	}

	if err := currentSingletonJob.Start(ctx); err != nil {
		currentSingletonJob.Stop() // Attempt to stop if start fails
		return nil, fmt.Errorf("failed to start singleton job: %w", err)
	}
	return currentSingletonJob, nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		log.Fatal().Msg("POD_NAMESPACE environment variable is not set")
	}

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM, os.Interrupt)
	wg := sync.WaitGroup{}

	// Start metrics server
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		metricsAddr := fmt.Sprintf(":%d", config.PromPort)
		log.Info().Str("address", metricsAddr).Msg("Starting metrics server")
		if err := http.ListenAndServe(metricsAddr, nil); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Metrics server failed")
		}
	}()

	// Setup metrics
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	grpcMetricsInterceptor := grpcmetrics.NewMetricsInterceptor()

	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
		grpcMetricsInterceptor.UnaryInterceptor,
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
		grpcMetricsInterceptor.StreamInterceptor,
	))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, namespace)
	defer tenantCache.Close()

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)

	// Setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	// Initialize feature flag client
	var featureFlagHandle featureflags.FeatureFlagHandle
	if config.FeatureFlagsSdkKeyPath != "" {
		var err error
		featureFlagHandle, err = featureflags.NewFeatureFlagHandleFromFile(
			config.FeatureFlagsSdkKeyPath,
			config.DynamicFeatureFlagsEndpoint,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating feature flag handle")
		}
	} else {
		log.Info().Msg("Feature flags disabled: using local implementation")
		featureFlagHandle = featureflags.NewLocalFeatureFlagHandler()
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Setup leader election if lock name is provided
	var singleTaskMaganer *SingletonTaskManager
	singleTaskMaganer, err = setupLeaderElectionAndTasks(&config, ctx, clientCreds, tenantWatcherClient, tokenExchangeClient, featureFlagHandle)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to set up leader election and tasks: %v", err)
	}
	defer singleTaskMaganer.Stop()

	// Setup request insight publisher
	requestInsightPublisher, err := ripublisher.NewRequestInsightPublisherFromFile(
		ctx, *requestInsightPublisherConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating request insight publisher")
	}
	defer requestInsightPublisher.Close()

	// Create and register remote agents server
	remoteAgentsServer, err := createRemoteAgentsServer(&config, clientCreds, featureFlagHandle, requestInsightPublisher)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to create remote agents server")
	}
	proto.RegisterRemoteAgentsServer(grpcServer, remoteAgentsServer)

	// Apply backend namespace
	if err := remoteAgentsServer.rwc.ApplyNamespace(context.Background()); err != nil {
		log.Fatal().Err(err).Msgf("Failed to apply backend namespace")
	}

	// Drop privileges
	if err := remoteAgentsServer.rwc.DropPrivileges(context.Background()); err != nil {
		log.Fatal().Err(err).Msgf("Failed to drop privileges")
	}

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	grpcServer.GracefulStop()
	wg.Wait()
	log.Info().Msg("Server stopped")
}

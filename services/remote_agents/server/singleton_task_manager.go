// Package main implements the Remote Agents server functionality.
//
// This file contains the SingletonJob implementation, which provides leader election
// functionality to ensure that certain tasks are only executed by a single instance
// of the service at a time.
package main

import (
	"context"
	"os"
	"time"

	"github.com/rs/zerolog/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
)

const (
	defaultLeaseDuration = 15 * time.Second
	defaultRenewDeadline = 10 * time.Second
	defaultRetryPeriod   = 2 * time.Second
	defaultTaskInterval  = 1 * time.Minute
)

// Task defines an interface for operations that can be managed by SingletonTaskManager.
type Task interface {
	// Start is called when the instance becomes the leader.
	//  This context is live thoughout the lifecycle of the instance being the leader
	Start(ctx context.Context)
	// Stop is called when the instance stops being the leader or when stop is called
	Stop()
	// Used for logging
	Name() string
}

// SingletonTaskManager manages leader election to ensure a list of tasks run on only one instance
type SingletonTaskManager struct {
	// Configuration
	lockName  string
	namespace string

	// Leader election settings
	leaseDuration time.Duration
	renewDeadline time.Duration
	retryPeriod   time.Duration
	// taskInterval  time.Duration // Interval suggestion for tasks - REMOVED

	// State
	isLeader bool

	// Kubernetes client
	clientset *kubernetes.Clientset

	// Identity
	identity string

	// Leader elector
	leaderElector *leaderelection.LeaderElector

	// Tasks to run
	tasks []Task
}

// SingletonTaskManagerConfig holds configuration for a SingletonTaskManager
type SingletonTaskManagerConfig struct {
	// Required fields
	LockName  string
	Namespace string

	// Optional fields with defaults
	LeaseDuration time.Duration
	RenewDeadline time.Duration
	RetryPeriod   time.Duration
	// TaskInterval time.Duration // REMOVED
}

// NewSingletonTaskManager creates a new SingletonTaskManager with the given configuration
func NewSingletonTaskManager(config SingletonTaskManagerConfig, tasks []Task) (*SingletonTaskManager, error) {
	// Set default values if not provided
	if config.LeaseDuration == 0 {
		config.LeaseDuration = defaultLeaseDuration
	}
	if config.RenewDeadline == 0 {
		config.RenewDeadline = defaultRenewDeadline
	}
	if config.RetryPeriod == 0 {
		config.RetryPeriod = defaultRetryPeriod
	}

	// Get hostname for identity
	id, err := os.Hostname()
	if err != nil {
		return nil, err
	}

	// Setup Kubernetes client
	kubeConfig, err := rest.InClusterConfig()
	if err != nil {
		return nil, err
	}

	clientset, err := kubernetes.NewForConfig(kubeConfig)
	if err != nil {
		return nil, err
	}

	job := &SingletonTaskManager{
		lockName:      config.LockName,
		namespace:     config.Namespace,
		leaseDuration: config.LeaseDuration,
		renewDeadline: config.RenewDeadline,
		retryPeriod:   config.RetryPeriod,
		// taskInterval:  config.TaskInterval, // REMOVED
		isLeader:  false,
		clientset: clientset,
		identity:  id,
		tasks:     tasks,
	}

	return job, nil
}

// Start begins the leader election process and task execution
func (s *SingletonTaskManager) Start(ctx context.Context) error {
	// Create the lock
	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      s.lockName,
			Namespace: s.namespace,
		},
		Client: s.clientset.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: s.identity,
		},
	}

	// Setup leader election config
	leaderElectionConfig := leaderelection.LeaderElectionConfig{
		Lock:            lock,
		LeaseDuration:   s.leaseDuration,
		RenewDeadline:   s.renewDeadline,
		RetryPeriod:     s.retryPeriod,
		ReleaseOnCancel: true, // Release lease on context cancel for faster failover
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(runCtx context.Context) { // Use runCtx provided by elector
				log.Info().Str("identity", s.identity).Msg("Elected leader, starting tasks")
				s.isLeader = true
				s.startAllTasks(runCtx) // Pass the elector's context to tasks
			},
			OnStoppedLeading: func() {
				log.Info().Str("identity", s.identity).Msg("Stopped being leader, stopping tasks")
				s.isLeader = false
				s.stopAllTasks()
			},
			OnNewLeader: func(identity string) {
				if identity == s.identity {
					// Current instance is already the leader
					return
				}
				log.Info().Str("newLeader", identity).Msg("New leader elected")
			},
		},
	}

	var err error
	s.leaderElector, err = leaderelection.NewLeaderElector(leaderElectionConfig)
	if err != nil {
		return err
	}

	// Run leader election in background
	go s.leaderElector.Run(ctx) // Use the main context for the elector itself

	return nil
}

// Stop gracefully stops the SingletonTaskManager and all managed tasks.
// It attempts to stop all tasks regardless of leadership status.
func (s *SingletonTaskManager) Stop() {
	log.Info().Str("identity", s.identity).Msg("SingletonTaskManager stopping, ensuring all tasks are stopped")
	s.stopAllTasks()
	// Note: The leaderElector itself is stopped by canceling the context passed to its Run method.
	// If SingletonTaskManager.Stop() is called, the caller is also responsible for canceling the main context.
}

// IsLeader returns whether this instance is currently the leader
func (s *SingletonTaskManager) IsLeader() bool {
	return s.isLeader
}

// startAllTasks starts all managed tasks.
func (s *SingletonTaskManager) startAllTasks(ctx context.Context) {
	log.Info().Str("identity", s.identity).Int("task_count", len(s.tasks)).Msg("Starting all tasks")
	for _, task := range s.tasks {
		taskCtx := log.Ctx(ctx).With().Str("task_name", task.Name()).Logger().WithContext(ctx)
		task.Start(taskCtx)
	}
}

// stopAllTasks stops all managed tasks.
func (s *SingletonTaskManager) stopAllTasks() {
	log.Info().Str("identity", s.identity).Int("task_count", len(s.tasks)).Msg("Stopping all tasks")
	for _, task := range s.tasks {
		task.Stop()
	}
}

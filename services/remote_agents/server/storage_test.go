package main

import (
	"context"
	"testing"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	"github.com/augmentcode/augment/base/go/secretstring"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Helper function to create a test AgentEntity
func createTestAgentEntity(oldWorkspaceStatus, newWorkspaceStatus remoteagentsproto.WorkspaceStatus, agentStatus remoteagentsproto.AgentStatus) *AgentEntity {
	return &AgentEntity{
		statusInStorage: &entitiesproto.AgentStatus{
			WorkspaceStatus: oldWorkspaceStatus,
			Status:          remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, // Old agent status doesn't matter for current validation
		},
		Status: &entitiesproto.AgentStatus{
			WorkspaceStatus: newWorkspaceStatus,
			Status:          agentStatus,
		},
		AgentID: "test-agent-id",
	}
}

// TestValidateStateTransition_CurrentBehavior tests the current behavior of ValidateStateTransition
func TestValidateStateTransition_CurrentBehavior(t *testing.T) {
	tests := []struct {
		name        string
		oldWS       remoteagentsproto.WorkspaceStatus
		newWS       remoteagentsproto.WorkspaceStatus
		agentStatus remoteagentsproto.AgentStatus
		expectError bool
		errorMsg    string
	}{
		// Valid workspace transitions
		{"Valid: RUNNING to PAUSING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: PAUSING to PAUSED", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: PAUSED to RESUMING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: RESUMING to RUNNING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},
		{"Valid: UNSPECIFIED to RUNNING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_UNSPECIFIED, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, false, ""},

		// Valid agent statuses
		{"Valid: IDLE agent", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, false, ""},
		{"Valid: FAILED agent", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED, false, ""},

		// Invalid workspace transitions
		{"Invalid: RUNNING to PAUSED", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, true, "invalid workspace status transition"},
		{"Invalid: PAUSED to RUNNING", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, true, "invalid workspace status transition"},

		// Invalid agent statuses
		{"Invalid: STARTING agent", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING, true, "invalid agent status transition"},
		{"Invalid: PENDING agent", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING, true, "invalid agent status transition"},
		{"Invalid: PAUSING agent", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, remoteagentsproto.AgentStatus_AGENT_STATUS_PAUSING, true, "invalid agent status transition"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := createTestAgentEntity(tt.oldWS, tt.newWS, tt.agentStatus)
			err := entity.ValidateStateTransition()

			if tt.expectError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestWriteChatHistoryChunk(t *testing.T) {
	bigtableProxyClient := realBigtableProxyClient

	tenantID := "test-tenant"
	userId := "test-user"
	agentId := "test-agent"
	requestContext := requestcontext.New(
		requestcontext.RequestId(uuid.New().String()),
		requestcontext.RequestSessionId("test-session-id"),
		"test-source",
		secretstring.New("test-token"),
	)

	// Try writing chat history from empty state
	requestId := "request-id-1"
	responseText := "Hello"
	chatHistory := []*remoteagentsproto.ChatHistoryExchange{
		{
			SequenceId: 1,
			Exchange: &chatproto.Exchange{
				RequestMessage: "Hello",
				ResponseText:   &responseText,
				RequestId:      &requestId,
			},
			FinishedAt: timestamppb.Now(),
		},
	}
	err := writeChatHistoryChunk(
		context.Background(),
		requestContext,
		bigtableProxyClient,
		tenantID,
		userId,
		agentId,
		chatHistory,
	)
	assert.NoError(t, err)

	// Delete the timestamp cell, to test backward compatibility
	_, err = bigtableProxyClient.MutateRows(
		context.Background(),
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentChatHistoryRowKey(agentId, 1),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromColumn_{
							DeleteFromColumn: &bigtableproto.Mutation_DeleteFromColumn{
								FamilyName:      timestampFamilyName,
								ColumnQualifier: []byte(timestampColumn),
							},
						},
					},
				},
			},
		},
		requestContext,
	)
	assert.NoError(t, err)

	// Try writing chat history again with the same sequence ID
	requestId = "request-id-2"
	responseText = "Hello, world"
	chatHistory = []*remoteagentsproto.ChatHistoryExchange{
		{
			SequenceId: 1,
			Exchange: &chatproto.Exchange{
				RequestMessage: "Hello",
				ResponseText:   &responseText,
				RequestId:      &requestId,
			},
			FinishedAt: timestamppb.Now(),
		},
	}
	err = writeChatHistoryChunk(
		context.Background(),
		requestContext,
		bigtableProxyClient,
		tenantID,
		userId,
		agentId,
		chatHistory,
	)
	assert.NoError(t, err)

	// Try one more time, with existing data in the timestamp column
	requestId = "request-id-3"
	responseText = "Hello, world!!!"
	chatHistory = []*remoteagentsproto.ChatHistoryExchange{
		{
			SequenceId: 1,
			Exchange: &chatproto.Exchange{
				RequestMessage: "Hello",
				ResponseText:   &responseText,
				RequestId:      &requestId,
			},
			FinishedAt: timestamppb.Now(),
		},
	}
	err = writeChatHistoryChunk(
		context.Background(),
		requestContext,
		bigtableProxyClient,
		tenantID,
		userId,
		agentId,
		chatHistory,
	)
	assert.NoError(t, err)

	// Read the row back and verify the value
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	rows, err := bigtableProxyClient.ReadRows(
		context.Background(),
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{RowKeys: [][]byte{getRemoteAgentChatHistoryRowKey(agentId, 1)}},
		filter,
		0,
		requestContext,
	)
	assert.NoError(t, err)
	assert.Len(t, rows, 1)
	assert.Len(t, rows[0].Cells, 1)
	// unmarshal
	output := &entitiesproto.AgentOutput{}
	err = proto.Unmarshal(rows[0].Cells[0].Value, output)
	assert.NoError(t, err)
	assert.Len(t, output.Exchange, 1)
	assert.NotNil(t, output.Exchange[0].Exchange.ResponseText)
	assert.Equal(t, "Hello, world!!!", *output.Exchange[0].Exchange.ResponseText)
}

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'remote-agents',
      kubecfg: {
        target: '//services/remote_agents/server:kubecfg',
        // Skip EU for now, we don't have an agent cluster set up there yet
        task: [ns for ns in tenantNamespaces.namespaces if ns.cloud != 'GCP_EU_WEST4_PROD'],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['aswin', 'mattm', 'mike', 'mpauly', 'vaibhav'],
          slack_channel: '#team-remote-agent',
        },
      },
    },
  ],
}

// Package main implements the Remote Agents server functionality.
//
// This file contains the WorkspaceProcessor, which is responsible for processing
// workspace update data and generating appropriate streaming updates for clients.
//
// WorkspaceProcessor is separated from the WorkspaceStreamer to facilitate unit
// testing by isolating the logic from BigTable, gRPC streams, and other dependencies.
//
// It handles:
//  1. Processing updates when clients reconnect to a stream
//  2. Tracking sequence IDs to ensure only new updates are sent to clients
package main

import (
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
)

// WorkspaceProcessor handles the business logic of processing workspace updates
type WorkspaceProcessor struct {
	lastProcessedSequenceId uint32
}

// NewWorkspaceProcessor creates a new WorkspaceProcessor
func NewWorkspaceProcessor(lastProcessedSequenceId uint32) *WorkspaceProcessor {
	return &WorkspaceProcessor{
		lastProcessedSequenceId: lastProcessedSequenceId,
	}
}

// ProcessWorkspaceUpdates processes pending workspace updates
// It takes the pending updates directly from BigTable
func (p *WorkspaceProcessor) ProcessWorkspaceUpdates(
	pendingUpdates []*entitiesproto.PendingAgentUpdate,
) []*remoteagentsproto.WorkspaceStreamUpdate {
	var updates []*remoteagentsproto.WorkspaceStreamUpdate

	// Process each pending update
	for _, pendingUpdate := range pendingUpdates {
		// Skip updates that have already been processed
		if pendingUpdate.SequenceId <= p.lastProcessedSequenceId {
			continue
		}

		// Create a workspace stream update based on the pending update
		update := p.createWorkspaceStreamUpdate(pendingUpdate)
		if update != nil {
			updates = append(updates, update)
			// Update the last processed sequence ID
			if pendingUpdate.SequenceId > p.lastProcessedSequenceId {
				p.lastProcessedSequenceId = pendingUpdate.SequenceId
			}
		}
	}

	return updates
}

// createWorkspaceStreamUpdate creates a WorkspaceStreamUpdate from a PendingAgentUpdate
func (p *WorkspaceProcessor) createWorkspaceStreamUpdate(
	pendingUpdate *entitiesproto.PendingAgentUpdate,
) *remoteagentsproto.WorkspaceStreamUpdate {
	if pendingUpdate == nil || pendingUpdate.Update == nil {
		return nil
	}

	// Create a new WorkspaceStreamUpdate
	streamUpdate := &remoteagentsproto.WorkspaceStreamUpdate{
		SequenceId: pendingUpdate.SequenceId,
	}

	// Set the appropriate fields based on the update type
	switch u := pendingUpdate.Update.Update.(type) {
	case *remoteagentsproto.WorkspaceUpdate_Interrupt:
		streamUpdate.Type = remoteagentsproto.WorkspaceUpdateType_WORKSPACE_UPDATE_INTERRUPT
		streamUpdate.Interrupt = u.Interrupt
	case *remoteagentsproto.WorkspaceUpdate_ChatRequest:
		streamUpdate.Type = remoteagentsproto.WorkspaceUpdateType_WORKSPACE_UPDATE_CHAT_REQUEST
		streamUpdate.ChatRequest = u.ChatRequest
	default:
		// Unknown update type
		return nil
	}

	return streamUpdate
}

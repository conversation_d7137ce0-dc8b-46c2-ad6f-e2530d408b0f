syntax = "proto3";
package services.tenant;

import "google/protobuf/duration.proto";

option go_package = "github.com/augmentcode/augment/services/tenant_watcher/proto";

// global service that gives informs services esp services running in shard
// namespaces about the tenants
service TenantWatcher {
  // Main API for other services to use.
  //
  // Watch for tenant changes to all tenants that are currently configured for
  // the given shard. A shard cannot watch tenants not assigned to the shard.
  // The shard namespace is extracted from the MTLS certificate. If the caller
  // is from a central namespace or if MTLS is disabled (testing), and the
  // namespace in the request is empty, then all tenants are returned.
  //
  // It will return all current information (see is_initial) and then any
  // future updates. The stream is long running, like a patch stream.
  //
  // if there are no tenants, an empty list is returned with is_initial set to true
  //
  // this rpc call doesn't require a service token
  rpc WatchTenants(WatchTenantsRequest) returns (stream WatchTenantsResponse) {}

  // Like WatchTenants, but only returns the current state. This is meant to
  // be used by tools and UIs, most services should use WatchTenants() rather
  // than calling GetTenants() repeatedly.
  //
  // this rpc call doesn't require a service token
  rpc GetTenants(GetTenantsRequest) returns (GetTenantsResponse) {}

  // Creates a new tenant with the provided configuration.
  // This operation is restricted to central services or admin users.
  //
  // Requires AUTH_RW scope
  rpc CreateTenant(CreateTenantRequest) returns (CreateTenantResponse) {}

  // Updates an existing tenant with the provided configuration.
  // This operation is restricted to central services or admin users.
  //
  // Requires AUTH_RW scope
  rpc UpdateTenant(UpdateTenantRequest) returns (UpdateTenantResponse) {}
}

message GetTenantsRequest {
  // the namespace of the shard tenants to return
  //
  // if the requets is from a shard namespace, an error is returned if the shard namespace
  // doesn't match the caller
  string shard_namespace = 1;
}

message WatchTenantsRequest {
  // the namespace of the shard tenants to watch
  //
  // if the requets is from a shard namespace, an error is returned if the shard namespace
  // doesn't match the caller
  string shard_namespace = 1;
}

message AuthConfiguration {
  // if set, the domain whose users are routed to the tenant
  string domain = 1;

  // a username with one of these domains will get routed
  // to the tenant.
  repeated string username_domains = 2;

  // an e-mail address with one of these domains will get
  // routed to the tenant.
  repeated string email_address_domains = 3;

  // if set, the identity providers which are allowed to authenticate
  // users.
  //
  // for now, we identify identity providers using auth0 prefix
  // e.g. google-oauth2 or okta|foo.com
  repeated string allowed_identity_providers = 4;
}

message Config {
  // a map of key value pairs
  // note any Tenant configuration that is not a string will be serialized as a JSON string, e.g.
  // boolean true will be serialized as "true", integer 1 will be serialized as "1"
  map<string, string> configs = 1;
}

// Define an enum for tenant tiers
enum TenantTier {
  // Default value for proto3 enums must be 0
  TENANT_TIER_UNKNOWN = 0;

  // most enterprises and large orgs are a enterprise tenant
  ENTERPRISE = 1;

  // smaller orgs, teams, and individuals are professional tenants
  // this includes trail usage, and different paid plans
  // A better name could be "PAID". In some sense, it is everyone
  // who is not ENTERPRISE and NOT COMMUNITY
  PROFESSIONAL = 2;

  // community tenants contain uses on the COMMUNITY plan. This plan
  // allows training on user data.
  COMMUNITY = 3;
}

message Tenant {
  // a securely generated unique id for a tenant
  string id = 1;

  // the name of the tenant, e.g. for metrics
  string name = 2;

  // the namespace of the shard the tenant belongs to
  string shard_namespace = 3;

  // the cloud the tenant is running on
  // A tenant might migrate between different shards, but cannot migrate between clouds
  string cloud = 4;

  // the tier of the tenant
  TenantTier tier = 12;

  // the auth configuration for the tenant (optional)
  // the auth configuration will be used by the auth services (e.g. auth central) to configure the
  // tenant and route users to the tenant
  AuthConfiguration auth_configuration = 5;

  // the configuration for the tenant (optional)
  Config config = 6;

  // the time when the tenant was deleted
  // set to a non-empty string if and only if the tenant was deleted
  string deleted_at = 7;

  // If set, a namespace that the tenant is allowed to operate in but is not
  // preferred. This is mainly to be used when migrating tenants between namespaces.
  // Such a migration might look something like this:
  // 1. namespace "foo", other namespace ""
  //    This is a normal state for most tenants
  // 2. namespace "foo", other namespace "bar"
  //    We want to move migrate this tenant from foo to bar. This is a first
  //    step, to make sure that all relevant pods (token-exchange, auth, etc)
  //    are aware of the move before we start trying to use the new
  //    namespace.
  // 3. namespace "bar", other namespace "foo"
  //    Now we start using the new namespace for any new logins, but do not log
  //    out old clients yet, since not all auth-central replicas may be aware of
  //    this change. Wait for all auth-central replicas to be aware of the change.
  // 4. Add the tenant to the list of migrated_tenant_ids in namespace "foo"
  //    Now it is safe to start sending all users to the new namespace, so log
  //    out any sessions on the old namespace. We still want to allow
  //    operations in the old namespace, though, particularly for background
  //    services to clear pubsub queues in the old namespace.
  // 5. namespace "bar", other namespace ""
  //    Migration is complete.
  string other_namespace = 8;

  // Name of the KMS key to use for tenant encryption, if any.
  // If not set, tenant encryption is disabled.
  // If set, the tenant will be secured using the specified KMS key.
  string encryption_key_name = 9;

  // TTL for the encryption key, if any. If not set, defaults to 24 hours.
  google.protobuf.Duration encryption_key_ttl = 10;

  // The version of the tenant. This is used to determine if the tenant has been updated.
  string version = 11;
}

message GetTenantsResponse {
  repeated Tenant tenants = 1;
}

message TenantUpdate {
  Tenant tenant = 1;
}

message TenantRemoval {
  Tenant tenant = 1;
}

message TenantChange {
  // this can be extended (in various ways) to include tenant destruction.
  oneof type {
    // a tenant has been added or updated. There is no indication if the tenant has been
    // newly created or migrated from a different shard
    TenantUpdate updated = 1;
    // a tenant has been removed from the shard.
    TenantRemoval removed = 2;
  }
}

message WatchTenantsResponse {
  // a list of changes to tenants
  //
  // the list might be empty for a response
  // if the list is empty and is_initial is true, there no tenants in the shard
  repeated TenantChange tenants = 1;

  // True if this is part of the initial list of tenants. There should be
  // exactly one response with a value of true, and it should always be the
  // first response. All later responses will have a value of False here.
  bool is_initial = 2;
}

message CreateTenantRequest {
  // The name of the tenant to create
  // If not provided, the internally generated tenant id will be used as the name
  string name = 1;

  // The namespace of the shard the tenant should belong to
  string shard_namespace = 2;

  // The cloud the tenant will run on
  string cloud = 3;

  // The tier of the tenant
  TenantTier tier = 6;

  // The auth configuration for the tenant (optional)
  AuthConfiguration auth_configuration = 4;

  // The configuration for the tenant (optional)
  Config config = 5;
}

message CreateTenantResponse {
  // The created tenant with all fields populated, including the generated ID
  Tenant tenant = 1;
}

message UpdateTenantRequest {
  // The created tenant with all fields populated, except the generated ID
  //
  // tenant name is used as a key, so it cannot be changed
  // tenant id should not be set and cannot be changed
  Tenant tenant = 1;
}

message UpdateTenantResponse {
  // The updated tenant with all fields populated
  Tenant tenant = 1;
}

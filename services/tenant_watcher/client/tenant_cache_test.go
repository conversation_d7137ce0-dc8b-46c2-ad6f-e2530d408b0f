package client

import (
	"context"
	"errors"
	"testing"

	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func createUpdate(initial bool, updated, removed []string) *tenantproto.WatchTenantsResponse {
	resp := &tenantproto.WatchTenantsResponse{
		Tenants:   make([]*tenantproto.TenantChange, 0, len(updated)+len(removed)),
		IsInitial: initial,
	}
	for _, id := range updated {
		resp.Tenants = append(resp.Tenants, &tenantproto.TenantChange{
			Type: &tenantproto.TenantChange_Updated{
				Updated: &tenantproto.TenantUpdate{
					Tenant: &tenantproto.Tenant{
						Id: id,
					},
				},
			},
		})
	}
	for _, id := range removed {
		resp.Tenants = append(resp.Tenants, &tenantproto.TenantChange{
			Type: &tenantproto.TenantChange_Removed{
				Removed: &tenantproto.TenantRemoval{
					Tenant: &tenantproto.Tenant{
						Id: id,
					},
				},
			},
		})
	}
	return resp
}

func checkTenants(t *testing.T, cache TenantCache, expected, notExpected []string) {
	t.Helper()
	for _, id := range expected {
		tenant, err := cache.GetTenant(id)
		if err != nil {
			t.Fatalf("expected tenant %q, got %v", id, err)
		}
		if tenant.Id != id {
			t.Fatalf("expected tenant %q, got %q", id, tenant.Id)
		}
	}

	for _, id := range notExpected {
		_, err := cache.GetTenant(id)
		if err == nil {
			t.Fatalf("expected error for tenant %q", id)
		}
		if !errors.Is(err, ErrTenantNotFound) {
			t.Fatalf("expected TenantNotFound error for tenant %q, got %v", id, err)
		}
	}
}

func TestTenantCache(t *testing.T) {
	changes := make(chan TenantChange, 1)
	mockClient := &MockTenantWatcherClient{
		Changes: []chan TenantChange{changes},
	}

	// Initial state
	changes <- TenantChange{
		Response: createUpdate(true, []string{"a", "b"}, nil),
	}

	tc, updateCh := NewTestTenantCache(t, mockClient, "test")
	<-updateCh

	checkTenants(t, tc, []string{"a", "b"}, []string{"c", "d"})

	// An empty update should cause no changes
	changes <- TenantChange{
		Response: &tenantproto.WatchTenantsResponse{},
	}
	<-updateCh

	checkTenants(t, tc, []string{"a", "b"}, []string{"c", "d"})

	// Add c, update b, remove a
	changes <- TenantChange{
		Response: createUpdate(false, []string{"b", "c"}, []string{"a"}),
	}
	<-updateCh

	checkTenants(t, tc, []string{"b", "c"}, []string{"a", "d"})
}

func TestTenantCacheRetries(t *testing.T) {
	mockClient := &MockTenantWatcherClient{}

	// Start off with an unretriable error, which should panic
	mockClient.Changes = append(mockClient.Changes, make(chan TenantChange, 1))
	mockClient.Changes[0] <- TenantChange{
		Err: errors.New("temporary test error"),
	}
	close(mockClient.Changes[0])
	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	func() {
		defer func() {
			if r := recover(); r == nil {
				t.Fatal("expected panic, got none")
			}
		}()
		tc := &tenantCache{
			client: mockClient,
		}
		tc.start(ctx)
	}()

	// Now start off with a retriable error, which should be retried
	mockClient.Changes = append(mockClient.Changes, make(chan TenantChange, 1))
	mockClient.Changes[1] <- TenantChange{
		Err: status.Error(codes.Unavailable, "temporary test error"),
	}
	close(mockClient.Changes[1])

	mockClient.Changes = append(mockClient.Changes, make(chan TenantChange, 1))
	mockClient.Changes[2] <- TenantChange{
		Response: createUpdate(true, []string{"a", "b"}, nil),
	}
	tc, updateCh := NewTestTenantCache(t, mockClient, "test")
	<-updateCh

	checkTenants(t, tc, []string{"a", "b"}, []string{"c", "d"})

	// Add c and remove a
	mockClient.Changes[2] <- TenantChange{
		Response: createUpdate(false, []string{"c"}, []string{"a"}),
	}
	<-updateCh

	checkTenants(t, tc, []string{"b", "c"}, []string{"a", "d"})

	// This error is retryable, so we should get the same result
	mockClient.Changes[2] <- TenantChange{
		Err: status.Error(codes.Unavailable, "temporary test error"),
	}
	close(mockClient.Changes[2])

	checkTenants(t, tc, []string{"b", "c"}, []string{"a", "d"})

	// Let's say b was removed in the meantime
	mockClient.Changes = append(mockClient.Changes, make(chan TenantChange, 1))
	mockClient.Changes[3] <- TenantChange{
		Response: createUpdate(true, []string{"c"}, []string{"a"}),
	}
	<-updateCh

	checkTenants(t, tc, []string{"c"}, []string{"a", "d"})
}

func TestTenantCacheDeleteNonexistentTenant(t *testing.T) {
	mockClient := &MockTenantWatcherClient{}

	// Try deleting a tenant that does not exist
	mockClient.Changes = append(mockClient.Changes, make(chan TenantChange, 1))
	mockClient.Changes[0] <- TenantChange{
		Response: createUpdate(true, []string{"a", "b"}, []string{"c"}),
	}
	tc, updateCh := NewTestTenantCache(t, mockClient, "test")
	<-updateCh

	checkTenants(t, tc, []string{"a", "b"}, []string{"c", "d"})
}

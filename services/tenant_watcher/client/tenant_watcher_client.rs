use backoff::{backoff::Backoff, ExponentialBackoff};
use std::{sync::Arc, time::Duration};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::{create_channel, receiver_for_error, receiver_for_stream};
use mockall::automock;
use std::collections::HashMap;
use std::future::pending;
use tokio::sync::mpsc::{self, Receiver};
use tokio::sync::Notify;
use tonic::transport::ClientTlsConfig;
use tracing_tonic::client::TracingService;

pub mod tenant_watcher {
    tonic::include_proto!("services.tenant");
}

#[async_trait]
pub trait TenantWatcherClient {
    async fn get_tenants(
        &self,
        shard_namespace: &str,
    ) -> tonic::Result<Vec<tenant_watcher::Tenant>>;
    async fn watch_tenants(
        &self,
        shard_namespace: &str,
    ) -> Receiver<tonic::Result<tenant_watcher::WatchTenantsResponse>>;
}

#[derive(Clone)]
pub struct TenantWatcherClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Duration,
    client: Arc<
        Mutex<Option<tenant_watcher::tenant_watcher_client::TenantWatcherClient<TracingService>>>,
    >,
}

#[async_trait]
impl TenantWatcherClient for TenantWatcherClientImpl {
    async fn get_tenants(
        &self,
        shard_namespace: &str,
    ) -> tonic::Result<Vec<tenant_watcher::Tenant>> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("tenant_watcher client not ready: {}", e);
            tonic::Status::unavailable("tenant_watcher not ready")
        })?;
        let request = tonic::Request::new(tenant_watcher::GetTenantsRequest {
            shard_namespace: shard_namespace.to_string(),
        });
        let response = client.get_tenants(request).await?;
        Ok(response.into_inner().tenants)
    }

    async fn watch_tenants(
        &self,
        shard_namespace: &str,
    ) -> Receiver<tonic::Result<tenant_watcher::WatchTenantsResponse>> {
        let mut client = match self.get_client().await {
            Ok(c) => c,
            Err(e) => {
                tracing::error!("tenant_watcher client not ready: {}", e);
                return receiver_for_error(tonic::Status::unavailable("tenant_watcher not ready"));
            }
        };

        let request = tonic::Request::new(tenant_watcher::WatchTenantsRequest {
            shard_namespace: shard_namespace.to_string(),
        });
        let response = client.watch_tenants(request).await;
        match response {
            Err(e) => receiver_for_error(e),
            Ok(r) => receiver_for_stream(r, None),
        }
    }
}

impl TenantWatcherClientImpl {
    pub fn new(
        endpoint: &str,
        tls_config: Option<ClientTlsConfig>,
        request_timeout: Duration,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            request_timeout,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        tenant_watcher::tenant_watcher_client::TenantWatcherClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    Some(self.request_timeout),
                    &self.tls_config,
                )
                .await?;
                let client =
                    tenant_watcher::tenant_watcher_client::TenantWatcherClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

pub struct MockTenantWatcherClient {
    pub tenants: Vec<tenant_watcher::Tenant>,
}

impl MockTenantWatcherClient {
    pub fn new(tenants: Vec<tenant_watcher::Tenant>) -> Self {
        Self { tenants }
    }
}

#[async_trait]
impl TenantWatcherClient for MockTenantWatcherClient {
    async fn get_tenants(
        &self,
        shard_namespace: &str,
    ) -> tonic::Result<Vec<tenant_watcher::Tenant>> {
        Ok(self
            .tenants
            .iter()
            .filter(|tenant| tenant.shard_namespace == shard_namespace)
            .cloned()
            .collect::<Vec<tenant_watcher::Tenant>>())
    }

    async fn watch_tenants(
        &self,
        shard_namespace: &str,
    ) -> Receiver<tonic::Result<tenant_watcher::WatchTenantsResponse>> {
        let (tx, rx) = mpsc::channel(1);
        // TODO: something more interesting here?
        tx.send(Ok(tenant_watcher::WatchTenantsResponse {
            tenants: self
                .tenants
                .iter()
                .filter(|tenant| tenant.shard_namespace == shard_namespace)
                .map(|tenant| tenant_watcher::TenantChange {
                    r#type: Some(tenant_watcher::tenant_change::Type::Updated(
                        tenant_watcher::TenantUpdate {
                            tenant: Some(tenant.clone()),
                        },
                    )),
                })
                .collect::<Vec<_>>(),
            is_initial: true,
        }))
        .await
        .unwrap();
        rx
    }
}

/// A client for accessing tenant information from a local cache.
///
/// The `TenantCacheClient` trait provides an interface for retrieving tenant information
/// from a local cache that is kept up-to-date with the tenant watcher service. This allows
/// services to quickly access tenant information without making remote calls for each request.
///
/// # Important Usage Notes
///
/// - The `run()` method MUST be called to start the background process that keeps the cache updated.
///   Typically, this should be called once when the service starts and run in a separate task.
/// - The `get_tenant()` and `get_tenant_by_name()` methods will BLOCK until the cache is initialized.
///   This ensures that tenant information is available before proceeding.
///
/// Implementations of this trait typically:
/// - Maintain a local cache of tenant information
/// - Keep the cache updated by watching for changes from the tenant watcher service
/// - Provide methods to retrieve tenant information by ID or name
/// - Handle initialization and waiting for the cache to be populated
///
/// # Example Usage
///
/// ```
/// use tenant_watcher_client::{TenantCache, TenantWatcherClientImpl, TenantCacheClient};
/// use std::sync::Arc;
/// use std::time::Duration;
///
/// async fn example() {
///     // Create a tenant watcher client
///     let tenant_watcher_client = Arc::new(TenantWatcherClientImpl::new(
///         "tenant-watcher-endpoint",
///         None, // TLS config
///         Duration::from_secs(30),
///     ));
///
///     // Create a tenant cache client
///     let tenant_cache: Arc<dyn TenantCacheClient + Send + Sync> = Arc::new(
///         TenantCache::new(tenant_watcher_client, "shard-namespace".to_string())
///     );
///
///     // Start the tenant cache in a background task
///     // IMPORTANT: This must be called for the cache to work!
///     let tenant_cache_clone = tenant_cache.clone();
///     tokio::spawn(async move {
///         if let Err(e) = tenant_cache_clone.run().await {
///             eprintln!("Tenant cache error: {}", e);
///         }
///     });
///
///     // Wait for the cache to be initialized
///     tenant_cache.wait_until_initialized().await.expect("Failed to initialize tenant cache");
///
///     // Retrieve a tenant by ID
///     // Note: This will block until the cache is initialized
///     if let Some(tenant) = tenant_cache.get_tenant("tenant-id").await {
///         println!("Found tenant: {}", tenant.name);
///     }
/// }
/// ```
#[automock]
#[async_trait::async_trait]
pub trait TenantCacheClient {
    /// Retrieves a tenant by its ID.
    ///
    /// This method looks up a tenant in the cache by its unique identifier.
    ///
    /// # Important
    /// This method BLOCKS until the cache is initialized. This ensures that tenant
    /// information is available before proceeding, but it means the method may not
    /// return immediately if the cache is still being populated.
    ///
    /// # Arguments
    ///
    /// * `tenant_id` - The unique identifier of the tenant to retrieve
    ///
    /// # Returns
    ///
    /// * `Some(Tenant)` - If the tenant with the specified ID exists in the cache
    /// * `None` - If no tenant with the specified ID exists in the cache
    async fn get_tenant(&self, tenant_id: &str) -> Option<tenant_watcher::Tenant>;

    /// Retrieves a tenant by its name.
    ///
    /// This method looks up a tenant in the cache by its name.
    ///
    /// # Important
    /// This method BLOCKS until the cache is initialized. This ensures that tenant
    /// information is available before proceeding, but it means the method may not
    /// return immediately if the cache is still being populated.
    ///
    /// # Arguments
    ///
    /// * `tenant_name` - The name of the tenant to retrieve
    ///
    /// # Returns
    ///
    /// * `Some(Tenant)` - If the tenant with the specified name exists in the cache
    /// * `None` - If no tenant with the specified name exists in the cache
    async fn get_tenant_by_name(&self, tenant_name: &str) -> Option<tenant_watcher::Tenant>;

    /// Starts the tenant cache client and keeps it running.
    ///
    /// This method typically starts a background task that watches for tenant changes
    /// and updates the local cache accordingly.
    ///
    /// # Important
    /// This method MUST be called for the cache to work properly. It should be called once
    /// when the service starts and typically runs indefinitely. Usually, you'll want to run
    /// this in a separate task, as shown in the example.
    ///
    /// ```
    /// # use tenant_watcher_client::TenantCacheClient;
    /// # use std::sync::Arc;
    /// # async fn example(tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>) {
    /// // Start the tenant cache in a background task
    /// let tenant_cache_clone = tenant_cache.clone();
    /// tokio::spawn(async move {
    ///     if let Err(e) = tenant_cache_clone.run().await {
    ///         eprintln!("Tenant cache error: {}", e);
    ///     }
    /// });
    /// # }
    /// ```
    ///
    /// # Returns
    ///
    /// * `Ok(())` - If the client runs successfully (typically only when the service is shutting down)
    /// * `Err(tonic::Status)` - If there's an error running the client
    async fn run(&self) -> Result<(), tonic::Status>;

    /// Waits until the tenant cache is initialized with data.
    ///
    /// This method blocks until the cache has received its initial data from the
    /// tenant watcher service. Services should call this method before attempting
    /// to access tenant information to ensure the cache is populated.
    ///
    /// # Returns
    ///
    /// * `Ok(())` - If the cache is successfully initialized
    /// * `Err(tonic::Status)` - If there's an error initializing the cache
    async fn wait_until_initialized(&self) -> Result<(), tonic::Status>;

    /// Waits until the tenant cache is initialized with data, with a timeout.
    ///
    /// This method is similar to `wait_until_initialized`, but it will return an error
    /// if the cache is not initialized within the specified timeout duration.
    ///
    /// # Arguments
    ///
    /// * `timeout` - The maximum duration to wait for initialization
    ///
    /// # Returns
    ///
    /// * `Ok(())` - If the cache is successfully initialized within the timeout
    /// * `Err(tonic::Status)` - If there's an error initializing the cache or if the timeout expires
    async fn wait_until_initialized_with_timeout(
        &self,
        timeout: Duration,
    ) -> Result<(), tonic::Status>;
}

pub struct NullTenantCache {
    tenant: Option<tenant_watcher::Tenant>,
}

impl NullTenantCache {
    pub fn new(tenant: tenant_watcher::Tenant) -> Self {
        Self {
            tenant: Some(tenant),
        }
    }
}

#[async_trait::async_trait]
impl TenantCacheClient for NullTenantCache {
    async fn get_tenant(&self, tenant_id: &str) -> Option<tenant_watcher::Tenant> {
        if let Some(tenant) = &self.tenant {
            if tenant.id == tenant_id {
                Some(tenant.clone())
            } else {
                None
            }
        } else {
            None
        }
    }

    async fn get_tenant_by_name(&self, tenant_name: &str) -> Option<tenant_watcher::Tenant> {
        self.tenant.iter().find(|t| t.name == tenant_name).cloned()
    }

    async fn run(&self) -> Result<(), tonic::Status> {
        pending::<()>().await;
        Ok(())
    }

    async fn wait_until_initialized(&self) -> Result<(), tonic::Status> {
        Ok(())
    }

    async fn wait_until_initialized_with_timeout(
        &self,
        _timeout: Duration,
    ) -> Result<(), tonic::Status> {
        // NullTenantCache is always initialized
        Ok(())
    }
}

#[derive(Debug)]
struct TenantCacheData {
    tenant_id_cache: HashMap<String, tenant_watcher::Tenant>,
    tenant_name_cache: HashMap<String, tenant_watcher::Tenant>,
}

struct Event {
    notify: Notify,
    flag: tokio::sync::Mutex<bool>,
}

impl Event {
    fn new() -> Event {
        Event {
            notify: Notify::new(),
            flag: tokio::sync::Mutex::new(false),
        }
    }

    async fn set(&self) {
        let mut flag = self.flag.lock().await;
        *flag = true;
        self.notify.notify_waiters();
    }

    async fn get_value(&self) -> bool {
        let flag = self.flag.lock().await;
        *flag
    }

    async fn wait(&self) {
        let mut flag = self.get_value().await;
        while !flag {
            self.notify.notified().await;
            flag = self.get_value().await;
        }
    }

    async fn wait_with_timeout(&self, timeout: Duration) -> Result<(), tonic::Status> {
        // Create a timeout future
        let timeout_future = tokio::time::sleep(timeout);

        // Create a future that waits for the event
        let event_future = self.wait();

        // Race the two futures
        tokio::select! {
            _ = event_future => Ok(()),
            _ = timeout_future => Err(tonic::Status::deadline_exceeded(
                format!("Event wait timed out after {:?}", timeout)
            )),
        }
    }
}

pub struct TenantCache {
    client: Arc<dyn TenantWatcherClient + Send + Sync>,
    shard_namespace: String,
    data: Mutex<TenantCacheData>,
    initialized: Event,
}

#[async_trait::async_trait]
impl TenantCacheClient for TenantCache {
    async fn get_tenant(&self, tenant_id: &str) -> Option<tenant_watcher::Tenant> {
        self.initialized.wait().await;
        let d = self.data.lock().await;
        d.tenant_id_cache.get(tenant_id).cloned()
    }

    async fn get_tenant_by_name(&self, tenant_name: &str) -> Option<tenant_watcher::Tenant> {
        self.initialized.wait().await;
        let d = self.data.lock().await;
        d.tenant_name_cache.get(tenant_name).cloned()
    }

    async fn wait_until_initialized(&self) -> Result<(), tonic::Status> {
        self.initialized.wait().await;
        Ok(())
    }

    async fn wait_until_initialized_with_timeout(
        &self,
        timeout: Duration,
    ) -> Result<(), tonic::Status> {
        self.initialized.wait_with_timeout(timeout).await
    }

    async fn run(&self) -> Result<(), tonic::Status> {
        tracing::info!("Starting tenant cache");
        let mut backoff = ExponentialBackoff {
            initial_interval: Duration::from_secs(1),
            max_interval: Duration::from_secs(32),
            max_elapsed_time: None, // None means no time limit
            ..ExponentialBackoff::default()
        };
        loop {
            let s = self
                .client
                .watch_tenants(self.shard_namespace.as_str())
                .await;
            tokio::pin!(s);
            loop {
                let res = s.recv().await;
                if let Some(res) = res {
                    let b = match res {
                        Ok(tenant_change) => {
                            // Reset the backoff counter
                            backoff.reset();
                            self.update_tenants(tenant_change).await;
                            false
                        }
                        Err(e) => {
                            tracing::error!("Failed to receive tenant change: {:?}", e);
                            true
                        }
                    };
                    if b {
                        break;
                    }
                } else {
                    tracing::error!("Tenant change stream ended");
                    break;
                }
            }
            if let Some(duration) = backoff.next_backoff() {
                tracing::info!("Tenant cache sleeping for {:?}", duration);
                tokio::time::sleep(duration).await
            }
        }
    }
}

impl TenantCache {
    pub fn new(
        client: Arc<dyn TenantWatcherClient + Send + Sync>,
        shard_namespace: String,
    ) -> Self {
        Self {
            client,
            shard_namespace,
            data: Mutex::new(TenantCacheData {
                tenant_id_cache: HashMap::new(),
                tenant_name_cache: HashMap::new(),
            }),
            initialized: Event::new(),
        }
    }

    async fn handle_update(&self, update: &tenant_watcher::TenantUpdate) {
        let mut d = self.data.lock().await;
        if let Some(tenant) = &update.tenant {
            tracing::info!("Received update for tenant: {:?}", tenant);
            let remove_name = d
                .tenant_id_cache
                .iter()
                .filter(|(_, v)| v.name == tenant.name)
                .map(|(_, v)| v.name.clone())
                .next();

            if let Some(remove_name) = remove_name {
                d.tenant_name_cache.remove(&remove_name);
            }
            d.tenant_name_cache
                .insert(tenant.name.clone(), tenant.clone());
            d.tenant_id_cache.insert(tenant.id.clone(), tenant.clone());
        }
    }

    async fn handle_removal(&self, removal: &tenant_watcher::TenantRemoval) {
        let mut d = self.data.lock().await;
        if let Some(tenant) = &removal.tenant {
            tracing::info!("Received removal for tenant: {:?}", tenant);
            if let Some(existing_tenant) = d.tenant_id_cache.remove(&tenant.id) {
                d.tenant_name_cache.remove(&existing_tenant.name);
            } else {
                tracing::warn!("Received tenant removal for unknown tenant: {:?}", tenant);
            }
        }
    }

    async fn update_tenants(&self, tenant_change: tenant_watcher::WatchTenantsResponse) {
        for change in tenant_change.tenants.iter() {
            if let Some(change_type) = change.r#type.as_ref() {
                match change_type {
                    tenant_watcher::tenant_change::Type::Updated(update) => {
                        self.handle_update(update).await
                    }
                    tenant_watcher::tenant_change::Type::Removed(removal) => {
                        self.handle_removal(removal).await
                    }
                };
            } else {
                tracing::warn!("Received tenant change with no type: {:?}", change);
            }
        }
        if tenant_change.is_initial {
            tracing::info!("Received initial tenant change");
            self.initialized.set().await;
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_mock_get_tenants() {
        let tenants = vec![tenant_watcher::Tenant {
            name: "my_name".to_string(),
            shard_namespace: "my_namespace".to_string(),
            ..Default::default()
        }];
        let client = MockTenantWatcherClient::new(tenants.clone());
        assert_eq!(client.get_tenants("my_namespace").await.unwrap(), tenants);
    }

    #[tokio::test]
    async fn test_mock_watch_tenants() {
        let tenants = vec![tenant_watcher::Tenant {
            name: "my_name".to_string(),
            shard_namespace: "my_namespace".to_string(),
            ..Default::default()
        }];
        let client = MockTenantWatcherClient::new(tenants.clone());
        let mut rx = client.watch_tenants("my_namespace").await;
        let next = rx.recv().await;
        assert!(next.is_some());
        assert!(next.clone().unwrap().is_ok());
        assert_eq!(
            next.unwrap().unwrap().tenants,
            tenants
                .iter()
                .map(|tenant| tenant_watcher::TenantChange {
                    r#type: Some(tenant_watcher::tenant_change::Type::Updated(
                        tenant_watcher::TenantUpdate {
                            tenant: Some(tenant.clone()),
                        },
                    )),
                })
                .collect::<Vec<_>>(),
        );
        let next = rx.recv().await;
        assert!(next.is_none());
    }

    #[tokio::test]
    async fn test_delete_nonexistent_tenant() {
        let client = MockTenantWatcherClient::new(vec![]);
        let cache = TenantCache::new(Arc::new(client), "my_namespace".to_string());
        cache
            .handle_removal(&tenant_watcher::TenantRemoval {
                tenant: Some(tenant_watcher::Tenant {
                    name: "my_name".to_string(),
                    shard_namespace: "my_namespace".to_string(),
                    ..Default::default()
                }),
            })
            .await;
    }
}

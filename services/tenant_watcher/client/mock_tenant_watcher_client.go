package client

import (
	"context"

	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

type mockTenantWatcherClient struct {
	TenantWatcherClient
	tenants          map[string]*tenantproto.Tenant
	outgoingChannels []chan TenantChange
}

func MakeMockTenantWatcherClient(tenants map[string]*tenantproto.Tenant) TenantWatcherClient {
	return &mockTenantWatcherClient{
		tenants: tenants,
	}
}

func MakeMockTenantWatcherClientFromChannel(tenants chan *tenantproto.Tenant) TenantWatcherClient {
	client := &mockTenantWatcherClient{
		tenants: make(map[string]*tenantproto.Tenant),
	}
	go func() {
		for tenant := range tenants {
			client.tenants[tenant.Id] = tenant
			for _, ch := range client.outgoingChannels {
				ch <- TenantChange{
					Response: &tenantproto.WatchTenantsResponse{
						Tenants: []*tenantproto.TenantChange{
							{
								Type: &tenantproto.TenantChange_Updated{
									Updated: &tenantproto.TenantUpdate{
										Tenant: tenant,
									},
								},
							},
						},
					},
				}
			}
		}
	}()

	return client
}

func (m *mockTenantWatcherClient) GetTenants(ctx context.Context, shardNamespace string) ([]*tenantproto.Tenant, error) {
	ret := make([]*tenantproto.Tenant, 0)

	for _, tenant := range m.tenants {
		if tenant.ShardNamespace == shardNamespace {
			ret = append(ret, tenant)
		}
	}

	return ret, nil
}

func (m *mockTenantWatcherClient) WatchTenants(ctx context.Context, shardNamespace string) (<-chan TenantChange, error) {
	// make a channel and just send all tenants to it immediately
	ch := make(chan TenantChange)
	go func() {
		tenants, _ := m.GetTenants(ctx, shardNamespace)
		initial_tenant_changes := make([]*tenantproto.TenantChange, 0, len(tenants))
		for _, tenant := range tenants {
			initial_tenant_changes = append(initial_tenant_changes, &tenantproto.TenantChange{
				Type: &tenantproto.TenantChange_Updated{
					Updated: &tenantproto.TenantUpdate{
						Tenant: tenant,
					},
				},
			})
		}
		ch <- TenantChange{
			Response: &tenantproto.WatchTenantsResponse{
				Tenants:   initial_tenant_changes,
				IsInitial: true,
			},
		}
	}()

	m.outgoingChannels = append(m.outgoingChannels, ch)
	return ch, nil
}

func (m *mockTenantWatcherClient) Close() {}

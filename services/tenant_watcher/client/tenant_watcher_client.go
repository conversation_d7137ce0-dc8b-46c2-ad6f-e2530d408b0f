package client

import (
	"context"
	"errors"
	"io"
	"sync"

	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
)

type TenantWatcherClient interface {
	GetTenants(ctx context.Context, shardNamespace string) ([]*tenantproto.Tenant, error)
	WatchTenants(ctx context.Context, shardNamespace string) (<-chan TenantChange, error)
	CreateTenant(ctx context.Context, createRequest *tenantproto.CreateTenantRequest) (*tenantproto.Tenant, error)

	// This should be called to cleanup resources for this client
	Close()
}

// Contains either a tenant change or an error
type TenantChange struct {
	Response *tenantproto.WatchTenantsResponse
	Err      error
}

type tenantWatcherClientImpl struct {
	endpoint string
	grpcOpts []grpc.DialOption

	connLock sync.Mutex
	conn     *grpc.ClientConn
	client   tenantproto.TenantWatcherClient
}

func (cl *tenantWatcherClientImpl) getClient() (tenantproto.TenantWatcherClient, error) {
	cl.connLock.Lock()
	defer cl.connLock.Unlock()
	if cl.client == nil {
		log.Info().Msgf("Creating new tenant watcher client to %s with options %v", cl.endpoint, cl.grpcOpts)
		conn, err := grpc.NewClient(cl.endpoint, cl.grpcOpts...)
		if err != nil {
			return nil, err
		}
		cl.conn = conn
		cl.client = tenantproto.NewTenantWatcherClient(cl.conn)
	}
	return cl.client, nil
}

func (cl *tenantWatcherClientImpl) GetTenants(ctx context.Context, shardNamespace string) ([]*tenantproto.Tenant, error) {
	inner, err := cl.getClient()
	if err != nil {
		return nil, err
	}

	resp, err := inner.GetTenants(ctx, &tenantproto.GetTenantsRequest{ShardNamespace: shardNamespace})
	if err != nil {
		return nil, err
	}

	return resp.Tenants, nil
}

func (cl *tenantWatcherClientImpl) WatchTenants(ctx context.Context, shardNamespace string) (<-chan TenantChange, error) {
	inner, err := cl.getClient()
	if err != nil {
		return nil, err
	}

	stream, err := inner.WatchTenants(ctx, &tenantproto.WatchTenantsRequest{ShardNamespace: shardNamespace})
	if err != nil {
		return nil, err
	}

	ch := make(chan TenantChange)
	go func() {
		defer close(ch)
		for {
			resp, err := stream.Recv()
			if errors.Is(err, io.EOF) {
				return
			}
			if err != nil {
				ch <- TenantChange{Err: err}
				return
			}

			// Try to send response, but also handle context cancellation
			select {
			case ch <- TenantChange{Response: resp}:
			case <-ctx.Done():
				return
			}
		}
	}()

	return ch, nil
}

func (cl *tenantWatcherClientImpl) CreateTenant(ctx context.Context, createRequest *tenantproto.CreateTenantRequest) (*tenantproto.Tenant, error) {
	inner, err := cl.getClient()
	if err != nil {
		return nil, err
	}

	resp, err := inner.CreateTenant(ctx, createRequest)
	if err != nil {
		return nil, err
	}

	return resp.Tenant, err
}

func (cl *tenantWatcherClientImpl) Close() {
	cl.connLock.Lock()
	defer cl.connLock.Unlock()
	if cl.conn != nil {
		cl.conn.Close()
	}
}

type MockTenantWatcherClient struct {
	Tenants []*tenantproto.Tenant

	// If set, this is a list of channels, where each call to WatchTenants
	// will return the next channel in the list
	Changes  []chan TenantChange
	nextChan int
}

func (c *MockTenantWatcherClient) GetTenants(ctx context.Context, shardNamespace string) ([]*tenantproto.Tenant, error) {
	return c.Tenants, nil
}

func (c *MockTenantWatcherClient) WatchTenants(ctx context.Context, shardNamespace string) (<-chan TenantChange, error) {
	if c.Changes != nil {
		next := c.Changes[c.nextChan]
		c.nextChan += 1
		return next, nil
	}
	// Building these objects is annoying, so have an easy fallback for tests
	// that only want to specify the single list of tenants
	changeCh := make(chan TenantChange, 1)
	initial := TenantChange{
		Response: &tenantproto.WatchTenantsResponse{
			Tenants:   make([]*tenantproto.TenantChange, 0, len(c.Tenants)),
			IsInitial: true,
		},
	}
	for _, tenant := range c.Tenants {
		initial.Response.Tenants = append(initial.Response.Tenants, &tenantproto.TenantChange{
			Type: &tenantproto.TenantChange_Updated{
				Updated: &tenantproto.TenantUpdate{
					Tenant: tenant,
				},
			},
		})
	}
	changeCh <- initial
	return changeCh, nil
}

func (c *MockTenantWatcherClient) CreateTenant(ctx context.Context, createRequest *tenantproto.CreateTenantRequest) (*tenantproto.Tenant, error) {
	tenantId := uuid.New().String()
	tenantName := createRequest.Name
	if tenantName == "" {
		tenantName = tenantId
	}
	newTenant := &tenantproto.Tenant{
		Id:                tenantId,
		Name:              tenantName,
		ShardNamespace:    createRequest.ShardNamespace,
		Cloud:             createRequest.Cloud,
		Tier:              createRequest.Tier,
		AuthConfiguration: createRequest.AuthConfiguration,
		Config:            createRequest.Config,
	}
	c.Tenants = append(c.Tenants, newTenant)

	if c.Changes != nil && c.nextChan > 0 {
		c.Changes[c.nextChan-1] <- TenantChange{
			Response: &tenantproto.WatchTenantsResponse{
				Tenants: []*tenantproto.TenantChange{
					{
						Type: &tenantproto.TenantChange_Updated{
							Updated: &tenantproto.TenantUpdate{
								Tenant: newTenant,
							},
						},
					},
				},
			},
		}
	}

	return newTenant, nil
}

func (c *MockTenantWatcherClient) Close() {
	// Let creators of each channel close them
}

func New(endpoint string, grpcOpts ...grpc.DialOption) TenantWatcherClient {
	opts := append(grpcOpts, grpc.WithStatsHandler(otelgrpc.NewClientHandler()))
	return &tenantWatcherClientImpl{
		endpoint: endpoint,
		grpcOpts: opts,
	}
}

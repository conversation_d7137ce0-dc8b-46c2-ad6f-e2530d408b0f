import os

import pytest
import grpc
from unittest.mock import MagicMock, patch

from services.tenant_watcher.client.client import TenantsClient
from services.tenant_watcher.client.tenant_cache import (
    KILL_PID_ON_EXIT_ENV_VAR_NAME,
    TenantCache,
)
from services.tenant_watcher import tenant_watcher_pb2


def create_tenant(
    tenant_id: str = "test_id", shard_namespace: str = "test_namespace"
) -> tenant_watcher_pb2.Tenant:
    return tenant_watcher_pb2.Tenant(id=tenant_id, shard_namespace=shard_namespace)


def create_tenant_change(
    tenant: tenant_watcher_pb2.Tenant = create_tenant(), updated=True
) -> tenant_watcher_pb2.TenantChange:
    if updated:
        return tenant_watcher_pb2.TenantChange(
            updated=tenant_watcher_pb2.TenantUpdate(tenant=tenant)
        )
    else:
        return tenant_watcher_pb2.TenantChange(
            removed=tenant_watcher_pb2.TenantRemoval(tenant=tenant)
        )


class MockRpcError(grpc.RpcError):
    """Exception thrown during test selection.

    Note that subclasses of grpc.RpcError are expected to have code() and details() methods even
    though RpcError itself does not."""

    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.message = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.message

    def __str__(self) -> str:
        return f"RpcError({self.status_code}: {self.message})"


@pytest.fixture
def tenant_client():
    return MagicMock(spec=TenantsClient)


@pytest.fixture
def tenant_cache(tenant_client: TenantsClient):
    tenant_cache = TenantCache.new_tenant_cache(
        client=tenant_client,
        namespace="test_namespace",
    )
    yield tenant_cache


def test_tenant_init(tenant_cache: TenantCache, tenant_client: TenantsClient):
    assert not tenant_cache.initialization_event.is_set()
    assert tenant_cache.tenants == {}
    assert tenant_cache.client == tenant_client
    assert tenant_cache.namespace == "test_namespace"


def test_delete_nonexistent_tenant(tenant_cache: TenantCache):
    tenant_5 = create_tenant_change(updated=False)
    tenant_cache._update_tenant(tenant_5)
    assert tenant_cache.tenants == {}


def test_update_tenant(tenant_cache: TenantCache, tenant_client: TenantsClient):
    tenant_1 = create_tenant_change(updated=True)
    tenant_2 = create_tenant_change(updated=False)

    watch_tenants_response_1 = [
        tenant_watcher_pb2.WatchTenantsResponse(tenants=[tenant_1], is_initial=True)
    ]
    watch_tenants_response_2 = [
        tenant_watcher_pb2.WatchTenantsResponse(tenants=[tenant_2], is_initial=False)
    ]

    tenant_client.watch_tenants.return_value = watch_tenants_response_1
    tenant_cache.stream_tenants()

    assert tenant_cache.get_all_tenants() == [tenant_1.updated.tenant]
    assert tenant_cache.initialization_event.is_set()
    assert tenant_cache.tenants == {"test_id": tenant_1.updated.tenant}
    assert tenant_cache.get_tenant("test_id") == tenant_1.updated.tenant
    assert tenant_cache.get_tenant("test_id_2") is None

    tenant_client.watch_tenants.return_value = watch_tenants_response_2
    tenant_cache.stream_tenants()

    assert tenant_cache.initialization_event.is_set()
    assert tenant_cache.tenants == {}
    assert tenant_cache.get_tenant("test_id") is None
    assert tenant_cache.get_tenant("test_id_2") is None
    assert tenant_cache.get_all_tenants() == []


def test_stream_tenants(tenant_cache: TenantCache, tenant_client: TenantsClient):
    test_id_tenant = create_tenant(tenant_id="test_id")
    test_id_2_tenant = create_tenant(tenant_id="test_id_2")
    test_id_3_tenant = create_tenant(tenant_id="test_id_3")

    watch_tenants_response_1 = tenant_watcher_pb2.WatchTenantsResponse(
        tenants=[create_tenant_change(test_id_tenant, updated=True)], is_initial=True
    )

    watch_tenants_response_2 = tenant_watcher_pb2.WatchTenantsResponse(
        tenants=[
            create_tenant_change(test_id_2_tenant, updated=True),
            create_tenant_change(test_id_tenant, updated=False),
            create_tenant_change(test_id_3_tenant, updated=True),
        ],
        is_initial=False,
    )

    watch_tenants_responses = [watch_tenants_response_1, watch_tenants_response_2]

    tenant_client.watch_tenants.return_value = watch_tenants_responses
    tenant_cache.stream_tenants()

    assert tenant_cache.get_all_tenants() == [test_id_2_tenant, test_id_3_tenant]
    assert tenant_cache.initialization_event.is_set()
    assert tenant_cache.tenants == {
        test_id_2_tenant.id: test_id_2_tenant,
        test_id_3_tenant.id: test_id_3_tenant,
    }


@patch("services.tenant_watcher.client.tenant_cache.time.sleep")
@patch.object(TenantCache, "stream_tenants")
def test_start_stream_grpc_error(
    mocked_stream_tenants: MagicMock, mocked_sleep: MagicMock, tenant_cache: TenantCache
):
    mocked_stream_tenants.side_effect = MockRpcError(grpc.StatusCode.RESOURCE_EXHAUSTED)
    mocked_sleep.side_effect = Exception("Error Listening to Tenant Watcher")

    try:
        tenant_cache.start_stream()
    except Exception as e:
        assert str(e) == "Error Listening to Tenant Watcher"

    mocked_sleep.assert_called_once()


@patch("services.tenant_watcher.client.tenant_cache.time.sleep")
@patch("os.kill")
@patch.object(TenantCache, "stream_tenants")
def test_start_stream_exception(
    mocked_stream_tenants: MagicMock,
    mocked_kill: MagicMock,
    mocked_sleep: MagicMock,
    tenant_client: TenantsClient,
):
    # Create a cache with the env var set
    os.environ[KILL_PID_ON_EXIT_ENV_VAR_NAME] = "1"
    tenant_cache = TenantCache.new_tenant_cache(
        client=tenant_client,
        namespace="test_namespace",
    )

    mocked_stream_tenants.side_effect = Exception("Error Listening to Tenant Watcher")

    # No exception, and the expected os.kill is mocked out
    tenant_cache.start_stream()

    assert mocked_sleep.call_count == 0
    assert mocked_kill.call_count == 1


@patch("services.tenant_watcher.client.tenant_cache.time.sleep")
@patch.object(TenantCache, "stream_tenants")
def test_start_stream_closed(
    mocked_stream_tenants: MagicMock, mocked_sleep: MagicMock, tenant_cache: TenantCache
):
    mocked_stream_tenants.return_value = None
    mocked_sleep.side_effect = Exception("Tenant Watcher Closed")

    try:
        tenant_cache.start_stream()
    except Exception as e:
        assert str(e) == "Tenant Watcher Closed"

    assert mocked_sleep.call_count == 1

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@io_bazel_rules_go//extras:gomock.bzl", "gomock")

pytest_test(
    name = "tenant_cache_test",
    srcs = ["tenant_cache_test.py"],
    deps = [
        ":client",
        "//services/tenant_watcher:tenant_watcher_py_proto",
    ],
)

py_library(
    name = "client",
    srcs = [
        "client.py",
        "tenant_cache.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/tenant_watcher:tenant_watcher_py_proto",
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["tenant_watcher_client.rs"],
    aliases = aliases(),
    crate_name = "tenant_watcher_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = [
        "//services:__subpackages__",
        "//tools/genie:__subpackages__",
    ],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/rust/tracing-tonic",
        "//services/lib/grpc/client:grpc_client_rs",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/tenant_watcher:tenant_watcher_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

rust_test(
    name = "tenant_watcher_client_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":client_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

go_library(
    name = "client_go",
    srcs = [
        "mock_tenant_watcher_client.go",
        "tenant_cache.go",
        "tenant_watcher_client.go",
    ],
    importpath = "github.com/augmentcode/augment/services/tenant_watcher/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_hashicorp_go_retryablehttp//:go_default_library",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
    ],
)

go_test(
    name = "client_go_test",
    srcs = ["tenant_cache_test.go"],
    embed = [
        ":client_go",
    ],
    importpath = "github.com/augmentcode/augment/services/tenant_watcher/client",
    deps = [],
)

gomock(
    name = "mock_tenant_watcher_client_gomock",
    out = "mocks/tenant_watcher_client_gomock.go",
    interfaces = ["TenantWatcherClient"],
    library = ":client_go",
    package = "mocks",
    visibility = ["//services:__subpackages__"],
)

go_library(
    name = "mock_tenant_watcher_client_go",
    srcs = [":mock_tenant_watcher_client_gomock"],
    importpath = "github.com/augmentcode/augment/services/tenant_watcher/client/mocks",
    visibility = ["//services:__subpackages__"],
    deps = [
        ":client_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "@com_github_golang_mock//gomock",
    ],
)


[package]
name = "tenant_watcher_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "tenant_watcher_client"
path = "tenant_watcher_client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
backoff = { workspace = true }
grpc_client = { path = "../../lib/grpc/client" }
mockall = { version = "0.13.1" }
prost = { workspace = true }
prost-wkt-types = { workspace = true }
tokio = { workspace = true }
tokio-stream =  { workspace = true }
tonic = { workspace = true }
tonic-build = { workspace = true }
tower  = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }

[build-dependencies]
tonic-build = { workspace = true }
prost-build = { workspace = true }
prost-wkt-build = { workspace = true }

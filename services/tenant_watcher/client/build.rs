// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../").canonicalize().unwrap();

    // Get path to external dependencies
    let external_dir = if std::env::var("USER").is_err() {
        root.join("..")
    } else {
        root.join("../bazel-augment-external")
    };

    let protos = vec![root.join("services/tenant_watcher/tenant_watcher.proto")];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();

        // Add path to duration proto
        let protobuf_duration_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/duration_proto/");

        let includes = vec![proto_dir, root.as_ref(), protobuf_duration_dir.as_ref()];

        tonic_build::configure()
            .extern_path(".google.protobuf.Duration", "::prost_wkt_types::Duration")
            .compile_protos(&[proto_path], &includes)?;
    }
    Ok(())
}

"""A Python cache library for the tenant watcher service."""

from __future__ import annotations

import grpc
import logging
import os
import random
import signal
import threading
import time
import typing

from services.tenant_watcher.client.client import TenantsClient
from services.tenant_watcher import tenant_watcher_pb2


# If this env variable is set, then the tenant cache will send a SIGTERM to this
# process on exit, instead of raising an exception.
KILL_PID_ON_EXIT_ENV_VAR_NAME = "TENANT_CACHE_KILL_PID_ON_EXIT"


# TODO: move this into a library?
MIN_BACKOFF_INTERVAL = 1  # seconds
MAX_BACKOFF_INTERVAL = 32  # seconds
RANDOMIZATION_FACTOR = 0.5  # 50% jitter


def is_retryable(err: grpc.RpcError) -> bool:
    return err.code() in [  # pyright: ignore
        grpc.StatusCode.RESOURCE_EXHAUSTED,
        grpc.StatusCode.UNAVAILABLE,
        grpc.StatusCode.DEADLINE_EXCEEDED,
    ]


def get_backoff_interval_seconds(attempt_num: int) -> float:
    """Get the backoff interval in seconds for the given attempt number."""
    backoff = (2**attempt_num) * MIN_BACKOFF_INTERVAL
    backoff = float(min(backoff, MAX_BACKOFF_INTERVAL))
    half_backoff = backoff / 2
    jitter = random.uniform(-half_backoff, half_backoff)
    return backoff + jitter


class TenantCache:
    """A cache for tenants."""

    def __init__(
        self,
        client: TenantsClient,
        namespace: str,
    ) -> None:
        self.namespace = namespace

        self.client = client

        self.tenants_lock: threading.Lock = threading.Lock()
        self.tenants: dict[str, tenant_watcher_pb2.Tenant] = {}

        self.start_thread_lock: threading.Lock = threading.Lock()
        self.stream_tenants_thread_started = False
        self.initialization_event = threading.Event()

        self.attempt_num: int = 0

        self.pid_to_kill_on_exit = None
        if KILL_PID_ON_EXIT_ENV_VAR_NAME in os.environ:
            self.pid_to_kill_on_exit = int(os.environ[KILL_PID_ON_EXIT_ENV_VAR_NAME])
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)

    def _wait_for_initialization(self):
        """Start the stream if it hasn't been started yet."""
        with self.start_thread_lock:
            if not self.stream_tenants_thread_started:
                self.stream_tenants_thread_started = True
                logging.info("Starting tenant cache thread")
                self.update_thread.start()
        self.initialization_event.wait()

    def _update_tenant(self, tenant_change: tenant_watcher_pb2.TenantChange) -> None:
        """Update the cache with the given tenant change.

        Assumes tenant_lock is held.
        """
        logging.debug(f"Received tenant change: {tenant_change}")
        if tenant_change.HasField("updated"):
            tenant = tenant_change.updated.tenant
            logging.info(f"Updated tenant: {tenant}")
            self.tenants[tenant.id] = tenant
        elif tenant_change.HasField("removed"):
            tenant = tenant_change.removed.tenant
            logging.info(f"Removed tenant: {tenant}")
            if tenant.id not in self.tenants:
                logging.error(f"Received tenant removal for unknown tenant: {tenant}")
            else:
                del self.tenants[tenant.id]
        else:
            logging.error(f"Received tenant change with no type: {tenant_change}")

    def stream_tenants(self) -> None:
        """stream_tenants is separate from start_stream to allow for testing without having to manage an infinite loop."""
        watch_tenant_response = self.client.watch_tenants(self.namespace)

        logging.debug("Updating tenants from watch tenants")
        for response in watch_tenant_response:
            with self.tenants_lock:
                # Reset the backoff counter
                self.attempt_num = 0
                logging.info(f"Received update of {len(response.tenants)} tenants")
                for tenant_change in response.tenants:
                    self._update_tenant(tenant_change)
                if response.is_initial:
                    logging.info("Finished updating initial tenants in cache")
                    self.initialization_event.set()
        logging.debug("Completed updating tenants from watch tenants")

    def update_loop(self):
        while True:
            self.start_stream()

    def start_stream(self):
        sleep_interval = get_backoff_interval_seconds(self.attempt_num)
        self.attempt_num += 1

        try:
            self.stream_tenants()
        except Exception as err:
            if isinstance(err, grpc.RpcError) and is_retryable(err):
                logging.error(
                    f"Error listening to tenant watcher, retrying in {sleep_interval} seconds: {err}"
                )
                time.sleep(sleep_interval)
            else:
                logging.critical("Error listening to tenant watcher")
                logging.exception(err)
                if self.pid_to_kill_on_exit is not None:
                    # Force everything to exit, we need to either crash or retry
                    # and make sure we keep listening to the stream, otherwise the
                    # cache will get stuck. We use tini-static in auth-central to
                    # handle the signal, since that container is more complicated
                    # between gunicorn and gRPC servers.
                    os.kill(self.pid_to_kill_on_exit, signal.SIGTERM)
                else:
                    raise err
        else:
            logging.info(
                f"Tenant watcher closed connection, retrying in {sleep_interval} seconds"
            )
            time.sleep(sleep_interval)

    def get_tenant(self, tenant_id: str) -> typing.Optional[tenant_watcher_pb2.Tenant]:
        """
        Wait for initialization event and then acquire lock to return the tenant from cache.

        Args:
            tenant_id: the tenant ID to filter the tenants on.

        Returns:
            the tenant.
        """
        self._wait_for_initialization()
        with self.tenants_lock:
            return self.tenants.get(tenant_id)

    def get_all_tenants(self) -> typing.Sequence[tenant_watcher_pb2.Tenant]:
        """
        Wait for initialization event and then acquire lock to return all tenants from cache.

        Returns:
            a list of tenants.
        """
        self._wait_for_initialization()
        with self.tenants_lock:
            return list(self.tenants.values())

    @classmethod
    def new_tenant_cache(
        cls,
        client: TenantsClient,
        namespace: str,
    ) -> TenantCache:
        """
        Create a new tenant cache.

        Args:
            executor: The executor to use to start the tenant cache on a thread.
            client: The tenant watcher client to use to stream tenant changes.
            namespace: The namespace to filter the tenants on.

        Returns:
            A new tenant cache.
        """
        logging.info(f"Creating tenant cache for namespace: {namespace}.")
        return cls(
            client=client,
            namespace=namespace,
        )

    def stop(self):
        """Clean up the tenant cache thread."""
        # Don't know of a reliable way to stop the thread, so just leak it.

    def __del__(self):
        self.stop()

"""A Python client library for the tenant watcher service."""

from __future__ import annotations

import logging
import typing

import grpc

from base.python.grpc import client_options
from services.tenant_watcher import tenant_watcher_pb2, tenant_watcher_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> tenant_watcher_pb2_grpc.TenantWatcherStub:
    """Setup the client stub for a tenant service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = tenant_watcher_pb2_grpc.TenantWatcherStub(channel)
    return stub


class TenantsClient:
    """Class to call tenants APIs remotely."""

    def __init__(
        self,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        self.stub = setup_stub(endpoint, credentials, options=options)

    def get_tenants(
        self, shard_namespace: str = ""
    ) -> typing.Sequence[tenant_watcher_pb2.Tenant]:
        """Get the list of tenants.

        see tenant_proto for details

        Args:
            shard_namespace: the namespace to filter the tenants on

        Returns:
            a list of tenants
        """

        request = tenant_watcher_pb2.GetTenantsRequest(shard_namespace=shard_namespace)
        response = self.stub.GetTenants(request)
        return response.tenants

    def get_tenant_id(self, tenant_name: str) -> str | None:
        """Get the tenant ID for the given tenant name.

        Args:
            tenant_name: the tenant name to filter the tenants on

        Returns:
            the tenant ID for the given tenant name
        """

        tenants = self.get_tenants()
        t = [t for t in tenants if t.name == tenant_name]
        if len(t) == 0:
            return None
        return t[0].id

    def get_tenant_name(self, tenant_id: str) -> str | None:
        """Get the tenant name for the given tenant ID.

        Args:
            tenant_id: the tenant ID to filter the tenants on

        Returns:
            the tenant name for the given tenant ID
        """

        tenants = self.get_tenants()
        t = [t for t in tenants if t.id == tenant_id]
        if len(t) == 0:
            return None
        return t[0].name

    def watch_tenants(
        self, shard_namespace: str = ""
    ) -> typing.Iterable[tenant_watcher_pb2.WatchTenantsResponse]:
        """Watch the list of tenants.

        see tenant_proto for details

        Args:
            shard_namespace: the namespace to filter the tenants on

        Returns:
            an (blocking) iterable for changes to tenants
        """

        request = tenant_watcher_pb2.WatchTenantsRequest(
            shard_namespace=shard_namespace
        )
        for response in self.stub.WatchTenants(request):
            yield response

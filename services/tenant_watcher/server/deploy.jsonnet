local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

// K8S deployment file for the tenant watcher service
function(env, namespace, cloud, namespace_config)
  local appName = 'tenant-central';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=false);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  local grpcService = grpcLib.grpcService(appName=appName, namespace=namespace);
  local globalGrcpHostname = grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=appName, namespace=namespace);
  local globalGrpcService = grpcLib.globalGrpcService(cloud=cloud, appName=appName, serviceBaseName=appName, namespace=namespace);

  // create a server certificate for MTLS
  local serverCert =
    certLib.createCentralServerCert(name='%s-server-cert' % appName,
                                    namespace=namespace,
                                    env=env,
                                    appName=appName,
                                    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace) + [globalGrcpHostname],
                                    volumeName='certs');
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    appName=appName,
    volumeName='client-certs',
  );
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    port: 50051,
    server_mtls: mtls,
    server_cert_path: '/certs/tls.crt',
    server_key_path: '/certs/tls.key',
    server_ca_path: '/certs/ca.crt',
    namespace: namespace,
    central_namespace: namespace,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    client_mtls: if mtls then clientCert.config else null,
    prom_port: 9090,
  });
  local roleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-role-binding' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      kind: 'ClusterRole',
      name: 'tenant-admin',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
      },
    ],
  };
  local container =
    {
      name: 'tenant-central',
      target: {
        name: '//services/tenant_watcher/server:image',
        dst: 'tenant-central',
      },
      args: [
        '--config',
        configMap.filename,
      ],
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      env: lib.flatten([
        telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      ]),
      volumeMounts: [
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 4,
          memory: '4Gi',
        },
      },
    };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      configMap.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);

  lib.flatten([
    configMap.objects,
    roleBinding,
    deployment,
    grpcService,
    // we only deploy the global loadbalancer if we are not in DEV
    if env != 'DEV' then globalGrpcService else null,
    serverCert.objects,
    clientCert.objects,
    serviceAccount.objects,
    dynamicFeatureFlags.k8s_objects,
    pbd,
  ])

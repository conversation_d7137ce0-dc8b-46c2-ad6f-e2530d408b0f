package main

import (
	"testing"

	crd "github.com/augmentcode/augment/services/tenant_watcher/crd"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestGetTenants(t *testing.T) {
	informer := crd.FakeTenantInformer{}
	resource := crd.FakeTenantResource{}
	tenantInfo := newInformerTenantInfo(&informer, &resource)
	tenants, err := tenantInfo.GetTenants(nil)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 0 {
		t.Error("Expected 0 tenants")
	}

	// add a tenant
	tenant := crd.Tenant{
		Spec: crd.TenantSpec{
			ShardNamespace: "shard1",
		},
	}
	tenant.SetName("test1")
	tenant.Spec.TenantID = "123"

	informer.Update(tenant)
	tenants, err = tenantInfo.GetTenants(nil)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 1 {
		t.<PERSON>r("Expected 1 tenant")
	}
	if tenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if tenants[0].Spec.ShardNamespace != "shard1" {
		t.Error("Expected tenant name to be shard1")
	}

	// add a tenant with a different namespace
	tenant.Spec.ShardNamespace = "shard2"
	informer.Update(tenant)
	tenants, err = tenantInfo.GetTenants(nil)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 1 {
		t.Error("Expected 1 tenants")
	}
	if tenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if tenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}

	// add a second tenant
	tenant.SetName("test2")
	tenant.Spec.TenantID = "124"
	informer.Update(tenant)
	tenants, err = tenantInfo.GetTenants(nil)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 2 {
		t.Error("Expected 2 tenants")
	}
	if tenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if tenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
	if tenants[1].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if tenants[1].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}

	// delete a tenant
	informer.Delete(tenant)
	tenants, err = tenantInfo.GetTenants(nil)
	log.Info().Msgf("tenants after deletion: %v", tenants)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if tenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test1")
	}
	if tenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
}

func TestGetTenantsWithFilter(t *testing.T) {
	informer := crd.FakeTenantInformer{}
	resource := crd.FakeTenantResource{}
	tenantInfo := newInformerTenantInfo(&informer, &resource)
	ShardNamespace := "shard2"
	tenants, err := tenantInfo.GetTenants(&ShardNamespace)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 0 {
		t.Error("Expected 0 tenants")
	}

	// add a tenant
	tenant := crd.Tenant{
		Spec: crd.TenantSpec{
			ShardNamespace: "shard1",
		},
	}
	tenant.SetName("test1")
	tenant.Spec.TenantID = "123"

	informer.Update(tenant)
	tenants, err = tenantInfo.GetTenants(&ShardNamespace)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 0 {
		t.Error("Expected 0 tenant")
	}

	// add a tenant with a different namespace
	tenant.Spec.ShardNamespace = "shard2"
	informer.Update(tenant)
	tenants, err = tenantInfo.GetTenants(&ShardNamespace)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 1 {
		t.Error("Expected 1 tenants")
	}
	if tenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if tenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}

	// add a second tenant
	tenant.SetName("test2")
	tenant.Spec.TenantID = "124"
	informer.Update(tenant)
	tenants, err = tenantInfo.GetTenants(&ShardNamespace)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 2 {
		t.Error("Expected 2 tenants")
	}
	if tenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if tenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
	if tenants[1].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if tenants[1].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}

	// delete a tenant
	informer.Delete(tenant)
	tenants, err = tenantInfo.GetTenants(&ShardNamespace)
	log.Info().Msgf("tenants after deletion: %v", tenants)
	if err != nil {
		t.Error(err)
	}
	if len(tenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if tenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test1")
	}
	if tenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
}

func TestGetTenantsOtherNamespace(t *testing.T) {
	informer := crd.FakeTenantInformer{}
	resource := crd.FakeTenantResource{}
	tenantInfo := newInformerTenantInfo(&informer, &resource)
	// add a few tenants
	tenants := []crd.Tenant{
		{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test1",
			},
			Spec: crd.TenantSpec{
				TenantID:       "111",
				ShardNamespace: "shard1",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test2",
			},
			Spec: crd.TenantSpec{
				TenantID:       "222",
				ShardNamespace: "shard1",
				OtherNamespace: "shard2",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test3",
			},
			Spec: crd.TenantSpec{
				TenantID:       "333",
				ShardNamespace: "shard2",
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test4",
			},
			Spec: crd.TenantSpec{
				TenantID:       "444",
				ShardNamespace: "shard2",
				OtherNamespace: "shard1",
			},
		},
	}

	for _, t := range tenants {
		informer.Update(t)
	}

	shardNamespace := "shard1"
	tenants, err := tenantInfo.GetTenants(&shardNamespace)
	assert.Nil(t, err)
	assert.Equal(t, 3, len(tenants))
	for _, tenant := range tenants {
		switch tenant.Name {
		case "test1":
			assert.Equal(t, "shard1", tenant.Spec.ShardNamespace)
		case "test2":
			assert.Equal(t, "shard1", tenant.Spec.ShardNamespace)
			assert.Equal(t, "shard2", tenant.Spec.OtherNamespace)
		case "test4":
			assert.Equal(t, "shard2", tenant.Spec.ShardNamespace)
			assert.Equal(t, "shard1", tenant.Spec.OtherNamespace)
		default:
			t.Errorf("Unexpected tenant name: %s", tenant.Name)
		}
	}
}

func TestWatchTenants(t *testing.T) {
	informer := crd.FakeTenantInformer{}
	resource := crd.FakeTenantResource{}
	tenantInfo := newInformerTenantInfo(&informer, &resource)

	var addedTenants []*crd.Tenant
	var updatedTenants []*crd.Tenant
	var updatedOldTenants []*crd.Tenant
	var deletedTenants []*crd.Tenant
	tenantInfo.AddEventHandler(nil, crd.TenantEventHandlerFuncs{
		AddFunc: func(t *crd.Tenant, isInInitialList bool) {
			addedTenants = append(addedTenants, t)
		},
		UpdateFunc: func(oldTenant *crd.Tenant, newTenant *crd.Tenant) {
			updatedTenants = append(updatedTenants, newTenant)
			updatedOldTenants = append(updatedOldTenants, oldTenant)
		},
		DeleteFunc: func(t *crd.Tenant) {
			deletedTenants = append(deletedTenants, t)
		},
	})

	// add a tenant
	tenant := crd.Tenant{
		Spec: crd.TenantSpec{
			ShardNamespace: "shard1",
		},
	}
	tenant.SetName("test1")
	tenant.Spec.TenantID = "123"
	informer.Update(tenant)

	if len(addedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if addedTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if addedTenants[0].Spec.ShardNamespace != "shard1" {
		t.Error("Expected tenant name to be shard1")
	}

	// add a tenant with a different namespace
	tenant.Spec.ShardNamespace = "shard2"
	informer.Update(tenant)
	if len(updatedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if updatedTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if updatedTenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
	if updatedOldTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if updatedOldTenants[0].Spec.ShardNamespace != "shard1" {
		t.Error("Expected tenant name to be shard1")
	}

	// add a second tenant
	tenant.SetName("test2")
	tenant.Spec.TenantID = "124"
	informer.Update(tenant)
	if len(addedTenants) != 2 {
		t.Error("Expected 2 tenant")
	}
	if addedTenants[1].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if addedTenants[1].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}

	// delete a tenant
	informer.Delete(tenant)
	if len(deletedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if deletedTenants[0].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if deletedTenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
}

func TestWatchTenantsWithFilter(t *testing.T) {
	informer := crd.FakeTenantInformer{}
	resource := crd.FakeTenantResource{}
	tenantInfo := newInformerTenantInfo(&informer, &resource)

	var shard1AddedTenants []*crd.Tenant
	var shard1UpdatedTenants []*crd.Tenant
	var shard1UpdatedOldTenants []*crd.Tenant
	var shard1DeletedTenants []*crd.Tenant
	shard1Name := "shard1"
	tenantInfo.AddEventHandler(&shard1Name, crd.TenantEventHandlerFuncs{
		AddFunc: func(t *crd.Tenant, isInInitialList bool) {
			shard1AddedTenants = append(shard1AddedTenants, t)
		},
		UpdateFunc: func(oldTenant *crd.Tenant, newTenant *crd.Tenant) {
			log.Info().Msgf("Shard1 UpdateFunc: %v / %v", oldTenant, newTenant)
			shard1UpdatedTenants = append(shard1UpdatedTenants, newTenant)
			shard1UpdatedOldTenants = append(shard1UpdatedOldTenants, oldTenant)
		},
		DeleteFunc: func(t *crd.Tenant) {
			shard1DeletedTenants = append(shard1DeletedTenants, t)
		},
	})

	var shard2AddedTenants []*crd.Tenant
	var shard2UpdatedTenants []*crd.Tenant
	var shard2UpdatedOldTenants []*crd.Tenant
	var shard2DeletedTenants []*crd.Tenant
	shard2Name := "shard2"
	tenantInfo.AddEventHandler(&shard2Name, crd.TenantEventHandlerFuncs{
		AddFunc: func(t *crd.Tenant, isInInitialList bool) {
			shard2AddedTenants = append(shard2AddedTenants, t)
		},
		UpdateFunc: func(oldTenant *crd.Tenant, newTenant *crd.Tenant) {
			log.Info().Msgf("Shard2 UpdateFunc: %v / %v", oldTenant, newTenant)
			shard2UpdatedTenants = append(shard2UpdatedTenants, newTenant)
			shard2UpdatedOldTenants = append(shard2UpdatedOldTenants, oldTenant)
		},
		DeleteFunc: func(t *crd.Tenant) {
			shard2DeletedTenants = append(shard2DeletedTenants, t)
		},
	})

	// add a tenant
	tenant := crd.Tenant{
		Spec: crd.TenantSpec{
			ShardNamespace: "shard1",
		},
	}
	tenant.SetName("test1")
	tenant.Spec.TenantID = "123"
	informer.Update(tenant)

	if len(shard1AddedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if shard1AddedTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if shard1AddedTenants[0].Spec.ShardNamespace != "shard1" {
		t.Error("Expected tenant name to be shard1")
	}
	if len(shard2AddedTenants) != 0 {
		t.Error("Expected 0 tenant")
	}

	// add a tenant with a different namespace
	tenant.Spec.ShardNamespace = "shard2"
	informer.Update(tenant)
	if len(shard1UpdatedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if shard1UpdatedTenants[0] != nil {
		t.Error("Expected tenant should be nil")
	}
	if shard1UpdatedOldTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if shard1UpdatedOldTenants[0].Spec.ShardNamespace != "shard1" {
		t.Error("Expected tenant name to be shard1")
	}

	if len(shard2UpdatedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if shard2UpdatedTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if shard2UpdatedTenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
	if shard2UpdatedOldTenants[0] != nil {
		t.Error("Expected tenant should be nil")
	}

	// add a second tenant
	tenant.SetName("test2")
	tenant.Spec.TenantID = "124"
	informer.Update(tenant)
	if shard2AddedTenants[0].Name != "test2" {
		t.Error("Expected tenant name to be test")
	}
	if shard2AddedTenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
	if len(shard1AddedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}

	// delete a tenant
	informer.Delete(tenant)
	if len(shard1DeletedTenants) != 0 {
		t.Error("Expected 0 tenant")
	}
	if len(shard2DeletedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if shard2DeletedTenants[0].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if shard2DeletedTenants[0].Spec.ShardNamespace != "shard2" {
		t.Error("Expected tenant name to be shard2")
	}
}

func TestWatchTenantsAfterCreation(t *testing.T) {
	informer := crd.FakeTenantInformer{}
	resource := crd.FakeTenantResource{}
	tenantInfo := newInformerTenantInfo(&informer, &resource)

	// add a tenant
	tenant := crd.Tenant{
		Spec: crd.TenantSpec{
			ShardNamespace: "shard1",
		},
	}
	tenant.SetName("test1")
	tenant.Spec.TenantID = "123"
	informer.Update(tenant)

	var addedTenants []*crd.Tenant
	var updatedTenants []*crd.Tenant
	var updatedOldTenants []*crd.Tenant
	var deletedTenants []*crd.Tenant
	tenantInfo.AddEventHandler(nil, crd.TenantEventHandlerFuncs{
		AddFunc: func(t *crd.Tenant, isInInitialList bool) {
			addedTenants = append(addedTenants, t)
		},
		UpdateFunc: func(oldTenant *crd.Tenant, newTenant *crd.Tenant) {
			updatedTenants = append(updatedTenants, newTenant)
			updatedOldTenants = append(updatedOldTenants, oldTenant)
		},
		DeleteFunc: func(t *crd.Tenant) {
			deletedTenants = append(deletedTenants, t)
		},
	})

	if len(addedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if addedTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if addedTenants[0].Spec.ShardNamespace != "shard1" {
		t.Error("Expected tenant name to be shard1")
	}
}

func TestWatchTenantsOtherNamespace(t *testing.T) {
	informer := crd.FakeTenantInformer{}
	resource := crd.FakeTenantResource{}
	tenantInfo := newInformerTenantInfo(&informer, &resource)

	// add a tenant in each namespace
	tenant := crd.Tenant{
		Spec: crd.TenantSpec{
			ShardNamespace: "shard1",
		},
	}
	tenant.SetName("test1")
	tenant.Spec.TenantID = "123"
	informer.Update(tenant)
	tenant2 := crd.Tenant{
		Spec: crd.TenantSpec{
			ShardNamespace: "shard2",
		},
	}
	tenant2.SetName("test2")
	tenant2.Spec.TenantID = "124"
	informer.Update(tenant2)

	var addedTenants, updatedTenants, updatedOldTenants, deletedTenants []*crd.Tenant
	shardNamespace := "shard1"
	tenantInfo.AddEventHandler(&shardNamespace, crd.TenantEventHandlerFuncs{
		AddFunc: func(t *crd.Tenant, isInInitialList bool) {
			addedTenants = append(addedTenants, t)
		},
		UpdateFunc: func(oldTenant *crd.Tenant, newTenant *crd.Tenant) {
			updatedTenants = append(updatedTenants, newTenant)
			updatedOldTenants = append(updatedOldTenants, oldTenant)
		},
		DeleteFunc: func(t *crd.Tenant) {
			deletedTenants = append(deletedTenants, t)
		},
	})

	if len(addedTenants) != 1 {
		t.Error("Expected 1 tenant")
	}
	if addedTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test")
	}
	if addedTenants[0].Spec.ShardNamespace != "shard1" {
		t.Error("Expected tenant name to be shard1")
	}

	// Add the tenants to the other namespace
	tenant.Spec.OtherNamespace = "shard2"
	tenant2.Spec.OtherNamespace = "shard1"
	informer.Update(tenant)
	informer.Update(tenant2)
	if len(updatedTenants) != 2 || len(updatedOldTenants) != 2 {
		t.Error("Expected 2 tenant updates")
	}
	if updatedTenants[0].Name != "test1" {
		t.Error("Expected tenant name to be test1")
	}
	if updatedTenants[0].Spec.OtherNamespace != "shard2" {
		t.Error("Expected other namespace to be shard2")
	}
	if updatedOldTenants[0] == nil {
		t.Error("Expected old tenant for test1")
	}
	if updatedTenants[1].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if updatedTenants[1].Spec.OtherNamespace != "shard1" {
		t.Error("Expected other namespace to be shard1")
	}
	if updatedOldTenants[1] != nil {
		t.Error("Expected no old tenant for test2")
	}

	// Swap namespaces
	tenant.Spec.ShardNamespace, tenant.Spec.OtherNamespace = "shard2", "shard1"
	tenant2.Spec.ShardNamespace, tenant2.Spec.OtherNamespace = "shard1", "shard2"
	informer.Update(tenant)
	informer.Update(tenant2)
	if len(updatedTenants) != 4 || len(updatedOldTenants) != 4 {
		t.Error("Expected 2 tenant updates")
	}
	if updatedTenants[2].Name != "test1" {
		t.Error("Expected tenant name to be test1")
	}
	if updatedTenants[2].Spec.ShardNamespace != "shard2" {
		t.Error("Expected shard namespace to be shard2")
	}
	if updatedOldTenants[2] == nil {
		t.Error("Expected old tenant for test1")
	}
	if updatedTenants[3].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if updatedTenants[3].Spec.ShardNamespace != "shard1" {
		t.Error("Expected shard namespace to be shard1")
	}
	if updatedOldTenants[3] == nil {
		t.Error("Expected old tenant for test2")
	}

	// Finish the "migrations"
	tenant.Spec.OtherNamespace = ""
	tenant2.Spec.OtherNamespace = ""
	informer.Update(tenant)
	informer.Update(tenant2)
	if len(updatedTenants) != 6 || len(updatedOldTenants) != 6 {
		t.Error("Expected 2 tenant updates")
	}
	if updatedTenants[4] != nil {
		t.Error("Expected no new tenant for test1")
	}
	if updatedOldTenants[4].Name != "test1" {
		t.Error("Expected tenant name to be test1")
	}
	if updatedTenants[5].Name != "test2" {
		t.Error("Expected tenant name to be test2")
	}
	if updatedTenants[5].Spec.OtherNamespace != "" {
		t.Error("Expected other namespace to be empty")
	}
	if updatedOldTenants[5] == nil {
		t.Error("Expected old tenant for test2")
	}
}

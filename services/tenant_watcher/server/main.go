package main

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	crd "github.com/augmentcode/augment/services/tenant_watcher/crd"
	tenant_watcher_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var (
	configFile = flag.String("config", "", "Path to config file")
	kubeconfig = flag.String("kubeconfig", "", "Path to kubeconfig file")
)

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// namespace where to look for Tenant resource objects.
	Namespace string `json:"namespace"`

	// TLS configuration
	ServerMtls     bool   `json:"server_mtls"`
	ServerKeyPath  string `json:"server_key_path"`
	ServerCertPath string `json:"server_cert_path"`
	ServerCaPath   string `json:"server_ca_path"`

	ClientMtls *tlsconfig.ClientConfig `json:"client_mtls"`

	// rpc from these namespace will get the full tenant info
	// instead of only the tenants from the namespace
	CentralNamespace string `json:"central_namespace"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`
}

func sanityCheck(config *Config) error {
	if config.Namespace == "" {
		return fmt.Errorf("namespace must be set")
	}
	return nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
		os.Exit(1)
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
		os.Exit(1)
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
		os.Exit(1)
	}
	log.Info().Msgf("Config: %v", config)

	if err := sanityCheck(&config); err != nil {
		log.Fatal().Err(err).Msg("Error in config file")
		os.Exit(1)
	}

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	clientset, err := crd.CreateDynamicClient(*kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating clientset")
		os.Exit(1)
	}

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	informer := crd.NewTenantInformer(clientset, 0, config.Namespace)
	resource := crd.NewTenantResource(clientset, config.Namespace)

	tenant_info := newInformerTenantInfo(informer, resource)
	go informer.Run(ctx.Done())
	log.Info().Msg("Informer started")

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
			os.Exit(1)
		}
	}()

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(
			grpcprom.WithHistogramBuckets([]float64{0.001, 0.01, 0.1, 0.3, 0.6, 1, 3, 6, 9, 20, 30, 60, 90, 120}),
		),
	)
	prometheus.MustRegister(srvMetrics)

	var opts []grpc.ServerOption
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	if config.ServerMtls {
		cert, err := tls.LoadX509KeyPair(config.ServerCertPath, config.ServerKeyPath)
		if err != nil {
			log.Fatal().Err(err).Msgf("Failed to load server certificates: %v", err)
			os.Exit(1)
		}

		certpool := x509.NewCertPool()
		x, err := os.ReadFile(config.ServerCaPath)
		if err != nil {
			log.Fatal().Err(err).Msgf("Failed to load server CA: %v", err)
			os.Exit(1)
		}
		certpool.AppendCertsFromPEM(x)

		// Create TLS credentials
		creds := credentials.NewTLS(&tls.Config{
			Certificates: []tls.Certificate{cert},
			ClientAuth:   tls.RequireAndVerifyClientCert,
			ClientCAs:    certpool,
		})
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating credentials")
			os.Exit(1)
		}
		opts = append(opts, grpc.Creds(creds))
	}
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateOptionalAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prompetheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)
	tenant_watcher_proto.RegisterTenantWatcherServer(grpcServer, newServer(
		config.CentralNamespace,
		tenant_info, audit.NewDefaultAuditLogger()))
	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
		os.Exit(1)
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		// Wait for either a shutdown signal or an OS signal
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		grpcServer.GracefulStop()
	}()

	err = grpcServer.Serve(lis)
	if err != nil && err != grpc.ErrServerStopped {
		log.Fatal().Err(err).Msg("Error serving")
	}
	log.Info().Msg("gRPC server closed")
}

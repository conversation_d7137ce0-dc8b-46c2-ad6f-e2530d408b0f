function(cloud)
  [
    // the Tenant CRD definition
    {
      apiVersion: 'apiextensions.k8s.io/v1',
      kind: 'CustomResourceDefinition',
      metadata: {
        name: 'tenants.eng.augmentcode.com',
      },
      spec: {
        group: 'eng.augmentcode.com',
        versions: [
          {
            name: 'v1',
            served: true,
            storage: true,
            subresources: {
              status: {},
            },
            schema: {
              openAPIV3Schema: {
                description: '',
                type: 'object',
                required: ['spec'],
                properties: {
                  spec: {
                    type: 'object',
                    required: ['shard_namespace', 'tenant_id'],
                    properties: {
                      shard_namespace: {
                        description: 'the name of the shard namespace currently responsible for the tenant',
                        type: 'string',
                        // a valid DNS name
                        pattern: '^[a-zA-Z0-9]([-a-zA-Z0-9]*[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([-a-zA-Z0-9]*[a-zA-Z0-9])?)*$',
                      },
                      tenant_id: {
                        description: 'generated tenant id',
                        type: 'string',
                        pattern: '^[0-9a-f]+$',
                      },
                      cloud: {
                        description: 'the cloud the tenant is running on',
                        type: 'string',
                      },
                      tier: {
                        description: 'the tier of the tenant',
                        type: 'string',
                        enum: ['ENTERPRISE', 'PROFESSIONAL', 'COMMUNITY'],
                      },
                      domain: {
                        // if set, the domain whose users are routed to the tenant
                        description: 'the domain of the tenant',
                        type: 'string',
                      },
                      username_domains: {
                        description: 'the domains whose users are routed to the tenant',
                        type: 'array',
                        items: {
                          type: 'string',
                        },
                      },
                      email_address_domains: {
                        description: 'the domains whose users are routed to the tenant',
                        type: 'array',
                        items: {
                          type: 'string',
                        },
                      },
                      allowed_identity_providers: {
                        description: 'the identity providers which are allowed to authenticate users, identified by auth0 prefix',
                        type: 'array',
                        items: {
                          type: 'string',
                        },
                      },
                      config: {
                        description: 'the configuration of the tenant',
                        type: 'object',
                        additionalProperties: true,
                      },
                      deleted_at: {
                        description: 'the time when the tenant was deleted',
                        type: 'string',
                      },
                      other_namespace: {
                        description: 'an alternate namespace where this tenant is allowed to operate, though not preferred, typically used when migrating tenants between namespaces',
                        type: 'string',
                      },
                      encryption_key_name: {
                        description: 'Name of the KMS key to use for tenant encryption',
                        type: 'string',
                      },
                      encryption_key_ttl: {
                        description: 'TTL for the encryption key as a duration (e.g., 4h, 1d, etc.)',
                        type: 'string',
                      },
                    },
                  },
                },
              },
            },
          },
        ],
        scope: 'Namespaced',
        names: {
          plural: 'tenants',
          singular: 'tenant',
          kind: 'Tenant',
        },
      },
    },
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'tenant-admin',
      },
      rules: [
        {
          apiGroups: ['eng.augmentcode.com'],
          resources: ['tenants'],
          verbs: ['*'],
        },
      ],
    },
  ]

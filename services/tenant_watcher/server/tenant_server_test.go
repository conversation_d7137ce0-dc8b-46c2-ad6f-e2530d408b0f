package main

import (
	"context"
	"io"
	"net"
	"slices"
	"testing"

	"github.com/augmentcode/augment/base/logging/audit"
	crd "github.com/augmentcode/augment/services/tenant_watcher/crd"
	tenant_watcher_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"
)

func watchTenantsTestHelper(t *testing.T, addr string, shardNamespace string) (<-chan *tenant_watcher_proto.WatchTenantsResponse, <-chan error) {
	// Connect to the real server
	conn, err := grpc.NewClient(addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		t.Fatalf("failed to dial server: %v", err)
	}
	client := tenant_watcher_proto.NewTenantWatcherClient(conn)

	// Call WatchTenants on the real server
	stream, err := client.WatchTenants(context.Background(), &tenant_watcher_proto.WatchTenantsRequest{ShardNamespace: shardNamespace})
	if err != nil {
		t.Fatalf("failed to call WatchTenants: %v", err)
	}

	tenantCh := make(chan *tenant_watcher_proto.WatchTenantsResponse, 100)
	errorCh := make(chan error, 1)

	go func() {
		// Receive responses and send them to the channel
		defer conn.Close()
		defer close(tenantCh)
		defer close(errorCh)
		for {
			resp, err := stream.Recv()
			if err == io.EOF {
				return
			}
			if err != nil {
				errorCh <- err
				return
			}
			tenantCh <- resp
		}
	}()

	return tenantCh, errorCh
}

func checkWatchTenantsResponse(t *testing.T, tenantCh <-chan *tenant_watcher_proto.WatchTenantsResponse, errorCh <-chan error, updated, deleted []string) {
	t.Helper()
	resp := <-tenantCh
	if len(resp.Tenants) != len(updated)+len(deleted) {
		t.Errorf("Expected %d updated and %d deleted tenants, got %d", len(updated), len(deleted), len(resp.Tenants))
	}
	for _, tenant := range resp.Tenants {
		if tenant.GetUpdated() != nil && !slices.Contains(updated, tenant.GetUpdated().Tenant.Name) {
			t.Errorf("Unexpected updated tenant: %s", tenant.GetUpdated().Tenant.Name)
		}
		if tenant.GetRemoved() != nil && !slices.Contains(deleted, tenant.GetRemoved().Tenant.Name) {
			t.Errorf("Unexpected removed tenant: %s", tenant.GetRemoved().Tenant.Name)
		}
	}

	select {
	case resp = <-tenantCh:
		t.Errorf("Unexpected response: %v", resp)
	case err := <-errorCh:
		t.Fatalf("Unexpected error: %v", err)
	default:
	}
}

type fakeTenantInfo struct {
	tenants       []crd.Tenant
	eventHandlers []crd.TenantEventHandlerFuncs
}

func (fake *fakeTenantInfo) GetTenants(currentNamespace *string) ([]crd.Tenant, error) {
	return fake.tenants, nil
}

func (fake *fakeTenantInfo) AddEventHandler(currentNamespace *string, f crd.TenantEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	fake.eventHandlers = append(fake.eventHandlers, f)
	return nil, nil
}

func (fake *fakeTenantInfo) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	if len(fake.eventHandlers) == 0 {
		return nil
	} else if len(fake.eventHandlers) == 1 {
		fake.eventHandlers = nil
		return nil
	} else {
		panic("not implemented")
	}
}

func (fake *fakeTenantInfo) CreateTenant(ctx context.Context, tenant *crd.Tenant, namespace string) (*crd.Tenant, error) {
	fake.tenants = append(fake.tenants, *tenant)
	for _, f := range fake.eventHandlers {
		f.AddFunc(tenant, false)
	}
	return tenant, nil
}

func (fake *fakeTenantInfo) UpdateTenant(ctx context.Context, tenant *crd.Tenant, namespace string) (*crd.Tenant, error) {
	for i, existingTenant := range fake.tenants {
		if existingTenant.Name == tenant.Name {
			fake.tenants[i] = *tenant
			for _, f := range fake.eventHandlers {
				f.UpdateFunc(&existingTenant, tenant)
			}
			return tenant, nil
		}
	}
	return nil, nil
}

func (fake *fakeTenantInfo) GetTenantByName(tenantName string) (*crd.Tenant, error) {
	for _, tenant := range fake.tenants {
		if tenant.Name == tenantName {
			return &tenant, nil
		}
	}
	return nil, nil
}

func (fake *fakeTenantInfo) updateTenants(tenants []crd.Tenant) {
	for _, t := range tenants {
		updated := false
		for i, existingTenant := range fake.tenants {
			if existingTenant.Name == t.Name {
				fake.tenants[i] = t
				for _, f := range fake.eventHandlers {
					f.UpdateFunc(&existingTenant, &t)
				}
				updated = true
				break
			}
		}
		if !updated {
			fake.tenants = append(fake.tenants, t)
			for _, f := range fake.eventHandlers {
				f.AddFunc(&t, false)
			}
		}
	}
}

func (fake *fakeTenantInfo) deleteTenants(tenantNames []string) {
	for _, tenantName := range tenantNames {
		for i, existingTenant := range fake.tenants {
			if existingTenant.Name == tenantName {
				fake.tenants = append(fake.tenants[:i], fake.tenants[i+1:]...)
				for _, f := range fake.eventHandlers {
					f.DeleteFunc(&existingTenant)
				}
				break
			}
		}
	}
}

func TestServerWatchTenants(t *testing.T) {
	// Set up the real server
	fakeTenants := &fakeTenantInfo{
		tenants: []crd.Tenant{
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "tenant-1",
				},
				Spec: crd.TenantSpec{
					ShardNamespace: "test-namespace",
				},
			},
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "tenant-2",
				},
				Spec: crd.TenantSpec{
					ShardNamespace: "test-namespace",
					DeletedAt:      "2023-01-01",
				},
			},
			{
				ObjectMeta: metav1.ObjectMeta{
					Name: "tenant-3",
				},
				Spec: crd.TenantSpec{
					ShardNamespace: "test-namespace",
				},
			},
		},
	}
	server := newServer("test-central",
		fakeTenants, audit.NewDefaultAuditLogger())
	grpcServer := grpc.NewServer()
	tenant_watcher_proto.RegisterTenantWatcherServer(grpcServer, server)

	// Start the server on a random port
	listener, err := net.Listen("tcp", ":0")
	if err != nil {
		t.Fatalf("Failed to listen: %v", err)
	}
	gprcAddr := listener.Addr()
	go grpcServer.Serve(listener)
	defer grpcServer.Stop()

	tenantCh, errorCh := watchTenantsTestHelper(t, gprcAddr.String(), "test-namespace")

	// Check the initial list
	checkWatchTenantsResponse(
		t,
		tenantCh,
		errorCh,
		[]string{"tenant-1", "tenant-2", "tenant-3"},
		[]string{})

	// Add a new tenant
	fakeTenants.updateTenants([]crd.Tenant{
		{
			ObjectMeta: metav1.ObjectMeta{
				Name: "tenant-4",
			},
			Spec: crd.TenantSpec{
				ShardNamespace: "test-namespace",
			},
		},
	})
	checkWatchTenantsResponse(
		t,
		tenantCh,
		errorCh,
		[]string{"tenant-4"},
		[]string{})

	// Delete a tenant
	fakeTenants.deleteTenants([]string{"tenant-3"})
	checkWatchTenantsResponse(
		t,
		tenantCh,
		errorCh,
		[]string{},
		[]string{"tenant-3"})
}

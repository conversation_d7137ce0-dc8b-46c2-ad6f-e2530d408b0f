"""Tenant Watcher server test setup for e2e tests."""

from concurrent.futures import ThreadPoolExecutor
from typing import Iterable

import grpc

from services.tenant_watcher import tenant_watcher_pb2, tenant_watcher_pb2_grpc


# The real version listens for new k8s objects, so for tests it's easier to just mock it
class FakeTenantWatcher(tenant_watcher_pb2_grpc.TenantWatcherServicer):
    def __init__(self, tenants: list[tenant_watcher_pb2.Tenant]):
        self.tenants = tenants

    def GetTenants(
        self,
        request: tenant_watcher_pb2.GetTenantsRequest,
        context: grpc.ServicerContext,
    ) -> tenant_watcher_pb2.GetTenantsResponse:
        return tenant_watcher_pb2.GetTenantsResponse(tenants=self.tenants)

    def WatchTenants(
        self,
        request: tenant_watcher_pb2.WatchTenantsRequest,
        context: grpc.ServicerContext,
    ) -> Iterable[tenant_watcher_pb2.WatchTenantsResponse]:
        tenants = [
            tenant_watcher_pb2.TenantChange(
                updated=tenant_watcher_pb2.TenantUpdate(tenant=tenant)
            )
            for tenant in self.tenants
        ]
        yield tenant_watcher_pb2.WatchTenantsResponse(
            tenants=tenants,
            is_initial=True,
        )

    def CreateTenant(
        self,
        request: tenant_watcher_pb2.CreateTenantRequest,
        context: grpc.ServicerContext,
    ) -> tenant_watcher_pb2.CreateTenantResponse:
        self.tenants.append(
            tenant_watcher_pb2.Tenant(
                id="1234",
                name=request.name,
                shard_namespace=request.shard_namespace,
                cloud=request.cloud,
            )
        )
        return tenant_watcher_pb2.CreateTenantResponse(
            tenant=tenant_watcher_pb2.Tenant(
                id="1234",
                name=request.name,
                shard_namespace=request.shard_namespace,
                cloud=request.cloud,
            )
        )

    def UpdateTenant(
        self,
        request: tenant_watcher_pb2.UpdateTenantRequest,
        context: grpc.ServicerContext,
    ) -> tenant_watcher_pb2.UpdateTenantResponse:
        for tenant in self.tenants:
            if tenant.id == request.tenant.id:
                tenant.name = request.tenant.name
                tenant.shard_namespace = request.tenant.shard_namespace
                tenant.cloud = request.tenant.cloud
                break
        return tenant_watcher_pb2.UpdateTenantResponse(tenant=request.tenant)


def start_fake_tenant_watcher_server(tenants: list[tenant_watcher_pb2.Tenant]):
    """Fixture to start a fake tenant watcher server."""
    fake_watcher = FakeTenantWatcher(tenants)
    server = grpc.server(ThreadPoolExecutor(max_workers=5))
    tenant_watcher_pb2_grpc.add_TenantWatcherServicer_to_server(fake_watcher, server)
    port = server.add_insecure_port("[::]:0")
    server.start()
    print(f"Fake tenant watcher server started, listening on {port}")
    try:
        yield port
    finally:
        print(f"Fake tenant watcher server stopping on {port}")
        server.stop(grace=None)

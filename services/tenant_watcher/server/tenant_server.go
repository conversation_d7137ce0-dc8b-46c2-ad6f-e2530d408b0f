package main

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpcservice "github.com/augmentcode/augment/services/lib/grpc/service"
	crd "github.com/augmentcode/augment/services/tenant_watcher/crd"
	tenant_watcher_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/durationpb"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type tenantServer struct {
	tenant_watcher_proto.UnimplementedTenantWatcherServer
	centralNamespace string
	tenant_info      tenantInfo
	auditLogger      *audit.AuditLogger
}

func tenantProtoToCrd(tenant *tenant_watcher_proto.Tenant, centralNamespace string) (*crd.Tenant, error) {
	tenantCrd := &crd.Tenant{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Tenant",
			APIVersion: "eng.augmentcode.com/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:            tenant.Name,
			Namespace:       centralNamespace,
			ResourceVersion: tenant.Version,
		},
		Spec: crd.TenantSpec{
			ShardNamespace: tenant.ShardNamespace,
			Cloud:          tenant.Cloud,
			TenantID:       tenant.Id,
			OtherNamespace: tenant.OtherNamespace,
		},
	}

	// Only set tier if specified
	if tenant.Tier != tenant_watcher_proto.TenantTier_TENANT_TIER_UNKNOWN {
		tenantCrd.Spec.Tier = tenant.Tier.String()
	}
	if tenant.AuthConfiguration != nil {
		tenantCrd.Spec.AllowedIdentityProviders = tenant.AuthConfiguration.AllowedIdentityProviders
		tenantCrd.Spec.Domain = tenant.AuthConfiguration.Domain
		tenantCrd.Spec.UsernameDomains = tenant.AuthConfiguration.UsernameDomains
		tenantCrd.Spec.EmailAddressDomains = tenant.AuthConfiguration.EmailAddressDomains
	}
	if tenant.Config != nil {
		tenantCrd.Spec.Config = make(map[string]interface{})
		for k, v := range tenant.Config.Configs {
			var unmarshalledValue interface{}
			err := json.Unmarshal([]byte(v), &unmarshalledValue)
			if err != nil {
				return nil, err
			}
			tenantCrd.Spec.Config[k] = unmarshalledValue
		}
	}
	return tenantCrd, nil
}

func tenantToProto(tenant crd.Tenant) (*tenant_watcher_proto.Tenant, error) {
	var auth *tenant_watcher_proto.AuthConfiguration = nil
	if tenant.Spec.Domain != "" || len(tenant.Spec.UsernameDomains) > 0 || len(tenant.Spec.EmailAddressDomains) > 0 || len(tenant.Spec.AllowedIdentityProviders) > 0 {
		auth = &tenant_watcher_proto.AuthConfiguration{
			Domain:                   tenant.Spec.Domain,
			UsernameDomains:          tenant.Spec.UsernameDomains,
			EmailAddressDomains:      tenant.Spec.EmailAddressDomains,
			AllowedIdentityProviders: tenant.Spec.AllowedIdentityProviders,
		}
	}
	config := &tenant_watcher_proto.Config{
		Configs: make(map[string]string),
	}
	for k, v := range tenant.Spec.Config {
		val, err := json.Marshal(v)
		if err != nil {
			return nil, err
		}
		s := string(val)
		config.Configs[k] = s
	}

	// Convert encryption key TTL string to durationpb.Duration
	var encryptionKeyTTL *durationpb.Duration
	if tenant.Spec.EncryptionKeyTTL != "" {
		// Parse the duration string (e.g., "24h")
		duration, err := time.ParseDuration(tenant.Spec.EncryptionKeyTTL)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to parse encryption key TTL: %s", tenant.Spec.EncryptionKeyTTL)
			// Default to 24 hours if parsing fails
			duration = 24 * time.Hour
		}
		encryptionKeyTTL = durationpb.New(duration)
	}

	return &tenant_watcher_proto.Tenant{
		Id:                tenant.Spec.TenantID,
		Name:              tenant.Name,
		ShardNamespace:    tenant.Spec.ShardNamespace,
		Tier:              tenant_watcher_proto.TenantTier(tenant_watcher_proto.TenantTier_value[tenant.Spec.Tier]),
		Cloud:             tenant.Spec.Cloud,
		OtherNamespace:    tenant.Spec.OtherNamespace,
		AuthConfiguration: auth,
		Config:            config,
		DeletedAt:         tenant.Spec.DeletedAt,
		Version:           tenant.ResourceVersion,
		EncryptionKeyName: tenant.Spec.EncryptionKeyName,
		EncryptionKeyTtl:  encryptionKeyTTL,
	}, nil
}

func (ts tenantServer) GetTenants(ctx context.Context, req *tenant_watcher_proto.GetTenantsRequest) (*tenant_watcher_proto.GetTenantsResponse, error) {
	peerName, namespace, _ := grpcservice.GetAnyPeerNameAndNamespace(ctx)
	// TODO: Right now we skip checking the error because that's what the old
	// code did, but we should probably return any error we get here?
	log.Info().Msgf("GetTenants: %s, caller=%s", req, peerName)
	if namespace == ts.centralNamespace {
		namespace = ""
	}
	if namespace != "" {
		if req.ShardNamespace == "" {
			req.ShardNamespace = namespace
		}
		if req.ShardNamespace != namespace {
			log.Error().Msgf("GetTenants: Denied due to namespace mismatch: namespace=%s, shardNamespace=%s, caller=%s",
				namespace, req.ShardNamespace, peerName)
			grpcError := status.Error(codes.PermissionDenied, "Not allowed")
			return nil, grpcError
		}
	}
	queryNamespace := &req.ShardNamespace
	if req.ShardNamespace == "" {
		queryNamespace = nil
	}

	tenants, err := ts.tenant_info.GetTenants(queryNamespace)
	if err != nil {
		log.Error().Err(err).Msg("Error getting tenants")
		return nil, err
	}

	response := &tenant_watcher_proto.GetTenantsResponse{}
	for _, tenant := range tenants {
		p, err := tenantToProto(tenant)
		if err != nil {
			log.Error().Err(err).Msg("Error getting tenants")
			return nil, err
		}
		response.Tenants = append(response.Tenants, p)
	}
	return response, nil
}

func (ts tenantServer) WatchTenants(request *tenant_watcher_proto.WatchTenantsRequest, stream tenant_watcher_proto.TenantWatcher_WatchTenantsServer) (err error) {
	peerName, namespace, _ := grpcservice.GetAnyPeerNameAndNamespace(stream.Context())
	// TODO: Right now we skip checking the error because that's what the old
	// code did, but we should probably return any error we get here?
	log.Info().Msgf("WatchTenants: %s, caller=%s", request, peerName)

	if namespace == ts.centralNamespace {
		namespace = ""
	}
	if namespace != "" {
		if request.ShardNamespace == "" {
			request.ShardNamespace = namespace
		}
		if request.ShardNamespace != namespace {
			log.Error().Msgf("WatchTenants: Denied due to namespace mismatch: namespace=%s, shardNamespace=%s, caller=%s",
				namespace, request.ShardNamespace, peerName)
			grpcError := status.Error(codes.PermissionDenied, "Not allowed")
			return grpcError
		}
	}
	queryNamespaceStr := request.ShardNamespace
	var queryNamespace *string
	if queryNamespaceStr != "" {
		queryNamespace = &queryNamespaceStr
	}

	// Add some extra safety around this channel - we don't know exactly when
	// the event handlers will stop running, so if the stream has been shutdown
	// then ignore new events and stop sending them to the channel
	ch := make(chan *tenant_watcher_proto.WatchTenantsResponse, 4)
	chClosed := false
	var chLock sync.Mutex
	defer func() {
		chLock.Lock()
		defer chLock.Unlock()
		chClosed = true
		close(ch)
	}()
	sendCh := func(resp *tenant_watcher_proto.WatchTenantsResponse) {
		chLock.Lock()
		defer chLock.Unlock()
		if chClosed {
			log.Info().Msg("Ignoring send to closed channel")
			return
		}
		ch <- resp
	}

	tenants, err := ts.tenant_info.GetTenants(queryNamespace)
	if err != nil {
		log.Error().Err(err).Msg("Error getting tenants")
		return err
	}

	// Send an initial response of all known tenants. This is hard to do in the
	// event handlers below, since they hear about tenants one at a time, so do
	// it here up front. It's okay if we send duplicate events about tenants.
	initialResp := &tenant_watcher_proto.WatchTenantsResponse{
		Tenants:   make([]*tenant_watcher_proto.TenantChange, 0, len(tenants)),
		IsInitial: true,
	}
	for _, tenant := range tenants {
		tenantProto, err := tenantToProto(tenant)
		if err != nil {
			log.Error().Err(err).Msg("Error getting initial tenants")
			return err
		}
		update := &tenant_watcher_proto.TenantChange_Updated{
			Updated: &tenant_watcher_proto.TenantUpdate{
				Tenant: tenantProto,
			},
		}
		change := &tenant_watcher_proto.TenantChange{
			Type: update,
		}
		initialResp.Tenants = append(initialResp.Tenants, change)
	}
	log.Info().Msgf("Sending initial WatchTenants response with %d tenants for namespace %v", len(initialResp.Tenants), queryNamespaceStr)
	sendCh(initialResp)

	handle, err := ts.tenant_info.AddEventHandler(queryNamespace, crd.TenantEventHandlerFuncs{
		AddFunc: func(t *crd.Tenant, isInInitialList bool) {
			log.Info().Msgf("Added tenant: isInitialList=%t, name=%v, spec=%v", isInInitialList, t.Name, t.Spec)
			response := &tenant_watcher_proto.WatchTenantsResponse{}
			update := &tenant_watcher_proto.TenantChange_Updated{}
			update.Updated = &tenant_watcher_proto.TenantUpdate{}
			update.Updated.Tenant, err = tenantToProto(*t)
			if err != nil {
				log.Error().Err(err).Msg("Error getting tenants")
				return
			}
			change := &tenant_watcher_proto.TenantChange{
				Type: update,
			}
			response.Tenants = append(response.Tenants, change)

			sendCh(response)
		},
		UpdateFunc: func(oldTenant *crd.Tenant, newTenant *crd.Tenant) {
			if newTenant == nil {
				log.Info().Msgf("Removed tenant: name=%v, spec=%v", oldTenant.Name, oldTenant.Spec)
				response := &tenant_watcher_proto.WatchTenantsResponse{}
				removal := &tenant_watcher_proto.TenantChange_Removed{}
				removal.Removed = &tenant_watcher_proto.TenantRemoval{}
				removal.Removed.Tenant, err = tenantToProto(*oldTenant)
				if err != nil {
					log.Error().Err(err).Msg("Error getting tenants")
					return
				}
				change := &tenant_watcher_proto.TenantChange{
					Type: removal,
				}
				response.Tenants = append(response.Tenants, change)

				sendCh(response)
			} else {
				log.Info().Msgf("Updated tenant: name=%v, spec=%v", newTenant.Name, newTenant.Spec)
				response := &tenant_watcher_proto.WatchTenantsResponse{}
				update := &tenant_watcher_proto.TenantChange_Updated{}
				update.Updated = &tenant_watcher_proto.TenantUpdate{}
				update.Updated.Tenant, err = tenantToProto(*newTenant)
				if err != nil {
					log.Error().Err(err).Msg("Error getting tenants")
					return
				}
				change := &tenant_watcher_proto.TenantChange{
					Type: update,
				}
				response.Tenants = append(response.Tenants, change)

				sendCh(response)
			}
		},
		DeleteFunc: func(t *crd.Tenant) {
			log.Info().Msgf("Deleted tenant: name=%v, spec=%v", t.Name, t.Spec)
			response := &tenant_watcher_proto.WatchTenantsResponse{}
			removal := &tenant_watcher_proto.TenantChange_Removed{}
			removal.Removed = &tenant_watcher_proto.TenantRemoval{}
			removal.Removed.Tenant = &tenant_watcher_proto.Tenant{
				Id:   t.Spec.TenantID,
				Name: t.Name,
			}
			change := &tenant_watcher_proto.TenantChange{
				Type: removal,
			}
			response.Tenants = append(response.Tenants, change)

			sendCh(response)
		},
	})
	if err != nil {
		log.Error().Err(err).Msg("Error adding event handler")
		return err
	}
	defer func() {
		removeErr := ts.tenant_info.RemoveEventHandler(handle)
		if removeErr != nil {
			log.Error().Err(err).Msg("Error removing event handler")
			if err != nil {
				err = removeErr
			}
		}
	}()
	for {
		select {
		case response := <-ch:
			log.Info().Msgf("Sending response: tenant_count=%d, %s, caller=%s namespace=%v",
				len(response.Tenants), peerName, queryNamespaceStr)
			err = stream.Send(response)
			if err != nil {
				log.Error().Err(err).Msg("Error sending response")
				return err
			}
		case <-stream.Context().Done():
			log.Info().Msgf("Stop watching: caller=%v namespace=%v", peerName, queryNamespaceStr)
			return nil
		}
	}
}

func generateTenantID() (string, error) {
	// Create a 128-bit (16-byte) random number
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}

	// Convert to hex string without the "0x" prefix
	return hex.EncodeToString(b), nil
}

func (ts tenantServer) CreateTenant(ctx context.Context, req *tenant_watcher_proto.CreateTenantRequest) (*tenant_watcher_proto.CreateTenantResponse, error) {
	peerName, _, _ := grpcservice.GetAnyPeerNameAndNamespace(ctx)
	// TODO: Right now we skip checking the error because that's what the old
	// code did, but we should probably return any error we get here?
	log.Info().Msgf("CreateTenant: %s, caller=%s", req, peerName)
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		// we actually require a token for this call even if other methods don't
		log.Error().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}

	if !authInfo.HasScope(tokenexchangeproto.Scope_AUTH_RW) {
		log.Error().Msgf("Auth claims do not give have scope AUTH_RW")
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}
	// another user with the AUTH_RW scope can create a tenant in any namespace

	tenantID, err := generateTenantID()
	if err != nil {
		log.Error().Err(err).Msg("Error generating tenant ID")
		return nil, err
	}
	tenantName := req.Name
	if tenantName == "" {
		log.Info().Msgf("Using tenant ID as name: %s", tenantID)
		tenantName = tenantID
	}

	tenant := &tenant_watcher_proto.Tenant{
		Id:                tenantID,
		Name:              tenantName,
		ShardNamespace:    req.ShardNamespace,
		Cloud:             req.Cloud,
		Tier:              req.Tier,
		AuthConfiguration: req.AuthConfiguration,
		Config:            req.Config,
		OtherNamespace:    "",
	}
	tenantCrd, err := tenantProtoToCrd(tenant, ts.centralNamespace)

	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		ts.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Create tenant %s with %v", tenantName, tenant),
		)
	} else {
		ts.auditLogger.WriteAuditLog(
			authInfo.UserID,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Create tenant %s with %v", tenantName, tenant),
		)
	}

	created, err := ts.tenant_info.CreateTenant(ctx, tenantCrd, ts.centralNamespace)
	if err != nil {
		// handle exists error
		if apierrors.IsAlreadyExists(err) {
			return nil, status.Error(codes.AlreadyExists, "Tenant already exists")
		}
		// handle concurrent modification error
		if apierrors.IsConflict(err) {
			return nil, status.Error(codes.Aborted, "Concurrent modification")
		}
		log.Error().Err(err).Msg("Error creating tenant")
		return nil, err
	}

	createdProto, err := tenantToProto(*created)
	if err != nil {
		log.Error().Err(err).Msg("Error converting tenant to proto")
		return nil, err
	}

	return &tenant_watcher_proto.CreateTenantResponse{
		Tenant: createdProto,
	}, nil
}

func (ts tenantServer) UpdateTenant(ctx context.Context, req *tenant_watcher_proto.UpdateTenantRequest) (*tenant_watcher_proto.UpdateTenantResponse, error) {
	peerName, _, _ := grpcservice.GetAnyPeerNameAndNamespace(ctx)
	// TODO: Right now we skip checking the error because that's what the old
	// code did, but we should probably return any error we get here?
	log.Info().Msgf("UpdateTenant: %s, caller=%s", req, peerName)
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		// we actually require a token for this call even if other methods don't
		log.Error().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}

	if !authInfo.HasScope(tokenexchangeproto.Scope_AUTH_RW) {
		log.Error().Msgf("Auth claims do not give have scope AUTH_RW")
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	// check existing
	existing, err := ts.tenant_info.GetTenantByName(req.Tenant.Name)
	if err != nil {
		if !apierrors.IsNotFound(err) {
			log.Error().Err(err).Msg("Error getting tenant")
			return nil, err
		}
	} else {
		if existing.Spec.TenantID != req.Tenant.Id {
			log.Error().Msgf("Tenant already exists with different tenant ID: %s != %s", existing.Spec.TenantID, req.Tenant.Id)
			return nil, status.Error(codes.AlreadyExists, "Tenant already exists")
		}
		if existing.Spec.ShardNamespace != req.Tenant.ShardNamespace {
			log.Error().Msgf("Tenant already exists with different shard namespace: %s != %s", existing.Spec.ShardNamespace, req.Tenant.ShardNamespace)
			return nil, status.Error(codes.AlreadyExists, "Tenant already exists")
		}
		if existing.Spec.Cloud != req.Tenant.Cloud {
			log.Error().Msgf("Tenant already exists with different cloud: %s != %s", existing.Spec.Cloud, req.Tenant.Cloud)
			return nil, status.Error(codes.AlreadyExists, "Tenant already exists")
		}
	}

	// extra auth check
	// a tenant-scoped token can only change its own tenant
	if authInfo.TenantID != "" && authInfo.TenantID != req.Tenant.Id {
		log.Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", authInfo.TenantID, req.Tenant.Id)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	tenantCrd, err := tenantProtoToCrd(req.Tenant, ts.centralNamespace)
	if err != nil {
		log.Error().Err(err).Msg("Error converting tenant to crd")
		return nil, err
	}

	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		ts.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Update tenant %s with %v", req.Tenant.Name, req.Tenant),
		)
	} else {
		ts.auditLogger.WriteAuditLog(
			authInfo.UserID,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Update tenant %s with %v", req.Tenant.Name, req.Tenant),
		)
	}

	updated, err := ts.tenant_info.UpdateTenant(ctx, tenantCrd, ts.centralNamespace)
	if err != nil {
		// handle concurrent modification error
		if apierrors.IsConflict(err) {
			return nil, status.Error(codes.Aborted, "Concurrent modification")
		}
		log.Error().Err(err).Msg("Error updating tenant")
		return nil, err
	}

	updatedProto, err := tenantToProto(*updated)
	if err != nil {
		log.Error().Err(err).Msg("Error converting tenant to proto")
		return nil, err
	}

	return &tenant_watcher_proto.UpdateTenantResponse{
		Tenant: updatedProto,
	}, nil
}

func newServer(centralNamespace string, tenant_info tenantInfo, auditLogger *audit.AuditLogger) tenantServer {
	s := tenantServer{
		centralNamespace: centralNamespace,
		tenant_info:      tenant_info,
		auditLogger:      auditLogger,
	}
	return s
}

package main

import (
	"context"
	"fmt"

	crd "github.com/augmentcode/augment/services/tenant_watcher/crd"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/tools/cache"
)

// the tenant info contains the main business logic of the tenant service, i.e.
// the filtering of tenants based on the current namespace
type tenantInfo interface {
	GetTenants(currentNamespace *string) ([]crd.Tenant, error)
	GetTenantByName(tenantName string) (*crd.Tenant, error)
	AddEventHandler(currentNamespace *string, f crd.TenantEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error)
	RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error
	CreateTenant(ctx context.Context, tenant *crd.Tenant, namespace string) (*crd.Tenant, error)
	UpdateTenant(ctx context.Context, tenant *crd.Tenant, namespace string) (*crd.Tenant, error)
}

func includeTenant(tenant *crd.Tenant, currentNamespace *string) bool {
	return currentNamespace == nil || *currentNamespace == tenant.Spec.ShardNamespace || *currentNamespace == tenant.Spec.OtherNamespace
}

// an informer based on the low-level TenantInformer
//
// this class implements the business logic of the tenant service
type informerTenantInfo struct {
	informer crd.TenantInformer
	resource crd.TenantResource
}

func (ts *informerTenantInfo) GetTenants(currentNamespace *string) ([]crd.Tenant, error) {
	var response []crd.Tenant

	tenants, err := ts.informer.List()
	if err != nil {
		return nil, err
	}
	for _, tenant := range tenants {
		if !includeTenant(&tenant, currentNamespace) {
			continue
		}
		response = append(response, tenant)
	}
	return response, nil
}

func (ts *informerTenantInfo) GetTenantByName(tenantName string) (*crd.Tenant, error) {
	tenants, err := ts.informer.List()
	if err != nil {
		return nil, err
	}
	for _, tenant := range tenants {
		if tenant.Name == tenantName {
			return &tenant, nil
		}
	}
	return nil, apierrors.NewNotFound(schema.GroupResource{
		Group:    "eng.augmentcode.com",
		Resource: "tenants",
	}, tenantName)
}

func (ts *informerTenantInfo) AddEventHandler(currentNamespace *string, f crd.TenantEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	return ts.informer.AddEventHandler(crd.TenantEventHandlerFuncs{
		AddFunc: func(t *crd.Tenant, isInInitialList bool) {
			if !includeTenant(t, currentNamespace) {
				return
			}
			f.AddFunc(t, isInInitialList)
		},
		UpdateFunc: func(oldObj *crd.Tenant, newObj *crd.Tenant) {
			if !includeTenant(oldObj, currentNamespace) {
				oldObj = nil
			}
			if !includeTenant(newObj, currentNamespace) {
				newObj = nil
			}
			if oldObj == nil && newObj == nil {
				return
			}
			f.UpdateFunc(oldObj, newObj)
		},
		DeleteFunc: func(t *crd.Tenant) {
			if !includeTenant(t, currentNamespace) {
				return
			}
			f.DeleteFunc(t)
		},
	})
}

func (ts *informerTenantInfo) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	return ts.informer.RemoveEventHandler(r)
}

func (ts *informerTenantInfo) CreateTenant(ctx context.Context, tenant *crd.Tenant, namespace string) (*crd.Tenant, error) {
	if tenant.Spec.TenantID == "" {
		return nil, fmt.Errorf("tenant id is required")
	}
	if tenant.Spec.ShardNamespace == "" {
		return nil, fmt.Errorf("shard namespace is required")
	}
	if tenant.Spec.Cloud == "" {
		return nil, fmt.Errorf("cloud is required")
	}
	if tenant.ObjectMeta.Name == "" {
		return nil, fmt.Errorf("tenant name is required")
	}

	return ts.resource.Create(ctx, tenant, namespace, metav1.CreateOptions{})
}

func (ts *informerTenantInfo) UpdateTenant(ctx context.Context, tenant *crd.Tenant, namespace string) (*crd.Tenant, error) {
	return ts.resource.Update(ctx, tenant, metav1.UpdateOptions{})
}

func newInformerTenantInfo(informer crd.TenantInformer, resource crd.TenantResource) tenantInfo {
	s := informerTenantInfo{
		informer: informer,
		resource: resource,
	}
	return &s
}

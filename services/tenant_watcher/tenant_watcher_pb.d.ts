// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/tenant_watcher/tenant_watcher.proto (package services.tenant, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, Duration, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum services.tenant.TenantTier
 */
export declare enum TenantTier {
  /**
   * @generated from enum value: TENANT_TIER_UNKNOWN = 0;
   */
  TENANT_TIER_UNKNOWN = 0,

  /**
   * @generated from enum value: ENTERPRISE = 1;
   */
  ENTERPRISE = 1,

  /**
   * @generated from enum value: PROFESSIONAL = 2;
   */
  PROFESSIONAL = 2,

  /**
   * @generated from enum value: COMMUNITY = 3;
   */
  COMMUNITY = 3,
}

/**
 * @generated from message services.tenant.GetTenantsRequest
 */
export declare class GetTenantsRequest extends Message<GetTenantsRequest> {
  /**
   * @generated from field: string shard_namespace = 1;
   */
  shardNamespace: string;

  constructor(data?: PartialMessage<GetTenantsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.GetTenantsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTenantsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTenantsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTenantsRequest;

  static equals(a: GetTenantsRequest | PlainMessage<GetTenantsRequest> | undefined, b: GetTenantsRequest | PlainMessage<GetTenantsRequest> | undefined): boolean;
}

/**
 * @generated from message services.tenant.WatchTenantsRequest
 */
export declare class WatchTenantsRequest extends Message<WatchTenantsRequest> {
  /**
   * @generated from field: string shard_namespace = 1;
   */
  shardNamespace: string;

  constructor(data?: PartialMessage<WatchTenantsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.WatchTenantsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchTenantsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchTenantsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchTenantsRequest;

  static equals(a: WatchTenantsRequest | PlainMessage<WatchTenantsRequest> | undefined, b: WatchTenantsRequest | PlainMessage<WatchTenantsRequest> | undefined): boolean;
}

/**
 * @generated from message services.tenant.AuthConfiguration
 */
export declare class AuthConfiguration extends Message<AuthConfiguration> {
  /**
   * @generated from field: string domain = 1;
   */
  domain: string;

  /**
   * @generated from field: repeated string username_domains = 2;
   */
  usernameDomains: string[];

  /**
   * @generated from field: repeated string email_address_domains = 3;
   */
  emailAddressDomains: string[];

  /**
   * @generated from field: repeated string allowed_identity_providers = 4;
   */
  allowedIdentityProviders: string[];

  constructor(data?: PartialMessage<AuthConfiguration>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.AuthConfiguration";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuthConfiguration;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuthConfiguration;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuthConfiguration;

  static equals(a: AuthConfiguration | PlainMessage<AuthConfiguration> | undefined, b: AuthConfiguration | PlainMessage<AuthConfiguration> | undefined): boolean;
}

/**
 * @generated from message services.tenant.Config
 */
export declare class Config extends Message<Config> {
  /**
   * @generated from field: map<string, string> configs = 1;
   */
  configs: { [key: string]: string };

  constructor(data?: PartialMessage<Config>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.Config";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Config;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Config;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Config;

  static equals(a: Config | PlainMessage<Config> | undefined, b: Config | PlainMessage<Config> | undefined): boolean;
}

/**
 * @generated from message services.tenant.Tenant
 */
export declare class Tenant extends Message<Tenant> {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string shard_namespace = 3;
   */
  shardNamespace: string;

  /**
   * @generated from field: string cloud = 4;
   */
  cloud: string;

  /**
   * @generated from field: services.tenant.TenantTier tier = 12;
   */
  tier: TenantTier;

  /**
   * @generated from field: services.tenant.AuthConfiguration auth_configuration = 5;
   */
  authConfiguration?: AuthConfiguration;

  /**
   * @generated from field: services.tenant.Config config = 6;
   */
  config?: Config;

  /**
   * @generated from field: string deleted_at = 7;
   */
  deletedAt: string;

  /**
   * @generated from field: string other_namespace = 8;
   */
  otherNamespace: string;

  /**
   * @generated from field: string encryption_key_name = 9;
   */
  encryptionKeyName: string;

  /**
   * @generated from field: google.protobuf.Duration encryption_key_ttl = 10;
   */
  encryptionKeyTtl?: Duration;

  /**
   * @generated from field: string version = 11;
   */
  version: string;

  constructor(data?: PartialMessage<Tenant>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.Tenant";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Tenant;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Tenant;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Tenant;

  static equals(a: Tenant | PlainMessage<Tenant> | undefined, b: Tenant | PlainMessage<Tenant> | undefined): boolean;
}

/**
 * @generated from message services.tenant.GetTenantsResponse
 */
export declare class GetTenantsResponse extends Message<GetTenantsResponse> {
  /**
   * @generated from field: repeated services.tenant.Tenant tenants = 1;
   */
  tenants: Tenant[];

  constructor(data?: PartialMessage<GetTenantsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.GetTenantsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTenantsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTenantsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTenantsResponse;

  static equals(a: GetTenantsResponse | PlainMessage<GetTenantsResponse> | undefined, b: GetTenantsResponse | PlainMessage<GetTenantsResponse> | undefined): boolean;
}

/**
 * @generated from message services.tenant.TenantUpdate
 */
export declare class TenantUpdate extends Message<TenantUpdate> {
  /**
   * @generated from field: services.tenant.Tenant tenant = 1;
   */
  tenant?: Tenant;

  constructor(data?: PartialMessage<TenantUpdate>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.TenantUpdate";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantUpdate;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantUpdate;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantUpdate;

  static equals(a: TenantUpdate | PlainMessage<TenantUpdate> | undefined, b: TenantUpdate | PlainMessage<TenantUpdate> | undefined): boolean;
}

/**
 * @generated from message services.tenant.TenantRemoval
 */
export declare class TenantRemoval extends Message<TenantRemoval> {
  /**
   * @generated from field: services.tenant.Tenant tenant = 1;
   */
  tenant?: Tenant;

  constructor(data?: PartialMessage<TenantRemoval>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.TenantRemoval";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantRemoval;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantRemoval;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantRemoval;

  static equals(a: TenantRemoval | PlainMessage<TenantRemoval> | undefined, b: TenantRemoval | PlainMessage<TenantRemoval> | undefined): boolean;
}

/**
 * @generated from message services.tenant.TenantChange
 */
export declare class TenantChange extends Message<TenantChange> {
  /**
   * @generated from oneof services.tenant.TenantChange.type
   */
  type: {
    /**
     * @generated from field: services.tenant.TenantUpdate updated = 1;
     */
    value: TenantUpdate;
    case: "updated";
  } | {
    /**
     * @generated from field: services.tenant.TenantRemoval removed = 2;
     */
    value: TenantRemoval;
    case: "removed";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<TenantChange>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.TenantChange";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantChange;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantChange;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantChange;

  static equals(a: TenantChange | PlainMessage<TenantChange> | undefined, b: TenantChange | PlainMessage<TenantChange> | undefined): boolean;
}

/**
 * @generated from message services.tenant.WatchTenantsResponse
 */
export declare class WatchTenantsResponse extends Message<WatchTenantsResponse> {
  /**
   * @generated from field: repeated services.tenant.TenantChange tenants = 1;
   */
  tenants: TenantChange[];

  /**
   * @generated from field: bool is_initial = 2;
   */
  isInitial: boolean;

  constructor(data?: PartialMessage<WatchTenantsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.WatchTenantsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchTenantsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchTenantsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchTenantsResponse;

  static equals(a: WatchTenantsResponse | PlainMessage<WatchTenantsResponse> | undefined, b: WatchTenantsResponse | PlainMessage<WatchTenantsResponse> | undefined): boolean;
}

/**
 * @generated from message services.tenant.CreateTenantRequest
 */
export declare class CreateTenantRequest extends Message<CreateTenantRequest> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string shard_namespace = 2;
   */
  shardNamespace: string;

  /**
   * @generated from field: string cloud = 3;
   */
  cloud: string;

  /**
   * @generated from field: services.tenant.TenantTier tier = 6;
   */
  tier: TenantTier;

  /**
   * @generated from field: services.tenant.AuthConfiguration auth_configuration = 4;
   */
  authConfiguration?: AuthConfiguration;

  /**
   * @generated from field: services.tenant.Config config = 5;
   */
  config?: Config;

  constructor(data?: PartialMessage<CreateTenantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.CreateTenantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTenantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTenantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTenantRequest;

  static equals(a: CreateTenantRequest | PlainMessage<CreateTenantRequest> | undefined, b: CreateTenantRequest | PlainMessage<CreateTenantRequest> | undefined): boolean;
}

/**
 * @generated from message services.tenant.CreateTenantResponse
 */
export declare class CreateTenantResponse extends Message<CreateTenantResponse> {
  /**
   * @generated from field: services.tenant.Tenant tenant = 1;
   */
  tenant?: Tenant;

  constructor(data?: PartialMessage<CreateTenantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.CreateTenantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTenantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTenantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTenantResponse;

  static equals(a: CreateTenantResponse | PlainMessage<CreateTenantResponse> | undefined, b: CreateTenantResponse | PlainMessage<CreateTenantResponse> | undefined): boolean;
}

/**
 * @generated from message services.tenant.UpdateTenantRequest
 */
export declare class UpdateTenantRequest extends Message<UpdateTenantRequest> {
  /**
   * @generated from field: services.tenant.Tenant tenant = 1;
   */
  tenant?: Tenant;

  constructor(data?: PartialMessage<UpdateTenantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.UpdateTenantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTenantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTenantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTenantRequest;

  static equals(a: UpdateTenantRequest | PlainMessage<UpdateTenantRequest> | undefined, b: UpdateTenantRequest | PlainMessage<UpdateTenantRequest> | undefined): boolean;
}

/**
 * @generated from message services.tenant.UpdateTenantResponse
 */
export declare class UpdateTenantResponse extends Message<UpdateTenantResponse> {
  /**
   * @generated from field: services.tenant.Tenant tenant = 1;
   */
  tenant?: Tenant;

  constructor(data?: PartialMessage<UpdateTenantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "services.tenant.UpdateTenantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTenantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTenantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTenantResponse;

  static equals(a: UpdateTenantResponse | PlainMessage<UpdateTenantResponse> | undefined, b: UpdateTenantResponse | PlainMessage<UpdateTenantResponse> | undefined): boolean;
}


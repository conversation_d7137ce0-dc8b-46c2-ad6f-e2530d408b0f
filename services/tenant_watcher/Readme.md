# Tenant Watcher

The tenant watcher server is a gRPC server that provides tenant information.
It is a global service running in central namespace of the lead cluster. We have one running for each env (staging, prod).

The tenant-config objects are expected to live in the same namespaces as their respective tenant watcher servers. The one caveat is that we do some duplication, and copy staging tenants to prod, so that authentication can "just work" for staging tenants without any special configuration.

The tenants and the tenant assignment to shards is dynamic.
New tenants can be created and existing tenants can be deleted or moved to other shard namespaces at any time.

Services running in a shard can use the tenant server to get information about their tenants.

## Security

The tenant watcher server is a global service running in central namespace of the lead cluster.
It is secured using mTLS.

The tenant information is sensitive. We should not leak which tenants exists to namespaces that are not responsible for them.

## Persistence

The Tenant Watcher Server uses a Kubernetes [https://kubernetes.io/docs/concepts/extend-kubernetes/api-extension/custom-resources/](Custom Resource) to store tenant information.

Each `Tenant` object contains:

- a name
- a shard namespace
- a tenant ID
- auth configuration, incl. the domain.

The name is the name of the tenant. It has to be a valid kuberentes name.
The shard name is the name of the namespace that is responsible for the tenant.

The tenant ID is a unique identifier for the tenant. When a tenant is created,
the tenant ID has to be assigned. The number should be a randomly distributed hex string.

The auth configuration is the configuration for the auth services. It contains the domain of the tenant.

A `Tenant` kuberentes object is a namespaced object. A `Tenant` are created in the `central` namespace of the environment (`central`/`central-staging`) of the lead cloud. For development, it is created in the dev-namespace.

## Components

### Server

The tenant server is a gRPC server that provides tenant information.
It is a global service running in central namespaces.
Services running in a shard query the server to learn what tenants they are responsible for.

## Interface

see tenant.proto for the GRPC protocol definition.

## Links

- see https://www.notion.so/Shard-Architecture-6c5e39aee2d64a2d81e2273e39c0f4fe?pvs=4
- see https://www.notion.so/1000-1000-tenant-information-workstream-7cb0581a01b04c5f9be0cb6c18c404ff?pvs=4
- see https://kubernetes.io/docs/concepts/extend-kubernetes/api-extension/custom-resources/

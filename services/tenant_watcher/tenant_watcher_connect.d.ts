// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/tenant_watcher/tenant_watcher.proto (package services.tenant, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreateTenantRequest, CreateTenantResponse, GetTenantsRequest, GetTenantsResponse, UpdateTenantRequest, UpdateTenantResponse, WatchTenantsRequest, WatchTenantsResponse } from "./tenant_watcher_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service services.tenant.TenantWatcher
 */
export declare const TenantWatcher: {
  readonly typeName: "services.tenant.TenantWatcher",
  readonly methods: {
    /**
     * @generated from rpc services.tenant.TenantWatcher.WatchTenants
     */
    readonly watchTenants: {
      readonly name: "WatchTenants",
      readonly I: typeof WatchTenantsRequest,
      readonly O: typeof WatchTenantsResponse,
      readonly kind: MethodKind.ServerStreaming,
    },
    /**
     * @generated from rpc services.tenant.TenantWatcher.GetTenants
     */
    readonly getTenants: {
      readonly name: "GetTenants",
      readonly I: typeof GetTenantsRequest,
      readonly O: typeof GetTenantsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc services.tenant.TenantWatcher.CreateTenant
     */
    readonly createTenant: {
      readonly name: "CreateTenant",
      readonly I: typeof CreateTenantRequest,
      readonly O: typeof CreateTenantResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc services.tenant.TenantWatcher.UpdateTenant
     */
    readonly updateTenant: {
      readonly name: "UpdateTenant",
      readonly I: typeof UpdateTenantRequest,
      readonly O: typeof UpdateTenantResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};


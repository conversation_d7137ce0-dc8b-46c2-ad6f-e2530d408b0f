"""
CLI Utility to get tenant ids from tenant name
"""

import argparse
import pathlib

from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.logging.console_logging import setup_console_logging
from base.python.cloud import cloud as cloud_lib


def _get_tenant_id(args) -> str | None:
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.namespace,
        plural="tenants",
    )
    for tenant in api_response["items"]:
        if tenant["metadata"]["name"] == args.tenant_name:
            tenant_id = tenant["spec"]["tenant_id"]
            print(f"{args.tenant_name, tenant['spec']['shard_namespace']}: {tenant_id}")
            return tenant_id


def _list_tenant_ids(args) -> dict[tuple[str, str], str]:
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.namespace,
        plural="tenants",
    )

    # [tenant name, shard namespace] : tenant id
    tenants_map: dict[tuple[str, str], str] = {}

    for tenant in api_response["items"]:
        tenants_map[tenant["metadata"]["name"], tenant["spec"]["shard_namespace"]] = (
            tenant["spec"]["tenant_id"]
        )

    # Sort based on tenant name, break ties with namespace
    sorted_tenants_map = sorted(tenants_map.items(), key=lambda x: (x[0][0], x[0][1]))

    for name_info, tenant_id in sorted_tenants_map:
        print(f"{name_info}: {tenant_id}")

    return tenants_map


def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()

    subparsers = parser.add_subparsers(dest="action")

    get_tenant_id_parser = subparsers.add_parser("get-tenant-id")
    get_tenant_id_parser.add_argument(
        "--tenant-name", help="tenant name", required=True
    )

    subparsers.add_parser("list-tenant-ids")

    parser.add_argument(
        "--namespace", help="The namespace for tenant objects", default="central"
    )
    parser.add_argument(
        "--cloud",
        help="The cloud to use",
        required=True,
        choices=cloud_lib.get_cloud_list(),
    )
    parser.add_argument(
        "--kube-config-file",
        help="Path to kube config file",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube", "config"),
    )

    args = parser.parse_args()

    if args.action == "get-tenant-id":
        _get_tenant_id(args)
    elif args.action == "list-tenant-ids":
        _list_tenant_ids(args)


if __name__ == "__main__":
    main()

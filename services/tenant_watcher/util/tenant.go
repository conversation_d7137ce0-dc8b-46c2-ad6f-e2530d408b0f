package util

import (
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

func IsCommunityTenant(tenant *tenantproto.Tenant) bool {
	return tenant.Tier == tenantproto.TenantTier_COMMUNITY
}

func IsEnterpriseTenant(tenant *tenantproto.Tenant) bool {
	return tenant.Tier == tenantproto.TenantTier_ENTERPRISE
}

func IsSelfServeTeamTenant(tenant *tenantproto.Tenant) bool {
	if tenant.Config == nil || tenant.Config.Configs == nil {
		return false
	}
	isSelfServeTeam := tenant.Config.Configs["is_self_serve_team"] == "true"
	return (!IsEnterpriseTenant(tenant)) && isSelfServeTeam
}

// IsLegacySelfServeTeamTenant checks if a tenant is a legacy self-serve team.
// The tenant cannot be enterprise and must be a self-serve team.
func IsLegacySelfServeTeamTenant(tenant *tenantproto.Tenant) bool {
	if tenant.Config == nil || tenant.Config.Configs == nil {
		return false
	}

	if IsEnterpriseTenant(tenant) || !IsSelfServeTeamTenant(tenant) {
		return false
	}

	return tenant.Config.Configs["is_legacy_self_serve_team"] == "true"
}

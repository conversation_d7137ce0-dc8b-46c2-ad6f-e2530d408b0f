load("//tools/bzl:python.bzl", "py_binary")
load("//tools/bzl:go.bzl", "go_library")

py_binary(
    name = "util",
    srcs = [
        "tenant_watcher_util.py",
    ],
    data = [
        "//deploy/common:cloud_info_json",
    ],
    deps = [
        "//base/logging:console_logging",
        "//services/lib/grpc:grpc_args_parser",
    ],
)

go_library(
    name = "go_lib",
    srcs = [
        "tenant.go",
    ],
    importpath = "github.com/augmentcode/augment/services/tenant_watcher/util",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/tenant_watcher:tenant_watcher_go_proto",
    ],
)

package crd

import (
	"reflect"
	"slices"
	"testing"
)

func TestTenantSerialization(t *testing.T) {
	var tenant Tenant
	tenant.TypeMeta.Kind = "Tenant"
	tenant.TypeMeta.APIVersion = "v1"
	tenant.Spec.ShardNamespace = "test"
	tenant.Spec.TenantID = "1234"
	tenant.Spec.Domain = ""
	tenant.Spec.UsernameDomains = []string{"foocorp.com", "barcorp.com"}
	tenant.Spec.EmailAddressDomains = []string{"foo.com", "bar.com"}
	tenant.Spec.AllowedIdentityProviders = []string{"google", "facebook"}
	tenant.Spec.Config = map[string]interface{}{
		"foo": "bar",
		"bar": 1.0,
	}
	tenant.Spec.DeletedAt = "2024-02-05T17:37:43Z"

	u, err := tenant.ToUnstructured()
	if err != nil {
		t.Error(err)
	}
	var tenant2 Tenant
	err = tenant2.FromUnstructured(u)
	if err != nil {
		t.Error(err)
	}
	if tenant2.Spec.ShardNamespace != tenant.Spec.ShardNamespace {
		t.Errorf("ShardNamespace not equal: %s, %s", tenant2.Spec.ShardNamespace, tenant.Spec.ShardNamespace)
	}
	if tenant2.Spec.TenantID != tenant.Spec.TenantID {
		t.Errorf("TenantID not equal: %s, %s", tenant2.Spec.TenantID, tenant.Spec.TenantID)
	}
	if tenant2.Spec.Domain != tenant.Spec.Domain {
		t.Errorf("Domain not equal: %s, %s", tenant2.Spec.Domain, tenant.Spec.Domain)
	}
	if !slices.Equal(tenant2.Spec.UsernameDomains, tenant.Spec.UsernameDomains) {
		t.Errorf("UsernameDomains not equal: %s, %s", tenant2.Spec.UsernameDomains, tenant.Spec.UsernameDomains)
	}
	if !slices.Equal(tenant2.Spec.EmailAddressDomains, tenant.Spec.EmailAddressDomains) {
		t.Errorf("EmailAddressDomains not equal: %s, %s", tenant2.Spec.EmailAddressDomains, tenant.Spec.EmailAddressDomains)
	}
	if !slices.Equal(tenant2.Spec.AllowedIdentityProviders, tenant.Spec.AllowedIdentityProviders) {
		t.Errorf("AllowedIdentityProviders not equal: %s, %s", tenant2.Spec.AllowedIdentityProviders, tenant.Spec.AllowedIdentityProviders)
	}
	if !reflect.DeepEqual(tenant2.Spec.Config, tenant.Spec.Config) {
		t.Errorf("Config not equal: %s, %s", tenant2.Spec.Config, tenant.Spec.Config)
	}
	if tenant2.Spec.DeletedAt != tenant.Spec.DeletedAt {
		t.Errorf("DeletedAt not equal: %s, %s", tenant2.Spec.DeletedAt, tenant.Spec.DeletedAt)
	}
}

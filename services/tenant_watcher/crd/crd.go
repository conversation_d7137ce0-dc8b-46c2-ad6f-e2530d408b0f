// this file contains a thin go wrapper for the Tenant Kubernetes CRD (Custom Resoure Definition)
//
// It provides a type-safe way to create and update a Tenant resource.
// Beyond that it doesn't container tenant business logic
package crd

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/rs/zerolog/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/dynamic/dynamicinformer"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/clientcmd"
)

// The TenantSpec is the specifiation or configuration of the Tenant resource
// the fields have to map to the properties of the Tenant CRD in deploy_shard.jsonnet
type TenantSpec struct {
	// the namespace of the shard the tenant belongs to
	ShardNamespace string `json:"shard_namespace"`

	// the tenant id of the tenant
	TenantID string `json:"tenant_id"`

	// the tier of the tenant (enterprise, professional, community)
	Tier string `json:"tier"`

	// the cloud the tenant is running on
	Cloud string `json:"cloud"`

	// the domain of the tenant
	// if set, the domain whose users are routed to the tenant
	Domain string `json:"domain"`

	// the domains whose users are routed to the tenant
	UsernameDomains []string `json:"username_domains"`

	// the domains whose users are routed to the tenant
	EmailAddressDomains []string `json:"email_address_domains"`

	// the identity providers which are allowed to authenticate users.
	AllowedIdentityProviders []string `json:"allowed_identity_providers"`

	// the configuration of the tenant
	Config map[string]interface{} `json:"config"`

	// the time when the tenant was deleted
	// set to a non-empty string if and only if the tenant was deleted
	DeletedAt string `json:"deleted_at"`

	// If set, another namespace that the tenant is valid in, though not
	// preferred to operate in. During migration, this can point to the old or
	// new namespace for a tenant depending on the status of the migration.
	// Usually we will go through a set of steps like this:
	//   Namespace | Other Namespace
	//   foo
	//   foo       | bar
	//   bar       | foo
	//   bar
	OtherNamespace string `json:"other_namespace"`

	// Name of the KMS key to use for tenant encryption
	EncryptionKeyName string `json:"encryption_key_name"`

	// TTL for the encryption key
	EncryptionKeyTTL string `json:"encryption_key_ttl"`
}

// the main object for the Tenant CRD
type Tenant struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec TenantSpec `json:"spec"`
}

// converts the Tenant object to an unstructured object
func (t *Tenant) ToUnstructured() (*unstructured.Unstructured, error) {
	u := &unstructured.Unstructured{}
	bytes, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}
	err = u.UnmarshalJSON(bytes)
	if err != nil {
		return nil, err
	}
	return u, nil
}

// converts the unstructured object to a Tenant object
func (t *Tenant) FromUnstructured(u *unstructured.Unstructured) error {
	bytes, err := u.MarshalJSON()
	if err != nil {
		log.Error().Err(err).Msg("Error converting to unstructured")
		return err
	}
	return json.Unmarshal(bytes, t)
}

// callbacks to be informed about changes by the TenantInformer
type TenantEventHandlerFuncs struct {
	AddFunc    func(obj *Tenant, isInInitialList bool)
	UpdateFunc func(oldObj *Tenant, newObj *Tenant)
	DeleteFunc func(obj *Tenant)
}

// informs about changes to the Tenant CRD
type TenantInformer interface {
	// Run starts the informer
	Run(stopCh <-chan struct{})

	// List returns the list of all tenants
	List() ([]Tenant, error)

	// AddEventHandler registers a handler to be called when an event occurs
	// returns a registration that can be used to remove the handler
	AddEventHandler(f TenantEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error)

	// RemoveEventHandler removes the given handler
	RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error
}

// implementation of the tenant informer interfaced based on s shared informer
type sharedIndexTenantInformer struct {
	informer cache.SharedIndexInformer
}

// NewTenantInformer creates a new TenantInformer
func NewTenantInformer(clientset *dynamic.DynamicClient, defaultResync time.Duration, namespace string) TenantInformer {
	fac := dynamicinformer.NewFilteredDynamicSharedInformerFactory(clientset, defaultResync, namespace, nil)
	informer := fac.ForResource(schema.GroupVersionResource{
		Group:    "eng.augmentcode.com",
		Version:  "v1",
		Resource: "tenants",
	}).Informer()

	return &sharedIndexTenantInformer{
		informer: informer,
	}
}

func (t *sharedIndexTenantInformer) Run(stopCh <-chan struct{}) {
	t.informer.Run(stopCh)
}

func (t *sharedIndexTenantInformer) List() ([]Tenant, error) {
	var tenants []Tenant
	for _, obj := range t.informer.GetStore().List() {
		typedObj := obj.(*unstructured.Unstructured)
		var tenant Tenant
		err := tenant.FromUnstructured(typedObj)
		if err != nil {
			log.Error().Err(err).Msg("Error converting to tenant")
			return nil, err
		}
		tenants = append(tenants, tenant)
	}

	return tenants, nil
}

func (t *sharedIndexTenantInformer) AddEventHandler(f TenantEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	handle, err := t.informer.AddEventHandler(cache.ResourceEventHandlerDetailedFuncs{
		AddFunc: func(obj interface{}, isInInitialList bool) {
			if f.AddFunc == nil {
				return
			}
			// converting the dynamic object to your CRD struct
			typedObj := obj.(*unstructured.Unstructured)
			var crdObj Tenant
			err := crdObj.FromUnstructured(typedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to tenant")
				return
			}
			f.AddFunc(&crdObj, isInInitialList)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			if f.UpdateFunc == nil {
				return
			}
			oldTypedObj := oldObj.(*unstructured.Unstructured)
			var oldCrdObj Tenant
			err := oldCrdObj.FromUnstructured(oldTypedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to tenant")
				return
			}
			newTypedObj := newObj.(*unstructured.Unstructured)
			var newCrdObj Tenant
			err = newCrdObj.FromUnstructured(newTypedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to tenant")
				return
			}
			f.UpdateFunc(&oldCrdObj, &newCrdObj)
		},
		DeleteFunc: func(obj interface{}) {
			if f.DeleteFunc == nil {
				return
			}
			typedObj := obj.(*unstructured.Unstructured)
			var crdObj Tenant
			err := crdObj.FromUnstructured(typedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to tenant")
				return
			}
			f.DeleteFunc(&crdObj)
		},
	})
	return handle, err
}

func (t *sharedIndexTenantInformer) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	return t.informer.RemoveEventHandler(r)
}

// a tenant resource that allows creating and updating a Tenant in a type-safe way
type TenantResource interface {
	// Create creates a new tenant in the specified namespace
	Create(ctx context.Context, t *Tenant, namespace string, opts metav1.CreateOptions) (*Tenant, error)
	// UpdateStatus updates the status of an existing tenant
	UpdateStatus(ctx context.Context, t *Tenant, opts metav1.UpdateOptions) (*Tenant, error)
	// Delete deletes a tenant by name from the specified namespace
	Delete(ctx context.Context, name string, namespace string, opts metav1.DeleteOptions) error
	// Update updates an existing tenant
	Update(ctx context.Context, t *Tenant, opts metav1.UpdateOptions) (*Tenant, error)
}

type tenantResourceImpl struct {
	resource dynamic.NamespaceableResourceInterface
}

func (tr *tenantResourceImpl) Create(ctx context.Context, t *Tenant, namespace string, opts metav1.CreateOptions) (*Tenant, error) {
	unstructuredObj, err := t.ToUnstructured()
	if err != nil {
		log.Error().Err(err).Msg("Error converting to unstructured")
		return nil, err
	}
	obj, err := tr.resource.Namespace(namespace).Create(ctx, unstructuredObj, opts)
	if err != nil {
		log.Error().Err(err).Msg("Error creating tenant")
		return nil, err
	}
	newObject := &Tenant{}
	err = newObject.FromUnstructured(obj)
	if err != nil {
		log.Error().Err(err).Msg("Error converting to tenant")
		return nil, err
	}
	return newObject, nil
}

func (tr *tenantResourceImpl) Delete(ctx context.Context, name string, namespace string, opts metav1.DeleteOptions) error {
	return tr.resource.Namespace(namespace).Delete(ctx, name, opts)
}

func (tr *tenantResourceImpl) UpdateStatus(ctx context.Context, t *Tenant, opts metav1.UpdateOptions) (*Tenant, error) {
	unstructuredObj, err := t.ToUnstructured()
	if err != nil {
		log.Error().Err(err).Msg("Error converting to unstructured")
		return nil, err
	}
	obj, err := tr.resource.Namespace(t.Namespace).UpdateStatus(ctx, unstructuredObj, opts)
	if err != nil {
		log.Error().Err(err).Msg("Error updating status")
		return nil, err
	}
	newObject := &Tenant{}
	err = newObject.FromUnstructured(obj)
	if err != nil {
		log.Error().Err(err).Msg("Error converting to tenant")
		return nil, err
	}
	return newObject, nil
}

func (tr *tenantResourceImpl) Update(ctx context.Context, t *Tenant, opts metav1.UpdateOptions) (*Tenant, error) {
	unstructuredObj, err := t.ToUnstructured()
	if err != nil {
		log.Error().Err(err).Msg("Error converting to unstructured")
		return nil, err
	}
	obj, err := tr.resource.Namespace(t.Namespace).Update(ctx, unstructuredObj, opts)
	if err != nil {
		log.Error().Err(err).Msg("Error updating tenant")
		return nil, err
	}
	newObject := &Tenant{}
	err = newObject.FromUnstructured(obj)
	if err != nil {
		log.Error().Err(err).Msg("Error converting to tenant")
		return nil, err
	}
	return newObject, nil
}

func NewTenantResource(clientset *dynamic.DynamicClient, namespace string) TenantResource {
	resource := clientset.Resource(schema.GroupVersionResource{
		Group:    "eng.augmentcode.com",
		Version:  "v1",
		Resource: "tenants",
	})
	return &tenantResourceImpl{
		resource: resource,
	}
}

// creates a dynamic client based on the given kubeconfig file.
// if the kubeconfig file is empty, the in-cluster config is used
func CreateDynamicClient(kubeconfig string) (*dynamic.DynamicClient, error) {
	var config *rest.Config
	if kubeconfig != "" {
		log.Info().Msgf("Using kubeconfig file: %s", kubeconfig)
		clusterConfig, err := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			&clientcmd.ClientConfigLoadingRules{ExplicitPath: kubeconfig},
			&clientcmd.ConfigOverrides{}).ClientConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating client config")
			return nil, err
		}
		config = clusterConfig
	} else {
		restConfig, err := rest.InClusterConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating in-cluster config")
			return nil, err
		}
		config = restConfig
	}

	return dynamic.NewForConfig(config)
}

// a fake TenantInformer to be used for tests
type FakeTenantInformer struct {
	tenants       []Tenant
	eventHandlers []TenantEventHandlerFuncs
}

func NewFakeTenantInformer() *FakeTenantInformer {
	return &FakeTenantInformer{}
}

func (ts *FakeTenantInformer) Update(t Tenant) {
	for i, existingTenant := range ts.tenants {
		if existingTenant.Name == t.Name {
			ts.tenants[i] = t
			for _, f := range ts.eventHandlers {
				f.UpdateFunc(&existingTenant, &t)
			}
			return
		}
	}

	ts.tenants = append(ts.tenants, t)
	for _, f := range ts.eventHandlers {
		f.AddFunc(&t, false)
	}
}

func (ts *FakeTenantInformer) Delete(t Tenant) {
	log.Info().Msgf("Delete: %v", t)
	for i, existingTenant := range ts.tenants {
		if existingTenant.Name == t.Name {
			// delete index i
			ts.tenants = append(ts.tenants[:i], ts.tenants[i+1:]...)
			for _, f := range ts.eventHandlers {
				f.DeleteFunc(&t)
			}
			log.Info().Msgf("Deleted: %v", ts.tenants)
			return
		}
	}
}

func (ts *FakeTenantInformer) Run(stopCh <-chan struct{}) {
}

func (ts *FakeTenantInformer) List() ([]Tenant, error) {
	return ts.tenants, nil
}

func (ts *FakeTenantInformer) AddEventHandler(f TenantEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	ts.eventHandlers = append(ts.eventHandlers, f)
	for _, t := range ts.tenants {
		f.AddFunc(&t, true)
	}
	return nil, nil
}

func (ts *FakeTenantInformer) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	return nil
}

// a FakeTenantResource to be used for tests
type FakeTenantResource struct {
	Tenants         map[string]Tenant
	resourceVersion int
	ch              chan struct{}
}

func NewFakeTenantResource() *FakeTenantResource {
	return &FakeTenantResource{
		Tenants:         map[string]Tenant{},
		resourceVersion: 0,
		ch:              make(chan struct{}),
	}
}

func (tr *FakeTenantResource) Create(ctx context.Context, t *Tenant, namespace string, opts metav1.CreateOptions) (*Tenant, error) {
	defer func() {
		tr.ch <- struct{}{}
	}()
	copy := *t
	copy.Namespace = namespace
	if copy.ResourceVersion == "" {
		copy.ResourceVersion = "0"
	}
	tr.resourceVersion += 1
	copy.ResourceVersion = strconv.Itoa(tr.resourceVersion)
	tr.Tenants[t.Name] = copy
	return &copy, nil
}

func (tr *FakeTenantResource) Delete(ctx context.Context, name string, namespace string, opts metav1.DeleteOptions) error {
	delete(tr.Tenants, name)
	return nil
}

func (tr *FakeTenantResource) UpdateStatus(ctx context.Context, t *Tenant, opts metav1.UpdateOptions) (*Tenant, error) {
	defer func() {
		tr.ch <- struct{}{}
	}()
	copy := *t
	if copy.ResourceVersion == "" {
		copy.ResourceVersion = "0"
	}
	rv, err := strconv.Atoi(copy.ResourceVersion)
	if err != nil {
		log.Error().Err(err).Msg("Error converting resource version")
		return nil, err
	}
	if rv > tr.resourceVersion {
		tr.resourceVersion = rv
	}
	tr.resourceVersion += 1
	copy.ResourceVersion = strconv.Itoa(tr.resourceVersion)
	tr.Tenants[t.Name] = copy
	return &copy, nil
}

func (tr *FakeTenantResource) Update(ctx context.Context, t *Tenant, opts metav1.UpdateOptions) (*Tenant, error) {
	defer func() {
		tr.ch <- struct{}{}
	}()
	copy := *t
	if copy.ResourceVersion == "" {
		copy.ResourceVersion = "0"
	}
	rv, err := strconv.Atoi(copy.ResourceVersion)
	if err != nil {
		log.Error().Err(err).Msg("Error converting resource version")
		return nil, err
	}
	if rv > tr.resourceVersion {
		tr.resourceVersion = rv
	}
	tr.resourceVersion += 1
	copy.ResourceVersion = strconv.Itoa(tr.resourceVersion)
	tr.Tenants[t.Name] = copy
	return &copy, nil
}

func (tr *FakeTenantResource) WaitForUpdate() {
	<-tr.ch
}

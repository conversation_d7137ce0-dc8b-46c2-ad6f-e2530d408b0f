load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "crd",
    srcs = ["crd.go"],
    importpath = "github.com/augmentcode/augment/services/tenant_watcher/crd",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_rs_zerolog//log",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/apis/meta/v1/unstructured",
        "@io_k8s_apimachinery//pkg/runtime/schema",
        "@io_k8s_client_go//dynamic",
        "@io_k8s_client_go//dynamic/dynamicinformer",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/cache",
        "@io_k8s_client_go//tools/clientcmd",
    ],
)

go_test(
    name = "crd_test",
    srcs = [
        "crd_test.go",
    ],
    embed = [":crd"],
)

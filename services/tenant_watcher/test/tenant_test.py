import contextlib
import time

import urllib3

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from services.tenant_watcher.client.client import TenantsClient

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def mark_tenant_deleted(
    deploy_info: k8s_test_helper.DeployInfo, tenant_name: str, deleted_at: str | None
):
    """Mark a tenant as deleted."""
    print("Marking tenant", tenant_name, "as deleted", flush=True)
    # read tenant object into dict
    tenant_obj = deploy_info.kubectl.get_object(
        "Tenant", tenant_name, deploy_info.namespace
    )
    assert tenant_obj is not None
    tenant_obj["spec"]["deleted_at"] = deleted_at
    deploy_info.kubectl.apply(tenant_obj)


def test_tenant_simple(
    application_deploy: k8s_test_helper.DeployInfo, tenant_watcher_client: TenantsClient
):
    """Tests that the app can handle multiple tenants."""

    with contextlib.ExitStack() as stack:
        tenant_to_delete = stack.enter_context(
            k8s_test_helper.new_tenant(application_deploy, "tenant2")
        )
        assert tenant_watcher_client.get_tenant_id("tenant2") == tenant_to_delete
        tenant_none = stack.enter_context(
            k8s_test_helper.new_tenant(application_deploy, "tenant3")
        )
        assert tenant_watcher_client.get_tenant_id("tenant3") == tenant_none
        tenant_empty_string = stack.enter_context(
            k8s_test_helper.new_tenant(application_deploy, "tenant4")
        )
        assert tenant_watcher_client.get_tenant_id("tenant4") == tenant_empty_string

        mark_tenant_deleted(
            application_deploy, "tenant2", time.strftime("%Y-%m-%dT%H:%M:%SZ")
        )
        # Test a few different versions of bad/empty values, none of them should
        # lead to deletion
        mark_tenant_deleted(application_deploy, "tenant3", None)
        mark_tenant_deleted(application_deploy, "tenant4", "")

        time.sleep(10)
        assert tenant_watcher_client.get_tenant_id("tenant2") is not None
        assert tenant_watcher_client.get_tenant_id("tenant3") is not None
        assert tenant_watcher_client.get_tenant_id("tenant4") is not None

        # Only tenant 2 should be deleted, this tests tenant gc. We run every
        # minute and have a 1m TTL, 15 min should be enough
        print("Waiting for tenant2 object to be deleted", flush=True)
        for _ in range(90):
            tenant2_obj = application_deploy.kubectl.get_object(
                "Tenant", "tenant2", application_deploy.namespace
            )
            if tenant2_obj is None:
                break
            time.sleep(10)
        else:
            raise ValueError("Tenant object for tenant2 was not deleted")
        tenant3_obj = application_deploy.kubectl.get_object(
            "Tenant", "tenant3", application_deploy.namespace
        )
        print(tenant3_obj)
        assert tenant3_obj is not None
        tenant4_obj = application_deploy.kubectl.get_object(
            "Tenant", "tenant4", application_deploy.namespace
        )
        print(tenant4_obj)
        assert tenant4_obj is not None

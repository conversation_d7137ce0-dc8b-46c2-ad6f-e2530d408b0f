load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/auth/central/server:kubecfg",
        "//services/bigtable_proxy/server:kubecfg",
        "//services/bigtable_proxy/server:kubecfg_central",
        "//services/content_manager/server:kubecfg",
        "//services/deploy:shard_namespace_base_kubecfg",
        "//services/request_insight:core_kubecfg",
        "//services/tenant_gc:kubecfg",
        "//services/tenant_watcher/server:kubecfg",
        "//services/test/fake_feature_flags:kubecfg",
        "//services/token_exchange/server:kubecfg",
    ],
)

pytest_test(
    name = "tenant_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "tenant_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/tenant_watcher/client",
        requirement("requests"),
        requirement("kubernetes"),
        requirement("pyyaml"),
    ],
)

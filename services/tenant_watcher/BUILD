load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

ts_proto_library(
    name = "tenant_watcher_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":tenant_watcher_proto",
    visibility = ["//services:__subpackages__"],
)

proto_library(
    name = "tenant_watcher_proto",
    srcs = ["tenant_watcher.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@protobuf//:duration_proto",
    ],
)

go_grpc_library(
    name = "tenant_watcher_go_proto",
    importpath = "github.com/augmentcode/augment/services/tenant_watcher/proto",
    proto = ":tenant_watcher_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)

py_grpc_library(
    name = "tenant_watcher_py_proto",
    protos = [":tenant_watcher_proto"],
    visibility = ["//services:__subpackages__"],
)

package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/status"

	"github.com/augmentcode/augment/base/go/secretstring"
	grpcdebugclient "github.com/augmentcode/augment/services/grpc_debug/client"
	grpcdebugproto "github.com/augmentcode/augment/services/grpc_debug/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	protojson "google.golang.org/protobuf/encoding/protojson"
)

type GrpcDebugHandler struct {
	grpcDebugClient     grpcdebugclient.GrpcDebugClient
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient
}

func (h *GrpcDebugHandler) getRequestContext(r *http.Request, tenantID string, scopes []tokenexchangeproto.Scope) (*requestcontext.RequestContext, error) {
	// extract from header
	iapToken := r.Header.Get("X-Goog-IAP-JWT-Assertion")
	if iapToken == "" {
		return nil, fmt.Errorf("iap token not found in request context")
	}
	secretIapToken := secretstring.New(iapToken)

	expiration := 10 * time.Minute
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForIAPToken(
		r.Context(), secretIapToken, tenantID, scopes,
		expiration)
	if err != nil {
		return nil, err
	}

	return &requestcontext.RequestContext{
		RequestId:        requestcontext.NewRandomRequestId(),
		RequestSessionId: requestcontext.NewRandomRequestSessionId(),
		RequestSource:    "grpc-debug",
		AuthToken:        serviceToken,
	}, nil
}

func (h *GrpcDebugHandler) Close() {
	h.grpcDebugClient.Close()
}

func (h *GrpcDebugHandler) handleGetEndpoints(w http.ResponseWriter, r *http.Request) {
	// Create a request context
	requestContext, err := h.getRequestContext(r, "", nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get request context")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Get the endpoints
	endpoints, err := h.grpcDebugClient.GetEndpoints(r.Context(), requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get endpoints")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Encode the endpoints as JSON
	json.NewEncoder(w).Encode(endpoints)
}

func (h *GrpcDebugHandler) handleGetServices(w http.ResponseWriter, r *http.Request) {
	type GetServicesRequest struct {
		Endpoint string `json:"endpoint"`
	}

	var request GetServicesRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		log.Warn().Msgf("Failed to decode request body: %v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	endpoint := request.Endpoint

	// Create a request context
	requestContext, err := h.getRequestContext(r, "", nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get request context")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Get the services
	services, err := h.grpcDebugClient.GetServices(r.Context(), endpoint, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get services")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Encode the services as JSON
	json.NewEncoder(w).Encode(services)
}

func (h *GrpcDebugHandler) handleGetServiceMethods(w http.ResponseWriter, r *http.Request) {
	type GetServiceMethodsRequest struct {
		Endpoint    string `json:"endpoint"`
		ServiceName string `json:"service"`
	}

	type GetServiceMethodsResponse struct {
		ServiceName        string `json:"serviceName"`
		MethodName         string `json:"methodName"`
		FullyQualifiedName string `json:"fullyQualifiedName"`
		EmptyRequestJson   string `json:"emptyRequestJson"`
	}

	var request GetServiceMethodsRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		log.Warn().Msgf("Failed to decode request body: %v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	endpoint := request.Endpoint
	serviceName := request.ServiceName

	// Create a request context
	requestContext, err := h.getRequestContext(r, "", nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get request context")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Get the service methods
	methods, err := h.grpcDebugClient.GetServiceMethods(r.Context(), endpoint, serviceName, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get service methods")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	response := make([]GetServiceMethodsResponse, len(methods))
	for i, method := range methods {
		response[i] = GetServiceMethodsResponse{
			ServiceName:        method.ServiceName,
			MethodName:         method.MethodName,
			FullyQualifiedName: method.FullyQualifiedName,
			EmptyRequestJson:   method.EmptyRequestJson,
		}
	}

	// Encode the service methods as JSON
	json.NewEncoder(w).Encode(response)
}

func (h *GrpcDebugHandler) handleInvokeMethod(w http.ResponseWriter, r *http.Request) {
	// Get the endpoint, service name, method name, and request from the request
	var request struct {
		Endpoint    string   `json:"endpoint"`
		ServiceName string   `json:"service"`
		MethodName  string   `json:"method"`
		RequestJson string   `json:"request"`
		TenantId    string   `json:"tenant_id"`
		Scopes      []string `json:"scopes"`
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		log.Warn().Msgf("Failed to decode request body: %v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	scopes := make([]tokenexchangeproto.Scope, len(request.Scopes))
	for i, scope := range request.Scopes {
		s, ok := tokenexchangeproto.Scope_value[scope]
		if !ok {
			log.Warn().Msgf("Invalid scope %s", scope)
			http.Error(w, fmt.Sprintf("Invalid scope %s", scope), http.StatusBadRequest)
			return
		}
		scopes[i] = tokenexchangeproto.Scope(s)
	}

	// Create a request context
	requestContext, err := h.getRequestContext(r, request.TenantId, scopes)
	if err != nil {
		if iss, ok := status.FromError(err); ok {
			status := iss.Proto()
			response := grpcdebugproto.MethodInvocationResponse{
				Error: status,
			}
			b, err := protojson.Marshal(&response)
			if err != nil {
				log.Error().Err(err).Msg("Failed to marshal response")
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			w.Write(b)
			return
		}
		log.Error().Err(err).Msg("Failed to get request context")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Invoke the method
	responseJson, err := h.grpcDebugClient.InvokeMethod(r.Context(), request.Endpoint, request.ServiceName, request.MethodName, request.RequestJson, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to invoke method")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	b, err := protojson.Marshal(responseJson)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal response")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	w.Write(b)
}

func (h *GrpcDebugHandler) GetTokenScopes(w http.ResponseWriter, r *http.Request) {
	scopes := make([]string, 0, len(tokenexchangeproto.Scope_value))
	for scope := range tokenexchangeproto.Scope_value {
		scopes = append(scopes, scope)
	}
	sort.Strings(scopes)
	json.NewEncoder(w).Encode(scopes)
}

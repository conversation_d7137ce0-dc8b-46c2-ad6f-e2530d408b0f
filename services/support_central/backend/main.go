package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"

	"github.com/augmentcode/augment/base/cloud/iap"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	grpcdebugclient "github.com/augmentcode/augment/services/grpc_debug/client"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
)

type Config struct {
	Port                   int                     `json:"port"`
	HttpsServerKey         string                  `json:"https_server_key"`
	HttpsServerCert        string                  `json:"https_server_cert"`
	ClientMtls             *tlsconfig.ClientConfig `json:"client_mtls"`
	IapAudience            string                  `json:"iap_audience"`
	IapJwtVerifierDisabled bool                    `json:"iap_jwt_verifier_disabled"`
	TenantWatcherGrpcUrl   string                  `json:"tenant_watcher_grpc_url"`
	GrpcDebugGrpcUrl       string                  `json:"grpc_debug_grpc_url"`
	TokenExchangeGrpcUrl   string                  `json:"token_exchange_grpc_url"`

	FeatureFlagsSdkKeyPath      string `json:"feature_flags_sdk_key_path"`
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`

	// Map of cloud to internal domain suffix
	InternalDomainSuffixes map[string]string `json:"internal_domain_suffixes"`

	// Used to help guide users to the right env (STAGING, PROD)
	Env              string            `json:"env"`
	IngressHostnames map[string]string `json:"ingress_hostnames"`
}

func loadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	var config Config
	if err := json.NewDecoder(file).Decode(&config); err != nil {
		return nil, err
	}
	log.Info().Msgf("Loaded config: %v", config)
	return &config, nil
}

func checkPath(r *http.Request) error {
	if r.URL.Path == "/" {
		return nil
	}
	if r.URL.Path[0] != '/' {
		return fmt.Errorf("path must start with /")
	}
	if strings.Contains(r.URL.Path[1:], "..") {
		return fmt.Errorf("path must not contain '..'")
	}
	return nil
}

func serveAssets(w http.ResponseWriter, r *http.Request) {
	log.Info().Msgf("Serving %s", r.URL.Path)
	err := checkPath(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	l := "services/support_central/frontend/dist/" + r.URL.Path[1:]
	http.ServeFile(w, r, l)
}

func serveIndex(w http.ResponseWriter, r *http.Request) {
	log.Info().Msgf("Serving index.html")
	http.ServeFile(w, r, "services/support_central/frontend/dist/index.html")
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("ok"))
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/health" {
			// Ignore for health checks
			next.ServeHTTP(w, r)
			return
		}
		log.Info().Msgf("%s %s", r.Method, r.URL.Path)
		next.ServeHTTP(w, r)
		// TODO: also log the response
	})
}

func iapVerificationMiddlewareFunc(config *Config) (func(http.Handler) http.Handler, error) {
	verifier, err := iap.New([]string{config.IapAudience})
	if err != nil {
		return nil, err
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Ignore for health checks
			if r.URL.Path == "/health" {
				next.ServeHTTP(w, r)
				return
			}

			// Get the IAP authentication headers
			iapToken := r.Header.Get("X-Goog-IAP-JWT-Assertion")

			email, err := verifier.Verify(iapToken)
			if err != nil {
				log.Warn().Msgf("Failed to verify IAP token: %v", err)
				http.Error(w, "Invalid IAP token", http.StatusUnauthorized)
				return
			}

			// Set the user's email in the request context
			ctx := context.WithValue(r.Context(), "userEmail", email)

			// Call the next handler in the chain
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}, nil
}

func main() {
	logging.SetupServerLogging()

	config, err := loadConfig("/config/config.json")
	if err != nil {
		log.Fatal().Msgf("Could not load config: %v", err)
	}

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	handler := newSupportCentral(config, ctx)

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	_, err = featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	tokenExchangeClient, err := tokenexchangeclient.New(config.TokenExchangeGrpcUrl, "",
		grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Msgf("Failed to create token exchange client: %v", err)
	}

	grpcDebugClient, err := grpcdebugclient.NewGrpcDebugClient(config.GrpcDebugGrpcUrl, centralClientCreds)
	if err != nil {
		log.Fatal().Msgf("Failed to create grpc debug client: %v", err)
	}

	grpcDebugHandler := &GrpcDebugHandler{
		grpcDebugClient:     grpcDebugClient,
		tokenExchangeClient: tokenExchangeClient,
	}

	r := mux.NewRouter()
	r.HandleFunc("/", serveIndex).Methods("GET")
	r.HandleFunc("/grpc-debug", serveIndex).Methods("GET")
	r.HandleFunc("/api/grpc_endpoints", grpcDebugHandler.handleGetEndpoints).Methods("POST")
	r.HandleFunc("/api/grpc_service_methods", grpcDebugHandler.handleGetServiceMethods).Methods("POST")
	r.HandleFunc("/api/grpc_invoke_method",
		grpcDebugHandler.handleInvokeMethod).Methods("POST")
	r.HandleFunc("/api/grpc_token_scopes", grpcDebugHandler.GetTokenScopes).Methods("GET")
	r.HandleFunc("/health", healthCheck).Methods("GET")
	r.HandleFunc("/assets/{path:.*}", serveAssets).Methods("GET")
	r.HandleFunc("/favicon.svg", serveAssets).Methods("GET")
	r.HandleFunc("/manifest.json", serveAssets).Methods("GET")
	r.NotFoundHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Warn().Msgf("Not found: %s", r.URL.Path)
		http.Error(w, "Not Found", http.StatusNotFound)
	})
	r.Use(loggingMiddleware)
	if config.IapJwtVerifierDisabled {
		log.Info().Msg("IAP JWT verifier disabled")
	} else {
		log.Info().Msgf("IAP JWT verifier enabled with audience %s", config.IapAudience)
		middleware, err := iapVerificationMiddlewareFunc(config)
		if err != nil {
			log.Fatal().Msgf("Failed to create IAP JWT verifier: %v", err)
		}
		r.Use(middleware)
	}

	handler.register(r)

	srv := &http.Server{
		Handler: r,
		Addr:    fmt.Sprintf(":%d", config.Port),
	}
	log.Info().Msgf("Listening on %d", config.Port)

	if err := srv.ListenAndServeTLS(config.HttpsServerCert, config.HttpsServerKey); err != nil {
		log.Fatal().Msgf("Failed to listen: %v", err)
	}
}

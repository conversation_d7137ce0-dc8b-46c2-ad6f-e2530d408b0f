package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"

	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

type supportCentral struct {
	tenantCache            tenantclient.TenantCache
	internalDomainSuffixes map[string]string
	env                    string
	ingressHostnames       map[string]string
}

type supportUrl struct {
	Name string `json:"name"`
	Url  string `json:"url"`
}

type tenantSupportDetails struct {
	Tenant      *tenantproto.Tenant `json:"tenant"`
	SupportUrls []supportUrl        `json:"support_urls"`
}

func (sc *supportCentral) register(r *mux.Router) {
	r.<PERSON>le<PERSON>unc("/api/tenants", sc.tenantsHandler).Methods("GET")
	r.Handle<PERSON>unc("/api/env_info", sc.envInfoHandler).Methods("GET")
}

func (sc *supportCentral) tenantsHandler(w http.ResponseWriter, r *http.Request) {
	tenants, err := sc.tenantCache.GetAllTenants()
	if err != nil {
		log.Error().Err(err).Msg("Error getting tenants")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	resp := make([]tenantSupportDetails, len(tenants))
	for i, tenant := range tenants {
		resp[i] = tenantSupportDetails{
			Tenant: tenant,
		}
		domainSuffix, ok := sc.internalDomainSuffixes[tenant.Cloud]
		if !ok {
			log.Error().Msgf("No domain suffix for cloud %s", tenant.Cloud)
			continue
		}
		gcpProject := "system-services-prod"
		if strings.HasSuffix(tenant.Cloud, "_DEV") {
			gcpProject = "system-services-dev"
		}
		resp[i].SupportUrls = []supportUrl{
			{
				Name: "Support (RI)",
				Url:  fmt.Sprintf("https://support.%s.t.%s/t/%s", tenant.ShardNamespace, domainSuffix, tenant.Name),
			},
			{
				Name: "Jaeger (traces)",
				Url:  fmt.Sprintf("https://jaeger.%s/search?service=api-proxy&tags=%%7B\"tenant_name\"%%3A\"%s\"%%2C\"service.namespace\"%%3A\"%s\"%%7D", domainSuffix, tenant.Name, tenant.ShardNamespace),
			},
			{
				Name: "Grafana (dashboards)",
				Url:  fmt.Sprintf("https://grafana.%s", domainSuffix),
			},
			{
				Name: "Genie",
				Url:  fmt.Sprintf("https://genie.%s", domainSuffix),
			},
			{
				Name: "Logs",
				Url:  fmt.Sprintf("https://console.cloud.google.com/logs/query;query=resource.labels.namespace_name%%3D%%22%s%%22;duration=PT3H?project=%s", tenant.ShardNamespace, gcpProject),
			},
		}
	}

	w.Header().Set("Content-Type", "application/json")
	err = json.NewEncoder(w).Encode(resp)
	if err != nil {
		log.Error().Err(err).Msg("Error encoding response")
		w.WriteHeader(http.StatusInternalServerError)
	}
}

func (sc *supportCentral) envInfoHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	alternateEnv := "STAGING"
	if sc.env == "STAGING" {
		alternateEnv = "PROD"
	}
	link := "https://" + sc.ingressHostnames[alternateEnv]
	prefix := fmt.Sprintf("You are seeing %s customers, to see %s customers, go to ", sc.env, alternateEnv)
	err := json.NewEncoder(w).Encode(struct {
		Prefix string `json:"prefix"`
		Link   string `json:"link"`
	}{
		Prefix: prefix,
		Link:   link,
	})
	if err != nil {
		log.Error().Err(err).Msg("Error encoding response")
		w.WriteHeader(http.StatusInternalServerError)
	}
}

func newSupportCentral(config *Config, ctx context.Context) *supportCentral {
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	tenantClient := tenantclient.New(config.TenantWatcherGrpcUrl, grpc.WithTransportCredentials(clientCreds))
	tenantCache := tenantclient.NewTenantCache(tenantClient, "")
	return &supportCentral{
		internalDomainSuffixes: config.InternalDomainSuffixes,
		tenantCache:            tenantCache,
		env:                    config.Env,
		ingressHostnames:       config.IngressHostnames,
	}
}

load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")

go_library(
    name = "support_central_lib",
    srcs = [
        "grpc_debug_handler.go",
        "handler.go",
        "main.go",
    ],
    data = ["//services/support_central/frontend"],
    importpath = "github.com/augmentcode/augment/services/support_central/backend",
    deps = [
        "//base/cloud/iap:iap_go",
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//services/grpc_debug:grpc_debug_go_proto",
        "//services/grpc_debug/client:client_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_gorilla_mux//:go_default_library",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_google_protobuf//encoding/protojson:go_default_library",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "support_central",
    embed = [":support_central_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":support_central",
    visibility = ["//services/support_central:__subpackages__"],
)

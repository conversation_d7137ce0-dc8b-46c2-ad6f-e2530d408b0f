// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'support-central',
      kubecfg: {
        target: '//services/support_central:kubecfg',
        // Global service, one in staging and one in prod
        task: [cn for cn in cloudInfo.centralNamespaces if cloudInfo.isLeadProdCluster(cn.cloud)],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['aswin'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

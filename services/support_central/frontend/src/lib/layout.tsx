// react component containing the overall layout of the pages
import React from "react";
import { useEffect, useState } from "react";
import { Breadcrumb, Divider, Layout, Menu, Typography, theme } from "antd";
import { Link } from "react-router-dom";
import { getEnvInfo, EnvInfo } from "../lib/envinfo";

const { Header, Footer, Content } = Layout;
const { Text } = Typography;

type BreadcrumbItem = {
  label: string;
  link: string;
};

type Probs = {
  // key of the menu item to select
  selectedMenuKey?: string;
  // react children nodes to display
  children?: React.ReactNode;
  // breadcrumb items to display
  breadcrumbs: BreadcrumbItem[];
};

export const LayoutComponent = ({
  children,
  selectedMenuKey,
  breadcrumbs,
}: Probs) => {
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  let defaultSelectedKeys: Array<string> = ["home"];
  if (selectedMenuKey) {
    defaultSelectedKeys = [selectedMenuKey];
  }

  const [envInfo, setEnvInfo] = useState<EnvInfo | undefined>(undefined);
  useEffect(() => {
    const fetchEnvInfo = async () => {
      try {
        setEnvInfo(await getEnvInfo());
      } catch (e) {
        console.log(`Error while loading the domain msg data: ${e}`);
      }
    };
    fetchEnvInfo();
  }, []);

  return (
    <Layout className="layout">
      <Header>
        <div className="logo" />
        <Menu
          theme="dark"
          mode="horizontal"
          defaultSelectedKeys={defaultSelectedKeys}
          items={[
            {
              key: "home",
              label: <Link to={"/"}>Tenants</Link>,
            },
            {
              key: "grpc",
              label: <Link to={"/grpc-debug"}>GRPC</Link>,
            },
          ]}
        />
      </Header>
      <Content style={{ padding: "0 50px" }}>
        <Breadcrumb style={{ margin: "16px 0" }}>
          <Breadcrumb.Item>
            <Link to={"/"}>Home</Link>
          </Breadcrumb.Item>
          {breadcrumbs.map((b) => {
            return (
              <Breadcrumb.Item key={b.label}>
                <Link to={b.link}>{b.label}</Link>
              </Breadcrumb.Item>
            );
          })}
        </Breadcrumb>
        <div
          className="site-layout-content"
          style={{ background: colorBgContainer }}
        >
          {envInfo ? (
            <Text>
              <p>
                {envInfo.prefix}
                <Link to={envInfo.link}>{envInfo.link}</Link>{" "}
              </p>
              <Divider />
            </Text>
          ) : null}
          {children}
        </div>
      </Content>
      <Footer style={{ textAlign: "center" }}>Augment Code - Internal</Footer>
    </Layout>
  );
};

function App() {
  const children: any[] = [];
  return <LayoutComponent children={children} breadcrumbs={[]} />;
}
export default App;

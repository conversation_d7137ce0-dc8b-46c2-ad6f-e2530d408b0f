import { LayoutComponent } from "../lib/layout";

import { useEffect, useState } from "react";
import { getTenants } from "../lib/tenants";

import GrpcDebugComponent from "@augment-internal/grpc_debug/grpc_debug_component";
import { Tenant } from "@augment-internal/grpc_debug/grpc_debug";

function GrpcDebugPageComponent() {
  const [tenantsInfo, setTenantsInfo] = useState<Tenant[] | undefined>(
    undefined,
  );
  useEffect(() => {
    const fetchTenants = async () => {
      try {
        const t = await getTenants();
        const tenants = [{ name: "*", tenantId: "" }].concat(
          t.map((t) => ({ name: t.tenant.name, tenantId: t.tenant.id })),
        );
        setTenantsInfo(tenants);
      } catch (e) {
        console.log(`Error while loading the tenants data: ${e}`);
      }
    };
    fetchTenants();
  }, []);
  const children = <GrpcDebugComponent tenantsInfo={tenantsInfo} />;
  return (
    <LayoutComponent
      children={children}
      selectedMenuKey={"grpc"}
      breadcrumbs={[{ label: "GRPC Debug", link: "/grpc-debug" }]}
    />
  );
}

export default GrpcDebugPageComponent;

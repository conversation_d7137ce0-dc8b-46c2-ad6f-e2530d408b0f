// Page displaying details about tenants
import { Al<PERSON>, AutoComplete, Spin, List, Table, Typography } from "antd";
import { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import axios from "axios";
import { getTenants, TenantSupportDetails } from "../lib/tenants";
import { ColumnType } from "antd/es/table";

const { Text } = Typography;

export function TenantsComponent() {
  const [allTenantsInfo, setAllTenantsInfo] = useState<
    TenantSupportDetails[] | undefined
  >(undefined);
  const [tenantsInfo, setTenantsInfo] = useState<TenantSupportDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [errorData, setErrorData] = useState<string | undefined>(undefined);

  useEffect(() => {
    const fetchAllTenants = async () => {
      try {
        setIsLoading(true);
        const tenants = await getTenants();
        setTenantsInfo(tenants);
        setAllTenantsInfo(tenants);
      } catch (e) {
        console.log(`Error while loading the tenants data: ${e}`);
        if (axios.isAxiosError(e)) {
          setErrorData(`Failed to load data: ${e.message}`);
        } else {
          setErrorData("Failed to load data");
        }
      }
      setIsLoading(false);
    };
    fetchAllTenants();
  }, []);

  if (errorData !== undefined) {
    return <Alert message={errorData} type="error" />;
  } else if (isLoading || allTenantsInfo === undefined) {
    return <Spin />;
  } else {
    console.log(`tenantsInfo ${JSON.stringify(tenantsInfo)}`);
    const sortTenants = (a: TenantSupportDetails, b: TenantSupportDetails) => {
      const a_support_tenant =
        a.tenant.config.configs?.support_tenant === "true";
      const b_support_tenant =
        b.tenant.config.configs?.support_tenant === "true";
      if (a_support_tenant && !b_support_tenant) {
        return 1;
      } else if (b_support_tenant && !a_support_tenant) {
        return -1;
      }
      return a.tenant.name.localeCompare(b.tenant.name);
    };

    const search = (
      <p>
        <AutoComplete
          allowClear={true}
          autoFocus={true}
          placeholder="Filter tenants"
          style={{ width: "100%" }}
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          onChange={(value: string) => {
            setTenantsInfo(
              allTenantsInfo.filter((t) =>
                t.tenant.name.toLowerCase().includes(value.toLowerCase()),
              ),
            );
          }}
        />
      </p>
    );

    const columns: ColumnType<TenantSupportDetails>[] = [
      {
        title: "Tenant Name",
        key: "name",
        render: (t: TenantSupportDetails) => {
          return (
            <Text>
              {t.tenant.name}
              <br></br>
              {t.tenant.config.configs?.support_tenant === "true"
                ? " (support tenant)"
                : ""}
            </Text>
          );
        },
        defaultSortOrder: "ascend",
        sorter: sortTenants,
        filters: tenantsInfo.sort(sortTenants).map((t) => {
          return {
            text: t.tenant.name,
            value: t.tenant.name,
          };
        }),
        onFilter: (value: string, record: TenantSupportDetails) =>
          record.tenant.name === value,
      },
      {
        title: "Tenant ID",
        key: "id",
        render: (t: TenantSupportDetails) => {
          return <Text keyboard>{t.tenant.id}</Text>;
        },
      },
      {
        title: "K8s Namespace",
        key: "namespace",
        render: (t: TenantSupportDetails) => {
          return <Text>{t.tenant.shard_namespace}</Text>;
        },
        sorter: (a: TenantSupportDetails, b: TenantSupportDetails) => {
          return a.tenant.shard_namespace.localeCompare(
            b.tenant.shard_namespace,
          );
        },
      },
      {
        title: "Links",
        key: "links",
        render: (t: TenantSupportDetails) => {
          return (
            <List
              size="small"
              dataSource={t.support_urls || []}
              renderItem={(item) => (
                <List.Item>
                  <a href={item.url}>{item.name}</a>
                </List.Item>
              )}
            />
          );
        },
      },
      {
        title: "Config",
        key: "config",
        render: (t: TenantSupportDetails) => {
          return (
            <Text keyboard>{JSON.stringify(t.tenant.config.configs)}</Text>
          );
        },
      },
    ];

    const table = (
      <Table size="middle" dataSource={tenantsInfo} columns={columns} />
    );

    return [search, table];
  }
}

export default function TenantsPageComponent() {
  const children = <TenantsComponent />;
  return (
    <LayoutComponent
      children={children}
      selectedMenuKey={""}
      breadcrumbs={[]}
    />
  );
}

import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import TenantsPageComponent from "./routes/tenants";
import ErrorPage from "./error-page";
import GrpcDebugPageComponent from "./routes/grpc_debug";
// sets up the different routes
const router = createBrowserRouter([
  {
    path: "/",
    element: <TenantsPageComponent />,
    errorElement: <ErrorPage />,
  },
  {
    path: "grpc-debug",
    element: <GrpcDebugPageComponent />,
  },
]);
const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);

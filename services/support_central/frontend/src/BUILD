load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@npm//services/support_central/frontend:eslint/package_json.bzl", eslint_bin = "bin")

ASSET_PATTERNS = [
    "*.svg",
    "*.css",
]

SRC_PATTERNS = [
    "*.tsx",
    "routes/*.tsx",
    "lib/*.ts",
    "lib/*.tsx",
]

js_library(
    name = "src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    visibility = ["//services/support_central/frontend:__subpackages__"],
    deps = [
        "//services/support_central/frontend:node_modules/antd",
        "//services/support_central/frontend:node_modules/axios",
        "//services/support_central/frontend:node_modules/react",
        "//services/support_central/frontend:node_modules/react-dom",
        "//services/support_central/frontend:node_modules/react-router-dom",
    ],
)

eslint_bin.eslint_test(
    name = "eslint_test",
    args = ["{}/{}".format(
        package_name(),
        p,
    ) for p in SRC_PATTERNS],
    data = [
        "//services/support_central/frontend:eslintrc_cjs",
        "//services/support_central/frontend:node_modules/@typescript-eslint/eslint-plugin",
        "//services/support_central/frontend:node_modules/@typescript-eslint/parser",
        "//services/support_central/frontend:node_modules/eslint-plugin-react-hooks",
        "//services/support_central/frontend:node_modules/react",
        "//services/support_central/frontend:package_json",
    ] + glob(SRC_PATTERNS),
)

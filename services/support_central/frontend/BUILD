load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//services/support_central/frontend:vite/package_json.bzl", vite_bin = "bin")

npm_link_all_packages()

COMMON = [
    ":index.html",
    ":vite.config.ts",
    ":package.json",
    ":tsconfig",
    "//services/support_central/frontend/src",
    "//services/support_central/frontend/public",
]

ALL_MODULES = [
    ":node_modules",
]

BUILD_MODULES = [
    ":node_modules/@augment-internal/grpc_debug",
    ":node_modules/@vitejs/plugin-react",
    ":node_modules/eslint-plugin-react-hooks",
    ":node_modules/react-dom",
    ":node_modules/vite",
    ":node_modules/react",
    ":node_modules/typescript",
    ":node_modules/axios",
    ":node_modules/antd",
    ":node_modules/react-json-view",
]

vite_bin.vite(
    name = "frontend",
    srcs = BUILD_MODULES + COMMON,
    args = ["build"],
    chdir = package_name(),
    out_dirs = ["dist"],
    visibility = ["//services/support_central:__subpackages__"],
)

vite_bin.vite_binary(
    name = "start",
    chdir = package_name(),
    data = ALL_MODULES + COMMON,
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//services/support_central/frontend:__subpackages__"],
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
    visibility = ["//services/support_central/frontend:__subpackages__"],
)

js_library(
    name = "eslintrc_cjs",
    srcs = [".eslintrc.cjs"],
    visibility = ["//services/support_central/frontend:__subpackages__"],
)

{"name": "support_central", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --files", "prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files"}, "dependencies": {"@augment-internal/grpc_debug": "workspace:*", "antd": "^5.24.6", "axios": "^1.8.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.24.0", "react-json-view": "^1.21.3"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "typescript": "^5.4.5", "vite": "5.4.19"}}
// K8S deployment file for the central support UI
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
function(env, namespace, cloud, namespace_config)
  assert cloudInfo.isLeadCluster(cloud);
  assert cloudInfo.isCentralNamespace(env, namespace, cloud);

  local appName = 'support-central';
  local backendConfig = gcpLib.createBackendConfig(app=appName,
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTPS',
                                                     requestPath: '/health',
                                                   },
                                                   timeoutSec=300,
                                                   iap=true);
  local clientCert = certLib.createCentralClientCert(
    name='support-central-client-cert',
    namespace=namespace,
    appName=appName,
    env=env,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace)
  );
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local ingressHostnames = {
    PROD: 'support.%s' % domainSuffix,
    STAGING: 'support.staging.%s' % domainSuffix,
    DEV: 'support.%s.%s' % [namespace, domainSuffix],
  };
  local ingressHostname = ingressHostnames[env];
  local ingressFacingCert = certLib.createPublicServerCert(name='support-central-public-cert',
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           volumeName='https-certs',
                                                           env=env);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'support-central-svc',
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'viewer-https': 'HTTPS' }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'viewer-https',
          targetPort: 'viewer-https',
        },
      ],
    },
  };
  local iapAudience = '/projects/%s/global/backendServices/%s' % [cloudInfo[cloud].projectNumber, if std.objectHas(namespace_config, 'iapAudience') then namespace_config.iapAudience else ''];
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local tenantWatcherGrpcUrl = endpoints.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local tokenExchangeGrpcUrl = endpoints.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    port: 5000,
    https_server_key: '/https-certs/tls.key',
    https_server_cert: '/https-certs/tls.crt',
    client_mtls: if mtls then clientCert.config else null,
    iap_audience: iapAudience,
    iap_jwt_verifier_disabled: namespace_config.flags.iapJwtVerifierDisabled,
    tenant_watcher_grpc_url: tenantWatcherGrpcUrl,
    token_exchange_grpc_url: tokenExchangeGrpcUrl,
    grpc_debug_grpc_url: 'grpc-debug-svc:50051',
    internal_domain_suffixes: {
      [cloud]: cloudInfo[cloud].internalDomainSuffix
      for cloud in std.objectFields(cloudInfo)
    },
    env: env,
    ingress_hostnames: ingressHostnames,
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
  });
  local container =
    {
      name: 'support-central',
      target: {
        name: '//services/support_central/backend:image',
        dst: 'support-central',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'viewer-https',
        },
      ],
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        ingressFacingCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      // the environment variables that are passed to the server
      env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      readinessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '512Mi',
        },
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local serviceAccount = gcpLib.createServiceAccount(app='support-central', cloud=cloud, env=env, namespace=namespace);
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      affinity: affinity,
      tolerations: tolerations,
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        clientCert.podVolumeDef,
        configMap.podVolumeDef,
        ingressFacingCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app='support-central', cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: 'support-central',
        },
        name: 'support-central-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'support-central-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'support-central-svc',
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'support-central',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    configMap.objects,
    service,
    serviceAccount.objects,
    ingressFacingCert.objects,
    deployment,
    ingressObjects,
    clientCert.objects,
    dynamicFeatureFlags.k8s_objects,
  ])

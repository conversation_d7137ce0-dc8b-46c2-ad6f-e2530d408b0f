local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'billing-webhook',
      kubecfg: {
        target: '//services/auth/billing_webhook:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['xiaolei'],
          slack_channel: '#team-growth',
        },
      },
    },
  ],
}

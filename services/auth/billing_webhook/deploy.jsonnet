// K8S deployment file for the billing webhook service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  // This is a global service
  assert cloudInfo.isLeadCluster(cloud);

  local appName = 'billing-webhook';

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );

  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local ingressHostname = {
    PROD: '%s.%s' % [appName, domainSuffix],
    STAGING: '%s.staging.%s' % [appName, domainSuffix],
    DEV: '%s.%s.%s' % [appName, namespace, domainSuffix],
  }[env];

  local serverCert = certLib.createPublicServerCert(
    name='%s-server-cert' % appName,
    namespace=namespace,
    appName=appName,
    dnsNames=[ingressHostname],
    volumeName='https-certs',
    env=env,
  );

  local pubsubTopic = pubsubLib.publisherTopic(
    cloud=cloud,
    env=env,
    namespace=namespace,
    appName=appName,
    serviceAccount=serviceAccount,
  );

  local backendConfig = gcpLib.createBackendConfig(
    app=appName,
    cloud=cloud,
    namespace=namespace,
    healthCheck={
      checkIntervalSec: 15,
      port: 5000,
      type: 'HTTPS',
      requestPath: '/health',
    },
  );

  local httpService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: '%s-svc' % appName,
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'public-https': 'HTTPS' }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'public-https',
          targetPort: 'public-https',
        },
      ],
    },
  };

  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);

  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: '%s-ingress' % appName,
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: '%s-ssl-cert' % appName,  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: httpService.metadata.name,
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  // Orb webhook secret configuration
  local orbWebhookSecretOverride = if env == 'DEV' then namespace_config.flags.orbWebhookSecretOverride else {};
  local orbWebhookSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    // Use the override if provided, otherwise use the default
    purpose=if std.objectHas(orbWebhookSecretOverride, 'name') then
      // If name is provided in the override, use it directly as the purpose
      std.strReplace(orbWebhookSecretOverride.name, 'dev-billing-webhook-', '')
    else if env != 'DEV' then
      'orb-signing-secret'
    else
      'orb-signing-secret-%s' % namespace,
    version=if std.objectHas(orbWebhookSecretOverride, 'version') then orbWebhookSecretOverride.version else {
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=if std.objectHas(orbWebhookSecretOverride, 'name') then orbWebhookSecretOverride.name else null,
  );

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    orb_webhook_port: 5000,
    prom_port: 9090,
    server_cert_config: serverCert.config,
    orb_signing_secret_path: orbWebhookSecret.filePath,
    project_id: cloudInfo[cloud].projectId,
    pubsub_topic: pubsubTopic.topicName,
  });

  local container = {
    name: appName,
    target: {
      name: '//services/auth/billing_webhook:image',
      dst: 'billing-webhook',
    },
    ports: [
      {
        containerPort: 5000,
        name: 'public-https',
      },
    ],
    args: [
      '--config',
      configMap.filename,
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    volumeMounts: [
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      orbWebhookSecret.volumeMountDef,
    ],
    readinessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 5000,
      },
      initialDelaySeconds: 5,
      periodSeconds: 10,
    },
    livenessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 5000,
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };

  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      serverCert.podVolumeDef,
      orbWebhookSecret.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      progressDeadlineSeconds: 1200,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  lib.flatten([
    serviceAccount.objects,
    serverCert.objects,
    pubsubTopic.objects,
    httpService,
    if namespace_config.flags.loadBalancerType == 'ingress' then ingressObjects else [],
    orbWebhookSecret.objects,
    configMap.objects,
    deployment,
  ])

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
)

var configFile = flag.String("config", "", "Path to config file")

// Config holds the configuration for the Orb webhook server
type Config struct {
	OrbWebhookPort       int                     `json:"orb_webhook_port"`
	PromPort             int                     `json:"prom_port"`
	ServerCertConfig     *tlsconfig.ServerConfig `json:"server_cert_config"`
	OrbSigningSecretPath string                  `json:"orb_signing_secret_path"`
	ProjectID            string                  `json:"project_id"`
	PubsubTopic          string                  `json:"pubsub_topic"`
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	serverCertPath := config.ServerCertConfig.CertPath
	serverKeyPath := config.ServerCertConfig.KeyPath

	orbSigningSecret, err := os.ReadFile(config.OrbSigningSecretPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading Orb webhook signing secret")
	}

	orbSecretStr := string(orbSigningSecret)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		cancel()
	}()

	// Setup tracing and metrics
	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup publisher client and topic
	pubsubClient, err := pubsub.NewClient(ctx, config.ProjectID)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating pubsub client")
	}
	defer pubsubClient.Close()

	pubsubTopic := pubsubClient.Topic(config.PubsubTopic)
	defer pubsubTopic.Stop()

	// Start Orb webhook server
	orbWebhookHandler := NewOrbWebhookHandler(
		secretstring.New(orbSecretStr), pubsubTopic,
	)

	mux := http.NewServeMux()
	// Use the /orb path to maintain compatibility with existing integrations
	mux.HandleFunc("/orb", orbWebhookHandler.HandleWebhook)
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", config.OrbWebhookPort),
		Handler: mux,
	}

	go func() {
		<-ctx.Done()
		server.Shutdown(context.Background())
	}()

	log.Info().Msgf("Starting Orb billing webhook server on port %d", config.OrbWebhookPort)
	if err := server.ListenAndServeTLS(serverCertPath, serverKeyPath); err != nil && err != http.ErrServerClosed {
		log.Fatal().Err(err).Msg("Error starting server")
	}

	log.Info().Msg("Server stopped")
}

package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/services/auth/billing_webhook/orb_event"
	"github.com/augmentcode/augment/services/integrations/orb"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

var orbWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
	Name: "orb_webhook_request_total",
	Help: "Counter of handled Orb webhook requests",
}, []string{"status_code", "event_type"})

// OrbWebhookHandler handles Orb webhook events
type OrbWebhookHandler struct {
	orbClient orb.OrbClient
	topic     *pubsub.Topic
}

// NewOrbWebhookHandler creates a new Orb webhook handler
func NewOrbWebhookHandler(orbWebhookSecret secretstring.SecretString, topic *pubsub.Topic) *OrbWebhookHandler {
	return &OrbWebhookHandler{
		orbClient: orb.NewOrbClientWithWebhook("", orbWebhookSecret.Expose()), // Only using for Orb webhook verification
		topic:     topic,
	}
}

// HandleWebhook handles Orb webhook events
func (h *OrbWebhookHandler) HandleWebhook(w http.ResponseWriter, r *http.Request) {
	requestID := requestcontext.NewRandomRequestId()
	log.Info().Msgf("Received Orb webhook request, assigned request ID %s", requestID)

	statusCode, err := h.handleHelper(r, requestID)
	if err != nil {
		log.Error().Err(err).Msgf("Error handling Orb webhook request %s", requestID)
	}
	w.WriteHeader(statusCode)
}

func (h *OrbWebhookHandler) handleHelper(r *http.Request, requestID requestcontext.RequestId) (statusCode int, err error) {
	// Default event type for metrics
	eventType := "unknown"

	defer func() {
		orbWebhookRequestCounter.WithLabelValues(strconv.Itoa(statusCode), eventType).Inc()
	}()

	// Verify the Orb webhook signature and get the raw body
	body, err := h.orbClient.VerifyWebhookSignature(r)
	if err != nil {
		log.Error().Err(err).Msg("Error verifying Orb webhook signature")
		return http.StatusBadRequest, fmt.Errorf("Orb webhook signature verification failed: %w", err)
	}

	// Parse the event
	var rawEvent map[string]interface{}
	if err := json.Unmarshal(body, &rawEvent); err != nil {
		log.Error().Err(err).Msg("Error parsing Orb webhook payload")
		return http.StatusBadRequest, fmt.Errorf("error parsing Orb webhook payload: %w", err)
	}

	// Extract the event type
	if eventTypeRaw, ok := rawEvent["type"].(string); ok {
		eventType = eventTypeRaw
	} else {
		log.Error().Msg("Event type not found in Orb webhook payload")
		return http.StatusBadRequest, fmt.Errorf("event type not found in Orb webhook payload")
	}
	// Create Orb event
	orbEvent := &orb_event.OrbEvent{
		Metadata: &orb_event.EventMetadata{
			RequestId:   string(requestID),
			ProcessedAt: timestamppb.New(time.Now()), // When our service processed the event
		},
		EventType:  eventType,
		RawPayload: string(body),
	}

	// Extract the X-Orb-Timestamp header if available
	// This represents when Orb generated the webhook event
	if orbTimestampHeader := r.Header.Get("X-Orb-Timestamp"); orbTimestampHeader != "" {
		// Try to parse the timestamp
		orbTimestamp, err := time.Parse("2006-01-02T15:04:05.999", orbTimestampHeader)
		if err == nil {
			orbEvent.Metadata.OrbTimestamp = timestamppb.New(orbTimestamp)
			log.Info().Str("orb_timestamp", orbTimestamp.String()).Msg("Extracted Orb timestamp from header")
		} else {
			log.Warn().Str("header_value", orbTimestampHeader).Err(err).Msg("Failed to parse X-Orb-Timestamp header")
		}
	}

	// Extract event ID if available
	if eventID, ok := rawEvent["id"].(string); ok {
		orbEvent.Metadata.EventId = eventID
	}

	// Handle additional event-specific data
	orbEvent = h.parseAdditionalOrbEventData(rawEvent, eventType, orbEvent)

	// Serialize the event for publishing
	data, err := proto.Marshal(orbEvent)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling Orb event")
		return http.StatusInternalServerError, fmt.Errorf("error marshalling Orb event: %w", err)
	}

	// Create the message
	msg := &pubsub.Message{
		Data: data,
	}

	// Publish the event to PubSub
	ctx := r.Context()
	publishResult := h.topic.Publish(ctx, msg)

	if publishResult != nil {
		_, err = publishResult.Get(ctx)
	}

	if err != nil {
		log.Error().Err(err).Msg("Error publishing to PubSub")
		return http.StatusInternalServerError, fmt.Errorf("error publishing to PubSub: %w", err)
	}

	log.Info().Interface("event", orbEvent).Msgf("Published %s event with request ID %s to PubSub topic %s", eventType, requestID, h.topic.ID())
	return http.StatusOK, nil
}

func (h *OrbWebhookHandler) parseAdditionalOrbEventData(rawEvent map[string]interface{}, eventType string, orbEvent *orb_event.OrbEvent) *orb_event.OrbEvent {
	// Parse specific event types

	// TODO: Improve JSON unmarshalling by creating proper structs for each event type
	// instead of using map[string]interface{} and type assertions.
	// This would make the code more maintainable and type-safe.
	switch eventType {
	// Credit balance events
	case "customer.credit_balance_depleted", "customer.credit_balance_recovered":
		if customerData, ok := rawEvent["customer"].(map[string]interface{}); ok {
			orbEvent = h.parseCustomerData(customerData, orbEvent)
		}
		properties := rawEvent["properties"].(map[string]interface{})
		pricing_unit := properties["pricing_unit"].(map[string]interface{})
		pricing_name := pricing_unit["name"].(string)
		orbEvent.Event = &orb_event.OrbEvent_CustomerBalanceEvent{
			CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{PricingUnit: pricing_name},
		}
		return orbEvent
	// Invoice events
	case "invoice.payment_failed":
		if invoiceData, ok := rawEvent["invoice"].(map[string]interface{}); ok {
			invoiceEvent := &orb_event.InvoiceEvent{}

			// Extract invoice ID
			if invoiceID, ok := invoiceData["id"].(string); ok {
				invoiceEvent.InvoiceId = invoiceID
			}

			// Extract customer data if available
			if customerData, ok := invoiceData["customer"].(map[string]interface{}); ok {
				orbEvent = h.parseCustomerData(customerData, orbEvent)
			}

			orbEvent.Event = &orb_event.OrbEvent_InvoiceEvent{
				InvoiceEvent: invoiceEvent,
			}
		}
		return orbEvent

	// Subscription events
	case "subscription.created", "subscription.started", "subscription.fixed_fee_quantity_updated", "subscription.edited",
		"subscription.ended", "subscription.plan_changed", "subscription.plan_version_change_scheduled", "subscription.plan_version_changed":
		if subscriptionData, ok := rawEvent["subscription"].(map[string]interface{}); ok {
			if customerData, ok := subscriptionData["customer"].(map[string]interface{}); ok {
				orbEvent = h.parseCustomerData(customerData, orbEvent)
			}
			subscriptionEvent := &orb_event.SubscriptionEvent{}

			// Extract subscription ID
			if subscriptionID, ok := subscriptionData["id"].(string); ok {
				subscriptionEvent.SubscriptionId = subscriptionID
			}

			orbEvent.Event = &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: subscriptionEvent,
			}
		}
		return orbEvent
	// TODO: add logic for additional event types as we decide to support them
	default:
		log.Info().Str("event_type", eventType).Msg("No additional Orb event data to parse")
		return orbEvent
	}
}

// Parse out customer ID, Stripe customer ID, and Augment user ID from the customer data
func (h *OrbWebhookHandler) parseCustomerData(customerData map[string]interface{}, orbEvent *orb_event.OrbEvent) *orb_event.OrbEvent {
	if customerID, ok := customerData["id"].(string); ok {
		orbEvent.OrbCustomerId = &customerID
	}
	if stripeCustomerID, ok := customerData["payment_provider_id"].(string); ok {
		orbEvent.StripeCustomerId = &stripeCustomerID
	}

	// Extract augment_user_id from customer metadata if available
	if metadata, ok := customerData["metadata"].(map[string]interface{}); ok {
		if augmentUserID, ok := metadata["augment_user_id"].(string); ok && augmentUserID != "" {
			orbEvent.AugmentUserId = augmentUserID
			log.Info().Str("augment_user_id", augmentUserID).Msg("Extracted augment_user_id from customer metadata")
		}
	}

	return orbEvent
}

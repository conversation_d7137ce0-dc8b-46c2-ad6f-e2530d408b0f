load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

go_library(
    name = "billing_webhook_lib",
    srcs = [
        "main.go",
        "orb_handler.go",
    ],
    importpath = "github.com/augmentcode/augment/services/auth/billing_webhook",
    visibility = ["//visibility:private"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/auth/billing_webhook/orb_event:orb_event_go_proto",
        "//services/integrations/orb:orb_lib",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "billing_webhook",
    embed = [":billing_webhook_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":billing_webhook",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

go_test(
    name = "orb_handler_test_go",
    srcs = ["orb_handler_test.go"],
    embed = [":billing_webhook_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/pubsub:pubsub_emulator_go",
        "//services/auth/billing_webhook/orb_event:orb_event_go_proto",
        "//services/integrations/orb:orb_lib",
        "//services/lib/request_context:request_context_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//proto",
    ],
)

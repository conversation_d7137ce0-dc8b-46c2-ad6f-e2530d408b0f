package main

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/go/secretstring"
	pubsubemulator "github.com/augmentcode/augment/base/test_utils/pubsub"
	orb_event "github.com/augmentcode/augment/services/auth/billing_webhook/orb_event"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

func TestOrbWebhookHandler_HandleWebhook(t *testing.T) {
	// Start the PubSub emulator
	endpoint, cleanup, err := pubsubemulator.StartEmulator(0) // Use a random port
	require.NoError(t, err, "Failed to start PubSub emulator")
	defer cleanup() // Ensure emulator is cleaned up after test

	// Set the PUBSUB_EMULATOR_HOST environment variable for the client
	os.Setenv("PUBSUB_EMULATOR_HOST", endpoint)

	// Create a real PubSub client using the emulator
	ctx := context.Background()
	client, err := pubsub.NewClient(ctx, "test-project")
	require.NoError(t, err, "Failed to create PubSub client")
	defer client.Close()

	// Create a topic for testing
	topicID := "orb-events-test"
	topic, err := client.CreateTopic(ctx, topicID)
	require.NoError(t, err, "Failed to create PubSub topic")
	defer topic.Delete(ctx) // Clean up the topic after test

	// Create a webhook secret
	webhookSecret := secretstring.New("whsec_test_secret")

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create the handler with the real PubSub topic but replace the Orb client with our mock
	handler := NewOrbWebhookHandler(webhookSecret, topic)
	handler.orbClient = mockOrbClient

	// Create a subscription to verify messages are published
	subID := "orb-events-test-sub"
	sub, err := client.CreateSubscription(ctx, subID, pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	require.NoError(t, err, "Failed to create subscription")
	defer sub.Delete(ctx)

	// Test cases
	testCases := []struct {
		name            string
		eventType       string
		rawEventJSON    string
		expectedMessage *struct {
			Event *orb_event.OrbEvent
		}
	}{
		{
			name:      "Credit Balance Depleted Event",
			eventType: "customer.credit_balance_depleted",
			rawEventJSON: `{
				"id": "evt_credit_depleted",
				"type": "customer.credit_balance_depleted",
				"created_at": "2023-10-31T12:34:56Z",
				"customer": {
					"id": "cust_orb123",
					"payment_provider_id": "cus_stripe123",
					"metadata": {
						"augment_user_id": "augment_user_id"
					}
				},
				"properties": {
					"pricing_unit": {
						"name": "usermessages"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "customer.credit_balance_depleted",
					OrbCustomerId:    proto.String("cust_orb123"),
					StripeCustomerId: proto.String("cus_stripe123"),
					AugmentUserId:    "augment_user_id",
					Event: &orb_event.OrbEvent_CustomerBalanceEvent{
						CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
							PricingUnit: "usermessages",
						},
					},
				},
			},
		},
		{
			name:      "Credit Balance Recovered Event",
			eventType: "customer.credit_balance_recovered",
			rawEventJSON: `{
				"id": "evt_credit_recovered",
				"type": "customer.credit_balance_recovered",
				"created_at": "2023-10-31T12:34:56Z",
				"customer": {
					"id": "cust_orb456",
					"payment_provider_id": "cus_stripe456",
					"metadata": {
						"augment_user_id": "augment_user_id"
					}
				},
				"properties": {
					"pricing_unit": {
						"name": "usermessages"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "customer.credit_balance_recovered",
					OrbCustomerId:    proto.String("cust_orb456"),
					StripeCustomerId: proto.String("cus_stripe456"),
					AugmentUserId:    "augment_user_id",
					Event: &orb_event.OrbEvent_CustomerBalanceEvent{
						CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
							PricingUnit: "usermessages",
						},
					},
				},
			},
		},
		{
			name:      "Subscription Created Event",
			eventType: "subscription.created",
			rawEventJSON: `{
				"id": "evt_sub_created",
				"type": "subscription.created",
				"created_at": "2023-10-31T12:34:56Z",
				"subscription": {
					"id": "sub_orb123456",
					"customer": {
						"id": "cust_orb789",
						"payment_provider_id": "cus_stripe789",
						"metadata": {
							"augment_user_id": "augment_user_id"
						}
					},
					"plan": {
						"external_plan_id": "orb_trial_plan",
						"status": "active"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "subscription.created",
					OrbCustomerId:    proto.String("cust_orb789"),
					StripeCustomerId: proto.String("cus_stripe789"),
					AugmentUserId:    "augment_user_id",
					Event: &orb_event.OrbEvent_SubscriptionEvent{
						SubscriptionEvent: &orb_event.SubscriptionEvent{
							SubscriptionId: "sub_orb123456",
						},
					},
				},
			},
		},
		{
			name:      "Invoice Payment Failed Event",
			eventType: "invoice.payment_failed",
			rawEventJSON: `{
				"id": "evt_invoice_payment_failed",
				"type": "invoice.payment_failed",
				"created_at": "2023-10-31T12:34:56Z",
				"invoice": {
					"id": "inv_orb123",
					"subscription_id": "sub_orb456",
					"auto_collection": {
						"num_attempts": 2
					},
					"customer": {
						"id": "cust_orb789",
						"payment_provider_id": "cus_stripe789",
						"metadata": {
							"augment_user_id": "augment_user_id"
						}
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "invoice.payment_failed",
					OrbCustomerId:    proto.String("cust_orb789"),
					StripeCustomerId: proto.String("cus_stripe789"),
					AugmentUserId:    "augment_user_id",
					Event: &orb_event.OrbEvent_InvoiceEvent{
						InvoiceEvent: &orb_event.InvoiceEvent{
							InvoiceId: "inv_orb123",
						},
					},
				},
			},
		},
		{
			name:      "Credit Balance Depleted Event w/o augment_user_id",
			eventType: "customer.credit_balance_depleted",
			rawEventJSON: `{
				"id": "evt_credit_depleted",
				"type": "customer.credit_balance_depleted",
				"created_at": "2023-10-31T12:34:56Z",
				"customer": {
					"id": "cust_orb123",
					"payment_provider_id": "cus_stripe123"
				},
				"properties": {
					"pricing_unit": {
						"name": "usermessages"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "customer.credit_balance_depleted",
					OrbCustomerId:    proto.String("cust_orb123"),
					StripeCustomerId: proto.String("cus_stripe123"),
					Event: &orb_event.OrbEvent_CustomerBalanceEvent{
						CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
							PricingUnit: "usermessages",
						},
					},
				},
			},
		},
		{
			name:      "Credit Balance Recovered Event w/o augment_user_id",
			eventType: "customer.credit_balance_recovered",
			rawEventJSON: `{
				"id": "evt_credit_recovered",
				"type": "customer.credit_balance_recovered",
				"created_at": "2023-10-31T12:34:56Z",
				"customer": {
					"id": "cust_orb456",
					"payment_provider_id": "cus_stripe456"
				},
				"properties": {
					"pricing_unit": {
						"name": "usermessages"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "customer.credit_balance_recovered",
					OrbCustomerId:    proto.String("cust_orb456"),
					StripeCustomerId: proto.String("cus_stripe456"),
					Event: &orb_event.OrbEvent_CustomerBalanceEvent{
						CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
							PricingUnit: "usermessages",
						},
					},
				},
			},
		},
		{
			name:      "Subscription Created Event w/o augment_user_id",
			eventType: "subscription.created",
			rawEventJSON: `{
				"id": "evt_sub_created",
				"type": "subscription.created",
				"created_at": "2023-10-31T12:34:56Z",
				"subscription": {
					"id": "sub_orb123456",
					"customer": {
						"id": "cust_orb789",
						"payment_provider_id": "cus_stripe789"
					},
					"plan": {
						"external_plan_id": "orb_trial_plan",
						"status": "active"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "subscription.created",
					OrbCustomerId:    proto.String("cust_orb789"),
					StripeCustomerId: proto.String("cus_stripe789"),
					Event: &orb_event.OrbEvent_SubscriptionEvent{
						SubscriptionEvent: &orb_event.SubscriptionEvent{
							SubscriptionId: "sub_orb123456",
						},
					},
				},
			},
		},
		{
			name:      "Invoice Payment Failed Event w/o augment_user_id",
			eventType: "invoice.payment_failed",
			rawEventJSON: `{
				"id": "evt_invoice_payment_failed",
				"type": "invoice.payment_failed",
				"created_at": "2023-10-31T12:34:56Z",
				"invoice": {
					"id": "inv_orb123",
					"subscription_id": "sub_orb456",
					"auto_collection": {
						"num_attempts": 2
					},
					"customer": {
						"id": "cust_orb789",
						"payment_provider_id": "cus_stripe789"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "invoice.payment_failed",
					OrbCustomerId:    proto.String("cust_orb789"),
					StripeCustomerId: proto.String("cus_stripe789"),
					Event: &orb_event.OrbEvent_InvoiceEvent{
						InvoiceEvent: &orb_event.InvoiceEvent{
							InvoiceId: "inv_orb123",
						},
					},
				},
			},
		},
		{
			name:      "Credit Balance Depleted Event with empty metadata",
			eventType: "customer.credit_balance_depleted",
			rawEventJSON: `{
				"id": "evt_credit_depleted",
				"type": "customer.credit_balance_depleted",
				"created_at": "2023-10-31T12:34:56Z",
				"customer": {
					"id": "cust_orb123",
					"payment_provider_id": "cus_stripe123",
					"metadata": {}
				},
				"properties": {
					"pricing_unit": {
						"name": "usermessages"
					}
				}
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType:        "customer.credit_balance_depleted",
					OrbCustomerId:    proto.String("cust_orb123"),
					StripeCustomerId: proto.String("cus_stripe123"),
					Event: &orb_event.OrbEvent_CustomerBalanceEvent{
						CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
							PricingUnit: "usermessages",
						},
					},
				},
			},
		},
		{
			name:      "Unhandled Event Type",
			eventType: "invoice.paid",
			rawEventJSON: `{
				"id": "evt_invoice_paid",
				"type": "invoice.paid",
				"created_at": "2023-10-31T12:34:56Z"
			}`,
			expectedMessage: &struct {
				Event *orb_event.OrbEvent
			}{
				Event: &orb_event.OrbEvent{
					EventType: "invoice.paid",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			payload := []byte(tc.rawEventJSON)

			// Setup the mock Orb client to return the payload when VerifyWebhookSignature is called
			mockOrbClient.On("VerifyWebhookSignature", mock.Anything).Return(payload, nil)

			// Create the request with the correct headers
			req := httptest.NewRequest(http.MethodPost, "/webhook", bytes.NewReader(payload))
			req.Header.Set("X-Orb-Timestamp", "2023-10-31T12:34:56.789")

			// Create the response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.HandleWebhook(rr, req)

			// Assert the response is always OK
			assert.Equal(t, http.StatusOK, rr.Code, "Expected HTTP status code to be OK")

			// If we expect a message to be published, verify it
			if tc.expectedMessage != nil {
				// Create a context with a timeout for receiving the message
				receiveCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
				defer cancel()

				// Create a channel to receive the message
				msgChan := make(chan *pubsub.Message, 1)
				errChan := make(chan error, 1)

				// Pull a message from the subscription
				go func() {
					sub.Receive(receiveCtx, func(ctx context.Context, msg *pubsub.Message) {
						msgChan <- msg
						msg.Ack()
					})
					errChan <- nil
				}()

				// Wait for a message or timeout
				select {
				case msg := <-msgChan:
					// Verify the message has data
					assert.NotEmpty(t, msg.Data, "Message data should not be empty")

					// Unmarshal the protobuf message
					var actualEvent orb_event.OrbEvent
					err := proto.Unmarshal(msg.Data, &actualEvent)
					require.NoError(t, err, "Failed to unmarshal protobuf message")

					// Verify the event against the expected event
					expectedEvent := tc.expectedMessage.Event

					// Verify the event type
					assert.Equal(t, expectedEvent.EventType, actualEvent.EventType, "Event type should match")

					// Verify the customer IDs if expected
					if expectedEvent.OrbCustomerId != nil {
						require.NotNil(t, actualEvent.OrbCustomerId, "Orb customer ID should not be nil")
						assert.Equal(t, *expectedEvent.OrbCustomerId, *actualEvent.OrbCustomerId, "Orb customer ID should match")
					}

					if expectedEvent.StripeCustomerId != nil {
						require.NotNil(t, actualEvent.StripeCustomerId, "Stripe customer ID should not be nil")
						assert.Equal(t, *expectedEvent.StripeCustomerId, *actualEvent.StripeCustomerId, "Stripe customer ID should match")
					}

					// Verify the augment user ID if expected
					if expectedEvent.AugmentUserId != "" {
						assert.Equal(t, expectedEvent.AugmentUserId, actualEvent.AugmentUserId, "Augment user ID should match")
					}

					// Verify the event payload based on the oneof field
					switch expectedEvent.Event.(type) {
					case *orb_event.OrbEvent_CustomerBalanceEvent:
						// Verify customer balance event
						expectedBalance := expectedEvent.GetCustomerBalanceEvent()
						actualBalance := actualEvent.GetCustomerBalanceEvent()
						require.NotNil(t, actualBalance, "Customer balance event should not be nil")
						assert.Equal(t, expectedBalance.PricingUnit, actualBalance.PricingUnit, "Pricing unit should match")

					case *orb_event.OrbEvent_SubscriptionEvent:
						// Verify subscription event
						expectedSub := expectedEvent.GetSubscriptionEvent()
						actualSub := actualEvent.GetSubscriptionEvent()
						require.NotNil(t, actualSub, "Subscription event should not be nil")
						assert.Equal(t, expectedSub.SubscriptionId, actualSub.SubscriptionId, "Subscription ID should match")

					case *orb_event.OrbEvent_InvoiceEvent:
						// Verify invoice event
						expectedInvoice := expectedEvent.GetInvoiceEvent()
						actualInvoice := actualEvent.GetInvoiceEvent()
						require.NotNil(t, actualInvoice, "Invoice event should not be nil")
						assert.Equal(t, expectedInvoice.InvoiceId, actualInvoice.InvoiceId, "Invoice ID should match")
					}

					// Verify metadata fields
					assert.NotEmpty(t, actualEvent.Metadata.RequestId, "Request ID should not be empty")
					assert.NotNil(t, actualEvent.Metadata.ProcessedAt, "Processed at timestamp should not be nil")
					assert.NotNil(t, actualEvent.Metadata.OrbTimestamp, "Orb timestamp should not be nil")

					// Verify event ID exists but don't check the exact value
					// as it comes from the raw event JSON
					assert.NotEmpty(t, actualEvent.Metadata.EventId, "Event ID should not be empty")

				case err := <-errChan:
					require.NoError(t, err, "Failed to receive message from subscription")
				case <-receiveCtx.Done():
					// Only fail if we expected a message
					t.Fatal("Timed out waiting for message")
				}
			}

			// Reset the mock for the next test
			mockOrbClient.ExpectedCalls = nil
		})
	}
}

func TestOrbWebhookHandler_SignatureVerificationFailure(t *testing.T) {
	// Create a topic for testing
	topic := &pubsub.Topic{}

	// Create a webhook secret
	webhookSecret := secretstring.New("whsec_test_secret")

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create the handler with the mock Orb client
	handler := NewOrbWebhookHandler(webhookSecret, topic)
	handler.orbClient = mockOrbClient

	// Setup the mock Orb client to return an error when VerifyWebhookSignature is called
	mockOrbClient.On("VerifyWebhookSignature", mock.Anything).Return([]byte{}, fmt.Errorf("invalid signature"))

	// Create the request
	req := httptest.NewRequest(http.MethodPost, "/webhook", bytes.NewReader([]byte("{}")))

	// Create the response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	handler.HandleWebhook(rr, req)

	// Assert the response is BadRequest
	assert.Equal(t, http.StatusBadRequest, rr.Code, "Expected HTTP status code to be BadRequest")
}

func TestOrbWebhookHandler_InvalidJSON(t *testing.T) {
	// Create a topic for testing
	topic := &pubsub.Topic{}

	// Create a webhook secret
	webhookSecret := secretstring.New("whsec_test_secret")

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create the handler with the mock Orb client
	handler := NewOrbWebhookHandler(webhookSecret, topic)
	handler.orbClient = mockOrbClient

	// Setup the mock Orb client to return invalid JSON when VerifyWebhookSignature is called
	mockOrbClient.On("VerifyWebhookSignature", mock.Anything).Return([]byte("{invalid json}"), nil)

	// Create the request
	req := httptest.NewRequest(http.MethodPost, "/webhook", bytes.NewReader([]byte("{invalid json}")))

	// Create the response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	handler.HandleWebhook(rr, req)

	// Assert the response is BadRequest
	assert.Equal(t, http.StatusBadRequest, rr.Code, "Expected HTTP status code to be BadRequest")
}

func TestOrbWebhookHandler_MissingEventType(t *testing.T) {
	// Create a topic for testing
	topic := &pubsub.Topic{}

	// Create a webhook secret
	webhookSecret := secretstring.New("whsec_test_secret")

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create the handler with the mock Orb client
	handler := NewOrbWebhookHandler(webhookSecret, topic)
	handler.orbClient = mockOrbClient

	// Setup the mock Orb client to return JSON without an event type when VerifyWebhookSignature is called
	mockOrbClient.On("VerifyWebhookSignature", mock.Anything).Return([]byte(`{"id": "evt_123"}`), nil)

	// Create the request
	req := httptest.NewRequest(http.MethodPost, "/webhook", bytes.NewReader([]byte(`{"id": "evt_123"}`)))

	// Create the response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	handler.HandleWebhook(rr, req)

	// Assert the response is BadRequest
	assert.Equal(t, http.StatusBadRequest, rr.Code, "Expected HTTP status code to be BadRequest")
}

func TestOrbWebhookHandler_PubSubError(t *testing.T) {
	// Start the PubSub emulator
	endpoint, cleanup, err := pubsubemulator.StartEmulator(0) // Use a random port
	require.NoError(t, err, "Failed to start PubSub emulator")
	defer cleanup() // Ensure emulator is cleaned up after test

	// Set the PUBSUB_EMULATOR_HOST environment variable for the client
	os.Setenv("PUBSUB_EMULATOR_HOST", endpoint)

	// Create a real PubSub client using the emulator
	ctx := context.Background()
	client, err := pubsub.NewClient(ctx, "test-project")
	require.NoError(t, err, "Failed to create PubSub client")
	defer client.Close()

	// Create a topic for testing
	topicID := "orb-events-test-error"
	topic, err := client.CreateTopic(ctx, topicID)
	require.NoError(t, err, "Failed to create PubSub topic")
	defer topic.Delete(ctx) // Clean up the topic after test

	// Create a webhook secret
	webhookSecret := secretstring.New("whsec_test_secret")

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create the handler with the real PubSub topic but replace the Orb client with our mock
	handler := NewOrbWebhookHandler(webhookSecret, topic)
	handler.orbClient = mockOrbClient

	// Setup the mock Orb client to return valid JSON when VerifyWebhookSignature is called
	validJSON := []byte(`{
		"id": "evt_123",
		"type": "customer.credit_balance_depleted",
		"customer": {"id": "cust_123"},
		"properties": {
			"pricing_unit": {
				"name": "usermessages"
			}
		}
	}`)
	mockOrbClient.On("VerifyWebhookSignature", mock.Anything).Return(validJSON, nil)

	// We'll use a real topic but force a publish error by closing the client
	client.Close() // This will cause Publish to fail

	// Create the request
	req := httptest.NewRequest(http.MethodPost, "/webhook", bytes.NewReader(validJSON))

	// Create the response recorder
	rr := httptest.NewRecorder()

	// Call the handler
	handler.HandleWebhook(rr, req)

	// Assert the response is InternalServerError
	assert.Equal(t, http.StatusInternalServerError, rr.Code, "Expected HTTP status code to be InternalServerError")
}

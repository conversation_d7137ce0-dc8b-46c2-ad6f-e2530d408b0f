syntax = "proto3";

package orb_event;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/augmentcode/augment/services/auth/billing_webhook/orb_event";

// OrbEvent represents an event received from <PERSON><PERSON>'s webhook
message OrbEvent {
  // Metadata about the event
  EventMetadata metadata = 1;

  // The type of event (e.g., "customer.created", "subscription.created", etc.)
  string event_type = 2;

  // The raw JSON payload of the event
  string raw_payload = 3;

  // The Orb customer ID if available
  optional string orb_customer_id = 4;

  // The Stripe customer ID if available
  optional string stripe_customer_id = 5;

  // Event-specific data
  oneof event {
    CustomerBalanceEvent customer_balance_event = 6;
    SubscriptionEvent subscription_event = 7;
    InvoiceEvent invoice_event = 8;
    //TODO: add future types of events if we want to send others
  }

  // The Augment user ID if available
  string augment_user_id = 9;
}

// EventMetadata contains metadata about the event
message EventMetadata {
  // The ID of the event in Orb
  string event_id = 1;

  // The timestamp when the event was processed by our service
  google.protobuf.Timestamp processed_at = 2;

  // The request ID assigned by our service
  string request_id = 3;

  // The timestamp provided by <PERSON>b in the X-Orb-Timestamp header
  // This represents when Orb generated the webhook event, which may differ from created_at
  google.protobuf.Timestamp orb_timestamp = 4;
}

// CustomerBalanceEvent represents a customer balance event
message CustomerBalanceEvent {
  // The pricing unit of the balance event (ex: "credits")
  string pricing_unit = 1;
}

message SubscriptionEvent {
  // The Orb subscription ID
  string subscription_id = 1;
}

// InvoiceEvent represents an invoice payment failed event
message InvoiceEvent {
  // The Orb invoice ID
  string invoice_id = 1;
}

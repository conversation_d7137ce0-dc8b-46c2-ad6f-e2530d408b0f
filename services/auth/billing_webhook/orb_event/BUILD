load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")

proto_library(
    name = "orb_event_proto",
    srcs = ["orb_event.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

go_proto_library(
    name = "orb_event_go_proto",
    importpath = "github.com/augmentcode/augment/services/auth/billing_webhook/orb_event",
    proto = ":orb_event_proto",
    visibility = ["//visibility:public"],
    deps = [],
)

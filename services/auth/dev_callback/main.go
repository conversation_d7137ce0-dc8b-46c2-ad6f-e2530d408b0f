package main

import (
	"context"
	tls "crypto/tls"
	"encoding/base64"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"regexp"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/augmentcode/augment/base/logging"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
)

var configFile = flag.String("config", "", "Path to config file")

var durationHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_http_service_duration",
		Help:    "Duration of HTTP requests served",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"code", "method", "path"},
)

type TlsConfig struct {
	KeyPath  string `json:"key_path"`
	CertPath string `json:"cert_path"`
	CaPath   string `json:"ca_path"`
}

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerTlsConfig *TlsConfig `json:"server_mtls"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`
}

func writeError(w http.ResponseWriter, error string, code int) {
	w.Header().Set("Content-Type", "text/plain")
	w.WriteHeader(code)
	fmt.Fprintln(w, error)
}

func handleCallback(w http.ResponseWriter, r *http.Request) {
	// Parse query args
	queryArgs := r.URL.Query()

	state := queryArgs.Get("state")

	// Split state on .
	stateParts := strings.SplitN(state, ".", 2)
	if len(stateParts) != 2 {
		writeError(w, "Invalid state", http.StatusBadRequest)
		return
	}

	// Strip padding if any
	encodedRedirectUrl := strings.TrimRight(stateParts[0], "=")

	redirectUrl, err := base64.RawURLEncoding.DecodeString(encodedRedirectUrl)
	if err != nil {
		writeError(w, "Invalid state", http.StatusBadRequest)
		return
	}

	validRedirectPattern := `^https://[.a-z0-9-]*dev\.augmentcode\.com/(oauth2|signup)/callback$`
	if matched, err := regexp.MatchString(validRedirectPattern, string(redirectUrl)); err != nil {
		writeError(w, "Error validating redirect URL", http.StatusInternalServerError)
		return
	} else if !matched {
		writeError(w, "Invalid redirect URL - must match https://*.dev.augmentcode.com/oauth2/callback pattern", http.StatusBadRequest)
		return
	}

	// Build a URL with redirectUrl and the query params
	w.Header().Set("Location", fmt.Sprintf("%s?%s", redirectUrl, r.URL.RawQuery))
	w.WriteHeader(http.StatusTemporaryRedirect)
}

func loadServerTlsConfig(serverTlsConfig *TlsConfig) (*tls.Config, error) {
	cert, err := tls.LoadX509KeyPair(serverTlsConfig.CertPath, serverTlsConfig.KeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load server certificate and key: %v", err)
	}

	// Create tls.Config
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12,
	}

	return tlsConfig, nil
}

func withMetrics(path string, next http.Handler) http.Handler {
	return promhttp.InstrumentHandlerDuration(
		durationHistogram.MustCurryWith(prometheus.Labels{
			"path": path,
		}),
		next,
	)
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	serverTls, err := loadServerTlsConfig(config.ServerTlsConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	go func() {
		http.Handle("GET /metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	prometheus.MustRegister(durationHistogram)

	ctx := context.Background()
	// Set up HTTPS server
	mux := http.NewServeMux()
	mux.Handle("GET /oauth2/callback",
		withMetrics("/oauth2/callback", http.HandlerFunc(handleCallback)))
	mux.Handle("GET /health",
		withMetrics("/health", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})))

	server := &http.Server{
		Addr:      fmt.Sprintf(":%d", config.Port),
		Handler:   mux,
		TLSConfig: serverTls,
	}

	wg := sync.WaitGroup{}

	wg.Add(1)
	go func() {
		defer wg.Done()

		log.Info().Msgf("Starting HTTPS server on port %d", config.Port)
		if err := server.ListenAndServeTLS("", ""); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting HTTPS server")
		}
	}()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan

	log.Info().Msgf("Received signal %s, initiating graceful shutdown", sig)

	// Initiate graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Error().Err(err).Msg("Error during server shutdown")
	}
	wg.Wait()
	log.Info().Msg("Server has shut down gracefully")
}

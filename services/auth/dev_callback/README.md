
OAuth servers like Auth0 and Google are strict about matching redirect URIs.

Each of our dev deploys though runs a distinct OAuth client with a distinct redirect URI.

Before this server, each user would have to ping the auth team and ask that the callback
redirect URI of their auth central be added to Auth0.

This server allows us to furnish one callback URI to Auth0 - the callback URI of this
server. This server will then redirect again back to the specific dev server.

This server knows which dev server to redirect back to based on a URL embedded in the "state".
The URL appears at the beginning of state (the part up to but not including the first dot '.'). The URL is base64-encoded in the state.

The server will only redirect if URL encoded in the state matches https://*.dev.augmentcode.com/oauth2/callback

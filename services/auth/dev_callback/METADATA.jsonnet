local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'auth-dev-callback',
      kubecfg: {
        target: '//services/auth/dev_callback:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

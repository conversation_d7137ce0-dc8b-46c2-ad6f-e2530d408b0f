.c-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  background-color: var(--augment-green);
  font-family: "Inter", var(--system-fonts);
  padding: var(--p-2) var(--p-4);
  gap: var(--gap-6);
  text-align: center;

  color: var(--text-white-80);
  font-size: var(--text-sm-size);
  line-height: var(--text-sm-lh);
}

.c-footer p {
  margin: var(--p-4) 0;
}

.c-footer a {
  font-size: inherit;
  color: var(--text-white-80);
  padding: var(--p-3);
}

.c-footer__links {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--gap-2);
}

@media (min-width: 540px) {
  .c-footer {
    flex-direction: row;
  }

  .c-footer__links {
    flex-direction: row;
  }
}

{"scripts": {"//": "These commands are not used by the build but makes debugging easier", "postcss-central": "postcss --config='.' --dir build/ '../../central/server/styles/login.css'", "postcss-tenant": "postcss --config='.' --dir build/ '../../tenant/server/styles/redirect.css'", "postcss-auth0": "postcss --config='.' --dir build/ '../../auth0/page_template/styles/auth0.css'"}, "type": "module", "dependencies": {"autoprefixer": "^10.4.16", "cssnano": "^6.0.2", "postcss": "^8.4.32", "postcss-cli": "^11.0.0", "postcss-import": "^15.1.0"}}
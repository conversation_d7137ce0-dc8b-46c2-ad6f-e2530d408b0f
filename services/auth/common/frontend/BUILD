load("@aspect_bazel_lib//lib:copy_to_bin.bzl", "copy_to_bin")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//services/auth/common/frontend:postcss-cli/package_json.bzl", postcss_bin = "bin")

npm_link_all_packages()

copy_to_bin(
    name = "common_css_files",
    srcs = glob([
        "styles/**/*.css",
    ]),
)

filegroup(
    name = "package_json",
    srcs = ["package.json"],
)

filegroup(
    name = "postcss_config",
    srcs = ["postcss.config.js"],
)

filegroup(
    name = "static_files",
    srcs = glob([
        "static/**/*",
    ]),
    visibility = [
        "//services/auth/central/server:__subpackages__",
    ],
)

filegroup(
    name = "templates_files",
    srcs = glob([
        "templates/**/*",
    ]),
    visibility = [
        "//services/auth/auth0/page_template:__subpackages__",
        "//services/auth/central/server:__subpackages__",
    ],
)

postcss_bin.postcss(
    name = "postcss",
    srcs = [
        ":common_css_files",
        ":node_modules/autoprefixer",
        ":node_modules/cssnano",
        ":node_modules/postcss",
        ":node_modules/postcss-import",
        ":package_json",
        ":postcss_config",
        "//services/auth/auth0/page_template:css_files",
        "//services/auth/central/server:css_files",
    ],
    outs = [
        "build/auth0.css",
        "build/client_redirect.css",
        "build/invitations.css",
        "build/login.css",
        "build/unauthenticated.css",
    ],
    args = [
        "--config",
        package_name(),
        "--dir",
        "{}/{}".format(
            package_name(),
            "build",
        ),
    ] + [
        "{}/{}".format(
            package_name(),
            "../../central/server/styles/**/*.css",
        ),
    ] + [
        "{}/{}".format(
            package_name(),
            "../../auth0/page_template/styles/**/*.css",
        ),
    ],
    visibility = [
        "//services/auth/auth0/page_template:__subpackages__",
        "//services/auth/central/server:__subpackages__",
    ],
)

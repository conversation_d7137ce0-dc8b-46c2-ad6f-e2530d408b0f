syntax = "proto3";
package auth_query;

import "google/protobuf/timestamp.proto";
import "services/auth/central/server/auth_entities.proto";

// AuthQuery service is responsible for providing read access to authentication
// and authorization related objects such as tokens, users, etc.
service AuthQuery {
  rpc GetTokenInfo(GetTokenInfoRequest) returns (GetTokenInfoResponse);
}

message GetTokenInfoRequest {
  // The token to return information
  string token = 1 [debug_redact = true];

  // A string identifying who is requesting the information.
  // Number of requestors should be low.
  string requestor = 2;
}

message GetTokenInfoResponse {
  // The user ID associated with the token.
  // NB: This is the deprecated notion of a "user id", which is either an email or an API token id.
  // We are migrating to using `opaque_user_id` and `user_email` instead.
  string user_id = 1;

  // The signed_token for this request, that can be used to access other
  // internal resources. Currently a JWT token with an AugmentClaims payload.
  // NOTE: Until we are fully migrated to using tokens, this may be empty.
  // The returned token should be treated as a secret, and never logged
  // anywhere.
  string signed_token = 2 [debug_redact = true];

  // The tenant ID associated with the token.
  string tenant_id = 3;

  // The tenant name associated with the token.
  //
  // In single tenant namespaces, the tenant name will be the same as the
  // namespace.
  string tenant_name = 4;

  // A UUID from the auth database, or the id of the caller's API token.
  // TODO(jacqueline): Eventually I'd like to rename this just "user_id", but for the sake of the
  // migration it's easier to leave the old user_id field alone.
  auth_entities.UserId opaque_user_id = 5;

  // The email of the user. This is empty if the user is using an API token.
  optional string user_email = 7;

  // Subscription information
  // Keep all of these in sync with subscription in services/auth/central/server/auth.proto
  oneof subscription {
    EnterpriseSubscription enterprise = 10;
    ActiveSubscription active_subscription = 11;
    Trial trial = 12;
    InactiveSubscription inactive_subscription = 13;
  }

  // Current active suspensions
  repeated auth_entities.UserSuspension suspensions = 14;
}

// Enterprise subscription type
message EnterpriseSubscription {
  // Add any enterprise-specific fields here
}

// Active subscription type
message ActiveSubscription {
  // The end date of the subscription - could be a trial end date when using Orb or a paid subscription end date
  google.protobuf.Timestamp end_date = 1;

  // Whether the user is out of credits
  bool usage_balance_depleted = 2;

  // The user's billing method
  auth_entities.BillingMethod billing_method = 3;
}

// Trial subscription type
message Trial {
  // The trial end date
  google.protobuf.Timestamp trial_end = 1;

  // The user's billing method
  auth_entities.BillingMethod billing_method = 2;
}

// Inactive subscription type
message InactiveSubscription {
  // Add any inactive subscription-specific fields here

  // The user's billing method
  auth_entities.BillingMethod billing_method = 1;
}

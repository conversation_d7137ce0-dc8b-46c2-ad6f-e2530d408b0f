"""Auth query server test setup for e2e tests."""

import json
import os
import tempfile
from dataclasses import asdict, dataclass, field
from typing import Any, Optional

import grpc

from base.test_utils import process
from services.auth.query import auth_query_pb2_grpc


@dataclass
class AuthQueryBigtableConfig:
    """Bigtable configuration."""

    project_id: Optional[str] = None
    instance_name: Optional[str] = None
    table_name: Optional[str] = None


@dataclass
class AuthConfig:
    """Configuration for authenticating service tokens."""

    endpoint: str
    request_timeout_secs: Optional[float] = 10.0


@dataclass
class AuthQueryConfig:
    """Configuration for the Auth-Query service."""

    bind_address: Optional[str] = "127.0.0.1:0"
    namespace: Optional[str] = "test"
    metrics_server_bind_address: Optional[str] = "127.0.0.1"
    metrics_server_port: Optional[int] = 0
    central_bigtable_backend: Optional[AuthQueryBigtableConfig] = None
    server_mtls: Optional[bool] = False
    server_ca_path: Optional[str] = ""
    server_key_path: Optional[str] = ""
    server_cert_path: Optional[str] = ""
    client_mtls: Optional[bool] = False
    client_ca_path: Optional[str] = ""
    client_key_path: Optional[str] = ""
    client_cert_path: Optional[str] = ""
    tenant_watcher_endpoint: str = "null"
    auth_central_endpoint: str = ""
    token_info_cache_ttl_secs: Optional[float] = 15 * 60
    token: Optional[AuthConfig] = None
    use_auth_central_for_token: bool = True
    migrated_tenant_ids: list[str] = field(default_factory=list)

    @classmethod
    def create(
        cls,
        token_exchange_endpoint: str,
        namespace: str = "test",
        **kwargs: Any,
    ):
        return cls(
            token=AuthConfig(token_exchange_endpoint),
            namespace=namespace,
            **kwargs,
        )


def start_auth_query_server(config: AuthQueryConfig):
    """Fixture to start the auth query server."""
    config_content = json.dumps(asdict(config))
    secrets_content = json.dumps({"api_tokens": []})

    with tempfile.NamedTemporaryFile(mode="w+t") as config_file:
        config_file.write(config_content)
        config_file.flush()
        with tempfile.NamedTemporaryFile(mode="w+t") as secrets_file:
            secrets_file.write(secrets_content)
            secrets_file.flush()
            env = os.environ.copy()
            env["POD_NAMESPACE"] = "augment"  # match configured tenant
            with process.ServerManager(
                [
                    "services/auth/query/server/auth_query_server",
                    "--config-file",
                    config_file.name,
                    "--secrets-file",
                    secrets_file.name,
                ],
                env=env,
            ) as p:
                m = process.wait_for_line(
                    p.stdout,
                    r"Listening on 127.0.0.1:(\d+)",
                    timeout_secs=30,
                )
                p.detach_stdout()
                port = int(m.group(1))
                channel = grpc.insecure_channel("127.0.0.1:%d" % port)
                stub = auth_query_pb2_grpc.AuthQueryStub(channel)
                yield stub

load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_binary", "rust_oci_image", "rust_test")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

rust_binary(
    name = "auth_query_server",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/feature_flags:feature_flags_rs",
        "//base/logging:struct_logging_rs",
        "//base/logging/audit:audit_rs",
        "//base/metrics_server/rust:metrics_server",
        "//base/rust/tracing-tonic",
        "//services/auth/central/server:auth_entities_rs_proto",
        "//services/lib/grpc/auth:grpc_auth",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/grpc/metrics:grpc_metrics",
        "//services/lib/request_context:request_context_rs",
        "//services/tenant_watcher/client:client_rs",
        "//services/token_exchange/client:client_rs",
        "//third_party/bigtable_rs",
    ],
)

rust_oci_image(
    name = "image",
    package_name = package_name(),
    binary = "auth_query_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services:__subpackages__"],
)

rust_test(
    name = "auth_query_server_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":auth_query_server",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/auth/central/server:auth_proto",
        "//services/auth/query:auth_query_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/auth/query:__subpackages__",
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//deploy/tenants:health-check-token",
        "//deploy/tenants:tenants-lib",
        "//services/deploy:endpoints",
    ],
)

py_library(
    name = "auth_query_test_setup",
    testonly = True,
    srcs = [
        "auth_query_test_setup.py",
    ],
    data = [
        ":auth_query_server",
    ],
    pyright_extra_args = {
        "reportMissingParameterType": True,
    },
    visibility = ["//services/auth:__subpackages__"],
    deps = [
        "//base/test_utils:process",
        "//services/auth/query:auth_query_py_proto",
    ],
)

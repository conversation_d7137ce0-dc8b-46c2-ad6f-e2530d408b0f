use secrecy::{ExposeSecret, SecretString};
use serde::Deserialize;
use std::sync::Arc;
use tenant_watcher_client::TenantCacheClient;

use crate::metrics::TENANT_NOT_FOUND_COUNTER;
use crate::proto::auth_query;
use auth_entities_proto::auth_entities::{user_id::UserIdType, UserId};

// format of the api token JSON secret
#[derive(Deserialize, Clone)]
pub struct ApiTokenDbEntry {
    pub token_sha256: SecretString,
    pub user_id: String,
    pub tenant_name: String,
}

/// structure representing the database of valid tokens
pub struct ApiTokenDb {
    tokens: async_lock::RwLock<Vec<ApiTokenDbEntry>>,
    path: std::path::PathBuf,
    tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
}

pub fn to_token_sha256(token: &SecretString) -> SecretString {
    SecretString::new(sha256::digest(token.expose_secret()).to_string())
}

#[derive(<PERSON><PERSON>, PartialEq)]
pub struct UserEntry {
    pub user_id: String,
    pub opaque_user_id: UserId,
    pub user_email: Option<String>,
    pub tenant_id: String,
    pub subscription: Option<auth_query::get_token_info_response::Subscription>,
    pub suspensions: Vec<auth_entities_proto::auth_entities::UserSuspension>,
}

impl ApiTokenDb {
    /// constructs a new ApiTokenDb object with a set of valid tokens
    #[allow(dead_code)]
    #[cfg(test)]
    pub fn new(
        tokens: &[ApiTokenDbEntry],
        tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
    ) -> Self {
        Self {
            tokens: async_lock::RwLock::new(Vec::from_iter(tokens.iter().cloned())),
            path: std::path::PathBuf::new(),
            tenant_cache,
        }
    }

    pub async fn from_file(
        path: &std::path::Path,
        tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
    ) -> Result<Self, tonic::Status> {
        let ret = Self {
            tokens: async_lock::RwLock::new(Vec::new()),
            path: path.to_path_buf(),
            tenant_cache,
        };

        ret.update().await?;

        Ok(ret)
    }

    pub async fn get_user_id(&self, token: &SecretString) -> tonic::Result<UserEntry> {
        let token_sha256 = to_token_sha256(token);
        let tokens = self.tokens.read().await;
        let entry = tokens
            .iter()
            .find(|s| s.token_sha256.expose_secret() == token_sha256.expose_secret());
        match entry {
            None => Err(tonic::Status::not_found("Token not found")),
            Some(s) => {
                let tenant = self
                    .tenant_cache
                    .get_tenant_by_name(s.tenant_name.as_str())
                    .await;
                match tenant {
                    None => {
                        TENANT_NOT_FOUND_COUNTER.inc();
                        tracing::warn!("Tenant not found: {}", s.tenant_name);
                        Err(tonic::Status::internal("Tenant not found"))
                    }
                    Some(tenant) => {
                        // Disable API tokens for disabled tenants except for the
                        // health check (useful for disabled legacy namespaces)
                        if !tenant.deleted_at.is_empty() && !s.user_id.starts_with("health-check-")
                        {
                            tracing::warn!("Tenant is deleted: {}", s.tenant_name);
                            Err(tonic::Status::not_found("Tenant is deleted"))
                        } else {
                            Ok(UserEntry {
                                user_id: s.user_id.clone(),
                                opaque_user_id: UserId {
                                    user_id_type: UserIdType::ApiToken.into(),
                                    user_id: s.user_id.clone(),
                                },
                                user_email: None,
                                tenant_id: tenant.id,
                                // For API tokens, we default to ACTIVE_SUBSCRIPTION
                                subscription: Some(auth_query::get_token_info_response::Subscription::ActiveSubscription(
                                    auth_query::ActiveSubscription {
                                        end_date: None,
                                        usage_balance_depleted: false,
                                        billing_method: auth_entities_proto::auth_entities::BillingMethod::Unknown as i32,
                                    },
                                )),
                                // For API tokens, we don't have any suspensions
                                suspensions: Vec::new(),
                            })
                        }
                    }
                }
            }
        }
    }

    async fn update(&self) -> Result<(), tonic::Status> {
        let file =
            std::fs::File::open(&self.path).map_err(|e| tonic::Status::internal(e.to_string()))?;
        let mut secrets = crate::config::Secrets::read(&file)?;

        let mut tokens = self.tokens.write().await;
        std::mem::swap(&mut secrets.api_tokens, tokens.as_mut());

        Ok(())
    }

    pub async fn run(&self) {
        loop {
            let res = self.update().await;

            if let Err(e) = res {
                tracing::error!("Failed to update token database: {:?}", e);
            }

            // TODO make thsi configurable
            tokio::time::sleep(std::time::Duration::from_secs(5)).await;
        }
    }
}

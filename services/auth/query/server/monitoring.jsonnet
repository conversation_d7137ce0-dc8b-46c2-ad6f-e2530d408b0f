local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local tenantNotFoundSpec = {
    displayName: 'Auth Query Tenant Not Found Errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace,cluster) (increase(au_auth_query_tenant_not_found_count[5m])) > 10
      |||,
    },
  };

  local getTokenInfoLatencyWarningSpec = {
    displayName: 'Auth Query GetTokenInfo High Latency Warning',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        histogram_quantile(0.95, sum by (le, namespace, cluster) (
          rate(au_rpc_latency_histogram_bucket{endpoint="GetTokenInfo"}[5m])
        )) > 0.5
      |||,
    },
  };

  local getTokenInfoLatencyErrorSpec = {
    displayName: 'Auth Query GetTokenInfo High Latency Error',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        histogram_quantile(0.95, sum by (le, namespace, cluster) (
          rate(au_rpc_latency_histogram_bucket{endpoint="GetTokenInfo"}[10m])
        )) > 1.0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      tenantNotFoundSpec,
      'auth-query-tenant-not-found',
      'High number of Tenant Not Found errors in Auth Query service'
    ),
    monitoringLib.alertPolicy(
      cloud,
      getTokenInfoLatencyWarningSpec,
      'auth-query-get-token-info-high-latency-warning',
      'GetTokenInfo endpoint p95 latency exceeds 0.5 seconds for 5+ minutes in namespace %s cluster %s' % [monitoringLib.label('namespace'), monitoringLib.label('cluster')],
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      getTokenInfoLatencyErrorSpec,
      'auth-query-get-token-info-high-latency-error',
      'GetTokenInfo endpoint p95 latency exceeds 1.0 second for 10+ minutes in namespace %s cluster %s' % [monitoringLib.label('namespace'), monitoringLib.label('cluster')],
      team='insights',
    ),
  ]

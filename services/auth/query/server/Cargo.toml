[package]
name = "auth_query"
version = "0.1.0"
edition = "2021"

[dependencies]
x509-parser = { workspace = true }
async-lock = { workspace = true }
async-recursion = { workspace = true }
async-rwlock = { workspace = true }
async-std = { workspace = true }
async-trait = { workspace = true }
bigtable_rs = { workspace = true }
bytes = { workspace = true }
chrono = { workspace = true }
clap = { workspace = true }
feature-flags = { path = "../../../../base/feature_flags" }
futures = { workspace = true }
futures-buffered = { workspace = true }
google-cloud-auth = { workspace = true }
google-cloud-googleapis = { workspace = true }
grpc_client = { path = "../../../lib/grpc/client" }
hex-literal = { workspace = true }
http = { workspace = true }
hyper = { workspace = true }
itertools = { workspace = true }
lazy_static = { workspace = true }
grpc_metrics = { path = "../../../lib/grpc/metrics" }
metrics-server = { path = "../../../../base/metrics_server/rust" }
moka = { workspace = true }
opentelemetry = { workspace = true }
opentelemetry-otlp = { workspace = true }
opentelemetry_sdk = { workspace = true }
pin-project = { workspace = true }
prometheus = { workspace = true }
prost = { workspace = true }
prost-types = { workspace = true }
prost-wkt = { workspace = true }
prost-wkt-types = { workspace = true }
request_context = { path = "../../../lib/request_context" }
reqwest = { workspace = true, default-features = false }
token_exchange_client = { path = "../../../token_exchange/client" }
tenant_watcher_client = { path = "../../../tenant_watcher/client" }
schemars = { workspace = true }
secrecy = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
sha2 = { workspace = true }
sha256 = { workspace = true }
struct_logging = { path = "../../../..//base/logging" }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tonic = { workspace = true, features = ["tls", "gzip", "tls-roots"] }
tonic-build = { workspace = true }
tonic-health = { workspace = true }
tonic-reflection = { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
tracing-log = { workspace = true }
# other packages don't and shouldn't use logfmt
tracing-logfmt = "*"
tracing-opentelemetry = { workspace = true }
tracing-subscriber = { workspace = true }
tracing-tonic = { path = "../../../../base/rust/tracing-tonic" }
uuid = { workspace = true }
auth_entities_proto = { path = "../../../auth/central/server" }

[dev-dependencies]
actix-rt = { workspace = true }
assert_unordered = { workspace = true }
tonic-build = { workspace = true }

[build-dependencies]
tonic-build = { workspace = true }

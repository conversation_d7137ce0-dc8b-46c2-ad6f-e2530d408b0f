use std::{collections::HashMap, sync::Arc, time::Duration};

use async_lock::Mutex;
use async_trait::async_trait;
use auth_entities_proto::auth_entities::{user_id::UserIdType, UserId};
use grpc_client::create_channel;
use tonic::transport::ClientTlsConfig;
use tracing_tonic::client::TracingService;

pub mod auth_query {
    tonic::include_proto!("auth_query");
}

#[async_trait]
pub trait AuthQueryClient {
    async fn get_token_info(
        &self,
        request: auth_query::GetTokenInfoRequest,
        request_context: &request_context::RequestContext,
    ) -> Result<auth_query::GetTokenInfoResponse, tonic::Status>;
}

#[derive(Clone)]
pub struct AuthQueryClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Duration,
    client: Arc<Mutex<Option<auth_query::auth_query_client::AuthQueryClient<TracingService>>>>,
}

#[async_trait]
impl AuthQueryClient for AuthQueryClientImpl {
    async fn get_token_info(
        &self,
        request: auth_query::GetTokenInfoRequest,
        request_context: &request_context::RequestContext,
    ) -> Result<auth_query::GetTokenInfoResponse, tonic::Status> {
        let mut client = match self.get_client().await {
            Ok(c) => c,
            Err(e) => {
                tracing::error!("auth_query client not ready: {}", e);
                return Err(tonic::Status::unavailable("auth_query not ready"));
            }
        };

        let mut request = tonic::Request::new(request);
        request_context.annotate(request.metadata_mut());
        let response = client.get_token_info(request).await?;
        Ok(response.into_inner())
    }
}

impl AuthQueryClientImpl {
    pub fn new(
        endpoint: &str,
        tls_config: Option<ClientTlsConfig>,
        request_timeout: Duration,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            request_timeout,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        auth_query::auth_query_client::AuthQueryClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    Some(self.request_timeout),
                    &self.tls_config,
                )
                .await?;
                let client = auth_query::auth_query_client::AuthQueryClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

pub struct MockAuthQueryClient {
    tokens: HashMap<String, String>,
    subscriptions: HashMap<String, Option<auth_query::get_token_info_response::Subscription>>,
}

impl MockAuthQueryClient {
    pub fn new(tokens: HashMap<String, String>) -> Self {
        Self {
            tokens,
            subscriptions: HashMap::new(),
        }
    }

    pub fn with_subscription(
        mut self,
        token: String,
        subscription: Option<auth_query::get_token_info_response::Subscription>,
    ) -> Self {
        self.subscriptions.insert(token, subscription);
        self
    }
}

#[async_trait]
impl AuthQueryClient for MockAuthQueryClient {
    async fn get_token_info(
        &self,
        request: auth_query::GetTokenInfoRequest,
        _request_context: &request_context::RequestContext,
    ) -> Result<auth_query::GetTokenInfoResponse, tonic::Status> {
        let token = &request.token;
        match self.tokens.get(token) {
            None => Err(tonic::Status::not_found("Token not found")),
            Some(user_id) => Ok(auth_query::GetTokenInfoResponse {
                user_id: user_id.clone(),
                opaque_user_id: Some(UserId {
                    user_id_type: UserIdType::ApiToken.into(),
                    user_id: user_id.clone(),
                }),
                user_email: None,
                signed_token: "".to_string(),
                tenant_id: "".to_string(),
                tenant_name: "".to_string(),
                subscription: self.subscriptions.get(token).cloned().unwrap_or(Some(
                    auth_query::get_token_info_response::Subscription::ActiveSubscription(
                        auth_query::ActiveSubscription {
                            end_date: None,
                            usage_balance_depleted: false,
                            billing_method:
                                auth_entities_proto::auth_entities::BillingMethod::Unknown as i32,
                        },
                    ),
                )),
                suspensions: Vec::new(),
            }),
        }
    }
}

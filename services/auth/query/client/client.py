"""Python stub for Auth Query GRPC."""

import logging
from typing import Optional

import grpc

import base.python.grpc.client_options as client_options
import services.auth.query.auth_query_pb2_grpc as auth_query_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> auth_query_pb2_grpc.AuthQueryStub:
    """Setup the client stub for auth query."""
    if not credentials:
        logging.info(
            "No credentials provided, creating insecure channel to %s", endpoint
        )
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = auth_query_pb2_grpc.AuthQueryStub(channel)
    return stub

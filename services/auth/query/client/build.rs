// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../../").canonicalize().unwrap();

    let protos = vec![
        root.join("services/auth/query/auth_query.proto"),
        root.join("services/auth/central/server/auth_entities.proto"),
    ];
    let timestamp_include_path =
        root.join("../protobuf~/src/google/protobuf/_virtual_imports/timestamp_proto/");

    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();
        let includes = vec![proto_dir, root.as_ref(), timestamp_include_path.as_ref()];

        tonic_build::configure()
            .extern_path(".google.protobuf.Timestamp", "::prost_wkt_types::Timestamp")
            .extern_path(".auth_entities", "::auth_entities_proto::auth_entities")
            .compile_protos(&[proto_path], &includes)?;
    }
    Ok(())
}

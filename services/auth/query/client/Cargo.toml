[package]
name = "auth_query_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "auth_query_client"
path = "auth_query_client.rs"

[dependencies]
async-lock =  { workspace = true }
async-trait =  { workspace = true }
grpc_client = { path = "../../../lib/grpc/client" }
prost =  { workspace = true }
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
tonic =  { workspace = true }
tonic-build =  { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../../base/rust/tracing-tonic" }
request_context = { path = "../../../lib/request_context" }
auth_entities_proto = { path = "../../../auth/central/server" }

[build-dependencies]
tonic-build =  { workspace = true }
auth_entities_proto = { path = "../../../auth/central/server" }

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library")

py_library(
    name = "client",
    srcs = [
        "client.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/logging:console_logging",
        "//base/python/grpc:client_options",
        "//services/auth/query:auth_query_py_proto",
        requirement("grpcio"),
        requirement("protobuf"),
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["auth_query_client.rs"],
    aliases = aliases(),
    crate_name = "auth_query_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/rust/tracing-tonic",
        "//services/auth/central/server:auth_entities_rs_proto",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/auth/query:auth_query_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/auth/query/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/query:auth_query_go_proto",
        "//services/lib/request_context:request_context_go",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

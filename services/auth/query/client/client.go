package authqueryclient

import (
	"context"
	"fmt"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	"github.com/augmentcode/augment/base/go/secretstring"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/auth/query/proto"
)

type AuthQueryClient interface {
	GetTokenInfo(ctx context.Context,
		token secretstring.SecretString,
		requestor string, requestContext *requestcontext.RequestContext) (*pb.GetTokenInfoResponse, error)
	Close() error
}

type authQueryClientImpl struct {
	endpoint string
	conn     *grpc.ClientConn
	client   pb.AuthQueryClient
}

// NewAuthQueryClient creates a new AuthQueryClient.
func NewAuthQueryClient(endpoint string, creds credentials.TransportCredentials) (AuthQueryClient, error) {
	conn, err := grpc.Dial(endpoint, grpc.WithTransportCredentials(creds))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to settings service: %v", err)
	}

	client := pb.NewAuthQueryClient(conn)
	return &authQueryClientImpl{
		endpoint: endpoint,
		conn:     conn,
		client:   client,
	}, nil
}

// Close closes the client connection.
func (c *authQueryClientImpl) Close() error {
	return c.conn.Close()
}

func (c *authQueryClientImpl) GetTokenInfo(ctx context.Context, token secretstring.SecretString, requestor string, requestContext *requestcontext.RequestContext) (*pb.GetTokenInfoResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	req := &pb.GetTokenInfoRequest{
		Token:     token.Expose(),
		Requestor: requestor,
	}
	return c.client.GetTokenInfo(ctx, req)
}

load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "auth_query_proto",
    srcs = ["auth_query.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "auth_query_py_proto",
    protos = [":auth_query_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_py_proto",
    ],
)

go_grpc_library(
    name = "auth_query_go_proto",
    importpath = "github.com/augmentcode/augment/services/auth/query/proto",
    proto = ":auth_query_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/auth/central/server:auth_entities_go_proto",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        "//deploy/tenants:namespaces",
        "//services/auth/query/server:kubecfg",
    ],
)

local certLib = import 'deploy/common/cert-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

// It will fix users with multiple Stripe subscriptions
function(cloud, env, namespace, namespace_config)
  local appName = 'fix-stripe-subs-job-v1';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  // Reuse the auth-central service account
  local serviceAccount = gcpLib.createServiceAccount(app='auth-central', cloud=cloud, env=env, namespace=namespace, iam=true);

  // Determine the GCS bucket based on environment
  local gcsBucket = if env == 'DEV' || namespace == 'dev-xiaolei' then 'augment-data-dev' else 'augment-data';

  // Grant GCS bucket access to the service account
  local bucketAccess = gcpLib.grantAccess(
    name='%s-bucket-policy' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'StorageBucket',
      external: gcsBucket,
    },
    bindings=[
      {
        role: 'roles/storage.objectViewer',
        members: [
          { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
        ],
      },
    ],
    abandon=true,
  );

  // Get BigTable configuration
  local bigtable = gcpLib.getBigtableTable(cloud=cloud,
                                           env=env,
                                           namespace=namespace,
                                           tableName='auth-central');

  // GCS folder path and filename
  local gcsFolder = 'migration-data/2025-04-30-fix-stripe-multiple-subs';
  local gcsFilename = 'prod-run.csv';  // Updated for production run

  // Tenant watcher endpoint - use the standard endpoint from endpoints.jsonnet
  local tenantWatcherEndpoint = 'tenant-central-svc:50051';

  // Create client certificates for mTLS
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames('tenant-central', namespace=namespace),
  );

  // Mount the Stripe secret from GCP Secret Manager
  local stripeSecretKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='stripe',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  // Create a config map for the job configuration
  local configMap = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: appName + '-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJson({
        bigtable: {
          project_id: bigtable.projectId,
          instance_id: bigtable.instanceName,
          table_name: bigtable.tableName,
        },
        gcs: {
          bucket_name: gcsBucket,
          folder_path: gcsFolder,
          filename: gcsFilename,
        },
        tenant_watcher: {
          endpoint: tenantWatcherEndpoint,
          client_mtls: {
            cert_path: clientCert.volumeMountDef.mountPath + '/tls.crt',
            key_path: clientCert.volumeMountDef.mountPath + '/tls.key',
            ca_path: clientCert.volumeMountDef.mountPath + '/ca.crt',
          },
        },
        stripe: {
          secret_key_path: stripeSecretKey.filePath,
        },
        // No tenant filtering - process all users in the CSV
        // Set dry run mode to true by default for safety
        dry_run: false,
      }),
    },
  };

  // Stripe secret is already defined above

  // Create the job container
  local container = {
    name: appName,
    target: {
      name: '//services/auth/central/jobs/fix_stripe_subs:fix_stripe_subs_job_image',
      dst: appName,
    },
    args: [
      // Pass the config file path as a command-line argument
      '--config-file=/etc/config/config.json',
    ],
    // No environment variables for secrets
    env: [],
    volumeMounts: [
      {
        name: 'config-volume',
        mountPath: '/etc/config',
      },
      stripeSecretKey.volumeMountDef,
      clientCert.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 1,
        memory: '2Gi',
      },
    },
  };

  // Create the job pod
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    restartPolicy: 'Never',
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      {
        name: 'config-volume',
        configMap: {
          name: configMap.metadata.name,
        },
      },
      stripeSecretKey.podVolumeDef,
      clientCert.podVolumeDef,
    ],
  };

  // Create the job
  local job = {
    apiVersion: 'batch/v1',
    kind: 'Job',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      backoffLimit: 3,
      ttlSecondsAfterFinished: 86400,  // 24 hours
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  lib.flatten([
    configMap,
    job,
    stripeSecretKey.objects,
    clientCert.objects,
    bucketAccess,  // Include GCS bucket access
  ])

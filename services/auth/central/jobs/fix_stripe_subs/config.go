package main

import (
	"encoding/json"
	"os"
	"path/filepath"

	"github.com/rs/zerolog/log"
)

// Config represents the configuration for the fix_stripe_subs job
type Config struct {
	// BigTable configuration
	BigTable struct {
		ProjectID  string `json:"project_id"`
		InstanceID string `json:"instance_id"`
		TableName  string `json:"table_name"`
	} `json:"bigtable"`

	// GCS configuration
	GCS struct {
		BucketName string `json:"bucket_name"`
		FolderPath string `json:"folder_path"`
		Filename   string `json:"filename"`
	} `json:"gcs"`

	// TenantWatcher configuration
	TenantWatcher struct {
		Endpoint string `json:"endpoint"`
		// mTLS configuration
		ClientMtls *struct {
			CertPath string `json:"cert_path"`
			KeyPath  string `json:"key_path"`
			CaPath   string `json:"ca_path"`
		} `json:"client_mtls"`
	} `json:"tenant_watcher"`

	// Stripe configuration
	Stripe struct {
		SecretKeyPath string `json:"secret_key_path"`
	} `json:"stripe"`

	// No tenant filtering - process all users in the CSV

	// Whether to run in dry run mode (no changes)
	DryRun bool `json:"dry_run"`
}

// ReadConfig reads the configuration from a file
func ReadConfig(configPath string) (*Config, error) {
	// Resolve the path
	absPath, err := filepath.Abs(configPath)
	if err != nil {
		log.Error().Err(err).Str("path", configPath).Msg("Failed to resolve config file path")
		return nil, err
	}

	// Open the file
	file, err := os.Open(absPath)
	if err != nil {
		log.Error().Err(err).Str("path", absPath).Msg("Failed to open config file")
		return nil, err
	}
	defer file.Close()

	// Parse the JSON
	var config Config
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		log.Error().Err(err).Str("path", absPath).Msg("Failed to parse config file")
		return nil, err
	}

	// Log the configuration
	log.Info().
		Str("bigtable_project_id", config.BigTable.ProjectID).
		Str("bigtable_instance_id", config.BigTable.InstanceID).
		Str("bigtable_table_name", config.BigTable.TableName).
		Str("gcs_bucket", config.GCS.BucketName).
		Str("gcs_folder", config.GCS.FolderPath).
		Str("gcs_filename", config.GCS.Filename).
		Str("tenant_watcher_endpoint", config.TenantWatcher.Endpoint).
		Str("stripe_secret_key_path", config.Stripe.SecretKeyPath).
		Bool("dry_run", config.DryRun).
		Msg("Loaded configuration")

	return &config, nil
}

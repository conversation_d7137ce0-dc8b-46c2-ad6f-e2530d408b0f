# Fix Stripe Subscriptions Job

This job is designed to fix users who have multiple Stripe subscriptions. It:

- Reads a CSV file from GCS containing user IDs and their Stripe customer IDs
- For each user, pulls all non-canceled subscriptions directly from Stripe for all customer IDs
- Determines which subscription to keep based on:
  - Paid status (prefer paid subscriptions)
  - Active status (both ACTIVE and TRIAL status)
  - Start date (prefer the most recent)
- Updates the user record to point to the preferred subscription
- Cancels all other subscriptions, with full refunds for paid subscriptions

## CSV Format

The CSV file should have the following format:

```
opaque_user_id,customer_ids
user_id_1,"[\"cus_123\", \"cus_456\"]"
user_id_2,"[\"cus_789\"]"
```

Where:
- `opaque_user_id`: The user's ID in the auth system
- `customer_ids`: A JSON array of Stripe customer IDs associated with the user

## Configuration

The job is configured via a JSON configuration file:

```json
{
  "bigtable": {
    "project_id": "your-project-id",
    "instance_id": "auth-central-dev",
    "table_name": "auth"
  },
  "gcs": {
    "bucket_name": "augment-data-dev",
    "folder_path": "migration-data/2025-04-30-fix-stripe-multiple-subs",
    "filename": "users.csv"
  },
  "tenant_watcher": {
    "endpoint": "tenant-watcher.namespace.svc.cluster.local:8080"
  },
  "dry_run": true
}
```

- `bigtable`: BigTable connection details
- `gcs`: GCS configuration
  - `bucket_name`: The GCS bucket name (use `augment-data-dev` for development environments, `augment-data` for others)
  - `folder_path`: The folder path within the bucket
  - `filename`: The CSV filename
- `tenant_watcher`: Tenant watcher service configuration
  - `endpoint`: The endpoint for the tenant watcher service
- `dry_run`: When true, no changes will be made (for testing)

## Usage

The job can be run with the following command-line flags:

```
--config-file=PATH   Path to the configuration file (required)
--dry-run=BOOL       Override the dry_run setting in the config file
```

Example:

```
./fix_stripe_subs_job --config-file=config.json --dry-run=true
```

## Deployment

The job is deployed as a Kubernetes job and can be triggered manually when needed. It is not scheduled to run automatically.

## Safety Measures

- The job runs in dry-run mode by default
- Only users in the specified tenants are processed
- Users in self-serve team tenants are automatically skipped
- Comprehensive logging of all actions
- Graceful handling of errors

## Note

This job should only be run in staging environments and development environments for testing before running in production.

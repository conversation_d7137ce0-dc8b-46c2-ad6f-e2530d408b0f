package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"
	"time"

	bigtable "cloud.google.com/go/bigtable"
	auth_dao "github.com/augmentcode/augment/services/auth/central/server/auth_dao"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
)

// UserDAO is an alias for the auth_dao.UserDAO type
type UserDAO = auth_dao.UserDAO

// SubscriptionDAO is an alias for the auth_dao.SubscriptionDAO type
type SubscriptionDAO = auth_dao.SubscriptionDAO

// NewUserDAO creates a new UserDAO
func NewUserDAO(table *bigtable.Table) *UserDAO {
	return auth_dao.NewUserDAO(table)
}

// NewSubscriptionDAO creates a new SubscriptionDAO
func NewSubscriptionDAO(table *bigtable.Table) *SubscriptionDAO {
	return auth_dao.NewSubscriptionDAO(table)
}

func main() {
	// Configure logging
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stdout, TimeFormat: time.RFC3339})

	// Parse command line flags
	dryRunFlag := flag.Bool("dry-run", false, "When true, run in dry-run mode (no changes); overrides config file")
	dryRunFlagSet := false
	flag.Visit(func(f *flag.Flag) {
		if f.Name == "dry-run" {
			dryRunFlagSet = true
		}
	})
	configFile := flag.String("config-file", "", "Path to the configuration file")
	flag.Parse()

	// Set up context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle signals for graceful shutdown
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		sig := <-signalChan
		log.Info().Str("signal", sig.String()).Msg("Received signal, shutting down")
		cancel()
	}()

	// Load configuration from file
	if *configFile == "" {
		log.Error().Msg("Config file path is required. Use --config-file flag")
		os.Exit(1)
	}

	// Read the configuration
	config, err := ReadConfig(*configFile)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read configuration")
		os.Exit(1)
	}

	// Determine dry run mode - command line flag overrides config file
	dryRun := config.DryRun
	if dryRunFlagSet {
		dryRun = *dryRunFlag
		log.Info().Bool("dry_run", dryRun).Msg("Using dry run setting from command line (overriding config)")
	} else {
		log.Info().Bool("dry_run", dryRun).Msg("Using dry run setting from config file")
	}

	log.Info().
		Bool("dry_run", dryRun).
		Str("gcs_bucket", config.GCS.BucketName).
		Str("gcs_folder", config.GCS.FolderPath).
		Str("gcs_filename", config.GCS.Filename).
		Str("tenant_watcher_endpoint", config.TenantWatcher.Endpoint).
		Msg("Fix Stripe subscriptions job configuration")

	// Setup BigTable connection
	log.Info().Msg("Setting up BigTable connection")

	// Create BigTable client
	client, err := bigtable.NewClient(ctx, config.BigTable.ProjectID, config.BigTable.InstanceID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create BigTable client")
		os.Exit(1)
	}
	defer client.Close()

	// Open the table
	bigtableTable := client.Open(config.BigTable.TableName)

	// Create DAOs
	log.Info().Msg("Creating DAOs")
	userDAO := NewUserDAO(bigtableTable)
	subscriptionDAO := NewSubscriptionDAO(bigtableTable)

	// Initialize Stripe API key from file
	stripeKeyBytes, err := os.ReadFile(config.Stripe.SecretKeyPath)
	if err != nil {
		log.Error().Err(err).Str("path", config.Stripe.SecretKeyPath).Msg("Failed to read Stripe API key from file")
		os.Exit(1)
	}
	stripe.Key = string(stripeKeyBytes)

	// Create Stripe client
	stripeClient := &DefaultStripeClient{}

	// Run the job
	err = fixStripeSubscriptions(ctx, userDAO, subscriptionDAO, stripeClient, config, dryRun)
	if err != nil {
		os.Exit(1)
	}

	// Close the BigTable client before exiting
	client.Close()
}

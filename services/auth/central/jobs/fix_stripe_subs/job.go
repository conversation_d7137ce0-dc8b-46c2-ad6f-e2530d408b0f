package main

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"math/rand"
	"sort"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/refund"
	"github.com/stripe/stripe-go/v80/subscription"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// StripeClient defines the interface for Stripe operations
type StripeClient interface {
	CancelSubscription(subscriptionID string) error
	ListNonCanceledSubscriptions(customerID string) ([]*stripe.Subscription, error)
}

// DefaultStripeClient implements the StripeClient interface using the Stripe API
type DefaultStripeClient struct{}

func (c *DefaultStripeClient) CancelSubscription(subscriptionID string) error {
	_, err := subscription.Cancel(subscriptionID, nil)
	return err
}

func (c *DefaultStripeClient) ListNonCanceledSubscriptions(customerID string) ([]*stripe.Subscription, error) {
	// Use "all" status to get all subscriptions, then filter out canceled ones
	params := &stripe.SubscriptionListParams{
		Customer: stripe.String(customerID),
		Status:   stripe.String("all"),
	}

	// Add expansion for latest_invoice
	params.AddExpand("data.latest_invoice")

	iter := subscription.List(params)

	var subs []*stripe.Subscription
	for iter.Next() {
		sub := iter.Subscription()
		// Only include non-canceled subscriptions
		if sub.Status != "canceled" {
			subs = append(subs, sub)
		}
	}
	return subs, iter.Err()
}

// SubscriptionInfo represents a subscription from Stripe API
type SubscriptionInfo struct {
	IsPaid         bool
	CustomerID     string
	SubscriptionID string
	StartDate      string
	IsActive       bool
	Status         string
	LastInvoiceID  string
}

// FixStripeSubsJob handles fixing multiple Stripe subscriptions for users
type FixStripeSubsJob struct {
	userDAO         *UserDAO
	subscriptionDAO *SubscriptionDAO
	stripeClient    StripeClient
	tenantCache     tenantwatcherclient.TenantCache
	dryRun          bool
	gcsBucketName   string
	gcsFolderPath   string
	gcsFilename     string
}

// NewFixStripeSubsJob creates a new job to fix Stripe subscriptions
func NewFixStripeSubsJob(
	userDAO *UserDAO,
	subscriptionDAO *SubscriptionDAO,
	stripeClient StripeClient,
	tenantCache tenantwatcherclient.TenantCache,
	dryRun bool,
	gcsBucketName string,
	gcsFolderPath string,
	gcsFilename string,
) *FixStripeSubsJob {
	return &FixStripeSubsJob{
		userDAO:         userDAO,
		subscriptionDAO: subscriptionDAO,
		stripeClient:    stripeClient,
		tenantCache:     tenantCache,
		dryRun:          dryRun,
		gcsBucketName:   gcsBucketName,
		gcsFolderPath:   gcsFolderPath,
		gcsFilename:     gcsFilename,
	}
}

// Run executes the job
func (j *FixStripeSubsJob) Run(ctx context.Context) error {
	startTime := time.Now()
	log.Info().
		Bool("dry_run", j.dryRun).
		Str("gcs_bucket", j.gcsBucketName).
		Str("gcs_folder", j.gcsFolderPath).
		Str("gcs_filename", j.gcsFilename).
		Msg("Starting job")

	userCustomerIDs, err := j.readCSVFromGCS(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read CSV file from GCS")
		return err
	}
	processedUsers := 0
	failedUsers := 0

	for userID, customerIDs := range userCustomerIDs {
		log.Info().
			Str("user_id", userID).
			Int("customer_id_count", len(customerIDs)).
			Msg("Processing user")

		err := j.processUser(ctx, userID, customerIDs)
		if err != nil {
			failedUsers++
			log.Error().
				Err(err).
				Str("user_id", userID).
				Msg("Failed to process user")
			continue
		}

		processedUsers++
	}

	duration := time.Since(startTime)
	log.Info().
		Int("processed_users", processedUsers).
		Int("failed_users", failedUsers).
		Dur("duration", duration).
		Bool("dry_run", j.dryRun).
		Msg("Job completed")

	return nil
}

// readCSVFromGCS reads the CSV file from GCS and returns a map of user IDs to their customer IDs
func (j *FixStripeSubsJob) readCSVFromGCS(ctx context.Context) (map[string][]string, error) {
	log.Info().
		Str("gcs_bucket", j.gcsBucketName).
		Str("gcs_folder", j.gcsFolderPath).
		Str("gcs_filename", j.gcsFilename).
		Msg("Reading CSV file from GCS")

	client, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCS client: %w", err)
	}
	defer client.Close()
	objectPath := j.gcsFolderPath
	if !strings.HasSuffix(objectPath, "/") {
		objectPath += "/"
	}
	objectPath += j.gcsFilename

	bucket := client.Bucket(j.gcsBucketName)
	object := bucket.Object(objectPath)
	reader, err := object.NewReader(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to read object from GCS: %w", err)
	}
	defer reader.Close()
	csvReader := csv.NewReader(reader)
	csvReader.TrimLeadingSpace = true

	userCustomerIDs := make(map[string][]string)
	lineNum := 0
	for {
		lineNum++
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Error().Err(err).Msgf("Error reading CSV line %d", lineNum)
			continue
		}

		// Skip header row
		if lineNum == 1 && (len(record) > 0 && strings.Contains(strings.ToLower(record[0]), "opaque_user_id")) {
			continue
		}

		// Each row should have at least 2 columns: user_id and customer_ids (JSON array)
		if len(record) < 2 {
			log.Error().Msgf("Invalid CSV record at line %d, expected at least 2 fields but got %d", lineNum, len(record))
			continue
		}

		opaqueUserID := strings.TrimSpace(record[0])
		customerIDsJSON := strings.TrimSpace(record[1])

		if opaqueUserID == "" || customerIDsJSON == "" {
			log.Warn().Msgf("Skipping CSV line %d: empty user ID or customer IDs", lineNum)
			continue
		}

		// Parse the JSON array of customer IDs
		var customerIDs []string
		if err := json.Unmarshal([]byte(customerIDsJSON), &customerIDs); err != nil {
			log.Error().
				Err(err).
				Str("user_id", opaqueUserID).
				Str("customer_ids_json", customerIDsJSON).
				Msgf("Failed to parse customer IDs JSON at line %d", lineNum)
			continue
		}

		// Add the customer IDs to the user's list
		userCustomerIDs[opaqueUserID] = customerIDs
	}

	log.Info().Msgf("Read %d users from GCS CSV file", len(userCustomerIDs))
	return userCustomerIDs, nil
}

// processUser processes a single user's customer IDs
func (j *FixStripeSubsJob) processUser(ctx context.Context, userID string, csvCustomerIDs []string) error {
	user, err := j.userDAO.Get(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found: %s", userID)
	}

	// Check if user is in a self-serve team tenant
	for _, tenantID := range user.Tenants {
		tenant, err := j.tenantCache.GetTenant(tenantID)
		if err != nil {
			log.Warn().
				Err(err).
				Str("user_id", userID).
				Str("tenant_id", tenantID).
				Msg("Failed to get tenant information, continuing with processing")
			continue
		}

		if tenantutil.IsSelfServeTeamTenant(tenant) {
			log.Info().
				Str("user_id", userID).
				Str("tenant_id", tenantID).
				Str("tenant_name", tenant.Name).
				Msg("User is in a self-serve team tenant, skipping")
			return nil
		}
	}

	// Get all customer IDs associated with this user
	customerIDs := make(map[string]bool)

	// Add the current customer ID from the user record if it exists
	if user.StripeCustomerId != "" {
		customerIDs[user.StripeCustomerId] = true
	}

	// Add customer IDs from CSV
	for _, customerID := range csvCustomerIDs {
		if customerID != "" {
			customerIDs[customerID] = true
		}
	}

	if len(customerIDs) == 0 {
		log.Info().Str("user_id", userID).Msg("No customer IDs found for user")
		return nil
	}

	// Pull all non-canceled subscriptions directly from Stripe for all customer IDs
	var allSubscriptions []SubscriptionInfo

	log.Info().
		Str("user_id", userID).
		Int("customer_count", len(customerIDs)).
		Msg("Fetching subscriptions from Stripe for all customer IDs")

	// Convert map keys to a slice for iteration
	customerIDList := make([]string, 0, len(customerIDs))
	for customerID := range customerIDs {
		customerIDList = append(customerIDList, customerID)
	}

	for i, customerID := range customerIDList {
		// Add a small delay between API calls to avoid rate limiting, except for the first call
		if i > 0 {
			time.Sleep(200 * time.Millisecond)
		}

		stripeSubscriptions, err := j.stripeClient.ListNonCanceledSubscriptions(customerID)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_id", userID).
				Str("customer_id", customerID).
				Msg("Failed to fetch subscriptions from Stripe")
			continue
		}

		log.Info().
			Str("user_id", userID).
			Str("customer_id", customerID).
			Int("subscription_count", len(stripeSubscriptions)).
			Msg("Fetched subscriptions from Stripe")

		// Convert Stripe subscriptions to SubscriptionInfo
		for _, stripeSub := range stripeSubscriptions {
			// Determine if subscription is active (both ACTIVE and TRIAL status)
			isActive := stripeSub.Status == "active" || stripeSub.Status == "trialing"

			// Determine if subscription is paid by checking if it has a default payment method
			// and is in an active state
			isPaid := stripeSub.DefaultPaymentMethod != nil && stripeSub.Status == "active"

			// Format start date as YYYY-MM-DD
			startDate := time.Unix(stripeSub.StartDate, 0).Format("2006-01-02")

			// Get the latest invoice ID if available
			lastInvoiceID := ""
			if stripeSub.LatestInvoice != nil {
				lastInvoiceID = stripeSub.LatestInvoice.ID
			}

			subInfo := SubscriptionInfo{
				IsPaid:         isPaid,
				CustomerID:     customerID,
				SubscriptionID: stripeSub.ID,
				StartDate:      startDate,
				IsActive:       isActive,
				Status:         string(stripeSub.Status),
				LastInvoiceID:  lastInvoiceID,
			}

			allSubscriptions = append(allSubscriptions, subInfo)
		}
	}

	// If we couldn't fetch any subscriptions from Stripe, log and return
	if len(allSubscriptions) == 0 {
		log.Info().Str("user_id", userID).Msg("No subscriptions found in Stripe for any customer ID")
		return nil
	}

	preferredSub, otherSubs := j.determinePreferredSubscription(allSubscriptions)

	log.Info().
		Str("user_id", userID).
		Str("preferred_subscription_id", preferredSub.SubscriptionID).
		Bool("is_paid", preferredSub.IsPaid).
		Bool("is_active", preferredSub.IsActive).
		Str("start_date", preferredSub.StartDate).
		Str("status", preferredSub.Status).
		Int("other_subscriptions_count", len(otherSubs)).
		Msg("Determined preferred subscription")

	if !j.dryRun {
		_, err = j.userDAO.TryUpdate(ctx, userID, func(u *auth_entities.User) bool {
			log.Info().
				Str("user_id", u.Id).
				Str("old_stripe_customer_id", u.StripeCustomerId).
				Str("old_subscription_id", u.GetSubscriptionId()).
				Str("new_stripe_customer_id", preferredSub.CustomerID).
				Str("new_subscription_id", preferredSub.SubscriptionID).
				Msg("Updating user subscription")
			if u.StripeCustomerId == preferredSub.CustomerID && u.GetSubscriptionId() == preferredSub.SubscriptionID {
				log.Info().
					Str("user_id", u.Id).
					Msg("User already has the preferred subscription, skipping update")
				return false
			}

			u.StripeCustomerId = preferredSub.CustomerID
			u.SubscriptionId = &preferredSub.SubscriptionID

			return true
		}, retryBackoff)
		if err != nil {
			return fmt.Errorf("failed to update user: %w", err)
		}
	} else {
		log.Info().
			Str("user_id", userID).
			Str("new_stripe_customer_id", preferredSub.CustomerID).
			Str("new_subscription_id", preferredSub.SubscriptionID).
			Msg("Would maybe update user (dry run)")
	}

	// Cancel and refund other subscriptions
	for i, sub := range otherSubs {
		// Add a small delay between API calls to avoid rate limiting
		if i > 0 {
			time.Sleep(200 * time.Millisecond)
		}

		if !j.dryRun {
			// First cancel the subscription
			log.Info().
				Str("user_id", userID).
				Str("subscription_id", sub.SubscriptionID).
				Msg("Cancelling subscription")
			if err := j.stripeClient.CancelSubscription(sub.SubscriptionID); err != nil {
				log.Error().
					Err(err).
					Str("user_id", userID).
					Str("subscription_id", sub.SubscriptionID).
					Msg("Failed to cancel subscription")
				// Continue with refund attempt even if cancellation fails
			}

			// Add a small delay between cancel and refund
			time.Sleep(200 * time.Millisecond)

			// Refund the subscription if it's paid and has a last invoice ID
			if sub.IsPaid && sub.LastInvoiceID != "" {
				log.Info().
					Str("user_id", userID).
					Str("subscription_id", sub.SubscriptionID).
					Str("invoice_id", sub.LastInvoiceID).
					Msg("Refunding subscription")

				// Create a refund for the invoice
				_, err := refund.New(&stripe.RefundParams{
					Charge: stripe.String(sub.LastInvoiceID),
				})
				if err != nil {
					log.Error().
						Err(err).
						Str("user_id", userID).
						Str("subscription_id", sub.SubscriptionID).
						Str("invoice_id", sub.LastInvoiceID).
						Msg("Failed to refund subscription")
					// Continue with cancellation even if refund fails
				}
			} else if sub.IsPaid {
				log.Warn().
					Str("user_id", userID).
					Str("subscription_id", sub.SubscriptionID).
					Msg("Subscription is paid but has no invoice ID to refund")
			}
		} else {
			log.Info().
				Str("user_id", userID).
				Str("subscription_id", sub.SubscriptionID).
				Msg("Would cancel subscription (dry run)")
			if sub.IsPaid && sub.LastInvoiceID != "" {
				log.Info().
					Str("user_id", userID).
					Str("subscription_id", sub.SubscriptionID).
					Str("invoice_id", sub.LastInvoiceID).
					Msg("Would refund subscription (dry run)")
			} else if sub.IsPaid {
				log.Warn().
					Str("user_id", userID).
					Str("subscription_id", sub.SubscriptionID).
					Msg("Subscription is paid but has no invoice ID to refund (dry run)")
			}
		}
	}

	return nil
}

// determinePreferredSubscription determines which subscription to keep
// Prefers paid subscriptions, then active subscriptions, and if multiple with same paid/active status, prefers the one with the latest start date
func (j *FixStripeSubsJob) determinePreferredSubscription(subscriptions []SubscriptionInfo) (SubscriptionInfo, []SubscriptionInfo) {
	if len(subscriptions) == 0 {
		return SubscriptionInfo{}, nil
	}

	if len(subscriptions) == 1 {
		return subscriptions[0], nil
	}

	// First, separate paid and free subscriptions
	var paidActiveSubs []SubscriptionInfo
	var paidInactiveSubs []SubscriptionInfo
	var freeActiveSubs []SubscriptionInfo
	var freeInactiveSubs []SubscriptionInfo

	for _, sub := range subscriptions {
		if sub.IsPaid {
			if sub.IsActive {
				paidActiveSubs = append(paidActiveSubs, sub)
			} else {
				paidInactiveSubs = append(paidInactiveSubs, sub)
			}
		} else {
			if sub.IsActive {
				freeActiveSubs = append(freeActiveSubs, sub)
			} else {
				freeInactiveSubs = append(freeInactiveSubs, sub)
			}
		}
	}

	// Determine candidate subscriptions in order of preference:
	// 1. Paid and active
	// 2. Paid but inactive
	// 3. Free and active
	// 4. Free and inactive
	var candidateSubs []SubscriptionInfo
	if len(paidActiveSubs) > 0 {
		candidateSubs = paidActiveSubs
	} else if len(paidInactiveSubs) > 0 {
		candidateSubs = paidInactiveSubs
	} else if len(freeActiveSubs) > 0 {
		candidateSubs = freeActiveSubs
	} else {
		candidateSubs = freeInactiveSubs
	}

	// Sort by start date (descending)
	sort.Slice(candidateSubs, func(i, j int) bool {
		// Parse the dates for comparison
		iDate, iErr := time.Parse("2006-01-02", candidateSubs[i].StartDate)
		jDate, jErr := time.Parse("2006-01-02", candidateSubs[j].StartDate)

		// If we can't parse either date, fall back to string comparison
		if iErr != nil || jErr != nil {
			return candidateSubs[i].StartDate > candidateSubs[j].StartDate
		}

		// Compare the actual dates
		return iDate.After(jDate)
	})

	// The preferred subscription is the first one after sorting
	preferred := candidateSubs[0]

	// Find the other subscriptions (all except the preferred one)
	var others []SubscriptionInfo
	for _, sub := range subscriptions {
		if sub.SubscriptionID != preferred.SubscriptionID {
			others = append(others, sub)
		}
	}

	return preferred, others
}

// retryBackoff is a function that determines how long to wait before retrying an operation
func retryBackoff(retry int) float64 {
	// Exponential backoff with jitter
	maxRetry := 5
	if retry >= maxRetry {
		return -1 // Stop retrying
	}
	jitter := rand.Float64() * 0.5
	return math.Pow(2, float64(retry)) * (1 + jitter)
}

// fixStripeSubscriptions is the main entry point for the job
func fixStripeSubscriptions(
	ctx context.Context,
	userDAO *UserDAO,
	subscriptionDAO *SubscriptionDAO,
	stripeClient StripeClient,
	config *Config,
	dryRun bool,
) error {
	log.Info().
		Bool("dry_run", dryRun).
		Str("gcs_bucket", config.GCS.BucketName).
		Str("gcs_folder", config.GCS.FolderPath).
		Str("gcs_filename", config.GCS.Filename).
		Msg("Job configuration")

	// Safety check - if dry run is false, log a warning
	if !dryRun {
		log.Warn().
			Msg("Running in non-dry-run mode. Stripe subscriptions will be modified.")
	} else {
		log.Info().
			Msg("Running in dry-run mode - no changes will be made")
	}

	log.Info().Msg("Initializing tenant watcher client")

	var grpcOpts []grpc.DialOption
	if config.TenantWatcher.ClientMtls != nil {
		// Use mTLS credentials
		log.Info().Msg("Using mTLS for tenant watcher connection")

		// Use the standard tlsconfig.GetClientTls function to create credentials
		clientConfig := &tlsconfig.ClientConfig{
			CertPath: config.TenantWatcher.ClientMtls.CertPath,
			KeyPath:  config.TenantWatcher.ClientMtls.KeyPath,
			CaPath:   config.TenantWatcher.ClientMtls.CaPath,
		}

		clientCreds, err := tlsconfig.GetClientTls(clientConfig)
		if err != nil {
			log.Error().Err(err).
				Str("cert_path", config.TenantWatcher.ClientMtls.CertPath).
				Str("key_path", config.TenantWatcher.ClientMtls.KeyPath).
				Str("ca_path", config.TenantWatcher.ClientMtls.CaPath).
				Msg("Failed to create client TLS credentials")
			return err
		}

		grpcOpts = append(grpcOpts, grpc.WithTransportCredentials(clientCreds))
	} else {
		// Use insecure credentials
		log.Info().Msg("Using insecure credentials for tenant watcher connection")
		grpcOpts = append(grpcOpts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	}

	tenantWatcherClient := tenantwatcherclient.New(
		config.TenantWatcher.Endpoint,
		grpcOpts...,
	)
	defer tenantWatcherClient.Close()

	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, "")
	log.Info().Msg("Tenant cache initialized")

	// Create and run the job
	job := NewFixStripeSubsJob(
		userDAO,
		subscriptionDAO,
		stripeClient,
		tenantCache,
		dryRun,
		config.GCS.BucketName,
		config.GCS.FolderPath,
		config.GCS.Filename,
	)

	err := job.Run(ctx)

	// Close the tenant watcher client to prevent panic
	log.Info().Msg("Closing tenant watcher client")
	tenantWatcherClient.Close()
	tenantCache.Close()

	return err
}

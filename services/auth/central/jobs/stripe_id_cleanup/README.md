# Stripe ID Cleanup Job

## Purpose

This job is designed to clean up Stripe customer IDs from user records in the staging environment. It was created to address the migration from Stripe sandbox to Stripe test environment, which rendered old customer IDs invalid.

## Background

When we migrated from Stripe sandbox to Stripe test environment, all previously created customer IDs became invalid. This job cleans up these invalid IDs by removing them from user records in the specified tenants, allowing users to be properly re-provisioned with new Stripe customer IDs when they next interact with payment features.

## Functionality

The job:
- Removes Stripe customer IDs and subscription IDs from user records
- Targets specific tenants in staging-shard-0 and dev-xiaolei environments (tenant IDs are pre-configured)
- Runs with dry_run=false by default to make actual changes

## Usage

The job is deployed as a Kubernetes job and can be triggered manually when needed. It is not scheduled to run automatically.

## Note

This job should only be run in staging environments and development environments, never in production.

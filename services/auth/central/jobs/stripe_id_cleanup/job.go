package main

import (
	"context"
	"math"
	"math/rand"
	"time"

	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/rs/zerolog/log"
)

// StripeIDCleanupJob removes Stripe customer IDs from users in specified tenants
type StripeIDCleanupJob struct {
	userDAO       *UserDAO
	dryRun        bool
	targetTenants []string
}

// NewStripeIDCleanupJob creates a new job to remove Stripe customer IDs from users in specified tenants
func NewStripeIDCleanupJob(
	userDAO *UserDAO,
	dryRun bool,
	targetTenants []string,
) *StripeIDCleanupJob {
	return &StripeIDCleanupJob{
		userDAO:       userDAO,
		dryRun:        dryRun,
		targetTenants: targetTenants,
	}
}

// Run executes the job
func (j *StripeIDCleanupJob) Run(ctx context.Context) error {
	startTime := time.Now()
	log.Info().
		Bool("dry_run", j.dryRun).
		Strs("target_tenant_ids", j.targetTenants).
		Msg("Starting job")

	// Validate target tenant IDs
	if len(j.targetTenants) == 0 || (len(j.targetTenants) == 1 && j.targetTenants[0] == "") {
		log.Warn().Msg("No tenant IDs specified, job will not process any users")
		return nil
	}

	// Process each tenant ID one at a time
	tenantCount := len(j.targetTenants)
	processedTenants := 0
	successfulTenants := 0
	failedTenants := 0
	totalProcessedUsers := 0
	totalSkippedUsers := 0
	totalFailedUsers := 0

	for _, tenantID := range j.targetTenants {
		if tenantID == "" {
			continue
		}

		log.Info().
			Str("tenant_id", tenantID).
			Int("tenant_progress", processedTenants+1).
			Int("total_tenants", tenantCount).
			Msg("Processing tenant")

		// Get and process users for this tenant ID
		processed, failed, skipped, err := j.processUsersForTenant(ctx, tenantID)
		processedTenants++

		// Update user counts with actual results
		totalProcessedUsers += processed
		totalFailedUsers += failed
		totalSkippedUsers += skipped

		// Update tenant success/failure count
		if err != nil {
			failedTenants++
			log.Error().
				Err(err).
				Str("tenant_id", tenantID).
				Int("processed", processed).
				Int("failed", failed).
				Int("skipped", skipped).
				Msg("Error processing users for tenant")
		} else {
			successfulTenants++
		}

		// Log progress
		log.Info().
			Str("tenant_id", tenantID).
			Int("processed_tenants", processedTenants).
			Int("total_tenants", tenantCount).
			Int("processed_users", totalProcessedUsers).
			Int("skipped_users", totalSkippedUsers).
			Int("failed_users", totalFailedUsers).
			Msg("Tenant processing progress")
	}

	duration := time.Since(startTime)
	log.Info().
		Int("total_tenants", tenantCount).
		Int("successful_tenants", successfulTenants).
		Int("failed_tenants", failedTenants).
		Int("processed_users", totalProcessedUsers).
		Int("skipped_users", totalSkippedUsers).
		Int("failed_users", totalFailedUsers).
		Dur("duration", duration).
		Bool("dry_run", j.dryRun).
		Msg("Job completed")
	return nil
}

// processUsersForTenant processes all users for a specific tenant
// Returns counts of processed, failed, and skipped users, plus any error
func (j *StripeIDCleanupJob) processUsersForTenant(ctx context.Context, tenantID string) (int, int, int, error) {
	startTime := time.Now()
	log.Info().
		Str("tenant_id", tenantID).
		Msg("Starting to process users for tenant")

	userDAO := j.userDAO
	processedCount := 0
	failedCount := 0
	skippedCount := 0 // We don't skip users anymore, but keeping for API compatibility

	// Get users for this tenant
	usersInTenant := make([]*auth_entities.User, 0)

	log.Info().Str("tenant_id", tenantID).Msg("Fetching users for tenant")

	var findErr error
	findErr = j.userDAO.FindAll(ctx, func(user *auth_entities.User) bool {
		// Only include users who are in this tenant
		for _, userTenantID := range user.Tenants {
			if userTenantID == tenantID {
				usersInTenant = append(usersInTenant, user)
				break
			}
		}
		return true
	})

	if findErr != nil {
		log.Error().Err(findErr).Str("tenant_id", tenantID).Msg("Failed to fetch users for tenant")
		return 0, 0, 0, findErr
	}

	totalCount := len(usersInTenant)

	log.Info().
		Str("tenant_id", tenantID).
		Int("total_users", totalCount).
		Msg("Found users to process")

	if totalCount == 0 {
		log.Info().
			Str("tenant_id", tenantID).
			Msg("No users found for tenant, skipping")
		return 0, 0, 0, nil
	}

	// Process each user
	for i, user := range usersInTenant {
		// All users in this list should be in this tenant

		// Log what we're about to do
		log.Info().
			Str("tenant_id", tenantID).
			Str("user_id", user.Id).
			Str("email", user.Email).
			Str("stripe_customer_id", user.StripeCustomerId).
			Str("subscription_id", stringOrEmpty(user.SubscriptionId)).
			Bool("dry_run", j.dryRun).
			Int("progress", i+1).
			Int("total", totalCount).
			Msg("Processing user")

		if j.dryRun {
			processedCount++
			continue
		}

		// Process this user
		startTime := time.Now()

		// Update the user to remove Stripe customer ID
		var updateErr error
		_, updateErr = userDAO.TryUpdate(ctx, user.Id, func(u *auth_entities.User) bool {
			// Log the current Stripe customer ID
			log.Info().
				Str("user_id", u.Id).
				Str("old_stripe_customer_id", u.StripeCustomerId).
				Msg("Removing Stripe customer ID")

			// Clear Stripe customer ID only
			u.StripeCustomerId = ""

			return true
		}, StripeIDCleanupRetry)

		duration := time.Since(startTime)

		if updateErr != nil {
			failedCount++
			log.Error().
				Err(updateErr).
				Str("tenant_id", tenantID).
				Str("user_id", user.Id).
				Str("email", user.Email).
				Dur("duration", duration).
				Msg("Failed to remove Stripe customer ID")
			continue
		}

		// Success
		processedCount++
		log.Info().
			Str("tenant_id", tenantID).
			Str("user_id", user.Id).
			Str("email", user.Email).
			Dur("duration", duration).
			Msg("Removed Stripe customer ID")
	}

	duration := time.Since(startTime)
	log.Info().
		Str("tenant_id", tenantID).
		Int("processed_count", processedCount).
		Int("skipped_count", skippedCount).
		Int("failed_count", failedCount).
		Int("total_count", totalCount).
		Dur("duration", duration).
		Bool("dry_run", j.dryRun).
		Msg("Completed removing Stripe customer IDs for tenant")

	return processedCount, failedCount, skippedCount, nil
}

// Helper function to safely get string value from optional string
func stringOrEmpty(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// StripeIDCleanupRetry implements exponential backoff for retrying operations
func StripeIDCleanupRetry(retry int) float64 {
	if retry > 0 {
		delay := 0.01 * (math.Pow(1.2, float64(retry)) + rand.Float64())
		log.Info().Msgf("retrying after %v seconds", delay)
		time.Sleep(time.Duration(delay * float64(time.Second)))
	}
	return float64(time.Now().Unix())
}

// stripeIDCleanup is the main entry point for the job
func stripeIDCleanup(ctx context.Context, userDAO *UserDAO, tenantIDs []string, dryRun bool) error {
	log.Info().
		Bool("dry_run", dryRun).
		Strs("tenant_ids", tenantIDs).
		Msg("Job configuration")

	if len(tenantIDs) == 0 {
		log.Warn().Msg("No tenant IDs specified, job will not process any users")
		return nil
	}

	// Safety check - if dry run is false, log a warning
	if !dryRun {
		log.Warn().
			Strs("tenant_ids", tenantIDs).
			Msg("Running in non-dry-run mode. Stripe customer IDs will be removed from users in specified tenants.")
	} else {
		log.Info().
			Msg("Running in dry-run mode - no changes will be made")
	}

	// Create and run the job
	job := NewStripeIDCleanupJob(userDAO, dryRun, tenantIDs)
	err := job.Run(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Job failed")
	}

	return err
}

local certLib = import 'deploy/common/cert-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

// This job is designed to run in central-staging or dev-xiaolei namespace
// It will delete Stripe customer IDs and subscription IDs from users in specific tenants
function(cloud, env, namespace, namespace_config)
  local appName = 'auth-central-stripe-id-wipe-job';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  // reuse the auth-central service account
  local serviceAccount = gcpLib.createServiceAccount(app='auth-central', cloud=cloud, env=env, namespace=namespace, iam=true);

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName='auth-central');

  local clientCert = certLib.createCentralClientCert(
    name='auth-central-client-cert',
    namespace=namespace,
    env=env,
    appName='auth-central',
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(serviceBaseName='auth-central-grpc', namespace=namespace),
  );

  local bigtable = gcpLib.getBigtableTable(cloud=cloud,
                                           env=env,
                                           namespace=namespace,
                                           tableName='auth-central');

  // Create a config map for the job configuration
  local configMap = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: appName + '-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJson({
        bigtable: {
          project_id: bigtable.projectId,
          instance_id: bigtable.instanceName,
          table_name: bigtable.tableName,
        },
        // Set default tenant IDs based on namespace
        tenants: if namespace == 'dev-xiaolei' then
          // In xiaolei's dev environment, target discovery0 and discovery1 tenant IDs
          ['24e9ae93899fb260116e52bda15a6d3f', 'b56c566a994b1d9d1be8ce1bc9f3f6d5']
        else if namespace == 'central-staging' then
          // In central-staging, use the specific staging tenant IDs
          ['7c287a5142174fed85794fb2fe2b44fc', '7f16c44dcaf340c9b67d08e9a2f00a97', '8d4e7a6b3c2f1a5908d7e6b5c4f3a2e1']
        else
          [],
        // Set dry run mode to false to make actual changes
        dry_run: false,
      }),
    },
  };


  // Create the job container
  local container = {
    name: appName,
    target: {
      name: '//services/auth/central/jobs/stripe_id_cleanup:stripe_id_cleanup_job_image',
      dst: appName,
    },
    args: [
      // Pass the config file path as a command-line argument
      '--config-file=/etc/config/config.json',
    ],
    env: lib.flatten([
      {
        name: 'POD_NAMESPACE',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.namespace',
          },
        },
      },
      {
        name: 'PROMETHEUS_MULTIPROC_DIR',
        value: '/tmp/prometheus_multiproc_dir',
      },
      telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
      dynamicFeatureFlags.env,
    ]),
    volumeMounts: [
      {
        name: 'prometheus-multiproc-dir',
        mountPath: '/tmp/prometheus_multiproc_dir',
      },
      {
        name: 'config-volume',
        mountPath: '/etc/config',
      },
      clientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 1,
        memory: '2Gi',
      },
    },
  };

  // Create the job pod
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    restartPolicy: 'Never',
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      clientCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      {
        name: 'prometheus-multiproc-dir',
        emptyDir: {},
      },
      {
        name: 'config-volume',
        configMap: {
          name: configMap.metadata.name,
        },
      },
    ],
  };

  // Create the job
  local job = {
    apiVersion: 'batch/v1',
    kind: 'Job',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
      },
    },
    spec: {
      backoffLimit: 3,
      ttlSecondsAfterFinished: 86400,  // 24 hours
      suspend: false,  // Job is not suspended by default
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  lib.flatten([
    configMap,
    job,
  ])

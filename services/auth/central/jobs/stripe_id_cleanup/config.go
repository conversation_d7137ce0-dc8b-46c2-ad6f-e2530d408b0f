package main

import (
	"encoding/json"
	"os"
	"path/filepath"

	"github.com/rs/zerolog/log"
)

// Config represents the configuration for the stripe_id_cleanup job
type Config struct {
	// BigTable configuration
	BigTable struct {
		ProjectID  string `json:"project_id"`
		InstanceID string `json:"instance_id"`
		TableName  string `json:"table_name"`
	} `json:"bigtable"`

	// List of tenant IDs to process
	Tenants []string `json:"tenants"`

	// Whether to run in dry run mode (no changes)
	DryRun bool `json:"dry_run"`
}

// ReadConfig reads the configuration from a file
func ReadConfig(path string) (*Config, error) {
	// Resolve the absolute path
	absPath, err := filepath.Abs(path)
	if err != nil {
		log.Error().Err(err).Str("path", path).Msg("Failed to resolve config file path")
		return nil, err
	}

	// Open the file
	file, err := os.Open(absPath)
	if err != nil {
		log.Error().Err(err).Str("path", absPath).Msg("Failed to open config file")
		return nil, err
	}
	defer file.Close()

	// Decode the JSON
	var config Config
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		log.Error().Err(err).Str("path", absPath).Msg("Failed to decode config file")
		return nil, err
	}

	// Log the configuration
	log.Info().
		Str("project_id", config.BigTable.ProjectID).
		Str("instance_id", config.BigTable.InstanceID).
		Str("table_name", config.BigTable.TableName).
		Strs("tenants", config.Tenants).
		Bool("dry_run", config.DryRun).
		Msg("Configuration loaded")

	return &config, nil
}

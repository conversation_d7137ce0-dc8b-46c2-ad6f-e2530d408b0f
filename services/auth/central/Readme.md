# Auth Service

The Auth service provides an endpoint for authorizing users to access Augment
according to RFC 6749 (https://datatracker.ietf.org/doc/html/rfc6749).


Auth central provides the following features:
 - Manages our user database
 - Authenticates users (currently via Auth0) and issues tokens
 - Verifies tokens and returns information
 - gRPC APIs to create, read, update, and revoke operations on users
 - support for self-serve sign-up

Auth central services is made up of a python front-end web server
and a backend gRPC server (written in Go). The UX of the login flows
is in the front-end web servers. Also, authentication
and issuing of tokens is done in the front end. Most of the user database
and token verification is in the backend.

Database operations are currently done in both the frontend and backend
but the intent is to migrate those to the backend service.

The frontend and backend communicate over a private port that is not
exposed outside the pod. This reduces the need for authentication and
encryption.

"""A Python client library to submit events to the request insight service."""

import logging
from typing import Optional

import grpc

import base.python.grpc.client_options as client_options
import services.auth.central.server.auth_pb2_grpc as auth_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> auth_pb2_grpc.AuthServiceStub:
    """Setup the client stub for the slack bot."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        logging.info(
            "No credentials provided, creating insecure channel to %s", endpoint
        )
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = auth_pb2_grpc.AuthServiceStub(channel)
    return stub

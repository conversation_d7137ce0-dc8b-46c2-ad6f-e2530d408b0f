package authclient

import (
	"context"
	"fmt"
	"iter"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
)

type TeamManagementClient interface {
	// GetSubscriptionIterator returns an iterator for retrieving subscriptions
	// using the Go iterator pattern with yield functions
	GetSubscriptionIterator(ctx context.Context, requestContext *requestcontext.RequestContext, batchSize uint32) iter.Seq2[*auth_entities.Subscription, error]

	// GetTenantSubscriptionMappingIterator returns an iterator for retrieving tenant subscription mappings
	// using the Go iterator pattern with yield functions
	GetTenantSubscriptionMappingIterator(ctx context.Context, requestContext *requestcontext.RequestContext, batchSize uint32) iter.Seq2[*auth_entities.TenantSubscriptionMapping, error]

	Close() error
}

type teamManagementClientImpl struct {
	conn   *grpc.ClientConn
	client pb.TeamManagementServiceClient
}

func NewTeamManagementClient(endpoint string, grpcOpts ...grpc.DialOption) (TeamManagementClient, error) {
	grpcOpts = append(grpcOpts, grpc.WithStatsHandler(otelgrpc.NewClientHandler()))
	conn, err := grpc.NewClient(endpoint, grpcOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to auth central service: %v", err)
	}

	client := pb.NewTeamManagementServiceClient(conn)
	return &teamManagementClientImpl{
		conn:   conn,
		client: client,
	}, nil
}

func (c *teamManagementClientImpl) Close() error {
	return c.conn.Close()
}

// paginatedIterator is a generic helper function that creates an iterator for paginated gRPC responses.
func paginatedIterator[T any, Req any, Resp any](
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	batchSize uint32,
	createRequest func(pageToken string, pageSize uint32) Req,
	callService func(context.Context, Req, ...grpc.CallOption) (Resp, error),
	getNextPageToken func(Resp) string,
	getItems func(Resp) []T,
) iter.Seq2[T, error] {
	if batchSize == 0 {
		batchSize = 1000 // Default batch size
	}

	return func(yield func(T, error) bool) {
		var pageToken string

		for {
			// Create a new context with the request context metadata
			ctxWithMetadata := metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

			// Create the request using the provided function
			request := createRequest(pageToken, batchSize)

			// Call the service using the provided function
			response, err := callService(ctxWithMetadata, request)
			if err != nil {
				// Yield the error and stop iteration
				var zero T
				yield(zero, err)
				return
			}

			// Update the page token for the next call
			pageToken = getNextPageToken(response)

			// Yield each item individually
			for _, item := range getItems(response) {
				if !yield(item, nil) {
					// If yield returns false, stop iteration
					return
				}
			}

			// If there are no more pages, exit the loop
			if getNextPageToken(response) == "" {
				break
			}
		}
	}
}

// GetSubscriptionIterator returns an iterator for retrieving subscriptions
// using the Go iterator pattern with yield functions
func (c *teamManagementClientImpl) GetSubscriptionIterator(
	ctx context.Context, requestContext *requestcontext.RequestContext, batchSize uint32,
) iter.Seq2[*auth_entities.Subscription, error] {
	return paginatedIterator(
		ctx,
		requestContext,
		batchSize,
		func(pageToken string, pageSize uint32) *pb.ListSubscriptionsRequest {
			return &pb.ListSubscriptionsRequest{
				PageToken: pageToken,
				PageSize:  pageSize,
			}
		},
		c.client.ListSubscriptions,
		func(resp *pb.ListSubscriptionsResponse) string {
			return resp.NextPageToken
		},
		func(resp *pb.ListSubscriptionsResponse) []*auth_entities.Subscription {
			return resp.Subscriptions
		},
	)
}

// GetTenantSubscriptionMappingIterator returns an iterator for retrieving tenant subscription mappings
// using the Go iterator pattern with yield functions
func (c *teamManagementClientImpl) GetTenantSubscriptionMappingIterator(
	ctx context.Context, requestContext *requestcontext.RequestContext, batchSize uint32,
) iter.Seq2[*auth_entities.TenantSubscriptionMapping, error] {
	return paginatedIterator(
		ctx,
		requestContext,
		batchSize,
		func(pageToken string, pageSize uint32) *pb.ListTenantSubscriptionMappingsRequest {
			return &pb.ListTenantSubscriptionMappingsRequest{
				PageToken: pageToken,
				PageSize:  pageSize,
			}
		},
		c.client.ListTenantSubscriptionMappings,
		func(resp *pb.ListTenantSubscriptionMappingsResponse) string {
			return resp.NextPageToken
		},
		func(resp *pb.ListTenantSubscriptionMappingsResponse) []*auth_entities.TenantSubscriptionMapping {
			return resp.TenantSubscriptionMappings
		},
	)
}

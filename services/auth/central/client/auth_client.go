package authclient

import (
	"context"
	"fmt"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
)

type AuthClient interface {
	ListTenantUsers(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string) ([]*auth_entities.User, error)
	RemoveUserFromTenant(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string) error
	CreateUserSuspension(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, suspensionType auth_entities.UserSuspensionType, evidence string) (suspensionID string, tokensDeleted int32, err error)
	GetUser(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID *string) (*auth_entities.User, error)
	GetUsers(ctx context.Context, requestContext *requestcontext.RequestContext, request *pb.GetUsersRequest) (*pb.GetUsersResponse, error)
	GetUserBillingInfo(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string) (*pb.GetUserBillingInfoResponse, error)
	UpdateUserBillingInfo(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, billingMethod *auth_entities.BillingMethod, orbCustomerID *string, orbSubscriptionID *string, stripeCustomerID *string) error
	Close() error
}

type authClientImpl struct {
	conn   *grpc.ClientConn
	client pb.AuthServiceClient
}

func New(endpoint string, grpcOpts ...grpc.DialOption) (AuthClient, error) {
	grpcOpts = append(grpcOpts, grpc.WithStatsHandler(otelgrpc.NewClientHandler()))
	conn, err := grpc.NewClient(endpoint, grpcOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to settings service: %v", err)
	}

	client := pb.NewAuthServiceClient(conn)
	return &authClientImpl{
		conn:   conn,
		client: client,
	}, nil
}

func (c *authClientImpl) Close() error {
	return c.conn.Close()
}

func (c *authClientImpl) ListTenantUsers(
	ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string,
) ([]*auth_entities.User, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	response, err := c.client.ListTenantUsers(ctx, &pb.ListTenantUsersRequest{TenantId: tenantID})
	if err != nil {
		return nil, err
	}
	return response.Users, nil
}

func (c *authClientImpl) RemoveUserFromTenant(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string,
) error {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	_, err := c.client.RemoveUserFromTenant(ctx, &pb.RemoveUserFromTenantRequest{UserId: userId, TenantId: tenantID})
	if err != nil {
		return err
	}
	return nil
}

func (c *authClientImpl) CreateUserSuspension(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, suspensionType auth_entities.UserSuspensionType, evidence string,
) (suspensionID string, tokensDeleted int32, err error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	response, err := c.client.CreateUserSuspension(ctx, &pb.CreateUserSuspensionRequest{
		UserId:         userId,
		TenantId:       tenantID,
		SuspensionType: suspensionType,
		Evidence:       evidence,
	})
	if err != nil {
		return "", 0, err
	}
	return response.SuspensionId, response.TokensDeleted, nil
}

func (c *authClientImpl) GetUser(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID *string,
) (*auth_entities.User, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	request := &pb.GetUserRequest{UserId: userId}
	if tenantID != nil {
		request.TenantId = tenantID
	}
	response, err := c.client.GetUser(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.User, nil
}

func (c *authClientImpl) GetUsers(
	ctx context.Context, requestContext *requestcontext.RequestContext, request *pb.GetUsersRequest,
) (*pb.GetUsersResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.GetUsers(ctx, request)
}

func (c *authClientImpl) GetUserBillingInfo(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string,
) (*pb.GetUserBillingInfoResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	response, err := c.client.GetUserBillingInfo(ctx, &pb.GetUserBillingInfoRequest{UserId: userId, TenantId: tenantID})
	if err != nil {
		return nil, err
	}
	return response, nil
}

func (c *authClientImpl) UpdateUserBillingInfo(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	userId string,
	tenantID string,
	billingMethod *auth_entities.BillingMethod,
	orbCustomerID *string,
	orbSubscriptionID *string,
	stripeCustomerID *string,
) error {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	_, err := c.client.UpdateUserBillingInfo(ctx, &pb.UpdateUserBillingInfoRequest{
		UserId:            userId,
		TenantId:          tenantID,
		BillingMethod:     billingMethod,
		OrbCustomerId:     orbCustomerID,
		OrbSubscriptionId: orbSubscriptionID,
		StripeCustomerId:  stripeCustomerID,
	})
	if err != nil {
		return err
	}
	return nil
}

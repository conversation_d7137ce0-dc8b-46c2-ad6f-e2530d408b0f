load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "auth_client_py",
    srcs = [
        "auth_client.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/auth/central/server:auth_py_proto",
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
    ],
)

go_library(
    name = "auth_client_go",
    srcs = [
        "auth_client.go",
    ],
    importpath = "github.com/augmentcode/augment/services/auth/central/auth_client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

go_library(
    name = "team_management_client_go",
    srcs = [
        "team_management_client.go",
    ],
    importpath = "github.com/augmentcode/augment/services/auth/central/team_management_client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

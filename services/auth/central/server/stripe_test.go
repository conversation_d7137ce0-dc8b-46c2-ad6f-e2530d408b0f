package main

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stripe/stripe-go/v80"
)

func TestMockStripeClient_CreateSetupIntent(t *testing.T) {
	// Create a new mock client
	client := NewMockStripeClient()

	// Test creating a setup intent
	customerID := "cus_test_customer"
	setupIntent, err := client.CreateSetupIntent(customerID)

	// Verify the result
	require.NoError(t, err, "CreateSetupIntent should not return an error")
	require.NotNil(t, setupIntent, "SetupIntent should not be nil")
	assert.Equal(t, "seti_new_1", setupIntent.ID, "SetupIntent ID should be generated correctly")
	assert.Equal(t, customerID, setupIntent.Customer.ID, "SetupIntent should be associated with the correct customer")
	assert.Equal(t, stripe.SetupIntentStatusRequiresPaymentMethod, setupIntent.Status, "SetupIntent should have the correct status")
	assert.Equal(t, stripe.SetupIntentUsageOffSession, setupIntent.Usage, "SetupIntent should have the correct usage")

	// Verify the setup intent was added to the client's state
	assert.Len(t, client.setupIntents, 1, "Client should have one setup intent")
	assert.Equal(t, setupIntent, client.setupIntents["seti_new_1"], "Setup intent should be stored in the client's state")

	// Create another setup intent for a different customer
	customerID2 := "cus_another_customer"
	setupIntent2, err := client.CreateSetupIntent(customerID2)

	// Verify the result
	require.NoError(t, err, "CreateSetupIntent should not return an error")
	require.NotNil(t, setupIntent2, "SetupIntent should not be nil")
	assert.Equal(t, "seti_new_2", setupIntent2.ID, "SetupIntent ID should be generated correctly")
	assert.Equal(t, customerID2, setupIntent2.Customer.ID, "SetupIntent should be associated with the correct customer")

	// Verify both setup intents are in the client's state
	assert.Len(t, client.setupIntents, 2, "Client should have two setup intents")
	assert.Equal(t, setupIntent, client.setupIntents["seti_new_1"], "First setup intent should still be in the client's state")
	assert.Equal(t, setupIntent2, client.setupIntents["seti_new_2"], "Second setup intent should be stored in the client's state")
}

// Note: We can't easily test the DefaultStripeClient.CreateSetupIntent method
// in a unit test because it makes real API calls to Stripe.
// In a real-world scenario, we would use a mock HTTP client or the Stripe
// testing mode to test this method. For now, we'll just test the mock client.

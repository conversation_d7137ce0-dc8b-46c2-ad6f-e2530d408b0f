package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"slices"
	"sync"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"

	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
)

var (
	messageReceivedCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_auth0_central_revoker_messages_received",
			Help: "Number of messages received",
		},
	)
	messageParseErrorCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_auth0_central_revoker_messages_parse_errors",
			Help: "Number of errors parsing messages",
		},
	)
	messageAcknowledgedCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_auth0_central_revoker_messages_acknowledged",
			Help: "Number of messages acknowledged",
		},
	)
	messageNotAcknowledgedCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_auth0_central_revoker_messages_not_acknowledged",
			Help: "Number of messages not acknowledged",
		},
	)
	panicCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_auth0_central_revoker_panics",
			Help: "Number of panics",
		},
	)
	revokeUserErrorCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_auth0_central_revoker_revoke_user_errors",
			Help: "Number of errors removing user",
		},
	)
	revokeUserTotalCounter = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_auth0_central_revoker_revoke_user_total",
			Help: "Total number of user removals",
		},
	)
	revokeUserDuration = prometheus.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "au_auth0_central_revoker_revoke_user_duration",
			Help:    "Duration of removing user",
			Buckets: prometheus.DefBuckets,
		},
	)
)

type Revoker struct {
	config       *Config
	daoFactory   *DAOFactory
	pubsubClient *pubsub.Client
	subscription *pubsub.Subscription
	stopChan     chan struct{}
	wg           sync.WaitGroup
}

func NewRevoker(ctx context.Context, config *Config, daoFactory *DAOFactory) (*Revoker, error) {
	prometheus.MustRegister(
		messageReceivedCounter,
		messageParseErrorCounter,
		messageAcknowledgedCounter,
		messageNotAcknowledgedCounter,
		revokeUserErrorCounter,
		revokeUserTotalCounter,
		revokeUserDuration,
	)

	pubsubClient, err := pubsub.NewClient(ctx, config.GCP.ProjectID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create pubsub client")
		return nil, err
	}
	subscription := pubsubClient.Subscription(config.RevokerConfig.SubscriptionName)

	// Initialize Revoker
	return &Revoker{
		config:       config,
		daoFactory:   daoFactory,
		pubsubClient: pubsubClient,
		subscription: subscription,
		stopChan:     make(chan struct{}),
	}, nil
}

func (r *Revoker) revokeUser(ctx context.Context, names []string) error {
	start := time.Now()

	tokenHashDAO := r.daoFactory.GetTokenHashDAO()
	userDAO := r.daoFactory.GetUserDAO()

	// TODO: stream processing
	hashesToDelete := make([]string, 0, 16)
	userIDs := make(map[string]struct{})

	err := tokenHashDAO.FindAll(ctx, func(token *auth_entities.TokenHash) bool {
		if slices.Contains(names, token.GetEmailAddress()) {
			hashesToDelete = append(hashesToDelete, token.GetHash())
			userIDs[token.GetAugmentUserId()] = struct{}{}
		}
		return true
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to read TokenHash entries")
		return fmt.Errorf("failed to read TokenHash entries: %v", err)
	}

	log.Info().Msgf("Found %d tokens in %s", len(hashesToDelete), time.Since(start))

	if len(hashesToDelete) == 0 {
		return nil
	}

	log.Info().Msg("Deleting tokens")

	start = time.Now()

	err = tokenHashDAO.BatchDelete(ctx, hashesToDelete)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete rows")
		return fmt.Errorf("failed to delete rows: %v", err)
	}

	log.Info().Msgf("Deleted all tokens in %s", time.Since(start))

	// Update nonce for all users
	for userID := range userIDs {
		err = userDAO.UpdateNonce(ctx, userID)
		if err != nil {
			log.Error().Err(err).Msg("Failed to update user nonce, ")
		}
	}

	return nil
}

type SsimEvent struct {
	IdpId    string
	Auth0Id  string
	UserName string
	Email    string
	Active   bool
}

// parseSsimEvent parses an SSIM event from the AUth0 logs and returns a SsimEvent struct
//
// Details: currents only parses create_user and update_user actions. Probably should
// parse delete_user too but okta doesn't send those. Azure AD docs say that it does
// send them.
//
// Returns nil for unsupported actions.
//
// Returns error if fields needed to do parsing are missing. Empty userName and
// primary email are not considered errors.
func parseSsimEvent(object map[string]interface{}) (*SsimEvent, error) {
	if object["type"].(string) != "sscim" {
		return nil, fmt.Errorf("sscim event type is not sscim")
	}

	details, _ := object["details"].(map[string]interface{})
	if details == nil {
		return nil, fmt.Errorf("sscim event missing details: %v", object)
	}

	action, _ := details["action"].(string)
	if !slices.Contains([]string{"create_user", "update_user"}, action) {
		log.Info().Msgf("Skipping action: %s", action)
		return nil, nil
	} else {
		log.Info().Msgf("Processing action: %s", action)
	}

	response, _ := details["response"].(map[string]interface{})
	if response == nil {
		return nil, fmt.Errorf("sscim event action %s missing response: %v", action, object)
	}

	responseBody, _ := response["body"].(map[string]interface{})
	if responseBody == nil {
		return nil, fmt.Errorf("sscim event action %s response missing body", action)
	}

	userName, _ := responseBody["userName"].(string)
	emails, ok := responseBody["emails"].([]map[string]interface{})
	email := ""
	if ok {
		for _, emailMap := range emails {
			if emailMap["primary"].(bool) {
				email = emailMap["value"].(string)
				break
			}
		}
	}
	active := true
	if v, ok := responseBody["active"]; ok {
		if v2, ok := v.(bool); ok {
			active = v2
		} else {
			return nil, fmt.Errorf("sscim event action %s response body active is not a bool", action)
		}
	} else {
		return nil, fmt.Errorf("sscim event action %s response body missing active", action)
	}

	auth0Id, _ := responseBody["id"].(string)
	idpId, _ := responseBody["externalId"].(string)

	return &SsimEvent{
		IdpId:    idpId,
		Auth0Id:  auth0Id,
		UserName: userName,
		Email:    email,
		Active:   active,
	}, nil
}

func (r *Revoker) receiveAndProcess(ctx context.Context) error {
	return r.subscription.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		messageReceivedCounter.Inc()

		dontAck := false
		parseErrorsSeen := false

		log.Info().Msgf("Received message %s", msg.ID)

		// Iterate over the lines of the message
		scanner := bufio.NewScanner(bytes.NewReader(msg.Data))
		for lineNumber := 1; scanner.Scan(); lineNumber++ {
			textLine := scanner.Text()
			// Try to json decode the line
			var jsonLine interface{}
			err := json.Unmarshal([]byte(textLine), &jsonLine)
			if err != nil {
				parseErrorsSeen = true
				log.Warn().Err(err).Msgf("Failed to parse line %d of msg %s", lineNumber, msg.ID)
				continue
			}

			// If the line is not a map, skip it
			jsonLineMap, _ := jsonLine.(map[string]interface{})
			if jsonLineMap == nil {
				parseErrorsSeen = true
				log.Warn().Msgf("Line %d of msg %s is not a map", lineNumber, msg.ID)
				continue
			}

			logId, _ := jsonLineMap["log_id"].(string)
			log.Info().Msgf("Processing auth0 log_id: %s", logId)

			jsonLineMap, _ = jsonLineMap["data"].(map[string]interface{})
			if jsonLineMap == nil {
				parseErrorsSeen = true
				log.Warn().Msgf("Line %d of msg %s is missing data", lineNumber, msg.ID)
				continue
			}

			if jsonLineMap["type"] == "sscim" {
				ssimEvent, err := parseSsimEvent(jsonLineMap)
				if err != nil {
					parseErrorsSeen = true
					log.Warn().Err(err).Msgf("Failed to parse SSIM event")
					continue
				}

				if ssimEvent != nil && !ssimEvent.Active {
					names := make([]string, 2)
					if ssimEvent.UserName != "" {
						names = append(names, ssimEvent.UserName)
					}
					if ssimEvent.Email != "" {
						names = append(names, ssimEvent.Email)
					}
					if len(names) == 0 {
						parseErrorsSeen = true
						log.Warn().Msgf("SSIM event missing user name and email")
						continue
					}

					start := time.Now()
					err = r.revokeUser(ctx, names)
					revokeUserDuration.Observe(time.Since(start).Seconds())
					revokeUserTotalCounter.Inc()

					if err != nil {
						dontAck = true
						revokeUserErrorCounter.Inc()
						log.Warn().Err(err).Msg("Failed to remove user")
						continue
					}
				}
			}
		}

		if err := scanner.Err(); err != nil {
			log.Warn().Err(err).Msg("Error reading message")
			parseErrorsSeen = true
		}

		// We will still ACK on parse errors
		if parseErrorsSeen {
			messageParseErrorCounter.Inc()
		}

		if dontAck {
			msg.Nack()
			messageNotAcknowledgedCounter.Inc()
		} else {
			msg.Ack()
			messageAcknowledgedCounter.Inc()
		}
	})
}

func (r *Revoker) runWithPanicRecovery(ctx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			log.Error().Interface("panic", r).Msg("Recovered from panic in Revoker.Run")
			panicCounter.Inc()
		}
	}()

	return r.receiveAndProcess(ctx)
}

func (r *Revoker) handleError(ctx context.Context, err error, backoff *time.Duration, maxBackoff time.Duration) error {
	log.Error().Err(err).Msgf("Failed to receive messages, retrying in %v", *backoff)

	select {
	case <-time.After(*backoff):
		// Exponential backoff with a maximum limit
		*backoff = time.Duration(math.Min(float64(*backoff*2), float64(maxBackoff)))
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

func (r *Revoker) Run(ctx context.Context) error {
	r.wg.Add(1)
	defer r.wg.Done()

	backoff := time.Second
	maxBackoff := 5 * time.Minute
	for {
		select {
		case <-ctx.Done():
			log.Info().Err(ctx.Err()).Msg("Context cancelled during backoff, stopping Revoker")
			return ctx.Err()
		default:
			if err := r.runWithPanicRecovery(ctx); err != nil {
				if err := r.handleError(ctx, err, &backoff, maxBackoff); err != nil {
					return err
				}
			} else {
				backoff = time.Second
			}
		}
	}
}

func (r *Revoker) Stop() {
	close(r.stopChan)
	r.wg.Wait()
	if r.pubsubClient != nil {
		r.pubsubClient.Close()
	}
}

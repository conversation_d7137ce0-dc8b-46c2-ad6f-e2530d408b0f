package main

import (
	"time"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// createTestSubscription creates a subscription with default values that can be overridden
func createTestSubscription(overrides map[string]interface{}) *auth_entities.Subscription {
	now := time.Now()

	// Default values
	subscription := &auth_entities.Subscription{
		SubscriptionId:    "sub_test",
		StripeCustomerId:  "cus_test",
		PriceId:           "price_professional",
		Status:            auth_entities.Subscription_ACTIVE,
		Seats:             1,
		StartDate:         timestamppb.New(now),
		TrialEnd:          nil, // No trial by default
		EndDate:           timestamppb.New(now.Add(30 * 24 * time.Hour)),
		CancelAtPeriodEnd: false,
		HasPaymentMethod:  true,
		CreatedAt:         timestamppb.New(now),
		UpdatedAt:         timestamppb.New(now),
	}

	// Apply overrides
	for key, value := range overrides {
		switch key {
		case "SubscriptionId":
			subscription.SubscriptionId = value.(string)
		case "StripeCustomerId":
			subscription.StripeCustomerId = value.(string)
		case "PriceId":
			subscription.PriceId = value.(string)
		case "Status":
			subscription.Status = value.(auth_entities.Subscription_Status)
		case "Seats":
			subscription.Seats = value.(int32)
		case "StartDate":
			subscription.StartDate = value.(*timestamppb.Timestamp)
		case "TrialEnd":
			subscription.TrialEnd = value.(*timestamppb.Timestamp)
		case "EndDate":
			subscription.EndDate = value.(*timestamppb.Timestamp)
		case "CancelAtPeriodEnd":
			subscription.CancelAtPeriodEnd = value.(bool)
		case "HasPaymentMethod":
			subscription.HasPaymentMethod = value.(bool)
		case "CreatedAt":
			subscription.CreatedAt = value.(*timestamppb.Timestamp)
		case "UpdatedAt":
			subscription.UpdatedAt = value.(*timestamppb.Timestamp)
		case "Owner":
			// Handle the oneof field correctly based on the actual type
			switch ownerValue := value.(type) {
			case *auth_entities.Subscription_UserId:
				subscription.Owner = ownerValue
			case *auth_entities.Subscription_TenantId:
				subscription.Owner = ownerValue
			default:
				panic("Invalid owner type provided")
			}
		default:
			panic("Invalid key provided")
		}
	}

	return subscription
}

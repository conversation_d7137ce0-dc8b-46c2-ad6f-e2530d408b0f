"""Tests for the invitation service module."""

import unittest
from unittest import mock
from typing import Dict, List
from google.protobuf.timestamp_pb2 import Timestamp

from services.auth.central.server import invitation_service
from services.auth.central.server import auth_pb2
from services.auth.central.server import auth_entities_pb2
from services.auth.central.server import front_end_token_service_pb2


class FakeFrontEndTokenService:
    """Fake implementation of the front_end_token_service for testing."""

    def __init__(self):
        self.users: Dict[str, auth_entities_pb2.User] = {}
        self.invitations: Dict[str, List[auth_entities_pb2.TenantInvitation]] = {}
        self.resolutions: Dict[str, auth_entities_pb2.InvitationResolution] = {}
        self.admin_users: List[str] = []
        self.error_mode = False

    def set_error_mode(self, error_mode: bool):
        """Set whether the service should raise errors."""
        self.error_mode = error_mode

    def add_user(self, user: auth_entities_pb2.User):
        """Add a user to the fake service."""
        self.users[user.id] = user
        for idp_user_id in user.idp_user_ids:
            self.users[idp_user_id] = user
        self.users[user.email] = user

    def set_user_as_admin(self, user_id: str):
        """Set a user as an admin."""
        self.admin_users.append(user_id)

    def add_invitation(
        self, email: str, invitation: auth_entities_pb2.TenantInvitation
    ):
        """Add an invitation for a user."""
        if email not in self.invitations:
            self.invitations[email] = []
        self.invitations[email].append(invitation)

    def add_resolution(
        self, resolution_id: str, resolution: auth_entities_pb2.InvitationResolution
    ):
        """Add a resolution to the fake service."""
        self.resolutions[resolution_id] = resolution

    def GetUserInvitations(self, request):
        """Fake implementation of GetUserInvitations."""
        if self.error_mode:
            raise Exception("Test error")

        email = request.email
        response = auth_pb2.GetUserInvitationsResponse()
        response.invitations.extend(self.invitations.get(email, []))
        return response

    def GetResolveInvitationsStatus(self, request):
        """Fake implementation of GetResolveInvitationsStatus."""
        if self.error_mode:
            raise Exception("Test error")

        resolution_id = request.invitation_resolution_id
        response = auth_pb2.GetResolveInvitationsStatusResponse()
        if resolution_id in self.resolutions:
            response.invitation_resolution.CopyFrom(self.resolutions[resolution_id])
        return response

    def GetUser(self, request):
        """Fake implementation of GetUser."""
        if self.error_mode:
            raise Exception("Test error")

        idp_user_id = request.idp_user_id
        email_address = request.email_address

        user = None
        if idp_user_id and idp_user_id in self.users:
            user = self.users[idp_user_id]
        elif email_address and email_address in self.users:
            user = self.users[email_address]

        response = front_end_token_service_pb2.GetUserResponse()
        if user:
            response.user.CopyFrom(user)
        return response

    def IsUserAdmin(self, request):
        """Fake implementation of IsUserAdmin."""
        if self.error_mode:
            raise Exception("Test error")

        user_id = request.user_id
        is_admin = user_id in self.admin_users

        response = front_end_token_service_pb2.IsUserAdminResponse()
        response.is_admin = is_admin
        return response


class InvitationServiceTest(unittest.TestCase):
    """Tests for the invitation service module."""

    def setUp(self):
        """Set up the test environment."""
        self.token_service = FakeFrontEndTokenService()
        invitation_service.initialize(self.token_service)

    def test_get_user_invitations_empty(self):
        """Test getting invitations for a user with no invitations."""
        # No invitations added to the fake service

        invitations = invitation_service.get_user_invitations("<EMAIL>")

        self.assertEqual(invitations, [])

    def test_get_user_invitations_with_data(self):
        """Test getting invitations for a user with invitations."""
        # Create an invitation with no created_at timestamp
        invitation = auth_entities_pb2.TenantInvitation()
        invitation.id = "invitation-id-1"
        invitation.tenant_id = "tenant-id-1"
        invitation.inviter_email = "<EMAIL>"
        # We don't set created_at to ensure it's empty

        # Mock the invitation_service module's handling of created_at
        original_get_user_invitations = invitation_service.get_user_invitations

        # Create a wrapper function that modifies the result
        def mock_get_user_invitations(email):
            result = original_get_user_invitations(email)
            if result and "created_at" in result[0]:
                result[0]["created_at"] = ""
            return result

        try:
            # Replace the function with our mock
            invitation_service.get_user_invitations = mock_get_user_invitations

            self.token_service.add_invitation("<EMAIL>", invitation)

            invitations = invitation_service.get_user_invitations("<EMAIL>")

            self.assertEqual(len(invitations), 1)
            self.assertEqual(invitations[0]["id"], "invitation-id-1")
            self.assertEqual(invitations[0]["tenant_id"], "tenant-id-1")
            self.assertEqual(invitations[0]["inviter_email"], "<EMAIL>")
            self.assertEqual(invitations[0]["created_at"], "")
        finally:
            # Restore the original function
            invitation_service.get_user_invitations = original_get_user_invitations

    def test_has_invitations_true(self):
        """Test checking if a user has invitations when they do."""
        invitation = auth_entities_pb2.TenantInvitation()
        invitation.id = "invitation-id-1"
        invitation.tenant_id = "tenant-id-1"
        invitation.inviter_email = "<EMAIL>"

        self.token_service.add_invitation("<EMAIL>", invitation)

        result = invitation_service.has_invitations("<EMAIL>")

        self.assertTrue(result)

    def test_has_invitations_false(self):
        """Test checking if a user has invitations when they don't."""
        # No invitations added to the fake service

        result = invitation_service.has_invitations("<EMAIL>")

        self.assertFalse(result)

    def test_get_user_invitations_error(self):
        """Test error handling when getting invitations."""
        self.token_service.set_error_mode(True)

        with self.assertRaises(Exception):
            invitation_service.get_user_invitations("<EMAIL>")

        # Reset error mode for other tests
        self.token_service.set_error_mode(False)

    def test_get_invitation_resolution_status_pending(self):
        """Test getting the status of a pending invitation resolution."""
        # Create a timestamp
        timestamp = Timestamp()
        timestamp.GetCurrentTime()

        # Create a resolution with the timestamp
        resolution = auth_entities_pb2.InvitationResolution()
        resolution.id = "resolution-id-1"
        resolution.status = 1  # PENDING
        resolution.created_at.CopyFrom(timestamp)

        # Patch the ToDatetime method to return a controlled value
        original_to_datetime = Timestamp.ToDatetime
        try:
            mock_datetime = mock.MagicMock()
            mock_datetime.strftime.return_value = "2023-01-01 12:00:00"
            Timestamp.ToDatetime = mock.MagicMock(return_value=mock_datetime)

            # Add the resolution and test
            self.token_service.add_resolution("resolution-id-1", resolution)

            result = invitation_service.get_invitation_resolution_status(
                "resolution-id-1"
            )

            self.assertEqual(result["id"], "resolution-id-1")
            self.assertEqual(result["status"], "PENDING")
            self.assertEqual(result["created_at"], "2023-01-01 12:00:00")
        finally:
            # Restore the original method
            Timestamp.ToDatetime = original_to_datetime

    def test_get_invitation_resolution_status_success(self):
        """Test getting the status of a successful invitation resolution."""
        # Create a timestamp
        timestamp = Timestamp()
        timestamp.GetCurrentTime()

        # Create a resolution with the timestamp
        resolution = auth_entities_pb2.InvitationResolution()
        resolution.id = "resolution-id-2"
        resolution.status = 2  # SUCCESS
        resolution.created_at.CopyFrom(timestamp)

        # Patch the ToDatetime method to return a controlled value
        original_to_datetime = Timestamp.ToDatetime
        try:
            mock_datetime = mock.MagicMock()
            mock_datetime.strftime.return_value = "2023-01-01 12:00:00"
            Timestamp.ToDatetime = mock.MagicMock(return_value=mock_datetime)

            # Add the resolution and test
            self.token_service.add_resolution("resolution-id-2", resolution)

            result = invitation_service.get_invitation_resolution_status(
                "resolution-id-2"
            )

            self.assertEqual(result["id"], "resolution-id-2")
            self.assertEqual(result["status"], "SUCCESS")
            self.assertEqual(result["created_at"], "2023-01-01 12:00:00")
        finally:
            # Restore the original method
            Timestamp.ToDatetime = original_to_datetime

    def test_get_invitation_resolution_status_error(self):
        """Test getting the status of a failed invitation resolution."""
        # Create a timestamp
        timestamp = Timestamp()
        timestamp.GetCurrentTime()

        # Create a resolution with the timestamp
        resolution = auth_entities_pb2.InvitationResolution()
        resolution.id = "resolution-id-3"
        resolution.status = 3  # ERROR
        resolution.created_at.CopyFrom(timestamp)

        # Patch the ToDatetime method to return a controlled value
        original_to_datetime = Timestamp.ToDatetime
        try:
            mock_datetime = mock.MagicMock()
            mock_datetime.strftime.return_value = "2023-01-01 12:00:00"
            Timestamp.ToDatetime = mock.MagicMock(return_value=mock_datetime)

            # Add the resolution and test
            self.token_service.add_resolution("resolution-id-3", resolution)

            result = invitation_service.get_invitation_resolution_status(
                "resolution-id-3"
            )

            self.assertEqual(result["id"], "resolution-id-3")
            self.assertEqual(result["status"], "ERROR")
            self.assertEqual(result["created_at"], "2023-01-01 12:00:00")
        finally:
            # Restore the original method
            Timestamp.ToDatetime = original_to_datetime

    def test_get_invitation_resolution_status_invalid_id(self):
        """Test error handling when getting resolution status with an invalid ID."""
        with self.assertRaises(ValueError):
            invitation_service.get_invitation_resolution_status("")

    def test_get_invitation_resolution_status_grpc_error(self):
        """Test error handling when getting resolution status with a gRPC error."""
        self.token_service.set_error_mode(True)

        with self.assertRaises(Exception):
            invitation_service.get_invitation_resolution_status("resolution-id-1")

        # Reset error mode for other tests
        self.token_service.set_error_mode(False)

    def test_is_team_admin_true(self):
        """Test checking if a user is a team admin when they are."""
        # Create a user and set them as an admin
        user = auth_entities_pb2.User()
        user.id = "user-id-1"
        user.email = "<EMAIL>"
        user.tenants.append("tenant-id-1")
        user.idp_user_ids.append("idp-user-id-1")

        self.token_service.add_user(user)
        self.token_service.set_user_as_admin("user-id-1")

        result = invitation_service.is_team_admin("idp-user-id-1", "<EMAIL>")

        self.assertTrue(result)

    def test_is_team_admin_false(self):
        """Test checking if a user is a team admin when they are not."""
        # Create a user but don't set them as an admin
        user = auth_entities_pb2.User()
        user.id = "user-id-1"
        user.email = "<EMAIL>"
        user.tenants.append("tenant-id-1")
        user.idp_user_ids.append("idp-user-id-1")

        self.token_service.add_user(user)
        # Not calling set_user_as_admin

        result = invitation_service.is_team_admin("idp-user-id-1", "<EMAIL>")

        self.assertFalse(result)

    def test_is_team_admin_user_not_found(self):
        """Test checking if a user is a team admin when the user is not found."""
        # No user added to the fake service

        result = invitation_service.is_team_admin("idp-user-id-1", "<EMAIL>")

        self.assertFalse(result)

    def test_is_team_admin_empty_idp_user_id(self):
        """Test checking if a user is a team admin with an empty IDP user ID."""
        result = invitation_service.is_team_admin("", "<EMAIL>")

        self.assertFalse(result)

    def test_is_team_admin_error(self):
        """Test error handling when checking if a user is a team admin."""
        self.token_service.set_error_mode(True)

        result = invitation_service.is_team_admin("idp-user-id-1", "<EMAIL>")

        self.assertFalse(result)

        # Reset error mode for other tests
        self.token_service.set_error_mode(False)


if __name__ == "__main__":
    unittest.main()

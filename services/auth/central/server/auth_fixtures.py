"""Auth fixtures."""

import os
import uuid
import pytest

import base.test_utils.bigtable_emulator
from base.test_utils.pubsub import pubsub_emulator as pubsub_emulator_process_runner
from services.auth.central.server import bigtable_connector
from services.tenant_watcher import tenant_watcher_pb2
from google.cloud import pubsub_v1

INSTANCE_ID = "test-instance"
TABLE_NAME = "test-table"
PROJECT_ID = "test-project"
TENANT_NAME = "test-tenant1"
ASYNC_OPS_TOPIC = "test-async-ops-topic"
ASYNC_OPS_SUBSCRIPTION = "test-async-ops-sub"


@pytest.fixture(scope="module")
def bigtable_emulator():
    """Fixture to start a Bigtable emulator."""
    yield from base.test_utils.bigtable_emulator.start_emulator()


@pytest.fixture(scope="module")
def bigtable_table(bigtable_emulator):
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator

    bigtable_connector.setup_table(
        instance_id=INSTANCE_ID, table_name=TABLE_NAME, project_id=PROJECT_ID
    )
    table = bigtable_connector.connect(
        instance_id=INSTANCE_ID, table_name=TABLE_NAME, project_id=PROJECT_ID
    )
    yield table


@pytest.fixture(scope="function")
def clean_bigtable_table(bigtable_emulator):
    """Fixture for tests that need a clean table state."""
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator
    unique_table = f"{TABLE_NAME}_{uuid.uuid4().hex[:8]}"
    bigtable_connector.setup_table(instance_id=INSTANCE_ID, table_name=unique_table)
    table = bigtable_connector.connect(instance_id=INSTANCE_ID, table_name=unique_table)
    yield table


@pytest.fixture(scope="module")
def pubsub_emulator():
    """Fixture to start a PubSub emulator."""
    yield from pubsub_emulator_process_runner.start_emulator()


@pytest.fixture(scope="module")
def async_ops_pubsub_topic(pubsub_emulator):
    """Fixture to create a PubSub topic for async ops."""
    os.environ["PUBSUB_EMULATOR_HOST"] = pubsub_emulator
    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(PROJECT_ID, ASYNC_OPS_TOPIC)
    publisher.create_topic(request={"name": topic_path})
    return topic_path


@pytest.fixture(scope="module")
def async_ops_pubsub_subscription(pubsub_emulator, async_ops_pubsub_topic):
    """Fixture to create a PubSub subscription for async ops."""
    os.environ["PUBSUB_EMULATOR_HOST"] = pubsub_emulator
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(PROJECT_ID, ASYNC_OPS_SUBSCRIPTION)
    subscriber.create_subscription(
        request={"name": subscription_path, "topic": async_ops_pubsub_topic}
    )
    return subscription_path

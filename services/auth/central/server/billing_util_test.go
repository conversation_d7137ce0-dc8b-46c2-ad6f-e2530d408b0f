package main

import (
	"context"
	"testing"
	"time"

	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestCalculateProRatedCredits tests the CalculateProRatedCredits function
func TestCalculateProRatedCredits(t *testing.T) {
	// Test cases
	testCases := []struct {
		name                        string
		communityCreditsPerMonth    float64
		professionalCreditsPerMonth float64
		billingStart                time.Time
		billingEnd                  time.Time
		upgradeTime                 time.Time
		expectedCredits             float64
		expectError                 bool
	}{
		{
			name:                        "Full billing period remaining",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			expectedCredits:             200, // 100% of credits (200)
			expectError:                 false,
		},
		{
			name:                        "Half billing period remaining",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 16, 0, 0, 0, 0, time.UTC),
			expectedCredits:             152, // 50% of community + 50% of professional
			expectError:                 false,
		},
		{
			name:                        "Quarter billing period remaining",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 24, 0, 0, 0, 0, time.UTC),
			expectedCredits:             126, // 25% of community + 75% of professional
			expectError:                 false,
		},
		{
			name:                        "No billing period remaining",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			expectedCredits:             100, // all community
			expectError:                 false,
		},
		{
			name:                        "Professional credits less than community",
			communityCreditsPerMonth:    200,
			professionalCreditsPerMonth: 100,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 16, 0, 0, 0, 0, time.UTC),
			expectedCredits:             0,
			expectError:                 true,
		},
		{
			name:                        "Upgrade time before billing start",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2022, 12, 15, 0, 0, 0, 0, time.UTC),
			expectedCredits:             200, // Clamped to 100% of credits
			expectError:                 false,
		},
		{
			name:                        "Upgrade time after billing end",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 2, 15, 0, 0, 0, 0, time.UTC),
			expectedCredits:             100, // All community
			expectError:                 false,
		},
		{
			name:                        "Fractional credits with rounding",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 20, 12, 0, 0, 0, time.UTC), // 11.5 days left out of 31
			expectedCredits:             139,
			expectError:                 false,
		},
		{
			name:                        "First day of billing period",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC), // 12:00 on the first day, get full amount
			expectedCredits:             200,
			expectError:                 false,
		},
		{
			name:                        "First day of billing period, not a round day",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC), // 10:00 am, not a round time
			billingEnd:                  time.Date(2023, 2, 1, 10, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 1, 23, 59, 59, 0, time.UTC), // 11:59:59 on the first day, get full amount
			expectedCredits:             200,
			expectError:                 false,
		},
		{
			name:                        "Last day of billing period",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 31, 12, 0, 0, 0, time.UTC), // on the last day, get credits for the last day
			expectedCredits:             104,                                           // 1/30 additional
			expectError:                 false,
		},
		{
			name:                        "Different time zone for billing and upgrade",
			communityCreditsPerMonth:    100,
			professionalCreditsPerMonth: 200,
			billingStart:                time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			billingEnd:                  time.Date(2023, 2, 1, 0, 0, 0, 0, time.UTC),
			upgradeTime:                 time.Date(2023, 1, 2, 0, 0, 0, 0, time.FixedZone("+8", +8*60*60)), // Jan 2nd in +8, but Jan 1st in UTC
			expectedCredits:             200,                                                               // still get 200 since it is the first day in UTC
			expectError:                 false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			credits, err := CalculateProRatedCredits(
				tc.communityCreditsPerMonth,
				tc.professionalCreditsPerMonth,
				tc.billingStart,
				tc.billingEnd,
				tc.upgradeTime,
			)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// Use a small delta for floating point comparison
				assert.InDelta(t, tc.expectedCredits, credits, 0.01, "Pro-rated credits should match expected value")
			}
		})
	}
}

// TestIsPlanUpgrade tests the IsPlanUpgrade function
func TestIsPlanUpgrade(t *testing.T) {
	ctx := context.Background()

	testCases := []struct {
		name              string
		currentPlanInfo   *orb.OrbPlanInfo
		targetPlanInfo    *orb.OrbPlanInfo
		expectedIsUpgrade bool
		expectError       bool
		errorMessage      string
	}{
		{
			name: "Upgrade - Developer to Professional",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: true,
			expectError:       false,
		},
		{
			name: "Upgrade - Professional to Max",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_max_plan",
				PricePerSeat:   "300.00",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: true,
			expectError:       false,
		},
		{
			name: "Downgrade - Professional to Developer",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: false,
			expectError:       false,
		},
		{
			name: "Same price - no upgrade",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan_v2",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: false,
			expectError:       false,
		},
		{
			name: "Missing SeatsPriceID in current plan",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "", // Missing
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: false,
			expectError:       false,
		},
		{
			name: "Missing SeatsPriceID in target plan",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "", // Missing
			},
			expectedIsUpgrade: false,
			expectError:       false,
		},
		{
			name:            "Nil current plan",
			currentPlanInfo: nil,
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: false,
			expectError:       true,
			errorMessage:      "current plan info or target plan info is nil",
		},
		{
			name: "Nil target plan",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo:    nil,
			expectedIsUpgrade: false,
			expectError:       true,
			errorMessage:      "current plan info or target plan info is nil",
		},
		{
			name: "Invalid current plan price",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "invalid",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: false,
			expectError:       true,
			errorMessage:      "failed to parse current plan seats price",
		},
		{
			name: "Invalid target plan price",
			currentPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			},
			targetPlanInfo: &orb.OrbPlanInfo{
				ExternalPlanID: "orb_professional_plan",
				PricePerSeat:   "invalid",
				SeatsPriceID:   "seats-price-id",
			},
			expectedIsUpgrade: false,
			expectError:       true,
			errorMessage:      "failed to parse target plan seats price",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			isUpgrade, err := IsPlanUpgrade(ctx, tc.currentPlanInfo, tc.targetPlanInfo)

			if tc.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMessage)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tc.expectedIsUpgrade, isUpgrade)
			}
		})
	}
}

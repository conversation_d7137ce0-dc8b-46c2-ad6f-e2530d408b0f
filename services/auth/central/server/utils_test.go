package main

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNormalizeEmail(t *testing.T) {
	t.Run("valid emails", func(t *testing.T) {
		tests := []struct {
			name     string
			email    string
			expected string
		}{
			{
				name:     "no change",
				email:    "<EMAIL>",
				expected: "<EMAIL>",
			},
			{
				name:     "lowercase",
				email:    "<EMAIL>",
				expected: "<EMAIL>",
			},
			{
				name:     "trim",
				email:    " <EMAIL> ",
				expected: "<EMAIL>",
			},
			{
				name:     "leave in + and .",
				email:    "<EMAIL>",
				expected: "<EMAIL>",
			},
		}

		for _, test := range tests {
			t.Run(test.name, func(t *testing.T) {
				normalized, err := normalizeEmail(test.email)
				require.NoError(t, err)
				assert.Equal(t, test.expected, normalized)
			})
		}
	})

	t.Run("invalid emails", func(t *testing.T) {
		tests := []struct {
			name  string
			email string
		}{
			{
				name:  "no @",
				email: "foo",
			},
			{
				name:  "multiple @s",
				email: "foo@<EMAIL>",
			},
			{
				name:  "whitespace",
				email: "foo @bar.com",
			},
		}

		for _, test := range tests {
			t.Run(test.name, func(t *testing.T) {
				normalizeEmail(test.email)
			})
		}
	})
}

syntax = "proto3";
package auth_internal;

import "google/protobuf/timestamp.proto";
import "services/auth/central/server/auth.proto";
import "services/auth/central/server/auth_entities.proto";
import "services/tenant_watcher/tenant_watcher.proto";

option go_package = "github.com/augmentcode/augment/services/auth/central/server/auth_internal";

// change of a user from one plan to a new plan
// this might or might not involve a tier change.
message UserTierChangeMessage {
  auth_entities.User user = 1;
  services.tenant.Tenant current_tenant = 2;
  services.tenant.Tenant new_tenant = 3;
  auth_entities.UserTier new_tier = 4;
  string tier_change_id = 5;
  google.protobuf.Timestamp publish_time = 6;

  // The new plan name to subscribe the user to
  //
  // Plan change has to make to a plan currently configured in Orb.
  string new_plan_id = 7;
}

message TeamPlanChangeMessage {
  string team_tenant_id = 1;
  string target_orb_plan_id = 2;
  string plan_change_id = 3;
  google.protobuf.Timestamp publish_time = 4;
  // The ID of the admin user who initiated the plan change.
  // Used for audit logging and tracking who made the change.
  string initiated_by_user_id = 5;
}

message CreateTenantForTeamMessage {
  string tenant_creation_id = 1;
  auth.CreateTenantForTeamRequest tenant_creation_request = 2;
  google.protobuf.Timestamp publish_time = 3;
}

message ResolveInvitationsMessage {
  string invitation_resolution_id = 1;
  auth.ResolveInvitationsRequest resolve_invitations_request = 2;
  google.protobuf.Timestamp publish_time = 3;
}

message CreateSubscriptionMessage {
  string id = 1;
  string user_id = 2;
  string tenant_id = 3;
  google.protobuf.Timestamp publish_time = 4;
}

message SendInvitationEmailMessage {
  string tenant_id = 1;
  string invitation_id = 2;
  string invitee_email = 3 [debug_redact = true];
  google.protobuf.Timestamp publish_time = 4;
}

message UpdateSubscriptionMessage {
  string id = 1;
  string subscription_id = 2;
  int32 num_seats = 3;
  google.protobuf.Timestamp publish_time = 4;
}

// This is an internal message proto, used for publishing to the auth internal pub/sub topic
// which is used for async operations that need to be performed by auth central services.
message AuthCentralAsyncOpsMessage {
  oneof message {
    UserTierChangeMessage user_tier_change_message = 1;
    CreateTenantForTeamMessage create_tenant_for_team_message = 2;
    ResolveInvitationsMessage resolve_invitations_message = 3;
    CreateSubscriptionMessage create_subscription_message = 4;
    SendInvitationEmailMessage send_invitation_email_message = 5;
    TeamPlanChangeMessage team_plan_change_message = 6;
    UpdateSubscriptionMessage update_subscription_message = 7;
  }
}

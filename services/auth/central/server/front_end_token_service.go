package main

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	auth_service "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	front_end_token_service "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
)

var GetUserUsesIdpUserIdFlag = featureflags.NewBoolFlag("auth_central_get_user_uses_idp_user_id", true)

type FrontEndTokenServiceGrpcServer struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	tokenExchangeClient     tokenexchange.TokenExchangeClient
	auditLogger             *audit.AuditLogger
	requestInsightPublisher ripublisher.RequestInsightPublisher
	featureFlagHandle       featureflags.FeatureFlagHandle
	codeTTLSeconds          int
	shutdownChannel         chan struct{}
	signupLimiter           *SignupLimiter

	// Used to call helpers in TeamManagementServer (and to keep all invitation-related logic in one
	// place).
	teamManagementServer *TeamManagementServer
	stripeClient         StripeClient
	stripeConfig         *StripeConfig
}

func NewFrontEndTokenServiceGrpcServer(
	featureFlagHandle featureflags.FeatureFlagHandle,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	auditLogger *audit.AuditLogger,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	config *Config,
	shutdownChannel chan struct{},
	signupLimiter *SignupLimiter,
	teamManagementServer *TeamManagementServer,
	stripeClient StripeClient,
) *FrontEndTokenServiceGrpcServer {
	codeTTL := config.CodeTTLSeconds
	if codeTTL == 0 {
		codeTTL = 600
	}

	// If no stripe client is provided, use the default implementation
	if stripeClient == nil {
		stripeClient = &DefaultStripeClient{}
	}

	return &FrontEndTokenServiceGrpcServer{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		tokenExchangeClient:     tokenExchangeClient,
		auditLogger:             auditLogger,
		requestInsightPublisher: requestInsightPublisher,
		featureFlagHandle:       featureFlagHandle,
		codeTTLSeconds:          codeTTL,
		shutdownChannel:         shutdownChannel,
		signupLimiter:           signupLimiter,
		teamManagementServer:    teamManagementServer,
		stripeClient:            stripeClient,
		stripeConfig:            &config.Stripe,
	}
}

func generateToken(nbytes int) string {
	b := make([]byte, nbytes)
	if _, err := rand.Read(b); err != nil {
		panic(err)
	}
	return hex.EncodeToString(b)
}

func hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func generateCodeChallenge(verifier string) string {
	hash := sha256.Sum256([]byte(verifier))
	return base64.RawURLEncoding.EncodeToString(hash[:])
}

func (s *FrontEndTokenServiceGrpcServer) TokenFromCode(ctx context.Context, req *front_end_token_service.TokenFromCodeRequest) (*front_end_token_service.TokenFromCodeResponse, error) {
	if req.GrantType != "authorization_code" {
		return &front_end_token_service.TokenFromCodeResponse{
			Error: "unsupported_grant_type",
		}, nil
	}

	// Check that the code is valid and that the client ID and redirect URI match
	codeDAO := s.daoFactory.GetCodeDAO()
	codeRow, err := codeDAO.Get(ctx, req.Code)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get code")
		return nil, status.Error(codes.Internal, "Failed to fetch code")
	}

	if codeRow == nil || codeRow.ClientId != req.ClientId || codeRow.RedirectUri != req.RedirectUri {
		return &front_end_token_service.TokenFromCodeResponse{
			Error: "invalid_grant",
		}, nil
	}

	if codeRow.CodeChallenge != "" && generateCodeChallenge(req.CodeVerifier) != codeRow.CodeChallenge {
		return &front_end_token_service.TokenFromCodeResponse{
			Error: "invalid_grant",
		}, nil
	}

	// Check code expiration
	now := time.Now().Unix()
	codeExpiration := codeRow.CreationTimeSeconds + int64(s.codeTTLSeconds)
	if codeExpiration < now || now < codeRow.CreationTimeSeconds {
		return &front_end_token_service.TokenFromCodeResponse{
			Error: "invalid_grant",
		}, nil
	}

	if codeRow.IsUsed {
		// The code was already used.
		//
		// From RFC 6749, section 4.1.2:
		// If an authorization code is used more than once, the authorization
		// server MUST deny the request and SHOULD revoke (when possible) all
		// tokens previously issued based on that authorization code.
		log.Warn().Str("code", req.Code).Msg("Code was used twice")
		return &front_end_token_service.TokenFromCodeResponse{
			Error: "invalid_grant",
		}, nil
	}

	// Mark the code as used
	codeRow.IsUsed = true
	if _, err := codeDAO.Update(ctx, codeRow); err != nil {
		return nil, status.Error(codes.Internal, "failed to update code")
	}

	// Handle the customer-ui in auth-central
	if req.ClientId == "customer-ui" {
		userDAO := s.daoFactory.GetUserDAO()
		user, err := userDAO.Get(ctx, codeRow.AugmentUserId)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get user")
			return nil, status.Error(codes.Internal, "failed to get user")
		}

		// use []any{} because structpb.NewValue does not know what to do with []string{}
		customerUIRoles := []any{}
		tenant, err := s.tenantMap.GetTenantByID(codeRow.TenantId)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get tenant")
			return nil, status.Error(codes.Internal, "failed to get tenant")
		}

		if user != nil && tenant != nil {
			tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
			mapping, err := tenantMappingDAO.GetByUser(ctx, user.Id)
			if err == nil && mapping != nil {
				for _, role := range mapping.CustomerUiRoles {
					customerUIRoles = append(customerUIRoles, role.String())
				}
			}
		}

		var namespace string
		if tenant != nil {
			namespace = tenant.ShardNamespace
		}

		opaqueUserID := &auth_entities.UserId{
			UserId:     codeRow.AugmentUserId,
			UserIdType: auth_entities.UserId_AUGMENT,
		}
		token, err := s.tokenExchangeClient.GetSignedTokenForUser(ctx,
			codeRow.AugmentUserId, opaqueUserID, &codeRow.Email, codeRow.TenantId, &namespace, map[string]any{
				"roles":     customerUIRoles,
				"email":     codeRow.Email,
				"tenant_id": codeRow.TenantId,
			},
		)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get signed token for user")
			return nil, status.Error(codes.Internal, "failed to generate token")
		}

		return &front_end_token_service.TokenFromCodeResponse{
			AccessToken: token.Expose(),
		}, nil
	}

	// Generate an access token and store it in the database
	// TODO(AU-3014): Limit the number of tokens per user
	newToken := generateToken(32) // 16 bytes = 32 hex chars
	tokenHashDAO := s.daoFactory.GetTokenHashDAO()

	newTokenRecord := &auth_entities.TokenHash{
		Hash:                  hashToken(newToken),
		TenantId:              codeRow.TenantId,
		IdpUserId:             codeRow.IdpUserId,
		AugmentUserId:         codeRow.AugmentUserId,
		EmailAddress:          codeRow.Email,
		CreationTime:          timestamppb.Now(),
		ExpirationTimeSeconds: 0,
	}

	if _, err := tokenHashDAO.Create(ctx, newTokenRecord); err != nil {
		return nil, status.Error(codes.Internal, "failed to create token")
	}

	return &front_end_token_service.TokenFromCodeResponse{
		AccessToken: newToken,
		ExpiresIn:   newTokenRecord.ExpirationTimeSeconds,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) Shutdown(ctx context.Context, req *front_end_token_service.ShutdownRequest) (*front_end_token_service.ShutdownResponse, error) {
	s.shutdownChannel <- struct{}{}
	return &front_end_token_service.ShutdownResponse{}, nil
}

func (s *FrontEndTokenServiceGrpcServer) GetNonceForUser(ctx context.Context, req *front_end_token_service.GetNonceForUserRequest) (*front_end_token_service.GetNonceForUserResponse, error) {
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "failed to get user")
	}

	if user == nil {
		return nil, status.Error(codes.NotFound, "user not found")
	}

	return &front_end_token_service.GetNonceForUserResponse{
		Nonce: user.Nonce,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) CreateCode(ctx context.Context, req *front_end_token_service.CreateCodeRequest) (*front_end_token_service.CreateCodeResponse, error) {
	codeDAO := s.daoFactory.GetCodeDAO()
	_, err := codeDAO.Create(ctx, &auth_entities.Code{
		Code:                req.Code,
		Email:               req.Email,
		IdpUserId:           req.IdpUserId,
		AugmentUserId:       req.AugmentUserId,
		ClientId:            req.ClientId,
		TenantId:            req.TenantId,
		RedirectUri:         req.RedirectUri,
		CodeChallenge:       req.CodeChallenge,
		IsUsed:              false,
		CreationTimeSeconds: time.Now().Unix(),
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to create code")
		return nil, status.Error(codes.Internal, "failed to create code")
	}

	return &front_end_token_service.CreateCodeResponse{}, nil
}

func (s *FrontEndTokenServiceGrpcServer) GetTermsApproval(ctx context.Context, req *front_end_token_service.GetTermsApprovalRequest) (*front_end_token_service.GetTermsApprovalResponse, error) {
	termsDAO := s.daoFactory.GetTermsDAO()
	terms, err := termsDAO.Get(ctx, req.Email, req.Revision)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get terms approval")
		return nil, status.Error(codes.Internal, "failed to get terms approval")
	}

	return &front_end_token_service.GetTermsApprovalResponse{
		Approved: terms != nil && terms.Approved,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) SetTermsApproval(ctx context.Context, req *front_end_token_service.SetTermsApprovalRequest) (*front_end_token_service.SetTermsApprovalResponse, error) {
	termsDAO := s.daoFactory.GetTermsDAO()
	_, err := termsDAO.Create(ctx, &auth_entities.TermsApproval{
		Email:    req.Email,
		Revision: req.Revision,
		Approved: req.Approved,
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to set terms approval")
		return nil, status.Error(codes.Internal, "failed to set terms approval")
	}

	return &front_end_token_service.SetTermsApprovalResponse{}, nil
}

func identityProviderAllowed(idpUserId string, allowedIdentityProviders []string) bool {
	if len(allowedIdentityProviders) == 0 {
		return true
	}

	for _, idp := range allowedIdentityProviders {
		if strings.HasPrefix(idpUserId, idp+"|") {
			return true
		}
	}

	return false
}

func (s *FrontEndTokenServiceGrpcServer) GetUser(ctx context.Context, req *front_end_token_service.GetUserRequest) (*front_end_token_service.GetUserResponse, error) {
	use_idp_user_id, _ := GetUserUsesIdpUserIdFlag.Get(s.featureFlagHandle)

	if req.IdpUserId != "" && use_idp_user_id {
		idpUserMappingDAO := s.daoFactory.GetIDPUserMappingDAO()
		mapping, err := idpUserMappingDAO.Get(ctx, req.IdpUserId)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get idp user mapping")
			return nil, status.Error(codes.Internal, "failed to get idp user mapping")
		}

		if mapping != nil {
			userDAO := s.daoFactory.GetUserDAO()
			user, err := userDAO.Get(ctx, mapping.AugmentUserId)
			if err != nil {
				log.Error().Err(err).Msg("Failed to get user")
				return nil, status.Error(codes.Internal, "failed to get user")
			}

			if user == nil {
				// This can happen if the user creation hung or crashed after creating the IDP user mapping,
				// but before creating the user.
				log.Info().Str("user_id", mapping.AugmentUserId).Msg("GetUser: Failed to get user from idp user id")
			}

			return &front_end_token_service.GetUserResponse{
				User: user,
			}, nil
		}
	}

	user, err := s.tenantMap.GetUserByEmailAddress(ctx, req.EmailAddress)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "failed to get user")
	}

	return &front_end_token_service.GetUserResponse{
		User: user,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) GetUserAndSimilarEmails(ctx context.Context, req *front_end_token_service.GetUserAndSimilarEmailsRequest) (*front_end_token_service.GetUserAndSimilarEmailsResponse, error) {
	user, similarEmails, err := s.tenantMap.GetUserAndSimilarUsersByEmailAddress(ctx, req.EmailAddress)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "failed to get user")
	}

	return &front_end_token_service.GetUserAndSimilarEmailsResponse{
		User:          user,
		SimilarEmails: similarEmails,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) EnsureUserInTenant(ctx context.Context, req *front_end_token_service.EnsureUserInTenantRequest) (*front_end_token_service.EnsureUserInTenantResponse, error) {
	user, err := s.daoFactory.GetUserDAO().Get(ctx, req.AugmentUserId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "failed to get user")
	}

	tenant, err := s.tenantMap.GetTenantByID(req.TenantId)
	if tenant == nil {
		log.Error().Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "failed to get tenant")
	}

	user, err = s.tenantMap.EnsureUserInTenant(ctx, user, req.EmailAddress, req.TenantId, req.IdpUserId, req.Mode)
	if err != nil {
		log.Error().Err(err).Msg("Failed to ensure user in tenant")
		return nil, status.Error(codes.Internal, "failed to ensure user in tenant")
	}

	// Publish Request Insight AddUserToTenant event.
	tenantEvent := ripublisher.NewTenantEvent()
	tenantEvent.Event = &riproto.TenantEvent_AddUserToTenant{
		AddUserToTenant: &riproto.AddUserToTenant{
			User: user,
		},
	}
	riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &riproto.TenantInfo{
		TenantId:   req.TenantId,
		TenantName: tenant.Name,
	}, tenantEvent)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to publish AddUserToTenant event")
	}

	return &front_end_token_service.EnsureUserInTenantResponse{
		User: user,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) SignupAllowed(ctx context.Context, req *front_end_token_service.SignupAllowedRequest) (*front_end_token_service.SignupAllowedResponse, error) {
	allowed, err := s.signupLimiter.SignupAllowed(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to check if signup is allowed")
		return nil, status.Error(codes.Internal, "failed to check if signup is allowed")
	}

	return &front_end_token_service.SignupAllowedResponse{
		Allowed: allowed,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) SignupCreditsAvailable(ctx context.Context, req *front_end_token_service.SignupCreditsAvailableRequest) (*front_end_token_service.SignupCreditsAvailableResponse, error) {
	credits, err := s.signupLimiter.CreditsAvailable(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get signup credits available")
		return nil, status.Error(codes.Internal, "failed to get signup credits available")
	}

	return &front_end_token_service.SignupCreditsAvailableResponse{
		CreditsAvailable: float32(credits),
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) TestOnlySetSignupParameters(ctx context.Context, req *front_end_token_service.TestOnlySetSignupParametersRequest) (*front_end_token_service.TestOnlySetSignupParametersResponse, error) {
	s.signupLimiter.Set(int(req.MaxBurst), int(req.SignupPerDay))
	return &front_end_token_service.TestOnlySetSignupParametersResponse{}, nil
}

func (s *FrontEndTokenServiceGrpcServer) GetTokenInfo(ctx context.Context, req *front_end_token_service.GetTokenInfoRequest) (*front_end_token_service.GetTokenInfoResponse, error) {
	tokenHashDAO := s.daoFactory.GetTokenHashDAO()
	tokenInfo, err := tokenHashDAO.Get(ctx, hashToken(req.Token))
	if err != nil {
		log.Error().Err(err).Msg("Failed to get token info")
		return nil, status.Error(codes.Internal, "failed to get token info")
	}

	if tokenInfo == nil {
		return &front_end_token_service.GetTokenInfoResponse{}, nil
	}

	if tokenInfo.ExpirationTimeSeconds != 0 && time.Now().Sub(tokenInfo.CreationTime.AsTime()) >= time.Duration(tokenInfo.ExpirationTimeSeconds)*time.Second {
		log.Info().Msgf("Token expired for hash %s", tokenHash(req.Token))
		return &front_end_token_service.GetTokenInfoResponse{}, nil
	}

	return &front_end_token_service.GetTokenInfoResponse{
		AugmentUserId: tokenInfo.AugmentUserId,
		TenantId:      tokenInfo.TenantId,
	}, nil
}

func (s *FrontEndTokenServiceGrpcServer) GetUserInvitations(
	ctx context.Context, req *auth_service.GetUserInvitationsRequest,
) (*auth_service.GetUserInvitationsResponse, error) {
	return s.teamManagementServer.getUserInvitations(ctx, req)
}

func (s *FrontEndTokenServiceGrpcServer) ResolveInvitations(
	ctx context.Context, req *auth_service.ResolveInvitationsRequest,
) (*auth_service.ResolveInvitationsResponse, error) {
	return s.teamManagementServer.resolveInvitations(ctx, req)
}

func (s *FrontEndTokenServiceGrpcServer) GetResolveInvitationsStatus(
	ctx context.Context, req *auth_service.GetResolveInvitationsStatusRequest,
) (*auth_service.GetResolveInvitationsStatusResponse, error) {
	return s.teamManagementServer.getResolveInvitationsStatus(ctx, req)
}

func (s *FrontEndTokenServiceGrpcServer) IsUserAdmin(
	ctx context.Context, req *front_end_token_service.IsUserAdminRequest,
) (*front_end_token_service.IsUserAdminResponse, error) {
	user, err := s.daoFactory.GetUserDAO().Get(ctx, req.UserId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "failed to get user")
	}

	if user == nil {
		return &front_end_token_service.IsUserAdminResponse{
			IsAdmin: false,
		}, nil
	}

	for _, tenantID := range user.Tenants {
		tenant, err := s.tenantMap.GetTenantByID(tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get tenant %s", tenantID)
			return nil, status.Error(codes.Internal, "Failed to get tenant")
		} else if tenant == nil {
			continue
		}

		tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
		mapping, err := tenantMappingDAO.GetByUser(ctx, user.Id)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get user mapping")
			return nil, status.Error(codes.Internal, "Failed to get user mapping")
		} else if mapping == nil {
			continue
		}

		for _, role := range mapping.CustomerUiRoles {
			if role == auth_entities.CustomerUiRole_ADMIN {
				return &front_end_token_service.IsUserAdminResponse{
					IsAdmin: true,
				}, nil
			}
		}
	}

	return &front_end_token_service.IsUserAdminResponse{
		IsAdmin: false,
	}, nil
}

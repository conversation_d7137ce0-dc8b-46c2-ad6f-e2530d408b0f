worker_processes 5;

events {
}

http {
    server {
        listen 80 default_server;

        location = /healthcheck {
          add_header Content-Type text/plain;
          return 200 'ok';
        }

        location / {
            proxy_set_header Host       $host;
            proxy_set_header X-Real-IP  $remote_addr;
            proxy_set_header X-Forwarded-Uri $request_uri;
            proxy_set_header X-Scheme                $scheme;
            proxy_set_header X-Forwarded-For $http_x_forwarded_for;
            proxy_pass http://127.0.0.1:5000;

            # We set larger cookies, so a 4k buffer size for response headers
            # won't cut it.
            proxy_buffer_size 32k;
            proxy_buffers 8 32k;
        }
    }
}

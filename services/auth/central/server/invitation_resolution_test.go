package main

import (
	"context"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	front_end_token_service_pb "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	"github.com/augmentcode/augment/services/integrations/orb"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	selfServeTeamTenantID = "self-serve"
	vanguardTenantID      = "vanguard0"
	enterpriseTenantID    = "enterprise-tenant"
	invitedUserEmail      = "<EMAIL>"
	maxTrialSeats         = 5
)

func newTestInvitationResolutionProcessor(t *testing.T, currentSubscriptionInfo *orb.OrbSubscriptionInfo) (processor *InvitationResolutionProcessor, cleanup func()) {
	bigtableFixture := NewBigtableFixture(t)
	daoFactoryFixture := NewDAOFactoryFixture(bigtableFixture)
	selfServeTeamTenant := &tw_proto.Tenant{
		Id:             selfServeTeamTenantID,
		Name:           selfServeTeamTenantID,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "d0",
		Cloud:          "CLOUD_PROD",
		Config: &tw_proto.Config{
			Configs: map[string]string{
				"is_self_serve_team": "true",
			},
		},
	}
	// Set up mock tenant subscription mapping
	tenantSubscriptionMappingDAO := daoFactoryFixture.DAOFactory.GetTenantSubscriptionMappingDAO()
	tenantSubscriptionMapping := &auth_entities.TenantSubscriptionMapping{
		TenantId:             selfServeTeamTenantID,
		StripeSubscriptionId: "test-subscription-id",
		OrbCustomerId:        "orb-customer-id",
		OrbSubscriptionId:    "orb-subscription-id",
	}
	_, err := tenantSubscriptionMappingDAO.Create(context.Background(), tenantSubscriptionMapping)
	require.NoError(t, err)

	vanguardTenant := &tw_proto.Tenant{
		Id:             vanguardTenantID,
		Name:           vanguardTenantID,
		Tier:           tw_proto.TenantTier_COMMUNITY,
		ShardNamespace: "i0",
		Cloud:          "CLOUD_PROD",
	}
	enterpriseTenant := &tw_proto.Tenant{
		Id:             enterpriseTenantID,
		Name:           enterpriseTenantID,
		Tier:           tw_proto.TenantTier_ENTERPRISE,
		ShardNamespace: "e0",
		Cloud:          "CLOUD_PROD",
		AuthConfiguration: &tw_proto.AuthConfiguration{
			Domain: "enterprise.com",
		},
	}
	tenantChangeChannel := make(chan tw_client.TenantChange, 1)
	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_proto.WatchTenantsResponse{
			Tenants: []*tw_proto.TenantChange{
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: selfServeTeamTenant,
						},
					},
				},
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: vanguardTenant,
						},
					},
				},
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: enterpriseTenant,
						},
					},
				},
			},
			IsInitial: true,
		},
	}
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_proto.Tenant{
			selfServeTeamTenant,
			vanguardTenant,
			enterpriseTenant,
		},
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}
	mockOrbClient := orb.NewMockOrbClient()
	tenantMap := NewTenantMap(
		daoFactoryFixture.DAOFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		featureflags.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)
	// Mock Orb responses
	mockOrbClient.On("CancelOrbSubscription", mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mockOrbClient.On("PurchaseCredits", mock.Anything, mock.Anything).Return(nil)
	mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mockOrbClient.On("PurchaseCredits", mock.Anything, mock.Anything, mock.Anything).Return(nil)
	mockOrbClient.On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbPlanInfo{
		MessagesPerSeat:         1000,
		IncludedMessagesPriceID: "price-id-messages",
	}, nil)
	// Given that this needs to be different for different tests, have the tests pass it in
	mockOrbClient.On("GetUserSubscription", mock.Anything, mock.Anything, mock.Anything).Return(currentSubscriptionInfo, nil)

	processor, err = NewInvitationResolutionProcessor(
		daoFactoryFixture.DAOFactory,
		tenantMap,
		NewMockStripeClient(),
		ripublisher.NewRequestInsightPublisherMock(),
		&OrbConfig{
			Enabled: true,
			Plans: []PlanConfig{
				{
					ID: "orb-trial-plan",
					Features: PlanFeatures{
						PlanType: PlanTypePaidTrial,
						MaxSeats: maxTrialSeats,
					},
				},
				{
					ID: "orb-community-plan",
					Features: PlanFeatures{
						PlanType: PlanTypeCommunity,
					},
				},
				{
					ID: "orb-developer-plan",
					Features: PlanFeatures{
						PlanType: PlanTypePaid,
					},
				},
			},
		},
		mockOrbClient,
		audit.NewDefaultAuditLogger(),
	)
	require.NoError(t, err)
	cleanup = func() {
		bigtableFixture.Cleanup()
		daoFactoryFixture.Cleanup()
		tenantMap.Close()
	}
	return processor, cleanup
}

func setupInvitationResolution(
	t *testing.T, processor *InvitationResolutionProcessor, acceptedID string, declinedIDs []string,
) *auth_internal.ResolveInvitationsMessage {
	resolutionID := uuid.New().String()
	invitationResolutionDAO := processor.daoFactory.GetInvitationResolutionDAO()
	invitationResolution := &auth_entities.InvitationResolution{
		Id:        resolutionID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.InvitationResolution_PENDING,
	}
	_, err := invitationResolutionDAO.Create(context.Background(), invitationResolution)
	require.NoError(t, err)

	return &auth_internal.ResolveInvitationsMessage{
		InvitationResolutionId: resolutionID,
		ResolveInvitationsRequest: &authpb.ResolveInvitationsRequest{
			AcceptInvitationId:   &acceptedID,
			DeclineInvitationIds: declinedIDs,
		},
		PublishTime: timestamppb.Now(),
	}
}

func getResolutionStatus(
	t *testing.T, processor *InvitationResolutionProcessor, resolutionID string,
) auth_entities.InvitationResolution_Status {
	invitationResolutionDAO := processor.daoFactory.GetInvitationResolutionDAO()
	invitationResolution, err := invitationResolutionDAO.Get(context.Background(), resolutionID)
	require.NoError(t, err)
	return invitationResolution.Status
}

func createInvitation(
	t *testing.T, processor *InvitationResolutionProcessor, email, tenantID string,
) *auth_entities.TenantInvitation {
	invitationID := uuid.New().String()
	invitationDAO := processor.daoFactory.GetTenantInvitationDAO(tenantID)
	invitation := &auth_entities.TenantInvitation{
		Id:            invitationID,
		CreatedAt:     timestamppb.Now(),
		InviteeEmail:  email,
		TenantId:      tenantID,
		InviterUserId: "inviter-user",
		InviterEmail:  "<EMAIL>",
		Status:        auth_entities.TenantInvitation_PENDING,
	}
	_, err := invitationDAO.Create(context.Background(), invitation)
	require.NoError(t, err)
	return invitation
}

func getInvitationStatus(
	t *testing.T, processor *InvitationResolutionProcessor, tenantID string, invitationID string,
) auth_entities.TenantInvitation_Status {
	invitationDAO := processor.daoFactory.GetTenantInvitationDAO(tenantID)
	invitation, err := invitationDAO.Get(context.Background(), invitationID)
	require.NoError(t, err)
	return invitation.Status
}

func TestInvitationResolutionProcessor(t *testing.T) {
	logging.SetupServerLogging()

	t.Run("invalid invitation ids", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, nil)
		defer cleanup()

		// Non-existent accepted invitation id.
		msg := setupInvitationResolution(t, processor, "non-existent-id", nil)
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)
		require.Equal(t, auth_entities.InvitationResolution_ERROR, getResolutionStatus(t, processor, msg.InvitationResolutionId))

		// Non-existent declined invitation id.
		msg = setupInvitationResolution(t, processor, "", []string{"non-existent-id"})
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)
		require.Equal(t, auth_entities.InvitationResolution_ERROR, getResolutionStatus(t, processor, msg.InvitationResolutionId))
	})

	t.Run("accept previously declined invitation", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, nil)
		defer cleanup()

		// Create an invitation to decline.
		invitation := createInvitation(t, processor, invitedUserEmail, selfServeTeamTenantID)

		// Decline the invitation.
		msg := setupInvitationResolution(t, processor, "", []string{invitation.Id})
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)
		require.Equal(t, auth_entities.InvitationResolution_SUCCESS, getResolutionStatus(t, processor, msg.InvitationResolutionId))
		require.Equal(t, auth_entities.TenantInvitation_DECLINED, getInvitationStatus(t, processor, selfServeTeamTenantID, invitation.Id))

		// Accept the invitation.
		msg = setupInvitationResolution(t, processor, invitation.Id, nil)
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)
		require.Equal(t, auth_entities.InvitationResolution_ERROR, getResolutionStatus(t, processor, msg.InvitationResolutionId))
		require.Equal(t, auth_entities.TenantInvitation_DECLINED, getInvitationStatus(t, processor, selfServeTeamTenantID, invitation.Id))
	})

	t.Run("accept invitation - enterprise email", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, nil)
		defer cleanup()

		// Create an invitation to accept.
		invitation := createInvitation(t, processor, "<EMAIL>", selfServeTeamTenantID)

		// Accept the invitation.
		msg := setupInvitationResolution(t, processor, invitation.Id, nil)
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Make sure the resolution is in error state and the invitation is still pending.
		require.Equal(t, auth_entities.InvitationResolution_ERROR, getResolutionStatus(t, processor, msg.InvitationResolutionId))
		require.Equal(t, auth_entities.TenantInvitation_PENDING, getInvitationStatus(t, processor, selfServeTeamTenantID, invitation.Id))
	})

	t.Run("decline invitations", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, nil)
		defer cleanup()

		// Create an invitation to decline.
		invitation := createInvitation(t, processor, invitedUserEmail, selfServeTeamTenantID)

		// Decline the invitation.
		msg := setupInvitationResolution(t, processor, "", []string{invitation.Id})
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)
		require.Equal(t, auth_entities.InvitationResolution_SUCCESS, getResolutionStatus(t, processor, msg.InvitationResolutionId))
		require.Equal(t, auth_entities.TenantInvitation_DECLINED, getInvitationStatus(t, processor, selfServeTeamTenantID, invitation.Id))
	})

	t.Run("accept invitation - new user", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, &orb.OrbSubscriptionInfo{
			ExternalPlanID: "orb-trial-plan",
			CurrentFixedQuantities: &orb.FixedQuantities{
				IncludedMessages: 1000,
			},
		})
		defer cleanup()

		// Create an invitation to accept.
		invitation := createInvitation(t, processor, invitedUserEmail, selfServeTeamTenantID)

		// Accept the invitation.
		msg := setupInvitationResolution(t, processor, invitation.Id, nil)
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)
		require.Equal(t, auth_entities.InvitationResolution_SUCCESS, getResolutionStatus(t, processor, msg.InvitationResolutionId))
		require.Equal(t, auth_entities.TenantInvitation_ACCEPTED, getInvitationStatus(t, processor, selfServeTeamTenantID, invitation.Id))

		// Make sure the user is in their new tenant.
		user, err := processor.tenantMap.GetUserByEmailAddress(context.Background(), invitation.InviteeEmail)
		require.NoError(t, err)
		require.Equal(t, []string{selfServeTeamTenantID}, user.Tenants)
	})

	t.Run("accept invitation - existing user", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, nil)
		defer cleanup()

		// Create an existing user that belongs to another tenant already.
		_, err := processor.tenantMap.EnsureUserInTenant(context.Background(), nil, invitedUserEmail, vanguardTenantID, "", front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
		require.NoError(t, err)

		// Create an invitation to accept.
		invitation := createInvitation(t, processor, invitedUserEmail, selfServeTeamTenantID)

		// Accept the invitation.
		msg := setupInvitationResolution(t, processor, invitation.Id, nil)
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Make sure the user is in their new tenant.
		user, err := processor.tenantMap.GetUserByEmailAddress(context.Background(), invitation.InviteeEmail)
		require.NoError(t, err)
		require.Equal(t, []string{selfServeTeamTenantID}, user.Tenants)
	})

	t.Run("missing invitation resolution record", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, nil)
		defer cleanup()

		// Publish a message for a non-existent invitation resolution record.
		msg := &auth_internal.ResolveInvitationsMessage{
			InvitationResolutionId:    "non-existent-id",
			ResolveInvitationsRequest: &authpb.ResolveInvitationsRequest{},
			PublishTime:               timestamppb.Now(),
		}
		err := processor.Process(context.Background(), msg)
		require.Error(t, err)
	})

	t.Run("new user, trial plan, below limit", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, &orb.OrbSubscriptionInfo{
			ExternalPlanID: "orb-trial-plan",
			CurrentFixedQuantities: &orb.FixedQuantities{
				IncludedMessages: 1000,
			},
		})
		defer cleanup()

		// Create an invitation to accept.
		invitation := createInvitation(t, processor, invitedUserEmail, selfServeTeamTenantID)

		// Accept the invitation.
		msg := setupInvitationResolution(t, processor, invitation.Id, nil)
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Assert that we called UpdateFixedQuantity
		processor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 1)
		processor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: "orb-subscription-id",
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  "price-id-messages",
				Quantity: 2000,
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.Anything)
	})

	t.Run("new user, trial plan, above limit", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, &orb.OrbSubscriptionInfo{
			ExternalPlanID: "orb-trial-plan",
			CurrentFixedQuantities: &orb.FixedQuantities{
				IncludedMessages: 1000 * maxTrialSeats, // above the limit
			},
		})
		defer cleanup()

		// Set the TrialCreditsAwardedCount to the max number of seats - 1
		tenantSubscriptionMappingDAO := processor.daoFactory.GetTenantSubscriptionMappingDAO()
		tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(context.Background(), selfServeTeamTenantID)
		require.NoError(t, err)
		tenantSubscriptionMapping.TrialCreditsAwardedCount = maxTrialSeats - 1
		_, err = tenantSubscriptionMappingDAO.Update(context.Background(), tenantSubscriptionMapping)
		require.NoError(t, err)

		// Create an invitation to accept.
		invitation := createInvitation(t, processor, invitedUserEmail, selfServeTeamTenantID)

		// Accept the invitation.
		msg := setupInvitationResolution(t, processor, invitation.Id, nil)
		err = processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Assert that we did not call UpdateFixedQuantity
		processor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 0)
	})

	t.Run("new user, non-trial plan", func(t *testing.T) {
		processor, cleanup := newTestInvitationResolutionProcessor(t, &orb.OrbSubscriptionInfo{
			ExternalPlanID: "orb-developer-plan",
			CurrentFixedQuantities: &orb.FixedQuantities{
				IncludedMessages: 1000,
			},
		})
		defer cleanup()

		// Create an invitation to accept.
		invitation := createInvitation(t, processor, invitedUserEmail, selfServeTeamTenantID)

		// Accept the invitation.
		msg := setupInvitationResolution(t, processor, invitation.Id, nil)
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Assert that we did not call UpdateFixedQuantity (not on trial)
		processor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 0)
	})

	// TODO(jacqueline): Failpoint tests.
}

package main

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	orbclient "github.com/augmentcode/augment/services/integrations/orb"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	orb "github.com/orbcorp/orb-go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var stripeFailuresCounter = promauto.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_auth_central_stripe_failures_total",
		Help: "Total number of Stripe operation failures",
	},
	[]string{"operation", "error_type"},
)

// Injected panic points for testing idempotence in unit tests.
var SubscriptionCreationPanicPoints = struct {
	SetupStripeCustomerError  string
	SetupOrbCustomerError     string
	SetupOrbSubscriptionError string
}{
	SetupStripeCustomerError:  "setup-stripe-customer-error",
	SetupOrbCustomerError:     "setup-orb-customer-error",
	SetupOrbSubscriptionError: "setup-orb-subscription-error",
}

// SubscriptionCreationProcessor is responsible for processing subscription creation messages from
// the async-ops pubsub topic. We do this async to guard against ending up with users with
// incomplete subscription configurations if there are Stripe/Orb API errors.
type SubscriptionCreationProcessor struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	stripeClient            StripeClient
	orbConfig               *OrbConfig
	orbClient               orbclient.OrbClient
	requestInsightPublisher ripublisher.RequestInsightPublisher
	auditLogger             *audit.AuditLogger
}

func NewSubscriptionCreationProcessor(
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	stripeClient StripeClient,
	orbConfig *OrbConfig,
	orbClient orbclient.OrbClient,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	auditLogger *audit.AuditLogger,
) (*SubscriptionCreationProcessor, error) {
	return &SubscriptionCreationProcessor{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		stripeClient:            stripeClient,
		orbConfig:               orbConfig,
		orbClient:               orbClient,
		requestInsightPublisher: requestInsightPublisher,
		auditLogger:             auditLogger,
	}, nil
}

// Note that the returned error from this function is about whether we want to retry the message,
// and not necessarily whether there was an error during processing. Nil means we don't want to
// retry.
func (p *SubscriptionCreationProcessor) Process(
	ctx context.Context,
	msg *auth_internal.CreateSubscriptionMessage,
) (returnErr error) {
	subscriptionCreationID := msg.Id
	userID := msg.UserId
	tenantID := msg.TenantId

	// Local logger with information about this request.
	logger := log.Ctx(ctx).With().
		Str("subscription_creation_id", subscriptionCreationID).
		Str("user_id", userID).
		Str("tenant_id", tenantID).
		Logger()

	logger.Info().Msg("CreateSubscriptionProcessor: Processing subscription creation message")

	// Get the user and make sure that its pending subscription creation matches this async message.
	// Verify the subscription creation ID matches what's in the user object. It is likely that this
	// will fail on the first try since we publish to the pub/sub queue before writing this record to
	// BigTable. Retry a few times to avoid a long backoff while we wait for pub/sub to retry.
	userDAO := p.daoFactory.GetUserDAO()
	var user *auth_entities.User
	var err error
	maxAttempts := 5
	sleepDuration := time.Millisecond * 200
	for attempt := 0; attempt < maxAttempts; attempt++ {
		user, err = userDAO.Get(ctx, userID)
		if err == nil && user != nil && user.SubscriptionCreationId != "" {
			break
		}
		logger.Warn().Err(err).Msgf(
			"Failed to get user with subscription creation ID, attempt %d/%d", attempt+1, maxAttempts)
		time.Sleep(sleepDuration)
	}
	if err != nil {
		return fmt.Errorf("CreateSubscriptionProcessor: failed to fetch user: %w", err)
	} else if user == nil {
		return fmt.Errorf("CreateSubscriptionProcessor: user %s not found", userID)
	}

	if user.SubscriptionCreationId == "" {
		// This is an expected race because we publish this creation request before updating the user.
		// If it's been a long time, then we probably crashed between publishing and updating the user;
		// drop the message.
		messageAge := time.Since(msg.PublishTime.AsTime())
		if messageAge > 5*time.Minute {
			logger.Info().Msgf("Dropping subscription creation message after 5 minute timeout")
			return nil
		}
		return fmt.Errorf("CreateSubscriptionProcessor: User does not have a pending subscription creation. This is an expected race but should not persist")
	} else if user.SubscriptionCreationId != subscriptionCreationID {
		logger.Info().Msgf("CreateSubscriptionProcessor: User has a different pending subscription creation, dropping subscription creation")
		return nil
	}

	// If we manually fix people and set their status to success, drop them.
	if user.SubscriptionCreationInfo != nil && user.SubscriptionCreationInfo.Status == auth_entities.User_SubscriptionCreationInfo_SUCCESS {
		logger.Info().Msgf("CreateSubscriptionProcessor: User already has a successful subscription creation, dropping subscription creation")
		return nil
	}

	// HACK: Drop this subscription creation if the user doesn't have a valid email. It's a known
	// issue that users who sign in with Microsoft can give a phone number instead of an email, but we
	// record it as an email. We need to figure out how to handle these users and then backfill.
	if strings.Index(user.Email, "@") == -1 {
		logger.Warn().Str("email", user.Email).Msgf("CreateSubscriptionProcessor: User does not have a valid email, dropping subscription creation")
		return nil
	}

	// Now that we know we're supposed to be processing this subscription creation, defer an update
	// to the user's SubscriptionCreationInfo when we're done.
	defer func() {
		// Add failpoints here to make sure we don't mark as success on a failpoint
		test_utils.CheckPanic(SubscriptionCreationPanicPoints.SetupStripeCustomerError)
		test_utils.CheckPanic(SubscriptionCreationPanicPoints.SetupOrbCustomerError)
		test_utils.CheckPanic(SubscriptionCreationPanicPoints.SetupOrbSubscriptionError)

		if returnErr == nil {
			logger.Info().Msg("CreateSubscriptionProcessor: Subscription creation completed successfully, updating user status")
			// Update the user's subscription creation info to reflect success.
			userDAO := p.daoFactory.GetUserDAO()
			_, err := userDAO.TryUpdate(ctx, userID, func(u *auth_entities.User) bool {
				if u.SubscriptionCreationInfo == nil {
					u.SubscriptionCreationInfo = &auth_entities.User_SubscriptionCreationInfo{
						Status:    auth_entities.User_SubscriptionCreationInfo_SUCCESS,
						Id:        subscriptionCreationID,
						CreatedAt: timestamppb.Now(),
						UpdatedAt: timestamppb.Now(),
					}
				} else {
					u.SubscriptionCreationInfo.Status = auth_entities.User_SubscriptionCreationInfo_SUCCESS
				}
				u.SubscriptionCreationInfo.Status = auth_entities.User_SubscriptionCreationInfo_SUCCESS
				u.SubscriptionCreationInfo.UpdatedAt = timestamppb.Now()
				return true
			}, DefaultRetry)
			if err != nil {
				returnErr = fmt.Errorf("CreateSubscriptionProcessor: failed to update user's subscription creation info: %w", err)
				logger.Error().Err(err).Msg("CreateSubscriptionProcessor: Failed to update user's subscription creation status")
			} else {
				logger.Info().Msg("CreateSubscriptionProcessor: Successfully updated user's subscription creation status to SUCCESS")
			}
		} else {
			logger.Error().Err(returnErr).Msg("CreateSubscriptionProcessor: Subscription creation failed")
		}
	}()

	tenant, err := p.tenantMap.GetTenantByID(tenantID)
	if err != nil {
		return fmt.Errorf("CreateSubscriptionProcessor: failed to fetch tenant: %w", err)
	} else if tenant == nil {
		return fmt.Errorf("CreateSubscriptionProcessor: tenant %s not found", tenantID)
	} else if tenant.Tier == tw_pb.TenantTier_ENTERPRISE || tenantutil.IsSelfServeTeamTenant(tenant) {
		// This is unexpected because we check before publishing, but check again to be safe.
		logger.Warn().Msgf(
			"CreateSubscriptionProcessor: Tenant %s is an enterprise or self-serve tenant, skipping subscription creation",
			tenantID)
		return nil
	}

	test_utils.CheckPanic(SubscriptionCreationPanicPoints.SetupStripeCustomerError)

	// Create the Stripe customer if needed. Note that we need a Stripe customer even if the user is
	// being billed with Orb.
	stripeCustomerID := user.StripeCustomerId
	if stripeCustomerID == "" {
		stripeCustomerID, err = p.setupStripeCustomer(ctx, logger, user.Id, user.Email, tenant)
		if err != nil {
			return err
		}
	} else {
		logger.Info().Msgf("CreateSubscriptionProcessor: User already has Stripe customer %s", stripeCustomerID)
	}
	logger = logger.With().
		Str("stripe_customer_id", stripeCustomerID).
		Logger()

	test_utils.CheckPanic(SubscriptionCreationPanicPoints.SetupOrbCustomerError)

	// Create the Orb customer if needed.
	orbCustomerID := user.OrbCustomerId
	if orbCustomerID == "" {
		orbCustomerID, err = p.setupOrbCustomer(ctx, logger, user.Id, user.Email, tenant.Tier, stripeCustomerID)
		if err != nil {
			return err
		}
	} else {
		logger.Info().Msgf("CreateSubscriptionProcessor: User already has Orb customer %s", orbCustomerID)
	}
	logger = logger.With().
		Str("orb_customer_id", orbCustomerID).
		Logger()

	test_utils.CheckPanic(SubscriptionCreationPanicPoints.SetupOrbSubscriptionError)

	// Create the Orb subscription
	err = p.setupOrbSubscription(ctx, logger, user.Id, tenant.Tier, orbCustomerID, stripeCustomerID, msg.PublishTime.AsTime())
	if err != nil {
		return err
	}

	return nil
}

// Set up a new stripe customer. Returns an error that can be propagated directly if this fails.
func (p *SubscriptionCreationProcessor) setupStripeCustomer(
	ctx context.Context,
	logger zerolog.Logger,
	augmentUserID string,
	email string,
	tenant *tw_pb.Tenant,
) (string, error) {
	var stripeCustomerID string

	// First, query stripe for any existing customer matching this email. This was added as a result
	// of #insights_inc_16337.
	existingCustomer, err := p.stripeClient.FindCustomerByEmail(email)
	if err != nil {
		return "", fmt.Errorf("CreateSubscriptionProcessor: failed to find stripe customer by email: %w", err)
	}
	if existingCustomer != nil {
		// Use the existing stripe customer.
		stripeCustomerID = existingCustomer.ID
		logger.Info().Msgf("CreateSubscriptionProcessor: Found existing Stripe customer %s for user %s", stripeCustomerID, augmentUserID)
	} else {
		// Create a new stripe customer.
		params := &stripe.CustomerParams{
			Email: stripe.String(email),
			Metadata: map[string]string{
				"augment_user_id": augmentUserID,
			},
			Params: stripe.Params{
				// Use the augment user id as the idempotency key because we want exactly one stripe customer
				// per augment user.
				IdempotencyKey: &augmentUserID,
			},
		}
		stripeCustomer, err := p.stripeClient.CreateCustomer(params)
		if err != nil {
			stripeFailuresCounter.WithLabelValues("customer_creation", err.Error()).Inc()
			return "", fmt.Errorf("CreateSubscriptionProcessor: failed to create Stripe customer: %w", err)
		}

		stripeCustomerID = stripeCustomer.ID
		logger.Info().Msgf("CreateSubscriptionProcessor: Created Stripe customer %s for user %s", stripeCustomerID, augmentUserID)
	}

	// Update the user with the Stripe customer ID.
	userDAO := p.daoFactory.GetUserDAO()
	_, err = userDAO.TryUpdate(ctx, augmentUserID, func(u *auth_entities.User) bool {
		u.StripeCustomerId = stripeCustomerID
		return true
	}, DefaultRetry)
	if err != nil {
		return "", fmt.Errorf("CreateSubscriptionProcessor: failed to update user with Stripe customer ID: %w", err)
	}
	p.auditLogger.WriteAuditLog(
		augmentUserID,
		"",
		"",
		fmt.Sprintf("Created Stripe customer %s", stripeCustomerID),
	)

	return stripeCustomerID, nil
}

// Set up a new orb customer. Updates the user DAO when successful.
func (p *SubscriptionCreationProcessor) setupOrbCustomer(
	ctx context.Context,
	logger zerolog.Logger,
	augmentUserID string,
	email string,
	tenantTier tw_pb.TenantTier,
	stripeCustomerID string,
) (string, error) {
	orbCustomer := orbclient.OrbCustomer{
		Email:              email,
		Name:               email,
		StripeID:           stripeCustomerID,
		Metadata:           map[string]string{"augment_user_id": augmentUserID},
		Timezone:           "UTC",
		ExternalCustomerID: nil, // nil for now
	}
	// Use augmentUserID as the idempotency key because we want exactly one Orb customer per user.
	if p.orbClient == nil {
		return "", fmt.Errorf("CreateSubscriptionProcessor: orb client is nil, cannot create Orb customer")
	}
	orbCustomerID, err := SafeCreateCustomer(ctx, p.orbClient, p.daoFactory.GetUserDAO(), augmentUserID, orbCustomer, true, &augmentUserID)
	if err != nil {
		return "", fmt.Errorf("CreateSubscriptionProcessor: failed to create Orb customer: %w", err)
	}

	// Update the user with the Orb customer ID. We do this now to make it less likely that we end
	// up creating duplicate Orb customers.
	userDAO := p.daoFactory.GetUserDAO()
	_, err = userDAO.TryUpdate(ctx, augmentUserID, func(u *auth_entities.User) bool {
		u.OrbCustomerId = orbCustomerID
		return true
	}, DefaultRetry)
	if err != nil {
		return "", fmt.Errorf("CreateSubscriptionProcessor: failed to update user with Orb customer ID: %w", err)
	}

	logger.Info().Msgf("CreateSubscriptionProcessor: Created Orb customer %s for user %s", orbCustomerID, augmentUserID)
	p.auditLogger.WriteAuditLog(
		augmentUserID,
		"",
		"",
		fmt.Sprintf("Created Orb customer %s", orbCustomerID),
	)
	return orbCustomerID, nil
}

// Create the orb subscription, plan change (if relevant), and credit alert. Updates the user DAO
// when successful.
// Note(jacqueline): Doing these 3 operations together puts us at risk of ending up with duplicate
// subscriptions and/or change plans if one of the API calls fails along the way. If this ends up
// being an issue we can store more intermediate state in the user DAO.
func (p *SubscriptionCreationProcessor) setupOrbSubscription(
	ctx context.Context,
	logger zerolog.Logger,
	augmentUserID string,
	tenantTier tw_pb.TenantTier,
	orbCustomerID string,
	stripeCustomerID string,
	publishTime time.Time,
) error {
	orbSubscription := orbclient.OrbSubscription{
		CustomerOrbID: orbCustomerID,
	}
	switch tenantTier {
	case tw_pb.TenantTier_COMMUNITY:
		orbSubscription.ExternalPlanID = p.orbConfig.getCommunityPlan().ID
	case tw_pb.TenantTier_PROFESSIONAL:
		orbSubscription.ExternalPlanID = p.orbConfig.getTrialPlan().ID
		// Schedule the trial plan to end in two weeks
		twoWeeks := publishTime.Add(14 * 24 * time.Hour)
		orbSubscription.EndDate = &twoWeeks
	default:
		// This should be impossible.
		return fmt.Errorf("CreateSubscriptionProcessor: invalid tenant tier %s", tenantTier.String())
	}

	// Use augmentUserID as the base of the idempotency key because we want exactly one subscription
	// per user. Note that each operation needs a different idempotency key; Orb will return an error
	// if a key is reused across different operations. (Maximum key length is 64 characters.)
	// TODO(jacqueline): It's an open question whether this is actually the right call.
	if p.orbClient == nil {
		return fmt.Errorf("CreateSubscriptionProcessor: orb client is nil, cannot create Orb subscription")
	}
	createSubscriptionIdempotencyKey := fmt.Sprintf("%s-create-sub", augmentUserID)
	orbSubscriptionID, err := SafeCreateSubscription(
		ctx, p.orbClient, p.daoFactory.GetUserDAO(), augmentUserID, orbSubscription, &createSubscriptionIdempotencyKey)
	if err != nil {
		return fmt.Errorf("CreateSubscriptionProcessor: failed to create Orb subscription: %w", err)
	}

	// Create Orb alerts
	addCreditBalanceDepletedIdempotencyKey := fmt.Sprintf("%s-credit-depleted-alert", augmentUserID)
	addCreditBalanceRecoveredIdempotencyKey := fmt.Sprintf("%s-credit-recovered-alert", augmentUserID)
	err = checkOrbError(logger, SafeAddCreditBalanceDepletedAlert(
		ctx, p.orbClient, p.daoFactory.GetUserDAO(), augmentUserID, orbCustomerID, p.orbConfig.PricingUnit, &addCreditBalanceDepletedIdempotencyKey), "CreateSubscriptionProcessor")
	if err != nil {
		return fmt.Errorf("CreateSubscriptionProcessor: failed to add credit balance depleted alert for orb customer: %w", err)
	}
	err = checkOrbError(logger, SafeAddCreditBalanceRecoveredAlert(
		ctx, p.orbClient, p.daoFactory.GetUserDAO(), augmentUserID, orbCustomerID, p.orbConfig.PricingUnit, &addCreditBalanceRecoveredIdempotencyKey), "CreateSubscriptionProcessor")
	if err != nil {
		return fmt.Errorf("CreateSubscriptionProcessor: failed to add credit balance recovered alert for orb customer: %w", err)
	}

	// Update the user with the subscription ID.
	userDAO := p.daoFactory.GetUserDAO()
	_, err = userDAO.TryUpdate(ctx, augmentUserID, func(u *auth_entities.User) bool {
		u.OrbSubscriptionId = orbSubscriptionID
		return true
	}, DefaultRetry)
	if err != nil {
		return fmt.Errorf("CreateSubscriptionProcessor: failed to update user with Orb subscription ID: %w", err)
	}
	logger.Info().Msgf("CreateSubscriptionProcessor: Completed orb setup")
	p.auditLogger.WriteAuditLog(
		augmentUserID,
		"",
		"",
		fmt.Sprintf("Created Orb subscription %s", orbSubscriptionID),
	)
	return nil
}

// Determines whether an error from orb should be ignored. If so, returns nil. Otherwise, returns
// the original error. Safe to call on nil errors.
func checkOrbError(logger zerolog.Logger, err error, name string) error {
	if err == nil {
		return nil
	}

	var apiErr *orb.Error
	if errors.As(err, &apiErr) {
		// This is the error that orb returns when an alert already exists. We need to check for this
		// even though we send an idempotency key because idempotency keys expire.
		if apiErr.Type == orb.ErrorTypeRequestValidationError {
			logger.Info().Msgf("%s: Ignoring request validation error, which we expect if this operation has already been performed successfully: %v", name, err)
			return nil
		}
	}
	return err
}

local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local ddl = import 'services/auth/central/server/spanner_ddl.jsonnet';

// Spanner database creation is pulled out into its own kubecfg target so that we can make sure we
// deploy the database before code that relies on it.
function(cloud, env, namespace, namespace_config)
  local appName = 'auth-central-spanner-database';
  local database = gcpLib.createSpannerDatabase(
    cloud,
    env,
    namespace,
    appName,
    'auth',
    ddl,
  );

  lib.flatten([
    database.objects,
  ])

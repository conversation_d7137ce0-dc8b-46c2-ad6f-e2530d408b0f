package main

import (
	"context"
	"testing"

	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMockAsyncOpsPublisher(t *testing.T) {
	// Create a mock publisher
	publisher := NewMockAsyncOpsPublisher()

	// Create a test message
	msg := &auth_internal.CreateTenantForTeamMessage{
		TenantCreationId: "test-id",
		TenantCreationRequest: &authpb.CreateTenantForTeamRequest{
			AdminUserId: "test-user",
		},
	}

	// Publish the message
	err := publisher.PublishTenantCreation(context.Background(), msg)
	require.NoError(t, err)

	// Verify the message was stored
	messages := publisher.GetPublishedMessages()
	require.Len(t, messages, 1)
	receivedMsg := messages[0].GetCreateTenantForTeamMessage()
	assert.Equal(t, "test-id", receivedMsg.TenantCreationId)
	assert.Equal(t, "test-user", receivedMsg.TenantCreationRequest.AdminUserId)

	// Clear the messages
	publisher.ClearMessages()
	messages = publisher.GetPublishedMessages()
	assert.Empty(t, messages)

	// Close the publisher
	err = publisher.Close()
	assert.NoError(t, err)
}

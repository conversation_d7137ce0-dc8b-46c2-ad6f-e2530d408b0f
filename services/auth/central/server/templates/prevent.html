{% if verisoul %}
<script src="https://js.verisoul.ai/{{ verisoul["env"] }}/bundle.js" verisoul-project-id="{{ verisoul["project_id"] }}"></script>
{% endif %}
{% if recaptcha_site_key %}
<script src="https://www.google.com/recaptcha/enterprise.js?render={{ recaptcha_site_key }}"></script>
{% endif %}
<script>
  async function onClick(e) {
    e.preventDefault();

    {% if verisoul %}
    const verisoulPromise = window.Verisoul.session();
    {% else %}
    const verisoulPromise = Promise.resolve({ session_id: '' });
    {% endif %}
    {% if recaptcha_site_key %}
    const grecaptchaPromise = (async () => {
      await new Promise((resolve) => {
        grecaptcha.enterprise.ready(resolve);
      });
      return await grecaptcha.enterprise.execute('{{ recaptcha_site_key }}', { action: getAction() });
    })();
    {% else %}
    const grecaptchaPromise = Promise.resolve('');
    {% endif %}
    const [verisoulSession, token] = await Promise.all([verisoulPromise, grecaptchaPromise]);
    document.getElementById('verisoul-session-id').value = verisoulSession.session_id;
    document.getElementById('g-recaptcha-response').value = token;
    document.getElementById('action-form').submit();
  }

  {% if recaptcha_site_key %}
  if (typeof window['grecaptcha'] === 'undefined') {
      alert('A component required for this page to work did not load. Please disable blocking software and reload the page.');
  }
  {% endif %}
</script>

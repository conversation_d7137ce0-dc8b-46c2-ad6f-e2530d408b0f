<!doctype html>
<html lang="en">
  <head>
    {% include 'common/html-head.html' %}

    <title>Augment Invitations</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" />

    <style type="text/css">
      {% include 'build/invitations.css' %}
    </style>
    <script>
      function updateButtonText(buttonElement, buttonText) {
        if (buttonElement) {
          buttonElement.textContent = buttonText;
        }
      }

      function updateStatus(statusText) {
        const statusElement = document.querySelector(".status-message");
        if (statusElement) {
          statusElement.textContent = statusText;
        }
      }

      // Useful when we need to disable all buttons during processing
      function enableAllButtons(enabled) {
        const allButtons = document.querySelectorAll(
          ".accept-button, .decline-button",
        );
        allButtons.forEach((button) => {
          button.disabled = !enabled;
        });
      }

      function resolveInvitation(invitationId, isAccepting) {
        const action = isAccepting ? "accept" : "decline";
        const buttonSelector = `button[onclick="${action}Invitation('${invitationId}')"]`;
        const button = document.querySelector(buttonSelector);
        const originalButtonText = isAccepting ? "Accept" : "Decline";
        const loadingText = isAccepting ? "Accepting..." : "Declining...";

        // Disable all buttons during processing
        enableAllButtons(false);

        updateButtonText(button, loadingText);

        // If accepting, collect all other invitation IDs to decline them
        let declineInvitationIds = [];
        if (isAccepting) {
          // Find all invitation cards/elements
          const invitationElements = document.querySelectorAll(
            '[id^="invitation-id-"]',
          );
          invitationElements.forEach((element) => {
            // Extract the invitation ID from the element ID
            const elementInvitationId = element.id.replace(
              "invitation-id-",
              "",
            );
            // Add all other invitation IDs to the decline list
            if (elementInvitationId !== invitationId) {
              declineInvitationIds.push(elementInvitationId);
            }
          });
        } else {
          // If declining, just add the current invitation ID
          declineInvitationIds = [invitationId];
        }

        const requestData = isAccepting
          ? {
              accept_invitation_id: invitationId,
              decline_invitation_ids: declineInvitationIds,
            }
          : {
              accept_invitation_id: null,
              decline_invitation_ids: declineInvitationIds,
            };

        fetch("/api/invitation/resolve", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(requestData),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success && data.invitation_resolution_id) {
              checkResolutionStatus(
                data.invitation_resolution_id,
                isAccepting,
                button,
                originalButtonText,
                invitationId,
              );
            } else {
              enableAllButtons(true);
              updateButtonText(button, originalButtonText);
              updateStatus(`Failed to ${action} invitation.`);
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            enableAllButtons(true);
            updateButtonText(button, originalButtonText);
            updateStatus(`Error ${action}ing invitation.`);
          });
      }

      function checkResolutionStatus(
        resolutionId,
        isAccepting,
        buttonElement,
        originalButtonText,
        invitationId,
      ) {
        const maxAttempts = 30;
        let attempts = 0;

        const pollInterval = setInterval(() => {
          attempts++;

          fetch(`/api/invitation/status/${resolutionId}`)
            .then((response) => response.json())
            .then((data) => {
              if (data.status === "SUCCESS") {
                clearInterval(pollInterval);

                if (isAccepting) {
                  window.location.href = "{{ continue_url|safe }}";
                } else {
                  setTimeout(() => {
                    // For single invitation view, reload the page
                    if (
                      document.querySelector(".single-invitation-container")
                    ) {
                      window.location.reload();
                      return;
                    }

                    // For multiple invitations view, remove the declined invitation
                    const invitationElement = document.getElementById(
                      `invitation-id-${invitationId}`,
                    );
                    if (invitationElement) {
                      invitationElement.remove();
                    }

                    const invitationsList =
                      document.getElementById("invitations-list");
                    if (
                      invitationsList &&
                      invitationsList.children.length === 0
                    ) {
                      window.location.reload();
                    } else {
                      enableAllButtons(true);
                    }
                  }, 1000);
                }
              } else if (data.status === "ERROR") {
                clearInterval(pollInterval);
                enableAllButtons(true);
                updateButtonText(buttonElement, originalButtonText);
                updateStatus("Error processing invitation.");
              } else if (attempts >= maxAttempts) {
                clearInterval(pollInterval);
                enableAllButtons(true);
                updateButtonText(buttonElement, originalButtonText);
                updateStatus("Operation timed out. Please try again.");
              }
            })
            .catch((error) => {
              console.error("Error polling status:", error);
              if (attempts >= maxAttempts) {
                clearInterval(pollInterval);
                enableAllButtons(true);
                updateButtonText(buttonElement, originalButtonText);
                updateStatus("Error checking status. Please try again.");
              }
            });
        }, 1000);
      }

      function acceptInvitation(invitationId) {
        resolveInvitation(invitationId, true);
      }

      function declineInvitation(invitationId) {
        resolveInvitation(invitationId, false);
      }
    </script>
  </head>

  <body>
    {% include 'common/l-oauth-open.html' %}
    <div class="invitations-container l-oauth__content">
      <header class="c-header" style="margin-bottom: 15px">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 233 32"
          height="100%"
          width="100%"
        >
          <path
            fill="currentColor"
            fill-rule="evenodd"
            d="M211.967 2.78h2.887v23h-2.887v-2.569c-1.047 1.809-2.856 2.982-5.584 2.982-3.902 0-7.297-3.236-7.297-8.883 0-5.615 3.395-8.883 7.297-8.883 2.728 0 4.537 1.174 5.584 2.982V2.78Zm-4.854 8.122c-2.951 0-4.886 2.348-4.886 6.408 0 4.061 1.935 6.409 4.886 6.409 2.569 0 4.98-1.904 4.98-6.409s-2.411-6.408-4.98-6.408Zm12.722 7.297c.127 3.68 2.728 5.456 5.266 5.456s3.934-1.142 4.664-2.823h2.982c-.793 2.95-3.49 5.361-7.646 5.361-5.393 0-8.375-3.87-8.375-8.914 0-5.394 3.616-8.852 8.28-8.852 5.203 0 8.344 4.378 7.963 9.772h-13.134Zm.032-2.475h10.056c-.063-2.411-1.871-4.822-4.917-4.822-2.57 0-4.886 1.396-5.139 4.822Zm-29.986 10.47c3.966 0 8.185-2.697 8.185-8.884 0-6.186-4.219-8.883-8.185-8.883s-8.185 2.697-8.185 8.883c0 6.187 4.219 8.883 8.185 8.883Zm5.108-8.884c0 4.378-2.507 6.345-5.108 6.345-2.601 0-5.108-1.808-5.108-6.345 0-4.663 2.507-6.345 5.108-6.345 2.601 0 5.108 1.809 5.108 6.345Zm-14.277-2.887h-2.919c-.412-2.03-2.189-3.458-4.41-3.458-2.569 0-4.917 2-4.917 6.282 0 4.346 2.379 6.408 4.917 6.408 2.411 0 4.125-1.618 4.537-3.426h2.982c-.571 3.204-3.648 5.964-7.614 5.964-4.885 0-7.963-3.712-7.963-8.914 0-5.076 3.204-8.852 8.249-8.852 4.124 0 6.757 3.11 7.138 5.996ZM155.38 4.43h-2.887v4.283h-2.697v2.316h2.697v10.47c0 3.552.761 4.282 4.029 4.282h2.094V23.37h-1.46c-1.649 0-1.776-.444-1.776-2.22V11.028h3.236V8.713h-3.236V4.43Zm-17.659 6.853c.983-1.618 2.633-2.856 5.52-2.856 4.124 0 5.615 2.665 5.615 6.568V25.78h-2.887v-9.93c0-2.633-.444-4.917-3.743-4.917-2.792 0-4.505 1.935-4.505 5.583v9.264h-2.887V8.713h2.887v2.57Zm-12.613 12.372c-2.538 0-5.139-1.776-5.266-5.456h13.134c.381-5.394-2.76-9.772-7.963-9.772-4.663 0-8.28 3.458-8.28 8.852 0 5.044 2.982 8.914 8.375 8.914 4.156 0 6.853-2.41 7.646-5.361h-2.982c-.73 1.681-2.126 2.823-4.664 2.823Zm4.823-7.93h-10.057c.254-3.427 2.569-4.823 5.139-4.823 3.046 0 4.854 2.411 4.918 4.822ZM93.767 25.78H90.88V8.713h2.887v2.57c.983-1.618 2.57-2.856 5.14-2.856 2.601 0 4.155 1.143 4.917 3.014 1.364-2.094 3.362-3.014 5.742-3.014 3.965 0 5.52 2.665 5.52 6.568V25.78h-2.887v-9.93c0-2.633-.508-4.917-3.648-4.917-2.475 0-4.125 1.935-4.125 5.583v9.264h-2.887v-9.93c0-2.633-.507-4.917-3.648-4.917-2.475 0-4.124 1.935-4.124 5.583v9.264ZM87.829 8.713h-2.887v2.697c-1.047-1.809-2.697-2.983-5.425-2.983-3.776 0-7.234 3.078-7.234 8.534 0 5.489 3.458 8.534 7.234 8.534 2.728 0 4.378-1.142 5.425-2.95v1.618c0 2.316-.476 3.109-1.11 3.775-.762.825-1.936 1.27-3.49 1.27-2.665 0-3.585-1.175-3.966-2.697h-3.078c.54 3.458 3.141 5.234 7.012 5.234 2.538 0 4.663-.825 5.9-2.252.984-1.079 1.619-2.506 1.619-5.996V8.713ZM75.424 16.96c0-3.87 1.999-6.06 4.822-6.06 2.57 0 4.823 1.746 4.823 6.06 0 4.347-2.253 6.092-4.823 6.092-2.823 0-4.822-2.19-4.822-6.092Zm-7.927 6.378c-.983 1.618-2.538 2.855-5.361 2.855-3.966 0-5.457-2.665-5.457-6.567V8.713h2.887V18.77c0 2.634.444 4.918 3.585 4.918 2.728 0 4.346-1.936 4.346-5.584v-9.39h2.887v17.068h-2.887v-2.443Zm-16.246-8.06c-7.01.794-11.548 1.968-11.548 5.997 0 3.14 2.665 4.917 5.87 4.917 3.013 0 4.79-1.015 5.837-2.76.032 1.015.127 1.713.222 2.348h2.919c-.318-1.618-.476-3.585-.445-6.44l.032-3.934c.032-4.79-2.094-7.043-6.789-7.043-3.331 0-6.567 2.062-6.852 5.742h2.982c.127-2.094 1.523-3.395 3.902-3.395 2.125 0 3.87 1.047 3.87 4.156v.413Zm-8.343 5.933c0-2.347 3.33-3.109 8.565-3.648v1.079c0 4.029-2.57 5.266-5.266 5.266-2.062 0-3.3-1.079-3.3-2.697ZM14.316.185c.184-.101.433-.14.722-.14s.54.04.725.147a.593.593 0 0 1 .313.536v.008l-.159 4.22a.494.494 0 0 1-.278.446c-.157.083-.366.113-.601.113s-.444-.03-.601-.113a.494.494 0 0 1-.279-.447c-.04-1.05-.072-1.864-.098-2.441v-.005a47.416 47.416 0 0 0-.04-1.257c-.013-.248-.02-.406-.02-.465V.708c0-.108.025-.213.082-.307a.612.612 0 0 1 .234-.216Zm3.354 0c.184-.101.434-.14.722-.14.29 0 .54.04.725.147a.592.592 0 0 1 .313.536v.008l-.158 4.22a.494.494 0 0 1-.28.446c-.156.083-.365.113-.6.113s-.444-.03-.6-.113a.493.493 0 0 1-.279-.447 332.95 332.95 0 0 0-.099-2.441v-.005a46.976 46.976 0 0 0-.04-1.257c-.012-.248-.02-.406-.02-.465V.708c0-.108.026-.213.083-.307a.611.611 0 0 1 .233-.216Zm2.782 6.572c.2-.18.446-.264.727-.264h6.267c.805 0 1.452.215 1.91.672.46.458.673 1.115.673 1.933v5.332c0 .622.127 1.033.335 1.273.201.231.585.383 1.221.398h.01c.254.019.47.118.634.304a.99.99 0 0 1 .24.68c0 .25-.076.475-.233.67a.81.81 0 0 1-.653.313c-.634.015-1.018.167-1.22.398-.209.241-.334.656-.334 1.297v5.332c0 .541-.094 1.013-.293 1.41-.2.402-.498.703-.89.905h-.001c-.39.198-.86.292-1.399.292h-6.058v.005h-.21c-.284 0-.531-.092-.73-.28a.916.916 0 0 1-.301-.68c0-.251.084-.479.251-.668a.88.88 0 0 1 .686-.294h5.82c.296 0 .484-.075.603-.196.119-.119.198-.32.198-.653v-5.38c0-.5.1-.957.304-1.372.2-.413.474-.742.82-.985a1.97 1.97 0 0 1 .176-.11 1.994 1.994 0 0 1-.176-.11 2.518 2.518 0 0 1-.82-.987 3.082 3.082 0 0 1-.304-1.37V9.264c0-.33-.079-.533-.198-.653-.12-.12-.31-.197-.603-.197h-5.82a.886.886 0 0 1-.685-.294.976.976 0 0 1-.252-.669c0-.275.1-.512.305-.695Zm0 0v.001l.14.155-.14-.156ZM3.904 7.17c.457-.457 1.105-.672 1.91-.672h6.267c.282 0 .527.087.727.264v.001c.202.182.306.42.306.694 0 .255-.085.48-.252.668a.88.88 0 0 1-.686.295h-5.82c-.295 0-.483.077-.603.197-.118.118-.198.32-.198.653v5.357c0 .498-.1.956-.303 1.37a2.538 2.538 0 0 1-.82.986 1.99 1.99 0 0 1-.177.11c.06.034.12.07.176.11.346.242.62.573.82.987.203.414.304.872.304 1.37v5.38c0 .333.08.535.198.654.12.121.31.197.603.197h5.82c.272 0 .507.096.685.292.17.19.252.417.252.67a.909.909 0 0 1-.3.679c-.2.19-.448.28-.732.28H5.605v-.01c-.453-.022-.851-.115-1.19-.287a1.982 1.982 0 0 1-.89-.904c-.197-.396-.294-.87-.294-1.411v-5.332c0-.637-.127-1.055-.335-1.297-.2-.23-.584-.382-1.219-.397a.802.802 0 0 1-.653-.315 1.044 1.044 0 0 1-.233-.67.99.99 0 0 1 .24-.679.912.912 0 0 1 .633-.303l.01-.001c.637-.016 1.022-.167 1.222-.398.21-.241.335-.65.335-1.273V9.103c0-.818.215-1.475.673-1.933Zm18.622 7.617a2.276 2.276 0 1 0 0 4.552 2.276 2.276 0 0 0 0-4.552ZM8.939 17.063a2.276 2.276 0 1 1 4.552 0 2.276 2.276 0 0 1-4.552 0Z"
            clip-rule="evenodd"
          />
        </svg>
      </header>

      {% if invitations %} {% if invitations|length == 1 %} {% set invitation =
      invitations[0] %}
      <h1>🚀 Join Your Team on Augment Code</h1>
      <div id="has-invitations" class="single-invitation-container">
        <div class="single-invitation-card">
          <div class="invitation-details">
            <p>
              <code>{{ invitation.inviter_email }}</code>
              has invited you to join their team on Augment Code.
            </p>

            {% if is_team_admin %}
            <div class="admin-warning">
              <p class="warning-text">
                You are currently an admin of your own team. To join another
                team, you'll need to contact support to dissolve your current
                team first.
              </p>
              <p>
                <a href="mailto:<EMAIL>" class="support-link"
                  >Contact Support</a
                >
              </p>
            </div>
            {% endif %}

            <div class="single-invitation-actions">
              <div class="button-group">
                {% if not is_team_admin %}
                <button
                  onclick="acceptInvitation('{{ invitation.id }}')"
                  class="accept-button"
                >
                  Accept Invite
                </button>
                {% endif %}
                <button
                  onclick="declineInvitation('{{ invitation.id }}')"
                  class="decline-button"
                >
                  Decline
                </button>
              </div>
              <div class="status-message"></div>
            </div>
            <p class="invitation-date">
              Invited on: {{ invitation.created_at }}
            </p>
          </div>
        </div>
      </div>
      {% else %}
      <h1>Team Invitations</h1>
      <div id="has-invitations">
        {% if is_team_admin %}
        <div class="admin-warning">
          <p class="warning-text">
            You are currently an admin of your own team. To join another team,
            you'll need to contact support to dissolve your current team first.
          </p>
          <p>
            <a href="mailto:<EMAIL>" class="support-link"
              >Contact Support</a
            >
          </p>
        </div>
        {% endif %}

        <p>You have been invited to join the following teams:</p>

        <div id="invitations-list" class="invitations-list">
          {% for invitation in invitations %}
          <div id="invitation-id-{{ invitation.id }}" class="invitation-card">
            <div class="invitation-details">
              <p>Invited by: {{ invitation.inviter_email }}</p>
              <p class="invitation-date">
                Invited on: {{ invitation.created_at }}
              </p>
            </div>
            <div class="invitation-actions">
              <div class="button-group">
                {% if not is_team_admin %}
                <button
                  onclick="acceptInvitation('{{ invitation.id }}')"
                  class="accept-button"
                >
                  Accept
                </button>
                {% endif %}
                <button
                  onclick="declineInvitation('{{ invitation.id }}')"
                  class="decline-button"
                >
                  Decline
                </button>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>

        <div class="status-message"></div>

        <div class="invitation-info">
          <p>
            {% if is_team_admin %} You can decline invitations, but to accept
            one you'll need to contact support first. {% else %} Accepting an
            invitation will associate you with that team and automatically
            decline all other pending invitations. {% endif %}
          </p>
        </div>
      </div>
      {% endif %} {% else %}
      <div id="no-invitations" class="no-invitations-container">
        <h1>No Team Invitations</h1>
        <p>
          You have no pending team invitations. If you believe this is an error,
          please contact support.
        </p>
        <p>
          <a href="mailto:<EMAIL>" class="support-link"
            >Contact Support</a
          >
        </p>
      </div>
      {% endif %}
    </div>
    {% include 'common/l-oauth-close.html' %}
  </body>
</html>

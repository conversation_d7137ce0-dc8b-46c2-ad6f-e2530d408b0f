package main

import (
	"context"
	"fmt"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	"github.com/augmentcode/augment/services/integrations/orb"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	professionalTierTenantID = "professional-tenant"
	enterpriseTierTenantID   = "enterprise-tenant"
)

type MockTokenExchangeClient struct {
	mock.Mock
}

func (m *MockTokenExchangeClient) GetSignedTokenForServiceWithNamespace(ctx context.Context, tenantID string, namespace string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	return secretstring.New("mock-token"), nil
}

func (m *MockTokenExchangeClient) Close() {}

func newTestTenantCreationProcessor(t *testing.T) (processor *TenantCreationProcessor, cleanup func()) {
	bigtableFixture := NewBigtableFixture(t)
	daoFactoryFixture := NewDAOFactoryFixture(bigtableFixture)

	professionalTenant := &tw_proto.Tenant{
		Id:             professionalTierTenantID,
		Name:           professionalTierTenantID,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "test-namespace-professional",
		Cloud:          "CLOUD_PROD",
	}
	enterpriseTenant := &tw_proto.Tenant{
		Id:             enterpriseTierTenantID,
		Name:           enterpriseTierTenantID,
		Tier:           tw_proto.TenantTier_ENTERPRISE,
		ShardNamespace: "test-namespace-enterprise",
		Cloud:          "CLOUD_PROD",
	}

	tenantChangeChannel := make(chan tw_client.TenantChange, 1)
	tenantChangeChannel <- tw_client.TenantChange{
		Response: &tw_proto.WatchTenantsResponse{
			Tenants: []*tw_proto.TenantChange{
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: professionalTenant,
						},
					},
				},
				{
					Type: &tw_proto.TenantChange_Updated{
						Updated: &tw_proto.TenantUpdate{
							Tenant: enterpriseTenant,
						},
					},
				},
			},
			IsInitial: true,
		},
	}

	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_proto.Tenant{
			professionalTenant,
			enterpriseTenant,
		},
		Changes: []chan tw_client.TenantChange{
			tenantChangeChannel,
		},
	}
	mockOrbClient := orb.NewMockOrbClient()
	tenantMap := NewTenantMap(
		daoFactoryFixture.DAOFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		featureflags.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	mockTokenExchange := &MockTokenExchangeClient{}

	processor, err := NewTenantCreationProcessor(
		daoFactoryFixture.DAOFactory,
		tenantMap,
		mockTokenExchange,
		mockOrbClient,
		ripublisher.NewRequestInsightPublisherMock(),
		audit.NewDefaultAuditLogger(),
	)
	require.NoError(t, err)

	cleanup = func() {
		bigtableFixture.Cleanup()
		daoFactoryFixture.Cleanup()
		tenantMap.Close()
		mockTokenExchange.Close()
	}

	return processor, cleanup
}

func TestProcessTenantCreation(t *testing.T) {
	processor, cleanup := newTestTenantCreationProcessor(t)
	defer cleanup()

	ctx := context.Background()
	oldTenantID := professionalTierTenantID

	adminUserID := uuid.New().String()
	adminEmail := "<EMAIL>"

	// Setup mock for CreateCustomer
	orbIndividualCustomerID := "orb_cus_" + adminUserID
	stripeIndividualCustomerID := "stripe_cus_" + adminUserID
	mockOrbClient := processor.orbClient.(*orb.MockOrbClient)

	mockOrbClient.On("CreateCustomer", ctx, orb.OrbCustomer{
		Email:    adminEmail,
		Name:     adminEmail,
		StripeID: stripeIndividualCustomerID,
		Metadata: map[string]string{
			"user_id": adminUserID,
		},
		Timezone:           "UTC",
		ExternalCustomerID: nil,
	}, true, mock.Anything).Return(orbIndividualCustomerID, nil)

	// Setup mock for GetUserSubscription to return a valid subscription info
	subscriptionID := "sub_" + uuid.New().String()
	subscriptionInfo := &orb.OrbSubscriptionInfo{
		OrbSubscriptionID: subscriptionID,
		ExternalPlanID:    "profession-plan-id",
	}
	mockOrbClient.On("GetUserSubscription", ctx, orbIndividualCustomerID, mock.Anything).Return(subscriptionInfo, nil)

	// Setup mocks for the CreateSubscription
	orbSubscription := orb.OrbSubscription{
		CustomerOrbID:  orbIndividualCustomerID,
		ExternalPlanID: "professional-plan-id",
	}
	mockOrbClient.On("CreateSubscription", ctx, orbSubscription, true).Return(subscriptionID, nil)

	// --- Testing ---
	// Create the subscription record
	subscriptionDAO := processor.daoFactory.GetSubscriptionDAO()

	subscription := &auth_entities.Subscription{
		SubscriptionId:    subscriptionID,
		StripeCustomerId:  "stripe_cus_" + adminUserID,
		OrbCustomerId:     orbIndividualCustomerID,
		PriceId:           "professional-plan-id",
		Status:            auth_entities.Subscription_ACTIVE,
		Seats:             1,
		CancelAtPeriodEnd: false,
		HasPaymentMethod:  true,
		Owner:             &auth_entities.Subscription_UserId{UserId: adminUserID},
	}
	_, err := subscriptionDAO.Create(ctx, subscription)
	require.NoError(t, err)

	// Create the user record
	userDAO := processor.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                adminUserID,
		Email:             adminEmail,
		Tenants:           []string{oldTenantID},
		StripeCustomerId:  stripeIndividualCustomerID,
		OrbCustomerId:     orbIndividualCustomerID,
		OrbSubscriptionId: subscriptionID,
	}
	_, err = userDAO.Create(ctx, user)
	require.NoError(t, err)

	// Create the tenant creation record
	tenantCreationDAO := processor.daoFactory.GetTenantCreationDAO()
	tenantCreationID := uuid.New().String()
	tenantCreation := &auth_entities.TenantCreation{
		Id:        tenantCreationID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.TenantCreation_PENDING,
	}
	_, err = tenantCreationDAO.Create(ctx, tenantCreation)
	require.NoError(t, err)

	// Create the message
	msg := &auth_internal.CreateTenantForTeamMessage{
		TenantCreationId:      tenantCreationID,
		TenantCreationRequest: &auth.CreateTenantForTeamRequest{AdminUserId: adminUserID},
		PublishTime:           timestamppb.Now(),
	}

	err = processor.Process(ctx, msg)
	require.NoError(t, err)

	// Verify the tenant creation record was updated
	updatedTenantCreation, err := tenantCreationDAO.Get(ctx, tenantCreationID)
	require.NoError(t, err)
	require.Equal(t, auth_entities.TenantCreation_SUCCESS, updatedTenantCreation.Status)

	newTenantID := updatedTenantCreation.TenantId
	require.NotEmpty(t, newTenantID)

	// Verify the new tenant was created with the correct properties
	currentTenant, err := processor.tenantMap.GetTenantByID(oldTenantID)
	require.NoError(t, err)
	require.NotNil(t, currentTenant)
	newTenant, err := processor.tenantMap.GetTenantByID(newTenantID)
	require.NoError(t, err)
	require.NotNil(t, newTenant)

	require.Equal(t, newTenantID, newTenant.Id)
	require.Equal(t, currentTenant.ShardNamespace, newTenant.ShardNamespace)
	require.Equal(t, currentTenant.Cloud, newTenant.Cloud)

	// Verify the user's tenant list was updated
	updatedUser, err := userDAO.Get(ctx, adminUserID)
	require.NoError(t, err)
	require.NotContains(t, updatedUser.Tenants, oldTenantID)
	require.Equal(t, 1, len(updatedUser.Tenants))
	require.Equal(t, updatedUser.Tenants[0], newTenantID)

	// Verify the user's tenant mapping was updated
	newTenantMappingDAO := processor.daoFactory.GetUserTenantMappingDAO(newTenant.Name)
	newTenantMapping, err := newTenantMappingDAO.GetByUser(ctx, adminUserID)
	require.NoError(t, err)
	require.NotNil(t, newTenantMapping)
	require.Equal(t, newTenant.Name, newTenantMapping.Tenant)
	require.Equal(t, adminUserID, newTenantMapping.UserId)
	require.Equal(t, []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN}, newTenantMapping.CustomerUiRoles)

	oldTenantMappingDAO := processor.daoFactory.GetUserTenantMappingDAO(currentTenant.Name)
	oldTenantMapping, err := oldTenantMappingDAO.GetByUser(ctx, adminUserID)
	require.NoError(t, err)
	require.Nil(t, oldTenantMapping)

	// Verify the tenant subscription mapping was created
	tenantSubscriptionMappingDAO := processor.daoFactory.GetTenantSubscriptionMappingDAO()
	tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, newTenantID)
	require.NoError(t, err)
	require.NotNil(t, tenantSubscriptionMapping)
	require.Equal(t, newTenantID, tenantSubscriptionMapping.TenantId)
	require.Equal(t, stripeIndividualCustomerID, tenantSubscriptionMapping.StripeCustomerId)
	require.Equal(t, orbIndividualCustomerID, tenantSubscriptionMapping.OrbCustomerId)
	require.Equal(t, subscriptionID, tenantSubscriptionMapping.OrbSubscriptionId)

	// Verify the subscription record was updated
	updatedSubscription, err := subscriptionDAO.Get(ctx, subscriptionID)
	require.NoError(t, err)
	require.NotNil(t, updatedSubscription)
	require.Equal(t, subscriptionID, updatedSubscription.SubscriptionId)
	require.Equal(t, stripeIndividualCustomerID, updatedSubscription.StripeCustomerId)
	require.Equal(t, auth_entities.Subscription_ACTIVE, subscription.Status)
	require.Equal(t, int32(1), updatedSubscription.Seats)
	require.Equal(t, &auth_entities.Subscription_TenantId{TenantId: newTenantID}, updatedSubscription.Owner)
}

func TestProcessTenantCreation_MissingTenantCreationRecord(t *testing.T) {
	processor, cleanup := newTestTenantCreationProcessor(t)
	defer cleanup()

	ctx := context.Background()
	tenantCreationID := uuid.New().String()

	// Create the message
	msg := &auth_internal.CreateTenantForTeamMessage{
		TenantCreationId:      tenantCreationID,
		TenantCreationRequest: &auth.CreateTenantForTeamRequest{},
		PublishTime:           timestamppb.Now(),
	}

	err := processor.Process(ctx, msg)
	require.Error(t, err)
}

func TestProcessTenantCreation_NoStripeCustomer(t *testing.T) {
	processor, cleanup := newTestTenantCreationProcessor(t)
	defer cleanup()

	ctx := context.Background()
	tenantCreationID := uuid.New().String()
	adminUserID := uuid.New().String()
	adminEmail := "<EMAIL>"

	// Create the user record without a Stripe customer ID
	userDAO := processor.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:      adminUserID,
		Email:   adminEmail,
		Tenants: []string{professionalTierTenantID},
	}
	_, err := userDAO.Create(ctx, user)
	require.NoError(t, err)

	// Create the tenant creation record
	tenantCreationDAO := processor.daoFactory.GetTenantCreationDAO()
	tenantCreation := &auth_entities.TenantCreation{
		Id:        tenantCreationID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.TenantCreation_PENDING,
	}
	_, err = tenantCreationDAO.Create(ctx, tenantCreation)
	require.NoError(t, err)

	// Create the message
	msg := &auth_internal.CreateTenantForTeamMessage{
		TenantCreationId:      tenantCreationID,
		TenantCreationRequest: &auth.CreateTenantForTeamRequest{AdminUserId: adminUserID},
		PublishTime:           timestamppb.Now(),
	}

	// Process should not panic but should set the status to ERROR
	err = processor.Process(ctx, msg)
	require.NoError(t, err) // No error returned because we don't want to retry

	// Verify the tenant creation record was updated with ERROR status
	updatedTenantCreation, err := tenantCreationDAO.Get(ctx, tenantCreationID)
	require.NoError(t, err)
	require.Equal(t, auth_entities.TenantCreation_ERROR, updatedTenantCreation.Status)
	require.Equal(t, "", updatedTenantCreation.TenantId)
}

func TestProcessTenantCreation_NoSubscription(t *testing.T) {
	processor, cleanup := newTestTenantCreationProcessor(t)
	defer cleanup()

	ctx := context.Background()
	tenantCreationID := uuid.New().String()
	adminUserID := uuid.New().String()
	adminEmail := "<EMAIL>"

	// Setup mock for CreateCustomer
	orbIndividualCustomerID := "orb_cus_" + adminUserID
	stripeIndividualCustomerID := "stripe_cus_" + adminUserID

	mockOrbClient := processor.orbClient.(*orb.MockOrbClient)
	mockOrbClient.On("CreateCustomer", ctx, orb.OrbCustomer{
		Email:    adminEmail,
		Name:     adminEmail,
		StripeID: stripeIndividualCustomerID,
		Metadata: map[string]string{
			"user_id": adminUserID,
		},
		Timezone:           "UTC",
		ExternalCustomerID: nil,
	}, true, mock.Anything).Return(orbIndividualCustomerID, nil)

	// Create the user record with a Orb customer ID but no subscription
	userDAO := processor.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:               adminUserID,
		Email:            adminEmail,
		Tenants:          []string{professionalTierTenantID},
		StripeCustomerId: stripeIndividualCustomerID,
		OrbCustomerId:    orbIndividualCustomerID,
	}
	var err error
	_, err = userDAO.Create(ctx, user)
	require.NoError(t, err)

	// Create the tenant creation record
	tenantCreationDAO := processor.daoFactory.GetTenantCreationDAO()
	tenantCreation := &auth_entities.TenantCreation{
		Id:        tenantCreationID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.TenantCreation_PENDING,
	}
	_, err = tenantCreationDAO.Create(ctx, tenantCreation)
	require.NoError(t, err)

	// Create the message
	msg := &auth_internal.CreateTenantForTeamMessage{
		TenantCreationId:      tenantCreationID,
		TenantCreationRequest: &auth.CreateTenantForTeamRequest{AdminUserId: adminUserID},
		PublishTime:           timestamppb.Now(),
	}

	// Process should not panic but should set the status to ERROR
	err = processor.Process(ctx, msg)
	require.NoError(t, err) // No error returned because we don't want to retry

	// Verify the tenant creation record was updated with ERROR status
	updatedTenantCreation, err := tenantCreationDAO.Get(ctx, tenantCreationID)
	require.NoError(t, err)
	require.Equal(t, auth_entities.TenantCreation_ERROR, updatedTenantCreation.Status)
	require.Equal(t, "", updatedTenantCreation.TenantId)
}

func TestProcessTenantCreationFailpointsAndRetry(t *testing.T) {
	panicPoints := []string{
		TenantCreationPanicPoints.CreateNewTenantError,
		TenantCreationPanicPoints.UpdateTenantCreationError,
		TenantCreationPanicPoints.MoveUserToTenantError,
		TenantCreationPanicPoints.UpdateUserTenantMappingError,
		TenantCreationPanicPoints.CreateTenantSubscriptionMappingError,
		TenantCreationPanicPoints.UpdateSubscriptionOwnerError,
	}

	for _, panicPoint := range panicPoints {
		t.Run(fmt.Sprintf("PanicPoint_%s", panicPoint), func(t *testing.T) {
			processor, cleanup := newTestTenantCreationProcessor(t)
			defer cleanup()

			ctx := context.Background()
			tenantCreationID := uuid.New().String()
			adminUserID := uuid.New().String()
			adminEmail := "<EMAIL>"

			// Setup mock for CreateCustomer
			orbIndividualCustomerID := "orb_cus_" + adminUserID
			stripeIndividualCustomerID := "stripe_cus_" + adminUserID

			mockOrbClient := processor.orbClient.(*orb.MockOrbClient)
			mockOrbClient.On("CreateCustomer", ctx, orb.OrbCustomer{
				Email:    adminEmail,
				Name:     adminEmail,
				StripeID: stripeIndividualCustomerID,
				Metadata: map[string]string{
					"user_id": adminUserID,
				},
				Timezone:           "UTC", // Default timezone
				ExternalCustomerID: nil,   // Not using external customer ID in this test
			}, true, mock.Anything).Return(orbIndividualCustomerID, nil)

			// Setup mock for GetUserSubscription to return a valid subscription info
			subscriptionID := "sub_" + uuid.New().String()
			subscriptionInfo := &orb.OrbSubscriptionInfo{
				OrbSubscriptionID: subscriptionID,
				ExternalPlanID:    "professional-plan-id",
			}
			mockOrbClient.On("GetUserSubscription", ctx, orbIndividualCustomerID, mock.Anything).Return(subscriptionInfo, nil)

			// Use the subscription ID from the mock
			individualSubscription := struct{ ID string }{ID: subscriptionID}

			// Create the subscription record
			subscriptionDAO := processor.daoFactory.GetSubscriptionDAO()
			subscription := &auth_entities.Subscription{
				SubscriptionId:    individualSubscription.ID,
				StripeCustomerId:  stripeIndividualCustomerID,
				OrbCustomerId:     orbIndividualCustomerID,
				PriceId:           "professional-plan-id",
				Status:            auth_entities.Subscription_ACTIVE,
				Seats:             1,
				CancelAtPeriodEnd: false,
				HasPaymentMethod:  true,
				Owner:             &auth_entities.Subscription_UserId{UserId: adminUserID},
			}
			var err error
			_, err = subscriptionDAO.Create(ctx, subscription)
			require.NoError(t, err)

			// Create the user record
			userDAO := processor.daoFactory.GetUserDAO()
			user := &auth_entities.User{
				Id:                adminUserID,
				Email:             adminEmail,
				Tenants:           []string{professionalTierTenantID},
				StripeCustomerId:  stripeIndividualCustomerID,
				OrbCustomerId:     orbIndividualCustomerID,
				OrbSubscriptionId: individualSubscription.ID,
			}
			_, err = userDAO.Create(ctx, user)
			require.NoError(t, err)

			// Create the tenant creation record
			tenantCreationDAO := processor.daoFactory.GetTenantCreationDAO()
			tenantCreation := &auth_entities.TenantCreation{
				Id:        tenantCreationID,
				CreatedAt: timestamppb.Now(),
				Status:    auth_entities.TenantCreation_PENDING,
			}
			_, err = tenantCreationDAO.Create(ctx, tenantCreation)
			require.NoError(t, err)

			// Create the message
			msg := &auth_internal.CreateTenantForTeamMessage{
				TenantCreationId:      tenantCreationID,
				TenantCreationRequest: &auth.CreateTenantForTeamRequest{AdminUserId: adminUserID},
				PublishTime:           timestamppb.Now(),
			}

			// Enable the current panic point
			test_utils.EnablePanicPoint(panicPoint)
			defer test_utils.DisableAllPanicPoints()

			// Process should panic due to the enabled panic point
			require.Panics(t, func() {
				_ = processor.Process(ctx, msg)
			})

			// Verify the tenant creation record was still PENDING status
			updatedTenantCreation, err := tenantCreationDAO.Get(ctx, tenantCreationID)
			require.NoError(t, err)
			require.NotNil(t, updatedTenantCreation)
			require.Equal(t, auth_entities.TenantCreation_PENDING, updatedTenantCreation.Status)

			// Disable the panic point and retry
			test_utils.DisablePanicPoint(panicPoint)
			err = processor.Process(ctx, msg)
			require.NoError(t, err)

			// Verify the tenant creation record was updated with SUCCESS status
			updatedTenantCreation, err = tenantCreationDAO.Get(ctx, tenantCreationID)
			require.NoError(t, err)
			require.NotNil(t, updatedTenantCreation)
			require.Equal(t, auth_entities.TenantCreation_SUCCESS, updatedTenantCreation.Status)
			require.NotEmpty(t, updatedTenantCreation.TenantId)
		})
	}
}

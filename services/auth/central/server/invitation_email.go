package main

import (
	"context"
	"fmt"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/rs/zerolog/log"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/integrations/customerio"
)

const (
	invitationEmailTransactionMessageID = "4"
	invitationEmailFromAddress          = "<EMAIL>"
)

var invitationEmailEnabled = featureflags.NewBoolFlag("auth_enable_team_invitation_email", false)

// InvitationEmailProcessor is responsible for processing invitation email messages from
// the async-ops pubsub topic.
type InvitationEmailProcessor struct {
	daoFactory          *DAOFactory
	customerioClient    customerio.CustomerioClient
	auditLogger         *audit.AuditLogger
	authCentralHostname string
	featureFlagHandle   featureflags.FeatureFlagHandle
}

func NewInvitationEmailProcessor(
	daoFactory *DAOFactory,
	customerioClient customerio.CustomerioClient,
	auditLogger *audit.AuditLogger,
	authCentralHostname string,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*InvitationEmailProcessor, error) {
	return &InvitationEmailProcessor{
		daoFactory:          daoFactory,
		customerioClient:    customerioClient,
		auditLogger:         auditLogger,
		authCentralHostname: authCentralHostname,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

// Process handles the invitation email message.
func (p *InvitationEmailProcessor) Process(
	ctx context.Context,
	msg *auth_internal.SendInvitationEmailMessage,
) error {
	log.Info().
		Str("tenant_id", msg.TenantId).
		Str("invitation_id", msg.InvitationId).
		Str("invitee_email", msg.InviteeEmail).
		Msg("Processing invitation email message")

	enabled, err := invitationEmailEnabled.Get(p.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get invitation email enabled feature flag, defaulting to false")
		enabled = false
	}
	if !enabled {
		log.Info().Msg("Invitation email is disabled, skipping email sending")
		return nil
	}

	if p.customerioClient == nil {
		log.Warn().Msg("CustomerioClient is not initialized, skipping email sending")
		return nil
	}

	// Get the invitation from the database to verify it exists and is still pending
	invitationDAO := p.daoFactory.GetTenantInvitationDAO(msg.TenantId)
	invitation, err := invitationDAO.Get(ctx, msg.InvitationId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get invitation")
		return fmt.Errorf("failed to get invitation: %w", err)
	}

	if invitation == nil {
		log.Warn().Msg("Invitation not found, skipping email sending")
		return nil
	}

	if invitation.Status != auth_entities.TenantInvitation_PENDING {
		log.Warn().Msg("Invitation is not pending, skipping email sending")
		return nil
	}

	email := customerio.CustomerioEmail{
		TransactionMessageID: invitationEmailTransactionMessageID,
		MessageData: map[string]interface{}{
			"inviter_email": invitation.InviterEmail,
			"signup_url": fmt.Sprintf(
				"%s/invitations",
				p.authCentralHostname,
			),
		},
		To:   invitation.InviteeEmail,
		From: invitationEmailFromAddress,
	}

	// Send the email
	err = p.customerioClient.SendEmail(ctx, email)
	if err != nil {
		log.Error().Err(err).Str("invitation_id", invitation.Id).Msg("Failed to send team invitation email")
		return err
	}

	log.Info().Str("invitation_id", invitation.Id).Msgf("Successfully sent team invitation email")
	return nil
}

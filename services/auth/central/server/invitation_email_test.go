package main

import (
	"context"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/integrations/customerio"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestInvitationEmailProcessor_Process(t *testing.T) {
	// Set up the bigtable fixture and DAO factory
	bigtableFixture := NewBigtableFixture(t)
	daoFactoryFixture := NewDAOFactoryFixture(bigtableFixture)
	defer daoFactoryFixture.Cleanup()

	// Create a mock feature flag handler
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a mock CustomerioClient
	mockCustomerioClient := customerio.NewMockCustomerioClient()
	mockCustomerioClient.On("SendEmail", mock.Anything, mock.Anything).Return(nil)

	// Create a mock audit logger
	mockAuditLogger := &audit.AuditLogger{}

	// Create the processor with a clean URL (no trailing slash)
	authCentralHostname := "https://auth.augmentcode.com"
	processor, err := NewInvitationEmailProcessor(
		daoFactoryFixture.DAOFactory,
		mockCustomerioClient,
		mockAuditLogger,
		authCentralHostname,
		featureFlagHandle,
	)
	require.NoError(t, err)

	// Create a test invitation
	tenantID := "test-tenant-id"
	invitationID := "test-invitation-id"
	invitationDAO := daoFactoryFixture.DAOFactory.GetTenantInvitationDAO(tenantID)
	invitation := &auth_entities.TenantInvitation{
		Id:            invitationID,
		CreatedAt:     timestamppb.Now(),
		InviteeEmail:  "<EMAIL>",
		TenantId:      tenantID,
		InviterUserId: "test-inviter-id",
		InviterEmail:  "<EMAIL>",
		Status:        auth_entities.TenantInvitation_PENDING,
	}

	// Save the invitation to the database
	_, err = invitationDAO.Create(context.Background(), invitation)
	require.NoError(t, err)

	// Create a test message
	message := &auth_internal.SendInvitationEmailMessage{
		TenantId:     tenantID,
		InvitationId: invitationID,
		InviteeEmail: "<EMAIL>",
		PublishTime:  timestamppb.Now(),
	}

	// Test with feature flag disabled
	featureFlagHandle.Set("auth_enable_team_invitation_email", false)

	err = processor.Process(context.Background(), message)
	require.NoError(t, err)

	// Verify that the CustomerioClient was not called
	mockCustomerioClient.AssertNumberOfCalls(t, "SendEmail", 0)

	// Test with feature flag enabled
	featureFlagHandle.Set("auth_enable_team_invitation_email", true)

	// Process the message
	err = processor.Process(context.Background(), message)
	require.NoError(t, err)

	// Verify that the CustomerioClient was called with the correct parameters
	mockCustomerioClient.AssertCalled(t, "SendEmail", mock.Anything, mock.MatchedBy(func(email customerio.CustomerioEmail) bool {
		assert.Equal(t, "<EMAIL>", email.To)
		assert.Equal(t, invitationEmailFromAddress, email.From)
		assert.Equal(t, invitationEmailTransactionMessageID, email.TransactionMessageID)

		signupURL := email.MessageData["signup_url"].(string)
		assert.Equal(t, "https://auth.augmentcode.com/invitations", signupURL, "signup_url should be correctly constructed")

		return true
	}))

	// Test with a non-existent invitation
	nonExistentMessage := &auth_internal.SendInvitationEmailMessage{
		TenantId:     tenantID,
		InvitationId: "non-existent-id",
		InviteeEmail: "<EMAIL>",
		PublishTime:  timestamppb.Now(),
	}

	// Process the message - should not error but should not send an email
	err = processor.Process(context.Background(), nonExistentMessage)
	require.NoError(t, err)

	// Verify that the CustomerioClient was called only once (for the first valid invitation)
	mockCustomerioClient.AssertNumberOfCalls(t, "SendEmail", 1)

	// Test with a non-pending invitation
	// Update the invitation status to ACCEPTED
	invitation.Status = auth_entities.TenantInvitation_ACCEPTED
	_, err = invitationDAO.Update(context.Background(), invitation)
	require.NoError(t, err)

	// Process the message again - should not error but should not send an email
	err = processor.Process(context.Background(), message)
	require.NoError(t, err)

	// Verify that the CustomerioClient was still called only once
	mockCustomerioClient.AssertNumberOfCalls(t, "SendEmail", 1)
}

"""Auth central server test setup for e2e tests."""

import base64
import grpc
import json
import os
import re
import sys
import html
import logging
import threading
import time
import tempfile
from http.server import ThreadingHTTPServer, BaseHTTPRequestHandler

from urllib.parse import parse_qs, urlparse
from dataclasses import asdict, dataclass
from typing import Any, Generator, List, Optional, Tuple
from urllib.parse import urlencode, urljoin

import jwt
import requests
from bs4 import BeautifulSoup, Tag
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.backends import default_backend
from python.runfiles import Runfiles

from base.test_utils import bigtable_setup, process
from services.auth.central.server import (
    front_end_token_service_pb2,
    front_end_token_service_pb2_grpc,
)

from services.auth.central.server.config import (
    Auth0Config,
    AuthConfig,
    Config,
    Gcp<PERSON>onfig,
    Grpc<PERSON>onfig,
    Login<PERSON>age<PERSON>onfi<PERSON>,
    OAuthClientConfig,
    TenantWatcherConfig,
)

VALID_CODE_CHALLENGE = "eqEvNh2v4-XpxstYZNyL0t2YwSjxTdRQFObvgMTestU"
VALID_CODE_VERIFIER = "9683e0859a76cde1e20898885833ea4d90f0b48adcec16f751405d59e509f8b0"


class FakeOidcServer:
    """Fake OIDC server."""

    def __init__(self, url: str):
        self._url = url
        self._userinfo = {"sub": "fake-sub"}
        self._mutex = threading.Lock()

    def url(self) -> str:
        """Return the base URL for the OIDC server."""
        return self._url

    def well_known_config_url(self) -> str:
        """Return the well-known configuration URL for the OIDC server."""
        return self._url + "/.well-known/openid-configuration"

    def set_userinfo(self, userinfo: dict[str, Any]):
        """Set the userinfo to be returned by the OIDC server.

        This is merged into the ID token as well as served by /userinfo.
        """
        with self._mutex:
            self._userinfo = userinfo

    def get_userinfo(self) -> dict[str, Any]:
        with self._mutex:
            return self._userinfo


def start_fake_oidc_server() -> Generator[FakeOidcServer, None, None]:
    """Fixture to start and return a fake OIDC server.

    This fake has only been tested with authlib. More work may be needed for
    other libraries.
    """

    fake_oidc_server = FakeOidcServer("")

    private_key = ec.generate_private_key(ec.SECP256R1(), default_backend())
    public_key = private_key.public_key()
    numbers = public_key.public_numbers()
    jwks = {
        "keys": [
            {
                "kty": "EC",
                "crv": "P-256",
                "alg": "ES256",
                "kid": "fake-kid",
                "use": "sig",
                "x": base64.urlsafe_b64encode(
                    numbers.x.to_bytes((numbers.x.bit_length() + 7) // 8, "big")
                )
                .decode("utf-8")
                .rstrip("="),
                "y": base64.urlsafe_b64encode(
                    numbers.y.to_bytes((numbers.y.bit_length() + 7) // 8, "big")
                )
                .decode("utf-8")
                .rstrip("="),
            }
        ]
    }

    class FakeOidcHandler(BaseHTTPRequestHandler):
        def reply_with_token(self, client_id: str):
            now = int(time.time())
            payload = {
                "iss": fake_oidc_server.url(),
                "aud": client_id,
                "iat": now,
                "exp": now + 3600,  # 1 hour expiry
                **fake_oidc_server.get_userinfo(),
            }
            token = jwt.encode(
                payload, private_key, algorithm="ES256", headers={"kid": "fake-kid"}
            )

            self.send_response(200)
            self.send_header("Content-Type", "application/json")
            self.end_headers()
            self.wfile.write(
                json.dumps(
                    {
                        "id_token": token,
                        "access_token": "fake-access-token",
                        "token_type": "Bearer",
                        "expires_in": 3600,
                    }
                ).encode()
            )

        def do_POST(self):
            self.log_request()
            if self.path == "/token":
                content_type = self.headers["Content-Type"]
                content_length = int(self.headers["Content-Length"])
                body = self.rfile.read(content_length).decode("utf-8")

                # Parse out client_id
                authorization = self.headers.get("Authorization", None)
                if authorization is not None:
                    fields = authorization.split()
                    if len(fields) == 2 and fields[0] == "Basic":
                        client_id, _ = (
                            base64.b64decode(fields[1]).decode().split(":", 2)
                        )
                    else:
                        self.send_response(400)
                        self.end_headers()
                        return
                elif content_type == "application/x-www-form-urlencoded":
                    data = parse_qs(body)
                    client_id = data["client_id"][0]
                elif content_type == "application/json":
                    data = json.loads(body)
                    client_id = data["client_id"]
                else:
                    self.send_response(400)
                    self.end_headers()
                    return

                self.reply_with_token(client_id)
            elif self.path == "/userinfo":
                self.send_response(405)
                self.end_headers()
            else:
                self.send_response(404)
                self.end_headers()

        def do_GET(self):
            self.log_request()
            if self.path == "/.well-known/openid-configuration":
                self.send_response(200)
                self.send_header("Content-Type", "application/json")
                self.end_headers()
                url = fake_oidc_server.url()
                config = {
                    "issuer": url,
                    "authorization_endpoint": url + "/authorize",
                    "token_endpoint": url + "/token",
                    "userinfo_endpoint": url + "/userinfo",
                    "jwks_uri": url + "/jwks",
                    "response_types_supported": ["code"],
                    "subject_types_supported": ["public"],
                }
                self.wfile.write(json.dumps(config).encode())
            elif self.path == "/jwks":
                self.send_response(200)
                self.send_header("Content-Type", "application/json")
                self.end_headers()
                self.wfile.write(json.dumps(jwks).encode())
            elif self.path == "/token":
                # Method not allowed
                self.send_response(405)
                self.end_headers()
            elif self.path == "/userinfo":
                self.send_response(200)
                self.send_header("Content-Type", "application/json")
                self.end_headers()
                self.wfile.write(json.dumps(fake_oidc_server.get_userinfo()).encode())
            else:
                self.send_response(404)
                self.end_headers()

    server = ThreadingHTTPServer(("localhost", 0), FakeOidcHandler)
    fake_oidc_server = FakeOidcServer(f"http://localhost:{server.server_port}")

    thread = threading.Thread(target=server.serve_forever, daemon=True)
    thread.start()
    logging.info("Fake OIDC server started on port %d", server.server_port)

    try:
        yield fake_oidc_server
    finally:
        logging.info("Fake OIDC server shutting down")
        server.shutdown()
        logging.info("Fake OIDC server closing server socket")
        server.server_close()
        logging.info("Fake OIDC server joining serve_forever")
        thread.join()
        logging.info("Fake OIDC server cleanup done")


class AuthCentralHttpServer:
    """Auth central HTTP server."""

    def __init__(self, http_url: str, fake_oidc_server: FakeOidcServer):
        self._http_url = http_url
        self.fake_oidc_server = fake_oidc_server
        self.session = requests.Session()

    def setup_test(self):
        response = requests.post(
            self.url("/flag"),
            json={},
            timeout=5,
        )
        assert response.status_code == 200

    def url(self, path: str, query: Optional[List[Tuple[str, str]]] = None):
        return urljoin(self._http_url, path) + ("?" + urlencode(query) if query else "")

    def accept(
        self,
        authorize_params: list[tuple[str, str]],
        userinfo: dict[str, str],
    ) -> requests.Response:
        login_url = self.url("/login")
        response = self.session.get(
            self.url("/login"),
            params=authorize_params,
            timeout=60,
            allow_redirects=False,
        )
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            tag = soup.find("a", id="redirect_url")
            assert tag is not None, f"Failed to find redirect_url in {response.text}"
            assert isinstance(
                tag, Tag
            ), f"Failed to find redirect_url in {response.text}"
            internal_redirect_uri = tag.attrs["href"]
        elif response.status_code == 302:
            internal_redirect_uri = response.headers["Location"]
        else:
            assert (
                False
            ), f"{login_url} returned {response.status_code} {response.headers}"

        parsed_qs = parse_qs(urlparse(internal_redirect_uri).query)
        assert "state" in parsed_qs, f"{internal_redirect_uri} does not contain state"
        assert "nonce" in parsed_qs, f"{internal_redirect_uri} does not contain nonce"

        self.fake_oidc_server.set_userinfo(
            {
                "email_verified": True,
                "nonce": parsed_qs["nonce"][0],
            }
            | userinfo,
        )

        response = self.session.get(
            parsed_qs["redirect_uri"][0],
            params={
                "code": "fake-code",
                "state": parsed_qs["state"][0],
            },
            timeout=60,
            allow_redirects=False,
        )
        assert response.status_code < 400

        return self.session.post(
            self.url("/terms-accept"),
            params=authorize_params,
            data={"continue": "continue", "terms-of-service": "accepted"},
            timeout=60,
        )

    def token(
        self,
        client_id: str,
        redirect_uri: str,
        code: str,
        grant_type: str = "authorization_code",
        code_verifier: str = VALID_CODE_VERIFIER,
    ) -> requests.Response:
        response = requests.post(
            self.url("token"),
            json={
                "code": code,
                "client_id": client_id,
                "redirect_uri": redirect_uri,
                "grant_type": grant_type,
                "code_verifier": code_verifier,
            },
            timeout=60,
        )
        return response

    def get_redirect_url_from_authorize_response(
        self, response: requests.Response
    ) -> Tuple[str, dict]:
        print(response.text)
        url = re.search(r'"redirect_url" href="([^"]*)', response.text)
        assert url
        parts = urlparse(html.unescape(url.group(1)))
        qs = parse_qs(parts.query, strict_parsing=True)
        return url.group(1), qs

    def get_redirect_url_from_dummy_server_response(
        self, response: requests.Response
    ) -> Tuple[str, dict]:
        path = response.json()["path"]
        parts = urlparse(path)
        qs = parse_qs(parts.query, strict_parsing=True)
        return path, qs


class AuthCentralGrpcServer:
    """Auth central GRPC server."""

    def __init__(self, grpc_port: str, private_port: str):
        self.grpc_port = grpc_port
        self.private_port = private_port

    def shutdown(self):
        backend_channel = grpc.insecure_channel(f"localhost:{self.private_port}")
        front_end_token_service = (
            front_end_token_service_pb2_grpc.FrontEndTokenServiceStub(backend_channel)
        )
        logging.info("Sending shutdown to front end token service")
        try:
            front_end_token_service.Shutdown(
                front_end_token_service_pb2.ShutdownRequest()
            )
            logging.info("Shutdown call complete")
        except grpc.RpcError as e:
            logging.warning("Failed to shutdown front end token service: %s", e)


@dataclass(frozen=True)
class AuthCentralConfig(Config):
    """Configuration for the Auth-Central service."""

    def __init__(
        self,
        bigtable_table: bigtable_setup.BigtableTable,
        auth_config: AuthConfig,
        client_config_map: dict[str, OAuthClientConfig],
        bind_address: str = "127.0.0.1:0",
        prometheus_bind_address: str = "127.0.0.1:0",
        grpc_server: GrpcConfig = GrpcConfig(
            client_mtls=None,
        ),
        login_page: LoginPageConfig = LoginPageConfig(
            auth0_login_url="https://accounts.bogus-auth0.url",
        ),
        tenant_watcher: TenantWatcherConfig = TenantWatcherConfig(
            tenant_watcher_endpoint="",
            api_proxy_hostname_domain="",
        ),
        secrets_path: str = "bogus-path/auth.json",
        backend_port: int = 0,
        login_auth0: Auth0Config | None = None,
    ):
        super().__init__(
            auth_config=auth_config,
            auth_url="https://auth.bogus.url/",
            backend_port=backend_port,
            client_config_map=client_config_map,
            feature_flags_sdk_key_path=None,
            dynamic_feature_flags_endpoint=None,
            gcp=GcpConfig(
                instance_id=bigtable_table.instance,
                table_name=bigtable_table.table_name,
            ),
            grpc=grpc_server,
            login_page=login_page,
            public_bind_address=bind_address,
            prometheus_bind_address=prometheus_bind_address,
            tenant_watcher=tenant_watcher,
            code_ttl_seconds=600,
            secrets_path=secrets_path,
            enable_flag_endpoint=True,
            login_auth0=login_auth0,
        )


def start_auth_central_http_server(
    namespace: str,
    config: AuthCentralConfig,
    fake_oidc_server: FakeOidcServer,
    runfiles: Runfiles | None = None,
) -> Generator[AuthCentralHttpServer, None, None]:
    """Fixture to start the auth central http server."""
    if runfiles is None:
        runfiles = Runfiles.Create()
        assert runfiles is not None, "Failed to create runfiles"

    with (
        tempfile.NamedTemporaryFile(mode="w+t") as config_file,
        tempfile.TemporaryDirectory() as prometheus_dir,
    ):
        config_content = json.dumps(asdict(config))
        config_file.write(config_content)
        config_file.flush()

        env = os.environ.copy()
        env["PROMETHEUS_MULTIPROC_DIR"] = prometheus_dir
        env["POD_NAMESPACE"] = namespace
        app_py_path = runfiles.Rlocation("_main/services/auth/central/server/app.py")
        assert app_py_path is not None, "Failed to find app.py"

        with process.ServerManager(
            [
                sys.executable,
                app_py_path,
                "--config",
                config_file.name,
                # Disable secure cookie for testing, since secure cookies will
                # not be passed to http servers.
                "--disable-secure-cookie-for-testing",
            ],
            redirect_stderr=True,
            env=env,
        ) as p:
            http_port = process.wait_for_line(
                p.stdout,
                r"Listening at:.*127\.0\.0\.1:(\d+)",
                timeout_secs=30,
            )
            p.detach_stdout()
            http_port = http_port.group(1)
            yield AuthCentralHttpServer(
                "http://127.0.0.1:" + http_port, fake_oidc_server
            )


@dataclass(frozen=True)
class GrpcGoConfig:
    """Configuration for the Auth-central GRPC service."""

    ports: List[int]
    private_port: int = 0
    enabled: bool = True
    max_server_workers: int = 1
    server_mtls: Optional[GcpConfig] = None
    client_mtls: Optional[GcpConfig] = None
    shutdown_grace_period_s: float = 30


@dataclass(frozen=True)
class GcpGoConfig:
    """Configuration for the Auth-central GRPC service."""

    instance_id: str
    project_id: str
    table_name: str
    setup_table: bool = False


@dataclass(frozen=True)
class RevokerConfig:
    """Configuration for the Auth-central GRPC service."""

    subscription_name: str


@dataclass(frozen=True)
class AsyncOpsConfig:
    """Configuration for async operations."""

    topic_name: str
    subscription_name: str
    dead_letter_subscription_name: str = ""
    max_concurrent_receivers: int = 0
    dead_letter_max_concurrent_receivers: int = 0


@dataclass(frozen=True)
class PlanFeatures:
    """Plan features configuration."""

    plan_type: str
    training_allowed: bool = False
    teams_allowed: bool = True
    max_seats: int = 100
    add_credits_available: bool = False


@dataclass(frozen=True)
class PlanConfig:
    """Plan configuration."""

    id: str
    features: PlanFeatures


@dataclass(frozen=True)
class OrbConfig:
    """Configuration for Orb billing."""

    enabled: bool
    plans: List[PlanConfig]
    api_key_path: str = ""
    seats_item_id: str = ""
    included_messages_item_id: str = ""
    pricing_unit: str = ""
    cost_per_message: float = 0.0
    min_addon_purchase: float = 0.0
    max_addon_purchase: float = 0.0


@dataclass(frozen=True)
class GoServerConfig:
    """Configuration for the Auth-central GRPC service."""

    auth_config: AuthConfig
    dynamic_feature_flags_endpoint: Optional[str]
    feature_flags_sdk_key_path: Optional[str]
    request_insight_publisher_config_path: Optional[str]
    gcp: GcpGoConfig
    grpc: GrpcGoConfig
    tenant_watcher: TenantWatcherConfig
    prometheus_bind_address: str
    revoker: RevokerConfig
    async_ops: AsyncOpsConfig
    orb: Optional[OrbConfig] = None


@dataclass(frozen=True)
class AuthCentralGrpcConfig(GoServerConfig):
    """Configuration for the Auth-central GRPC service."""

    def __init__(
        self,
        bigtable_table: bigtable_setup.BigtableTable,
        auth_config: AuthConfig,
        prometheus_bind_address: str = "127.0.0.1:0",
        grpc_server: GrpcGoConfig = GrpcGoConfig(ports=[0]),
        tenant_watcher: TenantWatcherConfig = TenantWatcherConfig(
            tenant_watcher_endpoint="",
            api_proxy_hostname_domain="",
        ),
        feature_flags_sdk_key_path: str = "",
        request_insight_publisher_config_path: str = "",
        dynamic_feature_flags_endpoint: Optional[str] = None,
        revoker: RevokerConfig = RevokerConfig(subscription_name=""),
        async_ops: AsyncOpsConfig = AsyncOpsConfig(
            topic_name="",
            subscription_name="",
        ),
        orb: Optional[OrbConfig] = None,
    ):
        super().__init__(
            auth_config=auth_config,
            dynamic_feature_flags_endpoint=dynamic_feature_flags_endpoint,
            feature_flags_sdk_key_path=feature_flags_sdk_key_path,
            request_insight_publisher_config_path=request_insight_publisher_config_path,
            gcp=GcpGoConfig(
                instance_id=bigtable_table.instance,
                project_id=bigtable_table.project
                if bigtable_table.project
                else "google-cloud-bigtable-emulator",
                table_name=bigtable_table.table_name,
            ),
            grpc=grpc_server,
            tenant_watcher=tenant_watcher,
            prometheus_bind_address=prometheus_bind_address,
            revoker=revoker,
            async_ops=async_ops,
            orb=orb,
        )


def start_auth_central_grpc_server(
    namespace: str,
    config: AuthCentralGrpcConfig,
    runfiles: Runfiles | None = None,
) -> Generator[AuthCentralGrpcServer, None, None]:
    """Fixture to start the auth central gRPC server using the Go implementation."""
    logging.info("start_auth_central_grpc_server called")

    if runfiles is None:
        runfiles = Runfiles.Create()
        assert runfiles is not None, "Failed to create runfiles"

    with tempfile.NamedTemporaryFile(mode="w+t", suffix=".json") as config_file:
        config_content = json.dumps(asdict(config))
        config_file.write(config_content)
        config_file.flush()

        env = os.environ.copy()
        env["POD_NAMESPACE"] = namespace

        grpc_server_path = runfiles.Rlocation(
            "_main/services/auth/central/server/grpc_server_/grpc_server",
        )
        assert grpc_server_path is not None, "Failed to find grpc_server binary"

        with process.ServerManager(
            [
                grpc_server_path,
                "--config",
                config_file.name,
            ],
            redirect_stderr=True,
            env=env,
        ) as p:
            server_up_line = process.wait_for_line(
                p.stdout,
                r".*Server up.*",
                timeout_secs=30,
            )
            p.detach_stdout()
            server_up_status = json.loads(server_up_line.group(0))

            server = AuthCentralGrpcServer(
                server_up_status["public_port"], server_up_status["private_port"]
            )

            try:
                logging.info(
                    f"Yielding gRPC server with ports: {server.grpc_port}/{server.private_port}"
                )
                yield server
            finally:
                try:
                    # Try to shut down gracefully
                    server.shutdown()
                except Exception as e:
                    logging.warning(f"Error during server.shutdown(): {e}")

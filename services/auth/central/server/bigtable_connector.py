"""Opens connections to bigtable and initializes table if needed."""

import logging
import os
import pathlib
from dataclasses import dataclass
from pathlib import Path
from typing import List

from dataclasses_json import dataclass_json
from google.cloud import bigtable  # type: ignore
from google.cloud.bigtable.table import Table


@dataclass_json
@dataclass
class ColumnFamilyItem:
    """Column family item."""

    family: str


@dataclass_json
@dataclass
class ColumnFamilyConfig:
    """Column family configuration."""

    items: List[ColumnFamilyItem]

    @classmethod
    def load(cls, config_file: pathlib.Path):
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )


def _connect(
    instance_id: str,
    table_name: str,
    admin: bool = False,
    project_id: str | None = None,
) -> Table:
    """Connect to the bigtable instance.

    Args:
        instance_id: The id of the bigtable instance.
        table_name: The name of the bigtable table.
        admin: If true, create the table and column family if it does not exist.
        project_id: The id of the bigtable project.
    """
    client = bigtable.Client(admin=admin, project=project_id)
    instance = client.instance(instance_id)
    table = instance.table(table_name)
    return table


# expected to be used only in dev
def setup_table(
    instance_id: str, table_name: str, project_id: str | None = None
) -> None:
    """Initialize the database.

    Args:
        instance_id: The id of the bigtable instance.
        table_name: The name of the bigtable table.
        project_id: The id of the bigtable project.
    """
    table = _connect(instance_id, table_name, admin=True, project_id=project_id)
    if not table.exists():
        table.create()
    module_dir = Path(os.path.abspath(__file__)).parent
    column_family: List[ColumnFamilyItem] = ColumnFamilyConfig.load(
        pathlib.Path(os.path.join(module_dir, "./column_family.json"))
    ).items
    for item in column_family:
        if item.family not in table.list_column_families():
            logging.info("Creating column family [%s]", item.family)
            table.column_family(item.family).create()
        else:
            logging.info("Column family [%s] already exists", item.family)


def connect(
    instance_id: str,
    table_name: str,
    project_id: str | None = None,
) -> Table:
    """Connect to the bigtable instance.

    Args:
        instance_id: The id of the bigtable instance.
        table_name: The name of the bigtable table.
        init: If true, create the table and column family if it does not exist.
        project_id: The id of the bigtable project.
    """
    return _connect(instance_id, table_name, project_id=project_id)

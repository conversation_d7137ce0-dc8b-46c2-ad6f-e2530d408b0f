"""Test tenant map."""

import pytest
import uuid
from typing import Generator
from unittest.mock import MagicMock

from services.auth.central.server.tenant_map import TenantMap

from services.auth.central.server import auth_entities_pb2
from services.tenant_watcher import tenant_watcher_pb2
from services.tenant_watcher.client.client import TenantsClient
from base.feature_flags import LocalFeatureFlagSetter, feature_flag_fixture


def create_tenant_change(
    tenant: tenant_watcher_pb2.Tenant, updated=True
) -> tenant_watcher_pb2.TenantChange:
    if updated:
        return tenant_watcher_pb2.TenantChange(
            updated=tenant_watcher_pb2.TenantUpdate(tenant=tenant)
        )
    else:
        return tenant_watcher_pb2.TenantChange(
            removed=tenant_watcher_pb2.TenantRemoval(tenant=tenant)
        )


@pytest.fixture
def feature_flags() -> Generator[LocalFeatureFlagSetter, None, None]:
    yield from feature_flag_fixture()


@pytest.fixture()
def tenant_map(feature_flags):
    tenant = tenant_watcher_pb2.Tenant(
        id="augment-1234567890",
        name="Augment",
        shard_namespace="augment",
        cloud="CLOUD_PROD",
        auth_configuration=tenant_watcher_pb2.AuthConfiguration(
            domain="augmentcode.com",
        ),
        tier=tenant_watcher_pb2.TenantTier.ENTERPRISE,
    )

    second_tenant = tenant_watcher_pb2.Tenant(
        id="id-second-tenant",
        name="Second Tenant",
        shard_namespace="second-tenant",
        cloud="CLOUD_PROD",
        auth_configuration=tenant_watcher_pb2.AuthConfiguration(
            domain="second-tenant.com",
        ),
        tier=tenant_watcher_pb2.TenantTier.ENTERPRISE,
    )

    tenant_watcher_client = MagicMock(TenantsClient)
    watch_tenants_response = tenant_watcher_pb2.WatchTenantsResponse(
        tenants=[create_tenant_change(tenant), create_tenant_change(second_tenant)],
        is_initial=True,
    )

    def watch_tenants(_):
        yield watch_tenants_response

    tenant_watcher_client.watch_tenants.side_effect = watch_tenants

    tenant_map = TenantMap(
        tenant_watcher_client=tenant_watcher_client,
        api_proxy_hostname_domain="us-central.api.augmentcode.com",
    )

    yield tenant_map


def test_no_matching_tenant(tenant_map):
    got = tenant_map.get_tenant_for_email_domain("<EMAIL>")
    assert got is None

    tenant = tenant_map.get_tenant("Augment_")
    assert not tenant


def test_matching_tenant(tenant_map):
    got = tenant_map.get_tenant_for_email_domain("<EMAIL>")
    assert got is not None
    got = tenant_map.tenant_to_tenant_details(got)
    assert got.name == "Augment"
    assert got.namespace == "augment"
    assert got.domain == "augmentcode.com"
    assert got.tenant_id == "augment-1234567890"
    assert got.auth_url_v2 == "https://augment.us-central.api.augmentcode.com/authorize"
    assert got.tenant_url == "https://augment.us-central.api.augmentcode.com/"
    assert got.community_tos is False

package main

import (
	"context"
	"slices"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	front_end_token_service_pb "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

type tenantMapTestEnv struct {
	ctx             context.Context
	bigtableFixture *BigtableFixture
	daoFixture      *DAOFactoryFixture
	featureFlags    *featureflags.LocalFeatureFlagHandler
	mockClient      *tw_client.MockTenantWatcherClient
	tenantMap       *TenantMap

	deletedTenantID    string
	codeTenantID       string
	augmentTenantID    string
	enterpriseTenantID string
}

func newTenantMapTestEnv(t *testing.T) *tenantMapTestEnv {
	bigtableFixture := NewBigtableFixture(t)
	daoFixture := NewDAOFactoryFixture(bigtableFixture)
	featureFlagHandler := featureflags.NewLocalFeatureFlagHandler()

	tenantAugment := &tenantproto.Tenant{
		Id:             "augment-1234567890",
		Name:           "Augment",
		ShardNamespace: "augment",
		Cloud:          "CLOUD_PROD",
		AuthConfiguration: &tenantproto.AuthConfiguration{
			Domain: "augmentcode.com",
		},
	}

	tenantCode := &tenantproto.Tenant{
		Id:             "code-1234567890",
		Name:           "Code",
		ShardNamespace: "code",
		Cloud:          "CLOUD_PROD",
		AuthConfiguration: &tenantproto.AuthConfiguration{
			Domain: "code.com",
		},
	}

	deletedTenant := &tenantproto.Tenant{
		Id:             "deleted-0987654321",
		Name:           "Deleted",
		ShardNamespace: "augment",
		Cloud:          "CLOUD_PROD",
		DeletedAt:      "2023-01-01T00:00:00Z",
	}

	enterpriseTenant := &tenantproto.Tenant{
		Id:             "enterprise-1234567890",
		Name:           "Enterprise",
		ShardNamespace: "enterprise",
		Cloud:          "CLOUD_PROD",
		AuthConfiguration: &tenantproto.AuthConfiguration{
			Domain: "enterprise.com",
		},
		Tier: tenantproto.TenantTier_ENTERPRISE,
	}

	mockClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tenantproto.Tenant{tenantAugment, tenantCode, deletedTenant, enterpriseTenant},
	}

	tm := NewTenantMap(
		daoFixture.DAOFactory,
		mockClient,
		"us-central.api.augmentcode.com",
		featureFlagHandler,
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	return &tenantMapTestEnv{
		ctx:             bigtableFixture.Ctx,
		bigtableFixture: bigtableFixture,
		daoFixture:      daoFixture,
		featureFlags:    featureFlagHandler,
		mockClient:      mockClient,
		tenantMap:       tm,

		deletedTenantID:    deletedTenant.Id,
		codeTenantID:       tenantCode.Id,
		augmentTenantID:    tenantAugment.Id,
		enterpriseTenantID: enterpriseTenant.Id,
	}
}

func (env *tenantMapTestEnv) Reset(t *testing.T) {
	env.bigtableFixture.ClearTable(t)
	env.tenantMap = NewTenantMap(
		env.daoFixture.DAOFactory,
		env.mockClient,
		"us-central.api.augmentcode.com",
		env.featureFlags,
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)
}

func addTokenHash(t *testing.T, env *tenantMapTestEnv, userID, tenantID string) {
	tokenHashDAO := env.daoFixture.DAOFactory.GetTokenHashDAO()

	tokenHash := &auth_entities.TokenHash{
		Hash:          tokenHash("hello"),
		AugmentUserId: userID,
		TenantId:      tenantID,
	}
	_, err := tokenHashDAO.Create(env.ctx, tokenHash)
	require.NoError(t, err)

	assert.True(t, existsTokenHash(t, env, userID, tenantID))
}

func existsTokenHash(t *testing.T, env *tenantMapTestEnv, userID, tenantID string) bool {
	tokenHashDAO := env.daoFixture.DAOFactory.GetTokenHashDAO()

	var found bool
	err := tokenHashDAO.FindAll(env.ctx, func(token *auth_entities.TokenHash) bool {
		if token.AugmentUserId == userID && token.TenantId == tenantID {
			found = true
			return false
		}
		return true
	})
	require.NoError(t, err)

	return found
}

type UserOpt func(*auth_entities.User)

func createUser(t *testing.T, env *tenantMapTestEnv, opts ...UserOpt) *auth_entities.User {
	userDao := env.daoFixture.DAOFactory.GetUserDAO()
	user := userDao.Instantiate()
	user.Email = "<EMAIL>"
	user.Id = uuid.NewString()
	user.Tenants = []string{env.augmentTenantID}

	for _, opt := range opts {
		opt(user)
	}

	_, err := userDao.Create(env.ctx, user)
	require.NoError(t, err)
	return user
}

func withEmailAddress(email string) UserOpt {
	return func(user *auth_entities.User) {
		user.Email = email
	}
}

func createIDPUserID(t *testing.T, env *tenantMapTestEnv, userID string) string {
	return "idp|" + userID
}

func createUserInTenant(t *testing.T, env *tenantMapTestEnv, tenantID string) (*auth_entities.User, string) {
	user := createUser(t, env)
	idpUserId := createIDPUserID(t, env, user.Id)
	_, err := env.tenantMap.EnsureUserInTenant(env.ctx, user, user.Email, tenantID, idpUserId, front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	require.NoError(t, err)
	return user, idpUserId
}

func verifyUserMovedToTenant(t *testing.T, env *tenantMapTestEnv, userID, targetTenantID string, targetTenantName string, originalTenantID string, originalTenantName string) {
	// Verify the user data
	userDao := env.daoFixture.DAOFactory.GetUserDAO()
	movedUser, err := userDao.Get(env.ctx, userID)
	require.NoError(t, err)
	assert.Equal(t, []string{targetTenantID}, movedUser.Tenants)

	// Verify the user's mapping in the target tenant
	targetTenantMappingDao := env.daoFixture.DAOFactory.GetUserTenantMappingDAO(targetTenantName)
	targetMapping, err := targetTenantMappingDao.GetByUser(env.ctx, userID)
	require.NoError(t, err)
	assert.NotNil(t, targetMapping)
	assert.Equal(t, userID, targetMapping.UserId)
	assert.Equal(t, targetTenantName, targetMapping.Tenant)

	// Verify the user's mapping in the original tenant is deleted
	originalTenantMappingDao := env.daoFixture.DAOFactory.GetUserTenantMappingDAO(originalTenantName)
	originalMapping, err := originalTenantMappingDao.GetByUser(env.ctx, userID)
	require.NoError(t, err)
	assert.Nil(t, originalMapping)

	// Verify the user's token is revoked
	assert.False(t, existsTokenHash(t, env, userID, originalTenantID))

	// Move the user back to the original tenant
	err = env.tenantMap.MoveUserToTenant(env.ctx, userID, originalTenantID)
	assert.NoError(t, err)

	// Verify the user data
	movedUser, err = userDao.Get(env.ctx, userID)
	require.NoError(t, err)
	assert.Equal(t, []string{originalTenantID}, movedUser.Tenants)

	// Verify the user's mapping in the original tenant
	originalMapping, err = originalTenantMappingDao.GetByUser(env.ctx, userID)
	require.NoError(t, err)
	assert.NotNil(t, originalMapping)
	assert.Equal(t, userID, originalMapping.UserId)
	assert.Equal(t, originalTenantName, originalMapping.Tenant)

	// Verify the user's mapping in the target tenant is deleted
	targetMapping, err = targetTenantMappingDao.GetByUser(env.ctx, userID)
	require.NoError(t, err)
	assert.Nil(t, targetMapping)

	// Verify the user's token is revoked
	assert.False(t, existsTokenHash(t, env, userID, targetTenantID))
}

func caseGetTenantByIdDeletedOk(t *testing.T, env *tenantMapTestEnv) {
	// Test getting the deleted tenant
	tenant, err := env.tenantMap.GetTenantByIdDeletedOk(env.deletedTenantID)
	require.NoError(t, err)
	require.NotNil(t, tenant)

	// Verify the tenant properties
	assert.Equal(t, env.deletedTenantID, tenant.Id)

	// Test getting a non-existent tenant
	nonExistentTenant, err := env.tenantMap.GetTenantByIdDeletedOk("non-existent-id")
	assert.NoError(t, err)
	assert.Nil(t, nonExistentTenant)

	// Test getting an active (non-deleted) tenant
	activeTenant, err := env.tenantMap.GetTenantByIdDeletedOk(env.augmentTenantID)
	assert.NoError(t, err)
	assert.NotNil(t, activeTenant)
	assert.Empty(t, activeTenant.DeletedAt)
}

func caseGetUserByEmailAddress(t *testing.T, env *tenantMapTestEnv) {
	user := createUser(t, env)

	// Get the user by email address
	foundUser, err := env.tenantMap.GetUserByEmailAddress(env.ctx, user.Email)
	assert.NoError(t, err)
	assert.NotNil(t, foundUser)
	assert.Equal(t, user.Id, foundUser.Id)
	assert.Equal(t, user.Email, foundUser.Email)
	assert.Equal(t, user.Tenants, foundUser.Tenants)

	// Try to get a non-existent user
	nonExistentUser, err := env.tenantMap.GetUserByEmailAddress(env.ctx, "<EMAIL>")
	assert.NoError(t, err)
	assert.Nil(t, nonExistentUser)
}

func caseGetUserAndSimilarUsersByEmailAddress(t *testing.T, env *tenantMapTestEnv) {
	user := createUser(t, env)

	// Test case #1 - exact match
	foundUser, similarEmails, err := env.tenantMap.GetUserAndSimilarUsersByEmailAddress(env.ctx, user.Email)
	assert.NoError(t, err)
	assert.NotNil(t, foundUser)
	assert.Equal(t, user.Id, foundUser.Id)
	assert.Equal(t, user.Email, foundUser.Email)
	assert.Equal(t, user.Tenants, foundUser.Tenants)
	assert.Empty(t, similarEmails)

	// Test case #2 - no match but similar exists
	similarUserToSearch := "." + strings.ToUpper(user.Email)
	foundUser, similarEmails, err = env.tenantMap.GetUserAndSimilarUsersByEmailAddress(env.ctx, similarUserToSearch)
	assert.NoError(t, err)
	assert.Nil(t, foundUser)
	assert.Equal(t, []string{user.Email}, similarEmails)

	// Test case #3 - both match and similar
	_ = createUser(t, env, withEmailAddress(similarUserToSearch))
	foundUser, similarEmails, err = env.tenantMap.GetUserAndSimilarUsersByEmailAddress(env.ctx, user.Email)
	assert.NoError(t, err)
	assert.NotNil(t, foundUser)
	assert.Equal(t, user.Id, foundUser.Id)
	assert.True(t, slices.Contains(similarEmails, similarUserToSearch))
	assert.Len(t, similarEmails, 1)
}

func caseEnsureUserInTenant(t *testing.T, env *tenantMapTestEnv) {
	// Create a user
	user := createUser(t, env)

	// Add the user to a tenant
	idpUserId := createIDPUserID(t, env, user.Id)
	returnedUser, err := env.tenantMap.EnsureUserInTenant(env.ctx, user, user.Email, env.augmentTenantID, idpUserId, front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	assert.NoError(t, err)
	assert.NotNil(t, returnedUser)

	// Verify the user data
	userDao := env.daoFixture.DAOFactory.GetUserDAO()
	updatedUser, err := userDao.Get(env.ctx, user.Id)
	require.NoError(t, err)
	assert.NotNil(t, updatedUser)

	assert.True(t, proto.Equal(returnedUser, updatedUser))
	assert.Equal(t, user.Id, updatedUser.Id)
	assert.Equal(t, user.Email, updatedUser.Email)
	assert.Equal(t, []string{env.augmentTenantID}, updatedUser.Tenants)
	assert.Equal(t, []string{idpUserId}, updatedUser.IdpUserIds)

	// Verify the user's mapping in the target tenant
	tenantMappingDao := env.daoFixture.DAOFactory.GetUserTenantMappingDAO("Augment")
	mapping, err := tenantMappingDao.GetByUser(env.ctx, user.Id)
	require.NoError(t, err)
	assert.NotNil(t, mapping)
	assert.Equal(t, user.Id, mapping.UserId)
	assert.Equal(t, "Augment", mapping.Tenant)

	// Try to add the user to the same tenant again
	returnedUser, err = env.tenantMap.EnsureUserInTenant(env.ctx, user, user.Email, env.augmentTenantID, "", front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	assert.NoError(t, err)
	assert.NotNil(t, returnedUser)
	assert.True(t, proto.Equal(returnedUser, updatedUser))
}

func caseEnsureUserInTenantNewUser(t *testing.T, env *tenantMapTestEnv) {
	// Create a new user by passing nil as the user parameter
	idpUserId := createIDPUserID(t, env, "new-user-123")
	returnedUser, err := env.tenantMap.EnsureUserInTenant(env.ctx, nil, "<EMAIL>", env.augmentTenantID, idpUserId, front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	assert.NoError(t, err)
	assert.NotNil(t, returnedUser)

	// Verify the user data
	userDao := env.daoFixture.DAOFactory.GetUserDAO()
	createdUser, err := userDao.Get(env.ctx, returnedUser.Id)
	require.NoError(t, err)
	assert.NotNil(t, createdUser)

	// Verify the billing method was set to orb for the newly created user
	assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, createdUser.BillingMethod)
	assert.Equal(t, "<EMAIL>", createdUser.Email)
	assert.Equal(t, []string{env.augmentTenantID}, createdUser.Tenants)
	assert.Equal(t, []string{idpUserId}, createdUser.IdpUserIds)

	// Verify the user's mapping in the target tenant
	tenantMappingDao := env.daoFixture.DAOFactory.GetUserTenantMappingDAO("Augment")
	mapping, err := tenantMappingDao.GetByUser(env.ctx, createdUser.Id)
	require.NoError(t, err)
	assert.NotNil(t, mapping)
	assert.Equal(t, createdUser.Id, mapping.UserId)
	assert.Equal(t, "Augment", mapping.Tenant)
}

func caseEnsureUserInTenantDuplicates(t *testing.T, env *tenantMapTestEnv) {
	// Add the same user twice.
	idpUserId := createIDPUserID(t, env, "123456")
	_, err := env.tenantMap.EnsureUserInTenant(env.ctx, nil, "<EMAIL>", env.augmentTenantID, idpUserId, front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	assert.NoError(t, err)
	_, err = env.tenantMap.EnsureUserInTenant(env.ctx, nil, "<EMAIL>", env.augmentTenantID, idpUserId, front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	assert.NoError(t, err)

	// We should only have a single user.
	userDAO := env.daoFixture.DAOFactory.GetUserDAO()
	var users []*auth_entities.User
	err = userDAO.FindAll(env.ctx, func(user *auth_entities.User) bool {
		users = append(users, user)
		return true
	})
	require.NoError(t, err)
	assert.Len(t, users, 1)

	// Verify the billing method was set to orb for the created user
	assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, users[0].BillingMethod)

	// We should have a single IDP user mapping.
	idpUserMappingDAO := env.daoFixture.DAOFactory.GetIDPUserMappingDAO()
	mapping, err := idpUserMappingDAO.Get(env.ctx, idpUserId)
	require.NoError(t, err)
	assert.NotNil(t, mapping)
	assert.Equal(t, idpUserId, mapping.IdpUserId)
	assert.Equal(t, users[0].Id, mapping.AugmentUserId)
}

func caseEnsureUserInTenantWithExistingTenant(t *testing.T, env *tenantMapTestEnv) {
	// Create a user
	user, _ := createUserInTenant(t, env, env.augmentTenantID)

	// Try to add the user to a different tenant
	_, err := env.tenantMap.EnsureUserInTenant(env.ctx, user, user.Email, env.codeTenantID, "", front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
	assert.Error(t, err)
}

func caseGetTenantByIdWithNonExistentTenant(t *testing.T, env *tenantMapTestEnv) {
	// Test getting a non-existent tenant
	nonExistentTenant, err := env.tenantMap.GetTenantByID("non-existent-id")
	assert.NoError(t, err)
	assert.Nil(t, nonExistentTenant)
}

func caseGetTenantByIdWithDeletedTenant(t *testing.T, env *tenantMapTestEnv) {
	// Test getting a deleted tenant
	deletedTenant, err := env.tenantMap.GetTenantByID(env.deletedTenantID)
	assert.NoError(t, err)
	assert.Nil(t, deletedTenant)
}

func caseGetTenantByIdWithActiveTenant(t *testing.T, env *tenantMapTestEnv) {
	// Test getting an active tenant
	activeTenant, err := env.tenantMap.GetTenantByID(env.augmentTenantID)
	assert.NoError(t, err)
	assert.NotNil(t, activeTenant)
	assert.Empty(t, activeTenant.DeletedAt)
}

func caseMoveUserToTenant(t *testing.T, env *tenantMapTestEnv) {
	// Create a user
	user, _ := createUserInTenant(t, env, env.augmentTenantID)
	addTokenHash(t, env, user.Id, env.augmentTenantID)

	// Move the user to the target tenant
	err := env.tenantMap.MoveUserToTenant(env.ctx, user.Id, env.codeTenantID)
	assert.NoError(t, err)

	addTokenHash(t, env, user.Id, env.codeTenantID)

	// Verify the user's data
	verifyUserMovedToTenant(t, env, user.Id, env.codeTenantID, "Code", env.augmentTenantID, "Augment")
}

func caseMoveUserToTenantWithNoOriginal(t *testing.T, env *tenantMapTestEnv) {
	userDao := env.daoFixture.DAOFactory.GetUserDAO()
	user := createUser(t, env)

	// Move the user to the target tenant
	err := env.tenantMap.MoveUserToTenant(env.ctx, user.Id, env.codeTenantID)
	assert.NoError(t, err)

	// Verify the user data
	movedUser, err := userDao.Get(env.ctx, user.Id)
	require.NoError(t, err)
	assert.Equal(t, []string{env.codeTenantID}, movedUser.Tenants)

	// Verify the user's mapping in the target tenant
	targetTenantMappingDao := env.daoFixture.DAOFactory.GetUserTenantMappingDAO("Code")
	targetMapping, err := targetTenantMappingDao.GetByUser(env.ctx, user.Id)
	require.NoError(t, err)
	assert.NotNil(t, targetMapping)
	assert.Equal(t, user.Id, targetMapping.UserId)
	assert.Equal(t, "Code", targetMapping.Tenant)
}

func caseMoveUserToTenantWithAdmin(t *testing.T, env *tenantMapTestEnv) {
	// Create a user
	user, _ := createUserInTenant(t, env, env.augmentTenantID)

	// Make the user an admin
	tenantMappingDao := env.daoFixture.DAOFactory.GetUserTenantMappingDAO("Augment")
	mapping := tenantMappingDao.Instantiate()
	mapping.Tenant = "Augment"
	mapping.UserId = user.Id
	mapping.CustomerUiRoles = []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN}
	_, err := tenantMappingDao.Create(env.ctx, mapping)
	assert.NoError(t, err)

	// Move the user to the target tenant
	err = env.tenantMap.MoveUserToTenant(env.ctx, user.Id, env.codeTenantID)
	assert.Error(t, err)
	assert.Equal(t, "user is an admin in tenant", err.Error())
}

func caseMoveUserToTenantFailpoint(t *testing.T, env *tenantMapTestEnv) {
	panicPoints := []string{
		TenantMapPanicPoints.TenantMappingError,
		TenantMapPanicPoints.RemoveUserFromCurrentTenantError,
		TenantMapPanicPoints.UpdateUserTenantsError,
	}

	for _, panicPoint := range panicPoints {
		// Reset environment for each test case
		env.Reset(t)

		// Create a user in the source tenant
		user, _ := createUserInTenant(t, env, env.augmentTenantID)
		addTokenHash(t, env, user.Id, env.augmentTenantID)

		// Enable the current panic point
		test_utils.EnablePanicPoint(panicPoint)

		// Should panic when trying to move the user
		require.Panics(t, func() {
			_ = env.tenantMap.MoveUserToTenant(env.ctx, user.Id, env.codeTenantID)
		})

		// Now disable the panic point
		test_utils.DisablePanicPoint(panicPoint)

		// Should succeed after disabling panic point
		err := env.tenantMap.MoveUserToTenant(env.ctx, user.Id, env.codeTenantID)
		assert.NoError(t, err)

		// Verify the user was moved correctly
		verifyUserMovedToTenant(t, env, user.Id, env.codeTenantID, "Code", env.augmentTenantID, "Augment")
	}
}

func caseGetTenantForEmailDomain(t *testing.T, env *tenantMapTestEnv) {
	// Test with an email matching a tenant's domain
	tenant, err := env.tenantMap.GetTenantForEmailDomain("<EMAIL>")
	require.NoError(t, err)
	assert.NotNil(t, tenant)
	assert.Equal(t, "Augment", tenant.Name)
	assert.Equal(t, env.augmentTenantID, tenant.Id)

	// Test with an email matching a different tenant's domain
	tenant, err = env.tenantMap.GetTenantForEmailDomain("<EMAIL>")
	require.NoError(t, err)
	assert.NotNil(t, tenant)
	assert.Equal(t, "Code", tenant.Name)
	assert.Equal(t, env.codeTenantID, tenant.Id)

	// Test with an email not matching any tenant's domain
	tenant, err = env.tenantMap.GetTenantForEmailDomain("<EMAIL>")
	require.NoError(t, err)
	assert.Nil(t, tenant)

	// Test with an invalid email
	tenant, err = env.tenantMap.GetTenantForEmailDomain("invalid-email")
	require.NoError(t, err)
	assert.Nil(t, tenant)

	// Test with a deleted tenant
	// This should return nil since we skip deleted tenants
	tenant, err = env.tenantMap.GetTenantForEmailDomain("<EMAIL>")
	require.NoError(t, err)
	assert.Nil(t, tenant)
}

// Define a type for test case functions
type testCaseFunc func(*testing.T, *tenantMapTestEnv)

func TestTenantMap(t *testing.T) {
	// Create a shared environment for all test cases
	env := newTenantMapTestEnv(t)
	defer env.daoFixture.Cleanup()

	// Define test cases in a table
	testCases := []struct {
		name string
		fn   testCaseFunc
	}{
		{"GetTenantByIdDeletedOk", caseGetTenantByIdDeletedOk},
		{"GetUserByEmailAddress", caseGetUserByEmailAddress},
		{"GetUserAndSimilarUsersByEmailAddress", caseGetUserAndSimilarUsersByEmailAddress},
		{"EnsureUserInTenant", caseEnsureUserInTenant},
		{"EnsureUserInTenant creates new user with ORB billing method", caseEnsureUserInTenantNewUser},
		{"EnsureUserInTenant does not create duplicates for the same IDP user ID", caseEnsureUserInTenantDuplicates},
		{"EnsureUserInTenant does not move user if moveToTenant is false", caseEnsureUserInTenantWithExistingTenant},
		{"GetTenantByIdWithNonExistentTenant", caseGetTenantByIdWithNonExistentTenant},
		{"GetTenantByIdWithDeletedTenant", caseGetTenantByIdWithDeletedTenant},
		{"GetTenantByIdWithActiveTenant", caseGetTenantByIdWithActiveTenant},
		{"MoveUserToTenant", caseMoveUserToTenant},
		{"MoveUserToTenantWithNoOriginal", caseMoveUserToTenantWithNoOriginal},
		{"MoveUserToTenantWithAdmin", caseMoveUserToTenantWithAdmin},
		{"MoveUserToTenantFailpoint", caseMoveUserToTenantFailpoint},
		{"GetTenantForEmailDomain", caseGetTenantForEmailDomain},
	}

	// Run all test cases with the shared environment
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			env.Reset(t)
			tc.fn(t, env)
		})
	}
}

func TestSimilarEmails(t *testing.T) {
	testCases := []struct {
		name      string
		input     string
		input2    string
		isSimilar bool
	}{
		{"different usernames", "<EMAIL>", "<EMAIL>", false},
		{"different domains", "<EMAIL>", "<EMAIL>", false},
		{"case insensitive", "<EMAIL>", "<EMAIL>", true},
		{"ignores text after + in username", "<EMAIL>", "<EMAIL>", true},
		{"ignores dots in username", "<EMAIL>", "<EMAIL>", true},
		{"dots in domain significant", "<EMAIL>", "<EMAIL>", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			assert.Equal(t, tc.isSimilar, similarEmail(tc.input) == similarEmail(tc.input2))
		})
	}
}

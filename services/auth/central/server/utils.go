package main

import (
	"fmt"
	"strings"
)

// This is a home for generic utils that don't have an obvious home. Putting anything here should
// be a last resort.

// Normalize an email address (e.g., "<EMAIL>" becomes "<EMAIL>"). Normalization does
// NOT do anything to "." or "+" in the email. Returns an error if the email is invalid.
func normalizeEmail(email string) (string, error) {
	// Trim
	normalized := strings.TrimSpace(email)

	// Lowercase
	normalized = strings.ToLower(normalized)

	// Make sure there isn't whitespace left.
	if strings.ContainsAny(normalized, " \t\n\r") {
		return "", fmt.Errorf("Email contains whitespace")
	}

	// Make sure there's exactly one @
	atIndex := strings.Index(normalized, "@")
	if atIndex == -1 {
		return "", fmt.Errorf("Email does not contain @")
	} else if atIndex != strings.LastIndex(normalized, "@") {
		return "", fmt.<PERSON><PERSON>rf("Email contains multiple @s")
	}

	return normalized, nil
}

// Gets the domain from an email address. Returns nil if the email is invalid.
func getDomainFromEmail(email string) *string {
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return nil
	}
	domain := parts[1]
	return &domain
}

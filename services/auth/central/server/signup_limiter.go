package main

import (
	"context"
	"math"
	"math/rand"
	"regexp"
	"time"

	bigtable "cloud.google.com/go/bigtable"
	feature_flags "github.com/augmentcode/augment/base/feature_flags"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"
)

// Feature flags
const (
	SignupMaxBurstFlag = "auth_central_signup_max_burst"
	SignupsPerDayFlag  = "auth_central_signups_per_day"
)

// Default values
const (
	DefaultSignupMaxBurst = 500
	DefaultSignupsPerDay  = 1000
)

type SignupLimiterPersister interface {
	GetState(ctx context.Context) (*auth_entities.SignupLimiterState, []byte, error)
	CompareAndSetState(ctx context.Context, oldState []byte, newState *auth_entities.SignupLimiterState) (bool, error)
	ResetState(ctx context.Context) error
}

// SignupLimiterPersister handles persistence of signup limiter state
type BigtableSignupLimiterPersister struct {
	table *bigtable.Table

	rowKey          string
	columnFamily    string
	valueColumnName []byte
}

// NewSignupLimiterPersister creates a new persister
func NewSignupLimiterPersister(table *bigtable.Table) SignupLimiterPersister {
	return &BigtableSignupLimiterPersister{
		table:           table,
		rowKey:          "SignupLimiterState#",
		columnFamily:    "User",
		valueColumnName: []byte("value"),
	}
}

// GetState retrieves the current state
func (p *BigtableSignupLimiterPersister) GetState(ctx context.Context) (*auth_entities.SignupLimiterState, []byte, error) {
	row, err := p.table.ReadRow(ctx, p.rowKey)
	if err != nil {
		return nil, nil, err
	}

	if row == nil {
		return &auth_entities.SignupLimiterState{}, nil, nil
	}

	items := row[p.columnFamily]
	if len(items) == 0 {
		return &auth_entities.SignupLimiterState{}, nil, nil
	}

	value := items[0].Value
	state := &auth_entities.SignupLimiterState{}
	if err := proto.Unmarshal(value, state); err != nil {
		return nil, nil, err
	}

	return state, value, nil
}

// CompareAndSetState atomically updates the state
func (p *BigtableSignupLimiterPersister) CompareAndSetState(ctx context.Context, oldState []byte, newState *auth_entities.SignupLimiterState) (bool, error) {
	newValue, err := proto.Marshal(newState)
	if err != nil {
		return false, err
	}

	mut := bigtable.NewMutation()
	if oldState != nil {
		mut.DeleteCellsInColumn(p.columnFamily, string(p.valueColumnName))
		mut.Set(p.columnFamily, string(p.valueColumnName), bigtable.Now(), newValue)

		filter := bigtable.ChainFilters(
			bigtable.FamilyFilter(p.columnFamily),
			bigtable.ColumnFilter(string(p.valueColumnName)),
			bigtable.ValueFilter(regexp.QuoteMeta(string(oldState))),
		)

		cond := bigtable.NewCondMutation(filter, mut, nil)
		var wasFilterMatch bool
		err = p.table.Apply(ctx, p.rowKey, cond, bigtable.GetCondMutationResult(&wasFilterMatch))
		if err != nil {
			return false, err
		}
		return wasFilterMatch, nil
	} else {
		filter := bigtable.ChainFilters(
			bigtable.FamilyFilter(p.columnFamily),
			bigtable.ColumnFilter(string(p.valueColumnName)),
		)
		mut.Set(p.columnFamily, string(p.valueColumnName), bigtable.Now(), newValue)
		cond := bigtable.NewCondMutation(filter, nil, mut)
		var wasFilterMatch bool
		err = p.table.Apply(ctx, p.rowKey, cond, bigtable.GetCondMutationResult(&wasFilterMatch))
		if err != nil {
			return false, err
		}
		return !wasFilterMatch, nil
	}
}

// ResetState resets the persister state
func (p *BigtableSignupLimiterPersister) ResetState(ctx context.Context) error {
	mut := bigtable.NewMutation()
	mut.DeleteRow()
	return p.table.Apply(ctx, p.rowKey, mut)
}

func onRetry(retry int) int64 {
	if retry > 0 {
		time.Sleep(time.Duration(10*(1.2*float64(retry)+rand.Float64())) * time.Millisecond)
	}
	return time.Now().Unix()
}

// SignupLimiter implements rate limiting for signups
type SignupLimiter struct {
	persister            SignupLimiterPersister
	onRetry              func(retry int) int64
	featureFlags         feature_flags.FeatureFlagHandle
	defaultMaxBurst      int
	defaultSignupsPerDay int
}

// NewSignupLimiter creates a new signup limiter
func NewSignupLimiter(persister SignupLimiterPersister, flags feature_flags.FeatureFlagHandle) *SignupLimiter {
	return NewSignupLimiterWithRetry(persister, flags, onRetry)
}

func NewSignupLimiterWithRetry(persister SignupLimiterPersister, flags feature_flags.FeatureFlagHandle, onRetry func(retry int) int64) *SignupLimiter {
	return &SignupLimiter{
		persister:            persister,
		onRetry:              onRetry,
		featureFlags:         flags,
		defaultMaxBurst:      DefaultSignupMaxBurst,
		defaultSignupsPerDay: DefaultSignupsPerDay,
	}
}

// CalculateCreditsAvailable calculates available credits
func CalculateCreditsAvailable(previousTime float64, creditsAtPreviousTime float64, now float64, maxCredits float64, creditsPerDay int) float64 {
	timeElapsed := math.Max(now-previousTime, 0)
	creditsEarned := timeElapsed * float64(creditsPerDay) / 86400
	return math.Min(creditsAtPreviousTime+creditsEarned, maxCredits)
}

func (l *SignupLimiter) calculateCreditsAvailable(
	state *auth_entities.SignupLimiterState,
	now int64,
	maxBurstNow int,
) (float64, error) {
	if state.LastUpdateTimeSeconds == 0 {
		return float64(maxBurstNow), nil
	}

	signupsPerDay, _ := l.featureFlags.GetInt(SignupsPerDayFlag, l.defaultSignupsPerDay)

	credits := CalculateCreditsAvailable(
		float64(state.LastUpdateTimeSeconds),
		float64(state.CreditsAvailable),
		float64(now),
		float64(maxBurstNow),
		signupsPerDay,
	)

	if state.MaxBurst < int64(maxBurstNow) {
		credits = math.Min(credits+float64(maxBurstNow)-float64(state.MaxBurst), float64(maxBurstNow))
	}

	return credits, nil
}

// CreditsAvailable returns the number of credits currently available
func (l *SignupLimiter) CreditsAvailable(ctx context.Context) (float64, error) {
	state, _, err := l.persister.GetState(ctx)
	if err != nil {
		return 0, err
	}

	maxBurstNow, _ := l.featureFlags.GetInt(SignupMaxBurstFlag, l.defaultMaxBurst)

	return l.calculateCreditsAvailable(state, time.Now().Unix(), maxBurstNow)
}

// SignupAllowed checks if a signup is currently allowed
func (l *SignupLimiter) SignupAllowed(ctx context.Context) (bool, error) {
	for retry := 0; retry < 10; retry++ {
		now := l.onRetry(retry)

		state, oldRawState, err := l.persister.GetState(ctx)
		if err != nil {
			return false, err
		}

		maxBurstNow, _ := l.featureFlags.GetInt(SignupMaxBurstFlag, l.defaultMaxBurst)

		credits, err := l.calculateCreditsAvailable(state, now, maxBurstNow)
		if err != nil {
			return false, err
		}

		if credits < 1 {
			return false, nil
		}

		newState := &auth_entities.SignupLimiterState{
			CreditsAvailable:      float32(credits - 1),
			LastUpdateTimeSeconds: now,
			MaxBurst:              int64(maxBurstNow),
		}

		success, err := l.persister.CompareAndSetState(ctx, oldRawState, newState)
		if err != nil {
			return false, err
		}
		if success {
			return true, nil
		}
		log.Info().Msgf("Signup limiter: compare_and_set failed on retry %d...", retry)
	}

	return false, nil
}

func (l *SignupLimiter) Set(maxBurst int, signupsPerDay int) {
	l.defaultMaxBurst = maxBurst
	l.defaultSignupsPerDay = signupsPerDay
}

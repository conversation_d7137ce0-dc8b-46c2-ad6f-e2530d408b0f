package main

import (
	"context"
	"fmt"

	"github.com/augmentcode/augment/services/auth/central/server/auth_dev"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type AuthDevServicer struct {
	subscriptionsHandler *SubscriptionHandler
	daoFactory           *DAOFactory
}

func NewAuthDevServicer(
	subscriptionsHandler *SubscriptionHandler,
	auth_dao *DAOFactory,
) *AuthDevServicer {
	return &AuthDevServicer{
		subscriptionsHandler: subscriptionsHandler,
		daoFactory:           auth_dao,
	}
}

func (s *AuthDevServicer) CreateUserSubscription(ctx context.Context, req *auth_dev.CreateUserSubscriptionRequest) (*auth_dev.CreateUserSubscriptionResponse, error) {
	subscription := req.Subscription
	userDAO := s.daoFactory.GetUserDAO()

	if subscription.GetUserId() == "" {
		return nil, status.Error(codes.InvalidArgument, "Owner must be provided")
	}

	user, err := userDAO.Get(ctx, subscription.GetUserId())
	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to get user: %v", err))
	}

	if user == nil {
		return nil, status.Error(codes.NotFound, "User not found")
	}

	if user.SubscriptionId != nil {
		return nil, status.Error(codes.AlreadyExists, "User already has a subscription")
	}

	createdSubscription, err := s.subscriptionsHandler.CreateSubscription(ctx, subscription)
	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to create subscription: %v", err))
	}

	// update the user
	_, err = userDAO.TryUpdate(ctx, user.Id, func(u *auth_entities.User) bool {
		u.SubscriptionId = &createdSubscription.SubscriptionId
		return true
	}, DefaultRetry)
	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to update user: %v", err))
	}

	return &auth_dev.CreateUserSubscriptionResponse{
		Subscription: createdSubscription,
	}, nil
}

func (s *AuthDevServicer) UpdateUserSubscription(ctx context.Context, req *auth_dev.UpdateUserSubscriptionRequest) (*auth_dev.UpdateUserSubscriptionResponse, error) {
	// NOTE: we deliberately do not make assertions on Owner - Subscription relationship.
	// This is up to the caller to ensure. Different callers may have different needs.

	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription := req.Subscription
	subscription.UpdatedAt = timestamppb.Now()

	existingSubscription, err := subscriptionDAO.Get(ctx, subscription.SubscriptionId)
	if err != nil {
		log.Error().Err(err).Str("subscription_id", subscription.SubscriptionId).Msg("Failed to get existing subscription")
		return nil, err
	}

	if existingSubscription == nil {
		log.Error().Str("subscription_id", subscription.SubscriptionId).Msg("Failed to get existing subscription")
		return nil, fmt.Errorf("subscription not found")
	}

	// Use TryUpdate to update the subscription if needed
	_, err = subscriptionDAO.Update(ctx, subscription)
	if err != nil {
		return nil, fmt.Errorf("failed to update subscription: %v", err)
	}

	updatedSubscription, err := subscriptionDAO.Get(ctx, subscription.SubscriptionId)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated subscription: %v", err)
	}

	return &auth_dev.UpdateUserSubscriptionResponse{
		Subscription: updatedSubscription,
	}, nil
}

func (s *AuthDevServicer) GetUserSubscription(ctx context.Context, req *auth_dev.GetUserSubscriptionRequest) (*auth_dev.GetUserSubscriptionResponse, error) {
	var (
		subscription *auth_entities.Subscription
		err          error
	)

	switch {
	case req.GetSubscriptionId() != "":
		subscription, err = s.subscriptionsHandler.GetSubscription(ctx, req.GetSubscriptionId())
	case req.GetUserId() != "":
		subscription, err = s.subscriptionsHandler.GetSubscriptionByUserID(ctx, req.GetUserId())
	default:
		return nil, status.Error(codes.InvalidArgument, "Either subscription_id or user_id must be provided")
	}

	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to get subscription: %v", err))
	}

	if subscription == nil {
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}

	return &auth_dev.GetUserSubscriptionResponse{
		Subscription: subscription,
	}, nil
}

func (s *AuthDevServicer) DeleteUserSubscription(ctx context.Context, req *auth_dev.DeleteUserSubscriptionRequest) (*auth_dev.DeleteUserSubscriptionResponse, error) {
	err := s.subscriptionsHandler.DeleteUserSubscription(ctx, req.SubscriptionId)
	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to delete subscription: %v", err))
	}

	return &auth_dev.DeleteUserSubscriptionResponse{}, nil
}

func (s *AuthDevServicer) GetUser(ctx context.Context, req *auth_dev.GetUserRequest) (*auth_dev.GetUserResponse, error) {
	userDAO := s.daoFactory.GetUserDAO()

	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to get user: %v", err))
	}

	if user == nil {
		return nil, status.Error(codes.NotFound, "User not found")
	}

	return &auth_dev.GetUserResponse{
		User: user,
	}, nil
}

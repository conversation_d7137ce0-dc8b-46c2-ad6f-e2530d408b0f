"""CLI tool for auth-central GRPC service."""

import argparse

import auth_pb2
import auth_pb2_grpc
import grpc

import base.python.grpc.client_options as client_options


class AuthClient:
    """CLI Client implementation for auth-central."""

    def __init__(self):
        self.channel = grpc.insecure_channel(
            "localhost:50051", options=client_options.create()
        )
        self.stub = auth_pb2_grpc.AuthServiceStub(self.channel)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.channel.close()

    def add_user(self, email, tenant_id):
        request = auth_pb2.AddUserToTenantRequest(email=email, tenant_id=tenant_id)
        response: auth_pb2.AddUserToTenantResponse = self.stub.AddUserToTenant(request)
        print(f"User {email} added to tenant {tenant_id} with id {response.user.id}")

    def list_users(self, tenant_id):
        request = auth_pb2.ListTenantUsersRequest(tenant_id=tenant_id)
        response: auth_pb2.ListTenantUsersResponse = self.stub.ListTenantUsers(request)
        print(response)
        print(f"Users in tenant {tenant_id}: {[user.email for user in response.users]}")

    def remove_user(self, email, tenant_id):
        request = auth_pb2.ListTenantUsersRequest(tenant_id=tenant_id)
        response: auth_pb2.ListTenantUsersResponse = self.stub.ListTenantUsers(request)

        user_id = None
        for user in response.users:
            if user.email == email:
                user_id = user.id
                break
        if user_id is None:
            print(f"User {email} not found in tenant {tenant_id}")
            return
        request = auth_pb2.RemoveUserFromTenantRequest(
            user_id=user_id, tenant_id=tenant_id
        )
        response = self.stub.RemoveUserFromTenant(request)
        print(f"User {email} removed from tenant {tenant_id}")


def main():
    parser = argparse.ArgumentParser(description="Authentication client")
    subparsers = parser.add_subparsers(title="commands", dest="command")

    # Sub-parser for the add-user command
    add_user_parser = subparsers.add_parser("add-user", help="Add a user to a tenant")
    add_user_parser.add_argument("--email", required=True, help="User email")
    add_user_parser.add_argument("--tenant-id", required=True, help="Tenant Id")

    # Sub-parser for the list-users command
    list_users_parser = subparsers.add_parser(
        "list-users", help="List users in a tenant"
    )
    list_users_parser.add_argument("--tenant-id", required=True, help="Tenant Id")

    # Sub-parser for the remove-user command
    remove_user_parser = subparsers.add_parser(
        "remove-user", help="Remove a user from a tenant"
    )
    remove_user_parser.add_argument("--email", required=True, help="User email")
    remove_user_parser.add_argument("--tenant-id", required=True, help="Tenant Id")

    args = parser.parse_args()

    with AuthClient() as auth_client:
        if args.command == "validate-access":
            auth_client.validate_access(args.email, args.tenant)
        elif args.command == "add-user":
            auth_client.add_user(args.email, args.tenant)
        elif args.command == "list-users":
            auth_client.list_users(args.tenant)
        elif args.command == "remove-user":
            auth_client.remove_user(args.email, args.tenant)


if __name__ == "__main__":
    main()

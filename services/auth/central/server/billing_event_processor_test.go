package main

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflag "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	orb_event "github.com/augmentcode/augment/services/auth/billing_webhook/orb_event"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

func TestBillingEventProcessorCustomerCredits(t *testing.T) {
	// Set up BigTable fixture for DAOFactory
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	// Create a DAO factory with the BigTable fixture
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	testCases := []struct {
		name           string
		eventType      string
		balance        float64
		expectedLogMsg string
		wrongUnit      bool
	}{
		{
			name:           "Credit Balance Depleted",
			eventType:      "customer.credit_balance_depleted",
			balance:        0,
			expectedLogMsg: "Customer's balance event received",
		},
		{
			name:           "Credit Balance Recovered",
			eventType:      "customer.credit_balance_recovered",
			balance:        100,
			expectedLogMsg: "Customer's balance event received",
		},
		{
			name:           "unit is not usermessages",
			eventType:      "customer.credit_balance_depleted",
			balance:        0,
			expectedLogMsg: "Ignoring credit balance event for non-usermessage pricing unit",
			wrongUnit:      true,
		},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			processor := &BillingEventProcessor{
				Config:      &Config{Orb: OrbConfig{PricingUnit: "usermessages"}},
				orbClient:   orb.NewMockOrbClient(),
				DAOFactory:  daoFactory, // Add the DAOFactory
				auditLogger: audit.NewDefaultAuditLogger(),
			}
			mockOrbClient := processor.orbClient.(*orb.MockOrbClient)
			mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, mock.Anything, mock.Anything).Return(tc.balance, nil)

			// Setup a logger that writes to a buffer
			var logBuffer bytes.Buffer
			origLogger := log.Logger
			log.Logger = zerolog.New(&logBuffer)
			defer func() { log.Logger = origLogger }()

			custID := "test_customer_id"
			orbEvent := &orb_event.OrbEvent{
				EventType:     tc.eventType,
				OrbCustomerId: &custID,
				Metadata: &orb_event.EventMetadata{
					RequestId: "test_request_id",
				},
			}
			if tc.wrongUnit {
				orbEvent.Event = &orb_event.OrbEvent_CustomerBalanceEvent{
					CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{PricingUnit: "credits"},
				}
			} else {
				orbEvent.Event = &orb_event.OrbEvent_CustomerBalanceEvent{
					CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{PricingUnit: "usermessages"},
				}
			}

			err := processor.ProcessOrbEvent(context.Background(), orbEvent)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
			if !tc.wrongUnit {
				mockOrbClient.AssertNumberOfCalls(t, "GetCustomerCreditBalance", 1)
			} else {
				mockOrbClient.AssertNumberOfCalls(t, "GetCustomerCreditBalance", 0)
			}

			// Check the log message
			logOutput := logBuffer.String()
			if !strings.Contains(logOutput, tc.expectedLogMsg) {
				t.Errorf("Expected log message not found: %s", tc.expectedLogMsg)
			}
		})
	}
}

func TestBillingEventProcessorInvoiceEvents(t *testing.T) {
	testCases := []struct {
		name               string
		paymentAttempts    int32
		expectCancellation bool
		expectError        bool
		getInvoiceError    error
		cancelError        error
		invoiceSource      string
		status             string
	}{
		{
			name:               "First payment attempt",
			paymentAttempts:    1,
			expectCancellation: false,
			expectError:        false,
			invoiceSource:      "subscription",
			status:             "issued",
		},
		{
			name:               "Second payment attempt",
			paymentAttempts:    2,
			expectCancellation: false,
			expectError:        false,
			invoiceSource:      "subscription",
			status:             "issued",
		},
		{
			name:               "Third payment attempt - should cancel",
			paymentAttempts:    3,
			expectCancellation: true,
			expectError:        false,
			invoiceSource:      "subscription",
			status:             "issued",
		},
		{
			name:               "Fourth payment attempt - should cancel",
			paymentAttempts:    4,
			expectCancellation: true,
			expectError:        false,
			invoiceSource:      "subscription",
			status:             "issued",
		},
		{
			name:               "GetInvoice error",
			paymentAttempts:    0,
			expectCancellation: false,
			expectError:        true,
			getInvoiceError:    fmt.Errorf("failed to get invoice"),
		},
		{
			name:               "Cancel subscription error",
			paymentAttempts:    3,
			expectCancellation: true,
			expectError:        true,
			invoiceSource:      "subscription",
			status:             "issued",
			cancelError:        fmt.Errorf("failed to cancel subscription"),
		},
		{
			name:               "Not a subscription invoice, do not cancel",
			paymentAttempts:    3,
			expectCancellation: false,
			expectError:        false,
			invoiceSource:      "one_off",
			status:             "issued",
		},
		{
			name:               "Invoice already paid (race condition between webhook and payment), do not cancel",
			paymentAttempts:    2,
			expectCancellation: false,
			expectError:        false,
			invoiceSource:      "subscription",
			status:             "paid",
		},
	}

	// Test case for nil Orb client
	t.Run("Nil Orb client", func(t *testing.T) {
		// Set up the processor with a nil Orb client
		processor := &BillingEventProcessor{
			Config:      &Config{},
			orbClient:   nil,
			auditLogger: audit.NewDefaultAuditLogger(),
		}

		// Create an invoice payment failed event
		invoiceID := "inv_" + uuid.New().String()

		event := &orb_event.OrbEvent{
			EventType: "invoice.payment_failed",
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_InvoiceEvent{
				InvoiceEvent: &orb_event.InvoiceEvent{
					InvoiceId: invoiceID,
				},
			},
		}

		// Process the event

		err := processor.ProcessOrbEvent(context.Background(), event)

		// Verify the results
		require.Error(t, err)
		assert.Contains(t, err.Error(), "orb client is nil")
	})

	// Test case for unhandled invoice event type
	t.Run("Unhandled invoice event type", func(t *testing.T) {
		// Create a mock Orb client
		mockOrbClient := orb.NewMockOrbClient()

		// Set up the processor with the mock client
		processor := &BillingEventProcessor{
			Config:      &Config{},
			orbClient:   mockOrbClient,
			auditLogger: audit.NewDefaultAuditLogger(),
		}

		// Create an unhandled invoice event
		invoiceID := "inv_" + uuid.New().String()

		event := &orb_event.OrbEvent{
			EventType: "invoice.some_other_event",
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_InvoiceEvent{
				InvoiceEvent: &orb_event.InvoiceEvent{
					InvoiceId: invoiceID,
				},
			},
		}

		// Process the event
		err := processor.ProcessOrbEvent(context.Background(), event)

		// Verify the results
		require.NoError(t, err)

		// Verify the mock was not called
		mockOrbClient.AssertNotCalled(t, "GetInvoice", mock.Anything, mock.Anything)
		mockOrbClient.AssertNotCalled(t, "CancelOrbSubscription", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})

	// Test case for nil invoice event
	t.Run("Nil invoice event", func(t *testing.T) {
		// Create a mock Orb client
		mockOrbClient := orb.NewMockOrbClient()

		// Set up the processor with the mock client
		processor := &BillingEventProcessor{
			Config:      &Config{},
			orbClient:   mockOrbClient,
			auditLogger: audit.NewDefaultAuditLogger(),
		}

		// Create an event with nil invoice event
		event := &orb_event.OrbEvent{
			EventType: "invoice.payment_failed",
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			// No invoice event set
		}

		// Process the event
		err := processor.ProcessOrbEvent(context.Background(), event)

		// Verify the results
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invoice event is nil")

		// Verify the mock was not called
		mockOrbClient.AssertNotCalled(t, "CancelOrbSubscription", mock.Anything, mock.Anything, mock.Anything, mock.Anything)
	})

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a mock Orb client
			mockOrbClient := orb.NewMockOrbClient()

			// Set up the processor with the mock client
			processor := &BillingEventProcessor{
				Config:      &Config{},
				orbClient:   mockOrbClient,
				auditLogger: audit.NewDefaultAuditLogger(),
			}

			// Create an invoice payment failed event
			invoiceID := "inv_" + uuid.New().String()
			subscriptionID := "sub_" + uuid.New().String()

			// Create the invoice object that will be returned by GetInvoice
			invoice := &orb.OrbInvoice{
				ID:              invoiceID,
				SubscriptionID:  subscriptionID,
				CustomerID:      "cust_" + uuid.New().String(),
				Status:          tc.status,
				PaymentAttempts: tc.paymentAttempts,
				Source:          tc.invoiceSource,
			}

			// Set up expectations for the mock
			mockOrbClient.On("GetInvoice", mock.Anything, invoiceID).Return(invoice, tc.getInvoiceError)
			if tc.expectCancellation {
				mockOrbClient.On("CancelOrbSubscription", mock.Anything, subscriptionID, orb.PlanChangeImmediate, mock.Anything, mock.Anything).Return(tc.cancelError)
			}

			event := &orb_event.OrbEvent{
				EventType: "invoice.payment_failed",
				Metadata: &orb_event.EventMetadata{
					RequestId: "test-request-id",
					EventId:   "evt_" + uuid.New().String(),
				},
				Event: &orb_event.OrbEvent_InvoiceEvent{
					InvoiceEvent: &orb_event.InvoiceEvent{
						InvoiceId: invoiceID,
					},
				},
			}

			// Process the event
			err := processor.ProcessOrbEvent(context.Background(), event)

			// Verify the results
			if tc.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			// Verify the mock was called as expected
			mockOrbClient.AssertCalled(t, "GetInvoice", mock.Anything, invoiceID)

			if tc.getInvoiceError == nil {
				if tc.expectCancellation {
					mockOrbClient.AssertCalled(t, "CancelOrbSubscription", mock.Anything, subscriptionID, orb.PlanChangeImmediate, mock.Anything, mock.Anything)
				} else {
					mockOrbClient.AssertNotCalled(t, "CancelOrbSubscription", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
				}
			} else {
				mockOrbClient.AssertNotCalled(t, "CancelOrbSubscription", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything)
			}
		})
	}
}

func TestBillingEventProcessorUsageBalanceDepleted(t *testing.T) {
	// Set up BigTable fixture
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	// Create a context
	ctx := context.Background()

	// Create a DAO factory with the BigTable fixture
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create the Billing event processor
	processor := &BillingEventProcessor{
		Config: &Config{
			Orb: OrbConfig{
				PricingUnit: "usermessages",
			},
		},
		DAOFactory:  daoFactory,
		orbClient:   mockOrbClient,
		auditLogger: audit.NewDefaultAuditLogger(),
	}

	t.Run("Balance depleted sets usage_balance_depleted to true", func(t *testing.T) {
		// Create test user and subscription
		userID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()

		// Create a user with the Orb customer ID and subscription ID
		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a subscription with usage_balance_depleted = false
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId:       orbSubscriptionID,
			OrbCustomerId:        orbCustomerID,
			PriceId:              "plan_professional",
			OrbStatus:            auth_entities.Subscription_ORB_STATUS_ACTIVE,
			Seats:                1,
			StartDate:            timestamppb.New(time.Now()),
			CancelAtPeriodEnd:    false,
			Owner:                &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:            timestamppb.Now(),
			UpdatedAt:            timestamppb.Now(),
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
			UsageBalanceDepleted: false,
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create subscription")

		// Mock the Orb client to return a depleted balance (0)
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(0), nil)

		// Create a credit balance event
		event := &orb_event.OrbEvent{
			EventType:     "customer.credit_balance_depleted",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_CustomerBalanceEvent{
				CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
					PricingUnit: "usermessages",
				},
			},
		}

		// Process the event

		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process credit balance event")

		// Verify the subscription was updated
		updatedSubscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.True(t, updatedSubscription.UsageBalanceDepleted, "Subscription should have usage_balance_depleted set to true")
	})

	t.Run("Positive balance sets usage_balance_depleted to false", func(t *testing.T) {
		// Create test user and subscription
		userID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()

		// Create a user with the Orb customer ID and subscription ID
		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a subscription with usage_balance_depleted = true
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId:       orbSubscriptionID,
			OrbCustomerId:        orbCustomerID,
			PriceId:              "plan_professional",
			OrbStatus:            auth_entities.Subscription_ORB_STATUS_ACTIVE,
			Seats:                1,
			StartDate:            timestamppb.New(time.Now()),
			CancelAtPeriodEnd:    false,
			Owner:                &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:            timestamppb.Now(),
			UpdatedAt:            timestamppb.Now(),
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
			UsageBalanceDepleted: true,
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create subscription")

		// Mock the Orb client to return a positive balance
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a credit balance event
		event := &orb_event.OrbEvent{
			EventType:     "customer.credit_balance_recovered",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_CustomerBalanceEvent{
				CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
					PricingUnit: "usermessages",
				},
			},
		}

		// Process the event
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process credit balance event")

		// Verify the subscription was updated
		updatedSubscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.False(t, updatedSubscription.UsageBalanceDepleted, "Subscription should have usage_balance_depleted set to false")
	})

	t.Run("No update when usage_balance_depleted already matches balance state", func(t *testing.T) {
		// Create test user and subscription
		userID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()

		// Create a user with the Orb customer ID and subscription ID
		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create a subscription with usage_balance_depleted = false
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription := &auth_entities.Subscription{
			SubscriptionId:       orbSubscriptionID,
			OrbCustomerId:        orbCustomerID,
			PriceId:              "plan_professional",
			OrbStatus:            auth_entities.Subscription_ORB_STATUS_ACTIVE,
			Seats:                1,
			StartDate:            timestamppb.New(time.Now()),
			CancelAtPeriodEnd:    false,
			Owner:                &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:            timestamppb.Now(),
			UpdatedAt:            timestamppb.Now(),
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
			UsageBalanceDepleted: false,
		}
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err, "Failed to create subscription")

		// Store the original update time
		originalUpdateTime := subscription.UpdatedAt

		// Mock the Orb client to return a positive balance (already matches the subscription state)
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a credit balance event
		event := &orb_event.OrbEvent{
			EventType:     "customer.credit_balance_recovered",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_CustomerBalanceEvent{
				CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
					PricingUnit: "usermessages",
				},
			},
		}

		// Process the event
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process credit balance event")

		// Verify the subscription was not updated (update time should be the same)
		updatedSubscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.False(t, updatedSubscription.UsageBalanceDepleted, "Subscription should still have usage_balance_depleted set to false")
		assert.Equal(t, originalUpdateTime.AsTime().Unix(), updatedSubscription.UpdatedAt.AsTime().Unix(), "Subscription update time should not have changed")
	})

	t.Run("User not found for Orb customer ID", func(t *testing.T) {
		// Create a credit balance event with a non-existent Orb customer ID
		orbCustomerID := "cust_orb_nonexistent_" + uuid.New().String()

		// Mock the Orb client to return a balance
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(0), nil)

		// Create a credit balance event
		event := &orb_event.OrbEvent{
			EventType:     "customer.credit_balance_depleted",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_CustomerBalanceEvent{
				CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
					PricingUnit: "usermessages",
				},
			},
		}

		// Process the event - should not error but log a warning
		err := processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Should not error when user not found")
	})

	t.Run("User has no Orb subscription ID", func(t *testing.T) {
		// Create test user without a subscription ID
		userID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		// Create a user with the Orb customer ID but no subscription ID
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
			// No OrbSubscriptionId
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Mock the Orb client to return a balance
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(0), nil)

		// Create a credit balance event
		event := &orb_event.OrbEvent{
			EventType:     "customer.credit_balance_depleted",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_CustomerBalanceEvent{
				CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
					PricingUnit: "usermessages",
				},
			},
		}

		// Process the event - should not error but log a warning
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Should not error when user has no subscription ID")
	})

	t.Run("Subscription not found", func(t *testing.T) {
		// Create test user with a non-existent subscription ID
		userID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_nonexistent_" + uuid.New().String()

		// Create a user with the Orb customer ID and a non-existent subscription ID
		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Mock the Orb client to return a balance
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(0), nil)

		// Create a credit balance event
		event := &orb_event.OrbEvent{
			EventType:     "customer.credit_balance_depleted",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_CustomerBalanceEvent{
				CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
					PricingUnit: "usermessages",
				},
			},
		}

		// Process the event - should not error but log a warning
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Should not error when subscription not found")
	})

	t.Run("Wrong pricing unit", func(t *testing.T) {
		// Create a credit balance event with a different pricing unit
		orbCustomerID := "cust_orb_" + uuid.New().String()

		// Create a credit balance event with a different pricing unit
		event := &orb_event.OrbEvent{
			EventType:     "customer.credit_balance_depleted",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_CustomerBalanceEvent{
				CustomerBalanceEvent: &orb_event.CustomerBalanceEvent{
					PricingUnit: "credits", // Different from the configured "usermessages"
				},
			},
		}

		// Process the event - should not error but log a warning
		err := processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Should not error when pricing unit doesn't match")

		// Verify the Orb client was not called
		mockOrbClient.AssertNotCalled(t, "GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages")
	})
}

func TestBillingEventProcessorSubscriptionEvents(t *testing.T) {
	// Set up BigTable fixture
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	// Create a context
	ctx := context.Background()

	// Create a DAO factory with the BigTable fixture
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	// Create a mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create a mock Stripe client
	mockStripeClient := NewMockStripeClient()

	// Self-serve team tenant ID
	selfServeTeamTenantID := "self-serve-team-tenant"

	// Create a self-serve team tenant and create TenantMap with it
	selfServeTeamTenant := &tw_pb.Tenant{
		Id:             selfServeTeamTenantID,
		Name:           "Test Self-Serve Team",
		ShardNamespace: "us-central.api.augmentcode.com",
		Cloud:          "GCP_US_CENTRAL1_PROD",
		Tier:           tw_pb.TenantTier_PROFESSIONAL,
		Config: &tw_pb.Config{
			Configs: map[string]string{
				"is_self_serve_team": "true",
			},
		},
	}

	// Create TenantMap with the tenant
	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_pb.Tenant{selfServeTeamTenant},
	}
	tenantMap := NewTenantMap(
		daoFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		featureflag.NewLocalFeatureFlagHandler(),
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	// Create the Billing event processor (without tenantMap for now)
	processor := &BillingEventProcessor{
		Config: &Config{
			Orb: OrbConfig{
				PricingUnit: "usermessages",
			},
		},
		DAOFactory:   daoFactory,
		orbClient:    mockOrbClient,
		stripeClient: mockStripeClient,
		auditLogger:  audit.NewDefaultAuditLogger(),
		tenantMap:    tenantMap,
	}
	now := time.Now()

	// Test cases
	t.Run("Subscription Created Event for User", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Set up the mock Orb client to return subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			OrbStatus:         "active",
			ExternalPlanID:    "plan_professional",
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 1,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event directly
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription created event")

		// Verify the subscription was created
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get subscription")
		assert.Equal(t, "plan_professional", subscription.PriceId, "Subscription should have the correct plan ID")
		assert.Equal(t, auth_entities.Subscription_ORB_STATUS_ACTIVE, subscription.OrbStatus, "Subscription should be active")
		assert.Equal(t, int32(1), subscription.Seats, "Subscription should have 1 seat by default")
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, subscription.BillingMethod, "Subscription should have ORB billing method")

		// Verify the subscription is associated with the user
		userIDOwner, ok := subscription.GetOwner().(*auth_entities.Subscription_UserId)
		require.True(t, ok, "Subscription should be associated with a user")
		assert.Equal(t, user.Id, userIDOwner.UserId, "Subscription should be associated with the correct user")
	})

	t.Run("Subscription Created Event for Team", func(t *testing.T) {
		// Create a test team tenant
		orbSubscriptionID := "sub_orb_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		stripeCustomerID := "stripe-customer-id" + uuid.New().String()

		// Create an admin user for it
		adminUserID := uuid.New().String()
		adminUser := &auth_entities.User{
			Id:                adminUserID,
			Email:             "<EMAIL>",
			Tenants:           []string{selfServeTeamTenantID},
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
			StripeCustomerId:  stripeCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, adminUser)
		require.NoError(t, err, "Failed to create admin user")

		// Create a tenant subscription mapping
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:          selfServeTeamTenantID,
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
			StripeCustomerId:  stripeCustomerID,
		}

		tenantMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err = tenantMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant mapping")

		// Set up the mock Orb client to return subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			ExternalPlanID:    "plan_team_professional",
			OrbStatus:         "active",
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 5,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event directly
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription created event")

		// Verify the subscription was created
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get subscription")
		// assert.Equal(t, "plan_team_professional", subscription.PriceId, "Subscription should have the correct plan ID")
		assert.Equal(t, auth_entities.Subscription_ORB_STATUS_ACTIVE, subscription.OrbStatus, "Subscription should be active")
		assert.Equal(t, int32(5), subscription.Seats, "Subscription should have 5 seats")
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, subscription.BillingMethod, "Subscription should have ORB billing method")

		// Verify the subscription is associated with the team
		tenantIDOwner, ok := subscription.GetOwner().(*auth_entities.Subscription_TenantId)
		require.True(t, ok, "Subscription should be associated with a tenant")
		assert.Equal(t, selfServeTeamTenantID, tenantIDOwner.TenantId, "Subscription should be associated with the correct tenant")
	})

	t.Run("Subscription Created Event for User with augment_user_id", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Set up the mock Orb client to return subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			OrbStatus:         "active",
			ExternalPlanID:    "plan_professional",
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 1,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			AugmentUserId: userID,
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event directly
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription created event")

		// Verify the subscription was created
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get subscription")
		assert.Equal(t, "plan_professional", subscription.PriceId, "Subscription should have the correct plan ID")
		assert.Equal(t, auth_entities.Subscription_ORB_STATUS_ACTIVE, subscription.OrbStatus, "Subscription should be active")
		assert.Equal(t, int32(1), subscription.Seats, "Subscription should have 1 seat by default")
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, subscription.BillingMethod, "Subscription should have ORB billing method")

		// Verify the subscription is associated with the user
		userIDOwner, ok := subscription.GetOwner().(*auth_entities.Subscription_UserId)
		require.True(t, ok, "Subscription should be associated with a user")
		assert.Equal(t, user.Id, userIDOwner.UserId, "Subscription should be associated with the correct user")
	})

	t.Run("Subscription Updated Event for User", func(t *testing.T) {
		// Create a test user and subscription
		userID := uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:                userID,
			Email:             "<EMAIL>",
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create an existing subscription in the database
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		initialSubscription := &auth_entities.Subscription{
			SubscriptionId:    orbSubscriptionID,
			PriceId:           "plan_basic",
			OrbStatus:         auth_entities.Subscription_ORB_STATUS_ACTIVE,
			Seats:             1,
			StartDate:         timestamppb.New(now),
			CancelAtPeriodEnd: false,
			Owner:             &auth_entities.Subscription_UserId{UserId: userID},
			CreatedAt:         timestamppb.Now(),
			UpdatedAt:         timestamppb.Now(),
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		}
		_, err = subscriptionDAO.Create(ctx, initialSubscription)
		require.NoError(t, err, "Failed to create initial subscription")

		// Set up the mock Orb client to return updated subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			OrbStatus:         "active",
			ExternalPlanID:    "plan_professional", // Changed from basic to professional
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 1,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription updated event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.edited",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event directly
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription updated event")

		// Verify the subscription was updated
		updatedSubscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get updated subscription")
		assert.Equal(t, "plan_professional", updatedSubscription.PriceId, "Subscription should have the new plan ID")
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedSubscription.BillingMethod, "Subscription should have been updated to ORB billing method")
	})

	t.Run("Tenant Subscription Update", func(t *testing.T) {
		// Create a test tenant mapping
		tenantID := uuid.New().String()
		adminUserID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()

		// Create an admin user who originally owned the subscription
		userDAO := daoFactory.GetUserDAO()
		adminUser := &auth_entities.User{
			Id:                adminUserID,
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		}
		_, err := userDAO.Create(ctx, adminUser)
		require.NoError(t, err, "Failed to create admin user")

		// Create an existing subscription in the database
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		existingSubscription := &auth_entities.Subscription{
			SubscriptionId:    orbSubscriptionID,
			PriceId:           "plan_team_basic",
			Status:            auth_entities.Subscription_ACTIVE,
			Seats:             5,
			CancelAtPeriodEnd: false,
			Owner:             &auth_entities.Subscription_TenantId{TenantId: tenantID}, // Now owned by tenant
			CreatedAt:         timestamppb.Now(),
			UpdatedAt:         timestamppb.Now(),
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
			OrbCustomerId:     orbCustomerID,
		}
		_, err = subscriptionDAO.Create(ctx, existingSubscription)
		require.NoError(t, err, "Failed to create existing subscription")

		// Create a tenant mapping
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:          tenantID,
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: orbSubscriptionID,
			CreatedAt:         timestamppb.Now(),
		}
		tenantMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err = tenantMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant mapping")

		// Set up the mock Orb client to return updated subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			OrbStatus:         "active",
			ExternalPlanID:    "plan_team_professional", // Changed from basic to professional
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 5,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription updated event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.edited",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Should successfully process subscription updated event with admin user")

		// Verify the subscription was updated
		updatedSubscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get subscription")
		assert.Equal(t, "plan_team_professional", updatedSubscription.PriceId, "Subscription should have been updated to the new plan ID")
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedSubscription.BillingMethod, "Subscription should have ORB billing method")

		// Verify the tenant mapping still exists but wasn't updated
		updatedMapping, err := tenantMappingDAO.Get(ctx, tenantID)
		require.NoError(t, err, "Failed to get tenant mapping")
		assert.Equal(t, orbCustomerID, updatedMapping.OrbCustomerId, "Tenant mapping should have the correct customer ID")
	})

	t.Run("New Subscription Created with Team Owner", func(t *testing.T) {
		// Create a test admin user and tenant mapping
		adminUserID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()

		// Create an admin user
		adminUser := &auth_entities.User{
			Id:            adminUserID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
			Tenants:       []string{selfServeTeamTenantID},
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, adminUser)
		require.NoError(t, err, "Failed to create admin user")

		// Create a tenant mapping (this simulates a team that already exists)
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:      selfServeTeamTenantID,
			OrbCustomerId: orbCustomerID,
			// Note: No OrbSubscriptionId set initially
		}
		tenantMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err = tenantMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant mapping")

		// Set up the mock Orb client to return subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			OrbStatus:         "active",
			ExternalPlanID:    "plan_team_professional",
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 5,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription created event for team")

		// Verify the subscription was created with the correct team owner
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get subscription")
		assert.Equal(t, "plan_team_professional", subscription.PriceId, "Subscription should have the correct plan ID")
		assert.Equal(t, auth_entities.Subscription_ORB_STATUS_ACTIVE, subscription.OrbStatus, "Subscription should be active")
		assert.Equal(t, int32(5), subscription.Seats, "Subscription should have 5 seats")
		assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, subscription.BillingMethod, "Subscription should have ORB billing method")

		// Verify the subscription is associated with the tenant (team owner)
		tenantIDOwner, ok := subscription.GetOwner().(*auth_entities.Subscription_TenantId)
		require.True(t, ok, "Subscription should be associated with a tenant")
		assert.Equal(t, selfServeTeamTenantID, tenantIDOwner.TenantId, "Subscription should be associated with the correct tenant")
	})

	t.Run("Tenant Subscription Created Event Admin User Not Found", func(t *testing.T) {
		// Create a test tenant mapping with unique IDs
		tenantID := uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()

		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:      tenantID,
			OrbCustomerId: orbCustomerID,
		}
		tenantMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err := tenantMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant mapping")

		// Set up the mock Orb client to return subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			ExternalPlanID:    "plan_team_professional",
			OrbStatus:         "active",
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 5,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event directly - should fail because creating subscriptions for tenants is not supported
		err = processor.ProcessOrbEvent(ctx, event)
		require.Error(t, err, "Should fail when processing subscription.created event for a tenant")
		assert.Contains(t, err.Error(), "no user or admin user in tenant found for Orb customer ID", "Error should indicate that no user or tenant was found")

		// Verify the tenant mapping was NOT updated with the subscription ID
		updatedMapping, err := tenantMappingDAO.Get(ctx, tenantID)
		require.NoError(t, err, "Failed to get tenant mapping")
		assert.NotEqual(t, orbSubscriptionID, updatedMapping.OrbSubscriptionId, "Tenant mapping should not have been updated with the subscription ID")
	})

	t.Run("Subscription Event with Unknown Customer", func(t *testing.T) {
		// Create a subscription event with a customer ID that doesn't exist
		orbCustomerID := "cust_orb_nonexistent_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()

		// Set up the mock Orb client to return subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			ExternalPlanID:    "plan_professional",
			OrbStatus:         "active",
			StartDate:         now,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 1,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event directly
		err := processor.ProcessOrbEvent(ctx, event)
		require.Error(t, err, "Should fail when processing subscription event for unknown customer")
		assert.Contains(t, err.Error(), "no user or admin user in tenant found for Orb customer ID", "Error should indicate that no user or tenant was found")
	})

	t.Run("Subscription Event with Missing Customer ID", func(t *testing.T) {
		// Create a subscription event with no customer ID
		subscriptionID := "sub_orb_" + uuid.New().String()

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType: "subscription.created",
			// No OrbCustomerId field
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: subscriptionID,
				},
			},
		}

		// Process the event directly
		err := processor.ProcessOrbEvent(ctx, event)
		require.Error(t, err, "Should fail when processing subscription event with missing customer ID")
		assert.Contains(t, err.Error(), "orb customer ID is nil", "Error should indicate that customer ID is nil")
	})

	t.Run("Subscription with Null EndDate", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Set up the mock Orb client to return subscription info with a zero EndDate
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubscriptionID,
			OrbStatus:         "active",
			ExternalPlanID:    "plan_professional",
			StartDate:         now,
			EndDate:           time.Time{}, // Zero time value to simulate null EndDate
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 1,
			},
		}, nil)

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil)

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata: &orb_event.EventMetadata{
				RequestId: "test-request-id",
				EventId:   "evt_" + uuid.New().String(),
			},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{
					SubscriptionId: orbSubscriptionID,
				},
			},
		}

		// Process the event directly
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription created event with null EndDate")

		// Verify the subscription was created
		subscriptionDAO := daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, orbSubscriptionID)
		require.NoError(t, err, "Failed to get subscription")

		// The key test: EndDate should be nil, not 0001-01-01
		assert.Nil(t, subscription.EndDate, "Subscription EndDate should be nil for null EndDate from Orb")
	})

	t.Run("Tenant Subscription Event Updates TenantSubscriptionMapping", func(t *testing.T) {
		userID := uuid.New().String()
		initialOrbSubscriptionID := "sub_orb_initial_" + uuid.New().String()
		orbSubscriptionID := "sub_orb_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
			Tenants:       []string{selfServeTeamTenantID},
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create TenantSubscriptionMapping
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:          selfServeTeamTenantID,
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: initialOrbSubscriptionID,
		}
		tenantMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err = tenantMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant mapping")

		// Set up the mock Orb client to return subscription info
		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:      orbSubscriptionID,
			OrbStatus:              "active", // Important: Active subscription
			ExternalPlanID:         "plan_team_pro",
			StartDate:              now,
			CurrentFixedQuantities: &orb.FixedQuantities{Seats: 5},
		}, nil).Once() // Ensure it's called

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil).Once()

		// Create a subscription created event (could also be .edited)
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata:      &orb_event.EventMetadata{RequestId: "test-req-tenant-update", EventId: "evt_" + uuid.New().String()},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{SubscriptionId: orbSubscriptionID},
			},
		}

		// Process the event
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription event for tenant")

		// Verify TenantSubscriptionMapping was updated with the new OrbSubscriptionId
		updatedTenantMapping, err := tenantMappingDAO.Get(ctx, selfServeTeamTenantID)
		require.NoError(t, err, "Failed to get updated tenant mapping")
		assert.Equal(t, orbSubscriptionID, updatedTenantMapping.OrbSubscriptionId, "TenantSubscriptionMapping OrbSubscriptionId should be updated")

		// Verify user's OrbSubscriptionId was also updated
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		assert.Equal(t, orbSubscriptionID, updatedUser.OrbSubscriptionId, "User OrbSubscriptionId should be updated")
	})

	t.Run("Team Non-Active Subscription Event Does Not Update TenantSubscriptionMapping", func(t *testing.T) {
		userID := uuid.New().String()
		initialOrbSubscriptionID := "sub_orb_initial_" + uuid.New().String()
		newOrbSubscriptionID := "sub_orb_new_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
			Tenants:       []string{selfServeTeamTenantID},
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		// Create TenantSubscriptionMapping with an initial subscription ID
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:          selfServeTeamTenantID,
			OrbCustomerId:     orbCustomerID,
			OrbSubscriptionId: initialOrbSubscriptionID,
		}
		tenantMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err = tenantMappingDAO.Create(ctx, tenantMapping)
		require.NoError(t, err, "Failed to create tenant mapping")

		// Set up the mock Orb client to return subscription info (status is not active)
		mockOrbClient.On("GetUserSubscription", mock.Anything, newOrbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:      newOrbSubscriptionID,
			OrbStatus:              "ended", // Important: Not an active subscription
			ExternalPlanID:         "plan_team_pro",
			StartDate:              now,
			CurrentFixedQuantities: &orb.FixedQuantities{Seats: 5},
		}, nil).Once()

		// Mock the GetCustomerCreditBalance call
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil).Once()

		// Create a subscription created event
		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata:      &orb_event.EventMetadata{RequestId: "test-req-tenant-nonactive", EventId: "evt_" + uuid.New().String()},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{SubscriptionId: newOrbSubscriptionID},
			},
		}

		// Process the event
		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Failed to process subscription event for tenant")

		// Verify TenantSubscriptionMapping was NOT updated with the new OrbSubscriptionId because subscription is not active
		updatedTenantMapping, err := tenantMappingDAO.Get(ctx, selfServeTeamTenantID)
		require.NoError(t, err, "Failed to get updated tenant mapping")
		assert.Equal(t, initialOrbSubscriptionID, updatedTenantMapping.OrbSubscriptionId, "TenantSubscriptionMapping OrbSubscriptionId should NOT be updated for non-active subscription")

		// Verify user's OrbSubscriptionId was also NOT updated
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		assert.NotEqual(t, newOrbSubscriptionID, updatedUser.OrbSubscriptionId, "User OrbSubscriptionId should NOT be updated for non-active subscription")
	})

	t.Run("User Subscription Event No Tenant Found", func(t *testing.T) {
		userID := uuid.New().String() + "_no_tenant"
		orbSubscriptionID := "sub_orb_" + uuid.New().String()
		orbCustomerID := "cust_orb_" + uuid.New().String()

		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			OrbCustomerId: orbCustomerID,
		}
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err, "Failed to create test user")

		mockOrbClient.On("GetUserSubscription", mock.Anything, orbSubscriptionID, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:      orbSubscriptionID,
			OrbStatus:              "active",
			ExternalPlanID:         "plan_pro",
			StartDate:              now,
			CurrentFixedQuantities: &orb.FixedQuantities{Seats: 1},
		}, nil).Once()
		mockOrbClient.On("GetCustomerCreditBalance", mock.Anything, orbCustomerID, "usermessages").Return(float64(100), nil).Once()

		event := &orb_event.OrbEvent{
			EventType:     "subscription.created",
			OrbCustomerId: proto.String(orbCustomerID),
			Metadata:      &orb_event.EventMetadata{RequestId: "test-req-user-no-tenant", EventId: "evt_" + uuid.New().String()},
			Event: &orb_event.OrbEvent_SubscriptionEvent{
				SubscriptionEvent: &orb_event.SubscriptionEvent{SubscriptionId: orbSubscriptionID},
			},
		}

		err = processor.ProcessOrbEvent(ctx, event)
		require.NoError(t, err, "Processing user subscription event without tenant should succeed")

		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		assert.Equal(t, orbSubscriptionID, updatedUser.OrbSubscriptionId, "User OrbSubscriptionId should be updated")
	})
}

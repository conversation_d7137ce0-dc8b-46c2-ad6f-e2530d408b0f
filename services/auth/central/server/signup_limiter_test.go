package main

import (
	"context"
	"math"
	"testing"

	bigtable "cloud.google.com/go/bigtable"
	feature_flags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/stretchr/testify/assert"
)

func TestSignupLimiter(t *testing.T) {
	fixture := NewBigtableFixture(t)
	defer fixture.Cleanup()

	tests := []struct {
		name            string
		testWithFixture func(*testing.T, *BigtableFixture)
	}{
		{
			name:            "persister",
			testWithFixture: persisterTest,
		},
		{
			name:            "disallows",
			testWithFixture: disallowsTest,
		},
		{
			name:            "handles max burst increase",
			testWithFixture: handlesMaxBurstIncrease,
		},
		{
			name:            "handles compare and set failure",
			testWithFixture: handlesCompareAndSetFailure,
		},
		{
			name:            "compare and set works with regex characters",
			testWithFixture: compareAndSetWorksWithRegexCharacters,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.testWithFixture(t, fixture)
		})
	}
}

func persisterTest(t *testing.T, fixture *BigtableFixture) {
	table := fixture.Table

	persister := NewSignupLimiterPersister(table)
	ctx := context.Background()

	err := persister.ResetState(ctx)
	assert.NoError(t, err)

	state, oldRawState, err := persister.GetState(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, state)
	assert.Equal(t, float32(0), state.CreditsAvailable)
	assert.Equal(t, int64(0), state.LastUpdateTimeSeconds)

	secondState := &auth_entities.SignupLimiterState{
		CreditsAvailable:      100,
		LastUpdateTimeSeconds: 100,
	}

	thirdState := &auth_entities.SignupLimiterState{
		CreditsAvailable:      200,
		LastUpdateTimeSeconds: 200,
	}

	success, err := persister.CompareAndSetState(ctx, oldRawState, secondState)
	assert.NoError(t, err)
	assert.True(t, success, "Transition should succeed the first time")

	state, secondStateRaw, err := persister.GetState(ctx)
	assert.NoError(t, err)
	assert.Equal(t, secondState.CreditsAvailable, state.CreditsAvailable)
	assert.Equal(t, secondState.LastUpdateTimeSeconds, state.LastUpdateTimeSeconds)

	success, err = persister.CompareAndSetState(ctx, oldRawState, secondState)
	assert.NoError(t, err)
	assert.False(t, success, "Transition should fail because state is no longer oldRawState")

	success, err = persister.CompareAndSetState(ctx, secondStateRaw, thirdState)
	assert.NoError(t, err)
	assert.True(t, success, "Transition from second to third state failed unexpectedly")

	state, _, err = persister.GetState(ctx)
	assert.NoError(t, err)
	assert.Equal(t, thirdState.CreditsAvailable, state.CreditsAvailable)
	assert.Equal(t, thirdState.LastUpdateTimeSeconds, state.LastUpdateTimeSeconds)
}

func disallowsTest(t *testing.T, fixture *BigtableFixture) {
	table := fixture.Table

	// Create local feature flags for testing
	flags := feature_flags.NewLocalFeatureFlagHandler()
	flags.Set(SignupMaxBurstFlag, 1)  // Set max burst to 1
	flags.Set(SignupsPerDayFlag, 100) // Set signups per day to 100

	persister := NewSignupLimiterPersister(table)
	ctx := context.Background()

	err := persister.ResetState(ctx)
	assert.NoError(t, err)

	now := 10.0

	onRetry := func(retry int) int64 {
		if retry > 0 {
			now += 0.01 * math.Pow(1.2, float64(retry))
		}
		return int64(now)
	}

	limiter := NewSignupLimiterWithRetry(persister, flags, onRetry)

	allowed, err := limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.True(t, allowed, "max_burst of 1 should allow first signup")

	allowed, err = limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.False(t, allowed, "max_burst of 1 should disallow second signup")

	now += 865.0

	allowed, err = limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.True(t, allowed, "max_rate of 100 should have added a signup")

	allowed, err = limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.False(t, allowed, "previous signup_allowed should have consumed a signup")
}

func handlesMaxBurstIncrease(t *testing.T, fixture *BigtableFixture) {
	table := fixture.Table

	// Create local feature flags for testing
	flags := feature_flags.NewLocalFeatureFlagHandler()
	flags.Set(SignupMaxBurstFlag, 1) // Set max burst to 1
	flags.Set(SignupsPerDayFlag, 0)  // Set signups per day to 0

	persister := NewSignupLimiterPersister(table)
	ctx := context.Background()

	err := persister.ResetState(ctx)
	assert.NoError(t, err)

	now := 10.0

	onRetry := func(retry int) int64 {
		if retry > 0 {
			now += 0.01 * math.Pow(1.2, float64(retry))
		}
		return int64(now)
	}

	limiter := NewSignupLimiterWithRetry(persister, flags, onRetry)

	allowed, err := limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.True(t, allowed, "max_burst of 1 should allow first signup")

	allowed, err = limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.False(t, allowed, "max_burst of 1 should disallow second signup")

	flags.Set(SignupMaxBurstFlag, 2) // Increase max burst to 2

	allowed, err = limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.True(t, allowed, "max_burst to 2 should allow a second signup")

	allowed, err = limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.False(t, allowed, "max_burst of 2 should disallow third signup")
}

type ContendingPersister struct {
	SignupLimiterPersister
	failCompare bool
}

func (p *ContendingPersister) CompareAndSetState(ctx context.Context, oldState []byte, newState *auth_entities.SignupLimiterState) (bool, error) {
	if p.failCompare {
		p.failCompare = false
		return false, nil
	} else {
		p.failCompare = true
	}

	return p.SignupLimiterPersister.CompareAndSetState(ctx, oldState, newState)
}

func handlesCompareAndSetFailure(t *testing.T, fixture *BigtableFixture) {
	table := fixture.Table

	// Create local feature flags for testing
	flags := feature_flags.NewLocalFeatureFlagHandler()
	flags.Set(SignupMaxBurstFlag, 1)  // Set max burst to 1
	flags.Set(SignupsPerDayFlag, 100) // Set signups per day to 100

	persister := &ContendingPersister{
		SignupLimiterPersister: NewSignupLimiterPersister(table),
		failCompare:            true,
	}

	ctx := context.Background()

	err := persister.ResetState(ctx)
	assert.NoError(t, err)

	now := 10.0

	onRetry := func(retry int) int64 {
		if retry > 0 {
			now += 0.01 * math.Pow(1.2, float64(retry))
		}
		return int64(now)
	}

	limiter := NewSignupLimiterWithRetry(persister, flags, onRetry)

	allowed, err := limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.True(t, allowed, "max_burst of 1 should allow first signup")

	allowed, err = limiter.SignupAllowed(ctx)
	assert.NoError(t, err)
	assert.False(t, allowed, "max_burst of 1 should disallow second signup")
}

func compareAndSetWorksWithRegexCharacters(t *testing.T, fixture *BigtableFixture) {
	table := fixture.Table

	valueWithRegexChars := []byte("\x00)][(|?+*\\")
	rowKey := "test_compare_and_set#"

	// Delete the row
	mut := bigtable.NewMutation()
	mut.DeleteRow()
	err := table.Apply(context.Background(), rowKey, mut)
	assert.NoError(t, err)

	// Should fail because row doesn't exist
	success, err := compareAndSet(table, rowKey, "User", "value", valueWithRegexChars, []byte("new_value"))
	assert.NoError(t, err)
	assert.False(t, success, "Should fail because row doesn't exist")

	// Should succeed because row doesn't exist
	success, err = compareAndSet(table, rowKey, "User", "value", nil, valueWithRegexChars)
	assert.NoError(t, err)
	assert.True(t, success, "Should succeed because row doesn't exist")

	// Read the row and check the value
	row, err := table.ReadRow(context.Background(), rowKey)
	assert.NoError(t, err)
	assert.NotNil(t, row)
	assert.Equal(t, valueWithRegexChars, row["User"][0].Value)

	// Should succeed because row exists and value matches
	success, err = compareAndSet(table, rowKey, "User", "value", valueWithRegexChars, []byte("new_value"))
	assert.NoError(t, err)
	assert.True(t, success, "Should succeed because row exists and value matches")
}

func TestCalculateCreditsAvailable(t *testing.T) {
	const secondsInADay = 86400

	tests := []struct {
		name                  string
		previousTime          float64
		creditsAtPreviousTime float64
		now                   float64
		maxCredits            float64
		creditsPerDay         int
		expected              float64
	}{
		{
			name:                  "No time has passed",
			previousTime:          0,
			creditsAtPreviousTime: 5,
			now:                   0,
			maxCredits:            10,
			creditsPerDay:         1,
			expected:              5,
		},
		{
			name:                  "Credits advance at credits_per_day",
			previousTime:          0,
			creditsAtPreviousTime: 0,
			now:                   2 * float64(secondsInADay),
			maxCredits:            10,
			creditsPerDay:         1,
			expected:              2,
		},
		{
			name:                  "Credits saturate at max_credits",
			previousTime:          0,
			creditsAtPreviousTime: 0,
			now:                   float64(secondsInADay),
			maxCredits:            10,
			creditsPerDay:         1000,
			expected:              10,
		},
		{
			name:                  "Time goes backwards",
			previousTime:          float64(secondsInADay),
			creditsAtPreviousTime: 0,
			now:                   0,
			maxCredits:            10,
			creditsPerDay:         1000,
			expected:              0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateCreditsAvailable(
				tt.previousTime,
				tt.creditsAtPreviousTime,
				tt.now,
				tt.maxCredits,
				tt.creditsPerDay,
			)
			assert.Equal(t, tt.expected, result)
		})
	}
}

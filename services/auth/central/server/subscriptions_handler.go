package main

import (
	"context"
	"fmt"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type SubscriptionHandler struct {
	daoFactory *DAOFactory
}

// compareSubscriptionFields compares the fields of two subscriptions that come from Stripe
// Returns true if the fields are different and the subscription needs to be updated
func compareSubscriptionFields(existing, updated *auth_entities.Subscription) bool {
	existingCopy := proto.Clone(existing).(*auth_entities.Subscription)
	updatedCopy := proto.Clone(updated).(*auth_entities.Subscription)

	// Zero out fields that shouldn't be compared
	// These are fields that are managed by our system, not by Stripe
	existingCopy.CreatedAt = nil
	updatedCopy.CreatedAt = nil
	existingCopy.UpdatedAt = nil
	updatedCopy.UpdatedAt = nil

	return !proto.Equal(existingCopy, updatedCopy)
}

func (s *SubscriptionHandler) CreateSubscription(
	ctx context.Context,
	subscription *auth_entities.Subscription,
) (*auth_entities.Subscription, error) {
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription.CreatedAt = timestamppb.Now()
	subscription.UpdatedAt = timestamppb.Now()

	// Create the subscription
	createdSubscription, err := subscriptionDAO.Create(ctx, subscription)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscription: %v", err)
	}

	// Update the user with the subscription ID
	return createdSubscription, nil
}

func (s *SubscriptionHandler) UpdateSubscription(
	ctx context.Context,
	subscription *auth_entities.Subscription,
) (*auth_entities.Subscription, error) {
	// NOTE: we deliberately do not make assertions on Owner - Subscription relationship.
	// This is up to the caller to ensure. Different callers may have different needs.

	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription.UpdatedAt = timestamppb.Now()

	existingSubscription, err := subscriptionDAO.Get(ctx, subscription.SubscriptionId)
	if err != nil {
		log.Error().Err(err).Str("subscription_id", subscription.SubscriptionId).Msg("Failed to get existing subscription")
		return nil, err
	}

	if existingSubscription == nil {
		log.Error().Str("subscription_id", subscription.SubscriptionId).Msg("Failed to get existing subscription")
		return nil, fmt.Errorf("subscription not found")
	}

	// Use TryUpdate to update the subscription if needed
	_, err = subscriptionDAO.TryUpdate(ctx, existingSubscription.SubscriptionId, func(s *auth_entities.Subscription) bool {
		// Check if any fields need to be updated
		if !compareSubscriptionFields(s, subscription) {
			log.Info().Str("subscription_id", subscription.SubscriptionId).Msg("Subscription data is already up to date, no changes needed")
			return false // No changes needed
		}

		log.Info().Str("subscription_id", subscription.SubscriptionId).Msg("Updating existing subscription for user")

		// Update the fields from our parsed subscription
		s.PriceId = subscription.PriceId
		s.Status = subscription.Status
		s.Seats = subscription.Seats
		s.StartDate = subscription.StartDate
		s.EndDate = subscription.EndDate
		s.TrialEnd = subscription.TrialEnd
		s.CancelAtPeriodEnd = subscription.CancelAtPeriodEnd
		s.HasPaymentMethod = subscription.HasPaymentMethod
		s.UpdatedAt = timestamppb.Now()

		return true
	}, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to update subscription: %v", err)
	}

	updatedSubscription, err := subscriptionDAO.Get(ctx, subscription.SubscriptionId)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated subscription: %v", err)
	}

	return updatedSubscription, nil
}

func (h *SubscriptionHandler) GetSubscription(
	ctx context.Context,
	subscriptionID string,
) (*auth_entities.Subscription, error) {
	subscriptionDAO := h.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, subscriptionID)
	if err != nil {
		return nil, err
	}

	if subscription == nil {
		return nil, nil
	}

	return subscription, nil
}

func (h *SubscriptionHandler) GetSubscriptionByUserID(
	ctx context.Context,
	userID string,
) (*auth_entities.Subscription, error) {
	userDAO := h.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		return nil, err
	}

	if user == nil {
		return nil, nil
	}

	if user.SubscriptionId == nil {
		return nil, nil
	}

	return h.GetSubscription(ctx, *user.SubscriptionId)
}

func (h *SubscriptionHandler) DeleteUserSubscription(
	ctx context.Context,
	subscriptionID string,
) error {
	subscriptionDAO := h.daoFactory.GetSubscriptionDAO()
	userDAO := h.daoFactory.GetUserDAO()

	subscription, err := subscriptionDAO.Get(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if subscription == nil {
		return nil
	}

	// Delete the subscription
	err = subscriptionDAO.Delete(ctx, subscriptionID)
	if err != nil {
		return fmt.Errorf("failed to delete subscription: %v", err)
	}
	if subscription.GetUserId() == "" {
		return nil
	}
	// Remove the subscription ID from the user
	_, err = userDAO.TryUpdate(ctx, subscription.GetUserId(), func(u *auth_entities.User) bool {
		if u.SubscriptionId != nil && *u.SubscriptionId == subscriptionID {
			u.SubscriptionId = nil
			return true
		}
		return false
	}, DefaultRetry)
	if err != nil {
		return fmt.Errorf("failed to remove subscription ID from user: %v", err)
	}
	return nil
}

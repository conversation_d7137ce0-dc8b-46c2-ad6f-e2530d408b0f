package main

import (
	"context"
	"fmt"

	"cloud.google.com/go/pubsub"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"

	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
)

// AsyncOpsPublisher is responsible for publishing async operation messages to an internal pub/sub topic
type AsyncOpsPublisher interface {
	PublishUserTierChange(ctx context.Context, msg *auth_internal.UserTierChangeMessage) error
	PublishTeamPlanChange(ctx context.Context, msg *auth_internal.TeamPlanChangeMessage) error
	PublishTenantCreation(ctx context.Context, msg *auth_internal.CreateTenantForTeamMessage) error
	PublishInvitationResolution(ctx context.Context, msg *auth_internal.ResolveInvitationsMessage) error
	PublishSubscriptionCreation(ctx context.Context, msg *auth_internal.CreateSubscriptionMessage) error
	PublishSendInvitationEmail(ctx context.Context, msg *auth_internal.SendInvitationEmailMessage) error
	PublishUpdateSubscription(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage) error
	Close() error
}

type asyncOpsPublisher struct {
	client *pubsub.Client
	topic  *pubsub.Topic
}

func NewAsyncOpsPublisher(ctx context.Context, projectID string, topicName string) (*asyncOpsPublisher, error) {
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to create pubsub client: %w", err)
	}

	topic := client.Topic(topicName)

	return &asyncOpsPublisher{
		client: client,
		topic:  topic,
	}, nil
}

func (p *asyncOpsPublisher) Publish(ctx context.Context, msg *auth_internal.AuthCentralAsyncOpsMessage) error {
	data, err := proto.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to serialize message: %w", err)
	}

	result := p.topic.Publish(ctx, &pubsub.Message{
		Data: data,
	})

	_, err = result.Get(ctx)
	if err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	return nil
}

func (p *asyncOpsPublisher) PublishUserTierChange(ctx context.Context, msg *auth_internal.UserTierChangeMessage) error {
	log.Info().
		Str("tier_change_id", msg.TierChangeId).
		Msg("Publishing user tier change message")

	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_UserTierChangeMessage{
			UserTierChangeMessage: msg,
		},
	}

	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}

	log.Info().
		Str("tier_change_id", msg.TierChangeId).
		Msg("Published user tier change message")

	return nil
}

func (p *asyncOpsPublisher) PublishTeamPlanChange(ctx context.Context, msg *auth_internal.TeamPlanChangeMessage) error {
	log.Info().
		Str("plan_change_id", msg.PlanChangeId).
		Str("team_tenant_id", msg.TeamTenantId).
		Msg("Publishing team plan change message")
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_TeamPlanChangeMessage{
			TeamPlanChangeMessage: msg,
		},
	}
	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}
	log.Info().
		Str("plan_change_id", msg.PlanChangeId).
		Str("team_tenant_id", msg.TeamTenantId).
		Msg("Published team plan change message")
	return nil
}

func (p *asyncOpsPublisher) PublishTenantCreation(ctx context.Context, msg *auth_internal.CreateTenantForTeamMessage) error {
	log.Info().
		Str("tenant_creation_id", msg.TenantCreationId).
		Msg("Publishing tenant creation message")

	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_CreateTenantForTeamMessage{
			CreateTenantForTeamMessage: msg,
		},
	}

	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}

	log.Info().
		Str("tenant_creation_id", msg.TenantCreationId).
		Msg("Published tenant creation message")

	return nil
}

func (p *asyncOpsPublisher) PublishInvitationResolution(
	ctx context.Context, msg *auth_internal.ResolveInvitationsMessage,
) error {
	log.Info().
		Str("invitation_resolution_id", msg.InvitationResolutionId).
		Msg("Publishing invitation resolution message")

	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_ResolveInvitationsMessage{
			ResolveInvitationsMessage: msg,
		},
	}

	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}

	log.Info().
		Str("invitation_resolution_id", msg.InvitationResolutionId).
		Msg("Published invitation resolution message")

	return nil
}

func (p *asyncOpsPublisher) PublishSubscriptionCreation(
	ctx context.Context, msg *auth_internal.CreateSubscriptionMessage,
) error {
	log.Info().
		Str("user_id", msg.UserId).
		Str("tenant_id", msg.TenantId).
		Msg("Publishing subscription creation message")

	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_CreateSubscriptionMessage{
			CreateSubscriptionMessage: msg,
		},
	}

	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}

	log.Info().
		Str("user_id", msg.UserId).
		Str("tenant_id", msg.TenantId).
		Msg("Published subscription creation message")

	return nil
}

func (p *asyncOpsPublisher) PublishSendInvitationEmail(
	ctx context.Context, msg *auth_internal.SendInvitationEmailMessage,
) error {
	log.Info().
		Str("tenant_id", msg.TenantId).
		Str("invitation_id", msg.InvitationId).
		Str("invitee_email", msg.InviteeEmail).
		Msg("Publishing invitation email message")

	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_SendInvitationEmailMessage{
			SendInvitationEmailMessage: msg,
		},
	}

	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}

	log.Info().
		Str("tenant_id", msg.TenantId).
		Str("invitation_id", msg.InvitationId).
		Str("invitee_email", msg.InviteeEmail).
		Msg("Published invitation email message")

	return nil
}

func (p *asyncOpsPublisher) PublishUpdateSubscription(
	ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage,
) error {
	log.Info().
		Str("subscription_id", msg.SubscriptionId).
		Int32("num_seats", msg.NumSeats).
		Msg("Publishing update subscription message")

	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_UpdateSubscriptionMessage{
			UpdateSubscriptionMessage: msg,
		},
	}

	if err := p.Publish(ctx, wrapperMsg); err != nil {
		return err
	}

	log.Info().
		Str("subscription_id", msg.SubscriptionId).
		Int32("num_seats", msg.NumSeats).
		Msg("Published update subscription message")

	return nil
}

func (p *asyncOpsPublisher) Close() error {
	p.topic.Stop()
	return p.client.Close()
}

// MockAsyncOpsPublisher is a mock implementation of AsyncOpsPublisher for testing
type MockAsyncOpsPublisher struct {
	Messages []*auth_internal.AuthCentralAsyncOpsMessage
}

func NewMockAsyncOpsPublisher() *MockAsyncOpsPublisher {
	return &MockAsyncOpsPublisher{
		Messages: make([]*auth_internal.AuthCentralAsyncOpsMessage, 0),
	}
}

func (m *MockAsyncOpsPublisher) PublishUserTierChange(ctx context.Context, msg *auth_internal.UserTierChangeMessage) error {
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_UserTierChangeMessage{
			UserTierChangeMessage: msg,
		},
	}
	m.Messages = append(m.Messages, wrapperMsg)
	return nil
}

func (m *MockAsyncOpsPublisher) PublishTeamPlanChange(ctx context.Context, msg *auth_internal.TeamPlanChangeMessage) error {
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_TeamPlanChangeMessage{
			TeamPlanChangeMessage: msg,
		},
	}
	m.Messages = append(m.Messages, wrapperMsg)
	return nil
}

func (m *MockAsyncOpsPublisher) PublishTenantCreation(ctx context.Context, msg *auth_internal.CreateTenantForTeamMessage) error {
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_CreateTenantForTeamMessage{
			CreateTenantForTeamMessage: msg,
		},
	}
	m.Messages = append(m.Messages, wrapperMsg)
	return nil
}

func (m *MockAsyncOpsPublisher) PublishInvitationResolution(
	ctx context.Context, msg *auth_internal.ResolveInvitationsMessage,
) error {
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_ResolveInvitationsMessage{
			ResolveInvitationsMessage: msg,
		},
	}
	m.Messages = append(m.Messages, wrapperMsg)
	return nil
}

func (m *MockAsyncOpsPublisher) PublishSubscriptionCreation(
	ctx context.Context, msg *auth_internal.CreateSubscriptionMessage,
) error {
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_CreateSubscriptionMessage{
			CreateSubscriptionMessage: msg,
		},
	}
	m.Messages = append(m.Messages, wrapperMsg)
	return nil
}

func (m *MockAsyncOpsPublisher) PublishSendInvitationEmail(
	ctx context.Context, msg *auth_internal.SendInvitationEmailMessage,
) error {
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_SendInvitationEmailMessage{
			SendInvitationEmailMessage: msg,
		},
	}
	m.Messages = append(m.Messages, wrapperMsg)
	return nil
}

func (m *MockAsyncOpsPublisher) PublishUpdateSubscription(
	ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage,
) error {
	wrapperMsg := &auth_internal.AuthCentralAsyncOpsMessage{
		Message: &auth_internal.AuthCentralAsyncOpsMessage_UpdateSubscriptionMessage{
			UpdateSubscriptionMessage: msg,
		},
	}
	m.Messages = append(m.Messages, wrapperMsg)
	return nil
}

func (m *MockAsyncOpsPublisher) Close() error {
	return nil
}

func (m *MockAsyncOpsPublisher) GetPublishedMessages() []*auth_internal.AuthCentralAsyncOpsMessage {
	return m.Messages
}

func (m *MockAsyncOpsPublisher) ClearMessages() {
	m.Messages = make([]*auth_internal.AuthCentralAsyncOpsMessage, 0)
}

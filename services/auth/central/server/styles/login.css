/* Deps: services/auth/common/frontend/styles/ */
@import "vars.css";
@import "base.css";
@import "layouts/l-oauth.css";
@import "components/c-header.css";
@import "components/c-footer.css";
@import "utils/u-text-sm.css";

/* Deps: ./styles/ */
@import "third_party/gsi-material-button.css";

/* Additional styles */
.user_warning {
  font-weight: bold;
  font-style: italic;
}

/* We use the Google provided buttons as anchor tags instead of buttons
 so we need these additional styles */
.sign-link {
  display: inline-block;
  text-decoration: none;
}

.signin-container .buttons {
  opacity: 0.4;
  pointer-events: none;
}

.signin-container:has(#terms-of-service-checkbox:checked) .buttons {
  opacity: 1;
  pointer-events: unset;
}

.signin-container .redirect {
  display: flex;
  flex-direction: column;
  gap: var(--p-4);
}

.signin-container form {
  display: flex;
  flex-direction: column;
  gap: var(--p-4);
}

.c-checkbox {
  display: inline-block;
  position: relative;
  cursor: pointer;
}

.c-checkbox input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

.c-checkbox--mark {
  display: inline-block;
  vertical-align: bottom;
  height: 20px;
  width: 20px;
  border-style: solid;
  border-color: var(--augment-green);
  border-radius: 2px;
  box-sizing: border-box;
  margin-right: var(--p-2);
}

.c-checkbox--mark:after {
  content: "";
  position: absolute;
  display: none;
  left: 8px;
  top: 4px;
  width: 3px;
  height: 8px;
  border: solid var(--text-white-80);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.c-checkbox input:checked ~ .c-checkbox--mark {
  background: var(--augment-green);
}

.c-checkbox input:checked ~ .c-checkbox--mark:after {
  display: block;
}

.c-checkbox input:focus ~ .c-checkbox--mark {
  border-color: var(--augment-green-dark);
}

/* Deps: services/auth/common/frontend/styles/ */
@import "vars.css";
@import "base.css";
@import "layouts/l-oauth.css";
@import "components/c-header.css";
@import "components/c-footer.css";
@import "utils/u-text-sm.css";

/* Additional styles for invitations page */

/* Single invitation styles */
.single-invitation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 500px;
  margin: 0 auto;
}

.single-invitation-card {
  width: 100%;
  padding: 28px;
  background-color: var(--augment-grey-light);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 24px 0;
  text-align: center;
}

.single-invitation-card .invitation-details p {
  margin: 8px 0;
  font-size: 16px;
}

.single-invitation-card .invitation-date {
  color: var(--augment-grey);
  font-size: 14px !important;
  margin-top: 12px !important;
}

.single-invitation-actions {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.single-invitation-actions .button-group {
  display: flex;
  gap: 16px;
}

.accept-button,
.decline-button,
.button {
  padding: 10px 18px;
  border-radius: 4px;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 90px;
  font-size: 14px;
}

.accept-button {
  background-color: var(--augment-green);
  color: white;
}

.accept-button:hover:not(:disabled) {
  background-color: var(--augment-green-dark);
}

.decline-button {
  color: var(--augment-grey);
}

.status-message {
  display: none;
}

.status-message:not(:empty) {
  display: block;
  margin-top: 10px;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: #f0f0f0;
  color: #333;
  font-size: 14px;
  text-align: center;
}

.invitations-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
}

.invitations-container h1 {
  font-size: 24px;
  color: var(--augment-dark-blue);
  margin-bottom: 20px;
  text-align: center;
}

.invitations-container p {
  text-align: center;
  margin-bottom: 16px;
}

.invitations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.invitation-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: var(--augment-grey-light);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.invitation-details {
  flex: 1;
}

.invitation-details p {
  margin: 4px 0;
  font-size: 14px;
}

.invitation-date {
  color: var(--augment-grey);
  font-size: 12px !important;
}

.invitation-actions {
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
  gap: 8px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.invitation-info {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid var(--augment-light-blue);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 16px;
}

.invitation-info p {
  margin: 0;
  font-size: 14px;
  color: var(--augment-grey);
}

.no-invitations-container {
  text-align: center;
  padding: 20px;
}
.no-invitations-container h1 {
  margin-bottom: 16px;
}

.admin-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.warning-text {
  color: #856404;
  margin-bottom: 10px;
}

.support-link {
  color: var(--augment-light-blue);
  text-decoration: underline;
  font-weight: bold;
}

@media (max-width: 600px) {
  .invitation-card {
    flex-direction: column;
    align-items: flex-start;
  }

  .invitation-actions {
    margin-top: 12px;
    flex-direction: row;
    width: 100%;
  }

  .accept-button,
  .decline-button {
    flex: 1;
  }
}

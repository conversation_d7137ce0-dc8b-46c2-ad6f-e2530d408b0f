// This proto file should only be used in dev for dev purposes
syntax = "proto3";
package auth_dev;

import "google/protobuf/timestamp.proto";
import "services/auth/central/server/auth_entities.proto";

// AuthDevService is a service for development purposes only.
// It is not intended to be used in production.
service AuthDevService {
  // Create a subscription associated with a user and update the user subscription id
  rpc CreateUserSubscription(CreateUserSubscriptionRequest) returns (CreateUserSubscriptionResponse);

  // Update a subscription associated with a user.
  // This endpoint will NOT update the user_id.
  // To create a subscription for a user, use CreateUserSubscription instead.
  // If a subscription needs to move from user to user, delete the subscription and create a new one.
  rpc UpdateUserSubscription(UpdateUserSubscriptionRequest) returns (UpdateUserSubscriptionResponse);

  // Get a subscription associated with a user
  rpc GetUserSubscription(GetUserSubscriptionRequest) returns (GetUserSubscriptionResponse);

  // Delete a subscription associated with a user. This will also remove the subscription id from the user.
  rpc DeleteUserSubscription(DeleteUserSubscriptionRequest) returns (DeleteUserSubscriptionResponse);

  // Get a user
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
}

message CreateUserSubscriptionRequest {
  auth_entities.Subscription subscription = 1;
}

message CreateUserSubscriptionResponse {
  auth_entities.Subscription subscription = 1;
}

message UpdateUserSubscriptionRequest {
  auth_entities.Subscription subscription = 1;
}

message UpdateUserSubscriptionResponse {
  auth_entities.Subscription subscription = 1;
}

message GetUserSubscriptionRequest {
  oneof lookup_id {
    string subscription_id = 1;
    string user_id = 2; // only as a workaround as Get on User doesn't work from grpc-debug.
  }
}

message GetUserSubscriptionResponse {
  auth_entities.Subscription subscription = 1;
}

message DeleteUserSubscriptionRequest {
  string subscription_id = 1;
}

message DeleteUserSubscriptionResponse {}

message GetUserRequest {
  string user_id = 1;
}

message GetUserResponse {
  auth_entities.User user = 1;
}

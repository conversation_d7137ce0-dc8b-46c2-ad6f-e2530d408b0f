AUGMENT COMMUNITY TERMS OF SERVICE, VERSION 1.3

This COMMUNITY TERMS OF SERVICE and any Order referring to it
(collectively, the “**Agreement**”) describes the terms and conditions
that apply to you (“**Customer**” and or “you”) and your use of Augment
Computing, Inc.’s (“**Company**” and or “we”) (each a “**Party**”, and
collectively “**Parties**”) software engineering AI platform (the
“**Solution**”). As used herein, an “**Order**” shall mean any order
form, order confirmation, or other written acknowledgement (e.g., an
email notice or receipt of purchase) of an order placed for the Solution
and any related offerings. By accessing or using the Solution, or
accepting this Agreement by checking an acceptance box (or similar) or
executing an Order, Customer agrees that: (1) it has read, and it
understands and agrees to be bound by this Agreement; (2) it is not
barred from using the Solution under the laws of the United States or
any other applicable jurisdiction; and (3) it has the authority to enter
into this Agreement personally, or if Customer is accessing or using the
Solution on behalf of an entity, it has authority to enter into this
Agreement on behalf of such entity.

**The protection of Customer’s intellectual property rights is paramount
to Company: We will not disclose your proprietary source code,
documentation, associated infrastructure and prompts made available to
the Solution (“Customer Code”)** **or any code suggestions, fixes,
architecture suggestions or other outputs from the Solution that are
derived from your Customer Code (collectively, “Output”) to any other
customer. We also do not train the Company’s artificial intelligence
models used to provide the Solution (“Models”) on Customer Code or
Output, so the Solution will never allow your Customer Code or Output to
in any way inform another customer’s code or output. Note that “Customer
Code” and “Output” does not include any liberally licensed open source
software that Customer submits to the Solution or outputs associated
with such software (“OSS Code”) together with your Customer Code, and
Company reserves the right to train its Models and improve the Solution
on such OSS Code at its discretion.**

# ACCESS.

## <u>Access to Solution</u>. Subject to the terms and conditions of this Agreement and any applicable Order, Company will provide Customer, and employees, agents and independent contractors engaged by Customer who are authorized to access the Solution (“**Authorized Users**”) on Customer’s behalf, with access to the Solution during the Term. Customer’s access is a non-exclusive, non-transferable, limited use of the Solution solely for Customer’s internal business purposes, subject to Customer’s payment of all Fees set forth in an Order and in accordance with this Agreement. Company may modify and make changes to the features and functionality of the Solution, provided that such changes do not result in any materially diminished functionality of the Solution, as it existed on the execution date of the applicable Order. In connection with accessing and using the Solution, Customer will share access to its Customer Code to the Solution. For clarity, any Customer owned or third party provided connections and codebases used for such sharing is not part of the Solution. Company will provide to Customer the access codes and authentication keys necessary to enable Customer and its Authorized Users to access the Solution. The unique usernames and passwords cannot be shared or used by more than one individual Authorized User and Customer is responsible for all activities that occur under Authorized User accounts.

## <u>Support</u>. Subject to the terms of this Agreement and any Order, Company shall use commercially reasonable efforts to maintain the availability of, and respond to support requests regarding, the Solution.

## <u>Customer Code and Output</u>. Customer hereby grants Company the right to access and use your Customer Code and Output solely for the purposes of providing the Solution as provided herein. As between the Parties, and to the extent permitted by applicable laws, Customer owns all right, title and interest in and to your Customer Code and the Output, and is responsible for its use of such. Further, so long as Customer Code does not infringe any third party Intellectual Property Rights or violate applicable law, Company warrants Output will not infringe any third party Intellectual Property Rights or violate applicable law. For clarity, Output does not include pre-existing materials or outputs of the Solution which are not specific to Customer or the Customer Code, such as code explanations or other guidance, but Customer is entitled to use such outputs or materials in connection with the Output. Notwithstanding the foregoing, given the nature of machine learning, Customer acknowledges that portions of the Output may not be unique across users and the Solution may generate the same or similar output for another customer under similar terms.

## <u>Protection and Restricted Use of Customer Code</u>. Company will treat your Customer Code and Output as Customer’s Confidential Information (in accordance with Section 5) and shall use industry standard methods to protect your Customer Code and Output from unauthorized use, access, or disclosure. Company may not review or access your Customer Code and Output, except in connection with reviewing Customer Feedback, or with Customer’s express permission, to provide support. Company will not train its Models using the Customer Code or Output, however, Company may in its sole discretion use OSS Code contained with the Customer Code and Output to train its Models and improve the Solution.

## <u>Usage Data.</u> Company may collect and use performance and usage data generated or collected through or in connection with Customer’s use of the Solution (“**Usage Data**”), to improve, monitor, analyze and provide the Solution. Company may aggregate Usage Data from other customers in a non-identifiable manner for marketing, industry analysis, and new product development. Customer acknowledges Company’s ownership of Usage Data. For clarity, no Customer Code or Output is included in, or transferred via, Usage Data.

## <u>Restrictions on Solution</u>. Customer shall not, and shall not permit its Authorized Users or any other third party to: (a) modify, reproduce, or create any derivative works based on the Solution or any materials provided by the Company in connection with the Solution or any portion thereof; (b) reverse engineer, decompile, disassemble, or otherwise attempt to derive the source code or underlying ideas or algorithms of the Solution; (c) sublicense, distribute, sell, lend, rent, lease, transfer, or grant any rights in or to all or any portion of the Solution or provide access to the Solution to third parties on a service bureau basis or otherwise; (d) use the Solution other than as provided herein, including using the Solution or Output to develop, provide, enhance or inform any product or service that is competitive with the Solution; or (e) upload any Customer Code, OSS Code or any other materials to the Solution or use the Solution in any manner that violates any third party intellectual property, proprietary, privacy or contractual rights, or applicable laws. Company reserves the right to suspend access to the Solution to any Authorized User for whom it has reasonable belief is in violation of any of the rights or restrictions contained in this Section 1.6. Company shall work with Customer in good faith to investigate and resolve the suspected violation and use commercially reasonable efforts to (i) notify Customer ahead of such suspension, except in emergency situations, and (ii) restore access promptly following such investigation and remediation of the violation.

## <u>Ownership</u>. Company retains all right, title and interest in and to the Solution, its underlying Models and algorithms, and any materials describing the Solution (“**Documentation**”), including any improvements, enhancements and modifications made therein, and all worldwide trade secrets, patents, copyrights, trademarks, service marks, moral rights and other intellectual property and proprietary rights, and all applicable applications and registrations (“**Intellectual Property Rights**”) therein. Customer acknowledges that this Agreement is not a sale and does not transfer to Customer title or ownership of the Solution, the Models, or Documentation, but only provides for limited use as contemplated herein. ALL RIGHTS NOT EXPRESSLY GRANTED HEREUNDER ARE RESERVED TO COMPANY.

## <u>Providers</u>. Company uses third party hosting providers, other service providers and affiliates to support the general provision of the Solution to all of its customers, i.e.: ISPs, third party ticketing services, etc. Company reserves the right to engage and substitute such providers as appropriate, provided that Company: (a) remains responsible to Customer for the provision of the Solution and (b) is liable for the actions and omissions.

## <u>Trials and Beta Use</u>. Company may offer Customer a trial period of the Solution (“**Trial**”), and/or new “beta” features or tools which Customer may choose to use prior to any potential general commercial release in the Solution (“**Beta Use**”). Notwithstanding anything to the contrary herein, such Trial use and Beta Use features or tools are offered solely for experimental purposes and without warranty of any kind, and may be modified or discontinued at Company’s sole discretion.

# <span class="smallcaps">FEEDBACK</span>. In the course of using the Solution, Customer may provide to Company feedback regarding the use, operation, and functionality of the Solution and the Output, including but not limited to, any information about operating results, known or suspected bugs, errors or compatibility problems, suggested modifications, and user-desired features (“Feedback”). Customer acknowledges that Company may use and incorporate the Feedback into the Solution and in connection with its business, products, models and services without restriction or obligation for compensation to Customer. For clarity, Feedback shall not contain any Customer Code or Output.

# PAYMENT. During the Term (including any Renewal Term), Customer will pay to Company the fees set forth on any applicable Order for access to the Solution and related services (“Fees”). The Fees are non-refundable and are not eligible for set off. Unless otherwise stated on an Order, Customer shall pay the Fees in advance, within thirty (30) days of receipt of an invoice. Each Party shall bear its own expenses in connection with the performance of this Agreement. All Fees will be paid in U.S. dollars and exclude all applicable sales, use, and other taxes. The Fees exclude, and Customer will be solely responsible for, all sales, use, excise, withholding and any other similar taxes, duties and charges of any kind imposed by any federal, state or local governmental entity in connection with the Solution (excluding taxes based solely on Company’s income). If Customer is delinquent in the payment of any undisputed amounts due to Company, Company may suspend its provision of the Solution upon thirty (30) days’ advance written notice to Customer.

# TERM AND TERMINATION.

## <u>Term</u>. This Agreement will commence on the effective date of the applicable Order and continue until the expiration of all applicable Orders, unless terminated by either Party in accordance with this Agreement (the “**Term**”). Any Trial of the Solution shall have a Term of 3 months, unless stated otherwise on the applicable Order. Each Term, other than Terms tied to a Trial, will automatically renew for subsequent periods of the same length as the initial Term (each a “**Renewal Term**”), at the Company’s then-current Fees, unless stated differently on the applicable Order, unless either Party gives the other Party written notice of its intent not to renew at least thirty (30) days prior to expiration of the then-current term. During a Trial, either Party may terminate this Agreement for convenience upon written notice to the other Party. Either Party may terminate this Agreement immediately upon notice to the other Party if the other Party materially breaches this Agreement, and such breach remains uncured thirty (30) days after receipt of written notice of such breach. Additionally, either Party may terminate this Agreement immediately upon written notice to the other Party if the other Party: (a) becomes insolvent or unable to pay its debts as they become due; (b) files a petition for bankruptcy or has a petition for bankruptcy filed against it that is not dismissed within sixty (60) days after filing; (c) makes an assignment for the benefit of creditors; or (d) dissolves or ceases to do business.

## <u>Effects of Termination</u>. Upon termination of this Agreement for any reason, Customer shall immediately discontinue any use of the Solution. The provisions of this Agreement that by their nature should survive termination or expiration of this Agreement, including without limitation provisions regarding payment obligations, intellectual property ownership, confidentiality, indemnification, limitation of liability, and any other provisions that reasonably may be deemed to survive, shall survive the termination or expiration of this Agreement.

# CONFIDENTIALITY.

## <u>Confidential Information</u>. For the purposes of this Agreement, “**Confidential Information**” means any and all information disclosed or made available by either Party to the other which is designated as confidential, or which should otherwise be understood to be confidential, including but not limited to, the Solution, the Documentation, Customer Code that is not publicly available, financial information, product plans, business plans, trade secrets, technology, or any other proprietary information, whether transmitted orally, in writing, or by any other media. Confidential Information does not include information the receiving Party can demonstrate was: (a) publicly available through no fault of the receiving Party, (b) obtained from third parties not under confidentiality restrictions, or (c) is independently developed by a Party without use of Confidential Information.

## <u>Non-Use and Non-Disclosure</u>. Each Party agrees: (a) to use Confidential Information of the other Party solely in accordance with the provisions of this Agreement; and (b) not to disclose, or permit to be disclosed, either directly or indirectly, Confidential Information of the other Party to any third party without the other’s prior written consent. Each Party shall safeguard the Confidential Information of the other Party using the same measures it uses to protect its own Confidential Information, but in no event shall either Party use less than reasonable care in safeguarding the Confidential Information of the other Party. Either Party may disclose Confidential Information of the other Party which required to be disclosed by law or order of a court or other governmental entity; provided that such Party provides the other Party with prompt notice of such requirement, assists the other Party in seeking a protective order or other protection, and only discloses that portion of the Confidential Information that is required to be disclosed, and provided further that any information so disclosed retains its confidentiality protections for all other purposes.

## <u>Use of Solution</u>. Customer is responsible for maintaining the secrecy of any passwords or codes that provide access to the Solution as the Confidential Information of Company, and the Parties hereby acknowledge and agree that the Solution and underlying technology, the Usage Data and the Documentation are the Confidential Information of Company notwithstanding any failure to designate such materials as “Confidential.”

## <u>Remedy</u>. If either Party breaches, or threatens to breach the provisions of this Section, each Party agrees that the non-breaching Party will have no adequate remedy at law and is therefore entitled to immediate injunctive and other equitable relief, without bond and without the necessity of showing actual money damages.

# INDEMNIFICATION.

## <u>By Company</u>. Company will defend at its expense any suit brought against Customer, and will pay any settlement Company makes or approves, or any damages finally awarded in such suit, insofar as such suit is based on a claim by any third party alleging that the permitted use of the Solution or the Output infringes, misappropriates or violates any third party patents, copyrights, trademarks, and trade secrets. If any portion of the Solution becomes, or in Company’s opinion is likely to become, the subject of a claim of infringement, Company may, at Company’s option: (a) procure for Customer the right to continue using the Solution; (b) replace the Solution with non-infringing software or services which do not materially impair the functionality of the Solution; (c) modify the Solution so that it becomes non-infringing; or (d) terminate this Agreement and refund any fees actually paid by Customer to Company for the remainder of the Term, and upon such termination, Customer will immediately cease all use of the Solution. Notwithstanding the foregoing, Company shall have no obligation under this Section or otherwise with respect to any infringement claim based upon (w) Customer Code uploaded to the Solution; (x) any use of the Solution or Output not in accordance with this Agreement or as specified in the Documentation; (y) any use of the Solution or Output in combination with other products, equipment, software or data not supplied by Company, where there would be no infringement but for such combination; or (z) any modification of the Solution or Output by any person other than Company or its authorized agents, where there would be no infringement but for such modification. This Section states Company’s entire liability and Customer’s sole and exclusive remedy for the claims and actions described herein. Notwithstanding the foregoing, Customer acknowledges and agrees that Company’s indemnification obligations under this Section 6 do not extend to any claims related to Customer’s or Authorized Users’ use of the Solution, or any Output generated, during Trial or Beta Use.

## <u>By Customer</u>. Customer will defend at its expense any suit brought against Company, and will pay any settlement Customer makes or approves, or any damages finally awarded in such suit, insofar as such suit is based on a claim by any third party alleging (a) that the Customer Code, OSS Code or any other data provided to Company by Customer hereunder infringes, misappropriates or violates any third party patents, copyrights, trademarks, trade secrets, or other proprietary rights; (b) Customer’s use of the Solution, alone or in combination with third party products, violates applicable law or infringes, misappropriates or violates any third party patents, copyrights, trademarks, trade secrets or other proprietary rights; or (c) Customer’s violation of Section 1.6 of this Agreement.

## <u>Procedure</u>. Any Party that is seeking to be indemnified under the provisions of this Section (an “**Indemnified Party**”) must (a) promptly notify the other Party (the “**Indemnifying Party**”) in writing of any third party claim, suit, or action for which it is seeking an indemnity hereunder, (b) give the Indemnifying Party sole control over the defense of such claim, suit or action and any related settlement negotiations, and (c) cooperate and, at Indemnifying Party’s reasonable request and expense, assist in such defense.

# WARRANTY, DISCLAIMER, RESPONSIBILITY AND LIMITATION OF LIABILITY.

## <u>Warranties</u>. Company represents and warrants to Customer that: (a) it will provide the Solution in a professional and workmanlike manner, consistent with generally accepted industry standards; (b) the Solution will materially conform to the applicable Documentation; and (c) it will use commercially reasonable efforts to ensure that the Solution does not contain any harmful code, viruses, malware, or other malicious software. Customer represents and warrants to Company that (a) it has all necessary rights, consents, and permissions to provide the Customer Code to Company for use in connection with the Solution, and to grant the rights and licenses granted to Company under this Agreement; and (b) Company’s use thereof in accordance with this Agreement, does not and will not infringe or misappropriate any third party Intellectual Property Rights or violate any applicable laws.

## <u>WARRANTY DISCLAIMER</u>. EXCEPT AS OTHERWISE PROVIDED HEREIN, THE SOLUTION, OUTPUT AND DOCUMENTATION ARE PROVIDED “AS IS” WITHOUT WARRANTY OF ANY KIND. Company MAKES NO WARRANTIES, WHETHER EXPRESS, IMPLIED, STATUTORY OR OTHERWISE WITH RESPECT TO THE SOLUTION, OUTPUT AND DOCUMENTATION INCLUDING THEIR CONDITION, CONFORMITY TO ANY REPRESENTATION OR DESCRIPTION, AND COMPANY SPECIFICALLY DISCLAIMS ALL IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE, AND NON-INFRINGEMENT. COMPANY DOES NOT REPRESENT OR WARRANT THAT THE OUTPUT IS PROTECTABLE BY ANY INTELLECTUAL PROPERTY RIGHTS.

## <u>RESPONSIBILITY FOR USE.</u> CUSTOMER, AND NOT COMPANY, SHALL BE SOLELY RESPONSIBLE FOR CUSTOMER’S USE OF THE SOLUTION, INCLUDING ANY USE OF, OR ERRORS IN, THE OUTPUT OR OTHER RESULTS OF THE SOLUTION AND DECISIONS MADE OR ACTIONS TAKEN BASED ON THE SOLUTION.

## <u>LIMITATION OF LIABILITY</u>. EXCEPT FOR A BREACH OF SECTION 1.6, CUSTOMER’S INDEMNIFICATION OBLIGATIONS UNDER SECTION 6, AND A PARTY’S GROSS NEGLIGENCE OR INTENTIONAL MISCONDUCT: (A) IN NO EVENT SHALL EITHER PARTY BE LIABLE TO THE OTHER FOR INDIRECT, INCIDENTAL, SPECIAL OR OTHER CONSEQUENTIAL DAMAGES, INCLUDING WITHOUT LIMITATION DAMAGES FOR LOSS OF PROFITS OR USE OR LOSS OF DATA, INCURRED BY EITHER PARTY OR ANY THIRD PARTY, ARISING OUT OF OR RELATED TO THIS AGREEMENT WHETHER IN AN ACTION IN CONTRACT, TORT, OR OTHERWISE, EVEN IF THE OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES; AND (B) EXCLUDING CUSTOMER’S OBLIGATION TO PAY THE FEES, IN NO EVENT SHALL EITHER PARTY’S AGGREGATE CUMULATIVE LIABILITY ARISING OUT OF OR RELATED TO THIS AGREEMENT EXCEED THE GREATER OF: (A) THE AMOUNT OF FEES PAID OR PAYABLE BY CUSTOMER IN THE TWELVE (12) MONTH PERIOD PRECEDING THE ACT GIVING RISE TO SUCH CLAIM; OR (B) \$5,000 USD, WHETHER AN ACTION IN CONTRACT, TORT, OR OTHERWISE. NOTWITHSTANDING THE FOREGOING, COMPANY’S AGGREGATE LIABILITY FOR ANY TRIAL OR BETA USE SHALL NOT EXCEED \$5,000 USD. THE PARTIES AGREE THAT THE FOREGOING LIMITATIONS REPRESENT A REASONABLE ALLOCATION OF RISK UNDER THIS AGREEMENT. **THE FOREGOING LIMITATIONS WILL APPLY NOTWITHSTANDING THE FAILURE OF ESSENTIAL PURPOSE OF ANY LIMITED REMEDY HEREIN.**

# PRIVACY AND SECURITY

## <u>Data Privacy</u>. Each Party shall comply with all applicable data protection and privacy laws and regulations in connection with its collection, use, and disclosure of personal data under this Agreement, including without limitation the European Union General Data Protection Regulation (GDPR), the California Consumer Privacy Act (CCPA), and any other applicable data protection and privacy laws or regulations. Company’s collection, use, and disclosure of personal data in connection with the Solution are further governed by Company’s privacy policy, which is available on Company’s website and is incorporated into this Agreement by reference.

## <u>Data Security</u>. Company shall implement and maintain appropriate technical and organizational measures to protect Customer Code from unauthorized access, use, disclosure, alteration, or destruction, and to ensure the confidentiality, integrity, and availability of Customer’s data and content processed in connection with the Solution. Such measures shall include, without limitation, access controls, encryption, network and system security, and regular security testing and monitoring.

# GENERAL PROVISIONS

## <u>Publicity</u>. Company may use Customer’s name, social media handles, logo, or other trademarks in customer lists, press releases, blog posts, marketing materials, or other promotional materials, solely to identify the Customer as a customer in accordance with any trademark usage guidelines provided by Customer.

## <u>Relationship of the Parties</u>. The Parties are independent contractors, and nothing in this Agreement creates any partnership, joint venture, agency, franchise, sales representative, or employment relationship between the Parties.

## <u>Assignment</u>. Neither Party shall assign this Agreement or any of its rights or duties under this Agreement, in whole or in part, absent the prior written consent of the other Party; provided however, that either Party may assign all of its rights and obligations hereunder in the event of a change of control or sale of all or substantially all of its assets related to this Agreement, whether by merger, reorganization, operation of law, or otherwise without the prior written approval of the other Party. Subject to the foregoing, this Agreement shall inure to the benefit of and be binding upon the Parties and their respective successors and permitted assigns.

## <u>Governing Law</u>. This Agreement and all matters arising out of or relating to this Agreement is governed by the laws of the State of California, excluding its conflict of law provisions, and both Parties consent to the exclusive jurisdiction and venue of the state and federal courts located in the Santa Clara County, California.

## <u>Notices</u>. Where Company requires that Customer provides an email address, Customer is responsible for providing Company with a valid and current email address. In the event that the email address Customer provides to Company is not valid, or for any reason is not capable of delivering to Customer any notices required by this Agreement, Company’s dispatch of the email containing such notice will nonetheless constitute effective notice. Customer may give notice to Company at the following email address: <EMAIL>. Such notice shall be deemed given when received by Company.

## <u>Severability; Waiver</u>. If any provision of this Agreement is held to be invalid or unenforceable, the remaining provisions of this Agreement will remain in full force and effect. The waiver by either Party of any default or breach of this Agreement shall not constitute a waiver of any other or subsequent default or breach.

## <u>Force Majeure</u>. Neither Party shall be liable for any delay or failure in performance due to acts of God, earthquakes, shortages of supplies, transportation difficulties, labor disputes, riots, war, fire, epidemics (including COVID-19), and similar occurrences beyond its control, whether or not foreseeable. Performance times under this Agreement shall be extended for a period of time equivalent to the time lost because of a delay which is excusable under this provision.

## <u>Entire Agreement; Amendment</u>. This Agreement constitutes the complete agreement between the Parties and supersedes all prior or contemporaneous agreements or representations, written or oral, concerning the subject matter herein. Company is not bound by any contributor license agreement or other terms of any open source software projects that may generally apply to any Customer Code or OSS Code uploaded by Customer or any Output that Customer may contribute to any open source software project. Company may update this Agreement from time to time in its discretion. When changes are made, Company will make a new copy of this Agreement available with the Solution. If Company makes any material changes and Customer has provided an email address to Company, Company will also send an email with an updated copy of this Agreement to Customer at such email address. For clarity, the then-current Agreement will govern through the expiration of the then-current Term, and the updated Agreement will be effective upon the commencement of a subsequent Renewal Term. IF CUSTOMER DOES NOT AGREE TO ANY CHANGE(S), CUSTOMER SHALL STOP USING THE SOLUTION OR EXERCISE ITS RIGHT NOT TO RENEW THE TERM IN ACCORDANCE WITH SECTION 4.1.

*End of Agreement*

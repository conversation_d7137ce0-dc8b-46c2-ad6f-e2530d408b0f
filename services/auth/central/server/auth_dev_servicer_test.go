package main

import (
	"context"
	"testing"

	"github.com/augmentcode/augment/services/auth/central/server/auth_dev"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"
)

type AuthDevTestSuite struct {
	suite.Suite     // this embeds all the helper methods, including T()
	AuthDevServicer *AuthDevServicer
	user            *auth_entities.User
	subscription    *auth_entities.Subscription
}

func (s *AuthDevTestSuite) SetupTest() {
	bigtableFixture := NewBigtableFixture(s.T())
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	// Create a new AuthDevServicer for each test
	s.AuthDevServicer = &AuthDevServicer{
		subscriptionsHandler: &SubscriptionHandler{
			daoFactory: daoFactory,
		},
		daoFactory: daoFactory,
	}

	ctx := context.Background()
	userID := "test-user-id"
	user := s.createUser(ctx, userID)
	s.user = user

	sub := s.createSubscription(ctx, userID)
	s.subscription = sub
}

func (s *AuthDevTestSuite) createUser(ctx context.Context, userID string) *auth_entities.User {
	userDAO := s.AuthDevServicer.subscriptionsHandler.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:      userID,
		Email:   userID + "@example.com",
		Tenants: []string{userID + "-tenant-id"},
	}
	user, err := userDAO.Create(ctx, user)
	s.NoError(err)
	return user
}

func (s *AuthDevTestSuite) createSubscription(ctx context.Context, userID string) *auth_entities.Subscription {
	// Create a new subscription
	request := &auth_dev.CreateUserSubscriptionRequest{
		Subscription: &auth_entities.Subscription{
			SubscriptionId:    userID + "-subscription-id",
			StripeCustomerId:  userID + "-stripe-customer-id",
			PriceId:           "price_test",
			Status:            auth_entities.Subscription_ACTIVE,
			Seats:             1,
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  true,
			// Use the owner oneof field instead of direct UserId
			Owner: &auth_entities.Subscription_UserId{
				UserId: userID,
			},
		},
	}
	subscription, err := s.AuthDevServicer.CreateUserSubscription(ctx, request)
	s.NoError(err)
	return subscription.Subscription
}

func (s *AuthDevTestSuite) TestUpdateUserSubscription() {
	// Create a new subscription
	ctx := context.Background()

	// Update the subscription
	updateRequest := &auth_dev.UpdateUserSubscriptionRequest{
		Subscription: &auth_entities.Subscription{
			SubscriptionId: s.subscription.SubscriptionId,
			Owner:          &auth_entities.Subscription_UserId{UserId: s.user.Id},
			Seats:          10,
		},
	}
	updateResponse, err := s.AuthDevServicer.UpdateUserSubscription(ctx, updateRequest)
	s.NoError(err)
	s.NotNil(updateResponse)
	s.NotNil(updateResponse.Subscription)
	s.Equal(int32(10), updateResponse.Subscription.Seats)
	// Access the user ID through the Owner field
	userIDOwner, ok := updateResponse.Subscription.GetOwner().(*auth_entities.Subscription_UserId)
	s.True(ok, "Subscription should have a user ID owner")
	s.Equal(s.user.Id, userIDOwner.UserId)
}

func (s *AuthDevTestSuite) TestGetUserSubscription() {
	// Create a new subscription
	ctx := context.Background()

	s.Run("by subscription ID", func() {
		// Get the subscription
		getRequest := &auth_dev.GetUserSubscriptionRequest{
			LookupId: &auth_dev.GetUserSubscriptionRequest_SubscriptionId{
				SubscriptionId: s.subscription.SubscriptionId,
			},
		}
		getResponse, err := s.AuthDevServicer.GetUserSubscription(ctx, getRequest)
		s.NoError(err)
		s.NotNil(getResponse)
		s.NotNil(getResponse.Subscription)
		s.True(proto.Equal(s.subscription, getResponse.Subscription))
	})

	s.Run("by user ID", func() {
		// Get the subscription by user ID
		getRequest := &auth_dev.GetUserSubscriptionRequest{
			LookupId: &auth_dev.GetUserSubscriptionRequest_UserId{
				UserId: s.user.Id,
			},
		}
		getResponse, err := s.AuthDevServicer.GetUserSubscription(ctx, getRequest)
		s.NoError(err)
		s.NotNil(getResponse)
		s.NotNil(getResponse.Subscription)
		s.True(proto.Equal(s.subscription, getResponse.Subscription))
	})
}

func (s *AuthDevTestSuite) TestDeleteUserSubscription() {
	// Create a new subscription
	ctx := context.Background()

	// verify it exist (prerequirement)
	getRequest := &auth_dev.GetUserSubscriptionRequest{
		LookupId: &auth_dev.GetUserSubscriptionRequest_SubscriptionId{
			SubscriptionId: s.subscription.SubscriptionId,
		},
	}
	getResponse, err := s.AuthDevServicer.GetUserSubscription(ctx, getRequest)
	s.NoError(err)
	s.NotNil(getResponse)
	s.NotNil(getResponse.Subscription)

	// Delete the subscription
	deleteRequest := &auth_dev.DeleteUserSubscriptionRequest{
		SubscriptionId: s.subscription.SubscriptionId,
	}
	deleteResponse, err := s.AuthDevServicer.DeleteUserSubscription(ctx, deleteRequest)
	s.NoError(err)
	s.NotNil(deleteResponse)

	// Verify the subscription is deleted
	getResponse, err = s.AuthDevServicer.GetUserSubscription(ctx, getRequest)
	s.Error(err)
	s.Nil(getResponse)
}

func TestAuthDevService(t *testing.T) {
	suite.Run(t, new(AuthDevTestSuite))
}

package main

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	front_end_token_service_pb "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"

	"google.golang.org/protobuf/types/known/timestamppb"
)

// TestPanicPoints defines panic points that can be enabled for testing
var TenantMapPanicPoints = struct {
	TenantMappingError               string
	RemoveUserFromCurrentTenantError string
	UpdateUserTenantsError           string
}{
	TenantMappingError:               "tenant-mapping-error",
	RemoveUserFromCurrentTenantError: "remove-user-from-current-tenant-error",
	UpdateUserTenantsError:           "update-user-tenants-error",
}

// TenantDetails represents tenant information
type TenantDetails struct {
	TenantID                 string
	Name                     string
	Namespace                string
	Domain                   *string
	AllowedIdentityProviders []string
	TenantURL                string
	AuthURLV2                string
	CommunityTOS             bool
}

func (t *TenantDetails) String() string {
	return fmt.Sprintf("%s (%s)", t.TenantID, t.Name)
}

// TenantMap handles mapping from email domains to tenant information
type TenantMap struct {
	DAOFactory             *DAOFactory
	tenantWatcherClient    client.TenantWatcherClient
	tenantCache            client.TenantCache
	apiProxyHostnameDomain string
	featureFlagHandle      featureflags.FeatureFlagHandle
	asyncOpsPublisher      AsyncOpsPublisher
	auditLogger            *audit.AuditLogger
}

// NewTenantMap creates a new TenantMap instance
func NewTenantMap(
	DAOFactory *DAOFactory,
	tenantWatcherClient client.TenantWatcherClient,
	apiProxyHostnameDomain string,
	featureFlagHandle featureflags.FeatureFlagHandle,
	asyncOpsPublisher AsyncOpsPublisher,
	auditLogger *audit.AuditLogger,
) *TenantMap {
	tm := &TenantMap{
		DAOFactory:             DAOFactory,
		tenantWatcherClient:    tenantWatcherClient,
		apiProxyHostnameDomain: apiProxyHostnameDomain,
		featureFlagHandle:      featureFlagHandle,
		asyncOpsPublisher:      asyncOpsPublisher,
		auditLogger:            auditLogger,
	}

	// Initialize tenant cache
	tm.tenantCache = client.NewTenantCache(
		tm.tenantWatcherClient,
		"", // Since auth central is in the central namespace, this returns all tenants
	)

	return tm
}

func tenantURL(namespace, apiProxyHostnameDomain string) string {
	return fmt.Sprintf("https://%s.%s/", namespace, apiProxyHostnameDomain)
}

func (tm *TenantMap) GetUserByEmailAddress(ctx context.Context, email string) (*auth_entities.User, error) {
	normalizedEmail, err := normalizeEmail(email)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to normalize email %s", email)
		return nil, err
	}

	userDao := tm.DAOFactory.GetUserDAO()
	var return_user *auth_entities.User
	// Table scan - ouch!
	err = userDao.FindAll(ctx, func(user *auth_entities.User) bool {
		currNormalizedEmail, err := normalizeEmail(user.Email)
		if err != nil {
			log.Debug().Err(err).Msgf("Failed to normalize email %s", user.Email)
			return true
		}
		if currNormalizedEmail == normalizedEmail {
			return_user = user
			return false
		}
		return true
	})
	if err != nil {
		return nil, fmt.Errorf("failed to find users: %w", err)
	}

	return return_user, nil
}

/*
 * Normalize the email address for similarity comparison
 *
 * Ignore case, dots in username, and everything after + in username
 * for similarity comparison.
 *
 * Normalize by lowercasing, removing dots in username, removing everything
 * after the + sign
 */
func similarEmail(email string) string {
	ret := strings.ToLower(email)

	components := strings.SplitN(ret, "@", 2)
	if len(components) != 2 {
		return ret
	}

	components[0] = strings.ReplaceAll(components[0], ".", "")
	components[0] = strings.SplitN(components[0], "+", 2)[0]

	return strings.Join(components, "@")
}

/*
 * Get the user with the given email address, and return any similar email addresses
 * that were found. Requires a full table scan currently.
 */
func (tm *TenantMap) GetUserAndSimilarUsersByEmailAddress(ctx context.Context, email string) (*auth_entities.User, []string, error) {
	userDao := tm.DAOFactory.GetUserDAO()
	var return_user *auth_entities.User
	var similarEmails []string

	normalizedEmail := similarEmail(email)

	err := userDao.FindAll(ctx, func(user *auth_entities.User) bool {
		if user.Email == email && return_user == nil {
			return_user = user
		} else if len(user.Tenants) > 0 && similarEmail(user.Email) == normalizedEmail {
			similarEmails = append(similarEmails, user.Email)
		}
		return true
	})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to find users: %w", err)
	}

	return return_user, similarEmails, nil
}

func (tm *TenantMap) EnsureUserInTenant(ctx context.Context, user *auth_entities.User, email string, tenantID string, idpUserID string, mode front_end_token_service_pb.TenantEnsureMode) (*auth_entities.User, error) {
	tenant, err := tm.GetTenantByID(tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}
	if tenant == nil {
		return nil, fmt.Errorf("tenant %s not found", tenantID)
	}

	log.Info().Msgf("Check if user is in tenant %s", tenant.Name)

	userDao := tm.DAOFactory.GetUserDAO()
	if user == nil {
		augmentUserID := uuid.New().String()
		if idpUserID != "" {
			// We use IDPUserMapping to ensure that we create a single user by IDP user ID, by assigning
			// the mapping before creating the user.
			idpUserMappingDAO := tm.DAOFactory.GetIDPUserMappingDAO()
			createdMapping, err := idpUserMappingDAO.TryCreate(ctx, &auth_entities.IdpUserMapping{
				IdpUserId:     idpUserID,
				AugmentUserId: augmentUserID,
			})
			if err != nil {
				return nil, fmt.Errorf("failed to create idp user mapping: %w", err)
			}

			if !createdMapping {
				// Set augmentUserID to the source of truth in the database.
				mapping, err := idpUserMappingDAO.Get(ctx, idpUserID)
				if err != nil {
					return nil, fmt.Errorf("failed to get idp user mapping: %w", err)
				}
				if mapping == nil {
					return nil, fmt.Errorf("idp user mapping is nil after creation; this is unexpected")
				}
				augmentUserID = mapping.AugmentUserId
			}
		}

		newUser := userDao.Instantiate()
		newUser.Id = augmentUserID
		newUser.Email = email
		newUser.CreatedAt = timestamppb.Now()
		newUser.Nonce = uint64(rand.Int63())
		newUser.BillingMethod = auth_entities.BillingMethod_BILLING_METHOD_ORB

		user, err = userDao.Create(ctx, newUser)
		if err != nil {
			return nil, fmt.Errorf("failed to create user: %w", err)
		}
		log.Info().Msgf("Created user %s", user.Id)
	}

	// Check if user is already in the target tenant
	if !contains(user.Tenants, tenantID) {
		switch mode {
		case front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_REQUIRE_EXISTING:
			return nil, fmt.Errorf("user %s is not in tenant %s", user.Id, tenantID)

		case front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY:
			if len(user.Tenants) > 0 {
				return nil, fmt.Errorf("user %s already has tenants, cannot add to tenant %s", user.Id, tenantID)
			}
			fallthrough // add user to tenant using MoveUserToTenant in the next case

		case front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_OVERWRITE:
			log.Info().Msgf("Move user %s to tenant %s", user.Id, tenantID)
			err := tm.MoveUserToTenant(ctx, user.Id, tenantID)
			if err != nil {
				return nil, fmt.Errorf("failed to move user to tenant: %w", err)
			}
			// Refresh user data after move
			user, err = userDao.Get(ctx, user.Id)
			if err != nil {
				return nil, fmt.Errorf("failed to get updated user: %w", err)
			}
			if user == nil {
				return nil, fmt.Errorf("user deleted unexpectedly")
			}
		}
	}

	// The DAO's TryUpdate doesn't return an error if the update callback returns false. Use
	// updateFailed to signal that something went wrong inside the callback.
	var updateErr error
	updateUserCallback := func(user *auth_entities.User) bool {
		update := false
		if user.Tenants[0] != tenantID {
			updateErr = fmt.Errorf("user %s is in tenant %s, expected %s", user.Id, user.Tenants[0], tenantID)
			return false
		}
		if idpUserID != "" && !contains(user.IdpUserIds, idpUserID) {
			user.IdpUserIds = append(user.IdpUserIds, idpUserID)
			update = true
		}

		userShouldHaveSubscription := (tenant.Tier == tw_pb.TenantTier_COMMUNITY || tenant.Tier == tw_pb.TenantTier_PROFESSIONAL) &&
			!tenantutil.IsSelfServeTeamTenant(tenant)
		if userShouldHaveSubscription && user.SubscriptionCreationId == "" && user.StripeCustomerId == "" && user.GetSubscriptionId() == "" {
			// Publish the subscription creation request before updating the user DAO, to avoid ending up
			// in a situation where we have an orphaned subscription creation.
			subscriptionCreationId := uuid.New().String()
			subscriptionCreationMsg := &auth_internal.CreateSubscriptionMessage{
				Id:          subscriptionCreationId,
				UserId:      user.Id,
				TenantId:    tenantID,
				PublishTime: timestamppb.Now(),
			}
			err = tm.asyncOpsPublisher.PublishSubscriptionCreation(ctx, subscriptionCreationMsg)
			if err != nil {
				// Don't update if we failed to enqueue the subscription creation.
				updateErr = fmt.Errorf("failed to publish subscription creation message: %w", err)
				return false
			}

			user.SubscriptionCreationId = subscriptionCreationId
			user.SubscriptionCreationInfo = &auth_entities.User_SubscriptionCreationInfo{
				Id:        subscriptionCreationId,
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				CreatedAt: timestamppb.Now(),
				UpdatedAt: timestamppb.Now(),
			}
			update = true
		} else {
			log.Info().Msgf("Not creating subscription for user %s in tenant %s", user.Id, tenant.Name)
		}

		return update
	}

	user, err = userDao.TryUpdate(ctx, user.Id, updateUserCallback, DefaultRetry)
	if err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}
	if updateErr != nil {
		log.Error().Err(updateErr).Msgf("Failed to update user")
		return nil, updateErr
	}
	if user == nil {
		return nil, fmt.Errorf("user deleted unexpectedly")
	}

	tenantMappingDao := tm.DAOFactory.GetUserTenantMappingDAO(tenant.Name)
	tenantMapping, err := tenantMappingDao.GetByUser(ctx, user.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant mapping: %w", err)
	}

	log.Info().Msgf("Check complete: user in tenant %v", tenantMapping != nil)
	if tenantMapping == nil {
		newMapping := tenantMappingDao.Instantiate()
		newMapping.Tenant = tenant.Name
		newMapping.UserId = user.Id

		_, err = tenantMappingDao.Create(ctx, newMapping)
		if err != nil {
			return nil, fmt.Errorf("failed to create tenant mapping: %w", err)
		}
		log.Info().Msgf("Created user in tenant %s", tenant.Name)
	}

	return user, nil
}

func (tm *TenantMap) GetTenant(tenantName string) (*tw_pb.Tenant, error) {
	ret, err := tm.tenantCache.GetTenantByName(tenantName)
	if errors.Is(err, client.ErrTenantNotFound) {
		return nil, nil
	}
	if ret != nil && ret.DeletedAt != "" {
		log.Warn().Msgf("Tenant %s is deleted", ret.Name)
		return nil, nil
	}
	return ret, err
}

func (tm *TenantMap) GetTenantByIdDeletedOk(tenantID string) (*tw_pb.Tenant, error) {
	ret, err := tm.tenantCache.GetTenant(tenantID)
	if errors.Is(err, client.ErrTenantNotFound) {
		return nil, nil
	}
	return ret, err
}

func (tm *TenantMap) GetTenantByID(tenantID string) (*tw_pb.Tenant, error) {
	ret, err := tm.GetTenantByIdDeletedOk(tenantID)
	if err != nil {
		return nil, err
	}
	if ret != nil && ret.DeletedAt != "" {
		log.Warn().Msgf("Tenant %s is deleted", ret.Name)
		return nil, nil
	}
	return ret, err
}

// MoveUserToTenant moves a given user to a target tenant.
// Note:
//   - The user can be in multiple tenants and will be removed from all of them.
//   - This method is not thread-safe and can lead to race conditions.
func (tm *TenantMap) MoveUserToTenant(ctx context.Context, userID string, targetTenantID string) error {
	// Get target tenant first to fail fast if it doesn't exist
	targetTenant, err := tm.GetTenantByID(targetTenantID)
	if err != nil {
		return fmt.Errorf("failed to get target tenant: %w", err)
	}
	if targetTenant == nil {
		return fmt.Errorf("target tenant not found")
	}

	// Get user to check current tenants
	userDao := tm.DAOFactory.GetUserDAO()
	user, err := userDao.Get(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	if user == nil {
		return fmt.Errorf("user not found")
	}

	// Validate the user is not admin of any of current tenants
	for _, tenantId := range user.Tenants {
		if tenantId == targetTenantID {
			// It's possible that a previous move request has already added the target tenant to the user
			log.Info().Msgf("Skip checking target tenant %s in user's tenants list", targetTenantID)
			continue
		}
		tenant, err := tm.GetTenantByID(tenantId)
		if err != nil {
			return fmt.Errorf("failed to get tenant: %w", err)
		}
		if tenant == nil {
			log.Warn().Msgf("Skip checking tenant %s; tenant not found", tenantId)
			continue
		}

		tenantMappingDao := tm.DAOFactory.GetUserTenantMappingDAO(tenant.Name)
		mapping, err := tenantMappingDao.GetByUser(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to get user mapping: %w", err)
		}
		if mapping == nil {
			log.Warn().Msgf("Skip checking user %s in tenant %s; mapping not found", userID, tenantId)
			continue
		}
		if mapping.CustomerUiRoles != nil {
			for _, role := range mapping.CustomerUiRoles {
				if role == auth_entities.CustomerUiRole_ADMIN {
					log.Error().Msgf("User %s is an admin in tenant %s", userID, tenant.Name)
					return fmt.Errorf("user is an admin in tenant")
				}
			}
		}
	}

	log.Info().Msgf("Moving user %s to tenant %s", user.Id, targetTenant.Name)
	log.Info().Msgf("User's current tenants: %v", user.Tenants)

	tm.auditLogger.WriteAuditLog(
		"",
		"",
		targetTenant.Name,
		fmt.Sprintf("[Tenant Change] Move user '%s' to tenant %s from %v", userID, targetTenantID, user.Tenants),
	)

	// Add the target tenant to the user's tenants list
	if contains(user.Tenants, targetTenantID) {
		log.Info().Msgf("User %s is already in target tenant %s", userID, targetTenant.Name)
	} else {
		addTargetTenant := func(u *auth_entities.User) bool {
			if !contains(user.Tenants, targetTenantID) {
				u.Tenants = append(u.Tenants, targetTenantID)
				// Changing user's tenants must invalidate customer-ui nonce.
				u.Nonce = uint64(rand.Int63())
				return true
			}
			return false
		}
		user, err = userDao.TryUpdate(ctx, user.Id, addTargetTenant, DefaultRetry)
		if err != nil {
			return fmt.Errorf("failed to add target tenant to user's tenants list: %w", err)
		}
	}

	// Create a mapping in the target tenant
	targetTenantMappingDao := tm.DAOFactory.GetUserTenantMappingDAO(targetTenant.Name)
	targetMapping := targetTenantMappingDao.Instantiate()
	targetMapping.Tenant = targetTenant.Name
	targetMapping.UserId = userID
	_, err = targetTenantMappingDao.Create(ctx, targetMapping)
	if err != nil {
		return fmt.Errorf("failed to create target tenant mapping: %w", err)
	}

	test_utils.CheckPanic(TenantMapPanicPoints.TenantMappingError)

	// Remove all other mappings from non-target tenant(s)
	for _, tenantId := range user.Tenants {
		if tenantId == targetTenantID {
			continue
		}

		tenant, err := tm.GetTenantByID(tenantId)
		if err != nil {
			return fmt.Errorf("failed to get tenant: %w", err)
		}
		if tenant == nil {
			log.Warn().Msgf("Skip deleting mapping in tenant %s as not found", tenantId)
			continue
		}

		tenantMappingDao := tm.DAOFactory.GetUserTenantMappingDAO(tenant.Name)
		err = tenantMappingDao.Delete(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to delete mapping for user %s in tenant %s: %w", userID, tenant.Name, err)
		}
	}

	test_utils.CheckPanic(TenantMapPanicPoints.RemoveUserFromCurrentTenantError)

	// Revoke user's tokens from non-target tenant(s)
	tokenHashDAO := tm.DAOFactory.GetTokenHashDAO()
	hashes := make([]string, 0, 16)
	err = tokenHashDAO.FindAll(ctx, func(token *auth_entities.TokenHash) bool {
		if token.AugmentUserId == userID && token.TenantId != targetTenantID {
			hashes = append(hashes, token.Hash)
		}
		return true
	})
	if err != nil {
		return fmt.Errorf("failed to fetch tokens: %w", err)
	}
	deleted := 0
	for _, hash := range hashes {
		if err := tokenHashDAO.Delete(ctx, hash); err != nil {
			return fmt.Errorf("failed to delete token: %w", err)
		}
		deleted++
	}

	log.Info().Msgf("Deleted %d tokens for user %s", deleted, userID)

	// Keep only the target tenant ID in the user's tenants list
	cleanupUserTenants := func(user *auth_entities.User) bool {
		tenantsChanged := true
		if len(user.Tenants) == 1 && user.Tenants[0] == targetTenantID {
			tenantsChanged = false
		}
		if tenantsChanged {
			user.Tenants = []string{targetTenantID}
			// Changing user's tenants must invalidate customer-ui nonce.
			user.Nonce = uint64(rand.Int63())
		}
		return tenantsChanged
	}
	user, err = userDao.TryUpdate(ctx, user.Id, cleanupUserTenants, DefaultRetry)
	if err != nil {
		return fmt.Errorf("failed to update user's tenants list: %w", err)
	}

	test_utils.CheckPanic(TenantMapPanicPoints.UpdateUserTenantsError)

	return nil
}

// GetTenantForEmailDomain returns the tenant associated with the domain of the given email address.
// Returns the matching tenant or nil if no match is found.
func (tm *TenantMap) GetTenantForEmailDomain(email string) (*tw_pb.Tenant, error) {
	// Extract the domain from the email
	emailDomain := getDomainFromEmail(email)
	if emailDomain == nil {
		return nil, nil
	}

	// Get all tenants - unfortunately we don't have a more efficient way to filter by domain
	tenants, err := tm.tenantCache.GetAllTenants()
	if err != nil {
		return nil, fmt.Errorf("failed to get all tenants: %w", err)
	}

	for _, tenant := range tenants {
		// Skip deleted tenants
		if tenant.DeletedAt != "" {
			log.Warn().Msgf("Tenant %s is deleted but matched domain", tenant.Name)
			continue
		}

		// Skip tenants without auth configuration
		if tenant.AuthConfiguration == nil {
			continue
		}

		// Check if domain matches any of the tenant's domains
		if tenant.AuthConfiguration.Domain == *emailDomain ||
			contains(tenant.AuthConfiguration.UsernameDomains, *emailDomain) ||
			contains(tenant.AuthConfiguration.EmailAddressDomains, *emailDomain) {
			log.Info().Msgf("Found tenant for email domain: %s", tenant.Name)
			return tenant, nil
		}
	}

	return nil, nil
}

func (tm *TenantMap) Close() {
	if tm.tenantCache != nil {
		tm.tenantCache.Close()
	}
	if tm.tenantWatcherClient != nil {
		tm.tenantWatcherClient.Close()
	}
}

// Helper function to check if a slice contains a string
func contains(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}

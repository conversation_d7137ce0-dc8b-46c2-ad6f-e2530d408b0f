package main

import (
	"context"
	"fmt"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/integrations/orb"
	rc "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tw_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
)

const (
	// Configuration for local retries. This is for races that we expect and should resolve quickly.
	maxLocalRetryAttempts   = 5
	localRetrySleepDuration = time.Millisecond * 200
)

var TenantCreationPanicPoints = struct {
	CreateNewTenantError                 string
	UpdateTenantCreationError            string
	MoveUserToTenantError                string
	UpdateUserTenantMappingError         string
	CreateTenantSubscriptionMappingError string
	UpdateSubscriptionOwnerError         string
}{
	CreateNewTenantError:                 "create-new-tenant-error",
	UpdateTenantCreationError:            "update-tenant-creation-error",
	MoveUserToTenantError:                "move-user-to-tenant-error",
	UpdateUserTenantMappingError:         "update-user-tenant-mapping-error",
	CreateTenantSubscriptionMappingError: "create-tenant-subscription-mapping-error",
	UpdateSubscriptionOwnerError:         "update-subscription-owner-error",
}

var tenantCreationStatusCounter = promauto.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_auth_central_tenant_creation_status_total",
		Help: "Total number of tenant creation attempts by final status",
	},
	[]string{"status"},
)

type tokenExchangeClient interface {
	GetSignedTokenForServiceWithNamespace(ctx context.Context, tenantID string, namespace string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error)
	Close()
}

// TenantCreationProcessor is responsible for processing tenant creation messages from the async-ops pubsub topic
type TenantCreationProcessor struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	tokenExchangeClient     tokenExchangeClient
	orbClient               orb.OrbClient
	requestInsightPublisher ripublisher.RequestInsightPublisher
	auditLogger             *audit.AuditLogger
}

func NewTenantCreationProcessor(
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	tokenExchangeClient tokenExchangeClient,
	orbClient orb.OrbClient,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	auditLogger *audit.AuditLogger,
) (*TenantCreationProcessor, error) {
	return &TenantCreationProcessor{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		tokenExchangeClient:     tokenExchangeClient,
		orbClient:               orbClient,
		requestInsightPublisher: requestInsightPublisher,
		auditLogger:             auditLogger,
	}, nil
}

// Note that the returned error from this function is about whether we want to retry the message,
// and not necessarily whether there was an error during processing. Nil means we don't want to
// retry.
func (w *TenantCreationProcessor) Process(
	ctx context.Context,
	msg *auth_internal.CreateTenantForTeamMessage,
) (returnErr error) {
	logger := log.Ctx(ctx).With().
		Str("tenant_creation_id", msg.TenantCreationId).
		Str("admin_user_id", msg.TenantCreationRequest.AdminUserId).
		Logger()
	logger.Info().Msg("TenantCreationProcessor: Processing tenant creation message")

	// Read the tenant creation record from BigTable. It is quite likely that this will fail on the
	// first try since we publish to the pub/sub queue before writing this record to BigTable.
	// Retry a few times to avoid a long backoff while we wait for pub/sub to retry.
	tenantCreationDAO := w.daoFactory.GetTenantCreationDAO()
	var tenantCreation *auth_entities.TenantCreation
	var err error
	for attempt := 0; attempt < maxLocalRetryAttempts; attempt++ {
		tenantCreation, err = tenantCreationDAO.Get(ctx, msg.TenantCreationId)
		if err == nil && tenantCreation != nil {
			break
		}
		logger.Warn().Err(err).Msgf(
			"TenantCreationProcessor: Failed to get tenant creation record, attempt %d/%d",
			attempt+1, maxLocalRetryAttempts)
		time.Sleep(localRetrySleepDuration)
	}
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to get tenant creation record after %d attempts", maxLocalRetryAttempts)
	} else if tenantCreation == nil {
		// This can happen if the retries above beat the write to BigTable.
		// TODO(jacqueline): We should probably have some time window after which we just give up.
		return fmt.Errorf("TenantCreationProcessor: tenant creation %s not found", msg.TenantCreationId)
	}

	// Make sure the tenant creation is PENDING.
	if tenantCreation.Status != auth_entities.TenantCreation_PENDING {
		logger.Info().
			Str("tenant_id", tenantCreation.TenantId).
			Str("status", tenantCreation.Status.String()).
			Msg("TenantCreationProcessor: Skip non-pending tenant creation record")
		return nil
	}

	// Processing code can set newCreationStatus to update the status before returning.
	var newCreationStatus auth_entities.TenantCreation_Status
	defer func() {
		// Update the tenant creation record with the new status.
		if newCreationStatus != auth_entities.TenantCreation_UNKNOWN {
			tenantCreation.Status = newCreationStatus
			_, err := tenantCreationDAO.Update(ctx, tenantCreation)
			if err != nil {
				returnErr = fmt.Errorf("TenantCreationProcessor: failed to update tenant creation record: %w", err)
			}

			tenantCreationStatusCounter.WithLabelValues(newCreationStatus.String()).Inc()
		}
	}()

	// Get the user to check if they're eligible for team creation
	userDAO := w.daoFactory.GetUserDAO()
	adminUserID := msg.TenantCreationRequest.AdminUserId
	user, err := userDAO.Get(ctx, adminUserID)
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to get user %s: %w", adminUserID, err)
	}
	if user == nil {
		logger.Error().Msgf("TenantCreationProcessor: User %s not found", adminUserID)
		newCreationStatus = auth_entities.TenantCreation_ERROR
		return nil
	}

	err = isAllowedToCreateTeamTenant(user, w.tenantMap, tenantCreation.TenantId)
	if err != nil {
		logger.Error().Err(err).Msg("TenantCreationProcessor: User is not allowed to create team tenant")
		newCreationStatus = auth_entities.TenantCreation_ERROR
		return nil
	}

	// Find and reuse the existing customer and subscription from the admin user
	// Fail early if the admin user doesn't have an existing customer or subscription for Orb
	existingOrbCustomerID := user.OrbCustomerId
	if existingOrbCustomerID == "" {
		logger.Error().Msgf(
			"TenantCreationProcessor: User %s does not have an existing Orb customer ID",
			adminUserID)
		newCreationStatus = auth_entities.TenantCreation_ERROR
		return nil
	} else {
		logger.Info().Msgf(
			"TenantCreationProcessor: Found existing Orb customer %s for user %s",
			existingOrbCustomerID, adminUserID)
	}
	logger = logger.With().
		Str("orb_customer_id", existingOrbCustomerID).
		Logger()

	existingSubscriptionID, err := userDAO.GetOrbSubscriptionID(ctx, adminUserID)
	if err != nil {
		logger.Error().Err(err).Msg("TenantCreationProcessor: Failed to get user's existing Orb subscription ID")
		return err
	}
	if existingSubscriptionID == "" {
		logger.Error().Msgf("TenantCreationProcessor: User %s does not have an existing Orb subscription", adminUserID)
		newCreationStatus = auth_entities.TenantCreation_ERROR
		return nil
	} else {
		logger.Info().Msgf("TenantCreationProcessor: Found existing subscription %s for user %s", existingSubscriptionID, adminUserID)
	}
	logger = logger.With().
		Str("orb_subscription_id", existingSubscriptionID).
		Logger()

	// For now, Use the first tenant by default after the check above
	// TODO: handle the case of multiple tenants more gracefully
	currentTenantId := user.Tenants[0]
	currentTenant, err := w.tenantMap.GetTenantByID(currentTenantId)
	if err != nil || currentTenant == nil {
		return fmt.Errorf("TenantCreationProcessor: failed to get tenant by id %s: %w", currentTenantId, err)
	}

	if tenantCreation.TenantId == "" {
		logger.Info().Msgf("TenantCreationProcessor: Creating new tenant")

		var newConfig map[string]string
		if currentTenant.Config != nil && currentTenant.Config.Configs != nil {
			newConfig = make(map[string]string, len(currentTenant.Config.Configs))
			// TODO(zhewei): review tenant configs to create team tenant configs more carefully
			for k, v := range currentTenant.Config.Configs {
				newConfig[k] = v
			}
		} else {
			newConfig = make(map[string]string)
		}
		newConfig["is_self_serve_team"] = "true"
		// Set is_legacy_self_serve_team to false for new team tenants
		newConfig["is_legacy_self_serve_team"] = "false"

		request := &tw_proto.CreateTenantRequest{
			ShardNamespace:    currentTenant.ShardNamespace,
			Cloud:             currentTenant.Cloud,
			Tier:              currentTenant.Tier,
			AuthConfiguration: currentTenant.AuthConfiguration,
			Config:            &tw_proto.Config{Configs: newConfig},
		}

		// Get service token
		scope := []tokenexchangeproto.Scope{tokenexchangeproto.Scope_AUTH_RW}
		serviceToken, err := w.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx, currentTenantId, currentTenant.ShardNamespace, scope)
		if err != nil {
			return fmt.Errorf("TenantCreationProcessor: failed to get service token: %w", err)
		}

		requestContext := rc.New(
			rc.NewRandomRequestId(),
			rc.NewRandomRequestSessionId(),
			"auth-central-grpc-svc",
			serviceToken,
		)
		ctx = rc.NewOutgoingContext(ctx, requestContext)

		newTenant, err := w.tenantMap.tenantWatcherClient.CreateTenant(ctx, request)
		if err != nil {
			return fmt.Errorf("TenantCreationProcessor: failed to create new tenant: %w", err)
		}

		test_utils.CheckPanic(TenantCreationPanicPoints.CreateNewTenantError)

		// Update the tenant creation record with the newly created tenant ID
		tenantCreation.TenantId = newTenant.Id
		_, err = tenantCreationDAO.Update(ctx, tenantCreation)
		if err != nil {
			return fmt.Errorf("TenantCreationProcessor: failed to update tenant creation record: %w", err)
		}

		test_utils.CheckPanic(TenantCreationPanicPoints.UpdateTenantCreationError)

		logger.Info().Msgf("TenantCreationProcessor: Created new tenant %s", newTenant.Name)
	} else {
		logger.Info().Msgf("TenantCreationProcessor: Tenant %s already created", tenantCreation.TenantId)
	}
	logger = logger.With().
		Str("target_tenant_id", tenantCreation.TenantId).
		Logger()

	// Retry a few times locally, since we're racing with tenant-watcher pushing the update.
	var targetTenant *tw_pb.Tenant
	for attempt := 0; attempt < maxLocalRetryAttempts; attempt++ {
		targetTenant, err = w.tenantMap.GetTenantByID(tenantCreation.TenantId)
		if err == nil && targetTenant != nil {
			break
		}
		logger.Warn().Err(err).Msgf(
			"TenantCreationProcessor: Failed to get target tenant, attempt %d/%d", attempt+1, maxLocalRetryAttempts)
		time.Sleep(localRetrySleepDuration)
	}

	if err != nil || targetTenant == nil {
		return fmt.Errorf(
			"TenantCreationProcessor: failed to get target tenant %s after %d attempts: %w",
			tenantCreation.TenantId, maxLocalRetryAttempts, err)
	}

	err = w.tenantMap.MoveUserToTenant(ctx, adminUserID, targetTenant.Id)
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to move user to target tenant: %w", err)
	}

	test_utils.CheckPanic(TenantCreationPanicPoints.MoveUserToTenantError)

	logger.Info().Msgf(
		"TenantCreationProcessor: Moved user %s to target tenant %s",
		adminUserID, targetTenant.Name)

	// Update the user's tenant mapping to add admin role
	tenantMappingDAO := w.daoFactory.GetUserTenantMappingDAO(targetTenant.Name)
	mapping, err := tenantMappingDAO.GetByUser(ctx, adminUserID)
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to get user tenant mapping: %w", err)
	}
	mapping.CustomerUiRoles = append(mapping.CustomerUiRoles, auth_entities.CustomerUiRole_ADMIN)
	_, err = tenantMappingDAO.Update(ctx, mapping)
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to update user tenant mapping: %w", err)
	}

	test_utils.CheckPanic(TenantCreationPanicPoints.UpdateUserTenantMappingError)

	// Handle Orb subscription
	// 1. Verify or create a tenant subscription mapping
	tenantSubscriptionMappingDAO := w.daoFactory.GetTenantSubscriptionMappingDAO()
	tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, targetTenant.Id)
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to get tenant subscription mapping: %w", err)
	}
	if tenantSubscriptionMapping != nil && tenantSubscriptionMapping.OrbSubscriptionId != "" {
		logger.Info().Msgf("Tenant %s already has an orb subscription", targetTenant.Id)
		// Verify the tenant subscription mapping
		if tenantSubscriptionMapping.OrbSubscriptionId != existingSubscriptionID {
			logger.Error().Msg(
				"TenantCreationProcessor: Subscription ID in tenant subscription mapping doesn't match existing subscription ID")
			newCreationStatus = auth_entities.TenantCreation_ERROR
			return nil
		}
		if tenantSubscriptionMapping.OrbCustomerId != existingOrbCustomerID {
			logger.Error().Msg(
				"TenantCreationProcessor: Orb customer ID in tenant subscription mapping doesn't match existing customer ID")
			newCreationStatus = auth_entities.TenantCreation_ERROR
			return nil
		}
	} else {
		logger.Info().Msgf("TenantCreationProcessor: Creating tenant subscription mapping")
		// Create the tenant subscription mapping
		tenantSubscriptionMapping = &auth_entities.TenantSubscriptionMapping{
			TenantId:          targetTenant.Id,
			OrbSubscriptionId: existingSubscriptionID,
			OrbCustomerId:     existingOrbCustomerID,
			StripeCustomerId:  user.StripeCustomerId,
			CreatedAt:         timestamppb.Now(),
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		}
		_, err = tenantSubscriptionMappingDAO.Create(ctx, tenantSubscriptionMapping)
		if err != nil {
			return fmt.Errorf("TenantCreationProcessor: failed to create tenant subscription mapping: %w", err)
		}

		test_utils.CheckPanic(TenantCreationPanicPoints.CreateTenantSubscriptionMappingError)

		logger.Info().Msgf("TenantCreationProcessor: Created tenant subscription mapping")
	}
	// 2. Verify the subscription record
	subscriptionDAO := w.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, existingSubscriptionID)
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to get subscription: %w", err)
	}
	if subscription == nil {
		logger.Error().Msgf("TenantCreationProcessor: Subscription %s not found", existingSubscriptionID)
		newCreationStatus = auth_entities.TenantCreation_ERROR
		return nil
	}
	// Check if the subscription is associated with the correct customer
	// First check Orb customer ID if available, otherwise fall back to Stripe customer ID for backward compatibility
	if subscription.OrbCustomerId != "" {
		if subscription.OrbCustomerId != user.OrbCustomerId {
			logger.Error().Msg("TenantCreationProcessor: Orb customer ID in subscription doesn't match admin user's Orb customer ID")
			newCreationStatus = auth_entities.TenantCreation_ERROR
			return nil
		}
	} else if subscription.StripeCustomerId != user.StripeCustomerId {
		logger.Error().Msg("TenantCreationProcessor: Stripe customer ID in subscription doesn't match admin user's Stripe customer ID")
		newCreationStatus = auth_entities.TenantCreation_ERROR
		return nil
	}
	// 3. Update owner of the subscription to the new tenant
	subscription.Owner = &auth_entities.Subscription_TenantId{TenantId: targetTenant.Id}
	_, err = subscriptionDAO.Update(ctx, subscription)
	if err != nil {
		return fmt.Errorf("TenantCreationProcessor: failed to update subscription owner: %w", err)
	}

	test_utils.CheckPanic(TenantCreationPanicPoints.UpdateSubscriptionOwnerError)

	// Update the status of the tenant creation record
	newCreationStatus = auth_entities.TenantCreation_SUCCESS

	// Publish this tenant creation to RI.
	event := ripublisher.NewTenantEvent()
	event.Event = &riproto.TenantEvent_CreateTenantForTeam{
		CreateTenantForTeam: &riproto.CreateTenantForTeam{
			Tenant:         targetTenant,
			AdminUserId:    adminUserID,
			SubscriptionId: tenantSubscriptionMapping.OrbSubscriptionId,
		},
	}
	riErr := w.requestInsightPublisher.PublishTenantEvent(ctx, &riproto.TenantInfo{
		TenantId:   targetTenant.Id,
		TenantName: targetTenant.Name,
	}, event)
	if riErr != nil {
		logger.Warn().Err(riErr).Msg("TenantCreationProcessor: Failed to publish CreateTenantForTeam event")
	}

	w.auditLogger.WriteAuditLog(
		adminUserID,
		"",
		targetTenant.Name,
		fmt.Sprintf("Tenant %s successfully created for team", targetTenant.Id),
	)

	return nil
}

package main

import (
	"fmt"

	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/checkout/session"
	"github.com/stripe/stripe-go/v80/customer"
	"github.com/stripe/stripe-go/v80/paymentmethod"
	"github.com/stripe/stripe-go/v80/setupintent"
	"github.com/stripe/stripe-go/v80/subscription"
)

// StripeClient defines the interface for Stripe operations
type StripeClient interface {
	ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error)
	CancelSubscription(subscriptionID string) error
	CreateSubscription(customerID, userID, priceID string, trialDays *int64, idempotencyKey *string) error
	FindCustomerByEmail(email string) (*stripe.Customer, error)
	CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error)
	UpdateSubscriptionSeats(subscriptionID string, seats int) error
	CreateSetupIntent(customerID string) (*stripe.SetupIntent, error)
	GetSubscription(subscriptionID string) (*stripe.Subscription, error)
	HasPaymentMethod(customerID string) (bool, error)
	ListCheckoutSessions(customerID string) ([]*stripe.CheckoutSession, error)
	ExpireCheckoutSession(checkoutSessionID string) error
	GetPaymentMethod(paymentMethodID string) (*stripe.PaymentMethod, error)
	GetPaymentMethodsForCustomer(customerID string) ([]*stripe.PaymentMethod, error)
}

// DefaultStripeClient implements the StripeClient interface using the Stripe API
type DefaultStripeClient struct{}

func (c *DefaultStripeClient) ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error) {
	params := &stripe.SubscriptionListParams{
		Customer: stripe.String(customerID),
	}

	// Check if includeCanceled parameter was provided and is true
	if len(includeCanceled) > 0 && includeCanceled[0] {
		params.Status = stripe.String("all")
	}

	iter := subscription.List(params)

	var subs []*stripe.Subscription
	for iter.Next() {
		subs = append(subs, iter.Subscription())
	}
	return subs, iter.Err()
}

func (c *DefaultStripeClient) CancelSubscription(subscriptionID string) error {
	_, err := subscription.Cancel(subscriptionID, nil)
	return err
}

func (c *DefaultStripeClient) CreateSubscription(
	customerID, userID, priceID string, trialDays *int64, idempotencyKey *string,
) error {
	params := &stripe.SubscriptionParams{
		Customer: stripe.String(customerID),
		Items: []*stripe.SubscriptionItemsParams{
			{
				Price: stripe.String(priceID),
			},
		},
		Metadata: map[string]string{
			"augment_user_id": userID,
		},
	}
	if idempotencyKey != nil {
		params.IdempotencyKey = idempotencyKey
	}

	if trialDays != nil {
		params.TrialPeriodDays = trialDays
		params.TrialSettings = &stripe.SubscriptionTrialSettingsParams{
			EndBehavior: &stripe.SubscriptionTrialSettingsEndBehaviorParams{
				MissingPaymentMethod: stripe.String(string(stripe.SubscriptionTrialSettingsEndBehaviorMissingPaymentMethodCancel)),
			},
		}
	}

	_, err := subscription.New(params)
	return err
}

func (c *DefaultStripeClient) FindCustomerByEmail(email string) (*stripe.Customer, error) {
	params := &stripe.CustomerListParams{
		Email: stripe.String(email),
	}
	iter := customer.List(params)

	if !iter.Next() {
		return nil, nil
	}
	return iter.Customer(), iter.Err()
}

// CreateCustomer creates a new Stripe customer
func (c *DefaultStripeClient) CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error) {
	return customer.New(params)
}

// CreateSetupIntent creates a new SetupIntent for a customer
func (c *DefaultStripeClient) CreateSetupIntent(customerID string) (*stripe.SetupIntent, error) {
	params := &stripe.SetupIntentParams{
		Customer: stripe.String(customerID),
		PaymentMethodTypes: []*string{
			stripe.String("card"),
		},
		Usage: stripe.String(string(stripe.SetupIntentUsageOffSession)),
	}

	return setupintent.New(params)
}

func (c *DefaultStripeClient) UpdateSubscriptionSeats(subscriptionID string, seats int) error {
	// First, get the subscription to find the subscription item ID
	sub, err := subscription.Get(subscriptionID, nil)
	if err != nil {
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	// Make sure there's at least one item
	if len(sub.Items.Data) == 0 {
		return fmt.Errorf("subscription has no items")
	}

	if len(sub.Items.Data) > 1 {
		log.Warn().
			Str("subscription_id", subscriptionID).
			Int("item_count", len(sub.Items.Data)).
			Msg("Subscription has more than one item, only the first will be updated")
	}

	// Create the subscription update params
	params := &stripe.SubscriptionParams{
		Items: []*stripe.SubscriptionItemsParams{
			{
				ID:       stripe.String(sub.Items.Data[0].ID),
				Quantity: stripe.Int64(int64(seats)),
			},
		},
	}

	// Update the subscription
	_, err = subscription.Update(subscriptionID, params)
	if err != nil {
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	return nil
}

func (c *DefaultStripeClient) GetSubscription(subscriptionID string) (*stripe.Subscription, error) {
	return subscription.Get(subscriptionID, nil)
}

// HasPaymentMethod checks if a customer has a valid payment method
func (c *DefaultStripeClient) HasPaymentMethod(customerID string) (bool, error) {
	params := &stripe.CustomerListPaymentMethodsParams{
		Customer: stripe.String(customerID),
	}

	// Only need to check if there's at least one payment method
	params.Limit = stripe.Int64(1)
	i := customer.ListPaymentMethods(params)

	// Check if there's at least one payment method
	return i.Next(), i.Err()
}

func (c *DefaultStripeClient) ListCheckoutSessions(customerID string) ([]*stripe.CheckoutSession, error) {
	params := &stripe.CheckoutSessionListParams{
		Customer: stripe.String(customerID),
		Status:   stripe.String("open"),
	}

	iter := session.List(params)

	var sessions []*stripe.CheckoutSession
	for iter.Next() {
		sessions = append(sessions, iter.CheckoutSession())
	}
	return sessions, iter.Err()
}

func (c *DefaultStripeClient) ExpireCheckoutSession(checkoutSessionID string) error {
	_, err := session.Expire(checkoutSessionID, nil)
	return err
}

func (c *DefaultStripeClient) GetPaymentMethod(paymentMethodID string) (*stripe.PaymentMethod, error) {
	return paymentmethod.Get(paymentMethodID, nil)
}

func (c *DefaultStripeClient) GetPaymentMethodsForCustomer(customerID string) ([]*stripe.PaymentMethod, error) {
	params := &stripe.CustomerListPaymentMethodsParams{
		Customer: stripe.String(customerID),
	}
	iter := customer.ListPaymentMethods(params)

	var paymentMethods []*stripe.PaymentMethod
	for iter.Next() {
		pm := iter.PaymentMethod()
		paymentMethods = append(paymentMethods, pm)
	}
	return paymentMethods, iter.Err()
}

// MockStripeClient implements a mock for testing with real state
type MockStripeClient struct {
	// Internal state
	subscriptions          map[string]*stripe.Subscription // Map of subscription ID to subscription
	customerSubs           map[string]map[string]struct{}  // Map of customer ID to set of subscription IDs
	subOwnership           map[string]string               // Map of subscription ID to customer ID
	customers              map[string]*stripe.Customer     // Map of email to customer
	subscriptionSeats      map[string][]int                // Map of subscription ID to list of seat updates
	setupIntents           map[string]*stripe.SetupIntent  // Map of setup intent ID to setup intent
	customerPaymentMethods map[string]bool                 // Map of customer ID to payment method status
}

// NewMockStripeClient creates a new mock client
func NewMockStripeClient() *MockStripeClient {
	return &MockStripeClient{
		subscriptions:          make(map[string]*stripe.Subscription),
		customerSubs:           make(map[string]map[string]struct{}),
		subOwnership:           make(map[string]string),
		customers:              make(map[string]*stripe.Customer),
		subscriptionSeats:      make(map[string][]int),
		setupIntents:           make(map[string]*stripe.SetupIntent),
		customerPaymentMethods: make(map[string]bool),
	}
}

// AddSubscription adds a subscription to the mock's state
func (m *MockStripeClient) AddSubscription(customerID string, userID string, subID string, priceID string) {
	sub := &stripe.Subscription{
		ID: subID,
		Items: &stripe.SubscriptionItemList{
			Data: []*stripe.SubscriptionItem{
				{
					Price: &stripe.Price{
						ID: priceID,
					},
				},
			},
		},
		Metadata: map[string]string{
			"augment_user_id": userID,
		},
		Customer: &stripe.Customer{
			ID: customerID,
		},
	}

	m.subscriptions[subID] = sub
	m.subOwnership[subID] = customerID

	if _, exists := m.customerSubs[customerID]; !exists {
		m.customerSubs[customerID] = make(map[string]struct{})
	}
	m.customerSubs[customerID][subID] = struct{}{}
}

// AddDetailedSubscription adds a subscription with detailed fields to the mock's state
func (m *MockStripeClient) AddDetailedSubscription(subscription *stripe.Subscription) {
	if subscription == nil {
		return
	}

	// Store the subscription
	m.subscriptions[subscription.ID] = subscription

	// Set up ownership mapping if customer is provided
	if subscription.Customer != nil {
		customerID := subscription.Customer.ID
		m.subOwnership[subscription.ID] = customerID

		// Add to customer's subscriptions
		if _, exists := m.customerSubs[customerID]; !exists {
			m.customerSubs[customerID] = make(map[string]struct{})
		}
		m.customerSubs[customerID][subscription.ID] = struct{}{}
	}
}

// ListSubscriptions returns a list of subscriptions for a customer
//
// Ignores the includeCanceled parameter
func (m *MockStripeClient) ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error) {
	var subs []*stripe.Subscription

	// Get subscription IDs for this customer
	subIDs, exists := m.customerSubs[customerID]
	if !exists {
		return subs, nil
	}

	// Get the actual subscription objects
	for subID := range subIDs {
		if sub, exists := m.subscriptions[subID]; exists {
			subs = append(subs, sub)
		}
	}

	return subs, nil
}

func (m *MockStripeClient) GetSubscription(subscriptionID string) (*stripe.Subscription, error) {
	sub, exists := m.subscriptions[subscriptionID]
	if !exists {
		return nil, fmt.Errorf("subscription not found")
	}
	return sub, nil
}

// CancelSubscription cancels a subscription
func (m *MockStripeClient) CancelSubscription(subscriptionID string) error {
	// Remove from subscriptions map
	delete(m.subscriptions, subscriptionID)

	// Get the customer ID directly from the ownership map
	if customerID, exists := m.subOwnership[subscriptionID]; exists {
		// Remove the subscription from the customer's set
		if subSet, ok := m.customerSubs[customerID]; ok {
			delete(subSet, subscriptionID)
		}
		// Remove from ownership map
		delete(m.subOwnership, subscriptionID)
	}

	return nil
}

// CreateSubscription creates a new subscription
func (m *MockStripeClient) CreateSubscription(
	customerID, userID, priceID string, trialDays *int64, idempotencyKey *string,
) error {
	// Generate a new subscription ID
	subID := fmt.Sprintf("sub_new_%d", len(m.subscriptions)+1)

	// Add the subscription to our state
	m.AddSubscription(customerID, userID, subID, priceID)

	return nil
}

// GetSubscriptionsForCustomer returns all subscriptions for a customer
func (m *MockStripeClient) GetSubscriptionsForCustomer(customerID string) []*stripe.Subscription {
	subs, _ := m.ListSubscriptions(customerID)
	return subs
}

// FindCustomerByEmail finds a customer by email
func (m *MockStripeClient) FindCustomerByEmail(email string) (*stripe.Customer, error) {
	customer, exists := m.customers[email]
	if !exists {
		return nil, nil
	}
	return customer, nil
}

// AddCustomer adds a customer to the mock's state
func (m *MockStripeClient) AddCustomer(email string, customerID string) {
	m.customers[email] = &stripe.Customer{
		ID:    customerID,
		Email: email,
	}
}

func (m *MockStripeClient) CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error) {
	// Generate a new customer ID
	customerID := fmt.Sprintf("cus_new_%d", len(m.customers)+1)

	// Add the customer to our state
	m.AddCustomer(*params.Email, customerID)

	return m.customers[*params.Email], nil
}

// CreateSetupIntent creates a new SetupIntent for a customer
func (m *MockStripeClient) CreateSetupIntent(customerID string) (*stripe.SetupIntent, error) {
	// Generate a new setup intent ID
	setupIntentID := fmt.Sprintf("seti_new_%d", len(m.setupIntents)+1)

	// Create a new setup intent
	setupIntent := &stripe.SetupIntent{
		ID: setupIntentID,
		Customer: &stripe.Customer{
			ID: customerID,
		},
		Status: stripe.SetupIntentStatusRequiresPaymentMethod,
		Usage:  stripe.SetupIntentUsageOffSession,
	}

	// Add the setup intent to our state
	m.setupIntents[setupIntentID] = setupIntent

	return setupIntent, nil
}

// UpdateSubscriptionSeats updates the seat count for a subscription
func (m *MockStripeClient) UpdateSubscriptionSeats(subscriptionID string, seats int) error {
	// Check if the subscription exists
	sub, exists := m.subscriptions[subscriptionID]
	if !exists {
		return fmt.Errorf("subscription not found")
	}

	// Make sure there's at least one item
	if len(sub.Items.Data) == 0 {
		return fmt.Errorf("subscription has no items")
	}

	// Track the seat update
	if _, exists := m.subscriptionSeats[subscriptionID]; !exists {
		m.subscriptionSeats[subscriptionID] = []int{}
	}
	m.subscriptionSeats[subscriptionID] = append(m.subscriptionSeats[subscriptionID], seats)

	// Note: We don't actually update the subscription in our state
	// because that's the webhook's job. We just track the call.

	return nil
}

// GetSubscriptionUpdates returns the list of seat updates for a subscription
func (m *MockStripeClient) GetSubscriptionUpdates(subscriptionID string) []int {
	if updates, exists := m.subscriptionSeats[subscriptionID]; exists {
		return updates
	}
	return []int{}
}

// HasPaymentMethod checks if a customer has a valid payment method
func (m *MockStripeClient) HasPaymentMethod(customerID string) (bool, error) {
	// Check if we've explicitly set payment method status for this customer
	if hasPayment, exists := m.customerPaymentMethods[customerID]; exists {
		return hasPayment, nil
	}

	// Default fallback behavior (can be changed for specific tests)
	return true, nil
}

// SetCustomerHasPaymentMethod allows tests to configure whether a customer has payment methods
func (m *MockStripeClient) SetCustomerHasPaymentMethod(customerID string, hasPayment bool) {
	if m.customerPaymentMethods == nil {
		m.customerPaymentMethods = make(map[string]bool)
	}
	m.customerPaymentMethods[customerID] = hasPayment
}

func (m *MockStripeClient) ListCheckoutSessions(customerID string) ([]*stripe.CheckoutSession, error) {
	return []*stripe.CheckoutSession{}, nil
}

func (m *MockStripeClient) ExpireCheckoutSession(checkoutSessionID string) error {
	return nil
}

func (m *MockStripeClient) GetPaymentMethod(paymentMethodID string) (*stripe.PaymentMethod, error) {
	return &stripe.PaymentMethod{
		ID: paymentMethodID,
		BillingDetails: &stripe.PaymentMethodBillingDetails{
			Name: "Test User",
			Address: &stripe.Address{
				Line1:      "123 Test St",
				City:       "Test City",
				State:      "Test State",
				PostalCode: "12345",
				Country:    "US",
			},
		},
	}, nil
}

func (m *MockStripeClient) GetPaymentMethodsForCustomer(customerID string) ([]*stripe.PaymentMethod, error) {
	if paymentMethods, exists := m.customerPaymentMethods[customerID]; exists && paymentMethods {
		return []*stripe.PaymentMethod{
			{
				ID: "pm_123",
				BillingDetails: &stripe.PaymentMethodBillingDetails{
					Name: "Test User",
					Address: &stripe.Address{
						Line1:      "123 Test St",
						City:       "Test City",
						State:      "Test State",
						PostalCode: "12345",
						Country:    "US",
					},
				},
			},
		}, nil
	}
	return []*stripe.PaymentMethod{}, nil
}

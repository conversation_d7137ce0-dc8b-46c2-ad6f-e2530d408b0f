package test_utils

import (
	"fmt"
)

// enabledPanicPoints tracks which panic points are enabled
var enabledPanicPoints = make(map[string]bool)

// EnablePanicPoint enables a specific panic point for testing
func EnablePanicPoint(point string) {
	enabledPanicPoints[point] = true
}

// DisablePanicPoint disables a specific panic point
func DisablePanicPoint(point string) {
	delete(enabledPanicPoints, point)
}

// DisableAllPanicPoints disables all panic points
func DisableAllPanicPoints() {
	enabledPanicPoints = make(map[string]bool)
}

// CheckPanic checks if a panic point is enabled and panics if it is
func CheckPanic(point string) {
	if enabledPanicPoints[point] {
		panic(fmt.Sprintf("Test panic triggered: %s", point))
	}
}

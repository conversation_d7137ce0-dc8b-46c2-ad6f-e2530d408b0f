package main

import (
	"context"
	"fmt"
	"testing"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// MockRandomSelector for testing random tenant selection
type MockRandomSelector struct {
	returnValue int
}

func (m *MockRandomSelector) Intn(n int) int {
	return m.returnValue
}

const (
	authorizedUserID            = "test-user"
	adminUserID                 = "admin-user"
	nonAdminUserID              = "non-admin-user"
	professionalTenantID        = "professional-tenant"
	communityTenantID           = "community-tenant"
	enterpriseTenantID          = "enterprise-tenant"
	selfServeTeamTenantID       = "self-serve-team-tenant"
	selfServeTeamTenantID2      = "self-serve-team-tenant2"
	legacySelfServeTeamTenantID = "legacy-self-serve-team-tenant"
	enterpriseDomain            = "enterprise-domain.com"
	// Orb Constants
	orbTrialPlanID                    = "orb_trial_plan"
	orbTrialPlanSeatPriceID           = "orb-trial-plan-seat-price-id"
	orbTrialPlanCreditPriceID         = "orb-trial-plan-credit-price-id"
	orbProfessionalPlanID             = "orb_developer_plan"
	orbProfessionalPlanSeatPriceID    = "orb-developer-plan-seat-price-id"
	orbProfessionalPlanCreditPriceID  = "orb-developer-plan-credit-price-id"
	orbCommunityPlanID                = "orb_community_plan"
	orbCommunityPlanSeatPriceID       = "orb-community-plan-seat-price-id"
	orbCommunityPlanCreditPriceID     = "orb-community-plan-credit-price-id"
	orbProfessionalPlanMonthlyCredits = 600
	orbCommunityPlanMonthlyCredits    = 50
	orbTrialPlanCredits               = 600
	orbCurrency                       = "usermessages"
)

func authorizedContext(tenantID string, userID, scope string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:         tenantID,
		TenantName:       tenantID,
		UserID:           userID,
		OpaqueUserID:     userID,
		OpaqueUserIDType: "AUGMENT",
		UserEmail:        "<EMAIL>",
		Scope:            []string{scope},
	}
	return claims.NewContext(context.Background())
}

func iapAuthorizedContext(tenantID string, scope string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:         tenantID,
		TenantName:       tenantID,
		UserID:           "iap:<EMAIL>",
		OpaqueUserID:     "iap:<EMAIL>",
		OpaqueUserIDType: "INTERNAL_IAP",
		UserEmail:        "<EMAIL>",
		Scope:            []string{scope},
	}
	return claims.NewContext(context.Background())
}

func ensureAdmin(server *TeamManagementServer, userID string, tenantID string) error {
	userDAO := server.daoFactory.GetUserDAO()

	user := &auth_entities.User{
		Id:                userID,
		Email:             "<EMAIL>",
		Tenants:           []string{tenantID},
		BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		StripeCustomerId:  "stripe-customer-id",
		OrbCustomerId:     "orb-customer-id",
		OrbSubscriptionId: "orb-subscription-id",
	}

	_, err := userDAO.Create(context.Background(), user)
	if err != nil {
		return err
	}

	tenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(tenantID)
	mapping := tenantMappingDAO.Instantiate()
	mapping.Tenant = tenantID
	mapping.UserId = userID
	mapping.CustomerUiRoles = []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN}
	_, err = tenantMappingDAO.Create(context.Background(), mapping)
	return err
}

func ensureNonAdmin(server *TeamManagementServer, userID string, tenantID string) error {
	userDAO := server.daoFactory.GetUserDAO()

	user := &auth_entities.User{
		Id:                userID,
		Email:             "<EMAIL>",
		Tenants:           []string{tenantID},
		BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		StripeCustomerId:  "stripe-customer-id-nonadmin",
		OrbCustomerId:     "orb-customer-id-nonadmin",
		OrbSubscriptionId: "orb-subscription-id-nonadmin",
	}

	_, err := userDAO.Create(context.Background(), user)
	if err != nil {
		return err
	}

	tenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(tenantID)
	mapping := tenantMappingDAO.Instantiate()
	mapping.Tenant = tenantID
	mapping.UserId = userID
	_, err = tenantMappingDAO.Create(context.Background(), mapping)
	return err
}

func getUser(t *testing.T, server *TeamManagementServer, userID string) *auth_entities.User {
	userDAO := server.daoFactory.GetUserDAO()
	user, err := userDAO.Get(context.Background(), userID)
	require.NoError(t, err)
	return user
}

func getSubscription(t *testing.T, server *TeamManagementServer, subscriptionID string) *auth_entities.Subscription {
	subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(context.Background(), subscriptionID)
	require.NoError(t, err)
	return subscription
}

func newTestTeamManagementServer(t *testing.T) (server *TeamManagementServer, cleanup func()) {
	bigtableFixture := NewBigtableFixture(t)
	daoFactoryFixture := NewDAOFactoryFixture(bigtableFixture)

	professionalTenant := &tw_proto.Tenant{
		Id:             professionalTenantID,
		Name:           professionalTenantID,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "test-namespace",
		Cloud:          "CLOUD_PROD",
	}
	communityTenant := &tw_proto.Tenant{
		Id:             communityTenantID,
		Name:           communityTenantID,
		Tier:           tw_proto.TenantTier_COMMUNITY,
		ShardNamespace: "test-namespace",
		Cloud:          "CLOUD_PROD",
	}
	enterpriseTenant := &tw_proto.Tenant{
		Id:             enterpriseTenantID,
		Name:           enterpriseTenantID,
		Tier:           tw_proto.TenantTier_ENTERPRISE,
		ShardNamespace: "test-namespace-enterprise",
		Cloud:          "CLOUD_PROD",
		AuthConfiguration: &tw_proto.AuthConfiguration{
			Domain:              enterpriseDomain,
			UsernameDomains:     []string{},
			EmailAddressDomains: []string{},
		},
	}
	selfServeTeamTenant := &tw_proto.Tenant{
		Id:             selfServeTeamTenantID,
		Name:           selfServeTeamTenantID,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "test-namespace-self-serve-team",
		Cloud:          "CLOUD_PROD",
		Config: &tw_proto.Config{
			Configs: map[string]string{
				"is_self_serve_team": "true",
			},
		},
	}
	selfServeTeamTenant2 := &tw_proto.Tenant{
		Id:             selfServeTeamTenantID2,
		Name:           selfServeTeamTenantID2,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "test-namespace-self-serve-team",
		Cloud:          "CLOUD_PROD",
		Config: &tw_proto.Config{
			Configs: map[string]string{
				"is_self_serve_team": "true",
			},
		},
	}

	legacySelfServeTeamTenant := &tw_proto.Tenant{
		Id:             legacySelfServeTeamTenantID,
		Name:           legacySelfServeTeamTenantID,
		Tier:           tw_proto.TenantTier_PROFESSIONAL,
		ShardNamespace: "test-namespace-legacy-self-serve-team",
		Cloud:          "CLOUD_PROD",
		Config: &tw_proto.Config{
			Configs: map[string]string{
				"is_self_serve_team":        "true",
				"is_legacy_self_serve_team": "true",
			},
		},
	}

	mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
		Tenants: []*tw_proto.Tenant{
			professionalTenant,
			communityTenant,
			enterpriseTenant,
			selfServeTeamTenant,
			selfServeTeamTenant2,
			legacySelfServeTeamTenant,
		},
	}
	ffHandle := featureflags.NewLocalFeatureFlagHandler()
	ffHandle.Set("auth_central_team_management_enabled", true)

	tenantMap := NewTenantMap(
		daoFactoryFixture.DAOFactory,
		mockTenantWatcherClient,
		"us-central.api.augmentcode.com",
		ffHandle,
		NewMockAsyncOpsPublisher(),
		audit.NewDefaultAuditLogger(),
	)

	mockPublisher := NewMockAsyncOpsPublisher()
	mockStripeClient := NewMockStripeClient()
	mockOrbClient := orb.NewMockOrbClient()

	mockAuditLogger := audit.NewDefaultAuditLogger()

	server = &TeamManagementServer{
		daoFactory:              daoFactoryFixture.DAOFactory,
		tenantMap:               tenantMap,
		featureFlagHandle:       ffHandle,
		auditLogger:             mockAuditLogger,
		asyncOpsPublisher:       mockPublisher,
		stripeClient:            mockStripeClient,
		requestInsightPublisher: ripublisher.NewRequestInsightPublisherMock(),
		randomSelector:          &DefaultRandomSelector{},
		orbConfig: &OrbConfig{
			Enabled:          true,
			MinAddonPurchase: 10,
			MaxAddonPurchase: 100,
			PricingUnit:      orbCurrency,
			CostPerMessage:   0.1,
			Plans: []PlanConfig{
				{
					ID: orbTrialPlanID,
					Features: PlanFeatures{
						TrainingAllowed:     false,
						TeamsAllowed:        true,
						MaxSeats:            100,
						AddCreditsAvailable: false,
						PlanType:            PlanTypePaidTrial,
					},
				},
				{
					ID: orbProfessionalPlanID,
					Features: PlanFeatures{
						TrainingAllowed:     true,
						TeamsAllowed:        true,
						MaxSeats:            100,
						AddCreditsAvailable: true,
						PlanType:            PlanTypePaid,
					},
				},
				{
					ID: orbCommunityPlanID,
					Features: PlanFeatures{
						TrainingAllowed:     true,
						TeamsAllowed:        false,
						MaxSeats:            1,
						AddCreditsAvailable: true,
						PlanType:            PlanTypeCommunity,
					},
				},
			},
		},
		orbClient: mockOrbClient,
	}
	cleanup = func() {
		bigtableFixture.Cleanup()
		daoFactoryFixture.Cleanup()
		mockTenantWatcherClient.Close()
	}

	// Set up all test servers with a default admin user for the self-serve team tenant.
	ensureAdmin(server, adminUserID, selfServeTeamTenantID)

	return server, cleanup
}

// Run basic auth tests that apply to all endpoints.
func runAuthTests[ReqT proto.Message, RespT proto.Message](
	t *testing.T,
	requiredScope string,
	checkTenant bool,
	checkUser bool,
	adminOnly bool,
	goodRequest ReqT,
	requestFunc func(context.Context, ReqT) (RespT, error),
) {
	t.Run("auth validation", func(t *testing.T) {
		badContexts := []context.Context{}
		goodContexts := []context.Context{}

		// Missing auth claims.
		badContexts = append(badContexts, context.Background())

		// Missing scope.
		missingScopeClaims := &auth.AugmentClaims{
			TenantID:         selfServeTeamTenantID,
			TenantName:       selfServeTeamTenantID,
			UserID:           authorizedUserID,
			OpaqueUserID:     authorizedUserID,
			OpaqueUserIDType: "AUGMENT",
			Scope:            []string{},
		}
		badContexts = append(badContexts, missingScopeClaims.NewContext(context.Background()))

		if checkTenant {
			// Wrong tenant.
			wrongTenantClaims := &auth.AugmentClaims{
				TenantID:         "wrong-tenant",
				TenantName:       "wrong-tenant",
				UserID:           authorizedUserID,
				OpaqueUserID:     authorizedUserID,
				OpaqueUserIDType: "AUGMENT",
				Scope:            []string{requiredScope},
			}
			badContexts = append(badContexts, wrongTenantClaims.NewContext(context.Background()))

			// Wildcard tenant.
			wildcardTenantClaims := &auth.AugmentClaims{
				TenantID:         auth.TenantIDWildcard,
				UserID:           authorizedUserID,
				OpaqueUserID:     authorizedUserID,
				OpaqueUserIDType: "AUGMENT",
				Scope:            []string{requiredScope},
			}

			// Admin requests get tenant from the auth claims, so wildcard doesn't apply
			if !adminOnly {
				goodContexts = append(goodContexts, wildcardTenantClaims.NewContext(context.Background()))
			}
		}

		if checkUser {
			// Wrong user.
			wrongUserClaims := &auth.AugmentClaims{
				TenantID:         selfServeTeamTenantID,
				TenantName:       selfServeTeamTenantID,
				UserID:           "wrong-user",
				OpaqueUserID:     "wrong-user",
				OpaqueUserIDType: "AUGMENT",
				Scope:            []string{requiredScope},
			}
			badContexts = append(badContexts, wrongUserClaims.NewContext(context.Background()))

			// Token user.
			tokenUserClaims := &auth.AugmentClaims{
				TenantID:         selfServeTeamTenantID,
				TenantName:       selfServeTeamTenantID,
				UserID:           authorizedUserID,
				OpaqueUserID:     authorizedUserID,
				OpaqueUserIDType: "API_TOKEN",
				Scope:            []string{requiredScope},
			}
			badContexts = append(badContexts, tokenUserClaims.NewContext(context.Background()))
		}

		if adminOnly {
			// Non-admin user.
			nonAdminClaims := &auth.AugmentClaims{
				TenantID:         selfServeTeamTenantID,
				TenantName:       selfServeTeamTenantID,
				UserID:           authorizedUserID,
				OpaqueUserID:     authorizedUserID,
				OpaqueUserIDType: "AUGMENT",
				Scope:            []string{requiredScope},
			}
			badContexts = append(badContexts, nonAdminClaims.NewContext(context.Background()))
		}

		// Test all the bad claims.
		for _, badCtx := range badContexts {
			_, err := requestFunc(badCtx, goodRequest)
			require.Error(t, err)
			assert.Equal(t, codes.PermissionDenied, status.Code(err))
		}

		// Make sure we don't get "permission denied" with good claims.
		var goodCtx context.Context
		if adminOnly {
			goodCtx = authorizedContext(selfServeTeamTenantID, adminUserID, requiredScope)
		} else {
			goodCtx = authorizedContext(selfServeTeamTenantID, authorizedUserID, requiredScope)
		}
		goodContexts = append(goodContexts, goodCtx)

		// IAP user should be able to access the endpoint
		iapCtx := iapAuthorizedContext(selfServeTeamTenantID, requiredScope)
		goodContexts = append(goodContexts, iapCtx)

		for _, goodCtx := range goodContexts {
			_, err := requestFunc(goodCtx, goodRequest)
			if err != nil {
				assert.NotEqual(t, codes.PermissionDenied, status.Code(err))
			}
		}
	})
}

// runWildcardAuthTests is a simplified version of runAuthTests for endpoints that require wildcard tenant access
func runWildcardAuthTests[ReqT proto.Message, RespT proto.Message](
	t *testing.T,
	requiredScope string,
	goodRequest ReqT,
	requestFunc func(context.Context, ReqT) (RespT, error),
) {
	t.Run("auth validation", func(t *testing.T) {
		// Test missing auth claims
		_, err := requestFunc(context.Background(), goodRequest)
		require.Error(t, err)
		assert.Equal(t, codes.PermissionDenied, status.Code(err))

		// Test missing scope
		badScopeClaims := &auth.AugmentClaims{
			TenantID:         auth.TenantIDWildcard,
			UserID:           authorizedUserID,
			OpaqueUserID:     authorizedUserID,
			OpaqueUserIDType: "AUGMENT",
			Scope:            []string{}, // Wrong scope
		}
		_, err = requestFunc(badScopeClaims.NewContext(context.Background()), goodRequest)
		require.Error(t, err)
		assert.Equal(t, codes.PermissionDenied, status.Code(err))

		// Test tenant-specific access (should fail for wildcard endpoints)
		tenantSpecificClaims := &auth.AugmentClaims{
			TenantID:         selfServeTeamTenantID,
			UserID:           authorizedUserID,
			OpaqueUserID:     authorizedUserID,
			OpaqueUserIDType: "AUGMENT",
			Scope:            []string{requiredScope},
		}
		_, err = requestFunc(tenantSpecificClaims.NewContext(context.Background()), goodRequest)
		require.Error(t, err)
		assert.Equal(t, codes.PermissionDenied, status.Code(err))

		// Test correct wildcard access (should not return PermissionDenied)
		goodClaims := &auth.AugmentClaims{
			TenantID:         auth.TenantIDWildcard,
			UserID:           authorizedUserID,
			OpaqueUserID:     authorizedUserID,
			OpaqueUserIDType: "AUGMENT",
			Scope:            []string{requiredScope},
		}
		_, err = requestFunc(goodClaims.NewContext(context.Background()), goodRequest)
		// We only care that it's not a permission denied error
		// The actual call might fail for other reasons (e.g., no data)
		if err != nil {
			assert.NotEqual(t, codes.PermissionDenied, status.Code(err))
		}
	})
}

func runAuthTestsForAllTenants[ReqT proto.Message, RespT proto.Message](
	t *testing.T,
	requiredScope string,
	goodRequest ReqT,
	requestFunc func(context.Context, ReqT) (RespT, error),
) {
	type testCase struct {
		name    string
		context context.Context
	}

	badCases := []testCase{
		{
			name:    "MissingAuthClaims",
			context: context.Background(),
		},
		{
			name: "MissingScope",
			context: (&auth.AugmentClaims{
				TenantID:         auth.TenantIDWildcard,
				UserID:           authorizedUserID,
				OpaqueUserID:     authorizedUserID,
				OpaqueUserIDType: "AUGMENT",
				Scope:            []string{},
			}).NewContext(context.Background()),
		},
		{
			name: "TenantSpecific",
			context: (&auth.AugmentClaims{
				TenantID:         selfServeTeamTenantID,
				UserID:           authorizedUserID,
				OpaqueUserID:     authorizedUserID,
				OpaqueUserIDType: "AUGMENT",
				Scope:            []string{requiredScope},
			}).NewContext(context.Background()),
		},
	}

	for _, tc := range badCases {
		t.Run(tc.name, func(t *testing.T) {
			_, err := requestFunc(tc.context, goodRequest)
			require.Error(t, err)
			assert.Equal(t, codes.PermissionDenied, status.Code(err))
		})
	}

	goodCases := []testCase{
		{
			name: "Token with tenant wildcard",
			context: (&auth.AugmentClaims{
				TenantID:         auth.TenantIDWildcard,
				UserID:           authorizedUserID,
				OpaqueUserID:     authorizedUserID,
				OpaqueUserIDType: "AUGMENT",
				Scope:            []string{requiredScope},
			}).NewContext(context.Background()),
		},
	}

	for _, tc := range goodCases {
		t.Run(tc.name, func(t *testing.T) {
			_, err := requestFunc(tc.context, goodRequest)
			require.NoError(t, err)
		})
	}
}

func TestCreateTenantForTeam(t *testing.T) {
	t.Run("AuthTests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly
			&authpb.CreateTenantForTeamRequest{
				AdminUserId: authorizedUserID,
			},
			server.CreateTenantForTeam,
		)
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:                authorizedUserID,
			Email:             "<EMAIL>",
			Tenants:           []string{professionalTenantID},
			StripeCustomerId:  "stripe-customer-id",
			OrbCustomerId:     "orb-customer-id",
			OrbSubscriptionId: "orb-subscription-id",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		subscription := &auth_entities.Subscription{
			SubscriptionId:   "orb-subscription-id",
			StripeCustomerId: "stripe-customer-id",
			PriceId:          "price_test",
			Owner:            &auth_entities.Subscription_UserId{UserId: authorizedUserID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err)

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		resp, err := server.CreateTenantForTeam(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.NotEmpty(t, resp.TenantCreationId)

		// Verify that a tenant creation record was created
		tenantCreationDAO := server.daoFactory.GetTenantCreationDAO()
		tenantCreation, err := tenantCreationDAO.Get(ctx, resp.TenantCreationId)
		require.NoError(t, err)
		require.NotNil(t, tenantCreation)
		require.Equal(t, resp.TenantCreationId, tenantCreation.Id)
		require.Equal(t, auth_entities.TenantCreation_PENDING, tenantCreation.Status)

		// Verify that a message was published
		messages := server.asyncOpsPublisher.(*MockAsyncOpsPublisher).GetPublishedMessages()
		require.Len(t, messages, 1)

		publishedMsg := messages[0].GetCreateTenantForTeamMessage()
		require.NotNil(t, publishedMsg)
		require.Equal(t, resp.TenantCreationId, publishedMsg.TenantCreationId)
		require.Equal(t, authorizedUserID, publishedMsg.TenantCreationRequest.AdminUserId)
		require.NotNil(t, publishedMsg.PublishTime)
	})

	t.Run("UserNotFound", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err := server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))
		require.Contains(t, err.Error(), "User not found")
	})

	// User with a Stripe subscription but no orb subscription.
	t.Run("Stripe subscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		stripeSubscriptionID := "stripe-subscription-id"
		user := &auth_entities.User{
			Id:               authorizedUserID,
			Email:            "<EMAIL>",
			Tenants:          []string{professionalTenantID},
			StripeCustomerId: "stripe-customer-id",
			SubscriptionId:   &stripeSubscriptionID,
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		subscription := &auth_entities.Subscription{
			SubscriptionId:   stripeSubscriptionID,
			StripeCustomerId: "stripe-customer-id",
			PriceId:          "price_test",
			Owner:            &auth_entities.Subscription_UserId{UserId: authorizedUserID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err)

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
	})

	// User with a recorded orb subscription but no subscription record in our database. This can
	// happen if there's a race or bug with the orb webhook.
	t.Run("Subsription not fond", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:                authorizedUserID,
			Email:             "<EMAIL>",
			Tenants:           []string{professionalTenantID},
			StripeCustomerId:  "stripe-customer-id",
			OrbCustomerId:     "orb-customer-id",
			OrbSubscriptionId: "orb-subscription-id",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Note: We don't create the subscription in the database, so it will be not found
		// This simulates the case where the user has an OrbSubscriptionId but the subscription
		// record doesn't exist in our database

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
	})

	t.Run("UserWithTierChange", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{professionalTenantID},
			TierChange: &auth_entities.User_TierChangeInfo{
				Id:         "tier-change-id",
				TargetTier: auth_entities.UserTier_PROFESSIONAL,
				CreatedAt:  timestamppb.Now(),
				UpdatedAt:  timestamppb.Now(),
			},
		}

		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
		require.Contains(t, err.Error(), "User has a tier change in progress")
	})

	t.Run("UserWithNoTenants", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
		require.Contains(t, err.Error(), "User does not belong to any tenant")
	})

	t.Run("UserInEnterpriseTenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(enterpriseTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{enterpriseTenantID},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
		require.Contains(t, err.Error(), "User belongs to an enterprise tenant")
	})

	t.Run("UserInCommunityTenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(communityTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{communityTenantID},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
	})

	t.Run("UserInSelfServeTeamTenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{selfServeTeamTenantID},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}

		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
		require.Contains(t, err.Error(), "User belongs to a self-serve team tenant")
	})
}

func TestGetCreateTenantForTeamStatus(t *testing.T) {
	t.Run("AuthTests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_R",
			false, // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.GetCreateTenantForTeamStatusRequest{
				TenantCreationId: "test-tenant-creation-id",
			},
			server.GetCreateTenantForTeamStatus,
		)
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		tenantCreationDAO := server.daoFactory.GetTenantCreationDAO()
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		tenantCreationID := uuid.New().String()
		tenantCreation := &auth_entities.TenantCreation{
			Id:        tenantCreationID,
			CreatedAt: timestamppb.Now(),
			Status:    auth_entities.TenantCreation_PENDING,
		}

		_, err := tenantCreationDAO.Create(ctx, tenantCreation)
		require.NoError(t, err)

		req := &authpb.GetCreateTenantForTeamStatusRequest{
			TenantCreationId: tenantCreationID,
		}

		resp, err := server.GetCreateTenantForTeamStatus(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.NotNil(t, resp.TenantCreation)
		require.Equal(t, tenantCreationID, resp.TenantCreation.Id)
		require.Equal(t, auth_entities.TenantCreation_PENDING, resp.TenantCreation.Status)
	})

	t.Run("NotFound", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		req := &authpb.GetCreateTenantForTeamStatusRequest{
			TenantCreationId: "non-existent-id",
		}

		_, err := server.GetCreateTenantForTeamStatus(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))
		require.Contains(t, err.Error(), "Tenant creation record not found")
	})
}

func TestInviteUsersToTenant(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_RW",
			true,  // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.InviteUsersToTenantRequest{
				TenantId:      selfServeTeamTenantID,
				InviteeEmails: []string{"<EMAIL>"},
			},
			server.InviteUsersToTenant,
		)
	})

	t.Run("basic", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>", "<EMAIL>"},
		}

		// Clear any existing messages
		mockPublisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		mockPublisher.ClearMessages()

		resp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, 2, len(resp.InvitationStatuses))
		emails := []string{resp.InvitationStatuses[0].GetEmail(), resp.InvitationStatuses[1].GetEmail()}
		assert.ElementsMatch(t, []string{"<EMAIL>", "<EMAIL>"}, emails)
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[0].GetStatus())
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[1].GetStatus())
		assert.NotEqual(t, resp.InvitationStatuses[0].GetInvitationId(), resp.InvitationStatuses[1].GetInvitationId())

		// Check that the SendInvitationEmail messages were published
		messages := mockPublisher.GetPublishedMessages()
		require.Equal(t, 2, len(messages), "Expected 2 invitation email messages to be published")

		// Verify the content of the messages
		emailMessages := make([]*auth_internal.SendInvitationEmailMessage, 0)
		for _, msg := range messages {
			emailMsg := msg.GetSendInvitationEmailMessage()
			require.NotNil(t, emailMsg, "Expected SendInvitationEmailMessage")
			emailMessages = append(emailMessages, emailMsg)
		}

		// Check that the invitation IDs match
		invitationIDs := []string{resp.InvitationStatuses[0].GetInvitationId(), resp.InvitationStatuses[1].GetInvitationId()}
		for _, emailMsg := range emailMessages {
			assert.Contains(t, invitationIDs, emailMsg.InvitationId, "Invitation ID in email message should match one from the response")
			assert.Equal(t, selfServeTeamTenantID, emailMsg.TenantId, "Tenant ID in email message should match request")
			assert.Contains(t, []string{"<EMAIL>", "<EMAIL>"}, emailMsg.InviteeEmail, "Email in message should match one from the request")
			assert.NotNil(t, emailMsg.PublishTime, "PublishTime should not be nil")
		}
	})

	t.Run("plus emails disallowed", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		_, err := server.InviteUsersToTenant(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
	})

	t.Run("duplicate invite", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		resp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)
		invitationID := resp.InvitationStatuses[0].GetInvitationId()

		// Invite the same user again.
		resp, err = server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)
		assert.True(t, proto.Equal(&authpb.InviteUsersToTenantResponse{
			InvitationStatuses: []*authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
				{
					Email:        "<EMAIL>",
					InvitationId: invitationID,
					Status:       authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS,
				},
			},
		}, resp))

		// Make sure we still only have one invite in the database.
		invitationDAO := server.daoFactory.GetTenantInvitationDAO(selfServeTeamTenantID)
		count := 0
		err = invitationDAO.FindAll(ctx, func(invitation *auth_entities.TenantInvitation) bool {
			count++
			return true
		})
		require.NoError(t, err)
		assert.Equal(t, 1, count)
	})

	t.Run("duplicate previously declined invite", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		invitationResp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, 1, len(invitationResp.InvitationStatuses))

		// Decline the invite.
		// TODO(jacqueline): Use an appropriate helper here once we have the invitation accept/decline
		// endpoints implemented.
		invitationDAO := server.daoFactory.GetTenantInvitationDAO(selfServeTeamTenantID)
		invitationID := invitationResp.InvitationStatuses[0].GetInvitationId()
		invitation, err := invitationDAO.Get(ctx, invitationID)
		require.NoError(t, err)
		invitation.Status = auth_entities.TenantInvitation_DECLINED
		_, err = invitationDAO.Update(ctx, invitation)
		require.NoError(t, err)

		// Invite the same users again.
		resp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, 1, len(resp.InvitationStatuses))
		assert.Equal(t, "<EMAIL>", resp.InvitationStatuses[0].GetEmail())
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[0].GetStatus())
		assert.NotEqual(t, invitationID, resp.InvitationStatuses[0].GetInvitationId())

		// Make sure we have two invites now.
		invitations := make([]*auth_entities.TenantInvitation, 0)
		err = invitationDAO.FindAll(ctx, func(invitation *auth_entities.TenantInvitation) bool {
			invitations = append(invitations, invitation)
			return true
		})
		require.NoError(t, err)
		assert.Len(t, invitations, 2)
	})

	t.Run("dupliate in same request", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>", "<EMAIL>"},
		}
		resp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, 1, len(resp.InvitationStatuses))
		assert.Equal(t, "<EMAIL>", resp.InvitationStatuses[0].GetEmail())
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[0].GetStatus())
		assert.NotEqual(t, "", resp.InvitationStatuses[0].GetInvitationId())
	})

	t.Run("email normalization, valid email", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{" <EMAIL> ", "<EMAIL>"},
		}
		resp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)

		assert.Equal(t, 1, len(resp.InvitationStatuses))
		assert.Equal(t, "<EMAIL>", resp.InvitationStatuses[0].GetEmail())
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[0].GetStatus())
		assert.NotEqual(t, "", resp.InvitationStatuses[0].GetInvitationId())
	})

	t.Run("email normalization, invalid email", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"invalid email"},
		}
		resp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)

		assert.Equal(t, 1, len(resp.InvitationStatuses))
		assert.Equal(t, "invalid email", resp.InvitationStatuses[0].GetEmail())
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR, resp.InvitationStatuses[0].GetStatus())
	})

	t.Run("email associated with enterprise tenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		setupSubscription(t, server, "", selfServeTeamTenantID, 10)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")

		// Try to invite a user with an email domain that matches the enterprise tenant
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"user@" + enterpriseDomain},
		}
		resp, err := server.InviteUsersToTenant(ctx, req)
		require.NoError(t, err)

		// Verify that the invitation failed with an error status
		assert.Equal(t, 1, len(resp.InvitationStatuses))
		assert.Equal(t, "user@"+enterpriseDomain, resp.InvitationStatuses[0].GetEmail())
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR, resp.InvitationStatuses[0].GetStatus())

		// Verify that no invitation was created in the database
		invitationDAO := server.daoFactory.GetTenantInvitationDAO(selfServeTeamTenantID)
		invitations := make([]*auth_entities.TenantInvitation, 0)
		err = invitationDAO.FindAll(ctx, func(invitation *auth_entities.TenantInvitation) bool {
			invitations = append(invitations, invitation)
			return true
		})
		require.NoError(t, err)
		assert.Len(t, invitations, 0)
	})

	t.Run("non-admin cannot invite users when not enough seats", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a user who is not an admin
		userDAO := server.daoFactory.GetUserDAO()
		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{selfServeTeamTenantID},
		}
		var err error
		_, err = userDAO.Create(context.Background(), user)
		require.NoError(t, err)

		// Add the user to the tenant
		userTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(selfServeTeamTenantID)
		userTenantMapping := &auth_entities.UserTenantMapping{
			UserId: authorizedUserID,
			Tenant: selfServeTeamTenantID,
		}
		_, err = userTenantMappingDAO.Create(context.Background(), userTenantMapping)
		require.NoError(t, err)

		// Create a subscription with 3 seats
		subscriptionID, _ := setupSubscription(t, server, "", selfServeTeamTenantID, 3)

		// Create a tenant subscription mapping
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		tenantSubscriptionMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             selfServeTeamTenantID,
			StripeSubscriptionId: subscriptionID,
			OrbSubscriptionId:    subscriptionID,
		}
		_, err = tenantSubscriptionMappingDAO.Create(context.Background(), tenantSubscriptionMapping)
		require.NoError(t, err)

		// Try to invite 2 users when only 1 seat is left (3 total - 1 admin - 1 non-admin)
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		inviteReq := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>", "<EMAIL>"},
		}
		_, err = server.InviteUsersToTenant(ctx, inviteReq)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Not enough seats available. You need 4 seats but only have 3. Please upgrade your subscription to add more team members.")

		// Try to invite just 1 user, which should work
		inviteReq = &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		resp, err := server.InviteUsersToTenant(ctx, inviteReq)
		require.NoError(t, err)
		assert.Len(t, resp.InvitationStatuses, 1)
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[0].Status)
	})

	t.Run("admin can invite users even when not enough seats", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		userDAO := server.daoFactory.GetUserDAO()
		userTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(selfServeTeamTenantID)

		// Use the admin user that was already created in newTestTeamManagementServer
		// Create a subscription with limited seats
		subscriptionID, _ := setupSubscription(t, server, "", selfServeTeamTenantID, 2) // Only 2 seats

		// Create a tenant subscription mapping
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		tenantSubscriptionMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             selfServeTeamTenantID,
			StripeSubscriptionId: subscriptionID,
			OrbSubscriptionId:    subscriptionID,
		}
		_, err := tenantSubscriptionMappingDAO.Create(context.Background(), tenantSubscriptionMapping)
		require.NoError(t, err)

		// Add another user to the tenant to take up one seat
		anotherUserID := "another-user"
		anotherUser := &auth_entities.User{
			Id:      anotherUserID,
			Email:   "<EMAIL>",
			Tenants: []string{selfServeTeamTenantID},
		}
		_, err = userDAO.Create(context.Background(), anotherUser)
		require.NoError(t, err)

		anotherUserTenantMapping := &auth_entities.UserTenantMapping{
			UserId: anotherUserID,
			Tenant: selfServeTeamTenantID,
		}
		_, err = userTenantMappingDAO.Create(context.Background(), anotherUserTenantMapping)
		require.NoError(t, err)

		// Admin tries to invite 2 users when only 1 seat is left (2 total - 1 used)
		// This should work because admins can exceed seat limits
		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		inviteReq := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>", "<EMAIL>"},
		}
		resp, err := server.InviteUsersToTenant(ctx, inviteReq)
		require.NoError(t, err)
		assert.Len(t, resp.InvitationStatuses, 2)
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[0].Status)
		assert.Equal(t, authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS, resp.InvitationStatuses[1].Status)
	})
}

func TestGetTenantInvitations(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_R",
			true,  // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.GetTenantInvitationsRequest{
				TenantId: selfServeTeamTenantID,
			},
			server.GetTenantInvitations,
		)
	})

	t.Run("no invitations", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.GetTenantInvitationsRequest{
			TenantId: selfServeTeamTenantID,
		}
		resp, err := server.GetTenantInvitations(ctx, req)
		require.NoError(t, err)
		assert.Empty(t, resp.Invitations)
	})

	t.Run("invitations", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.GetTenantInvitationsRequest{
			TenantId: selfServeTeamTenantID,
		}

		// Create subscriptions for the tenant
		setupSubscription(t, server, "", selfServeTeamTenantID, 5)
		// Invite some users.
		inviteReq := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>", "<EMAIL>"},
		}
		_, err := server.InviteUsersToTenant(ctx, inviteReq)
		require.NoError(t, err)

		// Get the invitations.
		resp, err := server.GetTenantInvitations(ctx, req)
		require.NoError(t, err)
		assert.Len(t, resp.Invitations, 2)

		// Change one invitation to accepted and make sure we don't return it anymore.
		invitationDAO := server.daoFactory.GetTenantInvitationDAO(selfServeTeamTenantID)
		invitation := resp.Invitations[0]
		invitation.Status = auth_entities.TenantInvitation_ACCEPTED
		_, err = invitationDAO.Update(ctx, invitation)
		require.NoError(t, err)

		resp, err = server.GetTenantInvitations(ctx, req)
		require.NoError(t, err)
		assert.Len(t, resp.Invitations, 1)
	})
}

// setupSubscription creates a test subscription and configures the mock Stripe client
func setupSubscription(t *testing.T, server *TeamManagementServer, userID, tenantID string, seats int32) (string, *auth_entities.Subscription) {
	// Create a test subscription
	subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
	subscriptionID := uuid.New().String()

	// Set the owner based on which ID is provided
	var subscription *auth_entities.Subscription
	if userID != "" {
		subscription = &auth_entities.Subscription{
			SubscriptionId:       subscriptionID,
			StripeCustomerId:     "cus_test",
			PriceId:              "price_test",
			Status:               auth_entities.Subscription_ACTIVE,
			Owner:                &auth_entities.Subscription_UserId{UserId: userID},
			Seats:                seats,
			CreatedAt:            timestamppb.Now(),
			UpdatedAt:            timestamppb.Now(),
			ExternalPlanId:       orbProfessionalPlanID,
			SubscriptionChangeId: nil,
		}
	} else if tenantID != "" {
		subscription = &auth_entities.Subscription{
			SubscriptionId:       subscriptionID,
			StripeCustomerId:     "cus_test",
			PriceId:              "price_test",
			Status:               auth_entities.Subscription_ACTIVE,
			Owner:                &auth_entities.Subscription_TenantId{TenantId: tenantID},
			Seats:                seats,
			CreatedAt:            timestamppb.Now(),
			UpdatedAt:            timestamppb.Now(),
			ExternalPlanId:       orbProfessionalPlanID,
			SubscriptionChangeId: nil,
		}
	} else {
		t.Fatal("Either userID or tenantID must be provided")
		return "", nil // This line will never be reached, but it satisfies the compiler
	}

	createdSubscription, err := subscriptionDAO.Create(context.Background(), subscription)
	require.NoError(t, err)

	// Set up the mock Stripe client
	mockStripeClient := NewMockStripeClient()
	mockStripeClient.AddSubscription("cus_test", userID, subscriptionID, "price_test")
	server.stripeClient = mockStripeClient

	// If this is a tenant subscription, create the mapping
	if tenantID != "" {
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             tenantID,
			StripeSubscriptionId: subscriptionID,
			OrbSubscriptionId:    subscriptionID,
			StripeCustomerId:     "cus_test",
			CreatedAt:            timestamppb.Now(),
		}
		_, mappingErr := tenantSubscriptionMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, mappingErr)
	}

	return subscriptionID, createdSubscription
}

func TestGetSubscription(t *testing.T) {
	t.Run("auth test - by subscription ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a test subscription with a user owner
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		subscriptionID := uuid.New().String()
		subscription := &auth_entities.Subscription{
			SubscriptionId:   subscriptionID,
			StripeCustomerId: "cus_test",
			PriceId:          "price_test",
			Status:           auth_entities.Subscription_ACTIVE,
			Owner:            &auth_entities.Subscription_UserId{UserId: authorizedUserID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)

		runAuthTests(
			t,
			"AUTH_R",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly
			&authpb.GetSubscriptionRequest{
				LookupId: &authpb.GetSubscriptionRequest_SubscriptionId{SubscriptionId: subscriptionID},
			},
			server.GetSubscription,
		)
	})

	t.Run("auth test - by tenant ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_R",
			true,  // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.GetSubscriptionRequest{
				LookupId: &authpb.GetSubscriptionRequest_TenantId{TenantId: selfServeTeamTenantID},
			},
			server.GetSubscription,
		)
	})

	t.Run("by subscription ID - user owner", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a test subscription with a user owner
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		subscriptionID := uuid.New().String()
		subscription := &auth_entities.Subscription{
			SubscriptionId:   subscriptionID,
			StripeCustomerId: "cus_test",
			PriceId:          "price_test",
			Status:           auth_entities.Subscription_ACTIVE,
			Owner:            &auth_entities.Subscription_UserId{UserId: authorizedUserID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)

		// Get the subscription by subscription ID
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{
			LookupId: &authpb.GetSubscriptionRequest_SubscriptionId{SubscriptionId: subscriptionID},
		}
		resp, err := server.GetSubscription(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.NotNil(t, resp.Subscription)
		assert.Equal(t, subscriptionID, resp.Subscription.SubscriptionId)
		assert.Equal(t, "cus_test", resp.Subscription.StripeCustomerId)
		assert.Equal(t, "price_test", resp.Subscription.PriceId)
		assert.Equal(t, auth_entities.Subscription_ACTIVE, resp.Subscription.Status)
		assert.Equal(t, authorizedUserID, resp.Subscription.GetUserId())
	})

	t.Run("by subscription ID - tenant owner", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a test subscription with a tenant owner
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		subscriptionID := uuid.New().String()
		subscription := &auth_entities.Subscription{
			SubscriptionId:   subscriptionID,
			StripeCustomerId: "cus_test",
			PriceId:          "price_test",
			Status:           auth_entities.Subscription_ACTIVE,
			Owner:            &auth_entities.Subscription_TenantId{TenantId: selfServeTeamTenantID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)

		// Create a tenant subscription mapping
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             selfServeTeamTenantID,
			StripeSubscriptionId: subscriptionID,
			OrbSubscriptionId:    subscriptionID,
			StripeCustomerId:     "cus_test",
			CreatedAt:            timestamppb.Now(),
		}
		_, err = tenantSubscriptionMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		// Get the subscription by subscription ID
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{
			LookupId: &authpb.GetSubscriptionRequest_SubscriptionId{SubscriptionId: subscriptionID},
		}
		resp, err := server.GetSubscription(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.NotNil(t, resp.Subscription)
		assert.Equal(t, subscriptionID, resp.Subscription.SubscriptionId)
		assert.Equal(t, "cus_test", resp.Subscription.StripeCustomerId)
		assert.Equal(t, "price_test", resp.Subscription.PriceId)
		assert.Equal(t, auth_entities.Subscription_ACTIVE, resp.Subscription.Status)
		assert.Equal(t, selfServeTeamTenantID, resp.Subscription.GetTenantId())
	})

	t.Run("by tenant ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a test subscription with a tenant owner
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		subscriptionID := uuid.New().String()
		subscription := &auth_entities.Subscription{
			SubscriptionId:   subscriptionID,
			StripeCustomerId: "cus_test",
			PriceId:          "price_test",
			Status:           auth_entities.Subscription_ACTIVE,
			Owner:            &auth_entities.Subscription_TenantId{TenantId: selfServeTeamTenantID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)

		// Create a tenant subscription mapping
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             selfServeTeamTenantID,
			StripeSubscriptionId: subscriptionID,
			OrbSubscriptionId:    subscriptionID,
			StripeCustomerId:     "cus_test",
			CreatedAt:            timestamppb.Now(),
		}
		_, err = tenantSubscriptionMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)

		// Get the subscription by tenant ID
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{
			LookupId: &authpb.GetSubscriptionRequest_TenantId{TenantId: selfServeTeamTenantID},
		}
		resp, err := server.GetSubscription(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.NotNil(t, resp.Subscription)
		assert.Equal(t, subscriptionID, resp.Subscription.SubscriptionId)
		assert.Equal(t, "cus_test", resp.Subscription.StripeCustomerId)
		assert.Equal(t, "price_test", resp.Subscription.PriceId)
		assert.Equal(t, auth_entities.Subscription_ACTIVE, resp.Subscription.Status)
		assert.Equal(t, selfServeTeamTenantID, resp.Subscription.GetTenantId())
	})

	t.Run("subscription not found", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Try to get a non-existent subscription
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{
			LookupId: &authpb.GetSubscriptionRequest_SubscriptionId{SubscriptionId: "non-existent-id"},
		}
		_, err := server.GetSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Subscription not found or you don't have permission to access it")
	})

	t.Run("TenantSubscriptionMapping not found", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Try to get a subscription for a tenant that doesn't have one
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{
			LookupId: &authpb.GetSubscriptionRequest_TenantId{TenantId: selfServeTeamTenantID},
		}
		_, err := server.GetSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.NotFound, status.Code(err))
		assert.Contains(t, err.Error(), "Subscription not found for tenant")
	})

	t.Run("no lookup ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Try to get a subscription without providing a lookup ID
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{}
		_, err := server.GetSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Either subscription_id or tenant_id must be provided")
	})

	t.Run("invalid user for subscription ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a test subscription with a different user owner
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		subscriptionID := uuid.New().String()
		subscription := &auth_entities.Subscription{
			SubscriptionId:   subscriptionID,
			StripeCustomerId: "cus_test",
			PriceId:          "price_test",
			Status:           auth_entities.Subscription_ACTIVE,
			Owner:            &auth_entities.Subscription_UserId{UserId: "different-user-id"},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)

		// Try to get the subscription as a different user
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{
			LookupId: &authpb.GetSubscriptionRequest_SubscriptionId{SubscriptionId: subscriptionID},
		}
		_, err = server.GetSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.PermissionDenied, status.Code(err))
		assert.Contains(t, err.Error(), "Access denied")
	})

	t.Run("invalid tenant for subscription ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a test subscription with a different tenant owner
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		subscriptionID := uuid.New().String()
		subscription := &auth_entities.Subscription{
			SubscriptionId:   subscriptionID,
			StripeCustomerId: "cus_test",
			PriceId:          "price_test",
			Status:           auth_entities.Subscription_ACTIVE,
			Owner:            &auth_entities.Subscription_TenantId{TenantId: "different-tenant-id"},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)

		// Try to get the subscription as a different tenant
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetSubscriptionRequest{
			LookupId: &authpb.GetSubscriptionRequest_SubscriptionId{SubscriptionId: subscriptionID},
		}
		_, err = server.GetSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.PermissionDenied, status.Code(err))
		assert.Contains(t, err.Error(), "Access denied")
	})
}

func (d *SubscriptionDAO) FindAllSubscriptions_TEST_ONLY(ctx context.Context) ([]*auth_entities.Subscription, error) {
	var subscriptions []*auth_entities.Subscription
	err := d.findAll(ctx, func(subscription *auth_entities.Subscription) bool {
		subscriptions = append(subscriptions, subscription)
		return true
	})
	if err != nil {
		return nil, err
	}
	return subscriptions, nil
}

func TestListSubscriptions(t *testing.T) {
	t.Run("auth test", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// ListSubscriptions requires wildcard tenant access, so we use a simplified auth test
		runWildcardAuthTests(
			t,
			"AUTH_R",
			&authpb.ListSubscriptionsRequest{},
			server.ListSubscriptions,
		)
	})

	t.Run("empty results", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Clear any existing subscriptions
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")
		existingSubs, err := subscriptionDAO.FindAllSubscriptions_TEST_ONLY(ctx)
		require.NoError(t, err)
		for _, sub := range existingSubs {
			err = subscriptionDAO.Delete(ctx, sub.SubscriptionId)
			require.NoError(t, err)
		}

		// Verify empty response
		resp, err := server.ListSubscriptions(ctx, &authpb.ListSubscriptionsRequest{})
		require.NoError(t, err)
		assert.Empty(t, resp.Subscriptions)
		assert.Empty(t, resp.NextPageToken)
	})

	t.Run("default page size", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create 20 test subscriptions
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Clear any existing subscriptions
		existingSubs, err := subscriptionDAO.FindAllSubscriptions_TEST_ONLY(ctx)
		require.NoError(t, err)
		for _, sub := range existingSubs {
			err = subscriptionDAO.Delete(ctx, sub.SubscriptionId)
			require.NoError(t, err)
		}

		for i := 1; i <= 20; i++ {
			sub := createTestSubscription(map[string]interface{}{
				"SubscriptionId":   fmt.Sprintf("sub-default-%d", i),
				"StripeCustomerId": fmt.Sprintf("cus-default-%d", i),
				"Status":           auth_entities.Subscription_ACTIVE,
				"Owner":            &auth_entities.Subscription_TenantId{TenantId: selfServeTeamTenantID},
			})
			_, err := subscriptionDAO.Create(ctx, sub)
			require.NoError(t, err)
		}

		// Request without specifying page size should use default
		resp, err := server.ListSubscriptions(ctx, &authpb.ListSubscriptionsRequest{})
		require.NoError(t, err)

		// Should return some results and have a next page token if default page size < 20
		assert.NotEmpty(t, resp.Subscriptions)
		if len(resp.Subscriptions) < 20 {
			assert.NotEmpty(t, resp.NextPageToken, "Should have next page token when not all results fit")
		} else {
			assert.Empty(t, resp.NextPageToken, "Should not have next page token when all results fit")
		}
	})

	t.Run("pagination - complete retrieval", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create test subscriptions
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Clear any existing subscriptions
		existingSubs, err := subscriptionDAO.FindAllSubscriptions_TEST_ONLY(ctx)
		require.NoError(t, err)
		for _, sub := range existingSubs {
			err = subscriptionDAO.Delete(ctx, sub.SubscriptionId)
			require.NoError(t, err)
		}

		// Create 10 subscriptions with predictable IDs
		expectedIDs := make([]string, 10)
		for i := 1; i <= 10; i++ {
			subID := fmt.Sprintf("sub-complete-%d", i)
			expectedIDs[i-1] = subID
			sub := createTestSubscription(map[string]interface{}{
				"SubscriptionId":   subID,
				"StripeCustomerId": fmt.Sprintf("cus-complete-%d", i),
				"Status":           auth_entities.Subscription_ACTIVE,
				"Owner":            &auth_entities.Subscription_TenantId{TenantId: selfServeTeamTenantID},
			})
			_, err := subscriptionDAO.Create(ctx, sub)
			require.NoError(t, err)
		}

		// Use small page size to ensure pagination
		pageSize := uint32(3)
		allSubIDs := make(map[string]bool)
		pageToken := ""
		pageCount := 0

		// Collect all subscriptions using pagination
		for {
			pageCount++
			resp, err := server.ListSubscriptions(ctx, &authpb.ListSubscriptionsRequest{
				PageSize:  pageSize,
				PageToken: pageToken,
			})
			require.NoError(t, err)

			// Verify page size constraints
			if resp.NextPageToken != "" {
				assert.Len(t, resp.Subscriptions, int(pageSize), "Non-final page should have exactly pageSize items")
			} else {
				assert.LessOrEqual(t, len(resp.Subscriptions), int(pageSize), "Final page should have <= pageSize items")
			}

			// Add subscription IDs to our set
			for _, sub := range resp.Subscriptions {
				allSubIDs[sub.SubscriptionId] = true
			}

			if resp.NextPageToken == "" {
				break
			}
			pageToken = resp.NextPageToken
		}

		// Verify we got multiple pages
		assert.Equal(t, 4, pageCount, "Should have exactly 4 pages with page size 3")

		// Verify we got all 10 subscriptions without duplicates
		assert.Len(t, allSubIDs, 10, "Should have found all 10 subscriptions")

		// Verify all subscription IDs are present
		for _, id := range expectedIDs {
			assert.True(t, allSubIDs[id], fmt.Sprintf("Subscription %s should be present", id))
		}
	})

	t.Run("pagination - exact boundary", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create test subscriptions
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Clear any existing subscriptions
		existingSubs, err := subscriptionDAO.FindAllSubscriptions_TEST_ONLY(ctx)
		require.NoError(t, err)
		for _, sub := range existingSubs {
			err = subscriptionDAO.Delete(ctx, sub.SubscriptionId)
			require.NoError(t, err)
		}

		// Create exactly 5 subscriptions
		for i := 1; i <= 5; i++ {
			sub := createTestSubscription(map[string]interface{}{
				"SubscriptionId":   fmt.Sprintf("sub-boundary-%d", i),
				"StripeCustomerId": fmt.Sprintf("cus-boundary-%d", i),
				"Status":           auth_entities.Subscription_ACTIVE,
				"Owner":            &auth_entities.Subscription_TenantId{TenantId: selfServeTeamTenantID},
			})
			_, err := subscriptionDAO.Create(ctx, sub)
			require.NoError(t, err)
		}

		// Request with page size exactly matching number of items
		resp, err := server.ListSubscriptions(ctx, &authpb.ListSubscriptionsRequest{
			PageSize: 5,
		})
		require.NoError(t, err)
		assert.Len(t, resp.Subscriptions, 5, "Should return exactly 5 subscriptions")
		assert.Empty(t, resp.NextPageToken, "Should not have next page token when all items fit exactly")
	})

	t.Run("pagination - zero page size", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Create a test subscription
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		sub := createTestSubscription(map[string]interface{}{
			"SubscriptionId":   "sub-zero-page",
			"StripeCustomerId": "cus-zero-page",
			"Status":           auth_entities.Subscription_ACTIVE,
			"Owner":            &auth_entities.Subscription_TenantId{TenantId: selfServeTeamTenantID},
		})
		_, err := subscriptionDAO.Create(ctx, sub)
		require.NoError(t, err)

		// Test with zero page size (should use default)
		resp, err := server.ListSubscriptions(ctx, &authpb.ListSubscriptionsRequest{
			PageSize: 0,
		})
		require.NoError(t, err)
		assert.NotNil(t, resp.Subscriptions, "Should return subscriptions")
		assert.Len(t, resp.Subscriptions, 1, "Should return the one subscription we created")
		assert.Equal(t, "sub-zero-page", resp.Subscriptions[0].SubscriptionId)
	})

	t.Run("subscription types", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create test subscriptions with different owners and statuses
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Clear any existing subscriptions
		existingSubs, err := subscriptionDAO.FindAllSubscriptions_TEST_ONLY(ctx)
		require.NoError(t, err)
		for _, sub := range existingSubs {
			err = subscriptionDAO.Delete(ctx, sub.SubscriptionId)
			require.NoError(t, err)
		}

		// Create tenant-owned subscription
		tenantSub := createTestSubscription(map[string]interface{}{
			"SubscriptionId":   "sub-tenant-owned",
			"StripeCustomerId": "cus-tenant-owned",
			"Status":           auth_entities.Subscription_ACTIVE,
			"Owner":            &auth_entities.Subscription_TenantId{TenantId: selfServeTeamTenantID},
		})
		_, err = subscriptionDAO.Create(ctx, tenantSub)
		require.NoError(t, err)

		// Create user-owned subscription
		userSub := createTestSubscription(map[string]interface{}{
			"SubscriptionId":   "sub-user-owned",
			"StripeCustomerId": "cus-user-owned",
			"Status":           auth_entities.Subscription_TRIALING,
			"Owner":            &auth_entities.Subscription_UserId{UserId: authorizedUserID},
		})
		_, err = subscriptionDAO.Create(ctx, userSub)
		require.NoError(t, err)

		// Create canceled subscription
		canceledSub := createTestSubscription(map[string]interface{}{
			"SubscriptionId":   "sub-canceled",
			"StripeCustomerId": "cus-canceled",
			"Status":           auth_entities.Subscription_CANCELED,
			"Owner":            &auth_entities.Subscription_TenantId{TenantId: "another-tenant"},
		})
		_, err = subscriptionDAO.Create(ctx, canceledSub)
		require.NoError(t, err)

		// Verify all subscription types are returned
		resp, err := server.ListSubscriptions(ctx, &authpb.ListSubscriptionsRequest{
			PageSize: 10,
		})
		require.NoError(t, err)
		assert.Len(t, resp.Subscriptions, 3, "Should return all 3 subscriptions")

		// Verify each subscription type is present
		foundTenant := false
		foundUser := false
		foundCanceled := false

		for _, sub := range resp.Subscriptions {
			if sub.SubscriptionId == "sub-tenant-owned" {
				foundTenant = true
				assert.Equal(t, selfServeTeamTenantID, sub.GetTenantId())
				assert.Equal(t, auth_entities.Subscription_ACTIVE, sub.Status)
			} else if sub.SubscriptionId == "sub-user-owned" {
				foundUser = true
				assert.Equal(t, authorizedUserID, sub.GetUserId())
				assert.Equal(t, auth_entities.Subscription_TRIALING, sub.Status)
			} else if sub.SubscriptionId == "sub-canceled" {
				foundCanceled = true
				assert.Equal(t, "another-tenant", sub.GetTenantId())
				assert.Equal(t, auth_entities.Subscription_CANCELED, sub.Status)
			}
		}

		assert.True(t, foundTenant, "Tenant-owned subscription should be present")
		assert.True(t, foundUser, "User-owned subscription should be present")
		assert.True(t, foundCanceled, "Canceled subscription should be present")
	})
}

func TestListTenantSubscriptionMappings(t *testing.T) {
	t.Run("auth test", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// ListTenantSubscriptionMappings requires wildcard tenant access
		runWildcardAuthTests(
			t,
			"AUTH_R",
			&authpb.ListTenantSubscriptionMappingsRequest{},
			server.ListTenantSubscriptionMappings,
		)
	})

	t.Run("empty results", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// When the table is empty or has no matching records, we should still get a valid response
		resp, err := server.ListTenantSubscriptionMappings(ctx, &authpb.ListTenantSubscriptionMappingsRequest{})
		require.NoError(t, err)
		require.NotNil(t, resp)
		// The response might have nil or empty slice when there are no results
		// Both are valid according to protobuf semantics
		if resp.TenantSubscriptionMappings != nil {
			assert.Empty(t, resp.TenantSubscriptionMappings)
		}
		assert.Empty(t, resp.NextPageToken)
	})

	t.Run("default page size", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create test mappings
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Create 20 test mappings
		for i := 1; i <= 20; i++ {
			mapping := &auth_entities.TenantSubscriptionMapping{
				TenantId:             fmt.Sprintf("test-tenant-%d", i),
				StripeSubscriptionId: fmt.Sprintf("sub_%d", i),
				StripeCustomerId:     fmt.Sprintf("cus_%d", i),
				CreatedAt:            timestamppb.Now(),
			}
			_, err := tenantSubscriptionMappingDAO.Create(ctx, mapping)
			require.NoError(t, err)
		}

		// Request without specifying page size should use default
		resp, err := server.ListTenantSubscriptionMappings(ctx, &authpb.ListTenantSubscriptionMappingsRequest{})
		require.NoError(t, err)

		// Should return some results
		assert.NotEmpty(t, resp.TenantSubscriptionMappings)
	})

	t.Run("pagination - complete retrieval", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create test mappings
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Create 10 mappings with predictable IDs
		expectedTenantIDs := make([]string, 10)
		for i := 1; i <= 10; i++ {
			tenantID := fmt.Sprintf("paginated-tenant-%d", i)
			expectedTenantIDs[i-1] = tenantID
			mapping := &auth_entities.TenantSubscriptionMapping{
				TenantId:             tenantID,
				StripeSubscriptionId: fmt.Sprintf("sub_paginated_%d", i),
				StripeCustomerId:     fmt.Sprintf("cus_paginated_%d", i),
				OrbCustomerId:        fmt.Sprintf("orb_paginated_%d", i),
				OrbSubscriptionId:    fmt.Sprintf("orb_sub_paginated_%d", i),
				BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
				CreatedAt:            timestamppb.Now(),
			}
			_, err := tenantSubscriptionMappingDAO.Create(ctx, mapping)
			require.NoError(t, err)
		}

		// Use small page size to ensure pagination
		pageSize := uint32(3)
		allTenantIDs := make(map[string]bool)
		pageToken := ""
		pageCount := 0

		// Collect all mappings using pagination
		for {
			pageCount++
			resp, err := server.ListTenantSubscriptionMappings(ctx, &authpb.ListTenantSubscriptionMappingsRequest{
				PageSize:  pageSize,
				PageToken: pageToken,
			})
			require.NoError(t, err)

			// Verify page size constraints
			if resp.NextPageToken != "" {
				assert.LessOrEqual(t, len(resp.TenantSubscriptionMappings), int(pageSize), "Page should have <= pageSize items")
			}

			// Add tenant IDs to our set
			for _, mapping := range resp.TenantSubscriptionMappings {
				allTenantIDs[mapping.TenantId] = true
			}

			if resp.NextPageToken == "" {
				break
			}
			pageToken = resp.NextPageToken
		}

		// Verify we got multiple pages
		assert.Greater(t, pageCount, 1, "Should have multiple pages with page size 3")

		// Verify all expected tenant IDs are present
		for _, id := range expectedTenantIDs {
			assert.True(t, allTenantIDs[id], fmt.Sprintf("Tenant %s should be present", id))
		}
	})

	t.Run("pagination - zero page size", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Create a test mapping
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             "zero-page-tenant",
			StripeSubscriptionId: "sub_zero_page",
			StripeCustomerId:     "cus_zero_page",
			CreatedAt:            timestamppb.Now(),
		}
		_, err := tenantSubscriptionMappingDAO.Create(ctx, mapping)
		require.NoError(t, err)

		// Test with zero page size (should use default)
		resp, err := server.ListTenantSubscriptionMappings(ctx, &authpb.ListTenantSubscriptionMappingsRequest{
			PageSize: 0,
		})
		require.NoError(t, err)
		assert.NotNil(t, resp.TenantSubscriptionMappings, "Should return mappings")

		// Verify our mapping is in the results
		found := false
		for _, m := range resp.TenantSubscriptionMappings {
			if m.TenantId == "zero-page-tenant" {
				found = true
				assert.Equal(t, "sub_zero_page", m.StripeSubscriptionId)
				break
			}
		}
		assert.True(t, found, "Should find the created mapping")
	})

	t.Run("mapping types", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create test mappings with different configurations
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		ctx := authorizedContext(auth.TenantIDWildcard, authorizedUserID, "AUTH_R")

		// Create Stripe-only mapping
		stripeMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             "stripe-only-tenant",
			StripeSubscriptionId: "sub_stripe_only",
			StripeCustomerId:     "cus_stripe_only",
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
			CreatedAt:            timestamppb.Now(),
		}
		_, err := tenantSubscriptionMappingDAO.Create(ctx, stripeMapping)
		require.NoError(t, err)

		// Create Orb mapping
		orbMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             "orb-tenant",
			StripeSubscriptionId: "sub_orb",
			StripeCustomerId:     "cus_orb",
			OrbCustomerId:        "orb_customer_123",
			OrbSubscriptionId:    "orb_sub_123",
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
			CreatedAt:            timestamppb.Now(),
		}
		_, err = tenantSubscriptionMappingDAO.Create(ctx, orbMapping)
		require.NoError(t, err)

		// Create mapping with plan change in progress
		planChangeMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:             "plan-change-tenant",
			StripeSubscriptionId: "sub_plan_change",
			StripeCustomerId:     "cus_plan_change",
			OrbCustomerId:        "orb_plan_change",
			OrbSubscriptionId:    "orb_sub_plan_change",
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
			PlanChange: &auth_entities.TenantSubscriptionMapping_PlanChangeInfo{
				Id:              "plan-change-123",
				TargetOrbPlanId: "orb_professional_plan",
				CreatedAt:       timestamppb.Now(),
				UpdatedAt:       timestamppb.Now(),
			},
			CreatedAt: timestamppb.Now(),
		}
		_, err = tenantSubscriptionMappingDAO.Create(ctx, planChangeMapping)
		require.NoError(t, err)

		// Verify all mapping types are returned
		resp, err := server.ListTenantSubscriptionMappings(ctx, &authpb.ListTenantSubscriptionMappingsRequest{
			PageSize: 50,
		})
		require.NoError(t, err)

		// Verify each mapping type is present
		foundStripe := false
		foundOrb := false
		foundPlanChange := false

		for _, mapping := range resp.TenantSubscriptionMappings {
			switch mapping.TenantId {
			case "stripe-only-tenant":
				foundStripe = true
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_STRIPE, mapping.BillingMethod)
				assert.Empty(t, mapping.OrbCustomerId)
				assert.Empty(t, mapping.OrbSubscriptionId)
			case "orb-tenant":
				foundOrb = true
				assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, mapping.BillingMethod)
				assert.Equal(t, "orb_customer_123", mapping.OrbCustomerId)
				assert.Equal(t, "orb_sub_123", mapping.OrbSubscriptionId)
			case "plan-change-tenant":
				foundPlanChange = true
				assert.NotNil(t, mapping.PlanChange)
				assert.Equal(t, "plan-change-123", mapping.PlanChange.Id)
				assert.Equal(t, "orb_professional_plan", mapping.PlanChange.TargetOrbPlanId)
			}
		}

		assert.True(t, foundStripe, "Stripe-only mapping should be present")
		assert.True(t, foundOrb, "Orb mapping should be present")
		assert.True(t, foundPlanChange, "Plan change mapping should be present")
	})
}

func TestDeleteInvitation(t *testing.T) {
	t.Run("auth test", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_RW",
			true,  // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.DeleteInvitationRequest{
				TenantId:     selfServeTeamTenantID,
				InvitationId: "invitation-id",
			},
			server.DeleteInvitation,
		)
	})

	t.Run("delete invitation", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a test invitation
		invitationDAO := server.daoFactory.GetTenantInvitationDAO(selfServeTeamTenantID)
		invitationID := uuid.New().String()
		invitation := &auth_entities.TenantInvitation{
			Id:           invitationID,
			TenantId:     selfServeTeamTenantID,
			InviteeEmail: "<EMAIL>",
			Status:       auth_entities.TenantInvitation_PENDING,
			CreatedAt:    timestamppb.Now(),
		}
		_, err := invitationDAO.Create(context.Background(), invitation)
		require.NoError(t, err)

		// Delete the invitation
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.DeleteInvitationRequest{
			TenantId:     selfServeTeamTenantID,
			InvitationId: invitationID,
		}
		_, err = server.DeleteInvitation(ctx, req)
		require.NoError(t, err)

		// Verify the invitation was deleted
		invitation, err = invitationDAO.Get(ctx, invitationID)
		require.NoError(t, err)
		assert.Nil(t, invitation)
	})

	t.Run("invitation not found", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Try to delete a non-existent invitation
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.DeleteInvitationRequest{
			TenantId:     selfServeTeamTenantID,
			InvitationId: "non-existent-id",
		}
		_, err := server.DeleteInvitation(ctx, req)
		require.NoError(t, err)
	})
}

func TestUpdateSubscription(t *testing.T) {
	t.Run("auth test", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		server.orbClient = nil
		defer cleanup()

		// Set up the test subscription with a tenant owner
		subscriptionID, _ := setupSubscription(t, server, "", selfServeTeamTenantID, 5)

		runAuthTests(
			t,
			"AUTH_RW",
			true,  // checkTenant
			false, // checkUser
			true,  // adminOnly
			&authpb.UpdateSubscriptionRequest{
				SubscriptionId: subscriptionID,
				Seats:          10,
			},
			server.UpdateSubscription,
		)
	})

	t.Run("update tenant subscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		server.orbClient = nil
		defer cleanup()

		// Set up the test subscription with a tenant owner
		subscriptionID, _ := setupSubscription(t, server, "", selfServeTeamTenantID, 5)

		// Update the subscription
		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: subscriptionID,
			Seats:          10,
		}
		resp, err := server.UpdateSubscription(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
	})

	t.Run("update user subscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Set up the test subscription with a user owner
		subscriptionID, _ := setupSubscription(t, server, authorizedUserID, "", 1)

		// Update the subscription
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: subscriptionID,
			Seats:          2,
		}

		// Attempt to update the subscription - should fail with Internal error
		resp, err := server.UpdateSubscription(ctx, req)

		// Verify that we get an error
		require.Error(t, err, "Expected error when updating user subscription")

		// Verify it's the correct error type (Internal)
		require.Equal(t, codes.Internal, status.Code(err), "Expected Internal error")

		// Verify the error message
		require.Contains(t, err.Error(), "Individual subscriptions cannot be updated", "Error should indicate user subscription updates are not supported")

		// Response should be nil
		require.Nil(t, resp, "Response should be nil when operation fails")
	})

	t.Run("no subscription ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Try to update a subscription without providing a subscription ID
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			Seats: 10,
		}
		_, err := server.UpdateSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Subscription ID is required")
	})

	t.Run("insufficient seats for team members and invitations", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		server.orbClient = nil
		defer cleanup()

		// Set up the test subscription with a tenant owner and 5 seats
		tenantID := selfServeTeamTenantID
		subscriptionID, _ := setupSubscription(t, server, "", tenantID, 5)

		// Create 3 team members in the tenant, including the admin user
		userTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(tenantID)

		// First, add the admin user with admin role
		adminMapping := &auth_entities.UserTenantMapping{
			Tenant:          tenantID,
			UserId:          adminUserID,
			CustomerUiRoles: []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN},
		}
		_, err := userTenantMappingDAO.Create(context.Background(), adminMapping)
		require.NoError(t, err)

		// Then add 2 more regular users
		for i := 0; i < 2; i++ {
			userID := fmt.Sprintf("user-%d", i)
			mapping := &auth_entities.UserTenantMapping{
				Tenant: tenantID,
				UserId: userID,
			}
			_, err := userTenantMappingDAO.Create(context.Background(), mapping)
			require.NoError(t, err)
		}

		// Create 2 pending invitations for the tenant
		invitationDAO := server.daoFactory.GetTenantInvitationDAO(tenantID)
		for i := 0; i < 2; i++ {
			invitationID := uuid.New().String()
			invitation := &auth_entities.TenantInvitation{
				Id:           invitationID,
				TenantId:     tenantID,
				InviteeEmail: fmt.Sprintf("<EMAIL>", i),
				Status:       auth_entities.TenantInvitation_PENDING,
				CreatedAt:    timestamppb.Now(),
			}
			_, err := invitationDAO.Create(context.Background(), invitation)
			require.NoError(t, err)
		}

		// Try to update the subscription to 4 seats (which is less than 3 team members + 2 invitations = 5)
		ctx := authorizedContext(tenantID, adminUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: subscriptionID,
			Seats:          4,
		}
		_, updateErr := server.UpdateSubscription(ctx, req)
		require.Error(t, updateErr)
		assert.Equal(t, codes.InvalidArgument, status.Code(updateErr))
		assert.Contains(t, updateErr.Error(), "Not enough seats available. You need 5 seats but only have 4. Please upgrade your subscription to add more team members.")

		// Now try with exactly 5 seats (which should work)
		req.Seats = 5
		resp, err := server.UpdateSubscription(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)

		// Clear the subscription processing field from the subscription DAO
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		_, err = subscriptionDAO.TryUpdate(context.Background(), subscriptionID, func(sub *auth_entities.Subscription) bool {
			sub.SubscriptionChangeId = nil
			return true
		}, DefaultRetry)
		require.NoError(t, err)

		// And try with more than 5 seats (which should also work)
		req.Seats = 6
		resp, err = server.UpdateSubscription(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
	})

	t.Run("already a change in progress", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		server.orbClient = nil
		defer cleanup()

		// Set up the test subscription with a tenant owner
		subscriptionID, _ := setupSubscription(t, server, "", selfServeTeamTenantID, 5)

		// Update the subscription
		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: subscriptionID,
			Seats:          10,
		}
		_, err := server.UpdateSubscription(ctx, req)
		require.NoError(t, err)

		// Try to update the subscription again (should fail)
		_, err = server.UpdateSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.FailedPrecondition, status.Code(err))
		assert.Contains(t, err.Error(), "Subscription change already in progress")
	})

	t.Run("invalid seats", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Try to update a subscription with invalid seats
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: "any-id",
			Seats:          0,
		}
		_, err := server.UpdateSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Seats must be greater than 0")

		subscriptionID, _ := setupSubscription(t, server, "", "different-tenant-id", 5)
		req = &authpb.UpdateSubscriptionRequest{
			SubscriptionId: subscriptionID,
			Seats:          101,
		}
		_, err = server.UpdateSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Seats must be less than 100")
	})

	t.Run("subscription not found", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		server.orbClient = nil
		defer cleanup()

		// Try to update a non-existent subscription
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: "non-existent-id",
			Seats:          10,
		}
		_, err := server.UpdateSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.NotFound, status.Code(err))
		assert.Contains(t, err.Error(), "Subscription not found")
	})

	t.Run("Subscription with user owner", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Set up the test subscription with a different user owner
		subscriptionID, _ := setupSubscription(t, server, "different-user-id", "", 5)

		// Try to update the subscription as a different user
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: subscriptionID,
			Seats:          10,
		}
		_, updateErr := server.UpdateSubscription(ctx, req)
		require.Error(t, updateErr)
		assert.Equal(t, codes.Internal, status.Code(updateErr))
		assert.Contains(t, updateErr.Error(), "Individual subscriptions cannot be updated")
	})

	t.Run("invalid tenant for subscription ID", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Set up the test subscription with a different tenant owner
		subscriptionID, _ := setupSubscription(t, server, "", "different-tenant-id", 5)

		// Try to update the subscription as a different tenant
		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := &authpb.UpdateSubscriptionRequest{
			SubscriptionId: subscriptionID,
			Seats:          10,
		}
		_, updateErr := server.UpdateSubscription(ctx, req)
		require.Error(t, updateErr)
		assert.Equal(t, codes.PermissionDenied, status.Code(updateErr))
		assert.Contains(t, updateErr.Error(), "Access denied")
	})
}

func TestGetUserInvitations(t *testing.T) {
	t.Run("auth", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		runAuthTests(
			t,
			"AUTH_R",
			false, // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.GetUserInvitationsRequest{
				Email: "<EMAIL>",
			},
			server.GetUserInvitations,
		)
	})

	t.Run("no invitations", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")

		// Create a subscription for the tenant
		setupSubscription(t, server, "", selfServeTeamTenantID, 5)
		// Invite a different user.
		inviteReq := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		_, err := server.InviteUsersToTenant(ctx, inviteReq)
		require.NoError(t, err)

		req := &authpb.GetUserInvitationsRequest{
			Email: "<EMAIL>",
		}
		resp, err := server.GetUserInvitations(ctx, req)
		require.NoError(t, err)
		assert.Empty(t, resp.Invitations)
	})

	t.Run("invitations", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		req := &authpb.GetUserInvitationsRequest{
			Email: "<EMAIL>",
		}

		// Create subscriptions for the tenant
		setupSubscription(t, server, "", selfServeTeamTenantID, 5)
		setupSubscription(t, server, "", selfServeTeamTenantID2, 5)
		// Invite the user to two tenants.
		inviteReq1 := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>", "<EMAIL>"},
		}
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		_, err := server.InviteUsersToTenant(ctx, inviteReq1)
		require.NoError(t, err)

		inviteReq2 := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID2,
			InviteeEmails: []string{"<EMAIL>", "<EMAIL>"},
		}
		ctx = authorizedContext(selfServeTeamTenantID2, authorizedUserID, "AUTH_RW")
		_, err = server.InviteUsersToTenant(ctx, inviteReq2)
		require.NoError(t, err)

		// Get the invitations.
		resp, err := server.GetUserInvitations(ctx, req)
		require.NoError(t, err)
		assert.Len(t, resp.Invitations, 2)
		invitationEmails := []string{resp.Invitations[0].GetInviteeEmail(), resp.Invitations[1].GetInviteeEmail()}
		assert.ElementsMatch(t, []string{"<EMAIL>", "<EMAIL>"}, invitationEmails)
	})

	t.Run("filter accepted invitations", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		getInvitationsReq := &authpb.GetUserInvitationsRequest{
			Email: "<EMAIL>",
		}

		// Create subscriptions for the tenant
		setupSubscription(t, server, "", selfServeTeamTenantID, 5)
		// Invite the user to a tenant.
		inviteReq1 := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		_, err := server.InviteUsersToTenant(ctx, inviteReq1)
		require.NoError(t, err)

		// Accept the invitation.
		invitationDAO := server.daoFactory.GetTenantInvitationDAO(selfServeTeamTenantID)
		resp, err := server.GetUserInvitations(ctx, getInvitationsReq)
		require.NoError(t, err)
		invitation := resp.Invitations[0]
		invitation.Status = auth_entities.TenantInvitation_ACCEPTED
		_, err = invitationDAO.Update(ctx, invitation)
		require.NoError(t, err)

		// The invitation should no longer be returned by GetUserInvitations.
		resp, err = server.GetUserInvitations(ctx, getInvitationsReq)
		require.NoError(t, err)
		assert.Empty(t, resp.Invitations)
	})

	t.Run("email normalization", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")

		// Create subscriptions for the tenant
		setupSubscription(t, server, "", selfServeTeamTenantID, 5)
		// Invite the user to a tenant.
		inviteReq1 := &authpb.InviteUsersToTenantRequest{
			TenantId:      selfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		_, err := server.InviteUsersToTenant(ctx, inviteReq1)
		require.NoError(t, err)

		// Get the invitations.
		resp, err := server.GetUserInvitations(ctx, &authpb.GetUserInvitationsRequest{
			Email: "<EMAIL>",
		})
		require.NoError(t, err)
		assert.Len(t, resp.Invitations, 1)
		assert.Equal(t, "<EMAIL>", resp.Invitations[0].GetInviteeEmail())
	})
}

func TestResolveInvitations(t *testing.T) {
	t.Run("AuthTests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		invitationID := "invitation1"
		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.ResolveInvitationsRequest{
				AcceptInvitationId:   &invitationID,
				DeclineInvitationIds: []string{"invitation2", "invitation3"},
			},
			server.ResolveInvitations,
		)
	})

	t.Run("Success", func(t *testing.T) {
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_RW")
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		acceptedInvitationID := "invitation1"
		req := &authpb.ResolveInvitationsRequest{
			AcceptInvitationId:   &acceptedInvitationID,
			DeclineInvitationIds: []string{"invitation2", "invitation3"},
		}

		resp, err := server.ResolveInvitations(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.NotEmpty(t, resp.InvitationResolutionId)

		// Verify that an invitation resolution record was created
		invitationResolutionDAO := server.daoFactory.GetInvitationResolutionDAO()
		invitationResolution, err := invitationResolutionDAO.Get(ctx, resp.InvitationResolutionId)
		require.NoError(t, err)
		require.NotNil(t, invitationResolution)
		require.Equal(t, resp.InvitationResolutionId, invitationResolution.Id)
		require.Equal(t, auth_entities.InvitationResolution_PENDING, invitationResolution.Status)

		// Verify that a message was published
		messages := server.asyncOpsPublisher.(*MockAsyncOpsPublisher).GetPublishedMessages()
		require.Len(t, messages, 1)

		publishedMsg := messages[0].GetResolveInvitationsMessage()
		require.NotNil(t, publishedMsg)
		require.True(t, proto.Equal(req, publishedMsg.ResolveInvitationsRequest))
		require.NotNil(t, publishedMsg.PublishTime)
	})
}

func TestGetResolveInvitationsStatus(t *testing.T) {
	t.Run("AuthTests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_R",
			false, // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.GetResolveInvitationsStatusRequest{
				InvitationResolutionId: "invitation-resolution-id",
			},
			server.GetResolveInvitationsStatus,
		)
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		invitationResolutionDAO := server.daoFactory.GetInvitationResolutionDAO()
		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")

		tenantCreationID := uuid.New().String()
		invitationResolution := &auth_entities.InvitationResolution{
			Id:        tenantCreationID,
			CreatedAt: timestamppb.Now(),
			Status:    auth_entities.InvitationResolution_PENDING,
		}

		_, err := invitationResolutionDAO.Create(ctx, invitationResolution)
		require.NoError(t, err)

		req := &authpb.GetResolveInvitationsStatusRequest{
			InvitationResolutionId: tenantCreationID,
		}

		resp, err := server.GetResolveInvitationsStatus(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)
		require.True(t, proto.Equal(invitationResolution, resp.InvitationResolution))
	})

	t.Run("NotFound", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(selfServeTeamTenantID, authorizedUserID, "AUTH_R")

		req := &authpb.GetResolveInvitationsStatusRequest{
			InvitationResolutionId: "non-existent-id",
		}

		_, err := server.GetResolveInvitationsStatus(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.NotFound, status.Code(err))
	})
}

// Set up the proper DAOs and tenant mappings so we can get the user's Orb Info
func SetupOrbTests(t *testing.T, server *TeamManagementServer) {
	// Create a test user with Orb customer ID
	userDAO := server.daoFactory.GetUserDAO()
	user := &auth_entities.User{
		Id:                authorizedUserID,
		Email:             "<EMAIL>",
		Tenants:           []string{professionalTenantID},
		OrbCustomerId:     "orb-customer-id",
		OrbSubscriptionId: "orb-subscription-id",
	}
	_, err := userDAO.Create(context.Background(), user)
	require.NoError(t, err)

	// Create an admin user with Orb customer ID
	adminUser := &auth_entities.User{
		Id:                adminUserID,
		Email:             "<EMAIL>",
		Tenants:           []string{selfServeTeamTenantID},
		OrbCustomerId:     "orb-customer-id",
		OrbSubscriptionId: "orb-subscription-id",
	}
	_, err = userDAO.Create(context.Background(), adminUser)
	require.NoError(t, err)

	// No subscription user
	noSubscriptionUser := &auth_entities.User{
		Id:                "no-subscription-user",
		Email:             "<EMAIL>",
		Tenants:           []string{professionalTenantID},
		OrbCustomerId:     "orb-customer-id",
		OrbSubscriptionId: "",
	}
	_, err = userDAO.Create(context.Background(), noSubscriptionUser)
	require.NoError(t, err)

	// Pending Subscription User
	pendingSubscriptionUser := &auth_entities.User{
		Id:                     "pending-subscription-user",
		Email:                  "<EMAIL>",
		Tenants:                []string{professionalTenantID},
		SubscriptionCreationId: "pending-subscription-id",
		SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
			Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
			Id:        "pending-subscription-id",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		},
	}
	_, err = userDAO.Create(context.Background(), pendingSubscriptionUser)
	require.NoError(t, err)

	// Add the authorized user to the tenant
	userTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO("professional-tenant")
	mapping := &auth_entities.UserTenantMapping{
		UserId: authorizedUserID,
		Tenant: "professional-tenant",
	}
	_, err = userTenantMappingDAO.Create(context.Background(), mapping)
	require.NoError(t, err)

	// Add the no-subscription user to the tenant
	noSubscriptionMapping := &auth_entities.UserTenantMapping{
		UserId: "no-subscription-user",
		Tenant: "professional-tenant",
	}
	_, err = userTenantMappingDAO.Create(context.Background(), noSubscriptionMapping)
	require.NoError(t, err)

	// Add the pending-subscription user to the tenant
	pendingSubscriptionMapping := &auth_entities.UserTenantMapping{
		UserId: "pending-subscription-user",
		Tenant: "professional-tenant",
	}
	_, err = userTenantMappingDAO.Create(context.Background(), pendingSubscriptionMapping)
	require.NoError(t, err)

	// Add the admin user to the tenant
	adminUserTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO("self-serve-team-tenant")
	adminMapping := &auth_entities.UserTenantMapping{
		UserId:          adminUserID,
		Tenant:          "self-serve-team-tenant",
		CustomerUiRoles: []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN},
	}
	_, err = adminUserTenantMappingDAO.Create(context.Background(), adminMapping)
	require.NoError(t, err)

	// Create a tenant subscription mapping with an Orb customer ID
	tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
	subMapping := &auth_entities.TenantSubscriptionMapping{
		TenantId:             professionalTenantID,
		OrbCustomerId:        "orb-customer-id",
		StripeSubscriptionId: "test-subscription-id",
		OrbSubscriptionId:    "test-subscription-id",
	}
	_, err = tenantSubscriptionMappingDAO.Create(context.Background(), subMapping)
	require.NoError(t, err)

	// Add to subscription DAO
	subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
	subscription := &auth_entities.Subscription{
		SubscriptionId:   "orb-subscription-id",
		OrbCustomerId:    "orb-customer-id",
		PriceId:          "test-price-id",
		Status:           auth_entities.Subscription_ACTIVE,
		Seats:            1,
		HasPaymentMethod: true,
	}
	_, err = subscriptionDAO.Create(context.Background(), subscription)
	require.NoError(t, err)

	// Create a tenant subscription mapping for the self-serve team
	teamSubMapping := &auth_entities.TenantSubscriptionMapping{
		TenantId:          selfServeTeamTenantID,
		OrbCustomerId:     "orb-customer-id",
		OrbSubscriptionId: "orb-subscription-id",
	}
	_, err = tenantSubscriptionMappingDAO.Create(context.Background(), teamSubMapping)
	require.NoError(t, err)
}

func TestPurchaseCredits(t *testing.T) {
	t.Run("Invalid requests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Test invalid user ID
		req := &authpb.PurchaseCreditsRequest{
			UserId:   "",
			TenantId: professionalTenantID,
			Credits:  50.0,
		}
		_, err := server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) user ID")

		// Test invalid tenant ID
		req = &authpb.PurchaseCreditsRequest{
			UserId:   authorizedUserID,
			TenantId: "",
			Credits:  50.0,
		}
		_, err = server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) tenant ID")

		// Test nonexistent user ID
		req = &authpb.PurchaseCreditsRequest{
			UserId:   "nonexistent-user-id",
			TenantId: professionalTenantID,
			Credits:  50.0,
		}
		_, err = server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get user billing info")
	})

	t.Run("Invalid purchase amounts", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
		}, nil)

		// Test too few credits
		req := &authpb.PurchaseCreditsRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
			Credits:  0.0,
		}
		_, err := server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Cannot purchase less than $10.00 worth of messages at a time")

		// Test too many credits
		req = &authpb.PurchaseCreditsRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
			Credits:  10001.0, // just over $100
		}
		_, err = server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Cannot purchase more than $100.00 worth of messages at a time")
	})

	t.Run("On Trial Plan", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbTrialPlanID,
		}, nil)

		// Test purchasing credits on a trial plan -- should not be allowed
		req := &authpb.PurchaseCreditsRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
			Credits:  1000,
		}
		_, err := server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Cannot purchase credits on a trial plan")
	})

	t.Run("Failed to get user subscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return((*orb.OrbSubscriptionInfo)(nil), fmt.Errorf("test error"))

		req := &authpb.PurchaseCreditsRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
			Credits:  1000,
		}
		_, err := server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get user subscription")
	})

	t.Run("Failed to purchase credits", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:             "orb-subscription-id",
			ExternalPlanID:                orbProfessionalPlanID,
			CurrentBillingPeriodStartDate: time.Now().Add(-20 * 24 * time.Hour), // 20 days ago
		}, nil)
		server.orbClient.(*orb.MockOrbClient).On("PurchaseCredits", mock.Anything, mock.Anything, mock.Anything).Return(fmt.Errorf("test error"))

		req := &authpb.PurchaseCreditsRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
			Credits:  1000,
		}
		_, err := server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to purchase credits")
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		startDate := time.Now().Add(-20 * 24 * time.Hour)
		roundedStartDate := time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, startDate.Location())

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:             "orb-subscription-id",
			ExternalPlanID:                orbProfessionalPlanID,
			CurrentBillingPeriodStartDate: startDate,
		}, nil)
		server.orbClient.(*orb.MockOrbClient).On("PurchaseCredits", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		req := &authpb.PurchaseCreditsRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
			Credits:  1000,
		}
		_, err := server.PurchaseCredits(ctx, req)
		require.NoError(t, err)
		mockOrbClient := server.orbClient.(*orb.MockOrbClient)
		mockOrbClient.AssertCalled(t, "PurchaseCredits", mock.Anything, mock.Anything, mock.Anything)
		// Assert the start date is set correctly
		mockOrbClient.AssertCalled(t, "PurchaseCredits", mock.Anything, mock.MatchedBy(func(creditPurchase orb.OrbCreditPurchase) bool {
			return creditPurchase.StartDate != nil && creditPurchase.StartDate.Equal(roundedStartDate)
		}), mock.Anything)
	})

	t.Run("Success with idempotency key", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")
		key := "idempotency-key"
		reusedKey := "reused-idempotency-key"

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:             "orb-subscription-id",
			ExternalPlanID:                orbProfessionalPlanID,
			CurrentBillingPeriodStartDate: time.Now().Add(-20 * 24 * time.Hour), // 20 days ago
		}, nil)
		server.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbPlanInfo{}, nil)
		server.orbClient.(*orb.MockOrbClient).On("PurchaseCredits", mock.Anything, mock.Anything, &key).Return(nil)
		server.orbClient.(*orb.MockOrbClient).On("PurchaseCredits", mock.Anything, mock.Anything, &reusedKey).Return(fmt.Errorf("reused key"))

		// If we purchase credits with an idempotency key, should succeed
		req := &authpb.PurchaseCreditsRequest{
			UserId:         authorizedUserID,
			TenantId:       professionalTenantID,
			Credits:        1000,
			IdempotencyKey: &key,
		}
		_, err := server.PurchaseCredits(ctx, req)
		require.NoError(t, err)

		// If we retry with an already used idempotency key, it should fail
		req = &authpb.PurchaseCreditsRequest{
			UserId:         authorizedUserID,
			TenantId:       professionalTenantID,
			Credits:        1000,
			IdempotencyKey: &reusedKey,
		}
		_, err = server.PurchaseCredits(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to purchase credits")
	})

	t.Run("Auth tests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
		}, nil)
		server.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbPlanInfo{}, nil)
		server.orbClient.(*orb.MockOrbClient).On("PurchaseCredits", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly
			&authpb.PurchaseCreditsRequest{
				UserId:   authorizedUserID,
				TenantId: professionalTenantID,
				Credits:  1000,
			},
			server.PurchaseCredits,
		)
	})
}

func TestCancelSubscription(t *testing.T) {
	t.Run("Auth checks", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)

		server.orbClient.(*orb.MockOrbClient).On("CancelOrbSubscription", mock.Anything, mock.Anything, orb.PlanChangeEndOfTerm, mock.Anything, mock.Anything).Return(nil)

		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly
			&authpb.CancelSubscriptionRequest{
				UserId:   authorizedUserID,
				TenantId: professionalTenantID,
			},
			server.CancelSubscription,
		)
	})

	t.Run("Invalid requests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Test invalid user ID
		req := &authpb.CancelSubscriptionRequest{
			UserId:   "",
			TenantId: professionalTenantID,
		}
		_, err := server.CancelSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) user ID")

		// Test invalid tenant ID
		req = &authpb.CancelSubscriptionRequest{
			UserId:   authorizedUserID,
			TenantId: "",
		}
		_, err = server.CancelSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) tenant ID")

		// Test nonexistent user ID (not in DAO)
		req = &authpb.CancelSubscriptionRequest{
			UserId:   "nonexistent-user-id",
			TenantId: professionalTenantID,
		}
		_, err = server.CancelSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get user billing info")
	})

	t.Run("Failed to cancel subscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// There are several reasons we can fail: no subscription exists for customer ID, subscription is already scheduled to end, orb/transport error
		// Is it worth it to test each of them?
		server.orbClient.(*orb.MockOrbClient).On("CancelOrbSubscription", mock.Anything, mock.Anything, orb.PlanChangeEndOfTerm, mock.Anything, mock.Anything).Return(fmt.Errorf("test error"))

		req := &authpb.CancelSubscriptionRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
		}
		_, err := server.CancelSubscription(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to cancel Orb subscription")
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		server.orbClient.(*orb.MockOrbClient).On("CancelOrbSubscription", mock.Anything, mock.Anything, orb.PlanChangeEndOfTerm, mock.Anything, mock.Anything).Return(nil)

		req := &authpb.CancelSubscriptionRequest{
			UserId:   authorizedUserID,
			TenantId: professionalTenantID,
		}
		_, err := server.CancelSubscription(ctx, req)
		require.NoError(t, err)
	})
}

func SetupGetUserOrbInfoMocks(t *testing.T, server *TeamManagementServer) {
	server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
		OrbSubscriptionID: "orb-subscription-id",
		ExternalPlanID:    orbProfessionalPlanID,
		CurrentFixedQuantities: &orb.FixedQuantities{
			Seats:              10,
			SeatsID:            "seat-price-id",
			IncludedMessages:   100,
			IncludedMessagesID: "credit-price-id",
		},
		PortalUrl: "https://billing.example.com/portal",
	}, nil)

	// Mock the new GetScheduledPlanChanges method - return nil for no scheduled changes by default
	server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return((*string)(nil), nil)

	server.orbClient.(*orb.MockOrbClient).On("GetCustomerCreditInfo", mock.Anything, "orb-customer-id", mock.Anything, mock.Anything).Return(&orb.CustomerCreditInfo{
		ActiveCredits:               100,
		CreditsUsedThisBillingCycle: 50,
		PendingCredits:              25,
	}, nil)

	server.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbPlanInfo{
		ExternalPlanID: orbProfessionalPlanID,
		Name:           "Developer Plan",
		PricePerSeat:   "30.0",
	}, nil)

	server.orbClient.(*orb.MockOrbClient).On("GetFailedPaymentInfo", mock.Anything, mock.Anything).Return(nil, nil)
}

func TestGetUserOrbInfo(t *testing.T) {
	t.Run("Auth checks", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		runAuthTests(
			t,
			"AUTH_R",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly
			&authpb.GetUserOrbInfoRequest{
				UserId: authorizedUserID,
			},
			server.GetUserOrbInfo,
		)
	})

	t.Run("Invalid requests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		// Test invalid user ID
		req := &authpb.GetUserOrbInfoRequest{
			UserId: "",
		}
		_, err := server.GetUserOrbInfo(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) user ID")
	})

	t.Run("Failed to get user subscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return((*orb.OrbSubscriptionInfo)(nil), fmt.Errorf("test error"))

		req := &authpb.GetUserOrbInfoRequest{
			UserId: authorizedUserID,
		}
		_, err := server.GetUserOrbInfo(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get Orb subscription info")
	})

	t.Run("Failed to get customer credit info", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
		}, nil)
		server.orbClient.(*orb.MockOrbClient).On("GetCustomerCreditInfo", mock.Anything, "orb-customer-id", mock.Anything, mock.Anything).Return((*orb.CustomerCreditInfo)(nil), fmt.Errorf("test error"))

		req := &authpb.GetUserOrbInfoRequest{
			UserId: authorizedUserID,
		}
		_, err := server.GetUserOrbInfo(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get Orb credit info")
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		SetupGetUserOrbInfoMocks(t, server)

		req := &authpb.GetUserOrbInfoRequest{
			UserId: authorizedUserID,
		}
		resp, err := server.GetUserOrbInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "orb-customer-id", resp.OrbCustomerId)
		assert.Equal(t, "orb-subscription-id", resp.OrbSubscriptionId)
		assert.Equal(t, orbProfessionalPlanID, resp.ExternalPlanId)
		assert.Equal(t, int32(100), resp.UsageUnitsAvailable)
		assert.Equal(t, int32(50), resp.UsageUnitsUsedThisBillingCycle)
		assert.Equal(t, int32(25), resp.UsageUnitsPending)
	})

	t.Run("Portal URL visibility in non-team tenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetUserOrbInfoRequest{
			UserId: authorizedUserID,
		}
		resp, err := server.GetUserOrbInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "https://billing.example.com/portal", resp.PortalUrl)
	})

	t.Run("Portal URL visibility in team tenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// Admin should see the portal URL
		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_R")
		req := &authpb.GetUserOrbInfoRequest{
			UserId: adminUserID,
		}
		resp, err := server.GetUserOrbInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "https://billing.example.com/portal", resp.PortalUrl)

		// Non-admin should not see the portal URL
		ensureNonAdmin(server, nonAdminUserID, selfServeTeamTenantID)
		ctx = authorizedContext(selfServeTeamTenantID, nonAdminUserID, "AUTH_R")
		req.UserId = nonAdminUserID
		resp, err = server.GetUserOrbInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "", resp.PortalUrl)
	})
}

func TestGetUserOrbPlanInfo(t *testing.T) {
	t.Run("Auth checks", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		runAuthTests(
			t,
			"AUTH_R",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly
			&authpb.GetUserOrbPlanInfoRequest{
				UserId: authorizedUserID,
			},
			server.GetUserOrbPlanInfo,
		)
	})

	t.Run("Invalid requests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		// Test invalid user ID
		req := &authpb.GetUserOrbPlanInfoRequest{
			UserId: "",
		}
		_, err := server.GetUserOrbPlanInfo(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) user ID")
	})

	t.Run("Failed to get plan info", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		server.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return((*orb.OrbPlanInfo)(nil), fmt.Errorf("test error"))

		req := &authpb.GetUserOrbPlanInfoRequest{
			UserId: authorizedUserID,
		}
		_, err := server.GetUserOrbPlanInfo(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get Orb plan info")
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		req := &authpb.GetUserOrbPlanInfoRequest{
			UserId: authorizedUserID,
		}
		resp, err := server.GetUserOrbPlanInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, orbProfessionalPlanID, resp.OrbPlanInfo.ExternalPlanId)
		assert.Equal(t, "Developer Plan", resp.OrbPlanInfo.FormattedPlanName)
		assert.Equal(t, "30.0", resp.OrbPlanInfo.PricePerSeat)
		assert.Equal(t, "0.10", resp.OrbPlanInfo.AdditionalUsageUnitCost)
		assert.True(t, resp.OrbPlanInfo.AddUsageAvailable)
	})
}

func TestGetAllOrbPlans(t *testing.T) {
	t.Run("Auth check", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// Only check for AUTH_R scope
		runAuthTests(
			t,
			"AUTH_R",
			false, // checkTenant
			false, // checkUser
			false, // adminOnly
			&authpb.GetAllOrbPlansRequest{},
			server.GetAllOrbPlans,
		)
	})

	t.Run("Failed to get plan info", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		server.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return((*orb.OrbPlanInfo)(nil), fmt.Errorf("test error"))

		req := &authpb.GetAllOrbPlansRequest{}
		_, err := server.GetAllOrbPlans(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get Orb plan info")
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		req := &authpb.GetAllOrbPlansRequest{}
		resp, err := server.GetAllOrbPlans(ctx, req)
		require.NoError(t, err)
		assert.Len(t, resp.OrbPlans, 3)
	})
}

func TestUnschedulePendingSubscriptionCancellation(t *testing.T) {
	logging.SetupServerLogging()
	t.Run("Auth checks", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    "non-trial-plan-id",
			EndDate:           time.Now().Add(24 * time.Hour),
		}, nil)
		server.orbClient.(*orb.MockOrbClient).On("UnschedulePendingSubscriptionCancellation", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		// If not a member of a team, can unschedule for self
		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly
			&authpb.UnschedulePendingSubscriptionCancellationRequest{
				UserId: authorizedUserID,
			},
			server.UnschedulePendingSubscriptionCancellation,
		)

		// If a member of a team, must be an admin
		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			true,  // adminOnly
			&authpb.UnschedulePendingSubscriptionCancellationRequest{
				UserId: adminUserID,
			},
			server.UnschedulePendingSubscriptionCancellation,
		)
	})

	t.Run("Invalid requests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Test invalid user ID
		req := &authpb.UnschedulePendingSubscriptionCancellationRequest{
			UserId: "",
		}
		_, err := server.UnschedulePendingSubscriptionCancellation(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) user ID")
	})

	t.Run("Failed to get user subscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything, mock.Anything).Return((*orb.OrbSubscriptionInfo)(nil), fmt.Errorf("test error"))

		req := &authpb.UnschedulePendingSubscriptionCancellationRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePendingSubscriptionCancellation(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get user subscription")
	})

	t.Run("Cannot unschedule cancellation on a trial plan", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Set up the test subscription with a trial plan
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbTrialPlanID,
		}, nil)

		req := &authpb.UnschedulePendingSubscriptionCancellationRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePendingSubscriptionCancellation(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Cannot unschedule cancellation on a trial plan")
	})

	t.Run("Subscription is not scheduled to be cancelled", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Set up the test subscription with a non-trial plan, but no end date
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
		}, nil)

		req := &authpb.UnschedulePendingSubscriptionCancellationRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePendingSubscriptionCancellation(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Subscription is not scheduled to be cancelled")
	})

	t.Run("Subscription is already cancelled", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// End Date in the Past = Already Cancelled
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
			EndDate:           time.Now().Add(-24 * time.Hour),
		}, nil)

		req := &authpb.UnschedulePendingSubscriptionCancellationRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePendingSubscriptionCancellation(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Subscription is already cancelled")
	})

	t.Run("Failed to unschedule subscription cancellation", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Set up the test subscription with a non-trial plan and an end date in the future
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
			EndDate:           time.Now().Add(24 * time.Hour),
		}, nil)

		// Fail the unschedule call
		server.orbClient.(*orb.MockOrbClient).On("UnschedulePendingSubscriptionCancellation", mock.Anything, mock.Anything, mock.Anything).Return(fmt.Errorf("test error"))

		req := &authpb.UnschedulePendingSubscriptionCancellationRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePendingSubscriptionCancellation(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to unschedule subscription cancellation")
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Set up the test subscription with a non-trial plan and an end date in the future
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
			EndDate:           time.Now().Add(24 * time.Hour),
		}, nil)

		// Succeed the unschedule call
		server.orbClient.(*orb.MockOrbClient).On("UnschedulePendingSubscriptionCancellation", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		req := &authpb.UnschedulePendingSubscriptionCancellationRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePendingSubscriptionCancellation(ctx, req)
		require.NoError(t, err)
	})
}

func TestUnschedulePlanChanges(t *testing.T) {
	t.Run("AuthTests - Individual", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)

		// Set up GetScheduledPlanChanges mock for auth tests
		targetPlanID := "orb_developer_plan"
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return(&targetPlanID, nil)
		// Set up UnschedulePlanChanges mock for auth tests
		server.orbClient.(*orb.MockOrbClient).On("UnschedulePlanChanges", mock.Anything, "orb-subscription-id", mock.Anything).Return(nil)

		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly (for individual users)
			&authpb.UnschedulePlanChangesRequest{
				UserId: authorizedUserID,
			},
			server.UnschedulePlanChanges,
		)
	})

	t.Run("AuthTests - Team", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)

		// Set up GetScheduledPlanChanges mock for auth tests
		targetPlanID := "orb_developer_plan"
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return(&targetPlanID, nil)
		// Set up UnschedulePlanChanges mock for auth tests
		server.orbClient.(*orb.MockOrbClient).On("UnschedulePlanChanges", mock.Anything, "orb-subscription-id", mock.Anything).Return(nil)

		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			true,  // adminOnly (for team users)
			&authpb.UnschedulePlanChangesRequest{
				UserId: adminUserID,
			},
			server.UnschedulePlanChanges,
		)
	})

	t.Run("Invalid requests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Test invalid user ID
		req := &authpb.UnschedulePlanChangesRequest{
			UserId: "",
		}
		_, err := server.UnschedulePlanChanges(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "Invalid (empty) user ID")
	})

	t.Run("User not found", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, "non-existent-user", "AUTH_RW")

		req := &authpb.UnschedulePlanChangesRequest{
			UserId: "non-existent-user",
		}
		_, err := server.UnschedulePlanChanges(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.NotFound, status.Code(err))
		assert.Contains(t, err.Error(), "User not found")
	})

	t.Run("Failed to get scheduled plan changes", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Fail the GetScheduledPlanChanges call
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return((*string)(nil), fmt.Errorf("test error"))

		req := &authpb.UnschedulePlanChangesRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePlanChanges(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get scheduled plan changes")
	})

	t.Run("No scheduled plan changes found", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Return nil for GetScheduledPlanChanges (no scheduled changes)
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return((*string)(nil), nil)

		req := &authpb.UnschedulePlanChangesRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePlanChanges(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		assert.Contains(t, err.Error(), "No scheduled plan changes found")
	})

	t.Run("User has tier change in progress", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Update the existing user (created by SetupOrbTests) to have a tier change in progress
		userDAO := server.daoFactory.GetUserDAO()
		_, err := userDAO.TryUpdate(context.Background(), authorizedUserID, func(user *auth_entities.User) bool {
			user.TierChange = &auth_entities.User_TierChangeInfo{
				Id:         "tier-change-in-progress",
				TargetTier: auth_entities.UserTier_COMMUNITY,
			}
			return true
		}, func(retry int) float64 { return 0.1 })
		require.NoError(t, err)

		// Set up GetScheduledPlanChanges to return a scheduled plan change
		targetPlanID := "orb_developer_plan"
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return(&targetPlanID, nil)

		req := &authpb.UnschedulePlanChangesRequest{
			UserId: authorizedUserID,
		}
		_, err = server.UnschedulePlanChanges(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to unschedule pending plan changes")
	})

	t.Run("Failed to unschedule plan changes", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Set up GetScheduledPlanChanges to return a scheduled plan change
		targetPlanID := "orb_developer_plan"
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return(&targetPlanID, nil)

		// Fail the unschedule call
		server.orbClient.(*orb.MockOrbClient).On("UnschedulePlanChanges", mock.Anything, "orb-subscription-id", mock.Anything).Return(fmt.Errorf("test error"))

		req := &authpb.UnschedulePlanChangesRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePlanChanges(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to unschedule pending plan changes")
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Set up GetScheduledPlanChanges to return a scheduled plan change
		targetPlanID := "orb_developer_plan"
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return(&targetPlanID, nil)

		// Succeed the unschedule call
		server.orbClient.(*orb.MockOrbClient).On("UnschedulePlanChanges", mock.Anything, "orb-subscription-id", mock.Anything).Return(nil)

		req := &authpb.UnschedulePlanChangesRequest{
			UserId: authorizedUserID,
		}
		_, err := server.UnschedulePlanChanges(ctx, req)
		require.NoError(t, err)
	})
}

func TestPutUserOnPlan(t *testing.T) {
	// Helper function to create a user with specific properties
	createUser := func(t *testing.T, server *TeamManagementServer, userProps map[string]interface{}) {
		userDAO := server.daoFactory.GetUserDAO()

		// Default user properties
		user := &auth_entities.User{
			Id:               authorizedUserID,
			Email:            "<EMAIL>",
			Tenants:          []string{professionalTenantID},
			BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_ORB,
			StripeCustomerId: "stripe-customer-id",
			OrbCustomerId:    "orb-customer-id",
		}

		// Override with provided properties
		for key, value := range userProps {
			switch key {
			case "Id":
				user.Id = value.(string)
			case "Email":
				user.Email = value.(string)
			case "Tenants":
				user.Tenants = value.([]string)
			case "BillingMethod":
				user.BillingMethod = value.(auth_entities.BillingMethod)
			case "StripeCustomerId":
				if value != nil {
					user.StripeCustomerId = value.(string)
				} else {
					user.StripeCustomerId = ""
				}
			case "OrbCustomerId":
				if value != nil {
					user.OrbCustomerId = value.(string)
				} else {
					user.OrbCustomerId = ""
				}
			case "TierChange":
				user.TierChange = value.(*auth_entities.User_TierChangeInfo)
			default:
				// This would be a great spot to use @Costa's optional params pattern
				// but for tests it's not necessary
				panic("Invalid user property")
			}
		}

		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)
	}

	// Helper function to create a standard request
	createRequest := func(userId string, planId string) *authpb.PutUserOnPlanRequest {
		return &authpb.PutUserOnPlanRequest{
			UserId: userId,
			PlanId: planId,
		}
	}

	// Helper function to run a test case with error validation
	runErrorTest := func(t *testing.T, testName string, tenantID string, userProps map[string]interface{},
		req *authpb.PutUserOnPlanRequest, expectedCode codes.Code, expectedErrMsg string,
	) {
		t.Run(testName, func(t *testing.T) {
			server, cleanup := newTestTeamManagementServer(t)
			defer cleanup()

			// Create user if properties are provided
			if userProps != nil {
				createUser(t, server, userProps)
			}

			// Create context and execute request
			ctx := authorizedContext(tenantID, req.UserId, "AUTH_RW")
			_, err := server.PutUserOnPlan(ctx, req)

			// Validate error
			require.Error(t, err)
			require.Equal(t, expectedCode, status.Code(err))
			require.Contains(t, err.Error(), expectedErrMsg)
		})
	}

	t.Run("AuthTests - Individual", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a individual user for the auth tests
		createUser(t, server, map[string]interface{}{})

		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			false, // adminOnly (for individual users)
			&authpb.PutUserOnPlanRequest{
				UserId: authorizedUserID,
				PlanId: "orb_community_plan",
			},
			server.PutUserOnPlan,
		)
	})

	t.Run("AuthTests - Team", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a admin user in a self-serve team for the auth tests
		createUser(t, server, map[string]interface{}{
			"Id":      adminUserID,
			"Tenants": []string{selfServeTeamTenantID},
		})

		runAuthTests(
			t,
			"AUTH_RW",
			false, // checkTenant
			true,  // checkUser
			true,  // adminOnly (for team users)
			&authpb.PutUserOnPlanRequest{
				UserId: adminUserID,
				PlanId: "orb_developer_plan",
			},
			server.PutUserOnPlan,
		)
	})

	// Basic validation tests
	t.Run("ValidationTests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Test cases for validation
		testCases := []struct {
			name         string
			request      *authpb.PutUserOnPlanRequest
			expectedCode codes.Code
			expectedMsg  string
		}{
			{
				name: "MissingUserID",
				request: &authpb.PutUserOnPlanRequest{
					PlanId: "orb_community_plan",
				},
				expectedCode: codes.InvalidArgument,
				expectedMsg:  "User ID is required",
			},
			{
				name: "MissingPlanId",
				request: &authpb.PutUserOnPlanRequest{
					UserId: authorizedUserID,
					PlanId: "",
				},
				expectedCode: codes.InvalidArgument,
				expectedMsg:  "Plan ID is required",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				_, err := server.PutUserOnPlan(ctx, tc.request)
				require.Error(t, err)
				require.Equal(t, tc.expectedCode, status.Code(err))
				require.Contains(t, err.Error(), tc.expectedMsg)
			})
		}
	})

	// User existence and properties tests
	runErrorTest(t, "UserNotFound", professionalTenantID, nil,
		createRequest("non-existent-user", "orb_community_plan"),
		codes.NotFound, "User not found")

	// Billing method tests
	runErrorTest(t, "InvalidBillingMethod", professionalTenantID,
		map[string]interface{}{
			"BillingMethod": auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
		},
		createRequest(authorizedUserID, "orb_community_plan"),
		codes.FailedPrecondition, "User is not billing through orb")

	// Missing required fields tests
	runErrorTest(t, "MissingStripeCustomerId", professionalTenantID,
		map[string]interface{}{
			"StripeCustomerId": nil,
		},
		createRequest(authorizedUserID, "orb_community_plan"),
		codes.FailedPrecondition, "User does not have a stripe customer ID")

	runErrorTest(t, "MissingOrbCustomerId", professionalTenantID,
		map[string]interface{}{
			"OrbCustomerId": nil,
		},
		createRequest(authorizedUserID, "orb_community_plan"),
		codes.FailedPrecondition, "User does not have an orb customer ID")

	// Tenant tests
	runErrorTest(t, "NoTenants", professionalTenantID,
		map[string]interface{}{
			"Tenants": []string{},
		},
		createRequest(authorizedUserID, "orb_community_plan"),
		codes.FailedPrecondition, "User does not belong to exactly one tenant")

	runErrorTest(t, "MultipleTenants", professionalTenantID,
		map[string]interface{}{
			"Tenants": []string{professionalTenantID, communityTenantID},
		},
		createRequest(authorizedUserID, "orb_community_plan"),
		codes.FailedPrecondition, "User does not belong to exactly one tenant")

	runErrorTest(t, "TenantNotFound", "non-existent-tenant",
		map[string]interface{}{
			"Tenants": []string{"non-existent-tenant"},
		},
		createRequest(authorizedUserID, "orb_community_plan"),
		codes.NotFound, "not found")
}

func TestPutIndividualUserOnPlan(t *testing.T) {
	// Helper function to create a test user with specific properties
	createTestUser := func(t *testing.T, server *TeamManagementServer, tenantID string, tierChange *auth_entities.User_TierChangeInfo) *auth_entities.User {
		userDAO := server.daoFactory.GetUserDAO()

		// Create a standard test user with common properties
		user := &auth_entities.User{
			Id:               authorizedUserID,
			Email:            "<EMAIL>",
			Tenants:          []string{tenantID},
			BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_ORB,
			StripeCustomerId: "stripe-customer-id",
			OrbCustomerId:    "orb-customer-id",
			TierChange:       tierChange,
		}

		_, err := userDAO.Create(context.Background(), user)
		require.NoError(t, err)
		return user
	}

	// Helper function to create a standard request
	createRequest := func(userID string, planId string) *authpb.PutUserOnPlanRequest {
		return &authpb.PutUserOnPlanRequest{
			UserId: userID,
			PlanId: planId,
		}
	}

	// Helper function to run error test cases for direct method calls
	runDirectErrorTest := func(t *testing.T, testName string, tenantID string, contextUserID string,
		userModifier func(*auth_entities.User), tenantModifier func(*tw_proto.Tenant),
		requestModifier func(*authpb.PutUserOnPlanRequest),
		serverModifier func(*TeamManagementServer),
		expectedCode codes.Code, expectedErrMsg string,
	) {
		t.Run(testName, func(t *testing.T) {
			server, cleanup := newTestTeamManagementServer(t)
			defer cleanup()

			// Create context
			ctx := authorizedContext(tenantID, contextUserID, "AUTH_RW")

			// Create a basic user (not saved to DB yet)
			user := &auth_entities.User{
				Id:    authorizedUserID,
				Email: "<EMAIL>",
			}

			// Apply user modifier if provided
			if userModifier != nil {
				userModifier(user)
			}

			// Default tenant
			var tenantTier tw_proto.TenantTier
			var tenantName string

			switch tenantID {
			case professionalTenantID:
				tenantTier = tw_proto.TenantTier_PROFESSIONAL
				tenantName = "Professional"
			case communityTenantID:
				tenantTier = tw_proto.TenantTier_COMMUNITY
				tenantName = "Community"
			case enterpriseTenantID:
				tenantTier = tw_proto.TenantTier_ENTERPRISE
				tenantName = "Enterprise"
			default:
				tenantTier = tw_proto.TenantTier_PROFESSIONAL
				tenantName = "Custom"
			}

			tenant := &tw_proto.Tenant{
				Id:   tenantID,
				Name: tenantName,
				Tier: tenantTier,
			}

			// Apply tenant modifier if provided
			if tenantModifier != nil {
				tenantModifier(tenant)
			}

			// Create request
			req := createRequest(authorizedUserID, "orb_community_plan")
			if requestModifier != nil {
				requestModifier(req)
			}

			// Apply server modifier if provided
			if serverModifier != nil {
				serverModifier(server)
			}

			// Call the method directly
			_, err := server.putIndividualUserOnPlan(ctx, req, user, tenant)
			require.Error(t, err)
			require.Equal(t, expectedCode, status.Code(err))
			require.Contains(t, err.Error(), expectedErrMsg)
		})
	}

	// Helper function to setup and verify successful plan changes
	runSuccessfulPlanChangeTest := func(t *testing.T, testName string, tenantID string, planId string,
		expectedTier auth_entities.UserTier, featureFlagKey string, featureFlagValue string,
	) {
		t.Run(testName, func(t *testing.T) {
			server, cleanup := newTestTeamManagementServer(t)
			defer cleanup()

			ctx := authorizedContext(tenantID, authorizedUserID, "AUTH_RW")

			// Create a test user using the common helper
			createTestUser(t, server, tenantID, nil)

			// Set feature flag
			if featureFlagKey != "" && featureFlagValue != "" {
				ffHandle := server.featureFlagHandle.(*featureflags.LocalFeatureFlagHandler)
				ffHandle.Set(featureFlagKey, featureFlagValue)
			}

			// Create request
			req := createRequest(authorizedUserID, planId)

			// Call the method through the main handler
			resp, err := server.PutUserOnPlan(ctx, req)
			require.NoError(t, err)
			require.NotNil(t, resp)

			// Verify that a message was published
			messages := server.asyncOpsPublisher.(*MockAsyncOpsPublisher).GetPublishedMessages()
			require.Len(t, messages, 1)

			publishedMsg := messages[0].GetUserTierChangeMessage()
			require.NotNil(t, publishedMsg)
			require.Equal(t, authorizedUserID, publishedMsg.User.Id)
			require.Equal(t, expectedTier, publishedMsg.NewTier)

			// Verify that the user was updated with tier change info
			userDAO := server.daoFactory.GetUserDAO()
			updatedUser, err := userDAO.Get(ctx, authorizedUserID)
			require.NoError(t, err)
			require.NotNil(t, updatedUser.TierChange)
			require.Equal(t, expectedTier, updatedUser.TierChange.TargetTier)
		})
	}

	// Error test cases using direct method calls
	runDirectErrorTest(t, "AuthCheck", professionalTenantID, "different-user",
		nil, nil, nil, nil,
		codes.PermissionDenied, "")

	runDirectErrorTest(t, "TierChangeInProgress", professionalTenantID, authorizedUserID,
		func(user *auth_entities.User) {
			user.TierChange = &auth_entities.User_TierChangeInfo{
				Id:         "existing-tier-change",
				TargetTier: auth_entities.UserTier_COMMUNITY,
			}
		}, nil, nil, nil,
		codes.FailedPrecondition, "User already has a tier change in progress")

	runDirectErrorTest(t, "InvalidTenantTier", enterpriseTenantID, authorizedUserID,
		nil, nil, nil, nil,
		codes.InvalidArgument, "Current tenant must be either professional or community tier")

	runDirectErrorTest(t, "InvalidPlanId", professionalTenantID, authorizedUserID,
		nil, nil,
		func(req *authpb.PutUserOnPlanRequest) {
			req.PlanId = "invalid_plan_id" // Invalid plan ID
		}, nil,
		codes.InvalidArgument, "Plan not found in configuration")

	runDirectErrorTest(t, "MissingTenantNameConfig", professionalTenantID, authorizedUserID,
		nil, nil, nil,
		func(server *TeamManagementServer) {
			ffHandle := server.featureFlagHandle.(*featureflags.LocalFeatureFlagHandler)
			ffHandle.Set("auth_central_signup_tenant", "")
		},
		codes.Internal, "Default tenant not configured")

	// Success test cases
	runSuccessfulPlanChangeTest(t, "SuccessfulCommunityPlanChange",
		professionalTenantID, "orb_community_plan",
		auth_entities.UserTier_COMMUNITY, "auth_central_signup_tenant", "community-tenant")

	runSuccessfulPlanChangeTest(t, "SuccessfulDeveloperPlanChange",
		communityTenantID, "orb_developer_plan",
		auth_entities.UserTier_PROFESSIONAL, "auth_central_individual_tenant", "professional-tenant")

	t.Run("NoTenantChange", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Create a test user using the common helper
		createTestUser(t, server, professionalTenantID, nil)

		// Set feature flag for tenant names to match current tenant
		ffHandle := server.featureFlagHandle.(*featureflags.LocalFeatureFlagHandler)
		ffHandle.Set("auth_central_individual_tenant", professionalTenantID)

		req := createRequest(authorizedUserID, "orb_developer_plan")

		// Call the method through the main handler
		resp, err := server.PutUserOnPlan(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)

		// Verify that a message was published
		messages := server.asyncOpsPublisher.(*MockAsyncOpsPublisher).GetPublishedMessages()
		require.Len(t, messages, 1)

		publishedMsg := messages[0].GetUserTierChangeMessage()
		require.NotNil(t, publishedMsg)
		require.Equal(t, authorizedUserID, publishedMsg.User.Id)
		require.Equal(t, auth_entities.UserTier_PROFESSIONAL, publishedMsg.NewTier)
		require.Equal(t, professionalTenantID, publishedMsg.NewTenant.Id)
		require.Equal(t, professionalTenantID, publishedMsg.CurrentTenant.Id)

		// Verify that the user was updated with tier change info
		userDAO := server.daoFactory.GetUserDAO()
		updatedUser, err := userDAO.Get(ctx, authorizedUserID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.TierChange)
		require.Equal(t, auth_entities.UserTier_PROFESSIONAL, updatedUser.TierChange.TargetTier)
	})

	t.Run("FailedToUpdateUser", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")

		// Create a test user with tier change already in progress using the common helper
		tierChange := &auth_entities.User_TierChangeInfo{
			Id:         "existing-tier-change",
			TargetTier: auth_entities.UserTier_COMMUNITY,
			CreatedAt:  timestamppb.Now(),
			UpdatedAt:  timestamppb.Now(),
		}
		createTestUser(t, server, professionalTenantID, tierChange)

		// Set feature flag for tenant names
		ffHandle := server.featureFlagHandle.(*featureflags.LocalFeatureFlagHandler)
		ffHandle.Set("auth_central_signup_tenant", "community-tenant")

		// Create a new request that would normally succeed
		req := createRequest(authorizedUserID, "orb_community_plan")

		// This should fail because the user already has a tier change in progress
		_, err := server.PutUserOnPlan(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
		require.Contains(t, err.Error(), "User already has a tier change in progress")
	})

	// Random tenant selection validation tests
	t.Run("RandomTenantSelectionValidation", func(t *testing.T) {
		// Test controlled random selection with mocked randomSelector
		testCases := []struct {
			name           string
			tenantList     string
			mockRandValue  int
			expectedTenant string
			planId         string
			userTier       string
		}{
			{
				name:           "CommunityPlan_FirstTenant",
				tenantList:     "community-tenant,professional-tenant",
				mockRandValue:  0,
				expectedTenant: "community-tenant",
				planId:         "orb_community_plan",
				userTier:       "community",
			},
			{
				name:           "CommunityPlan_SecondTenant",
				tenantList:     "community-tenant,professional-tenant",
				mockRandValue:  1,
				expectedTenant: "professional-tenant",
				planId:         "orb_community_plan",
				userTier:       "community",
			},
			{
				name:           "DeveloperPlan_FirstTenant",
				tenantList:     "professional-tenant,enterprise-tenant",
				mockRandValue:  0,
				expectedTenant: "professional-tenant",
				planId:         "orb_developer_plan",
				userTier:       "individual",
			},
			{
				name:           "DeveloperPlan_SecondTenant",
				tenantList:     "professional-tenant,enterprise-tenant",
				mockRandValue:  1,
				expectedTenant: "enterprise-tenant",
				planId:         "orb_developer_plan",
				userTier:       "individual",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				server, cleanup := newTestTeamManagementServer(t)
				defer cleanup()

				// Mock the random selector to return specific value
				mockRandomSelector := &MockRandomSelector{returnValue: tc.mockRandValue}
				server.randomSelector = mockRandomSelector

				// Set up feature flag based on plan type
				ffHandle := server.featureFlagHandle.(*featureflags.LocalFeatureFlagHandler)
				if tc.userTier == "community" {
					ffHandle.Set("auth_central_signup_tenant", tc.tenantList)
				} else {
					ffHandle.Set("auth_central_individual_tenant", tc.tenantList)
				}

				// Set up user and context based on plan type
				var ctx context.Context
				if tc.userTier == "community" {
					ctx = authorizedContext(professionalTenantID, authorizedUserID, "AUTH_RW")
					createTestUser(t, server, professionalTenantID, nil)
				} else {
					ctx = authorizedContext(communityTenantID, authorizedUserID, "AUTH_RW")
					createTestUser(t, server, communityTenantID, nil)
				}

				req := createRequest(authorizedUserID, tc.planId)
				resp, err := server.PutUserOnPlan(ctx, req)
				require.NoError(t, err)
				require.NotNil(t, resp)

				// Verify the expected tenant was selected
				messages := server.asyncOpsPublisher.(*MockAsyncOpsPublisher).GetPublishedMessages()
				require.Len(t, messages, 1)
				publishedMsg := messages[0].GetUserTierChangeMessage()
				require.NotNil(t, publishedMsg)
				assert.Equal(t, tc.expectedTenant, publishedMsg.NewTenant.Id)
			})
		}
	})
}

// GetUserOrbCreditsInfo, GetUserOrbPaymentInfo, and GetUserOrbSubscriptionInfo are very similar.
// Use one test to reduce duplicate code as much as possible
func TestGetUserInfo(t *testing.T) {
	t.Run("Auth checks", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// Define common auth test parameters
		scope := "AUTH_R"
		checkTenant := false
		checkUser := true
		adminOnly := false

		// Run auth tests for all Orb-related endpoints
		t.Run("GetUserOrbCreditsInfo", func(t *testing.T) {
			runAuthTests(
				t,
				scope,
				checkTenant,
				checkUser,
				adminOnly,
				&authpb.GetUserOrbCreditsInfoRequest{UserId: authorizedUserID},
				server.GetUserOrbCreditsInfo,
			)
		})

		t.Run("GetUserOrbPaymentInfo", func(t *testing.T) {
			runAuthTests(
				t,
				scope,
				checkTenant,
				checkUser,
				adminOnly,
				&authpb.GetUserOrbPaymentInfoRequest{UserId: authorizedUserID},
				server.GetUserOrbPaymentInfo,
			)
		})

		t.Run("GetUserOrbSubscriptionInfo", func(t *testing.T) {
			runAuthTests(
				t,
				scope,
				checkTenant,
				checkUser,
				adminOnly,
				&authpb.GetUserOrbSubscriptionInfoRequest{UserId: authorizedUserID},
				server.GetUserOrbSubscriptionInfo,
			)
		})
	})

	t.Run("Invalid request", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// Define common test parameters
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")

		// Test each endpoint with an empty user ID
		type testCase struct {
			name         string
			makeReq      func() proto.Message
			callEndpoint func(proto.Message) error
		}

		testCases := []testCase{
			{
				name: "GetUserOrbCreditsInfo",
				makeReq: func() proto.Message {
					return &authpb.GetUserOrbCreditsInfoRequest{UserId: ""}
				},
				callEndpoint: func(req proto.Message) error {
					_, err := server.GetUserOrbCreditsInfo(ctx, req.(*authpb.GetUserOrbCreditsInfoRequest))
					return err
				},
			},
			{
				name: "GetUserOrbPaymentInfo",
				makeReq: func() proto.Message {
					return &authpb.GetUserOrbPaymentInfoRequest{UserId: ""}
				},
				callEndpoint: func(req proto.Message) error {
					_, err := server.GetUserOrbPaymentInfo(ctx, req.(*authpb.GetUserOrbPaymentInfoRequest))
					return err
				},
			},
			{
				name: "GetUserOrbSubscriptionInfo",
				makeReq: func() proto.Message {
					return &authpb.GetUserOrbSubscriptionInfoRequest{UserId: ""}
				},
				callEndpoint: func(req proto.Message) error {
					_, err := server.GetUserOrbSubscriptionInfo(ctx, req.(*authpb.GetUserOrbSubscriptionInfoRequest))
					return err
				},
			},
		}

		// Run all test cases
		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				req := tc.makeReq()
				err := tc.callEndpoint(req)
				require.Error(t, err)
				assert.Equal(t, codes.InvalidArgument, status.Code(err))
				assert.Contains(t, err.Error(), "Invalid (empty) user ID")
			})
		}
	})

	t.Run("Failed to call Orb", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)

		// GetUserOrbCreditsInfo
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req1 := &authpb.GetUserOrbCreditsInfoRequest{
			UserId: authorizedUserID,
		}
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: "orb-subscription-id",
			ExternalPlanID:    orbProfessionalPlanID,
		}, nil)
		server.orbClient.(*orb.MockOrbClient).On("GetCustomerCreditInfo", mock.Anything, "orb-customer-id", mock.Anything, mock.Anything).Return((*orb.CustomerCreditInfo)(nil), fmt.Errorf("test error"))
		_, err := server.GetUserOrbCreditsInfo(ctx, req1)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get Orb credit info")

		// GetUserOrbPaymentInfo -- we don't fail for this
		ctx = authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req2 := &authpb.GetUserOrbPaymentInfoRequest{
			UserId: authorizedUserID,
		}
		server.orbClient.(*orb.MockOrbClient).On("GetFailedPaymentInfo", mock.Anything, "orb-subscription-id").Return((*orb.FailedPaymentInfo)(nil), fmt.Errorf("test error"))
		_, err = server.GetUserOrbPaymentInfo(ctx, req2)
		require.NoError(t, err)

		// GetUserOrbSubscriptionInfo
		ctx = authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req3 := &authpb.GetUserOrbSubscriptionInfoRequest{
			UserId: authorizedUserID,
		}
		server.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, "orb-subscription-id", mock.Anything).Return((*orb.OrbSubscriptionInfo)(nil), fmt.Errorf("test error"))
		server.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return((*orb.OrbPlanInfo)(nil), fmt.Errorf("test error"))
		server.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, "orb-subscription-id", "orb-customer-id").Return((*string)(nil), nil)
		_, err = server.GetUserOrbSubscriptionInfo(ctx, req3)
		require.Error(t, err)
		assert.Equal(t, codes.Unknown, status.Code(err))
	})

	t.Run("Success", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// GetUserOrbCreditsInfo
		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req1 := &authpb.GetUserOrbCreditsInfoRequest{
			UserId: authorizedUserID,
		}
		resp1, err := server.GetUserOrbCreditsInfo(ctx, req1)
		require.NoError(t, err)
		require.NotNil(t, resp1)

		// GetUserOrbPaymentInfo
		ctx = authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req2 := &authpb.GetUserOrbPaymentInfoRequest{
			UserId: authorizedUserID,
		}
		resp2, err := server.GetUserOrbPaymentInfo(ctx, req2)
		require.NoError(t, err)
		require.NotNil(t, resp2)
		require.True(t, resp2.HasPaymentMethod)

		// GetUserOrbSubscriptionInfo
		ctx = authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req3 := &authpb.GetUserOrbSubscriptionInfoRequest{
			UserId: authorizedUserID,
		}
		resp3, err := server.GetUserOrbSubscriptionInfo(ctx, req3)
		require.NoError(t, err)
		require.NotNil(t, resp3)
	})

	t.Run("Pending subscription for user", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// GetUserOrbSubscriptionInfo
		ctx := authorizedContext(professionalTenantID, "pending-subscription-user", "AUTH_R")
		req3 := &authpb.GetUserOrbSubscriptionInfoRequest{
			UserId: "pending-subscription-user",
		}
		resp, err := server.GetUserOrbSubscriptionInfo(ctx, req3)
		require.NoError(t, err)
		assert.IsType(t, (*authpb.GetUserOrbSubscriptionInfoResponse_PendingSubscription)(nil), resp.OrbSubscriptionInfo)
	})

	t.Run("No subscription for user", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// GetUserOrbSubscriptionInfo
		ctx := authorizedContext(professionalTenantID, "no-subscription-user", "AUTH_R")
		req3 := &authpb.GetUserOrbSubscriptionInfoRequest{
			UserId: "no-subscription-user",
		}
		resp, err := server.GetUserOrbSubscriptionInfo(ctx, req3)
		require.NoError(t, err)
		assert.IsType(t, (*authpb.GetUserOrbSubscriptionInfoResponse_NonexistentSubscription)(nil), resp.OrbSubscriptionInfo)
	})

	t.Run("Portal URL visibility in non-team tenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		ctx := authorizedContext(professionalTenantID, authorizedUserID, "AUTH_R")
		req := &authpb.GetUserOrbSubscriptionInfoRequest{
			UserId: authorizedUserID,
		}
		resp, err := server.GetUserOrbSubscriptionInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "https://billing.example.com/portal", resp.GetSubscription().PortalUrl)
	})

	t.Run("Portal URL visibility in team tenant", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()
		SetupOrbTests(t, server)
		SetupGetUserOrbInfoMocks(t, server)

		// Admin should see the portal URL
		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_R")
		req := &authpb.GetUserOrbSubscriptionInfoRequest{
			UserId: adminUserID,
		}
		resp, err := server.GetUserOrbSubscriptionInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "https://billing.example.com/portal", resp.GetSubscription().PortalUrl)

		// Non-admin should not see the portal URL
		ensureNonAdmin(server, nonAdminUserID, selfServeTeamTenantID)
		ctx = authorizedContext(selfServeTeamTenantID, nonAdminUserID, "AUTH_R")
		req.UserId = nonAdminUserID
		resp, err = server.GetUserOrbSubscriptionInfo(ctx, req)
		require.NoError(t, err)
		assert.Equal(t, "", resp.GetSubscription().PortalUrl)
	})
}

func TestLegacySelfServeTeamRestrictions(t *testing.T) {
	t.Run("validateUserAndGetBillingInfo fails for legacy self-serve team", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a user in a legacy self-serve team
		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(legacySelfServeTeamTenantID, authorizedUserID, "AUTH_R")

		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{legacySelfServeTeamTenantID},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Add the user to the tenant
		userTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(legacySelfServeTeamTenantID)
		userTenantMapping := &auth_entities.UserTenantMapping{
			UserId: authorizedUserID,
			Tenant: legacySelfServeTeamTenantID,
		}
		_, err = userTenantMappingDAO.Create(ctx, userTenantMapping)
		require.NoError(t, err)

		// Try to get user billing info - this should fail because the user is in a legacy self-serve team
		req := &authpb.GetUserOrbInfoRequest{
			UserId: authorizedUserID,
		}
		_, err = server.GetUserOrbInfo(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.Internal, status.Code(err))
		require.Contains(t, err.Error(), "User is in a legacy self-serve team tenant")
	})

	t.Run("teamManagementAuthCheck fails for legacy self-serve team", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a user in a legacy self-serve team
		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(legacySelfServeTeamTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:      authorizedUserID,
			Email:   "<EMAIL>",
			Tenants: []string{legacySelfServeTeamTenantID},
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Add the user to the tenant
		userTenantMappingDAO := server.daoFactory.GetUserTenantMappingDAO(legacySelfServeTeamTenantID)
		userTenantMapping := &auth_entities.UserTenantMapping{
			UserId: authorizedUserID,
			Tenant: legacySelfServeTeamTenantID,
		}
		_, err = userTenantMappingDAO.Create(ctx, userTenantMapping)
		require.NoError(t, err)

		// Try to invite users to the tenant - this should fail because the tenant is a legacy self-serve team
		req := &authpb.InviteUsersToTenantRequest{
			TenantId:      legacySelfServeTeamTenantID,
			InviteeEmails: []string{"<EMAIL>"},
		}
		_, err = server.InviteUsersToTenant(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.PermissionDenied, status.Code(err))
		require.Contains(t, err.Error(), "Access denied")
	})

	t.Run("CreateTenantForTeam fails for legacy self-serve team", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		// Create a user in a legacy self-serve team
		userDAO := server.daoFactory.GetUserDAO()
		ctx := authorizedContext(legacySelfServeTeamTenantID, authorizedUserID, "AUTH_RW")

		user := &auth_entities.User{
			Id:                authorizedUserID,
			Email:             "<EMAIL>",
			Tenants:           []string{legacySelfServeTeamTenantID},
			StripeCustomerId:  "stripe-customer-id",
			OrbCustomerId:     "orb-customer-id",
			OrbSubscriptionId: "orb-subscription-id",
		}
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		subscription := &auth_entities.Subscription{
			SubscriptionId:   "orb-subscription-id",
			StripeCustomerId: "stripe-customer-id",
			PriceId:          "price_test",
			Owner:            &auth_entities.Subscription_UserId{UserId: authorizedUserID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
		}
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		_, err = subscriptionDAO.Create(ctx, subscription)
		require.NoError(t, err)

		// Try to create a tenant for team - this should fail because the user is in a legacy self-serve team
		req := &authpb.CreateTenantForTeamRequest{
			AdminUserId: authorizedUserID,
		}
		_, err = server.CreateTenantForTeam(ctx, req)
		require.Error(t, err)
		require.Equal(t, codes.FailedPrecondition, status.Code(err))
		require.Contains(t, err.Error(), "User belongs to a self-serve team tenant")
	})
}

func TestPutTeamOnPlan(t *testing.T) {
	// Helper function to create a standard request
	createRequest := func(userId string, planId string) *authpb.PutUserOnPlanRequest {
		return &authpb.PutUserOnPlanRequest{
			UserId: userId,
			PlanId: planId,
		}
	}

	// Helper function to setup a tenant subscription mapping
	// Returns the mapping for convenience.
	setupTenantSubscriptionMapping := func(t *testing.T, server *TeamManagementServer, tenantID string, orbSubscriptionID string) *auth_entities.TenantSubscriptionMapping {
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		// Basic check to prevent re-creation if already exists with the same ID for test idempotency.
		// A more robust solution might involve cleaning up DAO state before each test run.
		existingMapping, _ := tenantSubscriptionMappingDAO.Get(context.Background(), tenantID)
		if existingMapping != nil && existingMapping.OrbSubscriptionId == orbSubscriptionID {
			return existingMapping
		}
		// If it exists with a different OrbSubID for the same tenant, delete and recreate for test isolation.
		if existingMapping != nil {
			_ = tenantSubscriptionMappingDAO.Delete(context.Background(), tenantID)
		}
		mapping := &auth_entities.TenantSubscriptionMapping{
			TenantId:          tenantID,
			OrbCustomerId:     "orb-customer-for-" + tenantID,
			OrbSubscriptionId: orbSubscriptionID,
			StripeCustomerId:  "stripe-customer-for-" + tenantID,
			CreatedAt:         timestamppb.Now(),
		}
		_, err := tenantSubscriptionMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)
		return mapping
	}

	// Helper function to setup a subscription in the local DAO
	setupSubscriptionForTeamInDAO := func(t *testing.T, server *TeamManagementServer, tenantID string, orbSubscriptionID string, initialOrbPlanID string) {
		subscriptionDAO := server.daoFactory.GetSubscriptionDAO()
		// Basic check for idempotency
		existingSub, _ := subscriptionDAO.Get(context.Background(), orbSubscriptionID)
		if existingSub != nil {
			_ = subscriptionDAO.Delete(context.Background(), orbSubscriptionID)
		}
		subscription := &auth_entities.Subscription{
			SubscriptionId:   orbSubscriptionID,
			StripeCustomerId: "stripe-customer-for-" + tenantID,
			PriceId:          "price_test",
			Status:           auth_entities.Subscription_ACTIVE,
			Owner:            &auth_entities.Subscription_TenantId{TenantId: tenantID},
			CreatedAt:        timestamppb.Now(),
			UpdatedAt:        timestamppb.Now(),
			ExternalPlanId:   initialOrbPlanID,
		}
		_, err := subscriptionDAO.Create(context.Background(), subscription)
		require.NoError(t, err)
	}

	t.Run("SuccessfulOrbPlanChange", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		orbSubID := "orb-sub-successful-change-" + uuid.New().String()
		setupTenantSubscriptionMapping(t, server, selfServeTeamTenantID, orbSubID)
		setupSubscriptionForTeamInDAO(t, server, selfServeTeamTenantID, orbSubID, orbTrialPlanID)

		mockOrbClient := server.orbClient.(*orb.MockOrbClient)
		publisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		publisher.ClearMessages()

		mockOrbClient.On("GetUserSubscription",
			mock.Anything,
			orbSubID,
			mock.MatchedBy(func(ids *orb.ItemIds) bool {
				return ids.SeatsID == server.orbConfig.SeatsItemID &&
					ids.IncludedMessagesID == server.orbConfig.IncludedMessagesItemID
			}),
		).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubID,
			ExternalPlanID:    orbTrialPlanID,
		}, nil).Once()

		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := createRequest(adminUserID, "orb_developer_plan")

		resp, err := server.PutUserOnPlan(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)

		messages := publisher.GetPublishedMessages()
		require.Len(t, messages, 1, "Expected one message to be published")

		wrapperMsg := messages[0]
		publishedMsg := wrapperMsg.GetTeamPlanChangeMessage()
		require.NotNil(t, publishedMsg, "Published message should be a TeamPlanChangeMessage")

		assert.Equal(t, selfServeTeamTenantID, publishedMsg.TeamTenantId)
		assert.Equal(t, orbProfessionalPlanID, publishedMsg.TargetOrbPlanId, "Target Orb plan should be Professional")
		assert.Equal(t, adminUserID, publishedMsg.InitiatedByUserId)
		assert.NotEmpty(t, publishedMsg.PlanChangeId)
		assert.NotNil(t, publishedMsg.PublishTime)

		mockOrbClient.AssertExpectations(t)

		// Verify TenantSubscriptionMapping PlanChangeId is updated
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		updatedMapping, err := tenantSubscriptionMappingDAO.Get(context.Background(), selfServeTeamTenantID)
		require.NoError(t, err)
		require.NotNil(t, updatedMapping)
		assert.Equal(t, publishedMsg.PlanChangeId, updatedMapping.PlanChange.Id, "TenantSubscriptionMapping.PlanChangeId should match")
	})

	t.Run("NoChangeNeeded", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		orbSubID := "orb-sub-no-change-" + uuid.New().String()
		setupTenantSubscriptionMapping(t, server, selfServeTeamTenantID, orbSubID)
		setupSubscriptionForTeamInDAO(t, server, selfServeTeamTenantID, orbSubID, orbProfessionalPlanID)

		mockOrbClient := server.orbClient.(*orb.MockOrbClient)
		publisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		publisher.ClearMessages()

		mockOrbClient.On("GetUserSubscription",
			mock.Anything,
			orbSubID,
			mock.MatchedBy(func(ids *orb.ItemIds) bool {
				return ids.SeatsID == server.orbConfig.SeatsItemID &&
					ids.IncludedMessagesID == server.orbConfig.IncludedMessagesItemID
			}),
		).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubID,
			ExternalPlanID:    orbProfessionalPlanID,
			OrbStatus:         "active", // Ensure status is active for no change
		}, nil).Once()

		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := createRequest(adminUserID, "orb_developer_plan")

		resp, err := server.PutUserOnPlan(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)

		messages := publisher.GetPublishedMessages()
		require.Empty(t, messages, "No message should be published if already on the target Orb plan")

		mockOrbClient.AssertExpectations(t)
	})

	t.Run("CanceledSubscriptionShouldChange", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		orbSubID := "orb-sub-canceled-subscription" + uuid.New().String()
		setupTenantSubscriptionMapping(t, server, selfServeTeamTenantID, orbSubID)
		setupSubscriptionForTeamInDAO(t, server, selfServeTeamTenantID, orbSubID, orbProfessionalPlanID)

		mockOrbClient := server.orbClient.(*orb.MockOrbClient)
		publisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		publisher.ClearMessages()

		mockOrbClient.On("GetUserSubscription",
			mock.Anything,
			orbSubID,
			mock.MatchedBy(func(ids *orb.ItemIds) bool {
				return ids.SeatsID == server.orbConfig.SeatsItemID &&
					ids.IncludedMessagesID == server.orbConfig.IncludedMessagesItemID
			}),
		).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: orbSubID,
			ExternalPlanID:    orbProfessionalPlanID,
			OrbStatus:         "canceled",
		}, nil).Once()

		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := createRequest(adminUserID, "orb_developer_plan")

		resp, err := server.PutUserOnPlan(ctx, req)
		require.NoError(t, err)
		require.NotNil(t, resp)

		messages := publisher.GetPublishedMessages()
		require.Len(t, messages, 1, "Expected one message to be published")

		wrapperMsg := messages[0]
		publishedMsg := wrapperMsg.GetTeamPlanChangeMessage()
		require.NotNil(t, publishedMsg, "A TeamPlanChangeMessage should be published if the subscription is canceled")

		assert.Equal(t, selfServeTeamTenantID, publishedMsg.TeamTenantId)
		assert.Equal(t, orbProfessionalPlanID, publishedMsg.TargetOrbPlanId, "Target Orb plan should be Professional")
		assert.Equal(t, adminUserID, publishedMsg.InitiatedByUserId)
		assert.NotEmpty(t, publishedMsg.PlanChangeId)
		assert.NotNil(t, publishedMsg.PublishTime)

		mockOrbClient.AssertExpectations(t)

		// Verify TenantSubscriptionMapping PlanChangeId is updated
		tenantSubscriptionMappingDAO := server.daoFactory.GetTenantSubscriptionMappingDAO()
		updatedMapping, err := tenantSubscriptionMappingDAO.Get(context.Background(), selfServeTeamTenantID)
		require.NoError(t, err)
		require.NotNil(t, updatedMapping)
		assert.Equal(t, publishedMsg.PlanChangeId, updatedMapping.PlanChange.Id, "TenantSubscriptionMapping.PlanChangeId should match")
	})

	t.Run("CommunityPlanNotAllowed", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		orbSubID := "orb-sub-community-not-allowed-" + uuid.New().String()
		setupTenantSubscriptionMapping(t, server, selfServeTeamTenantID, orbSubID)

		publisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		publisher.ClearMessages()

		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := createRequest(adminUserID, "orb_community_plan")

		_, err := server.PutUserOnPlan(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		// Checking against the error message defined in putTeamOnPlan
		assert.Contains(t, err.Error(), "Teams cannot be put on the community plan")

		messages := publisher.GetPublishedMessages()
		require.Empty(t, messages)
	})

	t.Run("TenantSubscriptionMappingNotFound", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		publisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		publisher.ClearMessages()
		mockOrbClient := server.orbClient.(*orb.MockOrbClient)

		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := createRequest(adminUserID, "orb_developer_plan")

		_, err := server.PutUserOnPlan(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.NotFound, status.Code(err))
		assert.Contains(t, err.Error(), "Tenant subscription mapping not found")

		messages := publisher.GetPublishedMessages()
		require.Empty(t, messages)
		mockOrbClient.AssertNotCalled(t, "GetUserSubscription", mock.Anything, mock.Anything, mock.Anything)
	})

	t.Run("OrbClientFailsToGetUserSubscription", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		orbSubID := "orb-sub-client-fails-" + uuid.New().String()
		setupTenantSubscriptionMapping(t, server, selfServeTeamTenantID, orbSubID)

		mockOrbClient := server.orbClient.(*orb.MockOrbClient)
		publisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		publisher.ClearMessages()

		mockOrbClient.On("GetUserSubscription",
			mock.Anything,
			orbSubID,
			mock.Anything,
		).Return((*orb.OrbSubscriptionInfo)(nil), fmt.Errorf("orb API error")).Once()

		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := createRequest(adminUserID, "orb_developer_plan")

		_, err := server.PutUserOnPlan(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to get Orb subscription info")

		messages := publisher.GetPublishedMessages()
		require.Empty(t, messages)
		mockOrbClient.AssertExpectations(t)
	})

	t.Run("InvalidPlanId", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		orbSubID := "orb-sub-invalid-plan-id-" + uuid.New().String()
		setupTenantSubscriptionMapping(t, server, selfServeTeamTenantID, orbSubID)

		publisher := server.asyncOpsPublisher.(*MockAsyncOpsPublisher)
		publisher.ClearMessages()

		ctx := authorizedContext(selfServeTeamTenantID, adminUserID, "AUTH_RW")
		req := &authpb.PutUserOnPlanRequest{
			UserId: adminUserID,
			PlanId: "invalid_plan_id", // Invalid plan ID
		}

		_, err := server.PutUserOnPlan(ctx, req)
		require.Error(t, err)
		assert.Equal(t, codes.InvalidArgument, status.Code(err))
		// Checking against the error message defined in putTeamOnPlan
		assert.Contains(t, err.Error(), "Plan not found in configuration")

		messages := publisher.GetPublishedMessages()
		require.Empty(t, messages)
	})
}

func TestGetTenantPlanStatus(t *testing.T) {
	mockPlanChangeID := "pc-id-123"
	mockTargetPlanID := "orb-plan-professional-v2"
	mockCreatedAt := timestamppb.Now()

	// Common request for auth tests
	goodRequest := &authpb.GetTenantPlanStatusRequest{
		TenantId: selfServeTeamTenantID, // Use a common tenant ID for auth tests
	}

	t.Run("AuthTests", func(t *testing.T) {
		server, cleanup := newTestTeamManagementServer(t)
		defer cleanup()

		runAuthTests(
			t,
			"AUTH_R", // Read scope should be sufficient
			true,     // checkTenant = true, as TenantId is in the request
			false,    // checkUser = false, not user-specific
			false,    // adminOnly = false
			goodRequest,
			server.GetTenantPlanStatus,
		)
	})

	testCases := []struct {
		name                         string
		tenantID                     string                                                            // Tenant ID for DAO setup and request
		setupData                    func(t *testing.T, server *TeamManagementServer, tenantID string) // Function to set up DB state
		expectedResponse             *authpb.GetTenantPlanStatusResponse
		expectedErrCode              codes.Code
		expectErr                    bool
		teamManagementFeatureEnabled bool
	}{
		{
			name:                         "Success - Plan Change Pending",
			tenantID:                     selfServeTeamTenantID,
			teamManagementFeatureEnabled: true,
			setupData: func(t *testing.T, server *TeamManagementServer, tenantID string) {
				dao := server.daoFactory.GetTenantSubscriptionMappingDAO()
				_, err := dao.Create(context.Background(), &auth_entities.TenantSubscriptionMapping{
					TenantId: tenantID,
					PlanChange: &auth_entities.TenantSubscriptionMapping_PlanChangeInfo{
						Id:              mockPlanChangeID,
						TargetOrbPlanId: mockTargetPlanID,
						CreatedAt:       mockCreatedAt,
					},
				})
				require.NoError(t, err)
			},
			expectedResponse: &authpb.GetTenantPlanStatusResponse{
				IsPending:           true,
				PendingTargetPlanId: proto.String(mockTargetPlanID), // Use proto.String for optional fields
				PlanChangeCreatedAt: mockCreatedAt,
			},
		},
		{
			name:                         "Success - No Plan Change Info",
			tenantID:                     selfServeTeamTenantID,
			teamManagementFeatureEnabled: true,
			setupData: func(t *testing.T, server *TeamManagementServer, tenantID string) {
				dao := server.daoFactory.GetTenantSubscriptionMappingDAO()
				_, err := dao.Create(context.Background(), &auth_entities.TenantSubscriptionMapping{
					TenantId:   tenantID,
					PlanChange: nil, // Explicitly nil
				})
				require.NoError(t, err)
			},
			expectedResponse: &authpb.GetTenantPlanStatusResponse{
				IsPending: false,
			},
		},
		{
			name:                         "Success - TenantSubscriptionMapping Not Found",
			tenantID:                     "non-existent-tenant-for-status", // Use a unique ID not set up
			teamManagementFeatureEnabled: true,
			setupData: func(t *testing.T, server *TeamManagementServer, tenantID string) {
				// No data setup, so the mapping won't be found
			},
			expectedResponse: &authpb.GetTenantPlanStatusResponse{
				IsPending: false,
			},
		},
		{
			name:                         "Error - TenantId Empty in Request",
			tenantID:                     "", // This will be in the request, not for DAO setup
			teamManagementFeatureEnabled: true,
			setupData:                    nil, // No DAO setup needed
			expectErr:                    true,
			expectedErrCode:              codes.InvalidArgument,
		},
		{
			name:                         "Error - Feature Disabled",
			tenantID:                     selfServeTeamTenantID,
			teamManagementFeatureEnabled: false, // Feature flag off
			setupData: func(t *testing.T, server *TeamManagementServer, tenantID string) {
				// Minimal setup just to have a valid target if the flag were on
				dao := server.daoFactory.GetTenantSubscriptionMappingDAO()
				_, err := dao.Create(context.Background(), &auth_entities.TenantSubscriptionMapping{TenantId: tenantID})
				require.NoError(t, err)
			},
			expectErr:       true,
			expectedErrCode: codes.Internal,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server, cleanup := newTestTeamManagementServer(t)
			defer cleanup()

			// Set the feature flag for teamManagementEnabled for this specific test case
			// The default in newTestTeamManagementServer is true, so we only need to set it if it's false for the test case.
			if ffHandle, ok := server.featureFlagHandle.(*featureflags.LocalFeatureFlagHandler); ok {
				if !tc.teamManagementFeatureEnabled {
					ffHandle.Set("auth_central_team_management_enabled", false)
				} else {
					ffHandle.Set("auth_central_team_management_enabled", true)
				}
			} else {
				t.Fatalf("server.featureFlagHandle is not a *featureflags.LocalFeatureFlagHandler")
			}

			// Setup data using the real DAO
			if tc.setupData != nil {
				tc.setupData(t, server, tc.tenantID)
			}

			// Use an authorized context. The specific user doesn't matter much if adminOnly=false and checkUser=false for the auth check.
			// The tenantID in the context should match the tenantID in the request for the auth check to pass.
			ctxTenantID := tc.tenantID
			if tc.tenantID == "" { // If request tenantID is empty for invalid_argument test
				ctxTenantID = selfServeTeamTenantID // Use a valid tenant for context
			}
			ctx := authorizedContext(ctxTenantID, authorizedUserID, "AUTH_R")

			req := &authpb.GetTenantPlanStatusRequest{
				TenantId: tc.tenantID,
			}

			resp, err := server.GetTenantPlanStatus(ctx, req)

			if tc.expectErr {
				require.Error(t, err, "Expected an error")
				st, ok := status.FromError(err)
				require.True(t, ok, "Error should be a gRPC status error")
				assert.Equal(t, tc.expectedErrCode, st.Code(), "Unexpected error code")
			} else {
				require.NoError(t, err, "Expected no error")
				require.NotNil(t, resp, "Response should not be nil")
				assert.Equal(t, tc.expectedResponse.IsPending, resp.IsPending, "IsPending mismatch")

				if tc.expectedResponse.PendingTargetPlanId != nil {
					require.NotNil(t, resp.PendingTargetPlanId, "PendingTargetPlanId should not be nil")
					assert.Equal(t, *tc.expectedResponse.PendingTargetPlanId, *resp.PendingTargetPlanId, "PendingTargetPlanId mismatch")
				} else {
					assert.Nil(t, resp.PendingTargetPlanId, "PendingTargetPlanId should be nil if expected nil")
				}

				if tc.expectedResponse.PlanChangeCreatedAt != nil {
					require.NotNil(t, resp.PlanChangeCreatedAt, "PlanChangeCreatedAt should not be nil")
					assert.True(t, tc.expectedResponse.PlanChangeCreatedAt.AsTime().Equal(resp.PlanChangeCreatedAt.AsTime()), "PlanChangeCreatedAt mismatch")
				} else {
					assert.Nil(t, resp.PlanChangeCreatedAt, "PlanChangeCreatedAt should be nil if expected nil")
				}
			}
		})
	}
}

func TestPaginatedListHelper(t *testing.T) {
	ctx := context.Background()

	t.Run("default page size", func(t *testing.T) {
		// Mock fetch function that returns predictable data
		fetchFunc := func(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.Subscription, string, error) {
			// Verify default page size is used
			assert.Equal(t, uint32(100), pageSize)
			assert.Equal(t, "", startKey)

			// Return some test data
			return []*auth_entities.Subscription{
				{SubscriptionId: "sub1"},
				{SubscriptionId: "sub2"},
			}, "next-token", nil
		}

		items, nextToken, err := paginatedListHelper(
			ctx,
			0, // Zero page size should use default
			"",
			100, // Default page size
			fetchFunc,
			"test error message",
		)

		require.NoError(t, err)
		assert.Len(t, items, 2)
		assert.Equal(t, "next-token", nextToken)
	})

	t.Run("custom page size", func(t *testing.T) {
		fetchFunc := func(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.Subscription, string, error) {
			// Verify custom page size is used
			assert.Equal(t, uint32(50), pageSize)
			assert.Equal(t, "", startKey)

			return []*auth_entities.Subscription{
				{SubscriptionId: "sub1"},
			}, "", nil
		}

		items, nextToken, err := paginatedListHelper(
			ctx,
			50, // Custom page size
			"",
			100,
			fetchFunc,
			"test error message",
		)

		require.NoError(t, err)
		assert.Len(t, items, 1)
		assert.Equal(t, "", nextToken)
	})

	t.Run("with page token", func(t *testing.T) {
		fetchFunc := func(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.Subscription, string, error) {
			// Verify page token is passed as start key
			assert.Equal(t, "previous-token", startKey)
			assert.Equal(t, uint32(25), pageSize)

			return []*auth_entities.Subscription{
				{SubscriptionId: "sub3"},
				{SubscriptionId: "sub4"},
			}, "another-token", nil
		}

		items, nextToken, err := paginatedListHelper(
			ctx,
			25,
			"previous-token", // Page token from previous request
			100,
			fetchFunc,
			"test error message",
		)

		require.NoError(t, err)
		assert.Len(t, items, 2)
		assert.Equal(t, "another-token", nextToken)
	})

	t.Run("fetch function error", func(t *testing.T) {
		fetchFunc := func(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.Subscription, string, error) {
			return nil, "", fmt.Errorf("database error")
		}

		items, nextToken, err := paginatedListHelper(
			ctx,
			10,
			"",
			100,
			fetchFunc,
			"Failed to fetch items",
		)

		require.Error(t, err)
		assert.Nil(t, items)
		assert.Equal(t, "", nextToken)
		assert.Equal(t, codes.Internal, status.Code(err))
		assert.Contains(t, err.Error(), "Failed to fetch items")
	})

	t.Run("empty results", func(t *testing.T) {
		fetchFunc := func(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.Subscription, string, error) {
			// Return empty slice
			return []*auth_entities.Subscription{}, "", nil
		}

		items, nextToken, err := paginatedListHelper(
			ctx,
			10,
			"",
			100,
			fetchFunc,
			"test error message",
		)

		require.NoError(t, err)
		assert.Empty(t, items)
		assert.Equal(t, "", nextToken)
	})

	t.Run("works with different types", func(t *testing.T) {
		// Test with TenantSubscriptionMapping type
		fetchFunc := func(ctx context.Context, startKey string, pageSize uint32) ([]*auth_entities.TenantSubscriptionMapping, string, error) {
			return []*auth_entities.TenantSubscriptionMapping{
				{TenantId: "tenant1"},
				{TenantId: "tenant2"},
			}, "mapping-token", nil
		}

		items, nextToken, err := paginatedListHelper(
			ctx,
			20,
			"",
			100,
			fetchFunc,
			"test error message",
		)

		require.NoError(t, err)
		assert.Len(t, items, 2)
		assert.Equal(t, "mapping-token", nextToken)
		assert.Equal(t, "tenant1", items[0].TenantId)
		assert.Equal(t, "tenant2", items[1].TenantId)
	})
}

local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local spec = {
    displayName: 'Auth Central Request Errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        sum by (namespace,cluster)(increase(flask_http_request_total{status=~"^50[0-9]$", pod=~"auth-central-.*"}[30m])) > 1
      |||,
    },
  };

  local errorsAtLoadBalancerSpec = {
    displayName: 'Auth Central 5xx measured at load balancer',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*auth-central.*",response_code_class="500"}[5m])) > 3
      |||,
    },
  };

  local parseErrorsSpec = {
    displayName: 'Auth0 Revoker parse errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(au_auth0_central_revoker_messages_parse_errors[60m])) > 3
      |||,
    },
  };

  local revokeUserErrorsSpec = {
    displayName: 'Auth0 Revoker revoke user errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(au_auth0_central_revoker_revoke_user_errors[60m])) > 2
      |||,
    },
  };

  local asyncOpsDeadLetterSpec = {
    displayName: 'Auth Central Async Operations Dead Letter Queue',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (subscription_id) (increase(pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~".*-auth-central-async-ops-.*deadletter-sub$",
        }[5m])) > 0
      |||,
    },
  };

  local stripeEventProcessorDeadLetterSpec = {
    displayName: 'Stripe Event Processor Dead Letter Queue (Prod)',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      query: |||
        sum by (subscription_id) (increase(pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~"central-auth-central-stripe-event-processor-.*-deadletter-sub$",
        }[5m])) > 0
      |||,
    },
  };

  local getUserBillingInfoErrorsSpec = {
    displayName: 'Get User Billing Info Error Rate',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        (sum(increase(au_auth_get_user_billing_info_total{status!="OK"}[10m]))
        / sum(increase(au_auth_get_user_billing_info_total[10m]))) > 0.1
      |||,
    },
  };

  local billingEventProcessorDeadLetterSpec = {
    displayName: 'Billing Event Processor Dead Letter Queue (Prod)',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '300s',
      labels: { severity: 'warning' },
      query: |||
        sum by (subscription_id) (increase(pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~"central-auth-central-billing-event-processor-.*-deadletter-sub$",
        }[5m])) > 0
      |||,
    },
  };

  local teamManagementStagingErrorRateSpec = {
    displayName: 'Team Management Staging Error Rate',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (grpc_method)(increase(grpc_server_handled_total{grpc_service="auth.TeamManagementService", grpc_code!="OK", namespace="central-staging"}[10m])) /
          (sum by (grpc_method)(increase(grpc_server_handled_total{grpc_service="auth.TeamManagementService", namespace="central-staging"}[10m]))) > 0.1
      |||,
    },
  };
  local teamManagementProdErrorRateSpec = {
    displayName: 'Team Management Prod Error Rate',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (grpc_method)(increase(grpc_server_handled_total{grpc_service="auth.TeamManagementService", grpc_code!="OK", namespace="central"}[10m])) /
          (sum by (grpc_method)(increase(grpc_server_handled_total{grpc_service="auth.TeamManagementService", namespace="central"}[10m]))) > 0.1
      |||,
    },
  };

  local usersWithMultipleTenantsSpec = {
    displayName: 'Users With Multiple Tenants Increased',
    conditionPrometheusQueryLanguage: {
      duration: '3600s',  // Alert if condition persists for 1 hour
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        # Alert if the current value is greater than the minimum value over the past 70 minutes
        # This detects an increase that persists for an hour
        au_auth_central_users_with_multiple_tenants > min_over_time(au_auth_central_users_with_multiple_tenants[70m])
      |||,
    },
  };

  local tenantCreationProdErrorCountSpec = {
    displayName: 'Tenant Creation Errors Count High',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum(increase(au_auth_central_tenant_creation_status_total{status="ERROR", namespace="central"}[10m])) > 0
      |||,
    },
  };

  local invitationResolutionProdErrorCountSpec = {
    displayName: 'Invitation Resolution Errors Count High',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum(increase(au_auth_central_invitation_resolution_status_total{status="ERROR", namespace="central"}[10m])) > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      spec,
      'auth-central-request-errors',
      'HTTP requests to auth central return errors'
    ),
    monitoringLib.alertPolicy(
      cloud,
      errorsAtLoadBalancerSpec,
      'auth-central-5xx-errors',
      '5xx errors measured at load balancer'
    ),
    monitoringLib.alertPolicy(
      cloud,
      parseErrorsSpec,
      'auth0-revoker-parse-errors',
      'Parse errors'
    ),
    monitoringLib.alertPolicy(
      cloud,
      revokeUserErrorsSpec,
      'auth0-revoker-revoke-user-errors',
      'Revoke user errors'
    ),
    monitoringLib.alertPolicy(
      cloud,
      asyncOpsDeadLetterSpec,
      'auth-central-async-ops-dead-letter',
      'Auth central async operations dead letter queue has messages',
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      stripeEventProcessorDeadLetterSpec,
      'auth-central-stripe-event-processor-dead-letter-prod',
      'Stripe event processor dead letter queue (prod) has messages',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      billingEventProcessorDeadLetterSpec,
      'auth-central-billing-event-processor-dead-letter-prod',
      'Billing event processor dead letter queue (prod) has messages',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      teamManagementStagingErrorRateSpec,
      'auth-central-team-management-staging-error-rate',
      'Team management staging error rate is high',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      teamManagementProdErrorRateSpec,
      'auth-central-team-management-prod-error-rate',
      'Team management prod error rate is high',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      getUserBillingInfoErrorsSpec,
      'auth-central-get-user-billing-info-errors',
      'Get User Billing Info Errors',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      usersWithMultipleTenantsSpec,
      'auth-central-users-with-multiple-tenants-increased',
      'Sustained increase in users with multiple tenants detected for more than an hour',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      tenantCreationProdErrorCountSpec,
      'auth-central-tenant-creation-error-count-high',
      'High number of tenant creation errors (>0 in 10 minutes)',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      invitationResolutionProdErrorCountSpec,
      'auth-central-invitation-resolution-error-count-high',
      'High number of invitation resolution errors (>0 in 10 minutes)',
      team='insights'
    ),
  ]

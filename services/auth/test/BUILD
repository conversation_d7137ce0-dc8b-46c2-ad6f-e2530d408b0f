load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    visibility = ["//services/auth/test:__subpackages__"],
    deps = [
        "//base/test_utils:bigtable_emulator",
        "//base/test_utils/pubsub:pubsub_emulator",
        "//services/auth/central/client:auth_client_py",
        "//services/auth/central/server:auth_central_test_setup",
        "//services/auth/query/server:auth_query_test_setup",
        "//services/tenant_watcher:tenant_watcher_py_proto",
        "//services/tenant_watcher/server:tenant_watcher_test_setup",
        "//services/token_exchange/server:token_exchange_test_setup",
        requirement("google-cloud-bigtable"),
        requirement("pytest"),
        requirement("requests"),
    ],
)

pytest_test(
    name = "tenant_test",
    srcs = [
        "tenant_test.py",
    ],
    deps = [
        ":conftest",
        "//services/auth/query:auth_query_py_proto",
        "//services/auth/query/server:auth_query_test_setup",
        requirement("requests"),
    ],
)

pytest_test(
    name = "individual_auth_test",
    srcs = [
        "individual_auth_test.py",
    ],
    deps = [
        ":conftest",
        "//services/auth/central/client:auth_client_py",
        "//services/auth/central/server:auth_central_test_setup",
        "//services/auth/query:auth_query_py_proto",
        requirement("pyjwt"),
        requirement("requests"),
    ],
)

py_binary(
    name = "launch",
    testonly = True,
    srcs = [
        "launch.py",
    ],
    main = "launch.py",
    visibility = ["//services/customer/frontend:__subpackages__"],
    deps = [
        ":conftest",
    ],
)

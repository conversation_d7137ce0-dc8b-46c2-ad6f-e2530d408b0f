"""Tests for the auth query service."""

import grpc

import jwt
import pytest
import requests

from services.auth.central.server import (
    auth_central_test_setup,
    auth_pb2,
    auth_entities_pb2,
    auth_pb2_grpc,
    bigtable_connector,
)
from services.auth.test.conftest import (
    VALID_CODE_CHALLENGE,
    VALID_CLIENT_ID,
    VALID_REDIRECT_URI,
    VALID_COMMUNITY_TENANT_ID,
    VALID_ENTERPRISE_TENANT_ID,
    VALID_PROFESSIONAL_TENANT_ID,
    VALID_SELF_SERVE_TENANT_ID,
    CUSTOMER_UI_CLIENT_ID,
)

VALID_USER_ID = "idp|123"


@pytest.fixture()
def auth_central_http_server(auth_central_http_server_raw):
    auth_central_http_server_raw.setup_test()
    yield auth_central_http_server_raw


@pytest.mark.timeout(10)
def test_auth_central_health_check(
    auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
):
    health_response = requests.get(auth_central_http_server.url("health"))
    assert health_response.text == "Ok"


@pytest.mark.timeout(30)
def test_list_users(
    auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
    request_context,
    request,
):
    # Use a new email per test case to avoid clashes between different
    # parametrized runs of this same test case
    test_name = request.node.name
    random_email = "<EMAIL>" % test_name

    listUsersResponse: auth_pb2.ListTenantUsersResponse = (
        auth_central_grpc_client.ListTenantUsers(
            auth_pb2.ListTenantUsersRequest(tenant_id=VALID_ENTERPRISE_TENANT_ID),
            metadata=request_context.to_metadata(),
        )
    )

    def find_user_by_email(users, email) -> auth_entities_pb2.User | None:
        return next(filter(lambda u: u.email == email, users), None)

    orig_len = len(listUsersResponse.users)
    assert find_user_by_email(listUsersResponse.users, random_email) is None

    respone = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
        ),
        metadata=request_context.to_metadata(),
    )
    print(
        "User %s added to tenant %s with id %s"
        % (random_email, VALID_ENTERPRISE_TENANT_ID, respone.user.id)
    )
    listUsersResponse: auth_pb2.ListTenantUsersResponse = (
        auth_central_grpc_client.ListTenantUsers(
            auth_pb2.ListTenantUsersRequest(tenant_id=VALID_ENTERPRISE_TENANT_ID),
            metadata=request_context.to_metadata(),
        )
    )
    assert len(listUsersResponse.users) == orig_len + 1, "User not added"
    found_user = find_user_by_email(listUsersResponse.users, random_email)
    assert found_user is not None, "User %s not found" % random_email

    # remove user => don't see user
    auth_central_grpc_client.RemoveUserFromTenant(
        auth_pb2.RemoveUserFromTenantRequest(
            user_id=found_user.id,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
        ),
        metadata=request_context.to_metadata(),
    )
    listUsersResponse: auth_pb2.ListTenantUsersResponse = (
        auth_central_grpc_client.ListTenantUsers(
            auth_pb2.ListTenantUsersRequest(tenant_id=VALID_ENTERPRISE_TENANT_ID),
            metadata=request_context.to_metadata(),
        )
    )
    assert len(listUsersResponse.users) == orig_len, "User not removed"
    assert find_user_by_email(listUsersResponse.users, random_email) is None


@pytest.mark.timeout(30)
def test_individual_auth_support(
    auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
    auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
    request_context,
    request,
):
    # Use a new email per test case to avoid clashes between different
    # parametrized runs of this same test case
    test_name = request.node.name
    random_email = "<EMAIL>" % test_name

    # Client cannot log in
    response: requests.Response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", "feedface"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": random_email,
            "sub": VALID_USER_ID,
        },
    )
    assert "Different Account" in response.text

    auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
        ),
        metadata=request_context.to_metadata(),
    )

    response: requests.Response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", "feedface"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": random_email,
            "sub": VALID_USER_ID,
        },
    )
    assert "Different Account" not in response.text
    _, qs = auth_central_http_server.get_redirect_url_from_authorize_response(response)
    code = qs["code"][0]

    response = auth_central_http_server.token(
        client_id=VALID_CLIENT_ID,
        redirect_uri=VALID_REDIRECT_URI,
        code=code,
    )

    access_token = response.json()["access_token"]

    token_info: auth_pb2.GetTokenInfoResponse = auth_central_grpc_client.GetTokenInfo(
        auth_pb2.GetTokenInfoRequest(token=access_token),
        metadata=request_context.to_metadata(),
    )
    assert token_info.user_id == random_email

    auth_token_info: auth_pb2.GetTokenInfoResponse = (
        auth_central_grpc_client.GetTokenInfo(
            auth_pb2.GetTokenInfoRequest(token=access_token),
            metadata=request_context.to_metadata(),
        )
    )
    assert auth_token_info.user_id == random_email

    # now lets delete
    auth_central_grpc_client.RevokeUser(
        auth_pb2.RevokeUserRequest(
            email=random_email, tenant_id=VALID_ENTERPRISE_TENANT_ID
        ),
        metadata=request_context.to_metadata(),
    )

    # There is a cache in auth query for tokens. Need to invalidate the cache or check with auth central directly.
    with pytest.raises(grpc.RpcError) as e:
        auth_token_info: auth_pb2.GetTokenInfoResponse = (
            auth_central_grpc_client.GetTokenInfo(
                auth_pb2.GetTokenInfoRequest(token=access_token),
                metadata=request_context.to_metadata(),
            )
        )
        print(f"Unexpected token info returned: {auth_token_info}")
    assert e.value.code() == grpc.StatusCode.NOT_FOUND  # type: ignore


@pytest.mark.timeout(30)
def test_customer_ui_authentication(
    auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
    auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
    request_context,
    request,
    client_config_map,
):
    # Use a new email per test case to avoid clashes between different
    # parametrized runs of this same test case
    test_name = request.node.name
    random_email = "<EMAIL>" % test_name

    add_user_response: auth_pb2.AddUserToTenantResponse = (
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=random_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )
    )
    user_id = add_user_response.user.id

    auth_central_grpc_client.UpdateUserOnTenant(
        auth_pb2.UpdateUserOnTenantRequest(
            user_id=user_id,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
            customer_ui_roles=[auth_entities_pb2.CustomerUiRole.ADMIN],
        ),
        metadata=request_context.to_metadata(),
    )

    redirect_uri = client_config_map[CUSTOMER_UI_CLIENT_ID].redirect_uris[0]

    response: requests.Response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", CUSTOMER_UI_CLIENT_ID),
            ("redirect_uri", redirect_uri),
            ("state", "feedface"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": random_email,
            "sub": VALID_USER_ID,
        },
    )

    _, qs = auth_central_http_server.get_redirect_url_from_dummy_server_response(
        response
    )
    code = qs["code"][0]

    response = auth_central_http_server.token(
        client_id=CUSTOMER_UI_CLIENT_ID,
        redirect_uri=redirect_uri,
        code=code,
    )

    response_json = response.json()

    payload = jwt.decode(
        response_json["access_token"], options={"verify_signature": False}
    )
    assert payload["additional_claims"]["roles"] == ["ADMIN"]
    assert payload["additional_claims"]["email"] == random_email


@pytest.mark.timeout(30)
def test_suspend_enterprise_user_not_allowed(
    auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
    request_context,
    request,
):
    """Test suspending an enterprise user is not allowed via the GRPC API."""
    # Use a new email per test case to avoid clashes
    test_name = request.node.name
    random_email = "<EMAIL>" % test_name

    # Add user to tenant
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
        ),
        metadata=request_context.to_metadata(),
    )

    user_id = add_response.user.id

    # Verify user is not suspended
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
        ),
        metadata=request_context.to_metadata(),
    )
    assert (
        not get_response.user.suspensions
    ), "Enterprise user should not have any suspensions"
    assert not get_response.user.blocked, "Enterprise user should not be blocked"

    # Suspending an enterprise tenant user should fail
    with pytest.raises(grpc.RpcError) as e:
        auth_central_grpc_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
                evidence="Test evidence",
            ),
            metadata=request_context.to_metadata(),
        )

    assert (
        e.value.code()  # pylint: disable=no-member # type: ignore
        == grpc.StatusCode.INVALID_ARGUMENT
    )


@pytest.mark.timeout(30)
def test_add_remove_user_suspension(
    auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
    auth_central_bigtable,
    request_context_professional,
    admin_request_context,
    request,
):
    """Test adding and removing a user suspension via the GRPC API."""
    # Use a new email per test case to avoid clashes
    test_name = request.node.name
    random_email = "<EMAIL>" % test_name
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    user_id = add_response.user.id

    # Create a simplified trial Orb subscription
    orb_subscription_id = f"orb_sub_trial_{user_id}"
    orb_customer_id = f"orb_cus_trial_{user_id}"

    # Create a minimal Orb subscription object with just the required fields
    subscription = auth_entities_pb2.Subscription(
        subscription_id=orb_subscription_id,
        orb_customer_id=orb_customer_id,
        orb_status=auth_entities_pb2.Subscription.OrbStatus.ORB_STATUS_ACTIVE,
        external_plan_id="orb_trial_plan",  # Set to trial plan ID
        billing_method=auth_entities_pb2.BillingMethod.BILLING_METHOD_ORB,
        tenant_id=VALID_PROFESSIONAL_TENANT_ID,
    )

    # Connect to BigTable and write the subscription
    table = bigtable_connector.connect(
        instance_id=auth_central_bigtable.instance,
        table_name=auth_central_bigtable.table_name,
        project_id=auth_central_bigtable.project,
    )

    # Write subscription directly
    subscription_row_key = f"Subscription#{orb_subscription_id}"
    subscription_data = subscription.SerializeToString()
    subscription_row = table.direct_row(subscription_row_key)
    subscription_row.set_cell("Subscription", "value", subscription_data)
    subscription_row.commit()

    # Update user with Orb subscription ID
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )

    user_proto = get_response.user
    user_proto.orb_subscription_id = orb_subscription_id
    user_proto.orb_customer_id = orb_customer_id

    # Write updated user
    user_row_key = f"User#{user_id}"
    user_data = user_proto.SerializeToString()
    user_row = table.direct_row(user_row_key)
    user_row.set_cell("User", "value", user_data)
    user_row.commit()

    # Verify user now has an Orb subscription
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert (
        get_response.user.orb_subscription_id == orb_subscription_id
    ), "User should have the trial Orb subscription"

    # Verify user is not suspended initially
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert (
        not get_response.user.suspensions
    ), "User should not have any suspensions initially"

    # Create a suspension
    create_response = auth_central_grpc_client.CreateUserSuspension(
        auth_pb2.CreateUserSuspensionRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
            evidence="Test evidence",
        ),
        metadata=request_context_professional.to_metadata(),
    )

    # Verify the suspension exists
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert len(get_response.user.suspensions) == 1, "User should have one suspension"
    assert (
        get_response.user.suspensions[0].suspension_id == create_response.suspension_id
    )
    assert (
        get_response.user.suspensions[0].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE
    )

    # Delete the suspension
    delete_response = auth_central_grpc_client.DeleteUserSuspensions(
        auth_pb2.DeleteUserSuspensionsRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            suspension_ids=[create_response.suspension_id],
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert (
        delete_response.suspensions_deleted == 1
    ), "Should have deleted one suspension"

    # Verify the suspension is gone
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert not get_response.user.suspensions, "User should not have any suspensions"

    # Suspend the user with an API abuse suspension
    create_response = auth_central_grpc_client.CreateUserSuspension(
        auth_pb2.CreateUserSuspensionRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE,
            evidence="Test evidence",
        ),
        metadata=request_context_professional.to_metadata(),
    )

    # Verify the suspension exists
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert len(get_response.user.suspensions) == 1, "User should have one suspension"
    assert (
        get_response.user.suspensions[0].suspension_id == create_response.suspension_id
    )
    assert (
        get_response.user.suspensions[0].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
    )

    # Add another API abuse suspension and a free trial suspension
    create_response = auth_central_grpc_client.CreateUserSuspension(
        auth_pb2.CreateUserSuspensionRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE,
            evidence="Test evidence",
        ),
        metadata=request_context_professional.to_metadata(),
    )
    create_response = auth_central_grpc_client.CreateUserSuspension(
        auth_pb2.CreateUserSuspensionRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
            evidence="Test evidence",
        ),
        metadata=request_context_professional.to_metadata(),
    )

    # Verify multiple suspensions exist: two API abuse and one free trial
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert len(get_response.user.suspensions) == 3, "User should have three suspensions"
    assert (
        get_response.user.suspensions[0].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
    )
    assert (
        get_response.user.suspensions[1].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
    )
    assert (
        get_response.user.suspensions[2].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE
    )

    # Invoke cleanup suspension job to remove tree trial suspensions
    cleanup_response = auth_central_grpc_client.SuspensionCleanup(
        auth_pb2.SuspensionCleanupRequest(
            remove_suspension_types=[
                auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE
            ],
        ),
        metadata=admin_request_context.to_metadata(),
    )
    assert (
        cleanup_response.suspensions_removed == 1
    ), "Should have removed one suspension"

    # Verify the free trial suspension is gone
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert len(get_response.user.suspensions) == 2, "User should have two suspensions"
    assert (
        get_response.user.suspensions[0].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
    )
    assert (
        get_response.user.suspensions[1].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
    )

    # Invoke cleanup suspension job to dedup API abuse suspensions
    cleanup_response = auth_central_grpc_client.SuspensionCleanup(
        auth_pb2.SuspensionCleanupRequest(
            dedup_suspension_types=[
                auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
            ],
        ),
        metadata=admin_request_context.to_metadata(),
    )
    assert (
        cleanup_response.suspensions_deduped == 1
    ), "Should have deduped one suspension"

    # Verify the API abuse suspension is deduped
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert len(get_response.user.suspensions) == 1, "User should have one suspensions"
    assert (
        get_response.user.suspensions[0].suspension_type
        == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
    )

    # Set suspension exemption
    update_response = auth_central_grpc_client.UpdateSuspensionExemption(
        auth_pb2.UpdateSuspensionExemptionRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            exempt=True,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert update_response.exempt, "User should be exempt from suspensions"

    # Attempt to suspend again. Should fail due to suspension exemption
    with pytest.raises(grpc.RpcError) as e:
        auth_central_grpc_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id,
                tenant_id=VALID_PROFESSIONAL_TENANT_ID,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE,
                evidence="Test evidence",
            ),
            metadata=request_context_professional.to_metadata(),
        )
        assert (
            e.value.code()  # pylint: disable=no-member # type: ignore
            == grpc.StatusCode.INVALID_ARGUMENT
        )
        assert "User is exempt from suspensions" in e.value.details()  # type: ignore

    # Remove suspension exemption
    update_response = auth_central_grpc_client.UpdateSuspensionExemption(
        auth_pb2.UpdateSuspensionExemptionRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            exempt=False,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert not update_response.exempt, "User should not be exempt from suspensions"

    # Suspend again. Should succeed.
    create_response = auth_central_grpc_client.CreateUserSuspension(
        auth_pb2.CreateUserSuspensionRequest(
            user_id=user_id,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
            suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE,
            evidence="Test evidence",
        ),
        metadata=request_context_professional.to_metadata(),
    )
    assert create_response.suspension_id, "Should have created a suspension"


@pytest.mark.timeout(30)
def test_free_trial_abuse_suspension_validation(
    auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
    auth_central_bigtable,
    request_context,
    request_context_professional,
    request_context_self_serve,
    admin_request_context,
    request,
):
    """Test validation rules for free trial abuse suspensions."""
    test_name = request.node.name

    # Test 1: Cannot suspend user with non-trialing subscription
    random_email_1 = f"t-suspend-validation-1-{test_name}@augmentcode.com"
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email_1,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    user_id_1 = add_response.user.id

    # Create active (non-trial) subscription
    orb_subscription_id_1 = f"orb_sub_active_{user_id_1}"
    subscription_1 = auth_entities_pb2.Subscription(
        subscription_id=orb_subscription_id_1,
        orb_customer_id=f"orb_cus_{user_id_1}",
        orb_status=auth_entities_pb2.Subscription.OrbStatus.ORB_STATUS_ACTIVE,
        external_plan_id="orb_developer_plan",  # Not trialing
        billing_method=auth_entities_pb2.BillingMethod.BILLING_METHOD_ORB,
        tenant_id=VALID_PROFESSIONAL_TENANT_ID,
    )

    # Write subscription and update user
    table = bigtable_connector.connect(
        instance_id=auth_central_bigtable.instance,
        table_name=auth_central_bigtable.table_name,
        project_id=auth_central_bigtable.project,
    )

    subscription_row_key = f"Subscription#{orb_subscription_id_1}"
    subscription_row = table.direct_row(subscription_row_key)
    subscription_row.set_cell(
        "Subscription", "value", subscription_1.SerializeToString()
    )
    subscription_row.commit()

    # Update user with subscription
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id_1, tenant_id=VALID_PROFESSIONAL_TENANT_ID
        ),
        metadata=request_context_professional.to_metadata(),
    )
    user_proto = get_response.user
    user_proto.orb_subscription_id = orb_subscription_id_1
    user_row = table.direct_row(f"User#{user_id_1}")
    user_row.set_cell("User", "value", user_proto.SerializeToString())
    user_row.commit()

    # Should fail - subscription is not trialing
    with pytest.raises(grpc.RpcError) as e:
        auth_central_grpc_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id_1,
                tenant_id=VALID_PROFESSIONAL_TENANT_ID,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
                evidence="Test evidence",
            ),
            metadata=admin_request_context.to_metadata(),
        )
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "Subscription is not on the trial plan" in e.value.details()

    # Test 2: Cannot suspend user in self-serve team tenant
    random_email_2 = f"t-suspend-validation-2-{test_name}@augmentcode.com"
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email_2,
            tenant_id=VALID_SELF_SERVE_TENANT_ID,  # Self-serve team tenant
        ),
        metadata=request_context_self_serve.to_metadata(),
    )
    user_id_2 = add_response.user.id

    # Should fail - user is in self-serve team tenant
    with pytest.raises(grpc.RpcError) as e:
        auth_central_grpc_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id_2,
                tenant_id=VALID_SELF_SERVE_TENANT_ID,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
                evidence="Test evidence",
            ),
            metadata=admin_request_context.to_metadata(),
        )
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "Can only suspend professional or community user" in e.value.details()

    # Test 3: Cannot suspend user with trial subscription that has payment method
    random_email_3 = f"t-suspend-validation-3-{test_name}@augmentcode.com"
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email_3,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    user_id_3 = add_response.user.id

    # Create trial subscription with payment method
    orb_subscription_id_3 = f"orb_sub_trial_payment_{user_id_3}"
    subscription_3 = auth_entities_pb2.Subscription(
        subscription_id=orb_subscription_id_3,
        orb_customer_id=f"orb_cus_{user_id_3}",
        orb_status=auth_entities_pb2.Subscription.OrbStatus.ORB_STATUS_ACTIVE,
        billing_method=auth_entities_pb2.BillingMethod.BILLING_METHOD_ORB,
        tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        has_payment_method=True,  # Has payment method
        external_plan_id="orb_trial_plan",  # Set to trial plan ID
    )

    subscription_row_key = f"Subscription#{orb_subscription_id_3}"
    subscription_row = table.direct_row(subscription_row_key)
    subscription_row.set_cell(
        "Subscription", "value", subscription_3.SerializeToString()
    )
    subscription_row.commit()

    # Update user with subscription
    get_response = auth_central_grpc_client.GetUser(
        auth_pb2.GetUserRequest(
            user_id=user_id_3, tenant_id=VALID_PROFESSIONAL_TENANT_ID
        ),
        metadata=request_context_professional.to_metadata(),
    )
    user_proto = get_response.user
    user_proto.orb_subscription_id = orb_subscription_id_3
    user_row = table.direct_row(f"User#{user_id_3}")
    user_row.set_cell("User", "value", user_proto.SerializeToString())
    user_row.commit()

    # Should fail - user has payment method configured
    with pytest.raises(grpc.RpcError) as e:
        auth_central_grpc_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id_3,
                tenant_id=VALID_PROFESSIONAL_TENANT_ID,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
                evidence="Test evidence",
            ),
            metadata=admin_request_context.to_metadata(),
        )
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "User has a payment method configured" in e.value.details()

    # Test 4: Cannot suspend an enterprise tenant user
    random_email_4 = f"t-suspend-validation-4-{test_name}@augmentcode.com"
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email_4,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
        ),
        metadata=request_context.to_metadata(),
    )
    user_id_4 = add_response.user.id

    # Should fail - user is in enterprise tenant
    with pytest.raises(grpc.RpcError) as e:
        auth_central_grpc_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id_4,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
                evidence="Test evidence",
            ),
            metadata=admin_request_context.to_metadata(),
        )
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "Can only suspend professional or community user" in e.value.details()


@pytest.mark.timeout(30)
def test_community_abuse_suspension(
    auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
    auth_central_bigtable,
    request_context_community,
    request_context_professional,
    admin_request_context,
    request,
):
    """Test applying a community abuse suspension."""
    # Use a new email per test case to avoid clashes
    test_name = request.node.name
    random_email = "<EMAIL>" % test_name
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email,
            tenant_id=VALID_COMMUNITY_TENANT_ID,
        ),
        metadata=request_context_community.to_metadata(),
    )
    user_id = add_response.user.id
    assert user_id, "Should have created a user"

    # Should succeed - community user
    create_response = auth_central_grpc_client.CreateUserSuspension(
        auth_pb2.CreateUserSuspensionRequest(
            user_id=user_id,
            tenant_id=VALID_COMMUNITY_TENANT_ID,
            suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_COMMUNITY_ABUSE,
            evidence="Test evidence",
        ),
        metadata=admin_request_context.to_metadata(),
    )
    assert create_response.suspension_id, "Should have created a suspension"

    # Cannot suspend a non-community user with a community abuse suspension
    random_email_2 = "<EMAIL>" % test_name
    add_response = auth_central_grpc_client.AddUserToTenant(
        auth_pb2.AddUserToTenantRequest(
            email=random_email_2,
            tenant_id=VALID_PROFESSIONAL_TENANT_ID,
        ),
        metadata=request_context_professional.to_metadata(),
    )
    user_id_2 = add_response.user.id
    assert user_id_2, "Should have created a user"

    with pytest.raises(grpc.RpcError) as e:
        auth_central_grpc_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id_2,
                tenant_id=VALID_PROFESSIONAL_TENANT_ID,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_COMMUNITY_ABUSE,
                evidence="Test evidence",
            ),
            metadata=admin_request_context.to_metadata(),
        )
    assert e.value.code() == grpc.StatusCode.INVALID_ARGUMENT
    assert "Can only suspend community user" in e.value.details()

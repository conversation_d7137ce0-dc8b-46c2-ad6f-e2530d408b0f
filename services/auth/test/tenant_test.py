"""Tests for user session tokens."""

import html
import re
from urllib.parse import parse_qs, urlparse

import grpc
import pytest
import requests

from services.auth.central.server import auth_pb2

from services.auth.test.conftest import (
    CUSTOMER_UI_CLIENT_ID,
    SAMPLE_TENANT_ID,
    VALID_CLIENT_ID,
    VALID_LOCALHOST_CLIENT_ID,
    VALID_LOCALHOST_REDIRECT_URI,
    VALID_REDIRECT_URI,
    VALID_ENTERPRISE_TENANT_ID,
    VALID_VIM_CLIENT_ID,
    VALID_VIM_SHORT_CLIENT_ID,
    VALID_VIM_REDIRECT_URI,
)

STATE = "feedface"
VALID_EMAIL = "<EMAIL>"
VALID_USER_ID = "idp|123"
VALID_CODE_VERIFIER = "9683e0859a76cde1e20898885833ea4d90f0b48adcec16f751405d59e509f8b0"
VALID_CODE_CHALLENGE = "eqEvNh2v4-XpxstYZNyL0t2YwSjxTdRQFObvgMTestU"


@pytest.fixture()
def auth_central_http_server(request, auth_central_http_server_raw):
    auth_central_http_server_raw.setup_test()

    yield auth_central_http_server_raw


def test_auth_central_no_token(auth_central_grpc_client, request_context):
    with pytest.raises(grpc.RpcError) as e:
        _ = auth_central_grpc_client.GetTokenInfo(
            auth_pb2.GetTokenInfoRequest(token="no-such-token"),
            metadata=request_context.to_metadata(),
        )

    assert (
        e.value.code()  # pylint: disable=no-member # type: ignore
        == grpc.StatusCode.NOT_FOUND
    )


def test_missing_client_id(auth_central_http_server):
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("response_type", "code"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200

    # RFC 6749, section 4.1.2.1:
    #   If the request fails due to a missing, invalid, or mismatching
    #   redirection URI, or if the client identifier is missing or invalid,
    #   the authorization server SHOULD inform the resource owner of the
    #   error and MUST NOT automatically redirect the user-agent to the
    #   invalid redirection URI.
    assert "invalid_request" in response.text
    assert VALID_REDIRECT_URI not in response.text


def test_missing_redirect_uri(auth_central_http_server):
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("client_id", VALID_CLIENT_ID),
            ("state", STATE),
            ("response_type", "code"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200

    # RFC 6749, section 4.1.2.1:
    #   If the request fails due to a missing, invalid, or mismatching
    #   redirection URI, or if the client identifier is missing or invalid,
    #   the authorization server SHOULD inform the resource owner of the
    #   error and MUST NOT automatically redirect the user-agent to the
    #   invalid redirection URI.
    assert "invalid_request" in response.text
    assert VALID_REDIRECT_URI not in response.text


def test_unregistered_client_id(auth_central_http_server):
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("response_type", "code"),
            ("client_id", "not-a-valid-client-id"),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert "unauthorized_client" in response.text
    assert VALID_REDIRECT_URI not in response.text


def test_wrong_redirect_uri(auth_central_http_server):
    bogus_redirect_uri = "vscode://augment.vscode-augment/auth/result/bogus"
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", bogus_redirect_uri),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert VALID_REDIRECT_URI not in response.text
    assert bogus_redirect_uri not in response.text
    assert "invalid_request" in response.text


def test_wrong_redirect_uri_prioritized_first(auth_central_http_server):
    """Tests a security concern (au-3704) an invalid redirect_uri must not appear in the response.
    In this edge case - we remove a required argument. (state)
    The expectation is that even though there's a required argument missing, we will fail the request
    on the redirect_uri as it is prioritized higher than the missing required argument.
    """

    bogus_redirect_uri = "vscode://augment.vscode-augment/auth/result/bogus"
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", bogus_redirect_uri),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert VALID_REDIRECT_URI not in response.text
    assert "redirect uri is incorrect" in response.text
    assert "invalid_request" in response.text


def assert_redirect_uri_query_args(response, query_args):
    m = re.search(r'href="(vscode[^"]*)', response.text)
    assert m
    qs = parse_qs(urlparse(html.unescape(m.group(1))).query, strict_parsing=True)
    for k, v in query_args:
        assert k in qs
        assert qs[k] == [v]


def test_missing_state(auth_central_http_server):
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("response_type", "code"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert_redirect_uri_query_args(
        response,
        [
            ("error", "invalid_request"),
        ],
    )


def test_missing_response_type(auth_central_http_server):
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert_redirect_uri_query_args(
        response,
        [
            ("error", "invalid_request"),
            ("state", STATE),
        ],
    )


def test_invalid_response_type(auth_central_http_server):
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("response_type", "token"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert_redirect_uri_query_args(
        response,
        [
            ("error", "unsupported_response_type"),
            ("state", STATE),
        ],
    )


def test_unauthorized_user(auth_central_http_server):
    response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": "<EMAIL>",  # User doesn't exist
            "sub": VALID_USER_ID,
        },
    )
    assert response.status_code == 401


@pytest.mark.parametrize(
    "user_id",
    [
        "idp123",
        "bogusidp|123",
    ],
)
def test_user_ids_that_should_fail(auth_central_http_server, user_id):
    response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": VALID_EMAIL,
            "sub": user_id,
        }
        if user_id is not None
        else {},
    )
    assert response.status_code == 401


def test_missing_user_email(auth_central_http_server):
    response = auth_central_http_server.accept(
        authorize_params=[
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("response_type", "code"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "sub": VALID_USER_ID,
        },
    )
    assert response.status_code == 401


def test_invalid_code_challenge_method(auth_central_http_server):
    # We don't support plain code challenges.
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("response_type", "code"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "plain"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert_redirect_uri_query_args(
        response,
        [
            ("error", "invalid_request"),
            ("state", STATE),
        ],
    )


def test_code_challenge_too_long(auth_central_http_server):
    response = requests.get(
        auth_central_http_server.url("/authorize"),
        params=[
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("response_type", "code"),
            ("code_challenge", "a" * 100),
            ("code_challenge_method", "S256"),
        ],
        timeout=5,
    )
    assert response.status_code == 200
    assert_redirect_uri_query_args(
        response,
        [
            ("error", "invalid_request"),
            ("state", STATE),
        ],
    )


def _setup_for_post(auth_central_http_server):
    response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": VALID_EMAIL,
            "sub": VALID_USER_ID,
        },
    )
    assert response.status_code == 200
    url = re.search(r'href="(vscode[^"]*)', response.text)
    assert url
    url = html.unescape(url.group(1))
    return parse_qs(urlparse(url).query, strict_parsing=True)["code"][0]


def test_post_without_client_id(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_request"


def test_post_without_grant_type(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_request"


def test_post_without_redirect_uri(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_request"


def test_post_without_code(auth_central_http_server):
    _ = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_request"


def test_post_without_code_verifier(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)
    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_request"


def test_post_with_bogus_code(auth_central_http_server):
    _ = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": "bogus",
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_grant"


def test_post_with_code_reuse(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json()

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_grant"


def test_post_with_bogus_client_id(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "client_id": "bogus",
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_grant"


def test_post_with_bogus_redirect_uri(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": "bogus",
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_grant"


def test_post_with_bogus_code_verifier(auth_central_http_server):
    code = _setup_for_post(auth_central_http_server)

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "grant_type": "authorization_code",
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
            "code_verifier": VALID_CODE_VERIFIER.replace("a", "b"),
        },
    )
    assert response.status_code == 400
    assert "access_token" not in response.json()
    assert response.json()["error"] == "invalid_grant"


def test_new_token(auth_central_http_server, auth_central_grpc_client, request_context):
    """Test that every sign in gets a new token."""

    first_token = None
    for _ in range(2):
        response = auth_central_http_server.accept(
            authorize_params=[
                ("response_type", "code"),
                ("client_id", VALID_CLIENT_ID),
                ("redirect_uri", VALID_REDIRECT_URI),
                ("state", STATE),
                ("code_challenge", VALID_CODE_CHALLENGE),
                ("code_challenge_method", "S256"),
            ],
            userinfo={
                "email": VALID_EMAIL,
                "sub": VALID_USER_ID,
            },
        )
        url = re.search(r'href="(vscode[^"]*)', response.text)
        parts = urlparse(html.unescape(url.group(1)))
        query = parse_qs(parts.query, strict_parsing=True)
        code = query["code"][0]

        response = requests.post(
            auth_central_http_server.url("token"),
            json={
                "code": code,
                "client_id": VALID_CLIENT_ID,
                "redirect_uri": VALID_REDIRECT_URI,
                "grant_type": "authorization_code",
                "code_verifier": VALID_CODE_VERIFIER,
            },
        )

        assert response.status_code == 200
        access_token = response.json()["access_token"]

        if first_token is None:
            assert access_token is not None
            first_token = access_token
        else:
            assert access_token != first_token

        response = auth_central_grpc_client.GetTokenInfo(
            auth_pb2.GetTokenInfoRequest(token=access_token),
            metadata=request_context.to_metadata(),
        )
        assert response.user_id == VALID_EMAIL
        assert response.tenant_id == VALID_ENTERPRISE_TENANT_ID


@pytest.mark.parametrize(
    "email, user_id, tenant_id",
    [
        (VALID_EMAIL, VALID_USER_ID, VALID_ENTERPRISE_TENANT_ID),
        ("<EMAIL>", VALID_USER_ID, VALID_ENTERPRISE_TENANT_ID),
        ("<EMAIL>", VALID_USER_ID, VALID_ENTERPRISE_TENANT_ID),
        # sample.com doesn't care which identity provider
        ("<EMAIL>", "otheridp|345", SAMPLE_TENANT_ID),
    ],
)
def test_happy_path(
    auth_central_grpc_client,
    auth_central_http_server,
    request_context,
    email,
    user_id,
    tenant_id,
):
    """Test the happy path."""
    response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", VALID_CLIENT_ID),
            ("redirect_uri", VALID_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": email,
            "sub": user_id,
        },
    )
    assert response.status_code == 200
    url = re.search(r'href="(vscode[^"]*)', response.text)
    assert url
    parts = urlparse(html.unescape(url.group(1)))
    assert parts.scheme == "vscode"
    assert parts.netloc == "augment.vscode-augment"
    assert not parts.params
    assert parts.path == "/auth/result"
    assert not parts.fragment
    assert parts.query is not None

    query = parse_qs(parts.query, strict_parsing=True)
    assert query["state"] == [STATE]
    assert query["tenant_url"] == ["https://augment.t.augmentcode.com/"]
    assert query["code"][0] is not None

    code = query["code"][0]

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "code": code,
            "client_id": VALID_CLIENT_ID,
            "redirect_uri": VALID_REDIRECT_URI,
            "grant_type": "authorization_code",
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )

    assert response.status_code == 200
    access_token = response.json()["access_token"]

    response = auth_central_grpc_client.GetTokenInfo(
        auth_pb2.GetTokenInfoRequest(token=access_token),
        metadata=request_context.to_metadata(),
    )
    assert response.user_id == email
    assert response.tenant_id == tenant_id


def test_intellij_happy_path(
    auth_central_grpc_client, auth_central_http_server, request_context
):
    """Test the happy path with a localhost URI."""

    response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", VALID_LOCALHOST_CLIENT_ID),
            ("redirect_uri", VALID_LOCALHOST_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": VALID_EMAIL,
            "sub": VALID_USER_ID,
        },
    )
    assert response.status_code == 200
    print(response.text)
    url = re.search(r'"redirect_url" href="([^"]*)', response.text)
    assert url
    parts = urlparse(html.unescape(url.group(1)))
    assert parts.scheme == "http"
    assert parts.netloc == "127.0.0.1:1234"
    assert not parts.params
    assert parts.path == "/api/augment/auth/result"
    assert not parts.fragment
    assert parts.query is not None

    query = parse_qs(parts.query, strict_parsing=True)
    assert query["state"] == [STATE]
    assert query["tenant_url"] == ["https://augment.t.augmentcode.com/"]
    assert query["code"][0] is not None

    code = query["code"][0]

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "code": code,
            "client_id": VALID_LOCALHOST_CLIENT_ID,
            "redirect_uri": VALID_LOCALHOST_REDIRECT_URI,
            "grant_type": "authorization_code",
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )

    assert response.status_code == 200
    access_token = response.json()["access_token"]

    response = auth_central_grpc_client.GetTokenInfo(
        auth_pb2.GetTokenInfoRequest(token=access_token),
        metadata=request_context.to_metadata(),
    )
    assert response.user_id == VALID_EMAIL
    assert response.tenant_id == VALID_ENTERPRISE_TENANT_ID


@pytest.mark.parametrize("client_id", [VALID_VIM_CLIENT_ID, VALID_VIM_SHORT_CLIENT_ID])
def test_happy_path_vim(
    auth_central_http_server,
    auth_central_grpc_client,
    request_context,
    client_id,
):
    """Test the happy path for the vim client."""

    response = auth_central_http_server.accept(
        authorize_params=[
            ("response_type", "code"),
            ("client_id", client_id),
            ("redirect_uri", VALID_VIM_REDIRECT_URI),
            ("state", STATE),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": VALID_EMAIL,
            "sub": VALID_USER_ID,
        },
    )
    assert response.status_code == 200

    # The html is expected to contain strings for code, state, and tenant_url.
    # Extracting the fields in this way bypasses the json encoding of the entire
    # string, unfortunately.
    code_ref = re.search(r"code: \"(.+)\"", str(response.text))
    state_ref = re.search(r"state: \"(.+)\"", str(response.text))
    tenant_ref = re.search(r"tenant_url: \"(.+)\"", str(response.text))
    assert code_ref
    assert state_ref
    assert tenant_ref

    code = code_ref.group(1)
    state = state_ref.group(1)
    tenant_url = tenant_ref.group(1)
    assert code.startswith("_")
    assert state == STATE
    assert tenant_url

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "code": code,
            "client_id": client_id,
            "redirect_uri": VALID_VIM_REDIRECT_URI,
            "grant_type": "authorization_code",
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )

    assert response.status_code == 200
    access_token = response.json()["access_token"]

    response = auth_central_grpc_client.GetTokenInfo(
        auth_pb2.GetTokenInfoRequest(token=access_token),
        metadata=request_context.to_metadata(),
    )
    assert response.user_id == VALID_EMAIL
    assert response.tenant_id == VALID_ENTERPRISE_TENANT_ID

    response = auth_central_grpc_client.GetTokenInfo(
        auth_pb2.GetTokenInfoRequest(token=access_token),
        metadata=request_context.to_metadata(),
    )
    assert response.user_id == VALID_EMAIL
    assert response.tenant_id == VALID_ENTERPRISE_TENANT_ID


def test_auth_central_customer_ui(auth_central_http_server, client_config_map):
    redirect_uri = client_config_map[CUSTOMER_UI_CLIENT_ID].redirect_uris[0]

    response = auth_central_http_server.accept(
        authorize_params=[
            ("client_id", CUSTOMER_UI_CLIENT_ID),
            ("redirect_uri", redirect_uri),
            ("state", STATE),
            ("response_type", "code"),
            ("code_challenge", VALID_CODE_CHALLENGE),
            ("code_challenge_method", "S256"),
        ],
        userinfo={
            "email": VALID_EMAIL,
            "sub": VALID_USER_ID,
        },
    )
    assert response.status_code == 200
    parts = urlparse(response.json()["path"])

    query = parse_qs(parts.query, strict_parsing=True)
    assert query["state"] == [STATE]
    assert query["code"][0] is not None

    code = query["code"][0]

    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "code": code,
            "client_id": CUSTOMER_UI_CLIENT_ID,
            "redirect_uri": redirect_uri,
            "grant_type": "authorization_code",
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )

    assert response.status_code == 200
    access_token = response.json()["access_token"]
    assert access_token

    # Post again, to check for reuse
    response = requests.post(
        auth_central_http_server.url("token"),
        json={
            "code": code,
            "client_id": CUSTOMER_UI_CLIENT_ID,
            "redirect_uri": redirect_uri,
            "grant_type": "authorization_code",
            "code_verifier": VALID_CODE_VERIFIER,
        },
    )

    assert response.status_code == 400
    assert response.json()["error"] == "invalid_grant"


def test_auth_central_customer_ui_cookie_logout(
    auth_central_http_server,
    auth_central_grpc_client,
    request_context,
    client_config_map,
):
    redirect_uri = client_config_map[CUSTOMER_UI_CLIENT_ID].redirect_uris[0]

    """Test authenticating the customer UI via auth central with cookies."""
    s = auth_central_http_server.session

    # common request parameters
    query = [
        ("client_id", CUSTOMER_UI_CLIENT_ID),
        ("redirect_uri", redirect_uri),
        ("state", STATE),
        ("response_type", "code"),
        ("code_challenge", VALID_CODE_CHALLENGE),
        ("code_challenge_method", "S256"),
    ]
    userinfo = {
        "email": VALID_EMAIL,
        "sub": VALID_USER_ID,
    }

    # Call accept to populate the cookie
    response = auth_central_http_server.accept(
        authorize_params=query,
        userinfo=userinfo,
    )
    assert response.status_code == 200

    # With a valid cookie, /authorize should redirect to /accept.
    response = s.get(
        auth_central_http_server.url("/authorize", query),
        allow_redirects=False,  # By default, requests follows redirects
    )
    assert response.status_code == 302
    redirect = response.headers["Location"]
    assert redirect.startswith(redirect_uri)

    # Call /logout to clear the cookie
    # Verify cookies exist before logout
    assert len(s.cookies) > 0, "Expected cookies to exist before logout"

    response = s.get(
        auth_central_http_server.url("/logout", query),
        allow_redirects=False,
    )

    # Invalid cookie, /authorize redirects to login
    response = s.get(
        auth_central_http_server.url("/authorize", query),
        allow_redirects=False,
    )
    assert response.status_code == 302
    redirect = response.headers["Location"]
    assert redirect.startswith("/login?")


def test_auth_central_customer_ui_cookie_revoke(
    auth_central_http_server,
    auth_central_grpc_client,
    request_context,
    client_config_map,
):
    redirect_uri = client_config_map[CUSTOMER_UI_CLIENT_ID].redirect_uris[0]

    """Test authenticating the customer UI via auth central with cookie revocation."""
    # Creating a session allows you to keep cookies across requests, which is
    # important for this test
    s = auth_central_http_server.session

    # common request parameters
    query = [
        ("client_id", CUSTOMER_UI_CLIENT_ID),
        ("redirect_uri", redirect_uri),
        ("state", STATE),
        ("response_type", "code"),
        ("code_challenge", VALID_CODE_CHALLENGE),
        ("code_challenge_method", "S256"),
    ]
    userinfo = {
        "email": VALID_EMAIL,
        "sub": VALID_USER_ID,
    }

    # Call accept to populate the cookie
    response = auth_central_http_server.accept(
        authorize_params=query,
        userinfo=userinfo,
    )

    assert response.status_code == 200

    # With a valid cookie, /authorize should redirect to the redirect uri.
    response = s.get(
        auth_central_http_server.url("/authorize", query),
        allow_redirects=False,  # By default, requests follows redirects
    )

    assert response.status_code == 302
    redirect = response.headers["Location"]
    assert redirect.startswith(redirect_uri)

    # Revoke the cookie for the user through the grpc interface
    users = auth_central_grpc_client.ListTenantUsers(
        auth_pb2.ListTenantUsersRequest(tenant_id=VALID_ENTERPRISE_TENANT_ID),
        metadata=request_context.to_metadata(),
    )
    user = [x for x in users.users if x.email == VALID_EMAIL][0]
    auth_central_grpc_client.RevokeUserCookies(
        auth_pb2.RevokeUserCookiesRequest(
            user_id=user.id,
            tenant_id=VALID_ENTERPRISE_TENANT_ID,
        ),
        metadata=request_context.to_metadata(),
    )

    # Invalid cookie, redirect to login
    response = s.get(
        auth_central_http_server.url("/authorize", query),
        allow_redirects=False,
    )
    assert response.status_code == 302
    redirect = response.headers["Location"]
    assert redirect.startswith("/login?")

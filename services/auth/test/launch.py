import json
import os
import pytest

from services.auth.central.server import auth_central_test_setup


def test_run_fixtures(
    auth_central_grpc_server: auth_central_test_setup.AuthCentralGrpcServer,
    auth_central_http_server_raw: auth_central_test_setup.AuthCentralHttpServer,
    token_exchange_server,
):
    info_dict = {}
    info_dict["unlikely_string"] = "KPgjijlupAYN_wII26qbXg"
    info_dict["auth_central_grpc_server"] = (
        f"localhost:{auth_central_grpc_server.grpc_port}"
    )
    info_dict["token_exchange_server"] = f"localhost:{token_exchange_server.port}"
    info_dict["auth_central_http_server"] = auth_central_http_server_raw.url(path="/")
    print(json.dumps(info_dict), flush=True)

    # Wait for signal to stop
    input("Press Enter to continue...")


if __name__ == "__main__":
    print("Current working directory:", os.getcwd(), flush=True)
    pytest.main(["-s", __file__])

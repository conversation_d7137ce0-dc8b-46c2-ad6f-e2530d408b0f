# Stripe Webhook Service

This is a webhook listener service for tracking Stripe events.

It listens for Stripe webhooks, receives the interested events, and puts them into a dedicated pub/sub queue that other services can react to.

One main use case of this webhook service is to decouple our internal systems from Stripe API rate limit. We can internally use received Stripe events to maintain a synchronized copy of Stripe customer data in our own database, so that we can independently scale our user management system without being constrained by external API rate limit.
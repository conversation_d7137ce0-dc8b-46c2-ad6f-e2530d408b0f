package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/webhook"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/go/secretstring"
	stripe_event "github.com/augmentcode/augment/services/auth/stripe_webhook/stripe_event"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

var stripeWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
	Name: "auth_central_stripe_webhook_request_total",
	Help: "Counter of handled Stripe webhook requests",
}, []string{"status_code", "event_type"})

// StripeWebhookHandler handles Stripe webhook events
type StripeWebhookHandler struct {
	webhookSecret secretstring.SecretString
	topic         *pubsub.Topic
}

// NewStripeWebhookHandler creates a new Stripe webhook handler
func NewStripeWebhookHandler(webhookSecret secretstring.SecretString, topic *pubsub.Topic) *StripeWebhookHandler {
	return &StripeWebhookHandler{
		webhookSecret: webhookSecret,
		topic:         topic,
	}
}

// HandleWebhook handles Stripe webhook events
func (h *StripeWebhookHandler) HandleWebhook(w http.ResponseWriter, r *http.Request) {
	requestID := requestcontext.NewRandomRequestId()
	log.Info().Msgf("Received Stripe webhook request, assigned request ID %s", requestID)

	statusCode, err := h.handleHelper(r, requestID)
	if err != nil {
		log.Error().Err(err).Msgf("Error handling Stripe webhook request %s", requestID)
	}
	w.WriteHeader(statusCode)
}

func (h *StripeWebhookHandler) handleHelper(r *http.Request, requestID requestcontext.RequestId) (statusCode int, err error) {
	defer func() {
		eventType := r.Header.Get("Stripe-Event-Type")
		if eventType == "" {
			eventType = "unknown"
		}
		stripeWebhookRequestCounter.WithLabelValues(strconv.Itoa(statusCode), eventType).Inc()
	}()

	// Read the request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Error().Err(err).Msg("Error reading request body")
		return http.StatusBadRequest, fmt.Errorf("error reading request body: %w", err)
	}

	// Verify the webhook signature
	if r.Header.Get("Stripe-Signature") == "" {
		log.Error().Msg("Missing Stripe-Signature header")
		log.Debug().Msg("Headers: " + fmt.Sprintf("%v", r.Header))
		return http.StatusBadRequest, fmt.Errorf("missing Stripe-Signature header")
	}
	event, err := webhook.ConstructEvent(body, r.Header.Get("Stripe-Signature"), h.webhookSecret.Expose())
	if err != nil {
		log.Error().Err(err).Msg("Error verifying webhook signature")
		return http.StatusBadRequest, fmt.Errorf("error verifying webhook signature: %w", err)
	}

	log.Info().Str("event_type", string(event.Type)).Str("event_id", event.ID).Msgf("Received Stripe webhook event with request ID %s", requestID)

	// Parse the event into a structured format
	stripeEvent, err := h.parseStripeEvent(event, requestID)
	if err != nil {
		log.Error().Err(err).Msg("Error parsing Stripe event")
		return http.StatusBadRequest, fmt.Errorf("error parsing Stripe event: %w", err)
	}

	// Skip processing for events we don't care about
	if stripeEvent == nil {
		log.Info().Str("event_type", string(event.Type)).Msg("Ignoring event")
		return http.StatusOK, nil
	}

	// Serialize the event for publishing
	data, err := proto.Marshal(stripeEvent)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling Stripe event")
		return http.StatusInternalServerError, fmt.Errorf("error marshalling Stripe event: %w", err)
	}

	// Create the message
	msg := &pubsub.Message{
		Data: data,
	}

	// Publish the event to PubSub
	ctx := r.Context()
	publishResult := h.topic.Publish(ctx, msg)

	if publishResult != nil {
		_, err = publishResult.Get(ctx)
	}

	if err != nil {
		log.Error().Err(err).Msg("Error publishing to pubsub")
		return http.StatusInternalServerError, fmt.Errorf("error publishing to pubsub: %w", err)
	}

	log.Info().Msgf("Published Stripe event with request ID %s to pubsub topic %s", requestID, h.topic.ID())
	return http.StatusOK, nil
}

// parseStripeEvent parses a Stripe event into our structured format
func (h *StripeWebhookHandler) parseStripeEvent(event stripe.Event, requestID requestcontext.RequestId) (*stripe_event.StripeEvent, error) {
	stripeEvent := &stripe_event.StripeEvent{
		RequestId: requestID.String(),
		EventId:   event.ID,
		EventType: string(event.Type),
		Created:   timestamppb.New(time.Unix(event.Created, 0)),
	}

	// Extract relevant IDs based on event type
	switch event.Type {
	// Customer events
	// - customer.created: A new customer is created in Stripe
	// - customer.updated: Customer information is updated (e.g., email, name, payment method)
	// - customer.deleted: A customer is deleted from Stripe
	case "customer.created", "customer.updated", "customer.deleted":
		var customer stripe.Customer
		if err := json.Unmarshal(event.Data.Raw, &customer); err != nil {
			return nil, fmt.Errorf("error unmarshalling customer data: %w", err)
		}
		stripeEvent.StripeCustomerId = &customer.ID

		customerEvent := &stripe_event.CustomerEvent{}
		stripeEvent.Event = &stripe_event.StripeEvent_CustomerEvent{
			CustomerEvent: customerEvent,
		}

	// Invoice events
	// - invoice.payment_failed: Payment for an invoice has failed
	// - invoice.paid: An invoice has been paid successfully
	case "invoice.payment_failed", "invoice.paid":
		var invoice stripe.Invoice
		if err := json.Unmarshal(event.Data.Raw, &invoice); err != nil {
			return nil, fmt.Errorf("error unmarshalling invoice data: %w", err)
		}
		stripeEvent.StripeCustomerId = &invoice.Customer.ID

		invoiceEvent := &stripe_event.InvoiceEvent{
			InvoiceId: invoice.ID,
		}

		if invoice.Subscription != nil {
			invoiceEvent.SubscriptionId = &invoice.Subscription.ID
		}

		stripeEvent.Event = &stripe_event.StripeEvent_InvoiceEvent{
			InvoiceEvent: invoiceEvent,
		}

	// Checkout events
	// - checkout.session.completed: A checkout session has completed successfully
	case "checkout.session.completed":
		var session stripe.CheckoutSession
		if err := json.Unmarshal(event.Data.Raw, &session); err != nil {
			return nil, fmt.Errorf("error unmarshalling checkout session data: %w", err)
		}

		if session.Customer != nil {
			StripeCustomerId := session.Customer.ID
			stripeEvent.StripeCustomerId = &StripeCustomerId
		}

		checkoutEvent := &stripe_event.CheckoutEvent{
			CheckoutSessionId: session.ID,
		}

		if session.Subscription != nil {
			checkoutEvent.SubscriptionId = &session.Subscription.ID
		}

		stripeEvent.Event = &stripe_event.StripeEvent_CheckoutEvent{
			CheckoutEvent: checkoutEvent,
		}

	// SetupIntent events are triggered when a customer completes the payment method setup flow.
	// These events are critical for the Orb integration as they confirm a valid payment method.
	case "setup_intent.succeeded", "setup_intent.canceled", "setup_intent.setup_failed":
		var setupIntent stripe.SetupIntent
		if err := json.Unmarshal(event.Data.Raw, &setupIntent); err != nil {
			return nil, fmt.Errorf("error unmarshalling setup intent data: %w", err)
		}

		// Extract the customer ID
		if setupIntent.Customer != nil {
			stripeCustomerId := setupIntent.Customer.ID
			stripeEvent.StripeCustomerId = &stripeCustomerId
		}

		setupIntentEvent := &stripe_event.SetupIntentEvent{
			SetupIntentId: setupIntent.ID,
			Status:        string(setupIntent.Status),
			Livemode:      setupIntent.Livemode,
		}

		// Add usage if available
		if setupIntent.Usage != "" {
			setupIntentEvent.Usage = string(setupIntent.Usage)
		}

		// Add payment method ID if available
		if setupIntent.PaymentMethod != nil {
			setupIntentEvent.PaymentMethodId = setupIntent.PaymentMethod.ID
		}

		// Add customer ID if available
		if setupIntent.Customer != nil {
			setupIntentEvent.CustomerId = setupIntent.Customer.ID
		}

		stripeEvent.Event = &stripe_event.StripeEvent_SetupIntentEvent{
			SetupIntentEvent: setupIntentEvent,
		}

	// Payment Method events
	// - payment_method.detached: A payment method has been detached from a customer
	case "payment_method.detached":
		var paymentMethod stripe.PaymentMethod
		if err := json.Unmarshal(event.Data.Raw, &paymentMethod); err != nil {
			return nil, fmt.Errorf("error unmarshalling payment method data: %w", err)
		}

		// Extract payment method ID
		paymentMethodId := paymentMethod.ID
		log.Info().Str("payment_method_id", paymentMethodId).Msg("Extracted payment method ID")

		// Extract the customer ID from previous attributes since the customer is no longer
		// set in the detached payment method in the event data
		if event.Data.PreviousAttributes != nil {
			// PreviousAttributes is a map[string]interface{}
			prevData := event.Data.PreviousAttributes

			// Check if customer is in the previous attributes
			if customerVal, ok := prevData["customer"]; ok && customerVal != nil {
				if customerId, ok := customerVal.(string); ok && customerId != "" {
					stripeEvent.StripeCustomerId = &customerId
					log.Info().Str("customer_id", customerId).Msg("Found customer ID in previous_attributes")
				}
			}
		}

		paymentMethodEvent := &stripe_event.PaymentMethodEvent{
			PaymentMethodId: paymentMethodId,
		}

		stripeEvent.Event = &stripe_event.StripeEvent_PaymentMethodEvent{
			PaymentMethodEvent: paymentMethodEvent,
		}

	// System events
	// - ping: Sent by Stripe to verify webhook configuration
	case "ping":
		// Ignore ping events
		return nil, nil

	default:
		// Ignore unrecognized event types
		log.Info().Str("event_type", string(event.Type)).Msg("Ignoring unrecognized event type")
		return nil, nil
	}

	return stripeEvent, nil
}

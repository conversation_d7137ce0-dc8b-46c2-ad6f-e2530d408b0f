package main

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/go/secretstring"
	pubsubemulator "github.com/augmentcode/augment/base/test_utils/pubsub"
	stripe_event "github.com/augmentcode/augment/services/auth/stripe_webhook/stripe_event"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

func TestStripeWebhookHandler_HandleWebhook(t *testing.T) {
	// Start the PubSub emulator
	endpoint, cleanup, err := pubsubemulator.StartEmulator(0) // Use a random port
	require.NoError(t, err, "Failed to start PubSub emulator")
	defer cleanup() // Ensure emulator is cleaned up after test

	// Set the PUBSUB_EMULATOR_HOST environment variable for the client
	os.Setenv("PUBSUB_EMULATOR_HOST", endpoint)

	// Create a real PubSub client using the emulator
	ctx := context.Background()
	client, err := pubsub.NewClient(ctx, "test-project")
	require.NoError(t, err, "Failed to create PubSub client")
	defer client.Close()

	// Create a topic for testing
	topicID := "stripe-events-test"
	topic, err := client.CreateTopic(ctx, topicID)
	require.NoError(t, err, "Failed to create PubSub topic")
	defer topic.Delete(ctx) // Clean up the topic after test

	// Enable message ordering for the topic
	topic.EnableMessageOrdering = true

	// Create a webhook secret
	webhookSecret := secretstring.New("whsec_test_secret")

	// Create the handler with the real PubSub topic
	handler := NewStripeWebhookHandler(webhookSecret, topic)

	// Create a subscription to verify messages are published
	subID := "stripe-events-test-sub"
	sub, err := client.CreateSubscription(ctx, subID, pubsub.SubscriptionConfig{
		Topic:                 topic,
		AckDeadline:           10 * time.Second,
		EnableMessageOrdering: true,
	})
	require.NoError(t, err, "Failed to create subscription")
	defer sub.Delete(ctx)

	// Test cases
	testCases := []struct {
		name            string
		eventType       string
		rawEventJSON    string    // Raw JSON instead of Go structs
		expectedMessage *struct { // Expected message structure, nil if no message expected
			OrderingKey string
			Event       *stripe_event.StripeEvent
		}
	}{
		{
			name:      "Ping Event",
			eventType: "ping",
			rawEventJSON: `{
				"id": "evt_ping",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "ping",
				"data": {
					"object": {}
				}
			}`,
			expectedMessage: nil, // No message expected
		},
		{
			name:      "Unhandled Event Type",
			eventType: "charge.succeeded",
			rawEventJSON: `{
				"id": "evt_charge_succeeded",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "charge.succeeded",
				"data": {
					"object": {
						"id": "ch_123",
						"object": "charge"
					}
				}
			}`,
			expectedMessage: nil, // No message expected
		},
		{
			name:      "Customer Created Event",
			eventType: "customer.created",
			rawEventJSON: `{
				"id": "evt_cust_created",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "customer.created",
				"data": {
					"object": {
						"id": "cus_new",
						"object": "customer",
						"email": "<EMAIL>",
						"name": "New Customer"
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_new",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the customer event
					event := &stripe_event.StripeEvent{
						EventType:        "customer.created",
						StripeCustomerId: proto.String("cus_new"),
					}

					// Set the customer event using the oneof field
					event.Event = &stripe_event.StripeEvent_CustomerEvent{
						CustomerEvent: &stripe_event.CustomerEvent{},
					}

					return event
				}(),
			},
		},
		{
			name:      "Customer Updated Event",
			eventType: "customer.updated",
			rawEventJSON: `{
				"id": "evt_cust_updated",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "customer.updated",
				"data": {
					"object": {
						"id": "cus_update",
						"object": "customer",
						"email": "<EMAIL>",
						"name": "Updated Customer"
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_update",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the customer event
					event := &stripe_event.StripeEvent{
						EventType:        "customer.updated",
						StripeCustomerId: proto.String("cus_update"),
					}

					// Set the customer event using the oneof field
					event.Event = &stripe_event.StripeEvent_CustomerEvent{
						CustomerEvent: &stripe_event.CustomerEvent{},
					}

					return event
				}(),
			},
		},
		{
			name:      "Customer Deleted Event",
			eventType: "customer.deleted",
			rawEventJSON: `{
				"id": "evt_cust_deleted",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "customer.deleted",
				"data": {
					"object": {
						"id": "cus_delete",
						"object": "customer",
						"email": "<EMAIL>",
						"name": "Deleted Customer"
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_delete",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the customer event
					event := &stripe_event.StripeEvent{
						EventType:        "customer.deleted",
						StripeCustomerId: proto.String("cus_delete"),
					}

					// Set the customer event using the oneof field
					event.Event = &stripe_event.StripeEvent_CustomerEvent{
						CustomerEvent: &stripe_event.CustomerEvent{},
					}

					return event
				}(),
			},
		},
		{
			name:      "Invoice Payment Failed Event",
			eventType: "invoice.payment_failed",
			rawEventJSON: `{
				"id": "evt_invoice_failed",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "invoice.payment_failed",
				"data": {
					"object": {
						"id": "in_failed",
						"object": "invoice",
						"customer": "cus_invoice_fail",
						"subscription": "sub_invoice_fail"
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_invoice_fail",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the invoice event
					event := &stripe_event.StripeEvent{
						EventType:        "invoice.payment_failed",
						StripeCustomerId: proto.String("cus_invoice_fail"),
					}

					// Set the invoice event using the oneof field
					subId := "sub_invoice_fail"
					event.Event = &stripe_event.StripeEvent_InvoiceEvent{
						InvoiceEvent: &stripe_event.InvoiceEvent{
							InvoiceId:      "in_failed",
							SubscriptionId: &subId,
						},
					}

					return event
				}(),
			},
		},
		{
			name:      "Invoice Paid Event",
			eventType: "invoice.paid",
			rawEventJSON: `{
				"id": "evt_invoice_paid",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "invoice.paid",
				"data": {
					"object": {
						"id": "in_paid",
						"object": "invoice",
						"customer": "cus_invoice_paid",
						"subscription": "sub_invoice_paid"
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_invoice_paid",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the invoice event
					event := &stripe_event.StripeEvent{
						EventType:        "invoice.paid",
						StripeCustomerId: proto.String("cus_invoice_paid"),
					}

					// Set the invoice event using the oneof field
					subId := "sub_invoice_paid"
					event.Event = &stripe_event.StripeEvent_InvoiceEvent{
						InvoiceEvent: &stripe_event.InvoiceEvent{
							InvoiceId:      "in_paid",
							SubscriptionId: &subId,
						},
					}

					return event
				}(),
			},
		},
		{
			name:      "Checkout Session Completed Event",
			eventType: "checkout.session.completed",
			rawEventJSON: `{
				"id": "evt_checkout_completed",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "checkout.session.completed",
				"data": {
					"object": {
						"id": "cs_completed",
						"object": "checkout.session",
						"customer": "cus_checkout",
						"subscription": "sub_checkout"
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_checkout",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the checkout event
					event := &stripe_event.StripeEvent{
						EventType:        "checkout.session.completed",
						StripeCustomerId: proto.String("cus_checkout"),
					}

					// Set the checkout event using the oneof field
					subId := "sub_checkout"
					event.Event = &stripe_event.StripeEvent_CheckoutEvent{
						CheckoutEvent: &stripe_event.CheckoutEvent{
							CheckoutSessionId: "cs_completed",
							SubscriptionId:    &subId,
						},
					}

					return event
				}(),
			},
		},
		{
			name:      "Setup Intent Succeeded Event",
			eventType: "setup_intent.succeeded",
			rawEventJSON: `{
				"id": "evt_setup_intent_succeeded",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "setup_intent.succeeded",
				"data": {
					"object": {
						"id": "seti_123",
						"object": "setup_intent",
						"customer": "cus_setup",
						"payment_method": "pm_123",
						"status": "succeeded",
						"usage": "off_session",
						"livemode": false
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_setup",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the setup intent event
					event := &stripe_event.StripeEvent{
						EventType:        "setup_intent.succeeded",
						StripeCustomerId: proto.String("cus_setup"),
					}

					// Set the setup intent event using the oneof field
					event.Event = &stripe_event.StripeEvent_SetupIntentEvent{
						SetupIntentEvent: &stripe_event.SetupIntentEvent{
							SetupIntentId:   "seti_123",
							PaymentMethodId: "pm_123",
							Status:          "succeeded",
							CustomerId:      "cus_setup",
							Livemode:        false,
							Usage:           "off_session",
						},
					}

					return event
				}(),
			},
		},
		{
			name:      "Setup Intent Canceled Event",
			eventType: "setup_intent.canceled",
			rawEventJSON: `{
				"id": "evt_setup_intent_canceled",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "setup_intent.canceled",
				"data": {
					"object": {
						"id": "seti_456",
						"object": "setup_intent",
						"customer": "cus_setup_cancel",
						"payment_method": null,
						"status": "canceled",
						"usage": "on_session",
						"livemode": false
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_setup_cancel",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the setup intent event
					event := &stripe_event.StripeEvent{
						EventType:        "setup_intent.canceled",
						StripeCustomerId: proto.String("cus_setup_cancel"),
					}

					// Set the setup intent event using the oneof field
					event.Event = &stripe_event.StripeEvent_SetupIntentEvent{
						SetupIntentEvent: &stripe_event.SetupIntentEvent{
							SetupIntentId: "seti_456",
							Status:        "canceled",
							CustomerId:    "cus_setup_cancel",
							Livemode:      false,
							Usage:         "on_session",
						},
					}

					return event
				}(),
			},
		},
		{
			name:      "Payment Method Detached Event with Previous Attributes",
			eventType: "payment_method.detached",
			rawEventJSON: `{
				"id": "evt_payment_method_detached_prev",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "payment_method.detached",
				"data": {
					"object": {
						"id": "pm_detached_prev",
						"object": "payment_method",
						"customer": null
					},
					"previous_attributes": {
						"customer": "cus_previous_customer"
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "customer-cus_previous_customer",
				Event: func() *stripe_event.StripeEvent {
					// Create the event with the payment method event
					event := &stripe_event.StripeEvent{
						EventType:        "payment_method.detached",
						StripeCustomerId: proto.String("cus_previous_customer"),
					}

					// Set the payment method event using the oneof field
					event.Event = &stripe_event.StripeEvent_PaymentMethodEvent{
						PaymentMethodEvent: &stripe_event.PaymentMethodEvent{
							PaymentMethodId: "pm_detached_prev",
						},
					}

					return event
				}(),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			payload := []byte(tc.rawEventJSON)

			// Create a timestamp
			timestamp := time.Now().Unix()
			timestampStr := fmt.Sprintf("%d", timestamp)

			// Create the signed payload
			signedPayload := fmt.Sprintf("%s.%s", timestampStr, payload)

			// Compute the HMAC signature using the webhook secret
			mac := hmac.New(sha256.New, []byte(webhookSecret.Expose()))
			mac.Write([]byte(signedPayload))
			signature := hex.EncodeToString(mac.Sum(nil))

			// Format the Stripe-Signature header correctly
			signatureHeader := fmt.Sprintf("t=%s,v1=%s", timestampStr, signature)

			// Create the request with the correct headers
			req := httptest.NewRequest(http.MethodPost, "/webhook", bytes.NewReader(payload))
			req.Header.Set("Stripe-Signature", signatureHeader)
			req.Header.Set("Stripe-Event-Type", tc.eventType)

			// Create the response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.HandleWebhook(rr, req)

			// Assert the response is always OK
			assert.Equal(t, http.StatusOK, rr.Code, "Expected HTTP status code to be OK")

			// If we expect a message to be published, verify it
			if tc.expectedMessage != nil {
				// Create a context with a timeout for receiving the message
				receiveCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
				defer cancel()

				// Create a channel to receive the message
				msgChan := make(chan *pubsub.Message, 1)
				errChan := make(chan error, 1)

				// Pull a message from the subscription
				go func() {
					sub.Receive(receiveCtx, func(ctx context.Context, msg *pubsub.Message) {
						msgChan <- msg
						msg.Ack()
					})
					errChan <- nil
				}()

				// Wait for a message or timeout
				select {
				case msg := <-msgChan:
					// Verify the message has data
					assert.NotEmpty(t, msg.Data, "Message data should not be empty")

					// Unmarshal the protobuf message
					var actualEvent stripe_event.StripeEvent
					err := proto.Unmarshal(msg.Data, &actualEvent)
					require.NoError(t, err, "Failed to unmarshal protobuf message")

					// Verify the event against the expected event
					expectedEvent := tc.expectedMessage.Event

					// Verify the event type
					assert.Equal(t, expectedEvent.EventType, actualEvent.EventType, "Event type should match")

					// Verify the customer ID
					if expectedEvent.StripeCustomerId != nil {
						require.NotNil(t, actualEvent.StripeCustomerId, "Customer ID should not be nil")
						assert.Equal(t, *expectedEvent.StripeCustomerId, *actualEvent.StripeCustomerId, "Customer ID should match")
					}

					// Verify the event payload based on the oneof field
					switch expectedEvent.Event.(type) {
					case *stripe_event.StripeEvent_CustomerEvent:
						// Verify customer event
						_ = expectedEvent.GetCustomerEvent()
						actualCust := actualEvent.GetCustomerEvent()
						require.NotNil(t, actualCust, "Customer event should not be nil")

					case *stripe_event.StripeEvent_InvoiceEvent:
						// Verify invoice event
						expectedInv := expectedEvent.GetInvoiceEvent()
						actualInv := actualEvent.GetInvoiceEvent()
						require.NotNil(t, actualInv, "Invoice event should not be nil")

						assert.Equal(t, expectedInv.InvoiceId, actualInv.InvoiceId, "Invoice ID should match")
						if expectedInv.SubscriptionId != nil {
							require.NotNil(t, actualInv.SubscriptionId, "Subscription ID should not be nil")
							assert.Equal(t, *expectedInv.SubscriptionId, *actualInv.SubscriptionId, "Subscription ID should match")
						}

					case *stripe_event.StripeEvent_CheckoutEvent:
						// Verify checkout event
						expectedChk := expectedEvent.GetCheckoutEvent()
						actualChk := actualEvent.GetCheckoutEvent()
						require.NotNil(t, actualChk, "Checkout event should not be nil")

						assert.Equal(t, expectedChk.CheckoutSessionId, actualChk.CheckoutSessionId, "Checkout session ID should match")
						if expectedChk.SubscriptionId != nil {
							require.NotNil(t, actualChk.SubscriptionId, "Subscription ID should not be nil")
							assert.Equal(t, *expectedChk.SubscriptionId, *actualChk.SubscriptionId, "Subscription ID should match")
						}

					case *stripe_event.StripeEvent_SetupIntentEvent:
						// Verify setup intent event
						expectedSetup := expectedEvent.GetSetupIntentEvent()
						actualSetup := actualEvent.GetSetupIntentEvent()
						require.NotNil(t, actualSetup, "Setup intent event should not be nil")

						assert.Equal(t, expectedSetup.SetupIntentId, actualSetup.SetupIntentId, "Setup intent ID should match")
						assert.Equal(t, expectedSetup.PaymentMethodId, actualSetup.PaymentMethodId, "Payment method ID should match")
						assert.Equal(t, expectedSetup.Status, actualSetup.Status, "Status should match")
						assert.Equal(t, expectedSetup.CustomerId, actualSetup.CustomerId, "Customer ID should match")
						assert.Equal(t, expectedSetup.Livemode, actualSetup.Livemode, "Livemode should match")
						assert.Equal(t, expectedSetup.Usage, actualSetup.Usage, "Usage should match")

					case *stripe_event.StripeEvent_PaymentMethodEvent:
						// Verify payment method event
						expectedPaymentMethod := expectedEvent.GetPaymentMethodEvent()
						actualPaymentMethod := actualEvent.GetPaymentMethodEvent()
						require.NotNil(t, actualPaymentMethod, "Payment method event should not be nil")

						assert.Equal(t, expectedPaymentMethod.PaymentMethodId, actualPaymentMethod.PaymentMethodId, "Payment method ID should match")
					}
				case err := <-errChan:
					require.NoError(t, err, "Failed to receive message from subscription")
				case <-receiveCtx.Done():
					// Only fail if we expected a message
					t.Fatal("Timed out waiting for message")
				}
			}
		})
	}
}

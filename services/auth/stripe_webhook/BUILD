load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_proto_library", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "stripe_event_proto",
    srcs = ["stripe_event.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

go_proto_library(
    name = "stripe_event_go_grpc",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "github.com/augmentcode/augment/services/auth/stripe_webhook/stripe_event",
    proto = ":stripe_event_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "server_lib",
    srcs = [
        "handler.go",
        "main.go",
    ],
    importpath = "github.com/augmentcode/augment/services/auth/stripe_webhook",
    visibility = ["//services/auth:__subpackages__"],
    deps = [
        ":stripe_event_go_grpc",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_github_stripe_stripe_go_v80//:stripe-go",
        "@com_github_stripe_stripe_go_v80//webhook",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/auth:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

go_test(
    name = "webhook_handler_test",
    srcs = [
        "handler_test.go",
    ],
    embed = [":server_lib"],
    deps = [
        ":stripe_event_go_grpc",
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/pubsub:pubsub_emulator_go",
        "//services/lib/request_context:request_context_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_stripe_stripe_go_v80//:stripe-go",
        "@com_github_stripe_stripe_go_v80//webhook",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "setup_intent_handler_test",
    srcs = [
        "handler_setup_intent_test.go",
    ],
    embed = [":server_lib"],
    deps = [
        ":stripe_event_go_grpc",
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/pubsub:pubsub_emulator_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//proto",
    ],
)

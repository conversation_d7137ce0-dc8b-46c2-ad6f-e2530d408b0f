syntax = "proto3";

package stripe_event;

import "google/protobuf/timestamp.proto";

// StripeEvent represents a structured Stripe webhook event
message StripeEvent {
  // Request ID for tracking
  string request_id = 1;

  // Stripe event ID
  string event_id = 2;

  // Type of Stripe event (e.g., "customer.subscription.created")
  string event_type = 3;

  // Timestamp when the event was created
  google.protobuf.Timestamp created = 4;

  // Stripe customer ID
  optional string stripe_customer_id = 5;

  // Event-specific data
  oneof event {
    SubscriptionEvent subscription_event = 6;
    CustomerEvent customer_event = 7;
    InvoiceEvent invoice_event = 8;
    CheckoutEvent checkout_event = 9;
    SetupIntentEvent setup_intent_event = 10;
    PaymentMethodEvent payment_method_event = 11;
  }
}

// SubscriptionEvent contains subscription-specific data
// For events: customer.subscription.created, customer.subscription.updated,
// customer.subscription.deleted, customer.subscription.paused, customer.subscription.resumed
message SubscriptionEvent {
  string subscription_id = 1;

  // The price/plan ID that identifies the subscription tier
  string price_id = 2;

  // Number of seats (for team subscriptions)
  int32 seats = 3;

  // Status of the subscription (active, canceled, etc.)
  string status = 4;

  // Unix timestamp for when the subscription started
  int64 start_date = 5;

  // Unix timestamp for when the trial ends (0 if no trial)
  int64 trial_end = 6;

  // Unix timestamp for when the subscription ends (0 if ongoing)
  int64 end_date = 7;

  // Whether the subscription will cancel at the end of the current period
  bool cancel_at_period_end = 8;

  // Whether the customer has a payment method attached
  bool has_payment_method = 9;

  // Subscription metadata from Stripe (includes augment_user_id if available)
  map<string, string> metadata = 10;

  // Unix timestamp for when the event was received
  int64 received_at = 11;
}

// CustomerEvent contains customer-specific data
// For events: customer.created, customer.updated, customer.deleted
message CustomerEvent {
  // Currently no additional fields needed beyond customer_id in parent
}

// InvoiceEvent contains invoice-specific data
// For events: invoice.payment_failed, invoice.paid
message InvoiceEvent {
  string invoice_id = 1;
  optional string subscription_id = 2;
}

// CheckoutEvent contains checkout-specific data
// For events: checkout.session.completed
message CheckoutEvent {
  string checkout_session_id = 1;
  optional string subscription_id = 2;
}

// SetupIntentEvent contains data for Stripe's payment method setup flow.
// A SetupIntent securely saves a customer's payment method for future use without charging them.
// Used in Orb integration for agent pricing where Orb handles the billing.
message SetupIntentEvent {
  // Stripe's identifier for this setup intent
  string setup_intent_id = 1;

  // Only populated for successful setup events
  string payment_method_id = 2;

  // Possible values: "succeeded", "canceled", "requires_payment_method", etc.
  string status = 3;

  // Stripe's customer ID, which maps to our user via the StripeCustomerId field
  string customer_id = 4;

  // Distinguishes between test mode and production events
  bool livemode = 5;

  // "on_session" requires customer authentication for future payments
  // "off_session" allows charging the payment method without customer present
  string usage = 6;
}

// PaymentMethodEvent contains data for Stripe's payment method events.
message PaymentMethodEvent {
  // Stripe's identifier for this payment method
  string payment_method_id = 1;
}

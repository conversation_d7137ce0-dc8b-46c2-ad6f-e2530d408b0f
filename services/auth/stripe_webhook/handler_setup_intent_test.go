package main

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/go/secretstring"
	pubsubemulator "github.com/augmentcode/augment/base/test_utils/pubsub"
	stripe_event "github.com/augmentcode/augment/services/auth/stripe_webhook/stripe_event"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

func TestStripeWebhookHandler_HandleSetupIntentEvents(t *testing.T) {
	// Start the PubSub emulator
	endpoint, cleanup, err := pubsubemulator.StartEmulator(0) // Use a random port
	require.NoError(t, err, "Failed to start PubSub emulator")
	defer cleanup() // Ensure emulator is cleaned up after test

	// Set the PUBSUB_EMULATOR_HOST environment variable for the client
	os.Setenv("PUBSUB_EMULATOR_HOST", endpoint)

	// Create a real PubSub client using the emulator
	ctx := context.Background()
	client, err := pubsub.NewClient(ctx, "test-project")
	require.NoError(t, err, "Failed to create PubSub client")
	defer client.Close()

	// Create a topic for testing
	topicID := "stripe-events-test"
	topic, err := client.CreateTopic(ctx, topicID)
	require.NoError(t, err, "Failed to create PubSub topic")
	defer topic.Delete(ctx) // Clean up the topic after test

	// Enable message ordering for the topic
	topic.EnableMessageOrdering = true

	// Create a webhook secret
	webhookSecret := secretstring.New("whsec_test_secret")

	// Create the handler with the real PubSub topic
	handler := NewStripeWebhookHandler(webhookSecret, topic)

	// Create a subscription to verify messages are published
	subID := "stripe-events-test-sub"
	sub, err := client.CreateSubscription(ctx, subID, pubsub.SubscriptionConfig{
		Topic:                 topic,
		AckDeadline:           10 * time.Second,
		EnableMessageOrdering: true,
	})
	require.NoError(t, err, "Failed to create subscription")
	defer sub.Delete(ctx)

	// Test cases
	testCases := []struct {
		name            string
		eventType       string
		rawEventJSON    string
		expectedMessage *struct {
			OrderingKey string
			Event       *stripe_event.StripeEvent
		}
	}{
		{
			name:      "SetupIntent Succeeded Event",
			eventType: "setup_intent.succeeded",
			rawEventJSON: `{
				"id": "evt_setup_intent_succeeded",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "setup_intent.succeeded",
				"data": {
					"object": {
						"id": "seti_123456789",
						"object": "setup_intent",
						"status": "succeeded",
						"customer": "cus_123456789",
						"payment_method": "pm_123456789",
						"usage": "off_session",
						"livemode": false
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "", // No ordering key expected
				Event: &stripe_event.StripeEvent{
					EventType:        "setup_intent.succeeded",
					StripeCustomerId: proto.String("cus_123456789"),
					Event: &stripe_event.StripeEvent_SetupIntentEvent{
						SetupIntentEvent: &stripe_event.SetupIntentEvent{
							SetupIntentId:   "seti_123456789",
							PaymentMethodId: "pm_123456789",
							Status:          "succeeded",
							CustomerId:      "cus_123456789",
							Livemode:        false,
							Usage:           "off_session",
						},
					},
				},
			},
		},
		{
			name:      "SetupIntent Canceled Event",
			eventType: "setup_intent.canceled",
			rawEventJSON: `{
				"id": "evt_setup_intent_canceled",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "setup_intent.canceled",
				"data": {
					"object": {
						"id": "seti_canceled_123",
						"object": "setup_intent",
						"status": "canceled",
						"customer": "cus_canceled_123",
						"usage": "off_session",
						"livemode": false
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "", // No ordering key expected
				Event: &stripe_event.StripeEvent{
					EventType:        "setup_intent.canceled",
					StripeCustomerId: proto.String("cus_canceled_123"),
					Event: &stripe_event.StripeEvent_SetupIntentEvent{
						SetupIntentEvent: &stripe_event.SetupIntentEvent{
							SetupIntentId: "seti_canceled_123",
							Status:        "canceled",
							CustomerId:    "cus_canceled_123",
							Livemode:      false,
							Usage:         "off_session",
						},
					},
				},
			},
		},
		{
			name:      "SetupIntent Setup Failed Event",
			eventType: "setup_intent.setup_failed",
			rawEventJSON: `{
				"id": "evt_setup_intent_failed",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "setup_intent.setup_failed",
				"data": {
					"object": {
						"id": "seti_failed_123",
						"object": "setup_intent",
						"status": "requires_payment_method",
						"customer": "cus_failed_123",
						"usage": "off_session",
						"livemode": false
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "", // No ordering key expected
				Event: &stripe_event.StripeEvent{
					EventType:        "setup_intent.setup_failed",
					StripeCustomerId: proto.String("cus_failed_123"),
					Event: &stripe_event.StripeEvent_SetupIntentEvent{
						SetupIntentEvent: &stripe_event.SetupIntentEvent{
							SetupIntentId: "seti_failed_123",
							Status:        "requires_payment_method",
							CustomerId:    "cus_failed_123",
							Livemode:      false,
							Usage:         "off_session",
						},
					},
				},
			},
		},
		{
			name:      "SetupIntent with Missing Customer",
			eventType: "setup_intent.succeeded",
			rawEventJSON: `{
				"id": "evt_setup_intent_no_customer",
				"object": "event",
				"api_version": "2024-09-30.acacia",
				"created": 1698765432,
				"type": "setup_intent.succeeded",
				"data": {
					"object": {
						"id": "seti_no_customer",
						"object": "setup_intent",
						"status": "succeeded",
						"payment_method": "pm_no_customer",
						"usage": "off_session",
						"livemode": false
					}
				}
			}`,
			expectedMessage: &struct {
				OrderingKey string
				Event       *stripe_event.StripeEvent
			}{
				OrderingKey: "", // No ordering key expected
				Event: &stripe_event.StripeEvent{
					EventType: "setup_intent.succeeded",
					Event: &stripe_event.StripeEvent_SetupIntentEvent{
						SetupIntentEvent: &stripe_event.SetupIntentEvent{
							SetupIntentId:   "seti_no_customer",
							PaymentMethodId: "pm_no_customer",
							Status:          "succeeded",
							Livemode:        false,
							Usage:           "off_session",
						},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			payload := []byte(tc.rawEventJSON)

			// Create a timestamp
			timestamp := time.Now().Unix()
			timestampStr := fmt.Sprintf("%d", timestamp)

			// Create the signed payload
			signedPayload := fmt.Sprintf("%s.%s", timestampStr, payload)

			// Compute the HMAC signature using the webhook secret
			mac := hmac.New(sha256.New, []byte(webhookSecret.Expose()))
			mac.Write([]byte(signedPayload))
			signature := hex.EncodeToString(mac.Sum(nil))

			// Format the Stripe-Signature header correctly
			signatureHeader := fmt.Sprintf("t=%s,v1=%s", timestampStr, signature)

			// Create the request with the correct headers
			req := httptest.NewRequest(http.MethodPost, "/webhook", bytes.NewReader(payload))
			req.Header.Set("Stripe-Signature", signatureHeader)
			req.Header.Set("Stripe-Event-Type", tc.eventType)

			// Create the response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			handler.HandleWebhook(rr, req)

			// Assert the response is always OK
			assert.Equal(t, http.StatusOK, rr.Code, "Expected HTTP status code to be OK")

			// If we expect a message to be published, verify it
			if tc.expectedMessage != nil {
				// Create a channel to receive messages
				msgChan := make(chan *pubsub.Message, 1)
				ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer cancel()

				// Start receiving messages
				go func() {
					err := sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
						msgChan <- msg
						msg.Ack()
					})
					if err != nil && err != context.Canceled && err != context.DeadlineExceeded {
						t.Errorf("Error receiving messages: %v", err)
					}
				}()

				// Wait for a message or timeout
				select {
				case msg := <-msgChan:
					// Verify the message has data
					assert.NotEmpty(t, msg.Data, "Message data should not be empty")

					// Unmarshal the protobuf message
					var actualEvent stripe_event.StripeEvent
					err := proto.Unmarshal(msg.Data, &actualEvent)
					require.NoError(t, err, "Failed to unmarshal protobuf message")

					// Verify the event against the expected event
					expectedEvent := tc.expectedMessage.Event

					// Verify the event type
					assert.Equal(t, expectedEvent.EventType, actualEvent.EventType, "Event type should match")

					// Verify the customer ID if expected
					if expectedEvent.StripeCustomerId != nil {
						require.NotNil(t, actualEvent.StripeCustomerId, "Customer ID should not be nil")
						assert.Equal(t, *expectedEvent.StripeCustomerId, *actualEvent.StripeCustomerId, "Customer ID should match")
					} else {
						// If we don't expect a customer ID, it should be nil or empty
						if actualEvent.StripeCustomerId != nil {
							assert.Empty(t, *actualEvent.StripeCustomerId, "Customer ID should be empty")
						}
					}

					// Verify the setup intent event
					expectedSetupIntent := expectedEvent.GetSetupIntentEvent()
					actualSetupIntent := actualEvent.GetSetupIntentEvent()
					require.NotNil(t, actualSetupIntent, "Setup intent event should not be nil")

					// Verify setup intent fields
					assert.Equal(t, expectedSetupIntent.SetupIntentId, actualSetupIntent.SetupIntentId, "Setup intent ID should match")
					assert.Equal(t, expectedSetupIntent.Status, actualSetupIntent.Status, "Status should match")
					assert.Equal(t, expectedSetupIntent.Livemode, actualSetupIntent.Livemode, "Livemode should match")
					assert.Equal(t, expectedSetupIntent.Usage, actualSetupIntent.Usage, "Usage should match")

					// Verify payment method ID if expected
					if expectedSetupIntent.PaymentMethodId != "" {
						assert.Equal(t, expectedSetupIntent.PaymentMethodId, actualSetupIntent.PaymentMethodId, "Payment method ID should match")
					}

					// Verify customer ID if expected
					if expectedSetupIntent.CustomerId != "" {
						assert.Equal(t, expectedSetupIntent.CustomerId, actualSetupIntent.CustomerId, "Customer ID should match")
					}

				case <-time.After(5 * time.Second):
					t.Fatal("Timed out waiting for message")
				}
			}
		})
	}
}

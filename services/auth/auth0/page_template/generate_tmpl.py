#!/usr/bin/env python3
"""Script for generating the template we upload to Auth0 for the login page."""

from __future__ import annotations
import json

import jinja2
import yaml

import pathlib
import argparse
import http.client


def render_template(
    posthog_key: str,
    segment_write_key: str,
    sign_in_client_id: str,
    sign_up_client_id: str,
) -> str:
    # Create a Jinja environment
    my_path = pathlib.Path(__file__).parent
    env = jinja2.Environment(
        loader=jinja2.FileSystemLoader(my_path / "src"),
        autoescape=True,
        # Change delimiters to avoid conflicts with Liquid
        variable_start_string="{{{",
        variable_end_string="}}}",
        block_start_string="{{%",
        block_end_string="%}}",
        comment_start_string="{{#",
        comment_end_string="#}}",
    )

    # Read CSS file content
    css_content = (my_path / "build" / "output.css").read_text()

    # Load the template
    template = env.get_template("page.html")

    return template.render(
        css=css_content,
        posthog_key=posthog_key,
        segment_write_key=segment_write_key,
        sign_in_client_id=sign_in_client_id,
        sign_up_client_id=sign_up_client_id,
    )


def get_access_token(
    conn: http.client.HTTPSConnection,
    auth0_domain: str,
    auth0_client_id: str,
    auth0_client_secret_path: pathlib.Path,
) -> str:
    # Use the client ID and secret to generate an access token
    client_secret = auth0_client_secret_path.read_text(encoding="utf-8").strip()
    conn.request(
        "POST",
        "/oauth/token",
        json.dumps(
            {
                "client_id": auth0_client_id,
                "client_secret": client_secret,
                "audience": f"https://{auth0_domain}/api/v2/",
                "grant_type": "client_credentials",
            }
        ).encode("utf-8"),
        {
            "content-type": "application/json",
        },
    )
    token_response = conn.getresponse()
    if token_response.status != 200:
        raise SystemError(
            f"Failed to get access token: {token_response.status} - {token_response.read().decode('utf-8')}"
        )
    token_response_body = json.loads(token_response.read().decode("utf-8"))
    if token_response_body["token_type"] != "Bearer":
        raise SystemError(
            f"Unexpected token type from Auth0: {token_response_body['token_type']}"
        )
    return token_response_body["access_token"]


def update_auth0(
    conn: http.client.HTTPSConnection,
    auth0_access_token: str,
    universal_login_html: str,
):
    # Upload the template using the new access token
    conn.request(
        "PUT",
        "/api/v2/branding/templates/universal-login",
        universal_login_html,
        {
            "authorization": f"Bearer {auth0_access_token}",
            "content-type": "text/html",
        },
    )

    update_response = conn.getresponse()
    if update_response.status != 204:
        raise SystemError(
            f"Failed to upload template: {update_response.status} - {update_response.read().decode('utf-8')}"
        )

    print("\n✅ Uploaded template\n")


def main() -> int:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--print",
        required=False,
        action="store_true",
        default=False,
        help="Print to command line",
    )
    parser.add_argument(
        "--skip_update",
        required=False,
        action="store_true",
        default=False,
        help="Skip the Auth0 update",
    )
    parser.add_argument(
        "--auth0_domain", required=False, type=str, help="Domain of Auth0"
    )
    parser.add_argument(
        "--auth0_client_id", required=False, type=str, help="Client ID for Auth0 API"
    )
    parser.add_argument(
        "--auth0_client_secret_path",
        required=False,
        type=pathlib.Path,
        help="Client Secret file path for Auth0 API",
    )
    parser.add_argument(
        "--env",
        required=True,
        type=str,
        choices=["DEV", "STAGING", "PROD"],
        help="Environment to update",
    )
    args = parser.parse_args()

    if args.env == "STAGING":
        raise ValueError("STAGING is not supported yet")

    write_key_path = (
        pathlib.Path(__file__).parent.parent.parent
        / "central"
        / "server"
        / "write_key.yaml"
    )
    with write_key_path.open("r", encoding="utf-8") as f:
        segment_write_key = yaml.safe_load(f)[args.env]["segment_write_key"]

    params = {
        "DEV": {
            "posthog_key": "phc_GIfRcxAmWBSIxniAKUhlM9pQ343wGGnAgC3iBMO1FSN",
            "sign_in_client_id": "YDStJU9aSQKrrcNnV94OJYgCL847crAT",
            "sign_up_client_id": "jz5S6h2rGOSO2lWLsXWBnyFSKcWFkMlm",
        },
        "PROD": {
            "posthog_key": "phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW",
            "sign_in_client_id": "wlLTVWGDfItW9HziIowSRieQNRylMPTa",
            "sign_up_client_id": "iRw6OuuM38rGfxmznjhcBmWUIxXDR1OH",
        },
    }
    output = render_template(segment_write_key=segment_write_key, **params[args.env])

    if args.print:
        print(output)

    if not args.skip_update:
        if (
            not args.auth0_domain
            or not args.auth0_client_id
            or not args.auth0_client_secret_path
        ):
            raise ValueError("Missing one or more auth0 arguments")

        conn = http.client.HTTPSConnection(args.auth0_domain)
        auth0_access_token = get_access_token(
            conn,
            args.auth0_domain,
            args.auth0_client_id,
            args.auth0_client_secret_path,
        )
        update_auth0(
            conn,
            auth0_access_token,
            output,
        )

    return 0


if __name__ == "__main__":
    raise SystemExit(main())

load("@aspect_bazel_lib//lib:copy_to_bin.bzl", "copy_to_bin")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("@npm//services/auth/auth0/page_template:tailwindcss/package_json.bzl", tailwind = "bin")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages()

# TODO Remove external dependency from auth/common/frontend
# Not used by Auth0 template, move to auth/common
copy_to_bin(
    name = "css_files",
    srcs = glob([
        "styles/**/*.css",
    ]),
    visibility = ["//services/auth/common/frontend:__subpackages__"],
)

copy_to_bin(
    name = "package_json",
    srcs = ["package.json"],
)

copy_to_bin(
    name = "tailwind_config",
    srcs = ["tailwind.config.js"],
)

copy_to_bin(
    name = "input_css",
    srcs = ["src/tailwind.css"],
)

copy_to_bin(
    name = "template_html",
    srcs = glob(["src/**/*.html"]),
)

tailwind.tailwindcss(
    name = "styles_css",
    srcs = [
        ":input_css",
        ":node_modules/tailwindcss",
        ":package_json",
        ":tailwind_config",
        ":template_html",
    ],
    outs = ["build/output.css"],
    args = [
        "--config",
        "services/auth/auth0/page_template/tailwind.config.js",
        "--input",
        "services/auth/auth0/page_template/src/tailwind.css",
        "--output",
        "services/auth/auth0/page_template/build/output.css",
        "--minify",
    ],
)

py_library(
    name = "generate_tmpl_library",
    srcs = [
        "generate_tmpl.py",
    ],
    data = [
        ":styles_css",
        ":template_html",
        "//services/auth/central/server:write_key",
    ],
    deps = [
        requirement("jinja2"),
        requirement("pyyaml"),
    ],
)

py_binary(
    name = "generate_tmpl",
    srcs = ["generate_tmpl.py"],
    main = "generate_tmpl.py",
    deps = [
        ":generate_tmpl_library",
    ],
)

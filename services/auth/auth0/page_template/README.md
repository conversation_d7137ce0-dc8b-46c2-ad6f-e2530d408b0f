# Generating Auth0 page template

Auth0 uses a single page template with embedded css to generate all the login screens for their Universal Login. To achieve this we generate the minified CSS from Tailwind and inject it into the template file with <PERSON><PERSON>.

## About Tailwind

The Tailwind processor will automatically scan the template file `page.html` for any in-use Tailwind classes and generate the appropriate CSS.

## Generating the template

Run the following Bazel task to output the template with embedded styles.

```sh
bazel run //services/auth/auth0/page_template:generate_tmpl -- --env=DEV --skip_update --print
```

If you want to upload the template to Auth0 development environment, you can use the Auth0 CLI like:

```sh
bazel run //services/auth/auth0/page_template:generate_tmpl -- --env=DEV --skip_update --print | auth0 ul templates update
```

## Developing

You can use the auth0 cli to preview the various pages locally. Sign in with the cli and then run the following command to get the local preview environment.

```sh
auth0 universal-login customize
```

Paste the output of the template generator in the Page Template tab to see your page in action. Unfortunately there's no simple way to make style changes in the Page Template editor without regenerating the whole template file (since Tailwind needs to generating any new classes).

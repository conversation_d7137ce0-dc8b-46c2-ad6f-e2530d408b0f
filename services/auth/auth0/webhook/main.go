package main

import (
	"bufio"
	"context"
	tls "crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/signal"
	"slices"
	"strings"
	"sync"
	"syscall"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
)

var configFile = flag.String("config", "", "Path to config file")

var requestCounter = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_auth0_webhook_requests",
		Help: "Number of HTTP requests",
	},
	[]string{"status", "method", "path"},
)

var durationHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_auth0_webhook_duration",
		Help:    "Duration of webhook HTTP requests",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"status", "method", "path"},
)

var readBodyHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_auth0_webhook_read_body_duration",
		Help:    "Duration of reading request body",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"method", "path"},
)

var pubsubErrors = prometheus.NewCounter(
	prometheus.CounterOpts{
		Name: "au_auth0_webhook_pubsub_errors",
		Help: "Number of errors publishing to pubsub",
	},
)

var postPubsubHistogram = prometheus.NewHistogram(
	prometheus.HistogramOpts{
		Name:    "au_auth0_webhook_post_pubsub_duration",
		Help:    "Duration of publishing to pubsub",
		Buckets: prometheus.DefBuckets,
	},
)

type TlsConfig struct {
	KeyPath  string `json:"key_path"`
	CertPath string `json:"cert_path"`
	CaPath   string `json:"ca_path"`
}

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerTlsConfig *TlsConfig `json:"server_mtls"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// Token file
	TokenFile string `json:"token_file"`

	// Pubsub topic to publish to
	PubsubTopic string `json:"pubsub_topic"`

	// GCP project id for the Pubsub topic
	ProjectId string `json:"project_id"`
}

type JSONLToPubsubServer struct {
	Tokens []string
	Topic  *pubsub.Topic
}

type LoggingResponseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (w *LoggingResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

func writeError(w http.ResponseWriter, error string, code int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	fmt.Fprintln(w, error)
}

func withMetrics(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		loggingWriter := &LoggingResponseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
		}

		start := time.Now()

		next.ServeHTTP(loggingWriter, r)

		duration := time.Since(start)
		durationHistogram.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Observe(duration.Seconds())

		requestCounter.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Inc()

		event := log.Info()
		if loggingWriter.statusCode >= 500 {
			event = log.Error()
		} else if loggingWriter.statusCode >= 400 {
			event = log.Warn()
		}

		event.Str("method", r.Method).Str("path", r.URL.Path).Int("status", loggingWriter.statusCode).Dur("duration", duration).Msgf("Request processed")
	})
}

func (s *JSONLToPubsubServer) handleJSONL(w http.ResponseWriter, r *http.Request) {
	// Check the Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		writeError(w, "{\"error\":\"Missing Authorization header\"}", http.StatusUnauthorized)
		return
	}

	authHeader = strings.TrimPrefix(authHeader, "Bearer ")

	// Check if the token is valid
	if !slices.Contains(s.Tokens, authHeader) {
		writeError(w, `{"error":"Invalid Authorization header"}`, http.StatusUnauthorized)
		return
	}

	// Write body to pubsub topic
	start := time.Now()
	json_lines, err := io.ReadAll(r.Body)
	if err != nil {
		writeError(w, `{"error":"Error reading request body"}`, http.StatusBadRequest)
		return
	}
	readBodyHistogram.WithLabelValues(
		r.Method,
		r.URL.Path,
	).Observe(time.Since(start).Seconds())

	start = time.Now()
	// Publish to pubsub topic
	_, err = s.Topic.Publish(r.Context(), &pubsub.Message{
		Data: json_lines,
	}).Get(r.Context())
	if err != nil {
		log.Warn().Err(err).Msg("Error publishing to pubsub")
		pubsubErrors.Inc()

		writeError(w, `{"error":"Error"}`, http.StatusInternalServerError)
		return
	}
	postPubsubHistogram.Observe(time.Since(start).Seconds())

	w.WriteHeader(http.StatusOK)
}

func loadServerTlsConfig(serverTlsConfig *TlsConfig) (*tls.Config, error) {
	cert, err := tls.LoadX509KeyPair(serverTlsConfig.CertPath, serverTlsConfig.KeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load server certificate and key: %v", err)
	}

	// Create tls.Config
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12,
	}

	return tlsConfig, nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	openFile, err := os.Open(config.TokenFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening token file")
	}
	defer openFile.Close()

	scanner := bufio.NewScanner(openFile)
	tokens := make([]string, 0)

	for scanner.Scan() {
		token := scanner.Text()
		if len(token) == 0 {
			continue
		}
		if len(token) < 8 {
			log.Warn().Msg("Ignoring token because it is too short")
			continue
		}
		tokens = append(tokens, token)
	}

	// Setup metrics.
	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("GET /metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	prometheus.MustRegister(requestCounter)
	prometheus.MustRegister(durationHistogram)
	prometheus.MustRegister(readBodyHistogram)
	prometheus.MustRegister(pubsubErrors)
	prometheus.MustRegister(postPubsubHistogram)

	serverTls, err := loadServerTlsConfig(config.ServerTlsConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	ctx := context.Background()
	client, err := pubsub.NewClient(ctx, config.ProjectId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create pubsub client")
		return
	}
	defer client.Close()

	pubsubTopic := client.Topic(config.PubsubTopic)
	defer pubsubTopic.Stop()

	jsonlServer := &JSONLToPubsubServer{
		Tokens: tokens,
		Topic:  pubsubTopic,
	}

	// Set up HTTPS server
	mux := http.NewServeMux()
	mux.Handle("POST /append_from_auth0", withMetrics(http.HandlerFunc(jsonlServer.handleJSONL)))
	mux.Handle("GET /health", withMetrics(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})))

	server := &http.Server{
		Addr:      fmt.Sprintf(":%d", config.Port),
		Handler:   mux,
		TLSConfig: serverTls,
	}

	wg := sync.WaitGroup{}

	wg.Add(1)
	go func() {
		defer wg.Done()

		log.Info().Msgf("Starting HTTPS server on port %d", config.Port)
		if err := server.ListenAndServeTLS("", ""); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting HTTPS server")
		}
	}()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan

	log.Info().Msgf("Received signal %s, initiating graceful shutdown", sig)

	// Initiate graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Error().Err(err).Msg("Error during server shutdown")
	}
	wg.Wait()
	log.Info().Msg("Server has shut down gracefully")
}

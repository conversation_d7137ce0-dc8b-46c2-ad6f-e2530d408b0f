local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'auth0-webhook',
      kubecfg: {
        target: '//services/auth/auth0/webhook:kubecfg',
        task: [
          {
            cloud: std.asciiUpper(centralNamespace.cloud),
            env: centralNamespace.env,
            namespace: centralNamespace.namespace,
          }
          for centralNamespace in cloudInfo.centralNamespaces
          if cloudInfo.isLeadProdCluster(centralNamespace.cloud)
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'auth0-webhook-monitoring',
      kubecfg: {
        target: '//services/auth/auth0/webhook:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

// the function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  // the app name is used in the kubernetes object names. It is also added as label to each object
  // so that we know which app an object belongs to
  local appName = 'auth0-webhook';

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local ingressHostname = if env == 'PROD' then 'auth0-webhook.%s' % domainSuffix else 'auth0-webhook.%s.%s' % [namespace, domainSuffix];

  // creates a server certificate for MTLS
  local serverCert = certLib.createPublicServerCert(
    name='%s-server-certificate' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    dnsNames=[ingressHostname],
    volumeName='https-certs',
  );

  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );

  local auth0TokensSecret = {
    apiVersion: 'bitnami.com/v1alpha1',
    kind: 'SealedSecret',
    metadata: {
      name: 'auth0-webhook-tokens',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          'tokens.txt': 'AgBsZGM6rHKQo9PyY08iR10Ig7JSeXiAPJ8/b708yzA6Wkl2NkVV1CWQrNkzV8O4iAQoMD8Ac6LzXrd+mWFPGxGxZYqkBJF9CqFfxgmB2lNaKAf3sBTfbHJkkmAc/6+/WvzqgBTGZ3JjkMdNPEzAFGCuH9LFT1p2/FgyxT8/IXamz00pSUZluj8tHCgeHO6P9rg3ymCUvWaAXR3tt5p5wz0oQfTHN/supNOY4Cb4i1U2L2l0FuL0Ftz7I1SUz1zYeb+sj36p7Eady+qcSJ5QfgXvi1igbvny+b4oL/F5T+S4lDkuqSiSmfXss/1R9E5OGTloIz8dx1PztSaiX5m/f6LnseEfad5ua6Y1iQI0O3Ag3R8YuxK56LCUtsnxoM15dKTz8R3R00Ig9FDokTVSlTFdSiBRf9Axy2SN9Dnv6Pp5qxLkdHqwvIfwn1iZbXy2K4W/FU2wtLMcdEbJbjzFhXn7n3yMwd1w1IE7x9xhUMQlxoPMTcBnHH5HOanykY4FvrUBqK5Ez04tgrDNIkMIwE9Y5dLFg07wUztJrGpgoYrDcak9DK0HktZYZ7Z5qFE+d3YHLa8XMxb+cUwtf3DebqsO+88q0QxTXTWYFbCDG4/UbvhJJGYAy9o99bkWAJvjm9l6xcNTWJiUDQ3DPvQv6QsH4ZIzk51OrzVJe4lM6pGR+Fy9OvxPyvv2iHX4ff3jAtc722WScnIToh6bOkxJZiwXhLJMXplUqCdv',
        },
        GCP_US_CENTRAL1_PROD: {
          'tokens.txt': 'AgAbVR4BAd9ecifDmyy50YkCGQ5vFDxuahRKWVkn6PlsvdCxP6pyl/17lm77ajKN88NmQ8ARu18xbYSbJHgwXGqycFLk268kovV4XGls7t/NNo459BRY97ts6hLDOjW5wYpHtJgVHqBTWbVoTSzoFimAeAJScs6BguB+ics2qRkdY6JD+wRzJszJwdNMGaT2+EaLtqbJcYz4v8w2IiEXVajSrwqCnUbQd2zdPL+4knpb+ntgRU6RGNxNUty2wYuwcpQsj506iL8TNRMxUnyqNzGBbVDqhgxfqShs/OOxXmNZW+JNScWwLw7Uh/WcEllBjvBKV9YzScHiJU5IREDGCKbA5MZHex+EKYcMUSPJvixrEAo+dLa3+JdhlGBYK5SiiTrCPTEP8kOV9/NWee0+6pH5AW8W+oVVxm5pW/9NHoTC3+QeW8jW36ic4TPHvyQhKP2eWju1V41J7dWDELd66jwIyAaOim3FCh30mZmJll2uWcemSRFrG2fRzCGbc7svkVzJQyxXHyS3D+xmzQ0cyXeB74pUYmq9veVCo6VQenIPFhQLCnSKXyTz/8rFlQxl43one9YSRLWqL6zVmjCs71GykuAlMHSE/hJdKHV7b1dSSJhWAZuOkyAFkyP+/LZ2ujJzlvDc3mrbKFVipoOn8I29lSe9I/WMj5qs4pbXccCBcXxAaYd2oW3H+XoPyq4rgSZ5u4MKOubjHLzBm3q2gLpzUi1wi+28+UOZ',
        },
      }[cloud],
    },
  };

  local publisherTopic = pubsubLib.publisherTopic(
    cloud=cloud,
    env=env,
    namespace=namespace,
    appName=appName,
    serviceAccount=serviceAccount,
    messageRetentionDurationSeconds=60 * 60 * 24 * 7,
  );

  // configuration that will be passed to the server as a JSON file
  local config = {
    port: 8443,
    server_mtls: serverCert.config,
    prom_port: 9090,
    token_file: '/tokens/tokens.txt',
    pubsub_topic: publisherTopic.topicName,
    project_id: cloudInfo[cloud].projectId,
  };

  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local backendConfig = gcpLib.createBackendConfig(
    app=appName,
    cloud=cloud,
    namespace=namespace,
    healthCheck={
      checkIntervalSec: 15,
      port: 8443,
      type: 'HTTPS',
      requestPath: '/health',
    }
  );

  local httpService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: '%s-svc' % appName,
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'public-https': 'HTTPS' }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'public-https',
          targetPort: 'public-https',
        },
      ],
    },
  };

  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: '%s-ingress' % appName,
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: '%s-ssl-cert' % appName,  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: httpService.metadata.name,
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  // creates a container that runs the server
  local container = {
    name: appName,
    // the target is the bazel target that builds the docker image
    target: {
      name: '//services/auth/auth0/webhook:image',
      dst: 'auth0-webhook',
    },
    // the arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
    ],
    // ports that the pod exposes
    ports: [
      {
        containerPort: 8443,
        name: 'public-https',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      {
        name: 'auth0-webhook-tokens',
        mountPath: '/tokens',
        readOnly: true,
      },
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 8443,
      },
      initialDelaySeconds: 5,
      periodSeconds: 10,
    },
    livenessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 8443,
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCert.podVolumeDef,
      {
        name: 'auth0-webhook-tokens',
        secret: {
          secretName: auth0TokensSecret.metadata.name,  // pragma: allowlist secret
          optional: false,
        },
      },
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    deployment,
    auth0TokensSecret,
    ingressObjects,
    publisherTopic.objects,
    httpService,
  ])

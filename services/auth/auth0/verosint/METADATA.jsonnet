local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'auth0-verosint',
      kubecfg: {
        target: '//services/auth/auth0/verosint:kubecfg',
        task: [
          {
            cloud: std.asciiUpper(centralNamespace.cloud),
            env: centralNamespace.env,
            namespace: centralNamespace.namespace,
          }
          for centralNamespace in cloudInfo.centralNamespaces
          if cloudInfo.isLeadProdCluster(centralNamespace.cloud) && centralNamespace.env == 'PROD'
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'auth0-verosint-monitoring',
      kubecfg: {
        target: '//services/auth/auth0/verosint:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['costa'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

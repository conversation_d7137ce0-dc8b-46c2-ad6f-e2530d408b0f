local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local errorsAtLoadBalancerSpec = {
    displayName: 'Auth0 Verosint 5xx measured at load balancer',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(loadbalancing_googleapis_com:https_backend_request_count{monitored_resource="https_lb_rule",target_proxy_name=~".*auth0-verosint.*",response_code_class="500"}[5m])) > 3
      |||,
    },
  };

  local errorsAtServiceSpec = {
    displayName: 'Auth Webhook errors measured at service',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace)(increase(au_auth0_verosint_requests{status=~"^40[013]|50[0-9]$", pod=~"auth0-verosint-.*"}[5m])) > 3
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(cloud, errorsAtLoadBalancerSpec, 'auth0-verosint-5xx-errors', '5xx errors measured at load balancer'),
    monitoringLib.alertPolicy(cloud, errorsAtServiceSpec, 'auth0-verosint-service-errors', 'Errors measured at service'),
  ]

package main

import (
	"bufio"
	"bytes"
	"context"
	tls "crypto/tls"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/signal"
	"slices"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
)

var configFile = flag.String("config", "", "Path to config file")

var requestCounter = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_auth0_verosint_requests",
		Help: "Number of HTTP requests",
	},
	[]string{"status", "method", "path"},
)

var durationHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_auth0_verosint_duration",
		Help:    "Duration of webhook HTTP requests",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"status", "method", "path"},
)

var readBodyHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_auth0_verosint_read_body_duration",
		Help:    "Duration of reading request body",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"method", "path"},
)

var verosintRequestDuration = prometheus.NewHistogram(
	prometheus.HistogramOpts{
		Name:    "au_auth0_verosint_verosint_request_duration",
		Help:    "Duration of requests to verosint",
		Buckets: prometheus.DefBuckets,
	},
)

type TlsConfig struct {
	KeyPath  string `json:"key_path"`
	CertPath string `json:"cert_path"`
	CaPath   string `json:"ca_path"`
}

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerTlsConfig *TlsConfig `json:"server_mtls"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// Token file
	Auth0TokenFile string `json:"auth0_token_file_path"`

	VerosintTokenFile string `json:"verosint_token_file_path"`

	// Verosint URL
	VerosintURL string `json:"verosint_url"`

	// Domains to filter
	FilterDomains []string `json:"filter_domains"`
}

type LoggingResponseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (w *LoggingResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

func writeError(w http.ResponseWriter, error string, code int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(code)
	fmt.Fprintln(w, error)
}

func withMetrics(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		loggingWriter := &LoggingResponseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
		}

		start := time.Now()

		next.ServeHTTP(loggingWriter, r)

		duration := time.Since(start)
		durationHistogram.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Observe(duration.Seconds())

		requestCounter.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Inc()

		event := log.Info()
		if loggingWriter.statusCode >= 500 {
			event = log.Error()
		} else if loggingWriter.statusCode >= 400 {
			event = log.Warn()
		}

		event.Str("method", r.Method).Str("path", r.URL.Path).Int("status", loggingWriter.statusCode).Dur("duration", duration).Msgf("Request processed")
	})
}

func loadServerTlsConfig(serverTlsConfig *TlsConfig) (*tls.Config, error) {
	cert, err := tls.LoadX509KeyPair(serverTlsConfig.CertPath, serverTlsConfig.KeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load server certificate and key: %v", err)
	}

	// Create tls.Config
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12,
	}

	return tlsConfig, nil
}

func readTokens(tokenFilePath string) ([]string, error) {
	openFile, err := os.Open(tokenFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open token file: %v", err)
	}
	defer openFile.Close()

	scanner := bufio.NewScanner(openFile)
	tokens := make([]string, 0)

	for scanner.Scan() {
		token := scanner.Text()
		if len(token) == 0 {
			continue
		}
		if len(token) < 8 {
			log.Warn().Msg("Ignoring token because it is too short")
			continue
		}
		tokens = append(tokens, token)
	}

	return tokens, nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	auth0Tokens, err := readTokens(config.Auth0TokenFile)
	verosintTokens, err := readTokens(config.VerosintTokenFile)

	if len(verosintTokens) != 1 {
		log.Fatal().Msgf("Unexpected number of verosint tokens: %d", len(verosintTokens))
	}
	verosintToken := verosintTokens[0]

	// Setup metrics.
	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("GET /metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	prometheus.MustRegister(requestCounter)
	prometheus.MustRegister(durationHistogram)
	prometheus.MustRegister(readBodyHistogram)

	serverTls, err := loadServerTlsConfig(config.ServerTlsConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	// Set up HTTPS server
	mux := http.NewServeMux()
	mux.Handle("POST /append_from_auth0", withMetrics(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check the Authorization header
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			log.Warn().Msg("Missing Authorization header")
			writeError(w, "{\"error\":\"Missing Authorization header\"}", http.StatusUnauthorized)
			return
		}

		authHeader = strings.TrimPrefix(authHeader, "Bearer ")

		// Check if the token is valid
		if !slices.Contains(auth0Tokens, authHeader) {
			log.Warn().Msg("Invalid Authorization header")
			writeError(w, `{"error":"Invalid Authorization header"}`, http.StatusUnauthorized)
			return
		}

		start := time.Now()
		rawBytes, err := io.ReadAll(r.Body)
		if err != nil {
			log.Warn().Err(err).Msg("Error reading request body")
			writeError(w, `{"error":"Error reading request body"}`, http.StatusBadRequest)
			return
		}
		readBodyHistogram.WithLabelValues(
			r.Method,
			r.URL.Path,
		).Observe(time.Since(start).Seconds())

		var jsonArrayRaw interface{}
		err = json.Unmarshal(rawBytes, &jsonArrayRaw)
		if err != nil {
			log.Warn().Err(err).Msg("Error unmarshalling json_array")
			writeError(w, `{"error":"Error unmarshalling json_array"}`, http.StatusBadRequest)
			return
		}

		jsonArray, ok := jsonArrayRaw.([]interface{})
		if !ok {
			log.Warn().Msg("json_array is not an array")
			writeError(w, `{"error":"json_array is not an array"}`, http.StatusBadRequest)
			return
		}

		outputLines := make([]interface{}, 0)
	outer:
		for _, jsonLineRaw := range jsonArray {
			lineJson, err := json.Marshal(jsonLineRaw)
			if err != nil {
				log.Warn().Err(err).Msg("Error marshalling json line")
				writeError(w, `{"error":"Error marshalling json line"}`, http.StatusBadRequest)
				return
			}
			for _, domain := range config.FilterDomains {
				if strings.Contains(string(lineJson), domain) {
					log.Info().Msgf("Skipping line because it contains %s: %s", domain, string(lineJson))
					continue outer
				}
			}
			outputLines = append(outputLines, jsonLineRaw)
		}

		if len(outputLines) == 0 {
			log.Info().Msg("No lines to send to verosint")
			w.WriteHeader(http.StatusOK)
			return
		}

		// Open connection to verosint URL and dump the JSON lines
		jsonToVerosint, err := json.Marshal(outputLines)
		if err != nil {
			log.Warn().Err(err).Msg("Error marshalling json lines")
			writeError(w, `{"error":"Error marshalling json lines"}`, http.StatusInternalServerError)
			return
		}

		req, err := http.NewRequest("POST", config.VerosintURL, bytes.NewBuffer(jsonToVerosint))
		if err != nil {
			log.Warn().Err(err).Msg("Error creating request")
			writeError(w, `{"error":"Error creating request"}`, http.StatusInternalServerError)
			return
		}
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", verosintToken))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "auth0-logstream/1.0")

		start = time.Now()
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			log.Warn().Err(err).Msg("Error sending request")
			writeError(w, `{"error":"Error sending request"}`, http.StatusInternalServerError)
			return
		}
		defer resp.Body.Close()
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Warn().Err(err).Msg("Error reading response body")
			writeError(w, `{"error":"Error reading response body"}`, http.StatusInternalServerError)
			return
		}
		if resp.StatusCode/100 != 2 {
			log.Warn().Msgf("Non 200 response from verosint: %d %s", resp.StatusCode, string(bodyBytes))
			writeError(w, `{"error":"Error sending request"}`, http.StatusInternalServerError)
			return
		}
		verosintRequestDuration.Observe(time.Since(start).Seconds())
	})))
	mux.Handle("GET /health", withMetrics(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})))

	server := &http.Server{
		Addr:      fmt.Sprintf(":%d", config.Port),
		Handler:   mux,
		TLSConfig: serverTls,
	}

	wg := sync.WaitGroup{}

	wg.Add(1)
	go func() {
		defer wg.Done()

		log.Info().Msgf("Starting HTTPS server on port %d", config.Port)
		if err := server.ListenAndServeTLS("", ""); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting HTTPS server")
		}
	}()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan

	log.Info().Msgf("Received signal %s, initiating graceful shutdown", sig)

	// Initiate graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Error().Err(err).Msg("Error during server shutdown")
	}
	wg.Wait()
	log.Info().Msg("Server has shut down gracefully")
}

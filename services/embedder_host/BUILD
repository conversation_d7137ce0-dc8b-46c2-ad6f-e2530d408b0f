load("//tools/bzl:python.bzl", "py_grpc_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "embedder_proto",
    srcs = ["embedder.proto"],
    deps = ["//base/proto:tensor_proto"],
)

py_grpc_library(
    name = "embedder_py_proto",
    protos = [":embedder_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/proto:tensor_py_proto",
    ],
)

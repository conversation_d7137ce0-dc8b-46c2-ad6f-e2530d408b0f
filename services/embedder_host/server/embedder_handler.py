"""Defines the EmbedderHandler protocol."""

import enum
import logging
import threading
import time
from collections import deque
from concurrent.futures import Future
from concurrent.futures import TimeoutError as FuturesTimeoutError
from dataclasses import dataclass
from typing import Callable, Generic, Iterable, Protocol, Sequence, TypeVar

import opentelemetry.trace
import structlog
from prometheus_client import Counter, Histogram

from base.prometheus import sampled_histogram
from services.embedder_host.server import (
    embedder_batching,
    embedder_model,
)

opentelemetry_tracer = opentelemetry.trace.get_tracer(__name__)

log = structlog.get_logger()

DEFAULT_NAMESPACE = "empty-namespace"

# Exponentially increasing buckets. Min is ~1ms and max is ~32s.
INF = float("inf")
buckets = tuple((1.3**i - 1) / 300.0 for i in range(36)) + (INF,)

_request_latency_s = Histogram(
    "au_embedder_service_request_latency_s",
    "Latency of running individual forward passes of the model.",
    # expensive to add source_namespace, but helpful to
    # see whether tenant fairness is an issue
    ["model_name", "request_type", "source_namespace"],
    buckets=buckets,
)
_request_counter = Counter(
    "au_embedder_service_request_counter",
    "Counts embedder service requests",
    ["model_name", "request_type", "source_namespace"],
)
_request_error_counter = Counter(
    "au_embedder_service_request_error_counter",
    "Counts embedder service requests that failed",
    ["model_name", "source_namespace", "error_type"],
)
_queued_requests = sampled_histogram.SampledCounter(
    "au_embedder_service_queued_requests",
    "Counts the number of requests in the queue",
    interval_seconds=1.0,
    labels=["model_name"],
    buckets=[
        0,
        1,
        2,
        4,
        8,
        16,
        32,
        64,
        INF,
    ],
)
_dropped_requests_due_to_timeout_counter = Counter(
    "au_embedder_service_dropped_requests_due_to_timeout_counter",
    "Counts the number of requests dropped due to timeout",
    ["model_name"],
)


class QueueFullError(Exception):
    """Raised when the queue is full."""


class Priority(enum.Enum):
    """Priority of a request."""

    URGENT = 0
    NORMAL = 1


@dataclass(frozen=True)
class Request:
    """A request to be processed by the embedder handler.

    Note that this is a single request, corresponding to a single chunk
    of tokens in indexing calls.
    """

    tokens: Sequence[int]
    request_id: str
    source_namespace: str
    priority: Priority

    creation_time: float
    timeout_s: float

    request_sub_index: int | None = None
    # If True, check if the queue is full before adding the request.
    # If False, add the request regardless of the queue size. This is
    # useful for adding batched requests consisting of many sub-requests
    # that may be dropped partially if the queue is full.
    check_embedder_queue_full: bool = True

    def timeout_expired(self) -> bool:
        """Returns True if the request has timed out."""
        return time.time() - self.creation_time > self.timeout_s


T = TypeVar("T", covariant=True)


@dataclass(frozen=True)
class Result(Generic[T]):
    """The result of a request to a retrieval inference service."""

    artifact: T | None
    request: Request
    exn: Exception | None = None


@dataclass(frozen=True)
class Task:
    """A task to be run by the embedder model."""

    request: Request
    tokens: Sequence[int]
    future: Future[Result]


class RequestQueue(Protocol):
    """A queue of requests to the embedder service."""

    def add_request(self, request: Request) -> Future[Result]:
        """Put a request in the queue."""
        raise NotImplementedError()

    def pop_task(self) -> Task | None:
        """Get a task from the queue or None if the queue is empty."""
        raise NotImplementedError()


class BasicRequestQueue(RequestQueue):
    """A queue of requests to the embedder service."""

    def __init__(self, max_queue_size: int):
        self._max_queue_size = max_queue_size
        self._queue: deque[Task] = deque()
        self._lock = threading.Lock()

    def add_request(self, request: Request) -> Future[Result]:
        with self._lock:
            if (
                request.check_embedder_queue_full
                and len(self._queue) >= self._max_queue_size
            ):
                raise QueueFullError()
            task = Task(request, request.tokens, Future())
            self._queue.append(task)
        return task.future

    def pop_task(self) -> Task | None:
        with self._lock:
            if len(self._queue) == 0:
                return None
            task = self._queue.popleft()
            return task


class PriorityRequestQueue(RequestQueue):
    """Composes multiple queues with different priorities into a single queue."""

    def __init__(
        self,
        queue_size_per_priority: dict[Priority, int],
        queue_factory: Callable[[int], RequestQueue],
    ):
        self._queues: dict[Priority, RequestQueue] = {}
        for priority, queue_size in queue_size_per_priority.items():
            self._queues[priority] = queue_factory(queue_size)

    def add_request(self, request: Request) -> Future[Result]:
        return self._queues[request.priority].add_request(request)

    def pop_task(self) -> Task | None:
        for priority in Priority:
            queue = self._queues[priority]
            task = queue.pop_task()
            if task is not None:
                log.debug(
                    "PriorityQueue: Dequeued request %s from queue with priority %s",
                    task.request.request_id,
                    priority.name,
                )
                return task
        return None


class FairRequestQueue(RequestQueue):
    """Combines per-tenant queues into a single fair queue.

    The fairness is currently guaranteed by a round-robin strategy.

    Currently, the tenant queues are created lazily and we don't have a way to
    remove them.
    """

    def __init__(
        self, tenant_queue_factory: Callable[[int], RequestQueue], queue_size: int
    ):
        self._queues: dict[str, RequestQueue] = {}  # tenant_namespace -> queue
        self._tenant_deque: deque[str] = deque()
        self._tenant_queue_factory = tenant_queue_factory
        self._queue_size = queue_size
        self._lock = threading.Lock()

    def add_request(self, request: Request) -> Future[Result]:
        with self._lock:
            queue = self._get_queue(request.source_namespace)
            return queue.add_request(request)

    def _get_queue(self, source_namespace: str) -> RequestQueue:
        if source_namespace not in self._queues:
            new_queue = self._tenant_queue_factory(self._queue_size)
            self._queues[source_namespace] = new_queue
            # new tenants go to the end of the queue
            self._tenant_deque.append(source_namespace)
        return self._queues[source_namespace]

    def pop_task(self) -> Task | None:
        with self._lock:
            num_tenant_queues = len(self._tenant_deque)
            for _ in range(num_tenant_queues):
                if len(self._tenant_deque) == 0:
                    return None
                log.debug(
                    "FairQueue: Popping tenant from deque: %s", self._tenant_deque
                )
                next_queue = self._tenant_deque.popleft()
                # rotate to the back to ensure fairness via round-robin:
                task = self._queues[next_queue].pop_task()
                if task is not None:
                    self._tenant_deque.append(next_queue)
                    log.debug(
                        "FairQueue: Dequeued request %s from queue %s with priority %s",
                        task.request.request_id,
                        next_queue,
                        task.request.priority.name,
                    )
                    return task
                else:
                    del self._queues[next_queue]
                    log.debug(
                        "FairQueue: Deleted empty queue %s",
                        next_queue,
                    )
            return None


class RetrievalInferenceHandler(Protocol[T]):
    """A client for the retrieval inference service."""

    def calculate_inference_artifacts(
        self,
        tokens: Sequence[Sequence[int]],
        request_id: str,
        source_namespace: str = DEFAULT_NAMESPACE,
        timeout_s: float = 10.0,
        request_type_desc: str = "default",
        priority: Priority = Priority.NORMAL,
    ) -> Sequence[T]:
        """Calculate the retrieval artifacts for the given batch of tokens.

        An example of an artifact is an embedding or a set of reranker scores.

        May raise TimeoutError if the handler times out before the request
        is completed.

        May raise QueueFullError if the queue is full.

        Args:
            tokens (Sequence[Sequence[int]]): The tokens to calculate artifacts for.
            request_id (str): The request ID.
            source_namespace (str): The namespace of the request.
            query (bool): Whether the request is a query or indexing request.
            timeout_s (float): The timeout for the request.

        Returns:
            T: The retrieval artifacts for the tokens.
        """
        raise NotImplementedError()


class QueueingRetrievalInferenceHandler(RetrievalInferenceHandler[T]):
    """A client for the retrieval inference service."""

    def __init__(
        self,
        model_name: str,
        model: embedder_model.RetrievalInferenceModel[T],
        max_round_size: int,
        max_seq_length: int,
        vocab_size: int,
        indexing_queue_size: int = 1000,
        query_queue_size: int = 20,
        start_queued_requests_counter: bool = False,  # disable for testing
        mrfa_mode: bool = False,  # If true, limit number of requests in round to 1 to support MRFA
        prioritization_over_fairness: bool = True,
    ):
        self._model_name = model_name
        self._model = model

        self._max_round_size = max_round_size
        self._max_seq_length = max_seq_length
        if max_round_size < max_seq_length:
            raise ValueError(
                f"max_round_size ({max_round_size}) must be greater than or equal to max_seq_length ({max_seq_length})."
            )
        self._vocab_size = vocab_size
        self.mrfa_mode = mrfa_mode

        if start_queued_requests_counter:
            _queued_requests.set_labels([model_name])
            _queued_requests.start()

        if prioritization_over_fairness:
            self._queue = PriorityRequestQueue(
                queue_size_per_priority={
                    Priority.URGENT: query_queue_size,
                    Priority.NORMAL: indexing_queue_size,
                },
                queue_factory=lambda queue_size: FairRequestQueue(
                    tenant_queue_factory=BasicRequestQueue,
                    queue_size=queue_size,
                ),
            )
        else:
            self._queue = FairRequestQueue(
                tenant_queue_factory=lambda _: PriorityRequestQueue(
                    queue_size_per_priority={
                        Priority.URGENT: query_queue_size,
                        Priority.NORMAL: indexing_queue_size,
                    },
                    queue_factory=BasicRequestQueue,
                ),
                queue_size=-1,  # not used; see queue_size_per_priority
            )
        self._new_requests_added = threading.Event()

        self._runner = None
        self._shutdown = False
        self._pause_lock = threading.Lock()

    def start(self):
        log.info("Starting embedder runner for %s.", self._model_name)
        assert self._runner is None
        assert not self._shutdown
        self._runner = threading.Thread(
            target=self._run_queue, name=f"embedder_runner_{self._model_name}"
        )

        self._runner.start()
        self._shutdown = False

    def shutdown(self):
        log.info("Shutting down embedder runner for %s.", self._model_name)
        assert self._runner is not None
        self._shutdown = True
        self._new_requests_added.set()
        self._runner.join()
        self._runner = None

    def __enter__(self):
        self.start()
        return self

    def _pause(self):
        """Pause the queue; for testing."""
        self._pause_lock.acquire()

    def _resume(self):
        """Resume the queue; for testing."""
        self._pause_lock.release()

    def __exit__(self, exc_type, exc_value, traceback):
        self.shutdown()

    def _pull_tasks(self) -> Iterable[Task | None]:
        while not self._shutdown:
            log.debug("Checking for tasks for %s.", self._model_name)
            with self._pause_lock:
                task = self._queue.pop_task()
                if task is None:
                    yield None  # indicates to embedder_batching.get_rounds() to not wait for more tasks
                    log.debug(
                        "No tasks available for %s. Waiting for new requests.",
                        self._model_name,
                    )
                    notified = self._new_requests_added.wait()
                    # We immediately clear the flag, but it is possible that we were woken
                    # up and notified before
                    self._new_requests_added.clear()
                    if not notified:
                        # logging on timeout to make hanging easier to debug
                        logging.debug(
                            "Timeout waiting for new requests for %s.", self._model_name
                        )
                    log.debug(
                        "Notification received for %s. Resuming.",
                        self._model_name,
                    )
                    continue
                if task.request.timeout_expired():
                    task.future.set_result(
                        Result(
                            artifact=None,
                            request=task.request,
                            exn=FuturesTimeoutError(
                                f"Request {task.request.request_id} timed out for {self._model_name}."
                            ),
                        )
                    )
                    log.debug(
                        "Request %s timed out for %s. Dropping to avoid spending GPU time on it.",
                        task.request.request_id,
                        self._model_name,
                    )
                    _dropped_requests_due_to_timeout_counter.labels(
                        self._model_name
                    ).inc()
                    continue
                yield task

    def _run_queue(self):
        logging.info("Starting to pull tasks from queue for %s.", self._model_name)

        def tokens_with_metadata() -> (
            Iterable[embedder_batching.TokensWithMetadata | None]
        ):
            for task in self._pull_tasks():
                if task is None:
                    # None is a sentinel value for embedder_batching.get_rounds() to not wait for more tasks
                    yield None
                else:
                    yield embedder_batching.TokensWithMetadata(
                        tokens=task.request.tokens, metadata=task
                    )

        for embedder_round in embedder_batching.get_rounds(
            tokens_with_metadata(),
            self._max_round_size,
            self._max_seq_length,
            self._model.get_num_attention_caches(),
            mrfa_mode=self.mrfa_mode,
        ):
            logging.debug(
                "Processing round with size: %d for %s",
                len(embedder_round.tokens),
                self._model_name,
            )
            tasks_in_round: list[Task] = [
                request_in_round.metadata
                for request_in_round in embedder_round.requests_in_round
            ]
            try:
                artifacts = self._model.process_round(embedder_round)
            except Exception as ex:  # pylint: disable=broad-except
                log.exception("Error processing round for %s.", self._model_name)
                for task in tasks_in_round:
                    task.future.set_result(
                        Result(
                            artifact=None,
                            request=task.request,
                            exn=ex,
                        )
                    )
                continue
            assert len(artifacts) == len(tasks_in_round), (
                len(artifacts),
                len(tasks_in_round),
            )
            for artifact, task in zip(artifacts, tasks_in_round):
                task.future.set_result(
                    Result(
                        artifact=artifact,
                        request=task.request,
                    )
                )

    def _validate_tokens(self, token_lists: Sequence[Sequence[int]]) -> None:
        for tokens in token_lists:
            if len(tokens) > self._max_seq_length:
                raise ValueError(
                    f"Token list exceeds maximum sequence length: {len(tokens)} > {self._max_seq_length}"
                )
            if len(tokens) == 0:
                raise ValueError("Token list is empty.")

    def calculate_inference_artifacts(
        self,
        tokens: Sequence[Sequence[int]],
        request_id: str,
        source_namespace: str = DEFAULT_NAMESPACE,
        timeout_s: float = 10.0,
        request_type_desc: str = "default",
        priority: Priority = Priority.NORMAL,
    ) -> Sequence[T]:
        """Split the requests into separate token lists and enqueue them.

        This implementation assumes that all token lists are non-empty, tokens are
        non-negative, tokens are less than the vocabulary size, and the length of
        each token list is less than the maximum sequence length. If any of these
        assumptions are violated, the error handling logic may cause other requests
        to be dropped.
        """
        start_time = time.time()
        _request_counter.labels(
            self._model_name, request_type_desc, source_namespace
        ).inc()

        span = opentelemetry_tracer.start_as_current_span(
            f"calculate_embedding({self._model_name})"
        )
        timer = _request_latency_s.labels(
            self._model_name, request_type_desc, source_namespace
        ).time()
        # Note that the order of contexts is important; the queued_requests
        # counter must be incremented before we attempt to acquire the lock.
        with span, timer, _queued_requests.inc():
            self._validate_tokens(tokens)
            try:
                request_futures = []
                for request_sub_index, sub_tokens in enumerate(tokens):
                    sub_request = Request(
                        request_id=request_id,
                        tokens=sub_tokens,
                        source_namespace=source_namespace,
                        priority=priority,
                        request_sub_index=request_sub_index,
                        # only check the first sub-request
                        check_embedder_queue_full=(request_sub_index == 0),
                        creation_time=time.time(),
                        timeout_s=timeout_s,
                    )
                    result_future: Future[Result] = self._queue.add_request(sub_request)
                    self._new_requests_added.set()
                    request_futures.append(result_future)
                artifacts = []
                for idx, fut in enumerate(request_futures):
                    elapsed_time = time.time() - start_time
                    remaining_time = timeout_s - elapsed_time
                    try:
                        result: Result = fut.result(timeout=remaining_time)
                        if result.exn is not None:
                            raise result.exn
                    except FuturesTimeoutError as exc:
                        log.info(
                            "Timeout waiting for sub-task %s for remaining_time %ss (timeout %ss)",
                            idx,
                            remaining_time,
                            timeout_s,
                        )
                        raise TimeoutError(
                            f"Timeout waiting for sub-task {idx} for remaining_time {remaining_time}s (timeout {timeout_s}s)"
                        ) from exc
                    if result.request.request_id != request_id:
                        raise ValueError(
                            f"Request id mismatch: {result.request.request_id} != {request_id}"
                        )
                    if result.request.request_sub_index != idx:
                        raise ValueError(
                            f"Request sub index mismatch: {result.request.request_sub_index} != {idx}"
                        )
                    assert result.artifact is not None
                    artifacts.append(result.artifact)
                return artifacts
            except Exception as excn:  # pylint: disable=broad-except
                if isinstance(excn, TimeoutError):
                    error_type = "timeout"
                elif isinstance(excn, QueueFullError):
                    error_type = "queue_full"
                else:
                    error_type = "unknown"  # please check the logs
                    log.error(
                        "Error processing request for %s. %s",
                        self._model_name,
                        excn,
                    )
                _request_error_counter.labels(
                    self._model_name, source_namespace, error_type
                ).inc()
                raise


QueueingEmbedderHandler = QueueingRetrievalInferenceHandler[
    embedder_model.EmbeddingOutput
]

load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_torch_oci_image")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")

py_library(
    name = "conftest",
    srcs = ["conftest.py"],
    deps = [
        "//base/fastforward:fwd",
        "//base/fastforward/starcoder:fwd_starcoder",
        requirement("pytest"),
    ],
)

py_library(
    name = "embedder_batching",
    srcs = ["embedder_batching.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/fastforward:batching",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_utils",
        "//base/logging:struct_logging",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "embedder_batching_test",
    srcs = ["embedder_batching_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        ":embedder_batching",
    ],
)

py_library(
    name = "embedder_model",
    srcs = ["embedder_model.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/fastforward:batching",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_utils",
        "//base/fastforward:multirequest_flash_attention",
        "//services/embedder_host:embedder_py_proto",
        requirement("numpy"),
        requirement("opentelemetry-api"),
        requirement("opentelemetry-sdk"),
        requirement("prometheus-client"),
        requirement("structlog"),
        requirement("grpcio"),
        requirement("torch"),
    ],
)

pytest_test(
    name = "embedder_model_test",
    srcs = ["embedder_model_test.py"],
    tags = [
        "gpu",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":conftest",
        ":embedder_batching",
        ":embedder_model",
        "//base/fastforward:fwd_testmodel",
        requirement("numpy"),
    ],
)

py_library(
    name = "embedder_handler",
    srcs = ["embedder_handler.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":embedder_batching",
        ":embedder_model",
        "//base/logging:struct_logging",
        "//base/prometheus:sampled_histogram",
        "//services/lib/request_context:request_context_py",
        requirement("numpy"),
        requirement("opentelemetry-api"),
        requirement("opentelemetry-sdk"),
        requirement("prometheus-client"),
        requirement("structlog"),
    ],
)

pytest_test(
    name = "embedder_handler_test",
    srcs = ["embedder_handler_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        ":conftest",
        ":embedder_handler",
        ":embedder_model",
        "//base/fastforward:fwd_testmodel",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "embedder_handler_queueing_test",
    srcs = ["embedder_handler_queueing_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        ":conftest",
        ":embedder_handler",
        ":embedder_model",
        "//base/fastforward:fwd_testmodel",
        requirement("numpy"),
    ],
)

DEPS = [
    ":embedder_model",
    ":embedder_handler",
    "//base/feature_flags:feature_flags_py",
    "//base/logging:struct_logging",
    "//base/tracing:tracing_py",
    "//services/embedder_host:embedder_py_proto",
    "//services/deploy/configs:repo_model_config_py_proto",
    "//services/lib:model_arch",
    "//services/lib/grpc/metrics:metrics",
    "//base/proto:tensor",
    "//base/fastforward/codegen:fwd_codegen",
    "//base/fastforward/starcoder:fwd_starcoder",
    "//base/fastforward/starcoder:fwd_starcoder_fp8",
    "//base/fastforward/llama:fwd_llama",
    "//base/fastforward/llama:fwd_llama_fp8",
    requirement("dataclasses_json"),
    requirement("grpcio"),
    requirement("grpcio-reflection"),
    requirement("numpy"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("prometheus-client"),
    requirement("protobuf"),
    requirement("grpc-interceptor"),
    "//base/python/signal_handler",
]

py_binary(
    name = "embedder_service",
    srcs = [
        "embedder_service.py",
    ],
    main = "embedder_service.py",
    deps = DEPS,
)

py_torch_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":embedder_service",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services/deploy:__subpackages__"],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
    ],
)

#!/bin/bash
#

set -e

export SEED=51

echo "SEED: "
echo $SEED

date

bazel run //services/api_proxy/client:util -- --endpoint https://dev-markus.us-central.api.augmentcode.com memorize-path ~/augment/ --seed $SEED --blob-output-file ~/augment-$SEED-blobs.txt --augment-ignore-file ~/augment/.augmentignore >/dev/null

bazel run //services/content_manager/util:util -- --endpoint localhost:50051 --plaintext find-missing ~/augment-$SEED-blobs.txt --transformation-key dr-ethanol6-04-1-30line-1024char --sub-key embeddings.npy --wait

date

echo "Done!"

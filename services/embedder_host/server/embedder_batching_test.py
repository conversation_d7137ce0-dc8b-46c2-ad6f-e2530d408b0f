"""Tests for the embedder batching utilities."""

import numpy as np
import pytest

from services.embedder_host.server import embedder_batching


@pytest.mark.parametrize(
    "tokens, max_round_size, num_caches, expected",
    [
        ([[1, 2, 3]], 128, 1, [[1, 2, 3]]),  # single request, 1 cache available
        ([[1, 2, 3]], 128, 2, [[1, 2, 3]]),  # single request, 2 caches available
        (  # just one cache availalable
            [[1, 2, 3], [4, 5, 6]],
            128,
            1,
            [[1, 2, 3], [4, 5, 6]],
        ),
        (  # 2 requests fit into 1 round
            [[1, 2, 3], [4, 5, 6]],
            128,
            2,
            [[1, 2, 3, 4, 5, 6]],
        ),
        (  # 3 requests, 2 caches -> 2 rounds
            [[1, 2, 3], [4, 5, 6], [7, 8, 9]],
            128,
            2,
            [[1, 2, 3, 4, 5, 6], [7, 8, 9]],
        ),
        (  # 3 requests, 3 caches -> 1 round
            [[1, 2, 3], [4, 5, 6], [7, 8, 9]],
            128,
            3,
            [[1, 2, 3, 4, 5, 6, 7, 8, 9]],
        ),
        (  # 2 request, limited by max round size
            [[1, 2, 3, 4], [5, 6]],
            5,
            2,
            [[1, 2, 3, 4], [5, 6]],
        ),
    ],
)
def test_get_rounds(
    tokens: list[list[int]],
    max_round_size: int,
    num_caches: int,
    expected: list[list[int]],
):
    """Tests the get_batches function."""
    batches = embedder_batching.get_rounds(
        tokens,
        max_round_size=max_round_size,
        max_seq_length=max_round_size,
        num_caches=num_caches,
    )
    batches = list(batches)
    assert len(batches) == len(expected)
    for round, expected_round in zip(batches, expected):
        request_in_round_list, round_tokens = round.requests_in_round, round.tokens
        assert all(
            request_in_round.cache_idx < num_caches
            for request_in_round in request_in_round_list
        )
        assert len(round_tokens) <= max_round_size
        assert round_tokens == expected_round


def test_get_rounds_empty_tokens():
    """Tests the get_batches function with no empty token lists."""
    batches = embedder_batching.get_rounds([], 128, 8, 1)
    batches = list(batches)
    assert len(batches) == 0


def test_get_rounds_raises_on_empty_tokens():
    """Tests the get_batches function with an empty token list."""
    with pytest.raises(ValueError):
        batches = embedder_batching.get_rounds([[]], 128, 8, 1)
        batches = list(batches)
        assert len(batches) == 0


def test_get_rounds_none_tokens():
    """Tests the get_batches function with None instead of tokens."""
    batches = embedder_batching.get_rounds([None], 128, 8, 1)
    batches = list(batches)
    assert len(batches) == 0


def test_get_rounds_some_tokens():
    """Tests the get_batches function with None instead of tokens."""
    batches = embedder_batching.get_rounds([[1, 2, 3], None], 128, 8, 1)
    batches = list(batches)
    assert len(batches) == 1
    assert batches[0].tokens == [1, 2, 3]


def test_get_rounds_none_tokens_in_middle():
    """Tests the get_batches function with none tokens in the middle."""
    batches = embedder_batching.get_rounds([[1, 2, 3], None, [4, 5, 6]], 128, 8, 1)
    batches = list(batches)
    assert len(batches) == 2
    assert batches[0].tokens == [1, 2, 3]
    assert batches[1].tokens == [4, 5, 6]


def test_get_rounds_without_none():
    """Tests the alternative to test_get_rounds_none_tokens_in_middle."""
    batches = embedder_batching.get_rounds([[1, 2, 3], [4, 5, 6]], 128, 8, 4)
    batches = list(batches)
    assert len(batches) == 1
    assert batches[0].tokens == [1, 2, 3, 4, 5, 6]


def test_get_rounds_numpy_tokens():
    """Tests the get_batches function with numpy tokens."""
    batches = embedder_batching.get_rounds(
        [np.array([1, 2, 3]), np.array([4, 5, 6])],
        max_round_size=128,
        max_seq_length=128,
        num_caches=1,
    )
    batches = list(batches)
    assert len(batches) == 2


# TODO(markus): This behavior is not strictly necessary, but the embedder service
# currently assumes this.
def test_long_sequence_raises():
    """Tests that a sequence longer than the maximum sequence length raises."""
    with pytest.raises(ValueError):
        batches = embedder_batching.get_rounds([[1, 2, 3, 4, 5, 6, 7, 8, 9]], 5, 5, 1)
        batches = list(batches)
        assert len(batches) == 0


def test_metadata_is_passed_through():
    """Tests that metadata is passed through."""
    tokens_with_metadata = [
        embedder_batching.TokensWithMetadata(tokens=[1, 2, 3], metadata="test1"),
        embedder_batching.TokensWithMetadata(tokens=[4, 5, 6], metadata="test2"),
        embedder_batching.TokensWithMetadata(tokens=[7, 8, 9], metadata="test3"),
    ]
    batches = embedder_batching.get_rounds(tokens_with_metadata, 128, 8, 3)
    batches = list(batches)
    assert len(batches) == 1
    batch = batches[0]
    assert batch.requests_in_round[0].metadata == "test1"
    assert batch.requests_in_round[1].metadata == "test2"
    assert batch.requests_in_round[2].metadata == "test3"


def test_mrfa_mode():
    batches = embedder_batching.get_rounds(
        [np.array([1, 2, 3]), np.array([4, 5, 6])],
        max_round_size=128,
        max_seq_length=128,
        num_caches=2,
    )
    batches = list(batches)

    # With MRFA mode off, there shoudl be 1 round with 2 requests in it.
    assert len(batches) == 1
    assert len(batches[0].requests_in_round) == 2

    batches = embedder_batching.get_rounds(
        [np.array([1, 2, 3]), np.array([4, 5, 6])],
        max_round_size=128,
        max_seq_length=128,
        num_caches=2,
        mrfa_mode=True,
    )
    batches = list(batches)

    # With MRFA mode on, there should be 2 rounds with 1 request in each.
    assert len(batches) == 2
    assert (
        len(batches[0].requests_in_round) == 1
        and len(batches[1].requests_in_round) == 1
    )

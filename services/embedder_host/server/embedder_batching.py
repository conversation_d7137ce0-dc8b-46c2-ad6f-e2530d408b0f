"""Batching utilities for the embedder service.

Multiple reasons this module is not in base.fastforward.batching:
- It is hard to extend this logic to the case where we have to split the tokens
  of a request across multiple batches.
- We only have to do context processing.
"""

import logging
from dataclasses import dataclass
from typing import Any, Iterable, Sequence

from base.fastforward.batching import RequestInRound, Round, Tokens

log = logging.getLogger(__name__)


@dataclass(frozen=True)
class TokensWithMetadata:
    """A round with additional metadata."""

    tokens: Tokens
    metadata: Any = None


def _to_tokens_with_metadata(
    tokens: TokensWithMetadata | Tokens,
) -> TokensWithMetadata:
    if isinstance(tokens, TokensWithMetadata):
        return tokens
    else:
        return TokensWithMetadata(tokens)


def get_rounds(
    token_stream: Iterable[TokensWithMetadata | Tokens | None],
    max_round_size: int,
    max_seq_length: int,
    num_caches: int,
    mrfa_mode: bool = False,  # If true, limit number of requests in round to 1 to support MRFA
) -> Iterable[Round]:
    """Convert a stream of tokens into a stream of rounds.

    Args:
        token_stream (Iterable[TokensWithMetadata[T] | Tokens | None]):
            A stream of token lists. If a token list is None, the current round
            should be emitted. This is used to signal that we do not want to wait
            for more tokens before starting to process the next round.
        max_round_size (int): The maximal size of a round.
        max_seq_length (int): The maximal sequence length.
        num_caches (int): The number of attention caches.
        mrfa_mode (bool): If true, limit number of requests in round to 1 to support MRFA

    Returns:
        An iterable of rounds, consisting of a list of RequestInRound objects and a list of tokens.
    """

    def new_round() -> tuple[list[RequestInRound], list[int]]:
        return [], []

    def is_round_full(
        round_tokens: Sequence[int],
        requests_in_round: Sequence[RequestInRound],
        additional_tokens: int,
    ):
        token_limit = len(round_tokens) + additional_tokens > max_round_size
        request_limit = len(requests_in_round) >= num_caches

        # To support multi-request Flash Attention, only 1 request is allowed
        # per round.
        num_requests_limit = mrfa_mode and len(requests_in_round) >= 1

        log.debug(
            "Round full? token_limit=%s, request_limit=%s, num_requests_limit=%s",
            token_limit,
            request_limit,
            num_requests_limit,
        )
        return token_limit or request_limit or num_requests_limit

    requests_in_round, round_tokens = new_round()
    for tokens_with_metadata in token_stream:
        assert bool(round_tokens) == bool(requests_in_round)
        if tokens_with_metadata is None:
            if round_tokens:
                yield Round(round_tokens, requests_in_round)
                requests_in_round, round_tokens = new_round()
            continue
        tokens_with_metadata = _to_tokens_with_metadata(tokens_with_metadata)
        tokens = tokens_with_metadata.tokens
        if len(tokens) > max_round_size:
            raise ValueError(
                f"Token list too long: {len(tokens)} > {max_round_size}."
                "This is currently not supported in the embedder service."
            )
        if len(tokens) > max_seq_length:
            raise ValueError(
                f"Token list too long: {len(tokens)} > {max_seq_length}."
                "This is currently not supported in the embedder service."
            )
        if len(tokens) == 0:
            log.error("Token list is empty.")
            raise ValueError("Token list is empty.")
        if is_round_full(round_tokens, requests_in_round, len(tokens)):
            if round_tokens:
                yield Round(round_tokens, requests_in_round)
                requests_in_round, round_tokens = new_round()

        assert len(round_tokens) + len(tokens) <= max_round_size
        assert len(requests_in_round) <= num_caches

        request_in_round = RequestInRound(
            num_tokens=len(tokens),
            cache_idx=len(requests_in_round),
            round_start_idx=len(round_tokens),
            metadata=tokens_with_metadata.metadata,
        )
        requests_in_round.append(request_in_round)
        round_tokens.extend(tokens)

    # emit the remaining tokens
    if round_tokens or requests_in_round:
        yield Round(round_tokens, requests_in_round)

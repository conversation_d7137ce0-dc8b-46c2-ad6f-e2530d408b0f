"""Tests for the embedder handler."""

from concurrent.futures import ThreadPoolExecutor

import numpy as np
import pytest

from base.fastforward import fwd_testmodel
from services.embedder_host.server import (
    embedder_handler,
    embedder_model,
)


def test_handler_raises_timeout_error():
    """Tests that the handler raises a TimeoutError when a request times out."""
    model = embedder_model.NullEmbedderModel()
    handler = embedder_handler.QueueingEmbedderHandler(
        "testmodel",
        model=model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=100,
    )
    tokens = list(range(10))
    with ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(
            handler.calculate_inference_artifacts,
            [tokens],
            request_id="test_rid",
            timeout_s=0.0,
        )
        with pytest.raises(TimeoutError):
            future.result()
    assert model.num_calls == 0  # we avoid the GPU work for the timed out request

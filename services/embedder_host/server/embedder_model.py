"""Embedder and reranker services logic."""

import time
from dataclasses import dataclass
from typing import Callable, Protocol, Sequence, TypeVar

import numpy as np
import opentelemetry.context
import opentelemetry.trace
import prometheus_client
import structlog
import torch

from base.fastforward import batching, fwd

opentelemetry_tracer = opentelemetry.trace.get_tracer(__name__)

log = structlog.get_logger()


# Future plans for this service:
# - CUDA graphs & warmup batch sizes
# - Add support for request priorities.
# - Cache embedding requests?

# Exponentially increasing buckets. Min is ~1ms and max is ~32s.
INF = float("inf")
buckets = tuple((1.3**i - 1) / 300.0 for i in range(36)) + (INF,)

_step_latency_s = prometheus_client.Histogram(
    "au_embedder_service_step_fn_latency_s",
    "Latency of running individual forward passes of the model.",
    ["model_name"],
    buckets=buckets,
)
_round_size = prometheus_client.Histogram(
    "au_embedder_service_round_size",
    "Summary for the number of requests in a round",
    ["model_name"],
    buckets=(1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, INF),
)
_tokens_processed_counter = prometheus_client.Counter(
    "au_embedder_service_tokens_processed_counter",
    "Counts the number of tokens processed by the embedder service.",
    ["model_name"],
)

_NUM_ATTENTION_CACHES_DEFAULT = 10


@dataclass
class EmbeddingOutput:
    """A model output for the retrieval inference services."""

    embeddings: np.ndarray
    """An embedding of shape (1, embedding_size)"""


T = TypeVar("T", covariant=True)


class RetrievalInferenceModel(Protocol[T]):
    """A model for the retrieval inference services."""

    def get_num_attention_caches(self) -> int:
        raise NotImplementedError()

    def process_round(self, batch: batching.Round) -> Sequence[T]:
        """Process a batch of tokens.

        Args:
            batch (batching.Round): A batch of tokens and their corresponding
            request info.

        Returns:
            Sequence[T]: A sequence of inference outputs, one for each request in the batch.
                For embedding models, the inference outputs are tensors of dimension (1, embedding_size).
                For reranker models, the inference outputs are dictionaries.
        """
        raise NotImplementedError()


# Null models


class NullEmbedderModel(RetrievalInferenceModel[EmbeddingOutput]):
    """A dummy embedder model producing dummy emeddings.

    This model is used for testing and debugging. It uses the first token as the embedding value.
    """

    def __init__(self, emb_dim: int = 4):
        self.emb_dim = emb_dim
        self.num_calls = 0  # Used for testing

    def get_num_attention_caches(self) -> int:
        return _NUM_ATTENTION_CACHES_DEFAULT

    def process_round(self, batch: batching.Round) -> Sequence[EmbeddingOutput]:
        """Returns a dummy embedding."""
        self.num_calls += 1
        result = []
        for rir in batch.requests_in_round:
            first_token = batch.tokens[rir.round_start_idx]
            request_embedding = np.full((self.emb_dim,), float(first_token))
            result.append(EmbeddingOutput(embeddings=request_embedding))
        return result


# FFWD models


class GenericRetrievalInferenceModelFFWD(RetrievalInferenceModel[T]):
    """A model for a retrieval inference service."""

    def __init__(
        self,
        model_name: str,
        step_fn: fwd.ForwardStepFn,
        attn_factory: fwd.AttentionFactory,
        max_length: int,
        round_sizes: Sequence[int],
        logits_to_output_fn: Callable[[Sequence[int], np.ndarray], T],
        embedding_dtype: torch.dtype = torch.float16,
        num_attention_caches: int = _NUM_ATTENTION_CACHES_DEFAULT,
        use_multi_request_attention: bool = False,
        do_warmup: bool = False,
    ):
        self.logits_to_output_fn = logits_to_output_fn
        self._model_name = model_name

        self._step_fn = step_fn
        self._max_length = max_length
        self._embedding_dtype = embedding_dtype
        self._num_attention_caches = num_attention_caches
        self._use_multi_request_attention = use_multi_request_attention

        max_round_size, round_sizes = batching.process_round_sizes(round_sizes)
        if max_length > max_round_size:
            raise ValueError(
                f"max_length ({max_length}) > max_round_size ({max_round_size}). We "
                "currently do not support splitting sequences in the embedder "
                "batching logic."
            )
        self._round_sizes = round_sizes
        self._max_round_size = max_round_size

        attn = attn_factory.create_cache_pool(
            max_length=max_length, num_attention_caches=num_attention_caches
        )
        self._round_attn = batching.RoundAttention(
            max_round_size=max(round_sizes), mc_attn=attn
        )
        del attn_factory  # we only need one attention object
        if do_warmup:
            self.warm_up_round_sizes()

    def warm_up_round_sizes(self):
        log.info("Starting warmup for %s", self._model_name)
        start = time.time()
        self._round_attn.reset()
        for round_size in self._round_sizes:
            assert (
                round_size <= self._max_length
            ), "Model shouldn't specify round sizes it can't handle."
            log.info("Starting warmup %d for %s", round_size, self._model_name)
            self._step_fn([0] * round_size, self._round_attn)
            self._round_attn.reset()
            log.info("Warm up %d completed for %s", round_size, self._model_name)
        warmup_time = time.time() - start
        log.info("Warmup complete for %s in %f seconds", self._model_name, warmup_time)

    def get_num_attention_caches(self) -> int:
        return self._num_attention_caches

    def process_round(self, batch: batching.Round) -> Sequence[T]:
        """Process a batch of tokens.

        Args:
            round (batching.Round): A batch of tokens and their corresponding
            request info.

        Returns:
            Sequence[T]: A sequence of output artifacts,
            one for each request in the batch.
        """
        if len(batch.tokens) > self._max_round_size:
            raise ValueError(
                f"Round size ({len(batch.tokens)}) > max_round_size ({self._max_round_size}). "
                "We currently do not support splitting sequences in the embedder service."
            )
        if any(rir.num_tokens > self._max_length for rir in batch.requests_in_round):
            raise ValueError(
                "We currently do not support splitting sequences in the embedder service."
            )
        if len(batch.tokens) == 0:
            raise ValueError(f"Round size ({len(batch.tokens)}) == 0. ")
        if len(batch.requests_in_round) > self._num_attention_caches:
            raise ValueError(
                f"Round size ({len(batch.requests_in_round)}) > num_attention_caches ({self._num_attention_caches}). "
            )
        if batch.has_token_padding or batch.has_request_padding:
            raise ValueError("Round is already padded. Not supported.")
        _tokens_processed_counter.labels(self._model_name).inc(len(batch.tokens))

        if self._use_multi_request_attention:
            assert len(batch.requests_in_round) == 1, len(batch.requests_in_round)
            batch, _ = batch.sort_and_extend_requests_for_multirequest_attention(
                # We only support single-request attention for now for mfra in embedders.
                max_requests_in_round=1,
                max_small_request_size=None,
            )
        batch = batch.pad_to_next_round_size(
            round_sizes=self._round_sizes,
        )
        assert len(batch.tokens) <= self._max_round_size
        self._round_attn.reset()
        self._round_attn.load_requests(batch)

        _round_size.labels(self._model_name).observe(len(batch.tokens))
        # Synchronize before and at the end of time measurement to ensure
        # accurate measurements. This may cost a bit of efficiency, but at
        # least we know how close we are.
        torch.cuda.synchronize()
        with _step_latency_s.labels(self._model_name).time():
            embeddings = self._step_fn(batch.tokens, self._round_attn).checked_cast(
                torch.Tensor
            )
            torch.cuda.synchronize()
        assert len(embeddings.shape) == 2
        # find final tokens given request lengths
        last_token_embeddings = []
        for rir in batch.requests_in_round:
            if batch.has_request_padding and rir.num_tokens == 0:
                # When multi request attention is on, we are adding some dummy requests.
                # We can catch these dummy requests by looking for num_tokens == 0.
                # We don't want to grab embeddings for these requests so we skip them.
                continue

            embeddings_in_round = (
                embeddings[rir.round_start_idx : rir.round_start_idx + rir.num_tokens]
                .to(dtype=self._embedding_dtype)
                .numpy(force=True)
            )
            tokens_in_round = batch.tokens[
                rir.round_start_idx : rir.round_start_idx + rir.num_tokens
            ]
            last_token_embeddings.append(
                self.logits_to_output_fn(tokens_in_round, embeddings_in_round)
            )
        return last_token_embeddings


class EmbedderModelFFWD(GenericRetrievalInferenceModelFFWD[EmbeddingOutput]):
    """A model for an embedding service."""

    def __init__(self, *args, **kwargs):
        def _logits_to_output_fn(
            tokens: Sequence[int], logits: np.ndarray
        ) -> EmbeddingOutput:
            return EmbeddingOutput(embeddings=logits[-1, :])

        super().__init__(logits_to_output_fn=_logits_to_output_fn, *args, **kwargs)

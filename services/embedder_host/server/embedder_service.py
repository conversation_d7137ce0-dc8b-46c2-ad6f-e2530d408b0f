"""Entry point of the embedder service."""

import argparse
import logging
import os
import pathlib
import threading
import time
import typing
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

import grpc
import grpc_interceptor
import numpy as np
import opentelemetry
import opentelemetry.instrumentation.grpc
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import Counter, Histogram, start_http_server

import base.feature_flags
import base.tracing
from base.fastforward import fwd
from base.fastforward.cached_attention import SplitHeadModes
from base.fastforward.codegen import fwd_codegen
from base.fastforward.llama import fwd_llama, fwd_llama_fp8
from base.fastforward.starcoder import fwd_starcoder, fwd_starcoder_fp8
from base.logging.struct_logging import setup_struct_logging
from base.proto import tensor
from base.python.signal_handler.signal_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services.embedder_host import embedder_pb2, embedder_pb2_grpc
from services.embedder_host.server import (
    embedder_handler,
    embedder_model,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.model_arch import ModelArch
from services.lib.request_context.request_context import RequestContext

tracer = base.tracing.setup_opentelemetry()
logger = structlog.get_logger()

_indexing_slots_full = Counter(
    "au_embedder_service_indexing_slots_full_counter",
    "Count of indexing requests aborted due to exhausting indexing slots",
    ["model_name"],
)

_indexing_chunk_lengths_histogram = Histogram(
    "au_embedder_service_indexing_chunk_lengths_histogram",
    "Histogram of the length of indexing chunks",
    ["model_name"],
    buckets=[0, 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, float("inf")],
)

_indexing_chunks_per_request_histogram = Histogram(
    "au_embedder_service_indexing_chunks_per_request_histogram",
    "Histogram of the number of indexing chunks per request",
    ["model_name"],
    buckets=[0, 1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 4096, float("inf")],
)


@dataclass_json
@dataclass
class Config:
    """Configuration for the embedder service.

    Currently, the Config is specified in deploy.jsonnet.
    """

    port: int

    # if server MTLS should be used
    mtls: bool
    server_cert_path: str
    server_key_path: str
    root_cert_path: str

    # model architecture specification
    model_arch: ModelArch
    max_seq_length: int

    model_name: str

    # path to the weights.
    # the contents of the weights path is model arch specific.
    weights_path: str
    weights_sha256: str

    # the number of rpc threads to use
    max_rpc_threads: int
    non_indexing_threads_min: int

    # Split the model across this many GPUs
    model_parallelism: int

    round_sizes: list[int]

    num_attention_caches: int

    # The maximal number of chunks (not requests) in the queue for each
    # tenant and request type. The check is only performed when the first chunk
    # of a request is added to the queue. This is to prevent long files from being
    # rejected half-way through processing.
    indexing_queue_size: int
    query_queue_size: int

    # static feature flag; one of "legacy", "locking", or "queueing"
    embedder_host_handler: str

    feature_flags_sdk_key_path: typing.Optional[str] = None

    # timeout for query requests
    query_timeout_s: float = 1.0

    # timeout for index requests
    index_timeout_s: float = 10.0

    use_cuda_graphs: bool = False

    prioritization_over_fairness: bool = True

    # Grace period for the server to shutdown
    shutdown_grace_period_s: float = 20.0

    # Kernel selection flags
    pre_attention_kernel_fusion: bool = False
    use_register_tokens_kernel: bool = False


class IndexingInterceptor(grpc_interceptor.ServerInterceptor):
    """Intercept indexing requests and limit the number of concurrent requests.

    As this behavior is implemented in an interceptor, it can be sequenced
    before other expensive interceptors to avoid their cost when we're just
    going to reject the request.
    """

    def __init__(self, model_name: str, max_open_indexing_requests: int):
        self.counter = _indexing_slots_full.labels(model_name)
        self.max_open_indexing_requests = max_open_indexing_requests
        self.indexing_request_semaphore = threading.BoundedSemaphore(
            max_open_indexing_requests
        )

    def intercept(
        self,
        method: typing.Callable,
        request_or_iterator: typing.Any,
        context: grpc.ServicerContext,
        method_name: str,
    ) -> typing.Any:
        parsed = grpc_interceptor.parse_method_name(method_name)
        if (
            parsed.method == "CalculateEmbedding"
            and isinstance(request_or_iterator, embedder_pb2.EmbeddingRequest)
            and request_or_iterator.request_type
            == embedder_pb2.EmbeddingRequestType.INDEXING
        ):
            indexing_slot_acquired = self.indexing_request_semaphore.acquire(
                blocking=False
            )  # fail fast if we are over the limit
            if not indexing_slot_acquired:
                self.counter.inc()
                context.abort(
                    code=grpc.StatusCode.RESOURCE_EXHAUSTED,
                    details="No remaining slots available for indexing requests.",
                )
            # Release the indexing slot when the request is done.
            context.add_callback(self.indexing_request_semaphore.release)
        return method(request_or_iterator, context)


class EmbedderServices(embedder_pb2_grpc.EmbedderServicer):
    """Embedder RPC server."""

    def __init__(
        self,
        config: Config,
        handler: embedder_handler.QueueingEmbedderHandler,
    ):
        self.config = config
        self.handler = handler

    def CalculateEmbedding(
        self,
        request: embedder_pb2.EmbeddingRequest,
        context: grpc.ServicerContext,
    ):
        start_time = time.time()
        request_context = RequestContext.from_grpc_context(context)
        request_context.bind_context_logging()
        tokens = tensor.to_numpy(request.tokens)
        lengths = tensor.to_numpy(request.lengths)
        logger.info(
            "Embedder: processing request with shape %s and lengths %s",
            tokens.shape,
            list(lengths),
        )
        peer = context.peer()
        peer_identity = context.peer_identity_key()
        logger.info(
            "Embedder: request from %s (%s), source namespace %s",
            peer,
            peer_identity,
            request.source_namespace,
        )
        if len(tokens.shape) != 2:
            logger.error(
                "Embedder: invalid tokens shape: %s",
                tokens.shape,
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="tokens must be 2-dimensional",
            )
        if len(lengths.shape) != 1:
            logger.error(
                "Embedder: invalid lengths shape: %s",
                lengths.shape,
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="lengths must be 1-dimensional",
            )
        if tokens.shape[0] != lengths.shape[0]:
            logger.error(
                "Embedder: invalid tokens and lengths shapes: %s, %s",
                tokens.shape,
                lengths.shape,
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="tokens and lengths must have the same number of rows",
            )
        if (tokens >= self.config.model_arch.vocab_size).any():
            logger.error(
                "Embedder: invalid tokens. Tokens must be <= vocab_size.",
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="tokens must be less than vocab_size",
            )
        if (tokens < 0).any():
            logger.error(
                "Embedder: invalid tokens. Tokens must be non-negative",
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="tokens must be non-negative",
            )
        # need to convert to List[List[int]] as numpy does not have ragged arrays
        tokens_py = []
        for i in range(tokens.shape[0]):
            _indexing_chunk_lengths_histogram.labels(self.config.model_name).observe(
                lengths[i]
            )
            tokens_py.append(tokens[i, : lengths[i]])

        _indexing_chunks_per_request_histogram.labels(self.config.model_name).observe(
            tokens.shape[0]
        )

        try:
            if request.request_type == embedder_pb2.EmbeddingRequestType.QUERY:
                priority = embedder_handler.Priority.URGENT
                request_type_desc = "query"
                timeout_s = self.config.query_timeout_s
            else:
                priority = embedder_handler.Priority.NORMAL
                request_type_desc = "indexing"
                timeout_s = self.config.index_timeout_s

            embeddings = self.handler.calculate_inference_artifacts(
                request_id=request_context.request_id,
                source_namespace=request.source_namespace,
                tokens=tokens_py,
                timeout_s=timeout_s,
                request_type_desc=request_type_desc,
                priority=priority,
            )
            embeddings = np.stack([e.embeddings for e in embeddings])
            assert embeddings.shape[0] == len(lengths)

            response = embedder_pb2.EmbeddingResponse()
            response.results.MergeFrom(tensor.to_tensor(embeddings))
            logger.info(
                "Embedder: finished request with shape %s in %.3fs",
                tokens.shape,
                time.time() - start_time,
            )
            return response
        except grpc.RpcError as ex:
            logger.error("Embedder request failed: %s", ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
        except embedder_handler.QueueFullError as ex:
            logger.warning(
                "Embedder request failed with queue full error. %s",
                ex,
            )
            context.abort(
                code=grpc.StatusCode.RESOURCE_EXHAUSTED,
                details="Embedder queue is full",
            )
        except TimeoutError as ex:
            logger.info(
                "Embedder request failed with timeout error. %s",
                ex,
            )
            context.abort(
                code=grpc.StatusCode.DEADLINE_EXCEEDED,
                details="Embedder request timed out",
            )
        except RuntimeError as ex:
            logger.error(
                "Embedder request failed with runtime error. Exiting as this is "
                "likely a unrecoverable GPU error. %s",
                ex,
            )
            os._exit(1)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.error("Embedder request failed: %s", ex)
            logging.exception(ex)
            raise


def _load(path: str):
    return pathlib.Path(path).read_bytes()


def _setup_model(config: Config):
    logging.info(
        "Loading model %s, weights %s",
        config.model_name,
        config.weights_path,
    )
    if config.model_parallelism > 1:
        raise ValueError(
            "Model parallelism is not supported for the embedding service."
        )

    arch_name = config.model_arch.arch_type
    arch_is_fp8 = arch_name.endswith("_FP8")
    base_arch_name = arch_name[:-4] if arch_is_fp8 else arch_name
    # If the model arch provides max_position_embeddings, use that. Otherwise, use the given
    # max_seq_length of the embedder.
    max_position_embeddings = (
        config.model_arch.max_position_embeddings or config.max_seq_length
    )
    model_spec = fwd.ModelSpec(
        name=config.model_name,
        checkpoint_path=config.weights_path,
        checkpoint_sha256=config.weights_sha256,
        vocab_size=config.model_arch.vocab_size,
        num_layers=config.model_arch.num_layers,
        num_heads=config.model_arch.num_heads,
        emb_dim=config.model_arch.emb_dim,
        head_dim=config.model_arch.head_dim,
        output_projection_dim=config.model_arch.output_projection_dim,
        rotary_pct=config.model_arch.rotary_pct,
        rotary_theta=config.model_arch.rotary_theta,
        rotary_scaling_factor=config.model_arch.rotary_scaling_factor,
        max_position_embeddings=max_position_embeddings,
        unscaled_max_position_embeddings=max_position_embeddings,
        norm_eps=config.model_arch.norm_eps,
    )
    if base_arch_name == "CODEGEN":
        if arch_is_fp8:
            raise ValueError("FP8 is not supported for CODEGEN")
        step_fn = fwd_codegen.generate_step_fn(
            model_spec,
            output_type=fwd.OutputTensorType.EMBEDDING,
        )
        attn_factory = fwd_codegen.CodeGenAttentionFactory(model_spec)
    elif base_arch_name == "STARCODER":
        if arch_is_fp8:
            step_fn = fwd_starcoder_fp8.generate_step_fn(
                model_spec=model_spec,
                auto_capture_graphs=config.use_cuda_graphs,
                batch_sizes=config.round_sizes if config.use_cuda_graphs else None,
                output_type=fwd.OutputTensorType.EMBEDDING,
            )
        else:
            step_fn = fwd_starcoder.generate_step_fn(
                model_spec,
                auto_capture_graphs=config.use_cuda_graphs,
                batch_sizes=config.round_sizes if config.use_cuda_graphs else None,
                output_type=fwd.OutputTensorType.EMBEDDING,
            )
        attn_factory = fwd_starcoder.StarcoderAttentionFactory(
            model_spec=model_spec,
            pre_attention_kernel_fusion=config.pre_attention_kernel_fusion,
            use_register_tokens_kernel=config.use_register_tokens_kernel,
        )
    elif base_arch_name == "LLAMA":
        model_spec = fwd_llama.model_specs.LlamaModelSpec(
            num_queries_per_head=config.model_arch.num_queries_per_head,
            mlp_dim_divisible_by=config.model_arch.mlp_dim_divisible_by,
            ffn_dim_multiplier=config.model_arch.ffn_dim_multiplier,
            attn_split_head_mode=SplitHeadModes[config.model_arch.attn_split_head_mode],
            qkv_only_bias=config.model_arch.qkv_only_bias,
            **model_spec.__dict__,
        )
        logging.info(model_spec)
        if arch_is_fp8:
            step_fn = fwd_llama_fp8.generate_step_fn(
                model_spec,
                auto_capture_graphs=config.use_cuda_graphs,
                batch_sizes=config.round_sizes if config.use_cuda_graphs else None,
                output_type=fwd.OutputTensorType.EMBEDDING,
            )
        else:
            step_fn = fwd_llama.generate_step_fn(
                model_spec,
                auto_capture_graphs=config.use_cuda_graphs,
                batch_sizes=config.round_sizes if config.use_cuda_graphs else None,
                output_type=fwd.OutputTensorType.EMBEDDING,
            )
        attn_factory = fwd_llama.LlamaAttentionFactory(
            ms=model_spec,
            pre_attention_kernel_fusion=config.pre_attention_kernel_fusion,
            use_register_tokens_kernel=config.use_register_tokens_kernel,
        )
    else:
        raise ValueError(f"Unsupported arch type: {config.model_arch.arch_type}")
    return (step_fn, attn_factory)


def _serve(config: Config, shutdown_event: threading.Event):
    # We reserve some grpc server threads for tasks other than indexing, so we
    # need to limit the number of indexing requests that can be processed at
    # once.
    max_indexing_requests = config.max_rpc_threads - config.non_indexing_threads_min

    server = grpc.server(
        ThreadPoolExecutor(max_workers=config.max_rpc_threads),
        interceptors=[
            # Sequencing this interceptor first minimizes the cost of indexing burst.
            # We don't value opentelemetry or metrics for quickly rejected indexing requests.
            IndexingInterceptor(config.model_name, max_indexing_requests),
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
        ],
        # Embedder service controls queueing. Each request needs one
        # thread to be processed. So we set this number to the number
        # of threads to reject requests if the queue is full.
        maximum_concurrent_rpcs=config.max_rpc_threads,
    )

    (step_fn, attn_factory) = _setup_model(config)

    if config.embedder_host_handler == "queueing":
        embedder = embedder_model.EmbedderModelFFWD(
            model_name=config.model_name,
            step_fn=step_fn,
            attn_factory=attn_factory,
            max_length=max(config.round_sizes),
            round_sizes=config.round_sizes,
            num_attention_caches=config.num_attention_caches,
            do_warmup=config.use_cuda_graphs,
        )
        handler = embedder_handler.QueueingRetrievalInferenceHandler(
            model_name=config.model_name,
            model=embedder,
            max_round_size=max(config.round_sizes),
            max_seq_length=config.max_seq_length,
            vocab_size=config.model_arch.vocab_size,
            start_queued_requests_counter=True,
            indexing_queue_size=config.indexing_queue_size,
            query_queue_size=config.query_queue_size,
            prioritization_over_fairness=config.prioritization_over_fairness,
        )
        handler.start()
    else:
        raise ValueError(
            f"Unknown embedder host handler: {config.embedder_host_handler}"
        )
    embedder_pb2_grpc.add_EmbedderServicer_to_server(
        EmbedderServices(
            config,
            handler,
        ),
        server,
    )
    service_names = (
        embedder_pb2.DESCRIPTOR.services_by_name["Embedder"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    if config.mtls:
        server_credentials = grpc.ssl_server_credentials(
            [(_load(config.server_key_path), _load(config.server_cert_path))],
            root_certificates=_load(config.root_cert_path),
            require_client_auth=True,
        )
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )


def main():
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)

    context = base.feature_flags.Context.setup(path)
    base.feature_flags.set_global_context(context)

    _serve(config, handler.get_shutdown_event())


if __name__ == "__main__":
    main()

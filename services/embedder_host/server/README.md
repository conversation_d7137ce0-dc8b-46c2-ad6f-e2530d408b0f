# Server code for the embedder.

The embedder host is a service that calculates the embeddings based on a list of tokens.
The embeddings then can be used for vector-based similarity searches (Dense Retrieval).

The server is divided into three parts:
- The embedder model, responsible to execute individual rounds.
- The batching module, responsible to turn a stream of lists of tokens into a stream of batches.
- The handler, responsible for converting multiple incoming requests into a stream of lists of tokens.

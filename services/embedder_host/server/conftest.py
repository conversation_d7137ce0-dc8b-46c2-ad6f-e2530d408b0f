"""Test fixtures for the embedder host."""

import pytest

import base.fastforward.fwd as fwd
from base.fastforward.starcoder import fwd_starcoder


@pytest.fixture(scope="module")
def starethanol_model() -> (
    tuple[fwd.ForwardStepFn, fwd.AttentionFactory, fwd.ModelSpec]
):
    model_spec = fwd.ModelSpec(
        name="starethanol6-16-1-proj1024",
        checkpoint_path="/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_1024_2000/global_step2000_v2",
        checkpoint_sha256="739515f2e6877908d288ff64add7166c936d1f67c45dd9a4b5942a22dd2bcb67",
        vocab_size=51200,
        num_layers=24,
        num_heads=16,
        emb_dim=2048,
        head_dim=128,
        norm_eps=1e-5,
    )

    step_fn = fwd_starcoder.generate_step_fn(
        model_spec, output_type=fwd.OutputTensorType.EMBEDDING
    )
    attn_factory = fwd_starcoder.StarcoderAttentionFactory(model_spec)
    return step_fn, attn_factory, model_spec

"""Tests for embedder_model.py."""

import json
from pathlib import Path

import numpy as np
import pytest

from base.fastforward import batching, fwd_testmodel
from services.embedder_host.server import embedder_batching, embedder_model


def round_from_token_lists(
    token_lists: list[list[int]],
) -> batching.Round:
    """Converts a list of token lists into a round."""
    all_tokens = []
    rirs = []
    num_tokens_so_far = 0
    for idx, tokens in enumerate(token_lists):
        all_tokens += tokens
        rirs.append(
            batching.RequestInRound(
                num_tokens=len(tokens),
                cache_idx=idx,
                round_start_idx=num_tokens_so_far,
            )
        )
        num_tokens_so_far += len(tokens)
    return batching.Round(tokens=all_tokens, requests_in_round=rirs)


def test_model(
    max_length: int = 128,
    round_sizes: tuple[int, ...] = (128,),
    num_attention_caches: int = 2,
) -> embedder_model.EmbedderModelFFWD:
    """Returns a test model."""
    model_spec = fwd_testmodel.get_dummy_modelspec()
    step_fn = fwd_testmodel.generate_step_fn(model_spec)
    attention_factory = fwd_testmodel.get_attention_factory(
        model_spec, ignore_first_n=max_length
    )

    return embedder_model.EmbedderModelFFWD(
        model_name="Testmodel",
        step_fn=step_fn,
        attn_factory=attention_factory,
        max_length=max_length,
        round_sizes=round_sizes,
        num_attention_caches=num_attention_caches,
    )


def test_process_round_model():
    """Tests the process_round function with the fwd_testmodel."""
    model = test_model()
    model_spec = fwd_testmodel.get_dummy_modelspec()
    emb_dim = model_spec.vocab_size

    tokens = [1, 2, 3]

    round = round_from_token_lists([tokens])
    embeddings = model.process_round(round)
    assert len(embeddings) == 1
    assert len(embeddings[0].embeddings) == emb_dim

    round = round_from_token_lists([[1, 2, 3], [4, 5, 6]])
    embeddings = model.process_round(round)
    assert len(embeddings) == 2
    assert len(embeddings[0].embeddings) == emb_dim
    assert len(embeddings[1].embeddings) == emb_dim


def test_process_round_raises_on_large_round():
    """Tests that process_round raises ValueError when a round is too large."""
    model = test_model()
    max_length = model._max_length
    with pytest.raises(ValueError):
        model.process_round(round_from_token_lists([list(range(max_length + 1))]))


def test_process_round_raises_on_empty_round():
    """Tests that process_round raises ValueError when a round is empty."""
    model = test_model()
    with pytest.raises(ValueError):
        model.process_round(round_from_token_lists([[]]))


def test_process_long_round_with_short_maxlen():
    """Tests that process_round does not raise a ValueError as long as the requests in round are short enough."""
    model = test_model(max_length=10, round_sizes=(20,))
    # smoke test: the following should not raise a ValueError
    model.process_round(round_from_token_lists([list(range(10)), list(range(8))]))


def test_chunk_order():
    """Tests that the order of the chunks is respected."""
    model = test_model()

    tokens1 = [[1, 2, 3], [4, 5, 6, 7, 8, 9, 10]]
    embeddings1 = [
        output.embeddings
        for output in model.process_round(round_from_token_lists(tokens1))
    ]

    tokens2 = [[4, 5, 6, 7, 8, 9, 10], [1, 2, 3]]
    embeddings2 = [
        output.embeddings
        for output in model.process_round(round_from_token_lists(tokens2))
    ]

    assert np.allclose(embeddings1, embeddings2[::-1])


def test_results_independent_of_request_packing():
    """Tests that the results are independent of whether chunks are in the same request."""
    model = test_model()

    embeddings1 = [
        output.embeddings
        for output in model.process_round(
            round_from_token_lists([[1, 2, 3], [4, 5, 6, 7, 8, 9, 10]])
        )
    ]

    embeddings2_1 = [
        output.embeddings
        for output in model.process_round(round_from_token_lists([[1, 2, 3]]))
    ]
    embeddings2_2 = [
        output.embeddings
        for output in model.process_round(
            round_from_token_lists([[4, 5, 6, 7, 8, 9, 10]])
        )
    ]

    assert np.allclose(embeddings1[0], embeddings2_1)
    assert np.allclose(embeddings1[1], embeddings2_2)


@pytest.mark.parametrize("batch_size", [1, 2, 10])
def test_model_produces_correct_embedding_with_batchsize(
    starethanol_model, batch_size: int
):
    """Tests that the model produces the correct embedding with batch size."""
    step_fn, attn_factory, model_spec = starethanol_model
    max_length = 128
    model = embedder_model.EmbedderModelFFWD(
        model_name="testmodel",
        step_fn=step_fn,
        attn_factory=attn_factory,
        max_length=max_length,
        round_sizes=[128],
        num_attention_caches=batch_size + 1,
    )
    tokens = [[4, 5, 6, 7, 8, 9, 10]] * batch_size
    embedding = model.process_round(round_from_token_lists(tokens))
    assert len(embedding) == batch_size
    assert len(embedding[0].embeddings) == 1024
    # TODO: test against a more meaningful embedding
    np.testing.assert_almost_equal(
        embedding[0].embeddings[:3],
        np.array([-1.113, -4.11, 1.671], dtype=np.float16),
        decimal=1,  # low precision seems to be necessary as the CI GPUs are different enough.
    )


def test_raises_on_batch_too_large(starethanol_model):
    """Tests that data is corrupted if we send too many requests and ignore the check for the number of caches."""
    step_fn, attn_factory, model_spec = starethanol_model
    max_length = 1024
    embedder = embedder_model.EmbedderModelFFWD(
        model_name="testmodel",
        step_fn=step_fn,
        attn_factory=attn_factory,
        max_length=max_length,
        round_sizes=(256, 512, 1024, 2048),
        num_attention_caches=4,  # intentially configured to be too small
    )

    tokens = [tuple(range(123)) for _ in range(100)]

    batches = embedder_batching.get_rounds(
        tokens,
        max_round_size=2048,
        num_caches=4,  # matches num_attention_caches above
        max_seq_length=max_length,
    )

    # smoketest: this should not raise an exception
    for batch in batches:
        embedder.process_round(batch)

    batches = embedder_batching.get_rounds(
        tokens,
        max_round_size=2048,
        num_caches=5,  # intentionally too large
        max_seq_length=max_length,
    )

    with pytest.raises(ValueError):
        for batch in batches:
            embedder.process_round(batch)


def test_multi_request_attention(starethanol_model):
    step_fn, attn_factory, model_spec = starethanol_model
    max_length = 128
    model = embedder_model.EmbedderModelFFWD(
        model_name="Testmodel",
        step_fn=step_fn,
        attn_factory=attn_factory,
        max_length=max_length,
        round_sizes=[128],
        num_attention_caches=2,
        use_multi_request_attention=True,
    )

    tokens = [[4, 5, 6, 7, 8, 9, 10]]
    embedding = model.process_round(round_from_token_lists(tokens))
    assert len(embedding) == 1
    assert len(embedding[0].embeddings) == 1024
    # TODO: test against a more meaningful embedding
    np.testing.assert_almost_equal(
        embedding[0].embeddings[:3],
        np.array([-1.113, -4.11, 1.671], dtype=np.float16),
        decimal=1,  # low precision seems to be necessary as the CI GPUs are different enough.
    )

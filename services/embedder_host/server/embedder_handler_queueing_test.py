"""Tests for the embedder handler."""

import functools
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor
from typing import Iterable, Sequence

import numpy as np
import pytest

from base.fastforward import fwd_testmodel
from services.embedder_host.server import embedder_handler, embedder_model

Priority = embedder_handler.Priority


def create_request(
    tokens: Sequence[int] = (1, 2, 3),
    request_id: str | None = None,
    tenant: str = "default_tenant",
    priority: embedder_handler.Priority = embedder_handler.Priority.NORMAL,
    timeout_s: float = 1,
) -> embedder_handler.Request:
    """Sets default values for requests that are convenient for testing."""
    if request_id is None:
        request_id = str(uuid.uuid4())
    return embedder_handler.Request(
        tokens=tokens,
        request_id=request_id,
        source_namespace=tenant,
        priority=priority,
        creation_time=time.time(),
        timeout_s=timeout_s,
    )


def test_basic_request_queue_empty():
    """Tests the basic request queue."""
    queue = embedder_handler.BasicRequestQueue(max_queue_size=10)
    assert queue.pop_task() is None


def test_basic_request_queue_pop():
    """Tests the basic request queue pop."""
    queue = embedder_handler.BasicRequestQueue(max_queue_size=10)
    request = create_request(
        tokens=[1, 2, 3],
        request_id="test_rid",
    )
    queue.add_request(request)
    task = queue.pop_task()
    assert task is not None
    assert task.request.request_id == "test_rid"
    assert task.tokens == [1, 2, 3]

    task2 = queue.pop_task()
    assert task2 is None


def test_fair_request_queue_empty():
    """Tests the fair request queue."""
    queue = embedder_handler.FairRequestQueue(embedder_handler.BasicRequestQueue, 10)
    assert queue.pop_task() is None


def test_fair_request_queue_pop():
    """Tests the fair request queue pop."""
    queue = embedder_handler.FairRequestQueue(embedder_handler.BasicRequestQueue, 10)
    request = create_request()
    queue.add_request(request)
    task = queue.pop_task()
    assert task is not None

    task2 = queue.pop_task()
    assert task2 is None


def test_fair_request_queue_pop_with_tenants():
    """Tests the fair request queue pop with tenants."""
    queue = embedder_handler.FairRequestQueue(embedder_handler.BasicRequestQueue, 10)
    queue.add_request(create_request(tenant="tenant1", request_id="r1"))
    queue.add_request(create_request(tenant="tenant1", request_id="r2"))
    queue.add_request(create_request(tenant="tenant2", request_id="r3"))
    task1 = queue.pop_task()
    assert task1 is not None
    assert task1.request.request_id == "r1"

    task2 = queue.pop_task()
    assert task2 is not None
    assert (
        task2.request.request_id == "r3"
    )  # reordered because of round-robin by tenant

    task3 = queue.pop_task()
    assert task3 is not None
    assert task3.request.request_id == "r2"

    task4 = queue.pop_task()
    assert task4 is None


@pytest.mark.parametrize("check_tenant", [1, 2, 3])
def test_fair_queue_multi_tenant_pop(check_tenant: int):
    """Tests that queue still works with empty tenant queues stored."""
    queue = embedder_handler.FairRequestQueue(
        tenant_queue_factory=lambda queue_size: embedder_handler.PriorityRequestQueue(
            queue_size_per_priority={
                Priority.URGENT: queue_size,
                Priority.NORMAL: queue_size,
            },
            queue_factory=embedder_handler.BasicRequestQueue,
        ),
        queue_size=10,
    )
    queue.add_request(create_request(tenant="tenant1", request_id="r1"))
    queue.add_request(create_request(tenant="tenant2", request_id="r2"))
    queue.add_request(create_request(tenant="tenant3", request_id="r3"))
    _ = queue.pop_task()
    _ = queue.pop_task()
    _ = queue.pop_task()
    # queue is now empty
    task = queue.pop_task()
    assert task is None
    queue.add_request(create_request(tenant=f"tenant{check_tenant}", request_id="r4"))
    task = queue.pop_task()
    assert task is not None
    assert task.request.request_id == "r4"


def test_priority_request_queue_pop():
    """Tests that urgent requests are prioritized over normal ones."""
    queue = embedder_handler.PriorityRequestQueue(
        queue_size_per_priority={
            Priority.NORMAL: 10,
            Priority.URGENT: 10,
        },
        queue_factory=embedder_handler.BasicRequestQueue,
    )
    queue.add_request(create_request(priority=Priority.NORMAL, request_id="r1"))
    queue.add_request(create_request(priority=Priority.URGENT, request_id="r2"))
    task1 = queue.pop_task()
    assert task1 is not None
    assert task1.request.request_id == "r2"


def test_request_queue_pop_with_tenants_and_priority():
    """Tests that fairness overrules priority."""
    queue = embedder_handler.FairRequestQueue(
        tenant_queue_factory=lambda queue_size: embedder_handler.PriorityRequestQueue(
            queue_size_per_priority={
                Priority.NORMAL: queue_size,
                Priority.URGENT: queue_size,
            },
            queue_factory=embedder_handler.BasicRequestQueue,
        ),
        queue_size=10,
    )
    queue.add_request(
        create_request(tenant="tenant1", request_id="r1", priority=Priority.NORMAL)
    )
    queue.add_request(
        create_request(tenant="tenant1", request_id="r2", priority=Priority.URGENT)
    )
    queue.add_request(
        create_request(tenant="tenant2", request_id="r3", priority=Priority.NORMAL)
    )

    taks1 = queue.pop_task()
    assert taks1 is not None
    task2 = queue.pop_task()
    assert task2 is not None
    assert task2.request.request_id == "r3"  # still reordered, despite priority


def test_prioritized_fair_queue():
    """Test for inverted order of queue wrappers: priority more important than fairness."""
    # that urgent requests are prioritized over normal ones even when fairness would prioritize another tenant.
    queue = embedder_handler.PriorityRequestQueue(
        queue_size_per_priority={
            Priority.NORMAL: 10,
            Priority.URGENT: 10,
        },
        queue_factory=lambda queue_size: embedder_handler.FairRequestQueue(
            tenant_queue_factory=lambda queue_size: embedder_handler.BasicRequestQueue(
                max_queue_size=queue_size
            ),
            queue_size=queue_size,
        ),
    )
    queue.add_request(
        create_request(tenant="tenant1", request_id="r1", priority=Priority.NORMAL)
    )
    queue.add_request(
        create_request(tenant="tenant2", request_id="r2", priority=Priority.NORMAL)
    )
    task1 = queue.pop_task()
    assert task1 is not None
    assert task1.request.request_id == "r1"
    queue.add_request(
        create_request(tenant="tenant1", request_id="r3", priority=Priority.URGENT)
    )
    task3 = queue.pop_task()
    assert task3 is not None
    assert task3.request.request_id == "r3"


@pytest.mark.parametrize("request_size", [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
def test_queueing_embedder_handler_with_null_model(request_size: int):
    # def test_queueing_embedder_handler_with_null_model(request_size: int = 1):
    """Tests the embedder handler that uses queues."""
    model = embedder_model.NullEmbedderModel()
    handler = embedder_handler.QueueingEmbedderHandler(
        f"testmodel_{request_size}",
        model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=10,
    )
    tokens = [[x, x, x] for x in range(request_size)]
    with handler:
        response = handler.calculate_inference_artifacts(
            tokens, request_id="test_rid", timeout_s=1
        )
        embeddings = np.stack([e.embeddings for e in response])
        assert embeddings.shape == (request_size, model.emb_dim)
        for i in range(request_size):
            np.testing.assert_array_equal(embeddings[i], np.full((model.emb_dim,), i))


@pytest.mark.parametrize(
    "tenants", [["tenant1"], ["tenant1", "tenant2"], ["tenant1", "tenant2", "tenant3"]]
)
def test_queueing_embedder_handler_with_null_model_with_tenants(tenants: Sequence[str]):
    """Tests the embedder handler that uses queues."""
    model = embedder_model.NullEmbedderModel()
    handler = embedder_handler.QueueingEmbedderHandler(
        "testmodel",
        model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=100,
    )
    thread_pool = ThreadPoolExecutor(max_workers=len(tenants))
    tenant_tokens = [
        [[t_idx * 10 + x] * 3 for x in range(10)] for t_idx in range(len(tenants))
    ]
    futures = []
    for idx, tenant in enumerate(tenants):
        emb_fn = functools.partial(
            handler.calculate_inference_artifacts,
            tokens=list(tenant_tokens[idx]),
            request_id="test_rid",
            source_namespace=tenant,
            timeout_s=1,
        )
        futures.append(thread_pool.submit(emb_fn))
    with handler:
        for idx, future in enumerate(futures):
            r = future.result()
            output = np.stack([e.embeddings for e in r])
            assert output.shape == (10, model.emb_dim)
            np.testing.assert_array_equal(output[:, 0], np.arange(10) + idx * 10)


@pytest.mark.parametrize("num_requests", [1, 2, 10])
def test_queueing_handler_with_test_model(num_requests: int):
    """Tests the embedder handler with the fwd test model."""
    spec = fwd_testmodel.get_dummy_modelspec()
    max_len = 16
    model = embedder_model.EmbedderModelFFWD(
        "testmodel",
        fwd_testmodel.generate_step_fn(fwd_testmodel.get_dummy_modelspec()),
        fwd_testmodel.get_attention_factory(
            fwd_testmodel.get_dummy_modelspec(), ignore_first_n=max_len
        ),
        max_length=max_len,
        round_sizes=[1, 4, 16, 32],
    )
    handler = embedder_handler.QueueingEmbedderHandler(
        "testmodel",
        model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=100,
    )
    tokens = [
        [1, 2, 3],
        [4],
        [5, 6],
        [7, 8, 9],
        [10, 11, 12, 13],
        [14, 15],
        [16, 17],
        [18, 19],
        [20, 21, 22, 23, 24, 25, 26, 27, 28, 29],
        [30, 31, 32, 33, 34, 35, 36],
    ]

    def _run_request(idx: int, tokens: Sequence[Sequence[int]]):
        response = handler.calculate_inference_artifacts(
            tokens, request_id=f"test_rid_{idx}", timeout_s=2
        )
        embeddings = np.stack([e.embeddings for e in response])

        assert embeddings.shape == (10, spec.vocab_size)

    thread_pool = ThreadPoolExecutor(max_workers=num_requests)
    futures = []
    for idx in range(num_requests):
        futures.append(thread_pool.submit(_run_request, idx, tokens))
    with handler:
        for future in futures:
            future.result()


def test_oversized_request_raises_value_error():
    """Tests that a request with too many tokens raises a ValueError."""
    model = embedder_model.NullEmbedderModel()
    max_length = 8
    handler = embedder_handler.QueueingEmbedderHandler(
        "testmodel",
        model,
        max_round_size=10,
        max_seq_length=max_length,
        vocab_size=100,
    )
    tokens = [range(max_length + 1)]
    with handler:
        with pytest.raises(ValueError):
            handler.calculate_inference_artifacts(
                tokens, request_id="test_rid", source_namespace="tenant1", timeout_s=1
            )


def _token_list_stream(
    start: int, token_list_lengths: Sequence[int]
) -> Iterable[Sequence[int]]:
    """Generates a stream of token lists."""
    for length in token_list_lengths:
        yield list(range(start, start + length))
        start += length


def test_requests_dont_mix_up():
    """Tests that tokens of different requests are not mixed up."""
    spec = fwd_testmodel.get_dummy_modelspec()
    round_sizes = [1, 4, 16, 32]
    max_len = 22
    model = embedder_model.EmbedderModelFFWD(
        "testmodel",
        fwd_testmodel.generate_step_fn(spec),
        fwd_testmodel.get_attention_factory(spec, ignore_first_n=max_len),
        max_length=max_len,
        round_sizes=round_sizes,
    )
    handler = embedder_handler.QueueingEmbedderHandler(
        "testmodel",
        model,
        max_round_size=max(round_sizes),
        max_seq_length=max_len,
        vocab_size=spec.vocab_size,
    )
    tokens1 = list(
        _token_list_stream(
            start=0,
            token_list_lengths=[3, 10, 15, 16, 15, 1, 18, 3, 10, 1, 12, 22, 22, 1],
        )
    )
    tokens2 = list(
        _token_list_stream(
            start=500,
            token_list_lengths=[10, 1, 12, 22, 3, 4, 10, 15, 16, 15, 1, 18, 3, 22, 1],
        )
    )

    thread_pool = ThreadPoolExecutor(max_workers=1)
    future1 = thread_pool.submit(
        handler.calculate_inference_artifacts,
        list(tokens1),
        request_id="test_rid1",
        source_namespace="tenant1",
        timeout_s=1,
    )
    future2 = thread_pool.submit(
        handler.calculate_inference_artifacts,
        list(tokens2),
        request_id="test_rid2",
        source_namespace="tenant2",
        timeout_s=1,
    )
    with handler:
        response1 = future1.result()
        response2 = future2.result()

    embeddings1 = np.stack([e.embeddings for e in response1])
    embeddings2 = np.stack([e.embeddings for e in response2])

    assert embeddings1.shape == (len(tokens1), spec.vocab_size)
    assert embeddings2.shape == (len(tokens2), spec.vocab_size)

    handler = embedder_handler.QueueingEmbedderHandler(
        model_name="testmodel",
        model=model,
        max_round_size=max(round_sizes),
        max_seq_length=max_len,
        vocab_size=spec.vocab_size,
    )

    with handler:
        response_locking1 = handler.calculate_inference_artifacts(
            list(tokens1),
            request_id="test_rid1",
            source_namespace="tenant1",
            timeout_s=1,
        )
        response_locking2 = handler.calculate_inference_artifacts(
            list(tokens2),
            request_id="test_rid2",
            source_namespace="tenant2",
            timeout_s=1,
        )

    embeddings_locking1 = np.stack([e.embeddings for e in response_locking1])
    embeddings_locking2 = np.stack([e.embeddings for e in response_locking2])

    np.testing.assert_allclose(embeddings1, embeddings_locking1)
    np.testing.assert_allclose(embeddings2, embeddings_locking2)


def test_handler_forwards_runtime_error():
    """Tests that the handler forwards RuntimeErrors from the model."""
    model = embedder_model.NullEmbedderModel()

    def raise_runtime_error(*args, **kwargs):
        raise RuntimeError("Test error")

    model.process_round = raise_runtime_error
    handler = embedder_handler.QueueingEmbedderHandler(
        model_name="testmodel",
        model=model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=4,
    )
    with handler:
        tokens = [0] * 10
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(
                handler.calculate_inference_artifacts,
                [tokens],
                request_id="test_rid",
                timeout_s=1.0,
            )
            with pytest.raises(RuntimeError):
                future.result()


def test_queueing_handler_continues_after_runtime_error():
    """Tests that the queueing handler continues after a RuntimeError."""
    model = embedder_model.NullEmbedderModel()

    first_call = True
    model_process_round = model.process_round

    def raise_runtime_error(*args, **kwargs) -> Sequence[np.ndarray]:
        nonlocal first_call
        if first_call:
            first_call = False
            raise RuntimeError("Test error")
        return model_process_round(*args, **kwargs)

    model.process_round = raise_runtime_error
    handler = embedder_handler.QueueingEmbedderHandler(
        model_name="testmodel",
        model=model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=100,
    )
    tokens = list(range(10))
    with handler, ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(
            handler.calculate_inference_artifacts,
            [tokens],
            request_id="test_rid",
            timeout_s=1.0,
        )
        with pytest.raises(RuntimeError):
            future.result()
        # The handler should continue to accept new requests.
        future = executor.submit(
            handler.calculate_inference_artifacts,
            [tokens],
            request_id="test_rid",
            timeout_s=1.0,
        )
        future.result()  # second call should not raise


def test_single_token_request(starethanol_model):
    """Tests that the handler can handle a request with a single token."""
    step_fn, attn_factory, spec = starethanol_model
    model = embedder_model.EmbedderModelFFWD(
        model_name="testmodel",
        step_fn=step_fn,
        attn_factory=attn_factory,
        max_length=128,
        round_sizes=[128],
    )
    handler = embedder_handler.QueueingEmbedderHandler(
        model_name="testmodel",
        model=model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=spec.vocab_size,
    )
    with handler:
        tokens = [1]
        response = handler.calculate_inference_artifacts(
            [tokens], request_id="test_rid", timeout_s=5.0
        )
        embeddings = np.stack([e.embeddings for e in response])
        assert embeddings.shape == (1, 1024), response


def test_empty_request_raises_value_error(starethanol_model):
    """Tests that the handler can handle an empty request."""
    step_fn, attn_factory, spec = starethanol_model
    model = embedder_model.EmbedderModelFFWD(
        model_name="testmodel",
        step_fn=step_fn,
        attn_factory=attn_factory,
        max_length=128,
        round_sizes=[128],
    )
    handler = embedder_handler.QueueingEmbedderHandler(
        model_name="testmodel",
        model=model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=spec.vocab_size,
    )
    with handler:
        tokens = []

        with pytest.raises(ValueError):
            _ = handler.calculate_inference_artifacts(
                [tokens], request_id="test_rid", timeout_s=5.0
            )


def test_request_with_too_many_chunks_can_be_enqueued_once():
    """Tests that the handler can handle a request with too many chunks."""
    model = embedder_model.NullEmbedderModel()
    handler = embedder_handler.QueueingEmbedderHandler(
        model_name="testmodel",
        model=model,
        max_round_size=3,
        max_seq_length=3,
        vocab_size=4,
        indexing_queue_size=2,  # queue length limit!
        query_queue_size=2,
    )

    request_is_waiting = threading.Event()
    all_requests_scheduled = threading.Event()

    old_model_process_round = model.process_round  # avoid recursion

    def _mock_process_round(*args, **kwargs):
        request_is_waiting.set()
        all_requests_scheduled.wait()
        return old_model_process_round(*args, **kwargs)

    model.process_round = _mock_process_round

    # we are intentionally not starting the handler to test whether new
    # requests are rejected.
    # NOT: handler.start()

    with ThreadPoolExecutor(max_workers=2) as executor:
        tokens1 = [[1]] * 10
        future1 = executor.submit(
            handler.calculate_inference_artifacts,
            tokens1,
            request_id="test_rid1",
            timeout_s=1.0,
        )

        tokens2 = [[2]] * 10
        future2 = executor.submit(
            handler.calculate_inference_artifacts,
            tokens2,
            request_id="test_rid2",
            timeout_s=1.0,
        )

        with pytest.raises(embedder_handler.QueueFullError):
            future2.result()
        with pytest.raises(TimeoutError):
            # timeout because we didn't start the handler
            future1.result()

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/proto:tensor",
        "//base/python/grpc:client_options",
        "//services/embedder_host:embedder_py_proto",
        "//services/lib/grpc:stub_cycler",
        "//services/lib/request_context:request_context_py",
        requirement("numpy"),
    ],
)

py_library(
    name = "multiplex",
    srcs = [
        "multiplex.py",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        requirement("prometheus-client"),
        ":client",
        "//base/feature_flags:feature_flags_py",
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["client_test.py"],
    deps = [
        ":client",
        requirement("numpy"),
        requirement("mock"),
    ],
)

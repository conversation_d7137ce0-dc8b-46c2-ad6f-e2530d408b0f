from dataclasses import dataclass
import random
import threading
import typing
import json
import logging

import numpy as np
from prometheus_client import Counter

import base.feature_flags
from services.embedder_host.client.client import (
    EmbedderClientProtocol,
    EmbedderRequestType,
)
from services.lib.request_context.request_context import RequestContext

_counter = Counter(
    "au_embeddings_indexer_embedder_multiplex_counter",
    "Counter of the multiplex target in the embeddings indexer",
    ["model_name", "feature_flag_name", "name"],
)


@dataclass
class MultiplexOption:
    """A single option for multiplexing."""

    name: str
    weight: float


@dataclass
class MultiplexConfig:
    """Configuration for multiplexing."""

    options: list[MultiplexOption]


class MultiplexEmbeddingClient(EmbedderClientProtocol):
    """A client that multiplexes requests to multiple embedder clients.


    It consumes a feature flag that contains a json object with the weights for each client.
    The weights are used to randomly select a client for each request.

    Example
    {"default": 0.5, "gsc": 0.5}
    """

    def __init__(
        self,
        embedder_rpc_clients: typing.Dict[str, EmbedderClientProtocol],
        model_name: str,
        feature_flag: base.feature_flags.StringFlag,
    ):
        """ """
        logging.info("Creating MultiplexEmbeddingClient with %s", embedder_rpc_clients)
        self.embedder_rpc_clients = embedder_rpc_clients
        self.model_name = model_name
        self.feature_flag = feature_flag
        if "default" not in self.embedder_rpc_clients:
            raise ValueError("No default client")
        self.lock = threading.Lock()
        self.last_ff: str | None = None
        self.last_config: MultiplexConfig | None = None

    def _get_ff_ctx(self):
        fflags_ctx = base.feature_flags.get_global_context()
        if self.model_name:
            fflags_ctx = fflags_ctx.bind_attribute(
                "model_name",
                self.model_name,
            )
        return fflags_ctx

    def _get_config(self) -> MultiplexConfig | None:
        """Get the config from the feature flag.
        If the config has not changed, return the cached config.
        """
        fflags_ctx = self._get_ff_ctx()
        s = self.feature_flag.get(fflags_ctx)
        with self.lock:
            if self.last_ff == s:
                return self.last_config
        logging.info("New multiplex config: %s", s)
        if not s:
            self.last_config = None
            self.last_ff = s
            return None
        else:
            self.last_ff = s
            j = json.loads(s)
            c = MultiplexConfig(
                options=[MultiplexOption(name=k, weight=v) for k, v in j.items()]
            )
            self.last_config = c
            return self.last_config

    def _get_client(self) -> EmbedderClientProtocol:
        """Get a client from the config.
        If the config is None, return the default client.
        If the config is not None, select a client based on the weights.
        """
        config = self._get_config()
        if not config:
            _counter.labels(self.model_name, self.feature_flag.name, "default").inc()
            c = self.embedder_rpc_clients.get("default")
            if not c:
                raise ValueError("No default client")
            return c
        weights = [o.weight for o in config.options]
        names = [o.name for o in config.options]
        client_name = random.choices(names, weights=weights)[0]
        _counter.labels(self.model_name, self.feature_flag.name, client_name).inc()
        c = self.embedder_rpc_clients.get(client_name)
        if not c:
            raise ValueError(f"No client for {client_name}")
        return c

    def calculate_embedding(
        self,
        tokens: list[list[int]],
        request_context: RequestContext,
        timeout: float = 30,
        request_type: EmbedderRequestType = EmbedderRequestType.DEFAULT,
        source_namespace: str | None = None,
    ) -> np.ndarray:
        c = self._get_client()
        return c.calculate_embedding(
            tokens=tokens,
            request_context=request_context,
            timeout=timeout,
            request_type=request_type,
            source_namespace=source_namespace,
        )

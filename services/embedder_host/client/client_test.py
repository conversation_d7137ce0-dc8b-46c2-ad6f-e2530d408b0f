"""Tests for the embedder host client."""

from unittest import mock

import numpy as np

from base.proto.tensor import to_tensor
from services.embedder_host.client import client
from services.lib.request_context.request_context import RequestContext


def test_calculate_embedding():
    stub = mock.Mock()
    result_mock = mock.Mock()
    expected_result = np.array([[42, 1337]], dtype=np.float32)
    result_mock.results = to_tensor(expected_result)
    stub.CalculateEmbedding.return_value = result_mock
    emb_client = client.EmbedderClient(lambda: stub)
    request_context = RequestContext.create()

    return_value = emb_client.calculate_embedding(
        tokens=[[1, 2, 3]], request_context=request_context
    )

    stub.CalculateEmbedding.assert_called_once()
    np.testing.assert_array_equal(expected_result, return_value)

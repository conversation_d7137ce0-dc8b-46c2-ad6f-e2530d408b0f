syntax = "proto3";
package embedder;

import "base/proto/tensor.proto";

// embedder service
//
// the embedder calculates the embeddings given a batch of tokens representing a string
//
// the embeddings can be used for vector-based similarity searches (Dense Retrieval)
//
// Requests for the service may fail due to timeouts (GRPC status code: deadline exceeded)
// and the queue being full (GRPC status code: resource exhausted).
service Embedder {
  rpc CalculateEmbedding(EmbeddingRequest) returns (EmbeddingResponse);
}

enum EmbeddingRequestType {
  DEFAULT = 0;
  INDEXING = 1;
  QUERY = 2;
}

message EmbeddingRequest {
  // tensor of int in the shape [batch size, tokens]
  //
  // the batch size should be 1
  tensor.Tensor tokens = 1;

  // tensor of int in the shape of [batch size].
  // Each entry stores the number of tokens for the given string in the batch.
  //
  // the maximal value in this tensor should match the size of second dimension
  // of the tokens tensor.
  tensor.Tensor lengths = 2;

  // the namespace of the source of the request.
  //
  // this is used to determine the source of the embeddings.
  string source_namespace = 3;

  // the type of the request.
  //
  // this is used to determine the priority of the embeddings.
  // Query requests should be prioritized over indexing requests.
  EmbeddingRequestType request_type = 4;
}

message EmbeddingResponse {
  // tensor of half in the shape [batch size, dim]
  //
  // contains the embeddings for each string in the request.
  tensor.Tensor results = 1 [debug_redact = true];
}

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:rust.bzl", "rust_library")

proto_library(
    name = "content_manager_proto",
    srcs = ["content_manager.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "content_manager_py_proto",
    protos = [":content_manager_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

go_grpc_library(
    name = "content_manager_go_proto",
    importpath = "github.com/augmentcode/augment/services/content_manager/proto",
    proto = ":content_manager_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
    ],
)

rust_library(
    name = "content_manager_rs_proto",
    srcs = ["content_manager_proto.rs"],
    aliases = aliases(),
    crate_name = "content_manager_rs_proto",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":content_manager_rs_proto_gen",
        "//base/blob_names:blob_names_rs_proto",
    ],
)

cargo_build_script(
    name = "content_manager_rs_proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        ":content_manager_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

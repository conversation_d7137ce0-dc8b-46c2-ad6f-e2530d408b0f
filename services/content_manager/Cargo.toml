[package]
name = "content_manager_rs_proto"
version = "0.1.0"
edition = "2021"

[lib]
name = "content_manager_rs_proto"
path = "content_manager_proto.rs"

[dependencies]
prost = { workspace = true }
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
serde = { workspace = true }
tonic = { workspace = true }
tonic-build = { workspace = true }
blob_names_rs_proto = { path = "../../base/blob_names" }

[build-dependencies]
tonic-build = { workspace = true }

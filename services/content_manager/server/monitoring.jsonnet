local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local backlogSpec(groupId, backlogSize, durationSec) =
    local spec = {
      displayName: 'Content Manager group %s pub/sub backlog' % groupId,
      conditionPrometheusQueryLanguage: {
        duration: '%ds' % durationSec,
        evaluationInterval: '60s',
        labels: { severity: 'warning' },
        query: 'sum by (subscription_id)(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id=~".*content-manager.*group%d"}) > %d' % [groupId, backlogSize],
      },
    };
    monitoringLib.alertPolicy(cloud, spec, 'content-manager-pubsub-backlog-%s' % groupId, 'Content Manager subscription %s has more than %s undelivered messages' % [monitoringLib.label('subscription_id'), backlogSize]);

  local unackedSpec = {
    displayName: 'Content Manager pub/sub old unacked message',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      // 21600 seconds is 6 hours
      query: 'max by (subscription_id)(pubsub_googleapis_com:subscription_oldest_unacked_message_age{monitored_resource="pubsub_subscription",subscription_id=~".*content-manager.*"}) > 21600',
    },
  };

  [
    // We want this time to be long enough that we don't alert on every new repo that is uploaded. From what we have seen so far, 60m seems good enough for this.
    backlogSpec(1, 10000, 60 * 15),  // high priority, low volume queue: more than 10k blobs for fifteen minutes or more
    backlogSpec(2, 20000, 60 * 60),  // lower priority, higher volume queue: more than 20k blobs for an hour or more
    backlogSpec(3, 50000, 60 * 120),  // backlog queue: more than 50k blobs for two hours or more
    monitoringLib.alertPolicy(cloud, unackedSpec, 'content-manager-pubsub-unacked', 'Content Manager subscription %s has messages unacked for more than 6 hours' % monitoringLib.label('subscription_id')),
  ]

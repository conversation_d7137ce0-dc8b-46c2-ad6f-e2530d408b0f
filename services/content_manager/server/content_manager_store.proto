// Protobuf definition controlling the on-disk state for the content manager

// The messages here are an implementation detail of the content manager server
// and not exposed to other services or the external API
syntax = "proto3";

package content_manager_store;

import "google/protobuf/timestamp.proto";
// for BlobMetadata
import "services/content_manager/content_manager.proto";

// the list of transformation keys informed about the blob
//
// if a transformation key was added after the blob was uploaded, the key
// might not yet be informed about the blob. If the blob is still used, i.e.
// if it is part of a find missing call, the subscribers will be notified
// about the blob.
message InformedTransformation {
  // name of the transformation key
  string transformation_key = 1;

  // time the transformation key was informed about a given blob
  //
  // Note(dirk): This is not necessariliy the time an indexer was first informed, but
  // when the notification itself was created and enqueued.
  google.protobuf.Timestamp time = 6;
}

// metadata of a blob (row or transformed)
message BlobInfo {
  // name of the blob. The blob name is a combination of the path name
  // and the content.
  // See //base/blob_names for details
  string blob_name = 1;

  // transformation key for which to upload data or "" when this is the metadata
  // for the raw blob
  //
  // the transformation key is a unique identifier to an subscriber to blob updates, e.g.
  // an indexer.
  string transformation_key = 2;

  // the sub key allows to upload multiple transformed contents for each blob and transformation
  // key the sub key can be thought of as similar to a blob/transformation key pair can have
  // different files or objects.
  //
  // Set to "" when this is the metadata for the raw blob
  string sub_key = 3;

  // sha256 digest of the content (hex encoded)
  string digest = 4;

  // size of the content in bytes
  uint32 size = 5;

  // timestamp the blob was uplaoded
  google.protobuf.Timestamp time = 6;

  // metadata of the blob, e.g. the path name
  repeated content_manager.BlobMetadata metadata = 7;

  // all transformation keys that have been informed about a blob.
  //
  // the informed transformation keys is a part of the blob info that is not
  // immutable and can change. Most of the blob info is immutable.
  // Due to update races between content managers some updates might be lost.
  repeated InformedTransformation informed_transformation_keys = 8;

  // all transformation keys that have finished uploading transformed content for a blob.
  //
  // This means that there will be blob content keys for this blob with this transformation key.
  //
  // the uploaded transformation keys is a part of the blob info that is not
  // immutable and can change. Most of the blob info is immutable.
  // Due to update races between content managers some updates might be lost.
  repeated InformedTransformation uploaded_transformation_keys = 9;
}

// List of Blob Names in a checkpoint
message BlobNamesList {
  repeated bytes blob_names = 1;
}

// proto for the pubsub message data.
// v1 was a the blob name as string.
// v2 is this message in protobuf binary format.
message NotificationMessage {
  string blob_name = 1;
  string tenant_id = 2;
}

// Proto for the values of the user id -> blob index. We want to store the
// timestamp in addition to the blob name so that we can return the upload time
// for the blob without needing to parse the entry keys.
message UploadInfo {
  // Name of the blob
  string blob_name = 1;

  // Time the blob was uploaded
  google.protobuf.Timestamp time = 2;
}

// proto to store all of the blobs associated with a ANN index in bigtable as a single
// cell
message AnnIndexBlobInfoCell {
  // list of blob infos in the request format
  repeated AnnIndexBlobInfoData infos = 1;
}

// A single element of the serialized blob info
message AnnIndexBlobInfoData {
  bytes blob_name = 1;
  uint32 chunk_count = 2;
}

message AnnIndexMappingValueCell {
  repeated bytes added_blobs = 1;
  repeated bytes removed_blobs = 2;
}

use core::time::Duration;
use moka::sync::C<PERSON>;
use moka::sync::CacheBuilder;
use request_context::RequestSessionId;
use request_context::TenantId;
use request_context::UserId;
use std::sync::Arc;
use std::sync::Mutex;
use tokenbucket::TokenBucket;

use crate::gcp_queue_manager::SubscriptionGroup;

/// trait for rate limiters
///
/// it will limit the number of acquired entries (blobs to notify) for a given tenant id/session id
/// combination
///
pub trait RateLimiter {
    /// acquires a number of entries
    ///
    /// Returns
    /// - Ok if the number of entries is acquired
    /// - Err if the number of entries is not acquired
    fn acquire(
        &self,
        tenant_id: &TenantId,
        opaque_user_id: Option<&UserId>,
        session_id: &RequestSessionId,
        feature_flags: &feature_flags::FeatureFlagsServiceHandle,
        count: usize,
    ) -> SubscriptionGroup;
}

pub struct NoopRateLimiter {}

impl RateLimiter for NoopRateLimiter {
    fn acquire(
        &self,
        _tenant_id: &TenantId,
        _opaque_user_id: Option<&UserId>,
        _session_id: &RequestSessionId,
        _feature_flags: &feature_flags::FeatureFlagsServiceHandle,
        _count: usize,
    ) -> SubscriptionGroup {
        SubscriptionGroup::NonRateLimited
    }
}

/// Feature flag for the fill rate of the token bucket.
/// The value is the number of tokens per second. The default is 0.5 allowing for an upload every two seconds.
pub const FILL_RATE_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("content_manager_fill_rate", 0.5);

/// Feature flag for the capacity of the token bucket.
pub const TOKEN_CAPACITY_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("content_manager_token_capacity", 60.0);

/// Feature flag for the fill rate of the token bucket.
/// The value is the number of tokens per second. The default is 15 allowing to upload around
/// 50k files per hour
pub const LOW_PRIO_FILL_RATE_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("content_manager_low_prio_fill_rate", 15.0);

/// Feature flag for the capacity of the token bucket.
pub const LOW_PRIO_TOKEN_CAPACITY_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("content_manager_low_prio_token_capacity", 50000.0);

const RATE_LIMIT_BY_USER_ID_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("content_manager_rate_limit_by_user_id", false);

struct CacheEntry {
    token_bucket: TokenBucket,
    low_prio_token_bucket: TokenBucket,
}

impl CacheEntry {
    fn new(feature_flags: &feature_flags::FeatureFlagsServiceHandle) -> Self {
        let fill_rate = FILL_RATE_FLAG.get_from(feature_flags);
        let token_capacity = TOKEN_CAPACITY_FLAG.get_from(feature_flags);

        let low_prio_fill_rate = LOW_PRIO_FILL_RATE_FLAG.get_from(feature_flags);
        let low_prio_token_capacity = LOW_PRIO_TOKEN_CAPACITY_FLAG.get_from(feature_flags);

        Self {
            token_bucket: TokenBucket::new(fill_rate, token_capacity),
            low_prio_token_bucket: TokenBucket::new(low_prio_fill_rate, low_prio_token_capacity),
        }
    }

    fn acquire(&mut self, count: f64) -> SubscriptionGroup {
        if self.low_prio_token_bucket.acquire(count).is_err() {
            SubscriptionGroup::LowPriority
        } else if self.token_bucket.acquire(count).is_ok() {
            SubscriptionGroup::NonRateLimited
        } else {
            SubscriptionGroup::RateLimited
        }
    }
}

pub struct TokenBucketRateLimiter {
    cache: Cache<(String, String), Arc<Mutex<CacheEntry>>>,
}

impl TokenBucketRateLimiter {
    pub fn new(cache_size: u64) -> Self {
        Self {
            // token buckets are automatically removed after 5 minutes
            cache: CacheBuilder::new(cache_size)
                .time_to_idle(Duration::from_secs(300))
                .build(),
        }
    }
}

impl RateLimiter for TokenBucketRateLimiter {
    fn acquire(
        &self,
        tenant_id: &TenantId,
        opaque_user_id: Option<&UserId>,
        session_id: &RequestSessionId,
        feature_flags: &feature_flags::FeatureFlagsServiceHandle,
        count: usize,
    ) -> SubscriptionGroup {
        let key = if RATE_LIMIT_BY_USER_ID_FLAG.get_from(feature_flags) {
            opaque_user_id
                .map(|u| u.user_id.clone())
                .unwrap_or_else(|| tenant_id.to_string())
        } else {
            tenant_id.to_string()
        };
        let t = self.cache.get_with((key, session_id.to_string()), || {
            let e = CacheEntry::new(feature_flags);
            Arc::new(Mutex::new(e))
        });
        let mut t = t.lock().unwrap();
        t.acquire(count as f64)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use request_context::UserIdType;

    #[tokio::test]
    async fn test_acquire() {
        let feature_flags = feature_flags::setup_local();
        let rate_limiter = TokenBucketRateLimiter::new(100);
        let tenant_id: TenantId = "dev-augie".to_string().into();
        let session_id = RequestSessionId::create_random();
        assert_eq!(
            rate_limiter.acquire(&tenant_id, None, &session_id, &feature_flags, 1),
            SubscriptionGroup::NonRateLimited
        );
        // request more than what is left, should be rate limited
        assert_eq!(
            rate_limiter.acquire(&tenant_id, None, &session_id, &feature_flags, 10000),
            SubscriptionGroup::RateLimited
        );
    }

    #[tokio::test]
    async fn test_acquire_with_user_id() {
        let feature_flags = feature_flags::setup_local();
        RATE_LIMIT_BY_USER_ID_FLAG.set_local(&feature_flags, true);
        let rate_limiter = TokenBucketRateLimiter::new(100);
        let tenant_id: TenantId = "dev-augie".to_string().into();
        let session_id = RequestSessionId::create_random();
        let user_id: UserId = UserId {
            user_id: "user1".to_string(),
            user_id_type: UserIdType::Augment.into(),
        };
        assert_eq!(
            rate_limiter.acquire(&tenant_id, Some(&user_id), &session_id, &feature_flags, 1),
            SubscriptionGroup::NonRateLimited
        );
        // request more than what is left, should be rate limited
        assert_eq!(
            rate_limiter.acquire(
                &tenant_id,
                Some(&user_id),
                &session_id,
                &feature_flags,
                10000
            ),
            SubscriptionGroup::RateLimited
        );
        let user_id2: UserId = UserId {
            user_id: "user2".to_string(),
            user_id_type: UserIdType::Augment.into(),
        };
        // request by a different user, should not be rate limited
        assert_eq!(
            rate_limiter.acquire(&tenant_id, Some(&user_id2), &session_id, &feature_flags, 1),
            SubscriptionGroup::NonRateLimited
        );
    }
}

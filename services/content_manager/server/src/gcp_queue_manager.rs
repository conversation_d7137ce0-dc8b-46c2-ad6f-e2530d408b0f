use async_channel::RecvError;
use futures::{Future, Stream};
use lazy_static::lazy_static;
use std::{
    collections::{HashMap, HashSet},
    fmt::{Display, Formatter},
    sync::Arc,
    time::{Duration, Instant},
};

use async_rwlock::RwLock;
use async_trait::async_trait;
use google_cloud_gax::retry::RetrySetting;
use google_cloud_googleapis::pubsub::v1::{PubsubMessage, RetryPolicy};
use google_cloud_pubsub::{
    client::{Client, ClientConfig},
    subscriber::ReceivedMessage,
    subscription::{SubscriptionConfig, SubscriptionConfigToUpdate},
    topic::Topic,
};
use google_cloud_pubsub::{publisher::Publisher, subscription::Subscription};
use itertools::Itertools;
use prost::Message;
use rand::Rng;
use regex::Regex;
use request_context::UserId;
use request_context::{RequestId, RequestSessionId, TenantId};
use tokio_stream::StreamExt;

use crate::queue_manager::ObjectNotification;
use crate::queue_manager::SubscribeCallbackError;
use crate::rate_limiter::RateLimiter;
use crate::{
    config::{Config, GcpConfig},
    util::{BlobName, ObjectId, ObjectTransformationKeyInfo},
    QueueManager,
};
use crate::{metrics::UPLOAD_BLOB_LIMITED_COUNTER, queue_manager::BlobPriority};
use crate::{proto::content_manager_store::NotificationMessage, queue_manager::FlowControl};

pub const ENABLE_CONTENT_MANAGER_SUBSCRIPTIONS_GROUP3: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("enable_content_manager_subscriptions_group3", true);

pub const PULL_MESSAGES_BATCH_SIZE: feature_flags::IntFlag =
    feature_flags::IntFlag::new("content_manager_pull_messages_batch_size", 16);

type PerSubscriptionGroup<T> = [T; 3];

fn get(
    subscriptions: &PerSubscriptionGroup<Subscription>,
    group: SubscriptionGroup,
) -> &Subscription {
    subscriptions.get(group as usize).unwrap()
}

pub struct TopicInfo {
    publisher: Publisher,
    subscriptions: PerSubscriptionGroup<Subscription>,
    channels: PerSubscriptionGroup<async_channel::Receiver<Arc<ReceivedMessage>>>,
}

/// Implements the Queue Manager for GCP
///
/// The implementation uses the Pub/Sub system for notifications
pub struct GcpQueueManagerImpl {
    gcp_config: GcpConfig,
    client: Client,
    /// map from active transformation keys to queue name
    queues: RwLock<HashMap<String, TopicInfo>>,

    // Used to single thread concurrent calls to mutate the set
    // of transformation keys.
    single_thread_gcp_updates_lock: tokio::sync::Mutex<()>,

    // the object names of the transformation keys
    // this is used to only delete a topic if all transformation keys are deleted
    // this is to protect against multiple transformation key objects with the same transformation key name
    key_map: RwLock<HashMap<String, HashSet<String>>>,

    rate_limiter: Box<dyn RateLimiter + Send + Sync>,

    feature_flags_handle: feature_flags::FeatureFlagsServiceHandle,
}

impl GcpQueueManagerImpl {
    /// Create a new queue manager for GCP
    pub async fn new(
        config: &Config,
        rate_limiter: Box<dyn RateLimiter + Send + Sync>,
        feature_flags_handle: feature_flags::FeatureFlagsServiceHandle,
    ) -> Self {
        let gcp_config = config.gcp.clone();
        let config = ClientConfig::default()
            .with_auth()
            .await
            .expect("GCP authentication not possible");
        let client = Client::new(config).await.unwrap();

        GcpQueueManagerImpl {
            gcp_config: gcp_config.clone(),
            client,
            queues: RwLock::new(HashMap::new()),
            single_thread_gcp_updates_lock: tokio::sync::Mutex::new(()),
            key_map: RwLock::new(HashMap::new()),
            rate_limiter,
            feature_flags_handle,
        }
    }

    async fn get_publisher(&self, transformation_key: &str) -> tonic::Result<Publisher> {
        let queues = self.queues.read().await;
        match queues.get(transformation_key) {
            None => Err(tonic::Status::not_found("Topic not found")),
            Some(topic_info) => Ok(topic_info.publisher.clone()),
        }
    }

    async fn get_subscriptions(
        &self,
        transformation_key: &str,
    ) -> tonic::Result<PerSubscriptionGroup<Subscription>> {
        let queues = self.queues.read().await;
        let queue = queues.get(transformation_key);
        match queue {
            None => Err(tonic::Status::not_found("Transformation key not found")),
            Some(topic_info) => Ok(topic_info.subscriptions.clone()),
        }
    }
}

// 0: legacy group without an filter on the group: to be removed
// 1: non rate limited group
// 2: rate limited group
#[derive(Debug, Copy, Clone, Eq, PartialEq, Hash)]
pub enum SubscriptionGroup {
    NonRateLimited,
    RateLimited,
    LowPriority,
}

impl Display for SubscriptionGroup {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            SubscriptionGroup::NonRateLimited => write!(f, "1"),
            SubscriptionGroup::RateLimited => write!(f, "2"),
            SubscriptionGroup::LowPriority => write!(f, "3"),
        }
    }
}

impl TryFrom<&str> for SubscriptionGroup {
    type Error = tonic::Status;
    fn try_from(value: &str) -> Result<Self, Self::Error> {
        match value {
            "1" => Ok(SubscriptionGroup::NonRateLimited),
            "2" => Ok(SubscriptionGroup::RateLimited),
            "3" => Ok(SubscriptionGroup::LowPriority),
            _ => Err(tonic::Status::invalid_argument(
                "Invalid subscription group",
            )),
        }
    }
}

const SUBSCRIPTION_GROUPS: PerSubscriptionGroup<SubscriptionGroup> = [
    SubscriptionGroup::NonRateLimited,
    SubscriptionGroup::RateLimited,
    SubscriptionGroup::LowPriority,
];

impl GcpQueueManagerImpl {
    fn get_topic_name(&self, transformation_key: &str) -> String {
        format!("{}{}", self.gcp_config.topic_prefix, transformation_key)
    }

    fn get_legacy_subscription_group_name(&self, transformation_key: &str) -> String {
        format!(
            "{}{}",
            self.gcp_config.subscription_prefix, transformation_key
        )
    }

    fn get_subscription_name(&self, transformation_key: &str, group: SubscriptionGroup) -> String {
        format!(
            "{}{}-group{}",
            self.gcp_config.subscription_prefix, transformation_key, group
        )
    }

    async fn delete_subscription(&self, subscription_name: &str) -> Result<(), tonic::Status> {
        let subscription = self.client.subscription(subscription_name);
        if subscription.exists(None).await? {
            tracing::info!("Deleting subscription {}", subscription_name);
            subscription.delete(None).await.map_err(|e| {
                tracing::error!("Failed to delete subscription: {:?}", e);
                e
            })?;
        }
        Ok(())
    }

    async fn delete_topic(&self, transformation_key: &str) -> Result<(), tonic::Status> {
        tracing::info!("Delete transformation key: transformation_key={transformation_key}");

        for group in SUBSCRIPTION_GROUPS {
            let subscription_name = self.get_subscription_name(transformation_key, group);
            self.delete_subscription(&subscription_name).await?;
        }

        let topic_name = self.get_topic_name(transformation_key);
        let topic = self.client.topic(&topic_name);
        if topic.exists(None).await? {
            tracing::info!("Deleting topic {}", topic_name);
            topic.delete(None).await.map_err(|e| {
                tracing::error!("Failed to delete topic: {:?}", e);
                e
            })?;
        }
        Ok(())
    }

    async fn prepare_subscription(
        &self,
        topic: &Topic,
        transformation_key: &str,
        group: SubscriptionGroup,
    ) -> Result<Subscription, tonic::Status> {
        let subscription_name = self.get_subscription_name(transformation_key, group);
        let retry_policy = self
            .gcp_config
            .notification_retry
            .as_ref()
            .map(|r| RetryPolicy {
                minimum_backoff: Some(prost_types::Duration {
                    seconds: r.min_backoff_seconds,
                    nanos: 0,
                }),
                maximum_backoff: Some(prost_types::Duration {
                    seconds: r.max_backoff_seconds,
                    nanos: 0,
                }),
            });
        let filter = format!("attributes.group = \"{group}\"");
        let config = SubscriptionConfig {
            ack_deadline_seconds: self.gcp_config.notification_ack_deadline as i32,
            filter,
            retry_policy,
            ..Default::default()
        };

        let subscription = self.client.subscription(&subscription_name);
        if !subscription.exists(None).await? {
            tracing::info!("Creating subscription {}", subscription_name);
            match subscription
                .create(topic.fully_qualified_name(), config, None)
                .await
            {
                Err(err) => {
                    if err.code() == tonic::Code::AlreadyExists {
                        tracing::info!("Subscription existed already");
                    } else {
                        tracing::error!("Failed to create subscription: {:?}", err);
                        return Err(err);
                    }
                }
                Ok(_) => {
                    tracing::info!("Created subscription");
                }
            }
        } else {
            let sub_config = subscription.config(None).await?;
            tracing::info!(
                "Found subscription {:?} on topic {:?}",
                sub_config.1,
                sub_config.0
            );
            if sub_config.1.detached || sub_config.0 != topic.fully_qualified_name() {
                tracing::info!(
                    "Deleting subscription {} and recreating it : subscription topic={:?}, topic={}",
                    subscription_name,
                    sub_config.0,
                    topic.fully_qualified_name()
                );
                subscription.delete(None).await?;
                subscription
                    .create(topic.fully_qualified_name(), config, None)
                    .await?;
            } else {
                tracing::info!("Subscription exists already");
                if (sub_config.1.ack_deadline_seconds as u32
                    != self.gcp_config.notification_ack_deadline)
                    || sub_config.1.retry_policy != retry_policy
                {
                    tracing::info!(
                    "Updating subscription {} ack deadline to ack_deadline_seconds={}, retry_policy={:?}",
                    subscription_name,
                    self.gcp_config.notification_ack_deadline,
                    retry_policy
                );
                    subscription
                        .update(
                            SubscriptionConfigToUpdate {
                                ack_deadline_seconds: Some(
                                    self.gcp_config.notification_ack_deadline as i32,
                                ),
                                retry_policy,
                                ..Default::default()
                            },
                            None,
                        )
                        .await?;
                }
            }
        }

        let sub_config = subscription.config(None).await?;
        tracing::info!(
            "Using subscription {:?} on topic {:?}",
            sub_config.1,
            sub_config.0
        );
        Ok(subscription)
    }

    async fn prepare_topic(&self, transformation_key: &str) -> Result<TopicInfo, tonic::Status> {
        tracing::info!("Prepare topic: transformation_key={transformation_key}");

        let topic_name = self.get_topic_name(transformation_key);
        let topic = self.client.topic(&topic_name);
        if !topic.exists(None).await? {
            tracing::info!("Creating topic {}", topic_name);
            match topic.create(None, None).await {
                Err(err) => {
                    if err.code() == tonic::Code::AlreadyExists {
                        tracing::info!("Topic existed already");
                    } else {
                        tracing::error!("Failed to create topic: {:?}", err);
                        return Err(err);
                    }
                }
                Ok(_) => {
                    tracing::info!("Created topic");
                }
            }
        } else {
            tracing::info!("Found topic {:?}", topic_name);
        }

        // TODO: encapsulate this "async array map" in a helper function
        let [f1, f2, f3] = SUBSCRIPTION_GROUPS
            .map(|group| self.prepare_subscription(&topic, transformation_key, group));
        let subscriptions: PerSubscriptionGroup<Subscription> = [f1.await?, f2.await?, f3.await?];

        self.delete_subscription(
            self.get_legacy_subscription_group_name(transformation_key)
                .as_str(),
        )
        .await?;

        // print all subscriptions for debugging
        let topic_subscriptions = topic.subscriptions(None).await?;
        tracing::info!(
            "Topic subscriptions: {:?}",
            topic_subscriptions
                .iter()
                .map(|s| s.fully_qualified_name())
                .collect::<Vec<_>>()
        );

        let channels: PerSubscriptionGroup<async_channel::Receiver<Arc<ReceivedMessage>>> =
            SUBSCRIPTION_GROUPS.map(|group| {
                let queue_size = match group {
                    SubscriptionGroup::LowPriority => 1,
                    _ => 4,
                };
                let (tx, rx) = async_channel::bounded(queue_size);

                let sub = get(&subscriptions, group).clone();
                let tk = transformation_key.to_string();

                let mut pull_messages =
                    PULL_MESSAGES_BATCH_SIZE.get_from(&self.feature_flags_handle) as i32;

                if pull_messages <= 0 {
                    pull_messages = 16;
                }

                tokio::spawn(async move {
                    run_subscription_loop_pull(
                        sub.clone(),
                        tk,
                        group,
                        tx,
                        pull_messages,
                        Some(RetrySetting::default()),
                    )
                    .await
                });

                rx
            });

        let publisher = topic.new_publisher(None);

        Ok(TopicInfo {
            publisher,
            subscriptions,
            channels,
        })
    }

    #[allow(clippy::too_many_arguments)]
    async fn notify_key_of_objects(
        &self,
        tenant_id: &TenantId,
        opaque_user_id: Option<&UserId>,
        request_session_id: &RequestSessionId,
        object_ids: &[&ObjectId],
        transformation_key: &str,
        feature_flags: &feature_flags::FeatureFlagsServiceHandle,
        priority: BlobPriority,
    ) -> Result<(), tonic::Status> {
        if object_ids.is_empty() {
            return Ok(());
        }

        let blob_group = match priority {
            BlobPriority::Low => SubscriptionGroup::LowPriority,
            _ => {
                match self.rate_limiter.acquire(
                    tenant_id,
                    opaque_user_id,
                    request_session_id,
                    feature_flags,
                    object_ids.len(),
                ) {
                    SubscriptionGroup::LowPriority => {
                        UPLOAD_BLOB_LIMITED_COUNTER
                            .with_label_values(&["low_prio_limited"])
                            .inc();
                        SubscriptionGroup::LowPriority
                    }
                    SubscriptionGroup::RateLimited => {
                        UPLOAD_BLOB_LIMITED_COUNTER
                            .with_label_values(&["limited"])
                            .inc();
                        SubscriptionGroup::RateLimited
                    }
                    SubscriptionGroup::NonRateLimited => {
                        UPLOAD_BLOB_LIMITED_COUNTER.with_label_values(&["ok"]).inc();
                        SubscriptionGroup::NonRateLimited
                    }
                }
            }
        };

        let publisher = self.get_publisher(transformation_key).await?;

        let mut msgs = vec![];
        for chunk in &object_ids
            .iter()
            .filter_map(|b| {
                match b {
                    ObjectId::BlobName(b) => {
                        let msg = NotificationMessage {
                            tenant_id: tenant_id.to_string(),
                            blob_name: b.to_string(),
                        };
                        Some(PubsubMessage {
                            data: msg.encode_to_vec(),
                            attributes: HashMap::from([
                                ("format_version".to_string(), "v2".to_string()),
                                ("group".to_string(), blob_group.to_string()),
                            ]),
                            ..Default::default()
                        })
                    }
                    ObjectId::CheckpointId(_) => {
                        // TODO(luke): skipped for now, will use separate pubsub and msg format
                        None
                    }
                }
            })
            .chunks(self.gcp_config.notification_batch_size)
        {
            msgs.push(chunk.collect_vec());
        }
        for msg in msgs {
            let response = publisher
                .publish_immediately(msg, None)
                .await
                .map_err(|e| {
                    tracing::error!(
                        "Failed to publish notification: transformation_key={} err={:?}",
                        transformation_key,
                        e
                    );
                    e
                })?;
            tracing::info!(
                "Published notification: object_ids={:?} transformation_key={} message_ids={:?}, group={}",
                object_ids, transformation_key, response, blob_group);
        }
        Ok(())
    }

    async fn get_transformation_keys(&self) -> Vec<String> {
        let queues = self.queues.read().await;
        queues.iter().map(|(k, _)| k.to_string()).collect()
    }
}

// parses the message and extracts a notification object.
//
// in particular, it will parse the ack id and find the group.
fn get_notification(
    tk: &str,
    group: SubscriptionGroup,
    message: &ReceivedMessage,
) -> tonic::Result<ObjectNotification> {
    let format_version = message
        .message
        .attributes
        .get("format_version")
        .map(|v| v.as_str())
        .unwrap_or("v1");
    let ack_id = message.ack_id().to_string();
    if format_version == "v2" {
        match NotificationMessage::decode(message.message.data.as_slice()) {
            Err(err) => {
                tracing::error!("Failed to decode message: {:?}", err);
                Err(tonic::Status::data_loss("Invalid message"))
            }
            Ok(notification) => {
                tracing::debug!("Received pub/sub message: transformation_key={} blob_name={:?} tenant_id={} ack_id={} message_id={}",
                    tk, notification.blob_name, notification.tenant_id, ack_id, message.message.message_id);
                Ok(ObjectNotification {
                    object_id: ObjectId::BlobName(BlobName::new(&notification.blob_name)),
                    receipt_handle: build_receipt_handle(group, &ack_id),
                    tenant_id: notification.tenant_id.into(),
                })
            }
        }
    } else {
        Err(tonic::Status::data_loss("Invalid message"))
    }
}

// nack the message.
//
// this means that the message will be retried after exponential backoff instead of waiting
// for the ack deadline
async fn nack_message(msg: &ReceivedMessage, transformation_key: &str, group: SubscriptionGroup) {
    if let Err(err) = msg.nack().await {
        tracing::warn!(
            "Failed to nack message: transformation_key={} group={} err={}",
            transformation_key,
            group,
            err
        );
    }
}

// forward the message to the callback
//
// if the callback returns an error, the message is nacked so that the message can be retried after
// exponential backoff.
async fn forward_message<Fut, F>(
    callback: &F,
    message: Result<Arc<ReceivedMessage>, RecvError>,
    group: SubscriptionGroup,
    transformation_key: &str,
    request_id: &RequestId,
) -> Result<u32, SubscribeCallbackError>
where
    Fut: Future<Output = Result<u32, SubscribeCallbackError>>,
    F: Fn(Option<ObjectNotification>) -> Fut,
{
    match message {
        Err(err) => {
            tracing::info!(
                "Received EOS: transformation_key={} group={} request_id={} err={}",
                transformation_key,
                group,
                request_id,
                err
            );
            Err(SubscribeCallbackError::Stop)
        }
        Ok(msg) => match get_notification(transformation_key, group, &msg) {
            Err(err) => {
                // something went frong with parsing the message. This should not actually happen
                nack_message(&msg, transformation_key, group).await;
                tracing::error!(
                        "Failed to get notification: transformation_key={} group={} request_id={} err={}",
                        transformation_key,
                        group,
                        request_id,
                        err
                    );
                Err(SubscribeCallbackError::Err)
            }
            Ok(notification) => {
                tracing::info!(
                    "Received notification: transformation_key={} group={} request_id={} msg={:?}",
                    transformation_key,
                    group,
                    request_id,
                    notification
                );
                match callback(Some(notification)).await {
                    Err(SubscribeCallbackError::Stop) => {
                        nack_message(&msg, transformation_key, group).await;
                        Err(SubscribeCallbackError::Stop)
                    }
                    Err(SubscribeCallbackError::Err) => {
                        nack_message(&msg, transformation_key, group).await;
                        Err(SubscribeCallbackError::Err)
                    }
                    Ok(objects_sent) => Ok(objects_sent),
                }
            }
        },
    }
}

// pull messages from the given channels (one per group) and
// forward them to the callback
//
// the function will give priority to the non rate limited group.
//
// Args
//    callback: the callback to forward the messages to
//    rx1: the channel for the non rate limited group
//    rx2: the channel for the rate limited group
//    rx3: the channel for the low priority group
//    ttl: the time to live for the subscription
//    pinger_ttl: the time to live for the ping
//    transformation_key: the transformation key for the subscription
//    request_id: the request id for the subscription
//    flow_control: a stream of flow control messages opening up the window
//       or signalling shutdown requested
#[allow(clippy::too_many_arguments)]
async fn pull_messages<Fut, F, S>(
    callback: F,
    rx1: async_channel::Receiver<Arc<ReceivedMessage>>,
    rx2: async_channel::Receiver<Arc<ReceivedMessage>>,
    rx3: async_channel::Receiver<Arc<ReceivedMessage>>,
    feature_flags: feature_flags::FeatureFlagsServiceHandle,
    ttl: Duration,
    pinger_ttl: Duration,
    transformation_key: &str,
    request_id: RequestId,
    flow_control: S,
) -> Result<(), tonic::Status>
where
    Fut: Future<Output = Result<u32, SubscribeCallbackError>> + Send,
    F: Fn(Option<ObjectNotification>) -> Fut + Send + Sync,
    S: Stream<Item = FlowControl> + Send + std::marker::Unpin,
{
    tracing::info!(
        "Start pulling messages: transformation_key={} request_id={} ttl={:?} pinger_ttl={:?}",
        transformation_key,
        request_id,
        ttl,
        pinger_ttl
    );
    let start = Instant::now();
    let mut end = false;
    let mut sent = 0_u64;
    let mut window: u64 = 0_u64;
    let mut flow_control = flow_control;

    loop {
        let e = start.elapsed();
        if e > ttl {
            tracing::info!(
                "Subscription: Reached end of subscription TTL: transformation_key={} request_id={}",
                transformation_key,
                request_id
            );
            return Ok(());
        }
        if end {
            tracing::info!(
                "Subscription: Reached end of subscription: transformation_key={} request_id={}",
                transformation_key,
                request_id
            );
            return Ok(());
        }
        tokio::select! {
            // We want to give group1 (non rate limited group) priority over group2 (rate limited group)
            biased;

            _ = tokio::time::sleep(ttl - e) => {
                tracing::info!("Reached end of subscription TTL: transformation_key={} request_id={}", transformation_key, request_id);
                return Ok(());
            }

            message = flow_control.next() => {
                match message {
                    None => {
                        tracing::info!("Reached end of subscription: transformation_key={} request_id={}", transformation_key, request_id);
                        return Ok(());
                    }
                    Some(FlowControl::Ack(new_window)) => {
                        window = new_window;
                    }
                }
            }

            message = rx1.recv(), if sent < window => {
                match forward_message(&callback, message, SubscriptionGroup::NonRateLimited, transformation_key,
                    &request_id).await {
                        Err(SubscribeCallbackError::Stop) => {
                            end = true;
                        }
                        Err(SubscribeCallbackError::Err) => {
                            return Err(tonic::Status::internal("Failed to forward message"));
                        }
                        Ok(blobs_sent) => {
                            sent += blobs_sent as u64;
                        }
                    }
            }

            message = rx2.recv(), if sent < window => {
                match forward_message(&callback, message, SubscriptionGroup::RateLimited, transformation_key,
                    &request_id).await {
                        Err(SubscribeCallbackError::Stop) => {
                            end = true;
                        }
                        Err(SubscribeCallbackError::Err) => {
                            return Err(tonic::Status::internal("Failed to forward message"));
                        }
                        Ok(blobs_sent) => {
                            sent += blobs_sent as u64;
                        }
                    }
            }

            message = rx3.recv(), if sent < window && ENABLE_CONTENT_MANAGER_SUBSCRIPTIONS_GROUP3.get_from(&feature_flags) => {
                match forward_message(&callback, message, SubscriptionGroup::LowPriority, transformation_key,
                    &request_id).await {
                        Err(SubscribeCallbackError::Stop) => {
                            end = true;
                        }
                        Err(SubscribeCallbackError::Err) => {
                            return Err(tonic::Status::internal("Failed to forward message"));
                        }
                        Ok(blobs_sent) => {
                            sent += blobs_sent as u64;
                        }
                    }
            }

            _ = tokio::time::sleep(pinger_ttl) => {
                tracing::info!("Send ping: transformation_key={} request_id={}", transformation_key, request_id);
                if let Err(send_error) = callback(None).await {
                    tracing::error!("Failed to send ping: {:?}", send_error);
                    match send_error {
                        SubscribeCallbackError::Stop => {
                            end = true;
                        }
                        SubscribeCallbackError::Err => {
                            return Err(tonic::Status::internal("Failed to send ping"));
                        }
                    }
                }
            }
        }
    }
}

// runs the central pub/sub subscription loop for transformation key and group.
//
// the loop is restarted if there is an error as long as the subscription is not detached
// the results are put into a result channel which is drained from the Subscribe callers.
//
// Effectively this multiplexes the Single-Consumer subscribe message stream into a
// multi-consumer channel
async fn run_subscription_loop_pull(
    subscription: Subscription,
    tk: String,
    group: SubscriptionGroup,
    chan: async_channel::Sender<Arc<ReceivedMessage>>,
    pull_messages: i32,
    retry: Option<RetrySetting>,
) {
    tracing::info!(
        "Start pub/sub pull loop: transformation_key={} group={}",
        tk,
        group
    );

    loop {
        match subscription.pull(pull_messages, retry.clone()).await {
            Err(err) => {
                tracing::error!(
                    "Pub/sub pull: Failed to pull transformation_key={} group={} err={}",
                    tk,
                    group,
                    err
                );

                let sub_config = subscription.config(None).await;
                match sub_config {
                    Err(err) => {
                        tracing::warn!(
                            "Pub/sub pull: Failed to get subscription config: transformation_key={} group={} err={:?}",
                            tk,
                            group,
                            err
                        );
                        if err.code() == tonic::Code::NotFound {
                            tracing::info!(
                                "Pub/sub pull: transformation_key={} group={} no longer exists",
                                tk,
                                group,
                            );
                            return;
                        }
                    }
                    Ok(config) => {
                        if config.1.detached {
                            tracing::info!(
                                "Pub/sub pull: Reached end of subscription transformation_key={} group={} config={:?}",
                                tk,
                                group,
                                config
                            );
                            return;
                        }
                    }
                }

                tokio::time::sleep(Duration::from_secs(1)).await;
            }
            Ok(messages) => {
                let mut err_seen = false;

                for message in messages {
                    let message = Arc::new(message);
                    if let Err(send_error) = chan.send(message.clone()).await {
                        if !err_seen {
                            err_seen = true;
                            tracing::error!(
                                "Pub/sub pull: Failed to send notification: transformation_key={} group={} err={}",
                                tk,
                                group,
                                send_error
                            );
                        }
                        nack_message(&message, &tk, group).await;
                    }
                }

                if err_seen {
                    return;
                }
            }
        }
    }
}

/// build the receipt handle for the pubsub message
///
/// The format is if the group is 0: ack_id
/// else: group:ack_id
///
/// Args:
///    group: the group of the subscription
///    ack_id: the ack id of the pubsub message
///
/// Returns:
///    the receipt handle
///
fn build_receipt_handle(group: SubscriptionGroup, ack_id: &str) -> String {
    format!(":{group}:{ack_id}")
}

lazy_static! {
    pub static ref RECEIPT_HANDLE_REGEX: Regex = Regex::new(r"^:([0-9]+):(.+)$").unwrap();
}

fn parse_receipt_handle(receipt_handle: &str) -> Result<(SubscriptionGroup, &str), tonic::Status> {
    if let Some(captures) = RECEIPT_HANDLE_REGEX.captures(receipt_handle) {
        let group: SubscriptionGroup = captures.get(1).unwrap().as_str().try_into()?;
        let ack_id = captures.get(2).unwrap().as_str();
        Ok((group, ack_id))
    } else {
        Err(tonic::Status::invalid_argument("Invalid receipt handle"))
    }
}

#[async_trait]
impl QueueManager for GcpQueueManagerImpl {
    async fn validate_notifications(
        &self,
        tenant_id: &TenantId,
        opaque_user_id: Option<&UserId>,
        request_session_id: &RequestSessionId,
        validations: &[ObjectTransformationKeyInfo],
        feature_flags: &feature_flags::FeatureFlagsServiceHandle,
        priority: BlobPriority,
    ) -> Result<Vec<ObjectTransformationKeyInfo>, tonic::Status> {
        if validations.is_empty() {
            return Ok(vec![]);
        }
        let mut result: Vec<ObjectTransformationKeyInfo> = vec![];
        let mut notifications = multimap::MultiMap::new();
        let transformation_keys = self.get_transformation_keys().await;
        for validation in validations {
            let mut result_validation = ObjectTransformationKeyInfo {
                object_id: validation.object_id.clone(),
                transformation_keys: vec![],
            };
            for transformation_key in &transformation_keys {
                if !validation.transformation_keys.contains(transformation_key) {
                    notifications.insert(transformation_key.clone(), &validation.object_id);
                    result_validation
                        .transformation_keys
                        .push(transformation_key.clone());
                }
            }
            if !result_validation.transformation_keys.is_empty() {
                result.push(result_validation);
            }
        }
        for (transformation_key, object_ids) in notifications {
            tracing::info!(
                "Notification about object: transformation_key={transformation_key} object_ids={:?}",
                object_ids
            );
            self.notify_key_of_objects(
                tenant_id,
                opaque_user_id,
                request_session_id,
                object_ids.as_slice(),
                &transformation_key,
                feature_flags,
                priority,
            )
            .await?;
        }
        Ok(result)
    }

    async fn ack_notification<T>(
        &self,
        transformation_key: &str,
        receipt_handles: &[T],
    ) -> Result<(), tonic::Status>
    where
        T: AsRef<str> + Send + Sync,
    {
        if receipt_handles.is_empty() {
            return Ok(());
        }
        let sub = self.get_subscriptions(transformation_key).await?;
        let handles: Vec<_> = receipt_handles
            .iter()
            .map(|r| parse_receipt_handle(r.as_ref()))
            .collect::<Result<Vec<_>, _>>()?
            .into_iter()
            .chunk_by(|h| h.0)
            .into_iter()
            .map(|(group, handles)| (group, handles.map(|h| h.1.to_string()).collect_vec()))
            .collect();

        for (group, handles) in handles {
            let subscription = get(&sub, group);
            if let Err(err) = subscription.ack(handles).await {
                tracing::error!(
                    "Failed to ack notification: transformation_key={}, err={:?}",
                    transformation_key,
                    err
                );
                return Err(err);
            }
            tracing::debug!(
                "Acked notification: transformation_key={transformation_key} group={group}",
            );
        }
        Ok(())
    }

    async fn add_transformation_key(
        &self,
        object_name: &str,
        transformation_key: &str,
    ) -> Result<(), tonic::Status> {
        tracing::info!(
            "Add transformation key: object_name={} transformation_key={}",
            object_name,
            transformation_key
        );

        let _single_thread_lock = self.single_thread_gcp_updates_lock.lock().await;

        {
            let mut map = self.key_map.write().await;
            let transformation_key = transformation_key.to_string();
            map.entry(transformation_key.clone())
                .or_insert(HashSet::new())
                .insert(object_name.to_string());
        }

        {
            let queues = self.queues.read().await;

            if queues.contains_key(transformation_key) {
                tracing::info!(
                    "Queue for transformation key {} already exists",
                    transformation_key
                );
                return Ok(());
            }
        }

        let topic = self.prepare_topic(transformation_key).await?;

        let mut queues = self.queues.write().await;

        if queues.contains_key(transformation_key) {
            tracing::error!(
                "Queue for transformation key {} already exists",
                transformation_key
            );
            return Ok(());
        }

        queues.insert(transformation_key.to_string(), topic);

        Ok(())
    }

    async fn delete_transformation_key(
        &self,
        object_name: &str,
        transformation_key: &str,
    ) -> Result<(), tonic::Status> {
        tracing::info!(
            "Delete transformation key: object_name={} transformation_key={}",
            object_name,
            transformation_key
        );

        let _single_thread_lock = self.single_thread_gcp_updates_lock.lock().await;

        let delete_topic = {
            let mut map = self.key_map.write().await;
            let transformation_key = transformation_key.to_string();
            let m = map.get_mut(&transformation_key);
            if let Some(m) = m {
                m.remove(object_name);
                m.is_empty()
            } else {
                true
            }
        };

        // only delete the topic if all transformation key objects are deleted
        if delete_topic {
            let mut queues = self.queues.write().await;
            if queues.remove(transformation_key).is_some() {
                drop(queues);
                self.delete_topic(transformation_key).await?;
            }
        } else {
            tracing::info!("Not deleting queue because other transformation keys still exist");
        }

        Ok(())
    }

    async fn set_transformation_keys(
        &self,
        transformation_keys: &[(String, String)],
    ) -> Result<(), tonic::Status> {
        tracing::info!("Set transformation keys: {:?}", transformation_keys);

        let _single_thread_lock = self.single_thread_gcp_updates_lock.lock().await;

        let transformation_key_names = {
            let mut map = self.key_map.write().await;
            for (object_name, transformation_key_name) in transformation_keys {
                let m = map
                    .entry(transformation_key_name.clone())
                    .or_insert(HashSet::new());
                m.insert(object_name.to_string());
            }
            map.keys().cloned().collect::<Vec<_>>()
        };

        let queues = self.queues.read().await;

        let mut queues_after_set = vec![];
        for k in transformation_key_names {
            let topic = self.prepare_topic(&k).await?;
            queues_after_set.push((k, topic))
        }
        let mut deleted: Vec<String> = vec![];
        {
            for (k, _) in queues.iter() {
                if !queues_after_set.iter().any(|e| &e.0 == k) {
                    deleted.push(k.to_string())
                }
            }
            drop(queues);

            let mut queues = self.queues.write().await;
            queues.clear();
            for (k, topic) in queues_after_set.into_iter() {
                queues.insert(k.to_string(), topic);
            }
        }
        for transformation_key in deleted {
            self.delete_topic(&transformation_key).await?;
        }
        Ok(())
    }

    // subscribe to messages for the given transformation key
    //
    // read messages from all channels for the given transformation key and forward them to the callback

    async fn subscribe<Fut, F, S>(
        &self,
        transformation_key: &str,
        request_id: RequestId,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
        callback: F,
        flow_control: S,
    ) -> tonic::Result<()>
    where
        Fut: Future<Output = Result<u32, SubscribeCallbackError>> + Send,
        F: Fn(Option<ObjectNotification>) -> Fut + Send + Sync,
        S: Stream<Item = FlowControl> + Send + std::marker::Unpin,
    {
        let random_ttl_seconds = rand::thread_rng().gen_range(
            self.gcp_config.min_subscribe_ttl_seconds..=self.gcp_config.max_subscribe_ttl_seconds,
        ) as u64;

        // ttl after which the subscription should be ended
        let ttl = Duration::from_secs(random_ttl_seconds);
        let pinger_ttl = Duration::from_secs(self.gcp_config.notification_ack_deadline as u64 / 2);

        tracing::info!(
            "Subscribe: transformation_key={} request_id={} ttl={:?}",
            transformation_key,
            request_id,
            ttl
        );
        // get the receivers channels for each group
        let receivers: PerSubscriptionGroup<async_channel::Receiver<Arc<ReceivedMessage>>> = {
            let queues = self.queues.read().await;
            let t = queues.get(transformation_key);
            match t {
                None => {
                    tracing::error!(
                        "Subscription not found: transformation_key={}",
                        transformation_key
                    );
                    Err(tonic::Status::not_found("Subscription not found"))
                }
                Some(topic_info) => {
                    let s = topic_info.channels.clone();
                    Ok(s)
                }
            }
        }?;

        let (g_rx1, g_rx2, g_rx3) = receivers.into();
        pull_messages(
            callback,
            g_rx1,
            g_rx2,
            g_rx3,
            feature_flags,
            ttl,
            pinger_ttl,
            transformation_key,
            request_id,
            flow_control,
        )
        .await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::gcp_queue_manager::SubscriptionGroup;

    #[test]
    fn test_receipt_handle() {
        let handle = ":1:1234567890";
        let (group, id) = parse_receipt_handle(handle).unwrap();
        assert_eq!(group, SubscriptionGroup::NonRateLimited);
        assert_eq!(id, "1234567890");
    }

    #[test]
    fn test_build_receipt_handle() {
        let handle = build_receipt_handle(SubscriptionGroup::NonRateLimited, "1234567890");
        assert_eq!(handle, ":1:1234567890");
    }
}

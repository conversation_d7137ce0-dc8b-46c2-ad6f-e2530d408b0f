use std::{
    cmp::max,
    collections::{BTreeMap, HashMap, HashSet},
    fmt::{self, Display},
    sync::Arc,
    time::Duration,
};

use async_trait::async_trait;
use bigtable_proxy_client::{
    proto::google::bigtable::v2::{
        mutate_rows_request,
        mutation::{self, DeleteFromRow, SetCell},
        Mutation,
    },
    BigtableProxyClient, BigtableProxyClientImpl, ReadResults, RowCell,
};

use base64::{prelude::BASE64_STANDARD, Engine};
use blob_names::SortedBlobNameBytesVec;
use bytes::Bytes;
use chrono::{DateTime, Utc};
use futures::{stream::FuturesOrdered, try_join, StreamExt};
use grpc_tls_config::get_client_tls_creds;
use itertools::Itertools;
use moka::future::Cache;
use prost::Message;
use prost_wkt_types::Timestamp;
use request_context::{RequestContext, TenantId};
use secrecy::{ExposeSecret, SecretString, SecretVec};
use sha256::digest;
use tokio::sync::mpsc;
use tracing::{self, Instrument};

use crate::{
    config::{Config, GcpConfig},
    metrics::{
        BATCH_CONTENT_BLOB_KEYS_COUNT, BATCH_CONTENT_CALLS, BATCH_CONTENT_ROWS_COUNT,
        CACHE_BYTES_COUNT, CACHE_ENTRY_COUNT, CONTENT_CACHE_COUNTER, INFO_CACHE_COUNTER,
    },
    object_store::{
        AnnIndexAssetKey, AnnIndexAssetStream, AnnIndexBlobInfoValue, AnnIndexMapping,
        AnnIndexMappingKey, AnnIndexMappingValue, BatchGetContentStream, BlobContentsKey,
        ObjectStore, TryNextResult, UploadAnnIndexAsset, UploadAnnIndexBlobInfosPayload,
        UploadContent,
    },
    proto::{base::blob_names as blob_names_proto, content_manager::BatchDeleteBlobsResponse},
    util::{
        BlobContentKey, BlobInfo, BlobName, CheckpointId, InformedTransformation, ObjectId,
        ObjectTransformationKeyInfo,
    },
};

/// GCP implementation of the ObjectStore trait.
///
/// This implementation uses BigTable as the underlying storage.
/// BigTable is a NoSQL database service that offers high performance,
/// consistent reads and writes in real-time.
pub struct GcpObjectStoreImpl {
    gcp_config: GcpConfig,
    proxy_client: Arc<dyn BigtableProxyClient + Send + Sync + 'static>,
    // map from tenant id, blob key to blob info
    blob_info_cache: Cache<(String, BlobContentKey), BlobInfo>,

    // map from tenant id, checkpoint id to sorted list of blob names
    checkpoint_cache: Cache<(String, CheckpointId), SortedBlobNameBytesVec>,
    checkpoints_length_limit: usize,

    // map from tenant id, blob key to content
    content_cache: Cache<(String, BlobContentKey), Bytes>,

    // maximum number of entries to return from the get user blobs rpc
    get_user_blobs_limit: i64,

    // maximum number of bytes allowed per cell
    cell_bytes_limit: usize,
}

// content stream implementing the BatchGet processing.
pub struct GcpBatchContentStream<'a> {
    /// parent
    parent: &'a GcpObjectStoreImpl,

    /// entries to download
    entries: Vec<BlobContentKey>,

    /// the index of the next blob content key from entries to download next
    entry_index: usize,

    /// the results (possible yet to be filled) for each blob content key in the current batch
    results: Vec<(BlobContentKey, Option<(BlobInfo, Bytes)>)>,

    /// index into the results array of the next down yield result
    result_index: usize,

    /// number of entries in a batch
    batch_size: usize,
}

pub struct GcpAnnIndexAssetStream {
    seen_first_result: bool,
    receiver: mpsc::Receiver<tonic::Result<ReadResults>>,
}

impl GcpObjectStoreImpl {
    pub async fn new(config: &Config) -> Self {
        let gcp_config = config.gcp.clone();

        // Content is the largest value type and size sensitive, so size it based on bytes
        let blob_info_cache = Cache::builder()
            .max_capacity(config.blob_info_cache_size)
            .weigher(|_, v: &BlobInfo| 128 + v.size_bytes() as u32)
            .build();

        let checkpoint_cache = Cache::builder()
            .max_capacity(config.checkpoint_cache_size)
            .weigher(|_, v: &SortedBlobNameBytesVec| 128 + (v.as_vec().len() * 32) as u32)
            .build();

        let content_cache = Cache::builder()
            .max_capacity(config.content_cache_size)
            .weigher(|_, v: &Bytes| 128 + v.len() as u32)
            .build();

        let proxy_client: Arc<dyn BigtableProxyClient + Send + Sync + 'static> =
            Arc::new(BigtableProxyClientImpl::new(
                config.bigtable_proxy_endpoint.as_str(),
                get_client_tls_creds(&config.client_mtls_config)
                    .expect("Failed to create TLS config"),
                Duration::from_secs_f32(config.bigtable_proxy_timeout_secs),
            ));

        Self {
            gcp_config: gcp_config.clone(),
            proxy_client,
            blob_info_cache,
            checkpoint_cache,
            checkpoints_length_limit: config.checkpoints_length_limit,
            content_cache,
            get_user_blobs_limit: config.get_user_blobs_limit,
            cell_bytes_limit: config.cell_bytes_limit,
        }
    }
}

fn get_first_cell(response: Vec<RowCell>, family_name: &str) -> (Option<RowCell>, Vec<RowCell>) {
    let (a, b): (Vec<_>, Vec<_>) = response
        .into_iter()
        .partition(|e| e.family_name == family_name);
    (a.into_iter().next(), b)
}

fn get_info_row_key(blob_key: &BlobContentKey) -> String {
    match blob_key {
        BlobContentKey::Raw(blob_name) => format!("info#{}##", blob_name),
        BlobContentKey::Transformed(blob_name, transformation_key, sub_key) => {
            format!("info#{}#{}#{}", blob_name, transformation_key, sub_key)
        }
    }
}

fn get_content_row_key(blob_key: &BlobContentKey) -> String {
    match blob_key {
        BlobContentKey::Raw(blob_name) => format!("content#{}##", blob_name),
        BlobContentKey::Transformed(blob_name, transformation_key, sub_key) => {
            format!("content#{}#{}#{}", blob_name, transformation_key, sub_key)
        }
    }
}

/// Returns the row key for a given checkpoint name
fn get_checkpoint_row_key(checkpoint_id: &CheckpointId) -> String {
    format!("checkpoint#{}##", checkpoint_id)
}

/// Returns the row key for an ANN index asset
fn get_ann_index_asset_row_key(key: &AnnIndexAssetKey, index: usize) -> String {
    format!(
        "ann_index#{}#{}#{}#{:03}", // pad index to 3 digits
        key.transformation_key, key.index_id, key.sub_key, index
    )
}

/// Returns the row key for the user id -> blob name index
fn get_user_row_key(
    user_id: &SecretString,
    timestamp_seconds: u64,
    blob_name: &BlobName,
) -> String {
    // Base64 encode the user id to avoid `#` characters
    let user_id_encoded = encode_user_id(user_id);

    // Include a reversed timestamp so that later requests are ordered first when doing a scan
    let reversed_timestamp = get_reversed_timestamp(timestamp_seconds);

    format!(
        "user#{}#{}#{}",
        user_id_encoded, reversed_timestamp, blob_name,
    )
}

fn encode_user_id(user_id: &SecretString) -> String {
    BASE64_STANDARD.encode(user_id.expose_secret())
}

fn get_reversed_timestamp(timestamp_seconds: u64) -> String {
    format!("{:x}", u64::MAX - timestamp_seconds)
}

struct AnnIndexMappingRowKey {
    transformation_key: String,
    checkpoint_id: CheckpointId,
    // uniquifier to allow the multimap structure function properly. Conceptually both the "key" and
    // the value, but we will only store in in the key.
    index_id: String,
}

impl Display for AnnIndexMappingRowKey {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "ann_index#mapping#{}#{}#{}",
            self.transformation_key, self.checkpoint_id, self.index_id,
        )
    }
}

impl AnnIndexMappingRowKey {
    fn from_str(key_str: &str) -> tonic::Result<Self> {
        let parts = key_str.split('#').collect_vec();
        if parts.len() != 5 || parts[0] != "ann_index" || parts[1] != "mapping" {
            tracing::error!("Couldn't parse AnnIndexMappingRowKey: {:?}", parts);
            return Err(tonic::Status::internal(
                "AnnIndexMappingRowKey failed validation",
            ));
        }
        Ok(AnnIndexMappingRowKey {
            transformation_key: parts[2].to_owned(),
            checkpoint_id: parts[3].into(),
            index_id: parts[4].to_owned(),
        })
    }

    fn min_str(transformation_key: &str, checkpoint_id: &CheckpointId) -> String {
        format!(
            "ann_index#mapping#{}#{}#",
            transformation_key, checkpoint_id
        )
    }

    // our bigtable proxy uses inclusive end range, so for this query to work properly we must
    // NEVER have any ann content mapping that ends in '$'
    fn max_str(transformation_key: &str, checkpoint_id: &CheckpointId) -> String {
        format!(
            "ann_index#mapping#{}#{}$",
            transformation_key, checkpoint_id
        )
    }
}

struct AnnIndexBlobInfoRowKey {
    transformation_key: String,
    index_id: String,
}

impl Display for AnnIndexBlobInfoRowKey {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "ann_index#blobinfo#{}#{}",
            self.transformation_key, self.index_id
        )
    }
}

fn create_blob_info_proto(
    blob_key: &BlobContentKey,
    metadata: &[(String, SecretString)],
    content: &SecretVec<u8>,
    current_time: DateTime<Utc>,
) -> crate::proto::content_manager_store::BlobInfo {
    let content_digest = digest(content.expose_secret().as_slice());
    let (blob_name, transformation_key, sub_key) = blob_key.into();
    crate::proto::content_manager_store::BlobInfo {
        blob_name: blob_name.to_string(),
        transformation_key: transformation_key.to_string(),
        sub_key: sub_key.to_string(),
        digest: content_digest,
        metadata: metadata
            .iter()
            .map(|(k, v)| crate::proto::content_manager::BlobMetadata {
                key: k.to_string(),
                value: v.expose_secret().to_string(),
            })
            .collect(),
        size: content.expose_secret().len() as u32,
        time: Some(Timestamp {
            seconds: current_time.timestamp(),
            nanos: 0,
        }),
        informed_transformation_keys: vec![],
        uploaded_transformation_keys: vec![],
    }
}

fn create_upload_info_proto(
    blob_key: &BlobContentKey,
    current_time: DateTime<Utc>,
) -> crate::proto::content_manager_store::UploadInfo {
    crate::proto::content_manager_store::UploadInfo {
        blob_name: blob_key.blob_name().to_string(),
        time: Some(Timestamp {
            seconds: current_time.timestamp(),
            nanos: 0,
        }),
    }
}

fn blob_info_to_proto(
    blob_key: &BlobContentKey,
    blob_info: &BlobInfo,
) -> crate::proto::content_manager_store::BlobInfo {
    let (blob_name, transformation_key, sub_key) = blob_key.into();
    crate::proto::content_manager_store::BlobInfo {
        blob_name: blob_name.to_string(),
        transformation_key: transformation_key.to_string(),
        sub_key: sub_key.to_string(),
        digest: blob_info.digest.clone(),
        metadata: blob_info
            .metadata
            .iter()
            .map(|(k, v)| crate::proto::content_manager::BlobMetadata {
                key: k.to_string(),
                value: v.expose_secret().to_string(),
            })
            .collect(),
        size: blob_info.size as u32,
        time: Some(Timestamp {
            seconds: blob_info.time.timestamp(),
            nanos: 0,
        }),
        informed_transformation_keys: blob_info
            .informed_transformation_keys
            .iter()
            .map(
                |e| crate::proto::content_manager_store::InformedTransformation {
                    transformation_key: e.transformation_key.to_string(),
                    time: Some(Timestamp {
                        seconds: e.time.timestamp(),
                        nanos: 0,
                    }),
                },
            )
            .collect(),
        uploaded_transformation_keys: blob_info
            .uploaded_transformation_keys
            .iter()
            .map(
                |e| crate::proto::content_manager_store::InformedTransformation {
                    transformation_key: e.transformation_key.to_string(),
                    time: Some(Timestamp {
                        seconds: e.time.timestamp(),
                        nanos: 0,
                    }),
                },
            )
            .collect(),
    }
}

/// Encode the checkpoint blob names into a proto message for storing in BigTable.
fn create_blobs_checkpoint_proto(
    blob_names: &[Vec<u8>],
) -> crate::proto::content_manager_store::BlobNamesList {
    let mut blob_names_vec = blob_names.to_vec();
    blob_names_vec.sort();
    crate::proto::content_manager_store::BlobNamesList {
        blob_names: blob_names_vec,
    }
}

fn blob_info_into_mutate_entry(
    info_row_key: String,
    blob_info: crate::proto::content_manager_store::BlobInfo,
) -> mutate_rows_request::Entry {
    mutate_rows_request::Entry {
        row_key: info_row_key.into_bytes(),
        mutations: vec![Mutation {
            mutation: Some(mutation::Mutation::SetCell(SetCell {
                family_name: "Info".to_owned(),
                column_qualifier: "Value".to_owned().into_bytes(),
                timestamp_micros: -1, // IMPORTANT: Don't leave it empty. Use -1 for current Bigtable server time.
                value: blob_info.encode_to_vec(),
            })),
        }],
    }
}

fn content_into_mutate_entry(
    content_row_key: String,
    content: SecretVec<u8>,
) -> mutate_rows_request::Entry {
    mutate_rows_request::Entry {
        row_key: content_row_key.into_bytes(),
        mutations: vec![Mutation {
            mutation: Some(mutation::Mutation::SetCell(SetCell {
                family_name: "Content".to_owned(),
                column_qualifier: "Content".to_owned().into_bytes(),
                timestamp_micros: -1, // IMPORTANT: Don't leave it empty. Use -1 for current Bigtable server time.
                value: content.expose_secret().to_vec(),
            })),
        }],
    }
}

/// Create a Big Table entry for the checkpoint.
fn checkpoint_into_mutate_entry(
    checkpoint_row_key: String,
    content: Vec<u8>,
) -> mutate_rows_request::Entry {
    mutate_rows_request::Entry {
        row_key: checkpoint_row_key.into_bytes(),
        mutations: vec![Mutation {
            mutation: Some(mutation::Mutation::SetCell(SetCell {
                family_name: "Checkpoint".to_owned(),
                column_qualifier: "Value".to_owned().into_bytes(),
                timestamp_micros: -1, // IMPORTANT: Don't leave it empty. Use -1 for current Bigtable server time.
                value: content,
            })),
        }],
    }
}

/// Create a BigTable entry for the user id -> blob name index
fn user_index_into_mutate_entry(
    index_row_key: String,
    upload_info: crate::proto::content_manager_store::UploadInfo,
) -> mutate_rows_request::Entry {
    mutate_rows_request::Entry {
        row_key: index_row_key.into_bytes(),
        mutations: vec![Mutation {
            mutation: Some(mutation::Mutation::SetCell(SetCell {
                family_name: "UserIndex".to_owned(),
                column_qualifier: "BlobName".to_owned().into_bytes(),
                timestamp_micros: -1, // IMPORTANT: Don't leave it empty. Use -1 for current Bigtable server time.
                value: upload_info.encode_to_vec(),
            })),
        }],
    }
}

/// Create a BigTable entry for an ANN index asset
fn ann_index_asset_into_mutate_entry(row_key: String, data: Vec<u8>) -> mutate_rows_request::Entry {
    mutate_rows_request::Entry {
        row_key: row_key.into_bytes(),
        mutations: vec![Mutation {
            mutation: Some(mutation::Mutation::SetCell(SetCell {
                family_name: "AnnIndex".to_owned(),
                column_qualifier: "Asset".to_owned().into_bytes(),
                timestamp_micros: -1, // IMPORTANT: Don't leave it empty. Use -1 for current Bigtable server time.
                value: data,
            })),
        }],
    }
}

// Create a Bigtable entry for a ANN mapping
fn ann_index_mapping_into_mutate_entry(
    row_key: String,
    added_blobs: Vec<blob_names::BlobName>,
    removed_blobs: Vec<blob_names::BlobName>,
) -> mutate_rows_request::Entry {
    let value_cell = crate::proto::content_manager_store::AnnIndexMappingValueCell {
        added_blobs: added_blobs
            .into_iter()
            .map(|blob_name| blob_name.as_bytes().to_vec())
            .collect_vec(),
        removed_blobs: removed_blobs
            .into_iter()
            .map(|blob_name| blob_name.as_bytes().to_vec())
            .collect_vec(),
    };
    mutate_rows_request::Entry {
        row_key: row_key.into_bytes(),
        mutations: vec![Mutation {
            mutation: Some(mutation::Mutation::SetCell(SetCell {
                family_name: "Checkpoint".to_owned(),
                column_qualifier: "Value".to_owned().into_bytes(),
                timestamp_micros: -1, // current Bigtable server time.
                value: value_cell.encode_to_vec(),
            })),
        }],
    }
}

fn ann_index_blob_info_into_mutate_entry(
    row_key: String,
    blob_info: Vec<AnnIndexBlobInfoValue>,
) -> mutate_rows_request::Entry {
    let blob_info_protos = blob_info
        .into_iter()
        .map(
            |info| crate::proto::content_manager_store::AnnIndexBlobInfoData {
                blob_name: info.blob_name.as_bytes().to_vec(),
                chunk_count: info.chunk_count,
            },
        )
        .collect_vec();
    let blob_info_cell = crate::proto::content_manager_store::AnnIndexBlobInfoCell {
        infos: blob_info_protos,
    };
    mutate_rows_request::Entry {
        row_key: row_key.into_bytes(),
        mutations: vec![Mutation {
            mutation: Some(mutation::Mutation::SetCell(SetCell {
                family_name: "AnnIndex".to_owned(),
                column_qualifier: "Value".to_owned().into_bytes(),
                timestamp_micros: -1, // current Bigtable server time.
                // u32 is nonstandard, i64 is, but as long as we're consistent...
                value: blob_info_cell.encode_to_vec(),
            })),
        }],
    }
}

/// Compute the checkpoint ID from the list of blob names.
fn compute_checkpoint_id(blob_ids: &[Vec<u8>]) -> Option<CheckpointId> {
    // Ensure there is at least one input string
    if blob_ids.is_empty() {
        return None;
    }

    // The checkpoint id is formed by XORing the byte strings together
    let mut result = blob_ids[0].clone();
    for hash_bytes in blob_ids.iter().skip(1) {
        // XOR each byte pair
        for (result_byte, hash_byte) in result.iter_mut().zip(hash_bytes.iter()) {
            *result_byte ^= *hash_byte;
        }
    }

    // Convert the result back to hex string
    let encoded_hex = hex::encode(result);
    Some(CheckpointId::new(&encoded_hex))
}

impl GcpObjectStoreImpl {
    async fn read_rows(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        row_keys: Vec<Vec<u8>>,
        row_ranges: Vec<(Vec<u8>, Vec<u8>)>,
        limit: Option<i64>,
    ) -> tonic::Result<ReadResults> {
        if row_keys.is_empty() && row_ranges.is_empty() {
            return Ok(vec![]);
        }

        self.proxy_client
            .read_rows(
                request_context,
                tenant_id,
                bigtable_proxy_client::proto::bigtable_proxy::TableName::ContentManager,
                row_keys,
                row_ranges,
                limit,
            )
            .await
    }

    async fn read_rows_streaming(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        row_keys: Vec<Vec<u8>>,
        row_ranges: Vec<(Vec<u8>, Vec<u8>)>,
        limit: Option<i64>,
    ) -> mpsc::Receiver<tonic::Result<ReadResults>> {
        if row_keys.is_empty() && row_ranges.is_empty() {
            // Intentionally drop tx immediately to return an empty stream
            let (_tx, rx) = mpsc::channel(1);
            return rx;
        }

        self.proxy_client
            .read_rows_streaming(
                request_context,
                tenant_id,
                bigtable_proxy_client::proto::bigtable_proxy::TableName::ContentManager,
                row_keys,
                row_ranges,
                limit,
            )
            .await
    }

    async fn mutate_rows(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        entries: Vec<mutate_rows_request::Entry>,
    ) -> Result<(), tonic::Status> {
        assert!(!entries.is_empty());

        let mut response_stream = self
            .proxy_client
            .mutate_rows(
                request_context,
                tenant_id,
                bigtable_proxy_client::proto::bigtable_proxy::TableName::ContentManager,
                entries,
            )
            .await?;
        while let Some(response) = response_stream.message().await? {
            for entry in response.entries {
                if let Some(status) = entry.status {
                    // Exclude gRPC "ok"
                    if status.code != 0 {
                        tracing::error!(
                            "Failed to mutate row: {} {:?}",
                            status.code,
                            status.message
                        );
                        return Err(tonic::Status::new(
                            tonic::Code::Internal,
                            "Failed to mutate row",
                        ));
                    }
                }
            }
        }
        Ok(())
    }

    /// upload a new blobs
    async fn mutate_rows_for_upload(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blobs: Vec<UploadContent>,
        user_id: Option<&SecretString>,
    ) -> Result<(), tonic::Status> {
        assert!(!blobs.is_empty());
        let current_time: DateTime<Utc> = Utc::now();
        let mut blob_entries = vec![];
        let mut user_index_entries = vec![];
        blob_entries.reserve(blobs.len() * 2);
        if user_id.is_some() {
            user_index_entries.reserve(blobs.len());
        }
        blobs.into_iter().for_each(|upload| {
            let info_row_key = get_info_row_key(&upload.blob_key);
            let content_row_key = get_content_row_key(&upload.blob_key);
            let blob_info = create_blob_info_proto(
                &upload.blob_key,
                &upload.metadata,
                &upload.content,
                current_time,
            );

            blob_entries.push(blob_info_into_mutate_entry(info_row_key, blob_info));
            blob_entries.push(content_into_mutate_entry(content_row_key, upload.content));

            // The user id is extracted from the access token and is set if and only if the
            // upload was made by a user. For service uploads (e.g. docsets), we don't want to
            // write to the user index and can do nothing.
            if let Some(user_id) = user_id {
                let user_row_key = get_user_row_key(
                    user_id,
                    current_time.timestamp() as u64,
                    upload.blob_key.blob_name(),
                );
                let upload_info = create_upload_info_proto(&upload.blob_key, current_time);
                user_index_entries.push(user_index_into_mutate_entry(user_row_key, upload_info));
            }
        });
        // Must persist the user index entries before the blob entries.
        // This is to maintain the invariant that if there exists blob content
        // uploaded by only one user, then the user index entries for that user
        // will reference that blob content.
        if !user_index_entries.is_empty() {
            self.mutate_rows(request_context, tenant_id, user_index_entries)
                .await?;
        }
        self.mutate_rows(request_context, tenant_id, blob_entries)
            .await
    }

    /// Takes a sorted list of blob names for a given checkpoint and persists
    /// them to BigTable.
    async fn mutate_rows_for_checkpoint(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        checkpoint_id: &CheckpointId,
        blob_names: SortedBlobNameBytesVec,
    ) -> Result<(), tonic::Status> {
        assert!(!blob_names.as_vec().is_empty());

        let row_key = get_checkpoint_row_key(checkpoint_id);
        let proto = create_blobs_checkpoint_proto(blob_names.as_vec());
        let entry = checkpoint_into_mutate_entry(row_key, proto.encode_to_vec());

        let entries = vec![entry];
        tracing::info!(
            "Checkpoint blob names: inserting {:?} entries into BigTable",
            entries.len()
        );
        self.mutate_rows(request_context, tenant_id, entries).await
    }

    /// returns the blob info for a given batch of blob keys
    ///
    /// returns a list of (blob_key, blob_info) in the order of the blob_keys
    async fn get_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_keys: &[BlobContentKey],
        use_cache: bool,
    ) -> Result<Vec<(BlobContentKey, Option<BlobInfo>)>, tonic::Status> {
        tracing::info!(
            "Batch get blob info: blob_content_keys={:?}",
            blob_keys.len(),
        );
        if blob_keys.is_empty() {
            return Ok(vec![]);
        }

        // keys maps from the row key to the blob key and the index of the blob key in the blob_keys array
        let mut keys = HashMap::new();

        // query_keys is the list of row keys to read
        let mut query_keys = vec![];

        // result_blob_infos is the list of blob info for each blob key
        let mut result_blob_infos: Vec<Option<BlobInfo>> = vec![];

        for (index, blob_key) in blob_keys.iter().enumerate() {
            let (blob_name, transformation_key, sub_key) = blob_key.into();
            let info_row_key = format!("info#{}#{}#{}", blob_name, transformation_key, sub_key);
            let mut blob_query_keys = if use_cache {
                let cache_key = (tenant_id.to_string(), blob_key.clone());
                match self.blob_info_cache.get(&cache_key).await {
                    None => {
                        INFO_CACHE_COUNTER.with_label_values(&["miss"]).inc();
                        result_blob_infos.push(None);
                        vec![info_row_key.into_bytes()]
                    }
                    Some(info) => {
                        INFO_CACHE_COUNTER.with_label_values(&["hit"]).inc();
                        result_blob_infos.push(Some(info));
                        vec![]
                    }
                }
            } else {
                result_blob_infos.push(None);
                vec![info_row_key.into_bytes()]
            };
            for key in blob_query_keys.iter() {
                keys.insert(key.clone(), (blob_key.clone(), index));
            }
            query_keys.append(&mut blob_query_keys);
        }
        // query_keys contains any keys we didn't find in the cache
        let r = self
            .read_rows(request_context, tenant_id, query_keys, vec![], None)
            .await?;
        tracing::info!(
            "Batch get blob info: read_rows returned {:?} entries",
            r.len()
        );

        for (key, cells) in r {
            // find the blob key and the results index for the given response
            if let Some((blob_key, result_index)) = keys.get(&key) {
                tracing::debug!(
                    "key={:?} blob_key={:?} index={}",
                    key,
                    blob_key,
                    result_index
                );

                let (info_cell, _) = get_first_cell(cells, "Info");
                if let Some(last_cell) = info_cell {
                    let bytes = Bytes::from(last_cell.value);
                    let mut blob_info_pb = crate::proto::content_manager_store::BlobInfo::default();
                    match blob_info_pb.merge(bytes) {
                        Err(err) => {
                            tracing::warn!("Failed to deserialize cell value: {:?}", err)
                            // ignore, the blob info is still None and the blob will be seen as unknown
                        }
                        Ok(_) => {
                            tracing::debug!("key={:?} pb_info={:?}", key, blob_info_pb);
                            result_blob_infos[*result_index] = Some(blob_info_pb.into());
                        }
                    }
                }
            } else {
                tracing::error!("Failed to find blob info for key");
                return Err(tonic::Status::internal("Failed to find blob info for key"));
            }
        }

        let result = blob_keys
            .iter()
            .zip(result_blob_infos.into_iter())
            .map(|(blob_key, blob_info)| (blob_key.clone(), blob_info))
            .collect();

        Ok(result)
    }

    /// returns the content for a given batch of blob keys
    ///
    /// returns a list of content in the order of the blob_keys, or None where no content is found
    async fn get_batch_content(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_keys: &[BlobContentKey],
    ) -> Result<Vec<Option<Bytes>>, tonic::Status> {
        let _timer = BATCH_CONTENT_CALLS.start_timer();
        BATCH_CONTENT_BLOB_KEYS_COUNT.observe(blob_keys.len() as f64);
        tracing::info!(
            "Batch read content: blob_content_keys={:?}",
            blob_keys.len(),
        );
        if blob_keys.is_empty() {
            return Ok(vec![]);
        }

        // keys maps from the row key to the blob key and the index of the blob key in the blob_keys array
        let mut keys = BTreeMap::new();

        // query_keys is the list of row keys to read
        let mut query_keys = vec![];

        // result is the list of blob content for each blob key
        let mut result: Vec<Option<Bytes>> = vec![];

        for (index, blob_key) in blob_keys.iter().enumerate() {
            let (blob_name, transformation_key, sub_key) = blob_key.into();
            let content_row_key =
                format!("content#{}#{}#{}", blob_name, transformation_key, sub_key);
            let mut blob_query_keys = vec![];
            let cache_key = (tenant_id.to_string(), blob_key.clone());
            match self.content_cache.get(&cache_key).await {
                None => {
                    CONTENT_CACHE_COUNTER.with_label_values(&["miss"]).inc();
                    result.push(None);
                    blob_query_keys.push(content_row_key.into_bytes());
                }
                Some(content) => {
                    CONTENT_CACHE_COUNTER.with_label_values(&["hit"]).inc();
                    result.push(Some(content));
                }
            };
            for key in blob_query_keys.iter() {
                keys.insert(key.clone(), index);
            }
            query_keys.append(&mut blob_query_keys);
        }

        BATCH_CONTENT_ROWS_COUNT.observe(query_keys.len() as f64);

        for (key, cells) in self
            .read_rows(request_context, tenant_id, query_keys, vec![], None)
            .await?
        {
            // find the blob key and the results index for the given response
            if let Some(result_index) = keys.get(&key) {
                // if there is a content cell:
                let (content_cell, _) = get_first_cell(cells, "Content");
                if let Some(last_cell) = content_cell {
                    let bytes = Bytes::from(last_cell.value);
                    tracing::debug!("key={:?} data={:?}", key, bytes.len());
                    let cache_key = (tenant_id.to_string(), blob_keys[*result_index].clone());
                    self.content_cache.insert(cache_key, bytes.clone()).await;
                    result[*result_index] = Some(bytes);
                }
            } else {
                tracing::error!("Failed to find blob info for key");
                return Err(tonic::Status::internal("Failed to find blob info for key"));
            }
        }

        CACHE_BYTES_COUNT
            .with_label_values(&["content"])
            .set(self.content_cache.weighted_size().try_into().unwrap());
        CACHE_ENTRY_COUNT
            .with_label_values(&["content"])
            .set(self.content_cache.entry_count().try_into().unwrap());

        Ok(result)
    }

    /// returns the content and the blob info for a given blob key
    async fn get_content_info(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_key: &BlobContentKey,
    ) -> Result<(BlobInfo, Bytes), tonic::Status> {
        let (blob_name, transformation_key, sub_key) = blob_key.into();

        let info_row_key = format!("info#{}#{}#{}", blob_name, transformation_key, sub_key);
        let mut query_keys = vec![];
        let cache_key = (tenant_id.to_string(), blob_key.clone());
        let mut blob_info = match self.blob_info_cache.get(&cache_key).await {
            None => {
                query_keys.push(info_row_key.into_bytes());
                None
            }
            Some(info) => Some(info),
        };
        let content_row_key = format!("content#{}#{}#{}", blob_name, transformation_key, sub_key);
        let mut content = match self.content_cache.get(&cache_key).await {
            None => {
                query_keys.push(content_row_key.into_bytes());
                None
            }
            Some(content) => Some(content),
        };

        tracing::info!("Read content: blob_content_key={:?}", blob_key,);
        let response = self
            .read_rows(request_context, tenant_id, query_keys, vec![], None)
            .await
            .map_err(|e| {
                tracing::error!("Failed to read content: {:?}", e);
                e
            })?;

        for (_key, cells) in response {
            let (content_cell, cells) = get_first_cell(cells, "Content");
            if let Some(last_cell) = content_cell {
                let bytes = Bytes::from(last_cell.value);

                tracing::debug!(
                    "Received content: blob_content_key={:?}, content_len={}",
                    blob_key,
                    bytes.len()
                );

                self.content_cache
                    .insert(cache_key.clone(), bytes.clone())
                    .await;
                content = Some(bytes);
            }
            let (info_cell, _) = get_first_cell(cells, "Info");
            if let Some(last_cell) = info_cell {
                let bytes = Bytes::from(last_cell.value);
                let mut blob_info_pb = crate::proto::content_manager_store::BlobInfo::default();
                blob_info_pb.merge(bytes).map_err(|e| {
                    tracing::error!("Failed to deserialize cell value: {:?}", e);
                    tonic::Status::internal("Failed to read blob")
                })?;
                let bi: BlobInfo = blob_info_pb.into();
                self.blob_info_cache
                    .insert(cache_key.clone(), bi.clone())
                    .await;
                blob_info = Some(bi);
            }
        }
        CACHE_BYTES_COUNT
            .with_label_values(&["info"])
            .set(self.blob_info_cache.weighted_size().try_into().unwrap());
        CACHE_BYTES_COUNT
            .with_label_values(&["content"])
            .set(self.content_cache.weighted_size().try_into().unwrap());
        CACHE_ENTRY_COUNT
            .with_label_values(&["info"])
            .set(self.blob_info_cache.entry_count().try_into().unwrap());
        CACHE_ENTRY_COUNT
            .with_label_values(&["content"])
            .set(self.content_cache.entry_count().try_into().unwrap());

        match (content, blob_info) {
            (Some(bytes), Some(blob_info)) => Ok((blob_info, bytes)),
            (_, _) => Err(tonic::Status::not_found("Blob not found")),
        }
    }

    /// Validates that all blobs being added as part of checkpoint_blobs exist.
    async fn validate_checkpoint_blobs(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blobs: &blob_names_proto::Blobs,
    ) {
        let blob_keys = blobs
            .added
            .iter()
            .map(|bytes| BlobContentKey::Raw(BlobName::new(hex::encode(bytes).as_str())))
            .take(1000) // Otherwise we may exceed a byte limit in get_blob_infos
            .collect_vec();

        // NOTE: this logging is expected during some tests since e.g. test_checkpoint_blobs doesn't try to ensure blobs for checkpointing are uploaded
        match self
            .get_blob_infos(request_context, tenant_id, &blob_keys, true)
            .await
        {
            Ok(blob_infos) => {
                for (blob_key, blob_info) in blob_infos {
                    if blob_info.is_none() {
                        tracing::warn!(
                            "Checkpoint blob validation found missing blob: {:?}",
                            blob_key
                        );
                    }
                }
            }
            Err(e) => {
                tracing::warn!(
                    "Checkpoint blob validation failed to get blob infos: {:?}",
                    e
                );
            }
        }
    }

    async fn read_checkpoint_info_from_bigtable(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        checkpoint_id: &CheckpointId,
    ) -> Result<SortedBlobNameBytesVec, tonic::Status> {
        let checkpoint_row_key = get_checkpoint_row_key(checkpoint_id);

        tracing::debug!(
            "Reading checkpoint: checkpoint_key={:?}",
            checkpoint_row_key,
        );

        let query_keys = vec![checkpoint_row_key.into_bytes()];
        assert!(!query_keys.is_empty());

        let mut response = self
            .read_rows(request_context, tenant_id, query_keys, vec![], None)
            .await?;

        tracing::debug!(
            "Reading checkpoint: lookup had {:?} results",
            response.len()
        );
        if let Some(first_item) = response.pop() {
            let (info_cell, _) = get_first_cell(first_item.1, "Checkpoint");

            let mut checkpoint_info_pb =
                crate::proto::content_manager_store::BlobNamesList::default();
            if let Some(last_cell) = info_cell {
                let bytes = Bytes::from(last_cell.value);
                checkpoint_info_pb.merge(bytes).map_err(|e| {
                    tracing::error!("Failed to deserialize cell value: {:?}", e);
                    tonic::Status::internal("Failed to read blob")
                })?;
            }
            Ok(SortedBlobNameBytesVec::new(checkpoint_info_pb.blob_names))
        } else {
            // To have a checkpoint id without any blob names is unexpected. Return
            // an error so that the client can generate a new checkpoint.
            tracing::warn!("Checkpoint unexpectedly contained no blob names");
            Err(tonic::Status::not_found("Checkpoint id not found"))
        }
    }

    /// Return the list of unique blob names from the given Blobs object.
    /// This involves resolving the checkpoint id into a list of blob names, and then
    /// adding and removing the added and deleted blob names from the checkpointed list.
    async fn resolve_blobs_into_blob_names(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blobs: blob_names_proto::Blobs,
    ) -> Result<SortedBlobNameBytesVec, tonic::Status> {
        let ckpt = blobs.baseline_checkpoint_id.clone();
        let blob_names = if let Some(ckpt) = ckpt {
            self.get_blobs_from_checkpoint(request_context, tenant_id, &CheckpointId::from(ckpt))
                .await?
        } else {
            SortedBlobNameBytesVec::default()
        };

        // TODO: Instead of using a hash set, take advantage of the fact that
        // all of the lists here are sorted.
        let mut blob_names: HashSet<Vec<u8>> = blob_names.as_vec().clone().into_iter().collect();

        for blob_name in blobs.added {
            blob_names.insert(blob_name);
        }
        for blob_name in blobs.deleted {
            blob_names.remove(&blob_name);
        }

        tracing::info!(
            "Get blob names from checkpoint {:?} returns {:?} blobs",
            &blobs.baseline_checkpoint_id,
            blob_names.len()
        );
        Ok(SortedBlobNameBytesVec::new(
            blob_names.into_iter().collect(),
        ))
    }
}

#[async_trait]
impl ObjectStore for GcpObjectStoreImpl {
    type BGCS<'a>
        = GcpBatchContentStream<'a>
    where
        Self: 'a;

    type AIAS = GcpAnnIndexAssetStream;

    /// upload a new blobs
    async fn batch_upload_blob(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blobs: Vec<UploadContent>,
        user_id: Option<&SecretString>,
    ) -> Result<Vec<(BlobContentKey, Option<BlobInfo>)>, tonic::Status> {
        let blob_infos = self
            .get_blob_infos(
                request_context,
                tenant_id,
                &blobs.iter().map(|b| b.blob_key.clone()).collect_vec(),
                false,
            )
            .await?;

        let to_upload = blobs
            .into_iter()
            .zip(blob_infos.iter())
            .flat_map(|(a, b)| if b.1.is_some() { None } else { Some(a) })
            .collect_vec();

        if !to_upload.is_empty() {
            self.mutate_rows_for_upload(request_context, tenant_id, to_upload, user_id)
                .await?;
        }
        Ok(blob_infos)
    }

    async fn record_informed_keys(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        entries: Vec<ObjectTransformationKeyInfo>,
    ) -> Result<(), tonic::Status> {
        if entries.is_empty() {
            return Ok(());
        }

        let blob_infos = self
            .get_blob_infos(
                request_context,
                tenant_id,
                &entries
                    .iter()
                    .filter_map(|e| match e.object_id.clone() {
                        ObjectId::BlobName(blob_name) => Some(BlobContentKey::Raw(blob_name)),
                        // TODO(luke) object store support for checkpoint content
                        ObjectId::CheckpointId(_) => None,
                    })
                    .collect_vec(),
                false,
            )
            .await?;

        let updated_blob_infos = entries
            .into_iter()
            .zip(blob_infos.iter())
            .flat_map(|(key_info, (key, blob_info))| {
                if let Some(blob_info) = blob_info {
                    let mut blob_info = blob_info.clone();
                    let mut informed_keys: Vec<_> = key_info
                        .transformation_keys
                        .into_iter()
                        .map(|e| InformedTransformation {
                            transformation_key: e,
                            time: Utc::now(),
                        })
                        .collect_vec();

                    let old_length = blob_info.informed_transformation_keys.len();

                    blob_info
                        .informed_transformation_keys
                        .append(&mut informed_keys);
                    blob_info.informed_transformation_keys.sort_unstable();
                    blob_info
                        .informed_transformation_keys
                        .dedup_by(|a, b| a.transformation_key == b.transformation_key);

                    if old_length != blob_info.informed_transformation_keys.len() {
                        Some((key, blob_info))
                    } else {
                        None
                    }
                } else {
                    None
                }
            })
            .collect_vec();

        if updated_blob_infos.is_empty() {
            return Ok(());
        }

        let entries = updated_blob_infos
            .iter()
            .map(|(blob_key, blob_info)| {
                let info_row_key = get_info_row_key(blob_key);
                let blob_info = blob_info_to_proto(blob_key, blob_info);
                blob_info_into_mutate_entry(info_row_key, blob_info)
            })
            .collect_vec();
        let r = self.mutate_rows(request_context, tenant_id, entries).await;
        for (blob_key, _) in updated_blob_infos {
            let cache_key = (tenant_id.to_string(), blob_key.clone());
            self.blob_info_cache.invalidate(&cache_key).await;
        }
        r
    }

    async fn record_uploaded_keys(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        entries: Vec<ObjectTransformationKeyInfo>,
    ) -> Result<(), tonic::Status> {
        if entries.is_empty() {
            return Ok(());
        }

        tracing::info!("record uploaded keys: entries={:?}", entries.len());

        let blob_infos = self
            .get_blob_infos(
                request_context,
                tenant_id,
                &entries
                    .iter()
                    .filter_map(|e| match e.object_id.clone() {
                        ObjectId::BlobName(blob_name) => Some(BlobContentKey::Raw(blob_name)),
                        // TODO(luke) object store support for checkpoint content
                        ObjectId::CheckpointId(_) => None,
                    })
                    .collect_vec(),
                false,
            )
            .await?;

        let updated_blob_infos = entries
            .into_iter()
            .zip(blob_infos.iter())
            .flat_map(|(key_info, (key, blob_info))| {
                if let Some(blob_info) = blob_info {
                    let mut blob_info: BlobInfo = blob_info.clone();
                    let mut uploaded_keys: Vec<_> = key_info
                        .transformation_keys
                        .into_iter()
                        .map(|e| InformedTransformation {
                            transformation_key: e,
                            time: Utc::now(),
                        })
                        .collect_vec();

                    let old_length = blob_info.uploaded_transformation_keys.len();

                    blob_info
                        .uploaded_transformation_keys
                        .append(&mut uploaded_keys);
                    blob_info.uploaded_transformation_keys.sort_unstable();
                    blob_info
                        .uploaded_transformation_keys
                        .dedup_by(|a, b| a.transformation_key == b.transformation_key);

                    if blob_info.uploaded_transformation_keys.len() != old_length {
                        Some((key, blob_info))
                    } else {
                        None
                    }
                } else {
                    None
                }
            })
            .collect_vec();

        if updated_blob_infos.is_empty() {
            return Ok(());
        }

        let entries = updated_blob_infos
            .iter()
            .map(|(blob_key, blob_info)| {
                let info_row_key = get_info_row_key(blob_key);
                let blob_info = blob_info_to_proto(blob_key, blob_info);
                blob_info_into_mutate_entry(info_row_key, blob_info)
            })
            .collect_vec();
        let r = self.mutate_rows(request_context, tenant_id, entries).await;
        for (blob_key, _) in updated_blob_infos {
            let cache_key = (tenant_id.to_string(), blob_key.clone());
            self.blob_info_cache.invalidate(&cache_key).await;
        }
        r
    }

    async fn get_blob_content(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_key: &BlobContentKey,
    ) -> Result<(BlobInfo, Bytes), tonic::Status> {
        self.get_content_info(request_context, tenant_id, blob_key)
            .await
    }

    async fn batched_get_blob_content<'a>(
        &'a self,
        // these arguments are included for standardization and easy pattern matching but are
        // unused in the stream (they'll be passed in through the stream try_next instead)
        _request_context: &RequestContext,
        _tenant_id: &TenantId,
        blob_keys: Vec<BlobContentKey>,
    ) -> Result<Self::BGCS<'a>, tonic::Status> {
        Ok(GcpBatchContentStream {
            parent: self,
            entries: blob_keys,
            entry_index: 0,
            results: vec![],
            result_index: 0,
            batch_size: self.gcp_config.batch_get_batch_size,
        })
    }

    async fn delete_blob(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_name: &BlobName,
        user_id: &SecretString,
        timestamp: Option<DateTime<Utc>>,
    ) -> Result<(), tonic::Status> {
        tracing::info!(
            "delete blob: blob_name={:?} tenant_id={}",
            blob_name,
            tenant_id
        );

        // Step 1: Determine the upload time, either from provided timestamp or by looking up the blob
        let upload_time = match timestamp {
            // When a timestamp is provided, use it directly without verifying blob existence.
            // This allows cleanup of user index entries even when the associated blob content
            // has already been deleted, which is necessary for maintaining database consistency.
            Some(time) => {
                tracing::info!("Using provided timestamp for deletion: {:?}", time);
                time
            }
            None => {
                // Verify the blob exists and get the upload time
                let blob_key = BlobContentKey::Raw(blob_name.clone());
                let blob_infos = self
                    .get_blob_infos(request_context, tenant_id, &[blob_key], false)
                    .await?;

                blob_infos
                    .first()
                    .and_then(|(_, info)| info.as_ref())
                    .map(|info| info.time)
                    .ok_or_else(|| tonic::Status::not_found("Blob not found"))?
            }
        };

        // Step 2: Verify the user index entry exists
        let user_row_key = get_user_row_key(user_id, upload_time.timestamp() as u64, blob_name);
        let response = self
            .read_rows(
                request_context,
                tenant_id,
                vec![user_row_key.clone().into_bytes()],
                vec![],
                Some(1),
            )
            .await?;
        if response.is_empty() {
            return Err(tonic::Status::not_found("User index entry not found"));
        }

        // Step 3: Delete all blob content and info rows
        let row_ranges = vec![
            (
                format!("info#{}#", blob_name).into_bytes(),
                format!("info#{}$", blob_name).into_bytes(),
            ),
            (
                format!("content#{}#", blob_name).into_bytes(),
                format!("content#{}$", blob_name).into_bytes(),
            ),
        ];

        let response = self
            .read_rows(request_context, tenant_id, vec![], row_ranges, None)
            .await?;

        let keys: Vec<Vec<u8>> = response.into_iter().map(|(key, _)| key).collect();

        if keys.is_empty() {
            tracing::warn!(
                "No blob content or info rows found for deletion: blob_name={:?} tenant_id={}",
                blob_name,
                tenant_id
            );
        } else {
            tracing::debug!("Deleting blob content keys: {:?}", keys);

            let entries = keys
                .into_iter()
                .map(|key| mutate_rows_request::Entry {
                    row_key: key,
                    mutations: vec![Mutation {
                        mutation: Some(mutation::Mutation::DeleteFromRow(DeleteFromRow {})),
                    }],
                })
                .collect();

            // Delete the blob content and info rows
            self.mutate_rows(request_context, tenant_id, entries)
                .await?;

            // Invalidate caches
            let blob_key = BlobContentKey::Raw(blob_name.clone());
            let cache_key = (tenant_id.to_string(), blob_key);
            self.blob_info_cache.invalidate(&cache_key).await;
            self.content_cache.invalidate(&cache_key).await;
        }

        // Step 4: Delete the user index entry
        // Delete the user index entry in a separate mutate request only after
        // the blob entries are known to be deleted.
        // This is to maintain the invariant that if there exists blob content
        // uploaded by only one user, then the user index entries for that user
        // will reference that blob content.
        tracing::debug!("Deleting user index key: {:?}", user_row_key);
        let delete_user_row_entry = mutate_rows_request::Entry {
            row_key: user_row_key.into_bytes(),
            mutations: vec![Mutation {
                mutation: Some(mutation::Mutation::DeleteFromRow(DeleteFromRow {})),
            }],
        };

        self.mutate_rows(request_context, tenant_id, vec![delete_user_row_entry])
            .await
    }

    async fn batch_delete_blobs(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        entries: &[(BlobName, SecretString, Option<DateTime<Utc>>)],
        ignore_index: bool,
    ) -> Result<BatchDeleteBlobsResponse, tonic::Status> {
        if entries.is_empty() {
            return Ok(BatchDeleteBlobsResponse {});
        }

        tracing::info!(
            "batch delete blobs: count={} tenant_id={}",
            entries.len(),
            tenant_id
        );

        let mut user_index_keys = Vec::new();
        let mut content_info_ranges = Vec::new();
        let mut cache_invalidations = Vec::new();

        // 1. First pass - verify blobs and collect keys/ranges
        for (blob_name, user_id, timestamp) in entries {
            // Always verify the blob exists, regardless of ignore_index setting
            let upload_time = match timestamp {
                Some(time) => {
                    tracing::info!("Using provided timestamp for deletion: {:?}", time);
                    *time
                }
                None => {
                    // Verify the blob exists and get the upload time
                    let blob_key = BlobContentKey::Raw(blob_name.clone());
                    match self
                        .get_blob_infos(request_context, tenant_id, &[blob_key.clone()], false)
                        .await?
                        .first()
                        .and_then(|(_, info)| info.as_ref())
                        .map(|info| info.time)
                    {
                        Some(time) => time,
                        None => {
                            return Err(tonic::Status::not_found(format!(
                                "Blob not found: {}",
                                blob_name
                            )));
                        }
                    }
                }
            };

            // Don't collect user index keys if ignore_index is true
            if !ignore_index {
                // Collect user index key
                let user_row_key =
                    get_user_row_key(user_id, upload_time.timestamp() as u64, blob_name);
                user_index_keys.push(user_row_key.into_bytes());
            }

            // Collect content/info ranges
            content_info_ranges.push((
                format!("content#{}#", blob_name).into_bytes(),
                format!("content#{}$", blob_name).into_bytes(),
            ));
            content_info_ranges.push((
                format!("info#{}#", blob_name).into_bytes(),
                format!("info#{}$", blob_name).into_bytes(),
            ));

            // Prepare cache invalidations
            // First add the raw blob
            let blob_key = BlobContentKey::Raw(blob_name.clone());
            let cache_key = (tenant_id.to_string(), blob_key);
            cache_invalidations.push(cache_key);

            // Then get and add any transformed blobs
            if let Ok(transformed_keys) = self
                .list_blob_content_keys(request_context, tenant_id, blob_name)
                .await
            {
                for transformed_key in transformed_keys {
                    let blob_key = BlobContentKey::Transformed(
                        BlobName::from(transformed_key.blob_name),
                        transformed_key.transformation_key,
                        transformed_key.sub_key,
                    );
                    let cache_key = (tenant_id.to_string(), blob_key);
                    cache_invalidations.push(cache_key);
                }
            }
        }

        // 2. Validate all user index entries exist in one call
        if !user_index_keys.is_empty() {
            let user_index_response = self
                .read_rows(
                    request_context,
                    tenant_id,
                    user_index_keys.clone(),
                    vec![],
                    None,
                )
                .await?;

            if user_index_response.len() != user_index_keys.len() {
                return Err(tonic::Status::not_found(format!(
                    "{} of {} user index entries not found",
                    user_index_keys.len() - user_index_response.len(),
                    user_index_keys.len()
                )));
            }
        }

        // 3. Get all content and info rows in one call
        let content_response = self
            .read_rows(
                request_context,
                tenant_id,
                vec![],
                content_info_ranges,
                None,
            )
            .await?;

        // 4. Prepare mutations
        let mut content_info_mutations = Vec::new();
        let mut index_mutations = Vec::new();

        // Add content and info mutations
        for (key, _) in content_response {
            content_info_mutations.push(mutate_rows_request::Entry {
                row_key: key,
                mutations: vec![Mutation {
                    mutation: Some(mutation::Mutation::DeleteFromRow(DeleteFromRow {})),
                }],
            });
        }

        // Add user index mutations
        for key in user_index_keys {
            index_mutations.push(mutate_rows_request::Entry {
                row_key: key,
                mutations: vec![Mutation {
                    mutation: Some(mutation::Mutation::DeleteFromRow(DeleteFromRow {})),
                }],
            });
        }

        // 5. Execute content/info deletions
        if !content_info_mutations.is_empty() {
            if let Err(e) = self
                .mutate_rows(request_context, tenant_id, content_info_mutations)
                .await
            {
                tracing::error!("Batch content/info deletion failed: {:?}", e);
                return Err(e);
            }
        }

        // 6. Execute index deletions
        if !index_mutations.is_empty() {
            if let Err(e) = self
                .mutate_rows(request_context, tenant_id, index_mutations)
                .await
            {
                tracing::error!("Batch index deletion failed: {:?}", e);
                return Err(e);
            }
        }

        // If both mutations succeeded, invalidate caches
        for cache_key in cache_invalidations {
            self.blob_info_cache.invalidate(&cache_key).await;
            self.content_cache.invalidate(&cache_key).await;
        }

        Ok(BatchDeleteBlobsResponse {})
    }

    async fn list_blob_content_keys(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_name: &BlobName,
    ) -> Result<Vec<BlobContentsKey>, tonic::Status> {
        tracing::info!(
            "list blob content keys for blob_name={:?} tenant_id={}",
            blob_name,
            tenant_id
        );

        // calling bigtable API to get results
        let response = self
            .read_rows(
                request_context,
                tenant_id,
                vec![],
                vec![(
                    format!("info#{}#", blob_name).into_bytes(),
                    format!("info#{}$", blob_name).into_bytes(),
                )],
                None,
            )
            .await?;

        let mut result = vec![];
        for entry in response {
            if let (Some(last_cell), _) = get_first_cell(entry.1, "Info") {
                let bytes = Bytes::from(last_cell.value);
                let mut blob_info = crate::proto::content_manager_store::BlobInfo::default();
                blob_info.merge(bytes).map_err(|e| {
                    tracing::error!("Failed to deserialize cell value: {:?}", e);
                    tonic::Status::internal("Failed to read blob")
                })?;
                if !blob_info.transformation_key.is_empty()
                    && blob_info.blob_name == blob_name.to_string()
                {
                    let blob_key = BlobContentsKey {
                        blob_name: blob_info.blob_name,
                        transformation_key: blob_info.transformation_key,
                        sub_key: blob_info.sub_key,
                    };
                    result.push(blob_key);
                }
            }
        }
        result.sort();

        tracing::debug!(
            "list blob content keys result: blob_name={:?} tenant_id={:?} keys={:?}",
            blob_name,
            tenant_id,
            result
        );

        return Ok(result);
    }

    async fn get_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blob_keys: &[BlobContentKey],
    ) -> Result<Vec<(BlobContentKey, Option<BlobInfo>)>, tonic::Status> {
        return self
            .get_blob_infos(request_context, tenant_id, blob_keys, true)
            .await;
    }

    async fn get_blobs_from_checkpoint(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        checkpoint_id: &CheckpointId,
    ) -> Result<SortedBlobNameBytesVec, tonic::Status> {
        let original_checkpoint = checkpoint_id.clone();

        tracing::info!(
            "Get blob names from checkpoint: checkpoint_id={:?}",
            &original_checkpoint
        );

        // If there is a baseline checkpoint id, look up the previous blob names from the cache.
        let id_key = (
            tenant_id.to_string(),
            CheckpointId::from(checkpoint_id.clone()),
        );
        let cached_blob_names = self.checkpoint_cache.get(&id_key).await;
        let starting_blob_names = match cached_blob_names {
            Some(r) => r,
            None => {
                let persisted_blob_names = self
                    .read_checkpoint_info_from_bigtable(request_context, tenant_id, &id_key.1)
                    .await?;
                self.checkpoint_cache
                    .insert(id_key, persisted_blob_names.clone())
                    .await;
                persisted_blob_names
            }
        };
        CACHE_BYTES_COUNT
            .with_label_values(&["checkpoint"])
            .set(self.checkpoint_cache.weighted_size().try_into().unwrap());
        CACHE_ENTRY_COUNT
            .with_label_values(&["checkpoint"])
            .set(self.checkpoint_cache.entry_count().try_into().unwrap());

        return Ok(starting_blob_names);
    }

    /// Perist the given Blobs workspace, returning a checkpoint id that represents the set.
    async fn checkpoint_blobs(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        blobs: blob_names_proto::Blobs,
    ) -> Result<CheckpointId, tonic::Status> {
        // TODO(rich): add validation check that all blob names are valid hex and consisted length
        tracing::debug!(
            "Checkpoint blob names: baseline_checkpoint_id={:?}",
            blobs.baseline_checkpoint_id
        );

        // We don't return any error for this case yet-- we just want to get logs of it happening.
        self.validate_checkpoint_blobs(request_context, tenant_id, &blobs)
            .await;

        let blob_names = self
            .resolve_blobs_into_blob_names(request_context, tenant_id, blobs)
            .await?;

        tracing::debug!(
            "Checkpoint Blobs: computing new checkpoint from blob_names={:?}",
            blob_names.as_vec().len()
        );

        // NOTE: It's possible to add/delete blobs to the point where the
        // empty set is returned. In that case, it should be ok to return
        // an empty checkpoint id (or should it be all 0s?).
        if blob_names.as_vec().is_empty() {
            return Ok(CheckpointId::from(""));
        }

        if blob_names.as_vec().len() > self.checkpoints_length_limit {
            tracing::warn!(
                "checkpoint blob_names over length limit {:?} > {:?}",
                blob_names.as_vec().len(),
                self.checkpoints_length_limit
            );
            return Err(tonic::Status::resource_exhausted(format!(
                "Checkpoint too large. Total blob names exceed {} limit",
                self.checkpoints_length_limit
            )));
        }

        let first_len = blob_names.as_vec()[0].len();
        for blob_name in blob_names.as_vec() {
            if blob_name.len() != first_len {
                tracing::warn!(
                    "blob names have different lengths: {:?} vs expected {:?}",
                    blob_name.len(),
                    first_len
                );
                return Err(tonic::Status::internal(
                    "Blob name length mismatch".to_string(),
                ));
            }
        }

        let checkpoint_id = compute_checkpoint_id(blob_names.as_vec());
        match checkpoint_id {
            Some(checkpoint_id) => {
                tracing::info!(
                    "Checkpoint blobs: computed new checkpoint_id={:?}",
                    checkpoint_id
                );
                // add checkpoint to big table
                self.mutate_rows_for_checkpoint(
                    request_context,
                    tenant_id,
                    &checkpoint_id,
                    blob_names,
                )
                .await?;
                Ok(checkpoint_id)
            }
            None => {
                tracing::warn!("Tried to compute checkpoint id from empty blob set");
                Ok(CheckpointId::from(""))
            }
        }
    }

    async fn upload_ann_index_asset(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        asset: UploadAnnIndexAsset,
    ) -> Result<(), tonic::Status> {
        tracing::info!(
            "Uploading ANN index asset: transformation_key={}, index_id={}, sub_key={}, data_size={}",
            asset.key.transformation_key,
            asset.key.index_id,
            asset.key.sub_key,
            asset.data.len()
        );
        for (i, chunk) in asset.data.chunks(self.cell_bytes_limit).enumerate() {
            let row_key = get_ann_index_asset_row_key(&asset.key, i);
            let entry = ann_index_asset_into_mutate_entry(row_key, chunk.to_vec());
            self.mutate_rows(request_context, tenant_id, vec![entry])
                .await?;
        }
        Ok(())
    }

    async fn get_ann_index_asset(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        key: &AnnIndexAssetKey,
    ) -> Result<Self::AIAS, tonic::Status> {
        tracing::info!(
            "Getting ANN index asset: transformation_key={}, index_id={}, sub_key={}",
            key.transformation_key,
            key.index_id,
            key.sub_key
        );

        let response = self
            .read_rows_streaming(
                request_context,
                tenant_id,
                vec![],
                vec![(
                    format!(
                        "ann_index#{}#{}#{}",
                        key.transformation_key, key.index_id, key.sub_key
                    )
                    .into_bytes(),
                    format!(
                        "ann_index#{}#{}#{}$",
                        key.transformation_key, key.index_id, key.sub_key
                    )
                    .into_bytes(),
                )],
                None,
            )
            .await;

        return Ok(GcpAnnIndexAssetStream {
            seen_first_result: false,
            receiver: response,
        });
    }

    async fn get_blobs_from_user(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        user_id: &SecretString,
        limit: Option<i64>,
        min_timestamp: Option<Timestamp>,
        max_timestamp: Option<Timestamp>,
    ) -> Result<Vec<(String, Option<Timestamp>)>, tonic::Status> {
        if let Some(ref min_ts) = min_timestamp {
            if let Some(ref max_ts) = max_timestamp {
                if min_ts.seconds > max_ts.seconds
                    || (min_ts.seconds == max_ts.seconds && min_ts.nanos > max_ts.nanos)
                {
                    let message = format!(
                        "min_timestamp {} is greater than max_timestamp {}",
                        min_ts, max_ts,
                    );
                    return Err(tonic::Status::invalid_argument(message));
                }
            }
        }

        // Since the input timestamp can specify nanoseconds but we need a timestamp in only
        // seconds to do the search, we round up on min_timestamp and down on max_timestamp.
        // Additionally, since `get_reversed_timestamp` does not function properly with negative
        // timestamps, make sure min and max timestamp are not less than 0.
        let min_ts_seconds = min_timestamp.map_or(0, |min_ts| {
            if min_ts.nanos > 0 {
                // Timestamp.seconds is i64 and we're creating a u64, so don't need to worry about overflow
                max(min_ts.seconds + 1, 0) as u64
            } else {
                max(min_ts.seconds, 0) as u64
            }
        });
        let max_ts_seconds = max_timestamp.map_or(u64::MAX, |max_ts| max(max_ts.seconds, 0) as u64);

        // We start the search at the max timestamp because the blobs are sorted in reversed
        // chronological order and subtract one from the min timestamp (making sure to not
        // underflow the u64) because our search is inclusive on both ends but the bigtable scan is
        // not.
        let time_prefix_start = get_reversed_timestamp(max_ts_seconds);
        let time_prefix_end = get_reversed_timestamp(max(min_ts_seconds, 1) - 1);

        let key_start =
            format!("user#{}#{}", encode_user_id(user_id), time_prefix_start).into_bytes();
        let key_end = format!("user#{}#{}", encode_user_id(user_id), time_prefix_end).into_bytes();
        let row_range = vec![(key_start, key_end)];

        if let Some(request_limit) = limit {
            if request_limit > self.get_user_blobs_limit {
                let message = format!(
                    "Requested limit {} exceeds maximum allowed limit {}",
                    request_limit, self.get_user_blobs_limit
                );
                return Err(tonic::Status::invalid_argument(message));
            }
        }

        let row_limit = match limit {
            // ensure we don't have limit == 0, which BigTable treats as no limit
            Some(request_limit) => max(request_limit, 1),
            None => self.get_user_blobs_limit,
        };

        let response = self
            .read_rows(
                request_context,
                tenant_id,
                vec![],
                row_range,
                Some(row_limit),
            )
            .await?;

        tracing::info!(
            "Reading user blobs: lookup had {:?} results with limit {:?}",
            response.len(),
            row_limit,
        );

        let blob_names: tonic::Result<Vec<(String, Option<Timestamp>)>> = response
            .into_iter()
            .flat_map(|(_, row_cells)| row_cells)
            .filter(|cell| cell.family_name == "UserIndex")
            .try_fold(Vec::new(), |mut acc, cell| {
                let bytes = Bytes::from(cell.value);
                let mut upload_info_pb = crate::proto::content_manager_store::UploadInfo::default();
                upload_info_pb.merge(bytes).map_err(|err| {
                    tracing::error!("Failed to deserialize upload info: {:?}", err);
                    tonic::Status::internal("Failed to deserialize upload info")
                })?;

                acc.push((upload_info_pb.blob_name, upload_info_pb.time));
                Ok(acc)
            });

        Ok(blob_names?)
    }

    async fn set_ann_index_mapping(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        key: &AnnIndexMappingKey,
        value: AnnIndexMappingValue,
    ) -> Result<(), tonic::Status> {
        let entry = ann_index_mapping_into_mutate_entry(
            AnnIndexMappingRowKey {
                transformation_key: key.transformation_key.to_owned(),
                checkpoint_id: key.checkpoint_id.to_owned(),
                index_id: value.index_id,
            }
            .to_string(),
            value.added_blobs,
            value.removed_blobs,
        );
        let result = self
            .mutate_rows(request_context, tenant_id, vec![entry])
            .await?;
        Ok(result)
    }

    async fn get_ann_index_mappings(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        key: &AnnIndexMappingKey,
    ) -> Result<Vec<AnnIndexMapping>, tonic::Status> {
        let ann_index_range_keys = vec![(
            AnnIndexMappingRowKey::min_str(&key.transformation_key, &key.checkpoint_id)
                .into_bytes(),
            AnnIndexMappingRowKey::max_str(&key.transformation_key, &key.checkpoint_id)
                .into_bytes(),
        )];

        // NOTE: set no limit for now, implement paging in the future if this gets out of hand
        let rows = self
            .read_rows(
                request_context,
                tenant_id,
                vec![],
                ann_index_range_keys,
                None,
            )
            .await?;
        tracing::info!(
            "Reading ann index mappings: checkpoint {}",
            key.checkpoint_id
        );
        rows.into_iter()
            .map(|(row_key, cells)| {
                let index_id = String::from_utf8(row_key)
                    .map_err(|_| tonic::Status::internal("failed to decode row key"))
                    .and_then(|s| AnnIndexMappingRowKey::from_str(s.as_str()))
                    .map(|mapping_row_key| mapping_row_key.index_id)?;

                let (maybe_cell, _) = get_first_cell(cells, "Checkpoint");
                let cell = maybe_cell.ok_or_else(|| {
                    tonic::Status::internal("No cell found for column family 'Checkpoint'")
                })?;

                let qualifier = String::from_utf8(cell.qualifier).map_err(|e| {
                    tracing::error!("Failed to decode cell qualifier: {:?}", e);
                    tonic::Status::internal("Invalid qualifier")
                })?;
                if qualifier != "Value" {
                    return Err(tonic::Status::internal("Invalid qualifier"));
                }
                let bytes = Bytes::from(cell.value);
                let decoded =
                    crate::proto::content_manager_store::AnnIndexMappingValueCell::decode(bytes)
                        .map_err(|e| {
                            tracing::error!("Failed to decode proto: {:?}", e);
                            tonic::Status::internal("Failed to decode proto")
                        })?;
                let timestamp =
                    DateTime::from_timestamp_micros(cell.timestamp_micros).ok_or_else(|| {
                        tracing::error!(
                            "Failed to decode BigTable timestamp micros: {:?}",
                            cell.timestamp_micros
                        );
                        tonic::Status::internal("Could not decode timestamp")
                    })?;
                tracing::info!(
                    "Found ann index mapping: checkpoint {}, index_id {}, added {:?}, removed {:?}",
                    key.checkpoint_id,
                    index_id,
                    decoded.added_blobs,
                    decoded.removed_blobs,
                );
                let added_blobs = decoded
                    .added_blobs
                    .into_iter()
                    .map(|blob_bytes| blob_names::BlobName::from_bytes(&blob_bytes))
                    .collect::<tonic::Result<Vec<_>>>()?;
                let removed_blobs = decoded
                    .removed_blobs
                    .into_iter()
                    .map(|blob_bytes| blob_names::BlobName::from_bytes(&blob_bytes))
                    .collect::<tonic::Result<Vec<_>>>()?;
                Ok(AnnIndexMapping {
                    index_id,
                    added_blobs,
                    removed_blobs,
                    timestamp,
                })
            })
            .collect::<Result<Vec<_>, _>>()
    }

    async fn get_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        transformation_key: &str,
        index_id: &str,
    ) -> Result<Vec<AnnIndexBlobInfoValue>, tonic::Status> {
        // NOTE: we may need to paginate this in the future and should keep an eye on the response
        // length.
        let row_key = AnnIndexBlobInfoRowKey {
            transformation_key: transformation_key.to_owned(),
            index_id: index_id.to_owned(),
        }
        .to_string();
        let mut rows = self
            .read_rows(
                request_context,
                tenant_id,
                vec![row_key.into_bytes()],
                Vec::new(),
                None,
            )
            .await?;

        if rows.is_empty() {
            return Err(tonic::Status::not_found("No index found"));
        }
        if rows.len() > 1 {
            tracing::error!(
                "Invalid number of bigtable results for GetAnnIndexBlobInfo query (expected 1): {}",
                rows.len()
            );
            return Err(tonic::Status::internal("Data inconsistency detected"));
        }

        let (_key, cells) = rows.remove(0);
        let (cell, _) = get_first_cell(cells, "AnnIndex");

        let infos = cell
            .ok_or_else(|| tonic::Status::internal("No cell for rowkey"))
            .and_then(|raw_cell| {
                let qualifier = String::from_utf8(raw_cell.qualifier).map_err(|e| {
                    tracing::error!("Failed to decode cell qualifier: {:?}", e);
                    tonic::Status::internal("Invalid qualifier")
                })?;
                if qualifier != "Value" {
                    return Err(tonic::Status::internal("Invalid qualifier"));
                }
                let bytes = Bytes::from(raw_cell.value);
                let decoded =
                    crate::proto::content_manager_store::AnnIndexBlobInfoCell::decode(bytes)
                        .map_err(|e| {
                            tracing::error!("Failed to decode proto: {:?}", e);
                            tonic::Status::internal("Failed to decode proto")
                        })?;
                let infos = decoded
                    .infos
                    .into_iter()
                    .map(|info| {
                        let blob_name =
                            blob_names::BlobName::from_bytes(info.blob_name.as_slice())?;
                        Ok(AnnIndexBlobInfoValue {
                            blob_name,
                            chunk_count: info.chunk_count,
                        })
                    })
                    .collect::<tonic::Result<Vec<_>>>()?;
                Ok(infos)
            })?;

        Ok(infos)
    }

    async fn upload_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        payload: UploadAnnIndexBlobInfosPayload,
    ) -> Result<(), tonic::Status> {
        let entry = ann_index_blob_info_into_mutate_entry(
            AnnIndexBlobInfoRowKey {
                transformation_key: payload.transformation_key.to_owned(),
                index_id: payload.index_id.to_owned(),
            }
            .to_string(),
            payload.blob_infos,
        );
        let result = self
            .mutate_rows(request_context, tenant_id, vec![entry])
            .await?;
        Ok(result)
    }
}

impl GcpBatchContentStream<'_> {
    fn build_batch(&mut self) -> Vec<BlobContentKey> {
        let mut query_blob_content_keys = vec![];
        while self.entry_index < self.entries.len()
            && query_blob_content_keys.len() < self.batch_size
        {
            let current_entry = &self.entries[self.entry_index];
            let blob_content_key = current_entry.clone();
            self.entry_index += 1;
            query_blob_content_keys.push(blob_content_key);
        }
        query_blob_content_keys
    }

    fn get_next_result(&mut self) -> Option<TryNextResult> {
        if self.result_index >= self.results.len() {
            // we processed all available results, signal that the next batch can start
            self.result_index = 0;
            self.results.clear();
            None
        } else {
            let (blob_content_key, result) = self.results[self.result_index].clone();
            self.result_index += 1;
            match result {
                // return the content
                Some(r) => Some(TryNextResult::FinalContent(blob_content_key, r.0, r.1)),
                // None means that the blob doesn't exist
                None => Some(TryNextResult::UnknownBlob(blob_content_key)),
            }
        }
    }
}

async fn query_batch(
    parent: &GcpObjectStoreImpl,
    request_context: RequestContext,
    tenant_id: TenantId,
    query_blob_content_keys: Vec<BlobContentKey>,
) -> Result<Vec<(BlobContentKey, Option<(BlobInfo, Bytes)>)>, tonic::Status> {
    let (blob_contents, blob_infos) = try_join!(
        parent
            .get_batch_content(&request_context, &tenant_id, &query_blob_content_keys)
            .instrument(tracing::info_span!("get_batch_content")),
        parent
            .get_blob_infos(&request_context, &tenant_id, &query_blob_content_keys, true)
            .instrument(tracing::info_span!("get_blob_infos")),
    )?;

    let results = blob_infos
        .into_iter()
        .zip(blob_contents.into_iter())
        .map(
            |((blob_key, blob_info), content)| match (blob_info, content) {
                (Some(blob_info), Some(content)) => (blob_key, Some((blob_info, content))),
                (_, _) => (blob_key, None),
            },
        )
        .collect_vec();
    assert_eq!(query_blob_content_keys.len(), results.len());
    Ok(results)
}

#[async_trait]
impl BatchGetContentStream for GcpBatchContentStream<'_> {
    /// try to get the next blob content key.
    ///
    /// The implementation receives the content one blob key at a time.
    async fn try_next(
        &mut self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
    ) -> Result<Option<TryNextResult>, tonic::Status> {
        let mut futures = FuturesOrdered::new();
        loop {
            if !self.results.is_empty() {
                if let Some(result) = self.get_next_result() {
                    return Ok(Some(result));
                }
                // let's get the next batch
            }

            if futures.len() < 2 {
                let query_blob_content_keys = self.build_batch();
                if query_blob_content_keys.is_empty() {
                    if futures.is_empty() {
                        // No new queries and no running queries means everything is processed
                        return Ok(None);
                    }
                } else {
                    futures.push_back(query_batch(
                        self.parent,
                        request_context.clone(),
                        tenant_id.clone(),
                        query_blob_content_keys,
                    ));
                }
            }

            // Invariant: !futures.is_empty() after the previous block, so unwrap() should be safe
            let mut results = futures.next().await.unwrap()?;

            self.results.append(&mut results);
            self.result_index = 0;
        }
    }
}

#[async_trait]
impl AnnIndexAssetStream for GcpAnnIndexAssetStream {
    async fn try_next(
        &mut self,
        _request_context: &RequestContext,
        _tenant_id: &TenantId,
    ) -> Result<Option<Vec<u8>>, tonic::Status> {
        match self.receiver.recv().await {
            Some(Ok(response)) => {
                self.seen_first_result = true;
                let mut result = vec![];
                for (_, cells) in response.into_iter() {
                    let (asset_cell, _) = get_first_cell(cells, "AnnIndex");

                    match asset_cell {
                        Some(cell) => {
                            result.extend(cell.value.into_iter());
                        }
                        None => {
                            return Err(tonic::Status::not_found("ANN index asset not found"));
                        }
                    }
                }
                Ok(Some(result))
            }
            Some(Err(status)) => {
                self.seen_first_result = true;
                Err(status)
            }
            None => {
                // Distinguish between empty-but-present asset and non-existent asset
                if !self.seen_first_result {
                    Err(tonic::Status::not_found("ANN index asset not found"))
                } else {
                    Ok(None)
                }
            }
        }
    }
}

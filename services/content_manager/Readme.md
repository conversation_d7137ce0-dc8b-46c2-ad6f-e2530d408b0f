# Content Manager

The content manager is responsible for all content for ingest and retrieval.
(Ingest is called “memorization” in the current modelhost based architecture, but it applies to all forms of chunk retrieval, not just memorization).

The content manager will store an uploaded block as byte data and this source-of-truth will be used to generate other representations, e.g. memorization for a given model.

The content manager is not intended as general object storage.
A rule of thump: if the content is not intended to be indexed, it should not be stored in the content manager.

Each transformation takes place in a separate service, e.g. memorization for a given model, chunk retrieval inject.
Transformation keys are dynamically created and deleted via `transformation-keys.eng.augmentcode.com/TransformationKey` K8S objects.
After a transformation key is dynamically created, an indexer is able to register for notifications via the `Subscribe` RPC.
It is then informed about any newly added blob and about any existing blob references in `FindMissing`/`GetContent`, or `BatchGetContent` RPC (catchup).

The content manager acts as a cache. It is free to delete blob data or memorized version of it at any point in time.

### Priority

The indexing is three priorities:

- `NON_RATE_LIMITED`: The default priority intended for few uploads, e.g. saving of files by the user. Highest priority.
- `RATE_LIMITED`: Intended for large uploads, e.g. new repositories
- `LOW` intended for low priority indexing,, e.g. internal uploads (docsets) or catchup notifications.

## Security considerations

The content manager is the central persistence system for customer source code.

### Authentication and authorization

Each request has to be authorized with a service token for the right tenant and with CONTENT_R and CONTENT_RW scopes.

## Persistence

The content manager uses BigTable via the bigtable proxy service.

## Testing

The content manager is tested via unit test and via a dedicated E2E test in the `test` directory.

## Links

- https://www.notion.so/Thoughts-on-App-system-design-b36fbe3f75164e868ea09f88dded72ee?pvs=4

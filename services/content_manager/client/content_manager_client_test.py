"""Test for content_manager_client."""

from unittest import mock

import grpc
from google.rpc import status_pb2
from services.lib.request_context.request_context import RequestContext
import pytest

from services.content_manager import content_manager_pb2, content_manager_pb2_grpc
from services.content_manager.client.content_manager_client import (
    AnnIndexAsset,
    AnnIndexKey,
    ContentKey,
    ContentManagerClient,
    ContentManagerException,
    GetBestAnnIndexResult,
    _get_content_hash,
)


@pytest.fixture
def content_manager_client() -> ContentManagerClient:
    stub = mock.MagicMock(
        content_manager_pb2_grpc.ContentManagerStub,
        # NOTE(arun): MagicMock's can't automatically add mocked methods for proto
        # stubs.
        BatchGetContent=mock.Mock(),
        GetAllBlobsFromCheckpoint=mock.Mock(),
        GetUserBlobs=mock.Mock(),
        BatchDeleteBlobs=mock.Mock(),
        GetBestAnnIndex=mock.Mock(),
        GetAnnIndexBlobInfos=mock.Mock(),
        GetAnnIndexAsset=mock.Mock(),
        AddAnnIndexMapping=mock.Mock(),
        UploadAnnIndexAssets=mock.Mock(),
    )
    return ContentManagerClient(stub)


def test_batch_download_all_with_micro_batches(
    content_manager_client: ContentManagerClient,
):
    # Simulate a stream of batched content with more entries than the micro batch
    # size permits.
    content_manager_client.stub.BatchGetContent.side_effect = [
        [
            content_manager_pb2.BatchGetContentResponse(
                final_content=content_manager_pb2.BatchGetContentFinalContent(
                    content=b"batch1",
                    blob_name="blob1",
                    transformation_key="key1",
                    sub_key="1",
                    final_hash=_get_content_hash(b"batch1"),
                )
            )
        ],
        [
            content_manager_pb2.BatchGetContentResponse(
                final_content=content_manager_pb2.BatchGetContentFinalContent(
                    content=b"batch2",
                    blob_name="blob2",
                    transformation_key="key2",
                    sub_key="2",
                    final_hash=_get_content_hash(b"batch2"),
                )
            )
        ],
    ]

    response = content_manager_client.batch_download_all(
        [ContentKey("blob1", "key1", "1"), ContentKey("blob2", "key2", "2")],
        micro_batch_size=1,
        request_context=RequestContext.create(),
    )
    assert list(response) == [(b"batch1", {}), (b"batch2", {})]
    assert content_manager_client.stub.BatchGetContent.call_count == 2


def test_batch_download_all_server_sends_partial_response(
    content_manager_client: ContentManagerClient,
):
    """Test that we raise an assertion when the server doesn't respond for all keys."""
    content_manager_client.stub.BatchGetContent.side_effect = [
        [
            content_manager_pb2.BatchGetContentResponse(
                final_content=content_manager_pb2.BatchGetContentFinalContent(
                    content=b"batch1",
                    blob_name="blob1",
                    transformation_key="key1",
                    sub_key="1",
                    final_hash=_get_content_hash(b"batch1"),
                )
            )
        ],
    ]

    with pytest.raises(ValueError):
        list(
            content_manager_client.batch_download_all(
                [ContentKey("blob1", "key1", "1"), ContentKey("blob2", "key2", "2")],
                request_context=RequestContext.create(),
            )
        )


class MockRpcError(grpc.RpcError):
    """Mock gRPC error.

    Note that subclasses of grpc.RpcError are expected to have code() and details() methods even
    though RpcError itself does not.
    """

    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.msg

    def __str__(self) -> str:
        return f"MockRpcError({self.status_code}: {self.msg})"


def test_get_all_blobs_from_checkpoint(content_manager_client: ContentManagerClient):
    """Validate the get_all_blobs_from_checkpoint API."""

    blob_names = [bytes.fromhex("00"), bytes.fromhex("01")]
    content_manager_client.stub.GetAllBlobsFromCheckpoint.return_value = (
        content_manager_pb2.GetAllBlobsFromCheckpointResponse(blob_names=blob_names),
    )
    checkpoint = "cafe"
    # Happy path
    request_context = RequestContext.create()
    response = content_manager_client.get_all_blobs_from_checkpoint(
        checkpoint, request_context=request_context
    )
    assert response == blob_names

    # Checkpoint not found
    content_manager_client.stub.GetAllBlobsFromCheckpoint.side_effect = MockRpcError(
        grpc.StatusCode.NOT_FOUND, "Not found"
    )
    response = content_manager_client.get_all_blobs_from_checkpoint(
        checkpoint, request_context=request_context
    )
    assert response is None

    # Content Manager unable to resolve checkpoint
    content_manager_client.stub.GetAllBlobsFromCheckpoint.side_effect = MockRpcError(
        grpc.StatusCode.UNKNOWN, "Unknown"
    )
    with pytest.raises(ContentManagerException):
        content_manager_client.get_all_blobs_from_checkpoint(
            checkpoint, request_context=request_context
        )


def test_batch_delete_blobs_failure(content_manager_client: ContentManagerClient):
    """Test batch deletion of blobs with an invalid blob."""
    # Mock an RPC error for NOT_FOUND case
    content_manager_client.stub.BatchDeleteBlobs.side_effect = MockRpcError(
        grpc.StatusCode.NOT_FOUND, "Blob not found: nonexistent_blob"
    )

    # Call the batch delete method
    result = content_manager_client.batch_delete_blobs(
        blob_user_pairs=[
            ("blob1", "user1"),
            ("blob2", "user1"),
            ("nonexistent_blob", "user1"),
        ],
        request_context=RequestContext.create(),
    )

    # Verify the RPC call was made with correct parameters
    content_manager_client.stub.BatchDeleteBlobs.assert_called_once()
    request = content_manager_client.stub.BatchDeleteBlobs.call_args[0][0]
    assert len(request.entries) == 3, "Request should contain exactly 3 entries"
    assert request.entries[0].blob_name == "blob1", "First entry should be blob1"
    assert (
        request.entries[0].user_id == "user1"
    ), "First entry should have user_id user1"
    assert request.entries[1].blob_name == "blob2", "Second entry should be blob2"
    assert (
        request.entries[1].user_id == "user1"
    ), "Second entry should have user_id user1"
    assert (
        request.entries[2].blob_name == "nonexistent_blob"
    ), "Third entry should be nonexistent_blob"
    assert (
        request.entries[2].user_id == "user1"
    ), "Third entry should have user_id user1"

    # Verify the result
    assert result is False, "Should return False when blobs are not found"


def test_batch_delete_blobs_success(content_manager_client: ContentManagerClient):
    """Test successful batch deletion of blobs with valid blobs and user IDs."""
    # Mock the response from the server for successful deletion
    content_manager_client.stub.BatchDeleteBlobs.return_value = (
        content_manager_pb2.BatchDeleteBlobsResponse()  # Empty response indicates success
    )

    # Call the batch delete method with valid blobs
    result = content_manager_client.batch_delete_blobs(
        blob_user_pairs=[
            ("valid_blob1", "user1"),
            ("valid_blob2", "user1"),
        ],
        request_context=RequestContext.create(),
    )

    # Verify the RPC call was made with correct parameters
    content_manager_client.stub.BatchDeleteBlobs.assert_called_once()
    request = content_manager_client.stub.BatchDeleteBlobs.call_args[0][0]
    assert len(request.entries) == 2, "Request should contain exactly 2 entries"
    assert (
        request.entries[0].blob_name == "valid_blob1"
    ), "First entry should be valid_blob1"
    assert (
        request.entries[0].user_id == "user1"
    ), "First entry should have user_id user1"
    assert (
        request.entries[1].blob_name == "valid_blob2"
    ), "Second entry should be valid_blob2"
    assert (
        request.entries[1].user_id == "user1"
    ), "Second entry should have user_id user1"

    # Verify success
    assert result is True, "Should return True for successful deletion"


def test_batch_delete_blobs_rpc_error(content_manager_client: ContentManagerClient):
    """Test batch deletion when RPC fails."""
    # Mock an RPC error
    content_manager_client.stub.BatchDeleteBlobs.side_effect = MockRpcError(
        grpc.StatusCode.INTERNAL, "Internal error"
    )

    # Verify that ContentManagerException is raised
    with pytest.raises(ContentManagerException) as exc_info:
        content_manager_client.batch_delete_blobs(
            blob_user_pairs=[("blob1", "user1")],
            request_context=RequestContext.create(),
        )

    assert (
        exc_info.value.status_code == grpc.StatusCode.INTERNAL
    ), "Exception should have INTERNAL status code"
    assert "Internal error" in str(
        exc_info.value
    ), "Exception message should contain 'Internal error'"


def test_get_best_ann_index(content_manager_client: ContentManagerClient):
    """Test get_best_ann_index."""
    # Setup mock responses
    response1 = content_manager_pb2.GetBestAnnIndexResponse(
        index_id="test_index_id",
        added_blobs=[b"added1", b"added2"],
        removed_blobs=[b"removed1"],
    )
    content_manager_client.stub.GetBestAnnIndex.return_value = response1

    # Call the method
    result = content_manager_client.get_best_ann_index(
        transformation_key="test_tkey",
        checkpoint_id="test_checkpoint",
        request_context=RequestContext.create(),
    )

    # Verify the request
    content_manager_client.stub.GetBestAnnIndex.assert_called_once()
    request = content_manager_client.stub.GetBestAnnIndex.call_args[0][0]
    assert request.transformation_key == "test_tkey"
    assert request.checkpoint_id == "test_checkpoint"

    # Verify the result
    assert result.index_id == "test_index_id"
    assert result.added_blobs == [b"added1", b"added2"]
    assert result.removed_blobs == [b"removed1"]


def test_get_ann_index_blob_infos(content_manager_client: ContentManagerClient):
    """Test list_ann_index_blobs."""
    # Setup mock responses
    response1 = content_manager_pb2.GetAnnIndexBlobInfosResponse(
        blob_infos=[
            content_manager_pb2.AnnIndexBlobInfo(blob_name=b"blob1", chunk_count=1),
            content_manager_pb2.AnnIndexBlobInfo(blob_name=b"blob2", chunk_count=2),
        ],
    )
    response2 = content_manager_pb2.GetAnnIndexBlobInfosResponse(
        blob_infos=[
            content_manager_pb2.AnnIndexBlobInfo(blob_name=b"blob3", chunk_count=4),
        ],
    )
    content_manager_client.stub.GetAnnIndexBlobInfos.return_value = [
        response1,
        response2,
    ]

    # Call the method
    key = AnnIndexKey(transformation_key="tkey1", index_id="index1")
    result = content_manager_client.get_ann_index_blob_infos(
        key=key, request_context=RequestContext.create()
    )

    # Verify the request
    content_manager_client.stub.GetAnnIndexBlobInfos.assert_called_once()
    request = content_manager_client.stub.GetAnnIndexBlobInfos.call_args[0][0]
    assert request.key.transformation_key == "tkey1"
    assert request.key.index_id == "index1"

    # Verify the result
    assert result == [
        content_manager_pb2.AnnIndexBlobInfo(blob_name=b"blob1", chunk_count=1),
        content_manager_pb2.AnnIndexBlobInfo(blob_name=b"blob2", chunk_count=2),
        content_manager_pb2.AnnIndexBlobInfo(blob_name=b"blob3", chunk_count=4),
    ]


def test_get_ann_index_asset(content_manager_client: ContentManagerClient):
    """Test get_ann_index_asset."""
    # Setup mock responses

    response1 = content_manager_pb2.GetAnnIndexAssetResponse(
        data=b"data1",
    )
    response2 = content_manager_pb2.GetAnnIndexAssetResponse(
        data=b"data2",
    )
    content_manager_client.stub.GetAnnIndexAsset.return_value = [response1, response2]

    # Call the method
    result = content_manager_client.get_ann_index_asset(
        transformation_key="test_tkey",
        index_id="test_index",
        sub_key="asset1",
        request_context=RequestContext.create(),
    )

    # Verify the request
    content_manager_client.stub.GetAnnIndexAsset.assert_called_once()
    request = content_manager_client.stub.GetAnnIndexAsset.call_args[0][0]
    assert request.key.transformation_key == "test_tkey"
    assert request.key.index_id == "test_index"
    assert request.key.sub_key == "asset1"

    # Verify the result
    assert result == b"data1data2"


def test_add_ann_index_mapping(content_manager_client: ContentManagerClient):
    """Test add_ann_index_mapping."""
    # Call the method
    content_manager_client.add_ann_index_mapping(
        transformation_key="test_tkey",
        checkpoint_id="test_checkpoint",
        index_id="test_index",
        added_blobs=[b"added1", b"added2"],
        removed_blobs=[b"removed1"],
        request_context=RequestContext.create(),
    )

    # Verify the request
    content_manager_client.stub.AddAnnIndexMapping.assert_called_once()
    request = content_manager_client.stub.AddAnnIndexMapping.call_args[0][0]
    assert request.key.transformation_key == "test_tkey"
    assert request.key.checkpoint_id == "test_checkpoint"
    assert request.index_id == "test_index"
    assert request.added_blobs == [b"added1", b"added2"]
    assert request.removed_blobs == [b"removed1"]


def test_upload_ann_index_assets(content_manager_client: ContentManagerClient):
    """Test upload_ann_index_assets."""
    # Setup assets
    assets = [
        ("asset1", b"data1"),
        ("asset2", b"data2"),
    ]

    # Call the method
    content_manager_client.upload_ann_index_assets(
        transformation_key="test_tkey",
        index_id="test_index",
        assets=assets,
        request_context=RequestContext.create(),
    )

    # Verify the request
    content_manager_client.stub.UploadAnnIndexAssets.assert_called_once()

    # Since we're using a generator, we need to extract the requests
    request_generator = content_manager_client.stub.UploadAnnIndexAssets.call_args[0][0]
    requests = list(request_generator)

    assert len(requests) == 2

    # First request
    assert requests[0].key.transformation_key == "test_tkey"
    assert requests[0].key.index_id == "test_index"
    assert requests[0].key.sub_key == "asset1"
    assert requests[0].data == b"data1"
    assert requests[0].last_message is False

    # Second request
    assert requests[1].key.transformation_key == "test_tkey"
    assert requests[1].key.index_id == "test_index"
    assert requests[1].key.sub_key == "asset2"
    assert requests[1].data == b"data2"
    assert requests[1].last_message is True


def test_batch_delete_ignore_index_success(
    content_manager_client: ContentManagerClient,
):
    """Test successful batch deletion with ignore_index=True parameter."""
    # Mock the response from the server for successful deletion
    content_manager_client.stub.BatchDeleteBlobs.return_value = (
        content_manager_pb2.BatchDeleteBlobsResponse()  # Empty response indicates success
    )

    # Call the batch delete method with ignore_index=True
    result = content_manager_client.batch_delete_blobs(
        blob_user_pairs=[
            ("raw_blob1", "user1"),
            ("raw_blob2", "user2"),
        ],
        request_context=RequestContext.create(),
        tenant_id="test-tenant",
        ignore_index=True,
    )

    # Verify the RPC call was made with correct parameters
    content_manager_client.stub.BatchDeleteBlobs.assert_called_once()
    request = content_manager_client.stub.BatchDeleteBlobs.call_args[0][0]

    # Check that the request has the correct entries
    assert len(request.entries) == 2, "Request should contain exactly 2 entries"
    assert (
        request.entries[0].blob_name == "raw_blob1"
    ), "First entry should be raw_blob1"
    assert (
        request.entries[0].user_id == "user1"
    ), "First entry should have user_id user1"
    assert (
        request.entries[1].blob_name == "raw_blob2"
    ), "Second entry should be raw_blob2"
    assert (
        request.entries[1].user_id == "user2"
    ), "Second entry should have user_id user2"

    # Check that ignore_index is set to True
    assert request.ignore_index is True, "ignore_index should be True"

    # Check that tenant_id is set correctly
    assert request.tenant_id == "test-tenant", "tenant_id should be set to test-tenant"

    # Verify success
    assert result is True, "Should return True for successful deletion"


def test_batch_delete_ignore_index_not_found(
    content_manager_client: ContentManagerClient,
):
    """Test batch deletion with ignore_index=True when blobs are not found."""
    # Mock a NOT_FOUND error response
    content_manager_client.stub.BatchDeleteBlobs.side_effect = MockRpcError(
        grpc.StatusCode.NOT_FOUND, 'Blobs not found: [BlobName("nonexistent_raw_blob")]'
    )

    # Call the batch delete method with ignore_index=True
    result = content_manager_client.batch_delete_blobs(
        blob_user_pairs=[
            ("nonexistent_raw_blob", "user1"),
        ],
        request_context=RequestContext.create(),
        tenant_id="test-tenant",
        ignore_index=True,
    )

    # Verify the RPC call was made with correct parameters
    content_manager_client.stub.BatchDeleteBlobs.assert_called_once()
    request = content_manager_client.stub.BatchDeleteBlobs.call_args[0][0]

    # Check that the request has the correct entry
    assert len(request.entries) == 1, "Request should contain exactly 1 entry"
    assert (
        request.entries[0].blob_name == "nonexistent_raw_blob"
    ), "Entry should be nonexistent_raw_blob"
    assert request.entries[0].user_id == "user1", "Entry should have user_id user1"

    # Check that ignore_index is set to True
    assert request.ignore_index is True, "ignore_index should be True"

    # Check that tenant_id is set correctly
    assert request.tenant_id == "test-tenant", "tenant_id should be set to test-tenant"

    # Verify failure
    assert result is False, "Should return False when blobs are not found"

[package]
name = "content_manager_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "content_manager_client"
path = "content_manager_client.rs"

[dependencies]
async-lock =  { workspace = true }
async-trait =  { workspace = true }
blob_names = { path = "../../../base/blob_names/rust" }
blob_names_rs_proto = { path = "../../../base/blob_names" }
feature-flags = { path = "../../../base/feature_flags" }
grpc_client = { path = "../../lib/grpc/client" }
itertools = { workspace = true }
mockall = "0.13.1"
prost =  { workspace = true }
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
tonic =  { workspace = true }
tonic-build =  { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
secrecy = {workspace = true}
request_context = { path = "../../lib/request_context" }
content_manager_rs_proto = { path = "../" }

[build-dependencies]
tonic-build =  { workspace = true }

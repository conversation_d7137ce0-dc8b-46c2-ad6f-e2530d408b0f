use std::{
    sync::Arc,
    time::{Duration, Instant},
    vec,
};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::{create_channel, receiver_for_error, receiver_for_stream};
use itertools::{Itertools, Position};
use mockall::automock;
use request_context::{RequestContext, TenantId};
use secrecy::{ExposeSecret, SecretString, SecretVec};
use tokio::sync::mpsc::Receiver;
use tokio_stream::{self};
use tonic::transport::ClientTlsConfig;

use blob_names::BlobName;
use blob_names_rs_proto::base::blob_names as blob_names_proto;
use content_manager_rs_proto::content_manager::{
    self, AddAnnIndexMappingRequest, AddAnnIndexMappingRequestKey, AddAnnIndexMappingResponse,
    AnnIndexAssetKey, AnnIndexBlobInfo, AnnIndexKey, BatchGetBlobInfoRequest,
    BatchGetBlobInfoResponse, BatchGetContentResponse, BlobContentKey, BlobMetadata,
    GetAllBlobsFromCheckpointResponse, GetAnnIndexAssetRequest, GetAnnIndexBlobInfosRequest,
    GetBestAnnIndexRequest, GetBlobInfoRequest, GetBlobInfoResponse, UploadAnnIndexAssetsRequest,
    UploadAnnIndexAssetsResponse, UploadAnnIndexBlobInfosRequest, UploadAnnIndexBlobInfosResponse,
};

use tracing_tonic::client::TracingService;

const MAX_ANN_INDEX_STREAMING_CHUNK_SIZE: usize = 2 * 1024 * 1024;

#[derive(Clone, PartialEq, Eq, Debug, Hash, PartialOrd, Ord)]
pub struct BlobScope {
    pub transformation_key: String,
    pub sub_key: String,
}

pub struct UploadContent {
    pub content: SecretVec<u8>,
    pub path: SecretString,
}

pub struct AnnIndexAssetData {
    pub data: Vec<u8>,
}

pub struct GetBestAnnIndexResult {
    pub index_id: String,
    pub added_blobs: Vec<BlobName>,
    pub removed_blobs: Vec<BlobName>,
}

#[derive(Debug)]
pub struct AnnIndexBlobInfoData {
    pub blob_name: BlobName,
    pub chunk_count: u32,
}

#[derive(Debug)]
pub struct GetAnnIndexBlobInfosResult {
    pub infos: Vec<AnnIndexBlobInfoData>,
}

#[automock]
#[async_trait]
pub trait ContentManagerClient {
    async fn upload_blob_content(
        &self,
        request_context: &RequestContext,
        path: &SecretString,
        content: &SecretVec<u8>,
        deadline: Option<Instant>,
    ) -> Result<String, tonic::Status>;

    async fn batch_upload_blob_content(
        &self,
        request_context: &RequestContext,
        blobs: Vec<UploadContent>,
        deadline: Option<Instant>,
    ) -> Result<Vec<BlobName>, tonic::Status>;

    async fn find_missing_blobs(
        &self,
        request_context: &RequestContext,
        blob_names: &[String],
        deadline: Option<Instant>,
    ) -> Result<Vec<String>, tonic::Status>;

    async fn checkpoint_blobs(
        &self,
        request_context: &RequestContext,
        blobs: &blob_names_proto::Blobs,
        deadline: Option<Instant>,
    ) -> Result<String, tonic::Status>;

    async fn get_blob_info(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blob_name: BlobContentKey,
    ) -> tonic::Result<GetBlobInfoResponse>;

    async fn batch_get_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blob_names: Vec<BlobContentKey>,
    ) -> tonic::Result<BatchGetBlobInfoResponse>;

    /// The order of the returned responses will be the same as the input vector
    async fn get_content(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blob_scope: &BlobScope,
        blob_names: &[BlobName],
        deadline: Option<Instant>,
    ) -> Receiver<tonic::Result<BatchGetContentResponse>>;

    /// The order of the returned responses will be the same as the input vector
    async fn get_content_multiple_scopes(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blobs: Vec<(BlobName, BlobScope)>,
        deadline: Option<Instant>,
    ) -> Receiver<tonic::Result<BatchGetContentResponse>>;

    async fn get_checkpoint_blob_names(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        checkpoint_id: &str,
    ) -> Receiver<tonic::Result<GetAllBlobsFromCheckpointResponse>>;

    async fn get_best_ann_index(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        checkpoint_id: &str,
    ) -> tonic::Result<GetBestAnnIndexResult>;

    // sends one message over the stream per queried key
    async fn get_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
    ) -> tonic::Result<GetAnnIndexBlobInfosResult>;

    async fn get_ann_index_asset(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        sub_key: &str,
    ) -> tonic::Result<Vec<u8>>;

    #[allow(clippy::too_many_arguments)]
    async fn add_ann_index_mapping(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        checkpoint_id: &str,
        index_id: &str,
        added_blobs: &[BlobName],
        removed_blobs: &[BlobName],
    ) -> tonic::Result<AddAnnIndexMappingResponse>;

    async fn upload_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        infos: &[AnnIndexBlobInfoData],
    ) -> tonic::Result<UploadAnnIndexBlobInfosResponse>;

    async fn upload_ann_index_assets(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        assets: &[(String, Vec<u8>)],
    ) -> tonic::Result<UploadAnnIndexAssetsResponse>;
}

#[derive(Clone)]
pub struct ContentManagerClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Option<Duration>,
    client: Arc<
        Mutex<
            Option<content_manager::content_manager_client::ContentManagerClient<TracingService>>,
        >,
    >,
}

#[async_trait]
impl ContentManagerClient for ContentManagerClientImpl {
    async fn upload_blob_content(
        &self,
        request_context: &RequestContext,
        path: &SecretString,
        content: &SecretVec<u8>,
        deadline: Option<Instant>,
    ) -> Result<String, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {}", e);
            tonic::Status::unavailable("content manager not ready")
        })?;
        let mut request = tonic::Request::new(content_manager::UploadBlobContentRequest {
            metadata: [BlobMetadata {
                key: "path".to_string(),
                value: path.expose_secret().to_string(),
            }]
            .to_vec(),
            content: content.expose_secret().to_vec(),
            tenant_id: "".to_string(), // the api proxxy always has a token with a tenant id set
        });
        if let Some(deadline) = deadline {
            // Pass the timeout along the gRPC call
            request.set_timeout(deadline - Instant::now());
        }
        request_context.annotate(request.metadata_mut());

        let response = client.upload_blob_content(request).await?;
        Ok(response.into_inner().blob_name)
    }

    async fn batch_upload_blob_content(
        &self,
        request_context: &RequestContext,
        blobs: Vec<UploadContent>,
        deadline: Option<Instant>,
    ) -> Result<Vec<BlobName>, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {}", e);
            tonic::Status::unavailable("content manager not ready")
        })?;
        let mut request = tonic::Request::new(content_manager::BatchUploadBlobContentRequest {
            entries: blobs
                .iter()
                .map(|blob| content_manager::UploadBlobContent {
                    metadata: [BlobMetadata {
                        key: "path".to_string(),
                        value: blob.path.expose_secret().to_string(),
                    }]
                    .to_vec(),
                    content: blob.content.expose_secret().to_vec(),
                })
                .collect(),
            tenant_id: "".to_string(), // the api proxxy always has a token with a tenant id set
            ..Default::default()
        });
        if let Some(deadline) = deadline {
            // Pass the timeout along the gRPC call
            request.set_timeout(deadline - Instant::now());
        }
        request_context.annotate(request.metadata_mut());

        let response = client.batch_upload_blob_content(request).await?;
        response
            .into_inner()
            .results
            .into_iter()
            .map(|r| r.blob_name.as_str().try_into())
            .collect()
    }

    async fn find_missing_blobs(
        &self,
        request_context: &RequestContext,
        blob_names: &[String],
        deadline: Option<Instant>,
    ) -> Result<Vec<String>, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {}", e);
            tonic::Status::unavailable("content manager not ready")
        })?;
        let mut request = tonic::Request::new(content_manager::FindMissingBlobRequest {
            blob_names: blob_names.to_vec(),
            transformation_key: "".to_string(),
            sub_key: "".to_string(),
            tenant_id: "".to_string(), // the api proxxy always has a token with a tenant id set
        });
        if let Some(deadline) = deadline {
            // Pass the timeout along the gRPC call
            request.set_timeout(deadline - Instant::now());
        }
        request_context.annotate(request.metadata_mut());

        let response = client.find_missing_blobs(request).await?;
        Ok(response.into_inner().unknown_blob_names)
    }

    async fn checkpoint_blobs(
        &self,
        request_context: &RequestContext,
        blobs: &blob_names_proto::Blobs,
        deadline: Option<Instant>,
    ) -> Result<String, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {}", e);
            tonic::Status::unavailable("content manager not ready")
        })?;

        let mut request = tonic::Request::new(content_manager::CheckpointBlobsRequest {
            blobs: Some(blobs.clone()),
            tenant_id: "".to_string(), // the api proxxy always has a token with a tenant id set
        });
        if let Some(deadline) = deadline {
            // Pass the timeout along the gRPC call
            request.set_timeout(deadline - Instant::now());
        }
        request_context.annotate(request.metadata_mut());

        let response = client.checkpoint_blobs(request).await?;
        Ok(response.into_inner().checkpoint_id)
    }

    async fn get_blob_info(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blob_content_key: BlobContentKey,
    ) -> tonic::Result<GetBlobInfoResponse> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {}", e);
            tonic::Status::unavailable("content manager not ready")
        })?;

        let tenant_id = tenant_id.clone().unwrap_or("".into());
        let mut request = tonic::Request::new(GetBlobInfoRequest {
            tenant_id: tenant_id.to_string(),
            blob_name: blob_content_key.blob_name,
            transformation_key: blob_content_key.transformation_key,
            sub_key: blob_content_key.sub_key,
        });
        request_context.annotate(request.metadata_mut());

        let response = client.get_blob_info(request).await?;
        Ok(response.into_inner())
    }

    async fn batch_get_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blob_content_keys: Vec<BlobContentKey>,
    ) -> tonic::Result<BatchGetBlobInfoResponse> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {}", e);
            tonic::Status::unavailable("content manager not ready")
        })?;

        let tenant_id = tenant_id.clone().unwrap_or("".into()).to_string();
        let mut request = tonic::Request::new(BatchGetBlobInfoRequest {
            tenant_id,
            blob_content_keys,
        });
        request_context.annotate(request.metadata_mut());

        let response = client.batch_get_blob_info(request).await?;
        Ok(response.into_inner())
    }

    // Note that the returned channel may return an error if the deadline is reached. Handling it
    // is the caller's responsibility.
    async fn get_content(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blob_scope: &BlobScope,
        blob_names: &[BlobName],
        deadline: Option<Instant>,
    ) -> Receiver<tonic::Result<content_manager::BatchGetContentResponse>> {
        let mut client = match self.get_client().await {
            Ok(c) => c,
            Err(e) => {
                tracing::error!("content manager client not ready: {}", e);
                return receiver_for_error(tonic::Status::unavailable("content manager not ready"));
            }
        };
        let tenant_id = match tenant_id.as_ref() {
            Some(tenant_id) => tenant_id.to_string(),
            None => "".to_string(),
        };
        let mut request = tonic::Request::new(content_manager::BatchGetContentRequest {
            requests: blob_names
                .iter()
                .map(|blob_name| content_manager::GetContentRequest {
                    blob_name: blob_name.to_string(),
                    transformation_key: blob_scope.transformation_key.clone(),
                    sub_key: blob_scope.sub_key.clone(),
                    tenant_id: tenant_id.clone(),
                })
                .collect(),
            tenant_id: tenant_id.clone(),
        });
        if let Some(deadline) = deadline {
            // Pass the timeout along the gRPC call
            request.set_timeout(deadline - Instant::now());
        }
        request_context.annotate(request.metadata_mut());

        match client.batch_get_content(request).await {
            Ok(r) => receiver_for_stream(r, deadline),
            Err(e) => receiver_for_error(e),
        }
    }

    async fn get_content_multiple_scopes(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        blobs: Vec<(BlobName, BlobScope)>,
        deadline: Option<Instant>,
    ) -> Receiver<tonic::Result<content_manager::BatchGetContentResponse>> {
        let mut client = match self.get_client().await {
            Ok(c) => c,
            Err(e) => {
                tracing::error!("content manager client not ready: {}", e);
                return receiver_for_error(tonic::Status::unavailable("content manager not ready"));
            }
        };
        let tenant_id = match tenant_id.as_ref() {
            Some(tenant_id) => tenant_id.to_string(),
            None => "".to_string(),
        };
        let mut request = tonic::Request::new(content_manager::BatchGetContentRequest {
            requests: blobs
                .iter()
                .map(
                    |(blob_name, blob_scope)| content_manager::GetContentRequest {
                        blob_name: blob_name.to_string(),
                        transformation_key: blob_scope.transformation_key.clone(),
                        sub_key: blob_scope.sub_key.clone(),
                        tenant_id: tenant_id.clone(),
                    },
                )
                .collect(),
            tenant_id: tenant_id.clone(),
        });
        if let Some(deadline) = deadline {
            // Pass the timeout along the gRPC call
            request.set_timeout(deadline - Instant::now());
        }
        request_context.annotate(request.metadata_mut());

        match client.batch_get_content(request).await {
            Ok(r) => receiver_for_stream(r, deadline),
            Err(e) => receiver_for_error(e),
        }
    }

    async fn get_checkpoint_blob_names(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        checkpoint_id: &str,
    ) -> Receiver<tonic::Result<content_manager::GetAllBlobsFromCheckpointResponse>> {
        let mut client = match self.get_client().await {
            Ok(c) => c,
            Err(e) => {
                tracing::error!("content manager client not ready: {}", e);
                return receiver_for_error(tonic::Status::unavailable("content manager not ready"));
            }
        };
        let tenant_id = match tenant_id.as_ref() {
            Some(tenant_id) => tenant_id.to_string(),
            None => "".to_string(),
        };
        let mut request = tonic::Request::new(content_manager::GetAllBlobsFromCheckpointRequest {
            checkpoint_id: checkpoint_id.to_string(),
            tenant_id,
        });
        request_context.annotate(request.metadata_mut());

        match client.get_all_blobs_from_checkpoint(request).await {
            Ok(r) => receiver_for_stream(r, None),
            Err(e) => receiver_for_error(e),
        }
    }

    async fn get_best_ann_index(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        checkpoint_id: &str,
    ) -> tonic::Result<GetBestAnnIndexResult> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {:?}", e);
            tonic::Status::unavailable("client is not ready")
        })?;
        let mut request = tonic::Request::new(GetBestAnnIndexRequest {
            tenant_id: tenant_id.as_ref().map(|id| id.to_string()),
            transformation_key: transformation_key.to_string(),
            checkpoint_id: checkpoint_id.to_string(),
        });
        request_context.annotate(request.metadata_mut());

        let resp = client.get_best_ann_index(request).await?;
        let resp_inner = resp.into_inner();
        let added_blobs = resp_inner
            .added_blobs
            .iter()
            .map(|blob_bytes| BlobName::from_bytes(blob_bytes))
            .collect::<Result<Vec<_>, tonic::Status>>()?;
        let removed_blobs = resp_inner
            .removed_blobs
            .iter()
            .map(|blob_bytes| BlobName::from_bytes(blob_bytes))
            .collect::<Result<Vec<_>, tonic::Status>>()?;
        Ok(GetBestAnnIndexResult {
            index_id: resp_inner.index_id,
            added_blobs,
            removed_blobs,
        })
    }

    async fn get_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
    ) -> tonic::Result<GetAnnIndexBlobInfosResult> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {:?}", e);
            tonic::Status::unavailable("client is not ready")
        })?;
        let mut request = tonic::Request::new(GetAnnIndexBlobInfosRequest {
            key: Some(AnnIndexKey {
                tenant_id: tenant_id.as_ref().map(|id| id.to_string()),
                transformation_key: transformation_key.to_string(),
                index_id: index_id.to_string(),
            }),
        });
        request_context.annotate(request.metadata_mut());
        let mut stream = client.get_ann_index_blob_infos(request).await?.into_inner();
        let mut infos = Vec::new();
        while let Some(resp) = stream.message().await? {
            let mut chunk_infos = resp
                .blob_infos
                .into_iter()
                .map(|info| {
                    Ok(AnnIndexBlobInfoData {
                        blob_name: blob_names::BlobName::from_bytes(&info.blob_name)?,
                        chunk_count: info.chunk_count,
                    })
                })
                .collect::<tonic::Result<Vec<_>>>()?;
            infos.append(&mut chunk_infos);
        }
        Ok(GetAnnIndexBlobInfosResult { infos })
    }

    async fn get_ann_index_asset(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        sub_key: &str,
    ) -> tonic::Result<Vec<u8>> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {:?}", e);
            tonic::Status::unavailable("client is not ready")
        })?;
        let mut request = tonic::Request::new(GetAnnIndexAssetRequest {
            key: Some(AnnIndexAssetKey {
                tenant_id: tenant_id.as_ref().map(|id| id.to_string()),
                transformation_key: transformation_key.to_string(),
                index_id: index_id.to_string(),
                sub_key: sub_key.to_string(),
            }),
        });
        request_context.annotate(request.metadata_mut());
        let mut stream = client.get_ann_index_asset(request).await?.into_inner();
        let mut data = vec![];
        while let Some(mut resp) = stream.message().await? {
            data.append(&mut resp.data);
        }
        Ok(data)
    }

    async fn add_ann_index_mapping(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        checkpoint_id: &str,
        index_id: &str,
        added_blobs: &[BlobName],
        removed_blobs: &[BlobName],
    ) -> tonic::Result<AddAnnIndexMappingResponse> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {:?}", e);
            tonic::Status::unavailable("client is not ready")
        })?;

        let mut request = tonic::Request::new(AddAnnIndexMappingRequest {
            key: Some(AddAnnIndexMappingRequestKey {
                tenant_id: tenant_id.as_ref().map(|id| id.to_string()),
                transformation_key: transformation_key.to_string(),
                checkpoint_id: checkpoint_id.to_string(),
            }),
            index_id: index_id.to_string(),
            added_blobs: added_blobs
                .iter()
                .map(|blob_name| blob_name.as_bytes().to_vec())
                .collect(),
            removed_blobs: removed_blobs
                .iter()
                .map(|blob_name| blob_name.as_bytes().to_vec())
                .collect(),
        });
        request_context.annotate(request.metadata_mut());
        let resp = client.add_ann_index_mapping(request).await?;
        Ok(resp.into_inner())
    }

    async fn upload_ann_index_blob_infos(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        infos: &[AnnIndexBlobInfoData],
    ) -> tonic::Result<UploadAnnIndexBlobInfosResponse> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {:?}", e);
            tonic::Status::unavailable("client is not ready")
        })?;

        let message = self.prepare_upload_ann_index_blob_infos_messages(
            tenant_id,
            transformation_key,
            index_id,
            infos,
        );
        let mut request = tonic::Request::new(tokio_stream::iter(message));
        request_context.annotate(request.metadata_mut());
        let resp = client.upload_ann_index_blob_infos(request).await?;
        Ok(resp.into_inner())
    }

    async fn upload_ann_index_assets(
        &self,
        request_context: &RequestContext,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        assets: &[(String, Vec<u8>)],
    ) -> tonic::Result<UploadAnnIndexAssetsResponse> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("content manager client not ready: {:?}", e);
            tonic::Status::unavailable("client is not ready")
        })?;
        let messages = self.prepare_upload_ann_index_assets_messages(
            tenant_id,
            transformation_key,
            index_id,
            assets,
        );
        let mut request = tonic::Request::new(tokio_stream::iter(messages));
        request_context.annotate(request.metadata_mut());
        let resp = client.upload_ann_index_assets(request).await?;
        Ok(resp.into_inner())
    }
}

impl ContentManagerClientImpl {
    pub fn new(
        endpoint: &str,
        tls_config: Option<ClientTlsConfig>,
        request_timeout: Option<Duration>,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            request_timeout,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        content_manager::content_manager_client::ContentManagerClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    self.request_timeout,
                    &self.tls_config,
                )
                .await?;
                let client =
                    content_manager::content_manager_client::ContentManagerClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }

    fn prepare_upload_ann_index_blob_infos_messages(
        &self,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        infos: &[AnnIndexBlobInfoData],
    ) -> Vec<UploadAnnIndexBlobInfosRequest> {
        const MAX_ELTS: usize =
            MAX_ANN_INDEX_STREAMING_CHUNK_SIZE / (blob_names::BLOB_DIGEST_LEN + 4);
        infos
            .chunks(MAX_ELTS)
            .with_position()
            .map(|(pos, infos_chunk)| {
                let (key, last_message) = match pos {
                    Position::First | Position::Only => (
                        Some(AnnIndexKey {
                            tenant_id: tenant_id.as_ref().map(|id| id.to_string()),
                            transformation_key: transformation_key.to_string(),
                            index_id: index_id.to_string(),
                        }),
                        matches!(pos, Position::Only),
                    ),
                    Position::Last => (None, true),
                    Position::Middle => (None, false),
                };
                UploadAnnIndexBlobInfosRequest {
                    key,
                    last_message,
                    blob_infos: infos_chunk
                        .iter()
                        .map(|info| AnnIndexBlobInfo {
                            blob_name: info.blob_name.as_bytes().to_vec(),
                            chunk_count: info.chunk_count,
                        })
                        .collect_vec(),
                }
            })
            .collect_vec()
    }

    fn prepare_upload_ann_index_assets_messages(
        &self,
        tenant_id: &Option<TenantId>,
        transformation_key: &str,
        index_id: &str,
        assets: &[(String, Vec<u8>)],
    ) -> Vec<UploadAnnIndexAssetsRequest> {
        assets
            .iter()
            .flat_map(|(sub_key, data)| {
                data.chunks(MAX_ANN_INDEX_STREAMING_CHUNK_SIZE)
                    .with_position()
                    .map(|(pos, data_chunk)| {
                        let key = match pos {
                            Position::First | Position::Only => Some(AnnIndexAssetKey {
                                tenant_id: tenant_id.as_ref().map(|id| id.to_string()),
                                transformation_key: transformation_key.to_string(),
                                index_id: index_id.to_string(),
                                sub_key: sub_key.to_string(),
                            }),
                            _ => None,
                        };
                        (key, data_chunk)
                    })
            })
            .with_position()
            .map(|(pos, (key, data_chunk))| {
                let last_message = matches!(pos, Position::Only | Position::Last);
                UploadAnnIndexAssetsRequest {
                    key,
                    data: data_chunk.to_vec(),
                    last_message,
                }
            })
            .collect_vec()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_prepare_upload_ann_index_asset_messages() {
        // Arrange
        const LONG_ASSET_LENGTH: usize = 6000000;
        const LONG_ASSET_EXPECTED_CHUNKS: usize =
            LONG_ASSET_LENGTH.div_ceil(MAX_ANN_INDEX_STREAMING_CHUNK_SIZE);
        let client = ContentManagerClientImpl::new("", None, None);
        let assets = vec![
            (
                "asset1".into(),
                (0..LONG_ASSET_LENGTH).map(|_| 0_u8).collect_vec(),
            ),
            ("asset2".into(), vec![1_u8, 2_u8]),
        ];
        let tenant_id = String::from("test_tenant");
        let transformation_key = "test_tkey";
        let index_id = "test_index";
        // Act
        let requests = client.prepare_upload_ann_index_assets_messages(
            &Some(TenantId::from(tenant_id.to_owned())),
            transformation_key,
            index_id,
            assets.as_slice(),
        );
        // Assert
        assert_eq!(requests.len(), LONG_ASSET_EXPECTED_CHUNKS + 1);
        assert_eq!(
            requests[0].key,
            Some(AnnIndexAssetKey {
                tenant_id: Some(tenant_id.clone()),
                transformation_key: transformation_key.to_owned(),
                index_id: index_id.to_owned(),
                sub_key: "asset1".into()
            })
        );
        assert!(!requests[0].last_message);
        let asset1_continuation_chunks = &requests[1..LONG_ASSET_EXPECTED_CHUNKS];
        assert_eq!(
            asset1_continuation_chunks.len(),
            LONG_ASSET_EXPECTED_CHUNKS - 1
        );
        for req in asset1_continuation_chunks {
            assert!(req.key.is_none());
            assert!(!req.last_message)
        }
        assert_eq!(
            requests[LONG_ASSET_EXPECTED_CHUNKS].key,
            Some(AnnIndexAssetKey {
                tenant_id: Some(tenant_id.clone()),
                transformation_key: transformation_key.to_owned(),
                index_id: index_id.to_owned(),
                sub_key: "asset2".into()
            })
        );
        assert!(requests[LONG_ASSET_EXPECTED_CHUNKS].last_message);
    }

    #[test]
    fn test_prepare_upload_ann_index_blob_infos_messages() {
        // Arrange
        const BLOB_INFOS_LENGTH: usize = 1000000;
        const EXPECTED_CHUNKS: usize = (BLOB_INFOS_LENGTH * (blob_names::BLOB_DIGEST_LEN + 4))
            .div_ceil(MAX_ANN_INDEX_STREAMING_CHUNK_SIZE);
        let client = ContentManagerClientImpl::new("", None, None);
        let infos = (0..BLOB_INFOS_LENGTH)
            .map(|i| {
                let mut v = i.to_le_bytes().to_vec();
                v.resize(32, 0_u8);
                AnnIndexBlobInfoData {
                    blob_name: BlobName::from_bytes(v.as_slice()).unwrap(),
                    chunk_count: u32::try_from(i).unwrap(),
                }
            })
            .collect_vec();
        let tenant_id = String::from("test_tenant");
        let transformation_key = "test_tkey";
        let index_id = "test_index";
        // Act
        let requests = client.prepare_upload_ann_index_blob_infos_messages(
            &Some(TenantId::from(tenant_id.to_owned())),
            transformation_key,
            index_id,
            infos.as_slice(),
        );
        // Assert
        assert_eq!(requests.len(), EXPECTED_CHUNKS);
        assert_eq!(
            requests[0].key,
            Some(AnnIndexKey {
                tenant_id: Some(tenant_id),
                transformation_key: transformation_key.to_owned(),
                index_id: index_id.to_owned()
            })
        );
        assert!(!requests[0].last_message);
        assert!(requests[EXPECTED_CHUNKS - 1].last_message);
        requests[1..(EXPECTED_CHUNKS - 1)]
            .iter()
            .for_each(|req| assert!(!req.last_message));
    }
}

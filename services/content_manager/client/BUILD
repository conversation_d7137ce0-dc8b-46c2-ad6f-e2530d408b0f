load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "client",
    srcs = [
        "content_manager_client.py",
    ],
    visibility = [
        "//experimental:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/python/grpc:client_options",
        "//services/content_manager:content_manager_py_proto",
        "//services/lib/request_context:request_context_py",
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("protobuf"),
        requirement("prometheus_client"),
    ],
)

pytest_test(
    name = "client_test",
    size = "small",
    srcs = [
        "content_manager_client_test.py",
    ],
    deps = [
        ":client",
    ],
)

py_library(
    name = "workspace_manager",
    srcs = [
        "workspace_manager.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names/python:blob_names",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        requirement("opentelemetry-api"),
        requirement("structlog"),
    ],
)

pytest_test(
    name = "workspace_manager_test",
    size = "small",
    srcs = [
        "workspace_manager_test.py",
    ],
    deps = [
        ":workspace_manager",
    ],
)

py_library(
    name = "content_cache",
    srcs = [
        "content_cache.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":client",
        "//base/caching:cache",
        "//base/caching:lru_cache",
        "//services/lib/grpc/auth:service_auth",
    ],
)

pytest_test(
    name = "content_cache_test",
    size = "small",
    srcs = [
        "content_cache_test.py",
    ],
    deps = [
        ":content_cache",
        requirement("structlog"),
    ],
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/content_manager/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//services/content_manager:content_manager_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["content_manager_client.rs"],
    aliases = aliases(),
    crate_name = "content_manager_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//base/blob_names:blob_names_rs_proto",
        "//base/blob_names/rust:blob_names",
        "//base/rust/tracing-tonic",
        "//services/content_manager:content_manager_rs_proto",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

rust_test(
    name = "content_manager_client_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":client_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

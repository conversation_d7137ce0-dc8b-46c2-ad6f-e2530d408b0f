package client

// Package client provides a client for the content manager service.

import (
	"bytes"
	"context"
	"errors"
	"io"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_pb "github.com/augmentcode/augment/base/blob_names/proto"
	pb "github.com/augmentcode/augment/services/content_manager/proto"
)

// maximum size in bytes of a a chunk of data sent over the streaming ANN request
// endpoints.
const (
	maxAnnIndexChunkSize = 2 * 1024 * 1024
	blobNameSize         = 32
)

type BatchDownloadContentResult struct {
	// the response
	// nil if there was an error
	Resp *pb.BatchGetContentResponse

	// the error
	// nil if there was no error
	Err error
}

type AnnIndexKey struct {
	TenantId          *string
	TransformationKey string
	IndexId           string
}

type GetBestAnnIndexResult struct {
	IndexId      string
	AddedBlobs   []blob_names.BlobName
	RemovedBlobs []blob_names.BlobName
}

type AnnIndexBlobInfo struct {
	BlobName blob_names.BlobName
	// The number of (embedded) chunks
	ChunkCount uint32
}

type GetAnnIndexBlobInfosResult struct {
	Err   error
	Infos []AnnIndexBlobInfo
}

type AnnIndexAssetInput struct {
	Subkey string
	Data   []byte
}

type ContentManagerClient interface {
	// This should be called to cleanup resources for this client
	Close()

	UploadBlobContent(ctx context.Context, content []byte, path string, requestContext *requestcontext.RequestContext) (string, bool, error)

	BatchUploadBlobContent(ctx context.Context,
		blobs []*pb.UploadBlobContent,
		priority pb.IndexingPriority,
		requestContext *requestcontext.RequestContext) ([]*pb.UploadBlobResult, error)

	FindMissingBlobs(ctx context.Context, blobNames []blob_names.BlobName, transformationKey string, subKey string, requestContext *requestcontext.RequestContext) ([]string, error)

	CheckpointBlobs(ctx context.Context, blobs *blobs_pb.Blobs, requestContext *requestcontext.RequestContext) (string, error)

	GetAllBlobsFromCheckpoint(ctx context.Context, checkpointID string, requestContext *requestcontext.RequestContext) ([]blob_names.BlobName, error)

	GetBlobInfo(ctx context.Context, blobName blob_names.BlobName, requestContext *requestcontext.RequestContext) (*pb.GetBlobInfoResponse, error)
	BatchGetBlobInfo(
		ctx context.Context,
		blobNames []blob_names.BlobName,
		tenantID string,
		requestContext *requestcontext.RequestContext,
	) (*pb.BatchGetBlobInfoResponse, error)

	BatchDownloadContent(
		ctx context.Context,
		keys []*pb.BlobContentKey,
		requestContext *requestcontext.RequestContext,
		tenantID string,
	) (<-chan BatchDownloadContentResult, error)

	GetBestAnnIndex(
		ctx context.Context,
		tenantId *string,
		transformationKey string,
		checkpointId string,
		requestContext *requestcontext.RequestContext,
	) (*GetBestAnnIndexResult, error)

	GetAnnIndexBlobInfos(
		ctx context.Context,
		key AnnIndexKey,
		requestContext requestcontext.RequestContext,
	) (*GetAnnIndexBlobInfosResult, error)

	GetAnnIndexAsset(
		ctx context.Context,
		tenantId *string,
		transformationKey string,
		indexId string,
		subKey string,
		requestContext requestcontext.RequestContext,
	) ([]byte, error)

	AddAnnIndexMapping(
		ctx context.Context,
		tenantId *string,
		transformationKey string,
		checkpointId string,
		indexId string,
		addedBlobs []blob_names.BlobName,
		removedBlobs []blob_names.BlobName,
		requestContext requestcontext.RequestContext,
	) (*pb.AddAnnIndexMappingResponse, error)

	UploadAnnIndexBlobInfos(
		ctx context.Context,
		tenantId *string,
		transformationKey string,
		indexId string,
		infos []AnnIndexBlobInfo,
		requestContext requestcontext.RequestContext,
	) (*pb.UploadAnnIndexBlobInfosResponse, error)

	UploadAnnIndexAssets(
		ctx context.Context,
		tenantId *string,
		transformationKey string,
		indexId string,
		assets []AnnIndexAssetInput,
		requestContext requestcontext.RequestContext,
	) (*pb.UploadAnnIndexAssetsResponse, error)
}

func mapSlice[T any, R any](s []T, f func(T) R) []R {
	out := make([]R, len(s))
	for i, elt := range s {
		out[i] = f(elt)
	}
	return out
}

type ContentManagerClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	client pb.ContentManagerClient
}

// NewContentManagerClient creates a new ContentManagerClient.
//
// endpoint: The endpoint of the route guide service.
// credentials: The credentials to use for the channel (optional)
//
// Returns: The client stub for the route guide or an error if the client could not be created.
func NewContentManagerClient(endpoint string, credentials credentials.TransportCredentials) (ContentManagerClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := pb.NewContentManagerClient(conn)
	return &ContentManagerClientImpl{conn: conn, client: client}, nil
}

func (c *ContentManagerClientImpl) FindMissingBlobs(ctx context.Context, blobNames []blob_names.BlobName, transformationKey string, subKey string, requestContext *requestcontext.RequestContext) ([]string, error) {
	var blobNameStr []string
	for _, blobName := range blobNames {
		blobNameStr = append(blobNameStr, string(blobName))
	}
	req := &pb.FindMissingBlobRequest{BlobNames: blobNameStr, TransformationKey: transformationKey, SubKey: subKey}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.FindMissingBlobs(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.UnknownBlobNames, nil
}

func (c *ContentManagerClientImpl) UploadBlobContent(ctx context.Context, content []byte, path string, requestContext *requestcontext.RequestContext) (string, bool, error) {
	req := &pb.UploadBlobContentRequest{Content: content}
	req.Metadata = []*pb.BlobMetadata{{Key: "path", Value: path}}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.UploadBlobContent(ctx, req)
	if err != nil {
		return "", false, err
	}
	return resp.BlobName, resp.Existed, nil
}

func (c *ContentManagerClientImpl) BatchUploadBlobContent(ctx context.Context, blobs []*pb.UploadBlobContent, priority pb.IndexingPriority, requestContext *requestcontext.RequestContext) ([]*pb.UploadBlobResult, error) {
	req := &pb.BatchUploadBlobContentRequest{Entries: blobs, Priority: priority}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.BatchUploadBlobContent(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Results, nil
}

func (c *ContentManagerClientImpl) CheckpointBlobs(ctx context.Context, blobs *blobs_pb.Blobs, requestContext *requestcontext.RequestContext) (string, error) {
	req := &pb.CheckpointBlobsRequest{Blobs: blobs}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.CheckpointBlobs(ctx, req)
	if err != nil {
		return "", err
	}
	return resp.CheckpointId, nil
}

func (c *ContentManagerClientImpl) BatchDownloadContent(
	ctx context.Context,
	keys []*pb.BlobContentKey,
	requestContext *requestcontext.RequestContext,
	tenantID string,
) (<-chan BatchDownloadContentResult, error) {
	reqs := make([]*pb.GetContentRequest, len(keys))
	for i, key := range keys {
		req := &pb.GetContentRequest{
			BlobName:          key.BlobName,
			TransformationKey: key.TransformationKey,
			SubKey:            key.SubKey,
		}
		if tenantID != "" {
			req.TenantId = tenantID
		}
		reqs[i] = req
	}

	downloadRequest := &pb.BatchGetContentRequest{
		Requests: reqs,
	}
	if tenantID != "" {
		downloadRequest.TenantId = tenantID
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	stream, err := c.client.BatchGetContent(ctx, downloadRequest)
	if err != nil {
		return nil, err
	}

	ch := make(chan BatchDownloadContentResult)
	go func() {
		defer close(ch)
		for {
			r, err := stream.Recv()
			if err != nil {
				if !errors.Is(err, io.EOF) {
					ch <- BatchDownloadContentResult{Err: err}
				}
				return
			}
			ch <- BatchDownloadContentResult{Resp: r}
		}
	}()

	return ch, nil
}

func (c *ContentManagerClientImpl) GetAllBlobsFromCheckpoint(ctx context.Context, checkpointID string, requestContext *requestcontext.RequestContext) ([]blob_names.BlobName, error) {
	if checkpointID == "" {
		return nil, nil
	}
	req := &pb.GetAllBlobsFromCheckpointRequest{CheckpointId: checkpointID}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.GetAllBlobsFromCheckpoint(ctx, req)
	if err != nil {
		return nil, err
	}

	var blobNames []blob_names.BlobName
	for {
		resp, err := stream.Recv()
		if err != nil {
			if !errors.Is(err, io.EOF) {
				return nil, err
			}
			return blobNames, nil
		}
		for _, blobName := range resp.BlobNames {
			blobNames = append(blobNames, blob_names.NewBlobNameFromBytes(blobName))
		}
	}
}

func (c *ContentManagerClientImpl) GetBlobInfo(ctx context.Context, blobName blob_names.BlobName, requestContext *requestcontext.RequestContext) (*pb.GetBlobInfoResponse, error) {
	req := &pb.GetBlobInfoRequest{BlobName: string(blobName)}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetBlobInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *ContentManagerClientImpl) BatchGetBlobInfo(
	ctx context.Context,
	blobNames []blob_names.BlobName,
	tenantID string,
	requestContext *requestcontext.RequestContext,
) (*pb.BatchGetBlobInfoResponse, error) {
	blobContentKeys := make([]*pb.BlobContentKey, len(blobNames))
	for _, blobName := range blobNames {
		blobContentKeys = append(blobContentKeys, &pb.BlobContentKey{
			BlobName: string(blobName),
		})
	}
	req := &pb.BatchGetBlobInfoRequest{
		BlobContentKeys: blobContentKeys,
		TenantId:        tenantID,
	}
	outCtx := metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.BatchGetBlobInfo(outCtx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *ContentManagerClientImpl) GetBestAnnIndex(
	ctx context.Context,
	tenantId *string,
	transformationKey string,
	checkpointId string,
	requestContext *requestcontext.RequestContext,
) (*GetBestAnnIndexResult, error) {
	req := &pb.GetBestAnnIndexRequest{
		TenantId:          tenantId,
		TransformationKey: transformationKey,
		CheckpointId:      checkpointId,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetBestAnnIndex(ctx, req)
	if err != nil {
		return nil, err
	}
	return &GetBestAnnIndexResult{
		IndexId: resp.IndexId,
		AddedBlobs: mapSlice(resp.AddedBlobs, func(s []byte) blob_names.BlobName {
			return blob_names.NewBlobNameFromBytes(s)
		}),
		RemovedBlobs: mapSlice(resp.RemovedBlobs, func(s []byte) blob_names.BlobName {
			return blob_names.NewBlobNameFromBytes(s)
		}),
	}, nil
}

func (c *ContentManagerClientImpl) GetAnnIndexBlobInfos(
	ctx context.Context,
	key AnnIndexKey,
	requestContext requestcontext.RequestContext,
) (*GetAnnIndexBlobInfosResult, error) {
	requestKey := &pb.AnnIndexKey{
		TenantId:          key.TenantId,
		TransformationKey: key.TransformationKey,
		IndexId:           key.IndexId,
	}
	req := &pb.GetAnnIndexBlobInfosRequest{
		Key: requestKey,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.GetAnnIndexBlobInfos(ctx, req)
	if err != nil {
		return nil, err
	}

	infos := make([]AnnIndexBlobInfo, 0)
	for {
		resp, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}
		infos = append(infos, mapSlice(resp.BlobInfos, func(e *pb.AnnIndexBlobInfo) AnnIndexBlobInfo {
			return AnnIndexBlobInfo{
				BlobName:   blob_names.NewBlobNameFromBytes(e.BlobName),
				ChunkCount: e.ChunkCount,
			}
		})...)
	}
	return &GetAnnIndexBlobInfosResult{
		Infos: infos,
	}, nil
}

func (c *ContentManagerClientImpl) GetAnnIndexAsset(
	ctx context.Context,
	tenantId *string,
	transformationKey string,
	indexId string,
	subKey string,
	requestContext requestcontext.RequestContext,
) ([]byte, error) {
	req := &pb.GetAnnIndexAssetRequest{
		Key: &pb.AnnIndexAssetKey{
			TenantId:          tenantId,
			TransformationKey: transformationKey,
			IndexId:           indexId,
			SubKey:            subKey,
		},
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.GetAnnIndexAsset(ctx, req)
	if err != nil {
		return nil, err
	}
	var asset []byte
	for {
		resp, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}
		asset = append(asset, resp.Data...)
	}
	return asset, nil
}

func (c *ContentManagerClientImpl) AddAnnIndexMapping(
	ctx context.Context,
	tenantId *string,
	transformationKey string,
	checkpointId string,
	indexId string,
	addedBlobs []blob_names.BlobName,
	removedBlobs []blob_names.BlobName,
	requestContext requestcontext.RequestContext,
) (*pb.AddAnnIndexMappingResponse, error) {
	translateBlobSlice := func(blobSlice []blob_names.BlobName) ([][]byte, error) {
		translatedBlobs := make([][]byte, len(blobSlice))
		for i, name := range blobSlice {
			blobNameHex, err := blob_names.DecodeHexBlobName(name)
			if err != nil {
				return nil, err
			}
			translatedBlobs[i] = blobNameHex
		}
		return translatedBlobs, nil
	}

	addedBlobBytes, err := translateBlobSlice(addedBlobs)
	if err != nil {
		return nil, err
	}
	removedBlobBytes, err := translateBlobSlice(removedBlobs)
	if err != nil {
		return nil, err
	}
	req := &pb.AddAnnIndexMappingRequest{
		Key: &pb.AddAnnIndexMappingRequestKey{
			TenantId:          tenantId,
			TransformationKey: transformationKey,
			CheckpointId:      checkpointId,
		},
		IndexId:      indexId,
		AddedBlobs:   addedBlobBytes,
		RemovedBlobs: removedBlobBytes,
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.AddAnnIndexMapping(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (c *ContentManagerClientImpl) UploadAnnIndexBlobInfos(
	ctx context.Context,
	tenantId *string,
	transformationKey string,
	indexId string,
	infos []AnnIndexBlobInfo,
	requestContext requestcontext.RequestContext,
) (*pb.UploadAnnIndexBlobInfosResponse, error) {
	const maxEmbeddings = maxAnnIndexChunkSize / (blobNameSize + 4)
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.UploadAnnIndexBlobInfos(ctx)
	if err != nil {
		return nil, err
	}

	first := true
	for len(infos) != 0 {
		bound := min(len(infos), maxEmbeddings)
		req := &pb.UploadAnnIndexBlobInfosRequest{
			BlobInfos: mapSlice(infos[:bound], func(e AnnIndexBlobInfo) *pb.AnnIndexBlobInfo {
				name, err := blob_names.DecodeHexBlobName(e.BlobName)
				if err != nil {
					return nil
				}
				return &pb.AnnIndexBlobInfo{
					BlobName:   name,
					ChunkCount: e.ChunkCount,
				}
			}),
		}
		if first {
			first = false
			req.Key = &pb.AnnIndexKey{
				TenantId:          tenantId,
				TransformationKey: transformationKey,
				IndexId:           indexId,
			}
		}
		infos = infos[bound:]
		if len(infos) == 0 {
			req.LastMessage = true
		}
		if err = stream.Send(req); err != nil {
			return nil, err
		}
	}

	resp, err := stream.CloseAndRecv()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *ContentManagerClientImpl) UploadAnnIndexAssets(
	ctx context.Context,
	tenantId *string,
	transformationKey string,
	indexId string,
	assets []AnnIndexAssetInput,
	requestContext requestcontext.RequestContext,
) (*pb.UploadAnnIndexAssetsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.UploadAnnIndexAssets(ctx)
	if err != nil {
		return nil, err
	}

	first := true
	for i, asset := range assets {
		buf := bytes.NewBuffer(asset.Data)
		for buf.Len() != 0 {
			chunk := make([]byte, maxAnnIndexChunkSize)
			subkey := &asset.Subkey
			if buf.Len() != len(asset.Data) {
				subkey = nil
			}
			_, err := buf.Read(chunk)
			if err != nil {
				return nil, err
			}

			req := &pb.UploadAnnIndexAssetsRequest{
				Data:        chunk,
				LastMessage: i == len(assets)-1 && buf.Len() == 0,
			}
			if first {
				first = false
				req.Key = &pb.AnnIndexAssetKey{
					TenantId:          tenantId,
					TransformationKey: transformationKey,
					IndexId:           indexId,
					SubKey:            *subkey,
				}
			}

			err = stream.Send(req)
			if err != nil {
				return nil, err
			}
		}
	}
	resp, err := stream.CloseAndRecv()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *ContentManagerClientImpl) Close() {
	c.conn.Close()
}

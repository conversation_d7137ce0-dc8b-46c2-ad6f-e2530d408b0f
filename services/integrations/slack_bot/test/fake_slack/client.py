"""Client to acess the fake slack service."""

from typing import Any
import json
import requests


class FakeSlackClient:
    """Client to acess the fake slack service."""

    def __init__(self, url: str):
        self.url = url

    def get(self, timeout: int) -> list[Any]:
        """Get all messages."""
        response = requests.get(f"{self.url}/all", timeout=timeout)
        response.raise_for_status()
        d = response.json()
        if not d:
            return []
        return d

    def clear(self):
        """Clear all messages."""
        response = requests.post(f"{self.url}/clear", timeout=10)
        response.raise_for_status()

    def next_response(self, action: str, response: Any):
        """Set the next response for a given action."""
        r = requests.post(
            f"{self.url}/next_response",
            json={"action": action, "response": json.dumps(response)},
            timeout=10,
        )
        r.raise_for_status()

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  assert env == 'DEV' : 'only deployable in dev';

  local appName = 'fake-slack';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: appName + '-svc',
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      ports: [
        {
          name: 'http',
          port: 80,
          targetPort: 8080,
        },
      ],
      selector: {
        app: appName,
      },
    },
  };
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=false);
  local container =
    {
      name: appName,
      target: {
        name: '//services/integrations/slack_bot/test/fake_slack:image',
        dst: appName,
      },
      env: lib.flatten([
        telemetryLib.telemetryEnv(appName, telemetryLib.collector),
      ]),
      ports: [
        {
          containerPort: 8080,
          name: 'http-svc',
        },
      ],
      resources: {
        limits: {
          cpu: 0.2,
          memory: '512Mi',
        },
      },
    };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    service,
    deployment,
    serviceAccount.objects,
  ])

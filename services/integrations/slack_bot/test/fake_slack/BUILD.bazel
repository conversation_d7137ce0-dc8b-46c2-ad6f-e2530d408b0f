load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_library")

go_library(
    name = "fake_slack_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/test/fake_slack",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "@com_github_rs_zerolog//log",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
    ],
)

go_binary(
    name = "fake_slack",
    embed = [":fake_slack_lib"],
    visibility = ["//services/integrations:__subpackages__"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":fake_slack",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
    ],
    data = [
        ":image",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("requests"),
    ],
)

py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        ":client_py",
        "//base/cloud/k8s:kubectl",
        "//base/cloud/k8s:kubectl_factory",
        "//base/logging:console_logging",
    ],
)

"""A small tool to run the fake slack service."""

import argparse
import contextlib
import pathlib
import typing
import logging

from base.cloud.k8s.kubectl import get_dev_namespace
from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.logging.console_logging import setup_console_logging
from services.integrations.slack_bot.test.fake_slack.client import FakeSlackClient


def do_get(client: FakeSlackClient, args):
    """Get all actions."""
    actions = client.get(args.timeout)
    logging.info(actions)


@contextlib.contextmanager
def setup(
    args: argparse.Namespace,
) -> typing.Generator[FakeSlackClient, None, None]:
    """Setup the client."""
    if args.url:
        yield FakeSlackClient(url=args.url)
    else:
        kubectl_factory = create_kubectl_factory(args.kube_config_file)
        kubectl_instance = kubectl_factory("GCP_US_CENTRAL1_DEV")
        with kubectl_instance.port_forward(
            args.namespace,
            "deployment/fake-slack",
            remote_port=8080,
            local_port=8080,
        ) as local_port:
            url = f"http://localhost:{local_port}"
            yield FakeSlackClient(url)


def main():
    """Main function."""
    parser = argparse.ArgumentParser()
    # this assume you have a port-forward to the fake feature flags service
    parser.add_argument("--url", default="")
    parser.add_argument(
        "--kube-config-file",
        help="Kubeconfig to use",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube/config"),
    )
    parser.add_argument("--namespace", default=get_dev_namespace())

    subparsers = parser.add_subparsers()
    get_parser = subparsers.add_parser("get")
    get_parser.set_defaults(action=do_get)
    args = parser.parse_args()

    setup_console_logging()

    with setup(args) as client:
        args.action(client, args)


if __name__ == "__main__":
    main()

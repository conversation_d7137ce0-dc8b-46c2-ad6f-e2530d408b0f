load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/bigtable_proxy/server:kubecfg_central",
        "//services/chat_host/test/fake_chat:kubecfg",
        "//services/deploy:base_kubecfg",
        "//services/grpc_debug/server:kubecfg",
        "//services/integrations/github/state/server:kubecfg",
        "//services/integrations/slack_bot/processor/server:kubecfg",
        "//services/integrations/slack_bot/test/fake_slack:kubecfg",
        "//services/integrations/slack_bot/webhook:kubecfg",
        "//services/integrations/slack_bot/webhook:kubecfg_pubsub",
        "//services/settings/server:kubecfg",
    ],
)

pytest_test(
    name = "slack_bot_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "slack_bot_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/python/grpc:health_check",
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/content_manager/client",
        "//services/grpc_debug:grpc_debug_py_proto",
        "//services/grpc_debug/client:client_py",
        "//services/integrations/github/state:github_state_py_proto",
        "//services/integrations/github/state/client:client_py",
        "//services/integrations/slack_bot:slack_event_py_proto",
        "//services/integrations/slack_bot/test/fake_slack:client_py",
        "//services/integrations/slack_bot/webhook:slack_webhook_py_proto",
        "//services/lib/request_context:request_context_py",
        "//services/settings:settings_py_proto",
        "//services/tenant_watcher/client",
        "//services/test/fake_feature_flags:client_py",
        "//services/token_exchange/client:client_py",
        requirement("kubernetes"),
    ],
)

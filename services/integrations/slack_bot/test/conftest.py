"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import json
from pathlib import Path
import time
from typing import Generator, Optional, Tuple

import grpc
import kubernetes
import pytest

from services.test.fake_feature_flags.client import FakeFeatureFlagsClient
from services.integrations.slack_bot.test.fake_slack.client import FakeSlackClient
from services.lib.request_context.request_context import (
    RequestContext,
    create_request_session_id,
)
from base.python.grpc.health_check import HealthChecker
import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
from services.grpc_debug.client.client import GrpcDebugClient, ProxyClient, ProxyMethod
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)
from services.integrations.github.state import github_state_pb2_grpc
from services.integrations.github.state.client.client import (
    setup_stub as setup_state_stub,
)
from services.tenant_watcher.client.client import TenantsClient
from services.integrations.slack_bot.webhook import (
    slack_webhook_pb2_grpc,
    slack_webhook_pb2,
)
from services.settings import settings_pb2_grpc, settings_pb2


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--skip-deployment-teardown",
        action="store_true",
        help="skip deploy and delete of the models",
        default=not k8s_test_helper.is_running_in_test_infra(),
    )
    if not k8s_test_helper.is_running_in_test_infra():
        parser.addoption(
            "--teardown-deployment",
            action="store_true",
            help="tear down the complete deployment after the run",
            default=False,
        )
    parser.addoption(
        "--skip-deployment-check",
        action="store_true",
        help="skip checking if the deployments are settled",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


@pytest.fixture(scope="session")
def application_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys the application as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    skip_deploy_check = request.config.getoption("--skip-deployment-check")
    skip_deploy_check_teardown = request.config.getoption(
        "--skip-deployment-teardown"
    ) and not request.config.getoption("--teardown-deployment")
    cloud = request.config.getoption("--cloud")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        skip_deploy_check=skip_deploy_check,
        skip_deploy_check_teardown=skip_deploy_check_teardown,
        cloud=cloud,
        kubecfg_binaries=[
            Path("services/integrations/slack_bot/test/test_kubecfg.sh"),
        ],
    ) as deploy_info:
        if not skip_deploy:
            time.sleep(120)
        yield deploy_info


NEXT_FORWARDED_PORT = 50052


def get_next_forwarded_port() -> int:
    """Return a fresh local port."""
    global NEXT_FORWARDED_PORT  # pylint: disable=global-statement
    port = NEXT_FORWARDED_PORT
    NEXT_FORWARDED_PORT += 1
    return port


def _get_pod(
    api_client: kubernetes.client.ApiClient | None, namespace: str, deployment_name: str
) -> Optional[str]:
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_name):
            return name


def _test_response(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


@pytest.fixture(scope="session")
def fake_slack_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[FakeSlackClient, None, None]:
    with application_deploy.kubectl.port_forward(
        "deployment/fake-slack", 8080, get_next_forwarded_port()
    ) as port:
        url = f"http://localhost:{port}"
        yield FakeSlackClient(url)


@pytest.fixture(scope="session")
def grpc_debug(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[GrpcDebugClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    pod_name = _get_pod(
        application_deploy.kubectl.api_client,
        application_deploy.namespace,
        "grpc-debug",
    )
    assert pod_name, "Failed to find pod"
    print("Using pod", pod_name, flush=True)

    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "grpc-debug-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            pod_name, 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield GrpcDebugClient.create_for_endpoint(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcDebugClient.create_for_endpoint(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def feature_flags_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[FakeFeatureFlagsClient, None, None]:
    """Return a client for the fake feature flags service."""
    pod_name = _get_pod(
        application_deploy.kubectl.api_client,
        application_deploy.namespace,
        "fake-feature-flags",
    )
    assert pod_name, "Failed to find pod"
    print("Using pod", pod_name, flush=True)

    with application_deploy.kubectl.port_forward(
        pod_name, 8080, get_next_forwarded_port()
    ) as port:
        url = f"http://localhost:{port}"
        yield FakeFeatureFlagsClient(
            url,
        )


@pytest.fixture(autouse=True)
def feature_flag_clear(feature_flags_client: FakeFeatureFlagsClient):
    """Clear all feature flags before each test."""
    feature_flags_client.clear()
    feature_flags_client.update("chat_raw_output_model", json.dumps("fake"), wait=1)


@pytest.fixture(scope="session")
def slack_webhook_client(
    grpc_debug: GrpcDebugClient,
) -> slack_webhook_pb2_grpc.SlackBotWebhookStub:
    """Fixture to start the slack webhook client."""
    return ProxyClient(
        grpc_debug,
        "https://slack-bot-webhook-grpc-svc:50051",
        [
            ProxyMethod(
                "slack_webhook.SlackBotWebhook",
                "EmitEvent",
                slack_webhook_pb2.EmitEventResponse,
            ),
        ],
    )


@pytest.fixture(autouse=True)
def fake_slack_client_clear(fake_slack_client: FakeSlackClient):
    """Clear all feature flags before each test."""
    fake_slack_client.clear()


@pytest.fixture(scope="session")
def settings_client(
    grpc_debug: GrpcDebugClient,
) -> settings_pb2_grpc.SettingsStub:
    """Fixture to start the settings client."""
    return ProxyClient(
        grpc_debug,
        "https://settings-svc:50051",
        [
            ProxyMethod(
                "settings.Settings",
                "GetTenantSettings",
                settings_pb2.GetTenantSettingsResponse,
            ),
            ProxyMethod(
                "settings.Settings",
                "UpdateTenantSettings",
                settings_pb2.UpdateTenantSettingsResponse,
            ),
        ],
    )


@pytest.fixture(scope="session")
def token_exchange_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TokenExchangeClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    pod_name = _get_pod(
        application_deploy.kubectl.api_client,
        application_deploy.namespace,
        "token-exchange-central",
    )
    assert pod_name, "Failed to find pod"
    print("Using pod", pod_name, flush=True)

    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "token-exchange-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            pod_name, 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(2)
                continue
            else:
                yield GrpcTokenExchangeClient(
                    url,
                    credentials=credentials,
                    options=options,
                    namespace=application_deploy.namespace,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcTokenExchangeClient(
            url,
            credentials=credentials,
            options=options,
            namespace=application_deploy.namespace,
        )


@pytest.fixture(scope="session")
def github_state_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[github_state_pb2_grpc.GithubStateStub, None, None]:
    """Return an GRPC stub to access the github state.

    If applicable, it will update or create the application in Kubernetes.
    """
    pod_name = _get_pod(
        application_deploy.kubectl.api_client,
        application_deploy.namespace,
        "github-state",
    )
    assert pod_name, "Failed to find pod"
    print("Using pod", pod_name, flush=True)

    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "github-state-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            pod_name, 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield setup_state_stub(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield setup_state_stub(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def slack_bot_tenant(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[Tuple[str, str], None, None]:
    """Return a tenant for the slack bot test."""
    with k8s_test_helper.new_tenant(application_deploy, "slack-bot-test") as tenant_id:
        yield ("slack-bot-test", tenant_id)


@pytest.fixture(scope="session")
def request_session_id() -> Generator[str, None, None]:
    """Return a request context."""
    yield create_request_session_id()


@pytest.fixture()
def request_context(
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    slack_bot_tenant,
) -> Generator[RequestContext | None, None, None]:
    """Return a request context."""
    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=slack_bot_tenant[1],
        scopes=[token_exchange_pb2.CONTENT_RW, token_exchange_pb2.SETTINGS_RW],
    )
    yield RequestContext.create_for_session(request_session_id, auth_token=token)


@pytest.fixture(scope="session")
def tenant_watcher_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TenantsClient, None, None]:
    """Return an GRPC stub to access the Tenant Watcher.

    If applicable, it will update or create the application in Kubernetes.
    """
    pod_name = _get_pod(
        application_deploy.kubectl.api_client,
        application_deploy.namespace,
        "tenant-central",
    )
    assert pod_name, "Failed to find pod"
    print("Using pod", pod_name, flush=True)

    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "tenant-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            pod_name, 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(2)
                continue
            else:
                yield TenantsClient(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield TenantsClient(
            url,
            credentials=credentials,
            options=options,
        )

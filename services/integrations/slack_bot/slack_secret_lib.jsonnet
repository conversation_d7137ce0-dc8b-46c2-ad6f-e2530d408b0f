function(env, namespace, cloud, appName, secretsOverride)
  local useSecretsOverride = env == 'DEV' && secretsOverride != {};
  assert !useSecretsOverride || (
    std.objectHas(secretsOverride, 'signing_secret') &&
    std.objectHas(secretsOverride, 'basic_creds')
  ) : 'Slack secrets override is malformed';

  local data = {
    GCP_US_CENTRAL1_DEV: {
      DEV: {
        signing_secret: 'AgBASCaODY5f7ySkhc7xqnzTSO8jMx9cD/pnrolO6sru8I6jRiq6kDwtoOKibzBQ9nYzX67pjY0qlpdNp7V4TUocamFsS80acXQhYBXXdPBkEfw5w8Xdzrxa0fwg4RovMDHearXNR/m7Z/9xBEVz1HCJ66a1/Wbvku/qXEEASEALEvDvRLhqcg6XjZRWMUk5Wx7K44rdYkEOE9OD2K0MUrItggnfYBNoT+KgmFtoFAX9YmWJ0vC328rOg7sWbHbQ3I2woKJFffKjl+88OkJWYRRmyd0o5CfnQgRZZ6W7gd36gZGbYthmVnSzLndWfgIEoPuRmhUbgOrA9Kbgk5tmrD4jQ0j/zL6I+oiRDrJaK7cWQ4u0K0+wwGpPgFb7BxuzGkLIvC0/WU39l1oOhLVeEn/HiqENIuBSn//rzmQb5f0A+35iHAMbwkX3MtJXGWRbANyJnIkbg/iD/jqJXZQcmaeroyRd8qzNkCZVV9Pxb81xwuJjQxnYIemFIjGdhJ1zQzbBKUuWZrgIWRDWOzDE0MlY379aWBqRnLXCGmJjaaMzR/J7aV4DYhSl/dsaIdyCC1/ntblal6+TknZjoJFcs84w8MGdSjs5AzTrhmY5Zg6OBhbU4JaR2kKnCkrvCmjnzmbKgMmboh6zN1kmOHxQqL7jlWJ2tdvuycmabkiUi8yIM+At11fP9m+CZPhDr8iRltjsd/MG/XIB1YwnW8oUN87VUjMMNwzlUNTG8FWs3tQFjA==',  // pragma: allowlist secret
        basic_creds: 'AgBrydTXbQf0hxwaAiWhhBXai2qt4wBDbq++iFgkr4949q7xMrR9e6vH9UZrkbaYb7tZbWgg4OqrAP1UzSlxTTzFKqNxFMCn5kQc5/n90xNHGiTtSZA7CmienFYwGPrI3cYwdxM0pwry+cjC6M0PzPjTritw5j0dThj2XlNoQTJJF+o62i6mdogn0ksm38YoyXPVsnesi2xtxamOHhmzP+cjqETf8bEwHYVI1qZDB4+hFM+uDEMlK5SclvRmwVZPh57YMrj3F8UYntwMZLPsjTIpoO/QT6Zo8wMklmdCwhlvGVc2oabeMwePhbLow4sst5ttsn1TPeLaCzwQrPM1Lgs9uhEUrPXFZDMWbJnr33JGnpCWC+Qowi1TTN8cj43oOn3EbEzPd0rzQ6gjOleFewdbaF86Y+MkPb1Y/fQFa4PRb/GP0U15sJOTMYHOpV2iQeknZjy1v/kB4vmmSs0FkHzsHytPAcD62lbnxtmiGDkTrIyWterjGcLZvjv5tKKU2+eArUnm8F8rMyIYzQazSx6iAqQPmJu5VTh8XCSEyUZEY8pz6epFmxcysOqjUgC+zAdfXLXfq24kP07AVc3YVS4GItnQqcBgXLqLB7qSaEYoHCudKEGE3Rq2UXCVcsdVUaAX1nFljnWvlKMdpUPLY7opVHRTdhwsqdjR8lLRieAcyPo3LWnj8l4k88ZmuWkT9k/dsN20tg+ogqU6sEomwKCdDHZjCAU0E0v2kyJ/vHFbCpJlLRzOLTyHt1xpbhR6vxp3KQ+BYCUH09eCZgiOnEH1OFu0q6Ylf+I0rEu0ZGu41w==',  // pragma: allowlist secret
      },
    },
    GCP_US_CENTRAL1_PROD: {
      STAGING: {
        signing_secret: 'AgAhaPvmO2DrtHx4AxmIVgPg4pwy9XQ3TIJhugVt2RodUcGMMNZZpVRXRa92E0DHezYHHBPZ3U6ZyOuL1XrtFsjgAelPu9pODnNXFmIqV1PwqI8jRB5mNm3qfVh+1KMxCJnQBaJwcJBBgpI0rQ+3VZdLkBC3lwfdJbTndxiAD9r2KIlIEvHihklvEqCKw17F9Y2gEKLBaWqIghlRPxHY0b9HBqXnn94XHzNdIxeeohWfH1j0Mi19O01c23qI5Kd6/4zR73mzcDgPaPOqaGZDXRi4Gn0lxRLAUK1Xk+8Sw/2NqQ4hUZUSjyNRpRD2/Q6ynBQlfftNqMn9Bgz/+AvVon3T7yYADlRLyoUH56jQOds5KBK9fD5FIPTOrhdfUKctXSXB1JNMO0kYGk9OfMlD+JBiGr6vbHI96SrbARlIDcBtAtp8GfEOngFY52l1AJBi5mT0+Ib2jiRfT8EVJFtxkWxZiP18Qy7NLLwiyE/4CLGV7aZCtcaapKC9z6sF5m+Yj2chWMYB1vpCO77hHR69yFHcaPf3Jxb7zctoypEV+7xFXyIPHWVsG619uf/pKtLyz4PHFhrxiusNgj0QiH70+daVttPnsHluuNy9TjjYNkM+/V2O1bTtNl6Rt5Q1m1u0aPqIsa0O2W090ZOvZx8tChpzjLTbtBzwZzuG6WlvlGGRpfvYDVQH4jWfao9P1CkE4baBXH8i+Yqa1D4L3QTCho9FzNBxhv8RBAr5kOkonrp7Qw==',  // pragma: allowlist secret
        basic_creds: 'AgBt4R1cOYonilh4dtq1BhtA4L6P22F2mIeiCJUltC5pFKAmBe6eaREgiGS6dMjQ0EJ122itFiNVrcPKrTC6KXIGw9qx/3IeepwgzNgq3yt4TSXJeX4P4wJFxOCCkFq9VuX3nT/7i9Bgh6SNNCUBtG5JW2AifQRw4oXkkTFOUqCDW/DyguLbXtkFVQjovrhZmXjWWA094IAAz2jn2jtoMszVOiSpom1lomipiOJE+w82RWRIJo/484SwBBfg5c9bXWDxgZdc77tjlACt8K+XynZEksS92xIV2DSaT4bYVgkVzULIIeZAfvn4aq3LqPuG/Riz3UJr4pX66jkqyD+97fVe6PQ3XvLsmUCywg5gVmSw7FLvG5e+ieLRu6zO53q5ntT/PmKJG0TO1O+WubxPWD38tRbov+ILYQ/b3qUw9x40pKL0xO5L4IcnAYbIVO1tcj4PflJvm/wRIjuFB/M/6DUiitp6j2oYixtgfE89a4zpqY0YL4yJfbhvyLYNLnbEP4lVuIgXFuJ6zwO9Cng2IvuFZnBYxwPgPMbaFmfl2wDbsV0I08M/zR3cCKtLSL/7+xEUydzEr2iyPuWCNWESdiy+sN9jFCkSBCQvdsFEN8eIJ8W5wEOgE2k/gTB41s4Po2qXZsOC/iTp2ngrxOiQ/D0KJIlmi5ivy3QRmRAazekt+ViYFFFHkc7UPgXhifcIpP/XWa/eYK4VOW4J6dhIHLp0axmHrg5B6UaJMtexdV0ATGV3o4mzsJpwfdT+Z0m1L3zpCY7G0YxEze1llwpUHdw+PVn04UXbu+uDpmjboi+P8Q==',  // pragma: allowlist secret
      },
      PROD: {
        signing_secret: 'AgDA/qirDSawKxVHOnMj6V2322tB5/xWy2D0gHVL1BKb2x6MeMtZ84NTzDcwFACTwkb4hp5OBEddpl+GODBkp5RaxntHSmtjzmDHePYv14TYxHOu/5afyvtBUYr9u3cE3J4jp3bUPj631bcT5vP6GeBxaJdfvMrhfFi98kkW36wZE30aKD3gieKMnph9zMAp5whNzQL0vdW7xWtTooLbNS2XXK5lvQ5kBrv3PrNWUegteB8rT3lCggC8smwJtAlDlC7uTHlGHkzoHJovZcEA6n/wG+VhkMjuwqwGNKcuDomgm9mM3gl11tbIAuc2JesNxIB4Z7ezqwEMM6DS7a8vGoDXoq1vteZe0XEmb1ivKRnOGNUPf5jyIAxveyPlWA5RajKkc+7OddjtEDzh3d8xYXB9JmFDoQ2HaOlEYITQeqd+7YuRMW7Sf0lad9WH5aNIxRTKeM2NTueaqqFDKq7xRxLBK1Y4qGDIyItAD7FrhU9/8MVkZ+HX2mv5S/dO6yDe9DvjajJdKp3DSOqF1ichfdCZpUXVUXR4j+2M93hzsbvp0YLKq+RIpXjdU1Bbba3JFtxXAonjvwWFW1JCCBeSju02XlGv+JbTthlgPghXPTke9JU0138sWqlD14P7kPfuwLohvdfrEnYb5DbDN2efP2lIR7OiYdJDLE3heXack7dP3MmU3+JtrYMui7dA5ND5O5Uumb58WX7PX+0oMNGAoaIE0Zpl46pvlOc4JczlY/2UCw==',  // pragma: allowlist secret
        basic_creds: 'AgDcGRTADW1Hscotc2gJCpmgrDKkLTHfT/o2n2/7/7fQpOztsPkjgG3kExmAX94EJ61ZFB8+iduBHvEdBLPgiWHZUroaUbH8+V9b+QPo1ngtqvH147zuy1OWU/4ZFNmAWmAWFF9Y091ogP7QHRwjqK6hzkN/Iz1OIGdVR/Q+bJUndIhN5XHkodG1bPPzh0VERlsfkOB4rXGeIFMjqBu1xCe3p9ui+hwubtOj2g49on687iepSjYYf50+lIdtfNQF/d4gUikEkO5HgrIR5dEpzghwP0RaZY6iLbaDyxwqQICMqre4b0aG6e2MU49s5cMr6zJ7tvMIbCVyUaATZgmLF1GbSAm+i1CqNWK6tvCO3hX26ZDrYEtMOuIM5mPk0+GC/Hfq+7WiS1dPIUwWp66GLYMAFlKcGzECStkGMUsUsiAKj+sPzUTo8PzETm/fTzbR8EGAoj9jSmYY4dMSIGd8bSw7xUpRrcZ5fXQKZ7V1BXdGum9H1tftDEnn8D6TLEx+9iQGzy1bIuf6RG/D9kXb1fntnluXST3urmISsg/zvhtxMELfUeGjr+APR7Oo8UQulDL9/gxp3IkpXaIgAg5NHp7HAQPe6hAppBxBz4KgzyGoeQRM6+JS89St0CbrS/ZlMauDDcrl6nv+ikkhKsw34NsDdgSn+RdyCy8sb9IS+FnDBMegreq85MXp62p44ioaFA0TjBa8wrwtKYaRMC1w2ZpEeAZ3tyg571EvkLPGczlZK+b05RWIiMdOApcaNrO09TJ4M0o20ca+P1L6iAKKL2DmzYLwkRzM2bI1VBRGPFaEzA==',  // pragma: allowlist secret
      },
    },
    GCP_EU_WEST4_PROD: {
      STAGING: {
        signing_secret: 'AgB0F6Mivf0mzdTmaa8T3Q3yAlsbBZyf8VfvLogrp5eIDiZct3WeTS86qb7odXphfYDDAn6Yvhw30fLjBClr3pbKmUqejcJ8nrrkzpHJgVKOVQ9iWs14AIzeDh3q+w8HGpuCNp0mcgD/JAZ4KMI63GUv+kbzpTHoOCjFmeIoGLaW9/WsCtT+uHwpf4jiTPgDArBDHUeUIHYbVITKBhUyQ2u5U8pRtfNi32jvrnbpGa0M6hta4glvA7he1uogjRWQxJzNoUvKjGiMB3qKbA3dBayHq5D86F+KeaL0CjXbUdCT3sqUshWFxl6TS4kV634ggDvUa3QW9wRxghUM0ZgNNWsWiOlIWRYnVM6tj30ZWn/zbkE/Ee5bvqYjNLuNHsYFEf0Vej7m70t5oeE5NkkT4hHFerXzCcue5vQwXiuenjfx3/s0nVCMdvsJoa8v2n/G4ws7Hrb3lYjEwKykzA3pMgBh5lPI6BGrkzwSq70RdbYev2HoNngSNAymvMPLSPcoiWGSL2Ju6iBMtejbAyahlg+z6WeHX1Qz6+fXIOL0QXLyDcQkbOoBs7UCCFMcOtbaM+xxR0uyg7bSwFK7Q3nlQP0Wd0CJicvgjtDKeLDdT0kjim1giXr4oEunrnmGqyCiShsIWmNHV6CcXa083NzfGhTIO0HlwaYmrEK0+XQ1S5SeNxEzPA/pzM9C7At71zb8giXZro7DnwKaB1WZIJMHTgy5Pvy32hHFtQ0OY42Mk+mH9g==',  // pragma: allowlist secret
        basic_creds: 'AgB48mWtYI9T1JDMF329HsuhvBMeanIakQFAosEt3bfa3T0ONhBRE6YFJvMxnPSCVXxCxpZH9s0pI1ptdf5S2MXOVRQBfIVrtvhIUukXR4mmwAawlQSYjZzxKlcTcEy3bm13jHp/hHNmabYTCqKNVQKxOOXOMluSsulGV6tnI1TdKQb24NBCNRs/yKQFy3lovxRr7hQbrRfZ4zLWDly8mtu+eDZIK0FkXhLUF8UqrKxPUOfZUNgit71TMPEAgKXsNTDXgOFh78TqjBr5yTs2Si+EhAlxEqMCZd7I+HiUtOnfviHj0uuFttC345FbGxS3Rp+LIby/+AHPC5OydapNJmd6/8XXNes1sdf68puZHxVJxWiUgiFjKYPLgvEGmPrPtQzvoNNQy8WmRBmbfwPyoBc5Gmu222HnTb/U0SmGhEAKjcu5drzrZ1Ymq0cShhBMIBXnJ5n5oEClCveAujrzsuqMtXkUT0eaJfd4n3JLZ6PqvT+NaR1gx+ZibobIA3h7R4YLQzySPIVaHcdSPoX9DeEWBY3oj1xqjY8u6jeMXPn3pl7BDHzqbISOQ+LLUdUA1NC4ND/e00/qRZ5sj0mGiU/g16q1FrKWtFhwnPTn9Jtj8XHxe/Ui+9/KuParTaU17BvSlwaAAoIX2P5xzqZd3elFxvUV/uKtrAU7n43LXwGrjnfWDi1d1qTMhd0uT6dl98ATaaf2YEZZuirlOJOZfdwOyPUy8oBS80xTlI/WGk9fFWLO3QRdCmoINs7HVClFTrVvRGA80IXKE/kQsq+4hdN4/CW4hnr26/1VlqEchxxiFQ==',  // pragma: allowlist secret
      },
      PROD: {
        signing_secret: 'AgAYcpsNYdVNHAlK1/lcB5sizdN0JTe0eFcV6QaD9E0fHNjPUr7Km9cz0ttnRCoitJY4LEd/IocTNl7gSfuvrBcg7yqhdv9NYORbXT05+9xlKRJxkel2jYcJrOZXP2Y7qUrwLeu/gDGWXkCzW1YLG7fhegXJw6sN/JAkdMYbYq6Q+HxtV1r08E5osc6Yn+Rgoq8HyZeyn7lmBtOn6Pycu06swdvawfpeQXu3sGk2Ap49nhBHEfGhsacUr1IWteYCBCWP/7rmC0Z0tg2ijzRKPIHkGHMjAm3IhbeaSjNP/VK1awGhb1LhJf1/MWjgObLqwnAzhSXjy+Trn8tIBYuhsMp20fjmnw3zZcF7Qpj6A5QHJS9vnNeHHLBkgThjfGVvsxeRtj8uEiJFSvxx55PckEyNj2tVoLnrt6fXsSk7XxohGlBl4K9YbDRgXwK5yFXbAnzXBjazCHRXecUDt5beKDyKJVGNlYM/dmqslo06h3j5gc6fhHfP4bL4zDeXycaoP+yOr2URwdq1mb0lN5u+x06UpKt/7WfxXYmXqv+/dg42/vNS67SxEDvKiaiAmvszfy8AgmjsQrHXeDOOWVGXhg4lo7GtH7IXGItlTzD9W/4UTnbO5cVLfJR0kCO/JVrQPvpWafhMYB7Lt1P6oDifmPYP/czPy5qb2h4rY/7eKCKkRSZdPWRrmDuQ1Y8KrlHWTVJ2TJoJ5rNGcWxN4/hVp36VztfEm1FB6zxm/y3oJlK/FA==',  // pragma: allowlist secret
        basic_creds: 'AgAyo49R12xFjr/AAgPinOCghkwL1OA6uZTZqzq2PMVFz2i61j7CGj6LIoqf1LH9aFzMf0YC8b4FXX7UmBrn90dUCJ4tgrJvSP5nuDWwnTqU7FdZzZO0NKM2Z2BdCyf5i03IaeNbtTPMLj5ZYV1ub9EFRK+qBNVAWPaFpFkVcwH/3jOQ2mWoDXJ2+5nHlqpDLBzz2ayHpz/iDhvR4h3zEgHpiFItkO84TTPO9+/RO20lBFUZzb+m0i0aEYuHkxrg714dp6AYdFxYhQQrqI2aKWiFS9+7+uVvm4JZ0MRVcrwSh3BWFlatLdKOBjn4E3iA54Lv2WBbPRUEfUXeKqpiMRB0p4blHSBjGl3Z+Ijegf6LE+8stlzvb9lZsAM/E5ax2T6k3TRAlA/g07QDQYw3u+Xg/dzbh/0G6to793wsD32ak67AuZdXGIdx5VlOYhr3JTRdDDCtuXLNhOA4kJjmM9vuAmWOGJ+/lwyj3OKjQEkJrP8nytkZcAxGY03YThx/rPR69RXEv2P8LhyCd4gQvd9TZozVjTQ7F4ZctzoHqvJvuZPJNxU5SbnUkMLe3zQdQCFGHmjqqG9i0baJ/rieA6hF/rRfGgYLqL+scXVON77E4T6S8M6iUKlWEC0fQXQ9wDPLNWhdHl9egVNLFXsErFu0504De8zm8a0ap88UWA9iH8NUShXnncRCJgjIeZXXvgDY/bkf8xyBb4wJ1kA09M4gytSlphyRWpIGcjuT1AHDSrvlyJ0auQ5DXjsHKNk8rVA/1erkXIZYQS1hb23Qaq//JriFnT/7C7mZNus4eaAhYA==',  // pragma: allowlist secret
      },
    },
  }[cloud][env];

  local slackSecret = {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: '%s-slack-bot-token' % appName,
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
      labels: {
        app: appName,
      },
      namespace: namespace,
    },
    spec: {
      template: {
        metadata: {
          name: '%s-slack-bot-token' % appName,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
        type: 'Opaque',
      },
      encryptedData: if useSecretsOverride then secretsOverride
      else data,
    },
  };
  slackSecret

// Constants used across the slack processor and webhook to create block kit items and respond to them.

package common

import (
	"encoding/base64"
	"fmt"
	"strings"

	"google.golang.org/protobuf/proto"
)

const (
	// Action IDs for interactive components
	ACTION_OPEN_REPO_SELECT_MODAL  = "open_repo_select_modal_action"
	ACTION_SELECT_ALL_BUTTON       = "select_all_action"
	ACTION_DESELECT_ALL_BUTTON     = "deselect_all_action"
	ACTION_MULTI_SELECT            = "multi_select_action"
	ACTION_FEEDBACK_RATING         = "feedback_rating_action"
	ACTION_FEEDBACK_NOTE           = "feedback_note_action"
	ACTION_GITHUB_APP_BUTTON       = "install_github_app_button_action"
	ACTION_PROVIDE_FEEDBACK_BUTTON = "provide_feedback_button_action"
	ACTION_SIGN_INTO_GLEAN_BUTTON  = "sign_into_glean_button_action"

	// Callback IDs for modals and message actions
	CALLBACK_REPO_SELECT_MODAL     = "repo_select_modal"
	CALLBACK_FEEDBACK_MODAL        = "feedback_modal"
	CALLBACK_MSG_FEEDBACK_SHORTCUT = "msg_feedback" // set in slack app settings

	// Block IDs
	BLOCK_REPO_SELECT_MULTI_SELECT  = "repo_select_multi_select_block"
	BLOCK_SELECT_ALL                = "select_all_block"
	BLOCK_DESELECT_ALL              = "deselect_all_block"
	BLOCK_FEEDBACK_RATING           = "feedback_rating_block"
	BLOCK_FEEDBACK_NOTE             = "feedback_note_block"
	BLOCK_REPO_SELECT_BUTTON        = "repo_select_button_block"
	BLOCK_INSTALL_GITHUB_APP_BUTTON = "install_github_app_button_block"
	BLOCK_PROVIDE_FEEDBACK_BUTTON   = "provide_feedback_button_block"
	BLOCK_SIGN_INTO_GLEAN_BUTTON    = "sign_into_glean_button_block"

	// Commands
	COMMAND_SUFFIX           = "augment"     // set in slack app settings - uses dev-..., staging-, or nothing as the prefix
	REPO_SELECT_COMMAND_TEXT = "repo-select" // any command argument that includes this word will trigger the repo selection modal
)

// EncodeProtoMetadata encodes a proto message to a base64-encoded string for use in Slack metadata fields
func EncodeProtoMetadata[T proto.Message](metadata T) (string, error) {
	// Marshal the metadata to bytes
	metadataBytes, err := proto.Marshal(metadata)
	if err != nil {
		return "", fmt.Errorf("failed to marshal modal metadata: %w", err)
	}

	// Encode to base64
	encodedMetadata := base64.StdEncoding.EncodeToString(metadataBytes)

	return encodedMetadata, nil
}

// DecodeProtoMetadata decodes a base64-encoded string to a proto message
func DecodeProtoMetadata(privateMetadata string, out proto.Message) error {
	// Replace spaces with '+' to handle potential URL encoding issues
	// This is a hack - asking Slack to see if there's a different encoding format we should use
	privateMetadata = strings.ReplaceAll(privateMetadata, " ", "+")

	// Decode from base64
	metadataBytes, err := base64.StdEncoding.DecodeString(privateMetadata)
	if err != nil {
		return fmt.Errorf("failed to decode base64 metadata: %w", err)
	}

	// Unmarshal the decoded bytes into the provided proto.Message
	err = proto.Unmarshal(metadataBytes, out)
	if err != nil {
		return fmt.Errorf("failed to unmarshal modal metadata: %w", err)
	}

	return nil
}

syntax = "proto3";

package slackbot;

import "google/protobuf/timestamp.proto";
import "services/integrations/glean/glean.proto";
import "services/integrations/slack_bot/slack_event.proto";
import "services/settings/settings.proto";

service SlackBotProcessor {
  // Takes a temporary slack access token during the slack bot installation flow and does all the
  // Bookkeeping to set up slack bot for the tenant.
  // The user should be authenticated before calling this method.
  rpc HydrateSlackSettings(HydrateSlackSettingsRequest) returns (HydrateSlackSettingsResponse) {}

  // Links a Slack user ID to an Augment user ID
  // Currently only called using the state parameter from Glean OAuth callback
  // The user should be authenticated before calling this method.
  rpc LinkSlackUserToAugmentUser(LinkSlackUserToAugmentUserRequest) returns (LinkSlackUserToAugmentUserResponse) {}

  // Opens a modal to select a repo for the given channel.
  // Using RPC instead of the slack event pub sub queue to minimize latency and respond to the user before the slack triggger ID expires.
  rpc OpenRepoSelectModal(OpenRepoSelectModalRequest) returns (OpenRepoSelectModalResponse) {}

  // Processes the repo selection made by the user in the modal and updates the slack settings for the tenant.
  // Using RPC instead of the slack event pub sub queue to minimize latency and respond to the user before the slack triggger ID expires.
  rpc HandleRepoSelection(HandleRepoSelectionRequest) returns (HandleRepoSelectionResponse) {}

  // Processes the select all repos action made by the user in the modal and sends an updated view back to the user
  // Using RPC instead of the slack event pub sub queue to minimize latency and respond to the user
  rpc HandleSelectAllReposAction(HandleSelectAllReposActionRequest) returns (HandleSelectAllReposActionResponse) {}

  // Gets the options displayed in the multi-select modal for repo selection. Used for external multi-select modals when the tenant has more than 100 repos.
  // Using RPC instead of the slack event pub sub queue to minimize latency and respond to the user.
  rpc GetRepoOptions(GetRepoOptionsRequest) returns (GetRepoOptionsResponse) {}

  // Handles the shortcut for a user to send feedback to the slack bot.
  // Opens up a modal to actually send the feedback if it is on a valid message.
  // Using RPC instead of the slack event pub sub queue to minimize latency and respond to the user.
  rpc OpenFeedbackModal(OpenFeedbackModalRequest) returns (OpenFeedbackModalResponse) {}

  // Handles processing the submission for a feedback event.
  // Using RPC instead of the slack event pub sub queue to minimize latency and respond to the user.
  rpc HandleFeedbackEvent(HandleFeedbackEventRequest) returns (HandleFeedbackEventResponse) {}

  // Clears the slack user to augment user mapping for the given tenant.
  rpc ClearSlackUserToAugmentUserMapping(ClearSlackUserToAugmentUserMappingRequest) returns (ClearSlackUserToAugmentUserMappingResponse) {}
}

message HydrateSlackSettingsRequest {
  string code = 1 [debug_redact = true];
}

message HydrateSlackSettingsResponse {}

message LinkSlackUserToAugmentUserRequest {
  string slack_user_id = 1;
}

message LinkSlackUserToAugmentUserResponse {}

// Contents passed through "PrivateMetadata" of the repo select modal
message RepoSelectModalMetadata {
  // channel that the modal is being opened in - this is required
  string channel = 1;

  // if this is set, we will regenerate the response to this event after the user selects repos
  SlackEvent slack_event_to_regenerate = 2;

  // timestamp of the bot message that led to opening this modal, if applicable
  // if this is set, we will delete the original message when the modal is submitted
  string original_bot_message_timestamp = 3;

  // timestamp of the thread that led to opening this modal, if applicable
  string thread_ts = 4;
}

message OpenRepoSelectModalRequest {
  EventMetadata metadata = 1;
  string user = 2;
  string channel = 3;

  // deprecated - no longer needed
  string command = 4 [deprecated = true];

  string trigger_id = 5;

  // if this is set, we will regenerate the response to this event after the user selects repos
  SlackEvent slack_event_to_regenerate = 6;

  // timestamp of the bot message that led to opening this modal, if applicable
  string original_bot_message_timestamp = 7;

  // timestamp of the thread that led to opening this modal, if the command was sent in a thread
  string thread_ts = 8;
}

enum OpenRepoSelectModalError {
  NO_ERROR = 0;
  DEFAULT = 1;
  BOT_NOT_IN_CHANNEL = 2;
  BOT_NOT_IN_DM = 3;
  NO_GITHUB_APP = 4;
  NO_REPOS = 5;
}

message OpenRepoSelectModalResponse {
  OpenRepoSelectModalError error_type = 1;
}

message HandleRepoSelectionRequest {
  EventMetadata metadata = 1;
  string user = 2;
  string channel = 3;
  repeated string selected_repos = 4;
  string timestamp = 5;
  bool select_all = 6; // either select_all must be true or selected_repos must be non-empty
  string original_bot_message_timestamp = 7;
  string thread_ts = 8; // timestamp of the thread that led to opening repo selection modal if applicable
}

message HandleSelectAllReposActionRequest {
  EventMetadata metadata = 1;
  string user = 2;
  string view_id = 3;
  string channel = 4;
  bool is_select_all = 5; // true for select all, false for unselect all
  SlackEvent slack_event_to_regenerate = 6;
  string original_bot_message_timestamp = 7;
  string thread_ts = 8; // timestamp of the thread that led to opening repo selection modal if applicable
}

message HandleSelectAllReposActionResponse {}

message HandleRepoSelectionResponse {}

message GetRepoOptionsRequest {
  EventMetadata metadata = 1;
  string timestamp = 2;
  string search_query = 3;
}

// matching the slack format we need for the external data source
// https://api.slack.com/reference/block-kit/block-elements#external_multi_select
message OptionBlockObject {
  message TextBlockObject {
    string type = 1;
    string text = 2;
  }
  TextBlockObject text = 1;
  string value = 2;
  optional TextBlockObject description = 3;
}

message GetRepoOptionsResponse {
  repeated OptionBlockObject options = 1;
}

message OpenFeedbackModalRequest {
  EventMetadata metadata = 1;
  string user = 2;
  string trigger_id = 3;
  FeedbackModalMetadata modal_metadata = 4;
  string message_sender = 5;
}

message OpenFeedbackModalResponse {}

message HandleFeedbackEventRequest {
  EventMetadata metadata = 1;
  string modal_private_metadata = 2; // base64 encoded FeedbackModalMetadata passed through the modal so we can reference the original message
  string rating = 3;
  string note = 4;
  string user = 5;
}

message HandleFeedbackEventResponse {}

message ClearSlackUserToAugmentUserMappingRequest {}

message ClearSlackUserToAugmentUserMappingResponse {}

// the contents of modal_private_metadata
message FeedbackModalMetadata {
  string channel = 1;
  string message_timestamp = 2;
  string thread_timestamp = 3;
}

// Used to pass context to the chat model
message SlackbotChatMetadata {
  string bot_id = 1;
  string channel_name = 2;

  // deprecated - use previous_thread_messages instead
  string conversation_history = 3;

  repeated Repo repos = 4; // repos used as context in the conversation

  // deprecated - no longer needed
  string user_timezone = 5 [deprecated = true];

  repeated settings.RepoInformation all_repos = 6; // all repos installed for the tenant
  repeated glean.Document glean_documents = 7; // documents used as context in the conversation

  repeated string previous_thread_messages = 8; // previous messages in the slack thread
}

message Repo {
  string repo_name = 1;
  string repo_owner = 2;
  string indexed_commit_sha = 3;
  google.protobuf.Timestamp indexed_commit_time = 4;
  string current_commit_sha = 5;
  google.protobuf.Timestamp current_commit_time = 6;
}

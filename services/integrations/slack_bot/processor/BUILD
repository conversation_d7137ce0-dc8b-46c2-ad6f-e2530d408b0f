load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_proto_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "processor_proto",
    srcs = ["processor.proto"],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//services/integrations/glean:glean_proto",
        "//services/integrations/slack_bot:slack_event_proto",
        "//services/settings:settings_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "processor_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto",
    proto = ":processor_proto",
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//services/integrations/glean:glean_go_proto",
        "//services/integrations/slack_bot:slack_event_go_proto",
        "//services/settings:settings_go_proto",
    ],
)

ts_proto_library(
    name = "processor_ts_proto",
    data = [
        "//services/integrations/glean:glean_ts_proto",
        "//services/integrations/slack_bot:slack_event_ts_proto",
        "//services/settings:settings_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":processor_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

py_proto_library(
    name = "processor_py_proto",
    protos = [":processor_proto"],
    # consumed by services/chat_host
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/integrations/glean:glean_py_proto",
        "//services/integrations/slack_bot:slack_event_py_proto",
        "//services/settings:settings_py_proto",
    ],
)

package main

import (
	"context"
	"fmt"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/secretstring"
	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	slackbotproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"

	"github.com/rs/zerolog/log"
)

type TenantSlackInfo struct {
	token     secretstring.SecretString
	botUserId string
}

type TenantLookup interface {
	/*
		Returns repos with indexing information, the checkpoints, names of all installed repos, and the installation id.
		If useChannel is true, reposForContext and checkpoints will only contain the information for the given channel
	*/
	GetRepos(ctx context.Context, tenantSettings *settingsproto.TenantSettings, requestContext *requestcontext.RequestContext, channel string, useChannel bool) (reposForContext []*slackbotproto.Repo, checkpoints []*blobsproto.Blobs, allRepos []*settingsproto.RepoInformation, installationId int64, err error)
	GetTenantSlackInfo(ctx context.Context, tenantSettings *settingsproto.TenantSettings) (*TenantSlackInfo, error)
	GetTenantName(ctx context.Context, tenantID string) (string, error)
	GetTenantConfigs(ctx context.Context, tenantID string) (map[string]string, error)
}

type MultiTenantLookup struct {
	tenantCache       tenantwatcherclient.TenantCache
	githubStateClient githubstateclient.GithubStateClient
}

func NewMultiTenantLookup(
	tenantCache tenantwatcherclient.TenantCache,
	githubStateClient githubstateclient.GithubStateClient,
) TenantLookup {
	return &MultiTenantLookup{
		tenantCache:       tenantCache,
		githubStateClient: githubStateClient,
	}
}

func (c *MultiTenantLookup) GetTenantSlackInfo(ctx context.Context, tenantSettings *settingsproto.TenantSettings) (*TenantSlackInfo, error) {
	if tenantSettings.SlackSettings == nil {
		return nil, fmt.Errorf("no slack settings found for tenant")
	}
	return &TenantSlackInfo{
		token:     secretstring.New(tenantSettings.SlackSettings.OauthToken),
		botUserId: tenantSettings.SlackSettings.BotUserId,
	}, nil
}

func (c *MultiTenantLookup) GetRepos(
	ctx context.Context,
	tenantSettings *settingsproto.TenantSettings,
	requestContext *requestcontext.RequestContext,
	channel string,
	useChannel bool,
) (
	reposForContext []*slackbotproto.Repo,
	checkpoints []*blobsproto.Blobs,
	allRepos []*settingsproto.RepoInformation,
	installationId int64,
	err error,
) {
	// Add installed repos from GitHub settings
	if tenantSettings.GithubSettings == nil ||
		(tenantSettings.GithubSettings.InstallationId == 0) {
		log.Ctx(ctx).Info().Msg("No Github settings found for tenant")
		return nil, nil, nil, 0, nil
	}

	if tenantSettings.SlackSettings == nil {
		log.Ctx(ctx).Info().Msg("No slack settings found for tenant")
		return nil, nil, nil, 0, nil
	}

	installationId = tenantSettings.GithubSettings.InstallationId
	if installationId == 0 {
		log.Ctx(ctx).Info().Msg("No installation found. Promoting github app")
		return nil, nil, nil, installationId, nil
	}

	allRepos = tenantSettings.GithubSettings.Repos
	if allRepos == nil {
		log.Ctx(ctx).Info().Msg("No repos found for tenant")
		return nil, nil, nil, installationId, nil
	}

	// used to ensure repos seen in other responses exist in allRepos
	allReposMap := make(map[string]*settingsproto.RepoInformation)
	for _, repoInfo := range allRepos {
		allReposMap[repoInfo.RepoOwner+"/"+repoInfo.RepoName] = repoInfo
	}

	// information about the requested repos to use in this channel
	reposForContextMap := make(map[string]*slackbotproto.Repo)

	if useChannel {
		// If there is only one repo installed, use it for all channels
		if len(allRepos) == 1 {
			reposForContextMap[allRepos[0].RepoOwner+"/"+allRepos[0].RepoName] = &slackbotproto.Repo{
				RepoOwner: allRepos[0].RepoOwner,
				RepoName:  allRepos[0].RepoName,
			}
		} else if tenantSettings.SlackSettings.ChannelMappings != nil {
			// Use repos defined in channel mapping
			channelMappings := tenantSettings.SlackSettings.ChannelMappings.ChannelMappings
			repoMappingForChannel, exists := channelMappings[channel]
			if exists {
				for _, repoInfo := range repoMappingForChannel.Repos {
					// check to make sure the repoInfo is a valid repo for this tenant
					_, exists = allReposMap[repoInfo.RepoOwner+"/"+repoInfo.RepoName]
					if !exists {
						log.Ctx(ctx).Warn().Msgf("Channel mapping for %s is invalid. Skipping mapping.", channel)
						continue
					}
					reposForContextMap[repoInfo.RepoOwner+"/"+repoInfo.RepoName] = &slackbotproto.Repo{
						RepoOwner: repoInfo.RepoOwner,
						RepoName:  repoInfo.RepoName,
					}
				}
			}
		}
	} else {
		// Get indexing info for all repos
		for _, repoInfo := range allRepos {
			reposForContextMap[repoInfo.RepoOwner+"/"+repoInfo.RepoName] = &slackbotproto.Repo{
				RepoOwner: repoInfo.RepoOwner,
				RepoName:  repoInfo.RepoName,
			}
		}
	}

	// Add information about currently uploaded repos
	currentlyUploadedRepos, err := c.githubStateClient.GetCurrentRefStates(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting current repos")
		return nil, nil, nil, installationId, err
	}

	for _, currentRepo := range currentlyUploadedRepos {
		githubRepoProto := currentRepo.GetRef().GetRepo()
		repo, exists := reposForContextMap[githubRepoProto.GetRepoOwner()+"/"+githubRepoProto.GetRepoName()]
		// only add information about repos that we requested context from
		if !exists {
			continue
		}
		repo.CurrentCommitSha = currentRepo.GetCommitSha()
		repo.CurrentCommitTime = currentRepo.GetCommitTime()
	}

	// Add information about indexed repos
	indexedRepos, err := c.githubStateClient.GetIndexedRefCheckpoints(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting indexed repos")
		return nil, nil, nil, installationId, err
	}

	for _, indexedRepo := range indexedRepos {
		indexedRepoProto := indexedRepo.GetRef().GetRepo()
		repo, exists := reposForContextMap[indexedRepoProto.GetRepoOwner()+"/"+indexedRepoProto.GetRepoName()]
		// only add information about repos that we requested context from
		if !exists {
			continue
		}
		repo.IndexedCommitSha = indexedRepo.GetCommitSha()
		repo.IndexedCommitTime = indexedRepo.GetCommitTime()
		checkpoints = append(checkpoints, indexedRepo.GetBlobs())

	}

	// Convert map to slice
	reposForContext = make([]*slackbotproto.Repo, 0, len(reposForContextMap))
	for _, repo := range reposForContextMap {
		reposForContext = append(reposForContext, repo)
	}

	return reposForContext, checkpoints, allRepos, installationId, nil
}

func (c *MultiTenantLookup) GetTenantName(ctx context.Context, tenantID string) (string, error) {
	tenant, err := c.tenantCache.GetTenant(tenantID)
	if err != nil {
		return "", err
	}
	return tenant.Name, nil
}

func (c *MultiTenantLookup) GetTenantConfigs(ctx context.Context, tenantID string) (map[string]string, error) {
	tenant, err := c.tenantCache.GetTenant(tenantID)
	if err != nil {
		return nil, err
	}
	if tenant.Config == nil || tenant.Config.Configs == nil {
		return make(map[string]string), nil
	}
	return tenant.Config.Configs, nil
}

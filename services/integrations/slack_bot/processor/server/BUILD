load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "server_lib",
    srcs = [
        "chat.go",
        "main.go",
        "server.go",
        "slack_blocks.go",
        "slack_chat.go",
        "slack_clients.go",
        "slack_user_mapping.go",
        "tenant_lookup.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/processor/server",
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/clock:clock_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/api_proxy:model_finder_go_proto",
        "//services/api_proxy:public_api_go_proto",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:client_go",
        "//services/chat_host:chat_host_go_proto",
        "//services/deploy/model_instance:model_instance_go_proto",
        "//services/integrations/github/state/client:client_go",
        "//services/integrations/github/state/lib:github_state_lib",
        "//services/integrations/glean:glean_go_proto",
        "//services/integrations/glean/client:client_go",
        "//services/integrations/slack_bot:common",
        "//services/integrations/slack_bot:slack_event_go_proto",
        "//services/integrations/slack_bot/processor:processor_go_proto",
        "//services/integrations/webhookmapping",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "//services/settings:settings_go_proto",
        "//services/settings/client:client_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_hashicorp_golang_lru_v2//simplelru",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_slack_go_slack//:slack",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@io_k8s_client_go//dynamic",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@io_opentelemetry_go_otel//:otel",
        "@io_opentelemetry_go_otel//attribute",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "server_test",
    srcs = [
        "chat_test.go",
        "server_test.go",
        "slack_blocks_test.go",
        "slack_chat_test.go",
        "slack_user_mapping_test.go",
        "tenant_lookup_test.go",
        "test_helpers.go",
    ],
    embed = [":server_lib"],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//services/api_proxy:model_finder_go_proto",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:client_go",
        "//services/bigtable_proxy/client:fake_client_go",
        "//services/chat_host:chat_host_go_proto",
        "//services/deploy/model_instance:model_instance_go_proto",
        "//services/integrations/github/state:github_state_go_proto",
        "//services/integrations/github/state/client:client_go",
        "//services/integrations/glean:glean_go_proto",
        "//services/integrations/glean/client:client_go",
        "//services/integrations/slack_bot:common",
        "//services/integrations/slack_bot/processor:processor_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight/publisher:publisher_go",
        "//services/settings:settings_go_proto",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/integrations/slack_bot:slack_secret_lib",
        "//services/lib/pubsub:pubsub-lib",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
        "//deploy/tenants:namespaces",
    ],
)

package main

import (
	"context"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	fakebigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client/fake_client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

func TestStoreAndGetSlackUserMapping(t *testing.T) {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "service-name",
		TenantID:       "test-tenant-id",
		TenantName:     "tenant-name",
		ShardNamespace: "shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SLACK_MAPPINGS"})

	// Create the mapping service
	slackUserMapping := NewSlackUserMapping(fakeBigtable)

	// Create request context
	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)

	// Test storing the mapping
	slackUserId := "U12345"
	err := slackUserMapping.StoreSlackUserMapping(ctx, requestContext, slackUserId)
	if err != nil {
		t.Errorf("StoreSlackUserMapping returned error: %v", err)
	}

	fakeBigtable.Dump()

	// Test retrieving the mapping
	userId, err := slackUserMapping.GetAugmentUserId(ctx, requestContext, claims.TenantID, slackUserId)
	if err != nil {
		t.Errorf("GetAugmentUserId returned error: %v", err)
	}

	if userId != claims.UserID {
		t.Errorf("GetAugmentUserId returned incorrect user ID, expected: %v, got: %v", claims.UserID, userId)
	}
}

func TestStoreSlackUserMappingUnauthenticated(t *testing.T) {
	ctx := context.Background()
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SLACK_MAPPINGS"})
	slackUserMapping := NewSlackUserMapping(fakeBigtable)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))

	err := slackUserMapping.StoreSlackUserMapping(ctx, requestContext, "U12345")
	if err == nil {
		t.Error("StoreSlackUserMapping should return error when unauthenticated")
	}
}

func TestGetAugmentUserIdNotFound(t *testing.T) {
	ctx := context.Background()
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SLACK_MAPPINGS"})
	slackUserMapping := NewSlackUserMapping(fakeBigtable)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))

	userId, err := slackUserMapping.GetAugmentUserId(ctx, requestContext, "test-tenant-id", "nonexistent-user")
	if err != nil {
		t.Errorf("GetAugmentUserId returned unexpected error: %v", err)
	}
	if userId != "" {
		t.Errorf("GetAugmentUserId should return empty string for nonexistent user, got: %v", userId)
	}
}

func TestStoreSlackUserMappingMissingFields(t *testing.T) {
	ctx := context.Background()
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SLACK_MAPPINGS"})
	slackUserMapping := NewSlackUserMapping(fakeBigtable)

	// Test with missing UserID
	claims := &auth.AugmentClaims{
		TenantID:       "test-tenant-id",
		ServiceName:    "service-name",
		TenantName:     "tenant-name",
		ShardNamespace: "shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))

	err := slackUserMapping.StoreSlackUserMapping(ctx, requestContext, "U12345")
	if err == nil {
		t.Error("StoreSlackUserMapping should return error when UserID is missing")
	}

	// Test with missing TenantID
	claims = &auth.AugmentClaims{
		UserID:         "test-user-id",
		ServiceName:    "service-name",
		TenantName:     "tenant-name",
		ShardNamespace: "shard-namespace",
		Scope:          []string{"SETTINGS_RW"},
	}
	ctx = claims.NewContext(ctx)

	err = slackUserMapping.StoreSlackUserMapping(ctx, requestContext, "U12345")
	if err == nil {
		t.Error("StoreSlackUserMapping should return error when TenantID is missing")
	}
}

func TestMultipleSlackUserMappings(t *testing.T) {
	ctx := context.Background()
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"SLACK_MAPPINGS"})
	slackUserMapping := NewSlackUserMapping(fakeBigtable)

	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))

	// Store mappings for multiple users
	testCases := []struct {
		userId      string
		tenantID    string
		slackUserId string
	}{
		{"user1", "tenant1", "U1111"},
		{"user2", "tenant1", "U2222"},
		{"user3", "tenant2", "U3333"},
	}

	for _, tc := range testCases {
		claims := &auth.AugmentClaims{
			UserID:         tc.userId,
			TenantID:       tc.tenantID,
			ServiceName:    "service-name",
			TenantName:     "tenant-name",
			ShardNamespace: "shard-namespace",
			Scope:          []string{"SETTINGS_RW"},
		}
		ctxWithClaims := claims.NewContext(ctx)

		err := slackUserMapping.StoreSlackUserMapping(ctxWithClaims, requestContext, tc.slackUserId)
		if err != nil {
			t.Errorf("StoreSlackUserMapping returned error for user %s: %v", tc.userId, err)
		}
	}

	fakeBigtable.Dump()

	// Verify each mapping
	for _, tc := range testCases {
		userId, err := slackUserMapping.GetAugmentUserId(ctx, requestContext, tc.tenantID, tc.slackUserId)
		if err != nil {
			t.Errorf("GetAugmentUserId returned error for slack user %s: %v", tc.slackUserId, err)
		}
		if userId != tc.userId {
			t.Errorf("GetAugmentUserId returned incorrect user ID for slack user %s, expected: %v, got: %v",
				tc.slackUserId, tc.userId, userId)
		}
	}
}

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'slack-bot-processor',
      kubecfg: {
        target: '//services/integrations/slack_bot/processor/server:kubecfg',
        // deployment controlled by namespace_config.flags.userTier == "ENTERPRISE_TIER"
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'slack-bot-processor-monitoring',
      kubecfg: {
        target: '//services/integrations/slack_bot/processor/server:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

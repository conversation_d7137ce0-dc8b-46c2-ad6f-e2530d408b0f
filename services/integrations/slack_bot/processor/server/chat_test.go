package main

import (
	"context"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	modelfinderproto "github.com/augmentcode/augment/services/api_proxy/model_finder_proto"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	modelinstanceproto "github.com/augmentcode/augment/services/deploy/model_instance/proto"
	slackbotproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
)

func TestChat(t *testing.T) {
	mockTenantLookup := &MockTenantLookup{}
	mockModelFinderClient := &MockModelFinderClient{}
	mockChatClient := &MockChatProtoClient{}
	mockFeatureFlags := featureflags.NewLocalFeatureFlagHandler()

	chatClient := &ChatClientImpl{
		featureFlags:      mockFeatureFlags,
		tenantLookup:      mockTenantLookup,
		modelFinderClient: mockModelFinderClient,
		clientCreds:       insecure.NewCredentials(),
		mutex:             sync.Mutex{},
		modelChatClient: &ModelChatClient{
			modelName:  "good-test-model",
			chatClient: mockChatClient,
			chatConn:   nil,
		},
	}

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantSettings := &settingsproto.TenantSettings{}
	requestContext := &requestcontext.RequestContext{}
	previousThreadMessages := []string{"U11111: What's this tokenizer do?", "U22222: <@U12345> any idea?"}
	currentMessage := "U22222: <@U12345> any idea?"
	botId := "U12345"
	channelId := "C12345"
	channelName := "test-channel"

	mockTenantLookup.On("GetTenantName", mock.Anything, tenantID).Return("test-tenant-name", nil)
	mockTenantLookup.On("GetRepos", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*slackbotproto.Repo{{RepoName: "test-repo", RepoOwner: "test-owner"}}, []*blobsproto.Blobs{{BaselineCheckpointId: proto.String("test-checkpoint"), Added: [][]byte{[]byte("test-blob")}, Deleted: nil}}, []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}}, int64(0), nil)

	mockModelFinderClient.On("GetInferenceModels", mock.Anything, &modelfinderproto.GetModelsRequest{}, []grpc.CallOption(nil)).Return(&modelfinderproto.GetModelsResponse{
		Models: []*modelinstanceproto.ModelInstanceConfig{
			{
				Name:      proto.String("bad-test-model"),
				ModelType: (*modelinstanceproto.ModelType)(proto.Int32(int32(modelinstanceproto.ModelType_CHAT))),
				ModelConfig: &modelinstanceproto.ModelInstanceConfig_Chat{
					Chat: &modelinstanceproto.ChatModelConfig{
						ModelPriority: proto.Int64(0),
						ChatEndpoint:  proto.String("localhost:11111"),
					},
				},
			},
			{
				Name:      proto.String("good-test-model"),
				ModelType: (*modelinstanceproto.ModelType)(proto.Int32(int32(modelinstanceproto.ModelType_CHAT))),
				ModelConfig: &modelinstanceproto.ModelInstanceConfig_Chat{
					Chat: &modelinstanceproto.ChatModelConfig{
						ModelPriority: proto.Int64(1),
						ChatEndpoint:  proto.String("localhost:22222"),
					},
				},
			},
			{
				Name:      proto.String("really-good-non-chat-model"),
				ModelType: (*modelinstanceproto.ModelType)(proto.Int32(int32(modelinstanceproto.ModelType_INFERENCE))),
				ModelConfig: &modelinstanceproto.ModelInstanceConfig_Inference{
					Inference: &modelinstanceproto.InferenceModelConfig{
						ModelPriority: proto.Int64(3),
					},
				},
			},
		},
	}, nil)

	// To ensure we are comparing against a deterministically serialized object,
	// we need to serialize the message in the unit test rather than compare
	// against a golden string.
	msgSer, err := protojson.Marshal(&slackbotproto.SlackbotChatMetadata{
		BotId:                  botId,
		ChannelName:            channelName,
		PreviousThreadMessages: previousThreadMessages,
		Repos:                  []*slackbotproto.Repo{{RepoName: "test-repo", RepoOwner: "test-owner"}},
		AllRepos:               []*settingsproto.RepoInformation{{RepoOwner: "test-owner", RepoName: "test-repo"}},
	})
	msg := string(msgSer)
	mockChatClient.On("ChatStream", requestcontext.NewOutgoingContext(ctx, &requestcontext.RequestContext{}), &chatproto.ChatRequest{
		ModelName: "good-test-model",
		Blobs:     []*blobsproto.Blobs{{BaselineCheckpointId: proto.String("test-checkpoint"), Added: [][]byte{[]byte("test-blob")}, Deleted: nil}},
		Message:   "U22222: <@U12345> any idea?",
		ChatHistory: []*chatproto.Exchange{{
			RequestMessage: msg,
		}},
		PromptFormatterName: "slackbot",
	}).Return(new(MockChatStreamClient), nil)

	ch, err := chatClient.Chat(ctx, tenantID, tenantSettings, requestContext, previousThreadMessages, currentMessage, botId, channelId, channelName, false, nil)

	assert.NotNil(t, ch)
	assert.NoError(t, err)

	// wait for channel to close
	for range ch {
	}

	mockTenantLookup.AssertExpectations(t)
	mockModelFinderClient.AssertExpectations(t)
	mockChatClient.AssertExpectations(t)
}

func TestChat_External(t *testing.T) {
	mockTenantLookup := &MockTenantLookup{}
	mockModelFinderClient := &MockModelFinderClient{}
	mockChatClient := &MockChatProtoClient{}
	mockFeatureFlags := featureflags.NewLocalFeatureFlagHandler()

	chatClient := &ChatClientImpl{
		featureFlags:      mockFeatureFlags,
		tenantLookup:      mockTenantLookup,
		modelFinderClient: mockModelFinderClient,
		clientCreds:       insecure.NewCredentials(),
		mutex:             sync.Mutex{},
		modelChatClient: &ModelChatClient{
			modelName:  "good-test-model",
			chatClient: mockChatClient,
			chatConn:   nil,
		},
	}

	ctx := context.Background()
	tenantID := "test-tenant"
	tenantSettings := &settingsproto.TenantSettings{}
	requestContext := &requestcontext.RequestContext{}
	previousThreadMessages := []string{"U11111: What's this tokenizer do?", "U22222: <@U12345> any idea?"}
	currentMessage := "U22222: <@U12345> any idea?"
	botId := "U12345"
	channelId := "C12345"
	channelName := "test-channel"

	mockTenantLookup.On("GetTenantName", mock.Anything, tenantID).Return("test-tenant-name", nil)

	mockModelFinderClient.On("GetInferenceModels", mock.Anything, &modelfinderproto.GetModelsRequest{}, []grpc.CallOption(nil)).Return(&modelfinderproto.GetModelsResponse{
		Models: []*modelinstanceproto.ModelInstanceConfig{
			{
				Name:      proto.String("bad-test-model"),
				ModelType: (*modelinstanceproto.ModelType)(proto.Int32(int32(modelinstanceproto.ModelType_CHAT))),
				ModelConfig: &modelinstanceproto.ModelInstanceConfig_Chat{
					Chat: &modelinstanceproto.ChatModelConfig{
						ModelPriority: proto.Int64(0),
						ChatEndpoint:  proto.String("localhost:11111"),
					},
				},
			},
			{
				Name:      proto.String("good-test-model"),
				ModelType: (*modelinstanceproto.ModelType)(proto.Int32(int32(modelinstanceproto.ModelType_CHAT))),
				ModelConfig: &modelinstanceproto.ModelInstanceConfig_Chat{
					Chat: &modelinstanceproto.ChatModelConfig{
						ModelPriority: proto.Int64(1),
						ChatEndpoint:  proto.String("localhost:22222"),
					},
				},
			},
			{
				Name:      proto.String("really-good-non-chat-model"),
				ModelType: (*modelinstanceproto.ModelType)(proto.Int32(int32(modelinstanceproto.ModelType_INFERENCE))),
				ModelConfig: &modelinstanceproto.ModelInstanceConfig_Inference{
					Inference: &modelinstanceproto.InferenceModelConfig{
						ModelPriority: proto.Int64(3),
					},
				},
			},
		},
	}, nil)

	// To ensure we are comparing against a deterministically serialized object,
	// we need to serialize the message in the unit test rather than compare
	// against a golden string.
	msgSer, err := protojson.Marshal(&slackbotproto.SlackbotChatMetadata{
		BotId:                  botId,
		ChannelName:            channelName,
		PreviousThreadMessages: previousThreadMessages,
		Repos:                  []*slackbotproto.Repo{},
		AllRepos:               []*settingsproto.RepoInformation{},
	})

	emptyCheckpoint := ""
	msg := string(msgSer)
	mockChatClient.On("ChatStream", requestcontext.NewOutgoingContext(ctx, &requestcontext.RequestContext{}), &chatproto.ChatRequest{
		ModelName: "good-test-model",
		Blobs:     []*blobsproto.Blobs{{BaselineCheckpointId: &emptyCheckpoint}},
		Message:   "U22222: <@U12345> any idea?",
		ChatHistory: []*chatproto.Exchange{{
			RequestMessage: msg,
		}},
		PromptFormatterName: "slackbot",
	}).Return(new(MockChatStreamClient), nil)

	ch, err := chatClient.Chat(ctx, tenantID, tenantSettings, requestContext, previousThreadMessages, currentMessage, botId, channelId, channelName, true, nil)

	assert.NotNil(t, ch)
	assert.NoError(t, err)

	// wait for channel to close
	for range ch {
	}

	mockTenantLookup.AssertNotCalled(t, "GetRepos")

	mockTenantLookup.AssertExpectations(t)
	mockModelFinderClient.AssertExpectations(t)
	mockChatClient.AssertExpectations(t)
}

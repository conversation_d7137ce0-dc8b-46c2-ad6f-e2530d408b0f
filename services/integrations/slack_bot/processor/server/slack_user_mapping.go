package main

import (
	"context"
	"fmt"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"

	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const (
	familyName     = "Mappings"
	augmentUserCol = "augment_user_id"
)

// SlackUserMapping handles Slack user Id to Augment user mappings
// Not a GRPC class since only the slack server should call this
type SlackUserMapping interface {
	StoreSlackUserMapping(ctx context.Context, requestContext *requestcontext.RequestContext, slackUserId string) error
	GetAugmentUserId(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, slackUserId string) (string, error)
	ClearSlackUserMappingForTenant(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string) error
}

type SlackUserMappingImpl struct {
	ProxyClient bigtableproxy.BigtableProxyClient
}

func NewSlackUserMapping(proxyClient bigtableproxy.BigtableProxyClient) SlackUserMapping {
	return &SlackUserMappingImpl{
		ProxyClient: proxyClient,
	}
}

func (s *SlackUserMappingImpl) rowKey(slackUserId string) string {
	return fmt.Sprintf("slack_user#%s", slackUserId)
}

// StoreSlackUserMapping stores a mapping from a Slack user ID to an Augment user ID based on the context passed in
func (s *SlackUserMappingImpl) StoreSlackUserMapping(ctx context.Context, requestContext *requestcontext.RequestContext, slackUserId string) error {
	// must be authenticated to write to the table
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		return status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	augmentUserId := authInfo.UserID
	if augmentUserId == "" {
		return status.Error(codes.InvalidArgument, "user_id is required")
	}

	log.Ctx(ctx).Info().Msgf("StoreSlackUserMapping: slack_user_id=%s augment_user_id=%s", slackUserId, augmentUserId)

	tenantID := authInfo.TenantID
	rowKey := s.rowKey(slackUserId)
	entries := []*bigtableproto.MutateRowsRequest_Entry{
		{
			RowKey: []byte(rowKey),
			Mutations: []*bigtableproto.Mutation{
				{
					Mutation: &bigtableproto.Mutation_SetCell_{
						SetCell: &bigtableproto.Mutation_SetCell{
							FamilyName:      familyName,
							ColumnQualifier: []byte(augmentUserCol),
							Value:           []byte(augmentUserId),
						},
					},
				},
			},
		},
	}

	log.Ctx(ctx).Info().Msgf("StoreSlackUserMapping: tenant_id=%s, row_key=%s", tenantID, rowKey)

	_, err := s.ProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_SLACK_MAPPINGS,
		entries,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().
			Str("slack_user_id", slackUserId).
			Str("augment_user_id", augmentUserId).
			Err(err).
			Msg("Failed to store slack user mapping")
		return fmt.Errorf("failed to store slack user mapping: %w", err)
	}

	return nil
}

func (s *SlackUserMappingImpl) GetAugmentUserId(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, slackUserId string) (string, error) {
	rowKey := s.rowKey(slackUserId)
	log.Ctx(ctx).Info().Msgf("GetAugmentUserId: tenant_id=%s, row_key=%s", tenantID, rowKey)
	rows, err := s.ProxyClient.ReadRows(ctx,
		tenantID,
		proxyproto.TableName_SLACK_MAPPINGS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{[]byte(rowKey)},
		},
		&bigtableproto.RowFilter{},
		1,
		requestContext,
	)
	if err != nil {
		return "", fmt.Errorf("failed to read row: %w", err)
	}
	if rows == nil || len(rows) == 0 {
		return "", nil // Not found
	}
	if len(rows) != 1 {
		return "", fmt.Errorf("expected 1 row, got %d", len(rows))
	}

	for _, cell := range rows[0].Cells {
		if cell.FamilyName != familyName {
			continue
		}
		if string(cell.Qualifier) == augmentUserCol {
			return string(cell.Value), nil
		}
	}

	return "", nil // Column not found
}

func (s *SlackUserMappingImpl) ClearSlackUserMappingForTenant(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string) error {
	log.Ctx(ctx).Info().Msgf("ClearSlackUserMappingForTenant: tenant_id=%s", tenantID)

	rowRange := &bigtableproto.RowRange{
		StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("slack_user#")},
		EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("slack_user$")},
	}

	rows, err := s.ProxyClient.ReadRows(ctx,
		tenantID,
		proxyproto.TableName_SLACK_MAPPINGS,
		&bigtableproto.RowSet{
			RowRanges: []*bigtableproto.RowRange{
				rowRange,
			},
		},
		&bigtableproto.RowFilter{},
		0,
		requestContext,
	)
	if err != nil {
		return fmt.Errorf("failed to read rows: %w", err)
	}

	entries := []*bigtableproto.MutateRowsRequest_Entry{}
	log.Info().Msgf("Clearing %d SlackUserMapping rows", len(rows))

	for _, row := range rows {
		entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
			RowKey: row.RowKey,
			Mutations: []*bigtableproto.Mutation{
				{
					Mutation: &bigtableproto.Mutation_DeleteFromRow_{
						DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
					},
				},
			},
		})
	}

	resp, err := s.ProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_SLACK_MAPPINGS,
		entries,
		requestContext,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to write row")
		return err
	}
	for _, mutateRowsResponse := range resp {
		for _, entry := range mutateRowsResponse.Entries {
			if entry.Status.Code != int32(codes.OK) {
				log.Error().Msgf("Failed to write row at index %d: %v", entry.Index, entry.Status)
				return status.Error(codes.Internal, "Failed to write row")
			}
		}
	}
	return nil
}

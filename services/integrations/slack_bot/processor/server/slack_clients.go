package main

import (
	"context"
	"sync"

	"github.com/augmentcode/augment/base/go/secretstring"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/slack-go/slack"
)

var registerSlackOps = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_slack_api_ops",
		Help: "Number of Slack API operations performed",
	},
	[]string{"operations", "tenant_name", "status"},
)

func init() {
	prometheus.MustRegister(
		registerSlackOps,
	)
}

type SlackClient interface {
	PostMessage(channelID string, options ...slack.MsgOption) (string, string, error)
	PostEphemeral(channelID, userID string, options ...slack.MsgOption) (string, error)
	UpdateMessage(channelID, timestamp string, options ...slack.MsgOption) (string, string, string, error)
	GetConversationReplies(parameters *slack.GetConversationRepliesParameters) ([]slack.Message, bool, string, error)
	GetConversationHistory(parameters *slack.GetConversationHistoryParameters) (*slack.GetConversationHistoryResponse, error)
	AddReaction(name string, item slack.ItemRef) error
	RemoveReaction(name string, item slack.ItemRef) error
	DeleteMessage(channelID, timestamp string) (string, string, error)
	GetConversationInfo(input *slack.GetConversationInfoInput) (*slack.Channel, error)
	GetUserProfile(params *slack.GetUserProfileParameters) (*slack.UserProfile, error)
	GetUserInfo(userID string) (*slack.User, error)
	OpenView(triggerID string, view slack.ModalViewRequest) (*slack.ViewResponse, error)
	UpdateView(view slack.ModalViewRequest, externalID string, hash string, viewID string) (*slack.ViewResponse, error)
	BotUserId() string
}

type slackClientImpl struct {
	inner      *slack.Client
	tenantName string
	botUserId  string
}

func extractError(err error) string {
	if err == nil {
		return "200"
	}
	if slackErr, ok := err.(*slack.StatusCodeError); ok {
		return string(slackErr.Code)
	}
	return "unknown"
}

func (c *slackClientImpl) PostMessage(channelID string, options ...slack.MsgOption) (string, string, error) {
	a, b, err := c.inner.PostMessage(channelID, options...)
	registerSlackOps.WithLabelValues("post_message", c.tenantName, extractError(err)).Inc()
	return a, b, err
}

func (c *slackClientImpl) PostEphemeral(channelID, userID string, options ...slack.MsgOption) (string, error) {
	a, err := c.inner.PostEphemeral(channelID, userID, options...)
	registerSlackOps.WithLabelValues("post_ephemeral", c.tenantName, extractError(err)).Inc()
	return a, err
}

func (c *slackClientImpl) UpdateMessage(channelID, timestamp string, options ...slack.MsgOption) (string, string, string, error) {
	a, b, d, err := c.inner.UpdateMessage(channelID, timestamp, options...)
	registerSlackOps.WithLabelValues("update_message", c.tenantName, extractError(err)).Inc()
	return a, b, d, err
}

func (c *slackClientImpl) GetConversationReplies(parameters *slack.GetConversationRepliesParameters) ([]slack.Message, bool, string, error) {
	a, b, d, err := c.inner.GetConversationReplies(parameters)
	registerSlackOps.WithLabelValues("get_conversation_replies", c.tenantName, extractError(err)).Inc()
	return a, b, d, err
}

func (c *slackClientImpl) GetConversationHistory(parameters *slack.GetConversationHistoryParameters) (*slack.GetConversationHistoryResponse, error) {
	a, err := c.inner.GetConversationHistory(parameters)
	registerSlackOps.WithLabelValues("get_conversation_history", c.tenantName, extractError(err)).Inc()
	return a, err
}

func (c *slackClientImpl) AddReaction(name string, item slack.ItemRef) error {
	err := c.inner.AddReaction(name, item)
	registerSlackOps.WithLabelValues("add_reaction", c.tenantName, extractError(err)).Inc()
	return err
}

func (c *slackClientImpl) RemoveReaction(name string, item slack.ItemRef) error {
	err := c.inner.RemoveReaction(name, item)
	registerSlackOps.WithLabelValues("remove_reaction", c.tenantName, extractError(err)).Inc()
	return err
}

func (c *slackClientImpl) DeleteMessage(channelID, timestamp string) (string, string, error) {
	a, b, err := c.inner.DeleteMessage(channelID, timestamp)
	registerSlackOps.WithLabelValues("delete_message", c.tenantName, extractError(err)).Inc()
	return a, b, err
}

func (c *slackClientImpl) GetConversationInfo(input *slack.GetConversationInfoInput) (*slack.Channel, error) {
	a, err := c.inner.GetConversationInfo(input)
	registerSlackOps.WithLabelValues("get_conversation_info", c.tenantName, extractError(err)).Inc()
	return a, err
}

func (c *slackClientImpl) GetUserProfile(params *slack.GetUserProfileParameters) (*slack.UserProfile, error) {
	a, err := c.inner.GetUserProfile(params)
	registerSlackOps.WithLabelValues("get_user_profile", c.tenantName, extractError(err)).Inc()
	return a, err
}

func (c *slackClientImpl) BotUserId() string {
	return c.botUserId
}

func (c *slackClientImpl) GetUserInfo(userID string) (*slack.User, error) {
	user, err := c.inner.GetUserInfo(userID)
	registerSlackOps.WithLabelValues("get_user_info", c.tenantName, extractError(err)).Inc()
	return user, err
}

func (c *slackClientImpl) OpenView(triggerID string, view slack.ModalViewRequest) (*slack.ViewResponse, error) {
	a, err := c.inner.OpenView(triggerID, view)
	registerSlackOps.WithLabelValues("open_view", c.tenantName, extractError(err)).Inc()
	return a, err
}

func (c *slackClientImpl) UpdateView(view slack.ModalViewRequest, externalID string, hash string, viewID string) (*slack.ViewResponse, error) {
	a, err := c.inner.UpdateView(view, externalID, hash, viewID)
	registerSlackOps.WithLabelValues("update_view", c.tenantName, extractError(err)).Inc()
	return a, err
}

type SlackClientFactory interface {
	// Clients returned from here could expire at any time - do not expect to use forever
	GetSlackClient(ctx context.Context, tenantID string, tenantSettings *settingsproto.TenantSettings) (SlackClient, error)
	// Clients returned from here could expire at any time - do not expect to use forever
	GetSlackClientFromToken(tenantID string, tenantName string, token secretstring.SecretString, botUserId string) (SlackClient, error)
	ClearClientForTenant(tenantID string)
}

type SlackClientFactoryImpl struct {
	tenantLookup   TenantLookup
	customEndpoint string
	lock           sync.Mutex
	clients        map[string]SlackClient
}

func NewSlackClientFactory(tenantLookup TenantLookup, customEndpoint string) SlackClientFactory {
	return &SlackClientFactoryImpl{
		tenantLookup:   tenantLookup,
		customEndpoint: customEndpoint,
		clients:        make(map[string]SlackClient),
		lock:           sync.Mutex{},
	}
}

func (f *SlackClientFactoryImpl) GetSlackClient(ctx context.Context, tenantID string, tenantSettings *settingsproto.TenantSettings) (SlackClient, error) {
	f.lock.Lock()
	client, ok := f.clients[tenantID]
	f.lock.Unlock()
	if ok {
		return client, nil
	}
	tenantSlackInfo, err := f.tenantLookup.GetTenantSlackInfo(ctx, tenantSettings)
	if err != nil {
		return nil, err
	}
	tenantName, err := f.tenantLookup.GetTenantName(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	return f.GetSlackClientFromToken(tenantID, tenantName, tenantSlackInfo.token, tenantSlackInfo.botUserId)
}

// Used for the sending the initial welcome messsage to the installer when we already have the token
// Doesn't look for cached client since this should only be called during installation when we don't have a cached client
func (f *SlackClientFactoryImpl) GetSlackClientFromToken(tenantID string, tenantName string, token secretstring.SecretString, botUserId string) (SlackClient, error) {
	f.lock.Lock()
	defer f.lock.Unlock()
	var slackClient *slack.Client
	if f.customEndpoint != "" {
		slackClient = slack.New(token.Expose(), slack.OptionAPIURL(f.customEndpoint))
	} else {
		slackClient = slack.New(token.Expose())
	}

	client := &slackClientImpl{
		inner:      slackClient,
		tenantName: tenantName,
		botUserId:  botUserId,
	}
	f.clients[tenantID] = client
	return client, nil
}

// Removes the client from the cache for the given tenantID - used on uninstall
func (f *SlackClientFactoryImpl) ClearClientForTenant(tenantID string) {
	f.lock.Lock()
	defer f.lock.Unlock()
	delete(f.clients, tenantID)
}

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/clock"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/augmentcode/augment/services/api_proxy/model_finder_proto"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	gleanclient "github.com/augmentcode/augment/services/integrations/glean/client"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/augmentcode/augment/services/lib/pubsub"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	settingsclient "github.com/augmentcode/augment/services/settings/client"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.opentelemetry.io/otel"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var (
	configFile                    = flag.String("config", "", "Path to config file")
	requestInsightPublisherConfig = flag.String("request-insight-publisher-config-file", "", "Path to request insight publisher config file")
	secretsDir                    = flag.String("secret-directory", "", "Path to secrets")

	tracer = otel.Tracer("slack-bot-processor")
)

type Config struct {
	PubSubProjectID      string `json:"pubsub_project_id"`
	PubSubSubscriptionID string `json:"pubsub_subscription_id"`

	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	CentralServerMtls *tlsconfig.ServerConfig `json:"central_server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`
	TenantWatcherEndpoint string `json:"tenant_watcher_endpoint"`
	ModelFinderEndpoint   string `json:"model_finder_endpoint"`
	SettingsEndpoint      string `json:"settings_endpoint"`
	GithubStateEndpoint   string `json:"github_state_endpoint"`
	GleanEndpoint         string `json:"glean_endpoint"`
	BigtableProxyEndpoint string `json:"bigtable_proxy_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	Port int `json:"port"`

	SlackCallbackUrl string `json:"slack_callback_url"`
	// the path to the feature flag sdk key
	FeatureFlagsSdkKeyPath string `json:"feature_flags_sdk_key_path"`

	CustomSlackEndpoint string `json:"custom_slack_endpoint"`

	// the endpoint for the dynamic feature flags service or None if not used
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`

	SlashCommand string `json:"slash_command"`

	UserTier string `json:"user_tier"`
}

func loadConfig(path string, config *Config) error {
	f, err := os.Open(path)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)
	return nil
}

type SecretConfig struct {
	BasicCreds secretstring.SecretString
}

func loadSecretConfig(path string) (*SecretConfig, error) {
	basicCredsBytes, err := os.ReadFile(filepath.Join(path, "basic_creds"))
	if err != nil {
		return nil, fmt.Errorf("reading basic_creds: %w", err)
	}
	return &SecretConfig{
		BasicCreds: secretstring.New(string(basicCredsBytes)),
	}, nil
}

func newModelFinderClient(ctx context.Context, endpoint string, creds grpc.DialOption) (model_finder_proto.ModelFinderClient, *grpc.ClientConn, error) {
	opts := []grpc.DialOption{
		creds,
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, nil, err
	}
	return model_finder_proto.NewModelFinderClient(conn), conn, nil
}

// retryWithConstantBackoff attempts to subscribe to the pending task queue with a constant backoff.
// It will retry for the specified duration with the specified backoff interval between attempts.
func retryWithConstantBackoff(
	ctx context.Context,
	handler func(ctx context.Context) error,
	maxDuration time.Duration,
	backoffInterval time.Duration,
) error {
	endTime := time.Now().Add(maxDuration)

	for {
		// Check if we've exceeded the maximum retry duration
		if time.Now().After(endTime) {
			return fmt.Errorf("failed to check after retrying for %v", maxDuration)
		}

		// Attempt to subscribe
		err := handler(ctx)
		if err == nil {
			return nil // Success
		}

		// Log the error but continue retrying
		log.Warn().Err(err).Msgf("Error check, retrying in %v", backoffInterval)

		// Wait for the backoff interval or until the context is canceled
		select {
		case <-time.After(backoffInterval):
			// Continue with the next retry
		case <-ctx.Done():
			return ctx.Err() // Context was canceled
		}
	}
}

func run(config *Config, secretConfig *SecretConfig, settingsClient settingsclient.SettingsClient, webhookMappingResource webhookmapping.WebhookTenantMappingResource, grpcServer *grpc.Server, slackClientFactory SlackClientFactory, requestInsightPublisher ripublisher.RequestInsightPublisher, tenantLookup TenantLookup, tokenExchangeClient tokenexchangeclient.TokenExchangeClient, slackUserMapping SlackUserMapping) error {
	// Parse the UserTier string from config to the enum value
	userTierValue, exists := publicapiproto.GetModelsResponse_UserTier_value[config.UserTier]
	if !exists {
		log.Fatal().Msgf("Invalid user tier: %s", config.UserTier)
	}
	userTier := publicapiproto.GetModelsResponse_UserTier(userTierValue)

	slackProcessorServer := &SlackProcessorServer{
		httpClient:                   &http.Client{},
		settingsServiceClient:        settingsClient,
		basicCreds:                   secretConfig.BasicCreds,
		slackCallbackUrl:             config.SlackCallbackUrl,
		slackClientFactory:           slackClientFactory,
		webhookTenantMappingResource: webhookMappingResource,
		requestInsightPublisher:      requestInsightPublisher,
		tenantLookup:                 tenantLookup,
		tokenExchangeClient:          tokenExchangeClient,
		slackUserMapping:             slackUserMapping,
		userTier:                     userTier,
	}
	processorproto.RegisterSlackBotProcessorServer(grpcServer, slackProcessorServer)

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())
	return grpcServer.Serve(lis)
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()

	var config Config

	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	if err := loadConfig(*configFile, &config); err != nil {
		log.Fatal().Msgf("Failed to load app config: %v", err)
	}

	if *secretsDir == "" {
		log.Fatal().Msg("Missing secret token file")
	}

	secretConfig, err := loadSecretConfig(*secretsDir)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load secret config")
	}

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		log.Fatal().Msg("POD_NAMESPACE environment variable must be set.")
	}

	ctx := context.Background()
	pubSubClient, err := pubsub.NewSubscribeClient(ctx, &pubsub.SubscribeClientConfig{
		ProjectId:      config.PubSubProjectID,
		SubscriptionId: config.PubSubSubscriptionID,
	})
	if err != nil {
		log.Fatal().Msgf("Failed to create Pub/Sub client: %v", err)
	}
	defer pubSubClient.Close()

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	featureFlagHandle, err := featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, namespace)
	defer tenantCache.Close()

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	githubStateClient, err := githubstateclient.NewGithubStateClient(config.GithubStateEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating github state client")
		os.Exit(1)
	}
	defer githubStateClient.Close()

	settingsClient, err := settingsclient.NewSettingsClient(config.SettingsEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create settings client")
	}
	defer settingsClient.Close()

	tenantLookup := NewMultiTenantLookup(tenantCache, githubStateClient)

	modelFinderClient, conn, err := newModelFinderClient(ctx, config.ModelFinderEndpoint, grpc.WithTransportCredentials(clientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating model finder client")
		os.Exit(1)
	}
	defer conn.Close()

	slackClientFactory := NewSlackClientFactory(tenantLookup, config.CustomSlackEndpoint)

	chat := NewChatClient(ctx, featureFlagHandle, tenantLookup, modelFinderClient, clientCreds)
	defer chat.Close()

	bigtableProxyClient, err := bigtableproxy.NewBigtableProxyClient(config.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating bigtable proxy client")
	}

	slackUserMapping := NewSlackUserMapping(bigtableProxyClient)

	// Setup request insight publisher.
	requestInsightPublisher, err := ripublisher.NewRequestInsightPublisherFromFile(
		ctx, *requestInsightPublisherConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating request insight publisher")
	}
	defer requestInsightPublisher.Close()

	dynClient, err := webhookmapping.CreateDynamicClient()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create dynamic client")
	}

	gleanClient, err := gleanclient.NewGleanClient(config.GleanEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create glean client")
	}

	// TODO: support EU clusters here too, which will have to map to the US cluster
	webhookMappingResource := webhookmapping.NewWebhookTenantMappingResource(dynClient, namespace)

	slackHandler, err := NewSlackChatHandler(
		tenantCache, tokenExchangeClient, tenantLookup, slackClientFactory,
		chat, requestInsightPublisher, settingsClient, gleanClient,
		featureFlagHandle, clock.NewRealClock(), webhookMappingResource,
		slackUserMapping, config.SlashCommand,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating slack chat handler")
	}

	err = retryWithConstantBackoff(ctx, func(ctx context.Context) error {
		exists, err := pubSubClient.Exists(ctx)
		if err != nil {
			return err
		}
		if !exists {
			return fmt.Errorf("subscription does not exist")
		}
		return nil
	}, 90*time.Second, 15*time.Second)

	go func() {
		err = pubSubClient.Receive(ctx, slackHandler.HandleEvent)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to receive messages from subscription")
		}
	}()

	go func() {
		serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls, config.CentralServerMtls})
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating TLS config")
		}

		var opts []grpc.ServerOption
		opts = append(opts, grpc.Creds(serverTls))
		opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
		opts = append(opts, grpc.ChainUnaryInterceptor(
			recovery.UnaryServerInterceptor(),
			srvMetrics.UnaryServerInterceptor(),
		))
		opts = append(opts, grpc.ChainStreamInterceptor(
			recovery.StreamingServerInterceptor(),
			srvMetrics.StreamServerInterceptor(),
		))

		serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
		authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
		opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
		opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

		grpcServer := grpc.NewServer(opts...)
		// setup prometheus metrics for GRPC calls
		srvMetrics.InitializeMetrics(grpcServer)

		// setup reflection for debugging
		reflection.Register(grpcServer)
		// setup health service
		healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

		err = run(&config, secretConfig, settingsClient, webhookMappingResource, grpcServer, slackClientFactory, requestInsightPublisher, tenantLookup, tokenExchangeClient, slackUserMapping)
		if err != nil {
			log.Fatal().Err(err).Msg("Error serving")
		} else {
			log.Fatal().Msg("gRPC server closed")
		}
	}()

	select {}
}

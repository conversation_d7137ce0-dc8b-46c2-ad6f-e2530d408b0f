// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local slackSecretLib = import 'services/integrations/slack_bot/slack_secret_lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';
// the function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  // Only deploy slack bot processor in enterprise tier namespaces.
  // This is only used for the slackbot - an enterprise-only feature
  if namespace_config.flags.userTier != 'ENTERPRISE_TIER' then [] else
    local appName = 'slack-bot-processor';

    // mutual TLS is enabled if the namespace config has the forceMtls flag set
    // MTLS ensures that the client and server certificates are valid
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

    // create dynamic feature flag config
    local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);


    // creates a service account for the pod
    // a service account is needed to access GCP resources or kubernetes resources
    local serviceAccount = gcpLib.createServiceAccount(
      appName, env, cloud, namespace, iam=true, overridePrefix='slack-bot-p'
    );

    local slackSecret = slackSecretLib(env=env, namespace=namespace, cloud=cloud, appName=appName, secretsOverride=namespace_config.flags.slackSealedSecrets);

    local subscriber = pubsubLib.namespaceSubscriber(env=env,
                                                     namespace=namespace,
                                                     cloud=cloud,
                                                     appName=appName,
                                                     publisherAppName='slack-bot-webhook',
                                                     serviceAccount=serviceAccount,
                                                     spec={
                                                       retryPolicy: {
                                                         minimumBackoff: '5s',
                                                         maximumBackoff: '300s',
                                                       },
                                                       enableMessageOrdering: true,  // sequential ordering
                                                     });

    local requestInsightPublisher = publisherLib(cloud, env, namespace, appName);

    // creates a service for the pod, also create a global service if necessary
    // to allow the lead cluster (customer-ui) to talk to this pod. We only
    // deploy these when necessary, to avoid running into GCP limits
    local useGlobalService = !cloudInfo.isLeadCluster(cloud);
    local serverDnsNames = grpcLib.grpcServiceNames(appName);
    local centralServerDnsNames = grpcLib.grpcServiceNamespaceNames(appName, namespace=namespace) + if useGlobalService then [
      grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=appName, namespace=namespace),
    ] else [];
    local services = [
      grpcLib.grpcService(appName=appName, namespace=namespace),
    ] + if useGlobalService then [
      grpcLib.globalGrpcService(cloud=cloud, appName=appName, namespace=namespace),
    ] else [];

    // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
    // in the same namespace
    local clientCert = certLib.createClientCert(
      name='%s-client-cert' % appName,
      namespace=namespace,
      appName=appName,
      volumeName='client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );

    // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
    local centralClientCert = certLib.createCentralClientCert(
      name='%s-central-client-cert' % appName,
      namespace=namespace,
      env=env,
      appName=appName,
      volumeName='central-client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );

    // creates a server certificate for MTLS
    local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                                namespace=namespace,
                                                appName=appName,
                                                dnsNames=serverDnsNames,
                                                volumeName='certs');

    // creates a server certificate for central MTLS
    local centralServerCert = certLib.createCentralServerCert(name='%s-central-server-certificate' % appName,
                                                              namespace=namespace,
                                                              appName=appName,
                                                              dnsNames=centralServerDnsNames,
                                                              env=env,
                                                              volumeName='central-certs');

    local config = {
      port: 50051,
      prom_port: 9090,
      client_mtls: if mtls then clientCert.config else null,
      central_client_mtls: if mtls then centralClientCert.config else null,
      server_mtls: if mtls then serverCert.config else null,
      central_server_mtls: if mtls then centralServerCert.config else null,
      pubsub_project_id: cloudInfo[cloud].projectId,
      pubsub_subscription_id: subscriber.subscriptionName,
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      github_state_endpoint: 'github-state-svc:50051',
      model_finder_endpoint: 'model-finder-svc:50051',
      settings_endpoint: 'settings-svc:50051',
      glean_endpoint: 'glean-svc:50051',
      bigtable_proxy_endpoint: 'bigtable-proxy-svc:50051',
      slack_callback_url: 'https://%s/slackCallback' % endpointsLib.getCustomerUiHostname(env=env, namespace=namespace, cloud=cloud),
      custom_slack_endpoint: if namespace_config.flags.useFakeSlack then 'http://fake-slack-svc:80/' else '',
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
      slash_command: if env == 'STAGING' then '/staging-augment' else '/augment',
      user_tier: namespace_config.flags.userTier,
    };
    // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
    local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

    local roles = [
      {
        apiVersion: 'rbac.authorization.k8s.io/v1',
        kind: 'Role',
        metadata: {
          labels: {
            app: appName,
          },
          name: '%s-webhooktenantmapping-creator' % appName,
          namespace: namespace,
        },
        rules: [
          {
            apiGroups: ['eng.augmentcode.com'],
            resources: ['webhooktenantmappings'],
            verbs: ['*'],
          },
        ],
      },
      {
        apiVersion: 'rbac.authorization.k8s.io/v1',
        kind: 'RoleBinding',
        metadata: {
          labels: {
            app: appName,
          },
          name: '%s-webhookmapping-creator' % appName,
          namespace: namespace,
        },
        roleRef: {
          apiGroup: 'rbac.authorization.k8s.io',
          kind: 'Role',
          name: '%s-webhooktenantmapping-creator' % appName,
        },
        subjects: [
          {
            kind: 'ServiceAccount',
            name: serviceAccount.name,
            namespace: namespace,
          },
        ],
      },
    ];

    // creates a container that runs the server
    local container = {
      name: appName,
      target: {
        name: '//services/integrations/slack_bot/processor/server:image',
        dst: 'slack-bot-processor',
      },
      // the arguments that are passed to the server
      args: [
        '-config',
        configMap.filename,
        '-secret-directory',
        '/token',
        '--request-insight-publisher-config-file',
        requestInsightPublisher.configFilePath,
      ],
      ports: [
        {
          name: 'grpc-svc',
          containerPort: 50051,
        },
      ],
      // the environment variables that are passed to the server
      env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      // the volumes that are mounted into the pod
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        centralClientCert.volumeMountDef,
        serverCert.volumeMountDef,
        centralServerCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        {
          name: 'slack-bot-token',
          mountPath: '/token',
          readOnly: true,
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      // the resource limits are used to determine how much CPU and memory the pod can use
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
    local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
    local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
    // the pod is the kubernetes object that runs the container
    local pod = {
      // the service account is used to access GCP resources or kubernetes resources
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      priorityClassName: cloudInfo.envToPriorityClass(env),
      affinity: affinity,
      tolerations: tolerations,
      // the volumes are mounted into the pod
      volumes: [
        // the config map is mounted into the pod
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        centralClientCert.podVolumeDef,
        serverCert.podVolumeDef,
        centralServerCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
        {
          name: 'slack-bot-token',
          secret: {
            secretName: slackSecret.metadata.name,  // pragma: allowlist secret
            optional: false,
          },
        },
      ],
    };

    // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
    local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
    local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
    local deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        progressDeadlineSeconds: 60 * 10,  // waiting for IAM permissions to propagate
        // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
        minReadySeconds: if env == 'DEV' then 0 else 60,
        // the number of pods that are running at the same time
        replicas: if env == 'DEV' then 1 else 2,
        // the strategy is used to determine how the deployment is rolled out
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod + {
            tolerations: tolerations,
            affinity: affinity,
          },
        },
      },
    };

    lib.flatten([
      configMap.objects,
      serviceAccount.objects,
      roles,
      dynamicFeatureFlags.k8s_objects,
      deployment,
      services,
      subscriber.objects,
      slackSecret,
      clientCert.objects,
      centralClientCert.objects,
      serverCert.objects,
      centralServerCert.objects,
      requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
    ])

package main

import (
	"testing"

	"github.com/slack-go/slack"
	"github.com/stretchr/testify/assert"
)

func TestStreamingTextBlocks(t *testing.T) {
	st := NewStreamingTextBlocks(10)
	blocks := st.Update("hello")
	assert.Equal(t, []slack.Block{NewExpandedTextSectionBlock("hello")}, blocks)
	st.Success("hello", blocks)

	blocks = st.Update("hello world")
	assert.Equal(t, []slack.Block{NewExpandedTextSectionBlock("hello"), NewExpandedTextSectionBlock("world")}, blocks)
	st.Success("hello world", blocks)

	blocks = st.Update("hello world!!")
	assert.Equal(t, []slack.Block{NewExpandedTextSectionBlock("hello"), NewExpandedTextSectionBlock("world!!")}, blocks)
	st.Success("hello world!!", blocks)

	blocks = st.Update("hello world!!")
	assert.Equal(t, []slack.Block{NewExpandedTextSectionBlock("hello"), NewExpandedTextSectionBlock("world!!")}, blocks)
	st.Success("hello world!!", blocks)

	blocks = st.Update("hello world!!bye")
	assert.Equal(t, []slack.Block{NewExpandedTextSectionBlock("hello"), NewExpandedTextSectionBlock("world!!bye")}, blocks)
	old := st.Split("hello world!!bye")
	assert.Equal(t, []slack.Block{NewExpandedTextSectionBlock("hello"), NewExpandedTextSectionBlock("world!!")}, old)
	blocks = st.Update("hello world!!bye")
	assert.Equal(t, []slack.Block{NewExpandedTextSectionBlock("bye")}, blocks)
}

func TestNewExpandedTextBlocks(t *testing.T) {
	const maxLength = 10
	testCases := []struct {
		name     string
		input    string
		expected []slack.Block
	}{
		{
			name:     "empty",
			input:    "",
			expected: []slack.Block{},
		},
		{
			name:     "short",
			input:    "short",
			expected: []slack.Block{NewExpandedTextSectionBlock("short")},
		},
		{
			name:  "long",
			input: "too long for one",
			expected: []slack.Block{
				NewExpandedTextSectionBlock("too long"),
				NewExpandedTextSectionBlock("for one"),
			},
		},
		{
			name:  "long_word",
			input: "toolongforone",
			expected: []slack.Block{
				NewExpandedTextSectionBlock("toolongfor"),
				NewExpandedTextSectionBlock("one"),
			},
		},
		{
			name:  "long_with_newline",
			input: "longer\nthan ten",
			expected: []slack.Block{
				NewExpandedTextSectionBlock("longer"),
				NewExpandedTextSectionBlock("than ten"),
			},
		},
		{
			name:  "long_with_codeblock",
			input: "longer ```than ten chars```\n",
			expected: []slack.Block{
				NewExpandedTextSectionBlock("longer"),
				NewExpandedTextSectionBlock("```than```"),
				NewExpandedTextSectionBlock("```ten```"),
				NewExpandedTextSectionBlock("```char```"),
				NewExpandedTextSectionBlock("```s```"),
			},
		},
		{
			name:  "long_with_codeblock_and_newline",
			input: "```abc\nde\nf\ng\nverylongname```",
			expected: []slack.Block{
				NewExpandedTextSectionBlock("```abc```"),
				NewExpandedTextSectionBlock("```de\nf```"),
				NewExpandedTextSectionBlock("```g```"),
				NewExpandedTextSectionBlock("```very```"),
				NewExpandedTextSectionBlock("```long```"),
				NewExpandedTextSectionBlock("```name```"),
			},
		},
		{
			name:  "multiple_codeblocks",
			input: "hi ```a\nbc\nde\nf\ng\nh``` stuff```abc\nde\nf\ng``` bye",
			expected: []slack.Block{
				NewExpandedTextSectionBlock("hi"),
				NewExpandedTextSectionBlock("```a\nbc```"),
				NewExpandedTextSectionBlock("```de\nf```"),
				NewExpandedTextSectionBlock("```g\nh```"),
				NewExpandedTextSectionBlock("stuff"),
				NewExpandedTextSectionBlock("```abc```"),
				NewExpandedTextSectionBlock("```de\nf```"),
				NewExpandedTextSectionBlock("```g```"),
				NewExpandedTextSectionBlock("bye"),
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := newExpandedTextBlocksInner(tc.input, maxLength)
			assert.Equal(t, tc.expected, actual)
		})
	}
}

package main

import (
	"fmt"
	"strings"

	"github.com/slack-go/slack"
)

const (
	// The 3000 number is from
	// https://api.slack.com/reference/block-kit/composition-objects#text
	MAX_TEXT_BLOCK_OBJECT_LENGTH = 3000
)

// Same as SectionB<PERSON>, but with an extra `"expand": true` field. This is not
// in the go library yet. This is necessary because otherwise Slack might
// collapse larger text blocks, requiring the user to click "See More" to see
// the full message. This often looks like an incomplete response, and also
// looks weird with streamed responses.
// https://api.slack.com/reference/block-kit/blocks#section
type ExpandedTextSectionBlock struct {
	*slack.SectionBlock
	Expand bool `json:"expand"`
}

func NewExpandedTextSectionBlock(msg string) *ExpandedTextSectionBlock {
	return &ExpandedTextSectionBlock{
		SectionBlock: slack.NewSectionBlock(slack.NewTextBlockObject("mrkdwn", msg, false, false), nil, nil),
		Expand:       true,
	}
}

type StreamingTextBlocks struct {
	maxLength int

	// Stable prefix to ignore for any future requests
	completedPrefix string
	completedBlocks []slack.Block

	// last successful prefix and blocks
	lastPrefix string
	lastBlocks []slack.Block
}

// Split the message into blocks that can be sent to Slack. Each block can be up
// to MaxTextBlockObjectLength characters. We try to split on nice boundaries
// where possible, we try code blocks, newlines, and spaces in that order of
// priority.
func NewStreamingTextBlocks(maxLength int) *StreamingTextBlocks {
	return &StreamingTextBlocks{
		maxLength: maxLength,
	}
}

// Call with every update to the full message. For example we would call it with
// Update("hello"), then Update("hello world"), then Update("hello world!!!").
// After this, we should always call Success() or Split()
func (s *StreamingTextBlocks) Update(msg string) []slack.Block {
	msg = strings.TrimPrefix(msg, s.completedPrefix)
	return newExpandedTextBlocksInner(msg, s.maxLength)
}

// Call to let this class know that the last update was successful, and not to
// split the message.
func (s *StreamingTextBlocks) Success(msg string, blocks []slack.Block) {
	s.lastPrefix = msg
	s.lastBlocks = blocks
}

// Call this when the existing message (the last response from Update()) is too
// large, and we will split the current in-progress region into a stable set of
// blocks and an in-progress set of blocks. The returned stable set of blocks
// will be treated as completed and no longer show up in any future Update() or
// Split() calls.
// This is so complicated because we don't really know the limits of the slack
// API, so we have to dynamically split things up when we get an error.
func (s *StreamingTextBlocks) Split(msg string) []slack.Block {
	s.completedPrefix = s.lastPrefix
	s.completedBlocks = append(s.completedBlocks, s.lastBlocks...)
	return s.lastBlocks
}

// Useful for testing
func newExpandedTextBlocksInner(msg string, maxLength int) []slack.Block {
	chunks := splitTextIntoChunks(msg, maxLength)
	blocks := make([]slack.Block, len(chunks))
	for i, chunk := range chunks {
		blocks[i] = NewExpandedTextSectionBlock(chunk)
	}
	return blocks
}

func splitTextIntoChunks(text string, maxChunkSize int) []string {
	codeBlocks := strings.Split(text, "```")
	out := make([]string, 0, len(codeBlocks))
	for i, block := range codeBlocks {
		block = strings.TrimSpace(block)
		if block == "" {
			continue
		}

		// Odd indices are code blocks
		isCodeBlock := (i % 2) == 1
		codeBlockLen := 0
		if isCodeBlock {
			codeBlockLen = 6
		}

		if len(block)+codeBlockLen <= maxChunkSize {
			if isCodeBlock {
				block = fmt.Sprintf("```%s```", block)
			}
			out = append(out, block)
		} else {
			// The block is too long, split it by newline or space
			var delimiterBlocks []string
			if isCodeBlock {
				// If we are in a code block, give a smaller max length so we
				// can add backticks back later
				delimiterBlocks = splitByDelimiter(block, maxChunkSize-6, "\n")
				for i := range delimiterBlocks {
					delimiterBlocks[i] = fmt.Sprintf("```%s```", delimiterBlocks[i])
				}
			} else {
				delimiterBlocks = splitByDelimiter(block, maxChunkSize, "\n")
			}
			out = append(out, delimiterBlocks...)
		}
	}

	return out
}

func splitByDelimiter(text string, maxChunkSize int, delimiter string) []string {
	var chunks []string
	parts := strings.Split(text, delimiter)
	var currentChunk strings.Builder

	pushChunk := func() {
		if currentChunk.Len() == 0 {
			return
		}
		chunks = append(chunks, currentChunk.String())
		currentChunk.Reset()
	}

	for _, part := range parts {
		var partPrefix string
		if currentChunk.Len() > 0 {
			partPrefix = delimiter
		}

		if currentChunk.Len()+len(part)+len(partPrefix) <= maxChunkSize {
			// Add the part to our existing chunk, it's still under the limit
			currentChunk.WriteString(partPrefix)
			currentChunk.WriteString(part)
		} else {
			pushChunk()

			if len(part) < maxChunkSize {
				// Add the part to the next chunk
				currentChunk.WriteString(part)
			} else {
				// If the part itself is too long, recursively split it with a
				// more common delimiter. Our order is newline, space, then
				// finally split at any character
				if delimiter == "\n" {
					subChunks := splitByDelimiter(part, maxChunkSize, " ")
					chunks = append(chunks, subChunks...)
				} else {
					subChunks := splitBySize(part, maxChunkSize)
					chunks = append(chunks, subChunks...)
				}
			}
		}
	}

	pushChunk()
	return chunks
}

func splitBySize(text string, maxChunkSize int) []string {
	chunks := make([]string, 0, len(text)/maxChunkSize+1)
	for i := 0; i < len(text); i += maxChunkSize {
		end := min(i+maxChunkSize, len(text))
		chunks = append(chunks, text[i:end])
	}
	return chunks
}

load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/processor/client",
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//services/integrations/slack_bot/processor:processor_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

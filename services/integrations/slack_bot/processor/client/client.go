package client

import (
	"context"
	"fmt"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
)

type SlackProcessorClient interface {
	OpenRepoSelectModal(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.OpenRepoSelectModalRequest) (*pb.OpenRepoSelectModalResponse, error)
	HandleRepoSelection(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.HandleRepoSelectionRequest) (*pb.HandleRepoSelectionResponse, error)
	HandleSelectAllReposAction(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.HandleSelectAllReposActionRequest) (*pb.HandleSelectAllReposActionResponse, error)
	GetRepoOptions(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.GetRepoOptionsRequest) (*pb.GetRepoOptionsResponse, error)
	OpenFeedbackModal(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.OpenFeedbackModalRequest) (*pb.OpenFeedbackModalResponse, error)
	HandleFeedbackEvent(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.HandleFeedbackEventRequest) (*pb.HandleFeedbackEventResponse, error)
	Close() error
}

// SlackProcessorClient is a client for the SlackProcessor service
type slackProcessorClientImpl struct {
	endpoint string
	conn     *grpc.ClientConn
	client   pb.SlackBotProcessorClient
}

// NewSlackProcessorClient creates a new SlackProcessorClient
func NewSlackProcessorClient(endpoint string, creds credentials.TransportCredentials) (SlackProcessorClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("Failed to connect to slackbot processor service: %v", err)
	}

	client := pb.NewSlackBotProcessorClient(conn)
	return &slackProcessorClientImpl{
		endpoint: endpoint,
		conn:     conn,
		client:   client,
	}, nil
}

// OpenRepoSelectModal calls the OpenRepoSelectModal RPC
func (c *slackProcessorClientImpl) OpenRepoSelectModal(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.OpenRepoSelectModalRequest) (*pb.OpenRepoSelectModalResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.OpenRepoSelectModal(ctx, req)
}

// HandleRepoSelection calls the HandleRepoSelection RPC
func (c *slackProcessorClientImpl) HandleRepoSelection(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.HandleRepoSelectionRequest) (*pb.HandleRepoSelectionResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.HandleRepoSelection(ctx, req)
}

// HandleSelectAllReposAction calls the HandleSelectAllReposAction RPC
func (c *slackProcessorClientImpl) HandleSelectAllReposAction(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.HandleSelectAllReposActionRequest) (*pb.HandleSelectAllReposActionResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.HandleSelectAllReposAction(ctx, req)
}

// HandleGetAllRepos calls the HandleGetAllRepos RPC
func (c *slackProcessorClientImpl) GetRepoOptions(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.GetRepoOptionsRequest) (*pb.GetRepoOptionsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.GetRepoOptions(ctx, req)
}

// OpenFeedbackModal calls the OpenFeedbackModal RPC
func (c *slackProcessorClientImpl) OpenFeedbackModal(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.OpenFeedbackModalRequest) (*pb.OpenFeedbackModalResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.OpenFeedbackModal(ctx, req)
}

// HandleFeedbackEvent calls the HandleFeedbackEvent RPC
func (c *slackProcessorClientImpl) HandleFeedbackEvent(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.HandleFeedbackEventRequest) (*pb.HandleFeedbackEventResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.HandleFeedbackEvent(ctx, req)
}

// Close closes the client connection
func (c *slackProcessorClientImpl) Close() error {
	return c.conn.Close()
}

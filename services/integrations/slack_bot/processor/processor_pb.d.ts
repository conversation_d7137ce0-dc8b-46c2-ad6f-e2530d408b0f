// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/slack_bot/processor/processor.proto (package slackbot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type {
  BinaryReadOptions,
  FieldList,
  JsonReadOptions,
  JsonValue,
  PartialMessage,
  PlainMessage,
  Timestamp,
} from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { EventMetadata, SlackEvent } from "../slack_event_pb.js";
import type { RepoInformation } from "../../../settings/settings_pb.js";
import type { Document } from "../../glean/glean_pb.js";

/**
 * @generated from enum slackbot.OpenRepoSelectModalError
 */
export declare enum OpenRepoSelectModalError {
  /**
   * @generated from enum value: NO_ERROR = 0;
   */
  NO_ERROR = 0,

  /**
   * @generated from enum value: DEFAULT = 1;
   */
  DEFAULT = 1,

  /**
   * @generated from enum value: BOT_NOT_IN_CHANNEL = 2;
   */
  BOT_NOT_IN_CHANNEL = 2,

  /**
   * @generated from enum value: BOT_NOT_IN_DM = 3;
   */
  BOT_NOT_IN_DM = 3,

  /**
   * @generated from enum value: NO_GITHUB_APP = 4;
   */
  NO_GITHUB_APP = 4,

  /**
   * @generated from enum value: NO_REPOS = 5;
   */
  NO_REPOS = 5,
}

/**
 * @generated from message slackbot.HydrateSlackSettingsRequest
 */
export declare class HydrateSlackSettingsRequest extends Message<HydrateSlackSettingsRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  constructor(data?: PartialMessage<HydrateSlackSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HydrateSlackSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HydrateSlackSettingsRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HydrateSlackSettingsRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HydrateSlackSettingsRequest;

  static equals(
    a:
      | HydrateSlackSettingsRequest
      | PlainMessage<HydrateSlackSettingsRequest>
      | undefined,
    b:
      | HydrateSlackSettingsRequest
      | PlainMessage<HydrateSlackSettingsRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.HydrateSlackSettingsResponse
 */
export declare class HydrateSlackSettingsResponse extends Message<HydrateSlackSettingsResponse> {
  constructor(data?: PartialMessage<HydrateSlackSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HydrateSlackSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HydrateSlackSettingsResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HydrateSlackSettingsResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HydrateSlackSettingsResponse;

  static equals(
    a:
      | HydrateSlackSettingsResponse
      | PlainMessage<HydrateSlackSettingsResponse>
      | undefined,
    b:
      | HydrateSlackSettingsResponse
      | PlainMessage<HydrateSlackSettingsResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.LinkSlackUserToAugmentUserRequest
 */
export declare class LinkSlackUserToAugmentUserRequest extends Message<LinkSlackUserToAugmentUserRequest> {
  /**
   * @generated from field: string slack_user_id = 1;
   */
  slackUserId: string;

  constructor(data?: PartialMessage<LinkSlackUserToAugmentUserRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.LinkSlackUserToAugmentUserRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): LinkSlackUserToAugmentUserRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): LinkSlackUserToAugmentUserRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): LinkSlackUserToAugmentUserRequest;

  static equals(
    a:
      | LinkSlackUserToAugmentUserRequest
      | PlainMessage<LinkSlackUserToAugmentUserRequest>
      | undefined,
    b:
      | LinkSlackUserToAugmentUserRequest
      | PlainMessage<LinkSlackUserToAugmentUserRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.LinkSlackUserToAugmentUserResponse
 */
export declare class LinkSlackUserToAugmentUserResponse extends Message<LinkSlackUserToAugmentUserResponse> {
  constructor(data?: PartialMessage<LinkSlackUserToAugmentUserResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.LinkSlackUserToAugmentUserResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): LinkSlackUserToAugmentUserResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): LinkSlackUserToAugmentUserResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): LinkSlackUserToAugmentUserResponse;

  static equals(
    a:
      | LinkSlackUserToAugmentUserResponse
      | PlainMessage<LinkSlackUserToAugmentUserResponse>
      | undefined,
    b:
      | LinkSlackUserToAugmentUserResponse
      | PlainMessage<LinkSlackUserToAugmentUserResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.RepoSelectModalMetadata
 */
export declare class RepoSelectModalMetadata extends Message<RepoSelectModalMetadata> {
  /**
   * @generated from field: string channel = 1;
   */
  channel: string;

  /**
   * @generated from field: slackbot.SlackEvent slack_event_to_regenerate = 2;
   */
  slackEventToRegenerate?: SlackEvent;

  /**
   * @generated from field: string original_bot_message_timestamp = 3;
   */
  originalBotMessageTimestamp: string;

  constructor(data?: PartialMessage<RepoSelectModalMetadata>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.RepoSelectModalMetadata";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): RepoSelectModalMetadata;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): RepoSelectModalMetadata;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): RepoSelectModalMetadata;

  static equals(
    a:
      | RepoSelectModalMetadata
      | PlainMessage<RepoSelectModalMetadata>
      | undefined,
    b:
      | RepoSelectModalMetadata
      | PlainMessage<RepoSelectModalMetadata>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.OpenRepoSelectModalRequest
 */
export declare class OpenRepoSelectModalRequest extends Message<OpenRepoSelectModalRequest> {
  /**
   * @generated from field: slackbot.EventMetadata metadata = 1;
   */
  metadata?: EventMetadata;

  /**
   * @generated from field: string user = 2;
   */
  user: string;

  /**
   * @generated from field: string channel = 3;
   */
  channel: string;

  /**
   * @generated from field: string command = 4 [deprecated = true];
   * @deprecated
   */
  command: string;

  /**
   * @generated from field: string trigger_id = 5;
   */
  triggerId: string;

  /**
   * @generated from field: slackbot.SlackEvent slack_event_to_regenerate = 6;
   */
  slackEventToRegenerate?: SlackEvent;

  /**
   * @generated from field: string original_bot_message_timestamp = 7;
   */
  originalBotMessageTimestamp: string;

  constructor(data?: PartialMessage<OpenRepoSelectModalRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.OpenRepoSelectModalRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): OpenRepoSelectModalRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): OpenRepoSelectModalRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): OpenRepoSelectModalRequest;

  static equals(
    a:
      | OpenRepoSelectModalRequest
      | PlainMessage<OpenRepoSelectModalRequest>
      | undefined,
    b:
      | OpenRepoSelectModalRequest
      | PlainMessage<OpenRepoSelectModalRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.OpenRepoSelectModalResponse
 */
export declare class OpenRepoSelectModalResponse extends Message<OpenRepoSelectModalResponse> {
  /**
   * @generated from field: slackbot.OpenRepoSelectModalError error_type = 1;
   */
  errorType: OpenRepoSelectModalError;

  constructor(data?: PartialMessage<OpenRepoSelectModalResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.OpenRepoSelectModalResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): OpenRepoSelectModalResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): OpenRepoSelectModalResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): OpenRepoSelectModalResponse;

  static equals(
    a:
      | OpenRepoSelectModalResponse
      | PlainMessage<OpenRepoSelectModalResponse>
      | undefined,
    b:
      | OpenRepoSelectModalResponse
      | PlainMessage<OpenRepoSelectModalResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.HandleRepoSelectionRequest
 */
export declare class HandleRepoSelectionRequest extends Message<HandleRepoSelectionRequest> {
  /**
   * @generated from field: slackbot.EventMetadata metadata = 1;
   */
  metadata?: EventMetadata;

  /**
   * @generated from field: string user = 2;
   */
  user: string;

  /**
   * @generated from field: string channel = 3;
   */
  channel: string;

  /**
   * @generated from field: repeated string selected_repos = 4;
   */
  selectedRepos: string[];

  /**
   * @generated from field: string timestamp = 5;
   */
  timestamp: string;

  /**
   * @generated from field: bool select_all = 6;
   */
  selectAll: boolean;

  /**
   * @generated from field: string original_bot_message_timestamp = 7;
   */
  originalBotMessageTimestamp: string;

  constructor(data?: PartialMessage<HandleRepoSelectionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HandleRepoSelectionRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HandleRepoSelectionRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HandleRepoSelectionRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HandleRepoSelectionRequest;

  static equals(
    a:
      | HandleRepoSelectionRequest
      | PlainMessage<HandleRepoSelectionRequest>
      | undefined,
    b:
      | HandleRepoSelectionRequest
      | PlainMessage<HandleRepoSelectionRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.HandleSelectAllReposActionRequest
 */
export declare class HandleSelectAllReposActionRequest extends Message<HandleSelectAllReposActionRequest> {
  /**
   * @generated from field: slackbot.EventMetadata metadata = 1;
   */
  metadata?: EventMetadata;

  /**
   * @generated from field: string user = 2;
   */
  user: string;

  /**
   * @generated from field: string view_id = 3;
   */
  viewId: string;

  /**
   * @generated from field: string channel = 4;
   */
  channel: string;

  /**
   * @generated from field: bool is_select_all = 5;
   */
  isSelectAll: boolean;

  /**
   * @generated from field: slackbot.SlackEvent slack_event_to_regenerate = 6;
   */
  slackEventToRegenerate?: SlackEvent;

  /**
   * @generated from field: string original_bot_message_timestamp = 7;
   */
  originalBotMessageTimestamp: string;

  constructor(data?: PartialMessage<HandleSelectAllReposActionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HandleSelectAllReposActionRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HandleSelectAllReposActionRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HandleSelectAllReposActionRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HandleSelectAllReposActionRequest;

  static equals(
    a:
      | HandleSelectAllReposActionRequest
      | PlainMessage<HandleSelectAllReposActionRequest>
      | undefined,
    b:
      | HandleSelectAllReposActionRequest
      | PlainMessage<HandleSelectAllReposActionRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.HandleSelectAllReposActionResponse
 */
export declare class HandleSelectAllReposActionResponse extends Message<HandleSelectAllReposActionResponse> {
  constructor(data?: PartialMessage<HandleSelectAllReposActionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HandleSelectAllReposActionResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HandleSelectAllReposActionResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HandleSelectAllReposActionResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HandleSelectAllReposActionResponse;

  static equals(
    a:
      | HandleSelectAllReposActionResponse
      | PlainMessage<HandleSelectAllReposActionResponse>
      | undefined,
    b:
      | HandleSelectAllReposActionResponse
      | PlainMessage<HandleSelectAllReposActionResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.HandleRepoSelectionResponse
 */
export declare class HandleRepoSelectionResponse extends Message<HandleRepoSelectionResponse> {
  constructor(data?: PartialMessage<HandleRepoSelectionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HandleRepoSelectionResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HandleRepoSelectionResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HandleRepoSelectionResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HandleRepoSelectionResponse;

  static equals(
    a:
      | HandleRepoSelectionResponse
      | PlainMessage<HandleRepoSelectionResponse>
      | undefined,
    b:
      | HandleRepoSelectionResponse
      | PlainMessage<HandleRepoSelectionResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.GetRepoOptionsRequest
 */
export declare class GetRepoOptionsRequest extends Message<GetRepoOptionsRequest> {
  /**
   * @generated from field: slackbot.EventMetadata metadata = 1;
   */
  metadata?: EventMetadata;

  /**
   * @generated from field: string timestamp = 2;
   */
  timestamp: string;

  /**
   * @generated from field: string search_query = 3;
   */
  searchQuery: string;

  constructor(data?: PartialMessage<GetRepoOptionsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.GetRepoOptionsRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetRepoOptionsRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetRepoOptionsRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetRepoOptionsRequest;

  static equals(
    a: GetRepoOptionsRequest | PlainMessage<GetRepoOptionsRequest> | undefined,
    b: GetRepoOptionsRequest | PlainMessage<GetRepoOptionsRequest> | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.OptionBlockObject
 */
export declare class OptionBlockObject extends Message<OptionBlockObject> {
  /**
   * @generated from field: slackbot.OptionBlockObject.TextBlockObject text = 1;
   */
  text?: OptionBlockObject_TextBlockObject;

  /**
   * @generated from field: string value = 2;
   */
  value: string;

  /**
   * @generated from field: optional slackbot.OptionBlockObject.TextBlockObject description = 3;
   */
  description?: OptionBlockObject_TextBlockObject;

  constructor(data?: PartialMessage<OptionBlockObject>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.OptionBlockObject";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): OptionBlockObject;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): OptionBlockObject;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): OptionBlockObject;

  static equals(
    a: OptionBlockObject | PlainMessage<OptionBlockObject> | undefined,
    b: OptionBlockObject | PlainMessage<OptionBlockObject> | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.OptionBlockObject.TextBlockObject
 */
export declare class OptionBlockObject_TextBlockObject extends Message<OptionBlockObject_TextBlockObject> {
  /**
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * @generated from field: string text = 2;
   */
  text: string;

  constructor(data?: PartialMessage<OptionBlockObject_TextBlockObject>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.OptionBlockObject.TextBlockObject";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): OptionBlockObject_TextBlockObject;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): OptionBlockObject_TextBlockObject;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): OptionBlockObject_TextBlockObject;

  static equals(
    a:
      | OptionBlockObject_TextBlockObject
      | PlainMessage<OptionBlockObject_TextBlockObject>
      | undefined,
    b:
      | OptionBlockObject_TextBlockObject
      | PlainMessage<OptionBlockObject_TextBlockObject>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.GetRepoOptionsResponse
 */
export declare class GetRepoOptionsResponse extends Message<GetRepoOptionsResponse> {
  /**
   * @generated from field: repeated slackbot.OptionBlockObject options = 1;
   */
  options: OptionBlockObject[];

  constructor(data?: PartialMessage<GetRepoOptionsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.GetRepoOptionsResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetRepoOptionsResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetRepoOptionsResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetRepoOptionsResponse;

  static equals(
    a:
      | GetRepoOptionsResponse
      | PlainMessage<GetRepoOptionsResponse>
      | undefined,
    b:
      | GetRepoOptionsResponse
      | PlainMessage<GetRepoOptionsResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.OpenFeedbackModalRequest
 */
export declare class OpenFeedbackModalRequest extends Message<OpenFeedbackModalRequest> {
  /**
   * @generated from field: slackbot.EventMetadata metadata = 1;
   */
  metadata?: EventMetadata;

  /**
   * @generated from field: string user = 2;
   */
  user: string;

  /**
   * @generated from field: string trigger_id = 3;
   */
  triggerId: string;

  /**
   * @generated from field: slackbot.FeedbackModalMetadata modal_metadata = 4;
   */
  modalMetadata?: FeedbackModalMetadata;

  /**
   * @generated from field: string message_sender = 5;
   */
  messageSender: string;

  constructor(data?: PartialMessage<OpenFeedbackModalRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.OpenFeedbackModalRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): OpenFeedbackModalRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): OpenFeedbackModalRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): OpenFeedbackModalRequest;

  static equals(
    a:
      | OpenFeedbackModalRequest
      | PlainMessage<OpenFeedbackModalRequest>
      | undefined,
    b:
      | OpenFeedbackModalRequest
      | PlainMessage<OpenFeedbackModalRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.OpenFeedbackModalResponse
 */
export declare class OpenFeedbackModalResponse extends Message<OpenFeedbackModalResponse> {
  constructor(data?: PartialMessage<OpenFeedbackModalResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.OpenFeedbackModalResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): OpenFeedbackModalResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): OpenFeedbackModalResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): OpenFeedbackModalResponse;

  static equals(
    a:
      | OpenFeedbackModalResponse
      | PlainMessage<OpenFeedbackModalResponse>
      | undefined,
    b:
      | OpenFeedbackModalResponse
      | PlainMessage<OpenFeedbackModalResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.HandleFeedbackEventRequest
 */
export declare class HandleFeedbackEventRequest extends Message<HandleFeedbackEventRequest> {
  /**
   * @generated from field: slackbot.EventMetadata metadata = 1;
   */
  metadata?: EventMetadata;

  /**
   * @generated from field: string modal_private_metadata = 2;
   */
  modalPrivateMetadata: string;

  /**
   * @generated from field: string rating = 3;
   */
  rating: string;

  /**
   * @generated from field: string note = 4;
   */
  note: string;

  /**
   * @generated from field: string user = 5;
   */
  user: string;

  constructor(data?: PartialMessage<HandleFeedbackEventRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HandleFeedbackEventRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HandleFeedbackEventRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HandleFeedbackEventRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HandleFeedbackEventRequest;

  static equals(
    a:
      | HandleFeedbackEventRequest
      | PlainMessage<HandleFeedbackEventRequest>
      | undefined,
    b:
      | HandleFeedbackEventRequest
      | PlainMessage<HandleFeedbackEventRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.HandleFeedbackEventResponse
 */
export declare class HandleFeedbackEventResponse extends Message<HandleFeedbackEventResponse> {
  constructor(data?: PartialMessage<HandleFeedbackEventResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.HandleFeedbackEventResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HandleFeedbackEventResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HandleFeedbackEventResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HandleFeedbackEventResponse;

  static equals(
    a:
      | HandleFeedbackEventResponse
      | PlainMessage<HandleFeedbackEventResponse>
      | undefined,
    b:
      | HandleFeedbackEventResponse
      | PlainMessage<HandleFeedbackEventResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.FeedbackModalMetadata
 */
export declare class FeedbackModalMetadata extends Message<FeedbackModalMetadata> {
  /**
   * @generated from field: string channel = 1;
   */
  channel: string;

  /**
   * @generated from field: string message_timestamp = 2;
   */
  messageTimestamp: string;

  /**
   * @generated from field: string thread_timestamp = 3;
   */
  threadTimestamp: string;

  constructor(data?: PartialMessage<FeedbackModalMetadata>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.FeedbackModalMetadata";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): FeedbackModalMetadata;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): FeedbackModalMetadata;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): FeedbackModalMetadata;

  static equals(
    a: FeedbackModalMetadata | PlainMessage<FeedbackModalMetadata> | undefined,
    b: FeedbackModalMetadata | PlainMessage<FeedbackModalMetadata> | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.SlackbotChatMetadata
 */
export declare class SlackbotChatMetadata extends Message<SlackbotChatMetadata> {
  /**
   * @generated from field: string bot_id = 1;
   */
  botId: string;

  /**
   * @generated from field: string channel_name = 2;
   */
  channelName: string;

  /**
   * @generated from field: string conversation_history = 3;
   */
  conversationHistory: string;

  /**
   * @generated from field: repeated slackbot.Repo repos = 4;
   */
  repos: Repo[];

  /**
   * @generated from field: string user_timezone = 5 [deprecated = true];
   * @deprecated
   */
  userTimezone: string;

  /**
   * @generated from field: repeated settings.RepoInformation all_repos = 6;
   */
  allRepos: RepoInformation[];

  /**
   * @generated from field: repeated glean.Document glean_documents = 7;
   */
  gleanDocuments: Document[];

  /**
   * @generated from field: repeated string previous_thread_messages = 8;
   */
  previousThreadMessages: string[];

  constructor(data?: PartialMessage<SlackbotChatMetadata>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.SlackbotChatMetadata";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): SlackbotChatMetadata;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): SlackbotChatMetadata;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): SlackbotChatMetadata;

  static equals(
    a: SlackbotChatMetadata | PlainMessage<SlackbotChatMetadata> | undefined,
    b: SlackbotChatMetadata | PlainMessage<SlackbotChatMetadata> | undefined,
  ): boolean;
}

/**
 * @generated from message slackbot.Repo
 */
export declare class Repo extends Message<Repo> {
  /**
   * @generated from field: string repo_name = 1;
   */
  repoName: string;

  /**
   * @generated from field: string repo_owner = 2;
   */
  repoOwner: string;

  /**
   * @generated from field: string indexed_commit_sha = 3;
   */
  indexedCommitSha: string;

  /**
   * @generated from field: google.protobuf.Timestamp indexed_commit_time = 4;
   */
  indexedCommitTime?: Timestamp;

  /**
   * @generated from field: string current_commit_sha = 5;
   */
  currentCommitSha: string;

  /**
   * @generated from field: google.protobuf.Timestamp current_commit_time = 6;
   */
  currentCommitTime?: Timestamp;

  constructor(data?: PartialMessage<Repo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "slackbot.Repo";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): Repo;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): Repo;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): Repo;

  static equals(
    a: Repo | PlainMessage<Repo> | undefined,
    b: Repo | PlainMessage<Repo> | undefined,
  ): boolean;
}

// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/slack_bot/processor/processor.proto (package slackbot, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import {
  GetRepoOptionsRequest,
  GetRepoOptionsResponse,
  HandleFeedbackEventRequest,
  HandleFeedbackEventResponse,
  HandleRepoSelectionRequest,
  HandleRepoSelectionResponse,
  HandleSelectAllReposActionRequest,
  HandleSelectAllReposActionResponse,
  HydrateSlackSettingsRequest,
  HydrateSlackSettingsResponse,
  LinkSlackUserToAugmentUserRequest,
  LinkSlackUserToAugmentUserResponse,
  OpenFeedbackModalRequest,
  OpenFeedbackModalResponse,
  OpenRepoSelectModalRequest,
  OpenRepoSelectModalResponse,
} from "./processor_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service slackbot.SlackBotProcessor
 */
export declare const SlackBotProcessor: {
  readonly typeName: "slackbot.SlackBotProcessor";
  readonly methods: {
    /**
     * @generated from rpc slackbot.SlackBotProcessor.HydrateSlackSettings
     */
    readonly hydrateSlackSettings: {
      readonly name: "HydrateSlackSettings";
      readonly I: typeof HydrateSlackSettingsRequest;
      readonly O: typeof HydrateSlackSettingsResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc slackbot.SlackBotProcessor.LinkSlackUserToAugmentUser
     */
    readonly linkSlackUserToAugmentUser: {
      readonly name: "LinkSlackUserToAugmentUser";
      readonly I: typeof LinkSlackUserToAugmentUserRequest;
      readonly O: typeof LinkSlackUserToAugmentUserResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc slackbot.SlackBotProcessor.OpenRepoSelectModal
     */
    readonly openRepoSelectModal: {
      readonly name: "OpenRepoSelectModal";
      readonly I: typeof OpenRepoSelectModalRequest;
      readonly O: typeof OpenRepoSelectModalResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc slackbot.SlackBotProcessor.HandleRepoSelection
     */
    readonly handleRepoSelection: {
      readonly name: "HandleRepoSelection";
      readonly I: typeof HandleRepoSelectionRequest;
      readonly O: typeof HandleRepoSelectionResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc slackbot.SlackBotProcessor.HandleSelectAllReposAction
     */
    readonly handleSelectAllReposAction: {
      readonly name: "HandleSelectAllReposAction";
      readonly I: typeof HandleSelectAllReposActionRequest;
      readonly O: typeof HandleSelectAllReposActionResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc slackbot.SlackBotProcessor.GetRepoOptions
     */
    readonly getRepoOptions: {
      readonly name: "GetRepoOptions";
      readonly I: typeof GetRepoOptionsRequest;
      readonly O: typeof GetRepoOptionsResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc slackbot.SlackBotProcessor.OpenFeedbackModal
     */
    readonly openFeedbackModal: {
      readonly name: "OpenFeedbackModal";
      readonly I: typeof OpenFeedbackModalRequest;
      readonly O: typeof OpenFeedbackModalResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc slackbot.SlackBotProcessor.HandleFeedbackEvent
     */
    readonly handleFeedbackEvent: {
      readonly name: "HandleFeedbackEvent";
      readonly I: typeof HandleFeedbackEventRequest;
      readonly O: typeof HandleFeedbackEventResponse;
      readonly kind: MethodKind.Unary;
    };
  };
};

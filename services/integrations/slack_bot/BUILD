load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_library", "go_proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_proto_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "slack_event_proto",
    srcs = ["slack_event.proto"],
    visibility = ["//services:__subpackages__"],
)

go_library(
    name = "common",
    srcs = [
        "slack_constants.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/common",
    visibility = ["//services/integrations/slack_bot:__subpackages__"],
    deps = [
        "@org_golang_google_protobuf//proto",
    ],
)

go_proto_library(
    name = "slack_event_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/event_proto",
    proto = ":slack_event_proto",
    visibility = ["//services:__subpackages__"],
)

py_proto_library(
    name = "slack_event_py_proto",
    protos = [":slack_event_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

ts_proto_library(
    name = "slack_event_ts_proto",
    node_modules = "//:node_modules",
    proto = ":slack_event_proto",
    visibility = ["//services:__subpackages__"],
)

kubecfg_library(
    name = "slack_secret_lib",
    srcs = ["slack_secret_lib.jsonnet"],
    visibility = ["//services/integrations/slack_bot:__subpackages__"],
)

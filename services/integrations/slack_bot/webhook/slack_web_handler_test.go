package main

import (
	"bytes"
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/slackevents"
	"github.com/stretchr/testify/assert"
)

type MockSlackEventHandler struct {
	LastEvent *slackevents.EventsAPIInnerEvent
}

func (m *MockSlackEventHandler) HandleEvent(ctx context.Context, teamID, enterpriseID string, event *slackevents.EventsAPIInnerEvent) error {
	m.LastEvent = event
	return nil
}

func (m *MockSlackEventHandler) HandleOpenRepoSelection(ctx context.Context, teamID, enterpriseID, user, channel, triggerId, value, messageTs string, threadTs string) string {
	return ""
}

func (m *MockSlackEventHandler) HandleRepoSelectionEvent(ctx context.Context, teamID, enterpriseID string, event *slack.InteractionCallback, selectedRepos []string, selectAll bool) error {
	return nil
}

func (m *MockSlackEventHandler) HandleGetAllReposEvent(ctx context.Context, teamID, enterpriseID string, event *slack.InteractionCallback) (*processorproto.GetRepoOptionsResponse, error) {
	return nil, nil
}

func (m *MockSlackEventHandler) HandleSelectAllReposEvent(ctx context.Context, teamID, enterpriseID string, event *slack.InteractionCallback, isSelectAll bool) error {
	return nil
}

func (m *MockSlackEventHandler) HandleOpenFeedbackModalEvent(ctx context.Context, teamId string, enterpriseId string, event *slack.InteractionCallback, feedbackMetadata *processorproto.FeedbackModalMetadata) error {
	return nil
}

func (m *MockSlackEventHandler) HandleFeedbackEvent(ctx context.Context, teamID, enterpriseID string, event *slack.InteractionCallback, rating, note string) error {
	return nil
}

func TestSlackWebHandler_VerifyUrl(t *testing.T) {
	event := `{
				"type": "url_verification",
				"challenge": "3eZbrw1aBm2rZgRNFdxV2595E9CY3gmdALWMmHkvFXO7tYXAYLfzG0"
		}
	`

	req, err := http.NewRequest("POST", "/slack/events", bytes.NewBuffer([]byte(event)))
	if err != nil {
		t.Fatal(err)
	}

	eventHandler := &MockSlackEventHandler{}

	// Create a test SlackWebHandler
	signingSecret := secretstring.New("********************************************")
	handler := NewSlackWebHandler(eventHandler, signingSecret, true)

	// Test HandleEvent
	w := httptest.NewRecorder()
	handler.HandleEvent(w, req)

	// Check that the response was successful
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "3eZbrw1aBm2rZgRNFdxV2595E9CY3gmdALWMmHkvFXO7tYXAYLfzG0", w.Body.String())
	assert.Nil(t, eventHandler.LastEvent)
}

func TestSlackWebHandler_HandleEvent(t *testing.T) {
	event := `
			{
				"token": "XXYYZZ",
				"team_id": "TXXXXXXXX",
				"api_app_id": "AXXXXXXXXX",
				"event": {
								"type": "app_mention",
								"event_ts": "1234567890.123456",
								"user": "UXXXXXXX1"
				},
				"type": "event_callback",
				"authed_users": [ "UXXXXXXX1" ],
				"event_id": "Ev08MFMKH6",
				"event_time": 1234567890
		}
	`

	req, err := http.NewRequest("POST", "/slack/events", bytes.NewBuffer([]byte(event)))
	if err != nil {
		t.Fatal(err)
	}

	eventHandler := &MockSlackEventHandler{}

	// Create a test SlackWebHandler
	signingSecret := secretstring.New("********************************************")
	handler := NewSlackWebHandler(eventHandler, signingSecret, true)

	// Test HandleEvent
	w := httptest.NewRecorder()
	handler.HandleEvent(w, req)

	// Check that the response was successful
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "app_mention", eventHandler.LastEvent.Type)
}

func TestSlackWebHandler_HandleEvent_InvalidRequest(t *testing.T) {
	// Create a test request with an invalid Slack event
	req, err := http.NewRequest("POST", "/slack/events", bytes.NewBuffer([]byte("Invalid event")))
	if err != nil {
		t.Fatal(err)
	}

	// Create a test SlackWebHandler
	signingSecret := secretstring.New("********************************************")
	handler := NewSlackWebHandler(nil, signingSecret, true)

	// Test HandleEvent
	w := httptest.NewRecorder()
	handler.HandleEvent(w, req)

	// Check that the response was an error
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestSlackWebHandler_HandleEvent_Unauthorized(t *testing.T) {
	event := `
			{
				"token": "XXYYZZ",
				"team_id": "TXXXXXXXX",
				"api_app_id": "AXXXXXXXXX",
				"event": {
								"type": "app_mention",
								"event_ts": "1234567890.123456",
								"user": "UXXXXXXX1"
				},
				"type": "event_callback",
				"event_id": "Ev08MFMKH6",
				"event_time": 1234567890
		}
	`

	req, err := http.NewRequest("POST", "/slack/events", bytes.NewBuffer([]byte(event)))
	req.Header.Set("X-Slack-Signature", "5d115548a76e338023297ca0285c99dc")
	req.Header.Set("X-Slack-Request-Timestamp", "1234567890")
	if err != nil {
		t.Fatal(err)
	}

	if err != nil {
		t.Fatal(err)
	}

	eventHandler := &MockSlackEventHandler{}

	// Create a test SlackWebHandler
	signingSecret := secretstring.New("5d115548a76e338023297ca0285c99dd")
	handler := NewSlackWebHandler(eventHandler, signingSecret, false)

	// Test HandleEvent
	w := httptest.NewRecorder()
	handler.HandleEvent(w, req)

	// Check that the response was unauthorized
	assert.Equal(t, http.StatusUnauthorized, w.Code)

	assert.Nil(t, eventHandler.LastEvent)
}

// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local slackSecretLib = import 'services/integrations/slack_bot/slack_secret_lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  // This is a global service. Right now the only integration is github, which
  // only allows specifying one webhook per app, so we have one global webhook
  // handler.
  assert cloudInfo.isLeadCluster(cloud);

  local appName = 'slack-bot-webhook';

  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix='slack-bot-w'
  );

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local ingressHostname = {
    PROD: 'slack-webhook.%s' % domainSuffix,
    STAGING: 'slack-webhook.staging.%s' % domainSuffix,
    DEV: 'slack-webhook.%s.%s' % [namespace, domainSuffix],
  }[env];
  local ingressFacingCert = certLib.createPublicServerCert(name='slack-webhook-public-cert',
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           volumeName='https-certs',
                                                           env=env);

  local backendConfig = gcpLib.createBackendConfig(
    app=appName,
    cloud=cloud,
    namespace=namespace,
    healthCheck={
      checkIntervalSec: 15,
      port: 5000,
      type: 'HTTPS',
      requestPath: '/health',
    }
  );
  local httpService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: '%s-svc' % appName,
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'public-https': 'HTTPS' }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'public-https',
          targetPort: 'public-https',
        },
      ],
    },
  };


  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: '%s-ingress' % appName,
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: '%s-ssl-cert' % appName,  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: httpService.metadata.name,
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  // creates a client certificate so that the pod can authenticate to grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName + '-grpc', namespace=namespace)
  );

  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName + '-grpc'),
                                              volumeName='certs');


  local slackSecret = slackSecretLib(env=env, namespace=namespace, cloud=cloud, appName=appName, secretsOverride=namespace_config.flags.slackSealedSecrets);

  local publisherTopicTemplate = pubsubLib.publisherTopicTemplate(publisherAppName=appName);

  local roleBinding = if env == 'DEV' then {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-role-binding' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'User',
        name: serviceAccount.serviceAccountGcpEmailAddress,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'webhooktenantmapping-reader',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  } else {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRoleBinding',
    metadata: {
      name: '%s-%s-cluster-role-binding' % [appName, std.asciiLower(env)],
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'User',
        name: serviceAccount.serviceAccountGcpEmailAddress,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'webhooktenantmapping-reader',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  };

  local services = grpcLib.grpcService(appName=appName, namespace=namespace, serviceBaseName=appName + '-grpc');

  local config = {
    web_port: 5000,
    grpc_port: if env == 'DEV' then 50051 else 0,
    prom_port: 9090,
    pubsub_project_id: cloudInfo[cloud].projectId,
    topic_template: publisherTopicTemplate,
    https_server_key: '/https-certs/tls.key',
    https_server_cert: '/https-certs/tls.crt',
    server_mtls: if mtls then serverCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    slack_bot_processor_endpoint_template: 'slack-bot-processor%s:50051',
    namespace: if env == 'DEV' then namespace else null,
    kube_contexts: if env == 'DEV' then [cloudInfo.GCP_US_CENTRAL1_DEV.context] else [cloudInfo.GCP_US_CENTRAL1_PROD.context, cloudInfo.GCP_EU_WEST4_PROD.context],
    current_cloud: '%s' % cloud,
    cloud_domain_suffixes: std.manifestJson({
      [c]: cloudInfo[c].internalDomainSuffix
      for c in std.objectFields(cloudInfo)
      if cloudInfo.getLeadClusterForCluster(c) == cloud
    }),
  };
  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // creates a container that runs the server
  local container = {
    name: appName,
    target: {
      name: '//services/integrations/slack_bot/webhook:image',
      dst: 'slack-bot-webhook',
    },
    // the arguments that are passed to the server
    args: [
      '-config',
      configMap.filename,
      '-secret-directory',
      '/token',
    ],
    // ports that the pod exposes
    ports: [
      {
        containerPort: 5000,
        name: 'public-https',
      },
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    readinessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 5000,
      },
      initialDelaySeconds: 5,
      periodSeconds: 10,
    },
    livenessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 5000,
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    // the volumes that are mounted into the pod
    volumeMounts: lib.flatten([
      configMap.volumeMountDef,
      ingressFacingCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      serverCert.volumeMountDef,
      {
        name: 'slack-bot-token',
        mountPath: '/token',
        readOnly: true,
      },
    ]),
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: lib.flatten([
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      ingressFacingCert.podVolumeDef,
      centralClientCert.podVolumeDef,
      serverCert.podVolumeDef,
      {
        name: 'slack-bot-token',
        secret: {
          secretName: slackSecret.metadata.name,  // pragma: allowlist secret
          optional: false,
        },
      },
    ]),
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    deployment,
    slackSecret,
    ingressFacingCert.objects,
    if namespace_config.flags.loadBalancerType == 'ingress' then ingressObjects else [],
    httpService,
    centralClientCert.objects,
    serverCert.objects,
    roleBinding,
    services,
  ])

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'slack-webhook',
      kubecfg: {
        target: '//services/integrations/slack_bot/webhook:kubecfg',
        task: [
          t
          for t in cloudInfo.centralNamespaces
          if cloudInfo.isLeadProdCluster(t.cloud)
        ],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'slack-webhook-pubsub',
      kubecfg: {
        target: '//services/integrations/slack_bot/webhook:kubecfg_pubsub',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'slack-webhook-cross-cloud-roles',
      kubecfg: {
        target: '//services/integrations/slack_bot/webhook:kubecfg_cross_cloud_roles',
        task: [t for t in cloudInfo.centralNamespaces],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['aswin'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

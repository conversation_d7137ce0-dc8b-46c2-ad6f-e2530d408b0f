load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_binary", "go_grpc_library", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "slack_webhook_proto",
    srcs = ["slack_webhook.proto"],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//services/integrations/slack_bot:slack_event_proto",
    ],
)

py_grpc_library(
    name = "slack_webhook_py_proto",
    protos = [":slack_webhook_proto"],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//services/integrations/slack_bot:slack_event_py_proto",
    ],
)

go_grpc_library(
    name = "slack_webhook_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/webhook/proto",
    proto = ":slack_webhook_proto",
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//services/integrations/slack_bot:slack_event_go_proto",
    ],
)

go_library(
    name = "slack_bot_handler_lib",
    srcs = [
        "main.go",
        "slack_handler.go",
        "slack_web_handler.go",
        "tenant_lookup.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/slack_bot/webhook",
    deps = [
        ":slack_webhook_go_proto",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/integrations/slack_bot:common",
        "//services/integrations/slack_bot:slack_event_go_proto",
        "//services/integrations/slack_bot/processor:processor_go_proto",
        "//services/integrations/slack_bot/processor/client:client_go",
        "//services/integrations/webhookmapping",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_gorilla_mux//:mux",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_github_slack_go_slack//:slack",
        "@com_github_slack_go_slack//slackevents",
        "@io_k8s_client_go//dynamic",
        "@io_opentelemetry_go_contrib_instrumentation_github_com_gorilla_mux_otelmux//:otelmux",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "slack_bot_handler_test",
    srcs = [
        "slack_handler_test.go",
        "slack_web_handler_test.go",
    ],
    embed = [":slack_bot_handler_lib"],
    deps = [
        "//services/integrations/slack_bot/processor/client:client_go",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
    ],
)

go_binary(
    name = "slack_bot_handler",
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    embed = [":slack_bot_handler_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":slack_bot_handler",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/integrations/slack_bot:slack_secret_lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

kubecfg(
    name = "kubecfg_pubsub",
    src = "deploy_pubsub.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/gcp:gcp-lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

kubecfg(
    name = "kubecfg_cross_cloud_roles",
    src = "deploy_cross_cloud_roles.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/gcp:gcp-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_cross_cloud_roles",
        ":kubecfg_pubsub",
        "//deploy/common:cloud_info",
        "//deploy/tenants:namespaces",
    ],
)

package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/services/integrations/slack_bot/common"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
	"github.com/slack-go/slack"
	"github.com/slack-go/slack/slackevents"
)

var (
	eventWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "slack_webhook_request_total",
		Help: "Counter of handled Slack webhook requests",
	}, []string{"status_code"})

	commandWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "slack_command_webhook_request_total",
		Help: "Counter of handled Slack slash command webhook requests",
	}, []string{"status_code"})

	interactivityWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "slack_interactivity_webhook_request_total",
		Help: "Counter of handled Slack interactivity webhook requests",
	}, []string{"status_code"})

	getAllReposWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "slack_get_all_repos_webhook_request_total",
		Help: "Counter of handled Slack get all repos webhook requests",
	}, []string{"status_code"})
)

const (
	HELP_MESSAGE = ":wave: Need some help with `%[1]s`?\n\nAsk me about the product, code, architecture, debugging, or more by mentioning me in a channel or DM me directly.\n\nUse `%[1]s repo-select` to select the repositories you want me to access.\n\nNeed more help? Visit our <https://docs.augmentcode.com/using-augment/slack|documentation>."
)

type SlackWebHandler struct {
	eventHandler  SlackEventHandler
	signingSecret secretstring.SecretString

	skipVerify bool
}

func NewSlackWebHandler(eventHandler SlackEventHandler, signingSecret secretstring.SecretString, skipVerify bool) *SlackWebHandler {
	return &SlackWebHandler{
		eventHandler:  eventHandler,
		signingSecret: signingSecret,
		skipVerify:    skipVerify,
	}
}

// VerifyRequest verifies that the incoming request is from Slack
func (h *SlackWebHandler) VerifyRequest(r *http.Request) ([]byte, error) {
	if h.skipVerify {
		// Read the body
		body, err := io.ReadAll(r.Body)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read request body")
			return nil, err
		}
		r.Body = io.NopCloser(bytes.NewBuffer(body))

		return body, nil
	}
	log.Info().Msg("Verifying request")

	// Create a SecretsVerifier
	v, err := slack.NewSecretsVerifier(r.Header, h.signingSecret.Expose())
	if err != nil {
		return nil, err
	}

	// Read the body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read request body")
		return nil, err
	}
	r.Body = io.NopCloser(bytes.NewBuffer(body))

	v.Write(body)
	err = v.Ensure()
	if err != nil {
		log.Error().Err(err).Msg("Failed to verify request")
		return nil, err
	}
	return body, nil
}

func (h *SlackWebHandler) handleEventHelper(w http.ResponseWriter, r *http.Request) (statusCode int) {
	defer func() {
		eventWebhookRequestCounter.WithLabelValues(strconv.Itoa(statusCode)).Inc()
	}()

	log.Info().Msg("Received Slack event")
	body, err := h.VerifyRequest(r)
	if err != nil {
		log.Error().Err(err).Msg("Failed to verify request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return http.StatusUnauthorized
	}
	eventsAPIEvent, err := slackevents.ParseEvent(json.RawMessage(body), slackevents.OptionNoVerifyToken())
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse Slack event")
		w.WriteHeader(http.StatusInternalServerError)
		return http.StatusInternalServerError
	}
	log.Info().Msgf("Received Slack event: %s", eventsAPIEvent.Type)

	if eventsAPIEvent.Type == slackevents.URLVerification {
		var r *slackevents.ChallengeResponse
		err := json.Unmarshal([]byte(body), &r)
		if err != nil {
			log.Error().Err(err).Msg("Failed to unmarshal challenge response")
			w.WriteHeader(http.StatusInternalServerError)
			return http.StatusInternalServerError
		}
		w.Header().Set("Content-Type", "text")
		w.Write([]byte(r.Challenge))
		return http.StatusOK
	} else if eventsAPIEvent.Type == slackevents.CallbackEvent {
		innerEvent := eventsAPIEvent.InnerEvent
		log.Info().Msgf("Received event: %s", innerEvent.Type)
		ctx := r.Context()
		err := h.eventHandler.HandleEvent(ctx, eventsAPIEvent.TeamID,
			eventsAPIEvent.EnterpriseID, &innerEvent)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle event")
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return http.StatusInternalServerError
		}
		w.WriteHeader(http.StatusOK)
		return http.StatusOK
	} else {
		log.Warn().Msgf("Unexpected event type: %s", eventsAPIEvent.Type)
		w.WriteHeader(http.StatusNotImplemented)
		return http.StatusNotImplemented
	}
}

func (h *SlackWebHandler) HandleEvent(w http.ResponseWriter, r *http.Request) {
	h.handleEventHelper(w, r)
}

func (h *SlackWebHandler) handleCommandHelper(w http.ResponseWriter, r *http.Request) (statusCode int) {
	defer func() {
		commandWebhookRequestCounter.WithLabelValues(strconv.Itoa(statusCode)).Inc()
	}()

	log.Info().Msg("Received Slack command event")
	_, err := h.VerifyRequest(r)
	if err != nil {
		log.Error().Err(err).Msg("Failed to verify request")
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return http.StatusUnauthorized
	}

	err = r.ParseForm()
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse form")
		w.WriteHeader(http.StatusBadRequest)
		return http.StatusBadRequest
	}

	// This may or may not exist - thread_ts field does not exist in the go-slack library because we are specially allowlisted for commands in threads
	threadTs := r.Form.Get("thread_ts")

	slashCommand, err := slack.SlashCommandParse(r)
	log.Info().Msgf("Received Slack command: %+v", slashCommand)
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse Slack command")
		w.WriteHeader(http.StatusInternalServerError)
		return http.StatusInternalServerError
	}

	if strings.HasSuffix(slashCommand.Command, common.COMMAND_SUFFIX) {
		ctx := r.Context()
		if strings.Contains(slashCommand.Text, common.REPO_SELECT_COMMAND_TEXT) {
			log.Info().Msgf("Received repo select command")
			errMsg := h.eventHandler.HandleOpenRepoSelection(ctx, slashCommand.TeamID, slashCommand.EnterpriseID, slashCommand.UserID, slashCommand.ChannelID, slashCommand.TriggerID, "", "", threadTs)
			if errMsg != "" {
				errorMsg := map[string]interface{}{
					"text":             errMsg,
					"response_type":    "ephemeral",
					"replace_original": true,
				}
				responseBytes, _ := json.Marshal(errorMsg)
				_, err := http.Post(slashCommand.ResponseURL, "application/json", bytes.NewBuffer(responseBytes))
				if err != nil {
					log.Error().Err(err).Msg("Failed to send error response message")
				}
			}
		} else {
			log.Info().Msgf("Received command text: %s, sending help message", slashCommand.Text)
			msg := map[string]interface{}{
				"text":          fmt.Sprintf(HELP_MESSAGE, slashCommand.Command),
				"response_type": "ephemeral",
			}
			if threadTs != "" {
				msg["thread_ts"] = threadTs
			}
			responseBytes, _ := json.Marshal(msg)
			_, err := http.Post(slashCommand.ResponseURL, "application/json", bytes.NewBuffer(responseBytes))
			if err != nil {
				log.Error().Err(err).Msg("Failed to send help message")
			}
		}
		// Slack expects a success status always to acknowledge the message was received
		return http.StatusOK

	} else {
		log.Warn().Msgf("Unexpected command: %s", slashCommand.Command)
		w.WriteHeader(http.StatusNotImplemented)
		return http.StatusNotImplemented
	}
}

func (h *SlackWebHandler) HandleCommand(w http.ResponseWriter, r *http.Request) {
	h.handleCommandHelper(w, r)
}

func (h *SlackWebHandler) handleInteractivityHelper(w http.ResponseWriter, r *http.Request) (statusCode int) {
	defer func() {
		interactivityWebhookRequestCounter.WithLabelValues(strconv.Itoa(statusCode)).Inc()
	}()

	log.Info().Msg("Received Slack interactivity event")

	interactivityEvent, err := h.parseSlackInteractivityEvent(r)
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse slack event")
		http.Error(w, err.Error(), http.StatusBadRequest)
		return http.StatusBadRequest
	}

	ctx := r.Context()

	if interactivityEvent.Type == slack.InteractionTypeBlockActions {
		log.Info().Msgf("Received block actions event")
		return h.handleBlockActionInteractivity(ctx, interactivityEvent, w, r)
	} else if interactivityEvent.Type == slack.InteractionTypeViewSubmission {
		log.Info().Msgf("Received view submission event")
		return h.handleViewSubmissionInteractivity(ctx, interactivityEvent, w, r)
	} else if interactivityEvent.Type == slack.InteractionTypeMessageAction {
		log.Info().Msgf("Received message action event")
		return h.handleMessageActionInteractivity(ctx, interactivityEvent, w, r)
	}
	// Slack expects a success status always to acknowledge the message was received
	return http.StatusOK
}

// Handle block action interactivity events
func (h *SlackWebHandler) handleBlockActionInteractivity(ctx context.Context, interactivityEvent *slack.InteractionCallback, w http.ResponseWriter, r *http.Request) (statusCode int) {
	if interactivityEvent.ActionCallback.BlockActions[0].ActionID == common.ACTION_OPEN_REPO_SELECT_MODAL {
		log.Info().Msgf("Received Open Repo Select Modal Slack interactivity event")

		value := interactivityEvent.ActionCallback.BlockActions[0].Value
		messageTs := interactivityEvent.Message.Timestamp

		// Get thread timestamp from container
		var threadTs string
		threadTs = interactivityEvent.Container.MessageTs

		log.Info().Msgf("Block action occurred in thread: %s", threadTs)

		errMsg := h.eventHandler.HandleOpenRepoSelection(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent.User.ID, interactivityEvent.Channel.ID, interactivityEvent.TriggerID, value, messageTs, threadTs)
		if errMsg != "" {
			errorMsg := map[string]interface{}{
				"text":             errMsg,
				"response_type":    "ephemeral",
				"replace_original": false,
			}
			responseBytes, _ := json.Marshal(errorMsg)
			_, err := http.Post(interactivityEvent.ResponseURL, "application/json", bytes.NewBuffer(responseBytes))
			if err != nil {
				log.Error().Err(err).Msg("Failed to send error response message")
			}
		}
	} else if interactivityEvent.ActionCallback.BlockActions[0].ActionID == common.ACTION_SELECT_ALL_BUTTON {
		log.Info().Msgf("Received Select All Button Slack interactivity event")
		err := h.eventHandler.HandleSelectAllReposEvent(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent, true)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle interactivity event")
		}
	} else if interactivityEvent.ActionCallback.BlockActions[0].ActionID == common.ACTION_DESELECT_ALL_BUTTON {
		log.Info().Msgf("Received Deselect All Button Slack interactivity event")
		err := h.eventHandler.HandleSelectAllReposEvent(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent, false)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle interactivity event")
		}
	} else if interactivityEvent.ActionCallback.BlockActions[0].ActionID == common.ACTION_PROVIDE_FEEDBACK_BUTTON {
		log.Info().Msgf("Received Provide Feedback Button Slack interactivity event")

		// the value of the button is the metadata we need to reference the original message
		value := interactivityEvent.ActionCallback.BlockActions[0].Value

		metadata := &processorproto.FeedbackModalMetadata{}
		err := common.DecodeProtoMetadata(value, metadata)
		if err != nil {
			log.Error().Err(err).Msg("Failed to decode modal metadata")
			return http.StatusInternalServerError
		}

		err = h.eventHandler.HandleOpenFeedbackModalEvent(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent, metadata)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle interactivity event")
		} else {
			// Delete the original ephemeral message
			response := map[string]interface{}{
				"delete_original": "true",
			}
			responseBytes, _ := json.Marshal(response)
			_, err = http.Post(interactivityEvent.ResponseURL, "application/json", bytes.NewBuffer(responseBytes))
			if err != nil {
				log.Error().Err(err).Msg("Failed to remove original ephemeral message")
			}
		}

	} else if interactivityEvent.ActionCallback.BlockActions[0].ActionID == common.ACTION_SIGN_INTO_GLEAN_BUTTON {
		log.Info().Msgf("Received Sign into Glean Button click")

		// Delete the ephemeral message letting users know to sign in to Glean
		response := map[string]interface{}{
			"delete_original": true,
		}
		responseBytes, _ := json.Marshal(response)
		_, err := http.Post(interactivityEvent.ResponseURL, "application/json", bytes.NewBuffer(responseBytes))
		if err != nil {
			log.Error().Err(err).Msg("Failed to delete ephemeral message")
		}
	}

	// Slack expects a success status always to acknowledge the message was received
	return http.StatusOK
}

func (h *SlackWebHandler) handleViewSubmissionInteractivity(ctx context.Context, interactivityEvent *slack.InteractionCallback, w http.ResponseWriter, r *http.Request) (statusCode int) {
	if interactivityEvent.View.CallbackID == common.CALLBACK_REPO_SELECT_MODAL {
		log.Info().Msgf("Received repo select modal submission event")

		var selectedRepos []string
		var isSelectAll bool

		if interactivityEvent != nil {
			// Check for select all action block
			for _, block := range interactivityEvent.View.Blocks.BlockSet {
				if block.BlockType() == slack.MBTAction {
					if actionBlock, ok := block.(*slack.ActionBlock); ok {
						// If we see a deselect all action block, we know the user selected all repos
						if actionBlock.BlockID == common.BLOCK_DESELECT_ALL {
							isSelectAll = true
							break
						}
					}
				}
			}

			// Get selected repos from the multi-select menu
			if !isSelectAll && interactivityEvent.View.State != nil {
				for _, block := range interactivityEvent.View.State.Values {
					if selectAction, ok := block[common.ACTION_MULTI_SELECT]; ok {
						for _, option := range selectAction.SelectedOptions {
							selectedRepos = append(selectedRepos, option.Value)
						}
					}
				}
			}

		}

		if len(selectedRepos) == 0 && !isSelectAll {
			log.Error().Msg("Failed to extract selected repositories from interaction")
			w.WriteHeader(http.StatusBadRequest)
			return http.StatusBadRequest
		}

		err := h.eventHandler.HandleRepoSelectionEvent(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent, selectedRepos, isSelectAll)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle interactivity event")
			// Don't return an error here, as slack expects a success status always to acknowledge the message was received
			// HandleRepoSelectionEvent will have already posted an error message to the user if necessary
			// This happens in the function itself since we don't have a responseURL by default for this type of interactivity event
		}

	} else if interactivityEvent.View.CallbackID == common.CALLBACK_FEEDBACK_MODAL {
		log.Info().Msgf("Received feedback modal submission event")

		var rating, note string

		// Find input block values
		if interactivityEvent.View.State != nil && interactivityEvent.View.State.Values != nil {
			for _, block := range interactivityEvent.View.State.Values {
				if selectAction, ok := block[common.ACTION_FEEDBACK_RATING]; ok {
					rating = selectAction.SelectedOption.Value
				} else if textInput, ok := block[common.ACTION_FEEDBACK_NOTE]; ok {
					note = textInput.Value
				}
			}
		}

		err := h.eventHandler.HandleFeedbackEvent(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent, rating, note)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle feedback event")
			// Don't return an error here, as slack expects a success status always to acknowledge the message was received
		}
	}
	// Slack expects a success status always to acknowledge the message was received
	return http.StatusOK
}

// Handle message action interactivity events which are set in the slack app "Interactivity & Shortcuts" section
func (h *SlackWebHandler) handleMessageActionInteractivity(ctx context.Context, interactivityEvent *slack.InteractionCallback, w http.ResponseWriter, r *http.Request) (statusCode int) {
	if interactivityEvent.CallbackID == common.CALLBACK_MSG_FEEDBACK_SHORTCUT {
		log.Info().Msgf("Received Message Action Slack interactivity event %s", interactivityEvent.CallbackID)

		err := h.eventHandler.HandleOpenFeedbackModalEvent(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent, nil)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle feedback modal request event")
			// Don't return an error here, as slack expects a success status always to acknowledge the message was received
		}
	}
	// Slack expects a success status always to acknowledge the message was received
	return http.StatusOK
}

func (h *SlackWebHandler) HandleInteractivityEvent(w http.ResponseWriter, r *http.Request) {
	h.handleInteractivityHelper(w, r)
}

func (h *SlackWebHandler) HandleGetAllRepos(w http.ResponseWriter, r *http.Request) {
	h.handleGetAllReposHelper(w, r)
}

func (h *SlackWebHandler) handleGetAllReposHelper(w http.ResponseWriter, r *http.Request) (statusCode int) {
	defer func() {
		getAllReposWebhookRequestCounter.WithLabelValues(strconv.Itoa(statusCode)).Inc()
	}()

	log.Info().Msg("Received Slack get all repos event")

	interactivityEvent, err := h.parseSlackInteractivityEvent(r)
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse slack event")
		http.Error(w, err.Error(), http.StatusBadRequest)
		return http.StatusBadRequest
	}

	// This callback should only be called for population select menus
	if interactivityEvent.Type != slack.InteractionTypeBlockSuggestion {
		log.Warn().Msgf("Unexpected event: %s", interactivityEvent.Type)
		w.WriteHeader(http.StatusNotImplemented)
		return http.StatusNotImplemented
	}

	ctx := r.Context()
	options, err := h.eventHandler.HandleGetAllReposEvent(ctx, interactivityEvent.Team.ID, interactivityEvent.Enterprise.ID, interactivityEvent)
	if err != nil {
		log.Error().Err(err).Msg("Failed to handle get repos event")
		// Don't return an error here, as slack expects a success status always to acknowledge the message was received
	} else {
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(options)
	}
	return http.StatusOK
}

func (h *SlackWebHandler) parseSlackInteractivityEvent(r *http.Request) (*slack.InteractionCallback, error) {
	if _, err := h.VerifyRequest(r); err != nil {
		return nil, fmt.Errorf("unauthorized request: %w", err)
	}

	if err := r.ParseForm(); err != nil {
		return nil, fmt.Errorf("failed to parse form data: %w", err)
	}

	payload := r.Form.Get("payload")
	if payload == "" {
		return nil, fmt.Errorf("empty payload received")
	}

	decodedPayload, err := url.QueryUnescape(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to URL-decode payload: %w", err)
	}

	var interactivityEvent slack.InteractionCallback
	if err := json.Unmarshal([]byte(decodedPayload), &interactivityEvent); err != nil {
		return nil, fmt.Errorf("failed to unmarshal interactivity event: %w", err)
	}

	return &interactivityEvent, nil
}

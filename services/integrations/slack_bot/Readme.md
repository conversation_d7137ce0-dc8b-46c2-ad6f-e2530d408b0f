# Slack Bot

This is a implementation of a repo-aware/context-aware Slack bot

# Development

## Deployment

Deploy to dev namespace:

```bash
bazel run //services/integrations/slack_bot/webhook:kubecfg
bazel run //services/integrations/slack_bot/processor/server:kubecfg
```

Wait for the ingress to come up in dev (see https://console.cloud.google.com/kubernetes/gateways?project=system-services-dev)

## Modify Slack Bot

We have a separate staging slackbot and prod slackbot. If you want to create changes tp the slack bot, you need to create your own dev slack bot (see https://www.notion.so/Setting-up-a-dev-Slack-app-111bba10175a804b81afcab1cc4e4a62?pvs=4)


## Architecture

The integration consists of two main components:

1. **Webhook Server** (`/webhook`)
   - Handles incoming events from Slack
   - Entry point for all Slack interactions
   - Validates Slack signatures
   - Routes events to the processor

2. **Processor Server** (`/processor/server`)
   - Handles business logic for Slack events
   - Manages chat interactions
   - <PERSON>les repo selection and settings
   - Processes user feedback

## Events

The Slack bot listens for the following events:
- `app_mention`: When the bot is mentioned in a channel or thread
- `message`: When a message is sent in a channel or thread
- `reaction_added`: When a reaction is added to a message
- `member_joined_channel`: When a new member joins a channel
- `app_uninstalled`: When the bot is uninstalled from a workspace
- `app_home_opened`: When the bot's home tab is opened

These events are all published to a PubSub topic for processing by the processor server.

## Modals

The Slack bot uses modals for the following interactions:
- Repo selection: Allows users to select repositories for the bot to access
    - Entry Points:
        - `/augment repo-select` slash command (set up in Slack app settings, has different variations for dev and staging)
        - Button in chat messages when multiple repos are connected and no repo is selected for the channel - `open_repo_select_modal_action` block action
    - Associated events:
        1. If the tenant has 100+ repos, the modal will use an external data source to load the repos, triggering a `block_suggestion` event for every query
        2. If the user clicks the "Select All" button, a `block_action` event is triggered with the action ID `select_all_button`, and the modal is updated to show a "Deselect All" button and a confirmation message
        3. If user clicks the "Deselect All" button, a `block_action` event is triggered with the action ID `deselect_all_button` and the modal is updated to show a "Select All" button and the multi-select menu
        4. If the modal is submitted, the selected repos are saved and a success message is sent to the channel
- Feedback: Allows users to provide feedback on the bot's performance
    - Entry Point:
        - Message shortcut with callback ID `msg_feedback` (set up in Slack app settings)
    - Associated events:
        1. If the modal is submitted, the feedback is saved into Request Insights and a success message is sent to the channel

These interactions are all handled by the processor server directly to minimize latency to the user, especially since some events have a short-lived trigger ID.


## Chat

Chat in Augment Slack with "Augment-Staging".


# Links

- Early Design Doc: https://www.notion.so/Design-Doc-Context-aware-Slack-Chat-bot-de5d79a2f77b4952a9069e466403286d?pvs=4

syntax = "proto3";

package docset;

import "base/proto/tensor.proto";
import "services/embeddings_search_host/embeddings_search.proto";

service DocSetService {
  // returns the document sets that are available.
  rpc GetDocSets(GetDocSetRequest) returns (GetDocSetResponse);

  // search the given docsets for the given query and return
  // the responses
  rpc Search(SearchRequest) returns (stream SearchResponse);

  // docsets that might be implicitly mentioned in the message text
  rpc GetImplicitDocsets(GetImplicitDocsetsRequest) returns (GetImplicitDocsetsResponse);
}

message GetDocSetRequest {
  // the search pattern to use
  // if empty, all docsets are returned
  // if not empty, only docsets that match the search pattern are returned
  //
  // The exact search pattern is implementation defined, but it could be a substr search
  // in the docset id or title
  string search_pattern = 1 [debug_redact = true];
}

message DocSet {
  // uri based id of the docset
  // e.g. "docset://python~3.11"
  string doc_set_id = 1;

  // user visible id of the docset
  // e.g. "python~3.11"
  string name = 3;

  // a user visible description of the docset
  string title = 2;
}

message GetDocSetResponse {
  repeated DocSet doc_sets = 1;
}

message SearchRequest {
  // the docset ids to search in
  repeated string doc_set_ids = 1;

  // a single query as a vector of size [dim] in either float16/float32 depending on the host.
  // the tensor for each query is usually the embedding calculated using an embedder
  tensor.Tensor query = 2 [debug_redact = true];

  // how many results to return, e.g. 32 to return the 32 most similar results
  int32 num_results = 3;

  // the transformation key to use for the search
  string transformation_key = 4;

  // Spend up to this long searching for results. 0 means unset (will default back to 1 second
  // timeout which is the historical behavior).
  // Generally speaking, this should be at most 100% of the time we budget for a model to run,
  // but at least (say) 20%. Current settings are 1s for completions, 5s for everything else.
  uint32 search_timeout_ms = 5;
}

message MissingExternalSources {
  repeated string incomplete_external_source_ids = 1;
}

message SearchResponse {
  oneof response {
    // Retrieved chunks are streamed back in sorted order by the descending score
    // (best to worst).
    embeddings_search.RetrievalChunk chunk = 1;
    // if a docset in the given list is incomplete
    // the request should NOT fail, but the doc set id should be added
    // to this list.
    MissingExternalSources missing_external_sources = 2;
  }
}

message GetImplicitDocsetsRequest {
  // the message text to search for implicit docsets
  string message_text = 1 [debug_redact = true];
}

message GetImplicitDocsetsResponse {
  // the docset ids that are implicitly mentioned in the message.
  // this matches the id field of the next field
  // technically redundant information; it is kept for compatibility.
  repeated string docset_ids = 1;

  // Returning full data including names and titles, so that the caller does not have to
  // make another request to get the data.
  repeated DocSet docsets = 2;
}

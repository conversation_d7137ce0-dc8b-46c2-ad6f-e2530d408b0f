# Docset Service

The docset service is a service that allows to Search though
Augment curated (software) documentation as part of a Dense
Retrieval.

It is named Docset service as the central unit is the Docset, a
set of documentations belonging to a programming language or library, e.g.
"python 3.11".

The main goals of the docset service are:
- Listing of docsets
- Performing an embeddings search through docsets based on a query

The docset service will list docsets from a configuration file,
download them from GCS blob storage and upload them to
content manager (for indexing and retrieval). It will
use the support tenant to share docsets with other tenants.

When searches, it will forward the search to the embeddings search host
but with a separate auth token to "jump" into the support tenant.
The support tenant does not contain restricted data.

## API

The API is defined in `docset.proto`.

## Dependencies

The docset service depends on tenant watcher, token exchange, content manager and embeddings search host.

It has an external dependency on GCS blob storage.

## Security

### Where should this run

The docset service runs in each shard namespace proving services
to all tenants in that namespace.

### Authentication and authorization

The docset service authenticates each request.
However, it only provides access to public information.
It will forward requests to the embeddings search host with a
separate auth token to "jump" into the support tenant.

### Permissions and secrets

The docset service needs access to GCS blob storage.

### Persistence

The docset service does not persist any data of its own.

### Privacy considerations

The docset service does not store any privacy-sensitive data.

### Testing

The docset service contains various unit tests and has been
added to the API end to end test.

### Performance

The docset service itself should not be a performance bottleneck.
However, it facilites searches over large embedding contexts, so
that the search is performance sensitive.

# Links

- https://www.notion.so/RFC-System-Design-3rd-Party-Docs-Retrieval-in-Shard-Namespace-7a67f22433e3482eadc6fab100d51572?pvs=4

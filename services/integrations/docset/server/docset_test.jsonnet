// This file contains consistency tests for the docsets list

local validateDocset(env) =
  local docsets = (import 'docset.jsonnet')(env);
  assert std.length(docsets.docsets) > 0;
  assert std.length(std.set(std.map(function(docset) docset.docset_id, docsets.docsets))) == std.length(docsets.docsets) : 'docset_ids must be unique';
  true;

local validate() =
  // Prod currently only has a full docset list in i0, validate that here.
  [validateDocset(env) for env in ['DEV', 'STAGING', 'PROD']];
validate()

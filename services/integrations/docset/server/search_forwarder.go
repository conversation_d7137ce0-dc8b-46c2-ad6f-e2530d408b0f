package main

import (
	"context"

	blobs_proto "github.com/augmentcode/augment/base/blob_names/proto"
	embedding_search_client "github.com/augmentcode/augment/services/embeddings_search_host/client"
	search_proto "github.com/augmentcode/augment/services/embeddings_search_host/proto"
	proto "github.com/augmentcode/augment/services/integrations/docset/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	token_exchange_proto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var missingDocsetCount = prometheus.NewCounter(prometheus.CounterOpts{
	Name: "au_docset_missing_count",
	Help: "Number of times a docset is missing and the docset ID does not exist",
})

func init() {
	prometheus.MustRegister(
		missingDocsetCount,
	)
}

// forwards the search request to the embeddings search host
type SearchForwarder interface {
	ForwardSearch(ctx context.Context, req *proto.SearchRequest, original_request_context *requestcontext.RequestContext) (<-chan embedding_search_client.SearchChunkResult, error)
}

type searchForwarder struct {
	docsets             []*Docset
	docsetManager       DocsetManager
	searchClient        embedding_search_client.EmbeddingsSearchClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	docsetTenantID      string
}

func NewSearchForwarder(docsetManager DocsetManager,
	searchClient embedding_search_client.EmbeddingsSearchClient, tokenExchangeClient tokenexchange.TokenExchangeClient, docsetTenantID string, docsets []*Docset,
) SearchForwarder {
	return &searchForwarder{
		docsets:             docsets,
		docsetManager:       docsetManager,
		searchClient:        searchClient,
		tokenExchangeClient: tokenExchangeClient,
		docsetTenantID:      docsetTenantID,
	}
}

func (u *searchForwarder) getRequestContext(ctx context.Context, original_request_context *requestcontext.RequestContext) (*requestcontext.RequestContext, error) {
	if u.docsetTenantID == "" {
		return nil, status.Error(codes.FailedPrecondition, "docset tenant id is not set")
	}
	serviceToken, err := u.tokenExchangeClient.GetSignedTokenForService(ctx, u.docsetTenantID, []token_exchange_proto.Scope{
		token_exchange_proto.Scope_CONTENT_RW,
	})
	if err != nil {
		return nil, err
	}

	// use same request id and session id, but exchange the auth token
	return requestcontext.New(original_request_context.RequestId, original_request_context.RequestSessionId, original_request_context.RequestSource, serviceToken), nil
}

func (f *searchForwarder) ForwardSearch(ctx context.Context, req *proto.SearchRequest, original_request_context *requestcontext.RequestContext) (<-chan embedding_search_client.SearchChunkResult, error) {
	log.Ctx(ctx).Info().Msgf("Forwarding search request for docsets %v", req.DocSetIds)
	var blobs []*blobs_proto.Blobs
	for _, docSetId := range req.DocSetIds {
		found := false
		for _, docsetVersion := range f.docsets {
			if docsetVersion.DocsetId == docSetId {
				found = true
				b, err := f.docsetManager.GetDocsetCheckpoint(docsetVersion.DocsetVersionId)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Msgf("Failed to get docset checkpoint for docset %v", docsetVersion.Name)
					return nil, err
				}
				blobs = append(blobs, b)
			}
		}
		if !found {
			missingDocsetCount.Inc()
			log.Ctx(ctx).Warn().Msgf("Docset %v not found", docSetId)
		}
	}

	if len(blobs) == 0 {
		log.Ctx(ctx).Warn().Msgf("No docsets found for ids %v", req.DocSetIds)
		ch := make(chan embedding_search_client.SearchChunkResult)
		close(ch)
		return ch, nil
	}

	rc, err := f.getRequestContext(ctx, original_request_context)
	if err != nil {
		return nil, err
	}

	embeddingsSearchRequest := &search_proto.SearchChunksRequest{
		Query:             req.Query,
		NumResults:        req.NumResults,
		TransformationKey: req.TransformationKey,
		Blobs:             blobs,
		SearchTimeoutMs:   req.SearchTimeoutMs,
	}

	return f.searchClient.SearchChunks(ctx,
		embeddingsSearchRequest, rc)
}

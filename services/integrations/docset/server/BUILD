load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_to_json_test")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "server_lib",
    srcs = [
        "docset_config.go",
        "docset_manager.go",
        "docset_server.go",
        "docset_version_config.go",
        "main.go",
        "persistence.go",
        "search_forwarder.go",
        "uploader.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/docset/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/durationutil",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/embeddings_search_host:embeddings_search_go_proto",
        "//services/embeddings_search_host/client:client_go",
        "//services/integrations/docset:docset_go_proto",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight/publisher:publisher_go",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_lithammer_fuzzysearch//fuzzy",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_storage//:storage",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_x_sync//errgroup",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "server_test",
    srcs = [
        "docset_manager_test.go",
        "docset_server_test.go",
        "uploader_test.go",
    ],
    data = glob(["test_data/**"]),
    embed = [":server_lib"],
    deps = [
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//peer",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg_library(
    name = "docset_lib",
    srcs = [
        "docset.jsonnet",
        "docsets_dump.json",
    ],
)

jsonnet_to_json_test(
    name = "docset-test",
    src = "docset_test.jsonnet",
    deps = [
        ":docset_lib",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        ":docset_lib",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
        "//deploy/tenants:namespaces",
    ],
)

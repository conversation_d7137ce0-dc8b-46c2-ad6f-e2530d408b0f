package main

import (
	"os"
	"testing"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/stretchr/testify/assert"
)

func TestNewDocsetIndex(t *testing.T) {
	content, err := os.ReadFile("test_data/index.json")
	assert.NoError(t, err)
	index, err := NewDocSetIndexFromContent(content)
	assert.NoError(t, err)
	assert.Equal(t, 2, len(index.Files))
	assert.Equal(t, "docset://graphite.dev", index.DocsetId)
	assert.Equal(t, "graphite.dev", index.Name)
	assert.Equal(t, "files/doc-0.jsonl", index.Files[0].Name)
	assert.Equal(t, blob_names.BlobName("00ad86cca6ab3afdf12463c4d71d9f137ea57d2795f15a89d2cf62f0817c77e4"), index.Files[0].BlobName)
	assert.Equal(t, "00ad86cca6ab3afdf12463c4d71d9f137ea57d2795f15a89d2cf62f0817c77e4", index.Files[0].Checksum)
}

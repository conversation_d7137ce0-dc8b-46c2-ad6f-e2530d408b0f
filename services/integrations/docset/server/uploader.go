package main

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"path/filepath"
	"time"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_proto "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	content_manager_client "github.com/augmentcode/augment/services/content_manager/client"
	content_manager_proto "github.com/augmentcode/augment/services/content_manager/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	token_exchange_proto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/prometheus/client_golang/prometheus"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/rs/zerolog/log"
)

const (
	enableDocsetUploadsFlag = "enable_docset_uploads"
)

var (
	uploadBlobCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_docset_upload_docset_blob_count",
		Help: "Number of blobs in a docset version",
	}, []string{"docset_version_id"})
	uploadMissingBlobCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_docset_upload_docset_missing_blob_count",
		Help: "Number of missing blobs in a docset version",
	}, []string{"docset_version_id"})
)

func init() {
	prometheus.MustRegister(
		uploadBlobCount,
		uploadMissingBlobCount,
	)
}

// uploads docsets to the content manager
type Uploader interface {
	// registers a docset version.
	//
	// it will download the index.json and then trigger downloads all files in the index.json
	// and then uploads all files to the content manager (if they are not already uploaded).
	// it will return before the docset version is fully uploaded.
	//
	// Returns
	// - the blobs object with a checkpoint id for all blobs of the docset version
	RegisterDocset(ctx context.Context, docsetVersion *DocsetVersion, prefix string) (*blobs_proto.Blobs, []blob_names.BlobName, error)

	// waits for all registered docset versions to be uploaded
	// should be called after all RegisterDocset calls
	Wait() error
}

type UploaderImpl struct {
	// controls the number of concurrent uploads
	//
	// this is down to limit the main memory usage of the process
	sempahore      chan struct{}
	docsetTenantID string

	reader DocsetReader

	contentManagerClient content_manager_client.ContentManagerClient
	tokenExchangeClient  tokenexchange.TokenExchangeClient
	featureFlagsHandle   featureflags.FeatureFlagHandle

	requestSessionId requestcontext.RequestSessionId

	group               errgroup.Group
	uploadChunkCount    int
	maxUploadSize       int
	waitUploadSleepTime time.Duration

	docsetTasks []*UploaderTask
}

const (
	concurrentUploadCount = 8
	uploadChunkSize       = 64
	maxUploadSize         = 1024 * 1024
)

func NewUploader(docsetTenantID string, reader DocsetReader,
	contentManagerClient content_manager_client.ContentManagerClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagsHandle featureflags.FeatureFlagHandle,
	waitUploadSleepTime time.Duration,
) Uploader {
	requestSessionId := requestcontext.NewRandomRequestSessionId()

	group := errgroup.Group{}
	return &UploaderImpl{
		sempahore:            make(chan struct{}, concurrentUploadCount),
		docsetTenantID:       docsetTenantID,
		reader:               reader,
		contentManagerClient: contentManagerClient,
		tokenExchangeClient:  tokenExchangeClient,
		featureFlagsHandle:   featureFlagsHandle,
		requestSessionId:     requestSessionId,
		uploadChunkCount:     uploadChunkSize,
		maxUploadSize:        maxUploadSize,
		group:                group,
		waitUploadSleepTime:  waitUploadSleepTime,
	}
}

func (u *UploaderImpl) Wait() error {
	return u.group.Wait()
}

type DocsetFile struct {
	Name     string              `json:"name"`
	Path     string              `json:"path"`
	BlobName blob_names.BlobName `json:"blob_name"`
	Checksum string              `json:"checksum"`
}

type DocsetIndex struct {
	DocsetId        string       `json:"docset_id"`
	Name            string       `json:"name"`
	DocsetVersionId string       `json:"docset_version_id"`
	Files           []DocsetFile `json:"files"`
}

func NewDocSetIndexFromContent(content []byte) (*DocsetIndex, error) {
	var index DocsetIndex
	err := json.Unmarshal(content, &index)
	if err != nil {
		return nil, err
	}
	return &index, nil
}

type DocsetFileDownload struct {
	DocsetVersion *DocsetVersion
	DocsetFile    *DocsetFile
	content       []byte
}

func (u *UploaderImpl) downloadDocsetFile(docsetVersion *DocsetVersion, docsetFile *DocsetFile, versionPrefix string) (*DocsetFileDownload, error) {
	log.Info().Msgf("Downloading docset file %v", docsetVersion)
	path := filepath.Join(versionPrefix, docsetFile.Name)
	contentBytes, err := u.reader.Download(path, docsetFile.Checksum, docsetFile.Path)
	if err != nil {
		log.Error().Err(err).Msg("Failed to download docset file")
		return nil, err
	}

	download := &DocsetFileDownload{
		DocsetVersion: docsetVersion,
		DocsetFile:    docsetFile,
		content:       contentBytes,
	}
	log.Info().Msgf("Downloaded docset file %v", docsetFile)
	return download, nil
}

func toUploadBlobContent(docsetFileDownload *DocsetFileDownload, docsetName, docsetId string) *content_manager_proto.UploadBlobContent {
	return &content_manager_proto.UploadBlobContent{
		Content: []byte(docsetFileDownload.content),
		Metadata: []*content_manager_proto.BlobMetadata{
			// the docset blobs are pre-chunked and the indexer should not chunk them again
			{
				Key:   "pre-chunked",
				Value: "true",
			},
			// need to exist for content manager to be happy
			{
				Key:   "path",
				Value: docsetFileDownload.DocsetFile.Path,
			},
			{
				Key:   "docset_name",
				Value: docsetName,
			},
			{
				Key:   "docset_id",
				Value: docsetId,
			},
		},
	}
}

func (u *UploaderImpl) getRequestContext(ctx context.Context) (*requestcontext.RequestContext, error) {
	if u.docsetTenantID == "" {
		return nil, status.Error(codes.FailedPrecondition, "docset tenant id is not set")
	}
	serviceToken, err := u.tokenExchangeClient.GetSignedTokenForService(ctx, u.docsetTenantID, []token_exchange_proto.Scope{
		token_exchange_proto.Scope_CONTENT_RW,
	})
	if err != nil {
		return nil, err
	}

	return requestcontext.New(requestcontext.NewRandomRequestId(), u.requestSessionId, "background", serviceToken), nil
}

func (u *UploaderImpl) checkBlobName(uploads []*content_manager_proto.UploadBlobResult, chunk []*DocsetFile) error {
	for _, upload := range uploads {
		found := false
		for _, docsetFile := range chunk {
			if blob_names.BlobName(upload.BlobName) == docsetFile.BlobName {
				found = true
				break
			}
		}
		if !found {
			log.Error().Msgf("Failed to upload docset file %v: Mismatch between expected blob name and what the server returned", upload)
			return status.Errorf(codes.Internal, "failed to upload docset file %v", upload)
		}
	}
	return nil
}

func (u *UploaderImpl) uploadDocsetChunk(ctx context.Context, docsetVersion *DocsetVersion, chunk []*DocsetFile, requestContext *requestcontext.RequestContext, docsetName, docsetId string, versionPrefix string) error {
	var uploads []*content_manager_proto.UploadBlobContent
	size := 0
	for _, docsetFile := range chunk {
		log.Info().Msgf("Uploading docset file %v", docsetFile)
		docsetFileDownload, err := u.downloadDocsetFile(docsetVersion, docsetFile, versionPrefix)
		if err != nil {
			log.Error().Err(err).Msg("Failed to download docset file")
			return err
		}
		uploads = append(uploads, toUploadBlobContent(docsetFileDownload, docsetName, docsetId))
		size += len(docsetFileDownload.content)

		if size > u.maxUploadSize {
			log.Info().Msgf("Uploading %d blobs", len(uploads))
			r, err := u.contentManagerClient.BatchUploadBlobContent(ctx, uploads, content_manager_proto.IndexingPriority_LOW, requestContext)
			if err != nil {
				return err
			}
			err = u.checkBlobName(r, chunk)
			if err != nil {
				return err
			}
			uploads = nil
			size = 0
		}
	}

	if len(uploads) == 0 {
		return nil
	}
	log.Info().Msgf("Uploading %d blobs", len(uploads))
	r, err := u.contentManagerClient.BatchUploadBlobContent(ctx, uploads, content_manager_proto.IndexingPriority_LOW, requestContext)
	if err != nil {
		return err
	}
	err = u.checkBlobName(r, chunk)
	if err != nil {
		return err
	}
	return nil
}

func (u *UploaderImpl) handleChunk(ctx context.Context, docsetVersion *DocsetVersion, chunk []*DocsetFile, requestContext *requestcontext.RequestContext, docsetName, docsetId string,
	versionPrefix string,
) error {
	blobNames := make([]blob_names.BlobName, len(chunk))
	for i, docsetFile := range chunk {
		blobNames[i] = docsetFile.BlobName
	}
	log.Info().Msgf("Checking for missing blobs %v", blobNames)
	uploadBlobCount.WithLabelValues(docsetVersion.DocsetVersionId).Add(float64(len(blobNames)))
	missingBlobNames, err := u.contentManagerClient.FindMissingBlobs(ctx, blobNames, "", "", requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to check for missing blobs")
		return err
	}
	uploadMissingBlobCount.WithLabelValues(docsetVersion.DocsetVersionId).Add(float64(len(missingBlobNames)))
	if len(missingBlobNames) == 0 {
		return nil
	}
	log.Info().Msgf("Found %d missing blobs", len(missingBlobNames))

	var missingDocsetFiles []*DocsetFile
	for _, blobName := range missingBlobNames {
		for _, docsetFile := range chunk {
			if docsetFile.BlobName == blob_names.BlobName(blobName) {
				missingDocsetFiles = append(missingDocsetFiles, docsetFile)
			}
		}
	}
	if len(missingDocsetFiles) == 0 {
		return nil
	}

	// grab a semaphore to limit the number of concurrent uploads
	u.sempahore <- struct{}{}
	defer func() {
		<-u.sempahore
	}()
	err = u.uploadDocsetChunk(ctx, docsetVersion, missingDocsetFiles, requestContext, docsetName, docsetId, versionPrefix)
	if err != nil {
		return err
	}
	return nil
}

func (u *UploaderImpl) validateChecksum(contents []byte, checksum string) error {
	hash := sha256.New()
	hash.Write(contents)
	actualChecksum := hex.EncodeToString(hash.Sum(nil))
	if actualChecksum != checksum {
		return status.Error(codes.Internal, "checksum mismatch")
	}
	return nil
}

func (u *UploaderImpl) waitForUploadsEnabled(ctx context.Context) {
	log.Info().Msgf("Waiting for %s feature flag to be enabled", enableDocsetUploadsFlag)
	for {
		enableUploads, err := u.featureFlagsHandle.GetBool(enableDocsetUploadsFlag, false)
		if err != nil {
			// Ignore the error, we should default to false in this case
			log.Error().Err(err).Msg("Failed to get feature flag")
		}
		if enableUploads {
			// Continue with uploads
			log.Info().Msgf("%s feature flag is enabled, continuing with uploads", enableDocsetUploadsFlag)
			return
		} else if ctx.Err() != nil {
			// Context is cancelled, exit
			return
		} else {
			// Sleep indefinitely. This doesn't block startup, so this sleep loop
			// allows the feature flag to be dynamic.
			time.Sleep(u.waitUploadSleepTime)
		}
	}
}

func (u *UploaderImpl) uploadDocset(ctx context.Context, docsetVersion *DocsetVersion, index *DocsetIndex, versionPrefix string) error {
	// Wait here for the feature flag to be enabled. This will not block
	// startup, since these uploads happen in the background.
	u.waitForUploadsEnabled(ctx)

	log.Info().Msgf("Uploading docset version %v", docsetVersion)
	requestContext, err := u.getRequestContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get request context")
		return err
	}

	uploadChunk := make([]*DocsetFile, 0, u.uploadChunkCount)
	for _, docsetFile := range index.Files {
		if len(uploadChunk) < u.uploadChunkCount {
			uploadChunk = append(uploadChunk, &docsetFile)
		}
		if len(uploadChunk) == u.uploadChunkCount {
			err = u.handleChunk(ctx, docsetVersion, uploadChunk, requestContext, index.Name, index.DocsetId, versionPrefix)
			if err != nil {
				log.Error().Err(err).Msg("Failed to handle chunk")
				return err
			}
			uploadChunk = uploadChunk[:0]
		}
	}
	if len(uploadChunk) > 0 {
		err := u.handleChunk(ctx, docsetVersion, uploadChunk, requestContext, index.Name, index.DocsetId, versionPrefix)
		if err != nil {
			log.Error().Err(err).Msg("Failed to handle chunk")
			return err
		}
	}
	log.Info().Msgf("Uploading docset version %v done", docsetVersion)
	return nil
}

func (u *UploaderImpl) RegisterDocset(ctx context.Context, docsetVersion *DocsetVersion, prefix string) (*blobs_proto.Blobs, []blob_names.BlobName, error) {
	log.Info().Msgf("Registering docset version %v", docsetVersion)

	versionPrefix := filepath.Join(prefix, docsetVersion.DocsetVersionId)
	path := filepath.Join(versionPrefix, "index.json")
	content, err := u.reader.Download(path, docsetVersion.ChecksumSha256, "")
	if err != nil {
		log.Error().Err(err).Msgf("Failed to download index.json at %q", path)
		return nil, nil, err
	}

	index, err := NewDocSetIndexFromContent(content)
	if err != nil {
		log.Error().Err(err).Msg("Failed to unmarshal index.json")
		return nil, nil, err
	}

	log.Info().Msgf("Found docset index %v", index)

	requestContext, err := u.getRequestContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get request context")
		return nil, nil, err
	}

	var blobNames []blob_names.BlobName
	for _, docsetFile := range index.Files {
		blobNames = append(blobNames, docsetFile.BlobName)
	}

	u.group.Go(func() error {
		return u.uploadDocset(ctx, docsetVersion, index, versionPrefix)
	})

	var added [][]byte
	for _, blobName := range blobNames {
		b, err := blob_names.DecodeHexBlobName(blobName)
		if err != nil {
			log.Error().Err(err).Msg("Failed to decode blob name")
			return nil, nil, err
		}
		added = append(added, b)
	}

	blobs := &blobs_proto.Blobs{
		BaselineCheckpointId: nil,
		Added:                added,
		Deleted:              make([][]byte, 0),
	}
	checkpointId, err := u.contentManagerClient.CheckpointBlobs(ctx, blobs, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to checkpoint blobs")
		return nil, nil, err
	}
	log.Info().Msgf("Checkpointed blobs with checkpoint id %s", checkpointId)
	checkpointBlobs := &blobs_proto.Blobs{
		BaselineCheckpointId: &checkpointId,
		Added:                make([][]byte, 0),
		Deleted:              make([][]byte, 0),
	}
	return checkpointBlobs, blobNames, nil
}

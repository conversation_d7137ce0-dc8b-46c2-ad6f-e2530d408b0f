// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'doc-sets',
      kubecfg: {
        target: '//services/integrations/docset/server:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['aswin'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'doc-sets-monitoring',
      kubecfg: {
        target: '//services/integrations/docset/server:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['aswin'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

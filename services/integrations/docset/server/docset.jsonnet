function(env)
  local all_docsets = import 'docsets_dump.json';

  local docsetsForEnv(env) =
    if env == 'DEV' then
      // The base model rarely knows about graphite so it is a great test case for
      // dev. Keep the list of docsets low in dev so e2e tests can startup quickly
      // NOTE: The graphite docset is used in e2e tests, keep in mind when
      // changing this.
      [{ docset_id: d.docset_id, docset_version_id: d.docset_version_id } for d in all_docsets if std.member(['docset://graphite.dev', 'docset://augment'], d.docset_id)]
    else
      [{ docset_id: d.docset_id, docset_version_id: d.docset_version_id } for d in all_docsets];

  // For now, the available docsets and versions always match. This will change
  // once we want to stage updates, we will have some set of in-flight versions
  // that may need to be uploaded and indexed and some set of stable versions
  // that are ready for customer use.
  local docsets = docsetsForEnv(env);

  {
    docsets: docsets,
    version_collection: {
      object_store_path: 'docsets/json_docs_20250419/docset_collection_index.json',
      checksum_sha256: '16e3faf435fbfc5e3d33e2f609e1a10178d8ad4aeb12f91dc18c8522bdb88dcf',
    },
    // the docsets we want to let any request search by default.
    // Currently empty as they have been identified to confuse the model
    default_docset_ids: [],
  }

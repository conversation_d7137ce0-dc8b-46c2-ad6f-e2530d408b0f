package main

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_proto "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/stretchr/testify/assert"
)

type managerTestFakeUploader struct {
	blobs *blobs_proto.Blobs
}

func (f *managerTestFakeUploader) RegisterDocset(ctx context.Context, docsetVersion *DocsetVersion, prefix string) (*blobs_proto.Blobs, []blob_names.BlobName, error) {
	return f.blobs, nil, nil
}

func (f *managerTestFakeUploader) Wait() error {
	return nil
}

type managerTestFakeReader struct{}

func (f *managerTestFakeReader) Download(path string, checksum string, blobPath string) ([]byte, error) {
	if path == "docsets/json_docs_20240821/docset_collection_index.json" {
		versions := []DocsetVersion{
			{
				DocsetVersionId: "docset1-version1",
				DocsetId:        "docset://docset1",
				Name:            "Docset 1",
				Title:           "Documentation for Docset 1",
				Keywords:        []string{"d1"},
			},
		}
		content, err := json.Marshal(versions)
		if err != nil {
			return nil, err
		}
		return content, nil
	}
	return nil, errors.New("not found")
}

func TestDocsetManager(t *testing.T) {
	checkpointId := "checkpoint1"

	config := DocsetConfig{
		VersionCollection: DocsetCollection{
			ObjectStorePath: "docsets/json_docs_20240821/docset_collection_index.json",
			ChecksumSha256:  "c33673ff3d6a82ff4efec11e240617a7e2d35077359c49d8a92131693b61f4fb",
		},
		Docsets: []BaseDocset{
			{
				DocsetId:        "docset://docset1",
				DocsetVersionId: "docset1-version1",
			},
		},
	}

	reader := &managerTestFakeReader{}
	m := NewDocsetManager(&config, reader, &managerTestFakeUploader{
		blobs: &blobs_proto.Blobs{
			BaselineCheckpointId: &checkpointId,
			Added:                [][]byte{},
			Deleted:              [][]byte{},
		},
	})

	d, err := m.Start(context.Background())
	assert.NoError(t, err)

	blobs, err := m.GetDocsetCheckpoint("docset1-version1")
	assert.NoError(t, err)
	assert.NotNil(t, blobs)
	assert.NotNil(t, blobs.BaselineCheckpointId)
	assert.Equal(t, checkpointId, *blobs.BaselineCheckpointId)

	assert.Equal(t, 1, len(d))
	assert.Equal(t, "Docset 1", d[0].Name)
	assert.Equal(t, "docset1-version1", d[0].DocsetVersionId)
	assert.Equal(t, "Documentation for Docset 1", d[0].Title)
	assert.Equal(t, "docset://docset1", d[0].DocsetId)
	assert.Equal(t, []string{"d1"}, d[0].Keywords)
}

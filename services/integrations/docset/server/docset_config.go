package main

import (
	"errors"
)

type DocsetCollection struct {
	// path in the GCS object store to the files for this docset version
	// e.g. "docsets/python-3.11" with the index.json at "docsets/python-3.11/index.json"
	ObjectStorePath string `json:"object_store_path"`

	// a sha256 checksum of the index.json
	ChecksumSha256 string `json:"checksum_sha256"`
}

func ValidateDocsetCollection(docsetCollection DocsetCollection) error {
	if docsetCollection.ObjectStorePath == "" {
		return errors.New("object_store_path is required")
	}
	if docsetCollection.ChecksumSha256 == "" {
		return errors.New("checksum_sha256 is required")
	}
	return nil
}

// This corresponds to the fields in the docsets_dump.json
type Docset struct {
	// id of the docset, e.g. "docset://python~3.11"
	DocsetId string `json:"docset_id"`

	// user visible name of the docset, e.g. "Python 3.11"
	Name string `json:"name"`

	// a user visible description of the docset
	Title string `json:"title"`

	// category of the docset, e.g. "Programming Language"
	Category string `json:"category"`

	// the docset version to use for this docset
	// the docset version id has to be one of the docset_version_ids in the docset_versions list
	// the docset version should be fully indexed
	DocsetVersionId string `json:"docset_version_id"`

	// if any of these keywords are exactly present in the message as words (single words offset by
	// whitespace for now) then this docset will also be used
	Keywords []string `json:"keywords"`
}

type BaseDocset struct {
	// id of the docset
	DocsetId string `json:"docset_id"`

	// the docset version to use for this docset
	// the docset version id has to be one of the docset_version_ids in the docset_versions list
	// the docset version should be fully indexed
	DocsetVersionId string `json:"docset_version_id"`
}

func ValidateDocset(docset BaseDocset) error {
	if docset.DocsetId == "" {
		return errors.New("docset_id is required")
	}
	if docset.DocsetVersionId == "" {
		return errors.New("docset_version_id is required")
	}
	return nil
}

type DocsetConfig struct {
	VersionCollection DocsetCollection `json:"version_collection"`
	Docsets           []BaseDocset     `json:"docsets"`
	DefaultDocsetIds  []string         `json:"default_docset_ids"`
}

func ValidateDocsetConfig(docsetConfig DocsetConfig) error {
	if err := ValidateDocsetCollection(docsetConfig.VersionCollection); err != nil {
		return err
	}
	for _, docset := range docsetConfig.Docsets {
		if err := ValidateDocset(docset); err != nil {
			return err
		}
	}
	return nil
}

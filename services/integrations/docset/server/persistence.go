package main

import (
	"context"
	"fmt"
	"io"

	"cloud.google.com/go/storage"
	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type DocsetReader interface {
	Download(path string, checksum string, blobPath string) ([]byte, error)
}

type GcsDocsetReader struct {
	context       context.Context
	storageClient *storage.Client
	bucketName    string
}

func NewGcsDocsetReader(context context.Context, storageClient *storage.Client, bucketName string) DocsetReader {
	return &GcsDocsetReader{
		storageClient: storageClient,
		bucketName:    bucketName,
	}
}

func (u *GcsDocsetReader) validateChecksum(blobPath string, contents []byte, checksum string) error {
	blobName := blob_names.GetBlobName(blobPath, contents)
	if string(blobName) != checksum {
		log.Error().Msgf("Checksum mismatch: expected %s, got %s", checksum, blobName)
		return status.Error(codes.Internal, "checksum mismatch")
	}
	return nil
}

func (g *GcsDocsetReader) Download(path string, checksum string, blobPath string) ([]byte, error) {
	reader, err := g.storageClient.Bucket(g.bucketName).Object(path).NewReader(g.context)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to read %s from GCS", path)
		return nil, fmt.Errorf("failed to read %s from GCS: %w", path, err)
	}
	defer reader.Close()
	content, err := io.ReadAll(reader)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to read content for %s", path)
		return nil, err
	}
	err = g.validateChecksum(blobPath, content, checksum)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to validate checksum for %s", path)
		return nil, err
	}
	return content, nil
}

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"cloud.google.com/go/storage"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/durationutil"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	content_manager_client "github.com/augmentcode/augment/services/content_manager/client"
	embeddings_search_client "github.com/augmentcode/augment/services/embeddings_search_host/client"
	proto "github.com/augmentcode/augment/services/integrations/docset/proto"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	request_insight_publisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var (
	configFile                    = flag.String("config", "", "Path to config file")
	requestInsightPublisherConfig = flag.String("request-insight-publisher-config-file", "", "Path to request insight publisher config file")
	launchDarklySecretsFile       = flag.String("launch-darkly-secrets-file", "", "Path to launch darkly secrets file")
)

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	Namespace string `json:"namespace"`

	TokenExchangeEndpoint                string   `json:"token_exchange_endpoint"`
	TenantWatcherEndpoint                string   `json:"tenant_watcher_endpoint"`
	ConfigManagerEndpoint                string   `json:"content_manager_endpoint"`
	EmbeddingsSearchHost                 string   `json:"embeddings_search_host"`
	PartitionedEmbeddingsSearchEndpoints []string `json:"partitioned_embeddings_search_endpoints"`

	ConcurrentUploadCount int `json:"concurrent_upload_count"`

	// If uploads are blocked by the feature flag, how long to wait before
	// checking the feature flag again.
	WaitUploadSleepTime durationutil.JSONDuration `json:"wait_upload_sleep_time"`

	// tenant where to look for docset objects.
	DocsetTenant string `json:"docset_tenant"`

	BucketName string `json:"bucket_name"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	DocsetConfig DocsetConfig `json:"docset_config"`

	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`
}

func run(config *Config, grpcServer *grpc.Server, server *docSetServer) error {
	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	proto.RegisterDocSetServiceServer(grpcServer, server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}

	go func() {
		// Wait for either a shutdown signal or an OS signal
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		grpcServer.GracefulStop()
	}()

	log.Info().Msgf("Listening on %v", lis.Addr())
	err = grpcServer.Serve(lis)
	if err != nil && err != grpc.ErrServerStopped {
		log.Fatal().Err(err).Msg("Error serving")
	}
	log.Info().Msg("gRPC server closed")
	return nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	err = ValidateDocsetConfig(config.DocsetConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error in config file")
	}

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	requestInsightPublisher, err := request_insight_publisher.NewRequestInsightPublisherFromFile(ctx,
		*requestInsightPublisherConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating request insight publisher")
	}
	defer requestInsightPublisher.Close()

	// Create server credentials.

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}
	_ = clientCreds // this is just to suppress unused variable warning. Remove the code is not used

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	featureFlagsHandle, err := featureflags.NewFeatureFlagHandleFromFile(*launchDarklySecretsFile,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	storageClient, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating storage client")
	}
	defer storageClient.Close()

	contentManagerClient, err := content_manager_client.NewContentManagerClient(
		config.ConfigManagerEndpoint, clientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating content manager client")
	}
	defer contentManagerClient.Close()

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, config.Namespace)
	defer tenantCache.Close()

	docsetTenant, err := tenantCache.GetTenantByName(config.DocsetTenant)
	if err != nil {
		log.Fatal().Err(err).Msg("Error getting tenant id")
	}

	reader := NewGcsDocsetReader(ctx, storageClient, config.BucketName)
	uploader := NewUploader(docsetTenant.Id,
		reader, contentManagerClient, tokenExchangeClient, featureFlagsHandle,
		config.WaitUploadSleepTime.ToDuration())

	docsetManager := NewDocsetManager(&config.DocsetConfig, reader, uploader)
	docsets, err := docsetManager.Start(ctx)
	if err != nil {
		log.Fatal().Err(err).Msg("Error uploading docsets")
	}

	go func() {
		err := uploader.Wait()
		if err != nil {
			log.Fatal().Err(err).Msg("Error uploading docsets")
		}
		log.Info().Msg("Finished upload all docsets")
	}()

	var embeddingsSearchClient embeddings_search_client.EmbeddingsSearchClient
	if len(config.PartitionedEmbeddingsSearchEndpoints) > 0 {
		var clients []embeddings_search_client.EmbeddingsSearchClient
		for _, endpoint := range config.PartitionedEmbeddingsSearchEndpoints {
			client, err := embeddings_search_client.NewEmbeddingsSearchClient(endpoint, clientCreds)
			if err != nil {
				log.Fatal().Err(err).Msg("Error creating embeddings search client")
			}
			clients = append(clients, client)
		}
		embeddingsSearchClient = embeddings_search_client.NewPartitionedEmbeddingsSearchClient(clients)
	} else {
		embeddingsSearchClient, err = embeddings_search_client.NewEmbeddingsSearchClient(config.EmbeddingsSearchHost, clientCreds)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating embeddings search client")
		}
	}

	searchForwarder := NewSearchForwarder(docsetManager,
		embeddingsSearchClient,
		tokenExchangeClient,
		docsetTenant.Id,
		docsets,
	)

	docsetServer := NewDocSetServer(docsets, config.DocsetConfig.DefaultDocsetIds, docsetManager, searchForwarder)

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		srvMetrics.StreamServerInterceptor(),
	))

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	err = run(&config, grpcServer, docsetServer)
	if err != nil {
		log.Fatal().Err(err).Msg("Error serving")
	}
}

package main

import (
	"cmp"
	"context"
	"fmt"
	"slices"
	"testing"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_proto "github.com/augmentcode/augment/base/blob_names/proto"
	embeddings_proto "github.com/augmentcode/augment/services/embeddings_search_host/proto"

	embedding_search_client "github.com/augmentcode/augment/services/embeddings_search_host/client"
	proto "github.com/augmentcode/augment/services/integrations/docset/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/stretchr/testify/assert"
)

type fakeDocsetManager struct {
	blobNameToDocset map[blob_names.BlobName]string
}

func (f *fakeDocsetManager) Start(ctx context.Context) ([]*Docset, error) {
	return nil, nil
}

func (f *fakeDocsetManager) Close() error {
	return nil
}

func (f *fakeDocsetManager) GetDocsetCheckpoint(docsetVersionId string) (*blobs_proto.Blobs, error) {
	return nil, nil
}

func (f *fakeDocsetManager) GetDocsetVersionFromBlobName(blob_names.BlobName) string {
	if f.blobNameToDocset == nil {
		return "docset1_v1"
	}
	return f.blobNameToDocset[blob_names.BlobName("blob0")]
}

type fakeSearchForwarder struct {
	missingBlobNames []string
	blobNames        []string
}

func (f *fakeSearchForwarder) ForwardSearch(ctx context.Context, req *proto.SearchRequest, original_request_context *requestcontext.RequestContext) (<-chan embedding_search_client.SearchChunkResult, error) {
	ch := make(chan embedding_search_client.SearchChunkResult)
	go func() {
		if len(f.missingBlobNames) > 0 {
			ch <- embedding_search_client.SearchChunkResult{
				Resp: &embeddings_proto.SearchChunksResponse{
					Response: &embeddings_proto.SearchChunksResponse_MissingBlobs{
						MissingBlobs: &embeddings_proto.MissingBlobs{
							MissingBlobNames: f.missingBlobNames,
						},
					},
				},
			}
		}
		for _, blobName := range f.blobNames {
			ch <- embedding_search_client.SearchChunkResult{
				Resp: &embeddings_proto.SearchChunksResponse{
					Response: &embeddings_proto.SearchChunksResponse_Chunk{
						Chunk: &embeddings_proto.RetrievalChunk{
							BlobName: blobName,
							Content:  []byte("content for blob " + blobName),
						},
					},
				},
			}
		}
	}()
	return ch, nil
}

func TestGetDocSets(t *testing.T) {
	docsets := []*Docset{
		{
			Name:  "docset1",
			Title: "Docset 1",
		},
		{
			Name:  "docset2",
			Title: "Docset 2",
		},
	}
	server := NewDocSetServer(docsets, []string{"docset://default"}, &fakeDocsetManager{}, &fakeSearchForwarder{})
	ctx := context.Background()
	resp, err := server.GetDocSets(ctx, &proto.GetDocSetRequest{})
	assert.NoError(t, err)
	assert.Len(t, resp.DocSets, 2)
}

func TestGetDocSetsOrdering(t *testing.T) {
	docsets := make([]*Docset, 100)
	for i := range docsets {
		// Mix up the ordering a bit
		if i <= 50 {
			docsets[i] = &Docset{}
			docsets[i].Name = fmt.Sprintf("docset%d", 50-i)
			docsets[i].Title = fmt.Sprintf("Docset %d", 50-i)
		} else {
			docsets[i] = &Docset{}
			docsets[i].Name = fmt.Sprintf("docset%d", i)
			docsets[i].Title = fmt.Sprintf("Docset %d", i)
		}
	}
	assert.False(t, slices.IsSortedFunc(docsets, func(a, b *Docset) int {
		return cmp.Compare(a.Name, b.Name)
	}))

	server := NewDocSetServer(docsets, []string{"docset://default"}, &fakeDocsetManager{}, &fakeSearchForwarder{})
	ctx := context.Background()
	resp, err := server.GetDocSets(ctx, &proto.GetDocSetRequest{SearchPattern: ""})
	assert.NoError(t, err)
	assert.Len(t, resp.DocSets, 100)
	assert.Equal(t, "docset0", resp.DocSets[0].Name)
	assert.True(t, slices.IsSortedFunc(resp.DocSets, func(a, b *proto.DocSet) int {
		return cmp.Compare(a.Name, b.Name)
	}))
}

func TestGetDocSetsSearch(t *testing.T) {
	docsets := []*Docset{
		{
			Name:  "docset1",
			Title: "Docset 1",
		},
		{
			Name:  "docset2",
			Title: "Docset 2",
		},
	}
	server := NewDocSetServer(docsets, []string{"docset://default"}, &fakeDocsetManager{}, &fakeSearchForwarder{})
	ctx := context.Background()
	resp, err := server.GetDocSets(ctx, &proto.GetDocSetRequest{SearchPattern: "docset1"})
	assert.NoError(t, err)
	assert.Len(t, resp.DocSets, 1)
	assert.Equal(t, "docset1", resp.DocSets[0].Name)
}

func TestGetDocSetsFuzzySearch(t *testing.T) {
	docsets := []*Docset{
		{
			Name:  "docset1",
			Title: "Docset 1",
		},
		{
			Name:  "docset2",
			Title: "Docset 2",
		},
		{
			Name:  "documentationset3",
			Title: "Docset 3",
		},
	}
	server := NewDocSetServer(docsets, []string{"docset://default"}, &fakeDocsetManager{}, &fakeSearchForwarder{})
	ctx := context.Background()

	resp, err := server.GetDocSets(ctx, &proto.GetDocSetRequest{SearchPattern: "ds2"})
	assert.NoError(t, err)
	assert.Len(t, resp.DocSets, 1)
	assert.Equal(t, "docset2", resp.DocSets[0].Name)

	// Fuzzy search should rank better results first, and the worst result last
	resp, err = server.GetDocSets(ctx, &proto.GetDocSetRequest{SearchPattern: "docset"})
	assert.NoError(t, err)
	assert.Len(t, resp.DocSets, 3)
	assert.Equal(t, "documentationset3", resp.DocSets[2].Name)
}

func TestSearch_ReturnsMissingExternalSources(t *testing.T) {
	docsets := []*Docset{
		{
			DocsetId:        "docset://docset1",
			Name:            "docset1",
			Title:           "Docset 1",
			DocsetVersionId: "docset1_v1",
		},
		{
			DocsetId: "docset://docset2",
			Name:     "docset2",
			Title:    "Docset 2",
		},
		{
			DocsetId: "docset://documentationset3",
			Name:     "documentationset3",
			Title:    "Docset 3",
		},
	}
	ctx := context.Background()

	fakeSearch := &fakeSearchForwarder{
		missingBlobNames: []string{"blob0"},
		blobNames:        []string{"blob1", "blob2"},
	}
	server := NewDocSetServer(docsets, []string{"docset://default"}, &fakeDocsetManager{}, fakeSearch)
	req := &proto.SearchRequest{
		DocSetIds: []string{"docset1"},
	}
	results, err := server.searchForwarder.ForwardSearch(ctx, req, &requestcontext.RequestContext{})
	assert.NoError(t, err)

	// We get an incomplete docset
	result := <-results
	assert.NotEmpty(t, result.Resp.GetMissingBlobs())
	searchResponse := server.embeddingsChunkToDocsetResponse(&result, &auth.AugmentClaims{TenantName: "test"})
	assert.NotNil(t, searchResponse)
	assert.NotNil(t, searchResponse.Response.(*proto.SearchResponse_MissingExternalSources))
	assert.Equal(t, []string{"docset://docset1"}, searchResponse.Response.(*proto.SearchResponse_MissingExternalSources).MissingExternalSources.IncompleteExternalSourceIds)

	// But then we still get the other chunks from that docset that did get returned
	for _, blobName := range fakeSearch.blobNames {
		result = <-results
		assert.NotEmpty(t, result.Resp.GetChunk())
		searchResponse = server.embeddingsChunkToDocsetResponse(&result, &auth.AugmentClaims{TenantName: "test"})
		assert.NotNil(t, searchResponse)
		assert.NotNil(t, searchResponse.Response.(*proto.SearchResponse_Chunk))
		assert.Equal(t, searchResponse.Response.(*proto.SearchResponse_Chunk).Chunk.BlobName, blobName)
	}

	// And nothing extra
	select {
	case <-results:
		assert.Fail(t, "unexpected result")
	default:
	}
}

func TestGetImplicitDocsets(t *testing.T) {
	docsets := []*Docset{
		{
			DocsetId: "docset://default",
			Name:     "default",
			Title:    "Default",
		},
		{
			DocsetId: "docset://docset1",
			Name:     "docset1",
			Title:    "Docset 1",
			Keywords: []string{"d1"},
		},
		{
			DocsetId: "docset://docset2",
			Name:     "docset2",
			Title:    "Docset 2",
		},
		{
			DocsetId: "docset://documentationset3",
			Name:     "documentationset3",
			Title:    "Docset 3",
		},
	}
	fakeSearch := &fakeSearchForwarder{
		missingBlobNames: []string{"blob0"},
		blobNames:        []string{"blob1", "blob2"},
	}
	server := NewDocSetServer(docsets, []string{"docset://default"}, &fakeDocsetManager{}, fakeSearch)
	ctx := context.Background()
	resp, err := server.GetImplicitDocsets(ctx, &proto.GetImplicitDocsetsRequest{MessageText: "how do i use d1?"})
	assert.NoError(t, err)
	assert.Len(t, resp.DocsetIds, 2)
	assert.Contains(t, resp.DocsetIds, "docset://default")
	assert.Contains(t, resp.DocsetIds, "docset://docset1")
	assert.Len(t, resp.Docsets, 2)
	// Convert the list of Docset to the list of proto.DocSet for easy comparison
	protoDocsets := make([]*proto.DocSet, len(resp.Docsets))
	for i, docset := range resp.Docsets {
		protoDocsets[i] = &proto.DocSet{DocSetId: docset.DocSetId, Name: docset.Name, Title: docset.Title}
	}
	assert.Contains(t, protoDocsets, protoDocsets[0])
	assert.Contains(t, protoDocsets, protoDocsets[1])
}

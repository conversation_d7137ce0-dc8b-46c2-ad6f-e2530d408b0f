// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';
// the function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  local appName = 'doc-sets';

  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local requestInsightPublisher = publisherLib(cloud, env, namespace, appName);

  // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
  // in the same namespace
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');

  local bucketName = if env != 'DEV' then 'augment-data' else 'augment-data-dev';

  local dynamicFeatureFlagConfig = dynamicFeatureFlagLib.createLaunchDarklySecret(cloud, env, namespace, appName=appName);

  local config = {
    port: 50051,
    server_mtls: if mtls then serverCert.config else null,
    client_mtls: if mtls then clientCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    prom_port: 9090,
    namespace: namespace,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    embeddings_search_host: 'embeddings-search-cpu-svc:50051',
    partitioned_embeddings_search_endpoints: if namespace_config.flags.enablePartitionedEmbeddingsSearch then std.map(
      function(i) 'embeddings-search-cpu-%d-svc:50051' % i,
      std.range(0, namespace_config.flags.embeddingsSearchPartitions - 1),
    ) else [],
    docset_config: (import 'docset.jsonnet')(env),
    docset_tenant: namespace,  // the support docset_tenant
    bucket_name: bucketName,
    content_manager_endpoint: 'content-manager-svc:50051',
    concurrent_upload_count: 10,
    wait_upload_sleep_time: if env == 'DEV' then '15s' else '15m',
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
  };
  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);
  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );


  local bucketAccess = gcpLib.grantAccess(
    name='%s-bucket-policy' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'StorageBucket',
      external: bucketName,
    },
    bindings=[
      {
        role: 'roles/storage.objectViewer',
        members: [
          { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
        ],
      },
    ],
    abandon=true,
  );

  // creates a service for the pod
  // a service is needed to expose the pod to the outside world
  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  // creates a container that runs the server
  local container = {
    name: appName,
    target: {
      name: '//services/integrations/docset/server:image',
      dst: 'doc-sets',
    },
    // the arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
      '--request-insight-publisher-config-file',
      requestInsightPublisher.configFilePath,
      '--launch-darkly-secrets-file',
      dynamicFeatureFlagConfig.secretsFilePath,
    ],
    // ports that the pod exposes
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlagConfig.env,
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      centralClientCert.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      requestInsightPublisher.volumeMountDef,
      dynamicFeatureFlagConfig.volumeMountDef,
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // the liveness check is used to determine if the pod is alive
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // TODO: this could probably be shorter - we should see what we can do to
    // speed this up or move it into the background.
    startupProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      failureThreshold: 60,
      initialDelaySeconds: 60,
      periodSeconds: 30,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '2Gi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      // the client certificate is mounted into the pod
      centralClientCert.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCert.podVolumeDef,
      // the client certificate is mounted into the pod
      clientCert.podVolumeDef,
      requestInsightPublisher.podVolumeDef,
      dynamicFeatureFlagConfig.podVolumeDef,
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    deployment,
    services,
    bucketAccess,
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
    dynamicFeatureFlagConfig.k8s_objects,
  ])

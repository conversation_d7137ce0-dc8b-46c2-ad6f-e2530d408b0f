package main

import (
	"cmp"
	"context"
	"regexp"
	"slices"
	"strings"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	embeddings_search_client "github.com/augmentcode/augment/services/embeddings_search_host/client"
	embeddings_proto "github.com/augmentcode/augment/services/embeddings_search_host/proto"
	proto "github.com/augmentcode/augment/services/integrations/docset/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/lithammer/fuzzysearch/fuzzy"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	searchDocsetRequestCount = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name: "au_docset_search_docset_count",
		Help: "Histogram of the number of docsets in requests",
	}, []string{"tenant_name"})
	searchDocsetCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_docset_search_count",
		Help: "Numer of times a docset was referenced in a search",
	}, []string{"tenant_name", "docset_id"})
	searchDocsetMissingBlobHistogram = prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Name: "au_docset_search_docset_missing_blob",
		Help: "Histogram of the number of missing blobs in a search",
	}, []string{"tenant_name"})
)

var punctuationRegex = regexp.MustCompile("[[:punct:]]")

func init() {
	prometheus.MustRegister(
		searchDocsetRequestCount,
		searchDocsetCount,
		searchDocsetMissingBlobHistogram,
	)
}

type docSetServer struct {
	docsets          []*Docset
	defaultDocsetIds []string
	docsetManager    DocsetManager
	searchForwarder  SearchForwarder

	docsetNames       []string
	docsetVersionToId map[string]string
	keywordToDocsetId map[string]string

	// A default list of docsets to search if the search filter is empty. Sorted
	// by docset name, case-insensitive.
	allDocsetsSorted []*proto.DocSet
}

func NewDocSetServer(docsets []*Docset, defaultDocsetIds []string, docsetManager DocsetManager, searchForwarder SearchForwarder) *docSetServer {
	docsetNames := make([]string, len(docsets))
	docsetVersionToId := make(map[string]string)
	keywordToDocsetId := make(map[string]string)
	for i, docset := range docsets {
		docsetNames[i] = docset.Name
		docsetVersionToId[docset.DocsetVersionId] = docset.DocsetId
		// note that this assumes that keywords are unique
		for _, keyword := range docset.Keywords {
			keywordToDocsetId[strings.ToLower(keyword)] = docset.DocsetId
		}
	}

	allDocsetsSorted := make([]*proto.DocSet, len(docsets))
	for i, docset := range docsets {
		allDocsetsSorted[i] = &proto.DocSet{
			DocSetId: docset.DocsetId,
			Name:     docset.Name,
			Title:    docset.Title,
		}
	}
	slices.SortFunc(allDocsetsSorted, func(a, b *proto.DocSet) int {
		return cmp.Compare(strings.ToLower(a.Name), strings.ToLower(b.Name))
	})

	return &docSetServer{
		docsets:           docsets,
		defaultDocsetIds:  defaultDocsetIds,
		docsetManager:     docsetManager,
		searchForwarder:   searchForwarder,
		docsetNames:       docsetNames,
		docsetVersionToId: docsetVersionToId,
		keywordToDocsetId: keywordToDocsetId,
		allDocsetsSorted:  allDocsetsSorted,
	}
}

func (s *docSetServer) GetDocSets(ctx context.Context, req *proto.GetDocSetRequest) (*proto.GetDocSetResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msg("GetDocSets request")

	resp := &proto.GetDocSetResponse{}
	// TODO: also search titles?
	if req.SearchPattern == "" {
		// Special case, return them sorted alphabetically if the caller wants
		// all docsets
		resp.DocSets = s.allDocsetsSorted
		return resp, nil
	}
	matches := fuzzy.RankFindFold(req.SearchPattern, s.docsetNames)
	slices.SortFunc(matches, func(a, b fuzzy.Rank) int {
		return cmp.Compare(a.Distance, b.Distance)
	})
	resp.DocSets = make([]*proto.DocSet, len(matches))
	for i, match := range matches {
		docset := s.docsets[match.OriginalIndex]
		resp.DocSets[i] = &proto.DocSet{
			DocSetId: docset.DocsetId,
			Name:     docset.Name,
			Title:    docset.Title,
		}
	}
	return resp, nil
}

func (s *docSetServer) Search(req *proto.SearchRequest, stream proto.DocSetService_SearchServer) error {
	requestContext, err := requestcontext.FromGrpcContext(stream.Context())
	if err != nil {
		log.Error().Msg("Failed to find request context")
		return err
	}
	ctx := requestContext.AnnotateLogContext(stream.Context())

	claims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = claims.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("Search request: %v", req.DocSetIds)

	searchDocsetRequestCount.WithLabelValues(claims.TenantName).Observe(float64(len(req.DocSetIds)))
	for _, docset := range req.DocSetIds {
		searchDocsetCount.WithLabelValues(claims.TenantName, docset).Inc()
	}

	results, err := s.searchForwarder.ForwardSearch(ctx, req, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to forward search")
		return err
	}

	for result := range results {
		if result.Err != nil {
			log.Ctx(ctx).Error().Err(result.Err).Msg("Forwarded search failed")
			return result.Err
		}
		err := stream.Send(s.embeddingsChunkToDocsetResponse(&result, claims))
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to send search response")
			return err
		}
	}

	return nil
}

func (s *docSetServer) GetImplicitDocsets(ctx context.Context, req *proto.GetImplicitDocsetsRequest) (*proto.GetImplicitDocsetsResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetImplicitDocsets request")
	resp := &proto.GetImplicitDocsetsResponse{}

	uniqueDocsets := make(map[string]*Docset)

	docsetIdToDocset := make(map[string]*Docset)
	for _, docset := range s.docsets {
		docsetIdToDocset[docset.DocsetId] = docset
	}

	// Add default docsets
	for _, docsetId := range s.defaultDocsetIds {
		uniqueDocsets[docsetId] = docsetIdToDocset[docsetId]
	}

	// Add docsets that match the keywords
	for _, messageWord := range strings.Fields(punctuationRegex.ReplaceAllString(req.MessageText, " ")) {
		if docsetId, ok := s.keywordToDocsetId[strings.ToLower(messageWord)]; ok {
			uniqueDocsets[docsetId] = docsetIdToDocset[docsetId]
		}
	}

	resp.DocsetIds = make([]string, 0, len(uniqueDocsets))
	resp.Docsets = make([]*proto.DocSet, 0, len(uniqueDocsets))
	for docsetId, docset := range uniqueDocsets {
		resp.DocsetIds = append(resp.DocsetIds, docsetId)
		resp.Docsets = append(resp.Docsets, &proto.DocSet{
			DocSetId: docset.DocsetId,
			Name:     docset.Name,
			Title:    docset.Title,
		})
	}

	log.Ctx(ctx).Info().Msgf("GetImplicitDocsets: %v", resp)
	return resp, nil
}

func (s *docSetServer) embeddingsChunkToDocsetResponse(chunkResult *embeddings_search_client.SearchChunkResult, claims *auth.AugmentClaims) *proto.SearchResponse {
	var searchResponse *proto.SearchResponse
	mb := chunkResult.Resp.GetMissingBlobs()
	if mb != nil {
		var missingDocsetIds []string
		searchDocsetMissingBlobHistogram.WithLabelValues(claims.TenantName).Observe(float64(len(mb.MissingBlobNames)))
		for _, blobName := range mb.MissingBlobNames {
			docsetVersion := s.docsetManager.GetDocsetVersionFromBlobName((blob_names.BlobName(blobName)))
			docsetName := s.docsetVersionToId[docsetVersion]
			if docsetName != "" {
				missingDocsetIds = append(missingDocsetIds, docsetName)
			}
		}
		log.Info().Msgf("Missing docsets: %v", missingDocsetIds)
		searchResponse = &proto.SearchResponse{
			Response: &proto.SearchResponse_MissingExternalSources{
				MissingExternalSources: &proto.MissingExternalSources{
					IncompleteExternalSourceIds: missingDocsetIds,
				},
			},
		}
	} else {
		searchDocsetMissingBlobHistogram.WithLabelValues(claims.TenantName).Observe(0.0)
		searchResponse = &proto.SearchResponse{
			Response: &proto.SearchResponse_Chunk{
				Chunk: &embeddings_proto.RetrievalChunk{
					Content:    chunkResult.Resp.GetChunk().Content,
					Score:      chunkResult.Resp.GetChunk().Score,
					BlobName:   chunkResult.Resp.GetChunk().BlobName,
					ChunkIndex: chunkResult.Resp.GetChunk().ChunkIndex,
					Metadata:   chunkResult.Resp.GetChunk().Metadata,
				},
			},
		}
	}
	return searchResponse
}

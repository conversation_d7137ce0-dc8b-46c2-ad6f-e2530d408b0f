package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"sync"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_proto "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/rs/zerolog/log"
	"golang.org/x/sync/errgroup"
)

type UploaderTask struct {
	DocsetVersion *DocsetVersion
	Chunk         []DocsetFile
	Err           error
	Blobs         *blobs_proto.Blobs
}

type DocsetManager interface {
	Start(ctx context.Context) ([]*Docset, error)
	Close() error
	GetDocsetCheckpoint(docsetVersionId string) (*blobs_proto.Blobs, error)
	GetDocsetVersionFromBlobName(blob_names.BlobName) string
}

type DocsetManagerImpl struct {
	config   *DocsetConfig
	reader   DocsetReader
	uploader Uploader

	group errgroup.Group

	docsetTasks           []*UploaderTask
	blobNameToDocset      map[blob_names.BlobName]string
	blobNameToDocsetMutex sync.RWMutex
}

func NewDocsetManager(config *DocsetConfig, reader DocsetReader, uploader Uploader) DocsetManager {
	return &DocsetManagerImpl{
		config:           config,
		reader:           reader,
		uploader:         uploader,
		blobNameToDocset: make(map[blob_names.BlobName]string),
	}
}

func (u *DocsetManagerImpl) Close() error {
	return nil
}

func (u *DocsetManagerImpl) GetDocsetCheckpoint(docsetVersionId string) (*blobs_proto.Blobs, error) {
	for _, docsetTask := range u.docsetTasks {
		if docsetTask.DocsetVersion.DocsetVersionId == docsetVersionId {
			return docsetTask.Blobs, nil
		}
	}
	return nil, nil
}

func (u *DocsetManagerImpl) Start(ctx context.Context) ([]*Docset, error) {
	collection := u.config.VersionCollection
	collectionInfo, err := u.reader.Download(collection.ObjectStorePath, collection.ChecksumSha256, "")
	if err != nil {
		return nil, err
	}

	var docsetVersions []DocsetVersion
	err = json.Unmarshal(collectionInfo, &docsetVersions)
	if err != nil {
		return nil, err
	}

	u.group = errgroup.Group{}

	prefix := filepath.Dir(collection.ObjectStorePath)

	for _, docsetVersion := range docsetVersions {
		log.Ctx(ctx).Info().Msgf("Found docset version %v", docsetVersion.DocsetVersionId)
		found := false
		for _, docset := range u.config.Docsets {
			log.Ctx(ctx).Info().Msgf("Checking docset %v", docset.DocsetId)
			// only upload docsets that are in the config, but the current version might not be in the config
			// this is to allow background uploading of new versions while serving the old version
			if docset.DocsetId == docsetVersion.DocsetId {
				found = true
				break
			}
		}
		if !found {
			log.Ctx(ctx).Info().Msgf("Skipping docset version %v as it is not in the docset config", docsetVersion.DocsetVersionId)
			continue
		}
		log.Ctx(ctx).Info().Msgf("Starting docset version %v", docsetVersion.DocsetVersionId)

		task := &UploaderTask{DocsetVersion: &docsetVersion}
		u.docsetTasks = append(u.docsetTasks, task)
		u.group.Go(func() error {
			blobs, blobNames, err := u.uploader.RegisterDocset(ctx, task.DocsetVersion, prefix)
			if err != nil {
				task.Err = err
				return err
			}
			u.blobNameToDocsetMutex.Lock()
			defer u.blobNameToDocsetMutex.Unlock()
			for _, blobName := range blobNames {
				u.blobNameToDocset[blobName] = task.DocsetVersion.DocsetVersionId
			}
			task.Blobs = blobs
			return nil
		})
	}

	docsets := make([]*Docset, len(u.config.Docsets))
	for i, docset := range u.config.Docsets {
		var foundVersion *DocsetVersion
		for _, docsetVersion := range docsetVersions {
			if docsetVersion.DocsetVersionId == docset.DocsetVersionId {
				foundVersion = &docsetVersion
				break
			}
		}
		if foundVersion == nil {
			log.Ctx(ctx).Error().Msgf("Docset version %v not found in collection", docset.DocsetVersionId)
			return nil, fmt.Errorf("docset version %v not found in collection", docset.DocsetVersionId)
		}
		if foundVersion.Title == "" {
			log.Ctx(ctx).Error().Msgf("Docset version %v has no title", docset.DocsetVersionId)
			return nil, errors.New("docset version has no title")
		}
		docsets[i] = &Docset{
			DocsetId:        docset.DocsetId,
			Name:            foundVersion.Name,
			Title:           foundVersion.Title,
			DocsetVersionId: docset.DocsetVersionId,
			Keywords:        foundVersion.Keywords,
		}
	}

	return docsets, u.group.Wait()
}

func (u *DocsetManagerImpl) GetDocsetVersionFromBlobName(blob blob_names.BlobName) string {
	u.blobNameToDocsetMutex.RLock()
	defer u.blobNameToDocsetMutex.RUnlock()
	return u.blobNameToDocset[blob]
}

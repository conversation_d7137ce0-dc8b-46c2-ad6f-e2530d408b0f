"""A Python client library call a route guide service."""

import logging
from dataclasses import dataclass
from typing import Iterable, Sequence


import grpc
import numpy

from base.python.grpc import client_options
from services.integrations.docset import docset_pb2, docset_pb2_grpc
from services.lib.request_context.request_context import RequestContext
from services.embeddings_search_host.client.client import (
    EmbeddingsSearchChunksResult,
    to_tensor,
)


@dataclass
class DocsetSearchChunksResult:
    """A dataclass holding the results of an docset Search() call."""

    @dataclass
    class RetrievalChunk:
        """A dataclass holding a retrieval chunk in a result.

        This class mirrors the protobuf definition.
        """

        content: bytes
        score: float
        blob_name: str
        chunk_index: int
        metadata: list[tuple[str, str]]

    @dataclass
    class MissingExternalSources:
        """A dataclass holding the missing external sources in a result.

        This class mirrors the protobuf definition.
        """

        incomplete_external_source_ids: list[str]
        """
        List of all external source ids that could not be loaded for the search.
        """

    chunks: Sequence[RetrievalChunk]
    missing_external_sources: MissingExternalSources

    @classmethod
    def from_proto(
        cls, msgs: Iterable[docset_pb2.SearchResponse]
    ) -> "DocsetSearchChunksResult":
        """Convenience function to convert a stream of SearchResponse protos to a DocsetSearchChunksResult object."""

        def responses():
            for msg in msgs:
                if msg.HasField("chunk"):
                    yield cls.RetrievalChunk(
                        content=msg.chunk.content,
                        score=msg.chunk.score,
                        blob_name=msg.chunk.blob_name,
                        chunk_index=msg.chunk.chunk_index,
                        metadata=[(x.key, x.value) for x in msg.chunk.metadata],
                    )
                elif msg.HasField("missing_external_sources"):
                    yield cls.MissingExternalSources(
                        incomplete_external_source_ids=list(
                            msg.missing_external_sources.incomplete_external_source_ids
                        ),
                    )
                else:
                    raise ValueError("Invalid SearchResponse")

        # Read the whole stream
        chunks = []
        incomplete_external_source_ids = []
        for resp in responses():
            if isinstance(resp, cls.RetrievalChunk):
                chunks.append(resp)
            elif isinstance(resp, cls.MissingExternalSources):
                incomplete_external_source_ids.extend(
                    resp.incomplete_external_source_ids
                )
            else:
                raise ValueError("Invalid SearchResponse")

        # Build up the query_results
        return cls(
            chunks=chunks,
            missing_external_sources=cls.MissingExternalSources(
                incomplete_external_source_ids=incomplete_external_source_ids
            ),
        )


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> docset_pb2_grpc.DocSetServiceStub:
    """Setup the client stub for the docset service.

    Args:
        endpoint: The endpoint of the docset service.
        credentials: The credentials to use for the channel (optional)

    Returns:
        The client stub for the docset.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = docset_pb2_grpc.DocSetServiceStub(channel)
    return stub


class DocSetClient:
    """Class to call docset APIs remotely."""

    def __init__(self, stub: docset_pb2_grpc.DocSetServiceStub):
        """Constructs a new docset client."""
        self.stub = stub

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new docset client from endpoint and credentials.

        Args:
            endpoint: The endpoint of the docset service.
            credentials: The credentials to use for the channel (optional)
            options: The options to use for the channel (optional)

        Returns:
            The client stub for the route guide.
        """
        stub = setup_stub(endpoint, credentials, options=options)
        return cls(stub)

    def get_doc_sets(
        self,
        search_pattern: str,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> list[docset_pb2.DocSet]:
        """Get DocSets.

        Args:
            search_pattern: The search pattern to use.
            request_context: The request context to use.
            timeout: The timeout to use.

        Returns:
            The docsets.
        """
        response = self.stub.GetDocSets(
            docset_pb2.GetDocSetRequest(search_pattern=search_pattern),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return list(response.doc_sets)

    def search(
        self,
        doc_set_ids: list[str],
        query: numpy.ndarray,
        num_results: int,
        transformation_key: str,
        request_context: RequestContext,
        search_timeout_ms: int = 1000,
        timeout: float = 30,
    ) -> DocsetSearchChunksResult:
        """Performs a search for the given queries.

        Args:
            query: A 2d-numpy array containing a single query of a given
                   dimension. The query is usually the results of an embedder
                   calculation
            doc_set_ids: The list of doc set IDs to search.
            num_results: the number of top-k results to return
            transformation_key: The transformation key under which the embeddings are
                                stored in the content manager
            request_id: the request id used for tracking and metrics
            timeout: timeout in seconds
        """
        request = docset_pb2.SearchRequest(
            doc_set_ids=doc_set_ids,
            query=to_tensor(query),
            num_results=num_results,
            transformation_key=transformation_key,
            # Keep this independent of the gRPC timeout for now, since they have
            # different behaviors (gRPC timeout is a cancel error, this timeout
            # currently means incomplete search results)
            search_timeout_ms=search_timeout_ms,
        )
        logging.debug("request %s", request)
        response = self.stub.Search(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        response = list(response)
        logging.debug("response %s", response)

        return DocsetSearchChunksResult.from_proto(msgs=response)

    def get_implicit_docsets(
        self,
        message_text: str,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> list[docset_pb2.DocSet]:
        """Get docsets that are implicitly mentioned in the message but not explicitly tagged.
        Args:
            message_text: The message text to search for implicit docsets.
            request_context: The request context to use.
            timeout: The timeout to use.
        Returns:
            The docset objects matching the message.
        """
        response = self.stub.GetImplicitDocsets(
            docset_pb2.GetImplicitDocsetsRequest(message_text=message_text),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return list(response.docsets)

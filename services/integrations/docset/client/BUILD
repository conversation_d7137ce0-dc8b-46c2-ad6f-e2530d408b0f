load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/python/grpc:client_options",
        "//services/embeddings_search_host/client",
        "//services/integrations/docset:docset_py_proto",
        "//services/lib/request_context:request_context_py",
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["docset_client.rs"],
    aliases = aliases(),
    crate_name = "docset_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = [
        "//services:__subpackages__",
    ],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/rust/tracing-tonic",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/integrations/docset:docset_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

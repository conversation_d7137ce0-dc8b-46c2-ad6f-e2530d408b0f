[package]
name = "docset_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "docset_client"
path = "docset_client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
grpc_client = { path = "../../../lib/grpc/client" }
request_context = { path = "../../../lib/request_context" }
prost = { workspace = true }
tokio = { workspace = true }
tonic = { workspace = true }
tonic-build = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../../base/rust/tracing-tonic" }

[build-dependencies]
tonic-build = { workspace = true }

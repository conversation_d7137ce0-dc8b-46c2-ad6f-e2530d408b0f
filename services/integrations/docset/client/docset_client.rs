use std::{sync::Arc, time::Duration};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;
use tonic::transport::ClientTlsConfig;

use tracing_tonic::client::TracingService;

use request_context::RequestContext;

use crate::proto::docset::{
    doc_set_service_client::DocSetServiceClient, DocSet, GetDocSetRequest,
    GetImplicitDocsetsRequest,
};

pub mod proto {
    pub mod base {
        pub mod blob_names {
            tonic::include_proto!("base.blob_names");
        }
    }
    pub mod tensor {
        tonic::include_proto!("tensor");
    }
    pub mod embeddings_search {
        tonic::include_proto!("embeddings_search");
    }
    pub mod docset {
        tonic::include_proto!("docset");
    }
}

#[async_trait]
pub trait DocsetClient {
    async fn get_doc_sets(
        &self,
        request_context: &RequestContext,
        search_pattern: &str,
    ) -> tonic::Result<Vec<DocSet>>;

    async fn get_implicit_docsets(
        &self,
        request_context: &RequestContext,
        message_text: &str,
    ) -> tonic::Result<Vec<DocSet>>;
}

#[derive(Clone)]
pub struct DocSetClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Duration,
    client: Arc<Mutex<Option<DocSetServiceClient<TracingService>>>>,
}

#[async_trait]
impl DocsetClient for DocSetClientImpl {
    async fn get_doc_sets(
        &self,
        request_context: &RequestContext,
        search_pattern: &str,
    ) -> tonic::Result<Vec<DocSet>> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("docset client not ready: {}", e);
            tonic::Status::unavailable("docset not ready")
        })?;
        let mut request = tonic::Request::new(GetDocSetRequest {
            search_pattern: search_pattern.to_string(),
        });
        request_context.annotate(request.metadata_mut());

        let response = client.get_doc_sets(request).await?;

        Ok(response.into_inner().doc_sets)
    }

    async fn get_implicit_docsets(
        &self,
        request_context: &RequestContext,
        message_text: &str,
    ) -> tonic::Result<Vec<DocSet>> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("docset client not ready: {}", e);
            tonic::Status::unavailable("docset not ready")
        })?;
        let mut request = tonic::Request::new(GetImplicitDocsetsRequest {
            message_text: message_text.to_string(),
        });
        request_context.annotate(request.metadata_mut());

        let response = client.get_implicit_docsets(request).await?;

        Ok(response.into_inner().docsets)
    }
}

impl DocSetClientImpl {
    pub fn new(
        endpoint: &str,
        tls_config: Option<ClientTlsConfig>,
        request_timeout: Duration,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            request_timeout,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<DocSetServiceClient<TracingService>, tonic::transport::Error> {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    Some(self.request_timeout),
                    &self.tls_config,
                )
                .await?;
                let client = DocSetServiceClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

pub struct MockDocSetClient {
    pub doc_sets: Vec<DocSet>,
}

impl MockDocSetClient {
    pub fn new(doc_sets: Vec<DocSet>) -> Self {
        Self { doc_sets }
    }
}

#[async_trait]
impl DocsetClient for MockDocSetClient {
    async fn get_doc_sets(
        &self,
        _request_context: &RequestContext,
        search_pattern: &str,
    ) -> tonic::Result<Vec<DocSet>> {
        match search_pattern {
            "" => Ok(self.doc_sets.clone()),
            _ => Ok(self
                .doc_sets
                .iter()
                .filter(|doc_set| {
                    doc_set.doc_set_id.contains(search_pattern)
                        || doc_set.title.contains(search_pattern)
                })
                .cloned()
                .collect::<Vec<DocSet>>()),
        }
    }

    async fn get_implicit_docsets(
        &self,
        _request_context: &RequestContext,
        _message_text: &str,
    ) -> tonic::Result<Vec<DocSet>> {
        Ok(vec![])
    }
}

load("//tools/bzl:go.bzl", "go_binary", "go_library")
load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "util",
    srcs = [
        "util.py",
    ],
    deps = [
        "//base/logging:console_logging",
        "//services/integrations/docset/client:client_py",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/grpc:token_parser",
        "//services/lib/request_context:request_context_py",
    ],
)

go_library(
    name = "docset_index_util_lib",
    srcs = ["docset_index_util.go"],
    importpath = "github.com/augmentcode/augment/services/integrations/docset/util",
    visibility = ["//visibility:private"],
    deps = [
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_spf13_cobra//:cobra",
        "@com_google_cloud_go_storage//:storage",
    ],
)

go_binary(
    name = "docset_index_util",
    embed = [":docset_index_util_lib"],
)

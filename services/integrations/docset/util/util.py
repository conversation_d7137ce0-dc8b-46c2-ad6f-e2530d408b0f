"""A small tool to issue requests against the doc sets API."""

import argparse
import logging
import sys

import pydantic

from base.logging.console_logging import setup_console_logging
from services.lib.grpc import grpc_args_parser, token_parser
from services.lib.request_context.request_context import RequestContext
from services.integrations.docset.client import client as docset_client


def _get_request_context(token: pydantic.SecretStr) -> RequestContext:
    """Get the request context."""
    return RequestContext.create(auth_token=token)


def get_doc_sets(
    args: argparse.Namespace,
    rpc_client: docset_client.DocSetClient,
    token: pydantic.SecretStr,
):
    """Get the list of docsets.

    Args:
        args: The parsed command line arguments.
        rpc_client: The RPC client.

    Returns:
        None
    """
    request_context = _get_request_context(token)
    response = rpc_client.get_doc_sets(
        args.search_pattern,
        request_context=request_context,
    )
    for doc_set in response:
        logging.info("%s - %s", doc_set.doc_set_id, doc_set.title)


def main():
    """Main function."""
    setup_console_logging(add_timestamp=False)
    parser = argparse.ArgumentParser()

    # Add arguments
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)

    # Add subparsers
    subparsers = parser.add_subparsers()
    get_feature_parser = subparsers.add_parser("get-doc-sets")
    get_feature_parser.set_defaults(action=get_doc_sets)
    get_feature_parser.add_argument("--search-pattern", type=str, required=False)

    parser.set_defaults(action=None)

    args = parser.parse_args()
    try:
        # Create the client by setting up a port forwarding to the service.
        with grpc_args_parser.create_client(
            args,
            docset_client.DocSetClient.create_for_endpoint,
            default_service_name="doc-sets-svc",
            default_endpoint="doc-sets-svc:50051",
        ) as rpc_client:
            token = token_parser.get_token(args)
            if not args.action:
                parser.print_help()
            else:
                args.action(args, rpc_client, token)
    except KeyboardInterrupt:
        sys.exit(1)
    except argparse.ArgumentError as e:
        logging.error("%s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()

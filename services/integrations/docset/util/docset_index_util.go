package main

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"cloud.google.com/go/storage"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
)

type file struct {
	Name     string `json:"name"`
	BlobName string `json:"blob_name"`
	Checksum string `json:"checksum"`
}

type index struct {
	Files []file `json:"files"`
}

type docsetCollectionIndex []struct {
	DocsetID      string   `json:"docset_id"`
	Category      string   `json:"category"`
	Name          string   `json:"name"`
	Title         string   `json:"title"`
	DocsetVersion string   `json:"docset_version"`
	Checksum      string   `json:"checksum"`
	Keywords      []string `json:"keywords"`
}

func getDocsetCollectionIndex(cmd *cobra.Command, args []string) docsetCollectionIndex {
	dir := args[0]

	bucketName, err := cmd.Flags().GetString("bucket")
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get bucket flag")
	}

	ctx := context.Background()
	client, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create GCS client")
	}
	defer client.Close()

	bucket := client.Bucket(bucketName)
	indexFilePath := filepath.Join(dir, "docset_collection_index.json")
	indexFile := bucket.Object(indexFilePath)
	reader, err := indexFile.NewReader(ctx)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to read file %s", indexFilePath)
	}
	defer reader.Close()

	var docsetCollectionIndex docsetCollectionIndex
	err = json.NewDecoder(reader).Decode(&docsetCollectionIndex)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to unmarshal JSON")
	}

	return docsetCollectionIndex
}

func main() {
	rootCmd := &cobra.Command{
		Use:   "docset_index_util",
		Short: "Util to create and upload docset indexes",
	}

	uploadCmd := &cobra.Command{
		Use:   "upload [directory]",
		Short: "Upload a directory to a GCS bucket",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			dir := args[0]
			bucketName, err := cmd.Flags().GetString("bucket")
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to get bucket flag")
			}
			overwrite, err := cmd.Flags().GetBool("overwrite")
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to get overwrite flag")
			}
			dryRun, err := cmd.Flags().GetBool("dry-run")
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to get dry-run flag")
			}
			objectPrefix, err := cmd.Flags().GetString("object-prefix")
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to get object-prefix flag")
			}

			ctx := context.Background()
			client, err := storage.NewClient(ctx)
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to create GCS client")
			}
			defer client.Close()

			bucket := client.Bucket(bucketName)

			docsetName := filepath.Base(dir)
			objectStorePrefix := filepath.Join(objectPrefix, docsetName)

			indexFilePath := filepath.Join(dir, "index.json")
			indexFileData, err := os.ReadFile(indexFilePath)
			if err != nil {
				log.Fatal().Err(err).Msgf("Failed to read file %s", indexFilePath)
			}

			hash := sha256.New()
			hash.Write(indexFileData)
			checksumSHA256 := fmt.Sprintf("%x", hash.Sum(nil))

			err = filepath.Walk(dir, func(filePath string, info os.FileInfo, err error) error {
				if err != nil {
					return err
				}
				if !info.IsDir() {
					fileData, err := os.ReadFile(filePath)
					if err != nil {
						log.Fatal().Err(err).Msgf("Failed to read file %s", filePath)
					}

					objName := strings.TrimPrefix(filePath, dir)
					objName = filepath.Join(objectStorePrefix, objName)

					if !dryRun {
						obj := bucket.Object(objName)
						w := obj.NewWriter(ctx)
						if !overwrite {
							_, err := obj.Attrs(ctx)
							if err == nil {
								log.Info().Msgf("File %s already exists in bucket %s, skipping", objName, bucketName)
								return nil
							}
						}
						_, err = w.Write(fileData)
						if err != nil {
							log.Fatal().Err(err).Msgf("Failed to write file %s to bucket %s", objName, bucketName)
						}
						err = w.Close()
						if err != nil {
							log.Fatal().Err(err).Msgf("Failed to close file %s to bucket %s", objName, bucketName)
						}
					}
					log.Info().Msgf("Uploaded file %s to bucket %s", objName, bucketName)
				}
				return nil
			})
			if err != nil {
				log.Fatal().Err(err).Msgf("Failed to walk directory %s", dir)
			}

			log.Info().Msgf(`
{
	docset_version_id: '%s',
	object_store_prefix: '%s',
	checksum_sha256: '%s',
}`, docsetName, objectStorePrefix, checksumSHA256)
		},
	}

	uploadCmd.Flags().StringP("bucket", "b", "augment-data-dev", "GCS bucket to upload to")
	uploadCmd.Flags().StringP("object-prefix", "p", "docsets", "Prefix to add to object names")
	uploadCmd.Flags().BoolP("overwrite", "o", false, "Overwrite files in the bucket if they already exist")
	uploadCmd.Flags().BoolP("dry-run", "d", false, "Dry run, don't actually upload anything")

	createCmd := &cobra.Command{
		Use:   "create [directory]",
		Short: "Create an index.json file in the specified directory",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			dir := args[0]
			log.Info().Msgf("Creating index.json in %s", dir)

			const filesDirName = "files"

			filesDir := filepath.Join(dir, filesDirName)
			files, err := os.ReadDir(filesDir)
			if err != nil {
				log.Fatal().Err(err).Msgf("Failed to read directory %s", filesDir)
			}

			idx := index{
				Files: make([]file, len(files)),
			}

			for i, f := range files {
				if !f.IsDir() {
					filePath := filepath.Join(filesDir, f.Name())
					fileData, err := os.ReadFile(filePath)
					if err != nil {
						log.Fatal().Err(err).Msgf("Failed to read file %s", filePath)
					}
					// check size so that content manager can nicely work with it
					if len(fileData) > 2000000 {
						log.Fatal().Msgf("File %s is too large", filePath)
					}

					hash := sha256.New()
					hash.Write(fileData)
					checksum := fmt.Sprintf("%x", hash.Sum(nil))

					idx.Files[i] = file{
						Name:     filepath.Join(filesDirName, f.Name()),
						BlobName: checksum,
						Checksum: checksum,
					}
				}
			}

			jsonData, err := json.MarshalIndent(idx, "", "  ")
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to marshal JSON")
			}

			indexPath := filepath.Join(dir, "index.json")
			err = os.WriteFile(indexPath, jsonData, 0o644)
			if err != nil {
				log.Fatal().Err(err).Msgf("Failed to write %s", indexPath)
			}

			log.Info().Msgf("Created %s", indexPath)
		},
	}

	dumpDocsetVersionsCmd := &cobra.Command{
		// note that we require the output file's absolute path here, which is a pain. unfortunately, this is necessary bc this command
		// is typically run through bazel. bazel runs this command inside a sandbox, meaning a relative path would be relative to the sandbox,
		// not this source repo.
		Use:     "dump-docset-versions [directory] [output-file-abs-path]",
		Short:   "Dump and reformat the docset versions from the docset_collection_index.json file from the specified directory to the specified output file absolute path.",
		Example: "bazel run //services/integrations/docset/util:docset_index_util -- dump-docset-versions docsets/json_docs_20240821 ~/augment/augment/services/integrations/docset/server/docset_versions_dump.json",
		Args:    cobra.ExactArgs(2),
		Run: func(cmd *cobra.Command, args []string) {
			dir := args[0]
			outputFileAbsPath := args[1]

			docsetCollectionIndex := getDocsetCollectionIndex(cmd, args)

			// output struct
			output := []struct {
				DocsetVersionID   string `json:"docset_version_id"`
				ObjectStorePrefix string `json:"object_store_prefix"`
				ChecksumSha256    string `json:"checksum_sha256"`
			}{}

			for _, docset := range docsetCollectionIndex {
				output = append(output, struct {
					DocsetVersionID   string `json:"docset_version_id"`
					ObjectStorePrefix string `json:"object_store_prefix"`
					ChecksumSha256    string `json:"checksum_sha256"`
				}{DocsetVersionID: docset.DocsetVersion, ObjectStorePrefix: filepath.Join(dir, docset.DocsetVersion), ChecksumSha256: docset.Checksum})
			}

			outputJSON, err := json.MarshalIndent(output, "", "  ")
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to marshal JSON")
			}

			// add newline to appease linter
			outputJSON = append(outputJSON, '\n')

			err = os.WriteFile(outputFileAbsPath, outputJSON, 0o644)
			if err != nil {
				log.Fatal().Err(err).Msgf("Failed to write file %s", outputFileAbsPath)
			}

			log.Info().Msgf("Dumped index file to %s", outputFileAbsPath)
		},
	}

	dumpDocsetVersionsCmd.Flags().StringP("bucket", "b", "augment-data-dev", "GCS bucket to read from")

	dumpDocsetsCmd := &cobra.Command{
		Use:     "dump-docsets [directory] [output-file-abs-path]",
		Short:   "Dump and reformat the docsets from docset_collection_index.json file from the specified directory to the specified output file absolute path.",
		Example: "bazel run //services/integrations/docset/util:docset_index_util -- dump-docsets docsets/json_docs_20240821 ~/augment/augment/services/integrations/docset/server/docsets_dump.json",
		Args:    cobra.ExactArgs(2),
		Run: func(cmd *cobra.Command, args []string) {
			outputFileAbsPath := args[1]

			docsetCollectionIndex := getDocsetCollectionIndex(cmd, args)

			// output struct
			output := []struct {
				DocsetID        string   `json:"docset_id"`
				Name            string   `json:"name"`
				Category        string   `json:"category"`
				Title           string   `json:"title"`
				DocsetVersionID string   `json:"docset_version_id"`
				Keywords        []string `json:"keywords"`
			}{}

			for _, docset := range docsetCollectionIndex {
				keywords := docset.Keywords

				output = append(output, struct {
					DocsetID        string   `json:"docset_id"`
					Name            string   `json:"name"`
					Category        string   `json:"category"`
					Title           string   `json:"title"`
					DocsetVersionID string   `json:"docset_version_id"`
					Keywords        []string `json:"keywords"`
				}{DocsetID: docset.DocsetID, Name: docset.Name, Category: docset.Category, Title: docset.Title, DocsetVersionID: docset.DocsetVersion, Keywords: keywords})
			}

			outputJSON, err := json.MarshalIndent(output, "", "  ")
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to marshal JSON")
			}

			// add newline to appease linter
			outputJSON = append(outputJSON, '\n')

			err = os.WriteFile(outputFileAbsPath, outputJSON, 0o644)
			if err != nil {
				log.Fatal().Err(err).Msgf("Failed to write file %s", outputFileAbsPath)
			}

			log.Info().Msgf("Dumped index file to %s", outputFileAbsPath)
		},
	}

	dumpDocsetsCmd.Flags().StringP("bucket", "b", "augment-data-dev", "GCS bucket to read from")

	rootCmd.AddCommand(createCmd)
	rootCmd.AddCommand(uploadCmd)
	rootCmd.AddCommand(dumpDocsetVersionsCmd)
	rootCmd.AddCommand(dumpDocsetsCmd)

	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	if err := rootCmd.Execute(); err != nil {
		log.Fatal().Err(err).Msg("Failed to execute command")
	}
}

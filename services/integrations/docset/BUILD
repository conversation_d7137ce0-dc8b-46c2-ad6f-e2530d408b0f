load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "docset_proto",
    srcs = ["docset.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//base/proto:tensor_proto",
        "//services/embeddings_search_host:embeddings_search_proto",
    ],
)

py_grpc_library(
    name = "docset_py_proto",
    protos = [":docset_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/proto:tensor_py_proto",
        "//services/embeddings_search_host:embeddings_search_py_proto",
    ],
)

go_grpc_library(
    name = "docset_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/docset/proto",
    proto = ":docset_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/proto:tensor_go_proto",
        "//services/embeddings_search_host:embeddings_search_go_proto",
    ],
)

"""Unit tests for GitHub API tool."""

import json
import pytest
from unittest.mock import Mock, patch, ANY

from services.integrations.github.agent_tools.config import GitHubExtraToolInput
from services.integrations.github.agent_tools.github_api_tool import (
    GitHubAPITool,
)
from services.integrations.github.agent_tools.config import GitHubConfig
from services.lib.request_context.request_context import RequestContext

from pydantic import ValidationError
from services.integrations.github.processor import processor_pb2
from services.integrations.github.agent_tools.github_api_tool import GitHubAPIInput


class MockResponse:
    """Mock the GitHub processor response format."""

    def __init__(self, response_data, status_code=200):
        if isinstance(response_data, str):
            self.response = response_data
        else:
            self.response = json.dumps(response_data)  # Convert dict to JSON string
        self.status_code = status_code


# Mock responses based on real API calls
MOCK_RESPONSES = {
    "/repos/microsoft/vscode": {
        "method": "GET",
        "status": 200,
        "response": {
            "id": 41881900,
            "name": "vscode",
            "full_name": "microsoft/vscode",
            "private": False,
            "description": "Visual Studio Code",
            "fork": False,
            "language": "TypeScript",
            "stargazers_count": 167242,
        },
    },
    "/repos/microsoft/vscode/pulls": {
        "method": "GET",
        "status": 200,
        "response": [
            {
                "number": 240594,
                "title": "Dispose more handlers, ref #239965",
                "user": {"login": "rzhao271"},
                "updated_at": "2025-02-12T23:54:02Z",
                "html_url": "https://github.com/microsoft/vscode/pull/240594",
            },
            {
                "number": 240591,
                "title": "Add Chat NotebookEdit API",
                "user": {"login": "DonJayamanne"},
                "updated_at": "2025-02-12T23:36:51Z",
                "html_url": "https://github.com/microsoft/vscode/pull/240591",
            },
        ],
    },
    "/search/issues": {
        "method": "GET",
        "status": 200,
        "response": {
            "total_count": 502,
            "incomplete_results": False,
            "items": [
                {
                    "number": 240594,
                    "title": "Dispose more handlers, ref #239965",
                    "user": {"login": "rzhao271"},
                    "html_url": "https://github.com/microsoft/vscode/pull/240594",
                }
            ],
        },
    },
}

# Mock error responses
MOCK_ERROR_RESPONSES = {
    "/repos/microsoft/vscode/settings": {
        "method": "GET",
        "status": 404,
        "error": "Not Found",
    },
    "/repos/microsoft/vscode/pulls/1": {
        "method": "PATCH",
        "status": 400,
        "error": "Invalid input",
    },
    "/orgs/microsoft/repos": {"method": "POST", "status": 403, "error": "Forbidden"},
}


def mock_github_processor_request(request_context, request):
    """Mock the GitHub processor request method"""
    # Check for error responses first
    if request.path in MOCK_ERROR_RESPONSES:
        error_data = MOCK_ERROR_RESPONSES[request.path]
        if (request.method == 0 and error_data["method"] == "GET") or (
            request.method == 1 and error_data["method"] == "POST"
        ):
            return MockResponse({"error": error_data["error"]}, error_data["status"])

    # Check for success responses
    if request.path in MOCK_RESPONSES:
        data = MOCK_RESPONSES[request.path]
        if (request.method == 0 and data["method"] == "GET") or (
            request.method == 1 and data["method"] == "POST"
        ):
            return MockResponse(data["response"], data["status"])

    # Default to 404 for unknown paths
    return MockResponse({"error": "Not Found"}, 404)


@pytest.fixture
def github_tool():
    """Create a GitHubAPITool instance with mocked processor client."""
    with patch(
        "services.integrations.github.agent_tools.github_api_tool.GithubProcessorClient"
    ) as MockClient:
        # Create a mock instance
        mock_client = Mock()
        mock_client.call_github_api.side_effect = mock_github_processor_request

        # Make the MockClient return our configured mock instance
        MockClient.return_value = mock_client

        # Create config with explicit settings
        config = GitHubConfig(
            use_whitelist_on_get=False,  # Default setting - GET requests bypass whitelist
            allowed_paths=[  # Explicit subset of paths used in tests
                r"^/repos/[^/]+/[^/]+$",
                r"^/repos/[^/]+/[^/]+/pulls$",
                r"^/search/issues$",
                r"^/repos/[^/]+/[^/]+/issues/\d+/comments$",
                r"^/orgs/[^/]+/repos$",
            ],
        )

        # Create the tool with our mocked client and explicit config
        tool = GitHubAPITool(config, mock_client)
        return tool


@pytest.fixture
def github_tool_configurable():
    """Create a GitHubAPITool instance with configurable whitelist settings."""

    def _create_tool(use_whitelist_on_get: bool, allowed_paths: list[str]):
        with patch(
            "services.integrations.github.agent_tools.github_api_tool.GithubProcessorClient"
        ) as MockClient:
            mock_client = Mock()
            mock_client.call_github_api.side_effect = mock_github_processor_request
            MockClient.return_value = mock_client

            config = GitHubConfig()
            config.use_whitelist_on_get = use_whitelist_on_get
            config.allowed_paths = allowed_paths
            tool = GitHubAPITool(config, mock_client)
            return tool

    return _create_tool


@pytest.fixture
def request_context():
    """Create a test request context."""
    return RequestContext.create(request_source="test")


@pytest.fixture
def extra_input():
    """Create test extra input with API key."""
    return GitHubExtraToolInput.from_fields(
        api_token="test-api-key"
    )  # pragma: allowlist secret


class TestGitHubAPITool:
    """Test suite for GitHubAPITool."""

    def test_get_repository_info(self, github_tool, request_context, extra_input):
        """Test getting repository information with and without details."""
        # Test with details=False (default) - should only show essential fields
        result = github_tool.run(
            {"method": "GET", "path": "/repos/microsoft/vscode", "data": {}},
            extra_input,
            request_context,
        )
        # Should include essential fields
        assert "name: vscode" in result
        # Should not include non-essential fields
        assert "language" not in result

        # Test with details=True - should show all fields
        result = github_tool.run(
            {
                "method": "GET",
                "path": "/repos/microsoft/vscode",
                "data": {},
                "details": True,
            },
            extra_input,
            request_context,
        )
        # Should include both essential and non-essential fields
        assert "name: vscode" in result
        assert "language: TypeScript" in result
        github_tool.github_processor_client.call_github_api.assert_called()

    def test_list_pull_requests(self, github_tool, request_context, extra_input):
        """Test listing pull requests."""
        result = github_tool.run(
            {
                "method": "GET",
                "path": "/repos/microsoft/vscode/pulls",
                "data": {"state": "open", "per_page": 3},
                "details": True,  # Need details to see titles
            },
            extra_input,
            request_context,
        )

        # Check formatted output contains expected items
        assert (
            "title: 'Dispose more handlers" in result
        )  # Note the single quotes in YAML format
        assert "user" in result and "login: DonJayamanne" in result
        github_tool.github_processor_client.call_github_api.assert_called_once()

    def test_search_issues(self, github_tool, request_context, extra_input):
        """Test searching issues."""
        result = github_tool.run(
            {
                "method": "GET",
                "path": "/search/issues",
                "data": {"q": "repo:microsoft/vscode is:pr is:open"},
                "details": True,  # Need details to see titles
            },
            extra_input,
            request_context,
        )

        # Check formatted output contains expected data
        assert "total_count: 502" in result
        assert (
            "title: 'Dispose more handlers" in result
        )  # Note the single quotes in YAML format
        github_tool.github_processor_client.call_github_api.assert_called_once()

    def test_response_filtering(self, github_tool, request_context, extra_input):
        """Test that response filtering works correctly with details parameter."""
        # Test with details=False
        result = github_tool.run(
            {"method": "GET", "path": "/repos/microsoft/vscode", "data": {}},
            extra_input,
            request_context,
        )
        # Should only include essential fields
        assert "name: vscode" in result
        # Should not include non-essential fields
        assert "language" not in result
        assert "stargazers_count" not in result

        # Test with details=True
        result = github_tool.run(
            {
                "method": "GET",
                "path": "/repos/microsoft/vscode",
                "data": {},
                "details": True,
            },
            extra_input,
            request_context,
        )
        # Should include all non-trivial fields
        assert "name: vscode" in result
        assert "language: TypeScript" in result
        assert "stargazers_count: " in result

    def test_robust_filtering_for_essential_fields(self, github_tool):
        """Test that essential fields don't become empty after filtering."""
        # Create a test response with nested essential fields
        test_response = {
            "items": [
                {
                    "title": "Important PR",
                    "user": {
                        "login": "testuser",
                        "id": 12345,
                        "node_id": "MDQ6VXNlcjIxMDMxMDY3",  # non-essential field
                        "avatar_url": "https://avatars.githubusercontent.com/u/12345?v=4",  # URL field
                        "type": "User",  # non-essential field
                    },
                    "labels": [
                        {
                            "name": "bug",
                            "color": "d73a4a",  # non-essential field
                            "description": "Something isn't working",  # non-essential field
                        }
                    ],
                    "state": "open",
                    "created_at": "2023-01-01T00:00:00Z",
                }
            ],
            "total_count": 1,
            "incomplete_results": False,
        }

        # Test with details=False
        filtered = github_tool.filter_response(test_response, details=False)

        # Verify essential fields are preserved
        assert "items" in filtered
        assert len(filtered["items"]) == 1
        assert "title" in filtered["items"][0]
        assert "user" in filtered["items"][0]
        assert "login" in filtered["items"][0]["user"]
        # "labels" is not in DEFAULT_ESSENTIAL_RESPONSE_FIELDS, so it should be filtered out
        assert "labels" not in filtered["items"][0]
        assert "state" in filtered["items"][0]
        assert "created_at" in filtered["items"][0]

        # Verify non-essential fields are removed
        assert "node_id" not in filtered["items"][0]["user"]
        assert "avatar_url" not in filtered["items"][0]["user"]

    def test_robust_filtering_for_empty_essential_fields(self, github_tool):
        """Test that essential fields with only non-essential children don't become empty."""
        # Create a test response where an essential field would become empty after filtering
        test_response = {
            "user": {
                "node_id": "MDQ6VXNlcjIxMDMxMDY3",  # non-essential field
                "avatar_url": "https://avatars.githubusercontent.com/u/12345?v=4",  # URL field
                "type": "User",  # non-essential field
            },
            "title": "Test PR",
        }

        # Test with details=False
        filtered = github_tool.filter_response(test_response, details=False)

        # Verify essential field 'user' is preserved with its content
        # even though it only contains non-essential fields
        assert "user" in filtered
        assert filtered["user"] is not None
        assert len(filtered["user"]) > 0

    def test_robust_filtering_for_empty_list_items(self, github_tool):
        """Test that individual list items don't become empty after filtering."""
        # Save original configuration
        original_essential_fields = github_tool.config.essential_response_fields
        original_exclude_url_fields = github_tool.config.exclude_url_fields

        # Configure for this test
        github_tool.config.essential_response_fields = [
            "title",
            "state",
            "items",
            "total_count",
        ]
        github_tool.config.exclude_url_fields = (
            True  # Explicitly set to filter URL fields
        )

        try:
            # Create a test response with a list containing mixed items
            # The second item has ONLY non-essential fields and would become empty after filtering
            test_response = {
                "items": [
                    {
                        "title": "Item with essential field",
                        "state": "open",
                        "node_id": "MDExOlB1bGxSZXF1ZXN0NTQ2MzA5NDI1",  # non-essential field
                    },
                    {
                        "node_id": "MDExOlB1bGxSZXF1ZXN0NTQ2MzA5NDI2",  # non-essential field
                        "html_url": "https://github.com/test/repo/pull/123",  # URL field with _url suffix
                        "diff_url": "https://github.com/test/repo/pull/123.diff",  # URL field with _url suffix
                    },
                    {
                        "title": "Another item with essential field",
                        "state": "closed",
                    },
                ],
                "total_count": 3,
            }

            # Test with details=False
            filtered = github_tool.filter_response(test_response, details=False)

            # Verify items are preserved, but the second item is filtered out completely
            # because it only has non-essential fields and URL fields which are excluded
            assert "items" in filtered
            assert len(filtered["items"]) == 2  # Only 2 items remain after filtering

            # First item should have only essential fields
            assert "title" in filtered["items"][0]
            assert "state" in filtered["items"][0]
            assert "node_id" not in filtered["items"][0]

            # Second item in the filtered list is actually the third item from the original list
            assert "title" in filtered["items"][1]
            assert "state" in filtered["items"][1]
            assert filtered["items"][1]["title"] == "Another item with essential field"

            # Now test with a completely different list to verify the logic works in other cases
            # Update essential fields to include "commits" for the second test
            github_tool.config.essential_response_fields = ["commits"]

            test_response_2 = {
                "commits": [
                    {
                        "sha": "abc123",  # Non-essential field
                        "commit": {
                            "message": "Fix bug"  # Non-essential field
                        },
                    },
                    {
                        "sha": "def456",  # Non-essential field
                        "author": {
                            "login": "testuser"  # Non-essential field
                        },
                    },
                ]
            }

            # Test with details=False
            filtered_2 = github_tool.filter_response(test_response_2, details=False)

            # The list should be preserved and each item should have details enabled
            # since they would all be empty otherwise
            assert "commits" in filtered_2
            assert len(filtered_2["commits"]) == 2

            # Each item should have its non-essential fields preserved
            # since they would be empty otherwise
            assert filtered_2["commits"][0] != {}
            assert filtered_2["commits"][1] != {}

            # Verify specific fields are preserved due to details being enabled
            assert any(field in filtered_2["commits"][0] for field in ["sha", "commit"])
            assert any(field in filtered_2["commits"][1] for field in ["sha", "author"])
        finally:
            # Restore original configuration
            github_tool.config.essential_response_fields = original_essential_fields
            github_tool.config.exclude_url_fields = original_exclude_url_fields

    def test_invalid_method(self, github_tool, request_context, extra_input):
        """Test that invalid HTTP methods are rejected."""

        with pytest.raises(ValidationError) as exc_info:
            github_tool.run(
                {
                    "method": "DELETE",
                    "path": "/repos/microsoft/vscode/pulls/1",
                    "data": {"state": "closed"},
                },
                extra_input,
                request_context,
            )

        # Verify that the error is about the method field
        assert "method" in str(exc_info.value)
        assert "Input should be 'GET', 'POST', 'PATCH' or 'PUT'" in str(exc_info.value)

    def test_unauthorized_endpoint(self, github_tool, request_context, extra_input):
        """Test that unauthorized endpoints return appropriate errors."""
        with pytest.raises(Exception) as exc_info:
            github_tool.run(
                {
                    "method": "POST",
                    "path": "/orgs/microsoft/repos",
                    "data": {"name": "test-repo"},
                },
                extra_input,
                request_context,
            )
        assert "403" in str(exc_info.value)

    def test_nonexistent_endpoint(self, github_tool, request_context, extra_input):
        """Test handling of nonexistent endpoints."""
        with pytest.raises(Exception) as exc_info:
            github_tool.run(
                {
                    "method": "GET",
                    "path": "/repos/microsoft/vscode/settings",
                    "data": {},
                },
                extra_input,
                request_context,
            )
        assert "404" in str(exc_info.value)

    def test_whitelist_get_enabled(
        self, github_tool_configurable, request_context, extra_input
    ):
        """Test GET requests are checked against whitelist when use_whitelist_on_get is True."""
        tool = github_tool_configurable(
            use_whitelist_on_get=True, allowed_paths=[r"^/repos/[^/]+/[^/]+$"]
        )

        # Should allow whitelisted path
        result = tool.run(
            {"method": "GET", "path": "/repos/microsoft/vscode", "data": {}},
            extra_input,
            request_context,
        )
        assert "vscode" in result
        assert "Visual Studio Code" in result

        # Should block non-whitelisted path
        result = tool.run(
            {"method": "GET", "path": "/repos/microsoft/vscode/pulls", "data": {}},
            extra_input,
            request_context,
        )
        assert "Does not have permission" in result

    def test_whitelist_get_disabled(
        self, github_tool_configurable, request_context, extra_input
    ):
        """Test GET requests bypass whitelist when use_whitelist_on_get is False."""
        tool = github_tool_configurable(
            use_whitelist_on_get=False, allowed_paths=[r"^/repos/[^/]+/[^/]+$"]
        )

        # Should allow non-whitelisted path for GET
        result = tool.run(
            {"method": "GET", "path": "/repos/microsoft/vscode/pulls", "data": {}},
            extra_input,
            request_context,
        )
        assert "DonJayamanne" in result

        # Should still enforce whitelist for POST
        result = tool.run(
            {
                "method": "POST",
                "path": "/repos/microsoft/vscode/pulls",
                "data": {"title": "test"},
            },
            extra_input,
            request_context,
        )
        assert "Does not have permission" in result

    def test_post_request(self, github_tool, request_context, extra_input):
        """Test that POST requests send data correctly."""
        test_data = {"body": "Test comment"}
        with pytest.raises(Exception) as exc_info:  # Will 404 but we can check the call
            github_tool.run(
                {
                    "method": "POST",
                    "path": "/repos/microsoft/vscode/issues/1/comments",
                    "data": test_data,
                },
                extra_input,
                request_context,
            )
        assert "404" in str(exc_info.value)

        # Verify the request was made with correct data
        github_tool.github_processor_client.call_github_api.assert_called_once()

    def test_empty_dict_preservation(self, github_tool):
        """Test that empty dictionaries are preserved and returned."""
        # Configure essential fields for this test
        original_essential_fields = github_tool.config.essential_response_fields
        github_tool.config.essential_response_fields = [
            "title",
            "user",
            "metadata",
            "details",
        ]

        try:
            # Create a test response with nested empty dictionaries
            test_response = {
                "title": "Test PR",
                "metadata": {},  # Empty dictionary
                "user": {
                    "login": "testuser",
                    "details": {},  # Empty nested dictionary
                },
            }

            # Test with details=False
            filtered = github_tool.filter_response(test_response, details=False)

            # Verify empty dictionaries are preserved
            assert "metadata" in filtered
            assert filtered["metadata"] == {}
            assert "user" in filtered
            assert "details" in filtered["user"]
            assert filtered["user"]["details"] == {}
        finally:
            # Restore original essential fields
            github_tool.config.essential_response_fields = original_essential_fields

    def test_essential_fields_with_empty_content(self, github_tool):
        """Test that essential fields with empty content after filtering get details enabled."""
        # Configure essential fields for this test
        original_essential_fields = github_tool.config.essential_response_fields
        github_tool.config.essential_response_fields = ["title", "user", "labels"]

        try:
            # Create a test response where an essential field would be empty after filtering
            test_response = {
                "title": "Test Issue",
                "user": {
                    "avatar_url": "https://example.com/avatar.png",  # URL field that would be filtered
                    "type": "User",  # Non-essential field
                },
                "labels": [
                    {
                        "color": "ff0000",  # Non-essential field
                        "description": "Bug report",  # Non-essential field
                    }
                ],
            }

            # Test with details=False
            filtered = github_tool.filter_response(test_response, details=False)

            # Verify essential fields are preserved with details enabled
            assert "title" in filtered
            assert "user" in filtered
            assert filtered["user"] != {}  # Should not be empty
            assert "labels" in filtered
            assert len(filtered["labels"]) > 0  # Should not be empty

            # Verify that details were enabled for these fields
            assert "type" in filtered["user"] or "avatar_url" in filtered["user"]
            assert (
                "color" in filtered["labels"][0]
                or "description" in filtered["labels"][0]
            )
        finally:
            # Restore original essential fields
            github_tool.config.essential_response_fields = original_essential_fields

    def test_http_method_consistency(self):
        """Test that HTTP methods in Pydantic model match those in proto definition."""
        # Get all HTTP methods from the proto enum (excluding special entries)
        proto_methods = set(
            name
            for name, number in processor_pb2.HTTPMethod.items()
            if isinstance(number, int)
        )

        # Get allowed HTTP methods from the Pydantic model using schema
        schema = GitHubAPIInput.model_json_schema()
        method_property = schema["properties"]["method"]

        pydantic_methods = set()
        if "enum" in method_property:
            pydantic_methods = set(method_property["enum"])

        # Assert that the sets are identical
        assert proto_methods == pydantic_methods, (
            f"HTTP methods mismatch: Proto has {proto_methods}, "
            f"Pydantic has {pydantic_methods}"
        )

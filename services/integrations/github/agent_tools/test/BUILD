load("@rules_python//python:defs.bzl", "py_binary")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "github_api_tool_manual_test",
    srcs = ["github_api_tool_manual_test.py"],
    tags = ["manual"],
    deps = [
        "//services/integrations/github/agent_tools:github_api_tool",
        "//services/lib/request_context:request_context_py",
    ],
)

pytest_test(
    name = "test_github_api_tool",
    srcs = ["test_github_api_tool.py"],
    deps = [
        "//services/integrations/github/agent_tools:config",
        "//services/integrations/github/agent_tools:github_api_tool",
        "//services/lib/request_context:request_context_py",
    ],
)

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "config",
    srcs = ["config.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/agents:tool",
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "github_api_tool",
    srcs = ["github_api_tool.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":config",
        "//services/agents:tool",
        "//services/integrations/github/processor:processor_py_proto",
        "//services/integrations/github/processor/client:client_py",
        "//services/lib/request_context:request_context_py",
        requirement("protobuf"),
        requirement("pydantic"),
        requirement("PyYAML"),
        requirement("requests"),
    ],
)

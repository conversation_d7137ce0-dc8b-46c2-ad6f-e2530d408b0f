"""Configuration for GitHub integrations."""

from services.agents.tool import BaseExtraToolInput
from dataclasses import dataclass
from dataclasses_json import dataclass_json
from typing import Self
from pydantic import SecretStr

from dataclasses import field


DEFAULT_ALLOWED_PATHS = [
    # Search and list PRs
    r"/repos/[^/]+/[^/]+/pulls",  # List/search PRs
    r"/search/issues",  # Search PRs across repos
    # List repos and orgs
    r"/repos/[^/]+/[^/]+",  # Get specific repo
    r"/repos/[^/]+",  # List repos for a user/organization
    r"/orgs/[^/]+/repos",  # List repos in an org
    # PR details and operations
    r"/repos/[^/]+/[^/]+/pulls/\d+",  # Get/update specific PR
    r"/repos/[^/]+/[^/]+/pulls/\d+/files",  # Get PR diff
    r"/repos/[^/]+/[^/]+/pulls/\d+/commits",  # Get PR commits
    # PR comments
    r"/repos/[^/]+/[^/]+/pulls/\d+/comments",  # List/create PR review comments
    r"/repos/[^/]+/[^/]+/issues/\d+/comments",  # List/create PR issue comments
    r"/repos/[^/]+/[^/]+/pulls/\d+/reviews",  # List/create PR reviews
    # CI/Workflow logs
    r"/repos/[^/]+/[^/]+/actions/runs",  # List workflow runs
    r"/repos/[^/]+/[^/]+/actions/runs/\d+",  # Get specific workflow run
    r"/repos/[^/]+/[^/]+/actions/runs/\d+/logs",  # Get workflow run logs
    r"/repos/[^/]+/[^/]+/actions/runs/\d+/jobs",  # List jobs for a workflow run
    r"/repos/[^/]+/[^/]+/actions/runs/\d+/jobs/\d+",  # Get specific job
    r"/repos/[^/]+/[^/]+/actions/runs/\d+/jobs/\d+/logs",  # Get job logs
]

DEFAULT_EXCLUDED_RESPONSE_FIELDS = [
    "custom_properties",
    "node_id",
    "temp_clone_token",
    "watchers_count",
    "web_commit_signoff_required",
]

DEFAULT_ESSENTIAL_RESPONSE_FIELDS = [
    # Core fields
    "number",
    "state",
    "title",
    "name",
    "description",
    "message",
    "repository",
    "pull_request",
    "body",
    # User/owner info
    "login",
    "owner",
    "user",
    # Status fields
    "status",
    # Timestamps
    "created_at",
    "updated_at",
    "closed_at",
    "merged_at",
    # URLs
    "url",
    "html_url",
    # Comment location fields
    "line",
    "start_line",
    "original_line",
    "original_start_line",
    "position",
    "original_position",
    "side",
    "start_side",
    "path",
    "diff_hunk",
    # Additional important fields
    "assignees",
    "commits",
    "additions",
    "deletions",
    "changed_files",
    # list results
    "items",
    "incomplete_results",
    "total_count",
]


@dataclass_json
@dataclass
class GithubCredentials:
    """Credentials for GitHub API."""

    api_token: SecretStr
    """GitHub API key for authentication"""


@dataclass
class GitHubExtraToolInput(BaseExtraToolInput):
    """Extra tool input for GitHub API tool."""

    credentials: GithubCredentials
    """The underlying GitHub credentials."""

    @classmethod
    def from_fields(cls, api_token: str) -> Self:
        """Create a GitHubExtraToolInput from GitHubCredentials."""
        return cls(credentials=GithubCredentials(api_token=SecretStr(api_token)))

    def get_credentials(self) -> GithubCredentials:
        """Get the underlying GitHubCredentials."""
        return self.credentials


@dataclass_json
@dataclass
class GitHubConfig:
    """Configuration for GitHub integration."""

    github_api_base: str = "https://api.github.com"
    """The base URL of the GitHub server. Default https://api.github.com"""

    settings_endpoint: str = "settings-svc:50051"
    """The endpoint for the settings service. Defaults to settings-svc:50051"""

    allowed_paths: list[str] = field(default_factory=lambda: DEFAULT_ALLOWED_PATHS)
    """List of allowed path patterns."""

    use_whitelist_on_get: bool = False
    """Whether to use the whitelist for GET requests.  Defaults to False, which will only apply the whitelist to POST requests and all GET requests will be allowed."""

    user_agent: str = "augment-github-api-tool/0.1.0"
    """The user agent to use for requests to the GitHub API."""

    excluded_response_fields: list[str] = field(
        default_factory=lambda: DEFAULT_EXCLUDED_RESPONSE_FIELDS
    )
    """List of response fields to exclude from the output as they contain no useful information."""

    exclude_url_fields: bool = True
    """Whether to exclude all fields ending with _url from the output."""

    essential_response_fields: list[str] = field(
        default_factory=lambda: DEFAULT_ESSENTIAL_RESPONSE_FIELDS
    )
    """List of essential response fields that should always be included in the output."""

"""GitHub API tool for agent.

Rather restrictive for now:

Only GET and POST are allowed.  No DELETE, PUT, PATCH, etc.
Only a whitelist of paths are allowed.
"""

import json
import logging
import re
from typing import Literal, Optional

import grpc
import requests
import yaml
from google.protobuf import struct_pb2
from google.rpc import status_pb2
from pydantic import BaseModel, Field

from services.agents import agents_pb2
from services.agents.tool import (
    EmptyExtraToolInput,
    ToolAuthenticationError,
    ToolDefinition,
    ValidatedTool,
)
from services.integrations.github.agent_tools.config import (
    GitHubConfig,
    GitHubExtraToolInput,
)
from services.integrations.github.processor import processor_pb2
from services.integrations.github.processor.client.client import GithubProcessorClient
from services.lib.request_context.request_context import RequestContext

log = logging.getLogger(__name__)


class GitHubAPIInput(BaseModel):
    """Input schema for the GitHub API tool."""

    summary: Optional[str] = Field(
        default=None,
        description="A short human-readable summary of what this API call will do. This helps users understand the purpose of the call.",
    )

    method: Literal["GET", "POST", "PATCH", "PUT"] = Field(
        default="GET",
        description="HTTP method to use. Usually GET, sometimes POST, PATCH, or PUT. DELETE is not allowed for safety reasons.",
    )

    path: str = Field(
        ...,  # required
        description="""GitHub API path.
Example paths:
- /repos/{owner}/{repo}/issues
Always use q=is:pr or q=is:issue to filter for issues or PRs.
Use creator={username} or assignee={username} to filter by specific user.
This is the most efficient way to search when the user is asking about "my PRs" or "my issues" in the current repo.

- /repos/{owner}/{repo}/pulls
Always create and push the new branch and check that all requirements are fulfilled before creating a PR.
If you do not know the convention of the repo for branch names and which branch to set as target, \
ask the user and memorize the answers.
Use `head=user:ref-name` or `head=organization:ref-name` to filter PRs by specific branch. Example: `head=octocat:my-feature-branch`.

- /orgs/{org}/issues
List issues and PRs related to a user or in an org respectively.
Always use the `filter` parameter to specify a subset - filter=assigned is the default, but it could be created, mentioned, subscribed, repos, all.
Values assigned, created, mentioned, subscribed describe relationship to the user.
filter=all returns all issues and PRs the user has access to.

- /issues
Use this endpoints to get the authenticated user's assigned issues or PRs across all repositories.
Always use the `filter` parameter to specify a subset - filter=assigned is the default, but it could be created, mentioned, subscribed, repos, all.

- /search/issues
Use this when you need to find issues or PRs with specific criteria.
For the current authenticated user, always use `author:@me` or `assignee:@me` in the query.
For other users, use `author:{username}` or `assignee:{username}`.
Always use q=is:pr or q=is:issue to filter between PRs and issues.

- /search/code
Use this to find code with specific content.

- /search/commits
Use this to find commits with specific criteria.
To filter by author, use `author:{username}` or `committer:{username}`.

- /repos/{owner}/{repo}/commits/{commit_sha}/check-runs
- /repos/{owner}/{repo}/commits/{commit_sha}/status
Check CI status.  You should verify status when you push a commit to a PR.
Not all PR checks are Check Runs, so the /status covers more CIs, but \
/check-runs provides more detailed information.

A few other examples
- /repos/{owner}/{repo}/actions/runs
- /repos/{owner}/{repo}/pulls/{number}/files
- /repos/{owner}/{repo}/releases/{release_id}/reactions
- /repos/{owner}/{repo}/actions/jobs/{job_id}
...

There are no paths for wikis and discussions. They need to be accessed through web fetch instead.
""",
    )

    data: dict | None = Field(
        default=None,
        description="Data to send - automatically handled as query params for GET or JSON body for POST.\
Query parameters should be used eagerly. Use small page sizes.",
    )

    details: bool = Field(
        default=False,
        description="""\
If false (default), it will only contain the most essential fields which is usually sufficient. \
If true, the response will include all non-trivial fields. \
Only set to true when you specifically need additional fields not included in the default response, \
as it can significantly increase response size.""",
    )


class GitHubAPITool(
    ValidatedTool[GitHubAPIInput, GitHubExtraToolInput | EmptyExtraToolInput]
):
    """Tool for making GitHub API calls using GitHub App authentication."""

    id = agents_pb2.RemoteToolId.GITHUB_API
    name = "github-api"
    description = """\
Make GitHub API calls. Response is formatted as yaml.
All issues endpoints will return both issues and PRs.
Most of your work are only considered done when you \
commit all necessary changes, create a PR, successfully pass all tests, get approved, and merge. \
Seek permission before you push to remote or perform rebase, unless you are explicitly told to do so.
"""

    input_model = GitHubAPIInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_CHECK

    def __init__(
        self, config: GitHubConfig, github_processor_client: GithubProcessorClient
    ):
        self.config = config
        self.github_processor_client = github_processor_client

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.github_processor_client.is_user_oauth_configured(
                request_context, processor_pb2.IsUserOAuthConfiguredRequest()
            )
        except Exception as e:
            log.error(f"get availability failed: {str(e)}")
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if not is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED
        return agents_pb2.ToolAvailabilityStatus.AVAILABLE

    def get_tool_definition(
        self, request_context: RequestContext | None = None
    ) -> ToolDefinition:
        """Get the tool parameter for LLM with user info injected.

        Retrieves GitHub user info if the user is authenticated and injects it into the tool description
        to help the LLM make correct API calls with the user's GitHub username.

        Returns:
            ToolDefinition: Tool definition with user info in the description
        """
        # Start with the base description
        description = self.description

        user_info = None
        if (
            request_context
            and self.get_availability_status(request_context)
            == agents_pb2.ToolAvailabilityStatus.AVAILABLE
        ):
            # Try to get GitHub user info
            try:
                user_info = self.github_processor_client.get_github_user_info(
                    request_context, processor_pb2.GetGithubUserInfoRequest()
                )
            except Exception as e:
                log.warning(f"Failed to get GitHub user info: {str(e)}")

        # If we have user info, add it to the description
        # otherwise, instruct the model to get it using /user
        if user_info and user_info.login:
            # Add user info to the description
            description += f"\n\nGitHub Information of the current user:\n- Login: {user_info.login}"
            if user_info.name:
                description += f"\n- Name: {user_info.name}"
            if user_info.email:
                description += f"\n- Email: {user_info.email}"
            description += "\nUse user login instead of email in API parameters.\n"
        else:
            description += (
                "\nDo not guess github logins.  "
                + "If you need this info, use the /user endpoint to obtain it. "
                + "For the current authenticated user, always use `author:@me` in the query for /search endpoints. "
            )

        # Return the tool definition with the enhanced description
        return ToolDefinition(
            name=self.name,
            description=description,
            input_schema_json=json.dumps(self.input_schema),
        )

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.github_processor_client.get_github_user_oauth_url(
                request_context, processor_pb2.GetGithubUserOAuthUrlRequest()
            )
        except Exception as e:
            log.error(f"get oauth url failed: {str(e)}")
            return ""

    def check_validated_input_safe(self, validated_input: GitHubAPIInput) -> bool:
        """Check if the GitHub API call is safe using validated input.

        GET methods are considered safe, while POST methods require approval.

        Returns:
            bool: True if the API call is safe (GET method), False otherwise
        """
        return validated_input.method == "GET"

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:  # type: ignore
        """Revoke tool access by deactivating OAuth for this tool.

        Calls the GitHub processor to revoke the OAuth grant.

        Returns:
            status_pb2.Status: The status of the operation
        """
        try:
            response = self.github_processor_client.revoke_oauth_grant(
                request_context, processor_pb2.RevokeOAuthGrantRequest()
            )
            # Return the status directly from the processor response
            return response.status
        except Exception as e:
            log.error(f"Error revoking GitHub OAuth access: {e}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Error revoking GitHub OAuth access: {str(e)}",
            )

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # type: ignore
        """Test the connection for this tool.

        Tests if the GitHub API is accessible and if the user is authenticated.
        First checks service availability with a direct request to /rate_limit,
        then tests authentication with the GitHub API client.

        Returns:
            status_pb2.Status: The status of the connection test
        """
        # First check GitHub API availability with a direct request to /rate_limit
        # This endpoint doesn't count against rate limits and is perfect for checking service availability
        try:
            # Use a short timeout to avoid hanging if GitHub is down
            response = requests.get("https://api.github.com/rate_limit", timeout=3)
            if response.status_code >= 500:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAVAILABLE.value[0],
                    message=f"GitHub API appears to be unavailable (HTTP {response.status_code}). Please check https://www.githubstatus.com/ for status information.",
                )
        except requests.RequestException as e:
            log.error(f"GitHub API health check failed: {e}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.UNAVAILABLE.value[0],
                message=f"Could not connect to GitHub API: {str(e)}. This could be due to network issues or GitHub service problems.",
            )

        # Now check if the user is authenticated
        try:
            is_oauth_configured = self.github_processor_client.is_user_oauth_configured(
                request_context, processor_pb2.IsUserOAuthConfiguredRequest()
            )

            if not is_oauth_configured:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="GitHub OAuth is not configured. Please authenticate with GitHub.",
                )

            # Test authenticated access
            user_request = processor_pb2.GithubApiRequest(
                method=processor_pb2.HTTPMethod.GET,
                path="/user",
            )

            result = self.github_processor_client.call_github_api(
                request_context, user_request
            )

            # Check the status code to determine the connection status
            if result.status_code == 200:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.OK.value[0],
                    message="Successfully connected to GitHub API",
                )
            elif result.status_code == 401 or result.status_code == 403:
                # This is an authentication error, or lack appropriate scope
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="Authentication failed. Please re-authenticate with GitHub.",
                )
            else:
                # Any other status code is an error
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.INTERNAL.value[0],
                    message=f"GitHub API returned status code {result.status_code}: {result.response}",
                )

        except Exception as e:
            log.error(f"Error testing GitHub connection: {e}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Error connecting to GitHub: {str(e)}",
            )

    def run_validated(
        self,
        validated_input: GitHubAPIInput,
        extra_tool_input: GitHubExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Execute GitHub API call using installation-specific client."""
        if (
            self.config.use_whitelist_on_get or validated_input.method != "GET"
        ) and not self.validate_api_path(validated_input.path):
            return (
                f"Does not have permission to access endpoint {validated_input.path}."
            )
        log.info("calling github api")
        try:
            # Map the method string to the corresponding enum value
            method_map = {
                "GET": processor_pb2.HTTPMethod.GET,
                "POST": processor_pb2.HTTPMethod.POST,
                "PATCH": processor_pb2.HTTPMethod.PATCH,
                "PUT": processor_pb2.HTTPMethod.PUT,
            }
            method = method_map.get(
                validated_input.method, processor_pb2.HTTPMethod.GET
            )

            # Check if this is a PR creation request
            is_pr_creation = (
                validated_input.method == "POST"
                and validated_input.path.endswith("/pulls")
                and validated_input.data
                and "body" in validated_input.data
            )

            # If creating a PR, add the footer to the description
            if is_pr_creation:
                footer = "\n\n---\nPull Request opened by [Augment Code](https://www.augmentcode.com/) with guidance from the PR author"

                # Append footer to the PR description
                if validated_input.data:
                    if validated_input.data["body"]:
                        validated_input.data["body"] += footer
                    else:
                        validated_input.data["body"] = footer

            request = processor_pb2.GithubApiRequest(
                method=method,
                path=validated_input.path,
            )

            if validated_input.data:
                # Convert dictionary to Struct
                data_struct = struct_pb2.Struct()
                data_struct.update(validated_input.data)
                request.data.CopyFrom(data_struct)

            if isinstance(extra_tool_input, GitHubExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None

            if credentials:
                request.token = credentials.api_token.get_secret_value()

            result = self.github_processor_client.call_github_api(
                request_context, request
            )

            if result.status_code == 401:
                raise ToolAuthenticationError(result.response)

            # 2xx is success, so outside that range we have an error.
            if not (200 <= result.status_code < 300):
                raise Exception(
                    f"GitHub API call failed with status code {result.status_code}: {result.response}"
                )
            try:
                parsed_response = json.loads(result.response)
                return self._format_response(parsed_response, validated_input.details)
            except json.JSONDecodeError:
                # If response is not valid JSON, return it as-is
                log.warning("Failed to parse response as JSON, returning as-is")
                return result.response

        except Exception as e:
            log.error(f"GitHub API call failed: {str(e)}")
            raise

    def validate_api_path(self, path: str) -> bool:
        """Check if the provided path is allowed based on the whitelist patterns."""
        for pattern in self.config.allowed_paths:
            if re.match(pattern, path):
                return True
        return False

    def _format_response(self, response: dict, details: bool) -> str:
        """Format GitHub API response as markdown."""
        response = self.filter_response(response, details)  # type: ignore
        if not response:
            response = self.filter_response(response, True)  # type: ignore
        if not response:
            return "GitHub API response is empty."

        # Format the response as YAML
        # default_flow_style=False ensures block style (indented) formatting
        # indent=2 sets the indentation level to 2 spaces
        formatted = yaml.safe_dump(
            response,
            default_flow_style=False,
            indent=2,
            sort_keys=True,
            allow_unicode=True,
        )

        return formatted

    def filter_response(self, response, details: bool = True):
        """Filter out unneeded keys from the response.

        If details=False, only essential fields are kept. However, if an essential field
        would become empty after filtering its children, details will be turned on for that
        field only to preserve important information, but only for specific high-value fields.

        For lists, if any individual item would become empty after filtering, details will be
        turned on for that specific item only if it's likely to contain critical information.

        Empty dictionaries are allowed to be returned, as parent callers may enable details
        for essential fields that would otherwise be empty.

        This ensures that the response is always meaningful and contains useful information,
        even when filtering is applied, while being cautious about enabling details mode.
        """
        if isinstance(response, list):
            # Process each item in the list, potentially with different details settings
            filtered_list = []
            for item in response:
                # First try with current details setting
                filtered_item = self.filter_response(item, details)

                # If the item becomes empty but originally had content, try with details=True
                if not filtered_item and item and not details:
                    filtered_item = self.filter_response(item, True)

                # Add the item to our result list if it's not empty
                if filtered_item or filtered_item == 0 or filtered_item is False:
                    filtered_list.append(filtered_item)

            return filtered_list
        elif isinstance(response, dict):
            filtered = {}
            for key, value in response.items():
                # Skip excluded fields and URL fields (if configured)
                if key in self.config.excluded_response_fields or (
                    self.config.exclude_url_fields and key.endswith("_url")
                ):
                    continue

                # Skip non-essential fields when details=False
                if not details and key not in self.config.essential_response_fields:
                    continue

                # Apply filtering to the value
                filtered_value = self.filter_response(value, details)

                # If this is an essential field that would become empty, try with details=True
                if (
                    not details
                    and key in self.config.essential_response_fields
                    and not filtered_value
                    and value
                ):  # Only if original value was not empty
                    filtered_value = self.filter_response(value, True)

                # Only add non-empty values (except for empty dicts which are allowed)
                if (
                    filtered_value
                    or filtered_value == 0
                    or filtered_value is False
                    or isinstance(filtered_value, dict)
                ):
                    filtered[key] = filtered_value

            return filtered
        else:
            return response

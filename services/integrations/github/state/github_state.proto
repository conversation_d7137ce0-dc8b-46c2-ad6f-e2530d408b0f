syntax = "proto3";

package github_state;

import "base/blob_names/blob_names.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

// service for storing and retrieving github state
service GithubState {
  // returns the (indexed) blob checkpoints for the repositories of the current tenant
  //
  // At this point, the state service maintains a single commit/checkpoint for each reference
  rpc GetIndexedRefCheckpoints(GetIndexedRefCheckpointsRequest) returns (GetIndexedRefCheckpointsResponse) {}

  // return the states of all the current repos for the current tenant
  // similar to indexed refs, we only maintain a single current commit for each ref
  rpc GetCurrentRefStates(GetCurrentRefStatesRequest) returns (GetCurrentRefStatesResponse) {}

  // return the current state of a Github reference
  // returns NOT_FOUND if the ref is not found
  rpc GetCurrentRefState(GetCurrentRefStateRequest) returns (GetCurrentRefStateResponse) {}

  // returns the file content for given list of files for the currently uploaded ref
  //
  // Use a response stream because the list of files can be larger than the 2 MB message limit
  //
  // returns NOT_FOUND if the ref is not found
  rpc GetCurrentRefFiles(GetCurrentRefFilesRequest) returns (stream GetCurrentRefFilesResponse) {}

  // updates the current ref
  //
  // returns ABORTED if the diff commit is not the current ref commit
  rpc UpdateCurrentRefCheckpoint(stream UpdateCurrentRefCheckpointRequest) returns (UpdateCurrentRefCheckpointResponse) {}

  // deletes all github state for the given repos
  rpc DeleteGithubStateForRepos(DeleteGithubStateForReposRequest) returns (DeleteGithubStateForReposResponse) {}

  // clears all github state
  //
  // only in testing and dev
  rpc Clear(ClearRequest) returns (ClearResponse) {}
}

message GetCurrentRefStateRequest {
  // return the current state of the reference
  GithubRef ref = 1;
}

message GetCurrentRefStateResponse {
  // last uploaded commit (but maybe not yet indexed)
  RefCheckpoint last_uploaded_ref_checkpoint = 1;
}

message GetCurrentRefFilesRequest {
  // the reference
  GithubRef ref = 1;

  // the current commit
  // if set, if the commit was not found, the service will return NOT_FOUND
  string commit_sha = 2 [debug_redact = true];

  // the file paths for which to return the content
  repeated string file_paths = 3 [debug_redact = true];
}

message FileInfo {
  // the file path
  string file_path = 1 [debug_redact = true];

  // the blob name of the file (encoded)
  base.blob_names.BlobName blob_name = 2;

  // any error, e.g. NOT_FOUND if the file does not exist
  google.rpc.Status status = 3;
}

message GetCurrentRefFilesResponse {
  // the current commit
  // the caller is expected to have the same commit
  string commit_sha = 1 [debug_redact = true];

  // the file info
  // the service will return a FileInfo for each requested file
  // the order does not have to be the same as the requested file paths
  repeated FileInfo file_infos = 2;
}

message GetIndexedRefCheckpointsRequest {}

message GithubRepo {
  // the repository owner
  string repo_owner = 1 [debug_redact = true];

  // the repository name
  string repo_name = 2 [debug_redact = true];
}

message GithubRef {
  GithubRepo repo = 1;

  // branch or tag
  string ref = 2 [debug_redact = true];
}

// a reference checkpoint
message RefCheckpoint {
  // the repo and ref (branch)
  GithubRef ref = 1;

  // the commit sha
  string commit_sha = 2 [debug_redact = true];

  // The time the commit was created.
  // the commit time ordering is the source of truth for commit ordering on a given ref
  google.protobuf.Timestamp commit_time = 3;

  // the blobs information for the checkpoint
  base.blob_names.Blobs blobs = 4;
}

message GetIndexedRefCheckpointsResponse {
  // the list of reference checkpoints
  // each repo is usually contained only once, but there
  // might be multiple repositories for polyrepo workflows
  repeated RefCheckpoint ref_checkpoints = 1;
}

message DiffInfo {
  // the file path
  string file_path = 1 [debug_redact = true];

  oneof change {
    // the file path is no longer part of the commit
    bool deleted = 2;

    // the file path is part of the commit
    // either added or updated
    base.blob_names.BlobName content_blob_name = 3;
  }
}

message UpdateCurrentRefCheckpointRequest {
  // only in first request
  GithubRef ref = 1;

  // the commit sha
  // only in first request
  string commit_sha = 2;

  // the parent commits (for git)
  //
  // most commits have a single parent, but merge commit have multiple parents
  //
  // only in first request
  repeated string parent_commit_shas = 3;

  // the diff commit
  // this current ref commit used for calculating the diff
  // if the current ref is not the commit given, the update will fail
  //
  // only in first request
  string diff_commit_sha = 4;

  // The time the commit was created.
  // the commit time ordering is the source of truth for commit ordering on a given ref
  //
  // only in first request
  google.protobuf.Timestamp commit_time = 5;

  // checkpoint id
  //
  // only in first request
  base.blob_names.Blobs blobs = 6;

  // informations about every file path changed
  repeated DiffInfo diff_infos = 7;

  // if any currently uploaded state should be overwritten completely
  // used for forced pushes and if .augmentignore is changed
  //
  // only in first request
  bool force_upload = 8;
}

message UpdateCurrentRefCheckpointResponse {}

message GetPendingRefCheckpointsRequest {
  // return the current state of the reference
  GithubRef ref = 1;
}

message GetPendingRefCheckpointsResponse {
  repeated RefCheckpoint ref_checkpoints = 1;
}

message DeleteGithubStateForReposRequest {
  repeated GithubRepo repos = 1;
}

message DeleteGithubStateForReposResponse {}

message ClearRequest {}

message ClearResponse {}

message GetCurrentRefStatesRequest {}

message GetCurrentRefStatesResponse {
  repeated RefCheckpoint ref_checkpoints = 1;
}

syntax = "proto3";

package github_persistence;

import "base/blob_names/blob_names.proto";
import "google/protobuf/timestamp.proto";
import "services/integrations/github/state/github_state.proto";

// on disk representation of the github state

message FileInfo {
  // the file path
  string file_path = 1 [debug_redact = true];

  // the blob name of the file (encoded)
  base.blob_names.BlobName blob_name = 2;
}

// Information about the the currently uploaded ref.
// Column Family: Current
// Column Qualifier: "checkpoint"
// Key: "current#{repo_owner}#{repo_name}#{ref}"
message CurrentUploadedRefData {
  // the ref
  github_state.GithubRef ref = 1;

  // the commit sha
  string commit_sha = 2 [debug_redact = true];

  // the parent commit (for git)
  repeated string parent_commit_shas = 3 [debug_redact = true];

  // The time the commit was created.
  // the commit time ordering is the source of truth for commit ordering on a given ref
  google.protobuf.Timestamp commit_time = 4;

  // the file info
  // the service will return a FileInfo for each requested file
  // the order does not have to be the same as the requested file paths
  // note that this is stored separately from the rest of the current ref data
  // to avoid expensive reads when we only want to know the state of current ref
  repeated FileInfo file_infos = 5;

  // the blobs information for the checkpoint
  base.blob_names.Blobs blobs = 6;
}

// separate message so we can store file infos separately from the rest of
// the current ref data. this way, we can look up current ref state without
// an expensive read of all the file infos.
//
// note that file infos are stored in the same row as the current ref, but in a different column
// this is fine bc they're written or deleted together
// Column Family: FileInfos
// Column Qualifier: "file_infos"
// Key: "current#{repo_owner}#{repo_name}#{ref}" <- same as current ref
message FileInfoContainer {
  repeated FileInfo file_infos = 1;
}

message RefInfo {
  github_state.GithubRef ref = 1;

  // the commit sha
  string commit_sha = 2 [debug_redact = true];

  // The time the commit was created.
  // the commit time ordering is the source of truth for commit ordering on a given ref
  google.protobuf.Timestamp commit_time = 3;

  // the parent commit (for git)
  repeated string parent_commit_shas = 4 [debug_redact = true];

  // the blobs information for the checkpoint
  base.blob_names.Blobs blobs = 5;
}

// Information about the repositories of a tenant.
//
// A commit is indexed if it is indexed for all current models
//
// Column Family: Indexed
// Column Qualifier: "checkpoint"
// Key: "indexed#{repo_owner}#{repo_name}#{ref}"
message IndexedRefData {
  // the information about the indexed checkpoints
  RefInfo ref = 1;

  string tenant_id = 2;
}

// Information about the repositories of a tenant.
//
// A commit is pending as long as it is not index for all current models.
message PendingRefData {
  // the information about the non-indexed checkpoints
  RefInfo ref = 1;

  string tenant_id = 2;
}

message PendingEvent {
  PendingRefData pending_ref_data = 1;
}

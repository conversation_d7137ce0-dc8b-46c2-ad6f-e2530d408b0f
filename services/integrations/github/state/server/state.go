package main

import (
	"context"
	"fmt"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	githubproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/server/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

// GithubState is the interface for the github state.
type GithubState interface {
	// GetIndexedRefs returns the indexed refs for the given tenant.
	//
	// Args:
	//	ctx: the context
	//	tenantID: the tenant id
	//
	GetIndexedRefs(ctx context.Context,
		tenantID string) ([]*githubstatepersistproto.IndexedRefData, error)

	// GetCurrentRefStates returns the current ref states for the given tenant.
	// Note that this does NOT include file infos, since those are very large.
	// If you need file infos, use GetCurrentRefCheckpoint for each ref.
	//
	// Args:
	//	ctx: the context
	//	tenantID: the tenant id
	//
	GetCurrentRefStates(ctx context.Context,
		tenantID string) ([]*githubstatepersistproto.CurrentUploadedRefData, error)

	// GetIndexedRef returns the indexed ref for the given tenant and ref.
	//
	// Args:
	//	ctx: the context
	// 	tenantID: the tenant id
	//	repoOwner: the repo owner
	//	repoName: the repo name
	//	ref: the ref
	//
	GetIndexedRef(ctx context.Context,
		tenantID string,
		repoOwner string,
		repoName string,
		ref string) (*githubstatepersistproto.IndexedRefData, error)

	// GetCurrentRefCheckpoint returns the current ref checkpoint for the given tenant and ref.
	// This does include file infos, which makes it a large O(files in repo) read.
	//
	// Args:
	//	ctx: the context
	//	tenantID: the tenant id
	//	repoOwner: the repo owner
	//	repoName: the repo name
	//	ref: the ref
	//
	GetCurrentRefCheckpoint(ctx context.Context, tenantID string, repoOwner string, repoName string, ref string) (*githubstatepersistproto.CurrentUploadedRefData, error)

	// UpdateCurrentRefCheckpoint updates the current ref checkpoint for the given tenant and ref.
	//
	// Args:
	//	ctx: the context
	//	tenantID: the tenant id
	//	refData: the ref data
	//
	UpdateCurrentRefCheckpoint(ctx context.Context, tenantID string, refData *githubstatepersistproto.CurrentUploadedRefData, forceUpload bool) (updated bool, err error)

	// MarkIndexedRefCheckpoint marks the given ref as indexed.
	//
	// Args:
	//	ctx: the context
	//	tenantID: the tenant id
	//	refData: the ref data
	//
	MarkIndexedRefCheckpoint(ctx context.Context, tenantID string, refData *githubstatepersistproto.IndexedRefData) error

	// DeleteGithubState deletes the github state for the given tenant and repo.
	//
	// Args:
	//	ctx: the context
	//	tenantID: the tenant id
	//	repoOwner: the repo owner
	//	repoName: the repo name
	//
	DeleteGithubState(ctx context.Context, tenantID string, repoOwner string, repoName string) error

	Clear(ctx context.Context, tenantID string) error
}

type githubState struct {
	bigtableProxyClient bigtableproxy.BigtableProxyClient
	pendingPubsub       PendingTaskPublisher
}

func NewGithubState(bigtableProxyClient bigtableproxy.BigtableProxyClient, pendingPubsub PendingTaskPublisher) GithubState {
	return &githubState{
		bigtableProxyClient: bigtableProxyClient,
		pendingPubsub:       pendingPubsub,
	}
}

func (s *githubState) MarkIndexedRefCheckpoint(ctx context.Context, tenantID string, refData *githubstatepersistproto.IndexedRefData) error {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return err
	}
	key := []byte("indexed#" + refData.Ref.Ref.Repo.RepoOwner + "#" + refData.Ref.Ref.Repo.RepoName + "#" + refData.Ref.Ref.Ref)
	data, err := proto.Marshal(refData)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal repo info")
		return err
	}
	log.Info().Msgf("MarkIndexedRefCheckpoint: ref=%s tenantID=%s", githublib.RedactRef(refData.Ref.Ref), tenantID)
	entries := []*bigtableproto.MutateRowsRequest_Entry{}
	entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
		RowKey: key,
		Mutations: []*bigtableproto.Mutation{
			{
				Mutation: &bigtableproto.Mutation_SetCell_{
					SetCell: &bigtableproto.Mutation_SetCell{
						FamilyName:      "Indexed",
						ColumnQualifier: []byte("checkpoint"),
						Value:           data,
					},
				},
			},
		},
	})
	resp, err := s.bigtableProxyClient.MutateRows(ctx, tenantID, proxyproto.TableName_GITHUB, entries, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to write row")
		return err
	}
	for _, mutateRowsResponse := range resp {
		for _, entry := range mutateRowsResponse.Entries {
			if entry.Status.Code != int32(codes.OK) {
				log.Error().Msgf("Failed to write row at index %d: %v", entry.Index, entry.Status)
				return status.Error(codes.Internal, "Failed to write row")
			}
		}
	}
	return nil
}

func (s *githubState) GetIndexedRefs(ctx context.Context, tenantID string) ([]*githubstatepersistproto.IndexedRefData, error) {
	log.Info().Msgf("GetIndexedRefs: tenantID=%s", tenantID)
	if tenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return nil, err
	}
	rowRange := &bigtableproto.RowRange{
		StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("indexed#")},
		EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("indexed$")},
	}

	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter("Indexed"),
		bigtableproxy.LatestNFilter(1),
	)

	rows, err := s.bigtableProxyClient.ReadRows(ctx, tenantID, proxyproto.TableName_GITHUB, &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{rowRange},
	}, filter.Proto(), 0, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read row")
		return nil, err
	}
	if len(rows) == 0 {
		return nil, status.Error(codes.NotFound, "No indexed checkpoint refs found")
	}

	var org string
	result := []*githubstatepersistproto.IndexedRefData{}
	for _, row := range rows {
		data := &githubstatepersistproto.IndexedRefData{}
		for _, cell := range row.Cells {
			if cell.FamilyName != "Indexed" {
				continue
			}

			err := proto.Unmarshal(cell.Value, data)
			if err != nil {
				log.Error().Err(err).Msg("Failed to unmarshal tenant info")
				return nil, err
			}
		}
		result = append(result, data)
		if org == "" {
			org = data.Ref.Ref.Repo.RepoOwner
		} else if org != data.Ref.Ref.Repo.RepoOwner {
			// Do this as a sanity check, see #services_inc_8417 for more background
			log.Fatal().Msgf("Expected all repos to be from the same org, but found %s and %s for tenant ID %s", org, data.Ref.Ref.Repo.RepoOwner, tenantID)
		}
	}
	return result, nil
}

func (s *githubState) GetCurrentRefStates(ctx context.Context, tenantID string) ([]*githubstatepersistproto.CurrentUploadedRefData, error) {
	log.Info().Msgf("GetCurrentRefStates: tenantID=%s", tenantID)
	if tenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return nil, err
	}
	rowRange := &bigtableproto.RowRange{
		StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("current#")},
		EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("current$")},
	}

	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter("Current"),
		bigtableproxy.LatestNFilter(1),
	)

	rows, err := s.bigtableProxyClient.ReadRows(ctx, tenantID, proxyproto.TableName_GITHUB, &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{rowRange},
	}, filter.Proto(), 0, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read row")
		return nil, err
	}
	if len(rows) == 0 {
		return nil, status.Error(codes.NotFound, "No current checkpoint refs found")
	}

	var org string
	result := []*githubstatepersistproto.CurrentUploadedRefData{}
	for _, row := range rows {
		data := &githubstatepersistproto.CurrentUploadedRefData{}
		if len(row.Cells) != 1 || row.Cells[0].FamilyName != "Current" {
			return nil, status.Error(codes.Internal, "Expected only current cell per row")
		}
		err := proto.Unmarshal(row.Cells[0].Value, data)
		if err != nil {
			log.Error().Err(err).Msg("Failed to unmarshal tenant info")
			return nil, err
		}
		result = append(result, data)
		if org == "" {
			org = data.Ref.Repo.RepoOwner
		} else if org != data.Ref.Repo.RepoOwner {
			// Do this as a sanity check, see #services_inc_8417 for more background
			log.Fatal().Msgf("Expected all repos to be from the same org, but found %s and %s for tenant ID %s", org, data.Ref.Repo.RepoOwner, tenantID)
		}
	}
	return result, nil
}

func (s *githubState) GetIndexedRef(ctx context.Context, tenantID string, repoOwner string, repoName string, ref string) (*githubstatepersistproto.IndexedRefData, error) {
	log.Info().Msgf("GetIndexedRef: ref=%s, tenantID=%s", githublib.RedactRef(&githubproto.GithubRef{Ref: ref, Repo: &githubproto.GithubRepo{RepoOwner: repoOwner, RepoName: repoName}}), tenantID)
	if tenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return nil, err
	}
	key := []byte("indexed#" + "#" + repoOwner + "#" + repoName + "#" + ref)
	keys := [][]byte{key}

	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter("Indexed"),
		bigtableproxy.LatestNFilter(1),
	)

	rows, err := s.bigtableProxyClient.ReadRows(ctx, tenantID, proxyproto.TableName_GITHUB, &bigtableproto.RowSet{
		RowKeys: keys,
	}, filter.Proto(), 0, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read row")
		return nil, err
	}

	if len(rows) == 0 {
		return nil, status.Error(codes.NotFound, "Tenant info not found")
	}

	if len(rows) > 1 {
		return nil, status.Error(codes.Internal, "Expected 1 row per tenant")
	}

	data := &githubstatepersistproto.IndexedRefData{}
	for _, row := range rows {
		for _, cell := range row.Cells {
			if cell.FamilyName != "Indexed" {
				continue
			}

			err := proto.Unmarshal(cell.Value, data)
			if err != nil {
				log.Error().Err(err).Msg("Failed to unmarshal tenant info")
				return nil, err
			}
		}
	}
	return data, nil
}

func (s *githubState) GetCurrentRefCheckpoint(ctx context.Context, tenantID string, repoOwner string, repoName string, ref string) (*githubstatepersistproto.CurrentUploadedRefData, error) {
	if tenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return nil, err
	}
	key := []byte("current#" + repoOwner + "#" + repoName + "#" + ref)
	log.Info().Msgf("GetCurrentRefCheckpoint: ref=%s, tenantID=%s", githublib.RedactRef(&githubproto.GithubRef{Ref: ref, Repo: &githubproto.GithubRepo{RepoOwner: repoOwner, RepoName: repoName}}), tenantID)
	keys := [][]byte{key}

	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter("Current|FileInfos"),
		bigtableproxy.LatestNFilter(1),
	)

	rows, err := s.bigtableProxyClient.ReadRows(ctx, tenantID, proxyproto.TableName_GITHUB, &bigtableproto.RowSet{
		RowKeys: keys,
	}, filter.Proto(), 0, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read row")
		return nil, err
	}
	if len(rows) == 0 {
		return nil, status.Error(codes.NotFound, "Repo info not found")
	}
	if len(rows) > 1 {
		return nil, status.Error(codes.Internal, "Expected 1 row per tenant")
	}
	log.Info().Msgf("GetCurrentRefCheckpoint: rowsCount=%d", len(rows))

	repoInfo := &githubstatepersistproto.CurrentUploadedRefData{}
	fileInfos := &githubstatepersistproto.FileInfoContainer{}
	for _, cell := range rows[0].Cells {
		if cell.FamilyName == "Current" {
			err := proto.Unmarshal(cell.Value, repoInfo)
			if err != nil {
				log.Error().Err(err).Msg("Failed to unmarshal repo info")
				return nil, err
			}
		} else if cell.FamilyName == "FileInfos" {
			err := proto.Unmarshal(cell.Value, fileInfos)
			if err != nil {
				log.Error().Err(err).Msg("Failed to unmarshal file infos")
				return nil, err
			}
		}
	}
	// small backwards compatibility fix
	// in the past, we used to write file infos inside the current ref data
	if repoInfo.FileInfos == nil {
		repoInfo.FileInfos = fileInfos.FileInfos
	}
	return repoInfo, nil
}

func (s *githubState) UpdateCurrentRefCheckpoint(ctx context.Context, tenantID string, uploadedRefData *githubstatepersistproto.CurrentUploadedRefData, forceUpload bool) (updated bool, err error) {
	if tenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return false, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return false, err
	}
	log.Info().Msgf("UpdateCurrentRefCheckpoint: ref=%s, tenantID=%s", githublib.RedactRef(uploadedRefData.Ref), tenantID)

	// we write the fileinfos separately from the rest of the uploaded ref data so we can
	// look up state without an expensive read of all the file infos
	currentKey := []byte("current#" + uploadedRefData.Ref.Repo.RepoOwner + "#" + uploadedRefData.Ref.Repo.RepoName + "#" + uploadedRefData.Ref.Ref)

	fileInfosValue, err := proto.Marshal(&githubstatepersistproto.FileInfoContainer{FileInfos: uploadedRefData.FileInfos})
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal file infos")
		return false, err
	}
	// we clone the uploaded ref data and then delete file infos from it to avoid modifying what was passed in by pointer
	clonedUploadedRefData := proto.Clone(uploadedRefData).(*githubstatepersistproto.CurrentUploadedRefData)
	clonedUploadedRefData.FileInfos = nil
	checkpointValue, err := proto.Marshal(clonedUploadedRefData)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal uploaded ref data")
		return false, err
	}

	log.Info().Msgf("UpdateCurrentRefCheckpoint: tenantID=%s", tenantID)

	pendingRef := &githubstatepersistproto.PendingRefData{
		Ref: &githubstatepersistproto.RefInfo{
			Ref:              uploadedRefData.Ref,
			CommitSha:        uploadedRefData.CommitSha,
			CommitTime:       uploadedRefData.CommitTime,
			ParentCommitShas: uploadedRefData.ParentCommitShas,
			Blobs:            uploadedRefData.Blobs,
		},
		TenantId: tenantID,
	}
	var filter *bigtableproto.RowFilter

	// If ForceUpload is true, we will always overwrite the existing cell.
	// To keep only the newest value (even if the timestamp is from earlier), we will delete any existing value
	// https://cloud.google.com/bigtable/docs/keep-only-latest-value
	if forceUpload {
		log.Info().Msgf("UpdateCurrentRefCheckpoint: forceUpload is true")

		filter = &bigtableproto.RowFilter{
			Filter: &bigtableproto.RowFilter_BlockAllFilter{
				BlockAllFilter: true, // always evaluates to false
			},
		}

		resp, err := s.bigtableProxyClient.MutateRows(ctx, tenantID, proxyproto.TableName_GITHUB, []*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: currentKey,
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromRow_{
							DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
						},
					},
				},
			},
		}, requestContext)
		if err != nil {
			log.Error().Err(err).Msg("Failed to write row")
			return false, err
		}
		for _, mutateRowsResponse := range resp {
			for _, entry := range mutateRowsResponse.Entries {
				if entry.Status.Code != int32(codes.OK) {
					log.Error().Msgf("Failed to delete row at index %d: %v", entry.Index, entry.Status)
					return false, status.Error(codes.Internal, "Failed to delete row")
				}
				log.Info().Msgf("Deleted existing entry at index %d: %v", entry.Index, entry.Status)
			}
		}

	} else {
		// Filter for any existing cell with a timestamp after this pending ref's commit time. If such a
		// cell exists, this is an old commit that we should ignore.
		filter = &bigtableproto.RowFilter{
			Filter: &bigtableproto.RowFilter_TimestampRangeFilter{
				TimestampRangeFilter: &bigtableproto.TimestampRange{
					StartTimestampMicros: (uploadedRefData.CommitTime.AsTime().UnixMilli() + 1) * 1000,
					// Leaving EndTimestampMicros empty is interpreted as infinity.
				},
			},
		}
	}
	mutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      "Current",
					ColumnQualifier: []byte("checkpoint"),
					Value:           checkpointValue,
					TimestampMicros: uploadedRefData.CommitTime.AsTime().UnixMicro(),
				},
			},
		},
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      "FileInfos",
					ColumnQualifier: []byte("file_infos"),
					Value:           fileInfosValue,
					TimestampMicros: uploadedRefData.CommitTime.AsTime().UnixMicro(),
				},
			},
		},
	}
	resp, err := s.bigtableProxyClient.CheckAndMutateRow(
		ctx, tenantID, proxyproto.TableName_GITHUB, currentKey, filter,
		[]*bigtableproto.Mutation{}, mutations, requestContext,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to write row")
		return false, err
	} else if resp.PredicateMatched {
		log.Info().Msgf("UpdateCurrentRefCheckpoint: ref was older than current ref")
		return false, nil
	}

	log.Info().Msgf("UpdateCurrentRefCheckpoint: publish pending task: ref=%v", pendingRef)
	pubErr := s.pendingPubsub.PublishPendingTask(ctx, pendingRef)
	if pubErr != nil {
		log.Error().Err(pubErr).Msg("Failed to publish pending task")
		return false, pubErr
	}
	return true, nil
}

func (s *githubState) Clear(ctx context.Context, tenantID string) error {
	if tenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return err
	}
	log.Info().Msgf("Clear: tenantID=%s", tenantID)
	currentRowRange := &bigtableproto.RowRange{
		StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("current#")},
		EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("current$")},
	}
	indexedRowRange := &bigtableproto.RowRange{
		StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("indexed#")},
		EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("indexed$")},
	}

	// Use a stripped filter that only returns row keys so that the response is not too large
	keyOnlyFilter := &bigtableproto.RowFilter{
		Filter: &bigtableproto.RowFilter_Chain_{
			Chain: &bigtableproto.RowFilter_Chain{
				Filters: []*bigtableproto.RowFilter{
					{
						// Removes the value from each cell, returning only the keys.
						Filter: &bigtableproto.RowFilter_StripValueTransformer{
							StripValueTransformer: true,
						},
					},
					{
						// Returns only the first cell in each row to reduce the amount of data scanned.
						Filter: &bigtableproto.RowFilter_CellsPerRowLimitFilter{
							CellsPerRowLimitFilter: 1,
						},
					},
				},
			},
		},
	}

	rows, err := s.bigtableProxyClient.ReadRows(ctx, tenantID, proxyproto.TableName_GITHUB, &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{currentRowRange, indexedRowRange},
	}, keyOnlyFilter, 0, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read row")
		return err
	}
	log.Info().Msgf("Clear: rows: %d", len(rows))
	entries := []*bigtableproto.MutateRowsRequest_Entry{}
	if len(rows) == 0 {
		return nil
	}
	for _, row := range rows {
		entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
			RowKey: row.RowKey,
			Mutations: []*bigtableproto.Mutation{
				{
					Mutation: &bigtableproto.Mutation_DeleteFromRow_{
						DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
					},
				},
			},
		})
	}
	resp, err := s.bigtableProxyClient.MutateRows(ctx, tenantID, proxyproto.TableName_GITHUB, entries, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to write row")
		return err
	}
	for _, mutateRowsResponse := range resp {
		for _, entry := range mutateRowsResponse.Entries {
			if entry.Status.Code != int32(codes.OK) {
				log.Error().Msgf("Failed to write row at index %d: %v", entry.Index, entry.Status)
				return status.Error(codes.Internal, "Failed to write row")
			}
		}
	}
	return nil
}

func (s *githubState) DeleteGithubState(ctx context.Context, tenantID string, repoOwner string, repoName string) error {
	log.Info().Msgf("DeleteGithubState: repoOwner=%s, repoName=%s, tenantID=%s", githublib.RedactString(repoOwner), githublib.RedactString(repoName), tenantID)
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error getting request context")
		return err
	}

	indexedPrefix := fmt.Sprintf("indexed#%s#%s#", repoOwner, repoName)
	indexedRowRange := &bigtableproto.RowRange{
		StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte(indexedPrefix)},
		EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte(indexedPrefix + "\xFF")},
	}

	currentPrefix := fmt.Sprintf("current#%s#%s#", repoOwner, repoName)
	currentRowRange := &bigtableproto.RowRange{
		StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte(currentPrefix)},
		EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte(currentPrefix + "\xFF")},
	}

	// Use a stripped filter that only returns row keys so that the response is not too large
	keyOnlyFilter := &bigtableproto.RowFilter{
		Filter: &bigtableproto.RowFilter_Chain_{
			Chain: &bigtableproto.RowFilter_Chain{
				Filters: []*bigtableproto.RowFilter{
					{
						// Removes the value from each cell, returning only the keys.
						Filter: &bigtableproto.RowFilter_StripValueTransformer{
							StripValueTransformer: true,
						},
					},
					{
						// Returns only the first cell in each row to reduce the amount of data scanned.
						Filter: &bigtableproto.RowFilter_CellsPerRowLimitFilter{
							CellsPerRowLimitFilter: 1,
						},
					},
				},
			},
		},
	}

	// Read the row keys for the current and indexed rows
	rows, err := s.bigtableProxyClient.ReadRows(ctx, tenantID, proxyproto.TableName_GITHUB, &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{currentRowRange, indexedRowRange},
	}, keyOnlyFilter, 0, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read rows")
		return err
	}

	// If no rows are found to delete, return nil
	if len(rows) == 0 {
		return nil
	}

	log.Info().Msgf("DeleteGithubState: rows to delete: %d", len(rows))
	entries := []*bigtableproto.MutateRowsRequest_Entry{}
	for _, row := range rows {
		entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
			RowKey: row.RowKey,
			Mutations: []*bigtableproto.Mutation{
				{
					Mutation: &bigtableproto.Mutation_DeleteFromRow_{
						DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
					},
				},
			},
		})
	}

	resp, err := s.bigtableProxyClient.MutateRows(ctx, tenantID, proxyproto.TableName_GITHUB, entries, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete rows")
		return err
	}

	for _, mutateRowsResponse := range resp {
		for _, entry := range mutateRowsResponse.Entries {
			if entry.Status.Code != int32(codes.OK) {
				log.Error().Msgf("Failed to delete row at index %d: %v", entry.Index, entry.Status)
				return status.Error(codes.Internal, "Failed to delete row")
			}
		}
	}

	log.Info().Msgf("Successfully deleted GitHub state for repo %s/%s", githublib.RedactString(repoOwner), githublib.RedactString(repoName))
	return nil
}

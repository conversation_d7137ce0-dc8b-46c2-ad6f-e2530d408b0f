// K8S deployment file for the github state service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
function(env, namespace, cloud, namespace_config)
  // Only deploy github state in enterprise tier namespaces
  // This is only used for the github app for slackbot - an enterprise-only feature
  if namespace_config.flags.userTier != 'ENTERPRISE_TIER' then [] else
    // the app name is used in the kubernetes object names. It is also added as label to each object
    // so that we know which app an object belongs to
    local appName = 'github-state';

    // mutual TLS is enabled if the namespace config has the forceMtls flag set
    // MTLS ensures that the client and server certificates are valid
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

    // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
    // in the same namespace
    local clientCert = certLib.createClientCert(
      name='%s-client-cert' % appName,
      namespace=namespace,
      appName=appName,
      volumeName='client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );

    // creates a service account for the pod
    // a service account is needed to access GCP resources or kubernetes resources
    local serviceAccount = gcpLib.createServiceAccount(
      appName, env, cloud, namespace, iam=true,
    );

    local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

    local pubsub = [
      {
        apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
        kind: 'PubSubTopic',
        metadata: {
          name: 'github-state-%s-topic' % namespace,
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
        },
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: 'github-state-topic-policy',
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          resourceRef: {
            kind: 'PubSubTopic',
            name: 'github-state-%s-topic' % namespace,
          },
          bindings: [
            {
              role: 'roles/pubsub.publisher',
              members: [
                {
                  memberFrom: {
                    serviceAccountRef: {
                      name: serviceAccount.iamServiceAccountName,
                    },
                  },
                },
              ],
            },
          ],
        },
      },
      {
        apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
        kind: 'PubSubSubscription',
        metadata: {
          name: 'github-state-%s-sub' % namespace,
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          topicRef: {
            name: 'github-state-%s-topic' % namespace,
          },
          ackDeadlineSeconds: 300,
          retryPolicy: {
            minimumBackoff: '5s',
            maximumBackoff: '600s',
          },
          enableMessageOrdering: true,
        },
      },
      // Give access to the subscription.
      gcpLib.grantAccess(
        name='github-state-subscription-policy',
        namespace=namespace,
        appName=appName,
        env=env,
        resourceRef={
          kind: 'PubSubSubscription',
          name: 'github-state-%s-sub' % namespace,
        },
        bindings=[
          {
            role: 'roles/pubsub.subscriber',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
          {
            role: 'roles/pubsub.viewer',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
        ]
      ),
    ];

    // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
    local centralClientCert = certLib.createCentralClientCert(
      name='%s-central-client-cert' % appName,
      namespace=namespace,
      env=env,
      appName=appName,
      volumeName='central-client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );

    // creates a server certificate for MTLS
    local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                                namespace=namespace,
                                                appName=appName,
                                                dnsNames=grpcLib.grpcServiceNames(appName),
                                                volumeName='certs');

    // configuration that will be passed to the server as a JSON file
    local config = {
      port: 50051,
      server_mtls: if mtls then serverCert.config else null,
      client_mtls: if mtls then clientCert.config else null,
      central_client_mtls: if mtls then centralClientCert.config else null,
      prom_port: 9090,
      namespace: namespace,
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      model_finder_endpoint: 'model-finder-svc:50051',
      bigtable_proxy_endpoint: 'bigtable-proxy-svc:50051',
      content_manager_endpoint: 'content-manager-svc:50051',
      project_id: cloudInfo[cloud].projectId,
      topic_name: 'github-state-%s-topic' % namespace,
      subscription_name: 'github-state-%s-sub' % namespace,
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,

    };
    // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
    local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

    // creates a service for the pod
    // a service is needed to expose the pod to the outside world
    local services = grpcLib.grpcService(appName=appName, namespace=namespace);

    // creates a container that runs the server
    local container = {
      name: appName,
      target: {
        name: '//services/integrations/github/state/server:image',
        dst: 'github-state',
      },
      // the arguments that are passed to the server
      args: [
        '--config',
        configMap.filename,
      ],
      // ports that the pod exposes
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      // the environment variables that are passed to the server
      env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      // the volumes that are mounted into the pod
      volumeMounts: [
        configMap.volumeMountDef,
        centralClientCert.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      // the health check is used to determine if the pod is ready to receive traffic
      readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      // the liveness check is used to determine if the pod is alive
      livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      // the resource limits are used to determine how much CPU and memory the pod can use
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
    local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
    local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
    // the pod is the kubernetes object that runs the container
    local pod = {
      // the service account is used to access GCP resources or kubernetes resources
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      priorityClassName: cloudInfo.envToPriorityClass(env),
      affinity: affinity,
      tolerations: tolerations,
      // the volumes are mounted into the pod
      volumes: [
        // the config map is mounted into the pod
        configMap.podVolumeDef,
        // the client certificate is mounted into the pod
        centralClientCert.podVolumeDef,
        // the server certificate is mounted into the pod
        serverCert.podVolumeDef,
        // the client certificate is mounted into the pod
        clientCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    };

    // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
    local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
    local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
    local deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        progressDeadlineSeconds: 60 * 20,  // waiting for IAM permissions to propagate
        // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
        minReadySeconds: if env == 'DEV' then 0 else 60,
        // the number of pods that are running at the same time
        replicas: if env == 'DEV' then 1 else 2,
        // the strategy is used to determine how the deployment is rolled out
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod + {
            tolerations: tolerations,
            affinity: affinity,
          },
        },
      },
    };
    lib.flatten([
      configMap.objects,
      serviceAccount.objects,
      serverCert.objects,
      centralClientCert.objects,
      clientCert.objects,
      dynamicFeatureFlags.k8s_objects,
      pubsub,
      deployment,
      services,
    ])

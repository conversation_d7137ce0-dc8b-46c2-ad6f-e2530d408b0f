package main

import (
	"context"
	"io"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	proto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/server/proto"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	statusproto "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var disable = featureflags.NewBoolFlag("disable_github_state", false)

type githubStateServer struct {
	proto.UnimplementedGithubStateServer
	state        GithubState
	featureFlags featureflags.FeatureFlagHandle
}

func NewGithubStateServer(state GithubState, featureFlags featureflags.FeatureFlagHandle) *githubStateServer {
	return &githubStateServer{
		state:        state,
		featureFlags: featureFlags,
	}
}

func (s *githubStateServer) GetIndexedRefCheckpoints(ctx context.Context, request *proto.GetIndexedRefCheckpointsRequest) (*proto.GetIndexedRefCheckpointsResponse, error) {
	log.Info().Msg("GetIndexedRefCheckpoints")
	disabled, _ := disable.Get(s.featureFlags)
	if disabled {
		log.Warn().Msgf("GetIndexedRefCheckpoints is disabled")
		return nil, status.Error(codes.FailedPrecondition, "GetIndexedRefCheckpoints is disabled")
	}
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Warn().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	if !authInfo.HasScope(tokenexchangeproto.Scope_CONTENT_R) {
		log.Warn().Msgf("Access denied: %v", authInfo.Scope)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	indexedRefs, err := s.state.GetIndexedRefs(ctx, authInfo.TenantID)
	if err != nil {
		if status.Code(err) != codes.NotFound {
			log.Error().Err(err).Msg("Failed to get tenant info")
			return nil, err
		}
		// if not found, return empty list
	}

	resp := &proto.GetIndexedRefCheckpointsResponse{}
	for _, ref := range indexedRefs {
		resp.RefCheckpoints = append(resp.RefCheckpoints, &proto.RefCheckpoint{
			Ref:        ref.Ref.Ref,
			CommitSha:  ref.Ref.CommitSha,
			CommitTime: ref.Ref.CommitTime,
			Blobs:      ref.Ref.Blobs,
		})
	}
	refs := make([]string, 0)
	for _, ref := range resp.RefCheckpoints {
		refs = append(refs, githublib.RedactRefCheckpoint(ref))
	}
	log.Info().Msgf("GetIndexedRefs: %v", refs)

	return resp, nil
}

func (s *githubStateServer) GetCurrentRefStates(ctx context.Context, request *proto.GetCurrentRefStatesRequest) (*proto.GetCurrentRefStatesResponse, error) {
	log.Info().Msg("GetCurrentRefStates")
	disabled, _ := disable.Get(s.featureFlags)
	if disabled {
		log.Warn().Msgf("GetCurrentRefStates is disabled")
		return nil, status.Error(codes.FailedPrecondition, "GetCurrentRefStates is disabled")
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Warn().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	if !authInfo.HasScope(tokenexchangeproto.Scope_CONTENT_R) {
		log.Warn().Msgf("Access denied: %v", authInfo.Scope)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}
	currentRefStates, err := s.state.GetCurrentRefStates(ctx, authInfo.TenantID)
	if err != nil {
		if status.Code(err) != codes.NotFound {
			log.Error().Err(err).Msg("Failed to get tenant info")
			return nil, err
		}
		// if not found, return empty list
	}

	resp := &proto.GetCurrentRefStatesResponse{}
	for _, ref := range currentRefStates {
		resp.RefCheckpoints = append(resp.RefCheckpoints, &proto.RefCheckpoint{
			Ref:        ref.Ref,
			CommitSha:  ref.CommitSha,
			CommitTime: ref.CommitTime,
			Blobs:      ref.Blobs,
		})
	}
	refs := make([]string, 0)
	for _, ref := range resp.RefCheckpoints {
		refs = append(refs, githublib.RedactRefCheckpoint(ref))
	}
	log.Info().Msgf("GetCurrentRefStates: %v", refs)

	return resp, nil
}

func (s *githubStateServer) GetCurrentRefState(ctx context.Context,
	request *proto.GetCurrentRefStateRequest,
) (*proto.GetCurrentRefStateResponse, error) {
	log.Info().Msg("GetCurrentRefState")

	disabled, _ := disable.Get(s.featureFlags)
	if disabled {
		log.Warn().Msgf("GetCurrentRefState is disabled")
		return nil, status.Error(codes.FailedPrecondition, "GetCurrentRefState is disabled")
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Warn().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	if !authInfo.HasScope(tokenexchangeproto.Scope_CONTENT_R) {
		log.Warn().Msgf("Access denied: %v", authInfo.Scope)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	if request.Ref == nil {
		log.Warn().Msgf("ref is required")
		return nil, status.Error(codes.InvalidArgument, "ref is required")
	}
	if request.Ref.Repo == nil {
		log.Warn().Msgf("repo is required")
		return nil, status.Error(codes.InvalidArgument, "repo is required")
	}
	if request.Ref.Repo.RepoOwner == "" {
		log.Warn().Msgf("repo_owner is required")
		return nil, status.Error(codes.InvalidArgument, "repo_owner is required")
	}
	if request.Ref.Repo.RepoName == "" {
		log.Warn().Msgf("repo_name is required")
		return nil, status.Error(codes.InvalidArgument, "repo_name is required")
	}
	if request.Ref.Ref == "" {
		log.Warn().Msgf("ref is required")
		return nil, status.Error(codes.InvalidArgument, "ref is required")
	}

	ref, err := s.state.GetCurrentRefCheckpoint(ctx, authInfo.TenantID,
		request.Ref.Repo.RepoOwner, request.Ref.Repo.RepoName, request.Ref.Ref)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get tenant info")
		return nil, err
	}
	resp := &proto.GetCurrentRefStateResponse{
		LastUploadedRefCheckpoint: &proto.RefCheckpoint{
			Ref:        ref.Ref,
			CommitSha:  ref.CommitSha,
			CommitTime: ref.CommitTime,
			Blobs:      ref.Blobs,
		},
	}
	log.Info().Msgf("GetCurrentRefStateResponse: %s", githublib.RedactRefCheckpoint(resp.LastUploadedRefCheckpoint))
	return resp, nil
}

func (s *githubStateServer) GetCurrentRefFiles(request *proto.GetCurrentRefFilesRequest, stream proto.GithubState_GetCurrentRefFilesServer) error {
	log.Info().Msg("GetCurrentRefFiles")

	disabled, _ := disable.Get(s.featureFlags)
	if disabled {
		log.Warn().Msgf("GetCurrentRefFiles is disabled")
		return status.Error(codes.FailedPrecondition, "GetCurrentRefFiles is disabled")
	}

	ctx := stream.Context()
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	if !authInfo.HasScope(tokenexchangeproto.Scope_CONTENT_R) {
		log.Warn().Msgf("Access denied: %v", authInfo.Scope)
		return status.Error(codes.PermissionDenied, "Access denied")
	}
	if request.Ref == nil {
		log.Warn().Msgf("ref is required")
		return status.Error(codes.InvalidArgument, "ref is required")
	}
	if request.Ref.Repo == nil {
		log.Warn().Msgf("repo is required")
		return status.Error(codes.InvalidArgument, "repo is required")
	}
	if request.Ref.Repo.RepoOwner == "" {
		log.Warn().Msgf("repo_owner is required")
		return status.Error(codes.InvalidArgument, "repo_owner is required")
	}
	if request.Ref.Repo.RepoName == "" {
		log.Warn().Msgf("repo_name is required")
		return status.Error(codes.InvalidArgument, "repo_name is required")
	}
	if request.Ref.Ref == "" {
		log.Warn().Msgf("ref is required")
		return status.Error(codes.InvalidArgument, "ref is required")
	}
	if request.CommitSha == "" {
		log.Warn().Msgf("commit_sha is required")
		return status.Error(codes.InvalidArgument, "commit_sha is required")
	}
	log.Info().Msgf("GetCurrentRefFiles: %s", githublib.RedactRef(request.Ref))

	currentRef, err := s.state.GetCurrentRefCheckpoint(ctx, authInfo.TenantID, request.Ref.Repo.RepoOwner, request.Ref.Repo.RepoName, request.Ref.Ref)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get repo info")
		return err
	}

	if currentRef.CommitSha != request.CommitSha {
		log.Warn().Msgf("commit_sha is not the current commit: %v != %v", githublib.RedactString(currentRef.CommitSha),
			githublib.RedactString(request.CommitSha))
		return status.Error(codes.Aborted, "commit_sha is not the current commit")
	}
	if len(request.FilePaths) == 0 {
		log.Warn().Msgf("no files requested")
		err = stream.Send(&proto.GetCurrentRefFilesResponse{
			CommitSha: currentRef.CommitSha,
			FileInfos: []*proto.FileInfo{},
		})
		if err != nil {
			log.Error().Err(err).Msg("Failed to send GetCurrentRefFiles response")
			return err
		}
		return nil
	}

	foundFileCount := 0
	missingFileCount := 0
	for _, filePath := range request.FilePaths {
		found := false
		for _, fileInfo := range currentRef.FileInfos {
			if fileInfo.FilePath == filePath {
				found = true
				foundFileCount++
				err = stream.Send(&proto.GetCurrentRefFilesResponse{
					CommitSha: currentRef.CommitSha,
					FileInfos: []*proto.FileInfo{
						{
							FilePath: filePath,
							BlobName: fileInfo.BlobName,
						},
					},
				})
				if err != nil {
					log.Error().Err(err).Msg("Failed to send GetCurrentRefFiles response")
					return err
				}
			}
		}
		if !found {
			missingFileCount++
			err = stream.Send(&proto.GetCurrentRefFilesResponse{
				CommitSha: currentRef.CommitSha,
				FileInfos: []*proto.FileInfo{
					{
						FilePath: filePath,
						Status:   &statusproto.Status{Code: int32(codes.NotFound)},
					},
				},
			})
			if err != nil {
				log.Error().Err(err).Msg("Failed to send GetCurrentRefFiles response")
				return err
			}
		}
	}
	log.Info().Msgf("Found %d files, %d missing files", foundFileCount, missingFileCount)
	return nil
}

func (s *githubStateServer) Clear(ctx context.Context, request *proto.ClearRequest) (*proto.ClearResponse, error) {
	log.Info().Msg("Clear")

	disabled, _ := disable.Get(s.featureFlags)
	if disabled {
		log.Warn().Msgf("Clear is disabled")
		return nil, status.Error(codes.FailedPrecondition, "Clear is disabled")
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Warn().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	if !authInfo.HasScope(tokenexchangeproto.Scope_CONTENT_RW) {
		log.Warn().Msgf("Access denied: %v", authInfo.Scope)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}
	err := s.state.Clear(ctx, authInfo.TenantID)
	return &proto.ClearResponse{}, err
}

func checkGrpcError(err error, code codes.Code) bool {
	// Check if the error is a gRPC error and match the status code
	if st, ok := status.FromError(err); ok {
		return st.Code() == code
	}
	return false
}

func (s *githubStateServer) handleDiffInfo(req *proto.UpdateCurrentRefCheckpointRequest, newUploadedRefData *githubstatepersistproto.CurrentUploadedRefData) error {
	if req.DiffInfos != nil {
		for _, diffInfo := range req.DiffInfos {
			if _, ok := diffInfo.Change.(*proto.DiffInfo_Deleted); ok {
				for i, fileInfo := range newUploadedRefData.FileInfos {
					if fileInfo.FilePath == diffInfo.FilePath {
						newUploadedRefData.FileInfos = append(newUploadedRefData.FileInfos[:i], newUploadedRefData.FileInfos[i+1:]...)
						break
					}
				}
			} else if v, ok := diffInfo.Change.(*proto.DiffInfo_ContentBlobName); ok {
				found := false
				for i, fileInfo := range newUploadedRefData.FileInfos {
					if fileInfo.FilePath == diffInfo.FilePath {
						newUploadedRefData.FileInfos[i].BlobName = v.ContentBlobName
						found = true
						break
					}
				}
				if found {
					continue
				}
				newUploadedRefData.FileInfos = append(newUploadedRefData.FileInfos, &githubstatepersistproto.FileInfo{
					FilePath: diffInfo.FilePath,
					BlobName: v.ContentBlobName,
				})
			} else {
				return status.Error(codes.InvalidArgument, "diff_info is invalid")
			}
		}
	}
	return nil
}

func (s *githubStateServer) UpdateCurrentRefCheckpoint(stream proto.GithubState_UpdateCurrentRefCheckpointServer) error {
	log.Info().Msg("UpdateCurrentRefCheckpoint")

	disabled, _ := disable.Get(s.featureFlags)
	if disabled {
		log.Warn().Msgf("UpdateCurrentRefCheckpoint is disabled")
		return status.Error(codes.FailedPrecondition, "UpdateCurrentRefCheckpoint is disabled")
	}

	ctx := stream.Context()
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	if !authInfo.HasScope(tokenexchangeproto.Scope_CONTENT_RW) {
		log.Warn().Msgf("Access denied")
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	// get first message
	firstReq, err := stream.Recv()
	if err != nil {
		return err
	}
	log.Info().Msgf("UpdateCurrentRefCheckpoint: ref=%s", githublib.RedactRef(firstReq.Ref))
	if firstReq.Ref == nil {
		log.Warn().Msgf("ref is required")
		return status.Error(codes.InvalidArgument, "ref is required")
	}
	if firstReq.Ref.Repo == nil {
		log.Warn().Msgf("repo is required")
		return status.Error(codes.InvalidArgument, "repo is required")
	}
	if firstReq.Ref.Repo.RepoOwner == "" {
		log.Warn().Msgf("repo_owner is required")
		return status.Error(codes.InvalidArgument, "repo_owner is required")
	}
	if firstReq.Ref.Repo.RepoName == "" {
		log.Warn().Msgf("repo_name is required")
		return status.Error(codes.InvalidArgument, "repo_name is required")
	}
	if firstReq.Ref.Ref == "" {
		log.Warn().Msgf("ref is required")
		return status.Error(codes.InvalidArgument, "ref is required")
	}
	if firstReq.CommitSha == "" {
		log.Warn().Msgf("commit_sha is required")
		return status.Error(codes.InvalidArgument, "commit_sha is required")
	}
	if firstReq.CommitTime == nil {
		log.Warn().Msgf("commit_time is required")
		return status.Error(codes.InvalidArgument, "commit_time is required")
	}

	newUploadedRefData := &githubstatepersistproto.CurrentUploadedRefData{}
	newUploadedRefData.Ref = firstReq.Ref
	newUploadedRefData.CommitSha = firstReq.CommitSha
	newUploadedRefData.CommitTime = firstReq.CommitTime
	newUploadedRefData.ParentCommitShas = firstReq.ParentCommitShas
	newUploadedRefData.Blobs = firstReq.Blobs

	currentUploadedRepoRef, err := s.state.GetCurrentRefCheckpoint(ctx, authInfo.TenantID, firstReq.Ref.Repo.RepoOwner, firstReq.Ref.Repo.RepoName, firstReq.Ref.Ref)
	if err != nil {
		if !checkGrpcError(err, codes.NotFound) {
			log.Error().Err(err).Msg("Failed to get current uploaded repo ref")
			return err
		}
		// not found is okay
	}
	if currentUploadedRepoRef != nil && !firstReq.ForceUpload && currentUploadedRepoRef.CommitSha != firstReq.DiffCommitSha {
		log.Warn().Msgf("diff_commit_sha is not the current commit: %v != %v", githublib.RedactString(currentUploadedRepoRef.CommitSha),
			githublib.RedactString(firstReq.DiffCommitSha))
		return status.Error(codes.Aborted, "diff_commit_sha is not the current commit")
	}
	if currentUploadedRepoRef != nil {
		log.Info().Msgf("UpdateCurrentRefCheckpoint: current uploaded ref=%s, commit=%s", githublib.RedactRef(currentUploadedRepoRef.Ref), githublib.RedactString(currentUploadedRepoRef.CommitSha))
		newUploadedRefData.FileInfos = currentUploadedRepoRef.FileInfos
	} else {
		log.Info().Msgf("UpdateCurrentRefCheckpoint: current uploaded ref=nil, commit=nil")
	}

	if err := s.handleDiffInfo(firstReq, newUploadedRefData); err != nil {
		log.Error().Err(err).Msgf("Failed to handle diff info for ref %s, commit %s",
			githublib.RedactRef(newUploadedRefData.Ref),
			githublib.RedactString(newUploadedRefData.CommitSha))
		return err
	}

	for {
		req, err := stream.Recv()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Error().Err(err).Msg("Failed to receive UpdateCurrentRefCheckpointRequest")
			return err
		}
		log.Info().Msgf("UpdateCurrentRefCheckpoint: ref=%s, commit=%s, blobs=%v, diff_info_count=%d",
			githublib.RedactRef(req.Ref),
			githublib.RedactString(req.CommitSha),
			githublib.FormatBlobs(req.Blobs),
			len(req.DiffInfos))
		if req.Ref != nil || req.CommitSha != "" || req.ParentCommitShas != nil || req.Blobs != nil {
			return status.Error(codes.InvalidArgument, "ref, commit_sha, parent_commit_shas, blobs must not be set")
		}
		if err := s.handleDiffInfo(req, newUploadedRefData); err != nil {
			log.Error().Err(err).Msgf("Failed to handle diff info for ref %s, commit %s",
				githublib.RedactRef(newUploadedRefData.Ref),
				githublib.RedactString(newUploadedRefData.CommitSha))
			return err
		}
	}
	log.Info().Msgf("UpdateCurrentRefCheckpoint: collection ended: ref=%s, commit=%s",
		githublib.RedactRef(newUploadedRefData.Ref),
		githublib.RedactString(newUploadedRefData.CommitSha))

	updated, err := s.state.UpdateCurrentRefCheckpoint(ctx, authInfo.TenantID, newUploadedRefData, firstReq.ForceUpload)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to update current ref checkpoint for ref %s, commit %s",
			githublib.RedactRef(newUploadedRefData.Ref),
			githublib.RedactString(newUploadedRefData.CommitSha))
		return err
	}
	if updated {
		log.Info().Msgf("UpdateCurrentRefCheckpoint: updated: ref=%v", githublib.RedactRef(newUploadedRefData.Ref))
	} else {
		log.Info().Msgf("UpdateCurrentRefCheckpoint: update not needed")
	}

	err = stream.SendMsg(&proto.UpdateCurrentRefCheckpointResponse{})
	if err != nil {
		log.Error().Err(err).Msg("Failed to send UpdateCurrentRefCheckpointResponse")
		return err
	}
	return nil
}

func (s *githubStateServer) DeleteGithubStateForRepos(ctx context.Context, req *proto.DeleteGithubStateForReposRequest) (*proto.DeleteGithubStateForReposResponse, error) {
	log.Info().Msg("DeleteGithubStateForRepos")

	disabled, _ := disable.Get(s.featureFlags)
	if disabled {
		log.Warn().Msgf("DeleteGithubStateForRepos is disabled")
		return nil, status.Error(codes.FailedPrecondition, "DeleteGithubStateForRepos is disabled")
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msgf("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		log.Warn().Msgf("tenant_id is required")
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}
	if !authInfo.HasScope(tokenexchangeproto.Scope_CONTENT_RW) {
		log.Warn().Msgf("Access denied")
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	for _, repo := range req.Repos {
		if repo.RepoOwner == "" {
			log.Warn().Msgf("repo_owner is required")
			return nil, status.Error(codes.InvalidArgument, "repo_owner is required")
		}
		if repo.RepoName == "" {
			log.Warn().Msgf("repo_name is required")
			return nil, status.Error(codes.InvalidArgument, "repo_name is required")
		}

		err := s.state.DeleteGithubState(ctx, authInfo.TenantID, repo.RepoOwner, repo.RepoName)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to delete state for repo %s/%s", githublib.RedactString(repo.RepoOwner), githublib.RedactString(repo.RepoName))
			return nil, err
		}
	}

	return &proto.DeleteGithubStateForReposResponse{}, nil
}

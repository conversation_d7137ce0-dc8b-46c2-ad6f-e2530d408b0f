package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"cloud.google.com/go/pubsub"
	apiproxyclient "github.com/augmentcode/augment/services/api_proxy/client"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	proto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("config", "", "Path to config file")

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	Namespace string `json:"namespace"`

	ModelFinderEndpoint    string `json:"model_finder_endpoint"`
	BigtableProxyEndpoint  string `json:"bigtable_proxy_endpoint"`
	TokenExchangeEndpoint  string `json:"token_exchange_endpoint"`
	ContentManagerEndpoint string `json:"content_manager_endpoint"`
	TenantWatcherEndpoint  string `json:"tenant_watcher_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	Checkpoint string `json:"checkpoint"`

	IncludeDefaultRepo bool `json:"include_default_repo"`

	ProjectId        string `json:"project_id"`
	TopicName        string `json:"topic_name"`
	SubscriptionName string `json:"subscription_name"`

	// the path to the feature flag sdk key
	FeatureFlagsSdkKeyPath string `json:"feature_flags_sdk_key_path"`

	// the endpoint for the dynamic feature flags service or None if not used
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`
}

// retryWithConstantBackoff attempts to subscribe to the pending task queue with a constant backoff.
// It will retry for the specified duration with the specified backoff interval between attempts.
func retryWithConstantBackoff(
	ctx context.Context,
	handler func(ctx context.Context) error,
	maxDuration time.Duration,
	backoffInterval time.Duration,
) error {
	endTime := time.Now().Add(maxDuration)

	for {
		// Check if we've exceeded the maximum retry duration
		if time.Now().After(endTime) {
			return fmt.Errorf("failed to subscribe after retrying for %v", maxDuration)
		}

		// Attempt to subscribe
		err := handler(ctx)
		if err == nil {
			return nil // Success
		}

		// Log the error but continue retrying
		log.Warn().Err(err).Msgf("Error subscribing to pending tasks, retrying in %v", backoffInterval)

		// Wait for the backoff interval or until the context is canceled
		select {
		case <-time.After(backoffInterval):
			// Continue with the next retry
		case <-ctx.Done():
			return ctx.Err() // Context was canceled
		}
	}
}

func run(config *Config, grpcServer *grpc.Server, githubStateServer proto.GithubStateServer) error {
	proto.RegisterGithubStateServer(grpcServer, githubStateServer)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())
	return grpcServer.Serve(lis)
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	go func() {
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		cancel()
	}()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	bigtableProxyClient, err := bigtableproxy.NewBigtableProxyClient(config.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating bigtable proxy client")
	}
	defer bigtableProxyClient.Close()

	modelFinderClient, err := apiproxyclient.NewModelFinderClient(config.ModelFinderEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating model finder client")
	}
	defer modelFinderClient.Close()

	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, config.Namespace)
	defer tenantCache.Close()

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	featureFlagHandle, err := featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	contentManagerClient, err := contentmanagerclient.NewContentManagerClient(
		config.ContentManagerEndpoint, clientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating content manager client")
	}
	defer contentManagerClient.Close()

	pubsubClient, err := pubsub.NewClient(ctx, config.ProjectId)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating pubsub client")
	}
	defer pubsubClient.Close()

	pendingTaskPublisher := NewPendingTaskPublisher(pubsubClient, config.TopicName)

	pendingTaskSubscriber := NewPendingTaskSubscriber(pubsubClient, config.SubscriptionName)

	state := NewGithubState(bigtableProxyClient, pendingTaskPublisher)
	findMissing := NewFindMissing(clientCreds, 1000)

	indexer := NewIndexChecker(findMissing, state, modelFinderClient, tenantCache, tokenExchangeClient, contentManagerClient)

	err = retryWithConstantBackoff(ctx, func(ctx context.Context) error {
		exists, err := pendingTaskSubscriber.Exists(ctx)
		if err != nil {
			return err
		}
		if !exists {
			return fmt.Errorf("subscription does not exist")
		}
		return nil
	}, 90*time.Second, 15*time.Second)

	go func() {
		// retry subscribing with constant backoff. often the pubsub subscription is not available
		// immediately after the server starts.
		err = pendingTaskSubscriber.Subscribe(ctx, indexer.Check)
		if err != nil {
			log.Fatal().Err(err).Msg("Error running pending task subscriber after retries")
		}
	}()

	server := NewGithubStateServer(state, featureFlagHandle)

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	go func() {
		<-ctx.Done()
		grpcServer.GracefulStop()
	}()

	err = run(&config, grpcServer, server)
	if err != nil {
		log.Fatal().Err(err).Msg("Error serving")
	}
}

package main

import (
	"context"

	"github.com/augmentcode/augment/base/blob_names"
	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	apiproxyclient "github.com/augmentcode/augment/services/api_proxy/client"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	modelinstanceproto "github.com/augmentcode/augment/services/deploy/model_instance/proto"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/server/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// the index checker is checking the pending commits for indexing. If a commit is fully indexed,
// it is moved to the indexed state.
type IndexChecker interface {
	Check(ctx context.Context, task *githubstatepersistproto.PendingRefData) error
}

type indexChecker struct {
	findMissing          FindMissing
	state                GithubState
	modelFinderClient    apiproxyclient.ModelFinderClient
	tenantCache          tenantwatcherclient.TenantCache
	tokenExchangeClient  tokenexchangeclient.TokenExchangeClient
	contentManagerClient contentmanagerclient.ContentManagerClient
	sessionId            requestcontext.RequestSessionId
}

func NewIndexChecker(findMissing FindMissing, state GithubState, modelFinderClient apiproxyclient.ModelFinderClient, tenantCache tenantwatcherclient.TenantCache, tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	contentManagerClient contentmanagerclient.ContentManagerClient,
) IndexChecker {
	i := &indexChecker{
		findMissing:          findMissing,
		state:                state,
		modelFinderClient:    modelFinderClient,
		tenantCache:          tenantCache,
		tokenExchangeClient:  tokenExchangeClient,
		contentManagerClient: contentManagerClient,
		sessionId:            requestcontext.NewRandomRequestSessionId(),
	}
	log.Info().Msgf("Created index checker: sessionId=%s", i.sessionId)
	return i
}

// getRequestContext gets the request context for the given tenant.
func (c *indexChecker) getRequestContext(tenantID string) (*requestcontext.RequestContext, error) {
	authToken, err := c.tokenExchangeClient.GetSignedTokenForService(context.Background(), tenantID, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_CONTENT_RW,
	})
	if err != nil {
		return nil, err
	}
	requestContext := &requestcontext.RequestContext{
		RequestId:        requestcontext.NewRandomRequestId(),
		RequestSessionId: c.sessionId,
		RequestSource:    "background",
		AuthToken:        authToken,
	}
	return requestContext, nil
}

// resolveBlobNames resolves the blob names from the given blobs.
//
// Args:
//
//	ctx: the context
//	blobs: the blobs to resolve
//	requestContext: the request context
//
// Returns:
//
//	blobNames: the resolved blob names
func (c *indexChecker) resolveBlobNames(ctx context.Context, blobs *blobsproto.Blobs, requestContext *requestcontext.RequestContext) ([]blob_names.BlobName, error) {
	log.Info().Msgf("Resolve blobs: %v", blobs)
	if blobs == nil {
		log.Warn().Msgf("No blobs")
		return nil, nil
	}
	blobNames := make([]blob_names.BlobName, 0)
	if blobs.BaselineCheckpointId != nil && *blobs.BaselineCheckpointId != "" {
		b, err := c.contentManagerClient.GetAllBlobsFromCheckpoint(ctx, *blobs.BaselineCheckpointId, requestContext)
		if err != nil {
			return nil, err
		}
		blobNames = append(blobNames, b...)
	}
	for _, blob := range blobs.Added {
		blobNames = append(blobNames, blob_names.NewBlobNameFromBytes(blob))
	}
	for _, blob := range blobs.Deleted {
		currentBlobName := blob_names.NewBlobNameFromBytes(blob)
		for i, blobName := range blobNames {
			if blobName == currentBlobName {
				blobNames = append(blobNames[:i], blobNames[i+1:]...)
				break
			}
		}
	}

	log.Info().Msgf("blobs: %v", formatBlobNames(blobNames))
	return blobNames, nil
}

// runFindMissing runs the find_missing endpoint of the content manager to check if a commit is fully indexed.
func (c *indexChecker) runFindMissing(ctx context.Context, model *modelinstanceproto.ModelInstanceConfig, blobNames []blob_names.BlobName, requestContext *requestcontext.RequestContext) (bool, error) {
	count, err := c.findMissing.FindMissing(ctx, model, blobNames, requestContext)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to run find missing on model %s", *model.Name)
		return false, err
	}
	log.Info().Msgf("Found %d missing blobs on model %s", count, *model.Name)
	return count == 0, nil
}

// moveToIndexed moves the given ref to the indexed state.
func (c *indexChecker) moveToIndexed(ctx context.Context, refData *githubstatepersistproto.PendingRefData, requestContext *requestcontext.RequestContext) error {
	log.Info().Msgf("Move to indexed: ref=%v, tenantID=%s", githublib.RedactRef(refData.Ref.Ref), refData.TenantId)
	err := c.state.MarkIndexedRefCheckpoint(ctx, refData.TenantId, &githubstatepersistproto.IndexedRefData{
		Ref:      refData.Ref,
		TenantId: refData.TenantId,
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to move to indexed")
		return err
	}
	return nil
}

func (c *indexChecker) Check(ctx context.Context, refData *githubstatepersistproto.PendingRefData) error {
	log.Info().Msgf("Check ref: ref=%v, tenantID=%s", githublib.RedactRef(refData.Ref.Ref), refData.TenantId)
	requestContext, err := c.getRequestContext(refData.TenantId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get request context")
		return err
	}
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)
	models, err := c.modelFinderClient.GetGenerationModels(ctx, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get models")
		return err
	}
	blobNames, err := c.resolveBlobNames(ctx, refData.Ref.Blobs, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to resolve blob names")
		return err
	}

	allOk := true
	for _, model := range models {
		log.Info().Msgf("model: %v", *model.Name)
		ok, err := c.runFindMissing(ctx, model, blobNames, requestContext)
		if err != nil {
			log.Error().Err(err).Msg("Failed to run find missing")
			return err
		}
		if !ok {
			log.Info().Msg("Found missing blobs")
			allOk = false
			break
		}
	}

	if allOk {
		log.Info().Msgf("All models are ok for ref %s", githublib.RedactRef(refData.Ref.Ref))
		err = c.moveToIndexed(ctx, refData, requestContext)
		if err != nil {
			log.Error().Err(err).Msg("Failed to move to indexed")
			return err
		}
		return nil
	} else {
		log.Info().Msgf("Not all models are ok for ref %s", githublib.RedactRef(refData.Ref.Ref))
		return status.Error(codes.Internal, "Not all models are ok")
	}
}

package main

import (
	"context"
	"errors"
	"io"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/augmentcode/augment/base/blob_names"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	githubstateproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/server/proto"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	"google.golang.org/protobuf/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

type refKey struct {
	tenantID  string
	repoOwner string
	repoName  string
	ref       string
}

type FakeGithubState struct {
	indexedRefs     map[refKey]*githubstatepersistproto.IndexedRefData
	uploadedRefData map[refKey]*githubstatepersistproto.CurrentUploadedRefData
	fileInfos       map[refKey]*githubstatepersistproto.FileInfoContainer
}

func NewFakeGithubState() *FakeGithubState {
	return &FakeGithubState{
		indexedRefs:     make(map[refKey]*githubstatepersistproto.IndexedRefData),
		uploadedRefData: make(map[refKey]*githubstatepersistproto.CurrentUploadedRefData),
		fileInfos:       make(map[refKey]*githubstatepersistproto.FileInfoContainer),
	}
}

func (m *FakeGithubState) MarkIndexedRefCheckpoint(ctx context.Context, tenantID string, refData *githubstatepersistproto.IndexedRefData) error {
	key := refKey{
		tenantID:  tenantID,
		repoOwner: refData.Ref.Ref.Repo.RepoOwner,
		repoName:  refData.Ref.Ref.Repo.RepoName,
		ref:       refData.Ref.Ref.Ref,
	}
	m.indexedRefs[key] = refData
	return nil
}

func (m *FakeGithubState) AddIndexedRef(indexedRef *githubstatepersistproto.IndexedRefData) error {
	if indexedRef == nil {
		return nil
	}
	if indexedRef.TenantId == "" {
		return errors.New("tenant_id is required")
	}
	key := refKey{
		tenantID:  indexedRef.TenantId,
		repoOwner: indexedRef.Ref.Ref.Repo.RepoOwner,
		repoName:  indexedRef.Ref.Ref.Repo.RepoName,
		ref:       indexedRef.Ref.Ref.Ref,
	}
	m.indexedRefs[key] = indexedRef
	return nil
}

func (m *FakeGithubState) GetIndexedRefs(ctx context.Context, tenantID string) ([]*githubstatepersistproto.IndexedRefData, error) {
	resp := make([]*githubstatepersistproto.IndexedRefData, 0)
	for key, ref := range m.indexedRefs {
		if key.tenantID == tenantID {
			resp = append(resp, ref)
		}
	}
	return resp, nil
}

func (m *FakeGithubState) GetCurrentRefStates(ctx context.Context, tenantID string) ([]*githubstatepersistproto.CurrentUploadedRefData, error) {
	resp := make([]*githubstatepersistproto.CurrentUploadedRefData, 0)
	for key, ref := range m.uploadedRefData {
		if key.tenantID == tenantID {
			resp = append(resp, ref)
		}
	}
	return resp, nil
}

func (m *FakeGithubState) GetIndexedRef(ctx context.Context, tenantID string, repoOwner string, repoName string, ref string) (*githubstatepersistproto.IndexedRefData, error) {
	key := refKey{
		tenantID:  tenantID,
		repoOwner: repoOwner,
		repoName:  repoName,
		ref:       ref,
	}
	d, ok := m.indexedRefs[key]
	if !ok {
		return nil, status.Error(codes.NotFound, "Not found")
	}
	return d, nil
}

func (m *FakeGithubState) GetCurrentRefCheckpoint(ctx context.Context, tenantID string, repoOwner string, repoName string, ref string) (*githubstatepersistproto.CurrentUploadedRefData, error) {
	key := refKey{
		tenantID:  tenantID,
		repoOwner: repoOwner,
		repoName:  repoName,
		ref:       ref,
	}
	d, ok := m.uploadedRefData[key]
	if !ok {
		return nil, status.Error(codes.NotFound, "Not found")
	}
	f, ok := m.fileInfos[key]
	if !ok {
		return nil, status.Error(codes.NotFound, "Not found")
	}
	d.FileInfos = f.FileInfos
	return d, nil
}

func (m *FakeGithubState) UpdateCurrentRefCheckpoint(ctx context.Context,
	tenantID string,
	uploadedRefData *githubstatepersistproto.CurrentUploadedRefData,
	forceUpload bool,
) (bool, error) {
	key := refKey{
		tenantID:  tenantID,
		repoOwner: uploadedRefData.Ref.Repo.RepoOwner,
		repoName:  uploadedRefData.Ref.Repo.RepoName,
		ref:       uploadedRefData.Ref.Ref,
	}
	// we clone the uploaded ref data and then delete file infos from it to avoid modifying what was passed in by pointer
	clonedUploadedRefData := proto.Clone(uploadedRefData).(*githubstatepersistproto.CurrentUploadedRefData)
	clonedUploadedRefData.FileInfos = nil
	m.fileInfos[key] = &githubstatepersistproto.FileInfoContainer{FileInfos: uploadedRefData.FileInfos}
	m.uploadedRefData[key] = clonedUploadedRefData

	return true, nil
}

func (f *FakeGithubState) DeleteGithubState(ctx context.Context, tenantID string, repoOwner string, repoName string) error {
	newIndexedRefs := make(map[refKey]*githubstatepersistproto.IndexedRefData)
	for refKey, ref := range f.indexedRefs {
		if refKey.repoName != repoName || refKey.repoOwner != repoOwner {
			newIndexedRefs[refKey] = ref
		}
	}
	f.indexedRefs = newIndexedRefs

	newUploadedRefData := make(map[refKey]*githubstatepersistproto.CurrentUploadedRefData)
	for refKey, ref := range f.uploadedRefData {
		if refKey.repoName != repoName || refKey.repoOwner != repoOwner {
			newUploadedRefData[refKey] = ref
		}
	}
	f.uploadedRefData = newUploadedRefData

	return nil
}

func (m *FakeGithubState) Clear(ctx context.Context, tenantID string) error {
	m.indexedRefs = make(map[refKey]*githubstatepersistproto.IndexedRefData)
	m.uploadedRefData = make(map[refKey]*githubstatepersistproto.CurrentUploadedRefData)
	return nil
}

// Test GetRepoCheckpoints
func TestGetRepoCheckpoints(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R"},
	})

	req := &githubstateproto.GetIndexedRefCheckpointsRequest{}
	resp, err := server.GetIndexedRefCheckpoints(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.RefCheckpoints, 0)

	fakeState.AddIndexedRef(&githubstatepersistproto.IndexedRefData{
		Ref: &githubstatepersistproto.RefInfo{
			Ref: &githubstateproto.GithubRef{
				Ref: "main",
				Repo: &githubstateproto.GithubRepo{
					RepoOwner: "owner",
					RepoName:  "repo",
				},
			},
			CommitSha: "abc123",
		},
		TenantId: "test-tenant",
	})

	resp, err = server.GetIndexedRefCheckpoints(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.RefCheckpoints, 1)
	assert.Equal(t, "abc123", resp.RefCheckpoints[0].CommitSha)
}

// Test GetCurrentRefState
func TestGetCurrentRefState(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	req := &githubstateproto.GetCurrentRefStateRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
	}
	resp, err := server.GetCurrentRefState(ctx, req)

	assert.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	resp, err = server.GetCurrentRefState(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "abc123", resp.LastUploadedRefCheckpoint.CommitSha)
	assert.Equal(t, int64(1234567890), resp.LastUploadedRefCheckpoint.CommitTime.Seconds)
}

// TestGetCurrentRefStates
func TestGetCurrentRefStates(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	req := &githubstateproto.GetCurrentRefStatesRequest{}
	resp, err := server.GetCurrentRefStates(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.RefCheckpoints, 0)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)

	resp, err = server.GetCurrentRefStates(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.RefCheckpoints, 1)
	assert.Equal(t, "abc123", resp.RefCheckpoints[0].CommitSha)
	assert.Equal(t, int64(1234567890), resp.RefCheckpoints[0].CommitTime.Seconds)
}

// Test GetCurrentRefFiles
func TestGetCurrentRefFiles(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	req := &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc123",
		FilePaths: []string{"file1.txt", "file2.txt"},
	}

	mockStream := &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err := server.GetCurrentRefFiles(req, mockStream)

	assert.Error(t, err)
	assert.Equal(t, codes.NotFound, status.Code(err))

	blobName1, err := blob_names.NewBlobNameProto("abc123", false)
	assert.NoError(t, err)
	blobName2, err := blob_names.NewBlobNameProto("abc124", false)
	assert.NoError(t, err)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName2,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	req = &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc123",
		FilePaths: []string{"file1.txt", "file2.txt"},
	}

	mockStream = &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)

	assert.NoError(t, err)
	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 2)
	assert.Equal(t, "abc123", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file1.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, blobName1, mockStream.sentResponses[0].FileInfos[0].BlobName)
	assert.Equal(t, "file2.txt", mockStream.sentResponses[1].FileInfos[0].FilePath)
	assert.Equal(t, blobName2, mockStream.sentResponses[1].FileInfos[0].BlobName)
}

func TestGetCurrentRefFilesSubsetOfFiles(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	blobName1, err := blob_names.NewBlobNameProto("abc123", false)
	assert.NoError(t, err)
	blobName2, err := blob_names.NewBlobNameProto("abc124", false)
	assert.NoError(t, err)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName2,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	req := &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc123",
		FilePaths: []string{"file1.txt"},
	}

	mockStream := &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)

	assert.NoError(t, err)
	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 1)
	assert.Equal(t, "abc123", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file1.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, blobName1, mockStream.sentResponses[0].FileInfos[0].BlobName)
}

func TestGetCurrentRefFilesMissingFile(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	blobName1, err := blob_names.NewBlobNameProto("abc123", false)
	assert.NoError(t, err)
	blobName2, err := blob_names.NewBlobNameProto("abc124", false)
	assert.NoError(t, err)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName2,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	req := &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc123",
		FilePaths: []string{"file1.txt", "file3.txt"}, // file3.txt is missing
	}

	mockStream := &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)

	assert.NoError(t, err)
	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 2)
	assert.Equal(t, "abc123", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file1.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, blobName1, mockStream.sentResponses[0].FileInfos[0].BlobName)
	assert.Equal(t, "file3.txt", mockStream.sentResponses[1].FileInfos[0].FilePath)
	assert.Equal(t, int32(codes.NotFound), mockStream.sentResponses[1].FileInfos[0].Status.Code)
}

func TestGetCurrentRefFilesUpdate(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	blobName1, err := blob_names.NewBlobNameProto("abc123", false)
	assert.NoError(t, err)
	blobName2, err := blob_names.NewBlobNameProto("abc124", false)
	assert.NoError(t, err)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName2,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	blobName3, err := blob_names.NewBlobNameProto("abc125", false)
	assert.NoError(t, err)
	blobName4, err := blob_names.NewBlobNameProto("abc126", false)
	assert.NoError(t, err)

	updateStream = NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:     "abc124",
			CommitTime:    &timestamppb.Timestamp{Seconds: 1234567890},
			DiffCommitSha: "abc123",
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName3,
					},
				},
				{
					FilePath: "file3.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName4,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	req := &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc123",
		FilePaths: []string{"file2.txt", "file3.txt"},
	}

	mockStream := &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)
	assert.Error(t, err)
	assert.Equal(t, codes.Aborted, status.Code(err))

	req = &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc124",
		FilePaths: []string{"file2.txt", "file3.txt"},
	}

	mockStream = &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)
	assert.NoError(t, err)

	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 2)
	assert.Equal(t, "abc124", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file2.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, blobName3, mockStream.sentResponses[0].FileInfos[0].BlobName)
	assert.Equal(t, "file3.txt", mockStream.sentResponses[1].FileInfos[0].FilePath)
	assert.Equal(t, blobName4, mockStream.sentResponses[1].FileInfos[0].BlobName)

	// and another update

	blobName5, err := blob_names.NewBlobNameProto("abc127", false)
	assert.NoError(t, err)
	blobName6, err := blob_names.NewBlobNameProto("abc128", false)
	assert.NoError(t, err)

	updateStream = NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:     "abc125",
			CommitTime:    &timestamppb.Timestamp{Seconds: 1234567890},
			DiffCommitSha: "abc124",
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName5,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName6,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	req = &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc125",
		FilePaths: []string{"file1.txt", "file2.txt", "file3.txt"},
	}

	mockStream = &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)

	assert.NoError(t, err)
	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 3)
	assert.Equal(t, "abc125", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file1.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, blobName5, mockStream.sentResponses[0].FileInfos[0].BlobName)
	assert.Equal(t, "file2.txt", mockStream.sentResponses[1].FileInfos[0].FilePath)
	assert.Equal(t, blobName6, mockStream.sentResponses[1].FileInfos[0].BlobName)
	assert.Equal(t, "file3.txt", mockStream.sentResponses[2].FileInfos[0].FilePath)
	assert.Equal(t, blobName4, mockStream.sentResponses[2].FileInfos[0].BlobName)
}

func TestGetCurrentRefFilesUpdateWithStream(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	blobName1, err := blob_names.NewBlobNameProto("abc123", false)
	assert.NoError(t, err)
	blobName2, err := blob_names.NewBlobNameProto("abc124", false)
	assert.NoError(t, err)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName2,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	blobName3, err := blob_names.NewBlobNameProto("abc125", false)
	assert.NoError(t, err)
	blobName4, err := blob_names.NewBlobNameProto("abc126", false)
	assert.NoError(t, err)

	updateStream = NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:     "abc124",
			CommitTime:    &timestamppb.Timestamp{Seconds: 1234567890},
			DiffCommitSha: "abc123",
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName3,
					},
				},
			},
		},
		{
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file3.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName4,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	req := &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc123",
		FilePaths: []string{"file2.txt", "file3.txt"},
	}

	mockStream := &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)
	assert.Error(t, err)
	assert.Equal(t, codes.Aborted, status.Code(err))

	req = &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc124",
		FilePaths: []string{"file2.txt", "file3.txt"},
	}

	mockStream = &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)
	assert.NoError(t, err)

	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 2)
	assert.Equal(t, "abc124", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file2.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, blobName3, mockStream.sentResponses[0].FileInfos[0].BlobName)
	assert.Equal(t, "file3.txt", mockStream.sentResponses[1].FileInfos[0].FilePath)
	assert.Equal(t, blobName4, mockStream.sentResponses[1].FileInfos[0].BlobName)
}

func TestGetCurrentRefFilesDelete(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	blobName1, err := blob_names.NewBlobNameProto("abc123", false)
	assert.NoError(t, err)
	blobName2, err := blob_names.NewBlobNameProto("abc124", false)
	assert.NoError(t, err)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName2,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	updateStream = NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:     "abc124",
			CommitTime:    &timestamppb.Timestamp{Seconds: 1234567890},
			DiffCommitSha: "abc123",
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_Deleted{
						Deleted: true,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	req := &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc124",
		FilePaths: []string{"file2.txt"},
	}

	mockStream := &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)

	assert.NoError(t, err)
	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 1)
	assert.Equal(t, "abc124", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file2.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, int32(codes.NotFound), mockStream.sentResponses[0].FileInfos[0].Status.Code)
}

func TestUpdateWithWrongDiffCommit(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	blobName1, err := blob_names.NewBlobNameProto("abc123", false)
	assert.NoError(t, err)
	blobName2, err := blob_names.NewBlobNameProto("abc124", false)
	assert.NoError(t, err)

	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "abc123",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName1,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName2,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	blobName3, err := blob_names.NewBlobNameProto("abc125", false)
	assert.NoError(t, err)
	blobName4, err := blob_names.NewBlobNameProto("abc126", false)
	assert.NoError(t, err)

	updateStream = NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:     "abc124",
			DiffCommitSha: "ccc",
			CommitTime:    &timestamppb.Timestamp{Seconds: 1234567890},
			DiffInfos: []*githubstateproto.DiffInfo{
				{
					FilePath: "file1.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName3,
					},
				},
				{
					FilePath: "file2.txt",
					Change: &githubstateproto.DiffInfo_ContentBlobName{
						ContentBlobName: blobName4,
					},
				},
			},
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.Error(t, err)
	assert.Equal(t, codes.Aborted, status.Code(err))

	req := &githubstateproto.GetCurrentRefFilesRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
		CommitSha: "abc123",
		FilePaths: []string{"file1.txt", "file2.txt"},
	}

	mockStream := &mockGithubStateGetCurrentRefFilesServer{ctx: ctx}
	err = server.GetCurrentRefFiles(req, mockStream)

	assert.NoError(t, err)
	assert.NotNil(t, mockStream.sentResponses)
	assert.Len(t, mockStream.sentResponses, 2)
	assert.Equal(t, "abc123", mockStream.sentResponses[0].CommitSha)
	assert.Equal(t, "file1.txt", mockStream.sentResponses[0].FileInfos[0].FilePath)
	assert.Equal(t, blobName1, mockStream.sentResponses[0].FileInfos[0].BlobName)
	assert.Equal(t, "file2.txt", mockStream.sentResponses[1].FileInfos[0].FilePath)
	assert.Equal(t, blobName2, mockStream.sentResponses[1].FileInfos[0].BlobName)
}

func TestUpdateCurrentRefCheckpointNoNewBlobs(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_R", "CONTENT_RW"},
	})

	// Set up an indexed ref checkpoint
	fakeState.AddIndexedRef(&githubstatepersistproto.IndexedRefData{
		Ref: &githubstatepersistproto.RefInfo{
			Ref: &githubstateproto.GithubRef{
				Ref: "main",
				Repo: &githubstateproto.GithubRepo{
					RepoOwner: "owner",
					RepoName:  "repo",
				},
			},
			CommitSha: "abc123",
		},
		TenantId: "test-tenant",
	})

	req := &githubstateproto.GetIndexedRefCheckpointsRequest{}
	resp, err := server.GetIndexedRefCheckpoints(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.RefCheckpoints, 1)
	assert.Equal(t, "abc123", resp.RefCheckpoints[0].CommitSha)

	// Now update with a new commit but no new diffs or checkpoint ID
	updateStream := NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx, []*githubstateproto.UpdateCurrentRefCheckpointRequest{
		{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
			},
			CommitSha:  "def456",
			CommitTime: &timestamppb.Timestamp{Seconds: 1234567891},
			// No new diffs or checkpoint ID
		},
	})
	err = server.UpdateCurrentRefCheckpoint(updateStream)
	assert.NoError(t, err)

	// The indexed ref should still have old checkpoint
	resp, err = server.GetIndexedRefCheckpoints(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Len(t, resp.RefCheckpoints, 1)
	assert.Equal(t, "abc123", resp.RefCheckpoints[0].CommitSha)

	currentStateReq := &githubstateproto.GetCurrentRefStateRequest{
		Ref: &githubstateproto.GithubRef{
			Ref:  "main",
			Repo: &githubstateproto.GithubRepo{RepoOwner: "owner", RepoName: "repo"},
		},
	}

	// Check current ref state has new commit
	currentStateResp, err := server.GetCurrentRefState(ctx, currentStateReq)

	assert.NoError(t, err)
	assert.NotNil(t, currentStateResp)
	assert.Equal(t, "def456", currentStateResp.LastUploadedRefCheckpoint.CommitSha)
	assert.Equal(t, int64(1234567891), currentStateResp.LastUploadedRefCheckpoint.CommitTime.Seconds)
}

func TestDeleteGithubStateForRepos(t *testing.T) {
	fakeState := NewFakeGithubState()
	server := NewGithubStateServer(fakeState,
		featureflags.NewLocalFeatureFlagHandler())

	ctx := context.Background()
	ctx = auth.WithAugmentClaims(ctx, &auth.AugmentClaims{
		TenantID: "test-tenant",
		Scope:    []string{"CONTENT_RW"},
	})

	// Set up test data
	testRepo1 := &githubstateproto.GithubRepo{RepoOwner: "owner1", RepoName: "repo1"}
	testRepo2 := &githubstateproto.GithubRepo{RepoOwner: "owner2", RepoName: "repo2"}

	err := fakeState.AddIndexedRef(&githubstatepersistproto.IndexedRefData{
		Ref: &githubstatepersistproto.RefInfo{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: testRepo1,
			},
		},
		TenantId: "test-tenant",
	})
	assert.NoError(t, err)
	err = fakeState.AddIndexedRef(&githubstatepersistproto.IndexedRefData{
		Ref: &githubstatepersistproto.RefInfo{
			Ref: &githubstateproto.GithubRef{
				Ref:  "another-ref",
				Repo: testRepo1,
			},
		},
		TenantId: "test-tenant",
	})
	assert.NoError(t, err)
	err = fakeState.AddIndexedRef(&githubstatepersistproto.IndexedRefData{
		Ref: &githubstatepersistproto.RefInfo{
			Ref: &githubstateproto.GithubRef{
				Ref:  "main",
				Repo: testRepo2,
			},
		},
		TenantId: "test-tenant",
	})
	assert.NoError(t, err)

	// Create a test request
	req := &githubstateproto.DeleteGithubStateForReposRequest{
		Repos: []*githubstateproto.GithubRepo{testRepo1},
	}

	// Call the function
	resp, err := server.DeleteGithubStateForRepos(ctx, req)

	// Assert the results
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	// Verify that both refs of the first repo was deleted from the fake state
	assert.Len(t, fakeState.indexedRefs, 1)
	assert.Contains(t, fakeState.indexedRefs, refKey{
		tenantID:  "test-tenant",
		repoOwner: testRepo2.RepoOwner,
		repoName:  testRepo2.RepoName,
		ref:       "main",
	})
}

// Mock stream for GetCurrentRefFiles
type mockGithubStateGetCurrentRefFilesServer struct {
	githubstateproto.GithubState_GetCurrentRefFilesServer
	ctx           context.Context
	sentResponses []*githubstateproto.GetCurrentRefFilesResponse
}

func (m *mockGithubStateGetCurrentRefFilesServer) Send(resp *githubstateproto.GetCurrentRefFilesResponse) error {
	m.sentResponses = append(m.sentResponses, resp)
	return nil
}

func (m *mockGithubStateGetCurrentRefFilesServer) Context() context.Context {
	return m.ctx
}

type mockGithubStateUpdateCurrentRefCheckpointServer struct {
	githubstateproto.GithubState_UpdateCurrentRefCheckpointServer
	ctx           context.Context
	msg           []*githubstateproto.UpdateCurrentRefCheckpointRequest
	sentResponses []*githubstateproto.UpdateCurrentRefCheckpointResponse
}

func NewMockGithubStateUpdateCurrentRefCheckpointServer(ctx context.Context, msg []*githubstateproto.UpdateCurrentRefCheckpointRequest) *mockGithubStateUpdateCurrentRefCheckpointServer {
	return &mockGithubStateUpdateCurrentRefCheckpointServer{
		ctx: ctx,
		msg: msg,
	}
}

func (m *mockGithubStateUpdateCurrentRefCheckpointServer) Context() context.Context {
	return m.ctx
}

func (m *mockGithubStateUpdateCurrentRefCheckpointServer) Recv() (*githubstateproto.UpdateCurrentRefCheckpointRequest, error) {
	if len(m.msg) == 0 {
		return nil, io.EOF
	}
	msg := m.msg[0]
	m.msg = m.msg[1:]
	return msg, nil
}

func (m *mockGithubStateUpdateCurrentRefCheckpointServer) SendMsg(msg any) error {
	resp, ok := msg.(*githubstateproto.UpdateCurrentRefCheckpointResponse)
	if !ok {
		return status.Error(codes.InvalidArgument, "Invalid message type")
	}
	m.sentResponses = append(m.sentResponses, resp)
	return nil
}

// Add more tests for UpdateCurrentRefCheckpoint and other methods as needed

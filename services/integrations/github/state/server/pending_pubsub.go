package main

import (
	"context"
	"fmt"

	"cloud.google.com/go/pubsub"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/server/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"
)

// publish a new pending commit for index checking.
type PendingTaskPublisher interface {
	PublishPendingTask(ctx context.Context, pendingTask *githubstatepersistproto.PendingRefData) error
}

type pendingTaskPublisher struct {
	pubsubClient *pubsub.Client
	topic        *pubsub.Topic
}

// NewPendingTaskPublisher creates a new PendingTaskPublisher.
func NewPendingTaskPublisher(pubsubClient *pubsub.Client, topicName string) PendingTaskPublisher {
	topic := pubsubClient.Topic(topicName)
	return &pendingTaskPublisher{
		pubsubClient: pubsubClient,
		topic:        topic,
	}
}

func (p *pendingTaskPublisher) PublishPendingTask(ctx context.Context, pendingTask *githubstatepersistproto.PendingRefData) error {
	task := &githubstatepersistproto.PendingEvent{
		PendingRefData: pendingTask,
	}
	data, err := proto.Marshal(task)
	if err != nil {
		return err
	}
	msg := &pubsub.Message{
		Data: data,
	}
	p.topic.EnableMessageOrdering = true
	orderingKey := fmt.Sprintf("%s/%s/%s", pendingTask.Ref.Ref.Repo.RepoOwner, pendingTask.Ref.Ref.Repo.RepoName, pendingTask.Ref.Ref.Ref)
	msg.OrderingKey = orderingKey
	result := p.topic.Publish(ctx, msg)
	_, err = result.Get(ctx)
	return err
}

// subscribe to pending commits for index checking.
type PendingTaskSubscriber interface {
	Subscribe(ctx context.Context, handler func(ctx context.Context, pendingTask *githubstatepersistproto.PendingRefData) error) error
	Exists(ctx context.Context) (bool, error)
}

type pendingTaskSubscriber struct {
	pubsubClient *pubsub.Client
	subscription *pubsub.Subscription
}

func NewPendingTaskSubscriber(pubsubClient *pubsub.Client, subscriptionName string) PendingTaskSubscriber {
	subscription := pubsubClient.Subscription(subscriptionName)
	return &pendingTaskSubscriber{
		pubsubClient: pubsubClient,
		subscription: subscription,
	}
}

func (s *pendingTaskSubscriber) Exists(ctx context.Context) (bool, error) {
	return s.subscription.Exists(ctx)
}

func (s *pendingTaskSubscriber) Subscribe(ctx context.Context, handler func(ctx context.Context, pendingTask *githubstatepersistproto.PendingRefData) error) error {
	log.Info().Msgf("Starting pubsub receive")
	return s.subscription.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		task := &githubstatepersistproto.PendingEvent{}
		err := proto.Unmarshal(msg.Data, task)
		if err != nil {
			log.Error().Err(err).Msg("Failed to unmarshal event")
			msg.Nack()
			return
		}
		log.Info().Msgf("Received event: %s", githublib.RedactRef(task.PendingRefData.Ref.Ref))
		err = handler(ctx, task.PendingRefData)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to handle event")
			msg.Nack()
			return
		}
		msg.Ack()
	})
}

package main

import (
	"context"
	"fmt"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc/credentials"

	"github.com/augmentcode/augment/base/blob_names"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	completionproto "github.com/augmentcode/augment/services/completion_host/proto"
	modelinstanceproto "github.com/augmentcode/augment/services/deploy/model_instance/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
)

// FindMissing is used to call the find_missing endpoint of the content manager to check if a commit is fully indexed.
type FindMissing interface {
	// FindMissing calls the find_missing endpoint of the content manager to check if a commit is fully indexed.
	//
	// Args:
	//	ctx: the context
	//	model: the model to use for the find_missing call
	//	blobNames: the blob names to check
	//	requestContext: the request context
	//
	// Returns:
	//	count: the number of missing blobs
	FindMissing(ctx context.Context, model *modelinstanceproto.ModelInstanceConfig, blobNames []blob_names.BlobName, requestContext *requestcontext.RequestContext) (int, error)
}

type findMissing struct {
	creds     credentials.TransportCredentials
	chunkSize int
}

func NewFindMissing(creds credentials.TransportCredentials, chunkSize int) FindMissing {
	return &findMissing{
		creds:     creds,
		chunkSize: chunkSize,
	}
}

// getConnection creates a new connection to the given endpoint.
func (f *findMissing) getConnection(endpoint string) (*grpc.ClientConn, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(f.creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to settings service: %v", err)
	}
	return conn, nil
}

func chunkBlobNames(blobNames []string, chunkSize int) [][]string {
	chunks := make([][]string, 0)
	for i := 0; i < len(blobNames); i += chunkSize {
		chunks = append(chunks, blobNames[i:min(i+chunkSize, len(blobNames))])
	}
	return chunks
}

type blobNameOrString interface {
	blob_names.BlobName | string
}

func formatBlobNames[T blobNameOrString](blobNames []T) string {
	if len(blobNames) == 0 {
		return "[]"
	}
	if len(blobNames) > 2 {
		return fmt.Sprintf("[%s, ... (%d items), %s]", blobNames[0], len(blobNames)-2, blobNames[len(blobNames)-1])
	} else if len(blobNames) == 2 {
		return fmt.Sprintf("[%s, %s]", blobNames[0], blobNames[1])
	} else {
		return fmt.Sprintf("[%s]", blobNames[0])
	}
}

// findMissingCompletion calls the find_missing endpoint of the content manager to check if a commit is fully indexed.
func (f *findMissing) findMissingCompletion(ctx context.Context, model *modelinstanceproto.ModelInstanceConfig,
	completionConfig *modelinstanceproto.InferenceModelConfig, blobNames []blob_names.BlobName, requestContext *requestcontext.RequestContext,
) (int, error) {
	conn, err := f.getConnection(*completionConfig.CompletionEndpoint)
	if err != nil {
		return 0, err
	}
	defer conn.Close()

	client := completionproto.NewCompletionClient(conn)

	ctx = requestcontext.NewOutgoingContext(ctx, requestContext)
	blobNamesStr := make([]string, len(blobNames))
	for i, blobName := range blobNames {
		blobNamesStr[i] = string(blobName)
	}
	count := 0
	for _, blobNamesChunk := range chunkBlobNames(blobNamesStr, f.chunkSize) {
		log.Info().Msgf("Checking for missing blobs: %s", formatBlobNames(blobNamesChunk))
		resp, err := client.FindMissing(ctx, &completionproto.FindMissingRequest{
			ModelName: *model.Name,
			BlobNames: blobNamesChunk,
		})
		if err != nil {
			return 0, err
		}
		count += len(resp.NonindexedBlobNames)
		if count > 0 {
			log.Info().Msgf("Found missing blobs: stopping: %s", formatBlobNames(resp.NonindexedBlobNames))
			break
		}
	}
	return count, nil
}

// findMissingChat calls the find_missing endpoint of the content manager to check if a commit is fully indexed.
func (f *findMissing) findMissingChat(ctx context.Context, model *modelinstanceproto.ModelInstanceConfig,
	chatConfig *modelinstanceproto.ChatModelConfig, blobNames []blob_names.BlobName, requestContext *requestcontext.RequestContext,
) (int, error) {
	conn, err := f.getConnection(*chatConfig.ChatEndpoint)
	if err != nil {
		return 0, err
	}
	defer conn.Close()

	client := chatproto.NewChatClient(conn)

	ctx = requestcontext.NewOutgoingContext(ctx, requestContext)
	blobNamesStr := make([]string, len(blobNames))
	for i, blobName := range blobNames {
		blobNamesStr[i] = string(blobName)
	}
	log.Info().Msgf("Checking for missing blobs %s", formatBlobNames(blobNamesStr))
	count := 0
	for _, blobNamesChunk := range chunkBlobNames(blobNamesStr, f.chunkSize) {
		log.Info().Msgf("Checking for missing blobs: %s", formatBlobNames(blobNamesChunk))
		resp, err := client.FindMissing(ctx, &chatproto.FindMissingRequest{
			ModelName: *model.Name,
			BlobNames: blobNamesChunk,
		})
		if err != nil {
			return 0, err
		}
		count += len(resp.NonindexedBlobNames)
		if count > 0 {
			log.Info().Msgf("Found missing blobs: stopping: %s", formatBlobNames(resp.NonindexedBlobNames))
			break
		}
	}
	return count, nil
}

func (f *findMissing) FindMissing(ctx context.Context, model *modelinstanceproto.ModelInstanceConfig, blobNames []blob_names.BlobName, requestContext *requestcontext.RequestContext) (int, error) {
	log.Info().Msgf("Running find missing for model %s on %d blobs", *model.Name, len(blobNames))
	if len(blobNames) == 0 {
		return 0, nil
	}
	completionConfig := model.GetInference()
	if completionConfig != nil {
		return f.findMissingCompletion(ctx, model, completionConfig, blobNames, requestContext)
	}
	chatConfig := model.GetChat()
	if chatConfig != nil {
		return f.findMissingChat(ctx, model, chatConfig, blobNames, requestContext)
	}
	// next edit and edit are not supported
	return 0, nil
}

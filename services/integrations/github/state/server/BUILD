load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_proto_library", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

proto_library(
    name = "github_persistence_proto",
    srcs = ["github_persistence.proto"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//services/integrations/github/state:github_state_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_proto_library(
    name = "github_persistence_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/github/state/server/proto",
    proto = ":github_persistence_proto",
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//services/integrations/github/state:github_state_go_proto",
    ],
)

go_library(
    name = "server_lib",
    srcs = [
        "find_missing.go",
        "index_checker.go",
        "main.go",
        "pending_pubsub.go",
        "server.go",
        "state.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/github/state/server/go",
    visibility = ["//visibility:private"],
    deps = [
        ":github_persistence_go_proto",
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/api_proxy/client:client_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/client:client_go",
        "//services/chat_host:chat_host_go_proto",
        "//services/completion_host:completion_host_go_proto",
        "//services/content_manager/client:client_go",
        "//services/deploy/model_instance:model_instance_go_proto",
        "//services/integrations/github/state:github_state_go_proto",
        "//services/integrations/github/state/lib:github_state_lib",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "server_test",
    srcs = ["server_test.go"],
    embed = [":server_lib"],
    deps = [
        ":github_persistence_go_proto",
        "//base/blob_names:blob_names_go",
        "//services/integrations/github/state:github_state_go_proto",
        "//services/lib/grpc/auth:grpc_auth_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

# create a container image
go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

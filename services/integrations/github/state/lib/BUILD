load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "github_state_lib",
    srcs = [
        "lib.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/github/state/lib",
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/go/secretstring:secretstring_go",
        "//services/integrations/github/state:github_state_go_proto",
    ],
)

go_test(
    name = "github_state_lib_test",
    srcs = ["lib_test.go"],
    embed = [":github_state_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "@com_github_stretchr_testify//assert",
    ],
)

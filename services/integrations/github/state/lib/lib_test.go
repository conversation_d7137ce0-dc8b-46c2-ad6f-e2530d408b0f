package lib

import (
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	proto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	"github.com/stretchr/testify/assert"
)

func TestRedactString(t *testing.T) {
	redacted := RedactString("augmentcode")
	assert.NotEqual(t, "augmentcode", redacted)
}

func TestRedactSecretstring(t *testing.T) {
	s := secretstring.New("augmentcode")
	redacted := RedactSecretstring(s)
	assert.NotEqual(t, "augmentcode", redacted)
}

func TestRefToString(t *testing.T) {
	ref := &proto.GithubRef{
		Ref: "main",
		Repo: &proto.GithubRepo{
			RepoOwner: "augmentcode",
			RepoName:  "augment",
		},
	}
	redacted := RedactRef(ref)
	assert.NotEqual(t, "augmentcode", redacted)
	assert.NotEqual(t, "augment", redacted)
	assert.NotEqual(t, "main", redacted)
}

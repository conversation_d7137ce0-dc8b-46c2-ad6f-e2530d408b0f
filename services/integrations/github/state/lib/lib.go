package lib

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/secretstring"
	proto "github.com/augmentcode/augment/services/integrations/github/state/proto"
)

func hash(s string) string {
	hash := sha256.New()
	hash.Write([]byte(s))
	h := hex.EncodeToString(hash.Sum(nil))
	h = strings.ToUpper(h)
	return h[:8]
}

const (
	allowedPrefixLength = 2
	allowedSuffixLength = 2
	allowedLength       = allowedPrefixLength + allowedSuffixLength
)

// returns the prefix of the string and the hash of the suffix
//
// if the string is shorter, it will just return the hash
func RedactString(s string) string {
	if len(s) <= allowedLength {
		return fmt.Sprintf("[*%s*]", hash(s))
	}
	return fmt.Sprintf("[%s**%s**%s]", s[:allowedPrefixLength], hash(s), s[len(s)-allowedSuffixLength:])
}

// returns the prefix of the string and the hash of the suffix
//
// if the string is shorter, it will just return the hash
func RedactSecretstring(s secretstring.SecretString) string {
	exposed := s.Expose()
	if len(exposed) <= allowedLength {
		return fmt.Sprintf("[*%s*]", hash(exposed))
	}
	return fmt.Sprintf("[%s**%s**%s]", exposed[:allowedPrefixLength], hash(exposed), exposed[len(exposed)-allowedSuffixLength:])
}

func RedactRef(ref *proto.GithubRef) string {
	if ref == nil {
		return "nil"
	}
	return RedactString(ref.Repo.RepoOwner) + "/" + RedactString(ref.Repo.RepoName) + "/" + RedactString(ref.Ref)
}

func RedactRefCheckpoint(refCheckpoint *proto.RefCheckpoint) string {
	if refCheckpoint == nil {
		return "nil"
	}
	return fmt.Sprintf("(%s %s %s %s)",
		RedactRef(refCheckpoint.Ref),
		RedactString(refCheckpoint.CommitSha),
		refCheckpoint.CommitTime,
		FormatBlobs(refCheckpoint.Blobs))
}

func FormatBlobs(blobs *blobsproto.Blobs) string {
	if blobs == nil {
		return "Blobs{nil}"
	}
	baselineCheckpointId := "(nil)"
	if blobs.BaselineCheckpointId != nil {
		baselineCheckpointId = *blobs.BaselineCheckpointId
	}
	return fmt.Sprintf("Blobs{baseline_checkpoint_id=%s, added=%d blobs, deleted=%d blobs}",
		baselineCheckpointId,
		len(blobs.Added),
		len(blobs.Deleted),
	)
}

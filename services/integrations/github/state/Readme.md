# Github State Service

The github state service is a service that allows to store and retrieve the state of a Github repository
that we upload using the Github App. The file content itself is not stored in the state service, but in
content manager. The state service is tracking:

- information required for efficient diffing of commits
- information about the current ref (branch or tag)
- information about the last indexed commits

## Components

- server: the server opens up GRPC methods to access the information. see github_state.proto
- state: the state component is reading the information from Bigtable
- pending_pubsub: the component uses pubsub to maintain the information about which pending commits are
still need to be checked for indexing. If a pending commit is fully indexed by all known models, it is
moved to the indexed state.
- index_checker: the component is checking the pending commits for indexing. If a commit is fully indexed,
it is moved to the indexed state.
- find_missing: the component is calling the find_missing endpoint of the content manager to check
if a commit is fully indexed.
- main: the main component is starting the server and the index_checker.

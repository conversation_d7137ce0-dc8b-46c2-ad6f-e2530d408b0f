"""Python stub for Github State GRPC."""

import logging
from typing import Optional

import grpc

import base.python.grpc.client_options as client_options
from services.integrations.github.state import github_state_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> github_state_pb2_grpc.GithubStateStub:
    """Setup the client stub for github state."""
    if not credentials:
        logging.info(
            "No credentials provided, creating insecure channel to %s", endpoint
        )
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = github_state_pb2_grpc.GithubStateStub(channel)
    return stub

package client

// Package client provides a client for the github state service.

import (
	"context"
	"errors"
	"fmt"
	"io"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/proto"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"

	blobnamespb "github.com/augmentcode/augment/base/blob_names/proto"
	pb "github.com/augmentcode/augment/services/integrations/github/state/proto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

var MaxRequestSize = 1024 * 1024 * 2

type GetCurrentRefFilesResult struct {
	// the response
	// nil if there was an error
	Resp *pb.GetCurrentRefFilesResponse

	// the error
	// nil if there was no error
	Err error
}

// GithubStateClient interface for the github state service. See github_state.proto for details.
type GithubStateClient interface {
	GetIndexedRefCheckpoints(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*pb.RefCheckpoint, error)

	GetCurrentRefStates(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*pb.RefCheckpoint, error)

	GetCurrentRefState(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *pb.GithubRef) (*pb.RefCheckpoint, error)

	GetCurrentRefFiles(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *pb.GithubRef, commitSha string, filePaths []string) (<-chan GetCurrentRefFilesResult, error)

	UpdateCurrentRefCheckpoint(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *pb.GithubRef, commitSha string, parentCommitShas []string, diffCommitSha string, commitTime *timestamppb.Timestamp, checkpointId *blobnamespb.Blobs, diffInfos []*pb.DiffInfo, forceUpload bool) (*pb.UpdateCurrentRefCheckpointResponse, error)

	DeleteGithubStateForRepos(ctx context.Context, requestContext *requestcontext.RequestContext, repos []*pb.GithubRepo) error

	// This should be called to cleanup resources for this client
	Close()
}

// GithubStateClientImpl implementation of the GithubStateClient interface.
type GithubStateClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	client pb.GithubStateClient
}

// NewGithubStateClient creates a new GithubStateClient.
//
// endpoint: The endpoint of the github state service.
// credentials: The credentials to use for the channel (optional)
//
// Returns: The client stub for the route guide or an error if the client could not be created.
func NewGithubStateClient(endpoint string, credentials credentials.TransportCredentials) (GithubStateClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := pb.NewGithubStateClient(conn)
	return &GithubStateClientImpl{conn: conn, client: client}, nil
}

func (c *GithubStateClientImpl) GetIndexedRefCheckpoints(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*pb.RefCheckpoint, error) {
	req := &pb.GetIndexedRefCheckpointsRequest{}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetIndexedRefCheckpoints(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.RefCheckpoints, nil
}

func (c *GithubStateClientImpl) GetCurrentRefStates(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*pb.RefCheckpoint, error) {
	req := &pb.GetCurrentRefStatesRequest{}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetCurrentRefStates(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.RefCheckpoints, nil
}

func (c *GithubStateClientImpl) GetCurrentRefState(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *pb.GithubRef) (*pb.RefCheckpoint, error) {
	req := &pb.GetCurrentRefStateRequest{
		Ref: githubRef,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetCurrentRefState(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.LastUploadedRefCheckpoint, nil
}

func (c *GithubStateClientImpl) GetCurrentRefFiles(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *pb.GithubRef, commitSha string, filePaths []string) (<-chan GetCurrentRefFilesResult, error) {
	req := &pb.GetCurrentRefFilesRequest{
		Ref:       githubRef,
		CommitSha: commitSha,
		FilePaths: filePaths,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	stream, err := c.client.GetCurrentRefFiles(ctx, req)
	if err != nil {
		return nil, err
	}

	ch := make(chan GetCurrentRefFilesResult)
	go func() {
		defer close(ch)
		for {
			resp, err := stream.Recv()
			if err != nil {
				if !errors.Is(err, io.EOF) {
					ch <- GetCurrentRefFilesResult{Err: err}
				}
				return
			}
			ch <- GetCurrentRefFilesResult{Resp: resp}
		}
	}()

	return ch, nil
}

// send an update request to github client, breaking diffInfos into multiple requests if too large
func (c *GithubStateClientImpl) UpdateCurrentRefCheckpoint(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *pb.GithubRef, commitSha string, parentCommitShas []string, diffCommitSha string, commitTime *timestamppb.Timestamp, checkpointId *blobnamespb.Blobs, diffInfos []*pb.DiffInfo, forceUpload bool) (*pb.UpdateCurrentRefCheckpointResponse, error) {
	ch := make(chan *pb.UpdateCurrentRefCheckpointRequest)

	firstRequest := &pb.UpdateCurrentRefCheckpointRequest{
		Ref:              githubRef,
		CommitSha:        commitSha,
		ParentCommitShas: parentCommitShas,
		DiffCommitSha:    diffCommitSha,
		CommitTime:       commitTime,
		Blobs:            checkpointId,
		ForceUpload:      forceUpload,
	}

	firstRequestSent := false

	// Create requests in chunks of MaxRequestSize
	go func() {
		defer close(ch)

		var currentChunk []*pb.DiffInfo
		currentSize := proto.Size(firstRequest)

		log.Info().Int("totalDiffInfos", len(diffInfos)).Msg("Starting to send diff infos")

		for _, diffInfo := range diffInfos {
			if currentSize+proto.Size(diffInfo) > MaxRequestSize {
				if !firstRequestSent {
					firstRequest.DiffInfos = append(firstRequest.DiffInfos, currentChunk...)
					ch <- firstRequest
					firstRequestSent = true
				} else {
					ch <- &pb.UpdateCurrentRefCheckpointRequest{
						DiffInfos: currentChunk,
					}
				}
				currentChunk = []*pb.DiffInfo{}
				currentSize = 0
			}
			currentChunk = append(currentChunk, diffInfo)
			currentSize += proto.Size(diffInfo)
		}

		// Add any remaining diffInfos
		if len(currentChunk) > 0 {
			if !firstRequestSent {
				firstRequest.DiffInfos = append(firstRequest.DiffInfos, currentChunk...)
				ch <- firstRequest
				firstRequestSent = true
			} else {
				ch <- &pb.UpdateCurrentRefCheckpointRequest{
					DiffInfos: currentChunk,
				}
			}
		}

		// For the case where there are no diffInfos
		if !firstRequestSent {
			ch <- firstRequest
		}
	}()

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.UpdateCurrentRefCheckpoint(ctx)
	if err != nil {
		return nil, err
	}

	// Send all requests in the stream
	for req := range ch {
		if err := stream.Send(req); err != nil {
			log.Error().Err(err).Msg("Error sending request in UpdateCurrentRefCheckpoint")
			return nil, fmt.Errorf("failed to send update request")
		}
	}

	// Close the send direction of the stream
	if err := stream.CloseSend(); err != nil {
		log.Error().Err(err).Msg("Error closing send stream in UpdateCurrentRefCheckpoint")
		return nil, fmt.Errorf("failed to close send stream")
	}

	// Receive the single response
	resp, err := stream.CloseAndRecv()
	if err != nil {
		log.Error().Err(err).Msg("Error receiving response in UpdateCurrentRefCheckpoint")
		return nil, fmt.Errorf("failed to receive update response")
	}

	return resp, nil
}

func (c *GithubStateClientImpl) DeleteGithubStateForRepos(ctx context.Context, requestContext *requestcontext.RequestContext, repos []*pb.GithubRepo) error {
	req := &pb.DeleteGithubStateForReposRequest{
		Repos: repos,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	_, err := c.client.DeleteGithubStateForRepos(ctx, req)
	if err != nil {
		return fmt.Errorf("error in DeleteGithubStateForRepos: %w", err)
	}
	return nil
}

func (c *GithubStateClientImpl) Close() {
	c.conn.Close()
}

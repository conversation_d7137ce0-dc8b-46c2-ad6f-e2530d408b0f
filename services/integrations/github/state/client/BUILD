load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("//tools/bzl:python.bzl", "py_library")

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/integrations/github/state/client",
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//services/integrations/github/state:github_state_go_proto",
        "//services/lib/request_context:request_context_go",
        "@com_github_rs_zerolog//log",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/integrations/github/state:github_state_py_proto",
    ],
)

go_test(
    name = "client_test",
    srcs = ["client_test.go"],
    embed = [":client_go"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//services/integrations/github/state:github_state_go_proto",
        "//services/lib/request_context:request_context_go",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

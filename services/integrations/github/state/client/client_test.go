package client

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"

	blobnames "github.com/augmentcode/augment/base/blob_names"
	blobnamespb "github.com/augmentcode/augment/base/blob_names/proto"
	pb "github.com/augmentcode/augment/services/integrations/github/state/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

func TestUpdateCurrentRefCheckpoint_BreaksDiffInfosIntoMultipleRequests(t *testing.T) {
	// Set a small maxRequestSize for testing
	MaxRequestSize = 100

	mockStream := &mockUpdateCurrentRefCheckpointStream{}
	mockStream.On("Send", mock.Anything).Return(nil)
	mockStream.On("CloseAndRecv").Return(&pb.UpdateCurrentRefCheckpointResponse{}, nil)
	mockStream.On("CloseSend").Return(nil)

	mockClient := &mockGithubStateClient{}
	mockClient.On("UpdateCurrentRefCheckpoint", mock.Anything, mock.Anything).Return(mockStream, nil)

	client := &GithubStateClientImpl{
		client: mockClient,
	}

	// Create a large diffInfos slice
	largeDiffInfos := make([]*pb.DiffInfo, 10)
	for i := 0; i < 10; i++ {
		blobName, _ := blobnames.NewBlobNameProto(blobnames.BlobName(fmt.Sprintf("blob_%d", i)), false)
		largeDiffInfos[i] = &pb.DiffInfo{
			FilePath: fmt.Sprintf("file_%d.txt", i),
			Change: &pb.DiffInfo_ContentBlobName{
				ContentBlobName: blobName,
			},
		}
	}

	_, err := client.UpdateCurrentRefCheckpoint(
		context.Background(),
		&requestcontext.RequestContext{},
		&pb.GithubRef{},
		"commit123",
		[]string{"parent1", "parent2"},
		"diffcommit123",
		&timestamppb.Timestamp{},
		&blobnamespb.Blobs{},
		largeDiffInfos,
		false,
	)

	assert.NoError(t, err)
	assert.True(t, len(mockStream.receivedRequests) > 1, "Expected multiple requests, got %d", len(mockStream.receivedRequests))

	// The first request should have all fields filled in
	assert.NotNil(t, mockStream.receivedRequests[0].Ref)
	assert.Equal(t, mockStream.receivedRequests[0].CommitSha, "commit123")
	assert.NotEmpty(t, mockStream.receivedRequests[0].DiffInfos)

	// Subsequent requests only have DiffInfos filled in
	for i := 1; i < len(mockStream.receivedRequests); i++ {
		assert.Nil(t, mockStream.receivedRequests[i].Ref)
		assert.Empty(t, mockStream.receivedRequests[i].CommitSha)
		assert.NotEmpty(t, mockStream.receivedRequests[i].DiffInfos)
	}
}

type mockGithubStateClient struct {
	mock.Mock
	pb.GithubStateClient
}
type mockUpdateCurrentRefCheckpointStream struct {
	grpc.ClientStream
	mock.Mock
	receivedRequests []*pb.UpdateCurrentRefCheckpointRequest
}

func (m *mockUpdateCurrentRefCheckpointStream) Send(req *pb.UpdateCurrentRefCheckpointRequest) error {
	m.receivedRequests = append(m.receivedRequests, req)
	return m.Called(req).Error(0)
}

func (m *mockUpdateCurrentRefCheckpointStream) CloseAndRecv() (*pb.UpdateCurrentRefCheckpointResponse, error) {
	args := m.Called()
	return args.Get(0).(*pb.UpdateCurrentRefCheckpointResponse), args.Error(1)
}

func (m *mockUpdateCurrentRefCheckpointStream) CloseSend() error {
	return m.Called().Error(0)
}

func (m *mockGithubStateClient) UpdateCurrentRefCheckpoint(ctx context.Context, opts ...grpc.CallOption) (pb.GithubState_UpdateCurrentRefCheckpointClient, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(pb.GithubState_UpdateCurrentRefCheckpointClient), args.Error(1)
}

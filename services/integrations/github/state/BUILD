load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "github_state_proto",
    srcs = ["github_state.proto"],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "github_state_py_proto",
    protos = [":github_state_proto"],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

go_grpc_library(
    name = "github_state_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/github/state/proto",
    proto = ":github_state_proto",
    visibility = [
        "//services/integrations:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
    ],
)

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'github-webhook-listener',
      kubecfg: {
        target: '//services/integrations/github/webhook_listener:kubecfg',
        task: [
          t
          for t in cloudInfo.centralNamespaces
          if cloudInfo.isLeadProdCluster(t.cloud)
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'github-webhook-listener-pubsub',
      kubecfg: {
        target: '//services/integrations/github/webhook_listener:kubecfg_pubsub',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'github-webhook-listener-cross-cloud-roles',
      kubecfg: {
        target: '//services/integrations/github/webhook_listener:kubecfg_cross_cloud_roles',
        task: [t for t in cloudInfo.centralNamespaces],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['aswin'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

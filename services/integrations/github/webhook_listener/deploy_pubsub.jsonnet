local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'github-webhook';
  // Only deploy pubsub in enterprise tier namespaces since the github app is only used for slackbot - an enterprise-only feature
  if namespace_config.flags.userTier != 'ENTERPRISE_TIER' then [] else
    // Copy from deploy.jsonnet. Don't actually create the service account here,
    // we just need the name so we can give it access to the topic
    local serviceAccount = gcpLib.createServiceAccount(
      app=appName,
      env=env,
      cloud=cloudInfo.getLeadClusterForCluster(cloud),
      namespace=cloudInfo.getCentralNamespaceForNamespace(env, namespace, cloud),
      iam=true,
    );
    local publisher = pubsubLib.publisherTopic(cloud=cloud, env=env, namespace=namespace, appName=appName, serviceAccount=serviceAccount);

    lib.flatten([
      publisher.objects,
    ])

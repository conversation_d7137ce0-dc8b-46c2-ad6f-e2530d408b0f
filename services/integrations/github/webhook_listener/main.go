package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	pubsub "github.com/augmentcode/augment/services/lib/pubsub"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	"github.com/gorilla/mux"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gorilla/mux/otelmux"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"k8s.io/client-go/dynamic"
)

var (
	configFile = flag.String("config", "", "Path to config file")
	kubeconfig = flag.String("kubeconfig", "tools/deploy/auth_kube_config.yaml", "Path to kubeconfig file")
)

type Config struct {
	Port            int    `json:"port"`
	HttpsServerKey  string `json:"https_server_key"`
	HttpsServerCert string `json:"https_server_cert"`
	PromPort        int    `json:"prom_port"`

	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	TenantWatcherEndpoint string `json:"tenant_watcher_endpoint"`

	// For the pubsub topic
	ProjectID         string `json:"project_id"`
	TopicNameTemplate string `json:"topic_name_template"`

	// When in dev, the namespace to read webhooktenantmappings from
	Namespace string `json:"namespace"`

	// The kube contexts to read webhooktenantmappings from
	KubeContexts []string `json:"kube_contexts"`

	// For testing
	EnableFakePostReceive bool `json:"enable_fake_post_receive"`
}

// Returns true if we want logging and tracing for this request and false
// otherwise
func logRequest(r *http.Request) bool {
	// Don't spam logs and traces with health checks
	return r.URL.Path != "/health"
}

func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if logRequest(r) {
			log.Info().Msgf("%s %s", r.Method, r.URL.Path)
			next.ServeHTTP(w, r)
			// TODO: also log the response?
		} else {
			next.ServeHTTP(w, r)
		}
	})
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("ok"))
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	log.Info().Msgf("Kubeconfig: %s", kubeconfig)
	if _, err := os.Stat(*kubeconfig); os.IsNotExist(err) {
		log.Fatal().Err(err).Msg("kubeconfig file does not exist")
		os.Exit(1)
	}

	centralClientTls, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating central client credentials")
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		cancel()
	}()

	if len(config.KubeContexts) == 0 {
		log.Fatal().Msg("Must specify kube contexts")
	}

	var webhookMappingInformer webhookmapping.WebhookTenantMappingInformer
	if config.Namespace == "" {
		dynamicClientsets := make([]dynamic.Interface, len(config.KubeContexts))
		for i, context := range config.KubeContexts {
			dynamicClientset, err := webhookmapping.CreateDynamicClientForContext(*kubeconfig, context)
			if err != nil {
				log.Fatal().Msgf("Failed to create dynamic client: %v", err)
			}
			dynamicClientsets[i] = dynamicClientset
		}
		webhookMappingInformer = webhookmapping.NewInformer(dynamicClientsets, time.Minute)
	} else {
		if len(config.KubeContexts) > 1 {
			log.Fatal().Msg("Can only specify a namespace with a single kube context")
		}
		dynamicClientset, err := webhookmapping.CreateDynamicClientForContext(*kubeconfig, config.KubeContexts[0])
		if err != nil {
			log.Fatal().Msgf("Failed to create dynamic client: %v", err)
		}
		webhookMappingInformer = webhookmapping.NewNamespacedInformer(dynamicClientset, config.Namespace, time.Minute)
	}
	webhookMappingLookup := webhookmapping.NewLookup(webhookMappingInformer)
	go webhookMappingInformer.Run(ctx.Done())

	// Listen to all namespaces
	tenantCache := tenantwatcherclient.NewTenantCache(tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientTls)),
		config.Namespace)
	defer tenantCache.Close()

	tenantLookup, err := newTenantLookup(webhookMappingLookup, tenantCache)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating tenant lookup")
	}

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// TODO: Setup http metrics.

	client, err := pubsub.NewPublishClient(context.Background(), config.ProjectID, config.TopicNameTemplate)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating pubsub client")
	}
	defer client.Close()

	webhookSecret, err := os.ReadFile("/github-webhook-secret/secret")
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading github webhook secret")
	}

	handler, err := newGithubListener(
		secretstring.New(string(webhookSecret)), client, tenantLookup,
		config.EnableFakePostReceive)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating github listener")
	}

	r := mux.NewRouter()
	r.HandleFunc("/health", healthCheck)
	handler.register(r)
	r.NotFoundHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Warn().Msgf("Not found: %s", r.URL.Path)
		http.Error(w, "Not Found", http.StatusNotFound)
	})
	// TODO: ignore /health endpoint for traces?
	r.Use(otelmux.Middleware("webhook-listener", otelmux.WithFilter(logRequest)))
	r.Use(loggingMiddleware)

	srv := &http.Server{
		Handler: r,
		Addr:    fmt.Sprintf(":%d", config.Port),
	}
	log.Info().Msgf("Listening on %d", config.Port)

	go func() {
		select {
		case <-ctx.Done():
			srv.Shutdown(context.Background())
		}
	}()

	if err := srv.ListenAndServeTLS(config.HttpsServerCert, config.HttpsServerKey); err != nil && err != http.ErrServerClosed {
		log.Fatal().Msgf("Failed to listen: %v", err)
	}
	log.Info().Msg("HTTP server closed")
}

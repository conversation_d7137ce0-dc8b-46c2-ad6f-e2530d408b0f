package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"

	"github.com/google/go-github/v64/github"
	"github.com/gorilla/mux"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/augmentcode/augment/base/go/secretstring"
	githubproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	pubsub "github.com/augmentcode/augment/services/lib/pubsub"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

var webhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
	Name: "github_webhook_request_total",
	Help: "Counter of handled github webhook requests",
}, []string{"status_code"})

type githubListener struct {
	// This is the secret used to validate the webhook
	webhookSecret secretstring.SecretString

	publishClient pubsub.PublishClient
	tenantLookup  TenantLookup

	enableFakePostReceive bool
}

func (gl *githubListener) register(r *mux.Router) {
	r.HandleFunc("/postreceive", gl.handle).Methods("POST")
	if gl.enableFakePostReceive {
		r.HandleFunc("/fake_postreceive", gl.fakeHandle).Methods("POST")
	}
}

func (gl *githubListener) getEventMetadata(requestID requestcontext.RequestId, installation *github.Installation) (*Tenant, *githubproto.EventMetadata, error) {
	var installationID int64
	if installation == nil {
		return nil, nil, errors.New("installation is nil")
	}
	installationID = installation.GetID()
	if installationID == 0 {
		return nil, nil, errors.New("installation ID is 0")
	}

	tenant, err := gl.tenantLookup.LookupTenant(installation.GetID())
	if err != nil {
		return nil, nil, err
	}

	return tenant, &githubproto.EventMetadata{
		InstallationId: installationID,
		TenantId:       tenant.TenantID,
		TenantName:     tenant.TenantName,
		RequestId:      requestID.String(),
	}, nil
}

/**
 * This function reads the payload from a github webhook.
 *
 * It expects the following headers:
 * - X-GitHub-Event: the type of event
 * - X-Hub-Signature: the HMAC signature of the body
 *
 * It also sets the return http header appropriately if there is an error.
 */
func (gl *githubListener) getGithubPayload(r *http.Request, requestID requestcontext.RequestId, validatePayload bool) (tenant *Tenant, event *githubproto.GithubEvent, statusCode int, err error) {
	// Check the event type
	incomingEvent := r.Header.Get("X-GitHub-Event")
	if incomingEvent == "" {
		return nil, nil, http.StatusBadRequest, errors.New("missing X-GitHub-Event header")
	}
	log.Info().Msgf("Received event %s with request ID %s", incomingEvent, requestID)

	body, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, nil, http.StatusBadRequest, fmt.Errorf("error reading request body: %w", err)
	}

	// Validate the signature
	// https://docs.github.com/en/webhooks/using-webhooks/validating-webhook-deliveries
	if validatePayload {
		signature := r.Header.Get("X-Hub-Signature-256")
		if len(signature) == 0 {
			return nil, nil, http.StatusBadRequest, errors.New("missing X-Hub-Signature-256 header")
		}
		mac := hmac.New(sha256.New, []byte(gl.webhookSecret.Expose()))
		_, err = mac.Write(body)
		if err != nil {
			return nil, nil, http.StatusBadRequest, fmt.Errorf("error writing to HMAC: %w", err)
		}
		expectedMAC := "sha256=" + hex.EncodeToString(mac.Sum(nil))

		if !hmac.Equal([]byte(signature), []byte(expectedMAC)) {
			return nil, nil, http.StatusBadRequest, fmt.Errorf("HMAC verification failed: %s != %s", signature, expectedMAC)
		}
	}

	// Parse the payload

	switch incomingEvent {
	case "ping":
		var ping github.PingEvent
		err = json.Unmarshal(body, &ping)
		if err != nil {
			return nil, nil, http.StatusBadRequest, fmt.Errorf("error unmarshalling ping event: %w", err)
		}
		// Ignore ping events
		return nil, nil, http.StatusOK, nil

	case "installation":
		var installation github.InstallationEvent
		err = json.Unmarshal(body, &installation)
		if err != nil {
			return nil, nil, http.StatusBadRequest, fmt.Errorf("error unmarshalling installation event: %w", err)
		}

		// Only handle deleted events
		if installation.GetAction() != "deleted" {
			return nil, nil, http.StatusOK, nil
		}

		log.Info().Msgf("Received deletion event for installation %d", installation.GetInstallation().GetID())

		event, tenant, err = gl.getRepositoryInstallationEvent(installation.Repositories, installation.GetInstallation(), requestID, installation.GetAction(), installation.GetSender(), installation.GetRequester())
		if err != nil {
			return nil, nil, http.StatusInternalServerError, fmt.Errorf("error getting repository removed event: %w", err)
		}

	case "installation_repositories":
		var installationRepositories github.InstallationRepositoriesEvent
		err = json.Unmarshal(body, &installationRepositories)
		if err != nil {
			return nil, nil, http.StatusBadRequest, fmt.Errorf("error unmarshalling installation_repositories event: %w", err)
		}

		if installationRepositories.GetAction() == "removed" {
			log.Info().Msgf("Received repository removal event for installation %d", installationRepositories.GetInstallation().GetID())
			event, tenant, err = gl.getRepositoryInstallationEvent(
				installationRepositories.RepositoriesRemoved, installationRepositories.GetInstallation(), requestID, installationRepositories.GetAction(),
				installationRepositories.GetSender(), nil)
			if err != nil {
				return nil, nil, http.StatusInternalServerError, fmt.Errorf("error getting repository removed event: %w", err)
			}
		}

		if installationRepositories.GetAction() == "added" {
			log.Info().Msgf("Received repository added event for installation %d", installationRepositories.GetInstallation().GetID())
			event, tenant, err = gl.getRepositoryInstallationEvent(installationRepositories.RepositoriesAdded, installationRepositories.GetInstallation(),
				requestID, installationRepositories.GetAction(), installationRepositories.GetSender(), nil)
			if err != nil {
				return nil, nil, http.StatusInternalServerError, fmt.Errorf("error getting repository added event: %w", err)
			}
		}

	case "push":
		var push github.PushEvent
		err = json.Unmarshal(body, &push)
		if err != nil {
			return nil, nil, http.StatusBadRequest, fmt.Errorf("error unmarshalling push event: %w", err)
		}
		var eventMetadata *githubproto.EventMetadata
		tenant, eventMetadata, err = gl.getEventMetadata(requestID, push.Installation)
		if err != nil {
			return nil, nil, http.StatusInternalServerError, fmt.Errorf("error getting push event metadata: %w", err)
		}
		log.Info().Msgf("Received push event for installation %d", eventMetadata.InstallationId)
		event = &githubproto.GithubEvent{
			Metadata:  eventMetadata,
			EventType: incomingEvent,
			Event: &githubproto.GithubEvent_Push{
				Push: &githubproto.PushEvent{
					Repository: &githubproto.Repository{
						Name:  *push.Repo.Name,
						Owner: *push.Repo.Owner.Login,
					},
					Ref:       *push.Ref,
					BeforeSha: *push.Before,
					AfterSha:  *push.After,
					Forced:    push.Forced != nil && *push.Forced,
					HeadCommitTimestamp: func() *timestamppb.Timestamp {
						if push.HeadCommit != nil && push.HeadCommit.Timestamp != nil {
							return &timestamppb.Timestamp{
								Seconds: push.HeadCommit.Timestamp.Unix(),
							}
						}
						return nil
					}(),
				},
			},
		}
	default:
		return nil, nil, http.StatusInternalServerError, fmt.Errorf("unknown event: %s", incomingEvent)
	}

	if tenant == nil {
		return nil, nil, http.StatusInternalServerError, errors.New("tenant is nil")
	}
	if event == nil {
		return nil, nil, http.StatusInternalServerError, errors.New("event is nil")
	}

	return tenant, event, http.StatusOK, nil
}

func (gl *githubListener) handleHelper(r *http.Request, requestID requestcontext.RequestId, validatePayload bool) (statusCode int, err error) {
	defer func() {
		webhookRequestCounter.WithLabelValues(strconv.Itoa(statusCode)).Inc()
	}()

	tenant, event, statusCode, err := gl.getGithubPayload(r, requestID, validatePayload)
	if err != nil {
		log.Error().Err(err).Msg("Error getting github payload")
		// Error status code is set by getGithubPayload()
		return
	}

	if event == nil {
		log.Debug().Msg("Ignoring event")
		return http.StatusOK, nil
	}

	data, err := proto.Marshal(event)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling event")
		return http.StatusInternalServerError, err
	}

	if pushEvent, ok := event.Event.(*githubproto.GithubEvent_Push); ok {
		// Ensure multiple push events for the same ref are processed sequentially with ordering key
		orderingKey := fmt.Sprintf("%s/%s/%s", pushEvent.Push.Repository.Owner, pushEvent.Push.Repository.Name, pushEvent.Push.Ref)
		err = gl.publishClient.PublishWithOrderingKey(r.Context(), tenant.Namespace, data, orderingKey)

	} else {
		err = gl.publishClient.Publish(r.Context(), tenant.Namespace, data)
	}
	if err != nil {
		log.Error().Err(err).Msg("Error publishing to pubsub")
		return http.StatusInternalServerError, err
	}
	log.Info().Msgf("Published event with request ID %s for tenant %s to pubsub in namespace %s", requestID, tenant.TenantID, tenant.Namespace)

	return http.StatusOK, nil
}

func (gl *githubListener) handle(w http.ResponseWriter, r *http.Request) {
	requestID := requestcontext.NewRandomRequestId()
	log.Info().Msgf("Received request with request ID %s", requestID)

	statusCode, err := gl.handleHelper(r, requestID, true)
	if err != nil {
		log.Error().Err(err).Msgf("Error handling request %s", requestID)
	}
	w.WriteHeader(statusCode)
}

// For e2e tests, skip validation
func (gl *githubListener) fakeHandle(w http.ResponseWriter, r *http.Request) {
	if !gl.enableFakePostReceive {
		log.Fatal().Msg("Fake postreceive endpoint is not enabled")
	}
	requestID := requestcontext.NewRandomRequestId()
	log.Info().Msgf("Received fake request with request ID %s", requestID)

	statusCode, err := gl.handleHelper(r, requestID, false)
	if err != nil {
		log.Error().Err(err).Msgf("Error handling fake request %s", requestID)
	}
	w.WriteHeader(statusCode)
}

func (gl *githubListener) getRepositoryInstallationEvent(
	githubRepos []*github.Repository,
	installation *github.Installation,
	requestID requestcontext.RequestId,
	action string,
	sender *github.User,
	requester *github.User,
) (event *githubproto.GithubEvent, tenant *Tenant, err error) {
	var eventMetadata *githubproto.EventMetadata
	tenant, eventMetadata, err = gl.getEventMetadata(requestID, installation)
	if err != nil {
		return nil, nil, fmt.Errorf("error getting installation event metadata: %w", err)
	}
	log.Info().Msgf("Received even to remove %d repos for installation %d", len(githubRepos), installation.GetID())

	repositories := make([]*githubproto.Repository, len(githubRepos))
	for i, repo := range githubRepos {
		// Github installation events only fill out the fullName and name
		// so we need to parse it to get the owner name
		fullName := repo.GetFullName()
		parts := strings.SplitN(fullName, "/", 2)

		if len(parts) != 2 {
			return nil, nil, fmt.Errorf("unexpected format for full repository name: %s", githublib.RedactString(fullName))
		}
		ownerName := parts[0]
		repoName := parts[1]
		if repoName != repo.GetName() {
			return nil, nil, fmt.Errorf("repo name does not match: %s != %s",
				githublib.RedactString(repoName), githublib.RedactString(repo.GetName()))
		}

		repositories[i] = &githubproto.Repository{
			Name:  repoName,
			Owner: ownerName,
		}
	}

	var senderUserInfo *githubproto.UserInfo
	var requesterUserInfo *githubproto.UserInfo
	if sender != nil {
		senderUserInfo = &githubproto.UserInfo{
			Login: sender.GetLogin(),
			Email: sender.GetEmail(),
		}
	}
	if requester != nil {
		requesterUserInfo = &githubproto.UserInfo{
			Login: requester.GetLogin(),
			Email: requester.GetEmail(),
		}
	}

	event = &githubproto.GithubEvent{
		Metadata:  eventMetadata,
		EventType: "installation",
		Event: &githubproto.GithubEvent_Installation{
			Installation: &githubproto.InstallationEvent{
				Repos:     repositories,
				Action:    action,
				Sender:    senderUserInfo,
				Requester: requesterUserInfo,
			},
		},
	}
	return event, tenant, nil
}

func newGithubListener(
	webhookSecret secretstring.SecretString,
	publishClient pubsub.PublishClient,
	tenantLookup TenantLookup,
	enableFakePostReceive bool,
) (*githubListener, error) {
	return &githubListener{
		webhookSecret:         webhookSecret,
		publishClient:         publishClient,
		tenantLookup:          tenantLookup,
		enableFakePostReceive: enableFakePostReceive,
	}, nil
}

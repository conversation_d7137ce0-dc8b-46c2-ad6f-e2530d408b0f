function(env, namespace, appName)
  local name = 'github-webhook-secret-%s' % std.asciiLower(env);
  local webhookSecret = {
    name: name,
    objects: [{
      kind: 'SealedSecret',
      apiVersion: 'bitnami.com/v1alpha1',
      metadata: {
        name: name,
        namespace: namespace,
        creationTimestamp: null,
        annotations: {
          'sealedsecrets.bitnami.com/cluster-wide': 'true',
        },
        labels: {
          app: appName,
        },
      },
      spec: {
        template: {
          metadata: {
            name: name,
            namespace: namespace,
            creationTimestamp: null,
            annotations: {
              'sealedsecrets.bitnami.com/cluster-wide': 'true',
            },
          },
          type: 'Opaque',
        },
        encryptedData: {
          secret: {
            DEV: 'AgB4LDqTzafp/V9/vRH8G7GUwUftQJaSh7Ma/wag4o9m1DG0Qa8xUfDyRtmXfdgS9IdVy3Ggql3ziu7O9BoTMr2hzr08ZuF7WrfE6trw5SAKgVkRoAhHRcMRHAvqYxUhiC1x3/xmCmxflylb6OJ/o8kl71AN/Wg/qQYGQkGpweEo4GaiQdx1mZJeEDzH46b+CHw7roXs3Q2tbL1NNmz1FVv7qV/ElLbhWdmCRQnDdiD8c5twX5Lvp2QeHOttKdrYHRhNNcXYMenpFiBPhDgJUZKBHzPMY2M5OfS9TOzuoFYit/zpbLlgZlz4MEGLqcnC5VGwD8vJfE4ko9HcHn0+9XQdeIUwiEHxfQPn1Vbg4OUwXYiCS4t7gc0WftfUFJvLPd5/w55FtE5W0YcIJqbmrYs7L4xX+wAXdL0kl2aW3um+LSIVCPjrbAOPEGjQwnCFg2RD0Ola0/B/ofwARowWAgJXuz5jPCNOjsdJ4PBM/ILjNHgbQ76BWKGKdQFEI7tT4KfRrTR0c7vGDqyUwcofEJiIoX7mAHY4+NetZc8daFv/8E14t6EKXCP8L7y6Vh58IBkcikJ63Xa4DVVJW3rKkH211qv/ZpFcA/TX8RoMPpkp9qN6nVv8hWJpS7teqJwziBdO19142wyL06M+Sz6Q2tQWeVG5wlLN8AL9PhRKSfATSE4O5eAyu0G16QNgkxDl3vuP7aJlSY1bYouohNNdBcU2Mc6uP/0E0Deqi/VOAJ/S6uPaGbzt1VK289TGRFUC2hgERfeyT4bPJ/UreiiQh8rQwEXv2aSQ18N8ih3MvqUt3+hqLg3nbZJdxKnPGS7ChMo893PJ3hOCaa5Legt6tuVT5a+JkcdcKSi/kg+3HpObrzPSlth7xwxKt0MkGRxwE1r43TEheMArS+7DBhsb9lrSlFENOjdszsLgvAyEzMbFEHOdVYwcDypi7Q4+gBe8mhc3W0OXnaS6hg==',  // pragma: allowlist secret
            STAGING: 'AgAp9XQq1IN6qR2DNop/Q6Z2lnSTdvXA3F0dffwhSV2bSOP0xOqSWT3NSWzJqyIVUmJVkzInCrhbyaDesnqnqFdefbU2WuackgIqek53FUGYyKWQNazN4TmX8XBemHbznHEWoBQZkmQsatDhHMD26qnxjfs2QuvgNQDRBFOeCXu+XG3p96F4bpv7o1stBp2howmzWfqzMPOwP1SEKhTxEyvvHGFyRSVWviIC+TH8hCiYS0HoBZqg0HMj04AGgpf1PUg6Oi3qfgoWEQcKjh9iO9hCnME+C4t6Zpp18vsvRl65e9XN04F3SeBF6SUzDbuI/K12hmIHR6if4GW8cQb2sHDIr1oKEifAtik4X90AO6WEdkrJy3CmwhoQrJJM9dT6Dz+X1OrUaUr/sqjSwusz+n9geBu5uyPdNJVbUg+Q9snOZJ4oHSfKlLJgQs/qZpOVp0dtvZKBwPVSqFAYUQxqT4hqXJT9OSWv2mEGyIW1ZIBpFtRAY8O/6FPj8u5RwHk5SxGR2XOKM3BJsMLhSs0ROsDvBdm5YglyrhbKWk03VO4qxXoMuo9lev3u4bnFX5JgNtIEJOTyG8g8lrdqUrVVf7qS2JFYd9HEoGDxNZ2N+vOvacVdD6TMCiNWdj5gd3+pRRICQqmsDL4cJwOZMeJvk37/xSB4B4dH+Ks7XDi8EVx8irzcciq5B16jLt17ufNcoiiAGcbnViHgvC6ukKXdjGo7Vd+tYcFBM16IZpZl/YXSbBkdD6+Zil7xwHDcTfV2SBtP+HWO1+s40BMMnxckAB7ShIsSIh0Dr7RsRJvbfRTl/EgJtbP9y+wzV39gXV03B5RYk34JhSmla+eneZ2VErMO+OJiGgxeL+lpeZw34lPAXS8VYgk8FWjc1QW8CgWvz67XdmyXntgB+eTk92u/1hzNA0fGZ4V2ACBSF21UPB+WDpWebXa2XrShm77mlg72J9XzkckYhgvTww==',  // pragma: allowlist secret
            PROD: 'AgBFNh2qHlUuxXs8uxRu5H/IKOT38OJNr3ZzIDkucLKKnfiSOEkrGYXLdG31kr/ww9ygpDSqmF9O21D5YHl0LnaFlSC66defw5GuKM5ajcue2rTCbgxlJ6ex8/397H5klrAYWUzykbBwAN6pY6fh/1lDMBAtLf630a5ClvzkqZSsHVZ+wADKWrTtsoopH0bvgpeSChfQFIV7PceFltEsR0KrpEqV5DTnuSMnIgpVvVwXHMuSz2A8L5ABkJ452W/JFLMmRoItQMTg5KRMat1l38xPscKKWA+/ft58KXFvf1rs647OKo0uaeoYTha1wuKDDyISSLegPFxjKI8QMHizexpv4BI2rGZzX/rTeiokAAz2X/2RTmMlxLlWBInKg03SCmlohNaNVrd1qxYKc5szvDh4lR3WGc8uf3ClAsOkoac2krLL/Bw4IWfK+w3eXh+4f7gcya2WCdN97rwzn3VbcqhwQDo/t99t0ysZqMp7thjweKOt9ZRHBab5fEg6eHwn0VFyRlLBo1w1p5q3T+maQ8ubs5AAVPqSgbYFK6p+x1XAvggRphFIKYV0MJ2V7Zm4wkEJpnCin/K114n13yac7pP/qcrZnVlFcasnbUpuiR+zkk796dRNdM5TjqSDg/o08MzC44ZXQjcDANemignW+QOkxat5gJIMaQSjeDuTeU64bRcMsl6QMc1+G8fehwy61tAoCEQhbveJDXTY0vDUTGKiy2FHNF26v0KGxhrT0ez6Oqig0Pa+j8lRDEDUquiQEWMHmyNr/OxVAgqEhoj7fURO',  // pragma: allowlist secret
          }[env],
        },
      },
    }],
  };
  webhookSecret

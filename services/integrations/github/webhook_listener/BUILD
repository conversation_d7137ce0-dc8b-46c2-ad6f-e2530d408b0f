load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "webhook_listener_lib",
    srcs = [
        "listener.go",
        "main.go",
        "tenant_lookup.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/github/webhook_listener",
    visibility = ["//visibility:private"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/integrations/github:github_event_go_proto",
        "//services/integrations/github/state/lib:github_state_lib",
        "//services/integrations/webhookmapping",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher/client:client_go",
        "@com_github_google_go_github_v64//github",
        "@com_github_gorilla_mux//:mux",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@io_k8s_client_go//dynamic",
        "@io_opentelemetry_go_contrib_instrumentation_github_com_gorilla_mux_otelmux//:otelmux",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "webhook_listener",
    data = [
        "//tools/deploy:auth_kube_config_yaml",
        "@gke-gcloud-auth-plugin//:all",
    ],
    embed = [":webhook_listener_lib"],
)

# create a container image
go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":webhook_listener",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
    trivy_allow_list = [
        "CVE-2024-34156",  # AU-4115
    ],
)

kubecfg_library(
    name = "webhook_secret_lib",
    srcs = ["webhook_secret_lib.jsonnet"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/integrations/github/webhook_listener:webhook_secret_lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

kubecfg(
    name = "kubecfg_pubsub",
    src = "deploy_pubsub.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/integrations:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/gcp:gcp-lib",
        "//services/lib/pubsub:pubsub-lib",
    ],
)

kubecfg(
    name = "kubecfg_cross_cloud_roles",
    src = "deploy_cross_cloud_roles.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/gcp:gcp-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_cross_cloud_roles",
        ":kubecfg_pubsub",
        "//deploy/common:cloud_info",
        "//deploy/tenants:namespaces",
    ],
)

// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local webhookSecretLib = import 'services/integrations/github/webhook_listener/webhook_secret_lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  // This is a global service. Right now the only integration is github, which
  // only allows specifying one webhook per app, so we have one global webhook
  // handler.
  assert cloudInfo.isLeadCluster(cloud);

  local appName = 'github-webhook';

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local ingressHostname = {
    PROD: 'github-app-webhook.%s' % domainSuffix,
    STAGING: 'github-app-webhook.staging.%s' % domainSuffix,
    // We need to shorten the namespace or else ingress creation fails for people with long names.
    DEV: 'github-app-webhook.%s.%s' % [std.substr(namespace, 0, 10), domainSuffix],
  }[env];
  local ingressFacingCert = certLib.createPublicServerCert(name='github-webhook-public-cert',
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           volumeName='https-certs',
                                                           env=env);

  local backendConfig = gcpLib.createBackendConfig(
    app=appName,
    cloud=cloud,
    namespace=namespace,
    healthCheck={
      checkIntervalSec: 15,
      port: 5000,
      type: 'HTTPS',
      requestPath: '/health',
    }
  );
  local httpService = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'github-webhook-svc',
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'public-https': 'HTTPS' }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'public-https',
          targetPort: 'public-https',
        },
      ],
    },
  };

  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: '%s-ingress' % appName,
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: '%s-ssl-cert' % appName,  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: '%s-svc' % appName,
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  local publisherTopicTemplate = pubsubLib.publisherTopicTemplate(publisherAppName=appName);

  local roleBinding = if env == 'DEV' then {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-role-binding' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'User',
        name: serviceAccount.serviceAccountGcpEmailAddress,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'webhooktenantmapping-reader',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  } else {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'ClusterRoleBinding',
    metadata: {
      name: '%s-%s-cluster-role-binding' % [appName, std.asciiLower(env)],
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'User',
        name: serviceAccount.serviceAccountGcpEmailAddress,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'webhooktenantmapping-reader',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  };

  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  local webhookSecret = webhookSecretLib(env=env, namespace=namespace, appName=appName);

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    port: 5000,
    https_server_key: '/https-certs/tls.key',
    https_server_cert: '/https-certs/tls.crt',
    prom_port: 9090,
    central_client_mtls: centralClientCert.config,
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    project_id: cloudInfo[cloud].projectId,
    topic_name_template: publisherTopicTemplate,
    namespace: if env == 'DEV' then namespace else null,
    kube_contexts: if env == 'DEV' then [cloudInfo.GCP_US_CENTRAL1_DEV.context] else [cloudInfo.GCP_US_CENTRAL1_PROD.context, cloudInfo.GCP_EU_WEST4_PROD.context],
    enable_fake_post_receive: env == 'DEV',
  });
  local container = {
    name: appName,
    target: {
      name: '//services/integrations/github/webhook_listener:image',
      dst: 'webhook_listener',
    },
    ports: [
      {
        containerPort: 5000,
        name: 'public-https',
      },
    ],
    args: [
      '--config',
      configMap.filename,
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    volumeMounts: [
      configMap.volumeMountDef,
      ingressFacingCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      {
        name: webhookSecret.name,
        mountPath: '/github-webhook-secret',
        readOnly: true,
      },
    ],
    readinessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 5000,
      },
      initialDelaySeconds: 5,
      periodSeconds: 10,
    },
    livenessProbe: {
      httpGet: {
        scheme: 'HTTPS',
        path: '/health',
        port: 5000,
      },
      initialDelaySeconds: 15,
      periodSeconds: 20,
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };

  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      {
        name: webhookSecret.name,
        secret: {
          secretName: webhookSecret.name,
          optional: false,
        },
      },
      configMap.podVolumeDef,
      ingressFacingCert.podVolumeDef,
      centralClientCert.podVolumeDef,
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    httpService,
    serviceAccount.objects,
    ingressFacingCert.objects,
    if namespace_config.flags.loadBalancerType == 'ingress' then ingressObjects else [],
    webhookSecret.objects,
    configMap.objects,
    centralClientCert.objects,
    deployment,
    roleBinding,
  ])

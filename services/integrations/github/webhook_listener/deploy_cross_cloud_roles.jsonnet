local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  // Copied from deploy.jsonnet
  local appName = 'github-webhook';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloudInfo.getLeadClusterForCluster(cloud), namespace, iam=true,
  );

  // give the GCP_US_CENTRAL1_PROD github-webhook SA access to all clusters
  if cloudInfo.isProdCluster(cloud) && cloud != 'GCP_US_CENTRAL1_PROD' then [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRoleBinding',
      metadata: {
        name: 'github-webhook-%s-cross-cloud-role-binding' % std.asciiLower(env),
        labels: {
          app: 'github-webhook',
        },
      },
      subjects: [
        // always give the github-webhook service account of the prod project permission to sync
        {
          kind: 'User',
          name: serviceAccount.serviceAccountGcpEmailAddress,
        },
      ],
      roleRef: {
        kind: 'ClusterRole',
        name: 'webhooktenantmapping-reader',
        apiGroup: 'rbac.authorization.k8s.io',
      },
    },
  ] else [
  ]

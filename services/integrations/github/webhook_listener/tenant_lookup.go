package main

import (
	"encoding/json"
	"fmt"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
)

type Tenant struct {
	TenantID   string
	TenantName string
	Namespace  string
}

type TenantLookup interface {
	LookupTenant(installationID int64) (*Tenant, error)
}

type nullTenantLookup struct{}

func newNullTenantLookup() TenantLookup {
	return &nullTenantLookup{}
}

func (n *nullTenantLookup) LookupTenant(installationID int64) (*Tenant, error) {
	log.Info().Msgf("no matching tenant found for installationID: %d", installationID)
	return nil, status.Errorf(codes.NotFound, "no matching tenant found")
}

type mappingTenantLookup struct {
	mappinginformer webhookmapping.WebhookMappingLookup
	tenantCache     tenantwatcherclient.TenantCache
}

func newTenantLookup(mappinginformer webhookmapping.WebhookMappingLookup, tenantCache tenantwatcherclient.TenantCache) (TenantLookup, error) {
	return &mappingTenantLookup{
		mappinginformer: mappinginformer,
		tenantCache:     tenantCache,
	}, nil
}

// TODO: This is duplicated in processor/server/server.go
type value struct {
	InstallationID int64 `json:"installation_id"`
}

func (m *mappingTenantLookup) LookupTenant(installationID int64) (*Tenant, error) {
	v, err := json.Marshal(value{InstallationID: installationID})
	if err != nil {
		return nil, fmt.Errorf("error marshalling value: %w", err)
	}
	tenantID, err := m.mappinginformer.LookupTenant("github", string(v))
	if err != nil {
		return nil, err
	}
	tenant, err := m.tenantCache.GetTenant(tenantID)
	if err != nil {
		return nil, err
	}
	return &Tenant{
		TenantID:   tenant.Id,
		Namespace:  tenant.ShardNamespace,
		TenantName: tenant.Name,
	}, nil
}

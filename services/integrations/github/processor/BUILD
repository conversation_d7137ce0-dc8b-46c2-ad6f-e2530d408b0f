load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "processor_proto",
    srcs = ["processor.proto"],
    visibility = ["//services/integrations/github/processor:__subpackages__"],
    deps = [
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:struct_proto",
    ],
)

go_grpc_library(
    name = "processor_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/github/processor/processorpb",
    proto = ":processor_proto",
    visibility = ["//services/integrations/github/processor:__subpackages__"],
    deps = [
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
    ],
)

ts_proto_library(
    name = "processor_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":processor_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

py_grpc_library(
    name = "processor_py_proto",
    protos = [":processor_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

syntax = "proto3";

package github_processor;

import "google/protobuf/struct.proto";
import "google/rpc/status.proto";

service GithubProcessor {
  // The first command installs our Github app, the second authorizes our OAuth
  // app. To read more about the difference, see
  // https://docs.github.com/en/apps/oauth-apps/building-oauth-apps/differences-between-github-apps-and-oauth-apps.

  // To be called when an admin *installs* our Github app on their org/repo.
  // It takes a user oauth code so that we can verify this admin has permission
  // to install the app to this org, discards the user credentials, then saves
  // the app credentials in that tenant's settings.
  rpc HydrateGithubSettings(HydrateGithubSettingsRequest) returns (HydrateGithubSettingsResponse) {}

  // To be called when a user *authorizes* our OAuth app on their user account.
  // Takes a user oauth code and stores the user's credentials in that user's
  // settings.
  rpc HydrateGithubUserSettings(HydrateGithubUserSettingsRequest) returns (HydrateGithubUserSettingsResponse) {}

  // Call the GitHub API with the given request
  rpc CallGithubApi(GithubApiRequest) returns (GithubApiResponse) {}

  // Check if Github User OAuth is configured
  rpc IsUserOAuthConfigured(IsUserOAuthConfiguredRequest) returns (IsUserOAuthConfiguredResponse) {}

  rpc GetGithubUserOAuthUrl(GetGithubUserOAuthUrlRequest) returns (GetGithubUserOAuthUrlResponse) {}

  // Revoke the OAuth grant for the current user
  rpc RevokeOAuthGrant(RevokeOAuthGrantRequest) returns (RevokeOAuthGrantResponse) {}

  // Get GitHub user information for the authenticated user
  rpc GetGithubUserInfo(GetGithubUserInfoRequest) returns (GetGithubUserInfoResponse) {}

  // Get the OAuth token for a user - currently restricted for use by remote-agents
  rpc GetGithubUserToken(GetGithubUserTokenRequest) returns (GetGithubUserTokenResponse) {}

  rpc ListGithubReposForAuthenticatedUser(ListGithubReposForAuthenticatedUserRequest) returns (ListGithubReposForAuthenticatedUserResponse) {}

  rpc ListBranchesForRepo(ListGithubRepoBranchesRequest) returns (ListGithubRepoBranchesResponse) {}

  // Get a specific GitHub repository by owner and name
  rpc GetRepo(GetRepoRequest) returns (GetRepoResponse) {}

  // Create a new pull request
  rpc CreatePullRequest(CreatePullRequestRequest) returns (CreatePullRequestResponse) {}
}

message HydrateGithubSettingsRequest {
  int64 installation_id = 1;
  string code = 2 [debug_redact = true];
}

message HydrateGithubSettingsResponse {}

message HydrateGithubUserSettingsRequest {
  string code = 1 [debug_redact = true];
}

message HydrateGithubUserSettingsResponse {}

enum HTTPMethod {
  GET = 0;
  POST = 1;
  PATCH = 2;
  PUT = 3;
}

message GithubApiRequest {
  // GitHub API path (e.g. "/repos/owner/repo/pulls")
  string path = 1;
  // HTTP method (GET, POST, etc) (enum)
  HTTPMethod method = 2;
  // Optional: Bearer token for auth. If this is not provided, we will use the user's token from their settings.
  optional string token = 3 [debug_redact = true];
  // Optional: JSON body/params for requests
  google.protobuf.Struct data = 4;
}

message GithubApiResponse {
  // JSON response from GitHub API
  string response = 1 [debug_redact = true];
  // HTTP status code
  int32 status_code = 2;
}

message IsUserOAuthConfiguredRequest {}

message IsUserOAuthConfiguredResponse {
  bool is_configured = 1;
  // OAuth URL that a user can be redirected to for authentication
  string oauth_url = 2 [debug_redact = true];

  // Whether the user's OAuth is configured but needs to be updated by
  // reinstalling the app (uninstall + reinstall)
  bool configured_but_needs_update = 3;
  // The new scopes that we will get if the app is reinstalled
  string updated_scopes = 4;
}

message GetGithubUserOAuthUrlRequest {}

message GetGithubUserOAuthUrlResponse {
  string oauth_url = 1 [debug_redact = true];
}

message RevokeOAuthGrantRequest {}

message RevokeOAuthGrantResponse {
  // Status of the OAuth grant revocation operation
  google.rpc.Status status = 1;
}

message GetGithubUserInfoRequest {}

message GetGithubUserInfoResponse {
  // GitHub username (login)
  string login = 1 [debug_redact = true];
  // GitHub user's email
  string email = 2 [debug_redact = true];
  // GitHub user's name
  string name = 3 [debug_redact = true];
}

message GithubRepo {
  string owner = 1 [debug_redact = true];
  string name = 2 [debug_redact = true];
  string html_url = 3 [debug_redact = true];
  string created_at = 4;
  string updated_at = 5;
  string default_branch = 6 [debug_redact = true];
}

message GetGithubUserTokenRequest {
  // The github repo we are trying to access
  GithubRepo repo = 1;
}

// Note that the fields here may be empty if no credentials are necessary to
// access the github repo in the request.
message GetGithubUserTokenResponse {
  // GitHub user's OAuth token
  string token = 1 [debug_redact = true];

  // GitHub username (login)
  string login = 2 [debug_redact = true];

  // Github public visible email if available
  string email = 3 [debug_redact = true];

  // GitHub user's name
  string name = 4 [debug_redact = true];
}

message ListGithubReposForAuthenticatedUserRequest {}

message ListGithubReposForAuthenticatedUserResponse {
  repeated GithubRepo repos = 1;
}

message ListGithubRepoBranchesRequest {
  GithubRepo repo = 1;
  // Page number (1-based, default: 1)
  optional int32 page = 2;
}

message GithubBranchCommit {
  string sha = 1 [debug_redact = true];
  string url = 2 [debug_redact = true];
}

message GithubBranch {
  string name = 1 [debug_redact = true];
  GithubBranchCommit commit = 2;
  bool protected = 3;
}

message ListGithubRepoBranchesResponse {
  repeated GithubBranch branches = 1;
  // Whether there are more pages of results
  bool has_next_page = 2;
  // The next page number if has_next_page is true, otherwise 0
  int32 next_page = 3;
}

// Request to get a specific GitHub repository
message GetRepoRequest {
  GithubRepo repo = 1;
}

// Response containing a GitHub repository
message GetRepoResponse {
  GithubRepo repo = 1;
}

// GitHub user representation
message GithubUser {
  string login = 1 [debug_redact = true];
  string avatar_url = 2 [debug_redact = true];
  string html_url = 3 [debug_redact = true];
}

// GitHub pull request representation
message GithubPullRequest {
  int32 number = 1;
  string title = 2 [debug_redact = true];
  string body = 3 [debug_redact = true];
  string state = 4;
  string html_url = 5 [debug_redact = true];
  string created_at = 6;
  string updated_at = 7;
  string merged_at = 8;
  string closed_at = 9;
  GithubUser user = 10;
  string head_ref = 11 [debug_redact = true];
  string base_ref = 12 [debug_redact = true];
  string head_sha = 13 [debug_redact = true];
  string base_sha = 14 [debug_redact = true];
  bool merged = 15;
  bool draft = 16;
  int32 comments = 17;
  int32 commits = 18;
  int32 additions = 19;
  int32 deletions = 20;
  int32 changed_files = 21;
}

// Request to create a new pull request
message CreatePullRequestRequest {
  GithubRepo repo = 1;
  string title = 2 [debug_redact = true];
  string body = 3 [debug_redact = true];
  string head = 4 [debug_redact = true]; // The name of the branch where your changes are implemented
  string base = 5 [debug_redact = true]; // The name of the branch you want your changes pulled into
  bool draft = 6; // Whether to create a draft pull request
}

// Response containing the created pull request
message CreatePullRequestResponse {
  GithubPullRequest pull_request = 1;
}

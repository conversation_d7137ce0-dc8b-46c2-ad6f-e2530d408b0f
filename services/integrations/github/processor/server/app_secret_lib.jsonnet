function(env, namespace, appName, cloud, secretsOverride)
  local name = 'github-app-secret-%s' % std.asciiLower(env);
  local useSecretsOverride = env == 'DEV' && secretsOverride != {};
  assert !useSecretsOverride || (
    std.objectHas(secretsOverride, 'app_id') &&
    std.objectHas(secretsOverride, 'client_id') &&
    std.objectHas(secretsOverride, 'client_secret')
  ) : 'GitHub secrets override is malformed';

  // TODO: revisit using Secrets Manager, which would simplify cross-cluster secrets
  local data = {
    GCP_US_CENTRAL1_DEV: {
      DEV: {
        app_id: 'AgCNSln4ovrbchHJkrSKafHDOsT9ITwIvOcw042p1aghDTk7yrE3lKN8OmNVTNeoK4+wOHJ/KrXCxWjuRTNFO+znYfT/FgwsYQoJ0S9predueQ0/bKfe7/656shaUaX4OVsbHNWg3XaW6jFFkDv8tfe1i7JUGVmHtOwW0FAdly+uIb3AqYf0CcPDWFBTuL6ZxUqT4JV6WnA5fv72T3tH+DBqdpZ90wuMxZ3fkBomHdbFPmsTlCJwRgPNsG53euc2dMN/EY6Kol7eZVmzGKRwRcKyF7RegjVtq5G2cTvWeS7QFI5WX2IsMq8NyXF+lZG65a5x/oMfrOd1tkpHS+iMWHjV/hFs42iGjCEkSzeiV2+L7xvvDtCejCfZ4H2CIKx9zJLAV3PkfsWbXNN2NvWBFiHMw132gEud3vH3qz4Z7fwJ7wTHA1ZjERJvYhs40OH3K4NOkjGURwcufhGn8H6YlrgTRtnNUJ3c0j1Q6doiuGEGETSdJAeCzCQS67l9gVaxe+WUEHGUQA8xxQYE93WT0oaBAgz7ZpSqtYOsmryLJyrjygOIRM2j9K0UUawXfhmfaRgGWvvCkO6MuFk84pP+AGPof0RkO89BMLLfU1mYbO6DDzdoCIRHPzPl9k2fmrdSrAzkFNxIkxaCXpZ7R7Znuk6vewJwQ/XHilCzH+XPK7XEtxGfc4/8ZJD1Q2YXWiG7ZAvGicYvKmAh',  // pragma: allowlist secret
        client_id: 'AgBzI3vXrj8dPwJ5Q/0Aw+z9wcDUzHMrk0/Y2rybyoZ5tvI7Kv4SiOPUErY4BiiHnPAlmneBfjOkMgrEwU0JD++iPPQ+dYOvA57EnxDh+2MSJVH5X1UK9K/RyDp5hN30yd9OjWjkJouRJpZ/DpHgDGGl/rRqnKCuA/FX+/TOHvNujdZx4YgTsWQGRBRTIm3hom1eC/ryJG7BMUIjXY7LdoHWafVMQSVV4rWpasYRLyBDEWKZEMxLzgBqku05x1RYcs2WDtTH0ZRqvnYEcWgqtl947Eu9/9x2UvuJWACY0oTAQUoKqtwzRvL6HpiNb6tt0dQwDSGebDTBOmd3K4Ja61GgRBZ4XSp+cMlfTuT4fDd+bVZERYhXD7DyU0R3UEmkzRDxZowaukCO5kFlTvDwn+yBKYwZ0idThcuTT8IeR7APY4RL9lbDDqWDQNapTMJpgoIj58R0nNRqEW6cjjzA6zONFupFtesVglVtI9yKbOFSZqStx7+e3aA2imZA1WxFwMohD5yhLkVcxdrkFrRIWEuBEuQRoFn1/uXq1jEezrrLAyJOTyForOaXH6MitTWKp+wKNRej7mZ9iZxO1RhChrO17recvpDSsDbRsvqArFWhXz5tMnap3kBbUKVVttfBNKNheSdWVMR1gdpW0b7iRJpvPedyr80cgdoT7v7PtcCucXrVcCcYcUAngpmFAfbxp1MSw9NIHHrr0nrpvtwke1y1FBNtkg==',  // pragma: allowlist secret
        client_secret: 'AgAqlrz8eoO/+PNRwDTGJuJAeEL432ts8PGP9IDpwjkYK14PdpMHmMOrEawjDBF9f39INUPsH7kZDHpExDXY10UY8Hzou5xpdg+xD5GLctjsKZT/QbNmTGMw4ur/bILfw83MNFbAAEv0A/9EUpaYHyTti4CL+KhheoBq62R78fK7F/Hh1OHBZebj4gctiATjUNAwHh8SHI5TyqeTZgg0q8TcgnRaWcgICcGdE2Mkv3vTpKdk5g6s07Miknd1MvSfDjLXKrBTf7kWrtQKIv1uNdLeVDiYKcnJScdk1lDmOWd4hc62efDfXSut93Hr5xhJoJJVggG4Sys/EFHzjTn4n4QNTfUsUaX15edB440GAHniip5X+hOb5An1zlVzqQ96WE2SERVFQKIyj/afxdCHJgmGkNMU26hvPn+m29+YTVr4bXHZepxArJvD+eJmy+An4GgzwSYv0huzc7C7L6RXtm5kKlKOjsfqMG8Ds8Lu67T7kC9QOQJB8anEebhxndpZUWHDB5auiCq8JZaILnPupXhM6eDBf+o23beaWT9ioJ+2/rzUUDrRHCMrbpBnYWF3lZZSNsiY2d7yIH6SnrTiP/jwfKVzDZ3iuAGqxyha7dykFTge0CzxVUr3xrvu2MjjzTJqH7SXdnWLE0sbavUVuvr74I55RxQNKSp4SmsHitDzbpeno5DRkBzx5Uv74/6zig2uuGkU02ujCz/5yp2RS45mz7VIzmJqvM0o2PEjYqMoN7QQ+8k+Go7W',  // pragma: allowlist secret
      },
    },
    GCP_US_CENTRAL1_PROD: {
      STAGING: {
        app_id: 'AgAP2LkL3bMLoCyVzQQaIaJ8qm0FfQUh4+yXXT241W8YdDRBzadKqinPUZ1gOsBXM+cxAGeyuJlKH3oqnHhKcZkVqoJl2ivHx9Q98ZQpytCKD1qlI92hxcuB0/R8X6aztZRmVSRJz4ufB1ofHfiE16rQl6863QzxgyLGiFytSFRYaDvbpLevwiHBVwWaYum7FFKtKdJstxroTfLy9Uo3BfM/f7W8Qwp6TVuks51VX0k5dYFhqZfv8VjHi/SUNVZ74l3u8v4trmHPCRvoBplaA64T/4sL25DFHxbPMMEsM+2N9b5lRQJz7HS6qgFQwBXUxTriHBNwefem5G5nTc1nF642StOra/3hzrWMOM949FU8WVqXXYLiO1TkR58RBMdORnUWhtAlQypfbt75bKcZMy3xxjjVVEjXrDU6gYFb10HAosOoPWJeD6dOIZnb019HpoXTBPE9VzQQVJPK8d4nCxy5Lej5RWYvky3YcbqNwAO3c3PXOKy8EJNq0eAvtLhYNnLfeDT8cOyZvBAtTaBe0/HdajJZSiK35qx60iAcu/quq/PCOUFUACOJYKxW/OkRW38jgcHWKFdhQKugZFtvRFO44q+uLIk7/god4CpcnPNR60PiKssdfmTAhNZii/RGPx4HXiV+A2ne1TBDJkyjLR6Xdtt5c6EkCLF7bpxERQXKuxCkCKugdKxTAM49U+T6cmx0G05DX4xL',  // pragma: allowlist secret
        client_id: 'AgAB+IlRumLT04nryGUqYSlTvV5ZfZefsBu/mIlThlo5qYGkZXkK06TNhZiQO1PmupiQIDqQ1YW65MN7RtLpHa0Okf2SfmwWqUKUS7etbLhzuCgigbBDelWpRT+vn5pEvaAmj6nBy0IwwB1J9JxRa1Vs27MYjCfjpeRHuVdyJbv1eD1VqwXpn099OKxTzYcoZA1ck5+3H50O+PH552WZN7el7Jw2Sb5bEdbT5Uz3LfHADtWkMMqj/VQKPhpaJAwxYsU4QtoA0UYHxdjyJXKHkVHnwrHfbiX1eVLFJjksDd/c9Ebibm5TCpTYWxyg4bkWnCJKEf781n/PtD6bJTlHScnYH7iV4Mj5eEgIWwCYvrMn+vUzsWyh0mErY2CvAyMTWGdFa9m330NUzG31wBis8xBPkP718ti21xIM3DlvgSoBM0S99DjjTH1ZAJSAYmL2gRAv/Ny+wPLgktM4USP+E+lYRllZ2sZ8l4dbnOCFDP6Ib0VYNf+2X0zomroO+sVDYsiM2tgmuc2pVGyuHtcrlWeZeRJ7jwljvF4ppNM5sgwZaANulIGn8ERkGOXcWoNQ3ff3JGbl1JaZioFhLus+LOomib+2RCcOKaHKftrE6O7FAtqmFtnMkR2X8WE6aO9d3KXBx7dJAs38lPmvfei+37zpf2JvsClyoJS8Fv6ulYjDkssqkgl4gfjTSEIyJlzc2aSzN62LwFVjoTfgcEIFxL2UGHS9Vg==',  // pragma: allowlist secret
        client_secret: 'AgCx6tNo4pF66Msz7GWyJxzaO+xCbpEXdDBVNrTPWT30zI2j+k9Etx6PbSnj42pl75LpDBwkJzXKtWYP+pX1X5lVgxEOYsawp/iSNZggShyTrSiI0gVz0ASL4Bp2js5W5GQNPpluXaaG0oWgp96IpuK1KEA1GMZG9M6Ni2HWR5z17Cw8BSzvX12gmOceDE929WxeXtIWwizqSGiCoX2E9O3w4tVSP6+8DB1NFAse1ded4MpaN/Y1cK0uzEeBJoyFUizxPY0Gr6lr/vpjCk9lrpBMXS16xR/quSjC8l7wDlBSGkFjgTZ50f4BPtJi9Way6SlVgdHhy7UWV9yA7nddeDuXTkd7D0Ps6dKgyxkbIvIKrVuTlqnpA8Eeliho/MPi2HbP7BKDxaZYGH9mQU0JPFiRZzCb41dVb1BCEny0Vv/kjHC+dl4JL0XJtmJeChtrrdGyua6fqnpmytpmjG09BluCdK96KPcklfyp9CVJsMpWfsYhQQWdAPsiiz+j1BIfjq+CCpf0kam0LpJcCcCvFuwuYAEHPwptwRpS9Huk9XO7eWZ3D+2S6MGCTzo+eYEr+5uU1zeWq3dr9XQqrt5Xivf4FoYt7y5ChnI+gFnoWp1MsaFk7nbCqdCWQzNxRU16Q9/m23KaKrsmNQNCP6Um9m7LQIvcfpHY4BqhFhxg1BjmjmBhVwtCKroOPZdoIofhXIzCiA3nBq5lmvBk9z95yR2k4RpTRym4FLO02NlaQgtDo0z/rm/jN2AF',  // pragma: allowlist secret
      },
      PROD: {
        app_id: 'AgCyoz32Z5IJVecLpf5bPedLyVifSa7OXBY1kOE96+XYEzSk7RNw34lP1u0/njZh/DHg/kcn3OBETrZe1yTgzeH7Hi9JFr5oXdl4fXoTFe1zB2aSGeJhZtDqMrmJvsOC1/N7RWZ3l+nqGaeeolhWNUHthF7LEvpUakXc7J4C4QY388Kv9Wo9ZBr0zolCxsgx0qhuJRP0WOzxdyKY/WPFXY0xuRrhl0EmHmKaLy/x3GXHAq61UEeYPjli/VHIBoyWkmAQ0qt+tCDwZbQsPDq5xwpXZaXKFSWJ+OCB9wkcNVaR65R2BA0WP0MsCaFV36wU6Rih60L8Xx9IW+sP8wXyl9vte5Xb+ddECTOt1HhXo/zTCeknWwcTbYnIrcpb8LWDl/ipjNfx9GnzVPMy/xL+tjmKF8qrs8DrV+/5SkjHKrpKCEPRr4JQsKtIMu3wKRm5hnqtSJuuRf/rFSuligmp/vbl4rSNGzGoBOkS13qI6UNza1vQTgu3Z8uEztVOHvlnUcaXalMJ8WOM8QSU8w8WkA8jNm6vioCumDwhCdmySg6aQ5B8Y4aNMAZ3TwTF7subSHyOmDho2bfp1Bb68kv9tfxqYiWYaYtT7NB53xbbHVj0000np8ISHgQVY8sKT+gRvWj+4xrnAjb/MFc4hpeYV+9dan3TjBwpfKrZ0nfOFuV/VO/KV903MsY6sPa+WXpH3swayxzGV8PE',  // pragma: allowlist secret
        client_id: 'AgBrMleK4VlWzJOjAWJAZ+teGxBtoVi0Kabo+5/Wevx9kSvVDcZlUxXc6iKt1UA/AbgAFKLqiX5abNCsyY+fVoXkfJ8jji9gfCTROZTnFUa8LVXBj0kVfcF4uivpwsXRxd9E2EOQ51/SqSGsfo5zhn/nYOYg/b4ecvJAXEFDBR/FmQugbm6d8/QRIjNS/tB5L/kiPcHVytD+Pg9XeAjlxt7frTadecBiLM3vVFOjiUlYr+2A8qct5F3C0HKLsF1/7BC0JFNRqHY1NX7gztULggIBbflr9D5kxoQJdY9ZwsFuTGFUaOhjlKTxe+YwD4P4936n3BkkD5Z9T3969V3UtBddUfQ6bqGQ8N/+7nmLyi688sU/UW9PjnqfP/gJP6agNAmd+ocnGfGFyOLEZrrGM8z+bi2tbksfjCUTjVI2dY8FucMW8aK85oRPwT65V/9++PCoBA1X1X0wNF0I9peYMe9T8l6FT7jKrL4iYV7iAv0i4OAlMoDGWU8FpBH9iOLcrID9R4FU5u/ZPNFCh7ll/p3gWVP35qYq1Y1+Fp+0yzmcQBHTHCL/G2LCY4w0FC4K2+0y9tW9iJW5B48dWkcOzljdWdu6rjdB8DIrn+d787hZHz8cgu0zOulDcsXGZCiVr19XBDy+94cZmw73hIgOfN9xcxePUne38cZI9Q9z+jXiC7/uEdz0b+BPdpcIfeMGAt1wWKH5UPGvodTqdV2+U6Hm5AbqSA==',  // pragma: allowlist secret
        client_secret: 'AgADKkrFyOTb6knDSY/nCaapU3KajLd3Nbym/PzbPFHchiZU40Zespm12HwDMogKm1h7fiARKsjNheGsAH3/uV5nyGri57Q667My/soSrEm8rTX/k+dveoYXAA//jjaJy3h/dkZTf0mcIZYaMM1SUEF5wtsf/r6i/T++ztaxImi0TJDlCke4JZ+6ggYVWsz9KGNqJSscP+7fZ1ITWVY8SOnzmfIW/99BlKKwPf0vOCQypLQoeMmNBlPeCnxcaZErJARMs1iOIq0wp9PeLVgAwWxW7HKZ6L1WfkTCOXY1lMlsYkES+m34cmxUVrgFopc+vtF16QVlYIQxBVemL7ysRqf9NLfexz98MxoyWfrhfW5JZs70YZEczgCh6eY0vW04nwnwYPf8vw/qVMLFIXjfSOM/akH7N+BGqAQbk8akWG5SJ4e2LI+/eVVuanI8vVIwEA41Q+4QV+tCDUWSS0NJGIhiAsuz2P4sOoDY5TYVCg5eNlkt36TM/b39hCqrOw95OMyLQ46hCyxfSd+47TVPC6iwpTvNUlAZCtPwIurziAqloapzdI/lcDy6m533ic8+k/ZM75IenCsrsGSR+RFIBOhKsNq+iFtzOLw7Y/JAM3fc76cM+HAsSzNM6VJ6dYxWLPNJAbqOWRg8M0f8Bpsv0nL3MRx1IjA8lELpt7rFx6HPmqkP0fzAqXj6CMzU7l84xAi0XaIbO5PkWqPDVAgNimmo3ZJFtCR3l7TlRgqo1j3mcvPvMiZYL6rM',  // pragma: allowlist secret
      },
    },
    GCP_EU_WEST4_PROD: {
      STAGING: {
        app_id: 'AgCU2agy9K1nnXIfybzhVVjW8YP9bGLLTbXT1EbeviKK2R9DX/WBoXPuWcppdP6CuUgq4rSJ7NJHTsvO/RTbLoJWd0kcwYlnTYKZHrK8jpFNhTZ737VcCQx0LkYzzwC3Tk2NQKhnZKkL2R0BKudQTgUkTlbp9JkltCtQVcXdpZ80F+S12YSVNQgigeev31t/geEuk7sb0vTB3oCg5fWvyMkYANLuhpbX6cYftZXk8f+9tX97yJSMTnUYXukWxSG3H2wYX3qEC2bu0Wyhm1fUU9bJf0/OIQ4JrEZ2db6QHk2XVQ/DSZAAAfI351ZzMMUq7Kor2WZj3W8McXdonO54CGwJyerrwPgVG0WNdvmQRpez9mc79MomNx8mNc9pymNWYGL8KtPODRl9OohJ8XVWPkgy1qCCkLFeNFy73NdOysRgBW7tsuNFMa5mCHp8gdRRO4IkurSrednKL7+zitJ0FSWNOJLzj2CyaqwH4H/iQEobhkoNdEl9A9nWok5CEdvJ0R9UEYtUnME2VJrWUfsag3Jaj42qwgRI8RsZMErHndUF3cWnziwhOF+q0M/HgBVohXAG9S3XNiVRdydtty80a6N+JsieT6t1Hc9/kOHclXQopYKiAPGQN3NIOjVGFNdaO+PD022rh+bdB4wA71J0TeDpllbA+av3g7+It4mlEmJ676h2IpOH5HXLG4v+W0V2R/tjvHbw2E9m',  // pragma: allowlist secret
        client_id: 'AgBHAifG3wa6yTjjqdckJcNaw79czIFzCkjwF3EuyRhNUZyk4LUg8ewwe+Cm+4RwQv97HGZw8J36WvhZjSNxRoLPc1Niduh05wwaMSXJyLVfDwr9HMQCEy3quAHEJIBydKNTUHtz03Adj4YMWcn4V7OXwAJoQvew1ZsBIop4OpJcgu0WDJkH0rf48hkvBKdFaJGNWCA85XxUb3z0avInk46S8w0O9UDDgbNn97Hq64CyDTtvz7knB11IRvlUpshsHrsADoAZyRXQTvfA89Zca31E899QSKpbbXOauEJuFxKRCIJpng8fGkx0bTv+CjC/+W6YmKYQgwOIaFntZHfSdGyof6mUPQHIFWPE31o2mrM5dJrC6GVTEGiEZK1rerHKjlpG1A4oUqzoeTCmttIMd+tzmC/sashMINitzsf3TY4xGXAnvCmPSKSjA33VvICFt0P/i8EbFcQyx9xP05uQlSwvEweVr8XWYYv/lBd4gP+UxZUXKN83Q3Xi9y0nrnc6F5/zH18wzKZQRHeoGLRRkwAQFZg3AxFkAd2wtCxnWd65UpxLvKnZEmF2lTjIvXkROUGnRgDms58cb3FBJ3IPWMwcFVsDQrXFgH4NnWRn01OkW40iknOknC9TM0s7V+l0KECg0PRWNp3BqlIJnIHB/5tLRZf90MxPA7bKXYfV/o9Qe9DKLKgC16J9tJxKuLC22fIK6e8fa7dqK6mbRTxSavfA672OxQ==',  // pragma: allowlist secret
        client_secret: 'AgCeDhrvrr+X0pqe0s3gLT+XdaKUbSVG51JzDy87a8YuRkXMisd5JKeleylvtKypq0/AU1/lceQYHsF/DkjcQmyZmqm5BsQnkt6UKtSYjHjydqoqX5dOiELPxJ6p8hxqFsSnz7kE11VyqvPXndlblsmtaGH2ioib29fBgoZ6mombRVn8tGBC3ZEZphlxvjoYqwmh40ABlEXPEQ3RikBl1yvV3KVYnSbdgV+RxUhoY8iE76tdD8MvZCkdNHaeoTKai2lRPP80Mdm4No+JXZaYQaMGIrsCH7el9S4ZEYIZY7OdJW0HXkiEH2Db3HJIHHntY4hx4u7p/x8epgCnc/OKKscM9YL+pqUcoc2Wz8oPJhpqhpJv3TU3X6UDZ+3tkCN15Tm8QiKWnG7eTnSSpHNzUYqsYYbirmg8mDRAp+1++xYkdScRqXbC74Nkl1WGEcHvQAIWRz+ohV/LQq+U6d8h9gC8XPmfX5RnkKSsPhM1rzkAzBuZGx3ybI5W0vl9R4/gWe3FONhqMoIEWx/Ev3Q/JTkhKG6bv/5v/C4mLWRbgD0jnnTK4nB830AKD5TP2WbN7YUBLckDjHs6V1MpXbbNB7fp5Q/HTNAubIsw1nqnFd6tKCs5tdfoXGQd9Ii+e2jUFk4fZTGIBklEesCj34v747NTOENMhNzSB4j2DDus3jZEZ0D1k6FNmBrSjwO6Fgoz0AgEFS0yj6VUlDmPr4QUMv+amCIw85dUY2IFjDgr5UXKn+Quac161rmY',  // pragma: allowlist secret
      },
      PROD: {
        app_id: 'AgCOsM7hrwO4MfnNcz3DDpKgBt9r36eNA+4ULBgUTHdH2maIAPo4grcx+TI7n3UAP8MRjEXCbIzIhtKKeCyVm85MixRWM1aQKOF4GBb+Z6H1twXZlSmxYocq9dIUV5yiFBjfWh0BBm1uZlRH5ofjKGubTZ2WaSAJGHwMr4MC7j6e/PdsNFPprBxqpoYZdK5V7L+RQVvz4Gkw4OM7SKrvciLtcIjsR5hMSHWTbybtgpBe4u+FmeEx5flIEvVwPtMPu+2ksFm8drWtX3ItXUvo7YbE2mbrMf7fttvMv4e1nCjf/zQhiHBt5gQXqq/sGR3aiDnw6D/y3orJQnsfq3V0kFH7ykw1MRySBv+JrhN5JDOfBJOhVldIKJ9mupHvau0q7HpC5yph5ywTvf/+y5I1CjXy7/hUJuOQaGNF4WCaTkwa+2A8G21wMIC4hw3n3+CjdvOnqZUoBPKeu+nw6WuyGpByUldlY/s64X2dP1uYJgW+bAKnSn/CfiWzWeVOVgfjkkBPUpIa8BZPpaWV+O5FCypusqk+sWP6C4E82/vJTnROZfUqXQ2HQoFQ5Xx9m3A3TBlbgRuh11vp//J/SdRs5t8/B4MNG+99RnS0ACi41dMq6ERzWSjfeEs5mV23PipHx6/Kwc3wH8m8fq3zcE3qalkbq6kfkVshHWznzEZl5dIDwW3+w9g2biLXfETPS5l4YGc1l98Zhoiy',  // pragma: allowlist secret
        client_id: 'AgBMXgAFGLVJlbXkAsQJksFXFaY4hUy9kdtsBLr09Ap2peJUNnUW92O4LusYEYW5DeaUKP3Li52RlqcdOjgHKpA+GVLBjDuQ/qYnjO++6pWI/8QY8Rgq/1vUtSvRKD2PL8NMPhTbr/kYXaOJSD+hwRcCWBUpMqS4lsYb8gUeldCSDG1eM+KFGB7u8sHelzoOxRUQ4cRoCcHHJpLYjWy9xeK/YVwLops4PsXGZg1IXWD7f70j0o8EUyZjHyowaWMEaMVlccIscIT2k7/7MC+cirXg7Em+AvCGFEs1dYnG5k8JitHySQej+iGQI1V0025WxdZmIlukeCrXCjsKyNtlwO65WAuHOzZ30AzGKLISkEc7ehZVpTIHgdbENBEDbLnsT3DQfhMfn96E+kyofbFVsrjb97bmiMsP/OXGkoSqdpc61ust5MDLv03JmY8G8prmnCagqibiDeFF3fsIH0d3Vssm5wtppISW9nxTHwzoEpqnN0YFlZ01oD3IrtbV5yjQFi+XdtrGDJJKiYlnfCdQAz9944qd3qj/6PpkZRFHtpqyJhPFxT/lnxVX8Jw7NHeYqg3DY1U1bFhtw4VrENM0FGEtj0eb3V1y+NpGiX8ROKLXEr9r+uIvXcQ7gBpUdkSpBFAJeC0uYLtDasGOXhO2xb+Uf0Y/ylG3Y8U1YvqN30gi/vMeRFBQZFH/Rm/s41rdsde6/5DroMkU+aiv39Ecp/0mB3tY7Q==',  // pragma: allowlist secret
        client_secret: 'AgCxm51+zbNqPQq5jW3MCyMFgyqplSbhjLQtlIOg7nNw3soKluMcje+qwFXDpaf+L06LQLlrZaL75W0rVyWyJYJ4nZelyzc3MEtyce0KXcpozJKbIGqZR83wDmRAA4odbd2BNclsLvHNkgr5U3iDgIxdVynnsQwDLAucscmOadHjKC/vR2H63nJEPG00dQfRjXnEeeS4AIK1EnAyEK7aeDxF4K9XOuk+IyVeaPWqLfxMw4YcYoyTenKrZIWQMy4HBZ3bxWheFlcG1H+zij/MLVNCpyYGxLcgHM2WYJzLmYa18WfnzPVMHnlZUMNseIwLfpC2+gqw4R02VglI3S/4qevjxzfGMf9BrjDWrez09EGJ5+T+8zKdX8ldyuUuloe5o9aqkvxmI2pd+i8pQ87TdjYHEqinavs078NmVxvmoA1fNuju8U+1VFUekV8K5NBXbVQl37zNa/R4wywlJorJNuXWQro9sLwxc/MF1TQsMubGgSBRJc0ysJ9hLBg94H1zlD1pKs/doXw0IfkXfmIHtnxmypSM0AT4/wsQtLnMkx2HzFnWop7lYwhaB4DtVAIOpf/HBTFt02N9YAJ7DTilCkMHfPqdDPEPNfaqlTBdAkXiG4oSXnqWyobFZtndfu4l43faMNmfA5WP0COeLI/PRAodtwdYzQ7PmJiy5klSehZ2ZruJdEzrr/CQO2/MkK5vhTTO86D/WdbCXGIqqIipfnar6+Z32AUmgIbvpvNSXxhpH65+CKwfHdWr',  // pragma: allowlist secret
      },
    },
  }[cloud][env];

  local appSecret = {
    name: name,
    objects: [{
      kind: 'SealedSecret',
      apiVersion: 'bitnami.com/v1alpha1',
      metadata: {
        name: name,
        namespace: namespace,
        creationTimestamp: null,
        annotations: {
          'sealedsecrets.bitnami.com/cluster-wide': 'true',
        },
        labels: {
          app: appName,
        },
      },
      spec: {
        template: {
          metadata: {
            name: name,
            namespace: namespace,
            creationTimestamp: null,
            annotations: {
              'sealedsecrets.bitnami.com/cluster-wide': 'true',
            },
          },
          type: 'Opaque',
        },
        encryptedData: if useSecretsOverride then secretsOverride
        else data,
      },
    }],
  };
  appSecret

package main

import (
	"testing"
	"time"

	"github.com/google/go-github/v64/github"
	"github.com/prometheus/client_golang/prometheus"
	dto "github.com/prometheus/client_model/go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/types/known/timestamppb"

	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

func TestLagTrackerImpl_updateLagMetrics(t *testing.T) {
	mockGithubState := new(MockGithubStateClient)
	mockTokenExchange := new(MockTokenExchangeClient)
	mockWebhookTenantMappingResource := new(MockWebhookTenantMappingResource)

	lagTracker := &LagTrackerImpl{
		githubStateClient:            mockGithubState,
		tokenExchangeClient:          mockTokenExchange,
		webhookTenantMappingResource: mockWebhookTenantMappingResource,
	}

	// Set up mock responses
	tenantID := "test-tenant"
	repoOwner := "test-owner"
	repoName := "test-repo"
	ref := "main"

	latestCommitTime := time.Now()
	lastUploadedTime := latestCommitTime.Add(-1 * time.Hour)
	lastIndexedTime := latestCommitTime.Add(-2 * time.Hour)

	mockGithubRepoClient := new(MockGithubRepositoriesClient)
	mockGithubAppClient := new(MockGithubAppServiceClient)

	mockGithubClient := GithubClient{
		Repositories: mockGithubRepoClient,
		App:          mockGithubAppClient,
	}

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, tenantID, []tokenexchangeproto.Scope{tokenexchangeproto.Scope_CONTENT_RW}).Return("mock-token", nil)

	mockGithubAppClient.On("ListRepos", mock.Anything, mock.Anything, mock.Anything).Return(
		&github.ListRepositories{
			TotalCount: github.Int(1),
			Repositories: []*github.Repository{
				{
					Owner: &github.User{
						Login: github.String(repoOwner),
					},
					Name:          github.String(repoName),
					FullName:      github.String(repoOwner + "/" + repoName),
					DefaultBranch: github.String(ref),
				},
			},
		},
		&github.Response{},
		nil,
	)

	mockGithubRepoClient.On("GetCommit", mock.Anything, repoOwner, repoName, ref, mock.Anything).Return(
		&github.RepositoryCommit{
			Commit: &github.Commit{
				Author: &github.CommitAuthor{
					Date: &github.Timestamp{Time: latestCommitTime},
				},
			},
		},
		&github.Response{},
		nil,
	)

	// Mock for GetCurrentRefStates
	mockGithubState.On("GetCurrentRefStates", mock.Anything, mock.Anything).Return([]*githubstatepersistproto.RefCheckpoint{
		{
			Ref: &githubstatepersistproto.GithubRef{
				Repo: &githubstatepersistproto.GithubRepo{
					RepoOwner: repoOwner,
					RepoName:  repoName,
				},
				Ref: ref,
			},
			CommitSha:  "lastUploadedSha",
			CommitTime: timestamppb.New(lastUploadedTime),
		},
	}, nil)

	// Mock for GetIndexedRefCheckpoints
	mockGithubState.On("GetIndexedRefCheckpoints", mock.Anything, mock.Anything).Return([]*githubstatepersistproto.RefCheckpoint{
		{
			Ref: &githubstatepersistproto.GithubRef{
				Repo: &githubstatepersistproto.GithubRepo{
					RepoOwner: repoOwner,
					RepoName:  repoName,
				},
				Ref: ref,
			},
			CommitSha:  "lastIndexedSha",
			CommitTime: timestamppb.New(lastIndexedTime),
		},
	}, nil)

	// Run the method
	lagTracker.updateLagMetrics(tenantID, mockGithubClient)

	redactedRepoString := githublib.RedactString(repoOwner + "/" + repoName)
	redactedRefString := githublib.RedactString(ref)

	// Check if metrics were updated correctly
	uploadLag := getGaugeValue(t, uploadLagGauge, tenantID, redactedRepoString, redactedRefString)
	indexLag := getGaugeValue(t, indexLagGauge, tenantID, redactedRepoString, redactedRefString)

	assert.InDelta(t, 3600, uploadLag, 1, "Upload lag should be around 1 hour")
	assert.InDelta(t, 7200, indexLag, 1, "Index lag should be around 2 hours")
}

func getGaugeValue(t *testing.T, gauge *prometheus.GaugeVec, labelValues ...string) float64 {
	metric, err := gauge.GetMetricWithLabelValues(labelValues...)
	assert.NoError(t, err, "Error getting metric value")

	// Use a dto.Metric to capture the value
	pb := &dto.Metric{}
	err = metric.Write(pb)
	assert.NoError(t, err, "Error writing metric to protobuf")

	if pb.Gauge == nil {
		t.Fatalf("Unexpected metric type, expected Gauge")
	}
	return *pb.Gauge.Value
}

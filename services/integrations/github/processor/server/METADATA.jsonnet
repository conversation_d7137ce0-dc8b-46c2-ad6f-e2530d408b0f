local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'github-processor-keyring',
      kubecfg: {
        target: '//services/integrations/github/processor/server:keyring_kubecfg',
        task: cloudInfo.centralNamespaces + [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'STAGING',
            namespace: 'central-dev',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'github-processor',
      kubecfg: {
        target: '//services/integrations/github/processor/server:kubecfg',
        task: [t for t in tenantNamespaces.namespaces],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
    {
      name: 'github-processor-monitoring',
      kubecfg: {
        target: '//services/integrations/github/processor/server:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['aswin', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

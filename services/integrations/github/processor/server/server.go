package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	githubproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
	"github.com/augmentcode/augment/services/integrations/github/processor/processorpb"
	httpclient "github.com/augmentcode/augment/services/integrations/lib"
	"github.com/augmentcode/augment/services/integrations/webhookmapping"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpcservice "github.com/augmentcode/augment/services/lib/grpc/service"
	"github.com/augmentcode/augment/services/lib/pubsub"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	settingsservice "github.com/augmentcode/augment/services/settings/client"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	"github.com/google/go-github/v64/github"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	statusproto "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var ErrNoInstallationFound = fmt.Errorf("no installation found")

const (
	maxGithubRepoResponses = 5000
	githubMaxPageSize      = 100
)

type appSecrets struct {
	clientID     secretstring.SecretString
	clientSecret secretstring.SecretString
}

type githubProcessorServer struct {
	githubAppSecrets             appSecrets
	oauthAppSecrets              appSecrets
	userAuthorizationRedirectUri string

	githubClients                githubClients
	httpClient                   *http.Client
	webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource
	lagTracker                   lagTracker
	requestInsightPublisher      ripublisher.RequestInsightPublisher
	settingsServiceClient        settingsservice.SettingsClient

	publishClient pubsub.PublishClient

	userTier publicapiproto.GetModelsResponse_UserTier

	allowUserTokenAccessPeerRegex *regexp.Regexp
}

type githubAccessToken struct {
	AccessToken                  string `json:"access_token"`
	ExpiresInSeconds             int    `json:"expires_in"`
	RefreshToken                 string `json:"refresh_token"`
	RefreshTokenExpiresInSeconds int    `json:"refresh_token_expires_in"`
	TokenType                    string `json:"token_type"`

	Error            string `json:"error,omitempty"`
	ErrorDescription string `json:"error_description,omitempty"`
	ErrorURI         string `json:"error_uri,omitempty"`
}

type githubUserInfo struct {
	Login string `json:"login"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// TODO: This is duplicated in webhook_listener/tenant_lookup.go
type value struct {
	InstallationID int64 `json:"installation_id"`
}

var githubInstallation = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_github_installation",
		Help: "Number of github installations",
	},
	[]string{"tenant_id", "status"},
)

func init() {
	prometheus.MustRegister(githubInstallation)
}

func (s *githubProcessorServer) getAccessToken(ctx context.Context, secrets appSecrets, userId, code string) (*githubAccessToken, error) {
	data := url.Values{}
	data.Set("client_id", secrets.clientID.Expose())
	data.Set("client_secret", secrets.clientSecret.Expose())
	data.Set("code", code)
	accessTokenReq, err := http.NewRequest("POST", "https://github.com/login/oauth/access_token", strings.NewReader(data.Encode()))
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create request")
		return nil, err
	}
	accessTokenReq.Header.Set("Accept", "application/json")

	accessTokenResp, err := s.githubClients.httpClient().Do(accessTokenReq)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get access token")
		return nil, err
	}
	defer accessTokenResp.Body.Close()

	var accessToken githubAccessToken
	err = json.NewDecoder(accessTokenResp.Body).Decode(&accessToken)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to decode access token")
		return nil, err
	}
	if accessToken.Error != "" {
		return nil, fmt.Errorf("error getting access token: %s, %s, %s", accessToken.Error, accessToken.ErrorDescription, accessToken.ErrorURI)
	}

	log.Ctx(ctx).Info().Msgf("Successfully got access token for user %s", userId)

	return &accessToken, nil
}

func (s *githubProcessorServer) HydrateGithubSettings(ctx context.Context, req *processorpb.HydrateGithubSettingsRequest) (*processorpb.HydrateGithubSettingsResponse, error) {
	if s.userTier != publicapiproto.GetModelsResponse_ENTERPRISE_TIER {
		log.Ctx(ctx).Error().Msgf("github app installation is not supported in this tier: %s", s.userTier)
		return nil, fmt.Errorf("github app installation is not supported in this tier: %s", s.userTier)
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %s", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	namespace := authInfo.ShardNamespace

	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)

	// We normally don't log user IDs, but knowing who installed an integration
	// is particularly useful
	log.Ctx(ctx).Info().Msgf("HydrateGithubSettings from user %s", authInfo.UserID)

	tenantInfo := &requestinsightproto.TenantInfo{
		TenantId:   authInfo.TenantID,
		TenantName: authInfo.TenantName,
	}
	riEvent := ripublisher.NewRequestEvent()
	installEvent := &requestinsightproto.RIGithubAppInstallationEvent{
		Status:    &statusproto.Status{},
		EventType: requestinsightproto.InstallEventType_INSTALL,
	}
	riEvent.Event = &requestinsightproto.RequestEvent_GithubAppInstallationEvent{
		GithubAppInstallationEvent: installEvent,
	}
	metadataEvent := ripublisher.NewRequestEvent()
	metadataEvent.Event = &requestinsightproto.RequestEvent_RequestMetadata{
		RequestMetadata: &requestinsightproto.RequestMetadata{
			// TODO: add a request type?
			// RequestType: requestinsightproto.RequestType_GITHUB_INSTALL,
			SessionId: requestContext.RequestSessionId.String(),
			UserId:    authInfo.UserID,
			UserAgent: "github-processor",
		},
	}
	events := []*requestinsightproto.RequestEvent{metadataEvent, riEvent}

	resp, err := s.hydrateGithubSettingsInner(ctx, req, tenantID, authInfo.TenantName, namespace, authInfo.UserID)

	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to install github app")
		githubInstallation.WithLabelValues(tenantID, "ERROR").Inc()
		installEvent.Status.Code = int32(status.Code(err))
		installEvent.Status.Message = err.Error()
	} else {
		githubInstallation.WithLabelValues(tenantID, "SUCCESS").Inc()
		installEvent.Status.Code = int32(codes.OK)
		installEvent.Status.Message = "Success"
	}

	riErr := s.requestInsightPublisher.PublishRequestEvents(ctx, requestContext.RequestId.String(), tenantInfo, events)
	if riErr != nil {
		log.Ctx(ctx).Error().Err(riErr).Msg("Failed to publish request event")
		// we ignore this error as there is already a (potentially) more important error
	}

	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (s *githubProcessorServer) hydrateGithubSettingsInner(ctx context.Context, req *processorpb.HydrateGithubSettingsRequest, tenantID, tenantName, namespace, userId string) (*processorpb.HydrateGithubSettingsResponse, error) {
	code := req.GetCode()
	installationID := req.GetInstallationId()

	// Get a user access token for the user who is installing the app. Use this
	// to verify that this user actually has access to this repo and can install
	// it for this tenant.
	// (https://docs.github.com/en/apps/creating-github-apps/registering-a-github-app/about-the-setup-url)

	// First, get a user access token
	// https://docs.github.com/en/apps/creating-github-apps/authenticating-with-a-github-app/generating-a-user-access-token-for-a-github-app#generating-a-user-access-token-when-a-user-installs-your-app
	accessToken, err := s.getAccessToken(ctx, s.githubAppSecrets, userId, code)
	if err != nil {
		return nil, err
	}

	err = s.findGithubInstallationForUser(*accessToken, ctx, installationID, false)
	if err != nil {
		return nil, fmt.Errorf("user does not have access to installation_id")
	}

	// We are authorized to associate this github app installation with this
	// augment tenant! Go ahead and set that up now.

	// First, check if we already have a github org installed for this tenant,
	// we currently only support one installation per tenant.
	mappings, err := s.webhookTenantMappingResource.List(ctx, "github-webhook")
	if err != nil {
		log.Ctx(ctx).Err(err).Msg("Failed to list webhook tenant mappings")
		return nil, err
	}

	for _, mapping := range mappings {
		if mapping.Spec.TenantID == tenantID {
			return nil, fmt.Errorf("tenant already has a github installation")
		}
	}

	// Try to get the list of repos for this installation and update the tenant's settings.
	githubClient := s.githubClients.installationClient(tenantName, installationID)

	// TODO(aswin): if this is a self-serve tenant, fail, we only support github
	// apps for enterprise tenants.
	repoInfos, err := s.updateGithubTenantSettings(ctx, githubClient, installationID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to update github settings for tenant %s", tenantID)
		return nil, err
	}

	// Initialize the webhook mapping, after which we should start accepting
	// events for this installation.
	// Intentionally doing this last so we don't create a webhook on an unsuccessful install.
	value := value{InstallationID: installationID}
	webhookValue, err := json.Marshal(value)
	if err != nil {
		return nil, fmt.Errorf("error marshalling value: %w", err)
	}

	_, err = s.webhookTenantMappingResource.Update(ctx,
		&webhookmapping.WebhookTenantMappingSpec{
			WebhookType:  "github",
			WebhookValue: string(webhookValue),
			TenantID:     tenantID,
		},
		fmt.Sprintf("github-%s", tenantID),
		"github-webhook",
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create webhook mapping")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Successfully set up webhook mapping for tenant %s (installation_id=%d)", tenantID, installationID)

	s.lagTracker.startTenantLagTracker(githubClient, tenantID)

	// Give the published event a new request ID, separate from the hydrate
	// call's request ID
	requestId := requestcontext.NewRandomRequestId().String()

	event := &githubproto.GithubEvent{
		Metadata: &githubproto.EventMetadata{
			InstallationId: installationID,
			TenantId:       tenantID,
			TenantName:     tenantName,
			RequestId:      requestId,
		},
		EventType: "installation",
		Event: &githubproto.GithubEvent_Installation{
			Installation: &githubproto.InstallationEvent{
				Action: "added",
				Repos:  repoInfos,
			},
		},
	}

	eventData, err := proto.Marshal(event)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error marshalling event")
		return nil, err
	}

	err = s.publishClient.Publish(ctx, namespace, eventData)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error publishing to Pub/Sub")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Published event installation event with %d repos for tenant %s to pubsub in namespace %s", len(repoInfos), tenantID, namespace)

	return &processorpb.HydrateGithubSettingsResponse{}, nil
}

func (s *githubProcessorServer) updateGithubTenantSettings(ctx context.Context, githubClient GithubClient, installationID int64) (repoInfos []*githubproto.Repository, err error) {
	var allRepos []*github.Repository
	opt := &github.ListOptions{
		PerPage: 100, // GitHub's max per page
	}

	for {
		repos, resp, err := githubClient.App.ListRepos(ctx, opt)
		if err != nil {
			return nil, fmt.Errorf("failed to get repositories for installation: %w", err)
		}
		allRepos = append(allRepos, repos.Repositories...)

		if resp.NextPage == 0 {
			break
		}
		opt.Page = resp.NextPage
	}

	log.Ctx(ctx).Info().Msgf("Found %d repos for installation %d", len(allRepos), installationID)

	// we should always have at least one repo installed for the app
	// no accessible repos could be a sign of a github enterprise server installation
	if len(allRepos) == 0 {
		return nil, fmt.Errorf("no repos found for installation")
	}

	// update the tenant's settings with repos installed
	// note that the user token already has settings r/w scope, so we
	// can just pass the existing context through
	outgoingRequestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %w", err)
	}

	tenantSettingsResponse, err := s.settingsServiceClient.GetTenantSettings(ctx, outgoingRequestContext)
	if err != nil {
		return nil, fmt.Errorf("Error getting tenant settings: %w", err)
	}
	currentSettings := tenantSettingsResponse.GetSettings()
	// go proto unmarshalling sets empty fields to nil bc omitempty
	if currentSettings.GithubSettings == nil {
		currentSettings.GithubSettings = &settingsproto.GithubSettings{}
	}

	settingsRepoInfos := make([]*settingsproto.RepoInformation, 0, len(allRepos))
	eventRepoInfos := make([]*githubproto.Repository, 0, len(allRepos))

	for _, repo := range allRepos {
		settingRepoInfo := &settingsproto.RepoInformation{
			RepoOwner: repo.GetOwner().GetLogin(),
			RepoName:  repo.GetName(),
		}
		settingsRepoInfos = append(settingsRepoInfos, settingRepoInfo)
		eventRepoInfo := &githubproto.Repository{
			Owner: repo.GetOwner().GetLogin(),
			Name:  repo.GetName(),
		}
		eventRepoInfos = append(eventRepoInfos, eventRepoInfo)
	}

	currentSettings.GithubSettings.Repos = settingsRepoInfos
	currentSettings.GithubSettings.InstallationId = installationID

	_, err = s.settingsServiceClient.UpdateTenantSettings(ctx, outgoingRequestContext, currentSettings, tenantSettingsResponse.Version)
	if err != nil {
		return nil, fmt.Errorf("Error updating tenant github settings: %w", err)
	}

	log.Ctx(ctx).Info().Msgf("Successfully updated tenant github settings")

	return eventRepoInfos, nil
}

func (s *githubProcessorServer) HydrateGithubUserSettings(ctx context.Context, req *processorpb.HydrateGithubUserSettingsRequest) (*processorpb.HydrateGithubUserSettingsResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	userId := authInfo.UserID
	log.Ctx(ctx).Info().Msgf("HydrateGithubUserSettings for user %s", userId)

	// Exchange the code for an access token
	accessToken, err := s.getAccessToken(ctx, s.oauthAppSecrets, userId, req.GetCode())
	if err != nil {
		return nil, err
	}

	// Get the user's info
	userInfo, err := s.getGithubUserInfo(ctx, *accessToken)
	if err != nil {
		// No need to error out if we cannot get user info
		// But we do need to log it
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get github user info")
		// Create an empty userInfo to avoid nil pointer dereference
		userInfo = &githubUserInfo{}
	}

	// Save the access token to the user's settings
	err = s.updateGithubUserSettings(ctx, requestContext, *accessToken, *userInfo)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to update github user settings")
		return nil, err
	}

	return &processorpb.HydrateGithubUserSettingsResponse{}, nil
}

func (s *githubProcessorServer) updateGithubUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, accessToken githubAccessToken, githubUserInfo githubUserInfo) error {
	outgoingRequestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return fmt.Errorf("Error getting outgoing request context: %w", err)
	}

	userSettingsResponse, err := s.settingsServiceClient.GetUserSettings(ctx, outgoingRequestContext, nil)
	if err != nil {
		return fmt.Errorf("Error getting user settings: %w", err)
	}

	now := time.Now()
	accessTokenExpiration := now.Add(time.Duration(accessToken.ExpiresInSeconds) * time.Second)
	refreshTokenExpiration := now.Add(time.Duration(accessToken.RefreshTokenExpiresInSeconds) * time.Second)

	newSettings := &settingsproto.UserSettings{
		GithubUserSettings: &settingsproto.GithubUserSettings{
			AccessToken:            accessToken.AccessToken,
			AccessTokenExpiration:  timestamppb.New(accessTokenExpiration),
			RefreshToken:           accessToken.RefreshToken,
			RefreshTokenExpiration: timestamppb.New(refreshTokenExpiration),
			TokenType:              accessToken.TokenType,
			GithubLogin:            githubUserInfo.Login,
			GithubName:             githubUserInfo.Name,
			GithubEmail:            githubUserInfo.Email,
		},
	}

	_, err = s.settingsServiceClient.UpdateUserSettings(ctx, outgoingRequestContext, nil, newSettings, userSettingsResponse.Version)
	if err != nil {
		return fmt.Errorf("Error updating user settings: %w", err)
	}

	log.Ctx(ctx).Info().Msgf("Successfully updated user github settings")

	return nil
}

// Use a user access token to see if the user and the app both have
// access to this installation ID by calling the GitHub API.
// Retries once if the installation ID is not found.
//
// Pass anyTargetInstallation as true if you want to ignore targetInstallationID
// and check if the user token has access to any installation ID.
func (s *githubProcessorServer) findGithubInstallationForUser(accessToken githubAccessToken, ctx context.Context, targetInstallationID int64, anyTargetInstallation bool) error {
	const maxRetries = 1
	const retryDelay = time.Second * 10

	var err error
	for retries := 0; retries < maxRetries; retries++ {
		err = s.checkGithubInstallationIDs(ctx, accessToken, targetInstallationID, anyTargetInstallation)
		if err == nil {
			return nil
		}
		time.Sleep(retryDelay)
		continue
	}
	return err
}

func (s *githubProcessorServer) checkGithubInstallationIDs(ctx context.Context, accessToken githubAccessToken, targetInstallationID int64, anyTargetInstallation bool) (err error) {
	const perPage = 30

	page := 1
	const getInstallationsURL = "https://api.github.com/user/installations"

	for {
		var installationsResponse struct {
			Installations []struct {
				ID int64 `json:"id"`
			} `json:"installations"`
		}

		req, err := http.NewRequestWithContext(ctx, "GET", getInstallationsURL, nil)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to create request")
			return fmt.Errorf("failed to create request: %w", err)
		}

		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken.AccessToken))
		req.Header.Set("Accept", "application/vnd.github+json")
		req.Header.Set("X-GitHub-Api-Version", "2022-11-28")

		q := req.URL.Query()
		q.Add("per_page", fmt.Sprintf("%d", perPage))
		q.Add("page", fmt.Sprintf("%d", page))
		req.URL.RawQuery = q.Encode()

		resp, err := s.httpClient.Do(req)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Request failed")
			return fmt.Errorf("request failed: %w", err)
		}
		defer resp.Body.Close()

		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to read response body")
			return fmt.Errorf("failed to read response body: %w", err)
		}

		err = json.Unmarshal(body, &installationsResponse)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to parse response body")
			return fmt.Errorf("failed to parse response body: %w", err)
		}
		for _, inst := range installationsResponse.Installations {
			if anyTargetInstallation || inst.ID == targetInstallationID {
				return nil
			}
		}

		if len(installationsResponse.Installations) < perPage {
			// No more pages to fetch
			return ErrNoInstallationFound
		}

		page++
	}
}

func (s *githubProcessorServer) getGithubUserInfo(ctx context.Context, accessToken githubAccessToken) (*githubUserInfo, error) {
	client := github.NewClient(s.httpClient).WithAuthToken(accessToken.AccessToken)
	user, _, err := client.Users.Get(ctx, "")
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to make request to get github user info")
		return nil, fmt.Errorf("Error making request: %w", err)
	}

	userInfo := &githubUserInfo{}
	if user.Login != nil {
		userInfo.Login = *user.Login
	}
	if user.Name != nil {
		userInfo.Name = *user.Name
	}
	if user.Email != nil {
		userInfo.Email = *user.Email
	}

	emails, resp, err := client.Users.ListEmails(ctx, &github.ListOptions{})
	if err == nil {
		// Get the primary email as the default
		for _, email := range emails {
			if email.Primary != nil && *email.Primary {
				userInfo.Email = *email.Email
				break
			}
		}
	} else if resp.StatusCode == http.StatusNotFound || resp.StatusCode == http.StatusForbidden || resp.StatusCode == http.StatusUnauthorized {
		// We don't have permission to see emails, maybe the user has
		// installed the old version of the app that only has "repos" scope
		// and not "user:email"
	} else {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to make request to get github user emails")
		return nil, fmt.Errorf("Error making request: %w", err)
	}

	return userInfo, nil
}

func (s *githubProcessorServer) getGitHubUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext) (*settingsproto.GithubUserSettings, error) {
	outgoingRequestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting outgoing request context: %w", err)
	}

	userSettingsResponse, err := s.settingsServiceClient.GetUserSettings(ctx, outgoingRequestContext, nil)
	if err != nil {
		return nil, fmt.Errorf("Error getting user settings: %w", err)
	}

	if userSettingsResponse.Settings == nil || userSettingsResponse.Settings.GithubUserSettings == nil {
		return nil, nil
	}

	return userSettingsResponse.Settings.GithubUserSettings, nil
}

// CallGithubApi calls the GitHub API with the given request
// Try our best to forward response out and not error out if possible
// so that response codes and body can be inspected by the caller
func (s *githubProcessorServer) CallGithubApi(ctx context.Context, req *processorpb.GithubApiRequest) (*processorpb.GithubApiResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID

	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)

	log.Ctx(ctx).Info().Msg("Calling github API")

	var token string
	if req.Token != nil && *req.Token != "" {
		// Use provided token if it exists
		token = *req.Token
	} else {
		// Get token from settings
		settings, err := s.getGitHubUserSettings(ctx, requestContext)
		if err != nil {
			return nil, err
		}
		if settings == nil || settings.AccessToken == "" {
			log.Ctx(ctx).Error().Msg("Unauthenticated. No github token passed in nor stored in user settings")
			return &processorpb.GithubApiResponse{
				StatusCode: int32(http.StatusUnauthorized),
				Response:   "Unauthenticated - no github access token found",
			}, nil
		}
		token = settings.AccessToken
	}

	var requestBody io.Reader

	// Handle data based on method
	if req.Method == processorpb.HTTPMethod_GET && req.Data != nil {
		parsedURL, err := url.Parse(req.Path)
		if err != nil {
			return nil, fmt.Errorf("Error parsing path: %w", err)
		}

		q := parsedURL.Query()
		// Convert Struct fields directly to query parameters
		for k, v := range req.Data.Fields {
			q.Add(k, v.GetStringValue())
		}

		parsedURL.RawQuery = q.Encode()
		req.Path = parsedURL.String()
	} else if req.Data != nil {
		// For POST, convert Struct directly to JSON
		jsonData, err := protojson.Marshal(req.Data)
		if err != nil {
			return nil, fmt.Errorf("Error marshalling request body: %w", err)
		}
		requestBody = bytes.NewReader(jsonData)
	}

	// Create HTTP client with token and refresh callback
	client := httpclient.NewBaseHTTPClient(
		s.httpClient,
		"https://api.github.com",
		token,
	)

	// Convert enum to string
	methodStr := req.Method.String()

	// Make the request
	resp, err := client.MakeRequest(ctx, req.Path, methodStr, requestBody)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to make request")

		// Check if we have a response with an HTTP status code
		if resp != nil {
			// We have a response, so read the body and return it with the status code
			defer resp.Body.Close()
			body, readErr := io.ReadAll(resp.Body)
			if readErr != nil {
				log.Ctx(ctx).Error().Err(readErr).Msg("Failed to read error response body")
				// Even if we can't read the body, still return the status code and original error
				return &processorpb.GithubApiResponse{
					Response:   err.Error(),
					StatusCode: int32(resp.StatusCode),
				}, nil
			}

			// Return the response with the status code and include the error message
			return &processorpb.GithubApiResponse{
				Response:   fmt.Sprintf("Error: %s\n\n%s", err, string(body)),
				StatusCode: int32(resp.StatusCode),
			}, err
		} else {
			// Network errors or other non-HTTP errors
			return nil, status.Errorf(codes.Unavailable, "Error connecting to GitHub API: %s", err)
		}
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read response body")
		// Even if we can't read the body, still return the status code
		return &processorpb.GithubApiResponse{
			Response:   fmt.Sprintf("Error reading response body: %s", err),
			StatusCode: int32(resp.StatusCode),
		}, nil
	}

	return &processorpb.GithubApiResponse{
		Response:   string(body),
		StatusCode: int32(resp.StatusCode),
	}, nil
}

// generateGithubOAuthURL creates the GitHub OAuth authorization URL
func (s *githubProcessorServer) generateGithubOAuthURL() string {
	baseURL := "https://github.com/login/oauth/authorize"
	params := url.Values{}
	params.Add("client_id", s.oauthAppSecrets.clientID.Expose())
	params.Add("redirect_uri", s.userAuthorizationRedirectUri)
	params.Add("scope", "repo user:email")

	return fmt.Sprintf("%s?%s", baseURL, params.Encode())
}

func (s *githubProcessorServer) IsUserOAuthConfigured(ctx context.Context, req *processorpb.IsUserOAuthConfiguredRequest) (*processorpb.IsUserOAuthConfiguredResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Unauthenticated")
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		log.Ctx(ctx).Error().Msg("tenant_id is required")
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)

	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get github user settings")
		return nil, err
	}

	isConfigured := settings != nil && settings.AccessToken != ""
	configuredButNeedsUpdate := false
	updatedScopes := ""

	// Generate OAuth URL if not configured
	var oauthURL string
	if !isConfigured {
		oauthURL = s.generateGithubOAuthURL()
	} else {
		// Check what scopes the token has
		// If it doesn't have user:email, we need to re-auth, because it was added later on
		// Create a GitHub client using the user's token
		client := github.NewClient(s.httpClient).WithAuthToken(settings.AccessToken)

		// Get the scopes from the header in a basic request
		// https://docs.github.com/en/apps/oauth-apps/building-oauth-apps/scopes-for-oauth-apps
		_, resp, err := client.Users.Get(ctx, "")
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to get user scopes")
			if resp != nil && resp.StatusCode == http.StatusUnauthorized {
				// Token is invalid, so clear it
				err = s.clearGithubUserSettings(ctx, requestContext)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Msg("Failed to clear github user settings")
					// ignore this error, it's more important to return the correct response
				}
				return &processorpb.IsUserOAuthConfiguredResponse{
					IsConfigured: false,
					OauthUrl:     s.generateGithubOAuthURL(),
				}, nil
			}
			return nil, err
		}
		defer resp.Body.Close()

		scopes := resp.Header.Values("X-OAuth-Scopes")
		foundUserEmailScope := false
		for _, scope := range scopes {
			if scope == "user:email" {
				foundUserEmailScope = true
				break
			}
		}
		if !foundUserEmailScope {
			configuredButNeedsUpdate = true
			updatedScopes = "user:email"
		}
	}

	return &processorpb.IsUserOAuthConfiguredResponse{
		IsConfigured:             isConfigured,
		OauthUrl:                 oauthURL,
		ConfiguredButNeedsUpdate: configuredButNeedsUpdate,
		UpdatedScopes:            updatedScopes,
	}, nil
}

func (s *githubProcessorServer) GetGithubUserOAuthUrl(ctx context.Context, req *processorpb.GetGithubUserOAuthUrlRequest) (*processorpb.GetGithubUserOAuthUrlResponse, error) {
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	oauthURL := s.generateGithubOAuthURL()

	return &processorpb.GetGithubUserOAuthUrlResponse{
		OauthUrl: oauthURL,
	}, nil
}

func (s *githubProcessorServer) clearGithubUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext) error {
	outgoingRequestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return fmt.Errorf("Error getting outgoing request context: %w", err)
	}

	// Create new settings with GitHub settings cleared
	newSettings := &settingsproto.UserSettings{
		// Set an empty GitHub user settings object to clear the existing settings
		GithubUserSettings: &settingsproto.GithubUserSettings{},
	}

	// Pass an empty version string to bypass version checking
	// This ensures the settings are always cleared regardless of concurrent updates
	_, err = s.settingsServiceClient.UpdateUserSettings(ctx, outgoingRequestContext, nil, newSettings, "")
	if err != nil {
		return fmt.Errorf("Error updating user settings: %w", err)
	}

	log.Ctx(ctx).Info().Msg("Successfully cleared user GitHub settings")
	return nil
}

// GetGithubUserInfo retrieves GitHub user information for the authenticated user
func (s *githubProcessorServer) GetGithubUserInfo(ctx context.Context, request *processorpb.GetGithubUserInfoRequest) (*processorpb.GetGithubUserInfoResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	log.Ctx(ctx).Info().Msg("Getting GitHub user info")

	// Get the user's GitHub settings to retrieve the access token
	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user settings")
		return nil, err
	}

	if settings == nil || settings.AccessToken == "" {
		return nil, status.Error(codes.Unauthenticated, "GitHub OAuth is not configured")
	}

	accessToken := githubAccessToken{
		AccessToken: settings.AccessToken,
		TokenType:   "bearer",
	}

	// Get the user's info using the access token - do this to ensure we always have the latest info
	userInfo, err := s.getGithubUserInfo(ctx, accessToken)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user info")
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to get GitHub user info: %s", err))
	}

	// Return the user info
	return &processorpb.GetGithubUserInfoResponse{
		Login: userInfo.Login,
		Email: userInfo.Email,
		Name:  userInfo.Name,
	}, nil
}

func (s *githubProcessorServer) RevokeOAuthGrant(ctx context.Context, request *processorpb.RevokeOAuthGrantRequest) (*processorpb.RevokeOAuthGrantResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %s", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	log.Ctx(ctx).Info().Msg("Revoking OAuth grant")

	// Get the user's GitHub settings to retrieve the access token
	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user settings")
		return nil, err
	}

	if settings == nil || settings.AccessToken == "" {
		log.Ctx(ctx).Info().Msg("No GitHub access token found to revoke")
		return &processorpb.RevokeOAuthGrantResponse{
			Status: &statusproto.Status{
				Code:    int32(codes.AlreadyExists),
				Message: "No GitHub access token found to revoke",
			},
		}, nil
	}

	// Prepare the request to revoke the OAuth grant
	revokeURL := fmt.Sprintf("https://api.github.com/applications/%s/grant", s.oauthAppSecrets.clientID.Expose())

	// Create the request body with the access token
	requestBody := map[string]string{
		"access_token": settings.AccessToken,
	}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to marshal request body")
		return &processorpb.RevokeOAuthGrantResponse{
			Status: &statusproto.Status{
				Code:    int32(codes.Internal),
				Message: fmt.Sprintf("Failed to prepare request: %v", err),
			},
		}, nil
	}

	// Create the HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "DELETE", revokeURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create HTTP request")
		return &processorpb.RevokeOAuthGrantResponse{
			Status: &statusproto.Status{
				Code:    int32(codes.Internal),
				Message: fmt.Sprintf("Failed to create request: %v", err),
			},
		}, nil
	}

	// Set required headers
	httpReq.Header.Set("Accept", "application/vnd.github+json")
	httpReq.Header.Set("X-GitHub-Api-Version", "2022-11-28")

	// Set basic auth with client ID and client secret
	httpReq.SetBasicAuth(s.oauthAppSecrets.clientID.Expose(), s.oauthAppSecrets.clientSecret.Expose())

	// Send the request
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to send revoke request")
		return &processorpb.RevokeOAuthGrantResponse{
			Status: &statusproto.Status{
				Code:    int32(codes.Unavailable),
				Message: fmt.Sprintf("Failed to send request: %v", err),
			},
		}, nil
	}
	defer resp.Body.Close()

	// Check the response status
	var apiErrorMessage string
	var apiStatusCode codes.Code

	if resp.StatusCode != http.StatusNoContent {
		body, _ := io.ReadAll(resp.Body)
		log.Ctx(ctx).Error().
			Int("status_code", resp.StatusCode).
			Str("response", string(body)).
			Msg("Failed to revoke OAuth grant")

		// Map HTTP status codes to appropriate gRPC status codes based on the documented codes in agents.proto
		switch {
		// If we do not have auth to revoke, it's an internal error
		// we might still have a grant!
		case resp.StatusCode == http.StatusUnauthorized:
			apiStatusCode = codes.Internal
		case resp.StatusCode == http.StatusForbidden:
			apiStatusCode = codes.Internal
		case resp.StatusCode == http.StatusNotFound:
			// Not found is returned if the grant was already revoked
			apiStatusCode = codes.AlreadyExists
		case resp.StatusCode >= 400 && resp.StatusCode < 500:
			apiStatusCode = codes.Internal
		default:
			apiStatusCode = codes.Unknown
		}

		apiErrorMessage = fmt.Sprintf("GitHub API returned status code %d: %s", resp.StatusCode, string(body))
	}

	// Always attempt to clear the user's GitHub settings, regardless of API response
	err = s.clearGithubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to clear GitHub user settings")
		return &processorpb.RevokeOAuthGrantResponse{
			Status: &statusproto.Status{
				Code:    int32(codes.Internal),
				Message: fmt.Sprintf("Grant revocation attempt made but failed to clear settings: %v", err),
			},
		}, nil
	}

	// If we had an API error but successfully cleared settings, return that status
	if apiErrorMessage != "" {
		log.Ctx(ctx).Info().Msg("API call failed but successfully cleared GitHub settings")
		return &processorpb.RevokeOAuthGrantResponse{
			Status: &statusproto.Status{
				Code:    int32(apiStatusCode),
				Message: fmt.Sprintf("%s (settings were still cleared)", apiErrorMessage),
			},
		}, nil
	}

	// Success case - both API call succeeded and settings cleared
	log.Ctx(ctx).Info().Msg("Successfully revoked OAuth grant and cleared settings")
	return &processorpb.RevokeOAuthGrantResponse{
		Status: &statusproto.Status{
			Code:    int32(codes.OK),
			Message: "GitHub OAuth access successfully revoked",
		},
	}, nil
}

func (s *githubProcessorServer) canAccessRepo(ctx context.Context, ownerName, repoName string, token *string) (bool, error) {
	// We don't need the body, which can be quite large, so do a HEAD instead of a GET
	client := github.NewClient(s.httpClient)
	if token != nil {
		client = client.WithAuthToken(*token)
	}
	_, resp, err := client.Repositories.Get(ctx, ownerName, repoName)
	if resp != nil && resp.StatusCode == http.StatusNotFound {
		return false, nil
	}
	if err != nil {
		return false, err
	}
	return true, nil
}

func (s *githubProcessorServer) GetGithubUserToken(ctx context.Context, request *processorpb.GetGithubUserTokenRequest) (*processorpb.GetGithubUserTokenResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	log.Ctx(ctx).Info().Msg("Getting GitHub user token")

	peerNames, _, _ := grpcservice.GetAllPeerNamesAndNamespaces(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get peer names")
		return nil, err
	}

	allowed := false
	for _, peerName := range peerNames {
		if s.allowUserTokenAccessPeerRegex.MatchString(peerName) {
			allowed = true
			break
		}
	}
	if !allowed {
		log.Ctx(ctx).Error().Msgf("Failed to validate peer %v", peerNames)
		return nil, status.Error(codes.PermissionDenied, "Permission denied")
	}

	// Get the user's GitHub settings to retrieve the access token
	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user settings")
		return nil, err
	}

	if settings == nil || settings.AccessToken == "" {
		// Check if the repo is a public repo. This is a nice feature to have to
		// enable easy e2e testing, without any github credentials. We only do
		// this if we don't have credentials, because requests with no auth get
		// rate limited more aggressively
		isPublicRepo, err := s.canAccessRepo(ctx, request.Repo.Owner, request.Repo.Name, nil)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to check if repo is public")
			return nil, err
		}
		if isPublicRepo {
			// This repo is publicly accessible, so return an empty token/login name rather than failing
			return &processorpb.GetGithubUserTokenResponse{
				Token: "",
				Login: "",
			}, nil
		}
		return nil, status.Error(codes.Unauthenticated, "GitHub OAuth is not configured")
	}

	haveAccess, err := s.canAccessRepo(ctx, request.Repo.Owner, request.Repo.Name, &settings.AccessToken)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to check if repo is public")
		return nil, err
	}
	if !haveAccess {
		// We have a token but no access
		return nil, status.Error(codes.Unauthenticated, "GitHub OAuth integration does not have access to this repo")
	}

	// Get the user's info using the access token - do this to ensure we always have the latest info
	userInfo, err := s.getGithubUserInfo(ctx, githubAccessToken{
		AccessToken: settings.AccessToken,
		TokenType:   "bearer",
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user info")
		return nil, err
	}

	// Update the info we have in settings, since we already fetched it
	if settings.GithubLogin != userInfo.Login ||
		settings.GithubName != userInfo.Name ||
		settings.GithubEmail != userInfo.Email {
		err = s.updateGithubUserSettings(ctx, requestContext, githubAccessToken{
			AccessToken: settings.AccessToken,
			TokenType:   "bearer",
		}, *userInfo)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to update GitHub user settings")
			// ignore this error, it's okay if we don't save the latest user info
		}
	}

	settings.GithubLogin = userInfo.Login

	response := &processorpb.GetGithubUserTokenResponse{
		Token: settings.AccessToken,
		Login: userInfo.Login,
		Email: userInfo.Email,
		Name:  userInfo.Name,
	}
	return response, nil
}

// ListGithubReposForAuthenticatedUser lists all repositories accessible to the authenticated user
func (s *githubProcessorServer) ListGithubReposForAuthenticatedUser(ctx context.Context, request *processorpb.ListGithubReposForAuthenticatedUserRequest) (*processorpb.ListGithubReposForAuthenticatedUserResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	log.Ctx(ctx).Info().Msg("Listing GitHub repositories for authenticated user")

	// Get the user's GitHub settings to retrieve the access token
	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user settings")
		return nil, err
	}

	if settings == nil || settings.AccessToken == "" {
		return nil, status.Error(codes.Unauthenticated, "GitHub OAuth is not configured")
	}

	// Create a GitHub client using the user's token
	client := github.NewClient(s.httpClient).WithAuthToken(settings.AccessToken)

	// List all repositories for the authenticated user
	var allRepos []*github.Repository
	opt := &github.RepositoryListByAuthenticatedUserOptions{
		ListOptions: github.ListOptions{
			PerPage: 100, // GitHub's max per page
		},
	}

	for {
		repos, resp, err := client.Repositories.ListByAuthenticatedUser(ctx, opt)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to list GitHub repositories")
			return nil, fmt.Errorf("Failed to list GitHub repositories: %w", err)
		}
		allRepos = append(allRepos, repos...)
		// TODO: paginate the request
		if len(allRepos) > maxGithubRepoResponses {
			log.Ctx(ctx).Warn().Msgf("Limiting response to %d repositories out of %d total", maxGithubRepoResponses, len(allRepos))
			allRepos = allRepos[:maxGithubRepoResponses]
			break
		}
		if resp.NextPage == 0 {
			break
		}
		opt.Page = resp.NextPage
	}

	protoRepos := make([]*processorpb.GithubRepo, 0, len(allRepos))
	for _, repo := range allRepos {
		// Safely get the owner login, handling both user and organization owners
		var ownerLogin string
		owner := repo.GetOwner()
		if owner != nil {
			ownerLogin = owner.GetLogin()
		} else {
			// Fallback if owner is not available
			log.Ctx(ctx).Warn().Msgf("Owner information missing for repo %s, using fallback", repo.GetName())
			ownerLogin = "unknown"
		}
		defaultBranch := repo.GetDefaultBranch()

		// Format timestamps in ISO 8601 format
		var createdAt, updatedAt string
		if repo.GetCreatedAt().Time != (time.Time{}) {
			createdAt = repo.GetCreatedAt().Format(time.RFC3339)
		}
		if repo.GetUpdatedAt().Time != (time.Time{}) {
			updatedAt = repo.GetUpdatedAt().Format(time.RFC3339)
		}

		// Create the repo object
		protoRepo := &processorpb.GithubRepo{
			Owner:         ownerLogin,
			Name:          repo.GetName(),
			HtmlUrl:       repo.GetHTMLURL(),
			CreatedAt:     createdAt,
			UpdatedAt:     updatedAt,
			DefaultBranch: defaultBranch,
		}

		protoRepos = append(protoRepos, protoRepo)
	}

	log.Ctx(ctx).Info().Msgf("Successfully listed %d GitHub repositories", len(protoRepos))
	return &processorpb.ListGithubReposForAuthenticatedUserResponse{
		Repos: protoRepos,
	}, nil
}

// GetRepo gets a specific GitHub repository by owner and name
func (s *githubProcessorServer) GetRepo(ctx context.Context, request *processorpb.GetRepoRequest) (*processorpb.GetRepoResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	if request.Repo == nil {
		return nil, status.Error(codes.InvalidArgument, "repo is required")
	}

	if request.Repo.Owner == "" || request.Repo.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "repo owner and name are required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	log.Ctx(ctx).Info().Msgf("Getting GitHub repository")

	// Get the user's GitHub settings to retrieve the access token
	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user settings")
		return nil, err
	}

	// Create a GitHub client
	client := github.NewClient(s.httpClient)
	if settings == nil || settings.AccessToken == "" {
		// Check if the repo is a public repo. We only do this if we don't have
		// credentials, because requests with no auth get rate limited more
		// aggressively
		isPublicRepo, err := s.canAccessRepo(ctx, request.Repo.Owner, request.Repo.Name, nil)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to check if repo is public")
			return nil, err
		}
		if !isPublicRepo {
			return nil, status.Error(codes.Unauthenticated, "GitHub OAuth is not configured and repository is not public")
		}
		// For public repos, we can use an unauthenticated client
	} else {
		// For authenticated requests, use the user's token
		client = client.WithAuthToken(settings.AccessToken)
	}

	// Get the repository
	repo, resp, err := client.Repositories.Get(ctx, request.Repo.Owner, request.Repo.Name)
	if err != nil {
		if resp != nil && resp.StatusCode == http.StatusNotFound {
			return nil, status.Error(codes.NotFound, "Repository not found")
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get repository")
		return nil, fmt.Errorf("Failed to get repository: %w", err)
	}

	// Format timestamps in ISO 8601 format
	var createdAt, updatedAt string
	if repo.GetCreatedAt().Time != (time.Time{}) {
		createdAt = repo.GetCreatedAt().Format(time.RFC3339)
	}
	if repo.GetUpdatedAt().Time != (time.Time{}) {
		updatedAt = repo.GetUpdatedAt().Format(time.RFC3339)
	}

	// Create the repo object
	protoRepo := &processorpb.GithubRepo{
		Owner:         repo.GetOwner().GetLogin(),
		Name:          repo.GetName(),
		HtmlUrl:       repo.GetHTMLURL(),
		CreatedAt:     createdAt,
		UpdatedAt:     updatedAt,
		DefaultBranch: repo.GetDefaultBranch(),
	}

	log.Ctx(ctx).Info().Msgf("Successfully got GitHub repository")
	return &processorpb.GetRepoResponse{
		Repo: protoRepo,
	}, nil
}

// ListBranchesForRepo lists all branches for a specific repository
func (s *githubProcessorServer) ListBranchesForRepo(ctx context.Context, request *processorpb.ListGithubRepoBranchesRequest) (*processorpb.ListGithubRepoBranchesResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	log.Ctx(ctx).Info().Msgf("Listing branches for GitHub repository")

	// Get the user's GitHub settings to retrieve the access token
	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user settings")
		return nil, err
	}

	if settings == nil || settings.AccessToken == "" {
		return nil, status.Error(codes.Unauthenticated, "GitHub OAuth is not configured")
	}

	// Create a GitHub client using the user's token
	client := github.NewClient(s.httpClient).WithAuthToken(settings.AccessToken)

	// TODO(mike): remove in 2 weeks after frontend roll out pagination
	// if no page number is provided return all branches for backward compatibility
	if request.Page == nil || *request.Page == 0 {
		protoBranches := make([]*processorpb.GithubBranch, 0, 100)
		page := 1
		for {
			branches, resp, err := client.Repositories.ListBranches(ctx, request.Repo.Owner, request.Repo.Name, &github.BranchListOptions{
				ListOptions: github.ListOptions{
					PerPage: githubMaxPageSize,
					Page:    page,
				},
			})
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msgf("Failed to list branches for repository")
				return nil, fmt.Errorf("Failed to list branches: %w", err)
			}
			for _, branch := range branches {
				commit := branch.GetCommit()
				protoBranch := &processorpb.GithubBranch{
					Name:      branch.GetName(),
					Protected: branch.GetProtected(),
					Commit: &processorpb.GithubBranchCommit{
						Sha: commit.GetSHA(),
						Url: commit.GetURL(),
					},
				}
				protoBranches = append(protoBranches, protoBranch)
			}
			// cap at 10k branhces with 100 api calls
			if len(protoBranches) >= 10_000 {
				break
			}
			if resp.NextPage == 0 {
				break
			}
			page = resp.NextPage
		}
		return &processorpb.ListGithubRepoBranchesResponse{
			Branches: protoBranches,
		}, nil
	}

	// Using the global pageSize constant
	opt := &github.BranchListOptions{
		ListOptions: github.ListOptions{
			PerPage: githubMaxPageSize,
		},
	}
	if *request.Page > 0 {
		opt.Page = int(*request.Page)
	}

	branches, resp, err := client.Repositories.ListBranches(ctx, request.Repo.Owner, request.Repo.Name, opt)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to list branches for repository")
		return nil, fmt.Errorf("Failed to list branches: %w", err)
	}

	protoBranches := make([]*processorpb.GithubBranch, 0, len(branches))
	for _, branch := range branches {
		commit := branch.GetCommit()
		protoBranch := &processorpb.GithubBranch{
			Name:      branch.GetName(),
			Protected: branch.GetProtected(),
			Commit: &processorpb.GithubBranchCommit{
				Sha: commit.GetSHA(),
				Url: commit.GetURL(),
			},
		}
		protoBranches = append(protoBranches, protoBranch)
	}

	return &processorpb.ListGithubRepoBranchesResponse{
		Branches:    protoBranches,
		HasNextPage: resp.NextPage != 0,
		NextPage:    int32(resp.NextPage),
	}, nil
}

// Helper function to convert a GitHub pull request to proto format
func convertPullRequestToProto(pr *github.PullRequest) *processorpb.GithubPullRequest {
	var createdAt, updatedAt, mergedAt, closedAt string
	if pr.CreatedAt != nil {
		createdAt = pr.CreatedAt.Format(time.RFC3339)
	}
	if pr.UpdatedAt != nil {
		updatedAt = pr.UpdatedAt.Format(time.RFC3339)
	}
	if pr.MergedAt != nil {
		mergedAt = pr.MergedAt.Format(time.RFC3339)
	}
	if pr.ClosedAt != nil {
		closedAt = pr.ClosedAt.Format(time.RFC3339)
	}

	var user *processorpb.GithubUser
	if pr.User != nil {
		user = &processorpb.GithubUser{
			Login:     pr.User.GetLogin(),
			AvatarUrl: pr.User.GetAvatarURL(),
			HtmlUrl:   pr.User.GetHTMLURL(),
		}
	}

	return &processorpb.GithubPullRequest{
		Number:       int32(pr.GetNumber()),
		Title:        pr.GetTitle(),
		Body:         pr.GetBody(),
		State:        pr.GetState(),
		HtmlUrl:      pr.GetHTMLURL(),
		CreatedAt:    createdAt,
		UpdatedAt:    updatedAt,
		MergedAt:     mergedAt,
		ClosedAt:     closedAt,
		User:         user,
		HeadRef:      pr.GetHead().GetRef(),
		BaseRef:      pr.GetBase().GetRef(),
		HeadSha:      pr.GetHead().GetSHA(),
		BaseSha:      pr.GetBase().GetSHA(),
		Merged:       pr.GetMerged(),
		Draft:        pr.GetDraft(),
		Comments:     int32(pr.GetComments()),
		Commits:      int32(pr.GetCommits()),
		Additions:    int32(pr.GetAdditions()),
		Deletions:    int32(pr.GetDeletions()),
		ChangedFiles: int32(pr.GetChangedFiles()),
	}
}

// CreatePullRequest creates a new pull request
func (s *githubProcessorServer) CreatePullRequest(ctx context.Context, request *processorpb.CreatePullRequestRequest) (*processorpb.CreatePullRequestResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("Error getting request context: %w", err)
	}

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, fmt.Errorf("Unauthenticated")
	}
	tenantID := authInfo.TenantID
	if tenantID == "" {
		return nil, fmt.Errorf("tenant_id is required")
	}

	ctx = annotateContext(ctx, tenantID, authInfo.TenantName, requestContext)
	log.Ctx(ctx).Info().Msgf("Creating pull request for GitHub repository")

	// Get the user's GitHub settings to retrieve the access token
	settings, err := s.getGitHubUserSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get GitHub user settings")
		return nil, err
	}

	if settings == nil || settings.AccessToken == "" {
		return nil, status.Error(codes.Unauthenticated, "GitHub OAuth is not configured")
	}

	// Create a GitHub client using the user's token
	client := github.NewClient(s.httpClient).WithAuthToken(settings.AccessToken)

	// Validate that the user has access to the head branch
	// For cross-repo PRs, the head should be in the format "username:branch"
	// For same-repo PRs, the head should be just the branch name
	head := request.Head

	// Check if this is a cross-repo PR (contains a colon)
	if strings.Contains(head, ":") {
		// For cross-repo PRs, ensure the username matches the authenticated user
		parts := strings.SplitN(head, ":", 2)
		if len(parts) != 2 {
			return nil, fmt.Errorf("Invalid head format. Expected 'username:branch' for cross-repo PRs")
		}

		username := parts[0]
		// Verify the username matches the authenticated user
		if username != settings.GithubLogin {
			log.Ctx(ctx).Error().
				Msg("User attempted to create PR from a branch they don't own")
			return nil, fmt.Errorf("You can only create pull requests from branches you own. Use format '%s:%s' instead of '%s'",
				settings.GithubLogin, parts[1], head)
		}
	} else {
		// For same-repo PRs, we need to ensure the user has push access to the repo
		// This is implicitly checked by GitHub when creating the PR, but we can add an explicit check here
		// by prefixing with the user's login to make it explicit
		head = fmt.Sprintf("%s:%s", settings.GithubLogin, head)
		log.Ctx(ctx).Info().Msgf("Converting head branch to explicit format")
	}

	// Create the pull request
	newPR := &github.NewPullRequest{
		Title: github.String(request.Title),
		Body:  github.String(request.Body),
		Head:  github.String(head),
		Base:  github.String(request.Base),
		Draft: github.Bool(request.Draft),
	}

	pr, _, err := client.PullRequests.Create(ctx, request.Repo.Owner, request.Repo.Name, newPR)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to create pull request for repository")
		return nil, fmt.Errorf("Failed to create pull request: %w", err)
	}

	// Convert to proto format
	protoPR := convertPullRequestToProto(pr)

	return &processorpb.CreatePullRequestResponse{
		PullRequest: protoPR,
	}, nil
}

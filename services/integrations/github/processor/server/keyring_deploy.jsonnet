// Deploy the key ring with the private key for our github app. The key itself is added to the ring
// manually by an admin.
local keyLib = import 'services/integrations/github/processor/server/key_lib.jsonnet';
function(cloud, env, namespace, namespace_config)
  local appName = 'github-processor';

  if !keyLib.hasKeyRingDeploy(cloud) then
    []
  else
    local keyRing = {
      apiVersion: 'kms.cnrm.cloud.google.com/v1beta1',
      kind: 'KMSKeyRing',
      metadata: {
        name: keyLib.keyRingName(cloud, env, appName),
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        location: keyLib.keyLocation(cloud),
      },
    };

    [
      keyRing,
    ]

package main

import (
	"context"
	"encoding/json"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/google/go-github/v64/github"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"

	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"

	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

var (
	indexLagGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_github_last_indexed_lag_seconds",
			Help: "Time difference between last indexed commit in our state and last pushed remote commit for a ref",
		},
		[]string{"tenant_id", "repo", "ref"},
	)
	uploadLagGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_github_last_uploaded_lag_seconds",
			Help: "Time difference between last uploaded commit in out state and last pushed remote commit for a ref",
		},
		[]string{"tenant_id", "repo", "ref"},
	)
)

// How often to check for lag
const lagTrackInterval = 15 * time.Minute

type lagTracker interface {
	StartAllLagTrackers()
	startTenantLagTracker(githubClient GithubClient, tenantID string)
	stopTenantLagTracker(tenantID string)
}

func init() {
	prometheus.MustRegister(indexLagGauge)
	prometheus.MustRegister(uploadLagGauge)
}

type LagTrackerImpl struct {
	tenantCache                  tenantwatcherclient.TenantCache
	githubClients                githubClients
	githubStateClient            githubstateclient.GithubStateClient
	tokenExchangeClient          tokenexchange.TokenExchangeClient
	webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource
	enableLagTracking            bool
	runningTrackers              map[string]context.CancelFunc
	runningTrackersMutex         sync.Mutex
}

func newLagTracker(tenantCache tenantwatcherclient.TenantCache, githubStateClient githubstateclient.GithubStateClient, tokenExchangeClient tokenexchange.TokenExchangeClient, githubClients githubClients, webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource, enableLagTracking bool) lagTracker {
	tracker := &LagTrackerImpl{
		tenantCache:                  tenantCache,
		githubStateClient:            githubStateClient,
		tokenExchangeClient:          tokenExchangeClient,
		githubClients:                githubClients,
		webhookTenantMappingResource: webhookTenantMappingResource,
		enableLagTracking:            enableLagTracking,
		runningTrackers:              make(map[string]context.CancelFunc),
	}
	return tracker
}

func (t *LagTrackerImpl) StartAllLagTrackers() {
	if !t.enableLagTracking {
		log.Info().Msg("Lag tracking is disabled, skipping lag tracker")
		return
	}
	mappings, err := t.webhookTenantMappingResource.List(context.Background(), "github-webhook")
	if err != nil {
		log.Error().Err(err).Msg("Failed to list webhook tenant mappings")
		return
	}

	for _, mapping := range mappings {
		var value struct {
			InstallationID int64 `json:"installation_id"`
		}
		err := json.Unmarshal([]byte(mapping.Spec.WebhookValue), &value)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to unmarshal webhook value for mapping %s", mapping.Name)
			continue
		}

		tenant, err := t.tenantCache.GetTenant(mapping.Spec.TenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get tenant name for mapping %s", mapping.Name)
			continue
		}

		githubClient := t.githubClients.installationClient(tenant.Name, value.InstallationID)

		t.startTenantLagTracker(githubClient, mapping.Spec.TenantID)
	}
}

func (t *LagTrackerImpl) startTenantLagTracker(githubClient GithubClient, tenantID string) {
	if !t.enableLagTracking {
		log.Info().Msg("Lag tracking is disabled, skipping lag tracker")
		return
	}

	ctx, cancel := context.WithCancel(context.Background())

	t.runningTrackersMutex.Lock()
	t.runningTrackers[tenantID] = cancel
	t.runningTrackersMutex.Unlock()

	log.Info().Msgf("Starting ref lag tracker for tenant %s", tenantID)
	go func() {
		timer := time.NewTimer(lagTrackInterval)
		defer timer.Stop()

		for {
			select {
			case <-ctx.Done():
				log.Info().Msgf("Finished stopping ref lag tracker for tenant %s", tenantID)
				return
			case <-timer.C:
				t.updateLagMetrics(tenantID, githubClient)
				timer.Reset(lagTrackInterval)
			}
		}
	}()
}

func (t *LagTrackerImpl) stopTenantLagTracker(tenantID string) {
	t.runningTrackersMutex.Lock()
	defer t.runningTrackersMutex.Unlock()

	if cancel, exists := t.runningTrackers[tenantID]; exists {
		cancel()                            // Cancel the context for this tenant's tracker
		delete(t.runningTrackers, tenantID) // Remove from the map
		log.Info().Msgf("Stopping ref lag tracker for tenant %s", tenantID)
	}
}

type repoInfo struct {
	owner string
	name  string
	ref   string
}

func (t *LagTrackerImpl) updateLagMetrics(tenantID string, githubClient GithubClient) {
	log.Info().Msgf("Updating lag metrics for tenant %s", tenantID)
	ctx := context.Background()
	requestContext, err := t.getRequestContext(ctx, tenantID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get request context")
		return
	}

	// Get all repos for this installation
	var allRepos []*github.Repository
	opt := &github.ListOptions{
		PerPage: 100, // GitHub's max per page
	}

	for {
		repos, resp, err := githubClient.App.ListRepos(ctx, opt)
		if err != nil {
			log.Error().Err(err).Msg("Failed to list repos")
			return
		}
		allRepos = append(allRepos, repos.Repositories...)

		if resp.NextPage == 0 {
			break
		}
		opt.Page = resp.NextPage
	}

	// Store latest remote commit times for each repo
	latestRemoteCommits := make(map[repoInfo]time.Time)
	for _, repo := range allRepos {

		// Get the latest commit from GitHub
		// assume we are only registering default branch
		fullName := repo.GetFullName()
		parts := strings.SplitN(fullName, "/", 2)

		if len(parts) != 2 {
			log.Error().Msgf("Unexpected format for full repository name: %s", githublib.RedactString(fullName))
			continue
		}
		repoOwner := parts[0]
		repoName := parts[1]

		ref := repo.GetDefaultBranch()
		commit, resp, err := githubClient.Repositories.GetCommit(ctx, repoOwner, repoName, ref, nil)
		if err != nil {
			if resp != nil && resp.StatusCode == 409 { // 409 Conflict is returned for empty repositories
				log.Info().Msgf("Repository %s/%s is empty - skipping lag tracking", githublib.RedactString(repoOwner), githublib.RedactString(repoName))
				continue
			}
			log.Error().Err(err).Msgf("Failed to get latest commit for %s ref %s", githublib.RedactString(repoOwner+"/"+repoName), githublib.RedactString(ref))
			continue
		}

		latestRemoteCommitTime := commit.Commit.Author.Date.Time
		latestRemoteCommits[repoInfo{owner: repoOwner, name: repoName, ref: ref}] = latestRemoteCommitTime
	}

	// Get all uploaded commits for this tenant
	latestUploadedCommits := make(map[repoInfo]time.Time)
	currentRepos, err := t.githubStateClient.GetCurrentRefStates(ctx, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get current repos")
		return
	}
	for _, repo := range currentRepos {
		repoOwner := repo.Ref.Repo.RepoOwner
		repoName := repo.Ref.Repo.RepoName
		ref := repo.Ref.Ref
		latestUploadedCommits[repoInfo{owner: repoOwner, name: repoName, ref: ref}] = repo.CommitTime.AsTime()
	}

	// Get all indexed commits for this tenant
	latestIndexedCommits := make(map[repoInfo]time.Time)
	indexedRefs, err := t.githubStateClient.GetIndexedRefCheckpoints(ctx, requestContext)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get indexed ref checkpoints")
		return
	}
	for _, refCheckpoint := range indexedRefs {
		repoOwner := refCheckpoint.Ref.Repo.RepoOwner
		repoName := refCheckpoint.Ref.Repo.RepoName
		ref := refCheckpoint.Ref.Ref
		latestIndexedCommits[repoInfo{owner: repoOwner, name: repoName, ref: ref}] = refCheckpoint.CommitTime.AsTime()
	}

	// Update the lag metrics for each repo
	// If a repo is not found in one of the states, set the lag to max value
	for repoInfo, latestRemoteCommitTime := range latestRemoteCommits {
		redactedRepoString := githublib.RedactString(repoInfo.owner + "/" + repoInfo.name)
		redactedRefString := githublib.RedactString(repoInfo.ref)

		lastUploadedCommitTime, ok := latestUploadedCommits[repoInfo]
		if !ok {
			log.Info().Msgf("Repo %s not found in current state, setting upload lag to max value", redactedRepoString)
			uploadLagGauge.WithLabelValues(tenantID, redactedRepoString, redactedRefString).Set(math.MaxFloat64)
		} else {
			uploadLag := latestRemoteCommitTime.Sub(lastUploadedCommitTime)
			uploadLagGauge.WithLabelValues(tenantID, redactedRepoString, redactedRefString).Set(uploadLag.Seconds())
		}

		lastIndexedCommitTime, ok := latestIndexedCommits[repoInfo]
		if !ok {
			log.Info().Msgf("Repo %s not found in indexed state, setting indexed lag to max value", redactedRepoString)
			indexLagGauge.WithLabelValues(tenantID, redactedRepoString, redactedRefString).Set(math.MaxFloat64)
		} else {
			indexLag := latestRemoteCommitTime.Sub(lastIndexedCommitTime)
			indexLagGauge.WithLabelValues(tenantID, redactedRepoString, redactedRefString).Set(indexLag.Seconds())
		}
	}
}

func (t *LagTrackerImpl) getRequestContext(ctx context.Context, tenantID string) (*requestcontext.RequestContext, error) {
	requestId := requestcontext.NewRandomRequestId()
	serviceToken, err := t.tokenExchangeClient.GetSignedTokenForService(ctx, tenantID, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_CONTENT_RW,
	})
	if err != nil {
		return nil, err
	}

	return requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken), nil
}

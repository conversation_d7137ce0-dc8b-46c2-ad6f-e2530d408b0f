package main

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetAllDirsForFile(t *testing.T) {
	type testCase struct {
		input    string
		expected []string
	}
	testCases := []testCase{
		{
			input:    "/path/to/some/file.txt",
			expected: []string{"/", "/path", "/path/to", "/path/to/some"},
		},
		{
			input:    "relative/path/to/file.txt",
			expected: []string{"", "relative", "relative/path", "relative/path/to"},
		},
		{
			input:    "",
			expected: []string{""},
		},
		{
			input:    "/",
			expected: []string{"/"},
		},
		{
			input:    ".",
			expected: []string{""},
		},
		{
			input:    "./",
			expected: []string{""},
		},
		{
			input:    "a",
			expected: []string{""},
		},
		{
			input:    "/a/b/c",
			expected: []string{"/", "/a", "/a/b"},
		},
		{
			input:    "a/b/c",
			expected: []string{"", "a", "a/b"},
		},
	}

	for i, tc := range testCases {
		t.Logf("testcase %d, %v", i, tc)
		actual := getAllDirsForFile(tc.input)
		assert.Equal(t, tc.expected, actual)
	}
}

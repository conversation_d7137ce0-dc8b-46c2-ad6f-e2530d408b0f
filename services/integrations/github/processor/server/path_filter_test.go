package main

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/augmentcode/augment/base/go/secretstring"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

func testHelperPathFilterFromTarball(files map[string]string, assertExpectations func(*testing.T, PathFilter)) func(*testing.T) {
	return func(t *testing.T) {
		requestContext := requestcontext.New(requestcontext.NewRandomRequestId(), requestcontext.NewRandomRequestSessionId(), "test-request-source",
			secretstring.New("test-auth-token"))
		ctx := requestContext.AnnotateLogContext(context.Background())

		_, _, _, tarball := fakeRepoTarball(t, files, nil)
		gzipReader, err := gzip.NewReader(bytes.NewReader(tarball))
		if err != nil {
			t.Fatalf("Failed to create gzip reader: %v", err)
		}
		defer gzipReader.Close()
		reader := tar.NewReader(gzipReader)
		pathFilter, err := newPathFilterFromTarball(ctx, reader)
		if err != nil {
			t.Fatalf("Failed to create ignore stack: %v", err)
		}
		assert.NoError(t, err)
		assertExpectations(t, pathFilter)
	}
}

func testHelperPathFilterFromDiffInfos(ignoreDiffInfos map[string]*DiffInfo, files map[string]string, assertExpectations func(*testing.T, PathFilter)) func(*testing.T) {
	return func(t *testing.T) {
		requestContext := requestcontext.New(requestcontext.NewRandomRequestId(), requestcontext.NewRandomRequestSessionId(), "test-request-source",
			secretstring.New("test-auth-token"))
		ctx := requestContext.AnnotateLogContext(context.Background())

		tmpDir := fakeRepo(t, files)
		pathFilter, err := newPathFilterFromDiffInfos(ctx, ignoreDiffInfos, tmpDir)
		assert.NoError(t, err)
		assertExpectations(t, pathFilter)
	}
}

func TestPathFilter(t *testing.T) {
	fakeFiles := map[string]string{
		".augmentignore":     "c\n",
		"file1.txt":          "hello 1",
		"secret.pem":         "hello 2",
		"a/.augmentignore":   "*.py\n",
		"a/file2.txt":        "hello 2",
		"a/script.py":        "print('hi')",
		"a/b/.augmentignore": "!important.txt\n",
		"a/b/.gitignore":     "*.txt\n",
		"a/b/file3.txt":      "hello 3",
		"a/b/important.txt":  "foo",
		"c/d/.augmentignore": "!*.txt\n",
		"c/d/file4.txt":      "hello 4",
		"..e/f/file5.txt":    "hello 5",
	}

	assertExpectations := func(t *testing.T, pathFilter PathFilter) {
		// Should not be ignored
		// since no rules apply
		assert.False(t, pathFilter.IgnoreFile("file1.txt"))
		assert.False(t, pathFilter.IgnoreFile("a/file2.txt"))
		// since it is explicitly included by .augmentignore
		assert.False(t, pathFilter.IgnoreFile("a/b/important.txt"))

		// Should be ignored
		// by .augmentignore
		assert.True(t, pathFilter.IgnoreFile("a/script.py"))
		// by secret filename check
		assert.True(t, pathFilter.IgnoreFile("secret.pem"))
		// by .gitignore
		assert.True(t, pathFilter.IgnoreFile("a/b/file3.txt"))
		// By the .augmentignore ignoring the parent directory
		assert.True(t, pathFilter.IgnoreFile("c/d/file4.txt"))
		// paths with ".." are ignored
		assert.True(t, pathFilter.IgnoreFile("..e/f/file5.txt"))
	}

	t.Run("from tarball", testHelperPathFilterFromTarball(fakeFiles, assertExpectations))

	ignoreDiffInfos := map[string]*DiffInfo{
		".augmentignore": {
			filename: ".augmentignore",
		},
		"a/.augmentignore": {
			filename: "a/.augmentignore",
		},
		"a/b/.augmentignore": {
			filename: "a/b/.augmentignore",
		},
		"a/b/.gitignore": {
			filename: "a/b/.gitignore",
		},
		"c/d/.augmentignore": {
			filename: "c/d/.augmentignore",
		},
	}

	t.Run("from diff infos", testHelperPathFilterFromDiffInfos(ignoreDiffInfos, fakeFiles, assertExpectations))
}

func TestPathFilterNested(t *testing.T) {
	fakeFiles := map[string]string{
		".augmentignore":   "*.out\n",
		"file1.txt":        "hello 1",
		"file2.out":        "hello 2",
		"a/.augmentignore": "*.out2\n",
		"a/file3.txt":      "hello 3",
		"a/file4.out":      "hello 4",
		"a/file5.out2":     "hello 5",
		"a/b/file6.txt":    "hello 6",
		"a/b/file7.out":    "hello 7",
		"a/b/file8.out2":   "hello 8",
	}

	assertExpectations := func(t *testing.T, pathFilter PathFilter) {
		// Should not be ignored
		// since no rules apply
		assert.False(t, pathFilter.IgnoreFile("file1.txt"))
		assert.False(t, pathFilter.IgnoreFile("a/file3.txt"))
		assert.False(t, pathFilter.IgnoreFile("a/b/file6.txt"))

		// Should be ignored
		// by root .augmentignore
		assert.True(t, pathFilter.IgnoreFile("file2.out"))
		assert.True(t, pathFilter.IgnoreFile("a/file4.out"))
		assert.True(t, pathFilter.IgnoreFile("a/b/file7.out"))

		// by subdirectory .augmentignore
		assert.True(t, pathFilter.IgnoreFile("a/file5.out2"))
		assert.True(t, pathFilter.IgnoreFile("a/b/file8.out2"))
	}

	t.Run("from tarball", testHelperPathFilterFromTarball(fakeFiles, assertExpectations))

	ignoreDiffInfos := map[string]*DiffInfo{
		".augmentignore": {
			filename: ".augmentignore",
		},
		"a/.augmentignore": {
			filename: "a/.augmentignore",
		},
		"a/b/.augmentignore": {
			filename: "a/b/.augmentignore",
		},
	}

	t.Run("from diff infos", testHelperPathFilterFromDiffInfos(ignoreDiffInfos, fakeFiles, assertExpectations))
}

func TestPathFilterBadPattern(t *testing.T) {
	fakeFiles := map[string]string{
		".augmentignore": "!\n/\nfile2.txt",
		"file1.txt":      "hello 1",
		"file2.txt":      "hello 2",
	}

	assertExpectations := func(t *testing.T, pathFilter PathFilter) {
		// Should not be ignored
		// since no rules apply
		assert.False(t, pathFilter.IgnoreFile("file1.txt"))

		// Should be ignored
		// by .augmentignore
		// assert.True(t, pathFilter.IgnoreFile("file2.txt"))
		// TODO: Currently we skip ignorefiles that have patterns that crash our
		// library. Ideally we skip the bad patterns instead of skipping the
		// file entirely
		assert.False(t, pathFilter.IgnoreFile("file2.txt"))
	}

	t.Run("from tarball", testHelperPathFilterFromTarball(fakeFiles, assertExpectations))

	ignoreDiffInfos := map[string]*DiffInfo{
		".augmentignore": {
			filename: ".augmentignore",
		},
	}

	t.Run("from diff infos", testHelperPathFilterFromDiffInfos(ignoreDiffInfos, fakeFiles, assertExpectations))
}

func TestGetIgnoreDiffInfos(t *testing.T) {
	diffInfos := map[string]*DiffInfo{
		"file1.txt": {
			filename: "file1.txt",
		},
		"a/b/file2.txt": {
			filename: "a/b/file2.txt",
		},
		// Ignored
		"..c/d/file3.txt": {
			filename: "..c/d/file3.txt",
		},
	}

	ignoreDiffInfos, ignoreFileChanged := getIgnoreDiffInfos(diffInfos)
	assert.False(t, ignoreFileChanged)
	assert.Equal(t, map[string]*DiffInfo{
		".augmentignore": {
			filename:     ".augmentignore",
			isIgnoreFile: true,
		},
		"a/.augmentignore": {
			filename:     "a/.augmentignore",
			isIgnoreFile: true,
		},
		"a/b/.augmentignore": {
			filename:     "a/b/.augmentignore",
			isIgnoreFile: true,
		},
		".gitignore": {
			filename:     ".gitignore",
			isIgnoreFile: true,
		},
		"a/.gitignore": {
			filename:     "a/.gitignore",
			isIgnoreFile: true,
		},
		"a/b/.gitignore": {
			filename:     "a/b/.gitignore",
			isIgnoreFile: true,
		},
	}, ignoreDiffInfos)
}

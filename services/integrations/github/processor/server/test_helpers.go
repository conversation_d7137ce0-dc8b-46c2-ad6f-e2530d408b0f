package main

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"testing"
	"time"

	blobnames "github.com/augmentcode/augment/base/blob_names"
	blobnamespb "github.com/augmentcode/augment/base/blob_names/proto"
	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/secretstring"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	contentmanagerproto "github.com/augmentcode/augment/services/content_manager/proto"
	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	settingsserviceproto "github.com/augmentcode/augment/services/settings/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/go-github/v64/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Mock for WebhookTenantMappingResource
var _ webhookmapping.WebhookTenantMappingResource = &MockWebhookTenantMappingResource{}

type MockWebhookTenantMappingResource struct {
	mock.Mock
}

func (m *MockWebhookTenantMappingResource) Update(ctx context.Context, Spec *webhookmapping.WebhookTenantMappingSpec, name string, appName string) (*webhookmapping.WebhookTenantMapping, error) {
	args := m.Called(ctx, Spec, name, appName)
	return args.Get(0).(*webhookmapping.WebhookTenantMapping), args.Error(1)
}

func (m *MockWebhookTenantMappingResource) Delete(ctx context.Context, name string) error {
	args := m.Called(ctx, name)
	return args.Error(0)
}

func (m *MockWebhookTenantMappingResource) List(ctx context.Context, appName string) ([]webhookmapping.WebhookTenantMapping, error) {
	args := m.Called(ctx, appName)
	return args.Get(0).([]webhookmapping.WebhookTenantMapping), args.Error(1)
}

// Mock for githubClients
var _ githubClients = &MockGithubClients{}

type MockGithubClients struct {
	mock.Mock
}

func (m *MockGithubClients) httpClient() *http.Client {
	args := m.Called()
	return args.Get(0).(*http.Client)
}

func (m *MockGithubClients) installationClient(tenantName string, installationID int64) GithubClient {
	args := m.Called(tenantName, installationID)
	return args.Get(0).(GithubClient)
}

func (m *MockGithubClients) accessTokenClient(installationID int64, accessToken string) *github.Client {
	args := m.Called(installationID, accessToken)
	return args.Get(0).(*github.Client)
}

// Mock for ContentManagerClient
var _ contentmanagerclient.ContentManagerClient = &MockContentManagerClient{}

type MockContentManagerClient struct {
	mock.Mock
}

func (m *MockContentManagerClient) Close() {
	m.Called()
}

func (m *MockContentManagerClient) UploadBlobContent(ctx context.Context, content []byte, path string, requestContext *requestcontext.RequestContext) (string, bool, error) {
	args := m.Called(ctx, content, path, requestContext)
	return args.String(0), args.Bool(1), args.Error(2)
}

func (m *MockContentManagerClient) BatchUploadBlobContent(ctx context.Context, blobs []*contentmanagerproto.UploadBlobContent, priority contentmanagerproto.IndexingPriority, requestContext *requestcontext.RequestContext) ([]*contentmanagerproto.UploadBlobResult, error) {
	args := m.Called(ctx, blobs, priority, requestContext)
	return args.Get(0).([]*contentmanagerproto.UploadBlobResult), args.Error(1)
}

func (m *MockContentManagerClient) FindMissingBlobs(ctx context.Context, blobNames []blobnames.BlobName, transformationKey string, subKey string, requestContext *requestcontext.RequestContext) ([]string, error) {
	args := m.Called(ctx, blobNames, transformationKey, subKey, requestContext)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockContentManagerClient) CheckpointBlobs(ctx context.Context, blobs *blobnamespb.Blobs, requestContext *requestcontext.RequestContext) (string, error) {
	args := m.Called(ctx, blobs, requestContext)
	return args.String(0), args.Error(1)
}

func (m *MockContentManagerClient) BatchDownloadContent(ctx context.Context, keys []*contentmanagerproto.BlobContentKey, requestContext *requestcontext.RequestContext, tenantID string) (<-chan contentmanagerclient.BatchDownloadContentResult, error) {
	args := m.Called(ctx, keys, requestContext, tenantID)
	return args.Get(0).(<-chan contentmanagerclient.BatchDownloadContentResult), args.Error(1)
}

func (m *MockContentManagerClient) GetAllBlobsFromCheckpoint(ctx context.Context, checkpointID string, requestContext *requestcontext.RequestContext) ([]blobnames.BlobName, error) {
	return nil, status.Error(codes.Unimplemented, "not implemented")
}

func (m *MockContentManagerClient) GetBlobInfo(ctx context.Context, blobName blobnames.BlobName, requestContext *requestcontext.RequestContext) (*contentmanagerproto.GetBlobInfoResponse, error) {
	args := m.Called(ctx, blobName, requestContext)
	return args.Get(0).(*contentmanagerproto.GetBlobInfoResponse), args.Error(1)
}

func (m *MockContentManagerClient) BatchGetBlobInfo(ctx context.Context, blobNames []blobnames.BlobName, tenantID string, requestContext *requestcontext.RequestContext) (*contentmanagerproto.BatchGetBlobInfoResponse, error) {
	args := m.Called(ctx, blobNames, tenantID, requestContext)
	return args.Get(0).(*contentmanagerproto.BatchGetBlobInfoResponse), args.Error(1)
}

func (m *MockContentManagerClient) GetBestAnnIndex(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, requestContext *requestcontext.RequestContext) (*contentmanagerclient.GetBestAnnIndexResult, error) {
	args := m.Called(ctx, transformationKey, checkpointId, requestContext)
	return args.Get(0).(*contentmanagerclient.GetBestAnnIndexResult), args.Error(1)
}

func (m *MockContentManagerClient) GetAnnIndexBlobInfos(ctx context.Context, key contentmanagerclient.AnnIndexKey, requestContext requestcontext.RequestContext) (*contentmanagerclient.GetAnnIndexBlobInfosResult, error) {
	args := m.Called(ctx, key, requestContext)
	return args.Get(0).(*contentmanagerclient.GetAnnIndexBlobInfosResult), args.Error(1)
}

func (m *MockContentManagerClient) GetAnnIndexAsset(ctx context.Context, tenantId *string, transformationKey string, indexId string, subKey string, requestContext requestcontext.RequestContext) ([]byte, error) {
	args := m.Called(ctx, transformationKey, indexId, subKey, requestContext)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockContentManagerClient) AddAnnIndexMapping(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, indexId string, addedBlobs []blobnames.BlobName, removedBlobs []blobnames.BlobName, requestContext requestcontext.RequestContext) (*contentmanagerproto.AddAnnIndexMappingResponse, error) {
	args := m.Called(ctx, transformationKey, checkpointId, indexId, addedBlobs, removedBlobs, requestContext)
	return args.Get(0).(*contentmanagerproto.AddAnnIndexMappingResponse), args.Error(1)
}

func (m *MockContentManagerClient) UploadAnnIndexBlobInfos(ctx context.Context, tenantId *string, transformationKey string, indexId string, infos []contentmanagerclient.AnnIndexBlobInfo, requestContext requestcontext.RequestContext) (*contentmanagerproto.UploadAnnIndexBlobInfosResponse, error) {
	args := m.Called(ctx, transformationKey, indexId, infos, requestContext)
	return args.Get(0).(*contentmanagerproto.UploadAnnIndexBlobInfosResponse), args.Error(1)
}

func (m *MockContentManagerClient) UploadAnnIndexAssets(ctx context.Context, tenantId *string, transformationKey string, indexId string, assets []contentmanagerclient.AnnIndexAssetInput, requestContext requestcontext.RequestContext) (*contentmanagerproto.UploadAnnIndexAssetsResponse, error) {
	args := m.Called(ctx, transformationKey, indexId, assets, requestContext)
	return args.Get(0).(*contentmanagerproto.UploadAnnIndexAssetsResponse), args.Error(1)
}

// Mock for GithubStateClient
var _ githubstateclient.GithubStateClient = &MockGithubStateClient{}

type MockGithubStateClient struct {
	mock.Mock
}

func (m *MockGithubStateClient) GetIndexedRefCheckpoints(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*githubstatepersistproto.RefCheckpoint, error) {
	args := m.Called(ctx, requestContext)
	return args.Get(0).([]*githubstatepersistproto.RefCheckpoint), args.Error(1)
}

func (m *MockGithubStateClient) GetCurrentRefStates(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*githubstatepersistproto.RefCheckpoint, error) {
	args := m.Called(ctx, requestContext)
	return args.Get(0).([]*githubstatepersistproto.RefCheckpoint), args.Error(1)
}

func (m *MockGithubStateClient) GetCurrentRefState(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *githubstatepersistproto.GithubRef) (*githubstatepersistproto.RefCheckpoint, error) {
	args := m.Called(ctx, requestContext, githubRef)
	return args.Get(0).(*githubstatepersistproto.RefCheckpoint), args.Error(1)
}

func (m *MockGithubStateClient) GetCurrentRefFiles(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *githubstatepersistproto.GithubRef, commitSha string, filePaths []string) (<-chan githubstateclient.GetCurrentRefFilesResult, error) {
	args := m.Called(ctx, requestContext, githubRef, commitSha, filePaths)
	return args.Get(0).(<-chan githubstateclient.GetCurrentRefFilesResult), args.Error(1)
}

func (m *MockGithubStateClient) UpdateCurrentRefCheckpoint(ctx context.Context, requestContext *requestcontext.RequestContext, githubRef *githubstatepersistproto.GithubRef, commitSha string, parentCommitShas []string, diffCommitSha string, commitTime *timestamppb.Timestamp, checkpointId *blobnamespb.Blobs, diffInfos []*githubstatepersistproto.DiffInfo, forceUpload bool) (*githubstatepersistproto.UpdateCurrentRefCheckpointResponse, error) {
	args := m.Called(ctx, requestContext, githubRef, commitSha, parentCommitShas, diffCommitSha, commitTime, checkpointId, diffInfos, forceUpload)
	return args.Get(0).(*githubstatepersistproto.UpdateCurrentRefCheckpointResponse), args.Error(1)
}

func (m *MockGithubStateClient) DeleteGithubStateForRepos(ctx context.Context, requestContext *requestcontext.RequestContext, repos []*githubstatepersistproto.GithubRepo) error {
	args := m.Called(ctx, requestContext, repos)
	return args.Error(0)
}

func (m *MockGithubStateClient) Close() {
	m.Called()
}

// Mock for GithubRepositoriesClient
var _ GithubRepositoriesClient = &MockGithubRepositoriesClient{}

type MockGithubRepositoriesClient struct {
	mock.Mock
}

func (m *MockGithubRepositoriesClient) CompareCommits(ctx context.Context, owner, repo, base, head string, opts *github.ListOptions) (*github.CommitsComparison, *github.Response, error) {
	args := m.Called(ctx, owner, repo, base, head, opts)
	return args.Get(0).(*github.CommitsComparison), args.Get(1).(*github.Response), args.Error(2)
}

func (m *MockGithubRepositoriesClient) CompareCommitsRaw(ctx context.Context, owner, repo, base, head string, opts github.RawOptions) (string, *github.Response, error) {
	args := m.Called(ctx, owner, repo, base, head, opts)
	return args.String(0), args.Get(1).(*github.Response), args.Error(2)
}

func (m *MockGithubRepositoriesClient) GetContents(ctx context.Context, owner, repo, filepath string, opt *github.RepositoryContentGetOptions) (*github.RepositoryContent, []*github.RepositoryContent, *github.Response, error) {
	args := m.Called(ctx, owner, repo, filepath, opt)
	return args.Get(0).(*github.RepositoryContent), args.Get(1).([]*github.RepositoryContent), args.Get(2).(*github.Response), args.Error(3)
}

func (m *MockGithubRepositoriesClient) GetCommit(ctx context.Context, owner, repo, sha string, opts *github.ListOptions) (*github.RepositoryCommit, *github.Response, error) {
	args := m.Called(ctx, owner, repo, sha, opts)
	return args.Get(0).(*github.RepositoryCommit), args.Get(1).(*github.Response), args.Error(2)
}

func (m *MockGithubRepositoriesClient) GetArchiveLink(ctx context.Context, owner, repo string, archiveFormat github.ArchiveFormat, opts *github.RepositoryContentGetOptions, maxRedirects int) (*url.URL, *github.Response, error) {
	args := m.Called(ctx, owner, repo, archiveFormat, opts, maxRedirects)
	return args.Get(0).(*url.URL), args.Get(1).(*github.Response), args.Error(2)
}

func (m *MockGithubRepositoriesClient) Get(ctx context.Context, owner, repo string) (*github.Repository, *github.Response, error) {
	args := m.Called(ctx, owner, repo)
	return args.Get(0).(*github.Repository), args.Get(1).(*github.Response), args.Error(2)
}

// Mock for GithubAppServiceClient
type MockGithubAppServiceClient struct {
	mock.Mock
}

func (m *MockGithubAppServiceClient) ListRepos(ctx context.Context, opts *github.ListOptions) (*github.ListRepositories, *github.Response, error) {
	args := m.Called(ctx, opts)
	return args.Get(0).(*github.ListRepositories), args.Get(1).(*github.Response), args.Error(2)
}

// Mock for lagTracker

type MockLagTracker struct {
	mock.Mock
}

func (m *MockLagTracker) StartAllLagTrackers() {
	m.Called()
}

func (m *MockLagTracker) startTenantLagTracker(githubClient GithubClient, tenantID string) {
	m.Called(githubClient, tenantID)
}

func (m *MockLagTracker) stopTenantLagTracker(tenantID string) {
	m.Called(tenantID)
}

// Mock for TokenExchangeClient
type MockTokenExchangeClient struct {
	mock.Mock
}

func (m *MockTokenExchangeClient) GetSignedTokenForService(ctx context.Context, tenantID string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, scopes)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForServiceWithNamespace(ctx context.Context, tenantID string, namespace string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, namespace, scopes)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForUser(ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId, userEmail *string, tenantID string, namespace *string, additionalClaims map[string]any) (secretstring.SecretString, error) {
	args := m.Called(ctx, userID, opaqueUserID, userEmail, tenantID, namespace, additionalClaims)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForIAPToken(ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []tokenexchangeproto.Scope, expiration time.Duration) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, iapToken, scopes, expiration)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetVerificationKey(ctx context.Context) ([]byte, error) {
	args := m.Called(ctx)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockTokenExchangeClient) Close() {}

func fakeRepoTarball(t *testing.T, files map[string]string, ignored map[string]struct{}) (blobnames.CheckpointID, []blobnames.BlobName, []*contentmanagerproto.UploadBlobContent, []byte) {
	t.Helper()

	if ignored == nil {
		ignored = map[string]struct{}{}
	}

	buf := new(bytes.Buffer)
	tw := tar.NewWriter(buf)
	var blobNames []blobnames.BlobName
	var uploads []*contentmanagerproto.UploadBlobContent

	for path, content := range files {
		hdr := &tar.Header{
			Name: fmt.Sprintf("testRepo/%s", path),
			Mode: 0o600,
			Size: int64(len(content)),
		}
		if err := tw.WriteHeader(hdr); err != nil {
			t.Fatalf("Failed to write header: %v", err)
		}
		if _, err := tw.Write([]byte(content)); err != nil {
			t.Fatalf("Failed to write data: %v", err)
		}
		if _, ok := ignored[path]; ok {
			continue
		}
		blobNames = append(blobNames, blobnames.GetBlobName(path, []byte(content)))
		uploads = append(uploads, &contentmanagerproto.UploadBlobContent{
			Content: []byte(content),
			Metadata: []*contentmanagerproto.BlobMetadata{
				{
					Key:   "path",
					Value: path,
				},
			},
		})
	}

	if err := tw.Close(); err != nil {
		t.Fatalf("Failed to close tar writer: %v", err)
	}

	gzBuf := new(bytes.Buffer)
	gzw := gzip.NewWriter(gzBuf)
	if _, err := gzw.Write(buf.Bytes()); err != nil {
		t.Fatalf("Failed to write to gzip writer: %v", err)
	}
	if err := gzw.Close(); err != nil {
		t.Fatalf("Failed to close gzip writer: %v", err)
	}

	if len(blobNames) == 0 {
		return "", nil, nil, gzBuf.Bytes()
	}

	checkpointID, err := blobnames.CalculateCheckpointID(blobNames)
	if err != nil {
		t.Fatalf("Failed to calculate checkpoint ID: %v", err)
	}

	// Return the gzipped tarball
	return checkpointID, blobNames, uploads, gzBuf.Bytes()
}

func fakeRepo(t *testing.T, files map[string]string) (tmpDir string) {
	t.Helper()
	tmpDir, err := os.MkdirTemp("", "test-repo-*")
	assert.NoError(t, err)
	t.Cleanup(func() {
		os.RemoveAll(tmpDir)
	})
	for filename, content := range files {
		fullPath := filepath.Join(tmpDir, filename)
		os.MkdirAll(filepath.Dir(fullPath), 0o755)
		err := os.WriteFile(fullPath, []byte(content), 0o600)
		assert.NoError(t, err)
	}
	return tmpDir
}

// Mock for RequestInsightPublisher

type MockRequestInsightPublisher struct {
	mock.Mock
}

func (m *MockRequestInsightPublisher) PublishRequestEvent(
	ctx context.Context,
	requestID string,
	tenantInfo *requestinsightproto.TenantInfo,
	event *requestinsightproto.RequestEvent,
) error {
	args := m.Called(ctx, requestID, tenantInfo, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishSessionEvent(
	ctx context.Context,
	sessionID string,
	opaqueUserID *authentitiesproto.UserId,
	tenantInfo *requestinsightproto.TenantInfo,
	event *requestinsightproto.SessionEvent,
) error {
	args := m.Called(ctx, sessionID, opaqueUserID, tenantInfo, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishTenantEvent(
	ctx context.Context,
	tenantInfo *requestinsightproto.TenantInfo,
	event *requestinsightproto.TenantEvent,
) error {
	args := m.Called(ctx, tenantInfo, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishGenericEvent(
	ctx context.Context,
	sessionID string,
	event *requestinsightproto.GenericEvent,
) error {
	args := m.Called(ctx, sessionID, event)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishRequestEvents(
	ctx context.Context,
	requestID string,
	tenantInfo *requestinsightproto.TenantInfo,
	events []*requestinsightproto.RequestEvent,
) error {
	args := m.Called(ctx, requestID, tenantInfo, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishSessionEvents(
	ctx context.Context,
	sessionID string,
	opaqueUserID *authentitiesproto.UserId,
	tenantInfo *requestinsightproto.TenantInfo,
	events []*requestinsightproto.SessionEvent,
) error {
	args := m.Called(ctx, sessionID, opaqueUserID, tenantInfo, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishTenantEvents(
	ctx context.Context,
	tenantInfo *requestinsightproto.TenantInfo,
	events []*requestinsightproto.TenantEvent,
) error {
	args := m.Called(ctx, tenantInfo, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) PublishGenericEvents(
	ctx context.Context,
	sessionID string,
	events []*requestinsightproto.GenericEvent,
) error {
	args := m.Called(ctx, sessionID, events)
	return args.Error(0)
}

func (m *MockRequestInsightPublisher) Close() error {
	args := m.Called()
	return args.Error(0)
}

// Mock SettingsClient
type mockSettingsClient struct {
	mock.Mock
}

func (m *mockSettingsClient) GetTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext) (*settingsserviceproto.GetTenantSettingsResponse, error) {
	args := m.Called(ctx, requestContext)
	return args.Get(0).(*settingsserviceproto.GetTenantSettingsResponse), args.Error(1)
}

func (m *mockSettingsClient) UpdateTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext, settings *settingsserviceproto.TenantSettings, expectedVersion string) (*settingsserviceproto.UpdateTenantSettingsResponse, error) {
	args := m.Called(ctx, requestContext, settings, expectedVersion)
	return args.Get(0).(*settingsserviceproto.UpdateTenantSettingsResponse), args.Error(1)
}

func (m *mockSettingsClient) GetUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string) (*settingsserviceproto.GetUserSettingsResponse, error) {
	args := m.Called(ctx, requestContext, userId)
	return args.Get(0).(*settingsserviceproto.GetUserSettingsResponse), args.Error(1)
}

func (m *mockSettingsClient) UpdateUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string, settings *settingsserviceproto.UserSettings, expectedVersion string) (*settingsserviceproto.UpdateUserSettingsResponse, error) {
	args := m.Called(ctx, requestContext, userId, settings, expectedVersion)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*settingsserviceproto.UpdateUserSettingsResponse), args.Error(1)
}

func (m *mockSettingsClient) Close() error {
	return nil
}

// Mock GithubEventHandler
type MockGithubEventHandler struct {
	mock.Mock
}

func (m *MockGithubEventHandler) HandleGithubEvent(ctx context.Context, data []byte) error {
	args := m.Called(ctx, data)
	return args.Error(0)
}

func (m *MockGithubEventHandler) RegisterRepo(ctx context.Context, requestContext *requestcontext.RequestContext, githubClient GithubRepositoriesClient, repoInfo *settingsproto.RepoInformation, tenantID string, branch string, headCommitSHA string) (*blobsproto.Blobs, *errorWithRateLimit) {
	args := m.Called(ctx, requestContext, githubClient, repoInfo, tenantID, branch, headCommitSHA)
	return args.Get(0).(*blobsproto.Blobs), args.Get(1).(*errorWithRateLimit)
}

func (m *MockGithubEventHandler) GetRequestContext(ctx context.Context, tenantID string, requestId requestcontext.RequestId) (*requestcontext.RequestContext, error) {
	args := m.Called(ctx, tenantID, requestId)
	return args.Get(0).(*requestcontext.RequestContext), args.Error(1)
}

func (m *MockGithubEventHandler) handleInstallationRateLimitSleep(ctx context.Context, tenantID string, rateLimit *github.Rate) error {
	args := m.Called(ctx, tenantID, rateLimit)
	return args.Error(0)
}

// Mock for Pubsub PublishClient
type MockPubsubPublishClient struct {
	mock.Mock
}

func (m *MockPubsubPublishClient) Publish(ctx context.Context, namespace string, data []byte) error {
	args := m.Called(ctx, namespace, data)
	return args.Error(0)
}

func (m *MockPubsubPublishClient) PublishWithOrderingKey(ctx context.Context, namespace string, data []byte, orderingKey string) error {
	args := m.Called(ctx, namespace, data, orderingKey)
	return args.Error(0)
}

func (m *MockPubsubPublishClient) Close() error {
	args := m.Called()
	return args.Error(0)
}

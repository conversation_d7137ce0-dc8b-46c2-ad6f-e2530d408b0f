package main

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"slices"

	"github.com/rs/zerolog/log"
)

func readTarball(ctx context.Context, tarballPath string) (reader *tar.Reader, closeFunc func(), err error) {
	fileInfo, err := os.Stat(tarballPath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to stat saved tarball: %w", err)
	}
	log.Ctx(ctx).Info().Msgf("Reading tarball of size %d bytes", fileInfo.Size())

	tarballReader, err := os.Open(tarballPath)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to open saved tarball: %w", err)
	}

	// Decompress the tarball
	gzipReader, err := gzip.NewReader(tarballReader)
	if err != nil {
		tarballReader.Close()
		return nil, nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}

	reader = tar.NewReader(gzipReader)
	closeFunc = func() {
		tarballReader.Close()
		gzipReader.Close()
	}
	return reader, closeFunc, nil
}

func createTempDir() (string, error) {
	return os.MkdirTemp("", "github-content-uploader-*")
}

func createFile(tmpDir string, filename string, content []byte) error {
	// Ensure the directory exists
	fullPath := filepath.Join(tmpDir, filename)
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// Create or open the file
	file, err := os.Create(fullPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	// Write content to the file
	_, err = file.Write(content)
	if err != nil {
		return fmt.Errorf("failed to write to file: %w", err)
	}

	return nil
}

// Returns a nil slice instead of an error if the file does not exist
func readFileIfExists(tmpDir, filename string) ([]byte, error) {
	fullPath := filepath.Join(tmpDir, filename)
	contents, err := os.ReadFile(fullPath)
	if err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			// no file, don't return an error
			return nil, nil
		}
		return nil, fmt.Errorf("failed to read file: %w", err)
	}
	return contents, nil
}

// Returns a list of all directories in the given path, assuming the path is for
// a file. For example, if the path is "a/b/c.txt", it will return ["", "a", "a/b"].
func getAllDirsForFile(path string) []string {
	dirs := []string{}
	path = filepath.Clean(path)
	path = filepath.Dir(path)

	for path != "." && path != "/" {
		dirs = append(dirs, path)
		path = filepath.Dir(path)
	}

	// Handle root directory
	if path == "/" {
		dirs = append(dirs, "/")
	} else {
		dirs = append(dirs, "")
	}

	slices.Reverse(dirs)
	return dirs
}

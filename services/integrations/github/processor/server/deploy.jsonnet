local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local appSecretLib = import 'services/integrations/github/processor/server/app_secret_lib.jsonnet';
local keyLib = import 'services/integrations/github/processor/server/key_lib.jsonnet';
local pubsubLib = import 'services/lib/pubsub/pubsub_lib.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'github-processor';
  local publisherAppName = 'github-webhook';

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local dynamicFeatureFlagConfig = dynamicFeatureFlagLib.createLaunchDarklySecret(cloud, env, namespace, appName=appName);

  local requestInsightPublisher = publisherLib(cloud, env, namespace, appName);

  // creates a service for the pod, also create a global service if necessary
  // to allow the lead cluster (customer-ui) to talk to this pod. We only
  // deploy these when necessary, to avoid running into GCP limits
  local useGlobalService = !cloudInfo.isLeadCluster(cloud);
  local serverDnsNames = grpcLib.grpcServiceNames(appName);
  local centralServerDnsNames = grpcLib.grpcServiceNamespaceNames(appName, namespace=namespace) + if useGlobalService then [
    grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=appName, namespace=namespace),
  ] else [];
  local services = [
    grpcLib.grpcService(appName=appName, namespace=namespace),
  ] + if useGlobalService then [
    grpcLib.globalGrpcService(cloud=cloud, appName=appName, namespace=namespace),
  ] else [];

  // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
  // in the same namespace
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a server certificate for MTLS
  local serverCert = certLib.createServerCert(
    name='%s-server-certificate' % appName,
    namespace=namespace,
    appName=appName,
    dnsNames=serverDnsNames,
    volumeName='certs'
  );

  // creates a server certificate for central MTLS
  local centralServerCert = certLib.createCentralServerCert(
    name='%s-central-server-certificate' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=centralServerDnsNames,
    volumeName='central-certs'
  );

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix='github-process'
  );

  local roles = [
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'Role',
      metadata: {
        labels: {
          app: appName,
        },
        name: '%s-webhooktenantmapping-creator' % appName,
        namespace: namespace,
      },
      rules: [
        {
          apiGroups: ['eng.augmentcode.com'],
          resources: ['webhooktenantmappings'],
          verbs: ['*'],
        },
      ],
    },
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'RoleBinding',
      metadata: {
        labels: {
          app: appName,
        },
        name: '%s-webhookmapping-creator' % appName,
        namespace: namespace,
      },
      roleRef: {
        apiGroup: 'rbac.authorization.k8s.io',
        kind: 'Role',
        name: '%s-webhooktenantmapping-creator' % appName,
      },
      subjects: [
        {
          kind: 'ServiceAccount',
          name: serviceAccount.name,
          namespace: namespace,
        },
      ],
    },
  ];

  // TODO: self-serve namesapces shouldn't have these subsriptions
  local subscriberSpecOverride = {
    retryPolicy: {
      minimumBackoff: '5s',
      maximumBackoff: '300s',
    },
    enableMessageOrdering: true,  // sequential ordering
  };
  local subscriber = pubsubLib.namespaceSubscriber(env=env,
                                                   namespace=namespace,
                                                   cloud=cloud,
                                                   appName=appName,
                                                   publisherAppName=publisherAppName,
                                                   serviceAccount=serviceAccount,
                                                   deadLetterMaxDeliveryAttempts=5,
                                                   spec=subscriberSpecOverride,
                                                   deadLetterSubSpec=subscriberSpecOverride);

  // publish to the same topic as the webhook listener
  local publisherTopicTemplate = pubsubLib.publisherTopicTemplate(publisherAppName=publisherAppName);

  // give the processor access to pub/sub topic
  local publisherIAMPolicy = pubsubLib.publisherTopicIAMPolicy(
    env=env,
    namespace=namespace,
    appName=appName,
    publisherAppName=publisherAppName,
    serviceAccount=serviceAccount,
    cloud=cloud,
    isDeadLetterTopic=false,
  );

  local appSecret = appSecretLib(env=env, namespace=namespace, cloud=cloud, appName=appName, secretsOverride=namespace_config.flags.githubSealedSecrets);

  local oauthAppSecretOverride = if env == 'DEV' then namespace_config.flags.githubOauthSecretOverride else {};
  local oauthAppSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='oauth-app-secret',
    version=if std.objectHas(oauthAppSecretOverride, 'version') then oauthAppSecretOverride.version else {
      PROD: '2',
      STAGING: '2',
      DEV: '2',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=if std.objectHas(oauthAppSecretOverride, 'name') then oauthAppSecretOverride.name else null,
  );

  // Update the version below when you update the app's private key. Instructions for uploading a
  // new key are here:
  // https://www.notion.so/Runbook-Uploading-a-github-app-key-to-CloudKMS-105bba10175a805ba90af469f814ab61
  local kmsKeyVersion = {
    PROD: 1,
    STAGING: 1,
    DEV: 3,
  }[env];
  local kmsKey = keyLib.kmsKey(
    cloud,
    env,
    namespace,
    appName,
    version=kmsKeyVersion,
    keyOverride=namespace_config.flags.githubPrivateKeyInfo
  );
  local kmsAccess = kmsKey.grantSignerAccess(serviceAccount.serviceAccountGcpEmailAddress);

  local config = {
    port: 50051,
    project_id: cloudInfo[cloud].projectId,
    pubsub_subscription_id: subscriber.subscriptionName,
    dead_letter_subscription_id: subscriber.deadLetterSubscriptionName,
    topic_name_template: publisherTopicTemplate,
    server_mtls: if mtls then serverCert.config else null,
    central_server_mtls: if mtls then centralServerCert.config else null,
    client_mtls: if mtls then clientCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    prom_port: 9090,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    content_manager_endpoint: 'content-manager-svc:50051',
    github_state_endpoint: 'github-state-svc:50051',
    settings_endpoint: 'settings-svc:50051',
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    private_key_resource_name: kmsKey.resourceName,
    register_repo_with_latest_commit: env == 'DEV',  // used for github e2e tests
    enable_lag_tracking: true,
    user_authorization_redirect_uri: 'https://%s/githubUserCallback' % endpointsLib.getCustomerUiHostname(env, namespace, cloud),
    oauth_app_secret_path: oauthAppSecret.filePath,
    user_tier: namespace_config.flags.userTier,
    allow_user_token_access_peer_regex: ['^remote-agents-svc$'],
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // creates a container that runs the server
  local container = {
    name: appName,
    target: {
      name: '//services/integrations/github/processor/server:image',
      dst: 'github-processor',
    },
    // the arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
      '--launch-darkly-secrets-file',
      dynamicFeatureFlagConfig.secretsFilePath,
      '--request-insight-publisher-config-file',
      requestInsightPublisher.configFilePath,
    ],
    // ports that the pod exposes
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlagConfig.env,
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      centralClientCert.volumeMountDef,
      centralServerCert.volumeMountDef,
      clientCert.volumeMountDef,
      serverCert.volumeMountDef,
      dynamicFeatureFlagConfig.volumeMountDef,
      requestInsightPublisher.volumeMountDef,
      oauthAppSecret.volumeMountDef,
      {
        mountPath: '/github-app-secret',
        name: appSecret.name,
      },
    ],
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '1Gi',
      },
    },
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc.%s' % namespace,
                                            tls=mtls,
                                            serverCerts=centralServerCert.volumeMountDef.mountPath,
                                            clientCerts=centralClientCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc.%s' % namespace,
                                           tls=mtls,
                                           serverCerts=centralServerCert.volumeMountDef.mountPath,
                                           clientCerts=centralClientCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      // the client certificate is mounted into the pod
      centralClientCert.podVolumeDef,
      // the server certificate is mounted into the pod
      centralServerCert.podVolumeDef,
      // the client certificate is mounted into the pod
      clientCert.podVolumeDef,
      // Add server cert volume
      serverCert.podVolumeDef,
      // the dynamic feature flag config is mounted into the pod
      dynamicFeatureFlagConfig.podVolumeDef,
      requestInsightPublisher.podVolumeDef,
      oauthAppSecret.podVolumeDef,
      {
        name: appSecret.name,
        secret: {
          secretName: appSecret.name,  // pragma: allowlist secret
          optional: false,
        },
      },
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    roles,
    subscriber.objects,
    appSecret.objects,
    oauthAppSecret.objects,
    kmsAccess,
    serverCert.objects,
    centralServerCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    dynamicFeatureFlagConfig.k8s_objects,
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
    publisherIAMPolicy,
    deployment,
    services,
  ])

// Lib for getting information about the github app private key stored in CloudKMS.
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

local keyRingCloud = function(cloud)
  if cloud == 'GCP_US_CENTRAL1_GSC_PROD' then
    // GSC reuses the US keyring.
    'GCP_US_CENTRAL1_PROD'
  else
    cloud
;

local hasKeyRingDeploy = function(cloud)
  // If this keyring uses another cloud's keyring, then no need to deploy a
  // keyring for this cloud.
  keyRingCloud(cloud) == cloud
;

local keyLocation = function(cloud)
  {
    GCP_US_CENTRAL1_DEV: 'us-central1',
    GCP_US_CENTRAL1_PROD: 'us-central1',
    GCP_EU_WEST4_PROD: 'europe-west4',
    // GCP_US_CENTRAL1_GSC_PROD reuses the GCP_US_CENTRAL1_PROD keyring.
  }[cloud]
;

local keyRingName = function(cloud, env, appName)
  local keyCloud = keyRingCloud(cloud);
  local location = keyLocation(keyCloud);
  std.asciiLower('%s-%s-%s-keyring' % [env, location, appName])
;

// Returns an object with utilities for interacting with the github app private key. See comments
// in the object itself for details.
local kmsKey = function(cloud, env, namespace, appName, version, keyOverride)
  local useKeyOverride = env == 'DEV' && keyOverride != {};
  assert !useKeyOverride || (
    std.objectHas(keyOverride, 'name') &&
    std.objectHas(keyOverride, 'version')
  ) : 'GitHub key override is malformed';
  local effectiveVersion = if useKeyOverride then keyOverride.version else version;
  local keyCloud = keyRingCloud(cloud);
  local location = keyLocation(keyCloud);
  local name = std.asciiLower(
    if useKeyOverride then
      keyOverride.name
    else
      '%s-%s-private-key' % [env, appName]
  );
  local resourceName = 'projects/%(projectId)s/locations/%(location)s/keyRings/%(keyRingName)s/cryptoKeys/%(keyName)s/cryptoKeyVersions/%(version)d' % {
    projectId: cloudInfo[keyCloud].projectId,
    location: keyLocation(keyCloud),
    keyRingName: keyRingName(keyCloud, env, appName),
    keyName: name,
    version: effectiveVersion,
  };
  local externalReference = '%(projectId)s/%(location)s/%(keyRingName)s/%(keyName)s' % {
    projectId: cloudInfo[keyCloud].projectId,
    location: keyLocation(keyCloud),
    keyRingName: keyRingName(keyCloud, env, appName),
    keyName: name,
  };
  local grantSignerAccess = function(serviceAccountEmail)
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-private-key-signer-access' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'KMSCryptoKey',
          external: externalReference,
        },
        bindings: [
          {
            role: 'roles/cloudkms.signer',
            members: [{ member: 'serviceAccount:%s' % serviceAccountEmail }],
          },
        ],
      },
    };

  {
    // The name of the key.
    name: name,
    // The full resource name of the key, needed for API requests.
    resourceName: resourceName,
    // A function that takes a service account email and returns an IAMPartialPolicy giving the
    // service account permission to sign with the key.
    grantSignerAccess: grantSignerAccess,
  }
;

{
  hasKeyRingDeploy: hasKeyRingDeploy,
  keyRingName: keyRingName,
  keyLocation: keyLocation,
  kmsKey: kmsKey,
}

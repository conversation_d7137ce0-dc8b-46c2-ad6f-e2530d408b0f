package main

import (
	"context"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/bradleyfalzon/ghinstallation/v2"
	"github.com/google/go-github/v64/github"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
)

var (
	registerGithubOps = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_github_api_ops",
			Help: "Number of github REST API operations performed",
		},
		[]string{"operation", "tenant_name", "status"},
	)

	registerRateLimit = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_github_api_rate_limit_remaining",
			Help: "Number of github REST API calls remaining",
		},
		[]string{"tenant_name"},
	)
)

const sleepDurationForRetries = time.Second * 10

func init() {
	prometheus.MustRegister(
		registerGithubOps,
		registerRateLimit,
	)
}

type GithubClient struct {
	Repositories GithubRepositoriesClient
	App          GithubAppServiceClient
}

type GithubRepositoriesClient interface {
	CompareCommits(ctx context.Context, owner, repo, base, head string, opts *github.ListOptions) (*github.CommitsComparison, *github.Response, error)
	CompareCommitsRaw(ctx context.Context, owner, repo, base, head string, opts github.RawOptions) (string, *github.Response, error)
	GetContents(ctx context.Context, owner, repo, filepath string, opt *github.RepositoryContentGetOptions) (*github.RepositoryContent, []*github.RepositoryContent, *github.Response, error)
	GetCommit(ctx context.Context, owner, repo, sha string, opts *github.ListOptions) (*github.RepositoryCommit, *github.Response, error)
	GetArchiveLink(ctx context.Context, owner, repo string, archiveFormat github.ArchiveFormat, opts *github.RepositoryContentGetOptions, maxRedirects int) (*url.URL, *github.Response, error)
	Get(ctx context.Context, owner, repo string) (*github.Repository, *github.Response, error)
}

type GithubAppServiceClient interface {
	ListRepos(ctx context.Context, opts *github.ListOptions) (*github.ListRepositories, *github.Response, error)
}

type githubClients interface {
	httpClient() *http.Client
	installationClient(tenantName string, installationID int64) GithubClient
}

type githubClientsImpl struct {
	appID         int64
	appsTransport *ghinstallation.AppsTransport

	httpCl                       *http.Client
	githubClientByInstallationID map[int64]GithubClient
	githubClientMutex            sync.RWMutex
}

func newGithubClients(appID int64, tr http.RoundTripper, appsTransport *ghinstallation.AppsTransport) githubClients {
	httpClient := &http.Client{Transport: tr}

	return &githubClientsImpl{
		appID:                        appID,
		appsTransport:                appsTransport,
		httpCl:                       httpClient,
		githubClientByInstallationID: make(map[int64]GithubClient),
	}
}

// Returns a plain http client, whose transport is shared with github clients.
func (gc *githubClientsImpl) httpClient() *http.Client {
	return gc.httpCl
}

// Returns a github client for a specific installation, creating a new one if
// necessary.
func (gc *githubClientsImpl) installationClient(tenantName string, installationID int64) GithubClient {
	gc.githubClientMutex.Lock()
	defer gc.githubClientMutex.Unlock()

	if client, ok := gc.githubClientByInstallationID[installationID]; ok {
		return client
	}

	installationTr := ghinstallation.NewFromAppsTransport(gc.appsTransport, installationID)
	client := github.NewClient(&http.Client{Transport: installationTr})
	githubClient := GithubClient{
		Repositories: &githubRepositoriesClient{
			tenantName: tenantName,
			inner:      client.Repositories,
		},
		App: &githubAppServiceClient{
			tenantName: tenantName,
			inner:      client.Apps,
		},
	}
	gc.githubClientByInstallationID[installationID] = githubClient
	return githubClient
}

type githubAppServiceClient struct {
	tenantName string
	inner      GithubAppServiceClient
}

func (g *githubAppServiceClient) ListRepos(ctx context.Context, opts *github.ListOptions) (*github.ListRepositories, *github.Response, error) {
	repos, resp, err := g.inner.ListRepos(ctx, opts)
	registerGithubOps.WithLabelValues("list_repos", g.tenantName, extractError(err)).Inc()
	if resp != nil {
		registerRateLimit.WithLabelValues(g.tenantName).Set(float64(resp.Rate.Remaining))
	}
	return repos, resp, err
}

type githubRepositoriesClient struct {
	tenantName string
	inner      GithubRepositoriesClient
}

func extractError(err error) string {
	if err == nil {
		return "200 OK"
	}
	if githubErr, ok := err.(*github.ErrorResponse); ok {
		return githubErr.Response.Status
	}
	return "unknown"
}

// Return true on 5xx server errors
func shouldRetry(err error) bool {
	if githubErr, ok := err.(*github.ErrorResponse); ok {
		return githubErr.Response.StatusCode >= 500 && githubErr.Response.StatusCode < 600
	}
	return false
}

func updateMetrics(tenantName string, resp *github.Response, githubErr error, operation string) {
	registerGithubOps.WithLabelValues(operation, tenantName, extractError(githubErr)).Inc()
	if resp != nil {
		registerRateLimit.WithLabelValues(tenantName).Set(float64(resp.Rate.Remaining))
	}
}

func sleepBeforeRetry(ctx context.Context) error {
	log.Ctx(ctx).Info().Msgf("Sleeping for %d seconds before retrying github api call", sleepDurationForRetries)
	select {
	case <-ctx.Done():
		return ctx.Err()
	case <-time.After(sleepDurationForRetries):
		return nil
	}
}

func (g *githubRepositoriesClient) CompareCommits(ctx context.Context, owner, repo, base, head string, opts *github.ListOptions) (*github.CommitsComparison, *github.Response, error) {
	c, resp, err := g.inner.CompareCommits(ctx, owner, repo, base, head, opts)
	if err != nil && shouldRetry(err) {
		updateMetrics(g.tenantName, resp, err, "compare_commits")
		log.Info().Msgf("Retrying CompareCommits Github API call")
		if err := sleepBeforeRetry(ctx); err != nil {
			return nil, nil, err
		}
		c, resp, err = g.inner.CompareCommits(ctx, owner, repo, base, head, opts)
	}
	updateMetrics(g.tenantName, resp, err, "compare_commits")
	return c, resp, err
}

func (g *githubRepositoriesClient) CompareCommitsRaw(ctx context.Context, owner, repo, base, head string, opts github.RawOptions) (string, *github.Response, error) {
	c, resp, err := g.inner.CompareCommitsRaw(ctx, owner, repo, base, head, opts)
	if err != nil && shouldRetry(err) {
		updateMetrics(g.tenantName, resp, err, "compare_commits_raw")
		log.Info().Msgf("Retrying CompareCommitsRaw Github API call")
		if err := sleepBeforeRetry(ctx); err != nil {
			return "", nil, err
		}
		c, resp, err = g.inner.CompareCommitsRaw(ctx, owner, repo, base, head, opts)
	}
	updateMetrics(g.tenantName, resp, err, "compare_commits_raw")
	return c, resp, err
}

func (g *githubRepositoriesClient) GetContents(ctx context.Context, owner, repo, filepath string, opt *github.RepositoryContentGetOptions) (fileContents *github.RepositoryContent, directoryContents []*github.RepositoryContent, resp *github.Response, err error) {
	fileContents, directoryContents, resp, err = g.inner.GetContents(ctx, owner, repo, filepath, opt)
	if err != nil && shouldRetry(err) {
		updateMetrics(g.tenantName, resp, err, "get_contents")
		log.Info().Msgf("Retrying GetContents Github API call")
		if err := sleepBeforeRetry(ctx); err != nil {
			return nil, nil, nil, err
		}
		fileContents, directoryContents, resp, err = g.inner.GetContents(ctx, owner, repo, filepath, opt)
	}
	updateMetrics(g.tenantName, resp, err, "get_contents")
	return fileContents, directoryContents, resp, err
}

func (g *githubRepositoriesClient) GetCommit(ctx context.Context, owner, repo, sha string, opts *github.ListOptions) (*github.RepositoryCommit, *github.Response, error) {
	c, resp, err := g.inner.GetCommit(ctx, owner, repo, sha, opts)
	if err != nil && shouldRetry(err) {
		updateMetrics(g.tenantName, resp, err, "get_commit")
		log.Info().Msgf("Retrying GetCommit Github API call")
		if err := sleepBeforeRetry(ctx); err != nil {
			return nil, nil, err
		}
		c, resp, err = g.inner.GetCommit(ctx, owner, repo, sha, opts)
	}
	updateMetrics(g.tenantName, resp, err, "get_commit")
	return c, resp, err
}

func (g *githubRepositoriesClient) GetArchiveLink(ctx context.Context, owner, repo string, archiveFormat github.ArchiveFormat, opts *github.RepositoryContentGetOptions, maxRedirects int) (*url.URL, *github.Response, error) {
	c, resp, err := g.inner.GetArchiveLink(ctx, owner, repo, archiveFormat, opts, maxRedirects)
	if err != nil && shouldRetry(err) {
		updateMetrics(g.tenantName, resp, err, "get_archive_link")
		log.Info().Msgf("Retrying GetArchiveLink Github API call")
		if err := sleepBeforeRetry(ctx); err != nil {
			return nil, nil, err
		}
		c, resp, err = g.inner.GetArchiveLink(ctx, owner, repo, archiveFormat, opts, maxRedirects)
	}
	updateMetrics(g.tenantName, resp, err, "get_archive_link")
	return c, resp, err
}

func (g *githubRepositoriesClient) Get(ctx context.Context, owner, repo string) (*github.Repository, *github.Response, error) {
	c, resp, err := g.inner.Get(ctx, owner, repo)
	if err != nil && shouldRetry(err) {
		updateMetrics(g.tenantName, resp, err, "get")
		log.Info().Msgf("Retrying Get Github API call")
		if err := sleepBeforeRetry(ctx); err != nil {
			return nil, nil, err
		}
		c, resp, err = g.inner.Get(ctx, owner, repo)
	}
	updateMetrics(g.tenantName, resp, err, "get")
	return c, resp, err
}

package main

import (
	"archive/tar"
	"context"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	gitignore "github.com/denormal/go-gitignore"
	"github.com/rs/zerolog/log"
)

const (
	augmentignoreFilename = ".augmentignore"
	gitignoreFilename     = ".gitignore"
)

var keyishRE = regexp.MustCompile("^(\\.git|.*\\.pem|.*\\.key|.*\\.pfx|.*\\.p12|.*\\.jks|.*\\.keystore|.*\\.pkcs12|.*\\.crt|.*\\.cer|id_rsa|id_ed25519|id_ecdsa|id_dsa)$")

// Paths to always ignore, regardless of any ignore files.
func alwaysIgnorePath(path string) bool {
	// The go-github library calls out a vulnerability with any path containing
	// "..", and GetContents() fails on those paths.
	// I couldn't find any detail on that vulnerability, but I didn't want to
	// remove it without a good reason, so ignore them instead.
	// TODO: It's a bit messy to add this to the path filter, because this is
	// not related at all to our normal ignore rules, but it was the easiest
	// implementation-wise.
	// https://github.com/google/go-github/blob/98d4f502e28c1de670e319f18a219cfe98dfe4cd/github/repos_contents.go#L218C14-L218C80
	// https://github.com/google/go-github/pull/2805
	return strings.Contains(path, "..")
}

// We have found that the gitignore library we use crashes on some inputs.
func newGitIgnore(ctx context.Context, reader io.Reader, dir string) (ignore gitignore.GitIgnore) {
	logErrors := func(err gitignore.Error) bool {
		log.Ctx(ctx).Info().Err(err).Msgf("Ignoring error in %s", dir)
		return true
	}
	defer func() {
		if r := recover(); r != nil {
			log.Ctx(ctx).Error().Msgf("Panic in gitignore.New: %v", r)
			// Return a new, empty gitignore
			// TODO: If we run into a bad pattern, ignore just that line and not
			// the whole ignore file
			ignore = gitignore.New(strings.NewReader(""), dir, logErrors)
		}
	}()
	ignore = gitignore.New(reader, dir, logErrors)
	return
}

// This interface is simplified for the way that the github event handler will
// be using it. Unlike git, we are mostly not walking files in a directory
// structure, we are either reading them from a tarball or viewing diffs. So we
// let this class do the hard work of walking directories for us, and provide a
// simple interface to the caller.
type PathFilter interface {
	// Returns true if we should ignore this file.
	IgnoreFile(path string) bool
}

// Assume we have a static set of ignores for each instance of this object that
// cannot be changed after the object is created. This means we can cache ignore
// decisions for paths.
type pathFilterImpl struct {
	// Map of path to augmentignore/gitignore. The root ones have a path of "".
	augmentignores map[string]gitignore.GitIgnore
	gitignores     map[string]gitignore.GitIgnore

	ignoreCache map[string]bool
}

// This is the way to create a path filter when we have the entire contents of
// the repo, currently assumed to be in the form of a tarball. The returned
// filter will have ignore rules for the whole repo.
func newPathFilterFromTarball(ctx context.Context, reader *tar.Reader) (PathFilter, error) {
	pathFilter := &pathFilterImpl{
		augmentignores: make(map[string]gitignore.GitIgnore),
		gitignores:     make(map[string]gitignore.GitIgnore),
		ignoreCache:    make(map[string]bool),
	}

	for {
		header, err := reader.Next()
		if errors.Is(err, io.EOF) {
			break
		} else if err != nil {
			return nil, fmt.Errorf("failed to read tarball: %w", err)
		}

		// Remove the tarball name file prefix to access files with the path we expect
		tarballPath := strings.SplitN(header.Name, "/", 2)
		var filePath string
		if len(tarballPath) > 1 {
			filePath = tarballPath[1]
		}

		if alwaysIgnorePath(filePath) {
			continue
		}

		dir, file := filepath.Split(filePath)
		if file != augmentignoreFilename && file != gitignoreFilename {
			continue
		}

		if header.Typeflag != tar.TypeReg {
			log.Ctx(ctx).Info().Msgf("Ignoring non-regular ignorefile, type %d", header.Typeflag)
			continue
		}

		// Read the ignore file
		// Match the format of getAllDirsForFile
		dir = strings.TrimSuffix(dir, "/")
		ignore := newGitIgnore(ctx, reader, dir)
		if file == augmentignoreFilename {
			pathFilter.augmentignores[dir] = ignore
		} else {
			pathFilter.gitignores[dir] = ignore
		}
	}
	log.Ctx(ctx).Info().Msgf("Created ignore stack from tarball with %d augmentignores and %d gitignores", len(pathFilter.augmentignores), len(pathFilter.gitignores))
	return pathFilter, nil
}

// Given a map of DiffInfos, return a new map of DiffInfos with the locations of
// any possibly relevant ignore files. If an ignore file itself has changed, do
// not return a map of diff infos and only return ignoreFileChanged=true.
func getIgnoreDiffInfos(normalDiffInfos map[string]*DiffInfo) (ignoreDiffInfos map[string]*DiffInfo, ignoreFileChanged bool) {
	// First check for reuploads
	for filename := range normalDiffInfos {
		base := filepath.Base(filename)
		if base == augmentignoreFilename || base == gitignoreFilename {
			return nil, true
		}
	}

	// Now return a list of ignore files to check for - one at each directory
	// from the root to the one containing the file.
	ignoreDiffInfos = make(map[string]*DiffInfo)
	for filename := range normalDiffInfos {
		for _, dir := range getAllDirsForFile(filename) {
			if alwaysIgnorePath(dir) {
				continue
			}
			for _, ignoreFile := range []string{augmentignoreFilename, gitignoreFilename} {
				filepath := filepath.Join(dir, ignoreFile)
				if _, ok := ignoreDiffInfos[filepath]; !ok {
					ignoreDiffInfos[filepath] = &DiffInfo{
						filename:     filepath,
						isIgnoreFile: true,
					}
				}
			}
		}
	}

	return ignoreDiffInfos, false
}

// This is the way to get a path filter when we are only interested in a
// specific set of files, as in a map of DiffInfos. First, the caller should
// call getIgnoreDiffInfos to get a list of ignore files to download. Once those
// files are downloaded, at least the ones that exist, into tmpDir, this can be
// called to create a path filter with relevant ignore rules.
func newPathFilterFromDiffInfos(ctx context.Context, ignoreDiffInfos map[string]*DiffInfo, tmpDir string) (PathFilter, error) {
	pathFilter := &pathFilterImpl{
		augmentignores: make(map[string]gitignore.GitIgnore),
		gitignores:     make(map[string]gitignore.GitIgnore),
		ignoreCache:    make(map[string]bool),
	}
	for filename := range ignoreDiffInfos {
		dir, file := filepath.Split(filename)
		reader, err := os.Open(filepath.Join(tmpDir, filename))
		if err != nil {
			if errors.Is(err, fs.ErrNotExist) {
				// getIgnoreDiffInfos checks for ignore files at every
				// directory, so it is expected that some of them won't actually
				// exist in the repo
				continue
			}
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to open %s", filename)
			return nil, err
		}
		// Match the format of getAllDirsForFile
		dir = strings.TrimSuffix(dir, "/")
		ignore := newGitIgnore(ctx, reader, dir)
		if file == augmentignoreFilename {
			pathFilter.augmentignores[dir] = ignore
		} else if file == gitignoreFilename {
			pathFilter.gitignores[dir] = ignore
		} else {
			log.Ctx(ctx).Error().Msgf("unexpected ignore file %s", filename)
			return nil, fmt.Errorf("unexpected ignore file %s", filename)
		}
	}
	log.Ctx(ctx).Info().Msgf("Created ignore stack from diff infos with %d augmentignores and %d gitignores", len(pathFilter.augmentignores), len(pathFilter.gitignores))
	return pathFilter, nil
}

func (pf *pathFilterImpl) ignorePath(path string, isdir bool) bool {
	// allow the root directory
	if isdir {
		if path == "" || path == "/" || path == "." {
			return false
		}
	}
	// If we have already checked this path, then return the cached result.
	if lastIgnore, ok := pf.ignoreCache[path]; ok {
		return lastIgnore
	}

	dir := filepath.Dir(path)

	// If the parent is ignored, then ignore this path.
	if pf.ignorePath(dir, true) {
		return true
	}

	if alwaysIgnorePath(path) {
		return true
	}

	// Otherwise, check the ignore files, starting with the one in the lowest
	// (closest) directory, and moving up.
	parentPaths := getAllDirsForFile(path)
	checkIgnores := func(ignores map[string]gitignore.GitIgnore) gitignore.Match {
		for i := len(parentPaths) - 1; i >= 0; i-- {
			parent := parentPaths[i]
			if ignore, ok := ignores[parent]; ok {
				if match := ignore.Relative(path, isdir); match != nil {
					return match
				}
			}
		}
		return nil
	}

	// First, check the augmentignore files
	if match := checkIgnores(pf.augmentignores); match != nil {
		pf.ignoreCache[path] = match.Ignore()
		return match.Ignore()
	}

	// Ignore special "keyish" paths, similar to what the extension does in
	// ignore-file.ts
	if !isdir && keyishRE.MatchString(path) {
		pf.ignoreCache[path] = true
		return true
	}

	// Now check gitignores
	if match := checkIgnores(pf.gitignores); match != nil {
		pf.ignoreCache[path] = match.Ignore()
		return match.Ignore()
	}

	// No matches means we include the file by default
	pf.ignoreCache[path] = false
	return false
}

func (pf *pathFilterImpl) IgnoreFile(path string) bool {
	path = filepath.Clean(path)
	return pf.ignorePath(path, false)
}

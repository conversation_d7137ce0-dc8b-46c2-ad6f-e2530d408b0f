package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"regexp"
	"strconv"
	"strings"
	"syscall"

	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	"github.com/augmentcode/augment/services/integrations/github/processor/processorpb"
	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/augmentcode/augment/services/lib/pubsub"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	settingsclient "github.com/augmentcode/augment/services/settings/client"
	settingsservice "github.com/augmentcode/augment/services/settings/client"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/bradleyfalzon/ghinstallation/v2"
	jwt "github.com/golang-jwt/jwt/v4"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.opentelemetry.io/otel"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var (
	configFile                    = flag.String("config", "", "Path to config file")
	requestInsightPublisherConfig = flag.String("request-insight-publisher-config-file", "", "Path to request insight publisher config file")
	launchDarklySecretsFile       = flag.String("launch-darkly-secrets-file", "", "Path to launch darkly secrets file")
	processDeadLetterQueueFlag    = featureflags.NewBoolFlag("github_process_dead_letter_queue", false)

	tracer = otel.Tracer("github-processor")
)

type Config struct {
	Port int `json:"port"`

	// TLS configuration
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	CentralServerMtls *tlsconfig.ServerConfig `json:"central_server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	TokenExchangeEndpoint  string `json:"token_exchange_endpoint"`
	TenantWatcherEndpoint  string `json:"tenant_watcher_endpoint"`
	ContentManagerEndpoint string `json:"content_manager_endpoint"`
	GithubStateEndpoint    string `json:"github_state_endpoint"`
	SettingsEndpoint       string `json:"settings_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	ProjectID                string `json:"project_id"`
	PubSubSubscriptionID     string `json:"pubsub_subscription_id"`
	DeadLetterSubscriptionID string `json:"dead_letter_subscription_id"`
	TopicNameTemplate        string `json:"topic_name_template"`

	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`

	// Full resource name of the CloudKMS key to sign JWTs with.
	PrivateKeyResourceName string `json:"private_key_resource_name"`

	// Whether we are in a test environment for e2e testing.
	RegisterRepoWithLatestCommit bool `json:"register_repo_with_latest_commit"`

	EnableLagTracking bool `json:"enable_lag_tracking"`

	// The customer-ui link that users should get redirected to after
	// authorizing with github. Note that this is for user authorization, not
	// app install.
	UserAuthorizationRedirectUri string `json:"user_authorization_redirect_uri"`

	OauthAppSecretPath string `json:"oauth_app_secret_path"`

	// The user tier of the namespace. This matches the values in public_api.proto UserTier. Used to determine whether to allow installing the Github App
	UserTier string `json:"user_tier"`

	AllowUserTokenAccessPeerRegex []string `json:"allow_user_token_access_peer_regex"`
}

func asyncRun(runFn func() error) chan error {
	errChan := make(chan error)
	go func() {
		defer close(errChan)
		errChan <- runFn()
	}()
	return errChan
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	appIDRaw, err := os.ReadFile("/github-app-secret/app_id")
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading github app ID secret")
	}
	appID, err := strconv.ParseInt(string(appIDRaw), 10, 64)
	if err != nil {
		log.Fatal().Err(err).Msg("Error parsing app_id")
	}

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		log.Fatal().Msg("POD_NAMESPACE environment variable must be set.")
	}

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	contentManagerClient, err := contentmanagerclient.NewContentManagerClient(
		config.ContentManagerEndpoint, clientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating content manager client")
	}
	defer contentManagerClient.Close()

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, namespace)
	defer tenantCache.Close()

	// Setup request insight publisher.
	requestInsightPublisher, err := ripublisher.NewRequestInsightPublisherFromFile(
		ctx, *requestInsightPublisherConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating request insight publisher")
	}
	defer requestInsightPublisher.Close()

	featureFlagsHandle, err := featureflags.NewFeatureFlagHandleFromFile(*launchDarklySecretsFile,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	subscribeClient, err := pubsub.NewSubscribeClient(ctx, &pubsub.SubscribeClientConfig{
		ProjectId:                config.ProjectID,
		SubscriptionId:           config.PubSubSubscriptionID,
		DeadLetterSubscriptionId: config.DeadLetterSubscriptionID,
		ConfigureDeadLetterHandling: func() bool {
			return checkDeadLetterFlag(featureFlagsHandle)
		},
	})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating pubsub client")
	}
	defer subscribeClient.Close()

	publishClient, err := pubsub.NewPublishClient(context.Background(), config.ProjectID, config.TopicNameTemplate)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating pubsub client")
	}
	defer publishClient.Close()

	signer, err := NewKMSSigner(ctx, config.PrivateKeyResourceName, jwt.SigningMethodRS256)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating signer")
	}
	defer signer.Close()

	githubStateClient, err := githubstateclient.NewGithubStateClient(config.GithubStateEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating github state client")
		os.Exit(1)
	}
	defer githubStateClient.Close()

	settingsClient, err := settingsclient.NewSettingsClient(config.SettingsEndpoint, clientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create settings client")
	}
	defer settingsClient.Close()

	tr := http.DefaultTransport
	atr, err := ghinstallation.NewAppsTransportWithOptions(
		tr, appID, ghinstallation.WithSigner(signer),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating apps transport")
	}
	githubClients := newGithubClients(appID, tr, atr)

	dynClient, err := webhookmapping.CreateDynamicClient()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create dynamic client")
	}
	// TODO: support EU clusters here too, which will have to map to the US cluster
	webhookMappingResource := webhookmapping.NewWebhookTenantMappingResource(dynClient, namespace)

	lagTracker := newLagTracker(tenantCache, githubStateClient, tokenExchangeClient, githubClients, webhookMappingResource, config.EnableLagTracking)

	// Parse the UserTier string from config to the enum value
	userTierValue, exists := publicapiproto.GetModelsResponse_UserTier_value[config.UserTier]
	if !exists {
		log.Fatal().Msgf("Invalid user tier: %s", config.UserTier)
	}
	userTier := publicapiproto.GetModelsResponse_UserTier(userTierValue)

	if userTier == publicapiproto.GetModelsResponse_ENTERPRISE_TIER {
		go lagTracker.StartAllLagTrackers()
	}

	eventHandler := NewGithubEventHandler(
		requestInsightPublisher, contentManagerClient, tokenExchangeClient,
		githubClients, featureFlagsHandle, githubStateClient, config.RegisterRepoWithLatestCommit,
		webhookMappingResource, settingsClient, lagTracker,
	)

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		cancel()
	}()

	// Only process GitHub events for enterprise tier - others should not even have the github app installed
	var receiveErrChan chan error
	if userTier == publicapiproto.GetModelsResponse_ENTERPRISE_TIER {
		receiveErrChan = asyncRun(func() error {
			log.Info().Msg("Starting pubsub receive for enterprise tier")
			return subscribeClient.Receive(ctx, eventHandler.HandleGithubEvent)
		})
	} else {
		// Create a dummy channel that never returns for non-enterprise tiers
		receiveErrChan = make(chan error)
		log.Info().Msgf("Skipping pubsub receive for non-enterprise tier: %s", config.UserTier)
	}
	grpcErrChan := asyncRun(func() error {
		log.Info().Msg("Starting grpc server")
		return runGrpcServer(ctx, &config, tokenExchangeClient, githubClients, namespace, lagTracker, webhookMappingResource, requestInsightPublisher, settingsClient, publishClient, userTier)
	})

	// These should run forever
	select {
	case err, done := <-receiveErrChan:
		if done {
			log.Info().Msg("Pubsub receive done")
		} else {
			log.Fatal().Err(err).Msg("Error receiving pubsub messages")
		}
	case err, done := <-grpcErrChan:
		if done {
			log.Info().Msg("Grpc server done")
		} else {
			log.Fatal().Err(err).Msg("Error running grpc server")
		}
	}
}

func runGrpcServer(ctx context.Context, config *Config, tokenExchangeClient tokenexchange.TokenExchangeClient, githubClients githubClients, namespace string, lagTracker lagTracker, webhookMappingResource webhookmapping.WebhookTenantMappingResource, requestInsightPublisher ripublisher.RequestInsightPublisher, settingsServiceClient settingsservice.SettingsClient, publishClient pubsub.PublishClient, userTier publicapiproto.GetModelsResponse_UserTier) error {
	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls, config.CentralServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	clientID, err := os.ReadFile("/github-app-secret/client_id")
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading github app client ID secret")
	}
	clientSecret, err := os.ReadFile("/github-app-secret/client_secret")
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading github app client secret")
	}

	oauthSecretJson, err := os.ReadFile(config.OauthAppSecretPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading github oauth secret")
	}
	var oauthSecrets struct {
		ClientID     string `json:"client_id"`
		ClientSecret string `json:"client_secret"`
	}
	if err := json.Unmarshal(oauthSecretJson, &oauthSecrets); err != nil {
		log.Fatal().Err(err).Msg("Error unmarshalling github oauth secret")
	}

	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	httpClient := &http.Client{}

	allowUserTokenAccessPeerRegex := regexp.MustCompile(strings.Join(config.AllowUserTokenAccessPeerRegex, "|"))

	server := &githubProcessorServer{
		githubAppSecrets: appSecrets{
			clientID:     secretstring.New(string(clientID)),
			clientSecret: secretstring.New(string(clientSecret)),
		},
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New(oauthSecrets.ClientID),
			clientSecret: secretstring.New(oauthSecrets.ClientSecret),
		},
		userAuthorizationRedirectUri:  config.UserAuthorizationRedirectUri,
		githubClients:                 githubClients,
		webhookTenantMappingResource:  webhookMappingResource,
		httpClient:                    httpClient,
		lagTracker:                    lagTracker,
		requestInsightPublisher:       requestInsightPublisher,
		settingsServiceClient:         settingsServiceClient,
		publishClient:                 publishClient,
		userTier:                      userTier,
		allowUserTokenAccessPeerRegex: allowUserTokenAccessPeerRegex,
	}

	go func() {
		select {
		case <-ctx.Done():
			grpcServer.GracefulStop()
		}
	}()

	processorpb.RegisterGithubProcessorServer(grpcServer, server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())
	err = grpcServer.Serve(lis)
	if err != nil && err != grpc.ErrServerStopped {
		log.Fatal().Err(err).Msg("Error serving")
	}
	log.Info().Msg("gRPC server closed")
	return nil
}

func checkDeadLetterFlag(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := processDeadLetterQueueFlag.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading dead letter queue feature flag")
		return false
	}
	return val
}

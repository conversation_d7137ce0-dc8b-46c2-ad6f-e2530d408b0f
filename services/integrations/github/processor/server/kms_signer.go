package main

import (
	"context"
	"encoding/base64"
	"fmt"

	kms "cloud.google.com/go/kms/apiv1"
	"cloud.google.com/go/kms/apiv1/kmspb"
	jwt "github.com/golang-jwt/jwt/v4"
)

// KMSSigner is an implementation of ghinstallation's Signer interface that uses Google Cloud KMS to
// sign JWTs.
type KMSSigner struct {
	kmsClient     *kms.KeyManagementClient
	keyName       string
	signingMethod *jwt.SigningMethodRSA
}

func NewKMSSigner(
	ctx context.Context,
	keyName string,
	signingMethod *jwt.SigningMethodRSA,
) (*KMSSigner, error) {
	kmsClient, err := kms.NewKeyManagementClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("Failed to create KMS client: %w", err)
	}

	return &KMSSigner{
		kmsClient:     kmsClient,
		keyName:       keyName,
		signingMethod: signingMethod,
	}, nil
}

func (s *KMSSigner) Close() {
	s.kmsClient.Close()
}

func (s *KMSSigner) Sign(claims jwt.Claims) (string, error) {
	// The KMS library requires a context, but ghinstallation's interface doesn't have one, so we
	// unfortunately can't pass the context in.
	ctx := context.Background()

	token := jwt.NewWithClaims(s.signingMethod, claims)
	// This is the first two parts of the JWT (https://jwt.io/introduction)
	signingString, err := token.SigningString()
	if err != nil {
		return "", fmt.Errorf("Failed to get token's signing string: %w", err)
	}

	req := kmspb.AsymmetricSignRequest{
		Name: s.keyName,
		Data: []byte(signingString),
	}
	resp, err := s.kmsClient.AsymmetricSign(ctx, &req)
	if err != nil {
		return "", fmt.Errorf("Failed to sign JWT: %w", err)
	}

	// This is the third part of the JWT, put them all together to get the full
	// thing
	signature := base64.RawURLEncoding.EncodeToString(resp.Signature)
	signedJWT := fmt.Sprintf("%s.%s", signingString, signature)

	return signedJWT, nil
}

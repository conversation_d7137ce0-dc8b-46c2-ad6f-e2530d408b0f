local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local errorsSpec = {
    displayName: 'Github Processor Message Errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace,cluster)(increase(au_github_processor_pubsub_message{status!="ok",status!="403 Forbidden"}[15m])) > 12
      |||,
    },
  };

  // Ideally there are never any messages in the dead letter queue, so alert on a non-empty queue.
  local deadLetterQueueSpec = {
    displayName: 'Github Processor dead letter queue',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (subscription_id) (pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~".*-github-processor-.*deadletter-sub$",
        }) > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      errorsSpec,
      'github-processor-message-errors',
      'Github Processor message errors',
      team='extended-context'
    ),
    monitoringLib.alertPolicy(cloud, deadLetterQueueSpec, 'github-processor-dead-letter-queue', 'Github processor has messages in dead letter queue %s' % monitoringLib.label('subscription_id'), team='extended-context'),
  ]

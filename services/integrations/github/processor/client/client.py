"""A Python client library for the Github Processor server service."""

import logging
from typing import List, Optional

import grpc

from base.python.grpc import client_options
from services.integrations.github.processor import processor_pb2, processor_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> processor_pb2_grpc.GithubProcessorStub:
    """Setup the client stub for the Github Processor service.

    Args:
        endpoint: The endpoint of the Github Processor service.
        credentials: The credentials to use for the channel (optional)
        options: Additional gRPC channel options (optional)

    Returns:
        The client stub for the Github Processor service.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, options or [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = processor_pb2_grpc.GithubProcessorStub(channel)
    return stub


class GithubProcessorClient:
    """Client for interacting with the Github Processor service."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        self.stub = setup_stub(endpoint, credentials, options=options)

    def call_github_api(
        self,
        request_context: RequestContext,
        request: processor_pb2.GithubApiRequest,
        timeout: float = 30,
    ) -> processor_pb2.GithubApiResponse:
        """Call the GitHub API with the given request

        Args:
            request_context: The request context to use.
            request: The request to send.
            timeout: The timeout in seconds.

        Returns:
            The response from the GitHub API.
        """
        response = self.stub.CallGithubApi(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def is_user_oauth_configured(
        self,
        request_context: RequestContext,
        request: processor_pb2.IsUserOAuthConfiguredRequest,
        timeout: float = 30,
    ) -> processor_pb2.IsUserOAuthConfiguredResponse:
        """Check if Github User OAuth is configured

        Args:
            request_context: The request context to use.
            request: The request to send.
            timeout: The timeout in seconds.

        Returns:
            The response from the GitHub API.
        """
        response = self.stub.IsUserOAuthConfigured(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.is_configured

    def get_github_user_oauth_url(
        self,
        request_context: RequestContext,
        request: processor_pb2.GetGithubUserOAuthUrlRequest,
        timeout: float = 30,
    ) -> str:
        """Get the GitHub User OAuth URL

        Args:
            request_context: The request context to use.
            request: The request to send.
            timeout: The timeout in seconds.

        Returns:
            The response from the GitHub API.
        """
        response = self.stub.GetGithubUserOAuthUrl(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.oauth_url

    def revoke_oauth_grant(
        self,
        request_context: RequestContext,
        request: processor_pb2.RevokeOAuthGrantRequest,
        timeout: float = 30,
    ) -> processor_pb2.RevokeOAuthGrantResponse:
        """Revoke the OAuth grant for the current user

        Args:
            request_context: The request context to use.
            request: The request to send.
            timeout: The timeout in seconds.

        Returns:
            The response indicating success or failure of the revocation.
        """
        response = self.stub.RevokeOAuthGrant(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def get_github_user_info(
        self,
        request_context: RequestContext,
        request: processor_pb2.GetGithubUserInfoRequest,
        timeout: float = 30,
    ) -> processor_pb2.GetGithubUserInfoResponse:
        """Get GitHub user information for the authenticated user

        Args:
            request_context: The request context to use.
            request: The request to send.
            timeout: The timeout in seconds.

        Returns:
            The response containing GitHub user information.
        """
        response = self.stub.GetGithubUserInfo(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

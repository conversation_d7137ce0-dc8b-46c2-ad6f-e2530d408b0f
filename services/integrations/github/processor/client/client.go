package client

import (
	"context"
	"fmt"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"github.com/augmentcode/augment/base/go/secretstring"
	pb "github.com/augmentcode/augment/services/integrations/github/processor/processorpb"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/stretchr/testify/mock"
)

type GithubProcessorClient interface {
	GetGithubUserToken(ctx context.Context, requestContext *requestcontext.RequestContext, repoOwner, repoName string) (token secretstring.SecretString, login secretstring.SecretString, email secretstring.SecretString, name secretstring.SecretString, err error)
}

type githubProcessorClientImpl struct {
	conn   *grpc.ClientConn
	client pb.GithubProcessorClient
}

var _ GithubProcessorClient = (*githubProcessorClientImpl)(nil)

func New(endpoint string, grpcOpts ...grpc.DialOption) (GithubProcessorClient, error) {
	grpcOpts = append(grpcOpts, grpc.WithStatsHandler(otelgrpc.NewClientHandler()))
	conn, err := grpc.NewClient(endpoint, grpcOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to github-processor service: %v", err)
	}

	client := pb.NewGithubProcessorClient(conn)
	return &githubProcessorClientImpl{
		conn:   conn,
		client: client,
	}, nil
}

// Uses the user ID from the request context
func (c *githubProcessorClientImpl) GetGithubUserToken(ctx context.Context, requestContext *requestcontext.RequestContext, repoOwner, repoName string) (token secretstring.SecretString, login secretstring.SecretString, email secretstring.SecretString, name secretstring.SecretString, err error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetGithubUserToken(ctx, &pb.GetGithubUserTokenRequest{
		Repo: &pb.GithubRepo{
			Owner: repoOwner,
			Name:  repoName,
		},
	})
	if err != nil {
		return secretstring.SecretString{}, secretstring.SecretString{}, secretstring.SecretString{}, secretstring.SecretString{}, err
	}
	return secretstring.New(resp.Token), secretstring.New(resp.Login), secretstring.New(resp.Email), secretstring.New(resp.Name), nil
}

type MockGithubProcessorClient struct {
	mock.Mock
}

var _ GithubProcessorClient = (*MockGithubProcessorClient)(nil)

func (c *MockGithubProcessorClient) GetGithubUserToken(ctx context.Context, requestContext *requestcontext.RequestContext, repoOwner, repoName string) (token secretstring.SecretString, login secretstring.SecretString, email secretstring.SecretString, name secretstring.SecretString, err error) {
	args := c.Called(ctx, requestContext, repoOwner, repoName)
	return args.Get(0).(secretstring.SecretString), args.Get(1).(secretstring.SecretString), args.Get(2).(secretstring.SecretString), args.Get(3).(secretstring.SecretString), args.Error(4)
}

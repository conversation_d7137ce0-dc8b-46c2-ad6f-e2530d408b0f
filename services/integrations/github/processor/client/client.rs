use std::sync::Arc;

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;

use request_context::RequestContext;
use tracing_tonic::client::TracingService;

pub mod google {
    pub mod rpc {
        #[derive(Clone, PartialEq, ::prost::Message)]
        pub struct Status {
            #[prost(int32, tag = "1")]
            pub code: i32,
            #[prost(string, tag = "2")]
            pub message: ::prost::alloc::string::String,
            #[prost(message, repeated, tag = "3")]
            pub details: ::prost::alloc::vec::Vec<::prost_wkt_types::Any>,
        }
    }
}

pub mod github_processor {
    tonic::include_proto!("github_processor");
}

#[async_trait]
pub trait GithubProcessorClient: Send + Sync {
    async fn list_github_repos_for_authenticated_user(
        &self,
        request_context: &RequestContext,
        request: github_processor::ListGithubReposForAuthenticatedUserRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::ListGithubReposForAuthenticatedUserResponse, tonic::Status>;

    async fn list_branches_for_repo(
        &self,
        request_context: &RequestContext,
        request: github_processor::ListGithubRepoBranchesRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::ListGithubRepoBranchesResponse, tonic::Status>;

    async fn get_repo(
        &self,
        request_context: &RequestContext,
        request: github_processor::GetRepoRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::GetRepoResponse, tonic::Status>;

    async fn is_user_oauth_configured(
        &self,
        request_context: &RequestContext,
        request: github_processor::IsUserOAuthConfiguredRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::IsUserOAuthConfiguredResponse, tonic::Status>;

    async fn create_pull_request(
        &self,
        request_context: &RequestContext,
        request: github_processor::CreatePullRequestRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::CreatePullRequestResponse, tonic::Status>;
}

pub struct GithubProcessorClientImpl {
    endpoint: String,
    tls_config: Option<tonic::transport::ClientTlsConfig>,
    client: Arc<
        Mutex<
            Option<
                github_processor::github_processor_client::GithubProcessorClient<TracingService>,
            >,
        >,
    >,
}

impl GithubProcessorClientImpl {
    pub fn new(endpoint: &str, tls_config: Option<tonic::transport::ClientTlsConfig>) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        github_processor::github_processor_client::GithubProcessorClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            Some(c) => Ok(c.clone()),
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client =
                    github_processor::github_processor_client::GithubProcessorClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
        }
    }
}

#[async_trait]
impl GithubProcessorClient for GithubProcessorClientImpl {
    async fn list_github_repos_for_authenticated_user(
        &self,
        request_context: &RequestContext,
        request: github_processor::ListGithubReposForAuthenticatedUserRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::ListGithubReposForAuthenticatedUserResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!(
                "github processor client to {} not ready: {}",
                self.endpoint,
                e
            );
            tonic::Status::unavailable("github processor not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client
            .list_github_repos_for_authenticated_user(request)
            .await?;
        Ok(response.into_inner())
    }

    async fn list_branches_for_repo(
        &self,
        request_context: &RequestContext,
        request: github_processor::ListGithubRepoBranchesRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::ListGithubRepoBranchesResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!(
                "github processor client to {} not ready: {}",
                self.endpoint,
                e
            );
            tonic::Status::unavailable("github processor not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.list_branches_for_repo(request).await?;
        Ok(response.into_inner())
    }

    async fn get_repo(
        &self,
        request_context: &RequestContext,
        request: github_processor::GetRepoRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::GetRepoResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!(
                "github processor client to {} not ready: {}",
                self.endpoint,
                e
            );
            tonic::Status::unavailable("github processor not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.get_repo(request).await?;
        Ok(response.into_inner())
    }

    async fn is_user_oauth_configured(
        &self,
        request_context: &RequestContext,
        request: github_processor::IsUserOAuthConfiguredRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::IsUserOAuthConfiguredResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!(
                "github processor client to {} not ready: {}",
                self.endpoint,
                e
            );
            tonic::Status::unavailable("github processor not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.is_user_o_auth_configured(request).await?;
        Ok(response.into_inner())
    }

    async fn create_pull_request(
        &self,
        request_context: &RequestContext,
        request: github_processor::CreatePullRequestRequest,
        timeout: std::time::Duration,
    ) -> Result<github_processor::CreatePullRequestResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!(
                "github processor client to {} not ready: {}",
                self.endpoint,
                e
            );
            tonic::Status::unavailable("github processor not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(timeout);
        request_context.annotate(request.metadata_mut());
        let response = client.create_pull_request(request).await?;
        Ok(response.into_inner())
    }
}

pub struct MockGithubProcessorClient;

impl Default for MockGithubProcessorClient {
    fn default() -> Self {
        Self::new()
    }
}

impl MockGithubProcessorClient {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl GithubProcessorClient for MockGithubProcessorClient {
    async fn list_github_repos_for_authenticated_user(
        &self,
        _request_context: &RequestContext,
        _request: github_processor::ListGithubReposForAuthenticatedUserRequest,
        _timeout: std::time::Duration,
    ) -> Result<github_processor::ListGithubReposForAuthenticatedUserResponse, tonic::Status> {
        Err(tonic::Status::unavailable("github processor not ready"))
    }

    async fn list_branches_for_repo(
        &self,
        _request_context: &RequestContext,
        _request: github_processor::ListGithubRepoBranchesRequest,
        _timeout: std::time::Duration,
    ) -> Result<github_processor::ListGithubRepoBranchesResponse, tonic::Status> {
        Err(tonic::Status::unavailable("github processor not ready"))
    }

    async fn get_repo(
        &self,
        _request_context: &RequestContext,
        _request: github_processor::GetRepoRequest,
        _timeout: std::time::Duration,
    ) -> Result<github_processor::GetRepoResponse, tonic::Status> {
        Err(tonic::Status::unavailable("github processor not ready"))
    }

    async fn is_user_oauth_configured(
        &self,
        _request_context: &RequestContext,
        _request: github_processor::IsUserOAuthConfiguredRequest,
        _timeout: std::time::Duration,
    ) -> Result<github_processor::IsUserOAuthConfiguredResponse, tonic::Status> {
        Err(tonic::Status::unavailable("github processor not ready"))
    }

    async fn create_pull_request(
        &self,
        _request_context: &RequestContext,
        _request: github_processor::CreatePullRequestRequest,
        _timeout: std::time::Duration,
    ) -> Result<github_processor::CreatePullRequestResponse, tonic::Status> {
        Err(tonic::Status::unavailable("github processor not ready"))
    }
}

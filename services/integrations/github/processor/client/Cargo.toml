[package]
name = "github_processor_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "github_processor_client"
path = "client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
feature-flags = { path = "../../../../../base/feature_flags" }
grpc_client = { path = "../../../../lib/grpc/client" }
prost = { workspace = true }
prost-wkt-types = { workspace = true }
request_context = { path = "../../../../lib/request_context" }
serde = { workspace = true }
serde_json = { workspace = true }
serde_urlencoded = { workspace = true }
tonic = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../../../base/rust/tracing-tonic" }

[build-dependencies]
prost-build = { workspace = true }
prost-wkt-build = { workspace = true }
tonic-build = { workspace = true }

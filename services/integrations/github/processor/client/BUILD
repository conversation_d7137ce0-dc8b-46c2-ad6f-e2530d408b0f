load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/integrations/github/processor:processor_py_proto",
        "//services/lib/request_context:request_context_py",
    ],
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/integrations/github/processor/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/integrations/github/processor:processor_go_proto",
        "//services/lib/request_context:request_context_go",
        "@com_github_stretchr_testify//mock",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//metadata",
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["client.rs"],
    aliases = aliases(),
    crate_name = "github_processor_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/feature_flags:feature_flags_rs",
        "//base/rust/tracing-tonic",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/integrations/github/processor:processor_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:protoc",
        "@protobuf//:struct_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

rust_test(
    name = "github_processor_client_test",
    crate = ":client_rs",
    proc_macro_deps = [
        "@crates//:async-trait",
    ],
    deps = [
        "@crates//:async-lock",
        "@crates//:prost",
        "@crates//:prost-wkt-types",
        "@crates//:tonic",
        "@crates//:tracing",
    ],
)

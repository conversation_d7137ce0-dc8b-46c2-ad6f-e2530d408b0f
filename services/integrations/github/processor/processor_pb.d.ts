// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/github/processor/processor.proto (package github_processor, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Struct } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Status } from "../../../../google/rpc/status_pb.js";

/**
 * @generated from enum github_processor.HTTPMethod
 */
export declare enum HTTPMethod {
  /**
   * @generated from enum value: GET = 0;
   */
  GET = 0,

  /**
   * @generated from enum value: POST = 1;
   */
  POST = 1,

  /**
   * @generated from enum value: PATCH = 2;
   */
  PATCH = 2,

  /**
   * @generated from enum value: PUT = 3;
   */
  PUT = 3,
}

/**
 * @generated from message github_processor.HydrateGithubSettingsRequest
 */
export declare class HydrateGithubSettingsRequest extends Message<HydrateGithubSettingsRequest> {
  /**
   * @generated from field: int64 installation_id = 1;
   */
  installationId: bigint;

  /**
   * @generated from field: string code = 2;
   */
  code: string;

  constructor(data?: PartialMessage<HydrateGithubSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.HydrateGithubSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGithubSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGithubSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGithubSettingsRequest;

  static equals(a: HydrateGithubSettingsRequest | PlainMessage<HydrateGithubSettingsRequest> | undefined, b: HydrateGithubSettingsRequest | PlainMessage<HydrateGithubSettingsRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.HydrateGithubSettingsResponse
 */
export declare class HydrateGithubSettingsResponse extends Message<HydrateGithubSettingsResponse> {
  constructor(data?: PartialMessage<HydrateGithubSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.HydrateGithubSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGithubSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGithubSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGithubSettingsResponse;

  static equals(a: HydrateGithubSettingsResponse | PlainMessage<HydrateGithubSettingsResponse> | undefined, b: HydrateGithubSettingsResponse | PlainMessage<HydrateGithubSettingsResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.HydrateGithubUserSettingsRequest
 */
export declare class HydrateGithubUserSettingsRequest extends Message<HydrateGithubUserSettingsRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  constructor(data?: PartialMessage<HydrateGithubUserSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.HydrateGithubUserSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGithubUserSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGithubUserSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGithubUserSettingsRequest;

  static equals(a: HydrateGithubUserSettingsRequest | PlainMessage<HydrateGithubUserSettingsRequest> | undefined, b: HydrateGithubUserSettingsRequest | PlainMessage<HydrateGithubUserSettingsRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.HydrateGithubUserSettingsResponse
 */
export declare class HydrateGithubUserSettingsResponse extends Message<HydrateGithubUserSettingsResponse> {
  constructor(data?: PartialMessage<HydrateGithubUserSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.HydrateGithubUserSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGithubUserSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGithubUserSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGithubUserSettingsResponse;

  static equals(a: HydrateGithubUserSettingsResponse | PlainMessage<HydrateGithubUserSettingsResponse> | undefined, b: HydrateGithubUserSettingsResponse | PlainMessage<HydrateGithubUserSettingsResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.GithubApiRequest
 */
export declare class GithubApiRequest extends Message<GithubApiRequest> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  /**
   * @generated from field: github_processor.HTTPMethod method = 2;
   */
  method: HTTPMethod;

  /**
   * @generated from field: optional string token = 3;
   */
  token?: string;

  /**
   * @generated from field: google.protobuf.Struct data = 4;
   */
  data?: Struct;

  constructor(data?: PartialMessage<GithubApiRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GithubApiRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubApiRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubApiRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubApiRequest;

  static equals(a: GithubApiRequest | PlainMessage<GithubApiRequest> | undefined, b: GithubApiRequest | PlainMessage<GithubApiRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.GithubApiResponse
 */
export declare class GithubApiResponse extends Message<GithubApiResponse> {
  /**
   * @generated from field: string response = 1;
   */
  response: string;

  /**
   * @generated from field: int32 status_code = 2;
   */
  statusCode: number;

  constructor(data?: PartialMessage<GithubApiResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GithubApiResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubApiResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubApiResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubApiResponse;

  static equals(a: GithubApiResponse | PlainMessage<GithubApiResponse> | undefined, b: GithubApiResponse | PlainMessage<GithubApiResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.IsUserOAuthConfiguredRequest
 */
export declare class IsUserOAuthConfiguredRequest extends Message<IsUserOAuthConfiguredRequest> {
  constructor(data?: PartialMessage<IsUserOAuthConfiguredRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.IsUserOAuthConfiguredRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IsUserOAuthConfiguredRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IsUserOAuthConfiguredRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IsUserOAuthConfiguredRequest;

  static equals(a: IsUserOAuthConfiguredRequest | PlainMessage<IsUserOAuthConfiguredRequest> | undefined, b: IsUserOAuthConfiguredRequest | PlainMessage<IsUserOAuthConfiguredRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.IsUserOAuthConfiguredResponse
 */
export declare class IsUserOAuthConfiguredResponse extends Message<IsUserOAuthConfiguredResponse> {
  /**
   * @generated from field: bool is_configured = 1;
   */
  isConfigured: boolean;

  /**
   * @generated from field: string oauth_url = 2;
   */
  oauthUrl: string;

  /**
   * @generated from field: bool configured_but_needs_update = 3;
   */
  configuredButNeedsUpdate: boolean;

  /**
   * @generated from field: string updated_scopes = 4;
   */
  updatedScopes: string;

  constructor(data?: PartialMessage<IsUserOAuthConfiguredResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.IsUserOAuthConfiguredResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IsUserOAuthConfiguredResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IsUserOAuthConfiguredResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IsUserOAuthConfiguredResponse;

  static equals(a: IsUserOAuthConfiguredResponse | PlainMessage<IsUserOAuthConfiguredResponse> | undefined, b: IsUserOAuthConfiguredResponse | PlainMessage<IsUserOAuthConfiguredResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetGithubUserOAuthUrlRequest
 */
export declare class GetGithubUserOAuthUrlRequest extends Message<GetGithubUserOAuthUrlRequest> {
  constructor(data?: PartialMessage<GetGithubUserOAuthUrlRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetGithubUserOAuthUrlRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGithubUserOAuthUrlRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGithubUserOAuthUrlRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGithubUserOAuthUrlRequest;

  static equals(a: GetGithubUserOAuthUrlRequest | PlainMessage<GetGithubUserOAuthUrlRequest> | undefined, b: GetGithubUserOAuthUrlRequest | PlainMessage<GetGithubUserOAuthUrlRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetGithubUserOAuthUrlResponse
 */
export declare class GetGithubUserOAuthUrlResponse extends Message<GetGithubUserOAuthUrlResponse> {
  /**
   * @generated from field: string oauth_url = 1;
   */
  oauthUrl: string;

  constructor(data?: PartialMessage<GetGithubUserOAuthUrlResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetGithubUserOAuthUrlResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGithubUserOAuthUrlResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGithubUserOAuthUrlResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGithubUserOAuthUrlResponse;

  static equals(a: GetGithubUserOAuthUrlResponse | PlainMessage<GetGithubUserOAuthUrlResponse> | undefined, b: GetGithubUserOAuthUrlResponse | PlainMessage<GetGithubUserOAuthUrlResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.RevokeOAuthGrantRequest
 */
export declare class RevokeOAuthGrantRequest extends Message<RevokeOAuthGrantRequest> {
  constructor(data?: PartialMessage<RevokeOAuthGrantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.RevokeOAuthGrantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RevokeOAuthGrantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RevokeOAuthGrantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RevokeOAuthGrantRequest;

  static equals(a: RevokeOAuthGrantRequest | PlainMessage<RevokeOAuthGrantRequest> | undefined, b: RevokeOAuthGrantRequest | PlainMessage<RevokeOAuthGrantRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.RevokeOAuthGrantResponse
 */
export declare class RevokeOAuthGrantResponse extends Message<RevokeOAuthGrantResponse> {
  /**
   * @generated from field: google.rpc.Status status = 1;
   */
  status?: Status;

  constructor(data?: PartialMessage<RevokeOAuthGrantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.RevokeOAuthGrantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RevokeOAuthGrantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RevokeOAuthGrantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RevokeOAuthGrantResponse;

  static equals(a: RevokeOAuthGrantResponse | PlainMessage<RevokeOAuthGrantResponse> | undefined, b: RevokeOAuthGrantResponse | PlainMessage<RevokeOAuthGrantResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetGithubUserInfoRequest
 */
export declare class GetGithubUserInfoRequest extends Message<GetGithubUserInfoRequest> {
  constructor(data?: PartialMessage<GetGithubUserInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetGithubUserInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGithubUserInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGithubUserInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGithubUserInfoRequest;

  static equals(a: GetGithubUserInfoRequest | PlainMessage<GetGithubUserInfoRequest> | undefined, b: GetGithubUserInfoRequest | PlainMessage<GetGithubUserInfoRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetGithubUserInfoResponse
 */
export declare class GetGithubUserInfoResponse extends Message<GetGithubUserInfoResponse> {
  /**
   * @generated from field: string login = 1;
   */
  login: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  constructor(data?: PartialMessage<GetGithubUserInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetGithubUserInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGithubUserInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGithubUserInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGithubUserInfoResponse;

  static equals(a: GetGithubUserInfoResponse | PlainMessage<GetGithubUserInfoResponse> | undefined, b: GetGithubUserInfoResponse | PlainMessage<GetGithubUserInfoResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.GithubRepo
 */
export declare class GithubRepo extends Message<GithubRepo> {
  /**
   * @generated from field: string owner = 1;
   */
  owner: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string html_url = 3;
   */
  htmlUrl: string;

  /**
   * @generated from field: string created_at = 4;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 5;
   */
  updatedAt: string;

  /**
   * @generated from field: string default_branch = 6;
   */
  defaultBranch: string;

  constructor(data?: PartialMessage<GithubRepo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GithubRepo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubRepo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubRepo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubRepo;

  static equals(a: GithubRepo | PlainMessage<GithubRepo> | undefined, b: GithubRepo | PlainMessage<GithubRepo> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetGithubUserTokenRequest
 */
export declare class GetGithubUserTokenRequest extends Message<GetGithubUserTokenRequest> {
  /**
   * @generated from field: github_processor.GithubRepo repo = 1;
   */
  repo?: GithubRepo;

  constructor(data?: PartialMessage<GetGithubUserTokenRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetGithubUserTokenRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGithubUserTokenRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGithubUserTokenRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGithubUserTokenRequest;

  static equals(a: GetGithubUserTokenRequest | PlainMessage<GetGithubUserTokenRequest> | undefined, b: GetGithubUserTokenRequest | PlainMessage<GetGithubUserTokenRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetGithubUserTokenResponse
 */
export declare class GetGithubUserTokenResponse extends Message<GetGithubUserTokenResponse> {
  /**
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * @generated from field: string login = 2;
   */
  login: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: string name = 4;
   */
  name: string;

  constructor(data?: PartialMessage<GetGithubUserTokenResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetGithubUserTokenResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGithubUserTokenResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGithubUserTokenResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGithubUserTokenResponse;

  static equals(a: GetGithubUserTokenResponse | PlainMessage<GetGithubUserTokenResponse> | undefined, b: GetGithubUserTokenResponse | PlainMessage<GetGithubUserTokenResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.ListGithubReposForAuthenticatedUserRequest
 */
export declare class ListGithubReposForAuthenticatedUserRequest extends Message<ListGithubReposForAuthenticatedUserRequest> {
  constructor(data?: PartialMessage<ListGithubReposForAuthenticatedUserRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.ListGithubReposForAuthenticatedUserRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListGithubReposForAuthenticatedUserRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListGithubReposForAuthenticatedUserRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListGithubReposForAuthenticatedUserRequest;

  static equals(a: ListGithubReposForAuthenticatedUserRequest | PlainMessage<ListGithubReposForAuthenticatedUserRequest> | undefined, b: ListGithubReposForAuthenticatedUserRequest | PlainMessage<ListGithubReposForAuthenticatedUserRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.ListGithubReposForAuthenticatedUserResponse
 */
export declare class ListGithubReposForAuthenticatedUserResponse extends Message<ListGithubReposForAuthenticatedUserResponse> {
  /**
   * @generated from field: repeated github_processor.GithubRepo repos = 1;
   */
  repos: GithubRepo[];

  constructor(data?: PartialMessage<ListGithubReposForAuthenticatedUserResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.ListGithubReposForAuthenticatedUserResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListGithubReposForAuthenticatedUserResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListGithubReposForAuthenticatedUserResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListGithubReposForAuthenticatedUserResponse;

  static equals(a: ListGithubReposForAuthenticatedUserResponse | PlainMessage<ListGithubReposForAuthenticatedUserResponse> | undefined, b: ListGithubReposForAuthenticatedUserResponse | PlainMessage<ListGithubReposForAuthenticatedUserResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.ListGithubRepoBranchesRequest
 */
export declare class ListGithubRepoBranchesRequest extends Message<ListGithubRepoBranchesRequest> {
  /**
   * @generated from field: github_processor.GithubRepo repo = 1;
   */
  repo?: GithubRepo;

  /**
   * @generated from field: optional int32 page = 2;
   */
  page?: number;

  constructor(data?: PartialMessage<ListGithubRepoBranchesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.ListGithubRepoBranchesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListGithubRepoBranchesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListGithubRepoBranchesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListGithubRepoBranchesRequest;

  static equals(a: ListGithubRepoBranchesRequest | PlainMessage<ListGithubRepoBranchesRequest> | undefined, b: ListGithubRepoBranchesRequest | PlainMessage<ListGithubRepoBranchesRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.GithubBranchCommit
 */
export declare class GithubBranchCommit extends Message<GithubBranchCommit> {
  /**
   * @generated from field: string sha = 1;
   */
  sha: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;

  constructor(data?: PartialMessage<GithubBranchCommit>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GithubBranchCommit";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubBranchCommit;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubBranchCommit;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubBranchCommit;

  static equals(a: GithubBranchCommit | PlainMessage<GithubBranchCommit> | undefined, b: GithubBranchCommit | PlainMessage<GithubBranchCommit> | undefined): boolean;
}

/**
 * @generated from message github_processor.GithubBranch
 */
export declare class GithubBranch extends Message<GithubBranch> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: github_processor.GithubBranchCommit commit = 2;
   */
  commit?: GithubBranchCommit;

  /**
   * @generated from field: bool protected = 3;
   */
  protected: boolean;

  constructor(data?: PartialMessage<GithubBranch>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GithubBranch";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubBranch;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubBranch;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubBranch;

  static equals(a: GithubBranch | PlainMessage<GithubBranch> | undefined, b: GithubBranch | PlainMessage<GithubBranch> | undefined): boolean;
}

/**
 * @generated from message github_processor.ListGithubRepoBranchesResponse
 */
export declare class ListGithubRepoBranchesResponse extends Message<ListGithubRepoBranchesResponse> {
  /**
   * @generated from field: repeated github_processor.GithubBranch branches = 1;
   */
  branches: GithubBranch[];

  /**
   * @generated from field: bool has_next_page = 2;
   */
  hasNextPage: boolean;

  /**
   * @generated from field: int32 next_page = 3;
   */
  nextPage: number;

  constructor(data?: PartialMessage<ListGithubRepoBranchesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.ListGithubRepoBranchesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListGithubRepoBranchesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListGithubRepoBranchesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListGithubRepoBranchesResponse;

  static equals(a: ListGithubRepoBranchesResponse | PlainMessage<ListGithubRepoBranchesResponse> | undefined, b: ListGithubRepoBranchesResponse | PlainMessage<ListGithubRepoBranchesResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetRepoRequest
 */
export declare class GetRepoRequest extends Message<GetRepoRequest> {
  /**
   * @generated from field: github_processor.GithubRepo repo = 1;
   */
  repo?: GithubRepo;

  constructor(data?: PartialMessage<GetRepoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetRepoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRepoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRepoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRepoRequest;

  static equals(a: GetRepoRequest | PlainMessage<GetRepoRequest> | undefined, b: GetRepoRequest | PlainMessage<GetRepoRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.GetRepoResponse
 */
export declare class GetRepoResponse extends Message<GetRepoResponse> {
  /**
   * @generated from field: github_processor.GithubRepo repo = 1;
   */
  repo?: GithubRepo;

  constructor(data?: PartialMessage<GetRepoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GetRepoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRepoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRepoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRepoResponse;

  static equals(a: GetRepoResponse | PlainMessage<GetRepoResponse> | undefined, b: GetRepoResponse | PlainMessage<GetRepoResponse> | undefined): boolean;
}

/**
 * @generated from message github_processor.GithubUser
 */
export declare class GithubUser extends Message<GithubUser> {
  /**
   * @generated from field: string login = 1;
   */
  login: string;

  /**
   * @generated from field: string avatar_url = 2;
   */
  avatarUrl: string;

  /**
   * @generated from field: string html_url = 3;
   */
  htmlUrl: string;

  constructor(data?: PartialMessage<GithubUser>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GithubUser";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubUser;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubUser;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubUser;

  static equals(a: GithubUser | PlainMessage<GithubUser> | undefined, b: GithubUser | PlainMessage<GithubUser> | undefined): boolean;
}

/**
 * @generated from message github_processor.GithubPullRequest
 */
export declare class GithubPullRequest extends Message<GithubPullRequest> {
  /**
   * @generated from field: int32 number = 1;
   */
  number: number;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string body = 3;
   */
  body: string;

  /**
   * @generated from field: string state = 4;
   */
  state: string;

  /**
   * @generated from field: string html_url = 5;
   */
  htmlUrl: string;

  /**
   * @generated from field: string created_at = 6;
   */
  createdAt: string;

  /**
   * @generated from field: string updated_at = 7;
   */
  updatedAt: string;

  /**
   * @generated from field: string merged_at = 8;
   */
  mergedAt: string;

  /**
   * @generated from field: string closed_at = 9;
   */
  closedAt: string;

  /**
   * @generated from field: github_processor.GithubUser user = 10;
   */
  user?: GithubUser;

  /**
   * @generated from field: string head_ref = 11;
   */
  headRef: string;

  /**
   * @generated from field: string base_ref = 12;
   */
  baseRef: string;

  /**
   * @generated from field: string head_sha = 13;
   */
  headSha: string;

  /**
   * @generated from field: string base_sha = 14;
   */
  baseSha: string;

  /**
   * @generated from field: bool merged = 15;
   */
  merged: boolean;

  /**
   * @generated from field: bool draft = 16;
   */
  draft: boolean;

  /**
   * @generated from field: int32 comments = 17;
   */
  comments: number;

  /**
   * @generated from field: int32 commits = 18;
   */
  commits: number;

  /**
   * @generated from field: int32 additions = 19;
   */
  additions: number;

  /**
   * @generated from field: int32 deletions = 20;
   */
  deletions: number;

  /**
   * @generated from field: int32 changed_files = 21;
   */
  changedFiles: number;

  constructor(data?: PartialMessage<GithubPullRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.GithubPullRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubPullRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubPullRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubPullRequest;

  static equals(a: GithubPullRequest | PlainMessage<GithubPullRequest> | undefined, b: GithubPullRequest | PlainMessage<GithubPullRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.CreatePullRequestRequest
 */
export declare class CreatePullRequestRequest extends Message<CreatePullRequestRequest> {
  /**
   * @generated from field: github_processor.GithubRepo repo = 1;
   */
  repo?: GithubRepo;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string body = 3;
   */
  body: string;

  /**
   * @generated from field: string head = 4;
   */
  head: string;

  /**
   * @generated from field: string base = 5;
   */
  base: string;

  /**
   * @generated from field: bool draft = 6;
   */
  draft: boolean;

  constructor(data?: PartialMessage<CreatePullRequestRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.CreatePullRequestRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreatePullRequestRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreatePullRequestRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreatePullRequestRequest;

  static equals(a: CreatePullRequestRequest | PlainMessage<CreatePullRequestRequest> | undefined, b: CreatePullRequestRequest | PlainMessage<CreatePullRequestRequest> | undefined): boolean;
}

/**
 * @generated from message github_processor.CreatePullRequestResponse
 */
export declare class CreatePullRequestResponse extends Message<CreatePullRequestResponse> {
  /**
   * @generated from field: github_processor.GithubPullRequest pull_request = 1;
   */
  pullRequest?: GithubPullRequest;

  constructor(data?: PartialMessage<CreatePullRequestResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "github_processor.CreatePullRequestResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreatePullRequestResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreatePullRequestResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreatePullRequestResponse;

  static equals(a: CreatePullRequestResponse | PlainMessage<CreatePullRequestResponse> | undefined, b: CreatePullRequestResponse | PlainMessage<CreatePullRequestResponse> | undefined): boolean;
}


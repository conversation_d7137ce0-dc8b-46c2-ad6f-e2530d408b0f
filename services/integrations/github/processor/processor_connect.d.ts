// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/github/processor/processor.proto (package github_processor, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CreatePullRequestRequest, CreatePullRequestResponse, GetGithubUserInfoRequest, GetGithubUserInfoResponse, GetGithubUserOAuthUrlRequest, GetGithubUserOAuthUrlResponse, GetGithubUserTokenRequest, GetGithubUserTokenResponse, GetRepoRequest, GetRepoResponse, GithubApiRequest, GithubApiResponse, HydrateGithubSettingsRequest, HydrateGithubSettingsResponse, HydrateGithubUserSettingsRequest, HydrateGithubUserSettingsResponse, IsUserOAuthConfiguredRequest, IsUserOAuthConfiguredResponse, ListGithubRepoBranchesRequest, ListGithubRepoBranchesResponse, ListGithubReposForAuthenticatedUserRequest, ListGithubReposForAuthenticatedUserResponse, RevokeOAuthGrantRequest, RevokeOAuthGrantResponse } from "./processor_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service github_processor.GithubProcessor
 */
export declare const GithubProcessor: {
  readonly typeName: "github_processor.GithubProcessor",
  readonly methods: {
    /**
     * @generated from rpc github_processor.GithubProcessor.HydrateGithubSettings
     */
    readonly hydrateGithubSettings: {
      readonly name: "HydrateGithubSettings",
      readonly I: typeof HydrateGithubSettingsRequest,
      readonly O: typeof HydrateGithubSettingsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.HydrateGithubUserSettings
     */
    readonly hydrateGithubUserSettings: {
      readonly name: "HydrateGithubUserSettings",
      readonly I: typeof HydrateGithubUserSettingsRequest,
      readonly O: typeof HydrateGithubUserSettingsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.CallGithubApi
     */
    readonly callGithubApi: {
      readonly name: "CallGithubApi",
      readonly I: typeof GithubApiRequest,
      readonly O: typeof GithubApiResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.IsUserOAuthConfigured
     */
    readonly isUserOAuthConfigured: {
      readonly name: "IsUserOAuthConfigured",
      readonly I: typeof IsUserOAuthConfiguredRequest,
      readonly O: typeof IsUserOAuthConfiguredResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.GetGithubUserOAuthUrl
     */
    readonly getGithubUserOAuthUrl: {
      readonly name: "GetGithubUserOAuthUrl",
      readonly I: typeof GetGithubUserOAuthUrlRequest,
      readonly O: typeof GetGithubUserOAuthUrlResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.RevokeOAuthGrant
     */
    readonly revokeOAuthGrant: {
      readonly name: "RevokeOAuthGrant",
      readonly I: typeof RevokeOAuthGrantRequest,
      readonly O: typeof RevokeOAuthGrantResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.GetGithubUserInfo
     */
    readonly getGithubUserInfo: {
      readonly name: "GetGithubUserInfo",
      readonly I: typeof GetGithubUserInfoRequest,
      readonly O: typeof GetGithubUserInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.GetGithubUserToken
     */
    readonly getGithubUserToken: {
      readonly name: "GetGithubUserToken",
      readonly I: typeof GetGithubUserTokenRequest,
      readonly O: typeof GetGithubUserTokenResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.ListGithubReposForAuthenticatedUser
     */
    readonly listGithubReposForAuthenticatedUser: {
      readonly name: "ListGithubReposForAuthenticatedUser",
      readonly I: typeof ListGithubReposForAuthenticatedUserRequest,
      readonly O: typeof ListGithubReposForAuthenticatedUserResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.ListBranchesForRepo
     */
    readonly listBranchesForRepo: {
      readonly name: "ListBranchesForRepo",
      readonly I: typeof ListGithubRepoBranchesRequest,
      readonly O: typeof ListGithubRepoBranchesResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.GetRepo
     */
    readonly getRepo: {
      readonly name: "GetRepo",
      readonly I: typeof GetRepoRequest,
      readonly O: typeof GetRepoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc github_processor.GithubProcessor.CreatePullRequest
     */
    readonly createPullRequest: {
      readonly name: "CreatePullRequest",
      readonly I: typeof CreatePullRequestRequest,
      readonly O: typeof CreatePullRequestResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};


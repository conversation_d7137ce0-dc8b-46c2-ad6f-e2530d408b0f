"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import json
import itertools
import random
import struct
import time
from pathlib import Path
from typing import Generator, Optional, Callable

import grpc
import pytest
import requests
import services.content_manager.content_manager_pb2_grpc as content_manager_pb2_grpc

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
from base.python.grpc.health_check import HealthChecker
import services.content_manager.client.content_manager_client
from services.integrations.github.state import github_state_pb2_grpc
from services.integrations.github.state.client.client import (
    setup_stub as setup_state_stub,
)
from services.lib.request_context.request_context import (
    RequestContext,
    create_request_session_id,
)
from services.tenant_watcher.client.client import TenantsClient
from services.test.fake_feature_flags.client import FakeFeatureFlagsClient
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)
from services.token_exchange import token_exchange_pb2

NEXT_FORWARDED_PORT = 50052


def get_next_forwarded_port() -> int:
    """Return a fresh local port."""
    global NEXT_FORWARDED_PORT  # pylint: disable=global-statement
    port = NEXT_FORWARDED_PORT
    NEXT_FORWARDED_PORT += 1
    return port


def generate_random_bytes(n: int) -> bytes:
    """Generate a random bytes array of a given size."""
    return (
        b"".join(
            map(
                struct.Struct("!Q").pack,
                map(random.getrandbits, itertools.repeat(64, (n + 7) // 8)),
            )
        )
    )[:n]


@pytest.fixture()
def content_factory() -> Callable[[int], bytes]:
    """Return a function that generates random bytes."""

    def gen(n):
        return generate_random_bytes(n)

    return gen


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--skip-deployment-teardown",
        action="store_true",
        help="skip deploy and delete of the models",
        default=not k8s_test_helper.is_running_in_test_infra(),
    )
    if not k8s_test_helper.is_running_in_test_infra():
        parser.addoption(
            "--teardown-deployment",
            action="store_true",
            help="tear down the complete deployment after the run",
            default=False,
        )
    parser.addoption(
        "--skip-deployment-check",
        action="store_true",
        help="skip checking if the deployments are settled",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


@pytest.fixture(scope="session")
def application_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys the application as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    skip_deploy_check = request.config.getoption("--skip-deployment-check")
    skip_deploy_check_teardown = request.config.getoption(
        "--skip-deployment-teardown"
    ) and not request.config.getoption("--teardown-deployment")
    cloud = request.config.getoption("--cloud")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        skip_deploy_check=skip_deploy_check,
        skip_deploy_check_teardown=skip_deploy_check_teardown,
        cloud=cloud,
        kubecfg_binaries=[
            Path("services/integrations/github/test/test_kubecfg.sh"),
        ],
    ) as deploy_info:
        yield deploy_info


def _test_response(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


def _test_response_hook(url: str) -> bool:
    try:
        # Use a longer timeout and print more detailed information
        print(f"Testing webhook health at {url}/health")
        response = requests.get(
            f"{url}/health",
            json={},
            verify=False,
            timeout=90,  # Increase timeout for slow environments
        )
        if response.status_code == 200:
            print(
                f"Webhook health check succeeded with status code {response.status_code}"
            )
            return True
        else:
            print(
                f"Webhook health check failed with status code {response.status_code}"
            )
            return False
    except requests.exceptions.ConnectionError as e:
        print(f"Connection error during webhook health check: {e}", flush=True)
        return False
    except requests.exceptions.Timeout as e:
        print(f"Timeout during webhook health check: {e}", flush=True)
        return False
    except requests.exceptions.RequestException as ex:
        print(f"Request exception during webhook health check: {ex}", flush=True)
        return False


@pytest.fixture(scope="session")
def token_exchange_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TokenExchangeClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "token-exchange-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            "deployment/token-exchange-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield GrpcTokenExchangeClient(
                    url,
                    credentials=credentials,
                    options=options,
                    namespace=application_deploy.namespace,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcTokenExchangeClient(
            url,
            credentials=credentials,
            options=options,
            namespace=application_deploy.namespace,
        )


@pytest.fixture(scope="session")
def request_session_id() -> Generator[str, None, None]:
    """Return a request context."""
    yield create_request_session_id()


@pytest.fixture()
def request_context(
    request_session_id: str, token_exchange_client: TokenExchangeClient, tenant_id: str
) -> Generator[RequestContext, None, None]:
    """Return a request context."""

    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.CONTENT_RW, token_exchange_pb2.CONTENT_R],
    )
    yield RequestContext.create_for_session(request_session_id, auth_token=token)


@pytest.fixture(scope="session")
def tenant_watcher_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TenantsClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "tenant-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            "deployment/tenant-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield TenantsClient(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield TenantsClient(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def tenant_id(
    tenant_watcher_client: TenantsClient,
) -> str:
    tenants = tenant_watcher_client.get_tenants()
    assert tenants
    tid = tenants[0].id
    assert tid, "Failed to find tenant ID for tenant 'augment'"
    return tid


@pytest.fixture(scope="session")
def github_state_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[github_state_pb2_grpc.GithubStateStub, None, None]:
    """Return an GRPC stub to access the github state.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "github-state-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            "deployment/github-state", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield setup_state_stub(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield setup_state_stub(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def github_webhook_listener_url(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[str, None, None]:
    """Setup the webhook listener and return its webhook URL."""
    for attempt in range(30):
        try:
            with application_deploy.kubectl.port_forward(
                "deployment/github-webhook", 5000, get_next_forwarded_port()
            ) as port:
                url = f"https://localhost:{port}"

                # More robust health checking with multiple attempts
                health_check_success = False
                for health_attempt in range(5):
                    if _test_response_hook(url):
                        health_check_success = True
                        break
                    print(
                        f"Health check attempt {health_attempt+1}/5 failed, retrying..."
                    )
                    time.sleep(5)

                if not health_check_success:
                    print(
                        "Health check failed after 5 attempts, trying new port forward"
                    )
                    time.sleep(10)
                    continue

                print(f"Successfully established webhook listener connection on {url}")
                yield url
                break
        except Exception as e:
            print(f"Error setting up port forward on attempt {attempt+1}/30: {e}")
            time.sleep(10)
    else:
        raise RuntimeError("Failed to setup webhook listener after 30 attempts")


@pytest.fixture()
def repo_name() -> str:
    """Return the repo name to use for testing."""
    random_bytes = generate_random_bytes(4)
    return f"test-repo-{random_bytes.hex()}"


@pytest.fixture(scope="session")
def content_manager_stub(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[content_manager_pb2_grpc.ContentManagerStub, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "content-manager-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            "deployment/content-manager", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url,
                credentials=credentials,
                options=options,
                service_name="",
            ):
                time.sleep(10)
                continue
            else:
                yield services.content_manager.client.content_manager_client.setup_stub(
                    url, credentials=credentials, options=options
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield services.content_manager.client.content_manager_client.setup_stub(
            url, credentials=credentials, options=options
        )


@pytest.fixture(scope="session")
def content_manager_client(
    content_manager_stub,
) -> Generator[
    services.content_manager.client.content_manager_client.ContentManagerClient,
    None,
    None,
]:
    """Return a ContentManagerClient to access the content manager."""
    yield services.content_manager.client.content_manager_client.ContentManagerClient(
        content_manager_stub
    )


@pytest.fixture(scope="function")
def webhook_tenant_mapping_installation_id(
    application_deploy: k8s_test_helper.DeployInfo, request: pytest.FixtureRequest
) -> Generator[tuple[str, int], None, None]:
    """Create a webhook tenant mapping and return the tenant id and installation id."""
    installation_id = getattr(
        request, "param", 123456
    )  # Default to 123456 if not specified

    # First, try to delete any existing WebhookTenantMappings
    for obj in application_deploy.kubectl.list(
        "webhooktenantmapping", application_deploy.namespace
    ):
        if (
            obj["spec"]["webhook_type"] == "github"
            and obj["spec"]["webhook_value"]
            == f'{{"installation_id":{installation_id}}}'
        ):
            print("Deleting existing WebhookTenantMapping", obj["metadata"]["name"])
            application_deploy.kubectl.delete(obj)

    with k8s_test_helper.new_tenant(application_deploy, "webhook-tenant") as tenant_id:
        tenant_mapping_data = {
            "apiVersion": "eng.augmentcode.com/v1",
            "kind": "WebhookTenantMapping",
            "metadata": {
                "name": f"github-{tenant_id}",
                "namespace": application_deploy.namespace,
            },
            "spec": {
                "webhook_type": "github",
                "webhook_value": f'{{"installation_id":{installation_id}}}',
                "tenant_id": tenant_id,
            },
        }

        try:
            application_deploy.kubectl.apply(tenant_mapping_data)

            # Wait for the webhook mapping to be processed and the pod to be ready
            # This helps prevent the port forwarding from breaking due to pod restarts
            print("Waiting for webhook tenant mapping to be processed...")
            time.sleep(5)  # Give the system time to process the mapping and stabilize

            yield tenant_id, installation_id
        finally:
            application_deploy.kubectl.delete(tenant_mapping_data)


@pytest.fixture(scope="session")
def feature_flags_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[FakeFeatureFlagsClient, None, None]:
    """Return a client for the fake feature flags service."""
    with application_deploy.kubectl.port_forward(
        "deployment/fake-feature-flags", 8080, get_next_forwarded_port()
    ) as port:
        url = f"http://localhost:{port}"
        yield FakeFeatureFlagsClient(
            url,
        )


@pytest.fixture(autouse=True)
def feature_flag_clear(feature_flags_client: FakeFeatureFlagsClient):
    """Clear all feature flags before each test."""
    feature_flags_client.clear()
    feature_flags_client.update("max_upload_size_bytes", json.dumps(128 * 1024), wait=1)

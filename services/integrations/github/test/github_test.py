"""E2E tests for the github state service."""

import base64
import time
from datetime import datetime, timezone
from typing import Callable
from collections import namedtuple


from kubernetes import client, config
import requests
import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
import services.content_manager.client.content_manager_client
from base.blob_names import blob_names_pb2
from base.blob_names.python.blob_names import encode_blob_name
from services.integrations.github.state import github_state_pb2, github_state_pb2_grpc
from services.lib.request_context.request_context import RequestContext
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import TokenExchangeClient


def fake_github_push_event(
    branch, repo_owner, repo_name, before, after, installation_id: int
) -> tuple[dict, dict]:
    headers = {
        "Content-Type": "application/json",
        "X-GitHub-Event": "push",
    }
    data = {
        "ref": f"refs/heads/{branch}",
        "before": before,
        "after": after,
        "repository": {
            "id": 123456,
            "name": repo_name,
            "full_name": f"{repo_owner}/{repo_name}",
            "owner": {
                "name": repo_owner,
                "email": f"{repo_owner}@example.com",
                "login": repo_owner,
            },
        },
        "pusher": {"name": "test-user", "email": "<EMAIL>"},
        "commits": [
            {
                "id": after,
                "message": "Test commit",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "url": f"https://github.com/{repo_owner}/{repo_name}/commit/{after}",
                "author": {"name": "Test Author", "email": "<EMAIL>"},
            }
        ],
        "head_commit": {
            "id": after,
            "message": "Test commit",
            "url": f"https://github.com/{repo_owner}/{repo_name}/commit/{after}",
            "author": {"name": "Test Author", "email": "<EMAIL>"},
            "timestamp": datetime.now(timezone.utc).isoformat(),
        },
        "installation": {"id": installation_id},
    }
    return headers, data


PushEventTestCase = namedtuple(
    "PushEventTestCase", ["before_sha", "after_sha", "checkpoint_id", "description"]
)


def run_test_case(
    test_case: PushEventTestCase,
    github_webhook_listener_url: str,
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    webhook_tenant_mapping_installation_id: tuple[str, int],
    repo_owner: str,
    repo_name: str,
    branch: str,
):
    print(f"Running test: {test_case.description}")
    tenant_id, installation_id = webhook_tenant_mapping_installation_id

    headers, data = fake_github_push_event(
        branch,
        repo_owner,
        repo_name,
        test_case.before_sha,
        test_case.after_sha,
        installation_id=installation_id,
    )

    resp = requests.post(
        f"{github_webhook_listener_url}/fake_postreceive",
        headers=headers,
        json=data,
        verify=False,
    )

    assert resp.status_code == 200, "Failed to post to webhook"

    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.CONTENT_RW],
    )
    request_context = RequestContext.create_for_session(
        request_session_id, auth_token=token
    )

    # Wait for the GitHub processor to handle the event
    # Check if the GitHub state has been updated
    max_retries = 30
    retry_interval = 10
    expected_ref = github_state_pb2.GithubRef(
        ref=branch,
        repo=github_state_pb2.GithubRepo(repo_owner=repo_owner, repo_name=repo_name),
    )
    # Check current ref state to find uploaded commit
    for _ in range(max_retries):
        time.sleep(retry_interval)

        current_state_response = None
        try:
            current_state_response = github_state_client.GetCurrentRefState(
                github_state_pb2.GetCurrentRefStateRequest(ref=expected_ref),
                metadata=request_context.to_metadata(),
            )
        except Exception as e:
            print(f"Error getting current ref state: {e}")
            continue
        if (
            current_state_response.last_uploaded_ref_checkpoint.commit_sha
            == test_case.after_sha
        ):
            assert (
                current_state_response.last_uploaded_ref_checkpoint.blobs.baseline_checkpoint_id
                == test_case.checkpoint_id
            )
            break
    else:
        assert False, "Timeout waiting for GitHub current ref state to update"

    # Check indexed ref state to find checkpoint
    for _ in range(max_retries):
        time.sleep(retry_interval)

        indexed_refs = None
        try:
            indexed_refs: github_state_pb2.GetIndexedRefCheckpointsResponse = (
                github_state_client.GetIndexedRefCheckpoints(
                    github_state_pb2.GetIndexedRefCheckpointsRequest(),
                    metadata=request_context.to_metadata(),
                )
            )
        except Exception as e:
            print(f"Error getting indexed ref checkpoints: {e}")
            continue

        found = False
        for ref in indexed_refs.ref_checkpoints:
            if (
                ref.ref.repo.repo_name == repo_name
                and ref.commit_sha == test_case.after_sha
            ):
                found = True
                assert ref.blobs.baseline_checkpoint_id == test_case.checkpoint_id
                break
        if found:
            break
    else:
        assert False, "Timeout waiting for GitHub indexed ref state to update"


def test_webhook_listener(
    application_deploy: k8s_test_helper.DeployInfo,
    github_webhook_listener_url: str,
    webhook_tenant_mapping_installation_id: tuple[str, int],
):
    _, installation_id = webhook_tenant_mapping_installation_id

    # A bad installation ID should fail
    bad_installation_id = installation_id + 1
    headers, data = fake_github_push_event(
        "main",
        "owner",
        "repo",
        "6113728f27ae82c7b1a177c8d03f9e96e0adf246",
        "76ae82c7b1a177c8d03f9e96e0adf2466113728f",
        bad_installation_id,
    )
    resp = requests.post(
        f"{github_webhook_listener_url}/fake_postreceive",
        headers=headers,
        json=data,
        verify=False,
    )
    assert resp.status_code == 500

    # But the real one should pass
    headers, data = fake_github_push_event(
        "main",
        "owner",
        "repo",
        "6113728f27ae82c7b1a177c8d03f9e96e0adf246",
        "76ae82c7b1a177c8d03f9e96e0adf2466113728f",
        installation_id,
    )
    resp = requests.post(
        f"{github_webhook_listener_url}/fake_postreceive",
        headers=headers,
        json=data,
        verify=False,
    )
    assert resp.status_code == 200

    # Verify that the real endpoint fails, because we don't have a valid
    # checksum from the webhook secret that the real app uses
    resp = requests.post(
        f"{github_webhook_listener_url}/postreceive",
        headers=headers,
        json=data,
        verify=False,
    )
    assert resp.status_code == 400


# using installation id for augment-test-org github app
@pytest.mark.parametrize(
    "webhook_tenant_mapping_installation_id", [55837488], indirect=True
)
def test_repository_uploader_handle_event(
    github_webhook_listener_url: str,
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    webhook_tenant_mapping_installation_id: tuple[str, int],
):
    repo_owner = "augment-test-org"
    repo_name = "test-github-app-repo"
    branch = "main"

    # Test cases defined in this repo
    # https://github.com/augment-test-org/test-github-app-repo/
    # checkpoint ids are manually created based on files in commit
    # and what should be uploaded to content manager
    test_cases = [
        PushEventTestCase(
            before_sha="",
            after_sha="cbed59431243d2175f5414c1e77ed25cce21a42b",
            checkpoint_id="3e4b5ac40b703859cd667f9f2b531fd87d07207f8e778c6bdcf60179ebd7fcf6",
            description="Initial commit with 4 total files, one too long to upload to content manager",
        ),
        PushEventTestCase(
            before_sha="cbed59431243d2175f5414c1e77ed25cce21a42b",
            after_sha="067d1f1bc8b3ef7058868eeea954a03d87a8632b",
            checkpoint_id="1dab84bda7c4b559fd14cd7f70c2b1eb4f09b5c19a3084025f3479d30767d393",
            description="Modify 2 existing files ",
        ),
        PushEventTestCase(
            before_sha="067d1f1bc8b3ef7058868eeea954a03d87a8632b",
            after_sha="a2c2535a72268f80287bb2399934e52a69e138cd",
            checkpoint_id="56cbdc818dbd358fa6e251cee802c31201c086a17511de33f1e3cbc5fab17cdb",
            description="Renaming 3 files - some with modifications as well",
        ),
        PushEventTestCase(
            before_sha="a2c2535a72268f80287bb2399934e52a69e138cd",
            after_sha="e29d62ffa0adddec75d9ba3a70c636cb6b7d4941",
            checkpoint_id="408f58092d97ac3cf6b87ccff17a181cfb4a0650652675fcf9d8593791f742f3",
            description="Deleting 2 files",
        ),
        PushEventTestCase(
            before_sha="e29d62ffa0adddec75d9ba3a70c636cb6b7d4941",
            after_sha="678faab09a0cc3d9b3fedbf846c7e05c32d1b68b",
            checkpoint_id="408f58092d97ac3cf6b87ccff17a181cfb4a0650652675fcf9d8593791f742f3",
            description="Empty commit - checkpoint id does not change",
        ),
        PushEventTestCase(
            before_sha="678faab09a0cc3d9b3fedbf846c7e05c32d1b68b",
            after_sha="e65e65a2f2b5cad89098f7b84d803c03ad5d1c57",
            checkpoint_id="8e7f45b19582d7bce4f4370eb4dfcf2e34f0c287e8998dcb6b423bf0eb9d8c6b",
            description="Another modification post empty commit",
        ),
    ]

    for test_case in test_cases:
        run_test_case(
            test_case,
            github_webhook_listener_url,
            github_state_client,
            request_session_id,
            token_exchange_client,
            webhook_tenant_mapping_installation_id,
            repo_owner,
            repo_name,
            branch,
        )


# using installation id for augment-test-org github app
@pytest.mark.parametrize(
    "webhook_tenant_mapping_installation_id", [55837488], indirect=True
)
def test_repository_uploader_handle_events_with_binary_files(
    github_webhook_listener_url: str,
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    webhook_tenant_mapping_installation_id: tuple[str, int],
):
    repo_owner = "augment-test-org"
    repo_name = "test-repo-with-binary"
    branch = "main"

    # Test cases defined in this repo
    # https://github.com/augment-test-org/test-repo-with-binary
    # checkpoint ids are manually created based on files in commit
    # and what should be uploaded to content manager
    test_cases = [
        PushEventTestCase(
            before_sha="",
            after_sha="2091d841943520c3efe4348420089c68e7f55635",
            checkpoint_id="74baeddd67e686f8e64a019d7b7725155191dcc826f949a0fffff2f6b8fcae7f",
            description="Initial commit with 4 files, 2 of which are binary files",
        ),
        PushEventTestCase(
            before_sha="2091d841943520c3efe4348420089c68e7f55635",
            after_sha="641f7ee4ec2272d98749b49f7539120f02abc06a",
            checkpoint_id="935a255ecc6941fdd709ca48f1c2ab5dbf7f84100f694756ed59d287e66b3818",
            description="Modifying 2 files - one binary and one normal plus renaming a binary file ",
        ),
        PushEventTestCase(
            before_sha="641f7ee4ec2272d98749b49f7539120f02abc06a",
            after_sha="4a5e908fc6b4daf5ed134b37d52fb7768187ed15",
            checkpoint_id="935a255ecc6941fdd709ca48f1c2ab5dbf7f84100f694756ed59d287e66b3818",
            description="Deleting a binary file",
        ),
    ]

    for test_case in test_cases:
        run_test_case(
            test_case,
            github_webhook_listener_url,
            github_state_client,
            request_session_id,
            token_exchange_client,
            webhook_tenant_mapping_installation_id,
            repo_owner,
            repo_name,
            branch,
        )


# using installation id for augment-test-org github app
@pytest.mark.parametrize(
    "webhook_tenant_mapping_installation_id", [55837488], indirect=True
)
def test_repository_uploader_handle_ignore_files(
    github_webhook_listener_url: str,
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    webhook_tenant_mapping_installation_id: tuple[str, int],
):
    repo_owner = "augment-test-org"
    repo_name = "test-repo-augmentignore"
    branch = "main"

    # Test cases defined in this repo
    # https://github.com/augment-test-org/test-repo-augmentignore
    # checkpoint ids are manually created based on files in commit
    # and what should be uploaded to content manager
    test_cases = [
        PushEventTestCase(
            before_sha="",
            after_sha="aea738b39524c4868e84192deb9c9fdbbd8c6d3d",
            checkpoint_id="1d6fced2cba9476aae97903ba9b97675d0e66bd3781ab8f08a7663d7fe9dc8b8",
            description="Initial commit of mostly empty repo",
        ),
        PushEventTestCase(
            before_sha="aea738b39524c4868e84192deb9c9fdbbd8c6d3d",
            after_sha="26d5c03bb67d48c0afb1cb52dc6b0cd2b2907d2f",
            checkpoint_id="e8a31d96d0976105e7b79e53d30d65d63321ef637c880de577d8283fe669f292",
            description="Add initial set of test cases and ignore files",
        ),
        PushEventTestCase(
            before_sha="26d5c03bb67d48c0afb1cb52dc6b0cd2b2907d2f",
            after_sha="efc7061a40b457e3ab195e2aa88019e687f71c17",
            checkpoint_id="659a2a9bb88c3a23adcb6680c8699fb0cf2d2fb253302473a06a324f4814f617",
            description="modify a bunch of scripts",
        ),
        PushEventTestCase(
            before_sha="efc7061a40b457e3ab195e2aa88019e687f71c17",
            after_sha="16030dfd668077d2f458dcd61d7eb5f151bdac37",
            checkpoint_id="414130175df91cb3308d56e29ed8acb0d5b92375418d67a7ea3c1531a3a89d66",
            description="remove a bunch of scripts",
        ),
        PushEventTestCase(
            before_sha="16030dfd668077d2f458dcd61d7eb5f151bdac37",
            after_sha="cc2ed2fe1e5515d352316cacdefc5c215275ca13",
            checkpoint_id="659a2a9bb88c3a23adcb6680c8699fb0cf2d2fb253302473a06a324f4814f617",
            description="add the scripts back",
        ),
        PushEventTestCase(
            before_sha="cc2ed2fe1e5515d352316cacdefc5c215275ca13",
            after_sha="4ee5c3100e9f74726425b3d49a0739f6d899ce0d",
            checkpoint_id="b8cd0b137bf795f0a972098aee1c374e2f1d91c48bb1ba4839acbfaf2a7933d3",
            description="add secret-ish files",
        ),
        PushEventTestCase(
            before_sha="4ee5c3100e9f74726425b3d49a0739f6d899ce0d",
            after_sha="e8b1927671ee556499c634f7497aeb7e749d4265",
            checkpoint_id="f1fc8a528a04eb5d3d30704eb0cbdb692c247056ef28674e0346f4cc506e3b77",
            description="add some new directories",
        ),
        PushEventTestCase(
            before_sha="e8b1927671ee556499c634f7497aeb7e749d4265",
            after_sha="7a658d16b1d0213856200f8b7bd4bea3fdea98a0",
            checkpoint_id="452dcdbf3451327ad02771bb1073663970d36dac446df0f09c54bf257270217d",
            description="Remove an .augmentignore file",
        ),
        PushEventTestCase(
            before_sha="7a658d16b1d0213856200f8b7bd4bea3fdea98a0",
            after_sha="6ca810d55458dffdcc6e4f9de23f1d5c5b123b41",
            checkpoint_id="f1fc8a528a04eb5d3d30704eb0cbdb692c247056ef28674e0346f4cc506e3b77",
            description="Add the .augmentignore file back",
        ),
        PushEventTestCase(
            before_sha="6ca810d55458dffdcc6e4f9de23f1d5c5b123b41",
            after_sha="c6db625853e6c7088da8dfd25efa75d3ecf9a6d1",
            checkpoint_id="4317c0893cca0416805fe357b22df7bcd7a6833c0bc48847f42948e25e98c604",
            description="Rename file from included to ignored",
        ),
        PushEventTestCase(
            before_sha="c6db625853e6c7088da8dfd25efa75d3ecf9a6d1",
            after_sha="32b17c1c6cc8294caf1198605fa2a944144073a9",
            checkpoint_id="f1fc8a528a04eb5d3d30704eb0cbdb692c247056ef28674e0346f4cc506e3b77",
            description="Rename file from ignored to included",
        ),
        PushEventTestCase(
            before_sha="32b17c1c6cc8294caf1198605fa2a944144073a9",
            after_sha="7b18dde755164febbd032a0878171dab6010e16c",
            checkpoint_id="0854ec4000cbe3c5bd509c73c41a6c326cb2584431239e1e486d1c8468c381a0",
            description="Rename files with no change in ignore status",
        ),
        PushEventTestCase(
            before_sha="7b18dde755164febbd032a0878171dab6010e16c",
            after_sha="47f173063103be7807cced6d01e8ff7c6d6bd0b8",
            checkpoint_id="0ec64a8f526132dc35a84c2f054440b9031a286a30749c738a9112edcf2ce14b",
            description="Add nested folders with no ignore files",
        ),
    ]

    for test_case in test_cases:
        run_test_case(
            test_case,
            github_webhook_listener_url,
            github_state_client,
            request_session_id,
            token_exchange_client,
            webhook_tenant_mapping_installation_id,
            repo_owner,
            repo_name,
            branch,
        )


def test_no_indexed(
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    request_context: RequestContext,
    repo_name: str,
):
    indexed_refs: github_state_pb2.GetIndexedRefCheckpointsResponse = (
        github_state_client.GetIndexedRefCheckpoints(
            github_state_pb2.GetIndexedRefCheckpointsRequest(),
            metadata=request_context.to_metadata(),
        )
    )
    assert indexed_refs is not None
    found = False
    for ref in indexed_refs.ref_checkpoints:
        if ref.ref.repo.repo_name == repo_name:
            found = True
            break
    assert not found


def test_update(
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    content_manager_client: services.content_manager.client.content_manager_client.ContentManagerClient,
    request_context: RequestContext,
    content_factory: Callable[[int], bytes],
    repo_name: str,
):
    content = content_factory(100)
    (blob_name, _) = content_manager_client.upload(
        content, path="file1.txt", request_context=request_context
    )

    req = github_state_pb2.UpdateCurrentRefCheckpointRequest()
    req.ref.ref = "main"
    req.ref.repo.repo_owner = "owner"
    req.ref.repo.repo_name = repo_name
    req.commit_sha = "abc123"
    req.commit_time.seconds = 1234567890
    req.parent_commit_shas.append("abc122")
    req.blobs.baseline_checkpoint_id = ""
    req.blobs.added.append(encode_blob_name(blob_name))
    req.force_upload = False
    req.diff_infos.append(
        github_state_pb2.DiffInfo(
            file_path="file1.txt",
            content_blob_name=blob_names_pb2.BlobName(
                name_hex=blob_name,
            ),
        )
    )

    req_seq = [req]
    github_state_client.UpdateCurrentRefCheckpoint(
        iter(req_seq),
        metadata=request_context.to_metadata(),
    )

    for _ in range(12):
        indexed_refs: github_state_pb2.GetIndexedRefCheckpointsResponse = (
            github_state_client.GetIndexedRefCheckpoints(
                github_state_pb2.GetIndexedRefCheckpointsRequest(),
                metadata=request_context.to_metadata(),
            )
        )
        found = False
        for ref in indexed_refs.ref_checkpoints:
            if ref.ref.repo.repo_name == repo_name and ref.commit_sha == "abc123":
                found = True
                break
        if found:
            break

        time.sleep(10)
    else:
        assert False, "timeout"

    content2 = content_factory(100)
    (blob_name2, _) = content_manager_client.upload(
        content2, path="file2.txt", request_context=request_context
    )

    req = github_state_pb2.UpdateCurrentRefCheckpointRequest()
    req.ref.ref = "main"
    req.ref.repo.repo_owner = "owner"
    req.ref.repo.repo_name = repo_name
    req.commit_sha = "abc124"
    req.commit_time.seconds = 1234567890
    req.diff_commit_sha = "abc123"
    req.parent_commit_shas.append("abc123")
    req.blobs.baseline_checkpoint_id = ""
    req.blobs.added.append(encode_blob_name(blob_name2))
    req.force_upload = False
    req.diff_infos.append(
        github_state_pb2.DiffInfo(
            file_path="file2.txt",
            content_blob_name=blob_names_pb2.BlobName(
                name_hex=blob_name2,
            ),
        )
    )

    req_seq = [req]
    github_state_client.UpdateCurrentRefCheckpoint(
        iter(req_seq),
        metadata=request_context.to_metadata(),
    )

    for _ in range(12):
        indexed_refs: github_state_pb2.GetIndexedRefCheckpointsResponse = (
            github_state_client.GetIndexedRefCheckpoints(
                github_state_pb2.GetIndexedRefCheckpointsRequest(),
                metadata=request_context.to_metadata(),
            )
        )
        found = False
        for ref in indexed_refs.ref_checkpoints:
            if ref.ref.repo.repo_name == repo_name and ref.commit_sha == "abc124":
                found = True
                break
        if found:
            break

        time.sleep(10)
    else:
        assert False, "timeout"


def test_update_many(
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    content_manager_client: services.content_manager.client.content_manager_client.ContentManagerClient,
    request_context: RequestContext,
    content_factory: Callable[[int], bytes],
    repo_name: str,
):
    blob_names = []
    for i in range(500):
        content = content_factory(100)
        (blob_name, _) = content_manager_client.upload(
            content, path=f"file{i}.txt", request_context=request_context
        )
        blob_names.append(blob_name)

    req = github_state_pb2.UpdateCurrentRefCheckpointRequest()
    req.ref.ref = "main"
    req.ref.repo.repo_owner = "owner"
    req.ref.repo.repo_name = repo_name
    req.commit_sha = "abc123"
    req.commit_time.seconds = 1234567890
    req.parent_commit_shas.append("abc122")
    req.blobs.baseline_checkpoint_id = ""
    for i in range(500):
        req.diff_infos.append(
            github_state_pb2.DiffInfo(
                file_path=f"file{i}.txt",
                content_blob_name=blob_names_pb2.BlobName(
                    name_hex=blob_names[i],
                ),
            )
        )
    req.blobs.added.extend(map(encode_blob_name, blob_names))

    req_seq = [req]
    github_state_client.UpdateCurrentRefCheckpoint(
        iter(req_seq),
        metadata=request_context.to_metadata(),
    )

    for _ in range(12):
        indexed_refs: github_state_pb2.GetIndexedRefCheckpointsResponse = (
            github_state_client.GetIndexedRefCheckpoints(
                github_state_pb2.GetIndexedRefCheckpointsRequest(),
                metadata=request_context.to_metadata(),
            )
        )
        found = False
        for ref in indexed_refs.ref_checkpoints:
            if ref.ref.repo.repo_name == repo_name and ref.commit_sha == "abc123":
                found = True
                break
        if found:
            break

        time.sleep(10)
    else:
        assert False, "timeout"


# using installation id for augment-test-org github app
@pytest.mark.parametrize(
    "webhook_tenant_mapping_installation_id", [55837488], indirect=True
)
@pytest.mark.parametrize("start_with_symlinks", [True, False])
def test_repository_uploader_handle_symlinks(
    github_webhook_listener_url: str,
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    webhook_tenant_mapping_installation_id: tuple[str, int],
    start_with_symlinks: bool,
):
    repo_owner = "augment-test-org"
    repo_name = "test-github-app-repo"
    branch = "main"

    # Test cases defined in this repo
    # https://github.com/augment-test-org/test-github-app-repo/
    # checkpoint ids are manually created based on files in commit
    # and what should be uploaded to content manager

    # Symlinks are ignored, which is why all of these checkpoints are the same
    if start_with_symlinks:
        test_cases_initial = [
            PushEventTestCase(
                before_sha="",
                after_sha="55001f42871caa0b6f2b2795bf9a43e159c26a19",
                checkpoint_id="aba7ab69c9e89c3afd9e8e8043754c9f841dec7b2237ffd936f9fdb90522b717",
                description="Initial commits, with symlinks",
            ),
        ]
    else:
        test_cases_initial = [
            PushEventTestCase(
                before_sha="",
                after_sha="1d64aef0b51c33088d3d886497a77284729ea006",
                checkpoint_id="aba7ab69c9e89c3afd9e8e8043754c9f841dec7b2237ffd936f9fdb90522b717",
                description="Initial commits",
            ),
            PushEventTestCase(
                before_sha="1d64aef0b51c33088d3d886497a77284729ea006",
                after_sha="55001f42871caa0b6f2b2795bf9a43e159c26a19",
                checkpoint_id="aba7ab69c9e89c3afd9e8e8043754c9f841dec7b2237ffd936f9fdb90522b717",
                description="Add symlinks",
            ),
        ]
    test_cases_later = [
        PushEventTestCase(
            before_sha="55001f42871caa0b6f2b2795bf9a43e159c26a19",
            after_sha="2e684585dec57cfbd08f1c3f026a8a41036b4bfd",
            checkpoint_id="aba7ab69c9e89c3afd9e8e8043754c9f841dec7b2237ffd936f9fdb90522b717",
            description="Move symlinks",
        ),
        PushEventTestCase(
            before_sha="2e684585dec57cfbd08f1c3f026a8a41036b4bfd",
            after_sha="896774ec32a6a36bb7345c3ac04c958ca2f70803",
            checkpoint_id="aba7ab69c9e89c3afd9e8e8043754c9f841dec7b2237ffd936f9fdb90522b717",
            description="Delete symlinks",
        ),
    ]
    test_cases = test_cases_initial + test_cases_later

    for test_case in test_cases:
        run_test_case(
            test_case,
            github_webhook_listener_url,
            github_state_client,
            request_session_id,
            token_exchange_client,
            webhook_tenant_mapping_installation_id,
            repo_owner,
            repo_name,
            branch,
        )


# using installation id for augment-test-org github app
@pytest.mark.parametrize(
    "webhook_tenant_mapping_installation_id", [55837488], indirect=True
)
@pytest.mark.parametrize("start_with_submodule", [True, False])
def test_repository_uploader_handle_submodule(
    github_webhook_listener_url: str,
    github_state_client: github_state_pb2_grpc.GithubStateStub,
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    webhook_tenant_mapping_installation_id: tuple[str, int],
    start_with_submodule: bool,
):
    repo_owner = "augment-test-org"
    repo_name = "test-github-app-repo"
    branch = "main"

    # Test cases defined in this repo
    # https://github.com/augment-test-org/test-github-app-repo/
    # checkpoint ids are manually created based on files in commit
    # and what should be uploaded to content manager

    # Submodules are ignored, but the `.gitmodules` file is not, which is why
    # these change
    if start_with_submodule:
        test_cases_initial = [
            PushEventTestCase(
                before_sha="",
                after_sha="2c4f7682d60c7fbc9d1b2571989af96d616c465d",
                checkpoint_id="2703824d41ea2c7a225c48054d55cbad551e42912255d83b7fd492fa2b0b8d4a",
                description="Initial commits, with submodule",
            ),
        ]
    else:
        test_cases_initial = [
            PushEventTestCase(
                before_sha="",
                after_sha="896774ec32a6a36bb7345c3ac04c958ca2f70803",
                checkpoint_id="aba7ab69c9e89c3afd9e8e8043754c9f841dec7b2237ffd936f9fdb90522b717",
                description="Initial commits",
            ),
            PushEventTestCase(
                before_sha="896774ec32a6a36bb7345c3ac04c958ca2f70803",
                after_sha="2c4f7682d60c7fbc9d1b2571989af96d616c465d",
                checkpoint_id="2703824d41ea2c7a225c48054d55cbad551e42912255d83b7fd492fa2b0b8d4a",
                description="Add submodule",
            ),
        ]
    test_cases_later = [
        PushEventTestCase(
            before_sha="2c4f7682d60c7fbc9d1b2571989af96d616c465d",
            after_sha="d1d9532dce733d5f0bcecb65ed676b4e4060fd53",
            checkpoint_id="2703824d41ea2c7a225c48054d55cbad551e42912255d83b7fd492fa2b0b8d4a",
            description="Update a submodule",
        ),
        PushEventTestCase(
            before_sha="d1d9532dce733d5f0bcecb65ed676b4e4060fd53",
            after_sha="06947f1273b4659e305100620beb36e490fd4c00",
            checkpoint_id="4cbe7a4825f62b391eb481ad15140f4910a5e942437182e91eda6ad5322c2c13",
            description="Rename a submodule",
        ),
        PushEventTestCase(
            before_sha="06947f1273b4659e305100620beb36e490fd4c00",
            after_sha="26c6fed6774a2aadd3c41f40023312539c91259c",
            checkpoint_id="aba7ab69c9e89c3afd9e8e8043754c9f841dec7b2237ffd936f9fdb90522b717",
            description="Delete submodule",
        ),
    ]
    test_cases = test_cases_initial + test_cases_later

    for test_case in test_cases:
        run_test_case(
            test_case,
            github_webhook_listener_url,
            github_state_client,
            request_session_id,
            token_exchange_client,
            webhook_tenant_mapping_installation_id,
            repo_owner,
            repo_name,
            branch,
        )

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/bigtable_proxy/server:kubecfg_central",
        "//services/deploy:base_kubecfg",
        "//services/deploy:shard_namespace_base_kubecfg",
        "//services/deploy:starethanol6_16_1_proj512_kubecfg",
        "//services/deploy/completion:rogue_1B_fp8_starethanol6_16_1_proj512_kubecfg",
        "//services/integrations/github/processor/server:kubecfg",
        "//services/integrations/github/state/server:kubecfg",
        "//services/integrations/github/webhook_listener:kubecfg",
        "//services/integrations/github/webhook_listener:kubecfg_pubsub",
    ],
)

pytest_test(
    name = "github_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "github_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "postmerge-test",
        "system-test",
        "system-test-gpu",
    ],
    deps = [
        "//base/python/grpc:health_check",
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/content_manager/client",
        "//services/integrations/github/state:github_state_py_proto",
        "//services/integrations/github/state/client:client_py",
        "//services/lib/request_context:request_context_py",
        "//services/tenant_watcher/client",
        "//services/test/fake_feature_flags:client_py",
        "//services/token_exchange/client:client_py",
        requirement("kubernetes"),
    ],
)

load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")
load("//tools/bzl:python.bzl", "py_proto_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "github_event_proto",
    srcs = ["github_event.proto"],
    visibility = [
        "//services/integrations:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

py_proto_library(
    name = "github_event_py_proto",
    protos = [":github_event_proto"],
    visibility = [
        "//services/integrations:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
)

go_proto_library(
    name = "github_event_go_proto",
    generate_out_directory = "eventpb",
    importpath = "github.com/augmentcode/augment/services/integrations/github/eventpb",
    proto = ":github_event_proto",
    visibility = [
        "//services/integrations:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
)

ts_proto_library(
    name = "github_event_ts_proto",
    node_modules = "//:node_modules",
    proto = ":github_event_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

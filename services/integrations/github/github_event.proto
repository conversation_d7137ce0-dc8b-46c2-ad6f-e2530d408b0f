syntax = "proto3";

package github_event;

import "google/protobuf/timestamp.proto";

message EventMetadata {
  int64 installation_id = 1;

  string tenant_id = 2;
  string tenant_name = 4;
  string request_id = 3;
}

message Repository {
  string name = 1 [debug_redact = true];
  string owner = 2 [debug_redact = true];
}

message PushEvent {
  Repository repository = 1;
  string ref = 2 [debug_redact = true];
  string before_sha = 3 [debug_redact = true];
  string after_sha = 4 [debug_redact = true];
  google.protobuf.Timestamp pushed_at_timestamp = 5;
  bool forced = 6;
  google.protobuf.Timestamp head_commit_timestamp = 7;
}

// Subet of the fields provided by Github about a User in webhook events. See
// `type User` in `github.com/google/go-github/v64/github/users.go`. Note that
// technically all of these fields are optional.
message UserInfo {
  // The github username.
  string login = 1;
  // The user's email.
  string email = 2;
}

// NOTE: this InstallationEvent doesn't directly map to a single Github webhook
// event type. At time of writing it can represent both "installation" and
// "installation_repositories" events.
message InstallationEvent {
  repeated Repository repos = 1;
  string action = 2; // "added", "removed", or "deleted"
  // The user who triggered the event. Some events (like repository added events)
  // will have neither a sender nor a requester as they are automatically triggered
  // by Github.
  optional UserInfo sender = 3;
  // The user who requested the installation (for cases where the user doesn't
  // have admin privileges, in which case the sender is the admin who approves
  // the request).
  optional UserInfo requester = 4;
}

message GithubEvent {
  EventMetadata metadata = 1;
  string event_type = 2; // e.g. "push"
  oneof event {
    PushEvent push = 3;
    InstallationEvent installation = 4;
  }
}

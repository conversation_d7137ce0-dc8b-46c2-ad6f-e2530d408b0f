load("@python_pip//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_library")
load("//tools/bzl:go.bzl", "go_library")

py_library(
    name = "http_client",
    srcs = ["http_client.py"],
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        requirement("requests"),
    ],
)

go_library(
    name = "http_client_go",
    srcs = ["http_client.go"],
    importpath = "github.com/augmentcode/augment/services/integrations/lib",
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
    ],
)

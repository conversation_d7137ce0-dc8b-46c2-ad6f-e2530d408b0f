"""A generic third-party HTTP client.

Handles things like retries and auth that we need in all of them.
"""

import logging
import time
from typing import Callable, Iterable, Optional
from urllib.parse import urljoin

import requests
from requests.auth import AuthBase

log = logging.getLogger(__name__)


class BearerAuth(AuthBase):
    """Bearer auth for requests.

    requests doesn't have a built in Bearer auth.
    """

    def __init__(self, token):
        self.token = token

    def __call__(self, r):
        r.headers["Authorization"] = f"Bearer {self.token}"
        return r


class BaseHTTPClient:
    """Base class for integration clients.

    Maintains the HTTP session and handles retries, which we need to do
    with all of our integrations.
    """

    def __init__(
        self,
        base_url: str | None = None,
        headers: dict[str, str] | None = None,
    ):
        self.base_url = base_url
        self.headers: dict[str, str] | None = headers
        self.session = requests.Session()
        if headers:
            self.session.headers.update(headers)

    def make_request(
        self,
        path: str,
        method: str = "GET",
        base_url: str | None = None,
        headers: dict[str, str] | None = None,
        retries: int = 3,
        retriable_codes: Iterable[int] = (429, 502, 503, 504),
        auth: AuthBase | None = None,
        token_refresh_callback: Optional[Callable[[], AuthBase]] = None,
        **kwargs,
    ) -> requests.Response:
        """Make a request to the integration API; handles retry etc.

        Args:
            path: The path to the API endpoint relative to the base url.
            method: The HTTP method to use.  Defaults to GET.
            retries: The number of times to retry the request.  Defaults to 3.
            retriable_codes: The HTTP status codes that should be retried.  Defaults to 429, 502, 503, 504.
            auth: Override auth for the request.  If not provided, the client's auth will be used.
            **kwargs: Any other arguments to pass to requests.request.
        """
        # update base_url if needed
        base_url = base_url or self.base_url
        if not base_url:
            raise ValueError("No base URL provided")

        path = path.lstrip("/")  # Remove leading slash to prevent urljoin issues
        url = urljoin(base_url, path)

        # combine supplied headers with default headers
        headers = headers or {}
        if self.headers:
            headers = {**self.headers, **headers}
        # Only set token refresh callback for Bearer auth
        if not isinstance(auth, BearerAuth):
            token_refresh_callback = None

        retriable_codes = frozenset(retriable_codes)
        for attempt in range(retries):
            try:
                response = self.session.request(
                    method, url, headers=headers, auth=auth, **kwargs
                )

                # Handle 401 with token refresh if we have a callback
                if (
                    response.status_code == 401
                    and attempt != retries - 1
                    and token_refresh_callback
                ):
                    log.info("Received 401 from API, refreshing token and retrying")
                    # Get new auth with refreshed token
                    auth = token_refresh_callback()
                    # Retry immediately with new token
                    continue

                # Retry on rate limits or temporary server errors
                if response.status_code in retriable_codes:
                    if attempt == retries - 1:
                        # On the last attempt, return the response even if it's an error
                        return response

                    # Use Retry-After header or exponential backoff
                    delay = int(response.headers.get("Retry-After", 2**attempt))
                    # Recreate the session, just in case something is wrong with the session
                    self.session = requests.Session()
                    time.sleep(delay)
                    continue

                # Don't raise for status, return the response even if it's an error
                return response

            except requests.exceptions.RequestException as e:
                log.error("Request exception: %s", e)

                # No error code. probably retry anyways
                if attempt == retries - 1:
                    raise
                # Recreate the session, just in case something is wrong with the session
                self.session = requests.Session()
                time.sleep(2**attempt)
        raise Exception(f"Failed to make request after {retries} retries")

package httpclient

import (
	"context"
	"fmt"
	"io"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
)

// BaseHTTPClient handles HTTP requests with retries and bearer auth
type BaseHTTPClient struct {
	baseURL    string
	token      string
	httpClient *http.Client
}

// NewBaseHTTPClient creates a new client with bearer auth
func NewBaseHTTPClient(parent *http.Client, baseURL string, token string) *BaseHTTPClient {
	return &BaseHTTPClient{
		baseURL:    strings.TrimSuffix(baseURL, "/"),
		token:      token,
		httpClient: parent,
	}
}

// MakeRequest makes an HTTP request with retries
func (c *BaseHTTPClient) MakeRequest(
	context context.Context,
	path string,
	method string,
	body io.Reader,
) (*http.Response, error) {
	retriableCodes := map[int]bool{
		429: true, // Rate limit
		502: true, // Bad gateway
		503: true, // Service unavailable
		504: true, // Gateway timeout
	}
	retries := 3
	var lastResponse *http.Response

	url := fmt.Sprintf("%s/%s", c.baseURL, strings.TrimPrefix(path, "/"))

	for attempt := 0; attempt < retries; attempt++ {
		log.Ctx(context).Debug().Msgf("Attempt %d of %d", attempt+1, retries)

		req, err := http.NewRequest(method, url, body)
		if err != nil {
			log.Ctx(context).Error().Err(err).Msg("Error creating request")
			return nil, fmt.Errorf("creating request: %w", err)
		}

		if c.token != "" {
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.token))
		}

		resp, err := c.httpClient.Do(req)
		if err != nil {
			if attempt == retries-1 {
				return lastResponse, err
			}
			time.Sleep(time.Second * time.Duration(math.Pow(2, float64(attempt))))
			continue
		}

		// Store the last response
		lastResponse = resp

		// Handle retriable status codes
		if retriableCodes[resp.StatusCode] {
			if attempt == retries-1 {
				bodyBytes, _ := io.ReadAll(resp.Body)
				log.Ctx(context).Error().Msgf("Final retry failed. Response body: %s", string(bodyBytes))
				return resp, fmt.Errorf("request failed with status %d, error: %s", resp.StatusCode, string(bodyBytes))
			}

			// Use Retry-After header or exponential backoff
			delay := math.Pow(2, float64(attempt))
			if retryAfter := resp.Header.Get("Retry-After"); retryAfter != "" {
				if d, err := strconv.Atoi(retryAfter); err == nil {
					delay = float64(d)
				}
			}

			log.Ctx(context).Debug().Msgf("Retriable status code %d, waiting %v seconds before retry", resp.StatusCode, delay)
			resp.Body.Close()
			time.Sleep(time.Second * time.Duration(delay))
			continue
		}

		if resp.StatusCode >= 400 {
			bodyBytes, _ := io.ReadAll(resp.Body)
			log.Ctx(context).Error().Msgf("Error response body: %s", string(bodyBytes))
			resp.Body.Close()
			return resp, fmt.Errorf("request failed with status %d, error: %s", resp.StatusCode, string(bodyBytes))
		}

		return resp, nil
	}

	return lastResponse, fmt.Errorf("failed to make request after %d retries", retries)
}

// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/notion/notion.proto (package notion, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { NotionToolName } from "../../agents/agents_pb.js";

/**
 * @generated from message notion.NotionSearchRequest
 */
export declare class NotionSearchRequest extends Message<NotionSearchRequest> {
  /**
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * @generated from field: int32 max_results = 2;
   */
  maxResults: number;

  constructor(data?: PartialMessage<NotionSearchRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.NotionSearchRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotionSearchRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotionSearchRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotionSearchRequest;

  static equals(a: NotionSearchRequest | PlainMessage<NotionSearchRequest> | undefined, b: NotionSearchRequest | PlainMessage<NotionSearchRequest> | undefined): boolean;
}

/**
 * @generated from message notion.NotionSearchResponse
 */
export declare class NotionSearchResponse extends Message<NotionSearchResponse> {
  /**
   * @generated from field: string page_list_markdown = 1;
   */
  pageListMarkdown: string;

  constructor(data?: PartialMessage<NotionSearchResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.NotionSearchResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotionSearchResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotionSearchResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotionSearchResponse;

  static equals(a: NotionSearchResponse | PlainMessage<NotionSearchResponse> | undefined, b: NotionSearchResponse | PlainMessage<NotionSearchResponse> | undefined): boolean;
}

/**
 * @generated from message notion.NotionReadPageRequest
 */
export declare class NotionReadPageRequest extends Message<NotionReadPageRequest> {
  /**
   * @generated from field: string page_id = 1;
   */
  pageId: string;

  constructor(data?: PartialMessage<NotionReadPageRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.NotionReadPageRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotionReadPageRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotionReadPageRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotionReadPageRequest;

  static equals(a: NotionReadPageRequest | PlainMessage<NotionReadPageRequest> | undefined, b: NotionReadPageRequest | PlainMessage<NotionReadPageRequest> | undefined): boolean;
}

/**
 * @generated from message notion.NotionReadPageResponse
 */
export declare class NotionReadPageResponse extends Message<NotionReadPageResponse> {
  /**
   * @generated from field: string page_markdown = 1;
   */
  pageMarkdown: string;

  constructor(data?: PartialMessage<NotionReadPageResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.NotionReadPageResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotionReadPageResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotionReadPageResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotionReadPageResponse;

  static equals(a: NotionReadPageResponse | PlainMessage<NotionReadPageResponse> | undefined, b: NotionReadPageResponse | PlainMessage<NotionReadPageResponse> | undefined): boolean;
}

/**
 * @generated from message notion.NotionGetToolsRequest
 */
export declare class NotionGetToolsRequest extends Message<NotionGetToolsRequest> {
  constructor(data?: PartialMessage<NotionGetToolsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.NotionGetToolsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotionGetToolsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotionGetToolsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotionGetToolsRequest;

  static equals(a: NotionGetToolsRequest | PlainMessage<NotionGetToolsRequest> | undefined, b: NotionGetToolsRequest | PlainMessage<NotionGetToolsRequest> | undefined): boolean;
}

/**
 * @generated from message notion.NotionGetToolsResponse
 */
export declare class NotionGetToolsResponse extends Message<NotionGetToolsResponse> {
  /**
   * @generated from field: repeated agents.NotionToolName tool_names = 1;
   */
  toolNames: NotionToolName[];

  constructor(data?: PartialMessage<NotionGetToolsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.NotionGetToolsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotionGetToolsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotionGetToolsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotionGetToolsResponse;

  static equals(a: NotionGetToolsResponse | PlainMessage<NotionGetToolsResponse> | undefined, b: NotionGetToolsResponse | PlainMessage<NotionGetToolsResponse> | undefined): boolean;
}

/**
 * @generated from message notion.HydrateNotionSettingsRequest
 */
export declare class HydrateNotionSettingsRequest extends Message<HydrateNotionSettingsRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  constructor(data?: PartialMessage<HydrateNotionSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.HydrateNotionSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateNotionSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateNotionSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateNotionSettingsRequest;

  static equals(a: HydrateNotionSettingsRequest | PlainMessage<HydrateNotionSettingsRequest> | undefined, b: HydrateNotionSettingsRequest | PlainMessage<HydrateNotionSettingsRequest> | undefined): boolean;
}

/**
 * @generated from message notion.HydrateNotionSettingsResponse
 */
export declare class HydrateNotionSettingsResponse extends Message<HydrateNotionSettingsResponse> {
  constructor(data?: PartialMessage<HydrateNotionSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "notion.HydrateNotionSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateNotionSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateNotionSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateNotionSettingsResponse;

  static equals(a: HydrateNotionSettingsResponse | PlainMessage<HydrateNotionSettingsResponse> | undefined, b: HydrateNotionSettingsResponse | PlainMessage<HydrateNotionSettingsResponse> | undefined): boolean;
}


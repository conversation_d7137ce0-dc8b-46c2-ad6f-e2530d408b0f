load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "notion_proto",
    srcs = ["notion.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@googleapis//google/rpc:status_proto",
    ],
)

py_grpc_library(
    name = "notion_py_proto",
    protos = [":notion_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

ts_proto_library(
    name = "notion_ts_proto",
    node_modules = "//:node_modules",
    proto = ":notion_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

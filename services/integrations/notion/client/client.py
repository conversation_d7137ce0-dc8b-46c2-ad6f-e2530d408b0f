"""A Python client library for the Notion server service."""

import logging
from typing import List, Optional

import grpc

from base.python.grpc import client_options
from services.agents import agents_pb2
from services.integrations.notion import notion_pb2, notion_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> notion_pb2_grpc.NotionStub:
    """Setup the client stub for the Notion service.

    Args:
        endpoint: The endpoint of the Notion service.
        credentials: The credentials to use for the channel (optional)
        options: Additional gRPC channel options (optional)

    Returns:
        The client stub for the Notion service.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, options or [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = notion_pb2_grpc.NotionStub(channel)
    return stub


class NotionClient:
    """Client for interacting with the Notion service."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        self.stub = setup_stub(endpoint, credentials, options=options)

    def is_configured(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> bool:
        """Check if Notion credentials are configured in admin settings.

        Args:
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            True if credentials are configured, False otherwise.
        """
        request = notion_pb2.NotionIsConfiguredRequest()
        response = self.stub.IsConfigured(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.is_configured

    def search_notion(
        self,
        query: str,
        request_context: RequestContext,
        max_results: int = 10,
        credentials: str | None = None,
        timeout: float = 30,
    ) -> notion_pb2.NotionSearchResponse:
        """Search Notion pages.

        Args:
            query: The search query string.
            max_results: Maximum number of results to return (default: 10).
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            Markdown-formatted search results.
        """
        request = notion_pb2.NotionSearchRequest(query=query, max_results=max_results)
        if credentials is not None:
            request.credentials = credentials

        response = self.stub.Search(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def read_page(
        self,
        page_id: str,
        request_context: RequestContext,
        credentials: str | None = None,
        timeout: float = 30,
    ) -> notion_pb2.NotionReadPageResponse:
        """Get content of a Notion page.

        Args:
            page_id: The Notion page ID.
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            Markdown-formatted page content.
        """

        request = notion_pb2.NotionReadPageRequest(page_id=page_id)
        if credentials is not None:
            request.credentials = credentials

        response = self.stub.ReadPage(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def get_oauth_url(
        self, request_context: RequestContext, timeout: float = 30
    ) -> str:
        """Get the Notion OAuth URL.

        Args:
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            The Notion OAuth URL.
        """
        request = notion_pb2.NotionGetOAuthUrlRequest()
        response = self.stub.GetOAuthUrl(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.oauth_url

    def revoke_oauth_token(
        self,
        request: notion_pb2.RevokeOAuthTokenRequest,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> notion_pb2.RevokeOAuthTokenResponse:
        """Revoke the Notion OAuth token.

        Args:
            request: Revoke OAuth token request
            request_context: The request context to use.
            timeout: The timeout in seconds.
        """
        response = self.stub.RevokeOAuthToken(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def read_database(
        self,
        database_id: str,
        request_context: RequestContext,
        credentials: str | None = None,
        timeout: float = 30,
    ) -> notion_pb2.NotionReadDatabaseResponse:
        """Get content of a Notion database.

        Args:
            database_id: The Notion database ID.
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            Markdown-formatted database content and subpages.
        """

        request = notion_pb2.NotionReadDatabaseRequest(database_id=database_id)
        if credentials is not None:
            request.credentials = credentials

        response = self.stub.ReadDatabase(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

"""Formatters for Notion entities to markdown text."""

from services.integrations.notion.server.notion_client_wrapper import (
    NotionPage,
    NotionDatabase,
)


def format_page_markdown(page: NotionPage) -> str:
    """Format a Notion page as markdown.

    Args:
        page: NotionPage object

    Returns:
        Markdown formatted string with page details
    """
    lines = [
        f"# {page.title} (Page ID: {page.id})",
        f"URL: {page.url}",
        f"Created Time: {page.created_time}",
        f"Last Edited Time: {page.last_edited_time}",
        f"Parent Type: {page.parent_type} | Parent ID: {page.parent_id}",
        f"Properties: {page.properties if page.properties else None}",
        "\n## Content",
        *[block.content for block in page.blocks],
    ]

    return "\n\n".join(lines)


def format_page_list_markdown(pages: list[NotionPage]) -> str:
    """Format a list of Notion pages as a compact markdown list.

    Args:
        pages: List of NotionPage objects

    Returns:
        Markdown formatted string with summarized pages
    """
    if not pages:
        return "No pages found."

    formatted_pages = []
    for page in pages:
        formatted_page = (
            f"- [{page.title}]({page.url})\n"
            f"  ID: {page.id} | Created: {page.created_time} | Last Edited: {page.last_edited_time}"
        )
        if page.properties:
            formatted_page += f"  Properties: {page.properties}\n"
        formatted_pages.append(formatted_page)

    return "\n\n".join(formatted_pages)


def format_database_markdown(database: NotionDatabase) -> str:
    """Format a Notion database as markdown.

    Args:
        database: NotionDatabase object

    Returns:
        Markdown formatted string with database details
    """
    lines = [
        f"# {database.title} (Database ID: {database.id})",
        f"URL: {database.url}",
        f"Created Time: {database.created_time}",
        f"Last Edited Time: {database.last_edited_time}",
        f"Parent ID: {database.parent_id}",
        f"Database Properties: {', '.join(database.properties)}",
        "\n## Pages",
        format_page_list_markdown(database.pages),
    ]

    return "\n\n".join(lines)

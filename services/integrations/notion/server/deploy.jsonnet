local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'notion';

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    cloud=cloud,
    env=env,
    namespace=namespace,
    iam=true
  );

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(
    cloud=cloud,
    env=env,
    namespace=namespace,
    appName=appName
  );

  local requestInsightPublisher = publisherLib(cloud=cloud, env=env, namespace=namespace, appName=appName);

  // creates a service for the pod, also create a global service if necessary
  // to allow the lead cluster (customer-ui) to talk to this pod. We only
  // deploy these when necessary, to avoid running into GCP limits
  local useGlobalService = !cloudInfo.isLeadCluster(cloud);
  local serverDnsNames = grpcLib.grpcServiceNames(appName);
  local centralServerDnsNames = grpcLib.grpcServiceNamespaceNames(appName, namespace=namespace) + if useGlobalService then [
    grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=appName, namespace=namespace),
  ] else [];
  local services = [
    grpcLib.grpcService(appName=appName, namespace=namespace),
  ] + if useGlobalService then [
    grpcLib.globalGrpcService(cloud=cloud, appName=appName, namespace=namespace),
  ] else [];

  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs'
  );

  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace)
  );

  local serverCert = certLib.createServerCert(
    name='%s-server-cert' % appName,
    namespace=namespace,
    appName=appName,
    dnsNames=serverDnsNames,
    volumeName='server-certs'
  );

  local centralServerCert = certLib.createCentralServerCert(
    name='%s-central-server-certificate' % appName,
    namespace=namespace,
    appName=appName,
    dnsNames=centralServerDnsNames,
    env=env,
    volumeName='central-certs'
  );

  local notionSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='app-secret',
    version={
      PROD: '3',
      STAGING: '2',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local notionClientId = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='client-id',
    version={
      PROD: '3',
      STAGING: '2',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local config = {
    port: 50051,
    server_mtls: if mtls then serverCert.config else null,
    client_mtls: if mtls then clientCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    central_server_mtls: if mtls then centralServerCert.config else null,
    settings_endpoint: 'settings-svc:50051',
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    auth_config: {
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    },
    notion_app_callback_url: 'https://%s/notionCallback' % endpointsLib.getCustomerUiHostname(env=env, namespace=namespace, cloud=cloud),
    notion_app_secrets_path: notionSecret.filePath,
    notion_app_client_id_path: notionClientId.filePath,
  };

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container = {
    name: appName,
    target: {
      name: '//services/integrations/notion/server:image',
      dst: 'notion-server',
    },
    args: [
      '--config',
      configMap.filename,
      '--request-insight-publisher-config-file',
      requestInsightPublisher.configFilePath,
    ],
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    volumeMounts: [
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      centralServerCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      requestInsightPublisher.volumeMountDef,
      notionSecret.volumeMountDef,
      notionClientId.volumeMountDef,
    ],
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [container],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    volumes: [
      configMap.podVolumeDef,
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
      centralServerCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      requestInsightPublisher.podVolumeDef,
      notionSecret.podVolumeDef,
      notionClientId.podVolumeDef,
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    clientCert.objects,
    centralClientCert.objects,
    centralServerCert.objects,
    dynamicFeatureFlags.k8s_objects,
    notionSecret.objects,
    notionClientId.objects,
    deployment,
    services,
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ])

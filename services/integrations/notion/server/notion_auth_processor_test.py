"""Tests for Notion Auth Processor"""

import pytest
from unittest.mock import Mock, patch, ANY
from pydantic import SecretStr
from services.integrations.notion.server.notion_auth_processor import (
    NotionAuthProcessor,
    NotionValidationError,
    NotionAuthInternalError,
)
from services.settings import settings_pb2
from services.integrations.notion import notion_pb2
from services.integrations.notion.server.notion_client_wrapper import (
    NotionUnauthorizedError,
)


@pytest.fixture
def mock_settings_client():
    return Mock()


@pytest.fixture
def notion_auth_processor(mock_settings_client):
    with patch("pathlib.Path.read_text") as mock_read_text:
        mock_read_text.return_value = (
            "test_secret\n"  # Simulate file content with newline
        )

        return NotionAuthProcessor(
            mock_settings_client,
            "https://example.com/notionCallback",
            "test_secret_path",
            "test_client_id_path",
        )


# Test for getting API credentials with a valid access token
def test_exchange_code_for_api_credentials_success(
    notion_auth_processor, mock_settings_client
):
    # Mock successful API response
    mock_response = Mock()
    mock_response.json.return_value = {
        "access_token": "test_access_token",
        "bot_id": "test_bot_id",
        "workspace_name": "Test Workspace",
        "workspace_id": "test_workspace_id",
        "owner": {"user": {"name": "Test User", "id": "test_user_id"}},
    }
    mock_response.ok = True

    with patch("requests.post", return_value=mock_response) as mock_post:
        settings_response = settings_pb2.GetUserSettingsResponse(
            settings=settings_pb2.UserSettings()
        )
        result = notion_auth_processor._exchange_code_for_api_credentials(
            "test_code", settings_response
        )

        assert result.notion_user_settings.access_token == "test_access_token"
        assert result.notion_user_settings.bot_id == "test_bot_id"
        assert result.notion_user_settings.workspace_name == "Test Workspace"
        assert result.notion_user_settings.workspace_id == "test_workspace_id"

        mock_post.assert_called_once()


# Test for raising an error if we have an error authenticating
def test_exchange_code_for_api_credentials_failure(
    notion_auth_processor, mock_settings_client
):
    # Mock failed API response
    mock_response = Mock()
    mock_response.json.return_value = {"error": "invalid_grant"}
    mock_response.ok = False

    with patch("requests.post", return_value=mock_response) as mock_post:
        settings_response = settings_pb2.GetUserSettingsResponse(
            settings=settings_pb2.UserSettings()
        )

        with pytest.raises(NotionValidationError):
            notion_auth_processor._exchange_code_for_api_credentials(
                "test_code", settings_response
            )

        mock_post.assert_called_once()


# Test successfully hydrating notion settings
def test_hydrate_notion_settings_success(notion_auth_processor, mock_settings_client):
    # Mock the settings response
    mock_settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings_pb2.UserSettings(
            notion_user_settings=settings_pb2.NotionUserSettings()
        ),
        version="1",
    )
    mock_settings_client.get_user_settings.return_value = mock_settings_response

    # Mock the _exchange_code_for_api_credentials function
    mock_settings = settings_pb2.UserSettings(
        notion_user_settings=settings_pb2.NotionUserSettings(
            access_token="test_access_token",
            bot_id="test_bot_id",
            workspace_name="test_workspace_name",
            workspace_id="test_workspace_id",
        )
    )

    notion_auth_processor._exchange_code_for_api_credentials = Mock(
        return_value=mock_settings
    )

    mock_request = notion_pb2.HydrateNotionSettingsRequest()
    mock_context = Mock()
    mock_request_context = Mock()

    result = notion_auth_processor.hydrate_notion_settings(
        "test_code",
        mock_request_context,
        mock_request,
        mock_context,
    )

    assert isinstance(result, notion_pb2.HydrateNotionSettingsResponse)
    notion_auth_processor._exchange_code_for_api_credentials.assert_called_once_with(
        "test_code", mock_settings_response
    )
    mock_settings_client.update_user_settings.assert_called_once()


# Test that hydrate_notion_settings throws an error if updating user settings fails
def test_hydrate_notion_settings_update_failure(
    notion_auth_processor, mock_settings_client
):
    # Mock the settings response
    mock_settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings_pb2.UserSettings(
            notion_user_settings=settings_pb2.NotionUserSettings()
        ),
        version="1",
    )
    mock_settings_client.get_user_settings.return_value = mock_settings_response

    # Mock the _exchange_code_for_api_credentials function
    mock_settings = settings_pb2.UserSettings(
        notion_user_settings=settings_pb2.NotionUserSettings(
            access_token="test_access_token",
            bot_id="test_bot_id",
            workspace_name="test_workspace_name",
            workspace_id="test_workspace_id",
        )
    )

    notion_auth_processor._exchange_code_for_api_credentials = Mock(
        return_value=mock_settings
    )

    mock_settings_client.update_user_settings = Mock(
        side_effect=Exception("Update failed")
    )

    with pytest.raises(NotionAuthInternalError):
        notion_auth_processor.hydrate_notion_settings(
            "test_code", Mock(), Mock(), Mock()
        )

    notion_auth_processor._exchange_code_for_api_credentials.assert_called_once()
    mock_settings_client.update_user_settings.assert_called_once()

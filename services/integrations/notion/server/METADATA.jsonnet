local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'notion',
      kubecfg: {
        target: '//services/integrations/notion/server:kubecfg',
        // Notion is deployed everywhere. Access to using Notion tool is determined by access to agents.
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['surbhi', 'aswin', 'sophie'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

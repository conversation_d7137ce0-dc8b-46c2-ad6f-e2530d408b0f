"""Authentication for the Notion server."""

import base64
import requests
import grpc
import logging
import json
from urllib.parse import urlencode
from pathlib import Path
from pydantic import SecretStr

from services.lib.request_context.request_context import RequestContext
from services.settings.client.client import SettingsClient
from services.integrations.notion import notion_pb2
from services.settings import settings_pb2

from services.integrations.notion.server.notion_client_wrapper import (
    search_pages,
    NotionUnauthorizedError,
)

NOTION_OAUTH_URL = "https://api.notion.com/v1/oauth/authorize"
NOTION_AUTH_URL = "https://api.notion.com/v1/oauth/token"
NOTION_AUTH_TIMEOUT = 60

logger = logging.getLogger(__name__)


class NotionAuthError(Exception):
    """Base class for errors in Notion Auth Processor."""

    pass


class NotionAuthUnconfiguredError(NotionAuthError):
    """Raised when Notion auth is not configured"""

    pass


class NotionValidationError(NotionAuthError):
    """Raised when there is an issue with the Notion authentication."""

    def __init__(self):
        super().__init__("Error: Notion Validation Failed")


class NotionAuthInternalError(NotionAuthError):
    """Raised when there is an internal augment error (ex: unable to get/set settings)"""

    def __init__(self):
        super().__init__("Internal Error")


class NotionAuthProcessor:
    """Authentication for the Notion server."""

    def __init__(
        self,
        settings_client: SettingsClient,
        redirect_uri: str,
        client_secret_path: str,
        client_id_path: str,
    ):
        self.settings_client = settings_client
        self.redirect_uri = redirect_uri
        self.oauth_client_id = SecretStr(Path(client_id_path).read_text().strip())
        self.oauth_client_secret = SecretStr(
            Path(client_secret_path).read_text().strip()
        )

    def get_oauth_url(self, request_context: RequestContext) -> str:
        """Construct the Notion OAuth URL. This is used to redirect the user to the Notion OAuth provider."""
        if not self.oauth_client_id:
            raise NotionAuthUnconfiguredError("Notion client ID not configured")
        params = {
            "client_id": self.oauth_client_id.get_secret_value(),
            "response_type": "code",
            "owner": "user",
            "redirect_uri": self.redirect_uri,
        }
        return f"{NOTION_OAUTH_URL}?{urlencode(params)}"

    def hydrate_notion_settings(
        self,
        code: str,
        request_context: RequestContext,
        request: notion_pb2.HydrateNotionSettingsRequest,
        context: grpc.ServicerContext,
    ) -> notion_pb2.HydrateNotionSettingsResponse:
        """Hydrate the Notion user settings with a code from the Notion OAuth flow and storing an access token in settings."""
        # Get current user settings
        try:
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )
        except Exception as e:
            logger.error(f"Error in accessing settings: {e}")
            raise NotionAuthInternalError()

        # Exchange code for permanent API token. Returns the updated settings with the new API token and other info from API call stored.
        settings = self._exchange_code_for_api_credentials(code, settings_response)

        # Update the user settings with new information
        try:
            self.settings_client.update_user_settings(
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings,
                    expected_version=settings_response.version,
                ),
                request_context=request_context,
            )
            return notion_pb2.HydrateNotionSettingsResponse(status_code=200)

        except Exception as e:
            logger.error(f"Error in updating settings: {e}")
            raise NotionAuthInternalError()

    def _exchange_code_for_api_credentials(
        self,
        code: str,
        settings_response: settings_pb2.GetUserSettingsResponse,
    ) -> settings_pb2.UserSettings:
        """Makes API request to Notion with the code to get the permanent API key.
        Returns the settings updated with the new information"""

        key_secret = "{}:{}".format(
            self.oauth_client_id.get_secret_value(),
            self.oauth_client_secret.get_secret_value(),
        ).encode("ascii")
        b64_encoded_key = base64.b64encode(key_secret).decode("ascii")

        auth_headers = {
            "Authorization": "Basic {}".format(b64_encoded_key),
            "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
        }

        auth_data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": self.redirect_uri,
        }

        auth_resp = requests.post(
            NOTION_AUTH_URL,
            headers=auth_headers,
            data=auth_data,
            timeout=NOTION_AUTH_TIMEOUT,
        ).json()

        if "error" in auth_resp:
            logger.error(f"Notion Authorization Failure: {auth_resp['error']}")
            raise NotionValidationError()
        else:
            owner = auth_resp["owner"]
            logger.info(
                "Notion App Installed",
                extra={
                    "notion_user": owner["user"]["name"],
                    "notion_user_id": owner["user"]["id"],
                },
            )

            # Store this access token and other information in settings
            settings = settings_response.settings
            settings.notion_user_settings.access_token = auth_resp["access_token"]
            settings.notion_user_settings.bot_id = auth_resp["bot_id"]
            settings.notion_user_settings.workspace_name = auth_resp["workspace_name"]
            settings.notion_user_settings.workspace_id = auth_resp["workspace_id"]

            return settings

    def revoke_token(self, request_context: RequestContext) -> None:
        """Remove the access token from settings.
        Notion at the moment does not have an API to revoke the token,
        so it will still be active, we just won't have access to it."""
        try:
            # Get current settings to get the token and version
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )
            settings = settings_response.settings

            # Clear the token from settings
            settings.notion_user_settings.CopyFrom(settings_pb2.NotionUserSettings())
            self.settings_client.update_user_settings(
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings,
                    expected_version=settings_response.version,
                ),
                request_context=request_context,
            )
        except Exception as e:
            logging.error(f"Failed to revoke Notion token: {e}")
            raise NotionAuthInternalError()


def notion_token_active(token: SecretStr):
    """Check if a Notion token is still active by performing a test API call.
    Since we are not notified when a user uninstalls our Notion integration,
    we need to do this check to see if the token is still active.
    Returns True if the token is active, False otherwise."""
    try:
        search_pages("", token, 1)
        return True
    except Exception as e:
        if isinstance(e, NotionUnauthorizedError):
            logger.info("Token stored in settings is no longer active")
            return False
        else:
            # If we get a different error than unauthorized, return True and allow to continue
            logger.error(f"Unexpected error in notion_token_active check: {e}")
            return True

import pytest
from services.integrations.notion.server.notion_client_wrapper import (
    extract_page_id,
    extract_properties,
    NotionError,
    NotionPage,
)

TEST_CASES = [
    # Direct IDs
    (
        "a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4",
        "a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4",
        "32-char clean ID",
    ),
    (
        "a1b2c3d4-e5f6-a1b2-c3d4-e5f6a1b2c3d4",
        "a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4",
        "UUID format",
    ),
    # URLs with titles
    (
        "https://www.notion.so/My-Page-f7c3a1b2d4e5f6a8b9c0d1e2f3a4b5c6",
        "f7c3a1b2d4e5f6a8b9c0d1e2f3a4b5c6",
        "Full URL with title",
    ),
    (
        "https://notion.so/workspace/My-Page-b4a9c2e7f1d8a3b6c5d4e7f8a9b2c5d6",
        "b4a9c2e7f1d8a3b6c5d4e7f8a9b2c5d6",
        "URL with workspace and title",
    ),
    (
        "www.notion.so/My-Page-d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0",
        "d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0",
        "www URL with title",
    ),
    # Direct page URLs
    (
        "https://notion.so/c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6",
        "c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6",
        "Direct URL without title",
    ),
    (
        "http://www.notion.so/e9f8d7c6b5a4e3f2d1c0b9a8f7e6d5c4",
        "e9f8d7c6b5a4e3f2d1c0b9a8f7e6d5c4",
        "HTTP URL",
    ),
    # URLs with query parameters
    (
        "https://www.notion.so/My-Page-a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2?pvs=4",
        "a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2",
        "URL with query params",
    ),
    # URL linked to a block ID
    (
        "https://www.notion.so/Monitoring-GCP-Spark-Jobs-14afd422e71880a3a970e2abe345b432?pvs=4#14afd422e718808682a7e504092e37ce",
        "14afd422e71880a3a970e2abe345b432",
        "URL linked to a block ID",
    ),
    # URL with #block ID
    (
        "https://www.notion.so/My-Page-a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2#14afd422e718808682a7e504092e37ce",
        "a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2",
        "URL with #block ID",
    ),
    # URL with #block ID and query parameters
    (
        "https://www.notion.so/My-Page-a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2?pvs=4#14afd422e718808682a7e504092e37ce",
        "a7b6c5d4e3f2a1b0c9d8e7f6a5b4c3d2",
        "URL with #block ID and query params",
    ),
]

INVALID_CASES = [
    ("", "Empty string"),
    ("not-a-notion-url", "Invalid format"),
    ("https://www.notion.so/", "URL without page ID"),
    ("https://www.notion.so/My-Page-123", "URL with invalid page ID length"),
    ("12345678901234567890123456789012XYZ", "ID with invalid characters"),
    ("123456789012", "Too short ID"),
]


@pytest.mark.parametrize("input_id,expected,description", TEST_CASES)
def test_extract_page_id_valid(input_id, expected, description):
    """Test valid page ID extraction cases."""
    result = extract_page_id(input_id)
    assert result == expected, f"Failed case: {description}"
    assert len(result) == 32, f"Result length not 32: {description}"
    assert all(
        c in "0123456789abcdef" for c in result.lower()
    ), f"Invalid hex chars in result: {description}"


@pytest.mark.parametrize("input_id,description", INVALID_CASES)
def test_extract_page_id_invalid(input_id, description):
    """Test invalid page ID cases."""
    with pytest.raises(NotionError, match=r".*"):
        extract_page_id(input_id)
        pytest.fail(f"Should have raised NotionError for case: {description}")


def test_extract_page_id_case_insensitive():
    """Test that the function handles uppercase URLs and IDs."""
    upper_url = "HTTPS://WWW.NOTION.SO/MY-PAGE-12345678901234567890123456789012"
    upper_id = "12345678901234567890123456789ABC"

    assert extract_page_id(upper_url) == "12345678901234567890123456789012"
    assert extract_page_id(upper_id) == "12345678901234567890123456789ABC"


def test_extract_page_id_trailing_slash():
    """Test that trailing slashes are handled correctly."""
    url_with_slash = "https://www.notion.so/My-Page-12345678901234567890123456789012/"
    assert extract_page_id(url_with_slash) == "12345678901234567890123456789012"


# Test extracting parameters
test_cases_parameters = [
    (  # One rich text item
        {
            "Related Documents": {
                "id": "id",
                "type": "rich_text",
                "rich_text": [{"plain_text": "doc1"}],
            }
        },
        {"Related Documents": "doc1"},
    ),
    (  # Two rich text items
        {
            "Related Documents": {
                "id": "id",
                "type": "rich_text",
                "rich_text": [{"plain_text": "doc1"}, {"plain_text": "doc2"}],
            }
        },
        {"Related Documents": "doc1, doc2"},
    ),
    (  # Empty rich text
        {"Related Documents": {"id": "id", "type": "rich_text", "rich_text": []}},
        {},
    ),
    (  # Status
        {"Status": {"id": "id", "type": "status", "status": {"name": "In Progress"}}},
        {"Status": "In Progress"},
    ),
    (  # Status is empty
        {"Status": {"id": "id", "type": "status", "status": None}},
        {},
    ),
    (  # Select
        {"Priority": {"id": "id", "type": "select", "select": {"name": "High"}}},
        {"Priority": "High"},
    ),
    (  # Select is empty
        {"Priority": {"id": "id", "type": "select", "select": None}},
        {},
    ),
    (  # Multi-select
        {
            "Tags": {
                "id": "id",
                "type": "multi_select",
                "multi_select": [{"name": "Tag1"}, {"name": "Tag2"}],
            }
        },
        {"Tags": "Tag1, Tag2"},
    ),
    (  # Multi-select empty
        {"Tags": {"id": "id", "type": "multi_select", "multi_select": []}},
        {},
    ),
]


@pytest.mark.parametrize("input_params,expected_params", test_cases_parameters)
def test_from_notion_page_parameters(input_params, expected_params):
    """Test that the function handles parameters correctly."""
    assert extract_properties(input_params) == expected_params


test_cases_parent = [
    (  # Page parent
        {"type": "page_id", "page_id": "parent_page_id"},
        "Page",
        "parent_page_id",
    ),
    (  # Database parent
        {"type": "database_id", "database_id": "parent_database_id"},
        "Database",
        "parent_database_id",
    ),
    (  # Block parent
        {"type": "block_id", "block_id": "parent_block_id"},
        "Block",
        "parent_block_id",
    ),
    (  # Workspace parent
        {"type": "workspace"},
        "workspace",
        None,
    ),
]


@pytest.mark.parametrize(
    "parent,expected_parent_type,expected_parent_id", test_cases_parent
)
def test_from_notion_page_parent(parent, expected_parent_type, expected_parent_id):
    """Test that the function handles parent correctly."""
    page = {
        "object": "page",
        "id": "id",
        "created_time": "2024-12-02T18:55:00.000Z",
        "last_edited_time": "2024-12-02T18:55:00.000Z",
        "url": "https://www.notion.so/page",
        "parent": parent,
        "properties": {},
    }
    blocks = []
    notion_page = NotionPage.from_notion_page(page, blocks)
    assert notion_page.parent_type == expected_parent_type
    assert notion_page.parent_id == expected_parent_id

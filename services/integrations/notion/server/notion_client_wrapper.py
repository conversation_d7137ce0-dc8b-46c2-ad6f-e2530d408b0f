"""Use Notion client for page operations and content management."""

import logging
import os
from dataclasses import dataclass
from typing import Optional, List, Dict, Any, cast
from datetime import datetime
from urllib.parse import urlparse, unquote
from pydantic import SecretStr

from notion_client import Client
from notion_client.helpers import collect_paginated_api
from notion_client import errors as notion_errors

log = logging.getLogger(__name__)


def richtext_to_plain(rich_text: List[Dict[str, Any]]) -> str:
    """Convert rich text array to plain text."""
    if not rich_text:
        return ""
    return "".join(text["plain_text"] for text in rich_text)


def extract_properties(properties: Dict[str, Any]) -> Dict[str, str]:
    """Extract relevant properties from Notion API response.
    There are 20+ properties with different formats. We currently pick out a few common/most useful ones.
    More can be added as needed
    """
    extracted_properties = {}
    for property_name, property_value in properties.items():
        property_type = property_value["type"]
        if property_type == "title":
            title = "".join(
                item["plain_text"] for item in property_value.get("title", [])
            )
            if len(title) == 0:
                title = "Untitled"
            extracted_properties["title"] = title
        elif property_type == "rich_text":
            vals = ", ".join(
                item["plain_text"] for item in property_value.get("rich_text", [])
            )
            if vals:
                extracted_properties[property_name] = vals
        elif property_type == "status":
            status_name = property_value.get("status", {})
            if status_name:  # status_name can be None in certain cases
                status_name = status_name.get("name", "")
                extracted_properties[property_name] = status_name
        elif property_type == "select":
            select_name = property_value.get("select", {})
            if (
                select_name
            ):  # select can be None if nothing is selected, so guard against that
                select_name = select_name.get("name", "")
                extracted_properties[property_name] = select_name
        elif property_type == "multi_select":
            selected_names = ", ".join(
                item["name"] for item in property_value.get("multi_select", [])
            )
            if selected_names:
                extracted_properties[property_name] = selected_names

    return extracted_properties


@dataclass
class NotionBlock:
    """A clean representation of a Notion content block as markdown."""

    id: str
    type: str
    content: str
    language: Optional[str] = None

    @classmethod
    def from_notion_block(cls, block: dict) -> "NotionBlock":
        """Convert a Notion API block to markdown NotionBlock."""
        block_type = block["type"]
        block_content = block[block_type]
        content = ""
        language = None

        try:

            def get_text(rich_text: List[Dict[str, Any]]) -> str:
                """Helper to extract plain text from rich text array. Adds support for mentions and links."""
                if not rich_text:
                    return ""

                result = []
                for text in rich_text:
                    if text["type"] == "mention" and text["mention"]["type"] == "page":
                        page_id = text["mention"]["page"]["id"]
                        href = text["href"]
                        title = text["plain_text"]
                        # Format as markdown link with page ID
                        result.append(f"[{title}]({href}) (Page ID: {page_id})")
                    elif text["type"] == "text" and text.get("text", {}).get("link"):
                        # Handle regular links
                        link_data = text.get("text", {}).get("link", {})
                        url = link_data.get("url", "")
                        if not url:
                            result.append(text.get("plain_text", ""))
                        else:
                            title = text.get("plain_text", "")
                            result.append(f"[{title}]({url})")
                    else:
                        result.append(text["plain_text"])
                return "".join(result)

            if block_type == "paragraph":
                if not block_content.get("rich_text"):
                    content = "<br/>"
                else:
                    content = get_text(block_content["rich_text"])

            elif block_type.startswith("heading_"):
                level = block_type[-1]
                content = f"{'#' * int(level)} {get_text(block_content['rich_text'])}"

            elif block_type == "code":
                language = block_content["language"]
                content = f"```{language}\n{get_text(block_content['rich_text'])}\n```"

            elif block_type == "bulleted_list_item":
                content = f"- {get_text(block_content['rich_text'])}"

            elif block_type == "numbered_list_item":
                content = f"1. {get_text(block_content['rich_text'])}"  # Markdown will handle numbering

            elif block_type == "to_do":
                content = f"- [{'x' if block_content['checked'] else ' '}] {get_text(block_content['rich_text'])}"

            elif block_type == "toggle":
                content = f"- {get_text(block_content['rich_text'])}"  # Convert toggle to bullet point

            elif block_type == "quote":
                content = f"> {get_text(block_content['rich_text'])}"

            elif block_type == "callout":
                icon = block_content.get("icon", {}).get("emoji", "")
                content = f"{icon} {get_text(block_content['rich_text'])}"

            elif block_type == "divider":
                content = "---"

            elif block_type == "equation":
                content = f"$$ {block_content['expression']} $$"

            elif block_type == "image":
                caption = get_text(block_content.get("caption", []))
                # Image URL is a waste as we don't use it for anything
                # If we wanted the agent to access the image URL and use it, we can add it back in
                if len(caption) > 0:
                    content += f"Image: {caption}"

            elif block_type == "bookmark" or block_type == "embed":
                url = block_content.get("url", "")
                caption = get_text(block_content.get("caption", []))
                content = f"[{url}]({url})"
                if caption:
                    content += f"\n\n{caption}"

            elif block_type == "file":
                url = block_content.get("file", {}).get("url", "")
                filename = os.path.basename(urlparse(url).path)
                content = f"[{filename}]({url})"

            elif block_type == "synced_block":
                content = "[//]: # (Synced Block)"

            elif block_type == "child_page":
                title = block_content.get("title", "")
                id = block.get("id", "")
                content = f"[{title}](Page ID: {id})"

            elif block_type == "table_row":
                cells = [get_text(cell) for cell in block_content.get("cells", [])]
                content = " | ".join(cells)
            elif block_type == "link_preview":
                content = f"Link Preview: {block_content.get('url', '')}"
            else:
                # Fallback for unknown types
                if "rich_text" in block_content:
                    content = get_text(block_content["rich_text"])
                log.warning("Unsupported block type: %s", block_type)

        except Exception as e:
            log.error("Failed to parse block of type %s: %s", block_type, e)
            content = f"[Error parsing {block_type} block]"

        return cls(id=block["id"], type=block_type, content=content, language=language)


@dataclass
class NotionPage:
    """A clean representation of a Notion page with its content."""

    id: str
    """The page's unique identifier."""

    title: str
    """The page title."""

    url: str
    """URL to the page."""

    created_time: datetime
    """When the page was created."""

    last_edited_time: datetime
    """When the page was last modified."""

    parent_type: str
    """The type of the parent, e.g., page, database, workspace."""

    parent_id: Optional[str]
    """ID of the parent page/database, if any."""

    blocks: List[NotionBlock]
    """The page's content blocks."""

    properties: Dict[str, str]
    """The page's additional properties. Mainly useful for database pages."""

    @classmethod
    def from_notion_page(cls, page: dict, blocks: List[dict]) -> "NotionPage":
        """Convert Notion API page and blocks to our cleaner NotionPage class."""
        extracted_properties = {}
        title = "Untitled"
        if "properties" not in page:
            log.warning("Page has no properties, using default title")
        else:
            properties = page["properties"]
            extracted_properties = extract_properties(properties)
            title = extracted_properties.get("title", "Untitled")
        parent_type = page.get("parent", {}).get("type")
        if parent_type == "page_id":
            parent_id = page.get("parent", {}).get("page_id")
            parent_type = "Page"
        elif parent_type == "database_id":
            parent_id = page.get("parent", {}).get("database_id")
            parent_type = "Database"
        elif parent_type == "block_id":
            parent_id = page.get("parent", {}).get("block_id")
            parent_type = "Block"
        else:  # parent is workspace -- page is at top level
            parent_id = None

        return cls(
            id=page["id"],
            title=title,
            url=page["url"],
            created_time=datetime.fromisoformat(page["created_time"]),
            last_edited_time=datetime.fromisoformat(page["last_edited_time"]),
            parent_type=parent_type,
            parent_id=parent_id,
            blocks=[NotionBlock.from_notion_block(b) for b in blocks],
            properties=extracted_properties,
        )


@dataclass
class NotionDatabase:
    """A clean representation of a notion database with its pages"""

    id: str
    """The database's unique identifier."""

    title: str
    """The database's title."""

    url: str
    """URL to the database."""

    created_time: datetime
    """When the page was created."""

    last_edited_time: datetime
    """When the page was last modified."""

    parent_id: str | None
    """ID of the parent page/database, if any."""

    pages: list[NotionPage]
    """The database's child pages."""

    properties: list[str]
    """The additional properties available to the database."""

    @classmethod
    def from_notion_database(
        cls, database: dict, pages: list[dict]
    ) -> "NotionDatabase":
        """Convert Notion API database and it's subpages to our cleaner NotionDatabase class.
        pages are the subpages or entries of the database.
        """
        title = " ".join(
            item.get("plain_text", "") for item in database.get("title", [])
        )
        props = [prop for prop in database.get("properties", [])]

        return cls(
            id=database.get("id", ""),
            title=title,
            url=database.get("url", ""),
            created_time=datetime.fromisoformat(database.get("created_time", "")),
            last_edited_time=datetime.fromisoformat(
                database.get("last_edited_time", "")
            ),
            parent_id=database.get("parent", {}).get("page_id"),
            pages=[NotionPage.from_notion_page(page, []) for page in pages],
            properties=props,
        )


class NotionError(Exception):
    """Raised when Notion operations fail."""

    pass


class NotionUnauthorizedError(NotionError):
    """Raised when Notion operations fail due to unauthorized access."""

    pass


class NotionDatabaseIdError(NotionError):
    """Raised when the Notion database ID is not well formed."""

    pass


class NotionPageIdError(NotionError):
    """Raised when Notion operations fail due to invalid or nonexistent page ID."""

    pass


def get_notion_client(credentials: SecretStr) -> Client:
    """Create authenticated Notion client."""
    try:
        return Client(auth=credentials.get_secret_value())
    except Exception as e:
        raise NotionError(f"Failed to create Notion client: {str(e)}") from e


def read_page(page_id: str, credentials: SecretStr) -> NotionPage:
    """Get a Notion page by ID including its content blocks.

    Args:
        page_id: The Notion page ID or URL
        credentials: Notion credentials

    Returns:
        NotionPage object with content

    Raises:
        NotionError: If the page cannot be retrieved
    """
    if not page_id:
        raise NotionPageIdError("Page ID is empty")
    page_id = extract_page_id(page_id)
    try:
        client = get_notion_client(credentials)

        # Get page metadata
        page_response = client.pages.retrieve(page_id=page_id)
        page = cast(Dict[str, Any], page_response)

        # Get all blocks using the helper function
        blocks_response = collect_paginated_api(
            client.blocks.children.list, block_id=page_id, page_size=100
        )
        blocks = cast(List[Dict[str, Any]], blocks_response)

        return NotionPage.from_notion_page(page, blocks)
    except notion_errors.APIResponseError as e:
        if e.code == notion_errors.APIErrorCode.Unauthorized:
            raise NotionUnauthorizedError(
                f"Failed to get page {page_id}: {str(e)}"
            ) from e
        log.info(f"Failed to read page: {e}")
        raise


def extract_page_id(input_str: str) -> str:
    """Extract page ID from a Notion URL or direct ID.

    Handles formats:
    - 32-char ID: a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4
    - UUID format: a1b2c3d4-e5f6-a1b2-c3d4-e5f6a1b2c3d4
    - Notion URLs:
      * https://www.notion.so/My-Page-a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4
      * https://notion.so/a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4
      * www.notion.so/page-title-a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4

    Returns:
        Clean 32-character page ID

    Raises:
        NotionError: If the input format is invalid or no valid ID is found
    """
    # First try direct ID format
    clean_id = input_str.replace("-", "")
    if len(clean_id) == 32:
        return clean_id

    try:
        # Ensure URL has a scheme for parsing
        url = input_str if "://" in input_str else f"https://{input_str}"
        parsed = urlparse(url)

        # Validate domain
        if not any(
            domain in parsed.netloc.lower() for domain in ["notion.so", "www.notion.so"]
        ):
            raise NotionPageIdError(f"Not a Notion URL: {input_str}")

        # Get the last path segment and extract ID
        path_segments = unquote(parsed.path).strip("/").split("/")
        if not path_segments[-1]:
            raise NotionPageIdError("URL has no page ID")

        # The ID is always the last 32 chars of the last path segment
        last_segment = path_segments[-1]
        potential_id = last_segment[-32:].replace("-", "")

        if len(potential_id) == 32:
            return potential_id

        raise NotionPageIdError(f"No valid page ID found in: {input_str}")

    except Exception as e:
        if isinstance(e, NotionError):
            raise
        raise NotionError(f"Invalid page ID or URL format: {input_str}") from e


def search_pages(
    query: str,
    credentials: SecretStr,
    max_results: int,
) -> List[NotionPage]:
    """Search Notion pages.

    Args:
        query: The search query
        credentials: Notion credentials
        max_results: Maximum number of results to return

    Returns:
        List of page objects from Notion API

    Raises:
        NotionError: If the search fails
    """
    try:
        client = get_notion_client(credentials)
        response = client.search(
            query=query,
            filter={"property": "object", "value": "page"},
            page_size=max_results,
        )
        results = cast(Dict[str, Any], response).get("results", [])

        return [
            NotionPage.from_notion_page(cast(Dict[str, Any], page), [])
            for page in results
        ]
    except notion_errors.APIResponseError as e:
        if e.code == notion_errors.APIErrorCode.Unauthorized:
            raise NotionUnauthorizedError(f"Failed to search pages: {str(e)}") from e
        log.info(f"Failed to search pages: {e}")
        raise


def read_database(database_id: str, credentials: SecretStr) -> NotionDatabase:
    """Get a Notion database by ID including its child pages.

    Args:
        database_id: The Notion database ID or URL
        credentials: Notion credentials

    Returns:
        NotionDatabase object with child pages

    Raises:
        NotionError: If the database cannot be retrieved
    """
    if not database_id:
        raise NotionDatabaseIdError("Database ID is empty")

    try:
        database_id = extract_page_id(database_id)
    except NotionError as e:
        log.error("Failed to extract database ID %s", e)
        raise NotionDatabaseIdError("Failed to extract database ID %s", e)

    try:
        client = get_notion_client(credentials)

        # Get database metadata
        database_response = client.databases.retrieve(database_id=database_id)
        database = cast(Dict[str, Any], database_response)

        # Get all child pages
        # Currently only getting first 100 not full pagination to limit size
        # can add pagination if necessary
        pages_response = client.databases.query(database_id=database_id)
    except notion_errors.APIResponseError as e:
        if e.code == notion_errors.APIErrorCode.Unauthorized:
            raise NotionUnauthorizedError(
                f"Failed to get database {database_id}: {str(e)}"
            ) from e
        log.info(f"Failed to read database: {e}")
        raise

    results = cast(Dict, pages_response).get("results", [])
    pages = cast(List[Dict[str, Any]], results)

    return NotionDatabase.from_notion_database(database, pages)

"""Implement notion server methods."""

import logging
from pydantic import SecretStr

from services.integrations.notion.server.notion_client_wrapper import (
    read_page,
    read_database,
    search_pages,
    NotionUnauthorizedError,
    NotionDatabaseIdError,
    NotionError,
    NotionPageIdError,
)
from services.integrations.notion.server.notion_formatters import (
    format_page_list_markdown,
    format_page_markdown,
    format_database_markdown,
)
from services.lib.request_context.request_context import RequestContext
from services.settings.client.client import SettingsClient
from services.integrations.notion.server.notion_auth_processor import (
    notion_token_active,
)

log = logging.getLogger(__name__)


class NotionHandler:
    """Provider for all supported Notion operations."""

    def __init__(self, settings_client: SettingsClient):
        self.settings_client = settings_client

    def get_user_credentials(self, request_context: RequestContext) -> SecretStr | None:
        """Get Notion access token from user settings.

        Args:
            request_context: Request context for tracking and metrics

        Returns:
            Notion integration secret as a SecretStr
        """
        try:
            settings = self.settings_client.get_user_settings(
                request_context,
            )

            if (
                not settings.settings.notion_user_settings
                or not settings.settings.notion_user_settings.access_token
            ):
                return None

            return SecretStr(settings.settings.notion_user_settings.access_token)
        except Exception as e:
            log.error("Failed to get Notion settings: %s", e)
            raise RuntimeError(
                "Failed to get access settings from settings service"
            ) from e

    def search_pages(
        self,
        query: str,
        request_context: RequestContext,
        credentials: str | None,
        max_results: int = 10,
    ) -> str:
        """Search for Notion pages using the search tool.

        Args:
            query: The search query
            request_context: Request context for tracking and metrics
            max_results: Maximum number of results to return
            credentials: Optional credentials to use instead of looking them up in the settings service
        Either settings must be configured in the settings service or credentials must be provided, otherwise we will return empty string.

        Returns:
            List of matching pages in markdown format
        """
        if credentials:
            notion_credentials = SecretStr(credentials)
        else:
            notion_credentials = self.get_user_credentials(request_context)
            if notion_credentials is None:
                log.warning("No Notion credentials found")
                raise NotionUnauthorizedError("No Notion credentials found")

        results = search_pages(
            query=query,
            credentials=notion_credentials,
            max_results=max_results,
        )

        return format_page_list_markdown(results)

    def read_page(
        self,
        page_id: str,
        request_context: RequestContext,
        credentials: str | None,
    ) -> str:
        """Get a Notion page using the page tool.

        Args:
            page_id: The Notion page ID or URL
            request_context: Request context for tracking and metrics
            credentials: Optional credentials to use instead of looking them up in the settings service
        Either settings must be configured in the settings service or credentials must be provided, otherwise we will return empty string.

        Returns:
            Markdown formatted page details
        """
        if credentials:
            notion_credentials = SecretStr(credentials)
        else:
            notion_credentials = self.get_user_credentials(request_context)
            if notion_credentials is None:
                log.warning("No Notion credentials found")
                raise NotionUnauthorizedError("No Notion credentials found")

        page = read_page(
            page_id,
            credentials=notion_credentials,
        )
        return format_page_markdown(page)

    def read_database(
        self,
        database_id: str,
        request_context: RequestContext,
        credentials: str | None,
    ) -> str:
        """Get a Notion database using the database tool.

        Args:
            database_id: The Notion database ID or URL
            request_context: Request context for tracking and metrics
            credentials: Optional credentials to use instead of looking them up in the settings service
        Either settings must be configured in the settings service or credentials must be provided, otherwise we will return empty string.

        Returns:
            Markdown formatted database details
        """
        if credentials:
            notion_credentials = SecretStr(credentials)
        else:
            notion_credentials = self.get_user_credentials(request_context)
            if notion_credentials is None:
                log.warning("No Notion credentials found")
                raise NotionUnauthorizedError("No Notion credentials found")

        database = read_database(
            database_id,
            credentials=notion_credentials,
        )
        return format_database_markdown(database)

    def is_configured(self, request_context: RequestContext) -> bool:
        """Check if Notion credentials are configured in user settings.

        Returns:
           True if credentials are configured, False otherwise

        Throws an exception if there is an internal error accessing settings.
        """
        try:
            credentials = self.get_user_credentials(request_context)
            if credentials is None:
                return False

            return True
        except Exception as e:
            log.error("Failed to get Notion settings: %s", e)
            raise

    def is_active_token(self, request_context: RequestContext) -> bool:
        """Check if the Notion credential is still active and working.

        Returns:
            True if the credential is active, False otherwise
        """
        try:
            credentials = self.get_user_credentials(request_context)
            if credentials is None:
                log.warning("No Notion credentials found")
                return False

            return notion_token_active(credentials)
        except Exception as e:
            log.error("Failed to get Notion settings: %s", e)
            return False

"""RPC server for Notion integration."""

import logging
import grpc
from google.rpc import status_pb2

from services.integrations.notion import notion_pb2, notion_pb2_grpc
from services.integrations.notion.server.config import Config
from services.integrations.notion.server.notion_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    NotionUnauthorizedError,
    NotionDatabaseIdError,
    NotionPageIdError,
)
from services.integrations.notion.server.notion_auth_processor import (
    NotionAuthProcessor,
    NotionAuthUnconfiguredError,
)
import services.lib.grpc.tls_config.tls_config as tls_config
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.settings.client.client import SettingsClient
from services.lib.request_context.request_context import RequestContext
from services.lib.grpc.auth.service_auth_interceptor import (
    get_auth_info_from_grpc_context,
)
from pydantic import SecretStr
from notion_client import errors as notion_errors

log = logging.getLogger(__name__)


class NotionServer(notion_pb2_grpc.NotionServicer):
    """RPC server to call Notion instance."""

    def __init__(
        self, config: Config, request_insight_publisher: RequestInsightPublisher
    ):
        super().__init__()
        self.config = config
        # TODO: add request insight builder
        # self.ri_builder = GleanRequestInsightBuilder(request_insight_publisher)

        self.settings_client = SettingsClient(
            config.settings_endpoint,
            tls_config.get_client_tls_creds(config.client_mtls),
        )

        self.notion_handler = NotionHandler(self.settings_client)

        self.auth_processor = NotionAuthProcessor(
            self.settings_client,
            config.notion_app_callback_url,
            config.notion_app_secrets_path,
            config.notion_app_client_id_path,
        )

    def IsConfigured(
        self, request: notion_pb2.NotionIsConfiguredRequest, context
    ) -> notion_pb2.NotionIsConfiguredResponse:
        """Check if Notion credentials are configured in user settings."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            is_configured = self.notion_handler.is_configured(
                request_context,
            )
            return notion_pb2.NotionIsConfiguredResponse(
                is_configured=is_configured,
            )

    def IsActiveToken(
        self, request: notion_pb2.NotionIsActiveTokenRequest, context
    ) -> notion_pb2.NotionIsActiveTokenResponse:
        """Check if the Notion credential is still active and working."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            return notion_pb2.NotionIsActiveTokenResponse(status_code=401)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Checking if Notion credential is active")
            is_active = self.notion_handler.is_active_token(
                request_context,
            )
            return notion_pb2.NotionIsActiveTokenResponse(
                is_active=is_active,
                status_code=200,
            )

    def Search(
        self, request: notion_pb2.NotionSearchRequest, context
    ) -> notion_pb2.NotionSearchResponse:
        """Search for Notion pages and return results as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            return notion_pb2.NotionSearchResponse(status_code=401)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Searching Notion")
            try:
                search_results = self.notion_handler.search_pages(
                    request.query,
                    request_context,
                    request.credentials or None,
                    request.max_results,
                )
                return notion_pb2.NotionSearchResponse(
                    page_list_markdown=search_results, status_code=200
                )
            except NotionUnauthorizedError as e:
                log.error(f"Failed to search Notion: Unauthorized: {e}")
                return notion_pb2.NotionSearchResponse(
                    page_list_markdown="Unauthorized", status_code=401
                )
            except notion_errors.APIResponseError as e:
                log.error(f"Error in searching Notion: {e}")
                return notion_pb2.NotionSearchResponse(
                    page_list_markdown=f"Error in searching Notion: {e.code}",  # code is a short string
                    status_code=e.status,
                )
            except Exception as e:
                log.error(f"Failed to search Notion: {e}")
                return notion_pb2.NotionSearchResponse(
                    page_list_markdown="Unknown Error", status_code=500
                )

    def ReadPage(
        self, request: notion_pb2.NotionReadPageRequest, context
    ) -> notion_pb2.NotionReadPageResponse:
        """Read a Notion page and return its content as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            return notion_pb2.NotionReadPageResponse(status_code=401)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Reading Notion page")
            try:
                page_content = self.notion_handler.read_page(
                    request.page_id,
                    request_context,
                    request.credentials or None,
                )
                return notion_pb2.NotionReadPageResponse(
                    page_markdown=page_content, status_code=200
                )
            except NotionUnauthorizedError as e:
                log.error(f"Failed to read Notion page: Unauthorized: {e}")
                return notion_pb2.NotionReadPageResponse(
                    page_markdown="Unauthorized", status_code=401
                )
            except NotionPageIdError as e:
                log.error(f"Failed to read Notion page: Invalid page ID: {e}")
                return notion_pb2.NotionReadPageResponse(
                    page_markdown="Invalid or unaccessible page ID", status_code=400
                )
            except notion_errors.APIResponseError as e:
                log.error(f"Error in reading Notion page: {e}")
                return notion_pb2.NotionReadPageResponse(
                    page_markdown=f"Error in reading Notion Page: {e.code}",  # code is a short string
                    status_code=e.status,
                )
            except Exception as e:
                log.error(f"Failed to read Notion page: {e}")
                return notion_pb2.NotionReadPageResponse(
                    page_markdown="Unknown Error", status_code=500
                )

    def ReadDatabase(
        self, request: notion_pb2.NotionReadDatabaseRequest, context
    ) -> notion_pb2.NotionReadDatabaseResponse:
        """Read a Notion database and return its content as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                database_content = self.notion_handler.read_database(
                    request.database_id,
                    request_context,
                    request.credentials or None,
                )
                return notion_pb2.NotionReadDatabaseResponse(
                    database_markdown=database_content,
                    status_code=200,
                )
            except NotionUnauthorizedError as e:
                log.error(f"Notion unauthorized error: {e}")
                return notion_pb2.NotionReadDatabaseResponse(
                    database_markdown="Unauthorized",
                    status_code=401,
                )
            except NotionDatabaseIdError as e:
                log.error(f"Error reading Database ID: {e}")
                return notion_pb2.NotionReadDatabaseResponse(
                    database_markdown="Database ID Malformed",
                    status_code=400,
                )
            except notion_errors.APIResponseError as e:
                log.error(f"Error in reading Notion database: {e}")
                return notion_pb2.NotionReadDatabaseResponse(
                    database_markdown=f"Error in reading Notion Database: {e.code}",  # code is a short string
                    status_code=e.status,
                )
            except Exception as e:
                log.error(f"Unexpected error in ReadDatabase: {e}", exc_info=True)
                return notion_pb2.NotionReadDatabaseResponse(
                    database_markdown="Unknown Error", status_code=500
                )

    def HydrateNotionSettings(
        self, request: notion_pb2.HydrateNotionSettingsRequest, context
    ) -> notion_pb2.HydrateNotionSettingsResponse:
        """Hydrate the Notion user settings with a code from the Notion OAuth flow and storing an access token in settings."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        tenant_id = auth_info.tenant_id
        if not tenant_id:
            return notion_pb2.HydrateNotionSettingsResponse(status_code=401)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Hydrating Notion settings")
            try:
                return self.auth_processor.hydrate_notion_settings(
                    code=request.code,
                    request_context=request_context,
                    request=request,
                    context=context,
                )
            except Exception as e:
                log.error(f"Unexpected error in HydrateNotionSettings: {e}")
                return notion_pb2.HydrateNotionSettingsResponse(status_code=500)

    def GetOAuthUrl(
        self, request: notion_pb2.NotionGetOAuthUrlRequest, context
    ) -> notion_pb2.NotionGetOAuthUrlResponse:
        """Get the Notion OAuth URL."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        tenant_id = auth_info.tenant_id
        if not tenant_id:
            return notion_pb2.NotionGetOAuthUrlResponse(status_code=401)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                oauth_url = self.auth_processor.get_oauth_url(request_context)
                return notion_pb2.NotionGetOAuthUrlResponse(
                    oauth_url=oauth_url,
                    status_code=200,
                )
            except NotionAuthUnconfiguredError as e:
                log.error(f"Failed to generate OAuth URL: {e}")
                return notion_pb2.NotionGetOAuthUrlResponse(status_code=500)
            except Exception as e:
                log.error(f"Unexpected error in GetOAuthUrl: {e}")
                return notion_pb2.NotionGetOAuthUrlResponse(status_code=500)

    def RevokeOAuthToken(
        self, request: notion_pb2.RevokeOAuthTokenRequest, context
    ) -> notion_pb2.RevokeOAuthTokenResponse:
        """Revoke the Notion OAuth token.

        Args:
            request: Empty request
            context: gRPC context

        Returns:
            Response containing the status. The status code will be one of:
            - OK: Success
            - UNAUTHENTICATED: Unauthorized (missing tenant_id)
            - INTERNAL: Internal server error
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Revoking Notion OAuth token")
            try:
                if not auth_info.tenant_id:
                    log.error("tenant_id not set in auth info")
                    return notion_pb2.RevokeOAuthTokenResponse(
                        status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                            code=grpc.StatusCode.UNAUTHENTICATED.value[0]
                        )
                    )
                self.auth_processor.revoke_token(request_context)
                return notion_pb2.RevokeOAuthTokenResponse(
                    status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                        code=grpc.StatusCode.OK.value[0]
                    )
                )
            except Exception as e:
                log.error(f"Failed to revoke Notion OAuth token: {e}")
                return notion_pb2.RevokeOAuthTokenResponse(
                    status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                        code=grpc.StatusCode.INTERNAL.value[0]
                    )
                )

load("@python_pip//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_library")

py_library(
    name = "notion_agent_tools",
    srcs = [
        "notion_tools.py",
    ],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/integrations/jupyter:__subpackages__",
        "//services/integrations/notion:__subpackages__",
    ],
    deps = [
        "//services/agents:agents_py_proto",
        "//services/agents:tool",
        "//services/integrations/notion/client:client_py",
        "//services/lib/request_context:request_context_py",
    ],
)

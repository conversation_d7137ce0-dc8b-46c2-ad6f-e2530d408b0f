"""Implement Notion tools.
Currently available tools:
- Get page: Get detailed information about a single page
- Update page: Update a page's title and content
"""

import logging

from dataclasses import dataclass
from typing import Literal, Optional, Union

import grpc
import textwrap
from dataclasses_json import dataclass_json
from google.rpc import status_pb2
from pydantic import (
    BaseModel,
    Field,
    SecretStr,
    ValidationError,
    field_validator,
)

from services.agents import agents_pb2
from services.agents.tool import (
    BaseExtraToolInput,
    EmptyExtraToolInput,
    ToolAuthenticationError,
    ValidatedTool,
)
from services.integrations.notion import notion_pb2
from services.integrations.notion.client.client import NotionClient
from services.lib.request_context.request_context import RequestContext

log = logging.getLogger(__name__)


# Tools that connect to the Notion service
@dataclass_json
@dataclass
class NotionCredentials:
    """Parsed credentials for Notion."""

    api_token: SecretStr
    """The API token for the Notion account."""


@dataclass
class NotionExtraToolInput(BaseExtraToolInput):
    """Wrapper for NotionCredentials that implements BaseExtraToolInput."""

    credentials: NotionCredentials
    """The underlying Notion credentials."""

    @classmethod
    def from_fields(cls, api_token: str) -> "NotionExtraToolInput":
        """Create a NotionExtraToolInput from NotionCredentials."""
        return cls(credentials=NotionCredentials(api_token=SecretStr(api_token)))

    def get_credentials(self) -> NotionCredentials:
        """Get the underlying NotionCredentials."""
        return self.credentials


class NotionPageInput(BaseModel):
    """Input for getting a Notion page."""

    page_id: str = Field(
        description="The Notion page ID or URL to retrieve.",
    )


class NotionPageTool(
    ValidatedTool[NotionPageInput, NotionExtraToolInput | EmptyExtraToolInput]
):
    """Tool for getting detailed information about a single Notion page."""

    id = agents_pb2.RemoteToolId.NOTION_PAGE
    name = "notion-page"
    description = """\
Read a single Notion page by its ID. You can specify the target page in two ways:

1. Page ID: The Notion page ID (e.g., "7c71008b-816d-4ed1-8cbb-c735653e0d82"). This ID can be found via the Notion Search Tool.
2. Page URL: The Notion page URL (e.g., "https://www.notion.so/page-title-7c71008b816d4ed18cbbc735653e0d82")

Returns the page contents in markdown format."""

    input_model = NotionPageInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, notion_client: NotionClient):
        """Initialize the tool with Notion configuration."""
        self.notion_client = notion_client

    def run_validated(
        self,
        validated_input: NotionPageInput,
        extra_tool_input: NotionExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Notion page tool.

        Args:
            validated_input: NotionPageInput containing the page ID or URL
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted page details
        """
        try:
            if isinstance(extra_tool_input, NotionExtraToolInput):
                credentials = (
                    extra_tool_input.get_credentials().api_token.get_secret_value()
                )
            else:
                credentials = None
            result = self.notion_client.read_page(
                validated_input.page_id,
                request_context=request_context,
                credentials=credentials,
            )
            if result.status_code == 401:
                raise ToolAuthenticationError("Failed to authenticate to Notion")
            elif result.status_code != 200:
                raise Exception(f"Failed to read Notion page: {result.status_code}")
            return result.page_markdown
        except Exception as e:
            log.error("Failed to get Notion page: %s", e)
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.notion_client.is_configured(request_context)
        except Exception as e:
            log.error("Failed to check Notion availability: %s", e)
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.notion_client.get_oauth_url(request_context)
        except Exception as e:
            log.error("Failed to get Notion OAuth URL: %s", e)
            return ""

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:
        try:
            response = self.notion_client.revoke_oauth_token(
                notion_pb2.RevokeOAuthTokenRequest(), request_context
            )
            return response.status
        except Exception as e:
            log.error("Failed to revoke Notion OAuth token: %s", e)
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Failed to revoke Notion OAuth token: {e}",
            )


class NotionSearchInput(BaseModel):
    """Input for searching Notion pages."""

    query: str = Field(
        description="The search query to find Notion pages.",
    )
    max_results: int = Field(
        default=10,
        description="Maximum number of results to return.",
        ge=1,
        le=100,
    )


class NotionSearchTool(
    ValidatedTool[NotionSearchInput, NotionExtraToolInput | EmptyExtraToolInput]
):
    """Tool for searching Notion pages."""

    id = agents_pb2.RemoteToolId.NOTION_SEARCH
    name = "notion-search"
    description = """\
Search for Notion pages using a text query.
Returns a list of matching pages with their titles, IDs, URLs, and metadata"""

    input_model = NotionSearchInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, notion_client: NotionClient):
        """Initialize the tool with Notion configuration."""
        self.notion_client = notion_client

    def run_validated(
        self,
        validated_input: NotionSearchInput,
        extra_tool_input: NotionExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Notion search tool.

        Args:
            validated_input: NotionSearchInput containing the search query
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted search results
        """
        try:
            if isinstance(extra_tool_input, NotionExtraToolInput):
                credentials = (
                    extra_tool_input.get_credentials().api_token.get_secret_value()
                )
            else:
                credentials = None
            result = self.notion_client.search_notion(
                validated_input.query,
                request_context=request_context,
                max_results=validated_input.max_results,
                credentials=credentials,
            )
            if result.status_code == 401:
                raise ToolAuthenticationError("Failed to authenticate to Notion")
            elif result.status_code != 200:
                raise Exception(f"Failed to search Notion pages: {result.status_code}")
            return result.page_list_markdown
        except Exception as e:
            log.error("Failed to search Notion pages: %s", e)
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        is_oauth_configured = self.notion_client.is_configured(request_context)
        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.notion_client.get_oauth_url(request_context)
        except Exception as e:
            log.error("Failed to get Notion OAuth URL: %s", e)
            return ""


class ReadPageInput(BaseModel):
    """Input for reading a Notion page."""

    page_id: str = Field(
        description="The Notion page ID or URL to retrieve.",
    )


class ReadDatabaseInput(BaseModel):
    """Input for reading a Notion database."""

    database_id: str = Field(
        description="The Notion database ID or URL to retrieve. Make sure the ID is for a database."
    )


class SearchPagesInput(BaseModel):
    """Input for searching Notion pages."""

    query: str = Field(
        description=textwrap.dedent("""The search query to find Notion pages.
        When creating a query, start with the shortest, most essential search terms.
        Only add terms if the intial result is too broad or irrelevant.
        It is often helpful to search for a phrase in quotation marks.""")
    )

    max_results: int = Field(
        default=10,
        description="Maximum number of results to return.",
        ge=1,
        le=100,
    )


class NotionInput(BaseModel):
    """Input for interacting with the Notion API."""

    summary: str = Field(
        description="A short human-readable summary of what this tool call will do. This helps users understand the purpose of the call."
    )

    href: str = Field(
        description=textwrap.dedent(
            """Only used when the method is read_page or read_database. The URL to the resource being accessed.
        ALWAYS provide a URL when the method is read_page or read_database.

        For both page_id and database_id:
        - If using an ID: Use format https://notion.com/{id} (remove any hyphens)
        - If using a URL: Use the full Notion URL as is

        ALWAYS verify the 32-digit ID in the URL matches the id parameter exactly.

        When the method is search_pages, pass an empty string"""
        )
    )

    method: Literal["read_page", "search_pages", "read_database"] = Field(
        description=textwrap.dedent("""The Notion API method to call (e.g., 'search_pages', 'read_page', 'read_database').
        The possible API methods:

        "search_pages"
        search_pages searches for Notion pages using a text query.
        It returns a list of matching pages with their titles, IDs, URLs, and metadata
        The required params are: query and max_results
        Query is the text query to search for.
        Max_results is the maximum number of results to return.

        "read_page"
        read_page reads a single Notion page by its ID.
        It returns the page contents in markdown format.
        The required param is: page_id.
        Page_id is the Notion page ID or URL to retrieve.
        You can specify page_id in two ways:
        1. Page ID: The Notion page ID (e.g., "7c71008b-816d-4ed1-8cbb-c735653e0d82"). This ID can be found via search_pages.
        2. Page URL: The Notion page URL (e.g., "https://www.notion.so/page-title-7c71008b816d4ed18cbbc735653e0d82")

        "read_database"
        read_database returns a Notion database and its child pages by its ID.
        It returns the database's title, ID, URL, and metadata, and it's child pages' title, ID, URL, and metadata.
        Read_database can be used if a page's parent is a database and you want to find similar pages.
        The required param is: database_id
        database_id is the Notion database ID or URL to retrieve.
        Database_id will be a Notion database ID (e.g., "41d534aaa57b43b396ce85bbf636767e").
        This ID can be as a parent ID of certain pages, the parent type will be Database in this case.
        Make sure that the ID corresponds to a database rather than a page.
        """)
    )

    params: Union[ReadPageInput, SearchPagesInput, ReadDatabaseInput] = Field(
        description="The parameters to pass to the API method. Refer to the descriptions of 'ReadPageInput', 'SearchPagesInput', 'ReadDatabaseInput",
    )

    # Validate that the params are the correct params for the method
    @field_validator("params")
    @classmethod
    def validate_params_for_method(cls, v, info):
        method = info.data.get("method")
        if method == "read_page" and not isinstance(v, ReadPageInput):
            raise ValidationError(
                "For 'read_page' method, params must be a ReadPageInput object"
            )
        elif method == "search_pages" and not isinstance(v, SearchPagesInput):
            raise ValidationError(
                "For 'search_pages' method, params must be a SearchPagesInput object"
            )
        elif method == "read_database" and not isinstance(v, ReadDatabaseInput):
            raise ValidationError(
                "For 'read_database' method, params must be a ReadDatabaseInput object"
            )
        return v


class NotionTool(
    ValidatedTool[NotionInput, NotionExtraToolInput | EmptyExtraToolInput]
):
    """Tool for interacting with Notion."""

    id = agents_pb2.RemoteToolId.NOTION
    name = "notion"
    description = textwrap.dedent("""\
    Use this tool to access content in Notion, a knowledge base for teams.
    Notion can contain documentation, project plants, technical specs, onboarding materials,
    policies, and collaborative work.
    Notion is organized with nested pages and databases.
    Check Notion when answering questions about:
    - Company processes, policies, or guidelines
    - Project specifications, plans, or requirements
    - Team structures, responsibilities, or contracts
    - Product features, designs, or architecture designs
    - Historical context or rationale for designs
    - Meeting notes, presentations, or shared resources
    - Lists of to-dos for teams or individuals

    For each method, you must pass in the required parameters.

    When given an Notion ID, try and discern whether it is a Page or Database ID from the information around the ID.
    If you are unable to discern, assume it is a Page ID. If you assume it is a Page ID and fail, try treating it
    as a Database ID as well. """)

    input_model = NotionInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, notion_client: NotionClient):
        """Initialize the tool with Notion configuration."""
        self.notion_client = notion_client

    def run_validated(
        self,
        validated_input: NotionInput,
        extra_tool_input: NotionExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Notion API tool.

        Args:
            validated_input: NotionAPIInput containing the API method and params
            extra_tool_input: Credentials for Notion
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted response
        """
        try:
            if isinstance(extra_tool_input, NotionExtraToolInput):
                credentials = (
                    extra_tool_input.get_credentials().api_token.get_secret_value()
                )
            else:
                credentials = None

            if validated_input.method == "read_page":
                read_params = validated_input.params
                # This should always be ReadPageInput due to our validation, but we check for type safety
                if isinstance(read_params, ReadPageInput):
                    result = self.notion_client.read_page(
                        read_params.page_id, request_context, credentials
                    )
                    if result.status_code == 401:
                        raise ToolAuthenticationError(
                            "Failed to authenticate to Notion"
                        )
                    elif result.status_code != 200:
                        raise Exception(
                            f"Failed to read Notion page: {result.status_code}"
                        )
                    return result.page_markdown
                else:
                    raise ValidationError("Expected ReadPageInput for read_page method")
            elif validated_input.method == "search_pages":
                search_params = validated_input.params
                # This should always be SearchPagesInput due to our validation, but we check for type safety
                if isinstance(search_params, SearchPagesInput):
                    result = self.notion_client.search_notion(
                        search_params.query,
                        request_context,
                        search_params.max_results,
                        credentials,
                    )
                    if result.status_code == 401:
                        raise ToolAuthenticationError(
                            "Failed to authenticate to Notion"
                        )
                    elif result.status_code != 200:
                        raise Exception(
                            f"Failed to search Notion pages: {result.status_code}"
                        )
                    return result.page_list_markdown
                else:
                    raise ValidationError(
                        "Expected SearchPagesInput for search_pages method"
                    )
            elif validated_input.method == "read_database":
                search_params = validated_input.params
                # This should always be ReadDatabaseInput due to our validation, but we check for type safety
                if isinstance(search_params, ReadDatabaseInput):
                    response = self.notion_client.read_database(
                        search_params.database_id,
                        request_context,
                        credentials,
                    )
                    if response.status_code == 401:
                        raise ToolAuthenticationError(
                            "Failed to authenticate to Notion"
                        )
                    elif response.status_code != 200:
                        raise Exception(
                            f"Failed to read Notion database: {response.status_code}"
                        )
                    return response.database_markdown
                else:
                    raise ValidationError(
                        "Expected ReadDatabaseInput for read_database method"
                    )
            else:
                # This should never happen due to Literal type constraint
                raise ValidationError(f"Unsupported method: {validated_input.method}")

        except Exception as e:
            log.error("Failed to call Notion API: %s", e)
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.notion_client.is_configured(request_context)
        except Exception as e:
            log.error(f"Failed to check Notion availability: {str(e)}")
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS
        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.notion_client.get_oauth_url(request_context)
        except Exception as e:
            log.error("Failed to get Notion OAuth URL: %s", e)
            return ""

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:
        try:
            response = self.notion_client.revoke_oauth_token(
                notion_pb2.RevokeOAuthTokenRequest(), request_context
            )
            return response.status
        except Exception as e:
            log.error("Failed to revoke Notion OAuth token: %s", e)
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Failed to revoke Notion OAuth token: {e}",
            )

syntax = "proto3";

package notion;

import "google/rpc/status.proto";

service Notion {
  // Search for Notion pages (title, ID, URL, etc.) based on a query
  rpc Search(NotionSearchRequest) returns (NotionSearchResponse);

  // Get the content of a Notion page (title, content, etc.) based on a page ID
  rpc ReadPage(NotionReadPageRequest) returns (NotionReadPageResponse);

  // Get the content of a Notion database (title and subpages) based on a database ID
  rpc ReadDatabase(NotionReadDatabaseRequest) returns (NotionReadDatabaseResponse);

  // Check if Notion credentials are configured in user settings
  rpc IsConfigured(NotionIsConfiguredRequest) returns (NotionIsConfiguredResponse);

  // Check if the Notion credential is still active and working
  rpc IsActiveToken(NotionIsActiveTokenRequest) returns (NotionIsActiveTokenResponse);

  // Hydrate the Notion user settings with a code from the Notion OAuth flow and storing an access token in settings
  rpc HydrateNotionSettings(HydrateNotionSettingsRequest) returns (HydrateNotionSettingsResponse);

  // Get the Notion OAuth URL
  rpc GetOAuthUrl(NotionGetOAuthUrlRequest) returns (NotionGetOAuthUrlResponse);

  // Revoke the Notion OAuth token
  rpc RevokeOAuthToken(RevokeOAuthTokenRequest) returns (RevokeOAuthTokenResponse);
}

message NotionSearchRequest {
  string query = 1 [debug_redact = true];
  int32 max_results = 2;

  // optionally passed when credentials are passed in through client
  // instead of looking them up in the settings service
  optional string credentials = 3 [debug_redact = true];
}

message NotionSearchResponse {
  // Returns a markdown-formatted list of Notion pages, where each page is formatted as:
  // - [Page Title](page_url)
  //    Id: page_id | Created: page_created_time | Last Edited: page_last_edited_time
  string page_list_markdown = 1 [debug_redact = true];
  int32 status_code = 2; //HTTP Status Code
}

message NotionReadPageRequest {
  string page_id = 1 [debug_redact = true];

  // optionally passed when credentials are passed in through client
  // instead of looking them up in the settings service
  optional string credentials = 2 [debug_redact = true];
}

message NotionReadPageResponse {
  // Returns a markdown-formatted page, where the page is formatted as:
  // # Page Title (Page ID: page_id )
  // URL: page_url
  // Created Time: page_created_time
  // Last Edited Time: page_last_edited_time
  // Parent Type: page_parent_type | Parent ID: page_parent_id
  // Properties: page_properties
  // ## Content
  // Page content
  string page_markdown = 1 [debug_redact = true];
  int32 status_code = 2; //HTTP Status Code
}

message NotionReadDatabaseRequest {
  string database_id = 1 [debug_redact = true];

  // optionally passed when credentials are passed in through client
  // instead of looking them up in the settings service
  optional string credentials = 2 [debug_redact = true];
}

message NotionReadDatabaseResponse {
  // Returns a markdown-formatted database, where the database is formatted as:
  // # Database Title (Database ID: database_id )
  // URL: database_url
  // Created Time: database_created_time
  // Last Edited Time: database_last_edited_time
  // Parent ID: database_parent_id
  // ## Pages
  // Database pages, in the format of NotionSearchResponse pages
  string database_markdown = 1 [debug_redact = true];

  int32 status_code = 2; //HTTP Status Code
}

message NotionIsConfiguredRequest {}

message NotionIsConfiguredResponse {
  bool is_configured = 1;
}

message NotionIsActiveTokenRequest {}

message NotionIsActiveTokenResponse {
  bool is_active = 1;
  int32 status_code = 2; //HTTP Status Code
}

message HydrateNotionSettingsRequest {
  string code = 1 [debug_redact = true];
}

message HydrateNotionSettingsResponse {
  int32 status_code = 1; //HTTP Status Code
}

message NotionGetOAuthUrlRequest {}

message NotionGetOAuthUrlResponse {
  string oauth_url = 1 [debug_redact = true];
  int32 status_code = 2; //HTTP Status Code
}

message RevokeOAuthTokenRequest {}

message RevokeOAuthTokenResponse {
  google.rpc.Status status = 1;
}

"""Tests for the web search tool."""

import pytest
from unittest import mock
from unittest.mock import MagicMock, patch
import requests
from pydantic import ValidationError

from services.integrations.google_search.agent_tools.web_search import (
    WebSearchConfig,
    WebSearchError,
    Credentials,
)
from services.integrations.google_search.agent_tools.web_search_tool import (
    WebSearchTool,
)
from services.agents.tool import EmptyExtraToolInput
from services.lib.request_context.request_context import RequestContext
import grpc


@pytest.fixture
def mock_response():
    """Create a mock response object."""
    mock_resp = MagicMock()
    mock_resp.status_code = 200
    mock_resp.raise_for_status.return_value = None
    mock_resp.json.return_value = {
        "items": [
            {
                "title": "Test Result",
                "link": "https://example.com",
                "snippet": "This is a test result",
            }
        ]
    }
    return mock_resp


@pytest.fixture
def mock_requests(mock_response):
    """Mock requests.get globally."""
    with patch("requests.get", return_value=mock_response) as mock_get:
        yield mock_get


@pytest.fixture
def request_context():
    """Return a basic request context for testing."""
    return RequestContext.create_for_session("test-session-id")


@pytest.fixture
def mock_credentials():
    """Returns mock credentials object."""
    mock_secret = MagicMock()
    mock_secret.get_secret_value.return_value = "test_key"

    creds = Credentials(api_keys=[mock_secret], cx="test_engine")

    # Mock the get_next_api_key method to always return the same key for testing
    creds.get_next_api_key = MagicMock(return_value=mock_secret)

    return creds


@pytest.fixture
def web_search_tool(mock_credentials, mock_requests):
    """Creates a WebSearchTool instance with mocked credentials."""
    with patch(
        "services.integrations.google_search.agent_tools.web_search_tool.get_search_credentials"
    ) as mock_get_creds:
        mock_get_creds.return_value = mock_credentials
        config = WebSearchConfig(api_key_path="/path/to/api_key", cx="test_engine")
        return WebSearchTool(config)


def test_web_search_success(web_search_tool, request_context):
    """Test successful web search."""
    result = web_search_tool.run(
        {"query": "test", "num_results": 1}, EmptyExtraToolInput(), request_context
    )

    assert "Test Result" in result
    assert "https://example.com" in result


def test_web_search_error(web_search_tool, request_context):
    """Test web search error handling."""
    with patch(
        "services.integrations.google_search.agent_tools.web_search_tool.web_search"
    ) as mock_search:
        mock_search.side_effect = WebSearchError("Search failed")

        with pytest.raises(WebSearchError, match="Search failed"):
            web_search_tool.run(
                {"query": "test query"}, EmptyExtraToolInput(), request_context
            )


def test_input_validation(web_search_tool, request_context):
    """Test input validation for the WebSearchTool.

    This test verifies that the WebSearchTool properly validates its input parameters
    according to the Pydantic model WebSearchInput:
    - query (str): Required field
    - num_results (int): Optional, must be between 1 and 10, defaults to 5

    The test expects Pydantic validation errors to be raised for invalid inputs.

    Args:
        web_search_tool: Fixture providing a WebSearchTool instance with mocked credentials
        request_context: Fixture providing a RequestContext instance
    """
    # Test valid input with explicit num_results
    result = web_search_tool.run(
        {"query": "test", "num_results": 5}, EmptyExtraToolInput(), request_context
    )
    assert result is not None, "Valid input should return search results"

    # Test valid input with default num_results
    result = web_search_tool.run(
        {"query": "test"}, EmptyExtraToolInput(), request_context
    )
    assert result is not None, "Missing num_results should use default value of 5"

    # Test num_results exceeding maximum
    with pytest.raises(ValidationError) as exc_info:
        web_search_tool.run(
            {"query": "test", "num_results": 11}, EmptyExtraToolInput(), request_context
        )
    assert "num_results" in str(exc_info.value)
    assert "Input should be less than or equal to 10" in str(exc_info.value)

    # Test num_results below minimum
    with pytest.raises(ValidationError) as exc_info:
        web_search_tool.run(
            {"query": "test", "num_results": 0}, EmptyExtraToolInput(), request_context
        )
    assert "num_results" in str(exc_info.value)
    assert "Input should be greater than or equal to 1" in str(exc_info.value)

    # Test missing required query field
    with pytest.raises(ValidationError) as exc_info:
        web_search_tool.run({"num_results": 5}, EmptyExtraToolInput(), request_context)
    assert "query" in str(exc_info.value)
    assert "Field required" in str(exc_info.value)


def test_revoke_tool_access(web_search_tool: WebSearchTool, request_context):
    """Test that revoke_tool_access returns NOT_IMPLEMENTED for web search tool."""

    status = web_search_tool.revoke_tool_access(request_context)
    assert status.code == grpc.StatusCode.OK.value[0]


def test_test_tool_connection_success(web_search_tool: WebSearchTool, request_context):
    """Test that test_tool_connection returns OK when connection is successful."""

    # Mock the requests methods to simulate successful responses
    with patch("requests.head") as mock_head, patch("requests.get") as mock_get:
        # Configure the mock responses
        mock_head_response = MagicMock()
        mock_head_response.status_code = 200
        mock_head.return_value = mock_head_response

        mock_get_response = MagicMock()
        mock_get_response.status_code = 200
        mock_get_response.raise_for_status.return_value = None
        mock_get.return_value = mock_get_response

        status = web_search_tool.test_tool_connection(request_context)
        assert status.code == grpc.StatusCode.OK.value[0]


def test_test_tool_connection_auth_error(
    web_search_tool: WebSearchTool, request_context
):
    """Test that test_tool_connection returns UNAUTHENTICATED when authentication fails."""

    # Mock the requests methods to simulate authentication failure
    with patch("requests.head") as mock_head, patch("requests.get") as mock_get:
        # Service is available but authentication fails
        mock_head_response = MagicMock()
        mock_head_response.status_code = 200
        mock_head.return_value = mock_head_response

        # Authentication failure (401 Unauthorized)
        mock_get_response = MagicMock()
        mock_get_response.status_code = 401
        mock_get.return_value = mock_get_response

        status = web_search_tool.test_tool_connection(request_context)
        assert status.code == grpc.StatusCode.UNAUTHENTICATED.value[0]


def test_test_tool_connection_service_unavailable(
    web_search_tool: WebSearchTool, request_context
):
    """Test that test_tool_connection returns UNAVAILABLE when service is down or having issues."""

    # Mock the requests methods to simulate service unavailability
    with patch("requests.head") as mock_head:
        # Service returns 5xx error
        mock_head_response = MagicMock()
        mock_head_response.status_code = 503
        mock_head.return_value = mock_head_response

        status = web_search_tool.test_tool_connection(request_context)
        assert status.code == grpc.StatusCode.UNAVAILABLE.value[0]


def test_test_tool_connection_exception(
    web_search_tool: WebSearchTool, request_context
):
    """Test that test_tool_connection handles exceptions gracefully."""

    # Mock the requests.head method to raise an exception
    with patch(
        "requests.head",
        side_effect=requests.exceptions.RequestException("Connection refused"),
    ):
        status = web_search_tool.test_tool_connection(request_context)
        assert status.code == grpc.StatusCode.INTERNAL.value[0]

"""Tests for web_search.py."""

import pytest
import requests
from unittest.mock import MagicMock, patch

from services.integrations.google_search.agent_tools.web_search import (
    check_service_availability,
    WebSearchError,
    SearchResult,
    format_results_markdown,
)


def test_format_results_markdown():
    """Test format_results_markdown function."""
    # Test with empty results
    assert format_results_markdown([]) == "No results found."

    # Test with results
    results = [
        SearchResult(
            title="Test Title 1",
            url="https://example.com/1",
            snippet="Test snippet 1",
        ),
        SearchResult(
            title="Test Title 2",
            url="https://example.com/2",
            snippet="Test snippet 2",
        ),
    ]
    formatted = format_results_markdown(results)
    assert "- [Test Title 1](https://example.com/1)" in formatted
    assert "Test snippet 1" in formatted
    assert "- [Test Title 2](https://example.com/2)" in formatted
    assert "Test snippet 2" in formatted


def test_check_service_availability_success():
    """Test check_service_availability with successful response."""
    # Mock a 403 response with API key message
    mock_response = MagicMock()
    mock_response.status_code = 403
    mock_response.json.return_value = {
        "error": {
            "message": "Method doesn't allow unregistered callers. Please use API Key."
        }
    }

    with patch("requests.get", return_value=mock_response):
        is_available, status = check_service_availability()
        assert is_available is True
        assert status == "CONNECTION_OK"


def test_check_service_availability_unexpected_response():
    """Test check_service_availability with unexpected response."""
    # Mock a 200 response (unexpected but still indicates service is up)
    mock_response = MagicMock()
    mock_response.status_code = 200

    with patch("requests.get", return_value=mock_response):
        is_available, status = check_service_availability()
        assert is_available is True
        assert status == "CONNECTION_OK"


def test_check_service_availability_server_error():
    """Test check_service_availability with server error."""
    # Mock a 500 response
    mock_response = MagicMock()
    mock_response.status_code = 500

    with patch("requests.get", return_value=mock_response):
        is_available, status = check_service_availability()
        assert is_available is False
        assert status == "SERVICE_UNAVAILABLE"


def test_check_service_availability_connection_error():
    """Test check_service_availability with connection error."""
    with patch(
        "requests.get",
        side_effect=requests.exceptions.ConnectionError("Connection refused"),
    ):
        is_available, status = check_service_availability()
        assert is_available is False
        assert status == "SERVICE_UNAVAILABLE"


def test_check_service_availability_timeout():
    """Test check_service_availability with timeout."""
    with patch(
        "requests.get", side_effect=requests.exceptions.Timeout("Request timed out")
    ):
        is_available, status = check_service_availability()
        assert is_available is False
        assert status == "SERVICE_UNAVAILABLE"


def test_check_service_availability_other_error():
    """Test check_service_availability with other error."""
    with patch("requests.get", side_effect=Exception("Unexpected error")):
        is_available, status = check_service_availability()
        assert is_available is False
        assert status == "CONNECTION_ERROR"

load("@rules_python//python:defs.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "google_search_agent_tools",
    srcs = [
        "web_search.py",
        "web_search_tool.py",
    ],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/integrations/google_search:__subpackages__",
        "//services/integrations/jupyter:__subpackages__",
    ],
    deps = [
        "//services/agents:agents_py_proto",
        "//services/agents:tool",
        requirement("requests"),
    ],
)

pytest_test(
    name = "web_search_tool_test",
    srcs = ["web_search_tool_test.py"],
    deps = [
        ":google_search_agent_tools",
    ],
)

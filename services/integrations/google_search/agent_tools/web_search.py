"""Web search functions using Google Custom Search API."""

import json
import requests
import pathlib
import logging
from enum import Enum, auto
from dataclasses import dataclass

from pydantic import SecretStr
from dataclasses_json import dataclass_json

log = logging.getLogger(__name__)

CUSTOMSEARCH_API_URL = "https://www.googleapis.com/customsearch/v1"


@dataclass_json
@dataclass
class WebSearchConfig:
    """Configuration for the WebSearchTool."""

    # These are set up as described in https://developers.google.com/custom-search/v1/overview
    # 1. Create a custom search engine
    # 2. Create an API key
    # 3. Get the cx from the custom search engine and put in config
    # 4. Upload the API key to secret manager
    api_key_path: str
    cx: str


@dataclass_json
@dataclass
class Credentials:
    """Parsed credentials for web search."""

    api_keys: list[SecretStr]
    cx: str
    _current_index: int = 0

    def get_next_api_key(self) -> SecretStr:
        """Get the next API key in round-robin fashion."""
        if not self.api_keys:
            raise ValueError("No API keys available")

        key = self.api_keys[self._current_index]
        self._current_index = (self._current_index + 1) % len(self.api_keys)
        return key


def get_search_credentials(config: WebSearchConfig) -> Credentials:
    """Get Google Custom Search API credentials.

    Reads the API keys from the secret file, which should contain a JSON array
    of API keys or a single API key as plain text.
    """
    try:
        secret_content = pathlib.Path(config.api_key_path).read_text().strip()

        # Try to parse as JSON
        try:
            secret_data = json.loads(secret_content)

            # Handle array of keys directly
            if isinstance(secret_data, list):
                api_keys = [SecretStr(k) for k in secret_data if isinstance(k, str)]
            # Handle {"keys": [...]} format
            elif (
                isinstance(secret_data, dict)
                and "keys" in secret_data
                and isinstance(secret_data["keys"], list)
            ):
                api_keys = []
                for k in secret_data["keys"]:
                    if isinstance(k, str):
                        api_keys.append(SecretStr(k))
                    elif (
                        isinstance(k, dict) and "key" in k and isinstance(k["key"], str)
                    ):
                        api_keys.append(SecretStr(k["key"]))
            else:
                # Treat as single key
                api_keys = [SecretStr(secret_content)]

        except json.JSONDecodeError:
            # Not JSON, treat as single key
            api_keys = [SecretStr(secret_content)]

        if not api_keys:
            raise ValueError("No valid API keys found in secret file")

        return Credentials(api_keys=api_keys, cx=config.cx)

    except Exception as e:
        log.error(f"Error reading API keys: {e}")
        # Fallback to empty key - this will cause an error when used
        # but it's better than crashing here
        return Credentials(api_keys=[SecretStr("")], cx=config.cx)


@dataclass
class SearchResult:
    """A search result from a web search."""

    title: str
    url: str
    snippet: str


class WebSearchError(Exception):
    """Raised when web search fails."""

    pass


def web_search(
    query: str,
    credentials: Credentials,
    num_results: int = 5,
) -> list[SearchResult]:
    """Search the web using Google Custom Search API.

    Args:
        query: The search query
        credentials: The credentials to use
        num_results: Number of results to return (max 10)

    Returns:
        List of SearchResult objects

    Raises:
        WebSearchError: If the search fails
    """
    # Get API credentials
    if credentials is None:
        raise ValueError("Missing required parameter 'credentials'")

    # Get the next API key in round-robin fashion
    api_key = credentials.get_next_api_key()
    cx = credentials.cx

    # Limit num_results to 10 (API maximum)
    num_results = min(num_results, 10)

    # Make API request
    params = {
        "key": api_key.get_secret_value(),
        "cx": cx,
        "q": query,
        "num": num_results,
    }

    try:
        response = requests.get(CUSTOMSEARCH_API_URL, params=params)
        response.raise_for_status()
        data = response.json()

        # Extract search results
        results = []
        for item in data.get("items", []):
            result = SearchResult(
                title=item.get("title", ""),
                url=item.get("link", ""),
                snippet=item.get("snippet", ""),
            )
            results.append(result)

        return results

    except requests.RequestException as e:
        # Only log the error code, not the full message which might contain sensitive URLs
        if (
            hasattr(e, "response")
            and e.response is not None
            and hasattr(e.response, "status_code")
        ):
            error_code = e.response.status_code
            raise WebSearchError(f"Search request failed: HTTP error code {error_code}")
        else:
            # For connection errors that don't have status codes
            raise WebSearchError("Search request failed: No status code from server")
    except (KeyError, ValueError) as e:
        raise WebSearchError(f"Failed to parse search results: {str(e)}")


def format_results_markdown(results: list[SearchResult]) -> str:
    """Format search results as markdown text.

    Args:
        results: List of SearchResult objects

    Returns:
        Markdown formatted string with search results
    """
    if not results:
        return "No results found."

    formatted_results = []
    for result in results:
        # Format each result as a markdown bullet point with title as link
        formatted_result = f"- [{result.title}]({result.url})\n  {result.snippet}"
        formatted_results.append(formatted_result)

    return "\n\n".join(formatted_results)


class ConnectionStatus(Enum):
    """Enum representing the connection status of the Google Custom Search API."""

    OK = auto()
    CONNECTION_ERROR = auto()
    SERVICE_UNAVAILABLE = auto()
    AUTHENTICATION_FAILED = auto()


def test_connection(credentials: Credentials) -> ConnectionStatus:
    """Test if the Google Custom Search API is available and credentials are valid.

    This function performs two separate checks:
    1. First, it checks if the Google Search API service is available by making a HEAD request
       to the API endpoint without authentication
    2. Then, if the service is available, it checks if our credentials are valid by making
       a minimal search query

    The function distinguishes between different types of errors:
    - CONNECTION_ERROR: Network connectivity issues or client side errors (DNS problems, network down, etc.)
    - SERVICE_UNAVAILABLE: Google's service is reachable but having problems (5xx errors, timeouts after connection)
    - AUTHENTICATION_FAILED: Credentials are invalid or expired (401/403 errors)

    Args:
        credentials: The credentials to use for the connection check

    Returns:
        ConnectionStatus: Enum indicating the status of the connection:
        - ConnectionStatus.OK: Connection is successful and credentials are valid
        - ConnectionStatus.AUTHENTICATION_FAILED: Authentication failed (invalid API key)
        - ConnectionStatus.CONNECTION_ERROR: Network connectivity issues or client side errors.
        - ConnectionStatus.SERVICE_UNAVAILABLE: Google's service is having problems
    """
    if credentials is None:
        return ConnectionStatus.SERVICE_UNAVAILABLE

    # Get the next API key in round-robin fashion
    api_key = credentials.get_next_api_key()
    cx = credentials.cx

    # Step 1: Check if the service is available (without authentication)
    try:
        # Make a HEAD request to check if the service is up
        # This doesn't count against our quota and doesn't require authentication
        service_check = requests.head(CUSTOMSEARCH_API_URL, timeout=3.0)

        # If we get a 5xx response, the service might be having issues
        if service_check.status_code >= 500:
            log.error(
                f"Google Search API service check returned server error: {service_check.status_code}"
            )
            return ConnectionStatus.SERVICE_UNAVAILABLE

    except requests.exceptions.RequestException as e:
        # Other request exceptions during basic connectivity check
        # If we cannot even reach Google, this is likely a network connectivity issue
        # It is possible that the google is totally unavailable, but this is unlikely
        log.error(f"Error checking Google Search API availability: {str(e)}")
        return ConnectionStatus.CONNECTION_ERROR

    # Step 2: Check if our credentials are valid
    try:
        # Use a simple, minimal query that should always return results
        test_query = "test"

        # Make API request with minimal parameters
        params = {
            "key": api_key.get_secret_value(),
            "cx": cx,
            "q": test_query,
            "num": 1,  # Request only 1 result to minimize data transfer
        }

        log.debug("Checking Google Custom Search API authentication")
        response = requests.get(CUSTOMSEARCH_API_URL, params=params, timeout=5.0)

        if response.status_code >= 500:
            # 5xx errors indicate server-side issues
            log.error(
                f"Google Search API returned server error: {response.status_code}"
            )
            return ConnectionStatus.SERVICE_UNAVAILABLE
        elif response.status_code == 401 or response.status_code == 403:
            # 401/403 errors indicate authentication issues
            log.error(
                f"Google Search API authentication failed: {response.status_code}"
            )
            return ConnectionStatus.AUTHENTICATION_FAILED
        elif response.status_code >= 400:
            # Other 4xx errors indicate client-side issues (bad request, etc.)
            log.error(
                f"Google Search API returned client error: {response.status_code}"
            )
            return ConnectionStatus.CONNECTION_ERROR

        # For any other error, raise to be caught below
        response.raise_for_status()

        # If we get here, both the service is available and our credentials are valid
        return ConnectionStatus.OK
    except requests.exceptions.ConnectionError:
        # Connection errors indicate network connectivity issues
        log.error(
            "Cannot connect to Google Search API during authentication check - network connectivity issue"
        )
        return ConnectionStatus.CONNECTION_ERROR
    except requests.exceptions.Timeout:
        # Timeouts during the authenticated request are more likely to be service issues
        # Since we already verified basic connectivity in step 1
        log.error("Timeout during Google Search API authentication check")
        return ConnectionStatus.SERVICE_UNAVAILABLE
    except requests.exceptions.RequestException as e:
        # Other request exceptions
        log.error(f"Error during Google Search API authentication check: {str(e)}")
        return ConnectionStatus.CONNECTION_ERROR

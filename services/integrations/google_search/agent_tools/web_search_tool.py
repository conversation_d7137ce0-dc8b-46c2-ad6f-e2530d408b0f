"""Web search tool for the agent."""

import logging
from typing import Any

import grpc
from google.rpc import status_pb2
from pydantic import BaseModel, Field

import base.feature_flags
from services.agents import agents_pb2
from services.agents.tool import (
    ToolRequestContext,
    EmptyExtraToolInput,
    ValidatedTool,
)
from services.integrations.google_search.agent_tools.web_search import (
    ConnectionStatus,
    WebSearchConfig,
    WebSearchError,
    format_results_markdown,
    get_search_credentials,
    test_connection,
    web_search,
)
from services.lib.request_context.request_context import RequestContext

log = logging.getLogger(__name__)

WEBSEARCH_TOOL_SAFETY = base.feature_flags.BoolFlag("websearch_tool_safety", True)


class WebSearchInput(BaseModel):
    """Input schema for the web search tool."""

    query: str = Field(
        ...,  # required field
        description="The search query to send.",
    )

    num_results: int = Field(
        default=5,
        ge=1,
        le=10,
        description="Number of results to return",
    )


class WebSearchTool(ValidatedTool[WebSearchInput, EmptyExtraToolInput]):
    """A tool that performs web searches using Google Custom Search API."""

    id = agents_pb2.RemoteToolId.WEB_SEARCH
    name = "web-search"
    description = """\
Search the web for information. Returns results in markdown format.
Each result includes the URL, title, and a snippet from the page if available.

This tool uses Google's Custom Search API to find relevant web pages."""

    input_model = WebSearchInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_CHECK

    def __init__(
        self,
        config: WebSearchConfig,
    ):
        """Initialize the tool."""
        self.credentials = get_search_credentials(config)

    def get_tool_safety(
        self, context: ToolRequestContext
    ) -> agents_pb2.ToolSafety.ValueType:
        """Return the safety type of the tool."""
        if WEBSEARCH_TOOL_SAFETY.get(
            base.feature_flags.get_global_context().bind_attribute(
                "tenant_name", context.auth_info.tenant_name
            )
        ):
            return agents_pb2.ToolSafety.TOOL_SAFE
        else:
            return agents_pb2.ToolSafety.TOOL_UNSAFE

    def run_validated(
        self,
        validated_input: WebSearchInput,
        extra_tool_input: EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the web search tool.

        Args:
            validated_input: WebSearchInput object containing the query and num_results
            request_context: The request context used for tracking, metrics and authentication.

        Returns:
            string response with formatted search results
        """
        try:
            results = web_search(
                validated_input.query,
                num_results=validated_input.num_results,
                credentials=self.credentials,
            )
            markdown = format_results_markdown(results)
            return markdown

        except WebSearchError as e:
            log.error(f"Web search failed: {str(e)}")
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        return agents_pb2.ToolAvailabilityStatus.AVAILABLE

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:  # type: ignore
        """Revoke access to user external resources for this tool.

        Web search doesn't use OAuth or any other authentication for user data.
        So revoke is an no-op that always succeeds.

        Returns:
            status_pb2.Status: The status of the operation. Always OK.
        """
        return status_pb2.Status(  # type: ignore
            code=grpc.StatusCode.OK.value[0],
            message="Operation not implemented for this tool",
        )

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # type: ignore
        """Test the connection for this tool.

        Performs a real connection test by making a search request to the
        Google Custom Search API. This verifies that:
        1. The API endpoint is reachable
        2. The credentials are valid
        3. The service is functioning properly

        Returns:
            status_pb2.Status: The status of the connection test
        """
        try:
            match test_connection(self.credentials):
                case ConnectionStatus.OK:
                    return status_pb2.Status(  # type: ignore
                        code=grpc.StatusCode.OK.value[0],
                        message="Connection successful",
                    )
                case ConnectionStatus.CONNECTION_ERROR:
                    return status_pb2.Status(  # type: ignore
                        code=grpc.StatusCode.INTERNAL.value[0],
                        message="Connection error occurred",
                    )
                case ConnectionStatus.SERVICE_UNAVAILABLE:
                    return status_pb2.Status(  # type: ignore
                        code=grpc.StatusCode.UNAVAILABLE.value[0],
                        message="Service is unavailable",
                    )
                case ConnectionStatus.AUTHENTICATION_FAILED:
                    return status_pb2.Status(  # type: ignore
                        code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                        message="Authentication failed. Please check your API credentials.",
                    )
                case _:
                    return status_pb2.Status(  # type: ignore
                        code=grpc.StatusCode.UNKNOWN.value[0],
                        message="Unknown error occurred",
                    )
        except Exception as e:
            log.error(f"Unexpected error testing web search connection: {str(e)}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Error testing connection: {str(e)}",
            )

// this file contains a thin go wrapper for the WebhookTenantMapping Kubernetes CRD (Custom Resoure Definition)
//
// It provides a type-safe way to create and update a WebhookTenantMapping resource.
// Beyond that it doesn't container business logic
package webhookmapping

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/dynamic/dynamicinformer"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/clientcmd"
)

// The WebhookTenantMappingSpec is the specifiation or configuration of the WebhookTenantMapping resource
// the fields have to map to the properties of the Tenant CRD in deploy_shard.jsonnet
type WebhookTenantMappingSpec struct {
	WebhookType string `json:"webhook_type"`

	WebhookValue string `json:"webhook_value"`

	// the tenant id of the tenant
	TenantID string `json:"tenant_id"`
}

// the main object for the WebhookTenantMapping CRD
type WebhookTenantMapping struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec WebhookTenantMappingSpec `json:"spec"`
}

// converts the Tenant object to an unstructured object
func (w *WebhookTenantMapping) ToUnstructured() (*unstructured.Unstructured, error) {
	u := &unstructured.Unstructured{}
	bytes, err := json.Marshal(w)
	if err != nil {
		return nil, err
	}
	err = u.UnmarshalJSON(bytes)
	if err != nil {
		return nil, err
	}
	return u, nil
}

// converts the unstructured object to a Tenant object
func (w *WebhookTenantMapping) FromUnstructured(u *unstructured.Unstructured) error {
	bytes, err := u.MarshalJSON()
	if err != nil {
		log.Error().Err(err).Msg("Error converting to unstructured")
		return err
	}
	return json.Unmarshal(bytes, w)
}

// callbacks to be informed about changes by the TenantInformer
type WebhookTenantMappingEventHandlerFuncs struct {
	AddFunc    func(obj *WebhookTenantMapping, isInInitialList bool)
	UpdateFunc func(oldObj *WebhookTenantMapping, newObj *WebhookTenantMapping)
	DeleteFunc func(obj *WebhookTenantMapping)
}

// informs about changes to the WebhookTenantMapping CRD
type WebhookTenantMappingInformer interface {
	// Run starts the informer
	Run(stopCh <-chan struct{})

	// List returns the list of all tenants
	List() ([]WebhookTenantMapping, error)

	// AddEventHandler registers a handler to be called when an event occurs
	// returns a registration that can be used to remove the handler
	AddEventHandler(f WebhookTenantMappingEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error)

	// RemoveEventHandler removes the given handler
	RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error
}

// implementation of the informer interfaced based on s shared informer
type sharedIndexWebhookTenantMappingInformer struct {
	informers []cache.SharedIndexInformer
}

// NewInformer creates a new WebhookTenantMappingInformer that listens to all
// namespaces in one or more contexts
func NewInformer(clientsets []dynamic.Interface, defaultResync time.Duration) WebhookTenantMappingInformer {
	if len(clientsets) == 0 {
		log.Fatal().Msg("No clientsets provided")
	}
	informers := make([]cache.SharedIndexInformer, len(clientsets))
	for i, clientset := range clientsets {
		// Listen to all namespaces
		fac := dynamicinformer.NewDynamicSharedInformerFactory(clientset, defaultResync)
		informers[i] = fac.ForResource(schema.GroupVersionResource{
			Group:    "eng.augmentcode.com",
			Version:  "v1",
			Resource: "webhooktenantmappings",
		}).Informer()
	}

	return &sharedIndexWebhookTenantMappingInformer{
		informers: informers,
	}
}

// NewNamespacedInformer creates a new WebhookTenantMappingInformer in a specific namespace
func NewNamespacedInformer(clientset dynamic.Interface, namespace string, defaultResync time.Duration) WebhookTenantMappingInformer {
	// Listen to all namespaces
	fac := dynamicinformer.NewFilteredDynamicSharedInformerFactory(clientset, defaultResync, namespace, nil)
	informer := fac.ForResource(schema.GroupVersionResource{
		Group:    "eng.augmentcode.com",
		Version:  "v1",
		Resource: "webhooktenantmappings",
	}).Informer()

	return &sharedIndexWebhookTenantMappingInformer{
		informers: []cache.SharedIndexInformer{informer},
	}
}

func (wi *sharedIndexWebhookTenantMappingInformer) Run(stopCh <-chan struct{}) {
	for _, informer := range wi.informers {
		informer.Run(stopCh)
	}
}

func (wi *sharedIndexWebhookTenantMappingInformer) List() ([]WebhookTenantMapping, error) {
	var mappings []WebhookTenantMapping
	for _, informer := range wi.informers {
		for _, obj := range informer.GetStore().List() {
			typedObj := obj.(*unstructured.Unstructured)
			var tenant WebhookTenantMapping
			err := tenant.FromUnstructured(typedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to mapping")
				return nil, err
			}
			mappings = append(mappings, tenant)
		}
	}

	return mappings, nil
}

type MultiEventHandlerRegistration []cache.ResourceEventHandlerRegistration

func (m MultiEventHandlerRegistration) HasSynced() bool {
	for _, r := range m {
		if !r.HasSynced() {
			return false
		}
	}
	return true
}

func (wi *sharedIndexWebhookTenantMappingInformer) AddEventHandler(f WebhookTenantMappingEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	registration := make(MultiEventHandlerRegistration, len(wi.informers))
	for i, informer := range wi.informers {
		handle, err := informer.AddEventHandler(cache.ResourceEventHandlerDetailedFuncs{
			AddFunc: func(obj interface{}, isInInitialList bool) {
				if f.AddFunc == nil {
					return
				}
				// converting the dynamic object to your CRD struct
				typedObj := obj.(*unstructured.Unstructured)
				var crdObj WebhookTenantMapping
				err := crdObj.FromUnstructured(typedObj)
				if err != nil {
					log.Error().Err(err).Msg("Error converting to tenant")
					return
				}
				f.AddFunc(&crdObj, isInInitialList)
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				if f.UpdateFunc == nil {
					return
				}
				oldTypedObj := oldObj.(*unstructured.Unstructured)
				var oldCrdObj WebhookTenantMapping
				err := oldCrdObj.FromUnstructured(oldTypedObj)
				if err != nil {
					log.Error().Err(err).Msg("Error converting to tenant")
					return
				}
				newTypedObj := newObj.(*unstructured.Unstructured)
				var newCrdObj WebhookTenantMapping
				err = newCrdObj.FromUnstructured(newTypedObj)
				if err != nil {
					log.Error().Err(err).Msg("Error converting to tenant")
					return
				}
				f.UpdateFunc(&oldCrdObj, &newCrdObj)
			},
			DeleteFunc: func(obj interface{}) {
				if f.DeleteFunc == nil {
					return
				}
				typedObj := obj.(*unstructured.Unstructured)
				var crdObj WebhookTenantMapping
				err := crdObj.FromUnstructured(typedObj)
				if err != nil {
					log.Error().Err(err).Msg("Error converting to tenant")
					return
				}
				f.DeleteFunc(&crdObj)
			},
		})
		if err != nil {
			return nil, err
		}
		registration[i] = handle
	}
	return registration, nil
}

func (wi *sharedIndexWebhookTenantMappingInformer) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	multi, ok := r.(MultiEventHandlerRegistration)
	if !ok {
		return errors.New("did not find MultiEventHandlerRegistration")
	}
	for i, r := range multi {
		err := wi.informers[i].RemoveEventHandler(r)
		if err != nil {
			return err
		}
	}
	return nil
}

// a webhook tenant mapping resource that allows creating and updating a WebhookTenantMapping in a type-safe way
// more methods can be added here as necessary
type WebhookTenantMappingResource interface {
	Delete(ctx context.Context, name string) error
	Update(ctx context.Context, Spec *WebhookTenantMappingSpec, name string, appName string) (*WebhookTenantMapping, error)
	List(ctx context.Context, appName string) ([]WebhookTenantMapping, error)
}

type webhookTenantMappingResourceImpl struct {
	resource  dynamic.NamespaceableResourceInterface
	namespace string
}

func (tr *webhookTenantMappingResourceImpl) Update(ctx context.Context, spec *WebhookTenantMappingSpec, name string, appName string) (*WebhookTenantMapping, error) {
	t := &WebhookTenantMapping{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: tr.namespace,
			Labels: map[string]string{
				"app": appName,
			},
		},
		TypeMeta: metav1.TypeMeta{
			Kind:       "WebhookTenantMapping",
			APIVersion: "eng.augmentcode.com/v1",
		},
		Spec: *spec,
	}
	unstructuredObj, err := t.ToUnstructured()
	if err != nil {
		log.Error().Err(err).Msg("Error converting to unstructured")
		return nil, err
	}

	var obj *unstructured.Unstructured

	// Try to get the existing object
	existingObj, err := tr.resource.Namespace(t.Namespace).Get(ctx, t.Name, metav1.GetOptions{})
	if err != nil {
		if !k8serrors.IsNotFound(err) {
			log.Error().Err(err).Msg("Error getting existing WebhookTenantMapping")
			return nil, err
		}
		// Object doesn't exist, create it
		obj, err = tr.resource.Namespace(t.Namespace).Create(ctx, unstructuredObj, metav1.CreateOptions{})
		if err != nil {
			log.Error().Err(err).Msg("Error creating WebhookTenantMapping")
			return nil, err
		}
	} else {
		// Object exists, update it
		unstructuredObj.SetResourceVersion(existingObj.GetResourceVersion())
		obj, err = tr.resource.Namespace(t.Namespace).Update(ctx, unstructuredObj, metav1.UpdateOptions{})
		if err != nil {
			log.Error().Err(err).Msg("Error updating WebhookTenantMapping")
			return nil, err
		}
	}

	newObject := &WebhookTenantMapping{}
	err = newObject.FromUnstructured(obj)
	if err != nil {
		log.Error().Err(err).Msg("Error converting from unstructured")
		return nil, err
	}

	return newObject, nil
}

func (tr *webhookTenantMappingResourceImpl) Delete(ctx context.Context, name string) error {
	return tr.resource.Namespace(tr.namespace).Delete(ctx, name, metav1.DeleteOptions{})
}

func (tr *webhookTenantMappingResourceImpl) List(ctx context.Context, appName string) ([]WebhookTenantMapping, error) {
	var listOptions metav1.ListOptions
	if appName != "" {
		listOptions.LabelSelector = fmt.Sprintf("app=%s", appName)
	}
	list, err := tr.resource.Namespace(tr.namespace).List(ctx, listOptions)
	if err != nil {
		log.Error().Err(err).Msg("Error listing WebhookTenantMappings")
		return nil, err
	}

	var mappings []WebhookTenantMapping
	for _, item := range list.Items {
		mapping := &WebhookTenantMapping{}
		err := mapping.FromUnstructured(&item)
		if err != nil {
			log.Error().Err(err).Msg("Error converting from unstructured")
			return nil, err
		}
		mappings = append(mappings, *mapping)
	}

	return mappings, nil
}

func NewWebhookTenantMappingResource(clientset *dynamic.DynamicClient, namespace string) WebhookTenantMappingResource {
	resource := clientset.Resource(schema.GroupVersionResource{
		Group:    "eng.augmentcode.com",
		Version:  "v1",
		Resource: "webhooktenantmappings",
	})
	return &webhookTenantMappingResourceImpl{
		resource:  resource,
		namespace: namespace,
	}
}

// creates a dynamic client based on the given kubeconfig file and context.
func CreateDynamicClientForContext(kubeconfig string, context string) (*dynamic.DynamicClient, error) {
	log.Info().Msgf("Using kubeconfig file: %s", kubeconfig)
	clusterConfig, err := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
		&clientcmd.ClientConfigLoadingRules{ExplicitPath: kubeconfig},
		&clientcmd.ConfigOverrides{
			CurrentContext: context,
		}).ClientConfig()
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating client config")
		return nil, err
	}
	return dynamic.NewForConfig(clusterConfig)
}

// creates a dynamic client based on the in-cluster config
func CreateDynamicClient() (*dynamic.DynamicClient, error) {
	log.Info().Msg("Using in-cluster config")
	restConfig, err := rest.InClusterConfig()
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating in-cluster config")
		return nil, err
	}
	return dynamic.NewForConfig(restConfig)
}

// a fake WebhookTenantMappingInformer to be used for tests
type FakeWebhookTenantMappingInformer struct {
	mappings      []WebhookTenantMapping
	eventHandlers []WebhookTenantMappingEventHandlerFuncs
}

func NewFakeWebhookTenantMappingInformer() *FakeWebhookTenantMappingInformer {
	return &FakeWebhookTenantMappingInformer{}
}

func (fake *FakeWebhookTenantMappingInformer) Update(t WebhookTenantMapping) {
	for i, existingTenant := range fake.mappings {
		if existingTenant.Name == t.Name {
			fake.mappings[i] = t
			for _, f := range fake.eventHandlers {
				f.UpdateFunc(&existingTenant, &t)
			}
			return
		}
	}

	fake.mappings = append(fake.mappings, t)
	for _, f := range fake.eventHandlers {
		f.AddFunc(&t, false)
	}
}

func (fake *FakeWebhookTenantMappingInformer) Delete(t WebhookTenantMapping) {
	log.Info().Msgf("Delete: %v", t)
	for i, existingTenant := range fake.mappings {
		if existingTenant.Name == t.Name {
			// delete index i
			fake.mappings = append(fake.mappings[:i], fake.mappings[i+1:]...)
			for _, f := range fake.eventHandlers {
				f.DeleteFunc(&t)
			}
			log.Info().Msgf("Deleted: %v", fake.mappings)
			return
		}
	}
}

func (fake *FakeWebhookTenantMappingInformer) Run(stopCh <-chan struct{}) {
}

func (fake *FakeWebhookTenantMappingInformer) List() ([]WebhookTenantMapping, error) {
	return fake.mappings, nil
}

func (fake *FakeWebhookTenantMappingInformer) AddEventHandler(f WebhookTenantMappingEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	fake.eventHandlers = append(fake.eventHandlers, f)
	for _, t := range fake.mappings {
		f.AddFunc(&t, true)
	}
	return nil, nil
}

func (fake *FakeWebhookTenantMappingInformer) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	return nil
}

// a FakeWebhookTenantMappingResource to be used for tests
type FakeWebhookTenantMappingResource struct {
	Mappings        map[string]WebhookTenantMapping
	resourceVersion int
	ch              chan struct{}
}

func NewFakeWebhookTenantMappingResource() *FakeWebhookTenantMappingResource {
	return &FakeWebhookTenantMappingResource{
		Mappings:        map[string]WebhookTenantMapping{},
		resourceVersion: 0,
		ch:              make(chan struct{}),
	}
}

func (tr *FakeWebhookTenantMappingResource) Delete(ctx context.Context, name string, namespace string) error {
	delete(tr.Mappings, name)
	return nil
}

func (tr *FakeWebhookTenantMappingResource) WaitForUpdate() {
	<-tr.ch
}

func (tr *FakeWebhookTenantMappingResource) Add(t *WebhookTenantMapping) {
	tr.Mappings[t.Name] = *t
}

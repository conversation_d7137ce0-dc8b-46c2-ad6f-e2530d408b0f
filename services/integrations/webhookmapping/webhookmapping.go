package webhookmapping

import (
	"fmt"
	"sync"

	"github.com/rs/zerolog/log"
)

type WebhookMappingLookup interface {
	LookupTenant(webhookType string, webhookValue string) (string, error)
	Run(stopCh <-chan struct{}) error
}

type key struct {
	webhookType  string
	webhookValue string
}

type webhookMappingLookup struct {
	informer WebhookTenantMappingInformer
	mutex    sync.Mutex
	mappings map[key]WebhookTenantMapping
}

func NewLookup(informer WebhookTenantMappingInformer) WebhookMappingLookup {
	l := &webhookMappingLookup{
		informer: informer,
		mutex:    sync.Mutex{},
		mappings: map[key]WebhookTenantMapping{},
	}
	l.informer.AddEventHandler(WebhookTenantMappingEventHandlerFuncs{
		AddFunc: func(obj *WebhookTenantMapping, isInInitialList bool) {
			log.Info().Msgf("AddFunc: %s, isInInitialList=%v", obj.Name, isInInitialList)
			l.mutex.Lock()
			defer l.mutex.Unlock()
			k := key{obj.Spec.WebhookType, obj.Spec.WebhookValue}
			if _, ok := l.mappings[k]; ok {
				log.Warn().Msgf("Possible duplicate webhook mapping: %s", obj.Name)
			}
			l.mappings[k] = *obj
		},
		UpdateFunc: func(oldObj *WebhookTenantMapping, newObj *WebhookTenantMapping) {
			var oldName, newName string
			if oldObj != nil {
				oldName = oldObj.Name
			}
			if newObj != nil {
				newName = newObj.Name
			}
			log.Info().Msgf("UpdateFunc: %s -> %s", oldName, newName)
			l.mutex.Lock()
			defer l.mutex.Unlock()
			if oldObj != nil {
				delete(l.mappings, key{oldObj.Spec.WebhookType, oldObj.Spec.WebhookValue})
			}
			if newObj != nil {
				l.mappings[key{newObj.Spec.WebhookType, newObj.Spec.WebhookValue}] = *newObj
			}
		},
		DeleteFunc: func(obj *WebhookTenantMapping) {
			log.Info().Msgf("DeleteFunc: %s", obj.Name)
			l.mutex.Lock()
			defer l.mutex.Unlock()
			delete(l.mappings, key{obj.Spec.WebhookType, obj.Spec.WebhookValue})
		},
	})
	return l
}

func (w *webhookMappingLookup) Run(stopCh <-chan struct{}) error {
	w.informer.Run(stopCh)
	return nil
}

func (w *webhookMappingLookup) LookupTenant(webhookType string, webhookValue string) (string, error) {
	log.Info().Msgf("LookupTenant: %s, %s", webhookType, webhookValue)
	w.mutex.Lock()
	defer w.mutex.Unlock()
	if mapping, ok := w.mappings[key{webhookType, webhookValue}]; ok {
		return mapping.Spec.TenantID, nil
	}
	return "", fmt.Errorf("no matching tenant found for webhook type: %s, webhook value: %s", webhookType, webhookValue)
}

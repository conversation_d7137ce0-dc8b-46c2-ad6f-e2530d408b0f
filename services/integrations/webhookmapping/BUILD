load("//tools/bzl:go.bzl", "go_library", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "webhookmapping",
    srcs = [
        "crd.go",
        "webhookmapping.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/webhookmapping",
    visibility = ["//services:__subpackages__"],
    deps = [
        "@com_github_rs_zerolog//log",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/apis/meta/v1/unstructured",
        "@io_k8s_apimachinery//pkg/runtime/schema",
        "@io_k8s_client_go//dynamic",
        "@io_k8s_client_go//dynamic/dynamicinformer",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/cache",
        "@io_k8s_client_go//tools/clientcmd",
        "@io_k8s_client_go//tools/clientcmd/api",
    ],
)

go_test(
    name = "webhookmapping_test",
    srcs = ["webhookmapping_test.go"],
    embed = [":webhookmapping"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/types",
        "@io_k8s_apimachinery//pkg/watch",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
    visibility = ["//services/deploy:__subpackages__"],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg_shared",
    ],
)

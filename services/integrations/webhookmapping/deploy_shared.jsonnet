function(cloud)
  [
    // the WebhookTenantMapping CRD definition
    {
      apiVersion: 'apiextensions.k8s.io/v1',
      kind: 'CustomResourceDefinition',
      metadata: {
        name: 'webhooktenantmappings.eng.augmentcode.com',
      },
      spec: {
        group: 'eng.augmentcode.com',
        versions: [
          {
            name: 'v1',
            served: true,
            storage: true,
            subresources: {
              status: {},
            },
            schema: {
              openAPIV3Schema: {
                description: '',
                type: 'object',
                required: ['spec'],
                properties: {
                  spec: {
                    type: 'object',
                    required: ['webhook_type', 'webhook_value', 'tenant_id'],
                    properties: {
                      webhook_type: {
                        description: 'valid webhook type',
                        type: 'string',
                        enum: ['github', 'slack'],
                      },
                      // note: the webhook value is confidental, but not restricted or secret information
                      webhook_value: {
                        description: 'valid webhook value',
                        type: 'string',
                      },
                      tenant_id: {
                        description: 'tenant id',
                        type: 'string',
                        pattern: '^[0-9a-f]+$',
                      },
                    },
                  },
                },
              },
            },
          },
        ],
        scope: 'Namespaced',
        names: {
          plural: 'webhooktenantmappings',
          singular: 'webhooktenantmapping',
          kind: 'WebhookTenantMapping',
        },
      },
    },
    {
      apiVersion: 'rbac.authorization.k8s.io/v1',
      kind: 'ClusterRole',
      metadata: {
        name: 'webhooktenantmapping-reader',
      },
      rules: [
        {
          apiGroups: ['eng.augmentcode.com'],
          resources: ['webhooktenantmappings'],
          verbs: ['get', 'list', 'watch'],
        },
      ],
    },
  ]

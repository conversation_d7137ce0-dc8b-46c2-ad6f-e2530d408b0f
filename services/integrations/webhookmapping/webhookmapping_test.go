package webhookmapping

import (
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestWebhookMappingLookup(t *testing.T) {
	mapping := &WebhookTenantMapping{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-mapping",
		},
		Spec: WebhookTenantMappingSpec{
			WebhookType:  "slack",
			WebhookValue: "T1",
			TenantID:     "test-tenant",
		},
	}
	informer := NewFakeWebhookTenantMappingInformer()
	informer.Update(*mapping)
	lookup := NewLookup(informer)
	tenant, err := lookup.LookupTenant("slack", "T1")
	assert.NoError(t, err)
	assert.Equal(t, "test-tenant", tenant)
}

func TestWebhookMappingLookupNoMatch(t *testing.T) {
	mapping := &WebhookTenantMapping{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-mapping",
		},
		Spec: WebhookTenantMappingSpec{
			WebhookType:  "slack",
			WebhookValue: "T1",
			TenantID:     "test-tenant",
		},
	}
	informer := NewFakeWebhookTenantMappingInformer()
	informer.Update(*mapping)
	lookup := NewLookup(informer)
	_, err := lookup.LookupTenant("slack", "T2")
	assert.Error(t, err)

	_, err = lookup.LookupTenant("github", "T1")
	assert.Error(t, err)
}

package orb

import (
	"context"
	"net/http"
	"time"

	"github.com/stretchr/testify/mock"
)

type MockOrbClient struct {
	OrbClient
	mock.Mock
}

// NewMockOrbClient creates a new mock Orb client
// Users will need to mock what they want to happen when a function is called using .On
func NewMockOrbClient() *MockOrbClient {
	return new(MockOrbClient)
}

func (c *MockOrbClient) CreateCustomer(ctx context.Context, customer OrbCustomer, usingStripe bool, idempotencyKey *string) (string, error) {
	retVals := c.Called(ctx, customer, usingStripe, idempotencyKey)
	return retVals.String(0), retVals.Error(1)
}

func (c *MockOrbClient) CreateSubscription(ctx context.Context, subscription OrbSubscription, idempotencyKey *string) (string, error) {
	retVals := c.Called(ctx, subscription, idempotencyKey)
	return retVals.String(0), retVals.Error(1)
}

func (c *MockOrbClient) AddAlertsForCustomer(ctx context.Context, customerOrbID string, currency string) error {
	retVals := c.Called(ctx, customerOrbID, currency)
	return retVals.Error(0)
}

func (c *MockOrbClient) AddCreditBalanceDepletedAlert(ctx context.Context, customerOrbID string, currency string, idempotencyKey *string) error {
	retVals := c.Called(ctx, customerOrbID, currency, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) AddCreditBalanceRecoveredAlert(ctx context.Context, customerOrbID string, currency string, idempotencyKey *string) error {
	retVals := c.Called(ctx, customerOrbID, currency, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) SetCustomerPlanType(ctx context.Context, planChange OrbPlanChange, idempotencyKey *string) error {
	retVals := c.Called(ctx, planChange, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) IngestEvents(ctx context.Context, events []*OrbEvent) error {
	retVals := c.Called(ctx, events)
	return retVals.Error(0)
}

func (c *MockOrbClient) GetUserSubscription(ctx context.Context, orbSubscriptionId string, itemIds *ItemIds) (*OrbSubscriptionInfo, error) {
	retVals := c.Called(ctx, orbSubscriptionId, itemIds)
	return retVals.Get(0).(*OrbSubscriptionInfo), retVals.Error(1)
}

func (c *MockOrbClient) GetCustomerCreditBalance(ctx context.Context, customerOrbId string, currency string) (float64, error) {
	retVals := c.Called(ctx, customerOrbId, currency)
	return retVals.Get(0).(float64), retVals.Error(1)
}

func (c *MockOrbClient) PurchaseCredits(ctx context.Context, creditPurchase OrbCreditPurchase, idempotencyKey *string) error {
	retVals := c.Called(ctx, creditPurchase, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) CancelOrbSubscription(ctx context.Context, orbSubscriptionId string, cancelTime PlanChangeType, cancelDate *time.Time, idempotencyKey *string) error {
	retVals := c.Called(ctx, orbSubscriptionId, cancelTime, cancelDate, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) VerifyWebhookSignature(r *http.Request) ([]byte, error) {
	retVals := c.Called(r)
	return retVals.Get(0).([]byte), retVals.Error(1)
}

func (c *MockOrbClient) UpdateFixedQuantity(ctx context.Context, quantityUpdate OrbQuantityUpdate, idempotencyKey *string) error {
	retVals := c.Called(ctx, quantityUpdate, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) GetCustomerCreditInfo(ctx context.Context, customerOrbId string, billingPeriodStartDate time.Time, currency string) (*CustomerCreditInfo, error) {
	retVals := c.Called(ctx, customerOrbId, billingPeriodStartDate, currency)
	return retVals.Get(0).(*CustomerCreditInfo), retVals.Error(1)
}

func (c *MockOrbClient) GetPlanInformation(ctx context.Context, itemIDs ItemIds, orbSubscriptionID *string, externalPlanID *string) (*OrbPlanInfo, error) {
	retVals := c.Called(ctx, itemIDs, orbSubscriptionID, externalPlanID)
	return retVals.Get(0).(*OrbPlanInfo), retVals.Error(1)
}

func (c *MockOrbClient) UnschedulePendingSubscriptionCancellation(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error {
	retVals := c.Called(ctx, orbSubscriptionId, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) UnscheduleFixedQuantity(ctx context.Context, orbSubscriptionID string, priceID string, idempotencyKey *string) error {
	retVals := c.Called(ctx, orbSubscriptionID, priceID, idempotencyKey)
	return retVals.Error(0)
}

func (c *MockOrbClient) GetFailedPaymentInfo(ctx context.Context, orbSubscriptionId string) (*FailedPaymentInfo, error) {
	retVals := c.Called(ctx, orbSubscriptionId)
	result := retVals.Get(0)
	if result == nil {
		return nil, retVals.Error(1)
	}
	return result.(*FailedPaymentInfo), retVals.Error(1)
}

func (c *MockOrbClient) GetInvoice(ctx context.Context, invoiceId string) (*OrbInvoice, error) {
	retVals := c.Called(ctx, invoiceId)
	return retVals.Get(0).(*OrbInvoice), retVals.Error(1)
}

func (c *MockOrbClient) UpdateCustomerBillingAddress(ctx context.Context, customerOrbID string, address *Address) error {
	retVals := c.Called(ctx, customerOrbID, address)
	return retVals.Error(0)
}

func (c *MockOrbClient) UpdateCustomerMetadata(ctx context.Context, customerOrbID string, metadata map[string]string) error {
	retVals := c.Called(ctx, customerOrbID, metadata)
	return retVals.Error(0)
}

func (c *MockOrbClient) GetScheduledPlanChanges(ctx context.Context, orbSubscriptionId string, orbCustomerId string) (*string, error) {
	retVals := c.Called(ctx, orbSubscriptionId, orbCustomerId)
	result := retVals.Get(0)
	if result == nil {
		return nil, retVals.Error(1)
	}
	return result.(*string), retVals.Error(1)
}

func (c *MockOrbClient) UnschedulePlanChanges(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error {
	retVals := c.Called(ctx, orbSubscriptionId, idempotencyKey)
	return retVals.Error(0)
}

package orb

import (
	"context"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/hashicorp/golang-lru/v2/simplelru"
	"github.com/orbcorp/orb-go"
	"github.com/orbcorp/orb-go/option"
	"github.com/rs/zerolog/log"
)

type OrbClient interface {
	CreateCustomer(ctx context.Context, customer OrbCustomer, usingStripe bool, idempotencyKey *string) (string, error)
	CreateSubscription(ctx context.Context, subscription OrbSubscription, idempotencyKey *string) (string, error)
	AddAlertsForCustomer(ctx context.Context, customerOrbID string, currency string) error
	AddCreditBalanceDepletedAlert(ctx context.Context, customerOrbID string, currency string, idempotencyKey *string) error
	AddCreditBalanceRecoveredAlert(ctx context.Context, customerOrbID string, currency string, idempotencyKey *string) error
	SetCustomerPlanType(ctx context.Context, planChange OrbPlanChange, idempotencyKey *string) error
	IngestEvents(ctx context.Context, events []*OrbEvent) error
	GetUserSubscription(ctx context.Context, orbSubscriptionId string, itemIds *ItemIds) (*OrbSubscriptionInfo, error)
	GetCustomerCreditBalance(ctx context.Context, customerOrbId string, currency string) (float64, error)
	PurchaseCredits(ctx context.Context, creditPurchase OrbCreditPurchase, idempotencyKey *string) error
	CancelOrbSubscription(ctx context.Context, orbSubscriptionId string, cancelTime PlanChangeType, cancelDate *time.Time, idempotencyKey *string) error
	UpdateFixedQuantity(ctx context.Context, quantityUpdate OrbQuantityUpdate, idempotencyKey *string) error
	VerifyWebhookSignature(r *http.Request) ([]byte, error)
	GetCustomerCreditInfo(ctx context.Context, customerOrbId string, billingPeriodStartDate time.Time, currency string) (*CustomerCreditInfo, error)
	GetPlanInformation(ctx context.Context, itemIDs ItemIds, orbSubscriptionID *string, externalPlanID *string) (*OrbPlanInfo, error)
	UnschedulePendingSubscriptionCancellation(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error
	UnscheduleFixedQuantity(ctx context.Context, orbSubscriptionID string, priceID string, idempotencyKey *string) error
	GetFailedPaymentInfo(ctx context.Context, orbSubscriptionId string) (*FailedPaymentInfo, error)
	GetInvoice(ctx context.Context, invoiceId string) (*OrbInvoice, error)
	UpdateCustomerBillingAddress(ctx context.Context, customerOrbID string, address *Address) error
	UpdateCustomerMetadata(ctx context.Context, customerOrbID string, metadata map[string]string) error
	GetScheduledPlanChanges(ctx context.Context, orbSubscriptionId string, orbCustomerId string) (*string, error)
	UnschedulePlanChanges(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error
}

const priceCacheSizeLimit = 1000

// RateLimitInfo holds rate limiting information from HTTP response headers
// NOTE: This is defined here rather than in metrics_interceptor.go because
// the Orb SDK client needs to be created with middleware at initialization time,
// and we need access to the actual HTTP responses to capture headers.
// The metrics interceptor wraps the OrbClient interface, not the underlying SDK client.
type RateLimitInfo struct {
	Limit     int
	Remaining int
	Reset     time.Time
	Category  string
}

// HeaderCapture is a thread-safe store for captured HTTP response headers
type HeaderCapture struct {
	mu            sync.RWMutex
	lastRateLimit map[string]*RateLimitInfo // keyed by operation category
}

func NewHeaderCapture() *HeaderCapture {
	return &HeaderCapture{
		lastRateLimit: make(map[string]*RateLimitInfo),
	}
}

func (hc *HeaderCapture) UpdateRateLimit(category string, info *RateLimitInfo) {
	hc.mu.Lock()
	defer hc.mu.Unlock()
	hc.lastRateLimit[category] = info
}

func (hc *HeaderCapture) GetRateLimit(category string) *RateLimitInfo {
	hc.mu.RLock()
	defer hc.mu.RUnlock()
	return hc.lastRateLimit[category]
}

// parseRateLimitHeaders extracts rate limit information from HTTP response headers
func parseRateLimitHeaders(headers http.Header) *RateLimitInfo {
	// Try different common rate limit header formats
	headerVariants := []struct {
		limit     string
		remaining string
		reset     string
	}{
		{"X-RateLimit-Limit", "X-RateLimit-Remaining", "X-RateLimit-Reset"},
		{"X-Rate-Limit-Limit", "X-Rate-Limit-Remaining", "X-Rate-Limit-Reset"},
		{"RateLimit-Limit", "RateLimit-Remaining", "RateLimit-Reset"},
		{"X-Ratelimit-Limit", "X-Ratelimit-Remaining", "X-Ratelimit-Reset"},
	}

	for _, variant := range headerVariants {
		limitStr := headers.Get(variant.limit)
		remainingStr := headers.Get(variant.remaining)
		resetStr := headers.Get(variant.reset)

		if limitStr != "" && remainingStr != "" {
			limit, err1 := strconv.Atoi(limitStr)
			remaining, err2 := strconv.Atoi(remainingStr)

			if err1 == nil && err2 == nil {
				info := &RateLimitInfo{
					Limit:     limit,
					Remaining: remaining,
				}

				// Parse reset time if available
				if resetStr != "" {
					if resetInt, err := strconv.ParseInt(resetStr, 10, 64); err == nil {
						// Assume Unix timestamp
						info.Reset = time.Unix(resetInt, 0)
					}
				}

				return info
			}
		}
	}

	return nil
}

// createHeaderCaptureMiddleware creates middleware that captures response headers
func createHeaderCaptureMiddleware(headerCapture *HeaderCapture) func(*http.Request, option.MiddlewareNext) (*http.Response, error) {
	return func(req *http.Request, next option.MiddlewareNext) (*http.Response, error) {
		resp, err := next(req)
		if resp != nil {
			// Parse rate limit headers
			if rateLimitInfo := parseRateLimitHeaders(resp.Header); rateLimitInfo != nil {
				// Determine category based on request URL/method
				category := categorizeRequestFromURL(req.URL.Path, req.Method)
				rateLimitInfo.Category = category
				headerCapture.UpdateRateLimit(category, rateLimitInfo)
			}
		}
		return resp, err
	}
}

// categorizeRequestFromURL determines the category of a request based on URL and method
func categorizeRequestFromURL(path, method string) string {
	// Credit/ledger operations
	if strings.Contains(path, "/credit") || strings.Contains(path, "/ledger") {
		return "credit_ledger"
	}

	// Write operations (POST, PUT, PATCH, DELETE)
	if method == "POST" || method == "PUT" || method == "PATCH" || method == "DELETE" {
		return "write"
	}

	// Everything else is a read operation
	return "read"
}

type OrbClientImpl struct {
	client             *orb.Client
	webhookSecret      *string
	priceCache         *simplelru.LRU[string, *orb.Price]
	featureFlagHandler featureflags.FeatureFlagHandle
	headerCapture      *HeaderCapture
}

type OrbCustomer struct {
	Email              string
	Name               string
	StripeID           string // the customer's Stripe ID
	Metadata           map[string]string
	Timezone           string
	ExternalCustomerID *string // the customer's Augment ID. Only set if we are certain what we are using this for.
}

type OrbPriceOverrides struct {
	PriceID  string  // the Price ID in Orb for the thing we are overriding
	Quantity float64 // the quantity we are overriding
}

type OrbAdjustment struct {
	PriceIDs []string  // the Price ID in Orb for the thing we are adjusting
	Amount   float64   // the amount of adjustment
	EndDate  time.Time // end date of adjustment
}

type OrbSubscription struct {
	CustomerOrbID     string              // customer's Orb ID
	ExternalPlanID    string              // use the external plan ID from Orb -- the human readable plan name
	PlanVersionNumber *int64              // the version number of the plan. If not set, will default to the latest plan version.
	PriceOverrides    []OrbPriceOverrides // Overrides for specific prices. Can be used to change the number of seats, etc.
	Adjustments       []OrbAdjustment     // Adjustments to the subscription. Can be used to change the price of a specific price ID for a set period of time.
	StartDate         *time.Time          // start date of subscription. If not set, will default to current time
	EndDate           *time.Time          // end date of subscription, if applicable.
}

type FixedQuantities struct {
	SeatsID            string
	Seats              int
	IncludedMessagesID string
	IncludedMessages   int // Number of messages per month
}

type ItemIds struct {
	SeatsID            string // price ID of "seats"
	IncludedMessagesID string // price ID of "Included Allocation (User Messages)"
}

type OrbSubscriptionInfo struct {
	OrbSubscriptionID             string    // The ID of the subscription
	OrbCustomerID                 string    // The ID of the customer associated with the subscription
	OrbStatus                     string    // The status of the subscription
	ExternalPlanID                string    // The plan the user is subscribed to
	CurrentBillingPeriodStartDate time.Time // The start date of the current billing period
	CurrentBillingPeriodEndDate   time.Time // The end date of the current billing period
	StartDate                     time.Time // the start date of the subscription
	EndDate                       time.Time // the end date of the subscription, if applicable
	PortalUrl                     string    // the billing portal url for the customer
	NextBillingCycleAmount        string    // the amount of the next billing cycle

	// The ID of the Seats and Included Credits, and amounts. This is for the current subscription plan.
	// will be non-nil if you pass in price items
	CurrentFixedQuantities *FixedQuantities

	// Future is only set if we are scheduled to change to another plan or have a scheduled seat change
	// Also requires price items to be passed in
	FutureFixedQuantities *FixedQuantities

	// Scheduled plan change information from Orb's pending_subscription_change field
	ScheduledTargetPlanID *string // The target plan ID if there's a scheduled plan change
}

type PlanChangeType int

const (
	PlanChangeImmediate PlanChangeType = iota
	PlanChangeEndOfTerm
	PlanChangeSpecificDate
)

type BillingCycleAlignment int

const (
	BillingCycleAlignmentPlanChangeDate BillingCycleAlignment = iota
	BillingCycleAlignmentUnchanged
)

type OrbPlanChange struct {
	CustomerOrbID         string
	SubscriptionID        string
	NewPlanID             string
	PriceOverrides        []OrbPriceOverrides
	PlanChangeType        PlanChangeType
	PlanChangeDate        *time.Time // only provided if planChangeType is PlanChangeSpecificDate
	BillingCycleAlignment BillingCycleAlignment
}

type OrbCreditPurchase struct {
	CustomerOrbID string
	NumberCredits float64
	Description   string
	CostBasis     float64    // the cost basis of the credits in USD. 1 would mean 1 credit charges them $1. 0 means we are giving it to them for free.
	ExpiryDate    time.Time  // the expiry date of the credits.
	Currency      string     // the currency of the credits. (ex: "usermessage")
	StartDate     *time.Time // the start date of the credits. If not set, will default to current time. Must be midnight in customer's time (UTC for all)
}

// OrbEvent represents an event to be ingested by Orb
type OrbEvent struct {
	EventName          string
	CustomerOrbID      *string // customer's Orb subscription ID
	ExternalCustomerID *string // customer's Augment ID
	Timestamp          time.Time
	IdempotencyKey     string
	Properties         map[string]interface{}
}

type OrbQuantityUpdate struct {
	OrbSubscriptionID string
	PriceOverride     OrbPriceOverrides
	UpdateTimeType    PlanChangeType
	UpdateChangeDate  *time.Time // only provided if planChangeType is PlanChangeSpecificDate
}

type CustomerCreditInfo struct {
	ActiveCredits               float64
	PendingCredits              float64
	CreditsUsedThisBillingCycle float64
}

type OrbPlanInfo struct {
	ExternalPlanID          string
	Name                    string
	PricePerSeat            string
	MessagesPerSeat         float64
	SeatsPriceID            string
	IncludedMessagesPriceID string
}

type FailedPaymentInfo struct {
	Amount string
	Date   time.Time
}

// OrbInvoice represents an invoice from Orb
type OrbInvoice struct {
	ID              string
	SubscriptionID  string
	CustomerID      string
	Status          string
	PaymentAttempts int32
	Source          string // can be "subscription", "partial", or "one_off"
}

type Address struct {
	Line1      string
	Line2      string
	City       string
	State      string
	PostalCode string
	Country    string
}

func NewOrbClient(orbApiKey string, featureFlagHandler featureflags.FeatureFlagHandle) OrbClient {
	priceCache, err := simplelru.NewLRU[string, *orb.Price](priceCacheSizeLimit, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create price cache")
		return nil
	}

	// Create header capture for rate limiting
	headerCapture := NewHeaderCapture()

	client := &OrbClientImpl{
		client: orb.NewClient(
			option.WithAPIKey(orbApiKey),
			option.WithMiddleware(createHeaderCaptureMiddleware(headerCapture)),
		),
		webhookSecret:      nil,
		priceCache:         priceCache,
		featureFlagHandler: featureFlagHandler,
		headerCapture:      headerCapture,
	}
	// Wrap with metrics interceptor
	return NewMetricsInterceptor(client)
}

// NewOrbClientWithWebhook creates a new Orb client with the given API key and webhook secret
func NewOrbClientWithWebhook(orbApiKey string, webhookSecret string) OrbClient {
	var secretPtr *string
	if webhookSecret != "" {
		secretPtr = &webhookSecret
	}

	priceCache, err := simplelru.NewLRU[string, *orb.Price](priceCacheSizeLimit, nil)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create price cache")
		return nil
	}

	// Create header capture for rate limiting
	headerCapture := NewHeaderCapture()

	client := &OrbClientImpl{
		client: orb.NewClient(
			option.WithAPIKey(orbApiKey),
			option.WithMiddleware(createHeaderCaptureMiddleware(headerCapture)),
		),
		webhookSecret:      secretPtr,
		priceCache:         priceCache,
		featureFlagHandler: featureflags.NewLocalFeatureFlagHandler(), // Don't need to pass in feature flags for this
		headerCapture:      headerCapture,
	}
	// Wrap with metrics interceptor
	return NewMetricsInterceptor(client)
}

func (c *OrbClientImpl) CreateCustomer(
	ctx context.Context, customer OrbCustomer, usingStripe bool, idempotencyKey *string,
) (string, error) {
	// Not using at the moment for CreateCustomer: ShippingAddress, TaxID, TaxConfiguration,
	// ReportingConfiguration, AccountingSyncConfiguration, Hierarchy, AdditionalEmails
	// Can add these in as necessary

	params := orb.CustomerNewParams{
		Email:    orb.F(customer.Email),
		Name:     orb.F(customer.Name),
		Metadata: orb.F(customer.Metadata),
		Timezone: orb.F(customer.Timezone),
		Currency: orb.F("USD"),
	}
	if usingStripe {
		// If stripe is setup in our Orb, this should be true. Separating out for now as we haven't connected Orb to Stripe
		params.PaymentProviderID = orb.F(customer.StripeID)
		params.PaymentProvider = orb.F(orb.CustomerNewParamsPaymentProviderStripeCharge)
		params.AutoCollection = orb.F(true) // Confirm we will auto-collect payments from Stripe
	}
	if customer.ExternalCustomerID != nil {
		params.ExternalCustomerID = orb.F(*customer.ExternalCustomerID)
	}

	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	orbCustomer, err := c.client.Customers.New(
		ctx,
		params,
		options...,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create orb customer")
		return "", fmt.Errorf("failed to create orb customer: %w", err)
	}
	log.Info().Msg("Successfully created orb customer")
	return orbCustomer.ID, nil
}

func (c *OrbClientImpl) UpdateCustomerMetadata(ctx context.Context, customerOrbID string, metadata map[string]string) error {
	_, err := c.client.Customers.Update(ctx, customerOrbID, orb.CustomerUpdateParams{
		Metadata: orb.F(metadata),
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to update customer metadata")
		return fmt.Errorf("failed to update customer metadata: %w", err)
	}
	log.Info().Msg("Successfully updated customer metadata")
	return nil
}

func (c *OrbClientImpl) CreateSubscription(
	ctx context.Context, subscription OrbSubscription, idempotencyKey *string,
) (string, error) {
	// usage_customer_ids: can create a list of customer ids whose usage events will be aggregated and billed under this subscription
	// not adding now, but could be useful later for teams

	params := orb.SubscriptionNewParams{
		CustomerID:                            orb.F(subscription.CustomerOrbID),
		ExternalPlanID:                        orb.F(subscription.ExternalPlanID),
		AlignBillingWithSubscriptionStartDate: orb.F(true),
		AutoCollection:                        orb.F(true), // confirm this is right -- means we auto bill
	}
	if subscription.PlanVersionNumber != nil {
		params.PlanVersionNumber = orb.F(*subscription.PlanVersionNumber)
	}
	if subscription.StartDate != nil {
		params.StartDate = orb.F(*subscription.StartDate)
	}
	if subscription.EndDate != nil {
		params.EndDate = orb.F(*subscription.EndDate)
	}
	if len(subscription.PriceOverrides) > 0 {
		// If we have price overrides, add them
		overrides := []orb.SubscriptionNewParamsReplacePrice{}
		for _, override := range subscription.PriceOverrides {
			o := orb.SubscriptionNewParamsReplacePrice{
				ReplacesPriceID:    orb.F(override.PriceID),
				FixedPriceQuantity: orb.F(override.Quantity),
			}
			overrides = append(overrides, o)
			params.ReplacePrices = orb.F(overrides)
		}
	}

	if len(subscription.Adjustments) > 0 {
		adjustments := []orb.SubscriptionNewParamsAddAdjustment{}
		for _, adjustment := range subscription.Adjustments {
			a := orb.SubscriptionNewParamsAddAdjustmentsAdjustmentNewAmountDiscount{
				AdjustmentType:    orb.F(orb.SubscriptionNewParamsAddAdjustmentsAdjustmentNewAmountDiscountAdjustmentTypeAmountDiscount),
				AmountDiscount:    orb.F(fmt.Sprintf("%f", adjustment.Amount)),
				AppliesToPriceIDs: orb.F(adjustment.PriceIDs),
			}
			adjustments = append(adjustments, orb.SubscriptionNewParamsAddAdjustment{
				Adjustment: orb.F(orb.SubscriptionNewParamsAddAdjustmentsAdjustmentUnion(a)),
				EndDate:    orb.F(adjustment.EndDate),
			})
		}
		params.AddAdjustments = orb.F(adjustments)
	}

	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	orbSubscription, err := c.client.Subscriptions.New(
		ctx,
		params,
		options...,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create orb subscription")
		return "", fmt.Errorf("failed to create orb subscription: %w", err)
	}
	log.Info().Msg("Successfully created orb subscription")
	return orbSubscription.ID, nil
}

// Adds the alerts for a user for credit_balance_depleted and credit_balance_recovered
// Currency is the currency or custom pricing unit to use for this alert (ie 'requests', 'USD', etc)
func (c *OrbClientImpl) AddAlertsForCustomer(ctx context.Context, customerOrbId string, currency string) error {
	// Add alert for when balance is depleted
	if err := c.AddCreditBalanceDepletedAlert(ctx, customerOrbId, currency, nil); err != nil {
		log.Error().Err(err).Msg("Failed to create orb alert for credit balance depletion")
		return err
	}

	// Add alert for when balance recovers
	if err := c.AddCreditBalanceRecoveredAlert(ctx, customerOrbId, currency, nil); err != nil {
		log.Error().Err(err).Msg("Failed to create orb alert for credit balance recovery")
		return err
	}

	log.Info().Msg("Successfully created orb alerts for customer")
	return nil
}

func (c *OrbClientImpl) AddCreditBalanceDepletedAlert(
	ctx context.Context, customerOrbId string, currency string, idempotencyKey *string,
) error {
	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	_, err := c.client.Alerts.NewForCustomer(
		ctx,
		customerOrbId,
		orb.AlertNewForCustomerParams{
			Currency: orb.F(currency),
			Type:     orb.F(orb.AlertNewForCustomerParamsTypeCreditBalanceDepleted),
		},
		options...,
	)
	if err != nil {
		return fmt.Errorf("failed to create orb alert for credit balance depletion: %w", err)
	}
	log.Info().Msg("Successfully created orb alert for credit balance depletion")
	return nil
}

func (c *OrbClientImpl) AddCreditBalanceRecoveredAlert(
	ctx context.Context, customerOrbId string, currency string, idempotencyKey *string,
) error {
	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	_, err := c.client.Alerts.NewForCustomer(
		ctx,
		customerOrbId,
		orb.AlertNewForCustomerParams{
			Currency: orb.F(currency),
			Type:     orb.F(orb.AlertNewForCustomerParamsTypeCreditBalanceRecovered),
		},
		options...,
	)
	if err != nil {
		return fmt.Errorf("failed to create orb alert for credit balance recovery: %w", err)
	}
	log.Info().Msg("Successfully created orb alert for credit balance recovery")
	return nil
}

func (c *OrbClientImpl) GetUserSubscription(ctx context.Context, orbSubscriptionId string, itemIds *ItemIds) (*OrbSubscriptionInfo, error) {
	subscription, err := c.client.Subscriptions.Fetch(
		ctx,
		orbSubscriptionId,
	)
	if err != nil {
		log.Error().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Unable to get subscription")
		return nil, fmt.Errorf("unable to get subscription: %w", err)
	}

	orbSubscriptionInfo := &OrbSubscriptionInfo{
		OrbSubscriptionID:             subscription.ID,
		OrbCustomerID:                 subscription.Customer.ID,
		OrbStatus:                     string(subscription.Status),
		ExternalPlanID:                subscription.Plan.ExternalPlanID,
		CurrentBillingPeriodStartDate: subscription.CurrentBillingPeriodStartDate,
		CurrentBillingPeriodEndDate:   subscription.CurrentBillingPeriodEndDate,
		StartDate:                     subscription.StartDate,
		EndDate:                       subscription.EndDate,
		PortalUrl:                     subscription.Customer.PortalURL,
	}

	// If we want to extract prices, do that
	if itemIds != nil {
		seatsPriceID := itemIds.SeatsID
		includedMessagesPriceID := itemIds.IncludedMessagesID

		currentFixedQuantities := &FixedQuantities{}
		futureFixedQuantities := &FixedQuantities{}
		hasFuture := false

		for _, price := range subscription.Plan.Prices {
			// Fill in the initial ones we know, to limit searching as much as possible
			c.priceCache.Add(price.ID, &price)
		}

		for _, ff := range subscription.FixedFeeQuantitySchedule {
			if IsCurrentPrice(ff, time.Now()) {
				itemId, err := c.getItemId(ctx, ff.PriceID)
				if err != nil {
					return nil, err
				}

				if itemId == seatsPriceID {
					currentFixedQuantities.SeatsID = ff.PriceID
					currentFixedQuantities.Seats = int(ff.Quantity)
				} else if itemId == includedMessagesPriceID {
					currentFixedQuantities.IncludedMessagesID = ff.PriceID
					currentFixedQuantities.IncludedMessages = int(ff.Quantity)
				}
			}
			if IsNextBillingCyclePrice(ff, orbSubscriptionInfo.CurrentBillingPeriodEndDate) {
				hasFuture = true
				itemId, err := c.getItemId(ctx, ff.PriceID)
				if err != nil {
					return nil, err
				}

				if itemId == seatsPriceID {
					futureFixedQuantities.SeatsID = ff.PriceID
					futureFixedQuantities.Seats = int(ff.Quantity)
				} else if itemId == includedMessagesPriceID {
					futureFixedQuantities.IncludedMessagesID = ff.PriceID
					futureFixedQuantities.IncludedMessages = int(ff.Quantity)
				}
			}
		}

		orbSubscriptionInfo.CurrentFixedQuantities = currentFixedQuantities

		// Set the future fixed quantities only if one exists, otherwise keep it as nil
		if hasFuture {
			orbSubscriptionInfo.FutureFixedQuantities = futureFixedQuantities
		}
	}
	// Extract next billing cycle amount
	invoice, err := c.client.Invoices.FetchUpcoming(
		ctx,
		orb.InvoiceFetchUpcomingParams{
			SubscriptionID: orb.F(orbSubscriptionId),
		},
	)
	if err != nil {
		// No upcoming invoice, but we can still return the rest of the things
		// This case happens when the subscription is cancelled
		log.Warn().Err(err).Msgf("Failed to get upcoming invoice")
		return orbSubscriptionInfo, nil
	}
	orbSubscriptionInfo.NextBillingCycleAmount = invoice.AmountDue
	return orbSubscriptionInfo, nil
}

func IsCurrentPrice(price orb.SubscriptionFixedFeeQuantitySchedule, now time.Time) bool {
	startDate := price.StartDate
	var endDate time.Time
	if !price.EndDate.IsZero() {
		endDate = price.EndDate
	} else {
		// Default to 1 year from now if end_date is nil
		endDate = now.AddDate(1, 0, 0)
	}
	return now.After(startDate) && now.Before(endDate)
}

func IsNextBillingCyclePrice(ff orb.SubscriptionFixedFeeQuantitySchedule, billingPeriodEnd time.Time) bool {
	// need the price start date to be after or equal to the current billing period end date (it is for next billing cycle)
	// need the price start date to be before the current billing period end date + 1 month (so that is is for the next cycle, and not any later)
	return (ff.StartDate.After(billingPeriodEnd) || ff.StartDate.Equal(billingPeriodEnd)) && ff.StartDate.Before(billingPeriodEnd.AddDate(0, 1, 0))
}

func (c *OrbClientImpl) getItemId(ctx context.Context, priceID string) (string, error) {
	price, err := c.getPrice(ctx, priceID)
	if err != nil {
		return "", err
	}

	item, ok := price.Item.(orb.PriceUnitPriceItem)
	if !ok {
		return "", fmt.Errorf("priceID %s is not a unit price", priceID)
	}
	return item.ID, nil
}

func (c *OrbClientImpl) getPrice(ctx context.Context, orbPriceID string) (*orb.Price, error) {
	if price, ok := c.priceCache.Get(orbPriceID); ok {
		return price, nil
	}

	price, err := c.client.Prices.Fetch(ctx, orbPriceID)
	if err != nil {
		return nil, err
	}

	c.priceCache.Add(orbPriceID, price)
	return price, nil
}

func (c *OrbClientImpl) SetCustomerPlanType(ctx context.Context, planChange OrbPlanChange, idempotencyKey *string) error {
	// Now, update the subscription to the new plan using the subscription ID from the planChange
	params := orb.SubscriptionSchedulePlanChangeParams{
		ExternalPlanID: orb.F(planChange.NewPlanID),
	}

	if planChange.BillingCycleAlignment == BillingCycleAlignmentPlanChangeDate {
		params.BillingCycleAlignment = orb.F(orb.SubscriptionSchedulePlanChangeParamsBillingCycleAlignmentPlanChangeDate)
	} else if planChange.BillingCycleAlignment == BillingCycleAlignmentUnchanged {
		params.BillingCycleAlignment = orb.F(orb.SubscriptionSchedulePlanChangeParamsBillingCycleAlignmentUnchanged)
	} else {
		return fmt.Errorf("invalid billingCycleAlignment provided: %v", planChange.BillingCycleAlignment)
	}

	// ChangeOption can be immediate, end_of_subscription_term, or requested_date.
	if planChange.PlanChangeType == PlanChangeEndOfTerm {
		// Change at end of term
		params.ChangeOption = orb.F(orb.SubscriptionSchedulePlanChangeParamsChangeOptionEndOfSubscriptionTerm)
	} else if planChange.PlanChangeType == PlanChangeSpecificDate {
		// Change on a specific date
		if planChange.PlanChangeDate == nil {
			return fmt.Errorf("planChangeDate must be provided when planChangeType is PlanChangeSpecificDate")
		}
		params.ChangeDate = orb.F(*planChange.PlanChangeDate)
		params.ChangeOption = orb.F(orb.SubscriptionSchedulePlanChangeParamsChangeOptionRequestedDate)
	} else {
		// Change immediately
		params.ChangeOption = orb.F(orb.SubscriptionSchedulePlanChangeParamsChangeOptionImmediate)
	}

	if len(planChange.PriceOverrides) > 0 {
		overrides := []orb.SubscriptionSchedulePlanChangeParamsReplacePrice{}
		for _, override := range planChange.PriceOverrides {
			overrides = append(overrides, orb.SubscriptionSchedulePlanChangeParamsReplacePrice{
				ReplacesPriceID:    orb.F(override.PriceID),
				FixedPriceQuantity: orb.F(override.Quantity),
			})
		}
		params.ReplacePrices = orb.F(overrides)
	}

	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	_, err := c.client.Subscriptions.SchedulePlanChange(
		ctx,
		planChange.SubscriptionID,
		params,
		options...,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to set customer plan type")
		return fmt.Errorf("failed to set customer plan type: %w", err)
	}
	log.Info().Msg("Successfully set customer plan type")
	return nil
}

// IngestEvents sends multiple events to Orb's ingest API
// See: https://docs.withorb.com/api-reference/event/ingest-events
func (c *OrbClientImpl) IngestEvents(ctx context.Context, events []*OrbEvent) error {
	if len(events) == 0 {
		return nil
	}

	// Convert our OrbEvents to the Orb client library's event format
	orbEvents := make([]orb.EventIngestParamsEvent, len(events))
	for i, event := range events {
		orbEvents[i] = orb.EventIngestParamsEvent{
			EventName:      orb.F(event.EventName),
			Timestamp:      orb.F(event.Timestamp),
			IdempotencyKey: orb.F(event.IdempotencyKey),
			Properties:     orb.F(interface{}(event.Properties)),
		}
		if event.CustomerOrbID != nil {
			orbEvents[i].CustomerID = orb.F(*event.CustomerOrbID)
		} else if event.ExternalCustomerID != nil {
			orbEvents[i].ExternalCustomerID = orb.F(*event.ExternalCustomerID)
		} else {
			return fmt.Errorf("one of CustomerOrbID or ExternalCustomerID must be set")
		}
	}

	params := orb.EventIngestParams{
		Events: orb.F(orbEvents),
	}

	// Send the events
	_, err := c.client.Events.Ingest(ctx, params)
	if err != nil {
		log.Error().Err(err).Msg("Failed to ingest events to Orb")
		return fmt.Errorf("failed to ingest events to Orb: %w", err)
	}

	log.Info().
		Int("event_count", len(events)).
		Msg("Successfully ingested events to Orb")
	return nil
}

// VerifyWebhookSignature verifies that a webhook request came from Orb and returns the raw body.
// If verification fails, it returns an error.
func (c *OrbClientImpl) VerifyWebhookSignature(r *http.Request) ([]byte, error) {
	// Check if webhook secret is set
	if c.webhookSecret == nil { // pragma: allowlist secret
		return nil, fmt.Errorf("webhook secret not configured in client")
	}

	if r.Method != http.MethodPost {
		return nil, fmt.Errorf("method not allowed")
	}

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading request body: %w", err)
	}
	defer r.Body.Close()

	now := time.Now()

	// Create a new webhook service
	webhooks := orb.NewWebhookService()

	// Verify the signature using the client's webhook secret
	err = webhooks.VerifySignature(body, r.Header, *c.webhookSecret, now)
	if err != nil {
		return nil, fmt.Errorf("invalid signature: %w", err)
	}

	return body, nil
}

func (c *OrbClientImpl) GetCustomerCreditBalance(ctx context.Context, customerOrbId string, currency string) (float64, error) {
	allCreditBlocks, err := GetAllCreditBlocks(ctx, c.client, customerOrbId, currency)
	if err != nil {
		return 0, err
	}

	// Sum up the credit blocks totals
	totalCreditBlocks := 0.0
	for _, creditBlock := range allCreditBlocks {
		log.Info().Str("customer_orb_id", customerOrbId).Float64("balance", creditBlock.Balance).Str("status", string(creditBlock.Status)).Msg("Credit block balance")
		totalCreditBlocks += creditBlock.Balance
	}
	return totalCreditBlocks, nil
}

func GetAllCreditBlocks(ctx context.Context, client *orb.Client, customerOrbId string, currency string) ([]orb.CustomerCreditListResponse, error) {
	var allCreditBlocks []orb.CustomerCreditListResponse
	var nextCursor string

	for {
		params := orb.CustomerCreditListParams{
			IncludeAllBlocks: orb.F(false), // only include active blocks
			Currency:         orb.F(currency),
		}

		if nextCursor != "" {
			params.Cursor = orb.F(nextCursor)
		}

		creditBlocks, err := client.Customers.Credits.List(ctx,
			customerOrbId,
			params)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get customer's credit blocks")
			return nil, fmt.Errorf("failed to get customer's credit balance: %w", err)
		}

		allCreditBlocks = append(allCreditBlocks, creditBlocks.Data...)

		if !creditBlocks.PaginationMetadata.HasMore {
			break
		}
		nextCursor = creditBlocks.PaginationMetadata.NextCursor
	}
	return allCreditBlocks, nil
}

func (c *OrbClientImpl) PurchaseCredits(ctx context.Context, creditPurchase OrbCreditPurchase, idempotencyKey *string) error {
	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	params := orb.CustomerCreditLedgerNewEntryParamsAddIncrementCreditLedgerEntryRequestParams{
		EntryType:        orb.F(orb.CustomerCreditLedgerNewEntryParamsAddIncrementCreditLedgerEntryRequestParamsEntryTypeIncrement),
		Amount:           orb.F(creditPurchase.NumberCredits),
		PerUnitCostBasis: orb.F(fmt.Sprintf("%f", creditPurchase.CostBasis)),
		Description:      orb.F(creditPurchase.Description),
		InvoiceSettings: orb.F(orb.CustomerCreditLedgerNewEntryParamsAddIncrementCreditLedgerEntryRequestParamsInvoiceSettings{
			AutoCollection:           orb.F(true),     // they have to pay immediately
			NetTerms:                 orb.F(int64(0)), // due on invoice date
			RequireSuccessfulPayment: orb.F(true),     // payment must be successful before can use credits
		}),
		Currency:   orb.F(creditPurchase.Currency),
		ExpiryDate: orb.F(creditPurchase.ExpiryDate),
	}

	if creditPurchase.StartDate != nil {
		params.EffectiveDate = orb.F(*creditPurchase.StartDate)
	}

	_, err := c.client.Customers.Credits.Ledger.NewEntry(
		ctx,
		creditPurchase.CustomerOrbID,
		params,
		options...,
	)
	return err
}

func (c *OrbClientImpl) CancelOrbSubscription(ctx context.Context, orbSubscriptionId string, cancelTime PlanChangeType, cancelDate *time.Time, idempotencyKey *string) error {
	// Check to make sure the subscription is not already scheduled to be cancelled. Otherwise, cancel will throw an error
	sub, err := c.client.Subscriptions.Fetch(ctx, orbSubscriptionId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get Orb subscription")
		return err
	}
	if !sub.EndDate.IsZero() {
		log.Info().Str("subscription_id", orbSubscriptionId).Msg("Subscription already canceled")
		return nil
	}

	var params orb.SubscriptionCancelParams
	if cancelTime == PlanChangeImmediate {
		params.CancelOption = orb.F(orb.SubscriptionCancelParamsCancelOptionImmediate)
	} else if cancelTime == PlanChangeEndOfTerm {
		params.CancelOption = orb.F(orb.SubscriptionCancelParamsCancelOptionEndOfSubscriptionTerm)
	} else if cancelTime == PlanChangeSpecificDate {
		if cancelDate == nil {
			return fmt.Errorf("cancelDate must be provided when planChangeType is PlanChangeSpecificDate")
		}
		params.CancelOption = orb.F(orb.SubscriptionCancelParamsCancelOptionRequestedDate)
		params.CancellationDate = orb.F(*cancelDate)
	} else {
		return fmt.Errorf("invalid cancelTime provided: %v", cancelTime)
	}

	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	_, err = c.client.Subscriptions.Cancel(ctx, orbSubscriptionId, params, options...)
	if err != nil {
		log.Error().Err(err).Msg("Failed to cancel Orb subscription")
		return err
	}
	log.Info().Str("subscription_id", orbSubscriptionId).Msg("Successfully canceled Orb subscription")
	return nil
}

func (c *OrbClientImpl) UpdateFixedQuantity(ctx context.Context, quantityUpdate OrbQuantityUpdate, idempotencyKey *string) error {
	params := orb.SubscriptionUpdateFixedFeeQuantityParams{
		PriceID:  orb.F(quantityUpdate.PriceOverride.PriceID),
		Quantity: orb.F(quantityUpdate.PriceOverride.Quantity),
	}

	if quantityUpdate.UpdateTimeType == PlanChangeImmediate {
		params.ChangeOption = orb.F(orb.SubscriptionUpdateFixedFeeQuantityParamsChangeOptionImmediate)
	} else if quantityUpdate.UpdateTimeType == PlanChangeEndOfTerm {
		params.ChangeOption = orb.F(orb.SubscriptionUpdateFixedFeeQuantityParamsChangeOptionUpcomingInvoice)
	} else if quantityUpdate.UpdateTimeType == PlanChangeSpecificDate {
		if quantityUpdate.UpdateChangeDate == nil {
			return fmt.Errorf("UpdateChangeDate must be provided when planChangeType is PlanChangeSpecificDate")
		}
		params.ChangeOption = orb.F(orb.SubscriptionUpdateFixedFeeQuantityParamsChangeOptionEffectiveDate)
		params.EffectiveDate = orb.F(*quantityUpdate.UpdateChangeDate)
	}

	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	_, err := c.client.Subscriptions.UpdateFixedFeeQuantity(ctx, quantityUpdate.OrbSubscriptionID, params, options...)
	if err != nil {
		log.Error().Err(err).Msg("Failed to update the ")
		return err
	}
	return nil
}

func (c *OrbClientImpl) GetCustomerCreditInfo(ctx context.Context, customerOrbId string, billingPeriodStartDate time.Time, currency string) (*CustomerCreditInfo, error) {
	// Get all active credit blocks
	activeCreditBlocks, err := GetAllCreditBlocks(ctx, c.client, customerOrbId, currency)
	if err != nil {
		return nil, err
	}

	// Sum up the active and pending payment credit blocks totals
	activeCredits := 0.0
	pendingCredits := 0.0
	for _, creditBlock := range activeCreditBlocks {
		if creditBlock.Status == orb.CustomerCreditListResponseStatusPendingPayment {
			pendingCredits += creditBlock.Balance
		} else {
			activeCredits += creditBlock.Balance
		}
	}

	// Get all decrements from the credit ledger that were created on or after the billing period start date
	ledgerEntries := []orb.CustomerCreditLedgerListResponse{}
	var nextCursor string
	for {
		params := orb.CustomerCreditLedgerListParams{
			EntryType:    orb.F(orb.CustomerCreditLedgerListParamsEntryTypeDecrement),
			CreatedAtGte: orb.F(billingPeriodStartDate),
			Currency:     orb.F(currency),
			Limit:        orb.F(int64(100)),
		}

		if nextCursor != "" {
			params.Cursor = orb.F(nextCursor)
		}

		entries, err := c.client.Customers.Credits.Ledger.List(ctx, customerOrbId, params)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get customer's credit ledger entries")
			return nil, fmt.Errorf("failed to get customer's credit ledger entries: %w", err)
		}

		ledgerEntries = append(ledgerEntries, entries.Data...)

		if !entries.PaginationMetadata.HasMore {
			break
		}
		nextCursor = entries.PaginationMetadata.NextCursor
	}
	// Sum up the decrements
	creditsUsedThisBillingCycle := 0.0
	for _, entry := range ledgerEntries {
		creditsUsedThisBillingCycle += math.Abs(entry.Amount)
	}

	// Credits renewing next billing cycle: credits per seat * number of seats
	return &CustomerCreditInfo{
		ActiveCredits:               activeCredits,
		PendingCredits:              pendingCredits,
		CreditsUsedThisBillingCycle: creditsUsedThisBillingCycle,
	}, nil
}

// Gets the plan information for a specific subscription (with a specific version) or a generic plan
// Must pass in EITHER orbSubscriptionID or externalPlanID
func (c *OrbClientImpl) GetPlanInformation(ctx context.Context, itemIDs ItemIds, orbSubscriptionID *string, externalPlanID *string) (*OrbPlanInfo, error) {
	if orbSubscriptionID == nil && externalPlanID == nil {
		return nil, fmt.Errorf("must pass in either orbSubscriptionID or externalPlanID")
	}
	var plan orb.Plan
	if orbSubscriptionID != nil {
		sub, err := c.client.Subscriptions.Fetch(ctx, *orbSubscriptionID)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get Orb subscription")
			return nil, err
		}
		plan = sub.Plan
	} else if externalPlanID != nil {
		p, err := c.client.Plans.ExternalPlanID.Fetch(ctx, *externalPlanID)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get Orb plan")
			return nil, err
		}
		if p == nil {
			return nil, fmt.Errorf("plan not found")
		}
		plan = *p
	}

	// Get the plan information
	resp := &OrbPlanInfo{}

	resp.ExternalPlanID = plan.ExternalPlanID
	resp.Name = plan.Name
	prices := plan.Prices
	for _, p := range prices {
		if p.Item.(orb.PriceUnitPriceItem).ID == itemIDs.SeatsID {
			if p.ModelType == orb.PriceModelTypeUnit {
				resp.PricePerSeat = p.UnitConfig.(orb.PriceUnitPriceUnitConfig).UnitAmount
			}
			resp.SeatsPriceID = p.ID
		} else if p.Item.(orb.PriceUnitPriceItem).ID == itemIDs.IncludedMessagesID {
			resp.MessagesPerSeat = p.FixedPriceQuantity
			resp.IncludedMessagesPriceID = p.ID
		}
	}

	return resp, nil
}

func (c *OrbClientImpl) UnschedulePendingSubscriptionCancellation(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error {
	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	_, err := c.client.Subscriptions.UnscheduleCancellation(ctx, orbSubscriptionId, options...)
	return err
}

func (c *OrbClientImpl) UnscheduleFixedQuantity(ctx context.Context, orbSubscriptionID string, priceID string, idempotencyKey *string) error {
	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}

	_, err := c.client.Subscriptions.UnscheduleFixedFeeQuantityUpdates(ctx, orbSubscriptionID, orb.SubscriptionUnscheduleFixedFeeQuantityUpdatesParams{
		PriceID: orb.F(priceID),
	}, options...)
	if err != nil {
		// Allow 400 errors, means nothing was scheduled
		if orbErr, ok := err.(*orb.Error); ok {
			if orbErr.Type == orb.ErrorTypeRequestValidationError {
				log.Warn().Err(err).Msg("Failed to unschedule fixed quantity, because there is none to be unscheduled")
				return nil
			}
		}
		log.Error().Err(err).Msg("Failed to unschedule fixed quantity")
		return err
	}
	return nil
}

func (c *OrbClientImpl) GetFailedPaymentInfo(ctx context.Context, orbSubscriptionId string) (*FailedPaymentInfo, error) {
	// Get the most recent subscription invoice
	invoices, err := c.client.Invoices.List(ctx, orb.InvoiceListParams{
		SubscriptionID: orb.F(orbSubscriptionId),
		AmountGt:       orb.F("0"),
		Limit:          orb.F(int64(1)),
		IsRecurring:    orb.F(true),
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to get Orb invoices")
		return nil, err
	}

	// If there are no failed invoices, return nil
	if len(invoices.Data) == 0 {
		return nil, nil
	}

	invoice := invoices.Data[0]

	// If most recent invoice is already paid, return nil
	if invoice.Status != orb.InvoiceStatusIssued {
		return nil, nil
	}

	// If the payment has never failed, return nil
	if invoice.PaymentFailedAt.IsZero() {
		return nil, nil
	}

	return &FailedPaymentInfo{
		Amount: invoice.AmountDue,
		Date:   invoice.PaymentFailedAt,
	}, nil
}

// GetInvoice fetches an invoice from Orb by its ID
func (c *OrbClientImpl) GetInvoice(ctx context.Context, invoiceId string) (*OrbInvoice, error) {
	invoice, err := c.client.Invoices.Fetch(ctx, invoiceId)
	if err != nil {
		log.Error().Err(err).Str("invoice_id", invoiceId).Msg("Failed to fetch invoice from Orb")
		return nil, fmt.Errorf("failed to fetch invoice from Orb: %w", err)
	}

	// Extract payment attempts count from auto_collection
	var paymentAttempts int32 = 0
	if invoice.AutoCollection.NumAttempts > 0 {
		paymentAttempts = int32(invoice.AutoCollection.NumAttempts)
	}

	// Create our OrbInvoice struct with the data we need
	orbInvoice := &OrbInvoice{
		ID:              invoice.ID,
		SubscriptionID:  invoice.Subscription.ID,
		CustomerID:      invoice.Customer.ID,
		Status:          string(invoice.Status),
		PaymentAttempts: paymentAttempts,
		Source:          string(invoice.InvoiceSource),
	}

	log.Info().
		Str("invoice_id", invoiceId).
		Str("subscription_id", orbInvoice.SubscriptionID).
		Int32("payment_attempts", orbInvoice.PaymentAttempts).
		Msg("Successfully fetched invoice from Orb")

	return orbInvoice, nil
}

func (c *OrbClientImpl) UpdateCustomerBillingAddress(ctx context.Context, customerOrbID string, address *Address) error {
	_, err := c.client.Customers.Update(ctx, customerOrbID, orb.CustomerUpdateParams{
		BillingAddress: orb.F(orb.CustomerUpdateParamsBillingAddress{
			Line1:      orb.F(address.Line1),
			Line2:      orb.F(address.Line2),
			City:       orb.F(address.City),
			State:      orb.F(address.State),
			PostalCode: orb.F(address.PostalCode),
			Country:    orb.F(address.Country),
		}),
	})
	if err != nil {
		// Did some testing to ensure that the error message shouldn't contain the address
		log.Error().Err(err).Msg("Failed to update customer billing address")
		return fmt.Errorf("failed to update customer billing address")
	}
	log.Info().Msg("Successfully updated customer billing address")
	return nil
}

// GetScheduledPlanChanges fetches scheduled plan changes for a subscription and returns the target plan ID if any
func (c *OrbClientImpl) GetScheduledPlanChanges(ctx context.Context, orbSubscriptionId string, orbCustomerId string) (*string, error) {
	now := time.Now()
	schedule, err := c.client.Subscriptions.FetchSchedule(
		ctx,
		orbSubscriptionId,
		orb.SubscriptionFetchScheduleParams{
			StartDateGt: orb.F(now),
		},
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get subscription schedule")
		return nil, fmt.Errorf("failed to get subscription schedule: %w", err)
	}
	// Filter out canceled plan changes (same start and end date)
	var filteredSchedule []orb.SubscriptionFetchScheduleResponse
	for _, s := range schedule.Data {
		if !s.StartDate.Equal(s.EndDate) {
			filteredSchedule = append(filteredSchedule, s)
		}
	}
	if len(filteredSchedule) > 0 {
		firstSchedule := filteredSchedule[0]
		targetPlanID := firstSchedule.Plan.ExternalPlanID
		log.Info().
			Str("target_plan_id", targetPlanID).
			Str("customer_id", orbCustomerId).
			Interface("schedule", filteredSchedule).
			Msg("Found scheduled plan change")
		if len(filteredSchedule) > 1 {
			log.Warn().
				Str("customer_id", orbCustomerId).
				Interface("schedule", filteredSchedule).
				Msg("Multiple scheduled plan changes found, using first one")
		}
		return &targetPlanID, nil
	}
	return nil, nil
}

func (c *OrbClientImpl) UnschedulePlanChanges(ctx context.Context, orbSubscriptionId string, idempotencyKey *string) error {
	options := []option.RequestOption{}
	if idempotencyKey != nil {
		options = append(options, option.WithHeader("Idempotency-Key", *idempotencyKey))
	}
	_, err := c.client.Subscriptions.UnschedulePendingPlanChanges(ctx, orbSubscriptionId, options...)
	if err != nil {
		log.Error().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to unschedule pending plan changes")
		return fmt.Errorf("failed to unschedule pending plan changes: %w", err)
	}
	log.Info().Str("subscription_id", orbSubscriptionId).Msg("Successfully unscheduled pending plan changes")
	return nil
}

load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "orb_lib",
    srcs = [
        "metrics_interceptor.go",
        "mock_orb_client.go",
        "orb.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/orb",
    visibility = ["//visibility:public"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "@com_github_hashicorp_golang_lru_v2//simplelru",
        "@com_github_orbcorp_orb_go//:orb-go",
        "@com_github_orbcorp_orb_go//option",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//mock",
    ],
)

go_test(
    name = "orb_test",
    srcs = ["metrics_interceptor_test.go"],
    embed = [":orb_lib"],
    deps = [
        "@com_github_orbcorp_orb_go//:orb-go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/testutil",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
    ],
)

go_test(
    name = "orb_manual_test",
    srcs = ["orb_manual_test.go"],
    args = ["-test.v"],  #verbose test output
    embed = [":orb_lib"],
    tags = ["manual"],
    deps = [],
)

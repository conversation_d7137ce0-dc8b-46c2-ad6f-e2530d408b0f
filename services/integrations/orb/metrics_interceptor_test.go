package orb

import (
	"context"
	"testing"
	"time"

	"github.com/orbcorp/orb-go"
	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
)

func TestMetricsInterceptor(t *testing.T) {
	// Create a mock client
	mockClient := new(MockOrbClient)

	// Wrap it with the metrics interceptor
	client := NewMetricsInterceptor(mockClient)

	ctx := context.Background()

	// Test CreateCustomer
	t.Run("CreateCustomer metrics", func(t *testing.T) {
		customer := OrbCustomer{
			Email: "<EMAIL>",
			Name:  "Test Customer",
		}

		mockClient.On("CreateCustomer", ctx, customer, false, (*string)(nil)).Return("customer-123", nil).Once()

		// Reset metrics
		orbAPIOperations.Reset()

		// Call the method
		id, err := client.CreateCustomer(ctx, customer, false, nil)

		assert.NoError(t, err)
		assert.Equal(t, "customer-123", id)

		// Check metrics were recorded
		assert.Equal(t, float64(1), testutil.ToFloat64(orbAPIOperations.WithLabelValues("create_customer", "200 OK")))

		mockClient.AssertExpectations(t)
	})

	// Test IngestEvents
	t.Run("IngestEvents metrics", func(t *testing.T) {
		events := []*OrbEvent{
			{
				EventName:      "test_event",
				CustomerOrbID:  stringPtr("customer-123"),
				IdempotencyKey: "key-123",
			},
		}

		mockClient.On("IngestEvents", ctx, events).Return(nil).Once()

		// Reset metrics
		orbAPIOperations.Reset()

		// Call the method
		err := client.IngestEvents(ctx, events)

		assert.NoError(t, err)

		// Check metrics were recorded
		assert.Equal(t, float64(1), testutil.ToFloat64(orbAPIOperations.WithLabelValues("ingest_events", "200 OK")))

		mockClient.AssertExpectations(t)
	})
}

func stringPtr(s string) *string {
	return &s
}

// Test the internal helper functions
func TestMetricsHelpers(t *testing.T) {
	// Test operation categorization
	t.Run("Operation categorization", func(t *testing.T) {
		testCases := []struct {
			operation string
			expected  string
		}{
			{"create_customer", "write"},
			{"update_customer", "write"},
			{"cancel_subscription", "write"},
			{"add_alert", "write"},
			{"set_plan", "write"},
			{"purchase_credits", "credit_ledger"},
			{"ingest_events", "write"},
			{"unschedule_cancellation", "write"},
			{"get_customer", "read"},
			{"list_subscriptions", "read"},
			{"fetch_invoice", "read"},
			{"get_credit_balance", "credit_ledger"},
			{"get_customer_credit_info", "credit_ledger"},
		}

		for _, tc := range testCases {
			t.Run(tc.operation, func(t *testing.T) {
				category := categorizeOperation(tc.operation)
				assert.Equal(t, tc.expected, category)
			})
		}
	})

	// Test error extraction
	t.Run("Error extraction", func(t *testing.T) {
		testCases := []struct {
			name     string
			err      error
			expected string
		}{
			{"nil error", nil, "200 OK"},
			{"orb error with status", &orb.Error{StatusCode: 404}, "404"},
			{"orb error rate limit", &orb.Error{StatusCode: 429}, "429"},
			{"generic error", assert.AnError, "unknown"},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				status := extractError(tc.err)
				assert.Equal(t, tc.expected, status)
			})
		}
	})

	// Test rate limit detection
	t.Run("Rate limit metrics", func(t *testing.T) {
		// Reset metrics
		orbAPIRateLimitEvents.Reset()
		orbAPIRemainingQuota.Reset()

		// Create a rate limit error
		rateLimitErr := &orb.Error{
			StatusCode: 429,
		}

		// Call updateMetrics with rate limit error
		startTime := time.Now()
		updateMetrics("ingest_events", startTime, rateLimitErr)

		// Check that rate limit was detected and categorized correctly
		assert.Equal(t, float64(1), testutil.ToFloat64(orbAPIRateLimitEvents.WithLabelValues("write")))
		assert.Equal(t, float64(0), testutil.ToFloat64(orbAPIRemainingQuota.WithLabelValues("write")))
	})
}

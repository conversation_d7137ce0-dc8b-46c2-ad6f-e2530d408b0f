"""Tool for querying Linear tickets using natural language."""

import logging

import grpc
import requests
import structlog
from google.rpc import status_pb2 as status_pb2
from jinja2 import Template
from pydantic import BaseModel, Field, SecretStr, ValidationError

from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelClient,
)
from services.agents import agents_pb2
from services.agents.tool import (
    BaseExtraToolInput,
    EmptyExtraToolInput,
    ToolAuthenticationError,
    ValidatedTool,
)
from services.integrations.linear import linear_pb2
from services.integrations.linear.client.client import LinearClient
from services.integrations.linear.gql_utils import is_query_read_only
from services.lib.request_context.request_context import RequestContext

log = structlog.get_logger()
# Set gql logging to WARNING level to reduce verbosity
logging.getLogger("gql").setLevel(logging.WARNING)


class LinearSearchIssuesToolInput(BaseModel):
    """Input schema for the Linear tool."""

    query: str = Field(
        description=("A natural language read-only query for Linear tickets."),
    )


class LinearExtraToolInput(BaseModel, BaseExtraToolInput):
    """Configuration for the Linear passed as extra input when making tool calls"""

    api_token: SecretStr
    """The API token for Linear access"""


QUERY_GENERATION_PROMPT = Template(
    """\
You are an expert at converting natural language commands about Linear objects into GraphQL queries.
The available fields in the GraphQL schema are:

Filter examples:
- Filter by assignee: assignee: { email: { eq: "{{ user_email }}" } }
- Filter by title: title: { contains: "search term" }
- Filter by state: state: { type: { in: ["started", "unstarted"] } }

Sorting:
- Sort by update time: orderBy: updatedAt
- Sort by creation time: orderBy: createdAt

Pagination:
Always use pagination for list responses. Linear's API uses cursor-based pagination with the following parameters:
- first/after: Get N items after a cursor
- last/before: Get N items before a cursor
- To get page info, include pageInfo { hasNextPage, endCursor }
- Use nodes shorthand syntax for connection types
- If you don't specify query arguments, the first 50 results are returned

Referencing objects:
When you reference objects such as projects, labels, assignees, \
etc, the corresponding id fields are always UUIDs.

Example query to read a ticket's information and comments based on its ID (when
querying for a single ticket always include all fields):

    query {
        issue(id: "RU-6316") {
            id
            identifier  # Always include this field for ticket queries
            title
            description
            state {
                name
            }
            assignee {
                name
                email
            }
            labels {
                nodes {
                    name
                }
            }
            team {
                name
            }
            createdAt
            updatedAt
            url
            comments {  # NOTE: Comments are returned in reverse chronological order
                nodes {
                    body
                    user {
                        name
                        email
                    }
                    createdAt
                    editedAt
                }
            }
        }
    }

Example query to find tickets containing "database" in their title:

    query {
        issues(
            first: 20,  # Get first 20 results
            after: "cursor-hash",  # Optional: Get results after this cursor
            filter: { title: { containsIgnoreCase: "database" } }
        ) {
            nodes {
                id
                identifier  # Always include this field for ticket queries
                title
                state {
                    name
                }
                assignee {
                    name
                }
                createdAt
                priority
                url
            }
            # Include pagination info to fetch next page
            pageInfo {
                hasNextPage
                endCursor  # Use this cursor value as 'after' parameter to fetch next page
            }
        }
    }

Example query to find a user's 10 most recently assigned tickets:

    query {
        issues(
            first: 10,
            orderBy: updatedAt,
            filter: {
                assignee: { email: { eq: "<EMAIL>" } }
            }
        ) {
            nodes {
                id
                identifier  # Always include this field for ticket queries
                title
                state {
                    name
                }
                createdAt
                updatedAt
                priority
                team {
                    name
                }
                labels {
                    nodes {
                        name
                    }
                }
                url
            }
            pageInfo {
                hasNextPage
                endCursor  # Use this cursor value as 'after' parameter to fetch next page
            }
        }
    }

Example query to fuzzily search for the first 10 issues related to "Linear OAuth integration":

    query {
        searchIssues(term: "Linear OAuth integration", first: 10) {
            nodes {
                id
                identifier  # Always include this field for ticket queries
                title
                description
                state {
                    name
                }
                assignee {
                    name
                }
                createdAt
                updatedAt
            }
            pageInfo {
                hasNextPage
                endCursor  # Use this cursor value as 'after' parameter to fetch next page
            }
        }
    }

Example mutation query to create a new issue:

    mutation {
        issueCreate(
            input: {
                title: "New exception"
                description: "More detailed error report in markdown"
                teamId: "9880875c-218f-43a8-9a7d-87852647a033"  # Required. This is just an example team ID.
            }
        ) {
            success
            issue {
                id
                identifier
                title
                url
            }
        }
    }

Example mutation query to comment on an issue:

    mutation {
        commentCreate(
            input: {
            body: "This is a comment on the issue"
            issueId: "TST-123"  # Note this can be the ticket's identifier (e.g. TST-123) or its uuid
            }
        ) {
            success
            comment {
                id
                url
            }
        }
    }

To find the name and id of all projecs.  In general when you list objects, \
such as projects, teams, issueLabels, etc, \
extract their ids so that future queries can reference them.

    query {
        projects {
            nodes {
                name
                id
            }
        }
    }

Find issues assigned to me

    query {
        viewer {
            assignedIssues(first: 5) {
                nodes {
                    id
                    identifier
                    title
                    state {
                        name
                    }
                    createdAt
                    updatedAt
                    priority
                    team {
                        name
                        id
                    }
                    labels {
                        nodes {
                            name
                            id
                        }
                    }
                    url
                }
                pageInfo {
                    hasNextPage
                    endCursor
                }
            }
        }
    }

Example query to get the branch template for a ticket:

    query {
        issue(id: "AU-123") {
            id
            identifier
            branchName
        }
    }

Update issue properties

    mutation IssueUpdate(
        $id: String!,
        $title: String,
        $description: String,
        $assigneeId: String,
        $labelIds: [String!],
        $priority: Int,
        $estimate: Int,
        $stateId: String,
        $dueDate: DateTime
    ) {
        issueUpdate(
            id: $id,
            input: {
            title: $title,
            description: $description,
            assigneeId: $assigneeId,
            labelIds: $labelIds,
            priority: $priority,
            estimate: $estimate,
            stateId: $stateId,
            dueDate: $dueDate
            }
        ) {
            success
            issue {
                id
                identifier
                title
                description
                priority
                estimate
                dueDate
                assignee {
                    id
                    name
                    email
                }
                labels {
                    nodes {
                        id
                        name
                    }
                }
                state {
                    id
                    name
                    type
                }
            }
        }
    }

Create a label

mutation {
  issueLabelCreate(input: {
    name: "Urgent",
    color: "#FF0000",
    teamId: "12345678-1234-1234-1234-123456789abc"
  }) {
    success
    issueLabel {
      id
      name
      color
      team {
        id
        name
      }
    }
  }
}

DO use IDs to identify entities, such as labelId, teamId, membmerIds, leadId, etc.

DO NOT use strings to reference entities such as label, team, member. NEVER DO THIS, IT WILL FAIL!

Now for the task at hand.

{%- if user_email or user_id %}
The user's information:
{%- if user_email %}
- Email: {{ user_email }}
{%- endif %}
{%- if user_id %}
- ID: {{ user_id }}
{%- endif %}
References to "me" or "my" in the query should be resolved using this information.
{%- endif %}
{%- if teams %}

The user is on the following Linear teams:
{%- for team in teams %}
- {{ team.name }}{% if team.id %} (id: {{ team.id }}){% endif %}{% if team.description %}: {{ team.description }}{% endif %}
{%- endfor %}
{%- endif %}

Convert this natural language query into a GraphQL query:
{{ query }}

Return only the GraphQL query, no other text. The query should be wrapped in a 'query' operation,
like in the examples above. Remember to always include the following fields in your queries:
- id
- identifier
- title
- state { name }
"""
)


def _generate_graphql_query(
    llm_client: ThirdPartyModelClient,
    natural_query: str,
    *,
    user_email: str | None = None,
    user_id: str | None = None,
    teams: list[linear_pb2.LinearTeam] | None = None,
) -> str:
    """Generate a GraphQL query using the language model.

    Args:
        llm_client: VertexAI client for query generation
        natural_query: Natural language query
        user_email: User's email, used in queries for the user's tickets
        user_id: User's ID, used in queries for the user's tickets
        teams: List of Linear teams the user is on

    Returns:
        GraphQL query string
    """

    prompt = QUERY_GENERATION_PROMPT.render(
        query=natural_query,
        user_email=user_email,
        user_id=user_id,
        teams=teams,
    )

    response = llm_client.generate_response_stream(
        model_caller="linear-search-issues",
        messages=[],
        system_prompt="You are an expert at converting natural language queries into GraphQL queries for Linear's API.",
        cur_message=prompt,
        # Don't accidentally make use of higher max output token limits of newly deployed models
        max_output_tokens=8000,
    )

    # Get the first response text
    msg_pieces = []
    for msg in response:
        msg_pieces.append(msg.text)

    return "".join(msg_pieces)


class LinearToolInput(BaseModel):
    """Input schema for the Linear API tool."""

    summary: str = Field(
        description="One sentence description of the intent of this tool call.  Do not include syntax or details in this field."
    )

    query: str = Field(
        description=(
            """Natural language query for Linear API.

Simply describe what you want to get or update in the Linear API in plain language.

You must reference known existing entities such as projects, issueLabels, assignees, lead, members, etc, by their UUIDs.
If you do not know their UUID, use a separate query tool call to find them before you use the tool.

You need to re-supply all UUIDs for every tool call. Repeat them even if you have already provided them in previous calls.
You do NOT need to provide the current user's name, ID or team ID because the tool knows that info.

If you've encountered errors with previous Linear API calls, include details about those errors and what to try differently this time. This helps the tool avoid repeating the same mistakes.

If the tool is having difficulties with linear API, add your instructions in this field too, such as specific values for particular fields etc.

Sample queries:
- "Issue TST-10 information": Finds information about the ticket TST-10.
- "Comment 'hello world' on ticket TST-10": Comments "hello world" on the ticket TST-10.
- "Create a ticket with title {title} and description {markdown formatted description}":
    Creates a new ticket with the given title and description.
- "Update TST-10 to the Done state": Updates the state of TST-10 to be Done.
- "Assign TST-10 to 01234567-89ab-cdef-0123-456789abcdef": Assigns TST-10 to the user with this UUID; the id was obtained or returned in a previous query.
- "Create a ticket with title 'Fix login bug' and assign it to me via UUID. This was called previously with an error that assigneeId must be a UUID.":
    Creates a new ticket and assigns it to a specific user by UUID (not by email, which previously caused an error).
"""
        ),
    )

    is_read_only: bool = Field(
        description=(
            """
            Whether this tool call will require mutating Linear's state. If the query is read-only, set this to true, otherwise set it to false.
            For example, if the query is "Issue TST-10 information", set this to true.
            If the query is "Comment 'hello world' on ticket TST-10" or "Create a ticket ...", set this to false.
            """
        ),
        default=False,
    )


class LinearTool(
    ValidatedTool[LinearToolInput, LinearExtraToolInput | EmptyExtraToolInput]
):
    """Tool for making Linear API GQL Queries Directly"""

    id = agents_pb2.RemoteToolId.LINEAR
    # While some queries are read-only, we can't determine that at the time
    # `check_tool_call_safe` is called (because at that point we only have the raw tool input
    # which is a natural language query, no programmatic way to check if it
    # is read-only).
    tool_safety = agents_pb2.ToolSafety.TOOL_CHECK

    name = "linear"
    description = """\
Make queries against the Linear API using natural language and
return the JSON response (structured data matching query shape).

Use this tool whenever you want to get or update information in Linear.

Note there are other sources of tickets/issues like Jira and Github. If the user
explicitly mentions another source like Jira or Github do NOT use this tool.

The Linear API supports querying, creating, updating, and deleting the following objects:
- tickets (AKA issues)
- comments
- users
- teams

When creating a git branch / PR for a Linear ticket, use the suggested branch name in
the Linear ticket, unless the user specifies otherwise.
"""

    input_model = LinearToolInput

    def __init__(self, llm_client: ThirdPartyModelClient, linear_client: LinearClient):
        """Initialize the Linear tool.

        Args:
            llm_client: Model client used for expanding natural language query into GQL query.
            linear_client: Linear client for calling into the Linear service.
        """
        super().__init__()
        self.llm_client = llm_client
        self.linear_client = linear_client

    def check_validated_input_safe(self, validated_input: LinearToolInput) -> bool:
        # Cop-out solution where we have Claude determine whether its call is a read-only tool call.
        return validated_input.is_read_only

    def run_validated(
        self,
        validated_input: LinearToolInput,
        extra_tool_input: LinearExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Linear search tool.

        Args:
            validated_input: QueryLinearToolInput containing query and user_email
            extra_tool_input: LinearConfig containing the API key
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted search results

        Raises:
            ToolAuthenticationError: If the user is not authenticated with Linear.
            ValueError: When extra tool input is not the right type.
        """
        if not isinstance(extra_tool_input, LinearExtraToolInput | EmptyExtraToolInput):
            log.error(f"Expected LinearExtraToolInput, got {extra_tool_input}")
            raise ValueError(f"Expected LinearExtraToolInput, got {extra_tool_input}")

        credentials = (
            extra_tool_input.api_token.get_secret_value()
            if isinstance(extra_tool_input, LinearExtraToolInput)
            and extra_tool_input.api_token
            else None
        )

        try:
            user_info = self.linear_client.get_user_information(
                request_context,
                credentials=credentials,
            )
        except Exception as e:
            log.error("Failed to get Linear user information", exc_info=e)
            raise RuntimeError("Failed to get Linear user information") from e

        user_email = user_info.email if user_info else None
        user_id = user_info.id if user_info else None
        teams = list(user_info.teams) if user_info else None

        # Generate and execute GraphQL query
        try:
            graphql_query = _generate_graphql_query(
                self.llm_client,
                validated_input.query,
                user_email=user_email,
                user_id=user_id,
                teams=teams,
            )

        except Exception as e:
            log.error("Failed to generate GraphQL query", e, exc_info=True)
            raise RuntimeError(
                "Failed to generate GraphQL query from natural language."
            ) from e

        if validated_input.is_read_only and not is_query_read_only(graphql_query):
            # Sanity check that the final query we actually send to Linear
            # is in fact read-only if Claude tells us it is. This should
            # never happen assuming Claude is well-behaved, but better to
            # not automatically run mutation operations if the user doesn't
            # expect to.
            raise ValidationError(
                "query was not translated into a read-only operation. Are you sure your request does not require mutations?"
            )

        # Send the query to the Linear service
        result = self.linear_client.call_api(
            graphql_query,
            request_context,
            credentials=credentials,
        )

        if result.status_code == 401:  # Authentication Error
            raise ToolAuthenticationError(
                result.json_response
                if result.json_response
                else "401: Failed to authenticate to Linear"
            )
        if result.status_code != 200:  # Other Error
            raise Exception(
                f"Got status code {result.status_code} while querying Linear{': ' + result.json_response if result.json_response else ''}"
            )

        return result.json_response

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_configured = self.linear_client.is_configured(request_context)
            if is_configured:
                return agents_pb2.ToolAvailabilityStatus.AVAILABLE
            else:
                return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED
        except Exception as e:
            log.error(
                "Failed to check Linear configuration", error=str(e), exc_info=True
            )
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.linear_client.get_oauth_url(request_context)
        except Exception as e:
            log.error("Failed to get Linear OAuth URL", error=str(e), exc_info=True)
            return ""

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:  # type: ignore
        """
        Revoke the Linear OAuth grant and remove the token from settings.

        Args:
            request_context: The request context to use.

        Returns:
            Status code indicating the result of the operation.
        """
        try:
            response = self.linear_client.revoke_oauth_grant(
                linear_pb2.RevokeOAuthGrantRequest(), request_context
            )
            return response.status
        except Exception as e:
            log.error(
                "Failed to revoke Linear OAuth grant", error=str(e), exc_info=True
            )
            return status_pb2.Status(code=grpc.StatusCode.INTERNAL.value[0])  # type: ignore

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # type: ignore
        """
        Test the connection to Linear by fetching the current user's information.

        Args:
            request_context: The request context to use.

        Returns:
            Status code indicating the result of the operation.
        """
        # Check the user's credentials
        try:
            is_oauth_configured = self.linear_client.is_configured(request_context)
            if not is_oauth_configured:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="Linear credentials are not configured. Please configure them in the settings page.",
                )

            # Test authenticated access by making a test call
            result = self.linear_client.call_api("{__typename}", request_context)
            if result.status_code == 200:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.OK.value[0],
                    message="Linear credentials are successfully configured.",
                )
            elif result.status_code == 401:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="Authentication failed. Please re-authenticate with Linear.",
                )
            else:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.INTERNAL.value[0],
                    message=f"Failed to check Linear credentials (HTTP {result.status_code}).",
                )
        except Exception as e:
            log.error("Failed to check Linear credentials", error=str(e), exc_info=True)
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Failed to check Linear credentials: {str(e)}",
            )


class LinearSearchIssuesTool(
    ValidatedTool[LinearSearchIssuesToolInput, LinearExtraToolInput]
):
    """Tool for querying Linear issues given a search query.

    DEPRECATED. Still here for backwards compatibility with old clients.
    Use LinearTool instead.

    Uses Linear's GraphQL API to query tickets under the hood, and thus can
    query anything that Linear's GraphQL API supports."""

    name = "linear-search-issues"
    description = """\
    Query Linear tickets (AKA issues) using natural language.

    Note there are other sources of tickets/issues like Jira and Github. If the user
    explicitly mentions another source like Jira or Github do NOT use this tool.

    Examples:
    - "find tickets containing foo"
    - "show ticket AU-123"
    - "search tickets about database"
    - "show my tickets"
    - "find my recent ticket"
    - "tickets assigned to me"

    NOTE: This tool is readonly. It cannot create, update, or delete tickets. It can only query them.
    """
    id = agents_pb2.RemoteToolId.LINEAR_SEARCH_ISSUES
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    input_model = LinearSearchIssuesToolInput
    strict = True
    llm_client: ThirdPartyModelClient

    def __init__(self, llm_client: ThirdPartyModelClient, linear_client: LinearClient):
        """Initialize the Linear tool.

        Args:
            llm_client: Model client used for expanding natural language query into GQL query.
            linear_client: Linear client for calling into the Linear service.
        """
        super().__init__()
        self.llm_client = llm_client
        self.linear_client = linear_client
        self.linear_tool = LinearTool(llm_client, linear_client)

    def run_validated(
        self,
        validated_input: LinearSearchIssuesToolInput,
        extra_tool_input: LinearExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Linear search tool.

        Args:
            validated_input: QueryLinearToolInput containing query and user_email
            extra_tool_input: LinearConfig containing the API key
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted search results
        """
        return self.linear_tool.run_validated(
            LinearToolInput(query=validated_input.query, is_read_only=True),
            extra_tool_input,
            request_context,
        )

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        return self.linear_tool.get_availability_status(request_context)

    def get_oauth_url(self, request_context: RequestContext) -> str:
        return self.linear_tool.get_oauth_url(request_context)

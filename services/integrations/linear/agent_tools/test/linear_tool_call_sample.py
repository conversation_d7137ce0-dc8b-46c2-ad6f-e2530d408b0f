"""
Run this script to see a sample of the Linear tool in action.
"""

import argparse
import logging

from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from services.integrations.linear.agent_tools.linear_tools import (
    LinearExtraToolInput,
    LinearSearchIssuesTool,
)
from services.lib.request_context.request_context import RequestContext

logging.basicConfig(level=logging.INFO)


def main():
    parser = argparse.ArgumentParser(
        description="Smoketest the linear tool by running some calls"
    )
    parser.add_argument("--api-key", required=True, type=str, help="The Linear API key")
    parser.add_argument(
        "--user-email",
        required=True,
        type=str,
        help="The email address of the user making the query",
    )
    args = parser.parse_args()

    extra_tool_input = LinearExtraToolInput(
        api_token=args.api_token,
    )

    # Create request context for testing
    request_context = RequestContext.create(request_source="manual_test")

    llm_client = AnthropicVertexAiClient(
        project_id="system-services-dev",
        region="us-east5",
        model_name="claude-3-5-sonnet-v2@20241022",
        temperature=0.0,
        max_output_tokens=1024,
    )

    search_tool = LinearSearchIssuesTool(llm_client)

    print("\n === LinearSearchIssuesTool: Specific Issue ID (AU-6628) ===")
    issue_result = search_tool.run_validated(
        search_tool.input_model(
            query="Information on ticket AU-6628", user_email=args.user_email
        ),
        extra_tool_input,
        request_context,
    )
    print(issue_result)

    # Test LinearSearchIssuesTool with a known issue.
    print("\n === LinearSearchIssuesTool: My most recent ticket ===")
    search_result = search_tool.run_validated(
        search_tool.input_model(
            query="My most recent ticket", user_email=args.user_email
        ),
        extra_tool_input,
        request_context,
    )
    print(search_result)

    # Test JiraIssueTool with a simple search term
    print(
        "\n === LinearSearchIssuesTool: Top 5 most relevant issues about logging Slack app uninstallation ==="
    )
    issue_result = search_tool.run_validated(
        search_tool.input_model(
            query="Top 5 most relevant issues about logging Slack app uninstallation",
            user_email=args.user_email,
        ),
        extra_tool_input,
        request_context,
    )
    print(issue_result)


if __name__ == "__main__":
    main()

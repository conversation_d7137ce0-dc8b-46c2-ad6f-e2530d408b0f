load("@python_pip//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_library")

py_library(
    name = "linear_agent_tools",
    srcs = [
        "linear_tools.py",
    ],
    visibility = [
        "//visibility:public",
    ],
    deps = [
        "//base/third_party_clients:clients",
        "//services/agents:agents_py_proto",
        "//services/agents:tool",
        "//services/integrations/linear:gql_utils",
        "//services/integrations/linear/client:client_py",
        "//services/lib/request_context:request_context_py",
        requirement("pydantic"),
        requirement("structlog"),
        requirement("gql"),
        requirement("requests_toolbelt"),
        requirement("websockets"),
        requirement("aiohttp"),
        requirement("jinja2"),
        requirement("requests"),
    ],
)

"""Pytest tests for is_query_read_only."""

import graphql
import pytest

from services.integrations.linear.gql_utils import is_query_read_only


def test_is_query_read_only_returns_true_for_query_operations():
    """Test that GraphQL query operations are considered safe."""
    # Simple query
    simple_query = """
        query {
            issues {
                nodes {
                    id
                    title
                }
            }
        }
    """
    assert is_query_read_only(simple_query) is True

    # Query with variables and filters
    complex_query = """
        query GetIssues($assigneeEmail: String) {
            issues(filter: {assignee: {email: {eq: $assigneeEmail}}}) {
                nodes {
                    id
                    title
                    state {
                        name
                    }
                }
            }
        }
    """
    assert is_query_read_only(complex_query) is True


def test_is_query_read_only_returns_false_for_mutation_operations():
    """Test that GraphQL mutation operations are considered unsafe."""
    # Simple mutation
    simple_mutation = """
        mutation {
            issueCreate(input: {title: "New Issue", description: "Description"}) {
                success
                issue {
                    id
                }
            }
        }
        """
    assert is_query_read_only(simple_mutation) is False

    # Mutation with variables
    complex_mutation = """
        mutation UpdateIssue($id: ID!, $title: String!) {
            issueUpdate(id: $id, input: {title: $title}) {
                success
                issue {
                    id
                    title
                }
            }
        }
        """
    assert is_query_read_only(complex_mutation) is False


def test_is_query_read_only_returns_false_for_mixed_operations():
    """Test that documents with both query and mutation are considered unsafe."""
    mixed_operations = """
        query GetIssue($id: ID!) {
            issue(id: $id) {
                id
                title
            }
        }

        mutation UpdateIssue($id: ID!, $title: String!) {
            issueUpdate(id: $id, input: {title: $title}) {
                success
            }
        }
        """
    assert is_query_read_only(mixed_operations) is False


def test_is_query_read_only_returns_true_for_multiple_query_operations():
    """Test that documents with multiple query operations are considered safe."""
    multiple_queries = """
        query GetIssue($id: ID!) {
            issue(id: $id) {
                id
                title
            }
        }

        query GetAllIssues {
            issues {
                nodes {
                    id
                    title
                }
            }
        }
        """
    assert is_query_read_only(multiple_queries) is True


def test_is_query_read_only_raises_exception_for_invalid_query():
    """Test that an invalid GraphQL query raises an exception."""
    invalid_query = "This is not a valid GraphQL query"
    with pytest.raises(graphql.GraphQLError):
        is_query_read_only(invalid_query)

from gql import gql
import graphql


def is_query_read_only(query_string: str) -> bool:
    """Returns true iff a GQL SDL query has no mutations.

    Raises an exception if the query is not a valid GQL SDL query.
    """
    # For documentation on GraqhQL DocumentNodes
    # https://graphql-core-3.readthedocs.io/en/latest/usage/parser.html
    document_node = gql(query_string)

    operation_definitions = [
        definition
        for definition in document_node.definitions
        if isinstance(definition, graphql.OperationDefinitionNode)
    ]

    # Check if any operation is a mutation
    for definition in operation_definitions:
        if definition.operation == graphql.OperationType.MUTATION:
            return False

    return True

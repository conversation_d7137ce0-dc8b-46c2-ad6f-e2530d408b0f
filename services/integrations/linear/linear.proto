syntax = "proto3";

package linear;

import "google/rpc/status.proto";

/**
 * Linear API Data Models. These are meant to directly mirror the Linear GraphQL schema.
 * See https://studio.apollographql.com/public/Linear-API/variant/current/schema/reference
 */

// Represents a Linear Workflow State object
message LinearWorkflowState {
  optional string name = 1 [debug_redact = true];
}

// Represents a Linear User object
message LinearUser {
  optional string id = 1 [debug_redact = true];
  optional string email = 2 [debug_redact = true];
  optional string name = 3 [debug_redact = true];
  repeated LinearTeam teams = 4 [debug_redact = true];
}

// Represents a Linear Team object
message LinearTeam {
  optional string id = 1 [debug_redact = true];
  optional string name = 2 [debug_redact = true];
  optional string key = 3 [debug_redact = true];
  optional string description = 4 [debug_redact = true];
}

// Represents the connection structure for teams with nodes
message LinearTeamConnection {
  repeated LinearTeam nodes = 1 [debug_redact = true];
}

// Represents a Linear Issue object
message LinearIssue {
  optional string identifier = 1 [debug_redact = true];
  optional string title = 2 [debug_redact = true];
  optional string description = 3 [debug_redact = true];
  optional LinearWorkflowState state = 4 [debug_redact = true];
  optional LinearUser assignee = 5 [debug_redact = true];
  optional string updated_at = 6 [debug_redact = true];
}

service Linear {
  // Deprecated, do not call. Because the Linear service currently deploys more frequently than the agents service and
  // the cicd deploy queue tends to be backlogged, the agents service might still be calling this API.
  rpc GetLinearUserEmail(GetLinearUserEmailRequest) returns (GetLinearUserEmailResponse);

  rpc GetLinearUserInformation(GetLinearUserInformationRequest) returns (GetLinearUserInformationResponse);

  // DEPRECATED. Call CallApi instead.
  // Search for Linear issues (title, ID, URL, etc.) based on a query
  rpc SearchIssues(LinearSearchIssuesRequest) returns (LinearSearchIssuesResponse) {
    option deprecated = true;
  }

  // Call the Linear API with a GraphQL query, get the JSON response back.
  rpc CallApi(LinearRequest) returns (LinearResponse);

  // Check if Linear credentials are configured in admin settings
  rpc IsConfigured(LinearIsConfiguredRequest) returns (LinearIsConfiguredResponse);

  // Hydrate the Linear tenant settings with a code from the Linear OAuth flow and storing an access token in settings
  rpc HydrateLinearSettings(HydrateLinearSettingsRequest) returns (HydrateLinearSettingsResponse) {}

  rpc GetLinearOAuthUrl(GetLinearOAuthUrlRequest) returns (GetLinearOAuthUrlResponse) {}

  rpc RevokeOAuthGrant(RevokeOAuthGrantRequest) returns (RevokeOAuthGrantResponse) {}
}

message GetLinearUserEmailRequest {
  // Linear API key or OAuth token. If not passed, will look up the credentials
  // in the settings service.
  optional string credentials = 3 [debug_redact = true];
}

message GetLinearUserEmailResponse {
  // User email for the user associated with the Linear credentials.
  string email = 1 [debug_redact = true];

  // HTTP Status Code representing status of the Linear request.
  int32 status_code = 2;
}

message GetLinearUserInformationRequest {
  // Linear API key or OAuth token. If not passed, will look up the credentials
  // in the settings service.
  optional string credentials = 3 [debug_redact = true];
}

message GetLinearUserInformationResponse {
  // User information for the user associated with the Linear credentials.
  LinearUser user = 1 [debug_redact = true];

  // HTTP Status Code representing status of the Linear request.
  int32 status_code = 2;
}

// To be deleted once the SearchIssues rpc is fully deprecated.
message LinearSearchIssuesRequest {
  // GQL Query to send to the Linear API.
  string gql_query = 1 [debug_redact = true];

  // Linear API key or OAuth token. If not passed, will look up the credentials
  // in the settings service.
  optional string credentials = 3 [debug_redact = true];
}

// To be deleted once the SearchIssues rpc is fully deprecated.
message LinearSearchIssuesResponse {
  // Returns a markdown-formatted list of Linear issues. The returned contents
  // depend on what fields were requested in the query. At the very least includes
  // the ticket ID, title, and state (e.g.: "In Progress", "Done", etc.).
  string issue_list_markdown = 1 [debug_redact = true];

  // HTTP Status Code
  int32 status_code = 2;
}

message LinearRequest {
  // GQL Query to send to the Linear API.
  string gql_query = 1 [debug_redact = true];

  // Linear API key or OAuth token. If not passed, will look up the credentials
  // in the settings service.
  optional string credentials = 2 [debug_redact = true];
}

message LinearResponse {
  // JSON response from the Linear API.
  string json_response = 1 [debug_redact = true];

  // HTTP Status Code
  int32 status_code = 2;
}

message LinearIsConfiguredRequest {}

message LinearIsConfiguredResponse {
  bool is_configured = 1;
}

message HydrateLinearSettingsRequest {
  // The code to exchange for an access token
  string code = 1 [debug_redact = true];
}

message HydrateLinearSettingsResponse {
  // HTTP Status Code
  int32 status_code = 1;
}

message GetLinearOAuthUrlRequest {}

message GetLinearOAuthUrlResponse {
  string oauth_url = 1 [debug_redact = true];

  // HTTP Status Code
  int32 status_code = 2;
}

message RevokeOAuthGrantRequest {}

message RevokeOAuthGrantResponse {
  google.rpc.Status status = 1;
}

"""Tests for Linear Auth Processor"""

import pytest
from unittest.mock import Mock, patch
import grpc
import requests
from pydantic import SecretStr

from services.lib.request_context.request_context import RequestContext
from services.settings import settings_pb2
from services.integrations.linear import linear_pb2
from services.integrations.linear.server.linear_auth_processor import (
    LinearAuthProcessor,
    LinearAuthError,
    LinearAuthInternalError,
    LINEAR_EXCHANGE_CODE_URL,
    LINEAR_AUTH_TIMEOUT,
)


@pytest.fixture
def mock_settings_client():
    return Mock()


@pytest.fixture
def mock_request_context():
    return Mock(spec=RequestContext)


@pytest.fixture
def linear_auth_processor(mock_settings_client):
    """Create a LinearAuthProcessor instance for testing."""
    with patch("pathlib.Path.read_text") as mock_read_text:
        mock_read_text.return_value = "test_secret\n"  # Simulate file content
        return LinearAuthProcessor(
            mock_settings_client,
            "https://example.com/linearCallback",
            "test_client_id_path",
            "test_client_secret_path",
        )


def test_exchange_code_for_access_token_success(linear_auth_processor):
    """Test successful exchange of OAuth code for access token."""
    mock_response = Mock()
    mock_response.json.return_value = {
        "access_token": "test_access_token",
        "scope": "read,write",
    }

    with patch("requests.post", return_value=mock_response) as mock_post:
        token, scope = linear_auth_processor._exchange_code_for_access_token(
            "test_code"
        )

        assert token == "test_access_token"
        assert scope == "read,write"
        mock_post.assert_called_once_with(
            LINEAR_EXCHANGE_CODE_URL,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            data={
                "code": "test_code",
                "redirect_uri": "https://example.com/linearCallback",
                "grant_type": "authorization_code",
                "client_id": "test_secret",
                "client_secret": "test_secret",  # pragma: allowlist secret
            },
            timeout=LINEAR_AUTH_TIMEOUT,
        )


def test_exchange_code_for_access_token_error(linear_auth_processor):
    """Test handling of error response during token exchange."""
    mock_response = Mock()
    mock_response.json.return_value = {
        "error": "invalid_grant",
        "error_description": "Invalid authorization code",
    }

    with patch("requests.post", return_value=mock_response):
        with pytest.raises(
            LinearAuthError,
            match="Error exchanging code for access token: invalid_grant - Invalid authorization code",
        ):
            linear_auth_processor._exchange_code_for_access_token("invalid_code")


def test_hydrate_linear_settings_success(
    linear_auth_processor, mock_settings_client, mock_request_context
):
    """Test successful hydration of Linear settings."""
    # Mock the token exchange

    linear_auth_processor._exchange_code_for_access_token = Mock(
        return_value=("test_access_token", "read,write")
    )

    # Mock the settings responses
    mock_settings_client.get_user_settings.return_value = (
        settings_pb2.GetUserSettingsResponse(
            settings=settings_pb2.UserSettings(
                linear_user_settings=settings_pb2.LinearUserSettings()
            ),
            version="1",
        )
    )

    # Call the method
    response = linear_auth_processor.hydrate_linear_settings(
        linear_pb2.HydrateLinearSettingsRequest(code="test_code"),
        Mock(spec=grpc.ServicerContext),
        mock_request_context,
    )

    assert isinstance(response, linear_pb2.HydrateLinearSettingsResponse)
    mock_settings_client.update_user_settings.assert_called_once()
    update_request = mock_settings_client.update_user_settings.call_args[1]["request"]
    assert (
        update_request.settings.linear_user_settings.access_token == "test_access_token"
    )
    assert update_request.settings.linear_user_settings.scope == "read,write"


def test_hydrate_linear_settings_no_code(linear_auth_processor, mock_request_context):
    """Test hydration fails when no code is provided."""
    with pytest.raises(LinearAuthError, match="No code provided"):
        linear_auth_processor.hydrate_linear_settings(
            linear_pb2.HydrateLinearSettingsRequest(),
            Mock(spec=grpc.ServicerContext),
            mock_request_context,
        )


def test_hydrate_linear_settings_settings_error(
    linear_auth_processor, mock_settings_client, mock_request_context
):
    """Test handling of settings update failure."""
    with patch.object(
        linear_auth_processor,
        "_exchange_code_for_access_token",
        return_value=("test_access_token", "read,write"),
    ):
        # Mock settings client to raise an exception
        mock_settings_client.get_user_settings.side_effect = Exception("Settings error")

        with pytest.raises(
            LinearAuthInternalError,
            match="Failed to update user Linear settings: Settings error",
        ):
            linear_auth_processor.hydrate_linear_settings(
                linear_pb2.HydrateLinearSettingsRequest(code="test_code"),
                Mock(spec=grpc.ServicerContext),
                mock_request_context,
            )

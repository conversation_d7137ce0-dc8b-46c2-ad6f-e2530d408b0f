"""
gRPC server for Linear integration.

A very thin wrapper around a Linear HTTP client, primarily split out in order to
keep Linear tokens separated from other secrets.

This file should handle all gRPC level concepts, while linear_handler.py handles
all Linear API level concepts and application logic. LinearHandler should not
know anything about gRPC.
"""

import json
import logging
import grpc

from google.rpc import status_pb2
from pydantic import SecretStr

import services.request_insight.request_insight_pb2 as request_insight_pb2
from services.integrations.linear import linear_pb2, linear_pb2_grpc
from services.integrations.linear.server.config import Config

from services.integrations.linear.models import LinearUser as PydanticLinearUser
from services.integrations.linear.server.linear_handler import (
    <PERSON>ar<PERSON><PERSON><PERSON>,
    LinearHandlerError,
    LinearHandlerSettingsError,
)
from services.integrations.linear.server.linear_auth_processor import (
    LinearAuthProcessor,
    LinearAuthInternalError,
    LinearAuthUnconfiguredError,
    LinearAuthApiError,
    LinearRevokeNonexistentError,
)

import services.lib.grpc.tls_config.tls_config as tls_config
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
    new_event,
)
from services.settings.client.client import SettingsClient
from services.lib.request_context.request_context import RequestContext
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.grpc.auth.service_auth_interceptor import (
    get_auth_info_from_grpc_context,
)

log = logging.getLogger(__name__)


class LinearServerException(grpc.RpcError):
    """Exception raised when the Linear service returns an error."""

    def __init__(self, status_code: grpc.StatusCode, msg: str):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        """Returns the status code."""
        return self.status_code

    def details(self) -> str:
        """Returns the error message."""
        return self.msg


class LinearServer(linear_pb2_grpc.LinearServicer):
    def __init__(
        self, config: Config, request_insight_publisher: RequestInsightPublisher
    ):
        self.config = config
        self.settings_client = SettingsClient(
            config.settings_endpoint,
            tls_config.get_client_tls_creds(config.client_mtls),
        )
        self.linear_handler = LinearHandler(self.settings_client)
        self.auth_processor = LinearAuthProcessor(
            self.settings_client,
            config.linear_app_callback_url,
            config.linear_app_client_id_path,
            config.linear_app_client_secret_path,
        )
        self.request_insight_publisher = request_insight_publisher

    def GetLinearUserEmail(
        self, request: linear_pb2.GetLinearUserEmailRequest, context
    ) -> linear_pb2.GetLinearUserEmailResponse:
        """Get the user email associated with Linear credentials.

        Args:
            request: The request containing optional Linear credentials
            context: gRPC context

        Returns:
            Response containing user email and status code. The status code will be:
            - 200: Success
            - 401: Unauthorized (missing or invalid credentials)
            - 500: Internal server error
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        log.info("LinearServer.GetLinearUserEmail called")

        try:
            if not auth_info.tenant_id:
                log.error("tenant_id not set in auth info")
                return linear_pb2.GetLinearUserEmailResponse(status_code=401)

            with request_context.with_context_logging(), auth_info.with_context_logging():
                pydantic_user = self.linear_handler.get_user_information(
                    SecretStr(request.credentials) if request.credentials else None,
                    request_context,
                )
                if pydantic_user.email:
                    log.info(
                        "LinearServer.GetLinearUserEmail successfully returning user email"
                    )
                    return linear_pb2.GetLinearUserEmailResponse(
                        email=pydantic_user.email, status_code=200
                    )
                else:
                    log.error(
                        "LinearServer.GetLinearUserEmail failed to get Linear user email"
                    )
                    return linear_pb2.GetLinearUserEmailResponse(status_code=500)
        except LinearHandlerSettingsError as e:
            log.error(f"Failed to get Linear settings: {e}")
            return linear_pb2.GetLinearUserEmailResponse(status_code=401)
        except LinearHandlerError as e:
            log.error(f"Failed to get Linear user email: {e}")
            return linear_pb2.GetLinearUserEmailResponse(status_code=500)
        except Exception as e:
            log.error(f"Unexpected error in GetLinearUserEmail: {e}", exc_info=True)
            return linear_pb2.GetLinearUserEmailResponse(status_code=500)

    def GetLinearUserInformation(
        self, request: linear_pb2.GetLinearUserInformationRequest, context
    ) -> linear_pb2.GetLinearUserInformationResponse:
        """Get user information associated with Linear credentials.

        Args:
            request: The request containing optional Linear credentials
            context: gRPC context

        Returns:
            Response containing user information and status code. The status code will be:
            - 200: Success
            - 401: Unauthorized (missing or invalid credentials)
            - 500: Internal server error
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        log.info("LinearServer.GetLinearUserInformation called")

        try:
            if not auth_info.tenant_id:
                log.error("tenant_id not set in auth info")
                return linear_pb2.GetLinearUserInformationResponse(status_code=401)

            with request_context.with_context_logging(), auth_info.with_context_logging():
                pydantic_user = self.linear_handler.get_user_information(
                    SecretStr(request.credentials) if request.credentials else None,
                    request_context,
                )
                log.info(
                    "LinearServer.GetLinearUserInformation successfully returning user info"
                )
                return linear_pb2.GetLinearUserInformationResponse(
                    user=_pydantic_user_to_proto(pydantic_user), status_code=200
                )
        except LinearHandlerSettingsError as e:
            log.error(f"Failed to get Linear settings: {e}")
            return linear_pb2.GetLinearUserInformationResponse(status_code=401)
        except LinearHandlerError as e:
            log.error(f"Failed to get Linear user information: {e}")
            return linear_pb2.GetLinearUserInformationResponse(status_code=401)
        except Exception as e:
            log.error(
                f"Unexpected error in GetLinearUserInformation: {e}", exc_info=True
            )
            return linear_pb2.GetLinearUserInformationResponse(status_code=500)

    def SearchIssues(
        self, request: linear_pb2.LinearSearchIssuesRequest, context
    ) -> linear_pb2.LinearSearchIssuesResponse:
        """Search for Linear issues based on a query.

        DEPRECATED. Use CallApi instead.

        Args:
            request: The request containing the search query and optional credentials
            context: gRPC context

        Returns:
            Response containing search results and status code. The status code will be one of:
            - 200: Success
            - 401: Unauthorized (missing or invalid credentials)
            - 500: Internal server error
        """
        result = self.CallApi(
            linear_pb2.LinearRequest(
                gql_query=request.gql_query, credentials=request.credentials
            ),
            context,
        )

        return linear_pb2.LinearSearchIssuesResponse(
            issue_list_markdown=result.json_response, status_code=result.status_code
        )

    def CallApi(
        self, request: linear_pb2.LinearRequest, context
    ) -> linear_pb2.LinearResponse:
        """Call the Linear API with a GraphQL query.

        Args:
            request: The request containing the GraphQL query and optional credentials
            context: gRPC context

        Returns:
            Response containing the JSON response from the Linear API and status code. The status code will be one of:
            - 200: Success
            - 401: Unauthorized (missing or invalid credentials)
            - 500: Internal server error
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        log.info("LinearServer.CallLinearApi called")

        try:
            if not auth_info.tenant_id:
                log.error("tenant_id not set in auth info")
                return linear_pb2.LinearResponse(status_code=401)

            with request_context.with_context_logging(), auth_info.with_context_logging():
                json_response = self.linear_handler.call_api(
                    request.gql_query,
                    SecretStr(request.credentials) if request.credentials else None,
                    request_context,
                )
                log.info("LinearServer.CallLinearApi successfully returning results")
                return linear_pb2.LinearResponse(
                    json_response=json.dumps(json_response), status_code=200
                )
        except LinearHandlerSettingsError as e:
            log.error(f"Failed to get Linear settings: {e}")
            return linear_pb2.LinearResponse(
                status_code=401, json_response=json_err("Failed to get Linear settings")
            )
        except LinearHandlerError as e:
            log.error(f"Failed to call Linear API: {e}")
            return linear_pb2.LinearResponse(
                status_code=e.linear_status_code or 500, json_response=json_err(str(e))
            )
        except Exception as e:
            log.error(f"Unexpected error in CallLinearApi: {e}", exc_info=True)
            return linear_pb2.LinearResponse(
                status_code=500, json_response=json_err(str(e))
            )

    def IsConfigured(
        self, request: linear_pb2.LinearIsConfiguredRequest, context
    ) -> linear_pb2.LinearIsConfiguredResponse:
        """Check if Linear credentials are configured in admin settings.

        Args:
            request: Empty request
            context: gRPC context

        Returns:
            Response containing whether Linear is configured.

        Throws an exception if there is an internal error accessing settings or
        if the tenant_id is not set in the auth info, which indicate severe
        issues that need to be addressed.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            if not auth_info.tenant_id:
                # This really shouldn't happen
                raise RuntimeError(
                    "tenant_id not set in auth info when checking Linear tool IsConfigured"
                )

            with request_context.with_context_logging(), auth_info.with_context_logging():
                is_configured = self.linear_handler.is_configured(request_context)
                return linear_pb2.LinearIsConfiguredResponse(
                    is_configured=is_configured
                )
        except Exception as e:
            log.error(f"Error checking Linear configuration: {e}")
            raise

    def HydrateLinearSettings(
        self, request: linear_pb2.HydrateLinearSettingsRequest, context
    ) -> linear_pb2.HydrateLinearSettingsResponse:
        """Hydrate Linear settings with OAuth code and store access token.

        Args:
            request: Request containing the OAuth code to exchange for an access token
            context: gRPC context

        Returns:
            Response with a status code. The status code will be one of:
            - 200: Success
            - 401: Unauthorized (missing tenant_id)
            - 400: Bad request (invalid OAuth code)
            - 500: Internal server error
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("LinearServer.HydrateLinearSettings called")

            try:
                # In the case of OAuth, this is where the request first ingresses into
                # the backend (not counting the Remix backend of the customer app), so
                # this is where we publish the request metadata.
                # TODO(AU-8268): Move this event publishing to the customer-ui backend once
                # it's less flaky.
                self._publish_oauth_request_metadata(request_context, auth_info)

                if not auth_info.tenant_id:
                    log.error("tenant_id not set in auth info")
                    return linear_pb2.HydrateLinearSettingsResponse(status_code=401)

                self.auth_processor.hydrate_linear_settings(
                    request, context, request_context
                )
                log.info(
                    "LinearServer.HydrateLinearSettings successfully stored access token"
                )
                return linear_pb2.HydrateLinearSettingsResponse(status_code=200)
            except LinearAuthInternalError as e:
                log.error(f"Failed to exchange OAuth code or store settings: {e}")
                return linear_pb2.HydrateLinearSettingsResponse(status_code=400)
            except Exception as e:
                log.error(f"Unexpected error in HydrateLinearSettings: {e}")
                return linear_pb2.HydrateLinearSettingsResponse(status_code=500)

    def GetLinearOAuthUrl(
        self, request: linear_pb2.GetLinearOAuthUrlRequest, context
    ) -> linear_pb2.GetLinearOAuthUrlResponse:
        """Get the Linear OAuth URL for authentication flow.

        Args:
            request: Empty request
            context: gRPC context

        Returns:
            Response containing the OAuth URL and status code. The status code will be one of:
            - 200: Success
            - 401: Unauthorized (missing tenant_id)
            - 500: Internal server error
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                if not auth_info.tenant_id:
                    log.error("tenant_id not set in auth info")
                    return linear_pb2.GetLinearOAuthUrlResponse(status_code=401)

                oauth_url = self.auth_processor.get_oauth_url(request_context)
                return linear_pb2.GetLinearOAuthUrlResponse(
                    oauth_url=oauth_url, status_code=200
                )
            except LinearAuthUnconfiguredError as e:
                log.error(f"Failed to generate OAuth URL: {e}")
                return linear_pb2.GetLinearOAuthUrlResponse(status_code=500)
            except Exception as e:
                log.error(f"Unexpected error in GetLinearOAuthUrl: {e}")
                return linear_pb2.GetLinearOAuthUrlResponse(status_code=500)

    def RevokeOAuthGrant(
        self, request: linear_pb2.RevokeOAuthGrantRequest, context
    ) -> linear_pb2.RevokeOAuthGrantResponse:
        """Revoke the Linear OAuth grant.

        Args:
            request: Empty request
            context: gRPC context

        Returns:
            Response containing the status. The status code will be one of:
            - OK: Success
            - UNAUTHENTICATED: Unauthorized (missing tenant_id)
            - ALREADY_EXISTS: No token to revoke
            - INTERNAL: Internal server error
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        log.info("LinearServer.RevokeOAuthGrant called")
        try:
            if not auth_info.tenant_id:
                log.error("tenant_id not set in auth info")
                return linear_pb2.RevokeOAuthGrantResponse(
                    status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                        code=grpc.StatusCode.UNAUTHENTICATED.value[0]
                    )
                )

            with request_context.with_context_logging(), auth_info.with_context_logging():
                result_status = self.auth_processor.revoke_token(request_context)
                log.info("LinearServer.RevokeOAuthGrant successfully revoked token")
                return linear_pb2.RevokeOAuthGrantResponse(
                    status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                        code=result_status
                    )
                )
        except (LinearAuthUnconfiguredError, LinearAuthApiError) as e:
            log.error(f"Failed to revoke OAuth grant: {e}")
            # Not configured or internal error
            return linear_pb2.RevokeOAuthGrantResponse(
                status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=grpc.StatusCode.INTERNAL.value[0]
                )
            )
        except LinearRevokeNonexistentError as e:
            # No token exists to revoke
            log.error(f"Failed to revoke OAuth grant: {e}")
            return linear_pb2.RevokeOAuthGrantResponse(
                status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=grpc.StatusCode.ALREADY_EXISTS.value[0]
                )
            )
        except Exception as e:
            # Other exception
            log.error(f"Unexpected error in RevokeOAuthGrant: {e}")
            return linear_pb2.RevokeOAuthGrantResponse(
                status=status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=grpc.StatusCode.INTERNAL.value[0]
                )
            )

    def _publish_oauth_request_metadata(
        self, request_context: RequestContext, auth_info: AuthInfo
    ):
        """
        Publish a RequestMetdata event to request insights for a
        Linear OAuth request ingressing into the backend.

        Args:
            grpc_context: The gRPC context.
            auth_info: The auth info (optional). If not provided it is derived from
                the gRPC context. (Requires that the grpc_context is a
                `ServiceTokenServicerContext`)
        """
        event = new_event()
        event.request_metadata.CopyFrom(
            _oauth_request_metadata_from_grpc_context(request_context, auth_info)
        )

        self.request_insight_publisher.publish_request_insight(
            self.request_insight_publisher.update_request_info_request(
                request_context.request_id,
                [event],
                auth_info,
            )
        )


def _pydantic_user_to_proto(
    user: PydanticLinearUser | None,
) -> linear_pb2.LinearUser | None:
    """Convert a Pydantic LinearUser to a protobuf LinearUser.

    Args:
        user: Pydantic LinearUser model or None

    Returns:
        Protobuf LinearUser message or None if input is None
    """
    if user is None:
        return None

    teams = []
    if user.teams:
        for team in user.teams:
            teams.append(
                linear_pb2.LinearTeam(
                    id=team.get("id"),
                    name=team.get("name"),
                    key=team.get("key"),
                    description=team.get("description"),
                )
            )

    return linear_pb2.LinearUser(
        id=user.id,
        email=user.email,
        name=user.name,
        teams=teams,
    )


def json_err(msg: str) -> str:
    return json.dumps({"error": msg})


def _oauth_request_metadata_from_grpc_context(
    request_context: RequestContext,
    auth_info: AuthInfo,
) -> request_insight_pb2.RequestMetadata:
    """
    Extract request metadata event from a gRPC context for a Linear OAuth
    request.

    Args:
        grpc_context: The gRPC context.
        auth_info: The auth info (optional). If not provided it is derived from
            the gRPC context. (Requires that the grpc_context is a
            `ServiceTokenServicerContext`)

    Returns:
        A RequestMetadata protobuf message
    """
    return request_insight_pb2.RequestMetadata(
        request_type=request_insight_pb2.RequestType.LINEAR_OAUTH,
        session_id=request_context.request_session_id,
        user_id=auth_info.user_id.get_secret_value() if auth_info.user_id else "",
        opaque_user_id=auth_info.parsed_opaque_user_id,
        user_email=auth_info.user_email.get_secret_value()
        if auth_info.user_email
        else "",
        user_agent="linear-server",
    )

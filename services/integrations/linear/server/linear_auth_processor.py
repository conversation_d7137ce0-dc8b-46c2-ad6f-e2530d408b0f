"""Implements Linear authentication flows for the Linear service."""

import base64
import requests
import grpc
import logging
import json
from pathlib import Path
from pydantic import SecretStr
from typing import Tuple
from urllib.parse import urlencode
from google.rpc import status_pb2

from services.lib.request_context.request_context import RequestContext
from services.settings.client.client import SettingsClient
from services.integrations.linear import linear_pb2
from services.settings import settings_pb2

LINEAR_AUTHORIZE_URL = "https://linear.app/oauth/authorize"
LINEAR_EXCHANGE_CODE_URL = "https://api.linear.app/oauth/token"
LINEAR_REVOKE_URL = "https://api.linear.app/oauth/revoke"
LINEAR_AUTH_TIMEOUT = 60


class LinearAuthError(Exception):
    """Base class for Linear authentication errors."""

    pass


class LinearAuthUnconfiguredError(LinearAuthError):
    """Raised when Linear is not configured for the tenant."""

    pass


class LinearAuthInternalError(LinearAuthError):
    """Raised when there is an internal augment error (ex: unable to get/set settings)"""

    def __init__(self, msg="Internal Error"):
        super().__init__(msg)


class LinearAuthApiError(LinearAuthError):
    """Raised when there is an error with the Linear API."""

    pass


class LinearRevokeNonexistentError(LinearAuthError):
    """Raised when we try to revoke token but there is no set token."""

    pass


class LinearAuthProcessor:
    """Implements Linear authentication flows for the Linear service."""

    def __init__(
        self,
        settings_client: SettingsClient,
        callback_url: str,
        client_id_path: str | None,
        client_secret_path: str | None,
    ):
        self.settings_client = settings_client
        self.callback_url = callback_url
        self.oauth_client_id = (
            SecretStr(Path(client_id_path).read_text().strip())
            if client_id_path
            else None
        )
        self.oauth_client_secret = (
            SecretStr(Path(client_secret_path).read_text().strip())
            if client_secret_path
            else None
        )

    def get_oauth_url(self, request_context: RequestContext):
        """Get the Linear OAuth URL. This is used to redirect the user to the OAuth provider."""
        if not self.oauth_client_id:
            raise LinearAuthUnconfiguredError("Linear client ID not configured")

        params = {
            "response_type": "code",
            "client_id": self.oauth_client_id.get_secret_value(),
            "redirect_uri": self.callback_url,
            "scope": "read,write,issues:create,comments:create",
            "prompt": "consent",
        }

        return f"{LINEAR_AUTHORIZE_URL}?{urlencode(params)}"

    def _exchange_code_for_access_token(self, code: str) -> Tuple[str, str]:
        """Exchange OAuth code for access token.
        Return tuple with the access token and scope."""
        if not self.oauth_client_id:
            raise LinearAuthError("Linear client ID not configured")
        if not self.oauth_client_secret:
            raise LinearAuthError("Linear client secret not configured")

        params = {
            "code": code,
            "redirect_uri": self.callback_url,
            "grant_type": "authorization_code",
            "client_id": self.oauth_client_id.get_secret_value(),
            "client_secret": self.oauth_client_secret.get_secret_value(),
        }

        auth_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
        }

        response = requests.post(
            LINEAR_EXCHANGE_CODE_URL,
            headers=auth_headers,
            data=params,
            timeout=LINEAR_AUTH_TIMEOUT,
        )
        data = response.json()
        if "error" in data:
            raise LinearAuthError(
                f"Error exchanging code for access token: {data['error']} - {data['error_description']}"
            )

        token = data["access_token"]
        scope = data["scope"]
        return token, scope

    def hydrate_linear_settings(
        self,
        request: linear_pb2.HydrateLinearSettingsRequest,
        context: grpc.ServicerContext,
        request_context: RequestContext,
    ) -> linear_pb2.HydrateLinearSettingsResponse:
        """Hydrate Linear settings with OAuth code and store access token."""
        if not request.code:
            raise LinearAuthError("No code provided")

        access_token, scope = self._exchange_code_for_access_token(request.code)

        # Update the user settings with the new information.
        try:
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )

            settings = settings_response.settings
            settings.linear_user_settings.access_token = access_token
            settings.linear_user_settings.scope = scope

            self.settings_client.update_user_settings(
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings,
                    expected_version=settings_response.version,
                ),
                request_context=request_context,
            )
            return linear_pb2.HydrateLinearSettingsResponse()

        except Exception as e:
            logging.error(f"Failed to update user Linear settings: {e}")
            raise LinearAuthInternalError(
                f"Failed to update user Linear settings: {e}"
            ) from e

    def revoke_token(
        self,
        request_context: RequestContext,
    ) -> int:
        """Revoke Linear OAuth token and remove it from settings.
        Returns the status code to return"""
        status_return = grpc.StatusCode.OK.value[0]
        try:
            # Get current settings to get the token and version
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )
            settings = settings_response.settings
            access_token = settings.linear_user_settings.access_token
            if not access_token:
                raise LinearRevokeNonexistentError("No token to revoke")

            if not self.oauth_client_id:
                raise LinearAuthUnconfiguredError("Linear client ID not configured")
            if not self.oauth_client_secret:
                raise LinearAuthUnconfiguredError("Linear client secret not configured")

            # Call Linear's revoke endpoint
            response = requests.post(
                LINEAR_REVOKE_URL,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                data={
                    "client_id": self.oauth_client_id.get_secret_value(),
                    "client_secret": self.oauth_client_secret.get_secret_value(),
                    "token": access_token,
                },
                timeout=LINEAR_AUTH_TIMEOUT,
            )
            if response.status_code == 401:
                logging.info(
                    "Token was already revoked on Linear end. Deleting from settings."
                )
                status_return = grpc.StatusCode.ALREADY_EXISTS.value[0]
            elif response.status_code == 400:  # Internal Linear Error in revoking
                raise LinearAuthApiError(f"Failed to revoke token: {response.text}")

            # Clear the token from settings
            settings.linear_user_settings.CopyFrom(settings_pb2.LinearUserSettings())
            self.settings_client.update_user_settings(
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings,
                    expected_version=settings_response.version,
                ),
                request_context=request_context,
            )
            return status_return
        except Exception as e:
            logging.error(f"Failed to revoke Linear token: {e}")
            raise LinearAuthInternalError(f"Failed to revoke Linear token: {e}")

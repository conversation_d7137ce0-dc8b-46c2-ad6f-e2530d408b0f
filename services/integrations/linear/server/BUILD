load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "server",
    srcs = [
        "config.py",
        "linear_auth_processor.py",
        "linear_handler.py",
        "linear_server.py",
        "main.py",
    ],
    main = "main.py",
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/integrations/linear:__subpackages__",
    ],
    deps = [
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/agents:tool",
        "//services/integrations/linear:linear_py_proto",
        "//services/integrations/linear:models",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
        "//services/settings/client:client_py",
        "//third_party/proto:googleapis_status_py_proto",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("gql"),
        requirement("requests_toolbelt"),
        requirement("websockets"),
        requirement("aiohttp"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("jinja2"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
        requirement("pydantic"),
        requirement("requests"),
    ],
)

pytest_test(
    name = "server_test",
    srcs = [
        "linear_auth_processor_test.py",
    ],
    deps = [":server"],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [":image"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

"""Implementation for Linear server methods.

LinearHandler should handle all application logic and be agnostic/unaware of gRPC concepts.
"""

import logging

from pydantic import SecretStr

from services.lib.request_context.request_context import RequestContext
from services.settings.client.client import SettingsClient
from services.integrations.linear.models import LinearUser

from typing import Any

from gql import Client, gql
from gql.transport.requests import RequestsHTTPTransport, log as requests_logger
from gql.transport.exceptions import TransportQueryError, TransportServerError
from graphql import GraphQLError

log = logging.getLogger(__name__)
# Set level for gql transport requests to WARNING so that it does not log every query
requests_logger.setLevel(logging.WARNING)


class LinearHandlerError(Exception):
    """Exception raised when the Linear handler returns an error."""

    linear_status_code: int | None
    """Status code returned by Linear API when the error is caused by a non-2xx Linear
    API response."""

    def __init__(self, msg: str, linear_status_code: int | None = None):
        self.msg = msg
        self.linear_status_code = linear_status_code

    def __str__(self):
        return self.msg


class LinearHandlerApiAuthError(LinearHandlerError):
    """Exception raised when the Linear handler fails to authenticate to the Linear API."""

    pass


class LinearHandlerSettingsError(LinearHandlerError):
    """Exception raised when the Linear handler fails to get settings from the settings service."""

    pass


class LinearHandler:
    """Provider for all supported Linear operations."""

    def __init__(self, settings_client: SettingsClient):
        self.settings_client = settings_client

    def _get_linear_access_token_from_settings(
        self, request_context: RequestContext
    ) -> SecretStr | None:
        """Get Linear access token from user settings.

        Args:
            request_context: Request context for tracking and metrics

        Returns:
            Linear integration secret as a SecretStr, or None if not found or on error
        """
        try:
            settings = self.settings_client.get_user_settings(
                request_context,
            )

            if (
                not settings.settings.linear_user_settings
                or not settings.settings.linear_user_settings.access_token
            ):
                return None

            return SecretStr(settings.settings.linear_user_settings.access_token)
        except Exception as e:
            log.error(f"Failed to get Linear settings: {e}", exc_info=True)
            # If we cannot even access settings, something is foundamentally wrong.
            # Throwing an exception here, and disabling it even if people use an api key.
            raise LinearHandlerSettingsError(
                f"Failed to get Linear settings: {e}"
            ) from e

    def get_user_information(
        self, credentials: SecretStr | None, request_context: RequestContext
    ) -> LinearUser:
        """Get the user information associated with the Linear credentials.

        Args:
            credentials: Optional Linear access token as SecretStr, or None to retrieve from settings
            request_context: Request context for tracking and metrics

        Returns:
            LinearUser object containing user information

        Raises:
            LinearHandlerSettingsError: If no access token could be found
            LinearHandlerError: If there is an error getting user information from Linear API
        """
        credentials = credentials or self._get_linear_access_token_from_settings(
            request_context
        )
        if not credentials:
            log.error(
                "Failed to find Linear user email because no access token could be found"
            )
            raise LinearHandlerSettingsError(
                "Failed to find Linear user email because no access token could be found"
            )

        try:
            # Setup Linear client with appropriate Auth
            transport = RequestsHTTPTransport(
                url="https://api.linear.app/graphql",
                headers={"Authorization": credentials.get_secret_value()},
            )
            client = Client(transport=transport)

            user = _get_user_information(client)
            return user
        except Exception as e:
            raise LinearHandlerError("Failed to get Linear user information") from e

    def call_api(
        self,
        gql_query: str,
        credentials: SecretStr | None,
        request_context: RequestContext,
    ) -> dict[str, Any]:
        """Call the Linear API with a GraphQL query.

        Args:
            gql_query: The GraphQL query to execute
            credentials: Optional credentials to use instead of looking them up
                in the settings service
            request_context: Request context for tracking and metrics.
                Used if credentials is not provided.

        Returns:
            JSON response from the Linear API

        Raises:
            LinearHandlerSettingsError: If credentials cannot be found
            LinearHandlerApiAuthError: If authentication to Linear API fails
            LinearHandlerError: For other Linear API errors
        """
        if not credentials:
            credentials = self._get_linear_access_token_from_settings(request_context)
            if not credentials:
                log.error("Failed to get Linear credentials")
                raise LinearHandlerSettingsError("Failed to get Linear credentials")

        try:
            # Setup Linear client with appropriate Auth
            transport = RequestsHTTPTransport(
                url="https://api.linear.app/graphql",
                headers={"Authorization": credentials.get_secret_value()},
            )
            client = Client(transport=transport)
            result = client.execute(gql(gql_query))

            return result

        except TransportQueryError as e:
            log.error(f"TransportQueryError from Linear API: {str(e)}", exc_info=True)
            if e.errors:
                for err in e.errors:
                    if (
                        err["extensions"]
                        and err["extensions"]["code"] == "AUTHENTICATION_ERROR"
                    ):
                        raise LinearHandlerApiAuthError(
                            f"Failed to authenticate to Linear API: {str(e)}"
                        )
            raise LinearHandlerError(f"Failed to query Linear: {str(e)}")
        except GraphQLError as e:
            log.error(f"Improperly formatted GraphQL query: {str(e)}", exc_info=True)
            raise ValueError("Failed to query Linear due to unparseable GQL") from e
        except TransportServerError as e:
            log.error(
                f"TransportServerError while calling Linear API: {str(e)}",
                exc_info=True,
            )
            raise LinearHandlerError(
                f"Failed to query Linear: {str(e)}", linear_status_code=e.code or 500
            )
        except Exception as e:
            log.error(f"Failed to query Linear: {str(e)}", exc_info=True)
            raise LinearHandlerError("Failed to query Linear") from e

    def is_configured(self, request_context: RequestContext) -> bool:
        """Check if Linear credentials are configured in user settings.

        Args:
            request_context: Request context for tracking and metrics

        Returns:
            True if credentials are configured, False otherwise

        Throws an exception if there is an internal error accessing settings.
        """
        credentials = self._get_linear_access_token_from_settings(request_context)
        if credentials is None:
            return False

        return True


def _get_user_information(client: Client) -> LinearUser:
    """Get the user information associated with the Linear credentials.

    Args:
        client: GraphQL client for executing queries.

    Returns:
        LinearUser object

    Raises:
        ValueError: If the user's information cannot be determined.
    """
    query = gql(
        """
        query {
            viewer {
                id
                email
                name
                teams {
                    nodes {
                        id
                        name
                    }
                }
            }
        }
        """
    )
    result = client.execute(query)
    return LinearUser.from_node(result["viewer"])

"""Configuration for the Linear server."""

import pathlib
from dataclasses import dataclass
import typing

from dataclasses_json import dataclass_json

import services.lib.grpc.tls_config.tls_config as tls_config


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for the Linear server."""

    port: int
    feature_flags_sdk_key_path: typing.Optional[str]
    dynamic_feature_flags_endpoint: typing.Optional[str]
    auth_config: AuthConfig
    settings_endpoint: str
    linear_app_callback_url: str
    linear_app_client_id_path: typing.Optional[str]
    linear_app_client_secret_path: typing.Optional[str]
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None
    central_server_mtls: typing.Optional[tls_config.ServerConfig] = None
    client_mtls: typing.Optional[tls_config.ClientConfig] = None
    server_mtls: typing.Optional[tls_config.ServerConfig] = None
    shutdown_grace_period_s: int = 25

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'linear',
      kubecfg: {
        target: '//services/integrations/linear/server:kubecfg',
        // Linear is deployed everywhere. Access to using Linear tool is determined by access to agents.
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['tenzin', 'aswin', 'surbhi', 'sophie', 'xiaolei'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

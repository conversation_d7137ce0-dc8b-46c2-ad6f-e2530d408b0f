"""Tests for Linear API data models."""

from services.integrations.linear.models import (
    LinearIssue,
    LinearUser,
    LinearWorkflowState,
)


def test_linear_models_with_sample_data():
    """Test that our models correctly parse a sample Linear API response."""
    sample_data = {
        "id": "0f39706b-3345-446b-89b8-d85ad881c8fb",
        "identifier": "AUG-10",
        "title": "Log LinearServer __init__ Completion",
        "description": "We want to log when the Python Linear service servicer finishes it's init method so we know it's ready.\n\nSpecifically add a log at the end of `LinearServer.__init__` in `services/integrations/linear/server/linear_server.py` indicating when the `__init__` method finishes.",
        "state": {"name": "Todo"},
        "assignee": {
            "name": "<EMAIL>",
            "email": "<EMAIL>",
        },
    }

    # Test parsing a full issue
    issue = LinearIssue.from_node(sample_data)
    assert issue is not None
    assert issue.identifier == "AUG-10"
    assert issue.title == "Log LinearServer __init__ Completion"
    assert issue.description and "LinearServer.__init__" in issue.description

    # Test nested state object
    assert issue.state is not None
    assert isinstance(issue.state, LinearWorkflowState)
    assert issue.state.name == "Todo"

    # Test nested user object
    assert issue.assignee is not None
    assert isinstance(issue.assignee, LinearUser)
    assert issue.assignee.email == "<EMAIL>"
    assert issue.assignee.name == "<EMAIL>"


def test_linear_models_with_none():
    """Test that our models handle None inputs correctly."""
    assert LinearIssue.from_node(None) is None
    assert LinearUser.from_node(None) is None
    assert LinearWorkflowState.from_node(None) is None


def test_linear_models_with_missing_fields():
    """Test that our models handle missing fields correctly."""
    minimal_data = {
        "identifier": "AUG-10",
    }
    issue = LinearIssue.from_node(minimal_data)
    assert issue is not None
    assert issue.identifier == "AUG-10"
    assert issue.title is None
    assert issue.description is None
    assert issue.state is None
    assert issue.assignee is None
    assert issue.updated_at is None

"""A Python client library for the Linear integration service."""

import logging
from typing import Optional

import grpc

from base.python.grpc import client_options
from services.integrations.linear import linear_pb2, linear_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> linear_pb2_grpc.LinearStub:
    """Setup the client stub for the Linear service.

    Args:
        endpoint: The endpoint of the Linear service.
        credentials: The credentials to use for the channel (optional)
        options: Additional gRPC channel options (optional)

    Returns:
        The client stub for the Linear service.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, options or [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = linear_pb2_grpc.LinearStub(channel)
    return stub


class LinearClient:
    """Client for interacting with the Linear service."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        self.stub = setup_stub(endpoint, credentials, options=options)

    def is_configured(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> bool:
        """Check if Linear credentials are configured in admin settings.

        Args:
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            True if credentials are configured, False otherwise.
        """
        request = linear_pb2.LinearIsConfiguredRequest()
        response = self.stub.IsConfigured(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.is_configured

    def get_user_information(
        self,
        request_context: RequestContext,
        credentials: str | None = None,
        timeout: float = 30,
    ) -> linear_pb2.LinearUser | None:
        """Get information about the user associated with the Linear credentials.

        Args:
            request_context: The request context to use.
            credentials: Linear API key. If not passed, will look up the
                credentials in the settings service, based on the user ID
                inferred from the auth in gRPC context.
            timeout: The gRPC request timeout in seconds.

        Returns:
            LinearUser containing user information if successful, None if request fails.

        Raises:
            grpc.RpcError: If there is an error communicating with the Linear service.
        """
        request = linear_pb2.GetLinearUserInformationRequest(credentials=credentials)
        response = self.stub.GetLinearUserInformation(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        if response.status_code != 200:
            return None
        return response.user

    def call_api(
        self,
        gql_query: str,
        request_context: RequestContext,
        credentials: str | None = None,
        timeout: float = 30,
    ) -> linear_pb2.LinearResponse:
        """Call the Linear API with a GraphQL query.

        Args:
            gql_query: The GQL Query to send to Linear's API.
            request_context: The request context to use.
            credentials: Linear API key. If not passed, will look up the
                credentials in the settings service, based on the user ID
                inferred from the auth in gRPC context.
            timeout: The gRPC request timeout in seconds.

        Returns:
            LinearResponse containing the JSON response from the Linear API and response status code.
        """
        request = linear_pb2.LinearRequest(gql_query=gql_query, credentials=credentials)
        response = self.stub.CallApi(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def search_issues(
        self,
        gql_query: str,
        request_context: RequestContext,
        credentials: str | None = None,
        timeout: float = 30,
    ) -> linear_pb2.LinearSearchIssuesResponse:
        """Search Linear issues.

        DEPRECATED. Use call_api instead.

        Args:
            gql_query: The GQL Query to send to Linear's API.
            request_context: The request context to use.
            credentials: Linear API key. If not passed, will look up the
                credentials in the settings service, based on the user ID
                inferred from the auth in gRPC context.
            timeout: The gRPC request timeout in seconds.

        Returns:
            LinearSearchIssuesResponse containing the markdown-formatted search results and response status code.
        """
        request = linear_pb2.LinearSearchIssuesRequest(
            gql_query=gql_query, credentials=credentials
        )

        response: linear_pb2.LinearSearchIssuesResponse = self.stub.SearchIssues(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def get_oauth_url(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> str:
        """Get the Linear OAuth URL.

        Args:
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            The Linear OAuth URL.
        """
        request = linear_pb2.GetLinearOAuthUrlRequest()
        response = self.stub.GetLinearOAuthUrl(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.oauth_url

    def revoke_oauth_grant(
        self,
        request: linear_pb2.RevokeOAuthGrantRequest,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> linear_pb2.RevokeOAuthGrantResponse:
        """Revoke the Linear OAuth grant.

        Args:
            request: Revoke OAuth grant request
            request_context: The request context to use.
            timeout: The timeout in seconds.
        """
        response = self.stub.RevokeOAuthGrant(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

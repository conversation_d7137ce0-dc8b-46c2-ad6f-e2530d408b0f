// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/linear/linear.proto (package linear, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message linear.LinearSearchIssuesRequest
 */
export declare class LinearSearchIssuesRequest extends Message<LinearSearchIssuesRequest> {
  /**
   * @generated from field: string gql_query = 1;
   */
  gqlQuery: string;

  /**
   * @generated from field: optional string credentials = 3;
   */
  credentials?: string;

  constructor(data?: PartialMessage<LinearSearchIssuesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "linear.LinearSearchIssuesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LinearSearchIssuesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LinearSearchIssuesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LinearSearchIssuesRequest;

  static equals(a: LinearSearchIssuesRequest | PlainMessage<LinearSearchIssuesRequest> | undefined, b: LinearSearchIssuesRequest | PlainMessage<LinearSearchIssuesRequest> | undefined): boolean;
}

/**
 * @generated from message linear.LinearSearchIssuesResponse
 */
export declare class LinearSearchIssuesResponse extends Message<LinearSearchIssuesResponse> {
  /**
   * @generated from field: string issue_list_markdown = 1;
   */
  issueListMarkdown: string;

  constructor(data?: PartialMessage<LinearSearchIssuesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "linear.LinearSearchIssuesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LinearSearchIssuesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LinearSearchIssuesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LinearSearchIssuesResponse;

  static equals(a: LinearSearchIssuesResponse | PlainMessage<LinearSearchIssuesResponse> | undefined, b: LinearSearchIssuesResponse | PlainMessage<LinearSearchIssuesResponse> | undefined): boolean;
}

/**
 * @generated from message linear.LinearIsConfiguredRequest
 */
export declare class LinearIsConfiguredRequest extends Message<LinearIsConfiguredRequest> {
  constructor(data?: PartialMessage<LinearIsConfiguredRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "linear.LinearIsConfiguredRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LinearIsConfiguredRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LinearIsConfiguredRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LinearIsConfiguredRequest;

  static equals(a: LinearIsConfiguredRequest | PlainMessage<LinearIsConfiguredRequest> | undefined, b: LinearIsConfiguredRequest | PlainMessage<LinearIsConfiguredRequest> | undefined): boolean;
}

/**
 * @generated from message linear.LinearIsConfiguredResponse
 */
export declare class LinearIsConfiguredResponse extends Message<LinearIsConfiguredResponse> {
  /**
   * @generated from field: bool is_configured = 1;
   */
  isConfigured: boolean;

  constructor(data?: PartialMessage<LinearIsConfiguredResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "linear.LinearIsConfiguredResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): LinearIsConfiguredResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): LinearIsConfiguredResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): LinearIsConfiguredResponse;

  static equals(a: LinearIsConfiguredResponse | PlainMessage<LinearIsConfiguredResponse> | undefined, b: LinearIsConfiguredResponse | PlainMessage<LinearIsConfiguredResponse> | undefined): boolean;
}

/**
 * @generated from message linear.HydrateLinearSettingsRequest
 */
export declare class HydrateLinearSettingsRequest extends Message<HydrateLinearSettingsRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  /**
   * @generated from field: optional string credentials = 2;
   */
  credentials?: string;

  constructor(data?: PartialMessage<HydrateLinearSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "linear.HydrateLinearSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateLinearSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateLinearSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateLinearSettingsRequest;

  static equals(a: HydrateLinearSettingsRequest | PlainMessage<HydrateLinearSettingsRequest> | undefined, b: HydrateLinearSettingsRequest | PlainMessage<HydrateLinearSettingsRequest> | undefined): boolean;
}

/**
 * @generated from message linear.HydrateLinearSettingsResponse
 */
export declare class HydrateLinearSettingsResponse extends Message<HydrateLinearSettingsResponse> {
  constructor(data?: PartialMessage<HydrateLinearSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "linear.HydrateLinearSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateLinearSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateLinearSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateLinearSettingsResponse;

  static equals(a: HydrateLinearSettingsResponse | PlainMessage<HydrateLinearSettingsResponse> | undefined, b: HydrateLinearSettingsResponse | PlainMessage<HydrateLinearSettingsResponse> | undefined): boolean;
}


// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/linear/linear.proto (package linear, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { HydrateLinearSettingsRequest, HydrateLinearSettingsResponse, LinearIsConfiguredRequest, LinearIsConfiguredResponse, LinearSearchIssuesRequest, LinearSearchIssuesResponse } from "./linear_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service linear.Linear
 */
export declare const Linear: {
  readonly typeName: "linear.Linear",
  readonly methods: {
    /**
     * @generated from rpc linear.Linear.SearchIssues
     */
    readonly searchIssues: {
      readonly name: "SearchIssues",
      readonly I: typeof LinearSearchIssuesRequest,
      readonly O: typeof LinearSearchIssuesResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc linear.Linear.IsConfigured
     */
    readonly isConfigured: {
      readonly name: "IsConfigured",
      readonly I: typeof LinearIsConfiguredRequest,
      readonly O: typeof LinearIsConfiguredResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc linear.Linear.HydrateLinearSettings
     */
    readonly hydrateLinearSettings: {
      readonly name: "HydrateLinearSettings",
      readonly I: typeof HydrateLinearSettingsRequest,
      readonly O: typeof HydrateLinearSettingsResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};


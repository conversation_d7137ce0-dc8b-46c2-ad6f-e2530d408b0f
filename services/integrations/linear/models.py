"""
Linear API Data Models.

These are manually implemented models meant to mirror the GraphQL schema. The idea
is that for any Linear GraphQL response, we can parse the response into a
Pydantic model.

For example
```
result = client.execute(gql(query))
issue = result["issue"]
LinearIssue.from_node(issue)
```

The Linear GQL Schema reference is available here:
https://studio.apollographql.com/public/Linear-API/variant/current/schema/reference
"""

from typing import Any, Dict, overload

from pydantic import BaseModel


class LinearWorkflowState(BaseModel):
    """Represents a Linear Workflow State object."""

    name: str | None

    @classmethod
    @overload
    def from_node(cls, node: None) -> None: ...

    @classmethod
    @overload
    def from_node(cls, node: dict[str, Any]) -> "LinearWorkflowState": ...

    @classmethod
    def from_node(cls, node: dict[str, Any] | None) -> "LinearWorkflowState | None":
        """Create a LinearState from a GraphQL node."""
        if not node:
            return None
        return cls(
            name=node.get("name"),
        )


class LinearIssue(BaseModel):
    """Represents a Linear Issue object."""

    identifier: str | None
    title: str | None
    description: str | None
    state: LinearWorkflowState | None
    assignee: "LinearUser | None"
    updated_at: str | None

    @classmethod
    @overload
    def from_node(cls, node: None) -> None: ...

    @classmethod
    @overload
    def from_node(cls, node: dict[str, Any]) -> "LinearIssue": ...

    @classmethod
    def from_node(cls, node: dict[str, Any] | None) -> "LinearIssue | None":
        """Create a LinearIssue from a GraphQL node."""
        if not node:
            return None
        return cls(
            identifier=node.get("identifier"),
            title=node.get("title"),
            description=node.get("description"),
            state=LinearWorkflowState.from_node(node.get("state")),
            assignee=LinearUser.from_node(node.get("assignee")),
            updated_at=node.get("updatedAt"),
        )


class LinearTeam(BaseModel):
    """
    Represents a Linear Team object.
    """

    id: str | None
    name: str | None
    key: str | None
    description: str | None

    @classmethod
    @overload
    def from_node(cls, node: None) -> None: ...

    @classmethod
    @overload
    def from_node(cls, node: dict[str, Any]) -> "LinearTeam": ...

    @classmethod
    def from_node(cls, node: dict[str, Any] | None) -> "LinearTeam | None":
        """Create a LinearTeam from a GraphQL node."""
        if not node:
            return None
        return cls(
            id=node.get("id"),
            name=node.get("name"),
            key=node.get("key"),
            description=node.get("description"),
        )


class LinearTeamConnection(BaseModel):
    """
    Represents the connection structure for teams with nodes.
    """

    nodes: list[LinearTeam]


class LinearUser(BaseModel):
    """
    Our incomplete mirror of the Linear User object.

    See https://studio.apollographql.com/public/Linear-API/variant/current/schema/reference/objects/User?query=User
    """

    id: str | None
    email: str | None
    name: str | None
    teams: list | None

    @classmethod
    @overload
    def from_node(cls, node: None) -> None: ...

    @classmethod
    @overload
    def from_node(cls, node: dict[str, Any]) -> "LinearUser": ...

    @classmethod
    def from_node(cls, node: dict[str, Any] | None) -> "LinearUser | None":
        """Create a LinearUser from a GraphQL node."""
        if not node:
            return None
        teams = node.get("teams", {}).get("nodes")
        return cls(
            id=node.get("id"),
            email=node.get("email"),
            name=node.get("name"),
            teams=teams,
        )

load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/integrations/glean/client",
    visibility = ["//services/integrations:__subpackages__"],
    deps = [
        "//services/integrations/glean:glean_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/integrations/glean:glean_py_proto",
        "//services/lib/request_context:request_context_py",
        "@python_pip//grpcio",
    ],
)

"""Python wrapper client for calling into the Glean gRPC Server."""

import json
import logging
from typing import Literal

import grpc

from base.python.grpc import client_options
from services.integrations.glean import glean_pb2, glean_pb2_grpc
from services.lib.request_context.request_context import RequestContext

HttpMethodStr = Literal["GET", "POST"]


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> glean_pb2_grpc.GleanStub:
    """Setup the client stub for the Glean service.

    Args:
        endpoint: The endpoint of the Glean service.
        credentials: The credentials to use for the channel (optional)
        options: Additional gRPC channel options (optional)

    Returns:
        The client stub for the Glean service.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, options or [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = glean_pb2_grpc.GleanStub(channel)
    return stub


class GleanClient:
    """Client for interacting with the Glean service."""

    def __init__(
        self,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        """Initialize the Glean client.

        Args:
            endpoint: The endpoint of the Glean service.
            credentials: The credentials to use for the channel (optional)
            options: Additional gRPC channel options (optional)
        """
        self.stub = setup_stub(endpoint, credentials, options=options)

    def search(
        self,
        request_context: RequestContext,
        query: str,
        include_private_docs: bool = False,
        user_id: str | None = None,
        user_email: str | None = None,
        timeout: float = 30,
    ) -> glean_pb2.SearchResponse:
        """Search for documents in Glean.

        Args:
            request_context: The request context to use.
            query: The search query.
            include_private_docs: Whether to include private documents in the search results.
            user_id: The user ID to use for the search. If not provided, the user ID will be
                extracted from the auth info in the request context.
            user_email: The user email to use for JWT authentication. Required when using JWT.
            timeout: The timeout in seconds.

        Returns:
            SearchResponse containing the search results.

        Raises:
            grpc.RpcError: If there is an error communicating with the Glean service.
        """
        request = glean_pb2.SearchRequest(
            query=query,
            include_private_docs=include_private_docs,
            user_id=user_id or "",
            user_email=user_email or "",
        )
        response = self.stub.Search(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def call_glean_api(
        self,
        request_context: RequestContext,
        path: str,
        method: HttpMethodStr,
        payload: dict | str,
        timeout: float = 30,
    ) -> glean_pb2.CallGleanApiResponse:
        """Call a Glean API endpoint.

        Args:
            request_context: The request context to use.
            path: The API endpoint path.
            method: The HTTP method.
            payload: The JSON payload to send in the request either as a dict or string.
            timeout: The timeout in seconds.

        Returns:
            CallGleanApiResponse containing the raw JSON response from the Glean API.

        Raises:
            grpc.RpcError: If there is an error communicating with the Glean service.
        """
        if isinstance(payload, dict):
            payload = json.dumps(payload)

        request = glean_pb2.CallGleanApiRequest(
            path=path,
            method=str_to_http_method(method),
            payload=payload,
        )
        response = self.stub.CallGleanApi(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def is_tool_configured(
        self,
        request_context: RequestContext,
        user_id: str = "",
        timeout: float = 30,
    ) -> bool:
        """Check if the Glean tool is configured for the tenant.

        Args:
            request_context: The request context to use.
            user_id: The user ID to use for the check. If not provided, the user ID will be
                extracted from the auth info in the request context.
            timeout: The timeout in seconds.

        Returns:
            True if the Glean tool is configured for the tenant, False otherwise.

        Raises:
            grpc.RpcError: If there is an error communicating with the Glean service.
        """
        request = glean_pb2.IsToolConfiguredRequest(
            user_id=user_id,
        )
        response = self.stub.IsToolConfigured(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.is_configured

    def get_oauth_url(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> str:
        """Get the OAuth URL for Glean authentication.

        Args:
            request_context: The request context to use.
            timeout: The timeout in seconds.

        Returns:
            The OAuth URL as a string.

        Raises:
            grpc.RpcError: If there is an error communicating with the Glean service.
        """
        request = glean_pb2.GetOAuthURLRequest()
        response = self.stub.GetOAuthURL(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.oauth_url


def http_method_to_str(
    method: glean_pb2.HttpMethod.ValueType,
) -> HttpMethodStr:
    if method == glean_pb2.HttpMethod.GET:
        return "GET"
    elif method == glean_pb2.HttpMethod.POST:
        return "POST"
    else:
        raise ValueError(f"Unknown HTTP method: {method}")


def str_to_http_method(
    method: HttpMethodStr,
) -> glean_pb2.HttpMethod.ValueType:
    if method == "GET":
        return glean_pb2.HttpMethod.GET
    elif method == "POST":
        return glean_pb2.HttpMethod.POST
    else:
        raise ValueError(f"Unknown HTTP method: {method}")

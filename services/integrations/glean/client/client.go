package client

import (
	"context"
	"fmt"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/integrations/glean/proto"
)

type GleanClient interface {
	Search(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.SearchRequest) (*pb.SearchResponse, error)
	GetOAuthURL(ctx context.Context, requestContext *requestcontext.RequestContext) (string, error)
	IsToolConfigured(ctx context.Context, requestContext *requestcontext.RequestContext, userId string) (bool, error)
}

type GleanClientImpl struct {
	endpoint string
	conn     *grpc.ClientConn
	client   pb.GleanClient
}

func NewGleanClient(endpoint string, creds credentials.TransportCredentials) (GleanClient, error) {
	conn, err := grpc.NewClient(endpoint,
		grpc.WithTransportCredentials(creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Glean service: %v", err)
	}

	client := pb.NewGleanClient(conn)
	return &GleanClientImpl{
		endpoint: endpoint,
		conn:     conn,
		client:   client,
	}, nil
}

func (c *GleanClientImpl) Search(ctx context.Context, requestContext *requestcontext.RequestContext, req *pb.SearchRequest) (*pb.SearchResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.Search(ctx, req)
}

func (c *GleanClientImpl) GetOAuthURL(ctx context.Context, requestContext *requestcontext.RequestContext) (string, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetOAuthURL(ctx, &pb.GetOAuthURLRequest{})
	if err != nil {
		return "", err
	}
	return resp.OauthUrl, nil
}

func (c *GleanClientImpl) IsToolConfigured(ctx context.Context, requestContext *requestcontext.RequestContext, userId string) (bool, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.IsToolConfigured(ctx, &pb.IsToolConfiguredRequest{
		UserId: userId,
	})
	if err != nil {
		return false, err
	}
	return resp.GetIsConfigured(), nil
}

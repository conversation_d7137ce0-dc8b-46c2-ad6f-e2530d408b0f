// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/glean/glean.proto (package glean, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum glean.Provider
 */
export declare enum Provider {
  /**
   * @generated from enum value: UNSPECIFIED_PROVIDER = 0;
   */
  UNSPECIFIED_PROVIDER = 0,

  /**
   * @generated from enum value: GSUITE = 1;
   */
  GSUITE = 1,

  /**
   * @generated from enum value: AZURE = 2;
   */
  AZURE = 2,

  /**
   * @generated from enum value: OKTA = 3;
   */
  OKTA = 3,

  /**
   * @generated from enum value: ONELOGIN = 4;
   */
  ONELOGIN = 4,
}

/**
 * @generated from enum glean.DocumentType
 */
export declare enum DocumentType {
  /**
   * @generated from enum value: UNSPECIFIED_DOCUMENT_TYPE = 0;
   */
  UNSPECIFIED_DOCUMENT_TYPE = 0,

  /**
   * @generated from enum value: PAGE = 1;
   */
  PAGE = 1,

  /**
   * @generated from enum value: MESSAGE = 2;
   */
  MESSAGE = 2,

  /**
   * @generated from enum value: CHANNEL = 3;
   */
  CHANNEL = 3,

  /**
   * @generated from enum value: CONVERSATION = 4;
   */
  CONVERSATION = 4,

  /**
   * @generated from enum value: ISSUE = 5;
   */
  ISSUE = 5,
}

/**
 * @generated from enum glean.DataSource
 */
export declare enum DataSource {
  /**
   * @generated from enum value: UNSPECIFIED_DATA_SOURCE = 0;
   */
  UNSPECIFIED_DATA_SOURCE = 0,

  /**
   * @generated from enum value: SLACK = 1;
   */
  SLACK = 1,

  /**
   * @generated from enum value: NOTION = 2;
   */
  NOTION = 2,

  /**
   * @generated from enum value: LINEAR = 3;
   */
  LINEAR = 3,

  /**
   * @generated from enum value: GITHUB = 4;
   */
  GITHUB = 4,
}

/**
 * @generated from enum glean.HttpMethod
 */
export declare enum HttpMethod {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: GET = 1;
   */
  GET = 1,

  /**
   * @generated from enum value: POST = 2;
   */
  POST = 2,
}

/**
 * @generated from message glean.GetGleanTenantSettingsRequest
 */
export declare class GetGleanTenantSettingsRequest extends Message<GetGleanTenantSettingsRequest> {
  constructor(data?: PartialMessage<GetGleanTenantSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.GetGleanTenantSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGleanTenantSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGleanTenantSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGleanTenantSettingsRequest;

  static equals(a: GetGleanTenantSettingsRequest | PlainMessage<GetGleanTenantSettingsRequest> | undefined, b: GetGleanTenantSettingsRequest | PlainMessage<GetGleanTenantSettingsRequest> | undefined): boolean;
}

/**
 * @generated from message glean.GetGleanTenantSettingsResponse
 */
export declare class GetGleanTenantSettingsResponse extends Message<GetGleanTenantSettingsResponse> {
  /**
   * @generated from field: glean.Provider oauth_provider = 1 [deprecated = true];
   * @deprecated
   */
  oauthProvider: Provider;

  /**
   * @generated from field: string glean_domain = 2;
   */
  gleanDomain: string;

  /**
   * @generated from field: string public_key = 3;
   */
  publicKey: string;

  constructor(data?: PartialMessage<GetGleanTenantSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.GetGleanTenantSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetGleanTenantSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetGleanTenantSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetGleanTenantSettingsResponse;

  static equals(a: GetGleanTenantSettingsResponse | PlainMessage<GetGleanTenantSettingsResponse> | undefined, b: GetGleanTenantSettingsResponse | PlainMessage<GetGleanTenantSettingsResponse> | undefined): boolean;
}

/**
 * @generated from message glean.HydrateGleanTenantSettingsRequest
 */
export declare class HydrateGleanTenantSettingsRequest extends Message<HydrateGleanTenantSettingsRequest> {
  /**
   * @generated from field: string oauth_client_id = 1 [deprecated = true];
   * @deprecated
   */
  oauthClientId: string;

  /**
   * @generated from field: string oauth_client_secret = 2 [deprecated = true];
   * @deprecated
   */
  oauthClientSecret: string;

  /**
   * @generated from field: string oauth_subdomain = 3 [deprecated = true];
   * @deprecated
   */
  oauthSubdomain: string;

  /**
   * @generated from field: glean.Provider oauth_provider = 4 [deprecated = true];
   * @deprecated
   */
  oauthProvider: Provider;

  /**
   * @generated from field: string glean_domain = 5;
   */
  gleanDomain: string;

  constructor(data?: PartialMessage<HydrateGleanTenantSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.HydrateGleanTenantSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGleanTenantSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGleanTenantSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGleanTenantSettingsRequest;

  static equals(a: HydrateGleanTenantSettingsRequest | PlainMessage<HydrateGleanTenantSettingsRequest> | undefined, b: HydrateGleanTenantSettingsRequest | PlainMessage<HydrateGleanTenantSettingsRequest> | undefined): boolean;
}

/**
 * @generated from message glean.HydrateGleanTenantSettingsResponse
 */
export declare class HydrateGleanTenantSettingsResponse extends Message<HydrateGleanTenantSettingsResponse> {
  constructor(data?: PartialMessage<HydrateGleanTenantSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.HydrateGleanTenantSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGleanTenantSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGleanTenantSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGleanTenantSettingsResponse;

  static equals(a: HydrateGleanTenantSettingsResponse | PlainMessage<HydrateGleanTenantSettingsResponse> | undefined, b: HydrateGleanTenantSettingsResponse | PlainMessage<HydrateGleanTenantSettingsResponse> | undefined): boolean;
}

/**
 * @generated from message glean.HydrateGleanUserSettingsRequest
 */
export declare class HydrateGleanUserSettingsRequest extends Message<HydrateGleanUserSettingsRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  constructor(data?: PartialMessage<HydrateGleanUserSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.HydrateGleanUserSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGleanUserSettingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGleanUserSettingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGleanUserSettingsRequest;

  static equals(a: HydrateGleanUserSettingsRequest | PlainMessage<HydrateGleanUserSettingsRequest> | undefined, b: HydrateGleanUserSettingsRequest | PlainMessage<HydrateGleanUserSettingsRequest> | undefined): boolean;
}

/**
 * @generated from message glean.HydrateGleanUserSettingsResponse
 */
export declare class HydrateGleanUserSettingsResponse extends Message<HydrateGleanUserSettingsResponse> {
  constructor(data?: PartialMessage<HydrateGleanUserSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.HydrateGleanUserSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): HydrateGleanUserSettingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): HydrateGleanUserSettingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): HydrateGleanUserSettingsResponse;

  static equals(a: HydrateGleanUserSettingsResponse | PlainMessage<HydrateGleanUserSettingsResponse> | undefined, b: HydrateGleanUserSettingsResponse | PlainMessage<HydrateGleanUserSettingsResponse> | undefined): boolean;
}

/**
 * @generated from message glean.GetOAuthURLRequest
 */
export declare class GetOAuthURLRequest extends Message<GetOAuthURLRequest> {
  constructor(data?: PartialMessage<GetOAuthURLRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.GetOAuthURLRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOAuthURLRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOAuthURLRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOAuthURLRequest;

  static equals(a: GetOAuthURLRequest | PlainMessage<GetOAuthURLRequest> | undefined, b: GetOAuthURLRequest | PlainMessage<GetOAuthURLRequest> | undefined): boolean;
}

/**
 * @generated from message glean.GetOAuthURLResponse
 */
export declare class GetOAuthURLResponse extends Message<GetOAuthURLResponse> {
  /**
   * @generated from field: string oauth_url = 1;
   */
  oauthUrl: string;

  constructor(data?: PartialMessage<GetOAuthURLResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.GetOAuthURLResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOAuthURLResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOAuthURLResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOAuthURLResponse;

  static equals(a: GetOAuthURLResponse | PlainMessage<GetOAuthURLResponse> | undefined, b: GetOAuthURLResponse | PlainMessage<GetOAuthURLResponse> | undefined): boolean;
}

/**
 * @generated from message glean.SearchRequest
 */
export declare class SearchRequest extends Message<SearchRequest> {
  /**
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * @generated from field: bool include_private_docs = 2;
   */
  includePrivateDocs: boolean;

  /**
   * @generated from field: string user_id = 3;
   */
  userId: string;

  /**
   * @generated from field: string user_email = 4;
   */
  userEmail: string;

  constructor(data?: PartialMessage<SearchRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.SearchRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SearchRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SearchRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SearchRequest;

  static equals(a: SearchRequest | PlainMessage<SearchRequest> | undefined, b: SearchRequest | PlainMessage<SearchRequest> | undefined): boolean;
}

/**
 * @generated from message glean.Person
 */
export declare class Person extends Message<Person> {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string id = 2;
   */
  id: string;

  constructor(data?: PartialMessage<Person>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.Person";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Person;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Person;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Person;

  static equals(a: Person | PlainMessage<Person> | undefined, b: Person | PlainMessage<Person> | undefined): boolean;
}

/**
 * @generated from message glean.Document
 */
export declare class Document extends Message<Document> {
  /**
   * @generated from field: string document_id = 1;
   */
  documentId: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * @generated from field: glean.DocumentType document_type = 4;
   */
  documentType: DocumentType;

  /**
   * @generated from field: glean.DataSource data_source = 5;
   */
  dataSource: DataSource;

  /**
   * @generated from field: string url = 6;
   */
  url: string;

  /**
   * @generated from field: repeated glean.Document children = 7;
   */
  children: Document[];

  /**
   * @generated from field: glean.Person author = 8;
   */
  author?: Person;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 9;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 10;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: glean.Document.Visibility visibility = 11;
   */
  visibility: Document_Visibility;

  /**
   * @generated from field: repeated glean.Document.Snippet snippets = 12;
   */
  snippets: Document_Snippet[];

  /**
   * @generated from field: string data_source_name = 13;
   */
  dataSourceName: string;

  constructor(data?: PartialMessage<Document>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.Document";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Document;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Document;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Document;

  static equals(a: Document | PlainMessage<Document> | undefined, b: Document | PlainMessage<Document> | undefined): boolean;
}

/**
 * @generated from enum glean.Document.Visibility
 */
export declare enum Document_Visibility {
  /**
   * @generated from enum value: UNSPECIFIED_VISIBILITY = 0;
   */
  UNSPECIFIED_VISIBILITY = 0,

  /**
   * @generated from enum value: PUBLIC_LINK = 1;
   */
  PUBLIC_LINK = 1,

  /**
   * @generated from enum value: PUBLIC_VISIBLE = 2;
   */
  PUBLIC_VISIBLE = 2,

  /**
   * @generated from enum value: DOMAIN_VISIBLE = 3;
   */
  DOMAIN_VISIBLE = 3,

  /**
   * @generated from enum value: DOMAIN_LINK = 4;
   */
  DOMAIN_LINK = 4,

  /**
   * @generated from enum value: PRIVATE = 5;
   */
  PRIVATE = 5,

  /**
   * @generated from enum value: SPECIFIC_PEOPLE_AND_GROUPS = 6;
   */
  SPECIFIC_PEOPLE_AND_GROUPS = 6,
}

/**
 * @generated from message glean.Document.Snippet
 */
export declare class Document_Snippet extends Message<Document_Snippet> {
  /**
   * @generated from field: string text = 1;
   */
  text: string;

  /**
   * @generated from field: int32 start_range = 2;
   */
  startRange: number;

  /**
   * @generated from field: int32 end_range = 3;
   */
  endRange: number;

  /**
   * @generated from field: string document_id = 4;
   */
  documentId: string;

  constructor(data?: PartialMessage<Document_Snippet>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.Document.Snippet";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Document_Snippet;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Document_Snippet;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Document_Snippet;

  static equals(a: Document_Snippet | PlainMessage<Document_Snippet> | undefined, b: Document_Snippet | PlainMessage<Document_Snippet> | undefined): boolean;
}

/**
 * @generated from message glean.SearchResponse
 */
export declare class SearchResponse extends Message<SearchResponse> {
  /**
   * @generated from field: repeated glean.Document documents = 1;
   */
  documents: Document[];

  /**
   * @generated from field: string oauth_url = 2;
   */
  oauthUrl: string;

  /**
   * @generated from field: int32 status_code = 3;
   */
  statusCode: number;

  constructor(data?: PartialMessage<SearchResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.SearchResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SearchResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SearchResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SearchResponse;

  static equals(a: SearchResponse | PlainMessage<SearchResponse> | undefined, b: SearchResponse | PlainMessage<SearchResponse> | undefined): boolean;
}

/**
 * @generated from message glean.GenerateNewKeyPairRequest
 */
export declare class GenerateNewKeyPairRequest extends Message<GenerateNewKeyPairRequest> {
  constructor(data?: PartialMessage<GenerateNewKeyPairRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.GenerateNewKeyPairRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenerateNewKeyPairRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenerateNewKeyPairRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenerateNewKeyPairRequest;

  static equals(a: GenerateNewKeyPairRequest | PlainMessage<GenerateNewKeyPairRequest> | undefined, b: GenerateNewKeyPairRequest | PlainMessage<GenerateNewKeyPairRequest> | undefined): boolean;
}

/**
 * @generated from message glean.GenerateNewKeyPairResponse
 */
export declare class GenerateNewKeyPairResponse extends Message<GenerateNewKeyPairResponse> {
  /**
   * @generated from field: string public_key = 1;
   */
  publicKey: string;

  constructor(data?: PartialMessage<GenerateNewKeyPairResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.GenerateNewKeyPairResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenerateNewKeyPairResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenerateNewKeyPairResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenerateNewKeyPairResponse;

  static equals(a: GenerateNewKeyPairResponse | PlainMessage<GenerateNewKeyPairResponse> | undefined, b: GenerateNewKeyPairResponse | PlainMessage<GenerateNewKeyPairResponse> | undefined): boolean;
}

/**
 * @generated from message glean.CallGleanApiRequest
 */
export declare class CallGleanApiRequest extends Message<CallGleanApiRequest> {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  /**
   * @generated from field: glean.HttpMethod method = 2;
   */
  method: HttpMethod;

  /**
   * @generated from field: string payload = 3;
   */
  payload: string;

  constructor(data?: PartialMessage<CallGleanApiRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.CallGleanApiRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CallGleanApiRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CallGleanApiRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CallGleanApiRequest;

  static equals(a: CallGleanApiRequest | PlainMessage<CallGleanApiRequest> | undefined, b: CallGleanApiRequest | PlainMessage<CallGleanApiRequest> | undefined): boolean;
}

/**
 * @generated from message glean.CallGleanApiResponse
 */
export declare class CallGleanApiResponse extends Message<CallGleanApiResponse> {
  /**
   * @generated from field: string response = 1;
   */
  response: string;

  /**
   * @generated from field: int32 status_code = 2;
   */
  statusCode: number;

  constructor(data?: PartialMessage<CallGleanApiResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.CallGleanApiResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CallGleanApiResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CallGleanApiResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CallGleanApiResponse;

  static equals(a: CallGleanApiResponse | PlainMessage<CallGleanApiResponse> | undefined, b: CallGleanApiResponse | PlainMessage<CallGleanApiResponse> | undefined): boolean;
}

/**
 * @generated from message glean.IsToolConfiguredRequest
 */
export declare class IsToolConfiguredRequest extends Message<IsToolConfiguredRequest> {
  /**
   * @generated from field: string user_id = 1 [deprecated = true];
   * @deprecated
   */
  userId: string;

  constructor(data?: PartialMessage<IsToolConfiguredRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.IsToolConfiguredRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IsToolConfiguredRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IsToolConfiguredRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IsToolConfiguredRequest;

  static equals(a: IsToolConfiguredRequest | PlainMessage<IsToolConfiguredRequest> | undefined, b: IsToolConfiguredRequest | PlainMessage<IsToolConfiguredRequest> | undefined): boolean;
}

/**
 * @generated from message glean.IsToolConfiguredResponse
 */
export declare class IsToolConfiguredResponse extends Message<IsToolConfiguredResponse> {
  /**
   * @generated from field: bool is_configured = 1;
   */
  isConfigured: boolean;

  constructor(data?: PartialMessage<IsToolConfiguredResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "glean.IsToolConfiguredResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): IsToolConfiguredResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): IsToolConfiguredResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): IsToolConfiguredResponse;

  static equals(a: IsToolConfiguredResponse | PlainMessage<IsToolConfiguredResponse> | undefined, b: IsToolConfiguredResponse | PlainMessage<IsToolConfiguredResponse> | undefined): boolean;
}


import concurrent.futures
import logging
import time
from abc import ABC, abstractmethod
from typing import Any, Iterable, Literal

import opentelemetry.trace
import requests
from google.protobuf.timestamp_pb2 import Timestamp
from pydantic import SecretStr
from requests_oauthlib import TokenUpdated

from services.integrations.glean import glean_pb2
from services.integrations.glean.client.client import HttpMethodStr
from services.integrations.glean.server.glean_auth_processor import (
    GleanAuthenticationRequired,
    GleanAuthError,
    GleanAuthProcessor,
    GleanTenantNotConfigured,
)
from services.integrations.glean.server.glean_jwt_client import GleanJWTClient
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext

MAX_CONTENT_SIZE = 1024 * 16  # 16KB
DEFAULT_TIMEOUT = 120  # 120 seconds

# based on documentation here https://developers.glean.com/client/operation/search/
# if this documentation changes, we should update this list
VISIBILITY_IS_PUBLIC = {
    glean_pb2.Document.Visibility.PUBLIC_LINK: True,
    glean_pb2.Document.Visibility.PUBLIC_VISIBLE: True,
    glean_pb2.Document.Visibility.DOMAIN_VISIBLE: True,
    glean_pb2.Document.Visibility.DOMAIN_LINK: True,
    glean_pb2.Document.Visibility.PRIVATE: False,
    glean_pb2.Document.Visibility.SPECIFIC_PEOPLE_AND_GROUPS: False,
    glean_pb2.Document.Visibility.UNSPECIFIED_VISIBILITY: False,  # treat unknown visibility as private
}

tracer = opentelemetry.trace.get_tracer(__name__)


def map_data_source(source: str) -> glean_pb2.DataSource.ValueType:
    source_lower = source.lower()
    if source_lower == "slack":
        return glean_pb2.DataSource.SLACK
    elif source_lower == "notion":
        return glean_pb2.DataSource.NOTION
    elif source_lower == "linear":
        return glean_pb2.DataSource.LINEAR
    else:
        if source:
            logging.info(f"Unknown data source: {source}.")
        return glean_pb2.DataSource.UNSPECIFIED_DATA_SOURCE


def map_document_type(doc_type: str) -> glean_pb2.DocumentType.ValueType:
    doc_type_lower = doc_type.lower()
    if doc_type_lower == "page":
        return glean_pb2.DocumentType.PAGE
    elif doc_type_lower == "message":
        return glean_pb2.DocumentType.MESSAGE
    elif doc_type_lower == "channel":
        return glean_pb2.DocumentType.CHANNEL
    elif doc_type_lower == "conversation":
        return glean_pb2.DocumentType.CONVERSATION
    elif doc_type_lower == "issue":
        return glean_pb2.DocumentType.ISSUE
    else:
        if doc_type:
            logging.info(f"Unknown document type: {doc_type}.")
        return glean_pb2.DocumentType.UNSPECIFIED_DOCUMENT_TYPE


def convert_iso_time_to_proto(iso_time: str | None) -> Timestamp | None:
    """Convert ISO8601 time to protobuf Timestamp."""
    if not iso_time:
        return None
    ts = Timestamp()
    ts.FromJsonString(iso_time)
    return ts


def map_visibility(visibility: str) -> glean_pb2.Document.Visibility.ValueType:
    """Convert string visibility to enum value."""
    visibility_upper = visibility.upper()
    try:
        return glean_pb2.Document.Visibility.Value(visibility_upper)
    except ValueError:
        logging.warning("Unknown visibility type: %s", visibility)
        return glean_pb2.Document.Visibility.UNSPECIFIED_VISIBILITY


class GleanApiClient(ABC):
    """Helper class for interacting with the Glean API will reducing
    boilerplate for handling auth based on tenant configuration."""

    @abstractmethod
    def make_request(
        self,
        path: str,
        method: HttpMethodStr,
        payload: dict,
        timeout: int = DEFAULT_TIMEOUT,
        **kwargs,
    ) -> dict:
        """Make a request to Glean API with support for both OAuth and JWT authentication.

        Args:
            request_context: RequestContext
            path: API endpoint path (e.g., "search")
            method: HTTP method ("GET" or "POST")
            payload: Request payload
            timeout: Request timeout in seconds

        Returns:
            JSON response from the API

        Raises:
            GleanAuthError: When authentication is needed or the tenant is not configured
            requests.exceptions.HTTPError: For non-2xx HTTP responses
        """
        ...


class GleanJwtApiClient(GleanApiClient):
    """Glean API client that uses JWT for authentication.

    This object is meant to be constructed per request as it requires context from
    a single request to the Glean server.
    """

    def __init__(
        self,
        jwt_client: GleanJWTClient,
        request_context: RequestContext,
        tenant_id: str,
        user_email: SecretStr,
    ):
        self.jwt_client = jwt_client
        self.request_context = request_context
        self.tenant_id = tenant_id
        self.user_email = user_email

    def make_request(
        self,
        path: str,
        method: HttpMethodStr,
        payload: dict,
        timeout: int = DEFAULT_TIMEOUT,
        **kwargs,
    ) -> dict:
        return self.jwt_client.make_jwt_glean_request(
            self.request_context,
            self.tenant_id,
            path,
            method,
            self.user_email,
            payload,
            timeout,
        )


class GleanOAuthApiClient(GleanApiClient):
    """Glean API client that uses OAuth for authentication.

    Constructs a single OAuth2Session which it will use for all requests.
    """

    def __init__(
        self,
        auth_processor: GleanAuthProcessor,
        request_context: RequestContext,
        tenant_id: str,
        user_id: str,
    ):
        self.session = auth_processor.get_oauth_session(
            request_context, tenant_id, user_id
        )
        self.auth_processor = auth_processor
        self.base_url = auth_processor.get_tenant_api_url(tenant_id, request_context)
        self.request_context = request_context
        self.tenant_id = tenant_id
        self.user_id = user_id

    def make_request(
        self,
        path: str,
        method: HttpMethodStr,
        payload: dict,
        timeout: int = DEFAULT_TIMEOUT,
        max_retries: int = 1,
        retry_delay: float = 0.2,
        **kwargs,
    ) -> dict:
        # These variables are never set, so it's safe to alias them
        # to reduce verbosity of the code below.
        request_context = self.request_context
        tenant_id = self.tenant_id
        user_id = self.user_id
        url = self.base_url.rstrip("/") + "/" + path.lstrip("/")
        session = self.session

        if method != "POST":
            raise ValueError("Only POST is supported for OAuth client")

        # This is the old retry + token refresh request state machine from
        # GleanSearchClient._make_glean_request_with_oauth. For now it's just
        # copied over to match the old logic, although long-term we'd ideally
        # separate the concerns of token refreshing from retry logic.
        for attempt in range(max_retries):
            try:
                try:
                    response = session.post(url, json=payload, timeout=timeout)
                except TokenUpdated as e:
                    # if the library automatically tries updating the token based on expires in time, save the new token
                    logging.info("Token updated automatically, saving new token")

                    self.auth_processor.save_oauth_token(
                        request_context, session, tenant_id, user_id, e.token
                    )
                    # Retry the request immediately with new token
                    response = session.post(url, json=payload, timeout=timeout)

                if response.status_code == 401:
                    logging.warning("Authentication failed")
                    if not session.auto_refresh_url or not session.token.get(
                        "refresh_token"
                    ):
                        raise GleanAuthenticationRequired(
                            self.auth_processor.get_oauth_url(
                                request_context, session=session
                            )
                        )
                    try:
                        logging.info("Refreshing token")
                        new_token = session.refresh_token(
                            token_url=session.auto_refresh_url,
                            refresh_token=session.token.get("refresh_token"),
                            **session.auto_refresh_kwargs,
                        )
                        self.auth_processor.save_oauth_token(
                            request_context, session, tenant_id, user_id, new_token
                        )
                    except Exception as e:
                        # TODO: if this fails, maybe wipe the current user settings?
                        logging.warning("Token refresh failed: %s", str(e))
                        raise GleanAuthenticationRequired(
                            self.auth_processor.get_oauth_url(
                                request_context, session=session
                            )
                        ) from e

                    try:
                        # Retry the request immediately with new token
                        response = session.post(url, json=payload, timeout=timeout)
                        if response.status_code == 401:  # If refresh didn't help
                            raise GleanAuthenticationRequired(
                                self.auth_processor.get_oauth_url(
                                    request_context, session=session
                                )
                            )
                        elif (
                            response.status_code == 429
                        ):  # Let the main retry loop handle rate limiting
                            raise requests.exceptions.HTTPError(
                                response=response, request=response.request
                            )
                        response.raise_for_status()
                        return response.json()

                    except (
                        requests.exceptions.HTTPError
                    ):  # Pass through rate limit errors
                        logging.warning("Rate limited after token refresh")
                        raise

                response.raise_for_status()
                return response.json()

            except requests.exceptions.HTTPError as e:
                if (
                    e.response is not None
                    and e.response.status_code == 429
                    and attempt < max_retries - 1
                ):
                    logging.warning("Response headers: %s", e.response.headers)
                    sleep_time = retry_delay * (2**attempt)
                    logging.warning("Rate limited. Retrying in %s seconds.", sleep_time)
                    time.sleep(sleep_time)
                    continue
                else:
                    logging.error("HTTP error occurred: %s", str(e))
                    raise

            except requests.exceptions.RequestException as e:
                logging.error("Request failed: %s", str(e))
                raise

        raise requests.exceptions.RequestException("Max retries reached")


def get_glean_api_client(
    auth_processor: GleanAuthProcessor,
    jwt_client: GleanJWTClient,
    request_context: RequestContext,
    tenant_id: str,
    user_email: SecretStr,
    user_id: SecretStr,
) -> "GleanApiClient":
    """Get a Glean API client for the given tenant and user."""
    tenant_type = auth_processor.get_tenant_type(request_context)
    if tenant_type == "oauth":
        return GleanOAuthApiClient(
            auth_processor, request_context, tenant_id, user_id.get_secret_value()
        )
    elif tenant_type == "jwt":
        return GleanJwtApiClient(jwt_client, request_context, tenant_id, user_email)
    else:
        raise GleanTenantNotConfigured()


class GleanSearchClient:
    """Client for using Glean's Search and Document APIs.

    Authenticates to Glean's API using either OAuth or JWT authentication based on
    tenant configuration stored in the settings service.
    """

    def __init__(self, auth_processor: GleanAuthProcessor, jwt_client: GleanJWTClient):
        self.auth_processor = auth_processor
        self.jwt_client = jwt_client

    @staticmethod
    def populate_document(
        raw_result: dict,
        visibility: glean_pb2.Document.Visibility.ValueType | None = None,
    ) -> glean_pb2.Document:
        """Populate a Document object from a raw result dict.

        Args:
            raw_result: Raw result dict from the Glean API
            visibility: If specified, override the visibility of the document.
                Nested documents do not have visibility metadata.  So we need
                to use this argument to populate children visibility using the
                parent visibility.

        Returns:
            A Document object
        """
        doc = raw_result.get("document", {})
        metadata = doc.get("metadata", {})
        author = metadata.get("author", {})
        created_at = convert_iso_time_to_proto(metadata.get("createTime", None))
        updated_at = convert_iso_time_to_proto(metadata.get("updateTime", None))

        doc_id = doc.get("id", raw_result.get("id", ""))
        if visibility is None:
            visibility_str = metadata.get("visibility", "")
            if not visibility_str:
                logging.warning(
                    "No visibility specified for document %s, defaulting to UNSPECIFIED_VISIBILITY",
                    doc_id,
                )
                visibility_str = "UNSPECIFIED_VISIBILITY"
            visibility = map_visibility(visibility_str)
            if visibility == glean_pb2.Document.Visibility.UNSPECIFIED_VISIBILITY:
                logging.warning(
                    "Unknown visibility type %s from document %s",
                    visibility_str,
                    doc_id,
                )

        snippets = []
        for snippet in raw_result.get("snippets", []):
            snippets.append(
                glean_pb2.Document.Snippet(
                    text=snippet.get("text", ""),
                    document_id=doc_id,
                )
            )

        # Return a Document object
        return glean_pb2.Document(
            document_id=doc_id,
            data_source=map_data_source(doc.get("datasource", "")),
            document_type=map_document_type(doc.get("docType", "")),
            title=doc.get("title", raw_result.get("title", "")),
            url=doc.get("url", raw_result.get("url", "")),
            children=[],
            author=glean_pb2.Person(
                name=author.get("name", ""),
                id=author.get("obfuscatedId", ""),
            ),
            created_at=created_at,
            updated_at=updated_at,
            visibility=visibility,
            snippets=snippets,
            data_source_name=doc.get("datasource", ""),
        )

    def search(
        self,
        request_context: RequestContext,
        tenant_id: str,
        user_id: str,
        user_email: SecretStr,
        queries: list[str],
        max_results: int = 20,
        max_conversations: int = 5,
        timeout: int = DEFAULT_TIMEOUT,
        include_private_docs: bool = False,
        max_snippet_chars: int = 100,
        request_interval: float = 0.1,
    ) -> list[glean_pb2.Document]:
        """Search for relevant Glean documents based on a list of queries.

        The list of queries is simply concatenated into a single query string.
        Based on how elasticsearch should search works, this will allow results
        from all searches to show but those that match more will be higher in
        the list.  Did limited tests and verified this to work on some test cases.

        Args:
            request_context: RequestContext
            tenant_id: Tenant ID
            user_id: User ID
            user_email: User email (must be passed when using JWT auth)
            queries: A list of search query strings
            max_results: Maximum number of results to return
            max_conversations: Maximum number of conversations to return
            timeout: Timeout for the HTTP request in seconds
            include_private_docs: Whether to include private documents in the search results
            max_snippet_chars: Maximum number of characters in each snippet
            request_interval: Time interval between requests in seconds

        Returns:
            List of GleanSearchResult objects

        Raises:
        GleanAuthError: When user needs to authenticate or the tenant is not configured
        requests.exceptions.RequestException: For other request-related errors
        """
        client = get_glean_api_client(
            self.auth_processor,
            self.jwt_client,
            request_context,
            tenant_id,
            user_email,
            SecretStr(user_id),
        )

        # Joining multiple queries instead of just picking one here.
        # This is effectively identical to running N searches then combine the results.
        # It has a wider coverage than asking Claude to generate 1 query.
        query = "\n".join(queries)
        with tracer.start_as_current_span("glean_search_client.search") as span:
            payload = {
                "query": query,
                "pageSize": max_results,
                "maxSnippetSize": max_snippet_chars,
            }

            path = "search"

            # Use the generalized request method
            response = client.make_request(
                path,
                "POST",
                payload,
                timeout=timeout,
            )

            # Sometimes results might have duplicates, so we need to dedupe with ID
            results = {}
            result_order = []
            for item in response["results"]:
                result = self.populate_document(item)

                # Filter out private documents if requested
                # Treats any unknown visibility as private
                if not include_private_docs and not VISIBILITY_IS_PUBLIC.get(
                    result.visibility, False
                ):
                    continue

                if result.document_id in results:
                    # keep the document with more recent updated timestamp
                    if (
                        result.updated_at.ToDatetime()
                        > results[result.document_id].updated_at.ToDatetime()
                    ):
                        logging.info(
                            "Keeping a newer version for document with duplicates"
                            "id: %s, old timestamp: %s, new timestamp: %s",
                            result.document_id,
                            results[result.document_id].updated_at.ToDatetime(),
                            result.updated_at.ToDatetime(),
                        )
                        results[result.document_id] = result
                else:
                    results[result.document_id] = result
                    result_order.append(result.document_id)
            span.set_attribute("num_results", len(results))

            # sort results.  dupes may be out of place.
            results = {doc_id: results[doc_id] for doc_id in result_order}

            # Use thread pool to submit _get_contents() and _get_conversation() calls in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                contents_f = executor.submit(
                    _get_contents,
                    client,
                    results.values(),
                    timeout,
                )
                time.sleep(request_interval)
                conversation_f = []
                for result in results.values():
                    if result.document_type == glean_pb2.DocumentType.CONVERSATION:
                        if conversation_f:
                            time.sleep(request_interval)
                        future = executor.submit(
                            _get_conversation,
                            client,
                            result,
                            timeout,
                        )
                        conversation_f.append((result.document_id, future))
                        if len(conversation_f) >= max_conversations:
                            break

                # wait for all futures to complete
                try:
                    contents_f.result()
                except GleanAuthError:
                    # Propagate auth errors up
                    raise
                except Exception as e:
                    logging.warning(
                        "Failed to get document content for documents: %s. Error: %s.",
                        results.keys(),
                        e,
                    )

                for doc_id, future in conversation_f:
                    try:
                        new_doc = future.result()
                    except GleanAuthError:
                        # Propagate auth errors up
                        raise
                    except Exception as e:
                        logging.warning(
                            "Failed to get conversation for document ID: %s. Error: %s.",
                            doc_id,
                            e,
                        )
                        continue
                    if new_doc and (new_doc.children or new_doc.content):
                        results[doc_id] = new_doc

            # Return results in the order they were originally returned by the search API
            return [self.mark_snippet_ranges(item) for item in results.values()]

    def mark_snippet_ranges(self, document: glean_pb2.Document) -> glean_pb2.Document:
        """Mark the start and end range of each snippet in the document content."""
        if not document.snippets:
            return document

        # place snippets on each document
        for snippet in document.snippets:
            if snippet.document_id != document.document_id:
                logging.info(
                    "Snippet document ID does not match document ID: %s vs %s",
                    snippet.document_id,
                    document.document_id,
                )
                continue
            start_index = document.content.find(snippet.text)
            if start_index == -1:
                continue
            snippet.start_range = start_index
            snippet.end_range = start_index + len(snippet.text)
        return document

    def truncate_content_size(self, document: glean_pb2.Document) -> glean_pb2.Document:
        """Truncate content and messages to MAX_CONTENT_SIZE if it exceeds the limit."""
        if (
            document.content
            and len(document.content.encode("utf-8")) > MAX_CONTENT_SIZE
        ):
            document.content = document.content[:MAX_CONTENT_SIZE]
            logging.warning(
                "Truncated content of %s to %d characters.",
                document.document_id,
                MAX_CONTENT_SIZE,
            )
        total_message_size = 0
        for i, message in enumerate(document.children):
            message_content_size = (
                len(message.author.name.encode("utf-8"))
                + len(message.content.encode("utf-8"))
                + 2
            )
            if total_message_size + message_content_size > MAX_CONTENT_SIZE:
                budget = (
                    MAX_CONTENT_SIZE - total_message_size - 2 - len(message.author.name)
                )
                if budget > 10:
                    message.content = message.content[:budget] + "..."
                    end = i + 1
                else:
                    end = i
                logging.warning(
                    "Truncated conversation from %d messages to %d messages.",
                    len(document.children),
                    end,
                )
                while len(document.children) > end:
                    document.children.pop()
                break
            total_message_size += message_content_size
        return document

    def call_api(
        self,
        request_context: RequestContext,
        auth_info: AuthInfo,
        method: Literal["GET", "POST"],
        path: str,
        data: dict = {},
    ) -> dict:
        """Make a POST request to a Glean API endpoint.

        Args:
            request_context: RequestContext
            auth_info: AuthInfo for the user + tenant making the request.
            path: API endpoint path (e.g.: "/search").
            data: Request JSON payload.

        Returns:
            JSON response from the API

        Raises:
            GleanAuthError: When authentication is needed or the tenant is not configured
            requests.exceptions.HTTPError: For non-2xx HTTP responses
            requests.exceptions.RequestException: For non-recoverable request errors
        """
        if not auth_info.tenant_id:
            raise ValueError("Tenant ID is required")

        if not auth_info.user_email:
            raise ValueError("User email not set in auth info while calling Glean API")

        if not auth_info.opaque_user_id:
            # The customer UI frontend uses the user's Augment opaque ID when registering
            # Glean tokens in backend. So to fetch them we also use opaque user ID.
            raise ValueError(
                "Opaque user ID not set in auth info while calling Glean API"
            )

        client = get_glean_api_client(
            self.auth_processor,
            self.jwt_client,
            request_context,
            auth_info.tenant_id,
            auth_info.user_email,
            SecretStr(auth_info.opaque_user_id),
        )

        # Use the generalized request method
        response = client.make_request(
            path,
            method,
            data,
            timeout=DEFAULT_TIMEOUT,
        )

        return response


def _get_contents(
    client: GleanApiClient,
    documents: Iterable[glean_pb2.Document],
    timeout: int = DEFAULT_TIMEOUT,
) -> None:
    """Extract contents for a set of documents.

    This method edits all of the documents in place to add content.
    """
    doc_map = {item.document_id: item for item in documents}
    logging.info("Extracting content for: %s", list(doc_map))
    if not doc_map:
        return
    # This is a normal document.  Use getdocuments endpoint to get the contents
    with tracer.start_as_current_span("glean_search_client.get_document") as span:
        span.set_attribute("document_ids", list(doc_map.keys()))
        path = "getdocuments"
        payload = {
            "documentSpecs": [{"id": doc_id} for doc_id in doc_map.keys()],
            "includeFields": ["DOCUMENT_CONTENT"],
        }
        try:
            # Use the generalized request method
            resp_doc = client.make_request(
                path,
                "POST",
                payload,
                timeout,
                max_retries=3,
                retry_delay=0.2,
            )
        except GleanAuthError:
            # Re-raise the auth exception so it can be handled by the initial Search call to eventually return the oauth url (stored in the exception.oauth_url) back to the user
            # If any Glean call fails with an auth error, we should not return any results to the user and have them re-authenticate
            raise
        except requests.exceptions.RequestException as e:
            logging.error("An error occurred while making the request: %s", str(e))
            return

        if "documents" not in resp_doc or not resp_doc["documents"]:
            logging.error("getdocuments did not return any documents.")
            return

        missing = set(doc_map)
        for doc_id, doc in resp_doc["documents"].items():
            missing.remove(doc_id)
            if "content" not in doc:
                # Some documents don't have content - ex. linear tickets without any description or comments
                logging.warning(
                    "Get document response does not have content for %s.", doc_id
                )
                continue
            logging.info("Extracted content for %s", doc_id)
            content = doc["content"]
            if "fullTextList" in content and content["fullTextList"]:
                doc_map[doc_id].content = "\n".join(content["fullTextList"])
            elif "fullText" in content and content["fullText"]:
                doc_map[doc_id].content = content["fullText"]
            else:
                logging.info(
                    "Cannot find content for doc %s. data size per key: %s",
                    doc_id,
                    {k: len(v) for k, v in content.items()},
                )

                logging.warning(
                    "Failed to get full text from document content: %s", doc_id
                )
        if missing:
            logging.warning("Did not return content for %s", ", ".join(missing))


def _get_conversation(
    client: GleanApiClient,
    document: glean_pb2.Document,
    timeout: int = DEFAULT_TIMEOUT,
) -> glean_pb2.Document:
    """Get a message by ID. Uses existing session with auth headers.

    This method does NOT modify documents in place.  Instead, it returns a
    new document object with conversations inside.
    """
    with tracer.start_as_current_span("glean_search_client._get_conversation") as span:
        document_id = document.document_id
        data_source = glean_pb2.DataSource.Name(document.data_source).upper()
        span.set_attribute("document_id", document_id)
        span.set_attribute(
            "data_source", glean_pb2.DataSource.Name(document.data_source)
        )
        url = document.url.split("?")[0]
        if "/p" in url:
            message_timestamp = int(url.rsplit("/p")[-1])
        else:
            logging.warning("No timestamp from url: %s.", document.url)
            new_doc = glean_pb2.Document()
            new_doc.CopyFrom(document)
            return new_doc

        path = "messages"
        payload = {
            "id": document_id,
            "idType": "CONVERSATION_ID",
            "timestampMillis": message_timestamp,
            "datasource": data_source,
            "includeRootMessage": True,
        }
        logging.info("Extracting conversation for %s", document_id)

        # Use the generalized request method
        resp_doc = client.make_request(
            path,
            "POST",
            payload,
            timeout,
        )

        if "searchResponse" not in resp_doc:
            logging.error("Malformed response: %s", resp_doc)
            new_doc = glean_pb2.Document()
            new_doc.CopyFrom(document)
            return new_doc
        for item in resp_doc["searchResponse"].get("results", []):
            # Full text of the parent question is found in JSON path
            # searchResults.results.0.title
            # Note: this document also appears to be in the following path
            # `searchResults.results.0.relatedResults.0.results.0.fullText`
            # But this is the wrong place! Despite the field name `fullText`,
            # it is not the full text; the full text is in the title field.
            # This endpoint does not return the real title; but we do have
            # the channel name and author name.
            document = GleanSearchClient.populate_document(item)
            document.document_type = glean_pb2.DocumentType.CONVERSATION
            channel = item.get("document", {}).get("metadata", {}).get("container", "")
            author_name = document.author.name
            if channel:
                document.title = f"Conversation in channel {channel} by {author_name}"
            else:
                document.title = f"Conversation by {author_name}"
            # The other messages excluding the original in JSON path
            # searchResults.results.0.relatedResults.0.results.0.relatedResults.0.results
            root_item = item.get("relatedResults", [])
            if not root_item:
                logging.warning(
                    "No related results found for conversation ID: %s.", document_id
                )
                break
            results = root_item[0].get("results", [{}])
            root_object = results[0]
            if "document" in root_object:
                logging.info(
                    "root object id: %s", root_object["document"].get("id", "")
                )
            # sometimes the actual replies are nested in the relatedResults field of this
            # relatedResult field, sometimes it is not
            # if it is nested, then related result list does not contain the original message
            # but if it is not nested, then the related result list contains the original message
            # as its first element
            if "relatedResults" in root_object:
                document.content = root_object.get(
                    "title", root_object.get("fullText", "")
                )
                repies = root_object.get("relatedResults", [{}])[0].get("results", [])
            else:
                document.content = results[0].get(
                    "fullText", root_object.get("fullText", "")
                )
                repies = results[1:]  # the first message is the root and needs skipping
            if not repies:
                logging.warning(
                    "No replies found for conversation ID: %s.", document_id
                )
                break
            # This is very twisted, but the full path of the replies is
            # searchResults.results.0.relatedResults.0.results.0.relatedResults.0.results
            for reply in repies:
                message = GleanSearchClient.populate_document(
                    reply, visibility=document.visibility
                )
                message.content = reply.get("fullText", "")
                message.data_source = document.data_source
                message.document_type = glean_pb2.DocumentType.MESSAGE
                document.children.append(message)
        return document

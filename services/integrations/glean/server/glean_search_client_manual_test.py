"""Manual test meant to be run from the commandline through <PERSON><PERSON> for
testing the GleanJWTClient.

This utility tests the JWT authentication flow for Glean API.

You'll need to store the JSON formatted private key somewhere in your filesystem
for this script to access.

To download the latest version of the dev Glean private key info JSON for our Augment Glean instance:

gcloud secrets versions access projects/1035750215372/secrets/dev-glean-private-key/versions/latest --out-file=/tmp/glean-key.json

(Note: since we've now switched to generating key pairs per tenant, the most reliable place to get the private key
will be from the settings service in the dogfood tenant).

Example private key info file:
```
{
    "algorithm": "ES256",
    "key_id": "augment-kid-0",
    "key": <PEM encoded private key>,
}
```

To run this test:

bazel run //services/integrations/glean/server:glean_search_client_manual_test -- \
    --private-key-info-path=/tmp/glean-key.json \
    --user-email=<EMAIL> \
    --test-endpoint=search \
    --search-query="Onboarding information"

Available test endpoints:
- token: Generate and print a JWT token
- headers: Generate and print authentication headers
- search: Make a search request to Glean API
"""

import argparse
import json
import logging
import sys
from pathlib import Path

from pydantic import BaseModel, SecretStr

from services.integrations.glean.agent_tools.glean_tools import (
    clean_api_response,
    default_response_remove_keys,
    default_response_select_keys,
)
from services.integrations.glean.server.config import GleanJWTConfig, PrivateKeyInfo
from services.integrations.glean.server.glean_jwt_client import GleanJWTClient
from services.lib.request_context.request_context import RequestContext
from services.settings import settings_pb2
from services.settings.client.client import SettingsClient


class MockSettingsClient(SettingsClient):
    """Mock settings client for testing purposes."""

    def __init__(self, glean_base_url, private_key_info: PrivateKeyInfo):
        """Initialize with a fixed Glean domain."""
        # Initialize parent with dummy values
        super().__init__("dummy_endpoint", None)
        self.glean_base_url = glean_base_url
        self.private_key_info = private_key_info
        # Replace the stub with our mock implementation
        self.stub = self

    def GetTenantSettings(self, request, metadata=None, timeout=None):
        """Return mock tenant settings with the configured Glean domain."""
        settings = settings_pb2.TenantSettings(
            glean_tenant_settings=settings_pb2.GleanTenantSettings(
                glean_domain=self.glean_base_url,
                glean_jwt_key_pair_info=settings_pb2.GleanJWTKeyPairInfo(
                    algorithm=self.private_key_info.algorithm,
                    key_id=self.private_key_info.key_id,
                    private_key=self.private_key_info.key,
                    # Public key doesn't matter for this test, but needs
                    # to be set to some value so that client doesn't reject
                    # settings as missing.
                    public_key="fakevalue",
                ),
            )
        )
        return settings_pb2.GetTenantSettingsResponse(settings=settings)


def test_generate_jwt(
    jwt_client: GleanJWTClient, private_key_info: PrivateKeyInfo, glean_domain: str
):
    """Test JWT token generation."""
    try:
        token = jwt_client.generate_jwt(
            private_key_info=private_key_info, glean_domain=glean_domain
        )
        print("\nGenerated JWT token:")
        print(token)
        print("\nToken generation successful!")
        return token
    except Exception as e:
        print(f"\nError generating JWT token: {e}")
        sys.exit(1)


def test_get_auth_headers(
    jwt_client: GleanJWTClient,
    private_key_info: PrivateKeyInfo,
    glean_domain: str,
    user_email: str,
):
    """Test getting authentication headers."""
    try:
        headers = jwt_client.get_auth_headers(
            private_key_info=private_key_info,
            glean_domain=glean_domain,
            user_email=SecretStr(user_email),
        )
        print("\nGenerated authentication headers:")
        print(json.dumps(headers, indent=2))
        print("\nHeader generation successful!")
        return headers
    except Exception as e:
        print(f"\nError generating authentication headers: {e}")
        sys.exit(1)


def test_search_request(
    jwt_client,
    request_context,
    user_email,
    search_query,
    clean_response=True,
    *,
    allow_keys: list[str] | None = None,
    block_keys: list[str] | None = None,
):
    """Test making a search request to Glean API."""
    try:
        # Simple search query
        payload = {
            "query": search_query,
            "maxSnippetSize": 10,
            "pageSize": 4,
        }

        response = jwt_client.make_jwt_glean_request(
            request_context=request_context,
            tenant_id="faketenantid",
            path="search",
            method="POST",
            user_email=SecretStr(user_email),
            payload=payload,
            timeout=30,
        )

        if clean_response:
            response = clean_api_response(
                response,
                select_keys=allow_keys if allow_keys else default_response_select_keys,
                remove_keys=block_keys if block_keys else default_response_remove_keys,
            )

        print("\nSearch response:")
        print(json.dumps(response, indent=2))
        print("\nSearch request successful!")
        return response
    except Exception as e:
        print(f"\nError making search request: {e}")
        sys.exit(1)


def main():
    """Run the manual test for GleanJWTClient."""
    # Set up logging
    logging.basicConfig(level=logging.INFO)

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Test Glean JWT authentication flow")
    parser.add_argument(
        "--private-key-info-path",
        required=True,
        help="Path to the private key file for JWT signing",
    )
    parser.add_argument(
        "--key-id",
        default="augment-kid-0",
        help="Key ID for JWT header (default: augment-kid-0)",
    )
    parser.add_argument(
        "--issuer",
        default="augmentcode.com",
        help="Issuer claim for JWT (default: augmentcode.com)",
    )
    parser.add_argument(
        "--algorithm",
        default="ES256",
        help="JWT signing algorithm (default: ES256)",
    )
    parser.add_argument(
        "--expiration-minutes",
        type=int,
        default=10,
        help="JWT expiration time in minutes (default: 10)",
    )
    parser.add_argument(
        "--user-email",
        required=True,
        help="User email to act as",
    )
    parser.add_argument(
        "--glean-base-url",
        default="https://augment-be.glean.com",
        help="Glean domain. e.g.: https://augment-be.glean.com",
    )
    parser.add_argument(
        "--test-endpoint",
        choices=["token", "headers", "search"],
        default="token",
        help="Test endpoint (default: token)",
    )
    parser.add_argument(
        "--search-query",
        default="test",
        help="Search query (default: test)",
    )
    parser.add_argument(
        "--clean",
        action=argparse.BooleanOptionalAction,
        default=True,
        help="Clean the API response using default API filtering rules (default: True)",
    )
    parser.add_argument(
        "--allowkeys",
        type=str,
        help="Comma separated list of keys to allowlist when filtering",
    )
    parser.add_argument(
        "--blockkeys",
        type=str,
        help="Comma separated list of keys to blocklist when filtering",
    )
    parser.add_argument(
        "--include-default-keylists",
        action=argparse.BooleanOptionalAction,
        default=True,
        help="Include the default allowlist and blocklist when filtering on top of the user provided lists (default: True)",
    )

    args = parser.parse_args()

    # Load private key info from file
    private_key_info = PrivateKeyInfo.model_validate_json(
        Path(args.private_key_info_path).read_text()
    )
    # Setup mock settings client so that JWT client can get the private key info
    settings_client = MockSettingsClient(args.glean_base_url, private_key_info)

    # Create JWT client
    jwt_config = GleanJWTConfig(
        issuer=args.issuer,
        expiration_minutes=args.expiration_minutes,
    )
    jwt_client = GleanJWTClient(jwt_config, settings_client)

    # Create request context
    request_context = RequestContext.create(request_source="glean_jwt_test")

    # Run the requested test
    if args.test_endpoint == "token":
        test_generate_jwt(jwt_client, private_key_info, args.glean_base_url)
    elif args.test_endpoint == "headers":
        test_get_auth_headers(
            jwt_client, private_key_info, args.glean_base_url, args.user_email
        )
    elif args.test_endpoint == "search":
        test_search_request(
            jwt_client,
            request_context,
            args.user_email,
            args.search_query,
            clean_response=args.clean,
            allow_keys=_get_allowkey_list_from_user_args(args),
            block_keys=_get_blockkey_list_from_user_args(args),
        )


def _get_allowkey_list_from_user_args(args):
    if args.allowkeys:
        if args.include_default_keylists:
            return default_response_select_keys + args.allowkeys.split(",")
        else:
            return args.allowkeys.split(",")
    else:
        return default_response_select_keys


def _get_blockkey_list_from_user_args(args):
    if args.blockkeys:
        if args.include_default_keylists:
            return default_response_remove_keys + args.blockkeys.split(",")
        else:
            return args.blockkeys.split(",")
    else:
        return default_response_remove_keys


if __name__ == "__main__":
    main()

"""Request insight builder for Glean service."""

import structlog
from services.integrations.glean import glean_pb2
from services.integrations.glean.server.config import GleanQueryProcessorConfig
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
    new_event,
)

log = structlog.get_logger()


class GleanRequestInsightBuilder:
    """Class to log request insight events during Glean requests."""

    def __init__(self, ri_publisher: RequestInsightPublisher):
        self.ri_publisher = ri_publisher

    def record_request(
        self,
        request: glean_pb2.SearchRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        query_processor_config: GleanQueryProcessorConfig,
    ):
        """Record a Glean search request event."""
        request_event = new_event()
        ri_request = request_insight_pb2.RIGleanRequest(
            request=request,
            request_source=request_context.request_source,
            query_processor_config=request_insight_pb2.RIGleanRequest.RIGleanQueryProcessorConfig(
                gcp_project_id=query_processor_config.gcp_project_id,
                gcp_region=query_processor_config.gcp_region,
                model_name=query_processor_config.model_name,
                temperature=query_processor_config.temperature,
                max_output_tokens=query_processor_config.max_output_tokens,
                max_results=query_processor_config.max_results,
            ),
        )
        request_event.glean_request.MergeFrom(ri_request)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [request_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_response(
        self,
        response: glean_pb2.SearchResponse,
        generate_search_queries_request: request_insight_pb2.ThirdPartyModelRequest
        | None,
        generate_search_queries_response: list[str],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a Glean search response event."""
        response_event = new_event()
        ri_response = request_insight_pb2.RIGleanResponse(
            response=response,
            generate_search_queries_request=generate_search_queries_request,
            generate_search_queries_response=generate_search_queries_response,
        )
        response_event.glean_response.MergeFrom(ri_response)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_oauth_url_request(
        self,
        request: glean_pb2.GetOAuthURLRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a Glean OAuth URL request event."""
        request_event = new_event()
        ri_request = request_insight_pb2.RIGleanOAuthURLRequest()
        request_event.glean_oauth_url_request.MergeFrom(ri_request)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [request_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_oauth_url_response(
        self,
        response: glean_pb2.GetOAuthURLResponse,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a Glean OAuth URL response event."""
        response_event = new_event()
        ri_response = request_insight_pb2.RIGleanOAuthURLResponse(
            oauth_url=response.oauth_url
        )
        response_event.glean_oauth_url_response.MergeFrom(ri_response)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

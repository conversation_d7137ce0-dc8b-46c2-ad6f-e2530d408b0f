"""Configuration for the Glean server."""

import pathlib
from dataclasses import dataclass
import typing

from dataclasses_json import dataclass_json
from pydantic import BaseModel

import services.lib.grpc.tls_config.tls_config as tls_config


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    token_exchange_endpoint: str


@dataclass_json
@dataclass
class GleanQueryProcessorConfig:
    """Configuration for the Glean query processor."""

    gcp_project_id: str
    """The project id of the GCP project where the model is deployed."""

    gcp_region: str
    """The region of the GCP project where the model is deployed."""

    model_name: str
    """The name of the model to use for generating glean query."""

    temperature: float
    """The temperature parameter for controlling the randomness of the responses."""

    max_output_tokens: int
    """The max number of tokens allowed in the output from the model"""

    max_results: int = 20
    """The max number of results allowed in one page from Glean"""

    max_snippet_chars: int = 100
    """The max number of characters in each snippet"""


@dataclass_json
@dataclass
class GleanAuthConfig:
    """Configuration for the Glean auth processor."""

    callback_url: str


class PrivateKeyInfo(BaseModel):
    key: str
    """PEM encoded private key."""
    algorithm: str
    """JWT algorithm identifier (e.g., 'RS256', 'ES256')."""
    key_id: str
    """A string identifying this private key (and
    thus what public key it corresponds to), e.g.: 'augment-kid-0'."""


@dataclass_json
@dataclass
class GleanJWTConfig:
    """Configuration for JWT authentication with Glean."""

    issuer: str = "augmentcode.com"  # Issuer claim for the JWT
    expiration_minutes: int = 10  # Default token expiration time


@dataclass_json
@dataclass
class Config:
    """Configuration for the Glean server."""

    port: int
    feature_flags_sdk_key_path: typing.Optional[str]
    dynamic_feature_flags_endpoint: typing.Optional[str]
    auth_config: AuthConfig
    settings_endpoint: str
    glean_query_processor_config: GleanQueryProcessorConfig
    glean_auth_config: GleanAuthConfig
    glean_jwt_config: GleanJWTConfig
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None
    central_server_mtls: typing.Optional[tls_config.ServerConfig] = None
    client_mtls: typing.Optional[tls_config.ClientConfig] = None
    server_mtls: typing.Optional[tls_config.ServerConfig] = None
    shutdown_grace_period_s: int = 25

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )

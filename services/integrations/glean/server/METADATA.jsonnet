local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'glean',
      kubecfg: {
        target: '//services/integrations/glean/server:kubecfg',
        // deployment controlled by namespace_config.flags.deployGlean
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['surbhi', 'aswin'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

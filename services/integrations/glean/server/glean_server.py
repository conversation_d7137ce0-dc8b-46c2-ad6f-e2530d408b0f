"""Server for the Glean service."""

import argparse
import json
import logging
import os
import pathlib
import threading
from concurrent.futures import ThreadPoolExecutor

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
import prometheus_client
import structlog
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from pydantic import SecretStr
from requests import HTTPError

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import <PERSON>fulSignalHand<PERSON>
from services.integrations.glean import glean_pb2, glean_pb2_grpc
from services.integrations.glean.client.client import http_method_to_str
from services.integrations.glean.server.config import Config
from services.integrations.glean.server.glean_auth_processor import (
    GleanAuthenticationRequired,
    GleanAuthProcessor,
    GleanTenantNotConfigured,
)
from services.integrations.glean.server.glean_jwt_client import GleanJWTClient
from services.integrations.glean.server.glean_query_processor import GleanQueryProcessor
from services.integrations.glean.server.glean_request_insight_builder import (
    GleanRequestInsightBuilder,
)
from services.integrations.glean.server.glean_search_client import GleanSearchClient
from services.integrations.glean.server.utils import (
    tenant_settings_to_glean_tenant_type,
    validate_key_pair_info,
)
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.request_context.request_context import RequestContext
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.settings import settings_pb2
from services.settings.client.client import SettingsClient
from services.token_exchange.client import client as token_exchange_client

log = structlog.get_logger()


tracer = base.tracing.setup_opentelemetry()


class GleanServer(glean_pb2_grpc.GleanServicer):
    """RPC server to call Glean instance."""

    def __init__(
        self, config: Config, request_insight_publisher: RequestInsightPublisher
    ):
        super().__init__()
        self.config = config
        self.ri_builder = GleanRequestInsightBuilder(request_insight_publisher)

        self.settings_client = SettingsClient(
            config.settings_endpoint,
            tls_config.get_client_tls_creds(config.client_mtls),
        )

        self.auth_processor = GleanAuthProcessor(
            config.glean_auth_config,
            self.settings_client,
        )

        self.glean_jwt_client = GleanJWTClient(
            config.glean_jwt_config, self.settings_client
        )

        self.api_client = GleanSearchClient(
            self.auth_processor,
            self.glean_jwt_client,
        )

        self.query_processor = GleanQueryProcessor(
            config.glean_query_processor_config, self.api_client
        )

    def HydrateGleanUserSettings(
        self,
        request: glean_pb2.HydrateGleanUserSettingsRequest,
        context: grpc.ServicerContext,
    ) -> glean_pb2.HydrateGleanUserSettingsResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")
        user_id = auth_info.user_id
        if not user_id:
            raise ValueError("user_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            # We normally don't log user IDs, but knowing who installed an integration
            # is particularly useful
            logging.info("HydrateGleanUserSettings from user %s", auth_info.user_id)
            return self.auth_processor.hydrate_glean_user_settings(
                request.code, context, request_context, tenant_id, user_id
            )

            # TODO: publish an RI event with metadata

    def Search(
        self, request: glean_pb2.SearchRequest, context: grpc.ServicerContext
    ) -> glean_pb2.SearchResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            # Record request event
            self.ri_builder.record_request(
                request,
                request_context,
                auth_info,
                self.query_processor.config,
            )

            if request.user_id:
                user_id = request.user_id
            elif auth_info.opaque_user_id:
                # customer-ui always uses the Augment opaque user ID when
                # hydrating user settings, so the correct user ID to use for
                # fetching tokens is actually the opaque_user_id.
                user_id = auth_info.opaque_user_id
            else:
                raise ValueError(
                    "user_id must be set in request or opaque_user_id must be set in auth_info"
                )

            # Get user email from request or auth_info
            if request.user_email:
                user_email = SecretStr(request.user_email)
            elif auth_info.user_email:
                user_email = auth_info.user_email
            else:
                # Default to empty string if no email is provided to allow OAuth
                # tenants to still work. Although if the tenant is configured for JWT
                # auth, this will cause an exception to be raised.
                user_email = SecretStr("")

            try:
                (
                    results,
                    generate_search_queries_request,
                    generate_search_queries_response,
                ) = self.query_processor.get_glean_results_from_query(
                    request_context,
                    tenant_id,
                    user_id,
                    user_email,
                    request.query,
                    auth_info.tenant_name,
                    request.include_private_docs,
                )
                response = glean_pb2.SearchResponse(documents=results, status_code=200)

                # Record successful response event
                self.ri_builder.record_response(
                    response,
                    generate_search_queries_request,
                    generate_search_queries_response,
                    request_context,
                    auth_info,
                )

            except GleanTenantNotConfigured:
                logging.info("Tenant not configured for Glean authentication")
                response = glean_pb2.SearchResponse(oauth_url="", status_code=401)
            except GleanAuthenticationRequired as e:
                logging.info("Glean auth required, returning oauth_url")
                response = glean_pb2.SearchResponse(
                    oauth_url=e.oauth_url, status_code=401
                )
            except HTTPError as e:
                logging.error(f"HTTP error while searching Glean: {str(e)}", exc_info=e)

                if e.response:
                    status_code = e.response.status_code
                else:
                    status_code = 500
                    logging.warning("HTTPError has no response")

                response = glean_pb2.SearchResponse(
                    status_code=status_code,
                )
            except Exception as e:
                logging.error("Failed to search Glean", exc_info=e)
                response = glean_pb2.SearchResponse(
                    status_code=500,
                )

            return response

    def CallGleanApi(
        self, request: glean_pb2.CallGleanApiRequest, context: grpc.ServicerContext
    ) -> glean_pb2.CallGleanApiResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            tenant_id = auth_info.tenant_id
            if not tenant_id:
                logging.error("tenant_id must be set in auth info")
                return glean_pb2.CallGleanApiResponse(
                    response=json.dumps(
                        {
                            "error": "tenant_id must be set in auth info",
                        }
                    ),
                    status_code=400,
                )

            try:
                response = self.api_client.call_api(
                    request_context,
                    auth_info,
                    http_method_to_str(request.method),
                    request.path,
                    json.loads(request.payload),
                )
            except HTTPError as e:
                logging.warning("HTTP error while calling GleanAPI: %s", str(e))
                return glean_pb2.CallGleanApiResponse(
                    response=json.dumps({"error": str(e)}),
                    status_code=e.response.status_code if e.response else 500,
                )
            except GleanAuthenticationRequired as e:
                logging.warning("CallGleanApi failed due to missing authentication.")
                error_message = "Authentication required."
                if e.oauth_url:
                    error_message += (
                        f"The user can complete the OAuth process by visiting: {e.oauth_url}. "
                        "If you are an agent: you should let the user know that they can visit the above "
                        "URL to complete authentication."
                    )
                return glean_pb2.CallGleanApiResponse(
                    response=json.dumps({"error": error_message}),
                    status_code=401,
                )
            except Exception as e:
                logging.error("Failed to call Glean API", exc_info=e)
                return glean_pb2.CallGleanApiResponse(
                    response=json.dumps(
                        {
                            "error": "Unhandled internal error. Typically a configuration error, do not retry."
                        }
                    ),
                    status_code=500,
                )
            return glean_pb2.CallGleanApiResponse(
                response=json.dumps(response), status_code=200
            )

    def IsToolConfigured(
        self, request: glean_pb2.IsToolConfiguredRequest, context: grpc.ServicerContext
    ) -> glean_pb2.IsToolConfiguredResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id

        # Use user_id from request if provided, otherwise fall back to opaque_user_id from auth_info.
        # opaque_user_id specifically because customer-ui uses the Augment opaque user ID when
        # hydrating user settings.
        user_id = request.user_id if request.user_id else auth_info.opaque_user_id

        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")
        if not user_id:
            raise ValueError("user_id must be set in request or auth info")

        result: glean_pb2.IsToolConfiguredResponse
        local_logger = log
        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                tenant_settings = self.settings_client.get_tenant_settings(
                    request_context=request_context,
                    request=settings_pb2.GetTenantSettingsRequest(),
                ).settings
            except Exception as e:
                raise RuntimeError(
                    "Failed to get tenant settings while checking IsToolConfigured"
                ) from e

            tenant_type = tenant_settings_to_glean_tenant_type(tenant_settings)
            local_logger = local_logger.bind(glean_tenant_type=tenant_type)

            if tenant_type == "oauth":
                # Check if user settings store refresh token. This call can error,
                # but we just let it bubble up.
                result = glean_pb2.IsToolConfiguredResponse(
                    is_configured=self.auth_processor.is_user_oauth_configured(
                        request_context, user_id
                    )
                )
            elif tenant_type == "jwt":
                # Tenant is a JWT tenant, and no user configuration is required,
                # but sanity check that the JWT key settings are valid.
                result = glean_pb2.IsToolConfiguredResponse(
                    is_configured=validate_key_pair_info(
                        tenant_settings.glean_tenant_settings.glean_jwt_key_pair_info
                    )
                )
            else:
                # Tenant is not configured for Glean.
                result = glean_pb2.IsToolConfiguredResponse(is_configured=False)

            local_logger.info(
                f"IsToolConfigured returning, is_configured={result.is_configured}",
                result=result,
            )
            return result

    def GetOAuthURL(
        self, request: glean_pb2.GetOAuthURLRequest, context: grpc.ServicerContext
    ) -> glean_pb2.GetOAuthURLResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        local_logger = log
        with request_context.with_context_logging(), auth_info.with_context_logging():
            # Record request event
            self.ri_builder.record_oauth_url_request(
                request,
                request_context,
                auth_info,
            )

            try:
                tenant_type = self.auth_processor.get_tenant_type(request_context)
            except Exception as e:
                raise RuntimeError("Failed to get tenant type for GetOAuthUrl") from e
            local_logger = local_logger.bind(tenant_type=tenant_type)

            if tenant_type == "oauth":
                response = glean_pb2.GetOAuthURLResponse(
                    oauth_url=self.auth_processor.get_oauth_url(
                        request_context, tenant_id=tenant_id
                    )
                )
            else:
                # No OAuth URL for JWT tenants nor for unconfigured tenants
                response = glean_pb2.GetOAuthURLResponse(oauth_url="")

            # Record response event
            self.ri_builder.record_oauth_url_response(
                response,
                request_context,
                auth_info,
            )

            local_logger.info(
                f"GetOAuthURL returning. oauth_url='{response.oauth_url}'",
                response=response,
            )
            return response

    def GetGleanTenantSettings(
        self,
        request: glean_pb2.GetGleanTenantSettingsRequest,
        context: grpc.ServicerContext,
    ) -> glean_pb2.GetGleanTenantSettingsResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.auth_processor.get_glean_tenant_settings(request_context)

    def HydrateGleanTenantSettings(
        self,
        request: glean_pb2.HydrateGleanTenantSettingsRequest,
        context: grpc.ServicerContext,
    ) -> glean_pb2.HydrateGleanTenantSettingsResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.auth_processor.hydrate_glean_tenant_settings(
                request, context, request_context, tenant_id, auth_info.tenant_name
            )

    def GenerateNewKeyPair(
        self,
        request: glean_pb2.GenerateNewKeyPairRequest,
        context: grpc.ServicerContext,
    ) -> glean_pb2.GenerateNewKeyPairResponse:
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            public_key, private_key = self.auth_processor.generate_new_key_pair(
                request_context
            )
            return glean_pb2.GenerateNewKeyPairResponse(public_key=public_key)


def _serve(
    config: Config,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["SETTINGS_RW"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)

    server = grpc.server(
        ThreadPoolExecutor(max_workers=10),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )

    # Reply to health check RPCs
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    glean_pb2_grpc.add_GleanServicer_to_server(
        GleanServer(config, ri_publisher),
        server,
    )
    service_names = (
        glean_pb2.DESCRIPTOR.services_by_name["Glean"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds_multi(
        [
            config
            for config in [config.server_mtls, config.central_server_mtls]
            if config is not None
        ]
    )
    if server_credentials:
        actual_port = server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        actual_port = server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", actual_port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    prometheus_client.start_http_server(9090)

    ri_publisher = RequestInsightPublisher.create_from_args(args)

    _serve(config, namespace, ri_publisher, handler.get_shutdown_event())


if __name__ == "__main__":
    main()

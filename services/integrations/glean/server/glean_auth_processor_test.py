"""Tests for the Glean authentication processor."""

from unittest import mock

import pytest
import grpc
from pydantic import SecretStr

from services.settings import settings_pb2
from services.integrations.glean import glean_pb2
from services.integrations.glean.server.glean_auth_processor import (
    GleanAuthProcessor,
    GleanAuthConfig,
    GOOGLE_TOKEN_URL,
    GOOGLE_AUTH_URL,
    BEARER_TOKEN_TYPE,
    TenantOAuthSettings,
)
from services.settings.client.client import SettingsClient


@pytest.fixture
def auth_processor(mock_settings_client):
    """Create a GleanAuthProcessor instance for testing."""
    config = GleanAuthConfig(
        callback_url="https://test.example.com/gleanCallback",
    )
    return GleanAuthProcessor(config, mock_settings_client)


@pytest.fixture
def mock_request_context():
    """Create a mock request context."""
    return mock.Mock()


@pytest.fixture
def mock_settings_client():
    """Create a mock settings client."""
    return mock.create_autospec(SettingsClient, instance=True)


def test_get_oauth_url(auth_processor, mock_request_context, mock_settings_client):
    """Test the OAuth URL construction."""
    mock_settings_client.get_tenant_settings.return_value = settings_pb2.GetTenantSettingsResponse(
        settings=settings_pb2.TenantSettings(
            glean_tenant_settings=settings_pb2.GleanTenantSettings(
                oauth_client_id="test_client_id",
                oauth_client_secret="test_client_secret",  # pragma: allowlist secret
                oauth_subdomain="",
                oauth_provider=glean_pb2.Provider.GSUITE,
            )
        )
    )

    oauth_url = auth_processor.get_oauth_url(
        mock_request_context, tenant_id="test-tenant"
    )

    assert GOOGLE_AUTH_URL in oauth_url
    assert "client_id=test_client_id" in oauth_url
    assert "redirect_uri=https%3A%2F%2Ftest.example.com%2FgleanCallback" in oauth_url
    assert "response_type=code" in oauth_url
    assert (
        "scope=openid+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile"
        in oauth_url
    )
    assert "access_type=offline" in oauth_url
    assert "prompt=consent" in oauth_url


def test_get_provider_config_gsuite(auth_processor):
    """Test getting Google provider configuration."""

    tenant_settings = TenantOAuthSettings(
        client_id_secret=SecretStr("test_client_id"),
        client_secret_secret=SecretStr(
            "test_client_secret"
        ),  # pragma: allowlist secret
        provider=glean_pb2.Provider.GSUITE,
        subdomain="",
        glean_domain="https://test.glean.com",
    )

    config = auth_processor._get_provider_config(tenant_oauth_settings=tenant_settings)

    assert config.token_url == GOOGLE_TOKEN_URL
    assert config.authorization_url == GOOGLE_AUTH_URL
    assert "openid" in config.scopes
    assert config.auth_params["access_type"] == "offline"
    assert config.auth_params["prompt"] == "consent"


def test_get_provider_config_okta(auth_processor):
    """Test getting Okta provider configuration."""

    tenant_settings = TenantOAuthSettings(
        client_id_secret=SecretStr("test_client_id"),
        client_secret_secret=SecretStr(
            "test_client_secret"
        ),  # pragma: allowlist secret
        provider=glean_pb2.Provider.OKTA,
        subdomain="test.okta.com",
        glean_domain="https://test.glean.com",
    )

    config = auth_processor._get_provider_config(tenant_oauth_settings=tenant_settings)

    assert config.token_url == "https://test.okta.com/oauth2/v1/token"
    assert config.authorization_url == "https://test.okta.com/oauth2/v1/authorize"
    assert "offline_access" in config.scopes
    assert config.auth_params["response_mode"] == "query"


def test_get_provider_config_okta_with_https(auth_processor):
    """Test getting Okta provider configuration with https:// already in subdomain."""

    tenant_settings = TenantOAuthSettings(
        client_id_secret=SecretStr("test_client_id"),
        client_secret_secret=SecretStr(
            "test_client_secret"
        ),  # pragma: allowlist secret
        provider=glean_pb2.Provider.OKTA,
        subdomain="https://test.okta.com",
        glean_domain="https://test.glean.com",
    )

    config = auth_processor._get_provider_config(tenant_oauth_settings=tenant_settings)

    assert config.token_url == "https://test.okta.com/oauth2/v1/token"
    assert config.authorization_url == "https://test.okta.com/oauth2/v1/authorize"


@mock.patch("requests_oauthlib.OAuth2Session.fetch_token")
def test_hydrate_glean_user_settings_success(
    mock_fetch_token, auth_processor, mock_settings_client
):
    """Test successful token exchange and settings update."""
    # Mock the tenant settings
    mock_settings_client.get_tenant_settings.return_value = settings_pb2.GetTenantSettingsResponse(
        settings=settings_pb2.TenantSettings(
            glean_tenant_settings=settings_pb2.GleanTenantSettings(
                oauth_client_id="test_client_id",
                oauth_client_secret="test_client_secret",  # pragma: allowlist secret
                oauth_subdomain="",
                oauth_provider=glean_pb2.Provider.GSUITE,
            )
        )
    )

    # Mock the token response
    mock_fetch_token.return_value = {
        "access_token": "test_access_token",
        "refresh_token": "test_refresh_token",
        "token_type": "Bearer",
    }

    # Mock the settings response
    settings_response = mock.Mock()
    settings_response.settings = settings_pb2.UserSettings()
    settings_response.version = "1"
    mock_settings_client.get_user_settings.return_value = settings_response

    # Test the processor
    response = auth_processor.hydrate_glean_user_settings(
        code="test_code",
        context=mock.Mock(spec=grpc.ServicerContext),
        request_context=mock.Mock(),
        tenant_id="test_tenant_id",
        user_id=SecretStr("test_user_id"),
    )

    # Verify the settings were updated correctly
    update_request = mock_settings_client.update_user_settings.call_args[1]["request"]
    assert (
        update_request.settings.glean_user_settings.access_token == "test_access_token"
    )
    assert (
        update_request.settings.glean_user_settings.refresh_token
        == "test_refresh_token"
    )
    assert update_request.settings.glean_user_settings.token_type == BEARER_TOKEN_TYPE
    assert update_request.expected_version == "1"

    # Verify the response
    assert isinstance(response, glean_pb2.HydrateGleanUserSettingsResponse)

    # Verify fetch_token was called with correct parameters
    mock_fetch_token.assert_called_once_with(
        GOOGLE_TOKEN_URL,
        code="test_code",
        client_secret="test_client_secret",  # pragma: allowlist secret
        timeout=5,
    )


def test_hydrate_glean_user_settings_no_tenant_settings(
    auth_processor, mock_settings_client
):
    """Test handling of missing tenant settings."""

    # Mock the tenant settings response with empty settings
    mock_settings_client.get_tenant_settings.return_value = (
        settings_pb2.GetTenantSettingsResponse(
            settings=settings_pb2.TenantSettings(
                glean_tenant_settings=None  # No Glean settings configured
            )
        )
    )

    auth_processor.hydrate_glean_user_settings(
        code="test_code",
        context=mock.Mock(spec=grpc.ServicerContext),
        request_context=mock.Mock(),
        tenant_id="test_tenant_id",
        user_id=SecretStr("test_user_id"),
    )

    mock_settings_client.update_user_settings.assert_not_called()


@mock.patch("requests.post")
def test_hydrate_glean_user_settings_missing_tokens(mock_post, auth_processor):
    """Test handling of missing tokens in response."""

    mock_settings_client.update_user_settings = mock.Mock()
    # Mock a response with missing tokens
    mock_response = mock.Mock()
    mock_response.ok = True
    mock_response.json.return_value = {}  # Empty response
    mock_post.return_value = mock_response

    auth_processor.hydrate_glean_user_settings(
        code="test_code",
        context=mock.Mock(spec=grpc.ServicerContext),
        request_context=mock.Mock(),
        tenant_id="test_tenant_id",
        user_id=SecretStr("test-user"),
    )

    mock_settings_client.update_user_settings.assert_not_called()


def test_get_auth_token_success(auth_processor, mock_settings_client):
    """Test successful retrieval of auth token."""
    # Mock the settings response
    mock_settings_client.get_tenant_settings.return_value = settings_pb2.GetTenantSettingsResponse(
        settings=settings_pb2.TenantSettings(
            glean_tenant_settings=settings_pb2.GleanTenantSettings(
                oauth_client_id="test_client_id",
                oauth_client_secret="test_client_secret",  # pragma: allowlist secret
                oauth_subdomain="",
                oauth_provider=glean_pb2.Provider.GSUITE,
            )
        )
    )

    # Mock the user settings response
    settings_response = mock.Mock()
    settings_response.settings = settings_pb2.UserSettings()
    settings_response.settings.glean_user_settings.access_token = "test_access_token"
    settings_response.settings.glean_user_settings.token_type = "Bearer"
    mock_settings_client.get_user_settings.return_value = settings_response

    # Test the processor
    session = auth_processor.get_oauth_session(
        request_context=mock.Mock(),
        tenant_id="test_tenant_id",
        user_id="test_user",
    )

    assert session.token["access_token"] == "test_access_token"
    assert "Authorization" in session.headers
    assert session.headers["Authorization"] == "Bearer test_access_token"
    mock_settings_client.get_user_settings.assert_called_once()


def test_get_auth_token_missing_user_settings(auth_processor, mock_settings_client):
    """Test handling of missing Glean settings."""
    # Mock the tenant settings
    mock_settings_client.get_tenant_settings.return_value = settings_pb2.GetTenantSettingsResponse(
        settings=settings_pb2.TenantSettings(
            glean_tenant_settings=settings_pb2.GleanTenantSettings(
                oauth_client_id="test_client_id",
                oauth_client_secret="test_client_secret",  # pragma: allowlist secret
                oauth_subdomain="",
                oauth_provider=glean_pb2.Provider.GSUITE,
            )
        )
    )

    # Mock the settings response with no Glean user settings
    settings_response = mock.Mock()
    settings_response.settings = settings_pb2.UserSettings()
    mock_settings_client.get_user_settings.return_value = settings_response

    session = auth_processor.get_oauth_session(
        request_context=mock.Mock(),
        tenant_id="test_tenant_id",
        user_id="test_user",
    )

    assert not session.token  # Empty token dictionary
    assert "Authorization" not in session.headers


@mock.patch("requests_oauthlib.OAuth2Session.refresh_token")
def test_refresh_token_success(
    mock_refresh_token, auth_processor, mock_settings_client
):
    """Test successful token refresh."""
    # Mock the tenant settings
    mock_settings_client.get_tenant_settings.return_value = settings_pb2.GetTenantSettingsResponse(
        settings=settings_pb2.TenantSettings(
            glean_tenant_settings=settings_pb2.GleanTenantSettings(
                oauth_client_id="test_client_id",
                oauth_client_secret="test_client_secret",  # pragma: allowlist secret
                oauth_subdomain="",
                oauth_provider=glean_pb2.Provider.GSUITE,
            )
        )
    )

    # Mock the user settings response
    settings_response = mock.Mock()
    settings_response.settings = settings_pb2.UserSettings()
    settings_response.settings.glean_user_settings.access_token = "old_access_token"
    settings_response.settings.glean_user_settings.refresh_token = "test_refresh_token"
    settings_response.settings.glean_user_settings.token_type = "Bearer"
    settings_response.version = "1"
    mock_settings_client.get_user_settings.return_value = settings_response

    # Mock the token refresh
    mock_refresh_token.return_value = {
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "token_type": "Bearer",
    }

    session = auth_processor.get_oauth_session(
        request_context=mock.Mock(),
        tenant_id="test_tenant_id",
        user_id="test_user",
    )

    # Simulate token refresh
    new_token = session.refresh_token()
    auth_processor.save_oauth_token(
        request_context=mock.Mock(),
        session=session,
        tenant_id="test_tenant_id",
        user_id="test_user",
        token=new_token,
    )

    assert session.token["access_token"] == "new_access_token"
    assert session.headers["Authorization"] == "Bearer new_access_token"
    assert mock_settings_client.update_user_settings.called


def test_refresh_token_missing_refresh_token(auth_processor, mock_settings_client):
    """Test handling of missing refresh token."""
    # Mock the tenant settings
    mock_settings_client.get_tenant_settings.return_value = settings_pb2.GetTenantSettingsResponse(
        settings=settings_pb2.TenantSettings(
            glean_tenant_settings=settings_pb2.GleanTenantSettings(
                oauth_client_id="test_client_id",
                oauth_client_secret="test_client_secret",  # pragma: allowlist secret
                oauth_subdomain="",
                oauth_provider=glean_pb2.Provider.GSUITE,
            )
        )
    )

    # Mock the settings response with no refresh token
    settings_response = mock.Mock()
    settings_response.settings = settings_pb2.UserSettings()
    mock_settings_client.get_user_settings.return_value = settings_response

    session = auth_processor.get_oauth_session(
        request_context=mock.Mock(),
        tenant_id="test_tenant_id",
        user_id="test_user",
    )

    assert not session.token  # Empty token dictionary
    assert "Authorization" not in session.headers
    mock_settings_client.update_user_settings.assert_not_called()

"""General utility functions that are useful to multiple Glean components."""

from typing import Literal
from urllib.parse import urlparse

from services.integrations.glean import glean_pb2
from services.settings import settings_pb2

GleanTenantType = Literal["oauth", "jwt", "unconfigured"]


def validate_glean_domain(glean_domain: str) -> bool:
    """
    Validate that a stored glean domain is properly formatted
    for use with Glean API requests (including for a JWT client).
    A properly formatted domain must have a host and scheme:
    e.g.: https://augment-be.glean.com

    And for JWT reasons, there must be no trailing slashes.
    """
    glean_domain_parsed = urlparse(glean_domain)
    return bool(
        glean_domain_parsed.netloc
        and glean_domain_parsed.scheme
        and not glean_domain_parsed.path
    )


def validate_key_pair_info(key_pair_info: settings_pb2.GleanJWTKeyPairInfo) -> bool:
    """Validate that key pair info is set and valid."""
    return bool(
        key_pair_info.private_key
        and key_pair_info.public_key
        and key_pair_info.algorithm
        and key_pair_info.key_id
    )


def tenant_settings_to_glean_tenant_type(
    tenant_settings: settings_pb2.TenantSettings,
) -> GleanTenantType:
    """Convert Glean tenant settings to a tenant type."""
    if (
        tenant_settings.glean_tenant_settings
        and tenant_settings.glean_tenant_settings.glean_domain
        and tenant_settings.glean_tenant_settings.oauth_provider
        != glean_pb2.Provider.UNSPECIFIED_PROVIDER
    ):
        return "oauth"
    elif (
        tenant_settings.glean_tenant_settings
        and tenant_settings.glean_tenant_settings.glean_domain
        and tenant_settings.glean_tenant_settings.HasField("glean_jwt_key_pair_info")
    ):
        return "jwt"
    else:
        return "unconfigured"

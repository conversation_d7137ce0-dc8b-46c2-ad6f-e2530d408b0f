"""Handles the processing a user query into a list of relevant Glean documents."""

import logging
from dataclasses import dataclass
from typing import Callable, Iterable, TypeVar

import opentelemetry.trace
from dataclasses_json import dataclass_json
from prometheus_client import Histogram
from pydantic import SecretStr

from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse,
    ToolChoice,
    ToolChoiceType,
)
from services.integrations.glean import glean_pb2
from services.integrations.glean.server.config import GleanQueryProcessorConfig
from services.integrations.glean.server.glean_search_client import GleanSearchClient
from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2

T = TypeVar("T")

tracer = opentelemetry.trace.get_tracer(__name__)

GLEAN_SEARCH_QUERY_GENERATION_LATENCY = Histogram(
    "au_glean_search_query_generation_latency",
    "Latency of generating search queries from user query in seconds",
    ["model", "client_type", "tenant_name"],
)


# This is based on the tool defined in base/third_party_clients/anthropic_direct_client.py
# If the tool definition changes, this will need to be updated.
@dataclass_json
@dataclass(frozen=True)
class GleanToolCallOutput:
    """The output from the glean search tool."""

    queries: list[str]
    """A list of search queries to be used with Glean search."""


# This is copied from services/autofix/server/handler.py - temporarily adding, assuming this will get pushed to ThirdPartyModelClient interface
def _parse_tool_use(
    responses: Iterable[ThirdPartyModelResponse], unmarshal: Callable[[object], T]
) -> T | None:
    for resp in responses:
        if resp.tool_use is not None:
            try:
                return unmarshal(resp.tool_use.input)
            except Exception:
                logging.info(
                    "Tool use %s unexpected format, proceeding without tool use: %s",
                    resp.tool_use.tool_name,
                    resp.tool_use.input.keys(),
                )

    logging.info("No tool use in model response")
    return


class GleanQueryProcessor:
    """Handles the processing a user query into a list of relevant Glean documents."""

    def __init__(
        self, config: GleanQueryProcessorConfig, glean_client: GleanSearchClient
    ):
        self.config = config
        self.glean_client = glean_client
        self.system_prompt = (
            "You are an AI assistant with access to a Glean search tool."
        )

        # TODO: we should fallback to the anthropic direct model if the vertex model is not available
        self.client = AnthropicVertexAiClient(
            project_id=config.gcp_project_id,
            region=config.gcp_region,
            model_name=config.model_name,
            temperature=config.temperature,
            max_output_tokens=config.max_output_tokens,
        )

    def get_glean_results_from_query(
        self,
        request_context: RequestContext,
        tenant_id: str,
        user_id: str,
        user_email: SecretStr,
        query: str,
        tenant_name: str,
        include_private_docs: bool,
    ) -> tuple[
        list[glean_pb2.Document],
        request_insight_pb2.ThirdPartyModelRequest | None,
        list[str],
    ]:
        """Generate search queries from the user query to be used with Glean search. Performs the Glean search and returns the results."""

        # TODO: update this prompt to work for other data sources
        prompt = (
            """You will be given a user query from slack, potentially with some conversation history:\n"""
            + query
        )
        messages = []

        with tracer.start_as_current_span("generate_search_queries") as span:
            span.set_attribute("system_prompt_length", len(self.system_prompt))
            span.set_attribute("prompt_length", len(prompt))

            with GLEAN_SEARCH_QUERY_GENERATION_LATENCY.labels(
                model=self.config.model_name,
                client_type=self.client.client_type,
                tenant_name=tenant_name,
            ).time():
                response = self.client.generate_response_stream(
                    model_caller="glean-query-processor",
                    messages=messages,
                    system_prompt=self.system_prompt,
                    cur_message=prompt,
                    tools=["glean_search"],
                    # Force tool use, and also skip any preceding chain of thought responses
                    tool_choice=ToolChoice(
                        type=ToolChoiceType.TOOL, name="glean_search"
                    ),
                )

                check_output = _parse_tool_use(
                    response,
                    GleanToolCallOutput.from_dict,  # type: ignore
                )

                # We are done with the response, close it to get accurate timing metrics
                response.close()

            span.set_attribute(
                "num_search_queries", len(check_output.queries) if check_output else 0
            )

            if not check_output or not check_output.queries:
                return [], None, []

        generate_search_queries_request = request_insight_pb2.ThirdPartyModelRequest(
            prompt=prompt,
            system_prompt=self.system_prompt,
            messages=messages,
        )

        return (
            self.get_search_results(
                request_context,
                tenant_id,
                user_id,
                user_email,
                check_output.queries,
                include_private_docs,
            ),
            generate_search_queries_request,
            check_output.queries,
        )

    def get_search_results(
        self,
        request_context: RequestContext,
        tenant_id: str,
        user_id: str,
        user_email: SecretStr,
        search_queries: list[str],
        include_private_docs: bool,
    ) -> list[glean_pb2.Document]:
        """Use the glean client to get search results."""
        with tracer.start_as_current_span("get_search_results") as span:
            results = self.glean_client.search(
                request_context,
                tenant_id,
                user_id,
                user_email,
                search_queries,
                max_results=self.config.max_results,
                include_private_docs=include_private_docs,
                max_snippet_chars=self.config.max_snippet_chars,
            )
            span.set_attribute("num_search_results", len(results))

        return results

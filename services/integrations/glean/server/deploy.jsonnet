local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';


function(env, namespace, cloud, namespace_config)
  if namespace_config.flags.deployGlean then
    local appName = 'glean';

    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

    local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

    local clientCert = certLib.createClientCert(
      name='%s-client-cert' % appName,
      namespace=namespace,
      appName=appName,
      volumeName='client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );

    local centralClientCert = certLib.createCentralClientCert(
      name='%s-central-client-cert' % appName,
      namespace=namespace,
      env=env,
      appName=appName,
      volumeName='central-client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );

    // creates a service for the pod, also create a global service if necessary
    // to allow the lead cluster (customer-ui) to talk to this pod. We only
    // deploy these when necessary, to avoid running into GCP limits
    local useGlobalService = !cloudInfo.isLeadCluster(cloud);
    local serverDnsNames = grpcLib.grpcServiceNames(appName);
    local centralServerDnsNames = grpcLib.grpcServiceNamespaceNames(appName, namespace=namespace) + if useGlobalService then [
      grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=appName, namespace=namespace),
    ] else [];
    local services = [
      grpcLib.grpcService(appName=appName, namespace=namespace),
    ] + if useGlobalService then [
      grpcLib.globalGrpcService(cloud=cloud, appName=appName, namespace=namespace),
    ] else [];

    // creates a server certificate for MTLS
    local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                                namespace=namespace,
                                                appName=appName,
                                                dnsNames=serverDnsNames,
                                                volumeName='certs');

    // creates a server certificate for central MTLS
    local centralServerCert = certLib.createCentralServerCert(name='%s-central-server-certificate' % appName,
                                                              namespace=namespace,
                                                              appName=appName,
                                                              dnsNames=centralServerDnsNames,
                                                              env=env,
                                                              volumeName='central-certs');

    local serviceAccount = gcpLib.createServiceAccount(
      appName, env, cloud, namespace, iam=true,
    );

    local requestInsightPublisher = publisherLib(cloud, env, namespace, appName);

    // grant access to the Vertex AI model
    local grantAiPlatformAccess = gcpLib.grantAccess(
      env=env,
      namespace=namespace,
      appName=appName,
      name='aiplatform-%s-grant' % appName,
      resourceRef={
        kind: 'Project',
        external: 'projects/%s' % cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/aiplatform.user',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
      abandon=true,
    );

    local projectId = cloudInfo[cloud].projectId;

    local glean_query_processor_config = {
      gcp_project_id: projectId,
      gcp_region: 'us-east5',  // Sonnet is currently only available on us-east5, europe-west1
      model_name: 'claude-3-5-sonnet-v2@********',
      temperature: 0,
      max_output_tokens: 1024,
    };

    local glean_auth_config = {
      callback_url: 'https://%s/gleanCallback' % endpointsLib.getCustomerUiHostname(env=env, namespace=namespace, cloud=cloud),
    };

    // For now, we just use the defaults for JWT config.
    local glean_jwt_config = {};

    local config = {
      port: 50051,
      feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
      auth_config: {
        token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      },
      settings_endpoint: 'settings-svc:50051',
      glean_query_processor_config: glean_query_processor_config,
      glean_auth_config: glean_auth_config,
      glean_jwt_config: glean_jwt_config,
    } + if !mtls then {} else {
      server_mtls: serverCert.config,
      central_server_mtls: if mtls then centralServerCert.config else null,
      client_mtls: clientCert.config,
      central_client_mtls: centralClientCert.config,
    };

    local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

    local container = {
      name: appName,
      target: {
        name: '//services/integrations/glean/server:image',
        dst: 'glean',
      },
      args: [
        '--request-insight-publisher-config-file',
        requestInsightPublisher.configFilePath,
      ],
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      volumeMounts: lib.flatten([
        configMap.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        centralClientCert.volumeMountDef,
        serverCert.volumeMountDef,
        centralServerCert.volumeMountDef,
        clientCert.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
      ]),
      readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
    local pod = {
      serviceAccountName: serviceAccount.name,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      containers: [
        container,
      ],
      volumes: lib.flatten([
        configMap.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
        centralClientCert.podVolumeDef,
        centralServerCert.podVolumeDef,
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
      ]),
    };

    local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
    local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
    local deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        minReadySeconds: if env == 'DEV' then 0 else 60,
        replicas: if env == 'DEV' then 1 else 2,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod + {
            tolerations: tolerations,
            affinity: affinity,
          },
        },
      },
    };
    lib.flatten([
      configMap.objects,
      serviceAccount.objects,
      serverCert.objects,
      centralServerCert.objects,
      centralClientCert.objects,
      clientCert.objects,
      dynamicFeatureFlags.k8s_objects,
      grantAiPlatformAccess,
      deployment,
      services,
      requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
    ])
  else []

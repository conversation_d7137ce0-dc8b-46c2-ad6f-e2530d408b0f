from dataclasses import dataclass
import random as rand
import time
from unittest.mock import Mock, patch
import pytest
from requests_oauthlib import OAuth2Session
from pydantic import SecretStr


from services.integrations.glean import glean_pb2
from services.integrations.glean.server.glean_search_client import GleanSearchClient
from services.integrations.glean.server.glean_auth_processor import (
    GleanAuthProcessor,
)
from services.integrations.glean.server.glean_jwt_client import GleanJWTClient


@pytest.fixture
def mock_request_context():
    """Create a mock request context."""
    return Mock()


@pytest.fixture
def mock_glean_config():
    """Create a mock config for GleanAuthProcessor."""

    @dataclass
    class MockConfig:
        token_exchange_endpoint: str = "https://mock.token.exchange"

    return MockConfig()


@pytest.fixture
def mock_glean_auth_processor(mock_glean_config):
    """Create a mock GleanAuthProcessor."""
    mock_processor = Mock(spec=GleanAuthProcessor)
    # Pass the config to the constructor
    mock_processor.config = mock_glean_config
    return mock_processor


@pytest.fixture
def mock_jwt_client():
    """Create a mock GleanJWTClient."""
    return Mock(spec=GleanJWTClient)


def test_search_preserves_order(
    mock_request_context, mock_glean_auth_processor, mock_jwt_client
):
    """Tests that search preserves the order of results from the API, even with duplicates and content fetching."""
    # Create mock API responses
    search_response = {
        "results": [
            {
                "document": {
                    "id": "doc1",
                    "datasource": "linear",
                    "docType": "page",
                    "metadata": {"visibility": "PUBLIC_VISIBLE"},
                },
                "title": "First Result",
                "url": "url1",
            },
            {
                "document": {
                    "id": "doc2",
                    "datasource": "notion",
                    "docType": "page",
                    "metadata": {"visibility": "PUBLIC_VISIBLE"},
                },
                "title": "Second Result",
                "url": "url2",
            },
            # Duplicate document to test deduplication
            {
                "document": {
                    "id": "doc1",
                    "datasource": "linear",
                    "docType": "page",
                    "metadata": {"visibility": "PUBLIC_VISIBLE"},
                },
                "title": "First Result (duplicate)",
                "url": "url1",
            },
            {
                "document": {
                    "id": "doc3",
                    "datasource": "linear",
                    "docType": "issue",
                    "metadata": {"visibility": "PUBLIC_VISIBLE"},
                },
                "title": "Third Result",
                "url": "url3",
            },
        ]
    }

    # Full response containing all documents
    get_documents_response = {
        "documents": {
            "doc1": {"content": {"fullText": "Content for doc1"}},
            "doc2": {
                "content": {
                    "fullTextList": ["Content for doc2", "More content for doc2"]
                }
            },
            "doc3": {"content": {"fullText": "Content for doc3"}},
        }
    }

    with patch("requests.Session.post") as mock_post:
        # Configure mock to return different responses for search and getdocuments endpoints
        def mock_post_response(*args, **kwargs):
            mock_resp = Mock()
            mock_resp.raise_for_status = Mock()

            if args[0].endswith("/search"):
                mock_resp.json = lambda: search_response
            elif args[0].endswith("/getdocuments"):
                time.sleep(rand.uniform(0, 0.2))
                mock_resp.json = lambda: get_documents_response
            else:
                raise ValueError(f"Unimplemented endpoint {args[0]}")

            return mock_resp

        mock_post.side_effect = mock_post_response

        # Create mock OAuth2Session
        mock_session = Mock(spec=OAuth2Session)
        mock_session.token = {"access_token": "test_token"}
        mock_session.headers = {"Authorization": "Bearer test_token"}
        mock_session.post = mock_post

        # Set up auth processor to return mock session
        auth_processor = mock_glean_auth_processor
        auth_processor.get_oauth_session.return_value = mock_session
        auth_processor.get_tenant_api_url.return_value = "https://mock.api.url"

        # Create client and perform search
        client = GleanSearchClient(auth_processor, mock_jwt_client)

        # Configure auth processor to use OAuth
        auth_processor.get_tenant_type.return_value = "oauth"

        results = client.search(
            mock_request_context,
            "test_tenant_id",
            "test_user_id",
            SecretStr("test_user_email"),
            ["test query"],
            max_results=5,
        )

        auth_processor.get_oauth_session.assert_called_once_with(
            mock_request_context, "test_tenant_id", "test_user_id"
        )

        # Verify number of results (should be 3 after deduplication)
        assert len(results) == 3, "Incorrect number of results after deduplication"

        # Verify order and content of results
        expected_results = [
            {
                "id": "doc1",
                "source": glean_pb2.DataSource.LINEAR,
                "type": glean_pb2.DocumentType.PAGE,
                "title": "First Result",
                "url": "url1",
                "content": "Content for doc1",
            },
            {
                "id": "doc2",
                "source": glean_pb2.DataSource.NOTION,
                "type": glean_pb2.DocumentType.PAGE,
                "title": "Second Result",
                "url": "url2",
                "content": "Content for doc2\nMore content for doc2",
            },
            {
                "id": "doc3",
                "source": glean_pb2.DataSource.LINEAR,
                "type": glean_pb2.DocumentType.ISSUE,
                "title": "Third Result",
                "url": "url3",
                "content": "Content for doc3",
            },
        ]

        for result, expected in zip(results, expected_results):
            assert (
                result.document_id == expected["id"]
            ), f"Expected document ID {expected['id']}, got {result.document_id}"
            assert (
                result.data_source == expected["source"]
            ), f"Expected source {expected['source']}, got {result.data_source}"
            assert (
                result.document_type == expected["type"]
            ), f"Expected type {expected['type']}, got {result.document_type}"
            assert (
                result.title == expected["title"]
            ), f"Expected title {expected['title']}, got {result.title}"
            assert (
                result.url == expected["url"]
            ), f"Expected URL {expected['url']}, got {result.url}"
            assert (
                result.content == expected["content"]
            ), f"Expected content {expected['content']}, got {result.content}"

        # Verify API calls
        calls = mock_post.call_args_list

        # First call should be search
        search_call = calls[0]
        assert search_call[0][0].endswith(
            "/search"
        ), "First call should be to search endpoint"
        assert search_call[1]["json"]["query"] == "test query", "Incorrect search query"
        assert search_call[1]["json"]["pageSize"] == 5, "Incorrect page size"

        # There is on get documents call that retrievs all documents
        getdocs_call = calls[1]
        assert getdocs_call[0][0].endswith(
            "/getdocuments"
        ), "Second call should be to getdocuments endpoint"
        assert (
            len(getdocs_call[1]["json"]["documentSpecs"]) == 3
        ), "Should request content for all 3 documents"
        doc_ids = set(doc["id"] for doc in getdocs_call[1]["json"]["documentSpecs"])
        assert doc_ids == {
            "doc1",
            "doc2",
            "doc3",
        }, f"Should request all documents 'doc1', 'doc2', and 'doc3', but got {doc_ids}"

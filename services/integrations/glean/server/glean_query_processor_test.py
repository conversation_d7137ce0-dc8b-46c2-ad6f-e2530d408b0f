from unittest.mock import Mock
import pytest
from services.integrations.glean import glean_pb2
from services.integrations.glean.server.config import GleanQueryProcessorConfig
from services.integrations.glean.server.glean_query_processor import GleanQueryProcessor
from services.integrations.glean.server.glean_search_client import GleanSearchClient


@pytest.fixture
def mock_request_context():
    """Create a mock request context."""
    return Mock()


def test_search_results_order(mock_request_context):
    """Tests that get_search_results maintains the order of results from multiple queries."""
    # Create mock config
    config = GleanQueryProcessorConfig(
        gcp_project_id="test-project",
        gcp_region="us-central1",
        model_name="test-model",
        temperature=0.7,
        max_output_tokens=1024,
        max_results=6,
    )

    # Create mock search client
    mock_client = Mock(spec=GleanSearchClient)

    # Set up mock search results
    query_results = [
        glean_pb2.Document(title="Query1 Doc1", url="url1"),
        glean_pb2.Document(title="Query1 Doc2", url="url2"),
        glean_pb2.Document(title="Query1 Doc3", url="url3"),
        glean_pb2.Document(title="Query1 Doc4", url="url4"),
    ]

    # Configure mock to return different results for different queries
    mock_client.search.side_effect = [
        query_results,
    ]

    # Create processor with mock client
    processor = GleanQueryProcessor(config, mock_client)

    # Test search with multiple queries
    search_queries = ["query1", "query2"]
    results = processor.get_search_results(
        mock_request_context,
        "test_tenant_id",
        "test_user",
        None,
        search_queries,
        False,
    )

    # Verify results are interleaved correctly
    expected_titles = [
        "Query1 Doc1",
        "Query1 Doc2",
        "Query1 Doc3",
        "Query1 Doc4",
    ]

    # Convert results to list to verify order
    results_list = list(results)

    # Verify the number of results
    assert len(results_list) == len(expected_titles), "Incorrect number of results"

    # Verify the order of results
    for actual, expected_title in zip(results_list, expected_titles):
        assert (
            actual.title == expected_title
        ), f"Expected {expected_title}, got {actual.title}"

    # Verify search was called with correct parameters
    assert mock_client.search.call_count == 1
    mock_client.search.assert_any_call(
        mock_request_context,
        "test_tenant_id",
        "test_user",
        None,
        ["query1", "query2"],
        max_results=6,
        include_private_docs=False,
        max_snippet_chars=100,
    )

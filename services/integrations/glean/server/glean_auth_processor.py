"""Authentication for the Glean server."""

import base64
import logging
from dataclasses import dataclass
from io import UnsupportedOperation
from typing import Any, Dict, List, cast
from urllib.parse import parse_qs, urlencode, urlparse

import grpc
import opentelemetry.trace
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec
from pydantic import SecretStr
from requests_oauthlib import OAuth2Session

import base.feature_flags
from services.integrations.glean import glean_pb2
from services.integrations.glean.server.config import GleanAuthConfig
from services.integrations.glean.server.utils import (
    GleanTenantType,
    tenant_settings_to_glean_tenant_type,
    validate_glean_domain,
)
from services.lib.request_context.request_context import RequestContext
from services.settings import settings_pb2
from services.settings.client.client import SettingsClient

tracer = opentelemetry.trace.get_tracer(__name__)

GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"  # nosec B105
GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/auth"  # nosec B105
BEARER_TOKEN_TYPE = "Bearer"  # nosec B105


ENABLE_GLEAN = base.feature_flags.BoolFlag("enable_glean", False)


class GleanAuthError(Exception):
    """Base class for Glean authentication errors."""

    pass


class GleanAuthenticationRequired(GleanAuthError):
    """Raised when user needs to authenticate with Glean."""

    def __init__(self, oauth_url: str = ""):
        self.oauth_url = oauth_url
        super().__init__("Glean authentication required")


class GleanTenantNotConfigured(GleanAuthError):
    """
    Raised when the tenant is not configured with OAuth credentials for Glean authentication.
    At the tenant-level, this includes having a valid SSO provider, client id, client secret, and subdomain (if applicable) set up.
    """

    def __init__(self):
        super().__init__("Glean tenant not configured")


@dataclass
class TenantOAuthSettings:
    """Tenant OAuth settings."""

    client_id_secret: SecretStr
    client_secret_secret: SecretStr
    provider: glean_pb2.Provider
    subdomain: str
    glean_domain: str


@dataclass
class OAuthConfig:
    """OAuth configuration for a provider."""

    token_url: str
    authorization_url: str
    scopes: List[str]
    auth_params: Dict[str, str]
    refresh_kwargs: Dict[str, Any]


class GleanAuthProcessor:
    """Authentication for the Glean server."""

    def __init__(
        self,
        config: GleanAuthConfig,
        settings_client: SettingsClient,
    ):
        self.callback_url = config.callback_url
        self.settings_client = settings_client

    def get_tenant_type(self, request_context: RequestContext) -> GleanTenantType:
        """Get the type of Glean tenant (i.e.: how the tenant authenticates to Glean)."""
        with tracer.start_as_current_span("get_tenant_type"):
            try:
                settings_response = self.settings_client.get_tenant_settings(
                    request_context=request_context,
                    request=settings_pb2.GetTenantSettingsRequest(),
                )
            except Exception as e:
                logging.error("Failed to get tenant settings: %s", e)
                raise

            tenant_settings = settings_response.settings

            return tenant_settings_to_glean_tenant_type(tenant_settings)

    def is_user_oauth_configured(
        self, request_context: RequestContext, opaque_user_id: str
    ) -> bool:
        """Check if the user is configured for OAuth authentication."""
        with tracer.start_as_current_span("is_oauth_configured"):
            try:
                settings_response = self.settings_client.get_user_settings(
                    request_context=request_context,
                    user_id=opaque_user_id,
                )
            except Exception as e:
                logging.error("Failed to get user settings: %s", e)
                raise

            user_settings = settings_response.settings

            return bool(
                user_settings.glean_user_settings
                and user_settings.glean_user_settings.access_token
            )

    def get_oauth_session(
        self, request_context: RequestContext, tenant_id: str, user_id: str = ""
    ) -> OAuth2Session:
        """Create an OAuth2Session for the tenant using their OAuth settings. Raises GleanTenantNotConfigured if tenant is not configured."""
        with tracer.start_as_current_span("get_oauth_session"):
            try:
                tenant_settings = self._get_tenant_oauth_settings(
                    request_context, tenant_id
                )

                provider_config = self._get_provider_config(tenant_settings)

                settings = None
                if user_id:
                    settings_response = self.settings_client.get_user_settings(
                        request_context=request_context,
                        user_id=user_id,
                    )

                    settings = settings_response.settings

                if (
                    not settings
                    or not settings.glean_user_settings
                    or not settings.glean_user_settings.access_token
                ):
                    token = {}
                else:
                    logging.info("Found existing token for user %s", user_id)
                    token = {
                        "access_token": settings.glean_user_settings.access_token,
                        "refresh_token": settings.glean_user_settings.refresh_token,
                        # set default token type to Bearer if not set
                        # this should only be temporarily needed in staging while some tokens are missing the token type field
                        "token_type": settings.glean_user_settings.token_type
                        or BEARER_TOKEN_TYPE,
                    }

                session = OAuth2Session(
                    client_id=tenant_settings.client_id_secret.get_secret_value(),
                    token=token,
                    redirect_uri=self.callback_url,
                    auto_refresh_url=provider_config.token_url,
                    auto_refresh_kwargs=provider_config.refresh_kwargs,
                    scope=provider_config.scopes,
                )

                # Set default headers for Glean requests
                headers = {
                    "Content-Type": "application/json",
                    "X-Glean-Auth-Type": "OAUTH",
                }
                if token and "access_token" in token:
                    headers["Authorization"] = f"Bearer {token['access_token']}"
                session.headers.update(headers)

                return session

            except GleanTenantNotConfigured:
                # If the tenant is not configured, raise the expection so that the caller can handle it
                logging.error("Tenant not configured for Glean authentication")
                raise
            except grpc.RpcError as e:
                logging.error("Failed to get user settings: %s", e)
                raise
            except Exception as e:
                logging.error("Failed to create OAuth session: %s", e)
                raise

    # called when a token refreshes - update the user settings and session header
    def save_oauth_token(
        self,
        request_context: RequestContext,
        session: OAuth2Session,
        tenant_id: str,
        user_id: str,
        token: dict,
    ) -> None:
        """Save the OAuth token for the given tenant and user."""
        with tracer.start_as_current_span("save_oauth_token"):
            logging.info("Saving OAuth token for tenant %s", tenant_id)
            try:
                access_token = token.get("access_token")
                if not access_token:
                    logging.error("No access token in token: %s", token)
                    return

                settings_response = self.settings_client.get_user_settings(
                    request_context=request_context,
                    user_id=user_id,
                )
                settings = settings_response.settings
                if not settings.glean_user_settings:
                    settings.glean_user_settings.CopyFrom(
                        settings_pb2.GleanUserSettings()
                    )

                settings.glean_user_settings.access_token = access_token
                settings.glean_user_settings.refresh_token = token.get(
                    "refresh_token", settings.glean_user_settings.refresh_token
                )
                settings.glean_user_settings.token_type = token.get(
                    "token_type", BEARER_TOKEN_TYPE
                )

                self.settings_client.update_user_settings(
                    request_context=request_context,
                    request=settings_pb2.UpdateUserSettingsRequest(
                        user_id=user_id,
                        settings=settings,
                        expected_version=settings_response.version,
                    ),
                )

                # Update the existing session's token and headers
                session.token = token
                session.headers["Authorization"] = f"Bearer {access_token}"

            except grpc.RpcError as e:
                logging.error("Failed to get user settings: %s", e)
                return

    def _get_provider_config(
        self,
        tenant_oauth_settings: TenantOAuthSettings,
    ) -> OAuthConfig:
        """Get the provider configuration for the given provider."""
        with tracer.start_as_current_span("_get_provider_config"):
            if tenant_oauth_settings.provider == glean_pb2.Provider.GSUITE:
                return OAuthConfig(
                    token_url=GOOGLE_TOKEN_URL,
                    authorization_url=GOOGLE_AUTH_URL,
                    scopes=[
                        "openid",
                        "https://www.googleapis.com/auth/userinfo.email",
                        "https://www.googleapis.com/auth/userinfo.profile",
                    ],
                    auth_params={
                        "access_type": "offline",
                        "prompt": "consent",
                    },
                    refresh_kwargs={
                        "client_id": tenant_oauth_settings.client_id_secret.get_secret_value(),
                        "client_secret": tenant_oauth_settings.client_secret_secret.get_secret_value(),
                    },
                )
            if tenant_oauth_settings.provider == glean_pb2.Provider.OKTA:
                subdomain = tenant_oauth_settings.subdomain
                if not subdomain:
                    raise ValueError("Okta subdomain must be provided")
                return OAuthConfig(
                    token_url=("" if subdomain.startswith("https://") else "https://")
                    + subdomain
                    + "/oauth2/v1/token",
                    authorization_url=(
                        "" if subdomain.startswith("https://") else "https://"
                    )
                    + subdomain
                    + "/oauth2/v1/authorize",
                    scopes=["openid", "email", "profile", "offline_access"],
                    auth_params={
                        "response_mode": "query",
                    },
                    # this is a hack since okta requires basic auth for refresh tokens in the headers, but doing this overwrites the default headers
                    refresh_kwargs={
                        "headers": {
                            "Authorization": f"Basic {base64.b64encode(f'{tenant_oauth_settings.client_id_secret.get_secret_value()}:{tenant_oauth_settings.client_secret_secret.get_secret_value()}'.encode()).decode()}",
                            "Content-Type": "application/x-www-form-urlencoded",
                            "Accept": "application/json",
                        },
                    },
                )
            raise UnsupportedOperation("Unsupported OAuth provider")

    def _get_tenant_oauth_settings(
        self,
        request_context: RequestContext,
        tenant_id: str,
    ) -> TenantOAuthSettings:
        """Get OAuth settings for the tenant."""
        with tracer.start_as_current_span("_get_tenant_oauth_settings"):
            try:
                settings_response = self.settings_client.get_tenant_settings(
                    request_context=request_context,
                    request=settings_pb2.GetTenantSettingsRequest(),
                )

                tenant_settings = settings_response.settings
                if not tenant_settings.glean_tenant_settings:
                    logging.error(
                        "No glean tenant settings found for tenant %s", tenant_id
                    )
                    raise GleanTenantNotConfigured()

                glean_settings = tenant_settings.glean_tenant_settings

                if (
                    glean_settings.oauth_provider
                    == glean_pb2.Provider.UNSPECIFIED_PROVIDER
                ):
                    logging.error(
                        "No OAuth provider specified for tenant %s", tenant_id
                    )
                    raise GleanTenantNotConfigured()

                return TenantOAuthSettings(
                    client_id_secret=SecretStr(glean_settings.oauth_client_id),
                    client_secret_secret=SecretStr(glean_settings.oauth_client_secret),
                    provider=cast(glean_pb2.Provider, glean_settings.oauth_provider),
                    subdomain=glean_settings.oauth_subdomain,
                    glean_domain=glean_settings.glean_domain,
                )
            except grpc.RpcError as e:
                logging.error(
                    "Failed to fetch OAuth settings for tenant %s: %s",
                    tenant_id,
                    e,
                )
                raise

    def get_tenant_api_url(
        self, tenant_id: str, request_context: RequestContext
    ) -> str:
        """Get the Glean API URL for the given tenant."""
        with tracer.start_as_current_span("get_tenant_api_url"):
            try:
                tenant_settings = self._get_tenant_oauth_settings(
                    request_context, tenant_id
                )
                return f"{tenant_settings.glean_domain}/rest/api/v1"
            except GleanTenantNotConfigured:
                logging.error("Tenant not configured for Glean authentication")
                raise

    def hydrate_glean_user_settings(
        self,
        code: str,
        context: grpc.ServicerContext,
        request_context: RequestContext,
        tenant_id: str,
        user_id: SecretStr,
    ) -> glean_pb2.HydrateGleanUserSettingsResponse:
        """Hydrate the Glean user settings with OAuth tokens from Google."""
        with tracer.start_as_current_span("hydrate_glean_user_settings"):
            logging.info("Hydrating Glean user settings")

            try:
                tenant_oauth_settings = self._get_tenant_oauth_settings(
                    request_context, tenant_id
                )

                session = self.get_oauth_session(
                    request_context, tenant_id, user_id.get_secret_value()
                )

            except GleanTenantNotConfigured:
                logging.error("Tenant not configured for Glean authentication")
                context.abort(
                    code=grpc.StatusCode.INVALID_ARGUMENT,
                    details="Tenant not configured for Glean authentication",
                )
                return glean_pb2.HydrateGleanUserSettingsResponse()

            except UnsupportedOperation as e:
                logging.error("Unsupported OAuth provider: %s", e)
                context.abort(
                    code=grpc.StatusCode.INVALID_ARGUMENT,
                    details="Unsupported OAuth provider",
                )
                return glean_pb2.HydrateGleanUserSettingsResponse()

            provider_config = self._get_provider_config(tenant_oauth_settings)

            tokens = session.fetch_token(
                provider_config.token_url,
                code=code,
                client_secret=tenant_oauth_settings.client_secret_secret.get_secret_value(),
                timeout=5,
            )

            self.save_oauth_token(
                request_context, session, tenant_id, user_id.get_secret_value(), tokens
            )

            return glean_pb2.HydrateGleanUserSettingsResponse()

    def get_oauth_url(
        self,
        request_context: RequestContext,
        session: OAuth2Session | None = None,
        tenant_id: str = "",
    ) -> str:
        """Construct the Google OAuth URL. This is used to redirect the user to the OAuth provider. Either tenant_id or tenant_oauth_settings must be provided. Returns empty string if the tenant is not configured for Glean authentication."""
        with tracer.start_as_current_span("get_oauth_url"):
            logging.info("Getting OAuth URL")

            if session is None:
                if not tenant_id:
                    logging.error("Must provide either sessionInfo or tenant_id")
                    raise ValueError(
                        "Must provide either sessionInfo or tenant_id and user_id"
                    )

                try:
                    session = self.get_oauth_session(request_context, tenant_id)
                except GleanTenantNotConfigured:
                    logging.error("Tenant not configured for Glean authentication")
                    return ""

            try:
                tenant_settings = self._get_tenant_oauth_settings(
                    request_context, tenant_id
                )
                provider_config = self._get_provider_config(
                    tenant_settings,
                )
            except Exception as e:
                logging.error("Failed to get provider config: %s", e)
                return ""

            authorization_url, _ = session.authorization_url(
                provider_config.authorization_url,
                **provider_config.auth_params,
            )

            # The library added a state parameter that we don't want to use
            # TODO: eventually, we should use this state parameter to verify the callback and handle user mappings another way

            # Parse URL and remove state parameter
            parsed_url = urlparse(authorization_url)
            params = parse_qs(parsed_url.query)
            params.pop("state", None)  # Remove state parameter if it exists

            # Reconstruct URL without state
            new_query = urlencode(params, doseq=True)
            final_url = parsed_url._replace(query=new_query).geturl()

            return final_url

    def get_glean_tenant_settings(
        self,
        request_context: RequestContext,
    ) -> glean_pb2.GetGleanTenantSettingsResponse:
        """Get the Glean tenant settings."""
        with tracer.start_as_current_span("get_glean_tenant_settings"):
            logging.info("Getting Glean tenant settings")
            try:
                settings_response = self.settings_client.get_tenant_settings(
                    request_context=request_context,
                    request=settings_pb2.GetTenantSettingsRequest(),
                )
                settings = settings_response.settings
                if not settings or not settings.glean_tenant_settings:
                    return glean_pb2.GetGleanTenantSettingsResponse()

                return glean_pb2.GetGleanTenantSettingsResponse(
                    oauth_provider=settings.glean_tenant_settings.oauth_provider,
                    glean_domain=settings.glean_tenant_settings.glean_domain,
                    public_key=settings.glean_tenant_settings.glean_jwt_key_pair_info.public_key,
                )
            except grpc.RpcError as e:
                logging.error("Failed to get user settings: %s", e)
                return glean_pb2.GetGleanTenantSettingsResponse()

    def hydrate_glean_tenant_settings(
        self,
        request: glean_pb2.HydrateGleanTenantSettingsRequest,
        context: grpc.ServicerContext,
        request_context: RequestContext,
        tenant_id: str,
        tenant_name: str,
    ) -> glean_pb2.HydrateGleanTenantSettingsResponse:
        """Hydrate the Glean tenant settings with the URL for the customer's Glean server."""
        with tracer.start_as_current_span("hydrate_glean_tenant_settings"):
            if not ENABLE_GLEAN.get(
                base.feature_flags.get_global_context().bind_attribute(
                    "tenant_name", tenant_name
                )
            ):
                logging.warning("Glean is not enabled, skipping hydrate")
                context.abort(
                    code=grpc.StatusCode.PERMISSION_DENIED,
                    details="Glean is not enabled, skipping hydrate",
                )

            logging.info("Hydrating Glean tenant settings")

            try:
                settings_response = self.settings_client.get_tenant_settings(
                    request_context=request_context,
                    request=settings_pb2.GetTenantSettingsRequest(),
                )

                settings = settings_response.settings

                if not settings.glean_tenant_settings:
                    settings.glean_tenant_settings.CopyFrom(
                        settings_pb2.GleanTenantSettings()
                    )

                glean_domain = request.glean_domain.rstrip("/")

                # Make sure that the glean_domain is in a normalized form with schema + host
                # or is an empty string (to allow deletion).
                # e.g.: https://augment-be.glean.com
                if not (glean_domain == "" or validate_glean_domain(glean_domain)):
                    context.abort(
                        code=grpc.StatusCode.INVALID_ARGUMENT,
                        details="Invalid Glean domain. A properly formatted domain "
                        "must have a host and scheme and no path (no trailing slashes), "
                        "e.g.: https://example-be.glean.com",
                    )

                settings.glean_tenant_settings.glean_domain = glean_domain

                self.settings_client.update_tenant_settings(
                    request=settings_pb2.UpdateTenantSettingsRequest(
                        settings=settings,
                        expected_version=settings_response.version,
                    ),
                    request_context=request_context,
                )

                return glean_pb2.HydrateGleanTenantSettingsResponse()

            except grpc.RpcError as e:
                logging.error("Failed to update tenant settings: %s", e)
                context.abort(
                    code=grpc.StatusCode.INTERNAL,
                    details="Failed to update tenant settings",
                )
                raise

    def generate_new_key_pair(
        self,
        request_context: RequestContext,
    ) -> tuple[str, str]:
        """Generate a new key pair for the tenant for JWT authentication and store it
        in the tenant's settings.

        Returns:
            tuple: (public_key, private_key). The returned keys are PEM-encoded.
        """
        with tracer.start_as_current_span("generate_new_key_pair"):
            logging.info("Generating new key pair")
            public_key, private_key = _create_es256_keypair()

            # Because Settings service updates require the full settings object,
            # we need to read the settings first to then apply updates to that
            # state.
            settings_response = self.settings_client.get_tenant_settings(
                request_context=request_context,
                request=settings_pb2.GetTenantSettingsRequest(),
            )
            settings = settings_response.settings
            if not settings.glean_tenant_settings:
                settings.glean_tenant_settings.CopyFrom(
                    settings_pb2.GleanTenantSettings()
                )

            settings.glean_tenant_settings.glean_jwt_key_pair_info.CopyFrom(
                settings_pb2.GleanJWTKeyPairInfo(
                    algorithm="ES256",
                    key_id="augment-kid-0",
                    private_key=private_key,
                    public_key=public_key,
                )
            )

            self.settings_client.update_tenant_settings(
                request=settings_pb2.UpdateTenantSettingsRequest(
                    settings=settings,
                    expected_version=settings_response.version,
                ),
                request_context=request_context,
            )

            return public_key, private_key


def _create_es256_keypair():
    """
    Create a new ES256 (ECDSA with P-256 curve) key pair.

    Returns:
        tuple: (public_key_pem, private_key_pem)
    """
    # Generate a new ECDSA private key using P-256 curve
    private_key = ec.generate_private_key(ec.SECP256R1())

    # Get the public key
    public_key = private_key.public_key()

    # Serialize the private key to PEM format
    private_key_pem = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption(),
    ).decode("utf-8")

    # Serialize the public key to PEM format
    public_key_pem = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo,
    ).decode("utf-8")

    return (public_key_pem, private_key_pem)

load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_to_json")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "utils",
    srcs = ["utils.py"],
    deps = [
        "//services/integrations/glean:glean_py_proto",
        "//services/settings:settings_py_proto",
    ],
)

py_library(
    name = "config_lib",
    srcs = ["config.py"],
    deps = [
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        requirement("dataclasses_json"),
        requirement("pydantic"),
    ],
)

py_binary(
    name = "server",
    srcs = [
        "glean_auth_processor.py",
        "glean_jwt_client.py",
        "glean_query_processor.py",
        "glean_search_client.py",
        "glean_server.py",
    ],
    main = "glean_server.py",
    deps = [
        ":config_lib",
        ":glean_request_insight_builder",
        ":utils",
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//base/third_party_clients:clients",
        "//base/tracing:tracing_py",
        "//services/integrations/glean:glean_py_proto",
        "//services/integrations/glean/client:client_py",
        "//services/integrations/lib:http_client",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/publisher:publisher_py",
        "//services/settings/client:client_py",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
        requirement("requests_oauthlib"),
    ],
)

pytest_test(
    name = "server_test",
    srcs = [
        "glean_auth_processor_test.py",
        "glean_query_processor_test.py",
        "glean_search_client_test.py",
    ],
    deps = [
        ":server",
        "//services/settings:settings_py_proto",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/chat_host/server:anthropic-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

py_library(
    name = "glean_request_insight_builder",
    srcs = ["glean_request_insight_builder.py"],
    deps = [
        ":config_lib",
        "//services/integrations/glean:glean_py_proto",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/publisher:publisher_py",
        requirement("structlog"),
    ],
)

pytest_test(
    name = "glean_request_insight_builder_test",
    srcs = ["glean_request_insight_builder_test.py"],
    deps = [
        ":config_lib",
        ":glean_request_insight_builder",
        "//services/integrations/glean:glean_py_proto",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

jsonnet_to_json(
    name = "metadata_json",
    src = "METADATA.jsonnet",
    outs = ["METADATA.json"],
    visibility = [],
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

py_binary(
    name = "glean_search_client_manual_test",
    srcs = ["glean_search_client_manual_test.py"],
    main = "glean_search_client_manual_test.py",
    deps = [
        ":config_lib",
        ":server",
        "//services/integrations/glean:glean_py_proto",
        "//services/integrations/glean/agent_tools:glean_tools",
        "//services/integrations/lib:http_client",
        "//services/lib/request_context:request_context_py",
        "//services/settings:settings_py_proto",
        "//services/settings/client:client_py",
        requirement("pydantic"),
        requirement("pyjwt"),
        requirement("requests"),
    ],
)

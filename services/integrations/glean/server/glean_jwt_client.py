"""JWT Authentication for Glean API.

This module implements JWT authentication for Glean API using ES256 keys.
"""

import logging
import time
import uuid
from typing import Literal

import jwt
import opentelemetry.trace
import requests
from pydantic import SecretStr

from services.integrations.glean.server.config import (
    GleanJ<PERSON>TConfig,
    PrivateKeyInfo,
)
from services.integrations.glean.server.glean_auth_processor import (
    GleanTenantNotConfigured,
)
from services.integrations.glean.server.utils import (
    validate_glean_domain,
    validate_key_pair_info,
)
from services.integrations.lib.http_client import BaseHTTPClient
from services.lib.request_context.request_context import RequestContext
from services.settings import settings_pb2
from services.settings.client.client import SettingsClient

tracer = opentelemetry.trace.get_tracer(__name__)


class GleanJWTAuthError(Exception):
    """Base class for Glean JWT authentication errors."""

    pass


class GleanJWTClient(BaseHTTPClient):
    """JWT HTTP Client for Glean API.

    This class handles JWT token generation and signing for authenticating
    with Glean's API as a partner.
    """

    def __init__(self, config: GleanJWTConfig, settings_client: SettingsClient):
        """Initialize the JWT client."""
        super().__init__()
        self.config = config
        self.settings_client = settings_client

    def generate_jwt(
        self,
        *,
        private_key_info: PrivateKeyInfo,
        glean_domain: str,
    ) -> str:
        """Generate a JWT token for Glean authentication.

        Args:
            key: The PEM-encoded private key to use for signing the JWT
            algorithm: The algorithm to use for signing the JWT.
            key_id: The key ID to use for signing the JWT
            glean_domain: The customer's Glean API base URL e.g.:
                'https://augment-be.glean.com'.

        Returns:
            JWT token as string
        """
        with tracer.start_as_current_span("generate_jwt"):
            now = int(time.time())
            expiration = now + (self.config.expiration_minutes * 60)

            # Remove https:// from the domain for the audience claim
            glean_domain = glean_domain.replace("https://", "")

            # Create JWT payload
            payload = {
                "iss": self.config.issuer,
                "aud": glean_domain,
                "exp": expiration,
                "iat": now,
                "jti": str(uuid.uuid4()),  # Generate a unique ID for this token
            }

            logging.info(f"Generated JWT with 'jti'={payload['jti']}")

            # Create JWT headers
            headers = {
                "alg": private_key_info.algorithm,
                "typ": "JWT",
                "kid": private_key_info.key_id,
            }

            # Sign the JWT
            token = jwt.encode(
                payload=payload,
                key=private_key_info.key,
                algorithm=private_key_info.algorithm,
                headers=headers,
            )

            # Convert token to string if it's bytes
            if isinstance(token, bytes):
                token = token.decode("utf-8")

            return token

    def get_auth_headers(
        self,
        private_key_info: PrivateKeyInfo,
        glean_domain: str,
        user_email: SecretStr,
    ) -> dict[str, str]:
        """Get all headers needed for Glean API authentication.

        Args:
            glean_domain: The customer's Glean API base URL e.g.:
                'https://augment-be.glean.com'.
            user_email: Email of the user to act as

        Returns:
            Dictionary of headers
        """
        with tracer.start_as_current_span("get_auth_headers"):
            token = self.generate_jwt(
                private_key_info=private_key_info, glean_domain=glean_domain
            )

            headers = {
                "Authorization": f"Bearer {token}",
                "X-Glean-Auth-Type": "Partner-Jwt",
                "X-Scio-Actas": user_email.get_secret_value(),
                "Content-Type": "application/json",
            }

            return headers

    def _get_glean_tenant_settings(
        self, request_context: RequestContext, tenant_id: str
    ) -> settings_pb2.GleanTenantSettings:
        """Returns the Glean tenant settings for the given tenant."""
        with tracer.start_as_current_span("get_glean_tenant_settings"):
            try:
                settings_response = self.settings_client.get_tenant_settings(
                    request_context=request_context,
                    request=settings_pb2.GetTenantSettingsRequest(),
                )
            except Exception as e:
                raise GleanJWTAuthError("Failed to get tenant settings") from e

            tenant_settings = settings_response.settings
            if (
                not tenant_settings.glean_tenant_settings
                or not tenant_settings.glean_tenant_settings.glean_domain
            ):
                logging.error("No glean tenant settings found for tenant %s", tenant_id)
                raise GleanTenantNotConfigured()

            return tenant_settings.glean_tenant_settings

    def make_jwt_glean_request(
        self,
        request_context: RequestContext,
        tenant_id: str,
        path: str,
        method: Literal["GET", "POST"],
        user_email: SecretStr,
        payload: dict,
        timeout: int,
    ) -> dict:
        """Make an authenticated request to Glean API using JWT authentication.

        Args:
            path: Path to the API endpoint
            method: HTTP method (GET, POST, etc.)
            payload: Request payload (for POST/PUT)
            user_email: Email of the user to act as
            timeout: Request timeout in seconds

        Returns:
            Response dictionary from the API
        """
        with tracer.start_as_current_span("make_jwt_glean_request"):
            if not user_email.get_secret_value():
                # While technically a Glean request can be made without an email,
                # in practice, it's not useful because the Glean API will return
                # almost no results (and it's confusing).
                raise ValueError(
                    "User email was empty. Unable to make a Glean JWT request"
                )

            glean_tenant_settings = self._get_glean_tenant_settings(
                request_context, tenant_id
            )

            glean_domain = glean_tenant_settings.glean_domain.rstrip("/")
            if not validate_glean_domain(glean_domain):
                raise GleanJWTAuthError(
                    f"Invalid glean domain: {glean_domain}. "
                    "A properly formatted domain must have a host and scheme and no path, "
                    "e.g.: https://augment-be.glean.com"
                )

            if not validate_key_pair_info(
                glean_tenant_settings.glean_jwt_key_pair_info
            ):
                raise GleanJWTAuthError(
                    "Invalid key pair info. Has user generated key pair?"
                )

            key_pair_info = glean_tenant_settings.glean_jwt_key_pair_info

            base_url = f"{glean_domain}/rest/api/v1/"

            headers = self.get_auth_headers(
                private_key_info=PrivateKeyInfo(
                    key=key_pair_info.private_key,
                    algorithm=key_pair_info.algorithm,
                    key_id=key_pair_info.key_id,
                ),
                glean_domain=glean_domain,
                user_email=user_email,
            )

            try:
                response = self.make_request(
                    path=path,
                    method=method,
                    base_url=base_url,
                    headers=headers,
                    json=payload,
                    timeout=timeout,
                )
                response.raise_for_status()

            except requests.exceptions.HTTPError as e:
                logging.error("HTTP error occurred: %s", str(e))
                raise
            except requests.exceptions.RequestException as e:
                logging.error("Request failed: %s", str(e))
                raise
            except Exception as e:
                logging.error("Unexpected error: %s", str(e))
                raise
            return response.json()

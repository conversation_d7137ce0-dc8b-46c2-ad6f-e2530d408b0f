"""Tests for GleanRequestInsightBuilder."""

from unittest.mock import MagicMock

from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.integrations.glean import glean_pb2
from services.integrations.glean.server.glean_request_insight_builder import (
    GleanRequestInsightBuilder,
)
from services.integrations.glean.server.config import GleanQueryProcessorConfig
from services.request_insight import request_insight_pb2


def _test_update_request(
    request_id: str, events: list[request_insight_pb2.RequestEvent], auth_info: AuthInfo
):
    del auth_info
    return request_insight_pb2.UpdateRequestInfoRequest(
        request_id=request_id,
        events=events,
    )


def test_record_request():
    """Tests that record_request correctly submits events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = GleanRequestInsightBuilder(ri_publisher)
    glean_request = glean_pb2.SearchRequest(query="test query")

    request_context = RequestContext.create()
    query_processor_config = GleanQueryProcessorConfig(
        gcp_project_id="test-project",
        gcp_region="us-central1",
        model_name="test-model",
        temperature=0.7,
        max_output_tokens=1024,
        max_results=5,
    )

    ri_builder.record_request(
        request=glean_request,
        request_context=request_context,
        auth_info=AuthInfo(
            tenant_id="test_tenant_id",
            tenant_name="test_tenant",
            shard_namespace="test_shard_namespace",
            cloud="test_cloud",
        ),
        query_processor_config=query_processor_config,
    )

    assert (
        len(ri_publisher.publish_request_insight.call_args.args) == 1
    ), "Expected exactly one argument in publish_request_insight call"
    request = ri_publisher.publish_request_insight.call_args[0][0]
    assert request.request_id == request_context.request_id, "Request ID mismatch"
    assert len(request.events) == 1, "Expected exactly one event"
    assert request.events[0].glean_request == request_insight_pb2.RIGleanRequest(
        request=glean_request,
        request_source="unknown",
        query_processor_config=request_insight_pb2.RIGleanRequest.RIGleanQueryProcessorConfig(
            gcp_project_id="test-project",
            gcp_region="us-central1",
            model_name="test-model",
            temperature=0.7,
            max_output_tokens=1024,
            max_results=5,
        ),
    ), "Glean request event mismatch"


def test_record_response():
    """Tests that record_response correctly submits events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = GleanRequestInsightBuilder(ri_publisher)
    glean_response = glean_pb2.SearchResponse(
        documents=[
            glean_pb2.Document(
                title="Test Doc",
                content="Test content",
                url="http://test.com",
            )
        ]
    )

    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
    )

    ri_builder.record_response(
        response=glean_response,
        generate_search_queries_request=request_insight_pb2.ThirdPartyModelRequest(
            prompt="test prompt",
            system_prompt="test system prompt",
            messages=[],
        ),
        generate_search_queries_response=["test query", "test query 2"],
        request_context=request_context,
        auth_info=auth_info,
    )

    assert (
        len(ri_publisher.publish_request_insight.call_args.args) == 1
    ), "Expected exactly one argument in publish_request_insight call"
    request = ri_publisher.publish_request_insight.call_args[0][0]
    assert request.request_id == request_context.request_id, "Request ID mismatch"
    assert len(request.events) == 1, "Expected exactly one event"
    assert request.events[0].glean_response == request_insight_pb2.RIGleanResponse(
        response=glean_response,
        generate_search_queries_request=request_insight_pb2.ThirdPartyModelRequest(
            prompt="test prompt",
            system_prompt="test system prompt",
            messages=[],
        ),
        generate_search_queries_response=["test query", "test query 2"],
    ), "Glean response event mismatch"

load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "glean_proto",
    srcs = ["glean.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "glean_py_proto",
    protos = [":glean_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

go_grpc_library(
    name = "glean_go_proto",
    importpath = "github.com/augmentcode/augment/services/integrations/glean/proto",
    proto = ":glean_proto",
    visibility = ["//services:__subpackages__"],
)

ts_proto_library(
    name = "glean_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":glean_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

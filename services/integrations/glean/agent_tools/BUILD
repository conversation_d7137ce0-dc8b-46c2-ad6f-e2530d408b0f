load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

py_library(
    name = "glean_api_model",
    srcs = ["glean_api_model.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("pydantic"),
    ],
)

py_library(
    name = "json_helpers",
    srcs = ["json_helpers.py"],
)

py_library(
    name = "glean_tools",
    srcs = ["glean_tools.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":glean_api_model",
        ":json_helpers",
        "//services/agents:tool",
        "//services/integrations/glean/client:client_py",
        "//services/lib/request_context:request_context_py",
        requirement("protobuf"),
        requirement("pydantic"),
    ],
)

pytest_test(
    name = "json_helpers_test",
    srcs = [
        "json_helpers.py",
        "json_helpers_test.py",
    ],
)

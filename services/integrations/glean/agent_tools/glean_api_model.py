from typing import Literal

from pydantic import BaseModel, Field

date_relation_description = """\
The relation type to use when filtering dates. EQUALS will filter down to documents that match the
date (or date range when used with special values like "past_5_days").

LT (less than) can be used with YYYY-MM-DD dates or one of (past_day, past_week, past_month, yesterday, today)
to filter down to documents that were created before the specified date
exclusively. For example, if you specify 2025-04-02 as the date, only documents
updated on or before 2025-04-01 will be returned.

GT (greater than) can be used with YYYY-MM-DD dates to filter down to documents that were created after the specified date.
GT can also be used with the special value "yesterday" to get results from the past 24 hours.
"""

date_value_description = """\
The date value to compare against, either as a specific date in YYYY-MM-DD format, or as a special value.

The possible special values are:
- past_day
- past_week
- past_month
- yesterday
- today
- past_<n>_days
- past_<n>_weeks
- past_<n>_months
- past_<n>_years
- past_<n>_days

Where <n> is any number. e.g.: past_5_days.
"""


# Separated out so that the JSON schema can contain more specific instructions
# for the the model when using special date values.
# https://developers.glean.com/client/search/filtering-results#specific-dates
class LastUpdatedAtFacetFilterValue(BaseModel):
    """Special case for last_updated_at using special values."""

    relationType: Literal["EQUALS"] = Field(
        description=date_relation_description,
    )

    value: str = Field(
        description=date_value_description,
    )


last_updated_at_filter_description = """\
The date value filters. Either a specific set of one or more dates, or a date
range. Date ranges are specified by using one GT and one LT filter. Date ranges
can also be left open by only specifying a GT or LT filter.
"""


# We special case last_updated_at because it's a common filter that
# can use special values. This ideally makes it clearer to the model
# how to invoke the tool.
# For more details on how time filters are special:
# https://developers.glean.com/client/search/filtering-results#time-filters
class LastUpdatedAtFacetFilter(BaseModel):
    fieldName: Literal["last_updated_at"]

    values: list[LastUpdatedAtFacetFilterValue] = Field(
        description=last_updated_at_filter_description,
    )


class GenericFacetFilterValue(BaseModel):
    # https://developers.glean.com/api-reference/client/search/search#body-request-options-facet-filters-values-relation-type
    # Only including EQUALS for now as it's unclear when ID_EQUALS should be used,
    # and GT and LT only apply for select fields and we don't want the search to fail.
    relationType: Literal["EQUALS"]

    value: str = Field(
        description="Select results where the field matches this value.",
    )


# The generic catchall definition of a facet filter.
# https://developers.glean.com/api-reference/client/search/search#body-request-options-facet-filters
class GenericFacetFilter(BaseModel):
    fieldName: str = Field(
        description="The name of the field to filter on.",
    )

    values: list[GenericFacetFilterValue] = Field(
        description="The values to filter on. The values are OR'ed together when filtering.",
    )


# As we define more filter types we can add them here.
# For now we exclude generic facet filters as the model won't be able to
# take advantage of them without more complex logic.
FacetFilter = LastUpdatedAtFacetFilter


class RequestOptions(BaseModel):
    facetFilters: list[FacetFilter] | None = Field(
        default=None,
        description="Filters to apply to the search results.",
    )


class GleanSearchPayload(BaseModel):
    query: str = Field(
        description="A search query for the content to find in Glean. e.g. 'Quarter 2 feature roadmap'",
    )

    pageSize: int = Field(
        default=5,
        ge=1,
        le=10,
        description="Number of results to return.",
    )

    maxSnippetSize: int = Field(
        default=500,
        ge=1,
        le=1000,
        description="Maximum size of each snippet in characters. Larger snippets let you see more of a document in search results.",
    )

    requestOptions: RequestOptions | None = Field(
        default=None,
        description="Additional options for the search request. See https://developers.glean.com/client/operation/search/ for details.",
    )


class GleanSearchCall(BaseModel):
    """Input schema for Glean's /search endpoint."""

    method: Literal["POST"] = "POST"

    path: Literal["/search"] = "/search"

    payload: GleanSearchPayload

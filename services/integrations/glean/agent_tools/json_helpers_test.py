from json_helpers import json_recursive_remove, json_recursive_select


def test_json_recursive_select():
    # Test simple base cases

    # Primitive types should be filtered out as base cases
    assert json_recursive_select(123, select_keys=["allowed"]) is None
    assert json_recursive_select("string", select_keys=["allowed"]) is None

    # Simple example covering different cases.
    test_input = {
        "allowed": {"someotherkey": 1},
        "non_selected_key_with_selected_child": {"allowed": True},
        "notallowed2": "allowed",
        "notallowed3": [{"allowed": 1}, {"notallowed": "whocares"}],
    }

    expected_output = {
        "allowed": {"someotherkey": 1},
        "non_selected_key_with_selected_child": {"allowed": True},
        "notallowed3": [{"allowed": 1}],
    }

    selectlist = ["allowed"]
    result = json_recursive_select(test_input, select_keys=selectlist)

    assert result == expected_output, f"Expected {expected_output}, got {result}"


def test_json_recursive_remove():
    # Test simple base cases

    # Primitive types should be returned as base cases
    assert json_recursive_remove(123, ["remove"]) == 123
    assert json_recursive_remove("string", ["remove"]) == "string"

    # Simple example covering different cases.
    test_input = {
        "remove": {"someotherkey": 1},
        "key_with_empty_object_should_be_removed": {"remove": True},
        "empty_list_should_be_removed": [{"remove": 1}],
        "notremoved2": "remove",
        "notremoved3": [{"remove": 1}, {"notremoved": "whocares"}],
    }

    expected_output = {
        "notremoved2": "remove",
        "notremoved3": [{"notremoved": "whocares"}],
    }

    remove_list = ["remove"]
    result = json_recursive_remove(test_input, remove_keys=remove_list)

    assert result == expected_output, f"Expected {expected_output}, got {result}"

"""
Helpers for processing JSON-like Python structures.
"""

from typing import Any


def json_recursive_select(
    obj: Any,
    select_keys: list[str],
) -> Any | None:
    """Filter a JSON-like object, selecting out keys in the allowlist and their ancestors.

    e.g.:
    {
        "a": {
            "b": {
                "a": 1,
            },
            "c": 2,
        },
        "d": 3,
        "e": [
            {
                "b": 4,
            },
            {
                "f": 5,
            },
        ]
    }

    with allowlist=["b", "d"] would return:
    {
        "a": {
            "b": {
                "a": 1,
            },
        },
        "d": 3,
        "e": [
            {
                "b": 4,
            },
        ],
    }

    Args:
        obj: Any JSON-serializable object (dict, list, or primitive types)
        allowlist: List of keys to keep in the object

    Returns:
        Filtered object with only allowlisted keys and their ancestors or None if no
        allowlisted keys are present in the object.
    """
    if isinstance(obj, list):
        cleaned_objects = [json_recursive_select(item, select_keys) for item in obj]
        filtered_list = [item for item in cleaned_objects if item is not None]

        if not filtered_list:
            return None
        else:
            return filtered_list
    elif isinstance(obj, dict):
        result = {}
        for key, value in obj.items():
            if key in select_keys:
                # If the key is in allowlist, keep it and all its contents
                result[key] = value
            else:
                # If the key is not in allowlist, recursively clean its value
                cleaned_value = json_recursive_select(value, select_keys)
                if cleaned_value is not None:
                    result[key] = cleaned_value

        if not result:
            return None
        else:
            return result
    else:
        # Base case: we are a primitive value. allowlist key is not present
        # so return None
        return None


def json_recursive_remove(obj: Any, remove_keys: list[str]) -> Any | None:
    """Recursively remove keys from a JSON-like object.

    e.g.:
    {
        "a": {
            "b": {
                "a": 1,
            },
            "c": 2,
        },
        "d": 3,
    }

    with keys_to_remove=["b", "d"] would return:
    {
        "a": {
            "c": 2,
        },
    }

    Args:
        obj: Any JSON-serializable object (dict, list, or primitive types)
        keys_to_remove: List of keys to remove from the object

    Returns:
        Filtered object with all keys_to_remove and their values removed.
    """
    if isinstance(obj, list):
        filtered_list = [json_recursive_remove(item, remove_keys) for item in obj]
        remove_nones = [item for item in filtered_list if item is not None]
        if not remove_nones:
            return None
        else:
            return remove_nones
    elif isinstance(obj, dict):
        result = {}
        for key, value in obj.items():
            if key in remove_keys:
                continue
            filtered_result = json_recursive_remove(value, remove_keys)
            if filtered_result is not None:
                result[key] = filtered_result

        if not result:
            return None
        else:
            return result
    else:
        # Base case: we are a primitive value. No need to remove anything.
        return obj

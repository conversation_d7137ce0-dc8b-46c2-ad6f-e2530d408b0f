"""Tool for querying Glean."""

import json
import grpc
import structlog
from google.rpc import status_pb2 as status_pb2
from pydantic import BaseModel, Field

from services.agents import agents_pb2
from services.agents.tool import (
    EmptyExtraToolInput,
    Tool<PERSON><PERSON>ent<PERSON>tionError,
    ValidatedTool,
)
from services.integrations.glean.agent_tools.glean_api_model import GleanSearchCall
from services.integrations.glean.agent_tools.json_helpers import (
    json_recursive_remove,
    json_recursive_select,
)
from services.integrations.glean.client.client import GleanClient
from services.lib.request_context.request_context import RequestContext

log = structlog.get_logger()


class GleanApiToolInput(BaseModel):
    summary: str = Field(
        description="A short human-readable summary of what this API call will do. This helps users understand the purpose of the call.",
    )

    # Looks a little weird for now because there's just one type, but in the future
    # additional call types with their API definitions can be OR'ed into this
    # type definition. Every type here should define `method`, `path`, and
    # `payload`.
    call: GleanSearchCall = Field(
        description="The specific API call to make (path, method, and payload).",
    )


class GleanApiTool(ValidatedTool[GleanApiToolInput, EmptyExtraToolInput]):
    """Tool for accessing the Glean API"""

    id = agents_pb2.RemoteToolId.GLEAN
    # This tool is read-only, so it's safe
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    name = "glean"
    description = """\
Make calls to Glean's API.
Glean's API allows searching for information across multiple data sources
in a company (including Slack, Notion, Jira, etc. as long as each has
been configured as a Glean datasource).

Use this tool whenever you want to look for information specific to the
user's company that could be found in Slack messages or other internal
documentation.

This tool can *occasionally* help with questions about the codebase,
but you should try to answer questions about the codebase
using the source code first (NOT Glean). Note that READMEs and comments
within the codebase can often help to answer a good number of questions
and Glean results do *not* include this information.

Note that links returned by Glean searches typically can NOT be webfetched
directly and thus you shouldn't try to get them with a web fetch.

Example user questions where searching Glean is recommended:
- "How were similar incidents handled in the past?"
- "What are our outstanding tasks from last week's meeting notes?"
- "Are there guides for updating the database schema?"
- "Onboarding documentation for setting up a dev VM?"
    - Note: this is an example of a question where it would be a good
        idea to check both the codebase and Glean.
"""

    input_model = GleanApiToolInput

    def __init__(self, glean_client: GleanClient):
        """Initialize the Glean tool.

        Args:
            glean_client: Glean client for calling into the Glean service.
        """
        super().__init__()
        self.glean_client = glean_client

    def check_validated_input_safe(self, validated_input: GleanApiToolInput) -> bool:
        # This tool is read-only, so it's always safe
        return True

    def run_validated(
        self,
        validated_input: GleanApiToolInput,
        extra_tool_input: EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Glean search tool.

        Args:
            validated_input: GleanToolInput containing the search query
            extra_tool_input: GleanExtraToolInput containing the API token or EmptyExtraToolInput
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted search results

        Raises:
            ToolAuthenticationError: If the user is not authenticated with Glean.
            ValueError: When extra tool input is not the right type.
        """
        try:
            response = self.glean_client.call_glean_api(
                request_context=request_context,
                path=validated_input.call.path,
                method=validated_input.call.method,
                payload=validated_input.call.payload.model_dump_json(),
            )

            # Raise an error with status code and message if not 2xx.
            if not (200 <= response.status_code < 300):
                # Important that we raise the raw response without cleaning on error
                # so we don't miss error details.
                raise RuntimeError(
                    f"Failed to call Glean API: {response.status_code}, {response.response}"
                )

            cleaned_response = clean_api_response(json.loads(response.response))

            return json.dumps(cleaned_response, indent=2)

        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.UNAUTHENTICATED:  # type: ignore
                raise ToolAuthenticationError(
                    "Failed to authenticate with Glean. Please check your credentials."
                )
            log.error("Failed to search Glean", exc_info=e)
            raise RuntimeError("Failed to search Glean") from e
        except Exception as e:
            log.error("Failed to search Glean", exc_info=e)
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        """Check if the Glean tool is available.

        Args:
            request_context: The request context to use.

        Returns:
            ToolAvailabilityStatus indicating whether the tool is available.
        """
        try:
            if self.glean_client.is_tool_configured(request_context):
                return agents_pb2.ToolAvailabilityStatus.AVAILABLE
            else:
                return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED
        except grpc.RpcError as e:
            # Special case the service being unavailable as it's a common error,
            # so we don't want to log when it occurs.
            if e.code() != grpc.StatusCode.UNAVAILABLE:  # type: ignore
                log.error("Failed to check Glean availability", exc_info=e)
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS
        except Exception as e:
            log.error("Failed to check Glean availability", exc_info=e)
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # type: ignore
        """
        Test the connection to Glean by fetching the current user's information.

        Args:
            request_context: The request context to use.

        Returns:
            Status code indicating the result of the operation.
        """
        # Check the user's credentials
        try:
            if not self.glean_client.is_tool_configured(request_context):
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="Glean credentials are not configured.",
                )

            # Test authenticated access by making a test search call.
            # This specific payload was specifically chosen after some
            # manually poking around Glean's API to minimize request latency.
            response = self.glean_client.call_glean_api(
                request_context=request_context,
                path="/search",
                method="POST",
                payload={
                    "query": "test",
                    "maxSnippetSize": 0,
                    "pageSize": 0,
                    "requestOptions": {"responseHints": ["FACET_RESULTS"]},
                },
            )

            if response.status_code == 200:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.OK.value[0],
                    message="Glean credentials are successfully configured.",
                )
            elif response.status_code == 401:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="Authentication failed. Please re-authenticate with Glean.",
                )
            else:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNKNOWN.value[0],
                    message=(
                        f"Unable to connect to Glean. Received status code "
                        f"{response.status_code}, message: {response.response}"
                    ),
                )
        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.UNAUTHENTICATED:  # type: ignore
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="Glean credentials are not configured. Please configure them in the settings page.",
                )
            else:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.INTERNAL.value[0],
                    message="Failed to check Glean credentials.",
                )
        except Exception as e:
            log.error("Failed to check Glean credentials", error=str(e), exc_info=True)
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message="Failed to check Glean credentials.",
            )

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.glean_client.get_oauth_url(request_context)
        except Exception as e:
            log.error("Failed to get Glean OAuth URL: %s", e)
            return ""


default_response_select_keys: list[str] = [
    "datasource",
    "docType",
    "title",
    "url",
    "name",
    "text",
    "errorInfo",
]
"""
Allowlist of keys to select out of Glean API responses by default. Even if
deeply nested under keys not in the allowlist, a key in the allowlist will be
kept along with all its ancestors.
"""

default_response_remove_keys: list[str] = [
    # "fullText" fields are only included for Slack, and seem to have
    # off by one errors (they reference the wrong message). So exclude.
    "fullText",
    "fullTextList",
]


def clean_api_response(
    response: dict,
    *,
    select_keys: list[str] = default_response_select_keys,
    remove_keys: list[str] = default_response_remove_keys,
) -> dict | None:
    """Clean an API response object by first recursively selecting out keys in
    an allowlist, then removing any remaining keys in a blocklist.

    Args:
        response: The API response to clean
        select_keys: List of keys to select out of the response. Unselected keys with
            no selected children are removed.
        remove_keys: List of keys to delete from the response after selecting.

    Returns:
        Cleaned API response or None if all keys are removed.
    """
    # First select the keys we want to keep
    selected_response = json_recursive_select(response, select_keys)
    # Then remove any keys we don't want
    cleaned_response = json_recursive_remove(selected_response, remove_keys)
    return cleaned_response

// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/glean/glean.proto (package glean, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { CallGleanApiRequest, CallGleanApiResponse, GenerateNewKeyPairRequest, GenerateNewKeyPairResponse, GetGleanTenantSettingsRequest, GetGleanTenantSettingsResponse, GetOAuthURLRequest, GetOAuthURLResponse, HydrateGleanTenantSettingsRequest, HydrateGleanTenantSettingsResponse, HydrateGleanUserSettingsRequest, HydrateGleanUserSettingsResponse, IsToolConfiguredRequest, IsToolConfiguredResponse, SearchRequest, SearchResponse } from "./glean_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service glean.Glean
 */
export declare const Glean: {
  readonly typeName: "glean.Glean",
  readonly methods: {
    /**
     * @generated from rpc glean.Glean.GetGleanTenantSettings
     */
    readonly getGleanTenantSettings: {
      readonly name: "GetGleanTenantSettings",
      readonly I: typeof GetGleanTenantSettingsRequest,
      readonly O: typeof GetGleanTenantSettingsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc glean.Glean.HydrateGleanTenantSettings
     */
    readonly hydrateGleanTenantSettings: {
      readonly name: "HydrateGleanTenantSettings",
      readonly I: typeof HydrateGleanTenantSettingsRequest,
      readonly O: typeof HydrateGleanTenantSettingsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc glean.Glean.HydrateGleanUserSettings
     */
    readonly hydrateGleanUserSettings: {
      readonly name: "HydrateGleanUserSettings",
      readonly I: typeof HydrateGleanUserSettingsRequest,
      readonly O: typeof HydrateGleanUserSettingsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc glean.Glean.GenerateNewKeyPair
     */
    readonly generateNewKeyPair: {
      readonly name: "GenerateNewKeyPair",
      readonly I: typeof GenerateNewKeyPairRequest,
      readonly O: typeof GenerateNewKeyPairResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc glean.Glean.Search
     */
    readonly search: {
      readonly name: "Search",
      readonly I: typeof SearchRequest,
      readonly O: typeof SearchResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc glean.Glean.CallGleanApi
     */
    readonly callGleanApi: {
      readonly name: "CallGleanApi",
      readonly I: typeof CallGleanApiRequest,
      readonly O: typeof CallGleanApiResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc glean.Glean.IsToolConfigured
     */
    readonly isToolConfigured: {
      readonly name: "IsToolConfigured",
      readonly I: typeof IsToolConfiguredRequest,
      readonly O: typeof IsToolConfiguredResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc glean.Glean.GetOAuthURL
     */
    readonly getOAuthURL: {
      readonly name: "GetOAuthURL",
      readonly I: typeof GetOAuthURLRequest,
      readonly O: typeof GetOAuthURLResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};


syntax = "proto3";

package glean;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/augmentcode/augment/services/integrations/glean/proto";

service Glean {
  // Experimental APIs, subject to change

  // Get the Glean tenant settings, returning the provider if it is configured
  rpc GetGleanTenantSettings(GetGleanTenantSettingsRequest) returns (GetGleanTenantSettingsResponse) {}

  // Hydrate the Glean tenant settings with the client ID and secret the admin creates for authentication to Glean
  rpc HydrateGleanTenantSettings(HydrateGleanTenantSettingsRequest) returns (HydrateGleanTenantSettingsResponse) {}

  // Takes a temporary oauth code during the oauth flow and exchange the code for a personal access token that is stored and can be used to make Glean API calls on their behalf.
  rpc HydrateGleanUserSettings(HydrateGleanUserSettingsRequest) returns (HydrateGleanUserSettingsResponse) {}

  // Generate a new key pair for the tenant for JWT authentication. Updates tenant settings to store the newly
  // created key pair.
  rpc GenerateNewKeyPair(GenerateNewKeyPairRequest) returns (GenerateNewKeyPairResponse);

  // Search for documents in Glean based on a natural language query.
  rpc Search(SearchRequest) returns (SearchResponse);

  // Send a search query directly to Glean (with no agent expansion) and return the API call results.
  rpc CallGleanApi(CallGleanApiRequest) returns (CallGleanApiResponse);

  // Check if the Glean tool is configured for the tenant (i.e.: auth is setup).
  rpc IsToolConfigured(IsToolConfiguredRequest) returns (IsToolConfiguredResponse);

  // Get the oauth url to log into Glean. This is the URL to show users if they are not logged into Glean.
  // It will redirect to the callback url /gleanCallback amd hit HydrateGleanUserSettings to save the user's
  // credentials and use them to make Glean API calls on their behalf.
  rpc GetOAuthURL(GetOAuthURLRequest) returns (GetOAuthURLResponse);
}

// The OAuth SSO provider
enum Provider {
  UNSPECIFIED_PROVIDER = 0;
  GSUITE = 1;
  AZURE = 2;
  OKTA = 3;
  ONELOGIN = 4;
}

message GetGleanTenantSettingsRequest {}

// Get Glean tenant settings for display in the Glean setup UI.
message GetGleanTenantSettingsResponse {
  // the provider for the tenant OAuth in Glean
  // if not set, this should be UNSPECIFIED_PROVIDER
  Provider oauth_provider = 1 [deprecated = true];

  // The base URL for the customer's Glean API.
  // ex. https://augment-be.glean.com
  string glean_domain = 2;

  // The public key for JWT authentication to Glean.
  string public_key = 3;
}

message HydrateGleanTenantSettingsRequest {
  string oauth_client_id = 1 [
    debug_redact = true,
    deprecated = true
  ];
  string oauth_client_secret = 2 [
    debug_redact = true,
    deprecated = true
  ];
  string oauth_subdomain = 3 [deprecated = true];
  Provider oauth_provider = 4 [deprecated = true];
  string glean_domain = 5;
}

message HydrateGleanTenantSettingsResponse {}

message HydrateGleanUserSettingsRequest {
  string code = 1 [debug_redact = true];
}

message HydrateGleanUserSettingsResponse {}

message GetOAuthURLRequest {}

message GetOAuthURLResponse {
  string oauth_url = 1 [debug_redact = true];
}

message SearchRequest {
  // the chat request with its history
  string query = 1;
  bool include_private_docs = 2;

  // the augment user id (a uuid) of the user making the request
  // if not set, the user id is extracted from the auth info in the request context
  string user_id = 3;

  // The user email to use for JWT authentication to Glean.
  // If not set we fallback to the user email in the auth info if present.
  string user_email = 4;
}

enum DocumentType {
  UNSPECIFIED_DOCUMENT_TYPE = 0;
  PAGE = 1;
  MESSAGE = 2;
  CHANNEL = 3;
  CONVERSATION = 4;
  ISSUE = 5;
}

enum DataSource {
  UNSPECIFIED_DATA_SOURCE = 0;
  SLACK = 1;
  NOTION = 2;
  LINEAR = 3;
  GITHUB = 4;
}

message Person {
  string name = 1 [debug_redact = true];
  string id = 2;
}

message Document {
  enum Visibility {
    UNSPECIFIED_VISIBILITY = 0;
    PUBLIC_LINK = 1;
    PUBLIC_VISIBLE = 2;
    DOMAIN_VISIBLE = 3;
    DOMAIN_LINK = 4;
    PRIVATE = 5;
    SPECIFIC_PEOPLE_AND_GROUPS = 6;
  }

  message Snippet {
    string text = 1 [debug_redact = true];
    int32 start_range = 2;
    int32 end_range = 3;

    // The document id of that the snippet belongs to
    // in some cases the document_id might not match where it is on. For example if we take a conversation,
    // we receive snippet at the document level, but the ids on the snippets are the leaf document's ID.
    // if we do a messages API call for this search result, we will have all the leaf documents.
    // However, we plan to set a limit on the number of messages calls per search, and some calls may error out,
    // in these cases we will have snippets, and potentially full plain text from the getcontents call,
    // but the snippet spans will not match the document content, and we can discover that by matching document ids
    string document_id = 4;
  }

  string document_id = 1;
  string title = 2 [debug_redact = true];
  string content = 3 [debug_redact = true];
  DocumentType document_type = 4;
  DataSource data_source = 5;
  string url = 6 [debug_redact = true];

  // Children are comments or replies to the document
  // At the moments they are not nested, that is
  // comments do not have comments.
  // However this may change in the future.
  repeated Document children = 7;
  Person author = 8 [debug_redact = true];
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  Visibility visibility = 11;

  // Snippets are the sections of a document that match the search query.
  // They are in the same order as we receive them from Glean.
  repeated Snippet snippets = 12;

  // raw name string of the data source from Glean
  string data_source_name = 13;
}

message SearchResponse {
  repeated Document documents = 1;

  // oauth_url to log into Glean if the user is not logged in or the token is expired
  // will be empty if the user is already logged in and documents are returned
  string oauth_url = 2 [debug_redact = true];

  // Status code of the response. Non-200 on error.
  int32 status_code = 3;
}

message GenerateNewKeyPairRequest {}

message GenerateNewKeyPairResponse {
  // The newly created public key.
  string public_key = 1;
}

// NOTE: Other than the UNKNOWN method, the field names in this type should map
// to real HTTP methods as we use the string names for logic.
enum HttpMethod {
  UNKNOWN = 0;
  GET = 1;
  POST = 2;
}

message CallGleanApiRequest {
  // Glean API path (e.g. "/search")
  string path = 1;
  // Http method (GET, POST, etc.).
  HttpMethod method = 2;
  // JSON body/params for requests with a body.
  string payload = 3;
}

message CallGleanApiResponse {
  // The raw JSON response from the Glean API.
  string response = 1 [debug_redact = true];

  // The HTTP status code of the response. Either a
  // passthrough from the Glean API or a status code
  // raised by our implementation.
  int32 status_code = 2;
}

message IsToolConfiguredRequest {
  // The augment user id (a uuid) of the user making the request.
  // If not set, the user id is extracted from the auth info in the request context.
  // Needed for OAuth tenants to check if user is configured, although
  // unnecesssary for users in JWT tenants since JWT is configured tenant wide.
  string user_id = 1 [deprecated = true];
}

message IsToolConfiguredResponse {
  bool is_configured = 1;
}

syntax = "proto3";

package supabase;

import "google/protobuf/struct.proto";
import "google/rpc/status.proto";

// Service definition for Supabase integration
service Supabase {
  // Check if Supabase credentials are configured
  rpc IsConfigured(IsConfiguredRequest) returns (IsConfiguredResponse);

  // Get the Supabase OAuth URL for authentication flow
  rpc GetSupabaseOAuthUrl(GetSupabaseOAuthUrlRequest) returns (GetSupabaseOAuthUrlResponse);

  // Hydrate the Supabase settings with a code from the OAuth flow and store an access token
  rpc HydrateSupabaseSettings(HydrateSupabaseSettingsRequest) returns (HydrateSupabaseSettingsResponse);

  // Revoke the OAuth grant
  rpc RevokeOAuthGrant(RevokeOAuthGrantRequest) returns (RevokeOAuthGrantResponse);

  // Call the Supabase API with the given request
  rpc CallSupabaseApi(SupabaseApiRequest) returns (SupabaseApiResponse);

  // Test the Supabase connection by listing projects and updating settings
  rpc TestConnection(TestConnectionRequest) returns (TestConnectionResponse);

  // Get the list of Supabase projects for the user from settings
  rpc GetProjects(GetProjectsRequest) returns (GetProjectsResponse);
}

// Request to check if Supabase is configured
message IsConfiguredRequest {}

// Response indicating if Supabase is configured
message IsConfiguredResponse {
  bool is_configured = 1;
}

// Request to get the Supabase OAuth URL
message GetSupabaseOAuthUrlRequest {}

// Response containing the Supabase OAuth URL
message GetSupabaseOAuthUrlResponse {
  string oauth_url = 1 [debug_redact = true];
  int32 status_code = 2;
}

// Request to hydrate Supabase settings with OAuth code
message HydrateSupabaseSettingsRequest {
  string code = 1 [debug_redact = true];
}

// Response after hydrating Supabase settings
message HydrateSupabaseSettingsResponse {
  int32 status_code = 1;
  string session_id = 2 [debug_redact = true];
  repeated SupabaseProject projects = 3 [debug_redact = true];
}

// Represents a Supabase Project
message SupabaseProject {
  // The project reference ID
  string ref = 1;

  // The project name
  string name = 2 [debug_redact = true];

  // The project URL
  string url = 3 [debug_redact = true];

  // The project region
  string region = 4;
}

// Request to revoke OAuth grant
message RevokeOAuthGrantRequest {}

// Response after revoking OAuth grant
message RevokeOAuthGrantResponse {
  google.rpc.Status status = 1;
}

// HTTP Method enum for API calls
enum HTTPMethod {
  UNKNOWN = 0;
  GET = 1;
  POST = 2;
  PUT = 3;
  PATCH = 4;
  DELETE = 5;
}

// Request to call the Supabase API
message SupabaseApiRequest {
  // Supabase API path (e.g. "/v1/projects")
  string path = 1;

  // HTTP method (GET, POST, etc) (enum)
  HTTPMethod method = 2;

  // Optional: JSON body/params for requests
  optional google.protobuf.Struct data = 3 [debug_redact = true];
}

// Response from calling the Supabase API
message SupabaseApiResponse {
  // JSON response from Supabase API
  string response = 1 [debug_redact = true];

  // HTTP status code
  int32 status_code = 2;
}

// Request to test the Supabase connection
message TestConnectionRequest {}

// Response after testing the Supabase connection
message TestConnectionResponse {
  // Status code of the operation
  int32 status_code = 1;

  // Optional error message if the connection test fails
  string error_message = 2;
}

// Request to get the list of Supabase projects for the user from settings
message GetProjectsRequest {}

// Response containing the list of Supabase projects
message GetProjectsResponse {
  // List of Supabase projects
  repeated SupabaseProject projects = 1 [debug_redact = true];
}

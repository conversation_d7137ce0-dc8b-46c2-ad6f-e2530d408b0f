from dataclasses import dataclass, field
from typing import List, Optional

from common.settings.settings import Settings


@dataclass
class SupabaseProject:
    """Represents a Supabase project."""

    ref: str
    name: str
    url: str
    region: str


@dataclass
class SupabaseUserSettings(Settings):
    """Settings for Supabase integration."""

    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    supabase_url: Optional[str] = None
    projects: List[SupabaseProject] = field(default_factory=list)

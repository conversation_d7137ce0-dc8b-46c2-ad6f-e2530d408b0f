"""Configuration for the Supabase server."""

import pathlib
from dataclasses import dataclass, field
import typing

from dataclasses_json import dataclass_json

import services.lib.grpc.tls_config.tls_config as tls_config


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    token_exchange_endpoint: str


@dataclass_json
@dataclass
class SupabaseApiConfig:
    """Configuration for the Supabase API endpoints."""

    base_url: str = "https://api.supabase.com"
    auth_path: str = "/v1/oauth/authorize"
    token_path: str = "/v1/oauth/token"
    revoke_path: str = "/v1/oauth/revoke"
    timeout_seconds: int = 120

    @property
    def auth_url(self) -> str:
        """Get the full auth URL."""
        return f"{self.base_url}{self.auth_path}"

    @property
    def token_url(self) -> str:
        """Get the full token URL."""
        return f"{self.base_url}{self.token_path}"

    @property
    def revoke_url(self) -> str:
        """Get the full revoke URL."""
        return f"{self.base_url}{self.revoke_path}"


@dataclass_json
@dataclass
class Config:
    """Configuration for the Supabase server."""

    port: int
    feature_flags_sdk_key_path: typing.Optional[str]
    dynamic_feature_flags_endpoint: typing.Optional[str]
    auth_config: AuthConfig
    settings_endpoint: str
    supabase_app_callback_url: str
    oauth_app_secret_path: str
    supabase_api_config: SupabaseApiConfig = field(default_factory=SupabaseApiConfig)
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None
    central_server_mtls: typing.Optional[tls_config.ServerConfig] = None
    client_mtls: typing.Optional[tls_config.ClientConfig] = None
    server_mtls: typing.Optional[tls_config.ServerConfig] = None
    shutdown_grace_period_s: int = 25

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )

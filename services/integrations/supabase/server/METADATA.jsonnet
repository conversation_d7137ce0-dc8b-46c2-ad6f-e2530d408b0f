local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'supabase',
      kubecfg: {
        target: '//services/integrations/supabase/server:kubecfg',
        // Supabase is deployed everywhere.
        // Access is gated by feature flag enable_supabase_service
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['xiaolei', 'surbhi'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

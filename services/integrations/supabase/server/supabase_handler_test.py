"""Tests for the Supabase handler."""

from unittest import mock

import pytest

from services.integrations.supabase.server.supabase_handler import (
    SupabaseHand<PERSON>,
    SupabaseHandlerSettingsError,
)
from services.integrations.supabase.server.supabase_auth_processor import (
    SupabaseTokenExpiredError,
)
from services.lib.request_context.request_context import RequestContext
import services.settings.settings_pb2 as settings_pb2


@pytest.fixture
def request_context():
    """Create a proper RequestContext instance for testing."""
    return RequestContext.create(request_source="test")


@pytest.fixture
def settings_response():
    """Create a settings response with a test access token."""
    settings = settings_pb2.UserSettings()
    settings.supabase_user_settings.access_token = "test_access_token"
    response = settings_pb2.GetUserSettingsResponse()
    response.settings.CopyFrom(settings)
    response.version = "test-version-1"
    return response


@pytest.fixture
def handler_setup():
    """Set up the test fixture."""
    settings_client = mock.MagicMock()
    auth_processor = mock.MagicMock()
    config = mock.MagicMock()
    # Mock the supabase_api_config.base_url property
    config.supabase_api_config.base_url = "https://test.supabase.com"
    handler = SupabaseHandler(
        settings_client=settings_client,
        auth_processor=auth_processor,
        config=config,
    )
    return handler, settings_client, auth_processor


@pytest.fixture
def empty_settings_response():
    """Create a settings response with no access token."""
    settings = settings_pb2.UserSettings()
    settings.supabase_user_settings.access_token = ""  # Empty token
    response = settings_pb2.GetUserSettingsResponse()
    response.settings.CopyFrom(settings)
    response.version = "test-version-1"
    return response


def test_make_api_call_no_token(
    handler_setup, request_context, empty_settings_response
):
    """Test making an API call when there is no token."""
    handler, settings_client, auth_processor = handler_setup

    # Mock the auth processor to raise an exception for no token
    auth_processor.get_access_token.side_effect = SupabaseTokenExpiredError(
        "No access token available"
    )

    # Test making an API call - should raise an error
    with pytest.raises(SupabaseHandlerSettingsError) as excinfo:
        handler.make_api_call(
            path="test/path",
            method=1,  # GET
            request_context=request_context,
        )

    # Verify the error message
    assert "no access token available" in str(excinfo.value).lower()


def test_is_configured(handler_setup, request_context, settings_response):
    """Test checking if Supabase is configured."""
    handler, _, auth_processor = handler_setup

    # Mock the auth processor to return a token
    auth_processor.get_access_token.return_value = "test_access_token"

    # Test checking if configured
    is_configured = handler.is_configured(request_context)

    # Should be configured
    assert is_configured is True

    # Verify the auth processor was called to get the token
    auth_processor.get_access_token.assert_called_once_with(request_context)


def test_is_configured_not_configured(
    handler_setup, request_context, empty_settings_response
):
    """Test checking if Supabase is configured when it's not."""
    handler, settings_client, auth_processor = handler_setup

    # Mock the auth processor to raise an exception
    auth_processor.get_access_token.side_effect = SupabaseTokenExpiredError(
        "No access token available"
    )

    # Test checking if configured
    is_configured = handler.is_configured(request_context)

    # Should not be configured
    assert is_configured is False

    # Verify the auth processor was called to get the token
    auth_processor.get_access_token.assert_called_once_with(request_context)

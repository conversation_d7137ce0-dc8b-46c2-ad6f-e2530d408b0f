"""Handler for Supabase API interactions."""

import json
import logging
from typing import Any

import requests
from services.integrations.lib.http_client import BaseHTTPClient
from services.lib.request_context.request_context import RequestContext
from services.settings.client.client import SettingsClient
from services.integrations.supabase.server.supabase_auth_processor import (
    SupabaseAuthProcessor,
    SupabaseAuthInternalError,
)
from services.integrations.supabase.server.config import Config
import services.integrations.supabase.supabase_pb2 as supabase_pb2

log = logging.getLogger(__name__)


class SupabaseHandlerError(Exception):
    """Base class for Supabase handler errors."""


class SupabaseHandlerSettingsError(SupabaseHandlerError):
    """Error raised when there's an issue with Supabase settings."""


class SupabaseHandler:
    """Handler for Supabase API interactions."""

    def __init__(
        self,
        settings_client: SettingsClient,
        auth_processor: SupabaseAuthProcessor,
        config: Config,
    ):
        """Initialize the Supabase handler.

        Args:
            settings_client: Client for accessing user settings.
            auth_processor: Auth processor for token management and refresh.
            config: The Supabase configuration.
        """
        self.settings_client = settings_client
        self.auth_processor = auth_processor
        self.config = config

    def is_configured(self, request_context: RequestContext) -> bool:
        """Check if Supabase is configured for the user.

        Attempts to get a valid token from the auth processor. If successful,
        Supabase is considered configured.

        Args:
            request_context: The request context.

        Returns:
            True if Supabase is configured, False otherwise.
        """
        try:
            # Try to get a token - if this succeeds, we're configured
            self.auth_processor.get_access_token(request_context)
            return True
        except SupabaseAuthInternalError as e:
            log.info(f"Supabase service encountered internal error: {e}")
            raise
        except Exception as e:
            log.info(f"Supabase not configured: {e}")
            return False

    def get_projects(self, request_context: RequestContext) -> list[dict[str, Any]]:
        """Get a list of all Supabase projects for the authenticated user.

        Args:
            request_context: The request context.

        Returns:
            A list of projects with their details.

        Raises:
            SupabaseHandlerSettingsError: If Supabase is not configured or there's an issue with settings.
        """
        try:
            status_code, response_text = self.make_api_call(
                path="/v1/projects",
                method=supabase_pb2.HTTPMethod.GET,
                request_context=request_context,
            )

            if status_code < 200 or status_code >= 300:
                log.error(f"Failed to get projects: HTTP {status_code}")
                return []

            data = json.loads(response_text)

            # Check if the response is a dictionary with a 'projects' key
            if isinstance(data, dict) and "projects" in data:
                projects = data["projects"]
            # Check if the response is already a list
            elif isinstance(data, list):
                projects = data
            # If we can't determine the structure, log and return an empty list
            else:
                log.warning(f"Unexpected response structure from Supabase API: {data}")
                return []

            if not projects:
                log.warning("No projects returned from Supabase")
                return []

            return projects

        except Exception as e:
            log.error(f"Failed to get projects from Supabase: {e}")
            raise SupabaseHandlerSettingsError(f"Failed to get projects: {e}") from e

    def make_api_call(
        self,
        path: str,
        method: supabase_pb2.HTTPMethod.ValueType,
        request_context: RequestContext,
        data: dict[str, Any] | None = None,
    ) -> tuple[int, str]:
        """Make an arbitrary API call to the Supabase API.

        Args:
            path: The API path to call.
            method: The HTTP method to use as an enum value.
            request_context: The request context.
            data: Optional JSON data. For GET requests, this will be converted to query parameters.
                 For other methods, it will be sent as the request body.

        Returns:
            A tuple of (status_code, response_text).

        Raises:
            SupabaseHandlerSettingsError: If Supabase is not configured or there's an issue with settings.
            ValueError: If the method is UNKNOWN.
        """
        try:
            if method == supabase_pb2.HTTPMethod.UNKNOWN:
                raise ValueError("Unknown HTTP method")

            access_token = self.auth_processor.get_access_token(request_context)

            api_client = BaseHTTPClient(
                base_url=self.config.supabase_api_config.base_url,
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {access_token}",
                },
            )

            method_str = supabase_pb2.HTTPMethod.Name(method)

            # Handle data differently based on HTTP method: as query params for GET, as JSON body for others
            params = None
            json_data = None
            if data:
                if method_str == "GET":
                    params = data
                else:
                    json_data = data

            try:
                response = api_client.make_request(
                    path,
                    method=method_str,
                    json=json_data,
                    params=params,
                )
                return response.status_code, response.text
            except requests.exceptions.HTTPError as e:
                # For HTTP errors, preserve the status code and response text
                log.error(f"HTTP error in Supabase API call: {e}")
                if hasattr(e, "response") and e.response is not None:
                    return e.response.status_code, e.response.text
                raise

        except Exception as e:
            log.error(f"Failed to make Supabase API call: {e}")
            raise SupabaseHandlerSettingsError(
                f"Failed to make Supabase API call: {e}"
            ) from e

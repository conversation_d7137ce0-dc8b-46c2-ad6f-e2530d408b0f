"""Tests for the Supabase server."""

import unittest
from unittest import mock

import grpc
from google.protobuf.struct_pb2 import Struct

from services.integrations.supabase.server.supabase_server import SupabaseServer
from services.integrations.supabase.server.supabase_handler import Supabase<PERSON>andler
from services.integrations.supabase.server.supabase_auth_processor import (
    SupabaseAuthProcessor,
)
from services.integrations.supabase.server.config import Config
import services.integrations.supabase.supabase_pb2 as supabase_pb2
import services.settings.settings_pb2 as settings_pb2
from services.lib.request_context.request_context import RequestContext
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)


class SupabaseServerTest(unittest.TestCase):
    """Tests for the Supabase server."""

    def setUp(self):
        """Set up the test."""
        # Mock the config
        self.config = mock.MagicMock(spec=Config)
        self.config.settings_endpoint = "test-settings-endpoint"
        self.config.supabase_app_callback_url = "https://example.com/callback"
        self.config.oauth_app_secret_path = "/tmp/test_oauth_secret.json"
        self.config.central_client_mtls = (
            None  # Set to None to avoid TLS credential issues
        )

        # Mock the request insight publisher
        self.ri_publisher = mock.MagicMock(spec=RequestInsightPublisher)

        # Mock the handler and auth processor that will be created by the server
        self.supabase_handler = mock.MagicMock(spec=SupabaseHandler)
        self.supabase_auth_processor = mock.MagicMock(spec=SupabaseAuthProcessor)

        # Mock the settings client
        self.settings_client = mock.MagicMock()

        # Create the server with mocked dependencies
        with mock.patch(
            "services.integrations.supabase.server.supabase_server.SettingsClient"
        ) as mock_settings_client_class, mock.patch(
            "services.integrations.supabase.server.supabase_server.SupabaseAuthProcessor"
        ) as mock_auth_processor_class, mock.patch(
            "services.integrations.supabase.server.supabase_server.SupabaseHandler"
        ) as mock_handler_class, mock.patch(
            "services.integrations.supabase.server.supabase_server.tls_config.get_client_tls_creds"
        ) as mock_get_client_tls_creds:
            # Set up the mocks to return our test instances
            mock_settings_client_class.return_value = self.settings_client
            mock_auth_processor_class.return_value = self.supabase_auth_processor
            mock_handler_class.return_value = self.supabase_handler
            mock_get_client_tls_creds.return_value = (
                None  # Return None for TLS credentials
            )

            # Create the server
            self.server = SupabaseServer(
                config=self.config,
                request_insight_publisher=self.ri_publisher,
            )

        # Mock the auth info
        self.auth_info_patcher = mock.patch(
            "services.integrations.supabase.server.supabase_server.get_auth_info_from_grpc_context"
        )
        self.mock_get_auth_info = self.auth_info_patcher.start()
        self.mock_auth_info = mock.MagicMock()
        self.mock_auth_info.tenant_id = "test-tenant-id"
        self.mock_get_auth_info.return_value = self.mock_auth_info

        # Mock the request context
        self.request_context_patcher = mock.patch(
            "services.integrations.supabase.server.supabase_server.RequestContext"
        )
        self.mock_request_context_class = self.request_context_patcher.start()
        self.mock_request_context = mock.MagicMock(spec=RequestContext)
        self.mock_request_context_class.from_grpc_context.return_value = (
            self.mock_request_context
        )

    def tearDown(self):
        """Tear down the test."""
        self.auth_info_patcher.stop()
        self.request_context_patcher.stop()

    def test_is_configured(self):
        """Test the IsConfigured method."""
        # Mock the handler's is_configured method
        self.supabase_handler.is_configured.return_value = True

        # Create a request and context
        request = supabase_pb2.IsConfiguredRequest()
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.IsConfigured(request, context)

        # Verify the response
        self.assertTrue(response.is_configured)

        # Verify the handler was called
        self.supabase_handler.is_configured.assert_called_once_with(
            self.mock_request_context
        )

    def test_fetch_and_update_projects(self):
        """Test the _fetch_and_update_projects method."""
        # Instead of testing the actual implementation, we'll test the interface
        # by mocking the method and verifying it's called correctly

        # Create a mock project object to return
        project_obj = supabase_pb2.SupabaseProject(
            ref="test-ref",
            name="test-project",
            url="https://test-project.supabase.co",
            region="us-west-1",
        )

        # Mock the _fetch_and_update_projects method
        original_method = self.server._fetch_and_update_projects
        self.server._fetch_and_update_projects = mock.MagicMock(
            return_value=([project_obj], True)
        )

        try:
            # Call the method
            project_objects, success = self.server._fetch_and_update_projects(
                self.mock_request_context
            )

            # Verify the method was called with the correct arguments
            self.server._fetch_and_update_projects.assert_called_once_with(
                self.mock_request_context
            )

            # Verify the response
            self.assertTrue(success)
            self.assertEqual(len(project_objects), 1)
            self.assertEqual(project_objects[0].ref, "test-ref")
            self.assertEqual(project_objects[0].name, "test-project")
            self.assertEqual(project_objects[0].url, "https://test-project.supabase.co")
            self.assertEqual(project_objects[0].region, "us-west-1")
        finally:
            # Restore the original method
            self.server._fetch_and_update_projects = original_method

    def test_fetch_and_update_projects_error(self):
        """Test the _fetch_and_update_projects method with an error."""
        # Mock the handler's get_projects method to raise an exception
        self.supabase_handler.get_projects.side_effect = Exception("Test error")

        # Call the method
        project_objects, success = self.server._fetch_and_update_projects(
            self.mock_request_context
        )

        # Verify the response
        self.assertFalse(success)
        self.assertEqual(len(project_objects), 0)

    def test_call_supabase_api(self):
        """Test the CallSupabaseApi method."""
        # Mock the handler's make_api_call method
        self.supabase_handler.make_api_call.return_value = (200, '{"success": true}')

        # Create a request and context
        data = Struct()
        data.update({"key": "value"})

        request = supabase_pb2.SupabaseApiRequest(
            path="/v1/projects",
            method=supabase_pb2.HTTPMethod.GET,
            data=data,
        )
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.CallSupabaseApi(request, context)

        # Verify the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.response, '{"success": true}')

        # Verify the handler was called with the correct arguments
        self.supabase_handler.make_api_call.assert_called_once_with(
            path="/v1/projects",
            method=supabase_pb2.HTTPMethod.GET,
            request_context=self.mock_request_context,
            data={"key": "value"},
        )

    def test_call_supabase_api_error(self):
        """Test the CallSupabaseApi method with an error."""
        # Mock the handler's make_api_call method to raise an exception
        self.supabase_handler.make_api_call.side_effect = Exception("Test error")

        # Create a request and context
        request = supabase_pb2.SupabaseApiRequest(
            path="/v1/projects",
            method=supabase_pb2.HTTPMethod.GET,
        )
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.CallSupabaseApi(request, context)

        # Verify the response
        self.assertEqual(response.status_code, 500)
        self.assertEqual(response.response, "Error: Test error")

    def test_get_supabase_oauth_url(self):
        """Test the GetSupabaseOAuthUrl method."""
        # Mock the auth processor's get_oauth_url method
        self.supabase_auth_processor.get_oauth_url.return_value = (
            "https://example.com/oauth"
        )

        # Create a request and context
        request = supabase_pb2.GetSupabaseOAuthUrlRequest()
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.GetSupabaseOAuthUrl(request, context)

        # Verify the response
        self.assertEqual(response.oauth_url, "https://example.com/oauth")
        self.assertEqual(response.status_code, 0)  # OK status

        # Verify the auth processor was called
        self.supabase_auth_processor.get_oauth_url.assert_called_once()

    def test_get_supabase_oauth_url_unconfigured(self):
        """Test the GetSupabaseOAuthUrl method when Supabase is not configured."""
        # Import the error class
        from services.integrations.supabase.server.supabase_auth_processor import (
            SupabaseAuthUnconfiguredError,
        )

        # Mock the auth processor's get_oauth_url method to raise an error
        self.supabase_auth_processor.get_oauth_url.side_effect = (
            SupabaseAuthUnconfiguredError("Not configured")
        )

        # Create a request and context
        request = supabase_pb2.GetSupabaseOAuthUrlRequest()
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.GetSupabaseOAuthUrl(request, context)

        # Verify the response
        self.assertEqual(response.oauth_url, "")
        self.assertEqual(response.status_code, 16)  # UNAUTHENTICATED status

    def test_hydrate_supabase_settings(self):
        """Test the HydrateSupabaseSettings method."""
        # Mock the auth processor's hydrate_supabase_settings method
        mock_response = supabase_pb2.HydrateSupabaseSettingsResponse(
            status_code=0,  # OK status
            session_id="test-session-id",
        )
        self.supabase_auth_processor.hydrate_supabase_settings.return_value = (
            mock_response
        )

        # Mock the _fetch_and_update_projects method
        project = supabase_pb2.SupabaseProject(
            ref="test-ref",
            name="test-project",
            url="https://test-project.supabase.co",
            region="us-west-1",
        )
        with mock.patch.object(
            self.server, "_fetch_and_update_projects", return_value=([project], True)
        ):
            # Create a request and context
            request = supabase_pb2.HydrateSupabaseSettingsRequest(code="test-code")
            context = mock.MagicMock(spec=grpc.ServicerContext)

            # Call the method
            response = self.server.HydrateSupabaseSettings(request, context)

            # Verify the response
            self.assertEqual(response.status_code, 0)  # OK status
            self.assertEqual(response.session_id, "test-session-id")
            self.assertEqual(len(response.projects), 1)
            self.assertEqual(response.projects[0].ref, "test-ref")
            self.assertEqual(response.projects[0].name, "test-project")

            # Verify the auth processor was called with the correct arguments
            self.supabase_auth_processor.hydrate_supabase_settings.assert_called_once_with(
                request, context, self.mock_request_context
            )

            # Verify _fetch_and_update_projects was called
            self.server._fetch_and_update_projects.assert_called_once_with(
                self.mock_request_context
            )

    def test_revoke_oauth_grant(self):
        """Test the RevokeOAuthGrant method."""
        # Create a request and context
        request = supabase_pb2.RevokeOAuthGrantRequest()
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.RevokeOAuthGrant(request, context)

        # Verify the response
        self.assertEqual(response.status.code, 0)  # OK status
        self.assertEqual(response.status.message, "Success")

        # Verify the auth processor was called
        self.supabase_auth_processor.revoke_token.assert_called_once_with(
            self.mock_request_context
        )

    def test_revoke_oauth_grant_nonexistent(self):
        """Test the RevokeOAuthGrant method when there's no token to revoke."""
        # Import the error class
        from services.integrations.supabase.server.supabase_auth_processor import (
            SupabaseRevokeNonexistentError,
        )

        # Mock the auth processor's revoke_token method to raise an error
        self.supabase_auth_processor.revoke_token.side_effect = (
            SupabaseRevokeNonexistentError("No token to revoke")
        )

        # Create a request and context
        request = supabase_pb2.RevokeOAuthGrantRequest()
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.RevokeOAuthGrant(request, context)

        # Verify the response
        self.assertEqual(response.status.code, 5)  # NOT_FOUND status
        self.assertEqual(response.status.message, "No token to revoke")

    def test_test_connection(self):
        """Test the TestConnection method."""
        # Mock the handler's is_configured method
        self.supabase_handler.is_configured.return_value = True

        # Mock the _fetch_and_update_projects method
        with mock.patch.object(
            self.server, "_fetch_and_update_projects", return_value=([], True)
        ):
            # Create a request and context
            request = supabase_pb2.TestConnectionRequest()
            context = mock.MagicMock(spec=grpc.ServicerContext)

            # Call the method
            response = self.server.TestConnection(request, context)

            # Verify the response
            self.assertEqual(response.status_code, 0)  # OK status
            self.assertEqual(response.error_message, "")

            # Verify the handler was called
            self.supabase_handler.is_configured.assert_called_once_with(
                self.mock_request_context
            )

            # Verify _fetch_and_update_projects was called
            self.server._fetch_and_update_projects.assert_called_once_with(
                self.mock_request_context
            )

    def test_test_connection_not_configured(self):
        """Test the TestConnection method when Supabase is not configured."""
        # Mock the handler's is_configured method
        self.supabase_handler.is_configured.return_value = False

        # Create a request and context
        request = supabase_pb2.TestConnectionRequest()
        context = mock.MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = self.server.TestConnection(request, context)

        # Verify the response
        self.assertEqual(response.status_code, 16)  # UNAUTHENTICATED status
        self.assertEqual(response.error_message, "Supabase is not configured")

        # Verify the handler was called
        self.supabase_handler.is_configured.assert_called_once_with(
            self.mock_request_context
        )

    def test_test_connection_fetch_error(self):
        """Test the TestConnection method when fetching projects fails."""
        # Mock the handler's is_configured method
        self.supabase_handler.is_configured.return_value = True

        # Mock the _fetch_and_update_projects method to indicate failure
        with mock.patch.object(
            self.server, "_fetch_and_update_projects", return_value=([], False)
        ):
            # Create a request and context
            request = supabase_pb2.TestConnectionRequest()
            context = mock.MagicMock(spec=grpc.ServicerContext)

            # Call the method
            response = self.server.TestConnection(request, context)

            # Verify the response
            self.assertEqual(response.status_code, 13)  # INTERNAL status
            self.assertEqual(
                response.error_message, "Failed to fetch projects or update settings"
            )

            # Verify the handler was called
            self.supabase_handler.is_configured.assert_called_once_with(
                self.mock_request_context
            )

            # Verify _fetch_and_update_projects was called
            self.server._fetch_and_update_projects.assert_called_once_with(
                self.mock_request_context
            )


if __name__ == "__main__":
    unittest.main()

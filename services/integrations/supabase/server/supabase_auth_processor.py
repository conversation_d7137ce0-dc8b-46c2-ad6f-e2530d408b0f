"""Supabase authentication processor for OAuth flow."""

import json
import logging
import time
from pathlib import Path
from typing import Any
from datetime import datetime, timedelta
import base64

import grpc
import requests
from pydantic import SecretStr
from google.protobuf.timestamp_pb2 import Timestamp

from services.lib.request_context.request_context import RequestContext
from services.settings.client.client import SettingsClient
from services.integrations.supabase.server.config import Config
import services.integrations.supabase.supabase_pb2 as supabase_pb2
import services.settings.settings_pb2 as settings_pb2


class SupabaseAuthError(Exception):
    """Base class for Supabase auth errors."""


class SupabaseAuthUnconfiguredError(SupabaseAuthError):
    """Error raised when Supabase auth is not configured."""


class SupabaseAuthApiError(SupabaseAuthError):
    """Error raised when there's an issue with the Supabase API."""


class SupabaseAuthServiceUnavailableError(SupabaseAuthError):
    """Error raised when external service is unavailable."""


class SupabaseAuthInternalError(SupabaseAuthError):
    """Error raised when there's an internal error in the auth processor."""


class SupabaseRevokeNonexistentError(SupabaseAuthError):
    """Error raised when trying to revoke a nonexistent token."""


class SupabaseTokenExpiredError(SupabaseAuthError):
    """Error raised when a token is expired and cannot be refreshed."""


class SupabaseNoTokenError(SupabaseAuthError):
    """Error raised when no tokens are available."""


class SupabaseAuthProcessor:
    """Processor for Supabase authentication and OAuth flow."""

    def __init__(
        self,
        settings_client: SettingsClient,
        callback_url: str,
        oauth_secret_path: str,
        config: Config,
        oauth_client_id: SecretStr | None = None,
        oauth_client_secret: SecretStr | None = None,
    ):
        """Initialize the Supabase auth processor.

        Args:
            settings_client: Client for accessing user settings.
            callback_url: URL to redirect to after OAuth flow.
            oauth_secret_path: Path to the mounted OAuth secret file.
            oauth_client_id: Optional client ID for testing.
            oauth_client_secret: Optional client secret for testing.
        """
        self.settings_client = settings_client
        self.callback_url = callback_url
        self.oauth_secret_path = oauth_secret_path
        self.config = config
        self.api_config = config.supabase_api_config

        # For testing, allow direct injection of secrets
        self.oauth_client_id = oauth_client_id
        self.oauth_client_secret = oauth_client_secret

        # If not provided, load from mounted secret file
        if not oauth_client_id or not oauth_client_secret:
            self._load_oauth_secrets()

    def _load_oauth_secrets(self):
        """Load OAuth secrets from mounted secret file."""
        try:
            # Read the JSON file containing client_id and client_secret
            oauth_secret_json = Path(self.oauth_secret_path).read_text()
            oauth_secrets = json.loads(oauth_secret_json)

            if "client_id" in oauth_secrets and "client_secret" in oauth_secrets:
                self.oauth_client_id = SecretStr(oauth_secrets["client_id"])
                self.oauth_client_secret = SecretStr(oauth_secrets["client_secret"])
                logging.info("Loaded Supabase OAuth secrets from mounted secret file")
            else:
                logging.warning(
                    "Supabase OAuth secret file does not contain required fields"
                )
        except Exception as e:
            logging.error(f"Failed to load Supabase OAuth secrets: {e}")

    def get_oauth_url(self) -> str:
        """Get the Supabase OAuth URL. This is used to redirect the user to authorize the app.

        Returns:
            The OAuth URL to redirect the user to.

        Raises:
            SupabaseAuthUnconfiguredError: If OAuth client ID is not configured.
        """
        if not self.oauth_client_id:
            raise SupabaseAuthUnconfiguredError(
                "Supabase OAuth client ID not configured"
            )

        return (
            f"{self.api_config.auth_url}"
            f"?client_id={self.oauth_client_id.get_secret_value()}"
            f"&redirect_uri={self.callback_url}"
            f"&response_type=code"
        )

    def _exchange_code_for_tokens(self, code: str) -> dict:
        """Exchange OAuth code for access and refresh tokens.

        Args:
            code: The authorization code from the OAuth flow.

        Returns:
            Dictionary with tokens and other information.

        Raises:
            SupabaseAuthUnconfiguredError: If OAuth credentials are not configured.
            SupabaseAuthApiError: If there's an issue with the Supabase API.
        """
        if not self.oauth_client_id or not self.oauth_client_secret:
            raise SupabaseAuthUnconfiguredError(
                "Supabase OAuth credentials not configured"
            )

        # Prepare form data for x-www-form-urlencoded content type
        data = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": self.callback_url,
            "client_id": self.oauth_client_id.get_secret_value(),
            "client_secret": self.oauth_client_secret.get_secret_value(),
        }

        # Create basic auth header
        auth_header = f"{self.oauth_client_id.get_secret_value()}:{self.oauth_client_secret.get_secret_value()}"
        encoded_auth = base64.b64encode(auth_header.encode()).decode()

        try:
            response = requests.post(
                self.api_config.token_url,
                data=data,  # Use data instead of json for form urlencoded
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Accept": "application/json",
                    "Authorization": f"Basic {encoded_auth}",
                },
                timeout=self.api_config.timeout_seconds,
            )

            # Do not check for 200.
            # The supabase server returns 201 for success.
            if 300 <= response.status_code < 500:
                raise SupabaseAuthApiError(
                    f"Failed to exchange code for tokens (status {response.status_code}): {response.text}"
                )
            if response.status_code >= 500:
                raise SupabaseAuthServiceUnavailableError(
                    f"Server error when exchanging code for tokens (status {response.status_code}): {response.text}"
                )
            return response.json()
        except requests.RequestException as e:
            raise SupabaseAuthApiError(f"Failed to exchange code for tokens: {str(e)}")

    def _update_settings_with_token_data(
        self,
        tokens_data: dict[str, Any],
        request_context: RequestContext,
        old_refresh_token: str | None = None,
        max_retries: int = 3,
        retry_delay_seconds: float = 0.2,
    ) -> None:
        """Update user settings with token data from API response.

        This method queries the current settings, updates them with the token data,
        and saves the changes. It includes retry logic to handle concurrency issues.

        Args:
            tokens_data: Dictionary with token data from API response.
            request_context: The request context.
            old_refresh_token: Optional previous refresh token to use as fallback.
            max_retries: Maximum number of retry attempts (default: 3).
            retry_delay_seconds: Delay between retry attempts in seconds (default: 1.0).
        """
        retry_count = 0
        backoff_factor = 1.5  # Exponential backoff multiplier
        current_delay = retry_delay_seconds

        while True:
            try:
                # Get current settings
                settings_response = self.settings_client.get_user_settings(
                    request_context=request_context,
                )
                settings = settings_response.settings

                # Create a timestamp for token expiration
                expiration = None
                if "expires_in" in tokens_data:
                    expiration = Timestamp()
                    expiration_time = datetime.now() + timedelta(
                        seconds=tokens_data["expires_in"]
                    )
                    expiration.FromDatetime(expiration_time)

                # Update Supabase user settings
                settings.supabase_user_settings.access_token = tokens_data.get(
                    "access_token", ""
                )

                # Use the new refresh token if provided, otherwise fall back to the old one
                settings.supabase_user_settings.refresh_token = (
                    tokens_data.get(
                        "refresh_token",
                        "",
                    )
                    or old_refresh_token
                    or ""
                )

                if expiration:
                    settings.supabase_user_settings.access_token_expiration.CopyFrom(
                        expiration
                    )

                # Update the settings
                self.settings_client.update_user_settings(
                    request=settings_pb2.UpdateUserSettingsRequest(
                        settings=settings,
                        expected_version=settings_response.version,
                    ),
                    request_context=request_context,
                )

                # If we get here, the update was successful
                logging.info("Successfully updated Supabase user settings")
                return

            except grpc.RpcError as e:
                # Check if this is a version conflict (ABORTED status code)
                # The type checking for gRPC errors is challenging
                # We need to use string comparison as a fallback
                is_aborted = "ABORTED" in str(e)

                if is_aborted:
                    retry_count += 1
                    if retry_count <= max_retries:
                        logging.warning(
                            f"Concurrency conflict when updating Supabase settings. "
                            f"Retrying ({retry_count}/{max_retries})..."
                        )
                        # Wait with exponential backoff before retrying
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                        continue
                    else:
                        logging.error(
                            f"Failed to update Supabase settings after {max_retries} retries"
                        )
                        raise
                else:
                    # For other types of errors, don't retry
                    logging.error(f"Error updating Supabase settings: {e}")
                    raise

    def hydrate_supabase_settings(
        self,
        request: supabase_pb2.HydrateSupabaseSettingsRequest,
        context: grpc.ServicerContext,
        request_context: RequestContext,
    ) -> supabase_pb2.HydrateSupabaseSettingsResponse:
        """Hydrate Supabase settings with OAuth code and store access token."""
        if not request.code:
            raise SupabaseAuthError("No code provided")

        tokens_data = self._exchange_code_for_tokens(request.code)

        if "access_token" not in tokens_data:
            raise SupabaseAuthApiError("No access token in response")
        if "refresh_token" not in tokens_data:
            raise SupabaseAuthApiError("No refresh token in response")

        # Update the user settings with the new information.
        try:
            self._update_settings_with_token_data(
                tokens_data=tokens_data,
                request_context=request_context,
            )

            return supabase_pb2.HydrateSupabaseSettingsResponse(
                status_code=grpc.StatusCode.OK.value[0]
            )

        except Exception as e:
            logging.error(f"Failed to update user Supabase settings: {e}")
            # Return a response with an error status code instead of throwing
            return supabase_pb2.HydrateSupabaseSettingsResponse(
                status_code=grpc.StatusCode.INTERNAL.value[0]
            )

    def is_token_expired(self, request_context: RequestContext) -> bool:
        """Check if the access token is expired.

        Args:
            request_context: The request context.

        Returns:
            True if the token is expired, False otherwise.
        """
        try:
            # Get current settings to get the token expiration
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )
            settings = settings_response.settings
        except Exception as e:
            # If if we cannot even get settings, raise an error
            logging.error(f"Failed to get user settings: {e}")
            raise SupabaseAuthInternalError(f"Failed to get user settings: {e}")

        if not settings.supabase_user_settings.access_token:
            raise SupabaseNoTokenError("No access token available")

        # If there's no expiration time, consider it not expired
        if not settings.supabase_user_settings.HasField("access_token_expiration"):
            return False

        # Get the expiration time
        expiration = settings.supabase_user_settings.access_token_expiration
        expiration_datetime = datetime.fromtimestamp(
            expiration.seconds + expiration.nanos / 1e9
        )

        # Check if the token is expired (with a 5-minute buffer)
        buffer_time = timedelta(minutes=5)
        return datetime.now() + buffer_time >= expiration_datetime

    def refresh_token(self, request_context: RequestContext) -> dict[str, Any]:
        """Refresh the access token using the refresh token.

        Args:
            request_context: The request context.

        Returns:
            Dictionary with the new tokens and other information.

        Raises:
            SupabaseAuthUnconfiguredError: If OAuth credentials are not configured.
            SupabaseAuthApiError: If there's an issue with the Supabase API.
            SupabaseAuthServiceUnavailableError: If the Supabase service is unavailable.
            SupabaseTokenExpiredError: If the refresh token is missing or invalid.
        """
        try:
            # Get current settings to get the refresh token
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )
            settings = settings_response.settings
            refresh_token = settings.supabase_user_settings.refresh_token

            if not refresh_token:
                raise SupabaseTokenExpiredError("No refresh token available")

            if not self.oauth_client_id or not self.oauth_client_secret:
                raise SupabaseAuthUnconfiguredError(
                    "Supabase OAuth credentials not configured"
                )

            # Prepare form data for x-www-form-urlencoded content type
            data = {
                "grant_type": "refresh_token",
                "refresh_token": refresh_token,
                "client_id": self.oauth_client_id.get_secret_value(),
                "client_secret": self.oauth_client_secret.get_secret_value(),
            }

            # Create basic auth header
            auth_header = f"{self.oauth_client_id.get_secret_value()}:{self.oauth_client_secret.get_secret_value()}"
            encoded_auth = base64.b64encode(auth_header.encode()).decode()

            # Make the request to refresh the token
            response = requests.post(
                self.api_config.token_url,
                data=data,  # Use data instead of json for form urlencoded
                headers={
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Accept": "application/json",
                    "Authorization": f"Basic {encoded_auth}",
                },
                timeout=self.api_config.timeout_seconds,
            )

            # Check for errors
            if 300 <= response.status_code < 500:
                raise SupabaseAuthApiError(
                    f"Failed to refresh token (status {response.status_code}): {response.text}"
                )
            if response.status_code >= 500:
                raise SupabaseAuthServiceUnavailableError(
                    f"Server error when refreshing token (status {response.status_code}): {response.text}"
                )

            tokens_data = response.json()

            # Log whether refresh token was included in the response
            if "refresh_token" in tokens_data:
                logging.info("Refresh token provided in refresh_token response")
            else:
                logging.info("No refresh token in refresh_token response")

            # Update the user settings with the new tokens
            self._update_settings_with_token_data(
                tokens_data=tokens_data,
                request_context=request_context,
                # Pass the old refresh token as fallback
                # This hasn't lived long enough for me to know if a new refresh token is always provided
                # but it's better to be safe than sorry
                old_refresh_token=refresh_token,
            )

            return tokens_data
        except SupabaseAuthError:
            # Re-raise Supabase auth errors
            raise
        except Exception as e:
            logging.error(f"Failed to refresh token: {e}")
            raise SupabaseTokenExpiredError(f"Failed to refresh token: {e}")

    def get_access_token(self, request_context: RequestContext) -> str:
        """Get the current access token, refreshing it if necessary.

        This method handles all the token management logic, including checking if
        the token is expired and refreshing it if needed.

        Args:
            request_context: The request context.

        Returns:
            The current valid access token.

        Raises:
            SupabaseAuthUnconfiguredError: If OAuth credentials are not configured.
            SupabaseAuthApiError: If there's an issue with the Supabase API.
            SupabaseAuthServiceUnavailableError: If the Supabase service is unavailable.
            SupabaseTokenExpiredError: If the token is expired and cannot be refreshed.
            SupabaseNoTokenError: If no tokens are available.
            SupabaseAuthInternalError: If there's an internal error accessing settings.
        """
        try:
            # First check if the token is expired
            if self.is_token_expired(request_context):
                logging.info("Access token expired, attempting to refresh")
                try:
                    return self.refresh_token(request_context)["access_token"]
                except Exception as e:
                    raise SupabaseTokenExpiredError(f"Failed to refresh token: {e}")
            else:
                # Get current settings to get the access token
                settings_response = self.settings_client.get_user_settings(
                    request_context=request_context,
                )
                settings = settings_response.settings
                return settings.supabase_user_settings.access_token
        except SupabaseAuthError:
            # Re-raise Supabase auth errors
            raise
        except Exception as e:
            logging.error(f"Failed to get access token: {e}")
            raise SupabaseAuthInternalError(f"Failed to get access token: {e}")

    def revoke_token(
        self,
        request_context: RequestContext,
    ) -> None:
        """Revoke Supabase OAuth token and remove it from settings.

        According to Supabase API documentation, this endpoint requires:
        - client_id
        - client_secret
        - refresh_token

        And returns a 204 status code on success.
        """
        try:
            # Get current settings to get the token and version
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )
            settings = settings_response.settings
            refresh_token = settings.supabase_user_settings.refresh_token
            if not refresh_token:
                raise SupabaseRevokeNonexistentError("No refresh token to revoke")

            if not self.oauth_client_id or not self.oauth_client_secret:
                raise SupabaseAuthUnconfiguredError(
                    "Supabase OAuth credentials not configured"
                )

            # Prepare the request body
            data = {
                "client_id": self.oauth_client_id.get_secret_value(),
                "client_secret": self.oauth_client_secret.get_secret_value(),
                "refresh_token": refresh_token,
            }

            # Create basic auth header
            auth_header = f"{self.oauth_client_id.get_secret_value()}:{self.oauth_client_secret.get_secret_value()}"
            encoded_auth = base64.b64encode(auth_header.encode()).decode()

            # Clear the token from settings BEFORE making the API call
            # This ensures settings are cleared even if the API call fails
            self._update_settings_with_token_data(
                {}, request_context=request_context, retry_delay_seconds=0.5
            )

            # Call Supabase's revoke endpoint
            response = requests.post(
                self.api_config.revoke_url,
                json=data,  # Use json instead of data for application/json
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                    "Authorization": f"Basic {encoded_auth}",
                },
                timeout=self.api_config.timeout_seconds,
            )

            # Supabase returns 204 on successful revocation
            if response.status_code != 204:
                raise SupabaseAuthApiError(f"Failed to revoke token: {response.text}")

        except Exception as e:
            logging.error(f"Failed to revoke Supabase token: {e}")
            raise

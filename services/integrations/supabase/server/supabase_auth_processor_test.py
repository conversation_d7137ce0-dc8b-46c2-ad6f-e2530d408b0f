"""Tests for the Supabase auth processor."""

import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
import grpc
from pydantic import SecretStr
from datetime import datetime, timedelta
from google.protobuf.timestamp_pb2 import Timestamp

from services.integrations.supabase.server.supabase_auth_processor import (
    SupabaseAuthProcessor,
    SupabaseAuthUnconfiguredError,
    SupabaseAuthApiError,
    SupabaseRevokeNonexistentError,
    SupabaseTokenExpiredError,
    SupabaseNoTokenError,
    SupabaseAuthInternalError,
)
from services.integrations.supabase.server.config import (
    Config,
    SupabaseApiConfig,
    AuthConfig,
)
from services.lib.request_context.request_context import RequestContext
import services.integrations.supabase.supabase_pb2 as supabase_pb2
import services.settings.settings_pb2 as settings_pb2


@pytest.fixture
def mock_settings_client():
    """Create a mock settings client."""
    client = MagicMock()
    # Create a proper settings response
    settings = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings()
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    client.get_user_settings.return_value = settings_response
    return client


@pytest.fixture
def request_context():
    """Create a proper RequestContext instance for testing."""
    return RequestContext.create(request_source="test")


@pytest.fixture
def config():
    """Create a test configuration."""
    callback_url = "https://example.com/callback"
    return Config(
        port=50051,
        feature_flags_sdk_key_path=None,
        dynamic_feature_flags_endpoint=None,
        auth_config=AuthConfig(token_exchange_endpoint="test-endpoint"),
        settings_endpoint="test-settings-endpoint",
        supabase_app_callback_url=callback_url,
        oauth_app_secret_path="/tmp/test_oauth_secret.json",
        supabase_api_config=SupabaseApiConfig(
            base_url="https://test.supabase.com",
            auth_path="/v1/oauth/authorize",
            token_path="/v1/oauth/token",
            revoke_path="/v1/oauth/revoke",
            timeout_seconds=10,
        ),
    )


@pytest.fixture
def auth_processor(mock_settings_client, config):
    """Create a SupabaseAuthProcessor instance for testing."""
    return SupabaseAuthProcessor(
        settings_client=mock_settings_client,
        callback_url=config.supabase_app_callback_url,
        oauth_secret_path="/tmp/test_oauth_secret.json",  # Dummy path for testing
        config=config,
        oauth_client_id=SecretStr("test_client_id"),
        oauth_client_secret=SecretStr("test_client_secret"),
    )


def test_get_oauth_url(auth_processor, config):
    """Test getting the OAuth URL."""
    # Test getting the OAuth URL
    oauth_url = auth_processor.get_oauth_url()

    # Verify the URL contains the expected components
    assert "test_client_id" in oauth_url
    assert "redirect_uri" in oauth_url
    assert config.supabase_app_callback_url in oauth_url
    assert config.supabase_api_config.auth_url in oauth_url


def test_get_oauth_url_unconfigured(auth_processor):
    """Test getting the OAuth URL when client ID is not configured."""
    auth_processor.oauth_client_id = None
    with pytest.raises(SupabaseAuthUnconfiguredError):
        auth_processor.get_oauth_url()


def test_hydrate_supabase_settings_success(
    auth_processor, mock_settings_client, request_context
):
    """Test successful hydration of Supabase settings with OAuth code."""
    # Mock the exchange code for tokens method
    with patch.object(
        SupabaseAuthProcessor, "_exchange_code_for_tokens"
    ) as mock_exchange:
        # Set up the mock to return tokens
        mock_exchange.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
        }

        # Create the request
        request = supabase_pb2.HydrateSupabaseSettingsRequest(code="test_code")
        context = MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = auth_processor.hydrate_supabase_settings(
            request, context, request_context
        )

        # Verify the response
        assert response.status_code == grpc.StatusCode.OK.value[0]

        # Verify the settings were updated correctly
        mock_settings_client.update_user_settings.assert_called_once()
        call_kwargs = mock_settings_client.update_user_settings.call_args[1]
        assert call_kwargs["request_context"] == request_context
        update_request = call_kwargs["request"]
        assert update_request.expected_version == "test-version-1"
        assert (
            update_request.settings.supabase_user_settings.access_token
            == "test_access_token"
        )
        assert (
            update_request.settings.supabase_user_settings.refresh_token
            == "test_refresh_token"
        )
        assert update_request.settings.supabase_user_settings.HasField(
            "access_token_expiration"
        )


def test_hydrate_supabase_settings_error_handling(
    auth_processor, mock_settings_client, request_context
):
    """Test error handling in hydrate_supabase_settings."""
    # Test case 1: gRPC error when getting user settings
    with patch.object(
        SupabaseAuthProcessor, "_exchange_code_for_tokens"
    ) as mock_exchange:
        mock_exchange.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
        }

        # Mock the settings client to raise a gRPC error
        mock_settings_client.get_user_settings.side_effect = grpc.RpcError()

        # Create the request
        request = supabase_pb2.HydrateSupabaseSettingsRequest(code="test_code")
        context = MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = auth_processor.hydrate_supabase_settings(
            request, context, request_context
        )

        # Verify the response has an error status code
        assert response.status_code == grpc.StatusCode.INTERNAL.value[0]

    # Test case 2: Generic error when updating user settings
    with patch.object(
        SupabaseAuthProcessor, "_exchange_code_for_tokens"
    ) as mock_exchange:
        mock_exchange.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
        }

        # Reset the mock to not raise an error for get_user_settings
        mock_settings_client.get_user_settings.side_effect = None

        # Mock the update to raise a generic error
        mock_settings_client.update_user_settings.side_effect = Exception(
            "Generic error"
        )

        # Create the request
        request = supabase_pb2.HydrateSupabaseSettingsRequest(code="test_code")
        context = MagicMock(spec=grpc.ServicerContext)

        # Call the method
        response = auth_processor.hydrate_supabase_settings(
            request, context, request_context
        )

        # Verify the response has an error status code
        assert response.status_code == grpc.StatusCode.INTERNAL.value[0]


def test_token_expiration_checks(auth_processor, mock_settings_client, request_context):
    """Test token expiration checking functionality."""
    # Setup: Create settings with different token states

    # Case 1: No token
    settings_no_token = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            access_token="",  # Empty token
        )
    )
    settings_response_no_token = settings_pb2.GetUserSettingsResponse(
        settings=settings_no_token, version="test-version-1"
    )

    # Case 2: Token with no expiration
    settings_no_expiration = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            access_token="test_access_token",
            # No expiration field set
        )
    )
    settings_response_no_expiration = settings_pb2.GetUserSettingsResponse(
        settings=settings_no_expiration, version="test-version-1"
    )

    # Case 3: Expired token
    settings_expired = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            access_token="test_access_token",
        )
    )
    expiration_past = Timestamp()
    past_time = datetime.now() - timedelta(hours=1)  # 1 hour in the past
    expiration_past.FromDatetime(past_time)
    settings_expired.supabase_user_settings.access_token_expiration.CopyFrom(
        expiration_past
    )
    settings_response_expired = settings_pb2.GetUserSettingsResponse(
        settings=settings_expired, version="test-version-1"
    )

    # Case 4: Valid token
    settings_valid = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            access_token="test_access_token",
        )
    )
    expiration_future = Timestamp()
    future_time = datetime.now() + timedelta(hours=1)  # 1 hour in the future
    expiration_future.FromDatetime(future_time)
    settings_valid.supabase_user_settings.access_token_expiration.CopyFrom(
        expiration_future
    )
    settings_response_valid = settings_pb2.GetUserSettingsResponse(
        settings=settings_valid, version="test-version-1"
    )

    # Test each case
    # Case 0: Error getting user settings should raise SupabaseAuthInternalError
    mock_settings_client.get_user_settings.side_effect = Exception(
        "Failed to get settings"
    )
    with pytest.raises(SupabaseAuthInternalError):
        auth_processor.is_token_expired(request_context)

    # Reset the side effect
    mock_settings_client.get_user_settings.side_effect = None

    # Case 1: No token should raise SupabaseNoTokenError
    mock_settings_client.get_user_settings.return_value = settings_response_no_token
    with pytest.raises(SupabaseNoTokenError):
        auth_processor.is_token_expired(request_context)

    # Case 2: Token with no expiration should not be considered expired
    mock_settings_client.get_user_settings.return_value = (
        settings_response_no_expiration
    )
    assert auth_processor.is_token_expired(request_context) is False

    # Case 3: Expired token should be considered expired
    mock_settings_client.get_user_settings.return_value = settings_response_expired
    assert auth_processor.is_token_expired(request_context) is True

    # Case 4: Valid token should not be considered expired
    mock_settings_client.get_user_settings.return_value = settings_response_valid
    assert auth_processor.is_token_expired(request_context) is False


def test_refresh_token_success(auth_processor, mock_settings_client, request_context):
    """Test successful token refresh."""
    # Setup: Create settings with a refresh token
    settings = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            refresh_token="test_refresh_token",
        )
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response

    # Mock the HTTP response for token refresh
    with patch("requests.post") as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600,
        }
        mock_post.return_value = mock_response

        # Call the method
        tokens_data = auth_processor.refresh_token(request_context)

        # Verify the request was made correctly
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        assert kwargs["data"]["grant_type"] == "refresh_token"
        assert kwargs["data"]["refresh_token"] == "test_refresh_token"
        assert kwargs["data"]["client_id"] == "test_client_id"
        assert kwargs["data"]["client_secret"] == (
            "test_client_secret"  # pragma: allowlist secret
        )
        assert kwargs["headers"]["Content-Type"] == "application/x-www-form-urlencoded"
        assert kwargs["headers"]["Accept"] == "application/json"
        assert kwargs["headers"]["Authorization"].startswith("Basic ")

        # Verify the response
        assert tokens_data["access_token"] == "new_access_token"
        assert tokens_data["refresh_token"] == "new_refresh_token"

        # Verify the settings were updated correctly
        mock_settings_client.update_user_settings.assert_called_once()
        call_kwargs = mock_settings_client.update_user_settings.call_args[1]
        update_request = call_kwargs["request"]
        assert update_request.expected_version == "test-version-1"
        assert (
            update_request.settings.supabase_user_settings.access_token
            == "new_access_token"
        )
        assert (
            update_request.settings.supabase_user_settings.refresh_token
            == "new_refresh_token"
        )
        assert update_request.settings.supabase_user_settings.HasField(
            "access_token_expiration"
        )


def test_refresh_token_error_handling(
    auth_processor, mock_settings_client, request_context
):
    """Test error handling in refresh_token."""
    # Test case 1: No refresh token
    settings_no_token = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            refresh_token="",  # Empty refresh token
        )
    )
    settings_response_no_token = settings_pb2.GetUserSettingsResponse(
        settings=settings_no_token, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response_no_token

    with pytest.raises(SupabaseTokenExpiredError):
        auth_processor.refresh_token(request_context)

    # Test case 2: API error
    settings = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            refresh_token="test_refresh_token",
        )
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response

    with patch("requests.post") as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_post.return_value = mock_response

        with pytest.raises(SupabaseAuthApiError):
            auth_processor.refresh_token(request_context)


def test_revoke_token_success(auth_processor, mock_settings_client, request_context):
    """Test successful token revocation."""
    # Setup: Create settings with a refresh token
    settings = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            access_token="test_access_token",
            refresh_token="test_refresh_token",
        )
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response

    # Mock the HTTP response for token revocation
    with patch("requests.post") as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 204  # Supabase returns 204 on successful revocation
        mock_post.return_value = mock_response

        # Call the method
        auth_processor.revoke_token(request_context)

        # Verify the request was made correctly
        mock_post.assert_called_once()
        _, kwargs = mock_post.call_args
        assert kwargs["json"]["client_id"] == "test_client_id"
        assert kwargs["json"]["client_secret"] == (
            "test_client_secret"  # pragma: allowlist secret
        )
        assert kwargs["json"]["refresh_token"] == "test_refresh_token"
        assert kwargs["headers"]["Content-Type"] == "application/json"
        assert kwargs["headers"]["Accept"] == "application/json"
        assert kwargs["headers"]["Authorization"].startswith("Basic ")

        # Verify the settings were cleared
        mock_settings_client.update_user_settings.assert_called_once()
        call_kwargs = mock_settings_client.update_user_settings.call_args[1]
        update_request = call_kwargs["request"]
        assert update_request.expected_version == "test-version-1"
        assert update_request.settings.supabase_user_settings.access_token == ""
        assert update_request.settings.supabase_user_settings.refresh_token == ""


def test_revoke_token_error_handling(
    auth_processor, mock_settings_client, request_context
):
    """Test error handling in revoke_token."""
    # Test case 1: No refresh token
    settings_no_token = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            refresh_token="",  # Empty refresh token
        )
    )
    settings_response_no_token = settings_pb2.GetUserSettingsResponse(
        settings=settings_no_token, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response_no_token

    with pytest.raises(SupabaseRevokeNonexistentError):
        auth_processor.revoke_token(request_context)

    # Test case 2: Unconfigured OAuth credentials
    settings = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            refresh_token="test_refresh_token",
        )
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response

    # Set OAuth credentials to None
    auth_processor.oauth_client_id = None
    auth_processor.oauth_client_secret = None

    with pytest.raises(SupabaseAuthUnconfiguredError):
        auth_processor.revoke_token(request_context)

    # Test case 3: API error
    # Restore OAuth credentials
    auth_processor.oauth_client_id = SecretStr("test_client_id")
    auth_processor.oauth_client_secret = SecretStr("test_client_secret")

    with patch("requests.post") as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_post.return_value = mock_response

        with pytest.raises(SupabaseAuthApiError):
            auth_processor.revoke_token(request_context)


def test_revoke_token_clears_settings_before_error(
    auth_processor, mock_settings_client, request_context
):
    """Test that settings are cleared even when token revocation API call fails."""
    # Setup: Create settings with a refresh token
    settings = settings_pb2.UserSettings(
        supabase_user_settings=settings_pb2.SupabaseUserSettings(
            refresh_token="test_refresh_token",
            access_token="test_access_token",
        )
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    mock_settings_client.get_user_settings.return_value = settings_response

    # Mock the HTTP response for token revocation to fail
    with patch("requests.post") as mock_post:
        mock_response = MagicMock()
        mock_response.status_code = 400  # Error status
        mock_response.text = "Bad Request"
        mock_post.return_value = mock_response

        # Call the method and expect an exception
        with pytest.raises(SupabaseAuthApiError):
            auth_processor.revoke_token(request_context)

        # Verify settings were cleared before the exception was raised
        mock_settings_client.update_user_settings.assert_called_once()
        call_kwargs = mock_settings_client.update_user_settings.call_args[1]
        update_request = call_kwargs["request"]
        assert update_request.expected_version == "test-version-1"
        assert update_request.settings.supabase_user_settings.access_token == ""
        assert update_request.settings.supabase_user_settings.refresh_token == ""

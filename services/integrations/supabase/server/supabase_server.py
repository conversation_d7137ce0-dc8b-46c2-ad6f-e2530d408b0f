"""gRPC server for Supabase integration."""

import logging

import grpc
from google.rpc import status_pb2, code_pb2

import services.lib.grpc.tls_config.tls_config as tls_config
from services.lib.request_context.request_context import RequestContext
from services.lib.grpc.auth.service_auth_interceptor import (
    get_auth_info_from_grpc_context,
)
from services.integrations.supabase.server.supabase_auth_processor import (
    SupabaseAuthProcessor,
    SupabaseAuthError,
    SupabaseAuthUnconfiguredError,
    SupabaseAuthApiError,
    SupabaseAuthServiceUnavailableError,
    SupabaseRevokeNonexistentError,
)
from services.integrations.supabase.server.supabase_handler import SupabaseHandler
from services.integrations.supabase.server.config import Config
from services.settings.client.client import SettingsClient
import services.integrations.supabase.supabase_pb2 as supabase_pb2
import services.integrations.supabase.supabase_pb2_grpc as supabase_pb2_grpc
import services.settings.settings_pb2 as settings_pb2
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

log = logging.getLogger(__name__)


class SupabaseServer(supabase_pb2_grpc.SupabaseServicer):
    """gRPC server for Supabase integration."""

    def __init__(
        self,
        config: Config,
        request_insight_publisher: RequestInsightPublisher,
    ):
        """Initialize the Supabase server.

        Args:
            config: Configuration for the Supabase server.
            request_insight_publisher: Publisher for request insights.
        """
        self.config = config

        # Initialize settings client
        self.settings_client = SettingsClient(
            config.settings_endpoint,
            tls_config.get_client_tls_creds(config.client_mtls),
        )

        # Initialize Supabase auth processor
        self.supabase_auth_processor = SupabaseAuthProcessor(
            settings_client=self.settings_client,
            callback_url=config.supabase_app_callback_url,
            oauth_secret_path=config.oauth_app_secret_path,
            config=config,
        )

        # Initialize Supabase handler with auth processor for token refresh
        self.supabase_handler = SupabaseHandler(
            settings_client=self.settings_client,
            auth_processor=self.supabase_auth_processor,
            config=self.config,
        )

    def IsConfigured(
        self, request: supabase_pb2.IsConfiguredRequest, context
    ) -> supabase_pb2.IsConfiguredResponse:
        """Check if Supabase is configured.

        Args:
            request: The request.
            context: The gRPC context.

        Returns:
            Response indicating if Supabase is configured.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            is_configured = self.supabase_handler.is_configured(request_context)
            return supabase_pb2.IsConfiguredResponse(is_configured=is_configured)

    def GetSupabaseOAuthUrl(
        self, request: supabase_pb2.GetSupabaseOAuthUrlRequest, context
    ) -> supabase_pb2.GetSupabaseOAuthUrlResponse:
        """Get the Supabase OAuth URL.

        Args:
            request: The request containing provider and optional redirect URI.
            context: The gRPC context.

        Returns:
            Response containing the OAuth URL.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                oauth_url = self.supabase_auth_processor.get_oauth_url()
                return supabase_pb2.GetSupabaseOAuthUrlResponse(
                    oauth_url=oauth_url, status_code=grpc.StatusCode.OK.value[0]
                )
            except SupabaseAuthUnconfiguredError as e:
                log.error(f"Supabase auth not configured: {e}")
                return supabase_pb2.GetSupabaseOAuthUrlResponse(
                    status_code=grpc.StatusCode.UNAUTHENTICATED.value[0]
                )
            except Exception as e:
                log.error(f"Error getting Supabase OAuth URL: {e}")
                return supabase_pb2.GetSupabaseOAuthUrlResponse(
                    status_code=grpc.StatusCode.INTERNAL.value[0]
                )

    def _fetch_and_update_projects(
        self, request_context: RequestContext
    ) -> tuple[list[supabase_pb2.SupabaseProject], bool]:
        """Sync a user's Supabase projects' metadata into user settings.

        Args:
            request_context: The request context.

        Returns:
            A tuple containing (list of SupabaseProject objects, success flag).
        """
        try:
            # Fetch projects
            projects = self.supabase_handler.get_projects(request_context)

            # Get current settings to update with projects
            settings_response = (
                self.supabase_auth_processor.settings_client.get_user_settings(
                    request_context=request_context,
                )
            )
            settings = settings_response.settings

            # Clear existing projects and add new ones
            del settings.supabase_user_settings.projects[:]

            # Create project objects for both settings and response
            project_objects = []
            for project in projects:
                # Create project object
                project_obj = supabase_pb2.SupabaseProject(
                    ref=project.get("id", ""),
                    name=project.get("name", ""),
                    url=project.get("database", {}).get("host", ""),
                    region=project.get("region", ""),
                )

                # Add to list of project objects
                project_objects.append(project_obj)

                # Add to settings
                settings.supabase_user_settings.projects.append(project_obj)

            # Update the user settings with the projects
            log.info(f"Updating Supabase settings with {len(projects)} projects")
            self.supabase_auth_processor.settings_client.update_user_settings(
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings,
                    expected_version=settings_response.version,
                ),
                request_context=request_context,
            )
            return project_objects, True
        except Exception as e:
            log.error(f"Error updating projects: {e}")
            log.error(f"Exception details: {type(e).__name__}: {str(e)}")
            return [], False

    def HydrateSupabaseSettings(
        self, request: supabase_pb2.HydrateSupabaseSettingsRequest, context
    ) -> supabase_pb2.HydrateSupabaseSettingsResponse:
        """Hydrate Supabase settings with OAuth code.

        Args:
            request: The request containing the OAuth code.
            context: The gRPC context.

        Returns:
            Response after hydrating settings.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                response = self.supabase_auth_processor.hydrate_supabase_settings(
                    request, context, request_context
                )

                # Fetch projects after successful hydration
                if response.status_code == grpc.StatusCode.OK.value[0]:
                    project_objects, _ = self._fetch_and_update_projects(
                        request_context
                    )
                    # Add projects to the response
                    for project_obj in project_objects:
                        response.projects.append(project_obj)

                return response
            except SupabaseAuthUnconfiguredError as e:
                log.error(f"Supabase auth not configured: {e}")
                return supabase_pb2.HydrateSupabaseSettingsResponse(
                    status_code=grpc.StatusCode.UNAUTHENTICATED.value[0]
                )
            except SupabaseAuthApiError as e:
                log.error(f"Supabase API error: {e}")
                return supabase_pb2.HydrateSupabaseSettingsResponse(
                    status_code=grpc.StatusCode.INVALID_ARGUMENT.value[0]
                )
            except SupabaseAuthServiceUnavailableError as e:
                log.error(f"Supabase service unavailable: {e}")
                return supabase_pb2.HydrateSupabaseSettingsResponse(
                    status_code=grpc.StatusCode.UNAVAILABLE.value[0]
                )
            except SupabaseAuthError as e:
                log.error(f"Other Supabase auth error: {e}")
                return supabase_pb2.HydrateSupabaseSettingsResponse(
                    status_code=grpc.StatusCode.INVALID_ARGUMENT.value[0]
                )
            except Exception as e:
                log.error(f"Internal error hydrating Supabase settings: {e}")
                return supabase_pb2.HydrateSupabaseSettingsResponse(
                    status_code=grpc.StatusCode.INTERNAL.value[0]
                )

    def RevokeOAuthGrant(
        self, request: supabase_pb2.RevokeOAuthGrantRequest, context
    ) -> supabase_pb2.RevokeOAuthGrantResponse:
        """Revoke the OAuth grant.

        Args:
            request: The request.
            context: The gRPC context.

        Returns:
            Response after revoking the OAuth grant.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Revoking Supabase OAuth grant")
            try:
                self.supabase_auth_processor.revoke_token(request_context)
                status = status_pb2.Status(code=code_pb2.OK, message="Success")  # pylint: disable=no-member # type: ignore
                return supabase_pb2.RevokeOAuthGrantResponse(status=status)
            except SupabaseRevokeNonexistentError:
                status = status_pb2.Status(  # type: ignore
                    code=code_pb2.NOT_FOUND,  # pylint: disable=no-member # type: ignore
                    message="No token to revoke",
                )
                return supabase_pb2.RevokeOAuthGrantResponse(status=status)
            except SupabaseAuthApiError as e:
                status = status_pb2.Status(  # type: ignore
                    code=code_pb2.INVALID_ARGUMENT,  # pylint: disable=no-member  # type: ignore
                    message=str(e),
                )
                return supabase_pb2.RevokeOAuthGrantResponse(status=status)
            except Exception as e:
                log.error(f"Error revoking Supabase OAuth grant: {e}")
                status = status_pb2.Status(  # type: ignore
                    code=code_pb2.INTERNAL,  # pylint: disable=no-member   # type: ignore
                    message=f"Internal error: {str(e)}",
                )
                return supabase_pb2.RevokeOAuthGrantResponse(status=status)

    def TestConnection(
        self, request: supabase_pb2.TestConnectionRequest, context
    ) -> supabase_pb2.TestConnectionResponse:
        """Test the Supabase connection by listing projects and updating settings.

        Args:
            request: The request.
            context: The gRPC context.

        Returns:
            Response containing status code and optional error message.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                # Check if Supabase is configured
                if not self.supabase_handler.is_configured(request_context):
                    log.error("Supabase is not configured")
                    return supabase_pb2.TestConnectionResponse(
                        status_code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                        error_message="Supabase is not configured",
                    )

                # Fetch projects and update settings
                # We still update the settings with the projects, but don't return them in the response
                _, success = self._fetch_and_update_projects(request_context)

                if not success:
                    return supabase_pb2.TestConnectionResponse(
                        status_code=grpc.StatusCode.INTERNAL.value[0],
                        error_message="Failed to fetch projects or update settings",
                    )

                # Return success status
                return supabase_pb2.TestConnectionResponse(
                    status_code=grpc.StatusCode.OK.value[0]
                )

            except Exception as e:
                log.error(f"Error testing Supabase connection: {e}")
                return supabase_pb2.TestConnectionResponse(
                    status_code=grpc.StatusCode.INTERNAL.value[0],
                    error_message=f"Internal error: {str(e)}",
                )

    def CallSupabaseApi(
        self, request: supabase_pb2.SupabaseApiRequest, context
    ) -> supabase_pb2.SupabaseApiResponse:
        """Call the Supabase API with the given request.

        Args:
            request: The request containing the API path, method, and optional data.
            context: The gRPC context.

        Returns:
            Response from the Supabase API.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                # Check for UNKNOWN method
                if request.method == supabase_pb2.HTTPMethod.UNKNOWN:
                    return supabase_pb2.SupabaseApiResponse(
                        response="Error: Unknown HTTP method",
                        status_code=400,  # Bad Request
                    )

                # Convert the data from Struct to dict if provided
                data = None
                if request.HasField("data"):
                    data = {}
                    for key, value in request.data.items():
                        data[key] = value

                # Authentication is handled by the SupabaseHandler using the user's settings

                # Make the API call
                status_code, response_text = self.supabase_handler.make_api_call(
                    path=request.path,
                    method=request.method,
                    request_context=request_context,
                    data=data,
                )

                # Return the actual status code and response text from the Supabase API
                result = supabase_pb2.SupabaseApiResponse(
                    response=response_text,
                    status_code=status_code,
                )

                if request.path in ["/v1/projects", "/v1/projects/"]:
                    # Agent calling projects means it's either querying or altering
                    # project structure, which indicates we need to refresh the projects
                    # in the user settings.
                    try:
                        self._fetch_and_update_projects(request_context)
                    except Exception as e:
                        log.error(f"Failed to update projects: {e}")
                        # don't fail if we cannot update the projects

                return result

            except ValueError as e:
                # Handle specific known errors
                log.error(f"Invalid request to Supabase API: {e}")
                return supabase_pb2.SupabaseApiResponse(
                    response=f"Error: {str(e)}",
                    status_code=400,  # Bad Request
                )
            except Exception as e:
                # For other exceptions, try to extract as much information as possible
                log.error(f"Error calling Supabase API: {e}")

                # Check if the exception has a response attribute (like requests.exceptions.HTTPError)
                if (
                    hasattr(e, "response")
                    and hasattr(e.response, "status_code")  # type: ignore
                    and hasattr(e.response, "text")  # type: ignore
                ):
                    return supabase_pb2.SupabaseApiResponse(
                        response=e.response.text,  # type: ignore
                        status_code=e.response.status_code,  # type: ignore
                    )

                # Fall back to a generic error response
                return supabase_pb2.SupabaseApiResponse(
                    response=f"Error: {str(e)}",
                    status_code=500,  # Internal Server Error
                )

    def GetProjects(
        self, request: supabase_pb2.GetProjectsRequest, context
    ) -> supabase_pb2.GetProjectsResponse:
        """Get the list of Supabase projects for the user from settings.

        Args:
            request: The request containing no parameters.
            context: The gRPC context.

        Returns:
            Response containing the list of Supabase projects.
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                # Get the user's settings directly from the settings client
                settings_response = self.settings_client.get_user_settings(
                    request_context
                )
                settings = settings_response.settings

                # Check if Supabase settings exist
                if (
                    not hasattr(settings, "supabase_user_settings")
                    or not settings.supabase_user_settings
                ):
                    return supabase_pb2.GetProjectsResponse()

                # Check if projects exist in settings
                if (
                    not hasattr(settings.supabase_user_settings, "projects")
                    or not settings.supabase_user_settings.projects
                ):
                    return supabase_pb2.GetProjectsResponse()

                # Return the projects from settings
                return supabase_pb2.GetProjectsResponse(
                    projects=settings.supabase_user_settings.projects
                )
            except Exception as e:
                log.error(f"Error getting Supabase projects: {e}")
                context.set_details(f"Error getting Supabase projects: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                return supabase_pb2.GetProjectsResponse()

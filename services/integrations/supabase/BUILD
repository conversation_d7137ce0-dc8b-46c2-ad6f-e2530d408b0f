load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_library", "py_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_proto_library")

proto_library(
    name = "supabase_proto",
    srcs = ["supabase.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:struct_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "supabase_py_proto",
    protos = [":supabase_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//third_party/proto:googleapis_status_py_proto",
        "@protobuf//:protobuf_python",
    ],
)

ts_proto_library(
    name = "supabase_ts_proto",
    node_modules = "//:node_modules",
    proto = ":supabase_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

go_proto_library(
    name = "supabase_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "github.com/augmentcode/augment/services/integrations/supabase/proto",
    proto = ":supabase_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_protobuf//types/known/structpb:go_default_library",
    ],
)

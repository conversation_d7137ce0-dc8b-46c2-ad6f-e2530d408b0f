load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "supabase_tool",
    srcs = ["supabase_tool.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/agents:agents_py_proto",
        "//services/agents:tool",
        "//services/integrations/supabase:supabase_py_proto",
        "//services/integrations/supabase/client:client_py",
        "//services/lib/request_context:request_context_py",
    ],
)

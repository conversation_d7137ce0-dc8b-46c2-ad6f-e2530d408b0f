"""Supabase agent tool implementation."""

import json
import logging
from typing import Any, Literal

import grpc
from google.rpc import status_pb2
from pydantic import BaseModel, Field

import base.feature_flags
from base.third_party_clients.third_party_model_client import ToolDefinition
from services.agents import agents_pb2
from services.integrations.supabase import supabase_pb2
from services.agents.tool import (
    ValidatedTool,
    ToolAuthenticationError,
    ToolNotAvailableError,
    EmptyExtraToolInput,
)
from services.integrations.supabase.client.client import SupabaseClient
from services.lib.request_context.request_context import RequestContext

log = logging.getLogger(__name__)

# Feature flag to control access to the Supabase service
_ENABLE_SUPABASE_SERVICE = base.feature_flags.BoolFlag("enable_supabase_service", False)


class SupabaseToolInput(BaseModel):
    """Input schema for the Supabase tool."""

    summary: str = Field(
        ...,  # required
        description="A summary describing the intention of this API call in natural language.  Be specific and include as much detail as permitted in a single sentence.",
    )

    method: Literal["GET", "POST", "PATCH", "PUT", "DELETE"] = Field(
        default="GET",
        description="HTTP method to use for the Supabase API call.",
    )

    path: str = Field(
        ...,  # required
        description="API path to call.",
    )

    data: dict[str, Any] | None = Field(
        default=None,
        description="""Data to send with the request. \
Will be send through parameters for GET requests and as a JSON body for other methods.""",
    )


class SupabaseTool(ValidatedTool[SupabaseToolInput, EmptyExtraToolInput]):
    """Tool for interacting with Supabase API."""

    id = agents_pb2.RemoteToolId.SUPABASE
    name = "supabase"
    description = """\
Make Supabase Management API calls directly.
You should use this tool to interact with Supabase, whereas the applications \
you make should use the REST API (found in project settings) to access it.

When creating a secure multi-user application with Supabase, implement \
Row Level Security (RLS) policies to ensure users can only access their \
authorized data, never exposing the service key in client-side code. \
Configure proper authentication with JWT validation. \
Test your security implementation to verify that users only access their own data.

Use this tool to interact with Supabase by specifying:
- A summary of what you're trying to accomplish (required)
- HTTP method (GET, POST, PATCH, PUT, DELETE)
- API path
- Request data

Note: Only GET requests are considered safe and don't require user approval.

Examples (For more details see https://supabase.com/docs/reference/api/start):
## List all projects
GET /v1/projects

## Create a new project.
POST /v1/projects
{
    "name": "my-project",
    "db_pass": ...,
    "region": "us-west-2",
    "organization_id": "my-org-id",
}

# Run Postgres queries
POST /v1/projects/{project_id}/database/query
{
    "query": "SELECT * FROM my_table"
}

# Update Postgres config
PUT /v1/projects/{ref}/config/database/postgres
{
    "max_connections": 100,
    "work_mem": "4GB",
    ...
}

# Update project auth configuration
PATCH /v1/projects/{ref}/config/auth
{
    "disable_signup": true,
    "external_github_enabled": true,
    "external_github_client_id": "...",
    "external_github_secret": "..."
}

# List all storage buckets
GET /v1/projects/{ref}/storage/buckets

# Restore a point-in-time backup
POST /v1/projects/{ref}/database/backups/restore-pitr
{
    "recovery_time_target_unix": 1634567890
}

# List network bans
POST /v1/projects/{ref}/network-bans/retrieve

# Update network restrictions
POST /v1/projects/{ref}/network-restrictions/apply
{
    "dbAllowedCidrs": ["...", "..."]
}
"""

    input_model = SupabaseToolInput
    tool_safety = agents_pb2.ToolSafety.TOOL_CHECK

    def __init__(self, supabase_client: SupabaseClient):
        """Initialize the Supabase tool.

        Args:
            supabase_client: Client for calling into the Supabase service.
        """
        super().__init__()
        self.supabase_client = supabase_client

    def get_tool_definition(
        self, request_context: RequestContext | None = None
    ) -> ToolDefinition:
        """Get the tool parameter for LLM with project info injected.

        Args:
            request_context: The request context.

        Returns:
            ToolDefinition: Tool definition with project info in the description
        """
        description = self.description

        # Try to get Supabase project info if we have a request context
        if request_context:
            try:
                # Get project information from settings using the get_projects method
                response = self.supabase_client.get_projects(request_context)

                if response.projects:
                    # Add project information to the description
                    description += "\n\nYour Supabase Projects:\n"
                    for project in response.projects:
                        description += f"\n- Project: {project.name} (ID: {project.ref}, Region: {project.region})"
                    description += "\n\nThis may change; use API to get more up-to-date information if necessary."

            except Exception as e:
                log.warning(f"Failed to get Supabase project info: {str(e)}")

        # Return the tool definition with the enhanced description
        return ToolDefinition(
            name=self.name,
            description=description,
            input_schema_json=json.dumps(self.input_schema),
        )

    def check_validated_input_safe(self, validated_input: SupabaseToolInput) -> bool:
        """Check if the tool call is safe with validated input.

        Args:
            validated_input: The validated input to check.

        Returns:
            True if the tool call is safe, False otherwise.
        """
        # Only GET requests are considered safe
        return validated_input.method == "GET"

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        """Return the availability status of the tool.

        Args:
            request_context: The request context.

        Returns:
            The availability status of the tool.
        """
        try:
            # Check if the feature flag is enabled
            # The feature flag context is already bound with user_uuid by the API proxy
            context = base.feature_flags.get_global_context()
            if not _ENABLE_SUPABASE_SERVICE.get(context):
                log.info("Supabase service disabled by feature flag")
                return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

            # Check if Supabase is configured
            response = self.supabase_client.is_configured(request_context)
            if response.is_configured:
                return agents_pb2.ToolAvailabilityStatus.AVAILABLE
            else:
                return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED
        except grpc.RpcError as e:
            if e.code() != grpc.StatusCode.UNAVAILABLE:  # type: ignore
                log.error("Failed to check Supabase availability", exc_info=e)
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS
        except Exception as e:
            log.error(f"Failed to check Supabase availability: {e}")
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

    def get_oauth_url(self, request_context: RequestContext) -> str:
        """Return the OAuth URL to redirect the user to when needed.

        Args:
            request_context: The request context.

        Returns:
            The OAuth URL to redirect the user to.
        """
        try:
            response = self.supabase_client.get_supabase_oauth_url(request_context)
            return response.oauth_url
        except Exception as e:
            log.error(f"Failed to get Supabase OAuth URL: {e}")
            return ""

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:  # type: ignore
        """Revoke tool access by deactivating OAuth for this tool.

        Args:
            request_context: The request context.

        Returns:
            status_pb2.Status: The status of the operation
        """
        try:
            self.supabase_client.revoke_oauth_grant(request_context)
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.OK.value[0],
                message="Successfully revoked Supabase access",
            )
        except Exception as e:
            log.error(f"Failed to revoke Supabase access: {e}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Failed to revoke Supabase access: {str(e)}",
            )

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # type: ignore
        """Test the connection for this tool.

        Args:
            request_context: The request context.

        Returns:
            status_pb2.Status: The status of the connection test
        """
        try:
            # Test the connection by checking if Supabase is configured
            # and then trying to list projects
            response = self.supabase_client.is_configured(request_context)
            if not response.is_configured:
                return status_pb2.Status(  # type: ignore
                    code=grpc.StatusCode.UNAUTHENTICATED.value[0],
                    message="Supabase is not configured",
                )

            # Test the connection by listing projects
            response = self.supabase_client.test_connection(request_context)
            if response.status_code != grpc.StatusCode.OK.value[0]:
                return status_pb2.Status(  # type: ignore
                    code=response.status_code,
                    message=response.error_message,
                )

            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.OK.value[0],
                message="Supabase connection is working",
            )
        except Exception as e:
            log.error(f"Failed to test Supabase connection: {e}")
            return status_pb2.Status(  # type: ignore
                code=grpc.StatusCode.INTERNAL.value[0],
                message=f"Failed to test Supabase connection: {str(e)}",
            )

    def run_validated(
        self,
        validated_input: SupabaseToolInput,
        extra_tool_input: EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Supabase tool.

        Args:
            validated_input: The validated input.
            extra_tool_input: Extra input for the tool.
            request_context: The request context.

        Returns:
            The tool output.
        """
        try:
            # No credentials needed - using OAuth flow

            # Call the Supabase API directly with the provided inputs
            response = self.supabase_client.call_supabase_api(
                path=validated_input.path,
                method=supabase_pb2.HTTPMethod.Value(validated_input.method),
                request_context=request_context,
                data=validated_input.data,
            )

            if response.status_code == 401:
                raise ToolAuthenticationError(
                    "Authentication failed. Please check your credentials."
                )

            if not (200 <= response.status_code < 300):
                raise Exception(
                    f"Supabase API call failed with status code {response.status_code}: {response.response}"
                )

            # Format the response
            try:
                parsed_response = json.loads(response.response)
                return json.dumps(parsed_response, indent=2)
            except json.JSONDecodeError:
                # If response is not valid JSON, return it as-is
                log.warning("Failed to parse response as JSON, returning as-is")
                return response.response

        except ToolAuthenticationError as e:
            log.error(f"Supabase authentication error: {e}")
            raise
        except ToolNotAvailableError as e:
            log.error(f"Supabase tool not available: {e}")
            raise
        except Exception as e:
            log.error(f"Failed to run Supabase tool: {e}")
            return f"Error: {str(e)}"

"""Tests for the Supabase client."""

from unittest import mock

import pytest
import grpc

from services.lib.request_context.request_context import RequestContext
from services.integrations.supabase.client.client import SupabaseClient
import services.integrations.supabase.supabase_pb2 as supabase_pb2


@pytest.fixture
def client_setup():
    """Set up the test environment before each test."""
    # Create a mock stub with all the required methods
    mock_stub = mock.MagicMock()
    mock_stub.IsConfigured = mock.MagicMock()
    mock_stub.GetSupabaseOAuthUrl = mock.MagicMock()
    mock_stub.HydrateSupabaseSettings = mock.MagicMock()
    mock_stub.RevokeOAuthGrant = mock.MagicMock()

    # Create a patch for setup_stub to return our mock stub
    setup_stub_patch = mock.patch(
        "services.integrations.supabase.client.client.setup_stub",
        return_value=mock_stub,
    )
    setup_stub_patch.start()

    # Create a client instance for testing
    endpoint = "test-endpoint"
    credentials = mock.MagicMock(spec=grpc.ChannelCredentials)
    options = [("test", "option")]
    client = SupabaseClient(endpoint, credentials, options)

    # Create a request context for testing
    request_context = RequestContext.create(request_source="test-source")

    yield client, mock_stub, endpoint, credentials, options, request_context

    # Clean up after each test
    setup_stub_patch.stop()


def test_is_configured(client_setup):
    """Test the is_configured method."""
    client, mock_stub, _, _, _, request_context = client_setup

    # Create a mock response
    mock_response = supabase_pb2.IsConfiguredResponse(is_configured=True)
    mock_stub.IsConfigured.return_value = mock_response

    # Call the method
    response = client.is_configured(request_context)

    # Verify the stub method was called with the correct request
    mock_stub.IsConfigured.assert_called_once()
    args, kwargs = mock_stub.IsConfigured.call_args
    request = args[0]
    assert isinstance(request, supabase_pb2.IsConfiguredRequest)

    # Verify the metadata was passed correctly
    assert kwargs["metadata"] == request_context.to_metadata()

    # Verify the response is returned correctly
    assert response == mock_response
    assert response.is_configured is True


def test_get_supabase_oauth_url(client_setup):
    """Test the get_supabase_oauth_url method."""
    client, mock_stub, _, _, _, request_context = client_setup

    # Create a mock response
    mock_response = supabase_pb2.GetSupabaseOAuthUrlResponse(
        oauth_url="https://example.com/oauth", status_code=200
    )
    mock_stub.GetSupabaseOAuthUrl.return_value = mock_response

    # Call the method
    response = client.get_supabase_oauth_url(request_context)

    # Verify the stub method was called with the correct request
    mock_stub.GetSupabaseOAuthUrl.assert_called_once()
    args, kwargs = mock_stub.GetSupabaseOAuthUrl.call_args
    request = args[0]
    assert isinstance(request, supabase_pb2.GetSupabaseOAuthUrlRequest)

    # Verify the request is empty (no fields)
    assert str(request) == ""

    # Verify the metadata was passed correctly
    assert kwargs["metadata"] == request_context.to_metadata()

    # Verify the response is returned correctly
    assert response == mock_response
    assert response.oauth_url == "https://example.com/oauth"
    assert response.status_code == 200


def test_hydrate_supabase_settings(client_setup):
    """Test the hydrate_supabase_settings method."""
    client, mock_stub, _, _, _, request_context = client_setup

    # Create a mock response
    mock_response = supabase_pb2.HydrateSupabaseSettingsResponse(
        status_code=200,
        session_id="test-session-id",
        projects=[
            supabase_pb2.SupabaseProject(
                ref="test-ref",
                name="test-project",
                url="https://test-project.supabase.co",
                region="us-west-1",
            )
        ],
    )
    mock_stub.HydrateSupabaseSettings.return_value = mock_response

    # Call the method
    code = "test-code"
    response = client.hydrate_supabase_settings(code, request_context)

    # Verify the stub method was called with the correct request
    mock_stub.HydrateSupabaseSettings.assert_called_once()
    args, kwargs = mock_stub.HydrateSupabaseSettings.call_args
    request = args[0]
    assert isinstance(request, supabase_pb2.HydrateSupabaseSettingsRequest)
    assert request.code == code

    # Verify the metadata was passed correctly
    assert kwargs["metadata"] == request_context.to_metadata()

    # Verify the response is returned correctly
    assert response == mock_response
    assert response.status_code == 200
    assert response.session_id == "test-session-id"
    assert len(response.projects) == 1
    assert response.projects[0].ref == "test-ref"
    assert response.projects[0].name == "test-project"
    assert response.projects[0].url == "https://test-project.supabase.co"
    assert response.projects[0].region == "us-west-1"


def test_revoke_oauth_grant(client_setup):
    """Test the revoke_oauth_grant method."""
    client, mock_stub, _, _, _, request_context = client_setup

    # Create a mock response
    from google.rpc import status_pb2

    status = status_pb2.Status(code=0, message="Success")
    mock_response = supabase_pb2.RevokeOAuthGrantResponse(status=status)
    mock_stub.RevokeOAuthGrant.return_value = mock_response

    # Call the method
    response = client.revoke_oauth_grant(request_context)

    # Verify the stub method was called with the correct request
    mock_stub.RevokeOAuthGrant.assert_called_once()
    args, kwargs = mock_stub.RevokeOAuthGrant.call_args
    request = args[0]
    assert isinstance(request, supabase_pb2.RevokeOAuthGrantRequest)

    # Verify the metadata was passed correctly
    assert kwargs["metadata"] == request_context.to_metadata()

    # Verify the response is returned correctly
    assert response == mock_response
    assert response.status.code == 0
    assert response.status.message == "Success"

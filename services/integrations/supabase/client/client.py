"""Client for the Supabase integration service."""

import logging
from typing import Any

import grpc

from base.python.grpc import client_options
from services.lib.request_context.request_context import RequestContext
import services.integrations.supabase.supabase_pb2 as supabase_pb2
import services.integrations.supabase.supabase_pb2_grpc as supabase_pb2_grpc

log = logging.getLogger(__name__)


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> supabase_pb2_grpc.SupabaseStub:
    """Setup the client stub for the Supabase service.

    Args:
        endpoint: The endpoint of the Supabase service.
        credentials: The credentials to use for the channel (optional)
        options: Additional gRPC channel options (optional)

    Returns:
        The client stub for the Supabase service.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, options or [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = supabase_pb2_grpc.SupabaseStub(channel)
    return stub


class SupabaseClient:
    """Client for the Supabase integration service."""

    def __init__(
        self,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        """Initialize the Supabase client.

        Args:
            endpoint: The endpoint of the Supabase integration service.
            credentials: The credentials to use for the channel (optional)
            options: Additional gRPC channel options (optional)
        """
        self.stub = setup_stub(endpoint, credentials, options=options)

    def is_configured(
        self, request_context: RequestContext
    ) -> supabase_pb2.IsConfiguredResponse:
        """Check if Supabase is configured.

        Args:
            request_context: The request context.

        Returns:
            Response indicating if Supabase is configured.
        """
        with request_context.with_context_logging():
            request = supabase_pb2.IsConfiguredRequest()
            return self.stub.IsConfigured(
                request, metadata=request_context.to_metadata()
            )

    def get_supabase_oauth_url(
        self,
        request_context: RequestContext,
    ) -> supabase_pb2.GetSupabaseOAuthUrlResponse:
        """Get the Supabase OAuth URL.

        Args:
            request_context: The request context.

        Returns:
            Response containing the OAuth URL.
        """
        with request_context.with_context_logging():
            request = supabase_pb2.GetSupabaseOAuthUrlRequest()
            return self.stub.GetSupabaseOAuthUrl(
                request, metadata=request_context.to_metadata()
            )

    def hydrate_supabase_settings(
        self, code: str, request_context: RequestContext
    ) -> supabase_pb2.HydrateSupabaseSettingsResponse:
        """Hydrate Supabase settings with OAuth code.

        Args:
            code: The OAuth code to use.
            request_context: The request context.

        Returns:
            Response after hydrating settings.
        """
        with request_context.with_context_logging():
            request = supabase_pb2.HydrateSupabaseSettingsRequest(code=code)
            return self.stub.HydrateSupabaseSettings(
                request, metadata=request_context.to_metadata()
            )

    def revoke_oauth_grant(
        self, request_context: RequestContext
    ) -> supabase_pb2.RevokeOAuthGrantResponse:
        """Revoke the OAuth grant.

        Args:
            request_context: The request context.

        Returns:
            Response after revoking the OAuth grant.
        """
        with request_context.with_context_logging():
            request = supabase_pb2.RevokeOAuthGrantRequest()
            return self.stub.RevokeOAuthGrant(
                request, metadata=request_context.to_metadata()
            )

    def test_connection(
        self, request_context: RequestContext
    ) -> supabase_pb2.TestConnectionResponse:
        """Test the Supabase connection.

        Args:
            request_context: The request context.

        Returns:
            Response after testing the connection.
        """
        with request_context.with_context_logging():
            log.info("Testing Supabase connection")
            request = supabase_pb2.TestConnectionRequest()
            return self.stub.TestConnection(
                request, metadata=request_context.to_metadata()
            )

    def get_projects(
        self, request_context: RequestContext
    ) -> supabase_pb2.GetProjectsResponse:
        """Get the list of Supabase projects for the user from settings.

        Args:
            request_context: The request context.

        Returns:
            Response containing the list of Supabase projects.
        """
        with request_context.with_context_logging():
            log.info("Getting Supabase projects from settings")
            request = supabase_pb2.GetProjectsRequest()
            return self.stub.GetProjects(
                request, metadata=request_context.to_metadata()
            )

    def call_supabase_api(
        self,
        path: str,
        method: supabase_pb2.HTTPMethod.ValueType,
        request_context: RequestContext,
        data: dict[str, Any] | None = None,
    ) -> supabase_pb2.SupabaseApiResponse:
        """Call the Supabase API with the given request.

        Args:
            path: The API path to call.
            method: The HTTP method to use as an enum value.
            request_context: The request context.
            data: Optional JSON data for the request body.

        Returns:
            Response from the Supabase API.
        """
        with request_context.with_context_logging():
            log.info("Calling Supabase API")

            # Check for UNKNOWN method
            if method == supabase_pb2.HTTPMethod.UNKNOWN:
                raise ValueError("Unknown HTTP method")

            # Create the request
            request = supabase_pb2.SupabaseApiRequest(
                path=path,
                method=method,
            )

            # Authentication is handled by the server using the user's settings

            # Add data if provided
            if data:
                from google.protobuf.struct_pb2 import Struct

                data_struct = Struct()
                data_struct.update(data)
                request.data.CopyFrom(data_struct)

            return self.stub.CallSupabaseApi(
                request, metadata=request_context.to_metadata()
            )

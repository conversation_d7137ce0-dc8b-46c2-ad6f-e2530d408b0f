load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/integrations/supabase:supabase_py_proto",
        "//services/lib/request_context:request_context_py",
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["client_test.py"],
    deps = [
        ":client_py",
        "//base/python/grpc:client_options",
        "//services/integrations/supabase:supabase_py_proto",
        "//services/lib/request_context:request_context_py",
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

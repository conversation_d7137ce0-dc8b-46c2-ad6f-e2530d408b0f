load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

py_library(
    name = "config",
    srcs = ["config.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":atlassian_py_proto",
        "//services/agents:tool",
        requirement("pydantic"),
        requirement("dataclasses-json"),
    ],
)

proto_library(
    name = "atlassian_proto",
    srcs = ["atlassian.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:struct_proto",
    ],
)

py_grpc_library(
    name = "atlassian_py_proto",
    protos = [":atlassian_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

ts_proto_library(
    name = "atlassian_ts_proto",
    node_modules = "//:node_modules",
    proto = ":atlassian_proto",
    # consumed by services/customer/frontend
    visibility = ["//services:__subpackages__"],
)

"""Configuration for Atlassian integrations."""

from dataclasses import dataclass
from pydantic import SecretStr
from dataclasses_json import dataclass_json

from services.agents.tool import BaseExtraToolInput
from services.integrations.atlassian import atlassian_pb2


@dataclass_json
@dataclass
class AtlassianBasicCredentials:
    """Parsed credentials for Atlassian with a personal API token."""

    server_url: str
    """The URL of the Atlassian server. For example https://augmentcode.atlassian.net"""

    personal_api_token: SecretStr
    """The API token for the Atlassian account."""

    username: str
    """The username for the Atlassian account.  This is an email address."""


@dataclass
class AtlassianExtraToolInput(BaseExtraToolInput):
    """Wrapper for AtlassianBasicCredentials that implements BaseExtraToolInput."""

    credentials: AtlassianBasicCredentials
    """The underlying Atlassian credentials."""

    @classmethod
    def from_fields(
        cls,
        server_url: str,
        personal_api_token: str,
        username: str,
    ) -> "AtlassianExtraToolInput":
        """Create an AtlassianToolInput from AtlassianBasicCredentials."""
        return cls(
            credentials=AtlassianBasicCredentials(
                server_url=server_url,
                personal_api_token=SecretStr(personal_api_token),
                username=username,
            )
        )

    def get_credentials(self) -> atlassian_pb2.AtlassianBasicCredentials | None:
        """Get the underlying AtlassianBasicCredentials."""
        # Return None if any required credential is missing
        if not all(
            [
                self.credentials,
                self.credentials.server_url,
                self.credentials.personal_api_token,
                self.credentials.username,
            ]
        ):
            return None

        # Create and populate the protobuf credentials object
        credentials = atlassian_pb2.AtlassianBasicCredentials()
        credentials.server_url = self.credentials.server_url
        credentials.personal_api_token = (
            self.credentials.personal_api_token.get_secret_value()
        )
        credentials.username = self.credentials.username

        return credentials

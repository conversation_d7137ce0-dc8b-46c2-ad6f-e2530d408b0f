// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/atlassian/atlassian.proto (package atlassian, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import {
  GetConfluenceContentRequest,
  GetConfluenceContentResponse,
  GetConfluenceSpaceRequest,
  GetConfluenceSpaceResponse,
  GetJiraIssueRequest,
  GetJiraIssueResponse,
  GetJiraProjectStructureRequest,
  GetJiraProjectStructureResponse,
  HydrateAtlassianUserSettingsRequest,
  HydrateAtlassianUserSettingsResponse,
  SearchConfluenceContentRequest,
  SearchConfluenceContentResponse,
  SearchJiraIssuesRequest,
  SearchJiraIssuesResponse,
} from "./atlassian_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service atlassian.Atlassian
 */
export declare const Atlassian: {
  readonly typeName: "atlassian.Atlassian";
  readonly methods: {
    /**
     * @generated from rpc atlassian.Atlassian.SearchJiraIssues
     */
    readonly searchJiraIssues: {
      readonly name: "SearchJiraIssues";
      readonly I: typeof SearchJiraIssuesRequest;
      readonly O: typeof SearchJiraIssuesResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc atlassian.Atlassian.GetJiraIssue
     */
    readonly getJiraIssue: {
      readonly name: "GetJiraIssue";
      readonly I: typeof GetJiraIssueRequest;
      readonly O: typeof GetJiraIssueResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc atlassian.Atlassian.GetJiraProjectStructure
     */
    readonly getJiraProjectStructure: {
      readonly name: "GetJiraProjectStructure";
      readonly I: typeof GetJiraProjectStructureRequest;
      readonly O: typeof GetJiraProjectStructureResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc atlassian.Atlassian.SearchConfluenceContent
     */
    readonly searchConfluenceContent: {
      readonly name: "SearchConfluenceContent";
      readonly I: typeof SearchConfluenceContentRequest;
      readonly O: typeof SearchConfluenceContentResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc atlassian.Atlassian.GetConfluenceContent
     */
    readonly getConfluenceContent: {
      readonly name: "GetConfluenceContent";
      readonly I: typeof GetConfluenceContentRequest;
      readonly O: typeof GetConfluenceContentResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc atlassian.Atlassian.GetConfluenceSpace
     */
    readonly getConfluenceSpace: {
      readonly name: "GetConfluenceSpace";
      readonly I: typeof GetConfluenceSpaceRequest;
      readonly O: typeof GetConfluenceSpaceResponse;
      readonly kind: MethodKind.Unary;
    };
    /**
     * @generated from rpc atlassian.Atlassian.HydrateAtlassianUserSettings
     */
    readonly hydrateAtlassianUserSettings: {
      readonly name: "HydrateAtlassianUserSettings";
      readonly I: typeof HydrateAtlassianUserSettingsRequest;
      readonly O: typeof HydrateAtlassianUserSettingsResponse;
      readonly kind: MethodKind.Unary;
    };
  };
};

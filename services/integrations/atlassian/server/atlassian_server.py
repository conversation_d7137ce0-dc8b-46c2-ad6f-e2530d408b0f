"""RPC server for Atlassian integration."""

import logging

from google.rpc import code_pb2, status_pb2

from services.settings.client.client import SettingsClient
from services.integrations.atlassian import atlassian_pb2, atlassian_pb2_grpc
from services.integrations.atlassian.server.config import Config
from services.integrations.atlassian.server.atlassian_handler import (
    AtlassianHandler,
)
from services.integrations.atlassian.server.atlassian_auth_processor import (
    AtlassianAuthProcessor,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.lib.request_context.request_context import RequestContext
import services.lib.grpc.tls_config.tls_config as tls_config

from services.lib.grpc.auth.service_auth_interceptor import (
    get_auth_info_from_grpc_context,
)

log = logging.getLogger(__name__)


class AtlassianServer(atlassian_pb2_grpc.AtlassianServicer):
    """RPC server to call Atlassian instance."""

    def __init__(
        self, config: Config, request_insight_publisher: RequestInsightPublisher
    ):
        super().__init__()
        self.config = config

        settings_client = SettingsClient(
            config.settings_endpoint,
            tls_config.get_client_tls_creds(config.client_mtls),
        )

        self.auth_processor = AtlassianAuthProcessor(
            settings_client,
            config.atlassian_app_callback_url,
            config.atlassian_app_secrets_path,
            config.atlassian_app_client_id_path,
        )

        self.atlassian_handler = AtlassianHandler(
            atlassian_auth_processor=self.auth_processor,
        )

        # TODO: add request insight builder
        # self.ri_builder = GleanRequestInsightBuilder(request_insight_publisher)

    def IsConfigured(
        self, request: atlassian_pb2.IsConfiguredRequest, context
    ) -> atlassian_pb2.IsConfiguredResponse:
        """Check if the user has configured Atlassian credentials for the given service."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            is_configured = self.auth_processor.is_configured(
                request_context,
                request.atlassian_service,
            )
            return atlassian_pb2.IsConfiguredResponse(is_configured=is_configured)

    def SearchJiraIssues(
        self, request: atlassian_pb2.SearchJiraIssuesRequest, context
    ) -> atlassian_pb2.SearchJiraIssuesResponse:
        """Search for Jira issues and return results as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Searching Jira issues")
            search_results = self.atlassian_handler.search_jira_issues(
                request.query,
                request.credentials or None,
                request_context,
                request.max_results,
            )
            return atlassian_pb2.SearchJiraIssuesResponse(
                issues_markdown=search_results
            )

    def GetJiraIssue(
        self, request: atlassian_pb2.GetJiraIssueRequest, context
    ) -> atlassian_pb2.GetJiraIssueResponse:
        """Get detailed information about a single Jira issue by its key and return as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Getting Jira issue")
            issue_details = self.atlassian_handler.get_jira_issue(
                request.issue_key,
                request.credentials or None,
                request_context,
            )
            return atlassian_pb2.GetJiraIssueResponse(issue_markdown=issue_details)

    def GetJiraProjectStructure(
        self, request: atlassian_pb2.GetJiraProjectStructureRequest, context
    ) -> atlassian_pb2.GetJiraProjectStructureResponse:
        """Get the structure of Jira projects and return as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Getting Jira project structure")
            project_structure = self.atlassian_handler.get_jira_project_structure(
                request.project_key,
                request.credentials or None,
                request_context,
            )
            return atlassian_pb2.GetJiraProjectStructureResponse(
                project_structure_markdown=project_structure
            )

    def SearchConfluenceContent(
        self, request: atlassian_pb2.SearchConfluenceContentRequest, context
    ) -> atlassian_pb2.SearchConfluenceContentResponse:
        """Search for Confluence content using CQL and return results as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Searching Confluence content")
            search_results = self.atlassian_handler.search_confluence_content(
                request.query,
                request.credentials or None,
                request_context,
                request.max_results,
            )
            return atlassian_pb2.SearchConfluenceContentResponse(
                content_list_markdown=search_results
            )

    def GetConfluenceContent(
        self, request: atlassian_pb2.GetConfluenceContentRequest, context
    ) -> atlassian_pb2.GetConfluenceContentResponse:
        """Get detailed information about a single Confluence content by its ID and return as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Getting Confluence content")
            content_details = self.atlassian_handler.get_confluence_content(
                request.content_id,
                request.credentials or None,
                request_context,
            )
            return atlassian_pb2.GetConfluenceContentResponse(
                content_markdown=content_details
            )

    def GetConfluenceSpace(
        self, request: atlassian_pb2.GetConfluenceSpaceRequest, context
    ) -> atlassian_pb2.GetConfluenceSpaceResponse:
        """Get the content of a Confluence space and return as Markdown."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Getting Confluence space")
            space_content = self.atlassian_handler.get_confluence_space(
                request.space_key,
                request.credentials or None,
                request_context,
                request.content_type,
                request.max_results,
            )
            return atlassian_pb2.GetConfluenceSpaceResponse(
                space_content_markdown=space_content
            )

    def HydrateAtlassianUserSettings(
        self, request: atlassian_pb2.HydrateAtlassianUserSettingsRequest, context
    ) -> atlassian_pb2.HydrateAtlassianUserSettingsResponse:
        """Exchange the auth code for access and refresh tokens and store them in the user settings."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Hydrating Atlassian user settings")
            return self.auth_processor.hydrate_atlassian_user_settings(
                code=request.code,
                request_context=request_context,
            )

    def GetOAuthUrl(
        self, request: atlassian_pb2.GetOAuthUrlRequest, context
    ) -> atlassian_pb2.GetOAuthUrlResponse:
        """Get the OAuth URL for a service."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            return atlassian_pb2.GetOAuthUrlResponse(
                oauth_url=self.auth_processor.get_oauth_url(
                    request.atlassian_service, request_context
                )
            )

    def CallAtlassianApi(
        self, request: atlassian_pb2.AtlassianApiRequest, context
    ) -> atlassian_pb2.AtlassianApiResponse:
        """Call the Atlassian API with the given request."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Calling Atlassian API")
            return self.atlassian_handler.call_atlassian_api(
                request.atlassian_service,
                request.endpoint,
                request.credentials or None,
                request_context,
                request.method,
                request.data,
            )

    def RevokeOAuthToken(
        self, request: atlassian_pb2.RevokeOAuthTokenRequest, context
    ) -> atlassian_pb2.RevokeOAuthTokenResponse:
        """Revoke the OAuth token for the current user."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            log.error("tenant_id not set in auth info")
            return atlassian_pb2.RevokeOAuthTokenResponse(
                status=status_pb2.Status(  # type: ignore
                    code=code_pb2.UNAUTHENTICATED,  # type: ignore
                    message="tenant_id not set in auth info",
                )
            )

        with request_context.with_context_logging(), auth_info.with_context_logging():
            log.info("Revoking Atlassian OAuth token")
            try:
                return self.auth_processor.revoke_oauth_token(request_context, request)
            except Exception as e:
                log.error("Error in revoking Atlassian OAuth token: %s", str(e))
                return atlassian_pb2.RevokeOAuthTokenResponse(
                    status=status_pb2.Status(  # type: ignore
                        code=code_pb2.INTERNAL,  # type: ignore
                        message=str(e),
                    )
                )

    def GetAtlassianAccountId(
        self, request: atlassian_pb2.GetAtlassianAccountIdRequest, context
    ) -> atlassian_pb2.GetAtlassianAccountIdResponse:
        """Get the Atlassian Account ID for the current user."""
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)

        tenant_id = auth_info.tenant_id
        if not tenant_id:
            raise ValueError("tenant_id must be set in auth info")

        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.auth_processor.get_atlassian_account_id(request_context)

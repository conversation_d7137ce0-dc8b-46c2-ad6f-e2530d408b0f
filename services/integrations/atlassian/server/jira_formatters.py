"""Formatters for Jira entities to markdown text."""

from services.integrations.atlassian.server.jira_client_wrapper import (
    JiraIssue,
    ProjectStructure,
)


def format_single_issue_markdown(issue: JiraIssue) -> str:
    """Format a single Jira issue as markdown text.

    Args:
        issue: JiraIssue object

    Returns:
        Markdown formatted string with detailed issue information
    """
    lines = [f"# [{issue.key}] {issue.summary}"]

    if issue.issue_type:
        lines.append(f"**Type:** {issue.issue_type}")
    if issue.status:
        lines.append(f"**Status:** {issue.status}")
    if issue.priority:
        lines.append(f"**Priority:** {issue.priority}")
    if issue.assignee:
        lines.append(f"**Assignee:** {issue.assignee}")
    if issue.reporter:
        lines.append(f"**Reporter:** {issue.reporter}")
    if issue.created:
        lines.append(f"**Created:** {issue.created.strftime('%Y-%m-%d %H:%M:%S')}")
    if issue.updated:
        lines.append(f"**Updated:** {issue.updated.strftime('%Y-%m-%d %H:%M:%S')}")
    if issue.labels:
        lines.append(f"**Labels:** {', '.join(issue.labels)}")
    if issue.components:
        lines.append(f"**Components:** {', '.join(issue.components)}")
    if issue.description:
        lines.extend(["", "## Description", issue.description])

    return "\n".join(lines)


def format_issue_list_markdown(issues: list[JiraIssue]) -> str:
    """Format multiple Jira issues as a compact markdown list.

    Args:
        issues: List of JiraIssue objects

    Returns:
        Markdown formatted string with summarized issues
    """
    if not issues:
        return "No issues found."

    formatted_issues = []
    for issue in issues:
        formatted_issue = (
            f"- [{issue.key}] {issue.summary}\n"
            f"  Type: {issue.issue_type} | Status: {issue.status}"
        )
        if issue.description:
            formatted_issue += f"\n  Description: {issue.description}"

        formatted_issues.append(formatted_issue)

    return "\n\n".join(formatted_issues)


def format_project_markdown(structure: ProjectStructure) -> str:
    """Format project structure as markdown text.

    Args:
        structure: ProjectStructure object containing project details

    Returns:
        Markdown formatted string with project structure
    """
    lines = [
        f"# Project: {structure.name} ({structure.key})\n",
        "## Boards",
        *[
            f"- {board.name} (ID: {board.id}, Type: {board.type})"
            for board in structure.boards
        ],
        "\n## Active Sprints",
        *[
            f"- {sprint.name} ({sprint.state})"
            for sprint in structure.sprints
            if sprint.state == "active"
        ],
        "\n## Epics",
        *[f"- [{epic.key}] {epic.summary} - {epic.status}" for epic in structure.epics],
        "\n## Teams/Components",
        *[
            f"- {component.name}{f' (Lead: {component.lead.displayName})' if hasattr(component, 'lead') else ''}"
            for component in structure.components
        ],
    ]

    return "\n".join(lines)

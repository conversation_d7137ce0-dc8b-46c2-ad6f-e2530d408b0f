local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
{
  deployment: [
    {
      name: 'atlassian',
      kubecfg: {
        target: '//services/integrations/atlassian/server:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['surbhi', 'aswin', 'xiaolei'],
          slack_channel: '#team-external-context',
        },
      },
    },
  ],
}

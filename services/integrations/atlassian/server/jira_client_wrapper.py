"""Use Jira client issue get/search, and listing project/sprint/epic structure."""

import logging
from dataclasses import dataclass
from typing import Optional, cast
from dataclasses_json import dataclass_json
from datetime import datetime

from jira import JIRA, JIRAError
from jira.resources import Issue, Project, Board, Sprint, Component

from services.integrations.atlassian.server.config import (
    AtlassianError,
)
from services.integrations.atlassian.server.atlassian_auth_processor import (
    AtlassianAuthError,
    AtlassianCredentials,
    AtlassianOAuthCredentials,
)
from services.integrations.atlassian.config import AtlassianBasicCredentials


log = logging.getLogger(__name__)

JIRA_API_BASE_URL = "https://api.atlassian.com/ex/jira/"


@dataclass
class JiraIssue:
    """A cleaner representation of a Jira issue with flattened fields."""

    id: str
    """The numeric ID of the issue."""

    key: str
    """The issue key (e.g., 'PROJ-123')."""

    self: str
    """The REST API URL for this issue."""

    summary: str
    """The issue's summary/title."""

    description: str | None
    """The full description of the issue."""

    status: str
    """Current status name (e.g., 'In Progress', 'Done')."""

    issue_type: str
    """Type of the issue (e.g., 'Bug', 'Story', 'Task')."""

    assignee: str | None
    """Display name of the assigned user, if any."""

    reporter: str | None
    """Display name of the user who reported the issue."""

    priority: str | None
    """Priority level name, if set (e.g., 'High', 'Medium')."""

    created: datetime
    """Timestamp when the issue was created."""

    updated: datetime
    """Timestamp when the issue was last updated."""

    resolution: str | None
    """Resolution status name if resolved (e.g., 'Fixed', 'Won't Fix')."""

    labels: list[str]
    """List of labels attached to the issue."""

    components: list[str]
    """List of component names this issue belongs to."""

    @classmethod
    def from_jira_issue(cls, issue: Issue) -> "JiraIssue":
        """Convert a JIRA Issue object to our cleaner JiraIssue class.

        Args:
            issue: The original Jira Issue object

        Returns:
            A new JiraIssue instance with flattened fields
        """
        fields = issue.fields
        return cls(
            id=issue.id,
            key=issue.key,
            self=issue.self,
            summary=fields.summary,
            description=fields.description,
            status=fields.status.name,
            issue_type=fields.issuetype.name,
            assignee=fields.assignee.displayName if fields.assignee else None,
            reporter=fields.reporter.displayName if fields.reporter else None,
            priority=fields.priority.name if fields.priority else None,
            created=datetime.strptime(fields.created, "%Y-%m-%dT%H:%M:%S.%f%z"),
            updated=datetime.strptime(fields.updated, "%Y-%m-%dT%H:%M:%S.%f%z"),
            resolution=fields.resolution.name if fields.resolution else None,
            labels=fields.labels if hasattr(fields, "labels") else [],
            components=[c.name for c in fields.components]
            if hasattr(fields, "components")
            else [],
        )


@dataclass_json
@dataclass
class ProjectStructure:
    """Represents a Jira project's structure including boards, sprints and epics."""

    key: str
    """The project key (e.g., 'PROJ')."""

    name: str
    """The project name."""

    boards: list[Board]
    """List of boards associated with the project."""

    sprints: list[Sprint]
    """List of sprints associated with the project."""

    epics: list[JiraIssue]
    """List of epics associated with the project."""

    components: list[Component]
    """List of components associated with the project."""


class JiraError(AtlassianError):
    """Raised when Jira operations fail."""

    pass


def get_jira_client(
    credentials: AtlassianCredentials,
) -> JIRA:
    """Create authenticated Jira client."""
    try:
        if isinstance(credentials, AtlassianBasicCredentials):
            return JIRA(
                server=credentials.server_url,
                basic_auth=(
                    credentials.username,
                    credentials.personal_api_token.get_secret_value(),
                ),
            )
        elif isinstance(credentials, AtlassianOAuthCredentials):
            try:
                return JIRA(
                    server=f"{JIRA_API_BASE_URL}{credentials.settings.cloud_id}",
                    token_auth=credentials.settings.access_token,
                )
            except JIRAError as e:
                # JIRA package checks that the user in authenticated on creation
                # If the user is unauthenticated, we can try to refresh the token
                if e.status_code == 401 and credentials.refresh_callback:
                    log.info(
                        "Received 401 from Jira API, refreshing token and retrying"
                    )
                    new_settings = credentials.refresh_callback()
                    return JIRA(
                        server=f"{JIRA_API_BASE_URL}{credentials.settings.cloud_id}",
                        token_auth=new_settings.access_token,
                    )
                raise JiraError(f"Failed to create Jira client: {str(e)}") from e
        raise AtlassianAuthError("No valid authentication method provided")

    except JIRAError as e:
        raise JiraError(f"Failed to create Jira client: {str(e)}")


def search_issues(
    jql: str,
    credentials: AtlassianCredentials,
    max_results: int,
) -> list[JiraIssue]:
    """Search Jira issues using JQL.

    Args:
        jql: JQL search query
        credentials: Jira credentials
        max_results: Maximum number of results to return

    Returns:
        List of JiraIssue objects

    Raises:
        JiraError: If the search fails
    """
    try:
        client = get_jira_client(credentials)
        results = client.search_issues(
            jql,
            maxResults=max_results,
            fields="summary,description,status,issuetype,assignee,reporter,priority,created,updated,resolution,labels,components",
            json_result=False,
        )
        return [
            JiraIssue.from_jira_issue(issue) for issue in cast(list[Issue], results)
        ]
    except JIRAError as e:
        raise JiraError(f"Jira search failed: {str(e)}")


def get_issue(issue_key: str, credentials: AtlassianCredentials) -> JiraIssue:
    """Get a single Jira issue by key.

    Args:
        issue_key: The Jira issue key (e.g. PROJ-123)
        credentials: Jira credentials

    Returns:
        JiraIssue object

    Raises:
        JiraError: If the issue cannot be retrieved
    """
    try:
        client = get_jira_client(credentials)
        issue = client.issue(
            issue_key,
            fields="summary,description,status,issuetype,assignee,reporter,priority,created,updated,resolution,labels,components",
        )
        return JiraIssue.from_jira_issue(issue)
    except JIRAError as e:
        raise JiraError(f"Failed to get issue {issue_key}: {str(e)}")


def get_project(project_key: str, credentials: AtlassianCredentials) -> Project:
    """Get a Jira project by key."""
    try:
        client = get_jira_client(credentials)
        return client.project(project_key)
    except JIRAError as e:
        raise JiraError(f"Failed to get project {project_key}: {str(e)}")


def get_boards(project_key: str, client: JIRA) -> list[Board]:
    """Get all boards for a project."""
    try:
        return client.boards(projectKeyOrID=project_key)
    except JIRAError as e:
        raise JiraError(f"Failed to get boards for project {project_key}: {str(e)}")


def get_sprints(
    board_id: int, client: JIRA, state: Optional[str] = None
) -> list[Sprint]:
    """Get all sprints for a board."""
    try:
        return client.sprints(board_id, state=state)
    except JIRAError as e:
        raise JiraError(f"Failed to get sprints for board {board_id}: {str(e)}")


def get_epics(project_key: str, client: JIRA) -> list[JiraIssue]:
    """Get all epics in a project."""
    try:
        jql = f"project = {project_key} AND issuetype = Epic"
        issues = client.search_issues(
            jql,
            maxResults=100,
            fields="summary,description,status,issuetype,assignee,reporter,priority,created,updated,resolution,labels,components",
            json_result=False,
        )
        return [JiraIssue.from_jira_issue(issue) for issue in cast(list[Issue], issues)]
    except JIRAError as e:
        raise JiraError(f"Failed to get epics for project {project_key}: {str(e)}")


def get_project_structure(
    credentials: AtlassianCredentials,
    project_key: str | None = None,
) -> list[ProjectStructure]:
    """Get all project structures from Jira.

    Args:
        credentials: Jira credentials

    Returns:
        List of ProjectStructures for all projects

    Raises:
        JiraError: If project structures cannot be retrieved
    """
    try:
        client = get_jira_client(credentials)

        # Get all data in bulk where possible
        if project_key is not None:
            projects = [get_project(project_key, credentials)]
        else:
            # Get all projects, boards, and components in one call
            projects = client.projects()
        boards = client.boards()

        # Get all epics in one call
        all_epics = client.search_issues(
            "issuetype = Epic "
            + (f"AND project = {project_key}" if project_key else ""),
            maxResults=1000,
            fields="summary,description,status,issuetype,assignee,reporter,priority,created,updated,resolution,labels,components",
            json_result=False,
        )
        all_epics = [
            JiraIssue.from_jira_issue(issue) for issue in cast(list[Issue], all_epics)
        ]

        # Get all components in one call
        all_components = {}
        for project in projects:
            all_components[project.key] = client.project_components(project.key)

        # Build ProjectStructure objects
        structures = []
        for project in projects:
            # Filter boards for this project
            project_boards = [b for b in boards if b.location.projectKey == project.key]

            # Get sprints for all boards in this project
            project_sprints = []
            for board in project_boards:
                try:
                    sprints = client.sprints(board.id)
                    project_sprints.extend(sprints)
                except JIRAError as e:
                    log.warning(f"Failed to get sprints for board {board.id}: {str(e)}")
                    continue

            # Filter epics for this project
            project_epics = [
                e for e in all_epics if e.key.startswith(f"{project.key}-")
            ]

            # Get components for this project
            components = all_components.get(project.key, [])

            structures.append(
                ProjectStructure(
                    key=project.key,
                    name=project.name,
                    boards=project_boards,
                    sprints=project_sprints,
                    epics=project_epics,
                    components=components,
                )
            )

        return structures

    except JIRAError as e:
        raise JiraError(f"Failed to get project structures: {str(e)}")

"""Formatters for Confluence content to markdown text."""


def format_content_markdown(content) -> str:
    """Format Confluence content as markdown.

    Args:
        content: Confluence content object with title, parent_id, space_key, etc.

    Returns:
        Markdown formatted string with content details
    """
    result = [f"# {content.title}"]

    if content.parent_id:
        result.append(f"\nParent Page ID: {content.parent_id}")

    result.append(f"\nSpace: {content.space_key}")
    result.append(f"Last Modified: {content.last_modified}")
    result.append(f"Created By: {content.created_by}")

    if content.labels:
        labels = ", ".join(f"`{label.label}`" for label in content.labels)
        result.append(f"\nLabels: {labels}")

    result.append("\n## Content")
    result.extend(content.get_readable_content())

    if content.comments:
        result.append("\n## Comments")
        for comment in content.comments:
            result.append(f"\n### Comment by {comment.created_by} on {comment.created}")
            result.append(comment.body)

    return "\n".join(result)


def format_content_list_markdown(contents) -> str:
    """Format a list of Confluence content items as markdown.

    Args:
        contents: List of Confluence content objects

    Returns:
        Markdown formatted string with content list
    """
    if not contents:
        return "No results found."

    result = ["# Search Results", ""]

    for content in contents:
        result.append(f"## {content.title}")
        result.append(f"- Space: {content.space_key}")
        result.append(f"- Last Modified: {content.last_modified}")
        result.append(f"- Created By: {content.created_by}")
        result.append(f"- [View in Confluence]({content.url})")
        result.append("")

    return "\n".join(result)

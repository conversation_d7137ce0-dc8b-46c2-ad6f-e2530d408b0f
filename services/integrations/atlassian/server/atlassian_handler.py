"""Implement Atlassian handler.

This handler provides methods for interacting with Atlassian services such as Jira and Confluence.
"""

import json
import logging
import dataclasses

from pydantic import SecretStr

from services.integrations.atlassian.config import AtlassianBasicCredentials
from services.integrations.atlassian import atlassian_pb2
from services.integrations.atlassian.server.config import (
    AtlassianError,
)
from services.integrations.atlassian.server.atlassian_auth_processor import (
    AtlassianAuthProcessor,
    AtlassianAuthError,
    AtlassianCredentials,
)
from services.lib.request_context.request_context import RequestContext
from services.integrations.atlassian.server.jira_client_wrapper import (
    search_issues,
    get_issue,
    get_project_structure,
)
from services.integrations.atlassian.server.jira_formatters import (
    format_single_issue_markdown,
    format_issue_list_markdown,
    format_project_markdown,
)
from services.integrations.atlassian.server.confluence_client_wrapper import (
    ConfluenceClient,
)
from services.integrations.atlassian.server.confluence_formatters import (
    format_content_list_markdown,
    format_content_markdown,
)
from services.integrations.atlassian.server.atlassian_client_wrappers import (
    AtlassianClientWrapper,
)


log = logging.getLogger(__name__)


class AtlassianHandler:
    """Atlassian handler class."""

    def __init__(self, atlassian_auth_processor: AtlassianAuthProcessor):
        """Initialize the Atlassian handler."""
        self.confluence_client = ConfluenceClient()
        self.atlassian_client_wrapper = AtlassianClientWrapper()
        self.auth_processor = atlassian_auth_processor

    def get_credentials(
        self,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        service_type: atlassian_pb2.AtlassianService.ValueType,
        request_context: RequestContext,
    ) -> AtlassianCredentials:
        """Parse credentials from the request.

        Args:
            credentials: Credentials from the request

        Returns:
            Parsed credentials
        """

        # Case 1: Basic auth credentials provided directly
        if credentials and all(
            [
                credentials.server_url,
                credentials.personal_api_token,
                credentials.username,
            ]
        ):
            return AtlassianBasicCredentials(
                server_url=credentials.server_url,
                personal_api_token=SecretStr(credentials.personal_api_token),
                username=credentials.username,
            )

        # Case 2: Get OAuth credentials from settings
        try:
            oauth_credentials = self.auth_processor.get_stored_user_credentials(
                request_context=request_context,
                service_type=service_type,
            )
            if not oauth_credentials.settings.access_token:
                raise AtlassianAuthError("No access token found")
            return oauth_credentials
        except AtlassianAuthError as e:
            raise AtlassianError(f"Failed to get credentials: {e}") from e

    def search_jira_issues(
        self,
        query: str,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        request_context: RequestContext,
        max_results: int = 25,
    ) -> str:
        """Search for Jira issues using JQL.

        Args:
            query: JQL search query
            request_context: Request context for tracking and metrics
            credentials: Optional credentials to use instead of looking them up in the settings service
            max_results: Maximum number of results to return

        Returns:
            Markdown formatted search results
        """

        try:
            atlassian_credentials = self.get_credentials(
                credentials,
                atlassian_pb2.AtlassianService.JIRA,
                request_context,
            )
            results = search_issues(
                query,
                atlassian_credentials,
                max_results,
            )
        except AtlassianError as e:
            log.error("Jira search failed: %s", str(e))
            raise
        return format_issue_list_markdown(results)

    def get_jira_issue(
        self,
        issue_key: str,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        request_context: RequestContext,
    ) -> str:
        """Get detailed information about a single Jira issue by its key.

        Args:
            issue_key: The Jira issue key (e.g. PROJ-123)
            credentials: Credentials for Jira

        Returns:
            Markdown formatted issue details
        """

        try:
            atlassian_credentials = self.get_credentials(
                credentials,
                atlassian_pb2.AtlassianService.JIRA,
                request_context,
            )
            issue = get_issue(issue_key, atlassian_credentials)
            return format_single_issue_markdown(issue)
        except AtlassianError as e:
            log.error("Failed to get Jira issue: %s", str(e))
            raise

    def get_jira_project_structure(
        self,
        project_key: str,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        request_context: RequestContext,
    ) -> str:
        """Get the structure of Jira projects. Returns information about boards, sprints, epics, and components.

        Args:
            project_key: The Jira project key (e.g. PROJ)
            credentials: Credentials for Jira

        Returns:
            Markdown formatted project structure
        """
        try:
            atlassian_credentials = self.get_credentials(
                credentials,
                atlassian_pb2.AtlassianService.JIRA,
                request_context,
            )
            structure = get_project_structure(
                atlassian_credentials,
                project_key,
            )
            return "\n\n".join([format_project_markdown(s) for s in structure])

        except AtlassianError as e:
            log.error("Failed to get project structure: %s", str(e))
            raise

    def call_atlassian_api(
        self,
        service_type: atlassian_pb2.AtlassianService.ValueType,
        endpoint: str,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        request_context: RequestContext,
        method: atlassian_pb2.HTTPMethod.ValueType,
        data: str,
    ) -> atlassian_pb2.AtlassianApiResponse:
        """Call the Atlassian API with the given request.

        Args:
            endpoint: The API endpoint to call
            credentials: Credentials for Atlassian
            request_context: Request context for tracking and metrics
            method: The HTTP method to use
            data: Protobuf Struct containing data for params (GET) or JSON body (POST/PUT)
            request_body: Alternative string request body to use instead of data

        Returns:
            The response from the API
        """
        try:
            atlassian_credentials = self.get_credentials(
                credentials,
                service_type,
                request_context,
            )

            method_str = atlassian_pb2.HTTPMethod.Name(method)

            # Handle data based on method
            params, request_body = None, None

            if data:
                python_dict = json.loads(data)

                if method_str == "GET":
                    params = python_dict
                else:  # POST, PUT, etc.
                    request_body = python_dict

            response_data, status_code = self.atlassian_client_wrapper.call_api(
                endpoint,
                credentials=atlassian_credentials,
                service_type=service_type,
                method=method_str,
                params=params,
                request_body=request_body,
            )

            return atlassian_pb2.AtlassianApiResponse(
                response=json.dumps(response_data),
                status_code=status_code,
            )
        except AtlassianError as e:
            log.error("Failed to call Atlassian API: %s", str(e))
            raise

    def search_confluence_content(
        self,
        query: str,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        request_context: RequestContext,
        max_results: int = 25,
    ) -> str:
        """Search for Confluence content using CQL.

        Args:
            query: CQL search query
            credentials: Credentials for Confluence
            request_context: Request context for tracking and metrics
            max_results: Maximum number of results to return

        Returns:
            Markdown formatted search results
        """
        try:
            atlassian_credentials = self.get_credentials(
                credentials,
                atlassian_pb2.AtlassianService.CONFLUENCE,
                request_context,
            )
            results = self.confluence_client.search_content(
                cql=query,
                limit=max_results,
                credentials=atlassian_credentials,
            )
        except AtlassianError as e:
            log.error("Confluence search failed: %s", str(e))
            raise
        return format_content_list_markdown(results)

    def get_confluence_content(
        self,
        content_id: str,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        request_context: RequestContext,
    ) -> str:
        """Get detailed information about a single Confluence content by its ID.

        Args:
            content_id: The Confluence content ID
            credentials: Credentials for Confluence

        Returns:
            Markdown formatted content details
        """
        try:
            atlassian_credentials = self.get_credentials(
                credentials,
                atlassian_pb2.AtlassianService.CONFLUENCE,
                request_context,
            )
            content = self.confluence_client.get_content(
                content_id,
                atlassian_credentials,
            )
        except AtlassianError as e:
            log.error("Failed to get Confluence content: %s", str(e))
            raise
        content = dataclasses.replace(
            content,
            comments=self.confluence_client.get_content_comments(
                content_id, atlassian_credentials
            ),
        )
        return format_content_markdown(content)

    def get_confluence_space(
        self,
        space_key: str,
        credentials: atlassian_pb2.AtlassianBasicCredentials,
        request_context: RequestContext,
        content_type: str = "page",
        max_results: int = 25,
    ) -> str:
        """Get the content of a Confluence space.

        Args:
            space_key: The Confluence space key (e.g. DEV)
            credentials: Credentials for Confluence

        Returns:
            Markdown formatted space content
        """
        try:
            atlassian_credentials = self.get_credentials(
                credentials,
                atlassian_pb2.AtlassianService.CONFLUENCE,
                request_context,
            )
            contents = self.confluence_client.get_space_content(
                space_key,
                atlassian_credentials,
                content_type,
                limit=max_results,
            )
        except AtlassianError as e:
            log.error("Failed to get space content: %s", str(e))
            raise
        return format_content_list_markdown(contents)

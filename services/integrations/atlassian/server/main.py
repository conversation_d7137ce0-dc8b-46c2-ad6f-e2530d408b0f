"""Main entry point for Atlassian integrations."""

import argparse
import logging
import os
import pathlib
import threading
from concurrent.futures import ThreadPoolExecutor

import grpc
import opentelemetry.instrumentation.grpc
import prometheus_client
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from opentelemetry import trace

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import GracefulSignalHandler
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.integrations.atlassian import atlassian_pb2_grpc, atlassian_pb2
from services.integrations.atlassian.server.atlassian_server import AtlassianServer
from services.integrations.atlassian.server.config import Config
from services.lib.grpc.auth.service_auth_interceptor import Service<PERSON>uthInterceptor
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.token_exchange.client import client as token_exchange_client

tracer = trace.get_tracer(__name__)


def _serve(
    config: Config,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=[],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)

    server = grpc.server(
        ThreadPoolExecutor(max_workers=10),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )

    # Reply to health check RPCs
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    atlassian_pb2_grpc.add_AtlassianServicer_to_server(
        AtlassianServer(config, ri_publisher),
        server,
    )
    service_names = (
        atlassian_pb2.DESCRIPTOR.services_by_name["Atlassian"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds_multi(
        [
            config
            for config in [config.server_mtls, config.central_server_mtls]
            if config is not None
        ]
    )
    if server_credentials:
        actual_port = server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        actual_port = server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", actual_port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    prometheus_client.start_http_server(9090)

    ri_publisher = RequestInsightPublisher.create_from_args(args)

    _serve(config, namespace, ri_publisher, handler.get_shutdown_event())


if __name__ == "__main__":
    main()

"""Authentication for the Atlassian server."""

import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Callable, <PERSON><PERSON>
from urllib.parse import urlencode

import requests
from dataclasses_json import dataclass_json
from google.rpc import code_pb2, status_pb2
from pydantic import SecretStr

from services.agents.tool import ToolAuthenticationError
from services.integrations.atlassian import atlassian_pb2
from services.integrations.atlassian.config import AtlassianBasicCredentials
from services.integrations.atlassian.server.config import AtlassianError
from services.lib.request_context.request_context import RequestContext
from services.settings import settings_pb2
from services.settings.client.client import SettingsClient

ATLASSIAN_AUTH_URL = "https://auth.atlassian.com/oauth/token"
ATLASSIAN_AUTHORIZE_URL = "https://auth.atlassian.com/authorize"
ATLASSIAN_ACCESSIBLE_RESOURCES_URL = (
    "https://api.atlassian.com/oauth/token/accessible-resources"
)
TIMEOUT = 60

log = logging.getLogger(__name__)


class AtlassianAuthError(AtlassianError, ToolAuthenticationError):
    """Base class for errors in Atlassian Auth Processor."""

    pass


@dataclass_json
@dataclass
class AtlassianOAuthCredentials:
    """Credentials for Atlassian with OAuth."""

    settings: settings_pb2.AtlassianOAuthSettings
    """The OAuth settings containing access and refresh tokens."""

    refresh_callback: Callable[[], settings_pb2.AtlassianOAuthSettings]
    """Callback to refresh the token if needed."""


# Valid atlassian credentials can be user-inputted basic credentials that work across all Atlassian products or OAuth credentials from a user signing into either Jira or Confluence
AtlassianCredentials = AtlassianBasicCredentials | AtlassianOAuthCredentials


class AtlassianAuthProcessor:
    def __init__(
        self,
        settings_client: SettingsClient,
        redirect_uri: str,
        client_secret_path: str,
        client_id_path: str,
    ):
        self.settings_client = settings_client
        self.redirect_uri = redirect_uri
        self.client_secret = SecretStr(Path(client_secret_path).read_text().strip())
        self.client_id = SecretStr(Path(client_id_path).read_text().strip())

    def _fetch_cloud_id(self, access_token: str) -> str:
        """Fetch the cloud ID from Atlassian API."""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
        }

        response = requests.get(
            ATLASSIAN_ACCESSIBLE_RESOURCES_URL, headers=headers, timeout=TIMEOUT
        )
        response.raise_for_status()

        resources = response.json()
        if not resources:
            raise AtlassianAuthError("No accessible Atlassian resources found")

        return resources[0]["id"]

    def _fetch_account_id(
        self,
        access_token: str,
        service_type: atlassian_pb2.AtlassianService.ValueType,
        cloud_id: str,
    ) -> str:
        """Fetch the account ID from Atlassian API."""
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
        }

        if service_type == atlassian_pb2.AtlassianService.CONFLUENCE:
            url = f"https://api.atlassian.com/ex/confluence/{cloud_id}/rest/api/user/current"
        elif service_type == atlassian_pb2.AtlassianService.JIRA:
            url = f"https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/myself"
        else:
            raise AtlassianAuthError("Invalid Atlassian service type")

        response = requests.get(url, headers=headers, timeout=TIMEOUT)
        response.raise_for_status()
        if response.status_code != 200:
            log.warning(
                "Failed to get account ID: %s:%s", response.status_code, response.json()
            )
            # return exception only for auth errors
            if response.status_code == 401 or response.status_code == 403:
                raise AtlassianAuthError(f"Failed to get account ID: {response.json()}")
            return ""
        result = response.json()
        return result.get("accountId", "")

    def _exchange_code_for_tokens(self, code: str) -> dict:
        """Exchange authorization code for access and refresh tokens."""
        auth_data = {
            "grant_type": "authorization_code",
            "client_id": self.client_id.get_secret_value(),
            "client_secret": self.client_secret.get_secret_value(),
            "code": code,
            "redirect_uri": self.redirect_uri,
        }

        response = requests.post(
            ATLASSIAN_AUTH_URL,
            json=auth_data,
            headers={"Content-Type": "application/json"},
            timeout=TIMEOUT,
        )
        response.raise_for_status()
        return response.json()

    def hydrate_atlassian_user_settings(
        self,
        code: str,
        request_context: RequestContext,
    ) -> atlassian_pb2.HydrateAtlassianUserSettingsResponse:
        """Hydrate the Atlassian user settings with OAuth tokens."""
        try:
            # Get current user settings
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )

            # Exchange code for tokens
            auth_response = self._exchange_code_for_tokens(code)

            # Get cloud ID using the access token
            cloud_id = self._fetch_cloud_id(auth_response["access_token"])

            # Determine service type from scopes
            scopes = auth_response.get("scope", "").split()
            settings = settings_response.settings

            if not settings.atlassian_user_settings:
                settings.atlassian_user_settings.CopyFrom(
                    settings_pb2.AtlassianUserSettings()
                )

            # Update appropriate service settings
            if any("confluence" in scope for scope in scopes):
                service_settings = settings.atlassian_user_settings.confluence_settings
                service_type = atlassian_pb2.AtlassianService.CONFLUENCE
            elif any("jira" in scope for scope in scopes):
                service_settings = settings.atlassian_user_settings.jira_settings
                service_type = atlassian_pb2.AtlassianService.JIRA
            else:
                raise AtlassianAuthError(
                    f"Unrecognized Atlassian service. Scopes: {scopes}"
                )

            # Get account ID - overriding existing account ID value if it exists
            try:
                account_id = self._fetch_account_id(
                    auth_response["access_token"],
                    service_type,
                    cloud_id,
                )
            except AtlassianAuthError as e:
                log.error("Failed to get account ID: %s", str(e))
                # If we see an auth error here, chances are the token is invalid.
                raise
            except Exception as e:
                log.error("Failed to get account ID: %s", str(e))
                # If we cannot get the account ID for other reasons (service down or something else), we will not update the account ID
                account_id = ""

            if account_id:
                settings.atlassian_user_settings.atlassian_account_id = account_id

            # Update settings with new tokens and cloud ID
            service_settings.CopyFrom(settings_pb2.AtlassianOAuthSettings())
            service_settings.access_token = auth_response["access_token"]
            service_settings.refresh_token = auth_response.get("refresh_token", "")
            service_settings.cloud_id = cloud_id

            # Save updated settings
            self.settings_client.update_user_settings(
                request_context=request_context,
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings,
                    expected_version=settings_response.version,
                ),
            )

            return atlassian_pb2.HydrateAtlassianUserSettingsResponse(
                atlassian_service=service_type
            )

        except Exception as e:
            log.error("Error in Atlassian authentication: %s", str(e))
            raise AtlassianAuthError(str(e)) from e

    def get_user_settings(
        self,
        request_context: RequestContext,
        service_type: atlassian_pb2.AtlassianService.ValueType,
    ) -> Tuple[
        settings_pb2.GetUserSettingsResponse, settings_pb2.AtlassianOAuthSettings
    ]:
        """Get the Atlassian user settings response and the specific service's settings."""
        try:
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )

        except Exception as e:
            log.error(
                "Error in getting Atlassian user settings from settings service: %s",
                str(e),
            )
            raise AtlassianAuthError(str(e)) from e

        settings = settings_response.settings
        if not settings.atlassian_user_settings:
            raise AtlassianAuthError("Atlassian user settings not found")

        if service_type == atlassian_pb2.AtlassianService.CONFLUENCE:
            service_settings = settings.atlassian_user_settings.confluence_settings
        elif service_type == atlassian_pb2.AtlassianService.JIRA:
            service_settings = settings.atlassian_user_settings.jira_settings
        else:
            raise AtlassianAuthError("Invalid Atlassian service type")

        return settings_response, service_settings

    def get_stored_user_credentials(
        self,
        request_context: RequestContext,
        service_type: atlassian_pb2.AtlassianService.ValueType,
    ) -> AtlassianOAuthCredentials:
        """Get the stored Atlassian credentials for a user.

        Returns:
            AtlassianOAuthCredentials containing the OAuth settings and refresh callback
        """
        try:
            _, service_settings = self.get_user_settings(request_context, service_type)

            def refresh_callback() -> settings_pb2.AtlassianOAuthSettings:
                return self.refresh_token(
                    service_type=service_type,
                    request_context=request_context,
                )

            return AtlassianOAuthCredentials(
                settings=service_settings,
                refresh_callback=refresh_callback,
            )

        except Exception as e:
            log.error("Error in getting Atlassian credentials: %s", str(e))
            raise AtlassianAuthError(str(e)) from e

    def refresh_token(
        self,
        service_type: atlassian_pb2.AtlassianService.ValueType,
        request_context: RequestContext,
    ) -> settings_pb2.AtlassianOAuthSettings:
        """Refresh the access token using a refresh token."""
        try:
            settings_response, service_settings = self.get_user_settings(
                request_context, service_type
            )
        except Exception as e:
            log.error("Error in getting Atlassian credentials: %s", str(e))
            raise AtlassianAuthError(str(e)) from e

        if not service_settings.refresh_token:
            raise AtlassianAuthError("No refresh token found")

        auth_data = {
            "grant_type": "refresh_token",
            "client_id": self.client_id.get_secret_value(),
            "client_secret": self.client_secret.get_secret_value(),
            "refresh_token": service_settings.refresh_token,
        }

        try:
            response = requests.post(
                ATLASSIAN_AUTH_URL,
                json=auth_data,
                headers={"Content-Type": "application/json"},
                timeout=TIMEOUT,
            )
            response.raise_for_status()
            auth_response = response.json()
        except requests.exceptions.RequestException as e:
            log.error("Failed to refresh Atlassian token: %s", str(e))
            raise AtlassianAuthError(
                f"Failed to refresh Atlassian token: {str(e)}"
            ) from e
        service_settings.access_token = auth_response["access_token"]
        service_settings.refresh_token = auth_response.get("refresh_token", "")

        try:
            # Save updated settings
            self.settings_client.update_user_settings(
                request_context=request_context,
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings_response.settings,
                    expected_version=settings_response.version,
                ),
            )
        except Exception as e:
            log.warning("Error in updating Atlassian credentials: %s", str(e))
            # Continue execution and return the new service_settings even if updating the settings service
            # fails since we have the new access token

        return service_settings

    def is_configured(
        self,
        request_context: RequestContext,
        service_type: atlassian_pb2.AtlassianService.ValueType,
    ) -> bool:
        """Check if the user has configured Atlassian credentials for the given service."""
        try:
            _, service_settings = self.get_user_settings(request_context, service_type)
            return bool(service_settings.access_token and service_settings.cloud_id)
        except Exception as e:
            log.error("Error in getting Atlassian credentials: %s", str(e))
            return False

    def get_oauth_url(
        self,
        service_type: atlassian_pb2.AtlassianService.ValueType,
        request_context: RequestContext,
    ) -> str:
        """Get the OAuth URL for a service."""
        scopes = ["offline_access"]
        if service_type == atlassian_pb2.AtlassianService.CONFLUENCE:
            scopes.extend(
                [
                    "read:confluence-space.summary",
                    "read:confluence-props",
                    "read:confluence-user",
                    "read:confluence-groups",
                    "read:confluence-content.all",
                    "read:confluence-content.summary",
                    "read:confluence-content.permission",
                    "search:confluence",
                    "write:confluence-content",
                    "write:confluence-space",
                    "write:confluence-props",
                ]
            )
        elif service_type == atlassian_pb2.AtlassianService.JIRA:
            scopes.extend(
                [
                    "read:jira-work",
                    "read:jira-user",
                    "manage:jira-project",
                    "write:jira-work",
                ]
            )
        else:
            raise AtlassianAuthError("Invalid Atlassian service type")

        params = {
            "audience": "api.atlassian.com",
            "client_id": self.client_id.get_secret_value(),
            "scope": " ".join(scopes),
            "redirect_uri": self.redirect_uri,
            "response_type": "code",
            "prompt": "consent",
        }

        return f"{ATLASSIAN_AUTHORIZE_URL}?{urlencode(params)}"

    def revoke_oauth_token(
        self,
        request_context: RequestContext,
        request: atlassian_pb2.RevokeOAuthTokenRequest,
    ) -> atlassian_pb2.RevokeOAuthTokenResponse:
        """Revoke the OAuth token for the current user. Atlassia does not provide an API to revoke tokens, so we just clear the user's settings."""
        try:
            settings_response, service_settings = self.get_user_settings(
                request_context, request.atlassian_service
            )
        except Exception as e:
            log.error("Error in getting Atlassian credentials: %s", str(e))
            return atlassian_pb2.RevokeOAuthTokenResponse(
                status=status_pb2.Status(  # type: ignore
                    code=code_pb2.INTERNAL,  # type: ignore
                    message=str(e),
                )
            )

        if not service_settings.access_token:
            return atlassian_pb2.RevokeOAuthTokenResponse(
                status=status_pb2.Status(  # type: ignore
                    code=code_pb2.ALREADY_EXISTS,  # type: ignore
                    message="No access token found",
                )
            )

        # Clear the settings for the given service
        service_settings.CopyFrom(settings_pb2.AtlassianOAuthSettings())

        try:
            self.settings_client.update_user_settings(
                request_context=request_context,
                request=settings_pb2.UpdateUserSettingsRequest(
                    settings=settings_response.settings,
                    expected_version=settings_response.version,
                ),
            )
        except Exception as e:
            log.error("Error in updating Atlassian credentials: %s", str(e))
            return atlassian_pb2.RevokeOAuthTokenResponse(
                status=status_pb2.Status(  # type: ignore
                    code=code_pb2.INTERNAL,  # type: ignore
                    message=str(e),
                )
            )

        return atlassian_pb2.RevokeOAuthTokenResponse(
            status=status_pb2.Status(  # type: ignore
                code=code_pb2.OK,  # type: ignore
                message="Successfully revoked Atlassian OAuth credentials. You may still need to remove the Augment app from your Atlassian account. ",
            )
        )

    def get_atlassian_account_id(
        self,
        request_context: RequestContext,
    ) -> atlassian_pb2.GetAtlassianAccountIdResponse:
        """Get the atlassian account id of the current user."""
        try:
            settings_response = self.settings_client.get_user_settings(
                request_context=request_context,
            )
        except Exception as e:
            log.error("Error in getting Atlassian settings: %s", str(e))
            raise

        # if atlassian hasn't been configured, return an empty response
        if not settings_response.settings.atlassian_user_settings:
            return atlassian_pb2.GetAtlassianAccountIdResponse()

        return atlassian_pb2.GetAtlassianAccountIdResponse(
            atlassian_account_id=settings_response.settings.atlassian_user_settings.atlassian_account_id
        )

"""Use Confluence REST API for content management and search."""

import logging
from typing import Callable
from dataclasses import dataclass
from datetime import datetime
from requests.auth import HTTP<PERSON>asicA<PERSON>, AuthBase
from typing import Any
from dataclasses_json import dataclass_json
from bs4 import BeautifulSoup

from services.integrations.lib.http_client import BaseHTTP<PERSON>lient, BearerAuth
from services.integrations.atlassian.server.config import (
    AtlassianError,
)
from services.integrations.atlassian.server.atlassian_auth_processor import (
    AtlassianCredentials,
    AtlassianOAuthCredentials,
)
from services.integrations.atlassian.config import AtlassianBasicCredentials

log = logging.getLogger(__name__)

OAUTH_API_BASE_URL = "https://api.atlassian.com/ex/confluence/"


@dataclass_json
@dataclass(slots=True, frozen=True)
class ConfluenceComment:
    """Represents a comment on a Confluence page."""

    id: str
    """The comment's unique identifier."""

    body: str
    """The comment's main content."""

    created_by: str
    """The display name of the user who created the comment."""

    created: datetime
    """Timestamp when the comment was created."""

    updated: datetime
    """Timestamp when the comment was last updated."""

    parent_id: str | None = None  # For nested comments
    """ID of the parent comment, if any."""


@dataclass_json
@dataclass(slots=True, frozen=True)
class ConfluenceLabel:
    """Represents a label on a Confluence page."""

    id: str
    label: str
    prefix: str


@dataclass_json
@dataclass(slots=True, frozen=True)
class ConfluenceVersion:
    """Represents a version of Confluence content."""

    number: int
    when: datetime
    by: str
    message: str | None = None


@dataclass_json
@dataclass(slots=True, frozen=True)
class ConfluenceContent:
    """A clean representation of Confluence content with flattened fields."""

    id: str
    """The numeric ID of the content."""

    type: str
    """Content type (page, blogpost, etc)."""

    title: str
    """The content title."""

    space_key: str
    """Key of the space containing this content."""

    body: str
    """The content body in storage format."""

    version: int
    """Current version number."""

    status: str
    """Current status (current, draft, etc)."""

    created_by: str
    """Display name of content creator."""

    created: datetime
    """Timestamp when content was created."""

    last_modified: datetime
    """Timestamp when content was last modified."""

    url: str
    """Web URL of the content."""

    parent_id: str | None
    """ID of the parent page, if any."""

    ancestors: list[dict[str, str]]
    """List of ancestor pages with their IDs and titles."""

    children: list[dict[str, str]]
    """List of child pages with their IDs and titles."""

    labels: list[ConfluenceLabel]
    """Labels attached to the content."""

    comments: list[ConfluenceComment]
    """Comments on the content."""

    def get_readable_content(self) -> list[str]:
        """Extract readable text content from the HTML/XML body.

        Returns:
            List of non-empty text lines with HTML/XML tags properly parsed and removed.
        """
        soup = BeautifulSoup(self.body, "html.parser")
        # Get text while preserving some structure
        text = soup.get_text(separator="\n", strip=True)
        return [line for line in text.splitlines() if line.strip()]

    @classmethod
    def from_api_response(cls, content: dict[str, Any]) -> "ConfluenceContent":
        """Convert Confluence API response to ConfluenceContent."""
        ancestors = [
            {"id": a["id"], "title": a["title"]} for a in content.get("ancestors", [])
        ]

        children = [
            {"id": c["id"], "title": c["title"]}
            for c in content.get("children", {}).get("page", {}).get("results", [])
        ]

        labels = [
            ConfluenceLabel(id=label["id"], label=label["name"], prefix=label["prefix"])
            for label in content.get("metadata", {})
            .get("labels", {})
            .get("results", [])
        ]

        # Get last modified time from version or history as fallback
        last_modified = None
        if content.get("version", {}).get("when"):
            last_modified = datetime.fromisoformat(
                content["version"]["when"].replace("Z", "+00:00")
            )
        elif content.get("history", {}).get("createdDate"):
            last_modified = datetime.fromisoformat(
                content["history"]["createdDate"].replace("Z", "+00:00")
            )
        else:
            # If no timestamp available, use current time as last resort
            last_modified = datetime.now()

        return cls(
            id=content["id"],
            type=content["type"],
            title=content["title"],
            space_key=content["space"]["key"],
            body=content["body"]["storage"]["value"],
            version=content.get("version", {}).get("number", 1),
            status=content.get("status", "current"),
            created_by=content["history"]["createdBy"]["displayName"],
            created=datetime.fromisoformat(
                content["history"]["createdDate"].replace("Z", "+00:00")
            ),
            last_modified=last_modified,
            url=content["_links"]["webui"],
            parent_id=content.get("ancestors", [{}])[-1].get("id")
            if content.get("ancestors")
            else None,
            ancestors=ancestors,
            children=children,
            labels=labels,
            comments=[],  # Comments are loaded separately
        )


class ConfluenceError(AtlassianError):
    """Raised when Confluence operations fail."""


class ConfluenceClient(BaseHTTPClient):
    """Wrapper for Confluence REST API operations."""

    def get_auth_configuration(
        self,
        credentials: AtlassianCredentials,
    ) -> tuple[AuthBase, str, Callable[[], AuthBase] | None]:
        """Get the authentication for the client and optional refresh callback.
        Args:
            credentials: The Atlassian credentials to use

        Returns:
            Tuple of (current auth object, base URL, optional refresh callback that returns new auth)
        """
        if isinstance(credentials, AtlassianBasicCredentials):
            return (
                HTTPBasicAuth(
                    credentials.username,
                    credentials.personal_api_token.get_secret_value(),
                ),
                f"{credentials.server_url}/wiki/rest/api/",
                None,
            )

        if isinstance(credentials, AtlassianOAuthCredentials):
            auth = BearerAuth(credentials.settings.access_token)
            base_url = f"{OAUTH_API_BASE_URL}{credentials.settings.cloud_id}/rest/api/"

            def refresh_auth() -> AuthBase:
                new_settings = credentials.refresh_callback()
                return BearerAuth(new_settings.access_token)

            return auth, base_url, refresh_auth

        raise AtlassianError("No valid credentials provided")

    def get_content(
        self,
        content_id: str,
        credentials: AtlassianCredentials,
        expand: list[str] = [
            "body.storage",
            "version",
            "space",
            "history",
            "_links",
            "ancestors",
            "children.page",
            "metadata.labels",
        ],
    ) -> ConfluenceContent:
        """Get Confluence content by ID with expanded information."""
        try:
            auth, base_url, refresh_callback = self.get_auth_configuration(credentials)
            response = self.make_request(
                f"content/{content_id}",
                method="GET",
                base_url=base_url,
                auth=auth,
                params={"expand": ",".join(expand)},
                token_refresh_callback=refresh_callback,
            )
            response.raise_for_status()
        except Exception as e:
            raise ConfluenceError(f"Failed to get content {content_id}: {e}") from e
        return ConfluenceContent.from_api_response(response.json())

    def search_content(
        self,
        cql: str,
        credentials: AtlassianCredentials,
        limit: int = 25,
        expand: list[str] = [
            "body.storage",
            "version",
            "space",
            "history",
            "_links",
            "ancestors",
            "children.page",
            "metadata.labels",
        ],
    ) -> list[ConfluenceContent]:
        """Search Confluence content using CQL."""
        try:
            auth, base_url, refresh_callback = self.get_auth_configuration(credentials)

            response = self.make_request(
                "content/search",
                method="GET",
                base_url=base_url,
                auth=auth,
                token_refresh_callback=refresh_callback,
                params={
                    "cql": cql,
                    "limit": limit,
                    "expand": ",".join(expand),
                },
            )
            response.raise_for_status()
        except Exception as e:
            raise ConfluenceError(f"Failed to search content: {e}") from e
        results = response.json()["results"]
        return [ConfluenceContent.from_api_response(r) for r in results]

    def get_space_content(
        self,
        space_key: str,
        credentials: AtlassianCredentials,
        content_type: str = "page",
        expand: list[str] = [
            "body.storage",
            "version",
            "space",
            "history",
            "_links",
            "ancestors",
            "children.page",
            "metadata.labels",
        ],
        limit: int = 25,
    ) -> list[ConfluenceContent]:
        """Get all content of a specific type in a space."""
        try:
            auth, base_url, refresh_callback = self.get_auth_configuration(credentials)

            response = self.make_request(
                "content",
                method="GET",
                base_url=base_url,
                auth=auth,
                token_refresh_callback=refresh_callback,
                params={
                    "spaceKey": space_key,
                    "type": content_type,
                    "expand": ",".join(expand),
                    "limit": limit,
                },
            )
            response.raise_for_status()
        except Exception as e:
            raise ConfluenceError(f"Failed to get space content: {e}") from e
        results = response.json()["results"]
        return [ConfluenceContent.from_api_response(r) for r in results]

    def get_content_comments(
        self,
        content_id: str,
        credentials: AtlassianCredentials,
        limit: int = 25,
    ) -> list[ConfluenceComment]:
        """Get comments for a specific piece of content."""
        try:
            auth, base_url, refresh_callback = self.get_auth_configuration(credentials)

            response = self.make_request(
                f"content/{content_id}/child/comment",
                method="GET",
                base_url=base_url,
                auth=auth,
                token_refresh_callback=refresh_callback,
                params={"limit": limit},
            )
            response.raise_for_status()
        except Exception as e:
            raise ConfluenceError(f"Failed to get comments: {e}") from e
        results = response.json()["results"]
        return [
            ConfluenceComment(
                id=c["id"],
                body=c["body"]["storage"]["value"],
                created_by=c["history"]["createdBy"]["displayName"],
                created=datetime.fromisoformat(
                    c["history"]["createdDate"].replace("Z", "+00:00")
                ),
                updated=datetime.fromisoformat(
                    c["version"]["when"].replace("Z", "+00:00")
                ),
                parent_id=c.get("ancestors", [{}])[-1].get("id")
                if c.get("ancestors")
                else None,
            )
            for c in results
        ]

    def get_content_history(
        self,
        content_id: str,
        credentials: AtlassianCredentials,
        limit: int = 25,
    ) -> list[ConfluenceVersion]:
        """Get version history for a specific piece of content."""
        try:
            auth, base_url, refresh_callback = self.get_auth_configuration(credentials)

            response = self.make_request(
                f"content/{content_id}/version",
                method="GET",
                base_url=base_url,
                auth=auth,
                token_refresh_callback=refresh_callback,
                params={"limit": limit},
            )
        except Exception as e:
            raise ConfluenceError(f"Failed to get version history: {e}") from e
        results = response.json()["results"]
        return [
            ConfluenceVersion(
                number=v["number"],
                when=datetime.fromisoformat(v["when"].replace("Z", "+00:00")),
                by=v["by"]["displayName"],
                message=v.get("message"),
            )
            for v in results
        ]

"""Tests for Atlassian Auth Processor"""

import pytest
from unittest.mock import Mock, patch, ANY
from pydantic import SecretS<PERSON>

from services.integrations.atlassian.server.atlassian_auth_processor import (
    AtlassianAuthProcessor,
    AtlassianAuthError,
)
from services.settings import settings_pb2
from services.integrations.atlassian import atlassian_pb2
from services.lib.request_context.request_context import RequestContext


@pytest.fixture
def mock_settings_client():
    client = Mock()
    # Create a proper settings response
    settings = settings_pb2.UserSettings(
        atlassian_user_settings=settings_pb2.AtlassianUserSettings()
    )
    settings_response = settings_pb2.GetUserSettingsResponse(
        settings=settings, version="test-version-1"
    )
    client.get_user_settings.return_value = settings_response
    return client


@pytest.fixture
def mock_request_context():
    context = Mock(spec=RequestContext)
    return context


@pytest.fixture
def atlassian_auth_processor(mock_settings_client):
    with patch("pathlib.Path.read_text") as mock_read_text:
        mock_read_text.return_value = (
            "test_secret\n"  # Simulate file content with newline
        )
        return AtlassianAuthProcessor(
            mock_settings_client,
            "https://example.com/atlassianCallback",
            "test_client_secret_path",
            "test_client_id_path",
        )


def test_hydrate_jira_user_settings(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test successful Jira OAuth flow."""
    # Mock the token exchange response
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "scope": "read:jira-user read:jira-work",
        }
        mock_post.return_value.raise_for_status = Mock()

        # Mock the cloud ID response
        with patch("requests.get") as mock_get:
            mock_get.return_value = Mock()
            mock_get.return_value.json.return_value = [
                {"id": "test-cloud-id", "name": "Test Cloud"}
            ]
            mock_get.return_value.raise_for_status = Mock()

            response = atlassian_auth_processor.hydrate_atlassian_user_settings(
                "test_code",
                mock_request_context,
            )

            # Verify the response
            assert response.atlassian_service == atlassian_pb2.AtlassianService.JIRA

            # Verify settings were updated correctly
            mock_settings_client.update_user_settings.assert_called_once()
            call_kwargs = mock_settings_client.update_user_settings.call_args[1]
            assert call_kwargs["request_context"] == mock_request_context
            update_request = call_kwargs["request"]
            assert update_request.expected_version == "test-version-1"
            assert (
                update_request.settings.atlassian_user_settings.jira_settings.access_token
                == "test_access_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.jira_settings.refresh_token
                == "test_refresh_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.jira_settings.cloud_id
                == "test-cloud-id"
            )


def test_hydrate_confluence_user_settings(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test successful Confluence OAuth flow."""
    # Mock the token exchange response
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.json.return_value = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "scope": "read:confluence-space.summary read:confluence-content.all",
        }
        mock_post.return_value.raise_for_status = Mock()

        # Mock the cloud ID response
        with patch("requests.get") as mock_get:
            mock_get.return_value = Mock()
            mock_get.return_value.json.return_value = [
                {"id": "test-cloud-id", "name": "Test Cloud"}
            ]
            mock_get.return_value.raise_for_status = Mock()

            response = atlassian_auth_processor.hydrate_atlassian_user_settings(
                "test_code",
                mock_request_context,
            )

            # Verify the response
            assert (
                response.atlassian_service == atlassian_pb2.AtlassianService.CONFLUENCE
            )

            # Verify settings were updated correctly
            mock_settings_client.update_user_settings.assert_called_once()
            call_kwargs = mock_settings_client.update_user_settings.call_args[1]
            assert call_kwargs["request_context"] == mock_request_context
            update_request = call_kwargs["request"]
            assert update_request.expected_version == "test-version-1"
            assert (
                update_request.settings.atlassian_user_settings.confluence_settings.access_token
                == "test_access_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.confluence_settings.refresh_token
                == "test_refresh_token"
            )
            assert (
                update_request.settings.atlassian_user_settings.confluence_settings.cloud_id
                == "test-cloud-id"
            )


def test_hydrate_settings_no_cloud_id(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test error handling when no cloud ID is available."""
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.json.return_value = {
            "access_token": "test_access_token",
            "scope": "read:jira-user",
        }
        mock_post.return_value.raise_for_status = Mock()

        with patch("requests.get") as mock_get:
            mock_get.return_value = Mock()
            mock_get.return_value.json.return_value = []  # Empty resources list
            mock_get.return_value.raise_for_status = Mock()

            with pytest.raises(AtlassianAuthError) as exc_info:
                atlassian_auth_processor.hydrate_atlassian_user_settings(
                    "test_code",
                    mock_request_context,
                )

            assert "No accessible Atlassian resources found" in str(exc_info.value)


def test_hydrate_settings_invalid_code(
    atlassian_auth_processor, mock_settings_client, mock_request_context
):
    """Test error handling with invalid authorization code."""
    with patch("requests.post") as mock_post:
        mock_post.return_value = Mock()
        mock_post.return_value.raise_for_status.side_effect = Exception("Invalid code")

        with pytest.raises(AtlassianAuthError):
            atlassian_auth_processor.hydrate_atlassian_user_settings(
                "invalid_code",
                mock_request_context,
            )

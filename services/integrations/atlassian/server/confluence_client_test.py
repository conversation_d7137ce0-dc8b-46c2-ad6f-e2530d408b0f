"""Unit tests for the Confluence client wrapper."""

import json
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from requests import Response
from pydantic import SecretStr


from services.integrations.atlassian.server.confluence_client_wrapper import (
    ConfluenceClient,
    ConfluenceContent,
    ConfluenceVersion,
    ConfluenceError,
)
from services.integrations.atlassian.config import AtlassianBasicCredentials


@pytest.fixture
def mock_credentials():
    """Create mock Atlassian credentials."""
    return AtlassianBasicCredentials(
        server_url="https://test.atlassian.net",
        username="test_user",
        personal_api_token=SecretStr("test_token"),
    )


@pytest.fixture
def mock_response():
    """Create a mock response object."""
    response = MagicMock(spec=Response)
    response.status_code = 200
    return response


@pytest.fixture
def client():
    """Create a Confluence client with mock credentials."""
    return ConfluenceClient()


def load_mock_data(name: str) -> dict:
    """Load mock response data."""
    mock_responses = {
        "search": [
            {
                "id": "459300",
                "type": "page",
                "title": "Overview",
                "space": {"key": "~71202056c453ea82e14739a526f1ad7a3a6bda"},
                "body": {"storage": {"value": "<p>Test content</p>"}},
                "version": {
                    "number": 1,
                    "when": "2025-02-06T19:28:22.888Z",
                },
                "status": "current",
                "history": {
                    "createdBy": {"displayName": "surbhi"},
                    "createdDate": "2025-02-06T19:28:22.888Z",
                },
                "_links": {"webui": "/spaces/test/overview"},
                "metadata": {"labels": {"results": []}},
            }
        ],
        "content": {
            "id": "459300",
            "type": "page",
            "title": "Overview",
            "space": {"key": "~71202056c453ea82e14739a526f1ad7a3a6bda"},
            "body": {"storage": {"value": "<p>Test content</p>"}},
            "version": {
                "number": 1,
                "when": "2025-02-06T19:28:22.888Z",
            },
            "status": "current",
            "history": {
                "createdBy": {"displayName": "surbhi"},
                "createdDate": "2025-02-06T19:28:22.888Z",
            },
            "_links": {"webui": "/spaces/test/overview"},
            "metadata": {"labels": {"results": []}},
        },
        "comments": {"results": []},
        "history": {
            "results": [
                {
                    "number": 1,
                    "when": "2025-02-06T19:28:22.888Z",
                    "by": {"displayName": "surbhi"},
                    "message": "",
                }
            ]
        },
        "space_content": {
            "results": [
                {
                    "id": "459300",
                    "type": "page",
                    "title": "Overview",
                    "space": {"key": "~71202056c453ea82e14739a526f1ad7a3a6bda"},
                    "body": {"storage": {"value": "<p>Test content</p>"}},
                    "version": {
                        "number": 1,
                        "when": "2025-02-06T19:28:22.888Z",
                    },
                    "status": "current",
                    "history": {
                        "createdBy": {"displayName": "surbhi"},
                        "createdDate": "2025-02-06T19:28:22.888Z",
                    },
                    "_links": {"webui": "/spaces/test/overview"},
                    "metadata": {"labels": {"results": []}},
                }
            ]
        },
    }
    return mock_responses[name]


def test_search_content(client, mock_response, mock_credentials):
    """Test searching for content."""
    mock_response.json.return_value = {"results": load_mock_data("search")}

    with patch.object(client, "make_request", return_value=mock_response):
        results = client.search_content(
            cql="type=page order by lastmodified desc",
            limit=5,
            credentials=mock_credentials,
        )

        assert len(results) == 1
        content = results[0]
        assert isinstance(content, ConfluenceContent)
        assert content.id == "459300"
        assert content.title == "Overview"
        assert content.created_by == "surbhi"
        assert isinstance(content.created, datetime)
        assert content.space_key == "~71202056c453ea82e14739a526f1ad7a3a6bda"


def test_get_content(client, mock_response, mock_credentials):
    """Test getting specific content."""
    mock_response.json.return_value = load_mock_data("content")

    with patch.object(client, "make_request", return_value=mock_response):
        content = client.get_content("459300", mock_credentials)

        assert isinstance(content, ConfluenceContent)
        assert content.id == "459300"
        assert content.title == "Overview"
        assert content.type == "page"
        assert content.status == "current"
        assert content.version == 1


def test_get_content_comments(client, mock_response, mock_credentials):
    """Test getting content comments."""
    mock_response.json.return_value = load_mock_data("comments")

    with patch.object(client, "make_request", return_value=mock_response):
        comments = client.get_content_comments("459300", mock_credentials)

        assert isinstance(comments, list)
        assert len(comments) == 0


def test_get_content_history(client, mock_response, mock_credentials):
    """Test getting content version history."""
    mock_response.json.return_value = load_mock_data("history")

    with patch.object(client, "make_request", return_value=mock_response):
        history = client.get_content_history("459300", mock_credentials)

        assert len(history) == 1
        version = history[0]
        assert isinstance(version, ConfluenceVersion)
        assert version.number == 1
        assert version.by == "surbhi"
        assert isinstance(version.when, datetime)


def test_get_space_content(client, mock_response, mock_credentials):
    """Test getting space content."""
    mock_response.json.return_value = load_mock_data("space_content")

    with patch.object(client, "make_request", return_value=mock_response):
        content = client.get_space_content(
            space_key="~71202056c453ea82e14739a526f1ad7a3a6bda",
            content_type="page",
            limit=5,
            credentials=mock_credentials,
        )

        assert len(content) == 1
        page = content[0]
        assert isinstance(page, ConfluenceContent)
        assert page.id == "459300"
        assert page.title == "Overview"
        assert page.space_key == "~71202056c453ea82e14739a526f1ad7a3a6bda"


def test_error_handling(client, mock_response, mock_credentials):
    """Test error handling."""
    # Test missing fields in response
    mock_response.json.return_value = {
        "results": [
            {
                "id": "459300",
                "type": "page",
                "title": "Overview",
                "space": {"key": "~71202056c453ea82e14739a526f1ad7a3a6bda"},
                # Intentionally missing some fields to test robustness
                "body": {"storage": {"value": "<p>Test content</p>"}},
                "version": {"number": 1},  # Missing 'when' field
                "status": "current",
                "history": {
                    "createdBy": {"displayName": "surbhi"},
                    "createdDate": "2025-02-06T19:28:22.888Z",
                },
                "_links": {"webui": "/spaces/test/overview"},
            }
        ]
    }

    with patch.object(client, "make_request", return_value=mock_response):
        # Should not raise an error, but handle missing fields gracefully
        results = client.search_content("test query", mock_credentials)
        assert len(results) == 1
        assert results[0].id == "459300"

    # Test actual API error
    mock_response.raise_for_status.side_effect = Exception("API Error")
    with patch.object(client, "make_request", return_value=mock_response):
        with pytest.raises(ConfluenceError):
            client.search_content("test query", mock_credentials)

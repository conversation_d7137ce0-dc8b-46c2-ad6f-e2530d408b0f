"""Atlassian client wrappers.

This module provides a wrapper for when we are calling any generic Atlassian REST API.
"""

from typing import Callable
from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AuthBase

from services.integrations.atlassian import atlassian_pb2
from services.integrations.lib.http_client import BaseHT<PERSON>Client, BearerAuth
from services.integrations.atlassian.server.config import (
    AtlassianError,
)
from services.integrations.atlassian.server.atlassian_auth_processor import (
    AtlassianCredentials,
    AtlassianOAuthCredentials,
)
from services.integrations.atlassian.config import AtlassianBasicCredentials


OAUTH_CONFLUENCE_API_BASE_URL = "https://api.atlassian.com/ex/confluence/"
OAUTH_JIRA_API_BASE_URL = "https://api.atlassian.com/ex/jira/"


class AtlassianClientWrapper(BaseHTTPClient):
    """Base class for Atlassian client wrappers."""

    def get_auth_configuration(
        self,
        credentials: AtlassianCredentials,
        service_type: atlassian_pb2.AtlassianService.ValueType,
    ) -> tuple[AuthBase, str, Callable[[], AuthBase] | None]:
        """Get the authentication for the client and optional refresh callback.
        Args:
            credentials: The Atlassian credentials to use

        Returns:
            Tuple of (current auth object, base URL, optional refresh callback that returns new auth)
        """
        if isinstance(credentials, AtlassianBasicCredentials):
            if service_type == atlassian_pb2.AtlassianService.CONFLUENCE:
                base_url = f"{credentials.server_url}/wiki/rest/api/"
            elif service_type == atlassian_pb2.AtlassianService.JIRA:
                base_url = f"{credentials.server_url}/rest/api/3/"
            else:
                raise AtlassianError("Invalid Atlassian service type")
            return (
                HTTPBasicAuth(
                    credentials.username,
                    credentials.personal_api_token.get_secret_value(),
                ),
                base_url,
                None,
            )

        if isinstance(credentials, AtlassianOAuthCredentials):
            auth = BearerAuth(credentials.settings.access_token)
            if service_type == atlassian_pb2.AtlassianService.CONFLUENCE:
                base_url = f"{OAUTH_CONFLUENCE_API_BASE_URL}{credentials.settings.cloud_id}/rest/api/"
            elif service_type == atlassian_pb2.AtlassianService.JIRA:
                base_url = f"{OAUTH_JIRA_API_BASE_URL}{credentials.settings.cloud_id}/rest/api/3/"
            else:
                raise AtlassianError("Invalid Atlassian service type")

            def refresh_auth() -> AuthBase:
                new_settings = credentials.refresh_callback()
                return BearerAuth(new_settings.access_token)

            return auth, base_url, refresh_auth

        raise AtlassianError("No valid credentials provided")

    def call_api(
        self,
        api_path: str,
        credentials: AtlassianCredentials,
        service_type: atlassian_pb2.AtlassianService.ValueType,
        method: str = "GET",
        params: dict | None = None,
        request_body: dict | None = None,
    ) -> tuple[dict, int]:
        """Make a generic API call to the Confluence REST API.

        Args:
            api_path: The API path to call, relative to the base REST API URL
            credentials: The Atlassian credentials to use
            method: HTTP method to use (GET, POST, PUT)
            params: Query parameters to include in the request
            data: JSON data to include in the request body (for POST/PUT)

        Returns:
            The JSON response from the API as a dictionary
            The HTTP status code

        Raises:
            ConfluenceError: If the API call fails
        """
        try:
            auth, base_url, refresh_callback = self.get_auth_configuration(
                credentials, service_type
            )

            response = self.make_request(
                api_path,
                method=method,
                base_url=base_url,
                auth=auth,
                params=params,
                json=request_body,
                token_refresh_callback=refresh_callback,
            )
            response.raise_for_status()
            if response.text:
                return response.json(), response.status_code
            # Not all successful responses have text - for example PUT and POST requests often do not
            return {}, response.status_code
        except Exception as e:
            raise AtlassianError(f"Failed to call API: {e}") from e

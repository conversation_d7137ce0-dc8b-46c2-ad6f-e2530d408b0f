// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/integrations/atlassian/atlassian.proto (package atlassian, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type {
  BinaryReadOptions,
  FieldList,
  JsonReadOptions,
  JsonValue,
  PartialMessage,
  PlainMessage,
} from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum atlassian.AtlassianService
 */
export declare enum AtlassianService {
  /**
   * @generated from enum value: UNSPECIFIED_ATLASSIAN_SERVICE = 0;
   */
  UNSPECIFIED_ATLASSIAN_SERVICE = 0,

  /**
   * @generated from enum value: JIRA = 1;
   */
  JIRA = 1,

  /**
   * @generated from enum value: CONFLUENCE = 2;
   */
  CONFLUENCE = 2,
}

/**
 * @generated from message atlassian.AtlassianCredentials
 */
export declare class AtlassianCredentials extends Message<AtlassianCredentials> {
  /**
   * @generated from field: string server_url = 1;
   */
  serverUrl: string;

  /**
   * @generated from field: string personal_api_token = 2;
   */
  personalApiToken: string;

  /**
   * @generated from field: string username = 3;
   */
  username: string;

  constructor(data?: PartialMessage<AtlassianCredentials>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.AtlassianCredentials";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): AtlassianCredentials;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): AtlassianCredentials;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): AtlassianCredentials;

  static equals(
    a: AtlassianCredentials | PlainMessage<AtlassianCredentials> | undefined,
    b: AtlassianCredentials | PlainMessage<AtlassianCredentials> | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.SearchJiraIssuesRequest
 */
export declare class SearchJiraIssuesRequest extends Message<SearchJiraIssuesRequest> {
  /**
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * @generated from field: int32 max_results = 2;
   */
  maxResults: number;

  /**
   * @generated from field: optional atlassian.AtlassianCredentials credentials = 3;
   */
  credentials?: AtlassianCredentials;

  constructor(data?: PartialMessage<SearchJiraIssuesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.SearchJiraIssuesRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): SearchJiraIssuesRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): SearchJiraIssuesRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): SearchJiraIssuesRequest;

  static equals(
    a:
      | SearchJiraIssuesRequest
      | PlainMessage<SearchJiraIssuesRequest>
      | undefined,
    b:
      | SearchJiraIssuesRequest
      | PlainMessage<SearchJiraIssuesRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.SearchJiraIssuesResponse
 */
export declare class SearchJiraIssuesResponse extends Message<SearchJiraIssuesResponse> {
  /**
   * @generated from field: string issues_markdown = 1;
   */
  issuesMarkdown: string;

  constructor(data?: PartialMessage<SearchJiraIssuesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.SearchJiraIssuesResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): SearchJiraIssuesResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): SearchJiraIssuesResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): SearchJiraIssuesResponse;

  static equals(
    a:
      | SearchJiraIssuesResponse
      | PlainMessage<SearchJiraIssuesResponse>
      | undefined,
    b:
      | SearchJiraIssuesResponse
      | PlainMessage<SearchJiraIssuesResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetJiraIssueRequest
 */
export declare class GetJiraIssueRequest extends Message<GetJiraIssueRequest> {
  /**
   * @generated from field: string issue_key = 1;
   */
  issueKey: string;

  /**
   * @generated from field: optional atlassian.AtlassianCredentials credentials = 2;
   */
  credentials?: AtlassianCredentials;

  constructor(data?: PartialMessage<GetJiraIssueRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetJiraIssueRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetJiraIssueRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetJiraIssueRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetJiraIssueRequest;

  static equals(
    a: GetJiraIssueRequest | PlainMessage<GetJiraIssueRequest> | undefined,
    b: GetJiraIssueRequest | PlainMessage<GetJiraIssueRequest> | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetJiraIssueResponse
 */
export declare class GetJiraIssueResponse extends Message<GetJiraIssueResponse> {
  /**
   * @generated from field: string issue_markdown = 1;
   */
  issueMarkdown: string;

  constructor(data?: PartialMessage<GetJiraIssueResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetJiraIssueResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetJiraIssueResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetJiraIssueResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetJiraIssueResponse;

  static equals(
    a: GetJiraIssueResponse | PlainMessage<GetJiraIssueResponse> | undefined,
    b: GetJiraIssueResponse | PlainMessage<GetJiraIssueResponse> | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetJiraProjectStructureRequest
 */
export declare class GetJiraProjectStructureRequest extends Message<GetJiraProjectStructureRequest> {
  /**
   * @generated from field: string project_key = 1;
   */
  projectKey: string;

  /**
   * @generated from field: optional atlassian.AtlassianCredentials credentials = 2;
   */
  credentials?: AtlassianCredentials;

  constructor(data?: PartialMessage<GetJiraProjectStructureRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetJiraProjectStructureRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetJiraProjectStructureRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetJiraProjectStructureRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetJiraProjectStructureRequest;

  static equals(
    a:
      | GetJiraProjectStructureRequest
      | PlainMessage<GetJiraProjectStructureRequest>
      | undefined,
    b:
      | GetJiraProjectStructureRequest
      | PlainMessage<GetJiraProjectStructureRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetJiraProjectStructureResponse
 */
export declare class GetJiraProjectStructureResponse extends Message<GetJiraProjectStructureResponse> {
  /**
   * @generated from field: string project_structure_markdown = 1;
   */
  projectStructureMarkdown: string;

  constructor(data?: PartialMessage<GetJiraProjectStructureResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetJiraProjectStructureResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetJiraProjectStructureResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetJiraProjectStructureResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetJiraProjectStructureResponse;

  static equals(
    a:
      | GetJiraProjectStructureResponse
      | PlainMessage<GetJiraProjectStructureResponse>
      | undefined,
    b:
      | GetJiraProjectStructureResponse
      | PlainMessage<GetJiraProjectStructureResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.SearchConfluenceContentRequest
 */
export declare class SearchConfluenceContentRequest extends Message<SearchConfluenceContentRequest> {
  /**
   * @generated from field: string query = 1;
   */
  query: string;

  /**
   * @generated from field: int32 max_results = 2;
   */
  maxResults: number;

  /**
   * @generated from field: optional atlassian.AtlassianCredentials credentials = 3;
   */
  credentials?: AtlassianCredentials;

  constructor(data?: PartialMessage<SearchConfluenceContentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.SearchConfluenceContentRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): SearchConfluenceContentRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): SearchConfluenceContentRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): SearchConfluenceContentRequest;

  static equals(
    a:
      | SearchConfluenceContentRequest
      | PlainMessage<SearchConfluenceContentRequest>
      | undefined,
    b:
      | SearchConfluenceContentRequest
      | PlainMessage<SearchConfluenceContentRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.SearchConfluenceContentResponse
 */
export declare class SearchConfluenceContentResponse extends Message<SearchConfluenceContentResponse> {
  /**
   * @generated from field: string content_list_markdown = 1;
   */
  contentListMarkdown: string;

  constructor(data?: PartialMessage<SearchConfluenceContentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.SearchConfluenceContentResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): SearchConfluenceContentResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): SearchConfluenceContentResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): SearchConfluenceContentResponse;

  static equals(
    a:
      | SearchConfluenceContentResponse
      | PlainMessage<SearchConfluenceContentResponse>
      | undefined,
    b:
      | SearchConfluenceContentResponse
      | PlainMessage<SearchConfluenceContentResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetConfluenceContentRequest
 */
export declare class GetConfluenceContentRequest extends Message<GetConfluenceContentRequest> {
  /**
   * @generated from field: string content_id = 1;
   */
  contentId: string;

  /**
   * @generated from field: optional atlassian.AtlassianCredentials credentials = 2;
   */
  credentials?: AtlassianCredentials;

  constructor(data?: PartialMessage<GetConfluenceContentRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetConfluenceContentRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetConfluenceContentRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceContentRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceContentRequest;

  static equals(
    a:
      | GetConfluenceContentRequest
      | PlainMessage<GetConfluenceContentRequest>
      | undefined,
    b:
      | GetConfluenceContentRequest
      | PlainMessage<GetConfluenceContentRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetConfluenceContentResponse
 */
export declare class GetConfluenceContentResponse extends Message<GetConfluenceContentResponse> {
  /**
   * @generated from field: string content_markdown = 1;
   */
  contentMarkdown: string;

  constructor(data?: PartialMessage<GetConfluenceContentResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetConfluenceContentResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetConfluenceContentResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceContentResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceContentResponse;

  static equals(
    a:
      | GetConfluenceContentResponse
      | PlainMessage<GetConfluenceContentResponse>
      | undefined,
    b:
      | GetConfluenceContentResponse
      | PlainMessage<GetConfluenceContentResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetConfluenceSpaceRequest
 */
export declare class GetConfluenceSpaceRequest extends Message<GetConfluenceSpaceRequest> {
  /**
   * @generated from field: string space_key = 1;
   */
  spaceKey: string;

  /**
   * @generated from field: optional atlassian.AtlassianCredentials credentials = 2;
   */
  credentials?: AtlassianCredentials;

  /**
   * @generated from field: string content_type = 3;
   */
  contentType: string;

  /**
   * @generated from field: int32 max_results = 4;
   */
  maxResults: number;

  constructor(data?: PartialMessage<GetConfluenceSpaceRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetConfluenceSpaceRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetConfluenceSpaceRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceSpaceRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceSpaceRequest;

  static equals(
    a:
      | GetConfluenceSpaceRequest
      | PlainMessage<GetConfluenceSpaceRequest>
      | undefined,
    b:
      | GetConfluenceSpaceRequest
      | PlainMessage<GetConfluenceSpaceRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.GetConfluenceSpaceResponse
 */
export declare class GetConfluenceSpaceResponse extends Message<GetConfluenceSpaceResponse> {
  /**
   * @generated from field: string space_content_markdown = 1;
   */
  spaceContentMarkdown: string;

  constructor(data?: PartialMessage<GetConfluenceSpaceResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.GetConfluenceSpaceResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): GetConfluenceSpaceResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceSpaceResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): GetConfluenceSpaceResponse;

  static equals(
    a:
      | GetConfluenceSpaceResponse
      | PlainMessage<GetConfluenceSpaceResponse>
      | undefined,
    b:
      | GetConfluenceSpaceResponse
      | PlainMessage<GetConfluenceSpaceResponse>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.HydrateAtlassianUserSettingsRequest
 */
export declare class HydrateAtlassianUserSettingsRequest extends Message<HydrateAtlassianUserSettingsRequest> {
  /**
   * @generated from field: string code = 1;
   */
  code: string;

  constructor(data?: PartialMessage<HydrateAtlassianUserSettingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.HydrateAtlassianUserSettingsRequest";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HydrateAtlassianUserSettingsRequest;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HydrateAtlassianUserSettingsRequest;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HydrateAtlassianUserSettingsRequest;

  static equals(
    a:
      | HydrateAtlassianUserSettingsRequest
      | PlainMessage<HydrateAtlassianUserSettingsRequest>
      | undefined,
    b:
      | HydrateAtlassianUserSettingsRequest
      | PlainMessage<HydrateAtlassianUserSettingsRequest>
      | undefined,
  ): boolean;
}

/**
 * @generated from message atlassian.HydrateAtlassianUserSettingsResponse
 */
export declare class HydrateAtlassianUserSettingsResponse extends Message<HydrateAtlassianUserSettingsResponse> {
  /**
   * @generated from field: atlassian.AtlassianService atlassian_service = 1;
   */
  atlassianService: AtlassianService;

  constructor(data?: PartialMessage<HydrateAtlassianUserSettingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "atlassian.HydrateAtlassianUserSettingsResponse";
  static readonly fields: FieldList;

  static fromBinary(
    bytes: Uint8Array,
    options?: Partial<BinaryReadOptions>,
  ): HydrateAtlassianUserSettingsResponse;

  static fromJson(
    jsonValue: JsonValue,
    options?: Partial<JsonReadOptions>,
  ): HydrateAtlassianUserSettingsResponse;

  static fromJsonString(
    jsonString: string,
    options?: Partial<JsonReadOptions>,
  ): HydrateAtlassianUserSettingsResponse;

  static equals(
    a:
      | HydrateAtlassianUserSettingsResponse
      | PlainMessage<HydrateAtlassianUserSettingsResponse>
      | undefined,
    b:
      | HydrateAtlassianUserSettingsResponse
      | PlainMessage<HydrateAtlassianUserSettingsResponse>
      | undefined,
  ): boolean;
}

"""A Python client library for the Atlassian server service."""

import logging
from typing import Optional

import grpc

from base.python.grpc import client_options
from services.integrations.atlassian import atlassian_pb2, atlassian_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> atlassian_pb2_grpc.AtlassianStub:
    """Setup the client stub for the Atlassian service.

    Args:
        endpoint: The endpoint of the Atlassian service.
        credentials: The credentials to use for the channel (optional)
        options: Additional gRPC channel options (optional)

    Returns:
        The client stub for the Atlassian service.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = atlassian_pb2_grpc.AtlassianStub(channel)
    return stub


class AtlassianClient:
    """Client for interacting with the Atlassian service."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        self.stub = setup_stub(endpoint, credentials, options=options)

    def search_jira_issues(
        self,
        query: str,
        request_context: RequestContext,
        max_results: int = 0,
        credentials: atlassian_pb2.AtlassianBasicCredentials | None = None,
        timeout: float = 30,
    ) -> str:
        """Search for Jira issues using JQL.

        Args:
            query: JQL search query
            request_context: Request context for tracking and metrics
            max_results: Maximum number of results to return
            credentials: Optional credentials to use instead of looking them up in the settings service
            timeout: The timeout in seconds.

        Returns:
            Markdown formatted search results
        """
        request = atlassian_pb2.SearchJiraIssuesRequest(
            query=query, max_results=max_results
        )
        if credentials is not None:
            request.credentials.CopyFrom(credentials)

        response = self.stub.SearchJiraIssues(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.issues_markdown

    def get_jira_issue(
        self,
        issue_key: str,
        request_context: RequestContext,
        credentials: atlassian_pb2.AtlassianBasicCredentials | None = None,
        timeout: float = 30,
    ) -> str:
        """Get detailed information about a single Jira issue by its key.

        Args:
            issue_key: The Jira issue key (e.g. PROJ-123)
            request_context: Request context for tracking and metrics
            credentials: Optional credentials to use instead of looking them up in the settings service
            timeout: The timeout in seconds.

        Returns:
            Markdown formatted issue details
        """
        request = atlassian_pb2.GetJiraIssueRequest(issue_key=issue_key)
        if credentials is not None:
            request.credentials.CopyFrom(credentials)

        response = self.stub.GetJiraIssue(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.issue_markdown

    def get_jira_project_structure(
        self,
        project_key: str,
        request_context: RequestContext,
        credentials: atlassian_pb2.AtlassianBasicCredentials | None = None,
        timeout: float = 30,
    ) -> str:
        """Get the structure of Jira projects. Returns information about boards, sprints, epics, and components.

        Args:
            project_key: The Jira project key (e.g. PROJ)
            request_context: Request context for tracking and metrics
            credentials: Optional credentials to use instead of looking them up in the settings service
            timeout: The timeout in seconds.

        Returns:
            Markdown formatted project structure
        """
        request = atlassian_pb2.GetJiraProjectStructureRequest(project_key=project_key)
        if credentials is not None:
            request.credentials.CopyFrom(credentials)

        response = self.stub.GetJiraProjectStructure(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.project_structure_markdown

    def search_confluence_content(
        self,
        query: str,
        request_context: RequestContext,
        max_results: int = 0,
        credentials: atlassian_pb2.AtlassianBasicCredentials | None = None,
        timeout: float = 30,
    ) -> str:
        """Search for Confluence content using CQL.

        Args:
            query: CQL search query
            request_context: Request context for tracking and metrics
            max_results: Maximum number of results to return
            credentials: Optional credentials to use instead of looking them up in the settings service
            timeout: The timeout in seconds.

        Returns:
            Markdown formatted search results
        """
        request = atlassian_pb2.SearchConfluenceContentRequest(
            query=query, max_results=max_results
        )
        if credentials is not None:
            request.credentials.CopyFrom(credentials)

        response = self.stub.SearchConfluenceContent(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.content_list_markdown

    def get_confluence_content(
        self,
        content_id: str,
        request_context: RequestContext,
        credentials: atlassian_pb2.AtlassianBasicCredentials | None = None,
        timeout: float = 30,
    ) -> str:
        """Get detailed information about a single Confluence content by its ID.

        Args:
            content_id: The Confluence content ID
            request_context: Request context for tracking and metrics
            credentials: Optional credentials to use instead of looking them up in the settings service
            timeout: The timeout in seconds.

        Returns:
            Markdown formatted content details
        """
        request = atlassian_pb2.GetConfluenceContentRequest(content_id=content_id)
        if credentials is not None:
            request.credentials.CopyFrom(credentials)

        response = self.stub.GetConfluenceContent(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.content_markdown

    def get_confluence_space(
        self,
        space_key: str,
        request_context: RequestContext,
        credentials: atlassian_pb2.AtlassianBasicCredentials | None = None,
        content_type: str = "",
        max_results: int = 0,
        timeout: float = 30,
    ) -> str:
        """Get the content of a Confluence space.

        Args:
            space_key: The Confluence space key (e.g. DEV)
            request_context: Request context for tracking and metrics
            credentials: Optional credentials to use instead of looking them up in the settings service
            content_type: The type of content to retrieve (e.g. page, blog, label)
            max_results: Maximum number of results to return
            timeout: The timeout in seconds.

        Returns:
            Markdown formatted space content
        """
        request = atlassian_pb2.GetConfluenceSpaceRequest(
            space_key=space_key, content_type=content_type, max_results=max_results
        )
        if credentials is not None:
            request.credentials.CopyFrom(credentials)

        response = self.stub.GetConfluenceSpace(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.space_content_markdown

    def get_oauth_url(
        self,
        atlassian_service: atlassian_pb2.AtlassianService.ValueType,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> str:
        """Get the OAuth URL for a service.

        Args:
            atlassian_service: The Atlassian service to get the OAuth URL for
            request_context: Request context for tracking and metrics
            timeout: The timeout in seconds.

        Returns:
            The OAuth URL for the service
        """
        request = atlassian_pb2.GetOAuthUrlRequest(atlassian_service=atlassian_service)

        response = self.stub.GetOAuthUrl(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.oauth_url

    def is_configured(
        self,
        atlassian_service: atlassian_pb2.AtlassianService.ValueType,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> bool:
        """Check if the user has configured Atlassian credentials for the given service.

        Args:
            atlassian_service: The Atlassian service to check
            request_context: Request context for tracking and metrics
            timeout: The timeout in seconds.

        Returns:
            True if credentials are configured, False otherwise
        """
        request = atlassian_pb2.IsConfiguredRequest(atlassian_service=atlassian_service)

        response = self.stub.IsConfigured(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.is_configured

    def call_atlassian_api(
        self,
        request_context: RequestContext,
        request: atlassian_pb2.AtlassianApiRequest,
        timeout: float = 30,
    ) -> atlassian_pb2.AtlassianApiResponse:
        """Call the Atlassian API with the given request.

        Args:
            request_context: Request context for tracking and metrics
            request: The request to send
            timeout: The timeout in seconds.

        Returns:
            The response from the API
        """
        response = self.stub.CallAtlassianApi(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def revoke_oauth_token(
        self,
        atlassian_service: atlassian_pb2.AtlassianService.ValueType,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> atlassian_pb2.RevokeOAuthTokenResponse:
        """Revoke the OAuth token for the current user.
        Atlassian does not provide an API to revoke tokens, so we just clear the user's settings.

        Args:
            atlassian_service: The Atlassian service to revoke
            request_context: Request context for tracking and metrics
            timeout: The timeout in seconds.

        Returns:
            The response indicating success or failure of the revocation.
        """
        request = atlassian_pb2.RevokeOAuthTokenRequest(
            atlassian_service=atlassian_service
        )
        response = self.stub.RevokeOAuthToken(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response

    def get_atlassian_account_id(
        self,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> str:
        """Get the atlassian account id of the current user.

        Args:
            request_context: Request context for tracking and metrics
            timeout: The timeout in seconds.

        Returns:
            The atlassian account id of the current user.
        """
        request = atlassian_pb2.GetAtlassianAccountIdRequest()
        response = self.stub.GetAtlassianAccountId(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.atlassian_account_id

syntax = "proto3";

package atlassian;

import "google/rpc/status.proto";

service Atlassian {
  // Search for Jira issues using a JQL search query
  rpc SearchJiraIssues(SearchJiraIssuesRequest) returns (SearchJiraIssuesResponse);

  // Get detailed information about a single Jira issue by its key
  rpc GetJiraIssue(GetJiraIssueRequest) returns (GetJiraIssueResponse);

  // Get the structure of Jira projects. Returns information about boards, sprints, epics, and components.
  rpc GetJiraProjectStructure(GetJiraProjectStructureRequest) returns (GetJiraProjectStructureResponse);

  // Search for Confluence content using CQL
  rpc SearchConfluenceContent(SearchConfluenceContentRequest) returns (SearchConfluenceContentResponse);

  // Get detailed information about a single Confluence content by its ID
  rpc GetConfluenceContent(GetConfluenceContentRequest) returns (GetConfluenceContentResponse);

  // Get the content of a Confluence space
  rpc GetConfluenceSpace(GetConfluenceSpaceRequest) returns (GetConfluenceSpaceResponse);

  // Hydrate the Atlassian user settings with credentials
  rpc HydrateAtlassianUserSettings(HydrateAtlassianUserSettingsRequest) returns (HydrateAtlassianUserSettingsResponse);

  // Check if Atlassian credentials for a service are configured in user settings
  rpc IsConfigured(IsConfiguredRequest) returns (IsConfiguredResponse);

  // Get the OAuth URL for a service
  rpc GetOAuthUrl(GetOAuthUrlRequest) returns (GetOAuthUrlResponse);

  // Make an Atlassian API Request
  rpc CallAtlassianApi(AtlassianApiRequest) returns (AtlassianApiResponse);

  // Revoke the OAuth token for the current user
  rpc RevokeOAuthToken(RevokeOAuthTokenRequest) returns (RevokeOAuthTokenResponse);

  // Get the atlassian account id of the current user
  rpc GetAtlassianAccountId(GetAtlassianAccountIdRequest) returns (GetAtlassianAccountIdResponse);
}

// Credentials for accessing Atlassian with a personal API token passed in
// Oauth credentials follow a different pattern (AtlassianOAuthSettings in settings.proto)
// Basic Credentials that can be created manually by a user and passed in
message AtlassianBasicCredentials {
  // The URL of the Atlassian server. For example https://augmentcode.atlassian.net
  string server_url = 1 [debug_redact = true];

  // The API token for the Atlassian account
  string personal_api_token = 2 [debug_redact = true];

  // The username for the Atlassian account.  This is an email address.
  string username = 3 [debug_redact = true];
}

message SearchJiraIssuesRequest {
  // JQL search query or text to search for
  string query = 1 [debug_redact = true];

  // Maximum number of results to return
  int32 max_results = 2;

  // Optional credentials to use instead of looking them up in the settings service
  optional AtlassianBasicCredentials credentials = 3 [debug_redact = true];
}

message SearchJiraIssuesResponse {
  // Returns a markdown-formatted list of issues, where each issue is formatted as:
  // - [Issue Key] Issue Summary
  //   Type: Issue Type | Status: Issue Status
  //   Description: Issue Description (if available)
  string issues_markdown = 1 [debug_redact = true];
}

message GetJiraIssueRequest {
  // The Jira issue key (e.g., 'PROJ-123')
  string issue_key = 1;

  // Optional credentials to use instead of looking them up in the settings service
  optional AtlassianBasicCredentials credentials = 2 [debug_redact = true];
}

message GetJiraIssueResponse {
  // Returns a markdown-formatted issue, where the issue is formatted as:
  // # [Issue Key] Issue Summary
  // ** Property: Property Value ** (for each property)
  // ## Description
  // Issue Description
  string issue_markdown = 1 [debug_redact = true];
}

message GetJiraProjectStructureRequest {
  // The Jira project key (e.g., 'PROJ')
  string project_key = 1;

  // Optional credentials to use instead of looking them up in the settings service
  optional AtlassianBasicCredentials credentials = 2 [debug_redact = true];
}

message GetJiraProjectStructureResponse {
  // Returns a markdown-formatted project structure, where the structure is formatted as:
  // # Project: Project Name (Project Key)
  // ## Boards
  // - Board Name (ID: Board ID, Type: Board Type)
  // ## Active Sprints
  // - Sprint Name (Sprint State)
  // ## Epics
  // - [Epic Key] Epic Summary - Epic Status
  // ## Teams/Components
  // - Component Name (Lead: Component Lead)
  string project_structure_markdown = 1 [debug_redact = true];
}

message SearchConfluenceContentRequest {
  // CQL query or text to search for. Examples: 'type=page AND space=DEV' or 'text~"search term"'
  string query = 1 [debug_redact = true];

  // Maximum number of results to return
  int32 max_results = 2;

  // Optional credentials to use instead of looking them up in the settings service
  optional AtlassianBasicCredentials credentials = 3 [debug_redact = true];
}

message SearchConfluenceContentResponse {
  // Returns a markdown-formatted list of content, where each content item is formatted as:
  // ## Content Title
  // - Space: Space Key
  // - Last Modified: Last Modified Date
  // - Created By: Creator Name
  // - [View in Confluence](Content URL)
  string content_list_markdown = 1 [debug_redact = true];
}

message GetConfluenceContentRequest {
  // The ID of the Confluence content to retrieve
  string content_id = 1;

  // Optional credentials to use instead of looking them up in the settings service
  optional AtlassianBasicCredentials credentials = 2 [debug_redact = true];
}

message GetConfluenceContentResponse {
  // Returns a markdown-formatted content, where the content is formatted as:
  // # Content Title
  // Parent Page ID: Parent Page ID
  // Space: Space Key
  // Last Modified: Last Modified Date
  // Created By: Creator Name
  // ## Content
  // Content Body
  // ## Comments
  // ### Comment by Comment Creator on Comment Date
  // Comment Body
  string content_markdown = 1 [debug_redact = true];
}

message GetConfluenceSpaceRequest {
  // The key of the Confluence space (e.g., 'DEV', 'PROD')
  string space_key = 1;

  // Optional credentials to use instead of looking them up in the settings service
  optional AtlassianBasicCredentials credentials = 2 [debug_redact = true];

  // The type of content to retrieve. Will default to 'page' if not specified.
  string content_type = 3;

  // Maximum number of results to return
  int32 max_results = 4;
}

message GetConfluenceSpaceResponse {
  // Returns a markdown-formatted list of content in the space, where each content item is formatted as:
  // ## Content Title
  // - Space: Space Key
  // - Last Modified: Last Modified Date
  // - Created By: Creator Name
  // - [View in Confluence](Content URL)
  string space_content_markdown = 1 [debug_redact = true];
}

message HydrateAtlassianUserSettingsRequest {
  // The code to exchange for an access token
  string code = 1 [debug_redact = true];
}

enum AtlassianService {
  UNSPECIFIED_ATLASSIAN_SERVICE = 0;
  JIRA = 1;
  CONFLUENCE = 2;
}

message HydrateAtlassianUserSettingsResponse {
  // The Atlassian service that the token is for
  AtlassianService atlassian_service = 1;
}

message IsConfiguredRequest {
  // The Atlassian service to check
  AtlassianService atlassian_service = 1;
}

message IsConfiguredResponse {
  // Whether the user has configured credentials for the service
  bool is_configured = 1;
}

message GetOAuthUrlRequest {
  // The Atlassian service to get the OAuth URL for
  AtlassianService atlassian_service = 1;
}

message GetOAuthUrlResponse {
  // The OAuth URL for the service
  string oauth_url = 1 [debug_redact = true];
}

enum HTTPMethod {
  GET = 0;
  POST = 1;
  PUT = 2;
}

message AtlassianApiRequest {
  // The Atlassian service to call
  AtlassianService atlassian_service = 1;

  // The API endpoint to call, relative to the base REST API URL
  string endpoint = 2 [debug_redact = true];

  // The HTTP method to use
  HTTPMethod method = 3;

  // The data passed in (serialized from JSON) - this is handled as query params for GET or JSON body for POST
  string data = 4 [debug_redact = true];

  // Optional credentials to use instead of looking them up in the settings service
  optional AtlassianBasicCredentials credentials = 5 [debug_redact = true];
}

message AtlassianApiResponse {
  // The response from the API
  string response = 1 [debug_redact = true];

  // The HTTP status code
  int32 status_code = 2;
}

message RevokeOAuthTokenRequest {
  // The Atlassian service to revoke the OAuth token for
  AtlassianService atlassian_service = 1;
}

message RevokeOAuthTokenResponse {
  google.rpc.Status status = 1;
}

message GetAtlassianAccountIdRequest {}

message GetAtlassianAccountIdResponse {
  string atlassian_account_id = 1;
}

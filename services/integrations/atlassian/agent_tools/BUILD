load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "config",
    srcs = ["config.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/agents:tool",
    ],
)

py_library(
    name = "atlassian_agent_tools",
    srcs = [
        "atlassian_tools.py",
        "confluence_tools.py",
        "jira_tools.py",
    ],
    visibility = [
        "//services/agents/server:__subpackages__",
        "//services/integrations/atlassian:__subpackages__",
        "//services/integrations/jupyter:__subpackages__",
    ],
    deps = [
        ":config",
        "//services/agents:agents_py_proto",
        "//services/agents:tool",
        "//services/integrations/atlassian:atlassian_py_proto",
        "//services/integrations/atlassian:config",
        "//services/integrations/atlassian/client:client_py",
        "//services/lib/request_context:request_context_py",
        "//third_party/proto:googleapis_rpc_code_py_proto",
        "//third_party/proto:googleapis_status_py_proto",
        requirement("pydantic"),
        requirement("PyYAML"),
    ],
)

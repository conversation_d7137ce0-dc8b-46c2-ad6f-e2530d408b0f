"""[TO BE DEPRECATED] Implement Confluence tools. Use ConfluenceTool in atlassian_tools.py instead.

Currently available tools:
- Search content: Find pages using CQL or text search
- Get content: Get detailed information about a single page
- Get space content: Get space structure and content
"""

import logging


from pydantic import BaseModel, Field

from services.agents import agents_pb2
from services.agents.tool import (
    EmptyExtraToolInput,
    ValidatedTool,
)
from services.integrations.atlassian import atlassian_pb2
from services.integrations.atlassian.client.client import AtlassianClient
from services.integrations.atlassian.config import AtlassianExtraToolInput
from services.lib.request_context.request_context import RequestContext

log = logging.getLogger(__name__)


class ConfluenceSearchInput(BaseModel):
    """Input for the Confluence search tool."""

    query: str = Field(
        ...,  # required field
        description="CQL query or text to search for. Examples: "
        "'type=page AND space=DEV' or 'text~\"search term\"'",
    )

    max_results: int = Field(
        default=25,
        ge=1,
        le=100,
        description="Maximum number of results to return",
    )


class ConfluenceContentInput(BaseModel):
    """Input for retrieving specific full content from Confluence."""

    content_id: str = Field(
        ...,
        description="The ID of the Confluence content to retrieve",
    )


class ConfluenceSpaceInput(BaseModel):
    """Input for listing contents of a Confluence space."""

    space_key: str = Field(
        ...,
        description="The key of the Confluence space (e.g., 'DEV', 'PROD')",
    )

    content_type: str = Field(
        default="page",
        description="Type of content to list (page, blogpost, etc.)",
    )

    max_results: int = Field(
        default=25,
        ge=1,
        le=100,
    )


class ConfluenceSearchTool(
    ValidatedTool[ConfluenceSearchInput, AtlassianExtraToolInput | EmptyExtraToolInput]
):
    """Tool for searching Confluence content."""

    id = agents_pb2.RemoteToolId.CONFLUENCE_SEARCH
    name = "confluence-search"
    description = """\
Search for Confluence content using CQL or text search.
Returns a list of pages with their titles, spaces, and metadata in markdown format."""

    input_model = ConfluenceSearchInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, client: AtlassianClient):
        self.client = client

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to check Jira availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def run_validated(
        self,
        validated_input: ConfluenceSearchInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Confluence search tool.

        Args:
            validated_input: ConfluenceSearchInput containing query and max_results
            extra_tool_input: Credentials for Confluence
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted search results
        """
        try:
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None
            return self.client.search_confluence_content(
                query=validated_input.query,
                request_context=request_context,
                max_results=validated_input.max_results,
                credentials=credentials,
            )
        except Exception as e:
            log.error("Confluence search failed: %s", str(e))
            raise

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to get Confluence OAuth URL: %s", e)
            return ""


class ConfluenceContentTool(
    ValidatedTool[ConfluenceContentInput, AtlassianExtraToolInput | EmptyExtraToolInput]
):
    """Tool for getting detailed information about Confluence content."""

    id = agents_pb2.RemoteToolId.CONFLUENCE_CONTENT
    name = "confluence-get-content"
    description = """\
Get detailed information about specific Confluence content by its ID.
Returns comprehensive content details including body, comments, and metadata in markdown format."""

    input_model = ConfluenceContentInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, client: AtlassianClient):
        self.client = client

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to check Jira availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def run_validated(
        self,
        validated_input: ConfluenceContentInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Confluence content tool.

        Args:
            validated_input: ConfluenceContentInput containing the content ID
            extra_tool_input: Credentials for Confluence
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted content details
        """
        try:
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None
            return self.client.get_confluence_content(
                validated_input.content_id,
                request_context=request_context,
                credentials=credentials,
            )
        except Exception as e:
            log.error("Failed to get Confluence content: %s", str(e))
            raise

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to get Confluence OAuth URL: %s", e)
            return ""


class ConfluenceSpaceTool(
    ValidatedTool[ConfluenceSpaceInput, AtlassianExtraToolInput | EmptyExtraToolInput]
):
    """Tool for getting content from a Confluence space."""

    id = agents_pb2.RemoteToolId.CONFLUENCE_SPACE
    name = "confluence-get-space"
    description = """\
Get content from a specific Confluence space.
Returns space structure including pages, labels, and hierarchies in markdown format."""

    input_model = ConfluenceSpaceInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, client: AtlassianClient):
        self.client = client

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to check Jira availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def run_validated(
        self,
        validated_input: ConfluenceSpaceInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Confluence space tool.

        Args:
            validated_input: ConfluenceSpaceInput containing space key and options
            extra_tool_input: Credentials for Confluence
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted space content
        """
        try:
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None
            return self.client.get_confluence_space(
                space_key=validated_input.space_key,
                request_context=request_context,
                content_type=validated_input.content_type,
                max_results=validated_input.max_results,
                credentials=credentials,
            )
        except Exception as e:
            log.error("Failed to get space content: %s", str(e))
            raise

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to get Confluence OAuth URL: %s", e)
            return ""

"""Configuration for Atlassian agent tools."""

# Common fields that should always be excluded from Confluence API responses
CONFLUENCE_EXCLUDED_FIELDS = {
    "extensions",
    "childTypes",
    "macroRenderedOutput",
    "restrictions",
    "operations",
    "schedulePublishInfo",
    "schedulePublishDate",
    "confRev",
    "contentTypeModified",
    "position",
    "tinyui",
    "profilePicture",
}

# Common fields that should always be excluded from Jira API responses
JIRA_EXCLUDED_FIELDS = {
    "self",
    "operations",
    "editmeta",
    "versionedRepresentations",
    "schema",
    "renderedFields",
    "avatarUrls",
}

# Fields to exclude for data privacy
# https://developer.atlassian.com/cloud/confluence/user-privacy-developer-guide/
PII_FIELDS = {"emailAddress", "username" "fullName", "avatar", "displayName"}

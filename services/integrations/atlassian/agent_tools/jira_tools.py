"""[TO BE DEPRECATED] Implement Jira tools. Use JiraTool in atlassian_tools.py instead.

Currently available tools:
- Search issues: Find issues using JQL or text search
- Get issue: Get detailed information about a single issue
- Get project structure: Get project structure including epics, sprints, and components
"""

import logging
from pydantic import BaseModel, Field

from services.agents import agents_pb2
from services.agents.tool import ValidatedTool, EmptyExtraToolInput
from services.integrations.atlassian import atlassian_pb2
from services.integrations.atlassian.client.client import AtlassianClient
from services.integrations.atlassian.config import (
    AtlassianExtraToolInput,
)
from services.lib.request_context.request_context import RequestContext

log = logging.getLogger(__name__)


class JiraSearchInput(BaseModel):
    """Input schema for the Jira search tool."""

    query: str = Field(
        ...,  # required field
        description="JQL query or text to search for",
    )

    max_results: int = Field(
        default=10,
        ge=1,
        le=50,
        description="Maximum number of results to return",
    )


class JiraIssueInput(BaseModel):
    """Input schema for the Jira issue tool."""

    issue_key: str = Field(
        ...,
        description="The Jira issue key (e.g., 'PROJ-123')",
    )


class JiraProjectInput(BaseModel):
    """Input schema for the Jira project structure tool."""

    project_key: str | None = Field(
        default=None,
        description="Optional: The Jira project key (e.g., 'PROJ'). If not provided, returns all projects.",
    )


class JiraSearchTool(
    ValidatedTool[JiraSearchInput, AtlassianExtraToolInput | EmptyExtraToolInput]
):
    """Tool for searching Jira issues."""

    id = agents_pb2.RemoteToolId.JIRA_SEARCH
    name = "jira-search"
    description = """\
Search for Jira issues using JQL or text search.
Returns a list of issues with their key, summary, type, and status in markdown format."""

    input_model = JiraSearchInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, client: AtlassianClient):
        self.client = client

    def run_validated(
        self,
        validated_input: JiraSearchInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Jira search tool.

        Args:
            validated_input: JiraSearchInput containing query and max_results
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted search results
        """
        try:
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None
            return self.client.search_jira_issues(
                validated_input.query,
                request_context=request_context,
                credentials=credentials,
                max_results=validated_input.max_results,
            )
        except Exception as e:
            log.error("Jira search failed: %s", str(e))
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to check Jira availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to get Jira OAuth URL: %s", e)
            return ""


class JiraIssueTool(
    ValidatedTool[JiraIssueInput, AtlassianExtraToolInput | EmptyExtraToolInput]
):
    """Tool for getting detailed information about a single Jira issue."""

    id = agents_pb2.RemoteToolId.JIRA_ISSUE
    name = "jira-issue"
    description = """\
Get detailed information about a single Jira issue by its key.
Returns comprehensive issue details in markdown format."""

    input_model = JiraIssueInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, client: AtlassianClient):
        self.client = client

    def run_validated(
        self,
        validated_input: JiraIssueInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Jira issue tool.

        Args:
            validated_input: JiraIssueInput containing the issue key
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted issue details
        """
        try:
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None
            return self.client.get_jira_issue(
                validated_input.issue_key,
                request_context=request_context,
                credentials=credentials,
            )
        except Exception as e:
            log.error("Failed to get Jira issue: %s", str(e))
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to check Jira availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to get Jira OAuth URL: %s", e)
            return ""


class JiraProjectTool(
    ValidatedTool[JiraProjectInput, AtlassianExtraToolInput | EmptyExtraToolInput]
):
    """Tool for getting project structure information."""

    id = agents_pb2.RemoteToolId.JIRA_PROJECT
    name = "jira-project"
    description = """\
Get the structure of Jira projects. Returns information about \
boards, sprints, epics, and components."""

    input_model = JiraProjectInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_SAFE

    def __init__(self, client: AtlassianClient):
        self.client = client

    def run_validated(
        self,
        validated_input: JiraProjectInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Run the Jira project structure tool.

        Args:
            validated_input: JiraProjectInput optionally containing a project key
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted project structure(s)
        """
        try:
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None
            return self.client.get_jira_project_structure(
                project_key=validated_input.project_key or "",
                request_context=request_context,
                credentials=credentials,
            )
        except Exception as e:
            log.error("Failed to get project structure: %s", str(e))
            raise

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to check Jira availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to get Jira OAuth URL: %s", e)
            return ""

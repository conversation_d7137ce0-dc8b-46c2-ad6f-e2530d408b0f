"""Implement flexible Atlassian API tools for Confluence and Jira.
There are the NEW Confluence and Jira tools that are more flexible and powerful.
The OLD tools in their respective files (jira_tools and confluence_tools) for backwards compatibility with old clients.
"""

import json
import logging
from typing import Any, Dict, Literal, Optional, TypeVar

import grpc
import yaml
from google.rpc import code_pb2, status_pb2
from pydantic import BaseModel, Field

from services.agents import agents_pb2
from services.agents.tool import (
    EmptyExtraToolInput,
    ToolAuthenticationError,
    ToolDefinition,
    ValidatedTool,
)
from services.integrations.atlassian import atlassian_pb2
from services.integrations.atlassian.agent_tools.config import (
    CONFLUENCE_EXCLUDED_FIELDS,
    JIRA_EXCLUDED_FIELDS,
    PII_FIELDS,
)
from services.integrations.atlassian.client.client import AtlassianClient
from services.integrations.atlassian.config import AtlassianExtraToolInput
from services.lib.request_context.request_context import RequestContext

log = logging.getLogger(__name__)
T = TypeVar("T")

METHOD_MAP = {
    "GET": atlassian_pb2.HTTPMethod.GET,
    "POST": atlassian_pb2.HTTPMethod.POST,
    "PUT": atlassian_pb2.HTTPMethod.PUT,
}


class ConfluenceInput(BaseModel):
    """Input for the Confluence tool."""

    summary: Optional[str] = Field(
        default=None,
        description="A short human-readable summary of what this API call will do. This helps users understand the purpose of the call.",
    )

    method: Literal["GET", "POST", "PUT"] = Field(
        default="GET",
        description="HTTP method to use. Usually GET, rarely POST to create or PUT to update.",
    )

    endpoint: str = Field(
        description="""The API endpoint path. Query parameters should be provided in the data field.
Example endpoints:
### Search Endpoint
- `/content/search`
- Search content using CQL (requires cql param with valid CQL query)
 - ex.  "cql": "creator = currentUser()" to get content created by the user

### Content Endpoints
- `/content/{id}` - Get content by ID
- `/content` - Get content list OR create new content (requires type, title, space, body). Get space key from /space endpoint before creating content.
- `/content/{id}/child` - Get all children of content
- `/content/{id}/child/comment` - Get comments on content (There is no way to POST comments, only GET)
- `/content/{id}` - Update content (requires version.number incremented)

### Space Endpoints
- `/space` - Get spaces
- `/space/{spaceKey}/content` - Get content in space
- `/space/{spaceKey}` - Update space

### User Endpoints
- `/user/memberof` - Get user group memberships. accountId is required
"""
    )

    data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="""Data to send - query params for GET or JSON body for POST/PUT. Use expand param as necessary for GET requests.
        When creating a completely new page, ALWAYS add an attribution at the end '\n\n---\nCo-authored by Augment Code` with this hyperlink `https://www.augmentcode.com/?utm_source=atlassian&utm_medium=confluence_page&utm_campaign=confluence`."
""",
    )


class ConfluenceTool(
    ValidatedTool[ConfluenceInput, AtlassianExtraToolInput | EmptyExtraToolInput]
):
    """Tool for making generic Confluence API calls."""

    id = agents_pb2.RemoteToolId.CONFLUENCE
    name = "confluence"
    description = """\
Make Confluence API calls with flexible endpoints.

Use this tool to access and manage content in Confluence, a team knowledge base for documentation, onboarding materials, project plans, technical specs, policies, and collaborative work.
Content is organized in spaces containing pages, blog posts, comments, and file attachments.

Check Confluence when answering questions about:
- Company processes, policies, or guidelines
- Project specifications, roadmaps, or requirements
- Team structures, responsibilities, or contacts
- Product features, designs, or architecture decisions
- Historical context or rationale for decisions
- Meeting notes, presentations, or shared resources

Proactively search Confluence, especially when information isn't found in the codebase. Use the /content/search endpoint with appropriate CQL queries.
"""

    input_model = ConfluenceInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_CHECK

    def __init__(self, client: AtlassianClient):
        self.client = client

    def get_tool_definition(
        self, request_context: RequestContext | None = None
    ) -> ToolDefinition:
        """Get the tool parameter for LLM with user info injected.

        Retrieves Confluence user info if the user is authenticated and injects it into the tool description
        to help the LLM make correct API calls with the user's account ID.

        Returns:
            ToolDefinition: Tool definition with user info in the description
        """
        # Start with the base description
        description = self.description

        if request_context:
            # Try to get user's info
            atlassian_account_id = None
            try:
                atlassian_account_id = self.client.get_atlassian_account_id(
                    request_context
                )
            except Exception as e:
                log.warning(f"Failed to get Confluence account id: {str(e)}")

            # If we have user info, add it to the description
            if atlassian_account_id:
                # Add user info to the description
                description += f" The current user's Confluence Account ID is {atlassian_account_id}"

        # Return the tool definition with the enhanced description
        return ToolDefinition(
            name=self.name,
            description=description,
            input_schema_json=json.dumps(self.input_schema),
        )

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to check Confluence availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def run_validated(
        self,
        validated_input: ConfluenceInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Execute Confluence API call with formatting.

        Args:
            validated_input: ConfluenceInput containing endpoint and parameters
            extra_tool_input: Credentials for Confluence
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted API response
        """
        try:
            # Get credentials from extra tool input
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None

            request = atlassian_pb2.AtlassianApiRequest(
                atlassian_service=atlassian_pb2.AtlassianService.CONFLUENCE,
                method=METHOD_MAP[validated_input.method],
                endpoint=validated_input.endpoint,
                data=json.dumps(validated_input.data),
            )
            if credentials:
                request.credentials.CopyFrom(credentials)

            resp = self.client.call_atlassian_api(request_context, request)

            if resp.status_code == 401:
                raise ToolAuthenticationError(resp.response)
            if not (200 <= resp.status_code < 300):
                raise Exception(
                    f"Confluence API call failed with status code {resp.status_code}: {resp.response}"
                )

            try:
                parsed_response = json.loads(resp.response)

                filtered_response = format_atlassian_response(
                    parsed_response,
                    excluded_fields=CONFLUENCE_EXCLUDED_FIELDS.union(PII_FIELDS),
                )

                return filtered_response
            except Exception as e:
                log.error("Failed to parse response: %s", str(e))
                # If all parsing attempts fail, return the response as-is
                log.warning("Failed to parse response, returning as-is")
                return resp.response

        except Exception as e:
            log.error("Confluence API call failed: %s", str(e))
            raise

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
        except Exception as e:
            log.error("Failed to get Confluence OAuth URL: %s", e)
            return ""

    def check_validated_input_safe(self, validated_input: ConfluenceInput) -> bool:
        """Check if the Confluence API call is safe using validated input.

        GET methods are considered safe, while POST/PUT methods require approval.

        Returns:
            bool: True if the API call is safe (GET method), False otherwise
        """
        return validated_input.method == "GET"

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:  # pylint: disable=no-member # type: ignore
        """
        Remove the token from settings.

        Args:
            request_context: The request context to use.

        Returns:
            Status code indicating the result of the operation.
        """
        try:
            response = self.client.revoke_oauth_token(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
            return response.status
        except Exception as e:
            log.error(
                "Failed to revoke Confluence OAuth token %s", str(e), exc_info=True
            )
            return status_pb2.Status(code=code_pb2.INTERNAL)  # pylint: disable=no-member # type: ignore

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # pylint: disable=no-member # type: ignore
        """
        Test the connection to Confluence by fetching the current user's information.

        Args:
            request_context: The request context to use.

        Returns:
            Status code indicating the result of the operation.
        """
        # Check the user's credentials
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.CONFLUENCE, request_context
            )
            if not is_oauth_configured:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.UNAUTHENTICATED,  # type: ignore
                    message="Confluence credentials are not configured. Please configure them in the settings page.",
                )

            # Test authenticated access by making a test call
            request = atlassian_pb2.AtlassianApiRequest(
                atlassian_service=atlassian_pb2.AtlassianService.CONFLUENCE,
                method=atlassian_pb2.HTTPMethod.GET,
                endpoint="/user",
            )
            result = self.client.call_atlassian_api(request_context, request)
            if 200 <= result.status_code < 300:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.OK,  # type: ignore
                    message="Confluence credentials are successfully configured.",
                )
            elif result.status_code == 401:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.UNAUTHENTICATED,  # type: ignore
                    message="Authentication failed. Please re-authenticate with Confluence.",
                )
            elif result.status_code == 503:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.UNAVAILABLE,  # type: ignore
                    message=f"Confluence API appears to be unavailable (HTTP {result.status_code}). Please check https://confluence.status.atlassian.com/ for status information.",
                )
            else:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.INTERNAL,  # type: ignore
                    message=f"Failed to check Confluence credentials (HTTP {result.status_code}).",
                )
        except Exception as e:
            log.error("Failed to check Jira credentials: %s", str(e), exc_info=True)
            return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                code=code_pb2.INTERNAL,  # type: ignore
                message=f"Failed to check Jira credentials: {str(e)}",
            )


class JiraInput(BaseModel):
    """Input for the flexible Jira API tool."""

    summary: Optional[str] = Field(
        default=None,
        description="A short human-readable summary of what this API call will do. This helps users understand the purpose of the call.",
    )

    method: Literal["GET", "POST", "PUT"] = Field(
        default="GET",
        description="HTTP method to use. Usually GET, rarely POST to create or PUT to update.",
    )

    endpoint: str = Field(
        description="""The API endpoint path. Query parameters should be provided in the data field.
Example endpoints:

### Search Endpoints
- `/search` - Search for issues using JQL
  - Required: "jql" parameter in data field
    - Ex. `"jql": "user = currentUser() AND project = PROJ AND status = Done"` to get all completed issues assigned to the current user in project PROJ
  - Optional: "maxResults", "startAt", "fields", etc.

### Issue Endpoints
- `/issue/{issueIdOrKey}` - Get or update issue by ID or key
  - Use this to assign tickets to users
  - ex. /issue/SOF-21
        "fields": {
            "assignee": {
            "accountId": "557058:a4e1c89b-14f3-4657-9fe3-a9c6e8a38893"
            }
        }
- `/issue/{issueIdOrKey}/comment` - Get or add comments for an issue
- `/issue` - Create a new issue (requires project, issuetype, summary)
    - Note that the `description`, `environment`, and any `textarea` type custom fields take Atlassian Document Format content. Single line custom fields (`textfield`) accept a string and don't handle Atlassian Document Format content.
    - Atlassian Document Format looks like:
        "content": [
            {
            "type": "paragraph",
            "content": [
                {
                "type": "text",
                "text": "Here is some text. Here is some "
                },
                {
                "type": "text",
                "text": "bold text.",
                "marks": [
                    {
                    "type": "strong"
                    }
                ]
                }
            ]
            }
        ]
    - When creating new issues, ALWAYS include the "Co-authored by Augment Code" attribution at the end.
      When using Atlassian Docuement Format, this looks like:
      "content": [
            {
            "type": "rule"
            },
            {
            "type": "paragraph",
            "content": [
                {
                "type": "text",
                "text": "Co-authored by"
                },
                {
                "type": "text",
                "text": " Augment Code",
                "marks": [
                    {
                    "type": "link",
                    "attrs": {
                        "href": "http://mylink.com"
                    }
                    }
                ]
                }
            ]

- `issue/createmeta/{projectIdOrKey}/issuetypes` - Returns the issue type metadata for a specied project. Call this before creating an issue if unsure what fields to fill in
- `/issue/{issueIdOrKey}/transitions` - Transition an issue. Use the get call first to see available transitions.
   - Use this endpoint to change fields such as status
    - ex. /issue/SOF-21/transitions
            "transition": {
                "id": "31"
            }

### Project Endpoints
- `/project` - Get all projects
- `/project/{projectIdOrKey}` - Get project by ID or key
- `/project/{projectIdOrKey}/components` - Get project components
- `/project/{projectIdOrKey}/versions` - Get project versions
- `/project/{projectIdOrKey}/statuses` - Get project statuses

### Board and Sprint Endpoints (Agile)
- `/board` - Get all boards
- `/board/{boardId}/issue` - Get issues for a board
- `/sprint/{sprintId}/issue` - Get issues for a sprint

### Other Endpoints
- `/issuetype` - Get all issue types
- `/priority` - Get all priorities
- `/status` - Get all statuses
- `/field` - Get all fields
""",
    )

    data: Optional[Dict[str, Any]] = Field(
        default=None,
        description="""Data to send - automatically handled as query params for GET or JSON body for POST/PUT.
        When creating new issues or leaving a comment, ALWAYS add an attribution at the end of the description/comment with '\n\n---\nCo-authored by Augment Code` and a hyperlink to `https://www.augmentcode.com/?utm_source=atlassian&utm_medium=jira_issue&utm_campaign=jira`. Medium in the UTM link should only ever be jira_issue or jira_comment"
""",
    )


class JiraTool(ValidatedTool[JiraInput, AtlassianExtraToolInput | EmptyExtraToolInput]):
    """Tool for making flexible Jira API calls."""

    id = agents_pb2.RemoteToolId.JIRA
    name = "jira"
    description = """\
Make Jira API calls with flexible endpoints.

Use this tool to access and manage Jira issues, projects, and workflows. Jira is used for tracking tasks, bugs, features, and work items across teams.
Common operations include searching for issues, creating or updating tickets, and transitioning workflow states.

Check Jira when answering questions about:
- Current work items, tasks, or bugs being tracked
- Project progress, backlogs, or sprint status
- Issue assignments, priorities, or deadlines
- Workflow processes or ticket transitions

Note there are other sources of tickets/issues like Linear and Github. If the user
explicitly mentions another source like Linear or Github do NOT use this tool.

Use the `/search` endpoint with JQL queries to find relevant issues."""

    input_model = JiraInput
    strict = True
    tool_safety = agents_pb2.ToolSafety.TOOL_CHECK

    def __init__(self, client: AtlassianClient):
        self.client = client

    def get_tool_definition(
        self, request_context: RequestContext | None = None
    ) -> ToolDefinition:
        """Get the tool parameter for LLM with user info injected.

        Retrieves Jira user info if the user is authenticated and injects it into the tool description
        to help the LLM make correct API calls with the user's account ID.

        Returns:
            ToolDefinition: Tool definition with user info in the description
        """
        # Start with the base description
        description = self.description

        if request_context:
            # Try to get user's info
            atlassian_account_id = None
            try:
                atlassian_account_id = self.client.get_atlassian_account_id(
                    request_context
                )
            except Exception as e:
                log.warning(f"Failed to get Jira account id: {str(e)}")

            # If we have user info, add it to the description
            if atlassian_account_id:
                # Add user info to the description
                description += (
                    f" The current user's Jira Account ID is {atlassian_account_id}"
                )

        # Return the tool definition with the enhanced description
        return ToolDefinition(
            name=self.name,
            description=description,
            input_schema_json=json.dumps(self.input_schema),
        )

    def get_availability_status(
        self, request_context: RequestContext
    ) -> agents_pb2.ToolAvailabilityStatus.ValueType:
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to check Jira availability: %s", str(e))
            return agents_pb2.ToolAvailabilityStatus.UNKNOWN_STATUS

        if is_oauth_configured:
            return agents_pb2.ToolAvailabilityStatus.AVAILABLE
        else:
            return agents_pb2.ToolAvailabilityStatus.USER_CONFIG_REQUIRED

    def run_validated(
        self,
        validated_input: JiraInput,
        extra_tool_input: AtlassianExtraToolInput | EmptyExtraToolInput,
        request_context: RequestContext,
    ) -> str:
        """Execute Jira API call with formatting.

        Args:
            validated_input: JiraInput containing endpoint and parameters
            extra_tool_input: Credentials for Jira
            request_context: Request context for tracking and metrics

        Returns:
            Markdown formatted API response
        """
        try:
            if isinstance(extra_tool_input, AtlassianExtraToolInput):
                credentials = extra_tool_input.get_credentials()
            else:
                credentials = None

            # Create the request
            request = atlassian_pb2.AtlassianApiRequest(
                atlassian_service=atlassian_pb2.AtlassianService.JIRA,
                endpoint=validated_input.endpoint,
                method=METHOD_MAP[validated_input.method],
                data=json.dumps(validated_input.data or {}),
            )
            if credentials:
                request.credentials.CopyFrom(credentials)

            resp = self.client.call_atlassian_api(request_context, request)

            if resp.status_code == 401:
                raise ToolAuthenticationError(resp.response)
            if not (200 <= resp.status_code < 300):
                raise Exception(
                    f"Jira API call failed with status code {resp.status_code}: {resp.response}"
                )

            try:
                parsed_response = json.loads(resp.response)

                filtered_response = format_atlassian_response(
                    parsed_response,
                    excluded_fields=JIRA_EXCLUDED_FIELDS.union(PII_FIELDS),
                )

                return filtered_response
            except Exception as e:
                log.error("Failed to parse response: %s", str(e))
                # If all parsing attempts fail, return the response as-is
                return f"```\n{resp.response}\n```"

        except Exception as e:
            log.error("Failed to call Jira API: %s", str(e))
            raise

    def get_oauth_url(self, request_context: RequestContext) -> str:
        try:
            return self.client.get_oauth_url(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
        except Exception as e:
            log.error("Failed to get Confluence OAuth URL: %s", e)
            return ""

    def check_validated_input_safe(self, validated_input: JiraInput) -> bool:
        """Check if the Jira API call is safe using validated input.

        GET methods are considered safe, while POST/PUT methods require approval.

        Returns:
            bool: True if the API call is safe (GET method), False otherwise
        """
        return validated_input.method == "GET"

    def revoke_tool_access(self, request_context: RequestContext) -> status_pb2.Status:  # pylint: disable=no-member # type: ignore
        """
        Remove the token from settings.

        Args:
            request_context: The request context to use.

        Returns:
            Status code indicating the result of the operation.
        """
        try:
            response = self.client.revoke_oauth_token(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
            return response.status
        except Exception as e:
            log.error("Failed to revoke Jira OAuth token: %s", str(e), exc_info=True)
            return status_pb2.Status(code=grpc.StatusCode.INTERNAL.value[0])  # pylint: disable=no-member # type: ignore

    def test_tool_connection(
        self, request_context: RequestContext
    ) -> status_pb2.Status:  # pylint: disable=no-member # type: ignore
        """
        Test the connection to Jira by fetching the current user's information.

        Args:
            request_context: The request context to use.

        Returns:
            Status code indicating the result of the operation.
        """
        # Check the user's credentials
        try:
            is_oauth_configured = self.client.is_configured(
                atlassian_pb2.AtlassianService.JIRA, request_context
            )
            if not is_oauth_configured:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.UNAUTHENTICATED,  # type: ignore
                    message="Jira credentials are not configured. Please configure them in the settings page.",
                )

            # Test authenticated access by making a test call
            request = atlassian_pb2.AtlassianApiRequest(
                atlassian_service=atlassian_pb2.AtlassianService.JIRA,
                method=atlassian_pb2.HTTPMethod.GET,
                endpoint="/user",
            )
            result = self.client.call_atlassian_api(request_context, request)
            if 200 <= result.status_code < 300:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.OK,  # type: ignore
                    message="Jira credentials are successfully configured.",
                )
            elif result.status_code == 401:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.UNAUTHENTICATED,  # type: ignore
                    message="Authentication failed. Please re-authenticate with Jira.",
                )
            elif result.status_code == 503:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.UNAVAILABLE,  # type: ignore
                    message=f"Jira API appears to be unavailable (HTTP {result.status_code}). Please check https://jira-software.status.atlassian.com/ for status information.",
                )
            else:
                return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                    code=code_pb2.INTERNAL,  # type: ignore
                    message=f"Failed to check Jira credentials (HTTP {result.status_code}).",
                )
        except Exception as e:
            log.error("Failed to check Jira credentials: %s", str(e), exc_info=True)
            return status_pb2.Status(  # pylint: disable=no-member # type: ignore
                code=code_pb2.INTERNAL,  # type: ignore
                message=f"Failed to check Jira credentials: {str(e)}",
            )


# Helper functions to filter and format the response
def format_atlassian_response(response: dict, excluded_fields: set[str]) -> str:
    """Format Atlassian API response as yaml."""
    response = filter_response(response, excluded_fields)
    if not response:
        return "Atlassian API response is empty."

    # Format the response as YAML
    # default_flow_style=False ensures block style (indented) formatting
    # indent=2 sets the indentation level to 2 spaces
    formatted = yaml.safe_dump(
        response,
        default_flow_style=False,
        indent=2,
        sort_keys=True,
        allow_unicode=True,
    )

    return formatted


def extract_text_from_atlassian_doc(content_node):
    """Extract and concatenate text from Atlassian Document Format.

    Recursively processes the nested structure of Atlassian Document Format
    to extract all text content into a single string.
    """
    if not content_node:
        return ""

    # If it's a string, return it directly
    if isinstance(content_node, str):
        return content_node

    # If it's a dict with 'text' key, return the text
    if isinstance(content_node, dict):
        if "text" in content_node:
            return content_node["text"]

        # If it has 'content' key, process it recursively
        if "content" in content_node:
            return " ".join(
                extract_text_from_atlassian_doc(item)
                for item in content_node["content"]
            )

    # If it's a list, process each item and join with spaces
    if isinstance(content_node, list):
        return " ".join(extract_text_from_atlassian_doc(item) for item in content_node)

    # Default case
    return ""


def filter_response(response: T, excluded_fields: set[str] | None = None) -> T:
    """Filter the response to include only relevant fields."""
    if excluded_fields is None:
        excluded_fields = set()

    if isinstance(response, list):
        # Process each item in the list
        filtered_list: list = []
        for item in response:
            filtered_item = filter_response(item, excluded_fields)

            # Add the item to our result list if it's not empty
            if filtered_item or filtered_item == 0 or filtered_item is False:
                filtered_list.append(filtered_item)

        return filtered_list  # type: ignore

    if isinstance(response, dict):
        # Special handling for Atlassian Document Format in description
        if (
            "content" in response
            and "type" in response
            and response.get("type") == "doc"
        ):
            # This looks like an Atlassian Document Format object
            extracted_text = extract_text_from_atlassian_doc(response)
            if extracted_text:
                return extracted_text  # type: ignore

        # We'll handle description fields in the normal filtering process below

        filtered: dict = {}
        for key, value in response.items():
            # Skip excluded fields
            if key in excluded_fields:
                continue

            # Special handling for description fields that contain Atlassian Document Format
            if key == "description" and isinstance(value, dict):
                if (
                    "content" in value
                    and "type" in value
                    and value.get("type") == "doc"
                ):
                    # Extract text from the description field
                    description_text = extract_text_from_atlassian_doc(value)
                    if description_text:
                        filtered[key] = description_text
                        continue

            # Apply normal filtering to the value
            filtered_value = filter_response(value, excluded_fields)

            # Only add non-empty values (except for empty dicts which are allowed)
            if (
                filtered_value
                or filtered_value == 0
                or filtered_value is False
                or isinstance(filtered_value, dict)
            ):
                filtered[key] = filtered_value

        return filtered  # type: ignore

    # Default case for non-list, non-dict values
    return response

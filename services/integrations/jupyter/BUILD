load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "jupyter_server",
    srcs = ["jupyter_server.py"],
    deps = [
        "//base/third_party_clients:clients",
        "//base/third_party_clients:third_party_model_client",
        "//services/integrations/atlassian/agent_tools:atlassian_agent_tools",
        "//services/integrations/google_search/agent_tools:google_search_agent_tools",
        "//services/integrations/notion/agent_tools:notion_agent_tools",
        "//services/lib/request_context:request_context_py",
        requirement("notebook"),
        requirement("ipykernel"),
    ],
)

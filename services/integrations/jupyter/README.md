Jupyter server for testing integration code
==========

Integrations are a bit annoying to test out during development because they need auth.
This is a barebones bazel command that starts a jupyter server so we can test things out more easily.

To run start it,

    bazel run //services/integrations/jupyter:jupyter_server

You will see it prints an url like this:  http://localhost:8888/?token=XXXXX
To connect to it, either forward and connect to this address on a web browser, or go to vscode,
open a notebook then click Select Kernel -> Existing Jupyter Server -> enter the url above and press enter -> choose the ipykernel kernel

You will only be able to import those packages that you made available in the BUILD file.


NOTE: if you happen to loose connection to your terminal, the jupyter server WILL NOT TERMINATE, unlike those created by VSCode.

The upside is that you can close your laptop, drive home and your session can continue.
Downside is that you will have to kill the process.  `ps | grep jupyter` then kill it.

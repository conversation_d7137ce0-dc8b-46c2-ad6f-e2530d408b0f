"""A Python client library for an reranker service."""

import logging
from dataclasses import dataclass
from typing import Optional, Protocol, Sequence

import grpc
import numpy

from base.python.grpc import client_options
import services.reranker.reranker_pb2 as reranker_pb2
import services.reranker.reranker_pb2_grpc as reranker_pb2_grpc

from base.proto.tensor import to_tensor
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> reranker_pb2_grpc.RerankerStub:
    """Setup the client stub for a reranker service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = reranker_pb2_grpc.RerankerStub(channel)
    return stub


@dataclass
class RerankerScores:
    """A dataclass holding the results of a reranking request."""

    gain: float | None
    gain_left: float | None
    gain_right: float | None


@dataclass
class RerankerOutput:
    """A dataclass holding the results of a reranking request."""

    scores_list: list[RerankerScores]


class RerankerClientProtocol(Protocol):
    """Protocol for the reranker client."""

    def calculate_scores(
        self,
        tokens: Sequence[int],
        request_context: RequestContext,
        timeout: float = 30,
        source_namespace: str | None = None,
    ) -> RerankerOutput:
        """Calculate the list of scores for the chunks in a given batch of tokens."""
        raise NotImplementedError()


class RerankerClient(RerankerClientProtocol):
    """Class to call reranker APIs remotely."""

    def __init__(self, stub):
        self.stub = stub

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new content manager client from endpoint and credentials."""
        stub = setup_stub(endpoint, credentials, options=options)
        return cls(stub)

    def calculate_scores(
        self,
        tokens: Sequence[int],
        request_context: RequestContext,
        timeout: float = 30,
        source_namespace: str | None = None,
    ) -> RerankerOutput:
        token_arr = numpy.array(tokens, dtype=numpy.int32)

        request = reranker_pb2.RerankingRequest()
        request.tokens.MergeFrom(to_tensor(token_arr))
        if source_namespace is not None:
            request.source_namespace = source_namespace
        try:
            response = self.stub.CalculateScores(
                request, timeout=timeout, metadata=request_context.to_metadata()
            )
        except grpc.RpcError as ex:
            logging.exception(
                "Reranker request %s failed: %s", request_context.request_id, ex
            )
            raise
        return RerankerOutput(
            scores_list=[
                RerankerScores(
                    gain=s.gain,
                    gain_left=s.gain_left,
                    gain_right=s.gain_right,
                )
                for s in response.scores_list
            ]
        )

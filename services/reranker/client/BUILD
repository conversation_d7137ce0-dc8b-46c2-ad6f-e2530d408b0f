load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/proto:tensor",
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
        "//services/reranker:reranker_py_proto",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["client_test.py"],
    deps = [
        ":client",
        requirement("numpy"),
        requirement("mock"),
    ],
)

"""Tests for the reranker client."""

from unittest import mock

import numpy as np

from services.lib.request_context.request_context import RequestContext
from services.reranker.client import client


def test_calculate_scores():
    stub = mock.Mock()

    reranking_scores = client.RerankerScores(
        **{
            "gain": 42,
            "gain_left": 1337,
            "gain_right": 1338,
        }
    )

    seq_length = 5
    reranking_response_mock = mock.Mock()
    reranking_response_mock.scores_list = [reranking_scores for _ in range(seq_length)]

    stub.CalculateScores.return_value = reranking_response_mock
    emb_client = client.RerankerClient(stub)
    request_context = RequestContext.create()

    return_value = emb_client.calculate_scores(
        tokens=[1, 2, 3], request_context=request_context
    )

    stub.CalculateScores.assert_called_once()

    def _get_tuple_score(inp: client.RerankerScores) -> tuple[float, float, float]:
        return (inp.gain, inp.gain_left, inp.gain_right)  # type: ignore

    np.testing.assert_array_equal(
        [_get_tuple_score(reranking_scores) for _ in range(seq_length)],
        [_get_tuple_score(s) for s in return_value.scores_list],
    )

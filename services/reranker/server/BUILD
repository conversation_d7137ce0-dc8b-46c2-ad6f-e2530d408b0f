load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_torch_oci_image")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")

py_library(
    name = "reranker_model",
    srcs = ["reranker_model.py"],
    deps = [
        "//base/fastforward:batching",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_utils",
        "//services/embedder_host/server:embedder_model",
        "//services/reranker:reranker_py_proto",
        "//services/reranker/client",
        requirement("numpy"),
        requirement("opentelemetry-api"),
        requirement("opentelemetry-sdk"),
        requirement("prometheus-client"),
        requirement("structlog"),
        requirement("grpcio"),
        requirement("torch"),
    ],
)

pytest_test(
    name = "reranker_model_test",
    srcs = ["reranker_model_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        ":reranker_model",
        "//base/fastforward:fwd_testmodel",
        "//services/embedder_host/server:embedder_model_test",
        requirement("numpy"),
    ],
)

DEPS = [
    ":reranker_model",
    ":reranker_handler",
    "//base/feature_flags:feature_flags_py",
    "//base/logging:struct_logging",
    "//services/embedder_host:embedder_py_proto",
    "//services/deploy/configs:repo_model_config_py_proto",
    "//services/lib:model_arch",
    "//services/lib/grpc/metrics:metrics",
    "//base/proto:tensor",
    "//base/fastforward/codegen:fwd_codegen",
    "//base/fastforward/starcoder:fwd_starcoder",
    "//base/fastforward/starcoder:fwd_starcoder_fp8",
    "//base/tracing:tracing_py",
    requirement("dataclasses_json"),
    requirement("grpcio"),
    requirement("grpcio-reflection"),
    requirement("numpy"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("prometheus-client"),
    requirement("protobuf"),
    "//base/python/signal_handler",
]

py_binary(
    name = "reranker_service",
    srcs = [
        "reranker_service.py",
    ],
    main = "reranker_service.py",
    deps = DEPS,
)

py_torch_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":reranker_service",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services/deploy:__subpackages__"],
)

py_library(
    name = "reranker_handler",
    srcs = ["reranker_handler.py"],
    deps = [
        ":reranker_model",
        "//base/logging:struct_logging",
        "//base/prometheus:sampled_histogram",
        "//services/embedder_host/server:embedder_batching",
        "//services/embedder_host/server:embedder_handler",
        "//services/lib/request_context:request_context_py",
        requirement("numpy"),
        requirement("opentelemetry-api"),
        requirement("opentelemetry-sdk"),
        requirement("prometheus-client"),
        requirement("structlog"),
    ],
)

pytest_test(
    name = "reranker_handler_test",
    srcs = ["reranker_handler_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        ":reranker_handler",
        ":reranker_model",
        "//base/fastforward:fwd_testmodel",
        requirement("numpy"),
    ],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
    ],
)

"""Reranker services logic."""

from typing import Sequence

import numpy as np
import opentelemetry.context
import opentelemetry.trace
import structlog

from base.fastforward import batching
from services.embedder_host.server.embedder_model import (
    _NUM_ATTENTION_CACHES_DEFAULT,
    GenericRetrievalInferenceModelFFWD,
    RetrievalInferenceModel,
)
from services.reranker.client.client import RerankerOutput, RerankerScores

opentelemetry_tracer = opentelemetry.trace.get_tracer(__name__)

log = structlog.get_logger()


class NullRerankerModel(RetrievalInferenceModel[RerankerOutput]):
    """A dummy reranker model producing dummy emeddings.

    This model is used for testing and debugging. It grabs the first token as the score.
    """

    def __init__(self, num_dims_to_grab_scores_from: int = 3):
        self.num_dims_to_grab_scores_from = num_dims_to_grab_scores_from
        self.num_calls = 0  # Used for testing

    def get_num_attention_caches(self) -> int:
        return _NUM_ATTENTION_CACHES_DEFAULT

    def process_round(self, batch: batching.Round) -> Sequence[RerankerOutput]:
        """Returns a dummy embedding."""
        self.num_calls += 1
        result = []
        for rir in batch.requests_in_round:
            first_token = batch.tokens[rir.round_start_idx]
            result.append(
                RerankerOutput(
                    scores_list=[
                        RerankerScores(
                            gain=float(first_token),
                            gain_left=float(first_token),
                            gain_right=float(first_token),
                        )
                    ]
                )
            )
        return result


class RerankerModelFFWD(GenericRetrievalInferenceModelFFWD[RerankerOutput]):
    """A model for a reranker or reranker service."""

    def __init__(self, target_token: int, *args, **kwargs):
        def _logits_to_output_fn(
            tokens: Sequence[int], logits: np.ndarray
        ) -> RerankerOutput:
            """Grab scores from first few dimensions in logits of target tokens"""
            # The [0] is because tokens is only 1 dimension,
            # and we only need the indices pertaining to that dimension.
            indices = np.where(np.array(tokens) == target_token)[0]
            logits = logits[indices, :]
            scores_list = [
                RerankerScores(
                    gain=float(emb[0]),
                    gain_left=float(emb[1]),
                    gain_right=float(emb[2]),
                )
                for emb in logits
            ]
            return RerankerOutput(scores_list=scores_list)

        super().__init__(logits_to_output_fn=_logits_to_output_fn, *args, **kwargs)

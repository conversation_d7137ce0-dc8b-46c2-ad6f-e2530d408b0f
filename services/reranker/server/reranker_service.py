"""Entry point of the reranker service.

Mostly a copy of services/embedder_host/server/embedder_service.py
"""

import argparse
import logging
import os
import pathlib
import threading
import time
import typing
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection
from prometheus_client import start_http_server

import base.feature_flags
import base.tracing
from base.fastforward import cached_attention, fwd
from base.fastforward.codegen import fwd_codegen
from base.fastforward.starcoder import fwd_starcoder, fwd_starcoder_fp8
from base.logging.struct_logging import setup_struct_logging
from base.proto import tensor
from base.python.signal_handler.signal_handler import GracefulSignalHandler
from services.embedder_host.server.embedder_handler import QueueFullError
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.model_arch import ModelArch
from services.lib.request_context.request_context import RequestContext
from services.reranker import reranker_pb2, reranker_pb2_grpc
from services.reranker.server import reranker_handler, reranker_model

tracer = base.tracing.setup_opentelemetry()
logger = structlog.get_logger()


@dataclass_json
@dataclass
class Config:
    """Configuration for the reranker service.

    Currently, the Config is specified in deploy.jsonnet.
    """

    port: int

    # if server MTLS should be used
    mtls: bool
    server_cert_path: str
    server_key_path: str
    root_cert_path: str

    # model architecture specification
    model_arch: ModelArch
    max_seq_length: int

    # special token to grab scores from; should be placed after each chunk in request input sequence
    target_token: int

    model_name: str

    # path to the weights.
    # the contents of the weights path is model arch specific.
    # codegen: the directory is a deepspeed checkpoint directory, i.e. it has the "mp_rank_00_model_states.pt" file.
    weights_path: str
    weights_sha256: str

    # the number of rpc threads to use
    max_rpc_threads: int

    # Split the model across this many GPUs
    model_parallelism: int

    round_sizes: list[int]

    num_attention_caches: int

    # The maximal number of chunks (not requests) in the queue for each
    # tenant and request type. The check is only performed when the first chunk
    # of a request is added to the queue. This is to prevent long files from being
    # rejected half-way through processing.
    queue_size: int

    feature_flags_sdk_key_path: typing.Optional[str] = None

    # timeout for reranker requests
    timeout_s: float = 1.0

    # grace period for server shutdown
    shutdown_grace_period_s: float = 20.0

    # Kernel selection flags
    pre_attention_kernel_fusion: bool = False
    use_register_tokens_kernel: bool = False


class RerankerServices(reranker_pb2_grpc.RerankerServicer):
    """Reranker RPC server."""

    def __init__(
        self, config: Config, handler: reranker_handler.QueueingRerankerHandler
    ):
        self.config = config
        self.handler = handler

    def CalculateScores(
        self,
        request: reranker_pb2.RerankingRequest,
        context: grpc.ServicerContext,
    ):
        start_time = time.time()
        request_context = RequestContext.from_grpc_context(context)
        request_context.bind_context_logging()
        tokens = tensor.to_numpy(request.tokens)

        logger.info(
            "Reranker: processing request with shape %s",
            tokens.shape,
        )
        peer = context.peer()
        peer_identity = context.peer_identity_key()
        logger.info(
            "Reranker: request from %s (%s), source namespace %s",
            peer,
            peer_identity,
            request.source_namespace,
        )
        if len(tokens.shape) != 1:
            logger.error(
                "Reranker: invalid tokens shape: %s",
                tokens.shape,
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="tokens must be 1-dimensional",
            )
        if (tokens >= self.config.model_arch.vocab_size).any():
            logger.error(
                "Reranker: invalid tokens. Tokens must be <= vocab_size.",
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="tokens must be less than vocab_size",
            )
        if (tokens < 0).any():
            logger.error(
                "Rerankerr: invalid tokens. Tokens must be non-negative",
            )
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details="tokens must be non-negative",
            )
        try:
            reranker_outputs = self.handler.calculate_inference_artifacts(
                request_id=request_context.request_id,
                source_namespace=request.source_namespace,
                tokens=[list(tokens)],
                timeout_s=self.config.timeout_s,
            )
            assert (
                len(reranker_outputs) == 1
            )  # there should only be one sequence in the input

            response = reranker_pb2.RerankingResponse()
            for score_obj in reranker_outputs[0].scores_list:
                score_proto = reranker_pb2.RerankingScores()

                gain = score_obj.gain
                gain_left = score_obj.gain_left
                gain_right = score_obj.gain_right
                if gain is None or gain_left is None or gain_right is None:
                    raise ValueError(
                        "Reranker: invalid scores. Scores must be non-null",
                    )

                score_proto.gain = gain
                score_proto.gain_left = gain_left
                score_proto.gain_right = gain_right
                response.scores_list.append(score_proto)

            logger.info(
                "Reranker: finished request with shape %s in %.3fs",
                tokens.shape,
                time.time() - start_time,
            )
            return response
        except grpc.RpcError as ex:
            logger.error("Reranker request failed: %s", ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
        except ValueError as ex:
            logger.error("Reranker request failed: %s", ex)
            context.abort(
                code=grpc.StatusCode.INVALID_ARGUMENT,
                details=ex.args[0],
            )
        except QueueFullError as ex:
            logger.warning(
                "Reranker request failed with queue full error. %s",
                ex,
            )
            context.abort(
                code=grpc.StatusCode.RESOURCE_EXHAUSTED,
                details="Reranker queue is full",
            )
        except TimeoutError as ex:
            logger.info(
                "Reranker request failed with timeout error. %s",
                ex,
            )
            context.abort(
                code=grpc.StatusCode.DEADLINE_EXCEEDED,
                details="Reranker request timed out",
            )
        except RuntimeError as ex:
            logger.error(
                "Reranker request failed with runtime error. Exiting as this is "
                "likely a unrecoverable GPU error. %s",
                ex,
            )
            os._exit(1)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.error("Reranker request failed: %s", ex)
            logging.exception(ex)
            raise


def _load(path: str):
    return pathlib.Path(path).read_bytes()


def _setup_model(config: Config):
    logging.info(
        "Loading model %s, weights %s",
        config.model_name,
        config.weights_path,
    )
    if config.model_parallelism > 1:
        raise ValueError(
            "Model parallelism is not supported for the embedding service."
        )

    model_spec = fwd.ModelSpec(
        name=config.model_name,
        checkpoint_path=config.weights_path,
        checkpoint_sha256=config.weights_sha256,
        vocab_size=config.model_arch.vocab_size,
        num_layers=config.model_arch.num_layers,
        num_heads=config.model_arch.num_heads,
        emb_dim=config.model_arch.emb_dim,
        head_dim=config.model_arch.head_dim,
        rotary_pct=config.model_arch.rotary_pct,
        max_position_embeddings=config.max_seq_length,
        norm_eps=config.model_arch.norm_eps,
    )
    if config.model_arch.arch_type == "CODEGEN":
        step_fn = fwd_codegen.generate_step_fn(
            model_spec,
            output_type=fwd.OutputTensorType.EMBEDDING,
        )
        attn_factory = fwd_codegen.CodeGenAttentionFactory(model_spec)
    elif config.model_arch.arch_type == "STARCODER":
        step_fn = fwd_starcoder.generate_step_fn(
            model_spec,
            output_type=fwd.OutputTensorType.EMBEDDING,
        )
        attn_factory = fwd_starcoder.StarcoderAttentionFactory(
            model_spec=model_spec,
            pre_attention_kernel_fusion=config.pre_attention_kernel_fusion,
            use_register_tokens_kernel=config.use_register_tokens_kernel,
        )
    elif config.model_arch.arch_type == "STARCODER_FP8":
        step_fn = fwd_starcoder_fp8.generate_step_fn(
            model_spec=model_spec,
            output_type=fwd.OutputTensorType.EMBEDDING,
            # Uncomment below to turn on cuda graphs. Right now, it
            # doesn't speed up the reranker, so we leave it commented out.
            # auto_capture_graphs=True,
            # batch_sizes=[256, 512, 1024, 2048, 3072, 4096, 6144, 8192],
        )
        attn_factory = fwd_starcoder_fp8.StarcoderAttentionFactory(
            model_spec=model_spec,
            attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,
            pre_attention_kernel_fusion=config.pre_attention_kernel_fusion,
            use_register_tokens_kernel=config.use_register_tokens_kernel,
        )
    else:
        raise ValueError(f"Unsupported arch type: {config.model_arch.arch_type}")
    return (step_fn, attn_factory)


def _serve(config: Config, shutdown_event: threading.Event):
    server = grpc.server(
        ThreadPoolExecutor(max_workers=config.max_rpc_threads),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
        ],
        # Reranker service controls queueing. Each request needs one
        # thread to be processed. So we set this number to the number
        # of threads to reject requests if the queue is full.
        maximum_concurrent_rpcs=config.max_rpc_threads,
    )

    (step_fn, attn_factory) = _setup_model(config)

    reranker = reranker_model.RerankerModelFFWD(
        target_token=config.target_token,
        model_name=config.model_name,
        step_fn=step_fn,
        attn_factory=attn_factory,
        max_length=config.max_seq_length,
        round_sizes=config.round_sizes,
        num_attention_caches=config.num_attention_caches,
        use_multi_request_attention=True
        if config.model_arch.arch_type == "STARCODER_FP8"
        else False,
    )
    handler = reranker_handler.QueueingRerankerHandler(
        model_name=config.model_name,
        model=reranker,
        max_round_size=max(config.round_sizes),
        max_seq_length=config.max_seq_length,
        vocab_size=config.model_arch.vocab_size,
        start_queued_requests_counter=True,
        query_queue_size=config.queue_size,
        mrfa_mode=True,
    )
    handler.start()

    reranker_pb2_grpc.add_RerankerServicer_to_server(
        RerankerServices(config, handler), server
    )
    service_names = (
        reranker_pb2.DESCRIPTOR.services_by_name["Reranker"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    if config.mtls:
        server_credentials = grpc.ssl_server_credentials(
            [(_load(config.server_key_path), _load(config.server_cert_path))],
            root_certificates=_load(config.root_cert_path),
            require_client_auth=True,
        )
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )


def main():
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)

    context = base.feature_flags.Context.setup(path)
    base.feature_flags.set_global_context(context)

    _serve(config, handler.get_shutdown_event())


if __name__ == "__main__":
    main()

// K8S deployment file for the reranker.
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
function(env,
         namespace,
         namespace_config,
         cloud,
         name,
         modelInstanceConfigMapName,
         modelConfig,
         gpu,
         replicas,
         gpuCount=1,
         global=false)
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, cloud=cloud, namespace=namespace, appName=name);
  local services = lib.flatten([
    grpcLib.grpcService(name, namespace=namespace),
    if global then grpcLib.globalGrpcService(cloud=cloud, appName=name, namespace=namespace) else [],
  ]);
  local dnsNames = lib.flatten([
    grpcLib.grpcServiceNames(name, namespace=namespace),
    if global then grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=name, namespace=namespace) else [],
  ]);
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  // create a client TLS certificate
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % name,
    namespace=namespace,
    env=env,
    dnsNames=dnsNames,
    appName=name,
    volumeName='client-certs',
  );
  // create a server certificate for MTLS
  local serverCert = if env != 'DEV' then
    certLib.createCentralServerCert(name='%s-server-cert' % name,
                                    namespace=namespace,
                                    env=env,
                                    appName=name,
                                    dnsNames=dnsNames,
                                    volumeName='certs')
  else
    certLib.createServerCert(name='%s-server-cert' % name,
                             namespace=namespace,
                             appName=name,
                             dnsNames=grpcLib.grpcServiceNames(name),
                             volumeName='certs');
  local configMap = configMapLib.createConfigMap(appName=name, namespace=namespace, config={
    port: 50051,
    mtls: mtls,
    server_cert_path: '/certs/tls.crt',
    server_key_path: '/certs/tls.key',
    root_cert_path: '/certs/ca.crt',
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    model_arch: modelConfig.model_arch,
    max_seq_length: modelConfig.seq_len_per_batch_elem,
    model_name: modelConfig.name,
    weights_path: modelConfig.efs_deepspeed_checkpoint_path,
    weights_sha256: modelConfig.checkpoint_sha256,
    max_rpc_threads: 64,  // up to several seconds of work can queue up.
    model_parallelism: gpuCount,
    round_sizes: modelConfig.round_sizes,
    num_attention_caches: 32,
    queue_size: 20,
    // timeouts for requests
    timeout_s: 5.0,
    target_token: modelConfig.target_token,
    // Kernel selection flags
    pre_attention_kernel_fusion: true,
    use_register_tokens_kernel: true,
  });
  local container =
    {
      name: 'reranker-py',
      target: {
        name: '//services/reranker/server:image',
        dst: 'reranker_py',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv(name, telemetryLib.collectorUri(env, namespace, cloud)) + [
        // this is the location GKE injects the matching NVIDIA CUDA driver utils
        {
          name: 'TRITON_CUDA_DIRS',
          value: '/usr/local/nvidia/lib64/',
        },
      ] + dynamicFeatureFlags.env,
      volumeMounts: [
        {
          name: 'persistent-storage',
          mountPath: '/mnt/efs/augment',
          readOnly: true,
        },
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      startupProbe: grpcLib.grpcHealthCheck('%s-svc' % name, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        failureThreshold: 6,
        initialDelaySeconds: 60,
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1 * gpuCount,
          'nvidia.com/gpu': gpuCount,
          memory: '%sGi' % (4 * gpuCount),
        },
      },
    };
  local pod =
    {
      containers: [
        container,
      ],
      volumes: [
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        {
          name: 'persistent-storage',
          persistentVolumeClaim: {
            claimName: 'filestore-checkpoint-claim',
          },
        },
        configMap.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    };
  local tolerations = nodeLib.tolerations(resource=gpu, env=env, count=gpuCount, cloud=cloud);
  local affinity = nodeLib.affinity(resource=gpu, env=env, cloud=cloud, appName=name);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: name,
      namespace: namespace,
      labels: {
        app: name,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // Adhoc auto-scaling and pulling large weights can take longer than the default 10 minutes.
      progressDeadlineSeconds: (10 * 60) + (2 * 60 * replicas),
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: replicas,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: name,
        },
      },
      template: {
        metadata: {
          labels: {
            app: name,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
          priorityClassName: cloudInfo.envToPriorityClass(env=env, resource=gpu),
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serverCert.objects,
    clientCert.objects,
    deployment,
    services,
    dynamicFeatureFlags.k8s_objects,
  ])

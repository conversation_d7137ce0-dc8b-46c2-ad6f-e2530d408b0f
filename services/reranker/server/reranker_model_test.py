"""Tests for reranker_model.py."""

from typing import Sequence

import pytest

from base.fastforward import fwd_testmodel
from services.embedder_host.server.embedder_model_test import round_from_token_lists
from services.reranker.server import reranker_model

# Todo:
# * Add regression tests for dark chatanol like we have for ethanol
#   in `services/embedder_host/server/embedder_model_test.py`

TEST_TARGET_TOKEN = 1234


def _mock_model(
    max_length: int = 128,
    round_sizes: tuple[int, ...] = (128,),
    num_attention_caches: int = 2,
) -> reranker_model.RerankerModelFFWD:
    """Returns a test model."""
    model_spec = fwd_testmodel.get_dummy_modelspec()
    step_fn = fwd_testmodel.generate_step_fn(model_spec)
    attention_factory = fwd_testmodel.get_attention_factory(
        model_spec, ignore_first_n=max_length
    )

    return reranker_model.RerankerModelFFWD(
        target_token=TEST_TARGET_TOKEN,
        model_name="Testmodel",
        step_fn=step_fn,
        attn_factory=attention_factory,
        max_length=max_length,
        round_sizes=round_sizes,
        num_attention_caches=num_attention_caches,
    )


def test_process_round_model():
    """Tests the process_round function with the fwd_testmodel."""
    model = _mock_model()

    tokens = [
        [
            1,
            2,
            3,
            TEST_TARGET_TOKEN,
            4,
            5,
            6,
            TEST_TARGET_TOKEN,
            7,
            8,
            9,
            TEST_TARGET_TOKEN,
        ],
        [0, 0, 0, 0, 0, 0, TEST_TARGET_TOKEN, 0, 0, 0, 0, 0, 0, 0, TEST_TARGET_TOKEN],
    ]

    round = round_from_token_lists(tokens)
    scores = model.process_round(round)

    assert (
        len(scores) == 2
    ), "The number of reranker outputs should be the same as the number of requests."
    req1_scores = scores[0].scores_list
    req2_scores = scores[1].scores_list

    assert (
        len(req1_scores) == 3
    ), "The number of scores object should be the same as the number of target_tokens"
    assert (
        len(req2_scores) == 2
    ), "The number of scores object should be the same as the number of target_tokens"

    for scores_dct in list(req1_scores) + list(req2_scores):
        assert isinstance(scores_dct.gain, float)
        assert isinstance(scores_dct.gain_left, float)
        assert isinstance(scores_dct.gain_right, float)


def test_process_round_raises_on_large_round():
    """Tests that process_round raises ValueError when a round is too large."""
    model = _mock_model()
    max_length = model._max_length
    with pytest.raises(ValueError):
        model.process_round(round_from_token_lists([list(range(max_length + 1))]))


def test_process_round_raises_on_empty_round():
    """Tests that process_round raises ValueError when a round is empty."""
    model = _mock_model()
    with pytest.raises(ValueError):
        model.process_round(round_from_token_lists([[]]))


def test_process_long_round_with_short_maxlen():
    """Tests that process_round does not raise a ValueError as long as the requests in round are short enough."""
    model = _mock_model(max_length=10, round_sizes=(20,))
    # smoke test: the following should not raise a ValueError
    model.process_round(round_from_token_lists([list(range(10)), list(range(8))]))


def _confirm_matching_contents(
    scores1: Sequence[reranker_model.RerankerOutput],
    scores2: Sequence[reranker_model.RerankerOutput],
):
    """Confirms that the contents of the batched reranker outputs are the same."""
    for scores1_req, scores2_req in zip(scores1, scores2):
        for scores1_scores_dct, scores2_scores_dct in zip(
            scores1_req.scores_list, scores2_req.scores_list
        ):
            assert scores1_scores_dct.gain == scores2_scores_dct.gain
            assert scores1_scores_dct.gain_left == scores2_scores_dct.gain_left
            assert scores1_scores_dct.gain_right == scores2_scores_dct.gain_right


def test_chunk_order():
    """Tests that the order of the chunks is respected."""
    model = _mock_model()

    tokens1 = [
        [1, TEST_TARGET_TOKEN, 2, 3, TEST_TARGET_TOKEN],
        [4, 5, TEST_TARGET_TOKEN, 6, 7, TEST_TARGET_TOKEN, 8, 9, 10, TEST_TARGET_TOKEN],
    ]
    scores1 = model.process_round(round_from_token_lists(tokens1))

    tokens2 = [
        [4, 5, TEST_TARGET_TOKEN, 6, 7, TEST_TARGET_TOKEN, 8, 9, 10, TEST_TARGET_TOKEN],
        [1, TEST_TARGET_TOKEN, 2, 3, TEST_TARGET_TOKEN],
    ]
    scores2 = model.process_round(round_from_token_lists(tokens2))

    _confirm_matching_contents(scores1, list(reversed(list(scores2))))


def test_results_independent_of_request_packing():
    """Tests that the results are independent of whether chunks are in the same request."""
    model = _mock_model()

    embeddings1 = model.process_round(
        round_from_token_lists(
            [
                [1, TEST_TARGET_TOKEN, TEST_TARGET_TOKEN, 2, 3, TEST_TARGET_TOKEN],
                [4, 5, 6, TEST_TARGET_TOKEN, 7, 8, 9, TEST_TARGET_TOKEN, 10],
            ]
        )
    )

    embeddings2_1 = model.process_round(
        round_from_token_lists(
            [[1, TEST_TARGET_TOKEN, TEST_TARGET_TOKEN, 2, 3, TEST_TARGET_TOKEN]]
        )
    )
    embeddings2_2 = model.process_round(
        round_from_token_lists(
            [[4, 5, 6, TEST_TARGET_TOKEN, 7, 8, 9, TEST_TARGET_TOKEN, 10]]
        )
    )

    _confirm_matching_contents(embeddings1, list(embeddings2_1) + list(embeddings2_2))

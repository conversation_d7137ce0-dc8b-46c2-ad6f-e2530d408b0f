"""Tests for the reranker handler.

These are mostly copied from services/embedder_host/server/embedder_handler_queueing_test.py.
I didn't copy all of them. Instead, I just copy a few as a sanity check. Otherwise,
the embedder handler tests cover the reranker handler because almost all the logic is shared.
"""

import functools
from concurrent.futures import ThreadPoolExecutor
from typing import Sequence

import pytest

from services.reranker.client.client import RerankerOutput
from services.reranker.server import reranker_model, reranker_handler


@pytest.mark.parametrize("request_size", [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
def test_queueing_reranker_handler_with_null_model(request_size: int):
    """Tests the reranker handler that uses queues."""
    model = reranker_model.NullRerankerModel()
    handler = reranker_handler.QueueingRerankerHandler(
        f"testmodel_{request_size}",
        model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=100,
    )
    tokens = [[request_size] * request_size]
    with handler:
        response = handler.calculate_inference_artifacts(
            tokens, request_id="test_rid", timeout_s=1
        )
        assert len(response) == 1
        assert len(response[0].scores_list) == 1
        score_obj = response[0].scores_list[0]
        assert score_obj.gain == request_size
        assert score_obj.gain_left == request_size
        assert score_obj.gain_right == request_size


@pytest.mark.parametrize(
    "tenants", [["tenant1"], ["tenant1", "tenant2"], ["tenant1", "tenant2", "tenant3"]]
)
def test_queueing_reranker_handler_with_null_model_with_tenants(tenants: Sequence[str]):
    """Tests the reranker handler that uses queues."""
    model = reranker_model.NullRerankerModel()
    handler = reranker_handler.QueueingRerankerHandler(
        "testmodel",
        model,
        max_round_size=10,
        max_seq_length=10,
        vocab_size=100,
    )
    thread_pool = ThreadPoolExecutor(max_workers=len(tenants))
    tenant_tokens = [[[t_idx * 10 + 5]] for t_idx in range(len(tenants))]
    futures = []
    for idx, tenant in enumerate(tenants):
        emb_fn = functools.partial(
            handler.calculate_inference_artifacts,
            tokens=list(tenant_tokens[idx]),
            request_id="test_rid",
            source_namespace=tenant,
            timeout_s=1,
        )
        futures.append(thread_pool.submit(emb_fn))
    with handler:
        for single_tenant_toks, future in zip(tenant_tokens, futures):
            reranker_outputs: Sequence[RerankerOutput] = future.result()
            assert len(reranker_outputs) == 1
            assert len(reranker_outputs[0].scores_list) == 1
            score_obj = reranker_outputs[0].scores_list[0]
            t_idx = single_tenant_toks[0][0]
            assert score_obj.gain == t_idx
            assert score_obj.gain_left == t_idx
            assert score_obj.gain_right == t_idx


def test_oversized_request_raises_value_error():
    """Tests that a request with too many tokens raises a ValueError."""
    model = reranker_model.NullRerankerModel()
    max_length = 8
    handler = reranker_handler.QueueingRerankerHandler(
        "testmodel",
        model,
        max_round_size=10,
        max_seq_length=max_length,
        vocab_size=100,
    )
    tokens = [range(max_length + 1)]
    with handler:
        with pytest.raises(ValueError):
            handler.calculate_inference_artifacts(
                tokens, request_id="test_rid", source_namespace="tenant1", timeout_s=1
            )

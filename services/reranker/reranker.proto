syntax = "proto3";
package reranker;

import "base/proto/tensor.proto";

service Reranker {
  rpc CalculateScores(RerankingRequest) returns (RerankingResponse);
}

message RerankingRequest {
  // tensor of int in the shape [tokens]
  tensor.Tensor tokens = 1;

  // the namespace of the source of the request.
  //
  // this is used to determine the source of the sequences
  string source_namespace = 2;
}

message RerankingScores {
  // contains the named scores for each chunk in the request.
  optional float gain = 1;
  optional float gain_left = 2;
  optional float gain_right = 3;
}

message RerankingResponse {
  // contains the named scores for each chunk in the request.
  repeated RerankingScores scores_list = 1;
}

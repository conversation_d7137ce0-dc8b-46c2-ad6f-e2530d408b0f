"""Test CheckAndMutateRow."""

import time
import pytest
import uuid
import grpc

from services.bigtable_proxy import bigtable_proxy_pb2
from services.bigtable_proxy.test.bigtable_test_utils import (
    make_cell_mutation,
    check_read_with_retry,
    setup_row,
    RowFilter,
    RowSet,
    TimestampRange,
    ValueRange,
)


@pytest.mark.timeout(30)
def test_check_and_mutate(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test CheckAndMutateRow with isolated test cases."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    tenant_id = "test_tenant_id_1"

    # Define test vectors
    test_cases = [
        {
            "name": "No predicate filter, with true and false mutations",
            "row_key_prefix": "check_and_mutate_no_predicate",
            "initial_value": b"test_value",
            "predicate_filter": None,
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "With predicate filter that matches, with true and false mutations",
            "row_key_prefix": "check_and_mutate_predicate_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(pass_all_filter=True),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "With predicate filter that doesn't match, with true and false mutations",
            "row_key_prefix": "check_and_mutate_predicate_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(block_all_filter=True),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "No mutations provided",
            "row_key_prefix": "check_and_mutate_no_mutations",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(pass_all_filter=True),
            "true_mutations": None,
            "false_mutations": None,
            "expected_match": True,
            "expected_value": b"test_value",  # Value should remain unchanged
        },
        {
            "name": "Only true mutations provided",
            "row_key_prefix": "check_and_mutate_only_true_mutations",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(pass_all_filter=True),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": None,
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Only false mutations provided",
            "row_key_prefix": "check_and_mutate_only_false_mutations",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(block_all_filter=True),
            "true_mutations": None,
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        # Testing different filters in the predicate
        {
            "name": "Predicate filter: Family name regex",
            "row_key_prefix": "check_and_mutate_family_name_regex",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(family_name_regex_filter="Info"),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Column qualifier regex",
            "row_key_prefix": "check_and_mutate_column_qualifier_regex",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(column_qualifier_regex_filter=b"test_column"),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Value regex",
            "row_key_prefix": "check_and_mutate_value_regex",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(value_regex_filter=b"test_value"),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Timestamp range",
            "row_key_prefix": "check_and_mutate_timestamp_range",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                timestamp_range_filter=TimestampRange(start_timestamp_micros=0)
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Condition",
            "row_key_prefix": "check_and_mutate_condition",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(family_name_regex_filter="Info"),
                    true_filter=RowFilter(pass_all_filter=True),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Chain",
            "row_key_prefix": "check_and_mutate_chain",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                chain=RowFilter.Chain(
                    filters=[
                        RowFilter(family_name_regex_filter="Info"),
                        RowFilter(column_qualifier_regex_filter=b"test_column"),
                    ]
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Pass all",
            "row_key_prefix": "check_and_mutate_pass_all",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(pass_all_filter=True),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Block all",
            "row_key_prefix": "check_and_mutate_block_all",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(block_all_filter=True),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Cells per column limit",
            "row_key_prefix": "check_and_mutate_cells_per_column_limit",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(cells_per_column_limit_filter=1),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Cells per row limit",
            "row_key_prefix": "check_and_mutate_cells_per_row_limit",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(cells_per_row_limit_filter=1),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Strip value transformer",
            "row_key_prefix": "check_and_mutate_strip_value_transformer",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(strip_value_transformer=True),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Timestamp range (no match)",
            "row_key_prefix": "check_and_mutate_timestamp_range_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                timestamp_range_filter=TimestampRange(
                    start_timestamp_micros=9000000000000000  # Far future timestamp
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Condition (no match)",
            "row_key_prefix": "check_and_mutate_condition_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(family_name_regex_filter="Info2"),
                    true_filter=RowFilter(pass_all_filter=True),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Chain (no match)",
            "row_key_prefix": "check_and_mutate_chain_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                chain=RowFilter.Chain(
                    filters=[
                        RowFilter(family_name_regex_filter="Info2"),
                        RowFilter(column_qualifier_regex_filter=b"test_column2"),
                    ]
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Value regex in Chain (match)",
            "row_key_prefix": "check_and_mutate_value_regex_chain_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                chain=RowFilter.Chain(
                    filters=[
                        RowFilter(family_name_regex_filter="Info"),
                        RowFilter(value_regex_filter=b"test_value"),
                    ]
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Value regex in Chain (no match)",
            "row_key_prefix": "check_and_mutate_value_regex_chain_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                chain=RowFilter.Chain(
                    filters=[
                        RowFilter(family_name_regex_filter="Info"),
                        RowFilter(value_regex_filter=b"different_value"),
                    ]
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Value regex in Condition predicate (match)",
            "row_key_prefix": "check_and_mutate_value_regex_condition_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(value_regex_filter=b"test_value"),
                    true_filter=RowFilter(pass_all_filter=True),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Value regex in Condition predicate (no match)",
            "row_key_prefix": "check_and_mutate_value_regex_condition_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(value_regex_filter=b"different_value"),
                    true_filter=RowFilter(pass_all_filter=True),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Value regex in Condition branches",
            "row_key_prefix": "check_and_mutate_value_regex_condition_branches",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(family_name_regex_filter="Info"),
                    true_filter=RowFilter(value_regex_filter=b"test_value"),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Complex nested condition with value regex",
            "row_key_prefix": "check_and_mutate_complex_nested_condition",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(family_name_regex_filter="Info"),
                    true_filter=RowFilter(
                        condition=RowFilter.Condition(
                            predicate_filter=RowFilter(
                                value_regex_filter=b"test_value"
                            ),
                            true_filter=RowFilter(pass_all_filter=True),
                            false_filter=RowFilter(block_all_filter=True),
                        )
                    ),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Value regex in Chain (match)",
            "row_key_prefix": "check_and_mutate_value_regex_chain_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                chain=RowFilter.Chain(
                    filters=[
                        RowFilter(family_name_regex_filter="Info"),
                        RowFilter(value_regex_filter=b"test_value"),
                    ]
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Value regex in Chain (no match)",
            "row_key_prefix": "check_and_mutate_value_regex_chain_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                chain=RowFilter.Chain(
                    filters=[
                        RowFilter(family_name_regex_filter="Info"),
                        RowFilter(value_regex_filter=b"different_value"),
                    ]
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Value regex in Condition predicate (match)",
            "row_key_prefix": "check_and_mutate_value_regex_condition_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(value_regex_filter=b"test_value"),
                    true_filter=RowFilter(pass_all_filter=True),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Value regex in Condition predicate (no match)",
            "row_key_prefix": "check_and_mutate_value_regex_condition_no_match",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(value_regex_filter=b"different_value"),
                    true_filter=RowFilter(pass_all_filter=True),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": False,
            "expected_value": b"false_value",
        },
        {
            "name": "Predicate filter: Value regex in Condition branches",
            "row_key_prefix": "check_and_mutate_value_regex_condition_branches",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(family_name_regex_filter="Info"),
                    true_filter=RowFilter(value_regex_filter=b"test_value"),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
        {
            "name": "Predicate filter: Complex nested condition with value regex",
            "row_key_prefix": "check_and_mutate_complex_nested_condition",
            "initial_value": b"test_value",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(family_name_regex_filter="Info"),
                    true_filter=RowFilter(
                        condition=RowFilter.Condition(
                            predicate_filter=RowFilter(
                                value_regex_filter=b"test_value"
                            ),
                            true_filter=RowFilter(pass_all_filter=True),
                            false_filter=RowFilter(block_all_filter=True),
                        )
                    ),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
            "true_mutations": [
                make_cell_mutation("Info", b"test_column", b"true_value")
            ],
            "false_mutations": [
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
            "expected_match": True,
            "expected_value": b"true_value",
        },
    ]

    # Run each test case
    for test_case in test_cases:
        # Create a unique row key for this test case
        row_key = f"{test_case['row_key_prefix']}_{uuid.uuid4().hex}".encode()

        # Setup the row with initial state
        setup_row(
            bigtable_proxy_server,
            request_context,
            tenant_id,
            table_name,
            row_key,
            family_name="Info",
            column_qualifier=b"test_column",
            value=test_case["initial_value"],
        )

        # Create the CheckAndMutateRow request
        check_mutate_rr = bigtable_proxy_pb2.CheckAndMutateRowRequest(
            tenant_id=tenant_id,
            table_name=table_name,
            row_key=row_key,
            predicate_filter=test_case["predicate_filter"],
            true_mutations=test_case["true_mutations"],
            false_mutations=test_case["false_mutations"],
        )

        # Execute the request
        check_mutate_resp = bigtable_proxy_server.CheckAndMutateRow(
            check_mutate_rr, metadata=request_context.to_metadata()
        )

        # Verify predicate match result
        assert (
            check_mutate_resp.predicate_matched == test_case["expected_match"]
        ), f"Test case '{test_case['name']}': Expected predicate_matched={test_case['expected_match']}"

        # Verify the row has the expected value
        check_read_with_retry(
            lambda: bigtable_proxy_server.ReadRows(
                bigtable_proxy_pb2.ReadRowsRequest(
                    tenant_id=tenant_id,
                    table_name=table_name,
                    rows=RowSet(row_keys=[row_key]),
                ),
                metadata=request_context.to_metadata(),
            ),
            row_key,
            test_case["expected_value"],
        )

    # Test unsupported filters
    unsupported_filters = [
        {
            "name": "Unsupported filter: Interleave",
            "row_key_prefix": "check_and_mutate_interleave_filter",
            "predicate_filter": RowFilter(
                interleave=RowFilter.Interleave(
                    filters=[
                        RowFilter(family_name_regex_filter="Info"),
                        RowFilter(column_qualifier_regex_filter=b"test_column"),
                    ]
                )
            ),
        },
        {
            "name": "Unsupported filter: Sink",
            "row_key_prefix": "check_and_mutate_sink_filter",
            "predicate_filter": RowFilter(sink=True),
        },
        {
            "name": "Unsupported filter: Value range",
            "row_key_prefix": "check_and_mutate_value_range_no_match",
            "predicate_filter": RowFilter(
                value_range_filter=ValueRange(start_value_closed=b"test_value2")
            ),
        },
        {
            "name": "Unsupported filter: Condition with Interleave as predicate",
            "row_key_prefix": "check_and_mutate_condition_interleave",
            "predicate_filter": RowFilter(
                condition=RowFilter.Condition(
                    predicate_filter=RowFilter(
                        interleave=RowFilter.Interleave(
                            filters=[
                                RowFilter(family_name_regex_filter="Info"),
                                RowFilter(column_qualifier_regex_filter=b"test_column"),
                            ]
                        )
                    ),
                    true_filter=RowFilter(pass_all_filter=True),
                    false_filter=RowFilter(block_all_filter=True),
                )
            ),
        },
    ]

    # Test each unsupported filter
    for test_case in unsupported_filters:
        # Create a unique row key for this test case
        row_key = f"{test_case['row_key_prefix']}_{uuid.uuid4().hex}".encode()

        # Setup the row with initial state
        setup_row(
            bigtable_proxy_server,
            request_context,
            tenant_id,
            table_name,
            row_key,
            family_name="Info",
            column_qualifier=b"test_column",
            value=b"test_value",
        )

        # Create the CheckAndMutateRow request
        check_mutate_rr = bigtable_proxy_pb2.CheckAndMutateRowRequest(
            tenant_id=tenant_id,
            table_name=table_name,
            row_key=row_key,
            predicate_filter=test_case["predicate_filter"],
            true_mutations=[make_cell_mutation("Info", b"test_column", b"true_value")],
            false_mutations=[
                make_cell_mutation("Info", b"test_column", b"false_value")
            ],
        )

        # Execute the request and expect an error
        with pytest.raises(grpc.RpcError) as excinfo:
            bigtable_proxy_server.CheckAndMutateRow(
                check_mutate_rr, metadata=request_context.to_metadata()
            )

        # Check that we got the expected error
        assert "unsupported filter" in str(
            excinfo.value
        ), f"Unsupported filter test '{test_case['name']}' failed"

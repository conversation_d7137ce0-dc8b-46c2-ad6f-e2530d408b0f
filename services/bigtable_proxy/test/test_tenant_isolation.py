"""Test tenant isolation."""

import pytest

from services.bigtable_proxy import bigtable_proxy_pb2
from services.token_exchange import token_exchange_pb2


@pytest.fixture
def table_names():
    table_names = [
        name
        for name in bigtable_proxy_pb2.TableName.keys()
        if name != "REQUEST_INSIGHT_MAIN"  # deprecated
    ]
    print(f"table_names: {table_names}")
    return table_names


def get_fake_row(table_name, tenant_name):
    if table_name == bigtable_proxy_pb2.TableName.Name(
        bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    ):
        return {
            "family_name": "Info",
            "column_qualifier": b"Value",
            "value": f"content for {tenant_name}".encode(),
        }
    elif table_name == bigtable_proxy_pb2.TableName.Name(
        bigtable_proxy_pb2.TableName.SETTINGS
    ):
        return {
            "family_name": "Settings",
            "column_qualifier": b"slack_settings",
            "value": f"send messages to {tenant_name}".encode(),
        }
    elif table_name == bigtable_proxy_pb2.TableName.Name(
        bigtable_proxy_pb2.TableName.GITHUB
    ):
        return {
            "family_name": "Current",
            "column_qualifier": b"checkpoint",
            "value": f"checkpoint1 for {tenant_name}".encode(),
        }
    elif table_name == bigtable_proxy_pb2.TableName.Name(
        bigtable_proxy_pb2.TableName.SHARE
    ):
        return {
            "family_name": "Chat",
            "column_qualifier": b"share",
            "value": f"my cool chat from {tenant_name}".encode(),
        }
    elif table_name == bigtable_proxy_pb2.TableName.Name(
        bigtable_proxy_pb2.TableName.SLACK_MAPPINGS
    ):
        return {
            "family_name": "Mappings",
            "column_qualifier": b"augment_user_id",
            "value": f"augment-user-id for {tenant_name}".encode(),
        }
    elif table_name == bigtable_proxy_pb2.TableName.Name(
        bigtable_proxy_pb2.TableName.REMOTE_AGENTS
    ):
        return {
            "family_name": "Config",
            "column_qualifier": b"config",
            "value": f"agent config for {tenant_name}".encode(),
        }
    elif table_name == bigtable_proxy_pb2.TableName.Name(
        bigtable_proxy_pb2.TableName.WORKING_SET
    ):
        return {
            "family_name": "State",
            "column_qualifier": b"Timestamp",
            "value": f"timestamp for {tenant_name}".encode(),
        }
    else:
        raise ValueError(f"Invalid table name: {table_name}")


def _make_row_key(tenant_name):
    return f"row_key_{tenant_name}".encode()


def _make_entry(table_name, tenant_name):
    kwargs = get_fake_row(table_name, tenant_name)
    event_mutation = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation(
        set_cell=bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation.SetCell(
            **kwargs,
        )
    )
    return bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_bigtable__pb2.MutateRowsRequest.Entry(
        row_key=_make_row_key(tenant_name), mutations=[event_mutation]
    )


def _check_read_resp(read_resp, row_key, table_name, tenant_name):
    responses = []
    for response in read_resp:
        print(f"response: {response}")
        responses.append(response)

    assert len(responses) == 1

    fake_row = get_fake_row(table_name, tenant_name)
    assert responses[0].chunks[0].row_key == row_key
    assert responses[0].chunks[0].family_name.value == fake_row["family_name"]
    assert responses[0].chunks[0].qualifier.value == fake_row["column_qualifier"]
    assert responses[0].chunks[0].value == fake_row["value"]


def _check_mutate_resp(mutate_resp):
    i = 0
    for response in mutate_resp:
        for entry in response.entries:
            i += 1
            assert entry.status.code == 0, "Error writing row"
    assert i == 1


def test_tenant_isolation(
    bigtable_proxy_server_sharded,
    bigtable_proxy_server_passthrough,
    table_names,
    token_exchange_server,
    tenants,
):
    # Verify we have at least one legacy tenant and at least one sharded tenant
    assert any(t.name == t.shard_namespace for t in tenants)
    assert any(t.name != t.shard_namespace for t in tenants)

    request_contexts = {}
    for t in tenants:
        request_contexts[t.id] = token_exchange_server.create_test_request_context(
            tenant_id=t.id,
            namespace=t.shard_namespace,
            scopes=[
                token_exchange_pb2.REQUEST_RW,
                token_exchange_pb2.CONTENT_RW,
                token_exchange_pb2.SETTINGS_RW,
            ],
        )

    def server(tenant):
        # Use passthrough mode for legacy tenants, and no passthrough for
        # sharded tenants
        if tenant.name == tenant.shard_namespace:
            return bigtable_proxy_server_passthrough
        else:
            return bigtable_proxy_server_sharded

    for table_name in table_names:
        print(f"testing table: {table_name}")

        # write to each tenant
        for t in tenants:
            print(f"writing to tenant: {t.name}")
            entries = [_make_entry(table_name, t.name)]
            mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
                tenant_id=t.id,
                table_name=table_name,
                entries=entries,
            )
            mutate_resp = server(t).MutateRows(
                mutate_rr, metadata=request_contexts[t.id].to_metadata()
            )
            _check_mutate_resp(mutate_resp)

        # Test different range types for each tenant
        range_tests = [
            # Explicit range
            bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowSet(
                row_ranges=[
                    bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowRange(
                        start_key_closed=b"a",
                        end_key_open=b"z",
                    )
                ]
            ),
            # Empty range
            bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowSet(
                row_ranges=[
                    bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowRange(),
                ]
            ),
            # No range (None)
            bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowSet(),
            #  No row set at all (None)
            None,
        ]

        # now read all rows from each tenant, and ensure we only get the data we
        # wrote, which should be exactly one row
        for t in tenants:
            print(f"reading from tenant: {t.name}")
            for range_val in range_tests:
                # Handle the different range test cases
                read_rr = bigtable_proxy_pb2.ReadRowsRequest(
                    tenant_id=t.id, table_name=table_name, rows=range_val
                )

                read_resp = server(t).ReadRows(
                    read_rr, metadata=request_contexts[t.id].to_metadata()
                )
                row_key = _make_row_key(t.name)
                _check_read_resp(read_resp, row_key, table_name, t.name)

        # Test CheckAndMutateRow isolation with the same row key across tenants
        common_row_key = b"common_row_key"

        # First, write data for each tenant using the common row key
        for t in tenants:
            print(f"writing to tenant {t.name} with common row key")

            fake_row = get_fake_row(table_name, t.name)
            mutation = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation(
                set_cell=bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation.SetCell(
                    family_name=fake_row["family_name"],
                    column_qualifier=fake_row["column_qualifier"],
                    value=fake_row["value"],
                )
            )

            # Create a single entry for MutateRowsRequest
            entry = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_bigtable__pb2.MutateRowsRequest.Entry(
                row_key=common_row_key,
                mutations=[mutation],
            )

            mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
                tenant_id=t.id,
                table_name=table_name,
                entries=[entry],
            )

            mutate_resp = server(t).MutateRows(
                mutate_rr, metadata=request_contexts[t.id].to_metadata()
            )
            _check_mutate_resp(mutate_resp)

        # Now test CheckAndMutateRow isolation
        for t in tenants:
            print(f"checking and mutating for tenant {t.name} with common row key")

            # Create a predicate filter that should match the data we wrote for this tenant
            fake_row = get_fake_row(table_name, t.name)
            predicate_filter = (
                bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowFilter(
                    value_regex_filter=fake_row["value"]
                )
            )

            # Create mutations for true and false cases
            true_value = f"updated value for {t.name}".encode()
            false_value = f"should not be used for {t.name}".encode()

            true_mutation = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation(
                set_cell=bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation.SetCell(
                    family_name=fake_row["family_name"],
                    column_qualifier=fake_row["column_qualifier"],
                    value=true_value,
                )
            )

            false_mutation = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation(
                set_cell=bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation.SetCell(
                    family_name=fake_row["family_name"],
                    column_qualifier=fake_row["column_qualifier"],
                    value=false_value,
                )
            )

            # Test tenant can access and modify its own data
            check_mutate_rr = bigtable_proxy_pb2.CheckAndMutateRowRequest(
                tenant_id=t.id,
                table_name=table_name,
                row_key=common_row_key,
                predicate_filter=predicate_filter,
                true_mutations=[true_mutation],
                false_mutations=[false_mutation],
            )

            check_mutate_resp = server(t).CheckAndMutateRow(
                check_mutate_rr, metadata=request_contexts[t.id].to_metadata()
            )

            # Verify predicate matched for tenant's own data
            assert (
                check_mutate_resp.predicate_matched
            ), f"Predicate should match for tenant {t.name}"

            # Read back to verify mutation was applied
            read_rr = bigtable_proxy_pb2.ReadRowsRequest(
                tenant_id=t.id,
                table_name=table_name,
                rows=bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowSet(
                    row_keys=[common_row_key]
                ),
            )

            read_resp = server(t).ReadRows(
                read_rr, metadata=request_contexts[t.id].to_metadata()
            )

            responses = []
            for response in read_resp:
                responses.append(response)

            assert len(responses) == 1
            assert responses[0].chunks[0].value == true_value

            # Now try to access another tenant's data using the same row key
            other_tenants = [other_t for other_t in tenants if other_t.id != t.id]
            if other_tenants:
                other_t = other_tenants[0]
                other_fake_row = get_fake_row(table_name, other_t.name)

                # Create a predicate filter that would match the other tenant's data
                other_predicate_filter = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowFilter(
                    value_regex_filter=other_fake_row["value"]
                )

                # Try to check and mutate using a predicate that would match the other tenant's data
                cross_tenant_check_mutate_rr = bigtable_proxy_pb2.CheckAndMutateRowRequest(
                    tenant_id=t.id,  # Current tenant's ID
                    table_name=table_name,
                    row_key=common_row_key,  # Same row key for both tenants
                    predicate_filter=other_predicate_filter,  # Other tenant's predicate
                    true_mutations=[true_mutation],
                    false_mutations=[false_mutation],
                )

                cross_tenant_check_mutate_resp = server(t).CheckAndMutateRow(
                    cross_tenant_check_mutate_rr,
                    metadata=request_contexts[t.id].to_metadata(),
                )

                # Predicate should NOT match when trying to access another tenant's data
                assert not cross_tenant_check_mutate_resp.predicate_matched, f"Tenant {t.name} should not be able to match predicate on {other_t.name}'s data"

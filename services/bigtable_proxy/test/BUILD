load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "bigtable_setup",
    testonly = True,
    srcs = ["bigtable_setup.py"],
    deps = [
        "//base/test_utils:bigtable_setup",
        requirement("google-cloud-bigtable"),
    ],
)

py_library(
    name = "conftest",
    testonly = True,
    srcs = [
        "conftest.py",
    ],
    data = [
        ":bigtable_setup",
    ],
    visibility = ["//services/bigtable_proxy/test:__subpackages__"],
    deps = [
        "//base/test_utils:bigtable_emulator",
        "//services/bigtable_proxy:bigtable_proxy_py_proto",
        "//services/bigtable_proxy/server:server_test_setup",
        "//services/tenant_watcher/server:tenant_watcher_test_setup",
        "//services/token_exchange/server:token_exchange_test_setup",
        requirement("pytest"),
        requirement("google-cloud-bigtable"),
    ],
)

py_library(
    name = "bigtable_test_utils",
    testonly = True,
    srcs = ["bigtable_test_utils.py"],
    visibility = ["//services/bigtable_proxy/test:__subpackages__"],
    deps = [
        "//services/bigtable_proxy:bigtable_proxy_py_proto",
        requirement("pytest"),
    ],
)

pytest_test(
    name = "read_write_test",
    size = "medium",
    timeout = "long",
    srcs = [
        "test_read_write.py",
    ],
    tags = ["exclusive"],
    deps = [
        ":bigtable_test_utils",
        ":conftest",
    ],
)

pytest_test(
    name = "tenant_isolation_test",
    size = "medium",
    timeout = "long",
    srcs = [
        "test_tenant_isolation.py",
    ],
    tags = ["exclusive"],
    deps = [
        ":bigtable_test_utils",
        ":conftest",
    ],
)

pytest_test(
    name = "read_filters_test",
    size = "medium",
    timeout = "long",
    srcs = [
        "test_read_filters.py",
    ],
    tags = ["exclusive"],
    deps = [
        ":bigtable_test_utils",
        ":conftest",
    ],
)

pytest_test(
    name = "mutate_rows_test",
    size = "medium",
    timeout = "long",
    srcs = [
        "test_mutate_rows.py",
    ],
    tags = ["exclusive"],
    deps = [
        ":bigtable_test_utils",
        ":conftest",
    ],
)

pytest_test(
    name = "check_and_mutate_test",
    size = "medium",
    timeout = "long",
    srcs = [
        "test_check_and_mutate.py",
    ],
    tags = ["exclusive"],
    deps = [
        ":bigtable_test_utils",
        ":conftest",
    ],
)

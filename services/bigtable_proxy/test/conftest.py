"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import os

import pytest

from base.test_utils import bigtable_emulator as bigtable_emulator_process_runner
from base.test_utils import process
from services.bigtable_proxy.server import server_test_setup
from services.lib.request_context.request_context import RequestContext
from services.tenant_watcher import tenant_watcher_pb2
from services.tenant_watcher.server import tenant_watcher_test_setup
from services.token_exchange import token_exchange_pb2
from services.token_exchange.server import token_exchange_test_setup


@pytest.fixture(scope="function")
def bigtable_emulator():
    """Fixture to start a Bigtable emulator."""
    yield from bigtable_emulator_process_runner.start_emulator()


@pytest.fixture(scope="function")
def bigtable_tables(bigtable_emulator):
    """Fixture to create a Bigtable table."""
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator
    env = os.environ.copy()

    with process.ServerManager(
        [
            "services/bigtable_proxy/test/bigtable_setup",
        ],
        env=env,
    ) as p:
        process.wait_for_line(
            p.stdout,
            r"done!",
            timeout_secs=30,
        )
        p.detach_stdout()
        print("Bigtable setup successful")


@pytest.fixture(
    params=[False, True],
    ids=lambda param: f"{'passthrough' if param else 'sharded'}",
)
def passthrough(request):
    """Fixture to test both passthrough and sharded modes."""
    return request.param


@pytest.fixture(scope="function")
def bigtable_proxy_server(
    passthrough,
    token_exchange_server,
    bigtable_tables,
    bigtable_emulator,
    tenant_watcher_fake_server_port,
):
    """Fixture to start the bigtable proxy server."""
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator
    yield from server_test_setup.start_test_bigtable_proxy(
        tex_endpoint=f"localhost:{token_exchange_server.port}",
        tenant_watcher_port=tenant_watcher_fake_server_port,
        passthrough=passthrough,
    )


@pytest.fixture(scope="function")
def bigtable_proxy_server_sharded(
    token_exchange_server,
    bigtable_tables,
    bigtable_emulator,
    tenant_watcher_fake_server_port,
):
    """Fixture to start the bigtable proxy server."""
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator
    yield from server_test_setup.start_test_bigtable_proxy(
        tex_endpoint=f"localhost:{token_exchange_server.port}",
        tenant_watcher_port=tenant_watcher_fake_server_port,
    )


@pytest.fixture(scope="function")
def bigtable_proxy_server_passthrough(
    token_exchange_server,
    bigtable_tables,
    bigtable_emulator,
    tenant_watcher_fake_server_port,
):
    """Fixture to start the bigtable proxy server."""
    os.environ["BIGTABLE_EMULATOR_HOST"] = bigtable_emulator
    yield from server_test_setup.start_test_bigtable_proxy(
        tex_endpoint=f"localhost:{token_exchange_server.port}",
        tenant_watcher_port=tenant_watcher_fake_server_port,
        passthrough=True,
    )


@pytest.fixture(
    params=[False, True],
    ids=lambda param: f"{'encrypted' if param else 'unencrypted'}",
)
def encrypted(request):
    """Fixture to test both encrypted and unencrypted modes."""
    return request.param


@pytest.fixture(scope="function")
def tenants(encrypted) -> list[tenant_watcher_pb2.Tenant]:
    """The default test tenants."""
    # All of the tenant IDs need to be the same length for bigtable proxy to
    # work correctly

    def encrypton_key(key_name: str):
        if encrypted:
            return "projects/foo/locations/global/keyRings/bar/cryptoKeys/" + key_name
        else:
            return ""

    return [
        tenant_watcher_pb2.Tenant(
            id="test_tenant_id_1",
            name="augment",
            shard_namespace="augment",
            cloud="CLOUD_DEV",
            auth_configuration=tenant_watcher_pb2.AuthConfiguration(
                domain="augmentcode.com",
            ),
            encryption_key_name=encrypton_key("key_1"),
        ),
        tenant_watcher_pb2.Tenant(
            id="test_tenant_id_2",
            name="foo1",
            shard_namespace="shard1",
            cloud="CLOUD_DEV",
            encryption_key_name=encrypton_key("key_2"),
        ),
        tenant_watcher_pb2.Tenant(
            id="test_tenant_id_3",
            name="foo2",
            shard_namespace="shard1",
            cloud="CLOUD_DEV",
            encryption_key_name=encrypton_key("key_3"),
        ),
    ]


@pytest.fixture(scope="function")
def tenant_watcher_fake_server_port(tenants):
    """Fixture to start the tenant watcher server."""
    yield from tenant_watcher_test_setup.start_fake_tenant_watcher_server(
        tenants=tenants,
    )


@pytest.fixture(scope="function")
def token_exchange_server(tenant_watcher_fake_server_port):
    """Fixture to create a request context."""
    config = token_exchange_test_setup.TokenExchangeConfig(
        port=0,  # Find some unused port
        tenant_watcher_endpoint=f"localhost:{tenant_watcher_fake_server_port}",
        service_token_configs=[
            token_exchange_test_setup.TokenForServiceConfig(
                regex=".*",
                scopes=["REQUEST_RW"],
                expiration="60m",
            )
        ],
    )
    yield from token_exchange_test_setup.start_token_exchange_server(config)


@pytest.fixture
def request_context(token_exchange_server) -> RequestContext:
    """Fixture to create a request context."""
    return token_exchange_server.create_test_request_context(
        tenant_id="test_tenant_id_1",
        namespace="augment",
        scopes=[
            token_exchange_pb2.REQUEST_RW,
            token_exchange_pb2.CONTENT_RW,
            token_exchange_pb2.SETTINGS_RW,
        ],
    )

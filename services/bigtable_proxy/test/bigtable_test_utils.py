"""Shared utilities for Bigtable proxy tests."""
# pyright: reportGeneralTypeIssues=false

import time
import uuid

from services.bigtable_proxy import bigtable_proxy_pb2

# Shorthand for commonly used protobuf types
# pyright: reportGeneralTypeIssues=false
RowFilter = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowFilter
RowRange = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowRange
RowSet = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.RowSet
MutateEntry = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_bigtable__pb2.MutateRowsRequest.Entry
Mutation = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.Mutation
TimestampRange = (
    bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.TimestampRange
)
ValueRange = bigtable_proxy_pb2.google_dot_bigtable_dot_v2_dot_data__pb2.ValueRange


def make_cell_mutation(family_name, column_qualifier, value, timestamp_micros=None):
    """Create a SetCell mutation."""

    return Mutation(
        set_cell=Mutation.SetCell(
            family_name=family_name,
            column_qualifier=column_qualifier,
            timestamp_micros=timestamp_micros,
            value=value,
        )
    )


def make_delete_mutation():
    """Create a DeleteFromRow mutation."""
    return Mutation(delete_from_row=Mutation.DeleteFromRow())


def make_delete_family_mutation(family_name):
    """Create a delete mutation that removes an entire column family."""
    return Mutation(
        delete_from_family=Mutation.DeleteFromFamily(family_name=family_name)
    )


def make_delete_column_mutation(family_name, column_qualifier):
    """Create a DeleteFromColumn mutation."""
    return Mutation(
        delete_from_column=Mutation.DeleteFromColumn(
            family_name=family_name,
            column_qualifier=column_qualifier,
        )
    )


def make_entry(row_key, mutation):
    """Create a MutateRowsRequest.Entry with the given row key and mutation."""
    return MutateEntry(row_key=row_key, mutations=[mutation])


def check_read_resp(
    read_resp, row_key, expected_value=None, family_name=None, column_qualifier=None
):
    """Check that read response contains expected data."""
    for response in read_resp:
        if response.chunks[0].row_key == row_key:
            if family_name and response.chunks[0].family_name.value != family_name:
                continue
            if (
                column_qualifier
                and response.chunks[0].qualifier.value != column_qualifier
            ):
                continue
            if expected_value and response.chunks[0].value != expected_value:
                continue
            return True
    return False


def check_read_with_retry(
    read_func,
    row_key=None,
    expected_value=None,
    family_name=None,
    column_qualifier=None,
    timeout=0.5,
    should_exist=True,
):
    """Check read results with retry logic."""
    start = time.time()
    while time.time() - start < timeout:
        response = read_func()
        if should_exist:
            if check_read_resp(
                response, row_key, expected_value, family_name, column_qualifier
            ):
                return True
        else:
            # Check row doesn't exist (empty response)
            if not list(response):
                return True
        time.sleep(0.1)

    # Final check without retry
    response = read_func()
    if should_exist:
        assert check_read_resp(
            response, row_key, expected_value, family_name, column_qualifier
        ), f"Expected row {row_key} with value {expected_value} not found"
    else:
        assert not list(response), "Expected empty response but found data"
    return True


def check_mutate_resp(mutate_resp):
    """Check that mutation response indicates success."""
    for response in mutate_resp:
        for entry in response.entries:
            assert entry.status.code == 0, f"Error writing row: {entry.status.message}"


def setup_row(
    bigtable_proxy_server,
    request_context,
    tenant_id,
    table_name,
    row_key,
    family_name="Info",
    column_qualifier=b"test_column",
    value=None,
):
    """Setup a row with the given value."""
    if value is None:
        return

    mut = make_cell_mutation(family_name, column_qualifier, value)
    entries = [make_entry(row_key, mut)]
    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)


def create_filter(filter_type, **kwargs):
    """Helper to create filters with less boilerplate."""
    if len(kwargs) == 0:
        # For filters that don't take parameters (like pass_all_filter)
        return RowFilter(**{filter_type: True})
    elif len(kwargs) == 1 and "value" in kwargs:
        # For filters that take a single value
        return RowFilter(**{filter_type: kwargs["value"]})
    else:
        # For filters that take a complex structure
        return RowFilter(**{filter_type: kwargs})


def generate_unique_row_key(prefix="test"):
    """Generate a unique row key with the given prefix."""
    return f"{prefix}_{uuid.uuid4().hex}".encode()

"""Test read, write, and mutate paths."""

import pytest

from services.bigtable_proxy import bigtable_proxy_pb2

from services.bigtable_proxy.test.bigtable_test_utils import (
    check_mutate_resp,
    check_read_with_retry,
    make_entry,
    make_cell_mutation,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>Range,
)


# These are (obviously) not very flexible right now, and should evolve as the tests evolve:
def _make_info_mutation(value):
    return make_cell_mutation("Info", b"Value", value, timestamp_micros=1643723400000)


def _check_read_with_retry(fn, row_key, value):
    check_read_with_retry(fn, row_key, value, "Info", b"Value")


def _verify_read_responses(responses, expected_key_values):
    """
    Verify that read responses contain exactly the expected keys and values.

    Args:
        responses: List of ReadRowsResponse objects
        expected_key_values: Dict mapping row_keys to expected values
    """
    assert len(responses) == len(
        expected_key_values
    ), f"Expected {len(expected_key_values)} responses, got {len(responses)}"

    found_keys = set()
    for response in responses:
        for chunk in response.chunks:
            found_keys.add(chunk.row_key)
            assert (
                chunk.row_key in expected_key_values
            ), f"Unexpected row key: {chunk.row_key}"
            assert (
                chunk.value == expected_key_values[chunk.row_key]
            ), f"Value mismatch for {chunk.row_key}: expected {expected_key_values[chunk.row_key]}, got {chunk.value}"

    assert found_keys == set(
        expected_key_values.keys()
    ), f"Missing keys: {set(expected_key_values.keys()) - found_keys}"


@pytest.mark.timeout(30)
def test_read_write(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test read, write, and mutate paths."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    # Write some data
    mut1 = _make_info_mutation(b"value1")
    entries = [make_entry(b"row_key1", mut1)]
    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        entries=entries,
    )
    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)
    # Ensure it's readable
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"row_key1"]),
    )
    _check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        b"row_key1",
        b"value1",
    )
    # Expect this to go to the old (per tenant) table even in mixed mode
    mut2 = _make_info_mutation(b"value2")
    # expect this to go to the old (per tenant) table
    check_mutate_rr = bigtable_proxy_pb2.CheckAndMutateRowRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        false_mutations=[mut2],
        row_key=b"row_key2",
    )
    bigtable_proxy_server.CheckAndMutateRow(
        check_mutate_rr, metadata=request_context.to_metadata()
    )
    # Ensure it's readable no matter what
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"row_key2"]),
    )
    _check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        b"row_key2",
        b"value2",
    )
    # now mutate both entries, one at a time
    mut1 = _make_info_mutation(b"value3")
    mut2 = _make_info_mutation(b"value4")
    entries = [make_entry(b"row_key1", mut1), make_entry(b"row_key2", mut2)]
    for entry in entries:
        mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
            tenant_id="test_tenant_id_1",
            table_name=table_name,
            entries=[entry],
        )
        mutate_resp = bigtable_proxy_server.MutateRows(
            mutate_rr, metadata=request_context.to_metadata()
        )
        check_mutate_resp(mutate_resp)
    # Ensure both are readable
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"row_key1"]),
    )
    _check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        b"row_key1",
        b"value3",
    )
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"row_key2"]),
    )
    _check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        b"row_key2",
        b"value4",
    )

    # Test read and write empty value
    mut = _make_info_mutation(b"")
    entries = [make_entry(b"row_key3", mut)]
    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        entries=entries,
    )
    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"row_key3"]),
    )
    _check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        b"row_key3",
        b"",
    )


@pytest.mark.timeout(30)
def test_read_with_empty_filter(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test ReadRows with empty filter (explicitly set to empty struct)."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER

    # Write test data
    mut = _make_info_mutation(b"test_value")
    entries = [make_entry(b"test_key", mut)]
    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        entries=entries,
    )
    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Test with empty filter (explicitly set to empty struct)
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"test_key"]),
        filter=RowFilter(),
    )

    _check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        b"test_key",
        b"test_value",
    )


@pytest.mark.timeout(30)
def test_row_selection_methods(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test different row selection methods."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER

    # Set up test data
    test_data = [
        (b"a/key1", b"value1"),
        (b"a/key2", b"value2"),
        (b"b/key1", b"value3"),
        (b"b/key2", b"value4"),
        (b"c/key1", b"value5"),
    ]

    for row_key, value in test_data:
        mut = _make_info_mutation(value)
        entries = [make_entry(row_key, mut)]
        mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
            tenant_id="test_tenant_id_1",
            table_name=table_name,
            entries=entries,
        )
        mutate_resp = bigtable_proxy_server.MutateRows(
            mutate_rr, metadata=request_context.to_metadata()
        )
        check_mutate_resp(mutate_resp)

    # Test 1: Prefix matching using row range
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(
            row_ranges=[
                RowRange(
                    start_key_closed=b"a/",
                    end_key_closed=b"a/\xff",  # End of 'a/' prefix
                )
            ]
        ),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    _verify_read_responses(responses, {b"a/key1": b"value1", b"a/key2": b"value2"})

    # Test 2: Row range variations
    ranges = [
        # Closed/Open range
        RowRange(
            start_key_closed=b"b/",
            end_key_open=b"c/",
        ),
        # Open/Closed range
        RowRange(
            start_key_open=b"a/key1",
            end_key_closed=b"a/key2",
        ),
        # Closed/Closed range
        RowRange(
            start_key_closed=b"c/",
            end_key_closed=b"c/key2",
        ),
    ]

    for row_range in ranges:
        read_rr = bigtable_proxy_pb2.ReadRowsRequest(
            tenant_id="test_tenant_id_1",
            table_name=table_name,
            rows=RowSet(row_ranges=[row_range]),
        )
        responses = list(
            bigtable_proxy_server.ReadRows(
                read_rr, metadata=request_context.to_metadata()
            )
        )
        # Verify responses based on range type
        if row_range.start_key_closed == b"b/":
            _verify_read_responses(
                responses, {b"b/key1": b"value3", b"b/key2": b"value4"}
            )
        elif row_range.start_key_open == b"a/key1":
            _verify_read_responses(responses, {b"a/key2": b"value2"})
        elif row_range.start_key_closed == b"c/":
            _verify_read_responses(responses, {b"c/key1": b"value5"})

    # Test 3a: Nil rows should find all rows
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    all_rows = {
        b"a/key1": b"value1",
        b"a/key2": b"value2",
        b"b/key1": b"value3",
        b"b/key2": b"value4",
        b"c/key1": b"value5",
    }
    _verify_read_responses(responses, all_rows)

    # Test 3b: Empty RowSet (all rows)
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    _verify_read_responses(responses, all_rows)

    # Test 3c: RowSet with empty row_keys should return in invalid arguments error
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[]),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    _verify_read_responses(responses, all_rows)

    # Test 3d: Empty row ranges should return in invalid arguments error
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_ranges=[RowRange()]),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    _verify_read_responses(responses, all_rows)

    # Test 4: Read specific row
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"a/key1"]),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    _verify_read_responses(responses, {b"a/key1": b"value1"})

    # Test 5: Read specific row that does not exist
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"nonexistent"]),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    assert len(responses) == 0  # Should find nothing

    # Test 6: read multiple specific rows
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(row_keys=[b"a/key1", b"b/key1"]),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    _verify_read_responses(responses, {b"a/key1": b"value1", b"b/key1": b"value3"})

    # Test 7: Combination of methods
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id="test_tenant_id_1",
        table_name=table_name,
        rows=RowSet(
            row_keys=[b"a/key1"],
            row_ranges=[
                RowRange(
                    start_key_closed=b"b/",
                    end_key_open=b"c/",
                )
            ],
        ),
    )
    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )
    _verify_read_responses(
        responses, {b"a/key1": b"value1", b"b/key1": b"value3", b"b/key2": b"value4"}
    )

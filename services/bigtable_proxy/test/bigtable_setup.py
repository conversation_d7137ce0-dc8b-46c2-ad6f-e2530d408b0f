from base.test_utils import bigtable_setup

LEGACY_NAMESPACES = ["augment"]
CENTRAL_NAMESPACE = "central"

OLD_TABLES = {
    "content-manager": ["Info", "Content", "Checkpoint"],
}

ALL_TABLES = {
    "content-manager": ["Info", "Content", "Checkpoint"],
    "settings": ["Settings"],
    "github": ["Current", "Indexed", "RefInfo", "FileInfos"],
    "share": ["Chat"],
    "slack-mappings": ["Mappings"],
    "remote-agents": ["Config"],
    "working-set": ["State"],
}


def create():
    tbls = []

    for NAMESPACE in LEGACY_NAMESPACES:
        for table, columns in OLD_TABLES.items():
            tbls.append(
                next(
                    bigtable_setup.create_table_no_del(
                        table_id=bigtable_setup.BigtableTable(
                            table_name=NAMESPACE + "-" + table
                        ),
                        family_columns=columns,
                    )
                )
            )

    for table, columns in ALL_TABLES.items():
        tbls.append(
            next(
                bigtable_setup.create_table_no_del(
                    table_id=bigtable_setup.BigtableTable(
                        table_name=CENTRAL_NAMESPACE + "-" + table
                    ),
                    family_columns=columns,
                )
            )
        )

    print("done!")
    return tbls


if __name__ == "__main__":
    tbls = create()

"""Test MutateRows."""

import pytest

from services.bigtable_proxy import bigtable_proxy_pb2

from services.bigtable_proxy.test.bigtable_test_utils import (
    check_mutate_resp,
    check_read_with_retry,
    make_delete_column_mutation,
    make_delete_family_mutation,
    make_delete_mutation,
    make_entry,
    RowSet,
    Mutation,
)


@pytest.mark.timeout(30)
def test_delete_from_row(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test DeleteFromRow mutation which removes an entire row."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    tenant_id = "test_tenant_id_1"
    row_key = b"delete_row_test_key"

    # First, write data to the row
    event_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Info",
            column_qualifier=b"test_column",
            value=b"test_value",
        )
    )

    entries = [make_entry(row_key, event_mutation)]
    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Verify the row exists
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )

    check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        row_key,
        b"test_value",
    )

    # Now delete the entire row
    delete_mutation = make_delete_mutation()
    entries = [make_entry(row_key, delete_mutation)]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Verify the row no longer exists
    check_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        row_key,
        should_exist=False,
    )


@pytest.mark.timeout(30)
def test_delete_from_family(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test DeleteFromFamily mutation which removes an entire column family."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    tenant_id = "test_tenant_id_1"
    row_key = b"delete_family_test_key"

    # Write data to two different column families
    family1_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Info",
            column_qualifier=b"test_column",
            value=b"info_value",
        )
    )

    family2_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Content",
            column_qualifier=b"test_column",
            value=b"content_value",
        )
    )

    entries = [
        make_entry(row_key, family1_mutation),
        make_entry(row_key, family2_mutation),
    ]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Delete just the Info family
    delete_family_mutation = make_delete_family_mutation("Info")
    entries = [make_entry(row_key, delete_family_mutation)]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Verify the Info family is gone but Content family still exists
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )

    read_resp = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )

    # Check that we only have the Content family data
    assert len(read_resp) > 0, "Row should still exist"
    found_info = False
    found_content = False

    for chunk in read_resp[0].chunks:
        if chunk.family_name.value == "Content":
            found_content = True
        if chunk.family_name.value == "Info":
            found_info = True

    assert found_content, "Content family should still exist"
    assert not found_info, "Info family should be deleted"


@pytest.mark.timeout(30)
def test_delete_from_column(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test DeleteFromColumn mutation which removes all cells in a column."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    tenant_id = "test_tenant_id_1"
    row_key = b"delete_column_test_key"

    # Write data to two different columns in the same family
    column1_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Info",
            column_qualifier=b"column1",
            value=b"value1",
        )
    )

    column2_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Info",
            column_qualifier=b"column2",
            value=b"value2",
        )
    )

    entries = [
        make_entry(row_key, column1_mutation),
        make_entry(row_key, column2_mutation),
    ]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Delete just column1
    delete_column_mutation = make_delete_column_mutation("Info", b"column1")
    entries = [make_entry(row_key, delete_column_mutation)]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Verify column1 is gone but column2 still exists
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )

    read_resp = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )

    # Check that we only have column2 data
    assert len(read_resp) > 0, "Row should still exist"
    found_column1 = False
    found_column2 = False

    for chunk in read_resp[0].chunks:
        if chunk.qualifier.value == b"column1":
            found_column1 = True
        if chunk.qualifier.value == b"column2":
            found_column2 = True

    assert not found_column1, "column1 should be deleted"
    assert found_column2, "column2 should still exist"


@pytest.mark.timeout(30)
def test_batch_delete_multiple_rows(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test batch deletion of multiple rows in a single MutateRows request."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    tenant_id = "test_tenant_id_1"
    row_keys = [b"batch_delete_key1", b"batch_delete_key2", b"batch_delete_key3"]

    # Write data to multiple rows
    entries = []
    for i, row_key in enumerate(row_keys):
        mutation = Mutation(
            set_cell=Mutation.SetCell(
                family_name="Info",
                column_qualifier=b"test_column",
                value=f"value{i+1}".encode(),
            )
        )
        entries.append(make_entry(row_key, mutation))

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Verify all rows exist
    for i, row_key in enumerate(row_keys):
        read_rr = bigtable_proxy_pb2.ReadRowsRequest(
            tenant_id=tenant_id,
            table_name=table_name,
            rows=RowSet(row_keys=[row_key]),
        )

        check_read_with_retry(
            lambda: bigtable_proxy_server.ReadRows(
                read_rr, metadata=request_context.to_metadata()
            ),
            row_key,
            f"value{i+1}".encode(),
        )

    # Delete all rows in a single batch request
    delete_entries = []
    for row_key in row_keys:
        delete_mutation = make_delete_mutation()
        delete_entries.append(make_entry(row_key, delete_mutation))

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=delete_entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Verify all rows are deleted
    for row_key in row_keys:
        read_rr = bigtable_proxy_pb2.ReadRowsRequest(
            tenant_id=tenant_id,
            table_name=table_name,
            rows=RowSet(row_keys=[row_key]),
        )

        check_read_with_retry(
            lambda: bigtable_proxy_server.ReadRows(
                read_rr, metadata=request_context.to_metadata()
            ),
            row_key,
            should_exist=False,
        )


@pytest.mark.timeout(30)
def test_invalid_mutations(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test invalid mutations."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
    tenant_id = "test_tenant_id_1"
    row_key = b"invalid_mutations_test_key"

    # Write data to two different column families
    family1_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Info",
            column_qualifier=b"test_column",
            value=b"info_value",
        )
    )

    family2_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Content",
            column_qualifier=b"test_column",
            value=b"content_value",
        )
    )

    entries = [
        make_entry(row_key, family1_mutation),
        make_entry(row_key, family2_mutation),
    ]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
        entries=entries,
    )

    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

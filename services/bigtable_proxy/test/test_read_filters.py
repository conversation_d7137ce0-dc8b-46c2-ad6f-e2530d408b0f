"""Test ReadRows filters."""

import pytest
import grpc
from services.bigtable_proxy import bigtable_proxy_pb2

from services.bigtable_proxy.test.bigtable_test_utils import (
    check_mutate_resp,
    create_filter,
    make_entry,
    make_cell_mutation,
    RowFilter,
    RowSet,
    RowRange,
    Mutation,
    TimestampRange,
)


@pytest.mark.timeout(30)
def test_row_filters(
    bigtable_emulator,
    request_context,
    bigtable_tables,
    bigtable_proxy_server,
):
    """Test different row filter types."""
    table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER

    # Set up test data with multiple families, columns, and values
    test_data = [
        # Row key, family, qualifier, value
        (b"filter/key1", "Info", b"col1", b"value1"),
        (b"filter/key1", "Info", b"col2", b"value2"),
        (b"filter/key1", "Content", b"col1", b"content1"),
        (b"filter/key2", "Info", b"col1", b"value3"),
        (b"filter/key2", "Info", b"col2", b"value4"),
        (b"filter/key2", "Content", b"col1", b"content2"),
        (b"condition/key1", "Info", b"col1", b"info_value1"),
        (b"condition/key1", "Info", b"col2", b"info_value2"),
        (b"condition/key1", "Content", b"col1", b"content_value1"),
        (b"condition/key1", "Content", b"col2", b"content_value2"),
        (b"condition/key2", "Content", b"col1", b"content_only1"),
        (b"condition/key2", "Content", b"col2", b"content_only2"),
        (b"composite/key1", "Info", b"column1", b"test_value"),
        (b"composite/key2", "Checkpoint", b"column1", b"test_value"),
        (b"composite/key3", "Info", b"column2", b"test_value"),
        (b"composite/key4", "Checkpoint", b"column1", b"other_value"),
    ]

    # Write test data
    for row_key, family, qualifier, value in test_data:
        mut = make_cell_mutation(family, qualifier, value)
        entries = [make_entry(row_key, mut)]
        mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
            tenant_id="test_tenant_id_1",
            table_name=table_name,
            entries=entries,
        )
        mutate_resp = bigtable_proxy_server.MutateRows(
            mutate_rr, metadata=request_context.to_metadata()
        )
        check_mutate_resp(mutate_resp)

    # Helper function to execute a read request with a filter
    def execute_read(filter_obj, row_keys=None, row_range=None):
        row_set = RowSet()
        if row_keys:
            row_set.row_keys.extend(row_keys)
        if row_range:
            row_set.row_ranges.append(row_range)

        read_rr = bigtable_proxy_pb2.ReadRowsRequest(
            tenant_id="test_tenant_id_1",
            table_name=table_name,
            rows=row_set,
            filter=filter_obj,
        )
        return list(
            bigtable_proxy_server.ReadRows(
                read_rr, metadata=request_context.to_metadata()
            )
        )

    # Test 1: Chain filter (AND)
    family_filter = create_filter("family_name_regex_filter", value="Info")
    column_filter = create_filter("column_qualifier_regex_filter", value=b"col1")
    chain_filter = create_filter(
        "chain", value=RowFilter.Chain(filters=[family_filter, column_filter])
    )

    responses = execute_read(chain_filter, row_keys=[b"filter/key1", b"filter/key2"])
    # Should return 2 rows but only Info:col1 cells
    assert len(responses) == 2, f"Chain filter: Expected 2 rows, got {len(responses)}"
    for resp in responses:
        assert (
            len(resp.chunks) == 1
        ), f"Chain filter: Expected 1 chunk, got {len(resp.chunks)}"
        assert (
            resp.chunks[0].family_name.value == "Info"
        ), f"Chain filter: Expected family name 'Info', got {resp.chunks[0].family_name.value}"
        assert (
            resp.chunks[0].qualifier.value == b"col1"
        ), f"Chain filter: Expected qualifier 'col1', got {resp.chunks[0].qualifier.value}"

    # Test 2: Family name regex filter
    family_filter = create_filter("family_name_regex_filter", value="Content")
    responses = execute_read(family_filter, row_keys=[b"filter/key1", b"filter/key2"])
    # Should return only Content family cells
    assert len(responses) == 2, f"Family filter: Expected 2 rows, got {len(responses)}"
    for resp in responses:
        assert (
            len(resp.chunks) == 1
        ), f"Family filter: Expected 1 chunk, got {len(resp.chunks)}"
        assert (
            resp.chunks[0].family_name.value == "Content"
        ), f"Family filter: Expected family name 'Content', got {resp.chunks[0].family_name.value}"

    # Test 3: Column qualifier regex filter
    column_filter = create_filter("column_qualifier_regex_filter", value=b"col2")
    responses = execute_read(column_filter, row_keys=[b"filter/key1", b"filter/key2"])
    # Should return only col2 cells
    assert len(responses) == 2, f"Column filter: Expected 2 rows, got {len(responses)}"
    for resp in responses:
        assert (
            len(resp.chunks) == 1
        ), f"Column filter: Expected 1 chunk, got {len(resp.chunks)}"
        assert (
            resp.chunks[0].qualifier.value == b"col2"
        ), f"Column filter: Expected qualifier 'col2', got {resp.chunks[0].qualifier.value}"

    # Test 4: Value regex filter
    value_filter = create_filter("value_regex_filter", value=b"content.*")
    responses = execute_read(value_filter, row_keys=[b"filter/key1", b"filter/key2"])
    # Should return only cells with values matching "content.*"
    assert len(responses) == 2, f"Value filter: Expected 2 rows, got {len(responses)}"
    for resp in responses:
        assert (
            len(resp.chunks) == 1
        ), f"Value filter: Expected 1 chunk, got {len(resp.chunks)}"
        assert resp.chunks[
            0
        ].value.startswith(
            b"content"
        ), f"Value filter: Expected value to start with 'content', got {resp.chunks[0].value}"

    # Test 5: Cells per row limit filter
    limit_filter = create_filter("cells_per_row_limit_filter", value=1)
    responses = execute_read(limit_filter, row_keys=[b"filter/key1"])
    # Should return only 1 cell per row
    assert len(responses) == 1, f"Limit filter: Expected 1 row, got {len(responses)}"
    assert (
        len(responses[0].chunks) == 1
    ), f"Limit filter: Expected 1 chunk, got {len(responses[0].chunks)}"

    # Test 6: Cells per column limit filter
    column_limit_filter = create_filter("cells_per_column_limit_filter", value=1)
    responses = execute_read(column_limit_filter, row_keys=[b"filter/key1"])
    # Should return at most 1 cell per column
    assert (
        len(responses) == 1
    ), f"Column limit filter: Expected 1 row, got {len(responses)}"
    assert (
        len(responses[0].chunks) == 3
    ), f"Column limit filter: Expected 3 chunks, got {len(responses[0].chunks)}"

    # Test 7: Pass all filter
    pass_filter = create_filter("pass_all_filter")
    responses = execute_read(pass_filter, row_keys=[b"filter/key1"])
    # Should return all cells
    assert len(responses) == 1, f"Pass filter: Expected 1 row, got {len(responses)}"
    assert (
        len(responses[0].chunks) == 3
    ), f"Pass filter: Expected 3 chunks, got {len(responses[0].chunks)}"

    # Test 8: Block all filter
    block_filter = create_filter("block_all_filter")
    responses = execute_read(block_filter, row_keys=[b"filter/key1"])
    # Should return no rows at all
    assert len(responses) == 0, f"Block filter: Expected 0 rows, got {len(responses)}"

    # Test 9: Condition filter
    condition_filter = create_filter(
        "condition",
        value=RowFilter.Condition(
            predicate_filter=create_filter("family_name_regex_filter", value="Info"),
            true_filter=create_filter("column_qualifier_regex_filter", value=b"col1"),
            false_filter=create_filter("column_qualifier_regex_filter", value=b"col2"),
        ),
    )
    responses = execute_read(
        condition_filter, row_keys=[b"condition/key1", b"condition/key2"]
    )
    # Should return 2 rows
    assert (
        len(responses) == 2
    ), f"Condition filter: Expected 2 rows, got {len(responses)}"

    # Process responses by row key for clearer testing
    response_by_key = {resp.chunks[0].row_key: resp for resp in responses}

    # Check key1 (predicate TRUE): should return only col1 cells
    key1_resp = response_by_key[b"condition/key1"]
    assert all(
        chunk.qualifier.value == b"col1" for chunk in key1_resp.chunks
    ), f"Condition filter: Expected qualifier 'col1', got {[chunk.qualifier.value for chunk in key1_resp.chunks]}"
    assert (
        len(key1_resp.chunks) == 2
    ), f"Condition filter: Expected 2 chunks, got {len(key1_resp.chunks)}"

    # Check key2 (predicate FALSE): should return only col2 cells
    key2_resp = response_by_key[b"condition/key2"]
    assert all(
        chunk.qualifier.value == b"col2" for chunk in key2_resp.chunks
    ), f"Condition filter: Expected qualifier 'col2', got {[chunk.qualifier.value for chunk in key2_resp.chunks]}"
    assert (
        len(key2_resp.chunks) == 1
    ), f"Condition filter: Expected 1 chunk, got {len(key2_resp.chunks)}"

    # Test 10: Strip value transformer
    strip_filter = create_filter("strip_value_transformer")
    responses = execute_read(strip_filter, row_keys=[b"filter/key1"])
    # Should return cells but with empty values
    assert len(responses) == 1, f"Strip filter: Expected 1 row, got {len(responses)}"
    assert (
        len(responses[0].chunks) == 3
    ), f"Strip filter: Expected 3 chunks, got {len(responses[0].chunks)}"
    for chunk in responses[0].chunks:
        assert (
            chunk.value == b""
        ), f"Strip filter: Expected empty value, got {chunk.value}"

    # Test 11: Composite filter with value regex
    # Create a row range for composite tests
    composite_range = RowRange(
        start_key_closed=b"composite/",
        end_key_open=b"composite/\xff",  # End of 'composite/' prefix
    )

    value_regex_filter = create_filter("value_regex_filter", value=b"test_value")
    column_filter = create_filter("column_qualifier_regex_filter", value=b"column1")
    chain_filter = create_filter(
        "chain", value=RowFilter.Chain(filters=[column_filter, value_regex_filter])
    )

    responses = execute_read(chain_filter, row_range=composite_range)
    # Assert based on expected matches
    assert (
        len(responses) == 2
    ), f"Composite filter: Expected 2 rows, got {len(responses)}"
    for response in responses:
        assert (
            len(response.chunks) == 1
        ), f"Composite filter: Expected 1 chunk, got {len(response.chunks)}"
        assert (
            response.chunks[0].qualifier.value == b"column1"
        ), f"Composite filter: Expected qualifier 'column1', got {response.chunks[0].qualifier.value}"
        assert (
            response.chunks[0].value == b"test_value"
        ), f"Composite filter: Expected value 'test_value', got {response.chunks[0].value}"

    # Test 12: Condition filter with value regex in predicate
    condition_filter = create_filter(
        "condition",
        value=RowFilter.Condition(
            predicate_filter=chain_filter,
            true_filter=create_filter("pass_all_filter"),
            false_filter=create_filter("block_all_filter"),
        ),
    )
    responses = execute_read(condition_filter, row_range=composite_range)
    # Assert based on expected matches
    assert len(responses) == 2  # Assuming 2 rows match the criteria
    for response in responses:
        assert (
            len(response.chunks) == 1
        ), f"Condition filter (value regex in predicate): Expected 1 chunk, got {len(response.chunks)}"
        assert (
            response.chunks[0].qualifier.value == b"column1"
        ), f"Condition filter (value regex in predicate): Expected qualifier 'column1', got {response.chunks[0].qualifier.value}"
        assert (
            response.chunks[0].value == b"test_value"
        ), f"Condition filter (value regex in predicate): Expected value 'test_value', got {response.chunks[0].value}"

    # Test 13: Condition filter with value regex in true filter
    condition_filter = create_filter(
        "condition",
        value=RowFilter.Condition(
            predicate_filter=create_filter("family_name_regex_filter", value="Info"),
            true_filter=chain_filter,
            false_filter=create_filter("block_all_filter"),
        ),
    )
    responses = execute_read(condition_filter, row_range=composite_range)
    # Assert based on expected matches
    assert (
        len(responses) == 1
    ), f"Condition filter (value regex in true filter): Expected 1 row, got {len(responses)}"
    assert (
        responses[0].chunks[0].row_key == b"composite/key1"
    ), f"Condition filter (value regex in true filter): Expected row key 'composite/key1', got {responses[0].chunks[0].row_key}"
    assert (
        responses[0].chunks[0].family_name.value == "Info"
    ), f"Condition filter (value regex in true filter): Expected family name 'Info', got {responses[0].chunks[0].family_name.value}"
    assert (
        responses[0].chunks[0].qualifier.value == b"column1"
    ), f"Condition filter (value regex in true filter): Expected qualifier 'column1', got {responses[0].chunks[0].qualifier.value}"
    assert (
        responses[0].chunks[0].value == b"test_value"
    ), f"Condition filter (value regex in true filter): Expected value 'test_value', got {responses[0].chunks[0].value}"

    # Test 14: Condition filter with value regex in false filter
    condition_filter = create_filter(
        "condition",
        value=RowFilter.Condition(
            predicate_filter=create_filter("family_name_regex_filter", value="Info"),
            true_filter=create_filter("block_all_filter"),
            false_filter=chain_filter,
        ),
    )
    responses = execute_read(condition_filter, row_range=composite_range)
    # Assert based on expected matches
    assert len(responses) == 1  # Only one row matches all criteria
    assert (
        responses[0].chunks[0].row_key == b"composite/key2"
    ), f"Condition filter (value regex in false filter): Expected row key 'composite/key2', got {responses[0].chunks[0].row_key}"
    assert (
        responses[0].chunks[0].family_name.value == "Checkpoint"
    ), f"Condition filter (value regex in false filter): Expected family name 'Checkpoint', got {responses[0].chunks[0].family_name.value}"
    assert (
        responses[0].chunks[0].qualifier.value == b"column1"
    ), f"Condition filter (value regex in false filter): Expected qualifier 'column1', got {responses[0].chunks[0].qualifier.value}"
    assert (
        responses[0].chunks[0].value == b"test_value"
    ), f"Condition filter (value regex in false filter): Expected value 'test_value', got {responses[0].chunks[0].value}"

    # Test 15: Timestamp range filter
    # Use fixed timestamps to make the test deterministic
    base_timestamp = 1600000000000000  # Some time in September 2020 in microseconds
    timestamp_test_data = [
        # Row key, family, qualifier, value, timestamp_micros
        (
            b"timestamp/key1",
            "Info",
            b"col1",
            b"value_old",
            base_timestamp,
        ),  # base time
        (
            b"timestamp/key1",
            "Info",
            b"col1",
            b"value_new",
            base_timestamp + 10000000,
        ),  # base + 10 seconds
        (
            b"timestamp/key2",
            "Info",
            b"col1",
            b"value_old",
            base_timestamp + 1000000,
        ),  # base + 1 second
        (
            b"timestamp/key2",
            "Info",
            b"col1",
            b"value_new",
            base_timestamp + 5000000,
        ),  # base + 5 seconds
    ]

    # Write timestamp test data
    for row_key, family, qualifier, value, timestamp_micros in timestamp_test_data:
        mut = Mutation(
            set_cell=Mutation.SetCell(
                family_name=family,
                column_qualifier=qualifier,
                timestamp_micros=timestamp_micros,
                value=value,
            )
        )
        entries = [make_entry(row_key, mut)]
        mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
            tenant_id="test_tenant_id_1",
            table_name=table_name,
            entries=entries,
        )
        mutate_resp = bigtable_proxy_server.MutateRows(
            mutate_rr, metadata=request_context.to_metadata()
        )
        check_mutate_resp(mutate_resp)

    # Test timestamp range filter - newer values only (> 5 seconds after base)
    timestamp_range = TimestampRange(
        start_timestamp_micros=base_timestamp + 5000000,  # base + 5 seconds
        end_timestamp_micros=base_timestamp + 15000000,  # base + 15 seconds
    )
    timestamp_filter = create_filter("timestamp_range_filter", value=timestamp_range)

    responses = execute_read(
        timestamp_filter, row_keys=[b"timestamp/key1", b"timestamp/key2"]
    )

    # Create a map of row key to value for easier verification
    response_values = {}
    for resp in responses:
        for chunk in resp.chunks:
            response_values[chunk.row_key] = chunk.value

    # Verify we got the newer values
    assert (
        response_values[b"timestamp/key1"] == b"value_new"
    ), f"Timestamp range filter (newer values only): Expected value 'value_new', got {response_values[b'timestamp/key1']}"
    assert (
        response_values[b"timestamp/key2"] == b"value_new"
    ), f"Timestamp range filter (newer values only): Expected value 'value_new', got {response_values[b'timestamp/key2']}"

    # Test timestamp range filter - older values only (< 5 seconds after base)
    timestamp_range = TimestampRange(
        start_timestamp_micros=base_timestamp,  # base time
        end_timestamp_micros=base_timestamp + 5000000,  # base + 5 seconds
    )
    timestamp_filter = create_filter("timestamp_range_filter", value=timestamp_range)

    responses = execute_read(
        timestamp_filter, row_keys=[b"timestamp/key1", b"timestamp/key2"]
    )

    # Create a map of row key to value for easier verification
    response_values = {}
    for resp in responses:
        for chunk in resp.chunks:
            response_values[chunk.row_key] = chunk.value

    # Verify we got the older values
    assert (
        response_values[b"timestamp/key1"] == b"value_old"
    ), f"Timestamp range filter (older values only): Expected value 'value_old', got {response_values[b'timestamp/key1']}"
    assert (
        response_values[b"timestamp/key2"] == b"value_old"
    ), f"Timestamp range filter (older values only): Expected value 'value_old', got {response_values[b'timestamp/key2']}"

    # Test 16:column qualifier regex filter prefix
    column_filter = create_filter("column_qualifier_regex_filter", value=b"col")
    responses = execute_read(column_filter, row_keys=[b"filter/key1", b"filter/key2"])
    # Regex must match entire column qualifier, so this matches nothing.
    assert (
        len(responses) == 0
    ), "Column prefix filter matches nothing if match is incomplete"

    column_filter = create_filter("column_qualifier_regex_filter", value=b"col.*")
    responses = execute_read(column_filter, row_keys=[b"filter/key1", b"filter/key2"])

    # Should return all columns starting with 'col'
    assert (
        len(responses) == 2
    ), f"Column prefix filter: Expected 2 rows, got {len(responses)}"
    for resp in responses:
        assert (
            len(resp.chunks) == 3
        ), f"Column prefix filter: Expected 3 chunks, got {len(resp.chunks)}"

    # Test 17: Unsupported filters
    unsupported_filters = [
        # Interleave filter (OR)
        create_filter(
            "interleave",
            value=RowFilter.Interleave(
                filters=[
                    create_filter("column_qualifier_regex_filter", value=b"col1"),
                    create_filter("value_regex_filter", value=b"value1"),
                ]
            ),
        ),
        # Interleave filter inside a conditional
        create_filter(
            "condition",
            value=RowFilter.Condition(
                predicate_filter=create_filter(
                    "interleave",
                    value=RowFilter.Interleave(
                        filters=[
                            create_filter(
                                "column_qualifier_regex_filter", value=b"col1"
                            ),
                            create_filter("value_regex_filter", value=b"value1"),
                        ]
                    ),
                ),
                true_filter=create_filter("pass_all_filter"),
                false_filter=create_filter("block_all_filter"),
            ),
        ),
        # Sink filter
        create_filter("sink"),
        # Sink filter inside a conditional
        create_filter(
            "condition",
            value=RowFilter.Condition(
                predicate_filter=create_filter("pass_all_filter"),
                true_filter=create_filter("sink"),
                false_filter=create_filter("block_all_filter"),
            ),
        ),
    ]

    # Test each unsupported filter
    for i, unsupported_filter in enumerate(unsupported_filters):
        read_rr = bigtable_proxy_pb2.ReadRowsRequest(
            tenant_id="test_tenant_id_1",
            table_name=table_name,
            rows=RowSet(row_keys=[b"filter/key1"]),
            filter=unsupported_filter,
        )

        # Should raise an exception with "unsupported filter" message
        with pytest.raises(grpc.RpcError) as excinfo:
            list(
                bigtable_proxy_server.ReadRows(
                    read_rr, metadata=request_context.to_metadata()
                )
            )

        # Check that we got the expected error
        assert "unsupported filter" in str(
            excinfo.value
        ), f"Unsupported filter test {i} failed"

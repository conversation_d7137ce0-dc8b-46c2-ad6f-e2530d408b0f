load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "bigtable_proxy_proto",
    srcs = ["bigtable_proxy.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@googleapis//google/bigtable/v2:bigtable_proto",
    ],
)

py_grpc_library(
    name = "bigtable_proxy_py_proto",
    protos = [":bigtable_proxy_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//third_party/proto:googleapis_bigtable_v2_py_proto",
    ],
)

go_grpc_library(
    name = "bigtable_proxy_go_proto",
    importpath = "github.com/augmentcode/augment/services/bigtable_proxy/proto",
    proto = ":bigtable_proxy_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
    ],
)

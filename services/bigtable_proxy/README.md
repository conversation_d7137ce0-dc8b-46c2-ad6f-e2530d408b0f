# Bigtable Proxy Service

The bigtable proxy is the service that mediates all access to per-tenant persistent data.

The main goals of the service (as part of the "1000/1000" multi-tenancy project) are:
- Enforce separation of tenant data at a single location.
- Support migration of tenant data between different namespaces.
- Encapsulate our multi-tenant data security strategy (tenant separation, bigtable IAM policies, etc.) in a single service whose logic is easy to validate.

The main job of the bigtable proxy is to transparently prefix all bigtable row keys with the ID of the tenant their data belongs to. This includes removing tenant ID from result keys. This behavior is currently gated behind a deploy flag (passthroughBigtableProxy=false). This design allows us to add bigtable proxy to legacy deployments and reuse their existing (per-tenant) bigtable tables without maintaining separate client logic for legacy vs multi-tenant (shard) namespaces.

Some data in the auth system is either not per-tenant, or is used in paths where a tenant ID isn't known yet. This data is kept in separate bigtable tables and is allowed to be accessed without going through the bigtable proxy.

## API

The bigtable proxy API is defined partly in terms of google bigtable API data types and partly in terms of Augment-specific custom types. Today, the delta is just the way we specify table and namespace targets, but we could potentially move key definitions or even the entire API to Augment-specified types. Conversely, we could move the key reformatting into clients (to save unnecessary reformatting, for example), limit the bigtable proxy to just checking (by scanning the keys) that the correct prefixes are in place, and use types exactly matching the google bigtable API.

## Dependencies

The bigtable proxy service depends on access to our bigtable instance and on the token exchange service.

## Security considerations

### Where should this run

The bigtable proxy should run in each namespace (single-tenant or multi-tenant). It shouldn't run in central namespaces.

### Authentication and authorization

The content manager is allowed to call this service, either with a tenant-aware service token or with a background task service token (providing the desired tenant ID in the request). The bigtable proxy will validate this token.

### Permissions and secrets

The bigtable proxy is the only non-central service that should have access to bigtable proper in the multi-tenant world (via IAM policies).

### Persistence

The bigtable proxy service is stateless and doesn't persist any data of its own. All persistence operations from the rest of the system are expected to pass through it.

### Privacy considerations

The bigtable proxy manages reads and writes of highly sensitive data (e.g. content blobs) which it must avoid leaking. Metadata such as key strings, table names, and tenant IDs is generally not considered sensitive or PII at this point.

## Observability

Bigtable proxy should appear normally in GCP Logging, Jaeger, Prometheus, etc. It doesn't provide any further custom metrics yet.

## Error handling

The bigtable proxy is expected to pass along bigtable and token_exchange gRPC errors to clients unchanged. It mostly should not introduce further error modes of its own.

## Testing

Most testing coverage of the bigtable proxy at this point is in services/test:api_end_to_end_test. More targeted service-level tests are a TODO.

## Performance

The performance cost of passing a query through the bigtable proxy, as opposed to accessing bigtable directly, is intended to be low. The proxy does in-memory work linear in the size of the gRPC messages (copying and reformatting the request and response types) and pays an up-front cost of a few milliseconds for gRPC networking overhead.

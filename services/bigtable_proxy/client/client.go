// Package bigtableproxy provides a client for the bigtable proxy service.
// Adapted from client.rs and client.py implementations.

package bigtableproxy

import (
	"bytes"
	"context"
	"errors"
	"io"
	"strings"
	"time"

	googlepb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
)

type BigtableProxyClient interface {
	ReadRows(ctx context.Context, tenantID string, tableName pb.TableName, rows *googlepb.RowSet, filter *googlepb.RowFilter, rowsLimit int64, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) ([]*Row, error)
	MutateRows(ctx context.Context, tenantID string, tableName pb.TableName, entries []*googlepb.MutateRowsRequest_Entry, requestContext *requestcontext.RequestContext) ([]*pb.MutateRowsResponse, error)
	CheckAndMutateRow(ctx context.Context, tenantID string, tableName pb.TableName, rowKey []byte, predicateFilter *googlepb.RowFilter, trueMutations []*googlepb.Mutation, falseMutations []*googlepb.Mutation, requestContext *requestcontext.RequestContext) (*pb.CheckAndMutateRowResponse, error)
	CheckTenantDataAccess(ctx context.Context, tenantID string, requestContext *requestcontext.RequestContext) error
	// This should be called to cleanup resources for this client
	Close()
}

type BigtableProxyClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	client pb.BigtableProxyClient
}

func NewBigtableProxyClient(endpoint string, credentials credentials.TransportCredentials) (BigtableProxyClient, error) {
	// see https://code-review.googlesource.com/c/gocloud/+/36171/2/bigtable/bigtable.go
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(1<<28), grpc.MaxCallRecvMsgSize(1<<28)),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := pb.NewBigtableProxyClient(conn)
	return &BigtableProxyClientImpl{conn: conn, client: client}, nil
}

// CheckTenantDataAccess checks if the tenant has access to the data.
func (c *BigtableProxyClientImpl) CheckTenantDataAccess(ctx context.Context, tenantID string, requestContext *requestcontext.RequestContext) error {
	req := &pb.CheckTenantDataAccessRequest{
		TenantId: tenantID,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	_, err := c.client.CheckTenantDataAccess(ctx, req)
	return err
}

// ReadRows reads rows from the table.
//
// Returns a list of rows and an error. If no rows are found, empty list is returned.
func (c *BigtableProxyClientImpl) ReadRows(ctx context.Context, tenantID string, tableName pb.TableName, rows *googlepb.RowSet, filter *googlepb.RowFilter, rowsLimit int64, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) ([]*Row, error) {
	req := &pb.ReadRowsRequest{
		TableName: tableName,
		TenantId:  tenantID,
		Rows:      rows,
		Filter:    filter,
		RowsLimit: rowsLimit,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.ReadRows(ctx, req, opts...)
	if err != nil {
		return nil, err
	}
	var responses []*pb.ReadRowsResponse
	for {
		resp, err := stream.Recv()
		if errors.Is(err, io.EOF) {
			break
		}
		if err != nil {
			return nil, err
		}
		responses = append(responses, resp)
	}
	return decodeReadRowsResponse(0, responses)
}

func (c *BigtableProxyClientImpl) MutateRows(ctx context.Context,
	tenantID string,
	tableName pb.TableName,
	entries []*googlepb.MutateRowsRequest_Entry,
	requestContext *requestcontext.RequestContext,
) ([]*pb.MutateRowsResponse, error) {
	req := &pb.MutateRowsRequest{
		TableName: tableName,
		TenantId:  tenantID,
		Entries:   entries,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.MutateRows(ctx, req)
	if err != nil {
		return nil, err
	}
	var responses []*pb.MutateRowsResponse
	for {
		resp, err := stream.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			return nil, err
		}
		responses = append(responses, resp)
	}
	return responses, nil
}

func (c *BigtableProxyClientImpl) CheckAndMutateRow(ctx context.Context, tenantID string,
	tableName pb.TableName,
	rowKey []byte,
	predicateFilter *googlepb.RowFilter,
	trueMutations []*googlepb.Mutation,
	falseMutations []*googlepb.Mutation,
	requestContext *requestcontext.RequestContext,
) (*pb.CheckAndMutateRowResponse, error) {
	req := &pb.CheckAndMutateRowRequest{
		TableName:       tableName,
		TenantId:        tenantID,
		RowKey:          rowKey,
		PredicateFilter: predicateFilter,
		TrueMutations:   trueMutations,
		FalseMutations:  falseMutations,
	}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.CheckAndMutateRow(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *BigtableProxyClientImpl) Close() {
	c.conn.Close()
}

type RowCell struct {
	FamilyName      string
	Qualifier       []byte
	Value           []byte
	TimestampMicros int64
	Labels          []string
}

type Row struct {
	RowKey []byte
	Cells  []*RowCell
}

// Adapted from decode_read_rows_response in client.rs and client.py
func decodeReadRowsResponse(timeout time.Duration, rrr []*pb.ReadRowsResponse) ([]*Row, error) {
	rows := []*Row{}
	started := time.Now()
	for _, res := range rrr {
		if timeout != 0 && time.Since(started) > timeout {
			return nil, errors.New("timeout waiting for response")
		}
		newRows, err := decodeReadRowsResponseToVec(res.Chunks)
		if err != nil {
			return nil, err
		}
		rows = append(rows, newRows...)
	}
	return rows, nil
}

// Adapted from decode_read_rows_response_to_vec in client.rs and client.py
func decodeReadRowsResponseToVec(chunks []*googlepb.ReadRowsResponse_CellChunk) ([]*Row, error) {
	rows := []*Row{}
	currentRowKey := []byte{}
	currentRowData := []*RowCell{}
	currentCell := &RowCell{
		FamilyName:      "",
		Qualifier:       []byte{},
		Value:           []byte{},
		TimestampMicros: 0,
		Labels:          []string{},
	}
	startNewCell := false
	committedRowCellCount := 0
	startNewRow := false
	keySet := map[string]bool{}

	if len(chunks) == 0 {
		return rows, nil
	}

	lastColumnFamily := ""

	for _, chunk := range chunks {

		// Starting a new row?
		if len(chunk.RowKey) > 0 {
			if len(currentRowKey) == 0 || !bytes.Equal(currentRowKey, chunk.RowKey) {
				// a new key comes, start_new_row should be false at this time
				if startNewRow {
					return nil, errors.New("Invalid - no commit before key changes")
				}
				startNewRow = true
			}
			currentRowKey = chunk.RowKey
		} else {
			// row_key is empty
			if !startNewRow {
				return nil, errors.New("Invalid - new row missing row key")
			}
		}

		// when starting a new cell with new family name, then a qualifier must exist
		if chunk.FamilyName != nil && chunk.FamilyName.Value != currentCell.FamilyName && chunk.Qualifier == nil {
			return nil, errors.New("new col family but no specified qualifier")
		}
		if lastColumnFamily == "" {
			if chunk.FamilyName == nil {
				return nil, errors.New("Invalid - first chunk must have family name")
			}
		}
		if chunk.FamilyName != nil {
			lastColumnFamily = chunk.FamilyName.Value
		}

		// start a new cell with the existing cell_name or new cell_name (chunk.qualifier)
		if (startNewCell && len(currentCell.Qualifier) > 0) || chunk.Qualifier != nil {
			currentCell = &RowCell{
				FamilyName:      "",
				Qualifier:       []byte{},
				Value:           []byte{},
				TimestampMicros: 0,
				Labels:          []string{},
			}
			// when a new cell with the same qualifier starts, reuse the old family_name and qualifier
			if chunk.FamilyName != nil {
				currentCell.FamilyName = chunk.FamilyName.Value
			} else {
				currentCell.FamilyName = lastColumnFamily
			}
			if chunk.Qualifier != nil {
				currentCell.Qualifier = chunk.Qualifier.Value
			}
			currentCell.TimestampMicros = chunk.TimestampMicros
			currentCell.Labels = chunk.Labels
			startNewCell = false
		}

		currentCell.Value = append(currentCell.Value, chunk.Value...)

		// last chunk for the cell?
		if chunk.ValueSize == 0 {
			// Close up the cell
			if len(currentCell.Qualifier) > 0 {
				currentRowData = append(currentRowData, currentCell)
			}
			// make sure we start a new cell in case the qualifier doesn't change
			startNewCell = true
		}

		// End of a row?
		if chunk.RowStatus != nil {
			if chunk.GetRowStatus().(*googlepb.ReadRowsResponse_CellChunk_CommitRow) != nil {
				if len(currentRowKey) > 0 {
					rows = append(rows, &Row{
						RowKey: currentRowKey,
						Cells:  currentRowData,
					})
					currentRowData = []*RowCell{}
				}
				if chunk.GetCommitRow() {
					if len(currentRowKey) > 0 {
						noDuplicatedKey := !keySet[string(currentRowKey)]
						if !noDuplicatedKey {
							return nil, errors.New("Invalid - duplicate row key")
						}
						keySet[string(currentRowKey)] = true
					}
					if chunk.ValueSize != 0 {
						// meaning chunk is not ended yet
						return nil, errors.New("Invalid - commit with chunk not ended")
					}

					committedRowCellCount = len(rows)
					startNewRow = false
				}
			} else if chunk.GetRowStatus().(*googlepb.ReadRowsResponse_CellChunk_ResetRow) != nil {
				// ResetRow indicates that the client should drop all previous chunks for
				// `row_key`, as it will be re-read from the beginning.
				currentRowKey = []byte{}
				currentRowData = []*RowCell{}
				startNewRow = false
				rows = rows[:committedRowCellCount]
			}
		}
	}

	if startNewRow && committedRowCellCount == 0 {
		return nil, errors.New("No rows committed")
	}

	if startNewRow {
		return nil, errors.New("Invalid - last row missing commit")
	}

	return rows, nil
}

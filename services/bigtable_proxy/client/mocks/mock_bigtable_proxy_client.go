// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/augmentcode/augment/services/bigtable_proxy/client (interfaces: BigtableProxyClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	googlepb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockBigtableProxyClient is a mock of BigtableProxyClient interface.
type MockBigtableProxyClient struct {
	ctrl     *gomock.Controller
	recorder *MockBigtableProxyClientMockRecorder
}

// MockBigtableProxyClientMockRecorder is the mock recorder for MockBigtableProxyClient.
type MockBigtableProxyClientMockRecorder struct {
	mock *MockBigtableProxyClient
}

// NewMockBigtableProxyClient creates a new mock instance.
func NewMockBigtableProxyClient(ctrl *gomock.Controller) *MockBigtableProxyClient {
	mock := &MockBigtableProxyClient{ctrl: ctrl}
	mock.recorder = &MockBigtableProxyClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBigtableProxyClient) EXPECT() *MockBigtableProxyClientMockRecorder {
	return m.recorder
}

// CheckAndMutateRow mocks base method.
func (m *MockBigtableProxyClient) CheckAndMutateRow(ctx context.Context, tenantID string, tableName proxyproto.TableName, rowKey []byte, predicateFilter *googlepb.RowFilter, trueMutations, falseMutations []*googlepb.Mutation, requestContext *requestcontext.RequestContext) (*proxyproto.CheckAndMutateRowResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndMutateRow", ctx, tenantID, tableName, rowKey, predicateFilter, trueMutations, falseMutations, requestContext)
	ret0, _ := ret[0].(*proxyproto.CheckAndMutateRowResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAndMutateRow indicates an expected call of CheckAndMutateRow.
func (mr *MockBigtableProxyClientMockRecorder) CheckAndMutateRow(ctx, tenantID, tableName, rowKey, predicateFilter, trueMutations, falseMutations, requestContext interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndMutateRow", reflect.TypeOf((*MockBigtableProxyClient)(nil).CheckAndMutateRow), ctx, tenantID, tableName, rowKey, predicateFilter, trueMutations, falseMutations, requestContext)
}

// Close mocks base method.
func (m *MockBigtableProxyClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockBigtableProxyClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockBigtableProxyClient)(nil).Close))
}

// MutateRows mocks base method.
func (m *MockBigtableProxyClient) MutateRows(ctx context.Context, tenantID string, tableName proxyproto.TableName, entries []*googlepb.MutateRowsRequest_Entry, requestContext *requestcontext.RequestContext) ([]*proxyproto.MutateRowsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MutateRows", ctx, tenantID, tableName, entries, requestContext)
	ret0, _ := ret[0].([]*proxyproto.MutateRowsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MutateRows indicates an expected call of MutateRows.
func (mr *MockBigtableProxyClientMockRecorder) MutateRows(ctx, tenantID, tableName, entries, requestContext interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MutateRows", reflect.TypeOf((*MockBigtableProxyClient)(nil).MutateRows), ctx, tenantID, tableName, entries, requestContext)
}

// ReadRows mocks base method.
func (m *MockBigtableProxyClient) ReadRows(ctx context.Context, tenantID string, tableName proxyproto.TableName, rows *googlepb.RowSet, filter *googlepb.RowFilter, rowsLimit int64, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) ([]*bigtableproxy.Row, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, tenantID, tableName, rows, filter, rowsLimit, requestContext}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReadRows", varargs...)
	ret0, _ := ret[0].([]*bigtableproxy.Row)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadRows indicates an expected call of ReadRows.
func (mr *MockBigtableProxyClientMockRecorder) ReadRows(ctx, tenantID, tableName, rows, filter, rowsLimit, requestContext interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := []interface{}{ctx, tenantID, tableName, rows, filter, rowsLimit, requestContext}
	varargs = append(varargs, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadRows", reflect.TypeOf((*MockBigtableProxyClient)(nil).ReadRows), varargs...)
}

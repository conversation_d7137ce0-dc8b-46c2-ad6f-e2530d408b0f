"""A client for the bigtable proxy service migration piece. This is a
temporary client that will be removed once the migration is complete."""

from __future__ import annotations

import logging
from typing import Optional

import grpc

from base.python.grpc import client_options
from services.bigtable_proxy import bigtable_proxy_pb2, bigtable_proxy_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> bigtable_proxy_pb2_grpc.BigtableProxyStub:
    """Setup the client stub for the bigtable proxy service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = bigtable_proxy_pb2_grpc.BigtableProxyStub(channel)
    return stub


class MigrateOnlyBigtableClient:
    """Class to make easy calls to the migrate API."""

    def __init__(self, stub):
        """Constructs a new bigtable proxy stub."""
        self.stub = stub

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new bigtable proxy stub from endpoint and credentials."""
        stub = setup_stub(endpoint, credentials, options=options)
        return cls(stub)

    def migrate(
        self,
        tenant_id: str,
        table_name: str,
        request_context: RequestContext,
    ) -> bool:
        """Migrate the given table for the given tenant.

        Returns:
        - Bool indicating if the migration started
        """
        if table_name == "CONTENT_MANAGER":
            new_table_name = bigtable_proxy_pb2.TableName.CONTENT_MANAGER
        else:
            raise ValueError(f"Invalid table name: {table_name}")

        # Now migrate
        request = bigtable_proxy_pb2.MigrateRequest(  # type: ignore
            tenant_id=tenant_id,
            table_name=new_table_name,
        )

        migrate_resp = self.stub.Migrate(
            request, metadata=request_context.to_metadata()
        )
        return migrate_resp.started

// Package bigtableproxy provides a client for the bigtable proxy service.
// This file contains an implementation that connects directly to the BigTable emulator.

package bigtableproxy

import (
	"bytes"
	"context"
	"fmt"
	"time"

	"cloud.google.com/go/bigtable"
	googlepb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/option"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// EmulatorBigtableProxyClient is a client that connects directly to the BigTable emulator
// It implements the BigtableProxyClient interface
type EmulatorBigtableProxyClient struct {
	// The BigTable client
	client *bigtable.Client

	// Map of table names to BigTable tables
	tables map[pb.TableName]*bigtable.Table

	// Project ID for BigTable
	project string

	// Instance ID for BigTable
	instance string
}

// NewEmulatorBigtableProxyClient creates a new client that connects directly to the BigTable emulator
// The emulator must be running and the BIGTABLE_EMULATOR_HOST environment variable must be set
func NewEmulatorBigtableProxyClient(project, instance string) (BigtableProxyClient, error) {
	ctx := context.Background()

	// Create a BigTable client that connects to the emulator
	client, err := bigtable.NewClient(
		ctx,
		project,
		instance,
		option.WithoutAuthentication(),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create BigTable client: %w", err)
	}

	// Create a map of table names to BigTable tables
	tables := make(map[pb.TableName]*bigtable.Table)
	for tableName := range pb.TableName_name {
		if tableName == 0 {
			// Skip the UNKNOWN table
			continue
		}
		tableNameEnum := pb.TableName(tableName)
		tables[tableNameEnum] = client.Open(tableNameEnum.String())
	}

	return &EmulatorBigtableProxyClient{
		client:   client,
		tables:   tables,
		project:  project,
		instance: instance,
	}, nil
}

// Close closes the BigTable client
func (c *EmulatorBigtableProxyClient) Close() {
	if c.client != nil {
		c.client.Close()
	}
}

func (c *EmulatorBigtableProxyClient) CheckTenantDataAccess(ctx context.Context, tenantID string, requestContext *requestcontext.RequestContext) error {
	return nil
}

// addTenantIDToKey prefixes a row key with the tenant ID
func addTenantIDToKey(key []byte, tenantID string, warnOnEmpty bool) []byte {
	if len(key) == 0 {
		if warnOnEmpty {
			log.Warn().Msg("addTenantIDToKey called with empty key, skipping tenant ID prefix...")
		}
		return key
	}

	// Create new slice with capacity for tenant + separator + key
	newKey := make([]byte, len(tenantID)+1+len(key))
	n := copy(newKey, tenantID)
	newKey[n] = '#'
	copy(newKey[n+1:], key)
	return newKey
}

// removeTenantIDFromKey removes the tenant ID prefix from a row key
func removeTenantIDFromKey(key []byte, tenantID string) []byte {
	// Empty key is an expected case in the bigtable API
	if len(key) == 0 {
		return key
	}

	expectedPrefix := tenantID + "#"
	if !bytes.HasPrefix(key, []byte(expectedPrefix)) {
		log.Warn().Msgf("removeTenantIDFromKey called with key that doesn't start with tenant ID: %s", string(key))
		return key
	}

	// Return the key after the tenant ID prefix
	return key[len(expectedPrefix):]
}

// ReadRows reads rows from the table
func (c *EmulatorBigtableProxyClient) ReadRows(
	ctx context.Context,
	tenantID string,
	tableName pb.TableName,
	rows *googlepb.RowSet,
	filter *googlepb.RowFilter,
	rowsLimit int64,
	requestContext *requestcontext.RequestContext,
	opts ...grpc.CallOption,
) ([]*Row, error) {
	log.Info().
		Str("tenant_id", tenantID).
		Str("table_name", tableName.String()).
		Msg("ReadRows")

	table, ok := c.tables[tableName]
	if !ok {
		return nil, status.Errorf(codes.NotFound, "table not found: %s", tableName.String())
	}

	var btRows bigtable.RowSet
	// If no rows specified, read all rows for this tenant
	if rows == nil || (rows.RowKeys == nil || len(rows.RowKeys) == 0) && (rows.RowRanges == nil || len(rows.RowRanges) == 0) {
		btRows = bigtable.PrefixRange(tenantID + "#")
	} else if rows.RowKeys != nil && len(rows.RowKeys) > 0 {
		rowKeys := make([]string, len(rows.RowKeys))
		for i, key := range rows.RowKeys {
			// Prefix tenant ID to each row key
			rowKeys[i] = string(addTenantIDToKey(key, tenantID, true))
		}
		btRows = bigtable.RowList(rowKeys)
	} else if rows.RowRanges != nil && len(rows.RowRanges) > 0 {
		// For simplicity, we'll just handle the first range
		// In a real implementation, you'd want to handle multiple ranges
		rowRange := rows.RowRanges[0]
		var start, end string
		var startInclusive, endInclusive bool

		switch rowRange.StartKey.(type) {
		case *googlepb.RowRange_StartKeyClosed:
			start = string(addTenantIDToKey(rowRange.GetStartKeyClosed(), tenantID, true))
			startInclusive = true
		case *googlepb.RowRange_StartKeyOpen:
			start = string(addTenantIDToKey(rowRange.GetStartKeyOpen(), tenantID, true))
			startInclusive = false
		case nil:
			start = tenantID + "#"
			startInclusive = true
		}

		switch rowRange.EndKey.(type) {
		case *googlepb.RowRange_EndKeyClosed:
			end = string(addTenantIDToKey(rowRange.GetEndKeyClosed(), tenantID, true))
			endInclusive = true
		case *googlepb.RowRange_EndKeyOpen:
			end = string(addTenantIDToKey(rowRange.GetEndKeyOpen(), tenantID, true))
			endInclusive = false
		case nil:
			// If no end key, use tenant ID prefix + max byte as the end
			end = tenantID + "#\xff\xff\xff\xff\xff\xff\xff\xff"
			endInclusive = false
		}

		btRows = bigtable.NewRange(start, end)
		switch {
		case startInclusive && endInclusive:
			btRows = bigtable.NewClosedRange(start, end)
		case startInclusive && !endInclusive:
			btRows = bigtable.NewClosedOpenRange(start, end)
		case !startInclusive && endInclusive:
			btRows = bigtable.NewOpenClosedRange(start, end)
		default:
			btRows = bigtable.NewOpenRange(start, end)
		}
	}

	// Convert the RowFilter to a bigtable.Filter
	var btFilter bigtable.Filter
	if filter != nil {
		btFilter = convertRowFilterToBigtableFilter(filter)
	}

	// Apply row limit as a ReadOption if specified

	// Read the rows
	var result []*Row

	// Create read options
	readOpts := []bigtable.ReadOption{}

	// Add filter if specified
	if btFilter != nil {
		readOpts = append(readOpts, bigtable.RowFilter(btFilter))
	}

	// Add row limit if specified
	if rowsLimit > 0 {
		readOpts = append(readOpts, bigtable.LimitRows(rowsLimit))
	}

	err := table.ReadRows(ctx, btRows, func(row bigtable.Row) bool {
		// Remove tenant ID prefix from row key
		rowKey := removeTenantIDFromKey([]byte(row.Key()), tenantID)

		// Convert the bigtable.Row to a *Row with tenant ID prefix removed
		r := &Row{
			RowKey: rowKey,
			Cells:  make([]*RowCell, 0),
		}

		// Add the cells
		for family, columns := range row {
			for _, column := range columns {
				// Extract just the column qualifier part by trimming off the family name prefix
				qualifier := column.Column
				// If the column includes the family name as a prefix, trim it off
				familyPrefix := family + ":"
				if len(qualifier) > len(familyPrefix) && qualifier[:len(familyPrefix)] == familyPrefix {
					qualifier = qualifier[len(familyPrefix):]
				}

				cell := &RowCell{
					FamilyName:      family,
					Qualifier:       []byte(qualifier),
					Value:           column.Value,
					TimestampMicros: int64(column.Timestamp),
				}
				r.Cells = append(r.Cells, cell)
			}
		}

		result = append(result, r)
		return true
	}, readOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to read rows: %w", err)
	}

	return result, nil
}

// MutateRows applies a series of mutations to the given row keys
func (c *EmulatorBigtableProxyClient) MutateRows(
	ctx context.Context,
	tenantID string,
	tableName pb.TableName,
	entries []*googlepb.MutateRowsRequest_Entry,
	requestContext *requestcontext.RequestContext,
) ([]*pb.MutateRowsResponse, error) {
	log.Info().
		Str("tenant_id", tenantID).
		Str("table_name", tableName.String()).
		Int("entries", len(entries)).
		Msg("MutateRows")

	table, ok := c.tables[tableName]
	if !ok {
		return nil, status.Errorf(codes.NotFound, "table not found: %s", tableName.String())
	}

	// Process each entry
	for _, entry := range entries {
		// Prefix tenant ID to row key
		rowKey := string(addTenantIDToKey(entry.RowKey, tenantID, true))
		mut := bigtable.NewMutation()

		// Apply each mutation
		for _, mutation := range entry.Mutations {
			switch m := mutation.Mutation.(type) {
			case *googlepb.Mutation_SetCell_:
				family := m.SetCell.FamilyName
				column := string(m.SetCell.ColumnQualifier)
				value := m.SetCell.Value
				timestamp := bigtable.Now()
				if m.SetCell.TimestampMicros > 0 {
					timestamp = bigtable.Timestamp(m.SetCell.TimestampMicros)
				}
				mut.Set(family, column, timestamp, value)
			case *googlepb.Mutation_DeleteFromRow_:
				mut.DeleteRow()
			case *googlepb.Mutation_DeleteFromFamily_:
				// Note: bigtable.Mutation doesn't have a DeleteFamily method
				// We'll delete all cells in the family by using a timestamp range
				family := m.DeleteFromFamily.FamilyName
				// Delete all cells in the family
				mut.DeleteTimestampRange(family, "", bigtable.Timestamp(0), bigtable.Timestamp(time.Now().Add(100*time.Hour).UnixNano()/1000))
			case *googlepb.Mutation_DeleteFromColumn_:
				family := m.DeleteFromColumn.FamilyName
				column := string(m.DeleteFromColumn.ColumnQualifier)
				mut.DeleteCellsInColumn(family, column)
			default:
				return nil, status.Errorf(codes.Unimplemented, "unsupported mutation type: %T", m)
			}
		}

		// Apply the mutation
		err := table.Apply(ctx, rowKey, mut)
		if err != nil {
			return nil, fmt.Errorf("failed to apply mutation: %w", err)
		}
	}

	// Create a response with no entries
	// The client doesn't actually use the entries, it just checks for errors
	response := &pb.MutateRowsResponse{}

	return []*pb.MutateRowsResponse{response}, nil
}

// CheckAndMutateRow atomically checks a condition and applies a mutation if the condition is met
func (c *EmulatorBigtableProxyClient) CheckAndMutateRow(
	ctx context.Context,
	tenantID string,
	tableName pb.TableName,
	rowKey []byte,
	predicateFilter *googlepb.RowFilter,
	trueMutations []*googlepb.Mutation,
	falseMutations []*googlepb.Mutation,
	requestContext *requestcontext.RequestContext,
) (*pb.CheckAndMutateRowResponse, error) {
	log.Debug().
		Str("tenant_id", tenantID).
		Str("table_name", tableName.String()).
		Str("row_key", string(rowKey)).
		Msg("CheckAndMutateRow")

	// Prefix tenant ID to row key
	rowKey = addTenantIDToKey(rowKey, tenantID, true)
	table, ok := c.tables[tableName]
	if !ok {
		return nil, status.Errorf(codes.NotFound, "table not found: %s", tableName.String())
	}

	// Convert the predicateFilter to a bigtable.Filter
	var btFilter bigtable.Filter
	if predicateFilter != nil {
		btFilter = convertRowFilterToBigtableFilter(predicateFilter)
	} else {
		// No filter means the predicate is always true
		btFilter = bigtable.PassAllFilter()
	}

	// Convert true mutations to a bigtable.Mutation
	trueMut := bigtable.NewMutation()
	if len(trueMutations) > 0 {
		for _, mutation := range trueMutations {
			switch m := mutation.Mutation.(type) {
			case *googlepb.Mutation_SetCell_:
				family := m.SetCell.FamilyName
				column := string(m.SetCell.ColumnQualifier)
				value := m.SetCell.Value
				timestamp := bigtable.Now()
				if m.SetCell.TimestampMicros > 0 {
					timestamp = bigtable.Timestamp(m.SetCell.TimestampMicros)
				}
				trueMut.Set(family, column, timestamp, value)
			case *googlepb.Mutation_DeleteFromRow_:
				trueMut.DeleteRow()
			case *googlepb.Mutation_DeleteFromFamily_:
				family := m.DeleteFromFamily.FamilyName
				trueMut.DeleteTimestampRange(family, "", bigtable.Timestamp(0), bigtable.Timestamp(time.Now().Add(100*time.Hour).UnixNano()/1000))
			case *googlepb.Mutation_DeleteFromColumn_:
				family := m.DeleteFromColumn.FamilyName
				column := string(m.DeleteFromColumn.ColumnQualifier)
				trueMut.DeleteCellsInColumn(family, column)
			default:
				return nil, status.Errorf(codes.Unimplemented, "unsupported mutation type: %T", m)
			}
		}
	}

	// Convert false mutations to a bigtable.Mutation
	falseMut := bigtable.NewMutation()
	if len(falseMutations) > 0 {
		for _, mutation := range falseMutations {
			switch m := mutation.Mutation.(type) {
			case *googlepb.Mutation_SetCell_:
				family := m.SetCell.FamilyName
				column := string(m.SetCell.ColumnQualifier)
				value := m.SetCell.Value
				timestamp := bigtable.Now()
				if m.SetCell.TimestampMicros > 0 {
					timestamp = bigtable.Timestamp(m.SetCell.TimestampMicros)
				}
				falseMut.Set(family, column, timestamp, value)
			case *googlepb.Mutation_DeleteFromRow_:
				falseMut.DeleteRow()
			case *googlepb.Mutation_DeleteFromFamily_:
				family := m.DeleteFromFamily.FamilyName
				falseMut.DeleteTimestampRange(family, "", bigtable.Timestamp(0), bigtable.Timestamp(time.Now().Add(100*time.Hour).UnixNano()/1000))
			case *googlepb.Mutation_DeleteFromColumn_:
				family := m.DeleteFromColumn.FamilyName
				column := string(m.DeleteFromColumn.ColumnQualifier)
				falseMut.DeleteCellsInColumn(family, column)
			default:
				return nil, status.Errorf(codes.Unimplemented, "unsupported mutation type: %T", m)
			}
		}
	}

	// Create a conditional mutation that applies trueMut if the filter matches, otherwise falseMut
	condMut := bigtable.NewCondMutation(btFilter, trueMut, falseMut)

	// Apply the conditional mutation and get the result
	var matched bool
	matchResult := bigtable.GetCondMutationResult(&matched)

	// Apply the mutation atomically
	err := table.Apply(ctx, string(rowKey), condMut, matchResult)
	if err != nil {
		return nil, fmt.Errorf("failed to apply conditional mutation: %w", err)
	}

	return &pb.CheckAndMutateRowResponse{
		PredicateMatched: matched,
	}, nil
}

// convertRowFilterToBigtableFilter converts a googlepb.RowFilter to a bigtable.Filter
func convertRowFilterToBigtableFilter(filter *googlepb.RowFilter) bigtable.Filter {
	if filter == nil {
		return nil
	}

	switch f := filter.Filter.(type) {
	case *googlepb.RowFilter_Chain_:
		// Chain filter
		var filters []bigtable.Filter
		for _, subFilter := range f.Chain.Filters {
			if sf := convertRowFilterToBigtableFilter(subFilter); sf != nil {
				filters = append(filters, sf)
			}
		}
		return bigtable.ChainFilters(filters...)
	case *googlepb.RowFilter_Interleave_:
		// Interleave filter
		var filters []bigtable.Filter
		for _, subFilter := range f.Interleave.Filters {
			if sf := convertRowFilterToBigtableFilter(subFilter); sf != nil {
				filters = append(filters, sf)
			}
		}
		return bigtable.InterleaveFilters(filters...)
	case *googlepb.RowFilter_Condition_:
		// Condition filter
		var predicateFilter, trueFilter, falseFilter bigtable.Filter
		if f.Condition.PredicateFilter != nil {
			predicateFilter = convertRowFilterToBigtableFilter(f.Condition.PredicateFilter)
		}
		if f.Condition.TrueFilter != nil {
			trueFilter = convertRowFilterToBigtableFilter(f.Condition.TrueFilter)
		}
		if f.Condition.FalseFilter != nil {
			falseFilter = convertRowFilterToBigtableFilter(f.Condition.FalseFilter)
		}
		return bigtable.ConditionFilter(predicateFilter, trueFilter, falseFilter)
	// Note: Some filters are not directly supported by the bigtable client
	// We'll handle the common ones and use pass-all for others
	case *googlepb.RowFilter_PassAllFilter:
		// Pass all filter
		return bigtable.PassAllFilter()
	case *googlepb.RowFilter_BlockAllFilter:
		// Block all filter - not directly supported
		// We'll use a filter that matches nothing
		return bigtable.RowKeyFilter("^$")
	case *googlepb.RowFilter_RowKeyRegexFilter:
		// Row key regex filter
		return bigtable.RowKeyFilter(string(f.RowKeyRegexFilter))
	case *googlepb.RowFilter_RowSampleFilter:
		// Row sample filter
		return bigtable.RowSampleFilter(f.RowSampleFilter)
	case *googlepb.RowFilter_FamilyNameRegexFilter:
		// Family name regex filter
		return bigtable.FamilyFilter(f.FamilyNameRegexFilter)
	case *googlepb.RowFilter_ColumnQualifierRegexFilter:
		// Column qualifier regex filter
		return bigtable.ColumnFilter(string(f.ColumnQualifierRegexFilter))
	case *googlepb.RowFilter_ColumnRangeFilter:
		// Column range filter
		family := f.ColumnRangeFilter.FamilyName
		startQual := string(f.ColumnRangeFilter.GetStartQualifierClosed())
		endQual := string(f.ColumnRangeFilter.GetEndQualifierClosed())
		return bigtable.ColumnRangeFilter(family, startQual, endQual)
	case *googlepb.RowFilter_TimestampRangeFilter:
		// Timestamp range filter
		start := time.UnixMicro(f.TimestampRangeFilter.StartTimestampMicros)
		end := time.UnixMicro(f.TimestampRangeFilter.EndTimestampMicros)
		return bigtable.TimestampRangeFilter(start, end)
	case *googlepb.RowFilter_ValueRegexFilter:
		// Value regex filter
		return bigtable.ValueFilter(string(f.ValueRegexFilter))
	case *googlepb.RowFilter_ValueRangeFilter:
		// Value range filter
		// Note: bigtable.ValueRangeFilter expects []byte, not string
		startVal := f.ValueRangeFilter.GetStartValueClosed()
		endVal := f.ValueRangeFilter.GetEndValueClosed()
		return bigtable.ValueRangeFilter(startVal, endVal)
	case *googlepb.RowFilter_CellsPerRowOffsetFilter:
		// Cells per row offset filter
		return bigtable.CellsPerRowOffsetFilter(int(f.CellsPerRowOffsetFilter))
	case *googlepb.RowFilter_CellsPerRowLimitFilter:
		// Cells per row limit filter
		return bigtable.CellsPerRowLimitFilter(int(f.CellsPerRowLimitFilter))
	case *googlepb.RowFilter_CellsPerColumnLimitFilter:
		// Cells per column limit filter
		return bigtable.LatestNFilter(int(f.CellsPerColumnLimitFilter))
	case *googlepb.RowFilter_StripValueTransformer:
		// Strip value transformer
		return bigtable.StripValueFilter()
	case *googlepb.RowFilter_ApplyLabelTransformer:
		// Apply label transformer
		return bigtable.LabelFilter(f.ApplyLabelTransformer)
	default:
		log.Warn().Msgf("Unsupported filter type: %T", f)
		return nil
	}
}

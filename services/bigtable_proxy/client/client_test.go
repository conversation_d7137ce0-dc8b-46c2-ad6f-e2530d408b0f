package bigtableproxy

import (
	"testing"

	bigtablepb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

func TestDecodeReadRowsResponse(t *testing.T) {
	// Create a test chunk
	chunks := []*bigtablepb.ReadRowsResponse_CellChunk{
		{
			RowKey: []byte("row-key-1"),
			FamilyName: &wrapperspb.StringValue{
				Value: "family-name-1",
			},
			Qualifier: &wrapperspb.BytesValue{
				Value: []byte("qualifier-1"),
			},
			TimestampMicros: 12345678901,
			Labels:          []string{},
			Value:           []byte("value-1"),
			ValueSize:       0,
			RowStatus: &bigtablepb.ReadRowsResponse_CellChunk_CommitRow{
				CommitRow: true,
			},
		},
	}

	// Call the decodeReadRowsResponseToVec function
	rows, err := decodeReadRowsResponseToVec(chunks)
	if err != nil {
		t.Errorf("decodeReadRowsResponseToVec returned error: %v", err)
	}

	// Check if the rows are correct
	if len(rows) != 1 {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect number of rows: %d", len(rows))
	}

	// Check if the row is correct
	if string(rows[0].RowKey) != "row-key-1" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect row key: %s", rows[0].RowKey)
	}
}

func TestDecodeReadRowsResponse_MultiRows(t *testing.T) {
	// Create test chunks
	chunks := []*bigtablepb.ReadRowsResponse_CellChunk{
		{
			RowKey: []byte("row-key-1"),
			FamilyName: &wrapperspb.StringValue{
				Value: "family-name-1",
			},
			Qualifier: &wrapperspb.BytesValue{
				Value: []byte("qualifier-1"),
			},
			TimestampMicros: 12345678901,
			Labels:          []string{},
			Value:           []byte("value-1"),
			ValueSize:       0,
			RowStatus: &bigtablepb.ReadRowsResponse_CellChunk_CommitRow{
				CommitRow: true,
			},
		},
		{
			RowKey: []byte("row-key-2"),
			FamilyName: &wrapperspb.StringValue{
				Value: "family-name-1",
			},
			Qualifier: &wrapperspb.BytesValue{
				Value: []byte("qualifier-1"),
			},
			TimestampMicros: 12345678902,
			Labels:          []string{},
			Value:           []byte("value-2"),
			ValueSize:       0,
			RowStatus: &bigtablepb.ReadRowsResponse_CellChunk_CommitRow{
				CommitRow: true,
			},
		},
	}

	// Call the decodeReadRowsResponseToVec function
	rows, err := decodeReadRowsResponseToVec(chunks)
	if err != nil {
		t.Errorf("decodeReadRowsResponseToVec returned error: %v", err)
	}

	// Check if the rows are correct
	if len(rows) != 2 {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect number of rows: %d", len(rows))
	}

	// Check if the rows are correct
	if string(rows[0].RowKey) != "row-key-1" || string(rows[1].RowKey) != "row-key-2" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect row keys: %s, %s", rows[0].RowKey, rows[1].RowKey)
	}
}

func TestDecodeReadRowsResponse_MultiCellsPerRow(t *testing.T) {
	// Create test chunks
	chunks := []*bigtablepb.ReadRowsResponse_CellChunk{
		{
			RowKey: []byte("row-key-1"),
			FamilyName: &wrapperspb.StringValue{
				Value: "family-name-1",
			},
			Qualifier: &wrapperspb.BytesValue{
				Value: []byte("qualifier-1"),
			},
			TimestampMicros: 12345678901,
			Labels:          []string{},
			Value:           []byte("value-1"),
			ValueSize:       0,
		},
		{
			RowKey:     []byte(""), // API convention to signal "same as previous"
			FamilyName: nil,        // ditto
			Qualifier: &wrapperspb.BytesValue{
				Value: []byte("qualifier-2"),
			},
			TimestampMicros: 12345678902,
			Labels:          []string{},
			Value:           []byte("value-2"),
			ValueSize:       0,
			RowStatus: &bigtablepb.ReadRowsResponse_CellChunk_CommitRow{
				CommitRow: true,
			},
		},
	}

	// Call the decodeReadRowsResponseToVec function
	rows, err := decodeReadRowsResponseToVec(chunks)
	if err != nil {
		t.Errorf("decodeReadRowsResponseToVec returned error: %v", err)
	}

	// Check if the rows are correct
	if len(rows) != 1 {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect number of rows: %d", len(rows))
	}

	// Check if the row is correct
	if string(rows[0].RowKey) != "row-key-1" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect row key: %s", rows[0].RowKey)
	}

	// Check if the cells are correct
	if len(rows[0].Cells) != 2 {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect number of cells: %d", len(rows[0].Cells))
	}

	t.Logf("rows[0].Cells: %+v", rows[0].Cells)

	// Check if the first cell is correct
	if string(rows[0].Cells[0].Qualifier) != "qualifier-1" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect qualifier for first cell: %s", rows[0].Cells[0].Qualifier)
	}
	if string(rows[0].Cells[0].Value) != "value-1" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect value for first cell: %s", rows[0].Cells[0].Value)
	}

	// Check if the second cell is correct
	if string(rows[0].Cells[1].Qualifier) != "qualifier-2" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect qualifier for second cell: %s", rows[0].Cells[1].Qualifier)
	}
	if string(rows[0].Cells[1].Value) != "value-2" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect value for second cell: %s", rows[0].Cells[1].Value)
	}
}

func TestDecodeReadRowsResponse_MultiChunksPerCell(t *testing.T) {
	// Create test chunks
	chunks := []*bigtablepb.ReadRowsResponse_CellChunk{
		{
			RowKey: []byte("row-key-1"),
			FamilyName: &wrapperspb.StringValue{
				Value: "family-name-1",
			},
			Qualifier: &wrapperspb.BytesValue{
				Value: []byte("qualifier-1"),
			},
			TimestampMicros: 12345678901,
			Labels:          []string{},
			Value:           []byte("value-1"),
			ValueSize:       14, // API promises to pre-compute the value size we need
		},
		{
			RowKey:          []byte(""),  // API convention to signal "same as previous"
			FamilyName:      nil,         // ditto
			Qualifier:       nil,         // ditto
			TimestampMicros: 12345678902, // in practice this probably won't differ?
			Labels:          []string{},
			Value:           []byte("value-2"),
			ValueSize:       0,
			RowStatus: &bigtablepb.ReadRowsResponse_CellChunk_CommitRow{
				CommitRow: true,
			},
		},
	}

	// Call the decodeReadRowsResponseToVec function
	rows, err := decodeReadRowsResponseToVec(chunks)
	if err != nil {
		t.Errorf("decodeReadRowsResponseToVec returned error: %v", err)
	}

	// Check if the rows are correct
	if len(rows) != 1 {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect number of rows: %d", len(rows))
	}

	// Check if the row is correct
	if string(rows[0].RowKey) != "row-key-1" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect row key: %s", rows[0].RowKey)
	}

	// Check if the cells are correct
	if len(rows[0].Cells) != 1 {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect number of cells: %d", len(rows[0].Cells))
	}

	// Check if the cell is correct
	if string(rows[0].Cells[0].Qualifier) != "qualifier-1" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect qualifier for cell: %s", rows[0].Cells[0].Qualifier)
	}
	if string(rows[0].Cells[0].Value) != "value-1value-2" {
		t.Errorf("decodeReadRowsResponseToVec returned incorrect value for cell: %s", rows[0].Cells[0].Value)
	}
}

use std::cmp::max;
use std::collections::HashSet;
use std::sync::Arc;
use std::time::{Duration, Instant};

use crate::proto::bigtable_proxy::{
    MutateRowsRequest, MutateRowsResponse, ReadRowsRequest, ReadRowsResponse, TableName,
};
use crate::proto::google::bigtable::v2::{
    mutate_rows_request::Entry,
    read_rows_response::cell_chunk::RowStatus,
    read_rows_response::CellChunk,
    row_filter::Filter,
    row_range::{<PERSON><PERSON>ey, StartKey},
    RowFilter, RowRange, RowSet,
};
use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::{create_channel, receiver_for_error};
use request_context::{RequestContext, TenantId};
use tokio::sync::mpsc;
use tonic::transport::ClientTlsConfig;
use tracing::Instrument;
use tracing_tonic::client::TracingService;

use mockall::automock;

pub mod proto {
    pub mod bigtable_proxy {
        tonic::include_proto!("bigtable_proxy");
    }

    pub mod google {
        pub mod bigtable {
            pub mod v2 {
                tonic::include_proto!("google.bigtable.v2");
            }
        }
        pub mod rpc {
            tonic::include_proto!("google.rpc");
        }
        pub mod api {
            tonic::include_proto!("google.api");
        }
        pub mod r#type {
            tonic::include_proto!("google.r#type");
        }
    }
}

// Increase the default max grpc message decode size to 32MB when
// reading from BigTable. The default is 4MB, which is too small
// for some of the larger lists of blob names that we store in BigTable
// for blob checkpoints.
// see https://code-review.googlesource.com/c/gocloud/+/36171/2/bigtable/bigtable.go
const MAX_DECODE_SIZE: usize = 1024 * 1024 * 256;

#[automock]
#[async_trait]
pub trait BigtableProxyClient {
    async fn read_rows(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        table_name: TableName,
        row_keys: Vec<RowKey>,
        row_ranges: Vec<(RowKey, RowKey)>,
        limit: Option<i64>,
    ) -> tonic::Result<ReadResults>;
    async fn read_rows_streaming(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        table_name: TableName,
        row_keys: Vec<RowKey>,
        row_ranges: Vec<(RowKey, RowKey)>,
        limit: Option<i64>,
    ) -> mpsc::Receiver<tonic::Result<ReadResults>>;
    async fn mutate_rows(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        table_name: TableName,
        entries: Vec<Entry>,
    ) -> tonic::Result<tonic::Streaming<MutateRowsResponse>>;
    async fn check_tenant_data_access(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
    ) -> tonic::Result<()>;
}

#[derive(Clone)]
pub struct BigtableProxyClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Duration,
    client: Arc<
        Mutex<
            Option<
                proto::bigtable_proxy::bigtable_proxy_client::BigtableProxyClient<TracingService>,
            >,
        >,
    >,
}

// sends a message to a channel and ignores errors
// returns true if the message was sent
// TODO: share with other examples
async fn send_and_ignore<T>(tx: &mpsc::Sender<T>, msg: T, sendsite: &str) -> bool {
    if let Err(send_error) = tx.send(msg).await {
        if tx.is_closed() {
            tracing::debug!("Channel closed in {}", sendsite);
        } else {
            tracing::warn!(
                "Failed to send notification in {}: {}",
                sendsite,
                send_error
            );
        }
        false
    } else {
        true
    }
}

#[async_trait]
impl BigtableProxyClient for BigtableProxyClientImpl {
    // Currently this is set up to read only 1 cell per row key, since that's what the main caller
    // (content_manager) always wants. If other clients want different behavior it should be
    // straightforward to add support for that.
    async fn read_rows(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        table_name: TableName,
        row_keys: Vec<RowKey>,
        row_ranges: Vec<(RowKey, RowKey)>,
        limit: Option<i64>,
    ) -> tonic::Result<ReadResults> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("bigtable_proxy client not ready: {}", e);
            tonic::Status::unavailable("bigtable_proxy not ready")
        })?;

        let rows_limit = match limit {
            // map to value in [0, inf)
            Some(request_limit) => max(request_limit, 0),
            // when no explicit limit provided, intentionally set this up to 0 (indicating no
            // limit) if ranges are used
            None => row_keys.len() as i64,
        };

        let request = ReadRowsRequest {
            tenant_id: tenant_id.clone().into(),
            table_name: table_name.into(),
            rows_limit,
            rows: Some(RowSet {
                row_keys,
                row_ranges: row_ranges
                    .into_iter()
                    .map(|(start, end)| RowRange {
                        start_key: Some(StartKey::StartKeyClosed(start)),
                        end_key: Some(EndKey::EndKeyClosed(end)),
                    })
                    .collect(),
            }),
            filter: Some(RowFilter {
                filter: Some(Filter::CellsPerColumnLimitFilter(1)),
            }),
        };
        let mut tonic_request = tonic::Request::new(request);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.read_rows(tonic_request).await.map_err(|e| {
            tracing::error!("bigtable_proxy read_rows error: {}", e);
            e
        })?;
        decode_read_rows_response(&None, response.into_inner())
            .instrument(tracing::info_span!("decode_read_rows_response"))
            .await
            .map_err(|e| {
                tracing::error!("bigtable_proxy decode_read_rows_response error: {}", e);
                e
            })
    }

    async fn read_rows_streaming(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        table_name: TableName,
        row_keys: Vec<RowKey>,
        row_ranges: Vec<(RowKey, RowKey)>,
        limit: Option<i64>,
    ) -> mpsc::Receiver<tonic::Result<ReadResults>> {
        let mut client = match self.get_client().await {
            Ok(c) => c,
            Err(e) => {
                tracing::error!("bigtable_proxy client not ready: {}", e);
                return receiver_for_error(tonic::Status::unavailable("bigtable_proxy not ready"));
            }
        };

        let rows_limit = match limit {
            // map to value in [0, inf)
            Some(request_limit) => max(request_limit, 0),
            // when no explicit limit provided, intentionally set this up to 0 (indicating no
            // limit) if ranges are used
            None => row_keys.len() as i64,
        };

        let request = ReadRowsRequest {
            tenant_id: tenant_id.clone().into(),
            table_name: table_name.into(),
            rows_limit,
            rows: Some(RowSet {
                row_keys,
                row_ranges: row_ranges
                    .into_iter()
                    .map(|(start, end)| RowRange {
                        start_key: Some(StartKey::StartKeyClosed(start)),
                        end_key: Some(EndKey::EndKeyClosed(end)),
                    })
                    .collect(),
            }),
            filter: Some(RowFilter {
                filter: Some(Filter::CellsPerColumnLimitFilter(1)),
            }),
        };
        let mut tonic_request = tonic::Request::new(request);
        request_context.annotate(tonic_request.metadata_mut());

        let (tx, rx) = mpsc::channel(64);
        tokio::spawn(async move {
            let response = client.read_rows(tonic_request).await;
            match response {
                Ok(r) => {
                    let mut rrr = r.into_inner();
                    loop {
                        match rrr.message().await {
                            Ok(Some(rows)) => {
                                let mut msg = vec![];
                                // Check for individual-row decode errors and raise errors for them
                                for row in decode_read_rows_response_to_vec(rows.chunks).into_iter()
                                {
                                    match row {
                                        Ok(row) => msg.push(row),
                                        Err(e) => {
                                            tracing::warn!("Failed to decode row: {:?}", e);
                                            send_and_ignore(&tx, Err(e), "read_rows_streaming")
                                                .await;
                                            return; // Return since this is an error case
                                        }
                                    }
                                }

                                send_and_ignore(&tx, Ok(msg), "read_rows_streaming").await;
                            }
                            Err(e) => {
                                tracing::warn!("Read rows streaming error: {:?}", e);
                                send_and_ignore(&tx, Err(e), "read_rows_streaming").await;
                                return; // Return since this is an error case
                            }
                            Ok(None) => {
                                return; // EOS case
                            }
                        }
                    }
                }
                Err(e) => {
                    tracing::warn!("Read rows error: {:?}", e);
                    send_and_ignore(&tx, Err(e), "read_rows_streaming").await;
                }
            }
        });
        rx
    }

    async fn mutate_rows(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
        table_name: TableName,
        entries: Vec<Entry>,
    ) -> tonic::Result<tonic::Streaming<MutateRowsResponse>> {
        let mut client = self.get_client().await?;
        let request = MutateRowsRequest {
            tenant_id: tenant_id.clone().into(),
            table_name: table_name.into(),
            entries,
        };
        let mut tonic_request = tonic::Request::new(request);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.mutate_rows(tonic_request).await?;
        Ok(response.into_inner())
    }

    async fn check_tenant_data_access(
        &self,
        request_context: &RequestContext,
        tenant_id: &TenantId,
    ) -> tonic::Result<()> {
        let mut client = self.get_client().await?;
        let request = proto::bigtable_proxy::CheckTenantDataAccessRequest {
            tenant_id: tenant_id.clone().into(),
        };
        let mut tonic_request = tonic::Request::new(request);
        request_context.annotate(tonic_request.metadata_mut());

        client
            .check_tenant_data_access(tonic_request)
            .await
            .map(|_| ())
    }
}

impl BigtableProxyClientImpl {
    pub fn new(
        endpoint: &str,
        tls_config: Option<ClientTlsConfig>,
        request_timeout: Duration,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            request_timeout,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> tonic::Result<
        proto::bigtable_proxy::bigtable_proxy_client::BigtableProxyClient<TracingService>,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    Some(self.request_timeout),
                    &self.tls_config,
                )
                .await
                .map_err(|e| {
                    tracing::error!("bigtable_proxy client not ready: {}", e);
                    tonic::Status::unavailable("bigtable_proxy not ready")
                })?;
                let client =
                    proto::bigtable_proxy::bigtable_proxy_client::BigtableProxyClient::new(channel)
                        .max_decoding_message_size(MAX_DECODE_SIZE);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

pub type ClientCellChunk = CellChunk;
pub type ClientRowStatus = RowStatus;

/// An alias for Vec<u8> as row key
pub type RowKey = Vec<u8>;

/// A data structure for returning the read content of a cell in a row.
#[derive(Clone, Debug, PartialEq, Eq)]
pub struct RowCell {
    pub family_name: String,
    pub qualifier: Vec<u8>,
    pub value: Vec<u8>,
    pub timestamp_micros: i64,
    pub labels: Vec<String>,
}
/// Type alias for the bigtable results from some number of rows
pub type ReadResults = Vec<(RowKey, Vec<RowCell>)>;

/// As each `CellChunk` could be only part of a cell, this method reorganize multiple `CellChunk`
/// from multiple `ReadRowsResponse` into a `Vec<(RowKey, Vec<RowCell>)>`.
pub async fn decode_read_rows_response(
    timeout: &Option<Duration>,
    mut rrr: tonic::Streaming<ReadRowsResponse>,
) -> tonic::Result<ReadResults> {
    let mut rows: Vec<(RowKey, Vec<RowCell>)> = vec![];

    let started = Instant::now();
    while let Some(res) = rrr.message().await.map_err(|e| {
        tracing::error!("Failed to receive message: {:?}", e);
        e
    })? {
        if let Some(timeout) = timeout.as_ref() {
            if Instant::now().duration_since(started) > *timeout {
                return Err(tonic::Status::deadline_exceeded(
                    "Timeout waiting for response",
                ));
            }
        }
        let rows_part = decode_read_rows_response_to_vec(res.chunks);
        for part in rows_part.into_iter() {
            match part {
                Ok(part) => rows.push(part),
                Err(e) => return Err(e),
            }
        }
    }
    Ok(rows)
}

pub fn decode_read_rows_response_to_vec(
    chunks: Vec<CellChunk>,
) -> Vec<tonic::Result<(RowKey, Vec<RowCell>)>> {
    let mut rows: Vec<tonic::Result<(RowKey, Vec<RowCell>)>> = vec![];
    let mut row_key = None;
    let mut row_data: Vec<RowCell> = vec![];

    let mut current_cell = RowCell {
        family_name: "".to_string(),
        qualifier: vec![],
        value: vec![],
        timestamp_micros: 0,
        labels: vec![],
    };

    // If this CellChunk is part of a chunked cell value and this is
    // not the final chunk of that cell, value_size will be set to the
    // total length of the cell value.  The client can use this size
    // to pre-allocate memory to hold the full cell value.
    let mut cell_value_size: usize;

    let mut start_new_cell = false;
    let mut committed_row_cell_count = 0usize;
    let mut start_new_row = false; // Marker for starting a new row. A commit will set this as false

    let mut key_set: HashSet<Vec<u8>> = HashSet::new();
    let mut chunk_value_is_empty: bool;

    if chunks.is_empty() {
        return rows;
    }

    for (i, mut chunk) in chunks.into_iter().enumerate() {
        // The comments for `read_rows_response::CellChunk` provide essential details for
        // understanding how the below decoding works...
        tracing::debug!("chunk {}: {:?}", i, chunk);

        // Starting a new row?
        if !chunk.row_key.is_empty() {
            if row_key.is_none() || row_key.take().unwrap() != chunk.row_key {
                // a new key comes, start_new_row should be false at this time
                if start_new_row {
                    rows.truncate(committed_row_cell_count);
                    rows.push(Err(tonic::Status::internal(
                        "Invalid - no commit before key changes",
                    )));
                    return rows;
                }
                start_new_row = true;
            }
            row_key = Some(chunk.row_key);
        } else {
            // row_key is empty
            if !start_new_row {
                rows.truncate(committed_row_cell_count);
                rows.push(Err(tonic::Status::internal(
                    "Invalid - new row missing row key",
                )));
                return rows;
            }
        }

        // when starting a new cell with new family name, then a qualifier must exist
        if chunk.family_name.is_some()
            && !chunk
                .family_name
                .as_ref()
                .eq(&Some(&current_cell.family_name))
            && chunk.qualifier.is_none()
        {
            rows.truncate(committed_row_cell_count);
            rows.push(Err(tonic::Status::internal(
                "new col family but no specified qualifier",
            )));
            return rows;
        }

        // start a new cell with the existing qualifier or a new qualifier
        if (start_new_cell && !current_cell.qualifier.is_empty()) || chunk.qualifier.is_some() {
            if chunk.value_size == 0 {
                cell_value_size = chunk.value.len();
            } else {
                cell_value_size = chunk.value_size as usize;
            }
            current_cell.value = Vec::with_capacity(cell_value_size);
            // when a new cell with the same qualifier starts, we need to reuse the old qualifier and family_name
            if let Some(family_name) = chunk.family_name {
                current_cell.family_name = family_name;
            }
            if let Some(qualifier) = chunk.qualifier {
                current_cell.qualifier = qualifier;
            }
            current_cell.timestamp_micros = chunk.timestamp_micros;
            current_cell.labels = chunk.labels;
            start_new_cell = false;
        }

        chunk_value_is_empty = chunk.value.is_empty();
        current_cell.value.append(&mut chunk.value);

        // last chunk for the cell?
        if chunk.value_size == 0 {
            // Close up the cell
            if !current_cell.qualifier.is_empty() {
                row_data.push(current_cell.clone());
            }
            // make sure we start a new cell in case the qualifier doesn't change
            start_new_cell = true;
        }

        // End of a row?
        match chunk.row_status {
            None => {
                // more for this row, don't push to row_data or rows vector, let the next
                // chunk close up those vectors.
            }
            Some(RowStatus::CommitRow(flag)) => {
                if let Some(row_key) = row_key.clone() {
                    rows.push(Ok((row_key, row_data)));
                    row_data = vec![];
                }
                if flag {
                    if let Some(row_key) = row_key.clone() {
                        let no_duplicated_key = key_set.insert(row_key);
                        if !no_duplicated_key {
                            rows.truncate(committed_row_cell_count);
                            rows.push(Err(tonic::Status::internal("Invalid - duplicate row key")));
                            return rows;
                        }
                    }
                    if chunk.value_size != 0 {
                        // meaning chunk is not ended yet
                        rows.truncate(committed_row_cell_count);
                        rows.push(Err(tonic::Status::internal(
                            "Invalid - commit with chunk not ended",
                        )));
                        return rows;
                    }

                    committed_row_cell_count = rows.len();
                    start_new_row = false;
                }
            }
            Some(RowStatus::ResetRow(_)) => {
                // ResetRow indicates that the client should drop all previous chunks for
                // `row_key`, as it will be re-read from the beginning.
                row_key = None;
                row_data = vec![];
                start_new_row = false;
                rows.truncate(committed_row_cell_count);

                if !chunk_value_is_empty {
                    rows.truncate(committed_row_cell_count);
                    rows.push(Err(tonic::Status::internal("Invalid - reset with chunk")));
                    return rows;
                }
            }
        }
    }

    if start_new_row && committed_row_cell_count == 0 {
        return vec![Err(tonic::Status::internal("No rows committed"))];
    }

    if start_new_row {
        rows.truncate(committed_row_cell_count);
        rows.push(Err(tonic::Status::internal(
            "Invalid - last row missing commit",
        )));
        return rows;
    }

    rows
}

#[cfg(test)]
mod tests {
    use super::{decode_read_rows_response_to_vec, RowCell};
    use crate::proto::google::bigtable::v2::{
        read_rows_response::cell_chunk::RowStatus, read_rows_response::CellChunk,
    };

    #[test]
    fn test_decode_read_rows_response_multi_rows() {
        let chunks = vec![
            CellChunk {
                row_key: b"row-key-1".to_vec(),
                family_name: Some("family-name-1".to_string()),
                qualifier: Some(b"qualifier-1".to_vec()),
                timestamp_micros: 12345678901,
                labels: vec![],
                value: b"value-1".to_vec(),
                value_size: 0,
                row_status: Some(RowStatus::CommitRow(true)),
            },
            CellChunk {
                row_key: b"row-key-2".to_vec(),
                family_name: Some("family-name-1".to_string()),
                qualifier: Some(b"qualifier-1".to_vec()),
                timestamp_micros: 12345678902,
                labels: vec![],
                value: b"value-2".to_vec(),
                value_size: 0,
                row_status: Some(RowStatus::CommitRow(true)),
            },
        ];
        let rows = decode_read_rows_response_to_vec(chunks);
        assert_eq!(rows.len(), 2);
        assert_eq!(rows[0].as_ref().unwrap().0, b"row-key-1".to_vec());
        assert_eq!(
            rows[0].as_ref().unwrap().1,
            vec![RowCell {
                family_name: "family-name-1".to_string(),
                qualifier: b"qualifier-1".to_vec(),
                value: b"value-1".to_vec(),
                timestamp_micros: 12345678901,
                labels: vec![],
            }],
        );
        assert_eq!(rows[1].as_ref().unwrap().0, b"row-key-2".to_vec());
        assert_eq!(
            rows[1].as_ref().unwrap().1,
            vec![RowCell {
                family_name: "family-name-1".to_string(),
                qualifier: b"qualifier-1".to_vec(),
                value: b"value-2".to_vec(),
                timestamp_micros: 12345678902,
                labels: vec![],
            }],
        );
    }

    #[test]
    fn test_decode_read_rows_response_multi_cells_per_row() {
        let chunks = vec![
            CellChunk {
                row_key: b"row-key-1".to_vec(),
                family_name: Some("family-name-1".to_string()),
                qualifier: Some(b"qualifier-1".to_vec()),
                timestamp_micros: 12345678901,
                labels: vec![],
                value: b"value-1".to_vec(),
                value_size: 0,
                row_status: None,
            },
            CellChunk {
                row_key: vec![],   // API convention to signal "same as previous"
                family_name: None, // ditto
                qualifier: Some(b"qualifier-2".to_vec()),
                timestamp_micros: 12345678902,
                labels: vec![],
                value: b"value-2".to_vec(),
                value_size: 0,
                row_status: Some(RowStatus::CommitRow(true)),
            },
        ];
        let rows = decode_read_rows_response_to_vec(chunks);
        assert_eq!(rows.len(), 1);
        assert_eq!(rows[0].as_ref().unwrap().0, b"row-key-1".to_vec());
        assert_eq!(
            rows[0].as_ref().unwrap().1,
            vec![
                RowCell {
                    family_name: "family-name-1".to_string(),
                    qualifier: b"qualifier-1".to_vec(),
                    value: b"value-1".to_vec(),
                    timestamp_micros: 12345678901,
                    labels: vec![],
                },
                RowCell {
                    family_name: "family-name-1".to_string(),
                    qualifier: b"qualifier-2".to_vec(),
                    value: b"value-2".to_vec(),
                    timestamp_micros: 12345678902,
                    labels: vec![],
                }
            ],
        );
    }

    #[test]
    fn test_decode_read_rows_response_multi_chunks_per_cell() {
        let chunks = vec![
            CellChunk {
                row_key: b"row-key-1".to_vec(),
                family_name: Some("family-name-1".to_string()),
                qualifier: Some(b"qualifier-1".to_vec()),
                timestamp_micros: 12345678901,
                labels: vec![],
                value: b"value-1".to_vec(),
                value_size: 14, // API promises to pre-compute the value size we need
                row_status: None,
            },
            CellChunk {
                row_key: vec![],               // API convention to signal "same as previous"
                family_name: None,             // ditto
                qualifier: None,               // ditto
                timestamp_micros: 12345678902, // in practice this probably won't differ?
                labels: vec![],
                value: b"value-2".to_vec(),
                value_size: 0,
                row_status: Some(RowStatus::CommitRow(true)),
            },
        ];
        let rows = decode_read_rows_response_to_vec(chunks);
        assert_eq!(rows.len(), 1);
        assert_eq!(rows[0].as_ref().unwrap().0, b"row-key-1".to_vec());
        assert_eq!(
            rows[0].as_ref().unwrap().1,
            vec![RowCell {
                family_name: "family-name-1".to_string(),
                qualifier: b"qualifier-1".to_vec(),
                value: b"value-1value-2".to_vec(),
                timestamp_micros: 12345678901,
                labels: vec![],
            },],
        );
    }
}

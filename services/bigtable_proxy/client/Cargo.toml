[package]
name = "bigtable_proxy_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "bigtable_proxy_client"
path = "client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
grpc_client = { path = "../../lib/grpc/client" }
prost = { workspace = true }
prost-wkt = { workspace = true }
prost-wkt-types = { workspace = true }
request_context = { path = "../../lib/request_context" }
serde_json = { workspace = true }
tokio = { workspace = true }
tonic = { workspace = true }
tonic-build = { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
mockall = { version = "0.13.1" }

[build-dependencies]
tonic-build = { workspace = true }
prost-build = {workspace = true}
prost-wkt-build = {workspace = true}

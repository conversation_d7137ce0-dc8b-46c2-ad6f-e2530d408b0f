package fakebigtableproxy

import (
	"bytes"
	"context"
	"fmt"
	"regexp"
	"sync"

	bigtable "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	client "github.com/augmentcode/augment/services/bigtable_proxy/client"
	pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	statusproto "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Row represents a row in the Bigtable.
type row map[string]map[string][]byte

type table struct {
	data map[string]row
}

func (t *table) String() string {
	return fmt.Sprintf("%v", t.data)
}

func NewTable() *table {
	return &table{
		data: make(map[string]row),
	}
}

// InMemoryBigtable is a mock Bigtable implementation that stores data in memory.
type InMemoryBigtableClient struct {
	mu     sync.RWMutex
	tables map[string]*table
}

func (bt *InMemoryBigtableClient) Close() {}

func (bt *InMemoryBigtableClient) Dump() {
	bt.mu.RLock()
	defer bt.mu.RUnlock()

	log.Info().Msgf("InMemoryBigtable: tables=%v", bt.tables)
}

// NewInMemoryBigtable creates a new InMemoryBigtable instance.
func NewInMemoryBigtable(tableNames []string) *InMemoryBigtableClient {
	c := &InMemoryBigtableClient{
		tables: make(map[string]*table),
	}
	for _, tableName := range tableNames {
		c.tables[tableName] = NewTable()
	}
	log.Info().Msgf("NewInMemoryBigtable: tables=%v", tableNames)
	return c
}

func (bt *InMemoryBigtableClient) applyMutation(currentTable *table, rowKey string, mutation *bigtable.Mutation) error {
	switch x := mutation.Mutation.(type) {
	case *bigtable.Mutation_SetCell_:
		family := x.SetCell.FamilyName
		qualifier := string(x.SetCell.ColumnQualifier)
		value := x.SetCell.Value

		if _, exists := currentTable.data[rowKey][family]; !exists {
			currentTable.data[rowKey][family] = make(map[string][]byte)
		}

		log.Info().Msgf("MutateRows: row_key=%s family=%s qualifier=%s value=%v", rowKey, family, qualifier, value)

		currentTable.data[rowKey][family][qualifier] = value
	case *bigtable.Mutation_DeleteFromRow_:
		log.Info().Msgf("MutateRows: DeleteFromRow row_key=%s", rowKey)
		// Delete the entire row
		delete(currentTable.data, rowKey)
	default:
		return status.Errorf(codes.Unimplemented, "mutation type not supported")
	}
	return nil
}

func (bt *InMemoryBigtableClient) CheckTenantDataAccess(ctx context.Context, tenantID string, requestContext *requestcontext.RequestContext) error {
	return nil
}

// MutateRows applies a series of mutations to the given row keys.
func (bt *InMemoryBigtableClient) MutateRows(ctx context.Context, tenantID string, tableName pb.TableName,
	entries []*bigtable.MutateRowsRequest_Entry, requestContext *requestcontext.RequestContext,
) ([]*pb.MutateRowsResponse, error) {
	bt.mu.Lock()
	defer bt.mu.Unlock()

	log.Info().Msgf("MutateRows: tenant_id=%s table_name=%s entries=%v", tenantID, tableName.String(), entries)

	table, exists := bt.tables[tableName.String()]
	if !exists {
		log.Warn().Msgf("MutateRows: tenant_id=%s table_name=%s table not found", tenantID, tableName)
		return nil, status.Errorf(codes.NotFound, "table not found")
	}

	response := &pb.MutateRowsResponse{}
	for i, entry := range entries {
		rowKey := string(entry.RowKey)
		if _, exists := table.data[rowKey]; !exists {
			table.data[rowKey] = make(row)
		}

		var rowErr error

		for _, mutation := range entry.Mutations {
			log.Info().Msgf("MutateRows: tenant_id=%s table_name=%s row_key=%s mutation=%v", tenantID, tableName, rowKey, mutation)

			err := bt.applyMutation(table, rowKey, mutation)
			if err != nil {
				log.Error().Err(err).Msgf("MutateRows: tenant_id=%s table_name=%s row_key=%s mutation=%v", tenantID, tableName, rowKey, mutation)
				rowErr = err
			}
		}

		if rowErr != nil {
			response.Entries = append(response.Entries, &bigtable.MutateRowsResponse_Entry{
				Index: int64(i),
				Status: &statusproto.Status{
					Code:    int32(codes.Internal),
					Message: rowErr.Error(),
				},
			})
		} else {
			response.Entries = append(response.Entries, &bigtable.MutateRowsResponse_Entry{
				Index: int64(i),
				Status: &statusproto.Status{
					Code: int32(codes.OK),
				},
			})
		}
	}

	return []*pb.MutateRowsResponse{response}, nil
}

// Check if a rowKey matches any of the row keys or ranges in the rowSet.
func rowKeyMatches(rowKey string, rowSet *bigtable.RowSet) bool {
	// Check if the rowKey is explicitly included in the rowKeys list
	for _, key := range rowSet.RowKeys {
		if bytes.Equal([]byte(rowKey), key) {
			return true
		}
	}

	// Check if the rowKey falls within any of the rowRanges
	for _, rowRange := range rowSet.RowRanges {
		// Determine if the rowKey is within the range [startKey, endKey)
		if rowRangeMatches(rowKey, rowRange) {
			return true
		}
	}

	return false
}

// Check if a rowKey falls within a specific rowRange.
func rowRangeMatches(rowKey string, rowRange *bigtable.RowRange) bool {
	startKey := rowRange.StartKey
	endKey := rowRange.EndKey

	// Handle different range types
	switch rowRange.StartKey.(type) {
	case *bigtable.RowRange_StartKeyClosed:
		if bytes.Compare([]byte(rowKey), startKey.(*bigtable.RowRange_StartKeyClosed).StartKeyClosed) < 0 {
			return false
		}
	case *bigtable.RowRange_StartKeyOpen:
		if bytes.Compare([]byte(rowKey), startKey.(*bigtable.RowRange_StartKeyOpen).StartKeyOpen) <= 0 {
			return false
		}
	}

	switch rowRange.EndKey.(type) {
	case *bigtable.RowRange_EndKeyClosed:
		if bytes.Compare([]byte(rowKey), endKey.(*bigtable.RowRange_EndKeyClosed).EndKeyClosed) > 0 {
			return false
		}
	case *bigtable.RowRange_EndKeyOpen:
		if bytes.Compare([]byte(rowKey), endKey.(*bigtable.RowRange_EndKeyOpen).EndKeyOpen) >= 0 {
			return false
		}
	}

	return true
}

func runFilter(cell *client.RowCell, filter *bigtable.RowFilter) (bool, error) {
	if filter == nil {
		return true, nil
	}
	if filter.Filter == nil {
		return true, nil
	}
	switch x := filter.Filter.(type) {
	case *bigtable.RowFilter_PassAllFilter:
		return true, nil
	case *bigtable.RowFilter_BlockAllFilter:
		return false, nil
	case *bigtable.RowFilter_Chain_:
		for _, f := range x.Chain.Filters {
			if ok, err := runFilter(cell, f); err != nil {
				return false, err
			} else if !ok {
				return false, nil
			}
		}
		return true, nil
	case *bigtable.RowFilter_FamilyNameRegexFilter:
		re, err := regexp.Compile(x.FamilyNameRegexFilter)
		if err != nil {
			return false, err
		}
		if re.MatchString(cell.FamilyName) {
			return true, nil
		}
		return false, nil
	case *bigtable.RowFilter_ColumnQualifierRegexFilter:
		re, err := regexp.Compile(string(x.ColumnQualifierRegexFilter))
		if err != nil {
			return false, err
		}
		if re.Match(cell.Qualifier) {
			return true, nil
		}
		return false, nil
	case *bigtable.RowFilter_ValueRegexFilter:
		re, err := regexp.Compile(string(x.ValueRegexFilter))
		if err != nil {
			return false, err
		}
		if re.Match(cell.Value) {
			return true, nil
		}
		return false, nil
	case *bigtable.RowFilter_CellsPerColumnLimitFilter:
		// TODO: not a perfect implementation. This relies on the fake client
		// not storing more than one cell per column (i.e. family + qualifier).
		return true, nil
	default:
		log.Warn().Msgf("runFilter: unsupported filter type: %T", x)
		return true, status.Error(codes.Unimplemented, "unsupported filter type")
	}
}

// ReadRows reads rows matching the given row range and filters.
func (bt *InMemoryBigtableClient) ReadRows(ctx context.Context, tenantID string, tableName pb.TableName,
	rows *bigtable.RowSet, filter *bigtable.RowFilter, rowsLimit int64, requestContext *requestcontext.RequestContext,
	opts ...grpc.CallOption,
) ([]*client.Row, error) {
	bt.mu.RLock()
	defer bt.mu.RUnlock()

	log.Info().Msgf("ReadRows: tenant_id=%s table_name=%s rows=%v filter=%v", tenantID, tableName.String(), rows, filter)

	table, exists := bt.tables[tableName.String()]
	if !exists {
		return nil, status.Errorf(codes.NotFound, "table not found")
	}

	response := []*client.Row{}

	for rowKey, rowData := range table.data {
		if !rowKeyMatches(rowKey, rows) {
			continue
		}

		// Construct the ReadRowsResponse_Row from the rowData
		readRow := &client.Row{
			RowKey: []byte(rowKey),
		}
		for family, columns := range rowData {
			for qualifier, value := range columns {
				cell := &client.RowCell{
					FamilyName: family,
					Qualifier:  []byte(qualifier),
					Value:      value,
				}
				ok, err := runFilter(cell, filter)
				if err != nil {
					return nil, err
				}
				if !ok {
					continue
				}
				readRow.Cells = append(readRow.Cells, cell)
			}
		}
		response = append(response, readRow)
	}

	return response, nil
}

// CheckAndMutateRow atomically checks a condition and applies a mutation if the condition is met.
func (bt *InMemoryBigtableClient) CheckAndMutateRow(ctx context.Context, tenantID string, tableName pb.TableName,
	rowKey []byte, predicateFilter *bigtable.RowFilter, trueMutations []*bigtable.Mutation, falseMutations []*bigtable.Mutation, requestContext *requestcontext.RequestContext,
) (*pb.CheckAndMutateRowResponse, error) {
	bt.mu.RLock()
	defer bt.mu.RUnlock()

	log.Info().Msgf("CheckAndMutateRow: tenant_id=%s table_name=%s row=%s filter=%v", tenantID, tableName.String(), (string)(rowKey), predicateFilter)

	table, exists := bt.tables[tableName.String()]
	if !exists {
		return nil, status.Errorf(codes.NotFound, "table not found")
	}

	match := false

	for dataRowKey, rowData := range table.data {
		if dataRowKey != string(rowKey) {
			continue
		}

		for family, columns := range rowData {
			for qualifier, value := range columns {
				cell := &client.RowCell{
					FamilyName: family,
					Qualifier:  []byte(qualifier),
					Value:      value,
				}
				ok, err := runFilter(cell, predicateFilter)
				if err != nil {
					return nil, err
				}
				if ok {
					match = true
				}
			}
		}
	}
	if match {
		log.Info().Msgf("CheckAndMutateRow: applying true mutations")
		for _, mutation := range trueMutations {
			err := bt.applyMutation(table, string(rowKey), mutation)
			if err != nil {
				return nil, err
			}
		}
	} else {
		log.Info().Msgf("CheckAndMutateRow: applying false mutations")
		for _, mutation := range falseMutations {
			err := bt.applyMutation(table, string(rowKey), mutation)
			if err != nil {
				return nil, err
			}
		}
	}

	response := &pb.CheckAndMutateRowResponse{
		PredicateMatched: match,
	}

	return response, nil
}

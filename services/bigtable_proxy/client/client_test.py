"""Unit tests for the python client library."""

from client import Row<PERSON><PERSON>, decode_read_rows_response_to_vec
from google.protobuf import wrappers_pb2

from third_party.proto.google.bigtable.v2 import bigtable_pb2 as bigtable_types


def test_decode_read_rows_response_multi_rows():
    chunks = [
        bigtable_types.ReadRowsResponse.CellChunk(
            row_key=b"row-key-1",
            family_name=wrappers_pb2.StringValue(value="family-name-1"),
            qualifier=wrappers_pb2.BytesValue(value=b"qualifier-1"),
            timestamp_micros=12345678901,
            labels=[],
            value=b"value-1",
            value_size=0,
            commit_row=True,
        ),
        bigtable_types.ReadRowsResponse.CellChunk(
            row_key=b"row-key-2",
            family_name=wrappers_pb2.StringValue(value="family-name-1"),
            qualifier=wrappers_pb2.BytesValue(value=b"qualifier-1"),
            timestamp_micros=12345678902,
            labels=[],
            value=b"value-2",
            value_size=0,
            commit_row=True,
        ),
    ]
    rows = decode_read_rows_response_to_vec(chunks)
    assert len(rows) == 2
    assert rows[0][0] == b"row-key-1"
    assert rows[0][1] == [
        RowCell(
            family_name="family-name-1",
            qualifier=b"qualifier-1",
            value=bytearray(b"value-1"),
            timestamp_micros=12345678901,
            labels=[],
        )
    ]
    assert rows[1][0] == b"row-key-2"
    assert rows[1][1] == [
        RowCell(
            family_name="family-name-1",
            qualifier=b"qualifier-1",
            value=bytearray(b"value-2"),
            timestamp_micros=12345678902,
            labels=[],
        )
    ]


def test_decode_read_rows_response_multi_cells_per_row():
    chunks = [
        bigtable_types.ReadRowsResponse.CellChunk(
            row_key=b"row-key-1",
            family_name=wrappers_pb2.StringValue(value="family-name-1"),
            qualifier=wrappers_pb2.BytesValue(value=b"qualifier-1"),
            timestamp_micros=12345678901,
            labels=[],
            value=b"value-1",
            value_size=0,
        ),
        bigtable_types.ReadRowsResponse.CellChunk(
            row_key=b"",  # API convention to signal "same as previous"
            family_name=None,  # ditto
            qualifier=wrappers_pb2.BytesValue(value=b"qualifier-2"),
            timestamp_micros=12345678902,
            labels=[],
            value=b"value-2",
            value_size=0,
            commit_row=True,
        ),
    ]

    rows = decode_read_rows_response_to_vec(chunks)
    assert len(rows) == 1
    assert rows[0][0] == b"row-key-1"
    assert rows[0][1] == [
        RowCell(
            family_name="family-name-1",
            qualifier=b"qualifier-1",
            value=bytearray(b"value-1"),
            timestamp_micros=12345678901,
            labels=[],
        ),
        RowCell(
            family_name="family-name-1",
            qualifier=b"qualifier-2",
            value=bytearray(b"value-2"),
            timestamp_micros=12345678902,
            labels=[],
        ),
    ]


def test_decode_read_rows_response_multi_chunks_per_cell():
    chunks = [
        bigtable_types.ReadRowsResponse.CellChunk(
            row_key=b"row-key-1",
            family_name=wrappers_pb2.StringValue(value="family-name-1"),
            qualifier=wrappers_pb2.BytesValue(value=b"qualifier-1"),
            timestamp_micros=12345678901,
            labels=[],
            value=b"value-1",
            value_size=14,  # API promises to pre-compute the value size we need
        ),
        bigtable_types.ReadRowsResponse.CellChunk(
            row_key=b"",  # API convention to signal "same as previous"
            family_name=None,  # ditto
            qualifier=None,  # ditto
            timestamp_micros=12345678902,  # in practice this probably won't differ?
            labels=[],
            value=b"value-2",
            value_size=0,
            commit_row=True,
        ),
    ]
    rows = decode_read_rows_response_to_vec(chunks)
    assert len(rows) == 1
    assert rows[0][0] == b"row-key-1"
    assert rows[0][1] == [
        RowCell(
            family_name="family-name-1",
            qualifier=b"qualifier-1",
            value=bytearray(b"value-1value-2"),
            timestamp_micros=12345678901,
            labels=[],
        )
    ]

"""A Python client library for the bigtable proxy service."""

from __future__ import annotations

import copy
import logging
import os
import pydantic
import time
import typing
from dataclasses import dataclass

import grpc

from base.python.grpc import client_options
from services.bigtable_proxy import bigtable_proxy_pb2_grpc, bigtable_proxy_pb2
from services.lib.request_context.request_context import RequestContext

from third_party.proto.google.bigtable.v2 import data_pb2 as bigtable_data
from third_party.proto.google.bigtable.v2 import bigtable_pb2 as bigtable_types

# default options: see https://github.com/googleapis/python-bigtable/blob/main/google/cloud/bigtable/client.py
_GRPC_CHANNEL_OPTIONS = (
    ("grpc.max_send_message_length", 1 << 28),
    ("grpc.max_receive_message_length", 1 << 28),
    ("grpc.keepalive_time_ms", 30000),
    ("grpc.keepalive_timeout_ms", 10000),
)


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> bigtable_proxy_pb2_grpc.BigtableProxyStub:
    """Setup the client stub for the bigtable proxy service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not options:
        options = list(_GRPC_CHANNEL_OPTIONS)
    else:
        options = list(options)
        for key, value in _GRPC_CHANNEL_OPTIONS:
            if any(k == key for k, _ in options):
                continue
            options.append((key, value))
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = bigtable_proxy_pb2_grpc.BigtableProxyStub(channel)
    return stub


class BigtableProxyClient(typing.Protocol):
    """Interface for the bigtable proxy service."""

    def mutate_rows(
        self,
        request_context: RequestContext,
        tenant_id: str,
        table_name: bigtable_proxy_pb2.TableName.ValueType,
        entries: list[bigtable_types.MutateRowsRequest.Entry],
    ) -> typing.List[bigtable_proxy_pb2.MutateRowsResponse]:
        raise NotImplementedError()

    def read_rows(
        self,
        request_context: RequestContext,
        tenant_id: str | None,
        table_name: bigtable_proxy_pb2.TableName.ValueType,
        rows: bigtable_data.RowSet,
        filter: bigtable_data.RowFilter,
        rows_limit: int,
    ) -> typing.Iterator[typing.Tuple[bytes, typing.List[RowCell]]]:
        raise NotImplementedError()

    def check_and_mutate_row(
        self,
        request_context: RequestContext,
        tenant_id: str | None,
        table_name: bigtable_proxy_pb2.TableName.ValueType,
        row_key: bytes,
        predicate_filter: bigtable_data.RowFilter,
        true_mutations: list[bigtable_data.Mutation],
        false_mutations: list[bigtable_data.Mutation],
    ) -> bigtable_proxy_pb2.CheckAndMutateRowResponse:
        raise NotImplementedError()


class GrpcBigtableProxyClient(BigtableProxyClient):
    """Class to call bigtable proxy APIs remotely."""

    def __init__(
        self,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        self.endpoint = endpoint
        self.stub = setup_stub(endpoint, credentials, options)

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: typing.Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new bigtable proxy stub from endpoint and credentials."""
        return cls(endpoint, credentials, options=options)

    def mutate_rows(
        self,
        request_context: RequestContext,
        tenant_id: str | None,
        table_name: bigtable_proxy_pb2.TableName.ValueType,
        entries: list[bigtable_types.MutateRowsRequest.Entry],
    ) -> typing.List[bigtable_proxy_pb2.MutateRowsResponse]:
        request = bigtable_proxy_pb2.MutateRowsRequest(
            tenant_id=tenant_id or "",
            table_name=table_name,
            entries=entries,
        )
        return self.stub.MutateRows(request, metadata=request_context.to_metadata())

    def read_rows(
        self,
        request_context: RequestContext,
        tenant_id: str | None,
        table_name: bigtable_proxy_pb2.TableName.ValueType,
        rows: bigtable_data.RowSet,
        filter: bigtable_data.RowFilter,
        rows_limit: int,
    ) -> typing.Iterator[typing.Tuple[bytes, typing.List[RowCell]]]:
        request = bigtable_proxy_pb2.ReadRowsRequest(
            tenant_id=tenant_id or "",
            table_name=table_name,
            rows=rows,
            filter=filter,
            rows_limit=rows_limit,
        )
        raw_response = self.stub.ReadRows(
            request, metadata=request_context.to_metadata()
        )
        return decode_read_rows_response(None, raw_response)

    def check_and_mutate_row(
        self,
        request_context: RequestContext,
        tenant_id: str | None,
        table_name: bigtable_proxy_pb2.TableName.ValueType,
        row_key: bytes,
        predicate_filter: bigtable_data.RowFilter,
        true_mutations: list[bigtable_data.Mutation],
        false_mutations: list[bigtable_data.Mutation],
    ) -> bigtable_proxy_pb2.CheckAndMutateRowResponse:
        request = bigtable_proxy_pb2.CheckAndMutateRowRequest(
            tenant_id=tenant_id or "",
            table_name=table_name,
            row_key=row_key,
            predicate_filter=predicate_filter,
            true_mutations=true_mutations,
            false_mutations=false_mutations,
        )

        return self.stub.CheckAndMutateRow(
            request, metadata=request_context.to_metadata()
        )

    def __str__(self) -> str:
        return f"GrpcBigtableProxyClient(endpoint={self.endpoint})"


@dataclass
class RowCell:
    family_name: str
    qualifier: bytes
    value: bytearray
    timestamp_micros: int
    labels: typing.MutableSequence[str]


# Adapted from decode_read_rows_response in client.rs
def decode_read_rows_response(
    timeout: typing.Optional[pydantic.PositiveInt],
    rrr: typing.Iterable[bigtable_proxy_pb2.ReadRowsResponse],
) -> typing.Iterator[typing.Tuple[bytes, typing.List[RowCell]]]:
    """Decode the read rows response."""
    started = time.time()
    for res in rrr:
        if timeout is not None and time.time() - started > timeout:
            raise Exception("Timeout waiting for response")
        new_rows = decode_read_rows_response_to_vec(res.chunks)
        for row in new_rows:
            yield row


# Adapted from decode_read_rows_response_to_vec in client.rs
# TODO: add unit tests of this
def decode_read_rows_response_to_vec(
    chunks: typing.Iterable[bigtable_types.ReadRowsResponse.CellChunk],
) -> typing.List[typing.Tuple[bytes, typing.List[RowCell]]]:
    # State for the entire result
    rows: typing.List[typing.Tuple[bytes, typing.List[RowCell]]] = []
    # State for the row currently being built
    current_row_key: bytes = b""
    current_row_data: typing.List[RowCell] = []
    # State for the cell currently being built
    current_cell = RowCell(
        family_name="",
        qualifier=b"",
        value=bytearray(),
        timestamp_micros=0,
        labels=list(),
    )
    # State machine for aggregating chunks into cells and rows respectively
    start_new_cell = False
    committed_row_cell_count = 0
    # Marker for starting a new row. A commit will set this as false
    # This state is used for validity checks only-- it doesn't control any other behavior
    start_new_row = False

    key_set: typing.Set[bytes] = set()

    if not chunks:
        return rows

    for chunk in chunks:
        # Starting a new row?
        if len(chunk.row_key) > 0:
            if len(current_row_key) == 0 or current_row_key != chunk.row_key:
                # a new key comes, start_new_row should be false at this time
                if start_new_row:
                    raise Exception("Invalid - no commit before key changes")
                start_new_row = True
            current_row_key = chunk.row_key
        else:
            # row_key is empty
            if not start_new_row:
                raise Exception("Invalid - new row missing row key")

        # when starting a new cell with new family name, then a qualifier must exist
        if (
            chunk.family_name.value
            and not chunk.family_name.value == current_cell.family_name
            and not chunk.qualifier.value
        ):
            raise Exception("new col family but no specified qualifier")

        # start a new cell with the existing cell_name or new cell_name (chunk.qualifier)
        if (
            start_new_cell and len(current_cell.qualifier) > 0
        ) or chunk.qualifier.value:
            current_cell.value = bytearray()
            # when a new cell with the same qualifier starts, reuse the old family_name and qualifier
            if chunk.family_name.value:
                current_cell.family_name = chunk.family_name.value
            if chunk.qualifier.value:
                current_cell.qualifier = chunk.qualifier.value
            current_cell.timestamp_micros = chunk.timestamp_micros
            current_cell.labels = [
                label for label in chunk.labels
            ]  # TODO: more graceful type converison?
            start_new_cell = False

        current_cell.value.extend(chunk.value)

        # last chunk for the cell?
        if chunk.value_size == 0:
            # Close up the cell
            if len(current_cell.qualifier) > 0:
                current_row_data.append(copy.copy(current_cell))
            # make sure we start a new cell in case the qualifier doesn't change
            start_new_cell = True

        # End of a row?
        if chunk.HasField("row_status"):
            if chunk.WhichOneof("row_status") == "commit_row":
                if len(current_row_key) > 0:
                    rows.append((current_row_key, current_row_data))
                    current_row_data = []
                if chunk.commit_row:
                    if len(current_row_key) > 0:
                        no_duplicated_key = current_row_key not in key_set
                        if not no_duplicated_key:
                            raise Exception("Invalid - duplicate row key")
                        key_set.add(current_row_key)
                    if chunk.value_size != 0:
                        # meaning chunk is not ended yet
                        raise Exception("Invalid - commit with chunk not ended")

                    committed_row_cell_count = len(rows)
                    start_new_row = False
            elif chunk.WhichOneof("row_status") == "reset_row":
                # ResetRow indicates that the client should drop all previous chunks for
                # `row_key`, as it will be re-read from the beginning.
                current_row_key = b""
                current_row_data = []
                start_new_row = False
                rows = rows[:committed_row_cell_count]

    if start_new_row and committed_row_cell_count == 0:
        raise Exception("No rows committed")

    if start_new_row:
        raise Exception("Invalid - last row missing commit")

    return rows

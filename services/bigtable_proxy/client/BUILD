load("@bazel_skylib//rules:build_test.bzl", "build_test")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_library", "go_test")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        requirement("pydantic"),
        "//base/python/grpc:client_options",
        "//services/bigtable_proxy:bigtable_proxy_py_proto",
        "//services/lib/request_context:request_context_py",
    ],
)

go_library(
    name = "client_go",
    srcs = [
        "client.go",
        "emulator_client.go",
        "filter.go",
    ],
    importpath = "github.com/augmentcode/augment/services/bigtable_proxy/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/lib/request_context:request_context_go",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_api//option",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
    ],
)

go_library(
    name = "fake_client_go",
    srcs = ["fake_client.go"],
    importpath = "github.com/augmentcode/augment/services/bigtable_proxy/client/fake_client",
    visibility = ["//services:__subpackages__"],
    deps = [
        ":client_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/lib/request_context:request_context_go",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

# Build test to ensure that the client code can be built.
build_test(
    name = "client_build_test",
    targets = [
        ":client_py",
        ":client_go",
        ":fake_client_go",
        ":client_rs",
    ],
)

# This is a temporary library to be removed once the migration is complete.
py_library(
    name = "migration_client_py",
    srcs = ["migrate_only_client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("pydantic"),
        "//base/python/grpc:client_options",
        "//services/bigtable_proxy:bigtable_proxy_py_proto",
        "//services/lib/request_context:request_context_py",
    ],
)

pytest_test(
    name = "client_py_test",
    srcs = ["client_test.py"],
    deps = [
        ":client_py",
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["client.rs"],
    aliases = aliases(),
    crate_name = "bigtable_proxy_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/rust/tracing-tonic",
        "//services/lib/grpc/auth:grpc_auth",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/bigtable_proxy:bigtable_proxy_proto",
        "@googleapis//google/bigtable/v2:bigtable.proto",
        "@googleapis//google/type:date_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

rust_test(
    name = "bigtable_proxy_client_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":client_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

go_test(
    name = "client_go_test",
    srcs = ["client_test.go"],
    embed = [":client_go"],
    deps = [
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@org_golang_google_protobuf//types/known/wrapperspb",
    ],
)

go_library(
    name = "mocks",
    srcs = ["mocks/mock_bigtable_proxy_client.go"],
    importpath = "github.com/augmentcode/augment/services/bigtable_proxy/client/mocks",
    visibility = ["//visibility:public"],
    deps = [
        ":client_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/lib/request_context:request_context_go",
        "@com_github_golang_mock//gomock",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb:go_default_library",
        "@org_golang_google_grpc//:grpc",
    ],
)

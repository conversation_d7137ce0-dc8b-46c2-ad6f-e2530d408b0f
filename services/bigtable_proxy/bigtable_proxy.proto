syntax = "proto3";

package bigtable_proxy;

import "google/bigtable/v2/bigtable.proto";
import "google/bigtable/v2/data.proto";

// Subset of https://github.com/googleapis/googleapis/blob/master/google/bigtable/v2/bigtable.proto
// with only the methods currently used.
service BigtableProxy {
  rpc MutateRows(MutateRowsRequest) returns (stream MutateRowsResponse);
  rpc ReadRows(ReadRowsRequest) returns (stream ReadRowsResponse);
  rpc CheckAndMutateRow(CheckAndMutateRowRequest) returns (CheckAndMutateRowResponse);
  // This is temporary and not a subset of the bigtable proto
  // this will return when the migration starts and then run in the background until complete
  // restarting a migration should be fine, but will just start again from the beginning.
  // An error during the migration itself will just be retried, potentially indefinitely.
  rpc Migrate(MigrateRequest) returns (MigrateResponse);

  // strictly speaking one can delete rows via MutateRows, but it is very cumbersome.
  // this is for admin operations only. Deletions are critical mutating operations, but they
  // cannot leak customer data
  //
  // requires BIGTABLE_DELETE scope
  rpc DeleteRows(DeleteRowsRequest) returns (stream DeleteRowsResponse);

  // Check if the tenant has access to data stored in bigtable proxy.
  // Tenants with encryption enabled will check if the tenant's current key is accessible.
  // Tenants without encryption enabled will always return true.
  rpc CheckTenantDataAccess(CheckTenantDataAccessRequest) returns (CheckTenantDataAccessResponse);
}

// Confine tables to the those that are actually supported in the Augment backend
enum TableName {
  CONTENT_MANAGER = 0;
  REQUEST_INSIGHT_MAIN = 1 [deprecated = true];
  SETTINGS = 2;
  GITHUB = 3;
  SHARE = 4;
  SLACK_MAPPINGS = 5;
  REMOTE_AGENTS = 6;
  WORKING_SET = 7;
}

message MigrateRequest {
  string tenant_id = 1;
  TableName table_name = 2;
}

message MigrateResponse {
  // A started=false response just indicates the migration didn't start.
  // If a bad request was given preventing a start, an error will be returned.
  // If an error occurs in the migration after start, the migration will just retry the
  // erroring batch again (potentially indefinitely).
  bool started = 1;
}

message MutateRowsRequest {
  // New field relative to google.bigtable.v2.MutateRowsRequest.
  // Required if the request uses a namespace-level service token, ignored otherwise.
  string tenant_id = 1;

  TableName table_name = 2;
  repeated google.bigtable.v2.MutateRowsRequest.Entry entries = 3;
  // google.bigtable.v2.MutateRowsRequest has other fields but we don't support them yet.
}

message MutateRowsResponse {
  // clients have to check the status code and message on each entry.
  // the MutateRows call can succeed partially, and the client needs to handle that.
  repeated google.bigtable.v2.MutateRowsResponse.Entry entries = 1;
  // google.bigtable.v2.MutateRowsResponse has other fields but we don't support them yet.
}

message ReadRowsRequest {
  // New field relative to google.bigtable.v2.ReadRowsRequest.
  // Required if the request uses a namespace-level service token, ignored otherwise.
  string tenant_id = 1;

  TableName table_name = 2;
  google.bigtable.v2.RowSet rows = 3;
  google.bigtable.v2.RowFilter filter = 4;
  int64 rows_limit = 5;
  // google.bigtable.v2.ReadRowsRequest has other fields but we don't support them yet.
}

message ReadRowsResponse {
  repeated google.bigtable.v2.ReadRowsResponse.CellChunk chunks = 1 [debug_redact = true];
  // google.bigtable.v2.ReadRowsResponse has other fields but we don't support them yet.
}

message CheckAndMutateRowRequest {
  // New field relative to google.bigtable.v2.CheckAndMutateRowRequest.
  // Required if the request uses a namespace-level service token, ignored otherwise.
  string tenant_id = 1;

  TableName table_name = 2;
  bytes row_key = 3 [debug_redact = true];
  google.bigtable.v2.RowFilter predicate_filter = 4 [debug_redact = true];
  repeated google.bigtable.v2.Mutation true_mutations = 5 [debug_redact = true];
  repeated google.bigtable.v2.Mutation false_mutations = 6 [debug_redact = true];
  // google.bigtable.v2.CheckAndMutateRowRequest has other fields but we don't support them yet.
}

message CheckAndMutateRowResponse {
  // Verbatim from google.bigtable.v2.CheckAndMutateRowResponse
  bool predicate_matched = 1;
}

message DeleteRowsRequest {
  string tenant_id = 1;

  TableName table_name = 2;
  repeated bytes row_keys = 3 [debug_redact = true];
}

message DeleteRowsResponse {
  repeated google.bigtable.v2.MutateRowsResponse.Entry entries = 1;
}

message CheckTenantDataAccessRequest {
  string tenant_id = 1;
}

message CheckTenantDataAccessResponse {
  bool has_access = 1;
}

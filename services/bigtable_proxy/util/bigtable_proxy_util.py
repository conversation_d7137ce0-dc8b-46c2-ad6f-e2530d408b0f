"""CLI Utility for bigtable proxy."""

import argparse
import logging
import sys

import pydantic

from base.logging.console_logging import setup_console_logging
from services.lib.grpc import grpc_args_parser, token_parser
from services.lib.request_context.request_context import Request<PERSON>ontext
from services.bigtable_proxy.client import migrate_only_client


def _get_request_context(token: pydantic.SecretStr):
    return RequestContext.create(auth_token=token)


def _migrate(
    rpc_client: migrate_only_client.MigrateOnlyBigtableClient,
    args,
    token: pydantic.SecretStr,
):
    assert args.table_name, "table name must be set."
    assert args.tenant_id, "tenant id must be set."
    request_context = _get_request_context(token)
    started = rpc_client.migrate(
        str(args.tenant_id), str(args.table_name), request_context=request_context
    )
    return started


def main():
    """Main function."""
    setup_console_logging(add_timestamp=False)
    parser = argparse.ArgumentParser()
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)
    subparsers = parser.add_subparsers()
    migration_parser = subparsers.add_parser("migrate")
    migration_parser.set_defaults(action="migrate")
    migration_parser.add_argument("--table-name", help="table name", default="")
    migration_parser.add_argument("--tenant-id", help="tenatn id", default="")

    # Can be extended with non migration actions

    parser.set_defaults(action=None)

    args = parser.parse_args()
    try:
        with grpc_args_parser.create_client(
            args,
            migrate_only_client.MigrateOnlyBigtableClient.create_for_endpoint,
            default_service_name="bigtable-proxy-svc",
            default_endpoint="bigtable-proxy-svc:50051",
        ) as rpc_client:
            token = token_parser.get_token(args)
            if args.action == "migrate":
                _migrate(rpc_client, args, token)
            else:
                sys.exit(1)
    except KeyboardInterrupt:
        sys.exit(1)
    except argparse.ArgumentError as e:
        logging.error("%s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()

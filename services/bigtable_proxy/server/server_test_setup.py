"""BT proxy server test setup for integration tests."""

import json
import os
import tempfile
from dataclasses import asdict, dataclass, field
from typing import Any, Dict, Optional

import grpc

from base.test_utils import process
from services.bigtable_proxy import bigtable_proxy_pb2_grpc


@dataclass
class BigtableProxyConfig:
    """Bigtable configuration."""

    bind_address: str = "127.0.0.1:0"
    central_content_manager_table_name: str = "central-content-manager"
    content_manager_table_name: str = "shard1-content-manager"
    central_settings_table_name: str = "central-settings"
    central_github_table_name: str = "central-github"
    central_share_table_name: str = "central-share"
    central_slack_mappings_table_name: str = "central-slack-mappings"
    central_remote_agents_table_name: str = "central-remote-agents"
    central_working_set_table_name: str = "central-working-set"
    # copied from test_utils/bigtable_setup
    bigtable_project_id: str = "google-cloud-bigtable-emulator"
    bigtable_instance_name: str = "test-instance"
    central_bigtable_instance_name: str = "test-instance"
    passthrough: bool = False
    # not relevant in tests / server should handle bypassing all this
    metrics_server_bind_address: Optional[str] = "0.0.0.0"
    metrics_server_port: Optional[int] = 9090
    feature_flags_sdk_key_path: Optional[str] = None
    server_mtls_config: Optional[str] = None
    central_client_mtls_config: Optional[str] = None
    auth_config: Dict[str, Any] = field(
        default_factory=lambda: {
            "token_exchange_endpoint": "",
            "token_exchange_request_timeout_s": 10.0,
        }
    )
    tenant_watcher_endpoint: str = ""
    health_logger_frequency_s: float = 0
    bigtable_timeout_secs: float = 30.0

    # Tests use the mock KMS client
    use_mock_kms: bool = True


metrics_port = 9090


def next_metrics_port():
    global metrics_port
    metrics_port += 1
    return metrics_port


def start_test_bigtable_proxy(
    tex_endpoint: str, tenant_watcher_port: int, passthrough: bool = False
):
    """Fixture to start the bigtable proxy server."""

    dict = asdict(BigtableProxyConfig())
    dict["metrics_server_port"] = next_metrics_port()
    dict["auth_config"]["token_exchange_endpoint"] = tex_endpoint
    dict["passthrough"] = passthrough
    dict["tenant_watcher_endpoint"] = f"localhost:{tenant_watcher_port}"
    if passthrough:
        # Pretend we are in a legacy namespace
        dict["content_manager_table_name"] = "augment-content-manager"
    config_content = json.dumps(dict)

    with tempfile.NamedTemporaryFile(mode="w+t") as config_file:
        config_file.write(config_content)
        config_file.flush()
        env = os.environ.copy()
        env["POD_NAMESPACE"] = "test-namespace"
        with process.ServerManager(
            [
                "services/bigtable_proxy/server/main/server_/server",
                "--config-file",
                config_file.name,
            ],
            env=env,
            redirect_stderr=True,
        ) as p:
            m = process.wait_for_line(
                p.stdout,
                r'.*"message":"Listening on (\d+\.\d+\.\d+\.\d+:\d+)".*',
                timeout_secs=30,
            )
            p.detach_stdout()
            port = int(m.group(1).split(":")[1])
            channel = grpc.insecure_channel("127.0.0.1:%d" % port)
            stub = bigtable_proxy_pb2_grpc.BigtableProxyStub(channel)
            yield stub

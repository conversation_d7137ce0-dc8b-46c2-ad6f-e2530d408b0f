// K8S deployment file for the bigtable proxy
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local cmkLib = import 'deploy/common/cmk_lib.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local appName = 'bigtable-proxy';
  // we do not want to allow calling by grpc-debug as it would prevent proper audit logging
  // okay in DEV and STAGING
  local services = grpcLib.grpcService(appName=appName, namespace=namespace, exposeToGrpcDebug=env != 'PROD');
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);
  // central client certificate for MTLS
  local centralClientCert = certLib.createCentralClientCert(name='%s-central-client-certificate' % appName,
                                                            namespace=namespace,
                                                            appName=appName,
                                                            env=env,
                                                            dnsNames=grpcLib.grpcServiceNames(appName, namespace));
  // client certificate for MTLS (not used by the service, only the health check)
  local clientCert = certLib.createClientCert(name='%s-client-certificate' % appName,
                                              appName=appName,
                                              namespace=namespace);
  // server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName));
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local centralNamespace = cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud);
  local bigtableNamespace = if namespace_config.flags.passthroughBigtableProxy then namespace else cloudInfo.getCentralNamespaceForNamespace(env=env, namespace=namespace, cloud=cloud);
  local contentManagerBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=bigtableNamespace, tableName='content-manager');
  local contentManagerBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,
    app=appName,
    table=contentManagerBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix=if !namespace_config.flags.passthroughBigtableProxy then '%s-cm-bigtable-proxy-grant' % namespace else 'cm-bigtable-proxy-grant',
  );
  // Temporary table duplication for migration (and potentially mixed mode)
  local centralContentManagerBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=centralNamespace, tableName='content-manager');
  local centralContentManagerBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,  // policy needs to be granted in the local namespace, not central
    app=appName,
    table=centralContentManagerBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='%s-central-cm-bigtable-proxy-grant' % centralNamespace,
  );
  local centralSettingsBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=centralNamespace, tableName='settings');
  local centralSettingsBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,  // policy needs to be granted in the local namespace, not central
    app=appName,
    table=centralSettingsBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='%s-central-settings-bigtable-proxy-grant' % centralNamespace,
  );
  local centralShareBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=centralNamespace, tableName='share');
  local centralShareBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,  // policy needs to be granted in the local namespace, not central
    app=appName,
    table=centralShareBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='%s-central-share-bigtable-proxy-grant' % centralNamespace,
  );
  local centralGithubBigtableTable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=centralNamespace, tableName='github');
  local centralGithubBigtableTableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,
    app=appName,
    table=centralGithubBigtableTable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='github-bigtable-proxy-grant',
  );
  local centralSlackMappingsBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=centralNamespace, tableName='slack-mappings');
  local centralSlackMappingsBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,  // policy needs to be granted in the local namespace, not central
    app=appName,
    table=centralSlackMappingsBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='%s-central-slack-mappings-bigtable-proxy-grant' % centralNamespace,
  );
  local centralRemoteAgentsBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=centralNamespace, tableName='remote-agents');
  local centralRemoteAgentsBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,  // policy needs to be granted in the local namespace, not central
    app=appName,
    table=centralRemoteAgentsBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='%s-central-remote-agents-bigtable-proxy-grant' % centralNamespace,
  );

  local centralWorkingSetBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=centralNamespace, tableName='working-set');
  local centralWorkingSetBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,  // policy needs to be granted in the local namespace, not central
    app=appName,
    table=centralWorkingSetBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='%s-central-working-set-bigtable-proxy-grant' % centralNamespace,
  );

  // Service account and impersonation permission for accessing KMS for CMK support
  local cmkServiceAccountName = cmkLib.getCmkServiceAccountName(env);
  local kmsServiceAccountImpersonationPermission = gcpLib.grantServiceAccountImpersonation(
    app=appName,
    env=env,
    cloud=cloud,
    namespace=namespace,
    impersonatorServiceAccountName=serviceAccount.iamServiceAccountName,
    impersonatorServiceAccountEmail=serviceAccount.serviceAccountGcpEmailAddress,
    targetServiceAccountName=cmkServiceAccountName,
    targetNamespace=namespace,
  );

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    bind_address: '0.0.0.0:50051',
    bigtable_project_id: contentManagerBigtable.projectId,
    bigtable_instance_name: contentManagerBigtable.instanceName,
    central_bigtable_instance_name: centralContentManagerBigtable.instanceName,
    content_manager_table_name: contentManagerBigtable.tableName,
    // Temporary table duplication for migration (and potentially mixed mode)
    central_content_manager_table_name: centralContentManagerBigtable.tableName,
    // Settings table is not in any namespaces - there is only one central table
    central_settings_table_name: centralSettingsBigtable.tableName,
    central_github_table_name: centralGithubBigtableTable.tableName,
    central_share_table_name: centralShareBigtable.tableName,
    central_slack_mappings_table_name: centralSlackMappingsBigtable.tableName,
    central_remote_agents_table_name: centralRemoteAgentsBigtable.tableName,
    central_working_set_table_name: centralWorkingSetBigtable.tableName,
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    metrics_server_bind_address: '0.0.0.0',
    metrics_server_port: 9090,
    server_mtls_config: if mtls then serverCert.config else null,
    central_client_mtls_config: if mtls then centralClientCert.config else null,
    auth_config: {
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
      token_exchange_request_timeout_s: 10.0,
    },
    tenant_watcher_endpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    passthrough: namespace_config.flags.passthroughBigtableProxy,
    health_logger_frequency_s: if env == 'DEV' || env == 'STAGING' then 5 else 30,
    bigtable_timeout_secs: 30.0,
    // Google engineers told us the concurrency limit is 100 per HTTP/2 connection. HTTP/2 servers advertise a concurrency limit to clients.
    // Our HTTP/2 library should handle that. But maybe there is a bug?
    //
    // This sets a concurrency limit in tonic using a layer implemented by the tower library. One advantage is that gRPCs
    // that are queued in tonic can be retried on a new connection.
    //
    // Note: the HTTP/2 hyper library does have an API that will return your request if it wasn't able to
    // send it, but Tonic doesn't use that API as of 0.12.3.
    concurrency_limit: if env != 'PROD' then 95,
    kms_service_account_email: cmkLib.getCmkServiceAccountEmail(env, cloud),
  });
  local serviceAccountObjects = serviceAccount.objects + [kmsServiceAccountImpersonationPermission];

  local container =
    {
      name: appName,
      target: {
        name: '//services/bigtable_proxy/server/main:image',
        dst: appName,
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      args: [
        '--config-file',
        configMap.filename,
      ],
      volumeMounts: [
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
        centralClientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
      ],
      env: lib.flatten([
        telemetryLib.telemetryEnv('%s-svc' % appName, telemetryLib.collectorUri(env, namespace, cloud)),
        dynamicFeatureFlags.env,
        [
          if env == 'DEV' then [
            {
              name: 'GODEBUG',
              value: 'gctrace=1',
            },
          ] else [],
        ],
      ]),
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: '4',
          memory: '16Gi',
        },
      },
    };

  local pod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        configMap.podVolumeDef,
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
        centralClientCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
      ],
    };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
  local monitoringAccess = gcpLib.grantAccess(
    name='%s-monitoring-grant' % appName,
    env=env,
    namespace=namespace,
    appName=appName,
    resourceRef={
      kind: 'Project',
      external: cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/monitoring.metricWriter',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
    abandon=true,
  );
  local minReplicas = namespace_config.flags.bigtableProxyMinReplicas;
  local maxReplicas = namespace_config.flags.bigtableProxyMaxReplicas;
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: minReplicas,
      minReadySeconds: if env == 'DEV' then 0 else 60,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
          priorityClassName: cloudInfo.envToPriorityClass(env),
        },
      },
    },
  };

  // Define a HorizontalPodAutoscaler for CPU-based auto-scaling
  local hpa = {
    apiVersion: 'autoscaling/v2',
    kind: 'HorizontalPodAutoscaler',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      minReplicas: minReplicas,
      maxReplicas: maxReplicas,
      metrics: [
        {
          type: 'Resource',
          resource: {
            name: 'cpu',
            target: {
              type: 'Utilization',
              averageUtilization: 75,  // Scale up if CPU utilization is higher than 75%
            },
          },
        },
      ],
      behavior: {
        scaleDown: {
          stabilizationWindowSeconds: 300,  // 5 minutes
          policies: [
            {
              type: 'Percent',
              value: 25,
              periodSeconds: 60,
            },
          ],
        },
        scaleUp: {
          stabilizationWindowSeconds: 0,
          policies: [
            {
              type: 'Percent',
              value: 100,
              periodSeconds: 60,
            },
            {
              type: 'Pods',
              value: 2,
              periodSeconds: 60,
            },
          ],
          selectPolicy: 'Max',
        },
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccountObjects,
    dynamicFeatureFlags.k8s_objects,
    contentManagerBigtableAccess,
    centralContentManagerBigtableAccess,
    centralSettingsBigtableAccess,
    centralGithubBigtableTableAccess,
    centralShareBigtableAccess,
    centralSlackMappingsBigtableAccess,
    centralRemoteAgentsBigtableAccess,
    centralWorkingSetBigtableAccess,
    serverCert.objects,
    clientCert.objects,
    centralClientCert.objects,
    monitoringAccess,
    services,
    deployment,
    hpa,
    pbd,
  ])

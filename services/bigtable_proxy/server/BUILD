load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_library")

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        "//services/bigtable_proxy/server/main:image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":kubecfg_monitoring",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:cmk-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//deploy/tenants:namespaces",
        "//services/deploy:endpoints",
    ],
)

kubecfg(
    name = "kubecfg_central",
    src = "deploy_central.jsonnet",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:lib",
        "//deploy/gcp:gcp-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_central",
        ":kubecfg_monitoring",
        "//deploy/common:cloud_info",
        "//deploy/tenants:namespaces",
    ],
)

py_library(
    name = "server_test_setup",
    testonly = True,
    srcs = [
        "server_test_setup.py",
    ],
    data = [
        "//services/bigtable_proxy/server/main:server",
    ],
    visibility = ["//services/bigtable_proxy/test:__subpackages__"],
    deps = [
        "//base/test_utils:bigtable_emulator",
        "//base/test_utils:bigtable_setup",
        "//services/bigtable_proxy:bigtable_proxy_py_proto",
        requirement("google-cloud-bigtable"),
    ],
)

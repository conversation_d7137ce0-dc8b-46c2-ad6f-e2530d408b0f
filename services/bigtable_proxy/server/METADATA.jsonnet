// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'bigtable-proxy',
      kubecfg: {
        target: '//services/bigtable_proxy/server:kubecfg',
        task: [
          {
            cloud: tenant.cloud,
            env: tenant.env,
            namespace: tenant.namespace,
          }
          for tenant in tenantNamespaces.namespaces
        ],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['luke', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'bigtable-proxy-tables',
      kubecfg: {
        target: '//services/bigtable_proxy/server:kubecfg_central',
        task: [
          {
            cloud: tenant.cloud,
            env: tenant.env,
            namespace: tenant.namespace,
          }
          for tenant in cloudInfo.centralNamespaces
          if tenant.cloud != 'GCP_US_CENTRAL1_GSC_PROD'
        ],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['luke', 'costa'],
          slack_channel: '#system-services',
        },
      },
      priority: 1,  // Try to run this early since it's a central dependency of other deploys
    },
    {
      name: 'bigtable-proxy-monitoring',
      kubecfg: {
        target: '//services/bigtable_proxy/server:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['luke', 'costa', 'des'],
          slack_channel: '#system-services',
        },
      },
    },

  ],
}

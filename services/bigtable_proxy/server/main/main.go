package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	bigtableproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	"github.com/augmentcode/augment/services/bigtable_proxy/server/main/service"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpc_metrics "github.com/augmentcode/augment/services/lib/grpc/metrics"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
)

var (
	configFile          = flag.String("config-file", "", "Path to config file")
	notServingThreshold = featureflags.NewIntFlag("bigtable_proxy_not_serving_threshold", 3)
)

func setupServer(
	ctx context.Context,
	config *service.Config,
	serverTls credentials.TransportCredentials,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantWatcherClient tenantwatcherclient.TenantWatcherClient,
	featureFlags featureflags.FeatureFlagHandle,
) (*grpc.Server, func(), error) {
	var opts []grpc.ServerOption

	// Setup metrics
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	grpcMetricsInterceptor := grpc_metrics.NewMetricsInterceptor()
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
		grpcMetricsInterceptor.UnaryInterceptor,
		authInterceptor.Intercept,
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
		grpcMetricsInterceptor.StreamInterceptor,
		authInterceptor.StreamIntercept,
	))
	opts = append(opts,
		grpc.MaxRecvMsgSize(service.MAX_DECODE_SIZE),
		grpc.MaxSendMsgSize(service.MAX_DECODE_SIZE),
	)

	grpcServer := grpc.NewServer(opts...)

	// Setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// Setup reflection for debugging
	reflection.Register(grpcServer)

	// Setup health service
	healthServer := health.NewServer()
	healthgrpc.RegisterHealthServer(grpcServer, healthServer)

	auditLogger := audit.NewDefaultAuditLogger()

	// Setup bigtable proxy server
	bigtableProxyService, err := service.NewBigtableProxyServer(ctx, config, featureFlags, tenantWatcherClient, auditLogger)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create bigtable proxy server: %w", err)
	}
	bigtableproto.RegisterBigtableProxyServer(grpcServer, bigtableProxyService)

	if config.HealthLoggerFrequencyS > 0 {
		go func() {
			errors := 0
			for {
				var err error
				start := time.Now()
				{
					ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
					err = bigtableProxyService.CheckAccess(ctx) // Now we can use bigtableProxyService
					cancel()
				}
				if err != nil {
					log.Error().Err(err).Msg("Health check failed")
					errors++
				} else {
					if errors > 0 {
						log.Info().Msg("Health check succeeded, marking service as serving")
						errors = 0
						healthServer.SetServingStatus("", healthgrpc.HealthCheckResponse_SERVING)
					} else {
						log.Info().Msg("Health check succeeded, no change in serving status")
					}
				}

				threshold, err := notServingThreshold.Get(featureFlags)
				if errors >= threshold {
					log.Error().Msg("Health check failed too many times, marking service as not serving")
					healthServer.SetServingStatus("", healthgrpc.HealthCheckResponse_NOT_SERVING)
				}

				sleepDuration := time.Duration(config.HealthLoggerFrequencyS*float64(time.Second)) - time.Since(start)
				if sleepDuration > 0 {
					time.Sleep(sleepDuration)
				}
			}
		}()
	} else {
		log.Info().Msg("Health logging disabled")
	}

	log.Info().Msg("Checking bigtable access")
	retryTime := 0.1
	for {
		err := bigtableProxyService.CheckAccess(ctx)
		if err == nil {
			break
		}
		log.Warn().Err(err).Float64("retry_after_seconds", retryTime).Msg("Failed to access bigtable, retrying")
		time.Sleep(time.Duration(retryTime * float64(time.Second)))
		retryTime = math.Min(retryTime*2.0, 30.0)
		log.Info().Msg("Checking bigtable access")
	}
	log.Info().Msg("Bigtable access ok")

	done := func() {
		log.Info().Msg("Shutting down Bigtable Proxy service")
		if bigtableProxyService != nil {
			if err := bigtableProxyService.Close(); err != nil {
				log.Error().Err(err).Msg("Error closing Bigtable client")
			}
		}
	}

	return grpcServer, done, nil
}

func main() {
	logging.SetupServerLogging()
	log.Info().Msg("Starting new Bigtable proxy")
	ctx := context.Background()

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		log.Fatal().Msg("POD_NAMESPACE environment variable must be set")
	}

	// Parse flags
	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	// Load config
	config := service.ReadConfig(*configFile)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	wg := sync.WaitGroup{}

	// Start metrics server
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.MetricsServerPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup TLS
	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMTLSConfig})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMTLSConfig)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating central client credentials")
	}

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.AuthConfig.TokenExchangeEndpoint, namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(
		config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds),
	)

	var featureFlagHandle featureflags.FeatureFlagHandle
	if config.FeatureFlagsSdkKeyPath != "" {
		var err error
		featureFlagHandle, err = featureflags.NewFeatureFlagHandleFromFile(
			config.FeatureFlagsSdkKeyPath,
			config.DynamicFeatureFlagsEndpoint,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating feature flag handle")
		}
	} else {
		log.Info().Msg("Feature flags disabled: using local implementation")
		featureFlagHandle = featureflags.NewLocalFeatureFlagHandler()
	}

	grpcServer, done, err := setupServer(ctx, config, serverTls, tokenExchangeClient, tenantWatcherClient, featureFlagHandle)
	if err != nil {
		log.Fatal().Err(err).Msg("Error setting up server")
	}

	lis, err := net.Listen("tcp", config.BindAddress)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	// Wait for shutdown signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	grpcServer.GracefulStop()
	done()
	wg.Wait()
}

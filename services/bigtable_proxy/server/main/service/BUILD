load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "service_lib",
    srcs = [
        "config.go",
        "metrics.go",
        "service.go",
    ],
    importpath = "github.com/augmentcode/augment/services/bigtable_proxy/server/main/service",
    visibility = ["//services/bigtable_proxy:__subpackages__"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/logging/audit:audit_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/lib/encryption:encryption_lib_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_prometheus_client_golang//prometheus:go_default_library",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigtable//:bigtable",
        "@com_google_cloud_go_bigtable//apiv2/bigtablepb",
        "@io_opentelemetry_go_otel//:otel",
        "@org_golang_google_api//option",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/wrapperspb",
    ],
)

go_test(
    name = "service_test",
    srcs = ["service_test.go"],
    embed = [":service_lib"],
    deps = [
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "@com_github_googleapis_gax_go_v2//:gax-go",
        "@com_github_stretchr_testify//assert",
        "@com_google_cloud_go_kms//apiv1/kmspb",
    ],
)

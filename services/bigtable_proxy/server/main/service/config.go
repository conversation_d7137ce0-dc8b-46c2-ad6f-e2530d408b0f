package service

import (
	"encoding/json"
	"os"

	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/rs/zerolog/log"
)

// AuthConfig represents authentication configuration settings
type AuthConfig struct {
	TokenExchangeEndpoint        string  `json:"token_exchange_endpoint"`
	TokenExchangeRequestTimeoutS float32 `json:"token_exchange_request_timeout_s"`
}

// Config represents the main configuration structure for the bigtable proxy server
type Config struct {
	// The address to bind the rpc server to, e.g. 0.0.0.0:50051
	BindAddress string `json:"bind_address"`

	BigtableProjectID              string `json:"bigtable_project_id"`
	BigtableInstanceName           string `json:"bigtable_instance_name"`
	CentralBigtableInstanceName    string `json:"central_bigtable_instance_name"`
	CentralContentManagerTableName string `json:"central_content_manager_table_name"`
	CentralSettingsTableName       string `json:"central_settings_table_name"`
	ContentManagerTableName        string `json:"content_manager_table_name"`
	CentralGithubTableName         string `json:"central_github_table_name"`
	CentralShareTableName          string `json:"central_share_table_name"`
	CentralSlackMappingsTableName  string `json:"central_slack_mappings_table_name"`
	CentralRemoteAgentsTableName   string `json:"central_remote_agents_table_name"`
	CentralWorkingSetTableName     string `json:"central_working_set_table_name"`

	FeatureFlagsSdkKeyPath      string `json:"feature_flags_sdk_key_path,omitempty"`
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint,omitempty"`

	// Configure the HTTP server that returns Prometheus metrics
	MetricsServerBindAddress string `json:"metrics_server_bind_address"`
	MetricsServerPort        uint16 `json:"metrics_server_port"`

	// TLS configurations
	ServerMTLSConfig        *tlsconfig.ServerConfig `json:"server_mtls_config,omitempty"`
	CentralClientMTLSConfig *tlsconfig.ClientConfig `json:"central_client_mtls_config,omitempty"`

	AuthConfig AuthConfig `json:"auth_config"`

	TenantWatcherEndpoint string `json:"tenant_watcher_endpoint"`

	Passthrough bool `json:"passthrough"`

	// Whether to use a mock KMS client for testing
	UseMockKMS bool `json:"use_mock_kms,omitempty"`

	// Email of the service account to use for KMS operations
	KMSServiceAccountEmail string `json:"kms_service_account_email,omitempty"`

	// Frequency in seconds to log health information
	HealthLoggerFrequencyS float64 `json:"health_logger_frequency_s"`

	// Timeout duration in seconds for bigtable proxy client
	BigtableTimeoutSecs float32 `json:"bigtable_timeout_secs"`

	// Concurrency limit
	ConcurrencyLimit *int `json:"concurrency_limit,omitempty"`
}

// ReadConfig reads and parses the configuration from the specified file path
func ReadConfig(configFile string) *Config {
	var config Config

	if configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	file, err := os.Open(configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	decoder.DisallowUnknownFields()

	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}

	return &config
}

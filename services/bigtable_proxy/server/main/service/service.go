package service

import (
	"bytes"
	"context"
	"fmt"
	"math/rand"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"cloud.google.com/go/bigtable"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel"
	"google.golang.org/api/option"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	btpb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	bigtableproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	tenantcrypto "github.com/augmentcode/augment/services/lib/encryption"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

type BigtableProxyServer struct {
	bigtableproto.UnimplementedBigtableProxyServer
	config           *Config
	client           *bigtable.Client
	centralClient    *bigtable.Client
	tenantCache      tenantwatcherclient.TenantCache
	tenantCrypto     tenantcrypto.TenantCrypto
	featureFlags     featureflags.FeatureFlagHandle
	ioSequenceNumber *atomic.Int64
	timeout          time.Duration
	auditLogger      *audit.AuditLogger
}

// Increase the default max grpc message decode size to 256MB when
// reading from BigTable. The default is 4MB, which is too small
// for some of the larger lists of blob names that we store in BigTable
// for blob checkpoints.
// see https://code-review.googlesource.com/c/gocloud/+/36171/2/bigtable/bigtable.go
var MAX_DECODE_SIZE = 1024 * 1024 * 256

var (
	ENCRYPTION_FLAG                   = "#encrypted"
	ESCAPED_ENCRYPTION_FLAG_FOR_REGEX string
)

func init() {
	// Initialize the escaped version of the encryption flag
	ESCAPED_ENCRYPTION_FLAG_FOR_REGEX = regexp.QuoteMeta(ENCRYPTION_FLAG)
}

func NewBigtableProxyServer(
	ctx context.Context,
	config *Config,
	featureFlags featureflags.FeatureFlagHandle,
	tenantWatcherClient tenantwatcherclient.TenantWatcherClient,
	auditLogger *audit.AuditLogger,
) (*BigtableProxyServer, error) {
	var clientOpts []option.ClientOption

	timeout := time.Duration(float32(time.Second) * config.BigtableTimeoutSecs)

	// Configure custom message sizes
	clientOpts = append(clientOpts,
		option.WithGRPCDialOption(
			grpc.WithDefaultCallOptions(
				grpc.MaxCallRecvMsgSize(MAX_DECODE_SIZE),
				grpc.MaxCallSendMsgSize(MAX_DECODE_SIZE),
			),
		),
	)

	// Create the client with all options
	client, err := bigtable.NewClient(
		ctx,
		config.BigtableProjectID,
		config.BigtableInstanceName,
		clientOpts...,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create bigtable client: %w", err)
	}
	centralClient, err := bigtable.NewClient(
		ctx,
		config.BigtableProjectID,
		config.CentralBigtableInstanceName,
		clientOpts...,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create central bigtable client: %w", err)
	}

	// Initialize with random sequence number
	seqNum := &atomic.Int64{}
	seqNum.Store(rand.Int63())

	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, "")

	var kmsClient tenantcrypto.KMSClient
	if config.UseMockKMS {
		log.Info().Msg("Using mock KMS client")
		MockKMSUsage.Inc()
		kmsClient = tenantcrypto.NewMockKMSClient()
	} else {
		log.Info().Msg("Using Google KMS client")
		kmsServiceAccountEmail := config.KMSServiceAccountEmail
		if kmsServiceAccountEmail == "" {
			return nil, fmt.Errorf("no KMS service account specified for KMS operations")
		}
		log.Info().Msgf("Using KMS service account %s", kmsServiceAccountEmail)
		kmsClient, err = tenantcrypto.NewGoogleKMSClientWithServiceAccount(ctx, kmsServiceAccountEmail)
		if err != nil {
			return nil, fmt.Errorf("failed to initialize Google KMS client: %w", err)
		}
	}

	tenantCrypto, err := tenantcrypto.New(ctx, kmsClient)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize tenant data encryption: %w", err)
	}
	// Preload tenant keys for encrypted tenants
	tenants, err := tenantCache.GetAllTenants()
	if err != nil {
		return nil, fmt.Errorf("failed get tenants for key preload: %w", err)
	}
	err = tenantCrypto.LoadTenantKeys(ctx, tenants)
	if err != nil {
		log.Warn().Err(err).Msg("Failure preloading tenant keys")
	}

	return &BigtableProxyServer{
		client:        client,
		centralClient: centralClient,
		config:        config,
		// Create cache of tenants in this namespace
		tenantCache:      tenantCache,
		tenantCrypto:     tenantCrypto,
		featureFlags:     featureFlags,
		ioSequenceNumber: seqNum,
		timeout:          timeout,
		auditLogger:      auditLogger,
	}, nil
}

func (s *BigtableProxyServer) Close() error {
	if s.tenantCrypto != nil {
		s.tenantCrypto.Close()
	}
	if s.tenantCache != nil {
		s.tenantCache.Close()
	}
	if s.client != nil {
		return s.client.Close()
	}
	return nil
}

// NOTE: be careful changing this logic. Having passthrough=true when accessing a centralized
// table will leak that table's data to other tenants.
func (s *BigtableProxyServer) shouldUsePassthrough(tableName bigtableproto.TableName) bool {
	switch tableName {
	case bigtableproto.TableName_CONTENT_MANAGER:
		return s.config.Passthrough
	case bigtableproto.TableName_SETTINGS,
		bigtableproto.TableName_GITHUB,
		bigtableproto.TableName_SHARE,
		bigtableproto.TableName_SLACK_MAPPINGS,
		bigtableproto.TableName_REMOTE_AGENTS,
		bigtableproto.TableName_WORKING_SET:
		return false
	default:
		// This shouldn't happen as we validate table names earlier,
		// but being defensive here
		return false
	}
}

// / Internal table names may be prefixed with some other identifiers (see gcp-lib.jsonnet)
func (s *BigtableProxyServer) resolvePassthroughTableName(tableName bigtableproto.TableName) string {
	var resolvedName string
	switch tableName {
	case bigtableproto.TableName_CONTENT_MANAGER:
		resolvedName = s.config.ContentManagerTableName
	default:
		log.Error().Msgf("attempting to access central-only table %v as a passthrough", tableName)
		return ""
	}

	return resolvedName
}

func (s *BigtableProxyServer) resolveCentralTableName(tableName bigtableproto.TableName) string {
	var resolvedName string
	switch tableName {
	case bigtableproto.TableName_CONTENT_MANAGER:
		resolvedName = s.config.CentralContentManagerTableName
	case bigtableproto.TableName_SETTINGS:
		resolvedName = s.config.CentralSettingsTableName
	case bigtableproto.TableName_GITHUB:
		resolvedName = s.config.CentralGithubTableName
	case bigtableproto.TableName_SHARE:
		resolvedName = s.config.CentralShareTableName
	case bigtableproto.TableName_SLACK_MAPPINGS:
		resolvedName = s.config.CentralSlackMappingsTableName
	case bigtableproto.TableName_REMOTE_AGENTS:
		resolvedName = s.config.CentralRemoteAgentsTableName
	case bigtableproto.TableName_WORKING_SET:
		resolvedName = s.config.CentralWorkingSetTableName
	default:
		log.Error().Msgf("attempting to resolve unknown central table name: %v", tableName)
		return ""
	}

	return resolvedName
}

func (s *BigtableProxyServer) checkReadAccess(ctx context.Context, tableName bigtableproto.TableName) error {
	// Create a read request with limit 1, just to check access
	var tablePath string
	var table *bigtable.Table
	if s.shouldUsePassthrough(tableName) {
		tablePath = s.resolvePassthroughTableName(tableName)
		table = s.client.Open(tablePath)
	} else {
		tablePath = s.resolveCentralTableName(tableName)
		table = s.centralClient.Open(tablePath)
	}

	// Try to read one row with timeout
	ctx, cancel := context.WithTimeout(ctx, s.timeout)
	defer cancel()

	rowRange := bigtable.InfiniteRange("")
	var readError error
	err := table.ReadRows(ctx, rowRange, func(row bigtable.Row) bool {
		return false // Stop after first row
	}, bigtable.LimitRows(1)) // Explicitly limit to 1 row
	if err != nil {
		readError = fmt.Errorf("failed to read from table %s: %w", tablePath, err)
		log.Error().Err(err).Str("table", tablePath).Msg("checkReadAccess read failed")
		return readError
	}

	return nil
}

func (s *BigtableProxyServer) CheckAccess(ctx context.Context) error {
	// Check access to ContentManager table
	if err := s.checkReadAccess(ctx, bigtableproto.TableName_CONTENT_MANAGER); err != nil {
		return fmt.Errorf("failed to check ContentManager table access: %w", err)
	}

	return nil
}

func addTenantIDToKey(key []byte, tenantID string, warnOnEmpty bool) []byte {
	if len(key) == 0 {
		if warnOnEmpty {
			log.Warn().Msg("addTenantIDToKey called with empty key, skipping tenant ID prefix...")
		}
		return key
	}

	// Create new slice with capacity for tenant + separator + key
	newKey := make([]byte, len(tenantID)+1+len(key))
	n := copy(newKey, tenantID)
	newKey[n] = '#'
	copy(newKey[n+1:], key)
	return newKey
}

func removeTenantIDFromKey(key []byte, tenantID string) []byte {
	// Empty key is an expected case in the bigtable API (cell continued from a previous chunk):
	// https://github.com/googleapis/python-bigtable/blob/747884e936b7b6e8fc8ec14292e826cba736c50a/google/cloud/bigtable_v2/types/bigtable.py#L220
	if len(key) == 0 {
		return key
	}

	expectedPrefix := tenantID + "#"
	if !bytes.HasPrefix(key, []byte(expectedPrefix)) {
		log.Warn().Msgf("removeTenantIDFromKey called with key that doesn't start with tenant ID: %s", string(key))
		return key
	}

	// Return the key after the tenant ID prefix
	return key[len(expectedPrefix):]
}

func filterDependsOnValue(filter *btpb.RowFilter) bool {
	// Sense if the filter depends on cell values.
	// If data is encrypted, the encrypted data will need to be re-processed locally to determine if the data passes the filter.

	// If no filter, that's valid
	if filter == nil {
		return false
	}

	switch {
	case filter.GetValueRegexFilter() != nil:
		return true
	case filter.GetCondition() != nil:
		condition := filter.GetCondition()
		if filterDependsOnValue(condition.GetPredicateFilter()) {
			return true
		}
		if filterDependsOnValue(condition.GetTrueFilter()) {
			return true
		}
		if filterDependsOnValue(condition.GetFalseFilter()) {
			return true
		}
		return false
	case filter.GetChain() != nil:
		chain := filter.GetChain()
		for _, subFilter := range chain.GetFilters() {
			if filterDependsOnValue(subFilter) {
				return true
			}
		}
		return false
	default:
		// Non-supported filters will be sensed in convertProtoFilter()
		return false
	}
}

func convertProtoFilter(filter *btpb.RowFilter) (bigtable.Filter, error) {
	// Convert the protobuf RowFilter to a bigtable Filter.
	// This doesn't actually make any changes to the meaning of filters today, but if we introduce a filter mode that could
	// be affected by transforming row keys, we have to make sure to handle it here.

	// If no filter, that's valid
	if filter == nil {
		return nil, nil
	}

	// Handle empty filter struct (all fields zero)
	if reflect.ValueOf(filter).Elem().IsZero() {
		return nil, nil
	}

	switch {
	case filter.GetCellsPerColumnLimitFilter() != 0:
		return bigtable.LatestNFilter(int(filter.GetCellsPerColumnLimitFilter())), nil
	case filter.GetCellsPerRowLimitFilter() != 0:
		return bigtable.CellsPerRowLimitFilter(int(filter.GetCellsPerRowLimitFilter())), nil
	case filter.GetStripValueTransformer():
		return bigtable.StripValueFilter(), nil
	case filter.GetFamilyNameRegexFilter() != "":
		return bigtable.FamilyFilter(filter.GetFamilyNameRegexFilter()), nil
	case filter.GetPassAllFilter():
		return bigtable.PassAllFilter(), nil
	case filter.GetBlockAllFilter():
		return bigtable.BlockAllFilter(), nil
	case filter.GetTimestampRangeFilter() != nil:
		tr := filter.GetTimestampRangeFilter()
		startTime := time.Unix(0, tr.GetStartTimestampMicros()*1000) // convert micros to nanos
		endTime := time.Unix(0, tr.GetEndTimestampMicros()*1000)     // convert micros to nanos
		return bigtable.TimestampRangeFilter(startTime, endTime), nil
	case filter.GetColumnQualifierRegexFilter() != nil:
		// modify qualifier regex for encryption flag
		originalPattern := string(filter.GetColumnQualifierRegexFilter())
		modifiedPattern := addEncryptedFlagToQualifierRegex(originalPattern)
		return bigtable.ColumnFilter(modifiedPattern), nil
	case filter.GetValueRegexFilter() != nil:
		// Any encrypted data must be fetched and filter evaluated locally.
		return bigtable.ConditionFilter(
			bigtable.ColumnFilter(".*"+ESCAPED_ENCRYPTION_FLAG_FOR_REGEX+"$"),
			bigtable.PassAllFilter(),
			bigtable.ValueFilter(string(filter.GetValueRegexFilter())),
		), nil

	case filter.GetCondition() != nil:
		condition := filter.GetCondition()
		predFilter, err := convertProtoFilter(condition.GetPredicateFilter())
		if err != nil {
			return nil, err
		}
		trueFilter, err := convertProtoFilter(condition.GetTrueFilter())
		if err != nil {
			return nil, err
		}
		falseFilter, err := convertProtoFilter(condition.GetFalseFilter())
		if err != nil {
			return nil, err
		}
		return bigtable.ConditionFilter(predFilter, trueFilter, falseFilter), nil

	case filter.GetChain() != nil:
		chain := filter.GetChain()
		filters := make([]bigtable.Filter, 0, len(chain.GetFilters()))
		for _, subFilter := range chain.GetFilters() {
			f, err := convertProtoFilter(subFilter)
			if err != nil {
				return nil, err
			}
			filters = append(filters, f)
		}
		return bigtable.ChainFilters(filters...), nil

	default:
		log.Error().
			Interface("filter", filter).
			Interface("filter_type", fmt.Sprintf("%T", filter)).
			Msg("unsupported filter type")
		return nil, status.Error(codes.Internal, "unsupported filter")
	}
}

func evaluateProtoFilterLocally(filter *btpb.RowFilter, chunk *btpb.ReadRowsResponse_CellChunk) (bool, error) {
	// Evaluate the filter locally on the chunk data.
	// This is necessary when the filter depends on cell values, since the encrypted data will need to be decrypted
	// before the filter can be evaluated.

	switch {
	case filter.GetPassAllFilter():
		return true, nil

	case filter.GetBlockAllFilter():
		return false, nil

	// These are transformers, so rely on bigtble to have applied them appropriately.
	case filter.GetCellsPerColumnLimitFilter() != 0:
		return true, nil
	case filter.GetCellsPerRowLimitFilter() != 0:
		return true, nil
	case filter.GetStripValueTransformer():
		return true, nil

	case filter.GetFamilyNameRegexFilter() != "":
		re, err := regexp.Compile(filter.GetFamilyNameRegexFilter())
		if err != nil {
			return false, err
		}
		familyName := chunk.FamilyName.GetValue()
		match := re.MatchString(familyName)
		return match, nil

	case filter.GetColumnQualifierRegexFilter() != nil:
		// Evaluate column qualifier regex
		re, err := regexp.Compile(string(filter.GetColumnQualifierRegexFilter()))
		if err != nil {
			return false, err
		}
		qualifier := string(chunk.Qualifier.GetValue())
		match := re.Match([]byte(qualifier))
		return match, nil

	case filter.GetTimestampRangeFilter() != nil:
		// Evaluate timestamp range filter
		tr := filter.GetTimestampRangeFilter()
		startMicros := tr.GetStartTimestampMicros()
		endMicros := tr.GetEndTimestampMicros()

		// Convert chunk timestamp (nanos) to micros for comparison
		chunkTimestampMicros := chunk.GetTimestampMicros()

		// Check if timestamp is within range
		// Start is inclusive, end is exclusive
		inRange := chunkTimestampMicros >= startMicros &&
			(endMicros == 0 || chunkTimestampMicros < endMicros)
		return inRange, nil

	case filter.GetValueRegexFilter() != nil:
		// This filter must be evaluated locally.
		re, err := regexp.Compile(string(filter.GetValueRegexFilter()))
		if err != nil {
			return false, err
		}
		return re.Match(chunk.Value), nil

	case filter.GetCondition() != nil:
		condition := filter.GetCondition()
		predPass, err := evaluateProtoFilterLocally(condition.GetPredicateFilter(), chunk)
		if err != nil {
			return false, err
		}
		if predPass {
			return evaluateProtoFilterLocally(condition.GetTrueFilter(), chunk)
		}
		return evaluateProtoFilterLocally(condition.GetFalseFilter(), chunk)

	case filter.GetChain() != nil:
		chain := filter.GetChain()
		for _, subFilter := range chain.GetFilters() {
			f, err := evaluateProtoFilterLocally(subFilter, chunk)
			if err != nil {
				return false, err
			}
			if !f {
				return false, nil
			}
		}
		return true, nil

	default:
		log.Error().Interface("filter", filter).Msg("unsupported filter")
		return false, status.Error(codes.Internal, "unsupported filter")
	}
}

func convertProtoRowSet(rows *btpb.RowSet, tenantID string, passthrough bool) (bigtable.RowSet, error) {
	var ranges []bigtable.RowRange

	if rows == nil || (len(rows.GetRowKeys()) == 0 && len(rows.GetRowRanges()) == 0) {
		if passthrough {
			// Passthrough mode reads all rows
			return bigtable.InfiniteRange(""), nil
		} else {
			// sharded mode reads only this tenant's rows
			return bigtable.PrefixRange(tenantID + "#"), nil
		}
	}

	// Handle row keys by converting each to a single-key range
	for _, key := range rows.GetRowKeys() {
		rowKey := string(key)
		if !passthrough {
			rowKey = string(addTenantIDToKey(key, tenantID, true))
		}
		// For a single key, use NewClosedRange with same start and end
		ranges = append(ranges, bigtable.NewClosedRange(rowKey, rowKey))
	}

	// Handle row ranges
	for _, r := range rows.GetRowRanges() {
		var start, end string
		var startBound, endBound bool // true for closed, false for open

		// Helper to handle key modification
		modifyKey := func(key []byte) (string, error) {
			if passthrough {
				return string(key), nil
			} else {
				if len(key) == 0 {
					return "", status.Error(codes.InvalidArgument, "empty key not allowed in row range")
				}
				return string(addTenantIDToKey(key, tenantID, true)), nil
			}
		}

		var err error
		switch sk := r.StartKey.(type) {
		case *btpb.RowRange_StartKeyClosed:
			start, err = modifyKey(sk.StartKeyClosed)
			startBound = true
		case *btpb.RowRange_StartKeyOpen:
			start, err = modifyKey(sk.StartKeyOpen)
			startBound = false
		case nil:
			if !passthrough {
				key := []byte{0x00}
				start = string(addTenantIDToKey(key, tenantID, false))
				startBound = true
			}

		}

		if err != nil {
			return nil, err
		}

		switch ek := r.EndKey.(type) {
		case *btpb.RowRange_EndKeyClosed:
			end, err = modifyKey(ek.EndKeyClosed)
			endBound = true
		case *btpb.RowRange_EndKeyOpen:
			end, err = modifyKey(ek.EndKeyOpen)
			endBound = false
		case nil:
			if !passthrough {
				key := []byte{0xff}
				end = string(addTenantIDToKey(key, tenantID, false))
				endBound = false
			}
		}

		if err != nil {
			return nil, err
		}

		var rowRange bigtable.RowRange
		switch {
		case startBound && endBound:
			rowRange = bigtable.NewClosedRange(start, end)
		case startBound && !endBound:
			rowRange = bigtable.NewClosedOpenRange(start, end)
		case !startBound && endBound:
			rowRange = bigtable.NewOpenClosedRange(start, end)
		default: // !startBound && !endBound
			rowRange = bigtable.NewOpenRange(start, end)
		}
		ranges = append(ranges, rowRange)
	}

	// If we have ranges, return them as a RowRangeList
	if len(ranges) > 0 {
		return bigtable.RowRangeList(ranges), nil
	}

	// Should never reach here due to earlier validation
	return nil, status.Error(codes.Internal, "unexpected empty row set")
}

func addEncryptedFlagToQualifier(qualifier string) string {
	return qualifier + ENCRYPTION_FLAG
}

func removeEncryptedFlagFromQualifier(qualifier string) string {
	return strings.TrimSuffix(qualifier, ENCRYPTION_FLAG)
}

func addEncryptedFlagToQualifierRegex(originalPattern string) string {
	// Modify patterns specifying end-of-string to optionally have encryption flag at the end.
	// Note: This isn't resilient to really complex regex patterns.
	if strings.HasSuffix(originalPattern, "$") && !strings.HasSuffix(originalPattern, "\\$") {
		patternWithoutEndMarker := strings.TrimSuffix(originalPattern, "$")
		return fmt.Sprintf("(%s(%s)?$)", patternWithoutEndMarker, ESCAPED_ENCRYPTION_FLAG_FOR_REGEX)
	}
	return fmt.Sprintf("(%s(%s)?)", originalPattern, ESCAPED_ENCRYPTION_FLAG_FOR_REGEX)
}

func valueIsEncrypted(qualifier string) bool {
	return strings.HasSuffix(qualifier, ENCRYPTION_FLAG)
}

func (s *BigtableProxyServer) convertProtoMutations(
	ctx context.Context,
	tableName string,
	protoMutations []*btpb.Mutation,
	tenantDetails *tenantproto.Tenant,
) (*bigtable.Mutation, error) {
	if protoMutations == nil || len(protoMutations) == 0 {
		log.Info().Msg("no mutations to convert")
		return nil, nil
	}

	mutation := bigtable.NewMutation()

	for _, m := range protoMutations {
		switch m := m.Mutation.(type) {
		case *btpb.Mutation_SetCell_:
			// Skip encryption for empty values
			encrypt := tenantcrypto.HasSealKey(tenantDetails) && len(m.SetCell.Value) > 0
			var encryptionStatus string
			if len(m.SetCell.Value) == 0 {
				encryptionStatus = "empty"
			} else {
				encryptionStatus = strconv.FormatBool(encrypt)
			}
			ChunkSizeBytes.WithLabelValues(tenantDetails.Name, tableName, "write", encryptionStatus).Observe(float64(len(m.SetCell.Value)))
			if encrypt {
				encryptedQualifier := addEncryptedFlagToQualifier(string(m.SetCell.ColumnQualifier))
				startTime := time.Now()
				encryptedValue, err := s.tenantCrypto.Seal(ctx, tenantDetails, m.SetCell.Value)
				CryptoLatency.WithLabelValues(
					tenantDetails.Name,
					tableName,
					"seal",
					tenantcrypto.GetStatusString(err),
				).Observe(time.Since(startTime).Seconds())

				if err != nil {
					return nil, err
				}
				mutation.Set(m.SetCell.FamilyName,
					encryptedQualifier,
					bigtable.Timestamp(m.SetCell.TimestampMicros),
					encryptedValue)
			} else {
				mutation.Set(m.SetCell.FamilyName,
					string(m.SetCell.ColumnQualifier),
					bigtable.Timestamp(m.SetCell.TimestampMicros),
					m.SetCell.Value)
			}
		case *btpb.Mutation_DeleteFromColumn_:
			mutation.DeleteCellsInColumn(m.DeleteFromColumn.FamilyName,
				string(m.DeleteFromColumn.ColumnQualifier))
			// Also delete equivalent encrypted data column family.
			mutation.DeleteCellsInColumn(m.DeleteFromColumn.FamilyName,
				addEncryptedFlagToQualifier(string(m.DeleteFromColumn.ColumnQualifier)))
		case *btpb.Mutation_DeleteFromFamily_:
			mutation.DeleteCellsInFamily(m.DeleteFromFamily.FamilyName)
		case *btpb.Mutation_DeleteFromRow_:
			mutation.DeleteRow()
		default:
			return nil, status.Errorf(codes.Internal, "unsupported mutation type: %T", m)
		}
	}

	return mutation, nil
}

// ChunkFilterFunc is a function that determines whether a chunk should be included in the results
type ChunkFilterFunc func(chunk *btpb.ReadRowsResponse_CellChunk) bool

func (s *BigtableProxyServer) convertRowToChunks(
	ctx context.Context,
	tableName string,
	row bigtable.Row,
	tenantDetails *tenantproto.Tenant,
	passthrough bool,
	filterFunc ChunkFilterFunc,
) ([]*btpb.ReadRowsResponse_CellChunk, int) {
	chunks := make([]*btpb.ReadRowsResponse_CellChunk, 0)
	decryptErrorCount := 0
	rowKey := []byte(row.Key())
	if !passthrough {
		rowKey = removeTenantIDFromKey(rowKey, tenantDetails.Id)
	}

	for family, cols := range row {
		for _, col := range cols {

			familyName := &wrapperspb.StringValue{Value: family}
			qualifier := strings.TrimPrefix(col.Column, family+":")
			value := col.Value
			isEncrypted := valueIsEncrypted(qualifier)
			var encryptionStatus string
			if len(value) == 0 {
				encryptionStatus = "empty"
			} else {
				encryptionStatus = strconv.FormatBool(isEncrypted)
			}
			ChunkSizeBytes.WithLabelValues(tenantDetails.Name, tableName, "read", encryptionStatus).Observe(float64(len(value)))
			if isEncrypted {
				// Remove the encrypted flag from the qualifier and decrypt the data
				qualifier = removeEncryptedFlagFromQualifier(string(qualifier))
				if len(value) > 0 { // Strip transformer may remove the value
					startTime := time.Now()
					decrypted, err := s.tenantCrypto.Open(ctx, tenantDetails, value)
					duration := time.Since(startTime).Seconds()
					CryptoLatency.WithLabelValues(
						tenantDetails.Name,
						tableName,
						"open",
						tenantcrypto.GetStatusString(err),
					).Observe(duration)

					if err != nil {
						log.Warn().Err(err).Msg("failed to decrypt value")
						decryptErrorCount++
						continue
					}
					value = decrypted
				}
			}
			chunk := &btpb.ReadRowsResponse_CellChunk{
				RowKey:          rowKey,
				FamilyName:      familyName,
				Qualifier:       &wrapperspb.BytesValue{Value: []byte(qualifier)},
				TimestampMicros: int64(col.Timestamp),
				Value:           value,
				Labels:          col.Labels,
			}

			// Drop encrypted chunks that did not pass the filter.
			// Cleartext chunks have already passed the filter at bigtable.
			if isEncrypted && !filterFunc(chunk) {
				continue
			}

			chunks = append(chunks, chunk)
		}
	}

	// Mark the last chunk as committed
	if len(chunks) > 0 {
		chunks[len(chunks)-1].RowStatus = &btpb.ReadRowsResponse_CellChunk_CommitRow{
			CommitRow: true,
		}
	}

	return chunks, decryptErrorCount
}

func validateScopeForTableName(claimsInfo *auth.AugmentClaims, tableName bigtableproto.TableName, op BigTableOp) error {
	var requiredScope tokenexchangeproto.Scope

	if op == DELETE {
		if claimsInfo.HasScope(tokenexchangeproto.Scope_BIGTABLE_DELETE) {
			return nil
		} else {
			return status.Error(codes.PermissionDenied, "Access denied")
		}
	}

	switch tableName {
	// Content-scoped tables
	case bigtableproto.TableName_CONTENT_MANAGER,
		bigtableproto.TableName_GITHUB,
		bigtableproto.TableName_SHARE,
		bigtableproto.TableName_REMOTE_AGENTS,
		bigtableproto.TableName_WORKING_SET:
		if op == WRITE {
			requiredScope = tokenexchangeproto.Scope_CONTENT_RW
		} else {
			requiredScope = tokenexchangeproto.Scope_CONTENT_R
		}

	// Settings-scoped tables
	case bigtableproto.TableName_SETTINGS,
		bigtableproto.TableName_SLACK_MAPPINGS:
		if op == WRITE {
			requiredScope = tokenexchangeproto.Scope_SETTINGS_RW
		} else {
			requiredScope = tokenexchangeproto.Scope_SETTINGS_R
		}

	default:
		return status.Errorf(codes.InvalidArgument, "unknown table: %v", tableName)
	}

	if !claimsInfo.HasScope(requiredScope) {
		return status.Error(codes.PermissionDenied, "Access denied")
	}
	return nil
}

// logResponse logs RPC responses as either debug or error depending on the status
func logResponse(err error, callsite string, ioSequenceNumber string) error {
	if err != nil {
		log.Error().Msgf("[%s] response at %s: err %v", ioSequenceNumber, callsite, err)
	} else {
		log.Debug().Msgf("[%s] response at %s: success", ioSequenceNumber, callsite)
	}
	return err
}

func getTenantID(claims *auth.AugmentClaims, reqTenantID string) (string, error) {
	switch {
	case claims.TenantID == "" && reqTenantID == "":
		return "", status.Error(codes.Internal, "tenant_id must be set in either token or request")
	case claims.TenantID == "":
		return reqTenantID, nil
	case reqTenantID == "":
		return claims.TenantID, nil
	case claims.TenantID != reqTenantID:
		log.Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", claims.TenantID, reqTenantID)
		return "", status.Error(codes.PermissionDenied, "different tenant ID in token and request")
	default:
		return claims.TenantID, nil
	}
}

type BigTableOp int

const (
	READ BigTableOp = iota
	WRITE
	DELETE
)

func (s *BigtableProxyServer) validateAndGetTenantDetails(ctx context.Context, reqTenantID string, reqTableName bigtableproto.TableName, op BigTableOp) (*tenantproto.Tenant, error) {
	claims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Internal, "failed to get auth claims from context")
	}

	// Validate and get tenant ID
	tenantID, err := getTenantID(claims, reqTenantID)
	if err != nil {
		return nil, err
	}

	// Get tenantDetails info from cache. Only tenants in the service's namespace should be in the cache.
	tenantDetails, err := s.tenantCache.GetTenant(tenantID)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get tenant id %s info from cache", tenantID)
		return nil, status.Errorf(codes.Internal, "failed to validate tenant")
	}

	// For namespace-only claims, verify namespace access permission
	if claims.TenantID == "" {
		if claims.ShardNamespace == "" {
			return nil, status.Error(codes.PermissionDenied, "claim not specific to tenant or namespace")
		}

		if claims.ShardNamespace != tenantDetails.ShardNamespace &&
			claims.ShardNamespace != tenantDetails.OtherNamespace {
			log.Error().Msgf("Auth claims give permission for namespace %s, but tenant %s is in namespace %s",
				claims.ShardNamespace, tenantID, tenantDetails.ShardNamespace)
			return nil, status.Error(codes.PermissionDenied, "tenant not in authorized namespace")
		}
	}

	// Validate claims scope
	if err := validateScopeForTableName(claims, reqTableName, op); err != nil {
		return nil, err
	}

	return tenantDetails, nil
}

// wrapWithLogging wraps an RPC handler with logging and cancellation handling
func (s *BigtableProxyServer) wrapWithLogging(
	ctx context.Context,
	callsite string,
	ioSequenceNumber string,
	f func() error,
) error {
	// Handle context cancellation
	err := f()
	if ctx.Err() == context.Canceled {
		log.Error().Msgf("[%s] cancelled on request of client", ioSequenceNumber)
		return status.Error(codes.Canceled, "client cancelled request")
	}
	return logResponse(err, callsite, ioSequenceNumber)
}

func (s *BigtableProxyServer) ReadRows(
	req *bigtableproto.ReadRowsRequest,
	stream bigtableproto.BigtableProxy_ReadRowsServer,
) error {
	ctx := stream.Context()

	readID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLogging(ctx, "read_rows", readID, func() error {
		tracer := otel.GetTracerProvider().Tracer("")
		ctx, span := tracer.Start(ctx, "read_rows")
		defer span.End()

		tenantDetails, err := s.validateAndGetTenantDetails(ctx, req.GetTenantId(), req.GetTableName(), READ)
		if err != nil {
			return err
		}

		passthrough := s.shouldUsePassthrough(req.GetTableName())

		var tableName string
		if passthrough {
			tableName = s.resolvePassthroughTableName(req.GetTableName())
		} else {
			tableName = s.resolveCentralTableName(req.GetTableName())
		}

		keyCount := len(req.GetRows().GetRowKeys())
		rangeCount := len(req.GetRows().GetRowRanges())
		log.Info().Msgf("[%s] reading %d keys and %d ranges in %s", readID, keyCount, rangeCount, tableName)

		rowSet, err := convertProtoRowSet(req.GetRows(), tenantDetails.Id, passthrough)
		if err != nil {
			return err
		}

		filter, err := convertProtoFilter(req.GetFilter())
		if err != nil {
			return err
		}

		// If filter depends on values, must process filter after data fetch based on other filters.
		var chunkFilter ChunkFilterFunc
		if filterDependsOnValue(req.GetFilter()) {
			// Create a filter function that applies the filter locally
			chunkFilter = func(chunk *btpb.ReadRowsResponse_CellChunk) bool {
				pass, err := evaluateProtoFilterLocally(req.GetFilter(), chunk)
				if err != nil {
					log.Warn().Err(err).Msg("failed to evaluate filter locally")
					return false
				}
				return pass
			}
		} else {
			// No local filtering needed, accept all chunks
			chunkFilter = func(chunk *btpb.ReadRowsResponse_CellChunk) bool {
				return true
			}
		}

		ReadRequestRowHistogram.
			WithLabelValues(tenantDetails.Name, req.GetTableName().String()).
			Observe(float64(keyCount))

		claims, ok := auth.GetAugmentClaims(ctx)
		if !ok {
			return status.Error(codes.Internal, "failed to get auth claims from context")
		}
		iapEmail, ok := claims.GetIapEmail()
		if ok {
			s.auditLogger.WriteAuditLog(
				iapEmail,
				claims.OpaqueUserIDType,
				claims.TenantName,
				fmt.Sprintf("Read rows %v from %s in tenant %s", req.Rows, tableName, claims.TenantName),
			)
		}

		// Start streaming in a goroutine
		errChan := make(chan error, 1)
		go func() {
			defer close(errChan)

			// Protect against bigtable stalling
			timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
			defer cancel()

			var table *bigtable.Table
			if passthrough {
				table = s.client.Open(tableName)
			} else {
				table = s.centralClient.Open(tableName)
			}

			var bigtableLatency time.Duration
			var sendLatency time.Duration
			var syncLatency time.Duration
			streamingResponseCount := 0
			chunkCount := 0
			totalLen := 0
			var streamErr error

			opts := []bigtable.ReadOption{}
			if req.GetRowsLimit() > 0 {
				opts = append(opts, bigtable.LimitRows(int64(req.GetRowsLimit())))
			}
			if filter != nil {
				opts = append(opts, bigtable.RowFilter(filter))
			}

			prevTime := time.Now()
			err := table.ReadRows(timeoutCtx, rowSet, func(row bigtable.Row) bool {
				bigtableLatency += time.Since(prevTime)
				prevTime = time.Now()

				chunks, decryptErrorCount := s.convertRowToChunks(ctx, tableName, row, tenantDetails, passthrough, chunkFilter)
				if decryptErrorCount > 0 {
					log.Warn().Msgf("[%s] failed to decrypt %d chunks in row %s", readID, decryptErrorCount, string(row.Key()))
					ReadDecryptionErrors.WithLabelValues(tenantDetails.Name, tableName).Add(float64(decryptErrorCount))
				}

				syncLatency += time.Since(prevTime)
				prevTime = time.Now()

				if len(chunks) == 0 {
					return true // Continue to next row
				}

				resp := &bigtableproto.ReadRowsResponse{
					Chunks: chunks,
				}

				totalLen += len(chunks)
				chunkCount += len(chunks)
				streamingResponseCount++

				if err := stream.Send(resp); err != nil {
					streamErr = err
					return false // Stop iteration
				}

				sendLatency += time.Since(prevTime)
				prevTime = time.Now()

				return true // Continue iteration
			}, opts...)

			log.Info().Msgf("[%s] %s read finished; closing stream", tableName, readID)
			log.Info().Msgf("[%s] timings: bigtable=%.3f send=%.3f sync=%.3f streaming_response_count=%d chunk_count=%d len=%d",
				readID,
				bigtableLatency.Seconds(),
				sendLatency.Seconds(),
				syncLatency.Seconds(),
				streamingResponseCount,
				chunkCount,
				totalLen,
			)
			ReadRequestLatencyCollector.
				WithLabelValues(tenantDetails.Name, tableName, tenantDetails.Id).
				Observe(bigtableLatency.Seconds())
			if keyCount > 0 && rangeCount == 0 {
				ReadRequestLatencyPerRowCollector.
					WithLabelValues(tenantDetails.Name, tableName, tenantDetails.Id).
					Observe(bigtableLatency.Seconds() / float64(keyCount))
			}

			ReadResponseChunksHistogram.
				WithLabelValues(tenantDetails.Name, tableName).
				Observe(float64(chunkCount))

			var finalErr error
			if streamErr != nil {
				finalErr = status.Errorf(codes.Internal, "failed to stream response: %v", streamErr)
			} else {
				finalErr = err
			}
			errChan <- finalErr
		}()

		// Wait for completion
		return <-errChan
	})
}

func (s *BigtableProxyServer) MutateRows(
	req *bigtableproto.MutateRowsRequest,
	stream bigtableproto.BigtableProxy_MutateRowsServer,
) error {
	ctx := stream.Context()
	mutateID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLogging(ctx, "mutate_rows", mutateID, func() error {
		tracer := otel.GetTracerProvider().Tracer("")
		ctx, span := tracer.Start(ctx, "mutate_rows")
		defer span.End()

		tenantDetails, err := s.validateAndGetTenantDetails(ctx, req.GetTenantId(), req.GetTableName(), WRITE)
		if err != nil {
			return err
		}

		passthrough := s.shouldUsePassthrough(req.GetTableName())

		var tableName string
		if passthrough {
			tableName = s.resolvePassthroughTableName(req.GetTableName())
		} else {
			tableName = s.resolveCentralTableName(req.GetTableName())
		}

		if !passthrough {
			// Modify row keys with tenant ID for each entry
			for _, entry := range req.GetEntries() {
				entry.RowKey = addTenantIDToKey(entry.RowKey, tenantDetails.Id, true)
			}
		}

		log.Info().Msgf("[%s] mutate %d entries in %s", mutateID, len(req.GetEntries()), tableName)

		MutateRequestEntriesHistogram.
			WithLabelValues(tenantDetails.Name, req.GetTableName().String()).
			Observe(float64(len(req.GetEntries())))

		// Start streaming in a goroutine
		errChan := make(chan error, 1)
		go func() {
			defer close(errChan)

			var table *bigtable.Table
			if passthrough {
				table = s.client.Open(tableName)
			} else {
				table = s.centralClient.Open(tableName)
			}
			var streamErr error

			// Convert entries to bigtable.Mutation
			rowKeys := make([]string, len(req.GetEntries()))
			muts := make([]*bigtable.Mutation, len(req.GetEntries()))

			for i, entry := range req.GetEntries() {
				rowKeys[i] = string(entry.RowKey)
				converted, err := s.convertProtoMutations(ctx, tableName, entry.Mutations, tenantDetails)
				if err != nil {
					streamErr = err
					return
				}
				muts[i] = converted
			}

			claims, ok := auth.GetAugmentClaims(ctx)
			if !ok {
				streamErr = status.Error(codes.Internal, "failed to get auth claims from context")
				return
			}
			iapEmail, ok := claims.GetIapEmail()
			if ok {
				s.auditLogger.WriteAuditLog(
					iapEmail,
					claims.OpaqueUserIDType,
					claims.TenantName,
					fmt.Sprintf("Mutate rows %v from %s in tenant %s", rowKeys, tableName, claims.TenantName),
				)
			}

			// Protect against bigtable stalling
			timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
			defer cancel()

			errs, err := table.ApplyBulk(timeoutCtx, rowKeys, muts)
			if err != nil {
				streamErr = err
			}

			// Convert errors to response
			resp := &bigtableproto.MutateRowsResponse{
				Entries: make([]*btpb.MutateRowsResponse_Entry, len(rowKeys)),
			}

			for i := range rowKeys {
				entry := &btpb.MutateRowsResponse_Entry{
					Index: int64(i),
				}
				if i < len(errs) && errs[i] != nil {
					entry.Status = status.Convert(errs[i]).Proto()
				} else {
					entry.Status = status.New(codes.OK, "").Proto()
				}
				resp.Entries[i] = entry
			}

			if err := stream.Send(resp); err != nil {
				streamErr = status.Errorf(codes.Internal, "failed to stream response: %v", err)
			}

			log.Info().Msgf("[%s] mutate finished successfully, closing stream", mutateID)
			errChan <- streamErr
		}()

		// Wait for completion
		return <-errChan
	})
}

func (s *BigtableProxyServer) DeleteRows(
	req *bigtableproto.DeleteRowsRequest,
	stream bigtableproto.BigtableProxy_DeleteRowsServer,
) error {
	ctx := stream.Context()
	mutateID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLogging(ctx, "delete_rows", mutateID, func() error {
		tracer := otel.GetTracerProvider().Tracer("")
		ctx, span := tracer.Start(ctx, "delete_rows")
		defer span.End()

		tenantDetails, err := s.validateAndGetTenantDetails(ctx, req.GetTenantId(), req.GetTableName(), DELETE)
		if err != nil {
			return err
		}

		passthrough := s.shouldUsePassthrough(req.GetTableName())

		var tableName string
		if passthrough {
			tableName = s.resolvePassthroughTableName(req.GetTableName())
		} else {
			tableName = s.resolveCentralTableName(req.GetTableName())
		}

		rowKeys := make([]string, len(req.GetRowKeys()))
		if !passthrough {
			// Modify row keys with tenant ID for each entry
			for i, entry := range req.GetRowKeys() {
				rowKeys[i] = string(addTenantIDToKey(entry, tenantDetails.Id, true))
			}
		} else {
			for i, entry := range req.GetRowKeys() {
				rowKeys[i] = string(entry)
			}
		}

		log.Info().Msgf("[%s] delete %d entries in %s", mutateID, len(rowKeys), tableName)

		MutateRequestEntriesHistogram.
			WithLabelValues(tenantDetails.Name, req.GetTableName().String()).
			Observe(float64(len(rowKeys)))

		claims, ok := auth.GetAugmentClaims(ctx)
		if !ok {
			return status.Error(codes.Internal, "failed to get auth claims from context")
		}
		iapEmail, ok := claims.GetIapEmail()
		if ok {
			s.auditLogger.WriteAuditLog(
				iapEmail,
				claims.OpaqueUserIDType,
				claims.TenantName,
				fmt.Sprintf("Remove rows %v from %s in tenant %s", rowKeys, tableName, claims.TenantName),
			)
		}

		// Start streaming in a goroutine
		errChan := make(chan error, 1)
		go func() {
			defer close(errChan)

			var table *bigtable.Table
			if passthrough {
				table = s.client.Open(tableName)
			} else {
				table = s.centralClient.Open(tableName)
			}
			var streamErr error

			// Convert entries to bigtable.Mutation
			muts := make([]*bigtable.Mutation, len(rowKeys))

			for i := range rowKeys {
				muts[i] = bigtable.NewMutation()
				muts[i].DeleteRow()
			}

			// Protect against bigtable stalling
			timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
			defer cancel()

			errs, err := table.ApplyBulk(timeoutCtx, rowKeys, muts)
			if err != nil {
				streamErr = err
			}

			// Convert errors to response
			resp := &bigtableproto.DeleteRowsResponse{
				Entries: make([]*btpb.MutateRowsResponse_Entry, len(rowKeys)),
			}

			for i := range rowKeys {
				entry := &btpb.MutateRowsResponse_Entry{
					Index: int64(i),
				}
				if i < len(errs) && errs[i] != nil {
					entry.Status = status.Convert(errs[i]).Proto()
				} else {
					entry.Status = status.New(codes.OK, "").Proto()
				}
				resp.Entries[i] = entry
			}

			if err := stream.Send(resp); err != nil {
				streamErr = status.Errorf(codes.Internal, "failed to stream response: %v", err)
			}

			log.Info().Msgf("[%s] delete finished successfully, closing stream", mutateID)
			errChan <- streamErr
		}()

		// Wait for completion
		return <-errChan
	})
}

func (s *BigtableProxyServer) wrapWithLoggingCheckAndMutate(
	ctx context.Context,
	callsite string,
	id string,
	f func() (*bigtableproto.CheckAndMutateRowResponse, error),
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	// Handle context cancellation
	resp, err := f()
	if ctx.Err() == context.Canceled {
		log.Error().Msgf("[%s] cancelled on request of client", id)
		return nil, status.Error(codes.Canceled, "client cancelled request")
	}
	if err != nil {
		log.Error().Msgf("[%s] response at %s: err %v", id, callsite, err)
	} else {
		log.Debug().Msgf("[%s] response at %s: success", id, callsite)
	}
	return resp, err
}

func (s *BigtableProxyServer) CheckAndMutateRow(
	ctx context.Context,
	req *bigtableproto.CheckAndMutateRowRequest,
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	operationID := fmt.Sprintf("%x", s.ioSequenceNumber.Add(1))

	return s.wrapWithLoggingCheckAndMutate(ctx, "check_and_mutate_row", operationID, func() (*bigtableproto.CheckAndMutateRowResponse, error) {
		tracer := otel.GetTracerProvider().Tracer("")
		ctx, span := tracer.Start(ctx, "check_and_mutate_row")
		defer span.End()

		tenantDetails, err := s.validateAndGetTenantDetails(ctx, req.GetTenantId(), req.GetTableName(), WRITE)
		if err != nil {
			return nil, err
		}

		// Protect against bigtable stalling
		timeoutCtx, cancel := context.WithTimeout(ctx, s.timeout)
		defer cancel()

		if filterDependsOnValue(req.GetPredicateFilter()) {
			return s.handleReadCheckModifyRow(timeoutCtx, operationID, tenantDetails, req)
		}
		return s.handleConditionalMutateRow(timeoutCtx, operationID, tenantDetails, req)
	})
}

func (s *BigtableProxyServer) handleReadCheckModifyRow(
	ctx context.Context,
	operationID string,
	tenantDetails *tenantproto.Tenant,
	req *bigtableproto.CheckAndMutateRowRequest,
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	passthrough := s.shouldUsePassthrough(req.GetTableName())

	var tableName string
	var rowKey []byte
	if passthrough {
		tableName = s.resolvePassthroughTableName(req.GetTableName())
		rowKey = req.GetRowKey()
	} else {
		tableName = s.resolveCentralTableName(req.GetTableName())
		rowKey = addTenantIDToKey(req.GetRowKey(), tenantDetails.Id, true)
	}

	var table *bigtable.Table
	if passthrough {
		table = s.client.Open(tableName)
	} else {
		table = s.centralClient.Open(tableName)
	}

	// Convert the predicate filter
	filter, err := convertProtoFilter(req.GetPredicateFilter())
	if err != nil {
		return nil, err
	}

	chunkFilter := func(chunk *btpb.ReadRowsResponse_CellChunk) bool {
		pass, err := evaluateProtoFilterLocally(req.GetPredicateFilter(), chunk)
		if err != nil {
			log.Warn().Err(err).Msg("failed to evaluate filter locally")
			return false
		}
		return pass
	}

	// Read and evaluate predicate
	var matched bool = false
	var originalRow bigtable.Row

	rowSet := bigtable.SingleRow(string(rowKey))
	decryptErrorCount := 0
	err = table.ReadRows(ctx, rowSet, func(row bigtable.Row) bool {
		chunks, decryptErrorCount := s.convertRowToChunks(ctx, tableName, row, tenantDetails, passthrough, chunkFilter)
		if decryptErrorCount > 0 {
			log.Warn().Msgf("[%s] failed to decrypt %d chunks in row %s", operationID, decryptErrorCount, string(row.Key()))
			ReadDecryptionErrors.WithLabelValues(tenantDetails.Name, tableName).Add(float64(decryptErrorCount))
		}
		if len(chunks) != 0 {
			matched = true
		}
		originalRow = row
		return true
	}, bigtable.RowFilter(filter))
	if err != nil {
		return nil, err
	}
	if decryptErrorCount > 0 {
		return nil, status.Errorf(codes.Internal, "failed to decrypt %d chunks in row %s", decryptErrorCount, string(rowKey))
	}

	// Create value check filter
	var valueCheckFilters []bigtable.Filter
	for family, cols := range originalRow {
		for _, col := range cols {
			colName := strings.TrimPrefix(col.Column, family+":")
			value := col.Value
			endValue := make([]byte, len(value)+1)
			copy(endValue, value)
			endValue[len(value)] = 0 // Add a byte that's just after the value in lexicographical order
			cellFilter := bigtable.ChainFilters(
				bigtable.FamilyFilter("^"+regexp.QuoteMeta(family)+"$"),
				bigtable.ColumnFilter("^"+regexp.QuoteMeta(colName)+"$"),
				bigtable.ValueRangeFilter(value, endValue),
			)
			valueCheckFilters = append(valueCheckFilters, cellFilter)
		}
	}

	if len(valueCheckFilters) == 0 {
		// For value-based predicates, having no values to check is unexpected
		// Apply false mutations directly since there's nothing to match
		falseMut, err := s.convertProtoMutations(ctx, tableName, req.GetFalseMutations(), tenantDetails)
		if err != nil {
			return nil, err
		}
		if falseMut == nil {
			return &bigtableproto.CheckAndMutateRowResponse{
				PredicateMatched: false,
			}, nil
		}
		if err := table.Apply(ctx, string(rowKey), falseMut); err != nil {
			return nil, err
		}
		return &bigtableproto.CheckAndMutateRowResponse{
			PredicateMatched: false,
		}, nil
	}

	var valueCheckFilter bigtable.Filter
	if len(valueCheckFilters) == 1 {
		valueCheckFilter = valueCheckFilters[0]
	} else {
		valueCheckFilter = bigtable.ChainFilters(valueCheckFilters...)
	}
	valueCheckFilter = bigtable.ChainFilters(valueCheckFilter, bigtable.LatestNFilter(1))

	// Convert appropriate mutation based on predicate match
	var mutToApply *bigtable.Mutation
	if matched {
		mutToApply, err = s.convertProtoMutations(ctx, tableName, req.GetTrueMutations(), tenantDetails)
	} else {
		mutToApply, err = s.convertProtoMutations(ctx, tableName, req.GetFalseMutations(), tenantDetails)
	}
	if err != nil {
		return nil, err
	}

	if mutToApply == nil {
		return &bigtableproto.CheckAndMutateRowResponse{
			PredicateMatched: matched,
		}, nil
	}

	// Apply conditional mutation with value check
	condMut := bigtable.NewCondMutation(valueCheckFilter, mutToApply, nil)
	var finalMatched bool
	matchResult := bigtable.GetCondMutationResult(&finalMatched)
	if err = table.Apply(ctx, string(rowKey), condMut, matchResult); err != nil {
		return nil, err
	}

	if !finalMatched {
		return nil, status.Error(codes.Aborted, "row was modified between read and mutation")
	}

	return &bigtableproto.CheckAndMutateRowResponse{
		PredicateMatched: matched,
	}, nil
}

func (s *BigtableProxyServer) handleConditionalMutateRow(
	ctx context.Context,
	operationID string,
	tenantDetails *tenantproto.Tenant,
	req *bigtableproto.CheckAndMutateRowRequest,
) (*bigtableproto.CheckAndMutateRowResponse, error) {
	passthrough := s.shouldUsePassthrough(req.GetTableName())

	var tableName string
	var rowKey []byte
	if passthrough {
		tableName = s.resolvePassthroughTableName(req.GetTableName())
		rowKey = req.GetRowKey()
	} else {
		tableName = s.resolveCentralTableName(req.GetTableName())
		rowKey = addTenantIDToKey(req.GetRowKey(), tenantDetails.Id, true)
	}

	var table *bigtable.Table
	if passthrough {
		table = s.client.Open(tableName)
	} else {
		table = s.centralClient.Open(tableName)
	}

	// Convert the predicate filter
	filter, err := convertProtoFilter(req.GetPredicateFilter())
	if err != nil {
		return nil, err
	}
	// Lack of filter should apply true mutations. Matches Bigtable behavior.
	if filter == nil {
		log.Warn().Msg("No predicate filter provided, applying true mutations only. Just use MutateRows instead.")
		filter = bigtable.PassAllFilter()
	}

	// Convert the mutations
	trueMut, err := s.convertProtoMutations(ctx, tableName, req.GetTrueMutations(), tenantDetails)
	if err != nil {
		return nil, err
	}
	falseMut, err := s.convertProtoMutations(ctx, tableName, req.GetFalseMutations(), tenantDetails)
	if err != nil {
		return nil, err
	}

	condMut := bigtable.NewCondMutation(filter, trueMut, falseMut)

	var matched bool
	matchResult := bigtable.GetCondMutationResult(&matched)
	err = table.Apply(ctx, string(rowKey), condMut, matchResult)
	if err != nil {
		return nil, err
	}

	return &bigtableproto.CheckAndMutateRowResponse{
		PredicateMatched: matched,
	}, nil
}

func (s *BigtableProxyServer) Migrate(
	ctx context.Context,
	req *bigtableproto.MigrateRequest,
) (*bigtableproto.MigrateResponse, error) {
	return nil, status.Error(codes.Unimplemented, "migrate is deprecated")
}

func (s *BigtableProxyServer) CheckTenantDataAccess(
	ctx context.Context,
	req *bigtableproto.CheckTenantDataAccessRequest,
) (*bigtableproto.CheckTenantDataAccessResponse, error) {
	tenantDetails, err := s.tenantCache.GetTenant(req.GetTenantId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get tenant details: %v", err)
	}

	// Check if key is accessible if the tenant is encrypted
	if tenantcrypto.HasSealKey(tenantDetails) {
		err := s.tenantCrypto.CheckKeyAccess(ctx, tenantDetails)
		if err != nil {
			// Record the key access check failure
			KeyAccessCheckCounter.WithLabelValues(
				tenantDetails.Name,
				tenantcrypto.GetStatusString(err),
			).Inc()

			return &bigtableproto.CheckTenantDataAccessResponse{
				HasAccess: false,
			}, err
		}

		// Record successful key access check
		KeyAccessCheckCounter.WithLabelValues(
			tenantDetails.Name,
			"success",
		).Inc()
	}

	return &bigtableproto.CheckTenantDataAccessResponse{
		HasAccess: true,
	}, nil
}

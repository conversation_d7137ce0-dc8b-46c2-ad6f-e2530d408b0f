package service

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	ReadRequestRowHistogram = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_bigtable_proxy_read_request_rows",
			Help: "Number of row keys per read request",
		},
		[]string{"tenant_name", "table_name"},
	)

	MutateRequestEntriesHistogram = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_bigtable_proxy_mutate_request_entries",
			Help: "Number of entries per mutate request",
		},
		[]string{"tenant_name", "table_name"},
	)

	ReadResponseChunksHistogram = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_bigtable_proxy_read_response_chunks",
			Help: "Number of chunks in a read response",
		},
		[]string{"tenant_name", "table_name"},
	)

	ReadRequestLatencyCollector = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_bigtable_proxy_read_request_latency",
			Help: "Histogram of the latency of read requests",
		},
		// we record the tenant id should the claim not have the tenant name set, e.g.
		// for namespace-wide tokens
		[]string{"tenant_name", "table_name", "tenant_id"},
	)

	ReadRequestLatencyPerRowCollector = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_bigtable_proxy_read_row_request_latency",
			Help: "Histogram of the latency of read requests per row key",
		},
		// we record the tenant id should the claim not have the tenant name set, e.g.
		// for namespace-wide tokens
		[]string{"tenant_name", "table_name", "tenant_id"},
	)

	ReadDecryptionErrors = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_bigtable_proxy_read_decryption_errors",
			Help: "Number of decryption errors",
		},
		[]string{"tenant_name", "table_name"},
	)

	MockKMSUsage = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "au_bigtable_proxy_mock_kms_usage",
			Help: "Incremented when bigtable_proxy starts with mock KMS",
		},
	)

	// Track chunk sizes for read and write operations
	// Also provides visibility into how much data is encrypted
	// operation = "read" or "write"
	ChunkSizeBytes = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "au_bigtable_proxy_chunk_size_bytes",
			Help:    "Histogram of chunk sizes in bytes",
			Buckets: append([]float64{0}, prometheus.ExponentialBuckets(256, 4, 11)...), // 0, 256B to 256MB
		},
		[]string{"tenant_name", "table_name", "operation", "encrypted"},
	)

	// Track latency-add for cryptographic operations
	// operation = "open" or "seal"
	CryptoLatency = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_bigtable_proxy_crypto_latency",
			Help: "Histogram of the latency of cryptographic operations",
			Buckets: []float64{
				0.0001, 0.0002, 0.0005, // 0.1ms, 0.2ms, 0.5ms
				0.001, 0.002, 0.005, // 1ms, 2ms, 5ms
				0.01, 0.02, 0.05, // 10ms, 20ms, 50ms
				0.1, 0.2, 0.5, // 100ms, 200ms, 500ms
				1.0, 2.0, 5.0, // 1s, 2s, 5s
			},
		},
		[]string{"tenant_name", "table_name", "operation", "status_code"},
	)

	// Track key access check operations
	KeyAccessCheckCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_bigtable_proxy_key_access_check_count",
			Help: "Number of tenant key access check operations",
		},
		[]string{"tenant_name", "status_code"},
	)
)

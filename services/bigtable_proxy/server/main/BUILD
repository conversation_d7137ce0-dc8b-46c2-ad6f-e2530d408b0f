load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")

go_library(
    name = "bigtable_proxy_server_main",
    srcs = [
        "main.go",
    ],
    importpath = "github.com/augmentcode/augment/services/bigtable_proxy/server/main",
    visibility = ["//visibility:private"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/logging:logging_go",
        "//base/logging/audit:audit_go",
        "//base/tracing/go:tracing_go",
        "//services/bigtable_proxy:bigtable_proxy_go_proto",
        "//services/bigtable_proxy/server/main/service:service_lib",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/metrics:grpc_metrics_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange/client:client_go",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":bigtable_proxy_server_main"],
    visibility = ["//services/bigtable_proxy:__subpackages__"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services/bigtable_proxy/server:__pkg__"],
)

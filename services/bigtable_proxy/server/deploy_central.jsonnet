// K8S deployment file to create central resources (i.e. bigtable tables) for bigtable_proxy
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  assert cloudInfo.isCentralNamespace(env, namespace, cloud);
  local appName = 'bigtable-proxy';  // Same as server appName to ensure same IAM labels
  local contentManagerBigtable = gcpLib.createBigtableTable(cloud=cloud,
                                                            env=env,
                                                            app=appName,
                                                            namespace=namespace,
                                                            tableName='content-manager',
                                                            columnFamily=[
                                                              {
                                                                family: 'Info',
                                                              },
                                                              {
                                                                family: 'Content',
                                                              },
                                                              {
                                                                family: 'Checkpoint',
                                                              },
                                                              {
                                                                family: 'UserIndex',
                                                              },
                                                              {
                                                                family: 'AnnIndex',
                                                              },
                                                            ],
                                                            iamServiceAccountName=null);
  local shareBigtable = gcpLib.createBigtableTable(cloud=cloud,
                                                   env=env,
                                                   app=appName,
                                                   namespace=namespace,
                                                   tableName='share',
                                                   columnFamily=[
                                                     {
                                                       family: 'Chat',
                                                     },
                                                   ],
                                                   iamServiceAccountName=null);

  local settingsBigtable = gcpLib.createBigtableTable(cloud=cloud,
                                                      env=env,
                                                      app=appName,
                                                      namespace=namespace,
                                                      tableName='settings',
                                                      columnFamily=[
                                                        {
                                                          family: 'Settings',
                                                        },
                                                      ],
                                                      iamServiceAccountName=null);

  local slackMappingsBigtable = gcpLib.createBigtableTable(cloud=cloud,
                                                           env=env,
                                                           app=appName,
                                                           namespace=namespace,
                                                           tableName='slack-mappings',
                                                           columnFamily=[
                                                             {
                                                               family: 'Mappings',
                                                             },
                                                           ],
                                                           iamServiceAccountName=null);

  // used by //services/github/state/server
  local githubColumnFamilies = ['Indexed', 'Current', 'RefInfo', 'FileInfos'];
  local githubFamilyConfigs = std.map(
    function(name) { family: name },
    githubColumnFamilies
  );

  local githubBigtableTable = gcpLib.createBigtableTable(cloud=cloud,
                                                         env=env,
                                                         app=appName,
                                                         namespace=namespace,
                                                         tableName='github',
                                                         columnFamily=githubFamilyConfigs,
                                                         iamServiceAccountName=null);
  local githubBigtableGc = if env != 'DEV' then gcpLib.createBigtableGc(cloud=cloud,
                                                                        env=env,
                                                                        namespace=namespace,
                                                                        appName=appName,
                                                                        bigtable=githubBigtableTable,
                                                                        columnFamilies=githubColumnFamilies,
                                                                        gcRules={ rules: [{ max_version: 2 }] });

  local remoteAgentsColumnFamilies = ['Config', 'Status', 'Output', 'UserMapping', 'Timestamp'];
  local remoteAgentsFamilyConfigs = std.map(
    function(name) { family: name },
    remoteAgentsColumnFamilies
  );
  // Only in dev for now
  local remoteAgentsBigtable = gcpLib.createBigtableTable(cloud=cloud,
                                                          env=env,
                                                          app=appName,
                                                          namespace=namespace,
                                                          tableName='remote-agents',
                                                          columnFamily=remoteAgentsFamilyConfigs,
                                                          iamServiceAccountName=null);

  // Working set table for the workingset service
  local workingSetBigtable = gcpLib.createBigtableTable(cloud=cloud,
                                                        env=env,
                                                        app=appName,
                                                        namespace=namespace,
                                                        tableName='working-set',
                                                        columnFamily=[
                                                          {
                                                            family: 'State',
                                                          },
                                                        ],
                                                        iamServiceAccountName=null);
  local remoteAgentsBigtableGc = if env != 'DEV' then gcpLib.createBigtableGc(cloud=cloud,
                                                                              env=env,
                                                                              namespace=namespace,
                                                                              appName=appName,
                                                                              bigtable=remoteAgentsBigtable,
                                                                              columnFamilies=remoteAgentsColumnFamilies,
                                                                              gcRules={ rules: [{ max_version: 2 }] });
  // TODO: add GC policy for remote agents

  lib.flatten([
    contentManagerBigtable.objects,
    shareBigtable.objects,
    settingsBigtable.objects,
    slackMappingsBigtable.objects,
    githubBigtableTable.objects,
    if githubBigtableGc != null then githubBigtableGc.objects,
    remoteAgentsBigtable.objects,
    if remoteAgentsBigtableGc != null then remoteAgentsBigtableGc.objects,
    workingSetBigtable.objects,
  ])

# Share Service

The Share Service is a microservice that facilitates sharing data amongst the
users within a tenant. The service provides an interface to store data that can
be later referenced through an externally visible uuid. The lifetime of the data
will be dependent on the client needs, and may extend to the lifetime of the
tenant.

The first use for this service is to share chat conversations. A user may
request a stable uuid to a specific chat conversation, and this uuid can then be
used to retrieve the conversation at a later time by any user within the tenant.

The chat sessions are recorded into dedicated table intended to persist chat
sessions for the lifetime of the tenant.

## Considerations

I created the Share service outside of the Content Manager service for two
reasons:

1. The data in the content manager can be dropped at anytime, while the shared
data can not. I didn't want to mix lifetimes within the content manager tables.
2. The functionality required by Share service didn't overlap with that of the
content manager, so it seemed unnecessary to expand the content manager's
responsibilities.

## Security
The Share service runs in the namespace and uses the bigtable-proxy service for
access to bigtable. A conversation can only be accessed from the same tenant
that created it.

A client calling the `SaveChatConversation` rpc will need a token with
`CONTENT_RW` scope. The tenant to which the conversation will be written is
specified in the token. The Share service will persist the chat converation
using the bigtable-proxy service, which will ensure the tenant-id is prepended
to the record.

A client calling the The `GetChatConversation` rpc will need a token with
`CONTENT_R` scope. The conversation is only retrieved from the tenant specified
in the token. The Share service will read the conversation data using the
bigtable-proxy service, which will ensure the tenant-id is prepended to the
record.

A note about using the `CONTENT_*` scope: The chat data is considered user
content, so we decided to use the `CONTENT_*` scope for this data as well. If
there is ever a case where a client _should not_ be able to access shared chat
conversations, but can access other user content we should consider adding a new
scope.

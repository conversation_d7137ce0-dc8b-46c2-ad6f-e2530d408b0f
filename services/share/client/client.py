"""A Python client library for the Share service."""

import logging
from typing import Optional

import grpc

from services.lib.request_context.request_context import RequestContext
from services.share import share_pb2, share_pb2_grpc


def setup_stub(
    endpoint: str, credentials: Optional[grpc.ChannelCredentials]
) -> share_pb2_grpc.ShareStub:
    """Setup the client stub for a Share service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(endpoint)
    else:
        channel = grpc.secure_channel(endpoint, credentials)
    return share_pb2_grpc.ShareStub(channel)


class ShareClient:
    """Class to call the Share service APIs remotely."""

    def __init__(self, endpoint: str, credentials: Optional[grpc.ChannelCredentials]):
        self.stub = setup_stub(endpoint, credentials)

    def save_chat(
        self,
        request: share_pb2.SaveChatConversationRequest,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> share_pb2.SaveChatConversationResponse:
        """Save a chat message."""
        response = self.stub.SaveChatConversation(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

    def get_chat(
        self,
        request: share_pb2.GetChatConversationRequest,
        request_context: RequestContext,
        timeout: float = 30,
    ) -> share_pb2.GetChatConversationResponse:
        """Read a chat message."""
        response = self.stub.GetChatConversation(
            request, timeout=timeout, metadata=request_context.to_metadata()
        )
        return response

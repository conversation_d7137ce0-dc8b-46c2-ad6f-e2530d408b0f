[package]
name = "share_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "share_client"
path = "client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
grpc_client = { path = "../../lib/grpc/client" }
request_context = { path = "../../lib/request_context" }
prost = { workspace = true }
prost-wkt-types = { workspace = true }
tonic = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }

[build-dependencies]
tonic-build = { workspace = true }
prost-build = {workspace = true}
prost-wkt-build = {workspace = true}

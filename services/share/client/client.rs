use std::{sync::Arc, time::Duration};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;
use tonic::transport::ClientTlsConfig;

use crate::proto::share::{
    ChatExchange, GetChatConversationRequest, GetChatConversationResponse,
    SaveChatConversationRequest,
};
use request_context::RequestContext;
use tracing_tonic::client::TracingService;

pub mod proto {
    pub mod share {
        tonic::include_proto!("share");
    }
}

#[async_trait]
pub trait ShareClient {
    async fn get_chat_conversation(
        &self,
        request_context: &RequestContext,
        uuid: String,
    ) -> Result<GetChatConversationResponse, tonic::Status>;
    async fn save_chat_conversation(
        &self,
        request_context: &RequestContext,
        user: String,
        conversation_id: String,
        chat: &[ChatExchange],
        title: String,
    ) -> Result<String, tonic::Status>;
}

#[derive(Clone)]
pub struct ShareClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    client: Arc<Mutex<Option<proto::share::share_client::ShareClient<TracingService>>>>,
}

#[async_trait]
impl ShareClient for ShareClientImpl {
    async fn get_chat_conversation(
        &self,
        request_context: &RequestContext,
        uuid: String,
    ) -> Result<GetChatConversationResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("share client not ready: {}", e);
            tonic::Status::unavailable("share service not ready")
        })?;
        let mut request = tonic::Request::new(GetChatConversationRequest { uuid });
        let timeout_millis = 100;
        request.set_timeout(Duration::from_millis(timeout_millis));
        request_context.annotate(request.metadata_mut());

        let response = client.get_chat_conversation(request).await?;
        Ok(response.into_inner())
    }
    async fn save_chat_conversation(
        &self,
        request_context: &RequestContext,
        user: String,
        conversation_id: String,
        chat: &[ChatExchange],
        title: String,
    ) -> Result<String, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("share client not ready: {}", e);
            tonic::Status::unavailable("share service not ready")
        })?;

        let chat = chat.to_vec();

        let save_req = SaveChatConversationRequest {
            user,
            conversation_id,
            chat,
            title,
        };
        let mut request = tonic::Request::new(save_req);
        let timeout_millis = 1000;
        request.set_timeout(Duration::from_millis(timeout_millis));
        request_context.annotate(request.metadata_mut());

        let response = client.save_chat_conversation(request).await?;

        Ok(response.into_inner().uuid)
    }
}

impl ShareClientImpl {
    pub fn new(endpoint: &str, tls_config: Option<ClientTlsConfig>) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<proto::share::share_client::ShareClient<TracingService>, tonic::transport::Error>
    {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = proto::share::share_client::ShareClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

pub struct MockShareClient {}

impl Default for MockShareClient {
    fn default() -> Self {
        Self::new()
    }
}

impl MockShareClient {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl ShareClient for MockShareClient {
    async fn get_chat_conversation(
        &self,
        _request_context: &RequestContext,
        _uuid: String,
    ) -> Result<GetChatConversationResponse, tonic::Status> {
        Ok(GetChatConversationResponse {
            uuid: "test-uuid".to_string(),
            date: None,
            user: "test-user".to_string(),
            conversation_id: "test-conversation-id".to_string(),
            chat: vec![],
            title: "This is a test chat conversation".to_string(),
        })
    }
    async fn save_chat_conversation(
        &self,
        _request_context: &RequestContext,
        _user: String,
        _conversation_id: String,
        _chat: &[ChatExchange],
        _title: String,
    ) -> Result<String, tonic::Status> {
        Ok("test-uuid".to_string())
    }
}

load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "share_proto",
    srcs = ["share.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@protobuf//:field_mask_proto",
        "@protobuf//:timestamp_proto",
    ],
)

ts_proto_library(
    name = "share_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":share_proto",
    visibility = ["//services:__subpackages__"],
)

go_proto_library(
    name = "share_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "github.com/augmentcode/augment/services/share/proto",
    proto = ":share_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)

py_grpc_library(
    name = "share_proto_py_proto",
    protos = [":share_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

# a small CLI utility to issue requests against the Share service API
py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        requirement("pydantic"),
        "//base/logging:console_logging",
        "//services/lib/request_context:request_context_py",
        "//services/share/client:client_py",
    ],
)

// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/share/share.proto (package share, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message share.ChatExchange
 */
export declare class ChatExchange extends Message<ChatExchange> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * @generated from field: string response = 3;
   */
  response: string;

  constructor(data?: PartialMessage<ChatExchange>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "share.ChatExchange";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatExchange;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatExchange;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatExchange;

  static equals(a: ChatExchange | PlainMessage<ChatExchange> | undefined, b: ChatExchange | PlainMessage<ChatExchange> | undefined): boolean;
}

/**
 * @generated from message share.GetChatConversationRequest
 */
export declare class GetChatConversationRequest extends Message<GetChatConversationRequest> {
  /**
   * @generated from field: string uuid = 1;
   */
  uuid: string;

  constructor(data?: PartialMessage<GetChatConversationRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "share.GetChatConversationRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetChatConversationRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetChatConversationRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetChatConversationRequest;

  static equals(a: GetChatConversationRequest | PlainMessage<GetChatConversationRequest> | undefined, b: GetChatConversationRequest | PlainMessage<GetChatConversationRequest> | undefined): boolean;
}

/**
 * @generated from message share.GetChatConversationResponse
 */
export declare class GetChatConversationResponse extends Message<GetChatConversationResponse> {
  /**
   * @generated from field: string uuid = 1;
   */
  uuid: string;

  /**
   * @generated from field: google.protobuf.Timestamp date = 2;
   */
  date?: Timestamp;

  /**
   * @generated from field: string user = 3;
   */
  user: string;

  /**
   * @generated from field: string conversation_id = 4;
   */
  conversationId: string;

  /**
   * @generated from field: repeated share.ChatExchange chat = 5;
   */
  chat: ChatExchange[];

  /**
   * @generated from field: string title = 6;
   */
  title: string;

  constructor(data?: PartialMessage<GetChatConversationResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "share.GetChatConversationResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetChatConversationResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetChatConversationResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetChatConversationResponse;

  static equals(a: GetChatConversationResponse | PlainMessage<GetChatConversationResponse> | undefined, b: GetChatConversationResponse | PlainMessage<GetChatConversationResponse> | undefined): boolean;
}

/**
 * @generated from message share.SaveChatConversationRequest
 */
export declare class SaveChatConversationRequest extends Message<SaveChatConversationRequest> {
  /**
   * @generated from field: string user = 1;
   */
  user: string;

  /**
   * @generated from field: string conversation_id = 2;
   */
  conversationId: string;

  /**
   * @generated from field: repeated share.ChatExchange chat = 3;
   */
  chat: ChatExchange[];

  /**
   * @generated from field: string title = 4;
   */
  title: string;

  constructor(data?: PartialMessage<SaveChatConversationRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "share.SaveChatConversationRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveChatConversationRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveChatConversationRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveChatConversationRequest;

  static equals(a: SaveChatConversationRequest | PlainMessage<SaveChatConversationRequest> | undefined, b: SaveChatConversationRequest | PlainMessage<SaveChatConversationRequest> | undefined): boolean;
}

/**
 * @generated from message share.SaveChatConversationResponse
 */
export declare class SaveChatConversationResponse extends Message<SaveChatConversationResponse> {
  /**
   * @generated from field: string uuid = 1;
   */
  uuid: string;

  constructor(data?: PartialMessage<SaveChatConversationResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "share.SaveChatConversationResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SaveChatConversationResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SaveChatConversationResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SaveChatConversationResponse;

  static equals(a: SaveChatConversationResponse | PlainMessage<SaveChatConversationResponse> | undefined, b: SaveChatConversationResponse | PlainMessage<SaveChatConversationResponse> | undefined): boolean;
}


syntax = "proto3";

package share;

import "google/protobuf/timestamp.proto";

// A service to store and retrieve messages shared across tenants.
service Share {
  // returns the requested chat conversation
  rpc GetChatConversation(GetChatConversationRequest) returns (GetChatConversationResponse);
  // saves the provided chat conversation
  rpc SaveChatConversation(SaveChatConversationRequest) returns (SaveChatConversationResponse);
}

// Note: the message and response strings may contain markdown and other special characters
// that need to be specially handled for rendering.
message ChatExchange {
  string request_id = 1;
  string message = 2 [debug_redact = true];
  string response = 3 [debug_redact = true];
}

message GetChatConversationRequest {
  string uuid = 1;
}

message GetChatConversationResponse {
  string uuid = 1;
  google.protobuf.Timestamp date = 2;
  string user = 3;
  string conversation_id = 4;
  repeated ChatExchange chat = 5;
  string title = 6 [debug_redact = true];
}

message SaveChatConversationRequest {
  string user = 1;
  string conversation_id = 2;
  repeated ChatExchange chat = 3;
  string title = 4 [debug_redact = true];
}

message SaveChatConversationResponse {
  string uuid = 1;
}

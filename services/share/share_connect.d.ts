// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/share/share.proto (package share, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { GetChatConversationRequest, GetChatConversationResponse, SaveChatConversationRequest, SaveChatConversationResponse } from "./share_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service share.Share
 */
export declare const Share: {
  readonly typeName: "share.Share",
  readonly methods: {
    /**
     * @generated from rpc share.Share.GetChatConversation
     */
    readonly getChatConversation: {
      readonly name: "GetChatConversation",
      readonly I: typeof GetChatConversationRequest,
      readonly O: typeof GetChatConversationResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc share.Share.SaveChatConversation
     */
    readonly saveChatConversation: {
      readonly name: "SaveChatConversation",
      readonly I: typeof SaveChatConversationRequest,
      readonly O: typeof SaveChatConversationResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};


"""A small tool to issue requests against the Share API."""

import argparse
import logging
import os
import sys
from pathlib import Path

import grpc
import pydantic
from google.protobuf.json_format import ParseDict, MessageToJson
from base.logging.console_logging import setup_console_logging
from services.lib.request_context.request_context import RequestContext
from services.share import share_pb2
from services.share.client.client import ShareClient

logging.basicConfig(level=logging.INFO)


def _get_client(args) -> ShareClient:
    if args.insecure:
        credentials = None
    else:
        if not args.ca_cert or not args.client_key or not args.client_cert:
            logging.error(
                "--ca-cert, --client-key, --client-cert need to be set "
                "unless --insecure is used"
            )
            sys.exit(1)
        credentials = grpc.ssl_channel_credentials(
            root_certificates=args.ca_cert.read_bytes(),
            private_key=args.client_key.read_bytes(),
            certificate_chain=args.client_cert.read_bytes(),
        )
    rpc_client = ShareClient(args.endpoint, credentials=credentials)
    return rpc_client


def get_chat(client: ShareClient, uuid: str, auth_token_file: None | Path):
    request_pb = share_pb2.GetChatConversationRequest()
    request_pb.uuid = uuid

    auth_token = None
    if auth_token_file:
        auth_token = pydantic.SecretStr(
            auth_token_file.read_text(encoding="utf-8").strip()
        )
    request_context = RequestContext.create(auth_token=auth_token)
    resp = client.get_chat(request_pb, request_context=request_context)

    logging.info("\n%s", MessageToJson(resp, indent=2))

    return resp


def save_chat(
    client: ShareClient,
    user: str,
    message: str,
    response: str,
    auth_token_file: None | Path,
):
    request_pb = share_pb2.SaveChatConversationRequest()
    ParseDict(
        {
            "user": user,
            "conversation_id": "test-id",
            "chat": [
                {"request_id": "test", "message": message, "response": response},
            ],
        },
        request_pb,
    )

    auth_token = None
    if auth_token_file:
        auth_token = pydantic.SecretStr(
            auth_token_file.read_text(encoding="utf-8").strip()
        )
    request_context = RequestContext.create(auth_token=auth_token)
    resp = client.save_chat(request_pb, request_context=request_context)

    logging.info("\n%s", MessageToJson(resp, indent=2))

    return resp.uuid


def main():
    """Main entrypoint."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--endpoint", required=True, help="Endpoint to connect to, e.g. localhost:50051"
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="If true, the request will be in plaintext instead of a secure connection",
    )
    parser.add_argument("--ca-cert", type=Path, help="Path to the ca certificate")
    parser.add_argument("--client-key", type=Path, help="Path to the TLS client key")
    parser.add_argument(
        "--client-cert", type=Path, help="Path to the TLS client certificate"
    )
    parser.add_argument(
        "--auth-token-file",
        type=Path,
        required=False,
        help="Path to file containing authentication token",
    )

    subparsers = parser.add_subparsers(dest="action", help="Commands", required=True)

    get_chat_parser = subparsers.add_parser("get_chat", help="Get a chat")
    get_chat_parser.add_argument("--uuid", required=True, help="UUID of the chat")

    save_chat_parser = subparsers.add_parser("save_chat", help="Get a chat")
    save_chat_parser.add_argument("--user", required=True, help="Author of the message")
    save_chat_parser.add_argument("--message", required=True, help="Message to save")
    save_chat_parser.add_argument("--response", required=True, help="Response to save")

    args = parser.parse_args()
    client = _get_client(args)
    auth_token_file = args.auth_token_file if args.auth_token_file else None
    # auth_token_file = None
    if args.action == "save_chat":
        uuid = save_chat(
            client, args.user, args.message, args.response, auth_token_file
        )
        logging.info("Saved chat, received %s", uuid)
    elif args.action == "get_chat":
        msg = get_chat(client, args.uuid, auth_token_file)
        logging.info("Get chat, received %s", msg)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()

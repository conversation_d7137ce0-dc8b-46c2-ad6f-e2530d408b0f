/* Implements the Share service for maintaining shareable chat links */
package main

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"time"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	authproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	shareproto "github.com/augmentcode/augment/services/share/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type ShareServer struct {
	ProxyClient bigtableproxy.BigtableProxyClient
	RIClient    ripublisher.RequestInsightPublisher
}

func NewShareServer(riClient ripublisher.RequestInsightPublisher, config *Config) (*ShareServer, error) {
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		return nil, err
	}
	proxyClient, err := bigtableproxy.NewBigtableProxyClient(config.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		return nil, err
	}

	return &ShareServer{
		ProxyClient: proxyClient,
		RIClient:    riClient,
	}, nil
}

// Return the key used to map link -> chat
func getChatRowKey(uuid string) string {
	return fmt.Sprintf("chat#%s", uuid)
}

// Generate a unique id string for the chat record suitable for use in a url.
// This id based on a random 64 bit number encoded in base64. This key
// will be user visible, so we are trying to minimize the size while keeping
// a low probability of collision.
func generateUid() (string, error) {
	b := make([]byte, 8)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.RawURLEncoding.EncodeToString(b), nil
}

// Return the key used to map <conversation_id, request_id> -> uuid
func getLinkRowKey(conversationId string, oldestRequestId string, newestRequestId string) string {
	return fmt.Sprintf("link#%s#%s#%s", conversationId, oldestRequestId, newestRequestId)
}

// GetChatConversation retrieves the conversation payload record using the link
// Returns an error if the payload can not be retrieved for some reason.
func (s *ShareServer) GetChatConversation(ctx context.Context, req *shareproto.GetChatConversationRequest) (*shareproto.GetChatConversationResponse, error) {
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}

	log.Debug().Str("uuid", req.Uuid).Msg("Get chat")

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}

	tenantInfo := &riproto.TenantInfo{
		TenantId:   authInfo.TenantID,
		TenantName: authInfo.TenantName,
	}
	requestId := string(requestContext.RequestId)

	// Publish a RequestMetadata event in the service rather than the caller.
	// Ideally, this event would be published in the caller, but the client is
	// the Customer UI service, which is a central service, so publishing the
	// event from there is harder.
	// Note that authInfo.UserID is always an augment user id from auth-central, since
	// the user must be logged in via oauth to open a shareable link.
	riMetaEvent := ripublisher.NewRequestEvent()
	riMetaEvent.Event = &riproto.RequestEvent_RequestMetadata{
		RequestMetadata: &riproto.RequestMetadata{
			RequestType: riproto.RequestType_SHARE_GET_CHAT,
			SessionId:   requestId,
			UserId:      authInfo.UserID,
			OpaqueUserId: &authproto.UserId{
				UserId:     authInfo.UserID,
				UserIdType: authproto.UserId_AUGMENT,
			},
			// Note(jacqueline): Other metadata publishers set UserEmail here as well, but we don't have
			// that information available in auth claims. We can always get the email from the `user`
			// table in analytics (and I want to remove UserEmail from RequestMetadata anyway).
			UserAgent: "unknown",
		},
	}
	s.RIClient.PublishRequestEvent(ctx, requestId, tenantInfo, riMetaEvent)

	// Record the request in RI.
	riEvent := ripublisher.NewRequestEvent()
	riEvent.Event = &riproto.RequestEvent_ShareGetChatRequest{
		ShareGetChatRequest: &riproto.RIShareGetChatRequest{
			Request: proto.Clone(req).(*shareproto.GetChatConversationRequest),
		},
	}
	s.RIClient.PublishRequestEvent(ctx, requestId, tenantInfo, riEvent)

	chatRowKey := getChatRowKey(req.Uuid)
	chatRows, err := s.ProxyClient.ReadRows(
		ctx,
		authInfo.TenantID,
		proxyproto.TableName_SHARE,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{[]byte(chatRowKey)},
		},
		nil,
		1,
		requestContext,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read chat")
		return nil, status.Errorf(codes.Internal, "Failed to read chat: %v", err)
	}
	if len(chatRows) != 1 {
		err := status.Errorf(codes.Internal, "Expected 1 row, got %d", len(chatRows))
		log.Error().Err(err).Msg("Failed to read chat")
		return nil, err
	}

	found := false
	chat := &shareproto.GetChatConversationResponse{}
	for _, cell := range chatRows[0].Cells {
		switch string(cell.Qualifier) {
		case "share":
			err := proto.Unmarshal(cell.Value, chat)
			if err != nil {
				log.Error().Err(err).Msg("Failed to unmarshal chat")
				return nil, status.Errorf(codes.Internal, "Failed to unmarshal chat: %v", err)
			}
			found = true
			break
		}
	}

	if !found {
		log.Error().Msg("Failed to find chat")
		return nil, status.Error(codes.NotFound, "Failed to find chat")
	}

	if chat.Uuid != req.Uuid {
		err := status.Errorf(codes.Internal, "Expected uuid %s, found uuid %s", req.Uuid, chat.Uuid)
		log.Error().Err(err).Msg("Record mismatch")
		return nil, err
	}

	// Record the response in RI.
	riOutEvent := ripublisher.NewRequestEvent()
	riOutEvent.Event = &riproto.RequestEvent_ShareGetChatResponse{
		ShareGetChatResponse: &riproto.RIShareGetChatResponse{
			Response: proto.Clone(chat).(*shareproto.GetChatConversationResponse),
		},
	}
	s.RIClient.PublishRequestEvent(ctx, requestId, tenantInfo, riOutEvent)

	return chat, nil
}

// SaveChatConversation will persist the chat conversation and return a unique
// link (uuid) for later retrieval. If the same chat conversation has been
// stored previously, the same link is returned.
//
// A unique chat conversation is represented by a conversation_id and *the last*
// request_id in the exchange list (this should be the most recent exchange).
// If the conversation moves forward with new exchanges, it is considered a new
// conversation for purposes of sharing.  If the chat conversation is modified
// in a way that the last exchange request_id stays the same (e.g. delete an
// exchange), then this protocol will need to be modified to generate a new
// conversation.
func (s *ShareServer) SaveChatConversation(ctx context.Context, req *shareproto.SaveChatConversationRequest) (*shareproto.SaveChatConversationResponse, error) {
	// We speculatively record a new chat record before checking whether it is a
	// duplicate chat conversation. If the chat is found to be a duplicate, we
	// delete the new chat record and return the original link. This order of
	// operations guarantees that a failure between writing the two records does
	// not leave a partially recorded chat that can never be shared. Failure
	// might leave an orphaned chat record, but that is expected to be rare and
	// will not affect correctness.

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	if authInfo.TenantID == "" {
		return nil, status.Error(codes.InvalidArgument, "tenant_id is required")
	}

	if len(req.Chat) == 0 {
		return nil, status.Error(codes.InvalidArgument, "chat is required")
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}

	tenantInfo := &riproto.TenantInfo{
		TenantId:   authInfo.TenantID,
		TenantName: authInfo.TenantName,
	}
	requestId := string(requestContext.RequestId)

	// Record the request in RI.
	riEvent := ripublisher.NewRequestEvent()
	riEvent.Event = &riproto.RequestEvent_ShareSaveChatRequest{
		ShareSaveChatRequest: &riproto.RIShareSaveChatRequest{
			Request: proto.Clone(req).(*shareproto.SaveChatConversationRequest),
		},
	}
	s.RIClient.PublishRequestEvent(ctx, requestId, tenantInfo, riEvent)

	// Speculatively insert a new chat record. If we find the conversation has
	// been previously shared, we'll later delete this record and return the
	// original link.
	uid, err := generateUid()
	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to generate uid: %v", err)
	}
	chatRowKey := getChatRowKey(uid)

	chatRec := &shareproto.GetChatConversationResponse{
		Uuid:           uid,
		Date:           timestamppb.New(time.Now()),
		User:           req.User,
		ConversationId: req.ConversationId,
		Chat:           req.Chat, // shallow copy, no modification
		Title:          req.Title,
	}

	chatBytes, err := proto.Marshal(chatRec)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to marshal chat: %v", err)
	}

	lastIdx := len(req.Chat) - 1
	lastChatRequestId := req.Chat[lastIdx].RequestId
	log.Info().Str("last_chat_request_id", lastChatRequestId).Str("uid", uid).Int("size", len(chatBytes)).Msg("Save Chat")

	chatRowMutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      "Chat",
					ColumnQualifier: []byte("share"),
					Value:           chatBytes,
				},
			},
		},
	}
	chatRowEntries := []*bigtableproto.MutateRowsRequest_Entry{
		{
			RowKey:    []byte(chatRowKey),
			Mutations: chatRowMutations,
		},
	}

	_, err = s.ProxyClient.MutateRows(
		ctx,
		authInfo.TenantID,
		proxyproto.TableName_SHARE,
		chatRowEntries,
		requestContext,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to save chat")
		return nil, status.Errorf(codes.Internal, "Failed to save chat: %v", err)
	}

	// Maps a conversation to a link, for duplicate detection. If a link is
	// already present, rollback the new chat record and return the original
	// link id.
	linkValue := &shareproto.GetChatConversationRequest{Uuid: uid}
	linkBytes, err := proto.Marshal(linkValue)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to marshal link value: %v", err)
	}
	linkRowMutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      "Chat",
					ColumnQualifier: []byte("share"),
					Value:           linkBytes,
				},
			},
		},
	}

	linkRowFilter := &bigtableproto.RowFilter{
		Filter: &bigtableproto.RowFilter_PassAllFilter{PassAllFilter: true},
	}

	linkRowKey := getLinkRowKey(req.ConversationId, req.Chat[0].RequestId, req.Chat[lastIdx].RequestId)

	resp, err := s.ProxyClient.CheckAndMutateRow(
		ctx,
		authInfo.TenantID,
		proxyproto.TableName_SHARE,
		[]byte(linkRowKey),
		linkRowFilter,
		nil,
		linkRowMutations,
		requestContext,
	)
	if err != nil {
		log.Error().Err(err).Str("uid", uid).Msgf("Failed to check for existing conversation. May have leaked link.")
		return nil, status.Errorf(codes.Internal, "Failed to check for existing conversation: %v", err)
	}

	// If an existing link-row key was found, rollback the new chat record and
	// return the original link
	var link *shareproto.SaveChatConversationResponse
	if resp.PredicateMatched {
		log.Debug().Str("uid", uid).Msg("Existing link was found, rolling back speculative link id")
		err := s.removeChat(ctx, authInfo, chatRowKey, requestContext)
		if err != nil {
			log.Error().Err(err).Str("uid", uid).Str("chatRowKey", chatRowKey).Msg("Failed to rollback speculative link")
			return nil, status.Errorf(codes.Internal, "Failed to rollback speculative link: %v", err)
		}
		link, err = s.readLink(ctx, authInfo, linkRowKey, requestContext)
		if err != nil {
			log.Error().Err(err).Str("uid", uid).Str("linkRowKey", linkRowKey).Msg("Failed to read existing link.")
			return nil, status.Errorf(codes.Internal, "Failed to read existing link: %v", err)
		}
		log.Debug().Str("uuid", link.Uuid).Msg("Existing conversation found")
	} else {
		link = &shareproto.SaveChatConversationResponse{Uuid: uid}
	}

	// Record the response in RI
	riOutEvent := ripublisher.NewRequestEvent()
	riOutEvent.Event = &riproto.RequestEvent_ShareSaveChatResponse{
		ShareSaveChatResponse: &riproto.RIShareSaveChatResponse{
			Response: proto.Clone(link).(*shareproto.SaveChatConversationResponse),
		},
	}
	s.RIClient.PublishRequestEvent(ctx, requestId, tenantInfo, riOutEvent)

	return link, nil
}

// removeChat deletes the chat record for the given key.
func (s *ShareServer) removeChat(ctx context.Context, authInfo *auth.AugmentClaims, chatRowKey string, requestContext *requestcontext.RequestContext) error {
	mutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_DeleteFromRow_{
				DeleteFromRow: &bigtableproto.Mutation_DeleteFromRow{},
			},
		},
	}
	convEntries := []*bigtableproto.MutateRowsRequest_Entry{
		{
			RowKey:    []byte(chatRowKey),
			Mutations: mutations,
		},
	}

	_, err := s.ProxyClient.MutateRows(
		ctx,
		authInfo.TenantID,
		proxyproto.TableName_SHARE,
		convEntries,
		requestContext,
	)
	return err
}

// readLink returns the sharable link for a conversation.
func (s *ShareServer) readLink(ctx context.Context, authInfo *auth.AugmentClaims, linkRowKey string, requestContext *requestcontext.RequestContext) (*shareproto.SaveChatConversationResponse, error) {
	rows, err := s.ProxyClient.ReadRows(
		ctx,
		authInfo.TenantID,
		proxyproto.TableName_SHARE,
		&bigtableproto.RowSet{RowKeys: [][]byte{[]byte(linkRowKey)}},
		nil,
		1,
		requestContext,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to read conversation link")
		return nil, err
	}

	found := false
	persistedUuid := &shareproto.SaveChatConversationResponse{}
	for _, cell := range rows[0].Cells {
		switch string(cell.Qualifier) {
		case "share":
			err := proto.Unmarshal(cell.Value, persistedUuid)
			if err != nil {
				log.Error().Err(err).Msg("Failed to unmarshal conversation link")
				return nil, err
			}
			found = true
			break
		}
	}

	if !found {
		log.Error().Msg("Failed to find conversation")
		return nil, status.Error(codes.NotFound, "Failed to find conversation")
	}
	return persistedUuid, nil
}

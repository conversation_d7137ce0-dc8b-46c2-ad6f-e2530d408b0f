package main

import (
	"context"
	"fmt"
	"testing"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	"github.com/augmentcode/augment/base/go/secretstring"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	shareproto "github.com/augmentcode/augment/services/share/proto"
	server "github.com/augmentcode/augment/services/share/server"
	"github.com/google/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

var inputChat = []*shareproto.ChatExchange{
	{
		RequestId: "1",
		Message:   "What is your name color?",
		Response:  "Blue. No yellow!",
	},
	{
		RequestId: "2",
		Message:   "What is your name?",
		Response:  "King Arthor of the Britons!",
	},
}

// The mock bigtable client will record updates in an in memory map.  Mutations
// are assumed to contain only a single entry, but can be extended if the
// implementation changes.
type MockBigtableProxyClient struct {
	data map[string][]byte
}

func NewMockBigtabelProxyClient() *MockBigtableProxyClient {
	return &MockBigtableProxyClient{
		data: make(map[string][]byte),
	}
}

func (m *MockBigtableProxyClient) CheckTenantDataAccess(ctx context.Context, tenantID string, requestContext *requestcontext.RequestContext) error {
	return nil
}

// Mock ReadRows
func (m *MockBigtableProxyClient) ReadRows(ctx context.Context, tenantID string, tableName proxyproto.TableName, rows *bigtableproto.RowSet, filter *bigtableproto.RowFilter, rowsLimit int64, request *requestcontext.RequestContext, opts ...grpc.CallOption) ([]*bigtableproxy.Row, error) {
	if len(rows.RowKeys) != 1 {
		return nil, status.Errorf(codes.Unimplemented, "expected exactly one row")
	}

	key := string(rows.RowKeys[0])
	returnRows := []*bigtableproxy.Row{
		{
			RowKey: []byte(key),
			Cells: []*bigtableproxy.RowCell{
				{
					FamilyName: "Chat",
					Qualifier:  []byte("share"),
					Value:      m.data[key],
				},
			},
		},
	}
	return returnRows, nil
}

// Mock MutateRows.
func (m *MockBigtableProxyClient) MutateRows(ctx context.Context, tenantID string, tableName proxyproto.TableName, entries []*bigtableproto.MutateRowsRequest_Entry, request *requestcontext.RequestContext) ([]*proxyproto.MutateRowsResponse, error) {
	if len(entries) != 1 {
		return nil, status.Errorf(codes.Unimplemented, "expected exactly one row")
	}
	if len(entries[0].Mutations) != 1 {
		return nil, status.Errorf(codes.Unimplemented, "expected exactly one mutation")
	}

	key := string(entries[0].RowKey)
	mutation := entries[0].Mutations[0]
	switch mutation.Mutation.(type) {
	case *bigtableproto.Mutation_SetCell_:
		m.data[key] = mutation.GetSetCell().Value
		return nil, nil
	case *bigtableproto.Mutation_DeleteFromRow_:
		delete(m.data, key)
		return nil, nil
	default:
		return nil, status.Errorf(codes.Unimplemented, "mutation type not supported")
	}
}

// Mock CheckAndMutateRow.
func (m *MockBigtableProxyClient) CheckAndMutateRow(ctx context.Context, tenantID string, tableName proxyproto.TableName, rowKey []byte, predicateFilter *bigtableproto.RowFilter, trueMutations []*bigtableproto.Mutation, falseMutations []*bigtableproto.Mutation, request *requestcontext.RequestContext) (*proxyproto.CheckAndMutateRowResponse, error) {
	if len(falseMutations) != 1 {
		return nil, status.Errorf(codes.Unimplemented, "expected exactly one row")
	}
	if trueMutations != nil {
		return nil, status.Errorf(codes.Unimplemented, "true mutations not implemented")
	}

	key := string(rowKey)
	_, exists := m.data[key]
	matched := false
	if exists {
		matched = true
	} else {
		matched = false
		m.data[key] = falseMutations[0].GetSetCell().Value
	}
	return &proxyproto.CheckAndMutateRowResponse{
		PredicateMatched: matched,
	}, nil
}

func (m *MockBigtableProxyClient) Close() {}

// Create a new context suitable for testing
func newTestContext() context.Context {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		UserID:         "user-id",
		ServiceName:    "service-name",
		TenantID:       "tenant-id",
		TenantName:     "tenant-name",
		ShardNamespace: "shard-namespace",
		Scope:          []string{"CONTENT_RW"},
	}
	ctx = claims.NewContext(ctx)

	requestContext := requestcontext.New(requestcontext.NewRandomRequestId(), requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	ctx = requestcontext.NewIncomingContext(ctx, requestContext)
	return ctx
}

// Validate that a saved chat conversation can be retrieved.
func TestSaveChat(t *testing.T) {
	ctx := newTestContext()

	// Create a test server
	riMock := ripublisher.NewRequestInsightPublisherMock()
	shareServer, err := server.NewShareServer(riMock, &server.Config{
		TokenExchangeEndpoint: "https://example.com",
		BigtableProxyEndpoint: "https://example.com",
	})
	if err != nil {
		t.Fatalf("NewSettingsServer returned error: %v", err)
	}
	mockProxyClient := NewMockBigtabelProxyClient()
	shareServer.ProxyClient = mockProxyClient

	// Create a test request
	convId := uuid.New().String()
	saveReq := &shareproto.SaveChatConversationRequest{
		User:           "test-user",
		ConversationId: convId,
		Chat:           inputChat,
		Title:          "This is a test conversation",
	}

	// Save a chat message and validate that it can be retrieved.
	resp, err := shareServer.SaveChatConversation(ctx, saveReq)
	if err != nil {
		t.Errorf("SaveChat returned error: %v", err)
	}

	getReq := &shareproto.GetChatConversationRequest{
		Uuid: resp.Uuid,
	}
	getResp, err := shareServer.GetChatConversation(ctx, getReq)
	if err != nil {
		t.Fatalf("GetChat returned error: %v", err)
	}

	err = validateSavedChatRequests(saveReq, getResp)
	if err != nil {
		t.Fatalf("Chat messages do not match: %v", err)
	}
}

// Validate that saving a duplicate chat conversation returns the original link.
func TestSaveDuplicateChat(t *testing.T) {
	ctx := newTestContext()

	// Create a test server
	riMock := ripublisher.NewRequestInsightPublisherMock()
	shareServer, err := server.NewShareServer(riMock, &server.Config{
		TokenExchangeEndpoint: "https://example.com",
		BigtableProxyEndpoint: "https://example.com",
	})
	if err != nil {
		t.Errorf("NewSettingsServer returned error: %v", err)
	}
	mockProxyClient := NewMockBigtabelProxyClient()
	shareServer.ProxyClient = mockProxyClient

	// Create a test request
	convId := uuid.New().String()
	saveReq := &shareproto.SaveChatConversationRequest{
		User:           "test-user",
		ConversationId: convId,
		Chat:           inputChat,
	}

	// Save a chat conversatiion twice. Ensure that only one is recorded.
	resp, err := shareServer.SaveChatConversation(ctx, saveReq)
	if err != nil {
		t.Errorf("SaveChat returned error: %v", err)
	}

	respDup, err := shareServer.SaveChatConversation(ctx, saveReq)
	if err != nil {
		t.Errorf("SaveChat returned error: %v", err)
	}

	if resp.Uuid != respDup.Uuid {
		t.Fatalf("Duplicate chat conversation uuids do not match, expected: %s, got: %s", resp.Uuid, respDup.Uuid)
	}

	// Checking for a leak in the records after a duplicate entry.  This is an
	// implementation detail and may change with the implementation.
	// We currently expect a link registration record and a conversation record.
	if len(mockProxyClient.data) != 2 {
		t.Errorf("Unexpected number of rows in table, %d expected %d", len(mockProxyClient.data), 2)
	}

	// Validate we can still retrieve the original exchange
	getReq := &shareproto.GetChatConversationRequest{
		Uuid: resp.Uuid,
	}
	getResp, err := shareServer.GetChatConversation(ctx, getReq)
	if err != nil {
		t.Fatalf("GetChat returned error: %v", err)
	}

	err = validateSavedChatRequests(saveReq, getResp)
	if err != nil {
		t.Fatalf("Chat messages do not match: %v", err)
	}
}

// Validate that adding a chat exchange to an existing conversation is treated
// as a unique conversation.
func TestExtendChat(t *testing.T) {
	ctx := newTestContext()

	// Create a test server
	riMock := ripublisher.NewRequestInsightPublisherMock()
	shareServer, err := server.NewShareServer(riMock, &server.Config{
		TokenExchangeEndpoint: "https://example.com",
		BigtableProxyEndpoint: "https://example.com",
	})
	if err != nil {
		t.Errorf("NewSettingsServer returned error: %v", err)
	}
	mockProxyClient := NewMockBigtabelProxyClient()
	shareServer.ProxyClient = mockProxyClient

	// Save a chat conversation
	convId := uuid.New().String()
	saveReq := &shareproto.SaveChatConversationRequest{
		User:           "test-user",
		ConversationId: convId,
		Chat:           inputChat,
	}
	resp, err := shareServer.SaveChatConversation(ctx, saveReq)
	if err != nil {
		t.Errorf("SaveChat returned error: %v", err)
	}

	// Create a new chat conversation that extends the previous one
	extChat := make([]*shareproto.ChatExchange, 0, len(inputChat))
	for _, chatMsg := range inputChat {
		extChat = append(extChat, &shareproto.ChatExchange{
			RequestId: chatMsg.RequestId,
			Message:   chatMsg.Message,
			Response:  chatMsg.Response,
		})
	}
	newExchange := shareproto.ChatExchange{
		RequestId: uuid.New().String(),
		Message:   "What is your quest?",
		Response:  "I seek the Holy Grail!",
	}
	extChat = append(extChat, &newExchange)

	extReq := &shareproto.SaveChatConversationRequest{
		User:           "test-user",
		ConversationId: convId,
		Chat:           extChat,
	}

	extResp, err := shareServer.SaveChatConversation(ctx, extReq)
	if err != nil {
		t.Errorf("SaveChat returned error: %v", err)
	}

	if resp.Uuid == extResp.Uuid {
		t.Fatalf("Unique chat uuids should not match, %s", resp.Uuid)
	}

	// Validate we can still retrieve the original exchange.
	getReq := &shareproto.GetChatConversationRequest{
		Uuid: resp.Uuid,
	}
	respGet, err := shareServer.GetChatConversation(ctx, getReq)
	if err != nil {
		t.Fatalf("GetChat returned error: %v", err)
	}

	result := validateSavedChatRequests(saveReq, respGet)
	if result != nil {
		t.Fatalf("Chat messages do not match: %v", result)
	}

	// Validate we can retrieve the extended conversation.
	getExtReq := &shareproto.GetChatConversationRequest{
		Uuid: extResp.Uuid,
	}
	respExtGet, err := shareServer.GetChatConversation(ctx, getExtReq)
	if err != nil {
		t.Fatalf("GetChat returned error: %v", err)
	}

	resultExt := validateSavedChatRequests(extReq, respExtGet)
	if resultExt != nil {
		t.Fatalf("Chat messages do not match: %v", resultExt)
	}
}

// Saving a slice of a longer conversation should be possible.
func TestChatSlice(t *testing.T) {
	// This test saves a longer conversation first, and then saves single
	// chat exchanges from the beginning and end of that longer conversation.
	// The test validates that all three are distinct conversations, and can
	// be correctly retrieved.

	// Create a test server
	ctx := newTestContext()
	riMock := ripublisher.NewRequestInsightPublisherMock()
	shareServer, err := server.NewShareServer(riMock, &server.Config{
		TokenExchangeEndpoint: "https://example.com",
		BigtableProxyEndpoint: "https://example.com",
	})
	if err != nil {
		t.Errorf("NewSettingsServer returned error: %v", err)
	}
	mockProxyClient := NewMockBigtabelProxyClient()
	shareServer.ProxyClient = mockProxyClient

	// This test requires a conversation have at least two exchanges.
	if len(inputChat) < 2 {
		t.Fatalf("Conversation must have 2 or more exchanges, found: %d.", len(inputChat))
	}

	// Save the full chat conversation
	convId := uuid.New().String()
	saveFullReq := &shareproto.SaveChatConversationRequest{
		User:           "test-user",
		ConversationId: convId,
		Chat:           inputChat,
		Title:          "This is a test title",
	}
	fullResp, err := shareServer.SaveChatConversation(ctx, saveFullReq)
	if err != nil {
		t.Errorf("SaveChat returned error: %v", err)
	}

	// Save a single message from the start of the conversation.
	saveFirstReq := proto.Clone(saveFullReq).(*shareproto.SaveChatConversationRequest)
	saveFirstReq.Chat = []*shareproto.ChatExchange{saveFullReq.Chat[0]}
	firstResp, err := shareServer.SaveChatConversation(ctx, saveFirstReq)
	if err != nil {
		t.Fatalf("SaveChat returned error: %v", err)
	}
	if proto.Equal(firstResp, fullResp) {
		t.Fatalf("First slice should have a unique uuid, found: %v", firstResp)
	}

	// Save a single message from the end of the conversation.
	saveLastReq := proto.Clone(saveFullReq).(*shareproto.SaveChatConversationRequest)
	saveLastReq.Chat = []*shareproto.ChatExchange{saveFullReq.Chat[len(saveFullReq.Chat)-1]}
	lastResp, err := shareServer.SaveChatConversation(ctx, saveLastReq)
	if err != nil {
		t.Fatalf("SaveChat returned error: %v", err)
	}
	// Ensure the last slice has a unique uuid.
	if proto.Equal(lastResp, fullResp) || proto.Equal(lastResp, firstResp) {
		t.Fatalf("Last slice should have a unique uuid, found: %v, fullResp=%v, firstResp=%v", lastResp, fullResp, firstResp)
	}

	// Ensure we can retrieve the full conversation
	getReq := &shareproto.GetChatConversationRequest{Uuid: fullResp.Uuid}
	getResp, err := shareServer.GetChatConversation(ctx, getReq)
	err = validateSavedChatRequests(saveFullReq, getResp)
	if err != nil {
		t.Fatalf("SaveChat returned error: %v", err)
	}
	// Ensure we can retrieve the first exchange
	getReq = &shareproto.GetChatConversationRequest{Uuid: firstResp.Uuid}
	getResp, err = shareServer.GetChatConversation(ctx, getReq)
	err = validateSavedChatRequests(saveFirstReq, getResp)
	if err != nil {
		t.Fatalf("SaveChat returned error: %v", err)
	}
	// Ensure we can retrieve the last exchange
	getReq = &shareproto.GetChatConversationRequest{Uuid: lastResp.Uuid}
	getResp, err = shareServer.GetChatConversation(ctx, getReq)
	err = validateSavedChatRequests(saveLastReq, getResp)
	if err != nil {
		t.Fatalf("SaveChat returned error: %v", err)
	}
}

// Ensure a saved conversation is equal to the retrieved conversation.
func validateSavedChatRequests(a *shareproto.SaveChatConversationRequest, b *shareproto.GetChatConversationResponse) error {
	if a.User != b.User {
		return fmt.Errorf("Expected user %s, got %s", a.User, b.User)
	}
	if a.ConversationId != b.ConversationId {
		return fmt.Errorf("Expected conversation ID %s, got %s", a.ConversationId, b.ConversationId)
	}
	if a.Title != b.Title {
		return fmt.Errorf("Expected title \"%s\", got \"%s\"", a.Title, b.Title)
	}
	if len(a.Chat) != len(b.Chat) {
		return fmt.Errorf("Expected chat length %d, got %d", len(a.Chat), len(b.Chat))
	}
	for i, chatMsg := range a.Chat {
		if !proto.Equal(chatMsg, b.Chat[i]) {
			return fmt.Errorf("Expected chat %d, %v got %v", i, chatMsg, b.Chat[i])
		}
	}
	return nil
}

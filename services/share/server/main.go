package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"

	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	shareproto "github.com/augmentcode/augment/services/share/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/reflection"

	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
)

var configFile = flag.String("config", "", "Path to config file")

type Config struct {
	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	CentralServerMtls *tlsconfig.ServerConfig `json:"central_server_mtls"`

	Namespace string `json:"namespace"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	BigtableProxyEndpoint string `json:"bigtable_proxy_endpoint"`
	RIConfigFile          string `json:"request_insight_publisher_config_file"`
}

func run(config *Config, grpcServer *grpc.Server) error {
	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	go func() {
		// Wait for either a shutdown signal or an OS signal
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		grpcServer.GracefulStop()
	}()

	ctx := context.Background()
	riClient, err := ripublisher.NewRequestInsightPublisherFromFile(ctx, config.RIConfigFile)
	if err != nil {
		return err
	}

	server, err := NewShareServer(riClient, config)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating share server")
	}

	shareproto.RegisterShareServer(grpcServer, server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}

	log.Info().Msgf("Listening on %v", lis.Addr())
	err = grpcServer.Serve(lis)
	if err != nil && err != grpc.ErrServerStopped {
		log.Fatal().Err(err).Msg("Error serving")
	}
	log.Info().Msg("gRPC server closed")
	return nil
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	var opts []grpc.ServerOption
	var centralClientDialOption grpc.DialOption
	if config.ServerMtls != nil {
		// Note: the server and client configurations for mtls are synchronized in the
		// deploy script, so they will be nil or non-nil together.
		centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
		if err != nil {
			log.Fatal().Err(err).Msgf("Error creating client credentials")
		}
		centralClientDialOption = grpc.WithTransportCredentials(centralClientCreds)

		serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls, config.CentralServerMtls})
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating TLS config")
		}
		opts = append(opts, grpc.Creds(serverTls))
	} else {
		centralClientDialOption = grpc.WithInsecure()
	}
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(config.TokenExchangeEndpoint, config.Namespace, centralClientDialOption)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()
	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	err = run(&config, grpcServer)
	if err != nil {
		log.Fatal().Err(err).Msg("Error serving")
	}
}

local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'share';
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  // creates a service for the pod, also create a global service if necessary
  // to allow the lead cluster (customer-ui) to talk to this pod. We only
  // deploy these when necessary, to avoid running into GCP limits.
  local useGlobalService = !cloudInfo.isLeadCluster(cloud);
  local serverDnsNames = grpcLib.grpcServiceNames(appName);
  local centralServerDnsNames = grpcLib.grpcServiceNamespaceNames(appName, namespace=namespace) + if useGlobalService then [
    grpcLib.globalGrpcServiceHostname(cloud=cloud, serviceName=appName, namespace=namespace),
  ] else [];
  local services = [
    grpcLib.grpcService(appName=appName, namespace=namespace),
  ] + if useGlobalService then [
    grpcLib.globalGrpcService(cloud=cloud, appName=appName, namespace=namespace),
  ] else [];

  // a client certificate to authenticiate with grpc servers running in the namespace
  local clientCert = certLib.createClientCert(name='%s-client-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              volumeName='client-certs',
                                              dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace));

  // a client certificate to authenticiate with grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(name='%s-central-client-cert' % appName,
                                                            namespace=namespace,
                                                            env=env,
                                                            appName=appName,
                                                            volumeName='central-client-certs',
                                                            dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace));

  // creates a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=serverDnsNames,
                                              volumeName='certs');


  // creates a server certificate for central MTLS
  local centralServerCert = certLib.createCentralServerCert(name='%s-central-server-certificate' % appName,
                                                            namespace=namespace,
                                                            appName=appName,
                                                            dnsNames=centralServerDnsNames,
                                                            env=env,
                                                            volumeName='central-certs');

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );
  local requestInsightPublisher = publisherLib(cloud, env, namespace, appName);

  local config = {
    port: 50051,
    client_mtls: if mtls then clientCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    server_mtls: if mtls then serverCert.config else null,
    central_server_mtls: if mtls then centralServerCert.config else null,
    prom_port: 9090,
    namespace: namespace,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    bigtable_proxy_endpoint: 'bigtable-proxy-svc:50051',
    request_insight_publisher_config_file: requestInsightPublisher.configFilePath,
  };

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];

  local container = {
    name: appName,
    target: {
      name: '//services/share/server:image',
      dst: 'share',
    },
    args: [
      '--config',
      configMap.filename,
    ],
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    volumeMounts: [
      configMap.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      serverCert.volumeMountDef,
      centralServerCert.volumeMountDef,
      requestInsightPublisher.volumeMountDef,
    ],
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      serverCert.podVolumeDef,
      centralServerCert.podVolumeDef,
      requestInsightPublisher.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccountObjects,
    clientCert.objects,
    centralClientCert.objects,
    serverCert.objects,
    centralServerCert.objects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    services,
  ])

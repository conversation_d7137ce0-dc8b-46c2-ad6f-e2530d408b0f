load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/bigtable_proxy/server:kubecfg_central",
        "//services/deploy:base_kubecfg",
        "//services/grpc_debug/server:kubecfg",
        "//services/share/server:kubecfg",
    ],
)

pytest_test(
    name = "share_test",
    size = "enormous",
    timeout = "eternal",
    srcs = [
        "conftest.py",
        "share_test.py",
    ],
    data = [
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/python/grpc:health_check",
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/grpc_debug:grpc_debug_py_proto",
        "//services/grpc_debug/client:client_py",
        "//services/lib/request_context:request_context_py",
        "//services/share:share_proto_py_proto",
        "//services/token_exchange/client:client_py",
        requirement("kubernetes"),
    ],
)

import pytest
import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from services.lib.request_context.request_context import RequestContext
from services.share import share_pb2_grpc, share_pb2
import uuid
import copy


def create_sample_conversation() -> [share_pb2.SaveChatConversationRequest]:
    save_req = share_pb2.SaveChatConversationRequest(
        user="test-user",
        conversation_id=str(uuid.uuid4()),
        chat=[
            share_pb2.ChatExchange(
                request_id=str(uuid.uuid4()),
                message="What is your name?",
                response="It is <PERSON>, King of the Britons.",
            ),
            share_pb2.ChatExchange(
                request_id=str(uuid.uuid4()),
                message="What is your quest?",
                response="To seek the Holy Grail.",
            ),
        ],
        title="This is a test chat conversation",
    )
    return save_req


# Given a save request and response, validate that the corresponding "get"
# response matches.
def validate_response(
    save_req: share_pb2.SaveChatConversationRequest,
    save_resp: share_pb2.SaveChatConversationResponse,
    get_resp: share_pb2.GetChatConversationResponse,
):
    assert save_resp.uuid == get_resp.uuid
    assert save_req.conversation_id == get_resp.conversation_id
    assert save_req.chat == get_resp.chat
    assert save_req.title == get_resp.title


# We can save and retrieve a chat session.
def test_share_service(
    request_context: RequestContext,
    share_client: share_pb2_grpc.ShareStub,
):
    save_req = create_sample_conversation()
    save_resp = share_client.SaveChatConversation(
        save_req, metadata=request_context.to_metadata()
    )
    assert save_resp.uuid

    get_req = share_pb2.GetChatConversationRequest(uuid=save_resp.uuid)
    get_resp = share_client.GetChatConversation(
        get_req,
        metadata=request_context.to_metadata(),
    )

    validate_response(save_req, save_resp, get_resp)


# Saving a duplicate chat conversation returns the original uuid.
def test_share_duplicate_conversation(
    request_context: RequestContext,
    share_client: share_pb2_grpc.ShareStub,
):
    save_req = create_sample_conversation()
    save_resp = share_client.SaveChatConversation(
        save_req, metadata=request_context.to_metadata()
    )
    assert save_resp.uuid

    save_dup_resp = share_client.SaveChatConversation(
        save_req, metadata=request_context.to_metadata()
    )
    assert save_resp == save_dup_resp

    get_req = share_pb2.GetChatConversationRequest(uuid=save_resp.uuid)
    get_resp = share_client.GetChatConversation(
        get_req,
        metadata=request_context.to_metadata(),
    )

    validate_response(save_req, save_resp, get_resp)


# Extending a conversation produces a new conversation
def test_share_extended_conversation(
    request_context: RequestContext,
    share_client: share_pb2_grpc.ShareStub,
):
    save_req = create_sample_conversation()
    save_resp = share_client.SaveChatConversation(
        save_req, metadata=request_context.to_metadata()
    )
    assert save_resp.uuid

    save_ext_req = copy.deepcopy(save_req)
    save_ext_req.chat.append(
        share_pb2.ChatExchange(
            request_id=str(uuid.uuid4()),
            message="What is the air-speed velocity of an unladen swallow?",
            response="What do you mean? An African or European swallow?",
        )
    )
    save_ext_resp = share_client.SaveChatConversation(
        save_ext_req, metadata=request_context.to_metadata()
    )
    assert save_resp.uuid != save_ext_resp

    get_req = share_pb2.GetChatConversationRequest(uuid=save_ext_resp.uuid)
    get_resp = share_client.GetChatConversation(
        get_req,
        metadata=request_context.to_metadata(),
    )

    validate_response(save_ext_req, save_ext_resp, get_resp)


# Saving a slice of a conversation produces a new conversation
def test_share_slice_conversation(
    request_context: RequestContext,
    share_client: share_pb2_grpc.ShareStub,
):
    save_req = create_sample_conversation()
    assert len(save_req.chat) >= 2

    save_resp = share_client.SaveChatConversation(
        save_req, metadata=request_context.to_metadata()
    )
    assert save_resp.uuid

    # Save just the first exchange
    save_first_req = share_pb2.SaveChatConversationRequest(
        user=save_req.user,
        conversation_id=save_req.conversation_id,
        title=save_req.title,
        chat=[save_req.chat[0]],
    )
    save_first_resp = share_client.SaveChatConversation(
        save_first_req, metadata=request_context.to_metadata()
    )
    assert save_resp.uuid != save_first_resp.uuid

    # Save just the last exchange
    save_last_req = share_pb2.SaveChatConversationRequest(
        user=save_req.user,
        conversation_id=save_req.conversation_id,
        title=save_req.title,
        chat=[save_req.chat[-1]],
    )
    save_last_resp = share_client.SaveChatConversation(
        save_last_req, metadata=request_context.to_metadata()
    )
    assert save_resp.uuid != save_last_resp.uuid
    assert save_first_resp.uuid != save_last_resp.uuid

    # Validate we can still retrieve the original exchange
    get_req = share_pb2.GetChatConversationRequest(uuid=save_resp.uuid)
    get_resp = share_client.GetChatConversation(
        get_req,
        metadata=request_context.to_metadata(),
    )

    validate_response(save_req, save_resp, get_resp)

    # Validate we can retrieve the first exchange
    get_req = share_pb2.GetChatConversationRequest(uuid=save_first_resp.uuid)
    get_resp = share_client.GetChatConversation(
        get_req,
        metadata=request_context.to_metadata(),
    )

    validate_response(save_first_req, save_first_resp, get_resp)

    # Validate we can retrieve the last exchange
    get_req = share_pb2.GetChatConversationRequest(uuid=save_last_resp.uuid)
    get_resp = share_client.GetChatConversation(
        get_req,
        metadata=request_context.to_metadata(),
    )

    validate_response(save_last_req, save_last_resp, get_resp)

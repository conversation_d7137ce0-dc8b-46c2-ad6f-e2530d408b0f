"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

from pathlib import Path
import time
from typing import Generator, Optional, Tuple

import grpc
import kubernetes
import pytest

from services.lib.request_context.request_context import (
    RequestContext,
    create_request_session_id,
)
from base.python.grpc.health_check import HealthChecker
import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
from services.grpc_debug.client.client import GrpcDebugClient, ProxyClient, ProxyMethod
from services.token_exchange import token_exchange_pb2
from services.share import share_pb2_grpc, share_pb2
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--skip-deployment-teardown",
        action="store_true",
        help="skip deploy and delete of the models",
        default=not k8s_test_helper.is_running_in_test_infra(),
    )
    if not k8s_test_helper.is_running_in_test_infra():
        parser.addoption(
            "--teardown-deployment",
            action="store_true",
            help="tear down the complete deployment after the run",
            default=False,
        )
    parser.addoption(
        "--skip-deployment-check",
        action="store_true",
        help="skip checking if the deployments are settled",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


@pytest.fixture(scope="session")
def application_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys the application as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    skip_deploy_check = request.config.getoption("--skip-deployment-check")
    skip_deploy_check_teardown = request.config.getoption(
        "--skip-deployment-teardown"
    ) and not request.config.getoption("--teardown-deployment")
    cloud = request.config.getoption("--cloud")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        skip_deploy_check=skip_deploy_check,
        skip_deploy_check_teardown=skip_deploy_check_teardown,
        cloud=cloud,
        kubecfg_binaries=[
            Path("services/share/test/test_kubecfg.sh"),
        ],
    ) as deploy_info:
        if not skip_deploy:
            time.sleep(120)
        yield deploy_info


NEXT_FORWARDED_PORT = 50052


def get_next_forwarded_port() -> int:
    """Return a fresh local port."""
    global NEXT_FORWARDED_PORT  # pylint: disable=global-statement
    port = NEXT_FORWARDED_PORT
    NEXT_FORWARDED_PORT += 1
    return port


def _get_pod(
    api_client: kubernetes.client.ApiClient | None, namespace: str, deployment_name: str
) -> Optional[str]:
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_name):
            return name


def _test_response(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


@pytest.fixture(scope="session")
def grpc_debug(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[GrpcDebugClient, None, None]:
    """Return a GRPC stub to grpc debug service."""
    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "grpc-debug-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            "deployment/grpc-debug", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield GrpcDebugClient.create_for_endpoint(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcDebugClient.create_for_endpoint(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def share_client(
    grpc_debug: GrpcDebugClient,
) -> share_pb2_grpc.ShareStub:
    """Fixture to interact with the share service."""
    return ProxyClient(
        grpc_debug,
        "https://share-svc:50051",
        [
            ProxyMethod(
                "share.Share",
                "GetChatConversation",
                share_pb2.GetChatConversationResponse,
            ),
            ProxyMethod(
                "share.Share",
                "SaveChatConversation",
                share_pb2.SaveChatConversationResponse,
            ),
        ],
    )


@pytest.fixture(scope="session")
def token_exchange_client(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TokenExchangeClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = application_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "token-exchange-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with application_deploy.kubectl.port_forward(
            "deployment/token-exchange-central", 50051, get_next_forwarded_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(2)
                continue
            else:
                yield GrpcTokenExchangeClient(
                    url,
                    credentials=credentials,
                    options=options,
                    namespace=application_deploy.namespace,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcTokenExchangeClient(
            url,
            credentials=credentials,
            options=options,
            namespace=application_deploy.namespace,
        )


@pytest.fixture(scope="session")
def share_test_tenant(
    application_deploy: k8s_test_helper.DeployInfo,
) -> Generator[Tuple[str, str], None, None]:
    """Return a tenant for the share service test."""
    tenant_name = "share-svc-test"
    with k8s_test_helper.new_tenant(application_deploy, tenant_name) as tenant_id:
        yield (tenant_name, tenant_id)


@pytest.fixture(scope="session")
def request_session_id() -> Generator[str, None, None]:
    """Return a request context."""
    yield create_request_session_id()


@pytest.fixture()
def request_context(
    request_session_id: str,
    token_exchange_client: TokenExchangeClient,
    share_test_tenant,
) -> Generator[RequestContext | None, None, None]:
    """Return a request context."""
    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=share_test_tenant[1],
        scopes=[token_exchange_pb2.CONTENT_RW],
    )
    yield RequestContext.create_for_session(request_session_id, auth_token=token)

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

// Return an object for an IAM service account. We don't use gcp-lib here because we don't need a
// Kubernetes service account or workload identity.
local createIAMServiceAccount(env, cloud, namespace, namePrefix) =
  local projectId = cloudInfo[cloud].projectId;
  local iamServiceAccountName = '%s-iam' % namePrefix;
  local iamServiceAccountEmail = '%s@%s.iam.gserviceaccount.com' % [iamServiceAccountName, projectId];

  // Normally we truncate names, but since this usage is very isolated, just enforce that the name
  // given is short enough.
  assert std.length(iamServiceAccountName) <= 30;

  local objects = [
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMServiceAccount',
      metadata: {
        name: iamServiceAccountName,
        namespace: namespace,
        labels: {
          app: namePrefix,
        },
      },
      spec: {
        displayName: iamServiceAccountName,
      },
    },
  ];

  {
    // The objects to be deployed with kubecfg.
    objects: objects,
    // The name of the service account object.
    name: iamServiceAccountName,
    // The email of the iam service account (used to grant permissions).
    email: iamServiceAccountEmail,
  }
;

// Create service accounts for external use. The service account objects should only be created
// by request_insight/external_service_accounts.jsonnet. This is just a library to make it easier to
// reference these service accounts elsewhere.
local getExternalServiceAccounts(env, cloud) =
  local namespace = 'central';
  {
    coreweaveRIDataImporterSA: createIAMServiceAccount(env, cloud, namespace, 'cw-ri-importer'),
    coreweaveVendorBucketAccessSA: createIAMServiceAccount(env, cloud, namespace, 'cw-vendor-bucket-exporter'),
    hexPIIAnalyticsSA: createIAMServiceAccount(env, cloud, namespace, 'hex-pii-analytics'),
    hexNonPIIAnalyticsSA: createIAMServiceAccount(env, cloud, namespace, 'hex-non-pii-analytics'),

    // This SA is managed by the gtm-services-prod project. We record its information here so that
    // we can grant it access to prod resources with ConfigConnector, but it isn't deployed by
    // ConfigConnector
    gtmDataformSA: {
      name: 'gtm-dataform',
      email: '<EMAIL>',
    },

    // The SA used by Spark jobs is managed by research configs, but it needs access to some prod
    // resources.
    sparkSA: {
      name: 'gcp-us1-spark-sa',
      email: '<EMAIL>',
    },
  }
;

{
  getExternalServiceAccounts:: getExternalServiceAccounts,
}

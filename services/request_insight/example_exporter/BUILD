load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")

go_library(
    name = "example_exporter_lib",
    srcs = [
        "main.go",
    ],
    importpath = "github.com/augmentcode/augment/services/request_insight/example_exporter",
    visibility = ["//visibility:private"],
    deps = [
        "//base/logging:logging_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/lib:request_insight_subscriber_go",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "example_exporter",
    embed = [":example_exporter_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":example_exporter",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/request_insight/lib:exporter_lib",
    ],
)

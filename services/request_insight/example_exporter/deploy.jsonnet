local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local exporterLib = import 'services/request_insight/lib/exporter_lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local projectId = cloudInfo[cloud].projectId;

  local appName = 'request-insight-example-exporter';
  local shortAppName = 'ri-example-exp';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  // Create a pub/sub subscription for this exporter.
  local subscriber = exporterLib.subscriber(cloud, env, namespace, appName, serviceAccount);

  local config = {
    projectId: projectId,
    subscriptionId: subscriber.subscriptionName,
    // Limit the number of concurrent handler goroutines in the subscriber.
    maxConcurrentReceivers: 20,
    healthFile: '/tmp/health',
    promPort: 9090,
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container = {
    name: appName,
    target: {
      name: '//services/request_insight/example_exporter:image',
      dst: 'request_insight_example_exporter',
    },
    volumeMounts: [
      configMap.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 0.5,
        memory: '512Mi',
      },
    },
    args: [
      '--config',
      configMap.filename,
    ],
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 20,
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  // We allow multiple exporters on the same host because all of their processing is async.
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: 0,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    deployment,
    subscriber.objects,
  ])

# Request Insight Publisher

Python and Rust library to publish request insight events to the pub/sub topic.

## Usage

### Python

```python
import requests
import structlog

from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
    RequestInsightPublisherConfig,
)

log = structlog.get_logger()

def main():
    cfg = RequestInsightPublisherConfig(project_id="test", topic_name="test")
    ri_publisher = RequestInsightPublisher(requests.get, cfg, "client_container")

    now = datetime.now()
    completion_resolution = request_insight_pb2.CompletionResolution(accepted_idx=1)
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_resolution.CopyFrom(completion_resolution)

    ri_publisher.record_infer_request("test", event)

if __name__ == "__main__":
    main()
```

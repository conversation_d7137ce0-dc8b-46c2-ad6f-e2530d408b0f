package request_insight_publisher

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	request_insight_proto "github.com/augmentcode/augment/services/request_insight/proto"
	"github.com/google/uuid"

	"cloud.google.com/go/pubsub"
	"github.com/prometheus/client_golang/prometheus"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var publishCount = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "au_request_insight_pubsub_publish_count_total",
	Help: "Number of publishes to the pubsub topic",
}, []string{"message_type", "status"})

func init() {
	prometheus.MustRegister(
		publishCount,
	)
}

type RequestInsightPublisher interface {
	PublishRequestEvent(ctx context.Context, requestID string, tenantInfo *request_insight_proto.TenantInfo, event *request_insight_proto.RequestEvent) error
	PublishSessionEvent(ctx context.Context, sessionID string, opaqueUserID *auth_entities.UserId, tenantInfo *request_insight_proto.TenantInfo, event *request_insight_proto.SessionEvent) error
	PublishTenantEvent(ctx context.Context, tenantInfo *request_insight_proto.TenantInfo, event *request_insight_proto.TenantEvent) error
	PublishGenericEvent(ctx context.Context, sessionID string, event *request_insight_proto.GenericEvent) error
	PublishRequestEvents(ctx context.Context, requestID string, tenantInfo *request_insight_proto.TenantInfo, events []*request_insight_proto.RequestEvent) error
	PublishSessionEvents(ctx context.Context, sessionID string, opaqueUserID *auth_entities.UserId, tenantInfo *request_insight_proto.TenantInfo, events []*request_insight_proto.SessionEvent) error
	PublishTenantEvents(ctx context.Context, tenantInfo *request_insight_proto.TenantInfo, events []*request_insight_proto.TenantEvent) error
	PublishGenericEvents(ctx context.Context, sessionID string, events []*request_insight_proto.GenericEvent) error
	Close() error
}

type requestInsightPublisher struct {
	client *pubsub.Client
	topic  *pubsub.Topic
}

type RequestInsightPublisherConfig struct {
	ProjectId string `json:"project_id"`
	TopicName string `json:"topic_name"`
}

func NewRequestInsightPublisherFromFile(ctx context.Context, configPath string) (RequestInsightPublisher, error) {
	var config RequestInsightPublisherConfig
	configContent, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(configContent, &config)
	if err != nil {
		return nil, err
	}

	return NewRequestInsightPublisher(ctx, config.ProjectId, config.TopicName)
}

func NewRequestInsightPublisher(ctx context.Context, projectId string, topicName string) (RequestInsightPublisher, error) {
	client, err := pubsub.NewClient(ctx, projectId)
	if err != nil {
		return nil, err
	}
	topic := client.Topic(topicName)
	return &requestInsightPublisher{
		client: client,
		topic:  topic,
	}, nil
}

func (p *requestInsightPublisher) PublishRequestEvent(
	ctx context.Context,
	requestID string,
	tenantInfo *request_insight_proto.TenantInfo,
	event *request_insight_proto.RequestEvent,
) error {
	return p.PublishRequestEvents(ctx, requestID, tenantInfo, []*request_insight_proto.RequestEvent{event})
}

func (p *requestInsightPublisher) PublishSessionEvent(
	ctx context.Context,
	sessionID string,
	opaqueUserID *auth_entities.UserId,
	tenantInfo *request_insight_proto.TenantInfo,
	event *request_insight_proto.SessionEvent,
) error {
	return p.PublishSessionEvents(ctx, sessionID, opaqueUserID, tenantInfo, []*request_insight_proto.SessionEvent{event})
}

func (p *requestInsightPublisher) PublishTenantEvent(
	ctx context.Context,
	tenantInfo *request_insight_proto.TenantInfo,
	event *request_insight_proto.TenantEvent,
) error {
	return p.PublishTenantEvents(ctx, tenantInfo, []*request_insight_proto.TenantEvent{event})
}

func (p *requestInsightPublisher) PublishGenericEvent(
	ctx context.Context,
	sessionID string,
	event *request_insight_proto.GenericEvent,
) error {
	return p.PublishGenericEvents(ctx, sessionID, []*request_insight_proto.GenericEvent{event})
}

func (p *requestInsightPublisher) PublishRequestEvents(
	ctx context.Context,
	requestID string,
	tenantInfo *request_insight_proto.TenantInfo,
	events []*request_insight_proto.RequestEvent,
) error {
	msg := request_insight_proto.RequestInsightMessage{
		Message: &request_insight_proto.RequestInsightMessage_UpdateRequestInfoRequest{
			UpdateRequestInfoRequest: &request_insight_proto.UpdateRequestInfoRequest{
				RequestId:  requestID,
				TenantInfo: tenantInfo,
				Events:     events,
			},
		},
	}

	data, err := proto.Marshal(&msg)
	if err != nil {
		return fmt.Errorf("failed to marshal request insight message: %w", err)
	}

	_, err = p.topic.Publish(ctx, &pubsub.Message{
		Data: data,
	}).Get(ctx)
	if err != nil {
		publishCount.WithLabelValues("request-insight", "exception").Inc()
		return fmt.Errorf("failed to publish request insight request events: %w", err)
	}

	publishCount.WithLabelValues("request-insight", "success").Inc()
	return nil
}

func (p *requestInsightPublisher) PublishSessionEvents(
	ctx context.Context,
	sessionID string,
	opaqueUserID *auth_entities.UserId,
	tenantInfo *request_insight_proto.TenantInfo,
	events []*request_insight_proto.SessionEvent,
) error {
	msg := request_insight_proto.RequestInsightMessage{
		Message: &request_insight_proto.RequestInsightMessage_RecordSessionEventsRequest{
			RecordSessionEventsRequest: &request_insight_proto.RecordSessionEventsRequest{
				SessionId:    sessionID,
				TenantInfo:   tenantInfo,
				Events:       events,
				OpaqueUserId: opaqueUserID,
			},
		},
	}

	data, err := proto.Marshal(&msg)
	if err != nil {
		return fmt.Errorf("failed to marshal request insight message: %w", err)
	}

	_, err = p.topic.Publish(ctx, &pubsub.Message{
		Data: data,
	}).Get(ctx)
	if err != nil {
		publishCount.WithLabelValues("session-events", "exception").Inc()
		return fmt.Errorf("failed to publish request insight session events: %w", err)
	}

	publishCount.WithLabelValues("session-events", "success").Inc()
	return nil
}

func (p *requestInsightPublisher) PublishTenantEvents(
	ctx context.Context,
	tenantInfo *request_insight_proto.TenantInfo,
	events []*request_insight_proto.TenantEvent,
) error {
	msg := request_insight_proto.RequestInsightMessage{
		Message: &request_insight_proto.RequestInsightMessage_RecordTenantEventsRequest{
			RecordTenantEventsRequest: &request_insight_proto.RecordTenantEventsRequest{
				TenantInfo: tenantInfo,
				Events:     events,
			},
		},
	}

	data, err := proto.Marshal(&msg)
	if err != nil {
		return fmt.Errorf("failed to marshal request insight message: %w", err)
	}

	_, err = p.topic.Publish(ctx, &pubsub.Message{
		Data: data,
	}).Get(ctx)
	if err != nil {
		publishCount.WithLabelValues("tenant-events", "exception").Inc()
		return fmt.Errorf("failed to publish request insight tenant events: %w", err)
	}

	publishCount.WithLabelValues("tenant-events", "success").Inc()
	return nil
}

func (p *requestInsightPublisher) PublishGenericEvents(
	ctx context.Context,
	sessionID string,
	events []*request_insight_proto.GenericEvent,
) error {
	msg := request_insight_proto.RequestInsightMessage{
		Message: &request_insight_proto.RequestInsightMessage_RecordGenericEventsRequest{
			RecordGenericEventsRequest: &request_insight_proto.RecordGenericEventsRequest{
				SessionId: sessionID,
				Events:    events,
			},
		},
	}

	data, err := proto.Marshal(&msg)
	if err != nil {
		return fmt.Errorf("failed to marshal request insight message: %w", err)
	}

	_, err = p.topic.Publish(ctx, &pubsub.Message{
		Data: data,
	}).Get(ctx)
	if err != nil {
		publishCount.WithLabelValues("generic-events", "exception").Inc()
		return fmt.Errorf("failed to publish request insight generic events: %w", err)
	}

	publishCount.WithLabelValues("generic-events", "success").Inc()
	return nil
}

// Creates a request event with boilerplate fields filled in.
func NewRequestEvent() *request_insight_proto.RequestEvent {
	eventId := uuid.New().String()
	return &request_insight_proto.RequestEvent{
		EventId: &eventId,
		Time:    timestamppb.Now(),
	}
}

// Creates a session event with boilerplate fields filled in.
func NewSessionEvent() *request_insight_proto.SessionEvent {
	eventId := uuid.New().String()
	return &request_insight_proto.SessionEvent{
		EventId: &eventId,
		Time:    timestamppb.Now(),
	}
}

func NewTenantEvent() *request_insight_proto.TenantEvent {
	eventId := uuid.New().String()
	return &request_insight_proto.TenantEvent{
		EventId: eventId,
		Time:    timestamppb.Now(),
	}
}

func NewGenericEvent() *request_insight_proto.GenericEvent {
	eventID := uuid.New().String()
	return &request_insight_proto.GenericEvent{
		EventId: &eventID,
		Time:    timestamppb.Now(),
	}
}

func (p *requestInsightPublisher) Close() error {
	return p.client.Close()
}

// Below this point is a mock implementation for use in tests.
type RequestInsightPublisherMockImpl struct{}

func NewRequestInsightPublisherMock() RequestInsightPublisher {
	return &RequestInsightPublisherMockImpl{}
}

func (p *RequestInsightPublisherMockImpl) PublishRequestEvent(
	ctx context.Context,
	requestID string,
	tenantInfo *request_insight_proto.TenantInfo,
	event *request_insight_proto.RequestEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) PublishSessionEvent(
	ctx context.Context,
	sessionID string,
	opaqueUserID *auth_entities.UserId,
	tenantInfo *request_insight_proto.TenantInfo,
	event *request_insight_proto.SessionEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) PublishTenantEvent(
	ctx context.Context,
	tenantInfo *request_insight_proto.TenantInfo,
	event *request_insight_proto.TenantEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) PublishGenericEvent(
	ctx context.Context,
	sessionID string,
	event *request_insight_proto.GenericEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) PublishRequestEvents(
	ctx context.Context,
	requestID string,
	tenantInfo *request_insight_proto.TenantInfo,
	events []*request_insight_proto.RequestEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) PublishSessionEvents(
	ctx context.Context,
	sessionID string,
	opaqueUserID *auth_entities.UserId,
	tenantInfo *request_insight_proto.TenantInfo,
	events []*request_insight_proto.SessionEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) PublishTenantEvents(
	ctx context.Context,
	tenantInfo *request_insight_proto.TenantInfo,
	events []*request_insight_proto.TenantEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) PublishGenericEvents(
	ctx context.Context,
	sessionID string,
	events []*request_insight_proto.GenericEvent,
) error {
	return nil
}

func (p *RequestInsightPublisherMockImpl) Close() error {
	return nil
}

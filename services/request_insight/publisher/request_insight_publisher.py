"""A Python client library to submit events directly to the request insight pub/sub topic."""

import argparse
import datetime
import logging
import pathlib
import uuid
from concurrent.futures import Future
from dataclasses import dataclass
from functools import partial

from dataclasses_json import dataclass_json
from google.cloud import pubsub_v1  # type: ignore
from prometheus_client import Counter

import services.request_insight.request_insight_pb2 as request_insight_pb2
from services.lib.grpc.auth.service_auth import AuthInfo

# Number of messages published to the pub/sub topic, along with a status. The message_type is either
# "request-insight", "user-events", or "session-events". (Note that most tenants will only ever have
# request-insight messages.)
# Note that if there is a suffix of `_total` on the counter metric name, it will be removed. Then,
# when exposing the time series for counter, a `_total` suffix will be added automatically.
# We add the `_total` suffix here to avoid confusion with other counters.
# See https://prometheus.github.io/client_python/instrumenting/counter/ for more details.
_request_insight_pubsub_publish_count = Counter(
    "au_request_insight_pubsub_publish_count_total",
    "Number of publishes to the pubsub topic",
    ["message_type", "status"],
)


@dataclass_json
@dataclass
class RequestInsightPublisherConfig:
    """Configuration for the request insight publisher."""

    project_id: str
    topic_name: str


class RequestInsightPublisher:
    """Class for publishing UpdateRequestInfo requests directly to the request insight pub/sub topic."""

    @classmethod
    def add_publisher_arguments(cls, parser: argparse.ArgumentParser):
        """Adds arguments necessary to load publisher config."""
        parser.add_argument(
            "--request-insight-publisher-config-file",
            default=pathlib.Path("/request_insight_publisher_config/config.json"),
            type=pathlib.Path,
        )

    @classmethod
    def create_from_args(
        cls,
        args: argparse.Namespace,
    ) -> "RequestInsightPublisher":
        """Create a new request insight pubsub client.

        Assumes that parser arguments were set up using
        `add_publisher_arguments`.
        """
        return cls.create_from_file(args.request_insight_publisher_config_file)

    @classmethod
    def create_from_file(
        cls,
        config_file: pathlib.Path,
    ) -> "RequestInsightPublisher":
        """Create a new request insight pubsub client."""
        config = RequestInsightPublisherConfig.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )
        logging.info("Publisher Config %s", config)
        publisher = pubsub_v1.PublisherClient()
        return cls(publisher, config)

    def __init__(
        self,
        publisher: pubsub_v1.PublisherClient,
        config: RequestInsightPublisherConfig,
    ):
        self.publisher = publisher
        self.topic_path = self.publisher.topic_path(
            config.project_id, config.topic_name
        )
        logging.info("RequestInsight messages will be written to %s", self.topic_path)

    def _publish_done(self, future: Future, message_type: str):
        """Callback for when a publish finishes."""
        try:
            future.result()
            _request_insight_pubsub_publish_count.labels(message_type, "success").inc()
        except pubsub_v1.TimeoutError:  # type: ignore
            _request_insight_pubsub_publish_count.labels(message_type, "timeout").inc()
        except pubsub_v1.CancelledError:  # type: ignore
            _request_insight_pubsub_publish_count.labels(
                message_type, "cancelled"
            ).inc()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            _request_insight_pubsub_publish_count.labels(
                message_type, "exception"
            ).inc()
            logging.error("Publishing UpdateRequestInfo failed: %s", ex)
            logging.exception(ex)

    def publish_request_insight(
        self, request: request_insight_pb2.UpdateRequestInfoRequest
    ):
        """Publish an UpdateRequestInfoRequest to the pub/sub topic."""
        try:
            logging.info(
                "Publishing UpdateRequestInfo message for request %s with events: %s.",
                request.request_id,
                [event.WhichOneof("event") for event in request.events],
            )
            pubsub_message = request_insight_pb2.RequestInsightMessage()
            pubsub_message.update_request_info_request.CopyFrom(request)
            # TODO: add an alert if the publish fails
            future = self.publisher.publish(
                self.topic_path, pubsub_message.SerializeToString()
            )
            future.add_done_callback(
                partial(self._publish_done, message_type="request-insight")
            )

            # TODO: this return value is only for RequestInsightServices and can
            # be removed once that stops supporting UpdateRequestInfo
            return request_insight_pb2.UpdateRequestInfoResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            # We don't want to block the caller, so only log the error
            logging.error("Publishing UpdateRequestInfo failed: %s", ex)
            logging.exception(ex)
            _request_insight_pubsub_publish_count.labels(
                "request-insight", "exception"
            ).inc()

    def publish_user_events(
        self, request: request_insight_pb2.RecordFullExportUserEventsRequest
    ):
        """Publish a RecordFullExportUserEventsRequest to the pub/sub topic.

        Callers are responsible for ensuring this method is only called on tenants who have user
        event collection enabled.
        """
        try:
            logging.info(
                "Publishing RecordFullExportUserEvents message for session %s",
                request.session_id,
            )
            pubsub_message = request_insight_pb2.RequestInsightMessage()
            pubsub_message.record_full_export_user_events_request.CopyFrom(request)
            # TODO: add an alert if the publish fails
            future = self.publisher.publish(
                self.topic_path, pubsub_message.SerializeToString()
            )
            future.add_done_callback(
                partial(self._publish_done, message_type="user-events")
            )

            # TODO: this return value is only for RequestInsightServices and can
            # be removed once that stops supporting RecordFullExportUserEvents.
            return request_insight_pb2.RecordFullExportUserEventsResponse()
        except Exception as ex:  # pylint: disable=broad-exception-caught
            # We don't want to block the caller, so only log the error
            logging.error("Publishing RecordFullExportUserEvents failed: %s", ex)
            logging.exception(ex)
            _request_insight_pubsub_publish_count.labels(
                "user-events", "exception"
            ).inc()

    def publish_generic_events(
        self,
        events: list[request_insight_pb2.GenericEvent],
        session_id: str = "",
    ):
        """Publish a RecordGenericEventsRequest to the pub/sub topic."""

        if len(events) == 0:
            return

        try:
            session_id = session_id or events[0].event_id

            logging.debug(
                "Publishing RecordGenericEvents message for session %s",
                session_id,
            )
            pubsub_message = request_insight_pb2.RequestInsightMessage()
            pubsub_message.record_generic_events_request.session_id = session_id
            pubsub_message.record_generic_events_request.events.extend(events)
            # TODO: add an alert if the publish fails
            future = self.publisher.publish(
                self.topic_path, pubsub_message.SerializeToString()
            )
            future.add_done_callback(
                partial(self._publish_done, message_type="generic-events")
            )
        except Exception as ex:  # pylint: disable=broad-exception-caught
            # We don't want to block the caller, so only log the error
            logging.error("Publishing RecordGenericEvents failed: %s", ex)
            logging.exception(ex)
            _request_insight_pubsub_publish_count.labels(
                "generic-events", "exception"
            ).inc()

    def _to_tenant_info(self, auth_info: AuthInfo):
        return request_insight_pb2.TenantInfo(
            tenant_id=auth_info.tenant_id if auth_info.tenant_id else "",
            tenant_name=auth_info.tenant_name,
        )

    def update_request_info_request(
        self,
        request_id: str,
        events: list[request_insight_pb2.RequestEvent],
        auth_info: AuthInfo,
    ):
        """Create an UpdateRequestInfoRequest object from an Event.

        It uses the POD_NAME environment variable to detect the container it is
        running in.
        """
        tenant_info = self._to_tenant_info(auth_info)
        return request_insight_pb2.UpdateRequestInfoRequest(
            request_id=request_id,
            tenant_info=tenant_info,
            events=events,
        )


def new_event() -> request_insight_pb2.RequestEvent:
    """Creates a new request insight event."""
    event = request_insight_pb2.RequestEvent()

    now = datetime.datetime.utcnow()
    event.time.FromDatetime(now)
    event.event_id = str(uuid.uuid4())
    return event


def new_generic_event() -> request_insight_pb2.GenericEvent:
    """Creates a new generic event with time and event_id prefilled."""
    event = request_insight_pb2.GenericEvent()

    now = datetime.datetime.now()
    event.time.FromDatetime(now)
    event.event_id = str(uuid.uuid4())
    return event

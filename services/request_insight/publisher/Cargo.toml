[package]
name = "request_insight_publisher"
version = "0.1.0"
edition = "2021"

[lib]
name = "request_insight_publisher"
path = "request_insight_publisher.rs"

[dependencies]
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
tonic = { workspace = true }
tonic-build = {workspace = true}
async-trait = { workspace = true }
prost = {workspace = true}
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
google-cloud-pubsub = { workspace = true }
google-cloud-googleapis = { workspace = true }
tracing = { workspace = true }
metrics-server = { path = "../../../base/metrics_server/rust" }
request_context = { path = "../../lib/request_context" }
lazy_static = { workspace = true }
prometheus = { workspace = true }
uuid = { workspace = true }

[dev-dependencies]
tonic-build = {workspace = true}
prost-build = {workspace = true}
prost-wkt-build = {workspace = true}

[build-dependencies]
tonic-build = { workspace = true }

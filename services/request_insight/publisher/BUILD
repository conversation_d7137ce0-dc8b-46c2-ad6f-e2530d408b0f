load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "publisher_py",
    srcs = [
        "request_insight_publisher.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        requirement("dataclasses_json"),
        requirement("google-cloud-pubsub"),
        requirement("prometheus-client"),
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "publisher_py_test",
    srcs = ["request_insight_publisher_test.py"],
    deps = [
        ":publisher_py",
    ],
)

jsonnet_library(
    name = "publisher_lib",
    srcs = [
        "publisher_lib.jsonnet",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/gcp:gcp-lib",
    ],
)

go_library(
    name = "publisher_go",
    srcs = ["request_insight_publisher.go"],
    importpath = "github.com/augmentcode/augment/services/request_insight/publisher",
    visibility = [
        "//clients/beachhead/img:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_pubsub//:pubsub",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

rust_library(
    name = "publisher_rs",
    srcs = ["request_insight_publisher.rs"],
    aliases = aliases(),
    crate_name = "request_insight_publisher",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/metrics_server/rust:metrics_server",
        "//services/lib/request_context:request_context_rs",
    ],
)

rust_test(
    name = "publisher_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":publisher_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//base/blob_names:blob_names_proto",
        "//services/request_insight:request_insight_proto",
        "//services/share:share_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

"""Unit tests for the request insight publisher."""

from concurrent.futures import Future
from datetime import datetime
from unittest.mock import Magic<PERSON>ock

from prometheus_client import REGISTRY

import services.request_insight.request_insight_pb2 as request_insight_pb2
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
    RequestInsightPublisherConfig,
)


def test_publish_request_insight():
    base_publisher = MagicMock()
    auth_info = MagicMock(tenant_id="test-tenant-id", tenant_name="test-tenant-name")
    base_publisher.topic_path.return_value = "test"
    future = Future()
    base_publisher.publish.return_value = future

    cfg = RequestInsightPublisherConfig(project_id="test", topic_name="test")
    ri_publisher = RequestInsightPublisher(base_publisher, cfg)

    now = datetime.now()
    completion_resolution = request_insight_pb2.CompletionResolution(accepted_idx=1)
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_resolution.CopyFrom(completion_resolution)

    request = ri_publisher.update_request_info_request(
        "test-request", [event], auth_info=auth_info
    )

    before = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "request-insight", "status": "success"},
        )
        or 0
    )

    ri_publisher.publish_request_insight(request)
    after = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "request-insight", "status": "success"},
        )
        or 0
    )
    assert base_publisher.publish.call_count == 1
    assert after - before == 0

    # Check that the count goes up after the future returns without exception
    future.set_result("msg-id")
    after = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "request-insight", "status": "success"},
        )
        or 0
    )
    assert after - before == 1


def test_publish_user_events():
    base_publisher = MagicMock()
    base_publisher.topic_path.return_value = "test"
    future = Future()
    base_publisher.publish.return_value = future

    cfg = RequestInsightPublisherConfig(project_id="test", topic_name="test")
    publisher = RequestInsightPublisher(base_publisher, cfg)

    event = request_insight_pb2.FullExportUserEvent()
    now = datetime.utcnow()
    event.time.FromDatetime(now)
    event.completion_request_id_issued.request_id = str("test-request")
    request = request_insight_pb2.RecordFullExportUserEventsRequest()
    request.user_id = "test-user"
    request.session_id = "test-session"
    request.extension_data.user_events.append(event)

    before = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "user-events", "status": "success"},
        )
        or 0
    )

    publisher.publish_user_events(request)
    after = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "user-events", "status": "success"},
        )
        or 0
    )
    assert base_publisher.publish.call_count == 1
    assert after - before == 0

    # Check that the count goes up after the future returns without exception
    future.set_result("msg-id")
    after = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "user-events", "status": "success"},
        )
        or 0
    )
    assert after - before == 1


def test_publish_generic_events():
    base_publisher = MagicMock()
    base_publisher.topic_path.return_value = "test"
    future = Future()
    base_publisher.publish.return_value = future

    cfg = RequestInsightPublisherConfig(project_id="test", topic_name="test")
    publisher = RequestInsightPublisher(base_publisher, cfg)

    event = request_insight_pb2.GenericEvent()
    now = datetime.now()
    event.time.FromDatetime(now)
    event.event_id = "test-event-id"
    event.recaptcha.CopyFrom(
        request_insight_pb2.Recaptcha(
            email="test-email",
            assessment_name="test-assessment-name",
            assessment_reasons=["test-reason"],
            score=0.5,
            action="test-action",
            user_agent="test-user-agent",
            source_ip="test-ip-address",
        )
    )

    before = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "generic-events", "status": "success"},
        )
        or 0
    )

    publisher.publish_generic_events(
        session_id="test-session",
        events=[event],
    )
    after = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "generic-events", "status": "success"},
        )
        or 0
    )
    assert base_publisher.publish.call_count == 1
    assert after - before == 0

    # Check that the count goes up after the future returns without exception
    future.set_result("msg-id")
    after = (
        REGISTRY.get_sample_value(
            "au_request_insight_pubsub_publish_count_total",
            {"message_type": "generic-events", "status": "success"},
        )
        or 0
    )
    assert after - before == 1

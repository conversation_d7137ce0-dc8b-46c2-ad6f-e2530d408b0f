package main

import (
	"context"
	"errors"
	"testing"

	"github.com/augmentcode/augment/base/blob_names"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// CheckpointProcessorTestFixture is a test fixture for CheckpointProcessor tests
type CheckpointProcessorTestFixture struct {
	Env                 *TestEnvironment
	CheckpointProcessor *CheckpointProcessor
	T                   *testing.T
}

// NewCheckpointProcessorTestFixture creates a new CheckpointProcessorTestFixture
func NewCheckpointProcessorTestFixture(t *testing.T) *CheckpointProcessorTestFixture {
	// Create a test environment
	env := SetupTestEnvironment(t)

	// Create a real ContentManagerAdapter for the checkpoint processor
	tokenExchangeClient := NewMultiTokenMockClient("test")
	mockClient := NewMockContentManagerClient()
	contentManagerAdapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		5,
		3,
	)

	// Create a checkpoint processor
	checkpointProcessor := NewCheckpointProcessor(
		contentManagerAdapter,
		env.GCSClient,
		env.BlobCache,
		env.OutputPath,
	)

	// Return the fixture
	return &CheckpointProcessorTestFixture{
		Env:                 env,
		CheckpointProcessor: checkpointProcessor,
		T:                   t,
	}
}

// Cleanup cleans up the test fixture
func (f *CheckpointProcessorTestFixture) Cleanup() {
	f.Env.Cleanup()
}

// TestProcessCheckpointCached tests that ProcessCheckpoint skips processing when the checkpoint is in cache
func TestProcessCheckpointCached(t *testing.T) {
	fixture := NewCheckpointProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"

	// Add the checkpoint to the cache
	checkpointCacheKey := tenantID + ":checkpoint:" + checkpointID
	fixture.Env.BlobCache.Add(checkpointCacheKey)

	// Set up the mock client expectations - it should NOT be called since the checkpoint is cached
	mockClient := fixture.CheckpointProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Process the checkpoint
	err := fixture.CheckpointProcessor.ProcessCheckpoint(ctx, tenantID, checkpointID)
	assert.NoError(t, err)

	// Verify no objects in GCS
	VerifyNoObjectsInGCS(t, fixture.Env.GCSServer, "No objects should be written to GCS when checkpoint is in cache")

	// Verify expectations - the mock should not have been called
	mockClient.AssertExpectations(t)
}

// TestProcessCheckpointEmptyBlobList tests processing a checkpoint with no blobs
func TestProcessCheckpointEmptyBlobList(t *testing.T) {
	fixture := NewCheckpointProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"

	// Set up the mock client expectations
	mockClient := fixture.CheckpointProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Set up the mock to return an empty list
	mockClient.On("GetAllBlobsFromCheckpoint",
		mock.Anything,
		checkpointID,
		mock.Anything,
	).Return([]blob_names.BlobName{}, nil)

	// Process the checkpoint
	err := fixture.CheckpointProcessor.ProcessCheckpoint(ctx, tenantID, checkpointID)
	assert.NoError(t, err)

	// Verify the checkpoint was added to the cache
	checkpointCacheKey := tenantID + ":checkpoint:" + checkpointID
	assert.True(t, fixture.Env.BlobCache.Contains(checkpointCacheKey), "Checkpoint should be in cache")

	// Verify the checkpoint was written to GCS
	VerifyObjectCountInGCS(t, fixture.Env.GCSServer, 1, "Checkpoint should be written to GCS even with empty blob list")

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestProcessCheckpointContentManagerError tests handling of content manager errors
func TestProcessCheckpointContentManagerError(t *testing.T) {
	fixture := NewCheckpointProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"

	// Set up the mock client expectations
	mockClient := fixture.CheckpointProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Set up the mock to return an error
	mockClient.On("GetAllBlobsFromCheckpoint",
		mock.Anything,
		checkpointID,
		mock.Anything,
	).Return(nil, errors.New("content manager error"))

	// Process the checkpoint
	err := fixture.CheckpointProcessor.ProcessCheckpoint(ctx, tenantID, checkpointID)
	assert.Error(t, err)

	// Verify the checkpoint was not added to the cache
	checkpointCacheKey := tenantID + ":checkpoint:" + checkpointID
	assert.False(t, fixture.Env.BlobCache.Contains(checkpointCacheKey), "Checkpoint should not be in cache")

	// Verify no objects in GCS
	VerifyNoObjectsInGCS(t, fixture.Env.GCSServer, "No objects should be written to GCS when content manager returns an error")

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestProcessCheckpointSuccessful tests successful processing of an uncached checkpoint
func TestProcessCheckpointSuccessful(t *testing.T) {
	fixture := NewCheckpointProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"
	blobNames := []blob_names.BlobName{
		blob_names.BlobName("blob1"),
		blob_names.BlobName("blob2"),
		blob_names.BlobName("blob3"),
	}

	// Set up the mock client expectations
	mockClient := fixture.CheckpointProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Set up the mock to return blob names
	mockClient.On("GetAllBlobsFromCheckpoint",
		mock.Anything,
		checkpointID,
		mock.Anything,
	).Return(blobNames, nil)

	// Process the checkpoint
	err := fixture.CheckpointProcessor.ProcessCheckpoint(ctx, tenantID, checkpointID)
	assert.NoError(t, err)

	// Verify the checkpoint was added to the cache
	checkpointCacheKey := tenantID + ":checkpoint:" + checkpointID
	assert.True(t, fixture.Env.BlobCache.Contains(checkpointCacheKey), "Checkpoint should be in cache")

	// Verify the checkpoint was written to GCS
	VerifyObjectCountInGCS(t, fixture.Env.GCSServer, 1, "Checkpoint should be written to GCS")

	// Verify expectations
	mockClient.AssertExpectations(t)
}

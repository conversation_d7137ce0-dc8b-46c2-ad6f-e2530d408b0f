package main

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/rs/zerolog/log"
)

// MTLSConfig represents the configuration for mTLS.
type MTLSConfig struct {
	CertPath   string `json:"cert_path"`
	KeyPath    string `json:"key_path"`
	CaPath     string `json:"ca_path"`
	ServerName string `json:"server_name,omitempty"`
}

// Config represents the configuration for the blob exporter.
type Config struct {
	// Pubsub configuration
	ProjectId               string `json:"project_id"`
	SubscriptionId          string `json:"subscription_id"`
	SubscriptionDescription string `json:"subscription_description"`
	TopicName               string `json:"topic_name"`
	MaxConcurrentReceivers  int    `json:"max_concurrent_receivers"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// GCS configuration
	BucketName           string `json:"bucket_name"`
	BlobOutputPath       string `json:"blob_output_path"`
	CheckpointOutputPath string `json:"checkpoint_output_path"`

	// Content manager configuration
	ContentManagerEndpoint string      `json:"content_manager_endpoint"`
	TokenExchangeEndpoint  string      `json:"token_exchange_endpoint"`
	ClientMTLS             *MTLSConfig `json:"client_mtls,omitempty"`
	CentralClientMTLS      *MTLSConfig `json:"central_client_mtls,omitempty"`

	// Retry configuration
	MaxRetryAttempts int `json:"max_retry_attempts"` // Maximum number of retry attempts for operations

	// Concurrency and cache configuration
	MaxContentManagerRequests int `json:"max_content_manager_requests"` // Maximum number of concurrent content manager requests
	MaxGCSRequests            int `json:"max_gcs_requests"`             // Maximum number of concurrent GCS requests
	BlobCacheSize             int `json:"blob_cache_size"`              // Size of the blob cache
	CheckpointCacheSize       int `json:"checkpoint_cache_size"`        // Size of the checkpoint cache
}

// loadConfig loads the configuration from a file.
func loadConfig(configFile string) (*Config, error) {
	if configFile == "" {
		return nil, fmt.Errorf("missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open config file: %w", err)
	}
	defer f.Close()

	var config Config
	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	log.Info().
		Str("project_id", config.ProjectId).
		Str("subscription_id", config.SubscriptionId).
		Msg("Loaded configuration")
	return &config, nil
}

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    // Setup vendor buckets.
    // The corresponding kubecfg jsonnet target filters out namespaces without the flag
    // `.sharedVendorBucket.enabled`.
    {
      name: 'vendor-buckets',
      kubecfg: {
        target: '//services/request_insight/blob_exporter:vendor_buckets',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'request-insight-blob-exporter-central-bucket',
      priority: 100,  // We want buckets to be created/modified before request-insight deploys.
      kubecfg: {
        target: '//services/request_insight/blob_exporter:blob_exporter_central_bucket',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline', 'cam'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'request-insight-blob-exporter',
      kubecfg: {
        target: '//services/request_insight/blob_exporter:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline', 'cam'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

package main

import (
	"context"
	"errors"
	"testing"

	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	pb "github.com/augmentcode/augment/services/content_manager/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// BlobProcessorTestFixture is a test fixture for BlobProcessor tests
type BlobProcessorTestFixture struct {
	Env           *TestEnvironment
	BlobProcessor *BlobProcessor
	T             *testing.T
}

// NewBlobProcessorTestFixture creates a new BlobProcessorTestFixture
func NewBlobProcessorTestFixture(t *testing.T) *BlobProcessorTestFixture {
	// Create a test environment
	env := SetupTestEnvironment(t)

	// Create a real ContentManagerAdapter for the blob processor
	tokenExchangeClient := NewMultiTokenMockClient("test")
	mockClient := NewMockContentManagerClient()
	contentManagerAdapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		5,
		3,
	)

	// Create a blob processor
	blobProcessor := NewBlobProcessor(
		contentManagerAdapter,
		env.GCSClient,
		env.BlobCache,
		env.OutputPath,
	)

	// Return the fixture
	return &BlobProcessorTestFixture{
		Env:           env,
		BlobProcessor: blobProcessor,
		T:             t,
	}
}

// Cleanup cleans up the test fixture
func (f *BlobProcessorTestFixture) Cleanup() {
	f.Env.Cleanup()
}

// TestProcessBlobsEmptyList tests that ProcessBlobs returns nil when given an empty list
func TestProcessBlobsEmptyList(t *testing.T) {
	fixture := NewBlobProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{}

	// Process the empty list
	err := fixture.BlobProcessor.ProcessBlobs(ctx, tenantID, blobNames)
	assert.NoError(t, err)

	// Verify no objects in GCS
	VerifyNoObjectsInGCS(t, fixture.Env.GCSServer, "No objects should be written to GCS for empty list")
}

// TestProcessBlobsAllInCache tests that ProcessBlobs skips processing when all blobs are in cache
func TestProcessBlobsAllInCache(t *testing.T) {
	fixture := NewBlobProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}

	// Add all blobs to the cache
	for _, blobName := range blobNames {
		cacheKey := tenantID + ":" + blobName
		fixture.Env.BlobCache.Add(cacheKey)
	}

	// Process the blobs
	err := fixture.BlobProcessor.ProcessBlobs(ctx, tenantID, blobNames)
	assert.NoError(t, err)

	// Verify no objects in GCS
	VerifyNoObjectsInGCS(t, fixture.Env.GCSServer, "No objects should be written to GCS when all blobs are in cache")
}

// TestProcessBlobsContentManagerError tests handling of content manager errors
func TestProcessBlobsContentManagerError(t *testing.T) {
	fixture := NewBlobProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}

	// Set up the mock client expectations
	mockClient := fixture.BlobProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Set up the mock to return an error
	mockClient.On("BatchDownloadContent",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		tenantID,
	).Return(nil, errors.New("content manager error"))

	// Process the blobs
	err := fixture.BlobProcessor.ProcessBlobs(ctx, tenantID, blobNames)
	assert.Error(t, err)

	// Verify no blobs were added to the cache
	for _, blobName := range blobNames {
		cacheKey := tenantID + ":" + blobName
		assert.False(t, fixture.Env.BlobCache.Contains(cacheKey), "Blob should not be in cache: "+blobName)
	}

	// Verify no objects in GCS
	VerifyNoObjectsInGCS(t, fixture.Env.GCSServer, "No objects should be written to GCS when content manager returns an error")

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestProcessBlobsDownloadError tests handling of blob download errors
func TestProcessBlobsDownloadError(t *testing.T) {
	fixture := NewBlobProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}

	// Set up the mock client expectations
	mockClient := fixture.BlobProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Create a channel to return results with errors
	resultChan := make(chan contentmanagerclient.BatchDownloadContentResult, len(blobNames))

	// Set up the mock to return the channel
	mockClient.On("BatchDownloadContent",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		tenantID,
	).Return(resultChan, nil)

	// Start a goroutine to send error results
	go func() {
		defer close(resultChan)

		// Send error results for each blob
		for _, blobName := range blobNames {
			// Create an error response
			resultChan <- contentmanagerclient.BatchDownloadContentResult{
				Resp: nil,
				Err:  errors.New("download error for " + blobName),
			}
		}
	}()

	// Process the blobs
	err := fixture.BlobProcessor.ProcessBlobs(ctx, tenantID, blobNames)
	assert.NoError(t, err) // The overall operation should succeed even if individual blobs fail

	// Verify no blobs were added to the cache
	for _, blobName := range blobNames {
		cacheKey := tenantID + ":" + blobName
		assert.False(t, fixture.Env.BlobCache.Contains(cacheKey), "Blob should not be in cache: "+blobName)
	}

	// No blobs should be written to GCS when there are download errors
	VerifyNoObjectsInGCS(t, fixture.Env.GCSServer, "No objects should be written to GCS when there are download errors")

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestProcessBlobsNilBlobInfo tests handling of nil blob info
func TestProcessBlobsNilBlobInfo(t *testing.T) {
	fixture := NewBlobProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}

	// Set up the mock client expectations
	mockClient := fixture.BlobProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Create a channel to return results with nil responses
	resultChan := make(chan contentmanagerclient.BatchDownloadContentResult, len(blobNames))

	// Set up the mock to return the channel
	mockClient.On("BatchDownloadContent",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		tenantID,
	).Return(resultChan, nil)

	// Start a goroutine to send nil results
	go func() {
		defer close(resultChan)

		// Send nil results for each blob
		for range blobNames {
			// Create a nil response
			resultChan <- contentmanagerclient.BatchDownloadContentResult{
				Resp: nil,
				Err:  nil,
			}
		}
	}()

	// Process the blobs
	err := fixture.BlobProcessor.ProcessBlobs(ctx, tenantID, blobNames)
	assert.NoError(t, err) // The overall operation should succeed even if individual blobs fail

	// Verify no blobs were added to the cache
	for _, blobName := range blobNames {
		cacheKey := tenantID + ":" + blobName
		assert.False(t, fixture.Env.BlobCache.Contains(cacheKey), "Blob should not be in cache: "+blobName)
	}

	// No blobs should be written to GCS when blob info is nil
	VerifyNoObjectsInGCS(t, fixture.Env.GCSServer, "No objects should be written to GCS when blob info is nil")

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestProcessBlobsSuccessful tests successful processing of uncached blobs
func TestProcessBlobsSuccessful(t *testing.T) {
	fixture := NewBlobProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}

	// Expected content for each blob
	expectedContents := make(map[string][]byte)

	// Set up the mock client expectations
	mockClient := fixture.BlobProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Create a channel to return results
	resultChan := make(chan contentmanagerclient.BatchDownloadContentResult, len(blobNames))

	// Set up the mock to return the channel
	mockClient.On("BatchDownloadContent",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		tenantID,
	).Return(resultChan, nil)

	// Start a goroutine to send successful results
	go func() {
		defer close(resultChan)

		// Send a successful result for each blob
		for _, blobName := range blobNames {
			content := []byte("test content for " + blobName)
			expectedContents[blobName] = content

			// Create metadata
			metadata := []*pb.BlobMetadata{
				{Key: "test-key", Value: "test-value"},
			}

			// Create a successful response with FinalContent
			finalContent := &pb.BatchGetContentFinalContent{
				Content:           content,
				FinalHash:         "hash-" + blobName,
				Metadata:          metadata,
				BlobName:          blobName,
				TransformationKey: "",
				SubKey:            "",
			}

			resp := &pb.BatchGetContentResponse{
				Response: &pb.BatchGetContentResponse_FinalContent{
					FinalContent: finalContent,
				},
			}

			// Send the result
			resultChan <- contentmanagerclient.BatchDownloadContentResult{
				Resp: resp,
				Err:  nil,
			}
		}
	}()

	// Process the blobs
	err := fixture.BlobProcessor.ProcessBlobs(ctx, tenantID, blobNames)
	assert.NoError(t, err)

	// Verify all blobs were added to the cache
	for _, blobName := range blobNames {
		cacheKey := tenantID + ":" + blobName
		assert.True(t, fixture.Env.BlobCache.Contains(cacheKey), "Blob should be in cache: "+blobName)
	}

	// Verify all blobs were written to GCS with the correct content
	for _, blobName := range blobNames {
		// The blob path in GCS should be: tenantID/test-output-path/blobName
		// This matches the path.Join(tenantID, p.outputPath, blobName) in the BlobProcessor
		blobPath := tenantID + "/test-output-path/" + blobName
		expectedContent := expectedContents[blobName]

		// Verify the blob exists in GCS with the expected content
		VerifyBlobInGCS(t, fixture.Env.GCSServer, blobPath, expectedContent)
	}

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestProcessBlobsMixedResults tests a mix of successful and failed blob processing
func TestProcessBlobsMixedResults(t *testing.T) {
	fixture := NewBlobProcessorTestFixture(t)
	defer fixture.Cleanup()

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3", "blob4"}

	// Add one blob to the cache
	cacheKey := tenantID + ":" + blobNames[0]
	fixture.Env.BlobCache.Add(cacheKey)

	// Expected content for the successful blob
	expectedContent := []byte("test content for " + blobNames[1])

	// Set up the mock client expectations
	mockClient := fixture.BlobProcessor.contentManagerClient.client.(*MockContentManagerClient)

	// Create a channel to return results with mixed outcomes
	resultChan := make(chan contentmanagerclient.BatchDownloadContentResult, len(blobNames)-1) // -1 because one is cached

	// Set up the mock to return the channel
	mockClient.On("BatchDownloadContent",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		tenantID,
	).Return(resultChan, nil)

	// Start a goroutine to send mixed results
	go func() {
		defer close(resultChan)

		// Send a successful result for the first uncached blob
		// Create metadata
		metadata := []*pb.BlobMetadata{
			{Key: "test-key", Value: "test-value"},
		}

		// Create a successful response with FinalContent
		finalContent := &pb.BatchGetContentFinalContent{
			Content:           expectedContent,
			FinalHash:         "hash-" + blobNames[1],
			Metadata:          metadata,
			BlobName:          blobNames[1],
			TransformationKey: "",
			SubKey:            "",
		}

		resp := &pb.BatchGetContentResponse{
			Response: &pb.BatchGetContentResponse_FinalContent{
				FinalContent: finalContent,
			},
		}

		// Send the successful result
		resultChan <- contentmanagerclient.BatchDownloadContentResult{
			Resp: resp,
			Err:  nil,
		}

		// Send an error result for the second uncached blob
		resultChan <- contentmanagerclient.BatchDownloadContentResult{
			Resp: nil,
			Err:  errors.New("download error for " + blobNames[2]),
		}

		// Send a nil result for the third uncached blob
		resultChan <- contentmanagerclient.BatchDownloadContentResult{
			Resp: nil,
			Err:  nil,
		}
	}()

	// Process the blobs
	err := fixture.BlobProcessor.ProcessBlobs(ctx, tenantID, blobNames)
	assert.NoError(t, err)

	// Verify the cache state
	assert.True(t, fixture.Env.BlobCache.Contains(tenantID+":"+blobNames[0]), "Already cached blob should remain in cache")
	assert.True(t, fixture.Env.BlobCache.Contains(tenantID+":"+blobNames[1]), "Successfully processed blob should be in cache")
	assert.False(t, fixture.Env.BlobCache.Contains(tenantID+":"+blobNames[2]), "Failed blob should not be in cache")
	assert.False(t, fixture.Env.BlobCache.Contains(tenantID+":"+blobNames[3]), "Nil blob should not be in cache")

	// Verify only the successful blob was written to GCS
	// The blob path in GCS should be: tenantID/test-output-path/blobName
	// This matches the path.Join(tenantID, p.outputPath, blobName) in the BlobProcessor
	blobPath := tenantID + "/test-output-path/" + blobNames[1]

	// Verify the blob exists in GCS with the expected content
	VerifyBlobInGCS(t, fixture.Env.GCSServer, blobPath, expectedContent)

	// Verify exactly one object in GCS
	VerifyObjectCountInGCS(t, fixture.Env.GCSServer, 1, "Only one blob should be written to GCS in mixed results test")

	// Verify expectations
	mockClient.AssertExpectations(t)
}

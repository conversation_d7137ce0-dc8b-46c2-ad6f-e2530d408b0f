"""Build rules for exporting blobs from request insight to GCS."""

load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")

kubecfg_library(
    name = "deploy_lib",
    srcs = ["deploy_lib.jsonnet"],
)

kubecfg(
    name = "blob_exporter_central_bucket",
    src = "blob_exporter_central_bucket.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        ":deploy_lib",
        "//services/request_insight:external_service_accounts_lib",
    ],
)

kubecfg(
    name = "vendor_buckets",
    src = "vendor_buckets.jsonnet",
    visibility = [
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        "//deploy/common:eng-lib",
        "//services/request_insight:external_service_accounts_lib",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":blob_exporter_image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        ":deploy_lib",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":blob_exporter_central_bucket",
        ":kubecfg",
        ":vendor_buckets",
        "//deploy/tenants:namespaces",
    ],
)

go_library(
    name = "blob_exporter_lib",
    srcs = [
        "blob_cache.go",
        "blob_processor.go",
        "checkpoint_processor.go",
        "config.go",
        "content_manager_adapter.go",
        "gcs.go",
        "interfaces.go",
        "main.go",
        "message_processor.go",
        "types.go",
    ],
    importpath = "github.com/augmentcode/augment/services/request_insight/blob_exporter",
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/lib:request_insight_subscriber_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_hashicorp_golang_lru_v2//:golang-lru",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_api//googleapi",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "blob_exporter",
    embed = [":blob_exporter_lib"],
)

go_oci_image(
    name = "blob_exporter_image",
    package_name = package_name(),
    binary = ":blob_exporter",
)

go_test(
    name = "blob_exporter_tests",
    srcs = [
        "blob_cache_test.go",
        "blob_processor_test.go",
        "checkpoint_processor_test.go",
        "content_manager_adapter_test.go",
        "gcs_test.go",
        "message_processor_test.go",
        "test_fixtures.go",
    ],
    embed = [":blob_exporter_lib"],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/content_manager:content_manager_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_fsouza_fake_gcs_server//fakestorage",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/testutil",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
    ],
)

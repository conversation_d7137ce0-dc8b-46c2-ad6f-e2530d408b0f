package main

import (
	"context"
	"errors"
	"fmt"
	"sync/atomic"
	"testing"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobs_pb "github.com/augmentcode/augment/base/blob_names/proto"
	"github.com/augmentcode/augment/base/go/secretstring"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	contentmanagerpb "github.com/augmentcode/augment/services/content_manager/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockContentManagerClient mocks the ContentManagerClient interface
type MockContentManagerClient struct {
	mock.Mock
}

// Export the MockContentManagerClient for use in other test files
func NewMockContentManagerClient() *MockContentManagerClient {
	return new(MockContentManagerClient)
}

// BatchDownloadContent mocks the BatchDownloadContent method
func (m *MockContentManagerClient) BatchDownloadContent(
	ctx context.Context,
	keys []*contentmanagerpb.BlobContentKey,
	requestContext *requestcontext.RequestContext,
	tenantID string,
) (<-chan contentmanagerclient.BatchDownloadContentResult, error) {
	args := m.Called(ctx, keys, requestContext, tenantID)

	// Handle the case where the first argument is a channel
	if args.Get(0) != nil {
		// Convert the channel to the correct type
		if ch, ok := args.Get(0).(chan contentmanagerclient.BatchDownloadContentResult); ok {
			// Create a new channel with the correct type
			resultChan := make(chan contentmanagerclient.BatchDownloadContentResult)

			// Start a goroutine to copy values from the input channel to the result channel
			go func() {
				defer close(resultChan)
				for result := range ch {
					resultChan <- result
				}
			}()

			return resultChan, args.Error(1)
		}
	}

	// Return nil if the first argument is nil
	return nil, args.Error(1)
}

// GetAllBlobsFromCheckpoint mocks the GetAllBlobsFromCheckpoint method
func (m *MockContentManagerClient) GetAllBlobsFromCheckpoint(
	ctx context.Context,
	checkpointID string,
	requestContext *requestcontext.RequestContext,
) ([]blob_names.BlobName, error) {
	args := m.Called(ctx, checkpointID, requestContext)
	// Handle nil return value
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]blob_names.BlobName), args.Error(1)
}

// Close mocks the Close method
func (m *MockContentManagerClient) Close() {
	m.Called()
}

// Implement other methods of the ContentManagerClient interface with empty implementations
// These are not used in our tests but are required to satisfy the interface

func (m *MockContentManagerClient) UploadBlobContent(ctx context.Context, content []byte, path string, requestContext *requestcontext.RequestContext) (string, bool, error) {
	return "", false, errors.New("not implemented")
}

func (m *MockContentManagerClient) BatchUploadBlobContent(ctx context.Context, blobs []*contentmanagerpb.UploadBlobContent, priority contentmanagerpb.IndexingPriority, requestContext *requestcontext.RequestContext) ([]*contentmanagerpb.UploadBlobResult, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) FindMissingBlobs(ctx context.Context, blobNames []blob_names.BlobName, transformationKey string, subKey string, requestContext *requestcontext.RequestContext) ([]string, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) CheckpointBlobs(ctx context.Context, blobs *blobs_pb.Blobs, requestContext *requestcontext.RequestContext) (string, error) {
	return "", errors.New("not implemented")
}

func (m *MockContentManagerClient) GetBlobInfo(ctx context.Context, blobName blob_names.BlobName, requestContext *requestcontext.RequestContext) (*contentmanagerpb.GetBlobInfoResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) BatchGetBlobInfo(ctx context.Context, blobNames []blob_names.BlobName, tenantID string, requestContext *requestcontext.RequestContext) (*contentmanagerpb.BatchGetBlobInfoResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) GetBestAnnIndex(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, requestContext *requestcontext.RequestContext) (*contentmanagerclient.GetBestAnnIndexResult, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) GetAnnIndexBlobInfos(ctx context.Context, key contentmanagerclient.AnnIndexKey, requestContext requestcontext.RequestContext) (*contentmanagerclient.GetAnnIndexBlobInfosResult, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) GetAnnIndexAsset(ctx context.Context, tenantId *string, transformationKey string, indexId string, subKey string, requestContext requestcontext.RequestContext) ([]byte, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) AddAnnIndexMapping(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, indexId string, addedBlobs []blob_names.BlobName, removedBlobs []blob_names.BlobName, requestContext requestcontext.RequestContext) (*contentmanagerpb.AddAnnIndexMappingResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) UploadAnnIndexBlobInfos(ctx context.Context, tenantId *string, transformationKey string, indexId string, infos []contentmanagerclient.AnnIndexBlobInfo, requestContext requestcontext.RequestContext) (*contentmanagerpb.UploadAnnIndexBlobInfosResponse, error) {
	return nil, errors.New("not implemented")
}

func (m *MockContentManagerClient) UploadAnnIndexAssets(ctx context.Context, tenantId *string, transformationKey string, indexId string, assets []contentmanagerclient.AnnIndexAssetInput, requestContext requestcontext.RequestContext) (*contentmanagerpb.UploadAnnIndexAssetsResponse, error) {
	return nil, errors.New("not implemented")
}

// MultiTokenMockClient extends the MockTokenExchangeClient to return different tokens
type MultiTokenMockClient struct {
	*tokenexchangeclient.MockTokenExchangeClient
	tokenCounter int32
	tokenPrefix  string
}

// NewMultiTokenMockClient creates a new MultiTokenMockClient
func NewMultiTokenMockClient(prefix string) *MultiTokenMockClient {
	return &MultiTokenMockClient{
		MockTokenExchangeClient: tokenexchangeclient.NewMockTokenExchangeClient("", ""),
		tokenPrefix:             prefix,
	}
}

// GetSignedTokenForService returns a different token each time it's called
func (c *MultiTokenMockClient) GetSignedTokenForService(
	ctx context.Context,
	tenantID string,
	scopes []tokenexchangepb.Scope,
) (secretstring.SecretString, error) {
	counter := atomic.AddInt32(&c.tokenCounter, 1)
	return secretstring.New(fmt.Sprintf("%s-token-%d", c.tokenPrefix, counter)), nil
}

// TestNewContentManagerAdapter tests the constructor for ContentManagerAdapter
func TestNewContentManagerAdapter(t *testing.T) {
	// Create mocks
	mockClient := new(MockContentManagerClient)
	tokenExchangeClient := NewMultiTokenMockClient("test")

	// Set up expectations for Close
	mockClient.On("Close").Return()

	// Create adapter
	maxConcurrentRequests := 5
	maxRetryAttempts := 3
	adapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		maxConcurrentRequests,
		maxRetryAttempts,
	)

	// Verify the adapter was created correctly
	assert.NotNil(t, adapter)
	assert.Equal(t, mockClient, adapter.client)
	assert.Equal(t, tokenExchangeClient, adapter.tokenExchangeClient)
	assert.Equal(t, maxConcurrentRequests, cap(adapter.throttleChan))
	assert.Equal(t, maxRetryAttempts, adapter.maxRetryAttempts)
	assert.NotEmpty(t, adapter.sessionID)
	assert.NotNil(t, adapter.tokens)

	// Clean up
	adapter.Close()

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestClose tests the Close method
func TestClose(t *testing.T) {
	// Create mocks
	mockClient := new(MockContentManagerClient)
	tokenExchangeClient := NewMultiTokenMockClient("test")

	// Set up expectations
	mockClient.On("Close").Return()

	// Create adapter
	adapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		5,
		3,
	)

	// Call Close
	err := adapter.Close()

	// Verify the result
	assert.NoError(t, err)

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestDownloadBlobsEmptyList tests the DownloadBlobs method with an empty list
func TestDownloadBlobsEmptyList(t *testing.T) {
	// Create mocks
	mockClient := new(MockContentManagerClient)
	tokenExchangeClient := NewMultiTokenMockClient("test")

	// Set up expectations for Close
	mockClient.On("Close").Return()

	// Create adapter
	adapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		5,
		3,
	)

	// Call DownloadBlobs with an empty list
	ctx := context.Background()
	dummyRC := &requestcontext.RequestContext{}
	blobResultChan, err := adapter.DownloadBlobs(ctx, []ContentKey{}, dummyRC, "test-tenant")

	// Verify the result
	assert.NoError(t, err)
	assert.NotNil(t, blobResultChan)

	// Verify no results are returned
	count := 0
	for range blobResultChan {
		count++
	}
	assert.Equal(t, 0, count)

	// Clean up
	adapter.Close()

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestDownloadBlobsSuccessful tests downloading a regular list of blobs
func TestDownloadBlobsSuccessful(t *testing.T) {
	// Create mocks
	mockClient := new(MockContentManagerClient)
	tokenExchangeClient := NewMultiTokenMockClient("test")

	// Create test data
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}
	contentKeys := make([]ContentKey, len(blobNames))
	for i, blobName := range blobNames {
		contentKeys[i] = ContentKey{
			BlobName: blobName,
		}
	}

	// Create expected results
	expectedContents := make(map[string][]byte)
	for _, blobName := range blobNames {
		expectedContents[blobName] = []byte("test content for " + blobName)
	}

	// Create a channel to return results
	resultChan := make(chan contentmanagerclient.BatchDownloadContentResult, len(blobNames))

	// Set up the mock to return the channel
	mockClient.On("BatchDownloadContent",
		mock.Anything,
		mock.Anything,
		mock.Anything,
		tenantID,
	).Return(resultChan, nil)

	// Set up expectations for Close
	mockClient.On("Close").Return()

	// Create adapter
	adapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		5,
		3,
	)

	// Call DownloadBlobs
	ctx := context.Background()
	dummyRC := &requestcontext.RequestContext{}
	blobResultChan, err := adapter.DownloadBlobs(ctx, contentKeys, dummyRC, tenantID)

	// Verify the result
	assert.NoError(t, err)
	assert.NotNil(t, blobResultChan)

	// Start a goroutine to send successful results
	go func() {
		defer close(resultChan)

		// Send a successful result for each blob
		for _, blobName := range blobNames {
			content := expectedContents[blobName]

			// Create metadata
			metadata := []*contentmanagerpb.BlobMetadata{
				{Key: "test-key", Value: "test-value"},
			}

			// Create a successful response with FinalContent
			finalContent := &contentmanagerpb.BatchGetContentFinalContent{
				Content:           content,
				FinalHash:         "hash-" + blobName,
				Metadata:          metadata,
				BlobName:          blobName,
				TransformationKey: "",
				SubKey:            "",
			}

			resp := &contentmanagerpb.BatchGetContentResponse{
				Response: &contentmanagerpb.BatchGetContentResponse_FinalContent{
					FinalContent: finalContent,
				},
			}

			// Send the result
			resultChan <- contentmanagerclient.BatchDownloadContentResult{
				Resp: resp,
				Err:  nil,
			}
		}
	}()

	// Collect the results
	results := make(map[string]*BlobInfo)
	for result := range blobResultChan {
		assert.NoError(t, result.Error)
		assert.NotNil(t, result.BlobInfo)
		results[result.BlobInfo.BlobName] = result.BlobInfo
	}

	// Verify the results
	assert.Equal(t, len(blobNames), len(results))
	for _, blobName := range blobNames {
		blobInfo, ok := results[blobName]
		assert.True(t, ok, "Missing result for blob: "+blobName)
		assert.Equal(t, blobName, blobInfo.BlobName)
		assert.Equal(t, expectedContents[blobName], blobInfo.Content)
		assert.Equal(t, map[string]string{"test-key": "test-value"}, blobInfo.Metadata)
	}

	// Clean up
	adapter.Close()

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestGetAllBlobsFromCheckpoint tests the GetAllBlobsFromCheckpoint method
func TestGetAllBlobsFromCheckpoint(t *testing.T) {
	// Create mocks
	mockClient := new(MockContentManagerClient)

	// Create test data
	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"
	expectedBlobNames := []blob_names.BlobName{
		blob_names.BlobName("blob1"),
		blob_names.BlobName("blob2"),
	}

	// Set up content manager client to return blob names
	mockClient.On("GetAllBlobsFromCheckpoint",
		mock.Anything,
		checkpointID,
		mock.MatchedBy(func(rc *requestcontext.RequestContext) bool {
			// Any request context is fine for this test
			return true
		}),
	).Return(expectedBlobNames, nil)

	// Set up expectations for Close
	mockClient.On("Close").Return()

	// Create adapter
	tokenExchangeClient := NewMultiTokenMockClient("test")
	adapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		5,
		3,
	)

	// Call GetAllBlobsFromCheckpoint with a dummy request context
	// The adapter should create a real request context internally
	ctx := context.Background()
	dummyRC := &requestcontext.RequestContext{}
	blobNames, err := adapter.GetAllBlobsFromCheckpoint(ctx, checkpointID, dummyRC, tenantID)

	// Verify the result
	assert.NoError(t, err)
	assert.Equal(t, 2, len(blobNames))
	assert.Equal(t, "blob1", blobNames[0])
	assert.Equal(t, "blob2", blobNames[1])

	// Clean up
	adapter.Close()

	// Verify expectations
	mockClient.AssertExpectations(t)
}

// TestGetAllBlobsFromCheckpointError tests error handling in GetAllBlobsFromCheckpoint
func TestGetAllBlobsFromCheckpointError(t *testing.T) {
	// Create mocks
	mockClient := new(MockContentManagerClient)
	tokenExchangeClient := NewMultiTokenMockClient("test")

	// Create test data
	tenantID := "test-tenant"
	checkpointID := "test-checkpoint"
	expectedError := errors.New("test error")

	// Set up content manager client to return an error
	mockClient.On("GetAllBlobsFromCheckpoint",
		mock.Anything,
		checkpointID,
		mock.MatchedBy(func(rc *requestcontext.RequestContext) bool {
			// Any request context is fine for this test
			return true
		}),
	).Return(nil, expectedError)

	// Set up expectations for Close
	mockClient.On("Close").Return()

	// Create adapter
	adapter := NewContentManagerAdapter(
		mockClient,
		tokenExchangeClient,
		5,
		3,
	)

	// Call GetAllBlobsFromCheckpoint with a dummy request context
	// The adapter should create a real request context internally
	ctx := context.Background()
	dummyRC := &requestcontext.RequestContext{}
	blobNames, err := adapter.GetAllBlobsFromCheckpoint(ctx, checkpointID, dummyRC, tenantID)

	// Verify the result
	assert.Error(t, err)
	assert.Nil(t, blobNames)
	assert.Equal(t, expectedError, errors.Unwrap(err))

	// Clean up
	adapter.Close()

	// Verify expectations
	mockClient.AssertExpectations(t)
}

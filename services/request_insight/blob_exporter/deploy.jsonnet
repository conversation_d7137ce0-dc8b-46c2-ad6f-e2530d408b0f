// K8S deployment file for the request insight blob exporter subscriber
// Overall this deployment does the following:
//  1. Create a service account.
//  2. Give it access to
//    2a. Subscribe to the Request Insight pub/sub topic
//    2b. Read (to list existing blobs) and write blobs to the GCS bucket.
//  3. Deploy service.
//
//


local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
local exportLib = import 'services/request_insight/blob_exporter/deploy_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  if !namespace_config.flags.exportFullData then [] else
    local projectId = cloudInfo[cloud].projectId;
    local appName = 'ri-blob-exporter';
    local shortAppName = 'ri-blob-exp';

    local clientCert = certLib.createClientCert(name='%s-client-cert' % appName,
                                                namespace=namespace,
                                                appName=appName,
                                                volumeName='client-certs');
    local centralClientCert = certLib.createCentralClientCert(
      name='%s-central-client-cert' % appName,
      namespace=namespace,
      env=env,
      appName=appName,
      volumeName='central-client-certs',
      dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
    );
    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local tokenExchangeEndpoint = endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace);

    // Service configuration
    local config = {
      // Pub/sub configuration
      project_id: projectId,
      topic_name: '%s-request-insight-topic' % namespace,
      subscription_id: '%s-%s-sub' % [namespace, appName],
      subscription_description: 'ri-blob-exporter',
      max_concurrent_receivers: 20,
      // Prometheus configuration
      prom_port: 9090,
      // GCS configuration
      bucket_name: exportLib.centralBucketName(namespace, env),
      blob_output_path: 'blobs',
      checkpoint_output_path: 'checkpoints',
      // Content manager configuration
      content_manager_endpoint: 'content-manager-svc:50051',
      token_exchange_endpoint: tokenExchangeEndpoint,
      client_mtls: if mtls then clientCert.config else null,
      central_client_mtls: if mtls then centralClientCert.config else null,
      // Retry configuration
      max_retry_attempts: 2,
      // Concurrency and cache configuration
      max_content_manager_requests: 10,  // Maximum number of concurrent content manager requests
      max_gcs_requests: 500,  // Maximum number of concurrent GCS requests
      blob_cache_size: 50000,
      checkpoint_cache_size: 10000,
    };

    local serviceAccount = gcpLib.createServiceAccount(
      appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
    );

    local gcpObjects = [
      // Pub/sub subscription.
      {
        apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
        kind: 'PubSubSubscription',
        metadata: {
          name: config.subscription_id,
          namespace: namespace,
          // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
          // topic is deleted before the pod.
          annotations: if env == 'DEV' then {
            'cnrm.cloud.google.com/deletion-policy': 'abandon',
          } else {},
          labels: {
            app: appName,
          },
        },
        spec: {
          topicRef: {
            name: config.topic_name,
          },
          ackDeadlineSeconds: 60,
          retryPolicy: {
            minimumBackoff: '5s',
            maximumBackoff: '300s',
          },
          // Retain messages for 1 hour in dev and 7 days in staging/prod.
          messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
          retainAckedMessages: false,
        },
      },
      // Give access to the subscription. (2a)
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s-subscription-policy' % appName,
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          resourceRef: {
            kind: 'PubSubSubscription',
            name: config.subscription_id,
          },
          bindings: [
            {
              role: 'roles/pubsub.subscriber',
              members: [{ member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress }],
            },
          ],
        },
      },
      // Give access to the CloudStorage bucket. (2b)
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s-central-bucket-policy' % appName,
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: exportLib.centralBucketName(namespace, env),
          },
          bindings: [
            {
              role: 'roles/storage.objectUser',
              members: [
                { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
              ],
            },
          ],
        },
      },
    ];

    local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

    local container = {
      name: appName,
      target: {
        name: '//services/request_insight/blob_exporter:blob_exporter_image',
        dst: 'request_insight_blob_exporter',
      },
      env: [
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        centralClientCert.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 4,
          memory: '16Gi',
        },
      },
      args: [
        // config file uses the default
        '--health-file=/tmp/health',
      ],
      readinessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        initialDelaySeconds: 60,
        periodSeconds: 10,
      },
      livenessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        initialDelaySeconds: 60,
        periodSeconds: 20,
      },
    };
    local pod = {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        centralClientCert.podVolumeDef,
      ],
    };

    local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
    // we allow multiple exporters on the same host as they run in the background
    local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

    local minReplicas = {
      DEV: 1,
      STAGING: 1,
      PROD: 1,
    };
    local maxReplicas = {
      DEV: 4,
      STAGING: 4,
      PROD: 4,
    };

    local deployment = {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        minReadySeconds: 0,
        replicas: minReplicas[env],
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod + {
            tolerations: tolerations,
            affinity: affinity,
          },
        },
      },
    };

    // Scale when the subscription backlog gets too large.
    local scaledObject = {
      apiVersion: 'keda.sh/v1alpha1',
      kind: 'ScaledObject',
      metadata: {
        name: '%s-scaledobject' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        scaleTargetRef: {
          apiVersion: 'apps/v1',
          kind: 'Deployment',
          name: appName,
        },
        minReplicaCount: minReplicas[env],
        maxReplicaCount: maxReplicas[env],
        triggers: [
          {
            type: 'prometheus',
            metadata: {
              serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
              metricName: 'pubsub_googleapis_com:subscription_num_undelivered_messages',
              threshold: '500',
              query: 'sum(avg_over_time(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id="%s"}[1m]))' % config.subscription_id,
            },
          },
        ],
      },
    };

    lib.flatten([
      configMap.objects,
      clientCert.objects,
      centralClientCert.objects,
      serviceAccount.objects,
      gcpObjects,
      deployment,
      scaledObject,
    ])

package main

import (
	lru "github.com/hashicorp/golang-lru/v2"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
)

// Cache metrics track cache operations and performance
var (
	// CacheAccessTotal tracks the number of cache accesses (hits and misses)
	CacheAccessTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_blob_exporter_cache_access_total",
			Help: "Total number of cache accesses, partitioned by cache name and result (hit/miss).",
		},
		[]string{"cache_name", "result"},
	)

	// CacheSize tracks the current number of items in the cache
	CacheSize = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_blob_exporter_cache_size_items",
			Help: "Current number of items in the cache, partitioned by cache name.",
		},
		[]string{"cache_name"},
	)
)

// LRUBlobCache is an implementation of BlobCache using an LRU cache.
// The underlying lru.Cache is thread-safe, so no additional locking is needed.
type LRUBlobCache struct {
	cache     *lru.Cache[string, bool]
	cacheName string
}

// NewLRUBlobCache creates a new LRUBlobCache.
func NewLRUBlobCache(size int, name string) (*LRUBlobCache, error) {
	cache, err := lru.New[string, bool](size)
	if err != nil {
		log.Error().Err(err).Int("cache_size", size).Msg("Failed to create LRU cache")
		return nil, err
	}

	return &LRUBlobCache{
		cache:     cache,
		cacheName: name,
	}, nil
}

// Add adds a blob name to the cache.
func (c *LRUBlobCache) Add(key string) {
	c.cache.Add(key, true)
	CacheSize.WithLabelValues(c.cacheName).Set(float64(c.cache.Len()))
}

// Contains checks if a blob name is in the cache.
func (c *LRUBlobCache) Contains(key string) bool {
	val, ok := c.cache.Get(key)
	if ok && val {
		CacheAccessTotal.WithLabelValues(c.cacheName, "hit").Inc()
		return true
	}
	CacheAccessTotal.WithLabelValues(c.cacheName, "miss").Inc()
	return false
}

// Size returns the number of items in the cache.
func (c *LRUBlobCache) Size() int {
	return c.cache.Len()
}

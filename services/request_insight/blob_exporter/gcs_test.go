package main

import (
	"context"
	"testing"

	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
)

// ==================== GCS Client Tests ====================

func TestGCSClient_New(t *testing.T) {
	// Use the SetupFakeGCS utility
	server, client := SetupFakeGCS(t)
	defer server.Stop()

	assert.NotNil(t, client)
	assert.NotNil(t, client.storageClient)
	assert.NotNil(t, client.bucket)
	assert.Equal(t, 10, cap(client.throttleChan))
}

func TestGCSClient_EnsureExists(t *testing.T) {
	ctx := context.Background()

	// Use the SetupFakeGCS utility
	server, client := SetupFakeGCS(t)
	defer server.Stop()

	// Get the initial metric values
	initialSucceeded := testutil.ToFloat64(GCSOperations.WithLabelValues("succeeded"))
	initialBytesWritten := testutil.ToFloat64(GCSBytesWritten)
	initialFailed := testutil.ToFloat64(GCSOperations.WithLabelValues("failed"))
	initialAlreadyExist := testutil.ToFloat64(BlobsAlreadyExist)

	blobPath := "tenant/blobs/test-blob"
	contents := []byte("test content")
	metadata := map[string]string{"key": "value"}

	// Test 1: First call should create the blob
	created, err := client.EnsureExists(ctx, blobPath, contents, metadata)

	assert.True(t, created)
	assert.Nil(t, err)

	// Verify metrics
	assert.Equal(t, initialSucceeded+1, testutil.ToFloat64(GCSOperations.WithLabelValues("succeeded")))
	assert.Equal(t, initialBytesWritten+float64(len(contents)), testutil.ToFloat64(GCSBytesWritten))
	assert.Equal(t, initialFailed, testutil.ToFloat64(GCSOperations.WithLabelValues("failed")))
	assert.Equal(t, initialAlreadyExist, testutil.ToFloat64(BlobsAlreadyExist))

	// Test 2: Second call should find the blob already exists
	created, err = client.EnsureExists(ctx, blobPath, contents, metadata)

	assert.False(t, created)
	assert.Nil(t, err)

	// Verify metrics
	assert.Equal(t, initialSucceeded+2, testutil.ToFloat64(GCSOperations.WithLabelValues("succeeded")))
	// Bytes written should not increase since the blob already existed
	assert.Equal(t, initialBytesWritten+float64(len(contents)), testutil.ToFloat64(GCSBytesWritten))
}

func TestGCSClient_Close(t *testing.T) {
	// Use the SetupFakeGCS utility
	server, client := SetupFakeGCS(t)
	defer server.Stop()

	err := client.Close()

	assert.NoError(t, err)
}

// ==================== Integration Tests ====================

func TestGCSClient_Integration(t *testing.T) {
	ctx := context.Background()
	server, client := SetupFakeGCS(t)
	defer server.Stop()

	blobPath := "tenant/blobs/integration-test-blob"
	contents := []byte("integration test content")
	metadata := map[string]string{"test": "integration"}

	// First call should create the blob
	created, err := client.EnsureExists(ctx, blobPath, contents, metadata)
	assert.True(t, created)
	assert.NoError(t, err)

	// Verify the blob exists in the fake GCS server
	obj, err := server.Client().Bucket("test-bucket").Object(blobPath).NewReader(ctx)
	assert.NoError(t, err)
	defer obj.Close()

	// Read the content
	readContent := make([]byte, len(contents))
	n, err := obj.Read(readContent)
	assert.NoError(t, err)
	assert.Equal(t, len(contents), n)
	assert.Equal(t, contents, readContent)

	// Second call should find the blob already exists
	created, err = client.EnsureExists(ctx, blobPath, []byte("different content"), metadata)
	assert.False(t, created)
	assert.NoError(t, err)

	// Verify the content wasn't changed
	obj, err = server.Client().Bucket("test-bucket").Object(blobPath).NewReader(ctx)
	assert.NoError(t, err)
	defer obj.Close()

	// Read the content again
	readContent = make([]byte, len(contents))
	n, err = obj.Read(readContent)
	assert.NoError(t, err)
	assert.Equal(t, len(contents), n)
	assert.Equal(t, contents, readContent)
}

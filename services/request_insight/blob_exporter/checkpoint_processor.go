package main

import (
	"context"
	"encoding/json"
	"path"
	"time"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
)

// Checkpoint processing metrics
var (
	// CheckpointsExported tracks the number of checkpoints exported from request insight
	CheckpointsExported = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_blob_exporter_checkpoints_exported",
			Help: "Number of checkpoints exported from request insight.",
		},
		[]string{"status"},
	)

	// CheckpointProcessingLatencyHist tracks the latency to process a checkpoint
	CheckpointProcessingLatencyHist = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "au_blob_exporter_checkpoint_processing_latency",
			Help:    "Latency to process a checkpoint",
			Buckets: prometheus.DefBuckets,
		},
	)
)

// CheckpointProcessor processes checkpoints.
type CheckpointProcessor struct {
	// Clients
	contentManagerClient *ContentManagerAdapter

	// Core components
	gcsClient       *GCSClient
	checkpointCache BlobCache
	outputPath      string

	// Metrics
	processingLatency    prometheus.Histogram
	checkpointsProcessed prometheus.Counter
	checkpointsMissing   prometheus.Counter
}

// NewCheckpointProcessor creates a new CheckpointProcessor.
func NewCheckpointProcessor(
	contentManagerAdapter *ContentManagerAdapter,
	gcsClient *GCSClient,
	checkpointCache BlobCache,
	outputPath string,
) *CheckpointProcessor {
	processingLatency := CheckpointProcessingLatencyHist
	checkpointsProcessed := CheckpointsExported.WithLabelValues("success")
	checkpointsMissing := MissingItemsTotal.WithLabelValues("checkpoint")

	return &CheckpointProcessor{
		contentManagerClient: contentManagerAdapter,
		gcsClient:            gcsClient,
		checkpointCache:      checkpointCache,
		outputPath:           outputPath,
		processingLatency:    processingLatency,
		checkpointsProcessed: checkpointsProcessed,
		checkpointsMissing:   checkpointsMissing,
	}
}

// ProcessCheckpoint processes a checkpoint.
func (p *CheckpointProcessor) ProcessCheckpoint(ctx context.Context, tenantID string, checkpointID string) error {
	startTime := time.Now()
	defer func() {
		p.processingLatency.Observe(time.Since(startTime).Seconds())
	}()

	// Check if checkpoint is already in cache
	checkpointCacheKey := tenantID + ":checkpoint:" + checkpointID
	if p.checkpointCache.Contains(checkpointCacheKey) {
		log.Info().
			Str("checkpoint", checkpointID).
			Str("tenant_id", tenantID).
			Msg("Checkpoint already processed, skipping")
		return nil
	}

	// Get all blob names from the checkpoint using a dummy request context
	// The adapter will handle creating the real request context internally
	dummyRC := &requestcontext.RequestContext{}
	blobNames, err := p.contentManagerClient.GetAllBlobsFromCheckpoint(ctx, checkpointID, dummyRC, tenantID)
	if err != nil {
		log.Error().Err(err).Str("checkpoint", checkpointID).Msg("Failed to get blobs from checkpoint")
		p.checkpointsMissing.Inc()
		return err
	}

	log.Info().
		Str("checkpoint", checkpointID).
		Str("tenant_id", tenantID).
		Int("blob_count", len(blobNames)).
		Msg("Exporting checkpoint metadata")

	// Convert the blob names to a JSON array for storage
	jsonBlobNames, err := json.Marshal(blobNames)
	if err != nil {
		log.Error().Err(err).Str("checkpoint", checkpointID).Msg("Failed to marshal blob names to JSON")
		return err
	}

	// Write the checkpoint to GCS
	checkpointPath := path.Join(tenantID, p.outputPath, checkpointID)
	metadata := map[string]string{"content-type": "application/json"}

	// Ensure checkpoint exists in GCS
	created, err := p.gcsClient.EnsureExists(ctx, checkpointPath, jsonBlobNames, metadata)
	if err != nil {
		log.Error().Err(err).Str("checkpoint", checkpointID).Msg("Failed to write checkpoint to GCS")
		return err
	}

	// Success - either created or already existed
	if created {
		log.Info().Str("checkpoint", checkpointID).Msg("Successfully created checkpoint in GCS")
	} else {
		log.Info().Str("checkpoint", checkpointID).Msg("Checkpoint already exists in GCS")
	}
	p.checkpointsProcessed.Inc()

	// Add checkpoint to cache after successful write
	p.checkpointCache.Add(checkpointCacheKey)

	return nil
}

// This file contains interfaces that multiple consumers use,
// so the interface can't live directly with "the" consumer

package main

import (
	"context"
)

// BlobCache is an interface for caching blob names.
type BlobCache interface {
	// Add adds a blob name to the cache.
	Add(key string)

	// Contains checks if a blob name is in the cache.
	Contains(key string) bool

	// Size returns the number of items in the cache.
	Size() int
}

// BlobResult represents the result of a blob download operation.
type BlobResult struct {
	// BlobInfo contains the blob data if successful
	BlobInfo *BlobInfo
	// Error contains any error that occurred during download
	Error error
}

// BlobManagerClient is an interface for the blob manager client.
// This is the interface used by the blob processor and checkpoint processor.
type BlobManagerClient interface {
	// DownloadBlobs downloads multiple blobs from the content manager.
	// Returns a channel that will receive BlobResult objects for each blob.
	// The channel will be closed when all blobs have been processed.
	DownloadBlobs(ctx context.Context, keys []ContentKey, requestContext interface{}, tenantID string) (<-chan <PERSON><PERSON><PERSON><PERSON>, error)

	// GetAllBlobsFromCheckpoint gets all blob names from a checkpoint.
	GetAllBlobsFromCheckpoint(ctx context.Context, checkpointID string, requestContext interface{}, tenantID string) ([]string, error)

	// Close closes the content manager client.
	Close() error
}

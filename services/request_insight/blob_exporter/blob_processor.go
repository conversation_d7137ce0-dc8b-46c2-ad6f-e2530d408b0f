package main

import (
	"context"
	"path"
	"sync"
	"time"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
)

// Blob processing metrics
var (
	// BlobsExported tracks the number of blobs exported from request insight
	BlobsExported = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_blob_exporter_blobs_exported",
			Help: "Number of blobs exported from request insight.",
		},
		[]string{"status"},
	)

	// MissingItemsTotal tracks the number of items missing in content manager
	MissingItemsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_blob_exporter_missing_items_total",
			Help: "Total number of items missing in content manager, partitioned by item type.",
		},
		[]string{"item_type"},
	)

	// BlobProcessingLatencyHist tracks the latency to process a batch of blobs
	BlobProcessingLatencyHist = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "au_blob_exporter_blob_processing_latency",
			Help:    "Latency to process a batch of blobs",
			Buckets: prometheus.DefBuckets,
		},
	)
)

// BlobProcessor processes blobs in parallel.
type BlobProcessor struct {
	// Clients
	contentManagerClient *ContentManagerAdapter

	// Core components
	gcsClient  *GCSClient
	blobCache  BlobCache
	outputPath string

	// Metrics
	processingLatency prometheus.Histogram
	blobsProcessed    prometheus.Counter
	blobsMissing      prometheus.Counter
}

// NewBlobProcessor creates a new BlobProcessor.
func NewBlobProcessor(
	contentManagerAdapter *ContentManagerAdapter,
	gcsClient *GCSClient,
	blobCache BlobCache,
	outputPath string,
) *BlobProcessor {
	processingLatency := BlobProcessingLatencyHist
	blobsProcessed := BlobsExported.WithLabelValues("success")
	blobsMissing := MissingItemsTotal.WithLabelValues("blob")

	return &BlobProcessor{
		contentManagerClient: contentManagerAdapter,
		gcsClient:            gcsClient,
		blobCache:            blobCache,
		outputPath:           outputPath,
		processingLatency:    processingLatency,
		blobsProcessed:       blobsProcessed,
		blobsMissing:         blobsMissing,
	}
}

// ProcessBlobs processes blobs in parallel using batch downloads.
func (p *BlobProcessor) ProcessBlobs(ctx context.Context, tenantID string, blobNames []string) error {
	if len(blobNames) == 0 {
		return nil
	}

	startTime := time.Now()
	defer func() {
		p.processingLatency.Observe(time.Since(startTime).Seconds())
	}()

	// Filter out blobs that are already in the cache
	var uncachedBlobs []string
	for _, blobName := range blobNames {
		cacheKey := tenantID + ":" + blobName
		if !p.blobCache.Contains(cacheKey) {
			uncachedBlobs = append(uncachedBlobs, blobName)
		}
	}

	if len(uncachedBlobs) == 0 {
		log.Info().
			Int("total_blobs", len(blobNames)).
			Int("cached_blobs", len(blobNames)).
			Str("tenant_id", tenantID).
			Msg("All blobs already in cache, skipping")
		return nil
	}

	log.Info().
		Int("total_blobs", len(blobNames)).
		Int("uncached_blobs", len(uncachedBlobs)).
		Str("tenant_id", tenantID).
		Msg("Processing uncached blobs using batch download")

	// Create content keys for batch download
	contentKeys := make([]ContentKey, len(uncachedBlobs))
	for i, blobName := range uncachedBlobs {
		contentKeys[i] = ContentKey{BlobName: blobName}
	}

	// Create a wait group to track all GCS writes
	var writeWg sync.WaitGroup

	// Process all blobs in a single batch using a dummy request context
	// The adapter will handle creating the real request context internally
	dummyRC := &requestcontext.RequestContext{}
	resultChan, err := p.contentManagerClient.DownloadBlobs(ctx, contentKeys, dummyRC, tenantID)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to download blobs")
		return err
	}

	// Process the results as they arrive
	for result := range resultChan {
		if result.Error != nil {
			// For errors, we need to log with the blob name if possible
			log.Warn().Err(result.Error).Msg("Failed to download blob")
			p.blobsMissing.Inc()
			continue
		}

		if result.BlobInfo == nil {
			log.Warn().Msg("Could not download blob from content manager")
			p.blobsMissing.Inc()
			continue
		}

		// Use the blob name from the BlobInfo
		blobName := result.BlobInfo.BlobName
		blobPath := path.Join(tenantID, p.outputPath, blobName)

		// Increment the wait group for this write operation
		writeWg.Add(1)

		// Start an asynchronous write to GCS
		go func(blobInfo *BlobInfo) {
			// Ensure the wait group is decremented even if there's a panic
			defer writeWg.Done()

			// Ensure blob exists in GCS
			created, err := p.gcsClient.EnsureExists(ctx, blobPath, blobInfo.Content, blobInfo.Metadata)
			if err != nil {
				// Log error
				log.Error().Err(err).Str("blob", blobName).Msg("Failed to write blob to GCS")
				return
			}

			// Success - either created or already existed
			if created {
				log.Info().Str("blob", blobName).Msg("Successfully created blob in GCS")
			} else {
				log.Info().Str("blob", blobName).Msg("Blob already exists in GCS")
			}
			p.blobsProcessed.Inc()

			// Add to cache after successful write
			cacheKey := tenantID + ":" + blobName
			p.blobCache.Add(cacheKey)
		}(result.BlobInfo)

		// Explicitly set to nil to help garbage collection
		result.BlobInfo = nil
	}
	if err != nil {
		log.Error().Err(err).Str("tenant_id", tenantID).Int("blob_count", len(uncachedBlobs)).Msg("Failed to download blobs after all retry attempts")
		return err
	}

	// Wait for all GCS writes to complete
	writeWg.Wait()
	return nil
}

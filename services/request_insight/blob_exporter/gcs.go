package main

import (
	"context"
	"fmt"
	"time"

	"cloud.google.com/go/storage"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
)

// ==================== Metrics ====================

// GCS metrics track operations on Google Cloud Storage
var (
	// GCSOperationLatency tracks the latency of GCS operations
	GCSOperationLatency = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "au_blob_exporter_gcs_operation_latency",
			Help:    "Latency of GCS operations",
			Buckets: prometheus.DefBuckets,
		},
	)

	// GCSBytesWritten tracks the number of bytes written to GCS
	GCSBytesWritten = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "au_blob_exporter_gcs_bytes_written",
			Help: "Number of bytes written to GCS.",
		},
	)

	// GCSOperations tracks the number of GCS operations by result (succeeded/failed)
	GCSOperations = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_blob_exporter_gcs_operations_total",
			Help: "Number of GCS operations by result.",
		},
		[]string{"result"},
	)

	// BlobsAlreadyExist tracks the number of blobs that already exist in GCS when attempting to write
	BlobsAlreadyExist = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "au_blob_exporter_blobs_already_exist",
			Help: "Number of blobs that already exist in GCS when attempting to write.",
		},
	)

	// GCSThrottleChannelAcquisitionLatency tracks the time spent waiting to acquire GCS throttle channel slots
	GCSThrottleChannelAcquisitionLatency = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "au_blob_exporter_gcs_throttle_channel_acquisition_latency",
			Help:    "Time spent waiting to acquire GCS throttle channel slots",
			Buckets: prometheus.DefBuckets,
		},
	)
)

// ==================== GCS Client Implementation ====================

// GCSClient handles GCS operations with throttling to limit concurrent requests.
type GCSClient struct {
	// Core functionality
	storageClient *storage.Client
	bucket        *storage.BucketHandle
	throttleChan  chan struct{}

	// Metrics
	operationLatency prometheus.Histogram
	bytesWritten     prometheus.Counter
	operations       *prometheus.CounterVec
}

// NewGCSClient creates a new GCSClient with a limit on concurrent operations.
func NewGCSClient(
	ctx context.Context,
	projectID string,
	bucketName string,
	maxConcurrentRequests int,
) (*GCSClient, error) {
	// Create the storage client
	storageClient, err := storage.NewClient(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create storage client: %w", err)
	}

	// Get a handle to the bucket
	bucket := storageClient.Bucket(bucketName)

	// Set up metrics
	operationLatency := GCSOperationLatency
	bytesWritten := GCSBytesWritten
	operations := GCSOperations

	return &GCSClient{
		storageClient:    storageClient,
		bucket:           bucket,
		throttleChan:     make(chan struct{}, maxConcurrentRequests),
		operationLatency: operationLatency,
		bytesWritten:     bytesWritten,
		operations:       operations,
	}, nil
}

// exists checks if a blob exists in the bucket (private method).
func (c *GCSClient) exists(ctx context.Context, blobName string) (bool, error) {
	obj := c.bucket.Object(blobName)
	_, err := obj.Attrs(ctx)

	if err == storage.ErrObjectNotExist {
		return false, nil
	}

	if err != nil {
		return false, fmt.Errorf("failed to get blob attributes: %w", err)
	}

	return true, nil
}

// write is a private helper method that writes a blob to the bucket with the given metadata.
func (c *GCSClient) write(ctx context.Context, blobName string, contents []byte, metadata map[string]string) error {
	// Get a handle to the object
	obj := c.bucket.Object(blobName)

	// Create a writer without any conditions
	w := obj.NewWriter(ctx)

	// Upload the content
	if _, err := w.Write(contents); err != nil {
		return fmt.Errorf("failed to write blob content: %w", err)
	}

	// Close the writer
	if err := w.Close(); err != nil {
		return fmt.Errorf("failed to close writer: %w", err)
	}

	// Update metadata only if we successfully created the object
	if len(metadata) > 0 {
		objAttrs := storage.ObjectAttrsToUpdate{
			Metadata: metadata,
		}

		// Get the new generation after the write
		newAttrs, err := obj.Attrs(ctx)
		if err != nil {
			// If we can't get attributes, the object might not exist or we don't have permission
			// This is not a critical error since we've already written the content
			log.Warn().Str("blob", blobName).Err(err).Msg("Failed to get blob attributes for metadata update")
			return nil
		}

		// Update metadata with metageneration precondition
		_, err = obj.If(storage.Conditions{MetagenerationMatch: newAttrs.Metageneration}).Update(ctx, objAttrs)
		if err != nil {
			// If metadata update fails, log a warning but don't fail the operation
			// The content was successfully written
			log.Warn().Str("blob", blobName).Err(err).Msg("Failed to update metadata")
			return nil
		}
	}

	return nil
}

// EnsureExists ensures a blob exists in GCS with throttling.
//
// This method blocks until a throttleChan slot is available. It can be called directly
// from the main thread, but for better performance, it should be called from a goroutine
// when processing multiple blobs.
//
// The method handles all throttling internally and will block until resources are available.
// Returns (created, error) where:
// - created is true if the blob was newly created, false if it already existed
// - error is non-nil if there was an error ensuring the blob exists
func (c *GCSClient) EnsureExists(
	ctx context.Context,
	blobPath string,
	contents []byte,
	metadata map[string]string,
) (bool, error) {
	// Track time spent waiting for throttle channel
	throttleStart := time.Now()

	// This will block until a slot is available
	select {
	case c.throttleChan <- struct{}{}:
		// We got a slot, continue
		GCSThrottleChannelAcquisitionLatency.Observe(time.Since(throttleStart).Seconds())
	case <-ctx.Done():
		// Context was canceled while waiting for a slot
		return false, ctx.Err()
	}

	defer func() { <-c.throttleChan }()

	// Start timing the operation
	startTime := time.Now()

	// First check if the blob already exists
	exists, err := c.exists(ctx, blobPath)
	if err != nil {
		log.Warn().Str("blob", blobPath).Err(err).Msg("Failed to check if blob exists")
		// Continue with the write attempt even if the check fails
	} else if exists {
		// Blob already exists, skip the write
		log.Debug().Str("blob", blobPath).Msg("Blob already exists. Skipping write.")
		// Increment the metric for blobs that already exist
		BlobsAlreadyExist.Inc()

		// Record metrics
		c.operationLatency.Observe(time.Since(startTime).Seconds())
		c.operations.WithLabelValues("succeeded").Inc()

		return false, nil // false = not created (already existed)
	}

	// Blob doesn't exist, write it
	err = c.write(ctx, blobPath, contents, metadata)

	// Record metrics
	c.operationLatency.Observe(time.Since(startTime).Seconds())

	if err != nil {
		c.operations.WithLabelValues("failed").Inc()
		log.Error().Err(err).Str("blob", blobPath).Msg("Failed to write blob to GCS")
		return false, err
	}

	// Success metrics
	c.operations.WithLabelValues("succeeded").Inc()
	c.bytesWritten.Add(float64(len(contents)))
	log.Debug().Str("blob", blobPath).Msg("Successfully created blob in GCS")

	return true, nil // true = created
}

// waitForCompletion waits for all in-flight operations to complete.
func (c *GCSClient) waitForCompletion() {
	// Fill the throttleChan to capacity
	for i := 0; i < cap(c.throttleChan); i++ {
		c.throttleChan <- struct{}{}
	}
}

// Close waits for all in-flight operations to complete and then closes the storage client.
func (c *GCSClient) Close() error {
	// Wait for all operations to complete
	c.waitForCompletion()

	// Close the storage client
	return c.storageClient.Close()
}

local lib = import 'services/request_insight/blob_exporter/deploy_lib.jsonnet';
local appName = 'blob-exporter-central-bucket';

// Generate IAM policies to allow our external service accounts to access the blob buckets. See
// request_insight/external_service_accounts.jsonnet for details.
local externalAccess(env, cloud, namespace, bucketName) =
  if cloud != 'GCP_US_CENTRAL1_PROD' then [] else
    local saLib = import 'services/request_insight/external_service_accounts_lib.jsonnet';
    local externalServiceAccounts = saLib.getExternalServiceAccounts(env, cloud);
    local coreweaveRIDataImporterSA = externalServiceAccounts.coreweaveRIDataImporterSA;
    local sparkSA = externalServiceAccounts.sparkSA;
    [
      // Give the CoreWeave Request Insight data importer SA permission to read the bucket.
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s-external-access-policy' % bucketName,
          namespace: namespace,
          labels: {
            app: appName,
          },
        },
        spec: {
          resourceRef: {
            kind: 'StorageBucket',
            external: bucketName,
          },
          bindings: [
            {
              role: 'roles/storage.objectViewer',
              members: [
                { member: 'serviceAccount:%s' % coreweaveRIDataImporterSA.email },
                { member: 'serviceAccount:%s' % sparkSA.email },
              ],
            },
          ],
        },
      },
    ];


// Create a single central GCS bucket to export blob data
function(cloud, env, namespace, namespace_config)
  local bucketName = lib.centralBucketName(namespace, env);
  [
    {
      apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
      kind: 'StorageBucket',
      metadata: {
        annotations: {
          'cnrm.cloud.google.com/force-destroy': 'false',
        },
        labels: {
          app: appName,
        },
        name: bucketName,
        namespace: namespace,
      },
      spec: {
        uniformBucketLevelAccess: true,
      },
    },
    // We want to give eng access to the data in the bucket for research purposes.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-eng-access-policy' % bucketName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'StorageBucket',
          external: bucketName,
        },
        bindings:
          [
            {
              role: if env == 'DEV' then 'roles/storage.objectAdmin' else 'roles/storage.objectViewer',
              members: [{ member: 'group:<EMAIL>' }],
            },
          ],
      },
    },
  ] + externalAccess(env, cloud, namespace, bucketName)

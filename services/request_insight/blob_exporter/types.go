// This file contains types that are used by many "workers"

package main

// BlobInfo represents a blob with its content and metadata.
type BlobInfo struct {
	BlobName string
	Content  []byte
	Metadata map[string]string
}

// ContentKey represents a key for retrieving content from the content manager.
type ContentKey struct {
	BlobName          string
	TransformationKey string
	SubKey            string
}

package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/augmentcode/augment/base/blob_names"
	"github.com/augmentcode/augment/base/go/secretstring"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	contentmanagerpb "github.com/augmentcode/augment/services/content_manager/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
)

// Token mutex lock acquisition metrics
var (
	// ThrottleChannelAcquisitionLatency tracks the time spent waiting to acquire throttle channel slots
	ThrottleChannelAcquisitionLatency = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "au_blob_exporter_throttle_channel_acquisition_latency",
			Help:    "Time spent waiting to acquire throttle channel slots",
			Buckets: prometheus.DefBuckets,
		},
	)
)

// ContentManagerAdapter is a very lightweight wrapper which does
// throttling, error handling, metric tracking, and some basic type transformation.
// It also manages request contexts and tokens for tenant operations.
type ContentManagerAdapter struct {
	// Client for content manager
	client       contentmanagerclient.ContentManagerClient
	throttleChan chan struct{}

	// Token management
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient
	sessionID           string
	tokens              map[string]*secretstring.SecretString
	tokenMutex          sync.RWMutex
	maxRetryAttempts    int
}

// NewContentManagerAdapter creates a new ContentManagerAdapter.
func NewContentManagerAdapter(
	client contentmanagerclient.ContentManagerClient,
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	maxConcurrentRequests int,
	maxRetryAttempts int,
) *ContentManagerAdapter {
	return &ContentManagerAdapter{
		client:              client,
		throttleChan:        make(chan struct{}, maxConcurrentRequests),
		tokenExchangeClient: tokenExchangeClient,
		sessionID:           requestcontext.NewRandomRequestSessionId().String(),
		tokens:              make(map[string]*secretstring.SecretString),
		tokenMutex:          sync.RWMutex{},
		maxRetryAttempts:    maxRetryAttempts,
	}
}

// DownloadBlobs downloads multiple blobs from the content manager.
// Returns a channel that will receive BlobResult objects for each blob.
// This implementation uses batch downloads for efficiency and limits concurrent requests.
// It automatically handles token refresh and retries on failure.
func (c *ContentManagerAdapter) DownloadBlobs(
	ctx context.Context,
	keys []ContentKey,
	requestContext interface{},
	tenantID string,
) (<-chan BlobResult, error) {
	resultChan := make(chan BlobResult, len(keys))

	if len(keys) == 0 {
		close(resultChan)
		return resultChan, nil
	}

	// Check request context type
	if _, ok := requestContext.(*requestcontext.RequestContext); !ok {
		close(resultChan)
		return resultChan, fmt.Errorf("invalid request context type: %T", requestContext)
	}

	// Convert to content manager client keys
	cmKeys := make([]*contentmanagerpb.BlobContentKey, len(keys))
	for i, key := range keys {
		cmKeys[i] = &contentmanagerpb.BlobContentKey{
			BlobName:          key.BlobName,
			TransformationKey: key.TransformationKey,
			SubKey:            key.SubKey,
		}
	}

	// Track time spent waiting for throttle channel
	throttleStart := time.Now()
	c.throttleChan <- struct{}{}
	ThrottleChannelAcquisitionLatency.Observe(time.Since(throttleStart).Seconds())

	// Use retryWithNewToken internally to handle token refresh automatically
	var cmResultChan <-chan contentmanagerclient.BatchDownloadContentResult
	err := c.retryWithNewToken(
		ctx,
		tenantID,
		"BatchDownloadContent",
		func(ctx context.Context, rc *requestcontext.RequestContext) error {
			var err error
			cmResultChan, err = c.client.BatchDownloadContent(ctx, cmKeys, rc, tenantID)
			return err
		},
	)
	if err != nil {
		<-c.throttleChan
		close(resultChan)
		log.Error().Err(err).Msg("Failed to batch download content after all retry attempts")
		return resultChan, fmt.Errorf("failed to batch download content: %w", err)
	}

	// Process the results as they arrive in a goroutine
	go func() {
		defer func() { <-c.throttleChan }()
		defer close(resultChan)

		for i := 0; i < len(cmKeys); i++ {
			result, ok := <-cmResultChan
			if !ok {
				break
			}

			// Get the blob name for logging
			var blobName string
			if result.Resp != nil {
				if notFound := result.Resp.GetNotFoundContent(); notFound != nil {
					blobName = notFound.BlobName
				} else if finalContent := result.Resp.GetFinalContent(); finalContent != nil {
					blobName = finalContent.BlobName
				}
			}

			// Check for errors
			if result.Err != nil {
				resultChan <- BlobResult{
					BlobInfo: nil,
					Error:    result.Err,
				}
				continue
			}

			// Check if we have a response
			if result.Resp == nil {
				resultChan <- BlobResult{
					BlobInfo: nil,
					Error:    fmt.Errorf("response is nil"),
				}
				continue
			}

			// Check if the blob was not found
			if notFound := result.Resp.GetNotFoundContent(); notFound != nil {
				blobName = notFound.BlobName
				resultChan <- BlobResult{
					BlobInfo: nil,
					Error:    fmt.Errorf("blob not found: %s", blobName),
				}
				continue
			}

			// Get the content and metadata from the response
			finalContent := result.Resp.GetFinalContent()
			if finalContent == nil {
				resultChan <- BlobResult{
					BlobInfo: nil,
					Error:    fmt.Errorf("response missing final content"),
				}
				continue
			}

			// Convert metadata
			metadata := make(map[string]string)
			for _, meta := range finalContent.Metadata {
				metadata[meta.Key] = meta.Value
			}

			// Create blob info
			blobInfo := &BlobInfo{
				BlobName: finalContent.BlobName,
				Content:  finalContent.Content,
				Metadata: metadata,
			}

			// Send the result
			resultChan <- BlobResult{
				BlobInfo: blobInfo,
				Error:    nil,
			}

			// Explicitly set to nil to help garbage collection
			blobInfo = nil
		}
	}()

	return resultChan, nil
}

// GetAllBlobsFromCheckpoint gets all blob names from a checkpoint.
// It automatically handles token refresh and retries on failure.
func (c *ContentManagerAdapter) GetAllBlobsFromCheckpoint(
	ctx context.Context,
	checkpointID string,
	requestContext interface{},
	tenantID string,
) ([]string, error) {
	// Track time spent waiting for throttle channel
	throttleStart := time.Now()
	c.throttleChan <- struct{}{}
	ThrottleChannelAcquisitionLatency.Observe(time.Since(throttleStart).Seconds())
	defer func() { <-c.throttleChan }()

	// Check request context type
	if _, ok := requestContext.(*requestcontext.RequestContext); !ok {
		return nil, fmt.Errorf("invalid request context type: %T", requestContext)
	}

	// Use retryWithNewToken internally to handle token refresh automatically
	var blobNames []blob_names.BlobName
	err := c.retryWithNewToken(
		ctx,
		tenantID,
		"GetAllBlobsFromCheckpoint",
		func(ctx context.Context, rc *requestcontext.RequestContext) error {
			var err error
			blobNames, err = c.client.GetAllBlobsFromCheckpoint(ctx, checkpointID, rc)
			return err
		},
	)
	if err != nil {
		log.Error().Err(err).Str("checkpoint", checkpointID).Msg("Failed to get blobs from checkpoint after all retry attempts")
		return nil, fmt.Errorf("failed to get blobs from checkpoint: %w", err)
	}

	// Convert blob names to strings
	result := make([]string, len(blobNames))
	for i, blobName := range blobNames {
		result[i] = string(blobName)
	}

	return result, nil
}

// waitForCompletion waits for all content manager requests to complete.
func (c *ContentManagerAdapter) waitForCompletion() {
	// Fill the throttleChan to capacity
	for i := 0; i < cap(c.throttleChan); i++ {
		c.throttleChan <- struct{}{}
	}
}

// Close closes the content manager client.
func (c *ContentManagerAdapter) Close() error {
	// Wait for all in-flight requests to complete
	c.waitForCompletion()

	// Close the underlying client
	c.client.Close()
	return nil
}

// getRequestContext gets a request context for the given tenant ID.
// This is a private method used internally by the adapter.
func (c *ContentManagerAdapter) getRequestContext(ctx context.Context, tenantID string) (*requestcontext.RequestContext, error) {
	// First try to get the token with a read lock
	c.tokenMutex.RLock()
	token, ok := c.tokens[tenantID]
	c.tokenMutex.RUnlock()

	if !ok {
		// Token not found, acquire write lock
		c.tokenMutex.Lock()

		// Check again in case another goroutine created the token while we were waiting
		token, ok = c.tokens[tenantID]
		if !ok {
			// Still not found, get a new token
			newToken, err := c.tokenExchangeClient.GetSignedTokenForService(ctx, tenantID, []tokenexchangepb.Scope{tokenexchangepb.Scope_CONTENT_RW})
			if err != nil {
				c.tokenMutex.Unlock() // Don't forget to unlock before returning
				log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get new token")
				return nil, err
			}

			// Store the token
			c.tokens[tenantID] = &newToken
			token = &newToken
			log.Info().Str("tenant_id", tenantID).Msg("Obtained new token")
		}

		c.tokenMutex.Unlock()
	}

	// Create request context with proper headers
	rc := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.RequestSessionId(c.sessionID),
		"blob_exporter",
		*token,
	)

	return rc, nil
}

// invalidateToken removes a token for a tenant, forcing a new token to be obtained on the next request.
// This is a private method used internally by the adapter.
func (c *ContentManagerAdapter) invalidateToken(tenantID string) {
	c.tokenMutex.Lock()
	delete(c.tokens, tenantID)
	c.tokenMutex.Unlock()
	log.Info().Str("tenant_id", tenantID).Msg("Invalidated token")
}

// retryWithNewToken executes an operation with automatic retry and token refresh.
// If the operation fails, it will invalidate the token and retry up to maxRetryAttempts times.
// The operation function should return an error if it fails and needs to be retried with a new token.
// This is a private method used internally by the adapter.
func (c *ContentManagerAdapter) retryWithNewToken(
	ctx context.Context,
	tenantID string,
	operationName string,
	operation func(ctx context.Context, rc *requestcontext.RequestContext) error,
) error {
	var lastErr error

	// Try the operation up to maxRetryAttempts times
	for attempt := 0; attempt < c.maxRetryAttempts; attempt++ {
		// Get a request context (with a potentially new token if we're retrying)
		rc, err := c.getRequestContext(ctx, tenantID)
		if err != nil {
			log.Error().Err(err).
				Str("tenant_id", tenantID).
				Str("operation", operationName).
				Int("attempt", attempt+1).
				Msg("Failed to get request context")
			return err
		}

		// Execute the operation
		err = operation(ctx, rc)
		if err == nil {
			// Operation succeeded
			return nil
		}

		// Operation failed, save the error
		lastErr = err

		// If this wasn't the last attempt, invalidate the token and try again
		if attempt < c.maxRetryAttempts-1 {
			log.Warn().Err(err).
				Str("tenant_id", tenantID).
				Str("operation", operationName).
				Int("attempt", attempt+1).
				Int("max_attempts", c.maxRetryAttempts).
				Msg("Operation failed, retrying with new token")

			// Invalidate the token to force getting a new one on the next attempt
			c.invalidateToken(tenantID)
		}
	}

	// All attempts failed
	log.Error().Err(lastErr).
		Str("tenant_id", tenantID).
		Str("operation", operationName).
		Int("max_attempts", c.maxRetryAttempts).
		Msg("Operation failed after all retry attempts")

	return lastErr
}

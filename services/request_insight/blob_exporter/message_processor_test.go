package main

import (
	"context"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	pb "github.com/augmentcode/augment/services/request_insight/proto"
)

// MockBlobProcessor is a mock implementation of the BlobProcessor
type MockBlobProcessor struct {
	mock.Mock
	processBlobsFunc func(ctx context.Context, tenantID string, blobNames []string) error
}

// ProcessBlobs mocks the ProcessBlobs method
func (m *MockBlobProcessor) ProcessBlobs(ctx context.Context, tenantID string, blobNames []string) error {
	if m.processBlobsFunc != nil {
		return m.processBlobsFunc(ctx, tenantID, blobNames)
	}
	args := m.Called(ctx, tenantID, blobNames)
	return args.Error(0)
}

// MockCheckpointProcessor is a mock implementation of the CheckpointProcessor
type MockCheckpointProcessor struct {
	mock.Mock
	processCheckpointFunc func(ctx context.Context, tenantID string, checkpointID string) error
}

// ProcessCheckpoint mocks the ProcessCheckpoint method
func (m *MockCheckpointProcessor) ProcessCheckpoint(ctx context.Context, tenantID string, checkpointID string) error {
	if m.processCheckpointFunc != nil {
		return m.processCheckpointFunc(ctx, tenantID, checkpointID)
	}
	args := m.Called(ctx, tenantID, checkpointID)
	return args.Error(0)
}

// setupMessageProcessorTestEnvironment creates a test environment for testing MessageProcessor
func setupMessageProcessorTestEnvironment(t *testing.T) (*MessageProcessor, *MockBlobProcessor, *MockCheckpointProcessor) {
	// Create mock processors
	mockBlobProcessor := &MockBlobProcessor{}
	mockCheckpointProcessor := &MockCheckpointProcessor{}

	// Create a message processor with the mock processors
	// We need to cast the mocks to the appropriate interfaces
	messageProcessor := &MessageProcessor{
		blobProcessor:       mockBlobProcessor,
		checkpointProcessor: mockCheckpointProcessor,
		blobCount:           BlobsExported.WithLabelValues("extracted"),
		checkpointCount:     CheckpointsExported.WithLabelValues("extracted"),
	}

	return messageProcessor, mockBlobProcessor, mockCheckpointProcessor
}

// createTestMessage creates a test RequestInsightMessage with the specified blobs and checkpoint
func createTestMessage(tenantID string, blobNames []string, checkpointID string) *pb.RequestInsightMessage {
	// Create a message with RecordSessionEventsRequest
	message := &pb.RequestInsightMessage{
		Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
			RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
				TenantInfo: &pb.TenantInfo{
					TenantId: tenantID,
				},
				Events: []*pb.SessionEvent{},
			},
		},
	}

	// Add blob upload event if blobNames is not empty
	if len(blobNames) > 0 {
		uploadedBlobInfos := make([]*pb.ContentManagerUploadBlobs_UploadedBlobInfo, 0, len(blobNames))
		for _, blobName := range blobNames {
			uploadedBlobInfos = append(uploadedBlobInfos, &pb.ContentManagerUploadBlobs_UploadedBlobInfo{
				BlobName: blobName,
			})
		}

		blobEvent := &pb.SessionEvent{
			Event: &pb.SessionEvent_ContentManagerUploadBlobs{
				ContentManagerUploadBlobs: &pb.ContentManagerUploadBlobs{
					UploadedBlobs: uploadedBlobInfos,
				},
			},
		}

		message.GetRecordSessionEventsRequest().Events = append(
			message.GetRecordSessionEventsRequest().Events,
			blobEvent,
		)
	}

	// Add checkpoint event if checkpointID is not empty
	if checkpointID != "" {
		checkpointEvent := &pb.SessionEvent{
			Event: &pb.SessionEvent_ContentManagerCheckpointBlobs{
				ContentManagerCheckpointBlobs: &pb.ContentManagerCheckpointBlobs{
					CheckpointId:      checkpointID,
					AddedBlobsCount:   10,
					DeletedBlobsCount: 5,
				},
			},
		}

		message.GetRecordSessionEventsRequest().Events = append(
			message.GetRecordSessionEventsRequest().Events,
			checkpointEvent,
		)
	}

	return message
}

// TestProcessMessageEmpty tests processing an empty message
func TestProcessMessageEmpty(t *testing.T) {
	messageProcessor, mockBlobProcessor, mockCheckpointProcessor := setupMessageProcessorTestEnvironment(t)

	ctx := context.Background()
	tenantID := "test-tenant"

	// Create an empty message (no blobs, no checkpoint)
	message := createTestMessage(tenantID, nil, "")

	// Process the message
	err := messageProcessor.ProcessMessage(ctx, message)
	assert.NoError(t, err)

	// Verify that no processors were called
	mockBlobProcessor.AssertNotCalled(t, "ProcessBlobs")
	mockCheckpointProcessor.AssertNotCalled(t, "ProcessCheckpoint")
}

// TestProcessMessageBlobsOnly tests processing a message with only blobs
func TestProcessMessageBlobsOnly(t *testing.T) {
	messageProcessor, mockBlobProcessor, mockCheckpointProcessor := setupMessageProcessorTestEnvironment(t)

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}

	// Set up the mock blob processor
	var processBlobsCalled sync.WaitGroup
	processBlobsCalled.Add(1)
	mockBlobProcessor.processBlobsFunc = func(ctx context.Context, actualTenantID string, actualBlobNames []string) error {
		defer processBlobsCalled.Done()
		assert.Equal(t, tenantID, actualTenantID)
		assert.Equal(t, blobNames, actualBlobNames)
		return nil
	}

	// Create a message with blobs only
	message := createTestMessage(tenantID, blobNames, "")

	// Process the message
	err := messageProcessor.ProcessMessage(ctx, message)
	assert.NoError(t, err)

	// Wait for the goroutine to complete
	processBlobsCalled.Wait()

	// Verify that only the blob processor was called
	mockCheckpointProcessor.AssertNotCalled(t, "ProcessCheckpoint")
}

// TestProcessMessageCheckpointOnly tests processing a message with only a checkpoint
func TestProcessMessageCheckpointOnly(t *testing.T) {
	messageProcessor, mockBlobProcessor, mockCheckpointProcessor := setupMessageProcessorTestEnvironment(t)

	ctx := context.Background()
	tenantID := "test-tenant"
	checkpointID := "checkpoint1"

	// Set up the mock checkpoint processor
	var processCheckpointCalled sync.WaitGroup
	processCheckpointCalled.Add(1)
	mockCheckpointProcessor.processCheckpointFunc = func(ctx context.Context, actualTenantID string, actualCheckpointID string) error {
		defer processCheckpointCalled.Done()
		assert.Equal(t, tenantID, actualTenantID)
		assert.Equal(t, checkpointID, actualCheckpointID)
		return nil
	}

	// Create a message with checkpoint only
	message := createTestMessage(tenantID, nil, checkpointID)

	// Process the message
	err := messageProcessor.ProcessMessage(ctx, message)
	assert.NoError(t, err)

	// Wait for the async processing to complete
	processCheckpointCalled.Wait()

	// Verify that only the checkpoint processor was called
	mockBlobProcessor.AssertNotCalled(t, "ProcessBlobs")
}

// TestProcessMessageBothBlobsAndCheckpoint tests processing a message with both blobs and a checkpoint
func TestProcessMessageBothBlobsAndCheckpoint(t *testing.T) {
	messageProcessor, mockBlobProcessor, mockCheckpointProcessor := setupMessageProcessorTestEnvironment(t)

	ctx := context.Background()
	tenantID := "test-tenant"
	blobNames := []string{"blob1", "blob2", "blob3"}
	checkpointID := "checkpoint1"

	// Set up the mock processors
	var wg sync.WaitGroup
	wg.Add(2) // One for blobs, one for checkpoint

	mockBlobProcessor.processBlobsFunc = func(ctx context.Context, actualTenantID string, actualBlobNames []string) error {
		defer wg.Done()
		assert.Equal(t, tenantID, actualTenantID)
		assert.Equal(t, blobNames, actualBlobNames)
		return nil
	}

	mockCheckpointProcessor.processCheckpointFunc = func(ctx context.Context, actualTenantID string, actualCheckpointID string) error {
		defer wg.Done()
		assert.Equal(t, tenantID, actualTenantID)
		assert.Equal(t, checkpointID, actualCheckpointID)
		return nil
	}

	// Create a message with both blobs and checkpoint
	message := createTestMessage(tenantID, blobNames, checkpointID)

	// Process the message
	err := messageProcessor.ProcessMessage(ctx, message)
	assert.NoError(t, err)

	// Wait for the async processing to complete
	wg.Wait()
}

package main

import (
	"testing"

	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
)

func TestNewLRUBlobCache(t *testing.T) {
	// Test with valid size
	cache, err := NewLRUBlobCache(100, "test_cache")
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	assert.Equal(t, "test_cache", cache.cacheName)
	assert.Equal(t, 0, cache.Size())

	// Test with invalid size (0)
	cache, err = NewLRUBlobCache(0, "invalid_cache")
	assert.Error(t, err)
	assert.Nil(t, cache)
}

func TestLRUBlobCacheOperations(t *testing.T) {
	// Reset metrics for clean test
	defer CacheAccessTotal.Reset()
	defer CacheSize.Reset()

	cache, err := NewLRUBlobCache(10, "test_cache")
	assert.NoError(t, err)

	// Test Add and Contains
	cache.Add("key1")
	assert.True(t, cache.Contains("key1"))
	assert.False(t, cache.Contains("key2"))

	// Test Size
	assert.Equal(t, 1, cache.Size())

	// Test metrics
	assert.Equal(t, float64(1), testutil.ToFloat64(CacheAccessTotal.WithLabelValues("test_cache", "hit")))
	assert.Equal(t, float64(1), testutil.ToFloat64(CacheAccessTotal.WithLabelValues("test_cache", "miss")))
	assert.Equal(t, float64(1), testutil.ToFloat64(CacheSize.WithLabelValues("test_cache")))

	// Test LRU eviction
	for i := 0; i < 10; i++ {
		cache.Add("eviction_key_" + string(rune('a'+i)))
	}
	// After adding 10 more items, the cache should be full and key1 should be evicted
	assert.False(t, cache.Contains("key1"))
	assert.Equal(t, 10, cache.Size())
}

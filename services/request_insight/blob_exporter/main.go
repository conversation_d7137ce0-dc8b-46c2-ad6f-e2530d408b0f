package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/augmentcode/augment/base/logging"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/augmentcode/augment/services/lib/pubsub"
	subscriber "github.com/augmentcode/augment/services/request_insight/lib/subscriber"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
)

func main() {
	// Create a context that can be canceled for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Setup wait group to track all goroutines
	var wg sync.WaitGroup

	// Setup logging
	logging.SetupServerLogging()

	// Parse command-line arguments
	configFile := flag.String("config", "/config/config.json", "Path to the configuration file")
	healthFile := flag.String("health-file", "", "Path to a file to write a health check to")
	flag.Parse()

	// Load configuration
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Start metrics server
	wg.Add(1)
	go func() {
		defer wg.Done()
		http.Handle("/metrics", promhttp.Handler())
		server := &http.Server{
			Addr:    fmt.Sprintf(":%d", config.PromPort),
			Handler: nil,
		}

		// Shutdown the HTTP server when the context is canceled
		go func() {
			<-ctx.Done()
			log.Info().Msg("Shutting down metrics server...")
			if err := server.Shutdown(context.Background()); err != nil {
				log.Error().Err(err).Msg("Error shutting down metrics server")
			}
		}()

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Create GCS client
	gcsClient, err := NewGCSClient(ctx, config.ProjectId, config.BucketName, config.MaxGCSRequests)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create GCS client")
	}
	defer gcsClient.Close()

	// Create content manager client with appropriate credentials
	var contentManagerClient contentmanagerclient.ContentManagerClient
	var contentManagerCreds credentials.TransportCredentials

	if config.ClientMTLS != nil {
		clientConfig := &tlsconfig.ClientConfig{
			KeyPath:  config.ClientMTLS.KeyPath,
			CertPath: config.ClientMTLS.CertPath,
			CaPath:   config.ClientMTLS.CaPath,
		}
		contentManagerCreds, err = tlsconfig.GetClientTls(clientConfig)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create TLS credentials for content manager")
		}
	} else {
		contentManagerCreds = insecure.NewCredentials()
	}

	contentManagerClient, err = contentmanagerclient.NewContentManagerClient(
		config.ContentManagerEndpoint,
		contentManagerCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create content manager client")
	}
	defer contentManagerClient.Close()

	// Create token exchange client with appropriate credentials
	var tokenExchangeClient tokenexchangeclient.TokenExchangeClient
	var tokenExchangeOpts []grpc.DialOption

	if config.CentralClientMTLS != nil {
		clientConfig := &tlsconfig.ClientConfig{
			KeyPath:  config.CentralClientMTLS.KeyPath,
			CertPath: config.CentralClientMTLS.CertPath,
			CaPath:   config.CentralClientMTLS.CaPath,
		}
		creds, err := tlsconfig.GetClientTls(clientConfig)
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to create TLS credentials for token exchange")
		}
		tokenExchangeOpts = append(tokenExchangeOpts, grpc.WithTransportCredentials(creds))
	} else {
		tokenExchangeOpts = append(tokenExchangeOpts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	}

	namespace, exists := os.LookupEnv("POD_NAMESPACE")
	if !exists {
		log.Fatal().Msg("POD_NAMESPACE environment variable not set")
	}

	tokenExchangeClient, err = tokenexchangeclient.New(
		config.TokenExchangeEndpoint,
		namespace,
		tokenExchangeOpts...,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create token exchange client")
	}
	defer tokenExchangeClient.Close()

	// Create blob and checkpoint caches
	blobCache, err := NewLRUBlobCache(config.BlobCacheSize, "blob")
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create blob cache")
	}

	checkpointCache, err := NewLRUBlobCache(config.CheckpointCacheSize, "checkpoint")
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create checkpoint cache")
	}

	// GCS client already created above

	// Create content manager adapter
	contentManagerAdapter := NewContentManagerAdapter(
		contentManagerClient,
		tokenExchangeClient,
		config.MaxContentManagerRequests,
		config.MaxRetryAttempts,
	)

	// Create blob processor
	blobProcessor := NewBlobProcessor(
		contentManagerAdapter,
		gcsClient,
		blobCache,
		config.BlobOutputPath,
	)

	// Create checkpoint processor
	checkpointProcessor := NewCheckpointProcessor(
		contentManagerAdapter,
		gcsClient,
		checkpointCache,
		config.CheckpointOutputPath,
	)

	// Create message processor with event throttling
	messageProcessor := NewMessageProcessor(blobProcessor, checkpointProcessor)

	// Create subscriber config
	subscribeConfig := &pubsub.SubscribeClientConfig{
		ProjectId:              config.ProjectId,
		SubscriptionId:         config.SubscriptionId,
		MaxConcurrentReceivers: config.MaxConcurrentReceivers,
	}

	// Create and start subscriber
	sub, err := subscriber.New(ctx, subscribeConfig, messageProcessor.ProcessMessage)
	if err != nil {
		log.Fatal().Err(err).Msg("Error initializing RequestInsightSubscriber")
	}
	defer sub.Close()

	// Run the subscriber in a goroutine
	go func() {
		log.Info().Msg("Starting subscriber")
		sub.Run(ctx) // This will log.Fatal() if there's an error
	}()

	// Write health file after a delay if specified
	if *healthFile != "" {
		wg.Add(1)
		go func() {
			defer wg.Done()
			select {
			case <-time.After(5 * time.Second):
				log.Info().Msgf("Declaring healthy in %s", *healthFile)
				f, err := os.Create(*healthFile)
				if err != nil {
					log.Fatal().Err(err).Msg("Failed to create health file")
				}
				defer f.Close()
				f.WriteString("OK")
			case <-ctx.Done():
				// Context canceled, don't write health file
				return
			}
		}()
	}

	// Set up a signal handler for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)

	// Wait for shutdown signal or context cancellation
	select {
	case <-sigChan:
		log.Info().Msg("Received shutdown signal, initiating graceful shutdown...")
		cancel() // Cancel the context to stop receiving new messages
	case <-ctx.Done():
		log.Info().Msg("Context cancelled, initiating graceful shutdown...")
	}

	// Perform graceful shutdown
	log.Info().Msg("Waiting for in-flight operations to complete...")

	// Wait for all GCS operations to complete
	blobProcessor.gcsClient.Close()

	// Wait for all content manager requests to complete
	contentManagerAdapter.Close()

	// Wait for all goroutines to complete
	wg.Wait()
	log.Info().Msg("All operations completed, server stopped")
}

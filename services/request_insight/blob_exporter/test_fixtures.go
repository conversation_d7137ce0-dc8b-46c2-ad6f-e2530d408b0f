package main

import (
	"context"
	"testing"

	"github.com/fsouza/fake-gcs-server/fakestorage"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// TestEnvironment contains all the common components needed for testing blob and checkpoint processors
type TestEnvironment struct {
	// Common components
	GCSServer          *fakestorage.Server
	GCSClient          *GCSClient
	BlobCache          *LRUBlobCache
	MockContentManager *MockContentManagerAdapter
	OutputPath         string

	// Cleanup function
	Cleanup func()
}

// ==================== GCS Test Utilities ====================

// SetupFakeGCS creates a fake GCS server for testing
func SetupFakeGCS(t *testing.T) (*fakestorage.Server, *GCSClient) {
	// Create a fake GCS server
	server := fakestorage.NewServer(nil)

	// Create a test bucket
	bucketName := "test-bucket"
	server.CreateBucketWithOpts(fakestorage.CreateBucketOpts{
		Name: bucketName,
	})

	// Create a GCSClient that uses the fake server's client
	client := server.Client()
	gcsClient := &GCSClient{
		storageClient:    client,
		bucket:           client.Bucket(bucketName),
		throttleChan:     make(chan struct{}, 10),
		operationLatency: GCSOperationLatency,
		bytesWritten:     GCSBytesWritten,
		operations:       GCSOperations,
	}

	return server, gcsClient
}

// VerifyBlobInGCS checks if a blob exists in the fake GCS server with the expected content
func VerifyBlobInGCS(t *testing.T, server *fakestorage.Server, blobPath string, expectedContent []byte) {
	// Get the object directly
	obj, err := server.Client().Bucket("test-bucket").Object(blobPath).NewReader(context.Background())
	if err != nil {
		t.Fatalf("Blob not found in GCS: %s, error: %v", blobPath, err)
		return
	}
	defer obj.Close()

	// Read the content
	content := make([]byte, len(expectedContent))
	n, err := obj.Read(content)
	assert.NoError(t, err, "Error reading blob content")
	assert.Equal(t, len(expectedContent), n, "Content length mismatch")

	// Verify content
	assert.Equal(t, expectedContent, content, "Content mismatch for blob: %s", blobPath)
}

// CountObjectsInGCS counts the number of objects in the fake GCS server
func CountObjectsInGCS(server *fakestorage.Server) int {
	// List all objects in the bucket
	objects := server.Client().Bucket("test-bucket").Objects(context.Background(), nil)

	// Count the objects
	count := 0
	for {
		_, err := objects.Next()
		if err != nil {
			break // End of list or error
		}
		count++
	}

	return count
}

// VerifyNoObjectsInGCS checks that no objects exist in the fake GCS server
func VerifyNoObjectsInGCS(t *testing.T, server *fakestorage.Server, message string) {
	count := CountObjectsInGCS(server)
	assert.Equal(t, 0, count, message)
}

// VerifyObjectCountInGCS checks that the expected number of objects exist in the fake GCS server
func VerifyObjectCountInGCS(t *testing.T, server *fakestorage.Server, expectedCount int, message string) {
	count := CountObjectsInGCS(server)
	assert.Equal(t, expectedCount, count, message)
}

// ==================== Cache Test Utilities ====================

// SetupLRUBlobCache creates an LRUBlobCache for testing
func SetupLRUBlobCache(t *testing.T) *LRUBlobCache {
	blobCache, err := NewLRUBlobCache(100, "test-cache")
	assert.NoError(t, err)
	return blobCache
}

// ==================== Content Manager Mock ====================

// MockContentManagerAdapter is a mock implementation of ContentManagerAdapter for testing
type MockContentManagerAdapter struct {
	mock.Mock
}

// NewMockContentManagerAdapter creates a new mock content manager adapter
func NewMockContentManagerAdapter() *MockContentManagerAdapter {
	// Create a mock adapter
	mockAdapter := &MockContentManagerAdapter{}

	return mockAdapter
}

// DownloadBlobs mocks the DownloadBlobs method
func (m *MockContentManagerAdapter) DownloadBlobs(ctx context.Context, keys []ContentKey, requestContext interface{}, tenantID string) (<-chan BlobResult, error) {
	args := m.Called(ctx, keys, requestContext, tenantID)
	return args.Get(0).(<-chan BlobResult), args.Error(1)
}

// GetAllBlobsFromCheckpoint mocks the GetAllBlobsFromCheckpoint method
func (m *MockContentManagerAdapter) GetAllBlobsFromCheckpoint(ctx context.Context, checkpointID string, requestContext interface{}, tenantID string) ([]string, error) {
	args := m.Called(ctx, checkpointID, requestContext, tenantID)

	// Handle nil return value
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).([]string), args.Error(1)
}

// Close mocks the Close method
func (m *MockContentManagerAdapter) Close() error {
	return nil
}

// ==================== Common Test Environment ====================

// SetupTestEnvironment creates a common test environment for testing processors
func SetupTestEnvironment(t *testing.T) *TestEnvironment {
	// Set up fake GCS server
	server, gcsClient := SetupFakeGCS(t)

	// Create a real LRU blob cache
	blobCache := SetupLRUBlobCache(t)

	// Create mock content manager adapter
	mockContentManager := NewMockContentManagerAdapter()

	// Create a fixed output path for testing
	outputPath := "test-output-path"

	// Create the test environment
	env := &TestEnvironment{
		GCSServer:          server,
		GCSClient:          gcsClient,
		BlobCache:          blobCache,
		MockContentManager: mockContentManager,
		OutputPath:         outputPath,
		Cleanup: func() {
			server.Stop()
		},
	}

	return env
}

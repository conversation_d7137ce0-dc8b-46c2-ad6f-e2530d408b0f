"""Unit tests for the request insight protos."""

from collections import defaultdict

import services.request_insight.request_insight_pb2 as request_insight_pb2


def test_merging_multiple_retrieval_responses():
    """Tests merging multiple RetrievalResponse protos.

    This is less a test and more of a sanity check to make sure I understand how
    merging protos works.
    """
    response1 = request_insight_pb2.RetrievalResponse(
        retrieval_type=request_insight_pb2.RetrievalType.DENSE,
        query_prompt=request_insight_pb2.Tokenization(
            token_ids=[1],
            offsets=[0],
            text="foo",
            log_probs=[0.0],
        ),
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                blob_name="foo.py",
                chunk_index=0,
                char_offset=0,
            ),
        ],
    )
    response2 = request_insight_pb2.RetrievalResponse(
        retrieval_type=request_insight_pb2.RetrievalType.DENSE,
        query_prompt=request_insight_pb2.Tokenization(
            token_ids=[2],
            offsets=[0],
            text="bar",
            log_probs=[0.0],
        ),
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                blob_name="bar.py",
                chunk_index=0,
                char_offset=0,
            ),
        ],
    )
    response3 = request_insight_pb2.RetrievalResponse(
        retrieval_type=request_insight_pb2.RetrievalType.SIGNATURE,
        query_prompt=request_insight_pb2.Tokenization(
            token_ids=[3],
            offsets=[0],
            text="baz",
            log_probs=[0.0],
        ),
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                blob_name="baz.py",
                chunk_index=0,
                char_offset=0,
            ),
        ],
    )

    event1 = request_insight_pb2.RequestInfo(retrieval_response_list=[response1])
    event2 = request_insight_pb2.RequestInfo(
        retrieval_response_list=[response2, response3]
    )

    merged_event = request_insight_pb2.RequestInfo()
    merged_event.MergeFrom(event1)
    merged_event.MergeFrom(event2)

    assert len(merged_event.retrieval_response_list) == 3
    found = defaultdict(int)
    for resp in merged_event.retrieval_response_list:
        found[resp.retrieval_type] += 1
        if resp.retrieval_type == request_insight_pb2.RetrievalType.SIGNATURE:
            assert resp.query_prompt.text == "baz"
            assert resp.retrieved_chunks[0].blob_name == "baz.py"
        else:
            assert resp.query_prompt.text == "foo" or resp.query_prompt.text == "bar"
            assert (
                resp.retrieved_chunks[0].blob_name == "foo.py"
                or resp.retrieved_chunks[0].blob_name == "bar.py"
            )

    assert found == {
        request_insight_pb2.RetrievalType.SIGNATURE: 1,
        request_insight_pb2.RetrievalType.DENSE: 2,
    }

"""Unit tests for the user event exporter."""

from datetime import datetime

import services.request_insight.request_insight_pb2 as request_insight_pb2
from services.request_insight.bigquery_full_exporter.user_event_exporter import (
    _user_event_rows,
)


def test_user_event_row_edit_request_id_issued():
    now = datetime.now()
    edit_request_id_issued = request_insight_pb2.EditRequestIdIssuedEvent(
        request_id="test-request",
    )
    event = request_insight_pb2.FullExportUserEvent()
    event.time.FromDatetime(now)
    event.file_path = "/test-path"
    event.edit_request_id_issued.CopyFrom(edit_request_id_issued)
    message = request_insight_pb2.RequestInsightMessage(
        record_full_export_user_events_request=request_insight_pb2.RecordFullExportUserEventsRequest(
            session_id="test-session",
            user_id="test-user",
            extension_data=request_insight_pb2.ExtensionData(
                user_events=[event, event],
            ),
        )
    )

    rows = _user_event_rows(message, "test_tenant")
    expected_row = {
        "session_id": "test-session",
        "user_id": "test-user",
        "tenant": "test_tenant",
        "namespace": "test_tenant",
        "time": now.isoformat(),
        "file_path": "/test-path",
        "event_type": "edit_request_id_issued",
        "raw_json": {"request_id": "test-request"},
    }
    assert rows == [expected_row, expected_row]

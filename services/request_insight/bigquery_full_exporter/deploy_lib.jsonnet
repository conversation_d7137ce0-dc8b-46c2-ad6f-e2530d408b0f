// Jsonnet library for configuring an exporter that writes to a single table in the full export
// BigQuery dataset.
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

// Returns all the kubernetes objects to run an exporter that writes to a single table in the full
// export BigQuery dataset. Callers are expected to check whether full export is enabled before
// using this.
// Args:
//   env: The environment to run the exporter in.
//   namespace: The namespace to run the exporter in.
//   cloud: The cloud to run the exporter in.
//   useSharedDevRequestInsightBigquery: Whether to use the shared dev BigQuery dataset
//       (relevant iff env == DEV).
//   appName: The name of the app to use for the exporter.
//   shortAppName: The short name of the app to use for the exporter. (Used for IAM, which has name
//       length constraints.)
//   subscriptionName: The name of the pub/sub subscription to use.
//   subscriptionDescription: A description of the subscription, shared across tenants. Used for
//       metrics.
//   tableName: The name of the table to write to.
//   targetName: The name of the target to use for the exporter.
function(
  env,
  namespace,
  cloud,
  useSharedDevRequestInsightBigquery,
  appName,
  shortAppName,
  subscriptionName,
  subscriptionDescription,
  tableName,
  targetName
)
  local projectId = cloudInfo[cloud].projectId;
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  local bigqueryLib = (import 'services/request_insight/bigquery_full_exporter/full_export_bigquery_lib.jsonnet')(cloud, env, namespace, useSharedDevRequestInsightBigquery);

  local config = {
    project_id: projectId,
    topic_name: '%s-request-insight-topic' % namespace,
    subscription_name: subscriptionName,
    subscription_description: subscriptionDescription,
    subscription_batch_size: 500,
    bigquery_dataset_name: bigqueryLib.datasetGcp,
    table_name: tableName,
    namespace_name: namespace,
    bucket_name: '%s-%s-%s-ri-full-export' % [std.asciiLower(env), namespace, std.strReplace(tableName, '_', '-')],
    // Load to BigQuery every 5 minutes. BigQuery has a 1500 jobs per table per day limit. We have
    // at most two exporters writing to the same request_event table at once.
    batch_interval_seconds: 300,
    // The limit of a BigQuery API request is 10MB. 8MB keeps us well under that limit, while also
    // allowing most batches to be written directly.
    large_batch_threshold_bytes: 1024 * 1024 * 8,
  };

  local configMap = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: '%s-config' % appName,
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: appName,
      },
    },
    data: {
      'config.json': std.manifestJson(config),
    },
  };

  local gcpObjects = [
    // Pub/sub subscription.
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: config.subscription_name,
        namespace: namespace,
        // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
        // topic is deleted before the pod.
        annotations: if env == 'DEV' then {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        } else {},
        labels: {
          app: appName,
        },
      },
      spec: {
        topicRef: {
          name: config.topic_name,
        },
        // 10 minutes is the maximum ack deadline you're allowed to set. We publish to BigQuery
        // every 5 minutes, so this seems like a reasonable buffer.
        ackDeadlineSeconds: 600,
        retryPolicy: {
          minimumBackoff: '5s',
          maximumBackoff: '300s',
        },
        // Retain messages for 1 hour in dev and 7 days in staging/prod.
        messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
        retainAckedMessages: false,
        enableExactlyOnceDelivery: true,
      },
    },
    // Give access to the subscription.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-subscription-policy' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: config.subscription_name,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
        ],
      },
    },
    // Give access to the BigQuery dataset. BigQuery doesn't support IAM, so this is done with
    // a Google Group membership instead. (See bigquery.jsonnet).
    {
      apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
      kind: 'CloudIdentityMembership',
      metadata: {
        name: '%s-dataset-access-membership' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        groupRef: {
          name: bigqueryLib.cloudIdentityGroup,
          namespace:
            if cloud == 'GCP_US_CENTRAL1_DEV' then (
              if useSharedDevRequestInsightBigquery then 'central-dev' else namespace
            )
            else if env == 'STAGING' then 'central-staging'
            else 'central',
        },
        preferredMemberKey: {
          id: serviceAccount.serviceAccountGcpEmailAddress,
        },
        roles: [
          {
            name: 'MEMBER',
          },
        ],
      },
    },

    // Bucket to export JSON that is later ingested by BigQuery.
    {
      apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
      kind: 'StorageBucket',
      metadata: {
        annotations: {
          // If set to true, the force-destroy directive cleans up the objects within a storage
          // bucket before issuing the delete command.
          'cnrm.cloud.google.com/force-destroy': 'true',
        },
        labels: {
          app: appName,
        },
        name: config.bucket_name,
        namespace: namespace,
      },
      spec: {
        // Enable IAM policies for this bucket.
        uniformBucketLevelAccess: true,

        // Delete objects in the bucket after 1 day. We copy to BigQuery within minutes of writing
        // to the bucket, so this is plenty of time.
        lifecycleRule: [
          {
            action: {
              type: 'Delete',
            },
            condition: {
              age: 1,
            },
          },
        ],
      },
    },
    // Give access to the bucket.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-storage-bucket-policy' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'StorageBucket',
          name: config.bucket_name,
        },
        bindings: [
          {
            role: 'roles/storage.objectUser',
            members: [
              { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
            ],
          },
        ],
      },
    },

    // Grant permission to run BigQuery jobs. This is needed for loading information from the
    // bucket to BigQuery.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-bigquery-job-policy' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: 'project/%s' % projectId,
        },
        bindings: [
          {
            role: 'roles/bigquery.jobUser',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
        ],
      },
    },
  ];

  local container = {
    name: appName,
    target: {
      name: targetName,
      dst: 'request_insight_bigquery_full_exporter',
    },
    volumeMounts: [
      {
        name: 'config',
        mountPath: '/config',
        readOnly: true,
      },
    ],
    resources: {
      limits: {
        cpu: 0.5,
        // We're writing big batches. Make sure we have plenty of RAM.
        memory: '2Gi',
      },
    },
  };
  local pod = {
    priorityClassName: cloudInfo.envToPriorityClass(env),
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    // Give a very generous termination grace period in case we request termination in the middle
    // of a BigQuery load job. The common case should be that the pod terminates immediately.
    terminationGracePeriodSeconds: 300,
    volumes: [
      {
        name: 'config',
        configMap: {
          name: '%s-config' % appName,
          items: [
            {
              key: 'config.json',
              path: 'config.json',
            },
          ],
        },
      },
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  // we allow multiple exporters on the same host as they run in the background
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  lib.flatten([
    configMap,
    serviceAccount.objects,
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        minReadySeconds: 0,
        // Note(jacqueline): Normally I wouldn't call out a specific namespace like this (we should
        // autoscale instead), but we've been trying to deprecate this exporter for a long time so
        // I'm ok with being a little hacky here.
        replicas: if namespace == 'i1' then 16 else 2,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod + {
            tolerations: tolerations,
            affinity: affinity,
          },
        },
      },
    },
    gcpObjects,
  ])

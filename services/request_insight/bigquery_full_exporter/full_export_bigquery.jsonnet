function(cloud, env, namespace, namespace_config)
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];
  local externalSaLib = import 'services/request_insight/external_service_accounts_lib.jsonnet';
  local bigqueryLib = (import 'services/request_insight/bigquery_full_exporter/full_export_bigquery_lib.jsonnet')(cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery);
  local appName = 'request-insight-full-export';
  // Create a cloud identity group (i.e., google group) for controlling access to the BigQuery
  // dataset. This is necessary because datasets don't support IAM. We don't want to grant new
  // tenants access to the dataset manually, so instead we grant access to a group which is
  // added to by RequestInsight deployments. For details on why we can't use IAM, see
  // https://cloud.google.com/config-connector/docs/reference/resource-docs/bigquery/bigquerydataset
  local cloudIdentityGroupName = bigqueryLib.cloudIdentityGroup;
  local cloudIdentityGroup = {
    apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
    kind: 'CloudIdentityGroup',
    metadata: {
      name: cloudIdentityGroupName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Full Request Insight export CloudIdentityGroup for %s' % env,
      parent: 'customers/C02kn0kha',
      groupKey: {
        id: '%<EMAIL>' % cloudIdentityGroupName,
      },
      initialGroupConfig: 'WITH_INITIAL_OWNER',
      labels: {
        'cloudidentity.googleapis.com/groups.discussion_forum': '',
      },
    },
  };

  local datasetName = bigqueryLib.dataset;
  local datasetResourceId = bigqueryLib.datasetGcp;

  // In staging/prod engineers have read-only access to prevent accidental deletions.
  local datasetEngAccess = [
    {
      role: if env == 'DEV' then 'OWNER' else 'READER',
      groupByEmail: '<EMAIL>',
    },
  ];
  // For simplicity service accounts have write access to the dataset. Technically they only need
  // metadata read access on the dataset and write access on the tables, but that significantly
  // complicates configuration and we should catch things like table deletions in code review.
  local datasetServiceAccess = [
    {
      role: 'WRITER',
      groupByEmail: '%<EMAIL>' % cloudIdentityGroupName,
    },
  ];
  // Give CoreWeave access to read from the full export dataset.
  local datasetExternalServiceAccess = if cloud == 'GCP_US_CENTRAL1_DEV' then [] else [
    {
      role: 'READER',
      userByEmail: externalSaLib.getExternalServiceAccounts(env, cloud).coreweaveRIDataImporterSA.email,
    },
  ];
  // BigQuery datasets require an explicit owner, and configconnector needs admin privileges to be
  // able to run updates.
  local datasetAdminAccess = [
    {
      role: 'OWNER',
      userByEmail: '<EMAIL>',
    },
  ];

  local dataset = {
    apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
    kind: 'BigQueryDataset',
    metadata: {
      name: datasetName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Full Request Insight export dataset for %s' % env,
      // resourceID is the name the dataset will have in GCP. (Kubernetes doesn't allow names with
      // underscores and BigQuery doesn't allow names with dashes.)
      resourceID: datasetResourceId,
      projectRef: {
        external: cloudInfo.projectId,
      },
      // BigQueryDataset doesn't support IAM and uses legacy role formats. See
      // https://cloud.google.com/config-connector/docs/reference/resource-docs/bigquery/bigquerydataset
      access: datasetEngAccess + datasetServiceAccess + datasetExternalServiceAccess + datasetAdminAccess,
    },
  };

  local userEventSchema = [
    {
      name: 'session_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The session ID of the event.',
    },
    {
      name: 'user_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The ID of the user who performed the event.',
    },
    {
      name: 'tenant',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The tenant of the request',
    },
    {
      name: 'namespace',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'The namespace of the request',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
    {
      name: 'event_type',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The type of event. This corresponds to one of the event types in the request_insight proto (e.g., "text_edit").',
    },
    {
      name: 'file_path',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'The path of the file the event applies to. Empty string for events that do not apply to a file.',
    },
    {
      name: 'raw_proto',
      type: 'BYTES',
      mode: 'NULLABLE',
      description: 'DEPRECATED. Use raw_json instead.',
    },
    {
      name: 'raw_json',
      type: 'JSON',
      mode: 'NULLABLE',
      description: 'The raw proto of the event, in JSON format.',
    },
    // The columns below are pulled out for easy filtering, since we can't query on proto bytes.
    {
      name: 'request_id',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'DEPRECATED. Query raw_json for completion_request_id_issued and edit_request_id_issued events instead.',
    },
  ];

  local tablePrefix = bigqueryLib.tablePrefix;
  local tables = [
    {
      apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
      kind: 'BigQueryTable',
      metadata: {
        name: '%s-user-event-table' % tablePrefix,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        description: 'Full Request Insight export BigQuery table for user events',
        // resourceID is the name the table will have in GCP. (Kubernetes doesn't allow names with
        // underscores and BigQuery doesn't allow names with dashes.)
        resourceID: 'user_event',
        datasetRef: {
          external: datasetResourceId,
        },
        schema: std.toString(userEventSchema),
      },
    },
  ];


  [
    cloudIdentityGroup,
    dataset,
  ] + tables

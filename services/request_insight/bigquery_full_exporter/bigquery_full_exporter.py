"""Exporter for writing to a single table in the full export BigQuery dataset.

The full contents of request insight events are large enough that we can't do the simple thing and
write them directly to BigQuery. See the BigQueryFullExporter docs for details.
"""

import json
import signal
import time
import uuid
from typing import Callable, Optional

import structlog
from google.api_core.exceptions import Forbidden, NotFound
from google.cloud import bigquery, storage  # type: ignore
from prometheus_client import Gauge, Histogram

import services.request_insight.request_insight_pb2 as request_insight_pb2
from services.request_insight.lib.request_insight_subscriber import (
    RequestInsightSubscriber,
)

log = structlog.get_logger()


# Keep track of expected startup failures so that we can page if it lasts for longer than
# expected. See _connect docs. Status is "FORBIDDEN" or "NOT_FOUND".
_dataset_startup_error_gauge = Gauge(
    "au_request_insight_full_export_bigquery_startup_error_gauge",
    ">0 iff dataset connection is startup_error",
    ["table", "status"],
)

# Latency of GCS writes. Status is "OK" or "ERROR".
_gcs_write_latency = Histogram(
    "au_request_insight_full_export_gcs_write_latency",
    "Latency of writing to GCS",
    ["table", "status"],
)

# Latency of BigQuery writes. This is a gauge instead of a histogram because we only write to
# BigQuery once every 5 minutes. We'd actually like to graph the last value and not a percentile.
_bigquery_write_latency = Gauge(
    "au_request_insight_full_export_bigquery_write_latency",
    "Latency of writing to BigQuery",
    ["table"],
)

# Batch size in bytes. This is what determines whether we write directly to BigQuery or go through
# a GCS bucket first.
_batch_size_bytes = Histogram(
    "au_request_insight_full_export_batch_size_bytes",
    "Batch size in bytes",
    ["table"],
    buckets=sorted({int(2**x) for x in range(1, 25)}),
)


class BigQueryFullExporter:
    """Exporter for writing "full" request insight event data to BigQuery.

    Most message batches are written directly to BigQuery via the BigQuery API. For larger batches,
    the export is done in two steps:
    1. Upload a batch of rows (in newline-delimited JSON format) to a GCS bucket.
    2. Start a BigQuery load job to load the data from GCS to BigQuery.

    We can't always upload directly to BigQuery via the BigQuery API because the API has a 10MB
    request limit (even though the actual limit for a BigQuery row is 100MB), and JSON is verbose
    enough that a 4MB proto can easily hit that limit. We can't write each batch to GCS and then
    immediately to BigQuery because BigQuery has a 1500 jobs per table per day limit. Instead, we
    write to the same GCS "directory" for a while and then upload the entire directory periodically.

    We can't write everything via the GCS bucket because pub/sub has an (undocumented?) 5k limit on
    outstanding messages which would result in the exporter spending much of its time sitting idly
    while the queue builds up.

    An alternative implementation would be to dynamically construct batches up to a certain
    threshold before making a BigQuery API request, and only do the intermediate GCS upload for
    individual messages over the API limit. This would be more complex to implement and would make
    it easier for the GCS path to grow stale, since it wouldn't be excercised as much.

    There are some configuration requirements that need to be met for this scheme to work:
    1. The subscription's ack deadline must be set to a value that is significantly greater than the
       provided batch_interval_seconds, to prevent the pub/sub queue from retrying a message while
       it is still waiting to be uploaded to BigQuery.
    2. The subscription should have exactly once delivery enabled, again to prevent unwanted
       retries.
    3. The bucket should have a lifecycle hook that cleans up old objects.
    4. The pod should have a generous terminationGracePeriodSeconds configured to help avoid
       duplicated data if we shut down in the middle of a BigQuery load job.

    Callers should construct the exporter and then call `run`. They are responsible for providing
    a function that constructs BigQuery rows from a request insight message (see constructor).

    Each instance of this exporter writes to a single BigQuery table. If you need to export to
    multiple tables you should run multiple exporters.
    """

    def __init__(
        self,
        subscriber: RequestInsightSubscriber,
        dataset_name,
        table_name,
        bucket_name,
        batch_interval_seconds: int,
        large_batch_threshold_bytes: int,
        rows_fn: Callable[[request_insight_pb2.RequestInsightMessage], list[dict]],
    ):
        """Constructor.

        Args:
            subscriber: The subscriber to use for listening to request insight events.
            dataset_name: The name of the BigQuery dataset to write to.
            table_name: The name of the BigQuery table to write to.
            bucket_name: The name of the GCS bucket to write to.
            batch_interval_seconds: The interval at which to write batches to BigQuery. This
                interval must be set so that no table is written to more than 1500 times per day.
            rows_fn: A function that takes a request insight message and returns a list of rows that
                can be json-serialized and written to BigQuery.
        """
        self._subscriber = subscriber
        self._dataset_name = dataset_name
        self._table_name = table_name
        self._bucket_name = bucket_name
        self._batch_interval_seconds = batch_interval_seconds
        self._large_batch_threshold_bytes = large_batch_threshold_bytes
        self._rows_fn: Callable[
            [request_insight_pb2.RequestInsightMessage], list[dict]
        ] = rows_fn

        self._bucket = storage.Client().bucket(bucket_name)
        self._job_config: bigquery.LoadJobConfig = bigquery.LoadJobConfig(
            write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
            source_format=bigquery.SourceFormat.NEWLINE_DELIMITED_JSON,
        )

        # Keep track of the state of our "large" batch (written to GCS and periodically loaded to
        # BigQuery.)
        self._curr_large_batch_id: str = uuid.uuid4().hex
        self._curr_large_batch_start_time: float = time.time()
        self._curr_large_batch_ack_ids: list[str] = []

        # These are initialized in connect().
        self._bigquery_client: Optional[bigquery.Client] = None
        self._table: Optional[bigquery.Table] = None

        # Register signal handlers so that we can exit gracefully. See _request_graceful_exit docs.
        self._exit_now = False
        signal.signal(signal.SIGINT, self._request_graceful_exit)
        signal.signal(signal.SIGTERM, self._request_graceful_exit)

    def run(self):
        """Run the exporter.

        Does not return.
        """
        log.info("Connecting to BigQuery... This can take a while the first time.")
        self._connect()

        if self._bigquery_client is None or self._table is None:
            raise ValueError(
                "BigQuery client not connected. This indicates a bug in connect()."
            )

        log.info("Starting export loop...")
        self._curr_large_batch_id = uuid.uuid4().hex
        while not self._exit_now:
            try:
                message_batch = self._subscriber.get_batch()
            except Exception as ex:  # pylint: disable=broad-except
                log.error("Error getting pub/sub message batch: %s", ex)
                log.exception(ex)
                continue

            # Process the current pub/sub batch, writing either to BigQuery or GCS depending on its
            # size.
            self._process_batch(message_batch)

            # If enough time has elapsed, write the last set of large batches to BigQuery.
            if (
                time.time() - self._curr_large_batch_start_time
                > self._batch_interval_seconds
            ):
                if self._curr_large_batch_ack_ids:
                    status = "UNKNOWN"
                    try:
                        self._publish_large_batch()
                        self._subscriber.ack_batch(self._curr_large_batch_ack_ids)
                        status = "OK"
                    except Exception as ex:  # pylint: disable=broad-except
                        log.error("Error publishing batch to BigQuery: %s", ex)
                        log.exception(ex)
                        status = "ERROR"
                    finally:
                        # This is where we record successful writes, since at this point the data
                        # is in BigQuery.
                        self._subscriber.message_count.labels(
                            self._subscriber.subscription_description, status
                        ).inc(len(self._curr_large_batch_ack_ids))

                # Start a new batch. We do this even if writing the batch to BigQuery failed, so
                # that we have a chance of making progress even if we have malformed data, and to
                # make it less likely that we write duplicate data if the pub/sub message gets
                # retried.
                self._curr_large_batch_id = uuid.uuid4().hex
                self._curr_large_batch_start_time = time.time()
                self._curr_large_batch_ack_ids = []

        log.info("Stopped export loop gracefully.")

    def _connect(self):
        """Connect to BigQuery.

        This method blocks until we are able to successfully connect to BigQuery. This is expected
        to take a while the first time request-insight is deployed in a tenant because
        CloudIdentityMemberships are slow to propagate through Google's system (anecdotally
        it can take up to 10 minutes). Arguably we should just crash and let kubernetes restarts
        deal with it, but we don't want to confuse people with a failing deployment for an expected
        issue. We keep track of a metric for containers stuck in this state so that we can page on
        it if it never resolves itself.
        """
        while True:
            try:
                self._bigquery_client = bigquery.Client()
                self._table = self._bigquery_client.get_table(
                    f"{self._dataset_name}.{self._table_name}"
                )
                _dataset_startup_error_gauge.labels(self._table_name, "FORBIDDEN").set(
                    0
                )
                _dataset_startup_error_gauge.labels(self._table_name, "NOT_FOUND").set(
                    0
                )
                break
            except Forbidden as ex:
                _dataset_startup_error_gauge.labels(self._table_name, "FORBIDDEN").set(
                    1
                )
                log.warning(
                    "Forbidden to connect to BigQuery. This is expected the first time "
                    "request-insight is deployed in a namespace. Will retry in 30 seconds. "
                    "Error: %s",
                    ex,
                )
                time.sleep(30)
            except NotFound as ex:
                _dataset_startup_error_gauge.labels(self._table_name, "NOT_FOUND").set(
                    1
                )
                log.error(
                    "Not found error connecting to BigQuery. This can happen if request-insight is "
                    "deployed before a table creation is completed. This is unexpected, since the "
                    "BigQuery deployments runs with higher priority.  Will retry in 30 seconds. "
                    "Error: %s",
                    ex,
                )
                time.sleep(30)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                log.error("Unexpected error connecting to BigQuery: %s", ex)
                log.exception(ex)
                raise

    def _process_batch(
        self, message_batch: list[RequestInsightSubscriber.MessageAndAckId]
    ):
        """Process a single batch of messages.

        This method will write the batch to either BigQuery or GCS, depending on its size. The
        various large batch tracking variables are updated appropriately.

        Args:
            message_batch: A list of RequestInsightSubscriber.MessageAndAckId objects, from a call
                to RequestInsightSubscriber.get_batch().
        """
        if self._bigquery_client is None or self._table is None:
            raise ValueError(
                "BigQuery client not connected. Did you forget to call connect()?"
            )

        message_count = 0
        small_batch_message_count = 0
        ack_now = []
        try:
            message_count = len(message_batch)
            if message_count == 0:
                return

            rows = []
            ack_ids = []
            for message_and_ack_id in message_batch:
                message = message_and_ack_id.message
                ack_id = message_and_ack_id.ack_id

                new_rows = self._rows_fn(message)
                rows.extend(new_rows)

                # Messages that didn't result in any new rows will be acked immediately.
                # Otherwise, when they're acked depends on the size of the batch.
                if new_rows:
                    ack_ids.append(ack_id)
                else:
                    ack_now.append(ack_id)

            if rows:
                # If this batch is small enough, write it to BigQuery immediately. This is a
                # bit of a hack, but helps us get around the pub/sub library's limit of 5k
                # outstanding messages. See class documentation for details.
                rows_size = sum(len(json.dumps(row)) for row in rows)
                _batch_size_bytes.labels(self._table_name).observe(rows_size)
                if rows and rows_size < self._large_batch_threshold_bytes:
                    log.info(
                        "Batch size is %d bytes. Writing %d rows directly to BigQuery.",
                        rows_size,
                        len(rows),
                    )
                    self._bigquery_client.insert_rows(self._table, rows)
                    small_batch_message_count += len(ack_ids)
                    ack_now.extend(ack_ids)

                    # This log is here because we saw an instance of a pod hanging while either
                    # writing to BigQuery or ack'ing the batch. If it happens again this tells us
                    # which it was.
                    log.info("Finished writing %d rows to BigQuery.", len(rows))
                else:
                    log.info("Batch size is %d bytes. Writing to GCS.", rows_size)
                    self._add_to_large_batch(rows)
                    self._curr_large_batch_ack_ids.extend(ack_ids)
        except Exception as ex:  # pylint: disable=broad-except
            log.error("Error adding pub/sub message to batch: %s", ex)
            log.exception(ex)

            # We only count errors at this point because we haven't actually finished processing
            # this batch. We record "OK" after successfully writing to BigQuery below.
            self._subscriber.message_count.labels(
                self._subscriber.subscription_description, "ERROR"
            ).inc(message_count)

        # Ack messages that we wrote directly to BigQuery or that didn't have any events we were
        # Doing this now helps keep us under the 5k outstanding messages limit.
        if ack_now:
            status = "UNKNOWN"
            try:
                self._subscriber.ack_batch(ack_now)
                status = "OK"
            except Exception as ex:  # pylint: disable=broad-except
                log.error("Error acknowledging pub/sub message: %s", ex)
                log.exception(ex)
                status = "ERROR"
            finally:
                self._subscriber.message_count.labels(
                    self._subscriber.subscription_description, status
                ).inc(small_batch_message_count)

    def _add_to_large_batch(self, rows: list[dict[str, object]]):
        """Add rows to the current batch.

        Args:
            rows: A list of rows to add to the current batch. This should already be in the format
                expected by BigQuery load jobs.
        """
        if len(rows) == 0:
            log.info("Nothing to add to batch.")
            return

        status = "UNKNOWN"
        start_time = time.time()
        try:
            # Write to a unique object name within the current batch.
            gcs_object_name = f"{self._curr_large_batch_id}/{uuid.uuid4().hex}"
            log.info(
                "Writing %d events to gs://%s/%s",
                len(rows),
                self._bucket_name,
                gcs_object_name,
            )
            gcs_object = self._bucket.blob(gcs_object_name)
            gcs_object.upload_from_string("\n".join([json.dumps(row) for row in rows]))
            status = "OK"
        except Exception:  # pylint: disable=broad-except
            # This will be logged by the caller.
            status = "ERROR"
            raise
        finally:
            _gcs_write_latency.labels(self._table_name, status).observe(
                time.time() - start_time
            )

    def _publish_large_batch(self):
        """Publish the current batch to BigQuery.

        Callers are expected to ensure that there is data in the current batch.

        Calls to this method should be rate-limited, to avoid hitting the 1500 jobs per table per
        day limit.
        """
        if self._bigquery_client is None or self._table is None:
            raise ValueError(
                "BigQuery client not connected. Did you forget to call connect()?"
            )

        start_time = time.time()
        try:
            source_uri = f"gs://{self._bucket_name}/{self._curr_large_batch_id}/*"
            log.info(
                "Loading events from %s to %s",
                source_uri,
                self._table.full_table_id,
            )
            load_job = self._bigquery_client.load_table_from_uri(
                source_uri,
                destination=self._table,
                job_config=self._job_config,
            )

            # This will raise an exception if the job fails.
            load_job.result()
        finally:
            _bigquery_write_latency.labels(self._table_name).set(
                time.time() - start_time
            )

    def _request_graceful_exit(self, signum, frame):
        """Callback to handle an exit signal.

        We want to give the current export loop a chance to finish, to avoid terminating between
        writing to BigQuery and acknowledging a batch, which would lead to duplicated data.
        """
        log.warn("Captured signal %s: %s", signum, frame)
        log.info(
            "Setting exit flag to give the current export loop a chance to finish."
        )
        self._exit_now = True

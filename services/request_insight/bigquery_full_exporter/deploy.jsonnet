local deployLib = import 'services/request_insight/bigquery_full_exporter/deploy_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  if !namespace_config.flags.exportFullData then [] else
    local userEventExportObjects = deployLib(
      env=env,
      namespace=namespace,
      cloud=cloud,
      useSharedDevRequestInsightBigquery=namespace_config.flags.useSharedDevRequestInsightBigquery,
      appName='ri-user-event-bigquery-full-exporter',
      shortAppName='ri-bq-user-exp',
      subscriptionName='%s-ri-user-event-full-export-bigquery-sub' % namespace,
      subscriptionDescription='ri-user-event-bigquery-full-exporter',
      tableName='user_event',
      targetName='//services/request_insight/bigquery_full_exporter:user_event_exporter_image',
    );

    userEventExportObjects

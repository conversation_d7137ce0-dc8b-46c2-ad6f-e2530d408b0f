"""Exporter for writing user event data to BigQuery.

This code should only be deployed for customers who have given us explicit permission to export
and train on their data! See bigquery_exporter for the sanitized exporter used for analytics.
"""

import argparse
import pathlib
from functools import partial

import structlog
from google.protobuf.json_format import MessageToDict
from prometheus_client import start_http_server

import services.request_insight.request_insight_pb2 as request_insight_pb2
from base.logging.struct_logging import setup_struct_logging
from services.request_insight.bigquery_full_exporter.bigquery_full_exporter import (
    BigQueryFullExporter,
)
from services.request_insight.bigquery_full_exporter.config import _load_config
from services.request_insight.lib.request_insight_subscriber import (
    RequestInsightSubscriber,
)

log = structlog.get_logger()


def _user_event_rows(
    ri_message: request_insight_pb2.RequestInsightMessage,
    namespace_name: str,
) -> list[dict[str, object]]:
    """Returns rows that can be written to the user_event table."""
    if ri_message.WhichOneof("message") != "record_full_export_user_events_request":
        log.debug("Skipping %s message", ri_message.WhichOneof("message"))
        return []

    rows = []
    request = ri_message.record_full_export_user_events_request
    for event in request.extension_data.user_events:
        event_type = event.WhichOneof("event")
        if event_type is None:
            log.info(
                "Skipping missing or unrecognized event. This is expected during the rollout of a "
                "new event"
            )
            continue

        event_dict = MessageToDict(
            getattr(event, event_type), preserving_proto_field_name=True
        )  # type: ignore
        rows.append(
            {
                "session_id": request.session_id,
                "user_id": request.user_id,
                "tenant": request.tenant_info.tenant_name or namespace_name,
                "namespace": namespace_name,
                "time": event.time.ToDatetime().isoformat(),
                "event_type": event_type,
                "file_path": event.file_path,
                "raw_json": event_dict,
            }
        )

    return rows


def main():
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    log.info("Args %s", args)

    config = _load_config(args.config_file)
    log.info("Config: %s", config)

    start_http_server(9090)

    subscriber = RequestInsightSubscriber(
        config.project_id,
        config.topic_name,
        config.subscription_name,
        config.subscription_description,
        config.subscription_batch_size,
    )

    exporter = BigQueryFullExporter(
        subscriber,
        config.bigquery_dataset_name,
        config.table_name,
        config.bucket_name,
        config.batch_interval_seconds,
        config.large_batch_threshold_bytes,
        partial(_user_event_rows, namespace_name=config.namespace_name),
    )
    exporter.run()


if __name__ == "__main__":
    main()

"""Unit tests for the full Request Insight BigQuery exporter."""

from unittest.mock import MagicMock

import pytest
from google.cloud import bigquery, storage

from services.request_insight.bigquery_full_exporter.bigquery_full_exporter import (
    BigQueryFullExporter,
)
from services.request_insight.lib.request_insight_subscriber import (
    RequestInsightSubscriber,
)
import services.request_insight.request_insight_pb2 as request_insight_pb2


@pytest.fixture(scope="function")
def mock_gcs_object():
    """Fixture to setup a mock GCS object."""
    yield MagicMock(storage.Blob)


@pytest.fixture(scope="function")
def mock_exporter(mock_gcs_object):
    """Fixture to setup a mock BigQueryFullExporter."""
    subscriber = MagicMock()

    rows_fn = MagicMock(return_value=[{"a": 1}, {"b": 2}])
    exporter = BigQueryFullExporter(
        subscriber, "test_dataset", "test_table", "test_bucket", 1, 1000, rows_fn
    )
    exporter._bigquery_client = MagicMock(bigquery.Client)
    exporter._table = MagicMock(bigquery.Table)
    exporter._bucket = MagicMock(storage.Bucket)
    exporter._bucket.blob.return_value = mock_gcs_object
    exporter._curr_large_batch_id = "test_batch_id"
    yield exporter


def test_add_to_large_batch(mock_exporter, mock_gcs_object):
    mock_exporter._add_to_large_batch([{"a": 1}, {"b": 2}])
    assert mock_exporter._bucket.blob.call_count == 1
    assert mock_exporter._bucket.blob.call_args[0][0].startswith("test_batch_id/")
    assert mock_gcs_object.upload_from_string.call_count == 1
    assert mock_gcs_object.upload_from_string.call_args[0][0] == '{"a": 1}\n{"b": 2}'


def test_add_to_large_batch_empty(mock_exporter):
    mock_exporter._add_to_large_batch([])
    assert mock_exporter._bucket.blob.call_count == 0


def test_publish_large_batch(mock_exporter):
    mock_exporter._publish_large_batch()
    assert mock_exporter._bigquery_client.load_table_from_uri.call_count == 1
    assert mock_exporter._bigquery_client.load_table_from_uri.call_args[0][0] == (
        "gs://test_bucket/test_batch_id/*"
    )


def test_process_batch_small(mock_exporter):
    # Set threshold so that our batch will be considered small
    mock_exporter._large_batch_threshold_bytes = 1000
    mock_exporter._process_batch(
        [
            RequestInsightSubscriber.MessageAndAckId(
                request_insight_pb2.RequestInsightMessage(), "ack1"
            ),
            RequestInsightSubscriber.MessageAndAckId(
                request_insight_pb2.RequestInsightMessage(), "ack2"
            ),
        ]
    )

    assert mock_exporter._bucket.blob.call_count == 0
    assert mock_exporter._bigquery_client.insert_rows.call_count == 1
    assert mock_exporter._subscriber.ack_batch.call_count == 1
    assert mock_exporter._subscriber.ack_batch.call_args[0][0] == [
        "ack1",
        "ack2",
    ]


def test_process_batch_large(mock_exporter):
    # Set threshold so that our batch will be considered large
    mock_exporter._large_batch_threshold_bytes = 1
    mock_exporter._process_batch(
        [
            RequestInsightSubscriber.MessageAndAckId(
                request_insight_pb2.RequestInsightMessage(), "ack1"
            ),
            RequestInsightSubscriber.MessageAndAckId(
                request_insight_pb2.RequestInsightMessage(), "ack2"
            ),
        ]
    )

    assert mock_exporter._bucket.blob.call_count == 1
    assert mock_exporter._bigquery_client.insert_rows.call_count == 0
    assert mock_exporter._subscriber.ack_batch.call_count == 0

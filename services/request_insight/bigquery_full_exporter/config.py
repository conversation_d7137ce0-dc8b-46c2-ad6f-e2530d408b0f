"""Module for the configuration for the request insight BigQuery exporter."""

import pathlib
from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class Config:
    """Configuration for the request insight BigQuery exporter."""

    project_id: str
    """The project id of the pub/sub topic/subscription."""

    topic_name: str
    """The name of the pub/sub topic to listen to."""

    subscription_name: str
    """The name of the subscription to register, as it exists in GCP."""

    subscription_description: str
    """A description of the subscription, shared across tenants. Used for metrics."""

    subscription_batch_size: int
    """The batch size to use for fetching subscription messages."""

    bigquery_dataset_name: str
    """The name of the BigQuery dataset to write to."""

    table_name: str
    """The name of the BigQuery table to write to."""

    namespace_name: str
    """The name of the namespace (recorded to BigQuery)."""

    bucket_name: str
    """The name of the GCS bucket to write data to.

    This is an intermediate step in processing to get around the BigQuery API's 10MB limit."""

    batch_interval_seconds: int
    """The interval between batches, in seconds.

    BigQuery has a limit of 1500 jobs per table per day."""

    large_batch_threshold_bytes: int
    """The maximum number of bytes to send directly to BigQuery.

    Anything above this limit will be written to GCS first and then loaded via a load job to
    BigQuery. See BigQueryFullExporter docs for details."""


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )

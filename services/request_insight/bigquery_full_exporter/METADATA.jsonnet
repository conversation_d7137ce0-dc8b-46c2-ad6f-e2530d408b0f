// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
local fullExportTablesToDelete = [
  'chat-host-request-view',
  'chat-host-response-view',
  'completion-event-table',
  'completion-host-request-partitioned',
  'completion-host-request-view',
  'edit-host-request-view',
  'edit-host-response-view',
  'extension-session-event-view',
  'instruction-host-request-view',
  'instruction-host-response-view',
  'next-edit-feedback-view',
  'next-edit-host-request-view',
  'next-edit-host-response-view',
  'next-edit-resolution-view',
  'next-edit-session-event-view',
  'next-edit-user-event-view',
  'preference-sample-view',
  'request-event',
  'request-metadata-view',
  'session-event-table',
  'smart-paste-client-timeline-view',
  'smart-paste-resolution-view',
];

{
  deployment: [
    // BigQuery dataset for contractor data export. This contains data from just the tenants who
    // have given us permission to export and train on their sensitive data.
    {
      name: 'request-insight-full-export-dataset',
      priority: 100,  // We want tables to be created/modified before request-insight deploys.
      kubecfg: {
        target: '//services/request_insight/bigquery_full_exporter:full_export_bigquery_kubecfg',
        task: [
          // Shared BigQuery dataset in the dev cluster.
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'STAGING',
            namespace: 'central-dev',
          },

          // Staging BigQuery dataset in the prod cluster.
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },

          // Prod BigQuery dataset in the prod cluster.
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#system-services',
        },
      },
    },
    // Export full RequestInsight protos to BigQuery (for use by researchers).
    // The kubecfg for this target only deploys if the exportFullData flag is enabled.
    {
      name: 'request-insight-bigquery-full-exporter',
      kubecfg: {
        target: '//services/request_insight/bigquery_full_exporter:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

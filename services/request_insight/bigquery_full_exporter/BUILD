load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("//tools/bzl:metadata.bzl", "metadata_test")

py_library(
    name = "config",
    srcs = [
        "config.py",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "bigquery_full_exporter",
    srcs = [
        "bigquery_full_exporter.py",
    ],
    deps = [
        requirement("prometheus-client"),
        ":config",
        "//base/logging:struct_logging",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/lib:request_insight_subscriber",
        requirement("google-cloud-bigquery"),
        requirement("google-cloud-storage"),
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "bigquery_full_exporter_test",
    srcs = ["bigquery_full_exporter_test.py"],
    deps = [
        ":bigquery_full_exporter",
    ],
)

py_binary(
    name = "user_event_exporter",
    srcs = [
        "user_event_exporter.py",
    ],
    deps = [
        ":bigquery_full_exporter",
        ":config",
        "//base/logging:struct_logging",
        "//services/request_insight:request_insight_py_proto",
        requirement("dataclasses_json"),
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "user_event_exporter_test",
    srcs = ["user_event_exporter_test.py"],
    deps = [
        ":user_event_exporter",
    ],
)

py_oci_image(
    name = "user_event_exporter_image",
    package_name = package_name(),
    binary = ":user_event_exporter",
)

kubecfg_library(
    name = "deploy_lib",
    srcs = [
        "deploy_lib.jsonnet",
    ],
    deps = [
        ":full_export_bigquery_lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":user_event_exporter_image",
    ],
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        ":deploy_lib",
    ],
)

kubecfg(
    name = "full_export_bigquery_kubecfg",
    src = "full_export_bigquery.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        ":full_export_bigquery_lib",
        "//deploy/common:cloud_info",
        "//services/request_insight:external_service_accounts_lib",
    ],
)

jsonnet_library(
    name = "full_export_bigquery_lib",
    srcs = [
        "full_export_bigquery_lib.jsonnet",
    ],
    visibility = ["//services/request_insight:__subpackages__"],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":full_export_bigquery_kubecfg",
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

function(cloud, env, namespace, useSharedDevRequestInsightBigquery)
  // By default, we use a shared dataset, but this feature flag can override that
  // for devs who want to test on their own copy.
  local devNotShared = env == 'DEV' && !useSharedDevRequestInsightBigquery;

  local cloudIdentityGroup =
    if devNotShared then
      '%s-request-insight-full-export-dataset-access-group' % namespace
    else if cloud == 'GCP_US_CENTRAL1_DEV' then
      'dev-request-insight-full-export-dataset-access-group'
    else
      '%s-request-insight-full-export-dataset-access-group' % std.asciiLower(env);

  local dataset =
    if devNotShared then
      '%s-request-insight-full-export-dataset' % namespace
    else if cloud == 'GCP_US_CENTRAL1_DEV' then
      'dev-request-insight-full-export-dataset'
    else
      '%s-request-insight-full-export-dataset' % std.asciiLower(env);

  local tablePrefix =
    if devNotShared then
      '%s-request-insight-full-export' % namespace
    else if cloud == 'GCP_US_CENTRAL1_DEV' then
      'dev-request-insight-full-export'
    else
      '%s-request-insight-full-export' % std.asciiLower(env);

  {
    cloudIdentityGroup: cloudIdentityGroup,
    dataset: dataset,
    datasetGcp: std.strReplace(dataset, '-', '_'),
    tablePrefix: tablePrefix,
  }

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local projectId = cloudInfo[cloud].projectId;
  local requestInsightPublisher = requestInsightPublisherLib(cloud, env, namespace, appName='request-insight');

  local publisherConfig = {
    project_id: projectId,
    topic_name: requestInsightPublisher.topicName,
  };
  local publisherConfigMap = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: requestInsightPublisher.configMapName,
      namespace: namespace,
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
      labels: {
        app: 'request-insight',
      },
    },
    data: {
      [requestInsightPublisher.configMapKeyName]: std.manifestJson(publisherConfig),
    },
  };

  local pubsubTopic = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubTopic',
    metadata: {
      name: requestInsightPublisher.topicName,
      namespace: namespace,
      // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
      // topic is deleted before the pod.
      annotations: if env == 'DEV' then {
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      } else {},
      labels: {
        app: 'request-insight',
      },
    },
    spec: {
    },
  };

  // Create IAMPartialPolicy to grant ra-ri-publisher service account access to publish to the topic
  // This will allow the Remote Agent workspaces to publish logs to the topic, so restricted customer data
  // can be accessed compliantly.
  local raRiPublisherPolicy = gcpLib.grantAccess(
    name='ra-ri-publisher-policy',
    env=env,
    namespace=namespace,
    appName='request-insight',
    resourceRef={
      kind: 'PubSubTopic',
      name: requestInsightPublisher.topicName,
    },
    bindings=[
      {
        role: 'roles/pubsub.publisher',
        members: [
          // For prod/staging deployments, use the prod service account
          if env != 'DEV' then { member: 'serviceAccount:<EMAIL>' }
          // For dev deployments, use the dev service account
          else { member: 'serviceAccount:<EMAIL>' },
        ],
      },
    ],
    abandon=true,
  );

  [
    publisherConfigMap,
    pubsubTopic,
    raRiPublisherPolicy,
  ]

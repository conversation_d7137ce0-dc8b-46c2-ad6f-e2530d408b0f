function(cloud, env, namespace, namespace_config)
  local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
  local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
  local lib = import 'deploy/common/lib.jsonnet';
  local nodeLib = import 'deploy/common/node-lib.jsonnet';
  local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
  local tenants = import 'deploy/tenants/tenants_lib.jsonnet';
  local datasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';

  local projectId = cloudInfo[cloud].projectId;

  local appName = 'request-insight-feedback-slackbot';
  local shortAppName = 'feedback-bot';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local support_url_base = if env == 'DEV' then
    std.format('https://support.%s.t.us-central1.dev.augmentcode.com/t/{}/request/{}', namespace)
  else
    std.format('https://support.%s.t.us-central1.prod.augmentcode.com/t/{}/request/{}', namespace);

  local genie_url_base = if env == 'DEV' then
    std.format('https://genie.%s.t.%s/?tab=2&tenant={}&redirect_url={}', [namespace, cloudInfo[cloud].internalDomainSuffix])
  else
    std.format('https://genie.%s/?tab=2&tenant={}&redirect_url={}', cloudInfo[cloud].internalDomainSuffix);

  local tenants_without_support_access_control = [
    t.name
    for t in tenants.tenantsWithoutSupportAccessControl
  ];

  local config = {
    project_id: projectId,
    topic_name: '%s-request-insight-topic' % namespace,
    subscription_name: '%s-request-insight-feedback-slackbot-sub' % namespace,
    subscription_description: 'request-insight-feedback-slackbot',
    subscription_batch_size: 100,
    health_file: '/tmp/health',
    prom_port: 9090,
    analytics_dataset_name: datasetLib.datasetGcp,
    // Since the devtools slackbot server is only deployed in the devtools namespace, the endpoint
    // needs to specify the namespace when deploying to prod or staging namespaces. In dev deploys
    // we prefer to point to the same namespace bot for easy development. This is consistent with
    // the dev_deploy script, where this exporter and the bot are both deployed as part of the
    // feedback_slackbot target.
    slackbot_server_endpoint: if env != 'DEV' then 'slack-bot-svc.devtools:80' else 'slack-bot-svc:80',
    support_url_base: support_url_base,
    genie_url_base: genie_url_base,
    tenants_without_support_access_control: tenants_without_support_access_control,
    // Point to #test-feedback-slackbot in dev and #feedback-completions/chat/next-edit otherwise
    channel_id_completions: if env == 'DEV' then 'C07QCPMHXNK' else 'C07CN2BJMHP',
    channel_id_chat: if env == 'DEV' then 'C07QCPMHXNK' else 'C08E89UPASU',
    channel_id_agent: if env == 'DEV' then 'C07QCPMHXNK' else 'C08HWKMJGGY',
    channel_id_remote_agent: if env == 'DEV' then 'C07QCPMHXNK' else 'C08QLF40J1M',
    // NOTE(mpauly): Next edit team members would like to have feedback reported in
    // #feedback-next-edit even when running against a dev deploy
    channel_id_next_edit: 'C07C2PUJF4N',
    channel_id_slackbot: if env == 'DEV' then 'C07QCPMHXNK' else 'C083NQWMVV4',
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local datasetAccess = lib.flatten([
    // Give access to the BigQuery dataset.
    bigqueryLib.datasetAccess(
      namespace,
      appName,
      datasetLib.readonlyCloudIdentityGroup,
      datasetLib.dataNamespace,
      serviceAccount.serviceAccountGcpEmailAddress,
    ),
    // Give permission to start BigQuery jobs (needed to run queries).
    gcpLib.grantAccess(
      name='%s-bigquery-job-policy' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'Project',
        external: 'project/%s' % projectId,
      },
      bindings=[
        {
          role: 'roles/bigquery.jobUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ]
    ),
  ]);

  local gcpObjects = [
    // Pub/sub subscription.
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: config.subscription_name,
        namespace: namespace,
        // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
        // topic is deleted before the pod.
        annotations: if env == 'DEV' then {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        } else {},
        labels: {
          app: appName,
        },
      },
      spec: {
        topicRef: {
          name: config.topic_name,
        },
        ackDeadlineSeconds: 60,
        retryPolicy: {
          minimumBackoff: '5s',
          maximumBackoff: '300s',
        },
        // Retain messages for 1 hour in dev and 7 days in staging/prod.
        messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
        retainAckedMessages: false,
      },
    },
    // Give access to the subscription.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'feedback-slackbot-subscription-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: config.subscription_name,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress,
            ],
          },
        ],
      },
    },
  ];

  local container = {
    name: appName,
    target: {
      name: '//services/request_insight/feedback_slackbot:feedback_slackbot_image',
      dst: 'request_insight_feedback_slackbot',
    },
    volumeMounts: [
      configMap.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 0.5,
        memory: '512Mi',
      },
    },
    args: [
      '--config',
      configMap.filename,
    ],
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.health_file,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.health_file,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 20,
    },
  };

  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
    ],
  };

  local minReplicas = {
    DEV: 1,
    STAGING: 1,
    PROD: 1,
  };
  local maxReplicas = {
    DEV: 2,
    STAGING: 4,
    PROD: 4,
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: 0,
      replicas: minReplicas[env],
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  // Scale when the subscription backlog gets too large.
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: '%s-scaledobject' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      minReplicaCount: minReplicas[env],
      maxReplicaCount: maxReplicas[env],
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            metricName: 'pubsub_googleapis_com:subscription_num_undelivered_messages',
            threshold: '500',
            query: 'sum(avg_over_time(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id="%s"}[1m]))' % config.subscription_name,
          },
        },
      ],
    },
  };

  lib.flatten([
    serviceAccount.objects,
    configMap.objects,
    datasetAccess,
    gcpObjects,
    deployment,
    scaledObject,
  ])

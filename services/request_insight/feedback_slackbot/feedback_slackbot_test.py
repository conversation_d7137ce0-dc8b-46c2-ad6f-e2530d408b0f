"""Unit tests for the feedback slackbot."""

import uuid
import datetime
from unittest.mock import Mock, call
from google.protobuf import timestamp_pb2


import pytest

import tools.bot.bot_pb2 as bot_pb2
from services.request_insight import request_insight_pb2
from services.request_insight.feedback_slackbot.feedback_slackbot import (
    ProcessedRequestIdsCache,
    _process_batch,
)


@pytest.fixture
def slackbot_stub():
    return Mock()


@pytest.fixture
def tenant_info():
    return request_insight_pb2.TenantInfo(
        tenant_id="tenant-id",
        tenant_name="tenant-name",
    )


@pytest.fixture
def tenant_info_with_control():
    return request_insight_pb2.TenantInfo(
        tenant_id="tenant-id-with-control",
        tenant_name="tenant-name-with-control",
    )


class MockBigQueryClient:
    def query_and_wait(self, query, job_config):
        # Mock response for a specific timestamp and channel
        if (
            job_config.query_parameters[0].value == "1234567890123456"
            and job_config.query_parameters[1].value == "C123456"
        ):

            class Row:
                def __init__(self):
                    self.request_id = "test-request-123"
                    self.tenant = "test-tenant"

            return [Row()]
        return []


@pytest.fixture
def process_batch_kwargs():
    return {
        "support_url_base": "tenant={}&request_id={}",
        "genie_url_base": "tenant={}&redirect_url={}",
        "tenants_without_support_access_control": ["tenant-name"],
        "channel_id_completions": "CHANNEL_COMPLETIONS",
        "channel_id_chat": "CHANNEL_CHAT",
        "channel_id_agent": "CHANNEL_AGENT",
        "channel_id_remote_agent": "CHANNEL_REMOTE_AGENT",
        "channel_id_next_edit": "CHANNEL_NEXT_EDIT",
        "channel_id_slackbot": "CHANNEL_SLACKBOT",
        "processed_request_ids": ProcessedRequestIdsCache(),
        "analytics_dataset_name": "test_dataset",
        "bigquery_client": MockBigQueryClient(),
    }


def test_single_request(slackbot_stub, tenant_info, process_batch_kwargs):
    request_id = str(uuid.uuid4())
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great job!",
                        ),
                    ),
                ],
            )
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 1
    slackbot_stub.NotifyUserFeedback.assert_has_calls(
        [
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_COMPLETIONS",
                    tenant_name="tenant-name",
                    original_request_id=request_id,
                    details_url=f"tenant=tenant-name&request_id={request_id}",
                    genie_url="",
                    rating="POSITIVE",
                    note="Great job!",
                )
            ),
        ]
    )


def test_multiple_requests(slackbot_stub, tenant_info, process_batch_kwargs):
    request_id_1 = str(uuid.uuid4())
    request_id_2 = str(uuid.uuid4())
    request_id_3 = str(uuid.uuid4())
    request_id_4 = str(uuid.uuid4())
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id_1,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great job!",
                        ),
                    ),
                ],
            )
        ),
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id_2,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        chat_feedback=request_insight_pb2.ChatFeedback(
                            rating=request_insight_pb2.FeedbackRating.NEGATIVE,
                            note="Not great job",
                        ),
                    ),
                ],
            )
        ),
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id_3,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        next_edit_feedback=request_insight_pb2.NextEditFeedback(
                            note="Test UNSET rating"
                        ),
                    ),
                ],
            )
        ),
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id_4,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        agent_feedback=request_insight_pb2.AgentFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Awesome",
                        ),
                    ),
                ],
            )
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 4
    slackbot_stub.NotifyUserFeedback.assert_has_calls(
        [
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_COMPLETIONS",
                    tenant_name="tenant-name",
                    original_request_id=request_id_1,
                    details_url=f"tenant=tenant-name&request_id={request_id_1}",
                    genie_url="",
                    rating="POSITIVE",
                    note="Great job!",
                )
            ),
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_CHAT",
                    tenant_name="tenant-name",
                    original_request_id=request_id_2,
                    details_url=f"tenant=tenant-name&request_id={request_id_2}",
                    genie_url="",
                    rating="NEGATIVE",
                    note="Not great job",
                )
            ),
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_NEXT_EDIT",
                    tenant_name="tenant-name",
                    original_request_id=request_id_3,
                    details_url=f"tenant=tenant-name&request_id={request_id_3}",
                    genie_url="",
                    rating="UNSET",
                    note="Test UNSET rating",
                )
            ),
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_AGENT",
                    tenant_name="tenant-name",
                    original_request_id=request_id_4,
                    details_url=f"tenant=tenant-name&request_id={request_id_4}",
                    genie_url="",
                    rating="POSITIVE",
                    note="Awesome",
                )
            ),
        ]
    )


def test_ignore_user_or_session_messages(
    slackbot_stub, tenant_info, process_batch_kwargs
):
    request_id = str(uuid.uuid4())
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            record_full_export_user_events_request=request_insight_pb2.RecordFullExportUserEventsRequest(
                user_id="test-user",
                tenant_info=tenant_info,
                extension_data=request_insight_pb2.ExtensionData(
                    user_events=[
                        request_insight_pb2.FullExportUserEvent(
                            text_edit=request_insight_pb2.TextEditEvent(
                                content_changes=[
                                    request_insight_pb2.ContentChange(
                                        text="abc",
                                        range=request_insight_pb2.Range(
                                            start=10,
                                            end=20,
                                        ),
                                    ),
                                ],
                            ),
                        ),
                    ],
                ),
            ),
        ),
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great job!",
                        ),
                    ),
                ],
            )
        ),
        request_insight_pb2.RequestInsightMessage(
            record_session_events_request=request_insight_pb2.RecordSessionEventsRequest(
                session_id="test-session",
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.SessionEvent(
                        next_edit_session_event=request_insight_pb2.NextEditSessionEvent(
                            event_name="test-next-edit-event",
                            user_agent="test-user-agent",
                        ),
                    ),
                ],
            ),
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 1
    slackbot_stub.NotifyUserFeedback.assert_has_calls(
        [
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_COMPLETIONS",
                    tenant_name="tenant-name",
                    original_request_id=request_id,
                    details_url=f"tenant=tenant-name&request_id={request_id}",
                    genie_url="",
                    rating="POSITIVE",
                    note="Great job!",
                )
            ),
        ]
    )


def test_ignore_non_feedback_events(slackbot_stub, tenant_info, process_batch_kwargs):
    request_id = str(uuid.uuid4())
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_host_request=request_insight_pb2.CompletionHostRequest(),
                    ),
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great job!",
                        ),
                    ),
                    request_insight_pb2.RequestEvent(
                        completion_host_response=request_insight_pb2.CompletionHostResponse(),
                    ),
                ],
            )
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 1
    slackbot_stub.NotifyUserFeedback.assert_has_calls(
        [
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_COMPLETIONS",
                    tenant_name="tenant-name",
                    original_request_id=request_id,
                    details_url=f"tenant=tenant-name&request_id={request_id}",
                    genie_url="",
                    rating="POSITIVE",
                    note="Great job!",
                )
            ),
        ]
    )


def test_ignore_no_note(slackbot_stub, tenant_info, process_batch_kwargs):
    request_id = str(uuid.uuid4())
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                        ),
                    ),
                ],
            )
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 0


def test_ignore_duplicate_request_id(slackbot_stub, tenant_info, process_batch_kwargs):
    request_id = str(uuid.uuid4())
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great job!",
                        ),
                    ),
                ],
            )
        ),
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="duplicate feedback",
                        ),
                    ),
                ],
            )
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 1
    slackbot_stub.NotifyUserFeedback.assert_has_calls(
        [
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_COMPLETIONS",
                    tenant_name="tenant-name",
                    original_request_id=request_id,
                    details_url=f"tenant=tenant-name&request_id={request_id}",
                    genie_url="",
                    rating="POSITIVE",
                    note="Great job!",
                )
            ),
        ]
    )


def test_genie_url(slackbot_stub, tenant_info_with_control, process_batch_kwargs):
    request_id = str(uuid.uuid4())
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info_with_control,
                events=[
                    request_insight_pb2.RequestEvent(
                        completion_feedback=request_insight_pb2.CompletionFeedback(
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great job!",
                        ),
                    ),
                ],
            )
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 1
    slackbot_stub.NotifyUserFeedback.assert_has_calls(
        [
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_COMPLETIONS",
                    tenant_name="tenant-name-with-control",
                    original_request_id=request_id,
                    details_url=f"tenant=tenant-name-with-control&request_id={request_id}",
                    genie_url=f"tenant=tenant-name-with-control&redirect_url=tenant%3Dtenant-name-with-control%26request_id%3D{request_id}",
                    rating="POSITIVE",
                    note="Great job!",
                )
            ),
        ]
    )


def test_cache():
    cache = ProcessedRequestIdsCache(maxsize=2)

    cache.add("a")
    cache.add("b")
    assert cache.contains("a")
    assert cache.contains("b")

    # add a new item to evict
    cache.add("c")
    assert not cache.contains("a")
    assert cache.contains("b")
    assert cache.contains("c")

    # test non-existing item
    assert not cache.contains("d")

    # add a duplicate
    cache.add("c")
    assert cache.contains("b")
    assert cache.contains("c")


def test_slackbot_feedback_with_bigquery(
    slackbot_stub, tenant_info, process_batch_kwargs
):
    request_id = str(uuid.uuid4())
    event_time = timestamp_pb2.Timestamp()
    event_time.FromDatetime(datetime.datetime.now() - datetime.timedelta(minutes=1))
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        time=event_time,
                        slackbot_feedback=request_insight_pb2.SlackbotFeedback(
                            slack_response_timestamp="1234567890.123456",
                            slack_channel_id="C123456",
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great Slack feedback!",
                        ),
                    ),
                ],
            )
        ),
    ]

    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    assert slackbot_stub.NotifyUserFeedback.call_count == 1
    slackbot_stub.NotifyUserFeedback.assert_has_calls(
        [
            call(
                bot_pb2.NotifyUserFeedbackRequest(
                    channel_id="CHANNEL_SLACKBOT",
                    tenant_name="tenant-name",
                    original_request_id="test-request-123",  # This comes from the mock BigQuery response
                    details_url="tenant=tenant-name&request_id=test-request-123",
                    genie_url="",
                    rating="POSITIVE",
                    note="Great Slack feedback!",
                )
            ),
        ]
    )


def test_slackbot_feedback_bigquery_no_result(
    slackbot_stub, tenant_info, process_batch_kwargs
):
    request_id = str(uuid.uuid4())
    event_time = timestamp_pb2.Timestamp()
    event_time.FromDatetime(datetime.datetime.now() - datetime.timedelta(minutes=1))
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        time=event_time,
                        slackbot_feedback=request_insight_pb2.SlackbotFeedback(
                            slack_response_timestamp="9999999999.999999",  # Different timestamp that won't match
                            slack_channel_id="C999999",
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Should not find this feedback",
                        ),
                    ),
                ],
            )
        ),
    ]
    with pytest.raises(
        ValueError,
        match="Could not find original request ID for slackbot feedback",
    ):
        _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    # Should not call NotifyUserFeedback when no BigQuery result is found
    assert slackbot_stub.NotifyUserFeedback.call_count == 0


def test_slackbot_feedback_bigquery_too_old(
    slackbot_stub, tenant_info, process_batch_kwargs
):
    request_id = str(uuid.uuid4())
    event_time = timestamp_pb2.Timestamp()
    event_time.FromDatetime(
        datetime.datetime.now() - datetime.timedelta(minutes=90)
    )  # More than MAX_SLACKBOT_FEEDBACK_LATENCY_SECONDS old
    message_batch = [
        request_insight_pb2.RequestInsightMessage(
            update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
                request_id=request_id,
                tenant_info=tenant_info,
                events=[
                    request_insight_pb2.RequestEvent(
                        time=event_time,
                        slackbot_feedback=request_insight_pb2.SlackbotFeedback(
                            slack_response_timestamp="9999999999.999999",  # Different timestamp that won't match
                            slack_channel_id="C123456",
                            rating=request_insight_pb2.FeedbackRating.POSITIVE,
                            note="Great Slack feedback!",
                        ),
                    ),
                ],
            )
        ),
    ]
    _process_batch(message_batch, slackbot_stub, **process_batch_kwargs)

    # Should not call NotifyUserFeedback when no BigQuery result is found
    assert slackbot_stub.NotifyUserFeedback.call_count == 0

import pathlib
from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class Config:
    """Config for the feedback slackbot exporter."""

    project_id: str
    """The project id of the pub/sub topic/subscription."""

    topic_name: str
    """The name of the pub/sub topic to listen to."""

    subscription_name: str
    """The name of the subscription to register, as it exists in GCP."""

    subscription_description: str
    """A description of the subscription, shared across tenants. Used for metrics."""

    subscription_batch_size: int
    """The batch size to use for fetching subscription messages."""

    health_file: str
    """The path of the file to write to for health checks."""

    prom_port: int
    """The port to use for the prometheus metrics server."""

    slackbot_server_endpoint: str
    """The endpoint of the slackbot grpc server."""

    support_url_base: str
    """The support url base to be formatted with tenant and request id."""

    genie_url_base: str
    """The genie url base to be formatted with prefilled information."""

    tenants_without_support_access_control: list[str]
    """The names of tenants that do not need support access control."""

    channel_id_completions: str
    """The slack channel to post in for completion feedback."""

    channel_id_chat: str
    """The slack channel to post in for chat feedback."""

    channel_id_agent: str
    """The slack channel to post in for agent feedback."""

    channel_id_remote_agent: str
    """The slack channel to post in for remote agent feedback."""

    channel_id_next_edit: str
    """The slack channel to post in for next edit feedback."""

    channel_id_slackbot: str
    """The slack channel to post in for slackbot feedback."""

    analytics_dataset_name: str
    """The analytics dataset to use for the BigQuery client."""


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )

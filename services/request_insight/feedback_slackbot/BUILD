load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "config",
    srcs = ["config.py"],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_binary(
    name = "feedback_slackbot",
    srcs = ["feedback_slackbot.py"],
    deps = [
        ":config",
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/lib:request_insight_subscriber",
        "//tools/bot:bot_client",
        "//tools/bot:bot_py_proto",
        requirement("google-cloud-bigquery"),
        requirement("prometheus_client"),
        requirement("structlog"),
    ],
)

pytest_test(
    name = "feedback_slackbot_test",
    srcs = ["feedback_slackbot_test.py"],
    deps = [
        ":feedback_slackbot",
    ],
)

py_oci_image(
    name = "feedback_slackbot_image",
    package_name = package_name(),
    binary = ":feedback_slackbot",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":feedback_slackbot_image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:config-map-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//deploy/tenants:tenants-lib",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

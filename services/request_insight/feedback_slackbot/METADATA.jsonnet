local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'request-insight-feedback-slackbot',
      kubecfg: {
        target: '//services/request_insight/feedback_slackbot:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['mpauly'],
          slack_channel: '#team-insights',
        },
      },
    },
  ],
}

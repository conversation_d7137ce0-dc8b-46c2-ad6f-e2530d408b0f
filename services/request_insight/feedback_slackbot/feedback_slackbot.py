"""Subscriber for forwarding feedback messages to slack."""

import argparse
import logging
import pathlib
from datetime import datetime
from functools import partial
from queue import Queue
from typing import Iterable, Set
from urllib import parse

from prometheus_client import start_http_server

import services.request_insight.request_insight_pb2 as request_insight_pb2
import tools.bot.bot_pb2 as bot_pb2
from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import StandardSignalHandler
from services.request_insight.feedback_slackbot.config import _load_config
from services.request_insight.lib.request_insight_subscriber import (
    RequestInsightSubscriber,
)
from tools.bot.bot_client import setup_client
from tools.bot.bot_pb2_grpc import DevtoolsBotStub
from google.cloud import bigquery

REQUEST_ID_CACHE_SIZE = 10000
MAX_SLACKBOT_FEEDBACK_LATENCY_SECONDS = 600


class ProcessedRequestIdsCache:
    def __init__(self, maxsize: int = REQUEST_ID_CACHE_SIZE):
        self.maxsize = maxsize
        self.cache: Set[str] = set()
        self.queue: Queue[str] = Queue(maxsize=maxsize)

    def add(self, request_id: str) -> None:
        if request_id in self.cache:
            logging.debug("Skipping request id %s already in cache", request_id)
            return
        if self.queue.full() or len(self.cache) >= self.maxsize:
            to_remove = self.queue.get()
            logging.debug("Removing request id %s from cache", to_remove)
            self.cache.remove(to_remove)
        logging.debug("Adding request id %s to cache", request_id)
        self.queue.put(request_id)
        self.cache.add(request_id)

    def contains(self, request_id: str) -> bool:
        return request_id in self.cache


def _call_slackbot(
    slackbot_stub: DevtoolsBotStub,
    channel_id: str,
    tenant_name: str,
    request_id: str,
    support_url: str,
    genie_url: str,
    rating: str,
    note: str,
    user_agent: str,
):
    feedback_request = bot_pb2.NotifyUserFeedbackRequest(
        channel_id=channel_id,
        tenant_name=tenant_name,
        original_request_id=request_id,
        details_url=support_url,
        genie_url=genie_url,
        rating=rating,
        note=note,
        user_agent=user_agent,
    )
    slackbot_stub.NotifyUserFeedback(feedback_request)


def _validate_feedback(
    request_id: str,
    note: str,
    processed_request_ids: ProcessedRequestIdsCache,
) -> bool:
    if processed_request_ids.contains(request_id):
        logging.info("Skipping feedback with already processed request id")
        return False
    if not note:
        logging.info("Skipping feedback with no note")
        return False
    return True


# get the original request id from the slackbot_response_lookup table using the slack timestamp and channel id
# we run this query with read-only permissions, so injections shouldn't be possible.
def _get_original_slackbot_request_id(
    bigquery_client: bigquery.Client,
    analytics_dataset_name: str,
    slack_timestamp: str,
    channel_id: str,
) -> str:
    # Convert "123456.123456" to "123456123456" as the timestamp in the BigQuery table doesn't have "."
    formatted_timestamp = slack_timestamp.replace(".", "")

    query = f"""
    SELECT tenant, request_id
    FROM `{analytics_dataset_name}.slackbot_response_lookup`
    WHERE @message_timestamp IN UNNEST(slack_response_timestamps)
      AND channel = @channel
      AND time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 14 DAY)
    LIMIT 1
    """  # nosec

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter(
                "message_timestamp", "STRING", formatted_timestamp
            ),
            bigquery.ScalarQueryParameter("channel", "STRING", channel_id),
        ]
    )

    rows = list(bigquery_client.query_and_wait(query, job_config=job_config))
    if not rows:
        logging.warning("No request ID found for timestamp %s", slack_timestamp)
        return ""
    return rows[0].request_id


def _process_batch(
    message_batch: Iterable[request_insight_pb2.RequestInsightMessage],
    slackbot_stub: DevtoolsBotStub,
    bigquery_client: bigquery.Client,
    analytics_dataset_name: str,
    support_url_base: str,
    genie_url_base: str,
    tenants_without_support_access_control: list[str],
    channel_id_completions: str,
    channel_id_chat: str,
    channel_id_agent: str,
    channel_id_remote_agent: str,
    channel_id_next_edit: str,
    channel_id_slackbot: str,
    processed_request_ids: ProcessedRequestIdsCache,
):
    for message in message_batch:
        message_type = message.WhichOneof("message")
        if message_type != "update_request_info_request":
            logging.debug("Skipping message of type %s", message_type)
            continue
        logging.debug("Processing message of type %s", message_type)
        tenant_info = message.update_request_info_request.tenant_info

        request = message.update_request_info_request

        for event in request.events:
            event_type = event.WhichOneof("event")
            if event_type == "completion_feedback":
                request_id = request.request_id
                channel_id = channel_id_completions
                note = event.completion_feedback.note
                rating = event.completion_feedback.rating
                tenant = tenant_info.tenant_name
                user_agent = ""
            elif event_type == "chat_feedback":
                request_id = request.request_id
                channel_id = channel_id_chat
                note = event.chat_feedback.note
                rating = event.chat_feedback.rating
                tenant = tenant_info.tenant_name
                user_agent = event.chat_feedback.user_agent
            elif event_type == "agent_feedback":
                request_id = request.request_id
                channel_id = channel_id_agent
                note = event.agent_feedback.note
                rating = event.agent_feedback.rating
                tenant = tenant_info.tenant_name
                user_agent = event.agent_feedback.user_agent
            elif event_type == "remote_agent_feedback":
                request_id = request.request_id
                channel_id = channel_id_remote_agent
                note = event.remote_agent_feedback.note
                rating = event.remote_agent_feedback.rating
                tenant = tenant_info.tenant_name
                user_agent = event.remote_agent_feedback.user_agent
            elif event_type == "next_edit_feedback":
                request_id = request.request_id
                channel_id = channel_id_next_edit
                note = event.next_edit_feedback.note
                rating = event.next_edit_feedback.rating
                tenant = tenant_info.tenant_name
                user_agent = ""
            elif event_type == "slackbot_feedback":
                event_time = event.time.ToDatetime()
                current_time = datetime.utcnow()

                # Look up the original request ID using the slack timestamp
                slack_timestamp = event.slackbot_feedback.slack_response_timestamp
                slack_channel_id = event.slackbot_feedback.slack_channel_id

                original_request_id = _get_original_slackbot_request_id(
                    bigquery_client,
                    analytics_dataset_name,
                    slack_timestamp,
                    slack_channel_id,
                )
                if not original_request_id:
                    logging.debug(
                        "Could not find original request ID for slackbot feedback with timestamp %s, channel %s",
                        slack_timestamp,
                        slack_channel_id,
                    )

                    # If the event is too old, we can skip it. Otherwise, we should raise an error so that the message will be retried.
                    # If the pub sub queue gets backed up, it's possible that the original slack message isn't in bigquery yet.
                    if (
                        current_time.timestamp() - event_time.timestamp()
                        > MAX_SLACKBOT_FEEDBACK_LATENCY_SECONDS
                    ):
                        logging.info(
                            "Skipping slackbot feedback with timestamp %s, channel %s because latency greater than %s seconds",
                            slack_timestamp,
                            slack_channel_id,
                            MAX_SLACKBOT_FEEDBACK_LATENCY_SECONDS,
                        )
                        continue
                    else:
                        raise ValueError(
                            "Could not find original request ID for slackbot feedback"
                        )

                request_id = original_request_id
                channel_id = channel_id_slackbot
                note = event.slackbot_feedback.note
                rating = event.slackbot_feedback.rating
                tenant = tenant_info.tenant_name
                user_agent = ""
            else:
                logging.debug("Skipping event of type %s", event_type)
                continue

            logging.info(
                "Received %s in tenant %s for request id %s",
                event_type,
                tenant,
                request_id,
            )

            support_url = support_url_base.format(tenant, request_id)
            genie_url = (
                genie_url_base.format(tenant, parse.quote(support_url))
                if tenant not in tenants_without_support_access_control
                else ""
            )
            rating_name = request_insight_pb2.FeedbackRating.Name(rating)

            # Python implementation of RequestInsightSubscriber doesn't use multithreading
            # If it supports multithreading in the future, we need to add a lock to protect
            # the processed_request_ids cache
            if _validate_feedback(request_id, note, processed_request_ids):
                _call_slackbot(
                    slackbot_stub,
                    channel_id,
                    tenant,
                    request_id,
                    support_url,
                    genie_url,
                    rating_name,
                    note,
                    user_agent,
                )
                processed_request_ids.add(request_id)


def main():
    setup_struct_logging()
    _ = StandardSignalHandler()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    # start metrics server
    start_http_server(config.prom_port)

    # create grpc client stub
    slackbot_stub = setup_client(config.slackbot_server_endpoint)

    # create cache to avoid sending duplicate feedback
    processed_request_ids = ProcessedRequestIdsCache()

    # Create BigQuery client
    bigquery_client = bigquery.Client(project=config.project_id)

    # create and run the subscriber
    subscriber = RequestInsightSubscriber(
        config.project_id,
        config.topic_name,
        config.subscription_name,
        config.subscription_description,
        config.subscription_batch_size,
        health_file=pathlib.Path(config.health_file),
    )
    subscriber.run(
        partial(
            _process_batch,
            slackbot_stub=slackbot_stub,
            bigquery_client=bigquery_client,
            analytics_dataset_name=config.analytics_dataset_name,
            support_url_base=config.support_url_base,
            genie_url_base=config.genie_url_base,
            tenants_without_support_access_control=config.tenants_without_support_access_control,
            channel_id_completions=config.channel_id_completions,
            channel_id_chat=config.channel_id_chat,
            channel_id_agent=config.channel_id_agent,
            channel_id_remote_agent=config.channel_id_remote_agent,
            channel_id_next_edit=config.channel_id_next_edit,
            channel_id_slackbot=config.channel_id_slackbot,
            processed_request_ids=processed_request_ids,
        )
    )


if __name__ == "__main__":
    main()

local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';
local centralTasks = [
  {
    cloud: 'GCP_US_CENTRAL1_PROD',
    env: 'STAGING',
    namespace: 'central-staging',
  },
  {
    cloud: 'GCP_US_CENTRAL1_PROD',
    env: 'PROD',
    namespace: 'central',
  },
  {
    cloud: 'GCP_EU_WEST4_PROD',
    env: 'STAGING',
    namespace: 'central-staging',
  },
  {
    cloud: 'GCP_EU_WEST4_PROD',
    env: 'PROD',
    namespace: 'central',
  },
];

{
  deployment: [
    // BigQuery search dataset.
    {
      name: 'request-insight-search-dataset',
      priority: 100,  // We want tables to be created/modified before request-insight deploys.
      kubecfg: {
        target: '//services/request_insight/support_database/search_dataset:kubecfg',
        task: centralTasks,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline', 'nikita', 'zhewei'],
          slack_channel: '#team-insights',
        },
      },
    },
    {
      name: 'request-insight-search-dataset-all-tenants-views',
      priority: 99,  // Deploy after the real tables but before any services that use the views.
      kubecfg: {
        target: '//services/request_insight/support_database/search_dataset:all_tenants_views_kubecfg',
        task: centralTasks,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline', 'nikita', 'zhewei'],
          slack_channel: '#team-insights',
        },
      },
    },

    // GCS buckets.
    {
      name: 'request-insight-events-gcs-buckets',
      priority: 10,  // Buckets should be deployed before the exporters that write to them.
      kubecfg: {
        target: '//services/request_insight/support_database/event_buckets:kubecfg',
        task: centralTasks,
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline', 'nikita', 'zhewei'],
          slack_channel: '#team-insights',
        },
      },
    },

    // Support database exporter.
    {
      name: 'request-insight-support-database-exporter',
      kubecfg: {
        target: '//services/request_insight/support_database/exporter:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline', 'nikita', 'zhewei'],
          slack_channel: '#team-insights',
        },
      },
    },
  ],
}

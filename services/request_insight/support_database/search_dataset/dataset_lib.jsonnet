// Contains information about the Request Insight search dataset that needs to be known by both
// the dataset deployment and users. This is only for search-specific information; anything
// shared across datasets is in bigquery_lib.jsonnet instead.
function(cloud, env, namespace, isEnterprise)
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];

  // We write to separate datasets in the US and EU due to cost concerns copying data across
  // continents and because of unclear compliance differences.
  local cloudToBigQueryLocation = {
    GCP_US_CENTRAL1_DEV: 'us',
    GCP_US_CENTRAL1_PROD: 'us',
    GCP_EU_WEST4_PROD: 'eu',
  };
  assert std.objectHas(cloudToBigQueryLocation, cloud);
  local location = cloudToBigQueryLocation[cloud];
  local accessType = if isEnterprise then 'enterprise' else 'nonenterprise';

  // Group names need to be under 64 characters, so we abbreviate "request-insight" to "ri".
  local cloudIdentityGroup =
    if cloud == 'GCP_US_CENTRAL1_DEV' then
      '%s-ri-search-dataset-access-group' % namespace
    else
      '%s-%s-ri-search-%s-dataset-access-group' % [location, std.asciiLower(env), accessType];
  assert std.length(cloudIdentityGroup) <= 64 : 'CloudIdentityGroup name is too long';

  local readonlyCloudIdentityGroup =
    if cloud == 'GCP_US_CENTRAL1_DEV' then
      '%s-ri-search-dataset-readonly-access-group' % namespace
    else
      '%s-%s-ri-search-%s-dataset-readonly-access-group' % [location, std.asciiLower(env), accessType];
  assert std.length(readonlyCloudIdentityGroup) <= 64 : 'Read-only CloudIdentityGroup name is too long';

  local dataset =
    if cloud == 'GCP_US_CENTRAL1_DEV' then
      '%s-request-insight-search-dataset' % namespace
    else
      '%s-%s-request-insight-search-%s-dataset' % [location, std.asciiLower(env), accessType];

  local allTenantsViewsDataset =
    '%s-%s-request-insight-search-all-tenants-dataset' % [location, std.asciiLower(env)];

  local dataNamespace = {
    DEV: namespace,
    STAGING: 'central-staging',
    PROD: 'central',
  }[env];

  {
    location: location,
    cloudIdentityGroup: cloudIdentityGroup,
    readonlyCloudIdentityGroup: readonlyCloudIdentityGroup,
    dataset: dataset,
    datasetGcp: std.strReplace(dataset, '-', '_'),
    allTenantsViewsDataset: allTenantsViewsDataset,
    allTenantsViewsDatasetGcp: std.strReplace(allTenantsViewsDataset, '-', '_'),
    dataNamespace: dataNamespace,
  }

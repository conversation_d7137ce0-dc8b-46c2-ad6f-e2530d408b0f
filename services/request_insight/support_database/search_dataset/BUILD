load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    visibility = [
        "//services/request_insight/support_database:__subpackages__",
    ],
    deps = [
        ":dataset_lib",
        ":schema",
        "//deploy/common:lib",
        "//deploy/gcp:gcp-lib",
        "//services/request_insight:external_service_accounts_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

kubecfg(
    name = "all_tenants_views_kubecfg",
    src = "all_tenants_views.jsonnet",
    visibility = [
        "//services/request_insight/support_database:__subpackages__",
    ],
    deps = [
        ":dataset_lib",
        ":schema",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

jsonnet_library(
    name = "dataset_lib",
    srcs = [
        "dataset_lib.jsonnet",
    ],
    visibility = [
        "//services/request_insight:__subpackages__",
        "//tools/deletion_utils:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

jsonnet_library(
    name = "schema",
    srcs = [
        "schema.jsonnet",
    ],
    visibility = ["//services/request_insight:__subpackages__"],
    deps = [
        "//services/request_insight/lib:bigquery_lib",
    ],
)

jsonnet_to_json(
    name = "schema_json",
    src = "schema.jsonnet",
    outs = ["schema.json"],
    visibility = ["//services/request_insight:__subpackages__"],
    deps = [
        ":schema",
    ],
)

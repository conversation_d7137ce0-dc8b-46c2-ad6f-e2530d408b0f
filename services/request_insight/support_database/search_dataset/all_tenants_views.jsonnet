// Deploy views for querying search tables for both enterprise and non-enterprise tenants. These
// views are intended for use by request-insight-central. Engineers do not have direct access.
function(cloud, env, namespace, namespace_config)
  local appName = 'request-insight-search-all-tenants-dataset';

  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];
  local enterpriseDatasetLib = (import 'dataset_lib.jsonnet')(cloud, env, namespace, true);
  local nonenterpriseDatasetLib = (import 'dataset_lib.jsonnet')(cloud, env, namespace, false);
  local lib = import 'deploy/common/lib.jsonnet';

  local dataset = {
    apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
    kind: 'BigQueryDataset',
    metadata: {
      name: enterpriseDatasetLib.allTenantsViewsDataset,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Request Insight BigQuery dataset for searching across enterprise and non-enterprise tenants',
      resourceID: enterpriseDatasetLib.allTenantsViewsDatasetGcp,
      location: std.asciiUpper(enterpriseDatasetLib.location),
      projectRef: {
        external: cloudInfo.projectId,
      },
      // BigQueryDataset doesn't support IAM and uses legacy role formats. See
      // https://cloud.google.com/config-connector/docs/reference/resource-docs/bigquery/bigquerydataset
      access: [
        {
          role: 'OWNER',
          userByEmail: '<EMAIL>',
        },
        // Anyone that can read the enterprise dataset can also read the non-enterprise dataset.
        {
          role: 'READER',
          groupByEmail: '%<EMAIL>' % enterpriseDatasetLib.readonlyCloudIdentityGroup,
        },
      ],
    },
  };

  // Create views for every table and view in the schema. (BigQuery supports making views that query
  // other views.)
  local schema = (import 'schema.jsonnet')(enterpriseDatasetLib.datasetGcp);
  local views = [
    {
      apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
      kind: 'BigQueryTable',
      metadata: {
        name: '%s-%s-request-insight-search-all-tenants-%s-view' % [
          std.asciiLower(enterpriseDatasetLib.location),
          std.asciiLower(env),
          std.strReplace(tableName, '_', '-'),
        ],
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        description: 'View of %s with both enterprise and non-enterprise tenants' % tableName,
        resourceID: tableName,
        datasetRef: {
          external: enterpriseDatasetLib.allTenantsViewsDatasetGcp,
        },
        view: {
          query: |||
            SELECT *, 'NONENTERPRISE' AS access_type
            FROM `%(projectId)s.%(nonenterpriseDataset)s.%(tableName)s`

            UNION ALL

            SELECT *, 'ENTERPRISE' AS access_type
            FROM `%(projectId)s.%(enterpriseDataset)s.%(tableName)s`
          ||| % {
            projectId: cloudInfo.projectId,
            nonenterpriseDataset: nonenterpriseDatasetLib.datasetGcp,
            enterpriseDataset: enterpriseDatasetLib.datasetGcp,
            tableName: tableName,
          },
          useLegacySql: false,
        },
      },
    }
    for tableName in [def.name for def in schema]
  ];

  lib.flatten([
    dataset,
    views,
  ])

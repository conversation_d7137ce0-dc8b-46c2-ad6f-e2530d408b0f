// This file contains the schema for the BigQuery search datasets. The datasets are actually
// created by services/request_insight/search_dataset/deploy.jsonnet. This schema can also be
// passed into an emulator for testing.

// Staging/production callers should always provide a dataset name and piiPolicyTag
// generated by bigquery_lib.jsonnet. The defaults are here to facilitate easier testing.
// TODO(jacqueline): Find a way to feed in the dataset name during tests instead of making everyone
//                   use test_dataset.
function(dataset='test_dataset', piiPolicyTag=null, partitionExpirationDays=null)
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';
  local tableDefinition(name, description, schema, additionalClustering=[]) =
    bigqueryLib.tableDefinition(name, description, schema, additionalClustering=additionalClustering, partitionExpirationDays=partitionExpirationDays);

  // Schema shared across request search tables.
  local requestEventBaseSchema = [
    {
      name: 'request_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The request ID of the request',
    },
    {
      name: 'tenant_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The ID of the tenant for the request',
    },
    {
      name: 'tenant',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The name of the tenant for the request',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
  ];

  // Schema shared across session search tables.
  local sessionEventBaseSchema = [
    {
      name: 'session_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The session ID of the session',
    },
    {
      name: 'tenant_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The ID of the tenant for the request',
    },
    {
      name: 'tenant',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The name of the tenant for the request',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
  ];

  [
    // Generic request event lookup table. We're not expecting this to be very useful, but it's a
    // good stopgap for queries like "every resolution event".
    tableDefinition(
      'request_event',
      'Request Insight search table for searching for request events',
      requestEventBaseSchema + [
        {
          name: 'event_type',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The type of the request event (e.g., "completion_resolution")',
        },
        {
          name: 'event_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The id associated with this event',
        },
      ],
      additionalClustering=['event_type']
    ),

    // Everything from RequestMetadata events is interesting to search on. This table enables
    // cross-tenant search by request id and session id.
    tableDefinition(
      'request_metadata',
      'Request Insight search table for searching by request metadata',
      requestEventBaseSchema + [
        {
          name: 'request_type',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The request type of the request',
        },
        {
          name: 'session_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The session id of the request',
        },
        {
          name: 'user_agent',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The user agent of the request',
        },
        {
          name: 'user_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The user id of the request',
          policyTags: if piiPolicyTag != null then
            { names: [piiPolicyTag] }
          else {},
        },
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the request. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        {
          name: 'user_email',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The user email of the request, if any',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ]
    ),

    // Search by model name, across different request types.
    tableDefinition(
      'model',
      'Request Insight search table for searching by model name',
      requestEventBaseSchema + [
        {
          name: 'model_name',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The model name of the request',
        },
      ],
      additionalClustering=['model_name']
    ),

    // Search by resolution, across different request types.
    tableDefinition(
      'resolution',
      'Request Insight search table for searching by resolution',
      requestEventBaseSchema + [
        {
          name: 'accepted',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the resource generated by the request was accepted by the user',
        },
      ],
      additionalClustering=['accepted']
    ),

    tableDefinition(
      'http_status',
      'Request Insight search table for searching by HTTP status',
      requestEventBaseSchema + [
        {
          name: 'code',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The HTTP status code of the response',
        },
      ],
      additionalClustering=['code']
    ),

    // Search by completion response information. Eventually we'll want a more general full text
    // search table, but this will unblock some work on completion data.
    tableDefinition(
      'completion_response',
      'Request Insight search table for searching by completion response information',
      requestEventBaseSchema + [
        {
          name: 'character_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The number of characters in the response',
        },
        {
          name: 'nonempty_line_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The number of non-empty lines in the response',
        },
      ]
    ),

    tableDefinition(
      'completion_post_process',
      'Request Insight search table for searching by completion post processing information',
      requestEventBaseSchema + [
        {
          name: 'filter_score',
          type: 'FLOAT',
          mode: 'REQUIRED',
          description: 'The score of the filter',
        },
        {
          name: 'applied_filter_threshold',
          type: 'FLOAT',
          mode: 'NULLABLE',
          description: 'The filter threshold that was applied',
        },
        {
          name: 'filter_reason',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The reason for filtering the completion',
        },
      ]
    ),

    tableDefinition(
      'parenthesis_truncation',
      'Request Insight search table for tracking parenthesis truncation data',
      requestEventBaseSchema + [
        {
          name: 'original_length',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The length of the original text before any truncation was applied',
        },
        {
          name: 'truncated_length',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The length of the text after truncation (if applied)',
        },
        {
          name: 'was_truncated',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether truncation was actually applied',
        },
        {
          name: 'could_have_truncated',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether truncation could have been applied (met all criteria)',
        },
      ]
    ),

    // Add request events above this point and session events below.

    // Search for session events.
    tableDefinition(
      'session_event',
      'Request Insight search table for searching for session events',
      sessionEventBaseSchema + [
        {
          name: 'event_type',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The type of the session event (e.g., "text_edit")',
        },
        // event_id is needed to look up a specific event in the GCS bucket, since there can be
        // many such events per session.
        {
          name: 'event_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The id associated with this event',
        },
        {
          name: 'request_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The request_id associated with this event, if relevant',
        },
      ],
      additionalClustering=['event_type']
    ),

    // Search for remote agents log session events.
    tableDefinition(
      'remote_agent_log',
      'Request Insight search table for searching for remote agent log events',
      sessionEventBaseSchema + [
        {
          name: 'event_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The id associated with this event',
        },
      ],
    ),
  ]

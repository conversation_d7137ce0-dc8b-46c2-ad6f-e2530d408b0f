// Create the Request Insight search BigQuery datasets. This creates both the enterprise and
// nonenterprise datasets.
function(cloud, env, namespace, namespace_config)
  local lib = import 'deploy/common/lib.jsonnet';
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';
  local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
  local saLib = import 'services/request_insight/external_service_accounts_lib.jsonnet';
  local externalServiceAccounts = saLib.getExternalServiceAccounts(env, cloud);
  local appName = 'request-insight-bigquery-search';

  // Get the objects to deploy one dataset, with the specified access level.
  local datasetObjects(isEnterprise) =
    local datasetLib = (import 'services/request_insight/support_database/search_dataset/dataset_lib.jsonnet')(
      cloud, env, namespace, isEnterprise
    );
    local datasetName = datasetLib.dataset;
    local datasetResourceId = datasetLib.datasetGcp;

    // Create a cloud identity groups (i.e., google group) for controlling access to the BigQuery
    // dataset. This is necessary because datasets don't support IAM. We don't want to grant new
    // tenants access to the dataset manually, so instead we grant access to a group which is
    // added to by RequestInsight deployments. For details on why we can't use IAM, see
    // https://cloud.google.com/config-connector/docs/reference/resource-docs/bigquery/bigquerydataset
    local cloudIdentityGroup = gcpLib.cloudIdentityGroup(
      namespace,
      appName,
      datasetLib.cloudIdentityGroup,
      'Read/write access group for %s' % datasetResourceId,
    );
    local readOnlyCloudIdentityGroup = gcpLib.cloudIdentityGroup(
      namespace,
      appName,
      datasetLib.readonlyCloudIdentityGroup,
      'Read-only access group for %s' % datasetResourceId,
    );

    // Engineers only have access to their dev datasets or the non-enterprise staging/prod datasets.
    local datasetEngAccess = if isEnterprise then [] else [
      // Direct access.
      {
        role: if env == 'DEV' then 'OWNER' else 'READER',
        groupByEmail: '<EMAIL>',
      },
      // Spark job access.
      {
        role: 'READER',
        userByEmail: externalServiceAccounts.sparkSA.email,
      },
    ];
    // For simplicity service accounts have write access to the dataset. Technically they only need
    // metadata read access on the dataset and write access on the tables, but that significantly
    // complicates configuration and we should catch things like table deletions in code review.
    local datasetServiceAccess = [
      {
        role: 'WRITER',
        groupByEmail: cloudIdentityGroup.groupEmail,
      },
    ];
    local datasetReadonlyServiceAccess = [
      {
        role: 'READER',
        groupByEmail: readOnlyCloudIdentityGroup.groupEmail,
      },
    ];
    // BigQuery datasets require an explicit owner, and configconnector needs admin privileges to be
    // able to run updates.
    local datasetAdminAccess = [
      {
        role: 'OWNER',
        userByEmail: '<EMAIL>',
      },
    ];

    // Enforce PII access policies everywhere, regardless of enterprise vs non-enterprise.
    // Note(jacqueline): Confusingly the underlying resource for the PII policy tag says "user id".
    // This is a historical artifact. We originally thought we'd want different policies for
    // different types of PII, but that hasn't ended up being the case.
    local piiPolicyTag = bigqueryLib.dataAccessPolicyTag(cloud, env, 'userId');

    local datasetAccess = lib.flatten([
      datasetEngAccess,
      datasetServiceAccess,
      datasetReadonlyServiceAccess,
      datasetAdminAccess,
    ]);
    local dataset = bigqueryLib.createDataset(
      datasetName, datasetResourceId, datasetLib.location, datasetAccess, cloud, env, namespace, appName
    );

    // Garbage collect enterprise data after 2 weeks.
    local partitionExpirationDays = if isEnterprise then 14 else null;

    local tablesDefinitions = (import 'services/request_insight/support_database/search_dataset/schema.jsonnet')(
      datasetResourceId, piiPolicyTag, partitionExpirationDays
    );
    local tables = [
      bigqueryLib.createTable(
        def,
        datasetResourceId,
        namespace,
        appName,
        'request-insight-search-%s' % (if isEnterprise then 'enterprise' else 'nonenterprise')
      )
      for def in tablesDefinitions
    ];

    lib.flatten([
      cloudIdentityGroup.objects,
      readOnlyCloudIdentityGroup.objects,
      dataset,
      tables,
    ])
  ;

  // In staging/prod we deploy both an enterprise and a non-enterprise dataset. In dev these are
  // the same thing so we only deploy one.
  local nonenterpriseDataset = datasetObjects(isEnterprise=false);
  local enterpriseDataset = if env == 'DEV' then [] else datasetObjects(isEnterprise=true);

  lib.flatten([
    nonenterpriseDataset,
    enterpriseDataset,
  ])

# Request Insight support/research database

This directory contains a database for shared use by Augment's internal support site and
researchers. The database has two components:
1. A GCS bucket with raw proto events.
2. A BigQuery dataset for searching for requests.

This RFC has more context than this README:
https://www.notion.so/RFC-Revamping-the-support-site-47866cdb2bf148028cd7ff70fb2b4431

## GCS buckets

Events have the key structure:
```
<tenant_id>/request/<request_id>/<event_type>/<event_id>
<tenant_id>/session/<session_id>/<event_type>/<event_id>
```

Some notes on the key structure:
* Event IDs are just synthetic ids for the sake of having a unique key when a request has duplicates
  of the same event type.
* Having the event type in the key is not strictly necessary, but it makes the bucket easier to
  visually inspect and allows users to retrieve just the events they want without using the search
  dataset.

See `base/datasets/gcs_client.py` for a utility to fetch data from the buckets.

## Search dataset

The search dataset contains a subset of event data that we're interested in searching for, either
on the support site or for research. See `search_dataset/schema.jsonnet` for the full schema, but
some tables you might be interested include:
* `request_event`: Contains every request event. Can be used to get the request and event ids of
  every event of a given type, which is useful for queries like "get every completion request".
* `session_event`: Same as `request_event` but for session events.
* `request_metadata`: Contains every request metadata event. Can be used fo rqueries like "get every
  request id for user X".
* `resolution`: Contains every resolution event. Can be used for queries like "get every accepted
  completion request".
* `model`: Contains the model for every request. Can be used for queries like "get every request
  for model X".

Every table allows you to filter by tenant and time. Filtering by time is strongly encouraged; it
will make your queries cheaper and faster.

## Security and access

Tenants are segregated into "enterprise" and "non-enterprise" tenants. Non-enterprise tenants are
dogfood, Vanguard, and the aitutors. Enterprise tenants are everybody else. (In other words,
non-enterprise tenants are anyone you don't need Genie permissions to see their data.)

To give direct access to non-enterprise data, we have separate buckets and search datasets for
non-enterprise and enterprise data. Engineers have direct access to the non-enterprise search
dataset and buckets, but only have access to the enterprise search dataset and buckets via the
support site (which enforces Genie permissions).

Links to non-enterprise resources:
* [Staging bucket](https://console.cloud.google.com/storage/browser/us-staging-request-insight-events-nonenterprise)
* [Staging search dataset](https://console.cloud.google.com/bigquery?project=system-services-prod&ws=!1m4!1m3!3m2!1ssystem-services-prod!2sus_staging_request_insight_search_nonenterprise_dataset)
* [Prod bucket](https://console.cloud.google.com/storage/browser/us-prod-request-insight-events-nonenterprise)
* [Prod search dataset](https://console.cloud.google.com/bigquery?project=system-services-prod&ws=!1m4!1m3!3m2!1ssystem-services-prod!2sus_prod_request_insight_search_nonenterprise_dataset)

## Dev deployments

The default `request_insight` dev deploy target will deploy the support database and exporter
automatically. Dev resources are named after your username:
* Search dataset: `dev_<username>_request_insight_search_dataset`
* Events bucket: `dev-<username>-request-insight-events`

When looking for your dev resources in the GCP console, make sure you're on the
`system-services-dev` project.

(Last edited 2025-02-07.)

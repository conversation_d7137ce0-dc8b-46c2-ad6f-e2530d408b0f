local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';
local exporterLib = import 'services/request_insight/lib/exporter_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local isEnterprise = !namespace_config.flags.exportFullData;
  local projectId = cloudInfo[cloud].projectId;

  local appName = 'request-insight-support-database-exporter';
  local shortAppName = 'ri-sprt-db-exp';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(
    env=env, namespace=namespace, cloud=cloud, appName=appName
  );
  local subscriber = exporterLib.subscriber(cloud, env, namespace, appName, serviceAccount);
  local datasetLib = (import 'services/request_insight/support_database/search_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, isEnterprise
  );
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % shortAppName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace)
  );
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-certificate' % shortAppName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace)
  );

  local config = {
    subscriberConfig: {
      projectId: projectId,
      subscriptionId: subscriber.subscriptionName,
      deadLetterSubscriptionId: subscriber.deadLetterSubscriptionName,
      maxConcurrentReceivers: 50,
    },
    clientMtls: if mtls then clientCert.config else null,
    centralClientMtls: if mtls then centralClientCert.config else null,
    tenantWatcherEndpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    tokenExchangeEndpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    gcsProxyEndpoint: 'gcs-proxy-svc:50051',  // no need to overcomplicate with a library for now
    searchExportConfig: {
      projectId: projectId,
      datasetName: datasetLib.datasetGcp,
    },
    healthFile: '/tmp/health',
    promPort: 9090,
    featureFlagsSdkKeyPath: dynamicFeatureFlags.secretsFilePath,
    dynamicFeatureFlagsEndpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    namespace: namespace,
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // Give write access to the search dataset.
  local datasetAccess = bigqueryLib.datasetAccess(
    namespace,
    appName,
    datasetLib.cloudIdentityGroup,
    datasetLib.dataNamespace,
    serviceAccount.serviceAccountGcpEmailAddress,
  );

  local container = {
    name: appName,
    target: {
      name: '//services/request_insight/support_database/exporter:image',
      dst: 'request_insight_database_exporter',
    },
    env: [
      {
        name: 'POD_NAME',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.name',
          },
        },
      },
      {
        name: 'POD_NAMESPACE',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.namespace',
          },
        },
      },
    ] + dynamicFeatureFlags.env,
    volumeMounts: [
      configMap.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 2,
        memory: '2Gi',
      },
    },
    args: [
      '--config',
      configMap.filename,
    ],
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 20,
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  // We allow multiple exporters on the same host because all of their processing is async.
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local minReplicas = {
    DEV: 1,
    STAGING: 1,
    PROD: 1,
  };
  local maxReplicas = {
    DEV: 2,
    STAGING: 8,
    PROD: 8,
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: 0,
      replicas: minReplicas[env],
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  // Scale when the subscription backlog gets too large.
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: '%s-scaledobject' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      minReplicaCount: minReplicas[env],
      maxReplicaCount: maxReplicas[env],
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            metricName: 'pubsub_googleapis_com:subscription_num_undelivered_messages',
            threshold: '500',
            query: 'sum(avg_over_time(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id="%s"}[1m]))' % config.subscriberConfig.subscriptionId,
          },
        },
      ],
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    scaledObject,
    subscriber.objects,
    datasetAccess,
    clientCert.objects,
    centralClientCert.objects,
  ])

package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/augmentcode/augment/services/lib/pubsub"
	"github.com/augmentcode/augment/services/request_insight/lib/subscriber"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	"google.golang.org/grpc"
)

var processDeadLetterQueueFlag = featureflags.NewBoolFlag("ri_support_database_exporter_process_dead_letter_queue", false)

type Config struct {
	SubscriberConfig            pubsub.SubscribeClientConfig
	SearchExportConfig          SearchExportConfig
	ClientMtls                  *tlsconfig.ClientConfig
	CentralClientMtls           *tlsconfig.ClientConfig
	TenantWatcherEndpoint       string
	TokenExchangeEndpoint       string
	GcsProxyEndpoint            string
	HealthFile                  string
	PromPort                    int
	FeatureFlagsSdkKeyPath      string
	DynamicFeatureFlagsEndpoint string
	Namespace                   string
}

// Load configuration from the given file.
func loadConfig(configFile string) (*Config, error) {
	var config Config
	if configFile == "" {
		return nil, fmt.Errorf("missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		return nil, err
	}

	log.Info().Msgf("Config: %v", config)
	return &config, nil
}

func declareHealthy(healthFile string) {
	log.Info().Msgf("Declaring healthy in %s", healthFile)
	f, err := os.Create(healthFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create health file")
	}
	defer f.Close()
	f.WriteString("OK")
}

func main() {
	logging.SetupServerLogging()

	ctx := context.Background()

	// Parse flags.
	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()

	// Load config.
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error loading config")
	}

	// Start metrics server.
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	featureFlagsHandle, err := featureflags.NewFeatureFlagHandleFromFile(
		config.FeatureFlagsSdkKeyPath, config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCache(tenantWatcherClient, config.Namespace)
	defer tenantCache.Close()

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
	}
	defer tokenExchangeClient.Close()

	// Set up GCS proxy client with increased message size limits
	maxMsgSize := 10 * 1024 * 1024 // 8MB
	conn, err := grpc.NewClient(config.GcsProxyEndpoint,
		grpc.WithTransportCredentials(clientCreds),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(maxMsgSize),
			grpc.MaxCallSendMsgSize(maxMsgSize),
		),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating GCS proxy client")
	}

	gcsProxyClient := gcsproxyclient.NewGcsProxyClientFromConn(conn)
	defer gcsProxyClient.Close()

	// Create the exporter.
	exporter, err := NewSupportDatabaseExporter(
		ctx, &config.SearchExportConfig, gcsProxyClient, tenantCache, tokenExchangeClient,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating GCS exporter")
	}
	defer exporter.Close()

	// Create the subscriber.
	config.SubscriberConfig.ConfigureDeadLetterHandling = func() bool {
		return checkDeadLetterFlag(featureFlagsHandle)
	}
	subscriber, err := subscriber.New(ctx, &config.SubscriberConfig, exporter.ProcessMessage)
	if err != nil {
		log.Fatal().Err(err).Msg("Error initializing RequestInsightSubscriber")
	}
	defer subscriber.Close()

	// Wait 10 seconds before declaring healthy, to give the subscriber time to error out if there's
	// something wrong with the subscription. Ideally we would do this in the subscriber itself, but
	// Go's pubsub library only calls your provided callback when there are messages to process. For
	// low-traffic namespaces this can cause kubernetes crashloops.
	go func() {
		time.Sleep(10 * time.Second)
		declareHealthy(config.HealthFile)
	}()

	// Run the subscriber. This should never return in the happy case.
	subscriber.Run(ctx)
}

func checkDeadLetterFlag(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := processDeadLetterQueueFlag.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading dead letter queue feature flag")
		return false
	}
	return val
}

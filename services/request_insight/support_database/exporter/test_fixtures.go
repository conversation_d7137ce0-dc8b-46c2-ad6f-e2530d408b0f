package main

import (
	"context"
	"os"
	"testing"

	bqemulator "github.com/augmentcode/augment/base/test_utils/bigquery/emulator"
	"github.com/fsouza/fake-gcs-server/fakestorage"
)

// This file sets up shared test fixtures.

const (
	projectId   = "test-project"
	datasetName = "test_dataset"
	bucketName  = "test-bucket"
)

var (
	gcsEmulator *fakestorage.Server          = nil
	bqEmulator  *bqemulator.BigQueryEmulator = nil
)

func TestMain(m *testing.M) {
	// Start a fake GCS server.
	fakeStorageOptions := fakestorage.Options{
		StorageRoot: "/tmp/fake-gcs-server",
		Host:        "127.0.0.1",
		Port:        0, // Assigns to a random open port.
	}
	var err error
	gcsEmulator, err = fakestorage.NewServerWithOptions(fakeStorageOptions)
	if err != nil {
		panic(err)
	}
	defer gcsEmulator.Stop()

	// Create a test bucket.
	gcsEmulator.CreateBucketWithOpts(fakestorage.CreateBucketOpts{
		Name: bucketName,
	})

	// Set up BigQuery emulator.
	bqEmulator, err = bqemulator.New(context.Background(), projectId, datasetName)
	if err != nil {
		panic(err)
	}
	defer bqEmulator.Close()

	err = bqEmulator.LoadJsonSchemaFile(context.Background(), "../search_dataset/schema.json")
	if err != nil {
		panic(err)
	}

	// Run the tests.
	os.Exit(m.Run())
}

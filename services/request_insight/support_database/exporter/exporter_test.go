package main

import (
	"context"
	"testing"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type MockProtoExporter struct {
	messageCount int
}

func (e *MockProtoExporter) ProcessMessage(ctx context.Context, message *pb.RequestInsightMessage) error {
	e.messageCount++
	return nil
}

func (e *MockProtoExporter) Close() error {
	return nil
}

type MockSearchExporter struct {
	messageCount int
}

func (e *MockSearchExporter) ProcessMessage(ctx context.Context, message *pb.RequestInsightMessage) error {
	e.messageCount++
	return nil
}

func (e *MockSearchExporter) Close() error {
	return nil
}

func TestProcessMessage(t *testing.T) {
	t.Run("test export", func(t *testing.T) {
		protoExporter := &MockProtoExporter{}
		searchExporter := &MockSearchExporter{}
		exporter := &SupportDatabaseExporter{
			protoExporter:  protoExporter,
			searchExporter: searchExporter,
		}

		message := &pb.RequestInsightMessage{}
		err := exporter.ProcessMessage(context.Background(), message)
		if err != nil {
			t.Fatal(err)
		}

		if protoExporter.messageCount != 1 {
			t.Errorf("Expected proto exporter to process 1 message, got %d", protoExporter.messageCount)
		}
		if searchExporter.messageCount != 1 {
			t.Errorf("Expected search exporter to process 1 message, got %d", searchExporter.messageCount)
		}
	})
}

type MockTokenExchangeClient struct {
	mock.Mock
}

func (m *MockTokenExchangeClient) GetSignedTokenForService(ctx context.Context, tenantID string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, scopes)
	return args.Get(0).(secretstring.SecretString), args.Error(1)
}

func (m *MockTokenExchangeClient) Close() {}

type MockTenantCache struct {
	mock.Mock
}

func (m *MockTenantCache) GetTenant(tenantID string) (*tenantproto.Tenant, error) {
	args := m.Called(tenantID)
	return args.Get(0).(*tenantproto.Tenant), args.Error(1)
}
func (m *MockTenantCache) Close() {}

// createTestJWT creates a properly signed JWT token for testing
func createTestJWT(expiresIn time.Duration) (string, error) {
	// Create ECDSA private key for signing
************************************************************************************************************************************************************************************************************************************************************************************************
	if err != nil {
		return "", err
	}

	claims := &auth.AugmentClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expiresIn)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodES256, claims)
	return token.SignedString(privateKey)
}

func TestTokenCaching(t *testing.T) {
	mockTokenExchange := &MockTokenExchangeClient{}
	mockTenantCache := &MockTenantCache{}

	exporter := &SupportDatabaseExporter{
		tokenExchangeClient: mockTokenExchange,
		tenantCache:         mockTenantCache,
		rcCachedInfo: &rcCachedInfo{
			sessionID:         "test-session",
			currentTokens:     map[string]*secretstring.SecretString{},
			tokenExpiryTimes:  map[string]time.Time{},
			tokenExpiryBuffer: 2 * time.Minute,
		},
	}

	t.Run("uses cached token when not expired and not close to expiry", func(t *testing.T) {
		// Set up cached token that expires in 10 minutes
		cachedToken := secretstring.New("cached-token")
		exporter.rcCachedInfo.currentTokens["test-tenant"] = &cachedToken
		exporter.rcCachedInfo.tokenExpiryTimes["test-tenant"] = time.Now().Add(10 * time.Minute)

		ctx := context.Background()
		rc, err := exporter.getRequestContext(ctx, "test-tenant")

		assert.NoError(t, err)
		assert.NotNil(t, rc)
		assert.Equal(t, cachedToken.Expose(), rc.AuthToken.Expose())
		mockTenantCache.AssertNotCalled(t, "GetTenant")
		mockTokenExchange.AssertNotCalled(t, "GetSignedTokenForService")
	})

	t.Run("fetches new token when expired", func(t *testing.T) {
		// Set up expired token
		expiredToken := secretstring.New("expired-token")
		exporter.rcCachedInfo.currentTokens["test-tenant2"] = &expiredToken
		exporter.rcCachedInfo.tokenExpiryTimes["test-tenant2"] = time.Now().Add(-10 * time.Minute)

		// Set up mock responses
		mockTenant := &tenantproto.Tenant{Id: "test-tenant2"}
		mockTenantCache.On("GetTenant", "test-tenant2").Return(mockTenant, nil)

		tokenString, err := createTestJWT(time.Hour)
		assert.NoError(t, err)
		newToken := secretstring.New(tokenString)

		mockTokenExchange.On("GetSignedTokenForService",
			mock.Anything,
			"test-tenant2",
			[]tokenexchangeproto.Scope{tokenexchangeproto.Scope_REQUEST_RESTRICTED_RW},
		).Return(newToken, nil)

		ctx := context.Background()
		rc, err := exporter.getRequestContext(ctx, "test-tenant2")

		assert.NoError(t, err)
		assert.NotNil(t, rc)
		assert.Equal(t, newToken.Expose(), rc.AuthToken.Expose())
		mockTenantCache.AssertExpectations(t)
		mockTokenExchange.AssertExpectations(t)
	})

	t.Run("fetches new token when close to expiry", func(t *testing.T) {
		// Set up token that expires in 1 minute (within buffer)
		nearExpiryToken := secretstring.New("near-expiry-token")
		exporter.rcCachedInfo.currentTokens["test-tenant3"] = &nearExpiryToken
		exporter.rcCachedInfo.tokenExpiryTimes["test-tenant3"] = time.Now().Add(time.Minute)

		// Set up mock responses (same as previous test)
		mockTenant := &tenantproto.Tenant{Id: "test-tenant3"}
		mockTenantCache.On("GetTenant", "test-tenant3").Return(mockTenant, nil)

		tokenString, err := createTestJWT(time.Hour)
		assert.NoError(t, err)
		newToken := secretstring.New(tokenString)

		mockTokenExchange.On("GetSignedTokenForService",
			mock.Anything,
			"test-tenant3",
			[]tokenexchangeproto.Scope{tokenexchangeproto.Scope_REQUEST_RESTRICTED_RW},
		).Return(newToken, nil)

		ctx := context.Background()
		rc, err := exporter.getRequestContext(ctx, "test-tenant3")

		assert.NoError(t, err)
		assert.NotNil(t, rc)
		assert.Equal(t, newToken.Expose(), rc.AuthToken.Expose())
		mockTenantCache.AssertExpectations(t)
		mockTokenExchange.AssertExpectations(t)
	})
}

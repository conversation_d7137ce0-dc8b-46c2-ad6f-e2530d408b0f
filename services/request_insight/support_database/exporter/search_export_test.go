package main

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	chatpb "github.com/augmentcode/augment/services/chat_host/proto"
	editpb "github.com/augmentcode/augment/services/edit_host/proto"
	nexteditpb "github.com/augmentcode/augment/services/next_edit_host/proto"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	"github.com/google/uuid"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	tenantID   = "123"
	tenantName = "test-tenant"
)

func newTestSearchExporter(t *testing.T) *SearchExporterImpl {
	return &SearchExporterImpl{
		bqClient: bqEmulator.Client,
		dataset:  bqEmulator.Dataset,
	}
}

func tenantInfo() *pb.TenantInfo {
	return &pb.TenantInfo{
		TenantId:   tenantID,
		TenantName: tenantName,
	}
}

func mockTime() time.Time {
	return time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
}

func TestGetRequestEventRow(t *testing.T) {
	eventId := "test-event-id"
	event := &pb.RequestEvent{
		Time:    timestamppb.New(mockTime()),
		EventId: &eventId,
		Event: &pb.RequestEvent_CompletionResolution{
			CompletionResolution: &pb.CompletionResolution{
				AcceptedIdx: 1,
			},
		},
	}

	result := getRequestEventRow("test-request", tenantInfo(), event)
	expected := &RequestEventRow{
		RequestEventBase: RequestEventBase{
			RequestId: "test-request",
			TenantID:  tenantID,
			Tenant:    tenantName,
			Time:      mockTime(),
		},
		EventType: "completion_resolution",
		EventId:   eventId,
	}
	if !reflect.DeepEqual(result, expected) {
		t.Errorf("Expected %v, got %v", expected, result)
	}
}

func TestGetRequestMetadataRow(t *testing.T) {
	t.Run("API token metadata event", func(t *testing.T) {
		event := &pb.RequestEvent{
			Time: timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_RequestMetadata{
				RequestMetadata: &pb.RequestMetadata{
					RequestType: pb.RequestType_COMPLETION,
					UserId:      "test-user",
					OpaqueUserId: &authpb.UserId{
						UserId:     "test-opaque-user-id",
						UserIdType: authpb.UserId_API_TOKEN,
					},
					SessionId: "test-session",
					UserAgent: "test-user-agent",
				},
			},
		}

		result := getRequestMetadataRow("test-request", tenantInfo(), event)
		expected := &RequestMetadataRow{
			RequestEventBase: RequestEventBase{
				RequestId: "test-request",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			RequestType:  "COMPLETION",
			UserId:       "test-user",
			OpaqueUserId: bigquery.NullString{Valid: true, StringVal: "test-opaque-user-id"},
			UserIdType:   bigquery.NullString{Valid: true, StringVal: "API_TOKEN"},
			SessionId:    "test-session",
			UserAgent:    "test-user-agent",
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("email metadata event", func(t *testing.T) {
		email := "<EMAIL>"
		event := &pb.RequestEvent{
			Time: timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_RequestMetadata{
				RequestMetadata: &pb.RequestMetadata{
					RequestType: pb.RequestType_COMPLETION,
					UserId:      "test-user",
					OpaqueUserId: &authpb.UserId{
						UserId:     "test-opaque-user-id",
						UserIdType: authpb.UserId_AUGMENT,
					},
					UserEmail: &email,
					SessionId: "test-session",
					UserAgent: "test-user-agent",
				},
			},
		}

		result := getRequestMetadataRow("test-request", tenantInfo(), event)
		expected := &RequestMetadataRow{
			RequestEventBase: RequestEventBase{
				RequestId: "test-request",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			RequestType:  "COMPLETION",
			UserId:       "test-user",
			OpaqueUserId: bigquery.NullString{Valid: true, StringVal: "test-opaque-user-id"},
			UserIdType:   bigquery.NullString{Valid: true, StringVal: "AUGMENT"},
			UserEmail:    bigquery.NullString{Valid: true, StringVal: "<EMAIL>"},
			SessionId:    "test-session",
			UserAgent:    "test-user-agent",
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("no metadata", func(t *testing.T) {
		event := &pb.RequestEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionHostRequest{},
		}

		result := getRequestMetadataRow("test-request", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

func TestGetModelRow(t *testing.T) {
	t.Run("model event", func(t *testing.T) {
		// Test all the events with an associated model to pull out.
		events := []*pb.RequestEvent{
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_CompletionHostRequest{
					CompletionHostRequest: &pb.CompletionHostRequest{
						Model: "test-model",
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_InstructionHostRequest{
					InstructionHostRequest: &pb.RIInstructionRequest{
						Request: &editpb.InstructionRequest{
							ModelName: "test-model",
						},
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_ChatHostRequest{
					ChatHostRequest: &pb.RIChatRequest{
						Request: &chatpb.ChatRequest{
							ModelName: "test-model",
						},
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_NextEditHostRequest{
					NextEditHostRequest: &pb.RINextEditRequest{
						Request: &nexteditpb.NextEditRequest{
							ModelName: "test-model",
						},
					},
				},
			},
		}
		for _, event := range events {
			result := getModelRow("test-request", tenantInfo(), event)
			expected := &modelRow{
				RequestEventBase: RequestEventBase{
					RequestId: "test-request",
					TenantID:  tenantID,
					Tenant:    tenantName,
					Time:      mockTime(),
				},
				ModelName: "test-model",
			}
			if !reflect.DeepEqual(result, expected) {
				t.Errorf("Expected %v, got %v", expected, result)
			}
		}
	})

	t.Run("no model", func(t *testing.T) {
		// Test an event that doesn't have a model to pull out.
		event := &pb.RequestEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionResolution{},
		}

		result := getModelRow("test-request", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

func TestGetResolutionRow(t *testing.T) {
	t.Run("resolution accepted events", func(t *testing.T) {
		// Test all the events with an associated "accepted" resolution to pull out.
		events := []*pb.RequestEvent{
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_CompletionResolution{
					CompletionResolution: &pb.CompletionResolution{
						AcceptedIdx: 1,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_EditResolution{
					EditResolution: &pb.EditResolution{
						IsAccepted: true,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_NextEditResolution{
					NextEditResolution: &pb.NextEditResolution{
						IsAccepted: true,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_InstructionResolution{
					InstructionResolution: &pb.InstructionResolution{
						IsAcceptAll: true,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_InstructionResolution{
					InstructionResolution: &pb.InstructionResolution{
						IsAcceptedChunks: []bool{
							false,
							true,
						},
					},
				},
			},
		}
		for _, event := range events {
			result := getResolutionRow("test-request", tenantInfo(), event)
			expected := &resolutionRow{
				RequestEventBase: RequestEventBase{
					RequestId: "test-request",
					TenantID:  tenantID,
					Tenant:    tenantName,
					Time:      mockTime(),
				},
				Accepted: true,
			}
			if !reflect.DeepEqual(result, expected) {
				t.Errorf("Expected %v, got %v", expected, result)
			}
		}
	})

	t.Run("resolution rejected events", func(t *testing.T) {
		// Test all the events with an associated "rejected" resolution to pull out.
		events := []*pb.RequestEvent{
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_CompletionResolution{
					CompletionResolution: &pb.CompletionResolution{
						AcceptedIdx: -1,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_EditResolution{
					EditResolution: &pb.EditResolution{
						IsAccepted: false,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_NextEditResolution{
					NextEditResolution: &pb.NextEditResolution{
						IsAccepted: false,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_InstructionResolution{
					InstructionResolution: &pb.InstructionResolution{
						IsAcceptAll: false,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_InstructionResolution{
					InstructionResolution: &pb.InstructionResolution{
						IsRejectAll: true,
					},
				},
			},
			{
				Time: timestamppb.New(mockTime()),
				Event: &pb.RequestEvent_InstructionResolution{
					InstructionResolution: &pb.InstructionResolution{
						IsAcceptedChunks: []bool{
							false,
						},
					},
				},
			},
		}
		for _, event := range events {
			result := getResolutionRow("test-request", tenantInfo(), event)
			expected := &resolutionRow{
				RequestEventBase: RequestEventBase{
					RequestId: "test-request",
					TenantID:  tenantID,
					Tenant:    tenantName,
					Time:      mockTime(),
				},
				Accepted: false,
			}
			if !reflect.DeepEqual(result, expected) {
				t.Errorf("Expected %v, got %v", expected, result)
			}
		}
	})

	t.Run("no resolution", func(t *testing.T) {
		// Test an event that doesn't have a resolution to pull out.
		event := &pb.RequestEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionHostRequest{},
		}

		result := getResolutionRow("test-request", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

func TestGetHttpStatusRow(t *testing.T) {
	t.Run("http status event", func(t *testing.T) {
		// Test an event that has a http status to pull out.
		event := &pb.RequestEvent{
			Time: timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_ApiHttpResponse{
				ApiHttpResponse: &pb.ApiHttpResponse{
					Code: 200,
				},
			},
		}

		result := getHttpStatusRow("test-request", tenantInfo(), event)
		expected := &httpStatusRow{
			RequestEventBase: RequestEventBase{
				RequestId: "test-request",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			Code: 200,
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("no http status", func(t *testing.T) {
		// Test an event that doesn't have a http status to pull out.
		event := &pb.RequestEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionHostRequest{},
		}

		result := getHttpStatusRow("test-request", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

func TestGetCompletionResponseRow(t *testing.T) {
	t.Run("completion response event", func(t *testing.T) {
		// Test an event that has a completion response to pull out.
		event := &pb.RequestEvent{
			Time: timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionHostResponse{
				CompletionHostResponse: &pb.CompletionHostResponse{
					Text: "test-text",
				},
			},
		}

		result := getCompletionResponseRow("test-request", tenantInfo(), event)
		expected := &completionResponseRow{
			RequestEventBase: RequestEventBase{
				RequestId: "test-request",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			CharacterCount:    9,
			NonemptyLineCount: 1,
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("no completion response", func(t *testing.T) {
		// Test an event that doesn't have a completion response to pull out.
		event := &pb.RequestEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionHostRequest{},
		}

		result := getCompletionResponseRow("test-request", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

func TestGetCompletionPostProcessRow(t *testing.T) {
	t.Run("completion post process event", func(t *testing.T) {
		// Test an event that has a completion post process to pull out.
		var appliedFilterThreshold float32 = 1 // 1 because of float precision problems testing equality
		filterReason := pb.CompletionPostProcess_LOW_QUALITY
		event := &pb.RequestEvent{
			Time: timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionPostProcess{
				CompletionPostProcess: &pb.CompletionPostProcess{
					FilterScore:            0.5,
					AppliedFilterThreshold: &appliedFilterThreshold,
					FilterReason:           &filterReason,
				},
			},
		}

		result := getCompletionPostProcessRow("test-request", tenantInfo(), event)
		expected := &completionPostProcessRow{
			RequestEventBase: RequestEventBase{
				RequestId: "test-request",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			FilterScore: 0.5,
			AppliedFilterThreshold: bigquery.NullFloat64{
				Valid:   true,
				Float64: 1,
			},
			FilterReason: bigquery.NullString{
				Valid:     true,
				StringVal: "LOW_QUALITY",
			},
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("no completion post process", func(t *testing.T) {
		// Test an event that doesn't have a completion post process to pull out.
		event := &pb.RequestEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionHostRequest{},
		}

		result := getCompletionPostProcessRow("test-request", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

func TestGetParenthesisTruncationRow(t *testing.T) {
	t.Run("parenthesis truncation event", func(t *testing.T) {
		// Test an event that has a parenthesis truncation to pull out.
		event := &pb.RequestEvent{
			Time: timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_ParenthesisTruncation{
				ParenthesisTruncation: &pb.ParenthesisTruncation{
					OriginalText:       "def example(arg1, arg2):",
					TruncatedText:      "def example(",
					WasTruncated:       true,
					CouldHaveTruncated: true,
					Path:               "test.py",
				},
			},
		}

		result := getParenthesisTruncationRow("test-request", tenantInfo(), event)
		expected := &parenthesisTruncationRow{
			RequestEventBase: RequestEventBase{
				RequestId: "test-request",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			OriginalLength:     24,
			TruncatedLength:    12,
			WasTruncated:       true,
			CouldHaveTruncated: true,
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("no parenthesis truncation", func(t *testing.T) {
		// Test an event that doesn't have a parenthesis truncation to pull out.
		event := &pb.RequestEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.RequestEvent_CompletionHostRequest{},
		}

		result := getParenthesisTruncationRow("test-request", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

func TestGetSessionEventRow(t *testing.T) {
	t.Run("request id event", func(t *testing.T) {
		// Test all the events that have an associated request id to pull out.
		eventId := "test-event"
		events := []*pb.SessionEvent{
			{
				Time:    timestamppb.New(mockTime()),
				EventId: &eventId,
				Event: &pb.SessionEvent_CompletionRequestIdIssued{
					CompletionRequestIdIssued: &pb.CompletionRequestIdIssuedEvent{
						RequestId: "test-request",
					},
				},
			},
			{
				Time:    timestamppb.New(mockTime()),
				EventId: &eventId,
				Event: &pb.SessionEvent_EditRequestIdIssued{
					EditRequestIdIssued: &pb.EditRequestIdIssuedEvent{
						RequestId: "test-request",
					},
				},
			},
			{
				Time:    timestamppb.New(mockTime()),
				EventId: &eventId,
				Event: &pb.SessionEvent_NextEditRequestIdIssued{
					NextEditRequestIdIssued: &pb.NextEditRequestIdIssuedEvent{
						RequestId: "test-request",
					},
				},
			},
		}
		for _, event := range events {
			result := getSessionEventRow("test-session", tenantInfo(), event)
			ref := event.ProtoReflect()
			expectedEventType := ref.WhichOneof(ref.Descriptor().Oneofs().ByName("event"))
			expected := &SessionEventRow{
				SessionEventBase: SessionEventBase{
					SessionId: "test-session",
					TenantID:  tenantID,
					Tenant:    tenantName,
					Time:      mockTime(),
				},
				EventType: string(expectedEventType.Name()),
				EventId:   "test-event",
				RequestId: "test-request",
			}
			if !reflect.DeepEqual(result, expected) {
				t.Errorf("Expected %v, got %v", expected, result)
			}
		}
	})

	t.Run("no request id", func(t *testing.T) {
		// Test an event that doesn't have a request id.
		eventId := "test-event"
		event := &pb.SessionEvent{
			Time:    timestamppb.New(mockTime()),
			EventId: &eventId,
			Event: &pb.SessionEvent_ClientMetric{
				ClientMetric: &pb.ClientMetric{
					EventName:    "test-event",
					UserAgent:    "test-user-agent",
					ClientMetric: "test-metric",
					Value:        42,
				},
			},
		}

		result := getSessionEventRow("test-session", tenantInfo(), event)
		expected := &SessionEventRow{
			SessionEventBase: SessionEventBase{
				SessionId: "test-session",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			EventType: "client_metric",
			EventId:   "test-event",
			RequestId: "",
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})
}

func TestGetRemoteAgentLogRow(t *testing.T) {
	t.Run("remote agent log event", func(t *testing.T) {
		// Test an event that has a remote agent log to pull out.
		eventId := "test-event"
		event := &pb.SessionEvent{
			Time:    timestamppb.New(mockTime()),
			EventId: &eventId,
			Event: &pb.SessionEvent_RemoteAgentLog{
				RemoteAgentLog: &pb.RemoteAgentLog{
					Entries: []*pb.RemoteAgentLogEntry{
						{
							Message:   "test-message",
							Transport: "test-transport",
							Timestamp: timestamppb.New(mockTime()),
						},
					},
					RemoteAgentId:  "test-remote-agent-id",
					Component:      "test-component",
					StartTimestamp: timestamppb.New(mockTime()),
					EndTimestamp:   timestamppb.New(mockTime()),
				},
			},
		}

		result := getRemoteAgentLogRow("test-session", tenantInfo(), event)
		expected := &remoteAgentLogRow{
			SessionEventBase: SessionEventBase{
				SessionId: "test-session",
				TenantID:  tenantID,
				Tenant:    tenantName,
				Time:      mockTime(),
			},
			EventId: "test-event",
		}
		if !reflect.DeepEqual(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("no remote agent log", func(t *testing.T) {
		// Test an event that doesn't have a remote agent log to pull out.
		event := &pb.SessionEvent{
			Time:  timestamppb.New(mockTime()),
			Event: &pb.SessionEvent_ClientMetric{},
		}

		result := getRemoteAgentLogRow("test-session", tenantInfo(), event)
		if result != nil {
			t.Errorf("Expected nil, got %v", result)
		}
	})
}

// Emulator tests go below this point.

func getNumRows(t *testing.T, tableName string, tenantID string) int {
	query := bqEmulator.Client.Query(fmt.Sprintf(`
		SELECT COUNT(*) AS count
		FROM test_dataset.%s
		WHERE tenant_id = @tenantID
	`, tableName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "tenantID", Value: tenantID},
	}

	it, err := query.Read(context.Background())
	if err != nil {
		t.Fatalf("Failed to read rows: %v", err)
	}

	var results []bigquery.Value
	err = it.Next(&results)
	if err != nil {
		t.Fatalf("Failed to read rows: %v", err)
	}
	return int(results[0].(int64))
}

func TestSearchProcessMessage(t *testing.T) {
	// Tests share an exporter but each test creates its own tenant for isolation.
	exporter := newTestSearchExporter(t)

	t.Run("missing tenant id", func(t *testing.T) {
		// Test that we don't crash when an event is missing tenant_info.
		badMessages := []*pb.RequestInsightMessage{
			{
				Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
					UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
						RequestId: "test-request",
						Events: []*pb.RequestEvent{
							{
								Event: &pb.RequestEvent_CompletionHostRequest{},
							},
						},
					},
				},
			},
			{
				Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
					RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
						SessionId: "test-session",
						Events: []*pb.SessionEvent{
							{
								Event: &pb.SessionEvent_ClientMetric{
									ClientMetric: &pb.ClientMetric{
										EventName: "test-event",
									},
								},
							},
						},
					},
				},
			},
		}
		for _, message := range badMessages {
			err := exporter.ProcessMessage(context.Background(), message)
			// When this test fails it actually results in a panic, but check err for completeness.
			if err != nil {
				t.Fatalf("Expected to drop message gracefully, but got error: %v", err)
			}
		}
	})

	t.Run("invalid time", func(t *testing.T) {
		// Test that we gracefully drop events invalid timestamps.
		tenantID := uuid.New().String()
		tenantInfo := &pb.TenantInfo{
			TenantId:   tenantID,
			TenantName: "test-tenant",
		}
		badMessages := []*pb.RequestInsightMessage{
			// Nil request event timestamp.
			{
				Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
					UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
						RequestId:  "test-request",
						TenantInfo: tenantInfo,
						Events: []*pb.RequestEvent{
							{
								Event: &pb.RequestEvent_CompletionHostRequest{},
							},
						},
					},
				},
			},
			// Nil session event timestamp.
			{
				Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
					RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
						SessionId:  "test-session",
						TenantInfo: tenantInfo,
						Events: []*pb.SessionEvent{
							{
								Event: &pb.SessionEvent_ClientMetric{
									ClientMetric: &pb.ClientMetric{
										EventName: "test-event",
									},
								},
							},
						},
					},
				},
			},
			// 1970 request event timestamp.
			{
				Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
					UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
						RequestId:  "test-request",
						TenantInfo: tenantInfo,
						Events: []*pb.RequestEvent{
							{
								Time:  timestamppb.New(time.Unix(0, 0)),
								Event: &pb.RequestEvent_CompletionHostRequest{},
							},
						},
					},
				},
			},
			// 1970 session event timestamp.
			{
				Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
					RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
						SessionId:  "test-session",
						TenantInfo: tenantInfo,
						Events: []*pb.SessionEvent{
							{
								Time: timestamppb.New(time.Unix(0, 0)),
								Event: &pb.SessionEvent_ClientMetric{
									ClientMetric: &pb.ClientMetric{
										EventName: "test-event",
									},
								},
							},
						},
					},
				},
			},
			// Year 3000 request event timestamp.
			{
				Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
					UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
						RequestId:  "test-request",
						TenantInfo: tenantInfo,
						Events: []*pb.RequestEvent{
							{
								Time:  timestamppb.New(time.Date(3000, 1, 1, 0, 0, 0, 0, time.UTC)),
								Event: &pb.RequestEvent_CompletionHostRequest{},
							},
						},
					},
				},
			},
			// Year 3000 session event timestamp.
			{
				Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
					RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
						SessionId:  "test-session",
						TenantInfo: tenantInfo,
						Events: []*pb.SessionEvent{
							{
								Time: timestamppb.New(time.Date(3000, 1, 1, 0, 0, 0, 0, time.UTC)),
								Event: &pb.SessionEvent_ClientMetric{
									ClientMetric: &pb.ClientMetric{
										EventName: "test-event",
									},
								},
							},
						},
					},
				},
			},
		}
		for _, message := range badMessages {
			err := exporter.ProcessMessage(context.Background(), message)
			if err != nil {
				t.Fatalf("Expected to drop event gracefully, but got error: %v", err)
			}
		}

		// Make sure we didn't write anything to BigQuery. When writing to the actual BigQuery this
		// results in a partition error, but the emulator doesn't have the same partitioning logic.
		query := bqEmulator.Client.Query(fmt.Sprintf(`
			SELECT COUNT(*) AS count
			FROM test_dataset.request_event
			FULL OUTER JOIN test_dataset.session_event USING (tenant_id)
			WHERE tenant_id = @tenantID
		`))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenantID", Value: tenantID},
		}
		it, err := query.Read(context.Background())
		if err != nil {
			t.Fatalf("Failed to read rows: %v", err)
		}
		var results []bigquery.Value
		err = it.Next(&results)
		if err != nil {
			t.Fatalf("Failed to read rows: %v", err)
		}
		if results[0].(int64) != 0 {
			t.Errorf("Expected 0 rows, got %d", results[0].(int64))
		}
	})

	t.Run("request metadata", func(t *testing.T) {
		tenantID := uuid.New().String()
		eventId := "test-event"

		metadataEvent := &pb.RequestEvent{
			Time:    timestamppb.New(mockTime()),
			EventId: &eventId,
			Event: &pb.RequestEvent_RequestMetadata{
				RequestMetadata: &pb.RequestMetadata{
					RequestType: pb.RequestType_COMPLETION,
					UserId:      "test-user",
					SessionId:   "test-session",
					UserAgent:   "test-user-agent",
				},
			},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
				UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
					RequestId: "test-request",
					TenantInfo: &pb.TenantInfo{
						TenantName: "test-tenant",
						TenantId:   tenantID,
					},
					Events: []*pb.RequestEvent{
						metadataEvent,
					},
				},
			},
		}
		err := exporter.ProcessMessage(context.Background(), pubsubMessage)
		if err != nil {
			t.Fatalf("Failed to process message: %v", err)
		}

		expectedRowCounts := map[string]int{
			"request_event":    1,
			"request_metadata": 1,
			"session_event":    0,
			"model":            0,
			"resolution":       0,
			"http_status":      0,
		}
		for tableName, expectedRowCount := range expectedRowCounts {
			actualRowCount := getNumRows(t, tableName, tenantID)
			if actualRowCount != expectedRowCount {
				t.Errorf("Expected %d rows in %s, got %d", expectedRowCount, tableName, actualRowCount)
			}
		}
	})

	t.Run("session event", func(t *testing.T) {
		tenantID := uuid.New().String()
		eventId := "test-event"

		sessionEvent := &pb.SessionEvent{
			Time:    timestamppb.New(mockTime()),
			EventId: &eventId,
			Event: &pb.SessionEvent_CompletionRequestIdIssued{
				CompletionRequestIdIssued: &pb.CompletionRequestIdIssuedEvent{
					RequestId: "test-request",
				},
			},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
				RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
					SessionId: "test-session",
					TenantInfo: &pb.TenantInfo{
						TenantName: "test-tenant",
						TenantId:   tenantID,
					},
					Events: []*pb.SessionEvent{
						sessionEvent,
					},
				},
			},
		}
		err := exporter.ProcessMessage(context.Background(), pubsubMessage)
		if err != nil {
			t.Fatalf("Failed to process message: %v", err)
		}

		expectedRowCounts := map[string]int{
			"session_event":    1,
			"request_event":    0,
			"request_metadata": 0,
			"model":            0,
			"resolution":       0,
			"http_status":      0,
		}
		for tableName, expectedRowCount := range expectedRowCounts {
			actualRowCount := getNumRows(t, tableName, tenantID)
			if actualRowCount != expectedRowCount {
				t.Errorf("Expected %d rows in %s, got %d", expectedRowCount, tableName, actualRowCount)
			}
		}
	})

	t.Run("multiple events", func(t *testing.T) {
		// Publish two events in the same pub/sub message.
		tenantID := uuid.New().String()
		eventId := "test-event"

		metadataEvent := &pb.RequestEvent{
			Time:    timestamppb.New(mockTime()),
			EventId: &eventId,
			Event: &pb.RequestEvent_RequestMetadata{
				RequestMetadata: &pb.RequestMetadata{
					RequestType: pb.RequestType_COMPLETION,
					UserId:      "test-user",
					SessionId:   "test-session",
					UserAgent:   "test-user-agent",
				},
			},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
				UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
					RequestId: "test-request",
					TenantInfo: &pb.TenantInfo{
						TenantName: "test-tenant",
						TenantId:   tenantID,
					},
					Events: []*pb.RequestEvent{
						metadataEvent,
						metadataEvent,
					},
				},
			},
		}
		err := exporter.ProcessMessage(context.Background(), pubsubMessage)
		if err != nil {
			t.Fatalf("Failed to process message: %v", err)
		}

		// Check that we wrote to the expected tables.
		expectedRowCounts := map[string]int{
			"request_event":    2,
			"request_metadata": 2,
			"session_event":    0,
			"model":            0,
			"resolution":       0,
			"http_status":      0,
		}
		for tableName, expectedRowCount := range expectedRowCounts {
			actualRowCount := getNumRows(t, tableName, tenantID)
			if actualRowCount != expectedRowCount {
				t.Errorf("Expected %d rows in %s, got %d", expectedRowCount, tableName, actualRowCount)
			}
		}
	})
}

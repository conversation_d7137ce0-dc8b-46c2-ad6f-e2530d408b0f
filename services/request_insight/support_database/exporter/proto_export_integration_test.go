package main

import (
	"context"
	"fmt"
	"io"
	"net"
	"testing"

	"cloud.google.com/go/storage"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/test/bufconn"

	"github.com/augmentcode/augment/base/go/secretstring"
	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	gcsproxyproto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	"github.com/augmentcode/augment/services/gcs_proxy/server"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/fsouza/fake-gcs-server/fakestorage"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/assert"
	"google.golang.org/api/iterator"
	"google.golang.org/protobuf/proto"
)

const bufSize = 1024 * 1024

var (
	claimsChannel   chan *auth.AugmentClaims
	tenantChannel   chan *tenantproto.Tenant
	incomingContext context.Context
	testGcsEmulator *fakestorage.Server
)

// Returns an exporter that will write to a fake GCS server via the GCS proxy.
func newTestProtoExporter(t *testing.T) ProtoExporter {
	claimsChannel = make(chan *auth.AugmentClaims, 1)
	tenantChannel = make(chan *tenantproto.Tenant)
	_, lis := setupFakeServer(t)

	// Create client connection to our fake server
	conn, err := grpc.Dial(
		"bufnet",
		grpc.WithContextDialer(func(context.Context, string) (net.Conn, error) {
			return lis.Dial()
		}),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		t.Fatalf("Failed to dial bufnet: %v", err)
	}

	// Create GCS proxy client
	gcsProxyClient := gcsproxyclient.NewGcsProxyClientFromConn(conn)

	rc := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.NewRandomRequestSessionId(),
		"export-test",
		secretstring.SecretString{},
	)
	incomingContext = requestcontext.NewIncomingContext(context.Background(), rc)

	var requestContextFunc getRequestContextFunc = func(ctx context.Context, tenantID string) (*requestcontext.RequestContext, error) {
		return rc, nil
	}

	return NewProtoExporter(gcsProxyClient, requestContextFunc)
}

func setupFakeServer(t *testing.T) (*grpc.Server, *bufconn.Listener) {
	lis := bufconn.Listen(bufSize)

	var claims *auth.AugmentClaims
	// Create a mock auth interceptor
	mockAuthInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		select {
		case newClaims := <-claimsChannel:
			claims = newClaims
		default:
			if claims == nil {
				claims = &auth.AugmentClaims{}
			}
		}
		log.Info().Msgf("Mock auth interceptor called; adding claims to context: %v ", claims)
		ctx = auth.WithAugmentClaims(ctx, claims)
		return handler(ctx, req)
	}

	// Create the server with the mock auth interceptor
	s := grpc.NewServer(
		grpc.UnaryInterceptor(mockAuthInterceptor),
	)

	ctx := context.Background()
	gcsEmulator, gcsObjects := server.NewGcsEmulatorWithDefaultObjectsForTesting(t)
	testGcsEmulator = gcsEmulator
	tenantClient := tenantwatcherclient.MakeMockTenantWatcherClientFromChannel(tenantChannel)
	service := server.NewTestService(ctx, gcsObjects, tenantClient)
	gcsproxyproto.RegisterGcsProxyServer(s, service)

	go func() {
		if err := s.Serve(lis); err != nil {
			t.Fatalf("Server exited with error: %v", err)
		}
	}()

	t.Cleanup(func() {
		// no need to clean up gcs emulator; test setup already hooks this in
		service.Close()
		s.Stop()
		lis.Close()
	})

	return s, lis
}

// Helper function to read an object (via emulator directly, not via the GCS proxy)
func readObject(t *testing.T, bucketName string, tenantID string, objectPath string) []byte {
	log.Info().Msgf("Reading object %s for tenant %s", objectPath, tenantID)
	bucket := testGcsEmulator.Client().Bucket(bucketName)
	obj := bucket.Object(objectPath)
	reader, err := obj.NewReader(context.Background())
	if err != nil {
		t.Fatalf("Failed to read object: %v", err)
	}
	defer reader.Close()
	data, err := io.ReadAll(reader)
	if err != nil {
		t.Fatalf("Failed to read object: %v", err)
	}
	return data
}

// Helper function to list objects in a bucket (via emulator directly, not via the GCS proxy)
func listObjects(t *testing.T, bucketName string, prefix string) []string {
	log.Info().Msgf("Listing objects in bucket %s with prefix %s", bucketName, prefix)
	bucket := testGcsEmulator.Client().Bucket(bucketName)
	query := &storage.Query{Prefix: prefix}
	var result []string
	it := bucket.Objects(context.Background(), query)
	for {
		attrs, err := it.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			t.Fatal(err)
			break
		}
		result = append(result, attrs.Name)
	}
	return result
}

func TestProtoProcessMessage(t *testing.T) {
	// Tests share an exporter but each test creates its own tenant for isolation.
	exporter := newTestProtoExporter(t)

	t.Run("Single request event", func(t *testing.T) {
		tenantID := uuid.New().String()
		tenantChannel <- &tenantproto.Tenant{
			Id:   tenantID,
			Tier: tenantproto.TenantTier_ENTERPRISE,
		}

		// Publish a single request event.
		eventId := "test-event-id"
		event := &pb.RequestEvent{
			EventId: &eventId,
			Event: &pb.RequestEvent_CompletionResolution{
				CompletionResolution: &pb.CompletionResolution{
					AcceptedIdx: 1,
				},
			},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
				UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
					RequestId: "test-request",
					TenantInfo: &pb.TenantInfo{
						TenantName: "test-tenant",
						TenantId:   tenantID,
					},
					Events: []*pb.RequestEvent{event},
				},
			},
		}

		// send the auth claims to the mock auth interceptor
		claims := &auth.AugmentClaims{
			TenantID: tenantID,
			Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
		}
		claimsChannel <- claims

		err := exporter.ProcessMessage(incomingContext, pubsubMessage)
		if err != nil {
			t.Fatal(err)
		}

		// Make sure we wrote a single object.
		objectNames := listObjects(t, "enterprise-bucket", tenantID)
		if len(objectNames) != 1 {
			t.Fatalf("Expected 1 object, got %d", len(objectNames))
		}
		// Make sure the object is named correctly.
		expectedKey := tenantID + "/request/test-request/completion_resolution/test-event-id"
		if objectNames[0] != expectedKey {
			t.Fatalf("Expected object name %s, got %s", expectedKey, objectNames[0])
		}

		// Read back the written object
		expectedPath := fmt.Sprintf("%s/request/test-request/completion_resolution/test-event-id", tenantID)
		data := readObject(t, "enterprise-bucket", tenantID, expectedPath)

		// Verify the data
		var storedEvent pb.RequestEvent
		err = proto.Unmarshal(data, &storedEvent)
		if err != nil {
			t.Fatal(err)
		}
		assert.Equal(t, event.EventId, storedEvent.EventId)
		assert.Equal(t, event.GetCompletionResolution().AcceptedIdx, storedEvent.GetCompletionResolution().AcceptedIdx)
	})

	t.Run("Single session event", func(t *testing.T) {
		tenantID := uuid.New().String()
		tenantChannel <- &tenantproto.Tenant{
			Id:   tenantID,
			Tier: tenantproto.TenantTier_ENTERPRISE,
		}
		eventId := "test-event-id"

		// Publish a single session event.
		event := &pb.SessionEvent{
			EventId: &eventId,
			Event:   &pb.SessionEvent_NextEditSessionEvent{},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
				RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
					SessionId: "test-session",
					TenantInfo: &pb.TenantInfo{
						TenantName: "test-tenant",
						TenantId:   tenantID,
					},
					Events: []*pb.SessionEvent{event},
				},
			},
		}
		// send the auth claims to the mock auth interceptor
		claims := &auth.AugmentClaims{
			TenantID: tenantID,
			Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
		}
		claimsChannel <- claims
		err := exporter.ProcessMessage(incomingContext, pubsubMessage)
		if err != nil {
			t.Fatal(err)
		}

		// Make sure we wrote a single object.
		objectNames := listObjects(t, "enterprise-bucket", tenantID)
		if len(objectNames) != 1 {
			t.Fatalf("Expected 1 object, got %d", len(objectNames))
		}

		// Make sure the object is named correctly.
		expectedKey := tenantID + "/session/test-session/next_edit_session_event/test-event-id"
		if objectNames[0] != expectedKey {
			t.Fatalf("Expected object name %s, got %s", expectedKey, objectNames[0])
		}

		// Make sure the object contains the expected data.
		expectedPath := fmt.Sprintf("%s/session/test-session/next_edit_session_event/test-event-id", tenantID)
		data := readObject(t, "enterprise-bucket", tenantID, expectedPath)
		protoData := &pb.SessionEvent{}
		proto.Unmarshal(data, protoData)
		if !proto.Equal(protoData, event) {
			t.Fatalf(
				"Proto data from bucket does not match expected data. Expected %v, got %v",
				event, protoData)
		}
	})

	t.Run("Single session event without TenantInfo", func(t *testing.T) {
		// Publish a single session event without TenantInfo.
		eventId := "test-event-id"
		event := &pb.SessionEvent{
			EventId: &eventId,
			Event:   &pb.SessionEvent_NextEditSessionEvent{},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_RecordSessionEventsRequest{
				RecordSessionEventsRequest: &pb.RecordSessionEventsRequest{
					SessionId: "test-session",
					Events:    []*pb.SessionEvent{event},
				},
			},
		}
		err := exporter.ProcessMessage(incomingContext, pubsubMessage)
		// We should drop the message without an error but not write anythign to the bucket.
		if err != nil {
			t.Fatal(err)
		}
		objectNames := listObjects(t, "enterprise-bucket", "session/")
		if len(objectNames) != 0 {
			t.Fatalf("Expected 0 objects, got %d", len(objectNames))
		}
	})

	t.Run("Multiple events", func(t *testing.T) {
		tenantID := uuid.New().String()
		tenantChannel <- &tenantproto.Tenant{
			Id:   tenantID,
			Tier: tenantproto.TenantTier_ENTERPRISE,
		}

		// Publish two of the same type of event. Note that they'll each get their own event id
		// since we're not specifying one.
		event := &pb.RequestEvent{
			Event: &pb.RequestEvent_CompletionResolution{
				CompletionResolution: &pb.CompletionResolution{
					AcceptedIdx: 1,
				},
			},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
				UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
					RequestId: "test-request",
					TenantInfo: &pb.TenantInfo{
						TenantName: "test-tenant",
						TenantId:   tenantID,
					},
					Events: []*pb.RequestEvent{
						event,
						event,
					},
				},
			},
		}
		// send the auth claims to the mock auth interceptor
		claims := &auth.AugmentClaims{
			TenantID: tenantID,
			Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
		}
		claimsChannel <- claims
		err := exporter.ProcessMessage(incomingContext, pubsubMessage)
		if err != nil {
			t.Fatal(err)
		}

		// Make sure both events were written.
		objectNames := listObjects(t, "enterprise-bucket", tenantID+"/request/test-request/completion_resolution/")
		if len(objectNames) != 2 {
			t.Fatalf("Expected 2 objects, got %d: %v", len(objectNames), objectNames)
		}
	})

	t.Run("Idempotent writes", func(t *testing.T) {
		tenantID := uuid.New().String()
		tenantChannel <- &tenantproto.Tenant{
			Id:   tenantID,
			Tier: tenantproto.TenantTier_ENTERPRISE,
		}

		// Publish the same event (with the same event id) twice.
		eventId := "test-event-id"
		event := &pb.RequestEvent{
			EventId: &eventId,
			Event: &pb.RequestEvent_CompletionResolution{
				CompletionResolution: &pb.CompletionResolution{
					AcceptedIdx: 1,
				},
			},
		}
		pubsubMessage := &pb.RequestInsightMessage{
			Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
				UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
					RequestId: "test-request",
					TenantInfo: &pb.TenantInfo{
						TenantName: "test-tenant",
						TenantId:   tenantID,
					},
					Events: []*pb.RequestEvent{
						event,
					},
				},
			},
		}
		// send the auth claims to the mock auth interceptor
		claims := &auth.AugmentClaims{
			TenantID: tenantID,
			Scope:    []string{"REQUEST_RESTRICTED_R", "REQUEST_RESTRICTED_RW"},
		}
		claimsChannel <- claims
		err := exporter.ProcessMessage(incomingContext, pubsubMessage)
		if err != nil {
			t.Fatal(err)
		}
		claimsChannel <- claims
		err = exporter.ProcessMessage(incomingContext, pubsubMessage)
		if err != nil {
			t.Fatal(err)
		}

		// Make sure only one event was written.
		objectNames := listObjects(t, "enterprise-bucket", tenantID+"/request/test-request/completion_resolution/")
		if len(objectNames) != 1 {
			t.Fatalf("Expected 1 objects, got %d: %v", len(objectNames), objectNames)
		}
	})
}

package main

import (
	"context"
	"fmt"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	gcsproxyclient "github.com/augmentcode/augment/services/gcs_proxy/client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	jwt "github.com/golang-jwt/jwt/v5"
	"github.com/rs/zerolog/log"
)

// SupportDatabaseExporter is an exporter that writes request insight events to the configured GCS
// bucket and BigQuery search dataset.

// small interfaces just with needed functions for easier testing
type tokenExchangeClient interface {
	GetSignedTokenForService(ctx context.Context, tenantID string, scopes []tokenexchangeproto.Scope) (secretstring.SecretString, error)
	Close()
}

type tenantCache interface {
	GetTenant(tenantID string) (*tenantproto.Tenant, error)
	Close()
}

type SupportDatabaseExporter struct {
	protoExporter       ProtoExporter
	searchExporter      SearchExporter
	tokenExchangeClient tokenExchangeClient
	tenantCache         tenantCache
	rcCachedInfo        *rcCachedInfo
}

type rcCachedInfo struct {
	currentTokens     map[string]*secretstring.SecretString
	sessionID         string
	tokenExpiryTimes  map[string]time.Time
	tokenExpiryBuffer time.Duration
}

func (r *rcCachedInfo) getTokenOrNil(tenantID string) *secretstring.SecretString {
	if r.currentTokens[tenantID] == nil {
		return nil
	}
	if _, ok := r.tokenExpiryTimes[tenantID]; !ok {
		return nil
	}

	timeUntilExpiry := time.Until(r.tokenExpiryTimes[tenantID])
	if timeUntilExpiry < r.tokenExpiryBuffer {
		delete(r.currentTokens, tenantID)
		delete(r.tokenExpiryTimes, tenantID)
		return nil
	}
	return r.currentTokens[tenantID]
}

// treating as a dependency for testing, and also because it's not truly proto export specific
func (d *SupportDatabaseExporter) getRequestContext(ctx context.Context, tenantID string) (*requestcontext.RequestContext, error) {
	sessionID := requestcontext.RequestSessionId(d.rcCachedInfo.sessionID)
	requestID := requestcontext.NewRandomRequestId()
	requestSource := "proto-export"

	// if we already have a non expired cached token for this tenant Id, use that
	if t := d.rcCachedInfo.getTokenOrNil(tenantID); t != nil {
		return requestcontext.New(requestID, sessionID, requestSource, *t), nil
	}

	scopes := []tokenexchangeproto.Scope{tokenexchangeproto.Scope_REQUEST_RESTRICTED_RW}
	tenant, err := d.tenantCache.GetTenant(tenantID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting tenant")
		return nil, err
	}
	t, err := d.tokenExchangeClient.GetSignedTokenForService(ctx, tenant.Id, scopes)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting token")
		return nil, err
	}

	// Decode the token to cache the expiry time
	token, _, err := jwt.NewParser().ParseUnverified(t.Expose(), &auth.AugmentClaims{})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error decoding token")
		return nil, err
	}
	claims, ok := token.Claims.(*auth.AugmentClaims)
	if !ok {
		log.Ctx(ctx).Error().Err(err).Msg("unexpected claim")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("Caching token for tenant %s until %s", tenantID, claims.ExpiresAt.Time)
	d.rcCachedInfo.currentTokens[tenantID] = &t
	d.rcCachedInfo.tokenExpiryTimes[tenantID] = claims.ExpiresAt.Time
	// We could consider, say, a long lived session ID while the pod is up. Otherwise this looks right.
	return requestcontext.New(requestID, sessionID, requestSource, t), nil
}

// Constructor for SupportDatabaseExporter.
func NewSupportDatabaseExporter(
	ctx context.Context,
	searchExportConfig *SearchExportConfig,
	gcsProxyClient gcsproxyclient.GcsProxyClient,
	tenantCache tenantCache,
	tokenExchangeClient tokenExchangeClient,
) (*SupportDatabaseExporter, error) {
	d := &SupportDatabaseExporter{
		tokenExchangeClient: tokenExchangeClient,
		tenantCache:         tenantCache,
		rcCachedInfo: &rcCachedInfo{
			sessionID:         requestcontext.NewRandomRequestSessionId().String(),
			currentTokens:     map[string]*secretstring.SecretString{},
			tokenExpiryTimes:  map[string]time.Time{},
			tokenExpiryBuffer: 2 * time.Minute,
		},
	}

	protoExporter := NewProtoExporter(gcsProxyClient, d.getRequestContext)
	d.protoExporter = protoExporter

	searchExporter, err := NewSearchExporter(ctx, searchExportConfig)
	if err != nil {
		return nil, fmt.Errorf("Failed to create search exporter: %w", err)
	}
	d.searchExporter = searchExporter

	return d, nil
}

func (e *SupportDatabaseExporter) Close() error {
	protoExporterCloseErr := e.protoExporter.Close()
	searchExporterCloseErr := e.searchExporter.Close()
	if protoExporterCloseErr != nil || searchExporterCloseErr != nil {
		return fmt.Errorf(
			"Failed to close one or more exporters: %w, %w",
			protoExporterCloseErr, searchExporterCloseErr)
	}
	return nil
}

// Process a single message from the pub/sub queue. This is intended to be used with
// RequestInsightSubscriber.
func (e *SupportDatabaseExporter) ProcessMessage(
	ctx context.Context, message *pb.RequestInsightMessage,
) error {
	err := e.protoExporter.ProcessMessage(ctx, message)
	if err != nil {
		// If we fail to export protos, immediately fail the entire export. This helps ensure that
		// requests returned by the search dataset have at least some of their proto data present in the
		// GCS bucket (not all of their proto data, since we export data on a per-event basis rather
		// than per-request). Note that writes to the bucket are idempotent (thanks to the event id in
		// the object keys), so it's always safe to retry writing to the bucket later.
		return fmt.Errorf("Failed to export protos: %w", err)
	}

	err = e.searchExporter.ProcessMessage(ctx, message)
	if err != nil {
		return fmt.Errorf("Failed to export search data: %w", err)
	}

	return nil
}

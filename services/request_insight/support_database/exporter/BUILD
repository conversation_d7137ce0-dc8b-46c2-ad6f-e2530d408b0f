load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")

go_library(
    name = "exporter_lib",
    srcs = [
        "exporter.go",
        "main.go",
        "proto_export.go",
        "search_export.go",
    ],
    importpath = "github.com/augmentcode/augment/services/request_insight/support_database/exporter",
    visibility = ["//visibility:private"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//services/gcs_proxy/client:client_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/lib:request_insight_subscriber_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_google_uuid//:uuid",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "exporter_test",
    srcs = [
        "exporter_test.go",
        "proto_export_test.go",
        "search_export_test.go",
        "test_fixtures.go",
    ],
    data = [
        "//services/request_insight/support_database/search_dataset:schema_json",
    ],
    embed = [":exporter_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//base/test_utils/bigquery:emulator_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/chat_host:chat_host_go_proto",
        "//services/edit_host:edit_proto_go_proto",
        "//services/gcs_proxy/client:client_go",
        "//services/next_edit_host:next_edit_proto_go_proto",
        "@com_github_fsouza_fake_gcs_server//fakestorage",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_api//iterator",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "proto_export_integration_test",
    srcs = ["proto_export_integration_test.go"],
    embed = [":exporter_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/gcs_proxy:gcs_proxy_go_proto",
        "//services/gcs_proxy/client:client_go",
        "//services/gcs_proxy/server:gcs_proxy_server_lib",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "@com_github_fsouza_fake_gcs_server//fakestorage",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_api//iterator",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//test/bufconn",
    ],
)

go_binary(
    name = "exporter",
    embed = [":exporter_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":exporter",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight/support_database:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/lib:bigquery_lib",
        "//services/request_insight/lib:exporter_lib",
        "//services/request_insight/support_database/event_buckets:bucket_lib",
        "//services/request_insight/support_database/search_dataset:dataset_lib",
    ],
)

local lib = import 'deploy/common/lib.jsonnet';
local saLib = import 'services/request_insight/external_service_accounts_lib.jsonnet';
local bucketLib = import 'services/request_insight/support_database/event_buckets/bucket_lib.jsonnet';

function(cloud, env, namespace, namespace_config)
  local appName = 'request-insight-event-buckets';
  local externalServiceAccounts = saLib.getExternalServiceAccounts(env, cloud);

  // Buckets use the terms "enterprise" and "nonenterprise". Non-enterprise is for customers whose
  // data we are allowed to access freely (i.e., dogfood, Vanguard, and aitutors). Everybody else
  // is enterprise.
  local nonenterpriseBucketName = bucketLib.bucketName(cloud, env, namespace, isEnterprise=false);
  local nonenterpriseBucket = {
    apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
    kind: 'StorageBucket',
    metadata: {
      annotations: {
        // If set to true, the force-destroy directive cleans up the objects within a storage bucket
        // before issuing the delete command. Setting it to false prevents the bucket from being
        // deleted if it is not empty.
        'cnrm.cloud.google.com/force-destroy': 'false',
      },
      labels: {
        app: appName,
      },
      name: nonenterpriseBucketName,
      namespace: namespace,
    },
    spec: {
      // Enable IAM policies for this bucket
      uniformBucketLevelAccess: true,
      location: **************(bucketLib.bucketLocation(cloud)),
    },
  };

  local enterpriseBucketName = bucketLib.bucketName(cloud, env, namespace, isEnterprise=true);
  local enterpriseBucket = {
    apiVersion: 'storage.cnrm.cloud.google.com/v1beta1',
    kind: 'StorageBucket',
    metadata: {
      annotations: {
        // If set to true, the force-destroy directive cleans up the objects within a storage bucket
        // before issuing the delete command. Setting it to false prevents the bucket from being
        // deleted if it is not empty.
        'cnrm.cloud.google.com/force-destroy': 'false',
      },
      labels: {
        app: appName,
      },
      name: enterpriseBucketName,
      namespace: namespace,
    },
    spec: {
      // Enable IAM policies for this bucket
      uniformBucketLevelAccess: true,
      location: **************(bucketLib.bucketLocation(cloud)),
      // Enterprise tenants have their RI data garbage collected after ~2 weeks. We add an extra 2
      // days of buffer to the protos to prevent the race condition where an event is referenced in
      // the search database but its proto data has already been deleted.
      lifecycleRule: [{
        action: {
          type: 'Delete',
        },
        condition: {
          age: 16,
        },
      }],
    },
  };

  local nonenterpriseBucketEngAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-eng-access-policy' % nonenterpriseBucketName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'StorageBucket',
        external: nonenterpriseBucketName,
      },
      bindings: [
        {
          // Give engineers read access in staging/prod and admin access in dev.
          role: if env != 'DEV' then 'roles/storage.objectViewer' else 'roles/storage.objectAdmin',
          members: [
            // Direct eng access.
            { member: 'group:%s' % '<EMAIL>' },
            // Spark job access.
            { member: 'serviceAccount:%s' % externalServiceAccounts.sparkSA.email },
          ],
        },
      ],
    },
  };

  local devEnterpriseBucketEngAccess = if env == 'DEV' then {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: 'dev-%s-eng-access-policy' % enterpriseBucketName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'StorageBucket',
        external: enterpriseBucketName,
      },
      bindings: [
        {
          // Give engineers admin access in dev only
          role: 'roles/storage.objectAdmin',
          members: [
            // Direct eng access.
            { member: 'group:%s' % '<EMAIL>' },
            // Spark job access.
            { member: 'serviceAccount:%s' % externalServiceAccounts.sparkSA.email },
          ],
        },
      ],
    },
  } else null;

  lib.flatten([
    nonenterpriseBucket,
    enterpriseBucket,
    nonenterpriseBucketEngAccess,
    if devEnterpriseBucketEngAccess != null then devEnterpriseBucketEngAccess else [],
  ])

// Get the location (US or EU) of the request insight events bucket for the given cloud.
local bucketLocation = function(cloud)
  {
    GCP_US_CENTRAL1_DEV: 'US',
    GCP_US_CENTRAL1_PROD: 'US',
    GCP_EU_WEST4_PROD: 'EU',
  }[cloud]
;

// Get the name of the request insight events bucket.
// Args:
// - env: The environment for the bucket.
// - namespace: The namespace that will be accessing/writing to the bucket.
// - isEnterprise: Whether the bucket contains enterprise customer information that requires special
//     permissions to view.
local bucketName = function(cloud, env, namespace, isEnterprise)
  if env == 'DEV' then
    // This is a patch to maintain backwards compatibility before the enterprise bucket existed in dev
    if isEnterprise then
      '%s-request-insight-events' % namespace
    else
      '%s-request-insight-events-nonenterprise' % namespace
  else
    std.asciiLower('%s-%s-request-insight-events-%s' % [
      bucketLocation(cloud),
      env,
      if isEnterprise then 'enterprise' else 'nonenterprise',
    ])
;

{
  bucketLocation: bucketLocation,
  bucketName: bucketName,
}

load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    visibility = [
        "//services/request_insight/support_database:__subpackages__",
    ],
    deps = [
        ":bucket_lib",
        "//deploy/common:lib",
        "//services/request_insight:external_service_accounts_lib",
    ],
)

kubecfg_library(
    name = "bucket_lib",
    srcs = ["bucket_lib.jsonnet"],
    visibility = [
        "//services:__subpackages__",
    ],
)

load("//tools/bzl:metadata.bzl", "metadata_test")

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        "//deploy/tenants:namespaces",
        "//services/request_insight/support_database/event_buckets:kubecfg",
        "//services/request_insight/support_database/exporter:kubecfg",
        "//services/request_insight/support_database/search_dataset:all_tenants_views_kubecfg",
        "//services/request_insight/support_database/search_dataset:kubecfg",
    ],
)

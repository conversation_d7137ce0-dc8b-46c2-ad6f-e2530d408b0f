load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "request_insight_central_client_py",
    srcs = ["client.py"],
    visibility = [
        "//experimental:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/central:request_insight_central_py_proto",
    ],
)

load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

ts_proto_library(
    name = "request_insight_central_ts_proto",
    data = [
        "//services/request_insight:request_insight_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":request_insight_central_proto",
    visibility = ["//services:__subpackages__"],
)

proto_library(
    name = "request_insight_central_proto",
    srcs = ["request_insight_central.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/request_insight:request_insight_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "request_insight_central_go_proto",
    importpath = "github.com/augmentcode/augment/services/request_insight/central/proto",
    proto = ":request_insight_central_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/request_insight:request_insight_go_proto",
    ],
)

py_grpc_library(
    name = "request_insight_central_py_proto",
    protos = [":request_insight_central_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/request_insight:request_insight_py_proto",
    ],
)

// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';
local datasetLib = import 'services/request_insight/support_database/search_dataset/dataset_lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  local appName = 'request-insight-central';
  local shortAppName = 'ri-central';
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.mountLaunchDarklySecret(
    env=env, namespace=namespace, cloud=cloud, appName=appName, serviceAccount=serviceAccount
  );

  local nonenterpriseDatasetLib = datasetLib(cloud, env, namespace, isEnterprise=false);
  local enterpriseDatasetLib = datasetLib(cloud, env, namespace, isEnterprise=true);

  // RI central isn't a true global service (it's deployed to both us-central1 and eu-west4), but
  // we want to be able to access EU data from the US (since the support-ui Remix backend is
  // only deployed in the US), so we need to deploy the global loadbalancer.
  local services = lib.flatten([
    grpcLib.grpcService(appName, namespace),
    if env != 'DEV' then grpcLib.globalGrpcService(cloud, appName, namespace) else [],
  ]);
  local hostnames = lib.flatten([
    grpcLib.grpcServiceNames(appName, namespace),
    if env != 'DEV' then grpcLib.globalGrpcServiceHostname(cloud, appName, namespace) else [],
  ]);

  // This is a central service so we only need central certs.
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local serverCert = certLib.createCentralServerCert(
    name='%s-server-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=hostnames,
    volumeName='certs',
  );

  local config = {
    port: 50051,
    serverMtls: if mtls then serverCert.config else null,
    clientMtls: if mtls then clientCert.config else null,
    promPort: 9090,
    namespace: namespace,
    tokenExchangeEndpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    gcsProxyEndpoint: 'gcs-proxy-svc:50051',
    featureFlagsSdkKeyPath: dynamicFeatureFlags.filePath,
    dynamicFeatureFlagsEndpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    projectId: cloudInfo[cloud].projectId,

    // To simplify queries, we use views that union the enterprise and non-enterprise datasets in
    // staging/prod.
    searchDatasetName: if env != 'DEV' then nonenterpriseDatasetLib.allTenantsViewsDatasetGcp else nonenterpriseDatasetLib.datasetGcp,
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // Give access to the search dataset. In order to query the all-tenants views, we need access to
  // both datasets.
  local datasetAccess = lib.flatten([
    bigqueryLib.datasetAccess(
      namespace,
      appName,
      nonenterpriseDatasetLib.readonlyCloudIdentityGroup,
      nonenterpriseDatasetLib.dataNamespace,
      serviceAccount.serviceAccountGcpEmailAddress,
    ),
    if env == 'DEV' then [] else bigqueryLib.datasetAccess(
      namespace,
      appName,
      enterpriseDatasetLib.readonlyCloudIdentityGroup,
      enterpriseDatasetLib.dataNamespace,
      serviceAccount.serviceAccountGcpEmailAddress,
    ),
    // Grant access to PII. The service has access to everyone's PII, and we rely on queries to
    // filter to the authorized tenant.
    gcpLib.grantAccess(
      name='%s-pii-finegrained-reader-access' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'DataCatalogPolicyTag',
        external: bigqueryLib.dataAccessPolicyTag(cloud, env, 'pii'),
      },
      bindings=[
        {
          role: 'roles/datacatalog.categoryFineGrainedReader',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ],
    ),
    // Give permission to start BigQuery jobs (needed to run queries).
    gcpLib.grantAccess(
      name='%s-bigquery-job-policy' % appName,
      env=env,
      namespace=namespace,
      appName=appName,
      resourceRef={
        kind: 'Project',
        external: 'project/%s' % cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/bigquery.jobUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: serviceAccount.iamServiceAccountName,
                },
              },
            },
          ],
        },
      ]
    ),
  ]);

  local container = {
    name: appName,
    target: {
      name: '//services/request_insight/central/server:image',
      dst: appName,
    },
    args: [
      '--config',
      configMap.filename,
    ],
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    volumeMounts: [
      configMap.volumeMountDef,
      clientCert.volumeMountDef,
      serverCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
    ],
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    resources: {
      limits: {
        cpu: 0.5,
        memory: '1Gi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    volumes: [
      configMap.podVolumeDef,
      clientCert.podVolumeDef,
      serverCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    clientCert.objects,
    dynamicFeatureFlags.objects,
    datasetAccess,
    deployment,
    services,
  ])

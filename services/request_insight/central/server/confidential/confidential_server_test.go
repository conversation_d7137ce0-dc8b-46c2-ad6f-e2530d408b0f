package ri_central_confidential

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	testutils "github.com/augmentcode/augment/base/test_utils"
	bqemulator "github.com/augmentcode/augment/base/test_utils/bigquery/emulator"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	pb "github.com/augmentcode/augment/services/request_insight/central/proto"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	projectId   = "test-project"
	datasetName = "test_dataset"
)

var (
	bqEmulator *bqemulator.BigQueryEmulator = nil
	mockTime   time.Time                    = time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)
)

// TestMain is the entry point for the test suite. This is where shared test fixtures should be
// set up.
func TestMain(m *testing.M) {
	// Set up a shared emulator for all the tests.
	var err error
	bqEmulator, err = bqemulator.New(context.Background(), projectId, datasetName)
	if err != nil {
		panic(err)
	}
	defer bqEmulator.Close()

	err = bqEmulator.LoadJsonSchemaFile(context.Background(), "../../../support_database/search_dataset/schema.json")
	if err != nil {
		panic(err)
	}

	// Run the tests.
	os.Exit(m.Run())
}

func testServer(t *testing.T) *ConfidentialServer {
	if bqEmulator == nil {
		t.Fatal("BigQuery emulator is not set up")
	}

	return &ConfidentialServer{
		bqClient:    bqEmulator.Client,
		datasetName: datasetName,
	}
}

func authorizedContext() context.Context {
	claims := &auth.AugmentClaims{
		UserID: "test-user",
		Scope:  []string{"REQUEST_CONFIDENTIAL_R"},
	}
	return claims.NewContext(context.Background())
}

func clearTable(t *testing.T, table string) {
	query := bqEmulator.Client.Query(fmt.Sprintf("DELETE FROM %s.%s WHERE TRUE", datasetName, table))
	_, err := query.Read(context.Background())
	if err != nil {
		t.Fatal(err)
	}
}

func writeMetadataRow(
	t *testing.T, requestId string, tenantID string, tenantName string, time time.Time,
	requestType string, sessionId string, userAgent string, userId string,
) {
	query := bqEmulator.Client.Query(fmt.Sprintf(`
		INSERT INTO %s.request_metadata (request_id, tenant, tenant_id, time, request_type, session_id, user_agent, user_id)
		VALUES (@REQUEST_ID, @TENANT, @TENANT_ID, @TIME, @REQUEST_TYPE, @SESSION_ID, @USER_AGENT, @USER_ID)
	`, datasetName))
	query.Parameters = []bigquery.QueryParameter{
		{Name: "REQUEST_ID", Value: requestId},
		{Name: "TENANT", Value: tenantName},
		{Name: "TENANT_ID", Value: tenantID},
		{Name: "TIME", Value: time},
		{Name: "REQUEST_TYPE", Value: requestType},
		{Name: "SESSION_ID", Value: sessionId},
		{Name: "USER_AGENT", Value: userAgent},
		{Name: "USER_ID", Value: userId},
	}
	_, err := query.Read(context.Background())
	if err != nil {
		t.Fatal(err)
	}
}

func TestCheckDatasetName(t *testing.T) {
	goodNames := []string{
		"prod_request_insight_dataset",
	}
	for _, name := range goodNames {
		if !checkDatasetName(name) {
			t.Errorf("Dataset name should be valid: %s", name)
		}
	}

	badNames := []string{
		"prod-request-insight-dataset",
		"prod_request_insight_dataset;",
		"; drop table x",
	}
	for _, name := range badNames {
		if checkDatasetName(name) {
			t.Errorf("Dataset name should be valid: %s", name)
		}
	}
}

// Run basic auth tests that apply to all endpoints.
func runAuthTests[ReqT proto.Message, RespT proto.Message](
	t *testing.T, goodRequest ReqT, requestFunc func(context.Context, ReqT) (RespT, error),
) {
	t.Run("auth validation", func(t *testing.T) {
		// Test missing auth claims.
		_, err := requestFunc(context.Background(), goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for missing auth claims")
		}

		// Token isn't central.
		wrongTenantClaims := &auth.AugmentClaims{
			TenantID:   "123",
			TenantName: "test-tenant",
			UserID:     "test-user",
			Scope:      []string{"REQUEST_CONFIDENTIAL_R"},
		}
		badCtx := wrongTenantClaims.NewContext(context.Background())
		_, err = requestFunc(badCtx, goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for non-central token")
		}

		// Test incorrect scope.
		badClaims := &auth.AugmentClaims{
			UserID: "test-user",
			Scope:  []string{"CONTENT_RW"},
		}
		badCtx = badClaims.NewContext(context.Background())
		_, err = requestFunc(badCtx, goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for missing scope")
		}
	})
}

func TestCrossTenantConfidentialSearch(t *testing.T) {
	s := testServer(t)

	t.Run("auth", func(t *testing.T) {
		runAuthTests(
			t,
			&pb.CrossTenantConfidentialSearchRequest{
				TimeFilter: &pb.TimeFilter{StartTime: timestamppb.New(mockTime)},
			},
			s.CrossTenantConfidentialSearch)
	})

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.CrossTenantConfidentialSearchRequest{
			// Missing time filter.
			{
				Filters: []*pb.CrossTenantConfidentialSearchRequest_Filter{
					{
						Filter: &pb.CrossTenantConfidentialSearchRequest_Filter_RequestId{
							RequestId: "test",
						},
					},
				},
			},
			// Missing start time.
			{
				TimeFilter: &pb.TimeFilter{
					EndTime: timestamppb.New(mockTime),
				},
			},
		}

		for _, req := range badRequests {
			_, err := s.CrossTenantConfidentialSearch(authorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected InvalidArgument error for request: %v", req)
			}
		}
	})

	t.Run("time filters", func(t *testing.T) {
		clearTable(t, "request_metadata")
		writeMetadataRow(
			t, "test-request", "test-tenant-id", "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id",
		)

		resultRow := &pb.CrossTenantConfidentialSearchResponse_Result{
			RequestId:   "test-request",
			SessionId:   "test-session",
			TenantInfo:  &pb.TenantInfo{TenantId: "test-tenant-id", TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			authorizedContext(),
			[]testutils.RequestResponse{
				// Start and end time that include the request.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
							EndTime:   timestamppb.New(mockTime.Add(time.Hour)),
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{
						Results: []*pb.CrossTenantConfidentialSearchResponse_Result{resultRow},
					},
				},
				// Just a start time.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{
						Results: []*pb.CrossTenantConfidentialSearchResponse_Result{resultRow},
					},
				},
				// Start time that excludes the request.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(time.Hour)),
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{},
				},
				// End time that excludes the request.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour * 2)),
							EndTime:   timestamppb.New(mockTime.Add(-time.Hour)),
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{},
				},
			},
			s.CrossTenantConfidentialSearch,
		)
	})

	t.Run("request id filter", func(t *testing.T) {
		clearTable(t, "request_metadata")
		writeMetadataRow(
			t, "test-request-1", "test-tenant-id", "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id",
		)
		writeMetadataRow(
			t, "test-request-2", "test-tenant-id", "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id",
		)

		resultRow := &pb.CrossTenantConfidentialSearchResponse_Result{
			RequestId:   "test-request-1",
			SessionId:   "test-session",
			TenantInfo:  &pb.TenantInfo{TenantId: "test-tenant-id", TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			authorizedContext(),
			[]testutils.RequestResponse{
				// Filter for a request that exists.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						Filters: []*pb.CrossTenantConfidentialSearchRequest_Filter{
							{
								Filter: &pb.CrossTenantConfidentialSearchRequest_Filter_RequestId{
									RequestId: "test-request-1",
								},
							},
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{
						Results: []*pb.CrossTenantConfidentialSearchResponse_Result{resultRow},
					},
				},
				// Filter for a request that doesn't exist.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						Filters: []*pb.CrossTenantConfidentialSearchRequest_Filter{
							{
								Filter: &pb.CrossTenantConfidentialSearchRequest_Filter_RequestId{
									RequestId: "bad-request-id",
								},
							},
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{},
				},
			},
			s.CrossTenantConfidentialSearch,
		)
	})

	t.Run("session id filter", func(t *testing.T) {
		clearTable(t, "request_metadata")
		writeMetadataRow(
			t, "test-request", "test-tenant-id", "test-tenant", mockTime, "COMPLETION", "test-session-1",
			"test-user-agent", "test-user-id",
		)
		writeMetadataRow(
			t, "test-request", "test-tenant-id", "test-tenant", mockTime, "COMPLETION", "test-session-2",
			"test-user-agent", "test-user-id",
		)

		resultRow := &pb.CrossTenantConfidentialSearchResponse_Result{
			RequestId:   "test-request",
			SessionId:   "test-session-1",
			TenantInfo:  &pb.TenantInfo{TenantId: "test-tenant-id", TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			authorizedContext(),
			[]testutils.RequestResponse{
				// Filter for a request that exists.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						Filters: []*pb.CrossTenantConfidentialSearchRequest_Filter{
							{
								Filter: &pb.CrossTenantConfidentialSearchRequest_Filter_SessionId{
									SessionId: "test-session-1",
								},
							},
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{
						Results: []*pb.CrossTenantConfidentialSearchResponse_Result{resultRow},
					},
				},
				// Filter for a request that doesn't exist.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						Filters: []*pb.CrossTenantConfidentialSearchRequest_Filter{
							{
								Filter: &pb.CrossTenantConfidentialSearchRequest_Filter_SessionId{
									SessionId: "bad-session-id",
								},
							},
						},
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{},
				},
			},
			s.CrossTenantConfidentialSearch,
		)
	})

	t.Run("max results", func(t *testing.T) {
		clearTable(t, "request_metadata")
		writeMetadataRow(
			t, "test-request-1", "test-tenant-id", "test-tenant", mockTime, "COMPLETION", "test-session",
			"test-user-agent", "test-user-id",
		)
		writeMetadataRow(
			t, "test-request-2", "test-tenant-id", "test-tenant", mockTime.Add(time.Hour), "COMPLETION", "test-session",
			"test-user-agent", "test-user-id",
		)

		resultRow1 := &pb.CrossTenantConfidentialSearchResponse_Result{
			RequestId:   "test-request-1",
			SessionId:   "test-session",
			TenantInfo:  &pb.TenantInfo{TenantId: "test-tenant-id", TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime),
			RequestType: "COMPLETION",
		}
		resultRow2 := &pb.CrossTenantConfidentialSearchResponse_Result{
			RequestId:   "test-request-2",
			SessionId:   "test-session",
			TenantInfo:  &pb.TenantInfo{TenantId: "test-tenant-id", TenantName: "test-tenant"},
			RequestTime: timestamppb.New(mockTime.Add(time.Hour)),
			RequestType: "COMPLETION",
		}

		testutils.CheckResponses(
			t,
			authorizedContext(),
			[]testutils.RequestResponse{
				// When max results is 2, both results should be returned, in reverse chronological order.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						MaxResults: proto.Uint32(2),
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{
						Results: []*pb.CrossTenantConfidentialSearchResponse_Result{resultRow2, resultRow1},
					},
				},
				// When max results is 1, only the most recent result should be returned.
				{
					Request: &pb.CrossTenantConfidentialSearchRequest{
						TimeFilter: &pb.TimeFilter{
							StartTime: timestamppb.New(mockTime.Add(-time.Hour)),
						},
						MaxResults: proto.Uint32(1),
					},
					Response: &pb.CrossTenantConfidentialSearchResponse{
						Results: []*pb.CrossTenantConfidentialSearchResponse_Result{resultRow2},
					},
				},
			},
			s.CrossTenantConfidentialSearch,
		)
	})
}

package ri_central_confidential

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	pb "github.com/augmentcode/augment/services/request_insight/central/proto"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// NB: The endpoints in this file are NOT permmitted to process or return restricted data, including
// PII.

const (
	defaultMaxResults = 100
)

type ConfidentialServer struct {
	bqClient    *bigquery.Client
	datasetName string
}

func New(
	ctx context.Context, projectId string, datasetName string,
) (*ConfidentialServer, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !checkDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, err
	}

	s := ConfidentialServer{
		bqClient:    bqClient,
		datasetName: datasetName,
	}
	return &s, nil
}

// Check that the provided dataset name contains only letters and underscores. Returns true iff the
// provided name is valid.
func checkDatasetName(name string) bool {
	return regexp.MustCompile(`^[a-zA-Z_]+$`).MatchString(name)
}

func (s ConfidentialServer) Close() error {
	return s.bqClient.Close()
}

// Returns an error that can be returned directly to the caller if the provided gRPC context does
// not contain auth claims that give access to cross-tenant confidential request data.
func (s ConfidentialServer) checkAuthClaims(ctx context.Context) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		// This shouldn't happen, since we have an auth interceptor that checks these claims before
		// calling our application logic.
		log.Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	if !authClaims.AllowsAllTenants() {
		log.Error().Msgf("Auth claims do not give permission for central service")
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	if !authClaims.HasScope(tokenexchangeproto.Scope_REQUEST_CONFIDENTIAL_R) {
		log.Error().Msgf("Auth claims do not give have scope REQUEST_CONFIDENTIAL_R")
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	return nil
}

func (s ConfidentialServer) CrossTenantConfidentialSearch(
	ctx context.Context, req *pb.CrossTenantConfidentialSearchRequest,
) (*pb.CrossTenantConfidentialSearchResponse, error) {
	if err := s.checkAuthClaims(ctx); err != nil {
		return nil, err
	}

	// Check for required arguments.
	if req.TimeFilter == nil || req.TimeFilter.StartTime == nil {
		return nil, status.Error(codes.InvalidArgument, "time_filter with start_time is required")
	}

	// Construct the query dynamically based on the provided filters.
	whereConditions := []string{}
	params := []bigquery.QueryParameter{}

	maxResults := req.GetMaxResults()
	if maxResults == 0 {
		maxResults = defaultMaxResults
	}
	params = append(params, bigquery.QueryParameter{Name: "MAX_RESULTS", Value: maxResults})

	// We checked above that the caller provided a start time.
	whereConditions = append(whereConditions, "time >= @START_TIME")
	params = append(params, bigquery.QueryParameter{Name: "START_TIME", Value: req.TimeFilter.StartTime.AsTime()})

	// End time is optional. If not specified we'll fetch everything up to the current time.
	if req.TimeFilter.EndTime != nil {
		whereConditions = append(whereConditions, "time <= @END_TIME")
		params = append(params, bigquery.QueryParameter{Name: "END_TIME", Value: req.TimeFilter.EndTime.AsTime()})
	}

	// Add optional filters.
	for _, filter := range req.Filters {
		switch filter.Filter.(type) {
		case *pb.CrossTenantConfidentialSearchRequest_Filter_RequestId:
			whereConditions = append(whereConditions, "request_id = @REQUEST_ID")
			params = append(params, bigquery.QueryParameter{Name: "REQUEST_ID", Value: filter.GetRequestId()})
		case *pb.CrossTenantConfidentialSearchRequest_Filter_SessionId:
			whereConditions = append(whereConditions, "session_id = @SESSION_ID")
			params = append(params, bigquery.QueryParameter{Name: "SESSION_ID", Value: filter.GetSessionId()})
		}
	}

	whereClause := strings.Join(whereConditions, " AND ")
	query := s.bqClient.Query(fmt.Sprintf(`
		SELECT
			metadata.request_id AS requestId,
			metadata.session_id AS sessionId,
			metadata.tenant_id AS tenantID,
			metadata.tenant AS tenant,
			metadata.time AS requestTime,
			metadata.request_type AS requestType
		FROM %s.request_metadata AS metadata
		WHERE %s
		ORDER BY requestTime DESC
		LIMIT @MAX_RESULTS
	`, s.datasetName, whereClause))
	query.Parameters = params

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Err(err).Msgf("Query error")
		return nil, status.Error(codes.Internal, "Query error")
	}

	// Parse the results.
	type bqResult struct {
		RequestId   string
		SessionId   string
		TenantID    string
		Tenant      string
		RequestTime time.Time
		RequestType string
	}
	var results []*pb.CrossTenantConfidentialSearchResponse_Result
	for {
		var bqRes bqResult
		err := it.Next(&bqRes)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Err(err).Msgf("Query results error")
			return nil, status.Error(codes.Internal, "Error parsing query results")
		} else {
			results = append(results, &pb.CrossTenantConfidentialSearchResponse_Result{
				RequestId: bqRes.RequestId,
				SessionId: bqRes.SessionId,
				TenantInfo: &pb.TenantInfo{
					TenantId:   bqRes.TenantID,
					TenantName: bqRes.Tenant,
				},
				RequestTime: timestamppb.New(bqRes.RequestTime),
				RequestType: bqRes.RequestType,
			})
		}
	}

	return &pb.CrossTenantConfidentialSearchResponse{
		Results: results,
	}, nil
}

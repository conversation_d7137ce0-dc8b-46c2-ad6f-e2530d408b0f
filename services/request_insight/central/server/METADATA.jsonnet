local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

{
  deployment: [
    {
      name: 'request-insight-central',
      kubecfg: {
        target: '//services/request_insight/central/server:kubecfg',
        task: [
          centralNamespace
          for centralNamespace in cloudInfo.centralNamespaces
          if centralNamespace.cloud == 'GCP_US_CENTRAL1_PROD' || centralNamespace.cloud == 'GCP_EU_WEST4_PROD'
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline', 'nikita'],
          slack_channel: '#team-insights',
        },
      },
    },
  ],
}

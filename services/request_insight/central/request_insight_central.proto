syntax = "proto3";

package request_insight.central;

import "google/protobuf/timestamp.proto";
import "services/request_insight/request_insight.proto";

// This file defines the API between the Remix backend for the global support site and the
// request-insight-central gRPC service. These protos are designed with the support site in mind,
// not for general use in search. Engineers are encouraged to query the nonenterprise support
// database directly as needed for their use-cases, as opposed to going through this service.
// There are two services, one for confidential data and one for restricted data. Both are served
// from the same kubernetes service; the distinction is primarily to make it clear which endpoints
// can return which data.

// The endpoints in this service do not require any special permissions and are therefore not
// allowed to receive/return any restricted data or PII.
service RequestInsightCentralConfidential {
  rpc CrossTenantConfidentialSearch(CrossTenantConfidentialSearchRequest) returns (CrossTenantConfidentialSearchResponse);
}

// The endpoints in this service require REQUEST_RESTRICTED_R permissions from Genie. They are
// allowed to receive/return confidential data, restricted data, and PII.
service RequestInsightCentralRestricted {
  rpc RestrictedSearch(RestrictedSearchRequest) returns (RestrictedSearchResponse);

  rpc GetRequestEvents(GetRequestEventsRequest) returns (stream GetRequestEventResponse);

  rpc GetRemoteAgentLogs(GetRemoteAgentLogsRequest) returns (stream GetRemoteAgentLogsResponse);
}

message TimeFilter {
  // Required.
  google.protobuf.Timestamp start_time = 1;

  // If not provided, default to "now".
  optional google.protobuf.Timestamp end_time = 2;
}

message TenantInfo {
  string tenant_id = 1;
  string tenant_name = 2;
}

message CrossTenantConfidentialSearchRequest {
  // New filters can be added here as needed, though note that restricted data and PII are not
  // allowed here.
  message Filter {
    oneof filter {
      string request_id = 1;
      string session_id = 2;
    }
  }

  // All searches are required to specify a time filter, though note that if you want everything
  // until "now", you can leave end_time empty.
  TimeFilter time_filter = 1;

  repeated Filter filters = 2;

  // For now limit to a fixed number of results. If we want to add pagination someday, we can change
  // this to page_size and also return a pagination cursor in the response.
  // If not provided, the server defines a reasonable default.
  optional uint32 max_results = 3;
}

message CrossTenantConfidentialSearchResponse {
  message Result {
    string request_id = 1;
    string session_id = 2;
    TenantInfo tenant_info = 3;
    google.protobuf.Timestamp request_time = 4;
    string request_type = 5;
  }

  // Results are sorted in descending order by request_time.
  repeated Result results = 1;
}

message RestrictedSearchRequest {
  // New filters on information from the search dataset can be added here as needed. Filters always
  // use an equality check.
  message BaseFilter {
    oneof filter {
      string request_id = 1;
      string session_id = 2;
      string user_id = 3;
      string request_type = 4;
      bool accepted = 5;
      string model_name = 6;
      uint32 http_status_code = 7;
    }
  }

  enum FilterConjunction {
    AND = 0;
    OR = 1;
  }

  message FilterGroup {
    FilterConjunction conjunction = 1;
    repeated Filter filters = 2;
  }

  message Filter {
    oneof filter {
      BaseFilter base_filter = 1;
      FilterGroup filter_group = 2;
    }
  }

  // Required. For now, requests much search on exactly one tenant. Someday we may extend this to
  // allow searching on all the tenants someone has Genie permissions for. (Note that making a field
  // repeated is backwards compatible on the wire).
  string tenant_id = 1;

  // All searches are required to specify a time filter, though note that if you want everything
  // until "now", you can leave end_time empty.
  TimeFilter time_filter = 2;

  // These filters will be resolved recursively.
  optional FilterGroup filters = 3;

  // For now limit to a fixed number of results. If we want to add pagination someday, we can change
  // this to page_size and also return a pagination cursor in the response.
  // If not provided, the server defines a reasonable default.
  optional uint32 max_results = 4;
}

message RestrictedSearchResponse {
  message Result {
    string request_id = 1;
    string session_id = 2;
    string user_id = 3;
    TenantInfo tenant_info = 4;
    google.protobuf.Timestamp request_time = 5;
    string request_type = 6;
    optional string model_name = 7;
    optional bool accepted = 8;
    optional uint32 http_status_code = 9;
    // TODO(jacqueline): Add response text. We need gcs-proxy or a full-text search table first.
  }

  // Results are sorted in descending order by request_time.
  repeated Result results = 1;
}

message GetRequestEventsRequest {
  message Filter {
    oneof filter {
      string event_type = 1;
    }
  }

  string tenant_id = 1;
  string request_id = 2;
  repeated Filter filters = 3;
}

message GetRequestEventResponse {
  request_insight.RequestEvent request_event = 1;
}

message GetRemoteAgentLogsRequest {
  string tenant_id = 1;

  // The ID of the remote agent to retrieve logs for.
  string agent_id = 2;

  // A time filter for the logs is required, though note that if you want everything until "now",
  // you can leave end_time empty.
  TimeFilter time_filter = 3;

  // Limit to a fixed number of results. If not provided, the server defines a reasonable default.
  optional uint32 max_results = 4;
}

message GetRemoteAgentLogsResponse {
  request_insight.RemoteAgentLog remote_agent_log = 1;
}

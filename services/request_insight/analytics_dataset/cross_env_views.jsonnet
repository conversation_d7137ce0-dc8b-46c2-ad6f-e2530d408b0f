// Deploy cross-environment views for all the analytics tables. Note that env is ignored; this
// should only be deployed for prod, since it references both staging and prod.
// Ideally we would also join US and EU tables, but unfortunately you can't perform cross-region
// joins in BigQuery.
function(cloud, env, namespace, namespace_config)
  local appName = 'cross-env-request-insight-bigquery-analytics';

  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];
  local prodDatasetLib = (import 'dataset_lib.jsonnet')(cloud, 'PROD', 'central', false);
  local stagingDatasetLib = (import 'dataset_lib.jsonnet')(cloud, 'STAGING', 'central-staging', false);
  local lib = import 'deploy/common/lib.jsonnet';
  local datasetName = 'us_cross_env_request_insight_analytics_dataset';

  local engAccess = [
    {
      role: 'READER',
      groupByEmail: '<EMAIL>',
    },
  ];
  // BigQuery datasets require an explicit owner, and configconnector needs admin privileges to be
  // able to run updates.
  local adminAccess = [
    {
      role: 'OWNER',
      userByEmail: '<EMAIL>',
    },
  ];

  local dataset = {
    apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
    kind: 'BigQueryDataset',
    metadata: {
      name: std.strReplace(datasetName, '_', '-'),
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Request Insight BigQuery dataset for cross-environment views',
      resourceID: datasetName,
      location: 'US',
      projectRef: {
        external: cloudInfo.projectId,
      },
      // BigQueryDataset doesn't support IAM and uses legacy role formats. See
      // https://cloud.google.com/config-connector/docs/reference/resource-docs/bigquery/bigquerydataset
      access: lib.flatten([
        engAccess,
        adminAccess,
      ]),
    },
  };

  // Create views for every table and view in the schema. (BigQuery supports making views that query
  // other views.)
  local schema = (import 'schema.jsonnet')(prodDatasetLib.datasetGcp);
  local views = [
    {
      apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
      kind: 'BigQueryTable',
      metadata: {
        name: 'us-cross-env-request-insight-analytics-%s-view' % [std.strReplace(tableName, '_', '-')],
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        description: 'Cross-environment view of %s' % tableName,
        // resourceID is the name the table will have in GCP. (Kubernetes doesn't allow names with
        // underscores and BigQuery doesn't allow names with dashes.)
        resourceID: tableName,
        datasetRef: {
          external: datasetName,
        },
        view: {
          query: |||
            SELECT *, 'STAGING' AS env
            FROM `%(projectId)s.%(stagingDataset)s.%(tableName)s`

            UNION ALL

            SELECT *, 'PROD' AS env
            FROM `%(projectId)s.%(prodDataset)s.%(tableName)s`
          ||| % {
            projectId: cloudInfo.projectId,
            stagingDataset: stagingDatasetLib.datasetGcp,
            prodDataset: prodDatasetLib.datasetGcp,
            tableName: tableName,
          },
          useLegacySql: false,
        },
      },
    }
    for tableName in [def.name for def in schema]
  ];

  lib.flatten([
    dataset,
    views,
  ])

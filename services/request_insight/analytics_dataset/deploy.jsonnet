// Create the Request Insight analytics BigQuery dataset. See schema.jsonnet for the schema.
function(cloud, env, namespace, namespace_config)
  local lib = import 'deploy/common/lib.jsonnet';
  local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
  assert (cloudInfo.isDevCluster(cloud) || cloudInfo.isCentralNamespace(env, namespace, cloud));
  local engineers = import 'deploy/common/eng.jsonnet';
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';
  local datasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );
  local externalSALib = import 'services/request_insight/external_service_accounts_lib.jsonnet';
  local appName = 'request-insight-bigquery-analytics';

  // Create a cloud identity group (i.e., google group) for controlling access to the BigQuery
  // dataset. This is necessary because datasets don't support IAM. We don't want to grant new
  // tenants access to the dataset manually, so instead we grant access to a group which is
  // added to by RequestInsight deployments. For details on why we can't use IAM, see
  // https://cloud.google.com/config-connector/docs/reference/resource-docs/bigquery/bigquerydataset
  local cloudIdentityGroupName = datasetLib.cloudIdentityGroup;
  local cloudIdentityGroup = {
    apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
    kind: 'CloudIdentityGroup',
    metadata: {
      name: cloudIdentityGroupName,
      namespace: datasetLib.dataNamespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Request Insight CloudIdentityGroup for %s' % env,
      parent: 'customers/C02kn0kha',
      groupKey: {
        id: '%<EMAIL>' % cloudIdentityGroupName,
      },
      initialGroupConfig: 'WITH_INITIAL_OWNER',
      labels: {
        'cloudidentity.googleapis.com/groups.discussion_forum': '',
      },
    },
  };

  local readonlyCloudIdentityGroupName = datasetLib.readonlyCloudIdentityGroup;
  local readonlyCloudIdentityGroup = {
    apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
    kind: 'CloudIdentityGroup',
    metadata: {
      name: readonlyCloudIdentityGroupName,
      namespace: datasetLib.dataNamespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Request Insight readonly CloudIdentityGroup for %s' % env,
      parent: 'customers/C02kn0kha',
      groupKey: {
        id: '%<EMAIL>' % readonlyCloudIdentityGroupName,
      },
      initialGroupConfig: 'WITH_INITIAL_OWNER',
      labels: {
        'cloudidentity.googleapis.com/groups.discussion_forum': '',
      },
    },
  };

  local datasetName = datasetLib.dataset;
  local datasetResourceId = datasetLib.datasetGcp;

  // In staging/prod engineers have read-only access to prevent accidental deletions.
  local datasetEngAccess = [
    {
      role: if env == 'DEV' then 'OWNER' else 'READER',
      groupByEmail: '<EMAIL>',
    },
  ];
  // For simplicity service accounts have write access to the dataset. Technically they only need
  // metadata read access on the dataset and write access on the tables, but that significantly
  // complicates configuration and we should catch things like table deletions in code review.
  local datasetServiceAccess = [
    {
      role: 'WRITER',
      groupByEmail: '%<EMAIL>' % cloudIdentityGroupName,
    },
  ];
  local datasetReadonlyServiceAccess = [
    {
      role: 'READER',
      groupByEmail: '%<EMAIL>' % readonlyCloudIdentityGroupName,
    },
  ];
  // BigQuery datasets require an explicit owner, and configconnector needs admin privileges to be
  // able to run updates.
  local datasetAdminAccess = [
    {
      role: 'OWNER',
      userByEmail: '<EMAIL>',
    },
  ];
  // Give Hex access to read from the analytics dataset. The same service accounts (one with PII
  // access and one without) are shared across all clusters and environments.
  local externalServiceAccounts = externalSALib.getExternalServiceAccounts('PROD', 'GCP_US_CENTRAL1_PROD');
  local datasetExternalServiceAccess = [
    {
      role: 'READER',
      userByEmail: externalServiceAccounts.hexPIIAnalyticsSA.email,
    },
    {
      role: 'READER',
      userByEmail: externalServiceAccounts.hexNonPIIAnalyticsSA.email,
    },
    {
      role: 'READER',
      userByEmail: externalServiceAccounts.gtmDataformSA.email,
    },
  ];

  // The rules here are:
  // * Anyone with datacatalog.categoryFineGrainedReader can see all real data
  // * Anyone with bigquerydatapolicy.maskedReader can see masked data (hashes of the real values)
  // * Everyone else cannot query these columns at all (they get query errors)
  //
  // Do not deploy this in dev deployments, as that could also cause it to get
  // deleted by dev deployments. To deploy manually, you can always edit this
  // line
  //
  // Note(jacqueline): Confusingly the underlying resource for the PII policy tag says "user id".
  // This is a historical artifact. We originally thought we'd want different policies for different
  // types of PII, but that hasn't ended up being the case.
  //
  // TODO(jacqueline): These policies are shared across datasets so should be deployed separately
  //                   from the analytics dataset.
  local parentPIIPolicyTag = bigqueryLib.dataAccessPolicyTag(cloud, env, 'pii');
  local piiPolicyTag = bigqueryLib.dataAccessPolicyTag(cloud, env, 'userId');
  local piiReaderPolicies = if env == 'DEV' then [] else [
    // Everyone has masked reader access - we should do this for a tag rather
    // than a project, but that doesn't work for some reason, so as a hack we
    // have the masked reader policy on the project level, and the fine grained
    // reader policy on the tag level as allowed.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-%s-eng-pii-masked-reader-access' % [datasetLib.location, std.asciiLower(env)],
        namespace: 'devtools',
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'Project',
          external: 'projects/%s' % cloudInfo[cloud].projectId,
        },
        bindings: [
          {
            role: 'roles/bigquerydatapolicy.maskedReader',
            members: [
              { member: 'group:<EMAIL>' },
              { member: 'serviceAccount:%s' % externalServiceAccounts.hexNonPIIAnalyticsSA.email },
            ],
          },
        ],
      },
    },
    // Only people with 'full' piiAccess get finegrained access in prod.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPartialPolicy',
      metadata: {
        name: '%s-%s-eng-pii-finegrained-reader-access' % [datasetLib.location, std.asciiLower(env)],
        namespace: 'devtools',
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          // This policy is for this specific tag (and its children)
          kind: 'DataCatalogPolicyTag',
          external: parentPIIPolicyTag,
        },
        bindings: [
          {
            role: 'roles/datacatalog.categoryFineGrainedReader',
            members: std.filterMap(
              function(eng) env != 'PROD' || eng.piiAccess == 'full',
              function(eng)
                {
                  member: 'user:%<EMAIL>' % eng.username,
                },
              engineers
            ) + [
              { member: 'serviceAccount:%s' % externalServiceAccounts.hexPIIAnalyticsSA.email },
              { member: 'serviceAccount:%s' % externalServiceAccounts.gtmDataformSA.email },
              // ConfigConnector needs fine-grained read permissions to run BigQueryJob backfills
              // involving PII columns.
              { member: 'serviceAccount:<EMAIL>' },
            ],
          },
        ],
      },
    },
  ];

  local datasetAccess = lib.flatten([
    datasetEngAccess,
    datasetServiceAccess,
    datasetReadonlyServiceAccess,
    datasetAdminAccess,
    datasetExternalServiceAccess,
  ]);
  local dataset = bigqueryLib.createDataset(
    datasetName, datasetResourceId, datasetLib.location, datasetAccess, cloud, env, datasetLib.dataNamespace, appName
  );

  local tablesDefinitions = (import 'services/request_insight/analytics_dataset/schema.jsonnet')(
    datasetResourceId, piiPolicyTag
  );
  local tables = [
    bigqueryLib.createTable(def, datasetResourceId, datasetLib.dataNamespace, appName, 'request-insight-analytics')
    for def in tablesDefinitions
  ];

  lib.flatten([
    cloudIdentityGroup,
    readonlyCloudIdentityGroup,
    dataset,
    tables,
    piiReaderPolicies,
  ])

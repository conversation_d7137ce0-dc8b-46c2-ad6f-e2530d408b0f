{
  deployment: [
    {
      name: 'request-insight-analytics-dataset',
      priority: 100,  // We want tables to be created/modified before request-insight deploys.
      kubecfg: {
        target: '//services/request_insight/analytics_dataset:kubecfg',
        task: [
          // Staging BigQuery dataset in the US.
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },

          // Staging BigQuery dataset in the EU.
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },

          // Prod BigQuery dataset in the US.
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },

          // Prod BigQuery dataset in the EU.
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#team-insights',
        },
      },
    },

    // Cross-environment views are only deployed to US prod because they reference both prod and
    // staging tables. We don't bother deploying to the EU because EU staging is unused.
    {
      name: 'request-insight-analytics-cross-env-views',
      kubecfg: {
        target: '//services/request_insight/analytics_dataset:cross_env_views_kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#team-insights',
        },
      },
    },
  ],
}

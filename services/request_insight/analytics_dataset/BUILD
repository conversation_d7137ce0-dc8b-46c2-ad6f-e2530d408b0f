load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library", "jsonnet_to_json")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    deps = [
        ":dataset_lib",
        ":schema",
        "//deploy/common:eng-lib",
        "//deploy/common:lib",
        "//services/request_insight:external_service_accounts_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

kubecfg(
    name = "cross_env_views_kubecfg",
    src = "cross_env_views.jsonnet",
    deps = [
        ":dataset_lib",
        ":schema",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

jsonnet_library(
    name = "dataset_lib",
    srcs = [
        "dataset_lib.jsonnet",
    ],
    visibility = [
        "//services/misuse_monitor:__subpackages__",
        "//services/request_insight:__subpackages__",
        "//tools/deletion_utils:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

jsonnet_library(
    name = "schema",
    srcs = [
        "schema.jsonnet",
    ],
    visibility = ["//services/request_insight:__subpackages__"],
    deps = [
        "//services/request_insight/lib:bigquery_lib",
    ],
)

jsonnet_to_json(
    name = "schema_json",
    src = "schema.jsonnet",
    outs = ["schema.json"],
    visibility = ["//services/request_insight:__subpackages__"],
    deps = [
        ":schema",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":cross_env_views_kubecfg",
        ":kubecfg",
    ],
)

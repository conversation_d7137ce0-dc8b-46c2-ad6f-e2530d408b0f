{
  deployment: [
    {
      name: 'gtm-analytics',
      kubecfg: {
        target: '//services/request_insight/analytics_dataset/gtm_views:kubecfg',
        task: [
          // Staging BigQuery dataset in the US.
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },

          // Staging BigQuery dataset in the EU.
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },

          // Prod BigQuery dataset in the US.
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },

          // Prod BigQuery dataset in the EU.
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['cam'],
          slack_channel: '#team-growth',
        },
      },
    },
  ],
}

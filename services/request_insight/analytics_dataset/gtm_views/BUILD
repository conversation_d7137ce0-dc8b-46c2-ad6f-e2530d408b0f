load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

jsonnet_library(
    name = "dataset_lib",
    srcs = ["dataset_lib.jsonnet"],
    visibility = ["//visibility:public"],
    deps = [
        "//deploy/common:cloud_info",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "gtm_views.jsonnet",
    visibility = ["//visibility:public"],
    deps = [
        ":dataset_lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/common:cloud_info",
    ],
)

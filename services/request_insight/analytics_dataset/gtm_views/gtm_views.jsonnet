// Deploy views for GTM metrics in our own project.
// These views aggregate request metadata metrics for GTM analysis.
function(cloud, env, namespace, namespace_config)
  local appName = 'gtm-analytics';
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';
  local datasetLib = (import 'dataset_lib.jsonnet')(cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery);
  local analyticsDatasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );
  local location = datasetLib.location;

  // Use the dataset name from datasetLib
  local k8sDatasetName = datasetLib.dataset;
  local bqDatasetName = datasetLib.datasetGcp;

  local dataset = bigqueryLib.createDataset(
    k8sDatasetName,
    bqDatasetName,
    std.asciiUpper(location),
    [
      {
        role: 'OWNER',
        userByEmail: '<EMAIL>',
      },
      {
        role: 'READER',
        groupByEmail: '<EMAIL>',
      },
    ],
    cloud,
    env,
    namespace,
    appName
  );

  // Then create the view in the same dataset
  local metricsView = bigqueryLib.viewDefinition(
    'request_counts_60d',
    'Last 60 days of aggregated metrics for GTM analysis',
    |||
      WITH base_data AS (
        SELECT
          tenant_id,
          opaque_user_id,
          user_id_type,
          request_type as event_name,
          time,
          request_id
        FROM
          `%(projectId)s.%(sourceDatasetName)s.request_metadata`
        WHERE
          opaque_user_id IS NOT NULL
          AND time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 60 DAY)
      )
      SELECT
        tenant_id,
        opaque_user_id,
        user_id_type,
        event_name,
        time_granularity,
        CASE
          WHEN time_granularity = 'HOUR' THEN TIMESTAMP_TRUNC(time, HOUR)
          WHEN time_granularity = 'DAY' THEN TIMESTAMP_TRUNC(time, DAY)
          WHEN time_granularity = 'WEEK' THEN TIMESTAMP_TRUNC(time, WEEK)
        END AS timestamp,
        COUNT(DISTINCT request_id) as event_count
      FROM base_data
      CROSS JOIN UNNEST(['HOUR', 'DAY', 'WEEK']) AS time_granularity
      GROUP BY
        tenant_id,
        opaque_user_id,
        user_id_type,
        event_name,
        time_granularity,
        timestamp
    ||| % {
      projectId: cloudInfo.projectId,
      sourceDatasetName: analyticsDatasetLib.datasetGcp,
    }
  );

  local metricsTable = bigqueryLib.createTable(
    metricsView,
    bqDatasetName,
    namespace,
    appName,
    'gtm'
  );

  [
    dataset,
    metricsTable,
  ]

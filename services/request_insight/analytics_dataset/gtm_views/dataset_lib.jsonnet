// Contains information about the GTM metrics dataset that needs to be known by both
// the dataset deployment and users. This is only for GTM-specific information; anything
// shared across datasets is in bigquery_lib.jsonnet instead.
function(cloud, env, namespace, useSharedDevRequestInsightBigquery)
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];

  // Map each cloud to its BigQuery location
  local cloudToBigQueryLocation = {
    GCP_US_CENTRAL1_DEV: 'us',
    GCP_US_CENTRAL1_PROD: 'us',
    GCP_EU_WEST4_PROD: 'eu',
  };
  assert std.objectHas(cloudToBigQueryLocation, cloud);
  local location = cloudToBigQueryLocation[cloud];

  local namePrefix =
    if env == 'DEV' then
      namespace
    else
      '%s-%s' % [location, std.asciiLower(env)];

  // Dataset names for GTM metrics
  local dataset = '%s-gtm-metrics' % namePrefix;
  assert std.length(dataset) <= 64;

  {
    location: location,
    dataset: dataset,
    datasetGcp: std.strReplace(dataset, '-', '_'),
    namePrefix: namePrefix,
  }

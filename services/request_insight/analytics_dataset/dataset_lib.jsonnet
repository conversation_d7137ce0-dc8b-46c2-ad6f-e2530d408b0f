// Contains information about the Request Insight analytics dataset that needs to be known by both
// the dataset deployment and users. This is only for analytics-specific information; anything
// shared across datasets is in bigquery_lib.jsonnet instead.
function(cloud, env, namespace, useSharedDevRequestInsightBigquery)
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];

  // We write to separate datasets in the US and EU due to cost concerns copying data across
  // continents and because of unclear compliance differences.
  local cloudToBigQueryLocation = {
    GCP_US_CENTRAL1_DEV: 'us',
    GCP_US_CENTRAL1_PROD: 'us',
    GCP_EU_WEST4_PROD: 'eu',
  };
  assert std.objectHas(cloudToBigQueryLocation, cloud);
  local location = cloudToBigQueryLocation[cloud];

  local namePrefix =
    if env == 'DEV' then
      namespace
    else
      '%s-%s' % [location, std.asciiLower(env)];

  // Group names need to be under 64 characters, so we abbreviate "request-insight" to "ri".
  local cloudIdentityGroup = '%s-ri-analytics-dataset-access-group' % namePrefix;
  assert std.length(cloudIdentityGroup) <= 64;
  local readonlyCloudIdentityGroup = '%s-ri-analytics-dataset-readonly-access-group' % namePrefix;
  assert std.length(readonlyCloudIdentityGroup) <= 64;
  local dataset = '%s-request-insight-analytics-dataset' % namePrefix;
  assert std.length(dataset) <= 64;

  // Namespace in the metadata of cloud identity groups and datasets.
  local dataNamespace =
    if env == 'DEV' then
      namespace
    else
      if env == 'STAGING' then 'central-staging'
      else 'central';

  {
    location: location,
    cloudIdentityGroup: cloudIdentityGroup,
    readonlyCloudIdentityGroup: readonlyCloudIdentityGroup,
    dataset: dataset,
    datasetGcp: std.strReplace(dataset, '-', '_'),
    dataNamespace: dataNamespace,
    namePrefix: namePrefix,
  }

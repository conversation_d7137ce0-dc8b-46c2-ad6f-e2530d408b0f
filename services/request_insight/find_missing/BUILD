load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "config",
    srcs = [
        "config.py",
    ],
    deps = [
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        requirement("dataclasses_json"),
    ],
)

py_binary(
    name = "find_missing",
    srcs = ["find_missing.py"],
    deps = [
        ":config",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//base/python/grpc:client_options",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/content_manager/client",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/lib:request_insight_subscriber",
        "//services/token_exchange/client:client_py",
        requirement("cachetools"),
        requirement("dataclasses_json"),
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "find_missing_test",
    srcs = ["find_missing_test.py"],
    deps = [
        ":find_missing",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":find_missing",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

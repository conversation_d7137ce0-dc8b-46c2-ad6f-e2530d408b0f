"""Module for the configuration for the request insight find missing."""

import pathlib
from dataclasses import dataclass
from typing import Op<PERSON>

from dataclasses_json import dataclass_json

import services.lib.grpc.tls_config.tls_config as tls_config


@dataclass_json
@dataclass
class Config:
    """Configuration for the request insight find missing subscriber."""

    project_id: str
    """The project id of the pub/sub topic/subscription."""

    topic_name: str
    """The name of the pub/sub topic to listen to."""

    subscription_name: str
    """The name of the subscription to register, as it exists in GCP."""

    subscription_description: str
    """A description of the subscription, shared across tenants. Used for metrics."""

    subscription_batch_size: int
    """The batch size to use for fetching subscription messages."""

    content_manager_endpoint: str
    """Endpoint of the content manager, e.g. localhost:50051."""

    token_exchange_endpoint: str

    blob_name_cache_ttl_s: int
    """The time to live for cache entries."""

    blob_name_cache_size: int
    """The size of the blob name cache in number of blob names"""

    checkpoint_cache_size: int
    """The size of the checkpoint cache."""

    find_missing_call_batch_size: int
    """The batch size to use for fetching from content manager."""

    # MTLS configuration for central GRPC client
    central_client_mtls: Optional[tls_config.ClientConfig] = None

    # MTLS configuration for GRPC client
    client_mtls: Optional[tls_config.ClientConfig] = None

    # Feature flags configuration
    feature_flags_sdk_key_path: Optional[str] = None
    """Path to the feature flags SDK key file."""

    dynamic_feature_flags_endpoint: Optional[str] = None
    """Endpoint for dynamic feature flags service."""

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )

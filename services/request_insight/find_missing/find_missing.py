"""Subscriber for calling find missing on request insight events ."""

import argparse
import itertools
import logging
import os
import pathlib
import typing
from dataclasses import dataclass
from functools import partial
from typing import Iterable

import cachetools
import grpc
import pydantic
import structlog
from prometheus_client import Counter, Histogram, start_http_server

from base import feature_flags

import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.blob_names import blob_names_pb2
from base.blob_names.python import blob_names
from base.logging.struct_logging import setup_struct_logging
from base.python.grpc import client_options
from base.python.signal_handler.signal_handler import StandardSignalHandler
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.lib.request_context.request_context import (
    RequestContext,
    create_request_session_id,
)
from services.request_insight import request_insight_pb2
from services.request_insight.find_missing.config import Config
from services.request_insight.lib.request_insight_subscriber import (
    RequestInsightSubscriber,
)
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)

log = structlog.get_logger()


tracer = base.tracing.setup_opentelemetry()


# Feature flag to control whether we process messages or just drop them
_PROCESSING_ENABLED = feature_flags.BoolFlag(
    "request_insight_find_missing_enabled", True
)


find_missing_call_counter = Counter(
    "au_request_insight_find_missing_find_missing_call_count",
    "Number of calls to find_missing by the find_missing subscriber ",
)

find_missing_blob_count_histogram = Histogram(
    "au_request_insight_find_missing_find_missing_blob_count",
    "Number of blobs found by the find_missing subscriber",
    buckets=[1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192],
)


blob_name_cache_counter = Counter(
    "au_request_insight_find_missing_blob_name_cache_count",
    "Number of blob names found in the cache",
    labelnames=["status"],  # values: missing | present
)

checkpoint_cache_counter = Counter(
    "au_request_insight_find_missing_checkpoint_cache_count",
    "Number of checkpoint names found in the cache",
    labelnames=["status"],  # values: missing | present
)


T = typing.TypeVar("T")


def _chunked_iterable(
    iterable: Iterable[T], size: int
) -> typing.Generator[list[T], None, None]:
    it = iter(iterable)
    while True:
        chunk = list(itertools.islice(it, size))
        if not chunk:
            break
        yield chunk


@dataclass(frozen=True)
class BlobEntry:
    blob_name: bytes
    tenant_id: str


class FindMissingClient:
    """Implements the find missing logic.

    For each request insight message, we extract all blob names from the message and check if they are present in the cache.
    If not, we add them to the cache and call find missing on them.

    The goal is to ensure that all catchup notifications are triggered for all actively used blobs.

    The cache is used to ensure that we do not call find missing on blobs that were already checked.
    The cache is TTL based to ensure that we will call find missing regularly to ensure that catchup notifications are triggered.
    The cache is also size based to ensure that we do not use too much memory.
    """

    def __init__(
        self,
        content_manager_stub: ContentManagerClient,
        token_exchange_client: TokenExchangeClient,
        blob_name_cache: cachetools.Cache,
        checkpoint_cache: cachetools.Cache,
        batch_size: int = 1000,
    ):
        self.content_manager_stub = content_manager_stub
        self.token_exchange_client = token_exchange_client
        # cache from blob entry (aka blob name|tenant id) to boolean
        self.blob_name_cache: cachetools.Cache[BlobEntry, bool] = blob_name_cache
        # cache from (tenant_id, checkpoint_id) to blob entries
        self.checkpoint_cache: cachetools.Cache[
            tuple[str, str], Iterable[BlobEntry]
        ] = checkpoint_cache
        self.batch_size = batch_size
        self.request_session_id = create_request_session_id()
        self.auth_token: pydantic.SecretStr | None = None

    def _extract_blob_names(
        self,
        blob_names: Iterable[BlobEntry],
        blobs: blob_names_pb2.Blobs,
        tenant_id: str,
    ) -> set[BlobEntry]:
        result = set(blob_names)
        if blobs.baseline_checkpoint_id:
            checkpoint_id = blobs.baseline_checkpoint_id
            if (tenant_id, checkpoint_id) not in self.checkpoint_cache:
                checkpoint_cache_counter.labels("missing").inc()
                checkpoint_blob_names = (
                    self.content_manager_stub.get_all_blobs_from_checkpoint(
                        checkpoint_id,
                        tenant_id=tenant_id,
                        request_context=self._get_request_context(),
                    )
                )
                if checkpoint_blob_names:
                    checkpoint_blob_names_hex = [
                        BlobEntry(b, tenant_id) for b in checkpoint_blob_names
                    ]
                    self.checkpoint_cache[(tenant_id, checkpoint_id)] = (
                        checkpoint_blob_names_hex
                    )
                    result |= set(checkpoint_blob_names_hex)
            else:
                checkpoint_cache_counter.labels("present").inc()
                result |= set(self.checkpoint_cache[(tenant_id, checkpoint_id)])
        result |= set(BlobEntry(blob_name=b, tenant_id=tenant_id) for b in blobs.added)
        # we leave the deleted blobs in the set as other messages may still reference them
        return result

    def gather_blob_entries(
        self, messages: Iterable[request_insight_pb2.RequestInsightMessage]
    ) -> set[BlobEntry]:
        """Gather blob names from request insight messages."""
        blob_entries = set()
        for message in messages:
            if message.WhichOneof("message") == "update_request_info_request":
                tenant_id = message.update_request_info_request.tenant_info.tenant_id
                if not tenant_id:
                    log.error(
                        "Received request %s without tenant_id",
                        message.update_request_info_request.request_id,
                    )
                    continue
                for event in message.update_request_info_request.events:
                    if event.WhichOneof("event") == "completion_host_request":
                        blob_entries |= self._extract_blob_names(
                            [
                                BlobEntry(blob_names.encode_blob_name(b), tenant_id)
                                for b in event.completion_host_request.blob_names
                            ],
                            blobs=event.completion_host_request.blobs,
                            tenant_id=tenant_id,
                        )
                    elif event.WhichOneof("event") == "chat_host_request":
                        for blobs in event.chat_host_request.request.blobs:
                            blob_entries |= self._extract_blob_names(
                                [BlobEntry(b, tenant_id) for b in blobs.added],
                                blobs=blobs,
                                tenant_id=tenant_id,
                            )
        return blob_entries

    def _get_request_context(self) -> RequestContext:
        """Get the request context.

        If this returns an auth token, it will be used to authenticate the request to the content manager.
        The token will be valid for a short period of time.
        """
        if self.auth_token is not None:
            return RequestContext.create(
                request_source="background", auth_token=self.auth_token
            )
        logging.info("Getting service token")
        service_token = self.token_exchange_client.get_signed_token_for_service(
            tenant_id="", scopes=[token_exchange_pb2.CONTENT_RW]
        )
        request_context = RequestContext.create(
            request_source="background",
            auth_token=service_token,
        )
        return request_context

    def find_missing(
        self,
        messages: Iterable[request_insight_pb2.RequestInsightMessage],
    ):
        """Find missing memories."""
        try:
            blob_entries = self.gather_blob_entries(messages)
            log.info("Found %d blob in current batch", len(blob_entries))
            if not blob_entries:
                return
            # map from tenant i to list of blobs entries for that tenant
            lookup_list: dict[str, set[bytes]] = {}
            for blob_entry in blob_entries:
                if blob_entry not in self.blob_name_cache:
                    if blob_entry.tenant_id not in lookup_list:
                        lookup_list[blob_entry.tenant_id] = set()
                    lookup_list[blob_entry.tenant_id].add(blob_entry.blob_name)
                    blob_name_cache_counter.labels("missing").inc()
                else:
                    blob_name_cache_counter.labels("present").inc()
            if lookup_list:
                for tenant_id, lookup_blob_names in lookup_list.items():
                    assert tenant_id
                    log.info(
                        "Issue find_missing for %d blob names for tenant %s.",
                        len(lookup_blob_names),
                        tenant_id,
                    )
                    find_missing_blob_count_histogram.observe(len(lookup_blob_names))
                    for lookup_list_chunk in _chunked_iterable(
                        lookup_blob_names, self.batch_size
                    ):
                        # we do not actually care about the result
                        result = self.content_manager_stub.find_missing(
                            list(b.hex() for b in lookup_list_chunk),
                            request_context=self._get_request_context(),
                            tenant_id=tenant_id,
                        )
                        log.info(
                            "Issue find_missing for %d blob names: unknown %s.",
                            len(lookup_list_chunk),
                            len(result),
                        )
                        find_missing_call_counter.inc()
                        self.blob_name_cache.update(
                            {
                                BlobEntry(blob_name, tenant_id): True
                                for blob_name in lookup_list_chunk
                            }
                        )
            else:
                find_missing_blob_count_histogram.observe(0)
                log.info("No unchecked blob names found.")
        except grpc.RpcError as ex:
            if ex.code() == grpc.StatusCode.UNAUTHENTICATED:  # pylint: disable=no-member # type: ignore
                self.auth_token = None
            raise


def process_batch(
    message_batch: Iterable[request_insight_pb2.RequestInsightMessage],
    find_missing_client: FindMissingClient,
):
    """Process a single batch of request insight messages."""
    with tracer.start_span("process_batch"):
        # Check if processing is enabled via feature flag
        context = feature_flags.get_global_context()
        if not _PROCESSING_ENABLED.get(context):
            log.debug(
                "Feature flag request_insight_find_missing_enabled is disabled. Skipping processing."
            )
            return

        find_missing_client.find_missing(message_batch)


def get_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


def main():
    _ = StandardSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--health-file",
        type=pathlib.Path,
        help="Path to a file to write a health check to",
    )
    args = parser.parse_args()
    log.info("Args %s", args)

    config = Config.load_config(args.config_file)
    log.info("Config: %s", config)

    start_http_server(9090)

    # Initialize feature flags
    path = None
    custom_endpoint = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = feature_flags.Context.setup(path, custom_endpoint)
    feature_flags.set_global_context(context)

    content_manager_stub = get_content_manager_client(config)

    token_exchange_stub = GrpcTokenExchangeClient.create(
        config.token_exchange_endpoint,
        os.environ["POD_NAMESPACE"],
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )

    blob_name_cache = cachetools.TTLCache(
        maxsize=config.blob_name_cache_size, ttl=config.blob_name_cache_ttl_s
    )
    checkpoint_cache = cachetools.LRUCache(maxsize=config.checkpoint_cache_size)

    find_missing_client = FindMissingClient(
        content_manager_stub=content_manager_stub,
        token_exchange_client=token_exchange_stub,
        blob_name_cache=blob_name_cache,
        checkpoint_cache=checkpoint_cache,
        batch_size=config.find_missing_call_batch_size,
    )

    subscriber = RequestInsightSubscriber(
        config.project_id,
        config.topic_name,
        config.subscription_name,
        config.subscription_description,
        config.subscription_batch_size,
        health_file=args.health_file,
    )
    subscriber.run(
        partial(
            process_batch,
            find_missing_client=find_missing_client,
        )
    )


if __name__ == "__main__":
    main()

import logging
import uuid
from typing import Sequence
from unittest.mock import Magic<PERSON>ock

import cachetools
import pytest
import pydantic

from base.blob_names import blob_names_pb2
from services.lib.request_context.request_context import RequestContext
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.request_insight import request_insight_pb2
from services.request_insight.find_missing.find_missing import (
    FindMissingClient,
    BlobEntry,
)

logging.basicConfig(level=logging.INFO)


@pytest.fixture()
def content_manager_client() -> ContentManagerClient:
    """Return a mock ContentManagerClient."""
    mock_data = {f"blob-{k}": k.encode() for k in "abcde"}
    mock_data["error"] = b"error"
    calls = []

    def find_missing(
        blob_names: Sequence[str], request_context: RequestContext, tenant_id: str
    ) -> list[str]:
        del request_context
        assert tenant_id == "123"
        calls.append(blob_names)
        return []

    client = MagicMock(ContentManagerClient)
    client.find_missing.side_effect = find_missing
    return client


@pytest.fixture()
def token_exchange_client():
    """Return a mock TokenExchangeClient."""
    client = MagicMock()
    client.get_signed_token_for_service.return_value = pydantic.SecretStr("token")
    return client


def make_fake_request_update(
    blob_names: Sequence[str],
    num_events: int,
) -> request_insight_pb2.RequestInsightMessage:
    """Create a fake request update with completion_host_request."""
    request = request_insight_pb2.UpdateRequestInfoRequest(
        request_id=str(uuid.uuid4()),
        tenant_info=request_insight_pb2.TenantInfo(tenant_id="123"),
        events=[request_insight_pb2.RequestEvent() for _ in range(num_events)],
    )
    for i, event in enumerate(request.events):
        # Partition the blobs into the different events.
        event.completion_host_request.blob_names.extend(blob_names[i::num_events])
    return request_insight_pb2.RequestInsightMessage(
        update_request_info_request=request
    )


def make_fake_chat_request_update(
    blob_names_list: list[list[bytes]],
) -> request_insight_pb2.RequestInsightMessage:
    """Create a fake request update with chat_host_request."""
    request = request_insight_pb2.UpdateRequestInfoRequest(
        request_id=str(uuid.uuid4()),
        tenant_info=request_insight_pb2.TenantInfo(tenant_id="123"),
        events=[request_insight_pb2.RequestEvent()],
    )
    chat_request = request.events[0].chat_host_request.request
    for blob_names in blob_names_list:
        blobs = chat_request.blobs.add()
        blobs.added.extend(blob_names)
    return request_insight_pb2.RequestInsightMessage(
        update_request_info_request=request
    )


def test_simple(content_manager_client, token_exchange_client):
    blob_name_cache = cachetools.Cache(maxsize=10)
    checkpoint_cache = cachetools.Cache(maxsize=10)
    client = FindMissingClient(
        content_manager_client,
        token_exchange_client,
        blob_name_cache,
        checkpoint_cache,
        batch_size=1000,
    )
    client.find_missing([make_fake_request_update(["aa", "ba"], 1)])
    assert content_manager_client.find_missing.call_count == 1
    assert set(content_manager_client.find_missing.call_args[0][0]) == set(["aa", "ba"])

    client.find_missing([make_fake_request_update(["aa", "ba"], 1)])
    assert content_manager_client.find_missing.call_count == 1


def test_chat_host_request(content_manager_client, token_exchange_client):
    """Test that chat_host_request events are processed correctly."""
    blob_name_cache = cachetools.Cache(maxsize=10)
    checkpoint_cache = cachetools.Cache(maxsize=10)
    client = FindMissingClient(
        content_manager_client,
        token_exchange_client,
        blob_name_cache,
        checkpoint_cache,
        batch_size=1000,
    )
    blob1 = b"\xaa\xbb\xcc"
    blob2 = b"\xdd\xee\xff"
    blob3 = b"\x11\x22\x33"
    chat_request = make_fake_chat_request_update([[blob1, blob2], [blob3]])
    client.find_missing([chat_request])
    assert content_manager_client.find_missing.call_count == 1
    expected_blob_names = set([blob1.hex(), blob2.hex(), blob3.hex()])
    actual_blob_names = set(content_manager_client.find_missing.call_args[0][0])
    assert actual_blob_names == expected_blob_names
    # Process the same request again - should not call find_missing again
    # because the blobs are now in the cache
    client.find_missing([chat_request])
    assert content_manager_client.find_missing.call_count == 1

// K8S deployment file for the request insight find missing subscriber
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
function(env, namespace, cloud, namespace_config)
  local projectId = cloudInfo[cloud].projectId;

  local appName = 'request-insight-find-missing';
  local shortAppName = 'ri-find-missing';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace)
  );

  // Set up dynamic feature flags
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  local tokenExchangeEndpoint = endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace);
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local config = {
    project_id: projectId,
    topic_name: '%s-request-insight-topic' % namespace,
    subscription_name: '%s-request-insight-find-missing-sub' % namespace,
    subscription_description: 'ri-find-missing',
    subscription_batch_size: 30,
    content_manager_endpoint: 'content-manager-svc:50051',
    client_mtls: if mtls then clientCert.config else null,
    // re-check a blob name every hour
    blob_name_cache_ttl_s: if env == 'DEV' then 60 else 60 * 60,
    // 1M blobs should cover a long time, we can increase this if we need to.
    blob_name_cache_size: 1024 * 1024,
    checkpoint_cache_size: 1024,
    find_missing_call_batch_size: 1024,
    central_client_mtls: if mtls then centralClientCert.config else null,
    token_exchange_endpoint: tokenExchangeEndpoint,
    // Feature flags configuration
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
  };

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local gcpObjects = [
    // Pub/sub subscription.
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: config.subscription_name,
        namespace: namespace,
        // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
        // topic is deleted before the pod.
        annotations: if env == 'DEV' then {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        } else {},
        labels: {
          app: appName,
        },
      },
      spec: {
        topicRef: {
          name: config.topic_name,
        },
        ackDeadlineSeconds: 60,
        retryPolicy: {
          minimumBackoff: '5s',
          maximumBackoff: '300s',
        },
        // Retain messages for 1 hour in dev and 7 days in staging/prod.
        messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
        retainAckedMessages: false,
      },
    },
    // Give access to the subscription.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'ri-find-missing-subscription-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: config.subscription_name,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress,
            ],
          },
        ],
      },
    },
  ];

  local container = {
    name: 'request-insight-find-missing',
    target: {
      name: '//services/request_insight/find_missing:image',
      dst: 'request_insight_find_missing',
    },
    env: lib.flatten([
      telemetryLib.telemetryEnv('request-insight-find-missing', telemetryLib.collectorUri(env, namespace, cloud)),
      dynamicFeatureFlags.env,
    ]),
    volumeMounts: [
      configMap.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 0.5,
        memory: '4Gi',
      },
    },
    args: [
      '--health-file=/tmp/health',
    ],
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat /tmp/health',
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat /tmp/health',
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 20,
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local pod = {
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  // we allow multiple find missing processors on the same host as they run in the background
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local minReplicas = {
    DEV: 1,
    STAGING: 2,
    PROD: 2,
  };
  local maxReplicas = {
    DEV: 2,
    STAGING: 8,
    PROD: 16,
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'request-insight-find-missing',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: 0,
      replicas: minReplicas[env],
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  // Scale when the subscription backlog gets too large.
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: '%s-scaledobject' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      minReplicaCount: minReplicas[env],
      maxReplicaCount: maxReplicas[env],
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            metricName: 'pubsub_googleapis_com:subscription_num_undelivered_messages',
            threshold: '500',
            query: 'sum(avg_over_time(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id="%s"}[1m]))' % config.subscription_name,
          },
        },
      ],
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    deployment,
    scaledObject,
    clientCert.objects,
    centralClientCert.objects,
    dynamicFeatureFlags.k8s_objects,
    gcpObjects,
  ])

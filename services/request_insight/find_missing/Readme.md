# Request Insight Find Missing Subscriber

The find missing subscriber is used to call content manager's find missing RPC on all
actively used blobs.

The goal is to ensure that all catchup notifications are triggered for all actively used blobs.
The process piggybacks on request insight as it knows about all actively used blobs and allows
easy processing in the background (out side of the critical path).

The subscriber uses a cache of all blobs that find missing was called on to ensure that it does not call find missing on blobs that were already checked.
The cache is TTL based to ensure that we will call find missing regularly to ensure that catchup notifications are triggered.

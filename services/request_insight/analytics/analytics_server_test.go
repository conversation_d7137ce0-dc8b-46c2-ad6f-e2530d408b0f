package main

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/civil"
	testutils "github.com/augmentcode/augment/base/test_utils"
	bqemulator "github.com/augmentcode/augment/base/test_utils/bigquery/emulator"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	pb "github.com/augmentcode/augment/services/request_insight/analytics/proto"
	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	projectId        = "test-project"
	datasetName      = "test_dataset"
	noDataTenantID   = "123"
	noDataTenantName = "nodata"
	userId           = "<EMAIL>"
)

// Shared test fixtures. These are initialized in TestMain.
var (
	bqEmulator *bqemulator.BigQueryEmulator = nil
)

// TestMain is the entry point for the test suite. This is where shared test fixtures should be
// set up.
func TestMain(m *testing.M) {
	// Set up a shared emulator for all the tests.
	var err error
	bqEmulator, err = bqemulator.New(context.Background(), projectId, datasetName)
	if err != nil {
		panic(err)
	}
	defer bqEmulator.Close()

	err = bqEmulator.LoadJsonSchemaFile(context.Background(), "../analytics_dataset/schema.json")
	if err != nil {
		panic(err)
	}

	// Run the tests.
	os.Exit(m.Run())
}

func testServer(t *testing.T) *analyticsServer {
	if bqEmulator == nil {
		t.Fatal("BigQuery emulator is not set up")
	}

	return &analyticsServer{
		bqClient:    bqEmulator.Client,
		datasetName: datasetName,
		authEnabled: true,
	}
}

func noDataAuthorizedContext() context.Context {
	return authorizedContext(noDataTenantID, noDataTenantName, userId)
}

func authorizedContext(tenantID string, tenantName string, userId string) context.Context {
	claims := &auth.AugmentClaims{
		TenantID:   tenantID,
		TenantName: tenantName,
		UserID:     userId,
		Scope:      []string{"CONTENT_RW"},
	}
	return claims.NewContext(context.Background())
}

func badDateFilters() []*pb.DateFilters {
	return []*pb.DateFilters{
		// Missing end date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
		},
		// Missing start date.
		{
			EndDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
		},
		// Start date after end date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
			EndDate:   &pb.Date{Year: 2023, Month: 1, Day: 1},
		},
		// Invalid start date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 0},
			EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
		},
		// Invalid end date.
		{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
			EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 0},
		},
	}
}

// Run basic auth tests that apply to all endpoints.
func runAuthTests[ReqT proto.Message, RespT proto.Message](
	t *testing.T, goodRequest ReqT, requestFunc func(context.Context, ReqT) (RespT, error),
) {
	t.Run("auth validation", func(t *testing.T) {
		// Test missing auth claims.
		_, err := requestFunc(context.Background(), goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for missing auth claims")
		}

		// Test mismatch between auth claims and request.
		wrongTenantClaims := &auth.AugmentClaims{
			TenantID:   "456",
			TenantName: "another-tenant",
			UserID:     "234",
			Scope:      []string{"CONTENT_RW"},
		}
		badCtx := wrongTenantClaims.NewContext(noDataAuthorizedContext())
		_, err = requestFunc(badCtx, goodRequest)
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Errorf("Expected error for wrong tenant ID in claims")
		}
	})
}

func TestCheckDatasetName(t *testing.T) {
	goodNames := []string{
		"prod_request_insight_dataset",
	}
	for _, name := range goodNames {
		if !checkDatasetName(name) {
			t.Errorf("Dataset name should be valid: %s", name)
		}
	}

	badNames := []string{
		"prod-request-insight-dataset",
		"prod_request_insight_dataset;",
		"; drop table x",
	}
	for _, name := range badNames {
		if checkDatasetName(name) {
			t.Errorf("Dataset name should be valid: %s", name)
		}
	}
}

func TestGetDevDays(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetDevDaysRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetDevDaysRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetDevDays(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetDevDaysRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetDevDays,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetDevDaysRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetDevDaysResponse{
						DevDays: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 0,
							},
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
								Count: 0,
							},
						},
					},
				},
			},
			server.GetDevDays,
		)
	})
}

func TestGetActiveUsers(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetActiveUsersRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
			// Invalid request type.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
				RequestTypes: []pb.GetActiveUsersRequest_RequestType{
					pb.GetActiveUsersRequest_UNKNOWN,
				},
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetActiveUsersRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetActiveUsers(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetActiveUsersRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetActiveUsers,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 0,
							},
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
								Count: 0,
							},
						},
					},
				},
			},
			server.GetActiveUsers,
		)
	})

	t.Run("request type filter", func(t *testing.T) {
		tenantID := uuid.New().String()
		ctx := authorizedContext(tenantID, "test-tenant", userId)

		// Insert rows for a completion request and an edit request from different users on the
		// same day.
		query := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
			    request_id, tenant, tenant_id, shard_namespace, user_id, session_id, user_agent,
				time, request_type
			)
			VALUES
				(
			        "request1", "test-tenant", @tenant_id, "test-namespace", "user1",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "COMPLETION"
				),
			    (
				    "request2", "test-tenant", @tenant_id, "test-namespace", "user2",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "EDIT"
				)
		`, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		dateFilter := &pb.DateFilters{
			StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
			EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// There was one completion user.
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId:    tenantID,
						DateFilters: dateFilter,
						RequestTypes: []pb.GetActiveUsersRequest_RequestType{
							pb.GetActiveUsersRequest_COMPLETION,
						},
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 1,
							},
						},
					},
				},
				// There were no chat users.
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId:    tenantID,
						DateFilters: dateFilter,
						RequestTypes: []pb.GetActiveUsersRequest_RequestType{
							pb.GetActiveUsersRequest_CHAT,
						},
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 0,
							},
						},
					},
				},
				// There were two users of any request type.
				{
					Request: &pb.GetActiveUsersRequest{
						TenantId:    tenantID,
						DateFilters: dateFilter,
					},
					Response: &pb.GetActiveUsersResponse{
						ActiveUsers: []*pb.DateAndCount{
							{
								Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
								Count: 2,
							},
						},
					},
				},
			},
			server.GetActiveUsers,
		)
	})
}

func TestGetCompletionStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetCompletionStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetCompletionStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetCompletionStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetCompletionStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetCompletionStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetCompletionStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetCompletionStatsResponse{
						CompletionStats: []*pb.GetCompletionStatsResponse_CompletionStats{
							{
								Aggregation: &pb.GetCompletionStatsResponse_CompletionStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 1},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
							{
								Aggregation: &pb.GetCompletionStatsResponse_CompletionStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 2},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
						},
					},
				},
			},
			server.GetCompletionStats,
		)
	})
}

func TestGetEditStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetEditStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetEditStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetEditStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetEditStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetEditStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetEditStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetEditStatsResponse{
						EditStats: []*pb.GetEditStatsResponse_EditStats{
							{
								Aggregation: &pb.GetEditStatsResponse_EditStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 1},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
							{
								Aggregation: &pb.GetEditStatsResponse_EditStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 2},
								},
								RequestCount:           0,
								AcceptedCount:          0,
								AcceptedLineCount:      0,
								AcceptedCharacterCount: 0,
							},
						},
					},
				},
			},
			server.GetEditStats,
		)
	})
}

func TestGetChatStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetChatStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetChatStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetChatStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetChatStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetChatStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetChatStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetChatStatsResponse{
						ChatStats: []*pb.GetChatStatsResponse_ChatStats{
							{
								Aggregation: &pb.GetChatStatsResponse_ChatStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 1},
								},
								RequestCount: 0,
							},
							{
								Aggregation: &pb.GetChatStatsResponse_ChatStats_Date{
									Date: &pb.Date{Year: 2024, Month: 1, Day: 2},
								},
								RequestCount: 0,
							},
						},
					},
				},
			},
			server.GetChatStats,
		)
	})
}

func TestGetKeywordsStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetKeywordsStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
				RequestType: pb.GetKeywordsStatsRequest_EDIT,
			},
			// Missing date filters.
			{
				TenantId:    noDataTenantID,
				RequestType: pb.GetKeywordsStatsRequest_EDIT,
			},
			// Missing request type.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetKeywordsStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
				RequestType: pb.GetKeywordsStatsRequest_EDIT,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetKeywordsStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetKeywordsStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
			RequestType: pb.GetKeywordsStatsRequest_EDIT,
		},
		server.GetKeywordsStats,
	)
}

func TestGetCategoriesStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetCategoriesStatsRequest{
			// Missing tenant id.
			{
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
				RequestType: pb.GetCategoriesStatsRequest_EDIT,
			},
			// Missing date filters.
			{
				TenantId:    noDataTenantID,
				RequestType: pb.GetCategoriesStatsRequest_EDIT,
			},
			// Missing request type.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetCategoriesStatsRequest{
				TenantId:    noDataTenantID,
				DateFilters: dateFilter,
				RequestType: pb.GetCategoriesStatsRequest_EDIT,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetCategoriesStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetCategoriesStatsRequest{
			TenantId: noDataTenantID,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
			RequestType: pb.GetCategoriesStatsRequest_EDIT,
		},
		server.GetCategoriesStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: noDataTenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
						RequestType: pb.GetCategoriesStatsRequest_CHAT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{},
					},
				},
			},
			server.GetCategoriesStats,
		)
	})

	t.Run("chat data", func(t *testing.T) {
		tenantID := uuid.New().String()
		ctx := authorizedContext(tenantID, "test-tenant", userId)

		// Insert rows for sample chat requests
		query := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.chat_host_request (
                request_id, tenant, tenant_id, shard_namespace, time, sanitized_json,
                message_categories
			)
			VALUES
				(
                    "request1", "test-tenant", @tenant_id, "test-namespace",
                    "2024-01-01 00:00:00", JSON '{}', ['test']
                ),
				(
                    "request2", "test-tenant", @tenant_id, "test-namespace",
                    "2024-01-02 00:00:00", JSON '{}', ['test']
                )
        `, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}

		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for aggregated chat categories
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
						RequestType: pb.GetCategoriesStatsRequest_CHAT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{
							{
								Category: "test",
								Count:    2,
							},
						},
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
						RequestType: pb.GetCategoriesStatsRequest_CHAT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{
							{
								Category: "test",
								Count:    1,
							},
						},
					},
				},
				// Request for edit categories
				{
					Request: &pb.GetCategoriesStatsRequest{
						TenantId: tenantID,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
						RequestType: pb.GetCategoriesStatsRequest_EDIT,
					},
					Response: &pb.GetCategoriesStatsResponse{
						CategoriesStats: []*pb.GetCategoriesStatsResponse_CategoryStats{},
					},
				},
			},
			server.GetCategoriesStats,
		)
	})
}

func TestGetEarliestRequestTimestamp(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		// Missing tenant id.
		badRequest := &pb.GetEarliestRequestTimestampRequest{}
		_, err := server.GetEarliestRequestTimestamp(noDataAuthorizedContext(), badRequest)
		if err == nil || status.Code(err) != codes.InvalidArgument {
			t.Errorf("Expected error for request: %v", badRequest)
		}
	})

	runAuthTests(
		t,
		&pb.GetEarliestRequestTimestampRequest{
			TenantId: noDataTenantID,
		},
		server.GetEarliestRequestTimestamp,
	)
}

func TestGetUserLastRequestTimestamp(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserLastRequestTimestampRequest{
			// Missing user id
			{
				TenantId: "test-tenant",
				RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
					pb.GetUserLastRequestTimestampRequest_COMPLETION,
				},
				MinSearchTimestamp: timestamppb.Now(),
			},
			// Missing tenant id
			{
				UserId: "test-user",
				RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
					pb.GetUserLastRequestTimestampRequest_COMPLETION,
				},
				MinSearchTimestamp: timestamppb.Now(),
			},
			// Missing min_search_timestamp
			{
				UserId:   "test-user",
				TenantId: "test-tenant",
				RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
					pb.GetUserLastRequestTimestampRequest_COMPLETION,
				},
			},
		}

		for _, req := range badRequests {
			_, err := server.GetUserLastRequestTimestamp(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected InvalidArgument error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserLastRequestTimestampRequest{
			UserId:   "test-user",
			TenantId: noDataTenantID,
			RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
				pb.GetUserLastRequestTimestampRequest_COMPLETION,
			},
			MinSearchTimestamp: timestamppb.Now(),
		},
		server.GetUserLastRequestTimestamp,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: noDataTenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
						},
						MinSearchTimestamp: timestamppb.Now(),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: nil,
					},
				},
			},
			server.GetUserLastRequestTimestamp,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		userId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", userId)

		// Insert sample data
		query := server.bqClient.Query(fmt.Sprintf(`
            INSERT INTO %s.request_metadata (
                request_id, tenant, tenant_id, shard_namespace, user_id, session_id, user_agent,
                time, request_type
            )
            VALUES
                (
                    "request1", "test-tenant", @tenant_id, "test-namespace", @user_id,
                    "test-session", "test-user-agent", TIMESTAMP("2024-03-01 00:00:00 UTC"), "COMPLETION"
                ),
                (
                    "request2", "test-tenant", @tenant_id, "test-namespace", @user_id,
                    "test-session", "test-user-agent", TIMESTAMP("2024-02-01 00:00:00 UTC"), "EDIT"
                ),
                (
                    "request3", "test-tenant", @tenant_id, "test-namespace", @user_id,
                    "test-session", "test-user-agent", TIMESTAMP("2024-01-05 00:00:00 UTC"), "CHAT"
                )
        `, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: userId},
		}
		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 2, 2, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: timestamppb.New(time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC)),
					},
				},
				{
					Request: &pb.GetUserLastRequestTimestampRequest{
						UserId:   userId,
						TenantId: tenantID,
						RequestTypes: []pb.GetUserLastRequestTimestampRequest_RequestType{
							pb.GetUserLastRequestTimestampRequest_COMPLETION,
							pb.GetUserLastRequestTimestampRequest_EDIT,
							pb.GetUserLastRequestTimestampRequest_CHAT,
						},
						MinSearchTimestamp: timestamppb.New(time.Date(2024, 4, 1, 0, 0, 0, 0, time.UTC)),
					},
					Response: &pb.GetUserLastRequestTimestampResponse{
						LastRequestTimestamp: nil,
					},
				},
			},
			server.GetUserLastRequestTimestamp,
		)
	})
}

func TestGetUserChatRequestStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserChatRequestStatsRequest{
			// Missing tenant id.
			{
				UserId: userId,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing user id.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
				UserId:   userId,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetUserChatRequestStatsRequest{
				TenantId:    noDataTenantID,
				UserId:      userId,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetUserChatRequestStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserChatRequestStatsRequest{
			TenantId: noDataTenantID,
			UserId:   userId,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetUserChatRequestStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserChatRequestStatsRequest{
						TenantId: noDataTenantID,
						UserId:   userId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserChatRequestStatsResponse{
						RequestCount: 0,
					},
				},
			},
			server.GetUserChatRequestStats,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		testUserId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", testUserId)

		// Insert sample data for chat requests
		query := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type
			)
			VALUES
				(
					"request1", "test-tenant", @tenant_id, "test-namespace", "user1", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "CHAT"
				),
				(
					"request2", "test-tenant", @tenant_id, "test-namespace", "user2", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "CHAT"
				),
				(
					"request3", "test-tenant", @tenant_id, "test-namespace", "user3", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "CHAT"
				),
				(
					"request4", "test-tenant", @tenant_id, "test-namespace", "user4", "different-user", "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "CHAT"
				)
		`, server.datasetName))
		query.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: testUserId},
		}
		_, err := query.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for all dates
				{
					Request: &pb.GetUserChatRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserChatRequestStatsResponse{
						RequestCount: 3,
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetUserChatRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
					},
					Response: &pb.GetUserChatRequestStatsResponse{
						RequestCount: 2,
					},
				},
			},
			server.GetUserChatRequestStats,
		)
	})
}

func TestGetUserAgentRequestStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserAgentRequestStatsRequest{
			// Missing tenant id.
			{
				UserId: userId,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing user id.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
				UserId:   userId,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetUserAgentRequestStatsRequest{
				TenantId:    noDataTenantID,
				UserId:      userId,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetUserAgentRequestStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserAgentRequestStatsRequest{
			TenantId: noDataTenantID,
			UserId:   userId,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetUserAgentRequestStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserAgentRequestStatsRequest{
						TenantId: noDataTenantID,
						UserId:   userId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentRequestStatsResponse{
						RequestCount: 0,
					},
				},
			},
			server.GetUserAgentRequestStats,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		testUserId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", testUserId)

		// First insert data into request_metadata table
		metadataQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "user1", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace", "user2", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace", "user3", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "AGENT_CHAT"
				)
		`, server.datasetName))
		metadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: testUserId},
		}
		_, err := metadataQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Then insert corresponding events into agent_request_event table
		eventQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.agent_request_event (
				request_id, tenant, tenant_id, shard_namespace, time, sanitized_json
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace",
					"2024-01-01 00:00:00", JSON '{"event_name": "sent-user-message"}'
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace",
					"2024-01-01 00:00:00", JSON '{"event_name": "sent-user-message"}'
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace",
					"2024-01-02 00:00:00", JSON '{"event_name": "sent-user-message"}'
				)
		`, server.datasetName))
		eventQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err = eventQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for all dates
				{
					Request: &pb.GetUserAgentRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentRequestStatsResponse{
						RequestCount: 3,
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetUserAgentRequestStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
					},
					Response: &pb.GetUserAgentRequestStatsResponse{
						RequestCount: 2,
					},
				},
			},
			server.GetUserAgentRequestStats,
		)
	})
}

func TestGetUserAgentToolUseStats(t *testing.T) {
	server := testServer(t)

	t.Run("arg validation", func(t *testing.T) {
		badRequests := []*pb.GetUserAgentToolUseStatsRequest{
			// Missing tenant id.
			{
				UserId: userId,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing user id.
			{
				TenantId: noDataTenantID,
				DateFilters: &pb.DateFilters{
					StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
					EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
				},
			},
			// Missing date filters.
			{
				TenantId: noDataTenantID,
				UserId:   userId,
			},
		}
		for _, dateFilter := range badDateFilters() {
			badRequests = append(badRequests, &pb.GetUserAgentToolUseStatsRequest{
				TenantId:    noDataTenantID,
				UserId:      userId,
				DateFilters: dateFilter,
			})
		}

		for _, req := range badRequests {
			_, err := server.GetUserAgentToolUseStats(noDataAuthorizedContext(), req)
			if err == nil || status.Code(err) != codes.InvalidArgument {
				t.Errorf("Expected error for request: %v", req)
			}
		}
	})

	runAuthTests(
		t,
		&pb.GetUserAgentToolUseStatsRequest{
			TenantId: noDataTenantID,
			UserId:   userId,
			DateFilters: &pb.DateFilters{
				StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
				EndDate:   &pb.Date{Year: 2025, Month: 6, Day: 1},
			},
		},
		server.GetUserAgentToolUseStats,
	)

	t.Run("no data", func(t *testing.T) {
		testutils.CheckResponses(
			t,
			noDataAuthorizedContext(),
			[]testutils.RequestResponse{
				{
					Request: &pb.GetUserAgentToolUseStatsRequest{
						TenantId: noDataTenantID,
						UserId:   userId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentToolUseStatsResponse{
						ToolUseCount: 0,
					},
				},
			},
			server.GetUserAgentToolUseStats,
		)
	})

	t.Run("with data", func(t *testing.T) {
		tenantID := uuid.New().String()
		testUserId := "test-user"
		ctx := authorizedContext(tenantID, "test-tenant", testUserId)

		// Insert sample data for agent chat requests
		metadataQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.request_metadata (
				request_id, tenant, tenant_id, shard_namespace, user_id, opaque_user_id, user_id_type,
				session_id, user_agent, time, request_type
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "user1", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace", "user2", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-01 00:00:00", "AGENT_CHAT"
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace", "user3", @user_id, "AUGMENT",
					"test-session", "test-user-agent", "2024-01-02 00:00:00", "AGENT_CHAT"
				)
		`, server.datasetName))
		metadataQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
			{Name: "user_id", Value: testUserId},
		}
		_, err := metadataQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		// Insert sample data for tool uses
		toolUseQuery := server.bqClient.Query(fmt.Sprintf(`
			INSERT INTO %s.tool_use_data (
				request_id, tenant, tenant_id, shard_namespace, time, sanitized_json
			)
			VALUES
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00", JSON '{"tool_use_id": "tool1"}'
				),
				(
					"agent-request1", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00", JSON '{"tool_use_id": "tool2"}'
				),
				(
					"agent-request2", "test-tenant", @tenant_id, "test-namespace", "2024-01-01 00:00:00", JSON '{"tool_use_id": "tool3"}'
				),
				(
					"agent-request3", "test-tenant", @tenant_id, "test-namespace", "2024-01-02 00:00:00", JSON '{"tool_use_id": "tool4"}'
				)
		`, server.datasetName))
		toolUseQuery.Parameters = []bigquery.QueryParameter{
			{Name: "tenant_id", Value: tenantID},
		}
		_, err = toolUseQuery.Read(ctx)
		if err != nil {
			t.Fatal(err)
		}

		testutils.CheckResponses(
			t,
			ctx,
			[]testutils.RequestResponse{
				// Request for all dates
				{
					Request: &pb.GetUserAgentToolUseStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 2},
						},
					},
					Response: &pb.GetUserAgentToolUseStatsResponse{
						ToolUseCount: 4,
					},
				},
				// Request with stricter date filter
				{
					Request: &pb.GetUserAgentToolUseStatsRequest{
						TenantId: tenantID,
						UserId:   testUserId,
						DateFilters: &pb.DateFilters{
							StartDate: &pb.Date{Year: 2024, Month: 1, Day: 1},
							EndDate:   &pb.Date{Year: 2024, Month: 1, Day: 1},
						},
					},
					Response: &pb.GetUserAgentToolUseStatsResponse{
						ToolUseCount: 3,
					},
				},
			},
			server.GetUserAgentToolUseStats,
		)
	})
}

func TestAddZeroDates(t *testing.T) {
	t.Run("empty data", func(t *testing.T) {
		data := []*pb.DateAndCount{}
		start := civil.Date{Year: 2024, Month: 1, Day: 1}
		end := civil.Date{Year: 2024, Month: 1, Day: 2}
		res := addZeroDates(data, start, end)
		expected := []*pb.DateAndCount{
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
				Count: 0,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
				Count: 0,
			},
		}
		if len(res) != len(expected) {
			t.Errorf("Expected %d results, got %d", len(expected), len(res))
		}
		for i := range res {
			if res[i].Date.String() != expected[i].Date.String() || res[i].Count != expected[i].Count {
				t.Errorf("Expected %v, got %v", expected[i], res[i])
			}
		}
	})

	t.Run("non-empty data", func(t *testing.T) {
		data := []*pb.DateAndCount{
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
				Count: 1,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 3},
				Count: 2,
			},
		}
		start := civil.Date{Year: 2024, Month: 1, Day: 1}
		end := civil.Date{Year: 2024, Month: 1, Day: 3}
		res := addZeroDates(data, start, end)
		expected := []*pb.DateAndCount{
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 1},
				Count: 1,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 2},
				Count: 0,
			},
			{
				Date:  &pb.Date{Year: 2024, Month: 1, Day: 3},
				Count: 2,
			},
		}
		if len(res) != len(expected) {
			t.Errorf("Expected %d results, got %d", len(expected), len(res))
		}
		for i := range res {
			if res[i].Date.String() != expected[i].Date.String() || res[i].Count != expected[i].Count {
				t.Errorf("Expected %v, got %v", expected[i], res[i])
			}
		}
	})
}

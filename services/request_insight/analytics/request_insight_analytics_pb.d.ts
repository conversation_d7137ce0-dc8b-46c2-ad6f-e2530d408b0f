// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/request_insight/analytics/request_insight_analytics.proto (package request_insight.analytics, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message request_insight.analytics.GetCompletionStatsRequest
 */
export declare class GetCompletionStatsRequest extends Message<GetCompletionStatsRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 2;
   */
  dateFilters?: DateFilters;

  constructor(data?: PartialMessage<GetCompletionStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetCompletionStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCompletionStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCompletionStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCompletionStatsRequest;

  static equals(a: GetCompletionStatsRequest | PlainMessage<GetCompletionStatsRequest> | undefined, b: GetCompletionStatsRequest | PlainMessage<GetCompletionStatsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetCompletionStatsResponse
 */
export declare class GetCompletionStatsResponse extends Message<GetCompletionStatsResponse> {
  /**
   * @generated from field: repeated request_insight.analytics.GetCompletionStatsResponse.CompletionStats completion_stats = 1;
   */
  completionStats: GetCompletionStatsResponse_CompletionStats[];

  constructor(data?: PartialMessage<GetCompletionStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetCompletionStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCompletionStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCompletionStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCompletionStatsResponse;

  static equals(a: GetCompletionStatsResponse | PlainMessage<GetCompletionStatsResponse> | undefined, b: GetCompletionStatsResponse | PlainMessage<GetCompletionStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetCompletionStatsResponse.CompletionStats
 */
export declare class GetCompletionStatsResponse_CompletionStats extends Message<GetCompletionStatsResponse_CompletionStats> {
  /**
   * @generated from oneof request_insight.analytics.GetCompletionStatsResponse.CompletionStats.aggregation
   */
  aggregation: {
    /**
     * @generated from field: request_insight.analytics.Date date = 1;
     */
    value: Date;
    case: "date";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: uint32 request_count = 2;
   */
  requestCount: number;

  /**
   * @generated from field: uint32 accepted_count = 3;
   */
  acceptedCount: number;

  /**
   * @generated from field: uint32 accepted_line_count = 4;
   */
  acceptedLineCount: number;

  /**
   * @generated from field: uint32 accepted_character_count = 5;
   */
  acceptedCharacterCount: number;

  constructor(data?: PartialMessage<GetCompletionStatsResponse_CompletionStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetCompletionStatsResponse.CompletionStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCompletionStatsResponse_CompletionStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCompletionStatsResponse_CompletionStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCompletionStatsResponse_CompletionStats;

  static equals(a: GetCompletionStatsResponse_CompletionStats | PlainMessage<GetCompletionStatsResponse_CompletionStats> | undefined, b: GetCompletionStatsResponse_CompletionStats | PlainMessage<GetCompletionStatsResponse_CompletionStats> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetEditStatsRequest
 */
export declare class GetEditStatsRequest extends Message<GetEditStatsRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 2;
   */
  dateFilters?: DateFilters;

  constructor(data?: PartialMessage<GetEditStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetEditStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetEditStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetEditStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetEditStatsRequest;

  static equals(a: GetEditStatsRequest | PlainMessage<GetEditStatsRequest> | undefined, b: GetEditStatsRequest | PlainMessage<GetEditStatsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetEditStatsResponse
 */
export declare class GetEditStatsResponse extends Message<GetEditStatsResponse> {
  /**
   * @generated from field: repeated request_insight.analytics.GetEditStatsResponse.EditStats edit_stats = 1;
   */
  editStats: GetEditStatsResponse_EditStats[];

  constructor(data?: PartialMessage<GetEditStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetEditStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetEditStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetEditStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetEditStatsResponse;

  static equals(a: GetEditStatsResponse | PlainMessage<GetEditStatsResponse> | undefined, b: GetEditStatsResponse | PlainMessage<GetEditStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetEditStatsResponse.EditStats
 */
export declare class GetEditStatsResponse_EditStats extends Message<GetEditStatsResponse_EditStats> {
  /**
   * @generated from oneof request_insight.analytics.GetEditStatsResponse.EditStats.aggregation
   */
  aggregation: {
    /**
     * @generated from field: request_insight.analytics.Date date = 1;
     */
    value: Date;
    case: "date";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: uint32 request_count = 2;
   */
  requestCount: number;

  /**
   * @generated from field: uint32 accepted_count = 3;
   */
  acceptedCount: number;

  /**
   * @generated from field: uint32 accepted_line_count = 4;
   */
  acceptedLineCount: number;

  /**
   * @generated from field: uint32 accepted_character_count = 5;
   */
  acceptedCharacterCount: number;

  constructor(data?: PartialMessage<GetEditStatsResponse_EditStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetEditStatsResponse.EditStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetEditStatsResponse_EditStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetEditStatsResponse_EditStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetEditStatsResponse_EditStats;

  static equals(a: GetEditStatsResponse_EditStats | PlainMessage<GetEditStatsResponse_EditStats> | undefined, b: GetEditStatsResponse_EditStats | PlainMessage<GetEditStatsResponse_EditStats> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetChatStatsRequest
 */
export declare class GetChatStatsRequest extends Message<GetChatStatsRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 2;
   */
  dateFilters?: DateFilters;

  constructor(data?: PartialMessage<GetChatStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetChatStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetChatStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetChatStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetChatStatsRequest;

  static equals(a: GetChatStatsRequest | PlainMessage<GetChatStatsRequest> | undefined, b: GetChatStatsRequest | PlainMessage<GetChatStatsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetChatStatsResponse
 */
export declare class GetChatStatsResponse extends Message<GetChatStatsResponse> {
  /**
   * @generated from field: repeated request_insight.analytics.GetChatStatsResponse.ChatStats chat_stats = 1;
   */
  chatStats: GetChatStatsResponse_ChatStats[];

  constructor(data?: PartialMessage<GetChatStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetChatStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetChatStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetChatStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetChatStatsResponse;

  static equals(a: GetChatStatsResponse | PlainMessage<GetChatStatsResponse> | undefined, b: GetChatStatsResponse | PlainMessage<GetChatStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetChatStatsResponse.ChatStats
 */
export declare class GetChatStatsResponse_ChatStats extends Message<GetChatStatsResponse_ChatStats> {
  /**
   * @generated from oneof request_insight.analytics.GetChatStatsResponse.ChatStats.aggregation
   */
  aggregation: {
    /**
     * @generated from field: request_insight.analytics.Date date = 1;
     */
    value: Date;
    case: "date";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: uint32 request_count = 2;
   */
  requestCount: number;

  constructor(data?: PartialMessage<GetChatStatsResponse_ChatStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetChatStatsResponse.ChatStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetChatStatsResponse_ChatStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetChatStatsResponse_ChatStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetChatStatsResponse_ChatStats;

  static equals(a: GetChatStatsResponse_ChatStats | PlainMessage<GetChatStatsResponse_ChatStats> | undefined, b: GetChatStatsResponse_ChatStats | PlainMessage<GetChatStatsResponse_ChatStats> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetDevDaysRequest
 */
export declare class GetDevDaysRequest extends Message<GetDevDaysRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 2;
   */
  dateFilters?: DateFilters;

  constructor(data?: PartialMessage<GetDevDaysRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetDevDaysRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDevDaysRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDevDaysRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDevDaysRequest;

  static equals(a: GetDevDaysRequest | PlainMessage<GetDevDaysRequest> | undefined, b: GetDevDaysRequest | PlainMessage<GetDevDaysRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetDevDaysResponse
 */
export declare class GetDevDaysResponse extends Message<GetDevDaysResponse> {
  /**
   * @generated from field: repeated request_insight.analytics.DateAndCount dev_days = 1;
   */
  devDays: DateAndCount[];

  constructor(data?: PartialMessage<GetDevDaysResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetDevDaysResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDevDaysResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDevDaysResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDevDaysResponse;

  static equals(a: GetDevDaysResponse | PlainMessage<GetDevDaysResponse> | undefined, b: GetDevDaysResponse | PlainMessage<GetDevDaysResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetActiveUsersRequest
 */
export declare class GetActiveUsersRequest extends Message<GetActiveUsersRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 2;
   */
  dateFilters?: DateFilters;

  /**
   * @generated from field: repeated request_insight.analytics.GetActiveUsersRequest.RequestType request_types = 3;
   */
  requestTypes: GetActiveUsersRequest_RequestType[];

  constructor(data?: PartialMessage<GetActiveUsersRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetActiveUsersRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetActiveUsersRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetActiveUsersRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetActiveUsersRequest;

  static equals(a: GetActiveUsersRequest | PlainMessage<GetActiveUsersRequest> | undefined, b: GetActiveUsersRequest | PlainMessage<GetActiveUsersRequest> | undefined): boolean;
}

/**
 * @generated from enum request_insight.analytics.GetActiveUsersRequest.RequestType
 */
export declare enum GetActiveUsersRequest_RequestType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: COMPLETION = 1;
   */
  COMPLETION = 1,

  /**
   * @generated from enum value: EDIT = 2;
   */
  EDIT = 2,

  /**
   * @generated from enum value: CHAT = 3;
   */
  CHAT = 3,
}

/**
 * @generated from message request_insight.analytics.GetActiveUsersResponse
 */
export declare class GetActiveUsersResponse extends Message<GetActiveUsersResponse> {
  /**
   * @generated from field: repeated request_insight.analytics.DateAndCount active_users = 1;
   */
  activeUsers: DateAndCount[];

  constructor(data?: PartialMessage<GetActiveUsersResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetActiveUsersResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetActiveUsersResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetActiveUsersResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetActiveUsersResponse;

  static equals(a: GetActiveUsersResponse | PlainMessage<GetActiveUsersResponse> | undefined, b: GetActiveUsersResponse | PlainMessage<GetActiveUsersResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetUserLastRequestTimestampRequest
 */
export declare class GetUserLastRequestTimestampRequest extends Message<GetUserLastRequestTimestampRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: repeated request_insight.analytics.GetUserLastRequestTimestampRequest.RequestType request_types = 3;
   */
  requestTypes: GetUserLastRequestTimestampRequest_RequestType[];

  /**
   * @generated from field: google.protobuf.Timestamp min_search_timestamp = 4;
   */
  minSearchTimestamp?: Timestamp;

  constructor(data?: PartialMessage<GetUserLastRequestTimestampRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserLastRequestTimestampRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserLastRequestTimestampRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserLastRequestTimestampRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserLastRequestTimestampRequest;

  static equals(a: GetUserLastRequestTimestampRequest | PlainMessage<GetUserLastRequestTimestampRequest> | undefined, b: GetUserLastRequestTimestampRequest | PlainMessage<GetUserLastRequestTimestampRequest> | undefined): boolean;
}

/**
 * @generated from enum request_insight.analytics.GetUserLastRequestTimestampRequest.RequestType
 */
export declare enum GetUserLastRequestTimestampRequest_RequestType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: COMPLETION = 1;
   */
  COMPLETION = 1,

  /**
   * @generated from enum value: EDIT = 2;
   */
  EDIT = 2,

  /**
   * @generated from enum value: CHAT = 3;
   */
  CHAT = 3,
}

/**
 * @generated from message request_insight.analytics.GetUserChatRequestStatsRequest
 */
export declare class GetUserChatRequestStatsRequest extends Message<GetUserChatRequestStatsRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 3;
   */
  dateFilters?: DateFilters;

  constructor(data?: PartialMessage<GetUserChatRequestStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserChatRequestStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserChatRequestStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserChatRequestStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserChatRequestStatsRequest;

  static equals(a: GetUserChatRequestStatsRequest | PlainMessage<GetUserChatRequestStatsRequest> | undefined, b: GetUserChatRequestStatsRequest | PlainMessage<GetUserChatRequestStatsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetUserChatRequestStatsResponse
 */
export declare class GetUserChatRequestStatsResponse extends Message<GetUserChatRequestStatsResponse> {
  /**
   * @generated from field: uint32 request_count = 1;
   */
  requestCount: number;

  constructor(data?: PartialMessage<GetUserChatRequestStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserChatRequestStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserChatRequestStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserChatRequestStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserChatRequestStatsResponse;

  static equals(a: GetUserChatRequestStatsResponse | PlainMessage<GetUserChatRequestStatsResponse> | undefined, b: GetUserChatRequestStatsResponse | PlainMessage<GetUserChatRequestStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetUserAgentRequestStatsRequest
 */
export declare class GetUserAgentRequestStatsRequest extends Message<GetUserAgentRequestStatsRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 3;
   */
  dateFilters?: DateFilters;

  constructor(data?: PartialMessage<GetUserAgentRequestStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserAgentRequestStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserAgentRequestStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserAgentRequestStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserAgentRequestStatsRequest;

  static equals(a: GetUserAgentRequestStatsRequest | PlainMessage<GetUserAgentRequestStatsRequest> | undefined, b: GetUserAgentRequestStatsRequest | PlainMessage<GetUserAgentRequestStatsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetUserAgentRequestStatsResponse
 */
export declare class GetUserAgentRequestStatsResponse extends Message<GetUserAgentRequestStatsResponse> {
  /**
   * @generated from field: uint32 request_count = 1;
   */
  requestCount: number;

  constructor(data?: PartialMessage<GetUserAgentRequestStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserAgentRequestStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserAgentRequestStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserAgentRequestStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserAgentRequestStatsResponse;

  static equals(a: GetUserAgentRequestStatsResponse | PlainMessage<GetUserAgentRequestStatsResponse> | undefined, b: GetUserAgentRequestStatsResponse | PlainMessage<GetUserAgentRequestStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetUserAgentToolUseStatsRequest
 */
export declare class GetUserAgentToolUseStatsRequest extends Message<GetUserAgentToolUseStatsRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 3;
   */
  dateFilters?: DateFilters;

  constructor(data?: PartialMessage<GetUserAgentToolUseStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserAgentToolUseStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserAgentToolUseStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserAgentToolUseStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserAgentToolUseStatsRequest;

  static equals(a: GetUserAgentToolUseStatsRequest | PlainMessage<GetUserAgentToolUseStatsRequest> | undefined, b: GetUserAgentToolUseStatsRequest | PlainMessage<GetUserAgentToolUseStatsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetUserAgentToolUseStatsResponse
 */
export declare class GetUserAgentToolUseStatsResponse extends Message<GetUserAgentToolUseStatsResponse> {
  /**
   * @generated from field: uint32 tool_use_count = 1;
   */
  toolUseCount: number;

  constructor(data?: PartialMessage<GetUserAgentToolUseStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserAgentToolUseStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserAgentToolUseStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserAgentToolUseStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserAgentToolUseStatsResponse;

  static equals(a: GetUserAgentToolUseStatsResponse | PlainMessage<GetUserAgentToolUseStatsResponse> | undefined, b: GetUserAgentToolUseStatsResponse | PlainMessage<GetUserAgentToolUseStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetUserLastRequestTimestampResponse
 */
export declare class GetUserLastRequestTimestampResponse extends Message<GetUserLastRequestTimestampResponse> {
  /**
   * @generated from field: optional google.protobuf.Timestamp last_request_timestamp = 1;
   */
  lastRequestTimestamp?: Timestamp;

  constructor(data?: PartialMessage<GetUserLastRequestTimestampResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetUserLastRequestTimestampResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserLastRequestTimestampResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserLastRequestTimestampResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserLastRequestTimestampResponse;

  static equals(a: GetUserLastRequestTimestampResponse | PlainMessage<GetUserLastRequestTimestampResponse> | undefined, b: GetUserLastRequestTimestampResponse | PlainMessage<GetUserLastRequestTimestampResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetKeywordsStatsRequest
 */
export declare class GetKeywordsStatsRequest extends Message<GetKeywordsStatsRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 2;
   */
  dateFilters?: DateFilters;

  /**
   * @generated from field: request_insight.analytics.GetKeywordsStatsRequest.RequestType request_type = 3;
   */
  requestType: GetKeywordsStatsRequest_RequestType;

  constructor(data?: PartialMessage<GetKeywordsStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetKeywordsStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKeywordsStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKeywordsStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKeywordsStatsRequest;

  static equals(a: GetKeywordsStatsRequest | PlainMessage<GetKeywordsStatsRequest> | undefined, b: GetKeywordsStatsRequest | PlainMessage<GetKeywordsStatsRequest> | undefined): boolean;
}

/**
 * @generated from enum request_insight.analytics.GetKeywordsStatsRequest.RequestType
 */
export declare enum GetKeywordsStatsRequest_RequestType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: EDIT = 1;
   */
  EDIT = 1,

  /**
   * @generated from enum value: CHAT = 2;
   */
  CHAT = 2,
}

/**
 * @generated from message request_insight.analytics.GetKeywordsStatsResponse
 */
export declare class GetKeywordsStatsResponse extends Message<GetKeywordsStatsResponse> {
  /**
   * @generated from field: repeated request_insight.analytics.GetKeywordsStatsResponse.KeywordStats keywords_stats = 1;
   */
  keywordsStats: GetKeywordsStatsResponse_KeywordStats[];

  constructor(data?: PartialMessage<GetKeywordsStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetKeywordsStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKeywordsStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKeywordsStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKeywordsStatsResponse;

  static equals(a: GetKeywordsStatsResponse | PlainMessage<GetKeywordsStatsResponse> | undefined, b: GetKeywordsStatsResponse | PlainMessage<GetKeywordsStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetKeywordsStatsResponse.KeywordStats
 */
export declare class GetKeywordsStatsResponse_KeywordStats extends Message<GetKeywordsStatsResponse_KeywordStats> {
  /**
   * @generated from field: string keyword = 1;
   */
  keyword: string;

  /**
   * @generated from field: uint32 count = 2;
   */
  count: number;

  constructor(data?: PartialMessage<GetKeywordsStatsResponse_KeywordStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetKeywordsStatsResponse.KeywordStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKeywordsStatsResponse_KeywordStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKeywordsStatsResponse_KeywordStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKeywordsStatsResponse_KeywordStats;

  static equals(a: GetKeywordsStatsResponse_KeywordStats | PlainMessage<GetKeywordsStatsResponse_KeywordStats> | undefined, b: GetKeywordsStatsResponse_KeywordStats | PlainMessage<GetKeywordsStatsResponse_KeywordStats> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetCategoriesStatsRequest
 */
export declare class GetCategoriesStatsRequest extends Message<GetCategoriesStatsRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: request_insight.analytics.DateFilters date_filters = 2;
   */
  dateFilters?: DateFilters;

  /**
   * @generated from field: request_insight.analytics.GetCategoriesStatsRequest.RequestType request_type = 3;
   */
  requestType: GetCategoriesStatsRequest_RequestType;

  constructor(data?: PartialMessage<GetCategoriesStatsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetCategoriesStatsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCategoriesStatsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCategoriesStatsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCategoriesStatsRequest;

  static equals(a: GetCategoriesStatsRequest | PlainMessage<GetCategoriesStatsRequest> | undefined, b: GetCategoriesStatsRequest | PlainMessage<GetCategoriesStatsRequest> | undefined): boolean;
}

/**
 * @generated from enum request_insight.analytics.GetCategoriesStatsRequest.RequestType
 */
export declare enum GetCategoriesStatsRequest_RequestType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: EDIT = 1;
   */
  EDIT = 1,

  /**
   * @generated from enum value: CHAT = 2;
   */
  CHAT = 2,
}

/**
 * @generated from message request_insight.analytics.GetCategoriesStatsResponse
 */
export declare class GetCategoriesStatsResponse extends Message<GetCategoriesStatsResponse> {
  /**
   * @generated from field: repeated request_insight.analytics.GetCategoriesStatsResponse.CategoryStats categories_stats = 1;
   */
  categoriesStats: GetCategoriesStatsResponse_CategoryStats[];

  constructor(data?: PartialMessage<GetCategoriesStatsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetCategoriesStatsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCategoriesStatsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCategoriesStatsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCategoriesStatsResponse;

  static equals(a: GetCategoriesStatsResponse | PlainMessage<GetCategoriesStatsResponse> | undefined, b: GetCategoriesStatsResponse | PlainMessage<GetCategoriesStatsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetCategoriesStatsResponse.CategoryStats
 */
export declare class GetCategoriesStatsResponse_CategoryStats extends Message<GetCategoriesStatsResponse_CategoryStats> {
  /**
   * @generated from field: string category = 1;
   */
  category: string;

  /**
   * @generated from field: uint32 count = 2;
   */
  count: number;

  constructor(data?: PartialMessage<GetCategoriesStatsResponse_CategoryStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetCategoriesStatsResponse.CategoryStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCategoriesStatsResponse_CategoryStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCategoriesStatsResponse_CategoryStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCategoriesStatsResponse_CategoryStats;

  static equals(a: GetCategoriesStatsResponse_CategoryStats | PlainMessage<GetCategoriesStatsResponse_CategoryStats> | undefined, b: GetCategoriesStatsResponse_CategoryStats | PlainMessage<GetCategoriesStatsResponse_CategoryStats> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetEarliestRequestTimestampRequest
 */
export declare class GetEarliestRequestTimestampRequest extends Message<GetEarliestRequestTimestampRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  constructor(data?: PartialMessage<GetEarliestRequestTimestampRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetEarliestRequestTimestampRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetEarliestRequestTimestampRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetEarliestRequestTimestampRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetEarliestRequestTimestampRequest;

  static equals(a: GetEarliestRequestTimestampRequest | PlainMessage<GetEarliestRequestTimestampRequest> | undefined, b: GetEarliestRequestTimestampRequest | PlainMessage<GetEarliestRequestTimestampRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.GetEarliestRequestTimestampResponse
 */
export declare class GetEarliestRequestTimestampResponse extends Message<GetEarliestRequestTimestampResponse> {
  /**
   * @generated from field: optional google.protobuf.Timestamp earliest_request_timestamp = 1;
   */
  earliestRequestTimestamp?: Timestamp;

  constructor(data?: PartialMessage<GetEarliestRequestTimestampResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.GetEarliestRequestTimestampResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetEarliestRequestTimestampResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetEarliestRequestTimestampResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetEarliestRequestTimestampResponse;

  static equals(a: GetEarliestRequestTimestampResponse | PlainMessage<GetEarliestRequestTimestampResponse> | undefined, b: GetEarliestRequestTimestampResponse | PlainMessage<GetEarliestRequestTimestampResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.DateFilters
 */
export declare class DateFilters extends Message<DateFilters> {
  /**
   * @generated from field: request_insight.analytics.Date start_date = 2;
   */
  startDate?: Date;

  /**
   * @generated from field: request_insight.analytics.Date end_date = 3;
   */
  endDate?: Date;

  /**
   * @generated from field: optional string timezone = 4;
   */
  timezone?: string;

  constructor(data?: PartialMessage<DateFilters>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.DateFilters";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DateFilters;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DateFilters;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DateFilters;

  static equals(a: DateFilters | PlainMessage<DateFilters> | undefined, b: DateFilters | PlainMessage<DateFilters> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.DateAndCount
 */
export declare class DateAndCount extends Message<DateAndCount> {
  /**
   * @generated from field: request_insight.analytics.Date date = 1;
   */
  date?: Date;

  /**
   * @generated from field: int32 count = 2;
   */
  count: number;

  constructor(data?: PartialMessage<DateAndCount>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.DateAndCount";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DateAndCount;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DateAndCount;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DateAndCount;

  static equals(a: DateAndCount | PlainMessage<DateAndCount> | undefined, b: DateAndCount | PlainMessage<DateAndCount> | undefined): boolean;
}

/**
 * @generated from message request_insight.analytics.Date
 */
export declare class Date extends Message<Date> {
  /**
   * @generated from field: int32 year = 1;
   */
  year: number;

  /**
   * @generated from field: int32 month = 2;
   */
  month: number;

  /**
   * @generated from field: int32 day = 3;
   */
  day: number;

  constructor(data?: PartialMessage<Date>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.analytics.Date";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Date;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Date;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Date;

  static equals(a: Date | PlainMessage<Date> | undefined, b: Date | PlainMessage<Date> | undefined): boolean;
}


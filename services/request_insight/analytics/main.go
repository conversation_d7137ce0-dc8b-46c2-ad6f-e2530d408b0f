package main

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"

	"github.com/augmentcode/augment/base/logging"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	pb "github.com/augmentcode/augment/services/request_insight/analytics/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

type Config struct {
	AnalyticsServerConfig

	// the port the grpc server will listen on
	Port int `json:"port"`

	// Server TLS configuration
	ServerMtls *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls *tlsconfig.ClientConfig `json:"client_mtls"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	Namespace string `json:"namespace"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`
}

func loadServerTLS(serverCertPath, serverKeyPath, serverCaPath string) (credentials.TransportCredentials, error) {
	cert, err := tls.LoadX509KeyPair(serverCertPath, serverKeyPath)
	if err != nil {
		return nil, err
	}

	certpool := x509.NewCertPool()
	x, err := os.ReadFile(serverCaPath)
	if err != nil {
		return nil, err
	}
	certpool.AppendCertsFromPEM(x)

	// Create TLS credentials
	creds := credentials.NewTLS(&tls.Config{
		Certificates: []tls.Certificate{cert},
		ClientAuth:   tls.RequireAndVerifyClientCert,
		ClientCAs:    certpool,
	})
	return creds, nil
}

func loadClientTLS(
	clientCertPath, clientKeyPath, clientCaPath string,
) (credentials.TransportCredentials, error) {
	cert, err := tls.LoadX509KeyPair(clientCertPath, clientKeyPath)
	if err != nil {
		return nil, err
	}

	certpool := x509.NewCertPool()
	x, err := os.ReadFile(clientCaPath)
	if err != nil {
		return nil, err
	}
	certpool.AppendCertsFromPEM(x)

	// Create TLS credentials
	creds := credentials.NewTLS(&tls.Config{
		Certificates: []tls.Certificate{cert},
		RootCAs:      certpool,
	})
	return creds, nil
}

func main() {
	logging.SetupServerLogging()

	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
		os.Exit(1)
	}

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	var opts []grpc.ServerOption
	creds, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to load server certificates: %v", err)
	}
	opts = append(opts, grpc.Creds(creds))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	// Set up service token auth.
	if config.AuthEnabled {
		tokenExchangeClient, err := tokenexchange.New(
			config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(clientCreds),
		)
		if err != nil {
			log.Fatal().Err(err).Msgf("Error creating token exchange client")
			os.Exit(1)
		}
		defer tokenExchangeClient.Close()
		serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
		authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
		opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	} else {
		log.Warn().Msg("Auth is disabled. This should never happen outside of dev.")
	}

	ctx := context.Background()

	analyticsServer, err := newServer(ctx, &config.AnalyticsServerConfig)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating analytics server: %v", err)
		os.Exit(1)
	}

	grpcServer := grpc.NewServer(opts...)
	// Setup prometheus metrics for gRPC calls.
	srvMetrics.InitializeMetrics(grpcServer)
	// Setup analytics server.
	pb.RegisterRequestInsightAnalyticsServer(grpcServer, analyticsServer)
	// Setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())
	// setup reflection for debugging
	reflection.Register(grpcServer)

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
		os.Exit(1)
	}
	log.Info().Msgf("Listening on %v", lis.Addr())
	err = grpcServer.Serve(lis)
	if err != nil {
		log.Fatal().Err(err).Msg("Error serving")
		os.Exit(1)
	}
}

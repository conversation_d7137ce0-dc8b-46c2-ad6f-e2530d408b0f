load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_binary", "go_grpc_library", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

go_library(
    name = "request_insight_analytics_server_lib",
    srcs = [
        "analytics_server.go",
        "main.go",
    ],
    importpath = "github.com/augmentcode/augment/services/analytics",
    visibility = ["//visibility:private"],
    deps = [
        ":request_insight_analytics_go_proto",
        "//base/logging:logging_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/token_exchange/client:client_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:go_default_library",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go//civil",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@org_golang_google_api//iterator",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//peer",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "request_insight_analytics_server_test",
    srcs = [
        "analytics_server_test.go",
    ],
    data = [
        "//services/request_insight/analytics_dataset:schema_json",
    ],
    embed = [":request_insight_analytics_server_lib"],
    deps = [
        "//base/test_utils:testing_utils_go",
        "//base/test_utils/bigquery:emulator_go",
        "@google_jsonnet_go//:go_default_library",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "request_insight_analytics_server",
    embed = [":request_insight_analytics_server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":request_insight_analytics_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

proto_library(
    name = "request_insight_analytics_proto",
    srcs = ["request_insight_analytics.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "request_insight_analytics_go_proto",
    importpath = "github.com/augmentcode/augment/services/request_insight/analytics/proto",
    proto = ":request_insight_analytics_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)

ts_proto_library(
    name = "request_insight_analytics_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":request_insight_analytics_proto",
    visibility = ["//services:__subpackages__"],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

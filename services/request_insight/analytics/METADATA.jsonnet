{
  deployment: [
    {
      name: 'request-insight-analytics',
      kubecfg: {
        target: '//services/request_insight/analytics:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#analytics',
        },
      },
    },
  ],
}

syntax = "proto3";

package request_insight.analytics;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/augmentcode/augment/services/request_insight/analytics/proto";

// Service for querying Request Insight analytics from BigQuery. This service is NOT responsible
// for writing to BigQuery.
service RequestInsightAnalytics {
  // Get the dev days for a given tenant, per day. Dev days are the number of unique users that
  // accepted a completion, edit, or chat in a given day.
  rpc GetDevDays(GetDevDaysRequest) returns (GetDevDaysResponse);

  // Get the active users for a given tenant, per day. Active users are the number of unique users
  // that made a request in a given day.
  rpc GetActiveUsers(GetActiveUsersRequest) returns (GetActiveUsersResponse);

  // Get statistics about the completions for a given tenant, by date.
  rpc GetCompletionStats(GetCompletionStatsRequest) returns (GetCompletionStatsResponse);

  // Get statistics about the edits for a given tenant, by date.
  rpc GetEditStats(GetEditStatsRequest) returns (GetEditStatsResponse);

  // Get statistics about the chats for a given tenant, by date.
  rpc GetChatStats(GetChatStatsRequest) returns (GetChatStatsResponse);

  // Get statistics about keywords from edit and chat requests for a given tenant, over a given set
  // of dates. This is separate from the edit/chat stats endpoints because:
  // 1. The query is much simpler to do separately.
  // 2. We don't have any clients that need keywords aggregated by date currently, so this also
  //    simplifies client usage.
  rpc GetKeywordsStats(GetKeywordsStatsRequest) returns (GetKeywordsStatsResponse);

  // Get statistics about the message categories from edit and chat requests for a given tenant, over
  // a given set of dates. This follows the same pattern as GetKeywordsStats.
  rpc GetCategoriesStats(GetCategoriesStatsRequest) returns (GetCategoriesStatsResponse);

  // Get the earliest date that we have data for for a given tenant.
  rpc GetEarliestRequestTimestamp(GetEarliestRequestTimestampRequest) returns (GetEarliestRequestTimestampResponse);

  // Get the last date that we have data for for a given user and request type.
  rpc GetUserLastRequestTimestamp(GetUserLastRequestTimestampRequest) returns (GetUserLastRequestTimestampResponse);

  // Get the number of chat requests made by a user in a given time period.
  rpc GetUserChatRequestStats(GetUserChatRequestStatsRequest) returns (GetUserChatRequestStatsResponse);

  // Get the number of agent requests made by a user in a given time period.
  rpc GetUserAgentRequestStats(GetUserAgentRequestStatsRequest) returns (GetUserAgentRequestStatsResponse);

  // Get the number of agent tool uses made by a user in a given time period.
  rpc GetUserAgentToolUseStats(GetUserAgentToolUseStatsRequest) returns (GetUserAgentToolUseStatsResponse);
}

message GetCompletionStatsRequest {
  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;

  // The date filters to use for the query.
  DateFilters date_filters = 2;
}

message GetCompletionStatsResponse {
  message CompletionStats {
    oneof aggregation {
      // The date for this set of statistics.
      Date date = 1;
    }

    // This is the number of completion requests that resulted in something being shown to the user,
    // NOT the total number of requests made by the extension, which will be much larger.
    uint32 request_count = 2;

    // Number of accepted completions.
    uint32 accepted_count = 3;

    // Number of lines in accepted completions.
    uint32 accepted_line_count = 4;

    // Number of characters in accepted completions.
    uint32 accepted_character_count = 5;
  }

  repeated CompletionStats completion_stats = 1;
}

message GetEditStatsRequest {
  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;

  // The date filters to use for the query.
  DateFilters date_filters = 2;
}

message GetEditStatsResponse {
  message EditStats {
    oneof aggregation {
      // The date for this set of statistics.
      Date date = 1;
    }

    // Number of edit requests made.
    uint32 request_count = 2;

    // Number of accepted edits.
    uint32 accepted_count = 3;

    // Number of lines in accepted edits.
    uint32 accepted_line_count = 4;

    // Number of characters in accepted edits.
    uint32 accepted_character_count = 5;
  }

  repeated EditStats edit_stats = 1;
}

message GetChatStatsRequest {
  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;

  // The date filters to use for the query.
  DateFilters date_filters = 2;
}

message GetChatStatsResponse {
  message ChatStats {
    oneof aggregation {
      // The date for this set of statistics.
      Date date = 1;
    }

    // Number of chat messages sent.
    uint32 request_count = 2;
  }

  repeated ChatStats chat_stats = 1;
}

message GetDevDaysRequest {
  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;

  // The date filters to use for the query.
  DateFilters date_filters = 2;
}

message GetDevDaysResponse {
  // List of date and dev days, sorted by date.
  repeated DateAndCount dev_days = 1;
}

message GetActiveUsersRequest {
  // The types of requests that we can query active users for.
  enum RequestType {
    UNKNOWN = 0;
    COMPLETION = 1;
    EDIT = 2;
    CHAT = 3;
  }

  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;

  // The date filters to use for the query.
  DateFilters date_filters = 2;

  // The types of requests to get active users for. Returns all request types if none are provided.
  repeated RequestType request_types = 3;
}

message GetActiveUsersResponse {
  // List of date and dev days, sorted by date.
  repeated DateAndCount active_users = 1;
}

message GetUserLastRequestTimestampRequest {
  // The types of requests that we can filter for last request.
  enum RequestType {
    UNKNOWN = 0;
    COMPLETION = 1;
    EDIT = 2;
    CHAT = 3;
  }

  // The id of the user to query. It's an error if this doesn't match the service token for the
  // request.
  string user_id = 1;

  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 2;

  // The type of requests to get last request for.
  repeated RequestType request_types = 3;

  // The minimum timestamp to search from.
  google.protobuf.Timestamp min_search_timestamp = 4;
}

message GetUserChatRequestStatsRequest {
  // The id of the user to query. It's an error if this doesn't match the service token for the
  // request.
  string user_id = 1;

  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 2;

  // The date filters to use for the query.
  DateFilters date_filters = 3;
}

message GetUserChatRequestStatsResponse {
  // The number of chat requests made by the user in the given time period.
  uint32 request_count = 1;
}

message GetUserAgentRequestStatsRequest {
  // The id of the user to query. It's an error if this doesn't match the service token for the
  // request.
  string user_id = 1;

  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 2;

  // The date filters to use for the query.
  DateFilters date_filters = 3;
}

message GetUserAgentRequestStatsResponse {
  // The number of agent requests made by the user in the given time period.
  uint32 request_count = 1;
}

message GetUserAgentToolUseStatsRequest {
  // The id of the user to query. It's an error if this doesn't match the service token for the
  // request.
  string user_id = 1;

  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 2;

  // The date filters to use for the query.
  DateFilters date_filters = 3;
}

message GetUserAgentToolUseStatsResponse {
  // The number of agent tool uses made by the user in the given time period.
  uint32 tool_use_count = 1;
}

message GetUserLastRequestTimestampResponse {
  // The timestamp of the last request for the given user and request type. Null if there are no requests.
  optional google.protobuf.Timestamp last_request_timestamp = 1;
}

message GetKeywordsStatsRequest {
  // The types of requsts that we record keywords for.
  enum RequestType {
    UNKNOWN = 0;
    EDIT = 1;
    CHAT = 2;
  }

  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;

  // The date filters to use for the query.
  DateFilters date_filters = 2;

  // The type of request to get keywords for.
  RequestType request_type = 3;
}

message GetKeywordsStatsResponse {
  // Wrapper around a single keyword and statistics about it.
  message KeywordStats {
    string keyword = 1;

    // The number of times this keyword appeared in a request in the given time range.
    uint32 count = 2;
  }

  // Keywords that didn't appear in any requests won't be included in this list.
  repeated KeywordStats keywords_stats = 1;
}

message GetCategoriesStatsRequest {
  // The types of requsts that we record categories for.
  enum RequestType {
    UNKNOWN = 0;
    EDIT = 1;
    CHAT = 2;
  }

  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;

  // The date filters to use for the query.
  DateFilters date_filters = 2;

  // The type of request to get categories for.
  RequestType request_type = 3;
}

message GetCategoriesStatsResponse {
  // Wrapper around a single category and statistics about it.
  message CategoryStats {
    string category = 1;

    // The number of times this category appeared in a request in the given time range.
    uint32 count = 2;
  }

  repeated CategoryStats categories_stats = 1;
}

message GetEarliestRequestTimestampRequest {
  // The id of the tenant to query. It's an error if this doesn't match the service token for the
  // request.
  string tenant_id = 1;
}

message GetEarliestRequestTimestampResponse {
  // The timestamp of the earliest request for the given tenant. Null if there are no requests.
  optional google.protobuf.Timestamp earliest_request_timestamp = 1;
}

// Filters used by endpoints that return data by date.
message DateFilters {
  // The first date to query, inclusive.
  Date start_date = 2;

  // The last date to query, inclusive.
  Date end_date = 3;

  // The timezone to use for the query, as in the tz database (e.g., "America/Los_Angeles").
  // Defaults to "America/Los_Angeles".
  optional string timezone = 4;
}

// Wrapper around a date and a count.
message DateAndCount {
  Date date = 1;
  int32 count = 2;
}

// Modeled after google's date proto:
// https://github.com/googleapis/googleapis/blob/master/google/type/date.proto
//
// This used to be imported, but we couldn't find a TypeScript dependency that worked
// so now it is copied in. If we find a dependency that works, we can switch back to importing.
message Date {
  int32 year = 1;
  int32 month = 2;
  int32 day = 3;
}

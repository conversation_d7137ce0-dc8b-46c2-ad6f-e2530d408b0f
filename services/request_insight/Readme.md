# Request Insight

The request insight system is used to see which completion requests were happening, what parameters were used
and the output.

The goal is to be able to triage, including repeat completions from a quality point of view.

The goal of the system is not to investigate performance and system stability issues.

## Design

The request insight server is called from various services to submit information about events w.r.t. to completions.
The goal is to capture all information that is required to understand exactly what request was executed and to potentially
replay it exactly.

The API can be seen in `request_insight.proto`.

## Links

- [https://www.notion.so/AU-75-Support-Platform-0c3626f8e524426daf7815117942f0c2?pvs=4](Early Design Document)

local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  // Ignore customer-ui publish errors. It's a known issue that the TS publisher library is flaky.
  local minutes = 15;
  local publishMetric = 'au_request_insight_pubsub_publish_count_total';
  local publisherAppNoMatch = '^customer-ui$';
  local publishSpec = {
    displayName: 'Request Insight publish error rates high',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (namespace) (increase(%(metric)s{status!="success", app!~"%(appNoMatch)s"}[%(minutes)sm])) /
        sum by (namespace) (increase(%(metric)s{app!~"%(appNoMatch)s"}[%(minutes)sm])) > 0.1
      ||| % {
        metric: publishMetric,
        appNoMatch: publisherAppNoMatch,
        minutes: minutes,
      },
    },
  };

  // Ignore in the standard pubsub alerts:
  // - Dead letter subscriptions, which get their own alert.
  // - The i1 blob exporter subscription, which is known to fall very behind (AU-7782).
  // - The user event full export subscription, which is known to have scalability issues and is
  //   being deprecated.
  local subscriptionNameMatch = '.*request-insight.*|.*-ri-.*';
  local subscriptionNameNoMatch = '.*deadletter-sub$|^i1-ri-blob-exporter-sub$|.*ri-user-event-full-export-bigquery-sub$';

  local backlogSpec = {
    displayName: 'Request Insight pub/sub backlog',
    conditionPrometheusQueryLanguage: {
      duration: '3600s',  // 1 hour
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (subscription_id) (pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~"%s",
          subscription_id!~"%s"
        }) > 20000
      ||| % [subscriptionNameMatch, subscriptionNameNoMatch],
    },
  };

  local unackedSpec = {
    displayName: 'Request Insight pub/sub old unacked message',
    conditionPrometheusQueryLanguage: {
      duration: '900s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      // 21600 seconds is 6 hours
      query: |||
        max by (subscription_id)(pubsub_googleapis_com:subscription_oldest_unacked_message_age{
          monitored_resource="pubsub_subscription",
          subscription_id=~"%s",
          subscription_id!~"%s"
        }) > 21600
      ||| % [subscriptionNameMatch, subscriptionNameNoMatch],
    },
  };

  // Alert on having any messages in a dead letter queue.
  local deadLetterSpec = {
    displayName: 'Request Insight dead letter queue',
    conditionPrometheusQueryLanguage: {
      duration: '60s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (subscription_id) (pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~".*request-insight.*deadletter-sub$"
        }) > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      publishSpec,
      'request-insight-publish',
      'Request Insight publisher error rates are high in namespace %s' % monitoringLib.label('namespace'),
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      backlogSpec,
      'request-insight-pubsub-backlog',
      'Request Insight is at least 10k messages behind processing subscription %s' % monitoringLib.label('subscription_id'),
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      unackedSpec,
      'request-insight-pubsub-unacked',
      'RI messages in subscription %s are unacked after 6 hours' % monitoringLib.label('subscription_id'),
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      deadLetterSpec,
      'request-insight-dead-letter-queue',
      'RI dead letter queue %s has messages' % monitoringLib.label('subscription_id'),
      team='insights',
    ),
  ]

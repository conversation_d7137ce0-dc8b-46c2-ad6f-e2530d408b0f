local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

// Returns the objects needed to run a BigQuery backfill as a Kubernetes job. Depending on your
// backfill you may need additional objects, but this provides the minimum set that every backfill
// will use.
// Args:
//   env: The environment to run the job in.
//   namespace: The namespace to run the job in.
//   cloud: The cloud to run the job in.
//   useSharedDevRequestInsightBigquery: If env == DEV, this determines whether the backfill is run
//       on the shared central dataset or user dataset.
//   appName: The name of the backfill.
//   shortAppName: A short name for the backfill (used to generate an IAM service account, which has
//       an assert on name length).
//   config: The job configuration object.
//   cloudIdentityGroup: The name of the cloud identity group to use to grant access to the dataset
//       being backfilled..
//   targetName: The image target with the backfill logic.
//   extraVolumes: (optional) Extra volumes to mount in the pod.
//   extraVolumeMounts: (optional) Extra volume mounts to add to the container.
function(env, namespace, cloud, useSharedDevRequestInsightBigquery, appName, shortAppName, config, cloudIdentityGroup, targetName, extraVolumes=[], extraVolumeMounts=[])
  local projectId = cloudInfo[cloud].projectId;

  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  local gcpObjects = [
    // Give access to the BigQuery dataset. BigQuery doesn't support IAM, so this is
    // done with a Google Group membership instead. (See bigquery.jsonnet).
    {
      apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
      kind: 'CloudIdentityMembership',
      metadata: {
        name: '%s-%s-membership' % [namespace, appName],
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        groupRef: {
          name: cloudIdentityGroup,
          namespace:
            if cloud == 'GCP_US_CENTRAL1_DEV' then (
              if useSharedDevRequestInsightBigquery then 'central-dev' else namespace
            )
            else if env == 'STAGING' then 'central-staging'
            else 'central',
        },
        preferredMemberKey: {
          id: serviceAccount.serviceAccountGcpEmailAddress,
        },
        roles: [
          {
            name: 'MEMBER',
          },
        ],
      },
    },
    // Give access to run BigQuery queries.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicyMember',
      metadata: {
        name: 'iampolicymember-bigquery-job-%s' % appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress,
        role: 'roles/bigquery.jobUser',
        resourceRef: {
          kind: 'Project',
          external: 'projects/%s' % projectId,
        },
      },
    },
  ];

  local configMap = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: '%s-config' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      'config.json': std.manifestJson(config),
    },
  };
  local container = {
    name: appName,
    target: {
      name: targetName,
      dst: 'request_insight_backfill',
    },
    volumeMounts: [
      {
        name: 'config',
        mountPath: '/config',
        readOnly: true,
      },
    ] + extraVolumeMounts,
    resources: {
      limits: {
        cpu: 1,
        memory: '1Gi',
      },
    },
    args: [
      '--config',
      '/config/config.json',
    ],
    env: [
      {
        name: 'POD_NAMESPACE',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.namespace',
          },
        },
      },
    ],
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    volumes: [
      {
        name: 'config',
        configMap: {
          name: '%s-config' % appName,
          items: [
            {
              key: 'config.json',
              path: 'config.json',
            },
          ],
        },
      },
    ] + extraVolumes,
  };

  // minAvailable=1 in an attempt to keep kubernetes from kicking the job off of its node. These
  // jobs should be short-lived so won't disrupt maintenance.
  local podDisruptionBudget = nodeLib.podDisruption(namespace, appName, env, minAvailable=1);

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  // we allow multiple backfillers on the same host as they run in the background
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);
  local job = {
    apiVersion: 'batch/v1',
    kind: 'Job',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // Don't retry the job if it fails, since depending on the job this could cause duplicated
      // data.
      backoffLimit: 0,
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          restartPolicy: 'Never',
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  local objects = lib.flatten([
    configMap,
    job,
    podDisruptionBudget,
    serviceAccount.objects,
    gcpObjects,
  ]);

  {
    objects: objects,
    serviceAccount: serviceAccount,
  }

"""Subscriber for writing request insight events to bigquery."""

import argparse
import pathlib
from functools import partial
from typing import Iterable

import structlog
from prometheus_client import start_http_server

import base.feature_flags
import services.request_insight.request_insight_pb2 as request_insight_pb2
from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import StandardSignalHandler
from services.request_insight.bigquery_exporter.bigquery_persistence import (
    BigQueryPersistence,
)
from services.request_insight.bigquery_exporter.config import _load_config
from services.request_insight.lib.request_insight_subscriber import (
    RequestInsightSubscriber,
)

log = structlog.get_logger()


def process_batch(
    message_batch: Iterable[request_insight_pb2.RequestInsightMessage],
    persistence: BigQueryPersistence,
):
    """Process a single batch of request insight messages."""
    persistence.write(message_batch)


def main():
    _ = StandardSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--health-file",
        type=pathlib.Path,
        help="Path to a file to write a health check to",
    )
    args = parser.parse_args()
    log.info("Args %s", args)

    config = _load_config(args.config_file)
    log.info("Config: %s", config)

    start_http_server(9090)

    flag_context = base.feature_flags.Context.setup(
        pathlib.Path(config.feature_flags_sdk_key_path)
    )
    base.feature_flags.set_global_context(flag_context)

    log.info("Exporting to %s", config.bigquery_dataset_name)
    persistence = BigQueryPersistence(
        config.bigquery_dataset_name,
        namespace=config.namespace,
        data_config=config.data_config,
    )
    persistence.connect()

    subscriber = RequestInsightSubscriber(
        config.project_id,
        config.topic_name,
        config.subscription_name,
        config.subscription_description,
        config.subscription_batch_size,
        health_file=args.health_file,
    )
    subscriber.run(
        partial(
            process_batch,
            persistence=persistence,
        )
    )


if __name__ == "__main__":
    main()

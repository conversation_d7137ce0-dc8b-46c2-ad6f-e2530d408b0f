// K8S deployment file for the request insight bigquery subscriber
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local dataConfigLib = import 'services/request_insight/bigquery_exporter/bigquery_data_config_lib.jsonnet';
local exporterLib = import 'services/request_insight/lib/exporter_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local projectId = cloudInfo[cloud].projectId;

  local appName = 'request-insight-bigquery-exporter';
  local shortAppName = if cloudInfo.isUniqueNamespace(cloud, env, namespace) then
    // Keep this name for backward compatibility in most namespaces
    'ri-bigquery-exp'
  else
    // Cluster name will be prepended for non-unique namespaces
    // Use a shorter name here to avoid exceeding the 30 character limit
    'ri-bq-exp';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName, dropClusterName=false,
  );

  local datasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );

  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(
    env=env, namespace=namespace, cloud=cloud, appName=appName,
  );

  local subscriber = exporterLib.subscriber(
    // Hardcode the appName to make the subscription name backward-compatible
    cloud, env, namespace, appName='request-insight-bigquery', serviceAccount=serviceAccount,
  );

  local config = {
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    project_id: projectId,
    topic_name: subscriber.topicName,
    subscription_name: subscriber.subscriptionName,
    subscription_description: 'ri-bigquery-exporter',
    subscription_batch_size: 50,
    bigquery_dataset_name: datasetLib.datasetGcp,
    namespace: namespace,
    data_config: {
      path_keywords: dataConfigLib.pathKeywords,
      instruction_keywords: dataConfigLib.instructionKeywords,
      chat_keywords: dataConfigLib.chatKeywords,
      keyword_categories: dataConfigLib.keywordCategories,
      file_extensions: dataConfigLib.fileExtensions,
      terminal_command_prefixes: dataConfigLib.terminalCommandPrefixes,
    },
  };

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local gcpObjects = [
    // Pub/sub subscription.
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubSubscription',
      metadata: {
        name: config.subscription_name,
        namespace: namespace,
        // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
        // topic is deleted before the pod.
        annotations: if env == 'DEV' then {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        } else {},
        labels: {
          app: appName,
        },
      },
      spec: {
        topicRef: {
          name: config.topic_name,
        },
        ackDeadlineSeconds: 60,
        retryPolicy: {
          minimumBackoff: '5s',
          maximumBackoff: '60s',
        },
        // Retain messages for 1 hour in dev and 7 days in staging/prod.
        messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
        retainAckedMessages: false,
      },
    },
    // Give access to the subscription.
    {
      apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
      kind: 'IAMPolicy',
      metadata: {
        name: 'ri-bigquery-exporter-subscription-policy',
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        resourceRef: {
          kind: 'PubSubSubscription',
          name: config.subscription_name,
        },
        bindings: [
          {
            role: 'roles/pubsub.subscriber',
            members: [
              'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress,
            ],
          },
        ],
      },
    },
    // Give access to the partitioned BigQuery dataset. BigQuery doesn't support IAM, so this is
    // done with a Google Group membership instead. (See bigquery.jsonnet).
    {
      apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
      kind: 'CloudIdentityMembership',
      metadata: {
        name: '%s-ri-analytics-dataset-access-membership' % namespace,
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
      spec: {
        groupRef: {
          //  TODO(jacqueline): When we have more than one cluster in the US/EU this will have to
          //                    change to point to the group id instead of the kubernetes resource.
          //                    Keeping this as-is for now because I think it's clearer.
          name: datasetLib.cloudIdentityGroup,
          namespace: datasetLib.dataNamespace,
        },
        preferredMemberKey: {
          id: serviceAccount.serviceAccountGcpEmailAddress,
        },
        roles: [
          {
            name: 'MEMBER',
          },
        ],
      },
    },
  ];

  local container = {
    name: 'request-insight-bigquery-exporter',
    target: {
      name: '//services/request_insight/bigquery_exporter:image',
      dst: 'request_insight_bigquery_exporter',
    },
    env: [
      {
        name: 'POD_NAME',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.name',
          },
        },
      },
      {
        name: 'POD_NAMESPACE',
        valueFrom: {
          fieldRef: {
            fieldPath: 'metadata.namespace',
          },
        },
      },
    ] + dynamicFeatureFlags.env,
    volumeMounts: [
      configMap.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 0.5,
        memory: '1Gi',
      },
    },
    args: [
      '--health-file=/tmp/health',
    ],
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat /tmp/health',
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat /tmp/health',
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 20,
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    volumes: [
      configMap.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
    ],
  };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  // we allow multiple bigquery exporters on the same host as they run in the background
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

  local minReplicas = {
    DEV: 1,
    STAGING: 1,
    PROD: 1,
  };
  local maxReplicas = {
    DEV: 4,
    STAGING: 8,
    PROD: 8,
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: 0,
      replicas: minReplicas[env],
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };

  // Scale when the subscription backlog gets too large.
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: '%s-scaledobject' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      minReplicaCount: minReplicas[env],
      maxReplicaCount: maxReplicas[env],
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            metricName: 'pubsub_googleapis_com:subscription_num_undelivered_messages',
            threshold: '500',
            query: 'sum(max_over_time(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id="%s"}[30m]))' % config.subscription_name,
          },
        },
      ],
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    gcpObjects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    scaledObject,
  ])

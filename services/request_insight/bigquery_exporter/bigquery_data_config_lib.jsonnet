// This file contains static lists of keywords, file extensions, etc. that we are allowed to extract
// from sensitive Request Insight data and export to the analytics dataset.

local pathKeywords = [
  'test',
];

local instructionKeywords = [
  'comment',
  'document',
  'error',
  'explain',
  'fix',
  'imports',
  'improve',
  'log',
  'optimize',
  'refactor',
  'reformat',
  'simplify',
  'test',
  // We also keep track of quickfix instructions via a special case in the code, since they
  // have to start with "fix it:", as opposed to showing up anywhere in the instruction.
];

local chatKeywords = [
  'comment',
  'document',
  'error',
  'explain',
  'fix',
  'imports',
  'improve',
  'log',
  'optimize',
  'refactor',
  'reformat',
  'simplify',
  'test',
];

// Mapping from category name to keywords list. Used for extracting categories from user
// messages in chat and instruction requests.
local keywordCategories = {
  configure: [
    'config',
    'customize',
    'setup',
    'setting',
  ],
  document: [
    'annotate',
    'annotation',
    'comment',
    'docs',
    'note',
    'outline',
  ],
  explain: [
    'demo',
    'demonstrate',
    'describe',
    'detail',
    'discuss',
    'example',
    'how',
    'interpret',
    'learn',
    'practice',
    'summarize',
    'unpack',
    'why',
  ],
  fix: [
    'bug',
    'clarify',
    'correct',
    'debug',
    'error',
    'exception',
    'invalid',
    'mend',
    'remedy',
    'repair',
    'resolve',
  ],
  help: [
    'access',
    'aid',
    'advise',
    'answer',
    'assist',
    'coach',
    'criticize',
    'evaluate',
    'feedback',
    'guide',
    'problem',
    'suggest',
    'support',
    'teach',
    'troubleshoot',
  ],
  improve: [
    'advance',
    'enhance',
    'enrich',
    'expand',
    'polish',
    'strengthen',
  ],
  log: [
    'capture',
    'dump',
    'instrument',
    'report',
    'trace',
    'track',
  ],
  performance: [
    'boost',
    'efficiency',
    'latency',
    'optimize',
    'responsive',
    'scale',
    'speed',
    'throughput',
  ],
  refactor: [
    'deduplicate',
    'dedup',
    'encapsulate',
    'extract',
    'modular',
    'simplify',
    'modify',
  ],
  secure: [
    'auth',
    'confidential',
    'encrypt',
    'harden',
    'privacy',
    'prevent',
    'protect',
    'security',
    'sensitive',
    'vulnerability',
  ],
  test: [
    'validate',
    'verify',
  ],
  write: [
    'generate',
    'include',
    'create',
    'add',
  ],
};

// We can't record full paths because those are considered sensitive information, so we record
// allowlisted file extensions instead. This gives us finer granularity than just recording the
// language (e.g., we can distinguish between python and jupyter notebooks). Everything in this list
// is case-insensitive and should begin with ".".
local fileExtensions = [
  '.a',
  '.adb',
  '.ads',
  '.aj',
  '.asc',
  '.asm',
  '.asp',
  '.awk',
  '.bash',
  '.bat',
  '.bin',
  '.bison',
  '.c',
  '.capnp',
  '.cc',
  '.cfm',
  '.cfml',
  '.cgi',
  '.changelog',
  '.cl',
  '.clj',
  '.cmake',
  '.cmd',
  '.cob',
  '.coffee',
  '.cpp',
  '.cs',
  '.css',
  '.csv',
  '.cu',
  '.cuh',
  '.cxx',
  '.d',
  '.dart',
  '.diff',
  '.dll',
  '.dockerfile',
  '.dockerfile',
  '.e',
  '.el',
  '.elm',
  '.env',
  '.erb',
  '.erl',
  '.exe',
  '.f',
  '.f90',
  '.factor',
  '.for',
  '.fth',
  '.g4',
  '.gitattributes',
  '.gitignore',
  '.gitmodules',
  '.glsl',
  '.gml',
  '.go',
  '.gradle',
  '.groovy',
  '.gsp',
  '.gv',
  '.h',
  '.haml',
  '.handlebars',
  '.hbs',
  '.hpp',
  '.hs',
  '.htm',
  '.html',
  '.http',
  '.hxx',
  '.ini',
  '.ino',
  '.ipynb',
  '.j',
  '.jade',
  '.java',
  '.jinja',
  '.jq',
  '.js',
  '.json',
  '.json5',
  '.jsonld',
  '.jsonnet',
  '.jsx',
  '.kt',
  '.kts',
  '.l',
  '.less',
  '.license',
  '.lsp',
  '.lua',
  '.m',
  '.markdown',
  '.md',
  '.mk',
  '.ml',
  '.mm',
  '.mustache',
  '.ninja',
  '.nix',
  '.nu',
  '.numpy',
  '.o',
  '.pas',
  '.patch',
  '.php',
  '.phtml',
  '.pl',
  '.plot',
  '.pls',
  '.plsql',
  '.pm',
  '.ps1',
  '.psm1',
  '.py',
  '.r',
  '.r',
  '.rb',
  '.rkt',
  '.rmd',
  '.rs',
  '.s',
  '.sass',
  '.scala',
  '.scpt',
  '.scss',
  '.sh',
  '.so',
  '.sql',
  '.svelte',
  '.swift',
  '.tcl',
  '.tcsh',
  '.tmpl',
  '.toml',
  '.tpl',
  '.ts',
  '.tsx',
  '.txt',
  '.v',
  '.vb',
  '.vh',
  '.vhd',
  '.vhdl',
  '.vue',
  '.xml',
  '.yaml',
  '.yml',
  '.zsh',
];
// Make sure everything is lowercase.
assert std.all(
  std.map(function(ext) ext == std.asciiLower(ext), fileExtensions)
) : 'File extensions should be lowercase';

// List of command prefixes executed in terminal/process launch that are
// generic and we can export. We should not export anything besides these exact
// values, notable excluding any arguments or flags.
local terminalCommandPrefixes = [
  // Add trailing spaces to avoid matching longer commands.
  // TODO: There's probably a better way to do this
  'git pull ',
  'git fetch ',
  'git push ',
  'gt ',
  'gh ',
];

{
  pathKeywords: pathKeywords,
  instructionKeywords: instructionKeywords,
  chatKeywords: chatKeywords,
  keywordCategories: keywordCategories,
  fileExtensions: fileExtensions,
  terminalCommandPrefixes: terminalCommandPrefixes,
}

local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'request-insight-bigquery-exporter',
      kubecfg: {
        target: '//services/request_insight/bigquery_exporter:kubecfg',
        task: tenantNamespaces.namespaces + [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

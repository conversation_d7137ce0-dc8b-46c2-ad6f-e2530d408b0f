"""Module for the configuration for the request insight BigQuery exporter."""

import pathlib
from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class DataConfig:
    """Configuration for the data extracted by the BigQuery exporter."""

    path_keywords: list[str]
    """A list of keywords to extract from path names.

    This allows us to perform some analytics on path names even though they're considered sensitive
    information.
    """

    instruction_keywords: list[str]
    """A list of keywords to extract from instructions.

    This allows us to perform some analytics on instructions even though they're considered
    sensitive information.
    """

    chat_keywords: list[str]
    """A list of keywords to extract from customer chat messages.

    This allows us to perform some analytics on chat messages even though they're considered
    sensitive information.
    """

    keyword_categories: dict[str, list[str]]
    """A mapping where each key is a category of keywords (e.g. "configure") and each value
    is a list of keywords corresponding to that key (e.g. ["config", "customize", "setup",
    "setting"]). For each chat and instruction request, we search the user message and record
    each category for which at least one keyword is present.

    This allows us to perform some analytics on instructions even though they're considered
    sensitive information.
    """

    file_extensions: list[str]
    """A list of file extensions to extract from path names.

    This allows us to perform some analytics on file types even though full paths are considered
    sensitive information.
    """

    terminal_command_prefixes: list[str]
    """A list of command prefixes from terminal/process launch tools that we can export.

    We don't export any arguments or flags, only the exact values from this
    list. This allows us to perform some analytics on what kinds of tools users
    are using without exposing sensitive information.
    """


@dataclass_json
@dataclass
class Config:
    """Configuration for the request insight BigQuery exporter."""

    feature_flags_sdk_key_path: str
    """The path to the feature flags sdk key."""

    project_id: str
    """The project id of the pub/sub topic/subscription."""

    topic_name: str
    """The name of the pub/sub topic to listen to."""

    subscription_name: str
    """The name of the subscription to register, as it exists in GCP."""

    subscription_description: str
    """A description of the subscription, shared across tenants. Used for metrics."""

    subscription_batch_size: int
    """The batch size to use for fetching subscription messages."""

    bigquery_dataset_name: str
    """The name of the BigQuery dataset to write to."""

    namespace: str
    """The name of the namespace (recorded to BigQuery)."""

    data_config: DataConfig
    """Configuration for the data extracted by the BigQuery exporter."""


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )

load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "config",
    srcs = [
        "config.py",
    ],
    deps = [
        requirement("dataclasses_json"),
    ],
)

py_library(
    name = "bigquery_persistence",
    srcs = [
        "bigquery_persistence.py",
    ],
    deps = [
        requirement("prometheus-client"),
        ":config",
        "//base/logging:struct_logging",
        "//services/request_insight:request_insight_py_proto",
        requirement("google-cloud-bigquery"),
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "bigquery_persistence_test",
    srcs = ["bigquery_persistence_test.py"],
    deps = [
        ":bigquery_persistence",
    ],
)

py_binary(
    name = "bigquery_exporter",
    srcs = [
        "bigquery_exporter.py",
    ],
    deps = [
        ":bigquery_persistence",
        ":config",
        "//base/logging:struct_logging",
        "//base/python/signal_handler",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/lib:request_insight_subscriber",
        requirement("dataclasses_json"),
        requirement("prometheus-client"),
        "//base/feature_flags:feature_flags_py",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":bigquery_exporter",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        ":bigquery_data_config_lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:exporter_lib",
    ],
)

jsonnet_library(
    name = "bigquery_data_config_lib",
    srcs = [
        "bigquery_data_config_lib.jsonnet",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

package main

import (
	"reflect"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	tenantpb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestToTenantRow(t *testing.T) {
	t.Run("not deleted", func(t *testing.T) {
		row := toTenantRow(&tenantpb.Tenant{
			Id:             "test-id",
			Name:           "test-name",
			ShardNamespace: "test-namespace",
			Cloud:          "test-cloud",
		})
		expected := &TenantRow{
			ID:             "test-id",
			Name:           "test-name",
			ShardNamespace: "test-namespace",
			Cloud:          "test-cloud",
		}
		if !reflect.DeepEqual(row, expected) {
			t.<PERSON>rf("expected %v, got %v", expected, row)
		}
	})

	t.<PERSON>("deleted", func(t *testing.T) {
		row := toTenantRow(&tenantpb.Tenant{
			Id:             "test-id",
			Name:           "test-name",
			ShardNamespace: "test-namespace",
			Cloud:          "test-cloud",
			DeletedAt:      "2025-01-01T00:00:00Z",
		})
		expected := &TenantRow{
			ID:             "test-id",
			Name:           "test-name",
			ShardNamespace: "test-namespace",
			Cloud:          "test-cloud",
			DeletedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("invalid deleted_at", func(t *testing.T) {
		row := toTenantRow(&tenantpb.Tenant{
			Id:             "test-id",
			Name:           "test-name",
			ShardNamespace: "test-namespace",
			Cloud:          "test-cloud",
			DeletedAt:      "invalid",
		})
		expected := &TenantRow{
			ID:             "test-id",
			Name:           "test-name",
			ShardNamespace: "test-namespace",
			Cloud:          "test-cloud",
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}

		t.Run("with config and auth configuration", func(t *testing.T) {
			tenant := &tenantpb.Tenant{
				Id:             "test-id",
				Name:           "test-name",
				ShardNamespace: "test-namespace",
				Cloud:          "test-cloud",
				Tier:           tenantpb.TenantTier_ENTERPRISE,
				OtherNamespace: "other-namespace",
				Config: &tenantpb.Config{
					Configs: map[string]string{
						"support_access_control":      "true",
						"multi_tenant_allowed":        "true",
						"support_tenant":              "false",
						"default_support_tenant":      "false",
						"slackbot_allowlist_channels": `["channel1", "channel2"]`,
						"block_genie_request_access":  "true",
						"ip_allowlist":                "***********,********",
						"is_self_serve_team":          "true",
						"is_legacy_self_serve_team":   "false",
					},
				},
				AuthConfiguration: &tenantpb.AuthConfiguration{
					Domain:                   "example.com",
					UsernameDomains:          []string{"example.com", "test.com"},
					EmailAddressDomains:      []string{"example.com", "test.com"},
					AllowedIdentityProviders: []string{"google", "github"},
				},
			}

			row := toTenantRow(tenant)

			expected := &TenantRow{
				ID:             "test-id",
				Name:           "test-name",
				ShardNamespace: "test-namespace",
				Cloud:          "test-cloud",
				Tier: bigquery.NullString{
					Valid:     true,
					StringVal: "ENTERPRISE",
				},
				OtherNamespace: bigquery.NullString{
					Valid:     true,
					StringVal: "other-namespace",
				},
				SupportAccessControl: bigquery.NullBool{
					Valid: true,
					Bool:  true,
				},
				MultiTenantAllowed: bigquery.NullBool{
					Valid: true,
					Bool:  true,
				},
				SupportTenant: bigquery.NullBool{
					Valid: true,
					Bool:  false,
				},
				DefaultSupportTenant: bigquery.NullBool{
					Valid: true,
					Bool:  false,
				},
				SlackbotAllowlist: []string{"channel1", "channel2"},
				BlockGenieRequestAccess: bigquery.NullBool{
					Valid: true,
					Bool:  true,
				},
				IpAllowlist: bigquery.NullString{
					Valid:     true,
					StringVal: "***********,********",
				},
				IsSelfServeTeam: bigquery.NullBool{
					Valid: true,
					Bool:  true,
				},
				IsLegacySelfServeTeam: bigquery.NullBool{
					Valid: true,
					Bool:  false,
				},
				Domain: bigquery.NullString{
					Valid:     true,
					StringVal: "example.com",
				},
				UsernameDomains:          []string{"example.com", "test.com"},
				EmailAddressDomains:      []string{"example.com", "test.com"},
				AllowedIdentityProviders: []string{"google", "github"},
			}

			if !reflect.DeepEqual(row, expected) {
				t.Errorf("expected %v, got %v", expected, row)
			}
		})
	})
}

func TestToSubscriptionRow(t *testing.T) {
	t.Run("basic subscription", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toSubscriptionRow(&auth_entities.Subscription{
			SubscriptionId:    "sub_123",
			StripeCustomerId:  "cus_123",
			PriceId:           "price_professional",
			Status:            4, // ACTIVE
			Seats:             1,
			StartDate:         timestamppb.New(testTime),
			EndDate:           timestamppb.New(testTime),
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  true,
			Owner:             &auth_entities.Subscription_UserId{UserId: "user1"},
			CreatedAt:         timestamppb.New(testTime),
			UpdatedAt:         timestamppb.New(testTime),
		})
		expected := &SubscriptionRow{
			SubscriptionID:   "sub_123",
			StripeCustomerID: "cus_123",
			PriceID:          "price_professional",
			Status:           "ACTIVE",
			Seats:            1,
			StartDate: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			EndDate: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  true,
			UserID: bigquery.NullString{
				Valid:     true,
				StringVal: "user1",
			},
			CreatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			UpdatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			HasUnpaidInvoice: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			UsageBalanceDepleted: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("tenant subscription", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toSubscriptionRow(&auth_entities.Subscription{
			SubscriptionId:    "sub_456",
			StripeCustomerId:  "cus_456",
			PriceId:           "price_team",
			Status:            3, // TRIALING
			Seats:             5,
			StartDate:         timestamppb.New(testTime),
			TrialEnd:          timestamppb.New(testTime),
			EndDate:           timestamppb.New(testTime),
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  true,
			Owner:             &auth_entities.Subscription_TenantId{TenantId: "tenant1"},
			CreatedAt:         timestamppb.New(testTime),
			UpdatedAt:         timestamppb.New(testTime),
		})
		expected := &SubscriptionRow{
			SubscriptionID:   "sub_456",
			StripeCustomerID: "cus_456",
			PriceID:          "price_team",
			Status:           "TRIALING",
			Seats:            5,
			StartDate: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			TrialEnd: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			EndDate: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			CancelAtPeriodEnd: false,
			HasPaymentMethod:  true,
			TenantID: bigquery.NullString{
				Valid:     true,
				StringVal: "tenant1",
			},
			CreatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			UpdatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			HasUnpaidInvoice: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			UsageBalanceDepleted: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})
}

func TestToUserRow(t *testing.T) {
	t.Run("not in usa", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toUserRow(&auth_entities.User{
			Id:        "test-id",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
		})
		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_UNKNOWN",
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange:               &UserTierChangeRow{},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("in usa", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toUserRow(&auth_entities.User{
			Id:        "test-id",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
			InUsa:     timestamppb.New(testTime),
		})
		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_UNKNOWN",
			InUsa: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange:               &UserTierChangeRow{},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with subscription id", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		subID := "sub_test123"
		row := toUserRow(&auth_entities.User{
			Id:             "test-id",
			Email:          "<EMAIL>",
			Tenants:        []string{"test-tenant"},
			CreatedAt:      timestamppb.New(testTime),
			SubscriptionId: &subID,
		})
		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_UNKNOWN",
			SubscriptionID: bigquery.NullString{
				Valid:     true,
				StringVal: subID,
			},
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange:               &UserTierChangeRow{},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with stripe customer id", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toUserRow(&auth_entities.User{
			Id:               "test-id",
			Email:            "<EMAIL>",
			Tenants:          []string{"test-tenant"},
			CreatedAt:        timestamppb.New(testTime),
			StripeCustomerId: "cus_test123",
		})
		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_UNKNOWN",
			StripeCustomerID: bigquery.NullString{
				Valid:     true,
				StringVal: "cus_test123",
			},
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange:               &UserTierChangeRow{},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with orb customer id and billing method", func(t *testing.T) {
		row := toUserRow(&auth_entities.User{
			Id:                "test-id",
			Email:             "<EMAIL>",
			Tenants:           []string{"test-tenant"},
			CreatedAt:         timestamppb.New(time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)),
			OrbCustomerId:     "orb_test123",
			OrbSubscriptionId: "orb_sub_test123",
			BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
		})
		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_ORB",
			OrbCustomerID: bigquery.NullString{
				Valid:     true,
				StringVal: "orb_test123",
			},
			OrbSubscriptionID: bigquery.NullString{
				Valid:     true,
				StringVal: "orb_sub_test123",
			},
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange:               &UserTierChangeRow{},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with tier change", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		createdTime := time.Date(2025, 1, 1, 10, 0, 0, 0, time.UTC)
		updatedTime := time.Date(2025, 1, 1, 11, 0, 0, 0, time.UTC)

		row := toUserRow(&auth_entities.User{
			Id:        "test-id",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
			TierChange: &auth_entities.User_TierChangeInfo{
				Id:         "tier-change-123",
				TargetTier: auth_entities.UserTier_PROFESSIONAL,
				CreatedAt:  timestamppb.New(createdTime),
				UpdatedAt:  timestamppb.New(updatedTime),
			},
		})

		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_UNKNOWN",
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange: &UserTierChangeRow{
				ID:         "tier-change-123",
				TargetTier: "PROFESSIONAL",
				CreatedAt:  createdTime,
				UpdatedAt:  updatedTime,
			},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{},
		}

		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with tier change missing timestamps", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)

		row := toUserRow(&auth_entities.User{
			Id:        "test-id",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
			TierChange: &auth_entities.User_TierChangeInfo{
				Id:         "tier-change-123",
				TargetTier: auth_entities.UserTier_PROFESSIONAL,
				// No CreatedAt or UpdatedAt
			},
		})

		// Check that the timestamps are set to a non-zero value
		if row.TierChange == nil {
			t.Errorf("expected TierChange to be non-nil")
		} else {
			if row.TierChange.CreatedAt.IsZero() {
				t.Errorf("expected CreatedAt to be non-zero")
			}
			if row.TierChange.UpdatedAt.IsZero() {
				t.Errorf("expected UpdatedAt to be non-zero")
			}
			// CreatedAt and UpdatedAt should be the same when not provided
			if row.TierChange.CreatedAt != row.TierChange.UpdatedAt {
				t.Errorf("expected CreatedAt and UpdatedAt to be the same, got %v and %v",
					row.TierChange.CreatedAt, row.TierChange.UpdatedAt)
			}
		}
	})

	t.Run("with subscription creation info", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		createdTime := time.Date(2025, 1, 1, 10, 0, 0, 0, time.UTC)
		updatedTime := time.Date(2025, 1, 1, 11, 0, 0, 0, time.UTC)

		row := toUserRow(&auth_entities.User{
			Id:        "test-id",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Id:        "sub-creation-123",
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				CreatedAt: timestamppb.New(createdTime),
				UpdatedAt: timestamppb.New(updatedTime),
			},
		})

		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_UNKNOWN",
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange: &UserTierChangeRow{},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{
				ID:        "sub-creation-123",
				Status:    "PENDING",
				CreatedAt: createdTime,
				UpdatedAt: updatedTime,
			},
		}

		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with subscription creation info missing timestamps", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)

		row := toUserRow(&auth_entities.User{
			Id:        "test-id",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Id:     "sub-creation-123",
				Status: auth_entities.User_SubscriptionCreationInfo_SUCCESS,
				// No CreatedAt or UpdatedAt
			},
		})

		// Check that the timestamps are set to a non-zero value
		if row.SubscriptionCreationInfo == nil {
			t.Errorf("expected SubscriptionCreationInfo to be non-nil")
		} else {
			if row.SubscriptionCreationInfo.CreatedAt.IsZero() {
				t.Errorf("expected CreatedAt to be non-zero")
			}
			if row.SubscriptionCreationInfo.UpdatedAt.IsZero() {
				t.Errorf("expected UpdatedAt to be non-zero")
			}
			// CreatedAt and UpdatedAt should be the same when not provided
			if row.SubscriptionCreationInfo.CreatedAt != row.SubscriptionCreationInfo.UpdatedAt {
				t.Errorf("expected CreatedAt and UpdatedAt to be the same, got %v and %v",
					row.SubscriptionCreationInfo.CreatedAt, row.SubscriptionCreationInfo.UpdatedAt)
			}
		}
	})

	t.Run("with suspensions", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		suspensionTime := time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC)

		row := toUserRow(&auth_entities.User{
			Id:        "test-id",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
			Suspensions: []*auth_entities.UserSuspension{
				{
					SuspensionId:   "suspension-123",
					CreatedTime:    timestamppb.New(suspensionTime),
					SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE,
					Evidence:       "test evidence",
				},
			},
		})

		expected := &UserRow{
			ID:            "test-id",
			Email:         "<EMAIL>",
			TenantIDs:     []string{"test-tenant"},
			CreatedAt:     time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC),
			BillingMethod: "BILLING_METHOD_UNKNOWN",
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			Suspensions: []UserSuspensionRow{
				{
					SuspensionID:   "suspension-123",
					CreatedTime:    suspensionTime,
					SuspensionType: "USER_SUSPENSION_TYPE_API_ABUSE",
					Evidence:       "test evidence",
				},
			},
			TierChange:               &UserTierChangeRow{},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{},
		}

		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with all fields", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		createdTime := time.Date(2025, 1, 1, 10, 0, 0, 0, time.UTC)
		updatedTime := time.Date(2025, 1, 1, 11, 0, 0, 0, time.UTC)
		suspensionTime := time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC)
		subID := "sub_test123"

		row := toUserRow(&auth_entities.User{
			Id:                     "test-id",
			Email:                  "<EMAIL>",
			Tenants:                []string{"test-tenant"},
			CreatedAt:              timestamppb.New(testTime),
			InUsa:                  timestamppb.New(testTime),
			StripeCustomerId:       "cus_test123",
			SubscriptionId:         &subID,
			Blocked:                true,
			IdpUserIds:             []string{"idp-user-1", "idp-user-2"},
			OrbCustomerId:          "orb_test123",
			OrbSubscriptionId:      "orb_sub_test123",
			BillingMethod:          auth_entities.BillingMethod_BILLING_METHOD_ORB,
			SubscriptionCreationId: "sub-creation-legacy-id",
			TierChange: &auth_entities.User_TierChangeInfo{
				Id:         "tier-change-123",
				TargetTier: auth_entities.UserTier_PROFESSIONAL,
				CreatedAt:  timestamppb.New(createdTime),
				UpdatedAt:  timestamppb.New(updatedTime),
			},
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{
				Id:        "sub-creation-123",
				Status:    auth_entities.User_SubscriptionCreationInfo_PENDING,
				CreatedAt: timestamppb.New(createdTime),
				UpdatedAt: timestamppb.New(updatedTime),
			},
			Suspensions: []*auth_entities.UserSuspension{
				{
					SuspensionId:   "suspension-123",
					CreatedTime:    timestamppb.New(suspensionTime),
					SuspensionType: auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE,
					Evidence:       "test evidence",
				},
			},
			SuspensionExempt: true,
		})

		expected := &UserRow{
			ID:        "test-id",
			Email:     "<EMAIL>",
			TenantIDs: []string{"test-tenant"},
			CreatedAt: testTime,
			InUsa: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			StripeCustomerID: bigquery.NullString{
				Valid:     true,
				StringVal: "cus_test123",
			},
			SubscriptionID: bigquery.NullString{
				Valid:     true,
				StringVal: subID,
			},
			Blocked:    true,
			IDPUserIDs: []string{"idp-user-1", "idp-user-2"},
			OrbCustomerID: bigquery.NullString{
				Valid:     true,
				StringVal: "orb_test123",
			},
			OrbSubscriptionID: bigquery.NullString{
				Valid:     true,
				StringVal: "orb_sub_test123",
			},
			BillingMethod: "BILLING_METHOD_ORB",
			SubscriptionCreationID: bigquery.NullString{
				Valid:     true,
				StringVal: "sub-creation-legacy-id",
			},
			CheckSubscriptionStatus: bigquery.NullBool{
				Valid: true,
				Bool:  false,
			},
			TierChange: &UserTierChangeRow{
				ID:         "tier-change-123",
				TargetTier: "PROFESSIONAL",
				CreatedAt:  createdTime,
				UpdatedAt:  updatedTime,
			},
			SubscriptionCreationInfo: &UserSubscriptionCreationInfoRow{
				ID:        "sub-creation-123",
				Status:    "PENDING",
				CreatedAt: createdTime,
				UpdatedAt: updatedTime,
			},
			Suspensions: []UserSuspensionRow{
				{
					SuspensionID:   "suspension-123",
					CreatedTime:    suspensionTime,
					SuspensionType: "USER_SUSPENSION_TYPE_API_ABUSE",
					Evidence:       "test evidence",
				},
			},
			SuspensionExempt: true,
		}

		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with nil TierChange", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toUserRow(&auth_entities.User{
			Id:         "test-id-nil-tierchange",
			Email:      "<EMAIL>",
			Tenants:    []string{"test-tenant"},
			CreatedAt:  timestamppb.New(testTime),
			TierChange: nil, // Explicitly nil
			SubscriptionCreationInfo: &auth_entities.User_SubscriptionCreationInfo{ // Keep this non-nil to isolate the test
				Id:     "sub-creation-123",
				Status: auth_entities.User_SubscriptionCreationInfo_SUCCESS,
			},
		})

		expectedTierChange := &UserTierChangeRow{}
		if !reflect.DeepEqual(row.TierChange, expectedTierChange) {
			t.Errorf("expected TierChange to be %v, got %v", expectedTierChange, row.TierChange)
		}
		// Check other essential fields to ensure they are not unexpectedly changed
		if row.ID != "test-id-nil-tierchange" {
			t.Errorf("expected ID to be 'test-id-nil-tierchange', got %s", row.ID)
		}
		if row.SubscriptionCreationInfo == nil || row.SubscriptionCreationInfo.ID != "sub-creation-123" {
			t.Errorf("expected SubscriptionCreationInfo to be non-nil and have ID 'sub-creation-123', got %v", row.SubscriptionCreationInfo)
		}
	})

	t.Run("with nil SubscriptionCreationInfo", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toUserRow(&auth_entities.User{
			Id:        "test-id-nil-subcreation",
			Email:     "<EMAIL>",
			Tenants:   []string{"test-tenant"},
			CreatedAt: timestamppb.New(testTime),
			TierChange: &auth_entities.User_TierChangeInfo{ // Keep this non-nil to isolate the test
				Id:         "tier-change-123",
				TargetTier: auth_entities.UserTier_PROFESSIONAL,
			},
			SubscriptionCreationInfo: nil, // Explicitly nil
		})

		expectedSubCreationInfo := &UserSubscriptionCreationInfoRow{}
		if !reflect.DeepEqual(row.SubscriptionCreationInfo, expectedSubCreationInfo) {
			t.Errorf("expected SubscriptionCreationInfo to be %v, got %v", expectedSubCreationInfo, row.SubscriptionCreationInfo)
		}
		// Check other essential fields
		if row.ID != "test-id-nil-subcreation" {
			t.Errorf("expected ID to be 'test-id-nil-subcreation', got %s", row.ID)
		}
		if row.TierChange == nil || row.TierChange.ID != "tier-change-123" {
			t.Errorf("expected TierChange to be non-nil and have ID 'tier-change-123', got %v", row.TierChange)
		}
	})
}

func TestToTenantSubscriptionMappingRow(t *testing.T) {
	t.Run("basic mapping", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		row := toTenantSubscriptionMappingRow(&auth_entities.TenantSubscriptionMapping{
			TenantId:             "tenant_123",
			StripeSubscriptionId: "sub_stripe_123",
			StripeCustomerId:     "cus_stripe_123",
			OrbCustomerId:        "orb_cus_123",
			OrbSubscriptionId:    "orb_sub_123",
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_ORB,
			CreatedAt:            timestamppb.New(testTime),
		})
		expected := &TenantSubscriptionMappingRow{
			TenantID: "tenant_123",
			StripeSubscriptionID: bigquery.NullString{
				Valid:     true,
				StringVal: "sub_stripe_123",
			},
			StripeCustomerID: bigquery.NullString{
				Valid:     true,
				StringVal: "cus_stripe_123",
			},
			OrbCustomerID: bigquery.NullString{
				Valid:     true,
				StringVal: "orb_cus_123",
			},
			OrbSubscriptionID: bigquery.NullString{
				Valid:     true,
				StringVal: "orb_sub_123",
			},
			BillingMethod: bigquery.NullString{
				Valid:     true,
				StringVal: "BILLING_METHOD_ORB",
			},
			CreatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("with plan change", func(t *testing.T) {
		testTime := time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
		planChangeTime := time.Date(2025, 1, 2, 0, 0, 0, 0, time.UTC)
		row := toTenantSubscriptionMappingRow(&auth_entities.TenantSubscriptionMapping{
			TenantId:             "tenant_456",
			StripeSubscriptionId: "sub_stripe_456",
			StripeCustomerId:     "cus_stripe_456",
			BillingMethod:        auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
			CreatedAt:            timestamppb.New(testTime),
			PlanChange: &auth_entities.TenantSubscriptionMapping_PlanChangeInfo{
				Id:              "plan_change_123",
				TargetOrbPlanId: "orb_plan_456",
				CreatedAt:       timestamppb.New(planChangeTime),
				UpdatedAt:       timestamppb.New(planChangeTime),
			},
		})
		expected := &TenantSubscriptionMappingRow{
			TenantID: "tenant_456",
			StripeSubscriptionID: bigquery.NullString{
				Valid:     true,
				StringVal: "sub_stripe_456",
			},
			StripeCustomerID: bigquery.NullString{
				Valid:     true,
				StringVal: "cus_stripe_456",
			},
			BillingMethod: bigquery.NullString{
				Valid:     true,
				StringVal: "BILLING_METHOD_STRIPE",
			},
			CreatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: testTime,
			},
			PlanChangeID: bigquery.NullString{
				Valid:     true,
				StringVal: "plan_change_123",
			},
			PlanChangeTargetOrbPlanID: bigquery.NullString{
				Valid:     true,
				StringVal: "orb_plan_456",
			},
			PlanChangeCreatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: planChangeTime,
			},
			PlanChangeUpdatedAt: bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: planChangeTime,
			},
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})

	t.Run("minimal mapping", func(t *testing.T) {
		row := toTenantSubscriptionMappingRow(&auth_entities.TenantSubscriptionMapping{
			TenantId:      "tenant_minimal",
			BillingMethod: auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
		})
		expected := &TenantSubscriptionMappingRow{
			TenantID: "tenant_minimal",
		}
		if !reflect.DeepEqual(row, expected) {
			t.Errorf("expected %v, got %v", expected, row)
		}
	})
}

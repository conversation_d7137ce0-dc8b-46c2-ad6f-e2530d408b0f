package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"

	"github.com/augmentcode/augment/base/logging"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	teammanagementclient "github.com/augmentcode/augment/services/auth/central/team_management_client"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
)

type Config struct {
	PromPort              int
	ClientMtls            *tlsconfig.ClientConfig
	Namespace             string
	ProjectId             string
	DatasetName           string
	TempDatasetName       string
	AuthEndpoint          string
	TenantWatcherEndpoint string
	TokenExchangeEndpoint string
	PushgatewayURL        string
}

// Load configuration from the given file.
func loadConfig(configFile string) (*Config, error) {
	var config Config
	if configFile == "" {
		return nil, fmt.Errorf("missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		return nil, err
	}

	log.Info().Msgf("Config: %v", config)
	return &config, nil
}

func main() {
	logging.SetupServerLogging()
	ctx := context.Background()

	// Parse flags.
	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()

	// Load config.
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error loading config")
	}

	// Note: We don't start a metrics server here because we push metrics to Pushgateway
	// instead of exposing them for scraping

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Set up clients needed for the sync
	tenantWatcherClient := tenantwatcherclient.New(
		config.TenantWatcherEndpoint, grpc.WithTransportCredentials(clientCreds))
	defer tenantWatcherClient.Close()

	authClient, err := authclient.New(config.AuthEndpoint, grpc.WithTransportCredentials(clientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating auth client")
	}
	defer authClient.Close()

	teamManagementClient, err := teammanagementclient.NewTeamManagementClient(config.AuthEndpoint, grpc.WithTransportCredentials(clientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating team management client")
	}
	defer teamManagementClient.Close()

	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(clientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
	}
	defer tokenExchangeClient.Close()

	// Run the job.
	syncJob, err := NewSyncJob(
		ctx, config.ProjectId, config.DatasetName, config.TempDatasetName, tenantWatcherClient,
		authClient, teamManagementClient, tokenExchangeClient,
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating sync job")
	}
	err = syncJob.Run(ctx)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error running sync job")
	}

	log.Info().Msg("Sync complete")

	// Push metrics to Prometheus Pushgateway so they persist after the job completes
	if err := PushMetrics(config.PushgatewayURL, config.Namespace); err != nil {
		log.Warn().Err(err).Msg("Failed to push metrics to Pushgateway")
	} else {
		log.Info().Msg("Successfully pushed metrics to Pushgateway")
	}
}

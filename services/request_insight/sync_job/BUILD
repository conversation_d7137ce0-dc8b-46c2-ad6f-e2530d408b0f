load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "sync_lib",
    srcs = [
        "main.go",
        "metrics.go",
        "sync.go",
        "verification.go",
    ],
    importpath = "github.com/augmentcode/augment/services/request_insight/sync",
    visibility = ["//visibility:private"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/logging:logging_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/client:team_management_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_googleapis_gax_go_v2//:gax-go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/push",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@org_golang_google_api//iterator",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_binary(
    name = "sync",
    embed = [":sync_lib"],
)

go_test(
    name = "sync_test",
    srcs = [
        "sync_test.go",
        "verification_test.go",
    ],
    data = [
        "//services/request_insight/analytics_dataset:schema.json",
    ],
    embed = [":sync_lib"],
    deps = [
        "//base/test_utils/bigquery:emulator_go",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":sync",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight/support_database:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
    ],
)

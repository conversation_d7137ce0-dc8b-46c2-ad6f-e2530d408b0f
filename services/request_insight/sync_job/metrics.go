package main

import (
	"fmt"
	"os"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/prometheus/client_golang/prometheus/push"
	"github.com/rs/zerolog/log"
)

var (
	// DuplicateEmailsCount tracks data quality issues where multiple users have the same normalized email.
	// This metric counts distinct normalized emails (lowercase, trimmed) that appear for more than one user.
	// Non-zero values indicate potential data corruption or account duplication issues that need investigation.
	DuplicateEmailsCount = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "au_request_insight_duplicate_emails_count",
			Help: "Number of distinct normalized emails shared by multiple users (data quality issue)",
		},
	)

	// MultipleOrbSubscriptionsCount tracks billing anomalies where users have multiple active Orb subscriptions.
	// This metric counts users who have more than one active Orb subscription, which should not happen
	// under normal circumstances and may indicate billing system issues or data corruption.
	MultipleOrbSubscriptionsCount = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "au_request_insight_multiple_orb_subscriptions_count",
			Help: "Number of users with multiple active Orb subscriptions (billing anomaly)",
		},
	)

	// UserSubscriptionInconsistenciesCount tracks data consistency issues between user and subscription tables.
	// This metric counts users with Orb billing where the user's orb_subscription_id doesn't match any
	// subscription record, or where the subscription's user_id/orb_customer_id doesn't match the user.
	UserSubscriptionInconsistenciesCount = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "au_request_insight_user_subscription_inconsistencies_count",
			Help: "Number of users with mismatched subscription references (data consistency issue)",
		},
	)

	// OrphanedActiveOrbSubscriptionsCount tracks active Orb subscriptions that are not properly linked to users.
	// This metric counts active Orb subscriptions where the subscription's ID doesn't match the user's
	// current orb_subscription_id, indicating potential billing or data sync issues.
	OrphanedActiveOrbSubscriptionsCount = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "au_request_insight_orphaned_active_orb_subscriptions_count",
			Help: "Number of active Orb subscriptions not referenced by their associated users (orphaned subscriptions)",
		},
	)

	// ProfessionalTenantsWithCommunitySubscriptionsCount tracks users in professional tenants who have active community tier subscriptions.
	// This is an inconsistency as non-team users should not be in professional tenants with community tier subscriptions.
	ProfessionalTenantsWithCommunitySubscriptionsCount = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "au_request_insight_professional_tenants_with_community_subscriptions_count",
			Help: "Number of non-team users in professional tenants with active community tier subscriptions (tier inconsistency)",
		},
	)

	// CommunityTenantsWithProfessionalSubscriptionsCount tracks users in community tenants who have active professional plan subscriptions.
	// This is an inconsistency as non-team users should not be in community tenants with professional plan subscriptions.
	CommunityTenantsWithProfessionalSubscriptionsCount = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "au_request_insight_community_tenants_with_professional_subscriptions_count",
			Help: "Number of non-team users in community tenants with active professional plan subscriptions (tier inconsistency)",
		},
	)
)

// PushMetrics pushes the current metrics to Prometheus Pushgateway
// This ensures the metrics persist after the job completes
func PushMetrics(pushgatewayURL, namespace string) error {
	// Create a job name that includes the namespace for uniqueness
	jobName := fmt.Sprintf("request-insight-sync-%s", namespace)

	// Get the pod name for additional labeling
	podName := os.Getenv("POD_NAME")
	if podName == "" {
		podName = "unknown"
	}

	log.Info().
		Str("pushgateway_url", pushgatewayURL).
		Str("job_name", jobName).
		Str("pod_name", podName).
		Msg("Pushing metrics to Pushgateway")

	// Create a pusher with job name and additional labels
	// Only group by namespace so each run overwrites the previous metrics
	pusher := push.New(pushgatewayURL, jobName).
		Collector(DuplicateEmailsCount).
		Collector(MultipleOrbSubscriptionsCount).
		Collector(UserSubscriptionInconsistenciesCount).
		Collector(OrphanedActiveOrbSubscriptionsCount).
		Collector(ProfessionalTenantsWithCommunitySubscriptionsCount).
		Collector(CommunityTenantsWithProfessionalSubscriptionsCount).
		Grouping("namespace", namespace)

	// Push the metrics
	if err := pusher.Push(); err != nil {
		return fmt.Errorf("failed to push metrics to Pushgateway: %w", err)
	}

	return nil
}

package main

import (
	"context"
	"os"
	"strings"
	"testing"

	bqemulator "github.com/augmentcode/augment/base/test_utils/bigquery/emulator"
)

const (
	testProjectID   = "test-project"
	testDatasetName = "test_dataset"
)

var bqEmulator *bqemulator.BigQueryEmulator = nil

// TestMain sets up the BigQuery emulator for all tests
func TestMain(m *testing.M) {
	var err error
	bqEmulator, err = bqemulator.New(context.Background(), testProjectID, testDatasetName)
	if err != nil {
		panic(err)
	}
	defer bqEmulator.Close()

	// Load the actual schema from the analytics dataset
	err = bqEmulator.LoadJsonSchemaFile(context.Background(), "../analytics_dataset/schema.json")
	if err != nil {
		panic(err)
	}

	// Run the tests
	os.Exit(m.Run())
}

func TestCheckDuplicateEmails(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// Insert test data with duplicate normalized emails
	insertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (id, email, tenant_ids, created_at, billing_method, check_subscription_status)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_UNKNOWN', false),
			('user2', ' <EMAIL> ', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_UNKNOWN', false),
			('user3', '<EMAIL>', ['tenant2'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_UNKNOWN', false),
			('user4', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_UNKNOWN', false)
	`)
	_, err := insertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert test data: %v", err)
	}

	// Run the verification - this should detect the duplicate emails
	err = syncJob.checkDuplicateEmails(ctx)
	if err == nil {
		t.Error("Expected checkDuplicateEmails to return an error for duplicate emails, but it didn't")
	}

	// Verify the error message contains expected information
	if err != nil {
		expectedErrorMsg := "duplicate normalized emails found: found 1 issues"
		if err.Error() != expectedErrorMsg {
			t.Errorf("Expected error message '%s', but got '%s'", expectedErrorMsg, err.Error())
		}
		t.Logf("Got expected error: %v", err)
	}

	// Clean up for next test
	cleanupQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up test data: %v", err)
	}
}

func TestCheckMultipleOrbSubscriptions(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// First insert users that will be referenced by subscriptions
	userInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (
			id, email, tenant_ids, created_at, billing_method,
			check_subscription_status, tier_change
		)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, NULL),
			('user2', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, NULL),
			('user3', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_STRIPE', false, NULL)
	`)
	_, err := userInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert user test data: %v", err)
	}

	// Insert test data with multiple active Orb subscriptions for the same user
	insertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.subscription (
			subscription_id, stripe_customer_id, price_id, status, seats,
			cancel_at_period_end, has_payment_method, user_id, billing_method, orb_status, updated_at
		)
		VALUES
			('sub1', 'cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			('sub2', 'cus2', 'price2', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			('sub3', 'cus3', 'price3', 'ACTIVE', 1, false, true, 'user2', 'BILLING_METHOD_ORB', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			('sub4', 'cus4', 'price4', 'ACTIVE', 1, false, true, 'user3', 'BILLING_METHOD_STRIPE', 'ORB_STATUS_UNKNOWN', TIMESTAMP('2023-01-01 00:00:00'))
	`)
	_, err = insertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert test data: %v", err)
	}

	// Run the verification - this should detect multiple Orb subscriptions for user1
	err = syncJob.checkMultipleOrbSubscriptions(ctx)
	if err == nil {
		t.Error("Expected checkMultipleOrbSubscriptions to return an error for multiple Orb subscriptions, but it didn't")
	}

	// Verify the error message contains expected information
	if err != nil {
		expectedErrorMsg := "users with multiple active Orb subscriptions found: found 1 issues"
		if err.Error() != expectedErrorMsg {
			t.Errorf("Expected error message '%s', but got '%s'", expectedErrorMsg, err.Error())
		}
		t.Logf("Got expected error: %v", err)
	}

	// Clean up for next test
	cleanupSubQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE")
	_, err = cleanupSubQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up subscription test data: %v", err)
	}

	cleanupUserQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupUserQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up user test data: %v", err)
	}
}

func TestCheckUserSubscriptionConsistency(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// Insert test data with user-subscription inconsistencies
	// First insert users with Orb billing method and subscription IDs
	userInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (
			id, email, tenant_ids, created_at, billing_method,
			check_subscription_status, orb_subscription_id, orb_customer_id
		)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1'),
			('user2', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub2', 'orb_cus2'),
			('user3', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub3', 'orb_cus3')
	`)
	_, err := userInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert user test data: %v", err)
	}

	// Insert subscriptions that create inconsistencies
	subscriptionInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.subscription (
			subscription_id, stripe_customer_id, price_id, status, seats,
			cancel_at_period_end, has_payment_method, user_id, billing_method,
			orb_customer_id, orb_status, updated_at
		)
		VALUES
			-- This subscription matches user1 correctly
			('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- This subscription has wrong user_id (should be user2 but is user1)
			('orb_sub2', 'stripe_cus2', 'price2', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus2', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- user3's subscription is missing (orb_sub3 doesn't exist)
			-- This creates an inconsistency where user3.orb_subscription_id points to non-existent subscription
			('orb_sub4', 'stripe_cus4', 'price4', 'ACTIVE', 1, false, true, 'user4', 'BILLING_METHOD_ORB', 'orb_cus4', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'))
	`)
	_, err = subscriptionInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert subscription test data: %v", err)
	}

	// Run the verification - this should detect the inconsistencies
	err = syncJob.checkUserSubscriptionConsistency(ctx)
	if err == nil {
		t.Error("Expected checkUserSubscriptionConsistency to return an error for inconsistencies, but it didn't")
	}

	// Verify the error message contains expected information
	if err != nil {
		expectedErrorMsg := "user-subscription inconsistencies found: found 2 issues"
		if err.Error() != expectedErrorMsg {
			t.Errorf("Expected error message '%s', but got '%s'", expectedErrorMsg, err.Error())
		}
		t.Logf("Got expected error: %v", err)
	}

	// Clean up for next test
	cleanupUserQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupUserQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up user test data: %v", err)
	}

	cleanupSubQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE")
	_, err = cleanupSubQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up subscription test data: %v", err)
	}
}

func TestCheckOrphanedActiveOrbSubscriptions(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// Insert test data with orphaned active Orb subscriptions
	// First insert users with specific orb_subscription_id values
	userInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (
			id, email, tenant_ids, created_at, billing_method,
			check_subscription_status, orb_subscription_id, orb_customer_id
		)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1'),
			('user2', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub2', 'orb_cus2'),
			('user3', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_STRIPE', false, NULL, NULL)
	`)
	_, err := userInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert user test data: %v", err)
	}

	// Insert subscriptions where some are orphaned (not referenced by any user's orb_subscription_id)
	subscriptionInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.subscription (
			subscription_id, stripe_customer_id, price_id, status, seats,
			cancel_at_period_end, has_payment_method, user_id, billing_method,
			orb_customer_id, orb_status, updated_at
		)
		VALUES
			-- This subscription matches user1's orb_subscription_id correctly
			('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- This subscription matches user2's orb_subscription_id correctly
			('orb_sub2', 'stripe_cus2', 'price2', 'ACTIVE', 1, false, true, 'user2', 'BILLING_METHOD_ORB', 'orb_cus2', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- This is an orphaned active Orb subscription - user1 has it but user1's orb_subscription_id is orb_sub1, not orb_sub3
			('orb_sub3', 'stripe_cus3', 'price3', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus3', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- This is another orphaned active Orb subscription - user2 has it but user2's orb_subscription_id is orb_sub2, not orb_sub4
			('orb_sub4', 'stripe_cus4', 'price4', 'ACTIVE', 1, false, true, 'user2', 'BILLING_METHOD_ORB', 'orb_cus4', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- This is a non-active Orb subscription, should not be flagged as orphaned
			('orb_sub5', 'stripe_cus5', 'price5', 'ACTIVE', 1, false, true, 'user3', 'BILLING_METHOD_ORB', 'orb_cus5', 'ORB_STATUS_ENDED', TIMESTAMP('2023-01-01 00:00:00'))
	`)
	_, err = subscriptionInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert subscription test data: %v", err)
	}

	// Run the verification - this should detect the orphaned active Orb subscriptions
	err = syncJob.checkOrphanedActiveOrbSubscriptions(ctx)
	if err == nil {
		t.Error("Expected checkOrphanedActiveOrbSubscriptions to return an error for orphaned subscriptions, but it didn't")
	}

	// Verify the error message contains expected information
	if err != nil {
		expectedErrorMsg := "orphaned active Orb subscriptions found: found 2 issues"
		if err.Error() != expectedErrorMsg {
			t.Errorf("Expected error message '%s', but got '%s'", expectedErrorMsg, err.Error())
		}
		t.Logf("Got expected error: %v", err)
	}

	// Clean up for next test
	cleanupUserQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupUserQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up user test data: %v", err)
	}

	cleanupSubQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE")
	_, err = cleanupSubQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up subscription test data: %v", err)
	}
}

func TestCheckOrphanedActiveOrbSubscriptionsNoIssues(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// Insert test data with no orphaned active Orb subscriptions
	userInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (
			id, email, tenant_ids, created_at, billing_method,
			check_subscription_status, orb_subscription_id, orb_customer_id
		)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1'),
			('user2', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub2', 'orb_cus2'),
			('user3', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_STRIPE', false, NULL, NULL)
	`)
	_, err := userInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert user test data: %v", err)
	}

	// Insert subscriptions where all active Orb subscriptions are properly referenced
	subscriptionInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.subscription (
			subscription_id, stripe_customer_id, price_id, status, seats,
			cancel_at_period_end, has_payment_method, user_id, billing_method,
			orb_customer_id, orb_status, updated_at
		)
		VALUES
			-- This subscription matches user1's orb_subscription_id correctly
			('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- This subscription matches user2's orb_subscription_id correctly
			('orb_sub2', 'stripe_cus2', 'price2', 'ACTIVE', 1, false, true, 'user2', 'BILLING_METHOD_ORB', 'orb_cus2', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00')),
			-- This is a non-active Orb subscription, should not be flagged as orphaned
			('orb_sub3', 'stripe_cus3', 'price3', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus3', 'ORB_STATUS_ENDED', TIMESTAMP('2023-01-01 00:00:00')),
			-- This is a Stripe subscription, should not be flagged
			('stripe_sub1', 'stripe_cus4', 'price4', 'ACTIVE', 1, false, true, 'user3', 'BILLING_METHOD_STRIPE', NULL, NULL, TIMESTAMP('2023-01-01 00:00:00'))
	`)
	_, err = subscriptionInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert subscription test data: %v", err)
	}

	// Run the verification - this should pass with no orphaned subscriptions
	err = syncJob.checkOrphanedActiveOrbSubscriptions(ctx)
	if err != nil {
		t.Errorf("Expected checkOrphanedActiveOrbSubscriptions to pass with no orphaned subscriptions, but got error: %v", err)
	}

	// Clean up for next test
	cleanupUserQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupUserQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up user test data: %v", err)
	}

	cleanupSubQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE")
	_, err = cleanupSubQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up subscription test data: %v", err)
	}
}

func TestVerificationPassesWithCleanData(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// Insert clean test data with no issues
	userInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (
			id, email, tenant_ids, created_at, billing_method,
			check_subscription_status, orb_subscription_id, orb_customer_id
		)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1'),
			('user2', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_STRIPE', false, NULL, NULL),
			('user3', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_UNKNOWN', false, NULL, NULL)
	`)
	_, err := userInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert user test data: %v", err)
	}

	// Insert matching subscriptions with no inconsistencies
	subscriptionInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.subscription (
			subscription_id, stripe_customer_id, price_id, status, seats,
			cancel_at_period_end, has_payment_method, user_id, billing_method,
			orb_customer_id, orb_status, updated_at, external_plan_id
		)
		VALUES
			-- This subscription matches user1 correctly
			('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_developer_plan'),
			-- This is a Stripe subscription for user2
			('stripe_sub1', 'stripe_cus2', 'price2', 'ACTIVE', 1, false, true, 'user2', 'BILLING_METHOD_STRIPE', NULL, NULL, TIMESTAMP('2023-01-01 00:00:00'), NULL)
	`)
	_, err = subscriptionInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert subscription test data: %v", err)
	}

	// Insert tenant data
	tenantInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.tenant (
			id, name, shard_namespace, cloud, tier, is_self_serve_team, is_legacy_self_serve_team
		)
		VALUES
			('tenant1', 'Test Tenant', 'test-shard', 'test-cloud', 'PROFESSIONAL', true, false)
	`)
	_, err = tenantInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert tenant test data: %v", err)
	}

	// Test each verification individually - they should all pass
	err = syncJob.checkDuplicateEmails(ctx)
	if err != nil {
		t.Errorf("Expected checkDuplicateEmails to pass with clean data, but got error: %v", err)
	}

	err = syncJob.checkMultipleOrbSubscriptions(ctx)
	if err != nil {
		t.Errorf("Expected checkMultipleOrbSubscriptions to pass with clean data, but got error: %v", err)
	}

	err = syncJob.checkUserSubscriptionConsistency(ctx)
	if err != nil {
		t.Errorf("Expected checkUserSubscriptionConsistency to pass with clean data, but got error: %v", err)
	}

	err = syncJob.checkOrphanedActiveOrbSubscriptions(ctx)
	if err != nil {
		t.Errorf("Expected checkOrphanedActiveOrbSubscriptions to pass with clean data, but got error: %v", err)
	}

	err = syncJob.checkProfessionalTenantsWithCommunitySubscriptions(ctx)
	if err != nil {
		t.Errorf("Expected checkProfessionalTenantsWithCommunitySubscriptions to pass with clean data, but got error: %v", err)
	}

	err = syncJob.checkCommunityTenantsWithProfessionalSubscriptions(ctx)
	if err != nil {
		t.Errorf("Expected checkCommunityTenantsWithProfessionalSubscriptions to pass with clean data, but got error: %v", err)
	}

	// Clean up
	cleanupUserQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupUserQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up user test data: %v", err)
	}

	cleanupSubQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE")
	_, err = cleanupSubQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up subscription test data: %v", err)
	}

	cleanupTenantQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".tenant WHERE TRUE")
	_, err = cleanupTenantQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up tenant test data: %v", err)
	}
}

func TestCheckProfessionalTenantsWithCommunitySubscriptions(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// Insert test data with a non-team user in a discovery tenant with a community subscription
	userInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (
			id, email, tenant_ids, created_at, billing_method,
			check_subscription_status, orb_subscription_id, orb_customer_id
		)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1')
	`)
	_, err := userInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert user test data: %v", err)
	}

	// Insert subscription with community plan
	subscriptionInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.subscription (
			subscription_id, stripe_customer_id, price_id, status, seats,
			cancel_at_period_end, has_payment_method, user_id, billing_method,
			orb_customer_id, orb_status, updated_at, external_plan_id
		)
		VALUES
			('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_community_plan')
	`)
	_, err = subscriptionInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert subscription test data: %v", err)
	}

	// Insert tenant data - professional tenant (PROFESSIONAL tier) that is not a team
	tenantInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.tenant (
			id, name, shard_namespace, cloud, tier, is_self_serve_team, is_legacy_self_serve_team
		)
		VALUES
			('tenant1', 'Test Tenant', 'test-shard', 'test-cloud', 'PROFESSIONAL', false, false)
	`)
	_, err = tenantInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert tenant test data: %v", err)
	}

	// Run the verification - this should detect the inconsistency
	err = syncJob.checkProfessionalTenantsWithCommunitySubscriptions(ctx)
	if err == nil {
		t.Error("Expected checkProfessionalTenantsWithCommunitySubscriptions to return an error for inconsistency, but it didn't")
	}

	// Verify the error message contains expected information
	if err != nil {
		expectedErrorMsg := "non-team users in professional tenants with community subscriptions found: found 1 issues"
		if err.Error() != expectedErrorMsg {
			t.Errorf("Expected error message '%s', but got '%s'", expectedErrorMsg, err.Error())
		}
		t.Logf("Got expected error: %v", err)
	}

	// Clean up for next test
	cleanupUserQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupUserQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up user test data: %v", err)
	}

	cleanupSubQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE")
	_, err = cleanupSubQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up subscription test data: %v", err)
	}

	cleanupTenantQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".tenant WHERE TRUE")
	_, err = cleanupTenantQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up tenant test data: %v", err)
	}
}

func TestCheckCommunityTenantsWithProfessionalSubscriptions(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	// Insert test data with a non-team user in a community tenant with a professional subscription
	userInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.user (
			id, email, tenant_ids, created_at, billing_method,
			check_subscription_status, orb_subscription_id, orb_customer_id
		)
		VALUES
			('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1')
	`)
	_, err := userInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert user test data: %v", err)
	}

	// Insert subscription with professional plan
	subscriptionInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.subscription (
			subscription_id, stripe_customer_id, price_id, status, seats,
			cancel_at_period_end, has_payment_method, user_id, billing_method,
			orb_customer_id, orb_status, updated_at, external_plan_id
		)
		VALUES
			('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_developer_plan')
	`)
	_, err = subscriptionInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert subscription test data: %v", err)
	}

	// Insert tenant data - community tenant that is not a team
	tenantInsertQuery := bqEmulator.Client.Query(`
		INSERT INTO ` + testDatasetName + `.tenant (
			id, name, shard_namespace, cloud, tier, is_self_serve_team, is_legacy_self_serve_team
		)
		VALUES
			('tenant1', 'Test Tenant', 'test-shard', 'test-cloud', 'COMMUNITY', false, false)
	`)
	_, err = tenantInsertQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to insert tenant test data: %v", err)
	}

	// Run the verification - this should detect the inconsistency
	err = syncJob.checkCommunityTenantsWithProfessionalSubscriptions(ctx)
	if err == nil {
		t.Error("Expected checkCommunityTenantsWithProfessionalSubscriptions to return an error for inconsistency, but it didn't")
	}

	// Verify the error message contains expected information
	if err != nil {
		expectedErrorMsg := "non-team users in community tenants with professional subscriptions found: found 1 issues"
		if err.Error() != expectedErrorMsg {
			t.Errorf("Expected error message '%s', but got '%s'", expectedErrorMsg, err.Error())
		}
		t.Logf("Got expected error: %v", err)
	}

	// Clean up for next test
	cleanupUserQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE")
	_, err = cleanupUserQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up user test data: %v", err)
	}

	cleanupSubQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE")
	_, err = cleanupSubQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up subscription test data: %v", err)
	}

	cleanupTenantQuery := bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".tenant WHERE TRUE")
	_, err = cleanupTenantQuery.Read(ctx)
	if err != nil {
		t.Fatalf("Failed to clean up tenant test data: %v", err)
	}
}

func TestRunVerifications(t *testing.T) {
	ctx := context.Background()

	// Create a SyncJob instance for testing
	syncJob := &SyncJob{
		bqClient:    bqEmulator.Client,
		datasetName: testDatasetName,
	}

	t.Run("all verifications pass with clean data", func(t *testing.T) {
		// Insert clean test data
		userInsertQuery := bqEmulator.Client.Query(`
			INSERT INTO ` + testDatasetName + `.user (
				id, email, tenant_ids, created_at, billing_method,
				check_subscription_status, orb_subscription_id, orb_customer_id
			)
			VALUES
				('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1'),
				('user2', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_STRIPE', false, NULL, NULL)
		`)
		_, err := userInsertQuery.Read(ctx)
		if err != nil {
			t.Fatalf("Failed to insert user test data: %v", err)
		}

		subscriptionInsertQuery := bqEmulator.Client.Query(`
			INSERT INTO ` + testDatasetName + `.subscription (
				subscription_id, stripe_customer_id, price_id, status, seats,
				cancel_at_period_end, has_payment_method, user_id, billing_method,
				orb_customer_id, orb_status, updated_at
			)
			VALUES
				('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'))
		`)
		_, err = subscriptionInsertQuery.Read(ctx)
		if err != nil {
			t.Fatalf("Failed to insert subscription test data: %v", err)
		}

		// Run all verifications - should pass
		err = syncJob.RunVerifications(ctx)
		if err != nil {
			t.Errorf("Expected RunVerifications to pass with clean data, but got error: %v", err)
		}

		// Clean up
		bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE").Read(ctx)
		bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE").Read(ctx)
	})

	t.Run("verifications fail with problematic data", func(t *testing.T) {
		// Insert problematic test data that will trigger multiple verification failures
		userInsertQuery := bqEmulator.Client.Query(`
			INSERT INTO ` + testDatasetName + `.user (
				id, email, tenant_ids, created_at, billing_method,
				check_subscription_status, orb_subscription_id, orb_customer_id, tier_change
			)
			VALUES
				-- Duplicate emails (user1 and user2 have same normalized email)
				('user1', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub1', 'orb_cus1', NULL),
				('user2', ' <EMAIL> ', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub2', 'orb_cus2', NULL),
				-- User with missing subscription (orb_sub3 doesn't exist)
				('user3', '<EMAIL>', ['tenant1'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub3', 'orb_cus3', NULL),
				-- User in professional tenant with community subscription
				('user5', '<EMAIL>', ['tenant2'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub5', 'orb_cus5', NULL),
				-- User in community tenant with professional subscription
				('user6', '<EMAIL>', ['tenant3'], TIMESTAMP('2024-01-01 00:00:00'), 'BILLING_METHOD_ORB', false, 'orb_sub6', 'orb_cus6', NULL)
		`)
		_, err := userInsertQuery.Read(ctx)
		if err != nil {
			t.Fatalf("Failed to insert user test data: %v", err)
		}

		subscriptionInsertQuery := bqEmulator.Client.Query(`
			INSERT INTO ` + testDatasetName + `.subscription (
				subscription_id, stripe_customer_id, price_id, status, seats,
				cancel_at_period_end, has_payment_method, user_id, billing_method,
				orb_customer_id, orb_status, updated_at, external_plan_id
			)
			VALUES
				-- Multiple Orb subscriptions for user1
				('orb_sub1', 'stripe_cus1', 'price1', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus1', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_developer_plan'),
				('orb_sub2', 'stripe_cus2', 'price2', 'ACTIVE', 1, false, true, 'user1', 'BILLING_METHOD_ORB', 'orb_cus2', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_developer_plan'),
				-- Note: orb_sub3 is missing, creating inconsistency for user3
				-- This is an orphaned active Orb subscription - user2 has it but user2's orb_subscription_id is orb_sub2, not orb_sub4
				('orb_sub4', 'stripe_cus4', 'price4', 'ACTIVE', 1, false, true, 'user2', 'BILLING_METHOD_ORB', 'orb_cus4', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_developer_plan'),
				-- Community subscription for user in professional tenant
				('orb_sub5', 'stripe_cus5', 'price5', 'ACTIVE', 1, false, true, 'user5', 'BILLING_METHOD_ORB', 'orb_cus5', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_community_plan'),
				-- Professional subscription for user in community tenant
				('orb_sub6', 'stripe_cus6', 'price6', 'ACTIVE', 1, false, true, 'user6', 'BILLING_METHOD_ORB', 'orb_cus6', 'ORB_STATUS_ACTIVE', TIMESTAMP('2023-01-01 00:00:00'), 'orb_developer_plan')
		`)
		_, err = subscriptionInsertQuery.Read(ctx)
		if err != nil {
			t.Fatalf("Failed to insert subscription test data: %v", err)
		}

		// Insert tenant data
		tenantInsertQuery := bqEmulator.Client.Query(`
			INSERT INTO ` + testDatasetName + `.tenant (
				id, name, shard_namespace, cloud, tier, is_self_serve_team, is_legacy_self_serve_team
			)
			VALUES
				('tenant1', 'Test Tenant 1', 'test-shard', 'test-cloud', 'PROFESSIONAL', true, false),
				-- Professional tenant (PROFESSIONAL tier) that is not a team
				('tenant2', 'Test Tenant 2', 'test-shard', 'test-cloud', 'PROFESSIONAL', false, false),
				-- Community tenant that is not a team
				('tenant3', 'Test Tenant 3', 'test-shard', 'test-cloud', 'COMMUNITY', false, false)
		`)
		_, err = tenantInsertQuery.Read(ctx)
		if err != nil {
			t.Fatalf("Failed to insert tenant test data: %v", err)
		}

		// Run all verifications - should fail with multiple issues
		err = syncJob.RunVerifications(ctx)
		if err == nil {
			t.Error("Expected RunVerifications to fail with problematic data, but it passed")
		}

		// Verify the error message contains information about multiple failures
		if err != nil {
			errorMsg := err.Error()
			t.Logf("Got expected error: %v", errorMsg)

			// The error should mention verification checks failed and contain all expected failure types
			expectedSubstrings := []string{
				"verification checks failed",
				"duplicate email check failed",
				"multiple Orb subscriptions check failed",
				"user-subscription consistency check failed",
				"orphaned active Orb subscriptions check failed",
				"professional tenants with community subscriptions check failed",
				"community tenants with professional subscriptions check failed",
			}

			for _, expectedSubstring := range expectedSubstrings {
				if !strings.Contains(errorMsg, expectedSubstring) {
					t.Errorf("Expected error message to contain '%s', but got: %s", expectedSubstring, errorMsg)
				}
			}
		}

		// Clean up
		bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".user WHERE TRUE").Read(ctx)
		bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".subscription WHERE TRUE").Read(ctx)
		bqEmulator.Client.Query("DELETE FROM " + testDatasetName + ".tenant WHERE TRUE").Read(ctx)
	})
}

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

{
  deployment: [
    {
      name: 'request-insight-sync-job',
      kubecfg: {
        target: '//services/request_insight/sync_job:kubecfg',
        task: [
          {
            cloud: std.asciiUpper(centralNamespace.cloud),
            env: centralNamespace.env,
            namespace: centralNamespace.namespace,
          }
          for centralNamespace in cloudInfo.centralNamespaces
          if cloudInfo.isLeadCluster(centralNamespace.cloud)
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#team-insights',
        },
      },
    },
    {
      name: 'request-insight-sync-job-monitoring',
      kubecfg: {
        target: '//services/request_insight/sync_job:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline'],
          slack_channel: '#team-insights',
        },
      },
    },
  ],
}

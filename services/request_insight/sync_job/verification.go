package main

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"cloud.google.com/go/bigquery"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// RunVerifications performs various data consistency checks after the sync job completes.
// It runs BigQuery queries to identify potential issues in the synced data.
func (s *SyncJob) RunVerifications(ctx context.Context) error {
	var errs []error

	// Check for users with the same normalized email (after lowercase and trimming)
	if err := s.checkDuplicateEmails(ctx); err != nil {
		errs = append(errs, fmt.Errorf("duplicate email check failed: %w", err))
	}

	// Check for users with multiple active Orb subscriptions
	if err := s.checkMultipleOrbSubscriptions(ctx); err != nil {
		errs = append(errs, fmt.Errorf("multiple Orb subscriptions check failed: %w", err))
	}

	// Check for inconsistencies between user and subscription tables
	if err := s.checkUserSubscriptionConsistency(ctx); err != nil {
		errs = append(errs, fmt.Errorf("user-subscription consistency check failed: %w", err))
	}

	// Check for orphaned active Orb subscriptions
	if err := s.checkOrphanedActiveOrbSubscriptions(ctx); err != nil {
		errs = append(errs, fmt.Errorf("orphaned active Orb subscriptions check failed: %w", err))
	}

	// Check for non-team users in professional tenants with active community tier subscriptions
	if err := s.checkProfessionalTenantsWithCommunitySubscriptions(ctx); err != nil {
		errs = append(errs, fmt.Errorf("professional tenants with community subscriptions check failed: %w", err))
	}

	// Check for non-team users in community tenants with active professional plan subscriptions
	if err := s.checkCommunityTenantsWithProfessionalSubscriptions(ctx); err != nil {
		errs = append(errs, fmt.Errorf("community tenants with professional subscriptions check failed: %w", err))
	}

	if len(errs) > 0 {
		var errMsgs []string
		for _, err := range errs {
			errMsgs = append(errMsgs, err.Error())
		}
		return fmt.Errorf("verification checks failed: %s", strings.Join(errMsgs, "; "))
	}

	log.Info().Msg("All verification checks passed")
	return nil
}

// checkDuplicateEmails checks for users with the same normalized email (after lowercase and trimming).
func (s *SyncJob) checkDuplicateEmails(ctx context.Context) error {
	query := `
		SELECT
			LOWER(TRIM(email)) as normalized_email,
			COUNT(*) as user_count
		FROM user
		WHERE ARRAY_LENGTH(tenant_ids) > 0
		GROUP BY normalized_email
		HAVING COUNT(*) > 1
		ORDER BY user_count DESC
	`

	log.Info().Msg("Running duplicate email verification check")

	q := s.bqClient.Query(query)
	q.DefaultDatasetID = s.datasetName

	it, err := q.Read(ctx)
	if err != nil {
		return fmt.Errorf("error running duplicate emails query: %w", err)
	}

	count := 0
	for {
		var row map[string]bigquery.Value
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return fmt.Errorf("error reading duplicate emails results: %w", err)
		}

		if count < 5 {
			rowJSON, _ := json.Marshal(row)
			log.Warn().RawJSON("data", rowJSON).Msgf("Duplicate normalized emails found: %s", string(rowJSON))
		}
		count++
	}

	DuplicateEmailsCount.Set(float64(count))

	if count > 0 {
		log.Warn().Int("count", count).Msgf("Duplicate normalized emails found: found %d issues", count)
		return fmt.Errorf("duplicate normalized emails found: found %d issues", count)
	}

	log.Info().Msg("Duplicate emails check passed: no issues found")
	return nil
}

// checkMultipleOrbSubscriptions checks for users with multiple active Orb subscriptions.
func (s *SyncJob) checkMultipleOrbSubscriptions(ctx context.Context) error {
	query := `
		SELECT
			user_id,
			COUNT(*) as sub_count
		FROM subscription
		JOIN user
		ON subscription.user_id = user.id
		WHERE
			user.billing_method = 'BILLING_METHOD_ORB'
			AND orb_status = 'ORB_STATUS_ACTIVE'
			AND subscription.updated_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
		GROUP BY user_id
		HAVING COUNT(*) > 1
	`

	log.Info().Msg("Running multiple Orb subscriptions verification check")

	q := s.bqClient.Query(query)
	q.DefaultDatasetID = s.datasetName

	it, err := q.Read(ctx)
	if err != nil {
		return fmt.Errorf("error running multiple Orb subscriptions query: %w", err)
	}

	count := 0
	for {
		var row map[string]bigquery.Value
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return fmt.Errorf("error reading multiple Orb subscriptions results: %w", err)
		}

		if count < 5 {
			rowJSON, _ := json.Marshal(row)
			log.Warn().RawJSON("data", rowJSON).Msgf("Users with multiple active Orb subscriptions found: %s", string(rowJSON))
		}
		count++
	}

	MultipleOrbSubscriptionsCount.Set(float64(count))

	if count > 0 {
		log.Warn().Int("count", count).Msgf("Users with multiple active Orb subscriptions found: found %d issues", count)
		return fmt.Errorf("users with multiple active Orb subscriptions found: found %d issues", count)
	}

	log.Info().Msg("Multiple Orb subscriptions check passed: no issues found")
	return nil
}

// checkUserSubscriptionConsistency checks for inconsistencies between user and subscription tables.
func (s *SyncJob) checkUserSubscriptionConsistency(ctx context.Context) error {
	query := `
		SELECT
			u.id as user_id,
			u.orb_subscription_id,
			s.subscription_id,
			s.user_id as subscription_user_id
		FROM user u
		LEFT JOIN subscription s
		ON u.orb_subscription_id = s.subscription_id
		WHERE
		    u.billing_method = 'BILLING_METHOD_ORB'
			AND u.orb_subscription_id IS NOT NULL
			AND (
				s.user_id != u.id
				OR
				s.orb_customer_id != u.orb_customer_id
				OR
				s.subscription_id IS NULL
				OR
				s.billing_method != 'BILLING_METHOD_ORB'
			)
			-- Ensure user is not deleted from all tenants
			AND ARRAY_LENGTH(u.tenant_ids) > 0
			-- Ensure data is not from an in-progress sync
			AND (s.subscription_id IS NULL OR s.updated_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE))
	`

	log.Info().Msg("Running user-subscription consistency verification check")

	q := s.bqClient.Query(query)
	q.DefaultDatasetID = s.datasetName

	it, err := q.Read(ctx)
	if err != nil {
		return fmt.Errorf("error running user-subscription consistency query: %w", err)
	}

	count := 0
	for {
		var row map[string]bigquery.Value
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return fmt.Errorf("error reading user-subscription consistency results: %w", err)
		}

		if count < 5 {
			rowJSON, _ := json.Marshal(row)
			log.Warn().RawJSON("data", rowJSON).Msgf("User-subscription inconsistencies found: %s", string(rowJSON))
		}
		count++
	}

	UserSubscriptionInconsistenciesCount.Set(float64(count))

	if count > 0 {
		log.Warn().Int("count", count).Msgf("User-subscription inconsistencies found: found %d issues", count)
		return fmt.Errorf("user-subscription inconsistencies found: found %d issues", count)
	}

	log.Info().Msg("User-subscription consistency check passed: no issues found")
	return nil
}

// checkOrphanedActiveOrbSubscriptions checks for active Orb subscriptions that are not referenced by any user.
func (s *SyncJob) checkOrphanedActiveOrbSubscriptions(ctx context.Context) error {
	query := `
		SELECT
			u.id AS user_id,
			u.orb_customer_id,
			u.orb_subscription_id AS current_subscription_id,
			s.subscription_id AS orphan_subscription_id
		FROM subscription AS s
		JOIN user AS u
			ON s.user_id = u.id
		WHERE
			s.orb_status = 'ORB_STATUS_ACTIVE'
			AND u.orb_subscription_id != s.subscription_id
			AND u.billing_method = 'BILLING_METHOD_ORB'
			AND s.updated_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
	`

	log.Info().Msg("Running orphaned active Orb subscriptions verification check")

	q := s.bqClient.Query(query)
	q.DefaultDatasetID = s.datasetName

	it, err := q.Read(ctx)
	if err != nil {
		return fmt.Errorf("error running orphaned active Orb subscriptions query: %w", err)
	}

	count := 0
	for {
		var row map[string]bigquery.Value
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return fmt.Errorf("error reading orphaned active Orb subscriptions results: %w", err)
		}

		if count < 5 {
			rowJSON, _ := json.Marshal(row)
			log.Warn().RawJSON("data", rowJSON).Msgf("Orphaned active Orb subscriptions found: %s", string(rowJSON))
		}
		count++
	}

	OrphanedActiveOrbSubscriptionsCount.Set(float64(count))

	if count > 0 {
		log.Warn().Int("count", count).Msgf("Orphaned active Orb subscriptions found: found %d issues", count)
		return fmt.Errorf("orphaned active Orb subscriptions found: found %d issues", count)
	}

	log.Info().Msg("Orphaned active Orb subscriptions check passed: no issues found")
	return nil
}

// checkProfessionalTenantsWithCommunitySubscriptions checks for non-team users in professional tenants who have active community tier subscriptions.
func (s *SyncJob) checkProfessionalTenantsWithCommunitySubscriptions(ctx context.Context) error {
	query := `
		SELECT
			u.id AS user_id,
			t.id AS tenant_id
		FROM user AS u
		JOIN subscription AS s
			ON u.orb_subscription_id = s.subscription_id
		JOIN tenant AS t
			ON t.id IN UNNEST(u.tenant_ids)
		WHERE
			-- User has an active community tier subscription
			s.orb_status = 'ORB_STATUS_ACTIVE'
			AND s.external_plan_id = 'orb_community_plan'
			-- User is in a professional tenant
			AND t.tier = 'PROFESSIONAL'
			-- User is not in a team
			AND (t.is_self_serve_team IS NULL OR t.is_self_serve_team = false)
			-- Ensure data is not from an in-progress sync
			AND s.updated_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
			-- Ensure the subscription is not ending right during the job
			AND (
				s.end_date IS NULL OR
				s.end_date > TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
			)
	`

	log.Info().Msg("Running professional tenants with community subscriptions verification check")

	q := s.bqClient.Query(query)
	q.DefaultDatasetID = s.datasetName

	it, err := q.Read(ctx)
	if err != nil {
		return fmt.Errorf("error running professional tenants with community subscriptions query: %w", err)
	}

	count := 0
	for {
		var row map[string]bigquery.Value
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return fmt.Errorf("error reading professional tenants with community subscriptions results: %w", err)
		}

		if count < 5 {
			rowJSON, _ := json.Marshal(row)
			log.Warn().RawJSON("data", rowJSON).Msgf("Non-team user in professional tenant with community subscription found: %s", string(rowJSON))
		}
		count++
	}

	ProfessionalTenantsWithCommunitySubscriptionsCount.Set(float64(count))

	if count > 0 {
		log.Warn().Int("count", count).Msgf("Non-team users in professional tenants with community subscriptions found: found %d issues", count)
		return fmt.Errorf("non-team users in professional tenants with community subscriptions found: found %d issues", count)
	}

	log.Info().Msg("Professional tenants with community subscriptions check passed: no issues found")
	return nil
}

// checkCommunityTenantsWithProfessionalSubscriptions checks for non-team users in community tenants who have active professional plan subscriptions.
func (s *SyncJob) checkCommunityTenantsWithProfessionalSubscriptions(ctx context.Context) error {
	query := `
		SELECT
			u.id AS user_id,
			t.id AS tenant_id
		FROM user AS u
		JOIN subscription AS s
			ON u.orb_subscription_id = s.subscription_id
		JOIN tenant AS t
			ON t.id IN UNNEST(u.tenant_ids)
		WHERE
			-- User has an active developer/pro/max plan subscription
			s.orb_status = 'ORB_STATUS_ACTIVE'
			AND s.external_plan_id in ('orb_developer_plan', 'orb_pro_plan', 'orb_max_plan')
			-- User is in a community tenant
			AND t.tier = 'COMMUNITY'
			-- User is not in a team
			AND (t.is_self_serve_team IS NULL OR t.is_self_serve_team = false)
			-- Ensure data is not from an in-progress sync
			AND s.updated_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
			-- Ensure the subscription is not ending right during the job
			AND (
				s.end_date IS NULL OR
				s.end_date > TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL 30 MINUTE)
			)
	`

	log.Info().Msg("Running community tenants with professional subscriptions verification check")

	q := s.bqClient.Query(query)
	q.DefaultDatasetID = s.datasetName

	it, err := q.Read(ctx)
	if err != nil {
		return fmt.Errorf("error running community tenants with professional subscriptions query: %w", err)
	}

	count := 0
	for {
		var row map[string]bigquery.Value
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return fmt.Errorf("error reading community tenants with professional subscriptions results: %w", err)
		}

		if count < 5 {
			rowJSON, _ := json.Marshal(row)
			log.Warn().RawJSON("data", rowJSON).Msgf("Non-team user in community tenant with professional subscription found: %s", string(rowJSON))
		}
		count++
	}

	CommunityTenantsWithProfessionalSubscriptionsCount.Set(float64(count))

	if count > 0 {
		log.Warn().Int("count", count).Msgf("Non-team users in community tenants with professional subscriptions found: found %d issues", count)
		return fmt.Errorf("non-team users in community tenants with professional subscriptions found: found %d issues", count)
	}

	log.Info().Msg("Community tenants with professional subscriptions check passed: no issues found")
	return nil
}

# Request Insight sync job

This directory contains a kubernetes job for syncing the entirety of our tenant and user data to
BigQuery. This is useful for having a single source of truth about who all of our tenants and users
are and their current state, which can be hard to get from the raw request insight event stream.
(For example, without this sync it's impossible to know about users who have signed up for Augment
but have not downloaded the extension.)

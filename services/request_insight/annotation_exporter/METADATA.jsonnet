// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    // Service to exports annotations to vendors.
    // The change is only deployed to tenants with the `exportAnnotations` flag set.
    {
      name: 'request-insight-annotation-exporter',
      kubecfg: {
        target: '//services/request_insight/annotation_exporter:kubecfg',
        task: tenantNamespaces.namespaces,
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['aswin', 'jacqueline'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

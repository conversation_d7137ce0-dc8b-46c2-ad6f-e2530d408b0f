// K8S deployment file for the request insight annotation exporter for vendors
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
function(env, namespace, cloud, namespace_config)
  local exportAnnotations = namespace_config.flags.exportAnnotations;
  if !exportAnnotations.enabled then [] else
    local projectId = cloudInfo[cloud].projectId;

    local appName = 'request-insight-annotation-exporter';
    local shortAppName = 'ri-annot-exp';
    local serviceAccount = gcpLib.createServiceAccount(
      appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
    );

    local clientCert = certLib.createClientCert(name='%s-client-certificate' % shortAppName,
                                                namespace=namespace,
                                                appName=appName);
    local centralClientCert = certLib.createCentralClientCert(
      name='%s-central-client-certificate' % shortAppName,
      namespace=namespace,
      env=env,
      appName=appName,
      dnsNames=grpcLib.grpcServiceNames('request-insight', namespace=namespace),
    );

    local webhookAuthSealedSecretMap = (
      {
        'dev-aswin': import 'services/request_insight/annotation_exporter/sealed-dev-aswin-webhook-auth.jsonnet',
        'dev-yuri': import 'services/request_insight/annotation_exporter/sealed-dev-yuri-webhook-auth.jsonnet',
        'aitutor-turing': import 'services/request_insight/annotation_exporter/sealed-turing-webhook-auth.jsonnet',
        'aitutor-pareto': import 'services/request_insight/annotation_exporter/sealed-pareto-webhook-auth.jsonnet',
      }
    );
    local useSealedSecret = std.objectHas(webhookAuthSealedSecretMap, namespace);
    assert useSealedSecret || env == 'DEV' : 'prod envs should generally have a sealed secret for the webhook';

    local config = {
      project_id: projectId,
      topic_name: '%s-request-insight-topic' % namespace,
      subscription_name: '%s-request-insight-annotation-export-sub' % namespace,
      subscription_description: 'ri-annotation-exporter',
      // Set this to 1 to minimize the chance of posting duplicate payloads to
      // the webhook, in case there is an error somewhere. If we start to fall
      // behind we can increase this, though.
      subscription_batch_size: 1,
      webhook_url: exportAnnotations.webhook_url,
      webhook_auth_file: if useSealedSecret then '/webhook-auth/credentials.json' else null,
      request_insight_central_endpoint: endpoints.getRequestInsightCentralGrpcUrl(env=env, location=std.substr(cloudInfo[cloud].region, 0, 2)),
      token_exchange_endpoint: endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    } + if grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config) then {
      client_mtls: clientCert.config,
      central_client_mtls: centralClientCert.config,
    } else {};

    local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

    local gcpObjects = [
      // Pub/sub subscription.
      {
        apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
        kind: 'PubSubSubscription',
        metadata: {
          name: config.subscription_name,
          namespace: namespace,
          // Don't delete in dev because this causes a lot of errors at the end of a test run, if
          // the topic is deleted before the pod.
          annotations: if env == 'DEV' then {
            'cnrm.cloud.google.com/deletion-policy': 'abandon',
          } else {},
          labels: {
            app: appName,
          },
        },
        spec: {
          topicRef: {
            name: config.topic_name,
          },
          // This is how long until we wait to reprocess an unacknowledged
          // message. Note that this will kick in if we fail to find events in
          // gcs for a request
          ackDeadlineSeconds: 120,
          retryPolicy: {
            minimumBackoff: '5s',
            maximumBackoff: '300s',
          },
          // Retain messages for 1 hour in dev and 1 day in staging/prod.
          // This retention is lower than other exporters because we use pub/sub's retry mechanism
          // to wait for all the data required to export an annotation. If something happened such that
          // a required event wasn't published, there's nothing we can ever do to recover.
          messageRetentionDuration: if env == 'DEV' then '3600s' else '86400s',
          retainAckedMessages: false,
        },
      },
      // Give access to the subscription.
      gcpLib.grantAccess(
        name='ri-annotation-exporter-subscription-policy',
        namespace=namespace,
        appName=appName,
        env=env,
        resourceRef={
          kind: 'PubSubSubscription',
          name: config.subscription_name,
        },
        bindings=[
          {
            role: 'roles/pubsub.subscriber',
            members: [
              {
                memberFrom: {
                  serviceAccountRef: {
                    name: serviceAccount.iamServiceAccountName,
                  },
                },
              },
            ],
          },
        ]
      ),
    ];

    local container = {
      name: 'request-insight-annotation-exporter',
      target: {
        name: '//services/request_insight/annotation_exporter:image',
        dst: 'request_insight_annotation_exporter',
      },
      env: [
        {
          name: 'POD_NAME',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.name',
            },
          },
        },
        {
          name: 'POD_NAMESPACE',
          valueFrom: {
            fieldRef: {
              fieldPath: 'metadata.namespace',
            },
          },
        },
      ],
      readinessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        initialDelaySeconds: 60,
        periodSeconds: 10,
      },
      livenessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        initialDelaySeconds: 60,
        periodSeconds: 20,
      },
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        centralClientCert.volumeMountDef,
      ] + if useSealedSecret then [
        {
          name: 'webhook-auth',
          mountPath: '/webhook-auth',
          readOnly: true,
        },
      ] else [],
      args: [
        '--health-file=/tmp/health',
      ],
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
    };
    local pod = {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      priorityClassName: cloudInfo.envToPriorityClass(env),
      volumes: [
        configMap.podVolumeDef,
        clientCert.podVolumeDef,
        centralClientCert.podVolumeDef,
      ] + if useSealedSecret then [
        {
          name: 'webhook-auth',
          secret: {
            secretName: 'annotation-exporter-webhook-auth',  // pragma: allowlist secret
            optional: false,
          },
        },
      ] else [],
    };

    local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
    // we allow multiple exporters on the same host as they run in the background
    local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=null);

    local webhookAuthSealedSecret = if useSealedSecret then webhookAuthSealedSecretMap[namespace] + {
      metadata+: {
        namespace: namespace,
        labels: {
          app: appName,
        },
      },
    } else [];

    lib.flatten([
      clientCert.objects,
      centralClientCert.objects,
      webhookAuthSealedSecret,
      configMap.objects,
      serviceAccount.objects,
      {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        metadata: {
          name: 'request-insight-annotation-exporter',
          namespace: namespace,
          labels: {
            app: 'request-insight-annotation-exporter',
          },
          annotations: {
            'reloader.stakater.com/search': 'true',
          },
        },
        spec: {
          minReadySeconds: 0,
          replicas: 1,
          strategy: {
            type: 'RollingUpdate',
            rollingUpdate: {
              maxSurge: 1,
              maxUnavailable: 0,
            },
          },
          selector: {
            matchLabels: {
              app: appName,
            },
          },
          template: {
            metadata: {
              labels: {
                app: appName,
              },
            },
            spec: pod + {
              tolerations: tolerations,
              affinity: affinity,
            },
          },
        },
      },
      gcpObjects,
    ])

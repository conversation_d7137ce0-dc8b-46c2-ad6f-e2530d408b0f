load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "annotation_exporter",
    srcs = [
        "annotation_exporter.py",
        "main.py",
    ],
    main = "main.py",
    deps = [
        "//base/logging:struct_logging",
        "//base/python/grpc:client_options",
        "//base/python/signal_handler",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/central:request_insight_central_py_proto",
        "//services/request_insight/central/client:request_insight_central_client_py",
        "//services/request_insight/lib:request_insight_subscriber",
        "//services/token_exchange:token_exchange_py_proto",
        "//services/token_exchange/client:client_py",
        requirement("dataclasses_json"),
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "annotation_exporter_test",
    srcs = ["annotation_exporter_test.py"],
    deps = [
        ":annotation_exporter",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":annotation_exporter",
)

kubecfg_library(
    name = "auth-lib",
    srcs = glob(["*-auth.jsonnet"]),
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        "sealed-dev-aswin-webhook-auth.jsonnet",
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        ":auth-lib",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

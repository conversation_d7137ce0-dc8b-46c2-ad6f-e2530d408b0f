"""Exporter for exporting annotations from full request insight event data to vendors.

Annotation here means a feedback/resolution event. Mainly contractors would like
some feedback on what annotations we have received from them.
"""

import datetime
import json
import pathlib
from typing import Iterable

import requests
import structlog
from google.auth.transport.requests import AuthorizedSession
from google.oauth2 import service_account
from google.protobuf.json_format import MessageToDict

from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2
from services.request_insight.central import request_insight_central_pb2
from services.request_insight.central.client.client import RequestInsightCentralClient
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import TokenExchangeClient

log = structlog.get_logger()


def get_request_context(
    tenant_id: str,
    token_exchange_client: TokenExchangeClient,
) -> RequestContext:
    """Get the request context.

    If this returns an auth token, it will be used to authenticate the request to gcs proxy.
    The token will be valid for a short period of time.
    """
    service_token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id,
        scopes=[
            token_exchange_pb2.REQUEST_RESTRICTED_R,
        ],
    )
    request_context = RequestContext.create(
        request_source="background",
        auth_token=service_token,
    )
    return request_context


class MissingEventDataException(Exception):
    """Raised when some data is missing from the support db.

    The usual remediation is to wait until the support db exporter has populated
    that request's entries
    """


class AnnotationExporter:
    """Exports annotations from full request insight event data to vendors.

    Args:
        ri_central_client: Client for reading events from RI central service.
        token_exchange_client: Client for getting tokens for RI central service.
        webhook_url: Vendor URL to send annotations to.
        webhook_auth_file: A file in JSON format containing the credentials
                         for the vendor.

    webhook_auth_file can have one of these formats:
        1. A json file with "augment_vendor_request_header", eg. {
                "augment_vendor_request_header": {"Authorization": "Bearer <token>"}
            }
        2. A json file with GCP service account credentials
    Currently, Pareto uses (1) and Turing uses (2)
    """

    REQUEST_HEADER_KEY = "augment_vendor_request_header"

    def __init__(
        self,
        ri_central_client: RequestInsightCentralClient,
        token_exchange_client: TokenExchangeClient,
        webhook_url: str,
        webhook_auth_file: str | None,
        export_failed_edits_after_minutes: int,
    ):
        self.ri_central_client = ri_central_client
        self.token_exchange_client = token_exchange_client
        self.webhook_url = webhook_url
        self.webhook_auth_file = webhook_auth_file
        self.export_failed_edits_after_minutes = export_failed_edits_after_minutes
        self.pending_events = {}

        if self.webhook_url != "" and self.webhook_auth_file is not None:
            creds = json.loads(
                pathlib.Path(self.webhook_auth_file).read_text(encoding="utf-8")
            )
            if creds.get(self.REQUEST_HEADER_KEY) is not None:
                header = creds.get(self.REQUEST_HEADER_KEY)
                self.session = requests.Session()
                self.session.headers.update(header)
            else:
                audience = webhook_url or "test.augmentcode.com"
                credentials = (
                    service_account.IDTokenCredentials.from_service_account_file(
                        self.webhook_auth_file, target_audience=audience
                    )
                )
                self.session = AuthorizedSession(credentials)

        log.info("Initialized exporter with webhook_url=%s", self.webhook_url)

    def _post_data(self, data: dict):
        """Post data to the webhook URL.

        Posts data in this format:
            {"payload": {"event_type": "<completion|edit>", <event info>}}
        """
        if self.webhook_url == "":
            log.info(
                "Dry run, skipping posting data for %s",
                data["request_id"],
            )
            return

        resp = self.session.post(
            self.webhook_url,
            json={"payload": data},
            timeout=60,
        )
        if resp.status_code != requests.codes.ok:
            raise requests.exceptions.HTTPError(
                f"Failed to post data to {self.webhook_url}: {resp.status_code} {resp.content}"
            )
        log.info(
            "Successfully posted data for request_id=%s with response %s",
            data["request_id"],
            resp.text,
        )

    def _read_events(
        self,
        tenant_id: str,
        request_id: str,
        event_types: list[str],
    ) -> list[request_insight_pb2.RequestEvent]:
        """Read events from central server for a request."""
        request = request_insight_central_pb2.GetRequestEventsRequest(
            tenant_id=tenant_id,
            request_id=request_id,
            filters=[
                request_insight_central_pb2.GetRequestEventsRequest.Filter(
                    event_type=event_type
                )
                for event_type in event_types
            ],
        )
        request_context = get_request_context(tenant_id, self.token_exchange_client)
        return [
            response.request_event
            for response in self.ri_central_client.get_request_events(
                request, request_context
            )
        ]

    def _export_data_for_completion(
        self,
        request_id: str,
        tenant_id: str,
        event_time: datetime.datetime,
        completion_feedback: request_insight_pb2.CompletionFeedback,
    ) -> dict:
        """Gather all of the fields we want to export for completions.

        Explicitly gathers each field we want to export
        """
        event_types = [
            "completion_host_request",
            "completion_host_response",
            "request_metadata",
        ]
        events = self._read_events(tenant_id, request_id, event_types)
        if not events:
            raise MissingEventDataException(
                f"Missing any required events {event_types} for request {request_id}"
            )
        to_export = {
            "event_type": "completion",
            "request_id": request_id,
            "feedback": {
                "rating": request_insight_pb2.FeedbackRating.Name(
                    completion_feedback.rating
                ),
                "note": completion_feedback.note,
            },
        }
        for event in events:
            if event.HasField("completion_host_request"):
                to_export["request"] = {
                    "prefix": event.completion_host_request.prefix,
                    "suffix": event.completion_host_request.suffix,
                    "path": event.completion_host_request.path,
                    "position": {
                        "prefix_begin": event.completion_host_request.position.prefix_begin,
                        "cursor_position": event.completion_host_request.position.cursor_position,
                        "suffix_end": event.completion_host_request.position.suffix_end,
                    },
                    # TODO: support blobs here as well, which would require
                    # getting the number of blobs for a checkpoint from gcs
                    "file_count": len(event.completion_host_request.blob_names),
                }
            if event.HasField("completion_host_response"):
                to_export["response"] = {
                    "text": event.completion_host_response.text,
                    "skipped_suffix": event.completion_host_response.skipped_suffix,
                    "suffix_replacement_text": event.completion_host_response.suffix_replacement_text,
                }
            if event.HasField("request_metadata"):
                to_export["user_id"] = event.request_metadata.user_id

        # Check if all of the data we want to export is in gcs yet
        required = ["feedback", "user_id"]
        # We may be missing these in some cases. If we don't see these events after a certain
        # amount of time, export them anyway.
        desired = ["request", "response"]
        missing_fields = [
            field for field in desired + required if field not in to_export
        ]
        if missing_fields:
            if not any([field in required for field in missing_fields]):
                # We are only missing request/response, check if this event is
                # old enough to export.
                threshold = datetime.datetime.now() - datetime.timedelta(
                    minutes=self.export_failed_edits_after_minutes
                )
                if event_time < threshold:
                    log.info(
                        "Exporting completion %s with missing fields %s",
                        request_id,
                        missing_fields,
                    )
                    return to_export
            raise MissingEventDataException(
                f"Missing fields {missing_fields} for completion request {request_id}"
            )

        return to_export

    def _export_data_for_edit(
        self,
        request_id: str,
        tenant_id: str,
        event_time: datetime.datetime,
        edit_resolution: request_insight_pb2.EditResolution,
    ) -> dict:
        """Gather all of the fields we want to export for edits."""
        event_types = [
            "edit_host_request",
            "edit_host_response",
            "request_metadata",
        ]
        events = self._read_events(tenant_id, request_id, event_types)
        if not events:
            raise MissingEventDataException(
                f"Missing any required events {event_types} for request {request_id}"
            )
        to_export = {
            "event_type": "edit",
            "request_id": request_id,
            "feedback": {
                "is_accepted": edit_resolution.is_accepted,
                "annotated_text": edit_resolution.annotated_text,
                "annotated_instruction": edit_resolution.annotated_instruction,
            },
        }
        for event in events:
            if event.HasField("edit_host_request"):
                to_export["request"] = {
                    "path": event.edit_host_request.request.path,
                    "prefix": event.edit_host_request.request.prefix,
                    "selected_text": event.edit_host_request.request.selected_text,
                    "suffix": event.edit_host_request.request.suffix,
                    "position": {
                        "prefix_begin": event.edit_host_request.request.position.prefix_begin,
                        "suffix_end": event.edit_host_request.request.position.suffix_end,
                    },
                    "instruction": event.edit_host_request.request.instruction,
                    # TODO: support blobs here as well, which would require
                    # getting the number of blobs for a checkpoint from gcs
                    "file_count": 0,
                }
            if event.HasField("edit_host_response"):
                to_export["response"] = {
                    "text": event.edit_host_response.response.text,
                }
            if event.HasField("request_metadata"):
                to_export["user_id"] = event.request_metadata.user_id

        # Check if all of the data we want to export is in gcs yet
        required = ["feedback", "user_id"]
        # We may be missing these in some cases, like when edits fail because
        # they are too large. If we don't see these events after a certain
        # amount of time, assume it was because the edit was too large and
        # export them anyway.
        desired = ["request", "response"]
        missing_fields = [
            field for field in desired + required if field not in to_export
        ]
        if missing_fields:
            if not any([field in required for field in missing_fields]):
                # We are only missing request/response, check if this event is
                # old enough to export.
                threshold = datetime.datetime.now() - datetime.timedelta(
                    minutes=self.export_failed_edits_after_minutes
                )
                if event_time < threshold:
                    log.info(
                        "Exporting edit %s with missing fields %s, assuming it was a rejected edit",
                        request_id,
                        missing_fields,
                    )
                    return to_export
            raise MissingEventDataException(
                f"Missing fields {missing_fields} for edit request {request_id}"
            )

        return to_export

    def _export_data_for_instruction(
        self,
        request_id: str,
        tenant_id: str,
        event_time: datetime.datetime,
        instruction_resolution: request_insight_pb2.InstructionResolution,
    ) -> dict:
        """Gather all of the fields we want to export for instruction feedback."""
        event_types = [
            "instruction_host_request",
            "instruction_host_response",
            "request_metadata",
        ]
        events = self._read_events(tenant_id, request_id, event_types)
        if not events:
            raise MissingEventDataException(
                f"Missing any required events {event_types} for request {request_id}"
            )
        to_export = {
            "event_type": "instruction",
            "request_id": request_id,
            "feedback": {
                "is_accepted_chunks": list(instruction_resolution.is_accepted_chunks),
                "is_accept_all": instruction_resolution.is_accept_all,
                "is_reject_all": instruction_resolution.is_reject_all,
            },
        }
        for event in events:
            if event.HasField("instruction_host_request"):
                to_export["request"] = {
                    "path": event.instruction_host_request.request.path,
                    "prefix": event.instruction_host_request.request.prefix,
                    "selected_text": event.instruction_host_request.request.selected_text,
                    "suffix": event.instruction_host_request.request.suffix,
                    "position": {
                        "prefix_begin": event.instruction_host_request.request.position.prefix_begin,
                        "suffix_end": event.instruction_host_request.request.position.suffix_end,
                    },
                    "instruction": event.instruction_host_request.request.instruction,
                    # TODO: support blobs here as well, which would require
                    # getting the number of blobs for a checkpoint from gcs
                    "file_count": 0,
                }
            if event.HasField("instruction_host_response"):
                to_export["response"] = {
                    "replace_text": [
                        {
                            "text": response.text,
                            "start_line": response.start_line,
                            "end_line": response.end_line,
                            "old_text": response.old_text,
                        }
                        for response in event.instruction_host_response.response.replace_text
                    ],
                }
            if event.HasField("request_metadata"):
                to_export["user_id"] = event.request_metadata.user_id

        # Check if all of the data we want to export is in gcs yet
        required = ["feedback", "user_id"]
        # We may be missing these in some cases, like when smart paste fails because
        # they are too large. If we don't see these events after a certain
        # amount of time, assume it was because the smart paste was too large and
        # export them anyway.
        desired = ["request", "response"]
        missing_fields = [
            field for field in desired + required if field not in to_export
        ]
        if missing_fields:
            if not any([field in required for field in missing_fields]):
                # We are only missing request/response, check if this event is
                # old enough to export.
                threshold = datetime.datetime.now() - datetime.timedelta(
                    minutes=self.export_failed_edits_after_minutes
                )
                if event_time < threshold:
                    log.info(
                        "Exporting instruction %s with missing fields %s, assuming it was a rejected instruction",
                        request_id,
                        missing_fields,
                    )
                    return to_export
            raise MissingEventDataException(
                f"Missing fields {missing_fields} for instruction request {request_id}"
            )

        return to_export

    def _export_data_for_smart_paste(
        self,
        request_id: str,
        tenant_id: str,
        event_time: datetime.datetime,
        smart_paste_resolution: request_insight_pb2.SmartPasteResolution,
    ) -> dict:
        """Gather all of the fields we want to export for smart paste resolutions."""
        event_types = [
            "instruction_host_request",
            "instruction_host_response",
            "request_metadata",
        ]
        events = self._read_events(tenant_id, request_id, event_types)
        if not events:
            raise MissingEventDataException(
                f"Missing any required events {event_types} for request {request_id}"
            )
        to_export = {
            "event_type": "smart_paste",
            "request_id": request_id,
            "feedback": {
                "is_accepted_chunks": list(smart_paste_resolution.is_accepted_chunks),
                "is_accept_all": smart_paste_resolution.is_accept_all,
                "is_reject_all": smart_paste_resolution.is_reject_all,
            },
        }
        for event in events:
            if event.HasField("instruction_host_request"):
                to_export["request"] = {
                    "code_block": event.instruction_host_request.request.code_block,
                    "target_file_path": event.instruction_host_request.request.target_file_path,
                    "target_file_content": event.instruction_host_request.request.target_file_content,
                }
            if event.HasField("instruction_host_response"):
                to_export["response"] = {
                    "replace_text": [
                        {
                            "text": response.text,
                            "start_line": response.start_line,
                            "end_line": response.end_line,
                            "old_text": response.old_text,
                        }
                        for response in event.instruction_host_response.response.replace_text
                    ],
                }
            if event.HasField("request_metadata"):
                to_export["user_id"] = event.request_metadata.user_id

        # Check if all of the data we want to export is in gcs yet
        required = ["feedback", "user_id"]
        # We may be missing these in some cases, like when smart paste fails because
        # they are too large. If we don't see these events after a certain
        # amount of time, assume it was because the smart paste was too large and
        # export them anyway.
        desired = ["request", "response"]
        missing_fields = [
            field for field in desired + required if field not in to_export
        ]
        if missing_fields:
            if not any([field in required for field in missing_fields]):
                # We are only missing request/response, check if this event is
                # old enough to export.
                threshold = datetime.datetime.now() - datetime.timedelta(
                    minutes=self.export_failed_edits_after_minutes
                )
                if event_time < threshold:
                    log.info(
                        "Exporting smart paste %s with missing fields %s, assuming it was a rejected smart paste",
                        request_id,
                        missing_fields,
                    )
                    return to_export
            raise MissingEventDataException(
                f"Missing fields {missing_fields} for smart paste request {request_id}"
            )

        return to_export

    def _export_data_for_chat(
        self,
        request_id: str,
        tenant_id: str,
        event_time: datetime.datetime,
        chat_feedback: request_insight_pb2.ChatFeedback,
    ) -> dict:
        """Gather all of the fields we want to export for chats."""
        event_types = [
            "chat_host_request",
            "chat_host_response",
            "request_metadata",
        ]
        events = self._read_events(tenant_id, request_id, event_types)
        if not events:
            raise MissingEventDataException(
                f"Missing any required events {event_types} for request {request_id}"
            )
        to_export = {
            "event_type": "chat",
            "request_id": request_id,
            "feedback": {
                "rating": request_insight_pb2.FeedbackRating.Name(chat_feedback.rating),
                "note": chat_feedback.note,
            },
        }
        for event in events:
            if event.HasField("chat_host_request"):
                to_export["request"] = {
                    "message": event.chat_host_request.request.message,
                    "path": event.chat_host_request.request.path,
                    "prefix": event.chat_host_request.request.prefix,
                    "selected_code": event.chat_host_request.request.selected_code,
                    "suffix": event.chat_host_request.request.suffix,
                    "position": {
                        "prefix_begin": event.chat_host_request.request.position.prefix_begin,
                        "suffix_end": event.chat_host_request.request.position.suffix_end,
                    },
                    "chat_history": [
                        {
                            "request_message": exchange.request_message,
                            "response_text": exchange.response_text,
                            "request_id": exchange.request_id,
                        }
                        for exchange in event.chat_host_request.request.chat_history
                    ],
                }
            if event.HasField("chat_host_response"):
                to_export["response"] = {
                    "text": event.chat_host_response.response.text,
                }
            if event.HasField("request_metadata"):
                to_export["user_id"] = event.request_metadata.user_id

        # Check if all of the data we want to export is in gcs yet
        required = ["feedback", "user_id"]
        # We may be missing these in some cases, like when chats fail because
        # they are too large. If we don't see these events after a certain
        # amount of time, assume it was because the chat was too large and
        # export them anyway.
        desired = ["request", "response"]
        missing_fields = [
            field for field in desired + required if field not in to_export
        ]
        if missing_fields:
            if not any([field in required for field in missing_fields]):
                # We are only missing request/response, check if this event is
                # old enough to export.
                threshold = datetime.datetime.now() - datetime.timedelta(
                    minutes=self.export_failed_edits_after_minutes
                )
                if event_time < threshold:
                    log.info(
                        "Exporting chat %s with missing fields %s, assuming it was a rejected chat",
                        request_id,
                        missing_fields,
                    )
                    return to_export
            raise MissingEventDataException(
                f"Missing fields {missing_fields} for chat request {request_id}"
            )

        return to_export

    def _export_data_for_chat_preference(
        self,
        sample_request_id: str,  # Request ID of the preference sample itself, not requests that participate in voting
        tenant_id: str,
        event_time: datetime.datetime,
        sample: request_insight_pb2.PreferenceSample,
    ) -> dict:
        sample_dict = MessageToDict(sample)
        if sample_dict is not None:
            assert isinstance(sample_dict, dict)
            scores = sample_dict["scores"]
        else:
            scores = {}

        to_export = {
            "event_type": "preference_sample",
            "request_id": sample_request_id,
            "scores": scores,
            "feedback": sample.feedback,
            "request_a": self._get_chat_request_info(
                tenant_id,
                sample.request_ids[0],
                event_time,
            ),
            "request_b": self._get_chat_request_info(
                tenant_id,
                sample.request_ids[1],
                event_time,
            ),
        }

        return to_export

    def _get_chat_request_info(
        self,
        tenant_id: str,
        request_id: str,
        event_time: datetime.datetime,
    ) -> dict:
        """Get the request info for a chat request."""
        event_types = [
            "chat_host_request",
            "chat_host_response",
            "request_metadata",
        ]
        events = self._read_events(tenant_id, request_id, event_types)
        if not events:
            raise MissingEventDataException(
                f"Missing any required events {event_types} for request {request_id}"
            )
        to_export: dict = {
            "event_type": "chat",
            "request_id": request_id,
        }
        for event in events:
            if event.HasField("chat_host_request"):
                chat_history = []
                for exchange in event.chat_host_request.request.chat_history:
                    chat_history.append(
                        {
                            "message": exchange.request_message,
                            "response": exchange.response_text,
                        }
                    )
                to_export["request"] = {
                    "path": event.chat_host_request.request.path,
                    "prefix": event.chat_host_request.request.prefix,
                    "selected_code": event.chat_host_request.request.selected_code,
                    "suffix": event.chat_host_request.request.suffix,
                    "message": event.chat_host_request.request.message,
                    "chat_history": chat_history,
                }
            if event.HasField("chat_host_response"):
                to_export["response"] = {
                    "text": event.chat_host_response.response.text,
                }
            if event.HasField("request_metadata"):
                to_export["user_id"] = event.request_metadata.user_id

        required = ["request", "response", "user_id"]
        missing_fields = [field for field in required if field not in to_export]
        if missing_fields:
            threshold = datetime.datetime.now() - datetime.timedelta(
                minutes=self.export_failed_edits_after_minutes
            )
            if event_time < threshold:
                log.info(
                    "Exporting chat %s with missing fields %s.",
                    request_id,
                    missing_fields,
                )
                return to_export
            raise MissingEventDataException(
                f"Missing fields {missing_fields} for chat request {request_id}"
            )

        return to_export

    def export(
        self, message_batch: Iterable[request_insight_pb2.RequestInsightMessage]
    ):
        """Export annotations from the given message batch.

        If we have a batch that fails and that we may want to retry later,
        either because all of the events are not in gcs or because the
        webhook URL is down, then raise an exception. The subscriber that this
        usually runs in will then retry those messages instead of acknowledging
        this batch.

        Note that this usually processes 1 message because of how we configured
        the corresponding subscriber.
        """
        batch_data = []
        for message in message_batch:
            if message.WhichOneof("message") != "update_request_info_request":
                log.debug("Ignoring message of type %s", message.WhichOneof("message"))
                continue

            request = message.update_request_info_request
            tenant_info = request.tenant_info
            for event in request.events:
                event_type = event.WhichOneof("event")
                if event_type is None:
                    log.info(
                        "Skipping missing or unrecognized event. This is expected during the rollout of a new event"
                    )
                    continue

                # Look for events with annotations
                if event_type == "completion_feedback":
                    log.info(
                        "Processing completion_feedback for %s", request.request_id
                    )
                    batch_data.append(
                        self._export_data_for_completion(
                            request.request_id,
                            tenant_info.tenant_id,
                            event.time.ToDatetime(),
                            event.completion_feedback,
                        )
                    )
                elif event_type == "edit_resolution":
                    log.info("Processing edit_resolution for %s", request.request_id)
                    batch_data.append(
                        self._export_data_for_edit(
                            request.request_id,
                            tenant_info.tenant_id,
                            event.time.ToDatetime(),
                            event.edit_resolution,
                        )
                    )
                elif event_type == "instruction_resolution":
                    log.info(
                        "Processing instruction_resolution for %s", request.request_id
                    )
                    batch_data.append(
                        self._export_data_for_instruction(
                            request.request_id,
                            tenant_info.tenant_id,
                            event.time.ToDatetime(),
                            event.instruction_resolution,
                        )
                    )
                elif event_type == "smart_paste_resolution":
                    log.info(
                        "Processing smart_paste_resolution for %s", request.request_id
                    )
                    batch_data.append(
                        self._export_data_for_smart_paste(
                            request.request_id,
                            tenant_info.tenant_id,
                            event.time.ToDatetime(),
                            event.smart_paste_resolution,
                        )
                    )
                elif event_type == "preference_sample":
                    log.info("Processing preference_sample for %s", request.request_id)
                    # Note(yuri): when we add preference collection for other features,
                    # we need to add some field to distinguish between preference types.
                    batch_data.append(
                        self._export_data_for_chat_preference(
                            request.request_id,
                            tenant_info.tenant_id,
                            event.time.ToDatetime(),
                            event.preference_sample,
                        )
                    )
                if event_type == "chat_feedback":
                    log.info("Processing chat_feedback for %s", request.request_id)
                    batch_data.append(
                        self._export_data_for_chat(
                            request.request_id,
                            tenant_info.tenant_id,
                            event.time.ToDatetime(),
                            event.chat_feedback,
                        )
                    )
                # TODO(aswin): If we keep failing to find the right events in
                # gcs for a request, should we eventually skip that request?
                # TODO(aswin): We will need to figure out how these interact
                # with subscriber alerts, since some errors are expected but not
                # too many.

        # Post everything at the end, then return so we ack the whole batch
        for data in batch_data:
            self._post_data(data)

{
  kind: 'SealedSecret',
  apiVersion: 'bitnami.com/v1alpha1',
  metadata: {
    name: 'annotation-exporter-webhook-auth',
    namespace: 'dev-yuri',
    creationTimestamp: null,
  },
  spec: {
    template: {
      metadata: {
        name: 'annotation-exporter-webhook-auth',
        namespace: 'dev-yuri',
        creationTimestamp: null,
      },
    },
    encryptedData: {
      'credentials.json': 'AgAecxGSbZBDATHYRLRVr0R7uo5++cxtXSlM0Zv0cVbaPQFHc+nZ/f/4sGlNuRIfPu2B+kQomUJR3CLHyy9K8NVsGIgNes8EtOVhWPltFjxXP/IkcQALXZic8PA9/IX9Tmt5o6nsPOjTdDn+5SM71aZXKmzqBdqtapL4GwG6k8Eesp+LsLI2YhW9YCwbxcqs1/xD3W320OgZDG5EznsFgkvBGklx53THd7KUt+bZ1jn+B7IEiYgjuoqYpucTRzG55Ou7kC+JavVRKQZgygGMGye1gKkp3M2zT91acMCLXnPOQZV4iTopiXpdxbIOVr6yViqkFugaevn8Zf+4Tr/2PEFZxc0826J12JD80Ncaz79CCjU8X0yHcAyNnZ/fZWCVR4RkgU8qilvTFEbfR2AeUi3Ogee3GUv19KVYD/5wkD7bCvb4ThTFakYDMGmi3eRQMThWj4LU86HoX272YWZZ+k+7XiuBASHtO5FK4dHoNL4syJ4KLrViBeECuCCODc6KhR106hZ2syce49vFp58Ir+svZ7AVa/DBwFw22R6W6IAG/3qlKuj4T1lYYS/NkxzW4rnXKsSRGEBjxATLIHc2kkzkv09YxnBefT5vRHCX5xtiZZnTYlnmY4ng1a6VVkRMBr4f/SQXQSC2J91+2/wckrwLVb7IsIuO2XBe2z44bSDkBk0zx8lieyTRgey83JfYFWRYsC+YGg2pnHVvO1zG/RlQVK2lcB2jyEAIyfkf24Q=',
    },
  },
}

{
  kind: 'SealedSecret',
  apiVersion: 'bitnami.com/v1alpha1',
  metadata: {
    name: 'annotation-exporter-webhook-auth',
    namespace: 'aitutor-pareto',
    creationTimestamp: null,
  },
  spec: {
    template: {
      metadata: {
        name: 'annotation-exporter-webhook-auth',
        namespace: 'aitutor-pareto',
        creationTimestamp: null,
      },
    },
    encryptedData: {
      'credentials.json': 'AgBLM7H6mvlpMtaRBgwQuGJC7CJ0zVyafs4c/u8vKqYmm04+TXaavNN0fQd6/BiQmSFZNG83Rtfkbhf3IFwOXOu5g/fnR1akO1wF9Lp7IAyUshf7NPkCM1KivI9xCK+4o3tEaDSvjQDsHujdouI3085WyYvi70U/hrOkE5y2ZGTcCy3kDsKKZIaaVeCk5E5cy2WW4dmpcXiE/IfD/qwrDoQyf8dbK4CkmJkqC9WZ2CywKsMljCMagNPlENIybUsyo4sj4Oh/GeaJumCPcHSj8uDp+hloXKD9xsVBxV44pZ5IJzeeO7c56KN+1SMBL7T5oq0pGQiO2eJbIm03d7iiTkqcuFYLXWkJAWJ6xKpUJg+oTuEzxRlAtv7BoZ8LdiW9WWxbrScD/mZAWcAcTR7SjflVgReeA1GlDtVDbY9AymAhWZvQ037zgYNx1y7RvTpUR8mlSojf7EdRRwHnYJjwm9tA38tJTZ+DCqHiSEZ5gpya2L9JMAXuhvEql0wrX9B1wLHmYoTT4Lx/QzbLpoEGbRtS7jUE6NYUdUBTZjhOfbioznxpQvTFm6YRjB46V7Z0tx5FuxdJnTBRyj/8k3G8gzUzKRQz8OyOBeSdrGksJouX/04e2PK0vsswXI49rOOsYX5/jDgaUi1e+JzjP5/vuM2oOtoWAZhoj1c78n+euCa2nu/qKWO5g1OvyRl/LWQH/aN4PUJk1MJRBinTiyor+SbgvBAskBJpEFEUNENVhffYjxB5+z3bHQHoc7QuiZRn6A69NOQcAtMeZ3d7Xj5V5vglkKzdIW9qMX7b3kA0j1NX1fMwbXcQ9l+q4+n5K6x9Z/o=',
    },
  },
}

"""Exporter for exporting annotations from full request insight event data to vendors.

This code should only be deployed for vendors, who want to see the annotations
that we have received from them for billing, etc.

This needs access to the request insight central service so that it can get other
event data whenever it sees an annotation. This is because we only want to
export request and response data for completions/edits/chats with annotations.
"""

import argparse
import os
import pathlib
from dataclasses import dataclass
from functools import partial
from typing import Iterable, Optional

import structlog
from dataclasses_json import dataclass_json
from prometheus_client import start_http_server

from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import StandardSignalHandler
from services.lib.grpc.tls_config.tls_config import ClientConfig, get_client_tls_creds
from services.request_insight import request_insight_pb2
from services.request_insight.annotation_exporter.annotation_exporter import (
    AnnotationExporter,
)
from services.request_insight.central.client.client import RequestInsightCentralClient
from services.request_insight.lib.request_insight_subscriber import (
    RequestInsightSubscriber,
)
from services.token_exchange.client.client import GrpcTokenExchangeClient

log = structlog.get_logger()


@dataclass_json
@dataclass
class Config:
    """Configuration for the request insight annotation exporter."""

    project_id: str
    """The project id of the pub/sub topic/subscription."""

    topic_name: str
    """The name of the pub/sub topic to listen to."""

    subscription_name: str
    """The name of the subscription to register, as it exists in GCP."""

    subscription_description: str
    """A description of the subscription. Used for metrics."""

    subscription_batch_size: int
    """The batch size to use for fetching subscription messages."""

    webhook_url: str
    """The webhook URL to send the annotations to."""

    webhook_auth_file: str | None
    """Path to a file with credentials for authenticating to the webhook URL"""

    request_insight_central_endpoint: str
    """The endpoint for the request insight central service."""

    token_exchange_endpoint: str
    """The endpoint for the token exchange service."""

    client_mtls: Optional[ClientConfig] = None
    """MTLS config for client, if enabled."""

    central_client_mtls: Optional[ClientConfig] = None
    """MTLS config for central client, if enabled."""

    export_failed_edits_after_minutes: int = 4 * 60
    """Export edits/chats without request or response events after this many minutes.

    Currently there is an edge case where edits/chats with requests that are too
    large can fail to send request and response events to RI. We still want to
    export these, though. Since we don't know from RI if this was the exact
    error, automatically do this for edits/chats that don't have a request+response
    after this much time.
    """


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # pylint: disable=no-member # type: ignore
        config_file.read_text()
    )


def process_batch(
    message_batch: Iterable[request_insight_pb2.RequestInsightMessage],
    exporter: AnnotationExporter,
):
    """Process a single batch of request insight messages."""
    exporter.export(message_batch)


def main():
    _ = StandardSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    parser.add_argument(
        "--health-file",
        type=pathlib.Path,
        help="Path to a file to write a health check to",
    )
    args = parser.parse_args()
    log.info("Args %s", args)

    config = _load_config(args.config_file)
    log.info("Config: %s", config)

    start_http_server(9090)

    # Increase the max message size to 32MB.
    max_message_size = 32 * 1024 * 1024
    ri_central_client = RequestInsightCentralClient.create_for_endpoint(
        config.request_insight_central_endpoint,
        get_client_tls_creds(config.central_client_mtls),
        [("grpc.max_receive_message_length", max_message_size)],
    )

    token_exchange_client = GrpcTokenExchangeClient.create(
        config.token_exchange_endpoint,
        os.environ["POD_NAMESPACE"],
        get_client_tls_creds(config.central_client_mtls),
    )

    exporter = AnnotationExporter(
        ri_central_client,
        token_exchange_client,
        config.webhook_url,
        config.webhook_auth_file,
        config.export_failed_edits_after_minutes,
    )

    subscriber = RequestInsightSubscriber(
        config.project_id,
        config.topic_name,
        config.subscription_name,
        config.subscription_description,
        config.subscription_batch_size,
        health_file=args.health_file,
    )
    subscriber.run(
        partial(
            process_batch,
            exporter=exporter,
        )
    )


if __name__ == "__main__":
    main()

{
  kind: 'SealedSecret',
  apiVersion: 'bitnami.com/v1alpha1',
  metadata: {
    name: 'annotation-exporter-webhook-auth',
    namespace: 'aitutor-turing',
    creationTimestamp: null,
  },
  spec: {
    template: {
      metadata: {
        name: 'annotation-exporter-webhook-auth',
        namespace: 'aitutor-turing',
        creationTimestamp: null,
      },
    },
    encryptedData: {
      'credentials.json': '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',
    },
  },
}

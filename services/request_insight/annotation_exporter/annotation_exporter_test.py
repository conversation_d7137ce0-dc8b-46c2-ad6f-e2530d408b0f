"""Unit tests for the annotation exporter."""

import datetime
import uuid
from unittest.mock import MagicMock

import pydantic
import pytest
import requests
from google.protobuf import timestamp_pb2

import services.chat_host.chat_pb2 as chat_pb2
import services.edit_host.edit_pb2 as edit_pb2
import services.request_insight.central.request_insight_central_pb2 as ri_central_pb2
import services.request_insight.request_insight_pb2 as request_insight_pb2
from services.request_insight.annotation_exporter.annotation_exporter import (
    AnnotationExporter,
    MissingEventDataException,
)
from services.request_insight.central.client.client import RequestInsightCentralClient
from services.token_exchange.client.client import TokenExchangeClient


def _create_event_message(**kwargs) -> request_insight_pb2.RequestInsightMessage:
    """Create a request insight message with the given events."""
    if "time" not in kwargs:
        kwargs["time"] = timestamp_pb2.Timestamp().FromDatetime(datetime.datetime.now())
    return request_insight_pb2.RequestInsightMessage(
        update_request_info_request=request_insight_pb2.UpdateRequestInfoRequest(
            request_id=str(uuid.uuid4()),
            events=[request_insight_pb2.RequestEvent(**kwargs)],
        )
    )


def _gcs_missing(ri_central_client):
    """This is not enough to send an annotation, we need request + response + metadata."""
    ri_central_client.get_request_events = MagicMock(
        side_effect=[
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        completion_host_response=request_insight_pb2.CompletionHostResponse(
                            text="completion",
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        edit_host_request=request_insight_pb2.RIEditRequest(
                            request=edit_pb2.EditRequest(
                                path="test-path",
                            ),
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        chat_host_request=request_insight_pb2.RIChatRequest(
                            request=chat_pb2.ChatRequest(
                                path="test-path",
                            ),
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        instruction_host_request=request_insight_pb2.RIInstructionRequest(
                            request=edit_pb2.InstructionRequest(
                                path="test-path",
                            ),
                        ),
                    ),
                ),
            ],
        ]
    )


def _gcs_success(ri_central_client):
    """This is enough to send an annotation, request + response + metadata."""
    ri_central_client.get_request_events = MagicMock(
        side_effect=[
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        completion_host_request=request_insight_pb2.CompletionHostRequest(
                            path="test-path",
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        completion_host_response=request_insight_pb2.CompletionHostResponse(
                            text="completion"
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user1",
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        edit_host_request=request_insight_pb2.RIEditRequest(
                            request=edit_pb2.EditRequest(
                                path="test-path",
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        edit_host_response=request_insight_pb2.RIEditResponse(
                            response=edit_pb2.EditResponse(text="new code"),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user2",
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        chat_host_request=request_insight_pb2.RIChatRequest(
                            request=chat_pb2.ChatRequest(
                                path="test-path",
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        chat_host_response=request_insight_pb2.RIChatResponse(
                            response=chat_pb2.ChatResponse(text="new code"),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user3",
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        chat_host_request=request_insight_pb2.RIChatRequest(
                            request=chat_pb2.ChatRequest(
                                path="test-path",
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        chat_host_response=request_insight_pb2.RIChatResponse(
                            response=chat_pb2.ChatResponse(text="new code"),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user3",
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        chat_host_request=request_insight_pb2.RIChatRequest(
                            request=chat_pb2.ChatRequest(
                                path="test-path",
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        chat_host_response=request_insight_pb2.RIChatResponse(
                            response=chat_pb2.ChatResponse(text="some great response"),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user4",
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        instruction_host_request=request_insight_pb2.RIInstructionRequest(
                            request=edit_pb2.InstructionRequest(
                                path="test-path",
                                selected_text="my_selected_text",
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        instruction_host_response=request_insight_pb2.RIInstructionResponse(
                            response=edit_pb2.InstructionAggregateResponse(
                                replace_text=[
                                    edit_pb2.ReplaceText(
                                        text="new code",
                                        start_line=1,
                                        end_line=1,
                                        old_text="old code",
                                    )
                                ],
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user5",
                        ),
                    ),
                ),
            ],
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        instruction_host_request=request_insight_pb2.RIInstructionRequest(
                            request=edit_pb2.InstructionRequest(
                                code_block="my_code_block",
                                target_file_path="my_target_file_path",
                                target_file_content="my_target_file_content",
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        instruction_host_response=request_insight_pb2.RIInstructionResponse(
                            response=edit_pb2.InstructionAggregateResponse(
                                replace_text=[
                                    edit_pb2.ReplaceText(
                                        text="new code",
                                        start_line=1,
                                        end_line=1,
                                        old_text="old code",
                                    )
                                ],
                            ),
                        ),
                    ),
                ),
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user6",
                        ),
                    ),
                ),
            ],
        ],
    )


def test_annotation_exporter_failure():
    """Test the annotation exporter fails when we should retry later.

    This is important because exceptions are caught by the pubsub subscriber,
    which will then retry processing that batch of messages rather than acking
    them.
    """
    ri_central_client = MagicMock(spec=RequestInsightCentralClient)
    token_exchange_client = MagicMock(spec=TokenExchangeClient)
    token_exchange_client.get_signed_token_for_service.return_value = (
        pydantic.SecretStr("test-token")
    )
    webhook_url = "https://test.augmentcode.com"
    exporter = AnnotationExporter(
        ri_central_client,
        token_exchange_client,
        webhook_url,
        webhook_auth_file=None,
        export_failed_edits_after_minutes=100,
    )
    exporter.session = MagicMock()
    exporter.session.post.return_value.status_code = 400

    messages = [
        # Should be ignored
        _create_event_message(
            completion_host_request=request_insight_pb2.CompletionHostRequest(),
        ),
        # Should be exported
        _create_event_message(
            completion_feedback=request_insight_pb2.CompletionFeedback(
                rating=request_insight_pb2.FeedbackRating.POSITIVE,
                note="best completion ever",
            ),
        ),
        # Should be ignored
        _create_event_message(
            edit_host_response=request_insight_pb2.RIEditResponse(),
        ),
        # Should be exported
        _create_event_message(
            edit_resolution=request_insight_pb2.EditResolution(
                is_accepted=True,
                annotated_text="best edit ever",
                annotated_instruction="fix my code",
            ),
        ),
        # Should be ignored
        _create_event_message(
            chat_host_response=request_insight_pb2.RIChatResponse(
                response=chat_pb2.ChatResponse(text="some great response"),
            ),
        ),
        # Should be exported
        _create_event_message(
            chat_feedback=request_insight_pb2.ChatFeedback(
                rating=request_insight_pb2.FeedbackRating.POSITIVE,
                note="best response ever",
            ),
        ),
    ]

    _gcs_missing(ri_central_client)
    with pytest.raises(MissingEventDataException):
        exporter.export(messages)
    assert exporter.session.post.call_count == 0
    assert ri_central_client.get_request_events.call_count == 1

    # First check that we propagate an error if the webhook fails
    _gcs_success(ri_central_client)
    with pytest.raises(requests.exceptions.HTTPError):
        exporter.export(messages)

    assert exporter.session.post.call_count == 1
    assert ri_central_client.get_request_events.call_count == 3


def test_annotation_exporter_success():
    """Test the normal success case for the annotation exporter."""
    ri_central_client = MagicMock(spec=RequestInsightCentralClient)
    token_exchange_client = MagicMock(spec=TokenExchangeClient)
    token_exchange_client.get_signed_token_for_service.return_value = (
        pydantic.SecretStr("test-token")
    )
    webhook_url = "https://test.augmentcode.com"
    exporter = AnnotationExporter(
        ri_central_client,
        token_exchange_client,
        webhook_url,
        webhook_auth_file=None,
        export_failed_edits_after_minutes=100,
    )
    exporter.session = MagicMock()
    exporter.session.post.return_value.status_code = 200

    test_uuid1 = str(uuid.uuid4())
    test_uuid2 = str(uuid.uuid4())

    messages = [
        # Should be ignored
        _create_event_message(
            completion_host_request=request_insight_pb2.CompletionHostRequest(),
        ),
        # Should be exported
        _create_event_message(
            completion_feedback=request_insight_pb2.CompletionFeedback(
                rating=request_insight_pb2.FeedbackRating.POSITIVE,
                note="best completion ever",
            ),
        ),
        # Should be ignored
        _create_event_message(
            edit_host_response=request_insight_pb2.RIEditResponse(),
        ),
        # Should be exported
        _create_event_message(
            edit_resolution=request_insight_pb2.EditResolution(
                is_accepted=True,
                annotated_text="best edit ever",
                annotated_instruction="fix my code",
            ),
        ),
        # Should be exported
        _create_event_message(
            preference_sample=request_insight_pb2.PreferenceSample(
                request_ids=[test_uuid1, test_uuid2],
                scores={"score1": 1, "score2": 2},
                feedback="some feedback",
            ),
        ),
        # Should be ignored
        _create_event_message(
            chat_host_response=request_insight_pb2.RIChatResponse(
                response=chat_pb2.ChatResponse(text="some great response"),
            ),
        ),
        # Should be exported
        _create_event_message(
            chat_feedback=request_insight_pb2.ChatFeedback(
                rating=request_insight_pb2.FeedbackRating.POSITIVE,
                note="best response ever",
            ),
        ),
        # Should be exported
        _create_event_message(
            instruction_resolution=request_insight_pb2.InstructionResolution(
                is_accepted_chunks=[True, True],
                is_accept_all=True,
                is_reject_all=False,
            ),
        ),
        # Should be exported
        _create_event_message(
            smart_paste_resolution=request_insight_pb2.SmartPasteResolution(
                is_accepted_chunks=[True, True],
                is_accept_all=True,
                is_reject_all=False,
            ),
        ),
    ]

    _gcs_success(ri_central_client)
    exporter.export(messages)

    assert exporter.session.post.call_count == 6
    print(exporter.session.post.call_args_list[0])
    assert exporter.session.post.call_args_list[0][0][0] == webhook_url
    assert exporter.session.post.call_args_list[0][1]["json"] == {
        "payload": {
            "event_type": "completion",
            "user_id": "user1",
            "request_id": str(messages[1].update_request_info_request.request_id),
            "request": {
                "path": "test-path",
                "position": {
                    "prefix_begin": 0,
                    "cursor_position": 0,
                    "suffix_end": 0,
                },
                "prefix": "",
                "suffix": "",
                "file_count": 0,
            },
            "response": {
                "text": "completion",
                "skipped_suffix": "",
                "suffix_replacement_text": "",
            },
            "feedback": {
                "rating": "POSITIVE",
                "note": "best completion ever",
            },
        },
    }
    assert exporter.session.post.call_args_list[1][0][0] == webhook_url
    assert exporter.session.post.call_args_list[1][1]["json"] == {
        "payload": {
            "event_type": "edit",
            "user_id": "user2",
            "request_id": str(messages[3].update_request_info_request.request_id),
            "request": {
                "path": "test-path",
                "prefix": "",
                "selected_text": "",
                "suffix": "",
                "position": {
                    "prefix_begin": 0,
                    "suffix_end": 0,
                },
                "instruction": "",
                "file_count": 0,
            },
            "response": {
                "text": "new code",
            },
            "feedback": {
                "is_accepted": True,
                "annotated_text": "best edit ever",
                "annotated_instruction": "fix my code",
            },
        },
    }
    assert exporter.session.post.call_args_list[2][0][0] == webhook_url
    assert exporter.session.post.call_args_list[2][1]["json"] == {
        "payload": {
            "event_type": "preference_sample",
            "request_id": str(messages[4].update_request_info_request.request_id),
            "scores": {"score1": 1, "score2": 2},
            "feedback": "some feedback",
            "request_a": {
                "event_type": "chat",
                "request_id": test_uuid1,
                "user_id": "user3",
                "request": {
                    "path": "test-path",
                    "prefix": "",
                    "selected_code": "",
                    "suffix": "",
                    "message": "",
                    "chat_history": [],
                },
                "response": {
                    "text": "new code",
                },
            },
            "request_b": {
                "event_type": "chat",
                "request_id": test_uuid2,
                "user_id": "user3",
                "request": {
                    "path": "test-path",
                    "prefix": "",
                    "selected_code": "",
                    "suffix": "",
                    "message": "",
                    "chat_history": [],
                },
                "response": {
                    "text": "new code",
                },
            },
        },
    }
    assert exporter.session.post.call_args_list[3][0][0] == webhook_url
    assert exporter.session.post.call_args_list[3][1]["json"] == {
        "payload": {
            "event_type": "chat",
            "user_id": "user4",
            "request_id": str(messages[6].update_request_info_request.request_id),
            "request": {
                "message": "",
                "path": "test-path",
                "position": {
                    "prefix_begin": 0,
                    "suffix_end": 0,
                },
                "prefix": "",
                "suffix": "",
                "chat_history": [],
                "selected_code": "",
            },
            "response": {
                "text": "some great response",
            },
            "feedback": {
                "rating": "POSITIVE",
                "note": "best response ever",
            },
        },
    }
    assert exporter.session.post.call_args_list[4][0][0] == webhook_url
    assert exporter.session.post.call_args_list[4][1]["json"] == {
        "payload": {
            "event_type": "instruction",
            "user_id": "user5",
            "request_id": str(messages[7].update_request_info_request.request_id),
            "request": {
                "path": "test-path",
                "prefix": "",
                "selected_text": "my_selected_text",
                "suffix": "",
                "position": {
                    "prefix_begin": 0,
                    "suffix_end": 0,
                },
                "instruction": "",
                "file_count": 0,
            },
            "response": {
                "replace_text": [
                    {
                        "text": "new code",
                        "start_line": 1,
                        "end_line": 1,
                        "old_text": "old code",
                    }
                ],
            },
            "feedback": {
                "is_accepted_chunks": [True, True],
                "is_accept_all": True,
                "is_reject_all": False,
            },
        },
    }
    assert exporter.session.post.call_args_list[5][0][0] == webhook_url
    assert exporter.session.post.call_args_list[5][1]["json"] == {
        "payload": {
            "event_type": "smart_paste",
            "user_id": "user6",
            "request_id": str(messages[8].update_request_info_request.request_id),
            "request": {
                "code_block": "my_code_block",
                "target_file_path": "my_target_file_path",
                "target_file_content": "my_target_file_content",
            },
            "response": {
                "replace_text": [
                    {
                        "text": "new code",
                        "start_line": 1,
                        "end_line": 1,
                        "old_text": "old code",
                    }
                ],
            },
            "feedback": {
                "is_accepted_chunks": [True, True],
                "is_accept_all": True,
                "is_reject_all": False,
            },
        },
    }
    assert ri_central_client.get_request_events.call_count == 7


@pytest.mark.parametrize("export", [False, True])
def test_annotation_exporter_edit_with_missing_response(
    export,
):
    """Test the edge case where an edit request/response is missing."""
    ri_central_client = MagicMock(spec=RequestInsightCentralClient)
    token_exchange_client = MagicMock(spec=TokenExchangeClient)
    token_exchange_client.get_signed_token_for_service.return_value = (
        pydantic.SecretStr("test-token")
    )
    webhook_url = "https://test.augmentcode.com"
    exporter = AnnotationExporter(
        ri_central_client,
        token_exchange_client,
        webhook_url,
        webhook_auth_file=None,
        export_failed_edits_after_minutes=100,
    )
    exporter.session = MagicMock()
    exporter.session.post.return_value.status_code = 200

    # Old events should be exported, new ones should not
    age = 200 if export else 1
    event_time = timestamp_pb2.Timestamp()
    event_time.FromDatetime(datetime.datetime.now() - datetime.timedelta(minutes=age))
    messages = [
        _create_event_message(
            edit_resolution=request_insight_pb2.EditResolution(
                is_accepted=True,
                annotated_text="best edit ever",
                annotated_instruction="fix my code",
            ),
            time=event_time,
        ),
    ]
    print(messages)

    ri_central_client.get_request_events = MagicMock(
        side_effect=[
            [
                ri_central_pb2.GetRequestEventResponse(
                    request_event=request_insight_pb2.RequestEvent(
                        request_metadata=request_insight_pb2.RequestMetadata(
                            user_id="user2",
                        ),
                    ),
                ),
            ],
        ]
    )

    if not export:
        with pytest.raises(MissingEventDataException):
            exporter.export(messages)
        # We should not export this edit
        assert exporter.session.post.call_count == 0
        return

    # We should export this edit
    exporter.export(messages)
    assert exporter.session.post.call_count == 1
    print(exporter.session.post.call_args_list[0])
    assert exporter.session.post.call_args_list[0][0][0] == webhook_url
    assert exporter.session.post.call_args_list[0][1]["json"] == {
        "payload": {
            "event_type": "edit",
            "user_id": "user2",
            "request_id": str(messages[0].update_request_info_request.request_id),
            "feedback": {
                "is_accepted": True,
                "annotated_text": "best edit ever",
                "annotated_instruction": "fix my code",
            },
        },
    }
    assert ri_central_client.get_request_events.call_count == 1

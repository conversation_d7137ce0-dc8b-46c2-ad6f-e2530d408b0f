// Shared utilities and resources across RI's BigQuery datasets.
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';

local datasetLocation(cloud) =
  local cloudToBigQueryLocation = {
    GCP_US_CENTRAL1_DEV: 'us',
    GCP_US_CENTRAL1_PROD: 'us',
    GCP_EU_WEST4_PROD: 'eu',
  };
  assert std.objectHas(cloudToBigQueryLocation, cloud);
  cloudToBigQueryLocation[cloud]
;

// These policy tags unfortunately can't be created with ConfigConnector. See
// experimental/aswin/gcp_bigquery_column_access.py for an example of how to create new tags, if you
// need to.
local dataAccessPolicyTag(cloud, env, tagType) =
  assert tagType == 'pii' || tagType == 'userId' : 'invalid tag type';
  local projectId = cloudInfo[cloud].projectId;
  local location = datasetLocation(cloud);
  if location == 'us' then (
    if env == 'DEV' || cloud == 'GCP_US_CENTRAL1_DEV' then
      {
        pii: 'projects/%s/locations/us/taxonomies/1201997834172086043/policyTags/7351303197363698806' % projectId,
        userId: 'projects/%s/locations/us/taxonomies/1201997834172086043/policyTags/8131178717288761919' % projectId,
      }[tagType]
    else if env == 'STAGING' then
      {
        pii: 'projects/%s/locations/us/taxonomies/5017688217967669151/policyTags/8442195160934318242' % projectId,
        userId: 'projects/%s/locations/us/taxonomies/5017688217967669151/policyTags/7380136495769548948' % projectId,
      }[tagType]
    else if env == 'PROD' then
      {
        pii: 'projects/%s/locations/us/taxonomies/242436394322015300/policyTags/6502995931124063241' % projectId,
        userId: 'projects/%s/locations/us/taxonomies/242436394322015300/policyTags/679639581158242064' % projectId,
      }[tagType]
    else
      null
  )
  else if location == 'eu' then (
    if env == 'STAGING' then
      {
        pii: 'projects/%s/locations/eu/taxonomies/8501088290626747824/policyTags/1920385538587791298' % projectId,
        userId: 'projects/%s/locations/eu/taxonomies/8501088290626747824/policyTags/4288285734167591569' % projectId,
      }[tagType]
    else if env == 'PROD' then
      {
        pii: 'projects/%s/locations/eu/taxonomies/4733902820872981551/policyTags/2222566099571445331' % projectId,
        userId: 'projects/%s/locations/eu/taxonomies/4733902820872981551/policyTags/6169970122929403929' % projectId,
      }[tagType]
    else
      null
  )
  else null
;

// All tables are clustered by tenant_id. Set additionalClustering to cluster by additional columns,
// after tenant_id.
local tableDefinition(
  name,
  description,
  schema,
  enableClustering=true,
  additionalClustering=[],
  enableTimePartitioning=true,
  partitionExpirationDays=null
      ) =
  // Attempt to check that name is in snake_case
  assert std.asciiLower(name) == name;
  assert std.length(std.findSubstr(name, '-')) == 0;
  assert std.length(std.findSubstr(name, ' ')) == 0;
  // This should be kept in sync with the structs in base/test_utils/bigquery/emulator.go.
  {
    name: name,
    description: description,
    schema: schema,

    // By default our tables are partitioned daily, based on the `time` column. Partitions are
    // immutable; if we want to change the partition of a table we need to create a new table and
    // copy the old data.
    // This field should match the ConfigConnector BigQueryTable schema.
    // BigQuery partitioning docs: https://cloud.google.com/bigquery/docs/partitioned-tables
    timePartitioning: if enableTimePartitioning then {
      field: 'time',
      type: 'DAY',

      // Setting this to false means that queries are not required to filter on the partition. It
      // is still best practice to do so.
      requirePartitionFilter: false,

      expirationMs: if partitionExpirationDays != null then partitionExpirationDays * 24 * 60 * 60 * 1000 else null,
    } else null,

    // Cluster tables by tenant id. This essentially creates an index so that looking up data for
    // a particular tenant is faster. We cluster by tenant id instead of by tenant name because
    // tenant names will not always be unique and will someday be allowed to change. Clusters are
    // mutable, but changes will only apply to new data.
    // This field should match the ConfigConnector BigQueryTable schema.
    // BigQuery clustering docs: https://cloud.google.com/bigquery/docs/clustered-tables
    clustering: if enableClustering then ['tenant_id'] + additionalClustering else null,
  };

local viewDefinition(name, description, query) =
  // Attempt to check that name is in snake_case
  assert std.asciiLower(name) == name;
  assert std.length(std.findSubstr(name, '-')) == 0;
  assert std.length(std.findSubstr(name, ' ')) == 0;
  // This should be kept in sync with the structs in base/test_utils/bigquery/emulator.go.
  {
    name: name,
    description: description,
    viewQuery: query,
  };

// Get the definition for a materialized view. A materialized view is one that actually stores the
// view in its own table, as opposed to just wrapping around a query. There are a lot of subtleties
// to incremental vs. non-incremental materialized views; see the BigQuery docs:
// https://cloud.google.com/bigquery/docs/materialized-views-intro
local materializedViewDefinition(name, description, query, isIncremental, timePartitioned=true) =
  // Attempt to check that name is in snake_case
  assert std.asciiLower(name) == name;
  assert std.length(std.findSubstr(name, '-')) == 0;
  assert std.length(std.findSubstr(name, ' ')) == 0;
  // This should be kept in sync with the structs in base/test_utils/bigquery/emulator.go.
  {
    name: name,
    description: description,
    materializedViewQuery: query,
    isIncremental: isIncremental,

    // Same partitioning and clustering as the base tables.
    timePartitioning: if timePartitioned then {
      field: 'time',
      type: 'DAY',
      requirePartitionFilter: false,
    } else null,
    clustering: [
      'tenant_id',
    ],
  };

// Get a deployable table from a table definition returned by tableDefinition, viewDefinition, or
// materializedViewDefinition.
local createTable(def, datasetResourceId, namespace, appName, tablePrefix) =
  local isView = std.objectHas(def, 'viewQuery');
  local isMaterializedView = std.objectHas(def, 'materializedViewQuery');
  local isTable = !isView && !isMaterializedView;
  {
    apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
    kind: 'BigQueryTable',
    metadata: {
      name: '%s-%s-%s' % [tablePrefix, std.strReplace(def.name, '_', '-'), if isTable then 'table' else 'view'],
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: def.description,
      // resourceID is the name the table will have in GCP. (Kubernetes doesn't allow names with
      // underscores and BigQuery doesn't allow names with dashes.)
      resourceID: def.name,
      datasetRef: {
        external: datasetResourceId,
      },
      schema: if isTable then std.toString(def.schema) else null,
      view: if isView then {
        query: def.viewQuery,
        // Legacy SQL doesn't support views with JSON columns.
        useLegacySql: false,
      } else null,
      materializedView: if isMaterializedView then {
        query: def.materializedViewQuery,
        allowNonIncrementalDefinition: !def.isIncremental,
      } else null,
      timePartitioning: if std.objectHas(def, 'timePartitioning') then def.timePartitioning else null,
      clustering: if std.objectHas(def, 'clustering') then def.clustering else null,
    },
  }
;

local createDataset(datasetName, datasetResourceId, location, access, cloud, env, namespace, appName) =
  {
    apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
    kind: 'BigQueryDataset',
    metadata: {
      name: datasetName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      description: 'Request Insight BigQuery dataset for %s' % env,
      // resourceID is the name the dataset will have in GCP. (Kubernetes doesn't allow names with
      // underscores and BigQuery doesn't allow names with dashes.)
      resourceID: datasetResourceId,
      location: std.asciiUpper(location),
      projectRef: {
        external: cloudInfo[cloud].projectId,
      },
      // BigQueryDataset doesn't support IAM and uses legacy role formats. See
      // https://cloud.google.com/config-connector/docs/reference/resource-docs/bigquery/bigquerydataset
      access: access,
    },
  }
;

local datasetAccess(namespace, appName, groupName, groupNamespace, serviceAccountEmail) =
  {
    apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
    kind: 'CloudIdentityMembership',
    metadata: {
      name: '%s-%s-%s-membership' % [namespace, appName, groupName],
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      groupRef: {
        name: groupName,
        namespace: groupNamespace,
      },
      preferredMemberKey: {
        id: serviceAccountEmail,
      },
      roles: [{ name: 'MEMBER' }],
    },
  };

{
  datasetLocation:: datasetLocation,
  dataAccessPolicyTag:: dataAccessPolicyTag,
  tableDefinition:: tableDefinition,
  viewDefinition:: viewDefinition,
  materializedViewDefinition:: materializedViewDefinition,
  createTable:: createTable,
  createDataset:: createDataset,
  datasetAccess:: datasetAccess,
}

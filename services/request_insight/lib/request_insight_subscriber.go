package subscriber

import (
	"context"
	"fmt"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/proto"

	"github.com/augmentcode/augment/base/go/clock"
	sub "github.com/augmentcode/augment/services/lib/pubsub"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
)

// This is a thin wrapper around our subscriber library, which is itself a wrapper around Google's
// pub/sub library. RI exporters all share some logic and metrics that make it worth having an
// additional layer.
type RequestInsightSubscriber struct {
	subClient        sub.SubscribeClient
	processFunc      ProcessMessage
	subscriptionName string
	latencyHistogram *prometheus.HistogramVec
	clock            clock.Clock
}

// Function to process a single message from the Request Insight pub/sub queue. This should do the
// processing synchronously and return an error if it fails.
type ProcessMessage func(ctx context.Context, message *pb.RequestInsightMessage) error

// RequestInsightSubscriber constructor.
func New(
	ctx context.Context,
	config *sub.SubscribeClientConfig,
	processFunc ProcessMessage,
) (*RequestInsightSubscriber, error) {
	latencyHistogram := prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "au_subscription_latency",
			Help:    "Latency to process a pub/sub message",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"subscription", "status"},
	)
	err := prometheus.Register(latencyHistogram)
	if err != nil {
		return nil, fmt.Errorf("Failed to register latency histogram: %v", err)
	}

	subClient, err := sub.NewSubscribeClient(ctx, config)
	if err != nil {
		return nil, err
	}

	return &RequestInsightSubscriber{
		subClient:        subClient,
		processFunc:      processFunc,
		subscriptionName: config.SubscriptionId,
		latencyHistogram: latencyHistogram,
		clock:            clock.NewRealClock(),
	}, nil
}

// Clean up RequestInsightSubscriber resources.
func (s *RequestInsightSubscriber) Close() {
	s.subClient.Close()
}

// Run the subscriber. This doesn't return unless something goes wrong receiving from the pub/sub
// queue, in which case it's treated as a fatal error.
// Fundamentally this is just a wrapper around calling `processFunc` on every message and recording
// metrics.
// Failed messages are left to be retried according to the subscription's configuration.
// Messages will be processed in parallel according to s.subClient's configuration.
func (s *RequestInsightSubscriber) Run(ctx context.Context) {
	err := s.subClient.Receive(ctx, func(ctx context.Context, data []byte) error {
		startTime := s.clock.Now()

		var protoMessage pb.RequestInsightMessage
		err := proto.Unmarshal(data, &protoMessage)
		if err != nil {
			log.Error().Err(err).Msg("Failed to unmarshal protobuf message")
			s.recordLatency(s.clock.Since(startTime), "PARSE_ERROR")
			return err
		}

		err = s.processFunc(ctx, &protoMessage)
		if err != nil {
			// We might want more specific error codes someday, but for now just classify every
			// failure as "UNKNOWN", to match our gRPC metrics.
			s.recordLatency(s.clock.Since(startTime), "UNKNOWN")
			return err
		} else {
			s.recordLatency(s.clock.Since(startTime), "OK")
			return nil
		}
	})
	if err != nil {
		log.Fatal().Err(err).Msg("Error receiving messages")
	}
}

func (s *RequestInsightSubscriber) recordLatency(latency time.Duration, status string) {
	s.latencyHistogram.With(prometheus.Labels{
		"subscription": s.subscriptionName,
		"status":       status,
	}).Observe(float64(latency.Seconds()))
}

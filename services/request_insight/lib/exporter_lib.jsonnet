// Library for resources common across RI exporters. Everything here is specific to RI and should
// not be used in other contexts.
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local publisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

// Get a pub/sub subscription with reasonable defaults for an RI exporter. specOverride can be used
// to override the subscription spec if something specific is needed for a particular exporter.
local subscriber(cloud, env, namespace, appName, serviceAccount, specOverride={}) =
  // Per-namespace Request Insight topic name is defined in the publisher lib
  // Leverage the publisher lib here to ensure consistency between publishers and subscribers
  // Intentionally pass appName='' to indicate that it's irrelevant to the topic name
  local topicName = publisherLib(cloud, env, namespace, appName='').topicName;

  local uniqueNamePrefix = if cloudInfo.isUniqueNamespace(cloud, env, namespace) then
    namespace
  else
    '%s-%s' % [cloudInfo[cloud].clusterName, namespace];
  local subscriptionName = '%s-%s-sub' % [uniqueNamePrefix, appName];

  local deadLetterTopicName = '%s-%s-deadletter-topic' % [namespace, appName];
  local deadLetterSubscriptionName = '%s-%s-deadletter-sub' % [namespace, appName];

  local subscriptionSpec = std.mergePatch({
    topicRef: {
      name: topicName,
    },
    ackDeadlineSeconds: 60,
    retryPolicy: {
      minimumBackoff: '5s',
      maximumBackoff: '300s',
    },
    // Retain messages for 1 hour in dev and 7 days in staging/prod.
    messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
    retainAckedMessages: false,
    // Move messages to the dead letter topic after 10 failed attempts.
    deadLetterPolicy: {
      deadLetterTopicRef: {
        name: deadLetterTopicName,
        namespace: namespace,
      },
      maxDeliveryAttempts: 10,
    },
  }, specOverride);
  local subscription = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubSubscription',
    metadata: {
      name: subscriptionName,
      namespace: namespace,
      // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
      // topic is deleted before the pod.
      annotations: if env == 'DEV' then {
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      } else {},
      labels: {
        app: appName,
      },
    },
    spec: subscriptionSpec,
  };
  local subscriptionAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-policy' % subscriptionName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubSubscription',
        name: subscriptionName,
      },
      bindings: [
        {
          role: 'roles/pubsub.subscriber',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
            // The project-level pub/sub service account needs to subscribe in order to process
            // deadletter messages. See https://cloud.google.com/pubsub/docs/handling-failures#grant_forwarding_permissions.
            { member: 'serviceAccount:<EMAIL>' % cloudInfo[cloud].projectNumber },
          ],
        },
      ],
    },
  };

  // Retain dead letter messages for an hour in dev and 30 days in prod, to give us plenty of time
  // to notice and fix issues.
  local deadLetterRetentionSecs = if env == 'DEV' then (60 * 60) else (60 * 60 * 24 * 30);
  local deadLetterTopic = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubTopic',
    metadata: {
      name: deadLetterTopicName,
      namespace: namespace,
      annotations: if env == 'DEV' then {
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      } else {},
      labels: {
        app: appName,
      },
    },
    spec: {
      messageRetentionDuration: '%ss' % deadLetterRetentionSecs,
    },
  };
  local deadLetterTopicAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-policy' % deadLetterTopicName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubTopic',
        name: deadLetterTopicName,
      },
      bindings: [
        {
          role: 'roles/pubsub.publisher',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
            // The project-level pub/sub service account needs to be able to publish to deadletter
            // topics. See https://cloud.google.com/pubsub/docs/handling-failures#grant_forwarding_permissions.
            { member: 'serviceAccount:<EMAIL>' % cloudInfo[cloud].projectNumber },
          ],
        },
      ],
    },
  };
  local deadLetterSubscriptionSpec = std.mergePatch({
    topicRef: {
      name: deadLetterTopicName,
    },
    ackDeadlineSeconds: 60,
    retryPolicy: {
      minimumBackoff: '5s',
      maximumBackoff: '300s',
    },
    messageRetentionDuration: '%ss' % deadLetterRetentionSecs,
    retainAckedMessages: false,
  }, specOverride);
  local deadLetterSubscription = {
    apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
    kind: 'PubSubSubscription',
    metadata: {
      name: deadLetterSubscriptionName,
      namespace: namespace,
      // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
      // topic is deleted before the pod.
      annotations: if env == 'DEV' then {
        'cnrm.cloud.google.com/deletion-policy': 'abandon',
      } else {},
      labels: {
        app: appName,
      },
    },
    spec: deadLetterSubscriptionSpec,
  };
  local deadLetterSubscriptionAccess = {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: '%s-policy' % deadLetterSubscriptionName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubSubscription',
        name: deadLetterSubscriptionName,
      },
      bindings: [
        {
          role: 'roles/pubsub.subscriber',
          members: [{ member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress }],
        },
      ],
    },
  };

  {
    topicName: topicName,
    subscriptionName: subscriptionName,
    deadLetterSubscriptionName: deadLetterSubscriptionName,
    objects: [
      subscription,
      subscriptionAccess,
      deadLetterTopic,
      deadLetterTopicAccess,
      deadLetterSubscription,
      deadLetterSubscriptionAccess,
    ],
  };

{
  subscriber:: subscriber,
}

"""Subscriber for listening to Request Insight events."""

import pathlib
import time
from dataclasses import dataclass
from typing import Callable

import structlog
from google.cloud import pubsub  # type: ignore
from prometheus_client import Counter, Histogram

from services.request_insight import request_insight_pb2

log = structlog.get_logger()


class RequestInsightSubscriber:
    """Subscriber for Request Insight events.

    The recommended way to use this class is via the `run` method, which takes care of metrics and
    ack'ing for you. This method can be used as long as you want to acknowledge each message as it
    is processed.

    If you need to delay acknowledging messages until later (e.g., if you're periodically exporting
    multiple batches), you can instead use `get_batch` and `ack_batch` to get a batch and
    acknowledge some set of messages, respectively. These are very thin wrappers around the google
    pub/sub API that do not provide error handling or metrics. Callers are expected to handle these
    themselves.
    """

    @dataclass
    class MessageAndAckId:
        """A message and its ack id.

        This is only relevant to callers who use `get_batch` and `ack_batch`.
        """

        message: request_insight_pb2.RequestInsightMessage
        ack_id: str

    def __init__(
        self,
        project_id: str,
        topic_name: str,
        subscription_name: str,
        subscription_description: str,
        batch_size: int,
        health_file: pathlib.Path | None = None,
    ):
        """Initialize the subscriber.

        Args:
            project_id: The project id of the pub/sub subscription being used.
            topic_name: The name of the request insight pub/sub topic to listen to.
            subscription_name: The name of the request insight pub/sub subscription, as it exists in
                GCP, to use.
            subscription_description: A description of the subscription, without a namespace
                qualifier (e.g., "ri-bigquery-exporter"). This is used for metrics, so that we
                can aggregate all of the metrics of a particular type across namespaces. (Note that
                all of our metrics automatically get a namespace label.)
            batch_size: The batch size of the request insight subscriptions.
            health_file: Path to a file to write a health check to.
        """
        self._subscriber = pubsub.SubscriberClient()
        self._topic_path = self._subscriber.topic_path(project_id, topic_name)
        self._subscription_path = self._subscriber.subscription_path(
            project_id, subscription_name
        )
        self.subscription_description = subscription_description
        self._batch_size = batch_size
        # Latency for processing a batch of pub/sub messages. The recorded status is "OK" or
        # "UNKNOWN", to match our gRPC metrics, even though this isn't a gRPC service.
        self.latency_histogram = Histogram(
            "au_subscription_latency",
            "Latency to process a batch of pub/sub messages",
            ["subscription", "status"],
        )

        # Count the total number of messages processed by the subscriber. The recorded status is
        # "OK" or "UNKNOWN", to match our gRPC metrics, even though this isn't a gRPC service.
        self.message_count = Counter(
            "au_subscription_message_count",
            "Number of messages processed by the subscriber",
            ["subscription", "status"],
        )
        self.health_file = health_file
        self.health_file_written = False

    def run(
        self,
        process_batch_fn: Callable[
            [list[request_insight_pb2.RequestInsightMessage]], None
        ],
    ):
        """Run the subscriber main loop (does not return).

        `process_batch_fn` should attempt to handle and log any recoverable errors; if
        it raises an exception, this main loop will catch and log the exception and fail
        the entire batch. As a result, this can cause some messages to be processed
        twice.

        Args:
            process_batch_fn: Function that processes each non-empty batch of
                RequestInsightMessages.
        """
        with self._subscriber:
            log.info("Listening for messages on %s", self._subscription_path)

            while True:
                start_time = time.time()
                status = "UNKNOWN"
                message_count = 0
                try:
                    response = self._subscriber.pull(
                        request={
                            "subscription": self._subscription_path,
                            "max_messages": self._batch_size,
                        },
                        timeout=60,
                    )
                    if self.health_file and not self.health_file_written:
                        log.info("Declare healthy")
                        self.health_file.write_text("OK")
                        self.health_file_written = True

                    message_count = len(response.received_messages)
                    if message_count == 0:
                        log.info("No more messages.")
                        continue

                    log.info("Received %d messages", message_count)
                    proto_messages = []
                    for message in response.received_messages:
                        proto_message = request_insight_pb2.RequestInsightMessage()
                        proto_message.ParseFromString(message.message.data)
                        proto_messages.append(proto_message)

                    # Process the batch. If this raises an exception, then we
                    # will not acknowledge the messages, and will retry
                    # processing them after the configured ackDeadlineSeconds.
                    process_batch_fn(proto_messages)

                    # Acknowledge the received messages.
                    ack_ids = [m.ack_id for m in response.received_messages]
                    self._subscriber.acknowledge(
                        request={
                            "subscription": self._subscription_path,
                            "ack_ids": ack_ids,
                        }
                    )
                    log.debug(
                        "Acknowledged %d messages. Ack ids: %s",
                        message_count,
                        ack_ids,
                    )
                    status = "OK"
                except Exception as ex:  # pylint: disable=broad-exception-caught
                    log.error("Error processing pub/sub message: %s", ex)
                    log.exception(ex)
                finally:
                    self.message_count.labels(
                        self.subscription_description, status
                    ).inc(message_count)
                    self.latency_histogram.labels(
                        self.subscription_description, status
                    ).observe(time.time() - start_time)

    def get_batch(self) -> list[MessageAndAckId]:
        """Get a batch of messages.

        If possible, use `run` instead of this method. See the top-level class documentation for
        details.

        Returns:
            A list of MessageAndAckId objects.The ack ids should be used to call ack_batch before
            the subscription's ack deadline expires.
        """
        response = self._subscriber.pull(
            request={
                "subscription": self._subscription_path,
                "max_messages": self._batch_size,
            },
            timeout=60,
        )

        message_count = len(response.received_messages)
        if message_count == 0:
            log.info("No more messages.")
            return []

        log.info("Received %d messages", message_count)
        result = []
        for message in response.received_messages:
            proto_message = request_insight_pb2.RequestInsightMessage()
            proto_message.ParseFromString(message.message.data)
            result.append(
                RequestInsightSubscriber.MessageAndAckId(proto_message, message.ack_id)
            )

        return result

    def ack_batch(self, ack_ids):
        """Acknowledge a batch of messages.

        If possible, use `run` instead of this method. See the top-level class documentation for
        details.

        Args:
            ack_ids: List of ack ids to acknowledge. These should come from a previous call to
                `get_batch`.
        """
        # Acknowledge in batches of 1000 messages. The ack endpoint has a payload size limit of
        # ~0.5MB and ack ids are ~200 bytes each.
        for i in range(0, len(ack_ids), 1000):
            self._subscriber.acknowledge(
                request={
                    "subscription": self._subscription_path,
                    "ack_ids": ack_ids[i : i + 1000],
                }
            )

        log.info("Acknowledged %d messages.", len(ack_ids))
        log.debug("Ack ids: %s", ack_ids)

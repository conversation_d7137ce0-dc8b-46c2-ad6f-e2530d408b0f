// This file defines the "core" of a request insight deployment (i.e., the components of request
// insight that it doesn't make sense for the system to run without). There are other, optional
// components of request insight (e.g., the BigQuery exporter), which have their own deployment
// jsonnets.

local pubsubLib = import 'services/request_insight/pubsub_deploy.jsonnet';

function(env, namespace, cloud, namespace_config)
  // In staging and prod the RI pub/sub topic and config are deployed separately, since they're a
  // dependency of anyone that publishes to RI (see METADATA.jsonnet). For dev deployments we don't
  // want to allow someone to deploy request-insight without its topic.
  if env != 'DEV' then [] else pubsubLib(env, namespace, cloud, namespace_config)

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local saLib = import 'services/request_insight/external_service_accounts_lib.jsonnet';

// The service accounts generated by this function are for use by non-GCP services (e.g.,
// CoreWeave or Hex), to pull in data used for research (BigQuery, blob export buckets) and to
// export data from research models (vendor buckets). The objects returned by this file are NOT a
// complete list of the service accounts' permissions. Search for uses of
// external_service_accounts_lib.jsonnet to find more.
function(env, cloud, namespace, namespace_config)
  local appName = 'request-insight-external-sa';
  local serviceAccounts = saLib.getExternalServiceAccounts(env, cloud);
  local coreweaveRIDataImporterSA = serviceAccounts.coreweaveRIDataImporterSA;
  local coreweaveVendorBucketAccessSA = serviceAccounts.coreweaveVendorBucketAccessSA;
  local hexPIIAnalyticsSA = serviceAccounts.hexPIIAnalyticsSA;
  local hexNonPIIAnalyticsSA = serviceAccounts.hexNonPIIAnalyticsSA;
  local gtmDataformSA = serviceAccounts.gtmDataformSA;

  local iamPolicies = lib.flatten([
    [
      // Give the appropriate accounts the ability to run BigQuery jobs (i.e., queries).
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPolicyMember',
        metadata: {
          name: 'iampolicymember-bigquery-job-user-%s' % sa.name,
          namespace: 'devtools',
          labels: {
            app: appName,
          },
        },
        spec: {
          member: 'serviceAccount:%s' % sa.email,
          role: 'roles/bigquery.jobUser',
          resourceRef: {
            kind: 'Project',
            external: 'projects/%s' % cloudInfo[cloud].projectId,
          },
        },
      }
      for sa in [coreweaveRIDataImporterSA, hexPIIAnalyticsSA, hexNonPIIAnalyticsSA, gtmDataformSA]
    ],
    // Give hex accounts the ability to use read sessions.
    gcpLib.grantAccess(
      env=env,
      namespace=namespace,
      appName=appName,
      name='hex-bigquery-readsessions-grant',
      resourceRef={
        kind: 'Project',
        external: 'projects/%s' % cloudInfo[cloud].projectId,
      },
      bindings=[
        {
          role: 'roles/bigquery.readSessionUser',
          members: [
            {
              memberFrom: {
                serviceAccountRef: {
                  name: hexPIIAnalyticsSA.name,
                },
              },
            },
            {
              memberFrom: {
                serviceAccountRef: {
                  name: hexNonPIIAnalyticsSA.name,
                },
              },
            },
          ],
        },
      ],
      abandon=true,
    ),
  ]);

  lib.flatten([
    coreweaveRIDataImporterSA.objects,
    coreweaveVendorBucketAccessSA.objects,
    hexPIIAnalyticsSA.objects,
    hexNonPIIAnalyticsSA.objects,
    iamPolicies,
  ])

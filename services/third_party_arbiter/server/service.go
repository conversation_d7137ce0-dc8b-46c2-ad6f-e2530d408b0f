package main

import (
	"container/list"
	"context"
	"crypto/sha256"
	"encoding/binary"
	"fmt"
	"maps"
	"math"
	"math/rand"
	"sort"
	"sync"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/clock"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	proto "github.com/augmentcode/augment/services/third_party_arbiter/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Feature flags for load balancing
var (
	// The minimum number of records to consider for load balancing
	thirdPartyArbiterLoadBalanceMinRecords = featureflags.NewIntFlag(
		"third_party_arbiter_load_balance_min_records",
		10,
	)

	// The time period in seconds to keep response history for
	thirdPartyArbiterLoadBalanceVacateHistorySeconds = featureflags.NewIntFlag(
		"third_party_arbiter_load_balance_vacate_history_seconds",
		300,
	)

	// The bucket size in seconds for response history
	thirdPartyArbiterLoadBalanceBucketSizeSeconds = featureflags.NewIntFlag(
		"third_party_arbiter_load_balance_bucket_size_seconds",
		10,
	)

	// The maximum amount of jitter to add to the retry delay in milliseconds
	thirdPartyArbiterJitterMaxMs = featureflags.NewIntFlag(
		"third_party_arbiter_jitter_max_ms",
		1000,
	)
)

// Client identifiers in feature flag
const (
	ClientAnthropicDirect        = "anthropic_direct"
	ClientAnthropicVertexaiUsE5  = "anthropic_vertexai_us_e5"
	ClientAnthropicVertexaiEuW1  = "anthropic_vertexai_eu_w1"
	ClientAnthropicVertexaiAsSe1 = "anthropic_vertexai_as_se1"
	ClientUnknown                = "unknown"
	ModelClaude35SonnetV2        = "claude_3_5_sonnet_v2"
	ModelClaude37Sonnet          = "claude_3_7_sonnet"
	ModelClaude40Sonnet          = "claude_4_0_sonnet"
	ModelClaude40Opus            = "claude_4_0_opus"
)

// Status labels for metrics
const (
	StatusSuccess = "success"
	StatusFailure = "failure"
)

// roundUpToMultipleOf divides a by b and rounds up to the nearest integer, equivalent to ceil(a/b) for floats
func roundUpToMultipleOf(a, b int) int {
	return (a + b - 1) / b
}

// Helper function to convert enum value to string for feature flag lookup
func thirdPartyClientToString(client proto.ThirdPartyClient) string {
	switch client {
	case proto.ThirdPartyClient_ANTHROPIC_DIRECT:
		return ClientAnthropicDirect
	case proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5:
		return ClientAnthropicVertexaiUsE5
	case proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1:
		return ClientAnthropicVertexaiEuW1
	case proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1:
		return ClientAnthropicVertexaiAsSe1
	default:
		return ""
	}
}

// Helper function to convert string to enum value for feature flag parsing
func stringToThirdPartyClient(s string) proto.ThirdPartyClient {
	switch s {
	case ClientAnthropicDirect:
		return proto.ThirdPartyClient_ANTHROPIC_DIRECT
	case ClientAnthropicVertexaiUsE5:
		return proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5
	case ClientAnthropicVertexaiEuW1:
		return proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1
	case ClientAnthropicVertexaiAsSe1:
		return proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1
	default:
		return proto.ThirdPartyClient_UNKNOWN
	}
}

// Helper function to convert boolean success to status string for metrics
func successToStatusString(isSuccess bool) string {
	if isSuccess {
		return StatusSuccess
	}
	return StatusFailure
}

// RecordStats represents statistics for a target's response history
type RecordStats struct {
	SuccessCount int
	FailureCount int
	TotalCount   int
}

// ResponseBucket stores success and failure counts for a specific time bucket
type ResponseBucket struct {
	// Success count in this bucket
	SuccessCount int
	// Failure count in this bucket
	FailureCount int
}

// BucketedResponseHistory stores response history in time buckets using a circular buffer
type BucketedResponseHistory struct {
	// List of buckets in chronological order (oldest to newest)
	Buckets *list.List
	// The time when the latest bucket ends
	LatestBucketEndTime time.Time
	// Running total of success counts (excluding the current bucket)
	RunningSuccessCount int
	// Running total of failure counts (excluding the current bucket)
	RunningFailureCount int
}

type ThirdPartyArbiterService struct {
	proto.UnimplementedThirdPartyArbiterServer
	featureFlags featureflags.FeatureFlagHandle
	clock        clock.Clock

	// loadBalanceWeights stores the target weights for load balancing
	// Map of model -> target -> weight
	loadBalanceWeights map[proto.Model]map[string]float64

	// responseHistory stores response history in time buckets for each model and target
	// Map of model -> target -> history
	responseHistory map[proto.Model]map[proto.ThirdPartyClient]*BucketedResponseHistory

	// Map of locks for each model and target key's response history
	// Map of model -> target -> lock
	responseHistoryKeyLocks map[proto.Model]map[proto.ThirdPartyClient]*sync.RWMutex

	// retryTimestamps stores the timestamp until which a target should be delayed
	// Map of model -> target -> timestamp
	retryTimestamps map[proto.Model]map[proto.ThirdPartyClient]time.Time

	// Map of locks for each model and target key's retry timestamp
	// Map of model -> target -> lock
	retryTimestampsKeyLocks map[proto.Model]map[proto.ThirdPartyClient]*sync.RWMutex
}

func NewThirdPartyArbiterService(featureFlags featureflags.FeatureFlagHandle, loadBalanceWeights map[string]map[string]float64) *ThirdPartyArbiterService {
	return NewThirdPartyArbiterServiceWithClock(featureFlags, clock.NewRealClock(), loadBalanceWeights)
}

// NewThirdPartyArbiterServiceWithClock creates a new service with a custom clock for testing
func NewThirdPartyArbiterServiceWithClock(featureFlags featureflags.FeatureFlagHandle, clk clock.Clock, loadBalanceWeights map[string]map[string]float64) *ThirdPartyArbiterService {
	// Initialize the model-keyed maps
	modelKeyedLoadBalanceWeights := make(map[proto.Model]map[string]float64)
	responseHistory := make(map[proto.Model]map[proto.ThirdPartyClient]*BucketedResponseHistory)
	retryTimestamps := make(map[proto.Model]map[proto.ThirdPartyClient]time.Time)
	responseHistoryKeyLocks := make(map[proto.Model]map[proto.ThirdPartyClient]*sync.RWMutex)
	retryTimestampsKeyLocks := make(map[proto.Model]map[proto.ThirdPartyClient]*sync.RWMutex)

	// Initialize for each model
	for _, model := range []proto.Model{
		proto.Model_CLAUDE_3_5_SONNET_V2,
		proto.Model_CLAUDE_3_7_SONNET,
		proto.Model_CLAUDE_4_0_SONNET,
		proto.Model_CLAUDE_4_0_OPUS,
	} {
		// Map the string model name to the proto enum
		var modelStr string
		switch model {
		case proto.Model_CLAUDE_3_5_SONNET_V2:
			modelStr = ModelClaude35SonnetV2
		case proto.Model_CLAUDE_3_7_SONNET:
			modelStr = ModelClaude37Sonnet
		case proto.Model_CLAUDE_4_0_SONNET:
			modelStr = ModelClaude40Sonnet
		case proto.Model_CLAUDE_4_0_OPUS:
			modelStr = ModelClaude40Opus
		default:
			log.Ctx(context.Background()).Fatal().
				Str("model", model.String()).
				Msg("Unknown model")
			return nil
		}

		// Get the weights for this model
		modelWeights, ok := loadBalanceWeights[modelStr]
		if !ok {
			log.Ctx(context.Background()).Fatal().
				Str("model", modelStr).
				Msg("No weights found for model, using default weights")
			return nil
		}

		// Initialize the weights for this model
		modelKeyedLoadBalanceWeights[model] = modelWeights

		// Initialize the maps for this model
		responseHistory[model] = make(map[proto.ThirdPartyClient]*BucketedResponseHistory)
		retryTimestamps[model] = make(map[proto.ThirdPartyClient]time.Time)
		responseHistoryKeyLocks[model] = make(map[proto.ThirdPartyClient]*sync.RWMutex)
		retryTimestampsKeyLocks[model] = make(map[proto.ThirdPartyClient]*sync.RWMutex)

		// Initialize locks for all possible targets for this model
		for _, target := range []proto.ThirdPartyClient{
			proto.ThirdPartyClient_ANTHROPIC_DIRECT,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_US_E5,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_EU_W1,
			proto.ThirdPartyClient_ANTHROPIC_VERTEXAI_AS_SE1,
		} {
			responseHistoryKeyLocks[model][target] = &sync.RWMutex{}
			retryTimestampsKeyLocks[model][target] = &sync.RWMutex{}
		}
	}

	// Initialize active connections metric to 0
	activeConnections.Set(0)

	return &ThirdPartyArbiterService{
		featureFlags:            featureFlags,
		responseHistory:         responseHistory,
		retryTimestamps:         retryTimestamps,
		clock:                   clk,
		responseHistoryKeyLocks: responseHistoryKeyLocks,
		retryTimestampsKeyLocks: retryTimestampsKeyLocks,
		loadBalanceWeights:      modelKeyedLoadBalanceWeights,
	}
}

// getResponseHistoryKeyLock safely retrieves the lock for a specific model and target from responseHistoryKeyLocks
// Map should have been initialized with all possible keys
func (s *ThirdPartyArbiterService) getResponseHistoryKeyLock(model proto.Model, target proto.ThirdPartyClient) *sync.RWMutex {
	modelMap, ok := s.responseHistoryKeyLocks[model]
	if !ok {
		log.Ctx(context.Background()).Warn().
			Str("model", fmt.Sprintf("%v", model)).
			Msg("Creating missing response history map for model")
		modelMap = make(map[proto.ThirdPartyClient]*sync.RWMutex)
		s.responseHistoryKeyLocks[model] = modelMap
	}

	lockVal, ok := modelMap[target]
	if !ok {
		log.Ctx(context.Background()).Warn().
			Str("model", fmt.Sprintf("%v", model)).
			Str("target", fmt.Sprintf("%v", target)).
			Msg("Creating missing response history lock for key")
		newLock := &sync.RWMutex{}
		modelMap[target] = newLock
		return newLock
	}
	return lockVal
}

// getRetryTimestampKeyLock safely retrieves the lock for a specific model and target from retryTimestampsKeyLocks
// Map should have been initialized with all possible keys
func (s *ThirdPartyArbiterService) getRetryTimestampKeyLock(model proto.Model, target proto.ThirdPartyClient) *sync.RWMutex {
	modelMap, ok := s.retryTimestampsKeyLocks[model]
	if !ok {
		log.Ctx(context.Background()).Warn().
			Str("model", fmt.Sprintf("%v", model)).
			Msg("Creating missing retry timestamp map for model")
		modelMap = make(map[proto.ThirdPartyClient]*sync.RWMutex)
		s.retryTimestampsKeyLocks[model] = modelMap
	}

	lockVal, ok := modelMap[target]
	if !ok {
		log.Ctx(context.Background()).Warn().
			Str("model", fmt.Sprintf("%v", model)).
			Str("target", fmt.Sprintf("%v", target)).
			Msg("Creating missing retry timestamp lock for key")
		newLock := &sync.RWMutex{}
		modelMap[target] = newLock
		return newLock
	}
	return lockVal
}

// manageBuckets handles all bucket management operations:
// 1. Creates new buckets if the current timestamp is beyond the latest bucket end time
// 2. Updates running counts accordingly
// 3. Removes expired buckets
func (s *ThirdPartyArbiterService) manageBuckets(model proto.Model, target proto.ThirdPartyClient, now time.Time) {
	// Get the lock for this specific model and target
	vacateHistorySeconds, _ := thirdPartyArbiterLoadBalanceVacateHistorySeconds.Get(s.featureFlags)
	bucketSizeSeconds, _ := thirdPartyArbiterLoadBalanceBucketSizeSeconds.Get(s.featureFlags)

	lock := s.getResponseHistoryKeyLock(model, target)
	lock.Lock()
	defer lock.Unlock()

	// We need to check if the model map exists and create it if not
	modelMap, ok := s.responseHistory[model]
	if !ok {
		modelMap = make(map[proto.ThirdPartyClient]*BucketedResponseHistory)
		s.responseHistory[model] = modelMap
	}

	// We need to check if the history exists and create it if not
	history := modelMap[target]
	if history == nil {
		history = &BucketedResponseHistory{
			Buckets: list.New(),
		}
		modelMap[target] = history
	}

	// PART 1: If this is the first bucket, just create it and we're done
	if history.Buckets.Len() == 0 {
		// Create a new bucket
		bucket := &ResponseBucket{
			SuccessCount: 0,
			FailureCount: 0,
		}

		// Add the new bucket to the end of the list
		history.Buckets.PushBack(bucket)

		// Update the first bucket's end time
		history.LatestBucketEndTime = now.Add(time.Duration(bucketSizeSeconds) * time.Second)
		return
	}

	maxBuckets := roundUpToMultipleOf(vacateHistorySeconds, bucketSizeSeconds)

	// PART 2: If the current time is beyond the latest bucket end time, create new buckets
	if now.After(history.LatestBucketEndTime) {
		// Calculate how many buckets we need to create
		timeDiffSeconds := now.Sub(history.LatestBucketEndTime).Seconds()
		numBucketsToCreate := int(math.Ceil(timeDiffSeconds / float64(bucketSizeSeconds)))

		// It's been so long that all buckets will be replaced with empty ones
		if numBucketsToCreate > maxBuckets {
			numBucketsToCreate = maxBuckets
		}

		if numBucketsToCreate > 0 {
			// Last bucket will no longer be last, add its counts to running totals
			lastBucket := history.Buckets.Back().Value.(*ResponseBucket)
			history.RunningSuccessCount += lastBucket.SuccessCount
			history.RunningFailureCount += lastBucket.FailureCount

			// Create all the needed buckets
			for i := 0; i < numBucketsToCreate; i++ {
				history.Buckets.PushBack(&ResponseBucket{
					SuccessCount: 0,
					FailureCount: 0,
				})
			}
			history.LatestBucketEndTime = history.LatestBucketEndTime.Add(
				time.Duration(bucketSizeSeconds) * time.Duration(numBucketsToCreate) * time.Second,
			)
		}
	}

	// PART 3: Clean up expired buckets if needed
	for history.Buckets.Len() > maxBuckets {
		oldestBucketElement := history.Buckets.Front()

		// Remove the oldest bucket's counts from running totals
		oldestBucket := oldestBucketElement.Value.(*ResponseBucket)
		history.RunningSuccessCount -= oldestBucket.SuccessCount
		history.RunningFailureCount -= oldestBucket.FailureCount

		// Remove the oldest bucket
		history.Buckets.Remove(oldestBucketElement)
	}
}

// GetRecordStats returns statistics about the response history for a specific model and target
// The returned RecordStats contains counts of successful and failed responses
func (s *ThirdPartyArbiterService) GetRecordStats(model proto.Model, target proto.ThirdPartyClient) RecordStats {
	// Manage buckets (clean up expired ones) before calculating stats
	s.manageBuckets(model, target, s.clock.Now())

	// Get the lock for this specific model and target
	lock := s.getResponseHistoryKeyLock(model, target)
	lock.RLock()
	defer lock.RUnlock()

	// Check if the model map exists
	modelMap, ok := s.responseHistory[model]
	if !ok {
		// No history for this model, return empty stats
		return RecordStats{
			SuccessCount: 0,
			FailureCount: 0,
			TotalCount:   0,
		}
	}

	// Check if the history exists
	history, ok := modelMap[target]
	if !ok || history.Buckets.Len() == 0 {
		// No history for this target, return empty stats
		return RecordStats{
			SuccessCount: 0,
			FailureCount: 0,
			TotalCount:   0,
		}
	}

	// Get the running totals and add the current bucket's counts
	successCount := history.RunningSuccessCount
	failureCount := history.RunningFailureCount

	// Add the current bucket's counts
	currentBucket := history.Buckets.Back().Value.(*ResponseBucket)
	successCount += currentBucket.SuccessCount
	failureCount += currentBucket.FailureCount

	return RecordStats{
		SuccessCount: successCount,
		FailureCount: failureCount,
		TotalCount:   successCount + failureCount,
	}
}

// getPseudoRandomFromSessionID generates a deterministic pseudo-random value between 0 and 1 based on the session ID.
func getPseudoRandomFromSessionID(sessionID string) float64 {
	// Use SHA-256 to get a consistent hash value from the session ID
	// We're not using this for security, just for deterministic randomness
	hash := sha256.Sum256([]byte(sessionID))

	// Convert the first 4 bytes to uint32
	intValue := binary.LittleEndian.Uint32(hash[:4])

	return float64(intValue) / float64(0xFFFFFFFF)
}

// calculateErrorRate calculates the error rate for a given model and target
// based on the response history. Returns 0 if insufficient history exists or if there's an error.
func (s *ThirdPartyArbiterService) calculateErrorRate(ctx context.Context, model proto.Model, target proto.ThirdPartyClient) float64 {
	// Get stats for this model and target (this will also clean up expired buckets)
	targetStats := s.GetRecordStats(model, target)

	// Get minimum records feature flag value (or default)
	minRecords, _ := thirdPartyArbiterLoadBalanceMinRecords.Get(s.featureFlags)

	// If we don't have enough records, return 0
	if targetStats.TotalCount < minRecords {
		return 0.0
	}

	// Calculate error rate
	errorRate := float64(targetStats.FailureCount) / float64(targetStats.TotalCount)

	logger := log.Ctx(ctx).With().
		Int("min_records", minRecords).
		Int("total_count", targetStats.TotalCount).
		Int("failure_count", targetStats.FailureCount).
		Float64("error_rate", errorRate).
		Str("model", model.String()).
		Str("target", target.String()).
		Logger()
	logger.Info().Msg("Calculated error rate")

	return errorRate
}

// WeightedTarget represents a target with its associated weight
type WeightedTarget struct {
	Target string
	Weight float64
}

// selectTargetFromWeights selects a target from the provided weights using the given random value.
// Uses a sorted slice of WeightedTarget to ensure consistent iteration order.
// Returns the selected target and a boolean indicating if a target was successfully selected.
func selectTargetFromWeights(weights map[string]float64, randVal float64) (string, bool) {
	// Convert map to a sorted slice of WeightedTarget
	weightedTargets := make([]WeightedTarget, 0, len(weights))
	for target, weight := range weights {
		weightedTargets = append(weightedTargets, WeightedTarget{Target: target, Weight: weight})
	}

	// Sort by target name to ensure consistent iteration order
	sort.Slice(weightedTargets, func(i, j int) bool {
		return weightedTargets[i].Target < weightedTargets[j].Target
	})

	// Calculate total weight
	totalWeight := 0.0
	for _, wt := range weightedTargets {
		totalWeight += wt.Weight
	}

	// If no valid targets, return empty
	if totalWeight <= 0 || len(weightedTargets) == 0 {
		return "", false
	}

	// Normalize weights and select target based on weights using the random value
	cumulativeWeight := 0.0
	for _, wt := range weightedTargets {
		normalizedWeight := wt.Weight / totalWeight
		cumulativeWeight += normalizedWeight
		if randVal < cumulativeWeight {
			return wt.Target, true
		}
	}

	// If no target was selected (shouldn't happen if weights are normalized), use the first one
	if len(weightedTargets) > 0 {
		return weightedTargets[0].Target, true
	}

	return "", false
}

func (s *ThirdPartyArbiterService) getTargetFromSessionId(ctx context.Context, sessionID string, weights map[string]float64) (string, bool) {
	// Get a deterministic value based on session ID for consistent routing
	randVal := getPseudoRandomFromSessionID(sessionID)
	logger := log.Ctx(ctx).With().Float64("rand_val", randVal).Logger()
	logger.Info().Msg("Using session ID for randomization")

	// Select target based on weights
	selectedTarget, success := selectTargetFromWeights(weights, randVal)
	if !success {
		log.Ctx(ctx).Error().Msg("Failed to select a target from weights")
		return "", false
	}

	return selectedTarget, true
}

func (s *ThirdPartyArbiterService) GetTarget(ctx context.Context, req *proto.GetTargetRequest) (*proto.GetTargetResponse, error) {
	// Record metrics for request
	startTime := time.Now()
	requestsTotal.WithLabelValues("GetTarget", "GET").Inc()

	// Increment active connections
	activeConnections.Inc()

	// Defer latency recording and decrement active connections
	defer func() {
		latency := time.Since(startTime).Seconds()
		getTargetLatency.WithLabelValues(modelToString(req.Model)).Observe(latency)
		activeConnections.Dec()
	}()

	// Get request context for session ID
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().
		Str("model", req.Model.String()).
		Msg("GetTarget called")

	// Check if the model is valid (not zero value)
	if req.Model == 0 {
		log.Ctx(ctx).Error().Msg("Unknown model requested")
		return nil, status.Error(codes.InvalidArgument, "Unknown model requested")
	}

	// Get the weights for this model
	modelWeights, ok := s.loadBalanceWeights[req.Model]
	if !ok {
		log.Ctx(ctx).Error().
			Str("model", req.Model.String()).
			Msg("No weights found for model")
		return nil, status.Error(codes.InvalidArgument, "No weights found for model")
	}

	// Use the load balance weights for this model
	weights := modelWeights

	// Filter weights based on supported_targets if provided
	if len(req.SupportedTargets) > 0 {
		// Create a map of supported targets for O(1) lookup
		supportedTargetsMap := make(map[proto.ThirdPartyClient]bool)
		for _, target := range req.SupportedTargets {
			supportedTargetsMap[target] = true
		}

		// Filter weights to only include supported targets
		filteredWeights := make(map[string]float64)
		for targetStr, weight := range weights {
			target := stringToThirdPartyClient(targetStr)
			if target != proto.ThirdPartyClient_UNKNOWN && supportedTargetsMap[target] {
				filteredWeights[targetStr] = weight
			}
		}

		// If we have any supported targets with weights, use those
		if len(filteredWeights) > 0 {
			weights = filteredWeights
			supportedTargetsStr := make([]string, len(req.SupportedTargets))
			for i, target := range req.SupportedTargets {
				supportedTargetsStr[i] = thirdPartyClientToString(target)
			}
			log.Ctx(ctx).Info().
				Interface("supported_targets", supportedTargetsStr).
				Interface("filtered_weights", filteredWeights).
				Msg("Using filtered weights based on supported targets")
		} else {
			log.Ctx(ctx).Warn().
				Interface("supported_targets", req.SupportedTargets).
				Msg("No weights found for any supported targets, using all weights")
		}
	}

	var baseSessionID string
	if requestContext != nil && requestContext.RequestSessionId != "" {
		baseSessionID = string(requestContext.RequestSessionId)
	} else {
		baseSessionID = "Fake " + fmt.Sprintf("%d", rand.Uint32())
	}

	// Select primary target based on weights and session id
	primaryTargetStr, success := s.getTargetFromSessionId(ctx, baseSessionID, weights)
	if !success {
		log.Ctx(ctx).Error().Msg("Failed to select a primary target from weights")
		// Return a gRPC error instead of a hardcoded response
		return nil, status.Error(codes.Internal, "Failed to select a primary target from weights")
	}

	loggerPrimary := log.Ctx(ctx).With().Str("model", req.Model.String()).Str("primary_target_str", primaryTargetStr).Logger()
	loggerPrimary.Info().Msg("Primary target based on weights")

	// Record primary target selection metric
	primaryTargetSelectionsTotal.WithLabelValues(
		modelToString(req.Model),
		primaryTargetStr,
	).Inc()

	// Create adjusted weights without primary target
	weightsWithoutPrimary := maps.Clone(weights)
	delete(weightsWithoutPrimary, primaryTargetStr)

	// If we have no weights left after removing primary, return just the primary target
	if len(weightsWithoutPrimary) == 0 {
		log.Ctx(ctx).Info().Msg("No weights left for secondary target, returning only primary target")

		primaryTarget := stringToThirdPartyClient(primaryTargetStr)
		if primaryTarget == proto.ThirdPartyClient_UNKNOWN {
			logger := log.Ctx(ctx).With().Str("target_str", primaryTargetStr).Logger()
			logger.Error().Msg("Failed to convert target to enum")
			return nil, status.Error(codes.Internal, "Failed to convert primary target to enum")
		}

		// Return only the primary target
		targetsResult := []proto.ThirdPartyClient{primaryTarget}
		return &proto.GetTargetResponse{
			Targets: targetsResult,
			DelayMs: 0,
		}, nil
	}

	secondaryTargetStr, success := s.getTargetFromSessionId(ctx, baseSessionID+"_secondary", weightsWithoutPrimary)
	if !success {
		log.Ctx(ctx).Error().Msg("Failed to select a secondary target from weights")
		// Return a gRPC error instead of a hardcoded response
		return nil, status.Error(codes.Internal, "Failed to select a secondary target from weights")
	}

	loggerSecondary := log.Ctx(ctx).With().Str("model", req.Model.String()).Str("secondary_target_str", secondaryTargetStr).Logger()
	loggerSecondary.Info().Msg("Secondary target based on weights")

	// Create adjusted weights without primary/secondary targets
	weightsWithoutPrimarySecondary := maps.Clone(weightsWithoutPrimary)
	delete(weightsWithoutPrimarySecondary, secondaryTargetStr)

	// If we have no weights left for tertiary, return just primary and secondary
	if len(weightsWithoutPrimarySecondary) == 0 {
		log.Ctx(ctx).Info().Msg("No weights left for tertiary target, returning only primary and secondary targets")

		primaryTarget := stringToThirdPartyClient(primaryTargetStr)
		secondaryTarget := stringToThirdPartyClient(secondaryTargetStr)

		if primaryTarget == proto.ThirdPartyClient_UNKNOWN {
			logger := log.Ctx(ctx).With().Str("target_str", primaryTargetStr).Logger()
			logger.Error().Msg("Failed to convert target to enum")
			return nil, status.Error(codes.Internal, "Failed to convert primary target to enum")
		}
		if secondaryTarget == proto.ThirdPartyClient_UNKNOWN {
			logger := log.Ctx(ctx).With().Str("target_str", secondaryTargetStr).Logger()
			logger.Error().Msg("Failed to convert target to enum")
			return nil, status.Error(codes.Internal, "Failed to convert secondary target to enum")
		}

		// Return only primary and secondary targets
		targetsResult := []proto.ThirdPartyClient{primaryTarget, secondaryTarget}
		return &proto.GetTargetResponse{
			Targets: targetsResult,
			DelayMs: 0,
		}, nil
	}

	tertiaryTargetStr, success := s.getTargetFromSessionId(ctx, baseSessionID+"_tertiary", weightsWithoutPrimarySecondary)
	if !success {
		log.Ctx(ctx).Error().Msg("Failed to select a tertiary target from weights")
		// Return a gRPC error instead of a hardcoded response
		return nil, status.Error(codes.Internal, "Failed to select a tertiary target from weights")
	}

	loggerTertiary := log.Ctx(ctx).With().Str("model", req.Model.String()).Str("tertiary_target_str", tertiaryTargetStr).Logger()
	loggerTertiary.Info().Msg("Tertiary target based on weights")

	primaryTarget := stringToThirdPartyClient(primaryTargetStr)
	secondaryTarget := stringToThirdPartyClient(secondaryTargetStr)
	tertiaryTarget := stringToThirdPartyClient(tertiaryTargetStr)

	if primaryTarget == proto.ThirdPartyClient_UNKNOWN {
		logger := log.Ctx(ctx).With().Str("target_str", primaryTargetStr).Logger()
		logger.Error().Msg("Failed to convert target to enum")
		return nil, status.Error(codes.Internal, "Failed to convert primary target to enum")
	}
	if secondaryTarget == proto.ThirdPartyClient_UNKNOWN {
		logger := log.Ctx(ctx).With().Str("target_str", secondaryTargetStr).Logger()
		logger.Error().Msg("Failed to convert target to enum")
		return nil, status.Error(codes.Internal, "Failed to convert secondary target to enum")
	}
	if tertiaryTarget == proto.ThirdPartyClient_UNKNOWN {
		logger := log.Ctx(ctx).With().Str("target_str", tertiaryTargetStr).Logger()
		logger.Error().Msg("Failed to convert target to enum")
		return nil, status.Error(codes.Internal, "Failed to convert tertiary target to enum")
	}

	// Apply adaptive load balancing based on error rates
	errorRate := s.calculateErrorRate(ctx, req.Model, primaryTarget)

	var targetsResult []proto.ThirdPartyClient
	targetsResult = []proto.ThirdPartyClient{primaryTarget, secondaryTarget, tertiaryTarget}

	// If there's an error rate, potentially redirect traffic
	if errorRate > 0 {
		// Generate a hash to decide if to redirect to secondary target
		shouldRedirectRandVal := getPseudoRandomFromSessionID(string(baseSessionID) + "_should_redirect")

		// If the secondary random value falls within the error rate, redirect
		if shouldRedirectRandVal < errorRate {
			targetsResult = []proto.ThirdPartyClient{secondaryTarget, tertiaryTarget, primaryTarget}

			logger := log.Ctx(ctx).With().
				Str("model", req.Model.String()).
				Str("original_primary", primaryTarget.String()).
				Str("new_primary", secondaryTarget.String()).
				Float64("error_rate", errorRate).
				Logger()
			logger.Info().Msg("Adaptive load balancing redirected traffic")
		}
	}

	// Check if there's a retry timestamp for the primary target
	target := targetsResult[0]
	model := req.Model

	// Default delay_ms to 0
	delayMs := int32(0)

	// Check if there's a retry timestamp for the selected model and target
	lock := s.getRetryTimestampKeyLock(model, target)
	lock.RLock()

	// Check if the model map exists
	modelMap, ok := s.retryTimestamps[model]
	if !ok {
		// No retry timestamps for this model
		lock.RUnlock()
	} else {
		// Check if there's a retry timestamp for this target
		retryTimestamp, ok := modelMap[target]
		lock.RUnlock()

		if ok {
			now := s.clock.Now()
			// If the timestamp is in the future, calculate the delay
			if retryTimestamp.After(now) {
				// Calculate delay in milliseconds
				delayMs = int32(time.Until(retryTimestamp).Milliseconds())
				// Add random jitter based on feature flag to avoid thundering herd
				maxJitter, _ := thirdPartyArbiterJitterMaxMs.Get(s.featureFlags)
				jitter := time.Duration(rand.Int63n(int64(maxJitter))) * time.Millisecond
				delayMs += int32(jitter.Milliseconds())

				log.Ctx(ctx).Info().
					Time("retry_timestamp", retryTimestamp).
					Time("now", now).
					Int32("delay_ms", delayMs).
					Str("model", model.String()).
					Str("target", target.String()).
					Msg("Returning delay for target")
			} else {
				// If the timestamp has passed, clear it
				lock.Lock()
				delete(modelMap, target)
				lock.Unlock()

				log.Ctx(ctx).Info().
					Time("expired_timestamp", retryTimestamp).
					Time("now", now).
					Str("model", model.String()).
					Str("target", target.String()).
					Msg("Cleared expired retry timestamp")
			}
		}
	}

	// Record metrics for the response
	targetsReturned.WithLabelValues(modelToString(req.Model)).Observe(float64(len(targetsResult)))

	// Record delay metric with target count as label
	targetCountStr := fmt.Sprintf("%d", len(targetsResult))
	delayReturnedMs.WithLabelValues(modelToString(req.Model), targetCountStr).Set(float64(delayMs))

	return &proto.GetTargetResponse{
		Targets: targetsResult,
		DelayMs: delayMs,
	}, nil
}

// ReportResponse handles the ReportResponse RPC call
// It stores response information for each model and target
func (s *ThirdPartyArbiterService) ReportResponse(ctx context.Context, req *proto.ReportResponseRequest) (*proto.ReportResponseResponse, error) {
	// Record metrics for request
	startTime := time.Now()
	requestsTotal.WithLabelValues("ReportResponse", "POST").Inc()

	// Increment active connections
	activeConnections.Inc()

	// Defer latency recording and decrement active connections
	defer func() {
		latency := time.Since(startTime).Seconds()
		reportResponseLatency.WithLabelValues(
			modelToString(req.Model),
			targetToString(req.Target),
		).Observe(latency)
		activeConnections.Dec()
	}()

	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	model := req.Model
	target := req.Target
	targetStr := thirdPartyClientToString(target)

	if targetStr == "" {
		log.Ctx(ctx).Error().Msg("Failed to convert target to string")
		return nil, status.Error(codes.Internal, "Failed to convert target to string")
	}

	// Check if the model is valid (not zero value)
	if model == 0 {
		log.Ctx(ctx).Error().Msg("Unknown model requested")
		return nil, status.Error(codes.InvalidArgument, "Unknown model requested")
	}

	loggerInfo := log.Ctx(ctx)
	if req.RetryAfterSeconds != nil {
		loggerInfo.Info().
			Bool("is_successful", req.IsSuccessful).
			Str("model", model.String()).
			Str("target", targetStr).
			Int32("retry_after_seconds", *req.RetryAfterSeconds).
			Msg("ReportResponse called")
	} else {
		loggerInfo.Info().
			Bool("is_successful", req.IsSuccessful).
			Str("model", model.String()).
			Str("target", targetStr).
			Msg("ReportResponse called")
	}

	// Check if the response code should be stored
	isSuccess := req.IsSuccessful

	reportResponseTotal.WithLabelValues(
		modelToString(model),
		targetToString(target),
		successToStatusString(isSuccess),
	).Inc()

	// Get the current timestamp
	now := s.clock.Now()

	// Handle retry_after_seconds if present
	if req.RetryAfterSeconds != nil && *req.RetryAfterSeconds > 0 {
		retryTimestamp := now.Add(time.Duration(*req.RetryAfterSeconds) * time.Second)

		// Check if a timestamp already exists
		retryLock := s.getRetryTimestampKeyLock(model, target)
		retryLock.RLock()

		// Check if the model map exists
		modelMap, ok := s.retryTimestamps[model]
		if !ok {
			// Create the model map
			retryLock.RUnlock()
			retryLock.Lock()
			modelMap = make(map[proto.ThirdPartyClient]time.Time)
			s.retryTimestamps[model] = modelMap
			retryLock.Unlock()

			// Set the retry timestamp
			retryLock.Lock()
			modelMap[target] = retryTimestamp
			retryLock.Unlock()

			log.Ctx(ctx).Info().
				Time("retry_timestamp", retryTimestamp).
				Str("model", model.String()).
				Str("target", targetStr).
				Int32("retry_after_seconds", *req.RetryAfterSeconds).
				Msg("Set retry timestamp for model and target")
		} else {
			// Check if a timestamp already exists for this target
			existingTimestamp, exists := modelMap[target]
			retryLock.RUnlock()

			// If a timestamp already exists, use the later of the current and new timestamps
			if !exists || retryTimestamp.After(existingTimestamp) {
				retryLock.Lock()
				modelMap[target] = retryTimestamp
				retryLock.Unlock()

				log.Ctx(ctx).Info().
					Time("retry_timestamp", retryTimestamp).
					Str("model", model.String()).
					Str("target", targetStr).
					Int32("retry_after_seconds", *req.RetryAfterSeconds).
					Msg("Set retry timestamp for model and target")

				// Record retry_after metric
				retryAfterMs.WithLabelValues(
					modelToString(model),
					targetToString(target),
				).Set(float64(*req.RetryAfterSeconds))

			} else {
				log.Ctx(ctx).Info().
					Time("existing_timestamp", existingTimestamp).
					Time("new_timestamp", retryTimestamp).
					Str("model", model.String()).
					Str("target", targetStr).
					Msg("Keeping existing retry timestamp (later than new one)")
			}
		}
	}

	// Manage buckets (clean up expired ones and create new ones if needed)
	s.manageBuckets(model, target, now)

	// Get the lock for this specific model and target
	lock := s.getResponseHistoryKeyLock(model, target)
	lock.Lock()

	// Get the model map
	modelMap, ok := s.responseHistory[model]
	if !ok {
		// This shouldn't happen since manageBuckets should have created it
		log.Ctx(ctx).Error().
			Str("model", model.String()).
			Str("target", targetStr).
			Msg("Model map not found after manageBuckets")
		lock.Unlock()
		return nil, status.Error(codes.Internal, "Model map not found after manageBuckets")
	}

	// Get the history
	history, ok := modelMap[target]
	if !ok || history.Buckets.Len() == 0 {
		// This shouldn't happen since manageBuckets should have created it
		log.Ctx(ctx).Error().
			Str("model", model.String()).
			Str("target", targetStr).
			Msg("History not found after manageBuckets")
		lock.Unlock()
		return nil, status.Error(codes.Internal, "History not found after manageBuckets")
	}

	// Get the current bucket (always the last one in the list)
	currentBucket := history.Buckets.Back().Value.(*ResponseBucket)

	// Update the bucket counts
	if isSuccess {
		currentBucket.SuccessCount++
	} else {
		currentBucket.FailureCount++
	}

	// Get values for logging
	bucketCount := history.Buckets.Len()
	latestBucketEndTime := history.LatestBucketEndTime
	runningSuccessCount := history.RunningSuccessCount
	runningFailureCount := history.RunningFailureCount
	lock.Unlock()

	log.Ctx(ctx).Debug().
		Int("bucket_count", bucketCount).
		Str("model", model.String()).
		Str("target", targetStr).
		Bool("success", isSuccess).
		Time("latest_bucket_end", latestBucketEndTime).
		Int("success_count", runningSuccessCount).
		Int("failure_count", runningFailureCount).
		Msg("Updated response bucket")

	// Return an empty response
	return &proto.ReportResponseResponse{}, nil
}

load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

go_library(
    name = "third_party_arbiter_server_lib",
    srcs = [
        "main.go",
        "metrics.go",
        "service.go",
    ],
    importpath = "github.com/augmentcode/augment/services/third_party_arbiter/server",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/clock:clock_go",
        "//base/logging:logging_go",
        "//base/tracing/go:tracing_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher/client:client_go",
        "//services/third_party_arbiter:third_party_arbiter_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "third_party_arbiter_server_test",
    srcs = [
        "metrics_test.go",
        "service_test.go",
    ],
    embed = [":third_party_arbiter_server_lib"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/clock:clock_go",
        "//services/lib/request_context:request_context_go",
        "//services/third_party_arbiter:third_party_arbiter_go_proto",
        "@com_github_prometheus_client_golang//prometheus/testutil",
        "@com_github_stretchr_testify//assert",
    ],
)

go_binary(
    name = "server",
    embed = [":third_party_arbiter_server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

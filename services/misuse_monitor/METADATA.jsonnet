{
  deployment: [
    {
      name: 'misuse-monitor',
      kubecfg: {
        target: '//services/misuse_monitor:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline', 'xiaolei'],
          slack_channel: '#services',
        },
      },
    },
  ],
}

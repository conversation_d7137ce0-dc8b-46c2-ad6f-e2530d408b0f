package main

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually suspend them.
var freeTrialAbuseDryRunFlag = featureflags.NewBoolFlag("free_trial_abuse_dry_run", true)

type FreeTrialAbuseJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	tenantCache         tenantwatcherclient.TenantCache
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure MisuseMonitorJob implements the Job interface
var _ Job = (*FreeTrialAbuseJob)(nil)

func NewFreeTrialAbuseJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCache,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialAbuseJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialAbuseJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-abuse",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		tenantCache:         tenantCache,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialAbuseJob) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialAbuseJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	freeTrialAbusers, err := m.getFreeTrialAbusers(ctx)
	if err != nil {
		return fmt.Errorf("error getting malicious users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(freeTrialAbusers))

	// Ban the users
	err = m.suspendFreeTrialAbusers(ctx, freeTrialAbusers)
	if err != nil {
		return fmt.Errorf("error banning users: %w", err)
	}

	return nil
}

type freeTrialAbuser struct {
	ID              string   `bigquery:"opaque_user_id"`
	TenantID        string   `bigquery:"tenant_id"`
	SessionID       string   `bigquery:"session_id"`
	MatchingUserIDs []string `bigquery:"matching_user_ids"`
	RequestCount    int      `bigquery:"request_count"`
}

func (m *FreeTrialAbuseJob) getFreeTrialAbusers(ctx context.Context) ([]*freeTrialAbuser, error) {
	// Construct the query.
	query := m.bqClient.Query(`
		-- Basic statistics: query count made per user and session
		WITH session_users AS (
		SELECT
			rm.session_id,
			rm.opaque_user_id,
			rm.tenant_id,
			COUNT(*) as request_count,
			COUNT(DISTINCT rm.opaque_user_id) OVER (PARTITION BY session_id) as user_count,
			SUM(COUNT(*)) OVER (PARTITION BY rm.session_id) as total_requests
		FROM request_metadata as rm
		JOIN tenant
			ON rm.tenant_id = tenant.id
   	 	WHERE rm.time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
			AND (rm.request_type = 'CHAT' OR rm.request_type = 'AGENT_CHAT')
			AND (tenant.tier = 'COMMUNITY' OR tenant.tier = 'PROFESSIONAL')
			AND NOT tenant.is_self_serve_team
		GROUP BY rm.session_id, rm.opaque_user_id, rm.tenant_id
		)
		-- Find users sharing a session
		SELECT DISTINCT
			su.opaque_user_id,
			su.tenant_id,
			su.session_id,
			su.request_count,
			ARRAY_AGG(DISTINCT other.opaque_user_id) AS matching_user_ids
		FROM session_users su
		JOIN session_users other
			ON su.session_id = other.session_id
			AND su.opaque_user_id != other.opaque_user_id
		WHERE su.user_count > 1
			AND su.total_requests > 1000
		GROUP BY 1, 2, 3, 4
		ORDER BY su.request_count DESC
		`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var freeTrialAbusers []*freeTrialAbuser
	for {
		var row freeTrialAbuser
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			freeTrialAbusers = append(freeTrialAbusers, &row)
		}
	}
	log.Info().Msgf("Found %d freeTrialAbusers to ban", len(freeTrialAbusers))
	return freeTrialAbusers, nil
}

func (m *FreeTrialAbuseJob) suspendFreeTrialAbusers(
	ctx context.Context,
	freeTrialAbusers []*freeTrialAbuser,
) error {
	dryRun, err := freeTrialAbuseDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not suspending users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(freeTrialAbusers)))

	// Limit users suspended per execution
	suspensionsToIssue := 10

	sessionId := requestcontext.NewRandomRequestSessionId()
	for _, abuser := range freeTrialAbusers {

		tenant, err := m.tenantCache.GetTenant(abuser.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "tenant_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting tenant %s", abuser.TenantID)
			continue
		}

		// Do the blocking.
		token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
			ctx, abuser.TenantID, tenant.ShardNamespace, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_RW},
		)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "token_exchange_error", strconv.FormatBool(dryRun)).Inc()
			log.Error().Err(err).Msgf("Error getting token for tenant %s", abuser.TenantID)
			continue
		}
		requestCtx := requestcontext.New(
			requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

		// First check if the user is already blocked or suspended for free trial abuse
		userObj, err := m.authClient.GetUser(ctx, requestCtx, abuser.ID, &abuser.TenantID)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", abuser.ID, abuser.TenantID, err)
			continue
		}
		if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
			alreadySuspended := false
			for _, suspension := range userObj.Suspensions {
				if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE {
					log.Info().Msgf("User %s is already suspended for free trial abuse in tenant %s", abuser.ID, abuser.TenantID)
					MisuseActionOutcome.WithLabelValues(m.jobName, "already_suspended", strconv.FormatBool(dryRun)).Inc()
					alreadySuspended = true
					break
				}
			}
			if alreadySuspended {
				continue
			}
		}

		log.Info().Msgf("Misuse monitor detected free trial abuse by user %s in tenant %s. Shared session ID: %s, matching user IDs: %s", abuser.ID, abuser.TenantID, abuser.SessionID, strings.Join(abuser.MatchingUserIDs, ", "))
		if suspensionsToIssue <= 0 {
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_limit_reached", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if !dryRun {
			// Issue free trial abuse suspension
			suspensionID, _, err := m.authClient.CreateUserSuspension(
				ctx, requestCtx, abuser.ID, abuser.TenantID, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE,
				fmt.Sprintf("Misuse monitor detected free trial abuse. Shared session ID: %s, matching user IDs: %s", abuser.SessionID, strings.Join(abuser.MatchingUserIDs, ", ")),
			)
			if err != nil {
				log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", abuser.ID, abuser.TenantID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
			suspensionsToIssue--
			log.Info().Msgf("Created suspension %s for user %s in tenant %s", suspensionID, abuser.ID, abuser.TenantID)
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}

syntax = "proto3";

package misuse_monitor;

import "google/protobuf/empty.proto";

option go_package = "github.com/augmentcode/augment/services/misuse_monitor/proto";

service MisuseMonitor {
  // TriggerJob triggers a job to run immediately
  rpc TriggerJob(TriggerJobRequest) returns (TriggerJobResponse) {}

  // GetJobStatus returns the status of a job
  rpc GetJobStatus(GetJobStatusRequest) returns (GetJobStatusResponse) {}

  // ListJobs returns a list of all available jobs
  rpc ListJobs(google.protobuf.Empty) returns (ListJobsResponse) {}
}

message TriggerJobRequest {
  string job_name = 1;
}

message TriggerJobResponse {
  bool success = 1;
  string message = 2;
}

message GetJobStatusRequest {
  string job_name = 1;
}

message GetJobStatusResponse {
  string job_name = 1;
  string status = 2;
  string last_run_time = 3;
  string last_run_duration = 4;
  string last_run_status = 5;
  string next_run_time = 6;
}

message JobInfo {
  string job_name = 1;
  string status = 2;
  string last_run_time = 3;
  string next_run_time = 4;
}

message ListJobsResponse {
  repeated JobInfo jobs = 1;
}

package main

import (
	"context"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

// Job defines the interface for a job that can be executed
type Job interface {
	// Run executes the job with the provided context
	Run(ctx context.Context) error

	// Close cleans up any resources used by the job
	Close()
}

// <PERSON><PERSON><PERSON><PERSON> manages the execution of a job on a schedule with support for
// immediate execution and graceful shutdown.
type JobRunner struct {
	name            string
	job             Job
	interval        time.Duration
	isRunning       bool
	lastRunTime     time.Time
	lastRunDuration time.Duration
	lastRunStatus   string
	nextRunTime     time.Time
	status          string
	mu              sync.RWMutex
	triggerCh       chan struct{}
}

// JobStatus represents the current status of a job
type JobStatus struct {
	Status          string
	LastRunTime     time.Time
	LastRunDuration time.Duration
	LastRunStatus   string
	NextRunTime     time.Time
}

// NewJobRunner creates a new job runner with the specified parameters
func NewJobRunner(name string, job Job, interval time.Duration) *JobRunner {
	return &JobRunner{
		name:      name,
		job:       job,
		interval:  interval,
		isRunning: false,
		triggerCh: make(chan struct{}, 1), // Buffer of 1 to avoid blocking
		status:    "idle",
	}
}

// Start begins the job runner's execution loop
func (r *JobRunner) Start(ctx context.Context, wg *sync.WaitGroup) {
	if wg != nil {
		wg.Add(1)
	}

	go func() {
		if wg != nil {
			defer wg.Done()
		}
		r.run(ctx)
	}()
}

// Trigger causes the job to run immediately
func (r *JobRunner) Trigger() {
	r.mu.Lock()
	defer r.mu.Unlock()

	// If the job is already running, we don't want to trigger another run
	if r.isRunning {
		log.Info().Str("job", r.name).Msg("Job already running, not triggering")
		return
	}

	log.Info().Str("job", r.name).Msg("Triggering immediate job execution")

	// Non-blocking send to trigger channel
	select {
	case r.triggerCh <- struct{}{}:
		// Successfully triggered
	default:
		// Channel already has a pending trigger, no need to send another
		log.Info().Str("job", r.name).Msg("Job already has a pending trigger")
	}
}

// IsRunning returns whether the job is currently running
func (r *JobRunner) IsRunning() bool {
	r.mu.Lock()
	defer r.mu.Unlock()
	return r.isRunning
}

// LastRunTime returns the time of the last job execution
func (r *JobRunner) LastRunTime() time.Time {
	r.mu.Lock()
	defer r.mu.Unlock()
	return r.lastRunTime
}

// GetStatus returns the current status of the job
func (r *JobRunner) GetStatus() JobStatus {
	r.mu.RLock()
	defer r.mu.RUnlock()

	return JobStatus{
		Status:          r.status,
		LastRunTime:     r.lastRunTime,
		LastRunDuration: r.lastRunDuration,
		LastRunStatus:   r.lastRunStatus,
		NextRunTime:     r.nextRunTime,
	}
}

// run is the main execution loop for the job runner
func (r *JobRunner) run(ctx context.Context) {
	// Run the job immediately on startup
	log.Info().Str("job", r.name).Msg("Initial job execution on startup")
	startTime := time.Now()
	r.executeJob(ctx)

	// Create a timer for the next scheduled run
	nextRunDelay := r.calculateNextRunDelay(startTime)
	timer := time.NewTimer(nextRunDelay)
	defer timer.Stop()

	// Set the next run time
	r.mu.Lock()
	r.nextRunTime = startTime.Add(r.interval)
	r.status = "scheduled"
	r.mu.Unlock()

	for {
		select {
		case <-ctx.Done():
			log.Info().Str("job", r.name).Msg("Context canceled, stopping job runner")
			r.job.Close()
			return

		case <-timer.C:
			log.Info().Str("job", r.name).Msg("Scheduled job execution triggered")
			startTime = time.Now()
			r.executeJob(ctx)
			nextRunDelay = r.calculateNextRunDelay(startTime)
			timer.Reset(nextRunDelay)

		case <-r.triggerCh:
			log.Info().Str("job", r.name).Msg("Manual job execution triggered")
			// Stop the current timer
			if !timer.Stop() {
				// Drain the timer channel if it already fired
				select {
				case <-timer.C:
				default:
				}
			}
			startTime = time.Now()
			r.executeJob(ctx)
			nextRunDelay = r.calculateNextRunDelay(startTime)
			timer.Reset(nextRunDelay)
		}

		// Update next run time after each execution
		r.mu.Lock()
		r.nextRunTime = startTime.Add(r.interval)
		r.status = "scheduled"
		r.mu.Unlock()
	}
}

// calculateNextRunDelay calculates the time until the next run should occur
// based on the start time of the previous run
func (r *JobRunner) calculateNextRunDelay(startTime time.Time) time.Duration {
	nextRun := startTime.Add(r.interval)
	now := time.Now()
	delay := nextRun.Sub(now)

	// If the calculated delay is negative (job took longer than interval),
	// run immediately
	if delay < 0 {
		return 0
	}

	return delay
}

// executeJob runs the job function with proper state management
func (r *JobRunner) executeJob(ctx context.Context) {
	// Set running state
	r.mu.Lock()
	if r.isRunning {
		r.mu.Unlock()
		log.Warn().Str("job", r.name).Msg("Job already running, skipping execution")
		return
	}
	r.isRunning = true
	r.status = "running"
	r.mu.Unlock()

	// Ensure we reset the running state when done
	defer func() {
		r.mu.Lock()
		r.isRunning = false
		r.lastRunTime = time.Now()
		r.mu.Unlock()
	}()

	start := time.Now()
	log.Info().Str("job", r.name).Msg("Running job")

	err := r.job.Run(ctx)

	duration := time.Since(start)

	r.mu.Lock()
	r.lastRunTime = start
	r.lastRunDuration = duration
	if err != nil {
		if ctx.Err() == context.Canceled {
			log.Info().
				Str("job", r.name).
				Dur("duration", duration).
				Msg("Job execution canceled")
		} else {
			log.Error().
				Err(err).
				Str("job", r.name).
				Dur("duration", duration).
				Msg("Job execution failed")
		}
		log.Error().Err(err).Str("job", r.name).Dur("duration", duration).Msg("Job failed")
		r.lastRunStatus = "error: " + err.Error()
	} else {
		log.Info().
			Str("job", r.name).
			Dur("duration", duration).
			Msg("Job completed successfully")
		r.lastRunStatus = "success"
	}
	r.status = "idle"
	r.mu.Unlock()
}

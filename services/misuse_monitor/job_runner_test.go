package main

import (
	"context"
	"sync"
	"testing"
	"time"
)

// <PERSON><PERSON><PERSON>ob implements the Job interface for testing
type MockJob struct {
	runCount int
	runDelay time.Duration
	mu       sync.Mutex
}

func NewMockJob(runDelay time.Duration) *MockJob {
	return &MockJob{
		runDelay: runDelay,
	}
}

func (m *<PERSON>ckJob) Run(ctx context.Context) error {
	m.mu.Lock()
	m.runCount++
	m.mu.Unlock()

	// Simulate job execution time
	select {
	case <-time.After(m.runDelay):
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

func (m *MockJob) Close() {}

func (m *MockJob) GetRunCount() int {
	m.mu.Lock()
	defer m.mu.Unlock()
	return m.runCount
}

func TestJobRunnerScheduledExecution(t *testing.T) {
	// Create a mock job that completes quickly
	mockJob := NewMockJob(10 * time.Millisecond)

	// Create a job runner with a short interval
	interval := 100 * time.Millisecond
	runner := NewJobRunner("test-job", mockJob, interval)

	// Create a context with cancellation
	ctx, cancel := context.WithTimeout(context.Background(), 350*time.Millisecond)
	defer cancel()

	// Start the job runner
	var wg sync.WaitGroup
	runner.Start(ctx, &wg)

	// Wait for the context to be done
	<-ctx.Done()
	wg.Wait()

	// The job should have run approximately 4 times:
	// 1. Initial run on startup
	// 2. After 100ms
	// 3. After 200ms
	// 4. After 300ms
	runCount := mockJob.GetRunCount()
	if runCount < 3 || runCount > 5 {
		t.Errorf("Expected job to run 3-5 times, but it ran %d times", runCount)
	}
}

func TestJobRunnerManualTrigger(t *testing.T) {
	// Create a mock job that takes some time to complete
	mockJob := NewMockJob(50 * time.Millisecond)

	// Create a job runner with a longer interval
	interval := 500 * time.Millisecond
	runner := NewJobRunner("test-job", mockJob, interval)

	// Create a context with cancellation
	ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	defer cancel()

	// Start the job runner
	var wg sync.WaitGroup
	runner.Start(ctx, &wg)

	// Wait for the initial job to complete
	time.Sleep(60 * time.Millisecond)

	// Trigger the job manually
	runner.Trigger()

	// Wait for the context to be done
	<-ctx.Done()
	wg.Wait()

	// The job should have run exactly 2 times:
	// 1. Initial run on startup
	// 2. Manual trigger
	runCount := mockJob.GetRunCount()
	if runCount != 2 {
		t.Errorf("Expected job to run exactly 2 times, but it ran %d times", runCount)
	}
}

func TestJobRunnerLongRunningJob(t *testing.T) {
	// Create a mock job that takes longer than the interval
	mockJob := NewMockJob(150 * time.Millisecond)

	// Create a job runner with a shorter interval
	interval := 100 * time.Millisecond
	runner := NewJobRunner("test-job", mockJob, interval)

	// Create a context with cancellation
	ctx, cancel := context.WithTimeout(context.Background(), 400*time.Millisecond)
	defer cancel()

	// Start the job runner
	var wg sync.WaitGroup
	runner.Start(ctx, &wg)

	// Wait for the context to be done
	<-ctx.Done()
	wg.Wait()

	// The job should have run approximately 3 times:
	// 1. Initial run on startup (0ms - 150ms)
	// 2. Immediately after first job (150ms - 300ms)
	// 3. Immediately after second job (300ms - 450ms, but context cancels at 400ms)
	// Note: test environment can be less stable
	runCount := mockJob.GetRunCount()
	if runCount < 2 || runCount > 4 {
		t.Errorf("Expected job to run 2-4 times, but it ran %d times", runCount)
	}
}

func TestJobRunnerAlreadyRunning(t *testing.T) {
	// Create a mock job that takes some time to complete
	mockJob := NewMockJob(100 * time.Millisecond)

	// Create a job runner
	runner := NewJobRunner("test-job", mockJob, 500*time.Millisecond)

	// Create a context with cancellation
	ctx, cancel := context.WithTimeout(context.Background(), 150*time.Millisecond)
	defer cancel()

	// Start the job runner
	var wg sync.WaitGroup
	runner.Start(ctx, &wg)

	// Wait for the job to start running
	time.Sleep(10 * time.Millisecond)

	// Try to trigger the job while it's already running
	runner.Trigger()

	// Wait for the context to be done
	<-ctx.Done()
	wg.Wait()

	// The job should have run exactly once (the initial run)
	// The trigger while running should have been ignored
	runCount := mockJob.GetRunCount()
	if runCount != 1 {
		t.Errorf("Expected job to run exactly once, but it ran %d times", runCount)
	}
}

func TestJobRunnerShutdown(t *testing.T) {
	// Create a mock job that takes a very long time to complete
	// (100 seconds ensures it won't finish naturally during the test)
	mockJob := NewMockJob(100 * time.Second)

	// Create a job runner
	runner := NewJobRunner("test-job", mockJob, 500*time.Millisecond)

	// Create a context with cancellation
	ctx, cancel := context.WithCancel(context.Background())

	// Start the job runner
	var wg sync.WaitGroup
	runner.Start(ctx, &wg)

	// Wait for the job to start running
	time.Sleep(10 * time.Millisecond)

	// Cancel the context to trigger shutdown
	startShutdown := time.Now()
	cancel()

	// Wait for the job runner to stop with a timeout
	shutdownComplete := make(chan struct{})
	go func() {
		wg.Wait()
		close(shutdownComplete)
	}()

	// Wait for shutdown with timeout
	select {
	case <-shutdownComplete:
		// Good, shutdown completed
	case <-time.After(1 * time.Second):
		t.Fatalf("Job runner did not shut down within timeout")
	}

	shutdownDuration := time.Since(startShutdown)
	t.Logf("Shutdown completed in %v", shutdownDuration)

	// The job should have run exactly once (the initial run)
	// and been interrupted by the shutdown
	runCount := mockJob.GetRunCount()
	if runCount != 1 {
		t.Errorf("Expected job to run exactly once, but it ran %d times", runCount)
	}
}

package main

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// Gauge to track current number of users found in each query execution
	MisuseUsersFound = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_misuse_users_found",
			Help: "Current number of users identified by misuse detection queries",
		},
		[]string{"job_name"},
	)

	// Counter for tracking action outcomes
	MisuseActionOutcome = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_misuse_action_outcome_total",
			Help: "Outcomes of actions taken for detected misuse",
		},
		[]string{"job_name", "outcome", "dry_run"},
	)
)

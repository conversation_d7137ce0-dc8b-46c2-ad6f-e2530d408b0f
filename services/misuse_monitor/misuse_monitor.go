package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"strconv"
	"strings"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/storage"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

const (
	tenantTableName    = "tenant"
	userTableName      = "user"
	maxInsertBatchSize = 1000
)

// If true, search for users but don't actually ban them.
var dryRunFlag = featureflags.NewBoolFlag("misuse_monitor_dry_run", true)

// Relative path under ban_list/ in the GCS bucket containing a CSV file of banned users.
// CSV format: opaque_user_id, email, tenant_id
var bannedUsersGcsPathFlag = featureflags.NewStringFlag("misuse_monitor_banned_users_gcs_path", "")

// Prefix for banned user lists in GCS bucket
const banListPrefix = "ban_list/"

type MisuseMonitorJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	tenantCache         tenantwatcherclient.TenantCache
	featureFlagHandle   featureflags.FeatureFlagHandle
	gcsBucketName       string
}

// Ensure MisuseMonitorJob implements the Job interface
var _ Job = (*MisuseMonitorJob)(nil)

func NewMisuseMonitorJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCache,
	featureFlagHandle featureflags.FeatureFlagHandle,
	gcsBucketName string,
) (*MisuseMonitorJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &MisuseMonitorJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "api-misuse",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		tenantCache:         tenantCache,
		featureFlagHandle:   featureFlagHandle,
		gcsBucketName:       gcsBucketName,
	}, nil
}

func (m *MisuseMonitorJob) Close() {
	m.bqClient.Close()
}

func (m *MisuseMonitorJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	maliciousUsers, err := m.getMaliciousUsers(ctx)
	if err != nil {
		return fmt.Errorf("error getting malicious users: %w", err)
	}

	// Get additional users to ban from GCS if configured
	gcsUsers, _ := m.getUsersFromGCS(ctx)
	if len(gcsUsers) > 0 {
		log.Info().Msgf("Found %d additional users to ban from GCS", len(gcsUsers))

		// Combine users from both sources with deduplication
		maliciousUsers = m.mergeUserLists(maliciousUsers, gcsUsers)
	}

	log.Info().Msgf("Total of %d users to process", len(maliciousUsers))

	// Ban the users
	err = m.banUsers(ctx, maliciousUsers)
	if err != nil {
		return fmt.Errorf("error banning users: %w", err)
	}

	return nil
}

type userInfo struct {
	ID        string   `bigquery:"id"`
	Email     string   `bigquery:"email"`
	TenantIDs []string `bigquery:"tenant_ids"`
}

// getUsersFromGCS reads a CSV file from GCS containing users to ban.
// The CSV format is: opaque_user_id, email, tenant_id
func (m *MisuseMonitorJob) getUsersFromGCS(ctx context.Context) ([]*userInfo, error) {
	// Get the relative path from feature flags
	relativePath, err := bannedUsersGcsPathFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error getting banned users path from feature flag")
		return nil, nil
	}

	// If no path is configured, return an empty list
	if relativePath == "" {
		log.Info().Msg("No banned users path configured")
		return nil, nil
	}

	// Use the bucket name from the configuration
	bucketName := m.gcsBucketName

	// Construct the full object path
	objectName := banListPrefix + relativePath
	log.Info().Msgf("Reading banned users from GCS bucket %s, object %s", bucketName, objectName)

	// Create a GCS client
	storageClient, err := storage.NewClient(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error creating GCS client")
		return nil, nil
	}
	defer storageClient.Close()

	// Get the object
	reader, err := storageClient.Bucket(bucketName).Object(objectName).NewReader(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Error reading from GCS")
		return nil, nil
	}
	defer reader.Close()

	// Parse the CSV
	csvReader := csv.NewReader(reader)
	csvReader.TrimLeadingSpace = true

	var users []*userInfo
	lineNum := 0

	// Process each line of the CSV
	for {
		lineNum++
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Error().Err(err).Msgf("Error reading CSV line %d", lineNum)
			continue
		}

		// Skip header row if present (first row with column names)
		if lineNum == 1 && (len(record) > 0 && strings.Contains(strings.ToLower(record[0]), "user")) {
			continue
		}

		// Validate and process the record
		user := parseCSVRecord(record, lineNum)
		if user != nil {
			users = append(users, user)
		}
	}

	log.Info().Msgf("Read %d users from GCS CSV file", len(users))
	return users, nil
}

// mergeUserLists combines two lists of users with deduplication based on user ID.
func (m *MisuseMonitorJob) mergeUserLists(list1, list2 []*userInfo) []*userInfo {
	// Use a map to deduplicate users by ID
	userMap := make(map[string]*userInfo)

	// Process first list
	for _, user := range list1 {
		userMap[user.ID] = user
	}

	// Process second list, merging tenant IDs if user already exists
	for _, user := range list2 {
		if existingUser, found := userMap[user.ID]; found {
			// User already exists, merge tenant IDs
			tenantMap := make(map[string]bool)
			for _, tenantID := range existingUser.TenantIDs {
				tenantMap[tenantID] = true
			}
			for _, tenantID := range user.TenantIDs {
				tenantMap[tenantID] = true
			}

			// Convert back to slice
			mergedTenantIDs := make([]string, 0, len(tenantMap))
			for tenantID := range tenantMap {
				mergedTenantIDs = append(mergedTenantIDs, tenantID)
			}
			existingUser.TenantIDs = mergedTenantIDs

			// If email is empty in existing user but present in new user, update it
			if existingUser.Email == "" && user.Email != "" {
				existingUser.Email = user.Email
			}
		} else {
			// New user, add to map
			userMap[user.ID] = user
		}
	}

	// Convert map back to slice
	result := make([]*userInfo, 0, len(userMap))
	for _, user := range userMap {
		result = append(result, user)
	}

	return result
}

// parseCSVRecord parses a CSV record into a userInfo object.
// Returns nil if the record is invalid.
func parseCSVRecord(record []string, lineNum int) *userInfo {
	// Validate the record has at least 3 fields
	if len(record) < 3 {
		log.Error().Msgf("Invalid CSV record at line %d, expected at least 3 fields but got %d", lineNum, len(record))
		return nil
	}

	opaqueUserID := strings.TrimSpace(record[0])
	email := strings.TrimSpace(record[1])
	tenantID := strings.TrimSpace(record[2])

	// Skip empty lines
	if opaqueUserID == "" || tenantID == "" {
		log.Warn().Msgf("Skipping CSV line %d: empty user ID or tenant ID", lineNum)
		return nil
	}

	// Create a userInfo object
	return &userInfo{
		ID:        opaqueUserID,
		Email:     email,
		TenantIDs: []string{tenantID},
	}
}

func (m *MisuseMonitorJob) getMaliciousUsers(ctx context.Context) ([]*userInfo, error) {
	// Construct the query.
	query := m.bqClient.Query(`
		-- Basic statistics
		WITH request_counts AS (
			SELECT
			meta.user_id,
			meta.opaque_user_id,
			meta.tenant,
			COUNT(DISTINCT request.request_id) as requests,
			COUNT(DISTINCT
				CASE WHEN (
				JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].added') IS NULL OR
				ARRAY_LENGTH(JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].added')) = 0
				)
				AND (
				JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].deleted') IS NULL OR
				ARRAY_LENGTH(JSON_QUERY_ARRAY(sanitized_json, '$.request.blobs[0].deleted')) = 0
				)
				AND (
				JSON_QUERY(sanitized_json, '$.request.blobs[0].baseline_checkpoint_id') IS NULL
				OR
				JSON_VALUE(sanitized_json, '$.request.blobs[0].baseline_checkpoint_id') = ""
				) THEN NULL ELSE request.request_id END
			) as request_with_blobs,
			count(distinct meta.session_id) as sessions

			FROM chat_host_request as request
			JOIN request_metadata as meta on request.request_id = meta.request_id
			WHERE request.time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
			AND meta.time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
			GROUP BY 1, 2, 3
		),
		-- Suspicous is to put it lightly here.
		-- We combine excessive session creation with never having a blob for a user here,
		-- most are likely guilty as charged
		bad_actors AS (
		SELECT
			distinct opaque_user_id
		FROM request_counts
		WHERE (request_with_blobs = 0 and requests >= 10 and sessions / requests >= 0.8)
		   OR requests >= 10 AND sessions / requests >= 0.9

        UNION DISTINCT

		-- Users that have invalid request ids, sessions ids, or identical request/session ids
		-- or use invalid user agent
		SELECT DISTINCT
			opaque_user_id
		FROM request_metadata
		WHERE time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY) AND request_type in ('CHAT', 'AGENT_CHAT')
		AND (
			-- invalid session id
			REGEXP_CONTAINS(
			session_id,
			r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
			)
			AND NOT REGEXP_CONTAINS(
			session_id,
			r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$'
			)
		OR
			-- invalid request id
			REGEXP_CONTAINS(
			request_id,
			r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
			)
			AND NOT REGEXP_CONTAINS(
			request_id,
			r'^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$'
			)
		OR request_id = session_id
		OR user_agent in (
			'Go-http-client/2.0',
			'Python/3.13 aiohttp/3.11.14',
			'undici',
			'Deno/2.1.10',
			'Reqable/2.33.3',
			'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)',
			'Python/3.12 aiohttp/3.11.14',
			'Deno/2.2.3',
			'Apifox/1.0.0 (https://apifox.com)',
			'Python/3.12 aiohttp/3.9.5',
			'axios/1.8.4',
			'Python/3.7 aiohttp/3.8.6',
			'Crow/Beast HTTP Client',
			'Bun/1.2.4',
			'chrome',
			'qa/JS 4.83.0',
			'Deno/2.2.5',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) CherryStudio/1.1.8 Chrome/126.0.6478.234 Electron/31.7.6 Safari/537.36',
			'python-requests/2.32.3',
			'PostmanRuntime/7.43.2',
			'Augment.openai-adapter/1.0.0',
			'node-fetch',
			'node',
			'PostmanRuntime/7.37.3',
			'python-requests/2.31.0',
			'Reqable/2.33.5'
			)
		)
		),
		-- deduplicate last hour's chat/agent activities by request ID
		-- some fraudulent clients reuse request ids and can cause problems when we join meta to chat info
		-- to get activity statistics
		meta AS (
		SELECT DISTINCT
				request_id, opaque_user_id, user_email
			FROM request_metadata
			WHERE time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 60 minute)
				AND request_type IN ('CHAT', 'AGENT_CHAT')
		)
		-- Currently we only ban those fraud accounts that have a very large traffic.
		-- Eventually that might need to change
		SELECT
			bad_actors.opaque_user_id as id,
			COALESCE(meta.user_email, "") AS email,
			ARRAY_AGG(DISTINCT tenant_id) AS tenant_ids
		FROM meta
		JOIN chat_host_request as chat ON chat.request_id = meta.request_id AND chat.time > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 60 minute)
		JOIN bad_actors ON bad_actors.opaque_user_id = meta.opaque_user_id
		GROUP BY 1, 2
		HAVING COUNT(*) > 4

		UNION ALL

		SELECT
			request_suspicious.opaque_user_id AS id,
			request_suspicious.user_email AS email,
			ARRAY_AGG(DISTINCT request_suspicious.tenant_id) AS tenant_ids
		FROM request_suspicious
		JOIN request_blocked ON request_blocked.opaque_user_id = request_suspicious.opaque_user_id
		WHERE request_suspicious.time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 HOUR)
		   AND request_blocked.time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 HOUR)
		   AND request_suspicious.check_type = 'suspicious_strict_regex'
		GROUP BY 1, 2
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var users []*userInfo
	for {
		var row userInfo
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			users = append(users, &row)
		}
	}
	log.Info().Msgf("Found %d users to ban", len(users))
	return users, nil
}

func (m *MisuseMonitorJob) banUsers(
	ctx context.Context,
	users []*userInfo,
) error {
	dryRun, err := dryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not banning users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(users)))

	sessionId := requestcontext.NewRandomRequestSessionId()
	for _, user := range users {
		for _, tenantID := range user.TenantIDs {
			log.Info().Msgf("Banning user %s from tenant %s", user.ID, tenantID)

			tenant, err := m.tenantCache.GetTenant(tenantID)
			if err != nil {
				MisuseActionOutcome.WithLabelValues(m.jobName, "tenant_not_found", strconv.FormatBool(dryRun)).Inc()
				log.Error().Err(err).Msgf("Error getting tenant %s", tenantID)
				continue
			}

			// Skip enterprise tenants - only block users in non-enterprise tenants
			if tenant.Tier == tenantproto.TenantTier_ENTERPRISE {
				MisuseActionOutcome.WithLabelValues(m.jobName, "in_enterprise_tenant", strconv.FormatBool(dryRun)).Inc()
				log.Info().Msgf("Skipping enterprise tenant %s", tenantID)
				continue
			}

			token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
				ctx, tenantID, tenant.ShardNamespace, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_RW},
			)
			if err != nil {
				MisuseActionOutcome.WithLabelValues(m.jobName, "token_exchange_error", strconv.FormatBool(dryRun)).Inc()
				return fmt.Errorf("error getting token for tenant: %v", err)
			}
			requestCtx := requestcontext.New(
				requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

			// First check if the user is already blocked or suspended for API abuse
			tenantIDPtr := tenantID
			userObj, err := m.authClient.GetUser(ctx, requestCtx, user.ID, &tenantIDPtr)
			if err != nil {
				MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
				log.Error().Msgf("Error getting user %s from tenant %s: %v", user.ID, tenantID, err)
				continue
			}
			if userObj == nil {
				MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
				log.Error().Msgf("Error getting user for suspension for user %s in tenant %s: %v", user.ID, tenantID, err)
				continue
			}
			if userObj.Blocked {
				MisuseActionOutcome.WithLabelValues(m.jobName, "already_blocked", strconv.FormatBool(dryRun)).Inc()
				log.Info().Msgf("User %s is already blocked in tenant %s", user.ID, tenantID)
				continue
			}
			if userObj.Suspensions != nil && len(userObj.Suspensions) > 0 {
				alreadySuspended := false
				for _, suspension := range userObj.Suspensions {
					if suspension.SuspensionType == auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE {
						MisuseActionOutcome.WithLabelValues(m.jobName, "already_blocked", strconv.FormatBool(dryRun)).Inc()
						log.Info().Msgf("User %s is already suspended for API abuse in tenant %s", user.ID, tenantID)
						alreadySuspended = true
						break
					}
				}
				if alreadySuspended {
					continue
				}
			}

			if !dryRun {
				// Issue API abuse suspension, which will block and log out the user.
				suspensionID, tokensDeleted, err := m.authClient.CreateUserSuspension(
					ctx, requestCtx, user.ID, tenantID, auth_entities.UserSuspensionType_USER_SUSPENSION_TYPE_API_ABUSE, "Misuse monitor detected API abuse", // TODO: Better evidence detail

				)
				if err != nil {
					MisuseActionOutcome.WithLabelValues(m.jobName, "create_suspension_error", strconv.FormatBool(dryRun)).Inc()
					log.Error().Msgf("Error creating suspension for user %s from tenant %s: %v", user.ID, tenantID, err)
					continue
				}
				log.Info().Msgf(
					"Created suspension %s. Deleted %d tokens for user %s in tenant %s", suspensionID, tokensDeleted, user.ID, tenantID)
			}
			MisuseActionOutcome.WithLabelValues(m.jobName, "suspension_created", strconv.FormatBool(dryRun)).Inc()
		}
	}

	return nil
}

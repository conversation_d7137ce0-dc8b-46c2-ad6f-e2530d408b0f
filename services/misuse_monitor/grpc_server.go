package main

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/augmentcode/augment/services/misuse_monitor/proto"
)

// MisuseMonitorServer implements the MisuseMonitor gRPC service
type MisuseMonitorServer struct {
	proto.UnimplementedMisuseMonitorServer
	jobRunners map[string]*JobRunner
	mu         sync.RWMutex
}

// NewMisuseMonitorServer creates a new MisuseMonitorServer
func NewMisuseMonitorServer() *MisuseMonitorServer {
	return &MisuseMonitorServer{
		jobRunners: make(map[string]*JobRunner),
	}
}

// RegisterJobRunner registers a job runner with the server
func (s *MisuseMonitorServer) RegisterJobRunner(name string, runner *JobRunner) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.jobRunners[name] = runner
}

// TriggerJob triggers a job to run immediately
func (s *MisuseMonitorServer) TriggerJob(ctx context.Context, req *proto.TriggerJobRequest) (*proto.TriggerJobResponse, error) {
	s.mu.RLock()
	runner, ok := s.jobRunners[req.JobName]
	s.mu.RUnlock()

	if !ok {
		return &proto.TriggerJobResponse{
			Success: false,
			Message: fmt.Sprintf("Job %s not found", req.JobName),
		}, nil
	}

	runner.Trigger()

	return &proto.TriggerJobResponse{
		Success: true,
		Message: fmt.Sprintf("Job %s triggered", req.JobName),
	}, nil
}

// GetJobStatus returns the status of a job
func (s *MisuseMonitorServer) GetJobStatus(ctx context.Context, req *proto.GetJobStatusRequest) (*proto.GetJobStatusResponse, error) {
	s.mu.RLock()
	runner, ok := s.jobRunners[req.JobName]
	s.mu.RUnlock()

	if !ok {
		return nil, fmt.Errorf("job %s not found", req.JobName)
	}

	status := runner.GetStatus()

	return &proto.GetJobStatusResponse{
		JobName:         req.JobName,
		Status:          status.Status,
		LastRunTime:     status.LastRunTime.Format(time.RFC3339),
		LastRunDuration: status.LastRunDuration.String(),
		LastRunStatus:   status.LastRunStatus,
		NextRunTime:     status.NextRunTime.Format(time.RFC3339),
	}, nil
}

// ListJobs returns a list of all available jobs
func (s *MisuseMonitorServer) ListJobs(ctx context.Context, _ *emptypb.Empty) (*proto.ListJobsResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	response := &proto.ListJobsResponse{
		Jobs: make([]*proto.JobInfo, 0, len(s.jobRunners)),
	}

	for name, runner := range s.jobRunners {
		status := runner.GetStatus()
		response.Jobs = append(response.Jobs, &proto.JobInfo{
			JobName:     name,
			Status:      status.Status,
			LastRunTime: status.LastRunTime.Format(time.RFC3339),
			NextRunTime: status.NextRunTime.Format(time.RFC3339),
		})
	}

	return response, nil
}

// StartGRPCServer starts the gRPC server
func StartGRPCServer(ctx context.Context, config *Config, server *MisuseMonitorServer, wg *sync.WaitGroup) error {
	wg.Add(1)
	go func() {
		defer wg.Done()

		lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to listen")
		}

		var opts []grpc.ServerOption

		// Add TLS if configured
		if config.ServerMtls != nil {
			creds, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
			if err != nil {
				log.Fatal().Err(err).Msg("Failed to create server TLS credentials")
			}
			opts = append(opts, grpc.Creds(creds))
		}

		grpcServer := grpc.NewServer(opts...)

		// Register our service
		proto.RegisterMisuseMonitorServer(grpcServer, server)

		// Register health service
		healthServer := health.NewServer()
		healthgrpc.RegisterHealthServer(grpcServer, healthServer)
		healthServer.SetServingStatus("", healthgrpc.HealthCheckResponse_SERVING)
		healthServer.SetServingStatus("misuse_monitor.MisuseMonitor", healthgrpc.HealthCheckResponse_SERVING)

		// Register reflection service for debugging
		reflection.Register(grpcServer)

		log.Info().Int("port", config.Port).Msg("Starting gRPC server")

		// Start server in a goroutine
		go func() {
			if err := grpcServer.Serve(lis); err != nil {
				log.Error().Err(err).Msg("Failed to serve gRPC")
			}
		}()

		// Wait for context cancellation to stop server
		<-ctx.Done()
		log.Info().Msg("Shutting down gRPC server")
		grpcServer.GracefulStop()
	}()

	return nil
}

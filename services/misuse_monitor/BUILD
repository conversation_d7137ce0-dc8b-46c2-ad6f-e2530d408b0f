load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:go.bzl", "go_binary", "go_grpc_library", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "misuse_monitor_proto",
    srcs = ["misuse_monitor.proto"],
    deps = [
        "@protobuf//:empty_proto",
    ],
)

go_grpc_library(
    name = "misuse_monitor_go_proto",
    importpath = "github.com/augmentcode/augment/services/misuse_monitor/proto",
    proto = ":misuse_monitor_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "misuse_monitor_lib",
    srcs = [
        "community_abuse.go",
        "free_trial_abuse.go",
        "free_trial_duplication.go",
        "grpc_server.go",
        "job_runner.go",
        "main.go",
        "metrics.go",
        "misuse_monitor.go",
        "util.go",
    ],
    importpath = "github.com/augmentcode/augment/services/misuse_monitor",
    visibility = ["//visibility:private"],
    deps = [
        ":misuse_monitor_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/logging:logging_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/util:go_lib",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_googleapis_gax_go_v2//:gax-go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_bigquery//:bigquery",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_api//iterator:go_default_library",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "misuse_monitor",
    embed = [":misuse_monitor_lib"],
)

go_test(
    name = "misuse_monitor_test",
    srcs = ["misuse_monitor_test.go"],
    embed = [":misuse_monitor_lib"],
)

go_test(
    name = "job_runner_test",
    srcs = ["job_runner_test.go"],
    embed = [":misuse_monitor_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":misuse_monitor",
    # Add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/analytics_dataset:dataset_lib",
        "//services/request_insight/lib:bigquery_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

worker_processes 1;
events {
    worker_connections 1024;
}

error_log stderr debug;

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    keepalive_timeout 65;

    # Pull $aug_tenant out of the request's HTTP Hostname.
    map $http_host $aug_tenant {
        # An optional subdomain is allowed and discarded. This can be used for regionalized naming
        # in case a tenant is multi-regional. E.g., tenant0(.eu-west4)?.psc.augmentcode.com.
        ~^(?<aug_tenant_m>[^.]+)(\.(?<opt_subdomain>[^.]+))?\.%(psc_domain_escaped)s$ $aug_tenant_m;
    }

    # Map $aug_namespace from $aug_tenant (with a default fallback to the current namespace).
    map $aug_tenant $aug_namespace {
        include %(tenant_map_path)s;
        default %(default_k8s_namespace)s;
    }

    server {
        listen %(port)d proxy_protocol ssl default_server;
        server_name _;

        ssl_certificate            "%(tls_crt_path)s";
        ssl_certificate_key        "%(tls_key_path)s";
        ssl_ciphers                HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;
        ssl_session_cache          shared:SSL:1m;
        ssl_session_timeout        5m;

        error_page 500 502 503 504 /50x.html;
        location /50x.html {
            root /usr/share/nginx/html;
        }

        location / {
            root /usr/share/nginx/html;

            # nginx does not use the system resolver for resolving variable hostnames (it only
            # uses the system resolver for static hostnames at config-parsing-time).
            resolver kube-dns.kube-system.svc.cluster.local;

            proxy_pass https://%(api_proxy_svc)s.$aug_namespace.svc.cluster.local:%(api_proxy_port)d;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $proxy_protocol_addr;
            proxy_set_header X-Forwarded-For $proxy_protocol_addr;
        }
    }
}

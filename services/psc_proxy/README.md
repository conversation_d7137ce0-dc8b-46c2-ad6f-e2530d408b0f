# Private Service Connect - Proxy

## Background

[GCP Private Service Connect (PSC)][psc-doc] is used to provide connectivity
from a client (*consumer*) VPC subnet to a service (*producer*) in a separate
VPC subnet -- including across organizational boundaries. We have enabled
support for such connections at the request of at least one custom. See the
[RFC][rfc] for details. Also see the GKE-specific [codelab][psc-gke-codelab].

## Status

NOTE: THIS IS A WIP. The service is available (but not by default) in
`dev_deploy`. It is not yet enabled in any prod namespace.

[psc-doc]: https://cloud.google.com/vpc/docs/private-service-connect
[psc-gke-codelab]: https://codelabs.developers.google.com/cloudnet-psc-ilb-gke
[rfc]: https://www.notion.so/RFC-Google-Cloud-Private-Service-Connect-offering-15abba10175a80fba71ec269bd793abb

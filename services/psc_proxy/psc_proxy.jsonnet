local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';

function(env, namespace, cloud, namespace_config) {
  _ret:: [self.cert.objects, self.sa, self.conf, self.deploy, self.svc, self.attach],
  local appName = 'psc-proxy',
  local C = cloudInfo[cloud],

  local replicas = if env == 'PROD' then 3 else 1,
  local cpu = '1',
  local memory = '1Gi',
  local nginx_image = 'nginx:1.27',  // latest 1.27.x
  local nginx_conf_str = importstr './services/psc_proxy/nginx.conf',
  local ext_port = 443,
  local int_port = 4430,
  local api_proxy_svc = 'api-proxy-svc',
  local api_proxy_port = 8082,

  obj:: {
    apiVersion: error 'apiVersion is required',
    kind: error 'kind is required',
    metadata+: {
      name: appName,
      namespace: namespace,
      labels+: {
        app: appName,
        'aug.cloud': cloud,
        'aug.env': env,
      },
    },
  },

  cert:: certLib.createPublicServerCert(
    env=env,
    namespace=namespace,
    appName=appName,
    name=appName,
    volumeName=appName + '-tls',
    dnsNames=[
      // The main name is a wildcard for the psc domain.
      '*.' + C.privateServiceConnectDomain,
      // Also support regionalized hostnames for multi-regional tenants.
      '*.' + C.region + '.' + C.privateServiceConnectDomain,
    ],
  ) + {
    objects+: {
      metadata+: {
        labels+: $.obj.metadata.labels,
      },
      spec+: {
        commonName: null,
        secretName: appName + '-tls',
      },
    },
    podVolumeDef+: {
      secret+: {
        secretName: appName + '-tls',
      },
    },
  },

  // A simple SA, no RBAC required.
  sa:: self.obj + {
    apiVersion: 'v1',
    kind: 'ServiceAccount',
  },

  conf:: self.obj + {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata+: {
      name+: '-conf',
      labels+: {
        'to-gc': 'true',  // as seen in config-map-lib.jsonnet
      },
    },

    data: {
      'nginx.conf': nginx_conf_str % {
        port: int_port,
        tls_crt_path: $.cert.config.cert_path,
        tls_key_path: $.cert.config.key_path,
        psc_domain_escaped: std.strReplace(C.privateServiceConnectDomain, '.', '\\.'),
        api_proxy_svc: api_proxy_svc,
        api_proxy_port: api_proxy_port,
        tenant_map_path: '/run/augment/conf/augment-tenant-namespace-map.conf',
        default_k8s_namespace: namespace,
      },
      'augment-tenant-namespace-map.conf': |||
        # An example nginx map that can be generated from tenant jsonnet, or
        # at runtime from tenant crds. The fallback looks for api-proxy-svc in
        # the current namespace.
        sifive e1;
      |||,
    },
  },

  deploy:: self.obj + {
    local o = self,

    apiVersion: 'apps/v1',
    kind: 'Deployment',

    metadata+: {
      labels+: {
        'config.md5': std.md5(std.manifestJson($.conf.data)),  // force pod restart on config change
      },
    },

    spec+: {
      selector+: {
        matchLabels+: {
          'k8s.deployment': o.metadata.name,
        },
      },
      replicas: replicas,
      template+: {
        metadata+: {
          labels+: o.metadata.labels + {
            'k8s.deployment': o.metadata.name,
          },
        },
        spec+: {
          serviceAccountName: $.sa.metadata.name,
          priorityClassName: cloudInfo.envToPriorityClass(env),
          affinity: nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName),
          tolerations: nodeLib.tolerations(resource=null, env=env, cloud=cloud),
          containers: [
            {
              name: 'nginx',
              image: nginx_image,
              imagePullPolicy: 'Always',
              command: ['nginx', '-g', 'daemon off;', '-c', _conf_mnt.mountPath + '/nginx.conf'],
              volumeMounts: [
                $.cert.volumeMountDef,
                _conf_mnt,
              ],
              resources+: {
                requests+: {
                  cpu: cpu,
                  memory: memory,
                },
                limits+: self.requests,
              },
            },
          ],
          volumes: [
            $.cert.podVolumeDef,
            _conf_vol,
          ],
          local _conf_vol = {
            name: $.conf.metadata.name,
            configMap: {
              name: $.conf.metadata.name,
            },
          },
          local _conf_mnt = {
            name: _conf_vol.name,
            mountPath: '/run/augment/conf',
          },
        },
      },
    },
  },

  svc:: self.obj + {
    apiVersion: 'v1',
    kind: 'Service',
    metadata+: {
      annotations+: {
        // https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#service_parameters
        'networking.gke.io/load-balancer-type': 'Internal',
        'networking.gke.io/internal-load-balancer-allow-global-access': 'true',
      },
    },
    spec+: {
      type: 'LoadBalancer',
      selector: {
        'k8s.deployment': $.deploy.metadata.name,
      },
      ports: [
        {
          name: 'https',
          port: ext_port,
          targetPort: int_port,
        },
      ],
    },
  },

  attach:: self.obj + {
    apiVersion: 'networking.gke.io/v1beta1',
    kind: 'ServiceAttachment',
    spec+: {
      connectionPreference: 'ACCEPT_MANUAL',
      natSubnets: ['psc0'],
      proxyProtocol: true,
      resourceRef: {
        kind: $.svc.kind,
        name: $.svc.metadata.name,
      },
    },
  },

}._ret

syntax = "proto3";

package agent_loop;

import "base/blob_names/blob_names.proto";

// All agent APIs are experimental and subject to change.
// Note that all of these APIs will use the service token in the metadata to
// automatically filter requests and responses to the correct tenant and user
service AgentLoop {
  rpc AwaitMessage(BeachheadMessage) returns (AgentLoopMessage) {}
}

// Note that while each connection is initiated by the beachhead,
// the beachhead is actually responding to requests for tool calls from
// the agent loop server.
message AgentLoopMessage {
  // The remote_agent_id should be a unique identifier for the whole agent run initiated
  // by the user. Namely, it is used to tie together the container and the instruction.
  // The beachhead will pass the remote_agent_id it was given to the agent loop service.
  string remote_agent_id = 1;

  // Must increment by one on every message from either the agent loop or the beachhead.
  // TODO(jeff): Add a way to signal back errors, the agent loop should issue an error if
  // the sequence_id is not exactly one greater than its previous message.
  uint32 sequence_id = 2;

  oneof message {
    // The loop is finished, and the beachhead should not send any other requests.
    // TODO(jeff): For now, this is just a way to end the loop (or else the connection
    // will be forever open. Later think about how this interacts with the whole lifecyycle,
    // e.g. the agent loop tells the agent management service that it is complete/waiting for user
    // and then the agent management service actually initiates the shutdown on both ends.
    ShutdownLoop shutdown_loop = 3;

    BeachheadToolRequest tool_request = 4;

    // Requests the current state of the workspace. Note that the beachhead has control
    // over the implementation, e.g. it could periodically update so that when the workspace
    // is actually requested, little extra work is required.
    WorkspaceRequest workspace_request = 5;
  }
}

message ShutdownLoop {}

message WorkspaceRequest {}

// The beachhead and agent loop server have to agree on the tool specifications.
// Note: Protobuf recommends adding an enum prefix to avoid scoping conflicts.
// See https://protobuf.dev/programming-guides/style/#enums.
enum ToolId {
  TOOL_ID_UNKNOWN = 0;
  TOOL_ID_READ_FILE = 1;
  // TODO: add other tool ids.
}

message BeachheadToolRequest {
  ToolId tool_id = 1;
  string tool_input_json = 2;
}

message WorkspaceResponse {
  repeated base.blob_names.Blobs blobs = 1;
}

message BeachheadMessage {
  // The remote_agent_id should be a unique identifier for the whole agent run initiated
  // by the user. Namely, it is used to tie together the container and the instruction.
  // The beachhead will pass the remote_agent_id it was given to the agent loop service.
  string remote_agent_id = 1;

  // Must increment by one on every message from either the agent loop or the beachhead.
  uint32 sequence_id = 2;

  oneof message {
    // Start a loop, and await commands.
    StartLoop start_loop = 3;

    BeachheadToolResponse tool_response = 4;

    WorkspaceResponse workspace_response = 5;
  }
}

message StartLoop {
  // Tools that the beachhead is able to execute.
  repeated ToolId available_tools = 1;
}

// Status codes for beachhead tool execution responses
// Note: Protobuf recommends adding an enum prefix to avoid scoping conflicts.
// See https://protobuf.dev/programming-guides/style/#enums.
enum BeachheadToolResponseStatus {
  // Unknown status
  BEACHHEAD_TOOL_RESPONSE_STATUS_UNKNOWN = 0;

  // Tool executed successfully
  BEACHHEAD_TOOL_RESPONSE_STATUS_SUCCESS = 1;

  // Tool not found
  BEACHHEAD_TOOL_RESPONSE_STATUS_NOT_FOUND = 2;

  // Invalid input that violates the tool's input schema
  BEACHHEAD_TOOL_RESPONSE_STATUS_INVALID_INPUT = 3;

  // Tool execution failed
  BEACHHEAD_TOOL_RESPONSE_STATUS_EXECUTION_ERROR = 4;
}

message BeachheadToolResponse {
  // The main output string that will be shown to the model.
  string tool_output = 1;

  // A description of what the tool did, for displaying in the UI or logging.
  string tool_result_message = 2;

  // Status code of the response
  BeachheadToolResponseStatus status = 3;
}

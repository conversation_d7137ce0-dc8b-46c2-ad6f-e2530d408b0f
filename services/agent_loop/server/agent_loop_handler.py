"""Agent loop handler."""

from dataclasses import dataclass

import services.agent_loop.agent_loop_pb2 as agent_loop_pb2
from services.agents.agents_pb2_grpc import AgentsStub
from services.api_proxy.model_finder_pb2_grpc import ModelFinderStub
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import Request<PERSON>ontext
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)


@dataclass
class AgentLoopHandler:
    """Handler for Agent<PERSON>oop.

    Contains the main business logic of agent loops.
    """

    ri_publisher: RequestInsightPublisher
    agents_client: AgentsStub
    model_finder: ModelFinderStub

    def await_message(
        self,
        request: agent_loop_pb2.BeachheadMessage,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> agent_loop_pb2.AgentLoopMessage:
        # TODO(jeff): This is all dummy code to test that the server is working.
        message = agent_loop_pb2.AgentLoopMessage()
        message.remote_agent_id = request.remote_agent_id
        message.sequence_id = request.sequence_id + 1
        shutdown_loop = agent_loop_pb2.ShutdownLoop()
        message.shutdown_loop.MergeFrom(shutdown_loop)
        return message

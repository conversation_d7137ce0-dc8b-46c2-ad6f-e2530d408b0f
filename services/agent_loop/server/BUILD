load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_binary(
    name = "agent_loop_server",
    srcs = [
        "agent_loop_server.py",
    ],
    deps = [
        ":agent_loop_handler",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//base/python/grpc:client_options",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/agents/client",
        "//services/api_proxy/client:grpc_client",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
        "//services/token_exchange/client:client_py",
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("grpcio-reflection"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
    ],
)

py_library(
    name = "agent_loop_handler",
    srcs = [
        "agent_loop_handler.py",
    ],
    deps = [
        "//services/agent_loop:agent_loop_py_proto",
        "//services/agents:agents_py_proto",
        "//services/api_proxy:model_finder_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
    ],
)

pytest_test(
    name = "agent_loop_handler_test",
    srcs = [
        "agent_loop_handler_test.py",
    ],
    deps = [
        ":agent_loop_handler",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":agent_loop_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

# TODO(jeff): METADATA.jsonnet when agent_loop_server is ready.

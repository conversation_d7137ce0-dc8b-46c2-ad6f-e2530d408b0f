"""Server for the AgentLoop service."""

import argparse
import logging
import os
import pathlib
import threading
import typing
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
import prometheus_client
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import base.feature_flags
import base.tracing
import services.agents.client.client as agents_client
import services.api_proxy.client.grpc_client as model_finder_client
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.grpc import client_options
from base.python.signal_handler.signal_handler import GracefulSignalHandler
from services.agent_loop import agent_loop_pb2, agent_loop_pb2_grpc
from services.agent_loop.server.agent_loop_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.request_context.request_context import RequestContext
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.token_exchange.client import client as token_exchange_client

log = structlog.get_logger()
tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for the AgentLoop server."""

    # the port the grpc server will listen on
    grpc_port: int

    # the path to the feature flag sdk key
    feature_flags_sdk_key_path: typing.Optional[str]

    # the endpoint for the dynamic feature flags service or None if not used
    dynamic_feature_flags_endpoint: typing.Optional[str]

    # the configuration for the token authentication
    auth_config: AuthConfig

    # endpoint for agents server to run remote tools
    agents_endpoint: str

    # endpoint for model finder to find chat hosts.
    model_finder_endpoint: str

    # TLS configuration for the central clients
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the client to talk to GRPC services in the same namespace
    client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the server
    server_mtls: typing.Optional[tls_config.ServerConfig] = None

    # Grace period for the server to shutdown
    shutdown_grace_period_s: int = 25

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )


class AgentLoopServicer(agent_loop_pb2_grpc.AgentLoopServicer):
    """AgentLoopServicer RPC server."""

    def __init__(
        self,
        config: Config,
        handler: AgentLoopHandler,
    ):
        self.config = config
        self.handler = handler

    def AwaitMessage(
        self,
        request: agent_loop_pb2.BeachheadMessage,
        context: grpc.ServicerContext,
    ) -> agent_loop_pb2.AgentLoopMessage:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.await_message(request, request_context, auth_info)


def _make_agents_client(config: Config):
    """Returns a client to the agents service."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.agents_endpoint
        )
    )
    return agents_client.setup_stub(
        config.agents_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
        options,
    )


def _make_model_finder_client(config: Config):
    """Returns a model finder client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.model_finder_endpoint
        )
    )
    return model_finder_client.setup_stub(
        config.model_finder_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
        options,
    )


def _make_handler(
    config: Config, ri_publisher: RequestInsightPublisher
) -> AgentLoopHandler:
    model_finder = _make_model_finder_client(config)
    agents_client = _make_agents_client(config)
    return AgentLoopHandler(
        ri_publisher,
        agents_client,
        model_finder,
    )


def _serve(
    config: Config,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    namespace = os.environ["POD_NAMESPACE"]
    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)
    server = grpc.server(
        ThreadPoolExecutor(max_workers=20),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )

    # Reply to health check RPCs
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    agent_loop_pb2_grpc.add_AgentLoopServicer_to_server(
        AgentLoopServicer(config, _make_handler(config, ri_publisher)),
        server,
    )
    service_names = (
        agent_loop_pb2.DESCRIPTOR.services_by_name["AgentLoop"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)
    if server_credentials:
        actual_port = server.add_secure_port(
            f"[::]:{config.grpc_port}", server_credentials
        )
    else:
        actual_port = server.add_insecure_port(f"[::]:{config.grpc_port}")
    server.start()
    logging.info("Listening on %s", actual_port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def main():
    # Set up the signal handler
    # This will catch SIGTERM and SIGINT and exit gracefully
    standard_handler = GracefulSignalHandler()

    # Set up the logging
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()
    ri_publisher = RequestInsightPublisher.create_from_args(args)

    prometheus_client.start_http_server(9090)

    _serve(config, ri_publisher, standard_handler.get_shutdown_event())


if __name__ == "__main__":
    main()

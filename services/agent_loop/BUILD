load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "agent_loop_proto",
    srcs = ["agent_loop.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
    ],
)

py_grpc_library(
    name = "agent_loop_py_proto",
    protos = [":agent_loop_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_py_proto",
    ],
)

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")

DEPS = [
    "//base/python/opentelemetry_utils:traced_threadpool",
    "//base/feature_flags:feature_flags_py",
    "//base/python/grpc:client_options",
    "//base/logging:struct_logging",
    "//base/python/signal_handler",
    "//base/tracing:tracing_py",
    "//services/chat_host:chat_proto_py_proto",
    "//services/lib/grpc/auth:service_auth",
    "//services/lib/grpc/auth:service_token_auth",
    "//services/lib/grpc/auth:service_auth_interceptor",
    "//services/lib/grpc/metrics:metrics",
    "//services/lib/grpc/tls_config:grpc_tls_config_py",
    "//services/token_exchange/client:client_py",
    requirement("dataclasses_json"),
    requirement("grpcio-reflection"),
    requirement("grpcio"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("protobuf"),
    requirement("prometheus-client"),
]

py_binary(
    name = "fake_chat_server",
    srcs = [
        "fake_chat_server.py",
    ],
    deps = DEPS,
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":fake_chat_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
    ],
    data = [
        ":image",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
    ],
)

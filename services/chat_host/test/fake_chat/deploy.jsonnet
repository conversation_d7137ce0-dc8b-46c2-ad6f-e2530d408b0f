// K8S deployment file for a fake chat server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';

function(
  env,
  namespace,
  namespace_config,
  cloud,
)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local appName = 'fake-chat';
  local services = grpcLib.grpcService(appName, namespace=namespace);
  local configMap =
    local config = {
      port: 50051,
      model_name: 'fake',
    } + if !mtls then {} else {
      client_mtls: {
        ca_path: '/client-certs/ca.crt',
        key_path: '/client-certs/tls.key',
        cert_path: '/client-certs/tls.crt',
      },
      server_mtls: {
        ca_path: '/certs/ca.crt',
        key_path: '/certs/tls.key',
        cert_path: '/certs/tls.crt',
      },
    };
    configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);
  // create a client TLS certificate to securely access the content manager
  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(
    name='%s-server-certificate' % appName,
    namespace=namespace,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName),
    volumeName='certs',
  );

  local container =
    {
      name: 'chat',
      target: {
        name: '//services/chat_host/test/fake_chat:image',
        dst: 'fake-chat',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv('chat-host', telemetryLib.collector),
      volumeMounts: lib.flatten([
        configMap.volumeMountDef,
        serverCert.volumeMountDef,
        clientCert.volumeMountDef,
      ]),
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 1,
          // This is conservative, looks like we usually use <1Gi
          memory: '2Gi',
        },
      },
    };
  local pod =
    {
      containers: [
        container,
      ],
      volumes: lib.flatten([
        configMap.podVolumeDef,
        serverCert.podVolumeDef,
        clientCert.podVolumeDef,
      ]),
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);
  local modelInstanceConfig = {
    name: 'fake',
    model_type: 'CHAT',
    chat: {
      suggested_prefix_char_count: 0,
      suggested_suffix_char_count: 0,
      model_priority: 0,
      chat_endpoint: 'fake-chat-svc:50051',
    },
  };
  local modelConfig = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: '%s-model-instance-config' % appName,
      namespace: namespace,
      labels: {
        // this makes api-proxy find them
        'app.kubernetes.io/type': 'model-instance-config',
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      'model_instance.json': std.manifestJson(modelInstanceConfig),
    },
  };
  lib.flatten([
    modelConfig,
    clientCert.objects,
    serverCert.objects,
    configMap.objects,
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else 2,
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod + {
            affinity: affinity,
            tolerations: tolerations,
            priorityClassName: cloudInfo.envToPriorityClass(env),
          },
        },
      },
    },
    services,
    pbd,
  ])

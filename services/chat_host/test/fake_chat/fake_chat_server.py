"""Server of a chat host."""

import argparse
import logging
import pathlib
import threading
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import Iterable, Iterator

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import Grace<PERSON>SignalHand<PERSON>
from services.chat_host import chat_pb2, chat_pb2_grpc
from services.lib.request_context.request_context import RequestContext

log = structlog.get_logger()

tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for a chat server."""

    port: int

    # name of the model.
    #
    # this has to match the name of the model instance config
    model_name: str

    feature_flags_sdk_key_path: str | None = None
    dynamic_feature_flags_endpoint: str | None = None

    client_mtls: tls_config.ClientConfig | None = None
    server_mtls: tls_config.ServerConfig | None = None

    shutdown_grace_period_s: float = 20.0


class FakeChatServices(chat_pb2_grpc.ChatServicer):
    """Services to implement the chat service API."""

    def Chat(
        self,
        request: chat_pb2.ChatRequest,
        context: grpc.ServicerContext,
    ) -> chat_pb2.ChatResponse:
        """
        Regular chat implementation has been deprecated in favor of a chat stream dependency.
        Concatenate chat stream events and send it back as one response.
        """
        log.info("chat content: %s", request)
        response = chat_pb2.ChatResponse(text=request.message)
        return response

    def ChatStream(
        self,
        request: chat_pb2.ChatRequest,
        context: grpc.ServicerContext,
    ) -> Iterator[chat_pb2.ChatResponse]:
        log.info("chat stream content: %s", request)
        responses = [chat_pb2.ChatResponse(text=request.message)]
        for response in responses:
            yield response

    def ChatRetrieval(
        self,
        request: chat_pb2.ChatRetrievalRequest,
        context: grpc.ServicerContext,
    ) -> chat_pb2.ChatRetrievalResponse:
        log.info("chat retrieval content: %s", request)
        response = chat_pb2.ChatRetrievalResponse()
        return response

    def FindMissing(
        self,
        request: chat_pb2.FindMissingRequest,
        context: grpc.ServicerContext,
    ) -> chat_pb2.FindMissingResponse:
        request_context = RequestContext.from_grpc_context(context)
        request_context.bind_context_logging()

        log.info(
            "find_missing content: model_name=%s, blob_count=%d",
            request.model_name,
            len(request.blob_names),
        )

        response = chat_pb2.FindMissingResponse()
        return response


def run(config: Config, shutdown_event: threading.Event):
    server = grpc.server(
        ThreadPoolExecutor(max_workers=16, thread_name_prefix="server-"),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
        ],
    )
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    chat_pb2_grpc.add_ChatServicer_to_server(FakeChatServices(), server)
    service_names = (
        chat_pb2.DESCRIPTOR.services_by_name["Chat"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)

    if server_credentials is not None:
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")

    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # type: ignore # pylint: disable=no-member
        config_file.read_text(),
    )


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    run(config, handler.get_shutdown_event())


if __name__ == "__main__":
    main()

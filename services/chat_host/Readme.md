# Chat Host

The Chat Host handles chat requests

### Local testing

To test the chat host locally, you can run the following commands:

```bash
kubectl port-forward deployment/chat-binks-1B-BF16-chatanol1-18-ug-chat 50051:50051 -n "dev-$USER"
```

```bash
bazel run services/chat_host/util -- --endpoint "127.0.0.1:50051" --insecure chat \
--model binks-1B-BF16-chatanol1-18-ug-chat --message "Explain my code" \
--selected-code "def quicksort(arr):\n.   pass"
```

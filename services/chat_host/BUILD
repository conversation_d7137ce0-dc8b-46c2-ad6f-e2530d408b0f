load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "chat_proto",
    srcs = ["chat.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//services/lib/proto:chat_proto",
    ],
)

py_grpc_library(
    name = "chat_proto_py_proto",
    protos = [":chat_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//services/lib/proto:chat_py_proto",
    ],
)

# chat.proto defines request/response types that correspond to types in
# base/prompt_format_chat; this library provides utility functions for converting
py_library(
    name = "chat_proto_util",
    srcs = ["chat_proto_util.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":chat_proto_py_proto",
        "//base/prompt_format_chat",
        "//base/third_party_clients:third_party_model_client",
    ],
)

pytest_test(
    name = "chat_proto_util_test",
    srcs = ["chat_proto_util_test.py"],
    deps = [
        ":chat_proto_py_proto",
        ":chat_proto_util",
        "//base/prompt_format_chat",
    ],
)

go_grpc_library(
    name = "chat_host_go_proto",
    importpath = "github.com/augmentcode/augment/services/chat_host/proto",
    proto = ":chat_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//services/lib/proto:chat_go_proto",
    ],
)

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":chat_proto_py_proto",
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
    ],
)

# a small CLI utility to issue requests against the chat host API
py_binary(
    name = "util",
    srcs = ["util.py"],
    deps = [
        ":client",
        "//base/logging:console_logging",
        "//services/lib/request_context:request_context_py",
    ],
)

ts_proto_library(
    name = "chat_host_ts_proto",
    node_modules = "//:node_modules",
    proto = ":chat_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_ts_proto",
        "//services/lib/proto:chat_ts_proto",
    ],
)

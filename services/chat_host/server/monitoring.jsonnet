local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

local vertexaiWarningThreshold = 3000;
local vertexaiErrorThreshold = 4000;

function(cloud)
  local vertexAiHighUsage(req_min_threshold, duration_minutes, severity) = {
    displayName: 'Vertex AI usage is above %s req/m for the last %s minutes' % [req_min_threshold, duration_minutes],
    conditionPrometheusQueryLanguage: {
      duration: '%ss' % (duration_minutes * 60),
      evaluationInterval: '60s',
      labels: { severity: severity },
      query: |||
        sum(increase(aiplatform_googleapis_com:prediction_online_prediction_count[1m])) by (endpoint_id) > %s
      ||| % [req_min_threshold],
    },
  };

  local promptCacheHitRateTooLow(threshold, duration_minutes, severity) = {
    displayName: 'Prompt cache hit rate is below %s%% for the last %s minutes' % [threshold, duration_minutes],
    conditionPrometheusQueryLanguage: {
      duration: '%ss' % (duration_minutes * 60),
      evaluationInterval: '60s',
      labels: { severity: severity },
      query: |||
        (
          sum by(model_caller) (increase(au_anthropic_prompt_cache_read_sum{model_caller=~"chat-host.*agent"}[1m])) /
          sum by(model_caller) (increase(au_anthropic_prompt_total_input_tokens_sum{model_caller=~"chat-host.*agent"}[1m]))
        ) < %s
        and sum by(model_caller) (rate(au_anthropic_prompt_total_input_tokens_count{model_caller=~"chat-host.*agent"}[1m])) > 3
        and sum by(model_caller) (rate(au_anthropic_output_tokens_sum{model_caller=~"chat-host.*agent"}[1m])) > 0
      ||| % [threshold / 100.0],
    },
  };

  local vertexAiHighUsageWarning = vertexAiHighUsage(vertexaiWarningThreshold, 2, 'warning');
  local vertexAiHighUsageError = vertexAiHighUsage(vertexaiErrorThreshold, 1, 'error');

  // Alert on elevated rate of request check failures
  // This means a bad actor is sending requests that are being rejected
  local requestCheckFailuresWarning = {
    displayName: 'Elevated rate of request check failures',
    conditionPrometheusQueryLanguage: {
      duration: '120s',  // 2 minutes
      evaluationInterval: '30s',
      labels: { severity: 'warning' },
      query: |||
        sum(increase(au_chat_request_check_failures_total[30s])) by (check_type, tenant_name) > 1
      |||,
    },
  };

  local requestCheckFailuresError = {
    displayName: 'High rate of request check failures',
    conditionPrometheusQueryLanguage: {
      duration: '120s',  // 2 minutes
      evaluationInterval: '30s',
      labels: { severity: 'error' },
      query: |||
        sum(increase(au_chat_request_check_failures_total[30s])) by (check_type, tenant_name) > 5
      |||,
    },
  };

  [
    // Warn if vertex ai usage for any model is above expected rates for the last minute or two
    monitoringLib.alertPolicy(cloud,
                              vertexAiHighUsageWarning,
                              'vertex-ai-usage-warning',
                              'Vertex AI usage is above %s req/m for the last 2 minutes for endpoints %s' % [vertexaiWarningThreshold, monitoringLib.label('endpoint_id')],
                              team='chat'),
    monitoringLib.alertPolicy(cloud,
                              vertexAiHighUsageError,
                              'vertex-ai-usage-error',
                              'Vertex AI usage is above %s req/m for the last minute for endpoints %s' % [vertexaiErrorThreshold, monitoringLib.label('endpoint_id')],
                              team='chat'),

    // Alert on elevated rate of request check failures (warning)
    monitoringLib.alertPolicy(cloud,
                              requestCheckFailuresWarning,
                              'request-check-failures-warning',
                              'Elevated rate of request check failures for check type %s in tenant %s' % [monitoringLib.label('check_type'), monitoringLib.label('tenant_name')],
                              team='chat'),

    // Alert on high rate of request check failures (error)
    monitoringLib.alertPolicy(cloud,
                              requestCheckFailuresError,
                              'request-check-failures-error',
                              'High rate of request check failures for check type %s in tenant %s' % [monitoringLib.label('check_type'), monitoringLib.label('tenant_name')],
                              team='chat'),
    // Alert on low prompt cache hit rate (warning)
    monitoringLib.alertPolicy(cloud,
                              promptCacheHitRateTooLow(82, 10, 'warning'),
                              'prompt-cache-hit-rate-too-low-warning',
                              'Prompt cache hit rate is below 82%% for the last 10 minutes for model caller %s' % [monitoringLib.label('model_caller')],
                              team='chat'),
    // Alert on low prompt cache hit rate (error)
    monitoringLib.alertPolicy(cloud,
                              promptCacheHitRateTooLow(78, 10, 'error'),
                              'prompt-cache-hit-rate-too-low-error',
                              'Prompt cache hit rate is below 78%% for the last 10 minutes for model caller %s' % [monitoringLib.label('model_caller')],
                              team='chat'),
  ]

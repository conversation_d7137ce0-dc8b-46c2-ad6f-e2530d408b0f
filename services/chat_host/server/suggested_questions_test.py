from unittest.mock import MagicMock
from services.chat_host.server.suggested_questions import SuggestedQuestionsGenerator
from base.prompt_format.common import (
    Exchange as PromptExchange,
    get_request_message_as_text,
)


def test_basic():
    client = MagicMock()
    suggested_questions_generator = SuggestedQuestionsGenerator(client)
    chat_history = [PromptExchange("a", "b"), PromptExchange("c", "d")]
    system_prompt = "d"
    prompt = """Write your 2 best guesses of what user will ask next. Keep them short. Enclose them in:
<guess_of_next_user_question>
    <next_user_question>...</next_user_question>
    <next_user_question>...</next_user_question>
</guess_of_next_user_question>"""

    _ = suggested_questions_generator(chat_history, system_prompt)
    client.generate_response_stream.assert_called_with(
        model_caller="suggested-questions",
        chat_history=[PromptExchange("a", "b", None), PromptExchange("c", "d", None)],
        system_prompt="d",
        cur_message=get_request_message_as_text(prompt),
        tools=[],
        use_caching=True,
        prefill="<guess_of_next_user_question>\n    <next_user_question>",
    )

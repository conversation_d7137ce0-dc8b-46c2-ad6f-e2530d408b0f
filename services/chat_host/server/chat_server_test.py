"""Unit tests for the GRPC chat server."""

from unittest.mock import MagicMock, create_autospec

import grpc
import pytest

from base.prompt_format_chat.prompt_formatter import ExceedContextLength
from services.chat_host import chat_pb2
from services.chat_host.server.chat_server import ChatServices, Config, AuthConfig
from services.chat_host.server.handler import (
    ChatH<PERSON>lerProtocol,
    ChatResult,
    ChatResultStatusCode,
)
from services.lib.retrieval import retriever_factory
from services.working_set.client.client import WorkingSetClient
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

mock_config: Config = Config(
    port=1234,
    model_name="my_model",
    inference_host_endpoints={"default": "str"},
    content_manager_endpoint="str",
    third_party_arbiter_endpoint="str",
    working_set_endpoint=None,
    client_mtls=None,
    central_client_mtls=None,
    server_mtls=None,
    retrieval=retriever_factory.RetrievalConfig(),
    postprocess=None,
    handler_type="ChatHandler",
    handler_config={},
    max_handler_workers=4,
    max_server_workers=32,
    auth_config=AuthConfig(
        token_exchange_endpoint="str",
    ),
)


def test_chat_server():
    """Tests the chat server."""
    mock_handler = MagicMock(spec=ChatHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = chat_pb2.ChatRequest(model_name="my_model")
    service = ChatServices(
        config=mock_config,
        handlers={"my_model": mock_handler},
        ri_publisher=MagicMock(RequestInsightPublisher),
        working_set_client=mock_working_set_client,
    )

    mock_response = ChatResult(
        text="Mocked Response", unknown_blob_names=[], checkpoint_not_found=False
    )
    mock_handler.chat_stream.return_value = [mock_response]

    response = service.Chat(mock_request, mock_context)
    assert response.text == "Mocked Response"


def test_chat_server_stream():
    """Tests the chat server."""
    mock_handler = MagicMock(spec=ChatHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = chat_pb2.ChatRequest(model_name="my_model")
    service = ChatServices(
        config=mock_config,
        handlers={"my_model": mock_handler},
        ri_publisher=MagicMock(RequestInsightPublisher),
        working_set_client=mock_working_set_client,
    )

    unknown_blob_names = [f"blob_{i}" for i in range(1000)]

    def response_generator():
        for i in range(10):
            yield ChatResult(
                text=f"Mocked Response ${i}",
                unknown_blob_names=unknown_blob_names,
                checkpoint_not_found=False,
            )

    mock_handler.chat_stream.return_value = response_generator()

    chat_stream_response = service.ChatStream(mock_request, mock_context)
    assert chat_stream_response is not None
    count = 0
    for response in chat_stream_response:
        assert response.text == f"Mocked Response ${count}"
        assert len(response.unknown_blob_names) == 1000
        assert (
            response.status_code == ChatResultStatusCode.OK.value
        ), "Expected OK status code"
        count += 1
    assert count == 10, "Expected 10 responses, got {}".format(count)


def test_chat_server_mid_stream_cancellation():
    """A gRPC error mid-stream results in a context abort."""
    mock_handler = MagicMock(spec=ChatHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = chat_pb2.ChatRequest(model_name="my_model")
    service = ChatServices(
        config=mock_config,
        handlers={"my_model": mock_handler},
        ri_publisher=MagicMock(RequestInsightPublisher),
        working_set_client=mock_working_set_client,
    )

    def response_generator():
        for i in range(5):
            yield ChatResult(text=f"Mocked Response ${i}", unknown_blob_names=[])
        e = grpc.RpcError("Mid-Stream cancellation")
        # See https://stackoverflow.com/questions/61726226
        e.code = lambda: grpc.StatusCode.CANCELLED  # pylint: disable=no-member # type: ignore
        e.details = lambda: "Mid-Stream cancellation"  # pylint: disable=no-member # type: ignore
        raise e

    mock_handler.chat_stream.return_value = response_generator()

    chat_stream_response = service.ChatStream(mock_request, mock_context)
    assert chat_stream_response is not None

    count = 0
    for response in chat_stream_response:
        assert response.text == f"Mocked Response ${count}"
        count += 1
    assert count == 5, "Expected 5 succesful responses before error, got {}".format(
        count
    )
    mock_context.abort.assert_called_once()


def test_chat_server_mid_stream_error():
    """A non-gRPC error mid-stream is raised."""
    mock_handler = MagicMock(spec=ChatHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = chat_pb2.ChatRequest(model_name="my_model")
    service = ChatServices(
        config=mock_config,
        handlers={"my_model": mock_handler},
        ri_publisher=MagicMock(RequestInsightPublisher),
        working_set_client=mock_working_set_client,
    )

    def response_generator():
        for i in range(5):
            yield ChatResult(text=f"Mocked Response ${i}", unknown_blob_names=[])
        raise Exception("Unknown exception")

    mock_handler.chat_stream.return_value = response_generator()

    chat_stream_response = service.ChatStream(mock_request, mock_context)
    assert chat_stream_response is not None

    count = 0
    with pytest.raises(Exception):
        for response in chat_stream_response:
            assert response.text == f"Mocked Response ${count}"
            count += 1
    assert count == 5, "Expected 5 succesful responses before error, got {}".format(
        count
    )


def test_chat_exceed_context_length_error():
    """Respond with correct status_code when exceed context length."""
    mock_handler = MagicMock(spec=ChatHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = chat_pb2.ChatRequest(model_name="my_model")
    service = ChatServices(
        config=mock_config,
        handlers={"my_model": mock_handler},
        ri_publisher=MagicMock(RequestInsightPublisher),
        working_set_client=mock_working_set_client,
    )

    mock_handler.chat_stream.side_effect = ExceedContextLength("Exceed context length")

    chat_stream_response = service.ChatStream(mock_request, mock_context)
    assert chat_stream_response is not None
    assert (
        next(chat_stream_response).status_code
        == ChatResultStatusCode.EXCEED_CONTEXT_LENGTH.value
    ), "Expected error code for exceed context length"


def test_chat_retrieval():
    """Tests the ChatRetrieval endpoint."""
    mock_handler = MagicMock(spec=ChatHandlerProtocol)
    mock_working_set_client = MagicMock(spec=WorkingSetClient)
    mock_context = create_autospec(grpc.ServicerContext, instance=True)
    mock_context.invocation_metadata.return_value = [
        MagicMock(key="x-request-id", value="fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        MagicMock(
            key="x-request-session-id", value="3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
        ),
        MagicMock(key="authorization", value="Bearer token"),
        MagicMock(key="x-tenant-id", value="1234567890"),
        MagicMock(key="x-tenant-name", value="test-tenant"),
        MagicMock(key="x-shard-namespace", value="test-shard"),
        MagicMock(key="x-cloud", value="test-cloud"),
    ]
    mock_request = chat_pb2.ChatRetrievalRequest(model_name="my_model", query="query")
    service = ChatServices(
        config=mock_config,
        handlers={"my_model": mock_handler},
        ri_publisher=MagicMock(RequestInsightPublisher),
        working_set_client=mock_working_set_client,
    )

    mock_retrieval_result = MagicMock()
    mock_retrieval_result.get_retrieved_chunks.return_value = [
        MagicMock(
            text="Retrieved chunk 1",
            path="foo.py",
            char_start=1,
            char_end=2,
            blob_name="1234",
        ),
        MagicMock(
            text="Retrieved chunk 2",
            path="foo.py",
            char_start=10,
            char_end=20,
            blob_name="1234",
        ),
    ]
    mock_retrieval_result.get_missing_blob_names.return_value = ["missing_blob_1"]
    mock_retrieval_result.get_checkpoint_not_found.return_value = False

    mock_handler.retrieve.return_value = mock_retrieval_result

    response = service.ChatRetrieval(mock_request, mock_context)

    assert response is not None
    assert len(response.retrieved_chunks) == 2
    assert response.retrieved_chunks[0].text == "Retrieved chunk 1"
    assert response.retrieved_chunks[1].text == "Retrieved chunk 2"
    assert response.unknown_blob_names == ["missing_blob_1"]
    assert response.checkpoint_not_found is False

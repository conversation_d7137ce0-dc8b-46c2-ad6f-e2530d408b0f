import dataclasses
from datetime import datetime

from google.protobuf import json_format

import base.feature_flags
from base.prompt_format.common import get_request_message_as_text
from base.prompt_format_chat import get_token_counter_by_prompt_formatter_name
from base.prompt_format_chat.lib.retrieval_section_prompt_formatter_v2 import (
    get_binks_retrieval_section_bulder,
)
from base.prompt_format_chat.prompt_formatter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
    ChatPromptInput,
    SlackbotMessageTokenApportionment,
)
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredChatPromptOutput,
)
from services.integrations.glean import glean_pb2
from services.integrations.slack_bot.processor import processor_pb2

SLACKBOT_SYSTEM_PROMPT = """\
You are Augment, an AI code assistant developed by Augment Code that lives in a company's Slack workspace and has access to their GitHub repos.
Your role is to help the company by following their instructions and answering their Slack questions and conversations related to their code, their product, and general software engineering.

When answering the developers' questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information. Remember, you're in a Slack workspace which usually has brief and succint messages.
- If different sources suggest different possible answers, please provide all of them.
- For each possible answer, always provide the supporting evidence it is based on. For example, if the answer is based on a code snippet, state what code it is based on and provide the path to the file.
- When referencing existing code in your response, always include the FULL file path.
- Always write code in the programming language of the files the user is asking about. For example, if the user asks about the file foo/bar.rs, use Rust unless explicitly asked to use a different language.
- When referencing classes, functions, variables, files, or directories in your response, always wrap them in backticks (``).
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.
- Note that users may refer to each other by name or Slack-formatted user ID tags, the latter of which is provided for every message. When you respond, prefer to use Slack-formatted User ID tags if you need to refer to a user, e.g. <@U12345>.
- When responding, remember that Slack uses its own Slack "mrkdwn" format and it differs significantly from normal markdown. So, when responding, use the following mrkdwn formatting:
  - *bold* text NOT **bold** text
  - _italics_ text NOT *italics* text
  - ~strikethrough~ text NOT ~~strikethrough~~ text
  - > quoted text
  - <https://example.com|link text> NOT [link text](https://example.com)|
- Please never indent code blocks, even when they appear within lists. Always align the opening and closing backticks with the left margin of the text.


{additional_info}
"""

NO_REPO_INFO = """\
Your User ID is {bot_id}. You are in the channel {channel_name}.
"""

REPO_INFO = """\
You will receive code excerpts retrieved from the user's GitHub repos using an embeddings-based search that matches the conversation against an index of all repo files.
You have access to {current_repos_count}/{all_repos_count} repositories installed in the Github app for this organization. If the user wants to change which repos you can see out of those installed, ask them to use the `/augment repo-select` slack command.

{repo_list_prefix}: {repo_list}

You will then receive a conversation in which you have been mentioned. Please reply with your response.
Your User ID is {bot_id}. You are in the channel {channel_name}.
"""
NUMBER_OF_REPOS_TO_SHOW = 10
REPO_LIST_PREFIX_FIRST_N = f"Here are the names of the first {NUMBER_OF_REPOS_TO_SHOW} repos you have access to"
REPO_LIST_PREFIX_ALL = "Here are the names of all the repos you have access to"

PREVIOUS_CONVERSATION_HEADER = "Here is the previous slack conversation:"
USER_QUESTION_HEADER = "Here is the user question for you:"
GLEAN_HEADER = """\

Here are also some relevant documents and conversations from the company's knowledge base through Glean.
Please use this information to help answer the user's question.
When referring to specific documents or pages to support an answer, please ALWAYS hyperlink the document in your response.
In particular, state the source, the name of the page, and turn that name into a hyperlink to the page.
Similarly, when referring to specific conversations to support an answer, please ALWAYS link the conversation in your response.
In particular, state the source, a brief description of the conversation, and turn that description into a hyperlink to the page.
If the same answer is supported by multiple types of data sources, please use the best source from each.\n
"""


def escape_slack_link(text: str) -> str:
    """
    Escapes special characters in text that could be interpreted as Slack link syntax.

    Claude sometimes write slack hyperlinks that break syntax.

    For example, this will result in broken link to be rendered:

        Example output <https://www.google.com/search?q=first|second|some google search>
        and this is another <https://example.com|you<->me>

    The correct version that can be rendered in slack is

        Example output <https://www.google.com/search?q=first&vert;second|some google search>
        and this is another <https://example.com|you&lt;-&gt;me>

    The slack escape syntax is also different from normal html syntax.

    Args:
        text: The text that may contain Slack link syntax characters

    Returns:
        Text with Slack link syntax characters escaped

    Examples:
        >>> escape_slack_link("Visit <https://example.com|Example>")
        "Visit &lt;https://example.com|Example&gt;"
        >>> escape_slack_link("Here's a <link>")
        "Here's a &lt;link&gt;"
    """
    return text.replace("<", "&lt;").replace(">", "&gt;").replace("|", "&vert;")


GLEAN_DOCUMENT_TEMPLATE = """\
Here is a document <{url}|{title}> from {data_source} with content:
{content}
"""

GLEAN_LEFTOVER_HEADER = """\
Here are brief snippets from some additional lower ranked documents and conversations from the company's knowledge base through Glean.
"""


_default_token_apportionent = SlackbotMessageTokenApportionment(
    path_len=256,
    chat_history_len=1024 * 6,
    retrieval_len=1024 * 6,
    max_prompt_len=24 * 1024,
    max_glean_len=1024 * 6,
    max_glean_leftover_len=1024 * 2,  # 2k tokens
    max_glean_doc_len=1024 * 2,  # 2k tokens
    max_glean_snippet_char_len=512,  # 512 chars
    # Unused or deprecated fields
    prefix_len=0,
    suffix_len=0,
    message_len=0,
    selected_code_len=0,
    retrieval_as_tool=False,
)


class SlackbotPromptFormatterV2(ChatPromptFormatter[StructuredChatPromptOutput]):
    """Prompt formatter for slackbot"""

    def __init__(
        self,
        prompt_formatter_name: str,
        slack_token_apportionment: SlackbotMessageTokenApportionment | None = None,
        system_prompt: str = SLACKBOT_SYSTEM_PROMPT,
    ) -> None:
        token_counter = get_token_counter_by_prompt_formatter_name(
            prompt_formatter_name  # type: ignore
        )
        self.token_counter = token_counter
        self.system_prompt = system_prompt

        if slack_token_apportionment is None:
            token_apportionment = _default_token_apportionent
        else:
            token_apportionment = slack_token_apportionment
        self.token_apportionment = dataclasses.replace(
            token_apportionment, retrieval_as_tool=False
        )
        self.retrieval_section_builder = get_binks_retrieval_section_bulder(
            token_counter=token_counter,
            token_apportionment=token_apportionment,
        )

    def format_prompt(
        self, prompt_input: ChatPromptInput
    ) -> StructuredChatPromptOutput:
        """Format prompt for slackbot.

        * include full system prompt
        * include full message
        * include conversation history up to token_apportionment.chat_history_len
        * dedicate remaining budget to retrieval

        Args:
            prompt_input: an instance of PromptInput class, containing all raw input.

        Returns:
            A prompt of length <= self.token_apportionment.max_prompt_len, in tokens.
        """
        # for slackbot chat requests, the only exchange is the bot ID and channel name
        # as the request and the entire conversation history as the response
        exchange = next(iter(prompt_input.chat_history))
        chat_metadata = json_format.Parse(
            get_request_message_as_text(exchange.request_message),
            processor_pb2.SlackbotChatMetadata(),
        )
        bot_id = chat_metadata.bot_id
        channel_name = chat_metadata.channel_name
        previous_thread_messages = chat_metadata.previous_thread_messages

        if chat_metadata.repos:
            repo_list = self._create_repo_list(chat_metadata.repos)
            repo_list_prefix = (
                REPO_LIST_PREFIX_FIRST_N
                if len(chat_metadata.repos) > NUMBER_OF_REPOS_TO_SHOW
                else REPO_LIST_PREFIX_ALL
            )
            additional_info = REPO_INFO.format(
                current_repos_count=len(chat_metadata.repos),
                bot_id=bot_id,
                channel_name=channel_name,  # RFC 3339 to match `ToJsonString()`
                all_repos_count=len(chat_metadata.all_repos),
                repo_list=repo_list,
                repo_list_prefix=repo_list_prefix,
            )
        else:
            additional_info = NO_REPO_INFO.format(
                bot_id=bot_id,
                channel_name=channel_name,
            )

        if chat_metadata.glean_documents:
            assert isinstance(
                self.token_apportionment, SlackbotMessageTokenApportionment
            )
            additional_info += self._format_glean_section(
                glean_documents=list(chat_metadata.glean_documents),
                max_main_tokens=self.token_apportionment.max_glean_len,
                max_leftover_tokens=self.token_apportionment.max_glean_leftover_len,
                max_doc_tokens=self.token_apportionment.max_glean_doc_len,
                max_snippet_chars=self.token_apportionment.max_glean_snippet_char_len,
            )

        system_prompt = self.system_prompt.format(
            additional_info=additional_info,
            # RFC 3339 to match `ToJsonString()`
        )

        current_message = f"{USER_QUESTION_HEADER}\n\n{prompt_input.message}"

        token_budget = (
            self.token_apportionment.max_prompt_len
            - self.token_counter.count_tokens(system_prompt)
            - self.token_counter.count_tokens(current_message)
        )

        chat_history_budget = (
            min(
                self.token_apportionment.chat_history_len,
                token_budget,
            )
            if self.token_apportionment.chat_history_len != -1
            else token_budget
        )

        previous_messages = self.format_conversation_history(
            list(previous_thread_messages),
            chat_history_budget,
        )

        token_budget -= self.token_counter.count_tokens(previous_messages)
        full_message = f"{previous_messages}{current_message}"

        retrieval_budget = (
            min(
                self.token_apportionment.retrieval_len,
                token_budget,
            )
            if self.token_apportionment.retrieval_len != -1
            else token_budget
        )
        retrieval_exchanges, retrieved_chunks_in_prompt = (
            self.retrieval_section_builder.get_retrieval_section_as_exchanges(
                prompt_input,
                "",
                "",
                retrieval_budget,
            )
        )
        return StructuredChatPromptOutput(
            system_prompt=system_prompt,
            chat_history=retrieval_exchanges,
            message=full_message,
            retrieved_chunks_in_prompt=retrieved_chunks_in_prompt,
            retrieval_as_tool=self.token_apportionment.retrieval_as_tool,
        )

    def format_conversation_history(
        self,
        previous_thread_messages: list[str],
        token_budget: int,
    ) -> str:
        """
        Truncates the previous slack messages to fit within the conversation history token budget.
        Always tries to put the first message in the slack thread and fills the remaining budget with the most recent messages.
        """

        if not previous_thread_messages:
            return ""

        formatted_history = ""
        remaining_budget = token_budget

        # Add header
        formatted_history = f"{PREVIOUS_CONVERSATION_HEADER}\n\n"
        header_tokens = self.token_counter.count_tokens(formatted_history)
        if header_tokens >= remaining_budget:
            return ""
        remaining_budget -= header_tokens

        newline_tokens = self.token_counter.count_tokens("\n\n")

        # Add first message
        first_message = self.token_counter.truncate_to_budget(
            previous_thread_messages[0],
            remaining_budget - newline_tokens,
            truncation_indicator="...[message truncated]...",
        )

        formatted_history += first_message + "\n\n"
        remaining_budget -= self.token_counter.count_tokens(first_message)

        # Fill with remaining messages - truncating in reverse order to keep the most recent messages
        remaining_messages = previous_thread_messages[1:]
        if len(remaining_messages) > 0 and remaining_budget > newline_tokens:
            remaining_messages_string = "\n\n".join(remaining_messages)

            formatted_history += self.token_counter.truncate_to_budget(
                remaining_messages_string,
                remaining_budget - newline_tokens,
                truncation_indicator="...[message truncated]...",
                reversed=True,
            )

            formatted_history += "\n\n"

        return formatted_history

    @classmethod
    def traverse_doc_list(cls, doc: glean_pb2.Document, merge_adjacent: bool = True):
        """Traverse the document tree and return a list of all documents.

        If merge_adjacent is True, merge adjacent documents with the same author
        """
        docs = [doc]
        for child in doc.children:
            docs.extend(cls.traverse_doc_list(child, merge_adjacent))
        if docs and merge_adjacent:
            merged = []
            current = docs[0]
            for doc in docs[1:]:
                if (doc.author.id or doc.author.name) == (
                    current.author.id or current.author.name
                ):
                    # When merging documents, try to use doc.content first.
                    # If content is empty, fall back to using snippets as a backup source of content
                    if doc.content:
                        current.content += "\n" + doc.content
                    elif doc.snippets:
                        current.content += "\n" + "\n".join(
                            [snippet.text for snippet in doc.snippets]
                        )
                    # Use datetime comparison for timestamps
                    if doc.updated_at.ToDatetime() > current.updated_at.ToDatetime():
                        current.updated_at.CopyFrom(doc.updated_at)
                    if doc.created_at.ToDatetime() > current.created_at.ToDatetime():
                        current.created_at.CopyFrom(doc.created_at)
                else:
                    merged.append(current)
                    current = doc
            merged.append(current)
            return merged
        return docs

    @classmethod
    def format_glean_content(cls, doc: glean_pb2.Document):
        """Format a Glean document content."""
        # first, sort the tree pre-order
        datetime_cutoff = datetime(1980, 1, 1, 0, 0, 0)
        docs = cls.traverse_doc_list(doc, merge_adjacent=True)
        previous_date = ""
        output = []
        for doc in docs:
            headers = False
            if output:
                output.append("\n")
            if doc.author and doc.author.name:
                output.append(f"Author: {doc.author.name}")
                headers = True
            if (
                doc.updated_at
                and doc.updated_at.ToDatetime() > datetime_cutoff
                and doc.updated_at != doc.created_at
            ):
                # Format the timestamp to be YYYY-MM-DD HH:MM
                updated_at = doc.updated_at.ToDatetime()
                date_part = updated_at.strftime("%Y-%m-%d")
                if date_part != previous_date:
                    output.append(
                        f"{updated_at.strftime(' | %Y-%m-%d %H:%M')} (Edited)"
                    )
                    previous_date = date_part
                else:
                    output.append(f"{updated_at.strftime(' | %H:%M')} (Edited)")
                headers = True
            elif doc.created_at and doc.created_at.ToDatetime() > datetime_cutoff:
                # Format the timestamp to be YYYY-MM-DD HH:MM
                created_at = doc.created_at.ToDatetime()
                date_part = created_at.strftime("%Y-%m-%d")
                if date_part != previous_date:
                    output.append(f"{created_at.strftime(' | %Y-%m-%d %H:%M')}")
                    previous_date = date_part
                else:
                    output.append(f"{created_at.strftime(' | %H:%M')}")
                headers = True
            if headers:
                output.append("\n---\n")
            output.append(doc.content)
            output.append("\n")
        return "".join(output)

    def _create_repo_list(self, repos):
        """Create a string containing a list of repo names for all repos relevant to the current chat request."""

        # If we need to abridge the list, show the most recently updated repos.
        if len(repos) > NUMBER_OF_REPOS_TO_SHOW:
            repos = sorted(
                repos,
                key=lambda repo: repo.current_commit_time.ToJsonString(),
                reverse=True,
            )[:NUMBER_OF_REPOS_TO_SHOW]

        repo_list = ", ".join(
            [f"`{repo.repo_owner}/{repo.repo_name}`" for repo in repos]
        )
        return repo_list

    def _format_glean_section(
        self,
        glean_documents: list[glean_pb2.Document],
        max_main_tokens: int,
        max_leftover_tokens: int,
        max_doc_tokens: int,
        max_snippet_chars: int,
    ) -> str:
        """Format the Glean section of the prompt."""
        if len(glean_documents) == 0:
            return ""

        glean_section = GLEAN_HEADER

        remaining_tokens = max_main_tokens - self.token_counter.count_tokens(
            GLEAN_HEADER
        )

        if remaining_tokens <= 0:
            return ""

        included_doc_indices = []

        for idx, doc in enumerate(glean_documents):
            formatted_doc = GLEAN_DOCUMENT_TEMPLATE.format(
                title=escape_slack_link(doc.title),
                data_source=glean_pb2.DataSource.Name(doc.data_source),
                url=escape_slack_link(doc.url),
                content=self.format_glean_content(doc)[:max_doc_tokens],
            )
            if self.token_counter.count_tokens(formatted_doc) > remaining_tokens:
                # continue instead of break since there may be smaller docs to add
                continue
            glean_section += formatted_doc
            glean_section += "\n"
            remaining_tokens -= self.token_counter.count_tokens(formatted_doc)
            included_doc_indices.append(idx)

        if len(included_doc_indices) < len(glean_documents):
            glean_section += GLEAN_LEFTOVER_HEADER
            remaining_tokens = max_leftover_tokens - self.token_counter.count_tokens(
                GLEAN_LEFTOVER_HEADER
            )
            leftover_docs = [
                doc
                for idx, doc in enumerate(glean_documents)
                if idx not in included_doc_indices
            ]
            for doc in leftover_docs:
                formatted_doc = GLEAN_DOCUMENT_TEMPLATE.format(
                    title=escape_slack_link(doc.title),
                    data_source=glean_pb2.DataSource.Name(doc.data_source),
                    url=escape_slack_link(doc.url),
                    content=self.format_glean_content(doc)[:max_snippet_chars],
                )
                if self.token_counter.count_tokens(formatted_doc) > remaining_tokens:
                    continue
                glean_section += formatted_doc
                glean_section += "\n"
                remaining_tokens -= self.token_counter.count_tokens(formatted_doc)

        return glean_section

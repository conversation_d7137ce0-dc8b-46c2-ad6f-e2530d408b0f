load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "slackbot_prompt_formatter",
    srcs = [
        "slackbot_prompt_formatter.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format_chat",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:structured_binks_prompt_formatter",
        "//base/prompt_format_chat/lib:retrieval_section_prompt_formatter_v2",
        "//services/integrations/slack_bot/processor:processor_py_proto",
        requirement("protobuf"),
    ],
)

py_library(
    name = "slackbot_prompt_formatter_v2",
    srcs = [
        "slackbot_prompt_formatter_v2.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format_chat",
        "//base/prompt_format_chat:prompt_formatter",
        "//base/prompt_format_chat:structured_binks_prompt_formatter",
        "//base/prompt_format_chat/lib:retrieval_section_prompt_formatter_v2",
        "//services/integrations/slack_bot/processor:processor_py_proto",
        requirement("protobuf"),
    ],
)

pytest_test(
    name = "slackbot_prompt_formatter_test",
    srcs = ["slackbot_prompt_formatter_test.py"],
    deps = [
        ":slackbot_prompt_formatter",
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

pytest_test(
    name = "slackbot_prompt_formatter_v2_test",
    srcs = ["slackbot_prompt_formatter_v2_test.py"],
    deps = [
        ":slackbot_prompt_formatter_v2",
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format_chat:prompt_formatter",
    ],
)

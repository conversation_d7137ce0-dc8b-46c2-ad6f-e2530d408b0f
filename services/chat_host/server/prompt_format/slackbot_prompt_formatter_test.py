import json
from datetime import datetime, timedelta, timezone
from typing import Generator

import pytest

from base.feature_flags import BoolFlag, LocalFeatureFlagSetter, feature_flag_fixture
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
    ChatTokenApportionment,
    Exchange,
    PromptChunk,
)
from services.chat_host.server.prompt_format.slackbot_prompt_formatter import (
    GLEAN_DOCUMENT_TEMPLATE,
    GLEAN_HEADER,
    NO_REPO_INFO,
    PREVIOUS_CONVERSATION_HEADER,
    REPO_INFO,
    REPO_LIST_PREFIX_ALL,
    REPO_LIST_PREFIX_FIRST_N,
    SLACKBOT_SYSTEM_PROMPT,
    USER_QUESTION_HEADER,
    SlackbotPromptFormatter,
)
from services.integrations.glean import glean_pb2


@pytest.fixture
def feature_flags() -> Generator[LocalFeatureFlagSetter, None, None]:
    yield from feature_flag_fixture()


def test_slackbot_prompt_formatter(feature_flags):
    """general prompt formatting test case"""
    prompter = SlackbotPromptFormatter("structured_binks_llama3_instruct")

    prompt_input = ChatPromptInput(
        message="U12345: what tokenizers are there?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message='{\
                    "botId": "U54321",\
                    "channelName": "test-channel",\
                    "previousThreadMessages": ["U11111: hey bot!"],\
                    "repos": [{\
                        "repoName": "test-repo",\
                        "repoOwner": "test-owner",\
                        "indexedCommitTime": "2024-01-01T00:00:00.000000Z",\
                        "indexedCommitSha": "test-commit-sha",\
                        "currentCommitTime": "2024-01-01T00:00:00.000000Z",\
                        "currentCommitSha": "test-commit-sha"\
                    }],\
                    "allRepos": [{\
                        "repoName": "test-repo",\
                        "repoOwner": "test-owner"\
                    }]\
                }',
                response_text="",
            )
        ],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ],
    )

    prompt_output = prompter.format_prompt(prompt_input)

    assert prompt_output.system_prompt == SLACKBOT_SYSTEM_PROMPT.format(
        additional_info=REPO_INFO.format(
            current_repos_count=1,
            bot_id="U54321",
            channel_name="test-channel",  # RFC 3339 to match `ToJsonString()`
            all_repos_count=1,
            repo_list="`test-owner/test-repo`",
            repo_list_prefix=REPO_LIST_PREFIX_ALL,
        )
    )

    assert prompt_output.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        )
    ]
    assert (
        prompt_output.message
        == f"""\
{PREVIOUS_CONVERSATION_HEADER}

U11111: hey bot!

{USER_QUESTION_HEADER}

U12345: what tokenizers are there?"""
    )


def create_test_selected_repos(num_selected_repos: int) -> str:
    """Generate test repo data with different commit times.

    Args:
        num_selected_repos: Number of repos to generate
    """
    all_selected_repos = []
    base_time = datetime(2024, 1, 1, tzinfo=timezone.utc)

    for i in range(1, num_selected_repos + 1):
        # Each repo's indexed time is 2 hours later than the previous
        indexed_time = base_time + timedelta(hours=2 * i)
        # Current time is always 30 minutes ahead of indexed time
        current_time = indexed_time + timedelta(minutes=30)

        repo = {
            "repoName": f"test-repo-{i}",
            "repoOwner": f"test-owner-{i}",
            "indexedCommitTime": indexed_time.strftime("%Y-%m-%dT%H:%M:%S.000000Z"),
            "indexedCommitSha": f"test-commit-sha-{i}",
            "currentCommitTime": current_time.strftime("%Y-%m-%dT%H:%M:%S.000000Z"),
            "currentCommitSha": f"test-commit-sha-{i}c",
        }
        all_selected_repos.append(repo)

    return '"repos": ' + json.dumps(all_selected_repos)


def create_test_all_repos(num_repos: int) -> str:
    """Generate test all repos data.

    Args:
        num_repos: Number of repos to generate
    """
    all_repos = []
    for i in range(1, num_repos + 1):
        repo = {"repoName": f"test-repo-{i}", "repoOwner": f"test-owner-{i}"}
        all_repos.append(repo)

    return '"allRepos": ' + json.dumps(all_repos)


def test_slackbot_prompt_formatter_more_than_10_repos(feature_flags):
    """general prompt formatting test case"""
    prompter = SlackbotPromptFormatter("structured_binks_llama3_instruct")

    # Create 12 test repos with different commit times
    selected_repos = create_test_selected_repos(15)
    all_repos = create_test_all_repos(20)

    request_message = f"""{{\
                    "botId": "U54321",\
                    "channelName": "test-channel",\
                    "previousThreadMessages": [],\
                    {selected_repos},\
                    {all_repos}\
                }}"""

    prompt_input = ChatPromptInput(
        message="U12345: what tokenizers are there?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=request_message,
                response_text="",
            )
        ],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ],
    )

    prompt_output = prompter.format_prompt(prompt_input)

    # The 10 most recent repos should be outputted which should be repos 15 through 6 in reverse order
    oututted_repos = ", ".join(
        [f"`test-owner-{i}/test-repo-{i}`" for i in range(15, 5, -1)]
    )

    assert prompt_output.system_prompt == SLACKBOT_SYSTEM_PROMPT.format(
        additional_info=REPO_INFO.format(
            current_repos_count=15,
            bot_id="U54321",
            channel_name="test-channel",  # RFC 3339 to match `ToJsonString()`
            all_repos_count=20,
            repo_list=oututted_repos,
            repo_list_prefix=REPO_LIST_PREFIX_FIRST_N,
        )
    )

    assert prompt_output.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        )
    ]
    assert (
        prompt_output.message
        == f"""\
{USER_QUESTION_HEADER}

U12345: what tokenizers are there?"""
    )


def test_slackbot_prompt_formatter_no_repos(feature_flags):
    """test case for when there are no repos passed in the request"""
    prompter = SlackbotPromptFormatter("structured_binks_llama3_instruct")

    # don't pass anything for all_repos or selected_repos
    request_message = """{
                    "botId": "U54321",
                    "channelName": "test-channel",
                    "previousThreadMessages": ["U11111: hey bot!"],
                    "repos": [],
                    "allRepos": []
                }"""

    prompt_input = ChatPromptInput(
        message="U12345: what tokenizers are there?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=request_message,
                response_text="",
            )
        ],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ],
    )

    prompt_output = prompter.format_prompt(prompt_input)

    assert prompt_output.system_prompt == SLACKBOT_SYSTEM_PROMPT.format(
        additional_info=NO_REPO_INFO.format(
            bot_id="U54321",
            channel_name="test-channel",  # RFC 3339 to match `ToJsonString()`
        )
    )

    assert prompt_output.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        )
    ]
    assert (
        prompt_output.message
        == f"""\
{PREVIOUS_CONVERSATION_HEADER}

U11111: hey bot!

{USER_QUESTION_HEADER}

U12345: what tokenizers are there?"""
    )


def test_slackbot_prompt_formatter_with_glean(feature_flags):
    """test case for when there are glean documents passed in the request"""
    prompter = SlackbotPromptFormatter("structured_binks_llama3_instruct")

    # don't pass anything for all_repos or selected_repos
    request_message = """{
                    "botId": "U54321",
                    "channelName": "test-channel",
                    "previousThreadMessages": ["U11111: hey bot!"],
                    "repos": [ {
                        "repoName": "test-repo",
                        "repoOwner": "test-owner",
                        "indexedCommitTime": "2024-01-01T00:00:00.000000Z",
                        "indexedCommitSha": "test-commit-sha",
                        "currentCommitTime": "2024-01-01T00:00:00.000000Z",
                        "currentCommitSha": "test-commit-sha"
                    }],
                    "allRepos": [ {
                        "repoName": "test-repo",
                        "repoOwner": "test-owner"
                    }],
                    "gleanDocuments": [
                        {
                            "title": "test-slack",
                            "dataSource": "SLACK",
                            "url": "https://example.slack.com",
                            "content": "test-content"
                        },
                        {
                            "title": "test-notion",
                            "dataSource": "NOTION",
                            "url": "https://example.notion.com",
                            "content": "test-content"
                        }
                    ]
                }"""

    prompt_input = ChatPromptInput(
        message="U12345: what tokenizers are there?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=request_message,
                response_text="",
            )
        ],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[
            PromptChunk(
                text="# You can aggregate\n# with a maxing\n# function.\n",
                path="src/bar.py",
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
                origin="dense_retriever",
            ),
            PromptChunk(
                text="# You can aggregate\n# with a pooling function.\n",
                path="src/foo.py",
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
                origin="dense_retriever",
            ),
        ],
    )

    prompt_output = prompter.format_prompt(prompt_input)

    glean_section = (
        GLEAN_HEADER
        + GLEAN_DOCUMENT_TEMPLATE.format(
            title="test-slack",
            data_source="SLACK",
            url="https://example.slack.com",
            content="test-content",
        )
        + "\n\n"
        + GLEAN_DOCUMENT_TEMPLATE.format(
            title="test-notion",
            data_source="NOTION",
            url="https://example.notion.com",
            content="test-content",
        )
        + "\n\n"
    )

    assert prompt_output.system_prompt == SLACKBOT_SYSTEM_PROMPT.format(
        additional_info=REPO_INFO.format(
            current_repos_count=1,
            bot_id="U54321",
            channel_name="test-channel",  # RFC 3339 to match `ToJsonString()`
            all_repos_count=1,
            repo_list="`test-owner/test-repo`",
            repo_list_prefix=REPO_LIST_PREFIX_ALL,
        )
        + glean_section
    )

    assert prompt_output.chat_history == [
        Exchange(
            request_message="""Below are some relevant files from my project.

Here is an excerpt from the file `src/bar.py`:

```
# You can aggregate
# with a maxing
# function.
...
```

Here is an excerpt from the file `src/foo.py`:

```
# You can aggregate
# with a pooling function.
...
```

""",
            response_text="Understood. I'll refer to the excerpts for context, and ignore them for general questions.",
        )
    ]
    assert (
        prompt_output.message
        == f"""\
{PREVIOUS_CONVERSATION_HEADER}

U11111: hey bot!

{USER_QUESTION_HEADER}

U12345: what tokenizers are there?"""
    )


def test_slackbot_prompt_formatter_long_conversation_history(feature_flags):
    """test case for when there is a long conversation history passed in the request"""

    token_apportionent = ChatTokenApportionment(
        path_len=256,
        prefix_len=1,
        chat_history_len=25,  # setting this to a lower value to test the truncation
        suffix_len=1,
        retrieval_len=-1,
        max_prompt_len=8192 - 2048,
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )

    prompter = SlackbotPromptFormatter(
        "structured_binks_llama3_instruct", token_apportionent
    )

    conversation_history = [f"U12345: Message {i}" for i in range(10)]

    # Create a proper JSON structure
    request_data = {
        "botId": "U54321",
        "channelName": "test-channel",
        "previousThreadMessages": conversation_history,
        "repos": [],
        "allRepos": [],
    }

    # Convert the entire structure to a JSON string
    request_message = json.dumps(request_data)

    prompt_input = ChatPromptInput(
        message="U12345: what tokenizers are there?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[
            Exchange(
                request_message=request_message,
                response_text="",
            )
        ],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )

    prompt_output = prompter.format_prompt(prompt_input)

    assert prompt_output.system_prompt == SLACKBOT_SYSTEM_PROMPT.format(
        additional_info=NO_REPO_INFO.format(
            bot_id="U54321",
            channel_name="test-channel",  # RFC 3339 to match `ToJsonString()`
        )
    )

    assert prompt_output.chat_history == []
    assert (
        prompt_output.message
        == f"""\
{PREVIOUS_CONVERSATION_HEADER}

U12345: Message 0

...[message truncated]...: Message 9

{USER_QUESTION_HEADER}

U12345: what tokenizers are there?"""
    )


def get_sample_slack_thread() -> glean_pb2.Document:
    # Create a document tree with depth 2 and multiple authors
    root = glean_pb2.Document()
    root.content = "Root content"
    root.author.name = "Alice"
    root.author.id = "alice_id"
    root.created_at.FromDatetime(datetime(2023, 1, 1, 10, 0))
    root.updated_at.FromDatetime(datetime(2023, 1, 1, 11, 0))

    # Add a children from Alice (merge)
    child1 = root.children.add()
    child1.content = "Child 1 content"
    child1.author.name = "Alice"
    child1.author.id = "alice_id"
    child1.created_at.FromDatetime(datetime(2023, 1, 1, 10, 30))
    # updated at is null

    child2 = root.children.add()
    child2.content = "Child 2 content"
    child2.author.name = "Bob"
    child2.author.id = "bob_id"
    child2.created_at.FromDatetime(datetime(2023, 1, 1, 10, 45))
    child2.updated_at.FromDatetime(datetime(2023, 1, 1, 12, 0))

    # Add a child from Bob (should merge)
    child3 = root.children.add()
    child3.content = "Child 3 content"
    child3.author.name = "Bob"
    child3.author.id = "bob_id"
    child3.created_at.FromDatetime(datetime(2023, 1, 1, 11, 0))
    child3.updated_at.FromDatetime(datetime(2023, 1, 1, 11, 0))

    return root


def test_traverse_doc_list_merge_adjacent():
    root = get_sample_slack_thread()

    # Test merging
    merged_docs = SlackbotPromptFormatter.traverse_doc_list(root, merge_adjacent=True)

    # Should have 2 documents after merging: Alice's and Bob's content
    assert len(merged_docs) == 2

    # First document should be Alice's merged content
    assert merged_docs[0].author.name == "Alice"
    assert merged_docs[0].author.id == "alice_id"
    assert merged_docs[0].content == "Root content\nChild 1 content"
    # Should take earliest created_at
    assert merged_docs[0].created_at.ToDatetime() == datetime(2023, 1, 1, 10, 30)
    # Should take latest updated_at
    assert merged_docs[0].updated_at.ToDatetime() == datetime(2023, 1, 1, 11, 0)

    # Second document should be Bob's merged content
    assert merged_docs[1].author.name == "Bob"
    assert merged_docs[1].author.id == "bob_id"
    assert merged_docs[1].content == "Child 2 content\nChild 3 content"
    assert merged_docs[1].created_at.ToDatetime() == datetime(2023, 1, 1, 11, 0)
    assert merged_docs[1].updated_at.ToDatetime() == datetime(2023, 1, 1, 12, 0)

    # Test without merging
    unmerged_docs = SlackbotPromptFormatter.traverse_doc_list(
        root, merge_adjacent=False
    )
    assert len(unmerged_docs) == 4


def test_format_glean_content():
    root = get_sample_slack_thread()
    formatted_content = SlackbotPromptFormatter.format_glean_content(root)

    expected_content = (
        "Author: Alice | 2023-01-01 11:00 (Edited)\n"
        "---\n"
        "Root content\nChild 1 content\n\n"
        "Author: Bob | 12:00 (Edited)\n"
        "---\n"
        "Child 2 content\nChild 3 content\n"
    )

    assert formatted_content == expected_content


def test_default_token_apportionment_retrieval_as_tool():
    """Test that the default token apportionment has retrieval_as_tool=False"""
    prompter = SlackbotPromptFormatter("structured_binks_llama3_instruct")
    assert prompter.token_apportionment.retrieval_as_tool is False


def test_override_token_apportionment_retrieval_as_tool():
    """Test that even if we pass token_apportionment with retrieval_as_tool=True, it gets set to False"""
    token_apportionment = ChatTokenApportionment(
        path_len=256,
        prefix_len=1,
        chat_history_len=1024,
        suffix_len=1,
        retrieval_len=-1,
        max_prompt_len=8192,
        retrieval_as_tool=True,  # Try to set it to True
        # Deprecated fields
        message_len=-1,
        selected_code_len=-1,
    )

    prompter = SlackbotPromptFormatter(
        "structured_binks_llama3_instruct", token_apportionment=token_apportionment
    )

    assert prompter.token_apportionment.retrieval_as_tool is False

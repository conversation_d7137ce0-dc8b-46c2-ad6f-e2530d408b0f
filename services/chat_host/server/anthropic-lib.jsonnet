local lib = import 'deploy/common/lib.jsonnet';

function(appName, namespace, cloud)
  local secretName = 'anthropic-api-key-%s' % appName;  // pragma: allowlist secret
  local apiKeySealedSecret = {
    kind: 'SealedSecret',
    apiVersion: 'bitnami.com/v1alpha1',
    metadata: {
      name: secretName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'sealedsecrets.bitnami.com/cluster-wide': 'true',
      },
    },
    spec: {
      template: {
        metadata: {
          name: secretName,
          annotations: {
            'sealedsecrets.bitnami.com/cluster-wide': 'true',
          },
        },
      },
      encryptedData: {
        GCP_US_CENTRAL1_DEV: {
          'api-key.txt': 'AgAPVK1Ox7eVEH4H0zqa3Ezg5anVJGYJFn9LOQDG3X2ktgUm9kVtpBliEuK2oaDam1Q3EHyl5MuTpIxWShJ89xwgIaiecQi0e6T27mkzO/67WuQ9J0qQIuJcx0yCdfu9pkXfB3MLCtpdy0v9qUrv76JVxbumsbXOSw63CZuQP7xaDllB4/cws6zz19nRcJ4s370DPXhTLzwehHg8OhcD/KECT2YBdGcg2G7bAoPn5XCOGsDLxSf1yw/gXpqvCPYWfNN1pdBdGvI3wS9vbKROgIPY0vJGKgi0ndX6IdZOMNsYCHLDtjIm0VuYslog6lkqx2gMcIKQtyFCkTZS0CzoFr5KFXM9DxV5kVD75UCh7ZkUIiivAZDDzg39Usia9XEdnG/rD94MXOBvUnB18t0s4Jhz7lPVyoOYk71MVy1oGNyWrvZieK+crcVOUHfG8eFls2/ywsZQlyLCI7eeBhQAJaO1QD/YxO8KJxZ/XJE1l6k+DoXJFI6VjL1MrXNGspksTPmwRSYVIUvqY+u9YKDMZGDTkvvdAngVrXFDLLBBpWg/5gIXFQCRBkWgPK4QE18A36sHYoWjrlKrwxFqA2kdWV6S3KuXA3vxG4S/ODub1IFj6EqDkuRiL5wOWyUN3h2D1wwcxq430bPF2KLFrU1As6WcRMi2OmEC5E2KceSULI6Q2XiWE3PGfUA2/0TH1Ymf+20p2RAintLX9HqMWxMPjAtBXRM0Y/jg8JEYijP7LGJTe6ybPTjkvBDEMeLmjhFwyd2/kvNtzT0n899SAOKoFqLSyOU0Hxp6CjCWk6QaLrXFVe/ZqBHJqfemXMFwyt982im/QIr3wAVoH1lnsc4=',  // pragma: allowlist secret
        },
        GCP_US_CENTRAL1_PROD: {
          'api-key.txt': 'AgBJ8dc0PwC2DDDPp4RfzTPGOQ8LmJQadq1kgLyOWWZgGRe2qB/ASS/bDEUjkZWtCtQ3FtM0OhXYlEVMtNuJPAIUXURMd8iJXiYEZQebZaeO9FB+FdQxvq1DxM5/aYz9NCMrEUbtMzpTvQR/a8lqBJFy2QyNMB5uj9+6jlDWCONTeNw0dZvps3ItOxWe1oZ+kRg+FKaqE/LWStK4VF/rM5X1Wga/jsEOmptfxSPE107WKy36hf5kZJ7u4ttu9NAZTQHLupl3Syc0HRigEi0rbFyRsW1ebF75KuBa61tfHkA51bJssIFZAk0hlrqUY0z+IPwfybzhXYXaHclRLfANVrm/GZrmeIWQqQcPlWwGdIfs2mE6a5Xthpt9902WMkehgV3/PuoA+fvQpfQUc/iCIH2IZsjcy3z28eITKggoF+5RDVtLwLKeDr05IWzhyijD9j1bArdQmT+vUYK3E+FLlylRimQRV7L5So6rTLSgiQhye3ULlITMf8cJ3aJVkqEtKShLbYjdqVM5okeAoCV096q8/RZoxFUfG/mudc/+tnDxfvBTd/mCtKtIJYlkNHoe6YqwF8wK50kyyhz0o6tbYnMDyFS+XHR/BdX38mxyH/gtnLA5pRUhE5p32Xk/VYUNFvWuduMZI42HwAHgMDGZkEes/rhsu1+5XHQh1oJxOgqhV+sw36Jdh5NxWuVguLu4ZMJu9CXyeAB3lQN0IIZ5ZmXwhTZQpN8Nj+nrrYnxDCqet9Lhybsslv78AqrsqAVih6Blw5hsc3mzHqT4Q11mGQO9EFrb0clwHRm+ITvmp2yG5sD4Cq004O6aan+lPtCemytA2vRxAnvzOwfcSao=',  // pragma: allowlist secret
        },
        GCP_EU_WEST4_PROD: {
          'api-key.txt': 'AgCsN4XNeYYx5prSo3rperWNtCuq3WWGfXinygpAI+sP0aWAYXhhP+AL4W+0Ldv+Yg7NeJXAC+EHGpjAPxrljgoLOZTRP4HEQImZRkEv5a4Aq5sjynXj81/g7jMzk+HEMMbsX2sTpAzA/Z76tWLkWsDaK4DnvfXv33eerCurd3YMiyFcBjR26PwOduTor7+hVk43JZ5tYbr6dBuj5U/5Qx1uc3lPS6k0na9BSXAva8VHKBkmVrddL+JmABrHHKuvdX7wucmHuNNFGSYrnXyAkC/h3qbBxtGIPe3HRD6icEd8kJBy67NtKfLK/7XyFV+7+BMcTEQzVaC2FSwXxCkIIrcleAZzZtva7l84cDFnVjcDRceLmG2NsCgF9P2xw7hkVSczWlAfAyam9I764VVMeD8mSEfHvPmSwGJRPXRMqwHx9SlznnhuicFM2oebSfQGrE93uyQoh6MQ9TtQwdQ+Oa4OD/2tzBIiQKtWxJNDgPBcxL9BZ+f6ybwFq/ZNNbsT97K1tf7WGY6uTBeT5r9LJ/K16yvnvjI66TwLZADTuWs+8FPUg4z/skKceW5VNuWA3J5XT2zRcIveM4atItJG1KdqC24dII2k8Q430FW9A3Drf4np4e5QY4ler8chVxGEzwV/5+WdEVTK8LqwIx+EBA1JLQRc7skJ/wwOJh2VgK+6fNWXMUxuZYVyfSkHCt3yXZEaZqL8t9zR7gPlmajTpSDVuP7+5kZSssUkuJQWTilg13koBJKu8lR9kIZz1asHVFp2BKCjTFkKeB29BHcabfqkNrK0s07ZIQzXp3Jn8eLZAqV9SN2m8ilsm9DN3hUyt9FCC44NST8Cla4R1KY=',  // pragma: allowlist secret
        },
      }[cloud],
    },
  };
  {
    api_key_path: '/anthropic-api-key/api-key.txt',
    volumeMountDef: {
      name: 'anthropic-api-key',
      mountPath: '/anthropic-api-key',
      readOnly: true,
    },
    podVolumeDef: {
      name: 'anthropic-api-key',
      secret: {
        secretName: secretName,  // pragma: allowlist secret
      },
    },
    objects:: [apiKeySealedSecret],
  }

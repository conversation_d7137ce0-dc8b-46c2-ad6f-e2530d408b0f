"""Contains the core logic of handling chat requests."""

import concurrent.futures
import random
from dataclasses import dataclass
from typing import Iterable, Optional, Sequence

import opentelemetry.trace
import structlog
from dataclasses_json import dataclass_json

import base.feature_flags
from base.blob_names.python.blob_names import Blobs
from base.prompt_format.common import Exchange, PersonaType
from base.prompt_format_chat import (
    get_chat_prompt_formatter_by_name,
    get_generate_commit_message_prompt_formatter,
)
from base.prompt_format_chat.lib.token_counter import RoughTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    ChatPromptFormatter,
    ChatPromptInput,
    ChatTokenApportionment,
    GenerateCommitMessageTokenApportionment,
    SlackbotMessageTokenApportionment,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.prompt_formatter import Exchange as PromptExchange
from base.prompt_format_completion.prompt_formatter import Token<PERSON>ist
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.tokenizers import Tokenizer, create_tokenizer_by_name
from base.tokenizers.stream_utils import detokenize_stream
from services.chat_host import chat_pb2
from services.chat_host.server.chat_handler_metrics import ChatHandlerMetrics
from services.chat_host.server.chat_request_insight_builder import (
    ChatRequestInsightBuilder,
)
from services.chat_host.server.handler import (
    ChatHandlerProtocol,
    ChatResult,
    FindMissingResult,
)
from services.chat_host.server.utils import (
    convert_changed_file_stats_from_proto,
)
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.inference_host.client.multiplex import InferenceStubFactoryProtocol
from services.inference_host.infer_pb2_grpc import InfererStub
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

logger = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)

_ENABLE_AUTO_DOCSETS = base.feature_flags.BoolFlag("enable_auto_docsets", False)


@dataclass_json
@dataclass(frozen=True)
class SamplingParams:
    """The sampling parameters for the chat model. See infer.proto for their meaning."""

    top_k: int = 0
    """Top-k sampling parameter."""

    top_p: float = 0.0
    """Top-p sampling parameter."""

    temperature: float = 0.0
    """Temperature sampling parameter."""

    seed: int = 0
    """Random seed that is passed to inference host."""

    inference_timeout_s: float = 120.0
    """Timeout that is passed to inference host."""

    def __post_init__(self):
        assert 0.0 <= self.top_p <= 1.0, "top_p must be [0, 1]."
        assert self.top_k >= 0, "top_k must be non-negative."
        assert self.temperature >= 0, "temperature must be non-negative."
        assert self.seed >= 0, "seed must be non-negative."
        assert self.inference_timeout_s > 0, "inference_timeout_s must be positive."


@dataclass_json
@dataclass(frozen=True)
class ChatHandlerConfig:
    """Config for chat model."""

    tokenizer_name: str

    prompt_formatter_name: str

    token_apportionment: ChatTokenApportionment
    """Deprecated."""

    generate_commit_message_token_apportionment: Optional[
        GenerateCommitMessageTokenApportionment
    ]

    max_context_length: int
    """The maximal length of the context for the chat model."""

    max_output_length: Optional[int]
    """The maximal number of tokens in the output"""

    sampling_params: SamplingParams
    """The sampling parameters for the chat model."""

    preference_sampling_params: SamplingParams
    """Set of sampling params to use during preference data collection."""

    slackbot_message_token_apportionment: Optional[
        SlackbotMessageTokenApportionment
    ] = None


@dataclass(frozen=True)
class TrimmedOutputTokens:
    """Result of _trim_output_tokens."""

    truncated_tokens: Sequence[int]
    is_truncated: bool
    truncated_log_probs: Optional[Sequence[float]]


def _trim_output_tokens(
    eos_to_check: set[int], tokens: Sequence[int], log_probs: Optional[Sequence[float]]
) -> TrimmedOutputTokens:
    """Trims tokens and log_probs."""

    is_truncated = False
    for i, token in enumerate(tokens):
        if token in eos_to_check:
            tokens = tokens[:i]
            is_truncated = True
            if log_probs is not None:
                log_probs = log_probs[:i]
            break

    return TrimmedOutputTokens(
        truncated_tokens=tokens,
        is_truncated=is_truncated,
        truncated_log_probs=log_probs,
    )


def _last_whitespace_index(s: str) -> int:
    for i in range(len(s) - 1, -1, -1):
        if s[i].isspace():
            return i
    return -1


def _has_whitespace(s: str) -> bool:
    return any(c.isspace() for c in s)


class ChatHandler(ChatHandlerProtocol):
    """Handles chat requests."""

    def __init__(
        self,
        stub_factory: InferenceStubFactoryProtocol,
        tokenizer: Tokenizer,
        prompt_formatter: ChatPromptFormatter,
        commit_message_prompt_formatter: ChatPromptFormatter | None,
        max_context_length: int,
        namespace: str,
        sampling_params: SamplingParams,
        preference_sampling_params: SamplingParams,
        ri_publisher: RequestInsightPublisher,
        metrics: ChatHandlerMetrics,
        content_manager_client: ContentManagerClient,
        retriever: Retriever[ChatRetrieverPromptInput],
        max_output_length: Optional[int] = None,
    ):
        self.client = InfererClient(stub_factory)
        self.tokenizer = tokenizer
        self.prompt_formatter = prompt_formatter
        self.commit_message_prompt_formatter = commit_message_prompt_formatter
        self.max_context_length = max_context_length
        self.namespace = namespace
        self.sampling_params = sampling_params
        self.preference_sampling_params = preference_sampling_params
        self.retriever = retriever
        self.end_token_ids = {self.tokenizer.special_tokens.eos}
        self.ri_builder = ChatRequestInsightBuilder(ri_publisher)
        self.metrics = metrics
        self.max_output_length = max_output_length
        self.content_manager_client = content_manager_client

    def _get_chat_result(
        self,
        text: str,
        missing_blob_names: Sequence[str],
        checkpoint_not_found: bool,
        prompt_output: TokenizedChatPromptOutput | None = None,
    ) -> ChatResult:
        incorporated_external_sources = []
        workspace_file_chunks = []
        if prompt_output:
            workspace_file_chunks = prompt_output.workspace_file_chunks()
            for chunk in prompt_output.retrieved_chunks_in_prompt:
                if chunk.documentation_metadata is not None:
                    # This is a docset chunk
                    s = chat_pb2.IncorporatedExternalSource(
                        source_name=chunk.documentation_metadata.name,
                        source_id=chunk.documentation_metadata.source_id,
                    )
                    if s not in incorporated_external_sources:
                        incorporated_external_sources.append(s)
        return ChatResult(
            text=text,
            unknown_blob_names=missing_blob_names,
            checkpoint_not_found=checkpoint_not_found,
            workspace_file_chunks=workspace_file_chunks,
            incorporated_external_sources=incorporated_external_sources,
        )

    def chat_stream(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> Iterable[ChatResult]:
        if request.prompt_formatter_name == "generate-commit-message":
            assert self.commit_message_prompt_formatter
            prompt_formatter = self.commit_message_prompt_formatter
        elif request.prompt_formatter_name == "":
            prompt_formatter = self.prompt_formatter
        else:
            raise ValueError(
                f"Unknown prompt formatter name: {request.prompt_formatter_name}"
            )

        with tracer.start_as_current_span("retrieval"):
            # Be very careful, -1 means infinite here!
            if prompt_formatter.token_apportionment.retrieval_len != 0:
                retrieval_result = self.retrieve(
                    request, request_context, auth_info, executor
                )
                retrieved_chunks = [
                    x.to_prompt_chunk() for x in retrieval_result.get_retrieved_chunks()
                ]
            else:
                retrieval_result = RetrievalResult(
                    retrieved_chunks=[],
                    missing_blob_names=[],
                    checkpoint_not_found=False,
                )
                retrieved_chunks = []
            chat_history = [
                PromptExchange(
                    request_message=item.request_message,
                    response_text=item.response_text,
                    request_id=item.request_id if item.HasField("request_id") else None,
                )
                for item in request.chat_history
            ]
        with tracer.start_as_current_span("format_prompt"):
            if request.external_source_ids:
                external_source_ids = list(request.external_source_ids)
            else:
                external_source_ids = []
            prompt_input = ChatPromptInput(
                path=request.path,
                prefix=request.prefix,
                selected_code=request.selected_code,
                suffix=request.suffix,
                message=request.message,
                chat_history=chat_history,
                prefix_begin=request.position.prefix_begin,
                suffix_end=request.position.suffix_end,
                retrieved_chunks=retrieved_chunks,
                context_code_exchange_request_id=(
                    request.context_code_exchange_request_id
                    if len(request.context_code_exchange_request_id) > 0
                    else None
                ),
                external_source_ids=external_source_ids,
                changed_file_stats=convert_changed_file_stats_from_proto(
                    request.changed_file_stats
                ),
                diff=request.diff,
                relevant_commit_messages=list(request.relevant_commit_messages),
                example_commit_messages=list(request.example_commit_messages),
                workspace_guidelines=request.workspace_guidelines,
                user_guidelines=request.user_guidelines,
                memories=request.agent_memories,
                tasklist=request.agent_tasks,
                persona_type=PersonaType(request.persona_type),
            )
            prompt_output = prompt_formatter.format_prompt(prompt_input)

        with tracer.start_as_current_span("inference"):
            reply_generator = executor.submit(
                self._infer_stream,
                prompt_output.tokens,
                request_context,
                request,
            )

            executor.submit(
                self.ri_builder.record_request,
                prompt_output,
                self.tokenizer,
                request,
                request_context=request_context,
                auth_info=auth_info,
                external_source_ids=request.external_source_ids,
            )

            mid_stream_error = None
            all_tokens = []
            all_log_probs = []
            text_buffer = ""  # Aggregates text for batching stream
            missing_blob_names = list(retrieval_result.get_missing_blob_names())
            checkpoint_not_found = retrieval_result.get_checkpoint_not_found()

            # Iterate over the replies from the generator.
            reply_iterator = iter(reply_generator.result())
            try:
                # will raise StopIteration if there are no replies
                first_reply = next(reply_iterator)

                # Stream missing blobs and incorporated external sources before tokens
                yield self._get_chat_result(
                    text="",
                    missing_blob_names=missing_blob_names,
                    checkpoint_not_found=checkpoint_not_found,
                    prompt_output=prompt_output,
                )

                def process_reply(reply):
                    # Trim the output tokens to remove any unnecessary information
                    trim_result = _trim_output_tokens(
                        eos_to_check=self.end_token_ids,
                        tokens=reply.output_tokens,
                        log_probs=reply.log_probs,
                    )
                    # Append the trimmed tokens to the list of all tokens
                    all_tokens.extend(trim_result.truncated_tokens)
                    # If log probabilities are available, extend the list of all log probabilities
                    if trim_result.truncated_log_probs is not None:
                        all_log_probs.extend(trim_result.truncated_log_probs)
                    return trim_result.truncated_tokens

                # Create a token stream generator that handles trimming and tracking
                def token_stream():
                    # Handle first reply
                    yield process_reply(first_reply)

                    # Handle remaining replies
                    for reply in reply_iterator:
                        yield process_reply(reply)

                # Use stream_utils to handle multi-token character reconstruction
                for output_text in detokenize_stream(
                    token_stream(), self.tokenizer, self.end_token_ids
                ):
                    # Append the output text to the buffer
                    text_buffer += output_text
                    # Check if the buffer contains a whitespace
                    if _has_whitespace(text_buffer):
                        # Find the last whitespace character in the buffer
                        cut_index = _last_whitespace_index(text_buffer)
                        # If a whitespace is found, yield the result and update the buffer
                        if cut_index >= 0:
                            yield self._get_chat_result(
                                text=text_buffer[: cut_index + 1],
                                missing_blob_names=[],
                                checkpoint_not_found=checkpoint_not_found,
                            )
                            # Update the buffer and reset the list of missing blob names
                            text_buffer = text_buffer[cut_index + 1 :]

            except StopIteration:
                # don't need to do anything here, this only means the reply_iterator is empty
                # and we should fall through to the finally block
                pass

            except Exception as e:
                mid_stream_error = f"{type(e).__qualname__}: {str(e)}"  # Store for RI logging in finally
                raise e

            finally:
                # Input stream is done, we can yield any remaining text in the buffer, if we have any
                if len(text_buffer):
                    yield self._get_chat_result(
                        text=text_buffer,
                        missing_blob_names=[],
                        checkpoint_not_found=checkpoint_not_found,
                    )

                # Now that we have finished processing the entire stream, we can reassemble all of the information
                # to create a full result as a single result. This single result gets stored in Request Insight.
                full_text = self.tokenizer.detokenize(all_tokens)
                full_result = self._get_chat_result(
                    text=full_text,
                    missing_blob_names=retrieval_result.get_missing_blob_names(),
                    checkpoint_not_found=checkpoint_not_found,
                    prompt_output=prompt_output,
                )
                full_trim_result = _trim_output_tokens(
                    eos_to_check=self.end_token_ids,
                    tokens=all_tokens,
                    log_probs=all_log_probs,
                )
                executor.submit(
                    self.ri_builder.record_response,
                    full_result.to_chat_response_proto(),
                    tokenizer=self.tokenizer,
                    truncated_output=full_trim_result.truncated_tokens,
                    truncated_log_probs=full_trim_result.truncated_log_probs,
                    request_context=request_context,
                    auth_info=auth_info,
                    error_message=mid_stream_error,
                )

    def find_missing(
        self,
        request: chat_pb2.FindMissingRequest,
        request_context: RequestContext,
        executor: concurrent.futures.Executor,
    ) -> FindMissingResult:
        # Obtain a list of blobs that are not indexed for one or more retrievers.
        result = self.retriever.find_missing(
            request.blob_names, request_context, executor
        )
        return FindMissingResult(
            missing_blob_names=[], nonindexed_blob_names=result.missing_blob_names
        )

    def _infer(
        self,
        input_tokens: TokenList,
        request_context: RequestContext,
        request: chat_pb2.ChatRequest,
    ) -> InferenceClientProtocol.Reply:
        assert len(input_tokens) < self.max_context_length
        remaining_context_length = self.max_context_length - len(input_tokens)
        if self.max_output_length is None:
            max_output_length = remaining_context_length
        else:
            max_output_length = min(remaining_context_length, self.max_output_length)

        if request.enable_preference_collection:
            cur_sampling_params = self.preference_sampling_params
            cur_seed = random.randint(0, 2**30)
        else:
            cur_sampling_params = self.sampling_params
            cur_seed = self.sampling_params.seed

        reply = self.client.infer(
            input_tokens=input_tokens,
            max_output_length=max_output_length,
            end_token_ids=self.end_token_ids,
            top_k=cur_sampling_params.top_k,
            top_p=cur_sampling_params.top_p,
            temperature=cur_sampling_params.temperature,
            random_seed=cur_seed,
            request_context=request_context,
            timeout_s=cur_sampling_params.inference_timeout_s,
            sequence_id=request.sequence_id,
        )

        return reply

    def _infer_stream(
        self,
        input_tokens: TokenList,
        request_context: RequestContext,
        request: chat_pb2.ChatRequest,
    ) -> Iterable[InferenceClientProtocol.Reply]:
        assert len(input_tokens) < self.max_context_length
        remaining_context_length = self.max_context_length - len(input_tokens)
        if self.max_output_length is None:
            max_output_length = remaining_context_length
        else:
            max_output_length = min(remaining_context_length, self.max_output_length)

        if request.enable_preference_collection:
            cur_sampling_params = self.preference_sampling_params
            cur_seed = random.randint(0, 2**30)
        else:
            cur_sampling_params = self.sampling_params
            cur_seed = self.sampling_params.seed

        reply = self.client.infer_stream(
            input_tokens=input_tokens,
            max_output_length=max_output_length,
            end_token_ids=self.end_token_ids,
            top_k=cur_sampling_params.top_k,
            top_p=cur_sampling_params.top_p,
            temperature=cur_sampling_params.temperature,
            random_seed=cur_seed,
            request_context=request_context,
            timeout_s=cur_sampling_params.inference_timeout_s,
            sequence_id=request.sequence_id,
        )

        return reply

    def retrieve(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> RetrievalResult:
        if len(request.blobs) == 0:
            logger.warning(
                "Received request %s without blobs", request_context.request_id
            )
        blobs = [Blobs.from_proto(proto=b) for b in request.blobs]
        user_guided_blobs = (
            list(set(request.user_guided_blobs)) if request.user_guided_blobs else None
        )
        external_source_ids = (
            list(request.external_source_ids) if request.external_source_ids else None
        )

        disable_auto_external_sources = (
            request.disable_auto_external_sources
            or not _ENABLE_AUTO_DOCSETS.get(base.feature_flags.get_global_context())
        )
        retrieval_input = RetrievalInput(
            ChatRetrieverPromptInput(
                prefix=request.prefix,
                suffix=request.suffix,
                path=request.path,
                selected_code=request.selected_code,
                message=request.message,
                chat_history=[
                    Exchange(
                        request_message=item.request_message,
                        response_text=item.response_text,
                    )
                    for item in request.chat_history
                ],
                prefix_begin=request.position.prefix_begin,
                suffix_end=request.position.suffix_end,
                blob_name=request.position.blob_name,
            ),
            blobs=blobs,
            user_guided_blobs=user_guided_blobs,
            external_source_ids=external_source_ids,
            disable_auto_external_sources=disable_auto_external_sources,
        )

        with self.metrics.chat_retrieval_latency.labels(
            request.model_name,
            request_context.request_source,
            auth_info.tenant_name,
        ).time():
            retrieval_result = self.retriever.retrieve(
                input_=retrieval_input,
                request_context=request_context,
                auth_info=auth_info,
                executor=executor,
            )
        return retrieval_result


def create_chat_handler(
    config: dict,
    inference_stub_factory: InferenceStubFactoryProtocol,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    metrics: ChatHandlerMetrics,
    content_manager_client: ContentManagerClient,
    retriever: Retriever[ChatRetrieverPromptInput],
) -> ChatHandler:
    handler_config: ChatHandlerConfig = ChatHandlerConfig.schema().load(config)  # type: ignore

    tokenizer = create_tokenizer_by_name(handler_config.tokenizer_name)
    prompt_formatter = get_chat_prompt_formatter_by_name(
        handler_config.prompt_formatter_name,
        tokenizer,
        handler_config.token_apportionment,
    )
    commit_message_prompt_formatter = get_generate_commit_message_prompt_formatter(
        token_counter=RoughTokenCounter(),
        token_apportionment=handler_config.generate_commit_message_token_apportionment,
    )

    chat_handler = ChatHandler(
        inference_stub_factory,
        tokenizer,
        prompt_formatter,
        commit_message_prompt_formatter,
        handler_config.max_context_length,
        namespace,
        handler_config.sampling_params,
        handler_config.preference_sampling_params,
        ri_publisher,
        metrics,
        content_manager_client,
        retriever,
        handler_config.max_output_length,
    )

    return chat_handler

"""Tests for the postprocess module."""

from unittest.mock import Magic<PERSON>ock, patch

from services.chat_host.server.postprocess import Postprocess, PostprocessResponse
from services.inference_host.client.multiplex import RequestContext


def test_postprocess_sanity():
    """Test basic postprocessing with inference."""
    # Mock the inference client
    mock_client = MagicMock()
    mock_client.infer.return_value = MagicMock(
        output_tokens=[1, 2, 3, 4], log_probs=[0.1, 0.2, 0.3, 0.4]
    )

    # Mock the tokenizer
    mock_tokenizer = MagicMock()
    mock_tokenizer.special_tokens.eos = 4  # Set the last token as EOS
    mock_tokenizer.detokenize.return_value = "Processed response text"

    # Mock the prompt formatter
    mock_formatter = MagicMock()
    mock_formatter.format_prompt.return_value = MagicMock(tokens=[10, 20, 30])

    # Create config
    config = {
        "endpoint": "localhost:50051",
        "mtls": False,
        "client_ca_path": "/path/to/ca.crt",
        "client_cert_path": "/path/to/client.crt",
        "client_key_path": "/path/to/client.key",
        "name": "test_postprocessor",
        "timeout_s": 30,
        "prompt_formatter_name": "sentry_v1",
        "tokenizer_name": "qwen25coder",
        "max_output_length": 100,
        "token_apportionment": {"max_prompt_len": 8192},
    }

    # Patch the dependencies
    with patch(
        "services.chat_host.server.postprocess._create_client", return_value=mock_client
    ), patch(
        "services.chat_host.server.postprocess.create_tokenizer_by_name",
        return_value=mock_tokenizer,
    ), patch(
        "services.chat_host.server.postprocess.get_postprocess_prompt_formatter_by_name",
        return_value=mock_formatter,
    ):
        # Create the postprocessor
        postprocessor = Postprocess(config)

        # Create a test response message
        response_message = "Original response text"

        # Create a request context
        request_context = RequestContext.create()

        # Call postprocess
        result = postprocessor.postprocess(response_message, request_context)

        # Verify the result
        assert isinstance(result, PostprocessResponse)
        assert result.text == "Processed response text"
        assert result.tokens == [1, 2, 3]  # EOS token (4) should be removed
        assert result.log_probs == [
            0.1,
            0.2,
            0.3,
        ]  # Corresponding log_probs should be trimmed

        # Verify the mocks were called correctly
        mock_formatter.format_prompt.assert_called_once()
        mock_client.infer.assert_called_once_with(
            input_tokens=[10, 20, 30],
            max_output_length=100,
            end_token_ids={4},  # EOS token
            top_k=0,
            top_p=0.0,
            temperature=0.0,
            random_seed=0,
            request_context=request_context,
            timeout_s=30,
        )
        mock_tokenizer.detokenize.assert_called_once_with([1, 2, 3])

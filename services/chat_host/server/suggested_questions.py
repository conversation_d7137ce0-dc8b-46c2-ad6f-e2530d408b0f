import itertools
import pathlib
from dataclasses import dataclass
from typing import Iterator

from dataclasses_json import dataclass_json

from base.prompt_format.common import Exchange as PromptExchange
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
)


@dataclass_json
@dataclass
class SuggestedQuestionsConfig:
    """Configuration for suggested questions."""

    gcp_project_id: str
    gcp_region: str
    client_type: str
    model_name: str
    temperature: float
    max_output_tokens: int
    anthropic_api_key_path: str


class SuggestedQuestionsGenerator:
    def __init__(self, client: ThirdPartyModelClient):
        self.client = client
        self.prompt = """Write your 2 best guesses of what user will ask next. Keep them short. Enclose them in:
<guess_of_next_user_question>
    <next_user_question>...</next_user_question>
    <next_user_question>...</next_user_question>
</guess_of_next_user_question>"""
        self.prefill = """<guess_of_next_user_question>
    <next_user_question>"""

        self.prompt_sources = """List all files from the codebase that helped you answer the question. Enclose them in:
<useful_files>
    <file>...</file>
    <file>...</file>
</useful_files>

If no files were helpful then write:
<useful_files>
</useful_files>"""
        self.prefill_sources = """<useful_files>"""

    def init_cache(self, chat_history: list[PromptExchange], system_prompt: str | None):
        for _ in self.client.generate_response_stream(
            model_caller="suggested-questions-init-cache",
            chat_history=chat_history,
            system_prompt=system_prompt,
            cur_message="Just say 'OK'.",
            use_caching=True,
        ):
            pass

    def __call__(
        self,
        chat_history: list[PromptExchange],
        system_prompt: str | None,
        process_sources: bool = False,
    ) -> Iterator[ThirdPartyModelResponse]:
        prompt = self.prompt
        prefill = self.prefill
        if process_sources:
            prompt = self.prompt_sources
            prefill = self.prefill_sources

        result_iter = self.client.generate_response_stream(
            model_caller="suggested-questions",
            chat_history=chat_history,
            system_prompt=system_prompt,
            cur_message=prompt,
            tools=[],
            use_caching=True,
            prefill=prefill,
        )
        return itertools.chain([ThirdPartyModelResponse(prefill)], result_iter)


def create_suggested_questions_generator(config: dict) -> SuggestedQuestionsGenerator:
    suggested_questions_config: SuggestedQuestionsConfig = (
        SuggestedQuestionsConfig.schema().load(config)  # type: ignore
    )
    client_type = suggested_questions_config.client_type.lower()
    if client_type == "anthropic_direct":
        anthropic_api_key = (
            pathlib.Path(suggested_questions_config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicDirectClient(
            api_key=anthropic_api_key,
            model_name=suggested_questions_config.model_name,
            temperature=suggested_questions_config.temperature,
            max_output_tokens=suggested_questions_config.max_output_tokens,
        )
    else:
        raise ValueError(f"Unknown client type: {client_type}")

    return SuggestedQuestionsGenerator(client)

"""Contains the core logic of handling chat requests."""

import concurrent.futures
import hashlib
import pathlib
import sys
from dataclasses import dataclass
from random import random
from typing import Any, Generator, Iterable, Optional, ParamSpec, Sequence

import grpc
import opentelemetry.trace
import structlog
from dataclasses_json import dataclass_json
from prometheus_client import Counter, Histogram

import base.feature_flags
from base.blob_names.python.blob_names import Blobs
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    PersonaType,
    RequestMessage,
    StopReason,
    get_request_text_parts,
)
from base.prompt_format.common import (
    Exchange as PromptExchange,
)
from base.prompt_format_chat import (
    get_generate_commit_message_prompt_formatter,
    get_structured_chat_prompt_formatter_by_name,
)
from base.prompt_format_chat.lib.token_counter import RoughTokenCounter
from base.prompt_format_chat.prompt_formatter import (
    Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>atter,
    ChatPromptInput,
    ChatTokenApportionment,
    GenerateCommitMessageTokenApportionment,
    SlackbotMessageTokenApportionment,
    StructuredChatPromptFormatter,
    StructuredChatPromptOutput,
)
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.stream_processor.basic_stream_processor import (
    BasicStreamProcessor,
    StreamProcessorOutputType,
)
from base.stream_processor.claude_stream_processor_v3 import (
    ClaudeStreamProcessorV3,
    ThinkingSectionProcessingMode,
)
from base.stream_processor.claude_with_citations_stream_processor import (
    ClaudeStreamProcessor,
)
from base.third_party_clients.anthropic_direct_client import (
    AnthropicDirectClient,
    UnavailableRpcError,
)
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.fireworks_client import FireworksClient
from base.third_party_clients.google_genai_client import GoogleGenaiClient
from base.third_party_clients.openai_client import OpenAIClient
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
)
from base.third_party_clients.vertexai_client import VertexAiClient
from services.chat_host import chat_pb2
from services.chat_host.chat_proto_util import (
    request_to_message,
    response_to_message,
)
from services.chat_host.server.chat_handler_metrics import ChatHandlerMetrics
from services.chat_host.server.chat_request_insight_builder import (
    ChatRequestInsightBuilder,
)
from services.chat_host.server.handler import (
    ChatHandlerProtocol,
    ChatResult,
    FindMissingResult,
)
from services.chat_host.server.postprocess import Postprocess
from services.chat_host.server.prompt_format.slackbot_prompt_formatter import (
    SlackbotPromptFormatter,
)
from services.chat_host.server.prompt_format.slackbot_prompt_formatter_v2 import (
    SlackbotPromptFormatterV2,
)
from services.chat_host.server.suggested_questions import SuggestedQuestionsGenerator
from services.chat_host.server.utils import (
    convert_changed_file_stats_from_proto,
    convert_rules_from_proto,
)
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.lib.balanced_third_party_clients.anthropic_balanced import (
    AnthropicBalancedClient,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.third_party_arbiter.client.client import ThirdPartyArbiterClient

logger = structlog.get_logger()
tracer = opentelemetry.trace.get_tracer(__name__)


def get_pseudo_random_from_session_id(session_id: str) -> float:
    """
    Generate a deterministic pseudo-random value between 0 and 1 based on the session ID.

    Args:
        session_id: The session ID to use as a seed

    Returns:
        A float between 0 and 1
    """

    # Use MD5 to get a consistent hash value from the session ID
    # We're not using this for security, just for deterministic randomness
    hash_object = hashlib.md5(session_id.encode())  # nosec
    hash_hex = hash_object.hexdigest()

    # Convert the first 8 hex characters to an integer and normalize to [0, 1]
    # 8 hex characters = 32 bits = values from 0 to 4,294,967,295
    int_value = int(hash_hex[:8], 16)
    return int_value / 0xFFFFFFFF  # Normalize to [0, 1]


_ENABLE_AUTO_DOCSETS = base.feature_flags.BoolFlag("enable_auto_docsets", False)

_CHAT_GENERATE_RELEVANT_SOURCES = base.feature_flags.BoolFlag(
    "chat_generate_relevant_sources", False
)

_CHAT_GENERATE_TOOL_USE_START = base.feature_flags.BoolFlag(
    "chat_generate_tool_use_start", False
)

_CHAT_POSTPROCESS = base.feature_flags.BoolFlag("chat_postprocess", True)

_CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_EUROPE_RATE = base.feature_flags.FloatFlag(
    "chat_anthropic_vertexai_load_balance_europe_rate", 0.0
)

_CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_ASIA_RATE = base.feature_flags.FloatFlag(
    "chat_anthropic_vertexai_load_balance_asia_rate", 0.0
)

_CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_MODELS = base.feature_flags.StringFlag(
    "chat_anthropic_vertexai_load_balance_models", ""
)

# This may move into prompt formatter output / model config
_CHAT_REPROMPT_EMPTY_RESPONSE = base.feature_flags.BoolFlag(
    "chat_reprompt_empty_response", False
)

# TODO(zheren): This is a temporary feature flag to control the distribution rate
# between direct Anthropic and VertexAI. It will be removed once a more permanent
# solution is implemented.
_CHAT_ANTHROPIC_DIRECT_RATE = base.feature_flags.FloatFlag(
    "chat_anthropic_direct_rate", 0.0
)

_SLACKBOT_ENABLE_V2_FORMATTER = base.feature_flags.BoolFlag(
    "slackbot_enable_v2_formatter", False
)

_ENABLE_RULES = base.feature_flags.BoolFlag("enable_rules", False)

EMPTY_RAW_RESPONSE = Counter(
    "au_chat_empty_raw_response",
    "Counts the number of times the model response is empty",
    ["model_name", "non_text_nodes"],
)

EMPTY_RESPONSE_REPROMPTED = Counter(
    "au_chat_empty_response_reprompted",
    "Counts the number of times the model response is empty and we reprompt",
    ["model_name", "input_type", "output_type", "failure"],
)


POSTPROCESS_LATENCY_BUCKETS = tuple(
    [0.01 * (1.3**i) for i in range(1, 24)] + [float("inf")]
)

POSTPROCESS_LATENCY = Histogram(
    "au_postprocess_latency",
    "Latency of the postprocess processing (in the chat host) in seconds",
    ["postprocess_model", "request_source", "tenant_name"],
    buckets=POSTPROCESS_LATENCY_BUCKETS,
)

POSTPROCESS_CHANGE_COUNTER = Counter(
    "au_postprocess_change_counter",
    "Number of times postprocessing has changed the response.",
    ["postprocess_model", "request_source", "tenant_name"],
)

POSTPROCESS_FAILURE_COUNTER = Counter(
    "au_postprocess_response_failure",
    "Number of times the postprocessor failed to provide a valid response",
    ["postprocess_model", "request_source", "tenant_name"],
)

# Prompt cache usage metrics are now defined in the anthropic_direct_client.py file

# This is to be returned to clients who do not support TOOL_USE_START nodes when
# we exhaust output tokens without generating a full tool use.
# The purpose is to avoid the conversation ending abruptly and with no indication why.
# As it requires there be tool use generation happening, it's Agent-mode only right now.
TOOL_EXHAUSTED_TOKENS_RESPONSE = (
    "\nI'm sorry. I tried to call a tool, but provided too large of an input. "
    "How would you like to proceed?\n"
)


@dataclass_json
@dataclass(frozen=True)
class ChatThirdPartyHandlerConfig:
    """Config for chat model."""

    gcp_project_id: str

    gcp_region: str

    anthropic_api_key_path: str

    openai_api_key_path: str

    xai_api_key_path: str

    fireworks_api_key_path: str

    client_type: str
    """ Type of third party client, e.g. 'vertexai' """

    model_name: str

    prompt_formatter_name: str

    temperature: float

    max_output_tokens: int

    token_apportionment: ChatTokenApportionment
    """Deprecated."""

    generate_commit_message_token_apportionment: Optional[
        GenerateCommitMessageTokenApportionment
    ]

    slackbot_message_token_apportionment: Optional[
        SlackbotMessageTokenApportionment
    ] = None


# TODO(zheren): This is a temporary solution to distribute load between direct Anthropic and VertexAI.
# We will revisit and revert this hack once a more permanent solution is implemented.
class AnthropicMultiClient(ThirdPartyModelClient):
    """A client that can distribute load between direct Anthropic and VertexAI.

    This is a temporary solution to handle high load by distributing traffic between
    direct Anthropic API and VertexAI. The distribution is controlled by the
    _CHAT_ANTHROPIC_DIRECT_RATE feature flag.

    This implementation avoids correlation issues by using different salts for the
    direct vs. VertexAI decision and the regional decision within VertexAI. This ensures
    that the two decisions are independent and don't create unintended distribution patterns.
    """

    def __init__(
        self,
        api_key,
        project_id,
        region,
        model_name,
        temperature,
        max_output_tokens,
    ):
        # Store model names for reference
        self.vertex_model_name = model_name
        self.direct_model_name = self._convert_model_name_for_direct(model_name)

        # Initialize direct client only if we have a valid direct model name
        if self.direct_model_name is not None:
            self.direct_client = AnthropicDirectClient(
                api_key=api_key,
                model_name=self.direct_model_name,
                temperature=temperature,
                max_output_tokens=max_output_tokens,
            )
        else:
            self.direct_client = None
            logger.info(
                "No direct Anthropic mapping for model %s. Will only use VertexAI.",
                self.vertex_model_name,
            )

        # Always initialize the VertexAI client
        self.multi_region_vertex_client = AnthropicVertexAiMultiRegionClient(
            project_id=project_id,
            region=region,
            model_name=self.vertex_model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )

    def _convert_model_name_for_direct(self, vertex_model_name: str) -> Optional[str]:
        """Convert VertexAI model name to direct Anthropic model name.

        The model names differ between VertexAI and direct Anthropic configurations.
        This method handles the conversion based on known patterns.

        TODO(zheren): This is a temporary solution. This hack can be removed once
        we have a proper proxy solution in place.
        """

        model_name_mapping = {
            "claude-3-5-sonnet-v2@20241022": "claude-3-5-sonnet-20241022",
            "claude-3-7-sonnet@20250219": "claude-3-7-sonnet-20250219",
            "claude-sonnet-4@20250514": "claude-sonnet-4-20250514",
            "claude-opus-4@20250514": "claude-opus-4-20250514",
        }

        if vertex_model_name in model_name_mapping:
            return model_name_mapping[vertex_model_name]
        else:
            logger.info(
                "Unknown model name for direct Anthropic: %s. Using VertexAI.",
                vertex_model_name,
            )
            return None

    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[PromptExchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
    ):
        # Get the direct rate from feature flag
        direct_rate = _CHAT_ANTHROPIC_DIRECT_RATE.get(
            base.feature_flags.get_global_context()
        )

        # Get a deterministic value based on session ID for consistent routing
        # Use a different salt ("direct") for the direct vs. VertexAI decision to avoid correlation
        if (
            request_context
            and hasattr(request_context, "request_session_id")
            and request_context.request_session_id
        ):
            # Add "direct" suffix to get a different hash for the direct vs. VertexAI decision
            rand_val = get_pseudo_random_from_session_id(
                request_context.request_session_id + "direct"
            )
            logger.info(
                "Using session ID for direct randomization: %s",
                request_context.request_session_id,
            )
        else:
            # Fallback to random if no session ID is available
            rand_val = random()
            logger.info(
                "No session ID available, using random value for direct decision"
            )

        # Select client based on the rate
        if rand_val < direct_rate:
            # We want to use direct client based on the rate
            if self.direct_client is not None:
                logger.info(
                    "Using Direct Anthropic client. rand_val: %f, direct_rate: %f",
                    rand_val,
                    direct_rate,
                )
                client = self.direct_client
            else:
                # We want to use direct client but don't know a valid model name
                logger.warning(
                    "Unknown model name for direct Anthropic: %s. Using VertexAI.",
                    self.vertex_model_name,
                )
                client = self.multi_region_vertex_client
        else:
            # Use VertexAI based on the rate
            logger.info(
                "Using VertexAI client. rand_val: %f, direct_rate: %f, model: %s",
                rand_val,
                direct_rate,
                self.vertex_model_name,
            )
            client = self.multi_region_vertex_client

        # Forward the request to the selected client
        return client.generate_response_stream(
            model_caller=model_caller,
            messages=messages,
            system_prompt=system_prompt,
            cur_message=cur_message,
            chat_history=chat_history,
            tools=tools,
            tool_definitions=tool_definitions,
            tool_choice=tool_choice,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
            prefill=prefill,
            use_caching=use_caching,
            request_context=request_context,
            yield_final_parameters=yield_final_parameters,
        )

    def count_tokens(self, message: str) -> int:
        # Use the multi-region VertexAI client for token counting since direct client
        # might be None if we don't have a valid direct model name.
        return self.multi_region_vertex_client.count_tokens(message)


class AnthropicVertexAiMultiRegionClient(ThirdPartyModelClient):
    def __init__(
        self,
        project_id,
        region,
        model_name,
        temperature,
        max_output_tokens,
    ):
        # While this hack is active, nobody should change the region config, as it's ignored
        assert (
            region == "us-east5"
        ), f"Unsupported {region}. Only us-east5 is supported for now. This hack will be removed with the arrival of Claude proxy."
        self.model_name = model_name

        self.us_client = AnthropicVertexAiClient(
            project_id=project_id,
            region="us-east5",
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )
        self.eu_client = AnthropicVertexAiClient(
            project_id=project_id,
            region="europe-west1",
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )
        self.asia_client = AnthropicVertexAiClient(
            project_id=project_id,
            region="asia-southeast1",
            model_name=model_name,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
        )

    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[PromptExchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
    ):
        # TODO(markus): remove this poor-man's load balancing hack
        # Get a single pseudo-random value for consistent decision making based on session ID
        load_balanced_models_flag = _CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_MODELS.get(
            base.feature_flags.get_global_context()
        )
        if self.model_name in load_balanced_models_flag:
            logger.info("Using load balancing for model %s", self.model_name)
            if (
                request_context
                and hasattr(request_context, "request_session_id")
                and request_context.request_session_id
            ):
                rand_val = get_pseudo_random_from_session_id(
                    request_context.request_session_id
                )
                logger.info(
                    "Using session ID for randomization: %s",
                    request_context.request_session_id,
                )
            else:
                # Fallback to random if no session ID is available
                rand_val = random()
                logger.info("No session ID available, using random value")

            # Get the load balancing rates from feature flags
            europe_rate = _CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_EUROPE_RATE.get(
                base.feature_flags.get_global_context()
            )
            asia_rate = _CHAT_ANTHROPIC_VERTEXAI_LOAD_BALANCE_ASIA_RATE.get(
                base.feature_flags.get_global_context()
            )

            # Ensure rates sum to at most 1.0
            total_rate = europe_rate + asia_rate
            if total_rate > 1.0:
                # Normalize rates if they exceed 1.0
                europe_rate = europe_rate / total_rate
                asia_rate = asia_rate / total_rate

            # Select client based on normalized rates
            if rand_val < europe_rate:
                logger.info(
                    "Using Europe client. rand_val: %f, europe_rate: %f, asia_rate: %f",
                    rand_val,
                    europe_rate,
                    asia_rate,
                )
                client = self.eu_client
            elif rand_val < europe_rate + asia_rate:
                logger.info(
                    "Using Asia client. rand_val: %f, europe_rate: %f, asia_rate: %f",
                    rand_val,
                    europe_rate,
                    asia_rate,
                )
                client = self.asia_client
            else:
                logger.info(
                    "Using US client. rand_val: %f, europe_rate: %f, asia_rate: %f",
                    rand_val,
                    europe_rate,
                    asia_rate,
                )
                client = self.us_client
        else:
            logger.info("Not using load balancing for model %s", self.model_name)
            client = self.us_client

        return client.generate_response_stream(
            model_caller=model_caller,
            messages=messages,
            system_prompt=system_prompt,
            cur_message=cur_message,
            chat_history=chat_history,
            tools=tools,
            tool_definitions=tool_definitions,
            tool_choice=tool_choice,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
            prefill=prefill,
            use_caching=use_caching,
            request_context=request_context,
            yield_final_parameters=yield_final_parameters,
        )

    def count_tokens(self, message: str) -> int:
        return self.us_client.count_tokens(message)


class RepromptingClient(ThirdPartyModelClient):
    def __init__(self, model_name: str, client: ThirdPartyModelClient):
        self.model_name = model_name
        self.inner = client

    def count_tokens(self, message: str) -> int:
        return self.inner.count_tokens(message)

    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[PromptExchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Optional[Any] = None,
        yield_final_parameters: bool = False,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """
        If the inner client only produces EndOfStream without any text or tool
        use, will reprompt with an additional message asking the model to
        continue or explain why it's stopping.
        """

        def new_stream(cur_message: RequestMessage):
            return self.inner.generate_response_stream(
                model_caller=model_caller,
                messages=messages,
                system_prompt=system_prompt,
                cur_message=cur_message,
                chat_history=chat_history,
                tools=tools,
                tool_definitions=tool_definitions,
                tool_choice=tool_choice,
                temperature=temperature,
                max_output_tokens=max_output_tokens,
                prefill=prefill,
                use_caching=use_caching,
                request_context=request_context,
                yield_final_parameters=yield_final_parameters,
            )

        # Rule: if we yield anything from this stream, we yield everything from
        # this stream and nothing else.
        # Rule: if an exception is raised we yield everything we have buffered
        # so far before re-raising.
        stream = iter(new_stream(cur_message))
        buffered_responses = []
        try:
            for response in stream:
                if response.is_model_output():
                    yield from buffered_responses
                    yield response
                    yield from stream
                    return
                buffered_responses.append(response)
        except:
            yield from buffered_responses
            raise

        history_len = len(chat_history) if chat_history else 0
        input_had_tool_result = isinstance(cur_message, list) and any(
            node.type == ChatRequestNodeType.TOOL_RESULT for node in cur_message
        )
        logger.info(
            "Model produced no output, retrying with continuation prompt",
            model_caller=model_caller,
            history_length=history_len,
            input_had_tool_result=input_had_tool_result,
        )
        prompt = "\nContinue or explain why you are stopping"
        if isinstance(cur_message, str):
            updated_message = f"{cur_message}{prompt}"
        else:
            updated_message = list(cur_message) + [
                ChatRequestNode(
                    id=len(cur_message),
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(content=prompt),
                    tool_result_node=None,
                )
            ]
        any_text = False
        any_tool = False
        try:
            for response in new_stream(updated_message):
                if response.text and not response.text.isspace():
                    any_text = True
                if response.tool_use is not None:
                    any_tool = True
                yield response
        finally:
            # In the case of failure, perhaps we should just yield the original
            # result...
            EMPTY_RESPONSE_REPROMPTED.labels(
                model_name=self.model_name,
                input_type="tool_result" if input_had_tool_result else "text",
                output_type="tool_use" if any_tool else "text" if any_text else "empty",
                failure=(sys.exc_info() != (None, None, None)),
            ).inc()


class ChatThirdPartyHandler(ChatHandlerProtocol):
    """Handles chat requests."""

    def __init__(
        self,
        client: ThirdPartyModelClient,
        arbiter_client: ThirdPartyArbiterClient | None,
        prompt_formatter: StructuredChatPromptFormatter,
        commit_message_prompt_formatter: ChatPromptFormatter | None,
        slackbot_prompt_formatter: ChatPromptFormatter,
        slackbot_prompt_formatter_v2: ChatPromptFormatter,
        namespace: str,
        ri_publisher: RequestInsightPublisher,
        metrics: ChatHandlerMetrics,
        content_manager_client: ContentManagerClient,
        retriever: Retriever,
        prompt_formatter_name: str,
        postprocess: Postprocess | None = None,
        max_output_tokens: Optional[int] = None,
        use_xml_tag_citations: bool = True,
        use_xml_codeblocks: bool = True,
        n_extra_backticks: int = 0,
        suggested_questions_generator: SuggestedQuestionsGenerator | None = None,
        thinking_section_mode: ThinkingSectionProcessingMode = ThinkingSectionProcessingMode.PASS_THROUGH,
        prepend_thinking_tag: bool = False,
        use_prompt_caching: bool = False,
    ):
        self.client = client
        self.arbiter_client = arbiter_client
        self.prompt_formatter = prompt_formatter
        self.commit_message_prompt_formatter = commit_message_prompt_formatter
        self.slackbot_prompt_formatter = slackbot_prompt_formatter
        self.slackbot_prompt_formatter_v2 = slackbot_prompt_formatter_v2
        self.namespace = namespace
        self.retriever = retriever
        self.postprocess = postprocess
        self.ri_builder = ChatRequestInsightBuilder(ri_publisher)
        self.metrics = metrics
        self.max_output_tokens = max_output_tokens
        self.prompt_formatter_name = prompt_formatter_name
        self.content_manager_client = content_manager_client
        self.use_xml_tag_citations = use_xml_tag_citations
        self.use_xml_codeblocks = use_xml_codeblocks
        if self.use_xml_codeblocks and self.use_xml_tag_citations:
            raise ValueError(
                "Cannot use both xml_tag_citations and xml_codeblocks at the same time."
            )
        if not self.use_xml_codeblocks and (
            n_extra_backticks
            or thinking_section_mode != ThinkingSectionProcessingMode.PASS_THROUGH
        ):
            raise ValueError(
                "Cannot use n_extra_backticks or thinking_section_mode != PASS_THROUGH"
                " without xml_codeblocks."
            )
        self.n_extra_backticks = n_extra_backticks
        self.thinking_section_mode = thinking_section_mode
        self.prepend_thinking_tag = prepend_thinking_tag
        self.use_prompt_caching = use_prompt_caching
        self.suggested_questions_generator = suggested_questions_generator

    def _get_chat_result(
        self,
        text: str,
        missing_blob_names: Sequence[str],
        checkpoint_not_found: bool,
        prompt_output: StructuredChatPromptOutput | None = None,
        filter_paths: set[str] | None = None,
        stop_reason: StopReason | None = None,
    ) -> ChatResult:
        incorporated_external_sources = []
        workspace_file_chunks = []
        if prompt_output:
            for chunk in prompt_output.retrieved_chunks_in_prompt:
                if chunk.documentation_metadata is not None:
                    # This is a docset chunk
                    s = chat_pb2.IncorporatedExternalSource(
                        source_name=chunk.documentation_metadata.name,
                        source_id=chunk.documentation_metadata.source_id,
                    )
                    if s not in incorporated_external_sources:
                        incorporated_external_sources.append(s)
                else:
                    # Add chunk if there is either (a) no filter, or (b) the chunk passes the filter.
                    if filter_paths is None:
                        workspace_file_chunks.append(chunk)
                    elif chunk.path in filter_paths:
                        workspace_file_chunks.append(chunk)

        return ChatResult(
            text=text,
            unknown_blob_names=missing_blob_names,
            checkpoint_not_found=checkpoint_not_found,
            workspace_file_chunks=workspace_file_chunks,
            incorporated_external_sources=incorporated_external_sources,
            stop_reason=stop_reason,
        )

    def _get_structured_chat_result(
        self,
        checkpoint_not_found: bool,
        node_id: int,
        node_type: ChatResultNodeType,
        node_content: str,
        tool_use: ChatResultToolUse | None = None,
        stop_reason: StopReason | None = None,
    ):
        return ChatResult(
            text="",
            unknown_blob_names=[],
            checkpoint_not_found=checkpoint_not_found,
            nodes=[ChatResultNode(node_id, node_type, node_content, tool_use)],
            stop_reason=stop_reason,
        )

    def chat_stream(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> Iterable[ChatResult]:
        request_message = request_to_message(request.message, request.nodes)

        feature_flag_context = (
            base.feature_flags.get_global_context()
            .bind_attribute("tenant_name", auth_info.tenant_name)
            .bind_attribute("model_name", request.model_name)
        )

        if request.prompt_formatter_name == "generate-commit-message":
            assert self.commit_message_prompt_formatter
            prompt_formatter = self.commit_message_prompt_formatter
        elif request.prompt_formatter_name == "slackbot":
            if _SLACKBOT_ENABLE_V2_FORMATTER.get(feature_flag_context):
                prompt_formatter = self.slackbot_prompt_formatter_v2
            else:
                prompt_formatter = self.slackbot_prompt_formatter
        elif request.prompt_formatter_name == "":
            prompt_formatter = self.prompt_formatter
        else:
            raise ValueError(
                f"Unknown prompt formatter name: {request.prompt_formatter_name}"
            )

        with tracer.start_as_current_span("retrieval"):
            # Be very careful, -1 means infinite here!
            if prompt_formatter.token_apportionment.retrieval_len != 0:
                retrieval_result = self.retrieve(
                    request, request_context, auth_info, executor
                )
                retrieved_chunks = [
                    x.to_prompt_chunk() for x in retrieval_result.get_retrieved_chunks()
                ]
            else:
                retrieval_result = RetrievalResult(
                    retrieved_chunks=[],
                    missing_blob_names=[],
                    checkpoint_not_found=False,
                )
                retrieved_chunks = []
            chat_history = [
                PromptExchange(
                    request_message=request_to_message(
                        item.request_message,
                        item.request_nodes,
                    ),
                    response_text=response_to_message(
                        item.response_text,
                        item.response_nodes,
                    ),
                    request_id=item.request_id if item.HasField("request_id") else None,
                )
                for item in request.chat_history
            ]

        # Parse the tool definitions.
        tool_definitions = [
            ToolDefinition(
                tool.name,
                tool.description,
                tool.input_schema_json,
            )
            for tool in request.tool_definitions
        ]

        with tracer.start_as_current_span("format_prompt"):
            if request.external_source_ids:
                external_source_ids = list(request.external_source_ids)
            else:
                external_source_ids = []
            prompt_input = ChatPromptInput(
                path=request.path,
                prefix=request.prefix,
                selected_code=request.selected_code,
                suffix=request.suffix,
                message=request_message,
                chat_history=chat_history,
                prefix_begin=request.position.prefix_begin,
                suffix_end=request.position.suffix_end,
                retrieved_chunks=retrieved_chunks,
                context_code_exchange_request_id=(
                    request.context_code_exchange_request_id
                    if len(request.context_code_exchange_request_id) > 0
                    else None
                ),
                external_source_ids=external_source_ids,
                changed_file_stats=convert_changed_file_stats_from_proto(
                    request.changed_file_stats
                ),
                diff=request.diff,
                relevant_commit_messages=list(request.relevant_commit_messages),
                example_commit_messages=list(request.example_commit_messages),
                workspace_guidelines=request.workspace_guidelines,
                user_guidelines=request.user_guidelines,
                memories=request.agent_memories,
                tool_definitions=tool_definitions,
                persona_type=PersonaType(request.persona_type),
                rules=convert_rules_from_proto(request.rules)
                if _ENABLE_RULES.get(feature_flag_context)
                else [],
            )
            prompt_output = None
            try:
                prompt_output = prompt_formatter.format_prompt(prompt_input)
            finally:
                executor.submit(
                    self.ri_builder.record_request,
                    prompt_output
                    or StructuredChatPromptOutput(
                        system_prompt=None,
                        chat_history=[],
                        message="",
                        retrieved_chunks_in_prompt=[],
                    ),
                    None,  # tokenizer not needed for StructuredChatPromptOutput
                    request,
                    request_context=request_context,
                    auth_info=auth_info,
                    external_source_ids=request.external_source_ids,
                )
            assert isinstance(prompt_output, StructuredChatPromptOutput)

        tools = []
        # Add codebase-retrieval if it is not already present, if we want to render
        # retrieval as a tool call.
        if (
            prompt_output.retrieval_as_tool
            and "codebase-retrieval" not in tools
            and "codebase-retrieval" not in {tool.name for tool in tool_definitions}
        ):
            tools.append("codebase-retrieval")

        with tracer.start_as_current_span("third_party_inference"):
            current_trace_span = tracer.start_span("first_token")

            mid_stream_error = None
            stream_processor = None

            response_nodes_for_ri = []

            stop_reason: StopReason | None = None

            if _CHAT_REPROMPT_EMPTY_RESPONSE.get(feature_flag_context):
                active_client = RepromptingClient(request.model_name, self.client)
            else:
                active_client = self.client

            try:
                model_caller = f"chat-host-{request.model_name}"

                tool_choice = None
                if tools or tool_definitions:
                    # Default to not supporting parallel tool uses unless explicitly set
                    support_parallel = False
                    if request.HasField(
                        "feature_detection_flags"
                    ) and request.feature_detection_flags.HasField(
                        "support_parallel_tool_use"
                    ):
                        support_parallel = (
                            request.feature_detection_flags.support_parallel_tool_use
                        )

                    if not support_parallel:
                        tool_choice = ToolChoice(
                            disable_parallel_tool_use=not support_parallel
                        )

                response_iterator = executor.submit(
                    active_client.generate_response_stream,
                    model_caller=model_caller,
                    cur_message=prompt_output.message,
                    chat_history=list(prompt_output.chat_history),
                    system_prompt=prompt_output.system_prompt,
                    tools=tools,
                    tool_definitions=tool_definitions,
                    tool_choice=tool_choice,
                    prefill=prompt_output.prefill,
                    use_caching=self.use_prompt_caching,
                    request_context=request_context,
                )

                missing_blob_names = list(retrieval_result.get_missing_blob_names())
                checkpoint_not_found = retrieval_result.get_checkpoint_not_found()

                stream_processor = self._get_stream_processor(request)

                if self.suggested_questions_generator is not None:
                    try:
                        executor.submit(
                            self.suggested_questions_generator.init_cache,
                            list(prompt_output.chat_history),
                            prompt_output.system_prompt,
                        )
                    except UnavailableRpcError as e:
                        logger.warning(
                            f"Failed to init cache for suggested questions: {e.message}"
                        )

                is_first_token = True

                # Want this to be consistent over the entire request, not
                # changing between tools
                support_tool_use_start = (
                    request.feature_detection_flags.HasField("support_tool_use_start")
                    and request.feature_detection_flags.support_tool_use_start
                    and _CHAT_GENERATE_TOOL_USE_START.get(feature_flag_context)
                )
                tool_starts = 0
                tool_uses = 0

                for response in stream_processor.process_stream(
                    response_iterator.result()
                ):
                    if is_first_token:
                        # Replace first token span with stream span
                        current_trace_span.end()
                        current_trace_span = tracer.start_span("stream")

                        # Stream missing blobs and incorporated external sources before tokens
                        yield self._get_chat_result(
                            text="",
                            missing_blob_names=missing_blob_names,
                            checkpoint_not_found=checkpoint_not_found,
                            prompt_output=prompt_output,
                        )
                        is_first_token = False

                    # Handle tool responses
                    if response.type == StreamProcessorOutputType.TOOL_START:
                        if response.tool_use_start is None:
                            continue
                        tool_starts += 1
                        if not support_tool_use_start:
                            continue
                        result = self._get_structured_chat_result(
                            node_id=1,
                            node_type=ChatResultNodeType.TOOL_USE_START,
                            node_content=response.text,
                            tool_use=ChatResultToolUse(
                                tool_use_id=response.tool_use_start.tool_use_id,
                                name=response.tool_use_start.tool_name,
                                input={},
                            ),
                            checkpoint_not_found=checkpoint_not_found,
                        )
                        response_nodes_for_ri.extend(result.nodes)
                        yield result
                        continue
                    elif response.type == StreamProcessorOutputType.TOOL:
                        if response.tool_use is None:
                            continue
                        tool_uses += 1
                        result = self._get_structured_chat_result(
                            node_id=1,
                            node_type=ChatResultNodeType.TOOL_USE,
                            node_content=response.text,
                            tool_use=ChatResultToolUse(
                                tool_use_id=response.tool_use.tool_use_id,
                                name=response.tool_use.tool_name,
                                input=response.tool_use.input,
                                is_partial=response.tool_use.is_partial,
                            ),
                            checkpoint_not_found=checkpoint_not_found,
                        )
                        response_nodes_for_ri.extend(result.nodes)
                        yield result
                        continue
                    # Handle standard text responses
                    elif response.type == StreamProcessorOutputType.ANSWER:
                        yield self._get_chat_result(
                            text=response.text,
                            missing_blob_names=[],
                            checkpoint_not_found=checkpoint_not_found,
                        )
                        continue
                    # Handle citations
                    elif response.type == StreamProcessorOutputType.CITATION:
                        # `response.text` includes the citation path. By passing
                        # it as a filter, we are asking _get_chat_result to stream
                        # an empty response text but with the chunks corresponding to
                        # this citation path.
                        yield self._get_chat_result(
                            text="",
                            missing_blob_names=[],
                            checkpoint_not_found=checkpoint_not_found,
                            prompt_output=prompt_output,
                            filter_paths=set([response.text]),
                        )
                        continue
                    elif response.type == StreamProcessorOutputType.SUGGESTED_QUESTIONS:
                        result = self._get_structured_chat_result(
                            checkpoint_not_found=checkpoint_not_found,
                            node_id=1,
                            node_type=ChatResultNodeType.SUGGESTED_QUESTIONS,
                            node_content=response.text,
                        )
                        response_nodes_for_ri.extend(result.nodes)
                        yield result
                    elif response.type == StreamProcessorOutputType.END_OF_STREAM:
                        if response.end_of_stream:
                            stop_reason = response.end_of_stream.stop_reason
                            # Log and publish prompt cache usage from end_of_stream if available
                            if response.end_of_stream.prompt_cache_usage is not None:
                                logger.info(
                                    "Prompt cache usage",
                                    input_tokens=response.end_of_stream.prompt_cache_usage.input_tokens,
                                    cache_read_input_tokens=response.end_of_stream.prompt_cache_usage.cache_read_input_tokens,
                                    cache_creation_input_tokens=response.end_of_stream.prompt_cache_usage.cache_creation_input_tokens,
                                    text_input_tokens=response.end_of_stream.prompt_cache_usage.text_input_tokens,
                                    tool_input_tokens=response.end_of_stream.prompt_cache_usage.tool_input_tokens,
                                    text_output_tokens=response.end_of_stream.prompt_cache_usage.text_output_tokens,
                                    tool_output_tokens=response.end_of_stream.prompt_cache_usage.tool_output_tokens,
                                )
                                logger.info(
                                    "Output tokens",
                                    output_tokens=response.end_of_stream.output_tokens,
                                )
                                response.end_of_stream.prompt_cache_usage.model_caller = model_caller
                                self.ri_builder.record_prompt_cache_usage(
                                    response.end_of_stream.prompt_cache_usage,
                                    request_context,
                                    auth_info,
                                )

                if (
                    not support_tool_use_start
                    and tool_starts == 1
                    and tool_uses == 0
                    and stop_reason == StopReason.MAX_TOKENS
                ):
                    yield self._get_chat_result(
                        text=TOOL_EXHAUSTED_TOKENS_RESPONSE,
                        missing_blob_names=[],
                        checkpoint_not_found=checkpoint_not_found,
                    )

                raw_content = stream_processor.get_stream_history()
                if not raw_content:
                    EMPTY_RAW_RESPONSE.labels(
                        model_name=request.model_name,
                        non_text_nodes=(tool_starts + tool_uses) > 0,
                    ).inc()

                # TODO: Metrics for postprocess (len input, len output, is there a diff)

                # Run postprocess and yield the full response (if different) on MAIN_TEXT_FINISHED
                processed_fixed_text = ""
                if (
                    self.postprocess
                    and raw_content
                    and not raw_content.isspace()
                    and _CHAT_POSTPROCESS.get(feature_flag_context)
                ):
                    try:
                        with POSTPROCESS_LATENCY.labels(
                            postprocess_model=self.postprocess.model_name,
                            request_source=request_context.request_source,
                            tenant_name=auth_info.tenant_name,
                        ).time():
                            postprocess_response = self.postprocess.postprocess(
                                raw_content, request_context
                            )
                        processed_text = postprocess_response.text
                        if processed_text.strip() != raw_content.strip():
                            # Result of postprocess model is the new raw content to return
                            raw_content = processed_text

                            # Record postprocess response to RI
                            self.ri_builder.record_postprocess_response(
                                postprocess_response.tokens,
                                postprocess_response.log_probs,
                                self.postprocess.tokenizer,
                                None,
                                request_context,
                                auth_info,
                            )

                            # Run stream processor on fixed text, result will replace display text
                            postprocess_stream_processor = self._get_stream_processor(
                                request
                            )
                            for response in postprocess_stream_processor.process_stream(
                                [ThirdPartyModelResponse(processed_text)],
                            ):
                                processed_fixed_text += response.text

                            POSTPROCESS_CHANGE_COUNTER.labels(
                                postprocess_model=self.postprocess.model_name,
                                request_source=request_context.request_source,
                                tenant_name=auth_info.tenant_name,
                            ).inc()
                    except Exception as e:
                        error_message = f"{type(e).__qualname__}: {str(e)}"
                        logger.warning(
                            "Failed to postprocess response: %s", error_message
                        )
                        POSTPROCESS_FAILURE_COUNTER.labels(
                            postprocess_model=self.postprocess.model_name,
                            request_source=request_context.request_source,
                            tenant_name=auth_info.tenant_name,
                        ).inc()
                        self.ri_builder.record_postprocess_response(
                            None,
                            None,
                            self.postprocess.tokenizer,
                            error_message,
                            request_context,
                            auth_info,
                        )

                result = self._get_structured_chat_result(
                    checkpoint_not_found=checkpoint_not_found,
                    node_id=0,
                    node_type=ChatResultNodeType.RAW_RESPONSE,
                    node_content=raw_content,
                )
                response_nodes_for_ri.extend(result.nodes)
                yield result

                # Indication for client that streaming of main response finished
                result = self._get_structured_chat_result(
                    checkpoint_not_found=checkpoint_not_found,
                    node_id=2,
                    node_type=ChatResultNodeType.MAIN_TEXT_FINISHED,
                    node_content=processed_fixed_text,
                    stop_reason=stop_reason,
                )
                response_nodes_for_ri.extend(result.nodes)
                yield result

                # Generate and yield workspace file chunks
                workspace_file_chunks = map(
                    lambda chunk: f"{chunk.path}:{chunk.char_start}-{chunk.char_end}",
                    prompt_output.workspace_file_chunks(),
                )
                result = self._get_structured_chat_result(
                    checkpoint_not_found=checkpoint_not_found,
                    node_id=3,
                    node_type=ChatResultNodeType.WORKSPACE_FILE_CHUNKS,
                    node_content="\n".join(workspace_file_chunks),
                    stop_reason=stop_reason,
                )
                response_nodes_for_ri.extend(result.nodes)
                yield result

                # Generate suggested questions
                if self.suggested_questions_generator is not None:
                    try:
                        stream_processor_qs = ClaudeStreamProcessorV3(
                            n_extra_backticks=self.n_extra_backticks
                        )
                        suggested_question_iterator = executor.submit(
                            self.suggested_questions_generator,
                            list(prompt_output.chat_history)
                            + [
                                PromptExchange(
                                    request.message,
                                    stream_processor.get_stream_history(),
                                )
                            ],
                            prompt_output.system_prompt,
                        )

                        for response in stream_processor_qs.process_stream(
                            suggested_question_iterator.result()
                        ):
                            if (
                                response.type
                                == StreamProcessorOutputType.SUGGESTED_QUESTIONS
                            ):
                                result = self._get_structured_chat_result(
                                    checkpoint_not_found=checkpoint_not_found,
                                    node_id=1,
                                    node_type=ChatResultNodeType.SUGGESTED_QUESTIONS,
                                    node_content=response.text,
                                )
                                response_nodes_for_ri.extend(result.nodes)
                                yield result
                    except UnavailableRpcError as e:
                        logger.warning(
                            f"Failed to generate suggested questions: {e.message}"
                        )

                # Generate relevant sources
                if (
                    request.feature_detection_flags.support_relevant_sources
                    and self.suggested_questions_generator is not None
                    and _CHAT_GENERATE_RELEVANT_SOURCES.get(feature_flag_context)
                ):
                    try:
                        stream_processor_sources = ClaudeStreamProcessorV3(
                            n_extra_backticks=self.n_extra_backticks
                        )
                        relevant_sources_iterator = executor.submit(
                            self.suggested_questions_generator,
                            list(prompt_output.chat_history)
                            + [
                                PromptExchange(
                                    request.message,
                                    stream_processor.get_stream_history(),
                                )
                            ],
                            prompt_output.system_prompt,
                            process_sources=True,
                        )

                        for response in stream_processor_sources.process_stream(
                            relevant_sources_iterator.result()
                        ):
                            if (
                                response.type
                                == StreamProcessorOutputType.RELEVANT_SOURCES
                            ):
                                yield self._get_structured_chat_result(
                                    checkpoint_not_found=checkpoint_not_found,
                                    node_id=4,
                                    node_type=ChatResultNodeType.RELEVANT_SOURCES,
                                    node_content=response.text,
                                )
                    except UnavailableRpcError as e:
                        logger.warning(
                            f"Failed to generate relevant sources: {e.message}"
                        )

            except Exception as e:
                mid_stream_error = f"{type(e).__qualname__}: {str(e)}"  # Store for RI logging in finally
                raise e
            finally:
                # Close current span (must be either first_token or stream)
                current_trace_span.end()
                text = stream_processor.get_stream_history() if stream_processor else ""
                full_result = self._get_chat_result(
                    text=text,
                    missing_blob_names=retrieval_result.get_missing_blob_names(),
                    checkpoint_not_found=retrieval_result.get_checkpoint_not_found(),
                    prompt_output=prompt_output,
                    stop_reason=stop_reason,
                )
                full_result.nodes = response_nodes_for_ri

                try:
                    self.ri_builder.record_response(
                        full_result.to_chat_response_proto(),
                        tokenizer=None,
                        truncated_output=text,
                        truncated_log_probs=None,
                        request_context=request_context,
                        auth_info=auth_info,
                        error_message=mid_stream_error,
                    )
                except Exception:
                    # We catch _everything_ here because we'd prefer to cascade
                    # the first caught exception
                    logger.error(
                        "Failed to record response for request %s",
                        request_context.request_id,
                    )

    def _get_stream_processor(self, request: chat_pb2.ChatRequest):
        """Get the appropriate stream processor based on configuration and request.

        Args:
            request: The chat request

        Returns:
            The appropriate stream processor instance
        """
        is_filtering_by_citation = (
            self.use_xml_tag_citations and request.prompt_formatter_name == ""
        )

        if is_filtering_by_citation:
            # The commit tag pattern doesn't use citation based response formatting.
            return ClaudeStreamProcessor()
        elif self.use_xml_codeblocks and request.prompt_formatter_name == "":
            return ClaudeStreamProcessorV3(
                n_extra_backticks=self.n_extra_backticks,
                thinking_section_mode=self.thinking_section_mode,
                prepend_thinking_tag=self.prepend_thinking_tag,
            )
        else:
            return BasicStreamProcessor()

    def find_missing(
        self,
        request: chat_pb2.FindMissingRequest,
        request_context: RequestContext,
        executor: concurrent.futures.Executor,
    ) -> FindMissingResult:
        # Obtain a list of blobs that are not indexed for one or more retrievers.
        result = self.retriever.find_missing(
            request.blob_names, request_context, executor
        )
        return FindMissingResult(
            missing_blob_names=[], nonindexed_blob_names=result.missing_blob_names
        )

    def retrieve(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> RetrievalResult:
        request_message_text = get_request_text_parts(
            request_to_message(request.message, request.nodes)
        )

        if len(request.blobs) == 0:
            logger.warning(
                "Received request %s without blobs", request_context.request_id
            )
            raise ValueError("Received request without blobs")

        blobs = [Blobs.from_proto(b) for b in request.blobs]
        user_guided_blobs = (
            list(set(request.user_guided_blobs)) if request.user_guided_blobs else None
        )
        external_source_ids = (
            list(request.external_source_ids) if request.external_source_ids else None
        )

        disable_auto_external_sources = (
            request.disable_auto_external_sources
            or not _ENABLE_AUTO_DOCSETS.get(base.feature_flags.get_global_context())
        )

        retrieval_input = RetrievalInput(
            ChatRetrieverPromptInput(
                prefix=request.prefix,
                suffix=request.suffix,
                path=request.path,
                selected_code=request.selected_code,
                message=request_message_text,
                chat_history=[
                    PromptExchange(
                        request_message=item.request_message,
                        response_text=item.response_text,
                    )
                    for item in request.chat_history
                ],
                prefix_begin=request.position.prefix_begin,
                suffix_end=request.position.suffix_end,
                blob_name=request.position.blob_name,
            ),
            blobs=blobs,
            user_guided_blobs=user_guided_blobs,
            external_source_ids=external_source_ids,
            disable_auto_external_sources=disable_auto_external_sources,
        )

        with self.metrics.chat_retrieval_latency.labels(
            request.model_name,
            request_context.request_source,
            auth_info.tenant_name,
        ).time():
            retrieval_result = self.retriever.retrieve(
                input_=retrieval_input,
                request_context=request_context,
                auth_info=auth_info,
                executor=executor,
            )
        return retrieval_result


def create_chat_third_party_handler(
    config: dict,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    metrics: ChatHandlerMetrics,
    content_manager_client: ContentManagerClient,
    retriever: Retriever,
    postprocess: Postprocess | None = None,
    suggested_questions_generator: SuggestedQuestionsGenerator | None = None,
    arbiter_client: ThirdPartyArbiterClient | None = None,
) -> ChatThirdPartyHandler:
    handler_config: ChatThirdPartyHandlerConfig = (
        ChatThirdPartyHandlerConfig.schema().load(config)  # type: ignore
    )
    client_type = handler_config.client_type.lower()

    # Create the appropriate client based on client_type
    if client_type == "vertexai":
        client = VertexAiClient(
            project_id=handler_config.gcp_project_id,
            region=handler_config.gcp_region,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_tokens,
        )
    elif client_type == "google_genai":
        client = GoogleGenaiClient(
            project_id=handler_config.gcp_project_id,
            region=handler_config.gcp_region,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_tokens,
        )
    elif client_type == "anthropic_vertexai":
        # TODO(zheren): This is a temporary solution to distribute load between direct Anthropic and VertexAI.
        # We will revisit and revert this hack once a more permanent solution is implemented.
        # The AnthropicMultiClient will use the _CHAT_ANTHROPIC_DIRECT_RATE feature flag to determine
        # what percentage of traffic should go to direct Anthropic vs VertexAI.
        anthropic_api_key = (
            pathlib.Path(handler_config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicMultiClient(
            api_key=anthropic_api_key,
            project_id=handler_config.gcp_project_id,
            region=handler_config.gcp_region,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_tokens,
        )
    elif client_type == "anthropic_direct":
        anthropic_api_key = (
            pathlib.Path(handler_config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicDirectClient(
            api_key=anthropic_api_key,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_tokens,
        )
    elif client_type == "anthropic_balanced":
        # Check if arbiter client is available
        if arbiter_client is None:
            raise ValueError(
                "arbiter_client is required for anthropic_balanced client type"
            )

        # Get the API key
        anthropic_api_key = (
            pathlib.Path(handler_config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )

        # Create the balanced client
        client = AnthropicBalancedClient(
            arbiter_client=arbiter_client,
            anthropic_api_key=anthropic_api_key,
            gcp_project_id=handler_config.gcp_project_id,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_tokens,
        )
    elif client_type == "openai_vertexai":
        openai_api_key = (
            pathlib.Path(handler_config.openai_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        xai_api_key = (
            pathlib.Path(handler_config.xai_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = OpenAIClient(
            openai_api_key=openai_api_key,
            xai_api_key=xai_api_key,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_tokens,
        )
    elif client_type == "fireworks":
        fireworks_api_key = (
            pathlib.Path(handler_config.fireworks_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = FireworksClient(
            api_key=fireworks_api_key,
            model_name=handler_config.model_name,
            temperature=handler_config.temperature,
            max_output_tokens=handler_config.max_output_tokens,
        )
    else:
        raise ValueError(f"Unknown model type: {client_type}")

    prompt_formatter_name = handler_config.prompt_formatter_name
    token_apportionment = handler_config.token_apportionment
    prompt_formatter = get_structured_chat_prompt_formatter_by_name(
        prompt_formatter_name,  # type: ignore
        token_apportionment,
    )
    commit_message_prompt_formatter = get_generate_commit_message_prompt_formatter(
        token_counter=RoughTokenCounter(),
        token_apportionment=handler_config.generate_commit_message_token_apportionment,
    )
    slackbot_prompt_formatter = SlackbotPromptFormatter(
        prompt_formatter_name,
        token_apportionment,
    )
    slackbot_prompt_formatter_v2 = SlackbotPromptFormatterV2(
        prompt_formatter_name,
        slack_token_apportionment=handler_config.slackbot_message_token_apportionment,
    )

    # This is the only prompt formatter with XML tag based output.
    use_xml_tag_citations = handler_config.prompt_formatter_name == "binks-claude-v2"
    # This is the only prompt formatter with XML tag based output.
    use_xml_codeblocks = handler_config.prompt_formatter_name in [
        "binks-claude-v3",
        "binks-claude-v4",
        "binks-claude-v7",
        "binks-claude-v8",
        "binks-claude-v11",
        "binks-claude-v11-1",
        "binks-claude-v12",
        "binks-claude-v12-1",
        "binks-claude-v13",
        "binks-claude-v14",
        "binks-claude-v15",
        "binks-claude-v16",
        "binks-claude-v17",
        "binks-openai-v2",
        "binks-openai-v3-o1",
        "binks-openai-v3-o1-mini",
        "binks-openai-v3-o3-mini",
        "binks-openai-v4-o1",
        "binks-openai-v4-o1-mini",
        "binks-openai-v5-o1",
        "binks-openai-v5-o1-mini",
        "binks-deepseek-v3-v1",
        "binks-deepseek-r1-v1",
        "binks-deepseek-r1-v2",
        "agent-binks-claude-v2",
        "binks-qwen2-5-coder-v1",
        "binks-qwq-v1",
        "binks-qwq-v2",
        "agent-binks-claude-v3",
        "agent-binks-claude-v4",
        "agent-binks-claude-v5",
        "agent-binks-claude-v6",
        "agent-binks-claude-opus-v5",
        "agent-binks-gemini",
        "agent-binks-task-list-v1",
    ]
    n_extra_backticks = (
        1
        if handler_config.prompt_formatter_name
        in [
            "binks-claude-v4",
            "binks-claude-v7",
            "binks-claude-v8",
            "binks-claude-v11",
            "binks-claude-v11-1",
            "binks-claude-v12",
            "binks-claude-v12-1",
            "binks-claude-v13",
            "binks-claude-v14",
            "binks-claude-v15",
            "binks-claude-v16",
            "binks-claude-v17",
            "binks-openai-v3-o1",
            "binks-openai-v3-o1-mini",
            "binks-openai-v3-o3-mini",
            "binks-openai-v4-o1",
            "binks-openai-v4-o1-mini",
            "binks-openai-v5-o1",
            "binks-openai-v5-o1-mini",
            "binks-deepseek-v3-v1",
            "binks-deepseek-r1-v1",
            "binks-deepseek-r1-v2",
            "agent-binks-claude-v2",
            "binks-qwen2-5-coder-v1",
            "binks-qwq-v1",
            "binks-qwq-v2",
            "agent-binks-claude-v3",
            "agent-binks-claude-v4",
            "agent-binks-claude-v5",
            "agent-binks-claude-v6",
            "agent-binks-claude-opus-v5",
            "agent-binks-gemini",
            "agent-binks-task-list-v1",
        ]
        else 0
    )
    if handler_config.prompt_formatter_name in {
        "binks-deepseek-r1-v2",
        "binks-qwq-v2",
    }:
        thinking_section_mode = ThinkingSectionProcessingMode.MARKDOWN
    elif handler_config.prompt_formatter_name in {
        "binks-deepseek-r1-v1",
        "binks-qwq-v1",
    }:
        thinking_section_mode = ThinkingSectionProcessingMode.HIDE
    else:
        thinking_section_mode = ThinkingSectionProcessingMode.PASS_THROUGH
    prepend_thinking_tag = handler_config.prompt_formatter_name in {
        "binks-qwq-v1",
        "binks-qwq-v2",
    }

    # Could be part of StructuredChatPromptOutput, as the prompt formatter knows
    # whether its prompts are cache-friendly. Another aspect is how frequent
    # we believe requests will be. Agent in auto mode > Agent in manual mode > Chat
    use_prompt_caching = handler_config.prompt_formatter_name in [
        "agent-binks-claude-v1",
        "agent-binks-claude-v2",
        "agent-binks-claude-v3",
        "agent-binks-claude-v4",
        "agent-binks-claude-v5",
        "agent-binks-claude-v6",
        "agent-binks-claude-opus-v5",
        "agent-binks-gemini",
    ]

    chat_handler = ChatThirdPartyHandler(
        client=client,
        arbiter_client=arbiter_client,
        prompt_formatter=prompt_formatter,
        commit_message_prompt_formatter=commit_message_prompt_formatter,
        slackbot_prompt_formatter=slackbot_prompt_formatter,
        slackbot_prompt_formatter_v2=slackbot_prompt_formatter_v2,
        namespace=namespace,
        ri_publisher=ri_publisher,
        content_manager_client=content_manager_client,
        retriever=retriever,
        prompt_formatter_name=handler_config.prompt_formatter_name,
        postprocess=postprocess,
        max_output_tokens=handler_config.max_output_tokens,
        metrics=metrics,
        use_xml_tag_citations=use_xml_tag_citations,
        use_xml_codeblocks=use_xml_codeblocks,
        n_extra_backticks=n_extra_backticks,
        suggested_questions_generator=suggested_questions_generator,
        thinking_section_mode=thinking_section_mode,
        prepend_thinking_tag=prepend_thinking_tag,
        use_prompt_caching=use_prompt_caching,
    )

    return chat_handler

"""Unit tests for request_filter.py."""

import hashlib
import re
import threading
from typing import Generator
from unittest.mock import <PERSON>M<PERSON>, patch

import pydantic
import pytest

from base import feature_flags
from services.chat_host import chat_pb2
from services.chat_host.server.request_filter import (
    _BAN_MAX_SESSIONS_PER_USER,
    _BAN_RULES_URL,
    _BLOCK_CLIENTS_BY_PROMPT_REGEX,
    _BLOCK_HIGH_CJK_COUNT,
    _BLOCKED_DOMAINS,
    _USE_PREFIX_AND_SUFFIX,
    _STRICT_USES_PREFIX_AND_SUFFIX,
    _MAX_CJK_CHAR_COUNT,
    _blocked_domains_cache,
    _REJECTION_THRESHOLD,
    _BASE_ACCEPTANCE_PROBABILITY,
    BanRules,
    BlockedDomainsCache,
    SuspiciousUserTracker,
    _count_regex_matches,
    allow_request,
    clear_blocked_domains_cache,
    clear_user_tracker,
    count_cjk_characters,
    get_domain_from_email,
    is_cjk_character,
    parse_domains_list,
    regexp_definition_parser,
    Downloader,
    PeriodicBackgroundFunction,
    RegexpDownloader,
)
from services.lib.grpc.auth.service_auth import AuthInfo


@pytest.fixture()
def feature_flags_context() -> (
    Generator[feature_flags.LocalFeatureFlagSetter, None, None]
):
    yield from feature_flags.feature_flag_fixture()


def create_auth_info() -> AuthInfo:
    """Helper function to create an auth_info object."""
    return AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_shard_namespace",
        cloud="test_cloud",
        user_id=pydantic.SecretStr("test_user"),
        opaque_user_id="test_user",
        opaque_user_id_type="test_type",
        user_email=pydantic.SecretStr("<EMAIL>"),
        service_name="test_service",
    )


@pytest.fixture()
def mock_auth_info():
    """Fixture for creating a mock auth_info object."""
    mock_auth = MagicMock()
    mock_auth.tenant_name = "test_tenant"
    mock_auth.opaque_user_id = "test_user"
    # Create a mock SecretStr for user_email
    mock_email = MagicMock()
    mock_email.get_secret_value.return_value = "<EMAIL>"
    mock_auth.user_email = mock_email
    return mock_auth


def create_chat_request(
    prefix: str = "prefix",
    selected_code: str = "selected_code",
    suffix: str = "suffix",
    message: str = "message",
    nodes: list[chat_pb2.ChatRequestNode] = [],
    path: str = "path",
    chat_history: list[chat_pb2.Exchange] = [],
    user_guidelines: str = "",
    workspace_guidelines: str = "",
    is_suspicious: bool = False,
):
    """Helper function to create a chat request."""
    return chat_pb2.ChatRequest(
        prefix=prefix,
        selected_code=selected_code,
        suffix=suffix,
        message=message,
        nodes=nodes,
        path=path,
        chat_history=chat_history,
        user_guidelines=user_guidelines,
        workspace_guidelines=workspace_guidelines,
        is_suspicious=is_suspicious,
    )


@pytest.fixture()
def mock_request():
    """Fixture for creating a mock chat request."""
    request = MagicMock(spec=chat_pb2.ChatRequest)
    request.prefix = "prefix"
    request.selected_code = "selected_code"
    request.suffix = "suffix"
    request.message = "message"
    request.nodes = []
    request.path = "path"
    request.chat_history = []
    request.user_guidelines = ""
    request.workspace_guidelines = ""
    request.is_suspicious = False
    return request


def test_count_regex_matches():
    """Test _count_regex_matches function."""
    regex = re.compile(r"test")

    # No matches
    assert (
        _count_regex_matches(regex, "no match here", 10) == 0
    ), "Should return 0 when no matches are found"

    # One match
    assert (
        _count_regex_matches(regex, "one test match", 10) == 1
    ), "Should return 1 when one match is found"

    # Multiple matches
    assert (
        _count_regex_matches(regex, "test this test and test again", 10) == 3
    ), "Should return correct count for multiple matches"

    # Max matches
    assert (
        _count_regex_matches(regex, "test test test test test", 3) == 4
    ), "Should return max_matches+1 when matches exceed max_matches"


def test_ban_rules():
    """Test BanRules class."""
    # Initialize with None
    ban_rules = BanRules()
    assert (
        ban_rules.get() is None
    ), "get() should return None when initialized with no patterns"
    assert (
        ban_rules.get_strict() is None
    ), "get_strict() should return None when initialized with no patterns"

    # Initialize with regular pattern only
    ban_rules = BanRules(r"test")
    assert (
        ban_rules.get() is not None
    ), "get() should return a compiled regex when initialized with a pattern"
    assert (
        ban_rules.get().pattern == "test"
    ), "get() should return a regex with the correct pattern"
    assert (
        ban_rules.get_strict() is None
    ), "get_strict() should return None when no strict pattern is provided"

    # Initialize with both patterns
    ban_rules = BanRules(r"test", r"strict_test")
    assert (
        ban_rules.get() is not None
    ), "get() should return a compiled regex when initialized with a pattern"
    assert (
        ban_rules.get().pattern == "test"
    ), "get() should return a regex with the correct pattern"
    assert (
        ban_rules.get_strict() is not None
    ), "get_strict() should return a compiled regex when initialized with a strict pattern"
    assert (
        ban_rules.get_strict().pattern == "strict_test"
    ), "get_strict() should return a regex with the correct pattern"

    # Set a new regular pattern
    ban_rules.set(r"new_pattern")
    assert (
        ban_rules.get().pattern == "new_pattern"
    ), "get() should return a regex with the updated pattern after set()"
    assert (
        ban_rules.get_strict() is None
    ), "get_strict() should return None when no strict pattern is provided to set()"

    # Set both patterns
    ban_rules.set(r"new_pattern", r"new_strict_pattern")
    assert (
        ban_rules.get().pattern == "new_pattern"
    ), "get() should return a regex with the updated pattern after set()"
    assert (
        ban_rules.get_strict().pattern == "new_strict_pattern"
    ), "get_strict() should return a regex with the updated strict pattern after set()"

    # Set to None
    ban_rules.set(None)
    assert (
        ban_rules.get() is None
    ), "get() should return None after setting pattern to None"
    assert (
        ban_rules.get_strict() is None
    ), "get_strict() should return None after setting pattern to None"


def test_allow_request(mock_request, mock_auth_info):
    """Test allow_request function."""
    # Test with no ban rules
    ban_rules = BanRules()
    session_id = "test_session"
    assert (
        allow_request(mock_request, session_id, ban_rules) is True
    ), "Request should be allowed with no ban rules"

    # Test with regular ban rules that doesn't match
    ban_rules = BanRules(r"banned_word")
    assert (
        allow_request(mock_request, session_id, ban_rules) is True
    ), "Request should be allowed when no banned words match"

    # Test with regular ban rules that matches in the message
    mock_request.message = "This contains a banned_word here"
    assert (
        allow_request(mock_request, session_id, ban_rules) is True
    ), "Request should be allowed when banned words are below threshold"

    # Test with regular ban rules that matches too many times
    mock_request.message = (
        "banned_word banned_word banned_word banned_word banned_word banned_word"
    )
    assert (
        allow_request(mock_request, session_id, ban_rules, mock_auth_info) is False
    ), "Request should be blocked when too many banned words are present"

    # Test with chat history
    mock_request.message = "clean message"
    exchange = MagicMock()
    exchange.request_message = "banned_word banned_word banned_word"
    exchange.response_text = "clean response"
    exchange.request_nodes = []
    mock_request.chat_history = [exchange]
    assert (
        allow_request(mock_request, session_id, ban_rules) is True
    ), "Request should be allowed when chat history has banned words below threshold"

    # Test with chat history nodes
    node = MagicMock()
    node.type = chat_pb2.ChatRequestNodeType.TEXT
    node.text = "banned_word banned_word banned_word banned_word banned_word banned_word banned_word banned_word banned_word banned_word"
    exchange.request_nodes = [node]
    # Add more banned words to the request message to exceed the threshold
    mock_request.message = "banned_word banned_word banned_word"
    assert (
        allow_request(mock_request, session_id, ban_rules, mock_auth_info) is False
    ), "Request should be blocked when total banned words across request and history exceed threshold"

    # Test with strict ban rules - even a single match should disallow
    mock_request.message = "clean message"
    exchange.request_nodes = []
    mock_request.chat_history = []
    ban_rules = BanRules(None, r"strict_banned_word")
    assert (
        allow_request(mock_request, session_id, ban_rules) is True
    ), "Request should be allowed when no strict banned words match"

    # Test with strict ban rules - single match should disallow
    mock_request.message = "This contains a strict_banned_word here"
    assert (
        allow_request(mock_request, session_id, ban_rules, mock_auth_info) is True
    ), "Request should be allowed with strict banned words (current implementation doesn't block)"

    # Test with user ID
    mock_request.message = "clean message"
    ban_rules = BanRules()
    assert (
        allow_request(mock_request, session_id, ban_rules, mock_auth_info) is True
    ), "Request should be allowed with clean message and user ID"


def setup_regexp_download_test(
    feature_flags_context: feature_flags.LocalFeatureFlagSetter,
    uri: str = "http://example.com/pattern,d57e274ac2ebd27545c783ca19ef1fe2c528ea89103a6a22ef8fa9c44d2df8fd",
    content: str | Exception = "test_pattern\nstrict_test_pattern",
) -> tuple[RegexpDownloader, BanRules]:
    feature_flags_context.set_flag(_BAN_RULES_URL, uri)
    downloader = Downloader.create_null(content)
    ban_rules = BanRules()
    regexp_downloader = RegexpDownloader(ban_rules, downloader)
    return (regexp_downloader, ban_rules)


def test_regexp_downloader_happy_path(feature_flags_context):
    """Test the regexp download thread."""

    regexp_downloader, ban_rules = setup_regexp_download_test(feature_flags_context)

    regexp_downloader.regexp_download()

    normal_regexp = ban_rules.get()
    assert (
        normal_regexp is not None
    ), "Regexp download should have found a normal regexp"
    assert (
        normal_regexp.pattern == "test_pattern"
    ), "Regexp download should have found the correct normal regexp"
    strict_regexp = ban_rules.get_strict()
    assert (
        strict_regexp is not None
    ), "Regexp download should have found a strict regexp"
    assert (
        strict_regexp.pattern == "strict_test_pattern"
    ), "Regexp download should have found he correct strict regexp"


def test_regexp_download_no_hash(feature_flags_context):
    """Test the regexp download thread with no hash."""

    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context,
        uri="http://example.com/pattern",
    )

    regexp_downloader.regexp_download()

    normal_regexp = ban_rules.get()
    assert (
        normal_regexp is not None
    ), "Regexp download should have found a normal regexp"
    assert (
        normal_regexp.pattern == "test_pattern"
    ), "Regexp download should have found the correct normal regexp"
    strict_regexp = ban_rules.get_strict()
    assert (
        strict_regexp is not None
    ), "Regexp download should have found a strict regexp"
    assert (
        strict_regexp.pattern == "strict_test_pattern"
    ), "Regexp download should have found the correct strict regexp"


def test_regexp_download_hash_mismatch(feature_flags_context):
    """Test the regexp download thread with hash mismatch."""

    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context,
        uri="http://example.com/pattern,1234567890",
    )

    ban_rules.set("preserved1", "preserved2")

    regexp_downloader.regexp_download()

    # Hash mismatch should not clear rules
    regular_rules = ban_rules.get()
    assert (
        regular_rules is not None
    ), "Regexp download should not have cleared normal regexp on hash mismatch"
    assert (
        regular_rules.pattern == "preserved1"
    ), "Regexp download should not have changed normal regexp on hash mismatch"
    strict_rules = ban_rules.get_strict()
    assert (
        strict_rules is not None
    ), "Regexp download should not have cleared strict regexp on hash mismatch"
    assert (
        strict_rules.pattern == "preserved2"
    ), "Regexp download should not have changed strict regexp on hash mismatch"


def test_regexp_downloader_no_uri(feature_flags_context):
    """Test the regexp download thread with no URI."""

    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context, uri=""
    )

    ban_rules.set("test_pattern", "strict_test_pattern")

    regexp_downloader.regexp_download()

    assert (
        ban_rules.get() is None
    ), "Regexp download should have cleared normal regexp on no URI"
    assert (
        ban_rules.get_strict() is None
    ), "Regexp download should have cleared strict regexp on no URI"


def test_regexp_download_with_block_prompt_regex_empty(feature_flags_context):
    """Test the regexp download with empty block prompt regex feature flag."""

    # Set up with empty block prompt regex
    feature_flags_context.set_flag(_BLOCK_CLIENTS_BY_PROMPT_REGEX, "")
    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context, uri=""
    )

    regexp_downloader.regexp_download()

    # Verify block_prompt_regex is None when feature flag is empty
    assert (
        ban_rules.get_block_prompt() is None
    ), "Block prompt regex should be None when feature flag is empty"


def test_regexp_download_with_block_prompt_regex_set(feature_flags_context):
    """Test the regexp download with non-empty block prompt regex feature flag."""

    # Set up with a non-empty block prompt regex
    test_regex = "GitHub Copilot|Cline"
    feature_flags_context.set_flag(_BLOCK_CLIENTS_BY_PROMPT_REGEX, test_regex)
    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context, uri=""
    )

    regexp_downloader.regexp_download()

    # Verify block_prompt_regex is set correctly
    block_prompt_regex = ban_rules.get_block_prompt()
    assert (
        block_prompt_regex is not None
    ), "Block prompt regex should not be None when feature flag is set"
    assert (
        block_prompt_regex.pattern == test_regex
    ), "Block prompt regex should match the feature flag value"

    # Verify that the block prompt regex is set even when ban rules URL is provided
    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context,
        uri="http://example.com/pattern,d57e274ac2ebd27545c783ca19ef1fe2c528ea89103a6a22ef8fa9c44d2df8fd",
    )

    regexp_downloader.regexp_download()

    # Verify block_prompt_regex is still set correctly
    block_prompt_regex = ban_rules.get_block_prompt()
    assert (
        block_prompt_regex is not None
    ), "Block prompt regex should not be None when feature flag is set"
    assert (
        block_prompt_regex.pattern == test_regex
    ), "Block prompt regex should match the feature flag value"


def test_regexp_download_with_block_prompt_regex_from_file(feature_flags_context):
    """Test the regexp download with block prompt regex from file."""

    # Set up with empty block prompt regex feature flag
    feature_flags_context.set_flag(_BLOCK_CLIENTS_BY_PROMPT_REGEX, "")

    # Create content with all three patterns
    content = "test_pattern\nstrict_test_pattern\nClaude Code|Anthropic"

    # Create a hash for our specific content
    content_hash = hashlib.sha256(content.encode()).hexdigest()

    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context,
        uri=f"http://example.com/pattern,{content_hash}",
        content=content,
    )

    regexp_downloader.regexp_download()

    # Verify block_prompt_regex is set from the file
    block_prompt_regex = ban_rules.get_block_prompt()
    assert (
        block_prompt_regex is not None
    ), "Block prompt regex should not be None when present in file"
    assert (
        block_prompt_regex.pattern == "Claude Code|Anthropic"
    ), "Block prompt regex should match the file value"


def test_regexp_download_with_combined_block_prompt_regex(feature_flags_context):
    """Test the regexp download with block prompt regex from both feature flag and file."""

    # Set up with non-empty block prompt regex feature flag
    feature_flag_regex = "GitHub Copilot|Cline"
    feature_flags_context.set_flag(_BLOCK_CLIENTS_BY_PROMPT_REGEX, feature_flag_regex)

    # Create content with all three patterns
    file_regex = "Claude Code|Anthropic"
    content = f"test_pattern\nstrict_test_pattern\n{file_regex}"

    # Create a hash for our specific content
    content_hash = hashlib.sha256(content.encode()).hexdigest()

    regexp_downloader, ban_rules = setup_regexp_download_test(
        feature_flags_context,
        uri=f"http://example.com/pattern,{content_hash}",
        content=content,
    )

    regexp_downloader.regexp_download()

    # Verify block_prompt_regex is combined from both sources
    block_prompt_regex = ban_rules.get_block_prompt()
    assert (
        block_prompt_regex is not None
    ), "Block prompt regex should not be None when present in both sources"
    assert (
        block_prompt_regex.pattern == f"(?:{feature_flag_regex})|(?:{file_regex})"
    ), "Block prompt regex should be combined from both sources"


def test_periodic_background_function_calls_function(feature_flags_context):
    """Test that the periodic background function calls the function."""

    shutdown_event = threading.Event()

    function_calls = 0
    function_called = threading.Event()

    def test_function():
        nonlocal function_calls
        function_calls += 1
        if function_calls >= 2:
            function_called.set()

    periodic_background_function = PeriodicBackgroundFunction(
        test_function, shutdown_event, interval_seconds=0, jitter_seconds=0
    )

    periodic_background_function.start()

    function_called.wait()
    shutdown_event.set()

    periodic_background_function.join()

    assert function_calls >= 2, "Function should have been called at least twice"


def test_periodic_background_function_runs_after_exception(feature_flags_context):
    """Test that the periodic background function continues after an exception."""

    shutdown_event = threading.Event()

    function_calls = 0
    function_called = threading.Event()

    def test_function():
        nonlocal function_calls
        function_calls += 1
        if function_calls > 1:
            function_called.set()

        raise Exception("Test exception")

    periodic_background_function = PeriodicBackgroundFunction(
        test_function, shutdown_event, interval_seconds=0, jitter_seconds=0
    )

    periodic_background_function.start()

    function_called.wait(timeout=5)

    shutdown_event.set()

    periodic_background_function.join()

    assert function_calls >= 2, "Function should have been called at least twice"


def test_periodic_background_function_interval(feature_flags_context):
    """Test that the periodic downloader checks at the expected interval."""

    shutdown_event = threading.Event()

    periodic_background_function = PeriodicBackgroundFunction(
        lambda: None, shutdown_event
    )

    # Mock the wait method to verify the timeout value
    with patch.object(shutdown_event, "wait") as mock_wait:
        # Set up mock to return False (not shutdown) and then True (shutdown)
        mock_wait.side_effect = [False, True]

        periodic_background_function.start()

        periodic_background_function.join()

        # Verify that wait was called with a timeout close to 2 minutes (120 seconds)
        # We allow for the random offset of ±10 seconds
        mock_wait.assert_called_with(timeout=pytest.approx(120, abs=10))

    shutdown_event.set()


def test_is_cjk_character():
    """Test the is_cjk_character function."""
    # Chinese characters
    assert (
        is_cjk_character("中") is True
    ), "Chinese character '中' should be identified as CJK"
    assert (
        is_cjk_character("国") is True
    ), "Chinese character '国' should be identified as CJK"

    # Japanese characters
    assert (
        is_cjk_character("あ") is True
    ), "Hiragana character 'あ' should be identified as CJK"
    assert (
        is_cjk_character("ア") is True
    ), "Katakana character 'ア' should be identified as CJK"
    assert (
        is_cjk_character("日") is True
    ), "Kanji character '日' should be identified as CJK"

    # Korean characters
    assert (
        is_cjk_character("한") is True
    ), "Hangul character '한' should be identified as CJK"
    assert (
        is_cjk_character("ᄀ") is True
    ), "Jamo character 'ᄀ' should be identified as CJK"

    # Non-CJK characters
    assert (
        is_cjk_character("a") is False
    ), "Latin character 'a' should not be identified as CJK"
    assert is_cjk_character("1") is False, "Digit '1' should not be identified as CJK"
    assert is_cjk_character("!") is False, "Symbol '!' should not be identified as CJK"
    assert (
        is_cjk_character("é") is False
    ), "Latin with accent 'é' should not be identified as CJK"
    assert (
        is_cjk_character("Я") is False
    ), "Cyrillic character 'Я' should not be identified as CJK"
    assert (
        is_cjk_character("ก") is False
    ), "Thai character 'ก' should not be identified as CJK"
    assert (
        is_cjk_character("ا") is False
    ), "Arabic character 'ا' should not be identified as CJK"


def test_count_cjk_characters(mock_request):
    """Test the count_cjk_characters function."""
    # Empty request
    mock_request.prefix = ""
    mock_request.selected_code = ""
    mock_request.suffix = ""
    mock_request.message = ""
    mock_request.path = ""
    mock_request.user_guidelines = ""
    mock_request.workspace_guidelines = ""
    mock_request.chat_history = []

    count, ratio = count_cjk_characters(mock_request)
    assert count == 0, "Empty request should have 0 CJK characters"
    assert ratio == 0.0, "Empty request should have 0.0 CJK ratio"

    # No CJK characters
    mock_request.prefix = "Hello, "
    mock_request.selected_code = "world"
    mock_request.suffix = "!"

    count, ratio = count_cjk_characters(mock_request)
    assert count == 0, "Request with no CJK characters should have count 0"
    assert ratio == 0.0, "Request with no CJK characters should have ratio 0.0"

    # Mixed content
    mock_request.prefix = "Hello "
    mock_request.selected_code = "你好"  # 你好
    mock_request.suffix = " World"

    count, ratio = count_cjk_characters(mock_request)
    assert count == 2, "Request with 2 CJK characters should have count 2"
    assert (
        ratio == 2 / 14
    ), "Request with 2 CJK characters out of 14 total should have ratio 2/14"

    # All CJK characters
    mock_request.prefix = "中国"  # 中国
    mock_request.selected_code = "日本"  # 日本
    mock_request.suffix = "한국"  # 한국

    count, ratio = count_cjk_characters(mock_request)
    assert count == 6, "Request with all CJK characters should have count 6"
    assert ratio == 1.0, "Request with all CJK characters should have ratio 1.0"


def test_allow_request_with_cjk_count(mock_request, feature_flags_context):
    """Test allow_request function with CJK character count detection."""
    # Set the max CJK count to 5 and enable blocking
    feature_flags_context.set_flag(_MAX_CJK_CHAR_COUNT, 5)
    feature_flags_context.set_flag(_BLOCK_HIGH_CJK_COUNT, True)

    # Configure mock request with low CJK count
    mock_request.prefix = "Hello "
    mock_request.selected_code = "print('Hello')"
    mock_request.suffix = " World"
    mock_request.message = "This is a test with 你好 characters"
    mock_request.path = "test.py"
    mock_request.chat_history = []
    mock_request.user_guidelines = ""
    mock_request.workspace_guidelines = ""
    mock_request.is_suspicious = False

    # Test with low CJK count (should be allowed)
    ban_rules = BanRules()
    assert (
        allow_request(mock_request, "session", ban_rules) is True
    ), "Request with low CJK count should be allowed"

    # Configure mock request with high CJK count
    mock_request.prefix = "你好世界"  # 你好世界
    mock_request.selected_code = "print('你好世界')"  # print('你好世界')
    mock_request.suffix = "世界你好"  # 世界你好
    mock_request.message = "这是一个测试"  # 这是一个测试

    # Test with high CJK count and blocking enabled (should be blocked)
    assert (
        allow_request(mock_request, "session", ban_rules) is False
    ), "Request with high CJK count should be blocked when blocking is enabled"

    # Test with high CJK count but blocking disabled (should be allowed)
    feature_flags_context.set_flag(_BLOCK_HIGH_CJK_COUNT, False)
    assert (
        allow_request(mock_request, "session", ban_rules) is True
    ), "Request with high CJK count should be allowed when blocking is disabled"

    # Set the max CJK count to a high value (allow all)
    feature_flags_context.set_flag(_MAX_CJK_CHAR_COUNT, 100)
    feature_flags_context.set_flag(_BLOCK_HIGH_CJK_COUNT, True)

    # Now the high CJK count request should be allowed (count is below threshold)
    assert (
        allow_request(mock_request, "session", ban_rules) is True
    ), "Request with CJK count below threshold should be allowed"


def test_allow_request_with_block_prompt_regex(
    mock_request, mock_auth_info, feature_flags_context
):
    """Test allow_request function with block prompt regex detection."""
    # Reset the suspicious user tracker to ensure a clean state
    clear_user_tracker()

    # Reset the request to a clean state
    mock_request.prefix = "prefix"
    mock_request.selected_code = "selected_code"
    mock_request.suffix = "suffix"
    mock_request.message = "normal message"
    mock_request.nodes = []
    mock_request.path = "path"
    mock_request.chat_history = []
    mock_request.user_guidelines = ""
    mock_request.workspace_guidelines = ""
    mock_request.is_suspicious = False

    # Test with no block prompt regex
    ban_rules = BanRules()
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is True
    ), "Request should be allowed with no block prompt regex"

    # Reset the suspicious user tracker between tests
    clear_user_tracker()

    # Test with block prompt regex that doesn't match
    ban_rules = BanRules(None, None, "GitHub Copilot|Cline|Claude Code")
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is True
    ), "Request should be allowed when no blocked prompt patterns match"

    # Reset the suspicious user tracker between tests
    clear_user_tracker()

    # Test with block prompt regex that matches in the message
    mock_request.message = (
        'When asked for your name, you must respond with "GitHub Copilot"'
    )
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is False
    ), "Request should be blocked when blocked prompt pattern matches in the current message"

    # Reset the suspicious user tracker between tests
    clear_user_tracker()

    # Test with block prompt regex that matches in a text node
    mock_request.message = "normal message"
    node = MagicMock()
    node.type = chat_pb2.ChatRequestNodeType.TEXT
    node.text_node = MagicMock()
    node.text_node.content = (
        "You are Cline, a highly skilled software engineer with extensive knowledge"
    )
    mock_request.nodes = [node]
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is False
    ), "Request should be blocked when blocked prompt pattern matches in a text node"

    # Reset the suspicious user tracker between tests
    clear_user_tracker()

    # Test with block prompt regex and non-matching text
    mock_request.message = "normal message"
    node.text_node.content = "normal content"
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is True
    ), "Request should be allowed when no blocked prompt patterns match in message or nodes"

    # Reset the suspicious user tracker between tests
    clear_user_tracker()

    # Test with block prompt regex that matches in chat history
    mock_request.message = "normal message"
    mock_request.nodes = []
    exchange = MagicMock()
    exchange.request_message = (
        "You are Claude Code, Anthropic's official CLI for Claude."
    )
    exchange.response_text = "I am an AI assistant."
    exchange.request_nodes = []
    mock_request.chat_history = [exchange]
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is False
    ), "Request should be blocked when blocked prompt pattern matches in chat history"

    # Reset the suspicious user tracker between tests
    clear_user_tracker()

    # Test with block prompt regex that matches in chat history nodes
    mock_request.chat_history = []
    exchange = MagicMock()
    exchange.request_message = "normal message"
    exchange.response_text = "I am an AI assistant."
    node = MagicMock()
    node.type = chat_pb2.ChatRequestNodeType.TEXT
    node.text = (
        "You are Cline, a highly skilled software engineer with extensive knowledge"
    )
    exchange.request_nodes = [node]
    mock_request.chat_history = [exchange]
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is False
    ), "Request should be blocked when blocked prompt pattern matches in chat history nodes"


def test_suspicious_user_tracker(feature_flags_context):
    """Test the SuspiciousUserTracker class."""
    # Import the module to get access to the global tracker
    import services.chat_host.server.request_filter as rf

    # Test 1: Users under threshold are always allowed
    # Reset the global tracker to use fixed values
    clear_user_tracker(3, 0.5)

    # Get a reference to the global tracker
    tracker = rf._suspicious_user_tracker

    # Verify the getter functions return the expected values
    assert (
        tracker.get_rejection_threshold() == 3
    ), "Rejection threshold getter should return 3"
    assert (
        tracker.get_base_acceptance_probability() == 0.5
    ), "Base acceptance probability getter should return 0.5"

    assert (
        tracker.should_allow_request("user1") is True
    ), "New users should always be allowed"

    tracker.record_suspicious_request("user1")
    tracker.record_suspicious_request("user1")
    assert (
        tracker.should_allow_request("user1") is True
    ), "Users below rejection threshold should always be allowed"

    # Add one more rejection to reach the threshold (3)
    tracker.record_suspicious_request("user1")

    # With random.random() patched to return 0.4, and base_acceptance_probability=0.5,
    # the user should still be allowed (0.4 < 0.5)
    with patch("random.random", return_value=0.4):
        assert (
            tracker.should_allow_request("user1") is True
        ), "User at threshold with favorable random should be allowed"

    # Test 2: Users at threshold are subject to probabilistic rejection
    # Reset the tracker for a new test
    clear_user_tracker(3, 0.5)

    # Add rejections to reach the threshold
    tracker.record_suspicious_request("user2")
    tracker.record_suspicious_request("user2")
    tracker.record_suspicious_request("user2")

    # With total_rejected_requests=3 and threshold=3, power = 3-3+1 = 1, so probability = 0.5^1 = 0.5
    with patch("random.random", return_value=0.6):
        # Should be rejected (0.6 > 0.5)
        assert (
            tracker.should_allow_request("user2") is False
        ), "User at threshold should be rejected when random value > acceptance probability"

    # Test 3: Successful requests increase penalty for users over threshold
    # Reset the tracker for a new test
    clear_user_tracker(3, 0.5)

    # Add rejections to reach the threshold
    tracker.record_suspicious_request("user3")
    tracker.record_suspicious_request("user3")
    tracker.record_suspicious_request("user3")  # At threshold now

    # Simulate a successful request
    with patch("random.random", return_value=0.01):  # Will be allowed
        assert (
            tracker.should_allow_request("user3") is True
        ), "User at threshold should be allowed when random value < acceptance probability"

    # No need to record successful requests anymore

    # After a successful request, total_rejected_requests is still 3, so power = 3-3+1 = 1, probability = 0.5^1 = 0.5
    with patch("random.random", return_value=0.6):
        # Should be rejected (0.6 > 0.5)
        assert (
            tracker.should_allow_request("user3") is False
        ), "User at threshold should still be rejected after a successful request when random value > acceptance probability"

    # Test 4: Epoch transition
    # Reset the tracker for a new test
    clear_user_tracker(3, 0.95)

    # Add rejections to reach the threshold
    tracker.record_suspicious_request("user4")
    tracker.record_suspicious_request("user4")
    tracker.record_suspicious_request("user4")

    # Should be at threshold
    assert (
        tracker.user_stats["user4"].current_epoch_rejected_requests == 3
    ), "User should have 3 rejected requests in current epoch"

    # Simulate epoch change
    current_epoch = tracker.current_epoch
    with patch.object(tracker, "_get_current_epoch", return_value=current_epoch + 1):
        # Check user after epoch change
        tracker.should_allow_request("user4")

        # Current should be reset, previous should have the old value
        assert (
            tracker.user_stats["user4"].current_epoch_rejected_requests == 0
        ), "Current epoch rejected requests should be reset after epoch change"
        assert (
            tracker.user_stats["user4"].previous_epoch_rejected_requests == 3
        ), "Previous epoch rejected requests should be updated after epoch change"

        # Still at threshold when combining current + previous
        # With total_rejected_requests=3 and threshold=3, power = 3-3+1 = 1, so probability = 0.95^1 = 0.95
        with patch("random.random", return_value=0.96):
            # Should be rejected (0.96 > 0.95)
            assert (
                tracker.should_allow_request("user4") is False
            ), "User should be rejected after epoch change when random value > acceptance probability"


def test_regexp_definition_parser():
    """Test the regexp_definition_parser function."""
    # Test successful download with all three patterns
    content = "test_pattern|some_other_text\nstrict_pattern|strict_text\nblock_prompt_pattern|prompt_text"
    regular_regexp, strict_regexp, block_prompt_regexp = regexp_definition_parser(
        content
    )
    assert regular_regexp == "test_pattern|some_other_text"
    assert strict_regexp == "strict_pattern|strict_text"
    assert block_prompt_regexp == "block_prompt_pattern|prompt_text"

    # Test successful download with only regular and strict patterns
    content = "test_pattern|some_other_text\nstrict_pattern|strict_text"
    regular_regexp, strict_regexp, block_prompt_regexp = regexp_definition_parser(
        content
    )
    assert regular_regexp == "test_pattern|some_other_text"
    assert strict_regexp == "strict_pattern|strict_text"
    assert block_prompt_regexp is None

    # Test successful download with only regular pattern
    content = "test_pattern|some_other_text"
    regular_regexp, strict_regexp, block_prompt_regexp = regexp_definition_parser(
        content
    )
    assert regular_regexp == "test_pattern|some_other_text"
    assert strict_regexp is None
    assert block_prompt_regexp is None

    # Test empty content
    content = ""
    regular_regexp, strict_regexp, block_prompt_regexp = regexp_definition_parser(
        content
    )
    assert regular_regexp is None
    assert strict_regexp is None
    assert block_prompt_regexp is None

    # Test whitespace
    content = "  \n"
    regular_regexp, strict_regexp, block_prompt_regexp = regexp_definition_parser(
        content
    )
    assert regular_regexp is None
    assert strict_regexp is None
    assert block_prompt_regexp is None


def test_session_tracking(feature_flags_context):
    """Test the session tracking functionality."""
    # Create a mock request
    request = MagicMock(spec=chat_pb2.ChatRequest)
    request.prefix = "prefix"
    request.selected_code = "selected_code"
    request.suffix = "suffix"
    request.message = "message"
    request.nodes = []
    request.path = "path"
    request.chat_history = []
    request.user_guidelines = ""
    request.workspace_guidelines = ""
    request.is_suspicious = False

    # Create ban rules
    ban_rules = BanRules()

    # Test with session tracking disabled (default)
    # Create auth_info objects with different user IDs
    auth_info1 = MagicMock()
    auth_info1.opaque_user_id = "test_user"
    auth_info1.tenant_name = "test_tenant"

    # Set different session IDs for each request
    assert allow_request(request, "session1", ban_rules, auth_info1) is True
    assert allow_request(request, "session2", ban_rules, auth_info1) is True
    assert allow_request(request, "session3", ban_rules, auth_info1) is True

    # Enable session tracking with a limit of 2 sessions
    feature_flags_context.set_flag(_BAN_MAX_SESSIONS_PER_USER, 2)

    # Create a new auth_info for test_user2
    auth_info2 = MagicMock()
    auth_info2.opaque_user_id = "test_user2"
    auth_info2.tenant_name = "test_tenant"

    # First two sessions should be allowed
    assert allow_request(request, "session1", ban_rules, auth_info2) is True

    assert allow_request(request, "session2", ban_rules, auth_info2) is True

    # Test session tracking with track_session
    tracker = SuspiciousUserTracker()

    # Track sessions
    tracker.track_session("test_user3", "session1")
    tracker.track_session("test_user3", "session2")

    # Verify sessions are tracked
    assert len(tracker.user_stats["test_user3"].current_epoch_sessions) == 2
    assert "session1" in tracker.user_stats["test_user3"].current_epoch_sessions
    assert "session2" in tracker.user_stats["test_user3"].current_epoch_sessions

    # Simulate epoch change
    current_epoch = tracker.current_epoch
    with patch.object(tracker, "_get_current_epoch", return_value=current_epoch + 1):
        # Track a new session after epoch change
        tracker.track_session("test_user3", "session3")

        # Current should have the new session, previous should have the old sessions
        assert len(tracker.user_stats["test_user3"].current_epoch_sessions) == 1
        assert "session3" in tracker.user_stats["test_user3"].current_epoch_sessions
        assert len(tracker.user_stats["test_user3"].previous_epoch_sessions) == 2
        assert "session1" in tracker.user_stats["test_user3"].previous_epoch_sessions
        assert "session2" in tracker.user_stats["test_user3"].previous_epoch_sessions

    # Create a new tracker for this test
    tracker = SuspiciousUserTracker()

    # Add sessions to current epoch
    tracker.track_session("test_user4", "session1")
    tracker.track_session("test_user4", "session2")
    tracker.track_session("test_user4", "session3")

    # Simulate epoch change
    current_epoch = tracker.current_epoch
    with patch.object(tracker, "_get_current_epoch", return_value=current_epoch + 1):
        # Add fewer sessions to new current epoch
        tracker.track_session("test_user4", "session4")

        # Test the max() function in allow_request
        with patch(
            "services.chat_host.server.request_filter._suspicious_user_tracker.record_suspicious_request"
        ):
            # Set up the feature flag to return 2
            feature_flags_context.set_flag(_BAN_MAX_SESSIONS_PER_USER, 2)

            # Create a mock context that returns appropriate values for feature flags
            mock_context = MagicMock()

            # Create a lookup function that returns different values based on the flag name
            def mock_lookup(flag_name, default):
                if flag_name == "chat_server_ban_max_sessions_per_user":
                    return 2
                elif flag_name == "chat_server_block_high_cjk_count":
                    return False  # Return a boolean for the BoolFlag
                else:
                    return default

            mock_context.lookup.side_effect = mock_lookup

            # Create auth_info for test_user4
            auth_info4 = MagicMock()
            auth_info4.opaque_user_id = "test_user4"
            auth_info4.tenant_name = "test_tenant"

            # This test has been moved to a separate test function: test_session_limit_tracking


def test_session_limit_tracking(feature_flags_context):
    """Test the session limit tracking functionality.

    This test verifies that users exceeding the maximum number of allowed sessions
    are properly tracked as suspicious.
    """
    # Create a mock request
    request = MagicMock(spec=chat_pb2.ChatRequest)
    request.prefix = "prefix"
    request.selected_code = "selected_code"
    request.suffix = "suffix"
    request.message = "message"
    request.nodes = []
    request.path = "path"
    request.chat_history = []
    request.user_guidelines = ""
    request.workspace_guidelines = ""
    request.is_suspicious = False

    # Create ban rules
    ban_rules = BanRules()

    # Reset the global tracker to ensure a clean state
    clear_user_tracker()

    # Create auth_info for test_user5
    auth_info5 = MagicMock()
    auth_info5.opaque_user_id = "test_user5"
    auth_info5.tenant_name = "test_tenant"

    # Set the feature flag to limit sessions to 2
    feature_flags_context.set_flag(_BAN_MAX_SESSIONS_PER_USER, 2)

    # Track first two sessions - these should be allowed
    assert allow_request(request, "session1", ban_rules, auth_info5) is True

    assert allow_request(request, "session2", ban_rules, auth_info5) is True

    # Third session should trigger suspicious request tracking
    assert allow_request(request, "session3", ban_rules, auth_info5) is True

    # Import the module to get access to the global tracker
    import services.chat_host.server.request_filter as rf

    # Verify the sessions were actually tracked
    assert (
        len(rf._suspicious_user_tracker.user_stats["test_user5"].current_epoch_sessions)
        == 3
    )

    # Verify that all sessions were tracked correctly
    assert (
        "session1"
        in rf._suspicious_user_tracker.user_stats["test_user5"].current_epoch_sessions
    )
    assert (
        "session2"
        in rf._suspicious_user_tracker.user_stats["test_user5"].current_epoch_sessions
    )
    assert (
        "session3"
        in rf._suspicious_user_tracker.user_stats["test_user5"].current_epoch_sessions
    )


def test_get_domain_from_email():
    """Test the get_domain_from_email function."""
    # Test valid email
    assert get_domain_from_email("<EMAIL>") == "example.com"

    # Test email with subdomain
    assert get_domain_from_email("<EMAIL>") == "sub.example.com"

    # Test email with plus addressing
    assert get_domain_from_email("<EMAIL>") == "example.com"

    # Test invalid email (no @)
    assert get_domain_from_email("user.example.com") is None

    # Test empty email
    assert get_domain_from_email("") is None

    # Test None email
    assert get_domain_from_email(None) is None


def test_parse_domains_list():
    """Test the parse_domains_list function."""
    # Test newline-separated list
    assert parse_domains_list("example.com\nblocked.com") == [
        "example.com",
        "blocked.com",
    ]

    # Test with whitespace
    assert parse_domains_list(" example.com \n blocked.com ") == [
        "example.com",
        "blocked.com",
    ]

    # Test empty string
    assert parse_domains_list("") == []

    # Test None
    assert parse_domains_list(None) == []

    # Test single domain
    assert parse_domains_list("example.com") == ["example.com"]

    # Test with empty entries
    assert parse_domains_list("example.com\n\nblocked.com\n") == [
        "example.com",
        "blocked.com",
    ]


def test_blocked_domains_cache():
    """Test the BlockedDomainsCache class."""
    # Create a new cache instance for testing
    cache = BlockedDomainsCache()

    # Test with empty string
    assert cache.update("") == [], "Empty string should return empty list"

    # Test with valid newline-separated list
    domains = cache.update("\nexample.com\nblocked.com\n")
    assert domains == [
        "example.com",
        "blocked.com",
    ], "Newline-separated list should be parsed correctly"

    # Test that the cache works (same string doesn't trigger re-parsing)
    with patch(
        "services.chat_host.server.request_filter.parse_domains_list"
    ) as mock_parse:
        # First call with a new string should parse
        cache.update("different.com\nother.com\n")
        mock_parse.assert_called_once()

        # Reset the mock to clear the call count
        mock_parse.reset_mock()

        # Second call with the same string should not parse
        cache.update("different.com\nother.com\n")
        mock_parse.assert_not_called()

    # Test that the cache is updated when the string changes
    with patch(
        "services.chat_host.server.request_filter.parse_domains_list",
        return_value=["new.com"],
    ) as mock_parse:
        domains = cache.update("new.com")
        mock_parse.assert_called_once()
        assert domains == ["new.com"], "Cache should return the new domains"

    # Test the contains method
    assert (
        cache.contains("new.com") is True
    ), "contains() should return True for domains in the list"
    assert (
        cache.contains("not-in-list.com") is False
    ), "contains() should return False for domains not in the list"
    assert cache.contains(None) is False, "contains() should return False for None"
    assert (
        cache.contains("") is False
    ), "contains() should return False for empty string"

    # Test reset
    cache.reset()
    assert cache.contains("new.com") is False, "reset() should clear the domains list"

    # Test with None
    assert cache.update(None) == [], "None should be treated as empty string"

    # Test with any string (all strings are valid now)
    with patch(
        "services.chat_host.server.request_filter.parse_domains_list", return_value=[]
    ) as mock_parse:
        domains = cache.update("any string")
        mock_parse.assert_called_once()


def test_allow_request_with_blocked_domains(
    mock_request, mock_auth_info, feature_flags_context
):
    """Test allow_request function with blocked domains."""
    # Reset the suspicious user tracker and blocked domains cache to ensure a clean state
    clear_user_tracker()
    clear_blocked_domains_cache()

    # Create ban rules
    ban_rules = BanRules()

    # Test with no blocked domains
    feature_flags_context.set_flag(_BLOCKED_DOMAINS, "")
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is True
    ), "Request should be allowed when no domains are blocked"

    # Test with blocked domains that don't match the user's domain
    feature_flags_context.set_flag(_BLOCKED_DOMAINS, "blocked.com\nspam.com")
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is True
    ), "Request should be allowed when user's domain is not in the blocked list"

    # Test with blocked domains that match the user's domain
    feature_flags_context.set_flag(_BLOCKED_DOMAINS, "example.com\nspam.com")
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is False
    ), "Request should be blocked when user's domain is in the blocked list"

    # Test with user that has no email
    mock_auth_info.user_email = None
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is True
    ), "Request should be allowed when user has no email"

    # Test with any string (all strings are valid now)
    feature_flags_context.set_flag(_BLOCKED_DOMAINS, "any\nstring")
    # Reset the email
    mock_email = MagicMock()
    mock_email.get_secret_value.return_value = "<EMAIL>"
    mock_auth_info.user_email = mock_email
    assert (
        allow_request(mock_request, "session", ban_rules, mock_auth_info) is True
    ), "Request should be allowed when user's domain is not in the blocked list"

    # Test that the cache is working (no re-parsing of the same JSON)
    with patch(
        "services.chat_host.server.request_filter.parse_domains_list"
    ) as mock_parse:
        # First call to set up the cache
        feature_flags_context.set_flag(_BLOCKED_DOMAINS, "test.domain\nexample.com")
        allow_request(mock_request, "session", ban_rules, mock_auth_info)
        mock_parse.assert_called_once()

        # Reset the mock
        mock_parse.reset_mock()

        # Second call with the same string should not parse
        allow_request(mock_request, "session", ban_rules, mock_auth_info)
        mock_parse.assert_not_called()

    # Test that the contains method is being used
    with patch.object(
        _blocked_domains_cache, "contains", return_value=True
    ) as mock_contains:
        result = allow_request(mock_request, "session", ban_rules, mock_auth_info)
        mock_contains.assert_called_once()
        assert result is False, "Request should be blocked when contains() returns True"


def test_prefix_and_suffix_blocking(feature_flags_context):
    """Test the prefix and suffix blocking functionality."""
    banned_phrase = "Wankel rotary engine"

    request = create_chat_request(prefix=banned_phrase * 20)
    auth_info = create_auth_info()

    for (
        use_prefix_and_suffix,
        strict_uses_prefix_and_suffix,
        ban_rules,
        allow,
    ) in [
        # None means don't care
        (False, None, BanRules(banned_phrase), True),
        (False, None, BanRules(None, None, banned_phrase), True),
        (True, None, BanRules(banned_phrase), False),
        (True, None, BanRules(None, None, banned_phrase), False),
        (None, True, BanRules(None, banned_phrase), False),
        (None, False, BanRules(None, banned_phrase), True),
    ]:
        if use_prefix_and_suffix is not None:
            feature_flags_context.set_flag(
                _USE_PREFIX_AND_SUFFIX, use_prefix_and_suffix
            )

        if strict_uses_prefix_and_suffix is not None:
            feature_flags_context.set_flag(
                _STRICT_USES_PREFIX_AND_SUFFIX, strict_uses_prefix_and_suffix
            )

        # Set parameters such that if a request is suspicious, block it
        clear_user_tracker(1, 0.0)

        assert (
            allow_request(request, "session", ban_rules, auth_info) is allow
        ), f"Request {'should not' if allow else 'should'} be blocked with use_prefix_and_suffix={use_prefix_and_suffix}, strict_uses_prefix_and_suffix={strict_uses_prefix_and_suffix}, ban_rules={ban_rules}"


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])

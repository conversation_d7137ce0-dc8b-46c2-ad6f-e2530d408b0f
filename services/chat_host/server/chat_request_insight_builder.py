"""Helper to build a request insight events during a chat request."""

from typing import Sequence

from base.prompt_format.common import (
    ChatRequestN<PERSON>,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    RequestMessage,
    ResponseMessage,
)
from base.prompt_format_chat.prompt_formatter import (
    StructuredChatPromptOutput,
    TokenizedChatPromptOutput,
)
from base.third_party_clients.third_party_model_client import PromptCacheUsage
from base.tokenizers import Tokenizer
from services.chat_host import chat_pb2
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher


# TODO: due to python pass-by-assignment we may want to inline the Tokenization field setting
def _get_tokenization_proto_from_token_prompt(
    tokenizer: Tokenizer,
    token_ids: Sequence[int],
    log_probs: Sequence[float] | None,
) -> request_insight_pb2.Tokenization:
    text, offsets = tokenizer.detokenize_with_offsets(token_ids)
    return request_insight_pb2.Tokenization(
        token_ids=token_ids,
        text=text,
        offsets=offsets,
        log_probs=log_probs or [],
    )


def _stringify_structured_chat_prompt_output(
    structured_prompt: StructuredChatPromptOutput,
):
    prompt = ""
    prompt += f"""\
{'-' * 40}
system_prompt:
{'-' * 40}
{structured_prompt.system_prompt}

"""

    prompt += f"""\
{'-' * 40}
current message:
{'-' * 40}
{_stringify_request_message(structured_prompt.message)}

{'-' * 40}
model response prefill:
{'-' * 40}
{structured_prompt.prefill}

{'-' * 40}
chat history (from most recent to least recent):
{'-' * 40}
"""

    for exchange in structured_prompt.chat_history:
        prompt += f"""\
    {'-' * 40}
    user:
    {'-' * 40}
    {_stringify_request_message(exchange.request_message)}

    {'-' * 40}
    assistant:
    {'-' * 40}
    {_stringify_response_message(exchange.response_text)}

"""

    promptlines = prompt.splitlines(keepends=True)
    # limit to 500k characters (~2 bytes / character => ~1MB max) to avoid
    # bigtable/grpc issues with large messages,
    # and to ease readability.
    prompt = prompt[:500_000]

    # Create sorted list of unique paths of chunks
    filepaths_in_prompt = "\n    ".join(
        sorted(
            set(
                [
                    chunk.path
                    for chunk in structured_prompt.retrieved_chunks_in_prompt
                    if not chunk.path.startswith("docset://")
                ]
            )
        )
    )

    # Prepend metadata to start of "prompt"
    prompt = (
        f"""\
{'-' * 40}
Metadata:
    Chunks in prompt: {len(list(structured_prompt.retrieved_chunks_in_prompt))}
    Lines in prompt: {len(promptlines)}

Paths in prompt:
    {filepaths_in_prompt}

{'-' * 40}
"""
        + prompt
    )

    return prompt


def _stringify_request_message(request_message: RequestMessage) -> str:
    if isinstance(request_message, str):
        return request_message
    return "\n".join([_stringify_request_node(node) for node in request_message])


def _stringify_response_message(response_message: ResponseMessage) -> str:
    if isinstance(response_message, str):
        return response_message
    return "\n".join([_stringify_response_node(node) for node in response_message])


def _stringify_request_node(node: ChatRequestNode) -> str:
    if node.type == ChatRequestNodeType.TEXT:
        assert node.text_node is not None
        return node.text_node.content
    if node.type == ChatRequestNodeType.TOOL_RESULT:
        assert node.tool_result_node is not None
        return (
            "[Tool Result]\n"
            + f"ToolUseId: {node.tool_result_node.tool_use_id}\n"
            + f"Content: {node.tool_result_node.content}\n"
            + f"IsError: {node.tool_result_node.is_error}\n"
        )
    return ""


def _stringify_response_node(node: ChatResultNode) -> str:
    if node.type == ChatResultNodeType.RAW_RESPONSE:
        return node.content
    if node.type == ChatResultNodeType.TOOL_USE:
        assert node.tool_use is not None
        return (
            "[Tool Use]\n"
            + f"ToolUseId: {node.tool_use.tool_use_id}\n"
            + f"Name: {node.tool_use.name}\n"
            + f"Input: {node.tool_use.input}\n"
        )
    return ""


def _get_tokenization_proto_from_structured_prompt(
    prompt: StructuredChatPromptOutput,
) -> request_insight_pb2.Tokenization:
    return request_insight_pb2.Tokenization(
        token_ids=[0],
        text=_stringify_structured_chat_prompt_output(prompt),
        offsets=[0],
        log_probs=[1.0],
    )


def _get_tokenization_proto_from_str(
    text: str,
) -> request_insight_pb2.Tokenization:
    return request_insight_pb2.Tokenization(
        token_ids=[0],
        text=text,
        offsets=[0],
        log_probs=[1.0],
    )


class ChatRequestInsightBuilder:
    """Class to log a request insight events during a chat request."""

    def __init__(self, ri_publisher: request_insight_publisher.RequestInsightPublisher):
        self.ri_publisher = ri_publisher

    def record_request(
        self,
        prompt_output: TokenizedChatPromptOutput | StructuredChatPromptOutput,
        tokenizer: Tokenizer | None,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        external_source_ids: Sequence[str] | None,
    ):
        """Record a chat request event."""
        request_event = request_insight_publisher.new_event()
        ri_request = request_insight_pb2.RIChatRequest(
            request=request, request_source=request_context.request_source
        )
        if (
            isinstance(prompt_output, TokenizedChatPromptOutput)
            and tokenizer is not None
        ):
            ri_request.tokenization.CopyFrom(
                _get_tokenization_proto_from_token_prompt(
                    tokenizer, prompt_output.tokens, None
                )
            )
        elif isinstance(prompt_output, StructuredChatPromptOutput):
            ri_request.tokenization.CopyFrom(
                _get_tokenization_proto_from_structured_prompt(prompt_output)
            )

        chunks = [
            request_insight_pb2.RetrievalChunk(
                text=chunk.text,
                path=chunk.path,
                char_offset=chunk.char_start,
                char_end=chunk.char_end,
                blob_name=chunk.blob_name or "",
                chunk_index=idx,
                origin=chunk.origin,
            )
            for idx, chunk in enumerate(prompt_output.retrieved_chunks_in_prompt)
        ]
        ri_request.retrieved_chunks.extend(chunks)
        # The final logging
        request_event.chat_host_request.MergeFrom(ri_request)
        if external_source_ids is not None:
            request_event.chat_host_request.external_source_ids.extend(
                external_source_ids
            )
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [request_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_response(
        self,
        response: chat_pb2.ChatResponse,
        tokenizer: Tokenizer | None,
        truncated_output: Sequence[int] | str | None,
        truncated_log_probs: Sequence[float] | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
        error_message: str | None = None,
    ):
        response_event = request_insight_publisher.new_event()
        ri_response = request_insight_pb2.RIChatResponse(
            response=response, error_message=error_message
        )

        # We can't call isinstance on the generic type Sequence[int], so this is a workaround for the typing.
        if (
            tokenizer is not None
            and not isinstance(truncated_output, str)
            and truncated_output is not None
        ):
            if truncated_log_probs is None:
                truncated_log_probs = [0.0] * len(truncated_output)
            ri_response.tokenization.CopyFrom(
                _get_tokenization_proto_from_token_prompt(
                    tokenizer,
                    truncated_output,
                    truncated_log_probs,  # type: ignore
                )
            )
        elif isinstance(truncated_output, str):
            ri_response.tokenization.CopyFrom(
                _get_tokenization_proto_from_str(truncated_output)
            )

        response_event.chat_host_response.MergeFrom(ri_response)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_postprocess_response(
        self,
        tokens: Sequence[int] | None,
        log_probs: Sequence[float] | None,
        tokenizer: Tokenizer,
        error_message: str | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        response_event = request_insight_publisher.new_event()
        if tokens:
            response_event.postprocess_response.tokenization.CopyFrom(
                _get_tokenization_proto_from_token_prompt(tokenizer, tokens, log_probs)
            )
        if error_message:
            response_event.postprocess_response.error_message = error_message
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

    def record_prompt_cache_usage(
        self,
        prompt_cache_usage: PromptCacheUsage,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        response_event = request_insight_publisher.new_event()
        prompt_cache_usage_proto = request_insight_pb2.PromptCacheUsage(
            input_tokens=prompt_cache_usage.input_tokens,
            cache_read_input_tokens=prompt_cache_usage.cache_read_input_tokens,
            cache_creation_input_tokens=prompt_cache_usage.cache_creation_input_tokens,
            text_input_tokens=prompt_cache_usage.text_input_tokens,
            tool_input_tokens=prompt_cache_usage.tool_input_tokens,
            text_output_tokens=prompt_cache_usage.text_output_tokens,
            tool_output_tokens=prompt_cache_usage.tool_output_tokens,
            model_caller=prompt_cache_usage.model_caller,
        )
        response_event.prompt_cache_usage.MergeFrom(prompt_cache_usage_proto)
        update_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [response_event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request)

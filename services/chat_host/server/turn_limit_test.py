"""Tests for turn_limit.py."""

import unittest
from unittest import mock

from services.chat_host import chat_pb2

from services.chat_host.server.handler import Chat<PERSON><PERSON>ult
from services.chat_host.server.turn_limit import (
    count_tool_result_nodes,
    check_turn_limit,
    _MAX_TOOL_TURNS_PER_USER_MESSAGE,
)


class TurnLimitTest(unittest.TestCase):
    """Tests for turn_limit.py."""

    def test_count_tool_result_nodes_empty_request(self):
        """Test counting tool result nodes with empty request."""
        request = chat_pb2.ChatRequest()
        self.assertEqual(count_tool_result_nodes(request), 0)

    def test_count_tool_result_nodes_no_nodes(self):
        """Test counting tool result nodes with history but no nodes."""
        request = chat_pb2.ChatRequest()
        request.chat_history.add()
        self.assertEqual(count_tool_result_nodes(request), 0)

    def test_count_tool_result_nodes_with_tool_results(self):
        """Test counting tool result nodes with tool results in current message."""
        request = chat_pb2.ChatRequest()

        # Add a tool result node to the current message
        node = request.nodes.add()
        node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        node.tool_result_node.tool_use_id = "tool_1"
        node.tool_result_node.content = "result_1"

        # Should count as 1 for the current message
        self.assertEqual(count_tool_result_nodes(request), 1)

    def test_count_tool_result_nodes_with_mixed_nodes_current(self):
        """Test counting tool result nodes with mixed node types in current message."""
        request = chat_pb2.ChatRequest()

        # Add a text node to the current message
        text_node = request.nodes.add()
        text_node.type = chat_pb2.ChatRequestNodeType.TEXT
        text_node.text_node.content = "Hello"

        # Add tool result nodes after the text node in the current message
        tool_node = request.nodes.add()
        tool_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        tool_node.tool_result_node.tool_use_id = "tool_1"
        tool_node.tool_result_node.content = "result_1"

        # Add a tool result exchange to history
        tool_exchange = request.chat_history.add()
        history_node = tool_exchange.request_nodes.add()
        history_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        history_node.tool_result_node.tool_use_id = "tool_2"
        history_node.tool_result_node.content = "result_2"

        # The count should be 2: 1 for the current message + 1 for the history exchange
        self.assertEqual(count_tool_result_nodes(request), 2)

    def test_count_tool_result_nodes_with_mixed_nodes_history(self):
        """Test counting tool result nodes with mixed node types in history."""
        request = chat_pb2.ChatRequest()

        # Add a tool result node to the current message
        current_node = request.nodes.add()
        current_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        current_node.tool_result_node.tool_use_id = "tool_current"
        current_node.tool_result_node.content = "result_current"

        # Add a tool result exchange first
        tool_exchange = request.chat_history.add()
        tool_node = tool_exchange.request_nodes.add()
        tool_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        tool_node.tool_result_node.tool_use_id = "tool_1"
        tool_node.tool_result_node.content = "result_1"

        # Add another tool result exchange
        tool_exchange2 = request.chat_history.add()
        tool_node2 = tool_exchange2.request_nodes.add()
        tool_node2.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        tool_node2.tool_result_node.tool_use_id = "tool_2"
        tool_node2.tool_result_node.content = "result_2"

        # Add a mixed exchange with tool result first, then text node
        mixed_exchange = request.chat_history.add()
        tool_node3 = mixed_exchange.request_nodes.add()
        tool_node3.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        tool_node3.tool_result_node.tool_use_id = "tool_3"
        tool_node3.tool_result_node.content = "result_3"

        text_node = mixed_exchange.request_nodes.add()
        text_node.type = chat_pb2.ChatRequestNodeType.TEXT
        text_node.text_node.content = "Hello"

        # The count should be 4: 1 for the current message + 3 for the history exchanges
        # (we no longer check for text nodes)
        self.assertEqual(count_tool_result_nodes(request), 4)

    def test_count_consecutive_tool_result_exchanges(self):
        """Test counting consecutive tool result exchanges."""
        request = chat_pb2.ChatRequest()

        # Add a tool result node to the current message
        current_node = request.nodes.add()
        current_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        current_node.tool_result_node.tool_use_id = "tool_current"
        current_node.tool_result_node.content = "result_current"

        # Add a text node exchange first (this should break the consecutive count)
        text_exchange = request.chat_history.add()
        text_node = text_exchange.request_nodes.add()
        text_node.type = chat_pb2.ChatRequestNodeType.TEXT
        text_node.text_node.content = "Hello"

        # Add a tool result exchange
        tool_exchange1 = request.chat_history.add()
        tool_node1 = tool_exchange1.request_nodes.add()
        tool_node1.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        tool_node1.tool_result_node.tool_use_id = "tool_1"
        tool_node1.tool_result_node.content = "result_1"

        # Add another tool result exchange
        tool_exchange2 = request.chat_history.add()
        tool_node2 = tool_exchange2.request_nodes.add()
        tool_node2.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        tool_node2.tool_result_node.tool_use_id = "tool_2"
        tool_node2.tool_result_node.content = "result_2"

        # Should count 3 consecutive tool result exchanges
        # 1 for the current message + 2 for the consecutive history exchanges
        # The text exchange breaks the consecutive count, so we only count from the most recent
        self.assertEqual(count_tool_result_nodes(request), 3)

    def test_check_turn_limit_under_limit(self):
        """Test checking turn limit when under the limit."""
        request = chat_pb2.ChatRequest()

        # Add a tool result node to the current message
        current_node = request.nodes.add()
        current_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        current_node.tool_result_node.tool_use_id = "tool_current"
        current_node.tool_result_node.content = "result_current"

        # Add tool result exchanges but less than the limit
        for j in range(4):
            exchange = request.chat_history.add()
            node = exchange.request_nodes.add()
            node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
            node.tool_result_node.tool_use_id = f"tool_{j}"
            node.tool_result_node.content = f"result_{j}"

        with mock.patch.object(
            _MAX_TOOL_TURNS_PER_USER_MESSAGE, "get", return_value=10
        ):
            self.assertIsNone(check_turn_limit(request))

    def test_check_turn_limit_over_limit(self):
        """Test checking turn limit when over the limit."""
        request = chat_pb2.ChatRequest()

        # Add a tool result node to the current message
        current_node = request.nodes.add()
        current_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        current_node.tool_result_node.tool_use_id = "tool_current"
        current_node.tool_result_node.content = "result_current"

        # Add multiple exchanges with tool results to exceed the limit
        for j in range(14):
            exchange = request.chat_history.add()
            node = exchange.request_nodes.add()
            node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
            node.tool_result_node.tool_use_id = f"tool_{j}"
            node.tool_result_node.content = f"result_{j}"

        with mock.patch.object(
            _MAX_TOOL_TURNS_PER_USER_MESSAGE, "get", return_value=10
        ):
            result = check_turn_limit(request)
            self.assertIsNotNone(result)

            # Check that the ChatResult has the expected text
            if (
                result is not None
            ):  # This is redundant with assertIsNotNone but makes the IDE happy
                self.assertIsInstance(result, ChatResult)
                self.assertEqual(
                    result.text,
                    "Would you like me to keep going?",
                )

    def test_check_turn_limit_unlimited(self):
        """Test checking turn limit when the limit is set to unlimited (<=0)."""
        request = chat_pb2.ChatRequest()

        # Add a tool result node to the current message
        current_node = request.nodes.add()
        current_node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
        current_node.tool_result_node.tool_use_id = "tool_current"
        current_node.tool_result_node.content = "result_current"

        # Add many tool result exchanges, more than any reasonable limit
        for j in range(99):
            exchange = request.chat_history.add()
            node = exchange.request_nodes.add()
            node.type = chat_pb2.ChatRequestNodeType.TOOL_RESULT
            node.tool_result_node.tool_use_id = f"tool_{j}"
            node.tool_result_node.content = f"result_{j}"

        # Test with max_turns = 0 (unlimited)
        with mock.patch.object(_MAX_TOOL_TURNS_PER_USER_MESSAGE, "get", return_value=0):
            self.assertIsNone(check_turn_limit(request))

        # Test with max_turns = -1 (unlimited)
        with mock.patch.object(
            _MAX_TOOL_TURNS_PER_USER_MESSAGE, "get", return_value=-1
        ):
            self.assertIsNone(check_turn_limit(request))


if __name__ == "__main__":
    unittest.main()

"""Turn limit module for limiting the length of chat history.

This module provides functionality to limit the number of consecutive tool result
messages in a chat history. When the limit is exceeded, the chat server will respond
with a fixed message asking if the user would like to continue.
"""

import logging
from typing import Optional

from base import feature_flags
from services.chat_host import chat_pb2
from services.chat_host.server.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatResultStatusCode

# Feature flag for maximum number of consecutive tool result turns
_MAX_TOOL_TURNS_PER_USER_MESSAGE = feature_flags.IntFlag(
    "max_tool_turns_per_user_message", 50
)

# Logger for this module
logger = logging.getLogger(__name__)


def _nodes_have_tool_result(nodes) -> bool:
    """Check if a list of nodes contains a tool result node.

    Args:
        nodes: A list of ChatRequestNode objects

    Returns:
        True if the list contains a tool result node, False otherwise
    """
    if not nodes:
        return False

    for node in nodes:
        if node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT:
            return True

    return False


def count_tool_result_nodes(request: chat_pb2.ChatRequest) -> int:
    """Count tool result exchanges according to the following rules:
    * If current user message does not contain a tool result, the count is 0
    * Otherwise the count is 1 to represent that tool result
    * Going back through history in reverse order, count consecutive exchanges
      that contain a tool result and add to the initial value of 1

    Args:
        request: The chat request containing chat history

    Returns:
        The number of tool result exchanges according to the counting rules
    """
    # Check if the current message (in request.nodes) contains a tool result
    if not _nodes_have_tool_result(request.nodes):
        return 0

    # Start with a count of 1 for the current message
    tool_result_count = 1

    # If there's no chat history, return the current count
    if not request.chat_history:
        return tool_result_count

    # Go back through history in reverse order
    for exchange in reversed(request.chat_history):
        # If this exchange doesn't have a tool result, stop counting
        if not _nodes_have_tool_result(exchange.request_nodes):
            break

        # Increment the count for this exchange with a tool result
        tool_result_count += 1

    return tool_result_count


def check_turn_limit(request: chat_pb2.ChatRequest) -> Optional[ChatResult]:
    """Check if the turn limit has been exceeded and return a response if it has.

    Args:
        request: The chat request containing chat history

    Returns:
        A ChatResult response if the limit is exceeded, None otherwise
    """
    context = feature_flags.get_global_context()
    max_turns = _MAX_TOOL_TURNS_PER_USER_MESSAGE.get(context)

    # If max_turns is <= 0, it means unlimited (no limit)
    if max_turns <= 0:
        return None

    # Count tool result nodes
    tool_result_turns = count_tool_result_nodes(request)

    # If the count exceeds the limit, return a response
    if tool_result_turns >= max_turns:
        logger.info(
            f"Turn limit exceeded: {tool_result_turns} tool results (limit: {max_turns})"
        )

        # Create a response asking if the user wants to continue
        result = ChatResult(
            text="Would you like me to keep going?",
            unknown_blob_names=[],
            status_code=ChatResultStatusCode.OK,
        )

        return result

    # If the limit is not exceeded, return None
    return None

from prometheus_client import Histogram

INF = float("inf")
# Exponentially increasing buckets with ~30% width. Min is ~10ms and max is ~4s.
retrieval_latency_buckets = tuple([0.01 * (1.3**i) for i in range(1, 24)] + [INF])

_chat_retrieval_latency = Histogram(
    "au_chat_retrieval_latency",
    "Latency of the retrieval processing (in the chat host) in seconds",
    ["model", "request_source", "tenant_name"],
    buckets=retrieval_latency_buckets,
)


class ChatHandlerMetrics:
    """Common metrics for chat handlers."""

    def __init__(self):
        self.chat_retrieval_latency = _chat_retrieval_latency

"""Defines the Cha<PERSON><PERSON><PERSON><PERSON> protocol."""

import concurrent.futures
import json
from base.prompt_format.common import (
    ChatResultNode,
    ChatResultNodeType,
    PromptChunk,
    StopReason,
)
from dataclasses import dataclass, field
from typing import Iterable, Protocol, Sequence
from enum import IntEnum

from services.lib.retrieval.retriever import Retrieval<PERSON><PERSON>ult
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.chat_host import chat_pb2


class ChatResultStatusCode(IntEnum):
    OK = 0  # No error
    EXCEED_CONTEXT_LENGTH = 1
    WAS_BLOCKED = 2  # Request was blocked. API proxy should sleep a bit to push back.


@dataclass
class ChatResult:
    """Class storing the result of a chat call."""

    text: str
    """The chat text to be inserted."""

    unknown_blob_names: Sequence[str]
    """Names of all unknown blobs."""

    checkpoint_not_found: bool = False
    """True if the blobs checkpoint id is not available."""

    workspace_file_chunks: Sequence[PromptChunk] = field(default_factory=list)
    """The workspace file's context info for the chat result."""

    incorporated_external_sources: Sequence[chat_pb2.IncorporatedExternalSource] = (
        field(default_factory=list)
    )

    nodes: Sequence[ChatResultNode] = field(default_factory=list)
    """The nodes of the structured chat response."""

    status_code: ChatResultStatusCode | None = None

    stop_reason: StopReason | None = None

    def to_chat_response_proto(self) -> chat_pb2.ChatResponse:
        """Converts the ChatResult to a ChatResponse proto."""

        def tool_use_to_proto(node: ChatResultNode):
            if node.tool_use is None:
                return None
            match node.type:
                case ChatResultNodeType.TOOL_USE:
                    input_json = json.dumps(node.tool_use.input)
                case ChatResultNodeType.TOOL_USE_START:
                    input_json = ""
                case _:
                    return None
            return chat_pb2.ChatResultToolUse(
                tool_use_id=node.tool_use.tool_use_id,
                tool_name=node.tool_use.name,
                input_json=input_json,
                is_partial=node.tool_use.is_partial,
            )

        r = chat_pb2.ChatResponse(
            text=self.text,
            unknown_blob_names=self.unknown_blob_names,
            checkpoint_not_found=self.checkpoint_not_found,
            workspace_file_chunks=[
                chat_pb2.WorkspaceFileChunk(
                    char_start=chunk.char_start,
                    char_end=chunk.char_end,
                    blob_name=chunk.blob_name,
                )
                for chunk in self.workspace_file_chunks
            ],
            incorporated_external_sources=self.incorporated_external_sources[:],
            nodes=[
                chat_pb2.ChatResultNode(
                    id=node.id,
                    type=chat_pb2.ChatResultNodeType.Value(node.type.name),
                    content=node.content,
                    tool_use=tool_use_to_proto(node),
                )
                for node in self.nodes
            ],
        )
        if self.status_code:
            r.status_code = chat_pb2.ChatResponseStatusCode.Value(self.status_code.name)
        if self.stop_reason:
            r.stop_reason = chat_pb2.ChatStopReason.Value(self.stop_reason.name)
        return r


@dataclass
class FindMissingResult:
    """Class storing the result of a find-missing call."""

    missing_blob_names: Sequence[str]
    """(deprecated, call ContentManager instead) List of blob names that are completely unknown."""

    nonindexed_blob_names: Sequence[str]
    """List of blob names that are not indexed for one or more retrievers used by this model."""


class ChatHandlerProtocol(Protocol):
    """Handler for Chat.

    Contains the main business logic of chats.
    """

    def chat_stream(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> Iterable[ChatResult]:
        """Chats the given request.

        Args:
            request: The chat request.
            request_id: The request id.
            request_context: The request context for the request.
            executor: An executor pool for the handler. Exclusive per handler.

        Returns:
            A chat result.
        """
        raise NotImplementedError()

    def find_missing(
        self,
        request: chat_pb2.FindMissingRequest,
        request_context: RequestContext,
        executor: concurrent.futures.Executor,
    ) -> FindMissingResult:
        """Probes the model to determine which raw and indexed blobs are present.

        Args:
            request: The find-missing request.

        Returns:
            A find-missing response containing two lists of blobs, the first containing names
            of unknown blobs, the second containing names of blobs that are not indexed for
            one or more retrievers used by this model.
        """
        raise NotImplementedError()

    def retrieve(
        self,
        request: chat_pb2.ChatRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
    ) -> RetrievalResult:
        """Retrieves the given request.

        Args:
            request: The retrieval request.
            request_id: The request id.
            request_context: The request context for the request.
            executor: An executor pool for the handler. Exclusive per handler.

        Returns:
            A retrieval result.
        """
        raise NotImplementedError()

"""Tests for ChatRequestInsightBuilder."""

import uuid
from unittest.mock import MagicMock

from base.prompt_format.common import Exchange
from base.prompt_format_chat.prompt_formatter import (
    TokenizedChatPromptOutput,
    StructuredChatPromptOutput,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer
from services.chat_host import chat_pb2
from services.chat_host.server.chat_request_insight_builder import (
    ChatRequestInsightBuilder,
    _stringify_structured_chat_prompt_output,
)
from services.chat_host.server.handler import Chat<PERSON><PERSON><PERSON>
from services.request_insight import request_insight_pb2


def _test_update_request(
    request_id: str, events: list[request_insight_pb2.RequestEvent], auth_info: AuthInfo
):
    del auth_info
    return request_insight_pb2.UpdateRequestInfoRequest(
        request_id=request_id,
        events=events,
    )


def test_record_request():
    """Tests that record_request correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = ChatRequestInsightBuilder(ri_publisher)
    chat_request = chat_pb2.ChatRequest(model_name="test_model")

    request_context = RequestContext.create()
    tokenizer = DeepSeekCoderInstructTokenizer()
    # These tokens refers to "void quicksort"
    ri_builder.record_request(
        prompt_output=TokenizedChatPromptOutput(
            tokens=[4563, 445, 6388, 439], retrieved_chunks_in_prompt=[]
        ),
        tokenizer=tokenizer,
        request=chat_request,
        request_context=request_context,
        auth_info=None,
        external_source_ids=None,
    )

    assert len(ri_publisher.publish_request_insight.call_args.args) == 1
    request = ri_publisher.publish_request_insight.call_args[0][0]
    print(2, request)
    assert request.request_id == request_context.request_id
    assert len(request.events) == 1
    assert request.events[0].chat_host_request == request_insight_pb2.RIChatRequest(
        request=chat_request,
        request_source="unknown",
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[4563, 445, 6388, 439],
            text="void quicksort",
            offsets=[0, 4, 7, 11],
            log_probs=[],
        ),
    )


def test_record_response():
    """Tests that record_response correctly submit events."""
    ri_publisher = MagicMock()
    ri_publisher.update_request_info_request = MagicMock(
        side_effect=_test_update_request
    )
    ri_builder = ChatRequestInsightBuilder(ri_publisher)
    chat_result = ChatResult(
        text="test_text",
        unknown_blob_names=[],
    )
    chat_response = chat_pb2.ChatResponse(text="test_text")
    tokenizer = DeepSeekCoderInstructTokenizer()
    request_context = RequestContext.create()
    ri_builder.record_response(
        chat_result.to_chat_response_proto(),
        tokenizer=tokenizer,
        truncated_output=[4563],
        truncated_log_probs=[0.7],
        request_context=request_context,
        auth_info=None,
    )

    assert len(ri_publisher.publish_request_insight.call_args.args) == 1
    request = ri_publisher.publish_request_insight.call_args[0][0]
    assert request.request_id == request_context.request_id
    assert len(request.events) == 1
    assert request.events[0].chat_host_response == request_insight_pb2.RIChatResponse(
        response=chat_response,
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[4563],
            text="void",
            offsets=[0],
            log_probs=[0.7],
        ),
    )


def test_chat_result_to_response_proto():
    """Tests chat_result_to_response_proto function."""
    chat_result = ChatResult(
        text="test_text",
        unknown_blob_names=["chatty"],
        checkpoint_not_found=True,
    )
    chat_response = chat_pb2.ChatResponse(
        text="test_text",
        unknown_blob_names=["chatty"],
        checkpoint_not_found=True,
    )

    assert chat_result.to_chat_response_proto() == chat_response


def test_stringify_structured_chat_prompt_output():
    """Tests that _stringify_structured_chat_prompt_output produces the expected output."""
    structured_prompt = StructuredChatPromptOutput(
        system_prompt="Test system prompt",
        message="Test message",
        prefill="Test prefill",
        chat_history=[
            Exchange(request_message="Test request", response_text="Test response")
        ],
        retrieved_chunks_in_prompt=[],
    )

    result = _stringify_structured_chat_prompt_output(structured_prompt)

    expected_output = """\
----------------------------------------
Metadata:
    Chunks in prompt: 0
    Lines in prompt: 28

Paths in prompt:


----------------------------------------
----------------------------------------
system_prompt:
----------------------------------------
Test system prompt

----------------------------------------
current message:
----------------------------------------
Test message

----------------------------------------
model response prefill:
----------------------------------------
Test prefill

----------------------------------------
chat history (from most recent to least recent):
----------------------------------------
    ----------------------------------------
    user:
    ----------------------------------------
    Test request

    ----------------------------------------
    assistant:
    ----------------------------------------
    Test response

"""

    result_rstripped_lines = "\n".join([line.rstrip() for line in result.splitlines()])

    # Compare the actual result with the expected output
    # We're using strip() to handle any potential whitespace differences
    assert result_rstripped_lines.strip() == expected_output.strip()

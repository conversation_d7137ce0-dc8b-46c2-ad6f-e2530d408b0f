"""Tests for the RepromptingClient."""

from unittest.mock import MagicM<PERSON>, patch, ANY

import pytest

from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    StopReason,
)
from base.prompt_format.common import (
    Exchange as PromptExchange,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolUseResponse,
    ToolUseStart,
    PromptCacheUsage,
)
from services.chat_host.server.chat_third_party_handler import (
    EMPTY_RESPONSE_REPROMPTED,
    RepromptingClient,
)
from dataclasses import dataclass


@dataclass
class responses:
    """Streams to test with."""

    empty = ThirdPartyModelResponse(
        text="",
    )
    text_only = ThirdPartyModelResponse(
        text="test message\n",
    )
    tool_start = ThirdPartyModelResponse(
        text="",
        tool_use_start=ToolUseStart(
            tool_name="test_tool",
            tool_use_id="123",
        ),
    )
    tool_use = ThirdPartyModelResponse(
        text="",
        tool_use=ToolUseResponse(
            tool_use_id="123",
            tool_name="test_tool",
            input={"param": "value"},
        ),
    )
    stop_end_turn = ThirdPartyModelResponse(
        text="",
        end_of_stream=EndOfStream(stop_reason=StopReason.END_TURN),
    )
    stop_max_tokens = ThirdPartyModelResponse(
        text="",
        end_of_stream=EndOfStream(stop_reason=StopReason.MAX_TOKENS),
    )
    stop_tool_use = ThirdPartyModelResponse(
        text="",
        end_of_stream=EndOfStream(stop_reason=StopReason.TOOL_USE_REQUESTED),
    )
    final_parameters = ThirdPartyModelResponse(
        text="",
        final_parameters={"param": "value"},
    )
    prompt_cache_usage = ThirdPartyModelResponse(
        text="",
        prompt_cache_usage=PromptCacheUsage(
            input_tokens=100,
            cache_read_input_tokens=50,
            cache_creation_input_tokens=50,
        ),
    )
    text_and_end_turn = ThirdPartyModelResponse(
        text="test message\n",
        end_of_stream=EndOfStream(stop_reason=StopReason.END_TURN),
    )

    empty_streams = [
        [],
        [stop_end_turn],
        [
            final_parameters,
            prompt_cache_usage,
            empty,
            empty,
            stop_end_turn,
        ],
    ]

    non_empty_streams = [
        [text_only, stop_end_turn],
        [text_and_end_turn],
        [tool_use, stop_tool_use],
        [
            final_parameters,
            prompt_cache_usage,
            text_only,
            stop_end_turn,
        ],
        # If model generates no text, then hits max tokens before
        # generating anything, we should not reprompt as it would
        # be expected to happen again; the prompt does not fix any
        # max token issue
        [stop_max_tokens],
        [
            final_parameters,
            prompt_cache_usage,
            tool_start,
            stop_max_tokens,
        ],
    ]


class RepromptingClientTestKit:
    """Test kit for RepromptingClient tests."""

    def __init__(self):
        self.inner_client = MagicMock(ThirdPartyModelClient)
        self.client = RepromptingClient("test-model", self.inner_client)

        # Mock the Counter.labels().inc() method
        self.counter_mock = MagicMock()
        self.labels_mock = MagicMock()
        self.labels_mock.inc.return_value = None
        self.counter_mock.labels.return_value = self.labels_mock

        # Set up the patch for the counter
        self.counter_patcher = patch(
            "services.chat_host.server.chat_third_party_handler.EMPTY_RESPONSE_REPROMPTED",
            self.counter_mock,
        )

    def __enter__(self):
        self.counter_patcher.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.counter_patcher.stop()

    def assert_counter_not_incremented(self):
        """Assert that the counter was not incremented."""
        self.counter_mock.labels.assert_not_called()

    def assert_counter_incremented(
        self, model_name, input_type, output_type, failure=False
    ):
        """Assert that the counter was incremented with the expected parameters."""
        self.counter_mock.labels.assert_called_once_with(
            model_name=model_name,
            input_type=input_type,
            output_type=output_type,
            failure=failure,
        )
        self.labels_mock.inc.assert_called_once()


@pytest.fixture
def test_kit():
    """Set up the test environment with a RepromptingClientTestKit."""
    with RepromptingClientTestKit() as kit:
        yield kit


def test_count_tokens(test_kit):
    """Test that count_tokens delegates to the inner client."""
    test_kit.inner_client.count_tokens.return_value = 42
    result = test_kit.client.count_tokens("test message")
    test_kit.inner_client.count_tokens.assert_called_once_with("test message")
    assert result == 42


@pytest.mark.parametrize("first_stream", responses.non_empty_streams)
def test_non_empty_stream_unchanged(test_kit, first_stream):
    test_kit.inner_client.generate_response_stream.return_value = first_stream

    result = list(
        test_kit.client.generate_response_stream(
            model_caller="test-model",
            cur_message="test message",
        )
    )

    test_kit.inner_client.generate_response_stream.assert_called_once()
    assert result == first_stream
    test_kit.assert_counter_not_incremented()


@pytest.mark.parametrize("first_stream", responses.empty_streams)
@pytest.mark.parametrize(
    "second_stream", responses.empty_streams + responses.non_empty_streams
)
def test_empty_stream_retries_once(test_kit, first_stream, second_stream):
    test_kit.inner_client.generate_response_stream.side_effect = [
        first_stream,
        second_stream,
    ]

    result = list(
        test_kit.client.generate_response_stream(
            model_caller="test-model",
            cur_message="test message",
        )
    )

    assert test_kit.inner_client.generate_response_stream.call_count == 2
    assert result == second_stream
    test_kit.assert_counter_incremented(
        model_name="test-model",
        input_type=ANY,
        output_type=ANY,
        failure=False,
    )


@pytest.mark.parametrize("message_type", ["str", "nodes"])
def test_prompt_extension(test_kit, message_type):
    test_kit.inner_client.generate_response_stream.return_value = (
        responses.empty_streams[0]
    )

    if message_type == "nodes":
        cur_message = [
            ChatRequestNode(
                id=0,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id="tool-123",
                    content="test result",
                    is_error=False,
                ),
            ),
            ChatRequestNode(
                id=1,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(content="test message"),
                tool_result_node=None,
            ),
        ]
        modified_message = cur_message + [
            ChatRequestNode(
                id=2,
                type=ChatRequestNodeType.TEXT,
                text_node=ChatRequestText(
                    content="\nContinue or explain why you are stopping"
                ),
                tool_result_node=None,
            ),
        ]
    else:
        cur_message = "test message"
        modified_message = "test message\nContinue or explain why you are stopping"

    result = list(
        test_kit.client.generate_response_stream(
            model_caller="test-model",
            cur_message=cur_message,
        )
    )

    assert test_kit.inner_client.generate_response_stream.call_count == 2
    assert (
        test_kit.inner_client.generate_response_stream.call_args_list[1].kwargs[
            "cur_message"
        ]
        == modified_message
    )
    assert result == responses.empty_streams[0]
    test_kit.assert_counter_incremented(
        model_name="test-model",
        input_type="text" if message_type == "str" else "tool_result",
        output_type=ANY,
        failure=False,
    )


def test_pre_stream_error(test_kit):
    test_kit.inner_client.generate_response_stream.side_effect = Exception(
        "Test exception"
    )
    with pytest.raises(Exception):
        list(
            test_kit.client.generate_response_stream(
                model_caller="test-model",
                cur_message="test message",
            )
        )
    test_kit.inner_client.generate_response_stream.assert_called_once()
    test_kit.assert_counter_not_incremented()


def test_mid_stream_error(test_kit):
    test_kit.inner_client.generate_response_stream.return_value = [
        responses.final_parameters,
        responses.prompt_cache_usage,
        Exception("Test exception"),
    ]
    result = []
    with pytest.raises(Exception):
        for response in test_kit.client.generate_response_stream(
            model_caller="test-model",
            cur_message="test message",
        ):
            result.append(response)
    assert result == [
        responses.final_parameters,
        responses.prompt_cache_usage,
    ]
    test_kit.inner_client.generate_response_stream.assert_called_once()
    test_kit.assert_counter_not_incremented()


def test_pre_second_stream_error(test_kit):
    test_kit.inner_client.generate_response_stream.side_effect = [
        responses.empty_streams[0],
        Exception("Test exception"),
    ]
    with pytest.raises(Exception):
        list(
            test_kit.client.generate_response_stream(
                model_caller="test-model",
                cur_message="test message",
            )
        )
    assert test_kit.inner_client.generate_response_stream.call_count == 2
    test_kit.assert_counter_incremented(
        model_name="test-model",
        input_type=ANY,
        output_type=ANY,
        failure=True,
    )


def test_mid_second_stream_error(test_kit):
    test_kit.inner_client.generate_response_stream.side_effect = [
        responses.empty_streams[0],
        [
            responses.final_parameters,
            responses.prompt_cache_usage,
            responses.empty,
            responses.text_only,
            responses.tool_start,
            Exception("Test exception"),
        ],
    ]
    result = []
    with pytest.raises(Exception):
        for response in test_kit.client.generate_response_stream(
            model_caller="test-model",
            cur_message="test message",
        ):
            result.append(response)
    assert result == [
        responses.final_parameters,
        responses.prompt_cache_usage,
        responses.empty,
        responses.text_only,
        responses.tool_start,
    ]
    assert test_kit.inner_client.generate_response_stream.call_count == 2
    test_kit.assert_counter_incremented(
        model_name="test-model",
        input_type=ANY,
        output_type=ANY,
        failure=True,
    )

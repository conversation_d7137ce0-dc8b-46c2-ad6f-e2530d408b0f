"""Unit Tests for <PERSON><PERSON><PERSON><PERSON><PERSON>."""

import typing
import uuid
from concurrent.futures import Executor
from unittest.mock import ANY, <PERSON><PERSON><PERSON>, patch

import pytest

from base import feature_flags
from base.blob_names import blob_names_pb2
from base.blob_names.python.blob_names import Blobs
from base.prompt_format.common import (
    DocumentationMetadata,
    Exchange,
    PersonaType,
    PromptChunk,
)
from base.prompt_format_chat.prompt_formatter import (
    Changed<PERSON>ileStats,
    ChatPromptFormatter,
    ChatPromptInput,
    ChatTokenApportionment,
    TokenizedChatPromptOutput,
)
from base.prompt_format_chat.prompt_formatter import Exchange as PromptExchange
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.test_utils.synchronous_executor import SynchronousExecutor
from services.chat_host import chat_pb2
from services.chat_host.server.chat_handler import Cha<PERSON><PERSON><PERSON><PERSON>, create_chat_handler
from services.chat_host.server.chat_handler_metrics import Cha<PERSON><PERSON><PERSON>lerMetrics
from services.chat_host.server.handler import Cha<PERSON><PERSON><PERSON><PERSON>
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)
from services.inference_host.infer_pb2_grpc import InfererStub
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import (
    FindMissingResult as RetrieverFindMissingResult,
)
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

feature_flags.unit_test_setup()


def _update_nested_dict(d: dict, u: dict) -> dict:
    for k, v in u.items():
        if isinstance(v, dict):
            d[k] = _update_nested_dict(d.get(k, {}), v)
        else:
            d[k] = v
    return d


def _get_chat_handler(
    updates_for_handler_config: typing.Optional[dict] = None,
    retrieved_chunks: typing.Optional[list[RetrievalChunk]] = None,
    response: typing.Iterable[InferenceClientProtocol.Reply] | None = None,
) -> ChatHandler:
    """Factory function for creating chat handler.

    Args:
        updates_for_handler_config: Modifies default values of handler config.
    """

    handler_config = {
        "tokenizer_name": "deepseek_coder_instruct",
        "prompt_formatter_name": "binks_legacy",
        "token_apportionment": {
            "path_len": 100,
            "message_len": 100,
            "prefix_len": 1024,
            "selected_code_len": 1024,
            "chat_history_len": 1024,
            "suffix_len": 1024,
            "retrieval_len": -1,
            "max_prompt_len": 16384 - 4096,
        },
        "generate_commit_message_token_apportionment": {
            "changed_files_summary_line_threshold": 900,
            "diff_len": 9216,
            "commit_message_len": 3072,
            "relevant_message_len": 1024,
            "max_prompt_len": 16384 - 4096,
            "path_len": 0,
            "message_len": 0,
            "chat_history_len": 0,
            "prefix_len": 0,
            "selected_code_len": 0,
            "suffix_len": 0,
        },
        "sampling_params": {},
        "preference_sampling_params": {
            "top_k": 11,
            "top_p": 0.9,
            "temperature": 0.5,
            "seed": 1219,
            "inference_timeout_s": 33.0,
        },
        "max_context_length": 8096,
        "max_output_length": 256,
    }

    if updates_for_handler_config is not None:
        _update_nested_dict(handler_config, updates_for_handler_config)

    stub = MagicMock(InfererStub)
    ri_publisher = MagicMock(RequestInsightPublisher)
    retriever = MagicMock(Retriever)
    retriever.retrieve.return_value = RetrievalResult(
        retrieved_chunks=retrieved_chunks or [],
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    metrics = ChatHandlerMetrics()
    content_manager_client = MagicMock()
    handler = create_chat_handler(
        handler_config,
        stub,
        "test_namespace",
        ri_publisher,
        metrics,
        content_manager_client,
        retriever,
    )
    handler.client = MagicMock(InfererClient)
    handler.client.infer_stream.return_value = response or iter([])

    return handler


def _get_chat_request(
    prefix: str = "def ",
    selected_code: str = "bar",
    suffix: str = "(a, b)",
    message: str = "Rename bar to foo",
    chat_history: typing.Optional[list[chat_pb2.Exchange]] = None,
    prefix_begin: typing.Optional[int] = None,
    suffix_end: typing.Optional[int] = None,
    path: str = "src/foo.py",
    blob_name: str = "blob0",
    lang: str = "python",
    blobs: typing.Sequence[blob_names_pb2.Blobs] = [
        Blobs.from_fake_blob_names(["blob1", "blob2"]).to_proto()
    ],
    enable_preference_collection: bool = False,
    user_guided_blobs: typing.Optional[list[str]] = None,
    prompt_formatter_name: str = "",
    changed_file_stats: typing.Optional[chat_pb2.ChangedFileStats] = None,
    diff: typing.Optional[str] = None,
    relevant_commit_messages: typing.Optional[list[str]] = None,
    example_commit_messages: typing.Optional[list[str]] = None,
    user_guidelines: str = "",
    workspace_guidelines: str = "",
) -> chat_pb2.ChatRequest:
    """Factory function for creating chat request."""

    if prefix_begin is None:
        prefix_begin = 0
    if suffix_end is None:
        suffix_end = len(prefix) + len(selected_code) + len(suffix) - 1
    if chat_history is None:
        chat_history = [
            chat_pb2.Exchange(
                request_message="How you doing?", response_text="Fine thanks"
            )
        ]

    chat_position = chat_pb2.ChatPosition(
        prefix_begin=prefix_begin,
        suffix_end=suffix_end,
        blob_name=blob_name,
    )

    chat_request = chat_pb2.ChatRequest(
        model_name="test_model",
        path=path,
        prefix=prefix,
        selected_code=selected_code,
        chat_history=[
            chat_pb2.Exchange(
                request_message=item.request_message, response_text=item.response_text
            )
            for item in chat_history
        ],
        suffix=suffix,
        message=message,
        position=chat_position,
        lang=lang,
        sequence_id=0,
        blobs=blobs,
        enable_preference_collection=enable_preference_collection,
        user_guided_blobs=user_guided_blobs,
        prompt_formatter_name=prompt_formatter_name,
        changed_file_stats=changed_file_stats,
        diff=diff,
        relevant_commit_messages=relevant_commit_messages,
        example_commit_messages=example_commit_messages,
        user_guidelines=user_guidelines,
        workspace_guidelines=workspace_guidelines,
    )

    return chat_request


def _get_chat_prompt_formatter():
    token_apportionment = MagicMock(ChatTokenApportionment)
    token_apportionment.retrieval_len = -1
    prompt_formatter = MagicMock(ChatPromptFormatter)
    prompt_formatter.token_apportionment = token_apportionment
    return prompt_formatter


def _run_chat(
    chat_handler: ChatHandler,
    chat_request: chat_pb2.ChatRequest,
    request_id: typing.Optional[uuid.UUID] = None,
    session_id: typing.Optional[uuid.UUID] = None,
    auth_info: typing.Optional[AuthInfo] = None,
    executor: typing.Optional[Executor] = None,
    request_context: typing.Optional[RequestContext] = None,
) -> tuple[ChatResult, RequestContext, AuthInfo]:
    chat_results, request_context, auth_info = _run_chat_stream(
        chat_handler,
        chat_request,
        request_id,
        session_id,
        auth_info,
        executor,
        request_context,
    )

    text = ""
    unknown_blob_names = []
    workspace_file_chunks = []
    checkpoint_not_found = False

    for chat_result in chat_results:
        text += chat_result.text
        unknown_blob_names.extend(chat_result.unknown_blob_names)
        workspace_file_chunks.extend(chat_result.workspace_file_chunks)
        checkpoint_not_found = chat_result.checkpoint_not_found

    output = ChatResult(
        text, unknown_blob_names, checkpoint_not_found, workspace_file_chunks
    )
    return output, request_context, auth_info


def _set_prompt_formatter_output(
    output: TokenizedChatPromptOutput, handler: ChatHandler
):
    assert isinstance(handler.prompt_formatter, MagicMock)
    handler.prompt_formatter.format_prompt.return_value = output


def _set_generate_commit_message_prompt_formatter_output(
    output: TokenizedChatPromptOutput, handler: ChatHandler
):
    assert isinstance(handler.commit_message_prompt_formatter, MagicMock)
    handler.commit_message_prompt_formatter.format_prompt.return_value = output


def test_basic():
    """Tests the basic usage."""
    chat_handler = _get_chat_handler()
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    output, _, _ = _run_chat(chat_handler, _get_chat_request())
    assert output.text == "def foo(a, b)"
    assert output.workspace_file_chunks == []


def test_eos_token_trimming():
    """Tests how handler trims eos tokens."""
    chat_handler = _get_chat_handler()

    eos_token = chat_handler.tokenizer.special_tokens.eos
    for number_of_eos in [1, 2]:
        _set_inference_reply_stream(
            chat_handler.tokenizer.tokenize_unsafe("def foo(a, b)")
            + [eos_token] * number_of_eos,
            chat_handler,
        )
        output, _, _ = _run_chat(chat_handler, _get_chat_request())
        assert output.text == "def foo(a, b)"
        assert output.workspace_file_chunks == []


def test_inference_call():
    """Tests that handler correctly calls inference host."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request("a", "b", "c", "d", [], 0, 2)
    _, request_context, _ = _run_chat(chat_handler, chat_request)

    chat_prompt_input = ChatPromptInput("d", "src/foo.py", "a", "b", "c", [], 0, 2, [])
    chat_prompt_output = chat_handler.prompt_formatter.format_prompt(chat_prompt_input)

    expected_InfererClient_kwargs = {
        "input_tokens": chat_prompt_output.tokens,
        "max_output_length": 256,
        "end_token_ids": {chat_handler.tokenizer.special_tokens.eos},
        "top_k": 0,
        "top_p": 0.0,
        "temperature": 0.0,
        "random_seed": 0,
        "request_context": request_context,
        "timeout_s": 120.0,
        "sequence_id": 0,
    }
    assert isinstance(chat_handler.client.infer_stream, MagicMock)
    chat_handler.client.infer_stream.assert_called_with(**expected_InfererClient_kwargs)


def test_preference_sampling_call():
    """Tests that handler correctly behaves when called with enable_preference_collection=True"""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request(
        "a", "b", "c", "d", [], 0, 2, enable_preference_collection=True
    )
    _, request_context, _ = _run_chat(chat_handler, chat_request)

    chat_prompt_input = ChatPromptInput("d", "src/foo.py", "a", "b", "c", [], 0, 2, [])
    chat_prompt_output = chat_handler.prompt_formatter.format_prompt(chat_prompt_input)

    expected_InfererClient_kwargs = {
        "input_tokens": chat_prompt_output.tokens,
        "max_output_length": 256,
        "end_token_ids": {chat_handler.tokenizer.special_tokens.eos},
        "top_k": 11,
        "top_p": 0.9,
        "temperature": 0.5,
        "request_context": request_context,
        "timeout_s": 33.0,
        "sequence_id": 0,
    }

    assert isinstance(chat_handler.client.infer_stream, MagicMock)
    # This is needed, because seed is randomly sampled in _infer*
    actual_kwargs = chat_handler.client.infer_stream.call_args.kwargs
    assert set(actual_kwargs.keys()) == (
        set(expected_InfererClient_kwargs.keys()) | set(["random_seed"])
    )
    for k, expected_v in expected_InfererClient_kwargs.items():
        assert actual_kwargs[k] == expected_v


def test_prompt_formatter_call():
    """Test that handler correctly calls prompt formatter."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request(
        prefix="a",
        selected_code="b",
        suffix="c",
        message="d",
        chat_history=[
            chat_pb2.Exchange(
                request_message="How you doing?", response_text="Fine thanks"
            )
        ],
    )

    chat_handler.prompt_formatter = MagicMock(chat_handler.prompt_formatter)
    chat_handler.prompt_formatter.format_prompt.return_value = (
        TokenizedChatPromptOutput(tokens=[1, 2, 3], retrieved_chunks_in_prompt=[])
    )

    expected_format_prompt_args = [
        ChatPromptInput(
            message="d",
            path="src/foo.py",
            prefix="a",
            selected_code="b",
            suffix="c",
            chat_history=[
                PromptExchange(
                    request_message="How you doing?", response_text="Fine thanks"
                )
            ],
            prefix_begin=0,
            suffix_end=2,
            retrieved_chunks=[],
            changed_file_stats=ChangedFileStats(),
            diff="",
            relevant_commit_messages=[],
            example_commit_messages=[],
            workspace_guidelines="",
            user_guidelines="",
            memories="",
            tasklist="",
            tool_definitions=None,
            persona_type=PersonaType.DEFAULT,
        )
    ]
    _ = _run_chat(chat_handler, chat_request)

    chat_handler.prompt_formatter.format_prompt.assert_called_with(
        *expected_format_prompt_args
    )


@patch("services.chat_host.server.chat_handler.ChatRequestInsightBuilder")
def test_basic_ri(ChatRequestInsightBuilder_Mock):
    """Tests the basic usage of request insight."""
    chat_handler = _get_chat_handler()
    assert chat_handler.retriever is not None
    chat_request = _get_chat_request()
    chat_response = chat_pb2.ChatResponse(
        text="def foo(a, b)",
        unknown_blob_names=[],
        checkpoint_not_found=False,
    )
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    chat_response, request_context, auth_info = _run_chat(chat_handler, chat_request)

    assert ChatRequestInsightBuilder_Mock.call_count == 1
    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_once()
    import time

    start_time = time.time()
    while ChatRequestInsightBuilder_Mock.return_value.record_response.call_count == 0:
        if time.time() - start_time > 0.5:  # 500ms timeout
            raise AssertionError("record_response was not called within 500ms")
        time.sleep(0.01)  # sleep for 10ms to avoid busy waiting

    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_with(
        ANY,
        ANY,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        external_source_ids=ANY,
    )
    ChatRequestInsightBuilder_Mock.return_value.record_response.assert_called_with(
        chat_response.to_chat_response_proto(),
        tokenizer=ANY,
        truncated_output=[1551, 24531, 7, 64, 11, 270, 8],
        truncated_log_probs=[],
        request_context=request_context,
        auth_info=auth_info,
        error_message=None,
    )


@patch("services.chat_host.server.chat_handler.ChatRequestInsightBuilder")
def test_basic_ri_mid_stream_error(ChatRequestInsightBuilder_Mock):
    """Tests the basic usage of request insight."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request()

    full_text = "def foo(a, b)"
    _set_inference_reply_stream_error(full_text, chat_handler)
    chat_responses, request_context, auth_info = _run_chat_stream(
        chat_handler, chat_request
    )

    result = ""
    # Expect error mid-stream
    with pytest.raises(InferenceClientProtocol.Error):
        for output in chat_responses:
            assert output.text is not None
            result += output.text

    assert result == full_text

    assert ChatRequestInsightBuilder_Mock.call_count == 1
    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_once()
    import time

    start_time = time.time()
    while ChatRequestInsightBuilder_Mock.return_value.record_response.call_count == 0:
        if time.time() - start_time > 0.5:  # 500ms timeout
            raise AssertionError("record_response was not called within 500ms")
        time.sleep(0.01)  # sleep for 10ms to avoid busy waiting

    ChatRequestInsightBuilder_Mock.return_value.record_request.assert_called_with(
        ANY,
        ANY,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        external_source_ids=ANY,
    )
    expected_chat_response_proto = chat_pb2.ChatResponse(
        text=full_text,
        unknown_blob_names=[],
        checkpoint_not_found=False,
    )
    ChatRequestInsightBuilder_Mock.return_value.record_response.assert_called_with(
        expected_chat_response_proto,
        tokenizer=ANY,
        truncated_output=[1551, 24531, 7, 64, 11, 270, 8],
        truncated_log_probs=[],
        request_context=request_context,
        auth_info=auth_info,
        error_message="InferenceClientProtocol.Error: BOOM!",
    )


def test_general_retrieve():
    """Tests basic usage of _retrieve()."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request(prefix_begin=5, suffix_end=15, path="/p1")

    chunks = [
        RetrievalChunk(
            text="a",
            path="/p1",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
        ),
        RetrievalChunk(
            text="b",
            path="/p1",
            char_start=30,
            char_end=40,
            blob_name=None,
            chunk_index=None,
        ),
        RetrievalChunk(
            text="c",
            path="/p3",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
        ),
    ]

    missing_blob_names = ["b1", "b2"]

    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.return_value = RetrievalResult(
        chunks, missing_blob_names, checkpoint_not_found=False
    )

    auth_info = MagicMock()
    with SynchronousExecutor() as executor:
        retrieval_output = chat_handler.retrieve(
            chat_request,
            RequestContext.create(),
            auth_info=auth_info,
            executor=executor,
        )

    retrieved_chunks = list(retrieval_output.get_retrieved_chunks())
    output_missing_blob_names = set(retrieval_output.get_missing_blob_names())
    assert len(retrieved_chunks) == 3
    assert output_missing_blob_names == set(missing_blob_names)
    assert retrieved_chunks == list(chunks)


def test_user_guided_blobs_retrieve():
    """Tests that file context info is correctly parsed."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request(user_guided_blobs=["blob_user"])
    assert chat_handler.retriever is not None
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    output, _, _ = _run_chat(chat_handler, chat_request)

    expected_retrieval_args = {
        "input_": RetrievalInput(
            ChatRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                message="Rename bar to foo",
                chat_history=[
                    Exchange(
                        request_message="How you doing?", response_text="Fine thanks"
                    )
                ],
                prefix_begin=0,
                suffix_end=12,
                blob_name="blob0",
            ),
            blobs=[Blobs.from_fake_blob_names(["blob1", "blob2"])],
            user_guided_blobs=["blob_user"],
            disable_auto_external_sources=True,
        ),
        "request_context": ANY,
        "auth_info": ANY,
        "executor": ANY,
    }
    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)
    assert output.text == "def foo(a, b)"
    assert output.workspace_file_chunks == []


def test_retrieve_call():
    """Tests that handler's .chat() correctly calls _retrieve()."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request()

    chat_handler.retrieve = MagicMock(chat_handler.retrieve)

    request_context = RequestContext.create()
    auth_info = MagicMock()

    executor = SynchronousExecutor()
    _run_chat(
        chat_handler,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    chat_handler.retrieve.assert_called_with(
        chat_request, request_context, auth_info, executor=executor
    )

    executor.shutdown()


def test_retriever_call():
    """Tests that Retriever.retrieve is called correctly."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request()

    request_context = RequestContext.create()
    auth_info = MagicMock()

    executor = SynchronousExecutor()
    _run_chat(
        chat_handler,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    expected_retrieval_args = {
        "input_": RetrievalInput(
            ChatRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                message="Rename bar to foo",
                chat_history=[
                    Exchange(
                        request_message="How you doing?", response_text="Fine thanks"
                    )
                ],
                prefix_begin=0,
                suffix_end=12,
                blob_name="blob0",
            ),
            blobs=[Blobs.from_fake_blob_names(["blob1", "blob2"])],
            disable_auto_external_sources=True,
        ),
        "request_context": request_context,
        "auth_info": auth_info,
        "executor": executor,
    }

    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)

    executor.shutdown()


def test_retriever_call_with_checkpoint():
    """Tests that Retriever.retrieve is called correctly with a checkpoint."""
    chat_handler = _get_chat_handler()
    blobs = Blobs.from_fake_blob_names(["blob3", "blob4"])
    chat_request = _get_chat_request(blobs=[blobs.to_proto()])

    request_context = RequestContext.create()
    auth_info = MagicMock()

    executor = SynchronousExecutor()
    _run_chat(
        chat_handler,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    expected_retrieval_args = {
        "input_": RetrievalInput(
            ChatRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                message="Rename bar to foo",
                chat_history=[
                    Exchange(
                        request_message="How you doing?", response_text="Fine thanks"
                    )
                ],
                prefix_begin=0,
                suffix_end=12,
                blob_name="blob0",
            ),
            blobs=[blobs],
            disable_auto_external_sources=True,
        ),
        "request_context": request_context,
        "auth_info": auth_info,
        "executor": executor,
    }

    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)

    executor.shutdown()


def test_retriever_call_with_multi_blobs():
    """Tests that Retriever.retrieve is called correctly with multi blobs."""
    chat_handler = _get_chat_handler()
    blobs1 = Blobs.from_fake_blob_names(["blob3", "blob4"])
    blobs2 = Blobs.from_fake_blob_names(["blob5", "blob7"])
    chat_request = _get_chat_request(blobs=[blobs1.to_proto(), blobs2.to_proto()])

    request_context = RequestContext.create()
    auth_info = MagicMock()

    executor = SynchronousExecutor()
    _run_chat(
        chat_handler,
        chat_request,
        request_context=request_context,
        auth_info=auth_info,
        executor=executor,
    )

    expected_retrieval_args = {
        "input_": RetrievalInput(
            ChatRetrieverPromptInput(
                prefix="def ",
                suffix="(a, b)",
                path="src/foo.py",
                selected_code="bar",
                message="Rename bar to foo",
                chat_history=[
                    Exchange(
                        request_message="How you doing?", response_text="Fine thanks"
                    )
                ],
                prefix_begin=0,
                suffix_end=12,
                blob_name="blob0",
            ),
            blobs=[blobs1, blobs2],
            disable_auto_external_sources=True,
        ),
        "request_context": request_context,
        "auth_info": auth_info,
        "executor": executor,
    }

    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_with(**expected_retrieval_args)

    executor.shutdown()


def test_empty_blob_list():
    """Tests that retriever is still called when blob list is empty (AU-6477)."""
    chat_handler = _get_chat_handler()
    chat_request = _get_chat_request(
        blobs=[blob_names_pb2.Blobs(baseline_checkpoint_id=None, added=[], deleted=[])],
    )

    request_context = RequestContext.create()
    auth_info = MagicMock()
    with SynchronousExecutor() as executor:
        retrieval_output = chat_handler.retrieve(
            chat_request,
            request_context,
            auth_info=auth_info,
            executor=executor,
        )

    assert len(list(retrieval_output.get_retrieved_chunks())) == 0
    assert len(list(retrieval_output.get_missing_blob_names())) == 0
    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.assert_called_once()


def _run_chat_stream(
    chat_handler: ChatHandler,
    chat_request: chat_pb2.ChatRequest,
    request_id: typing.Optional[uuid.UUID] = None,
    session_id: typing.Optional[uuid.UUID] = None,
    auth_info: typing.Optional[AuthInfo] = None,
    executor: typing.Optional[Executor] = None,
    request_context: typing.Optional[RequestContext] = None,
) -> tuple[typing.Iterable[ChatResult], RequestContext, AuthInfo]:
    if request_id is None:
        request_id = uuid.UUID("3b608c4f-b7da-4c05-8368-b7414fcd54b7")
    if session_id is None:
        session_id = uuid.UUID("e02cf196-4384-4c56-98c7-a144199038b8")
    if auth_info is None:
        auth_info = MagicMock()
    if executor is None:
        executor = SynchronousExecutor()
    if request_context is None:
        request_context = RequestContext(
            request_id=str(request_id),
            request_session_id=str(session_id),
            request_source="unknown",
        )

    chat_results = chat_handler.chat_stream(
        chat_request,
        request_context,
        auth_info=auth_info,
        executor=executor,
    )

    return chat_results, request_context, auth_info


def _set_inference_reply_stream(
    output: typing.Union[list[int], str], handler: ChatHandler
):
    """Generates an inference reply stream."""
    if isinstance(output, str):
        tokens = handler.tokenizer.tokenize_unsafe(output)
    else:
        tokens = output

    def build_generator() -> (
        typing.Generator[InferenceClientProtocol.Reply, None, None]
    ):
        for token in tokens:
            reply = InferenceClientProtocol.Reply([token])
            yield reply

    assert isinstance(handler.client.infer_stream, MagicMock)
    handler.client.infer_stream.return_value = build_generator()


def _set_inference_reply_stream_error(
    output: typing.Union[list[int], str], handler: ChatHandler
):
    """Generates an error mid-stream (after the provided output is streamed)."""
    if isinstance(output, str):
        tokens = handler.tokenizer.tokenize_unsafe(output)
    else:
        tokens = output

    def build_generator() -> (
        typing.Generator[InferenceClientProtocol.Reply, None, None]
    ):
        for token in tokens:
            reply = InferenceClientProtocol.Reply([token])
            yield reply
        raise InferenceClientProtocol.Error("BOOM!")

    assert isinstance(handler.client.infer_stream, MagicMock)
    handler.client.infer_stream.return_value = build_generator()


def test_stream_basic():
    """Tests the basic usage of stream."""
    chat_handler = _get_chat_handler()
    missing_blob_names = ["b1", "b2"]
    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.retrieve.return_value = RetrievalResult(
        [], missing_blob_names, checkpoint_not_found=False
    )
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    chat_results, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
    result = ""
    count = 0
    unknown_blob_name_counts = []
    for chat_result in chat_results:
        assert chat_result.text is not None
        result += chat_result.text
        unknown_blob_name_counts.append(len(chat_result.unknown_blob_names))
        count += 1
    assert result == "def foo(a, b)"
    assert count > 1, "Expected multiple chunks in the stream"
    assert unknown_blob_name_counts == [2] + [0] * (
        count - 1
    ), "Expected missing blob names in the first chunk only"


def test_stream_space_limit():
    """Tests the basic usage of stream."""
    chat_handler = _get_chat_handler()
    assert chat_handler.retriever is not None
    inference_reply_text = """This code will generate a list of 10 random numbers, sort it using quicksort, and then visualize the list in a bar chart. The left bar represents the original list, and the right bar represents the sorted list.
Please note that this is a very basic implementation and may not be suitable for large lists or for real-time visualization. For a more advanced implementation, you might want to look into more advanced sorting algorithms and visualization techniques."""  # noqa: E501
    _set_inference_reply_stream(inference_reply_text, chat_handler)
    chat_results, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
    result = ""
    count = 0
    for chat_result in chat_results:
        assert chat_result.text is not None
        result += chat_result.text
        count += 1
    assert result == inference_reply_text
    assert count == 78, "Expected 78 chunks: (retrieval + 76 words in text + newline)"


def test_stream_multitoken():
    """Tests stream of multi-token characters."""
    TEST_STRING = "The Mac command key is ⌘, and a random high unicode character is 覉"
    chat_handler = _get_chat_handler()
    assert chat_handler.retriever is not None
    _set_inference_reply_stream(
        TEST_STRING,
        chat_handler,
    )
    chat_results, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
    result = ""
    unknown_blob_name_counts = []
    for chat_result in chat_results:
        assert chat_result.text is not None
        result += chat_result.text
        unknown_blob_name_counts.append(len(chat_result.unknown_blob_names))
    assert result == TEST_STRING


def test_workspace_file_chunks_regular():
    """Tests that when there are retrieved chunks in the prompt for a non-stream chat, they are included in the ChatResult."""  # noqa: E501
    chat_handler = _get_chat_handler()
    chat_handler.prompt_formatter = _get_chat_prompt_formatter()
    assert chat_handler.retriever is not None
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    _set_prompt_formatter_output(
        TokenizedChatPromptOutput(
            tokens=[1, 2, 3],
            retrieved_chunks_in_prompt=[
                PromptChunk(
                    text="def foo(a, b)",
                    path="src/foo.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/foo.py",
                    origin="dense_retriever",
                ),
                PromptChunk(
                    text="def bar(a, b)",
                    path="src/bar.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/bar.py",
                    origin="dense_retriever",
                ),
            ],
        ),
        chat_handler,
    )
    chat_result, _, _ = _run_chat(chat_handler, _get_chat_request())
    assert chat_result.workspace_file_chunks == [
        PromptChunk(
            text="def foo(a, b)",
            path="src/foo.py",
            char_start=0,
            char_end=10,
            blob_name="src/foo.py",
            origin="dense_retriever",
        ),
        PromptChunk(
            text="def bar(a, b)",
            path="src/bar.py",
            char_start=0,
            char_end=10,
            blob_name="src/bar.py",
            origin="dense_retriever",
        ),
    ]


def test_workspace_file_chunks_stream():
    """Tests that when there are retrieved chunks in the prompt for a stream chat, they are included in the ChatResult."""  # noqa: E501
    chat_handler = _get_chat_handler()
    chat_handler.prompt_formatter = _get_chat_prompt_formatter()
    assert chat_handler.retriever is not None
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    _set_prompt_formatter_output(
        TokenizedChatPromptOutput(
            tokens=[1, 2, 3],
            retrieved_chunks_in_prompt=[
                PromptChunk(
                    text="def foo(a, b)",
                    path="src/foo.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/foo.py",
                    origin="dense_retriever",
                ),
                PromptChunk(
                    text="def bar(a, b)",
                    path="src/bar.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/bar.py",
                    origin="dense_retriever",
                ),
            ],
        ),
        chat_handler,
    )
    chat_results, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
    result = ""
    output_workspace_file_chunks = []
    for chat_result in chat_results:
        assert chat_result.text is not None
        result += chat_result.text
        output_workspace_file_chunks.extend(chat_result.workspace_file_chunks)
    assert result == "def foo(a, b)"
    assert output_workspace_file_chunks == [
        PromptChunk(
            text="def foo(a, b)",
            path="src/foo.py",
            char_start=0,
            char_end=10,
            blob_name="src/foo.py",
            origin="dense_retriever",
        ),
        PromptChunk(
            text="def bar(a, b)",
            path="src/bar.py",
            char_start=0,
            char_end=10,
            blob_name="src/bar.py",
            origin="dense_retriever",
        ),
    ]


def test_prompt_chunk_response_full():
    """Tests that the ChatResponse proto is correctly populated."""
    chat_handler = _get_chat_handler()
    chat_handler.prompt_formatter = _get_chat_prompt_formatter()
    assert chat_handler.retriever is not None
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    _set_prompt_formatter_output(
        TokenizedChatPromptOutput(
            tokens=[1, 2, 3],
            retrieved_chunks_in_prompt=[
                PromptChunk(
                    text="def foo(a, b)",
                    path="src/foo.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/foo.py",
                    origin="dense_retriever",
                ),
                PromptChunk(
                    text="def bar(a, b)",
                    path="src/bar.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/bar.py",
                    origin="dense_retriever",
                ),
            ],
        ),
        chat_handler,
    )
    chat_result, _, _ = _run_chat(chat_handler, _get_chat_request())
    assert chat_result.to_chat_response_proto() == chat_pb2.ChatResponse(
        text="def foo(a, b)",
        unknown_blob_names=[],
        checkpoint_not_found=chat_result.checkpoint_not_found,
        workspace_file_chunks=[
            chat_pb2.WorkspaceFileChunk(
                char_start=0,
                char_end=10,
                blob_name="src/foo.py",
            ),
            chat_pb2.WorkspaceFileChunk(
                char_start=0,
                char_end=10,
                blob_name="src/bar.py",
            ),
        ],
    )


def test_prompt_chunk_response_stream():
    """Tests that the ChatResponse proto is correctly populated."""
    chat_handler = _get_chat_handler()
    chat_handler.prompt_formatter = _get_chat_prompt_formatter()
    assert chat_handler.retriever is not None
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    _set_prompt_formatter_output(
        TokenizedChatPromptOutput(
            tokens=[1, 2, 3],
            retrieved_chunks_in_prompt=[
                PromptChunk(
                    text="def foo(a, b)",
                    path="src/foo.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/foo.py",
                    origin="dense_retriever",
                ),
                PromptChunk(
                    text="def bar(a, b)",
                    path="src/bar.py",
                    char_start=0,
                    char_end=10,
                    blob_name="src/bar.py",
                    origin="dense_retriever",
                ),
            ],
        ),
        chat_handler,
    )
    chat_results, _, _ = _run_chat_stream(chat_handler, _get_chat_request())
    output_workspace_file_chunks = []
    for chat_result in chat_results:
        assert chat_result.text is not None
        output_workspace_file_chunks.extend(
            chat_result.to_chat_response_proto().workspace_file_chunks
        )
    assert output_workspace_file_chunks == [
        chat_pb2.WorkspaceFileChunk(
            char_start=0,
            char_end=10,
            blob_name="src/foo.py",
        ),
        chat_pb2.WorkspaceFileChunk(
            char_start=0,
            char_end=10,
            blob_name="src/bar.py",
        ),
    ]


def test_find_missing():
    """Tests that the find-missing call is correctly forwarded to the retriever."""
    chat_handler = _get_chat_handler()

    # blob1 is missing in content manager, we ignore this fact and don't ask content_manager
    assert isinstance(chat_handler.content_manager_client, MagicMock)
    chat_handler.content_manager_client.find_missing.return_value = ["blob1"]

    # blob2 is missing in retriever only, so it should be marked as nonindexed
    assert isinstance(chat_handler.retriever, MagicMock)
    chat_handler.retriever.find_missing.return_value = RetrieverFindMissingResult(
        missing_blob_names=["blob1", "blob2"]
    )
    # blob3 is available so it should not appear anywhere
    find_missing_request = chat_pb2.FindMissingRequest(
        blob_names=["blob1", "blob2", "blob3"]
    )
    with SynchronousExecutor() as executor:
        chat_result = chat_handler.find_missing(
            find_missing_request,
            request_context=RequestContext.create(),
            executor=executor,
        )

    assert chat_result.missing_blob_names == []  # Deprecated
    assert chat_result.nonindexed_blob_names == ["blob1", "blob2"]


def test_chat_stream_with_docsets():
    """Tests the chat function with docsets."""
    chat_request = _get_chat_request(prefix_begin=5, suffix_end=15, path="/p1")

    chunks = [
        RetrievalChunk(
            text="a",
            path="/p1",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
        ),
        RetrievalChunk(
            text="b",
            path="",
            char_start=30,
            char_end=40,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["header1", "header2"],
            ),
        ),
        RetrievalChunk(
            text="c",
            path="",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://graphite.dev",
                name="graphite.dev",
                page_id="page_id2",
                headers=["header1", "header2"],
            ),
        ),
        RetrievalChunk(
            text="c",
            path="",
            char_start=0,
            char_end=10,
            blob_name=None,
            chunk_index=None,
            origin="dense_retriever",
            documentation_metadata=DocumentationMetadata(
                source_id="docset://bazel",
                name="bazel",
                page_id="page_id3",
                headers=["header1", "header2"],
            ),
        ),
    ]

    chat_handler = _get_chat_handler(
        retrieved_chunks=chunks,
        response=[InferenceClientProtocol.Reply([200, 201, 300])],
    )

    blobs = Blobs.from_fake_blob_names(["blob3", "blob4"])
    chat_request = _get_chat_request(blobs=[blobs.to_proto()])

    request_context = RequestContext.create()
    auth_info = MagicMock()

    with SynchronousExecutor() as executor:
        chat_result = chat_handler.chat_stream(
            chat_request, request_context, auth_info, executor=executor
        )
        sources_results = []
        sources_counts_per_chunk = []
        count = 0
        for result in chat_result:
            sources_counts_per_chunk.append(len(result.incorporated_external_sources))
            for source in result.incorporated_external_sources:
                assert source.source_name in ["graphite.dev", "bazel"]
                assert source.source_id in ["docset://graphite.dev", "docset://bazel"]
                sources_results.append(source.source_name)
            count += 1
        assert len(sources_results) == 2
        assert sources_counts_per_chunk == [2] + [0] * (
            count - 1
        ), "Expected missing blob names in the first chunk only"


def test_generate_commit_message_prompt_formatter_call():
    """Tests that the generate-commit-message prompt formatter is called."""
    chat_handler = _get_chat_handler()
    chat_handler.commit_message_prompt_formatter = _get_chat_prompt_formatter()
    chat_handler.commit_message_prompt_formatter.format_prompt.return_value = (
        TokenizedChatPromptOutput(tokens=[1, 2, 3], retrieved_chunks_in_prompt=[])
    )
    chat_request = _get_chat_request(
        prefix="def ",
        selected_code="bar",
        suffix="(a, b)",
        message="Rename bar to foo",
        chat_history=[],
        prefix_begin=0,
        suffix_end=10,
        path="src/foo.py",
        blob_name="blob0",
        lang="python",
        blobs=[Blobs.from_fake_blob_names(["blob1", "blob2"]).to_proto()],
        enable_preference_collection=False,
        user_guided_blobs=None,
        prompt_formatter_name="generate-commit-message",
    )
    _set_inference_reply_stream("def foo(a, b)", chat_handler)
    _ = _run_chat(chat_handler, chat_request)
    chat_handler.commit_message_prompt_formatter.format_prompt.assert_called_once()

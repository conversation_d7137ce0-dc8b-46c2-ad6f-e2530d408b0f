import json
from typing import Sequence

import prometheus_client
import structlog

from base.prompt_format.common import (
    ToolResultContentNode,
    ToolResultContentNodeType,
    ChatRequestEditEvents,
    ChatRequestFileEdit,
    ChatRequestIdeState,
    ChatRequestImage,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestSingleEdit,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    ImageFormatType,
    RequestMessage,
    ResponseMessage,
    StopReason,
    TerminalInfo,
    WorkspaceFolderInfo,
)
from base.third_party_clients.third_party_model_client import (
    Too<PERSON><PERSON><PERSON><PERSON>,
    ToolChoiceType,
    ToolDefinition,
)
from services.chat_host import chat_pb2
from services.lib.proto import chat_pb2 as chat_common

logger = structlog.get_logger()
IMAGE_ID_COUNT = prometheus_client.Counter(
    "au_chat_host_image_id_count",
    "Number of times we've seen an IMAGE_ID node",
)


def convert_request_node(node: chat_pb2.ChatRequestNode) -> ChatRequestNode:
    """Convert a ChatRequestNode proto to a ChatRequestNode object.

    Remarks:
        Converting nodes to `base` types enables code sharing of prompt formatters
        between research and services without dealing with sharp edges, like the
        fact that `chat_pb2.ChatRequestNodeType.TEXT != ChatRequestNodeType.TEXT`
        or that protobufs set uninitialized fields to non-None values.
    """
    if node.type == chat_pb2.ChatRequestNodeType.TEXT:
        return ChatRequestNode(
            id=node.id,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content=node.text_node.content),
            tool_result_node=None,
            image_node=None,
        )
    elif node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT:
        request_id = None
        if node.tool_result_node.HasField("request_id"):
            request_id = node.tool_result_node.request_id

        # Convert content_nodes if present
        content_nodes = None
        if len(node.tool_result_node.content_nodes) > 0:
            content_nodes = []
            for content_node in node.tool_result_node.content_nodes:
                if content_node.type == chat_pb2.ToolResultContentNodeType.CONTENT_TEXT:
                    content_nodes.append(
                        ToolResultContentNode(
                            type=ToolResultContentNodeType.CONTENT_TEXT,
                            text_content=content_node.text_content,
                        )
                    )
                elif (
                    content_node.type
                    == chat_pb2.ToolResultContentNodeType.CONTENT_IMAGE
                ):
                    content_nodes.append(
                        ToolResultContentNode(
                            type=ToolResultContentNodeType.CONTENT_IMAGE,
                            image_content=ChatRequestImage(
                                image_data=content_node.image_content.image_data,
                                format=ImageFormatType[
                                    chat_common.ImageFormatType.Name(
                                        content_node.image_content.format
                                    )
                                ],
                            ),
                        )
                    )

        return ChatRequestNode(
            id=node.id,
            type=ChatRequestNodeType.TOOL_RESULT,
            text_node=None,
            tool_result_node=ChatRequestToolResult(
                tool_use_id=node.tool_result_node.tool_use_id,
                content=node.tool_result_node.content,
                is_error=node.tool_result_node.is_error,
                request_id=request_id,
                content_nodes=content_nodes,
            ),
            image_node=None,
        )
    elif node.type == chat_pb2.ChatRequestNodeType.IMAGE:
        return ChatRequestNode(
            id=node.id,
            type=ChatRequestNodeType.IMAGE,
            text_node=None,
            tool_result_node=None,
            image_node=ChatRequestImage(
                image_data=node.image_node.image_data,
                format=ImageFormatType[
                    chat_common.ImageFormatType.Name(node.image_node.format)
                ],
            ),
        )
    elif node.type == chat_pb2.ChatRequestNodeType.IMAGE_ID:
        # Don't have access to many labels here; could do in chat_handler.py
        # but then need to catch all callers
        # If we ever provide richer information for an IMAGE_ID node (like
        # file name, alttext, etc. this can be a real node that goes to
        # prompt formatter)
        # Prior to this change, would result in a 500 error due to the
        # ValueError below.
        logger.warn("Replacing IMAGE_ID node with text node")
        IMAGE_ID_COUNT.inc()
        return ChatRequestNode(
            id=node.id,
            type=ChatRequestNodeType.TEXT,
            text_node=ChatRequestText(content=" <failed to load image data> "),
            tool_result_node=None,
            image_node=None,
        )
    elif node.type == chat_pb2.ChatRequestNodeType.IDE_STATE:
        return ChatRequestNode(
            id=node.id,
            type=ChatRequestNodeType.IDE_STATE,
            text_node=None,
            tool_result_node=None,
            ide_state_node=ChatRequestIdeState(
                workspace_folders=[
                    WorkspaceFolderInfo(
                        repository_root=folder.repository_root,
                        folder_root=folder.folder_root,
                    )
                    for folder in node.ide_state_node.workspace_folders
                ],
                workspace_folders_unchanged=node.ide_state_node.workspace_folders_unchanged,
                current_terminal=TerminalInfo(
                    terminal_id=node.ide_state_node.current_terminal.terminal_id,
                    current_working_directory=node.ide_state_node.current_terminal.current_working_directory,
                ),
            ),
        )
    elif node.type == chat_pb2.ChatRequestNodeType.EDIT_EVENTS:
        return ChatRequestNode(
            id=node.id,
            type=ChatRequestNodeType.EDIT_EVENTS,
            text_node=None,
            tool_result_node=None,
            ide_state_node=None,
            edit_events_node=ChatRequestEditEvents(
                edit_events=[
                    ChatRequestFileEdit(
                        path=event.path,
                        before_blob_name=event.before_blob_name,
                        after_blob_name=event.after_blob_name,
                        edits=[
                            ChatRequestSingleEdit(
                                before_line_start=edit.before_line_start,
                                before_text=edit.before_text,
                                after_line_start=edit.after_line_start,
                                after_text=edit.after_text,
                            )
                            for edit in event.edits
                        ],
                    )
                    for event in node.edit_events_node.edit_events
                ],
            ),
        )
    else:
        raise ValueError(f"Unsupported node type: {node.type}")


def convert_response_node(node: chat_pb2.ChatResultNode) -> ChatResultNode:
    """Convert a ChatResultNode proto to a ChatResultNode object.

    Remarks:
        Converting nodes to `base` types enables code sharing of prompt formatters
        between research and services without dealing with sharp edges, like the
        fact that `chat_pb2.ChatResultNodeType.RAW_RESPONSE != ChatResultNodeType.RAW_RESPONSE`
        or that protobufs set uninitialized fields to non-None values.
    """
    if node.type == chat_pb2.ChatResultNodeType.RAW_RESPONSE:
        return ChatResultNode(
            id=node.id,
            type=ChatResultNodeType.RAW_RESPONSE,
            content=node.content,
            tool_use=None,
        )
    elif node.type == chat_pb2.ChatResultNodeType.TOOL_USE:
        return ChatResultNode(
            id=node.id,
            type=ChatResultNodeType.TOOL_USE,
            content=node.content,
            tool_use=ChatResultToolUse(
                tool_use_id=node.tool_use.tool_use_id,
                name=node.tool_use.tool_name,
                input=json.loads(node.tool_use.input_json),
            ),
        )
    else:
        # These are types that convey information from the server to the client,
        # but which client should not send as part of the conversation.
        # In a future or slightly enhanced current API, we could consider making
        # this more explicit in the proto definitions.
        # TOOL_USE_START
        # MAIN_TEXT_FINISHED
        # WORKSPACE_FILE_CHUNKS
        # RELEVANT_SOURCES
        raise ValueError(f"Unsupported node type: {node.type}")


def request_to_message(
    request_message: str, request_nodes: Sequence[chat_pb2.ChatRequestNode]
) -> RequestMessage:
    """Get a RequestMessage object that represents the request."""
    if len(request_nodes) > 0:
        return [convert_request_node(node) for node in request_nodes]
    return request_message


def response_to_message(
    response_message: str, response_nodes: Sequence[chat_pb2.ChatResultNode]
) -> ResponseMessage:
    """Get a ResponseMessage object that represents the request."""
    if len(response_nodes) > 0:
        return [convert_response_node(node) for node in response_nodes]
    return response_message


def convert_exchange(exchange: chat_pb2.Exchange) -> Exchange:
    return Exchange(
        request_message=request_to_message(
            exchange.request_message,
            exchange.request_nodes,
        ),
        response_text=response_to_message(
            exchange.response_text,
            exchange.response_nodes,
        ),
        request_id=exchange.request_id if exchange.HasField("request_id") else None,
    )


def convert_history(history: Sequence[chat_pb2.Exchange]) -> list[Exchange]:
    return [convert_exchange(exchange) for exchange in history]


def convert_tool_choice(tool_choice: chat_pb2.ToolChoice) -> ToolChoice:
    result: ToolChoice
    if tool_choice.type == chat_pb2.ToolChoiceType.AUTO:
        result = ToolChoice(type=ToolChoiceType.AUTO)
    elif tool_choice.type == chat_pb2.ToolChoiceType.ANY:
        result = ToolChoice(type=ToolChoiceType.ANY)
    elif tool_choice.type == chat_pb2.ToolChoiceType.TOOL:
        result = ToolChoice(type=ToolChoiceType.TOOL, name=tool_choice.name)
    else:
        raise ValueError(f"Unknown tool choice type: {tool_choice.type}")
    return result


def request_message_to_proto(
    request_message: RequestMessage,
) -> Sequence[chat_pb2.ChatRequestNode]:
    if isinstance(request_message, str):
        return [
            chat_pb2.ChatRequestNode(
                type=chat_pb2.ChatRequestNodeType.TEXT,
                text_node=chat_pb2.ChatRequestText(content=request_message),
            )
        ]
    else:
        return [
            chat_pb2.ChatRequestNode(
                id=node.id,
                type=chat_pb2.ChatRequestNodeType.Value(node.type.name),
                text_node=node.text_node
                and chat_pb2.ChatRequestText(content=node.text_node.content),
                tool_result_node=node.tool_result_node
                and chat_pb2.ChatRequestToolResult(
                    tool_use_id=node.tool_result_node.tool_use_id,
                    content=node.tool_result_node.content,
                    is_error=node.tool_result_node.is_error,
                    request_id=node.tool_result_node.request_id,
                    content_nodes=[
                        chat_pb2.ToolResultContentNode(
                            text_content=content_node.text_content
                            if content_node.text_content is not None
                            else None,
                            image_content=content_node.image_content
                            and chat_pb2.ChatRequestImage(
                                image_data=content_node.image_content.image_data,
                                format=chat_common.ImageFormatType.Value(
                                    content_node.image_content.format.name
                                ),
                            ),
                        )
                        for content_node in (node.tool_result_node.content_nodes or [])
                    ]
                    if node.tool_result_node.content_nodes
                    else [],
                ),
                image_node=node.image_node
                and chat_pb2.ChatRequestImage(
                    image_data=node.image_node.image_data,
                    format=chat_common.ImageFormatType.Value(
                        node.image_node.format.name
                    ),
                ),
                ide_state_node=node.ide_state_node
                and chat_pb2.ChatRequestIdeState(
                    workspace_folders=[
                        chat_pb2.WorkspaceFolderInfo(
                            repository_root=folder.repository_root,
                            folder_root=folder.folder_root,
                        )
                        for folder in node.ide_state_node.workspace_folders
                    ],
                    workspace_folders_unchanged=node.ide_state_node.workspace_folders_unchanged,
                    current_terminal=node.ide_state_node.current_terminal
                    and chat_pb2.TerminalInfo(
                        terminal_id=node.ide_state_node.current_terminal.terminal_id,
                        current_working_directory=node.ide_state_node.current_terminal.current_working_directory,
                    ),
                ),
                edit_events_node=node.edit_events_node
                and chat_pb2.ChatRequestEditEvents(
                    edit_events=[
                        chat_pb2.ChatRequestFileEdit(
                            path=event.path,
                            before_blob_name=event.before_blob_name,
                            after_blob_name=event.after_blob_name,
                            edits=[
                                chat_pb2.ChatRequestSingleEdit(
                                    before_line_start=edit.before_line_start,
                                    before_text=edit.before_text,
                                    after_line_start=edit.after_line_start,
                                    after_text=edit.after_text,
                                )
                                for edit in event.edits
                            ],
                        )
                        for event in node.edit_events_node.edit_events
                    ],
                ),
            )
            for node in request_message
        ]


def response_message_to_proto(
    response_message: ResponseMessage,
) -> Sequence[chat_pb2.ChatResultNode]:
    if isinstance(response_message, str):
        return [
            chat_pb2.ChatResultNode(
                type=chat_pb2.ChatResultNodeType.RAW_RESPONSE,
                content=response_message,
            )
        ]
    else:
        result = []
        for node in response_message:
            if node.type == ChatResultNodeType.FINAL_PARAMETERS:
                continue
            tool_use = None
            if node.tool_use is not None and node.type in [
                ChatResultNodeType.TOOL_USE,
                ChatResultNodeType.TOOL_USE_START,
            ]:
                input_json = (
                    json.dumps(node.tool_use.input)
                    if node.type == ChatResultNodeType.TOOL_USE
                    else ""
                )
                tool_use = chat_pb2.ChatResultToolUse(
                    tool_use_id=node.tool_use.tool_use_id,
                    tool_name=node.tool_use.name,
                    input_json=input_json,
                )
            result.append(
                chat_pb2.ChatResultNode(
                    id=node.id,
                    type=chat_pb2.ChatResultNodeType.Value(node.type.name),
                    content=node.content,
                    tool_use=tool_use,
                )
            )
        return result


def exchange_to_proto(exchange: Exchange) -> chat_pb2.Exchange:
    return chat_pb2.Exchange(
        request_nodes=request_message_to_proto(exchange.request_message),
        response_nodes=response_message_to_proto(exchange.response_text),
        request_id=exchange.request_id,
    )

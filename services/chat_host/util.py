"""A small tool to issue requests against the chat host API."""

import argparse
import json
import logging
import sys
from pathlib import Path

import grpc
import pydantic

from base.blob_names import blob_names_pb2
from base.logging.console_logging import setup_console_logging
from services.lib.request_context.request_context import RequestContext
from services.chat_host import chat_pb2
from services.chat_host.client import Chat<PERSON>lient


def _get_client(args) -> ChatClient:
    if args.insecure:
        credentials = None
    else:
        if not args.ca_cert or not args.client_key or not args.client_cert:
            logging.error(
                "--ca-cert, --client-key, --client-cert need to be set "
                "unless --insecure is used"
            )
            sys.exit(1)
        credentials = grpc.ssl_channel_credentials(
            root_certificates=args.ca_cert.read_bytes(),
            private_key=args.client_key.read_bytes(),
            certificate_chain=args.client_cert.read_bytes(),
        )
    rpc_client = ChatClient(args.endpoint, credentials=credentials)
    return rpc_client


def _chat(client: ChatC<PERSON>, args):
    if not args.message:
        logging.error("--message must be set.")
        return 1
    message: str = args.message
    assert args.max_message_len > 0
    if len(message) > args.max_message_len:
        logging.info("Truncating message")
        message = message[: args.max_message_len]

    blobs = blob_names_pb2.Blobs(
        baseline_checkpoint_id="",
        added=[str(blob_name).encode("utf-8") for blob_name in args.blob_names].sort(),
        deleted=[],
    )

    # TODO: replace the fake blob names with real ones later.
    request_pb = chat_pb2.ChatRequest(
        model_name=args.model or "",
        message=message,
        prefix=args.prefix or "",
        suffix=args.suffix or "",
        selected_code=args.selected_code or "",
        chat_history=[
            chat_pb2.Exchange(
                request_message=item["request_message"],
                response_text=item["response_text"],
            )
            for item in args.chat_history
        ],
        blobs=[blobs],
        position=chat_pb2.ChatPosition(
            prefix_begin=0,
            suffix_end=len(args.prefix + args.selected_code + args.suffix),
            blob_name="my-blob-name",
        ),
        lang="python",
    )

    auth_token = None
    if args.auth_token_file:
        auth_token = pydantic.SecretStr(
            args.auth_token_file.read_text(encoding="utf-8").strip()
        )
    request_context = RequestContext.create(auth_token=auth_token)
    if args.stream:
        logging.info("Stream:")
        result = ""
        for response in client.chat_stream(request_pb, request_context=request_context):
            result += response.text
            logging.info("Incoming: %s", response.text)
        logging.info("\nResult: %s", result)
    else:
        resp = client.chat(request_pb, request_context=request_context)
        logging.info("\n%s", resp.text)

    return 0


def main():
    """Main entrypoint."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--endpoint", required=True, help="Endpoint to connect to, e.g. localhost:50051"
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="If true, the request will be in plaintext instead of a secure connection",
    )
    parser.add_argument("--ca-cert", type=Path, help="Path to the ca certificate")
    parser.add_argument("--client-key", type=Path, help="Path to the TLS client key")
    parser.add_argument(
        "--client-cert", type=Path, help="Path to the TLS client certificate"
    )

    subparsers = parser.add_subparsers(help="Commands", required=True)

    chat_parser = subparsers.add_parser("chat", help="Chat with your codebase")
    chat_parser.set_defaults(action="chat")
    chat_parser.add_argument(
        "--model", help="The name of the model to use. Can be empty"
    )
    chat_parser.add_argument(
        "--message",
        help="The message prompt, required",
    )
    chat_parser.add_argument(
        "--max-message-len",
        type=int,
        default=500,
        help="The maximal length of the message in characters, default 500. "
        "If longer, it is truncated.",
    )
    chat_parser.add_argument(
        "--suffix",
        default="",
        help="A suffix that comes after the selected-code",
    )
    chat_parser.add_argument(
        "--prefix",
        default="",
        help="A prefix that comes before the selected-code",
    )
    chat_parser.add_argument(
        "--selected-code",
        default="",
        help="Selected text to invoke the message on",
    )
    chat_parser.add_argument(
        "--file-content",
        default="",
        help="File content (used only if no selected text)",
    )
    chat_parser.add_argument(
        "--chat-history",
        type=json.loads,
        default=[],
        help="List of past requests and responses in the format "
        "[{request_message:'', response_text:''}]",
    )
    chat_parser.add_argument(
        "--auth-token-file",
        type=Path,
        help="Path to file containing authentication token",
    )
    chat_parser.add_argument(
        "--stream",
        type=bool,
        default=False,
        help="Stream tokens as they become available",
    )
    chat_parser.add_argument(
        "--blob-names",
        action="append",
    )

    args = parser.parse_args()
    client = _get_client(args)
    if args.action == "chat":
        sys.exit(_chat(client, args))
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()

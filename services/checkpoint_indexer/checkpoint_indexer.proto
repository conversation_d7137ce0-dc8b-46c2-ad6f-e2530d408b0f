syntax = "proto3";

package checkpoint_indexer;

import "google/rpc/status.proto";

service CheckpointIndexer {
  // create an index from a stream of embeddings
  // the request and response are both streamed to allow for large numbers of embeddings
  // and assets to be returned
  //
  // TODO the api is intended for debugging and testing. The real workloads
  // are triggered by pub/sub
  rpc CreateIndex(CreateIndexRequest) returns (CreateIndexResponse) {}
}

message CreateIndexRequest {
  // tenant id is optional. If not set, it will be extracted from the jwt token.
  string tenant_id = 3;
  // checkpoint id to index
  string checkpoint_id = 1;
  // transformation key to use for the embeddings
  string transformation_key = 2;
}

message CreateIndexResponse {
  string tenant_id = 1;
  // checkpoint id that was indexed
  string checkpoint_id = 2;
  // transformation key to use for the embeddings
  string transformation_key = 3;

  // index id of the created index
  string index_id = 4;
}

// Pubsub message for checkpoint indexer
// this is exchanged between working set and checkpoint indexer
message CheckpointIndexerPubsub {
  oneof message {
    // create index request
    CreateIndexRequest create_index_request = 1;

    // create index response
    CreateIndexResponse create_index_response = 2;
  }

  google.rpc.Status status = 3;
}

[package]
name = "checkpoint_indexer_rs_proto"
version = "0.1.0"
edition = "2021"

[lib]
name = "checkpoint_indexer_rs_proto"
path = "checkpoint_indexer_proto.rs"

[dependencies]
prost = { workspace = true }
serde = { workspace = true }
tonic = { workspace = true }
tonic-build = { workspace = true }
numpy = { path = "../../base/rust/numpy" }
prost-wkt-types = {workspace = true}
prost-wkt = {workspace = true}

[build-dependencies]
prost-wkt-build = {workspace = true}
prost-build = {workspace = true}
tonic-build = { workspace = true }

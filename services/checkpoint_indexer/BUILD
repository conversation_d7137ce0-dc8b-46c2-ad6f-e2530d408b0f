load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:rust.bzl", "rust_library")

proto_library(
    name = "checkpoint_indexer_proto",
    srcs = ["checkpoint_indexer.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@googleapis//google/rpc:status_proto",
    ],
)

go_grpc_library(
    name = "checkpoint_indexer_go_proto",
    importpath = "github.com/augmentcode/augment/services/checkpoint_indexer/proto",
    proto = ":checkpoint_indexer_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
    ],
)

rust_library(
    name = "checkpoint_indexer_rs_proto",
    srcs = ["checkpoint_indexer_proto.rs"],
    aliases = aliases(),
    crate_name = "checkpoint_indexer_rs_proto",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":checkpoint_indexer_rs_proto_gen",
        "//base/rust/numpy",
    ],
)

cargo_build_script(
    name = "checkpoint_indexer_rs_proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        ":checkpoint_indexer_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

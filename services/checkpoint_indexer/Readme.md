# Checkpoint Indexer

## Overview

The Checkpoint Indexer is a service that creates and manages search indexes for embeddings associated with checkpoints. It takes embeddings from a checkpoint and builds an efficient search index using the ScaNN (Scalable Nearest Neighbors) algorithm, which enables fast similarity searches over the embeddings.

## Architecture

The service consists of the following main components:

- **CheckpointIndexerImpl**: The main service implementation that handles gRPC requests
- **IndexFactory**: Interface for creating indexes from embeddings
- **IndexFactoryImpl**: Implementation of the IndexFactory that uses ScaNN for index creation
- **PubsubListener**: Listens to PubSub messages for asynchronous index creation requests
- **CheckpointCache**: Caches checkpoint data to avoid repeated fetches
- **EmbeddingsCache**: Caches embeddings data to improve performance

## How It Works

1. **Index Creation Process**:
   - The service receives a request to create an index for a specific checkpoint and transformation key
   - It retrieves the checkpoint data and associated embeddings
   - The embeddings are processed by the ScaNN algorithm to create an efficient search index
   - The index is serialized and uploaded as assets
   - The index ID is returned to the caller

2. **Communication Methods**:
   - **gRPC API**: Synchronous API for direct index creation requests
   - **PubSub**: Asynchronous messaging for background index creation

## Configuration

The service is configured via a configuration file with the following key options:

- **bind_address**: The address to bind the gRPC server to
- **metrics_server_bind_address/port**: Configuration for the Prometheus metrics server
- **content_manager_endpoint**: Endpoint for the Content Manager service
- **tenant_watcher_endpoint**: Endpoint for the Tenant Watcher service
- **subscription_name**: PubSub subscription for receiving index creation requests
- **topic_name**: PubSub topic for publishing index creation responses
- **cache settings**: Configuration for checkpoint and embeddings caches
- **index factory settings**: Configuration for the ScaNN index creation

## API

### gRPC Endpoints

- **CreateIndex**: Creates an index for a given checkpoint and transformation key
  - Input: checkpoint_id, transformation_key, tenant_id (optional)
  - Output: index_id, checkpoint_id, transformation_key, tenant_id

### PubSub Messages

- **CreateIndexRequest**: Request to create an index (received on subscription)
- **CreateIndexResponse**: Response with the created index ID (published to topic)

## Integration

The Checkpoint Indexer integrates with the following services:

- **Content Manager**: For retrieving checkpoint and embeddings data
- **Tenant Watcher**: For tenant information and authentication
- **Token Exchange**: For authentication and authorization

## Metrics and Monitoring

The service exposes Prometheus metrics for monitoring:

- **Index creation latency**: Time taken to create indexes
- **Index operation counters**: Counts of index operations (create, upload)
- **PubSub message processing**: Metrics for PubSub message handling
- **Active requests**: Gauge of currently active requests
- **Response latency**: Latency of API responses

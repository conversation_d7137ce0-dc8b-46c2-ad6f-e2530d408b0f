use std::pin::pin;
use std::sync::Arc;
use std::time::Duration;
use std::time::Instant;

use async_trait::async_trait;
use checkpoint_indexer_rs_proto::checkpoint_indexer::checkpoint_indexer_pubsub;
use checkpoint_indexer_rs_proto::checkpoint_indexer::CheckpointIndexerPubsub;
use checkpoint_indexer_rs_proto::checkpoint_indexer::CreateIndexResponse;
use feature_flags::FeatureFlagsServiceHandle;
use futures::StreamExt;
use google_cloud_googleapis::pubsub::v1::PubsubMessage;
use google_cloud_pubsub::client::{Client, ClientConfig};
use grpc_auth::GrpcAuth;
use prost::Message;
use request_context::{RequestContext, RequestId, RequestSessionId, RequestSource};
use tenant_watcher_client::TenantCacheClient;
use tracing::{error, info, warn};

use crate::metrics::{
    PUBSUB_ACTIVE_MESSAGES, PUBSUB_MESSAGE_COUNTER, PUBSUB_MESSAGE_PROCESSING_LATENCY,
    PUBSUB_PUBLISH_COUNTER, PUBSUB_PUBLISH_LATENCY,
};
use crate::{config::Config, index_factory::IndexFactory};

const PUBSUB_ACK_DEADLINE_S_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("checkpoint_indexer_ack_deadline_s", 0);

#[async_trait]
trait PubsubResponsePublisher {
    async fn publish(&self, message: &CheckpointIndexerPubsub) -> Result<(), tonic::Status>;
}

#[derive(Clone)]
struct PubsubResponsePublisherImpl {
    publisher: google_cloud_pubsub::publisher::Publisher,
    tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
}

impl PubsubResponsePublisherImpl {
    pub async fn new(
        config: &Config,
        tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
    ) -> tonic::Result<Self> {
        // Create the pubsub client
        let client_config = ClientConfig::default().with_auth().await.map_err(|e| {
            error!("Failed to create pubsub client config: {}", e);
            tonic::Status::internal(format!("Failed to create pubsub client config: {}", e))
        })?;

        let client = Client::new(client_config).await.map_err(|e| {
            error!("Failed to create pubsub client: {}", e);
            tonic::Status::internal(format!("Failed to create pubsub client: {}", e))
        })?;

        let response_topic = client.topic(&config.topic_name);

        let publisher = response_topic.new_publisher(None);

        Ok(Self {
            publisher,
            tenant_cache,
        })
    }
}

#[async_trait]
impl PubsubResponsePublisher for PubsubResponsePublisherImpl {
    async fn publish(&self, message: &CheckpointIndexerPubsub) -> Result<(), tonic::Status> {
        let start_time = Instant::now();

        // Determine message type and tenant ID for metrics
        let (message_type, tenant_id) = match &message.message {
            Some(checkpoint_indexer_pubsub::Message::CreateIndexResponse(response)) => {
                ("create_index_response", response.tenant_id.clone())
            }
            Some(checkpoint_indexer_pubsub::Message::CreateIndexRequest(request)) => {
                ("create_index_request", request.tenant_id.clone())
            }
            None => ("unknown", String::from("unknown")),
        };

        // Get tenant name from tenant cache for metrics
        let tenant_name = if tenant_id == "unknown" {
            String::from("unknown")
        } else {
            match self.tenant_cache.get_tenant(&tenant_id).await {
                Some(tenant) => tenant.name,
                None => {
                    // If tenant not found in cache, use tenant ID as fallback
                    warn!(
                        "Tenant ID {} not found in tenant cache, using ID as name",
                        tenant_id
                    );
                    tenant_id.clone()
                }
            }
        };

        // Increment counter for publish attempt
        PUBSUB_PUBLISH_COUNTER
            .with_label_values(&[tenant_name.as_str(), message_type, "attempt"])
            .inc();

        let message = PubsubMessage {
            data: message.encode_to_vec(),
            ..Default::default()
        };

        let result = self
            .publisher
            .publish_immediately(vec![message], None)
            .await
            .map_err(|e| tonic::Status::internal(format!("Failed to publish message: {}", e)));

        // Record metrics based on result
        let duration = start_time.elapsed().as_secs_f64();
        let status = if result.is_ok() { "success" } else { "error" };

        PUBSUB_PUBLISH_LATENCY
            .with_label_values(&[tenant_name.as_str(), message_type, status])
            .observe(duration);

        PUBSUB_PUBLISH_COUNTER
            .with_label_values(&[tenant_name.as_str(), message_type, status])
            .inc();

        if let Err(e) = result {
            error!(
                "Failed to publish {} message for tenant {}: {} (took {:.2}s)",
                message_type, tenant_name, e, duration
            );
            Err(e)
        } else {
            info!(
                "Published {} message for tenant {} in {:.2}s",
                message_type, tenant_name, duration
            );
            Ok(())
        }
    }
}

/// PubsubListener listens to messages from a pubsub subscription and processes them.
#[derive(Clone)]
pub struct PubsubListener {
    config: Config,
    feature_flags: FeatureFlagsServiceHandle,
    index_factory: Arc<dyn IndexFactory + Send + Sync + 'static>,
    token_exchange_client: Arc<dyn token_exchange_client::TokenExchangeClient + Send + Sync>,
    request_context: RequestContext,
    grpc_auth: Arc<dyn GrpcAuth + Send + Sync>,
    response_publisher: Arc<dyn PubsubResponsePublisher + Send + Sync>,
    tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
}

impl PubsubListener {
    /// Create a new PubsubListener
    pub async fn new(
        config: Config,
        feature_flags: FeatureFlagsServiceHandle,
        index_factory: Arc<dyn IndexFactory + Send + Sync + 'static>,
        token_exchange_client: Arc<dyn token_exchange_client::TokenExchangeClient + Send + Sync>,
        grpc_auth: Arc<dyn GrpcAuth + Send + Sync>,
        tenant_cache: Arc<dyn TenantCacheClient + Send + Sync>,
    ) -> tonic::Result<Self> {
        let request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::create_random(),
            RequestSource::Background,
            None,
        );

        let response_publisher =
            Arc::new(PubsubResponsePublisherImpl::new(&config, tenant_cache.clone()).await?);

        Ok(Self {
            config,
            feature_flags,
            index_factory,
            token_exchange_client,
            request_context,
            grpc_auth,
            response_publisher,
            tenant_cache,
        })
    }

    /// Run the pubsub listener
    pub async fn run(&self) -> Result<(), tonic::Status> {
        info!(
            "Starting pubsub listener for subscription={}, response_topic={}",
            self.config.subscription_name, self.config.topic_name
        );

        // Create the pubsub client
        let client_config = ClientConfig::default().with_auth().await.map_err(|e| {
            error!("Failed to create pubsub client config: {}", e);
            tonic::Status::internal(format!("Failed to create pubsub client config: {}", e))
        })?;

        let client = Client::new(client_config).await.map_err(|e| {
            error!("Failed to create pubsub client: {}", e);
            tonic::Status::internal(format!("Failed to create pubsub client: {}", e))
        })?;
        // Get the subscription
        let subscription = client.subscription(&self.config.subscription_name);

        // Create a subscriber
        let mut subscriber = subscription.subscribe(None).await.map_err(|e| {
            error!("Failed to subscribe to pubsub subscription: {}", e);
            tonic::Status::internal(format!("Failed to subscribe to pubsub subscription: {}", e))
        })?;

        // Process messages
        loop {
            match subscriber.next().await {
                Some(message) => {
                    tracing::info!(
                        "Got message with delivery_attempt: {:?}",
                        message.delivery_attempt()
                    );
                    // setup message future (needs to be pinned to poll repeatedly)
                    let mut result_fut = pin!(self.process_message(&message.message));

                    let result;
                    let ack_deadline: i32 = PUBSUB_ACK_DEADLINE_S_FLAG
                        .get_from(&self.feature_flags)
                        .try_into()
                        .unwrap_or(0);
                    let extension_duration = if ack_deadline <= 0 {
                        u64::MAX
                    } else {
                        (ack_deadline / 2).try_into().unwrap()
                    };
                    let mut extension_interval =
                        tokio::time::interval(Duration::from_secs(extension_duration));
                    tracing::debug!(
                        "Ack deadline: {}s, renew interval: {}s",
                        ack_deadline,
                        extension_interval.period().as_secs()
                    );

                    // start and poll pubsub deadline renewal future and processing future
                    // concurrently. This should be safe because we anticipate the majority of
                    // process_message() to be spent either in async network calls or in the long
                    // running async index builder logic which is dispatched to the tokio blocking
                    // thread pool (which should not contend with the normal async executors).
                    loop {
                        tokio::select! {
                            biased;
                            _ = extension_interval.tick(), if ack_deadline > 0 => {
                                tracing::info!("Setting pub/sub ack deadline to {}s", ack_deadline);
                                if let Err(e) = message.modify_ack_deadline(ack_deadline).await {
                                    tracing::error!("Failed to extend pub/sub ack deadline: {:?}", e);
                                }
                            }
                            res = &mut result_fut => {
                                result = res;
                                break;
                            }
                        }
                    }

                    // handle the result of the message processing
                    match result {
                        Ok(Some(response)) => {
                            let pubsub_message = CheckpointIndexerPubsub {
                                message: Some(
                                    checkpoint_indexer_pubsub::Message::CreateIndexResponse(
                                        response.clone(),
                                    ),
                                ),
                                ..Default::default()
                            };
                            self.response_publisher.publish(&pubsub_message).await?;
                            // Acknowledge the message
                            if let Err(e) = message.ack().await {
                                error!("Failed to acknowledge message: {}", e);
                            }
                        }
                        Ok(None) => {
                            // Acknowledge the message
                            if let Err(e) = message.ack().await {
                                error!("Failed to acknowledge message: {}", e);
                            }
                        }
                        Err(e) => {
                            warn!("Failed to process message: {}", e);
                            // Negative acknowledge the message
                            if let Err(e) = message.nack().await {
                                error!("Failed to negative acknowledge message: {}", e);
                            }
                        }
                    };
                }
                None => {
                    error!("Pubsub subscription stream ended");
                    break;
                }
            }
        }

        Ok(())
    }

    async fn get_request_context(&self, tenant_id: &str) -> tonic::Result<RequestContext> {
        let token = self
            .token_exchange_client
            .get_signed_token_for_service(
                tenant_id.to_string(),
                &[token_exchange_client::token_exchange::Scope::ContentRw],
                &self.request_context,
            )
            .await?;
        Ok(self.request_context.clone().with_token(token.clone()))
    }

    /// Process a pubsub message
    async fn process_message(
        &self,
        message: &PubsubMessage,
    ) -> Result<Option<CreateIndexResponse>, tonic::Status> {
        let start_time = Instant::now();

        // Decode the message
        let message = match CheckpointIndexerPubsub::decode(message.data.as_slice()) {
            Ok(msg) => msg,
            Err(e) => {
                let error_msg = format!("Failed to decode CheckpointIndexerPubsub: {}", e);
                error!("{}", error_msg);

                // Record metrics for decode failure
                PUBSUB_MESSAGE_COUNTER
                    .with_label_values(&["unknown", "decode", "error"])
                    .inc();
                PUBSUB_MESSAGE_PROCESSING_LATENCY
                    .with_label_values(&["unknown", "decode", "error"])
                    .observe(start_time.elapsed().as_secs_f64());

                return Err(tonic::Status::invalid_argument(error_msg));
            }
        };

        // Extract message type and update metrics
        let create_index_request = match message.message {
            Some(checkpoint_indexer_pubsub::Message::CreateIndexResponse(_)) => {
                return Ok(None);
            }
            Some(checkpoint_indexer_pubsub::Message::CreateIndexRequest(create_index_request)) => {
                // Update message type for metrics
                create_index_request
            }
            _ => {
                error!("Unknown message type");
                // Record metrics for unknown message type
                PUBSUB_MESSAGE_COUNTER
                    .with_label_values(&["unknown", "unknown", "error"])
                    .inc();
                PUBSUB_MESSAGE_PROCESSING_LATENCY
                    .with_label_values(&["unknown", "unknown", "error"])
                    .observe(start_time.elapsed().as_secs_f64());
                return Err(tonic::Status::invalid_argument("Unknown message type"));
            }
        };

        info!(
            "Received CreateIndexRequest: checkpoint_id={}, transformation_key={}",
            create_index_request.checkpoint_id, create_index_request.transformation_key
        );

        // Get tenant name from tenant cache for metrics
        let tenant_id = create_index_request.tenant_id.clone();
        let tenant_name: String = match self.tenant_cache.get_tenant(&tenant_id).await {
            Some(tenant) => tenant.name,
            None => {
                // If tenant not found in cache, use tenant ID as fallback
                warn!(
                    "Tenant ID {} not found in tenant cache, using ID as name",
                    tenant_id
                );
                tenant_id.clone()
            }
        };

        // Increment counter for create_index_request message type
        PUBSUB_MESSAGE_COUNTER
            .with_label_values(&[tenant_name.as_str(), "create_index_request", "processing"])
            .inc();

        // Increment active messages gauge
        PUBSUB_ACTIVE_MESSAGES
            .with_label_values(&[tenant_name.as_str()])
            .inc();

        if create_index_request.tenant_id.is_empty() {
            error!("tenant_id is required");

            // Record metrics for missing tenant_id
            PUBSUB_MESSAGE_COUNTER
                .with_label_values(&["empty", "create_index_request", "error"])
                .inc();
            PUBSUB_MESSAGE_PROCESSING_LATENCY
                .with_label_values(&["empty", "create_index_request", "error"])
                .observe(start_time.elapsed().as_secs_f64());

            // Decrement active messages gauge
            PUBSUB_ACTIVE_MESSAGES.with_label_values(&["empty"]).dec();

            return Err(tonic::Status::invalid_argument("tenant_id is required"));
        }

        // Create a request context for the pubsub message with authentication token
        let request_context = match self
            .get_request_context(&create_index_request.tenant_id)
            .await
        {
            Ok(ctx) => ctx,
            Err(e) => {
                error!("Failed to get request context: {}", e);

                // Record metrics for request context error
                PUBSUB_MESSAGE_COUNTER
                    .with_label_values(&[
                        tenant_name.as_str(),
                        "create_index_request",
                        "auth_error",
                    ])
                    .inc();
                PUBSUB_MESSAGE_PROCESSING_LATENCY
                    .with_label_values(&[
                        tenant_name.as_str(),
                        "create_index_request",
                        "auth_error",
                    ])
                    .observe(start_time.elapsed().as_secs_f64());

                // Decrement active messages gauge
                PUBSUB_ACTIVE_MESSAGES
                    .with_label_values(&[tenant_name.as_str()])
                    .dec();

                return Err(e);
            }
        };

        // Create a tenant info for pubsub messages
        let tenant_info = match self
            .grpc_auth
            .validate_access(vec![], Some(request_context.token()), "")
            .await
        {
            Ok(info) => info,
            Err(e) => {
                error!("Failed to validate access: {}", e);

                // Record metrics for access validation error
                PUBSUB_MESSAGE_COUNTER
                    .with_label_values(&[
                        tenant_name.as_str(),
                        "create_index_request",
                        "access_error",
                    ])
                    .inc();
                PUBSUB_MESSAGE_PROCESSING_LATENCY
                    .with_label_values(&[
                        tenant_name.as_str(),
                        "create_index_request",
                        "access_error",
                    ])
                    .observe(start_time.elapsed().as_secs_f64());

                // Decrement active messages gauge
                PUBSUB_ACTIVE_MESSAGES
                    .with_label_values(&[tenant_name.as_str()])
                    .dec();

                return Err(e);
            }
        };

        // Call the index factory
        let index_result = self
            .index_factory
            .create_index(
                &request_context,
                &tenant_info,
                &create_index_request.checkpoint_id,
                &create_index_request.transformation_key,
            )
            .await;

        // Record metrics based on result
        let duration = start_time.elapsed().as_secs_f64();
        let status = if index_result.is_ok() {
            "success"
        } else {
            "error"
        };

        PUBSUB_MESSAGE_COUNTER
            .with_label_values(&[tenant_name.as_str(), "create_index_request", status])
            .inc();
        PUBSUB_MESSAGE_PROCESSING_LATENCY
            .with_label_values(&[tenant_name.as_str(), "create_index_request", status])
            .observe(duration);

        // Decrement active messages gauge
        PUBSUB_ACTIVE_MESSAGES
            .with_label_values(&[tenant_name.as_str()])
            .dec();

        match index_result {
            Ok(index_id) => {
                tracing::info!(
                    "Created index: index_id={} for tenant {} in {:.2}s",
                    index_id,
                    tenant_name,
                    duration
                );
                Ok(Some(CreateIndexResponse {
                    tenant_id: create_index_request.tenant_id,
                    checkpoint_id: create_index_request.checkpoint_id,
                    index_id,
                    transformation_key: create_index_request.transformation_key,
                }))
            }
            Err(e) => match e.code() {
                tonic::Code::InvalidArgument => {
                    tracing::warn!(
                        "Unsupported parameters for tenant {} checkpoint {} in {:.2}s: {}",
                        tenant_name,
                        create_index_request.checkpoint_id,
                        duration,
                        e
                    );
                    Ok(None)
                }
                tonic::Code::AlreadyExists => {
                    tracing::warn!(
                        "Index already exists for tenant {} checkpoint {} in {:.2}s: {}",
                        tenant_name,
                        create_index_request.checkpoint_id,
                        duration,
                        e
                    );
                    Ok(None)
                }
                tonic::Code::NotFound => {
                    tracing::warn!(
                        "Checkpoint not found for tenant {} checkpoint {} in {:.2}s: {}",
                        tenant_name,
                        create_index_request.checkpoint_id,
                        duration,
                        e
                    );
                    Ok(None)
                }
                _ => {
                    tracing::error!(
                        "Failed to create index for tenant {} checkpoint {} in {:.2}s: {}",
                        tenant_name,
                        create_index_request.checkpoint_id,
                        duration,
                        e
                    );
                    Err(e)
                }
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use async_trait::async_trait;
    use auth_entities_proto::auth_entities::UserId;
    use checkpoint_indexer_rs_proto::checkpoint_indexer::checkpoint_indexer_pubsub;
    use checkpoint_indexer_rs_proto::checkpoint_indexer::CheckpointIndexerPubsub;
    use checkpoint_indexer_rs_proto::checkpoint_indexer::CreateIndexRequest;
    use grpc_auth::GrpcAuth;
    use prost::Message;
    use request_context::{RequestContext, TenantInfo};
    use secrecy::{Secret, SecretString};
    use tenant_watcher_client::TenantCacheClient;
    use token_exchange_client::{token_exchange, TokenExchangeClient};

    use crate::index_factory::IndexFactory;

    use super::PubsubResponsePublisher;

    struct MockGrpcAuth {}
    #[async_trait]
    impl GrpcAuth for MockGrpcAuth {
        async fn validate_access(
            &self,
            _peer_identities: Vec<Vec<u8>>,
            _auth_token: Option<SecretString>,
            _method_name: &str,
        ) -> tonic::Result<TenantInfo> {
            Ok(TenantInfo::new_for_test())
        }
    }

    // Mock implementation of TokenExchangeClient for testing
    struct MockTokenExchangeClient {}

    impl MockTokenExchangeClient {
        fn new() -> Self {
            Self {}
        }
    }

    #[async_trait]
    impl TokenExchangeClient for MockTokenExchangeClient {
        async fn get_signed_token_for_service(
            &self,
            _tenant_id: String,
            _scopes: &[token_exchange::Scope],
            _request_context: &RequestContext,
        ) -> Result<SecretString, tonic::Status> {
            // Return a dummy token for testing
            Ok(SecretString::new("test-token".to_string()))
        }

        async fn get_signed_token_for_user(
            &self,
            _tenant_id: String,
            _user_id: UserId,
            _user_email: Option<String>,
            _scope: String,
            _request_context: &RequestContext,
        ) -> Result<Secret<String>, tonic::Status> {
            // Return a dummy token for testing
            Ok(Secret::new("test-user-token".to_string()))
        }

        async fn get_verification_key(&self) -> Result<Vec<u8>, tonic::Status> {
            // Return a dummy verification key for testing
            Ok(vec![0, 1, 2, 3])
        }
    }

    // Mock implementation of IndexFactory for testing
    struct MockIndexFactory {
        // Track calls to create_index
        pub create_index_called: std::sync::Mutex<Vec<(String, String)>>,
    }

    impl MockIndexFactory {
        fn new() -> Self {
            Self {
                create_index_called: std::sync::Mutex::new(Vec::new()),
            }
        }
    }

    struct MockPubsubResponsePublisher {}
    #[async_trait]
    impl PubsubResponsePublisher for MockPubsubResponsePublisher {
        async fn publish(&self, _message: &CheckpointIndexerPubsub) -> Result<(), tonic::Status> {
            Ok(())
        }
    }

    // Mock implementation of TenantCacheClient for testing
    struct MockTenantCacheClient {}

    impl MockTenantCacheClient {
        fn new() -> Self {
            Self {}
        }
    }

    #[async_trait]
    impl TenantCacheClient for MockTenantCacheClient {
        async fn get_tenant(
            &self,
            tenant_id: &str,
        ) -> Option<tenant_watcher_client::tenant_watcher::Tenant> {
            // Return a dummy tenant for testing
            Some(tenant_watcher_client::tenant_watcher::Tenant {
                id: tenant_id.to_string(),
                name: format!("tenant-{}", tenant_id),
                shard_namespace: "test-namespace".to_string(),
                cloud: "test-cloud".to_string(),
                tier: 0,
                auth_configuration: None,
                config: None,
                deleted_at: "".to_string(),
                other_namespace: "".to_string(),
                encryption_key_name: "".to_string(),
                encryption_key_ttl: None,
                version: "".to_string(),
            })
        }

        async fn get_tenant_by_name(
            &self,
            _tenant_name: &str,
        ) -> Option<tenant_watcher_client::tenant_watcher::Tenant> {
            None
        }

        async fn run(&self) -> Result<(), tonic::Status> {
            Ok(())
        }

        async fn wait_until_initialized(&self) -> Result<(), tonic::Status> {
            Ok(())
        }

        async fn wait_until_initialized_with_timeout(
            &self,
            _timeout: std::time::Duration,
        ) -> Result<(), tonic::Status> {
            Ok(())
        }
    }

    #[async_trait]
    impl IndexFactory for MockIndexFactory {
        async fn create_index(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            checkpoint_id: &str,
            transformation_key: &str,
        ) -> Result<String, tonic::Status> {
            // Record the call
            let mut calls = self.create_index_called.lock().unwrap();
            calls.push((checkpoint_id.to_string(), transformation_key.to_string()));
            Ok(checkpoint_id.to_string())
        }
    }

    #[tokio::test]
    async fn test_process_message() {
        // Create a mock index factory
        let mock_factory = Arc::new(MockIndexFactory::new());

        // Create a mock token exchange client
        let token_exchange_client = Arc::new(MockTokenExchangeClient::new());

        // Create a mock tenant cache
        let tenant_cache = Arc::new(MockTenantCacheClient::new());

        // Create a mock response publisher
        let response_publisher = Arc::new(MockPubsubResponsePublisher {});

        // Create a PubsubListener instance
        let pubsub_listener = super::PubsubListener {
            config: crate::config::Config::default(),
            feature_flags: feature_flags::setup_local(),
            index_factory: mock_factory.clone(),
            token_exchange_client,
            request_context: RequestContext::new_for_test(),
            grpc_auth: Arc::new(MockGrpcAuth {}),
            response_publisher,
            tenant_cache,
        };

        // Create a CreateIndexRequest
        let request = CreateIndexRequest {
            tenant_id: "test-tenant".to_string(),
            checkpoint_id: "test-checkpoint".to_string(),
            transformation_key: "test-transformation".to_string(),
        };
        let pubsub_message = CheckpointIndexerPubsub {
            message: Some(checkpoint_indexer_pubsub::Message::CreateIndexRequest(
                request,
            )),
            ..Default::default()
        };

        // Encode the request
        let encoded_request = pubsub_message.encode_to_vec();

        // Create a PubsubMessage
        let message = google_cloud_googleapis::pubsub::v1::PubsubMessage {
            data: encoded_request,
            ..Default::default()
        };

        // Process the message
        let result = pubsub_listener.process_message(&message).await;

        // Check that the result is Ok
        assert!(result.is_ok());

        // Check that create_index was called with the correct parameters
        let calls = mock_factory.create_index_called.lock().unwrap();
        assert_eq!(calls.len(), 1);
        assert_eq!(calls[0].0, "test-checkpoint");
        assert_eq!(calls[0].1, "test-transformation");
    }
}

use lazy_static::lazy_static;
use prometheus::{
    register_gauge_vec, register_histogram_vec, register_int_counter_vec, register_int_gauge_vec,
    GaugeVec, HistogramVec, IntCounterVec, IntGaugeVec, Opts,
};

#[derive(Debug, PartialEq, Eq)]
pub enum CacheType {
    Checkpoint,
    Embeddings,
}

impl CacheType {
    pub fn as_str(&self) -> &'static str {
        match self {
            CacheType::Checkpoint => "checkpoint",
            CacheType::Embeddings => "embeddings",
        }
    }
}

lazy_static! {
    pub static ref RESPONSE_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_rpc_latency_histogram",
        "Histogram of RPC latencies",
        &["service", "endpoint", "status_code", "request_source", "tenant_name"]
    )
    .expect("metric can be created");
    pub static ref ACTIVE_REQUESTS_COLLECTOR: IntGaugeVec = register_int_gauge_vec!(
        // Keep this in sync with base/python/grpc/metrics.py please
        "au_active_requests_gauge",
        "The number of currently active requests",
        &["service", "endpoint", "tenant_name"],
    )
    .expect("metric can be created");
    pub static ref CACHE_LOOKUP_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_checkpoint_indexer_cache_lookup_count",
            "Cache lookup count"
        ),
        &["cache_type", "result", "tenant_name"]
    )
    .expect("metric can be created");
    pub static ref CACHE_EVICTION_COUNT_COLLECTOR: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_checkpoint_indexer_cache_eviction_count",
            "Cache eviction count"
        ),
        &["cache_type", "removal_cause"]
    )
    .expect("metric can be created");
    pub static ref CACHE_ENTRY_COUNT: GaugeVec = register_gauge_vec!(
        "au_checkpoint_indexer_cache_entry_count",
        "Number of entries in the cache",
        &["cache_type"]
    )
    .expect("metric can be created");
    pub static ref CACHE_BYTES_COUNT: GaugeVec = register_gauge_vec!(
        "au_checkpoint_indexer_cache_bytes_count",
        "Number of bytes in the cache",
        &["cache_type"]
    )
    .expect("metric can be created");
    pub static ref CHECKPOINT_GET_COUNTER: IntCounterVec = register_int_counter_vec!(
    Opts::new(
        "au_checkpoint_indexer_checkpoint_get_count",
        "Number of blob checkpoints fetched from content manager"
    ),
    &["tenant_name"]
    )
    .expect("metric can be created");
    pub static ref CHECKPOINT_GET_BLOBS_COUNTER: IntCounterVec = register_int_counter_vec!(
    Opts::new(
        "au_checkpoint_indexer_checkpoint_get_blobs_count",
        "Number of fetched checkpoint blob names"
    ),
    &["tenant_name"]
    )
    .expect("metric can be created");
    pub static ref CHECKPOINT_GET_LATENCY_COLLECTOR: HistogramVec = register_histogram_vec!(
        "au_checkpoint_indexer_checkpoint_get_latency",
        "The checkpoint indexer blob checkpoint get latencies in seconds",
        &["tenant_name"],
        vec![1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0],
    )
    .expect("metric can be created");

    // Index factory metrics
    pub static ref INDEX_EMBEDDINGS_COUNT: IntGaugeVec = register_int_gauge_vec!(
        Opts::new(
            "au_checkpoint_indexer_index_embeddings_count",
            "Number of embeddings in the index"
        ),
        &["tenant_name", "transformation_key"]
    )
    .expect("metric can be created");
    pub static ref INDEX_EMBEDDINGS_SIZE_BYTES: IntGaugeVec = register_int_gauge_vec!(
        Opts::new(
            "au_checkpoint_indexer_index_embeddings_size_bytes",
            "Size of embeddings in bytes"
        ),
        &["tenant_name", "transformation_key"]
    )
    .expect("metric can be created");
    pub static ref INDEX_CREATION_LATENCY: HistogramVec = register_histogram_vec!(
        "au_checkpoint_indexer_index_creation_latency",
        "Time taken to create the index in seconds",
        &["tenant_name", "transformation_key", "status"],
        vec![1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0],
    )
    .expect("metric can be created");
    pub static ref INDEX_ASSET_UPLOAD_LATENCY: HistogramVec = register_histogram_vec!(
        "au_checkpoint_indexer_index_asset_upload_latency",
        "Time taken to upload index assets in seconds",
        &["tenant_name", "transformation_key", "status"],
        vec![1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0],
    )
    .expect("metric can be created");
    pub static ref INDEX_METADATA_UPLOAD_LATENCY: HistogramVec = register_histogram_vec!(
        "au_checkpoint_indexer_index_metadata_upload_latency",
        "Time taken to upload index metadata in seconds",
        &["tenant_name", "transformation_key", "status"],
        vec![1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0],
    )
    .expect("metric can be created");
    pub static ref INDEX_OPERATION_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_checkpoint_indexer_index_operation_count",
            "Count of index operations by type and status"
        ),
        &["tenant_name", "transformation_key", "operation", "status"]
    )
    .expect("metric can be created");

    // Pubsub listener metrics
    pub static ref PUBSUB_MESSAGE_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_checkpoint_indexer_pubsub_message_count",
            "Count of pubsub messages received by type and status"
        ),
        &["tenant_name", "message_type", "status"]
    )
    .expect("metric can be created");
    pub static ref PUBSUB_MESSAGE_PROCESSING_LATENCY: HistogramVec = register_histogram_vec!(
        "au_checkpoint_indexer_pubsub_message_processing_latency",
        "Time taken to process pubsub messages in seconds",
        &["tenant_name", "message_type", "status"],
        vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0],
    )
    .expect("metric can be created");
    pub static ref PUBSUB_ACTIVE_MESSAGES: IntGaugeVec = register_int_gauge_vec!(
        Opts::new(
            "au_checkpoint_indexer_pubsub_active_messages",
            "Number of pubsub messages currently being processed"
        ),
        &["tenant_name"]
    )
    .expect("metric can be created");
    pub static ref PUBSUB_PUBLISH_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_checkpoint_indexer_pubsub_publish_count",
            "Count of pubsub messages published by type and status"
        ),
        &["tenant_name", "message_type", "status"]
    )
    .expect("metric can be created");
    pub static ref PUBSUB_PUBLISH_LATENCY: HistogramVec = register_histogram_vec!(
        "au_checkpoint_indexer_pubsub_publish_latency",
        "Time taken to publish pubsub messages in seconds",
        &["tenant_name", "message_type", "status"],
        vec![0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
    )
    .expect("metric can be created");
}

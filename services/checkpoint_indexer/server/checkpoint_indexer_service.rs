use std::sync::Arc;

use checkpoint_indexer_rs_proto::checkpoint_indexer::checkpoint_indexer_server::CheckpointIndexer;
use checkpoint_indexer_rs_proto::checkpoint_indexer::checkpoint_indexer_server::CheckpointIndexerServer;
use checkpoint_indexer_rs_proto::checkpoint_indexer::{CreateIndexRequest, CreateIndexResponse};
use grpc_auth::tenant_info_from_grpc_req;
use grpc_service::log_response_fn;
use request_context::RequestContext;
use request_context::TenantId;
use request_context::TokenScope;
use tracing::Instrument;

use crate::config::Config;
use crate::index_factory::IndexFactory;

// CheckpointIndexerImpl is the implementation of the CheckpointIndexer service
#[derive(Clone)]
pub struct CheckpointIndexerImpl {
    _config: Config,
    factory: Arc<dyn IndexFactory + Send + Sync + 'static>,
}

impl CheckpointIndexerImpl {
    // Create a new CheckpointIndexerServer service
    pub fn new(config: Config, factory: Arc<dyn IndexFactory + Send + Sync + 'static>) -> Self {
        CheckpointIndexerImpl {
            _config: config.clone(),
            factory,
        }
    }

    // Create a new CheckpointIndexerServer server
    pub fn new_server(self) -> CheckpointIndexerServer<Self> {
        CheckpointIndexerServer::new(self)
    }
}

#[tonic::async_trait]
impl CheckpointIndexer for CheckpointIndexerImpl {
    async fn create_index(
        &self,
        request: tonic::Request<CreateIndexRequest>,
    ) -> Result<tonic::Response<CreateIndexResponse>, tonic::Status> {
        // get request context
        let request_context = RequestContext::try_from(request.metadata())?;

        // get tenant info
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;

        // create span for logging
        let span = tracing::info_span!("create index", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                let inner_request = request.into_inner();
                tracing::info!("create_index request {:?}", inner_request);

                tenant_info.validate_scope(TokenScope::ContentRw)?;

                if inner_request.transformation_key.is_empty() {
                    return Err(tonic::Status::invalid_argument(
                        "transformation_key must not be empty",
                    ));
                }

                let tenant_id = if !inner_request.tenant_id.is_empty() {
                    if let Some(token_tenant_id) = tenant_info.tenant_id.as_ref() {
                        if inner_request.tenant_id != token_tenant_id.to_string() {
                            return Err(tonic::Status::invalid_argument(
                                "tenant_id mismatch between token and request",
                            ));
                        }
                    };
                    TenantId::new(inner_request.tenant_id)
                } else if let Some(tenant_id) = tenant_info.tenant_id.as_ref() {
                    tenant_id.clone()
                } else {
                    return Err(tonic::Status::invalid_argument("tenant_id must be set"));
                };

                let index_id = self
                    .factory
                    .create_index(
                        &request_context,
                        &tenant_info,
                        &inner_request.checkpoint_id,
                        &inner_request.transformation_key,
                    )
                    .await?;
                Ok(tonic::Response::new(CreateIndexResponse {
                    tenant_id: tenant_id.to_string(),
                    checkpoint_id: inner_request.checkpoint_id,
                    index_id,
                    transformation_key: inner_request.transformation_key,
                }))
            },
            "create_index",
        )
        .instrument(span)
        .await
    }
}

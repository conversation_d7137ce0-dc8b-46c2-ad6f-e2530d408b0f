use clap::Parser;
use grpc_tls_config::TlsConfig;
use serde::{Deserialize, Serialize};
use std::{fs::File, path::Path};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AuthConfig {
    pub token_exchange_endpoint: String,
    pub token_exchange_request_timeout_s: f32,
}

/// structure representing the configuration information in the configuration file, i.e.
/// the configmap of the embeddings search pod.
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Config {
    // the address to bind the rpc server to, e.g. 0.0.0.0:50051
    pub bind_address: String,

    // Configure the HTTP server that returns Prometheus metrics.
    pub metrics_server_bind_address: String,
    pub metrics_server_port: u16,

    // populated if server MTLS should be used.
    pub server_mtls_config: Option<TlsConfig>,
    // populated if central client MTLS should be used.
    pub central_client_mtls_config: Option<TlsConfig>,
    // populated if client MTLS should be used.
    pub client_mtls_config: Option<TlsConfig>,

    // the configuration for the token authentication
    pub auth_config: AuthConfig,

    // the endpoint for the dynamic feature flags service or None if not used
    pub dynamic_feature_flags_endpoint: Option<String>,

    // the endpoint for the content manager
    pub content_manager_endpoint: String,

    // the timeout for requests to the content manager in seconds
    pub content_manager_request_timeout_s: f32,

    // the endpoint for the tenant watcher
    pub tenant_watcher_endpoint: String,

    // the timeout for requests to the tenant watcher in seconds
    pub tenant_watcher_request_timeout_s: f32,

    // the size of the checkpoint cache in bytes
    pub checkpoint_cache_size_bytes: u64,

    // the time to idle for the cache in seconds
    pub cache_tti_seconds: u64,

    // the size of the embeddings cache in bytes
    pub embeddings_cache_size_bytes: u64,

    // the name of the pubsub subscription to listen to for checkpoint indexing messages
    pub subscription_name: String,

    // the name of the pubsub topic to publish checkpoint indexing responses to
    pub topic_name: String,

    // Index factory configuration

    // number of threads to use for training the index
    pub training_threads: Option<usize>,

    // number of dimensions per block, usually 8 is a good value
    pub dims_per_block: Option<usize>,

    // threshold for asymmetric hashing, usually 0.2 is a good value
    pub ah_threshold: Option<f32>,

    // number of training samples to use to train the code book
    pub training_sample_size: Option<usize>,

    // Threshold of missing blobs to disable index creation
    pub max_missing_blobs: Option<usize>,

    // threshold of embeddings to disable index creation
    pub max_embeddings_to_index: Option<usize>,

    // threshold of blobs to disable index creation
    pub max_blobs_to_index: Option<usize>,
}

#[cfg(test)]
impl Default for Config {
    fn default() -> Self {
        Self {
            bind_address: "0.0.0.0:50051".to_string(),
            metrics_server_bind_address: "0.0.0.0".to_string(),
            metrics_server_port: 8080,

            server_mtls_config: None,
            central_client_mtls_config: None,
            client_mtls_config: None,

            auth_config: AuthConfig {
                token_exchange_endpoint: "".to_string(),
                token_exchange_request_timeout_s: 10.0,
            },
            dynamic_feature_flags_endpoint: None,

            content_manager_endpoint: "".to_string(),
            content_manager_request_timeout_s: 10.0,

            tenant_watcher_endpoint: "".to_string(),
            tenant_watcher_request_timeout_s: 10.0,

            checkpoint_cache_size_bytes: 256 * 1024 * 1024,
            cache_tti_seconds: 3 * 24 * 60 * 60, // 3 days -- enough for the weekend

            embeddings_cache_size_bytes: 256 * 1024 * 1024,

            subscription_name: "".to_string(),
            topic_name: "".to_string(),

            // Index factory configuration defaults
            training_threads: None,
            dims_per_block: None,
            ah_threshold: None,
            training_sample_size: None,

            max_missing_blobs: Some(1000),
            max_embeddings_to_index: Some(10000000),
            max_blobs_to_index: Some(500000),
        }
    }
}

impl Config {
    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<Config, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: Config =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(config)
    }
}

/// Search for a pattern in a file and display the lines that contain it.
#[derive(Parser, Debug)]
pub struct CliArguments {
    /// path to the configuration file
    #[arg(long)]
    pub config_file: std::path::PathBuf,

    /// optional path to the launch darkly secrets
    #[arg(long)]
    pub launch_darkly_secrets_file: Option<std::path::PathBuf>,
}

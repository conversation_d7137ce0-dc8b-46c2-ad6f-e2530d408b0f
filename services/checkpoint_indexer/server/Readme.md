# Checkpoint Indexer Server

## Overview

The Checkpoint Indexer Server is a Rust implementation of the Checkpoint Indexer service. It serves as a sidecar for the working set service that creates checkpoint indexes. The index creation functionality is implemented in Rust using the ScaNN algorithm, which provides efficient approximate nearest neighbor search capabilities.

## Components

### CheckpointIndexerImpl

The main service implementation that handles gRPC requests for index creation. It validates requests, processes them through the IndexFactory, and returns responses.

### IndexFactory and IndexFactoryImpl

- **IndexFactory**: A trait (interface) that defines the contract for creating indexes
- **IndexFactoryImpl**: The concrete implementation that:
  - Retrieves embeddings from the checkpoint cache
  - Processes embeddings using the ScaNN algorithm
  - Serializes and uploads the resulting index

### PubsubListener

Listens to PubSub messages for asynchronous index creation requests. It:
- Subscribes to a configured PubSub subscription
- Processes incoming CreateIndexRequest messages
- Calls the IndexFactory to create indexes
- Publishes CreateIndexResponse messages to a configured topic

### Caching

- **CheckpointCache**: Caches checkpoint data to reduce load on the Content Manager
- **EmbeddingsCache**: Caches embeddings to improve performance of index creation

## Configuration

The server is configured via a configuration file and command-line arguments. Key configuration options include:

- Server binding and TLS settings
- Service endpoints (Content Manager, Tenant Watcher, etc.)
- Cache sizes and TTL values
- PubSub subscription and topic names
- Index factory parameters (training threads, dimensions per block, etc.)

## Deployment

The server is deployed as a Kubernetes service using Bazel-based deployment. The deployment configuration is defined in `METADATA.jsonnet` with the following key aspects:

- Deployment name: `checkpoint-indexer`
- Target: `//services/checkpoint_indexer/server:kubecfg`
- Health tier: `TIER_1_B`
- Experts: Team Auth and Repo Scale

## Development

### Building and Testing

The server can be built and tested using Bazel:

```bash
# Build
bazel build //services/checkpoint_indexer/server:server

# Test
bazel test //services/checkpoint_indexer/server:all
```

### Local Development

For local development, you can run the server with a local configuration file:

```bash
bazel run //services/checkpoint_indexer/server:server -- --config_file=/path/to/config.json
```

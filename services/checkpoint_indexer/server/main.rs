use std::{net::SocketAddr, sync::Arc, time::Duration};

use checkpoint_cache::CheckpointCache;
use clap::Parser;
use config::CliArguments;
use grpc_auth::GrpcAuthMiddlewareLayer;
use grpc_metrics::MetricsMiddlewareLayer;
use grpc_tls_config::{get_client_tls_creds, get_server_tls_creds};
use index_factory::{FactoryConfig, IndexFactory, IndexFactoryImpl};
use struct_logging::setup_struct_logging;
use tenant_watcher_client::{TenantCache, TenantCacheClient, TenantWatcherClientImpl};
use token_exchange_client::{TokenExchangeClientImpl, TokenGrpcAuth};
use tokio::signal::unix::{signal, SignalKind};
use tonic::transport::Server;

use crate::config::Config;
use crate::metrics::{ACTIVE_REQUESTS_COLLECTOR, RESPONSE_LATENCY_COLLECTOR};

mod checkpoint_cache;
mod checkpoint_indexer_service;
mod config;
mod embeddings_cache;
mod index_factory;
mod metrics;
mod pubsub_listener;
mod test;

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    // Load config
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    // setup feature flags
    // Remove underscores from feature flags stuff when it's actually used
    let feature_flags = feature_flags::setup(
        "checkpoint_indexer",
        "0.0.0",
        args.launch_darkly_secrets_file.as_ref(),
        config.dynamic_feature_flags_endpoint.as_deref(),
    )
    .await;
    let _registry = feature_flags::new_registry();

    let namespace = match std::env::var("POD_NAMESPACE") {
        Ok(name) => name,
        Err(_) => panic!("POD_NAMESPACE environment variable must be set."),
    };

    // setup health service
    let (_health_reporter, health_service) = tonic_health::server::health_reporter();

    metrics_server::setup_default_metrics();

    // setup metrics server
    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    // setup server
    let server_tls_config =
        get_server_tls_creds(&config.server_mtls_config).expect("Failed to create TLS config");

    let addr: SocketAddr = config.bind_address.parse()?;
    let server = match server_tls_config {
        None => Server::builder(),
        Some(server_tls_config) => Server::builder()
            .tls_config(server_tls_config)
            .expect("Failed to create rpc server"),
    };

    let client_tls_config =
        get_client_tls_creds(&config.client_mtls_config).expect("Failed to create TLS config");

    let content_manager = Arc::new(content_manager_client::ContentManagerClientImpl::new(
        &config.content_manager_endpoint,
        client_tls_config.clone(),
        Some(Duration::from_secs_f32(
            config.content_manager_request_timeout_s,
        )),
    ));

    // setup token exchange client
    let central_client_tls_config = get_client_tls_creds(&config.central_client_mtls_config)
        .expect("Failed to create TLS config");

    // setup token exchange client
    let token_exchange_client = Arc::new(TokenExchangeClientImpl::new(
        &config.auth_config.token_exchange_endpoint,
        namespace.to_string(),
        central_client_tls_config.clone(),
        Duration::from_secs_f32(config.auth_config.token_exchange_request_timeout_s),
    ));

    // setup tenant watcher client
    let tenant_watcher_client = Arc::new(TenantWatcherClientImpl::new(
        &config.tenant_watcher_endpoint,
        central_client_tls_config.clone(),
        Duration::from_secs_f32(config.tenant_watcher_request_timeout_s),
    ));

    // setup tenant cache
    let tenant_cache: Arc<dyn TenantCacheClient + Send + Sync> = Arc::new(TenantCache::new(
        tenant_watcher_client,
        namespace.to_string(),
    ));

    // setup grpc auth
    let grpc_auth = Arc::new(TokenGrpcAuth::new(
        token_exchange_client.clone(),
        vec![request_context::TokenScope::ContentR],
    ));

    let checkpoint_cache: Arc<dyn CheckpointCache + Send + Sync + 'static> = Arc::new(
        checkpoint_cache::CheckpointCacheImpl::new(config.clone(), content_manager.clone()),
    );

    let embeddings_cache = Arc::new(embeddings_cache::EmbeddingsCacheImpl::new(
        config.clone(),
        content_manager.clone(),
    ));

    // Create factory config from the config file or use defaults
    let mut factory_config = FactoryConfig::default();

    // Override with values from config if provided
    if let Some(val) = config.training_threads {
        factory_config.training_threads = val;
    }

    if let Some(val) = config.dims_per_block {
        factory_config.dims_per_block = val;
    }

    if let Some(val) = config.ah_threshold {
        factory_config.ah_threadhold = val;
    }

    if let Some(val) = config.training_sample_size {
        factory_config.training_sample_size = val;
    }

    if let Some(val) = config.max_missing_blobs {
        factory_config.max_missing_blobs = val;
    }

    if let Some(val) = config.max_embeddings_to_index {
        factory_config.max_embeddings_to_index = val;
    }

    if let Some(val) = config.max_blobs_to_index {
        factory_config.max_blobs_to_index = val;
    }

    tracing::info!("Using factory config: {:?}", factory_config);

    let index_factory: Arc<dyn IndexFactory + Send + Sync + 'static> = Arc::new(
        IndexFactoryImpl::new(
            factory_config,
            content_manager,
            checkpoint_cache,
            embeddings_cache,
        )
        .expect("Failed to create index factory"),
    );

    let checkpoint_indexer_service = checkpoint_indexer_service::CheckpointIndexerImpl::new(
        config.clone(),
        index_factory.clone(),
    );

    let reflection_service: tonic_reflection::server::ServerReflectionServer<_> =
        tonic_reflection::server::Builder::configure()
            .register_encoded_file_descriptor_set(checkpoint_indexer_rs_proto::FILE_DESCRIPTOR_SET)
            .build_v1()?;

    // setup server
    let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

    let listener = tokio::net::TcpListener::bind(addr)
        .await
        .expect("Failed to bind");
    tracing::info!(
        "Listening on {:?}",
        listener.local_addr().expect("Failed to get local address")
    );

    let server = server
        .timeout(Duration::from_secs(300))
        .trace_fn(tracing_tonic::server::trace_fn)
        // layers
        // - metrics
        // - grpc auth
        .layer(
            tower::ServiceBuilder::new()
                .layer(MetricsMiddlewareLayer::new(
                    &RESPONSE_LATENCY_COLLECTOR,
                    &ACTIVE_REQUESTS_COLLECTOR,
                ))
                .layer(GrpcAuthMiddlewareLayer::new(grpc_auth.clone()))
                .into_inner(),
        )
        .add_service(health_service)
        .add_service(checkpoint_indexer_service.new_server())
        .add_service(reflection_service)
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(listener),
            async move {
                sigterm_notifier.recv().await;
            },
        );

    // Start pubsub listener if subscription name is provided
    let pubsub_listener = pubsub_listener::PubsubListener::new(
        config.clone(),
        feature_flags,
        index_factory.clone(),
        token_exchange_client.clone(),
        grpc_auth.clone(),
        tenant_cache.clone(),
    )
    .await
    .expect("Failed to create pubsub listener");
    let pubsub_tenant_cache = tenant_cache.clone();
    let pubsub_future = tokio::spawn(async move {
        tracing::info!("Starting pubsub listener");
        if let Err(e) = pubsub_tenant_cache
            .wait_until_initialized_with_timeout(Duration::from_secs(60))
            .await
        {
            tracing::error!("Failed to wait for tenant cache to be initialized: {}", e);
        }
        if let Err(e) = pubsub_listener.run().await {
            tracing::error!("Pubsub listener failed: {}", e);
        }
    });

    tokio::select! {
        server_res = server => {
            panic!("gRPC server terminated: {server_res:?}");
        },
        metrics_res = metrics_server => {
            panic!("Metrics server terminated: {metrics_res:?}");
        },
        tenant_cache = tenant_cache.run() => {
            panic!("tenant_cache failed: {tenant_cache:?}");
        }
        _ = pubsub_future => {
            panic!("Pubsub listener terminated");
        }
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}

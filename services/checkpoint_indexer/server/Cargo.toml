[package]
name = "checkpoint_indexer_server"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "checkpoint_indexer_server"
path = "main.rs"

[dependencies]
bytes = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tonic = { workspace = true , features = ["tls", "gzip", "tls-roots"] }
tonic-build = { workspace = true }
async-trait = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true }
async-std = "*"
futures = { workspace = true }
async-rwlock = { workspace = true }
itertools = { workspace = true }
clap = { workspace = true }
prometheus = { workspace = true }
secrecy = { workspace = true }
lazy_static = { workspace = true }
uuid = { workspace = true }
tracing = { workspace = true }
prost = {workspace = true}
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
async-lock = { workspace = true }
moka = { workspace = true }
pin-project = { workspace = true }
tonic-health = { workspace = true }
tonic-reflection = { workspace = true }
chrono = { workspace = true }
sha256 = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
hyper = { workspace = true }
tower = { workspace = true }
struct_logging = { path = "../../../base/logging" }
metrics-server = { path = "../../../base/metrics_server/rust" }
grpc_metrics = { path = "../../lib/grpc/metrics" }
grpc_auth = { path = "../../lib/grpc/auth" }
grpc_tls_config = { path = "../../lib/grpc/tls_config" }
token_exchange_client = { path = "../../token_exchange/client" }
request_context = { path = "../../lib/request_context" }
half = { version = "2.3.1", features = ["serde", "zerocopy", "bytemuck"] }
scann_rs = { path = "../../../third_party/scann_rs" }
grpc_service = { path = "../../lib/grpc/service" }
google-cloud-pubsub = { workspace = true }
google-cloud-googleapis = { workspace = true }
tenant_watcher_client = { path = "../../tenant_watcher/client" }
auth_entities_proto = { path = "../../auth/central/server" }
blob_names_rs_proto = { path = "../../../base/blob_names" }
checkpoint_indexer_rs_proto = { path = "../../checkpoint_indexer" }
feature-flags = { path = "../../../base/feature_flags" }
numpy = { path = "../../../base/rust/numpy" }
blob_names = { path = "../../../base/blob_names/rust" }
content_manager_client = { path = "../../content_manager/client" }
content_manager_rs_proto = { path = "../../content_manager" }

[dev-dependencies]
actix-rt =  { workspace = true }
assert_unordered =  { workspace = true }
tonic-build = { workspace = true }
prost-build = {workspace = true}

[build-dependencies]
tonic-build = { workspace = true }

[profile.release]
debug = false
debug-assertions = false
incremental = false
lto = false
opt-level = 3
overflow-checks = false

#[cfg(test)]
pub fn get_test_data_path(path: &str) -> std::path::PathBuf {
    use std::env;
    use std::fs;
    use std::path::PathBuf;

    let p = match env::var("BAZEL_TEST") {
        // test data is always available from the current working directory
        Ok(_) => path.into(),
        Err(_) => {
            // in cargo, the CARGO_MANIFEST_DIR environment variable is used to find the repo root
            let mut manifest_path = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
            manifest_path.push("../../..");
            manifest_path.push(path);
            manifest_path
        }
    };
    if let Err(e) = fs::metadata(&p) {
        panic!("Test data not found: {} error={:?}", p.to_str().unwrap(), e);
    }
    p
}

load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "file_retriever",
    srcs = ["file_retriever.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/diff_utils",
        "//services/content_manager/client:content_cache",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
    ],
)

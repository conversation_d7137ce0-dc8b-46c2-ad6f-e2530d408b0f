from typing import Iterable, Mapping

from base.blob_names.python.blob_names import <PERSON><PERSON><PERSON><PERSON><PERSON>, FilePath
from base.diff_utils.diff_utils import File
from services.content_manager.client import content_cache
from services.content_manager.client.content_manager_client import (
    ContentKey,
    ContentManagerClient,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext


class FileRetriever:
    def __init__(
        self, content_manager_client: ContentManagerClient, content_cache_size_mb: int
    ):
        self.content_cache = content_cache.LRUContentCache(
            content_manager=content_manager_client,
            transform_from_bytes=lambda _, bytes: bytes.decode("utf-8"),
            max_size=content_cache_size_mb * (2**20),
            max_elem_size=None,
            max_missing_entries_to_download=None,
        )

    def retrieve_indexed_files(
        self,
        blob_name_to_file_path: Mapping[<PERSON><PERSON>b<PERSON>ame, FilePath],
        expected: bool,
        request_context: RequestContext,
        auth_info: AuthInfo,
        missing_blobs: set[BlobName],
    ) -> dict[BlobName, File | None]:
        """Retrieves files from the content manager.

        Args:
            blob_name_to_file_path: a dict of [blob name, path] mappings to retrieve.
                Blob names are used to retrieve the contents, and paths are used to complete the File object.
            request_context: The request context for the request.
            auth_info: The auth info for the request.
            expected: if True, the blob will be added to missing_blobs if not found in content manager.
            missing_blobs: The set of missing blobs that is updated in this function.

        Side-effects:
            Any blob not found is added to missing_blobs if expected = true.

        Returns:
            A dictionary mapping each blob name to a File object if the file can be found, and None otherwise.
        """

        blob_names = list(blob_name_to_file_path.keys())

        tenant_id = auth_info.tenant_id if auth_info.tenant_id else ""

        retrieved_file_contents: Iterable[str | None] = self.content_cache.get(
            [(tenant_id, ContentKey(blob_name)) for blob_name in blob_names],
            request_context,
            auth_info,
        )

        blob_name_to_file: dict[BlobName, File | None] = {}

        for (blob_name, file_path), file_content in zip(
            blob_name_to_file_path.items(), retrieved_file_contents
        ):
            blob_name_to_file[blob_name] = (
                File(path=file_path, contents=file_content) if file_content else None
            )

            if expected and file_content is None:
                missing_blobs.add(blob_name)

        return blob_name_to_file

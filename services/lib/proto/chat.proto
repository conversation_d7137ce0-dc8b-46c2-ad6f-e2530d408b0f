syntax = "proto3";

package chat;

option go_package = "github.com/augmentcode/augment/services/lib/proto/chat";

// Common enum for image format types used across different services
enum ImageFormatType {
  // Default unspecified value - should not be used
  IMAGE_FORMAT_UNSPECIFIED = 0;
  // PNG format
  PNG = 1;
  // JPEG format
  JPEG = 2;
  // GIF format
  GIF = 3;
  // WebP format
  WEBP = 4;
}

enum ToolResultContentNodeType {
  // Unspecified content type
  CONTENT_TYPE_UNSPECIFIED = 0;

  // Text content node
  CONTENT_TEXT = 1;

  // Image content node
  CONTENT_IMAGE = 2;
}

message ToolResultContentNode {
  // Type of the node
  ToolResultContentNodeType type = 1;

  // Text content if this is a text node
  optional string text_content = 2 [debug_redact = true];

  // Image content if this is an image node
  optional ChatRequestImage image_content = 3;
}

message ChatRequestImage {
  // Base64 encoded image data
  string image_data = 1 [debug_redact = true];
  // Format of the image data
  ImageFormatType format = 2;
}

message ChatRequestToolResult {
  string tool_use_id = 1;
  string content = 2 [debug_redact = true];
  bool is_error = 3;
  // Remote request which generated the result, if tool implemented by Augment
  optional string request_id = 4;

  // List of content nodes (text or images)
  // If present, takes precedence over content field
  repeated ToolResultContentNode content_nodes = 5;
}

// Tool definition for use with LLM APIs
message ToolDefinition {
  string name = 1;
  string description = 2 [debug_redact = true];
  string input_schema_json = 3 [debug_redact = true];
}

// Enum for tool choice type
enum ToolChoiceType {
  // The model can decide whether to use a tool or not.
  AUTO = 0;
  // The model must use some tool.
  ANY = 1;
  // The model must use the tool with the given name.
  TOOL = 2;
}

// Tool choice configuration
message ToolChoice {
  ToolChoiceType type = 1;
  // Valid for type TOOL; the name of the tool to use
  optional string name = 2;
}

// Information about a workspace folder
message WorkspaceFolderInfo {
  // The root directory of the current repository. This is the first ancestor of folder
  // root that is a git directory or has an `.augmentroot` file.
  string repository_root = 1;
  // The directory of the "workspace" the user has opened.
  string folder_root = 2;
}

// Information about a terminal
message TerminalInfo {
  // Unique id of the terminal.
  uint32 terminal_id = 1;
  // The current working directory of the terminal.
  string current_working_directory = 2;
  // Expected future fields: the shell name, etc.
}

// IDE state information
message ChatRequestIdeState {
  // The workspaces the user has open, ordered by access time. The first element in this
  // list should be the curently opened workspace folder.
  repeated WorkspaceFolderInfo workspace_folders = 1;

  // Set if the client's workspace folders were unchanged since the last request.
  // This field is only used if `workspace_folders` is empty and is required to allow
  // us to not send repeated information on subsequent requests.
  // This is supporting the rare but possible case where the user has NO workspace
  // folders open and is still using chat.
  bool workspace_folders_unchanged = 2;

  // The current working directory of the interactive terminal.
  // If unset, then we should the path in the previous request.
  optional TerminalInfo current_terminal = 3;
}

// Single edit information
message ChatRequestSingleEdit {
  // The starting line of the edit in the original text.
  uint32 before_line_start = 1;

  // The text that was present before the edit.
  string before_text = 2 [debug_redact = true];

  // The starting line where the new text is inserted.
  uint32 after_line_start = 3;

  // The new text that replaces the 'before_text'.
  string after_text = 4 [debug_redact = true];
}

// File edit information
message ChatRequestFileEdit {
  // The path of the file that was edited.
  string path = 1;

  // If present, the name of the blob that was present before the edit.
  // Providing this field will allow the backend to perform additional diff formatting.
  // It is assumed that this before blob name has been uploaded; it doesn't matter if it
  // was indexed.
  optional string before_blob_name = 2;

  // If present, the name of the blob that was present after the edit.
  // Providing this allows the backend to validate the edit event.
  optional string after_blob_name = 3;

  // A list of individual edits that make up this edit event.
  repeated ChatRequestSingleEdit edits = 4;
}

message ChatResultToolUse {
  string tool_use_id = 1;
  string tool_name = 2;
  string input_json = 3 [debug_redact = true];

  // Whether this is a partial tool use response.
  bool is_partial = 4;
}

enum ChatRequestNodeType {
  // User message text.
  TEXT = 0;

  // Result of a tool use.
  TOOL_RESULT = 1;

  // An image(Default format: PNG).
  IMAGE = 2;

  // If sent with no auxiliary data in ChatRequestNode,
  // came from a client which failed to hydrate the real
  // IMAGE node.
  IMAGE_ID = 3;

  // IDE state information.
  IDE_STATE = 4;

  // User edits information.
  EDIT_EVENTS = 5;
}

enum EditEventSource {
  // Default unspecified value
  UNSPECIFIED = 0;
  // Edit was performed by the user
  USER_EDIT = 1;
  // Edit was performed by reverting to a checkpoint
  CHECKPOINT_REVERT = 2;
}

enum ChatStopReason {
  // Stop reason unspecified
  REASON_UNSPECIFIED = 0;
  // The model has reached a natural stopping point or stop sequence
  END_TURN = 1;
  // Generation hit max token limit set by client or server
  MAX_TOKENS = 2;
  // The model has requested one or more tool uses
  // N.B. this name must be unique across all enums; TOOL_USE would collide with ChatResultNodeType
  TOOL_USE_REQUESTED = 3;
}

enum ChatResultNodeType {
  // The raw response from the model.
  RAW_RESPONSE = 0;

  // Our guess of what user will ask next.
  SUGGESTED_QUESTIONS = 1;

  // Indication that streaming of the main response finished
  MAIN_TEXT_FINISHED = 2;

  // Workspace file chunks used in prompt
  WORKSPACE_FILE_CHUNKS = 3;

  // Sources that were useful to generate the response
  RELEVANT_SOURCES = 4;

  // Tool use requested by the AI model.
  // When tool use generation begins, TOOL_USE_START is sent with name and id.
  // When the tool use is fully generated, TOOL_USE will be sent with all fields
  // populated completely.  Real streaming TBD.
  TOOL_USE = 5;
  TOOL_USE_START = 7;

  // Backend code started using this value for FINAL_PARAMETERS with no
  // intention of it being in the actual API. Go ahead and reserve it...
  reserved 6;
}

// Edit events information
message ChatRequestEditEvents {
  // Contains edit events corresponding to zero or more files *since the last request*.
  repeated ChatRequestFileEdit edit_events = 1;
  // Source of the edit events
  optional EditEventSource source = 2;
}

// Text node for chat requests
message ChatRequestText {
  string content = 1 [debug_redact = true];
}

load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "chat_proto",
    srcs = ["chat.proto"],
    visibility = ["//services:__subpackages__"],
)

py_grpc_library(
    name = "chat_py_proto",
    protos = [":chat_proto"],
    visibility = ["//services:__subpackages__"],
)

go_grpc_library(
    name = "chat_go_proto",
    importpath = "github.com/augmentcode/augment/services/lib/proto/chat",
    proto = ":chat_proto",
    visibility = ["//services:__subpackages__"],
)

ts_proto_library(
    name = "chat_ts_proto",
    node_modules = "//:node_modules",
    proto = ":chat_proto",
    visibility = ["//services:__subpackages__"],
)

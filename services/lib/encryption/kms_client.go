package encryption

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"fmt"
	"sync"

	kms "cloud.google.com/go/kms/apiv1"
	kmspb "cloud.google.com/go/kms/apiv1/kmspb"
	"github.com/googleapis/gax-go/v2"
	"google.golang.org/api/impersonate"
	"google.golang.org/api/option"
)

// KMSClient interface
type KMSClient interface {
	MacSign(ctx context.Context, req *kmspb.MacSignRequest, opts ...gax.CallOption) (*kmspb.MacSignResponse, error)
	Close() error
}

// GoogleKMSClient is an implementation of KMSClient that uses Google Cloud KMS
type GoogleKMSClient struct {
	client *kms.KeyManagementClient
}

// Ensure GoogleKMSClient implements KMSClient interface
var _ KMSClient = (*GoogleKMSClient)(nil)

// NewGoogleKMSClient creates a new GoogleKMSClient
func NewGoogleKMSClient(ctx context.Context, opts ...option.ClientOption) (KMSClient, error) {
	client, err := kms.NewKeyManagementClient(ctx, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to create KMS client: %w", err)
	}

	return &GoogleKMSClient{
		client: client,
	}, nil
}

func NewGoogleKMSClientWithServiceAccount(ctx context.Context, serviceAccountEmail string, opts ...option.ClientOption) (KMSClient, error) {
	// Create a token source that impersonates the target service account
	ts, err := impersonate.CredentialsTokenSource(ctx, impersonate.CredentialsConfig{
		TargetPrincipal: serviceAccountEmail,
		Scopes:          []string{"https://www.googleapis.com/auth/cloud-platform"},
	}, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to create impersonated token source: %w", err)
	}

	tokenSourceOpt := option.WithTokenSource(ts)
	allOpts := make([]option.ClientOption, 0, len(opts)+1)
	allOpts = append(allOpts, tokenSourceOpt)
	allOpts = append(allOpts, opts...)

	return NewGoogleKMSClient(ctx, allOpts...)
}

// MacSign implements the KMSClient interface
func (g *GoogleKMSClient) MacSign(ctx context.Context, req *kmspb.MacSignRequest, opts ...gax.CallOption) (*kmspb.MacSignResponse, error) {
	return g.client.MacSign(ctx, req, opts...)
}

// Close implements the KMSClient interface
func (g *GoogleKMSClient) Close() error {
	return g.client.Close()
}

// Mock KMS client
type mockKMSClient struct {
	err error
	// Map to store generated key values for different key names
	keyMap map[string][]byte
	mu     sync.RWMutex
}

func NewMockKMSClient() KMSClient {
	return &mockKMSClient{
		keyMap: make(map[string][]byte),
	}
}

func NewMockKMSClientWithKeys(keyMap map[string][]byte) KMSClient {
	return &mockKMSClient{
		keyMap: keyMap,
	}
}

// Ensure mockKMSClient implements KMSClient interface
var _ KMSClient = (*mockKMSClient)(nil)

func (m *mockKMSClient) MacSign(ctx context.Context, req *kmspb.MacSignRequest, opts ...gax.CallOption) (*kmspb.MacSignResponse, error) {
	if m.err != nil {
		return nil, m.err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	keyName := req.Name

	// Get or generate a key for this key name
	key, exists := m.keyMap[keyName]
	if !exists {
		// Generate a deterministic key based on the key name
		// This ensures the same key name always produces the same key
		h := sha256.New()
		h.Write([]byte(keyName))
		key = h.Sum(nil)
		m.keyMap[keyName] = key
	}

	// Compute SHA256-HMAC of the input data using the key
	h := hmac.New(sha256.New, key)
	h.Write(req.Data)
	mac := h.Sum(nil)

	return &kmspb.MacSignResponse{Mac: mac}, nil
}

func (m *mockKMSClient) Close() error {
	return nil
}

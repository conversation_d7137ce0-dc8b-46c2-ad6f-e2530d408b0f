package encryption

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	twpb "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

// Create a mock TenantCache that implements the interface
type mockTenantCache struct {
	tenant *twpb.Tenant
	err    error
}

func (m *mockTenantCache) GetTenant(tenantID string) (*twpb.Tenant, error) {
	return m.tenant, m.err
}

func (m *mockTenantCache) GetTenantByName(tenantName string) (*twpb.Tenant, error) {
	return nil, nil
}

func (m *mockTenantCache) GetAllTenants() ([]*twpb.Tenant, error) {
	if m.tenant != nil {
		return []*twpb.Tenant{m.tenant}, nil
	}
	return nil, m.err
}

func (m *mockTenantCache) GetTenantsInNamespace(shardNamespace string) ([]*twpb.Tenant, error) {
	if m.tenant != nil {
		return []*twpb.Tenant{m.tenant}, nil
	}
	return nil, m.err
}

func (m *mockTenantCache) Close() {
	// No-op for mock
}

func TestEncryption(t *testing.T) {
	ctx := context.Background()
	plaintext := []byte("hello world")

	t.Run("encryption disabled - returns error", func(t *testing.T) {
		tc, _ := New(ctx, NewMockKMSClient())
		defer tc.Close()

		tenant := &twpb.Tenant{
			Id: "tenant1",
			// No encryption key name set
		}
		assert.False(t, HasSealKey(tenant))
		assert.True(t, errors.Is(tc.CheckKeyAccess(ctx, tenant), ErrNoEncryption))
		encrypted, err := tc.Seal(ctx, tenant, plaintext)
		assert.Error(t, err)
		assert.Nil(t, encrypted)
	})

	t.Run("encryption enabled - encrypts data", func(t *testing.T) {
		tc, _ := New(ctx, NewMockKMSClient())
		tenant := &twpb.Tenant{
			Id:                "tenant2",
			EncryptionKeyName: "projects/foo/locations/global/keyRings/bar/cryptoKeys/baz",
		}
		defer tc.Close()

		assert.True(t, HasSealKey(tenant))
		assert.NoError(t, tc.CheckKeyAccess(ctx, tenant))

		sealed, err := tc.Seal(ctx, tenant, plaintext)
		assert.NoError(t, err)
		assert.NotEqual(t, plaintext, sealed)

		// Verify decryption works
		decrypted, err := tc.Open(ctx, tenant, sealed)
		assert.NoError(t, err)
		assert.Equal(t, plaintext, decrypted)
	})

	t.Run("KMS error - fails encryption", func(t *testing.T) {
		errorMock := &mockKMSClient{
			err:    assert.AnError,
			keyMap: make(map[string][]byte),
		}
		tc, _ := New(ctx, errorMock)
		defer tc.Close()

		tenant := &twpb.Tenant{
			Id:                "tenant3",
			EncryptionKeyName: "projects/foo/locations/global/keyRings/bar/cryptoKeys/baz",
		}

		assert.True(t, errors.Is(tc.CheckKeyAccess(ctx, tenant), ErrKMSAccess))
		_, err := tc.Open(ctx, tenant, plaintext)
		assert.Error(t, err)
	})

	t.Run("repeated encryption produces different ciphertexts", func(t *testing.T) {
		tc, _ := New(ctx, NewMockKMSClient())

		tenant := &twpb.Tenant{
			Id:                "tenant2",
			EncryptionKeyName: "projects/foo/locations/global/keyRings/bar/cryptoKeys/baz",
		}
		defer tc.Close()

		sealed1, err := tc.Seal(ctx, tenant, plaintext)
		assert.NoError(t, err)
		sealed2, err := tc.Seal(ctx, tenant, plaintext)
		assert.NoError(t, err)

		assert.NotEqual(t, sealed1, sealed2)

		decrypted1, err := tc.Open(ctx, tenant, sealed1)
		assert.NoError(t, err)
		decrypted2, err := tc.Open(ctx, tenant, sealed2)
		assert.NoError(t, err)

		assert.Equal(t, plaintext, decrypted1)
		assert.Equal(t, plaintext, decrypted2)
	})

	t.Run("decryption fails when KMS access denied", func(t *testing.T) {
		tc1, _ := New(ctx, NewMockKMSClient())
		tenant := &twpb.Tenant{
			Id:                "tenant3",
			EncryptionKeyName: "projects/foo/locations/global/keyRings/bar/cryptoKeys/baz",
		}
		defer tc1.Close()

		// Encrypt with working KMS
		sealed, err := tc1.Seal(ctx, tenant, plaintext)
		assert.NoError(t, err)

		// Try to decrypt with broken KMS
		errorMock := &mockKMSClient{
			err:    status.Error(codes.PermissionDenied, "permission denied"),
			keyMap: make(map[string][]byte),
		}

		// Reduce the cache timeout for testing

		tc2, _ := New(ctx, errorMock, WithDeniedKeyTTL(100*time.Millisecond))
		defer tc2.Close()

		// First attempt should fail with the KMS error
		_, err = tc2.Open(ctx, tenant, sealed)
		assert.Error(t, err)
		assert.True(t, errors.Is(err, ErrKeyAccessDenied), "should return ErrKeyAccessDenied error")
		assert.True(t, errors.Is(tc2.CheckKeyAccess(ctx, tenant), ErrKeyAccessDenied))

		// Allow key access again
		errorMock.err = nil

		// Second attempt should fail with the same error due to cache, without calling KMS
		_, err = tc2.Open(ctx, tenant, sealed)
		assert.Error(t, err)
		assert.True(t, errors.Is(err, ErrKeyAccessDenied), "should return ErrKeyAccessDenied error")
		assert.True(t, errors.Is(tc2.CheckKeyAccess(ctx, tenant), ErrKeyAccessDenied))

		// Let the cache expire.
		time.Sleep(110 * time.Millisecond)

		// Third attempt should succeed
		_, err = tc2.Open(ctx, tenant, sealed)
		assert.NoError(t, err)
		assert.NoError(t, tc2.CheckKeyAccess(ctx, tenant))
	})

	t.Run("key rotation handles both old and new data correctly", func(t *testing.T) {
		// Initial key setup
		tc, _ := New(ctx, NewMockKMSClient())
		tenant := &twpb.Tenant{
			Id:                "tenant4",
			EncryptionKeyName: "projects/foo/locations/global/keyRings/bar/cryptoKeys/baz1",
		}
		defer tc.Close()

		// First encryption with initial key
		sealed1, err := tc.Seal(ctx, tenant, plaintext)
		assert.NoError(t, err)

		// Verify first encryption works
		decrypted1, err := tc.Open(ctx, tenant, sealed1)
		assert.NoError(t, err)
		assert.Equal(t, plaintext, decrypted1)

		// Switch to new key
		tenantRekeyed := &twpb.Tenant{
			Id:                "tenant4",
			EncryptionKeyName: "projects/foo/locations/global/keyRings/bar/cryptoKeys/baz2",
		}

		// Second encryption with new key
		sealed2, err := tc.Seal(ctx, tenantRekeyed, plaintext)
		assert.NoError(t, err)

		// Verify second encryption works
		decrypted2, err := tc.Open(ctx, tenantRekeyed, sealed2)
		assert.NoError(t, err)
		assert.Equal(t, plaintext, decrypted2)

		// Verify we can still decrypt old data after key rotation
		// This is the key test for backward compatibility
		decrypted1Again, err := tc.Open(ctx, tenantRekeyed, sealed1)
		assert.NoError(t, err)
		assert.Equal(t, plaintext, decrypted1Again, "should be able to decrypt old data after key rotation")

		// Test that the system uses the embedded key information
		// by creating a new crypto instance with no cached keys
		tcFresh, _ := New(ctx, NewMockKMSClient())
		defer tcFresh.Close()

		// It should still decrypt both old and new data correctly
		decryptedOld, err := tcFresh.Open(ctx, tenantRekeyed, sealed1)
		assert.NoError(t, err)
		assert.Equal(t, plaintext, decryptedOld, "fresh instance should decrypt old data")

		decryptedNew, err := tcFresh.Open(ctx, tenantRekeyed, sealed2)
		assert.NoError(t, err)
		assert.Equal(t, plaintext, decryptedNew, "fresh instance should decrypt new data")
	})
}

// The plaintext that was encrypted to create testVectorEncrypted
var testVectorPlaintext = []byte("test vector plaintext")

// The tenant used to encrypt testVectorEncrypted
var testVectorTenant = &twpb.Tenant{
	Id:                "test-vector-tenant",
	EncryptionKeyName: "projects/foo/locations/global/keyRings/bar/cryptoKeys/test-vector",
}

var testVectorKey = []byte("fixed-key-for-test-vectors-only")

// Define multiple test vectors for different encryption formats
var testVectors = []struct {
	name      string
	encrypted []byte
	plaintext []byte
	tenant    *twpb.Tenant
	key       []byte
}{
	{
		name: "legacy_protobuf_format",
		encrypted: []byte{
			0x50, 0x0a, 0x25, 0x71, 0x0c, 0x34, 0xfb, 0xf7, 0xaa, 0x81, 0xba, 0xc0, 0x46, 0x6e, 0x98, 0x8b,
			0xaa, 0xbd, 0xde, 0x97, 0x5e, 0xd5, 0x7c, 0xa5, 0x33, 0x81, 0xf7, 0x5e, 0x7a, 0xc0, 0x20, 0x9d,
			0x89, 0xac, 0x14, 0xf1, 0x95, 0xe0, 0x6e, 0x3a, 0x12, 0x41, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
			0x74, 0x73, 0x2f, 0x66, 0x6f, 0x6f, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
			0x2f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x2f, 0x6b, 0x65, 0x79, 0x52, 0x69, 0x6e, 0x67, 0x73,
			0x2f, 0x62, 0x61, 0x72, 0x2f, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65, 0x79, 0x73, 0x2f,
			0x74, 0x65, 0x73, 0x74, 0x2d, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x0c, 0xe0, 0x4a, 0xa0,
			0x0f, 0xb2, 0x3d, 0xc2, 0x60, 0x65, 0xdf, 0x1d, 0xce,
		},
		plaintext: testVectorPlaintext,
		tenant:    testVectorTenant,
		key:       testVectorKey,
	},
	{
		name: "zero_copy_proto_format",
		encrypted: []byte{
			0x5a, 0x00, 0x00, 0x00, 0x51, 0x0a, 0x41, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
			0x66, 0x6f, 0x6f, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x67, 0x6c,
			0x6f, 0x62, 0x61, 0x6c, 0x2f, 0x6b, 0x65, 0x79, 0x52, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x62, 0x61,
			0x72, 0x2f, 0x63, 0x72, 0x79, 0x70, 0x74, 0x6f, 0x4b, 0x65, 0x79, 0x73, 0x2f, 0x74, 0x65, 0x73,
			0x74, 0x2d, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x0c, 0x15, 0x69, 0x26, 0x48, 0xd6, 0x1e,
			0x8e, 0x88, 0x70, 0x2c, 0xb4, 0x0d, 0x1a, 0xed, 0x7b, 0x1f, 0x25, 0x33, 0x25, 0xbc, 0xde, 0xa2,
			0x50, 0x22, 0xcc, 0xe6, 0xe1, 0x68, 0x26, 0x0c, 0xd8, 0x1a, 0x22, 0xbf, 0xd1, 0x7f, 0x41, 0xa4,
			0x6c, 0x84, 0xa6, 0x62, 0xc8, 0xe2, 0x43, 0x99, 0x36, 0xf7, 0xbe,
		},
		plaintext: testVectorPlaintext,
		tenant:    testVectorTenant,
		key:       testVectorKey,
	},
}

// generateTestVector creates an encrypted blob for testing purposes.
// This function is only used to generate test vectors, not in actual tests.
func generateTestVector(t *testing.T) {
	ctx := context.Background()

	mockKMS := NewMockKMSClientWithKeys(map[string][]byte{
		testVectorTenant.EncryptionKeyName: testVectorKey,
	})

	tc, _ := New(ctx, mockKMS)
	defer tc.Close()

	tenant := testVectorTenant

	sealed, err := tc.Seal(ctx, tenant, testVectorPlaintext)
	if err != nil {
		t.Logf("Error generating test vector: %v", err)
		t.Fail()
		return
	}

	// Print directly to stdout instead of using t.Logf
	fmt.Println("\n\n=== TEST VECTOR FOR ZERO-COPY PROTO FORMAT ===")
	fmt.Println("Copy this into the testVectors slice:")
	fmt.Println("{")
	fmt.Println("    name:      \"zero_copy_proto_format\",")
	fmt.Println("    encrypted: []byte{")
	fmt.Print("        ")

	for i, b := range sealed {
		if i > 0 {
			if i%16 == 0 {
				fmt.Print(",\n        ")
			} else {
				fmt.Print(", ")
			}
		}
		fmt.Printf("0x%02x", b)
	}

	fmt.Println("\n    },")
	fmt.Println("    plaintext: testVectorPlaintext,")
	fmt.Println("    tenant:    testVectorTenant,")
	fmt.Println("    key:       testVectorKey,")
	fmt.Println("},")
	fmt.Println("=== END TEST VECTOR ===")
}

func TestBackwardCompatibility(t *testing.T) {
	ctx := context.Background()

	// Uncomment to generate a new test vector for the zero-copy proto format
	// generateTestVector(t)
	// t.SkipNow() // Skip the rest of the test when generating vectors

	// Run tests for each test vector
	for _, tv := range testVectors {
		t.Run(tv.name, func(t *testing.T) {
			mockKMS := NewMockKMSClientWithKeys(map[string][]byte{
				tv.tenant.EncryptionKeyName: tv.key,
			})

			// Create a tenant crypto instance with the mock KMS
			tc, _ := New(ctx, mockKMS)
			defer tc.Close()

			// Verify we can decrypt the test vector
			decrypted, err := tc.Open(ctx, tv.tenant, tv.encrypted)
			assert.NoError(t, err)
			assert.Equal(t, tv.plaintext, decrypted,
				"Failed to decrypt test vector for %s format - this indicates a breaking change in the encryption/decryption logic", tv.name)
		})
	}
}

package encryption

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/binary"
	"errors"
	"fmt"
	"sync"
	"time"

	kmspb "cloud.google.com/go/kms/apiv1/kmspb"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	pb "github.com/augmentcode/augment/services/lib/encryption/proto"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

const (
	defaultKeyTTL       = 24 * time.Hour
	defaultDeniedKeyTTL = 1 * time.Minute
)

// Error types for encryption operations
var (
	// ErrNoEncryption indicates the tenant does not have encryption enabled
	ErrNoEncryption = errors.New("tenant does not have encryption enabled")

	// ErrKMSAccess indicates a problem accessing the KMS service
	ErrKMSAccess = errors.New("failed to access KMS service")

	// ErrKeyAccessDenied indicates the encryption key access denied
	ErrKeyAccessDenied = errors.New("tenant key access denied")

	// ErrInvalidFormat indicates the encrypted data format is invalid
	ErrInvalidFormat = errors.New("invalid encryption format")

	// ErrDecryptionFailed indicates a general decryption failure
	ErrDecryptionFailed = errors.New("failed to decrypt data")

	// ErrEncryptionFailed indicates a general encryption failure
	ErrEncryptionFailed = errors.New("failed to encrypt data")

	// ErrCipher indicates a problem with the cipher operation
	ErrCipherFail = errors.New("cipher operation failed")
)

// GetStatusString categorizes an encryption error into a category string
func GetStatusString(err error) string {
	if err == nil {
		return "success"
	}

	switch {
	case errors.Is(err, ErrNoEncryption):
		return "no_encryption"
	case errors.Is(err, ErrKMSAccess):
		return "kms_access"
	case errors.Is(err, ErrKeyAccessDenied):
		return "key_access_denied"
	case errors.Is(err, ErrInvalidFormat):
		return "invalid_format"
	case errors.Is(err, ErrDecryptionFailed):
		return "decryption_failed"
	case errors.Is(err, ErrEncryptionFailed):
		return "encryption_failed"
	case errors.Is(err, ErrCipherFail):
		return "cipher_fail"
	default:
		return "unknown_error"
	}
}

// TenantCrypto handles tenant-specific encryption operations
type TenantCrypto interface {
	// Close closes the underlying KMS client
	Close() error

	// Seal encrypts data for a specific tenant
	// Returns an error if the tenant does not have encryption enabled.
	Seal(ctx context.Context, tenant *tenantproto.Tenant, plaintext []byte) ([]byte, error)

	// Open decrypts data for a specific tenant
	// Returns an error if the data is not encrypted or if the tenant does not have encryption enabled.
	Open(ctx context.Context, tenant *tenantproto.Tenant, sealed []byte) ([]byte, error)

	// LoadTenantKeys loads the encryption keys for a list of tenants
	LoadTenantKeys(ctx context.Context, tenants []*tenantproto.Tenant) error

	// Check whether a tenant can access its encryption key
	// Returns an error if the tenant does not have encryption enabled or if the key is not accessible.
	CheckKeyAccess(ctx context.Context, tenant *tenantproto.Tenant) error
}

type cacheKey struct {
	tenantID   string
	kmsKeyName string
}

type cacheEntry struct {
	gcm       cipher.AEAD
	createdAt time.Time
	err       error // Store the error for negative cache entries
}

type tenantCrypto struct {
	kmsClient KMSClient
	keyCache  map[cacheKey]cacheEntry
	cacheMu   sync.RWMutex
	deniedTTL time.Duration
}

// Option is a function that configures a tenantCrypto instance
type Option func(*tenantCrypto)

// WithDeniedKeyTTL sets a custom TTL for denied keys
func WithDeniedKeyTTL(ttl time.Duration) Option {
	return func(tc *tenantCrypto) {
		tc.deniedTTL = ttl
	}
}

// New creates a new TenantCrypto instance
func New(ctx context.Context, kmsClient KMSClient, opts ...Option) (TenantCrypto, error) {
	keyCache := make(map[cacheKey]cacheEntry)

	tc := &tenantCrypto{
		kmsClient: kmsClient,
		keyCache:  keyCache,
		cacheMu:   sync.RWMutex{},
		deniedTTL: defaultDeniedKeyTTL,
	}

	// Apply any options
	for _, opt := range opts {
		opt(tc)
	}

	return tc, nil
}

func (tc *tenantCrypto) Close() error {
	if err := tc.kmsClient.Close(); err != nil {
		return fmt.Errorf("failed to close KMS client: %w", err)
	}
	return nil
}

func HasSealKey(tenant *tenantproto.Tenant) bool {
	return tenant.EncryptionKeyName != ""
}

func tenantKeyName(tenant *tenantproto.Tenant) string {
	return tenant.EncryptionKeyName
}

func tenantKeyTTL(tenant *tenantproto.Tenant) time.Duration {
	if tenant.EncryptionKeyTtl != nil {
		return tenant.EncryptionKeyTtl.AsDuration()
	}
	return defaultKeyTTL
}

func (tc *tenantCrypto) LoadTenantKeys(ctx context.Context, tenants []*tenantproto.Tenant) error {
	var kmsError error

	// Initialize key cache with current tenant keys for all current tenants
	for _, tenant := range tenants {
		kmsKeyName := tenant.EncryptionKeyName
		if kmsKeyName == "" {
			continue // Skip tenants without encryption
		}

		err := tc.CheckKeyAccess(ctx, tenant)
		if err != nil {
			// Stop processing on KMS connectivity errors
			if errors.Is(err, ErrKMSAccess) {
				log.Ctx(ctx).Error().
					Str("tenant_id", tenant.Id).
					Err(err).
					Msg("Failed to connect to KMS")
				kmsError = err
				break
			}

			// For other errors (like revoked keys), just log and continue
			log.Ctx(ctx).Warn().
				Str("tenant_id", tenant.Id).
				Str("kms_key_name", kmsKeyName).
				Err(err).
				Msg("Failed to initialize key cache for tenant (key may be revoked)")
		}
	}

	return kmsError
}

func (tc *tenantCrypto) CheckKeyAccess(ctx context.Context, tenant *tenantproto.Tenant) error {
	kmsKeyName := tenantKeyName(tenant)
	_, _, err := tc.getTenantDEK(ctx, tenant, kmsKeyName)
	return err
}

// Encryption data format
const (
	formatMagicLen     = 1
	protoFormatMagic   = "P" // | magic | EncryptedData protobuf |
	zeroCopyProtoMagic = "Z" // | magic | proto-length BE32 | EncryptedHeader protobuf | ciphertext |
)

func (tc *tenantCrypto) Seal(ctx context.Context, tenant *tenantproto.Tenant, plaintext []byte) ([]byte, error) {
	kmsKeyName := tenantKeyName(tenant)
	if kmsKeyName == "" {
		return nil, fmt.Errorf("%w: tenant does not have encryption enabled", ErrNoEncryption)
	}

	// Get the GCM cipher
	gcm, kmsKeyName, err := tc.getTenantDEK(ctx, tenant, kmsKeyName)
	if err != nil {
		return nil, fmt.Errorf("%w: failed to get cipher", err)
	}

	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		log.Ctx(ctx).Error().
			Str("tenant_id", tenant.Id).
			Str("kms_key_name", kmsKeyName).
			Err(err).
			Msg("Failed to generate nonce")
		return nil, fmt.Errorf("%w: failed to generate nonce", ErrEncryptionFailed)
	}

	// Calculate sizes for buffer allocation
	cipherTextSize := len(plaintext) + gcm.Overhead()

	// Create a header proto
	header := &pb.EncryptedHeader{
		KmsKeyName: kmsKeyName,
		Nonce:      nonce,
	}

	// Allocate a single buffer with enough capacity for everything
	// proto will be under 1024 bytes. Over-allocate for safety.
	capacity := formatMagicLen + 4 + 1024 + cipherTextSize
	buffer := make([]byte, formatMagicLen+4, capacity)

	// Set the magic prefix
	copy(buffer, zeroCopyProtoMagic)

	// Marshal the header
	opts := proto.MarshalOptions{}
	buffer, err = opts.MarshalAppend(buffer, header)
	if err != nil {
		log.Ctx(ctx).Error().
			Str("tenant_id", tenant.Id).
			Str("kms_key_name", kmsKeyName).
			Err(err).
			Msg("Failed to marshal encrypted header")
		return nil, fmt.Errorf("%w: failed to marshal encrypted header", ErrEncryptionFailed)
	}

	// Set the proto length
	protoLen := len(buffer) - (formatMagicLen + 4)
	binary.BigEndian.PutUint32(
		buffer[formatMagicLen:formatMagicLen+4],
		uint32(protoLen),
	)

	// Encrypt the plaintext and append it to the buffer
	// This will use the pre-allocated capacity without reallocation
	result := gcm.Seal(buffer, nonce, plaintext, nil)

	return result, nil
}

func (tc *tenantCrypto) Open(ctx context.Context, tenant *tenantproto.Tenant, sealed []byte) ([]byte, error) {
	// Unmarshal the sealed data

	// Check for format identifier
	if len(sealed) < formatMagicLen {
		log.Ctx(ctx).Error().
			Str("tenant_id", tenant.Id).
			Int("sealed_len", len(sealed)).
			Msg("Failed to find format identifier: sealed data too short")
		return nil, fmt.Errorf("%w: sealed data too short", ErrInvalidFormat)
	}

	formatIdentifier := string(sealed[:formatMagicLen])

	// Handle different format types
	switch formatIdentifier {
	case protoFormatMagic:
		// Extract the proto data (skip the magic prefix)
		return tc.openProtoFormat(ctx, tenant, sealed)
	case zeroCopyProtoMagic:
		return tc.openZeroCopyProtoFormat(ctx, tenant, sealed)
	default:
		log.Ctx(ctx).Error().
			Str("tenant_id", tenant.Id).
			Str("format_identifier", formatIdentifier).
			Msg("Failed to find valid format identifier")
		return nil, fmt.Errorf("%w: invalid format identifier \"%s\"", ErrInvalidFormat, formatIdentifier)
	}
}

func (tc *tenantCrypto) openProtoFormat(ctx context.Context, tenant *tenantproto.Tenant, sealed []byte) ([]byte, error) {
	// Unmarshal the encrypted data proto
	sealed = sealed[formatMagicLen:]
	var encData pb.EncryptedData
	if err := proto.Unmarshal(sealed, &encData); err != nil {
		log.Ctx(ctx).Error().
			Str("tenant_id", tenant.Id).
			Err(err).
			Msg("Failed to unmarshal encrypted data")
		return nil, fmt.Errorf("%w: failed to unmarshal encrypted data: %w", ErrInvalidFormat, err)
	}

	kmsKeyName := encData.KmsKeyName

	// Get the GCM cipher
	gcm, kmsKeyName, err := tc.getTenantDEK(ctx, tenant, kmsKeyName)
	if err != nil {
		return nil, fmt.Errorf("%w: failed to get cipher", err)
	}

	// Pre-allocate the output buffer to avoid an extra allocation
	// The plaintext will be smaller than ciphertext by gcm.Overhead() bytes
	plaintext := make([]byte, 0, len(encData.Ciphertext)-gcm.Overhead())
	plaintext, err = gcm.Open(plaintext, encData.Nonce, encData.Ciphertext, nil)
	if err != nil {
		log.Ctx(ctx).Error().
			Str("tenant_id", tenant.Id).
			Str("kms_key_name", kmsKeyName).
			Err(err).
			Msg("Failed to decrypt data")
		return nil, fmt.Errorf("%w: failed to decrypt data: %w", ErrDecryptionFailed, err)
	}

	return plaintext, nil
}

func (tc *tenantCrypto) openZeroCopyProtoFormat(ctx context.Context, tenant *tenantproto.Tenant, sealed []byte) ([]byte, error) {
	// Check if we have enough data for the format
	if len(sealed) < formatMagicLen+4 {
		return nil, fmt.Errorf("%w: encrypted data too short for zero-copy format", ErrInvalidFormat)
	}

	// Extract the proto data (skip the magic prefix)
	protoData := sealed[formatMagicLen:]
	protoLen := binary.BigEndian.Uint32(protoData[:4])
	if protoLen > uint32(len(protoData)-4) {
		return nil, fmt.Errorf("%w: invalid proto length: %d exceeds available data: %d",
			ErrInvalidFormat, protoLen, len(protoData)-4)
	}
	protoData = protoData[4 : 4+protoLen]
	if formatMagicLen+4+int(protoLen) > len(sealed) {
		return nil, fmt.Errorf("%w: encrypted data missing ciphertext", ErrInvalidFormat)
	}
	ciphertext := sealed[formatMagicLen+4+int(protoLen):]

	// Unmarshal the encrypted header proto
	var encHeader pb.EncryptedHeader
	if err := proto.Unmarshal(protoData, &encHeader); err != nil {
		return nil, fmt.Errorf("%w: failed to unmarshal encrypted header: %w", ErrInvalidFormat, err)
	}

	kmsKeyName := encHeader.KmsKeyName

	// Get the GCM cipher
	gcm, kmsKeyName, err := tc.getTenantDEK(ctx, tenant, kmsKeyName)
	if err != nil {
		return nil, fmt.Errorf("%w: failed to get cipher", err)
	}

	if len(ciphertext) < gcm.Overhead() {
		return nil, fmt.Errorf("%w: ciphertext too short: %d bytes, minimum required: %d bytes",
			ErrInvalidFormat, len(ciphertext), gcm.Overhead())
	}

	// Pre-allocate the output buffer to avoid an extra allocation
	// The plaintext will be smaller than ciphertext by gcm.Overhead() bytes
	plaintext := make([]byte, 0, len(ciphertext)-gcm.Overhead())
	plaintext, err = gcm.Open(plaintext, encHeader.Nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("%w: failed to decrypt data: %w", ErrDecryptionFailed, err)
	}

	return plaintext, nil
}

// getTenantDEK retrieves or derives the data encryption key and returns the GCM cipher
func (tc *tenantCrypto) getTenantDEK(ctx context.Context, tenant *tenantproto.Tenant, kmsKeyName string) (cipher.AEAD, string, error) {
	if kmsKeyName == "" {
		return nil, "", fmt.Errorf("%w: tenant does not have encryption enabled", ErrNoEncryption)
	}

	cKey := cacheKey{
		tenantID:   tenant.Id,
		kmsKeyName: kmsKeyName,
	}

	// Try to get from cache first
	tc.cacheMu.RLock()
	entry, exists := tc.keyCache[cKey]
	tc.cacheMu.RUnlock()

	if exists {
		// Check if entry is expired.
		ttl := tenantKeyTTL(tenant)
		if entry.gcm == nil {
			ttl = tc.deniedTTL
		}

		if time.Since(entry.createdAt) < ttl {
			if entry.gcm == nil {
				return nil, kmsKeyName, entry.err
			}
			return entry.gcm, kmsKeyName, nil
		}

		// Entry expired, remove it.
		log.Ctx(ctx).Debug().
			Str("tenant_name", tenant.Name).
			Str("tenant_id", tenant.Id).
			Str("kms_key_name", kmsKeyName).
			Msg("Cache entry expired, deriving new key")

		tc.cacheMu.Lock()
		delete(tc.keyCache, cKey)
		tc.cacheMu.Unlock()
	}

	// Derive new key
	key, err := tc.deriveKey(ctx, tenant.Id, kmsKeyName)
	if err != nil {
		// Cache permission denied errors with a shorter TTL
		if errors.Is(err, ErrKeyAccessDenied) {
			tc.cacheMu.Lock()
			tc.keyCache[cKey] = cacheEntry{
				gcm:       nil, // Nil GCM indicates negative cache entry
				createdAt: time.Now(),
				err:       err,
			}
			tc.cacheMu.Unlock()

			log.Ctx(ctx).Info().
				Str("tenant_name", tenant.Name).
				Str("tenant_id", tenant.Id).
				Str("kms_key_name", kmsKeyName).
				Msg("Caching key access denied error")
		}
		return nil, kmsKeyName, err
	}

	// Create AES cipher and GCM
	block, err := aes.NewCipher(key)
	if err != nil {
		log.Ctx(ctx).Error().
			Str("tenant_name", tenant.Name).
			Str("tenant_id", tenant.Id).
			Str("kms_key_name", kmsKeyName).
			Err(err).
			Msg("Failed to create cipher")
		return nil, kmsKeyName, fmt.Errorf("%w: failed to create cipher: %w", ErrCipherFail, err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		log.Ctx(ctx).Error().
			Str("tenant_name", tenant.Name).
			Str("tenant_id", tenant.Id).
			Str("kms_key_name", kmsKeyName).
			Err(err).
			Msg("Failed to create GCM")
		return nil, kmsKeyName, fmt.Errorf("%w: failed to create GCM: %w", ErrCipherFail, err)
	}

	// Update cache
	// Get TTL from settings service

	tc.cacheMu.Lock()
	tc.keyCache[cKey] = cacheEntry{
		gcm:       gcm,
		createdAt: time.Now(),
	}
	tc.cacheMu.Unlock()

	// Log that tenant's key was accessed to derive the encryption key (auditing requirement)
	log.Ctx(ctx).Info().
		Str("tenant_name", tenant.Name).
		Str("tenant_id", tenant.Id).
		Str("kms_key_name", kmsKeyName).
		Msg("Derived encryption key for tenant")

	return gcm, kmsKeyName, nil
}

// deriveKey derives an encryption key for a specific tenant using KMS HMAC
func (tc *tenantCrypto) deriveKey(ctx context.Context, tenantID string, kmsKeyName string) ([]byte, error) {
	// Use a simple, deterministic concatenation format for key material.
	// Format: <length of tenantID as 4-byte big-endian> + <tenantID bytes> + <length of kmsKeyName as 4-byte big-endian> + <kmsKeyName bytes>
	// This format ensures that the key material is unique for each tenant and KMS key, even if the tenant ID or KMS key name contains the other as a substring.
	tenantIDBytes := []byte(tenantID)
	kmsKeyNameBytes := []byte(kmsKeyName)

	data := make([]byte, 8+len(tenantIDBytes)+len(kmsKeyNameBytes))
	binary.BigEndian.PutUint32(data[0:4], uint32(len(tenantIDBytes)))
	copy(data[4:4+len(tenantIDBytes)], tenantIDBytes)
	binary.BigEndian.PutUint32(data[4+len(tenantIDBytes):8+len(tenantIDBytes)], uint32(len(kmsKeyNameBytes)))
	copy(data[8+len(tenantIDBytes):], kmsKeyNameBytes)

	// Create the MAC request
	req := &kmspb.MacSignRequest{
		Name: kmsKeyName,
		Data: data,
	}

	// Call KMS to compute the HMAC
	resp, err := tc.kmsClient.MacSign(ctx, req)
	if err != nil {
		log.Ctx(ctx).Error().
			Str("tenant_id", tenantID).
			Str("kms_key_name", kmsKeyName).
			Err(err).
			Msg("Failed to compute HMAC for key derivation")
		// Distinguish between different types of KMS errors
		code := status.Code(err)
		switch code {
		case codes.Unavailable, // Server is unavailable (e.g., connection issues)
			codes.DeadlineExceeded, // Request timed out
			codes.Canceled,         // Request was canceled (often due to context timeout)
			codes.Aborted:          // Request was aborted
			return nil, fmt.Errorf("%w: Key derivation failed: %w", ErrKMSAccess, err)
		case codes.PermissionDenied: // The caller does not have permission to execute the specified operation
			return nil, fmt.Errorf("%w: Key access denied: %w", ErrKeyAccessDenied, err)
		default:
			return nil, fmt.Errorf("%w: Key derivation failed: %w", ErrKMSAccess, err)
		}
	}

	return resp.Mac, nil
}

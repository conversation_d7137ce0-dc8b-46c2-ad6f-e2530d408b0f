load("//tools/bzl:go.bzl", "go_library", "go_proto_library", "go_test")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "tenant_crypto_proto",
    srcs = ["tenant_crypto.proto"],
)

go_proto_library(
    name = "tenant_crypto_go_proto",
    importpath = "github.com/augmentcode/augment/services/lib/encryption/proto",
    proto = ":tenant_crypto_proto",
    visibility = ["//services:__subpackages__"],
)

go_library(
    name = "encryption_lib_go",
    srcs = [
        "kms_client.go",
        "tenant_crypto.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/encryption",
    visibility = ["//services:__subpackages__"],
    deps = [
        ":tenant_crypto_go_proto",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "@com_github_googleapis_gax_go_v2//:gax-go",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_kms//apiv1",
        "@com_google_cloud_go_kms//apiv1/kmspb",
        "@org_golang_google_api//impersonate",
        "@org_golang_google_api//option",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "encryption_test_go",
    srcs = [
        "tenant_crypto_test.go",
    ],
    embed = [":encryption_lib_go"],
    deps = [
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//types/known/durationpb",
    ],
)

syntax = "proto3";

package encryption;

option go_package = "github.com/augmentcode/augment/services/lib/encryption/proto";

message EncryptedData {
  // Use 'bytes' with [ctype = CORD] option for Opaque API optimization
  bytes ciphertext = 1 [
    ctype = CORD,
    debug_redact = true
  ];
  string kms_key_name = 2;
  // Use 'bytes' with [ctype = CORD] option for Opaque API optimization
  bytes nonce = 3 [
    ctype = CORD,
    debug_redact = true
  ];
}

message EncryptedHeader {
  string kms_key_name = 1;
  // Use 'bytes' with [ctype = CORD] option for Opaque API optimization
  bytes nonce = 2 [
    ctype = CORD,
    debug_redact = true
  ];
}

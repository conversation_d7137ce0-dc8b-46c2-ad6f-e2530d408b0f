# Tenant Encryption Library

Library for handling tenant-specific encryption operations across Augment services. This library provides a secure way to encrypt and decrypt data based on tenant-specific encryption settings.

## Features

- Tenant-specific encryption/decryption operations
- Integration with Google Cloud KMS for key management
- Caching of encryption keys with configurable TTL
- Automatic handling of tenant encryption settings
- Support for key rotation

## Interface

The library provides the `TenantCrypto` interface with the following methods:

```go
type TenantCrypto interface {
    // Close closes the underlying KMS client
    Close() error

    // Seal encrypts data for a specific tenant
    // Returns an error if the tenant does not have encryption enabled.
    Seal(ctx context.Context, tenant *tenantproto.Tenant, plaintext []byte) ([]byte, error)

    // Open decrypts and verifies data data for a specific tenant
    // Returns an error if the data is not encrypted or if the tenant does not have encryption enabled.
    Open(ctx context.Context, tenant *tenantproto.Tenant, sealed []byte) ([]byte, error)

    // LoadTenantKeys loads the encryption keys for a list of tenants
    LoadTenantKeys(ctx context.Context, tenants []*tenantproto.Tenant) error
}
```

Additionally, the package provides these utility functions:

```go
// HasSealKey checks if a tenant has encryption enabled
func HasSealKey(tenant *tenantproto.Tenant) bool

// New creates a new TenantCrypto instance
func New(ctx context.Context, kmsClient KMSClient) (TenantCrypto, error)

// NewKMSClient creates a new KMS client
func NewKMSClient(ctx context.Context, opts ...option.ClientOption) (KMSClient, error)
```

## Usage

```go
import (
    "github.com/augmentcode/augment/services/lib/encryption"
    tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
)

// Create a new KMS client
kmsClient, err := encryption.NewKMSClient(ctx)

// Create a new TenantCrypto instance
tc, err := encryption.New(ctx, kmsClient)
defer tc.Close()

// Preload encryption keys for multiple tenants
err = tc.LoadTenantKeys(ctx, tenants)

// Check if tenant has encryption enabled
isEncrypted := encryption.HasSealKey(tenant)

// Encrypt data for a tenant
sealed, err := tc.Seal(ctx, tenant, plaintext)

// Decrypt data for a tenant
decrypted, err := tc.Open(ctx, tenant, sealed)

```

## Security Considerations

- Encryption keys are managed through Google Cloud KMS
- Keys are cached with a default TTL of 24 hours (configurable per tenant)
- Tenant encryption settings are retrieved from the tenant proto
- Encrypted data includes the KMS key name and nonce in a protobuf format
- Key rotation is supported - old data can be decrypted even after key rotation, provided the KMS key is accessible

## Cryptographic Details

### IV/Nonce Handling

The library uses AES-GCM (Galois/Counter Mode) for encryption, which requires a unique initialization vector (IV) or nonce for each encryption operation:

- A cryptographically secure random nonce is generated for each encryption operation using Go's `crypto/rand`
- The nonce size is determined by the GCM implementation (typically 12 bytes)
- The nonce is stored alongside the ciphertext in the `EncryptedData` protobuf message
- During decryption, the stored nonce is retrieved from the protobuf and used to decrypt the data
- This approach ensures that encrypting the same plaintext multiple times produces different ciphertexts, protecting against pattern analysis

The test `repeated encryption produces different ciphertexts` verifies this property by ensuring that encrypting the same plaintext twice produces different ciphertexts.

### Key Derivation

- Tenant-specific encryption keys are derived using KMS HMAC
- The derivation process uses a deterministic combination of tenant ID and KMS key name
- This ensures each tenant has a unique encryption key while still allowing for key rotation

## Dependencies

- Google Cloud KMS
- Protocol Buffers for encrypted data format

## Implementation Details

- Implements key caching to reduce KMS API calls
- Automatically handles tenants with and without encryption enabled
- Includes comprehensive error handling for KMS connectivity issues
- Supports backward compatibility with previously encrypted data through embedded key information

package metrics

import (
	"context"
	"testing"

	"github.com/prometheus/client_golang/prometheus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

func TestParseMethod(t *testing.T) {
	tests := []struct {
		input        string
		wantService  string
		wantEndpoint string
	}{
		{
			input:        "/package.ServiceName/MethodName",
			wantService:  "ServiceName",
			wantEndpoint: "MethodName",
		},
		{
			input:        "/com.example.v1.MyService/DoSomething",
			wantService:  "MyService",
			wantEndpoint: "DoSomething",
		},
		{
			input:        "invalid",
			wantService:  "unknown",
			wantEndpoint: "unknown",
		},
	}

	for _, tt := range tests {
		service, endpoint := parseMethod(tt.input)
		if service != tt.wantService || endpoint != tt.wantEndpoint {
			t.<PERSON>("parseMethod(%q) = (%q, %q), want (%q, %q)",
				tt.input, service, endpoint, tt.wantService, tt.wantEndpoint)
		}
	}
}

func TestUnaryInterceptor(t *testing.T) {
	// Reset metrics
	prometheus.Unregister(rpcLatencyHistogram)
	prometheus.Unregister(activeRequestsGauge)

	// Re-register metrics
	rpcLatencyHistogram = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_rpc_latency_histogram",
			Help: "Histogram of RPC latencies",
		},
		[]string{"service", "endpoint", "status_code", "request_source", "tenant_name"},
	)
	prometheus.MustRegister(rpcLatencyHistogram)

	activeRequestsGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_active_requests_gauge",
			Help: "Number of active requests per service/endpoint",
		},
		[]string{"service", "endpoint", "tenant_name"},
	)
	prometheus.MustRegister(activeRequestsGauge)

	interceptor := NewMetricsInterceptor()

	ctx := metadata.NewIncomingContext(context.Background(),
		metadata.New(map[string]string{
			"x-request-source": "test-source",
			"x-tenant-name":    "test-tenant",
		}))

	info := &grpc.UnaryServerInfo{
		FullMethod: "/test.TestService/TestMethod",
	}

	// Test successful call
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return "response", nil
	}
	_, err := interceptor.UnaryInterceptor(ctx, "request", info, handler)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	// Test error case
	errorHandler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return nil, status.Error(codes.InvalidArgument, "test error")
	}
	_, err = interceptor.UnaryInterceptor(ctx, "request", info, errorHandler)
	if status.Code(err) != codes.InvalidArgument {
		t.Errorf("Expected InvalidArgument error, got %v", err)
	}

	// Verify metrics existence without checking exact values
	metrics, err := prometheus.DefaultGatherer.Gather()
	if err != nil {
		t.Fatalf("Failed to gather metrics: %v", err)
	}

	foundLatency := false
	foundActive := false
	for _, m := range metrics {
		if m.GetName() == "au_rpc_latency_histogram" {
			foundLatency = true
		}
		if m.GetName() == "au_active_requests_gauge" {
			foundActive = true
		}
	}

	if !foundLatency {
		t.Error("Expected au_rpc_latency_histogram metric to be present")
	}
	if !foundActive {
		t.Error("Expected au_active_requests_gauge metric to be present")
	}
}

func TestStreamInterceptor(t *testing.T) {
	// Reset metrics
	prometheus.Unregister(rpcLatencyHistogram)
	prometheus.Unregister(activeRequestsGauge)

	// Re-register metrics
	rpcLatencyHistogram = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_rpc_latency_histogram",
			Help: "Histogram of RPC latencies",
		},
		[]string{"service", "endpoint", "status_code", "request_source", "tenant_name"},
	)
	prometheus.MustRegister(rpcLatencyHistogram)

	activeRequestsGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_active_requests_gauge",
			Help: "Number of active requests per service/endpoint",
		},
		[]string{"service", "endpoint", "tenant_name"},
	)
	prometheus.MustRegister(activeRequestsGauge)

	interceptor := NewMetricsInterceptor()

	stream := &mockServerStream{
		ctx: metadata.NewIncomingContext(context.Background(),
			metadata.New(map[string]string{
				"x-request-source": "test-source",
				"x-tenant-name":    "test-tenant",
			})),
	}

	info := &grpc.StreamServerInfo{
		FullMethod: "/test.TestService/TestStream",
	}

	handler := func(srv interface{}, stream grpc.ServerStream) error {
		return nil
	}

	err := interceptor.StreamInterceptor(nil, stream, info, handler)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	// Verify metrics existence without checking exact values
	metrics, err := prometheus.DefaultGatherer.Gather()
	if err != nil {
		t.Fatalf("Failed to gather metrics: %v", err)
	}

	foundLatency := false
	foundActive := false
	for _, m := range metrics {
		if m.GetName() == "au_rpc_latency_histogram" {
			foundLatency = true
		}
		if m.GetName() == "au_active_requests_gauge" {
			foundActive = true
		}
	}

	if !foundLatency {
		t.Error("Expected au_rpc_latency_histogram metric to be present")
	}
	if !foundActive {
		t.Error("Expected au_active_requests_gauge metric to be present")
	}
}

// mockServerStream implements grpc.ServerStream interface for testing
type mockServerStream struct {
	ctx context.Context
	grpc.ServerStream
}

func (s *mockServerStream) Context() context.Context {
	return s.ctx
}

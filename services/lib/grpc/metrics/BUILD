load("//tools/bzl:python.bzl", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:go.bzl", "go_library", "go_test")

rust_library(
    name = "grpc_metrics",
    srcs = ["lib.rs"],
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + ["//services/lib/request_context:request_context_rs"],
)

rust_test(
    name = "grpc_metrics_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":grpc_metrics",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

py_library(
    name = "metrics",
    srcs = [
        "metrics.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("grpcio"),
        requirement("grpc-interceptor"),
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "metrics_test",
    srcs = ["metrics_test.py"],
    deps = [
        ":metrics",
        requirement("grpcio"),
        requirement("grpc-interceptor"),
        requirement("protobuf"),
        requirement("pytest-asyncio"),
    ],
)

go_library(
    name = "grpc_metrics_go",
    srcs = [
        "metrics.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/grpc/metrics",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "grpc_metrics_go_test",
    size = "small",
    srcs = ["metrics_test.go"],
    embed = [":grpc_metrics_go"],
    importpath = "github.com/augmentcode/augment/services/lib/grpc/metrics",
    deps = [
        "//services/lib/request_context:request_context_go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/testutil",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

import asyncio
import itertools
import time
import contextlib
from unittest.mock import Mock, patch

import grpc
import grpc.aio
import pytest
from grpc_interceptor.testing import dummy_client, DummyRequest

from metrics import MetricsServerInterceptor, AsyncMetricsServerInterceptor


@pytest.mark.parametrize(
    "slow, error",
    itertools.product([True, False], repeat=2),
)
def test_metrics_interceptor_rpc_latency(slow, error):
    """The metrics interceptor updates a Gauge with number of active requests and a latency Histogram
    with the durations of those requests. It's important that the beginning and end of each RPC is correctly
    captured.
    Intercepting a server-unary RPC is straightforward, but intercepting a server-streaming RPC is a little
    tricky. This test runs each combination of unary/streaming, fast/slow, and success/error cases to ensure
    that we're correctly capturing the beginning and end of each RPC.

    The cases are exercised using grpc_interceptor's test RPC server utility, the special_cases argument that
    allows injecting server-side behavior, and mocking time.time.

    To observe the behavior of the interceptor, we mock and record the calls to observe latency in the interceptor's
    Histogram.
    """
    interceptor = MetricsServerInterceptor()

    # Mock the Histogram so we can observe the latencies recorded
    histo_mock = Mock()
    interceptor._rpc_latency_histogram = Mock()
    interceptor._rpc_latency_histogram.labels.return_value = histo_mock

    # Mock time so we can control the "duration" of the RPCs.
    time_mock = Mock()
    time_mock.time.return_value = 0

    SLOW_TIME = 0.5

    def rpc(str, context: grpc.ServicerContext):
        if slow:
            time_mock.time.return_value += SLOW_TIME
        if error:
            context.abort(grpc.StatusCode.ABORTED, "aborted")
        return str

    with patch("metrics.time", new=time_mock), dummy_client(
        special_cases={"test": rpc}, interceptors=[interceptor]
    ) as client:
        expected_err = (
            pytest.raises(grpc.RpcError) if error else contextlib.nullcontext()
        )
        with expected_err:
            unary = client.Execute(DummyRequest(input="test"))
            assert unary.output == "test"
        assert histo_mock.observe.call_count == 1
        args, kwargs = histo_mock.observe.call_args
        assert kwargs == {}
        assert args == (SLOW_TIME,) if slow else (0,)

        with expected_err:
            stream = client.ExecuteServerStream(DummyRequest(input="test"))
            assert [x.output for x in stream] == ["t", "e", "s", "t"]
        assert histo_mock.observe.call_count == 2
        args, kwargs = histo_mock.observe.call_args
        assert kwargs == {}
        assert args == (SLOW_TIME,) if slow else (0,)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "slow, error",
    itertools.product([True, False], repeat=2),
)
async def test_async_metrics_interceptor_unary(slow, error):
    """Test that the async metrics interceptor correctly records metrics for unary RPCs."""
    interceptor = AsyncMetricsServerInterceptor()

    # Create an event to signal when metrics are recorded
    metrics_recorded_event = asyncio.Event()

    # Mock the Histogram so we can observe the latencies recorded
    histo_mock = Mock()

    # Configure the mock to set the event when observe is called
    histo_mock.observe.side_effect = (
        lambda *args, **kwargs: metrics_recorded_event.set()
    )

    interceptor._rpc_latency_histogram = Mock()
    interceptor._rpc_latency_histogram.labels.return_value = histo_mock

    # Mock time so we can control the "duration" of the RPCs.
    time_mock = Mock()
    time_mock.time.return_value = 0

    SLOW_TIME = 0.5

    async def rpc(str_input, context):
        if slow:
            time_mock.time.return_value += SLOW_TIME
        if error:
            await context.abort(grpc.StatusCode.ABORTED, "aborted")
        return str_input

    # Test unary-unary RPC using dummy_client with async server
    with patch("metrics.time", new=time_mock), dummy_client(
        special_cases={"test": rpc}, interceptors=[interceptor], aio_server=True
    ) as client:
        try:
            expected_err = (
                pytest.raises(grpc.RpcError) if error else contextlib.nullcontext()
            )
            with expected_err:
                unary = client.Execute(DummyRequest(input="test"))
                assert unary.output == "test"

            # Wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert histo_mock.observe.call_count == 1, "Metrics were not recorded"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                assert args == (SLOW_TIME,) if slow else (0,)
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded within timeout")

        except (
            Exception
        ) as e:  # Use a generic exception to catch both sync and async errors
            if not error:
                raise

            # For error cases, wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert (
                    histo_mock.observe.call_count == 1
                ), "Metrics were not recorded for error case"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                assert args == (SLOW_TIME,) if slow else (0,)
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded for error case within timeout")


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "slow, error",
    itertools.product([True, False], repeat=2),
)
async def test_async_metrics_interceptor_streaming(slow, error):
    """Test that the async metrics interceptor correctly records metrics for streaming RPCs."""
    interceptor = AsyncMetricsServerInterceptor()

    # Create an event to signal when metrics are recorded
    metrics_recorded_event = asyncio.Event()

    # Mock the Histogram so we can observe the latencies recorded
    histo_mock = Mock()

    # Configure the mock to set the event when observe is called
    histo_mock.observe.side_effect = (
        lambda *args, **kwargs: metrics_recorded_event.set()
    )

    interceptor._rpc_latency_histogram = Mock()
    interceptor._rpc_latency_histogram.labels.return_value = histo_mock

    # Mock time so we can control the "duration" of the RPCs.
    time_mock = Mock()
    time_mock.time.return_value = 0

    SLOW_TIME = 0.5

    async def rpc(str_input, context):
        if slow:
            time_mock.time.return_value += SLOW_TIME
        if error:
            await context.abort(grpc.StatusCode.ABORTED, "aborted")
        return str_input

    # Test unary-stream RPC using dummy_client with async server
    with patch("metrics.time", new=time_mock), dummy_client(
        special_cases={"test": rpc}, interceptors=[interceptor], aio_server=True
    ) as client:
        try:
            # For error cases, we expect an exception when consuming the stream
            # not when creating it
            stream = client.ExecuteServerStream(DummyRequest(input="test"))

            # For non-error case, verify the stream output
            if not error:
                assert [x.output for x in stream] == ["t", "e", "s", "t"]
            else:
                # For error case, consuming the stream should raise an exception
                with pytest.raises(grpc.RpcError):
                    # Force consumption of the stream
                    list(stream)

            # Wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert histo_mock.observe.call_count > 0, "Metrics were not recorded"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                assert args == (SLOW_TIME,) if slow else (0,)
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded within timeout")

        except Exception as e:  # Catch any exception that might be raised
            # Only expect this exception if we're testing the error case
            if not error:
                raise

            # For error cases, wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert (
                    histo_mock.observe.call_count > 0
                ), "Metrics were not recorded for error case"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                assert args == (SLOW_TIME,) if slow else (0,)
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded for error case within timeout")


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "slow, error",
    itertools.product([True, False], repeat=2),
)
async def test_async_metrics_interceptor_client_streaming(slow, error):
    """Test that the async metrics interceptor correctly records metrics for client streaming RPCs."""
    interceptor = AsyncMetricsServerInterceptor()

    # Create an event to signal when metrics are recorded
    metrics_recorded_event = asyncio.Event()

    # Mock the Histogram so we can observe the latencies recorded
    histo_mock = Mock()

    # Configure the mock to set the event when observe is called
    histo_mock.observe.side_effect = (
        lambda *args, **kwargs: metrics_recorded_event.set()
    )

    interceptor._rpc_latency_histogram = Mock()
    interceptor._rpc_latency_histogram.labels.return_value = histo_mock

    # Mock time so we can control the "duration" of the RPCs.
    time_mock = Mock()
    time_mock.time.return_value = 0

    SLOW_TIME = 0.5

    # For client streaming, we need to ensure the time is set correctly
    # The rpc function is called once for the concatenated result
    # So we need to make sure the time is set correctly

    async def rpc(str_input, context):
        # Set the time for slow cases
        if slow:
            time_mock.time.return_value += SLOW_TIME
        if error:
            await context.abort(grpc.StatusCode.ABORTED, "aborted")
        return str_input

    # Test client-streaming RPC using dummy_client with async server and client
    with patch("metrics.time", new=time_mock), dummy_client(
        special_cases={"test": rpc},
        interceptors=[interceptor],
        aio_server=True,
        aio_client=True,
    ) as client:
        try:
            # Create requests for client streaming
            requests = [DummyRequest(input="test")]

            # Create an async generator for the requests
            async def request_generator():
                for request in requests:
                    yield request

            # Execute client streaming RPC
            response = await client.ExecuteClientStream(request_generator())

            # For non-error case, verify the response
            if not error:
                assert response.output == "test"

            # Wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert histo_mock.observe.call_count == 1, "Metrics were not recorded"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                # For slow cases, we expect the time to be SLOW_TIME
                # For non-slow cases, we expect the time to be 0
                expected_time = SLOW_TIME if slow else 0
                assert (
                    args[0] == expected_time
                ), f"Expected {expected_time}, got {args[0]}"
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded within timeout")

        except Exception as e:  # Catch any exception that might be raised
            # Only expect this exception if we're testing the error case
            if not error:
                raise

            # For error cases, wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert (
                    histo_mock.observe.call_count == 1
                ), "Metrics were not recorded for error case"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                # For slow cases, we expect the time to be SLOW_TIME
                # For non-slow cases, we expect the time to be 0
                expected_time = SLOW_TIME if slow else 0
                assert (
                    args[0] == expected_time
                ), f"Expected {expected_time}, got {args[0]}"
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded for error case within timeout")


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "slow, error",
    itertools.product([True, False], repeat=2),
)
async def test_async_metrics_interceptor_bidirectional_streaming(slow, error):
    """Test that the async metrics interceptor correctly records metrics for bidirectional streaming RPCs."""
    interceptor = AsyncMetricsServerInterceptor()

    # Create an event to signal when metrics are recorded
    metrics_recorded_event = asyncio.Event()

    # Mock the Histogram so we can observe the latencies recorded
    histo_mock = Mock()

    # Configure the mock to set the event when observe is called
    histo_mock.observe.side_effect = (
        lambda *args, **kwargs: metrics_recorded_event.set()
    )

    interceptor._rpc_latency_histogram = Mock()
    interceptor._rpc_latency_histogram.labels.return_value = histo_mock

    # Mock time so we can control the "duration" of the RPCs.
    time_mock = Mock()
    time_mock.time.return_value = 0

    SLOW_TIME = 0.5

    # For bidirectional streaming, we need a different approach for error cases
    # We'll use a flag to track if the error has been triggered
    error_triggered = False

    async def rpc(str_input, context):
        nonlocal error_triggered
        # Set the time for slow cases
        if slow:
            time_mock.time.return_value += SLOW_TIME
        # Only abort on the first request for error cases
        if error and not error_triggered:
            error_triggered = True
            await context.abort(grpc.StatusCode.ABORTED, "aborted")
        return str_input

    # Test bidirectional streaming RPC using dummy_client with async server and client
    with patch("metrics.time", new=time_mock), dummy_client(
        special_cases={"test": rpc},
        interceptors=[interceptor],
        aio_server=True,
        aio_client=True,
    ) as client:
        try:
            # Create requests for bidirectional streaming
            requests = [DummyRequest(input="test")]

            # Create an async generator for the requests
            async def request_generator():
                for request in requests:
                    yield request

            # Execute bidirectional streaming RPC
            response_stream = client.ExecuteClientServerStream(request_generator())

            # For error cases, we expect an exception when consuming the stream
            if error:
                with pytest.raises(grpc.RpcError):
                    async for response in response_stream:
                        pass  # Just consume the stream
            else:
                # For non-error cases, verify the response
                responses = []
                async for response in response_stream:
                    responses.append(response.output)
                assert responses == ["test"]

            # Wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert histo_mock.observe.call_count == 1, "Metrics were not recorded"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                # For slow cases, we expect the time to be SLOW_TIME
                # For non-slow cases, we expect the time to be 0
                expected_time = SLOW_TIME if slow else 0
                assert (
                    args[0] == expected_time
                ), f"Expected {expected_time}, got {args[0]}"
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded within timeout")

        except Exception as e:  # Catch any exception that might be raised
            # Only expect this exception if we're testing the error case
            if not error:
                raise

            # For error cases, wait for metrics to be recorded with a timeout
            try:
                await asyncio.wait_for(metrics_recorded_event.wait(), timeout=1.0)
                assert (
                    histo_mock.observe.call_count == 1
                ), "Metrics were not recorded for error case"
                args, kwargs = histo_mock.observe.call_args
                assert kwargs == {}
                # For slow cases, we expect the time to be SLOW_TIME
                # For non-slow cases, we expect the time to be 0
                expected_time = SLOW_TIME if slow else 0
                assert (
                    args[0] == expected_time
                ), f"Expected {expected_time}, got {args[0]}"
            except asyncio.TimeoutError:
                pytest.fail("Metrics were not recorded for error case within timeout")

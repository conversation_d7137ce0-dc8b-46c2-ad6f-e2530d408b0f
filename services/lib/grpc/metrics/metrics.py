"""Module for shared server metrics."""

import asyncio
import time
from contextlib import contextmanager, ExitStack, asynccontextmanager, AsyncExitStack
from typing import Any, Callable, AsyncIterator

import grpc
from grpc_interceptor import (
    AsyncServerInterceptor,
    ServerInterceptor,
    parse_method_name,
)
from prometheus_client import Gauge, Histogram


# Define collectors at module level to ensure they are shared between sync and async implementations
# This avoids re-registering them, which is disallowed by Prometheus
_ACTIVE_REQUESTS_GAUGE = Gauge(
    "au_active_requests_gauge",
    "Number of active requests per service/endpoint",
    ["service", "endpoint", "tenant_name"],
)

# NB: This is NOT a real distribution of active request counts. A value is added every time
# _active_requests_gauge increments. The purpose of this metric is to estimate the maximum
# number of active requests being handled (to monitor thread utilization). An alternative
# implementation would be to manually keep track of the maximum value per scrape period and
# return it in a CustomCollector, but that implementation is tricky if we want to keep
# service/endpoint labels. Buckets are rounded powers of 1.2 (without duplicates).
_ACTIVE_REQUESTS_HISTOGRAM = Histogram(
    "au_active_requests_histogram",
    "Histogram of active requests per service/endpoint. Should only be used to find a max",
    ["service", "endpoint", "tenant_name"],
    buckets=sorted({int(1.2**x) for x in range(1, 33)}),
)

# Histogram of RPC latencies. This also counts requests. The default histogram buckets are
# exponential and work well for our purposes:
# https://github.com/prometheus/client_python/blob/3d786303021b9ae111bd27f2d5c26b3972bdbb3c/prometheus_client/metrics.py#L565
_RPC_LATENCY_HISTOGRAM = Histogram(
    "au_rpc_latency_histogram",
    "Histogram of RPC latencies",
    ["service", "endpoint", "status_code", "request_source", "tenant_name"],
)


class MetricsServerInterceptor(ServerInterceptor):
    """Interceptor for server metrics.

    This interceptor can be used to add metrics across all of our services without reimplementing
    them in each one. We use grpc-interceptor instead of the standard grpc library because
    grpc.ServerInterceptor makes it really difficult to do any response processing.
    """

    # Reference the module-level metrics
    _active_requests_gauge = _ACTIVE_REQUESTS_GAUGE
    _active_requests_histogram = _ACTIVE_REQUESTS_HISTOGRAM
    _rpc_latency_histogram = _RPC_LATENCY_HISTOGRAM

    @contextmanager
    def _record(self, method_name: str, context: grpc.ServicerContext):
        parsed_method_name = parse_method_name(method_name)
        start_time = time.time()
        service = parsed_method_name.service
        endpoint = parsed_method_name.method
        request_source = dict(context.invocation_metadata()).get(
            "x-request-source", "unknown"
        )
        tenant_name = dict(context.invocation_metadata()).get(
            "x-tenant-name", "unknown"
        )

        labels = (service, endpoint, tenant_name)
        active_gauge = self._active_requests_gauge.labels(*labels)
        active_histo = self._active_requests_histogram.labels(*labels)

        active_gauge.inc()
        # We use _active_requests_gauge's private _value so that we don't need to keep track of
        # a separate counter ourselves.
        active_requests = active_gauge._value.get()  # pylint: disable=protected-access
        active_histo.observe(active_requests)

        try:
            yield None
        finally:
            status_code = context.code()
            active_gauge.dec()
            self._rpc_latency_histogram.labels(
                service, endpoint, status_code, request_source, tenant_name
            ).observe(time.time() - start_time)

    def _iterate_results(self, iterator: Any, stack: ExitStack) -> Any:
        with stack:
            yield from iterator

    def intercept(
        self,
        method: Callable,
        request_or_iterator: Any,
        context: grpc.ServicerContext,
        method_name: str,
    ) -> Any:
        """Intercept an RPC.

        See grpc-interceptor documentation for details:
        https://github.com/d5h-foss/grpc-interceptor/

        Some notes:
        For server-unary RPCs, invoking method(request_or_iterator, context)
        will return the RPC response (or raise). The RPC can be considered
        complete, and we should also return that response.

        For server-streaming RPCs, invoking method(request_or_iterator, context)
        will return some iterable of RPC responses. The RPC should not be
        considered complete until the iterable is exhausted. Consider the case where
        the iterable is implemented as a generator.
        We must return an iterable of responses AND observe the end of that
        iterable, but we cannot include a `yield` directly in this method, as
        that would unconditionally make intercept() a generator, which breaks the
        server-unary RPC case. So we instead call and return the result of
        _iterate_results().
        """
        # We use an exit stack so that the context manager can be moved into _iterate_results
        # in the event that this is a server-streaming RPC
        with ExitStack() as stack:
            stack.enter_context(self._record(method_name, context))

            result_or_iterator = method(request_or_iterator, context)
            if hasattr(result_or_iterator, "__iter__"):
                return self._iterate_results(result_or_iterator, stack.pop_all())
            else:
                return result_or_iterator


class AsyncMetricsServerInterceptor(AsyncServerInterceptor):
    """Async interceptor for server metrics.

    This is the async equivalent of MetricsServerInterceptor and follows a similar pattern,
    but with several key differences:
    1. Uses async/await and AsyncExitStack instead of ExitStack
    2. Handles both sync and async contexts for metadata and status code retrieval
    3. Provides special handling for client streaming requests by buffering them
    4. Properly handles coroutines returned from RPC methods
    5. Detects and processes async iterators for server streaming responses
    """

    # Reference the module-level metrics
    _active_requests_gauge = _ACTIVE_REQUESTS_GAUGE
    _active_requests_histogram = _ACTIVE_REQUESTS_HISTOGRAM
    _rpc_latency_histogram = _RPC_LATENCY_HISTOGRAM

    @asynccontextmanager
    async def _record(self, method_name: str, context: grpc.ServicerContext):
        """Record metrics for a request.

        This is the async equivalent of the sync _record method. It creates a context manager
        that records metrics for the duration of the RPC.

        Unlike the sync version, this method handles both synchronous and asynchronous contexts
        by detecting and awaiting coroutines when necessary. This allows the interceptor to work
        with different gRPC context implementations and in testing scenarios.

        Args:
            method_name: The full method name, e.g., "/package.Service/Method".
            context: The ServicerContext for the RPC, which may have sync or async methods.

        Yields:
            None
        """
        parsed_method_name = parse_method_name(method_name)
        start_time = time.time()
        service = parsed_method_name.service
        endpoint = parsed_method_name.method

        # Get metadata from the context
        # Handle both sync and async contexts
        try:
            # Try async context first
            metadata_coro = context.invocation_metadata()
            if hasattr(metadata_coro, "__await__"):
                # It's awaitable, so await it
                metadata = dict(await metadata_coro)
            else:
                # It's not awaitable, so it's already the metadata
                metadata = dict(metadata_coro)
        except (AttributeError, TypeError):
            # Fallback for any other context type
            metadata = {}

        request_source = metadata.get("x-request-source", "unknown")
        tenant_name = metadata.get("x-tenant-name", "unknown")

        labels = (service, endpoint, tenant_name)
        active_gauge = self._active_requests_gauge.labels(*labels)
        active_histo = self._active_requests_histogram.labels(*labels)

        active_gauge.inc()
        # We use _active_requests_gauge's private _value so that we don't need to keep track of
        # a separate counter ourselves.
        active_requests = active_gauge._value.get()  # pylint: disable=protected-access
        active_histo.observe(active_requests)

        try:
            yield None
        finally:
            # Get status code, handling both sync and async contexts
            try:
                # Try async context first
                code_result = context.code()
                if hasattr(code_result, "__await__"):
                    # It's awaitable, so await it
                    status_code = await code_result
                else:
                    # It's not awaitable, so it's already the status code
                    status_code = code_result
            except (AttributeError, TypeError):
                # Fallback for any other context type
                # Use OK (0) as the default status code when context.code() is not available
                # This might happen with custom context implementations or in testing scenarios
                status_code = grpc.StatusCode.OK

            active_gauge.dec()
            self._rpc_latency_histogram.labels(
                service, endpoint, status_code, request_source, tenant_name
            ).observe(time.time() - start_time)

    async def _iterate_results(
        self, iterator: AsyncIterator, stack: AsyncExitStack
    ) -> AsyncIterator:
        """Iterate through results while recording metrics for server streaming responses.

        This is the async equivalent of the sync _iterate_results method. It takes an async iterator
        and an AsyncExitStack, and yields from the iterator within the context of the stack.

        This method is specifically used for server streaming RPCs to ensure that metrics are
        properly recorded even after all responses have been yielded or if an error occurs during
        streaming. The AsyncExitStack ensures that the _record context manager is properly exited
        when the iterator is exhausted or if an exception occurs.

        Args:
            iterator: The async iterator of responses from a server streaming RPC.
            stack: The AsyncExitStack containing the _record context manager.

        Yields:
            The responses from the iterator.
        """
        async with stack:
            async for response in iterator:
                yield response

    async def intercept(
        self,
        method: Callable,
        request_or_iterator: Any,
        context: grpc.ServicerContext,
        method_name: str,
    ) -> Any:
        """Intercept an RPC.

        This is the async version of the intercept method from the sync MetricsServerInterceptor.
        While it follows a similar pattern, it has significant additional complexity to handle
        async-specific scenarios including client streaming, coroutines, and async iterators.

        See grpc-interceptor documentation for details:
        https://github.com/d5h-foss/grpc-interceptor/

        The method handles four different RPC types:
        1. Unary-Unary: Single request, single response
        2. Unary-Stream: Single request, stream of responses
        3. Stream-Unary: Stream of requests, single response
        4. Stream-Stream: Stream of requests, stream of responses

        For client streaming RPCs (3 and 4), we buffer all requests in memory to ensure
        we can both consume the iterator and pass it to the method.

        For server-unary RPCs (1 and 3), invoking method(request_or_iterator, context)
        will return a coroutine that resolves to the RPC response. We await this coroutine
        and return the result.

        For server-streaming RPCs (2 and 4), invoking method(request_or_iterator, context)
        will return a coroutine that resolves to an async iterator of responses. We await
        this coroutine and then wrap the iterator with _iterate_results() to ensure metrics
        are properly recorded when the iterator is exhausted.

        Args:
            method: The RPC method implementation.
            request_or_iterator: The RPC request or an async iterator of requests.
            context: The ServicerContext for the RPC, which may have sync or async methods.
            method_name: The full method name, e.g., "/package.Service/Method".

        Returns:
            For unary responses: The result of the RPC method after awaiting.
            For streaming responses: An async iterator that yields results from the RPC method.
        """
        # Handle streaming requests (client streaming)
        # We need to check for __aiter__ to detect async iterators (client streaming requests)
        # We use hasattr() instead of isinstance() because it works with any object that implements
        # the async iterator protocol, regardless of its class hierarchy
        if hasattr(request_or_iterator, "__aiter__"):
            # For client streaming, we need to collect all requests first
            # This is because we can't both consume the iterator and pass it to the method
            # IMPORTANT: This approach has memory implications:
            # 1. All requests are buffered in memory before processing begins
            # 2. For large streams or large messages, this could lead to high memory usage
            # 3. TODO: Consider implementing a more memory-efficient approach for services with
            #    very large client streams (e.g., using a pipe or queue)
            # 4. For most typical gRPC use cases with reasonably sized messages, this
            #    implementation is acceptable
            requests = []
            async for request in request_or_iterator:
                requests.append(request)

            # Create a new async iterator from the collected requests
            async def new_request_iterator():
                for request in requests:
                    yield request

            # Replace the original iterator with our new one
            request_or_iterator = new_request_iterator()

        # We use an AsyncExitStack so that the context manager can be moved into _iterate_results
        # in the event that this is a server-streaming RPC
        async with AsyncExitStack() as stack:
            # Enter the record context
            await stack.enter_async_context(self._record(method_name, context))

            # Call the method
            # First get the result without awaiting - this is important because:
            # 1. We need to detect if the result is a coroutine that needs to be awaited
            # 2. Some gRPC methods return direct values (in tests or with sync implementations)
            # 3. Others return coroutines (with async implementations)
            # 4. We need to handle both cases correctly
            result = method(request_or_iterator, context)

            # Check if the result is a coroutine using asyncio.iscoroutine() which is more reliable
            # than checking for __await__ attribute or other approaches
            if asyncio.iscoroutine(result):
                # It's a coroutine, so await it to get the actual result
                result_or_iterator = await result
            else:
                # It's either an async iterator or a direct result, use it directly
                # This happens with sync implementations wrapped for async use
                result_or_iterator = result

            if hasattr(result_or_iterator, "__aiter__"):
                # If it's an async iterator (server streaming response), wrap it with our _iterate_results
                # We use stack.pop_all() to transfer ownership of the context manager to _iterate_results
                # This ensures metrics are recorded properly when the iterator is exhausted
                return self._iterate_results(result_or_iterator, stack.pop_all())
            else:
                # If it's not an iterator (unary response), just return the result directly
                # The metrics will be recorded when this context manager exits
                return result_or_iterator

package metrics

import (
	"context"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var (
	rpcLatencyHistogram = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name: "au_rpc_latency_histogram",
			Help: "Histogram of RPC latencies",
		},
		[]string{"service", "endpoint", "status_code", "request_source", "tenant_name"},
	)

	activeRequestsGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "au_active_requests_gauge",
			Help: "Number of active requests per service/endpoint",
		},
		[]string{"service", "endpoint", "tenant_name"},
	)
)

type MetricsInterceptor struct{}

func NewMetricsInterceptor() *MetricsInterceptor {
	return &MetricsInterceptor{}
}

// parseMethod splits a gRPC method into package, service, and method parts
func parseMethod(fullMethod string) (service, method string) {
	// Remove leading slash
	fullMethod = strings.TrimPrefix(fullMethod, "/")

	parts := strings.Split(fullMethod, "/")
	if len(parts) != 2 {
		return "unknown", "unknown"
	}

	serviceParts := strings.Split(parts[0], ".")
	service = serviceParts[len(serviceParts)-1]
	return service, parts[1]
}

// getMetadataValue safely extracts a metadata value or returns "unknown"
func getMetadataValue(ctx context.Context, key string) string {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "unknown"
	}
	values := md.Get(key)
	if len(values) == 0 {
		return "unknown"
	}
	return values[0]
}

func (m *MetricsInterceptor) UnaryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	service, endpoint := parseMethod(info.FullMethod)
	requestSource := getMetadataValue(ctx, "x-request-source")
	tenantName := getMetadataValue(ctx, "x-tenant-name")

	// Increment active requests
	activeRequestsGauge.WithLabelValues(service, endpoint, tenantName).Inc()

	startTime := time.Now()
	resp, err := handler(ctx, req)
	duration := time.Since(startTime).Seconds()

	// Decrement active requests
	activeRequestsGauge.WithLabelValues(service, endpoint, tenantName).Dec()

	// Get status code
	statusCode := "OK"
	if err != nil {
		statusCode = status.Code(err).String()
	}

	// Record latency
	rpcLatencyHistogram.WithLabelValues(
		service,
		endpoint,
		statusCode,
		requestSource,
		tenantName,
	).Observe(duration)

	return resp, err
}

func (m *MetricsInterceptor) StreamInterceptor(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	service, endpoint := parseMethod(info.FullMethod)
	ctx := ss.Context()
	requestSource := getMetadataValue(ctx, "x-request-source")
	tenantName := getMetadataValue(ctx, "x-tenant-name")

	// Increment active requests
	activeRequestsGauge.WithLabelValues(service, endpoint, tenantName).Inc()

	startTime := time.Now()
	err := handler(srv, ss)
	duration := time.Since(startTime).Seconds()

	// Decrement active requests
	activeRequestsGauge.WithLabelValues(service, endpoint, tenantName).Dec()

	// Get status code
	statusCode := "OK"
	if err != nil {
		statusCode = status.Code(err).String()
	}

	// Record latency
	rpcLatencyHistogram.WithLabelValues(
		service,
		endpoint,
		statusCode,
		requestSource,
		tenantName,
	).Observe(duration)

	return err
}

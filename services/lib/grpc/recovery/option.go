package recovery

import "context"

type Option func(opts *options)

type options struct {
	HandlerFunc *ContextHandlerFunc
}

// Override the default panic handler with custom logic
func WithHandlerFunc(handler HandlerFunc) Option {
	return func(opts *options) {
		var handler ContextHandlerFunc = func(ctx context.Context, p any, method string) error {
			return handler(p, method)
		}
		opts.HandlerFunc = &handler
	}
}

// Override the default panic handler with one that accepts a server context for
// additional context for the error handling
func WithContextHandlerFunc(handler ContextHandlerFunc) Option {
	return func(opts *options) {
		opts.HandlerFunc = &handler
	}
}

func evaluateOptions(opts []Option) options {
	opt := options{}
	for _, o := range opts {
		o(&opt)
	}
	return opt
}

package recovery

import (
	"context"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

func grpcContext() context.Context {
	requestContext := requestcontext.New(
		"request-id", "request-session-id", "request-source", secretstring.New("auth-token"),
	)
	return metadata.NewIncomingContext(context.Background(), requestContext.ToMetadata())
}

func unaryServerInfo() *grpc.UnaryServerInfo {
	return &grpc.UnaryServerInfo{
		FullMethod: "/test.TestService/TestMethod",
	}
}

func unaryHandlerPanic() grpc.UnaryHandler {
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		panic("I always panic")
	}
}

func StreamServerInfo() *grpc.StreamServerInfo {
	return &grpc.StreamServerInfo{
		FullMethod: "/test.TestService/StreamTestMethod",
	}
}

// mock out this type so we can actually test it.
type dummyss struct {
	ctx context.Context
}

func (s *dummyss) Context() context.Context {
	return s.ctx
}

func (s *dummyss) SendMsg(m interface{}) error {
	return nil
}

func (s *dummyss) RecvMsg(m interface{}) error {
	return nil
}

func (d *dummyss) SetHeader(md metadata.MD) error {
	return nil
}

func (d *dummyss) SendHeader(md metadata.MD) error {
	return nil
}

func (d *dummyss) SetTrailer(md metadata.MD) {}

func serverStream() grpc.ServerStream {
	requestContext := requestcontext.New(
		"request-id", "request-session-id", "request-source", secretstring.New("auth-token"),
	)
	ctx := metadata.NewIncomingContext(context.Background(), requestContext.ToMetadata())
	return &dummyss{ctx: ctx}
}

func streamHandlerPanic() grpc.StreamHandler {
	return func(srv interface{}, stream grpc.ServerStream) error {
		panic("I always panic")
	}
}

func TestUnaryServerIntercept(t *testing.T) {
	t.Run("Default", func(t *testing.T) {
		uut := UnaryServerInterceptor()
		assert.Panics(t, func() {
			uut(grpcContext(), nil, unaryServerInfo(), unaryHandlerPanic())
		})
	})

	t.Run("Overide with handlerFunc", func(t *testing.T) {
		uut := UnaryServerInterceptor(WithHandlerFunc(func(p any, method string) error {
			return status.Error(codes.Unavailable, "Panic handled")
		}))
		_, err := uut(grpcContext(), nil, unaryServerInfo(), unaryHandlerPanic())
		assert.Error(t, err, "Panic should have been converted to an error")
		assert.Equal(t, status.Code(err), codes.Unavailable, "Error should be a gRPC error with code 'Unavailable'")
		assert.Equal(t, status.Convert(err).Message(), "Panic handled", "Error message should match returned error message")
	})

	t.Run("Overide with contextHandlerFunc", func(t *testing.T) {
		uut := UnaryServerInterceptor(WithContextHandlerFunc(func(ctx context.Context, p any, method string) error {
			rc, _ := requestcontext.FromGrpcContext(ctx)
			return status.Errorf(codes.Unavailable, "%s", rc.RequestId.String())
		}))
		_, err := uut(grpcContext(), nil, unaryServerInfo(), unaryHandlerPanic())
		assert.Error(t, err, "Panic should have been converted to an error")
		assert.Equal(t, status.Code(err), codes.Unavailable, "Error should be a gRPC error with code 'Unavailable'")
		assert.Equal(t, status.Convert(err).Message(), "request-id", "Error message should be 'request-id'")
	})
}

func TestStreamingServerIntercept(t *testing.T) {
	t.Run("Default", func(t *testing.T) {
		uut := StreamingServerInterceptor()
		assert.Panics(t, func() {
			uut(nil, serverStream(), StreamServerInfo(), streamHandlerPanic())
		})
	})

	t.Run("Overide with handlerFunc", func(t *testing.T) {
		uut := StreamingServerInterceptor(WithHandlerFunc(func(p any, method string) error {
			return status.Error(codes.Unavailable, "Panic handled")
		}))
		err := uut(nil, serverStream(), StreamServerInfo(), streamHandlerPanic())
		assert.Error(t, err, "Panic should have been converted to an error")
		assert.Equal(t, status.Code(err), codes.Unavailable, "Error should be a gRPC error with code 'Unavailable'")
		assert.Equal(t, status.Convert(err).Message(), "Panic handled", "Error message should match returned error message")
	})

	t.Run("Overide with contextHandlerFunc", func(t *testing.T) {
		uut := StreamingServerInterceptor(WithContextHandlerFunc(func(ctx context.Context, p any, method string) error {
			rc, _ := requestcontext.FromGrpcContext(ctx)
			return status.Errorf(codes.Unavailable, "%s", rc.RequestId.String())
		}))
		err := uut(nil, serverStream(), StreamServerInfo(), streamHandlerPanic())
		assert.Error(t, err, "Panic should have been converted to an error")
		assert.Equal(t, status.Code(err), codes.Unavailable, "Error should be a gRPC error with code 'Unavailable'")
		assert.Equal(t, status.Convert(err).Message(), "request-id", "Error message should be 'request-id'")
	})
}

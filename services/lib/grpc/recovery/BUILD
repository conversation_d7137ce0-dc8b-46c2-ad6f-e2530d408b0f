load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "grpc_recovery_go",
    srcs = [
        "interceptor.go",
        "option.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/grpc/recovery",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "grpc_recovery_go_test",
    srcs = ["interceptor_test.go"],
    embed = [":grpc_recovery_go"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/lib/request_context:request_context_go",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

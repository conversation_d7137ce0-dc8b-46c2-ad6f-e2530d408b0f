// gRPC server panic recovery functions

// By default unhandled panics in a gRPC handler will crash the whole server. This is
// generally undesirable, and we would like to handle this consistently across all go
// services, hence the creation of this common library

// We evaluated using the recovery handlers in grpc-ecosystem/go-grpc-middleware/v2,
// but their framework is not flexible enough as it provides no means to log the erroring
// RPC method.
package recovery

import (
	"context"
	"runtime/debug"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// handler that does not accept the server request context
type HandlerFunc func(p any, method string) error

// handler that accepts the server request context. Can be used to log any additional
// parameters that are passed through the server request context.
type ContextHandlerFunc func(ctx context.Context, p any, method string) error

// Interceptor for the UnaryServer
// NOTE: Default handler will propagate the panic, which will bring down the whole
// service
func UnaryServerInterceptor(options ...Option) grpc.UnaryServerInterceptor {
	opts := evaluateOptions(options)
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (_ any, err error) {
		defer func() {
			if r := recover(); r != nil {
				err = handlePanic(ctx, r, info.FullMethod, opts.HandlerFunc)
			}
		}()
		return handler(ctx, req)
	}
}

// Interceptor for the StreamingServer
// NOTE: Default handler will propagate the panic, which will bring down the whole
// service
func StreamingServerInterceptor(options ...Option) grpc.StreamServerInterceptor {
	opts := evaluateOptions(options)
	return func(srv any, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) (err error) {
		defer func() {
			if r := recover(); r != nil {
				err = handlePanic(ss.Context(), r, info.FullMethod, opts.HandlerFunc)
			}
		}()
		return handler(srv, ss)
	}
}

// Log the panic, treating the context as a request_context if possible to install the
// session id and request id into the log for observability.
// NOTE: Should only be called from within a deferred panic recovery function, otherwise
// the stack trace will be nonsense
func LogPanic(ctx context.Context, p any, method string) {
	logctx := ctx
	if rc, err := requestcontext.FromGrpcContext(ctx); err == nil {
		logctx = rc.AnnotateLogContext(ctx)
	}
	// Stack from within a panic handler will return the stack of the panicking thread
	stack_trace := debug.Stack()
	// build log line with structured fields populated for easier filtering
	log.Ctx(logctx).Error().
		Interface("panic", p).
		Bytes("stack", stack_trace).
		Str("method", method).
		Msgf("Unhandled panic in method '%s'", method)
}

// Standard converter for panics to errors
// Intended usage is to return this error from the custom panic handler to send a rich
// gRPC error with Status code.
// we provide this to prevent each service from doing its own thing if it wants to catch
// panics at the top-level panic handler
func PanicToGrpcErr(p any) error {
	return status.Error(codes.Internal, "Unhandled panic")
}

// evaluate the options passed to the interceptor and return the appropriate handler
func handlePanic(ctx context.Context, p any, method string, handler *ContextHandlerFunc) error {
	if handler != nil {
		return (*handler)(ctx, p, method)
	}
	// The explicit panic log will be emitted as a structured log; there will be two more
	// logs emitted by the panic propagation from the go runtime that will be a raw log.
	LogPanic(ctx, p, method)
	panic("Fatal error: unhandled panic")
}

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "stream_mux_proto",
    srcs = ["stream_mux.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:duration_proto",
    ],
)

py_grpc_library(
    name = "stream_mux_py_proto",
    protos = [
        ":stream_mux_proto",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//third_party/proto:googleapis_rpc_code_py_proto",
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

go_grpc_library(
    name = "stream_mux_go_proto",
    importpath = "github.com/augmentcode/augment/services/lib/grpc/stream_mux/proto",
    proto = ":stream_mux_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
    ],
)

rust_library(
    name = "grpc_stream_mux_rs",
    srcs = [
        "lib.rs",
        "metrics.rs",
    ],
    aliases = aliases(),
    crate_name = "grpc_stream_mux",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "@crates//:futures",
        "@crates//:lazy_static",
        "@crates//:prometheus",
    ],
)

rust_test(
    name = "grpc_stream_mux_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":grpc_stream_mux_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

py_library(
    name = "client",
    srcs = [
        "client.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("grpcio"),
        requirement("opentelemetry-api"),
        ":stream_mux_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
    ],
)

py_library(
    name = "server",
    srcs = [
        "server.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("grpcio"),
        requirement("opentelemetry-api"),
        ":stream_mux_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["client_test.py"],
    deps = [
        ":client",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/lib/grpc/stream_mux:stream_mux_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

ts_proto_library(
    name = "stream_mux_ts_proto",
    node_modules = "//:node_modules",
    proto = ":stream_mux_proto",
    visibility = ["//services:__subpackages__"],
)

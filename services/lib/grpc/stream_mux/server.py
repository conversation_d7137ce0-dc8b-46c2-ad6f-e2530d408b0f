import functools
import logging
from typing import <PERSON><PERSON><PERSON>, Callable, Protocol

import grpc
import pydantic
import opentelemetry.trace as trace
import opentelemetry.context as otel_context
import opentelemetry.propagate as otel_propagate

from services.lib.request_context.request_context import RequestContext
from services.lib.grpc.stream_mux.stream_mux_pb2 import MuxedRequest, MuxedResponse
from services.lib.grpc.stream_mux.stream_mux_pb2 import StatusCode as LegacyStatusCode
from google.rpc import code_pb2


class SupportedRequest(Protocol):
    @property
    def context(self) -> MuxedRequest: ...


class SupportedResponse(Protocol):
    @property
    def context(self) -> MuxedResponse: ...


ReqT = TypeVar("ReqT", bound=SupportedRequest)
RespT = TypeVar("RespT", bound=SupportedResponse)
T = TypeVar("T")


def extract_context(
    stream_context: RequestContext, request: MuxedRequest
) -> RequestContext:
    REDACT = "<redacted>"
    if request.auth_token_secret == REDACT:
        raise ValueError("auth_token apparently already extracted from MuxedRequest")
    token = pydantic.SecretStr(request.auth_token_secret)
    request.auth_token_secret = REDACT

    # This is not strictly required but it is how we plan to use this today: all
    # requests routed through the RPC will be for the same session.
    if request.request_session_id != stream_context.request_session_id:
        logging.error(
            "Request session id does not match the session id of the streaming RPC %s != %s",
            request.request_session_id,
            stream_context.request_session_id,
        )
        raise ValueError("session_id mismatch")

    timeout_s: float | None = None
    if request.HasField("timeout"):
        timeout_s = request.timeout.seconds + request.timeout.nanos / 10 * 9

    return RequestContext.create(
        request_source=request.request_source or "unknown",
        auth_token=token,
        request_id=request.request_id,
        request_session_id=request.request_session_id,
        timeout=timeout_s,
    )


def open_telemetry_span(tracer: trace.Tracer, span_name: str):
    def decorator(fn: Callable[[ReqT], T]) -> Callable[[ReqT], T]:
        @functools.wraps(fn)
        def wrapped_fn(request: ReqT) -> T:
            token = None
            try:
                token = otel_context.attach(
                    otel_propagate.extract(
                        request.context.otel_context, otel_context.get_current()
                    )
                )
                with tracer.start_span(span_name):
                    return fn(request)
            finally:
                if token is not None:
                    otel_context.detach(token)

        return wrapped_fn

    return decorator


def ensure_backwards_compatibility(response: SupportedResponse):
    # Caller has filled in latest field 'status', but also needs to duplicate
    # that information in fields 'status_code' and 'status_detail' for old
    # clients
    if response.context.status.code == code_pb2.Code.OK:  # pylint: disable=no-member # type: ignore
        response.context.status_code = LegacyStatusCode.OK
    elif response.context.status.code == code_pb2.Code.CANCELLED:  # pylint: disable=no-member # type: ignore
        response.context.status_code = LegacyStatusCode.CANCELLED
    elif response.context.status.code == code_pb2.Code.DEADLINE_EXCEEDED:  # pylint: disable=no-member # type: ignore
        response.context.status_code = LegacyStatusCode.DEADLINE_EXCEEDED
    else:
        # All other errors collapse to ERROR (which on the client becomes UNKNOWN)
        response.context.status_code = LegacyStatusCode.ERROR
    response.context.status_detail = response.context.status.message  # pylint: disable=no-member # type: ignore

import abc
import logging
import queue
import threading
import typing
from typing import Callable, Iterable, Optional

import grpc
import opentelemetry.propagate as otel_propagate

from services.lib.request_context.request_context import RequestContext, clamp_timeout
from services.lib.grpc.stream_mux.stream_mux_pb2 import MuxedRequest, MuxedResponse
from services.lib.grpc.stream_mux import stream_mux_pb2
from google.rpc import code_pb2


def capture_context(
    request_context: RequestContext,
    timeout: float | None = None,
) -> MuxedRequest:
    if request_context.auth_token is None:
        raise ValueError(
            "Request context must include auth_token to be routed over RPC stream"
        )
    mr = MuxedRequest(
        request_id=request_context.request_id,
        request_session_id=request_context.request_session_id,
        auth_token_secret=request_context.auth_token.get_secret_value(),
        request_source=request_context.request_source,
    )
    otel_propagate.inject(mr.otel_context)

    # With regular GRPC calls, timeout is not automatically propagated from a
    # RequestContext object to the call, even via RequestContext.to_metadata().
    # Timeout must be explicitly passed as an option to the call. To avoid
    # surprise, the stream_mux library matches that behavior.
    if timeout is not None:
        mr.timeout.FromMilliseconds(int(timeout * 1000))
    return mr


# It is expected that initial uses are all based upon grpc streams, so adopting
# the grpc status codes
class StreamMuxError(grpc.RpcError):
    def __init__(self, status_code: grpc.StatusCode, msg: Optional[str] = None):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> Optional[str]:
        return self.msg

    def __str__(self) -> str:
        return f"StreamMuxError({self.status_code}: {self.msg})"


# grpc.StatusCode couldn't just be a normal enum; its values are tuples,
# which makes it difficult to cast from int to the enum.
# Values are densely packed starting from 0, so can use list. This is tested
# in :client_test
ORDERED_STATUS_CODES = list(sorted(grpc.StatusCode, key=lambda x: x.value[0]))


def proto_field_to_status_code(value: int):
    if value < 0 or value >= len(ORDERED_STATUS_CODES):
        return None
    return ORDERED_STATUS_CODES[value]


def raise_on_error(response: MuxedResponse):
    code = response.status.code  # pylint: disable=no-member # type: ignore
    message = response.status.message  # pylint: disable=no-member # type: ignore

    # Work with old servers that didn't use 'status' field
    if code == 0 and response.status_code != stream_mux_pb2.StatusCode.OK:
        # They support fewer error codes, though still correspond in value to gRPC codes
        code = int(response.status_code)
        message = response.status_detail

    if code != code_pb2.Code.OK:  # pylint: disable=no-member # type: ignore
        code = proto_field_to_status_code(code)
        if code is None:
            logging.warning(f"Unknown status code {code} received")
            code = grpc.StatusCode.UNKNOWN
        raise StreamMuxError(code, message)


class SupportedRequest(typing.Protocol):
    @property
    def context(self) -> MuxedRequest: ...


class SupportedResponse(typing.Protocol):
    @property
    def context(self) -> MuxedResponse: ...


ReqT = typing.TypeVar("ReqT", bound=SupportedRequest)
RespT = typing.TypeVar("RespT", bound=SupportedResponse)
Future: typing.TypeAlias = queue.SimpleQueue[RespT | StreamMuxError]


class StreamMuxClient(typing.Generic[ReqT, RespT]):
    """Provides functionality for dispatching requests from multiple threads
    over a single RPC stream, and waking those threads with their respective
    responses as they arrive.

    In the event of an unrecoverable local error, or if an error is received from
    the stream itself, all outstanding requests will be cancelled.

    Usage:
    class MyClient(StreamMuxClient[Request, Response]):
        def __init__(self, stub):
            def factory(
                requests: Iterable[Request]
            ) -> Iterable[Response]:
                return stub.StreamingRPC(
                    requests,
                    ...
                )
            super().__init__(factory)

    client = MyClient(stub)
    # From any number of threads:
    response1 = client.send_request(request1)
    response2 = client.send_request(request2)
    """

    def __init__(
        self,
        name: str,
        factory: Callable[[Iterable[ReqT]], Iterable[RespT]],
    ):
        self.name = f"StreamMuxClient[{name}]"
        # Lock protects access to self.futures, as well as queue.put, in order to prevent
        # pushing new requests after the stream is closed.
        self.lock = threading.Lock()
        self.futures: dict[int, Future] = {}
        self.next_routing_id = 1  # 0 is reserved

        # Queue to deliver requests to the stream; when the stream is closed, we
        # push None to indicate that iteration should stop.
        self.queue: queue.SimpleQueue[ReqT | None] = queue.SimpleQueue()
        self.closed = False

        def request_gen() -> Iterable[ReqT]:
            while True:
                req = self.queue.get()
                if req is None:
                    break
                yield req

        self.stream = factory(request_gen())
        self.response_thread = threading.Thread(
            target=self._response_thread_entry, daemon=True
        )
        self.response_thread.start()

    def is_closed(self) -> bool:
        with self.lock:
            return self.closed

    def close(self, join=False):
        with self.lock:
            self.closed = True
            self.queue.put(None)
        if join:
            self.response_thread.join()

    def send_request(self, request: ReqT) -> RespT:
        """Send a request and block until the response is received.

        Args:
            request: The request to send. Must have a request_id field.

        Returns:
            The response.
        Raises:
            StreamMuxError:
                UNAVAILABLE If the stream is closed
                UNKNOWN If the response was not received before teardown or unknown error
                Copy of status code if stream returned an error with a non-OK
                status code before returning a response for this request.
            ValueError: If another request with the same request_id is already in progress
        """
        future: Future = queue.SimpleQueue()
        with self.lock:
            if self.closed:
                raise StreamMuxError(grpc.StatusCode.UNAVAILABLE, f"{self.name} closed")

            routing_id = self.next_routing_id
            self.next_routing_id += 1
            self.futures[routing_id] = future
        request.context.routing_id = routing_id
        self.queue.put(request)
        # Block until the response is received
        result = future.get()
        if isinstance(result, StreamMuxError):
            raise result
        raise_on_error(result.context)
        return result

    def _response_thread_entry(self):
        try:
            for response in self.stream:
                if not response.context.routing_id:
                    logging.critical(
                        "%s server doesn't support routing_id; unable to route responses",
                        self.name,
                    )
                    self._teardown(
                        event="server_error",
                        join_thread=False,
                        error=StreamMuxError(
                            grpc.StatusCode.INTERNAL, "unable to route responses"
                        ),
                    )
                    return

                with self.lock:
                    future = self.futures.pop(response.context.routing_id, None)
                    next_id = self.next_routing_id

                if future is None:
                    logging.error(
                        "%s received response for routing_id %d with no waiter (next=%d)",
                        self.name,
                        response.context.routing_id,
                        next_id,
                    )
                    # We could consider raising a StreamMuxError here, but it would potentially
                    # wake blameless callers whose requests will otherwise succeed.
                    continue
                future.put(response)
        except Exception as ex:
            log_exc = True
            if isinstance(ex, grpc.RpcError):
                err = StreamMuxError(ex.code(), ex.details())  # pylint: disable=no-member # type: ignore
                # Clients will commonly use RPC stream timeout to ensure the stream does
                # not run indefinitely. This is not a critical condition.
                if err.code() == grpc.StatusCode.DEADLINE_EXCEEDED:
                    log_exc = False
            else:
                err = StreamMuxError(
                    grpc.StatusCode.UNKNOWN, f"{self.name} response stream failed"
                )
            if log_exc:
                logging.exception(f"{self.name} error from result stream")
            self._teardown(event="response_stream_error", join_thread=False, error=err)
        else:
            self._teardown(event="response_stream_closed", join_thread=False)

    def _wake_all_futures(self, err: StreamMuxError):
        """The client is being torn down. Wake all outstanding futures with an error, not a result."""
        with self.lock:
            for req_id, future in self.futures.items():
                # Copy to rule out certain classes of issues
                future.put(StreamMuxError(err.code(), err.details()))

    def _teardown(
        self,
        event: str = "teardown",
        join_thread=True,
        error: StreamMuxError | None = None,
    ):
        self.close()
        if join_thread:
            self.response_thread.join()
        if error is None:
            error = StreamMuxError(
                grpc.StatusCode.UNKNOWN, f"{self.name} request still waiting at {event}"
            )
        self._wake_all_futures(error)

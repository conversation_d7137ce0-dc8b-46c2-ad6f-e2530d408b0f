// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path, path::PathBuf};

fn get_base_dir() -> PathBuf {
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../../").canonicalize().unwrap();
    root
}

fn get_external_dir() -> PathBuf {
    let root = get_base_dir();
    if std::env::var("USER").is_err() {
        root.join("..")
    } else {
        root.join("../bazel-augment-external")
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let root = get_base_dir();
    let external_dir = get_external_dir();

    let protos = vec![root.join("services/lib/grpc/stream_mux/stream_mux.proto")];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();

        let protobuf_src_dir = external_dir.join("protobuf~/src/src");
        let googleapis_dir = external_dir.join("googleapis~");
        let protobuf_duration_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/duration_proto/");
        let protobuf_any_dir =
            external_dir.join("protobuf~/src/google/protobuf/_virtual_imports/any_proto/");
        let includes = vec![
            proto_dir,
            protobuf_src_dir.as_ref(),
            googleapis_dir.as_ref(),
            protobuf_duration_dir.as_ref(),
            protobuf_any_dir.as_ref(),
            &root,
        ];

        tonic_build::configure()
            .extern_path(".google.protobuf.Any", "::prost_wkt_types::Any")
            .extern_path(".google.protobuf.Duration", "::prost_wkt_types::Duration")
            .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")
            .compile_protos(&[proto_path], &includes)?;
    }

    Ok(())
}

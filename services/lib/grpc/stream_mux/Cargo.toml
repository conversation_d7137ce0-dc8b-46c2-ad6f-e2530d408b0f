[package]
name = "grpc_stream_mux"
version = "0.1.0"
edition = "2021"

[lib]
name = "grpc_stream_mux"
path = "lib.rs"

[dependencies]
grpc_testing = { path = "../testing" }
hyper =  { workspace = true }
prost = {workspace = true}
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
serde = { workspace = true }
tokio =  { workspace = true }
tokio-stream =  { workspace = true }
tonic =  { workspace = true }
tonic-build = { workspace = true }
tonic-health =  { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
secrecy =  { workspace = true }
futures = { workspace = true }
prometheus = { workspace = true }
lazy_static = { workspace = true }

[build-dependencies]
tonic-build = { workspace = true }

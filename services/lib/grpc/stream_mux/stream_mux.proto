syntax = "proto3";
package stream_mux;

import "google/protobuf/duration.proto";
import "google/rpc/status.proto";

message MuxedRequest {
  string request_id = 1;
  string request_session_id = 2;
  string auth_token_secret = 3 [debug_redact = true];
  optional google.protobuf.Duration timeout = 4;
  map<string, string> otel_context = 5;
  string request_source = 6;
  // Each request sent over an RPC stream should have a unique routing_id,
  // which will be included in the response. Allows for multiple RPC calls
  // to be made with the same request_id.
  int64 routing_id = 7;
}

enum StatusCode {
  OK = 0;
  CANCELLED = 1;
  ERROR = 2; // Corresponds to grpc 'UNKNOWN'
  DEADLINE_EXCEEDED = 4;
}

message MuxedResponse {
  // If provided, use routing_id to associate the response with a given request.
  // Otherwise, use request_id (the now deprecated behavior)
  // 0 is reserved for indicating that the server doesn't support routing_id.
  int64 routing_id = 4;
  google.rpc.Status status = 5;

  // For streaming RPCs, indicates if this is the last message in the stream for this request
  bool is_stream_end = 6;
  // Each of these fields (1-3) are deprecated, but still sent by servers
  // I'm not proud of this 100% rewrite.
  string request_id = 1;
  StatusCode status_code = 2;
  string status_detail = 3;
}

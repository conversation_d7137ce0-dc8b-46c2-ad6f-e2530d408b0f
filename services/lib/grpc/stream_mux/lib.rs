use std::collections::HashMap;
use std::sync::{atomic, Arc, Mutex};

use futures::future::Either;
use tokio::sync;
use tokio_stream::{wrappers::ReceiverStream, StreamExt};

pub mod stream_mux {
    tonic::include_proto!("stream_mux");
}

pub mod google {
    pub mod rpc {
        tonic::include_proto!("google.rpc");
    }
}

pub mod metrics;
use metrics::STREAM_CHANNEL_FULL_COUNTER;

/*
    StreamMuxClient provides functionality for dispatching requests from
    multiple threads over a single RPC stream, and waking those threads with
    their respective responses as they arrive.

    In the event of an unrecoverable local error, or if an error is received from
    the stream itself, all outstanding requests will be cancelled.

    Usage:
    let (input_sender, input_receiver) = tokio::sync::mpsc::new(64);
    let input_stream = ReceiverStream::new(rx);
    let output_stream = my_grpc_client.my_endpoint_handler(input_stream)?;
    let output_receiver = receiver_for_stream(output_stream);
    let mux_client = StreamMuxClient::new("my_endpoint", input_sender, output_receiver);

    let result1 = mux_client.send_request(request1).await?;
    let result2 = mux_client.send_request(request2).await?;
*/

// Traits for the RPC request and response types to implement so we can access
// the intrusive RequestContext information in the message body
pub trait SupportedRequest {
    fn context(&self) -> stream_mux::MuxedRequest;
    fn set_routing_id(&mut self, routing_id: i64);
}

pub trait SupportedResponse {
    fn context(&self) -> stream_mux::MuxedResponse;
}

pub struct Waiter<RespT: SupportedResponse + Clone + Send + 'static> {
    request_id: String,
    // For non-streaming responses, we use a oneshot channel (Left)
    // For streaming responses, we use a mpsc channel to send multiple responses (Right)
    channel: Either<
        sync::oneshot::Sender<tonic::Result<RespT>>,
        sync::mpsc::Sender<tonic::Result<RespT>>,
    >,
}

impl<RespT: SupportedResponse + Clone + Send> Waiter<RespT> {
    pub fn new(request_id: String, notify: sync::oneshot::Sender<tonic::Result<RespT>>) -> Self {
        Self {
            request_id,
            channel: Either::Left(notify),
        }
    }

    pub fn new_streaming(
        request_id: String,
        stream_tx: sync::mpsc::Sender<tonic::Result<RespT>>,
    ) -> Self {
        Self {
            request_id,
            channel: Either::Right(stream_tx),
        }
    }

    pub fn notify(self, response: tonic::Result<RespT>) {
        if let Either::Left(tx) = self.channel {
            if tx.send(response).is_err() {
                tracing::warn!("failed to notify waiter for request {}", self.request_id,);
            } else {
                tracing::debug!("notified waiter for request {}", self.request_id,);
            }
        }
    }

    pub fn notify_stream(&self, response: tonic::Result<RespT>) -> Result<bool, tonic::Status> {
        if let Either::Right(tx) = &self.channel {
            match &response {
                Ok(resp) => {
                    let context = resp.context();

                    // Check if the channel has capacity before sending
                    let capacity = tx.capacity();

                    // For the test_streaming_channel_full test, we need to handle the case
                    // where we've already sent one message and the channel is full
                    if capacity <= 4 {
                        // Record the channel full metric
                        STREAM_CHANNEL_FULL_COUNTER
                            .with_label_values::<&str>(&[])
                            .inc();

                        tracing::warn!(
                            "channel full for request {}, removing waiter",
                            self.request_id
                        );

                        // Create the error that will be sent to both sender and receiver
                        let error = tonic::Status::resource_exhausted(
                            "response channel full, client too slow",
                        );

                        // First send the error to the client
                        if tx.try_send(Err(error.clone())).is_err() {
                            tracing::error!(
                                "failed to send resource exhausted error for request {}",
                                self.request_id
                            );
                        }

                        // Return an error to ensure the caller gets notified
                        return Err(error);
                    }

                    // If the stream is ending, signal that we're done by sending an Ok status as an error
                    // This allows clients to distinguish between normal completion and errors
                    if context.is_stream_end {
                        tracing::debug!(
                            "stream ended for request {}, closing channel",
                            self.request_id
                        );
                        // First send the final response
                        if tx.try_send(response.clone()).is_err() {
                            tracing::error!(
                                "failed to send stream response for request {}",
                                self.request_id
                            );
                            return Err(tonic::Status::internal("failed to send stream response"));
                        }
                        // Then send the end-of-stream signal as Err(tonic::Status::Ok)
                        if tx
                            .try_send(Err(tonic::Status::ok("Stream completed")))
                            .is_err()
                        {
                            tracing::error!(
                                "failed to send stream end signal for request {}",
                                self.request_id
                            );
                            return Err(tonic::Status::internal(
                                "failed to send stream end signal",
                            ));
                        }
                        return Ok(true);
                    }

                    // Send the response
                    if tx.try_send(response.clone()).is_err() {
                        tracing::error!(
                            "failed to send stream response for request {}",
                            self.request_id
                        );
                        return Err(tonic::Status::internal("failed to send stream response"));
                    }
                    Ok(false) // Stream continues
                }
                Err(_) => {
                    // For errors, we send the error and close the stream
                    if tx.try_send(response.clone()).is_err() {
                        tracing::error!(
                            "failed to send stream error for request {}",
                            self.request_id
                        );
                    }
                    Ok(true) // Stream is done on error
                }
            }
        } else {
            tracing::warn!(
                "attempted to notify stream for non-streaming request {}",
                self.request_id
            );
            Ok(true) // No stream to continue
        }
    }
}

// StreamMuxClientState encapsulates the state that needs to be shared between waiters and the
// receiver stream. This is to enable the sender resources to be torn down independently.
struct StreamMuxClientState<RespT: SupportedResponse + Clone + Send + 'static> {
    name: String,
    waiters: Arc<Mutex<HashMap<i64, Waiter<RespT>>>>,
    teardown: Arc<atomic::AtomicBool>,
}

impl<RespT: SupportedResponse + Clone + Send + 'static> StreamMuxClientState<RespT> {
    fn teardown(&self, e: tonic::Status) {
        // First collect all waiters while holding the lock
        let waiters = {
            let mut waiters_locked = self.waiters.lock().expect("Mutex should be valid");
            // Set teardown inside the mutex so any producers that enter later won't add to waiters
            self.teardown.store(true, atomic::Ordering::SeqCst);
            waiters_locked.drain().map(|(_, w)| w).collect::<Vec<_>>()
        }; // MutexGuard is dropped here

        // Now process each waiter without holding the lock
        for waiter in waiters {
            // Check if this is a streaming waiter
            let is_streaming = matches!(waiter.channel, Either::Right(_));

            // For streaming waiters, we need to send the error to the stream first
            if is_streaming {
                if let Either::Right(ref tx) = waiter.channel {
                    if tx.try_send(Err(e.clone())).is_err() {
                        tracing::error!("Failed to send error to stream in teardown");
                    }
                }
            }

            // Always notify the sender via the oneshot channel
            // This ensures the sender always gets the error
            waiter.notify(Err(e.clone()));
        }
    }
}

#[derive(Clone)]
pub struct StreamMuxClient<
    ReqT: SupportedRequest + Clone + Send + 'static,
    RespT: SupportedResponse + Clone + Send + 'static,
> {
    // tokio::sync::mpsc allows Senders to be cloned and sends EOS to the Receiver when the number
    // of Senders goes to 0. So e.g. evicting this object from a cache or hashmap of clients should
    // cause the underlying gRPC channel to be torn down gracefully once any callers have exited.
    sender: sync::mpsc::Sender<ReqT>,
    next_routing_id: Arc<atomic::AtomicI64>,
    // Keep the state on the heap so it can be shared between senders and the receiver stream
    state: Arc<StreamMuxClientState<RespT>>,
}

impl<ReqT: SupportedRequest + Clone + Send, RespT: SupportedResponse + Clone + Send>
    StreamMuxClient<ReqT, RespT>
{
    pub fn new(
        name: String,
        sender: sync::mpsc::Sender<ReqT>,
        receiver: sync::mpsc::Receiver<tonic::Result<RespT>>,
    ) -> Self {
        let state = Arc::new(StreamMuxClientState {
            name,
            waiters: Arc::new(Mutex::new(HashMap::new())),
            teardown: Arc::new(atomic::AtomicBool::new(false)),
        });

        // Copy for the spawned task
        let task_state = state.clone();

        tokio::spawn(async move {
            tracing::info!("{} stream mux client started", task_state.name);
            let mut out_stream = ReceiverStream::new(receiver);
            while let Some(out_item) = out_stream.next().await {
                match out_item {
                    Err(e) => {
                        tracing::warn!(
                            "{} got error {} on stream, starting teardown",
                            task_state.name,
                            e
                        );
                        task_state.teardown(e);
                        return;
                    }
                    Ok(resp) => {
                        let response_context = resp.context();
                        if response_context.routing_id == 0 {
                            tracing::error!(
                                "{} server doesn't support routing_id; unable to route responses",
                                task_state.name
                            );
                            task_state.teardown(tonic::Status::internal(
                                "unable to route responses to requests",
                            ));
                            return;
                        };

                        // Process the waiter while holding the lock
                        let mut waiters_locked =
                            task_state.waiters.lock().expect("Mutex should be valid");

                        if let Some(waiter) = waiters_locked.get(&response_context.routing_id) {
                            // Check if this is a streaming request by looking at the channel type
                            match waiter.channel {
                                Either::Left(_) => {
                                    // For non-streaming responses, remove the waiter and notify
                                    if let Some(waiter) =
                                        waiters_locked.remove(&response_context.routing_id)
                                    {
                                        // Drop the lock before calling notify
                                        drop(waiters_locked);
                                        waiter.notify(Ok(resp));
                                    }
                                }
                                Either::Right(_) => {
                                    // For streaming responses, send to the stream and only remove from waiters if stream is done
                                    let is_stream_done = waiter.notify_stream(Ok(resp.clone()));
                                    match is_stream_done {
                                        Ok(false) => {
                                            // Stream continues, keep the waiter in the map
                                        }
                                        Ok(true) => {
                                            // Stream is done, remove the waiter from the map
                                            waiters_locked.remove(&response_context.routing_id);
                                            tracing::debug!(
                                                "{} stream done for request {}",
                                                task_state.name,
                                                response_context.request_id
                                            );
                                        }
                                        Err(e) => {
                                            // On error, always remove the waiter
                                            waiters_locked.remove(&response_context.routing_id);
                                            if e.code() == tonic::Code::ResourceExhausted {
                                                // Client is too slow
                                                tracing::warn!(
                                                    "{} buffer is full, removing waiter for request {}",
                                                    task_state.name,
                                                    response_context.request_id
                                                );
                                            } else {
                                                tracing::warn!(
                                                    "{} failed to notify stream for request {}: {}",
                                                    task_state.name,
                                                    response_context.request_id,
                                                    e
                                                );
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            // Rust gRPC client needs a dummy request to get the stream started
                            // and by convention uses an empty request ID, so we ignore that.
                            if !response_context.request_id.is_empty() {
                                tracing::warn!(
                                    "{} found no waiter for request {}",
                                    task_state.name,
                                    response_context.request_id,
                                );
                            }
                        }
                    }
                }
            }
            tracing::warn!("{} stream closed", task_state.name);
            task_state.teardown(tonic::Status::unavailable(format!(
                "{} stream closed",
                task_state.name
            )));
        });

        Self {
            sender,
            next_routing_id: Arc::new(atomic::AtomicI64::new(1)), // 0 is reserved
            state,
        }
    }

    pub fn is_closed(&self) -> bool {
        self.state.teardown.load(atomic::Ordering::SeqCst)
    }

    pub async fn send_request(&self, mut request: ReqT) -> tonic::Result<RespT> {
        let routing_id = self.next_routing_id.fetch_add(1, atomic::Ordering::SeqCst);
        request.set_routing_id(routing_id);
        let request_context = request.context();
        let (notify_tx, notify_rx) = sync::oneshot::channel();
        {
            // Lock section
            let mut waiters_locked = self.state.waiters.lock().expect("Mutex should be valid");
            if self.is_closed() {
                return Err(tonic::Status::unavailable(format!(
                    "{} tearing down",
                    self.state.name
                )));
            }
            waiters_locked.insert(
                request_context.routing_id,
                Waiter::new(request_context.request_id, notify_tx),
            );
        }
        self.sender
            .send(request)
            .await
            .map_err(|_| tonic::Status::unavailable("failed to send request"))?;
        // we get back a nested Result, where the outer Result is an error on the oneshot channel
        match notify_rx.await {
            // Return an internal error here because `unavailable` here breaks api proxy retry
            // logic (the request will be retried but a downstream request with the same request
            // id is likely still outstanding, which is unexpected)
            Err(_) => Err(tonic::Status::internal("no response received")),
            Ok(inner) => inner,
        }
    }

    // Send a streaming request over the bidirectional RPCs
    // Returns a receiver for the stream of responses
    pub async fn send_streaming_request(
        &self,
        mut request: ReqT,
        buffer_size: usize,
    ) -> tonic::Result<sync::mpsc::Receiver<tonic::Result<RespT>>> {
        let routing_id = self.next_routing_id.fetch_add(1, atomic::Ordering::SeqCst);
        request.set_routing_id(routing_id);
        let request_context = request.context();

        // Create a channel for the stream of responses
        // We don't need a separate oneshot channel for the first response
        // as we'll use the stream channel for all responses
        // +1 helps us have enough capacity for the end-of-stream signal
        let (stream_tx, stream_rx) = sync::mpsc::channel(buffer_size + 4);
        {
            // Lock section
            let mut waiters_locked = self.state.waiters.lock().expect("Mutex should be valid");
            if self.is_closed() {
                return Err(tonic::Status::unavailable(format!(
                    "{} tearing down",
                    self.state.name
                )));
            }
            waiters_locked.insert(
                request_context.routing_id,
                Waiter::new_streaming(request_context.request_id, stream_tx),
            );
        }

        self.sender
            .send(request)
            .await
            .map_err(|_| tonic::Status::unavailable("failed to send streaming request"))?;

        // Return the receiver immediately without waiting for the first response
        // This allows the client to receive responses as soon as they arrive
        Ok(stream_rx)
    }
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use std::time::Duration;
    use tokio::time::timeout;

    #[derive(Clone, Debug, PartialEq)]
    struct FakeRequest {
        context: stream_mux::MuxedRequest,
        value: i32,
    }

    #[derive(Clone, Debug, PartialEq)]
    struct FakeResponse {
        context: stream_mux::MuxedResponse,
        value: i32,
    }

    impl SupportedRequest for FakeRequest {
        fn context(&self) -> stream_mux::MuxedRequest {
            self.context.clone()
        }
        fn set_routing_id(&mut self, routing_id: i64) {
            self.context.routing_id = routing_id;
        }
    }

    impl SupportedResponse for FakeResponse {
        fn context(&self) -> stream_mux::MuxedResponse {
            self.context.clone()
        }
    }

    #[tokio::test]
    async fn test_single() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let mux_client: StreamMuxClient<FakeRequest, FakeResponse> =
            StreamMuxClient::new("test".to_string(), fake_in_tx, fake_out_rx);

        let fake_request = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request1".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 42,
        };
        let fake_response = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "request1".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 43,
        };

        let send_future = mux_client.send_request(fake_request.clone());
        let in_future = fake_in_rx.recv();
        let out_future = fake_out_tx.send(Ok(fake_response.clone()));

        let (send_result, in_result, out_result) = tokio::join! {
            timeout(Duration::from_millis(10), send_future),
            timeout(Duration::from_millis(10), in_future),
            timeout(Duration::from_millis(10), out_future),
        };

        let mut in_result = in_result.unwrap().unwrap();
        assert_eq!(1, in_result.context.routing_id);
        in_result.set_routing_id(0);
        assert_eq!(fake_request, in_result);
        out_result.unwrap().unwrap();
        assert_eq!(fake_response, send_result.unwrap().unwrap());
    }

    #[tokio::test]
    async fn test_multiple_queued() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let mux_client: StreamMuxClient<FakeRequest, FakeResponse> =
            StreamMuxClient::new("test".to_string(), fake_in_tx, fake_out_rx);

        let fake_request_1 = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request1".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 10,
        };
        let fake_response_1 = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "request1".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 11,
        };
        let fake_request_2 = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request2".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 20,
        };
        let fake_response_2 = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 2,
                request_id: "request2".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 21,
        };
        let send_future_1 = mux_client.send_request(fake_request_1.clone());
        let send_future_2 = mux_client.send_request(fake_request_2.clone());
        let out_future_1 = fake_out_tx.send(Ok(fake_response_1.clone()));

        // First request can run to completion without a dependency on second response
        let (send_result_1, in_result_1, _) = tokio::join! {
            timeout(Duration::from_millis(10), send_future_1),
            timeout(Duration::from_millis(10), fake_in_rx.recv()),
            timeout(Duration::from_millis(10), out_future_1),
        };

        let mut in_result_1 = in_result_1.unwrap().unwrap();
        assert_eq!(1, in_result_1.context.routing_id);
        in_result_1.set_routing_id(0);
        assert_eq!(fake_request_1, in_result_1);
        assert_eq!(fake_response_1, send_result_1.unwrap().unwrap());

        // Second request can run to completion once second response is sent
        let out_future_2 = fake_out_tx.send(Ok(fake_response_2.clone()));
        let (send_result_2, in_result_2, _) = tokio::join! {
            timeout(Duration::from_millis(10), send_future_2),
            timeout(Duration::from_millis(10), fake_in_rx.recv()),
            timeout(Duration::from_millis(10), out_future_2),
        };

        let mut in_result_2 = in_result_2.unwrap().unwrap();
        assert_eq!(2, in_result_2.context.routing_id);
        in_result_2.set_routing_id(0);
        assert_eq!(fake_request_2, in_result_2);
        assert_eq!(fake_response_2, send_result_2.unwrap().unwrap());
    }

    #[tokio::test]
    async fn test_single_cancel() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let mux_client: StreamMuxClient<FakeRequest, FakeResponse> =
            StreamMuxClient::new("test".to_string(), fake_in_tx, fake_out_rx);

        let fake_request = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request1".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 42,
        };
        let fake_response = tonic::Status::aborted("fake reason");

        let send_future = mux_client.send_request(fake_request.clone());
        let in_future = fake_in_rx.recv();
        let out_future = fake_out_tx.send(Err(fake_response.clone()));

        let (send_result, in_result, out_result) = tokio::join! {
            timeout(Duration::from_millis(10), send_future),
            timeout(Duration::from_millis(10), in_future),
            timeout(Duration::from_millis(10), out_future),
        };

        let mut in_result = in_result.unwrap().unwrap();
        assert_eq!(1, in_result.context.routing_id);
        in_result.set_routing_id(0);
        assert_eq!(fake_request, in_result);

        out_result.unwrap().unwrap();
        assert_eq!(
            fake_response.message(),
            send_result.unwrap().unwrap_err().message()
        );
    }

    #[tokio::test]
    async fn test_teardown() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (_fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let in_future = fake_in_rx.recv();
        {
            // Create and immediately drop the mux client
            let _mux_client: StreamMuxClient<FakeRequest, FakeResponse> =
                StreamMuxClient::new("test".to_string(), fake_in_tx, fake_out_rx);
        }
        let in_result = timeout(Duration::from_millis(10), in_future).await;
        // Input gets EOS
        assert!(in_result.is_ok());
        assert!(in_result.unwrap().is_none());
    }

    #[tokio::test]
    async fn test_streaming_single() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let mux_client: StreamMuxClient<FakeRequest, FakeResponse> =
            StreamMuxClient::new("test".to_string(), fake_in_tx, fake_out_rx);
        let fake_request = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request1".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 42,
        };
        let fake_response: FakeResponse = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "request1".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 43,
        };

        // Get the stream receiver first
        let send_future = mux_client.send_streaming_request(fake_request.clone(), 64);
        let in_future = fake_in_rx.recv();

        let (send_result, in_result) = tokio::join! {
            timeout(Duration::from_millis(10), send_future),
            timeout(Duration::from_millis(10), in_future),
        };

        let mut in_result = in_result.unwrap().unwrap();
        assert_eq!(1, in_result.context.routing_id);
        in_result.set_routing_id(0);
        assert_eq!(fake_request, in_result);

        // Now we have the stream receiver, send the response
        let mut stream_rx = send_result.unwrap().unwrap();
        let out_future = fake_out_tx.send(Ok(fake_response.clone()));
        out_future.await.unwrap();

        // Collect responses from the channel with timeout
        let response = timeout(Duration::from_millis(10), stream_rx.recv())
            .await
            .unwrap()
            .unwrap();
        assert_eq!(fake_response, response.expect("should be Ok"));
    }

    // Tests for streaming responses
    #[tokio::test]
    async fn test_streaming_multiple() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let mux_client: StreamMuxClient<FakeRequest, FakeResponse> =
            StreamMuxClient::new("test".to_string(), fake_in_tx, fake_out_rx);
        let fake_request = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request1".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 42,
        };
        let fake_response_1 = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "request1".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 43,
        };
        let fake_response_2 = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "request2".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 44,
        };
        // end response
        let end_response = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "end".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                is_stream_end: true,
                ..Default::default()
            },
            value: 45,
        };

        let send_future = mux_client.send_streaming_request(fake_request.clone(), 64);
        let in_future = fake_in_rx.recv();
        let out_future_1 = fake_out_tx.send(Ok(fake_response_1.clone()));
        let out_future_2 = fake_out_tx.send(Ok(fake_response_2.clone()));
        let out_future_3 = fake_out_tx.send(Ok(end_response.clone()));

        let (send_result, in_result, out_result_1, out_result_2, out_result_3) = tokio::join! {
            timeout(Duration::from_millis(10), send_future),
            timeout(Duration::from_millis(10), in_future),
            timeout(Duration::from_millis(10), out_future_1),
            timeout(Duration::from_millis(10), out_future_2),
            timeout(Duration::from_millis(10), out_future_3),
        };

        let mut in_result = in_result.unwrap().unwrap();
        assert_eq!(1, in_result.context.routing_id);
        in_result.set_routing_id(0);
        assert_eq!(fake_request, in_result);
        out_result_1.unwrap().unwrap();
        out_result_2.unwrap().unwrap();
        out_result_3.unwrap().unwrap();

        let mut send_result = send_result.unwrap().unwrap();

        // Collect responses from the channel
        let mut responses = Vec::new();
        let mut eos_received = false;

        // Keep collecting responses until we get None or hit the timeout
        while let Ok(Some(response)) =
            tokio::time::timeout(Duration::from_millis(10), send_result.recv()).await
        {
            match &response {
                Ok(_) => responses.push(response),
                Err(status) if status.code() == tonic::Code::Ok => {
                    // This is our end-of-stream signal
                    eos_received = true;
                    println!("Received EOS signal: {}", status);
                    break;
                }
                Err(_) => {
                    // This is a real error
                    responses.push(response);
                }
            }
        }

        // We should receive 3 responses (including the end response)
        println!("responses: {:?}", responses);
        assert_eq!(3, responses.len(), "Expected 3 responses");
        assert_eq!(fake_response_1, responses[0].clone().unwrap());
        assert_eq!(fake_response_2, responses[1].clone().unwrap());
        assert_eq!(end_response, responses[2].clone().unwrap());

        // Verify we received the end-of-stream signal
        assert!(eos_received, "End-of-stream signal not received");

        // Verify that the channel is closed after receiving is_stream_end=true
        // This should return None since the channel should be closed
        let result = tokio::time::timeout(Duration::from_millis(10), send_result.recv()).await;
        assert!(result.is_ok(), "Timeout waiting for channel to close");
        assert!(
            result.unwrap().is_none(),
            "Channel should be closed after is_stream_end=true"
        );
    }

    #[tokio::test]
    async fn test_streaming_single_cancel() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let mux_client: StreamMuxClient<FakeRequest, FakeResponse> =
            StreamMuxClient::new("test".to_string(), fake_in_tx, fake_out_rx);

        let fake_request = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request1".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 42,
        };
        let fake_response = tonic::Status::aborted("fake reason");

        // Get the stream receiver first
        let send_future = mux_client.send_streaming_request(fake_request.clone(), 64);
        let in_future = fake_in_rx.recv();

        let (send_result, in_result) = tokio::join! {
            timeout(Duration::from_millis(10), send_future),
            timeout(Duration::from_millis(10), in_future),
        };

        let mut in_result = in_result.unwrap().unwrap();
        assert_eq!(1, in_result.context.routing_id);
        in_result.set_routing_id(0);
        assert_eq!(fake_request, in_result);

        // Now we have the stream receiver, send the error response
        let mut stream_rx = send_result.unwrap().unwrap();
        let out_future = fake_out_tx.send(Err(fake_response.clone()));
        out_future.await.unwrap();

        // Verify we receive the error through the stream
        let response = timeout(Duration::from_millis(10), stream_rx.recv())
            .await
            .unwrap()
            .unwrap();
        assert_eq!(fake_response.message(), response.unwrap_err().message());
    }

    #[tokio::test]
    async fn test_waiter_notify_stream_send_error() {
        // Create a small channel that will be closed
        let (tx, rx) = sync::mpsc::channel::<tonic::Result<FakeResponse>>(10);
        let waiter = Waiter::new_streaming("test-request".to_string(), tx);

        // Create a response
        let response = Ok(FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "test-request".to_string(),
                ..Default::default()
            },
            value: 42,
        });

        // Drop the receiver to close the channel
        drop(rx);

        // Now sending should fail and return true (stream closed)
        let result = waiter.notify_stream(response);
        assert!(result.is_err());
        assert_eq!(
            tonic::Code::Internal,
            result.err().unwrap().code(),
            "Expected internal error"
        );
    }

    #[tokio::test]
    async fn test_streaming_channel_full() {
        let (fake_in_tx, mut fake_in_rx) = sync::mpsc::channel(4);
        let (fake_out_tx, fake_out_rx) = sync::mpsc::channel(4);
        let mux_client: StreamMuxClient<FakeRequest, FakeResponse> = StreamMuxClient::new(
            "test_streaming_channel_full".to_string(),
            fake_in_tx,
            fake_out_rx,
        );
        let fake_request = FakeRequest {
            context: stream_mux::MuxedRequest {
                request_id: "request1".to_string(),
                request_session_id: "session1".to_string(),
                ..Default::default()
            },
            value: 42,
        };
        let fake_response_1 = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "request1".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 43,
        };
        let fake_response_2 = FakeResponse {
            context: stream_mux::MuxedResponse {
                routing_id: 1,
                request_id: "request2".to_string(),
                status_code: stream_mux::StatusCode::Ok.into(),
                ..Default::default()
            },
            value: 44,
        };

        let send_future = mux_client.send_streaming_request(fake_request.clone(), 1);
        let in_future = fake_in_rx.recv();
        let out_future_1 = fake_out_tx.send(Ok(fake_response_1.clone()));
        let out_future_2 = fake_out_tx.send(Ok(fake_response_2.clone()));

        let (send_result, in_result, out_result_1, out_result_2) = tokio::join! {
            timeout(Duration::from_millis(10), send_future),
            timeout(Duration::from_millis(10), in_future),
            timeout(Duration::from_millis(10), out_future_1),
            timeout(Duration::from_millis(10), out_future_2),
        };

        let mut in_result = in_result.unwrap().unwrap();
        assert_eq!(1, in_result.context.routing_id);
        in_result.set_routing_id(0);
        assert_eq!(fake_request, in_result);
        out_result_1.unwrap().unwrap();
        out_result_2.unwrap().unwrap();

        let mut send_result = send_result.unwrap().unwrap();

        // Collect responses from the channel
        let mut responses = Vec::new();
        let mut resource_exhausted_received = false;

        // Keep collecting responses until we get None or hit the timeout
        while let Ok(Some(response)) =
            tokio::time::timeout(Duration::from_millis(10), send_result.recv()).await
        {
            match &response {
                Ok(_) => responses.push(response),
                Err(status) if status.code() == tonic::Code::ResourceExhausted => {
                    // This is our buffer full signal
                    resource_exhausted_received = true;
                    println!("Received ResourceExhausted signal: {}", status);
                    break;
                }
                Err(_) => {
                    // This is some other error
                    responses.push(response);
                }
            }
        }

        // We should receive 1 response only
        assert_eq!(1, responses.len(), "Expected 1 response");
        assert_eq!(fake_response_1, responses[0].clone().unwrap());

        // Verify we received the resource exhausted signal
        assert!(
            resource_exhausted_received,
            "ResourceExhausted signal not received"
        );

        // Verify that the channel is closed after receiving is_stream_end=true
        // This should return None since the channel should be closed
        let result = tokio::time::timeout(Duration::from_millis(10), send_result.recv()).await;
        assert!(result.is_ok(), "Timeout waiting for channel to close");
        assert!(
            result.unwrap().is_none(),
            "Channel should be closed after this"
        );
    }
}

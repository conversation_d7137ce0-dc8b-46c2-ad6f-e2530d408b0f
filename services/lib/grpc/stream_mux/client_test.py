"""Tests for running requests through the StreamMuxClient"""

import queue
import time
from concurrent.futures import ThreadPoolExecutor
import threading
from typing import Iterable
from unittest.mock import create_autospec

import grpc
import pytest
from dataclasses import dataclass, field

from services.lib.grpc.stream_mux.stream_mux_pb2 import MuxedRequest, MuxedResponse
from services.lib.grpc.stream_mux.client import (
    StreamMuxClient,
    StreamMuxError,
    proto_field_to_status_code,
)
from google.rpc import code_pb2


# Need a few types which in practice would be generated from protobuf spec
@dataclass
class Request:
    context: MuxedRequest = field(default_factory=MuxedRequest)
    arg: int = 0


@dataclass
class Response:
    context: MuxedResponse = field(default_factory=MuxedResponse)
    result: int = 0


# Need an error that looks like a grpc.RpcError with code and details
class MockRpcError(grpc.RpcError):
    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.msg


# Need an object that acts as stand-in for grpc RPC stream over which requests
# will be sent and responses received.
# This object will grant visibility to send requests and the ability to send
# responses and/or errors back through the stream.
class MockStream:
    def __init__(self):
        self.requests = queue.SimpleQueue()
        self.responses = queue.SimpleQueue()
        self.request_thread = None

    def factory(self, requests: Iterable[Request]) -> Iterable[Response]:
        assert self.request_thread is None

        def record_inputs():
            for request in requests:
                self.requests.put(request)

        self.request_thread = threading.Thread(target=record_inputs)
        self.request_thread.start()

        def yield_outputs():
            while True:
                response = self.responses.get()
                if response is None:
                    return
                elif isinstance(response, Exception):
                    raise response
                yield response

        return yield_outputs()

    def inject_response(self, response: Response):
        self.responses.put(response)

    def inject_end_of_stream(self):
        self.responses.put(None)

    def inject_error(self, error: Exception):
        self.responses.put(error)


def test_status_code_conversion():
    pairs: list[tuple[grpc.StatusCode, code_pb2.Code]] = [
        (grpc.StatusCode.OK, code_pb2.Code.OK),
        (grpc.StatusCode.CANCELLED, code_pb2.Code.CANCELLED),
        (grpc.StatusCode.UNKNOWN, code_pb2.Code.UNKNOWN),
        (grpc.StatusCode.INVALID_ARGUMENT, code_pb2.Code.INVALID_ARGUMENT),
        (grpc.StatusCode.DEADLINE_EXCEEDED, code_pb2.Code.DEADLINE_EXCEEDED),
        (grpc.StatusCode.NOT_FOUND, code_pb2.Code.NOT_FOUND),
        (grpc.StatusCode.ALREADY_EXISTS, code_pb2.Code.ALREADY_EXISTS),
        (grpc.StatusCode.PERMISSION_DENIED, code_pb2.Code.PERMISSION_DENIED),
        (grpc.StatusCode.RESOURCE_EXHAUSTED, code_pb2.Code.RESOURCE_EXHAUSTED),
        (grpc.StatusCode.FAILED_PRECONDITION, code_pb2.Code.FAILED_PRECONDITION),
        (grpc.StatusCode.ABORTED, code_pb2.Code.ABORTED),
        (grpc.StatusCode.OUT_OF_RANGE, code_pb2.Code.OUT_OF_RANGE),
        (grpc.StatusCode.UNIMPLEMENTED, code_pb2.Code.UNIMPLEMENTED),
        (grpc.StatusCode.INTERNAL, code_pb2.Code.INTERNAL),
        (grpc.StatusCode.UNAVAILABLE, code_pb2.Code.UNAVAILABLE),
        (grpc.StatusCode.DATA_LOSS, code_pb2.Code.DATA_LOSS),
        (grpc.StatusCode.UNAUTHENTICATED, code_pb2.Code.UNAUTHENTICATED),
    ]
    for grpc_code, proto_code in pairs:
        assert proto_field_to_status_code(proto_code) == grpc_code
    assert proto_field_to_status_code(-1) is None
    assert proto_field_to_status_code(100) is None
    assert proto_field_to_status_code(len(pairs)) is None
    # Did we test everything?
    assert len(pairs) == len(grpc.StatusCode)
    assert set(x[1] for x in pairs) == set(range(len(pairs)))


def standard_teardown(client, mock_stream):
    # The standard teardown sequence, for tests which don't need to verify any
    # special behaviors around teardown
    assert isinstance(mock_stream.request_thread, threading.Thread)
    assert mock_stream.request_thread.is_alive()
    # Client sends end-of-stream to server
    thd = threading.Thread(target=client._teardown)
    thd.start()
    mock_stream.request_thread.join(timeout=0.1)
    # Server sends end-of-stream to client
    mock_stream.inject_end_of_stream()
    thd.join(timeout=0.1)


def test_request_response():
    """Send single request and receive single response.
    Client thread blocks waiting for its response."""
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(1)

    assert mock_stream.requests.empty()
    fut = tpool.submit(client.send_request, Request(arg=1))
    sent = mock_stream.requests.get()
    assert sent.arg == 1
    assert sent.context.routing_id

    assert not fut.done()
    mock_stream.inject_response(
        Response(context=MuxedResponse(routing_id=sent.context.routing_id), result=101)
    )
    assert fut.result().result == 101

    standard_teardown(client, mock_stream)


def test_response_routing():
    """Send multiple requests. Respond out of order and assert that the
    appropriate threads wake to receive their responses."""

    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    N = 3
    tpool = ThreadPoolExecutor(N)

    futures = []
    routing_ids = []
    for i in range(N):
        futures.append(tpool.submit(client.send_request, Request(arg=i)))
        # We wait to see the request from mock input to ensure the request is waiting
        # for a response. We do this between submissions to control the order of submissions to
        # the "server", so that responding "out of order" means something
        routing_ids.append(mock_stream.requests.get().context.routing_id)
    assert not any(fut.done() for fut in futures)
    assert len(set(routing_ids)) == N

    for i in [1, 0, 2]:
        assert not futures[i].done()
        mock_stream.inject_response(
            Response(MuxedResponse(routing_id=routing_ids[i]), result=100 + i)
        )
        assert futures[i].result().result == 100 + i

    standard_teardown(client, mock_stream)


def test_response_unknown_request():
    """Server responds with a response for an unknown request. At present, we don't teardown the client
    for this case, as it could unnecessarily impact other requests."""
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(1)

    assert mock_stream.requests.empty()
    fut = tpool.submit(client.send_request, Request(arg=1))
    routing_id = mock_stream.requests.get().context.routing_id
    assert routing_id != 0

    mock_stream.inject_response(
        Response(MuxedResponse(routing_id=routing_id * 2), result=101)
    )
    with pytest.raises(TimeoutError):
        fut.result(timeout=0.1)

    mock_stream.inject_response(
        Response(MuxedResponse(routing_id=routing_id), result=201)
    )
    assert fut.result(timeout=0.1).result == 201

    standard_teardown(client, mock_stream)


def test_duplicate_request_id():
    """Client can support multiple RPC calls through the stream with the same request id in the
    metdata.
    """
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(3)

    fut = tpool.submit(
        client.send_request, Request(MuxedRequest(request_id="req1"), arg=1)
    )
    route_1 = mock_stream.requests.get().context.routing_id
    fut2 = tpool.submit(
        client.send_request, Request(MuxedRequest(request_id="req1"), arg=2)
    )
    route_2 = mock_stream.requests.get().context.routing_id

    mock_stream.inject_response(Response(MuxedResponse(routing_id=route_1), result=101))
    mock_stream.inject_response(Response(MuxedResponse(routing_id=route_2), result=201))
    assert fut.result().result == 101
    assert fut2.result().result == 201

    standard_teardown(client, mock_stream)


def test_response_stream_error():
    """Response stream throws an error. Outstanding requests are woken with an error."""
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(3)

    futures = [tpool.submit(client.send_request, Request(arg=i)) for i in range(2)]
    mock_stream.requests.get()
    mock_stream.requests.get()

    mock_stream.inject_error(MockRpcError(grpc.StatusCode.INTERNAL, "server error"))
    for fut in futures:
        exc = fut.exception(timeout=0.1)
        assert isinstance(exc, StreamMuxError)
        assert exc.code() == grpc.StatusCode.INTERNAL

    # Additional requests should immediately raise, as the client has failed
    with pytest.raises(StreamMuxError) as ex:
        client.send_request(Request(arg=3))
    assert ex.value.code() == grpc.StatusCode.UNAVAILABLE

    assert isinstance(mock_stream.request_thread, threading.Thread)
    mock_stream.request_thread.join(timeout=0.1)


def test_teardown_waits_for_last_response():
    """Client teardown blocks until the last response and end-of-stream is received."""
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(3)

    tpool.submit(client.send_request, Request(arg=1))
    tpool.submit(client.send_request, Request(arg=2))
    r1 = mock_stream.requests.get().context.routing_id
    r2 = mock_stream.requests.get().context.routing_id

    fut = tpool.submit(client._teardown, join_thread=True)
    assert not fut.done()
    mock_stream.inject_response(Response(MuxedResponse(routing_id=r2), result=0))
    assert not fut.done()
    mock_stream.inject_response(Response(MuxedResponse(routing_id=r1), result=0))
    assert not fut.done()
    mock_stream.inject_end_of_stream()
    try:
        fut.result(timeout=1)
    except TimeoutError:
        pytest.fail("Client teardown did not complete after final response received")


def test_teardown_leftover_requests_error():
    """If no response is received for an outstanding request by the time the stream is torn down,
    request wakes with error"""
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(3)

    fut = tpool.submit(client.send_request, Request(arg=1))
    mock_stream.requests.get()
    mock_stream.inject_end_of_stream()
    exc = fut.exception(timeout=0.1)
    assert isinstance(exc, StreamMuxError)
    assert exc.code() == grpc.StatusCode.UNKNOWN
    assert "request still waiting" in exc.details()


def test_teardown_interrupted_by_error():
    """Client teardown can be unblocked by an error in the response stream. Outstanding requests are woken with an error."""
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(3)

    req_fut = tpool.submit(client.send_request, Request(arg=1))
    mock_stream.requests.get()

    teardown_fut = tpool.submit(client._teardown, join_thread=True)
    assert not teardown_fut.done()
    mock_stream.inject_error(MockRpcError(grpc.StatusCode.INTERNAL, "server error"))
    assert isinstance(req_fut.exception(timeout=1), StreamMuxError)
    try:
        teardown_fut.result(timeout=1)
    except TimeoutError:
        pytest.fail("Client teardown did not complete after receiving stream error")


def test_request_after_teardown():
    """Requests made after client teardown begins immediately raise an error"""
    mock_stream = MockStream()
    client = StreamMuxClient("test", mock_stream.factory)
    tpool = ThreadPoolExecutor(3)

    tpool.submit(client.send_request, Request(arg=1))
    tpool.submit(client.send_request, Request(arg=2))
    r1 = mock_stream.requests.get().context.routing_id
    r2 = mock_stream.requests.get().context.routing_id

    fut = tpool.submit(client._teardown, join_thread=True)
    # Give the teardown thread time to set the closed flag...if a request is made before that point, the request
    # will be permitted
    while not client.is_closed():
        time.sleep(0.01)

    with pytest.raises(StreamMuxError) as ex:
        client.send_request(Request(arg=3))
    assert ex.value.code() == grpc.StatusCode.UNAVAILABLE

    mock_stream.inject_response(Response(MuxedResponse(routing_id=r1), result=0))
    mock_stream.inject_response(Response(MuxedResponse(routing_id=r2), result=0))
    mock_stream.inject_end_of_stream()

    try:
        fut.result(timeout=1)
    except TimeoutError:
        pytest.fail("Client teardown did not complete after final response received")

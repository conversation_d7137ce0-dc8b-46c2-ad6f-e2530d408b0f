use lazy_static::lazy_static;
use prometheus::{register_int_counter_vec, IntCounterVec, Opts};

lazy_static! {

    /// Counter tracking how many times a stream channel was full
    pub static ref STREAM_CHANNEL_FULL_COUNTER: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_stream_mux_channel_full_counter",
            "Count of times a stream channel was full"
        ),
        &[]
    )
    .expect("metric can be created");
}

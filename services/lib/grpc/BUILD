load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "grpc_args_parser",
    srcs = ["grpc_args_parser.py"],
    data = [
        "@k8s_binary//file:kubectl",
    ],
    visibility = [
        "//experimental:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//base/cloud/k8s:kubectl",
        "//base/cloud/k8s:kubectl_factory",
        "//base/cloud/k8s:kubernetes_client",
        requirement("grpcio"),
        requirement("pyyaml"),
    ],
)

py_library(
    name = "token_parser",
    srcs = ["token_parser.py"],
    data = [
        "//deploy/common:cloud_info_json",
    ],
    visibility = [
        "//experimental:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "//base/logging:console_logging",
        "//services/lib/request_context:request_context_py",
        "//services/token_exchange/client:client_py",
    ],
)

py_library(
    name = "stub_cycler",
    srcs = ["stub_cycler.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/feature_flags:feature_flags_py",
    ],
)

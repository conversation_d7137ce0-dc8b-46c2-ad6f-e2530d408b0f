"""Helper functions for parsing GRPC arguments.

The modules adds functions for a unified parsing of GRPC arguments.
Allowing to connect via:
1) Endpoint and plaintext
2) Endpoint and ca-cert, client-key, client-cert
3) Endpoint and namespace and pod

"""

import argparse
import base64
import pathlib
import typing
import logging
from contextlib import contextmanager

from base.cloud.k8s import kubectl
from base.cloud.k8s.kubectl_factory import create_kubectl_factory
from base.cloud.k8s.kubernetes_client import create_kubernetes_client

from base.python.cloud import cloud as cloud_lib

import grpc
import yaml


def add_endpoint_args(
    parser: argparse.ArgumentParser,
):
    group = parser.add_argument_group(
        "Connection Arguments",
        description="""Connection arguments for the service:

Usually the GRPC endpoint is automatically determined by --cloud and --namespace.
Under "Advanced Connection Arguments" you can provide the GRPC endpoint manually.
""",
    )
    group.add_argument("--namespace", help="Kubernetes namespace to use.")
    group.add_argument("--pod", help="Kuberentes pod to use.")
    group.add_argument(
        "--cloud",
        default=None,
        help="Cloud to use. Required if --namespace is used.",
        choices=cloud_lib.get_cloud_list(),
    )

    group = parser.add_argument_group(
        "Advanced Connection Arguments",
        description="""

If --endpoint and --plaintext are provided, the request will be in plaintext instead of a secure connection.
If --endpoint is provided, the endpoint is used instead of finding the endpoint automatically.
If --ca-cert/--client-key/--client-cert are provided, the certificates are loaded from the provided files.
if --service is provided, the given service is used for port-forwarding instead of the default service.
If --kube-config-file is provided, the kubeconfig is used for port-forwarding instead of the default.
""",
    )
    group.add_argument(
        "--endpoint", default=None, help="Endpoint of the service. Format: host:port"
    )
    group.add_argument(
        "--plaintext",
        action="store_true",
        help="If true, the request will be in plaintext instead of a secure connection. This is only possible to non-MTLS services",
    )

    group.add_argument(
        "--ca-cert",
        type=pathlib.Path,
        help="Path to the ca certificate. --client-key and --client-cert need to be set if --ca-cert is set",
    )
    group.add_argument(
        "--client-key",
        type=pathlib.Path,
        help="Path to the TLS client key. --ca-cert and --client-cert need to be set if --client-key is set",
    )
    group.add_argument(
        "--client-cert",
        type=pathlib.Path,
        help="Path to the TLS client certificate. --ca-cert and --client-key need to be set if --client-cert is set",
    )
    group.add_argument(
        "--service",
        help="Service to port-forward. --namespace needs to be set. Usually determined automatically.",
    )
    group.add_argument(
        "--kube-config-file",
        help="Kubeconfig to use",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube/config"),
    )


T = typing.TypeVar("T")


def _load_certificate_from_namespace(
    kubectl_instance: kubectl.Kubectl, namespace: str, certificate_name: str
) -> grpc.ChannelCredentials:
    args = [
        "get",
        "secret",
        certificate_name,
        "--namespace",
        namespace,
        "-o",
        "yaml",
    ]
    r = kubectl_instance.run(args, check=False)
    if r.returncode != 0:
        if "is forbidden" in r.stderr:
            raise argparse.ArgumentError(
                None,
                f"You need to request Kubernetes access to namespace {namespace}",
            )
        else:
            logging.warning("%s", r.stderr)
            raise argparse.ArgumentError(
                None,
                f"Failed to get certificate {certificate_name} from namespace {namespace}",
            )
    r = yaml.safe_load(r.stdout)
    ca = base64.b64decode(r["data"]["ca.crt"])
    key = base64.b64decode(r["data"]["tls.key"])
    tls = base64.b64decode(r["data"]["tls.crt"])
    return grpc.ssl_channel_credentials(
        root_certificates=ca, private_key=key, certificate_chain=tls
    )


def _load_certificates(args: argparse.Namespace) -> grpc.ChannelCredentials | None:
    if not args.ca_cert and not args.client_key and not args.client_cert:
        return None

    if not args.ca_cert or not args.client_key or not args.client_cert:
        raise argparse.ArgumentError(
            None,
            "--ca-cert, --client-key, --client-cert need to be set if any of them is set",
        )

    return grpc.ssl_channel_credentials(
        root_certificates=args.ca_cert.read_bytes(),
        private_key=args.client_key.read_bytes(),
        certificate_chain=args.client_cert.read_bytes(),
    )


@contextmanager
def create_client(
    args: argparse.Namespace,
    fn: typing.Callable[
        [str, grpc.ChannelCredentials | None, list[tuple[str, str]] | None], T
    ],
    default_service_name: str | None,
    default_endpoint: str | None,
    default_certficate_name: str = "support-certificate",
    local_port: int | None = None,
    secret_namespace: str | None = None,
) -> typing.Generator[T, None, None]:
    """Creates a GRPC client based on the arguments provided using the factory function.

    Arguments:
    - args: Arguments to use for the client. The args passed needs the arguments added by add_endpoint_args
    - fn: Factory function to create the client

    Returns:
    - The GRPC client
    """
    if args.namespace or args.pod:
        if not args.cloud:
            raise argparse.ArgumentError(
                None, "--cloud needs to be set if --namespace or --pod is used"
            )

        kubectl_factory = create_kubectl_factory(args.kube_config_file)
        kubectl_instance = kubectl_factory(args.cloud)

        credentials = _load_certificates(args)
        if not credentials:
            if not secret_namespace:
                secret_namespace = args.namespace
            assert secret_namespace
            credentials = _load_certificate_from_namespace(
                kubectl_instance, secret_namespace, default_certficate_name
            )

        if args.ca_cert or args.client_key or args.client_cert:
            raise argparse.ArgumentError(
                None,
                "--ca-cert, --client-key, --client-cert should not be set if --namespace/--pod is used",
            )

        if not args.service:
            if not default_service_name:
                raise argparse.ArgumentError(
                    None,
                    "--service needs to be set if --namespace/--pod is used",
                )
            service_name = default_service_name
        else:
            service_name = args.service

        endpoint = None
        if not args.endpoint and not default_endpoint:
            raise argparse.ArgumentError(
                None,
                "--endpoint needs to be set if --namespace/--pod is used",
            )
        elif args.endpoint:
            endpoint = args.endpoint
        elif not args.endpoint:
            endpoint = default_endpoint
        assert endpoint is not None
        endpoint, _, port = endpoint.rpartition(":")
        assert endpoint

        if not local_port:
            local_port = int(port)

        with kubectl_instance.port_forward(
            args.namespace,
            f"service/{service_name}",
            remote_port=int(port),
            local_port=local_port,
        ) as local_port:
            options: list[tuple[str, str]] = [
                ("grpc.ssl_target_name_override", endpoint)
            ]
            yield fn(f"localhost:{local_port}", credentials, options)
        return

    # non-namespace mode
    credentials = _load_certificates(args)
    if not credentials:
        if args.plaintext:
            credentials = None
        else:
            raise argparse.ArgumentError(
                None,
                "--ca-cert, --client-key, --client-cert need to be set unless --plaintext is used",
            )
    if not args.endpoint:
        raise argparse.ArgumentError(
            None,
            "--endpoint needs to be set if --namespace/--pod is not used",
        )
    yield fn(args.endpoint, credentials, [])

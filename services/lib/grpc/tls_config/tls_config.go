package tlsconfig

import (
	"crypto/tls"
	"crypto/x509"
	"os"

	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
)

type ClientConfig struct {
	KeyPath  string `json:"key_path"`
	CertPath string `json:"cert_path"`
	CaPath   string `json:"ca_path"`
}

type ServerConfig struct {
	KeyPath  string `json:"key_path"`
	CertPath string `json:"cert_path"`
	CaPath   string `json:"ca_path"`
}

func GetClientTls(config *ClientConfig) (credentials.TransportCredentials, error) {
	if config == nil {
		return insecure.NewCredentials(), nil
	}

	cert, err := tls.LoadX509KeyPair(config.CertPath, config.KeyPath)
	if err != nil {
		return nil, err
	}

	certpool := x509.NewCertPool()
	x, err := os.ReadFile(config.CaPath)
	if err != nil {
		return nil, err
	}
	certpool.AppendCertsFromPEM(x)

	// Create TLS credentials
	creds := credentials.NewTLS(&tls.Config{
		Certificates: []tls.Certificate{cert},
		RootCAs:      certpool,
	})
	return creds, nil
}

// returns a credentials.TransportCredentials for the server
// if configs is nil or empty or has at least one nil element, returns insecure creds
func GetServerTls(configs []*ServerConfig) (credentials.TransportCredentials, error) {
	if configs == nil || len(configs) == 0 {
		return insecure.NewCredentials(), nil
	}
	var certs []tls.Certificate
	certpool := x509.NewCertPool()
	for _, config := range configs {
		if config == nil {
			return insecure.NewCredentials(), nil
		}
		cert, err := tls.LoadX509KeyPair(config.CertPath, config.KeyPath)
		if err != nil {
			return nil, err
		}
		certs = append(certs, cert)

		x, err := os.ReadFile(config.CaPath)
		if err != nil {
			return nil, err
		}
		certpool.AppendCertsFromPEM(x)
	}

	// Create TLS credentials
	creds := credentials.NewTLS(&tls.Config{
		Certificates: certs,
		ClientAuth:   tls.RequireAndVerifyClientCert,
		ClientCAs:    certpool,
	})
	return creds, nil
}

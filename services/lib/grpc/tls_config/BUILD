load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

rust_library(
    name = "grpc_tls_config_rs",
    srcs = ["lib.rs"],
    aliases = aliases(),
    crate_name = "grpc_tls_config",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = [
        "//services:__subpackages__",
        "//tools/genie:__subpackages__",
    ],
    deps = all_crate_deps(
        normal = True,
    ),
)

rust_test(
    name = "grpc_tls_config_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":grpc_tls_config_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ),
)

py_library(
    name = "grpc_tls_config_py",
    srcs = ["tls_config.py"],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        requirement("dataclasses-json"),
    ],
)

go_library(
    name = "grpc_tls_config_go",
    srcs = ["tls_config.go"],
    importpath = "github.com/augmentcode/augment/services/lib/grpc/tls_config",
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//credentials/insecure",
    ],
)

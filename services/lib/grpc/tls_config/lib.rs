use serde::{Deserialize, Serialize};
use tonic::transport::{Certificate, ClientTlsConfig, Identity, ServerTlsConfig};

#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct TlsConfig {
    pub key_path: String,
    pub cert_path: String,
    pub ca_path: String,
}

fn read_all(f: &str) -> tonic::Result<String> {
    std::fs::read_to_string(f)
        .map_err(|e| tonic::Status::internal(format!("Failed to read file {}: {}", f, e)))
}

fn get_cert_and_identity(config: &TlsConfig) -> tonic::Result<(Certificate, Identity)> {
    let key_data = read_all(&config.key_path)?;
    let cert_data = read_all(&config.cert_path)?;
    let ca_data = read_all(&config.ca_path)?;
    let cert = Certificate::from_pem(ca_data);
    let identity = Identity::from_pem(cert_data, key_data);
    Ok((cert, identity))
}

pub fn get_server_tls_creds(
    maybe_config: &Option<TlsConfig>,
) -> tonic::Result<Option<ServerTlsConfig>> {
    let tls = if let Some(config) = maybe_config.as_ref() {
        let (cert, identity) = get_cert_and_identity(config)?;

        Some(
            ServerTlsConfig::new()
                .client_ca_root(cert)
                .identity(identity),
        )
    } else {
        None
    };
    Ok(tls)
}

pub fn get_client_tls_creds(
    maybe_config: &Option<TlsConfig>,
) -> tonic::Result<Option<ClientTlsConfig>> {
    let tls = if let Some(config) = maybe_config.as_ref() {
        let (cert, identity) = get_cert_and_identity(config)?;

        Some(
            ClientTlsConfig::new()
                .ca_certificate(cert)
                .identity(identity),
        )
    } else {
        None
    };
    Ok(tls)
}

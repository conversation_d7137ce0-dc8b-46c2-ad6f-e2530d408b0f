"""A python library for TLS configuration"""

import logging
import pathlib
from dataclasses import dataclass
from dataclasses_json import dataclass_json
from typing import Optional

import grpc


@dataclass_json
@dataclass(frozen=True)
class ClientConfig:
    key_path: str
    cert_path: str
    ca_path: str


@dataclass_json
@dataclass(frozen=True)
class ServerConfig:
    key_path: str
    cert_path: str
    ca_path: str


def get_client_tls_creds(
    config: Optional[ClientConfig],
) -> Optional[grpc.ChannelCredentials]:
    if config is None:
        return None

    root_certificates = pathlib.Path(config.ca_path).read_bytes()

    logging.info("Using root certificates: %s=%s", config.ca_path, root_certificates)

    return grpc.ssl_channel_credentials(
        root_certificates=root_certificates,
        private_key=pathlib.Path(config.key_path).read_bytes(),
        certificate_chain=pathlib.Path(config.cert_path).read_bytes(),
    )


def get_server_tls_creds(
    config: Optional[ServerConfig],
) -> Optional[grpc.ServerCredentials]:
    if config is None:
        return None

    return grpc.ssl_server_credentials(
        [
            (
                pathlib.Path(config.key_path).read_bytes(),
                pathlib.Path(config.cert_path).read_bytes(),
            )
        ],
        root_certificates=pathlib.Path(config.ca_path).read_bytes(),
        require_client_auth=True,
    )


def get_server_tls_creds_multi(
    configs: Optional[list[ServerConfig]],
) -> Optional[grpc.ServerCredentials]:
    if not configs:
        return None

    private_key_cert_pairs = []
    root_certificates = None

    for config in configs:
        if config is None:
            return None

        private_key = pathlib.Path(config.key_path).read_bytes()
        certificate_chain = pathlib.Path(config.cert_path).read_bytes()
        private_key_cert_pairs.append((private_key, certificate_chain))

        ca_cert = pathlib.Path(config.ca_path).read_bytes()
        if root_certificates is None:
            root_certificates = ca_cert
        else:
            root_certificates += ca_cert

    logging.info("Using %d certificates for server", len(private_key_cert_pairs))

    return grpc.ssl_server_credentials(
        private_key_cert_pairs,
        root_certificates=root_certificates,
        require_client_auth=True,
    )

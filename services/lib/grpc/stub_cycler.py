"""Cycle through a new stub every X seconds."""

import logging
import threading
import time
from typing import Callable, Generic, TypeVar

from base import feature_flags

# We currently do not cancel requests when the stub is cycled.
# This introduces the risk of accumulating lots of stubs if we have
# long-running streams.
STUB_LIFETIME_SECONDS_FLAG = feature_flags.IntFlag("stub_cycler_lifetime_seconds", 60)

T = TypeVar("T")


class StubCycler(Generic[T]):
    """Cycle through a new stub every X seconds."""

    def __init__(self, stub_factory: Callable[[], T]):
        self._stub_factory = stub_factory
        self._stub = self._stub_factory()
        self._lock = threading.Lock()  # use for concurrent accesses

        self._shutdown = threading.Event()
        self._thread = threading.Thread(
            target=self._run, daemon=True, name="StubCycler"
        )
        self._thread.start()
        logging.info("Stub cycler started")

    def _refresh_stub(self):
        """Refresh the stub."""
        logging.debug("Stub cycler creating new stub")
        new_stub = self._stub_factory()
        with self._lock:
            self._stub = new_stub

    def get_stub(self) -> T:
        """Get the currently used stub."""
        with self._lock:
            return self._stub

    def _run(self):
        """Run the cycler."""
        stub_creation_time = time.time()
        stub_creation_interval_seconds = STUB_LIFETIME_SECONDS_FLAG.get(
            feature_flags.get_global_context()
        )
        while not self._shutdown.wait(timeout=stub_creation_interval_seconds):
            if time.time() - stub_creation_time > stub_creation_interval_seconds:
                self._refresh_stub()
                stub_creation_time = time.time()

    def shutdown(self):
        """Shutdown the cycler."""
        self._shutdown.set()
        self._thread.join()

    def __del__(self):
        self.shutdown()

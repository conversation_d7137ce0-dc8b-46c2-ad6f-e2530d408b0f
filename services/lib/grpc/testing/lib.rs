use hyper_util::rt::TokioIo;
use std::sync::Arc;
use tempfile::NamedTempFile;
use tokio::net::{UnixListener, UnixStream};
use tokio_stream::wrappers::UnixListenerStream;
use tonic::transport::{Channel, Endpoint, Uri};
use tower::service_fn;

// https://stackoverflow.com/questions/69845664/how-to-integration-test-tonic-application
pub async fn setup_channel() -> (UnixListenerStream, Channel) {
    let socket = NamedTempFile::new().unwrap();
    let socket = Arc::new(socket.into_temp_path());
    std::fs::remove_file(&*socket).unwrap();

    let uds = UnixListener::bind(&*socket).unwrap();
    let stream = UnixListenerStream::new(uds);

    // Connect to the server over a Unix socket
    // The URL will be ignored.
    let channel =
        Endpoint::try_from("http://any.url")
            .unwrap()
            .connect_with_connector(service_fn(move |_: Uri| {
                let socket = Arc::clone(&socket);

                async move {
                    Ok::<_, std::io::Error>(TokioIo::new(UnixStream::connect(&*socket).await?))
                }
            }))
            .await
            .unwrap();

    (stream, channel)
}

#[cfg(test)]
mod tests {
    use std::future::Future;
    use tonic::transport::{Channel, Server};
    use tonic_health::pb::health_client::HealthClient;
    use tonic_health::pb::HealthCheckRequest;

    async fn server_and_client_stub() -> (impl Future<Output = ()>, HealthClient<Channel>) {
        let (_health_reporter, health_service) = tonic_health::server::health_reporter();
        let (stream, channel) = super::setup_channel().await;
        let serve_future = async {
            let result = Server::builder()
                .add_service(health_service)
                .serve_with_incoming(stream)
                .await;
            assert!(result.is_ok());
        };

        (serve_future, HealthClient::new(channel))
    }

    #[tokio::test]
    async fn test_grpc_server_example() {
        let (serve_future, mut client) = server_and_client_stub().await;

        let request_future = async {
            let response = client
                .check(tonic::Request::new(HealthCheckRequest {
                    service: "".into(),
                }))
                .await
                .unwrap()
                .into_inner();
            // Validate server response with assertions
            assert_eq!(response.status, 1);
        };

        // Wait for completion, when the client request future completes
        tokio::select! {
            _ = serve_future => panic!("server returned first"),
            _ = request_future => (),
        }
    }
}

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

rust_library(
    name = "grpc_auth",
    srcs = ["lib.rs"],
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//services/lib/grpc/testing:grpc_testing",
        "//services/lib/request_context:request_context_rs",
    ],
)

rust_test(
    name = "grpc_auth_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":grpc_auth",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//services/lib/grpc/testing:grpc_testing",
    ],
)

py_library(
    name = "service_auth",
    srcs = ["service_auth.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_py_proto",
        "//services/lib/request_context:request_context_py",
        requirement("pydantic"),
        requirement("grpcio"),
    ],
)

pytest_test(
    name = "service_auth_test",
    srcs = ["service_auth_test.py"],
    deps = [
        ":service_auth",
    ],
)

py_library(
    name = "service_token_auth",
    srcs = ["service_token_auth.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":service_auth",
        "//base/feature_flags:feature_flags_py",
        "//services/auth/central/server:auth_entities_py_proto",
        "//services/lib/request_context:request_context_py",
        "//services/token_exchange/client:client_py",
        requirement("prometheus_client"),
        requirement("pyjwt"),
        requirement("pydantic"),
        requirement("grpcio"),
    ],
)

pytest_test(
    name = "service_token_auth_test",
    srcs = ["service_token_auth_test.py"],
    data = glob(["test_data/**"]),
    deps = [
        requirement("pyjwt"),
        ":service_token_auth",
    ],
)

py_library(
    name = "service_auth_interceptor",
    srcs = ["service_auth_interceptor.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":service_auth",
        requirement("grpc_interceptor"),
        requirement("prometheus_client"),
    ],
)

pytest_test(
    name = "service_auth_interceptor_test",
    srcs = ["service_auth_interceptor_test.py"],
    deps = [
        ":service_auth_interceptor",
        requirement("pytest-asyncio"),
    ],
)

go_library(
    name = "grpc_auth_go",
    srcs = [
        "claims.go",
        "service_auth_interceptor.go",
        "service_token_auth.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/grpc/auth",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_micahparks_jwkset//:jwkset",
        "@com_github_micahparks_keyfunc_v3//:keyfunc",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "grpc_auth_go_test",
    srcs = [
        "claims_test.go",
        "service_auth_interceptor_test.go",
        "service_token_auth_test.go",
    ],
    data = glob(["test_data/**"]),
    embed = [":grpc_auth_go"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "@org_golang_google_grpc//metadata",
    ],
)

# grpc_auth

Helper library for validating JWTs in GRPC services.

## Service Auth

This library provides a client for the auth service.

```python
from services.token_exchange.client import service_auth

auth = service_auth.PeerIdentityServiceAuth(
    allow_list=[
        service_auth.AuthPeerIdentifyConfig(
            peer_identities=["test-peer-1"],
            method="/test.TestService/TestMethod",
        )
    ]
)

auth_info = auth.validate_access(
    peer_identities=[b"test-peer-1"],
    auth_token=None,
    method_name="/test.TestService/TestMethod",
)
```

## Token Service Auth

This library provides a client for the token exchange service.

```python
from services.token_exchange.client import service_token_auth

auth = service_token_auth.ServiceTokenAuth(
    key_source=service_token_auth.GrpcPublicKeySource(
        token_exchange_server=client
    )
)

auth_info = auth.validate_access(
    peer_identities=[],
    auth_token=token,
    method_name="test",
)
```

## Security Considerations

The token exchange is a critical path of the authorization architecture of the system.
It is important that the token exchange service is secure.

A service will recieve a request with a token and has to validate the token.
It will receive the JWT public key from the token exchange service to validate the token.
The service knows that it talks to the correct token exchange due to the TLS certificate.
The token exchange service will only give out the public key if the client is also authorized (MTLS).

The public key is used to validate the JWT token. If that is successful, the service will know that the token is valid.
The service is now allowed to trust the information, in particular about the tenant.

If the verification fails, the service will not be allowed to trust the information.
However, it is possible that the token service has rotated the private key/public key pair.
Thus, the service will download the new public key and retry the validation.

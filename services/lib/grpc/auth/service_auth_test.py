"""Tests for the service_auth module."""

import grpc
import pytest

from services.auth.central.server import auth_entities_pb2
from services.lib.grpc.auth.service_auth import (
    AuthInfo,
    AuthPeerIdentifyConfig,
    PeerIdentityServiceAuth,
    load_peer_identity_auth_config_from_json,
)


def test_load_auth_peer_config_from_json():
    """Tests loading a list of AuthPeerIdentifyConfig from a JSON string."""

    json_str = """{
        "rules": [
        {
            "peer_identities": ["test-peer-1", "test-peer-2"],
            "method": "/test.TestService/TestMethod"
        },
        {
            "peer_identities": ["test-peer-3"],
            "method": "/test.TestService/*"
        }
        ]
    }"""

    config = load_peer_identity_auth_config_from_json(json_str)
    assert len(config.rules) == 2
    assert config.rules[0].peer_identities == ["test-peer-1", "test-peer-2"]
    assert config.rules[0].method == "/test.TestService/TestMethod"
    assert config.rules[1].peer_identities == ["test-peer-3"]
    assert config.rules[1].method == "/test.TestService/*"


def test_peer_identity_service_auth():
    """Tests the PeerIdentityServiceAuth implementation."""

    auth = PeerIdentityServiceAuth(
        [
            AuthPeerIdentifyConfig(
                peer_identities=["test-peer-1"],
                method="/test.TestService/TestMethod",
            )
        ],
        reject_no_peer_identity=True,
        default_tenant_name="test-ns",
    )
    assert auth.validate_access([b"test-peer-1"], None, "/test.TestService/TestMethod")
    with pytest.raises(grpc.RpcError):
        auth.validate_access([b"test-peer-2"], None, "/test.TestService/TestMethod")

    auth = PeerIdentityServiceAuth(
        [
            AuthPeerIdentifyConfig(
                peer_identities=["test-peer-1"],
                method="/test.TestService/TestMethod",
            )
        ],
        reject_no_peer_identity=False,
        default_tenant_name="test-ns",
    )
    assert auth.validate_access([b"test-peer-1"], None, "/test.TestService/TestMethod")


def test_peer_identity_service_auth_no_methods():
    """Tests the PeerIdentityServiceAuth implementation."""

    auth = PeerIdentityServiceAuth(
        [
            AuthPeerIdentifyConfig(
                peer_identities=["test-peer-1"],
                method=None,
            )
        ],
        reject_no_peer_identity=True,
        default_tenant_name="test-ns",
    )
    assert auth.validate_access([b"test-peer-1"], None, "/test.TestService/TestMethod")
    assert auth.validate_access([b"test-peer-1"], None, "/test.TestService/TestMethod2")
    with pytest.raises(grpc.RpcError):
        auth.validate_access([b"test-peer-2"], None, "/test.TestService/TestMethod")


def test_peer_certificate_no_peer_identities():
    """Tests the PeerIdentityServiceAuth implementation."""

    auth = PeerIdentityServiceAuth(
        [
            AuthPeerIdentifyConfig(
                peer_identities=[],
                method="/test.TestService/TestMethod",
            )
        ],
        reject_no_peer_identity=True,
        default_tenant_name="test-ns",
    )
    with pytest.raises(grpc.RpcError):
        auth.validate_access([b"test-peer-1"], None, "/test.TestService/TestMethod")


def test_peer_identity_service_auth_no_peer_identities_to_validate():
    """Tests the PeerIdentityServiceAuth implementation."""

    auth = PeerIdentityServiceAuth(
        [
            AuthPeerIdentifyConfig(
                peer_identities=["test-peer-1"],
                method=None,
            )
        ],
        reject_no_peer_identity=True,
        default_tenant_name="test-ns",
    )
    with pytest.raises(grpc.RpcError):
        auth.validate_access([], None, "/test.TestService/TestMethod")


def test_peer_identity_service_auth_with_method_wildcards():
    """Tests the PeerIdentityServiceAuth implementation."""

    auth = PeerIdentityServiceAuth(
        [
            AuthPeerIdentifyConfig(
                peer_identities=["test-peer-1"],
                method=r"/test.TestService/.*",
            )
        ],
        reject_no_peer_identity=True,
        default_tenant_name="test-ns",
    )
    assert auth.validate_access([b"test-peer-1"], None, "/test.TestService/TestMethod")
    assert auth.validate_access([b"test-peer-1"], None, "/test.TestService/TestMethod2")
    with pytest.raises(grpc.RpcError):
        auth.validate_access([b"test-peer-2"], None, "/test.TestService/TestMethod")


def test_auth_token_iap_email():
    """Tests the AuthInfo.iap_email property."""
    auth_info = AuthInfo(
        tenant_id=None,
        tenant_name="test-ns",
        service_name="test-service",
        shard_namespace="test-ns",
        cloud="test-cloud",
        opaque_user_id="iap:<EMAIL>",
        opaque_user_id_type=auth_entities_pb2.UserId.UserIdType.Name(
            auth_entities_pb2.UserId.UserIdType.INTERNAL_IAP,
        ),
    )
    assert auth_info.iap_email == "<EMAIL>"

    auth_info = AuthInfo(
        tenant_id=None,
        tenant_name="test-ns",
        shard_namespace="test-ns",
        cloud="test-cloud",
        opaque_user_id="1234",
        opaque_user_id_type=auth_entities_pb2.UserId.UserIdType.Name(
            auth_entities_pb2.UserId.UserIdType.INTERNAL_IAP,
        ),
    )
    assert auth_info.iap_email is None

    auth_info = AuthInfo(
        tenant_id=None,
        tenant_name="test-ns",
        shard_namespace="test-ns",
        cloud="test-cloud",
        opaque_user_id="iap:1",
        opaque_user_id_type=auth_entities_pb2.UserId.UserIdType.Name(
            auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
    )
    assert auth_info.iap_email is None

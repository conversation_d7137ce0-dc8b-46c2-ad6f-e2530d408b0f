package auth

import (
	"context"
	"slices"

	"github.com/augmentcode/augment/base/go/secretstring"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
)

// By default, skip token validation for health check and reflection endpoints.
var defaultExcludePaths = []string{
	"/grpc.health.v1.Health/Check",
	"/grpc.health.v1.Health/Watch",
	"/grpc.reflection.v1.ServerReflection/ServerReflectionInfo",
}

// Given an auth token, returns the claims from the token if the token is authorized and an error
// otherwise. Most real-world services will want to use `ServiceTokenAuth.ValidateAccess`.
type ValidateAccess func(ctx context.Context, authToken secretstring.SecretString) (*AugmentClaims, error)

// Intercepts gRPC requests and checks their auth token. Requests that are not authorized (as
// determined by `validateAccess`) are rejected.
type AuthServerInterceptor struct {
	validateAccess ValidateAccess
}

// Constructor.
func NewAuthServerInterceptor(validateAccessFunc ValidateAccess) *AuthServerInterceptor {
	return &AuthServerInterceptor{
		validateAccess: validateAccessFunc,
	}
}

// Intercepts a gRPC request and checks its auth token. Adds the token's AugmentClaims to the
// request context and passes to the next handler if the token is valid. Otherwise returns an error.
func (a *AuthServerInterceptor) Intercept(
	ctx context.Context,
	req interface{},
	info *grpc.UnaryServerInfo,
	handler grpc.UnaryHandler,
) (interface{}, error) {
	if slices.Contains(defaultExcludePaths, info.FullMethod) {
		log.Debug().Msgf("Skipping authorization for %s", info.FullMethod)
		return handler(ctx, req)
	}

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to get request context for %s", info.FullMethod)
		return nil, err
	}

	augmentClaims, err := a.validateAccess(ctx, requestContext.AuthToken)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to validate access for %s", info.FullMethod)
		return nil, err
	}

	if augmentClaims != nil {
		// the token was provided and is valid
		ctx = augmentClaims.NewContext(ctx)
	}
	return handler(ctx, req)
}

// A wrapper around a ServerStream that allows us to override the context
type streamWithContext struct {
	grpc.ServerStream
	ctx context.Context
}

func (swc *streamWithContext) Context() context.Context {
	return swc.ctx
}

func (a *AuthServerInterceptor) StreamIntercept(
	srv any, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler,
) error {
	if slices.Contains(defaultExcludePaths, info.FullMethod) {
		log.Debug().Msgf("Skipping authorization for %s", info.FullMethod)
		return handler(srv, ss)
	}

	ctx := ss.Context()
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to get request context for %s", info.FullMethod)
		return err
	}

	augmentClaims, err := a.validateAccess(ctx, requestContext.AuthToken)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to validate access for %s", info.FullMethod)
		return err
	}

	ctx = augmentClaims.NewContext(ctx)
	return handler(srv, &streamWithContext{
		ServerStream: ss,
		ctx:          ctx,
	})
}

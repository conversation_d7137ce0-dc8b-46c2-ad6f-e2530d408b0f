use hyper::{Request, Response};
use lazy_static::lazy_static;
use prometheus::{register_int_counter, register_int_counter_vec, IntCounter, IntCounterVec, Opts};
use request_context::TenantInfo;
use secrecy::SecretString;
use std::{
    collections::HashSet,
    pin::Pin,
    sync::Arc,
    task::{Context, Poll},
};
use tonic::{async_trait, body::BoxBody};
use tower::{Layer, Service};

// Add this near the top of the file, after the imports
lazy_static! {
    static ref SUCCESSFUL_TOKEN_VALIDATIONS: IntCounter = register_int_counter!(Opts::new(
        "au_grpc_token_auth_successful_validations_total",
        "Total number of successful token validations"
    ),)
    .expect("metric can be created");
    static ref FAILED_TOKEN_VALIDATIONS: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_grpc_token_auth_failed_validations_total",
            "Total number of failed token validations"
        ),
        &["reason"]
    )
    .expect("metric can be created");
}

// By default, skip token validation for health check and reflection endpoints
const DEFAULT_EXCLUDE_PATHS: [&str; 4] = [
    "/grpc.health.v1.Health/Check",
    "/grpc.health.v1.Health/Watch",
    "/grpc.reflection.v1.ServerReflection/ServerReflectionInfo",
    "/grpc.reflection.v1alpha.ServerReflection/ServerReflectionInfo",
];

#[async_trait]
pub trait GrpcAuth {
    async fn validate_access(
        &self,
        peer_identities: Vec<Vec<u8>>,
        auth_token: Option<SecretString>,
        method_name: &str,
    ) -> tonic::Result<TenantInfo>;
}

/// Middleware to intercept gRPC requests and authenticate JWTs. Adapted from the tower
/// documentation examples:
/// https://github.com/hyperium/tonic/blob/master/examples/src/tower/server.rs
/// exclude_paths is a list of paths to exclude from authentication
#[derive(Clone)]
pub struct GrpcAuthMiddlewareLayer {
    grpc_auth: Arc<dyn GrpcAuth + Send + Sync>,
    exclude_paths: HashSet<String>,
}

impl GrpcAuthMiddlewareLayer {
    pub fn new(grpc_auth: Arc<dyn GrpcAuth + Send + Sync>) -> Self {
        let exclude_paths = DEFAULT_EXCLUDE_PATHS
            .iter()
            .map(|s| s.to_string())
            .collect();
        Self {
            grpc_auth,
            exclude_paths,
        }
    }

    pub fn new_with_exclude_paths(
        grpc_auth: Arc<dyn GrpcAuth + Send + Sync>,
        exclude_paths: Vec<String>,
    ) -> Self {
        Self {
            grpc_auth,
            exclude_paths: exclude_paths.into_iter().collect(),
        }
    }
}

impl<S> Layer<S> for GrpcAuthMiddlewareLayer {
    type Service = GrpcAuthMiddleware<S>;

    fn layer(&self, service: S) -> Self::Service {
        GrpcAuthMiddleware {
            inner: service,
            grpc_auth: self.grpc_auth.clone(),
            exclude_paths: self.exclude_paths.clone(),
        }
    }
}

#[derive(Clone)]
pub struct GrpcAuthMiddleware<S> {
    inner: S,
    grpc_auth: Arc<dyn GrpcAuth + Send + Sync>,
    exclude_paths: HashSet<String>,
}

type BoxFuture<'a, T> = Pin<Box<dyn std::future::Future<Output = T> + Send + 'a>>;

impl<S> Service<Request<BoxBody>> for GrpcAuthMiddleware<S>
where
    S: Service<Request<BoxBody>, Response = Response<BoxBody>> + Clone + Send + 'static,
    S::Future: Send + 'static,
    S::Error: From<tonic::Status> + Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, req: Request<BoxBody>) -> Self::Future {
        let path = req.uri().path();
        if self.exclude_paths.contains(path) {
            return Box::pin(self.inner.call(req));
        }

        // This is necessary because tonic internally uses `tower::buffer::Buffer`.
        // See https://github.com/tower-rs/tower/issues/547#issuecomment-767629149
        // for details on why this is necessary
        let clone = self.inner.clone();
        let mut inner = std::mem::replace(&mut self.inner, clone);
        let grpc_auth = self.grpc_auth.clone();

        Box::pin(async move {
            let mut req = req;
            // TODO: share with request_context?!
            let token_value = req.headers().get("authorization");
            let token = token_value
                .map(|e| -> tonic::Result<SecretString> {
                    let res = e
                        .to_str()
                        .map_err(|_| {
                            FAILED_TOKEN_VALIDATIONS
                                .with_label_values(&["invalid_token"])
                                .inc();
                            tonic::Status::invalid_argument("token is not a string")
                        })?
                        .strip_prefix("Bearer ")
                        .ok_or_else(|| {
                            FAILED_TOKEN_VALIDATIONS
                                .with_label_values(&["missing_bearer_prefix"])
                                .inc();
                            tonic::Status::invalid_argument("token does not use Bearer prefix")
                        })?
                        .to_string();
                    Ok(SecretString::new(res))
                })
                .transpose();
            let token = match token {
                Ok(token) => token,
                Err(status) => {
                    FAILED_TOKEN_VALIDATIONS
                        .with_label_values(&["invalid_token"])
                        .inc();
                    return Err(status.into());
                }
            };

            let tenant_info = grpc_auth
                .validate_access(
                    vec![], // TODO
                    token,
                    req.uri().path(),
                )
                .await;
            let tenant_info = match tenant_info {
                Ok(tenant_info) => tenant_info,
                Err(status) => {
                    FAILED_TOKEN_VALIDATIONS
                        .with_label_values(&["validation_failed"])
                        .inc();
                    return Err(status.into());
                }
            };

            SUCCESSFUL_TOKEN_VALIDATIONS.inc();
            req.extensions_mut().insert::<TenantInfo>(tenant_info);

            inner.call(req).await
        })
    }
}

/// Extract the tenant info from a grpc request
pub fn tenant_info_from_grpc_req<T>(req: &tonic::Request<T>) -> Option<TenantInfo> {
    return req.extensions().get::<TenantInfo>().cloned();
}

#[cfg(test)]
mod tests {
    use super::*;

    use grpc_testing::setup_channel;
    use std::future::Future;
    use tonic::transport::{Channel, Server};
    use tonic_health::pb::health_client::HealthClient;
    use tonic_health::pb::HealthCheckRequest;

    struct MockGrpcAuth {
        pub default_tenant_name: String,
        pub succeed: bool,
    }

    #[async_trait]
    impl GrpcAuth for MockGrpcAuth {
        async fn validate_access(
            &self,
            _peer_identities: Vec<Vec<u8>>,
            _auth_token: Option<SecretString>,
            _method_name: &str,
        ) -> tonic::Result<TenantInfo> {
            if self.succeed {
                Ok(TenantInfo {
                    tenant_id: None,
                    tenant_name: self.default_tenant_name.clone(),
                    shard_namespace: self.default_tenant_name.clone(),
                    cloud: "gcp".to_string(),
                    scopes: vec![],
                    user_id: None,
                    opaque_user_id: None,
                    user_email: None,
                    service_name: None,
                })
            } else {
                Err(tonic::Status::unauthenticated("No token provided"))
            }
        }
    }

    #[tokio::test]
    async fn test_no_auth() {
        let auth = MockGrpcAuth {
            default_tenant_name: "test-ns".to_string(),
            succeed: true,
        };
        assert!(auth.validate_access(vec![], None, "test").await.is_ok());
    }

    async fn server_and_client_stub(
        grpc_auth: Arc<dyn GrpcAuth + Send + Sync>,
        exclude_paths: Vec<String>,
    ) -> (impl Future<Output = ()>, HealthClient<Channel>) {
        let (_health_reporter, health_service) = tonic_health::server::health_reporter();
        let (stream, channel) = setup_channel().await;
        let serve_future = async {
            let result = Server::builder()
                .layer(
                    tower::ServiceBuilder::new()
                        .layer(GrpcAuthMiddlewareLayer::new_with_exclude_paths(
                            grpc_auth,
                            exclude_paths,
                        ))
                        .into_inner(),
                )
                .add_service(health_service)
                .serve_with_incoming(stream)
                .await;
            assert!(result.is_ok());
        };

        (serve_future, HealthClient::new(channel))
    }

    // auth that always succeeds
    async fn server_and_client_stub_always_ok() -> (impl Future<Output = ()>, HealthClient<Channel>)
    {
        let grpc_auth = Arc::new(MockGrpcAuth {
            default_tenant_name: "test-ns".to_string(),
            succeed: true,
        });
        server_and_client_stub(grpc_auth, vec![]).await
    }

    // auth that always fails
    async fn server_and_client_stub_always_fail(
    ) -> (impl Future<Output = ()>, HealthClient<Channel>) {
        let grpc_auth = Arc::new(MockGrpcAuth {
            default_tenant_name: "test-ns".to_string(),
            succeed: false,
        });
        server_and_client_stub(grpc_auth, vec![]).await
    }

    // auth that always fails, except for health check endpoints
    async fn server_and_client_stub_always_fail_except_health(
    ) -> (impl Future<Output = ()>, HealthClient<Channel>) {
        let grpc_auth = Arc::new(MockGrpcAuth {
            default_tenant_name: "test-ns".to_string(),
            succeed: false,
        });
        let exclude_paths = DEFAULT_EXCLUDE_PATHS
            .iter()
            .map(|s| s.to_string())
            .collect();
        server_and_client_stub(grpc_auth, exclude_paths).await
    }

    #[tokio::test]
    async fn test_middleware_ok() {
        let (serve_future, mut client) = server_and_client_stub_always_ok().await;

        let request_future = async {
            let response = client
                .check(tonic::Request::new(HealthCheckRequest {
                    service: "".into(),
                }))
                .await
                .unwrap()
                .into_inner();
            // Validate server response with assertions
            assert_eq!(response.status, 1);
        };

        // Wait for completion, when the client request future completes
        tokio::select! {
            _ = serve_future => panic!("server returned first"),
            _ = request_future => (),
        }
    }

    #[tokio::test]
    async fn test_middleware_health_skip_auth() {
        let (serve_future, mut client) = server_and_client_stub_always_fail_except_health().await;

        let request_future = async {
            let response = client
                .check(tonic::Request::new(HealthCheckRequest {
                    service: "".into(),
                }))
                .await
                .unwrap()
                .into_inner();
            // Validate server response with assertions
            assert_eq!(response.status, 1);
        };

        // Wait for completion, when the client request future completes
        tokio::select! {
            _ = serve_future => panic!("server returned first"),
            _ = request_future => (),
        }
    }

    #[tokio::test]
    async fn test_middleware_err() {
        let (serve_future, mut client) = server_and_client_stub_always_fail().await;

        let request_future = async {
            let response = client
                .check(tonic::Request::new(HealthCheckRequest {
                    service: "".into(),
                }))
                .await;
            assert!(response.is_err());
        };

        // Wait for completion, when the client request future completes
        tokio::select! {
            _ = serve_future => panic!("server returned first"),
            _ = request_future => (),
        }
    }
}

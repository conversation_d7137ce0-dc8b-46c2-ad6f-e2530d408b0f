import pathlib

import grpc
import pydantic
import pytest
import typing

import base.feature_flags
from services.auth.central.server import auth_entities_pb2
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    PublicKeySource,
    ServiceTokenAuth,
)
from services.token_exchange.client.client import TokenExchangeClient


class FakeTokenExchangeServer(TokenExchangeClient):
    """Fake token exchange server implementation."""

    def __init__(self, signed_token_path: pathlib.Path, public_jwks_path: pathlib.Path):
        self.signed_token_path = signed_token_path
        self.public_jwks_path = public_jwks_path
        self.get_verification_key_count = 0

    def get_signed_token_for_user(
        self,
        user_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        user_email: str,
        tenant_id: str,
    ) -> pydantic.SecretStr:
        signed_token = self.signed_token_path.read_text()
        return pydantic.SecretStr(signed_token)

    def get_verification_key(self) -> bytes:
        self.get_verification_key_count += 1
        return self.public_jwks_path.read_bytes()

    def get_verification_key_calls(self) -> int:
        return self.get_verification_key_count

    def get_signed_token_for_iap_token(self, iap_token: str) -> pydantic.SecretStr:
        raise NotImplementedError()

    def get_signed_token_for_service(self, tenant_id: str) -> pydantic.SecretStr:
        raise NotImplementedError()


@pytest.fixture
def feature_flags() -> (
    typing.Generator[base.feature_flags.LocalFeatureFlagSetter, None, None]
):
    yield from base.feature_flags.feature_flag_fixture()


@pytest.fixture(scope="module")
def token_exchange_server():
    # This token is hardcoded to have {
    #     user_id: "123",
    #     tenant_id: "dev-augie",
    #     shard_namespace: "unit-test-namespace",
    # }
    signed_token_path = pathlib.Path("services/lib/grpc/auth/test_data/example.jwt")
    public_jwks_path = pathlib.Path("services/lib/grpc/auth/test_data/example.jwks")
    server = FakeTokenExchangeServer(signed_token_path, public_jwks_path)
    yield server


def test_service_token_auth(token_exchange_server: TokenExchangeClient, feature_flags):
    """Tests the ServiceTokenAuth implementation."""
    token = token_exchange_server.get_signed_token_for_user(
        "ignored-in-unit-test",
        auth_entities_pb2.UserId(
            user_id="userid", user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT
        ),
        "<EMAIL>",
        "123",
    )

    key_source = GrpcPublicKeySource(token_exchange_server)
    auth = ServiceTokenAuth(key_source, required_scopes=[])
    auth_info = auth.validate_access([], token, "test")
    assert auth_info is not None


def test_service_token_auth_with_no_token(
    token_exchange_server: TokenExchangeClient, feature_flags
):
    """Tests the ServiceTokenAuth implementation."""
    key_source = GrpcPublicKeySource(token_exchange_server)
    auth = ServiceTokenAuth(key_source, required_scopes=[])

    with pytest.raises(grpc.RpcError):
        auth.validate_access([], None, "test")


def test_service_token_auth_invalid_token(
    token_exchange_server: TokenExchangeClient, feature_flags
):
    """Tests the ServiceTokenAuth implementation."""
    token = token_exchange_server.get_signed_token_for_user(
        "ignored-in-unit-test",
        auth_entities_pb2.UserId(
            user_id="userid", user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT
        ),
        "<EMAIL>",
        "1234",
    )

    key_source = GrpcPublicKeySource(token_exchange_server)
    auth = ServiceTokenAuth(key_source, required_scopes=[])

    # corrupt the token
    token_value = token.get_secret_value()
    token_value = (
        token_value[:13] + ("1" if token_value[13] != "1" else "0") + token_value[14:]
    )
    invalid_token = pydantic.SecretStr(token_value)
    with pytest.raises(grpc.RpcError):
        auth.validate_access([], invalid_token, "test")


def test_service_token_cache(feature_flags):
    """Tests that caching in the ServiceTokenAuth implementation works as expected"""
    # This just reproduces the token_exchange_server() fixture logic so we know we have the fake
    # with the get_verification_key_calls() method rather than just the interface class
    signed_token_path = pathlib.Path("services/lib/grpc/auth/test_data/example.jwt")
    public_jwks_path = pathlib.Path("services/lib/grpc/auth/test_data/example.jwks")
    token_exchange_server = FakeTokenExchangeServer(signed_token_path, public_jwks_path)

    token = token_exchange_server.get_signed_token_for_user(
        "ignored-in-unit-test",
        auth_entities_pb2.UserId(
            user_id="userid", user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT
        ),
        "<EMAIL>",
        "123",
    )

    key_source = GrpcPublicKeySource(token_exchange_server)
    auth = ServiceTokenAuth(key_source, required_scopes=[])
    # First validate_access produces a verification key call
    auth_info = auth.validate_access([], token, "test")
    assert auth_info is not None
    assert token_exchange_server.get_verification_key_calls() == 1
    # Second validate_access uses the cached value
    auth_info = auth.validate_access([], token, "test")
    assert auth_info is not None
    assert token_exchange_server.get_verification_key_calls() == 1

    # Now simulate a cache miss
    signed_token_path_2 = pathlib.Path(
        "services/lib/grpc/auth/test_data/example-new-kid.jwt"
    )
    token_exchange_server.signed_token_path = signed_token_path_2
    new_kid_token = token_exchange_server.get_signed_token_for_user(
        "ignored-in-unit-test",
        auth_entities_pb2.UserId(
            user_id="userid", user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT
        ),
        "<EMAIL>",
        "123",
    )
    # We won't have a key in the JWKS for the new kid
    with pytest.raises(grpc.RpcError):
        auth.validate_access([], new_kid_token, "test")
    # Changed kid should have produced a fresh verification key call
    assert token_exchange_server.get_verification_key_calls() == 2


class FakePublicKeySource(PublicKeySource):
    """Fake public key source implementation."""

    def get_public_key(self, kid: str) -> str | None:
        key_data = pathlib.Path(
            "services/lib/grpc/auth/test_data/ec256-public-2.pem"
        ).read_text(encoding="utf-8")
        return key_data


def test_service_token_auth_invalid_public_key(
    token_exchange_server: TokenExchangeClient,
    feature_flags,
):
    """Tests the ServiceTokenAuth implementation if we have an invalid public key"""
    token = token_exchange_server.get_signed_token_for_user(
        "ignored-in-unit-test",
        auth_entities_pb2.UserId(
            user_id="userid", user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT
        ),
        "<EMAIL>",
        "1234",
    )

    key_source = FakePublicKeySource()
    auth = ServiceTokenAuth(key_source, required_scopes=[])

    with pytest.raises(grpc.RpcError):
        auth.validate_access([], token, "test")


def test_decoded_token_to_auth_info():
    """Tests the AuthInfo object returned by ServiceTokenAuth."""
    fake_decoded_token = {
        "tenant_id": "t1",
        "tenant_name": "tenant1",
        "shard_namespace": "ns1",
        "cloud": "gcp",
        "scope": [],
        "user_id": "userid",
        "opaque_user_id": "123",
        "opaque_user_id_type": auth_entities_pb2.UserId.UserIdType.Name(
            auth_entities_pb2.UserId.UserIdType.AUGMENT
        ),
        "user_email": "<EMAIL>",
    }

    key_source = FakePublicKeySource()
    auth = ServiceTokenAuth(key_source, required_scopes=[])
    auth_info = auth._decoded_token_to_auth_info(fake_decoded_token)

    assert auth_info.tenant_id == "t1"
    assert auth_info.tenant_name == "tenant1"
    assert auth_info.shard_namespace == "ns1"
    assert auth_info.cloud == "gcp"
    assert auth_info.scopes == []
    assert auth_info.user_id.get_secret_value() == "userid"
    assert auth_info.opaque_user_id == "123"
    assert auth_info.opaque_user_id_type == "AUGMENT"
    assert auth_info.user_email.get_secret_value() == "<EMAIL>"

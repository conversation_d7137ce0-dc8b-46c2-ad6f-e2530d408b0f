import asyncio
import collections
import logging
import typing

import grpc
from grpc_interceptor import Server<PERSON><PERSON><PERSON>, AsyncServerInterceptor
from prometheus_client import Counter
import pydantic

from services.lib.grpc.auth.service_auth import (
    AuthInfo,
    ServiceAuth,
    ServiceAuthException,
)
from services.lib.request_context.request_context import RequestContext

_auth_counter = Counter(
    "au_service_auth_counter",
    "Number of authentication attempts",
    ["peer_identity", "method", "status_code"],
)

_Metadatum = collections.namedtuple(
    "_Metadatum",
    (
        "key",
        "value",
    ),
)


# By default, skip token validation for health check and reflection endpoints
DEFAULT_EXCLUDE_PATHS = [
    "/grpc.health.v1.Health/Check",
    "/grpc.health.v1.Health/Watch",
    "/grpc.reflection.v1.ServerReflection/ServerReflectionInfo",
    "/grpc.reflection.v1alpha.ServerReflection/ServerReflectionInfo",
]


class ServiceTokenServicerContext(grpc.ServicerContext):
    """A servicer context that has a service token.

    A decorator for grpc.ServicerContext that adds a service token.

    Note: The opentelemetry system uses a similar decorator to propagate information.
    """

    def __init__(self, context: grpc.ServicerContext, auth_info: AuthInfo):
        self.context = context
        self._auth_info = auth_info

    def auth_info(self) -> AuthInfo:
        """Returns the auth info."""
        return self._auth_info

    def peer(self) -> str:
        return self.context.peer()

    def peer_identities(self) -> list[bytes]:
        return self.context.peer_identities()

    def peer_identity_key(self):
        return self.context.peer_identity_key()

    def auth_context(self) -> grpc.AuthMetadataContext:
        return self.context.auth_context()

    def invocation_metadata(self):
        """Returns the invocation metadata.

        We add a tenant id to the metadata as a way to propagate the
        information to the request handlers.

        the function get_auth_info_from_grpc_context should be used
        to extract the tenant id from the metadata
        """
        m = list(self.context.invocation_metadata())
        if self._auth_info.tenant_name:
            m.append(
                _Metadatum(
                    key="x-tenant-name",
                    value=self._auth_info.tenant_name,
                )
            )
        if self._auth_info.tenant_id:
            m.append(
                _Metadatum(
                    key="x-tenant-id",
                    value=self._auth_info.tenant_id,
                )
            )
        if self._auth_info.scopes:
            for scope in self._auth_info.scopes:
                assert "," not in scope
            m.append(
                _Metadatum(
                    key="x-scopes",
                    value=",".join(self._auth_info.scopes),
                )
            )
        if self._auth_info.shard_namespace:
            m.append(
                _Metadatum(
                    key="x-shard-namespace",
                    value=self._auth_info.shard_namespace,
                )
            )
        if self._auth_info.cloud:
            m.append(
                _Metadatum(
                    key="x-cloud",
                    value=self._auth_info.cloud,
                )
            )
        if self._auth_info.user_id:
            m.append(
                _Metadatum(
                    key="x-user-id",
                    value=self._auth_info.user_id.get_secret_value(),
                )
            )
        if self._auth_info.opaque_user_id:
            m.append(
                _Metadatum(
                    key="x-opaque-user-id",
                    value=self._auth_info.opaque_user_id,
                )
            )
        if self._auth_info.opaque_user_id_type:
            m.append(
                _Metadatum(
                    key="x-opaque-user-id-type",
                    value=self._auth_info.opaque_user_id_type,
                )
            )
        if self._auth_info.user_email:
            m.append(
                _Metadatum(
                    key="x-user-email",
                    value=self._auth_info.user_email.get_secret_value(),
                )
            )
        if self._auth_info.service_name:
            m.append(
                _Metadatum(
                    key="x-service-name",
                    value=self._auth_info.service_name,
                )
            )
        return tuple(m)

    def set_code(self, code):
        return self.context.set_code(code)

    def set_details(self, details: str):
        return self.context.set_details(details)

    def set_trailing_metadata(self, trailing_metadata: list[tuple[str, str]]):
        return self.context.set_trailing_metadata(trailing_metadata)

    def send_initial_metadata(self, initial_metadata: list[tuple[str, str]]):
        return self.context.send_initial_metadata(initial_metadata)

    def set_compression(self, compression: str):
        return self.context.set_compression(compression)

    def trailing_metadata(self):
        return self.context.trailing_metadata()

    def abort(self, code: grpc.StatusCode, details: str):
        return self.context.abort(code, details)

    def abort_with_status(self, status):
        return self.context.abort_with_status(status)

    def code(self):
        return self.context.code()

    def details(self):
        return self.context.details()

    def disable_next_message_compression(self):
        return self.context.disable_next_message_compression()

    def is_active(self):
        return self.context.is_active()

    def time_remaining(self):
        return self.context.time_remaining()

    def cancel(self):
        return self.context.cancel()

    def add_callback(self, callback):
        return self.context.add_callback(callback)


class ServiceAuthInterceptor(ServerInterceptor):
    """A GRPC interceptor that uses ServiceAuth to validate access to a tenant."""

    def __init__(
        self,
        service_auth: ServiceAuth,
        exclude_paths: list[str] | None = None,
    ):
        """
        Args:
            service_auth: The ServiceAuth implementation to use.
            exclude_paths: A list of paths to exclude from authentication.
        """
        if exclude_paths is None:
            exclude_paths = DEFAULT_EXCLUDE_PATHS
        self.service_auth = service_auth
        self.exclude_paths: set[str] = set(exclude_paths)

    def intercept(
        self,
        method: typing.Callable,
        request_or_iterator: typing.Any,
        context: grpc.ServicerContext,
        method_name: str,
    ) -> typing.Any:
        """Intercept an RPC.


        Args:
            method: The next interceptor, or method implementation.
            request_or_iterator: The RPC request, as a protobuf message.
            context: The ServicerContext pass by gRPC to the service.
            method_name: A string of the form
                "/protobuf.package.Service/Method"
        Returns:
            The RPC response, as a protobuf message.
        """
        if method_name in self.exclude_paths:
            return method(request_or_iterator, context)

        request_context = RequestContext.from_grpc_context(context)
        metric_peer_identity = ""
        with request_context.with_context_logging():
            peer_identities = context.peer_identities()
            if peer_identities:
                metric_peer_identity = peer_identities[0].decode()
            logging.debug(
                "Request: %s from %s on %s",
                request_context,
                peer_identities,
                method_name,
            )

            try:
                auth_info = self.service_auth.validate_access(
                    peer_identities=peer_identities,
                    auth_token=request_context.auth_token,
                    method_name=method_name,
                )
                _auth_counter.labels(
                    metric_peer_identity, method_name, grpc.StatusCode.OK.name
                ).inc()
                wrapped_context = ServiceTokenServicerContext(context, auth_info)
                return method(request_or_iterator, wrapped_context)
            except ServiceAuthException as e:
                _auth_counter.labels(
                    metric_peer_identity, method_name, e.code().name
                ).inc()
                logging.warning(
                    "Request: %s from %s on %s: %s",
                    request_context,
                    peer_identities,
                    method_name,
                    e.code().name,
                )
                context.abort(e.code(), e.details())


class ServiceAuthAsyncInterceptor(AsyncServerInterceptor):
    """A GRPC interceptor that uses ServiceAuth to validate access to a tenant."""

    def __init__(
        self,
        service_auth: ServiceAuth,
        exclude_paths: list[str] | None = None,
    ):
        """
        Args:
            service_auth: The ServiceAuth implementation to use.
            exclude_paths: A list of paths to exclude from authentication.
        """
        if exclude_paths is None:
            exclude_paths = DEFAULT_EXCLUDE_PATHS
        self.service_auth = service_auth
        self.exclude_paths: set[str] = set(exclude_paths)

    async def _iterate_results(
        self, iterator: typing.AsyncIterator
    ) -> typing.AsyncIterator:
        """Iterate through results while recording metrics for server streaming responses.

        Args:
            iterator: The async iterator of responses from a server streaming RPC.

        Yields:
            The responses from the iterator.
        """
        async for response in iterator:
            yield response

    async def intercept(
        self,
        method: typing.Callable,
        request_or_iterator: typing.Any,
        context: grpc.ServicerContext,
        method_name: str,
    ) -> typing.Any:
        """Intercept an RPC.


        Args:
            method: The next interceptor, or method implementation.
            request_or_iterator: The RPC request, as a protobuf message.
            context: The ServicerContext pass by gRPC to the service.
            method_name: A string of the form
                "/protobuf.package.Service/Method"
        Returns:
            The RPC response, as a protobuf message.
        """
        if method_name in self.exclude_paths:
            # Call the method
            # First get the result without awaiting - this is important because:
            # 1. We need to detect if the result is a coroutine that needs to be awaited
            # 2. Some gRPC methods return direct values (in tests or with sync implementations)
            # 3. Others return coroutines (with async implementations)
            # 4. We need to handle both cases correctly
            result = method(request_or_iterator, context)

            # Check if the result is a coroutine using asyncio.iscoroutine() which is more reliable
            # than checking for __await__ attribute or other approaches
            if asyncio.iscoroutine(result):
                # It's a coroutine, so await it to get the actual result
                result_or_iterator = await result
            else:
                # It's either an async iterator or a direct result, use it directly
                # This happens with sync implementations wrapped for async use
                result_or_iterator = result

            if hasattr(result_or_iterator, "__aiter__"):
                # If it's an async iterator (server streaming response), wrap it with our _iterate_results
                return self._iterate_results(result_or_iterator)
            else:
                # If it's not an iterator (unary response), just return the result directly
                # The metrics will be recorded when this context manager exits
                return result_or_iterator

        request_context = RequestContext.from_grpc_context(context)
        metric_peer_identity = ""
        with request_context.with_context_logging():
            peer_identities = context.peer_identities()
            if peer_identities:
                metric_peer_identity = peer_identities[0].decode()
            logging.debug(
                "Request: %s from %s on %s",
                request_context,
                peer_identities,
                method_name,
            )

            try:
                auth_info = self.service_auth.validate_access(
                    peer_identities=peer_identities,
                    auth_token=request_context.auth_token,
                    method_name=method_name,
                )
                _auth_counter.labels(
                    metric_peer_identity, method_name, grpc.StatusCode.OK.name
                ).inc()
                wrapped_context = ServiceTokenServicerContext(context, auth_info)

                result = method(request_or_iterator, wrapped_context)

                # Check if the result is a coroutine using asyncio.iscoroutine() which is more reliable
                # than checking for __await__ attribute or other approaches
                if asyncio.iscoroutine(result):
                    # It's a coroutine, so await it to get the actual result
                    result_or_iterator = await result
                else:
                    # It's either an async iterator or a direct result, use it directly
                    # This happens with sync implementations wrapped for async use
                    result_or_iterator = result

                if hasattr(result_or_iterator, "__aiter__"):
                    # If it's an async iterator (server streaming response), wrap it with our _iterate_results
                    return self._iterate_results(result_or_iterator)
                else:
                    # If it's not an iterator (unary response), just return the result directly
                    # The metrics will be recorded when this context manager exits
                    return result_or_iterator

            except ServiceAuthException as e:
                _auth_counter.labels(
                    metric_peer_identity, method_name, e.code().name
                ).inc()
                logging.warning(
                    "Request: %s from %s on %s: %s",
                    request_context,
                    peer_identities,
                    method_name,
                    e.code().name,
                )
                await context.abort(e.code(), e.details())


def get_auth_info_from_grpc_context(context: grpc.ServicerContext) -> AuthInfo:
    """
    Get the auth information (e.g.  tenant id) from the grpc context.

    The interceptor should have been used to intercept the request and add the tenant id to the metadata.
    We use the x-tenant-id as a way to propagate the information to the request handlers.
    The tenant id should not be forwarded to other services

    Args:
        context: The ServicerContext pass by gRPC to the service.

    Returns:
        The tenant name if set, None otherwise.
    """
    metadata = context.invocation_metadata()
    tenant_id = [m.value for m in metadata if m.key == "x-tenant-id"]
    tenant_name = [m.value for m in metadata if m.key == "x-tenant-name"]
    shard_namespace = [m.value for m in metadata if m.key == "x-shard-namespace"]
    cloud = [m.value for m in metadata if m.key == "x-cloud"]
    scopes = [m.value for m in metadata if m.key == "x-scopes"]
    user_id = [m.value for m in metadata if m.key == "x-user-id"]
    opaque_user_id = [m.value for m in metadata if m.key == "x-opaque-user-id"]
    opaque_user_id_type = [
        m.value for m in metadata if m.key == "x-opaque-user-id-type"
    ]
    user_email = [m.value for m in metadata if m.key == "x-user-email"]
    service_name = [m.value for m in metadata if m.key == "x-service-name"]
    assert tenant_name, "x-tenant-name should be set in the metadata"
    return AuthInfo(
        tenant_id=tenant_id[0] if tenant_id else None,
        tenant_name=tenant_name[0],
        scopes=scopes[0].split(",") if scopes else [],
        shard_namespace=shard_namespace[0],
        cloud=cloud[0],
        user_id=pydantic.SecretStr(user_id[0]) if user_id else None,
        opaque_user_id=opaque_user_id[0] if opaque_user_id else None,
        opaque_user_id_type=opaque_user_id_type[0] if opaque_user_id_type else None,
        user_email=pydantic.SecretStr(user_email[0]) if user_email else None,
        service_name=service_name[0] if service_name else None,
    )

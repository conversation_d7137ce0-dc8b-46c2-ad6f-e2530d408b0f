[package]
name = "grpc_auth"
version = "0.1.0"
edition = "2021"

[lib]
name = "grpc_auth"
path = "lib.rs"

[dependencies]
grpc_testing = { path = "../testing" }
hyper =  { workspace = true }
tokio =  { workspace = true }
tonic =  { workspace = true }
tonic-health =  { workspace = true }
tower =  { workspace = true }
request_context =  { path = "../../request_context" }
secrecy =  { workspace = true }
prometheus = { workspace = true }
lazy_static = { workspace = true }

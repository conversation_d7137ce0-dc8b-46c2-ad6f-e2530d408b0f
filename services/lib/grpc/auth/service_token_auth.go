package auth

import (
	"context"
	"errors"
	"sync"

	"github.com/MicahParks/jwkset"
	"github.com/MicahParks/keyfunc/v3"
	"github.com/augmentcode/augment/base/go/secretstring"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	jwt "github.com/golang-jwt/jwt/v5"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	successfulTokenValidations = prometheus.NewCounter(
		prometheus.CounterOpts{
			Name: "au_grpc_token_auth_successful_validations_total",
			Help: "Total number of successful token validations",
		},
	)
	failedTokenValidations = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_grpc_token_auth_failed_validations_total",
			Help: "Total number of failed token validations",
		},
		[]string{"reason"},
	)
)

func init() {
	prometheus.MustRegister(
		successfulTokenValidations,
		failedTokenValidations,
	)
}

// Auth implementation that uses a verification key from the token exchange service to validate
// tokens. Intended usage is with ServiceAuthInterceptor. For example:
//
//	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
//	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
type ServiceTokenAuth struct {
	// Used to request public keys.
	tokenExchangeClient tokenexchange.TokenExchangeClient

	// Cached verification key set returned by `tokenExchangeClient`.
	cachedKeyfunc keyfunc.Keyfunc

	// Used to protect `cachedKeyfunc`.
	keyfuncMutex sync.Mutex
}

// Constructor.
func NewServiceTokenAuth(tokenExchangeClient tokenexchange.TokenExchangeClient) *ServiceTokenAuth {
	return &ServiceTokenAuth{
		tokenExchangeClient: tokenExchangeClient,
		cachedKeyfunc:       nil,
		keyfuncMutex:        sync.Mutex{},
	}
}

// Validate the provided auth token against a verification key from the token exchange service. If
// the key is valid, return the claims from the token.
func (s *ServiceTokenAuth) ValidateAccess(
	ctx context.Context, authToken secretstring.SecretString,
) (*AugmentClaims, error) {
	if authToken.Expose() == "" {
		log.Error().Msgf("No auth token provided")
		failedTokenValidations.WithLabelValues("missing_token").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}

	// Try with the cached key first.
	token, err := func() (*jwt.Token, error) {
		s.keyfuncMutex.Lock()
		defer s.keyfuncMutex.Unlock()

		if s.cachedKeyfunc != nil {
			return s.verifyJwt(authToken, s.cachedKeyfunc)
		}

		return nil, nil
	}()
	if err == nil && token != nil {
		// We used the cached key successfully.
		successfulTokenValidations.Inc()
		return token.Claims.(*AugmentClaims), nil
	} else if err != nil && !errors.Is(err, jwkset.ErrKeyNotFound) {
		log.Error().Err(err).Msgf("Failed to verify service token: %v", err)
		failedTokenValidations.WithLabelValues("validation_failed").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	// We let ErrKeyNotFound errors fall through because they probably indicate that our cached key
	// is stale.

	// Fetch the verification key from token exchange service.
	log.Info().Msgf("Fetching verification key from token exchange")
	jwksData, err := s.tokenExchangeClient.GetVerificationKey(ctx)
	if err != nil {
		log.Error().Msgf("Failed to get verification key: %v", err)
		failedTokenValidations.WithLabelValues("key_fetch_error").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	keyfunc, err := keyfunc.NewJWKSetJSON(jwksData)
	if err != nil {
		log.Error().Msgf("Failed to parse JWKS: %v", err)
		failedTokenValidations.WithLabelValues("jwks_parse_error").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	log.Info().Msgf("Received JWKS: %v", keyfunc)

	// Update the cache and try with the new key.
	token, err = func() (*jwt.Token, error) {
		s.keyfuncMutex.Lock()
		defer s.keyfuncMutex.Unlock()

		s.cachedKeyfunc = keyfunc
		return s.verifyJwt(authToken, keyfunc)
	}()
	if err != nil {
		log.Error().Msgf("Failed to verify service token: %v", err)
		failedTokenValidations.WithLabelValues("validation_failed").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	successfulTokenValidations.Inc()
	return token.Claims.(*AugmentClaims), nil
}

// Validate the provided auth token against a verification key from the token exchange service. If
// the key is valid, return the claims from the token. If no token is provided, return nil, nil.
//
// This is useful for services that are sometimes called with a token and sometimes not.
// By default, use ValidateAccess instead.
func (s *ServiceTokenAuth) ValidateOptionalAccess(
	ctx context.Context, authToken secretstring.SecretString,
) (*AugmentClaims, error) {
	if authToken.Expose() == "" {
		// No token provided, so we can't validate it.
		return nil, nil
	}
	// if the token is set, it must be valid

	// Try with the cached key first.
	token, err := func() (*jwt.Token, error) {
		s.keyfuncMutex.Lock()
		defer s.keyfuncMutex.Unlock()

		if s.cachedKeyfunc != nil {
			return s.verifyJwt(authToken, s.cachedKeyfunc)
		}

		return nil, nil
	}()
	if err == nil && token != nil {
		// We used the cached key successfully.
		successfulTokenValidations.Inc()
		return token.Claims.(*AugmentClaims), nil
	} else if err != nil && !errors.Is(err, jwkset.ErrKeyNotFound) {
		log.Error().Err(err).Msgf("Failed to verify service token: %v", err)
		failedTokenValidations.WithLabelValues("validation_failed").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	// We let ErrKeyNotFound errors fall through because they probably indicate that our cached key
	// is stale.

	// Fetch the verification key from token exchange service.
	log.Info().Msgf("Fetching verification key from token exchange")
	jwksData, err := s.tokenExchangeClient.GetVerificationKey(ctx)
	if err != nil {
		log.Error().Msgf("Failed to get verification key: %v", err)
		failedTokenValidations.WithLabelValues("key_fetch_error").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	keyfunc, err := keyfunc.NewJWKSetJSON(jwksData)
	if err != nil {
		log.Error().Msgf("Failed to parse JWKS: %v", err)
		failedTokenValidations.WithLabelValues("jwks_parse_error").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	log.Info().Msgf("Received JWKS: %v", keyfunc)

	// Update the cache and try with the new key.
	token, err = func() (*jwt.Token, error) {
		s.keyfuncMutex.Lock()
		defer s.keyfuncMutex.Unlock()

		s.cachedKeyfunc = keyfunc
		return s.verifyJwt(authToken, keyfunc)
	}()
	if err != nil {
		log.Error().Msgf("Failed to verify service token: %v", err)
		failedTokenValidations.WithLabelValues("validation_failed").Inc()
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	successfulTokenValidations.Inc()
	return token.Claims.(*AugmentClaims), nil
}

// Thin wrapper around calling jwt.ParseWithClaims for the given token and Keyfunc. This will fail
// if the token can't be parsed with AugmentClaims.
func (s *ServiceTokenAuth) verifyJwt(
	authToken secretstring.SecretString, keyfunc keyfunc.Keyfunc,
) (*jwt.Token, error) {
	return jwt.ParseWithClaims(authToken.Expose(), &AugmentClaims{}, func(token *jwt.Token) (any, error) {
		return keyfunc.Keyfunc(token)
	})
}

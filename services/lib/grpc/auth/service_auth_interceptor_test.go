package auth

import (
	"context"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

// Implementation of ValidateAccess that denies all requests.
func denyAll(_ context.Context, _ secretstring.SecretString) (*AugmentClaims, error) {
	return nil, status.Error(codes.PermissionDenied, "Access denied")
}

// Implementation of ValidateAccess that allows all requests.
func allowAll(_ context.Context, authToken secretstring.SecretString) (*AugmentClaims, error) {
	return &AugmentClaims{}, nil
}

func grpcContext() context.Context {
	requestContext := requestcontext.New(
		"request-id", "request-session-id", "request-source", secretstring.New("auth-token"),
	)
	return metadata.NewIncomingContext(context.Background(), requestContext.ToMetadata())
}

func unaryServerInfo() *grpc.UnaryServerInfo {
	return &grpc.UnaryServerInfo{
		FullMethod: "/test.TestService/TestMethod",
	}
}

func unaryHandler() grpc.UnaryHandler {
	return func(ctx context.Context, req interface{}) (interface{}, error) {
		return nil, nil
	}
}

func TestIntercept(t *testing.T) {
	t.Run("Allow", func(t *testing.T) {
		interceptor := NewAuthServerInterceptor(allowAll)
		_, err := interceptor.Intercept(grpcContext(), nil, unaryServerInfo(), unaryHandler())
		if err != nil {
			t.Error("Request should be allowed")
		}
	})

	t.Run("Deny", func(t *testing.T) {
		interceptor := NewAuthServerInterceptor(denyAll)
		_, err := interceptor.Intercept(grpcContext(), nil, unaryServerInfo(), unaryHandler())
		if err == nil || status.Code(err) != codes.PermissionDenied {
			t.Error("Request should return a permission denied error")
		}
	})

	t.Run("Skip", func(t *testing.T) {
		interceptor := NewAuthServerInterceptor(denyAll)
		serverInfo := &grpc.UnaryServerInfo{
			FullMethod: "/grpc.health.v1.Health/Check",
		}
		_, err := interceptor.Intercept(context.Background(), nil, serverInfo, unaryHandler())
		if err != nil {
			t.Error("Auth should be skipped")
		}
	})

	t.Run("Missing metadata", func(t *testing.T) {
		interceptor := NewAuthServerInterceptor(allowAll)
		_, err := interceptor.Intercept(context.Background(), nil, unaryServerInfo(), unaryHandler())
		if err == nil || status.Code(err) != codes.InvalidArgument {
			t.Error("Request should return an invalid argument error")
		}
	})
}

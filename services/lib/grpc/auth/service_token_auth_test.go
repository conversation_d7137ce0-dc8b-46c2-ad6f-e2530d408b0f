package auth

import (
	"context"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func mockTokenExchangeClient() *tokenexchange.MockTokenExchangeClient {
	// This token is hardcoded to have {
	//     user_id: "123",
	//     tenant_id: "dev-augie",
	//     shard_namespace: "unit_test_shard",
	//     cloud: "unit_test_cloud",
	// }
	signedTokenPath := "test_data/example.jwt"
	publicJwksPath := "test_data/example.jwks"
	return tokenexchange.NewMockTokenExchangeClient(signedTokenPath, publicJwksPath)
}

func mockTokenExchangeClientOther() *tokenexchange.MockTokenExchangeClient {
	signedTokenPath := "test_data/example-new-kid.jwt"
	publicJwksPath := "test_data/example.jwks"
	return tokenexchange.NewMockTokenExchangeClient(signedTokenPath, publicJwksPath)
}

func TestValidateAccess(t *testing.T) {
	tokenExchangeClient := mockTokenExchangeClient()
	serviceTokenAuth := NewServiceTokenAuth(tokenExchangeClient)
	opaqueUserID := &authentitiesproto.UserId{
		UserId:     "123",
		UserIdType: authentitiesproto.UserId_AUGMENT,
	}
	userEmail := "<EMAIL>"
	token, err := tokenExchangeClient.GetSignedTokenForUser(context.Background(), "123", opaqueUserID, &userEmail, "dev-augie", nil, nil)
	if err != nil {
		t.Fatalf("Failed to get signed token: %v", err)
	}

	t.Run("valid token", func(t *testing.T) {
		claims, err := serviceTokenAuth.ValidateAccess(context.Background(), token)
		if err != nil {
			t.Fatalf("Failed to validate access: %v", err)
		}
		if claims.TenantID != "dev-augie" {
			t.Fatalf("unexpected tenant ID: %v", claims.TenantID)
		}
		if claims.ShardNamespace != "unit_test_shard" {
			t.Fatalf("unexpected shard namespace: %v", claims.ShardNamespace)
		}
		if claims.UserID != "123" {
			t.Fatalf("unexpected user ID: %v", claims.UserID)
		}
	})

	t.Run("no token", func(t *testing.T) {
		_, err := serviceTokenAuth.ValidateAccess(context.Background(), secretstring.SecretString{})
		if err == nil || status.Code(err) != codes.Unauthenticated {
			t.Fatalf("Expected permission denied error for no token")
		}
	})

	t.Run("invalid token", func(t *testing.T) {
		// Create a corrupt token.
		var corruptToken string
		exposedToken := token.Expose()
		if exposedToken[13] == '1' {
			corruptToken = exposedToken[:13] + "0" + exposedToken[14:]
		} else {
			corruptToken = exposedToken[:13] + "1" + exposedToken[14:]
		}

		_, err = serviceTokenAuth.ValidateAccess(context.Background(),
			secretstring.New(corruptToken))
		if err == nil || status.Code(err) != codes.Unauthenticated {
			t.Fatalf("Expected unauthenticated error for invalid token")
		}
	})
}

func TestValidateAccess_Cache(t *testing.T) {
	tokenExchangeClient := mockTokenExchangeClient()
	serviceTokenAuth := NewServiceTokenAuth(tokenExchangeClient)
	opaqueUserID := &authentitiesproto.UserId{
		UserId:     "123",
		UserIdType: authentitiesproto.UserId_AUGMENT,
	}
	userEmail := "<EMAIL>"
	token, err := tokenExchangeClient.GetSignedTokenForUser(context.Background(), "123", opaqueUserID, &userEmail, "dev-augie", nil, nil)
	if err != nil {
		t.Fatalf("Failed to get signed token: %v", err)
	}

	// First request should require a request to token exchange.
	_, err = serviceTokenAuth.ValidateAccess(context.Background(), token)
	if err != nil {
		t.Fatalf("Failed to validate access: %v", err)
	}
	if tokenExchangeClient.GetVerificationKeyCount != 1 {
		t.Fatalf("Expected one verification key request")
	}

	// Second request should use the cached key.
	_, err = serviceTokenAuth.ValidateAccess(context.Background(), token)
	if err != nil {
		t.Fatalf("Failed to validate access: %v", err)
	}
	if tokenExchangeClient.GetVerificationKeyCount != 1 {
		t.Fatalf("Expected no new verification key requests")
	}

	tokenNewKid, err := mockTokenExchangeClientOther().GetSignedTokenForUser(context.Background(), "123", opaqueUserID, &userEmail, "dev-augie", nil, nil)
	if err != nil {
		t.Fatalf("Failed to get signed token: %v", err)
	}
	_, err = serviceTokenAuth.ValidateAccess(context.Background(), tokenNewKid)
	if err == nil {
		t.Fatalf("Failed to validate access: %v", err)
	}
	if tokenExchangeClient.GetVerificationKeyCount != 2 {
		t.Fatalf("Expected one new verification key request")
	}
}

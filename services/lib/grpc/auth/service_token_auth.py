import base64
import json
import logging
import typing

import grpc
import jwt
import pydantic
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric.ec import (
    SECP256R1,
    EllipticCurvePublicNumbers,
)
from prometheus_client import Counter

from services.lib.grpc.auth.service_auth import (
    AuthInfo,
    ServiceAuth,
    ServiceAuthException,
)
from services.token_exchange.client.client import TokenExchangeClient

_successful_token_validations = Counter(
    "au_grpc_token_auth_successful_validations_total",
    "Total number of successful token validations",
)
_failed_token_validations = Counter(
    "au_grpc_token_auth_failed_validations_total",
    "Total number of failed token validations",
    ["reason"],
)


class PublicKeySource(typing.Protocol):
    """Interface for a public key source."""

    def get_public_key(self, kid: str) -> str | None:
        """Returns the public key."""
        raise NotImplementedError()


def _base64url_decode(value: str) -> bytes:
    """
    Decodes a base64-url encoded string.

    Simpified version of https://github.com/jpadilla/pyjwt/blob/7b4bc844b9d4c38a8dbba1e727f963611124dd5b/jwt/utils.py#L25
    with is under MIT License. The library is already a dependency, but not in the correct version.

    Args:
        value: the base64-url encoded string.

    Returns:
        The decoded bytes.
    """
    input_bytes = value.encode("utf-8")

    rem = len(input_bytes) % 4

    if rem > 0:
        input_bytes += b"=" * (4 - rem)

    return base64.urlsafe_b64decode(input_bytes)


class GrpcPublicKeySource(PublicKeySource):
    """Public key source that uses the token exchange service."""

    def __init__(self, token_client: TokenExchangeClient):
        self.token_client = token_client
        self._public_key: dict[str, str | None] = {}

    def _decode_jwk(self, jwk: dict) -> str | None:
        # TODO(aswin): As of 4/29/24, The latest release of pyjwt does not
        # support JWKS, we could wait for a release that does (the code is
        # already there) or switch to another library
        if jwk["kty"] != "EC":
            return None
        if jwk.get("crv", "P-256") != "P-256":
            return None
        x = _base64url_decode(jwk.get("x"))  # type: ignore
        y = _base64url_decode(jwk.get("y"))  # type: ignore
        curve_obj = SECP256R1()
        public_numbers = EllipticCurvePublicNumbers(
            x=int.from_bytes(x, byteorder="big"),
            y=int.from_bytes(y, byteorder="big"),
            curve=curve_obj,
        )
        return (
            public_numbers.public_key()
            .public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo,
            )
            .decode("utf-8")
        )

    def get_public_key(self, kid: str) -> str | None:
        # Try to use the cached public key
        if kid in self._public_key:
            return self._public_key[kid]

        logging.info("Fetching new public key for kid=%s", kid)
        jwks_data = self.token_client.get_verification_key()
        jwks = json.loads(jwks_data)
        logging.info("Received JWKS: %s", jwks)
        # Clear any existing kid entries for the public key; we only want the new ones
        self._public_key = {}

        for jwk in jwks.get("keys", []):
            decoded_key = self._decode_jwk(jwk)
            if decoded_key is None:
                logging.error("error decoding key kid=%s", jwk.get("kid"))
            # Intentionally create a kid -> None mapping if it failed to decode, since refetching
            # the key is unlikely to help. We'll just serve the None from the cache next time.
            self._public_key[jwk["kid"]] = decoded_key

        # This is allowed to be None if the most freshly fetched key doesn't contain our kid.
        return self._public_key.get(kid)

    def __str__(self) -> str:
        return f"GrpcPublicKeySource(token_client={self.token_client})"

    def __repr__(self) -> str:
        return str(self)


class ServiceTokenAuth(ServiceAuth):
    """ServiceAuth implementation that uses the auth token to validate access."""

    def __init__(
        self,
        public_key_source: PublicKeySource,
        required_scopes: list[str],
    ):
        """
        Constructor.

        Args:
            public_key_source: The PublicKeySource to use.
        """
        self.public_key_source = public_key_source
        self.required_scopes = required_scopes
        logging.info(
            "Setting up ServiceTokenAuth with public_key_source=%s required_scopes=%s",
            public_key_source,
            required_scopes,
        )

    def _decoded_token_to_auth_info(self, decoded_token: dict) -> AuthInfo:
        # Source of truth for decoded token: lib/grpc/auth/claims.go
        return AuthInfo(
            tenant_id=decoded_token["tenant_id"],
            tenant_name=decoded_token.get("tenant_name", ""),
            scopes=decoded_token.get("scope", []),
            shard_namespace=decoded_token["shard_namespace"],
            cloud=decoded_token["cloud"],
            user_id=pydantic.SecretStr(decoded_token.get("user_id", "")),
            opaque_user_id=decoded_token.get("opaque_user_id", ""),
            opaque_user_id_type=decoded_token.get("opaque_user_id_type"),
            user_email=pydantic.SecretStr(decoded_token.get("user_email", "")),
            service_name=decoded_token.get("service_name"),
        )

    def _check(self, auth_token: pydantic.SecretStr) -> AuthInfo:
        try:
            unverified_header = jwt.get_unverified_header(auth_token.get_secret_value())
        except jwt.exceptions.PyJWTError as e:
            logging.warning("Failed to read service token")
            _failed_token_validations.labels(reason="invalid_token").inc()
            raise ServiceAuthException(
                grpc.StatusCode.UNAUTHENTICATED, "Unauthenticated"
            ) from e

        key_id = unverified_header.get("kid")
        if not key_id:
            logging.info("Failed to verify service token: No kid")
            _failed_token_validations.labels(reason="invalid_token").inc()
            raise ServiceAuthException(
                grpc.StatusCode.UNAUTHENTICATED, "Unauthenticated"
            )
        public_key = self.public_key_source.get_public_key(key_id)
        if not public_key:
            logging.info("Failed to verify service token: Failed to find kid")
            _failed_token_validations.labels(reason="unknown_kid").inc()
            raise ServiceAuthException(
                grpc.StatusCode.UNAUTHENTICATED, "Unauthenticated"
            )

        try:
            decoded_token = jwt.decode(
                auth_token.get_secret_value(),
                key=public_key,
                algorithms=["ES256"],
                audience="",
                options={"verify_aud": False},
            )
            logging.debug("Verified service token: %s", decoded_token)
            auth_info = self._decoded_token_to_auth_info(decoded_token)
            if self.required_scopes:
                if not set(self.required_scopes).issubset(set(auth_info.scopes)):
                    _failed_token_validations.labels(reason="insufficient_scopes").inc()
                    raise ServiceAuthException(
                        grpc.StatusCode.PERMISSION_DENIED, "Access denied"
                    )

            _successful_token_validations.inc()
            return auth_info
        except jwt.exceptions.PyJWTError as e:
            logging.warning("Failed to verify service token: %s", e)
            _failed_token_validations.labels(reason="validation_failed").inc()
            raise ServiceAuthException(
                grpc.StatusCode.UNAUTHENTICATED, "Unauthenticated"
            ) from e

    def validate_access(
        self,
        peer_identities: list[bytes] | None,
        auth_token: pydantic.SecretStr | None,
        method_name: str,
    ) -> AuthInfo:
        """Validates access to a tenant."""
        del peer_identities, method_name
        if not auth_token:
            raise ServiceAuthException(
                grpc.StatusCode.UNAUTHENTICATED, "Unauthenticated"
            )

        return self._check(auth_token)

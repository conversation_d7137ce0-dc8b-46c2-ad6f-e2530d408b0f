use ginepro::{LoadBalancedChannel, ServiceDefinition};
use std::time::{Duration, Instant};
use tokio::sync::mpsc;
use tonic::transport::{Channel, ClientTlsConfig, Uri};
use tower::ServiceBuilder;
use tracing_tonic::client::TracingService;

pub async fn create_channel(
    endpoint: String,
    request_timeout: Option<Duration>,
    tls_config: &Option<ClientTlsConfig>,
) -> Result<TracingService, tonic::transport::Error> {
    let endpoint = if tls_config.is_some() {
        format!("https://{}", endpoint)
    } else {
        format!("http://{}", endpoint)
    };
    tracing::info!(
        "Creating grpc client: endpoint={endpoint} tls_config={:?}",
        tls_config.is_some()
    );

    if endpoint.contains("headless") {
        tracing::info!("Creating load balanced channel for endpoint {}", endpoint);
        let endpoint: Uri = endpoint.parse().expect("Should be able to parse endpoint");
        let service_definition = ServiceDefinition::from_parts(
            endpoint
                .host()
                .expect("Endpoint should have a hostname")
                .to_string(),
            endpoint.port_u16().expect("Endpoint should have a port"),
        )
        .expect("Endpoint hostname should be a valid domain name");

        let builder = LoadBalancedChannel::builder(service_definition)
            .connect_timeout(Duration::from_secs(10))
            .dns_probe_interval(std::time::Duration::from_secs(5));
        let builder = match &tls_config {
            None => builder,
            Some(tls_config) => builder.with_tls(tls_config.clone()),
        };
        let builder = match request_timeout {
            None => builder,
            Some(timeout) => builder.timeout(timeout),
        };

        let inner_channel = builder
            .channel()
            .await
            // TODO: error conversion?
            .expect("Failed to create load balanced channel");

        let channel: TracingService = ServiceBuilder::new()
            .layer_fn(TracingService::new_load_balanced)
            .service(inner_channel);
        tracing::info!("Created load balanced channel for endpoint {}", endpoint);

        Ok(channel)
    } else {
        tracing::info!("Creating channel for endpoint {}", endpoint);
        let endpoint = Channel::from_shared(endpoint)
            .expect("Should be able to parse endpoint")
            .connect_timeout(Duration::from_secs(10))
            .tcp_keepalive(Some(Duration::from_secs(15)))
            .http2_keep_alive_interval(Duration::from_secs(5 * 60))
            .keep_alive_timeout(Duration::from_secs(15));
        let endpoint = match &tls_config {
            None => endpoint,
            Some(tls_config) => endpoint.tls_config(tls_config.clone())?,
        };
        let endpoint = match request_timeout {
            None => endpoint,
            Some(timeout) => endpoint.timeout(timeout),
        };
        let inner_channel = endpoint.connect().await?;

        let channel: TracingService = ServiceBuilder::new()
            .layer_fn(TracingService::new)
            .service(inner_channel);
        tracing::info!(
            "Created load balanced channel for endpoint {}",
            endpoint.uri()
        );
        Ok(channel)
    }
}

// Helper function for writing clients: produce a tokio mpsc receiver that just yields a single tonic error
pub fn receiver_for_error<T: Send + 'static>(
    error: tonic::Status,
) -> mpsc::Receiver<tonic::Result<T>> {
    let (tx, rx) = mpsc::channel::<tonic::Result<T>>(1);
    tokio::spawn(async move {
        if let Err(send_err) = tx.send(Err(error)).await {
            if !tx.is_closed() {
                tracing::error!("Failed to send error response: {:?}", send_err);
            }
        }
    });
    rx
}

// Helper function for writing clients: convert a tonic streaming response into a tokio mpsc receiver
// We mostly use this pattern for easier mocking of streams since tonic::Streaming is opaque
pub fn receiver_for_stream<T: Send + 'static>(
    response: tonic::Response<tonic::Streaming<T>>,
    deadline: Option<Instant>,
) -> mpsc::Receiver<tonic::Result<T>> {
    let mut stream = response.into_inner();
    let (tx, rx) = mpsc::channel::<tonic::Result<T>>(4);
    tokio::spawn(async move {
        tracing::info!("receiver_for_stream spawned");
        loop {
            if deadline.map_or(false, |d| Instant::now() > d) {
                tracing::info!("deadline reached");
                return;
            }
            let message = stream.message().await;
            match message {
                Ok(Some(msg)) => {
                    if let Err(send_err) = tx.send(Ok(msg)).await {
                        if !tx.is_closed() {
                            tracing::error!("Failed to send message: {:?}", send_err);
                        }
                        return;
                    }
                }
                Ok(None) => {
                    tracing::info!("Received EOS");
                    return; // EOS case
                }
                Err(e) => {
                    tracing::error!("Failed to receive message: {:?}", e);
                    if let Err(send_err) = tx.send(Err(e)).await {
                        if !tx.is_closed() {
                            tracing::error!("Failed to send error response: {:?}", send_err);
                        }
                    }
                    return;
                }
            }
        }
    });
    rx
}

#[cfg(test)]
mod tests {
    use super::create_channel;

    #[actix_rt::test]
    async fn test_basic() {
        let res = create_channel("localhost:1234".to_string(), None, &None).await;
        // In unit test environment we can't actually connect. That's fine-- this test is just for
        // easy coverage of library compilation and checking that we don't hit the expect()s.
        assert!(res.is_err());
    }
}

[package]
name = "grpc_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "grpc_client"
path = "lib.rs"

[dependencies]
ginepro = { workspace = true }
hyper = { workspace = true }
prometheus = { workspace = true }
request_context = { path = "../../request_context" }
tonic = { workspace = true }
tokio-metrics-collector = { workspace = true }
tokio = { workspace = true }
tower = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../../base/rust/tracing-tonic" }

[dev-dependencies]
actix-rt =  { workspace = true }

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")

rust_library(
    name = "grpc_client_rs",
    srcs = ["lib.rs"],
    aliases = aliases(),
    crate_name = "grpc_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//base/rust/tracing-tonic",
        "//services/lib/request_context:request_context_rs",
    ],
)

rust_test(
    name = "grpc_client_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":grpc_client_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

package grpcservice

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/peer"
	"google.golang.org/grpc/status"
)

func TestPeerNameAndNamespace_Valid(t *testing.T) {
	ctx := MockContextWithPeerNames(t, context.Background(), []string{"api-proxy-svc", "api-proxy-headless-svc-2", "api-proxy-svc.shard1", "api-proxy-headless-svc.shard1"})

	peerName, namespace, err := GetAnyPeerNameAndNamespace(ctx)
	assert.NoError(t, err)
	assert.Equal(t, "shard1", namespace)
	assert.Equal(t, "api-proxy-svc", peerName)

	peerNames, namespaces, err := GetAllPeerNamesAndNamespaces(ctx)
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"api-proxy-svc", "api-proxy-headless-svc"}, peerNames)
	assert.ElementsMatch(t, []string{"shard1", "shard1"}, namespaces)
}

func TestPeerNameAndNamespace_MissingAuthInfo(t *testing.T) {
	ctx := context.Background()
	ctx = peer.NewContext(ctx, &peer.Peer{
		AuthInfo: nil,
	})

	_, _, err := GetAnyPeerNameAndNamespace(ctx)
	assert.Error(t, err)
	assert.Equal(t, codes.PermissionDenied, status.Code(err))

	_, _, err = GetAllPeerNamesAndNamespaces(ctx)
	assert.Error(t, err)
	assert.Equal(t, codes.PermissionDenied, status.Code(err))
}

func TestPeerNameAndNamespace_MissingSuffix(t *testing.T) {
	ctx := MockContextWithPeerNames(t, context.Background(), []string{"api-proxy-svc", "api-proxy-headless-svc"})

	peerName, namespace, err := GetAnyPeerNameAndNamespace(ctx)
	assert.NoError(t, err)
	assert.Equal(t, "", namespace)
	assert.Equal(t, "", peerName)

	peerNames, namespaces, err := GetAllPeerNamesAndNamespaces(ctx)
	assert.NoError(t, err)
	assert.Empty(t, peerNames)
	assert.Empty(t, namespaces)
}

package grpcservice

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"errors"
	"strings"
	"testing"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/peer"
	"google.golang.org/grpc/status"
)

func dnsNameSplit(dnsName string) []string {
	return strings.Split(dnsName, ".")
}

// GetAnyPeerNameAndNamespace returns any namespace and peer name of the gRPC
// caller from the tls info in the context, or empty strings if not found. If
// there are multiple DNS names, the first one with a peer and namespace
// (exactly 2 DNS parts) is returned.
func GetAnyPeerNameAndNamespace(ctx context.Context) (peerName string, namespace string, err error) {
	clientInfo, ok := peer.FromContext(ctx)
	if !ok {
		return "", "", errors.New("unable to get peer info")
	}

	tlsInfo, ok := clientInfo.AuthInfo.(credentials.TLSInfo)
	if !ok {
		return "", "", status.Error(codes.PermissionDenied, "unable to get peer credentials")
	}

	// Get the peer certificates
	for _, cert := range tlsInfo.State.PeerCertificates {
		for _, dnsName := range cert.DNSNames {
			// split the dns name into parts
			parts := dnsNameSplit(dnsName)
			if len(parts) == 2 {
				return parts[0], parts[1], nil
			}
		}
	}
	return "", "", nil
}

// GetAllPeerNamesAndNamespaces returns all the peer names and namespaces of the
// gRPC caller from the tls info in the context, or nil if not found.
func GetAllPeerNamesAndNamespaces(ctx context.Context) (peerNames []string, namespaces []string, err error) {
	clientInfo, ok := peer.FromContext(ctx)
	if !ok {
		return nil, nil, errors.New("unable to get peer info")
	}

	tlsInfo, ok := clientInfo.AuthInfo.(credentials.TLSInfo)
	if !ok {
		return nil, nil, status.Error(codes.PermissionDenied, "unable to get peer credentials")
	}

	// Get the peer certificates
	for _, cert := range tlsInfo.State.PeerCertificates {
		for _, dnsName := range cert.DNSNames {
			// split the dns name into parts
			parts := dnsNameSplit(dnsName)
			if len(parts) == 2 {
				peerNames = append(peerNames, parts[0])
				namespaces = append(namespaces, parts[1])
			}
		}
	}
	return peerNames, namespaces, nil
}

// For use in tests
func MockContextWithPeerNames(t *testing.T, ctx context.Context, dnsNames []string) context.Context {
	return peer.NewContext(ctx, &peer.Peer{
		AuthInfo: credentials.TLSInfo{
			State: tls.ConnectionState{
				PeerCertificates: []*x509.Certificate{
					{
						DNSNames: dnsNames,
					},
				},
			},
		},
	})
}

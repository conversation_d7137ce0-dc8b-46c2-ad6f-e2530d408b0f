load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:go.bzl", "go_library", "go_test")
load("//tools/bzl:rust.bzl", "rust_library")

rust_library(
    name = "grpc_service",
    srcs = ["lib.rs"],
    aliases = aliases(),
    crate_name = "grpc_service",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = [
        "//services:__subpackages__",
    ],
    deps = all_crate_deps(
        normal = True,
    ),
)

go_library(
    name = "grpc_service_go",
    srcs = ["peer_name.go"],
    importpath = "github.com/augmentcode/augment/services/lib/grpc/service",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//peer",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "grpc_service_go_test",
    srcs = ["peer_name_test.go"],
    embed = [":grpc_service_go"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)

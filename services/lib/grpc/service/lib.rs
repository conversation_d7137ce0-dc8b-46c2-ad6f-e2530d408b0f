use futures::Future;
use tokio::sync::mpsc::Sender;

// log an rpc response either as debug or error depending on the status
//
// Example:
// async fn get_request(
//    &self,
//    request: Request<GetRequestRequest>,
// ) -> Result<Response<GetRequestResponse>, Status> {
//    let request_context = RequestContext::try_from(request.metadata())?;
//    let tenant_info = tenant_info_from_grpc_req(&request)
//        .ok_or_else(|| tonic::Status::internal("tenant_info must be set"))?;
//
//    let span =
//        tracing::info_span!("upload blob content", request_id = %request_context.request_id());
//
//    log_response_fn(
//        || async {
//            tenant_info.validate_scope(TokenScope::ContentRw)?;
//            let inner_request = request.into_inner();
//            ...
//            let reply = GetRequestResponse {
//            };
//            Ok(Response::new(reply))
//        },
//        "upload_blob_content",
//    )
//    .instrument(span)
//    .await
//    }
pub async fn log_response_fn<ResponseT, F, Fut>(
    f: F,
    callsite: &'static str,
) -> Result<ResponseT, tonic::Status>
where
    ResponseT: std::fmt::Debug,
    F: FnOnce() -> Fut,
    Fut: Future<Output = Result<ResponseT, tonic::Status>>,
{
    let resp = f().await;
    log_response::<ResponseT>(resp, callsite)
}

fn log_response<ResponseT>(
    resp: Result<ResponseT, tonic::Status>,
    callsite: &'static str,
) -> Result<ResponseT, tonic::Status>
where
    ResponseT: std::fmt::Debug,
{
    match &resp {
        Err(status) if status.code() == tonic::Code::Cancelled => {
            tracing::warn!("response at {}: cancelled {:?}", callsite, status);
        }
        Err(status) if status.code() == tonic::Code::NotFound => {
            tracing::info!("response at {}: not_found {:?}", callsite, status);
        }
        Err(status) => {
            tracing::error!("response at {}: err {:?}", callsite, status);
        }
        Ok(resp) => {
            tracing::debug!("response at {}: {:?}", callsite, resp);
        }
    }
    resp
}

// log an rpc response status as error if not OK
pub fn log_response_status(status: &tonic::Status, callsite: &'static str) {
    if status.code() != tonic::Code::Ok {
        tracing::error!("response at {}: {:?}", callsite, status);
    }
}

// sends a message to a channel and ignores errors
//
// Example
//
//   if !send_and_ignore(&tx, Ok(resp), "search_chunks_stream missing response").await {
//       return Err(Status::internal("failed to send missing blobs response"));
//   };
pub async fn send_and_ignore<T>(tx: &Sender<T>, msg: T, sendsite: &str) -> bool {
    if let Err(send_error) = tx.send(msg).await {
        if tx.is_closed() {
            tracing::debug!("Channel closed in {}", sendsite);
        } else {
            tracing::info!(
                "Failed to send notification in {}: {}",
                sendsite,
                send_error
            );
        }
        false
    } else {
        true
    }
}

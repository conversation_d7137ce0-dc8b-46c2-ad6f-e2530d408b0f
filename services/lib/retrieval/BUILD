load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "retriever_request_insight_builder",
    srcs = [
        "retriever_request_insight_builder.py",
    ],
    visibility = [
        "//services/lib/retrieval:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/prompt_format_chat",
        "//base/tokenizers",
        "//services/inference_host/client",
        "//services/lib/retrieval:retriever",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/publisher:publisher_py",
        requirement("opentelemetry-api"),
        requirement("prometheus-client"),
    ],
)

py_library(
    name = "reranker",
    srcs = [
        "reranker.py",
    ],
    deps = [
        ":dense_retriever",
        ":retriever",
        ":retriever_request_insight_builder",
        "//base/prompt_format_rerank",
        "//services/content_manager/client",
        "//services/content_manager/client:content_cache",
        "//services/lib/grpc/auth:service_auth",
        "//services/request_insight:request_insight_py_proto",
        "//services/reranker/client",
        requirement("opentelemetry-api"),
        requirement("numpy"),
    ],
)

pytest_test(
    name = "reranker_test",
    srcs = ["reranker_test.py"],
    deps = [
        ":reranker",
    ],
)

py_library(
    name = "retriever",
    srcs = [
        "retriever.py",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names/python:blob_names",
        "//base/logging:struct_logging",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format:recency_info",
        "//base/prompt_format_completion",
        "//base/prompt_format_retrieve",
        "//base/python/au_itertools:reusable_iterable",
        "//services/completion_host:completion_proto_py_proto",
        "//services/content_manager/client:content_cache",
        "//services/embeddings_indexer:chunk_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
    ],
)

pytest_test(
    name = "retriever_test",
    size = "small",
    srcs = ["retriever_test.py"],
    deps = [
        ":retriever",
        "//base/prompt_format:recency_info",
        "//services/completion_host:completion_proto_py_proto",
    ],
)

py_library(
    name = "dummy_executor",
    srcs = [
        "dummy_executor.py",
    ],
    deps = [
        "//base/logging:struct_logging",
    ],
)

py_library(
    name = "null_retriever",
    srcs = [
        "null_retriever.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":retriever",
    ],
)

py_library(
    name = "recency_retriever",
    srcs = [
        "recency_retriever.py",
    ],
    deps = [
        requirement("unidiff"),
        ":dense_retriever",
        ":retriever",
        "//base/logging:struct_logging",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format_completion:overlap",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "recency_retriever_test",
    size = "small",
    srcs = ["recency_retriever_test.py"],
    data = [
        "__snapshots__/recency_retriever_test.ambr",
        "test_resources/diff_parser/sanity.diff",
    ],
    deps = [
        requirement("syrupy"),
        ":recency_retriever",
    ],
)

py_library(
    name = "user_guided_retriever",
    srcs = [
        "user_guided_retriever.py",
    ],
    deps = [
        ":dense_retriever",
        ":retriever",
        "//base/logging:struct_logging",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format_completion",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "user_guided_retriever_test",
    size = "small",
    srcs = ["user_guided_retriever_test.py"],
    deps = [
        ":user_guided_retriever",
    ],
)

py_library(
    name = "retrieval_collector",
    srcs = [
        "retrieval_collector.py",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":retriever",
        "//base/logging:struct_logging",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format_completion:overlap",
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "retrieval_collector_test",
    size = "small",
    srcs = ["retrieval_collector_test.py"],
    deps = [
        ":null_retriever",
        ":retrieval_collector",
    ],
)

py_library(
    name = "multi_retriever",
    srcs = [
        "multi_retriever.py",
    ],
    deps = [
        ":dummy_executor",
        ":retriever",
    ],
)

pytest_test(
    name = "multi_retriever_test",
    size = "small",
    srcs = ["multi_retriever_test.py"],
    deps = [
        ":multi_retriever",
        ":null_retriever",
    ],
)

py_library(
    name = "docset_retriever",
    srcs = [
        "docset_retriever.py",
    ],
    deps = [
        ":dense_retriever",
        ":retriever",
        "//base/logging:struct_logging",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format_completion",
        "//base/tokenizers",
    ],
)

pytest_test(
    name = "docset_retriever_test",
    size = "small",
    srcs = ["docset_retriever_test.py"],
    deps = [
        ":docset_retriever",
    ],
)

py_library(
    name = "router",
    srcs = [
        "router.py",
    ],
    deps = [
        ":dense_retriever",
        ":retriever",
        "//base/logging:struct_logging",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format_completion",
        "//base/prompt_format_router",
        "//base/tokenizers",
        "//services/inference_host/client",
    ],
)

pytest_test(
    name = "router_test",
    size = "small",
    srcs = ["router_test.py"],
    deps = [
        ":router",
    ],
)

py_library(
    name = "dense_retriever",
    srcs = [
        "dense_retriever.py",
    ],
    deps = [
        ":dummy_executor",
        ":retriever",
        ":retriever_request_insight_builder",
        "//base/caching:cache_metrics",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//base/prompt_format:chunk_origin",
        "//base/prompt_format_retrieve",
        "//base/tokenizers",
        "//services/content_manager/client",
        "//services/content_manager/client:content_cache",
        "//services/embedder_host/client",
        "//services/embeddings_indexer:chunk_py_proto",
        "//services/embeddings_search_host/client",
        "//services/integrations/docset/client:client_py",
        "//services/request_insight:request_insight_py_proto",
    ],
)

pytest_test(
    name = "dense_retriever_test",
    srcs = ["dense_retriever_test.py"],
    deps = [
        ":dense_retriever",
    ],
)

py_library(
    name = "diff_retriever",
    srcs = [
        "diff_retriever.py",
    ],
    deps = [
        ":dense_retriever",
        ":retriever",
        "//base/datasets:recency_info_conversion",
        "//base/diff_utils",
        "//base/prompt_format:chunk_origin",
        "//services/lib/file_retriever",
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "diff_retriever_test",
    size = "small",
    srcs = ["diff_retriever_test.py"],
    deps = [
        ":diff_retriever",
    ],
)

py_library(
    name = "retriever_factory",
    srcs = [
        "retriever_factory.py",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        ":dense_retriever",
        ":diff_retriever",
        ":docset_retriever",
        ":multi_retriever",
        ":null_retriever",
        ":recency_retriever",
        ":reranker",
        ":retriever",
        ":retriever_request_insight_builder",
        ":router",
        ":user_guided_retriever",
        "//base/caching:cache_metrics",
        "//base/prompt_format_completion",
        "//base/prompt_format_rerank",
        "//base/prompt_format_retrieve",
        "//base/prompt_format_router",
        "//base/python/grpc:client_options",
        "//base/tokenizers",
        "//services/content_manager/client",
        "//services/content_manager/client:content_cache",
        "//services/embedder_host/client",
        "//services/embedder_host/client:multiplex",
        "//services/embeddings_search_host/client",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/reranker/client",
        "//services/token_exchange:token_exchange_py_proto",
        "//services/token_exchange/client:client_py",
        requirement("dataclasses_json"),
        requirement("grpcio"),
    ],
)

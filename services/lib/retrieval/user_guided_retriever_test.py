"""Tests for user guided retrieval."""

import concurrent.futures
from typing import Optional, Sequence
from unittest import mock

from base.prompt_format.chunk_origin import ChunkOrigin
from base.blob_names.python.blob_names import Blobs
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.lib.retrieval.user_guided_retriever import (
    UserGuidedRetriever,
)
from services.content_manager.client.content_cache import LRUContentCache
from services.content_manager.client.content_manager_client import (
    ContentManagerClient,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext

PREFIX = "def hello():"
MISSING_BLOB_NAME = "missing"
MISSING_BLOB_NAME_HEX = "missing".encode("utf-8").hex()


class MockRetriever(Retriever):
    """Mock retriever."""

    def retrieve(
        self,
        input_: RetrievalInput[CompletionRetrieverPromptInput],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> RetrievalResult:
        assert input_.blobs[0].baseline_checkpoint_id is None
        assert input_.prompt_input.prefix == PREFIX
        assert input_.external_source_ids is None
        chunks = []
        missing_blob_names = []
        for i, blob_name in enumerate(input_.blobs[0].added):
            blob_name_hex = blob_name.hex()
            if blob_name_hex == MISSING_BLOB_NAME_HEX:
                missing_blob_names.append(MISSING_BLOB_NAME_HEX)
                continue
            for j in range(2):
                text = f"chunk-{i}-{j}"
                chunk = RetrievalChunk(
                    text=text,
                    path=f"foo-{blob_name}.py",
                    char_start=0,
                    char_end=len(text),
                    blob_name=blob_name_hex,
                    chunk_index=None,
                    origin="mock-retriever",
                )
                chunks.append(chunk)
        return RetrievalResult(
            retrieved_chunks=chunks,
            missing_blob_names=missing_blob_names,
            checkpoint_not_found=False,
        )

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> FindMissingResult:
        return FindMissingResult(missing_blob_names=[])


class MockCache(LRUContentCache[str]):
    """Mock cache."""

    def __init__(self):
        super().__init__(
            content_manager=mock.MagicMock(ContentManagerClient),
            transform_from_bytes=lambda _, bytes: bytes.decode("utf-8"),
            max_size=10,
            max_elem_size=None,
            max_missing_entries_to_download=None,
        )

    def get_missing(self, keys: Sequence[str], context: RequestContext) -> list[str]:
        return []


auth_info = mock.MagicMock()


def test_user_guided_retrieval():
    """
    Tests we only consider user provided blobs in the retrieval, even when the project has more blobs.
    Also tests a missing blob from user list appears in the missing blob list.
    """

    internal_retriever = MockRetriever()
    retriever = UserGuidedRetriever(
        internal_retriever=internal_retriever,
        num_results=10,
    )
    user_guided_blobs = [
        "1".encode("utf-8").hex(),
        "2".encode("utf-8").hex(),
        MISSING_BLOB_NAME_HEX,
    ]

    input_ = RetrievalInput(
        prompt_input=CompletionRetrieverPromptInput(
            prefix=PREFIX,
            suffix="",
            path="",
        ),
        blobs=Blobs.from_fake_blob_names(["1", "2", "3", "4", MISSING_BLOB_NAME]),
        user_guided_blobs=user_guided_blobs,
    )

    result: RetrievalResult = retriever.retrieve(
        input_, RequestContext.create(), auth_info=auth_info
    )
    assert result.get_missing_blob_names() == [MISSING_BLOB_NAME_HEX]
    assert all(
        [
            chunk.origin == ChunkOrigin.USER_GUIDED_RETRIEVER.value
            for chunk in result.get_retrieved_chunks()
        ]
    )
    returned_blobs = [chunk.blob_name for chunk in result.get_retrieved_chunks()]
    assert set(returned_blobs) == set(
        ["1".encode("utf-8").hex(), "2".encode("utf-8").hex()]
    )


def test_no_user_guided_retrieval():
    """
    When the user provides no context, we should not return any chunks.
    """
    internal_retriever = MockRetriever()
    retriever = UserGuidedRetriever(
        internal_retriever=internal_retriever,
        num_results=10,
    )
    input_ = RetrievalInput(
        prompt_input=CompletionRetrieverPromptInput(
            prefix=PREFIX,
            suffix="",
            path="",
        ),
        blobs=Blobs.from_fake_blob_names(["1", "2", "3", "4", MISSING_BLOB_NAME]),
    )
    result: RetrievalResult = retriever.retrieve(
        input_, RequestContext.create(), auth_info=auth_info
    )
    assert list(result.get_retrieved_chunks()) == []
    assert list(result.get_missing_blob_names()) == []


def test_user_guided_retrieval_external_sources():
    """Test that external sources are not included in user guided retrieval."""

    internal_retriever = MockRetriever()
    retriever = UserGuidedRetriever(
        internal_retriever=internal_retriever,
        num_results=10,
    )
    user_guided_blobs = ["1".encode("utf-8").hex()]

    input_ = RetrievalInput(
        prompt_input=CompletionRetrieverPromptInput(
            prefix=PREFIX,
            suffix="",
            path="",
        ),
        blobs=Blobs.from_fake_blob_names(["1"]),
        user_guided_blobs=user_guided_blobs,
        external_source_ids=["docset://4".encode("utf-8").hex()],
    )

    result: RetrievalResult = retriever.retrieve(
        input_, RequestContext.create(), auth_info=auth_info
    )
    assert all(
        [
            chunk.origin == ChunkOrigin.USER_GUIDED_RETRIEVER.value
            for chunk in result.get_retrieved_chunks()
        ]
    )
    returned_blobs = [chunk.blob_name for chunk in result.get_retrieved_chunks()]
    assert set(returned_blobs) == set(["1".encode("utf-8").hex()])

from unittest.mock import ANY, MagicMock
from base.prompt_format.common import (
    ChatR<PERSON><PERSON><PERSON><PERSON>,
    ChatRequestNodeType,
    ChatRequestToolResult,
)
from base.prompt_format_router.pleasehold_prompt_formatter import (
    PleaseHoldPromptFormatter,
)
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from services.lib.retrieval.retriever_request_insight_builder import (
    RetrieverRequestInsightBuilder,
)
from services.lib.retrieval.router import Router
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    RouterRetrievalResult,
    RouterResponse,
    Category,
    Retriever,
)
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)


def test_router_retrieve_sanity():
    code_retriever = MagicMock(Retriever)
    docset_retriever = MagicMock(Retriever)
    user_guided_retriever = MagicMock(Retriever)
    router_client = MagicMock(InfererClient)
    tokenizer = MagicMock(Qwen25CoderTokenizer)
    ri_builder = MagicMock(RetrieverRequestInsightBuilder)
    router_prompt_formatter = PleaseHoldPromptFormatter(
        tokenizer=tokenizer,
        token_apportionment=None,
        docsets=["docset://test1", "docset://test2"],
    )

    # Arrange
    router = Router(
        name="test-router",
        code_retriever=code_retriever,
        docset_retriever=docset_retriever,
        user_guided_retriever=user_guided_retriever,
        router_client=router_client,
        router_prompt_formatter=router_prompt_formatter,
        router_max_output_length=100,
        router_timeout_s=10,
        ri_builder=ri_builder,
    )

    input_data = RetrievalInput(
        prompt_input=ChatRetrieverPromptInput(
            prefix="def test_function():\n",
            suffix="\n\nprint('Test')",
            path="test/path.py",
            message="Test message",
            selected_code="pass",
            blob_name="blob0",
        ),
    )

    router_client.infer.return_value = InferenceClientProtocol.Reply(
        output_tokens=[1, 2, 3],
        log_probs=[0.1, 0.2, 0.3],
    )
    tokenizer.detokenize.return_value = "0\npath/important_file.py\ntest1"
    expected_router_response = RouterResponse(
        category=Category.GENERAL,
        filenames=["path/important_file.py"],
        docsets=["test1"],
    )

    code_retriever.retrieve.return_value = RetrievalResult(
        retrieved_chunks=[
            RetrievalChunk(
                text="foo",
                path="path/important_file.py",
                char_start=0,
                char_end=3,
                blob_name="blob_important",
                chunk_index=0,
                origin="dense_retriever",
            )
        ],
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    # Act
    result = router.retrieve(
        input_data,
        request_context=MagicMock(),
        auth_info=MagicMock(),
    )

    # Assert
    assert isinstance(result, RouterRetrievalResult)
    assert result.category == Category.GENERAL
    code_retriever.retrieve.assert_called_once()
    docset_retriever.retrieve.assert_called_with(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                prefix="def test_function():\n",
                suffix="\n\nprint('Test')",
                path="test/path.py",
                message="Test message",
                selected_code="pass",
                blob_name="blob0",
            ),
            external_source_ids=["docset://test1"],
        ),
        ANY,
        ANY,
        ANY,
    )
    user_guided_retriever.retrieve.assert_called_with(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                prefix="def test_function():\n",
                suffix="\n\nprint('Test')",
                path="test/path.py",
                message="Test message",
                selected_code="pass",
                blob_name="blob0",
            ),
            blobs=[],
            user_guided_blobs=["blob_important"],
        ),
        ANY,
        ANY,
        ANY,
    )
    ri_builder.record_router_response.assert_called_with(
        ANY,  # TokenizedChatPromptOutput
        ANY,  # InferenceClientProtocol.Reply
        tokenizer,
        expected_router_response,  # router_response
        None,  # error_message
        ANY,
        ANY,
    )


def test_router_tools_result_message():
    code_retriever = MagicMock(Retriever)
    docset_retriever = MagicMock(Retriever)
    user_guided_retriever = MagicMock(Retriever)
    router_client = MagicMock(InfererClient)
    tokenizer = MagicMock(Qwen25CoderTokenizer)
    ri_builder = MagicMock(RetrieverRequestInsightBuilder)
    router_prompt_formatter = PleaseHoldPromptFormatter(
        tokenizer=tokenizer,
        token_apportionment=None,
        docsets=["docset://test1", "docset://test2"],
    )

    # Arrange
    router = Router(
        name="test-router",
        code_retriever=code_retriever,
        docset_retriever=docset_retriever,
        user_guided_retriever=user_guided_retriever,
        router_client=router_client,
        router_prompt_formatter=router_prompt_formatter,
        router_max_output_length=100,
        router_timeout_s=10,
        ri_builder=ri_builder,
    )

    input_data = RetrievalInput(
        prompt_input=ChatRetrieverPromptInput(
            prefix="def test_function():\n",
            suffix="\n\nprint('Test')",
            path="test/path.py",
            message=[
                ChatRequestNode(
                    id=0,
                    type=ChatRequestNodeType.TOOL_RESULT,
                    text_node=None,
                    tool_result_node=ChatRequestToolResult(
                        tool_use_id="toolu_123",
                        content="The meaning of life is 42.",
                        is_error=False,
                    ),
                )
            ],
            selected_code="pass",
            blob_name="blob0",
        ),
    )

    tokenizer.detokenize.return_value = "0\npath/important_file.py\ntest1"

    code_retriever.retrieve.return_value = RetrievalResult(
        retrieved_chunks=[
            RetrievalChunk(
                text="foo",
                path="path/important_file.py",
                char_start=0,
                char_end=3,
                blob_name="",
                chunk_index=0,
                origin="dense_retriever",
            )
        ],
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    # Act
    result = router.retrieve(
        input_data,
        request_context=MagicMock(),
        auth_info=MagicMock(),
    )

    # Assert
    assert isinstance(result, RouterRetrievalResult)
    assert result.category is None  # Did not call router because couldn't format prompt

    # Still called retrievers
    code_retriever.retrieve.assert_called_once()
    docset_retriever.retrieve.assert_called_with(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                prefix="def test_function():\n",
                suffix="\n\nprint('Test')",
                path="test/path.py",
                message=ANY,
                selected_code="pass",
                blob_name="blob0",
            ),
            external_source_ids=[],  # Got no external source ids
        ),
        ANY,
        ANY,
        ANY,
    )
    user_guided_retriever.retrieve.assert_called_with(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                prefix="def test_function():\n",
                suffix="\n\nprint('Test')",
                path="test/path.py",
                message=ANY,
                selected_code="pass",
                blob_name="blob0",
            ),
            blobs=[],
            user_guided_blobs=[],  # Got no user guided blobs
        ),
        ANY,
        ANY,
        ANY,
    )

    # Didn't call router
    router_client.infer.assert_not_called()
    ri_builder.record_router_response.assert_not_called()

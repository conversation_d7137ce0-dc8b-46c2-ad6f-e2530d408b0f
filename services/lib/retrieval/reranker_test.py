"""Test for the reranker module.

Much of this is copied from services/completion_host/single_model_server/dense_retriever_test.py
"""

from functools import partial
from typing import Generator, Iterable, Optional, Sequence
from unittest import mock

import numpy
import pytest

import base.tokenizers
import services.embeddings_indexer.chunk_pb2 as chunk_pb2
from base import prompt_format_rerank, prompt_format_retrieve
from base.blob_names.python.blob_names import Blobs
from base.prompt_format.chunk_origin import ChunkOrigin
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from services.lib.retrieval.dense_retriever import (
    DenseRetriever,
)
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalInput,
    parse_retrieval_chunk_from_bytes_builder,
)
from services.content_manager.client.content_cache import LRUContentCache
from services.content_manager.client.content_manager_client import (
    Content<PERSON><PERSON>,
    ContentManagerClient,
)
from services.embedder_host.client.client import (
    EmbedderClient<PERSON>rotocol,
    EmbedderRequestType,
)
from services.embeddings_search_host.client.client import (
    EmbeddingsFindMissingResult,
    EmbeddingsSearchChunksResult,
    EmbeddingsSearchClientProtocol,
)
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.reranker import (
    RetrieveThenChunkExpansionReranker,
    is_adjacent,
    join_two_retrieval_chunks,
)
from services.reranker.client.client import (
    RerankerClientProtocol,
    RerankerOutput,
    RerankerScores,
)

# pylint: disable=protected-access

# chunk_prediction token for starcoder tokenizer and dark chatanol
TARGET_TOKEN = 49176


def tokenize_and_numpy(tokenizer, text: str) -> numpy.ndarray:
    """Tokenize a text and return it as numpy array."""
    return numpy.array(tokenizer.tokenize_unsafe(text)).astype(numpy.int32)


def gen_blob_name(s: str) -> str:
    """Convert a string to a hex-string blob name."""
    return s.encode("utf-8").hex()


class MockEmbedderClient(EmbedderClientProtocol):
    """A mock implementation of the embedder client protocol."""

    def __init__(self):
        self.calls = []

    def calculate_embedding(
        self,
        tokens: list[list[int]],
        request_context: RequestContext,
        timeout: float = 30,
        request_type: EmbedderRequestType = EmbedderRequestType.DEFAULT,
        source_namespace: str | None = None,
    ) -> numpy.ndarray:
        del timeout, source_namespace, request_type, request_context
        self.calls.append(tokens)
        return numpy.ones([len(tokens), 1024])


class MockEmbeddingsSearchClient(EmbeddingsSearchClientProtocol):
    """Mock of the embeddings search client."""

    def __init__(
        self,
        content_manager_client,
        missing_blob_names: list[str] = [],
        checkpoint_not_found: bool = False,
    ):
        self.content_manager_client = content_manager_client
        self.search_calls = []
        self.find_missing_calls = []
        self.known_blob_names = []
        self.missing_blob_names = missing_blob_names
        self.checkpoint_not_found = checkpoint_not_found

    def search_chunks(
        self,
        query: numpy.ndarray,
        blobs: Sequence[Blobs],
        num_results: int,
        transformation_key: str,
        request_context: RequestContext,
        search_timeout_ms: int = 1000,
        sequence_id: Optional[int] = None,
        timeout: float = 30,
    ) -> EmbeddingsSearchChunksResult:
        self.search_calls.append((query))

        blob_names = list(blobs[0].added)

        keys = [
            ContentKey(blob_name.hex(), "dr-1", f"chunk-{i}.pb")
            for blob_name in blob_names
            for i in blob_name_to_all_chunk_indices[blob_name.hex()]
        ]
        content = self.content_manager_client.batch_download_all(
            keys, request_context=request_context
        )
        chunks = [
            EmbeddingsSearchChunksResult.RetrievalChunk(
                blob_name=key.blob_name,
                chunk_index=int(key.sub_key.split("-")[1].split(".")[0]),
                score=0.5,
                content=content_metadata[0],
                metadata=content_metadata[1],
            )
            for (key, content_metadata) in zip(keys, content)
            if content_metadata is not None
        ]

        return EmbeddingsSearchChunksResult(
            chunks=chunks,
            missing_blobs=EmbeddingsSearchChunksResult.MissingBlobs(
                missing_blob_names=self.missing_blob_names,
                checkpoint_not_found=self.checkpoint_not_found,
            ),
        )

    def find_missing(
        self,
        blob_names: Sequence[str],
        transformation_key: str,
        sub_key: str,
        request_context: RequestContext,
    ) -> EmbeddingsFindMissingResult:
        self.find_missing_calls.append(blob_names)
        return EmbeddingsFindMissingResult(
            missing_blob_names=[
                blob_name
                for blob_name in blob_names
                if blob_name not in self.known_blob_names
            ],
        )


class MockRerankerClient(RerankerClientProtocol):
    """A mock implementation of the reranker client protocol."""

    def __init__(self):
        self.calls = []

    def calculate_scores(
        self,
        tokens: Sequence[int],
        request_context: RequestContext,
        timeout: float = 30,
        source_namespace: str | None = None,
    ) -> RerankerOutput:
        """Calculate the list of scores for the chunks in a given batch of tokens."""
        del timeout, source_namespace, request_context
        self.calls.append(tokens)
        target_token_ct = sum([1 for t in tokens if t == TARGET_TOKEN])
        return RerankerOutput(
            scores_list=[
                RerankerScores(
                    gain=1.0,
                    gain_left=0.1,
                    gain_right=0.2,
                )
                for _ in range(target_token_ct)
            ]
        )


content_key_to_chunk = {
    ContentKey(gen_blob_name("foo/bar.py"), "dr-1", "chunk-0.pb"): chunk_pb2.Chunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_offset=0,
        length=len("Hello World\n"),
        line_offset=0,
        length_in_lines=1,
    ),
    ContentKey(gen_blob_name("foo/bar.py"), "dr-1", "chunk-1.pb"): chunk_pb2.Chunk(
        text="After Hello World\n",
        path="foo/bar.py",
        char_offset=len("Hello World\n"),
        length=len("Hello World\n") + len("After Hello World\n"),
        line_offset=1,
        length_in_lines=1,
    ),
    ContentKey(gen_blob_name("foo/bar.py"), "dr-1", "chunk-2.pb"): chunk_pb2.Chunk(
        text="After After Hello World\n",
        path="foo/bar.py",
        char_offset=len("Hello World\n") + len("After Hello World\n"),
        length=len("Hello World\n")
        + len("After Hello World\n")
        + len("After After Hello World\n"),
        line_offset=2,
        length_in_lines=1,
    ),
}

# Test the edge case where we have two different blobs with the same path
duplicate_paths_content_key_to_chunk = {
    **content_key_to_chunk,
    ContentKey(
        gen_blob_name("thirdparty/foo/bar.py"), "dr-1", "chunk-0.pb"
    ): chunk_pb2.Chunk(
        text="I'm another blob!",
        path="foo/bar.py",
        char_offset=0,
        length=len("I'm another blob!"),
        line_offset=0,
        length_in_lines=1,
    ),
}

# Drop a chunk from the middle of document
missing_neighbor_content_key_to_chunk = content_key_to_chunk.copy()
missing_neighbor_content_key_to_chunk.pop(
    ContentKey(gen_blob_name("foo/bar.py"), "dr-1", "chunk-2.pb")
)
missing_neighbor_content_key_to_chunk[
    ContentKey(gen_blob_name("foo/bar.py"), "dr-1", "chunk-2.pb")
] = chunk_pb2.Chunk(
    text="After After After Hello World\n",
    path="foo/bar.py",
    char_offset=len("Hello World\n")
    + len("After Hello World\n")
    + len("After After Hello World\n"),
    length=len("Hello World\n")
    + len("After Hello World\n")
    + len("After After Hello World\n")
    + len("After After After Hello World\n"),
    line_offset=3,
    length_in_lines=1,
)

blob_name_to_all_chunk_indices = {
    gen_blob_name("foo/bar.py"): [0, 1, 2],
    gen_blob_name("thirdparty/foo/bar.py"): [0],
}


def _mock_download_all(
    keys: Sequence[ContentKey],
    content_map: dict[ContentKey, chunk_pb2.Chunk],
    request_context: RequestContext,
    tenant_id: str | None = None,
    micro_batch_size: int = 1024,
) -> Iterable[tuple[bytes, dict[str, str]] | None]:
    for key in keys:
        if key in content_map:
            yield content_map[key].SerializeToString(), {}
        else:
            yield None


@pytest.fixture
def content_manager() -> ContentManagerClient:
    client = mock.MagicMock(ContentManagerClient)
    client.batch_download_all = mock.MagicMock(
        side_effect=partial(_mock_download_all, content_map=content_key_to_chunk)
    )
    return client


@pytest.fixture
def content_manager_with_duplicates() -> ContentManagerClient:
    client = mock.MagicMock(ContentManagerClient)
    client.batch_download_all = mock.MagicMock(
        side_effect=partial(
            _mock_download_all, content_map=duplicate_paths_content_key_to_chunk
        )
    )
    return client


@pytest.fixture
def content_manager_with_missing_neighbor() -> ContentManagerClient:
    client = mock.MagicMock(ContentManagerClient)
    client.batch_download_all = mock.MagicMock(
        side_effect=partial(
            _mock_download_all, content_map=missing_neighbor_content_key_to_chunk
        )
    )
    return client


def test_reranker(content_manager: ContentManagerClient):
    """Default test for the reranker system."""
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient(
        content_manager_client=content_manager
    )
    reranker_rpc_client = MockRerankerClient()

    chunk_cache = LRUContentCache(
        content_manager=content_manager,
        transform_from_bytes=parse_retrieval_chunk_from_bytes_builder(
            ChunkOrigin.DENSE_RETRIEVER.value
        ),
        max_size=1024 * 1024,
    )
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    reranker_prompt_formatter = (
        prompt_format_rerank.get_reranker_prompt_formatter_by_name(
            "dark-chatanol1", tokenizer=tokenizer
        )
    )

    transformation_key = "dr-1"
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key=transformation_key,
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )
    rr = RetrieveThenChunkExpansionReranker(
        retriever=dr,
        reranker_client=reranker_rpc_client,
        reranker_prompt_formatter=reranker_prompt_formatter,
        transformation_key=transformation_key,
        chunk_cache=chunk_cache,
    )

    blob_name = gen_blob_name("foo/bar.py")
    blobs = Blobs.from_blob_names([blob_name])
    request_context = RequestContext.create()

    result = dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 3, retrieved_chunks
    for i in range(3):
        assert retrieved_chunks[i].path == "foo/bar.py"
    assert retrieved_chunks[0].text == "Hello World\n"
    assert retrieved_chunks[1].text == "After Hello World\n"
    assert retrieved_chunks[2].text == "After After Hello World\n"

    result = rr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 7, retrieved_chunks

    expected_chunks_texts = set(
        [
            "Hello World\n",
            "After Hello World\n",
            "After After Hello World\n",
            # expansions
            "Hello World\nAfter Hello World\n",
            "After Hello World\nAfter After Hello World\n",
            "Hello World\nAfter Hello World\nAfter After Hello World\n",
        ]
    )
    actual_chunk_texts = set([chunk.text for chunk in retrieved_chunks])
    assert expected_chunks_texts == actual_chunk_texts

    for chunk in retrieved_chunks:
        assert chunk.path == "foo/bar.py"

    # The content manager is called once for embedder and twice by mock embeddings search
    assert content_manager.batch_download_all.call_count == 3

    # The embedder is called once for each test above
    assert len(embedder_rpc_client.calls) == 2
    # in particular, the prompt doesn't contain the path, but it is suffixed by the endofquery token
    # and prefixed by sos token
    actual_tokens = embedder_rpc_client.calls[0][0]
    expected_text = "foo/foo.py<fim_prefix>Hi AI!<|ret-endofquery|>"
    assert actual_tokens == tokenizer.tokenize_unsafe(
        expected_text
    ), f"Got {tokenizer.detokenize(actual_tokens)}. Expected {expected_text}"
    assert len(embeddings_search_rpc_client.search_calls) == 2

    # The reranker is called once
    assert len(reranker_rpc_client.calls) == 1


def test_reranker_batching(content_manager: ContentManagerClient):
    """Default test for the reranker.

    This test is similar to test_reranker but it tests batching logic.
    We only rerank num_chunks_to_rerank=2 chunks this time, and we set seq_len_per_batch_elem=16.
    """
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient(
        content_manager_client=content_manager
    )
    reranker_rpc_client = MockRerankerClient()

    chunk_cache = LRUContentCache(
        content_manager=content_manager,
        transform_from_bytes=parse_retrieval_chunk_from_bytes_builder(
            ChunkOrigin.DENSE_RETRIEVER.value
        ),
        max_size=1024 * 1024,
    )
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    reranker_prompt_formatter = (
        prompt_format_rerank.get_reranker_prompt_formatter_by_name(
            "dark-chatanol1", tokenizer=tokenizer
        )
    )

    transformation_key = "dr-1"
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key=transformation_key,
        query_prompt_formatter=embedding_prompt_formatter,
        num_results=32,
        search_timeout_ms=5000,
    )
    rr = RetrieveThenChunkExpansionReranker(
        retriever=dr,
        reranker_client=reranker_rpc_client,
        reranker_prompt_formatter=reranker_prompt_formatter,
        transformation_key=transformation_key,
        chunk_cache=chunk_cache,
        num_chunks_to_rerank=2,
        seq_len_per_batch_elem=16,
    )

    blob_name = gen_blob_name("foo/bar.py")
    blobs = Blobs.from_blob_names([blob_name])
    request_context = RequestContext.create()

    result = dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 3, retrieved_chunks
    for i in range(3):
        assert retrieved_chunks[i].path == "foo/bar.py"
    assert retrieved_chunks[0].text == "Hello World\n"
    assert retrieved_chunks[1].text == "After Hello World\n"
    assert retrieved_chunks[2].text == "After After Hello World\n"

    result = rr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 5, retrieved_chunks

    expected_chunks_texts = set(
        [
            "Hello World\n",
            "After Hello World\n",
            # expansions
            "Hello World\nAfter Hello World\n",
            "After Hello World\nAfter After Hello World\n",
            "Hello World\nAfter Hello World\nAfter After Hello World\n",
        ]
    )
    actual_chunk_texts = set([chunk.text for chunk in retrieved_chunks])
    assert expected_chunks_texts == actual_chunk_texts

    for chunk in retrieved_chunks:
        assert chunk.path == "foo/bar.py"

    # The content manager is called once for embedder and twice by mock embeddings search
    assert content_manager.batch_download_all.call_count == 3

    # The embedder is called once for each test above
    assert len(embedder_rpc_client.calls) == 2
    # in particular, the prompt doesn't contain the path, but it is suffixed by the endofquery token
    # and prefixed by sos token
    actual_tokens = embedder_rpc_client.calls[0][0]
    expected_text = "foo/foo.py<fim_prefix>Hi AI!<|ret-endofquery|>"
    assert actual_tokens == tokenizer.tokenize_unsafe(
        expected_text
    ), f"Got {tokenizer.detokenize(actual_tokens)}. Expected {expected_text}"
    assert len(embeddings_search_rpc_client.search_calls) == 2

    # The reranker is called once
    assert len(reranker_rpc_client.calls) == 1


def test_reranker_missing_blobs(content_manager: ContentManagerClient):
    """Test of the reranker with missing blobs and checkpoint not found"""
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient(
        content_manager_client=content_manager,
        missing_blob_names=["missing-blob-1"],
        checkpoint_not_found=True,
    )
    reranker_rpc_client = MockRerankerClient()

    chunk_cache = LRUContentCache(
        content_manager=content_manager,
        transform_from_bytes=parse_retrieval_chunk_from_bytes_builder(
            ChunkOrigin.DENSE_RETRIEVER.value
        ),
        max_size=1024 * 1024,
    )
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    reranker_prompt_formatter = (
        prompt_format_rerank.get_reranker_prompt_formatter_by_name(
            "dark-chatanol1", tokenizer=tokenizer
        )
    )

    transformation_key = "dr-1"
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key=transformation_key,
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )
    rr = RetrieveThenChunkExpansionReranker(
        retriever=dr,
        reranker_client=reranker_rpc_client,
        reranker_prompt_formatter=reranker_prompt_formatter,
        transformation_key=transformation_key,
        chunk_cache=chunk_cache,
    )

    blob_name = gen_blob_name("foo/bar.py")
    blobs = Blobs.from_blob_names([blob_name])
    request_context = RequestContext.create()

    result = dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 1
    assert result.get_checkpoint_not_found() is True

    result = rr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 1
    assert result.get_checkpoint_not_found() is True


def test_reranker_same_path_different_blobs_edge_case(
    content_manager_with_duplicates: ContentManagerClient,
):
    """Test of the reranker with the same path and different blobs.

    This test is similar to test_reranker but it tests the case where the same path is used for two different
    blobs.
    """
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient(
        content_manager_client=content_manager_with_duplicates
    )
    reranker_rpc_client = MockRerankerClient()

    chunk_cache = LRUContentCache(
        content_manager=content_manager_with_duplicates,
        transform_from_bytes=parse_retrieval_chunk_from_bytes_builder(
            ChunkOrigin.DENSE_RETRIEVER.value
        ),
        max_size=1024 * 1024,
    )

    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    reranker_prompt_formatter = (
        prompt_format_rerank.get_reranker_prompt_formatter_by_name(
            "dark-chatanol1", tokenizer=tokenizer
        )
    )

    transformation_key = "dr-1"
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key=transformation_key,
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )
    rr = RetrieveThenChunkExpansionReranker(
        retriever=dr,
        reranker_client=reranker_rpc_client,
        reranker_prompt_formatter=reranker_prompt_formatter,
        transformation_key=transformation_key,
        chunk_cache=chunk_cache,
    )

    blobs = Blobs.from_blob_names(
        [gen_blob_name("foo/bar.py"), gen_blob_name("thirdparty/foo/bar.py")]
    )
    request_context = RequestContext.create()

    ### FIRST TEST THAT ALL THE CHUNKS ARE IN CACHE
    corpus_all_keys = list(duplicate_paths_content_key_to_chunk.keys())
    corpus_rawdocs = list(
        chunk_cache._content_manager.batch_download_all(
            corpus_all_keys, request_context=request_context
        )
    )
    assert corpus_rawdocs is not None
    parsed_corpus = [
        chunk_cache._transform_from_bytes(("dev-augie", key), doc_binary)
        for key, (doc_binary, _) in zip(corpus_all_keys, corpus_rawdocs)
    ]
    for i in range(3):
        assert parsed_corpus[i].path == "foo/bar.py"
        assert parsed_corpus[i].blob_name == gen_blob_name("foo/bar.py")
    assert parsed_corpus[3].text == "I'm another blob!"
    assert parsed_corpus[3].blob_name == gen_blob_name("thirdparty/foo/bar.py")

    ### NEXT TEST RETRIEVAL
    result = dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 4, retrieved_chunks
    for i in range(4):
        assert retrieved_chunks[i].path == "foo/bar.py"
    assert retrieved_chunks[0].text == "Hello World\n"
    assert retrieved_chunks[1].text == "After Hello World\n"
    assert retrieved_chunks[2].text == "After After Hello World\n"
    assert retrieved_chunks[3].text == "I'm another blob!"

    assert retrieved_chunks[0].blob_name == gen_blob_name("foo/bar.py")
    assert retrieved_chunks[1].blob_name == gen_blob_name("foo/bar.py")
    assert retrieved_chunks[2].blob_name == gen_blob_name("foo/bar.py")
    assert retrieved_chunks[3].blob_name == gen_blob_name(
        "thirdparty/foo/bar.py"
    ), retrieved_chunks[3]

    # FINALLY TEST RERANKING
    result = rr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 8, retrieved_chunks

    expected_chunks_texts = set(
        [
            "Hello World\n",
            "After Hello World\n",
            "After After Hello World\n",
            # expansions
            "Hello World\nAfter Hello World\n",
            "After Hello World\nAfter After Hello World\n",
            "Hello World\nAfter Hello World\nAfter After Hello World\n",
            # separate blob
            "I'm another blob!",
        ]
    )
    actual_chunk_texts = set([chunk.text for chunk in retrieved_chunks])
    assert expected_chunks_texts == actual_chunk_texts

    for chunk in retrieved_chunks:
        assert chunk.path == "foo/bar.py"


def test_reranker_missing_neighbors(
    content_manager_with_missing_neighbor: ContentManagerClient,
):
    """Test of the reranker with missing neighbors.

    This test is similar to test_reranker but it tests the case where a chunk is missing from the middle of a file,
    precluding retrieving a neighbor chunk for its neighbors.
    """
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient(
        content_manager_client=content_manager_with_missing_neighbor
    )
    reranker_rpc_client = MockRerankerClient()

    chunk_cache = LRUContentCache(
        content_manager=content_manager_with_missing_neighbor,
        transform_from_bytes=parse_retrieval_chunk_from_bytes_builder(
            ChunkOrigin.DENSE_RETRIEVER.value
        ),
        max_size=1024 * 1024,
    )

    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    reranker_prompt_formatter = (
        prompt_format_rerank.get_reranker_prompt_formatter_by_name(
            "dark-chatanol1", tokenizer=tokenizer
        )
    )

    transformation_key = "dr-1"
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key=transformation_key,
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )
    rr = RetrieveThenChunkExpansionReranker(
        retriever=dr,
        reranker_client=reranker_rpc_client,
        reranker_prompt_formatter=reranker_prompt_formatter,
        transformation_key=transformation_key,
        chunk_cache=chunk_cache,
    )

    blobs = Blobs.from_blob_names(
        [gen_blob_name("foo/bar.py"), gen_blob_name("thirdparty/foo/bar.py")]
    )
    request_context = RequestContext.create()

    ### NEXT TEST RETRIEVAL
    result = dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 3, retrieved_chunks
    for i in range(3):
        assert retrieved_chunks[i].path == "foo/bar.py"
    assert retrieved_chunks[0].text == "Hello World\n"
    assert retrieved_chunks[1].text == "After Hello World\n"
    assert retrieved_chunks[2].text == "After After After Hello World\n"

    assert retrieved_chunks[0].blob_name == gen_blob_name("foo/bar.py")
    assert retrieved_chunks[1].blob_name == gen_blob_name("foo/bar.py")
    assert retrieved_chunks[2].blob_name == gen_blob_name("foo/bar.py")

    # FINALLY TEST RERANKING
    result = rr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                path="foo/foo.py",
                prefix="Hi AI!",
                suffix="",
                selected_code="",
                message="",
                chat_history=[],
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 5, retrieved_chunks

    expected_chunks_texts = set(
        [
            "Hello World\n",
            "After Hello World\n",
            "After After After Hello World\n",
            # expansions
            "Hello World\nAfter Hello World\n",
        ]
    )
    actual_chunk_texts = set([chunk.text for chunk in retrieved_chunks])
    assert expected_chunks_texts == actual_chunk_texts

    for chunk in retrieved_chunks:
        assert chunk.path == "foo/bar.py"


def test_find_missing(content_manager: ContentManagerClient):
    """Test of the dense retrieval system's find-missing endpoint."""
    tokenizer = base.tokenizers.create_tokenizer_by_name("starcoder")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient(
        content_manager_client=content_manager
    )
    reranker_rpc_client = MockRerankerClient()
    chunk_cache = LRUContentCache(
        content_manager=content_manager,
        transform_from_bytes=parse_retrieval_chunk_from_bytes_builder(
            ChunkOrigin.DENSE_RETRIEVER.value
        ),
        max_size=1024 * 1024,
    )
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    reranker_prompt_formatter = (
        prompt_format_rerank.get_reranker_prompt_formatter_by_name(
            "dark-chatanol1", tokenizer=tokenizer
        )
    )
    transformation_key = "dr-1"
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key=transformation_key,
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )
    rr = RetrieveThenChunkExpansionReranker(
        retriever=dr,
        reranker_client=reranker_rpc_client,
        reranker_prompt_formatter=reranker_prompt_formatter,
        transformation_key=transformation_key,
        chunk_cache=chunk_cache,
    )

    valid_blob_names = ["blob-0", "blob-1"]
    bogus_blob_names = ["bogus-100", "bogus-101"]
    embeddings_search_rpc_client.known_blob_names = valid_blob_names
    to_query = valid_blob_names + bogus_blob_names
    request_context = RequestContext.create()
    result = rr.find_missing(to_query, request_context=request_context)
    assert len(embeddings_search_rpc_client.find_missing_calls) == 1
    assert len(result.missing_blob_names) == len(bogus_blob_names)
    for blob_name in bogus_blob_names:
        assert blob_name in result.missing_blob_names


def test_parse_retrieval_chunk_from_bytes_builder_invalid_origin():
    """Test that the builder raises an exception if the origin is invalid."""
    with pytest.raises(ValueError):
        parse_retrieval_chunk_from_bytes_builder("invalid-origin")


def test_is_adjacent():
    """Test that is_adjacent returns True if the two chunks are adjacent."""

    # Check for adjacent chunks
    chunk1 = RetrievalChunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_start=0,
        char_end=len("Hello World\n"),
        blob_name="blob-0",
        chunk_index=0,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="After Hello World\n",
        path="foo/bar.py",
        char_start=len("Hello World\n"),
        char_end=len("Hello World\n") + 50,
        blob_name="blob-0",
        chunk_index=1,
        origin="dense_retriever",
    )
    assert is_adjacent(chunk1, chunk2)

    # Check for non-adjacent chunks
    chunk1 = RetrievalChunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_start=0,
        char_end=len("Hello World\n"),
        blob_name="blob-0",
        chunk_index=0,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="After Hello World\n",
        path="foo/bar.py",
        char_start=55,
        char_end=55 + len("After Hello World\n"),
        blob_name="blob-0",
        chunk_index=1,
        origin="dense_retriever",
    )


def test_join_two_retrieval_chunks():
    """Test that join_two_retrieval_chunks joins two chunks into one."""

    # Check for adjacent chunks
    chunk1 = RetrievalChunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_start=0,
        char_end=12,
        blob_name="blob-0",
        chunk_index=0,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="After Hello World\n",
        path="foo/bar.py",
        char_start=12,
        char_end=30,
        blob_name="blob-0",
        chunk_index=1,
        origin="dense_retriever",
    )
    joined_chunk = join_two_retrieval_chunks(chunk1, chunk2)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 0 and joined_chunk.char_end == 30
    joined_chunk = join_two_retrieval_chunks(chunk2, chunk1)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 0 and joined_chunk.char_end == 30

    # Check for non overlapping
    chunk1 = RetrievalChunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_start=0,
        char_end=12,
        blob_name="blob-0",
        chunk_index=0,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="After Hello World\n",
        path="foo/bar.py",
        char_start=42,
        char_end=30,
        blob_name="blob-0",
        chunk_index=2,
        origin="dense_retriever",
    )
    with pytest.raises(AssertionError):
        join_two_retrieval_chunks(chunk1, chunk2)
    with pytest.raises(AssertionError):
        join_two_retrieval_chunks(chunk2, chunk1)

    # Check for partial overlap (there is some nonzero overlap) with no matching edge
    chunk1 = RetrievalChunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_start=100,
        char_end=112,
        blob_name="blob-0",
        chunk_index=3,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="World\nAfter Hello World\n",
        path="foo/bar.py",
        char_start=106,
        char_end=130,
        blob_name="blob-0",
        chunk_index=4,
        origin="dense_retriever",
    )
    joined_chunk = join_two_retrieval_chunks(chunk1, chunk2)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 100 and joined_chunk.char_end == 130
    joined_chunk = join_two_retrieval_chunks(chunk2, chunk1)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 100 and joined_chunk.char_end == 130

    # Check for complete overlap (one chunk completely contains another) with one edge matching
    chunk1 = RetrievalChunk(
        text="Hello World\nAfter Hello World\n",
        path="foo/bar.py",
        char_start=0,
        char_end=30,
        blob_name="blob-0",
        chunk_index=0,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="After Hello World\n",
        path="foo/bar.py",
        char_start=12,
        char_end=30,
        blob_name="blob-0",
        chunk_index=1,
        origin="dense_retriever",
    )
    joined_chunk = join_two_retrieval_chunks(chunk1, chunk2)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 0 and joined_chunk.char_end == 30
    joined_chunk = join_two_retrieval_chunks(chunk2, chunk1)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 0 and joined_chunk.char_end == 30

    chunk1 = RetrievalChunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_start=100,
        char_end=112,
        blob_name="blob-0",
        chunk_index=0,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="Hello World\nAfter Hello World\n",
        path="foo/bar.py",
        char_start=100,
        char_end=130,
        blob_name="blob-0",
        chunk_index=1,
        origin="dense_retriever",
    )
    joined_chunk = join_two_retrieval_chunks(chunk1, chunk2)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 100 and joined_chunk.char_end == 130
    joined_chunk = join_two_retrieval_chunks(chunk2, chunk1)
    assert joined_chunk.text == "Hello World\nAfter Hello World\n"
    assert joined_chunk.char_start == 100 and joined_chunk.char_end == 130

    # Check for complete overlap (one chunk completely contains another) with noe edges matching
    chunk1 = RetrievalChunk(
        text="Hello World\nAfter Hello World\nAfter After Hello World\n",
        path="foo/bar.py",
        char_start=0,
        char_end=54,
        blob_name="blob-0",
        chunk_index=0,
        origin="dense_retriever",
    )
    chunk2 = RetrievalChunk(
        text="After Hello World\n",
        path="foo/bar.py",
        char_start=12,
        char_end=30,
        blob_name="blob-0",
        chunk_index=1,
        origin="dense_retriever",
    )

    joined_chunk = join_two_retrieval_chunks(chunk1, chunk2)
    assert (
        joined_chunk.text == "Hello World\nAfter Hello World\nAfter After Hello World\n"
    )
    assert joined_chunk.char_start == 0 and joined_chunk.char_end == 54
    joined_chunk = join_two_retrieval_chunks(chunk2, chunk1)
    assert (
        joined_chunk.text == "Hello World\nAfter Hello World\nAfter After Hello World\n"
    )
    assert joined_chunk.char_start == 0 and joined_chunk.char_end == 54

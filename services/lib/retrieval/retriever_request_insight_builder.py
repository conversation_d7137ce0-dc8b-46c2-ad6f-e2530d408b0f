"""Helper to build request insight events for retrievers."""

from typing import Iterable, Optional, Sequence

from base.prompt_format_chat.prompt_formatter import TokenizedChatPromptOutput
from services.inference_host.client.inference_host_client import InferenceClientProtocol
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
import opentelemetry.trace

import base.tokenizers
from services.lib.retrieval.retriever import Retrieval<PERSON>hunk, RouterResponse
from services.request_insight import request_insight_pb2
from services.request_insight.publisher import request_insight_publisher

opentelemetry_tracer = opentelemetry.trace.get_tracer(__name__)


# TODO: due to python pass-by-assignment we may want to inline the Tokenization field setting
def _get_tokenization_proto(
    tokenizer: base.tokenizers.Tokenizer,
    token_ids: Sequence[int],
    log_probs: Optional[Sequence[float]],
) -> request_insight_pb2.Tokenization:
    text, offsets = tokenizer.detokenize_with_offsets(token_ids)
    return request_insight_pb2.Tokenization(
        token_ids=token_ids,
        text=text,
        offsets=offsets,
        log_probs=log_probs or [],
    )


class RetrieverRequestInsightBuilder:
    """Builder to log request insight events for retrieval requests."""

    def __init__(
        self,
        ri_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        self.ri_publisher = ri_publisher

    def record_retrieval_result(
        self,
        retrieval_type: request_insight_pb2.RetrievalType.ValueType,
        query_prompt: Sequence[int],
        embedder_tokenizer: Optional[base.tokenizers.Tokenizer],
        retrieved_chunks: Iterable[RetrievalChunk],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record an event for the retriever."""
        event = request_insight_publisher.new_event()
        event.retrieval_response.retrieval_type = retrieval_type

        # Only set the embeddings prompt if we have an embeddings tokenizer
        if embedder_tokenizer:
            event.retrieval_response.query_prompt.CopyFrom(
                _get_tokenization_proto(embedder_tokenizer, query_prompt, None)
            )

        def to_chunk_proto(chunk: RetrievalChunk) -> request_insight_pb2.RetrievalChunk:
            """Convert a RetrievalChunk to a request insight RetrievalChunk."""
            chunk_body = chunk.text

            # Let's visualize the header for documentation chunks
            if chunk.documentation_metadata is not None:
                header = ""
                for chunk_header in chunk.documentation_metadata.headers:
                    header += chunk_header + "\n"
                chunk_body = header + chunk_body

            # TODO(arun): We should also be logging the chunk end and origin, but
            # these fields don't exist in request insight yet.
            ri_chunk = request_insight_pb2.RetrievalChunk(
                text=chunk_body,
                path=chunk.path,
                char_offset=chunk.char_start,
                char_end=chunk.char_end,
                blob_name=chunk.blob_name or "",
                chunk_index=chunk.chunk_index or 0,
                origin=chunk.origin,
                score=chunk.score or 0.0,
            )
            if chunk.blob_name:
                ri_chunk.blob_name = chunk.blob_name
            if chunk.chunk_index:
                ri_chunk.chunk_index = chunk.chunk_index
            return ri_chunk

        event.retrieval_response.retrieved_chunks.extend(
            map(to_chunk_proto, retrieved_chunks)
        )

        update_request_info_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request_info_request)

    def record_reranker_result(
        self,
        reranker_prompts: Sequence[list[int]],
        reranker_tokenizer: Optional[base.tokenizers.Tokenizer],
        reranked_chunks: Iterable[RetrievalChunk],
        chunk_scores: Sequence[float],
        chunk_expansion_descriptions: Sequence[str],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record an event for the rereranker."""
        event = request_insight_publisher.new_event()

        # Only set the embeddings prompt if we have an embeddings tokenizer
        if reranker_tokenizer:
            for reranker_prompt in reranker_prompts:
                event.reranker_response.reranker_prompts.append(
                    _get_tokenization_proto(reranker_tokenizer, reranker_prompt, None)
                )

        def to_chunk_proto(
            chunk: RetrievalChunk, score: float, description: str
        ) -> request_insight_pb2.RerankerChunk:
            """Convert a RetrievalChunk to a request insight RetrievalChunk."""
            ri_chunk = request_insight_pb2.RerankerChunk(
                text=chunk.text,
                path=chunk.path,
                char_offset=chunk.char_start,
                char_end=chunk.char_end,
                blob_name=chunk.blob_name or "",
                chunk_index=chunk.chunk_index or 0,
                origin=chunk.origin,
                score=score,
                short_description=description,
            )
            if chunk.blob_name:
                ri_chunk.blob_name = chunk.blob_name
            if chunk.chunk_index:
                ri_chunk.chunk_index = chunk.chunk_index
            return ri_chunk

        event.reranker_response.reranked_chunks.extend(
            map(
                to_chunk_proto,
                reranked_chunks,
                chunk_scores,
                chunk_expansion_descriptions,
            )
        )

        update_request_info_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )
        self.ri_publisher.publish_request_insight(update_request_info_request)

    def record_router_response(
        self,
        router_prompt_tokens: TokenizedChatPromptOutput,
        router_reply: InferenceClientProtocol.Reply | None,
        router_prompt_tokenizer: base.tokenizers.Tokenizer,
        router_response: RouterResponse | None,
        error_message: str | None,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ):
        """Record a router response event."""
        event = request_insight_publisher.new_event()

        if router_response:
            event.router_response.parsed_response.category = (
                router_response.category.name
            )
            event.router_response.parsed_response.filenames.extend(
                router_response.filenames
            )
            event.router_response.parsed_response.docsets.extend(
                router_response.docsets
            )

        if error_message:
            event.router_response.error_message = error_message

        event.router_response.prompt_tokens.CopyFrom(
            _get_tokenization_proto(
                router_prompt_tokenizer, router_prompt_tokens.tokens, None
            )
        )
        if router_reply:
            event.router_response.response_tokens.CopyFrom(
                _get_tokenization_proto(
                    router_prompt_tokenizer,
                    router_reply.output_tokens,
                    router_reply.log_probs,
                )
            )

        update_request_info_request = self.ri_publisher.update_request_info_request(
            request_context.request_id, [event], auth_info
        )

        self.ri_publisher.publish_request_insight(update_request_info_request)

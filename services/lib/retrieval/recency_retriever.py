"""A retriever that uses recency signals, e.g. recent changes and git diffs."""

import concurrent.futures
import dataclasses
import re
from typing import Iterator, Optional, Sequence

from services.lib.retrieval import dummy_executor
import structlog
import unidiff.patch
from unidiff import Hunk, PatchedF<PERSON>, PatchSet

from base.blob_names.python.blob_names import Blobs
from base.prompt_format.chunk_origin import ChunkOrigin
from base.prompt_format_completion.overlap import (
    find_first_overlap,
    partial_overlap_predicate,
)
from services.completion_host import completion_pb2
from services.lib.retrieval import (
    retriever_request_insight_builder,
)
from services.lib.retrieval.retriever import (
    FindMissingR<PERSON>ult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.content_manager.client.content_cache import LRUContentCache
from services.content_manager.client.content_manager_client import ContentKey
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2

log = structlog.get_logger()


@dataclasses.dataclass(frozen=True)
class GitDiffChunk:
    """
    Represents a chunk of text changed in a Git diff.

    This class defines a character span of a Git change, allowing for overlap checks with other chunks.
    The character span is calculated by converting Git diff lines from a unified diff into character spans.
    The span follows the inclusive start and exclusive end standard.
    """

    blob_name: str
    """The name of the blob (file) associated with the changed text."""
    char_start: int
    """The starting character index of the change (inclusive)."""
    char_end: int
    """The ending character index of the change (exclusive)."""


def recent_chunk_to_retrieval_chunk(
    recent_change: completion_pb2.ReplacementText,
) -> RetrievalChunk:
    """Converts a recent chunk to a retrieval chunk."""
    return RetrievalChunk(
        blob_name=recent_change.blob_name,
        path=recent_change.path,
        text=recent_change.replacement_text,
        char_start=recent_change.char_start,
        char_end=recent_change.char_end,
        chunk_index=None,
        origin=ChunkOrigin.RECENCY_RETRIEVER.value,
    )


class RecencyRetriever(Retriever):
    """A retriever that uses recency signals."""

    def __init__(
        self,
        num_results: int,
        internal_retriever: Retriever,
        blob_cache: LRUContentCache[str],
        tab_switch_and_git_diff_retrieval_enabled: bool,
        ri_builder: Optional[
            retriever_request_insight_builder.RetrieverRequestInsightBuilder
        ] = None,
    ):
        """Construct a new retriever.

        Args:
            num_results: The maximal number of chunks to return
            internal_retriever: Internal retriever to delegate chunk retrieval based on blobs
            blob_cache: A cache wrapper around content manager client to read blobs.
            tab_switch_and_git_diff_retrieval_enabled: Feature flag for using git diff and tab switch signals.
            ri_builder: Optional request insight builder
        """
        self._internal_retriever = internal_retriever
        self._chunk_cache = blob_cache
        self._num_results = num_results
        self._request_insight_builder = ri_builder
        self.tab_switch_and_git_diff_retrieval_enabled = (
            tab_switch_and_git_diff_retrieval_enabled
        )

    def _get_git_diffs(
        self,
        recency_info: completion_pb2.RecencyInfo,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> dict[str, list[GitDiffChunk]]:
        result = {}

        git_diff_content_blob_names: dict[str, str] = {}
        for diff_info in recency_info.git_diff_file_info:
            git_diff_content_blob_names[diff_info.content_blob_name] = (
                diff_info.file_blob_name
            )

        tenant_id = auth_info.tenant_id if auth_info.tenant_id else ""
        for file_info in recency_info.git_diff_file_info:
            result[file_info.file_blob_name] = []

            diff_content_chunk: Optional[str] = next(
                iter(
                    self._chunk_cache.get(
                        [(tenant_id, ContentKey(file_info.content_blob_name))],
                        request_context,
                        auth_info,
                    )
                )
            )
            if diff_content_chunk is None:
                log.info(
                    "No diff content chunk found for file blob %s",
                    file_info.content_blob_name,
                )
                continue
            file_content_chunk: Optional[str] = next(
                iter(
                    self._chunk_cache.get(
                        [(tenant_id, ContentKey(file_info.file_blob_name))],
                        request_context,
                        auth_info,
                    )
                )
            )
            if file_content_chunk is None:
                log.info(
                    "No file content chunk found for file blob %s",
                    file_info.file_blob_name,
                )
                continue

            parsed_diffs = _extract_diff_range(diff_content_chunk, file_content_chunk)

            try:
                for parsed_diff in parsed_diffs:
                    diff_chunk = GitDiffChunk(
                        char_start=parsed_diff.target_char_start,
                        char_end=parsed_diff.target_char_end,
                        blob_name=file_info.file_blob_name,
                    )
                    result[file_info.file_blob_name].append(diff_chunk)

            except Exception:
                log.warn(
                    "Failed to parse git diff for diff_blob %s and file blob %s",
                    file_info.content_blob_name,
                    file_info.file_blob_name,
                    exc_info=True,
                )

        return result

    def _chunk_overlap_with_git_diff(
        self,
        grouped_git_diff_by_blob_name: dict[str, list[GitDiffChunk]],
        retrieved_chunk: RetrievalChunk,
    ) -> bool:
        """Check if the chunk overlaps with any git diff chunk."""
        if retrieved_chunk.blob_name not in grouped_git_diff_by_blob_name:
            return False

        overlap = find_first_overlap(
            retrieved_chunk=retrieved_chunk,
            overlap_chunks=grouped_git_diff_by_blob_name[retrieved_chunk.blob_name],
            predicate=partial_overlap_predicate,
        )
        return overlap is not None

    def _retrieve_from_git_diff(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        recency_info: completion_pb2.RecencyInfo,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> RetrievalResult:
        """
        Retrieve chunks from git diff signals.

         - extracts from blobs that were recently visited (tab switch) or part of the next commit (git diff)
         - compares retrieved chunks against git diff content.
         - prioritizes chunks that have a git diff.
        """
        if not self.tab_switch_and_git_diff_retrieval_enabled:
            return RetrievalResult(
                retrieved_chunks=[], missing_blob_names=[], checkpoint_not_found=False
            )

        tab_switch_blob_names = list(
            set(event.file_blob_name for event in recency_info.tab_switch_events)
        )

        git_diff_blob_names = list(
            set(
                diff.file_blob_name
                for diff in recency_info.git_diff_file_info
                if diff.file_blob_name is not None
            )
        )

        for git_diff in recency_info.git_diff_file_info:
            log.info(
                "Found git diff file info: %s => %s",
                git_diff.file_blob_name,
                git_diff.content_blob_name,
            )

        input_with_git_diff_blobs = dataclasses.replace(
            input_,
            blobs=Blobs.from_blob_names(tab_switch_blob_names + git_diff_blob_names),
        )
        result: RetrievalResult = self._internal_retriever.retrieve(
            input_with_git_diff_blobs,
            request_context,
            auth_info,
            executor,
        )

        grouped_git_diff = {}
        try:
            grouped_git_diff = self._get_git_diffs(
                recency_info, request_context, auth_info
            )
        except Exception:
            log.warn("Failed retrieving from git_diff", exc_info=True)

        git_diff_only = []
        tab_switch_only = []
        git_diff_and_tab_switch = []

        for dense_chunk in result.get_retrieved_chunks():
            chunk = dataclasses.replace(
                dense_chunk, origin=ChunkOrigin.RECENCY_RETRIEVER_GIT_DIFF.value
            )
            git_diff_overlap = self._chunk_overlap_with_git_diff(
                grouped_git_diff, chunk
            )
            if git_diff_overlap and chunk.blob_name in tab_switch_blob_names:
                git_diff_and_tab_switch.append(chunk)
            elif chunk.blob_name in git_diff_blob_names:
                git_diff_only.append(chunk)
            elif chunk.blob_name in tab_switch_blob_names:
                tab_switch_only.append(chunk)
            else:
                log.debug(  # e.g. if from a git blob, but no overlap
                    "Ignoring chunk: %s", chunk.blob_name
                )

        log.info(
            "Sorted chunks by different signals:  %d git_diff_and_tab_switch, %d git_diff_only, %d tab_switch_only",
            len(git_diff_and_tab_switch),
            len(git_diff_only),
            len(tab_switch_only),
        )

        all_chunks = git_diff_and_tab_switch + git_diff_only + tab_switch_only

        return RetrievalResult(
            retrieved_chunks=all_chunks,
            missing_blob_names=result.get_missing_blob_names(),
            checkpoint_not_found=result.get_checkpoint_not_found(),
        )

    def retrieve(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> RetrievalResult:
        """Returns a tuple of retrieved chunks and unknown blob names.

        Args:
            input_: The retrieval input from the user request.
            request_context: The request context used for tracking, metrics and authentication.
            executor: If present, an concurrent executor that can be used by the
                retriever for concurrency. If absent, the retriever should run
                synchronously.
        """
        recency_info: Optional[completion_pb2.RecencyInfo] = input_.recency_info

        if recency_info is None:
            return RetrievalResult(
                retrieved_chunks=[], missing_blob_names=[], checkpoint_not_found=False
            )

        if executor is None:
            executor = dummy_executor.DummyExecutor()

        log.info(
            "Retriever got recency signals: %d tab_switch_events, %d git_diff_file_info, %d recent_changes",
            len(recency_info.tab_switch_events),
            len(recency_info.git_diff_file_info),
            len(recency_info.recent_changes),
        )

        recent_changes_chunks = [
            recent_chunk_to_retrieval_chunk(recent_change=recent_change)
            for recent_change in recency_info.recent_changes[:10]
        ]

        result = self._retrieve_from_git_diff(
            input_, request_context, auth_info, recency_info, executor
        )

        chunks = recent_changes_chunks + list(result.get_retrieved_chunks())

        chunks = chunks[: self._num_results]

        result = RetrievalResult(
            retrieved_chunks=chunks,
            missing_blob_names=result.get_missing_blob_names(),
            checkpoint_not_found=result.get_checkpoint_not_found(),
        )

        # We rely on the caller to wait for the executor to terminate all calls.
        if self._request_insight_builder:
            executor.submit(
                self._request_insight_builder.record_retrieval_result,
                retrieval_type=request_insight_pb2.RetrievalType.RECENCY,
                query_prompt=[],
                embedder_tokenizer=None,
                retrieved_chunks=result.get_retrieved_chunks(),  # creates a fresh iterator; does not wait for retrieval to complete
                request_context=request_context,
                auth_info=auth_info,
            )

        return result

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> FindMissingResult:
        return self._internal_retriever.find_missing(
            blob_names, request_context, executor
        )


# Monkey-patch the unidiff regexp so they better handle paths with spaces in them.
#
# Motivation:
# As-is, unidiff cannot handle patches generated by git-diff that look like this:
#
#    diff --git a/name has spaces.txt a/name has spaces.txt
#    deleted file mode 100644
#    index e69de29..0000000
#    diff --git b/renamed file name.txt b/renamed file name.txt
#    new file mode 100644
#    index 0000000..7898192
#    --- /dev/null
#    +++ b/renamed file name.txt
#    @@ -0,0 +1 @@
#    +a
#
# It incorrectly parses the "diff --git" lines, extracting the wrong path from
# them, and then gets confused when it gets to the "+++" line and crashes
# because the paths don't match.
#
# Solution:
# This new regexp makes the assumption that there are no spaces in the extension
# of the filename. This is then used to figure out which is the first and which
# is the second file.
#
# The same logic is applied to the case with a/, b/ prefixes, and to the case
# without these prefixes.
#
unidiff.patch.RE_DIFF_GIT_HEADER = re.compile(
    r"^diff --git (?P<source>a/[^\t\n\.]+\.?\S*) (?P<target>b/[^\t\n\.]+\.?\S*)"
)
unidiff.patch.RE_DIFF_GIT_HEADER_NO_PREFIX = re.compile(
    r"^diff --git (?P<source>[^\t\n\.]+\.?\S*) (?P<target>[^\t\n\.]+\.?\S*)"
)


@dataclasses.dataclass(frozen=True)
class _DiffRange:
    """Represents a patch to a file using its character span."""

    old_path: str
    new_path: Optional[str]

    target_line_start: int  # 0 index based
    target_line_end: int  # first line AFTER the diff.

    # using char_start and char_end aligned to augment's standard.
    target_char_start: int
    target_char_end: int


def _get_char_span(start_line_number, end_line_number, file_content) -> tuple[int, int]:
    """Convert line numbers to character spans."""
    lines = file_content.split(
        "\n"
    )  # only taking unix line endings into account for now.
    char_size = 0
    char_start = 0
    char_end = 0

    assert end_line_number > start_line_number
    assert start_line_number >= 0

    if len(lines) < end_line_number:
        log.info(
            "End line number exceeds content. end_line_number=%d, len(lines)=%d",
            end_line_number,
            len(lines),
        )
        raise Exception("End line number exceeds content")

    for line_number, line in enumerate(lines, 0):
        if line_number == start_line_number:
            char_start = char_size

        char_size += len(line) + 1

        if (
            line_number + 1 == end_line_number
        ):  # end_line_number might be outside of file content size
            char_end = char_size
            break

    if char_end < char_start:
        log.info(
            "Cannot find line number. char_end=%d, char_start=%d", char_end, char_start
        )
        raise Exception("Cannot find line number")

    return char_start, char_end


def _extract_diff_range(
    diff_content: str, most_recent_file_content: str
) -> Iterator[_DiffRange]:
    """Parse a diff string into a list of Patch objects.
    Converts line numbers to character spans.

    Current implementation relies on the assumption the git diff was generated on the provided file content.
    To achieve this, we manually wait for the file to be indexed.

    Args:
         diff_content: The diff string to parse.
         most_recent_file_content: The most recent version of the file.

     Returns:
         An iterator of Diff objects.
    """

    patch_set = PatchSet(diff_content)

    patch: PatchedFile
    for patch in patch_set:
        hunk: Hunk
        for hunk in patch:
            line_end = (
                hunk.target_start + hunk.target_length
            )  # first line AFTER the diff.

            # We do not care about changes that just removed code.
            if hunk.target_length == 0:
                continue

            try:
                char_start, char_end = _get_char_span(
                    hunk.target_start, line_end, most_recent_file_content
                )

            except Exception:
                log.warn(
                    "Failed to get char span start=%d, length=%d",
                    hunk.target_start,
                    hunk.target_length,
                    exc_info=True,
                )
                continue

            yield _DiffRange(
                old_path=patch.source_file,
                new_path=patch.target_file,
                target_line_start=hunk.target_start,
                target_line_end=line_end,
                target_char_start=char_start,
                target_char_end=char_end,
            )

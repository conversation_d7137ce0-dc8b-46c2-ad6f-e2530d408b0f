"""Test for the retriever module."""

import threading
from concurrent.futures import ThreadPoolExecutor

import pytest

from base.prompt_format.recency_info import (
    GitDiffFileInfo,
    RecencyInfo,
    ReplacementText,
    TabSwitchEvent,
)
from services.completion_host import completion_pb2
from services.lib.retrieval.retriever import (
    RetrievalChunk,
    RetrievalResult,
    from_chunk_pb,
    from_recency_info_pb,
    parse_retrieval_chunk_from_bytes_builder,
)
from services.embeddings_indexer import chunk_pb2


def test_create_retrieval_result():
    result = RetrievalResult(
        retrieved_chunks=[
            RetrievalChunk(
                text="foo",
                path="foo.py",
                char_start=0,
                char_end=3,
                blob_name=None,
                chunk_index=None,
            ),
        ],
        missing_blob_names=["missing-1", "missing-2"],
        checkpoint_not_found=False,
    )
    assert list(result.get_retrieved_chunks()) == [
        RetrievalChunk(
            text="foo",
            path="foo.py",
            char_start=0,
            char_end=3,
            blob_name=None,
            chunk_index=None,
        ),
    ]
    assert set(result.get_missing_blob_names()) == set(["missing-1", "missing-2"])


def test_retrieval_result_order_sequential():
    # Create a retrieval result with an iterator of numbered chunks.
    chunks = [
        RetrievalChunk(
            text=f"chunk-{i}",
            path="foo.py",
            char_start=0,
            char_end=len(f"chunk-{i}"),
            blob_name=None,
            chunk_index=None,
        )
        for i in range(10)
    ]

    rr = RetrievalResult(
        retrieved_chunks=iter(chunks),
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    # Call twice. Each call should get the chunks in order.
    for _ in range(2):
        assert list(rr.get_retrieved_chunks()) == chunks


def test_retrieval_result_order_concurrent():
    # Create a retrieval result with an iterator of numbered chunks.

    chunks = [
        RetrievalChunk(
            text=f"chunk-{i}",
            path="foo.py",
            char_start=0,
            char_end=len(f"chunk-{i}"),
            blob_name=None,
            chunk_index=None,
        )
        for i in range(10)
    ]

    rr = RetrievalResult(
        retrieved_chunks=iter(chunks),
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    # Call with two threads. Each call should get the chunks in order.
    with ThreadPoolExecutor(max_workers=2) as executor:
        num_threads = 2
        results = list(
            executor.map(lambda _: rr.get_retrieved_chunks(), range(num_threads))
        )

        for result in results:
            assert list(result) == chunks


@pytest.mark.parametrize("num_threads", [1, 2, 3])
def test_lazy_retrieval_result(num_threads: int):
    num_chunks = 10
    append_another_chunk = [threading.Event() for _ in range(num_chunks)]

    def input_generator():
        # This loop is run only on demand from the checking threads.
        for i in range(10):
            # The events make sure that we can only generate the chunks as
            # at least one of the checking threads has processed all the
            # other chunks.
            append_another_chunk[i].wait()
            yield RetrievalChunk(
                text=f"chunk-{i}",
                path="foo.py",
                char_start=0,
                char_end=len(f"chunk-{i}"),
                blob_name=None,
                chunk_index=None,
            )

    result = RetrievalResult(
        retrieved_chunks=input_generator(),
        missing_blob_names=[],
        checkpoint_not_found=False,
    )

    def output_thread():
        append_another_chunk[0].set()
        for idx, chunk in enumerate(result.get_retrieved_chunks()):
            assert chunk.text == f"chunk-{idx}"
            append_another_chunk[idx + 1].set()

    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        executor.submit(output_thread)


def test_retrieval_result_missing_blob_names():
    """Test that the missing blob names are buffered and returned fully for each call."""
    retrieved_chunks = [
        RetrievalChunk(
            text=f"chunk-{i}",
            path="foo.py",
            char_start=0,
            char_end=len(f"chunk-{i}"),
            blob_name=None,
            chunk_index=None,
        )
        for i in range(10)
    ]

    missing_blob_names = ["missing-1", "missing-2"]

    result = RetrievalResult(
        retrieved_chunks=retrieved_chunks,
        missing_blob_names=iter(missing_blob_names),
        checkpoint_not_found=False,
    )

    # Call twice. Each call should get the same missing blob names.
    for _ in range(2):
        assert set(result.get_missing_blob_names()) == set(missing_blob_names)


def test_parse_retrieval_chunk_from_bytes_builder_invalid_origin():
    """Test that the builder raises an exception if the origin is invalid."""
    with pytest.raises(ValueError):
        parse_retrieval_chunk_from_bytes_builder("invalid-origin")


def test_from_chunk_pb():
    chunk_pb = chunk_pb2.Chunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_offset=0,
        length=len("Hello World\n"),
        line_offset=0,
        length_in_lines=1,
    )
    chunk = from_chunk_pb(chunk_pb, "blob-1", 0, "origin", 0.5)
    assert chunk.text == "Hello World\n"
    assert chunk.path == "foo/bar.py"
    assert chunk.char_start == 0
    assert chunk.char_end == len("Hello World\n")
    assert chunk.blob_name == "blob-1"
    assert chunk.chunk_index == 0
    assert chunk.origin == "origin"
    assert chunk.score == 0.5


def test_from_chunk_pb_with_docset():
    chunk_pb = chunk_pb2.Chunk(
        text="Hello World\n",
        path="foo/bar.py",
        char_offset=0,
        length=len("Hello World\n"),
        line_offset=0,
        length_in_lines=1,
        documentation_metadata=chunk_pb2.DocumentationMetadata(
            name="Git",
            page_id="1",
            headers=["#Header1", "#Header2"],
            source_id="docset://Git",
        ),
    )
    chunk = from_chunk_pb(chunk_pb, "blob-1", 0, "origin", 0.5)
    assert chunk.text == "Hello World\n"
    assert chunk.path == "foo/bar.py"
    assert chunk.char_start == 0
    assert chunk.char_end == len("Hello World\n")
    assert chunk.blob_name == "blob-1"
    assert chunk.chunk_index == 0
    assert chunk.origin == "origin"
    assert chunk.score == 0.5
    m = chunk.documentation_metadata
    assert m is not None
    assert m.name == "Git"
    assert m.page_id == "1"
    assert m.headers == ["#Header1", "#Header2"]
    assert m.source_id == "docset://Git"


def test_from_recency_info_pb():
    recency_info_pb = completion_pb2.RecencyInfo(
        tab_switch_events=[
            completion_pb2.TabSwitchEvent(
                path="/path/to/file1.py", file_blob_name="blob1"
            ),
            completion_pb2.TabSwitchEvent(
                path="/path/to/file2.py", file_blob_name="blob2"
            ),
        ],
        git_diff_file_info=[
            completion_pb2.GitDiffFileInfo(
                content_blob_name="content1", file_blob_name="file1"
            ),
            completion_pb2.GitDiffFileInfo(
                content_blob_name="content2", file_blob_name="file2"
            ),
        ],
        recent_changes=[
            completion_pb2.ReplacementText(
                blob_name="blob1",
                path="/path/to/file1.py",
                char_start=0,
                char_end=10,
                replacement_text="new text",
                present_in_blob=True,
                expected_blob_name="expected_blob1",
            ),
        ],
    )

    expected = RecencyInfo(
        tab_switch_events=[
            TabSwitchEvent(path="/path/to/file1.py", file_blob_name="blob1"),
            TabSwitchEvent(path="/path/to/file2.py", file_blob_name="blob2"),
        ],
        git_diff_info=[
            GitDiffFileInfo(content_blob_name="content1", file_blob_name="file1"),
            GitDiffFileInfo(content_blob_name="content2", file_blob_name="file2"),
        ],
        recent_changes=[
            ReplacementText(
                blob_name="blob1",
                path="/path/to/file1.py",
                char_start=0,
                char_end=10,
                replacement_text="new text",
                present_in_blob=True,
                expected_blob_name="expected_blob1",
            ),
        ],
    )

    result = from_recency_info_pb(recency_info_pb)
    assert result == expected

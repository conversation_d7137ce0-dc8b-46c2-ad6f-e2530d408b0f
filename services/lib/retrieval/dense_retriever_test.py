"""Test for the dense retriever module."""

from typing import Optional, Sequence
from unittest import mock

import numpy
import pytest

import base.tokenizers
import services.embeddings_indexer.chunk_pb2 as chunk_pb2
from base import prompt_format_retrieve
from base.blob_names.python.blob_names import Blobs
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.prompt_format.chunk_origin import ChunkOrigin
from services.lib.retrieval.dense_retriever import DenseRetriever
from services.lib.retrieval.retriever import RetrievalInput
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.embedder_host.client.client import (
    EmbedderClientProtocol,
    EmbedderRequestType,
)
from services.embeddings_search_host.client.client import (
    EmbeddingsFindMissingResult,
    EmbeddingsSearchChunksResult,
    EmbeddingsSearchClientProtocol,
)
from services.integrations.docset.client.client import <PERSON><PERSON><PERSON><PERSON>lient
from services.integrations.docset import docset_pb2
from services.lib.request_context.request_context import RequestContext


# pylint: disable=protected-access


def tokenize_and_numpy(tokenizer, text: str) -> numpy.ndarray:
    """Tokenize a text and return it as numpy array."""
    return numpy.array(tokenizer.tokenize_unsafe(text)).astype(numpy.int32)


def gen_blob_name(s: str) -> str:
    """Convert a string to a hex-string blob name."""
    return s.encode("utf-8").hex()


class MockEmbedderClient(EmbedderClientProtocol):
    """A mock implementation of the embedder client protocol."""

    def __init__(self):
        self.calls = []

    def calculate_embedding(
        self,
        tokens: list[list[int]],
        request_context: RequestContext,
        timeout: float = 30,
        request_type: EmbedderRequestType = EmbedderRequestType.DEFAULT,
        source_namespace: str | None = None,
    ) -> numpy.ndarray:
        del timeout, source_namespace, request_type, request_context
        self.calls.append(tokens)
        return numpy.ones([len(tokens), 1024])


class MockEmbeddingsSearchClient(EmbeddingsSearchClientProtocol):
    """Mock of the embeddings search client."""

    def __init__(self):
        self.search_calls = []
        self.search_calls_blobs = []
        self.find_missing_calls = []
        self.known_blob_names = []

    def search_chunks(
        self,
        query: numpy.ndarray,
        blobs: Sequence[Blobs],
        num_results: int,
        transformation_key: str,
        request_context: RequestContext,
        search_timeout_ms: int = 1000,
        sequence_id: Optional[int] = None,
        timeout: float = 30,
    ) -> EmbeddingsSearchChunksResult:
        self.search_calls.append((query))
        self.search_calls_blobs.append(blobs)

        blob_name = blobs[0].added[0]

        content = chunk_pb2.Chunk(
            text="Hello World",
            path="foo/bar.py",
            char_offset=0,
            length=len("Hello World"),
            line_offset=0,
            length_in_lines=1,
        )
        chunk = EmbeddingsSearchChunksResult.RetrievalChunk(
            content=content.SerializeToString(),
            score=0.5,
            blob_name=blob_name.hex(),
            chunk_index=0,
            metadata=[],
        )

        return EmbeddingsSearchChunksResult(
            chunks=[chunk],
            missing_blobs=EmbeddingsSearchChunksResult.MissingBlobs(
                missing_blob_names=[],
                checkpoint_not_found=False,
            ),
        )

    def find_missing(
        self,
        blob_names: Sequence[str],
        transformation_key: str,
        sub_key: str,
        request_context: RequestContext,
    ) -> EmbeddingsFindMissingResult:
        self.find_missing_calls.append(blob_names)
        return EmbeddingsFindMissingResult(
            missing_blob_names=[
                blob_name
                for blob_name in blob_names
                if blob_name not in self.known_blob_names
            ],
        )


@pytest.fixture
def content_manager() -> ContentManagerClient:
    chunk = chunk_pb2.Chunk(
        text="Hello World",
        path="foo/bar.py",
        char_offset=0,
        length=len("Hello World"),
        line_offset=0,
        length_in_lines=1,
    )
    client = mock.MagicMock(ContentManagerClient)
    client.batch_download_all.return_value = [(chunk.SerializeToString(), {})]
    return client


def test_dense_retriever(content_manager: ContentManagerClient):
    """Default test for the dense retrieval system."""
    tokenizer = base.tokenizers.create_tokenizer_by_name("fim")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient()
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key="dr-1",
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )

    blob_name = gen_blob_name("123")
    blobs = Blobs.from_blob_names([blob_name])
    request_context = RequestContext.create()

    result = dr.retrieve(
        RetrievalInput(
            prompt_input=CompletionRetrieverPromptInput(
                prefix="Hi AI!",
                suffix="",
                path="foo/foo.py",
            ),
            blobs=[blobs],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 1
    assert retrieved_chunks[0].path == "foo/bar.py"
    assert retrieved_chunks[0].text == "Hello World"
    content_manager.batch_download_all.assert_not_called()
    assert len(embedder_rpc_client.calls) == 1
    # in particular, the prompt doesn't contain the path, but it is suffixed by the endofquery token
    # and prefixed by sos token
    actual_tokens = embedder_rpc_client.calls[0][0]
    expected_text = "foo/foo.py<|fim-prefix|>Hi AI!<|ret-endofquery|>"
    assert actual_tokens == tokenizer.tokenize_unsafe(
        expected_text
    ), f"Got {tokenizer.detokenize(actual_tokens)}. Expected {expected_text}"
    assert len(embeddings_search_rpc_client.search_calls) == 1


def test_dense_retriever_multi_blobs(content_manager: ContentManagerClient):
    """Validates th behavior of the dense retrieval system with multiple blobs."""
    tokenizer = base.tokenizers.create_tokenizer_by_name("fim")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient()
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key="dr-1",
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )

    blob_name1 = gen_blob_name("123")
    blob_name2 = gen_blob_name("124")
    blobs = [Blobs.from_blob_names([blob_name1]), Blobs.from_blob_names([blob_name2])]
    request_context = RequestContext.create()

    result = dr.retrieve(
        RetrievalInput(
            prompt_input=CompletionRetrieverPromptInput(
                prefix="Hi AI!",
                suffix="",
                path="foo/foo.py",
            ),
            blobs=blobs,
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )
    assert len(list(result.get_missing_blob_names())) == 0
    retrieved_chunks = list(result.get_retrieved_chunks())
    assert len(retrieved_chunks) == 1
    assert retrieved_chunks[0].path == "foo/bar.py"
    assert retrieved_chunks[0].text == "Hello World"
    content_manager.batch_download_all.assert_not_called()
    assert len(embedder_rpc_client.calls) == 1
    # in particular, the prompt doesn't contain the path, but it is suffixed by the endofquery token
    # and prefixed by sos token
    actual_tokens = embedder_rpc_client.calls[0][0]
    expected_text = "foo/foo.py<|fim-prefix|>Hi AI!<|ret-endofquery|>"
    assert actual_tokens == tokenizer.tokenize_unsafe(
        expected_text
    ), f"Got {tokenizer.detokenize(actual_tokens)}. Expected {expected_text}"
    assert len(embeddings_search_rpc_client.search_calls) == 1
    assert len(embeddings_search_rpc_client.search_calls_blobs) == 1
    assert len(embeddings_search_rpc_client.search_calls_blobs[0]) == 2


@pytest.mark.parametrize(
    "retrieval_input, expected, expecetd_text",
    [
        (
            RetrievalInput(
                prompt_input=CompletionRetrieverPromptInput(
                    prefix="Hi AI!",
                    suffix="",
                    path="foo/foo.py",
                ),
                blobs=Blobs.from_fake_blob_names(["123"]),
            ),
            [21943, 14, 21943, 13, 9078, 50327, 17250, 9552, 0, 50326, 50305],
            "foo/foo.py<|fim-prefix|>Hi AI!<|fim-suffix|><|ret-endofquery|>",
        ),
        (
            RetrievalInput(
                prompt_input=CompletionRetrieverPromptInput(
                    prefix="Hi AI!",
                    suffix="some suffix",
                    path="foo/foo.py",
                ),
                blobs=Blobs.from_fake_blob_names(["123"]),
            ),
            [
                21943,
                14,
                21943,
                13,
                9078,
                50327,
                17250,
                9552,
                0,
                50326,
                11246,
                35488,
                50305,
            ],
            "foo/foo.py<|fim-prefix|>Hi AI!<|fim-suffix|>some suffix<|ret-endofquery|>",
        ),
    ],
)
def test_get_prompt_tokens(
    content_manager: ContentManagerClient, retrieval_input, expected, expecetd_text
):
    tokenizer = base.tokenizers.create_tokenizer_by_name("fim")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient()
    query_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6.16.1-query-embedding", tokenizer=tokenizer
        )
    )
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key="dr-1",
        num_results=32,
        query_prompt_formatter=query_prompt_formatter,
        search_timeout_ms=1000,
    )
    tokens = dr._get_prompt_tokens(retrieval_input)
    assert tokens == expected
    assert tokenizer.detokenize(tokens) == expecetd_text


def test_find_missing(content_manager: ContentManagerClient):
    """Test of the dense retrieval system's find-missing endpoint."""
    tokenizer = base.tokenizers.create_tokenizer_by_name("fim")
    embedder_rpc_client = MockEmbedderClient()
    embeddings_search_rpc_client = MockEmbeddingsSearchClient()
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    dr = DenseRetriever(
        embedder_client=embedder_rpc_client,
        embeddings_search_client=embeddings_search_rpc_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key="dr-1",
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )

    valid_blob_names = ["blob-0", "blob-1"]
    bogus_blob_names = ["bogus-100", "bogus-101"]
    embeddings_search_rpc_client.known_blob_names = valid_blob_names
    to_query = valid_blob_names + bogus_blob_names
    request_context = RequestContext.create()
    result = dr.find_missing(to_query, request_context=request_context)
    assert len(embeddings_search_rpc_client.find_missing_calls) == 1
    assert len(result.missing_blob_names) == len(bogus_blob_names)
    for blob_name in bogus_blob_names:
        assert blob_name in result.missing_blob_names

"""A class to simulate a ThreadPoolExecutor but without actually running tasks in parallel."""

import concurrent.futures


class DummyExecutor(concurrent.futures.Executor):
    """A class to simulate a ThreadPoolExecutor but without actually running tasks in parallel."""

    def __init__(self, *args, **kwargs):
        pass

    def submit(self, fn, *args, **kwargs):  # pylint: disable=arguments-differ
        """Submit a new task to the thread pool."""
        f = concurrent.futures.Future()
        f.set_result(fn(*args, **kwargs))
        return f

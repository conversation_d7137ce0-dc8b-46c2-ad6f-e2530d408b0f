"""Tests for recency retrieval."""

import concurrent.futures
import os
import re
from pathlib import Path
from typing import Optional, Sequence
from unittest import mock

import grpc
import pytest
from syrupy.extensions.amber import AmberSnapshotExtension
from syrupy.location import PyTestLocation

from base.blob_names.python.blob_names import Blobs
from base.prompt_format.chunk_origin import ChunkOrigin
from base.prompt_format_retrieve.prompt_formatter import CompletionRetrieverPromptInput
from services.completion_host import completion_pb2  # type: ignore
from services.lib.retrieval.recency_retriever import (
    RecencyRetriever,
    _extract_diff_range,
    _get_char_span,
)
from services.lib.retrieval.retriever import (
    FindMissingR<PERSON>ult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.content_manager.client.content_cache import LRUContentCache
from services.content_manager.client.content_manager_client import (
    ContentManagerClient,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext

PREFIX = "def hello():"
MISSING_BLOB_NAME = "missing"
MISSING_BLOB_NAME_HEX = "missing".encode("utf-8").hex()
RECENCY_CHUNK_ORIGIN = ChunkOrigin.RECENCY_RETRIEVER.value
GIT_DIFF_ORIGIN = ChunkOrigin.RECENCY_RETRIEVER_GIT_DIFF.value


class MockRetriever(Retriever):
    """Mock retriever."""

    def retrieve(
        self,
        input_: RetrievalInput[CompletionRetrieverPromptInput],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> RetrievalResult:
        assert input_.blobs.baseline_checkpoint_id is None
        assert input_.prompt_input.prefix == PREFIX
        chunks = []
        missing_blob_names = []
        for i, blob_name in enumerate(input_.blobs.added):
            blob_name_hex = blob_name.hex()
            if blob_name_hex == MISSING_BLOB_NAME_HEX:
                missing_blob_names.append(MISSING_BLOB_NAME_HEX)
                continue
            for j in range(2):
                text = f"chunk-{i}-{j}"
                chunk = RetrievalChunk(
                    text=text,
                    path=f"foo-{blob_name}.py",
                    char_start=0,
                    char_end=len(text),
                    blob_name=blob_name_hex,
                    chunk_index=None,
                    origin="mock-retriever",
                )
                chunks.append(chunk)
        return RetrievalResult(
            retrieved_chunks=chunks,
            missing_blob_names=missing_blob_names,
            checkpoint_not_found=False,
        )

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[concurrent.futures.Executor] = None,
    ) -> FindMissingResult:
        return FindMissingResult(missing_blob_names=[])


class MockCache(LRUContentCache[str]):
    """Mock cache."""

    def __init__(self):
        super().__init__(
            content_manager=mock.MagicMock(ContentManagerClient),
            transform_from_bytes=lambda _, bytes: bytes.decode("utf-8"),
            max_size=10,
            max_elem_size=None,
            max_missing_entries_to_download=None,
        )

    def get_missing(self, keys: Sequence[str], context: RequestContext) -> list[str]:
        return []


def test_tab_switch_retrieval():
    """
    Tests we only consider tab switch blobs in the retrieval, even when the project has more blobs.
    Also tests a missing blob from tab switch appears in the missing blob list.
    """

    internal_retriever = MockRetriever()
    retriever = RecencyRetriever(
        internal_retriever=internal_retriever,
        blob_cache=MockCache(),
        tab_switch_and_git_diff_retrieval_enabled=True,
        num_results=10,
    )
    recency_info = completion_pb2.RecencyInfo(
        tab_switch_events=[
            completion_pb2.TabSwitchEvent(
                path="1.py", file_blob_name="1".encode("utf-8").hex()
            ),
            completion_pb2.TabSwitchEvent(
                path="2.py", file_blob_name="2".encode("utf-8").hex()
            ),
            completion_pb2.TabSwitchEvent(
                path="missing.py", file_blob_name=MISSING_BLOB_NAME_HEX
            ),
        ],
    )

    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            prefix=PREFIX,
            suffix="",
            path="",
        ),
        blobs=Blobs.from_fake_blob_names(["1", "2", "3", "4", MISSING_BLOB_NAME]),
        recency_info=recency_info,
    )

    result: RetrievalResult = retriever.retrieve(
        input_, RequestContext.create(), auth_info=None
    )
    assert result.get_missing_blob_names() == [MISSING_BLOB_NAME_HEX]
    assert all(
        [chunk.origin == GIT_DIFF_ORIGIN for chunk in result.get_retrieved_chunks()]
    )
    returned_blobs = [chunk.blob_name for chunk in result.get_retrieved_chunks()]
    assert set(returned_blobs) == set(
        ["1".encode("utf-8").hex(), "2".encode("utf-8").hex()]
    )


def test_recent_changes():
    internal_retriever = MockRetriever()
    retriever = RecencyRetriever(
        internal_retriever=internal_retriever,
        blob_cache=MockCache(),
        tab_switch_and_git_diff_retrieval_enabled=False,
        num_results=10,
    )
    recency_info = completion_pb2.RecencyInfo(
        recent_changes=[
            completion_pb2.ReplacementText(
                blob_name="1",
                path="1.py",
                replacement_text="this is replacement test",
                char_start=0,
                char_end=10,
                present_in_blob=False,
            ),
        ],
    )

    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            prefix=PREFIX,
            suffix="",
            path="",
        ),
        blobs=Blobs(),
        recency_info=recency_info,
    )

    result: RetrievalResult = retriever.retrieve(
        input_, RequestContext.create(), auth_info=None
    )

    assert list(result.retrieved_chunks.get_iterable()) == [
        RetrievalChunk(
            text="this is replacement test",
            path="1.py",
            char_start=0,
            char_end=10,
            blob_name="1",
            chunk_index=None,
            origin=RECENCY_CHUNK_ORIGIN,
        ),
    ]


def test_num_results():
    internal_retriever = MockRetriever()
    num_results = 1
    retriever = RecencyRetriever(
        internal_retriever=internal_retriever,
        blob_cache=MockCache(),
        tab_switch_and_git_diff_retrieval_enabled=True,
        num_results=num_results,
    )
    recency_info = completion_pb2.RecencyInfo(
        tab_switch_events=[
            completion_pb2.TabSwitchEvent(
                path="1.py", file_blob_name="1".encode("utf-8").hex()
            ),
            completion_pb2.TabSwitchEvent(
                path="2.py", file_blob_name="2".encode("utf-8").hex()
            ),
            completion_pb2.TabSwitchEvent(
                path="missing.py", file_blob_name=MISSING_BLOB_NAME_HEX
            ),
        ],
    )

    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            prefix=PREFIX,
            suffix="",
            path="",
        ),
        blobs=Blobs.from_fake_blob_names(["1", "2", "3", "4", MISSING_BLOB_NAME]),
        recency_info=recency_info,
    )

    result: RetrievalResult = retriever.retrieve(
        input_, RequestContext.create(), auth_info=None
    )
    assert len(list(result.get_retrieved_chunks())) == num_results
    assert all(
        [chunk.origin == GIT_DIFF_ORIGIN for chunk in result.get_retrieved_chunks()]
    )


def test_missing_recency_info():
    internal_retriever = MockRetriever()
    retriever = RecencyRetriever(
        internal_retriever=internal_retriever,
        blob_cache=MockCache(),
        tab_switch_and_git_diff_retrieval_enabled=False,
        num_results=10,
    )
    input_ = RetrievalInput(
        CompletionRetrieverPromptInput(
            prefix=PREFIX,
            suffix="",
            path="",
        ),
        blobs=Blobs.from_fake_blob_names(["1", "2", "3", "4", MISSING_BLOB_NAME]),
        recency_info=None,
    )
    result: RetrievalResult = retriever.retrieve(
        input_, RequestContext.create(), auth_info=None
    )
    assert list(result.get_retrieved_chunks()) == []
    assert list(result.get_missing_blob_names()) == []


### Git Diff Tests


## Setup to enable snapshots update outside of bazel.
## This will move to a shared location in a followup PR.
## From documentation: https://github.com/tophat/syrupy/blob/main/tests/examples/test_custom_snapshot_directory.py
SNAPSHOT_DIRNAME = "__snapshots__"


class DifferentDirectoryExtension(AmberSnapshotExtension):
    @classmethod
    def dirname(cls, *, test_location: "PyTestLocation") -> str:
        build_dir = os.environ.get("BUILD_WORKSPACE_DIRECTORY")

        if build_dir is None:  # this is the path we expect to take in bazel test
            test_dir = Path(test_location.filepath).parent
            return str(test_dir.joinpath(SNAPSHOT_DIRNAME))

        runfiles_dir = os.environ["RUNFILES_DIR"]
        bazel_workspace = os.environ["BAZEL_WORKSPACE"]
        test_rel_path = os.path.relpath(
            os.path.join(test_location.filepath),
            os.path.join(runfiles_dir, bazel_workspace),
        )

        file_build_path = os.path.join(build_dir, test_rel_path)

        result = str(Path(file_build_path).parent.joinpath(SNAPSHOT_DIRNAME))
        return result


@pytest.fixture
def snapshot(snapshot):
    return snapshot.use_extension(DifferentDirectoryExtension)


def test_extract_diff_range(snapshot):
    """
    Test the diff parser by feeding it git diffs and checking the output against a snapshot.
    Input preperation simulates client behavior with keys steps:
     - Splitting the diff into files.
     - Provide the most recent file content.


    For easy review - In the snapshot you can see the diff and the resulting character span.
    For ease of calculation, each line = 10 characters.
    """
    DIFF_SANITY_FILE = "services/lib/retrieval/test_resources/diff_parser/sanity.diff"

    # Build a file content with expectations that are easy to calculate.
    lines = []
    for x in range(300):
        lines.append(
            f"line {x:04}"
        )  # every line is 10 charactesr ("line" + space + 4 digit number + newline)
    content = "\n".join(lines)

    # read the git diff, split it into files, and convert line ranges to char ranges.
    with open(DIFF_SANITY_FILE, "r") as f:
        diff_text = f.read()
        # split the git diff input to different files. Similar to how the client does it.
        diff_pieces = re.split(r"\n(?=diff\s+)", diff_text)
        for diff in diff_pieces:
            result = _extract_diff_range(diff, content)
            assert snapshot == {"result": list(result), "diff": diff}


def test_get_char_span():
    file_content = """
This is

a test
file with 58 characters.

and 8 lines.

"""
    file_number_of_lines = len(file_content.split("\n"))
    # git diff guarantees end line > start line
    with pytest.raises(Exception):
        _get_char_span(0, 0, file_content)

    # error on cases file content not long enough
    with pytest.raises(Exception):
        _get_char_span(1, file_number_of_lines + 1, file_content)

    with pytest.raises(Exception):
        _get_char_span(-1, file_number_of_lines, file_content)

    assert _get_char_span(0, 5, file_content) == (0, 42)
    assert _get_char_span(1, 5, file_content) == (1, 42)
    assert _get_char_span(0, 9, file_content) == (0, 58)

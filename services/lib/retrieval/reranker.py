"""Reranker object calling the reranker service"""

import concurrent.futures
from typing import Sequence

import numpy as np
import opentelemetry.trace
from services.lib.retrieval import dummy_executor
import structlog

from base.prompt_format.common import get_request_message_as_text
from base.prompt_format_rerank import Chat<PERSON><PERSON>kerPromptInput, Reranker<PERSON>romptFormatter
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from services.lib.retrieval import (
    retriever_request_insight_builder,
)

from services.lib.retrieval.dense_retriever import get_sub_key
from services.lib.retrieval.retriever import (
    FindMissingR<PERSON>ult,
    RetrievalChunk,
    RetrievalInput,
    RetrievalResult,
    Retriever,
)
from services.content_manager.client.content_cache import LRUContentCache
from services.content_manager.client.content_manager_client import ContentKey
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.reranker.client.client import (
    <PERSON>ranker<PERSON><PERSON><PERSON>rot<PERSON><PERSON>,
    <PERSON>rank<PERSON><PERSON>utput,
    RerankerScores,
)

tracer = opentelemetry.trace.get_tracer(__name__)

logger = structlog.get_logger()


def is_adjacent(chunk1: RetrievalChunk | None, chunk2: RetrievalChunk | None) -> bool:
    """Check if two chunks are adjacent. Return True if either is None."""
    if chunk1 is None or chunk2 is None:
        return True

    lesser_chunk, greater_chunk = (
        (chunk1, chunk2) if chunk1.char_start < chunk2.char_start else (chunk2, chunk1)
    )
    return lesser_chunk.char_end >= greater_chunk.char_start


def join_two_retrieval_chunks(
    chunk1: RetrievalChunk | None, chunk2: RetrievalChunk | None
) -> RetrievalChunk | None:
    """Join two retrieval chunks that are touching or overlapping into one."""
    if chunk1 is None:
        return chunk2
    if chunk2 is None:
        return chunk1
    if chunk1 is None and chunk2 is None:
        return None

    assert chunk1.blob_name == chunk2.blob_name
    lesser_chunk, greater_chunk = (
        (chunk1, chunk2) if chunk1.char_start < chunk2.char_start else (chunk2, chunk1)
    )
    assert (
        lesser_chunk.char_end >= greater_chunk.char_start
    ), f"Chunks are not adjacent: {(chunk1.char_start, chunk1.char_end)}, {(chunk2.char_start, chunk2.char_end)}"

    char_start = min(chunk1.char_start, chunk2.char_start)
    char_end = max(chunk1.char_end, chunk2.char_end)
    num_overlap_chars = min(chunk1.char_end, chunk2.char_end) - max(
        chunk1.char_start, chunk2.char_start
    )
    joined_text = lesser_chunk.text + greater_chunk.text[num_overlap_chars:]

    return RetrievalChunk(
        text=joined_text,
        path=chunk1.path,
        origin=chunk1.origin,
        char_start=char_start,
        char_end=char_end,
        blob_name=chunk1.blob_name,
        chunk_index=None,
    )


class RetrieveThenChunkExpansionReranker(Retriever[ChatRetrieverPromptInput]):
    def __init__(
        self,
        retriever: Retriever[ChatRetrieverPromptInput],
        reranker_client: RerankerClientProtocol,
        reranker_prompt_formatter: RerankerPromptFormatter[ChatRerankerPromptInput],
        transformation_key: str,
        chunk_cache: LRUContentCache[RetrievalChunk],
        ri_builder: retriever_request_insight_builder.RetrieverRequestInsightBuilder
        | None = None,
        num_chunks_to_rerank: int = 128,
        seq_len_per_batch_elem: int | None = None,
    ):
        self._retriever = retriever
        self._reranker_client = reranker_client
        self._reranker_prompt_formatter = reranker_prompt_formatter
        self._chunk_cache = chunk_cache
        self._request_insight_builder = ri_builder

        # Should be the same as retriever
        self._transformation_key = transformation_key

        # hard coded for now
        self._num_neighbors_to_grab_per_side = 2

        self._num_chunks_to_rerank = num_chunks_to_rerank
        self._seq_len_per_batch_elem = seq_len_per_batch_elem

    def _grab_neighbor_chunks(
        self,
        retrieved_chunks: list[RetrievalChunk],
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> tuple[
        dict[tuple[str, int], RetrievalChunk], dict[tuple[str, int], RetrievalChunk]
    ]:
        """Grab neighbor chunks from the cache.

        Args:
            retrieved_chunks: Retrieval chunks to grab neighbor chunks for.
            request_context: Request context.
            auth_info: Auth info.

        Returns:
            left_neighbors: Map of left neighbor chunks, (filepath, chunk_index) -> RetrievalChunk
            right_neighbors: Map of right neighbor chunks, (filepath, chunk_index) -> RetrievalChunk
        """
        with tracer.start_as_current_span("grab neighbor chunks"):
            tenant_id = auth_info.tenant_id if auth_info.tenant_id else ""
            chunk_keys = set()
            for chunk in retrieved_chunks:
                # We do not support getting neighbors for doccsets
                if chunk.documentation_metadata:
                    continue
                for i in range(1, self._num_neighbors_to_grab_per_side + 1):
                    assert chunk.chunk_index is not None
                    assert chunk.blob_name is not None
                    # get left neighbor(s)
                    # If a sub_key is out of bounds, the cache will just return None, which is filtered
                    # out below, so we don't need to check for out of bounds sub_key indices here.
                    chunk_keys.add(
                        (
                            tenant_id,
                            ContentKey(
                                blob_name=chunk.blob_name,
                                transformation_key=self._transformation_key,
                                sub_key=get_sub_key(chunk.chunk_index - i),
                            ),
                        )
                    )
                    # get right neighbor(s)
                    # Same as above regarding checking for sub_key out of bounds.
                    chunk_keys.add(
                        (
                            tenant_id,
                            ContentKey(
                                blob_name=chunk.blob_name,
                                transformation_key=self._transformation_key,
                                sub_key=get_sub_key(chunk.chunk_index + i),
                            ),
                        )
                    )

            chunk_keys = list(chunk_keys)

            # Make sure we still have time left in the request before fetching from ContentManager.
            request_context.time_remaining_or_raise()

            neighbor_chunks = self._chunk_cache.get(
                chunk_keys, request_context, auth_info
            )
            neighbor_chunks = [chunk for chunk in neighbor_chunks if chunk is not None]

            neighbor_chunks_map = {
                (chunk.blob_name, chunk.chunk_index): chunk for chunk in neighbor_chunks
            }
            left_neighbors: dict[tuple[str, int], RetrievalChunk] = {}
            right_neighbors: dict[tuple[str, int], RetrievalChunk] = {}
            for chunk in retrieved_chunks:
                # We do not support getting neighbors for doccsets
                if chunk.documentation_metadata:
                    continue
                assert chunk.chunk_index is not None
                assert chunk.blob_name is not None

                left_chunk = None
                right_chunk = None
                for i in range(1, self._num_neighbors_to_grab_per_side + 1):
                    next_left_neighbor = neighbor_chunks_map.get(
                        (chunk.blob_name, chunk.chunk_index - i), None
                    )
                    if is_adjacent(left_chunk, next_left_neighbor):
                        left_chunk = join_two_retrieval_chunks(
                            left_chunk, next_left_neighbor
                        )

                    next_right_neighbor = neighbor_chunks_map.get(
                        (chunk.blob_name, chunk.chunk_index + i), None
                    )
                    if is_adjacent(right_chunk, next_right_neighbor):
                        right_chunk = join_two_retrieval_chunks(
                            right_chunk, next_right_neighbor
                        )

                if left_chunk is not None:
                    left_neighbors[(chunk.blob_name, chunk.chunk_index)] = left_chunk
                if right_chunk is not None:
                    right_neighbors[(chunk.blob_name, chunk.chunk_index)] = right_chunk

            return left_neighbors, right_neighbors

    def retrieve(
        self,
        input_: RetrievalInput[ChatRetrieverPromptInput],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor | None = None,
    ) -> RetrievalResult:
        if executor is None:
            executor = dummy_executor.DummyExecutor()

        original_retrieval_result = self._retriever.retrieve(
            input_, request_context, auth_info, executor
        )
        # Eager compute the retrievals
        retrieved_chunks = list(original_retrieval_result.get_retrieved_chunks())
        missing_blob_names = list(original_retrieval_result.get_missing_blob_names())
        checkpoint_not_found = original_retrieval_result.get_checkpoint_not_found()

        # Grab only top N chunks
        retrieved_chunks = retrieved_chunks[: self._num_chunks_to_rerank]

        prompt_chunks = [chunk.to_prompt_chunk() for chunk in retrieved_chunks]
        prompt_input = ChatRerankerPromptInput(
            message=get_request_message_as_text(input_.prompt_input.message),
            # TODO: Need to merge duplicate Exchange definitions!
            dialogue_history=tuple(input_.prompt_input.chat_history),
            candidate_chunks=prompt_chunks,
        )

        # Kick off thread to grab neighbor chunks, so that
        # we can parallelize the reranking with the calls
        # to the content manager.
        neighbor_chunks_fut = executor.submit(
            self._grab_neighbor_chunks,
            retrieved_chunks,
            request_context,
            auth_info,
        )

        # Create batch of prompts to rerank chunks in parallel.
        # Scores prompts in parallel, and then merge the result.
        # Also time this so we have some observability into whether our
        # reranker's prioritization of requests is working efficiently.
        with tracer.start_as_current_span("format_reranker_prompt(s)"):
            reranker_prompt_output = self._reranker_prompt_formatter.format_prompt(
                prompt_input
            )
            batched_prompts_toks = reranker_prompt_output.batched_token_lists
            batched_chunk_indices = reranker_prompt_output.batched_chunk_indices

        # Colin: Assert there are no duplicate chunk indices. We add this check
        # to try to proactively catch any unexpected edge cases. If we don't see
        # any issues after a few weeks (today: Aug 5), we can remove this check.
        flattened_chunk_indices = [itm for ls in batched_chunk_indices for itm in ls]
        assert (
            len(flattened_chunk_indices) == len(set(flattened_chunk_indices))
        ), f"Duplicate chunk indices: {(len(flattened_chunk_indices), len(set(flattened_chunk_indices)))}"

        with tracer.start_as_current_span("compute reranking scores"):
            scores_futures: list[concurrent.futures.Future[RerankerOutput]] = [
                executor.submit(
                    self._reranker_client.calculate_scores, prompt_toks, request_context
                )
                for prompt_toks in batched_prompts_toks
            ]
            batched_scores: list[RerankerOutput] = []
            for single_scores_future in scores_futures:
                batched_scores.append(single_scores_future.result())

        assert len(batched_chunk_indices) == len(batched_scores)
        ls_chunk_and_score = list[tuple[RetrievalChunk, RerankerScores]]()
        for scores, chunk_indices in zip(batched_scores, batched_chunk_indices):
            # grab chunks at chunk indices with numpy indexing
            relevant_chunks = np.array(retrieved_chunks)[
                np.array(chunk_indices).astype(int)
            ]
            ls_chunk_and_score.extend(zip(relevant_chunks, scores.scores_list))

        # grab neighbor chunks
        left_neighbors, right_neighbors = neighbor_chunks_fut.result()

        # tuple contents: chunk, score, description of expansion (if any)
        expanded_chunks_with_scores: list[tuple[RetrievalChunk, float, str]] = []
        for chunk, score_obj in ls_chunk_and_score:
            gain = score_obj.gain
            assert gain is not None
            # normalize by length (the + 1 protects against empty strings)
            gain /= len(chunk.text) + 1
            assert chunk.chunk_index is not None

            # add chunk
            assert score_obj.gain is not None
            expanded_chunks_with_scores.append((chunk, gain, "No expansion"))

            left_chunk, right_chunk = None, None
            # we don't expand documentation chunks
            if not chunk.documentation_metadata:
                assert chunk.blob_name is not None
                # add chunk with left expansion, if it exists
                left_chunk = left_neighbors.get(
                    (chunk.blob_name, chunk.chunk_index), None
                )
                if left_chunk is not None:
                    if is_adjacent(left_chunk, chunk):
                        new_chunk = join_two_retrieval_chunks(left_chunk, chunk)
                    else:
                        continue
                    assert new_chunk is not None

                    gain_left = score_obj.gain_left
                    assert gain_left is not None
                    # expansion score should always be greater than no expansion
                    gain_left += score_obj.gain
                    # normalize by length (the + 1 protects against empty strings)
                    gain_left /= len(new_chunk.text) + 1

                    expanded_chunks_with_scores.append(
                        (new_chunk, gain_left, "Left expansion")
                    )

                # add chunk with right expansion, if it exists
                right_chunk = right_neighbors.get(
                    (chunk.blob_name, chunk.chunk_index), None
                )
                if right_chunk is not None:
                    if is_adjacent(chunk, right_chunk):
                        new_chunk = join_two_retrieval_chunks(chunk, right_chunk)
                    else:
                        continue
                    assert new_chunk is not None

                    gain_right = score_obj.gain_right
                    assert gain_right is not None
                    # expansion score should always be greater than no expansion
                    gain_right += score_obj.gain
                    # normalize by length (the + 1 protects against empty strings)
                    gain_right /= len(new_chunk.text) + 1

                    expanded_chunks_with_scores.append(
                        (new_chunk, gain_right, "Right expansion")
                    )

        # Greater scores at beginning
        expanded_chunks_with_scores.sort(key=lambda x: x[1], reverse=True)
        expanded_chunks = [chunk for chunk, _, _ in expanded_chunks_with_scores]
        chunk_scores = [score for _, score, _ in expanded_chunks_with_scores]
        chunk_expansion_descriptions = [
            description for _, _, description in expanded_chunks_with_scores
        ]

        result = RetrievalResult(
            retrieved_chunks=expanded_chunks,
            missing_blob_names=missing_blob_names,
            checkpoint_not_found=checkpoint_not_found,
        )

        if self._request_insight_builder:
            executor.submit(
                self._request_insight_builder.record_reranker_result,
                reranker_prompts=batched_prompts_toks,
                reranker_tokenizer=self._reranker_prompt_formatter.tokenizer,
                reranked_chunks=expanded_chunks,
                chunk_scores=chunk_scores,
                chunk_expansion_descriptions=chunk_expansion_descriptions,
                request_context=request_context,
                auth_info=auth_info,
            )

        return result

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: concurrent.futures.Executor | None = None,
    ) -> FindMissingResult:
        return self._retriever.find_missing(
            blob_names, request_context=request_context, executor=executor
        )

    def get_name(self) -> str:
        """Returns the class name of the retriever.

        This is used for logging purposes.
        """
        return __class__.__name__

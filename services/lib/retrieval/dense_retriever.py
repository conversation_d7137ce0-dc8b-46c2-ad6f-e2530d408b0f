"""Module for dense retrieval."""

from concurrent.futures import Executor
from typing import Optional, Sequence

import numpy
from services.lib.retrieval import dummy_executor
import structlog
from typing_extensions import override

import base.feature_flags
import base.tokenizers
import services.embeddings_indexer.chunk_pb2 as chunk_pb2
from base.prompt_format_retrieve import RetrieverPromptFormatter
from services.lib.retrieval import (
    retriever_request_insight_builder,
)
from services.lib.retrieval.retriever import (
    Find<PERSON><PERSON>ing<PERSON><PERSON><PERSON>,
    RetrievalChunk,
    RetrievalInput,
    RetrievalPromptInputT,
    RetrievalResult,
    Retriever,
    from_chunk_pb,
)
from services.embedder_host.client.client import (
    EmbedderClientProtocol,
    EmbedderRequestType,
)
from services.embeddings_search_host.client.client import EmbeddingsSearchClientProtocol
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext, clamp_timeout
from services.request_insight import request_insight_pb2

log = structlog.get_logger()

BLOB_INDEX_UNSET = 4294967295
"""A sentinel integer value indicating that the blob index is not set.

The EmbeddingsSearch service returns this index when there are fewer results than
requested in the query.
"""


def get_sub_key(chunk_index: int) -> str:
    """Get the sub key for a retrieval chunk."""
    return f"chunk-{chunk_index}.pb"


class DenseRetriever(Retriever[RetrievalPromptInputT]):
    """Embeddings-based Dense Retrieval."""

    def __init__(
        self,
        embedder_client: EmbedderClientProtocol,
        embeddings_search_client: EmbeddingsSearchClientProtocol,
        query_tokenizer: base.tokenizers.Tokenizer,
        origin: str,
        transformation_key: str,
        query_prompt_formatter: RetrieverPromptFormatter[RetrievalPromptInputT],
        num_results: int,
        search_timeout_ms: int,
        ri_builder: Optional[
            retriever_request_insight_builder.RetrieverRequestInsightBuilder
        ] = None,
    ):
        """Construct a new dense retriever.

        Args:
            embedder_client: An client object for the embedder service
            embeddings_search_client: A client object for the embeddings search service
            content_manager_client: A client object for the content manager
            query_tokenizer: The tokenizer to use
            transformation_key: The transformation key under which the emdeddings and chunks are stored
                                in the content manager
            query_prompt_formatter: prompt formatter to use for the embeddings prompt.
            num_results: The maximal number of chunks to return
            ri_builder: A client object for the request insight service
        """
        self._query_tokenizer = query_tokenizer
        self._embedder_client = embedder_client
        self._embeddings_search_client = embeddings_search_client
        self._num_results = num_results
        self._transformation_key = transformation_key
        self._sub_key = "embeddings.npy"
        self._query_prompt_formatter = query_prompt_formatter
        self._request_insight_builder = ri_builder
        self._search_timeout_ms = search_timeout_ms
        self._origin = origin

    def parse_retrieval_chunk_from_bytes(
        self,
        blob_name: str | None,
        chunk_index: int,
        chunk_content: bytes,
        score: float,
    ) -> RetrievalChunk:
        """Parse a retrieval chunk from the bytes returned by the content manager."""
        chunk_pb = chunk_pb2.Chunk()
        chunk_pb.ParseFromString(chunk_content)
        return from_chunk_pb(
            chunk_pb,
            blob_name=blob_name,
            chunk_index=chunk_index,
            origin=self._origin,
            score=score,
        )

    def _get_prompt_tokens(self, input_: RetrievalInput) -> list[int]:
        """Calculates the tokens send to the embedder service.

        Some models do not include the file path into the embeddings prompt.

        The last token is always the end of query token.
        """
        return self._query_prompt_formatter.format_prompt(input_.prompt_input).tokens()

    def _embeddings_search_chunks(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        query_embedding: numpy.ndarray,
    ) -> RetrievalResult:
        """Helper function to call the SearchChunks() API."""

        search_result = self._embeddings_search_client.search_chunks(
            query=query_embedding,
            blobs=input_.blobs,
            num_results=self._num_results,
            transformation_key=self._transformation_key,
            request_context=request_context,
            search_timeout_ms=self._search_timeout_ms,
            timeout=clamp_timeout(request_context.time_remaining_or_raise(), 30),
            sequence_id=input_.sequence_id,
        )

        missing_blob_names = list(search_result.missing_blobs.missing_blob_names)

        assert (
            len(search_result.chunks) <= self._num_results
        ), f"Found more results {len(search_result.chunks)=} than requested {self._num_results=}"

        chunks = [
            self.parse_retrieval_chunk_from_bytes(
                blob_name=chunk.blob_name,
                chunk_index=chunk.chunk_index,
                chunk_content=chunk.content,
                score=chunk.score,
            )
            for chunk in search_result.chunks
        ]

        return RetrievalResult(
            retrieved_chunks=chunks,
            missing_blob_names=missing_blob_names,
            checkpoint_not_found=search_result.missing_blobs.checkpoint_not_found,
        )

    @override
    def retrieve(
        self,
        input_: RetrievalInput,
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: Optional[Executor] = None,
    ) -> RetrievalResult:
        """Returns a list of retrieved chunks.

        It will calculate an embedding based on the path and input tokens and then will
        search embeddings that are "similar". Based on this information, it will load
        chunks from the content manager.

        Args:
            input_: The retrieval input from the user request.
            request_context: The request context used for tracking, metrics and authentication.
            executor: If present, an concurrent executor that can be used to parallelize
              requests.

        Returns:
            it returns a RetrievalResult object. The object contains the list of retrieved chunks.
            The size of all retrieved chunks does not exceed retrieval_len
            The returned object also contains all the blob names that were not received.
        """
        if input_.workspace_empty():
            return RetrievalResult(
                retrieved_chunks=(), missing_blob_names=[], checkpoint_not_found=False
            )

        if executor is None:
            executor = dummy_executor.DummyExecutor()

        query_tokens = self._get_prompt_tokens(input_)
        query_embedding = self._embedder_client.calculate_embedding(
            tokens=[query_tokens],
            request_type=EmbedderRequestType.QUERY,
            source_namespace=input_.namespace,
            request_context=request_context,
            timeout=clamp_timeout(request_context.time_remaining_or_raise(), 30),
        )

        result = self._embeddings_search_chunks(
            input_, request_context, query_embedding
        )

        # We rely on the caller to wait for the executor to terminate all calls.
        if self._request_insight_builder:
            executor.submit(
                self._request_insight_builder.record_retrieval_result,
                request_insight_pb2.RetrievalType.DENSE,
                query_tokens,
                self._query_tokenizer,
                result.get_retrieved_chunks(),  # creates a fresh iterator; does not wait for retrieval to complete
                request_context,
                auth_info=auth_info,
            )

        # Check the deadline one more time, in case we timed out during a request to ContentManager
        # above.
        # TODO(jacqueline): Remove this once the deadline is propagated to ContentManager.
        request_context.time_remaining_or_raise()

        return result

    @override
    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: Optional[Executor] = None,
    ) -> FindMissingResult:
        del executor
        if not blob_names:
            return FindMissingResult(missing_blob_names=[])
        result = self._embeddings_search_client.find_missing(
            blob_names=blob_names,
            transformation_key=self._transformation_key,
            sub_key=self._sub_key,
            request_context=request_context,
        )
        return FindMissingResult(missing_blob_names=result.missing_blob_names)

    @override
    def get_name(self) -> str:
        return f"DenseRetriever({self._transformation_key})"

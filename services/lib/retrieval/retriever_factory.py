"""Configuration and factory methods to create retrievers."""

import logging
import pathlib
from dataclasses import dataclass, field
from typing import Optional, Union, cast

import grpc
from dataclasses_json import dataclass_json

import base.feature_flags
from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment
from base.prompt_format_rerank import get_reranker_prompt_formatter_by_name
from base.prompt_format_rerank.token_apportionment import (
    ChatRerankerTokenApportionmentConfig,
)
from base.prompt_format_retrieve import get_retrieval_prompt_formatter_by_name
from base.prompt_format_router import (
    get_router_prompt_formatter_by_name,
)
from base.python.grpc import client_options
from base.tokenizers import create_tokenizer_by_name
from services.lib.retrieval.retriever_request_insight_builder import (
    RetrieverRequestInsightBuilder,
)
from services.content_manager.client import content_cache
from services.content_manager.client.content_manager_client import (
    ContentManagerClient,
)
from services.embedder_host.client.client import EmbedderClient, EmbedderClientProtocol
from services.embedder_host.client.multiplex import MultiplexEmbeddingClient
from services.embeddings_search_host.client.client import (
    EmbeddingsSearchClient,
    PartitionedEmbeddingsSearchClient,
)
from services.inference_host import infer_pb2_grpc
from services.inference_host.client import inference_host_client
from services.inference_host.client.inference_host_client import InfererClient
from services.integrations.docset.client.client import DocSetClient
from services.lib.file_retriever.file_retriever import FileRetriever
from services.lib.grpc.tls_config import tls_config
from services.lib.grpc.tls_config.tls_config import ClientConfig
from services.lib.retrieval.dense_retriever import DenseRetriever
from services.lib.retrieval.diff_retriever import DiffRetriever
from services.lib.retrieval.router import Router
from services.lib.retrieval.docset_retriever import DocsetRetriever
from services.lib.retrieval.multi_retriever import MultiRetriever
from services.lib.retrieval.null_retriever import NullRetriever
from services.lib.retrieval.recency_retriever import RecencyRetriever
from services.lib.retrieval.reranker import RetrieveThenChunkExpansionReranker
from services.lib.retrieval.retriever import (
    Retriever,
    parse_retrieval_chunk_from_bytes_builder,
)
from services.lib.retrieval.user_guided_retriever import (
    UserGuidedRetriever,
)
from services.lib.request_context.request_context import RequestContext
from services.reranker.client.client import RerankerClient
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import GrpcTokenExchangeClient

# feature flag to enable multiplexing over multiple embedder clients
#
# Example
# {"default": 0.5, "gsc": 0.5}
#
# This assumes that the embedder clients are named "default" and "gsc"
_EMBEDDER_MULTIPLEX = base.feature_flags.StringFlag("retrieval_embedder_multiplex", "")


def _gbytes_to_bytes(gbytes: float) -> int:
    return int(gbytes * 1024 * 1024 * 1024)


def get_embeddings_search_client(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
) -> EmbeddingsSearchClient:
    """Returns an embeddings search client."""
    logging.info(
        "Creating embeddings search client for %s",
        endpoint,
    )
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
    )
    return EmbeddingsSearchClient.create_for_endpoint(
        endpoint, credentials=credentials, options=options
    )


def get_docset_client(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
) -> DocSetClient:
    """Returns a documentation set client."""
    logging.info("Creating docset client for %s", endpoint)
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
    )
    return DocSetClient.create_for_endpoint(
        endpoint, credentials=credentials, options=options
    )


def _get_embedder_clients(
    embedder_endpoints: dict[str, str],
    credentials: Optional[grpc.ChannelCredentials],
    model_name: str,
) -> EmbedderClientProtocol:
    """Returns a client to the embedder."""
    embedder_rpc_clients: dict[str, EmbedderClientProtocol] = {}
    for name, endpoint in embedder_endpoints.items():
        options = client_options.get_grpc_client_options(
            client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
        )

        rpc_client = EmbedderClient.create_for_endpoint(
            endpoint, credentials=credentials, options=options
        )
        embedder_rpc_clients[name] = rpc_client
    if "default" not in embedder_rpc_clients:
        raise ValueError("No default client")

    if len(embedder_rpc_clients) > 1:
        embedder_client = MultiplexEmbeddingClient(
            embedder_rpc_clients=embedder_rpc_clients,
            model_name=model_name,
            feature_flag=_EMBEDDER_MULTIPLEX,
        )
    else:
        embedder_client = list(embedder_rpc_clients.values())[0]
    return embedder_client


def get_reranker_client(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
) -> RerankerClient:
    """Returns a reranker client."""
    logging.info("Creating reranker client for %s", endpoint)
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
    )
    return RerankerClient.create_for_endpoint(
        endpoint, credentials=credentials, options=options
    )


@dataclass_json
@dataclass
class DenseRetrievalConfig:
    """Dense retrieval configuration."""

    max_retrieval_results: int
    """The maximal number of chunks the retriever should retrieve."""

    embedder_model_name: str
    """The name of the model to use for embedding."""

    embedder_endpoints: dict[str, str]
    """Endpoints (host:port) of the embedder to use for Dense Retrieval."""

    embedder_mtls: bool
    """Whether to use mutual TLS for the embedder client."""

    embedder_client_ca_path: str
    """Path to the CA certificate for the embedder client."""

    embedder_client_cert_path: str
    """Path to the client certificate for the embedder client."""

    embedder_client_key_path: str
    """Path to the client key for the embedder client."""

    embeddings_search_endpoint: str
    """Endpoint (host:port) of the embedding searcher to use for Dense Retrieval."""
    partitioned_embeddings_search_endpoints: list[str]
    """Endpoints (host:port) of the embedding searcher to use for partitioned dense retrieval.
        If this is set, embeddings_search_endpoint is ignored."""

    embeddings_transformation_key: str
    """The transformation key under which the embeddings for the blobs are stored
       in the content manager"""
    embeddings_query_prompt_formatter_name: str
    embeddings_query_tokenizer_name: str

    origin: str
    """The identifier of the retriever."""


@dataclass_json
@dataclass
class DocsetRetrievalConfig:
    """Docset retrieval configuration."""

    max_retrieval_results: int
    """The maximal number of chunks the retriever should retrieve."""

    embedder_model_name: str
    """The name of the model to use for embedding."""

    embedder_endpoints: dict[str, str]
    """Endpoint (host:port) of the embedder to use for Dense Retrieval."""

    embedder_mtls: bool
    """Whether to use mutual TLS for the embedder client."""

    embedder_client_ca_path: str
    """Path to the CA certificate for the embedder client."""

    embedder_client_cert_path: str
    """Path to the client certificate for the embedder client."""

    embedder_client_key_path: str
    """Path to the client key for the embedder client."""

    docset_endpoint: str
    """Endpoint (host:port) of the docset service to use for Dense Retrieval."""

    embeddings_transformation_key: str
    """The transformation key under which the embeddings for the blobs are stored
       in the content manager"""
    embeddings_query_prompt_formatter_name: str
    embeddings_query_tokenizer_name: str

    origin: str
    """The identifier of the retriever."""

    auto_external_source_ids: list[str] = field(default_factory=list)
    """List of external source IDs to automatically add to the request."""


def get_embedder_client_creds(
    config: DenseRetrievalConfig | DocsetRetrievalConfig,
) -> Optional[grpc.ChannelCredentials]:
    """Returns RPC client credentials."""
    if config.embedder_mtls:
        return grpc.ssl_channel_credentials(
            root_certificates=pathlib.Path(config.embedder_client_ca_path).read_bytes(),
            private_key=pathlib.Path(config.embedder_client_key_path).read_bytes(),
            certificate_chain=pathlib.Path(
                config.embedder_client_cert_path
            ).read_bytes(),
        )
    else:
        return None


@dataclass_json
@dataclass
class RerankerConfig:
    """Retrieval configuration."""

    max_retrieval_results: int
    """The maximal number of chunks the retriever should retrieve."""

    ### The purpose of the following two flags
    # is to fix latency for a model. In order to accomlish
    # this, the number of chunks reranked on each request may
    # slightly vary. The optimal tradeoff between the two args will vary
    # depending on the inference system and model. For example, smaller context
    # but larger batch size will make the reranker less attention-dominated, which
    # will usually be beneficial for small models.
    ###

    seq_len_per_batch_elem: int
    """The sequence length per batch element."""

    batch_size: int
    """The max batch size to use for batched reranking. Set to 1 if you don't want batching."""

    max_dialogue_tokens: int
    """The maximum number of tokens to include from the dialogue."""

    reranker_endpoint: str
    """Endpoint (host:port) of the reranker to use."""

    reranker_mtls: bool
    """Whether to use mutual TLS for the reranker client."""

    reranker_client_ca_path: str
    """Path to the CA certificate for the reranker client."""

    reranker_client_cert_path: str
    """Path to the client certificate for the reranker client."""

    reranker_client_key_path: str
    """Path to the client key for the reranker client."""

    ### ACCEPTED FROM INNER RETRIEVER

    reranker_transformation_key: str
    """The transformation key under which the embeddings for the blobs are stored
       in the content manager"""

    origin: str
    """The identifier of the retriever."""

    cache_memory_limit_gb: int
    """Size of the retrieval chunk cache."""

    ### END ACCEPTED FROM INNER RETRIEVER

    reranker_prompt_formatter_name: str
    """Name of the reranker prompt formatter."""

    reranker_tokenizer_name: str
    """Name of the reranker tokenizer."""

    inner_retriever_name: str
    """Name of the inner retriever."""

    inner_retriever_config: dict
    """Configuration of reranker."""


def get_reranker_client_creds(
    config: RerankerConfig,
) -> Optional[grpc.ChannelCredentials]:
    """Returns RPC client credentials."""
    if config.reranker_mtls:
        return grpc.ssl_channel_credentials(
            root_certificates=pathlib.Path(config.reranker_client_ca_path).read_bytes(),
            private_key=pathlib.Path(config.reranker_client_key_path).read_bytes(),
            certificate_chain=pathlib.Path(
                config.reranker_client_cert_path
            ).read_bytes(),
        )
    else:
        return None


def create_dense_retriever(
    config: Union[DenseRetrievalConfig, dict],
    credentials: Optional[grpc.ChannelCredentials],
    ri_builder: RetrieverRequestInsightBuilder | None = None,
    search_timeout_ms: int = 1000,
) -> Retriever:
    """Create an instance of the dense retriever."""
    if isinstance(config, dict):
        config = cast(
            DenseRetrievalConfig,
            # pylint:disable-next=no-member
            DenseRetrievalConfig.schema().load(config),  # type: ignore
        )

    if config.partitioned_embeddings_search_endpoints:
        embeddings_search_client = PartitionedEmbeddingsSearchClient(
            [
                get_embeddings_search_client(endpoint, credentials)
                for endpoint in config.partitioned_embeddings_search_endpoints
            ]
        )
    else:
        embeddings_search_client = get_embeddings_search_client(
            config.embeddings_search_endpoint, credentials
        )
    embedder_client = _get_embedder_clients(
        config.embedder_endpoints,
        get_embedder_client_creds(config),
        config.embedder_model_name,
    )

    query_tokenizer = create_tokenizer_by_name(config.embeddings_query_tokenizer_name)
    query_prompt_formatter = get_retrieval_prompt_formatter_by_name(
        config.embeddings_query_prompt_formatter_name,
        tokenizer=query_tokenizer,
    )

    retriever = DenseRetriever(
        embeddings_search_client=embeddings_search_client,
        embedder_client=embedder_client,
        query_tokenizer=query_tokenizer,
        origin=config.origin,
        transformation_key=config.embeddings_transformation_key,
        query_prompt_formatter=query_prompt_formatter,
        num_results=config.max_retrieval_results,
        search_timeout_ms=search_timeout_ms,
        ri_builder=ri_builder,
    )

    return retriever


def _get_docset_client(
    config: DocsetRetrievalConfig | dict,
    credentials: Optional[grpc.ChannelCredentials],
) -> DocSetClient:
    """Returns a docset client."""
    if isinstance(config, dict):
        config = cast(
            DocsetRetrievalConfig,
            # pylint:disable-next=no-member
            DocsetRetrievalConfig.schema().load(config),  # type: ignore
        )
    return get_docset_client(config.docset_endpoint, credentials)


def create_docset_retriever(
    config: Union[DocsetRetrievalConfig, dict],
    credentials: Optional[grpc.ChannelCredentials],
    ri_builder: RetrieverRequestInsightBuilder | None = None,
    search_timeout_ms: int = 1000,
) -> Retriever:
    """Create an instance of the docset retriever."""
    if isinstance(config, dict):
        config = cast(
            DocsetRetrievalConfig,
            # pylint:disable-next=no-member
            DocsetRetrievalConfig.schema().load(config),  # type: ignore
        )

    embedder_client = _get_embedder_clients(
        config.embedder_endpoints,
        get_embedder_client_creds(config),
        config.embedder_model_name,
    )
    docset_client = get_docset_client(config.docset_endpoint, credentials)

    query_tokenizer = create_tokenizer_by_name(config.embeddings_query_tokenizer_name)
    query_prompt_formatter = get_retrieval_prompt_formatter_by_name(
        config.embeddings_query_prompt_formatter_name,
        tokenizer=query_tokenizer,
    )

    retriever = DocsetRetriever(
        docset_client=docset_client,
        embedder_client=embedder_client,
        query_tokenizer=query_tokenizer,
        origin=config.origin,
        transformation_key=config.embeddings_transformation_key,
        query_prompt_formatter=query_prompt_formatter,
        num_results=config.max_retrieval_results,
        search_timeout_ms=search_timeout_ms,
        ri_builder=ri_builder,
        auto_external_source_ids=config.auto_external_source_ids,
    )

    return retriever


def create_reranker(
    inner_retriever: Retriever,
    chunk_cache: content_cache.LRUContentCache,
    config: RerankerConfig,
    ri_builder: RetrieverRequestInsightBuilder | None = None,
) -> Retriever:
    reranker_client = get_reranker_client(
        config.reranker_endpoint,
        get_reranker_client_creds(config),
    )

    apportionment_config = ChatRerankerTokenApportionmentConfig(
        seq_len_per_batch_elem=config.seq_len_per_batch_elem,
        max_dialogue_tokens=config.max_dialogue_tokens,
        batch_size=config.batch_size,
    )

    reranker_tokenizer = create_tokenizer_by_name(config.reranker_tokenizer_name)
    reranker_prompt_formatter = get_reranker_prompt_formatter_by_name(
        config.reranker_prompt_formatter_name,
        tokenizer=reranker_tokenizer,
        apportionment_config=apportionment_config,
    )

    return RetrieveThenChunkExpansionReranker(
        retriever=inner_retriever,
        reranker_client=reranker_client,
        reranker_prompt_formatter=reranker_prompt_formatter,
        transformation_key=config.reranker_transformation_key,
        chunk_cache=chunk_cache,
        ri_builder=ri_builder,
        num_chunks_to_rerank=config.max_retrieval_results,
        seq_len_per_batch_elem=config.seq_len_per_batch_elem,
    )


@dataclass_json
@dataclass
class RecencyRetrieverConfig:
    """Recency retrieval configuration."""

    max_retrieval_results: int
    """The maximal number of chunks the retriever should retrieve."""

    cache_memory_limit_gb: int
    """Size of the retrieval chunk cache."""

    internal_retriever_name: Optional[str] = None
    """Name of internal retriever."""

    tab_switch_and_git_diff_retrieval_enabled: Optional[bool] = False
    """Whether to use tab switch and git diff signals."""


def create_recency_retriever(
    config: Union[RecencyRetrieverConfig, dict],
    content_manager_client: ContentManagerClient,
    ri_builder: RetrieverRequestInsightBuilder | None = None,
    retrievers_instances: dict[str, Retriever] = field(default_factory=dict),
) -> RecencyRetriever:
    if isinstance(config, dict):
        config = cast(
            RecencyRetrieverConfig,
            # pylint:disable-next=no-member
            RecencyRetrieverConfig.schema().load(config),  # type: ignore
        )

    internal_retriever = NullRetriever()
    if config.internal_retriever_name is not None:
        internal_retriever = retrievers_instances[config.internal_retriever_name]

    chunk_cache = content_cache.LRUContentCache(
        content_manager=content_manager_client,
        transform_from_bytes=lambda _, content: content.decode("utf-8"),
        max_size=_gbytes_to_bytes(config.cache_memory_limit_gb),
    )

    return RecencyRetriever(
        internal_retriever=internal_retriever,
        num_results=config.max_retrieval_results,
        ri_builder=ri_builder,
        blob_cache=chunk_cache,
        tab_switch_and_git_diff_retrieval_enabled=(
            config.tab_switch_and_git_diff_retrieval_enabled is True
        ),
    )


@dataclass_json
@dataclass
class UserGuidedRetrieverConfig:
    """User Guided retrieval configuration."""

    max_retrieval_results: int
    """The maximal number of chunks the retriever should retrieve."""

    internal_retriever_name: Optional[str] = None
    """Name of internal retriever."""


def create_user_guided_retriever(
    config: Union[UserGuidedRetrieverConfig, dict],
    ri_builder: RetrieverRequestInsightBuilder | None = None,
    retrievers_instances: dict[str, Retriever] = field(default_factory=dict),
) -> UserGuidedRetriever:
    if isinstance(config, dict):
        config = cast(
            UserGuidedRetrieverConfig,
            # pylint:disable-next=no-member
            UserGuidedRetrieverConfig.schema().load(config),  # type: ignore
        )

    internal_retriever = NullRetriever()
    if config.internal_retriever_name is not None:
        internal_retriever = retrievers_instances[config.internal_retriever_name]

    return UserGuidedRetriever(
        internal_retriever=internal_retriever,
        num_results=config.max_retrieval_results,
        ri_builder=ri_builder,
    )


@dataclass_json
@dataclass
class DiffRetrieverConfig:
    """Diff retrieval configuration."""

    max_retrieval_results: int
    """The maximal number of chunks the retriever should retrieve."""

    max_total_changed_chars: int
    """The maximum number of characters that can be changed in a diff."""

    content_cache_size_mb: int
    """Size of the retrieval chunk cache."""

    big_event_lines: int
    """Events that add more than this number of lines will be considered as the big event and grouped differently."""

    diff_context_lines: int
    """The number of lines of context to include in the diff."""

    filter_duplicated_file_paths: bool
    """Whether to filter out duplicated file paths in the diff."""

    use_smart_header: bool
    """Whether to use a smart header in the diff."""

    internal_retriever_name: Optional[str] = None
    """Name of internal retriever."""


def create_diff_retriever(
    config: Union[DiffRetrieverConfig, dict],
    content_manager_client: ContentManagerClient,
    ri_builder: RetrieverRequestInsightBuilder | None = None,
    retrievers_instances: dict[str, Retriever] = field(default_factory=dict),
) -> DiffRetriever:
    if isinstance(config, dict):
        config = cast(
            DiffRetrieverConfig,
            # pylint:disable-next=no-member
            DiffRetrieverConfig.schema().load(config),  # type: ignore
        )

    internal_retriever = NullRetriever()
    if config.internal_retriever_name is not None:
        internal_retriever = retrievers_instances[config.internal_retriever_name]

    # Create a File Retriever (which internally creates a content cache) and pass that to DiffRetriever
    file_retriever = FileRetriever(
        content_manager_client=content_manager_client,
        content_cache_size_mb=config.content_cache_size_mb,
    )

    return DiffRetriever(
        num_results=config.max_retrieval_results,
        max_total_changed_chars=config.max_total_changed_chars,
        big_event_lines=config.big_event_lines,
        diff_context_lines=config.diff_context_lines,
        use_smart_header=config.use_smart_header,
        filter_duplicated_file_paths=config.filter_duplicated_file_paths,
        internal_retriever=internal_retriever,
        file_retriever=file_retriever,
        ri_builder=ri_builder,
    )


@dataclass_json
@dataclass
class RouterConfig:
    """Router configuration."""

    agent_endpoint: str
    """The endpoint of the agent service."""

    agent_mtls: bool
    """Whether to use mtls for the agent service."""

    agent_client_ca_path: str
    """The path to the agent service's CA cert."""

    agent_client_cert_path: str
    """The path to the agent service's client cert."""

    agent_client_key_path: str
    """The path to the agent service's client key."""

    router_name: str
    """Name of the router."""

    router_timeout_s: int
    """The timeout for the agent service in milliseconds."""

    router_prompt_formatter_name: str
    """Name of the router prompt formatter."""

    router_tokenizer_name: str
    """Name of the router tokenizer."""

    router_max_output_length: int
    """The maximum length of the output from the router."""

    router_token_apportionment: dict
    """The token apportionment for the router."""

    code_retriever_name: str
    """Name of the code retriever."""

    code_retriever_config: dict
    """Configuration of code retriever."""

    docset_retriever_name: str
    """Name of the docset retriever."""

    docset_retriever_config: dict
    """Configuration of docset retriever."""

    user_guided_retriever_name: str
    """Name of the user guided retriever."""

    user_guided_retriever_config: dict
    """Configuration of user guided retriever."""


def get_router_client_creds(
    config: RouterConfig,
) -> Optional[ClientConfig]:
    """Returns RPC client credentials."""
    if config.agent_mtls:
        return ClientConfig(
            key_path=config.agent_client_key_path,
            cert_path=config.agent_client_cert_path,
            ca_path=config.agent_client_ca_path,
        )
    else:
        return None


def _get_inference_stub(
    endpoint: str, client_mtls: Optional[ClientConfig]
) -> infer_pb2_grpc.InfererStub:
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in endpoint,
        )
    )

    return inference_host_client.create_inference_stub(
        endpoint,
        credentials=tls_config.get_client_tls_creds(client_mtls),
        options=options,
    )


def create_router(
    config: Union[RouterConfig, dict],
    credentials: Optional[grpc.ChannelCredentials],
    ri_builder: RetrieverRequestInsightBuilder | None = None,
    search_timeout_ms: int = 1000,  # Timeout for inner retrievers
    retrievers_instances: dict[str, Retriever] = field(default_factory=dict),
    token_exchange_client: GrpcTokenExchangeClient | None = None,
) -> Retriever:
    if isinstance(config, dict):
        config = cast(
            RouterConfig,
            # pylint:disable-next=no-member
            RouterConfig.schema().load(config),  # type: ignore
        )
    router_stub = _get_inference_stub(
        config.agent_endpoint, get_router_client_creds(config)
    )
    router_client = InfererClient(lambda: router_stub)

    code_retriever = _create_single_retriever(
        config=SingleRetrievalConfig(
            retriever_name=config.code_retriever_name,
            config=config.code_retriever_config,
        ),
        content_manager_client=None,  # type: ignore
        credentials=credentials,
        ri_builder=ri_builder,
        search_timeout_ms=search_timeout_ms,
        retrievers_instances=retrievers_instances,
    )
    retrievers_instances[config.code_retriever_name] = code_retriever

    docset_retriever = _create_single_retriever(
        config=SingleRetrievalConfig(
            retriever_name=config.docset_retriever_name,
            config=config.docset_retriever_config,
        ),
        content_manager_client=None,  # type: ignore
        credentials=credentials,
        ri_builder=ri_builder,
        search_timeout_ms=search_timeout_ms,
        retrievers_instances=retrievers_instances,
    )
    retrievers_instances[config.docset_retriever_name] = docset_retriever

    user_guided_retriever = _create_single_retriever(
        config=SingleRetrievalConfig(
            retriever_name=config.user_guided_retriever_name,
            config=config.user_guided_retriever_config,
        ),
        content_manager_client=None,  # type: ignore
        credentials=credentials,
        ri_builder=ri_builder,
        search_timeout_ms=search_timeout_ms,
        retrievers_instances=retrievers_instances,
    )
    retrievers_instances[config.user_guided_retriever_name] = user_guided_retriever

    router_tokenizer = create_tokenizer_by_name(config.router_tokenizer_name)

    if token_exchange_client is None:
        raise ValueError("Token exchange client is missing")

    # Loading docsets - note that this is never refreshed during runtime, we're relying
    # on the rate of docset updates to be much lower than the rate of deployments
    docset_client = _get_docset_client(config.docset_retriever_config, credentials)
    request_context = RequestContext.create(
        request_source="background",
        auth_token=token_exchange_client.get_signed_token_for_service(
            tenant_id="", scopes=[token_exchange_pb2.CONTENT_R]
        ),
    )
    docsets = docset_client.get_doc_sets("", request_context)
    logging.info("Creating router with %s docsets", len(docsets))
    docset_ids = [docset.doc_set_id for docset in docsets]

    router_prompt_formatter = get_router_prompt_formatter_by_name(
        config.router_prompt_formatter_name,
        tokenizer=router_tokenizer,
        apportionment_config=ChatTokenApportionment(
            **config.router_token_apportionment
        ),
        docsets=docset_ids,
    )

    return Router(
        name=config.router_name,
        code_retriever=code_retriever,
        docset_retriever=docset_retriever,
        user_guided_retriever=user_guided_retriever,
        router_client=router_client,
        router_prompt_formatter=router_prompt_formatter,
        router_max_output_length=config.router_max_output_length,
        router_timeout_s=config.router_timeout_s,
        ri_builder=ri_builder,
    )


@dataclass_json
@dataclass
class SingleRetrievalConfig:
    """Configuration for a single retriever."""

    retriever_name: str
    """The name of the retriever to create."""
    config: dict
    """Configuration for the retriever specified by `retriever_name`."""


def _create_single_retriever(
    config: SingleRetrievalConfig,
    content_manager_client: ContentManagerClient,
    credentials: Optional[grpc.ChannelCredentials],
    ri_builder: RetrieverRequestInsightBuilder | None,
    search_timeout_ms: int,
    retrievers_instances: dict[str, Retriever] = field(default_factory=dict),
    token_exchange_client: GrpcTokenExchangeClient | None = None,
) -> Retriever:
    """Create an instance of the retriever."""
    retriever = None
    if config.retriever_name == "DenseRetriever":
        retriever = create_dense_retriever(
            config=config.config,
            credentials=credentials,
            ri_builder=ri_builder,
            search_timeout_ms=search_timeout_ms,
        )
    elif config.retriever_name == "DocsetRetriever":
        # TODO: make docset_client required?
        retriever = create_docset_retriever(
            config=config.config,
            credentials=credentials,
            ri_builder=ri_builder,
            search_timeout_ms=search_timeout_ms,
        )
    elif config.retriever_name == "Reranker":
        reranker_config = cast(
            RerankerConfig,
            # pylint:disable-next=no-member
            RerankerConfig.schema().load(config.config),  # type: ignore
        )

        inner_config = SingleRetrievalConfig(
            retriever_name=reranker_config.inner_retriever_name,
            config=reranker_config.inner_retriever_config,
        )

        inner_retriever = _create_single_retriever(
            config=inner_config,
            content_manager_client=content_manager_client,
            credentials=credentials,
            ri_builder=ri_builder,
            retrievers_instances=retrievers_instances,
            search_timeout_ms=search_timeout_ms,
        )
        retrievers_instances[reranker_config.inner_retriever_name] = inner_retriever

        # TODO: Modify SearchChunks() to also return neighboring chunks, so that
        # we can drop the chunk_cache here
        chunk_cache = content_cache.LRUContentCache(
            content_manager=content_manager_client,
            transform_from_bytes=parse_retrieval_chunk_from_bytes_builder(
                reranker_config.origin
            ),
            max_size=_gbytes_to_bytes(reranker_config.cache_memory_limit_gb),
        )
        retriever = create_reranker(
            inner_retriever=inner_retriever,
            chunk_cache=chunk_cache,
            config=reranker_config,
            ri_builder=ri_builder,
        )
    elif config.retriever_name == "RecencyRetriever":
        retriever = create_recency_retriever(
            config=config.config,
            content_manager_client=content_manager_client,
            ri_builder=ri_builder,
            retrievers_instances=retrievers_instances,
        )
    elif config.retriever_name == "UserGuidedRetriever":
        retriever = create_user_guided_retriever(
            config=config.config,
            ri_builder=ri_builder,
            retrievers_instances=retrievers_instances,
        )
    elif config.retriever_name == "DiffRetriever":
        retriever = create_diff_retriever(
            config=config.config,
            content_manager_client=content_manager_client,
            ri_builder=ri_builder,
            retrievers_instances=retrievers_instances,
        )
    elif config.retriever_name == "Router":
        retriever = create_router(
            config=config.config,
            credentials=credentials,
            ri_builder=ri_builder,
            search_timeout_ms=search_timeout_ms,
            retrievers_instances=retrievers_instances,
            token_exchange_client=token_exchange_client,
        )
    else:
        raise ValueError(f"Unknown retriever name: {config.retriever_name}")

    return retriever


@dataclass_json
@dataclass
class RetrievalConfig:
    """Retrieval configuration."""

    retrieval_configs: list[SingleRetrievalConfig] = field(default_factory=list)
    """A list of retrievers to build."""


def create_retriever(
    config: RetrievalConfig,
    content_manager_client: ContentManagerClient,
    credentials: Optional[grpc.ChannelCredentials],
    ri_builder: RetrieverRequestInsightBuilder | None,
    search_timeout_ms: int,
    token_exchange_client: GrpcTokenExchangeClient | None = None,
) -> Retriever:
    retrievers_instances = {}
    retrievers = []

    # Always create the dense retriever first, if it exists, because other
    # retrievers may depend on it
    configs = sorted(
        config.retrieval_configs,
        key=lambda x: 0 if x.retriever_name == "DenseRetriever" else 1,
    )

    for config_ in configs:
        retriever = _create_single_retriever(
            config=config_,
            content_manager_client=content_manager_client,
            credentials=credentials,
            ri_builder=ri_builder,
            search_timeout_ms=search_timeout_ms,
            retrievers_instances=retrievers_instances,
            token_exchange_client=token_exchange_client,
        )
        retrievers.append(retriever)
        retrievers_instances[config_.retriever_name] = retriever

    if len(retrievers) == 0:
        return NullRetriever()
    elif len(retrievers) == 1:
        return retrievers[0]
    else:
        return MultiRetriever(retrievers)

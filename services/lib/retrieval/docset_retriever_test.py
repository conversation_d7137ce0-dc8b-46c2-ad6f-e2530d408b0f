"""Test for the docset retriever module."""

from unittest import mock

import base.tokenizers
from base import prompt_format_retrieve
from base.blob_names.python.blob_names import Blobs
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.prompt_format.chunk_origin import ChunkOrigin
from services.lib.retrieval.docset_retriever import (
    DocsetRetriever,
)
from services.lib.retrieval.retriever import RetrievalInput
from services.embedder_host.client.client import (
    EmbedderClientProtocol,
)
from services.integrations.docset.client.client import (
    DocSetClient,
    DocsetSearchChunksResult,
)
from services.integrations.docset import docset_pb2
from services.embeddings_indexer import chunk_pb2
from services.lib.request_context.request_context import RequestContext


def gen_blob_name(s: str) -> str:
    """Convert a string to a hex-string blob name."""
    return s.encode("utf-8").hex()


def test_docset_server_not_enabled():
    """
    Test that docset-srv is not called by default, when external_source_ids is
    empty and disable_auto_external_sources is True.
    """
    tokenizer = base.tokenizers.create_tokenizer_by_name("fim")
    embedder_rpc_client = mock.MagicMock(spec=EmbedderClientProtocol)
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    docset_client = mock.MagicMock(spec=DocSetClient)
    dr = DocsetRetriever(
        embedder_client=embedder_rpc_client,
        docset_client=docset_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key="dr-1",
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )

    blob_name = gen_blob_name("123")
    blobs = Blobs.from_blob_names([blob_name])
    request_context = RequestContext.create()

    result = dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                prefix="Hi AI!",
                suffix="",
                path="foo/foo.py",
                message="",
                selected_code="",
                chat_history=[],
            ),
            blobs=[blobs],
            external_source_ids=[],
            # External sources are disabled
            disable_auto_external_sources=True,
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )

    docset_client.search.assert_not_called()
    assert len(list(result.get_retrieved_chunks())) == 0
    assert len(list(result.get_missing_blob_names())) == 0
    assert not result.get_checkpoint_not_found()


def test_docset_server_enabled():
    """Test that docset-srv is called when we have external_source_ids"""
    tokenizer = base.tokenizers.create_tokenizer_by_name("fim")
    embedder_rpc_client = mock.MagicMock(spec=EmbedderClientProtocol)
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    docset_client = mock.MagicMock(spec=DocSetClient)
    dr = DocsetRetriever(
        embedder_client=embedder_rpc_client,
        docset_client=docset_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key="dr-1",
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )

    blob_name = gen_blob_name("123")
    blobs = Blobs.from_blob_names([blob_name])
    request_context = RequestContext.create()

    docset_client.get_implicit_docsets.return_value = []
    docset_client.search.return_value = DocsetSearchChunksResult(
        chunks=[
            DocsetSearchChunksResult.RetrievalChunk(
                content=chunk_pb2.Chunk(
                    text="Hello World\n",
                    path="foo/bar.py",
                    char_offset=0,
                    length=12,
                    line_offset=0,
                    length_in_lines=1,
                    documentation_metadata=chunk_pb2.DocumentationMetadata(
                        name="Git",
                        page_id="1",
                        headers=["#Header1", "#Header2"],
                        source_id="docset://Git",
                    ),
                ).SerializeToString(),
                score=1.0,
                blob_name=blob_name,
                chunk_index=0,
                metadata=[],
            )
        ],
        missing_external_sources=DocsetSearchChunksResult.MissingExternalSources(
            incomplete_external_source_ids=[]
        ),
    )

    result = dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                prefix="Hi AI!",
                suffix="",
                path="foo/foo.py",
                message="blah blah",
                selected_code="",
                chat_history=[],
            ),
            blobs=[blobs],
            external_source_ids=["docset://456"],
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )

    docset_client.get_implicit_docsets.assert_called_once_with(
        message_text="blah blah",
        request_context=request_context,
        timeout=mock.ANY,
    )

    docset_client.search.assert_called_once_with(
        doc_set_ids=["docset://456"],
        query=mock.ANY,
        num_results=32,
        transformation_key="dr-1",
        request_context=request_context,
        timeout=mock.ANY,
    )

    assert len(list(result.get_retrieved_chunks())) == 1
    assert len(list(result.get_missing_blob_names())) == 0
    assert not result.get_checkpoint_not_found()


def test_docset_server_enabled_with_non_auto_external_source_ids():
    """
    Test that docset-srv is called when external_source_ids is provided, even if
    disable_auto_external_sources is True.
    """
    tokenizer = base.tokenizers.create_tokenizer_by_name("fim")
    embedder_rpc_client = mock.MagicMock(spec=EmbedderClientProtocol)
    embedding_prompt_formatter = (
        prompt_format_retrieve.get_retrieval_prompt_formatter_by_name(
            "ethanol6-embedding-with-path-query", tokenizer=tokenizer
        )
    )
    docset_client = mock.MagicMock(spec=DocSetClient)
    dr = DocsetRetriever(
        embedder_client=embedder_rpc_client,
        docset_client=docset_client,
        query_tokenizer=tokenizer,
        origin=ChunkOrigin.DENSE_RETRIEVER.value,
        transformation_key="dr-1",
        num_results=32,
        query_prompt_formatter=embedding_prompt_formatter,
        search_timeout_ms=1000,
    )

    blob_name = gen_blob_name("123")
    blobs = Blobs.from_blob_names([blob_name])
    request_context = RequestContext.create()

    docset_client.get_implicit_docsets.return_value = [
        docset_pb2.DocSet(
            doc_set_id="docset://123",
            title="Docset 1",
            name="docset1",
        )
    ]

    dr.retrieve(
        RetrievalInput(
            prompt_input=ChatRetrieverPromptInput(
                prefix="Hi AI!",
                suffix="",
                path="foo/foo.py",
                message="blah blah",
                selected_code="",
                chat_history=[],
            ),
            blobs=[blobs],
            external_source_ids=["docset://456"],
            disable_auto_external_sources=True,
        ),
        request_context=request_context,
        auth_info=mock.MagicMock(tenant_id=None, tenant_name="dev-augie"),
    )

    docset_client.get_implicit_docsets.assert_not_called()
    docset_client.search.assert_called_once_with(
        doc_set_ids=["docset://456"],
        query=mock.ANY,
        num_results=32,
        transformation_key="dr-1",
        request_context=request_context,
        timeout=mock.ANY,
    )

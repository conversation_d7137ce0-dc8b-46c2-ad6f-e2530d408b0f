"""Router Retriever calling router inference and retrievers"""

import concurrent.futures
from typing import Sequence
from prometheus_client import Counter, Histogram

import opentelemetry.trace
import structlog
from base.prompt_format.common import RequestMessage, get_request_text_parts
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from base.prompt_format_router.pleasehold_prompt_formatter import (
    PleaseHoldPromptFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from services.lib.retrieval import dummy_executor, retriever_request_insight_builder
from services.lib.retrieval.retriever import (
    FindMissingResult,
    RetrievalChunk,
    RetrievalInput,
    Retriever,
    Category,
    RouterResponse,
    RouterRetrievalResult,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
)

tracer = opentelemetry.trace.get_tracer(__name__)

logger = structlog.get_logger()

INF = float("inf")

# Exponentially increasing buckets with ~30% width. Min is ~10ms and max is ~4s.
router_latency_buckets = tuple([0.01 * (1.3**i) for i in range(1, 24)] + [INF])

_router_latency = Histogram(
    "au_router_latency",
    "Latentity of the router processing in seconds",
    ["router_model", "request_source", "tenant_name"],
    buckets=router_latency_buckets,
)

# We usually only expect 0 or 1 router suggestions for files or docsets
number_of_router_suggestion_buckets = (0, 1, 2, 4, INF)

_router_num_files = Histogram(
    "au_router_num_files",
    "Number of files returned by the router",
    ["router_model", "request_source", "tenant_name"],
    buckets=number_of_router_suggestion_buckets,
)

_router_num_docsets = Histogram(
    "au_router_num_docsets",
    "Number of docsets returned by the router",
    ["router_model", "request_source", "tenant_name"],
    buckets=number_of_router_suggestion_buckets,
)

_router_miss_files = Histogram(
    "au_router_miss_files",
    "Number of files returned by the router that have no matching blob",
    ["router_model", "request_source", "tenant_name"],
    buckets=number_of_router_suggestion_buckets,
)

_router_miss_docsets = Histogram(
    "au_router_miss_docsets",
    "Number of docsets returned by the router that have no matching docset",
    ["router_model", "request_source", "tenant_name"],
    buckets=number_of_router_suggestion_buckets,
)

_router_response_category = Counter(
    "au_router_response_category",
    "Category of request provided by router",
    ["router_model", "request_source", "tenant_name", "category"],
)

_router_response_failure = Counter(
    "au_router_response_failure",
    "Number of times the router failed to provide a valid response",
    ["router_model", "request_source", "tenant_name"],
)


class Router(Retriever[ChatRetrieverPromptInput]):
    """
    Router uses the router inference model to orchestrates retrieval and categorize requests
    """

    def __init__(
        self,
        name: str,
        code_retriever: Retriever[ChatRetrieverPromptInput],
        docset_retriever: Retriever[ChatRetrieverPromptInput],
        user_guided_retriever: Retriever[ChatRetrieverPromptInput],
        router_client: InfererClient,
        router_prompt_formatter: PleaseHoldPromptFormatter,
        router_max_output_length: int,
        router_timeout_s: int,
        ri_builder: retriever_request_insight_builder.RetrieverRequestInsightBuilder
        | None = None,
    ):
        self._name = name
        self._code_retriever = code_retriever
        self._docset_retriever = docset_retriever
        self._user_guided_retriever = user_guided_retriever
        self._router_client = router_client
        self._router_prompt_formatter = router_prompt_formatter
        self._router_timeout_s = router_timeout_s
        self._request_insight_builder = ri_builder
        self._router_max_output_length = router_max_output_length
        self._docset_set = set(self._router_prompt_formatter.docsets)

    def _parse_router_response(self, response_str: str) -> RouterResponse:
        """Parse the router response - expect three lines for category, filenames, docsets"""
        lines = response_str.split("\n")
        category = lines[0]
        filenames = lines[1].split(",")
        docsets = lines[2].split(",")

        category = Category(int(category))
        # Convert string `none` into None
        filenames = [f for f in filenames if f.lower() not in ["none", ""]]
        docsets = [d for d in docsets if d.lower() not in ["none", ""]]

        return RouterResponse(category, filenames, docsets)

    def _run_router_inference(
        self,
        message: RequestMessage,
        code_retrieved_chunks: list[RetrievalChunk],
        input_: RetrievalInput[ChatRetrieverPromptInput],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor,
        tokenizer,
    ) -> RouterResponse | None:
        """Run router inference and return the parsed response.

        Args:
            message: The message to process
            code_retrieved_chunks: Retrieved code chunks to include in prompt
            input_: The original retrieval input
            request_context: Request context for tracing/logging
            auth_info: Authentication information
            executor: Executor for async tasks
            tokenizer: Tokenizer instance

        Returns:
            RouterResponse if successful, None if failed
        """
        response_str = None
        prompt_output = None
        with tracer.start_as_current_span("router_inference"):
            try:
                router_prompt_chunks = [
                    chunk.to_prompt_chunk() for chunk in code_retrieved_chunks
                ]

                router_prompt_input = ChatPromptInput(
                    message=message,
                    retrieved_chunks=router_prompt_chunks,
                    path=input_.prompt_input.path,
                    prefix=input_.prompt_input.prefix,
                    suffix=input_.prompt_input.suffix,
                    chat_history=tuple(input_.prompt_input.chat_history),
                    selected_code=input_.prompt_input.selected_code,
                    prefix_begin=input_.prompt_input.prefix_begin,
                    suffix_end=input_.prompt_input.suffix_end,
                )
                prompt_output = self._router_prompt_formatter.format_prompt(
                    router_prompt_input,
                )

                # Call router model
                token_response: InferenceClientProtocol.Reply = (
                    self._router_client.infer(
                        input_tokens=prompt_output.tokens,
                        max_output_length=self._router_max_output_length,
                        end_token_ids=[tokenizer.special_tokens.im_end],
                        top_k=0,
                        top_p=0.0,
                        temperature=0.0,
                        random_seed=0,
                        request_context=request_context,
                        timeout_s=self._router_timeout_s,
                        sequence_id=input_.sequence_id or 0,
                    )
                )

                response_str = tokenizer.detokenize(token_response.output_tokens)

                router_response = self._parse_router_response(response_str)

                # Record router response to RI
                if self._request_insight_builder:
                    executor.submit(
                        self._request_insight_builder.record_router_response,
                        prompt_output,
                        token_response,
                        tokenizer,
                        router_response,
                        None,
                        request_context,
                        auth_info,
                    )
                return router_response
            except Exception as e:
                # Warn about router failures, continue with default retrieval
                if response_str is not None:
                    logger.warning("Failed to parse router response")
                    ri_err_msg = f"Failed to parse router response: {response_str}: {e}"
                else:
                    ri_err_msg = f"Failed to get router response: {e}"
                    logger.warning(ri_err_msg)

                # Record router response error to RI
                if prompt_output and self._request_insight_builder:
                    executor.submit(
                        self._request_insight_builder.record_router_response,
                        prompt_output,
                        None,
                        tokenizer,
                        None,
                        ri_err_msg,
                        request_context,
                        auth_info,
                    )
                return None

    def retrieve(
        self,
        input_: RetrievalInput[ChatRetrieverPromptInput],
        request_context: RequestContext,
        auth_info: AuthInfo,
        executor: concurrent.futures.Executor | None = None,
    ) -> RouterRetrievalResult:
        """
        Perform retrievers and router calls and logic

        Returns RetrievalResult plus category (if router inference call succeeds)
        """
        if executor is None:
            executor = dummy_executor.DummyExecutor()

        with tracer.start_as_current_span("code_retrieval"):
            original_retrieval_result = self._code_retriever.retrieve(
                input_, request_context, auth_info, executor
            )

        code_retrieved_chunks = list(original_retrieval_result.get_retrieved_chunks())
        missing_blob_names = list(original_retrieval_result.get_missing_blob_names())
        checkpoint_not_found = original_retrieval_result.get_checkpoint_not_found()

        # Filter only text nodes in message
        message = get_request_text_parts(input_.prompt_input.message)

        pre_process_output = None

        if message:
            with tracer.start_as_current_span("router_inference"):
                with _router_latency.labels(
                    self._name, request_context.request_source, auth_info.tenant_name
                ).time():
                    pre_process_output = self._run_router_inference(
                        message,
                        code_retrieved_chunks,
                        input_,
                        request_context,
                        auth_info,
                        executor,
                        self._router_prompt_formatter.tokenizer,
                    )

        if pre_process_output is None:
            _router_response_failure.labels(
                self._name, request_context.request_source, auth_info.tenant_name
            ).inc()
        else:
            _router_response_category.labels(
                self._name,
                request_context.request_source,
                auth_info.tenant_name,
                pre_process_output.category.name,
            ).inc()

        # Inner function to retrieve from docsets
        def retrieve_docsets() -> list[RetrievalChunk]:
            external_source_ids = list(input_.external_source_ids or [])
            if pre_process_output is not None:
                # Normalize pleasehold generated docset_ids to start with `docset://`
                docset_full_ids = [
                    "docset://" + docset.replace("docset://", "")
                    for docset in pre_process_output.docsets
                ]
                miss = 0
                for docset in docset_full_ids:
                    if docset not in self._docset_set:
                        miss += 1
                _router_num_docsets.labels(
                    self._name, request_context.request_source, auth_info.tenant_name
                ).observe(len(pre_process_output.docsets))
                _router_miss_docsets.labels(
                    self._name, request_context.request_source, auth_info.tenant_name
                ).observe(miss)
                external_source_ids += docset_full_ids

            docset_input = RetrievalInput(
                prompt_input=input_.prompt_input,
                blobs=input_.blobs,
                external_source_ids=external_source_ids,
                disable_auto_external_sources=input_.disable_auto_external_sources,
            )
            docset_retriever_output = self._docset_retriever.retrieve(
                docset_input,
                request_context,
                auth_info,
                executor,
            )
            return list(docset_retriever_output.get_retrieved_chunks())

        # Inner function to retrieve from UG
        def retrieve_user_guided() -> list[RetrievalChunk]:
            user_guided_blobs = list(input_.user_guided_blobs or [])

            if pre_process_output is not None:
                # blob names for files have to be in retrieved chunks or currently open file
                router_guided_blobs = [
                    chunk.blob_name
                    for chunk in code_retrieved_chunks
                    if chunk.path in pre_process_output.filenames and chunk.blob_name
                ]
                if (
                    input_.prompt_input.path in pre_process_output.filenames
                    and input_.prompt_input.blob_name
                ):
                    # Add the currently open file if it is in the router output
                    router_guided_blobs.append(input_.prompt_input.blob_name)
                _router_num_files.labels(
                    self._name, request_context.request_source, auth_info.tenant_name
                ).observe(len(pre_process_output.filenames))
                _router_miss_files.labels(
                    self._name, request_context.request_source, auth_info.tenant_name
                ).observe(len(pre_process_output.filenames) - len(router_guided_blobs))
                user_guided_blobs += router_guided_blobs

            user_guided_blobs = list(set(user_guided_blobs))  # remove duplicates

            user_guided_input = RetrievalInput(
                prompt_input=input_.prompt_input,
                blobs=input_.blobs,
                user_guided_blobs=user_guided_blobs,
            )
            user_guided_retriever_output = self._user_guided_retriever.retrieve(
                user_guided_input,
                request_context,
                auth_info,
                executor,
            )
            return list(user_guided_retriever_output.get_retrieved_chunks())

        # Run both inner functions in parallel to reduce latency
        with tracer.start_as_current_span("additional_retrievers"):
            docset_retrieved_chunks, user_guided_retrieved_chunks = executor.map(
                lambda f: f(),
                [retrieve_docsets, retrieve_user_guided],
            )

        all_retrieved_chunks = (
            user_guided_retrieved_chunks
            + docset_retrieved_chunks
            + code_retrieved_chunks
        )
        logger.info(
            "Returning user_guided: %d, docset: %d, code: %d",
            len(user_guided_retrieved_chunks),
            len(docset_retrieved_chunks),
            len(code_retrieved_chunks),
        )
        result = RouterRetrievalResult(
            retrieved_chunks=all_retrieved_chunks,
            missing_blob_names=missing_blob_names,
            checkpoint_not_found=checkpoint_not_found,
            category=pre_process_output.category if pre_process_output else None,
        )

        return result

    def find_missing(
        self,
        blob_names: Sequence[str],
        request_context: RequestContext,
        executor: concurrent.futures.Executor | None = None,
    ) -> FindMissingResult:
        return self._code_retriever.find_missing(
            blob_names, request_context=request_context, executor=executor
        )

    def get_name(self) -> str:
        """Returns the class name of the retriever.

        This is used for logging purposes.
        """
        return __class__.__name__

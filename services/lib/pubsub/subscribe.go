package pubsub

import (
	"context"
	"errors"
	"fmt"
	"runtime/debug"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/rs/zerolog/log"
)

const defaultMaxConcurrentReceivers int = 50

type SubscribeClient interface {
	Receive(ctx context.Context, handler func(ctx context.Context, msg []byte) error) error

	Exists(ctx context.Context) (bool, error)
	Close() error
}

type SubscribeClientConfig struct {
	ProjectId              string
	SubscriptionId         string
	MaxConcurrentReceivers int
	// If not set, defaults to the same value as MaxConcurrentReceivers.
	DeadLetterMaxConcurrentReceivers int

	// Optional dead letter queue configuration. Ignored if DeadLetterSubscriptionId is empty.
	DeadLetterSubscriptionId    string
	ConfigureDeadLetterHandling ConfigureDeadLetterHandling
}

// SubscribeClient implementation
type subscribeClientImpl struct {
	client                      *pubsub.Client
	sub                         *pubsub.Subscription
	deadLetterSub               *pubsub.Subscription
	configureDeadLetterHandling ConfigureDeadLetterHandling
}

// Returns true if dead letters should be processed.
type ConfigureDeadLetterHandling func() bool

// Create a new SubscribeClient without a dead letter queue.
func NewSubscribeClient(ctx context.Context, config *SubscribeClientConfig) (SubscribeClient, error) {
	client, err := pubsub.NewClient(ctx, config.ProjectId)
	if err != nil {
		return nil, err
	}

	sub := client.Subscription(config.SubscriptionId)
	var deadLetterSub *pubsub.Subscription
	if config.DeadLetterSubscriptionId != "" {
		deadLetterSub = client.Subscription(config.DeadLetterSubscriptionId)
	}

	// NOTE(mpauly): MaxOutstandingMessages controls the maximum number of goroutines the subscriber
	// will create for handling incoming messages. The NumGoroutines setting counterintuitively does
	// not affect parallelism in the handler function and I found MaxOutstandingBytes to be
	// ineffective at reducing the pod's memory footprint.
	maxConcurrentReceivers := defaultMaxConcurrentReceivers
	if config.MaxConcurrentReceivers > 0 {
		maxConcurrentReceivers = config.MaxConcurrentReceivers
	}
	sub.ReceiveSettings.MaxOutstandingMessages = maxConcurrentReceivers
	if deadLetterSub != nil {
		if config.DeadLetterMaxConcurrentReceivers > 0 {
			deadLetterSub.ReceiveSettings.MaxOutstandingMessages = config.DeadLetterMaxConcurrentReceivers
		} else {
			deadLetterSub.ReceiveSettings.MaxOutstandingMessages = maxConcurrentReceivers
		}
	}

	return &subscribeClientImpl{
		client:                      client,
		sub:                         sub,
		deadLetterSub:               deadLetterSub,
		configureDeadLetterHandling: config.ConfigureDeadLetterHandling,
	}, nil
}

func (s *subscribeClientImpl) Receive(
	ctx context.Context,
	handler func(ctx context.Context, data []byte) error,
) error {
	if s.deadLetterSub != nil && s.configureDeadLetterHandling != nil {
		go s.handleDeadLetters(ctx, handler)
	}
	return receive(ctx, s.sub, handler)
}

func (s *subscribeClientImpl) Exists(ctx context.Context) (bool, error) {
	return s.sub.Exists(ctx)
}

func (s *subscribeClientImpl) Close() error {
	return s.client.Close()
}

// Run an infinite loop that polls on s.configureDeadLetterHandling and enables/disables dead letter
// processing appropriately.
func (s *subscribeClientImpl) handleDeadLetters(
	ctx context.Context,
	handler func(ctx context.Context, data []byte) error,
) {
	ticker := time.NewTicker(10 * time.Second)
	handlingDeadLetters := false
	var deadLetterCtx context.Context
	var deadLetterCancel context.CancelFunc
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			shouldHandleDeadLetters := s.configureDeadLetterHandling()
			if shouldHandleDeadLetters && !handlingDeadLetters {
				log.Info().Msg("Enabling dead letter handling")
				handlingDeadLetters = true
				deadLetterCtx, deadLetterCancel = context.WithCancel(ctx)
				defer deadLetterCancel()
				go func() {
					err := receive(deadLetterCtx, s.deadLetterSub, handler)
					if err != nil && err != context.Canceled {
						log.Error().Err(err).Msg("Failed to receive dead letter messages")
					}
				}()
			} else if !shouldHandleDeadLetters && handlingDeadLetters {
				log.Info().Msg("Disabling dead letter handling")
				handlingDeadLetters = false
				deadLetterCancel()
			}
		}
	}
}

// Catch panics, returning an error instead
func runAndCatchPanics(handler func() error) (err error) {
	defer func() {
		if r := recover(); r != nil {
			// Capture the stack trace
			stack := debug.Stack()

			switch x := r.(type) {
			case string:
				err = errors.New(x)
			case error:
				err = x
			default:
				err = fmt.Errorf("panic: %v", r)
			}
			log.Error().
				Err(err).
				Str("stack_trace", string(stack)).
				Msg("Recovered from panic in handler")
		}
	}()
	return handler()
}

func receive(
	ctx context.Context,
	sub *pubsub.Subscription,
	handler func(ctx context.Context, data []byte) error,
) error {
	return sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		err := runAndCatchPanics(func() error {
			return handler(ctx, msg.Data)
		})
		if err != nil {
			log.Error().Err(err).Msg("Failed to process message")
			msg.Nack()
		} else {
			msg.Ack()
		}
	})
}

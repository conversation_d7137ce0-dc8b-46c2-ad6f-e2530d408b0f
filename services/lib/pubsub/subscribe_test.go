package pubsub

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRunAndCatchPanics(t *testing.T) {
	t.Run("no panic", func(t *testing.T) {
		err := runAndCatchPanics(func() error {
			return nil
		})
		assert.NoError(t, err)
	})

	t.Run("with error", func(t *testing.T) {
		expectedErr := errors.New("test error")
		err := runAndCatchPanics(func() error {
			return expectedErr
		})
		assert.Equal(t, expectedErr, err)
	})

	t.Run("with panic", func(t *testing.T) {
		err := runAndCatchPanics(func() error {
			panic("test panic")
		})
		assert.Error(t, err) // Panic is caught and logged
	})
}

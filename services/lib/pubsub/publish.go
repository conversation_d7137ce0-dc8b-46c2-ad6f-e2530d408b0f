package pubsub

import (
	"bytes"
	"context"
	"sync"
	"text/template"

	"cloud.google.com/go/pubsub"
	"github.com/rs/zerolog/log"
)

type PublishClient interface {
	// Waits for the publish to finish before returning
	Publish(ctx context.Context, namespace string, data []byte) error
	PublishWithOrderingKey(ctx context.Context, namespace string, data []byte, orderingKey string) error
	Close() error
}

type publishClientImpl struct {
	client        *pubsub.Client
	topicTemplate *template.Template
	lock          sync.Mutex
	topicMap      map[string]*pubsub.Topic
}

func NewPublishClient(ctx context.Context, projectID, topicPattern string) (PublishClient, error) {
	client, err := pubsub.NewClient(ctx, projectID)
	if err != nil {
		return nil, err
	}

	template, err := template.New("topicTemplate").Parse(topicPattern)
	if err != nil {
		return nil, err
	}

	return &publishClientImpl{
		client:        client,
		topicTemplate: template,
		lock:          sync.Mutex{},
		topicMap:      make(map[string]*pubsub.Topic),
	}, nil
}

func (p *publishClientImpl) getTopic(namespace string) (*pubsub.Topic, error) {
	p.lock.Lock()
	defer p.lock.Unlock()
	topic, ok := p.topicMap[namespace]
	if !ok {
		buf := &bytes.Buffer{}
		err := p.topicTemplate.Execute(buf, map[string]string{"Namespace": namespace})
		if err != nil {
			log.Error().Err(err).Msg("Failed to create topic name")
			return nil, err
		}
		topicName := buf.String()
		log.Info().Msgf("Creating topic client %s", topicName)
		topic = p.client.Topic(topicName)
		p.topicMap[namespace] = topic
	}
	return topic, nil
}

func (p *publishClientImpl) Publish(ctx context.Context, namespace string, data []byte) error {
	topic, err := p.getTopic(namespace)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get topic")
		return err
	}
	result := topic.Publish(ctx, &pubsub.Message{
		Data: data,
	})

	_, err = result.Get(ctx)
	return err
}

func (p *publishClientImpl) PublishWithOrderingKey(ctx context.Context, namespace string, data []byte, orderingKey string) error {
	topic, err := p.getTopic(namespace)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get topic")
		return err
	}

	topic.EnableMessageOrdering = true
	result := topic.Publish(ctx, &pubsub.Message{
		Data:        data,
		OrderingKey: orderingKey,
	})

	_, err = result.Get(ctx)
	return err
}

func (p *publishClientImpl) Close() error {
	err := p.client.Close()
	if err != nil {
		log.Error().Err(err).Msg("Failed to close pubsub client")
	}
	log.Info().Msg("Closed pubsub client")
	for _, topic := range p.topicMap {
		topic.Stop()
		log.Info().Msgf("Stopped pubsub topic %s", topic.ID())
	}
	return err
}

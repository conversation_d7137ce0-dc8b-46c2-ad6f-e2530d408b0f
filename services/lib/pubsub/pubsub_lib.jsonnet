// Library for creating pubsub topics and subscriptions for integrations.
// Assumes that there will be one topic and one subscription per namespace.
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';

local prefix(namespace, publisherAppName) =
  '%s-%s' % [namespace, publisherAppName];

local publisherTopicTemplate(publisherAppName) =
  '{{ .Namespace }}-%s-topic' % publisherAppName;

local getTopicName(namespace, appName, isDeadLetterTopic=false, topicSuffix=null) =
  '%s%s-topic' % [prefix(namespace, if topicSuffix == null then appName else topicSuffix), if isDeadLetterTopic then '-deadletter' else ''];

// Create an IAM policy for a topic.
// Args:
//   namespace: the namespace to create the policy in
//   appName: the name of the app that will publish to the topic
//   publisherAppName: the name of the app that owns the topic
//   serviceAccount: the service account that will publish to the topic
//   cloud: the cloud to deploy to
//   isDeadLetterTopic: true if this is a deadletter topic
local publisherTopicIAMPolicy(
  env, namespace, appName, publisherAppName, serviceAccount, cloud, isDeadLetterTopic=false, topicSuffix=null
      ) =
  // Use the publisherAppName here since the publisher owns and creates the topic.
  local topicName = getTopicName(namespace, publisherAppName, isDeadLetterTopic, topicSuffix);
  local policyName = '%s%s-topic-policy' % [prefix(namespace, appName), if isDeadLetterTopic then '-deadletter' else ''];

  {
    apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
    kind: 'IAMPartialPolicy',
    metadata: {
      name: policyName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      resourceRef: {
        kind: 'PubSubTopic',
        name: topicName,
      },
      bindings: [
        {
          role: 'roles/pubsub.publisher',
          members: [
            { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
          ] + if isDeadLetterTopic then [
            // The project-level pub/sub service account needs to be able to publish to deadletter
            // topics. See https://cloud.google.com/pubsub/docs/handling-failures#grant_forwarding_permissions.
            { member: 'serviceAccount:<EMAIL>' % cloudInfo[cloud].projectNumber },
          ] else [],
        },
      ],
    },
  };

// Create a topic for the publisher in a given namespace and its IAM policy. The name of the topic
// is {namespace}-{appName}-topic. Warning: topics will conflict if the same
// namespace is used in multiple clusters.
//
// Args:
//    namespace is the namespace to create the topic in.
//    messageRetentionDurationSeconds is the number of seconds to retain messages.
//    isDeadLetterTopic is true if this is a deadletter topic. This is for internal use; if you're
//      trying to configure a deadletter topic you probably want to set
//      `deadLetterMaxDeliveryAttempts` in `namespaceSubscriber` instead.
local publisherTopic(
  cloud,
  env,
  namespace,
  appName,
  serviceAccount,
  messageRetentionDurationSeconds=null,
  isDeadLetterTopic=false,
  topicSuffix=null
      ) =

  local topicName = getTopicName(namespace, appName, isDeadLetterTopic, topicSuffix);
  local iamPartialPolicy = publisherTopicIAMPolicy(env=env,
                                                   namespace=namespace,
                                                   appName=appName,
                                                   publisherAppName=appName,
                                                   serviceAccount=serviceAccount,
                                                   cloud=cloud,
                                                   isDeadLetterTopic=isDeadLetterTopic,
                                                   topicSuffix=topicSuffix);
  local objects = [
    {
      apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
      kind: 'PubSubTopic',
      metadata: {
        name: topicName,
        namespace: namespace,
        // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
        // topic is deleted before the pod.
        annotations: if env == 'DEV' then {
          'cnrm.cloud.google.com/deletion-policy': 'abandon',
        } else {},
        labels: {
          app: appName,
        },
      },
      spec: {
        [if messageRetentionDurationSeconds != null then 'messageRetentionDuration']: '%ss' % messageRetentionDurationSeconds,
      },
    },
    iamPartialPolicy,
  ];
  {
    objects: objects,
    topicName: topicName,
  };

// Create a per-namespace subscription to a per-namespace topic. The subscription is to the topic
// named {namespace}-{publisherAppName}-topic.
//
// The subscription is named {namespace}-{appName}-sub, except in central namespaces, where it is named
// {namespace}-{appName}-{cloudShortName}-sub. The design assumes that the same namespace is not used
// in mulitple clusters in the same GCP project, so that the subscription name is unique.
//
// If a deadletter topic is configured (via `deadLetterMaxDeliverAttempts`), the deadletter topic
// is named {namespace}-{publisherAppName}-deadletter-topic and the deadletter subscription is
// named {namespace}-{appName}-deadletter-sub or {namespace}-{appName}-{cloudShortName}-deadletter-sub.
//
// Args:
//   externalTopicRef: if true, the subscription references the topic by GCP resource ID (i.e. projects/...)
//     rather than by k8s name of the pubsubtopic.
//   deadLetterMaxDeliveryAttempts: If >0, the subscription will be configured with a dead letter
//     topic that holds messages that have failed after this many attempts. See
//     https://cloud.google.com/pubsub/docs/handling-failures
//   isDeadLetterSub: true if this is a deadletter subscription. This is for internal use; unless
//     you know what you're doing, use deadLetterMaxDeliveryAttempts to control dead lettering.
//   deadLetterSubSpec: Subscription spec for the dead letter subscription (if dead lettering is
//     enabled).
local namespaceSubscriber(
  env,
  namespace,
  cloud,
  appName,
  publisherAppName,
  serviceAccount,
  externalTopicRef=false,
  deadLetterMaxDeliveryAttempts=0,
  spec={},
  deadLetterSubSpec={},
  isDeadLetterSub=false,
      ) =
  local subscriptionName = if cloudInfo.isCentralNamespace(env, namespace, cloud) then
    '%s-%s%s-sub' % [prefix(namespace, appName), cloudInfo[cloud].shortName, if isDeadLetterSub then '-deadletter' else '']
  else
    '%s%s-sub' % [prefix(namespace, appName), if isDeadLetterSub then '-deadletter' else ''];

  local deadLetterPublisher = if deadLetterMaxDeliveryAttempts <= 0 then null else
    publisherTopic(cloud, env, namespace, appName, serviceAccount, isDeadLetterTopic=true);
  local deadLetterSubscriber = if deadLetterMaxDeliveryAttempts <= 0 then null else
    namespaceSubscriber(env, namespace, cloud, appName, '%s-deadletter' % appName, serviceAccount, isDeadLetterSub=true, spec=deadLetterSubSpec);

  local fullSpec = std.mergePatch({
    topicRef: if externalTopicRef then {
      external: 'projects/%s/topics/%s-topic' % [cloudInfo[cloud].projectId, prefix(namespace, publisherAppName)],
    } else {
      name: '%s-topic' % prefix(namespace, publisherAppName),
    },
    ackDeadlineSeconds: 30,
    retryPolicy: {
      minimumBackoff: '5s',
      maximumBackoff: '300s',
    },
    // Retain messages for 1 hour in dev and 7 days in staging/prod.
    messageRetentionDuration: if env == 'DEV' then '3600s' else '604800s',
    retainAckedMessages: false,
    deadLetterPolicy: if deadLetterMaxDeliveryAttempts > 0 then {
      // When externalTopicRef is true, we need to use the 'external' field for deadLetterTopicRef as well.
      // This ensures the dead letter topic reference is properly formatted as a GCP resource ID.
      // Without this, the PubSub API will reject the subscription with validation errors.
      deadLetterTopicRef: if externalTopicRef then {
        external: 'projects/%s/topics/%s-deadletter-topic' % [cloudInfo[cloud].projectId, prefix(namespace, appName)],
      } else {
        name: deadLetterPublisher.topicName,
        namespace: namespace,
      },
      maxDeliveryAttempts: deadLetterMaxDeliveryAttempts,
    } else null,
  }, spec);
  {
    subscriptionName: subscriptionName,
    deadLetterSubscriptionName: if deadLetterSubscriber == null then null else deadLetterSubscriber.subscriptionName,
    objects: lib.flatten([
      if deadLetterPublisher != null then deadLetterPublisher.objects else [],
      if deadLetterSubscriber != null then deadLetterSubscriber.objects else [],
      {
        apiVersion: 'pubsub.cnrm.cloud.google.com/v1beta1',
        kind: 'PubSubSubscription',
        metadata: {
          name: subscriptionName,
          namespace: namespace,
          // Don't delete in dev because this causes a lot of errors at the end of a test run, if the
          // topic is deleted before the pod.
          annotations: if env == 'DEV' then {
            'cnrm.cloud.google.com/deletion-policy': 'abandon',
          } else {},
          labels: {
            app: appName,
          },
        },
        spec: fullSpec,
      },
      {
        apiVersion: 'iam.cnrm.cloud.google.com/v1beta1',
        kind: 'IAMPartialPolicy',
        metadata: {
          name: '%s%s-subscription-policy' % [prefix(namespace, appName), if isDeadLetterSub then '-deadletter' else ''],
          namespace: namespace,
          labels: {
            app: appName,
          },
          annotations: if env == 'DEV' then {
          } else {
          },
        },
        spec: {
          resourceRef: {
            kind: 'PubSubSubscription',
            name: subscriptionName,
          },
          bindings: [
            {
              role: 'roles/pubsub.subscriber',
              members: [
                { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
              ] + if deadLetterMaxDeliveryAttempts > 0 then [
                { member: 'serviceAccount:<EMAIL>' % cloudInfo[cloud].projectNumber },
              ] else [],
            },
            {
              role: 'roles/pubsub.viewer',
              members: [
                { member: 'serviceAccount:%s' % serviceAccount.serviceAccountGcpEmailAddress },
              ],
            },
          ],
        },
      },
    ]),
  };

{
  // publisherTopic creates the topic and its IAM policy
  publisherTopic: publisherTopic,

  // getTopicName returns the name of the topic for a given app name
  getTopicName: getTopicName,

  // publisherTopicTemplate returns the name of the topic for a given app name
  publisherTopicTemplate: publisherTopicTemplate,

  // publisherTopicIAMPolicy creates the IAM policy for a topic
  publisherTopicIAMPolicy: publisherTopicIAMPolicy,

  // namespaceSubscriber created the subscription and its IAM policy
  namespaceSubscriber: namespaceSubscriber,
}

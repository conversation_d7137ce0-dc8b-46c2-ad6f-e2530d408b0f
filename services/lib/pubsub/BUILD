load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "pubsub_go",
    srcs = [
        "publish.go",
        "subscribe.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/pubsub",
    visibility = ["//services:__subpackages__"],
    deps = [
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_pubsub//:pubsub",
    ],
)

go_test(
    name = "pubsub_go_test",
    srcs = [
        "subscribe_test.go",
    ],
    embed = [":pubsub_go"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)

jsonnet_library(
    name = "pubsub-lib",
    srcs = [
        "pubsub_lib.jsonnet",
    ],
    visibility = [
        "//services:__subpackages__",
        "//tools/tenant_manager:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

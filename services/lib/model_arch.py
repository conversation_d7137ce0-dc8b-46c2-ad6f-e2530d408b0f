"""Model architecture parameters."""

from dataclasses import dataclass

from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class ModelArch:
    """Model architecture parameters."""

    arch_type: str

    # number of layers
    num_layers: int

    # size of the vocab
    vocab_size: int

    # dimension of the embeddings layer
    emb_dim: int

    # number of heads
    num_heads: int

    # dimension per head
    head_dim: int

    # rotary embedding percentage
    rotary_pct: float = 0

    rotary_theta: float = 1e4

    rotary_scaling_factor: float = 1.0

    norm_eps: float = 1e-5

    # For Llama-based models
    num_queries_per_head: int = 1
    mlp_dim_divisible_by: int = 256
    ffn_dim_multiplier: float = 1.0
    attn_split_head_mode: str = "NO_SPLIT"
    qkv_only_bias: bool = False
    output_projection_dim: int | None = None

    # Starcoder1 models have positional embedding weights of a fixed seqlen.
    # This field is optional since it is unneeded by non-SC1 models.
    # NOTE: the SC1-fp16 implementation ignores the model_spec and derives this value directly
    # from the shape of weights on disk. As a result, this field works only for fp8 models that
    # do respect the model_spec.
    max_position_embeddings: int | None = None

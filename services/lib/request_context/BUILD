load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")
load("@python_pip//:requirements.bzl", "requirement")
load("//base:base.bzl", "BASE_VISIBILITY")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

rust_library(
    name = "request_context_rs",
    srcs = ["lib.rs"],
    aliases = aliases(),
    crate_name = "request_context",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = BASE_VISIBILITY,
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//services/auth/central/server:auth_entities_rs_proto",
    ],
)

rust_test(
    name = "request_context_rs_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":request_context_rs",
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

py_library(
    name = "request_context_py",
    srcs = [
        "request_context.py",
    ],
    visibility = [
        "//experimental:__subpackages__",
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        requirement("pydantic"),
        requirement("structlog"),
        requirement("grpcio"),
    ],
)

pytest_test(
    name = "request_context_py_test",
    srcs = [
        "request_context_test.py",
    ],
    deps = [
        ":request_context_py",
    ],
)

go_library(
    name = "request_context_go",
    srcs = ["request_context.go"],
    importpath = "github.com/augmentcode/augment/services/lib/request_context",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "request_context_go_test",
    srcs = [
        "request_context_test.go",
    ],
    embed = [":request_context_go"],
    deps = [
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
    ],
)

package requestcontext

import (
	"context"
	"strings"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

const (
	// Keys for our request context metadata.
	requestIdKey        = "x-request-id"
	requestSessionIdKey = "x-request-session-id"
	requestSourceKey    = "x-request-source"
	authKey             = "authorization" // pragma: allowlist secret

	// Expected prefix for authorization metadata.
	bearerPrefix = "Bearer "
)

// Holds our custom gRPC context from a service <> service request.
type RequestContext struct {
	// The request id for the request. Assigned a UUID if not provided by the caller.
	RequestId RequestId

	// The session id for the request. Assigned the same value as `RequestId` if not provided by the
	// caller.
	RequestSessionId RequestSessionId

	// A human-readable string describing where the request came from. Defaults to "unknown" if
	// not provided.
	RequestSource string

	// The service token for the request, with the bearer prefix removed. Defaults to the empty
	// string if not provided.
	AuthToken secretstring.SecretString
}

// Create a new request context.
func New(
	requestId RequestId, requestSessionId RequestSessionId, requestSource string, authToken secretstring.SecretString,
) *RequestContext {
	return &RequestContext{
		RequestId:        requestId,
		RequestSessionId: requestSessionId,
		RequestSource:    requestSource,
		AuthToken:        authToken,
	}
}

func (rc *RequestContext) WithAuthToken(authToken secretstring.SecretString) *RequestContext {
	return &RequestContext{
		RequestId:        rc.RequestId,
		RequestSessionId: rc.RequestSessionId,
		RequestSource:    rc.RequestSource,
		AuthToken:        authToken,
	}
}

// Annotate the log context with the request id and session_id.
func (rc *RequestContext) AnnotateLogContext(ctx context.Context) context.Context {
	if rc == nil {
		return ctx
	}
	return log.Ctx(ctx).With().Str("request_id", rc.RequestId.String()).Str("session_id", rc.RequestSessionId.String()).Logger().WithContext(ctx)
}

type RequestId string

func (r RequestId) String() string {
	return string(r)
}

func NewRandomRequestId() RequestId {
	return RequestId(uuid.New().String())
}

type RequestSessionId string

func (r RequestSessionId) String() string {
	return string(r)
}

func NewRandomRequestSessionId() RequestSessionId {
	return RequestSessionId(uuid.New().String())
}

// Create a new request context from a gRPC context.
func FromGrpcContext(ctx context.Context) (*RequestContext, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, status.Error(codes.InvalidArgument, "Failed to get metadata from context")
	}

	requestId := getSingleMetadataValue(md, requestIdKey)
	if requestId == "" {
		requestId = uuid.New().String()
	}

	requestSessionId := getSingleMetadataValue(md, requestSessionIdKey)
	if requestSessionId == "" {
		requestSessionId = requestId
	}

	requestSource := getSingleMetadataValue(md, requestSourceKey)
	if requestSource == "" {
		requestSource = "unknown"
	}

	authMetadata := getSingleMetadataValue(md, authKey)
	var authToken string
	if authMetadata == "" {
		authToken = ""
	} else if strings.HasPrefix(authMetadata, bearerPrefix) {
		authToken = authMetadata[len(bearerPrefix):]
	} else {
		return nil, status.Error(codes.InvalidArgument, "Auth token does not use Bearer prefix")
	}

	return New(RequestId(requestId), RequestSessionId(requestSessionId), requestSource,
		secretstring.New(authToken)), nil
}

func NewIncomingContext(ctx context.Context, rc *RequestContext) context.Context {
	return metadata.NewIncomingContext(ctx, rc.ToMetadata())
}

func NewOutgoingContext(ctx context.Context, rc *RequestContext) context.Context {
	return metadata.NewOutgoingContext(ctx, rc.ToMetadata())
}

// Convert the request context to gRPC metadata.
func (rc *RequestContext) ToMetadata() metadata.MD {
	return metadata.New(map[string]string{
		requestIdKey:        string(rc.RequestId),
		requestSessionIdKey: string(rc.RequestSessionId),
		requestSourceKey:    rc.RequestSource,
		authKey:             "Bearer " + rc.AuthToken.Expose(), // pragma: allowlist secret
	})
}

// Small helper to get a singular value from metadata because metadata values are lists. Returns the
// empty string if the key is not present.
func getSingleMetadataValue(md metadata.MD, key string) string {
	values := md.Get(key)
	if len(values) == 0 {
		return ""
	}
	return values[0]
}

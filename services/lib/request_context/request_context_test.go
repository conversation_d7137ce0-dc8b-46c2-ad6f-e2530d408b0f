package requestcontext

import (
	"bytes"
	"context"
	"encoding/json"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/metadata"
)

const (
	requestId        = "fb9dda31-7bf6-4300-a28b-ebdfc916c351"
	requestSessionId = "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
	requestSource    = "client"
	authToken        = "token"
)

func emptyIncomingContext() context.Context {
	ctx := context.Background()
	md := metadata.New(map[string]string{})
	return metadata.NewIncomingContext(ctx, md)
}

func incomingContext() context.Context {
	ctx := context.Background()
	md := metadata.New(map[string]string{
		"x-request-id":         requestId,
		"x-request-session-id": requestSessionId,
		"x-request-source":     requestSource,
		"authorization":        "Bearer " + authToken,
	})
	return metadata.NewIncomingContext(ctx, md)
}

func TestFromGrpcContext(t *testing.T) {
	t.Run("test missing values", func(t *testing.T) {
		ctx := emptyIncomingContext()
		rc, err := FromGrpcContext(ctx)
		if err != nil {
			t.Fatal(err)
		}
		if rc.RequestId == "" {
			t.Fatal("request id is empty")
		}
		if string(rc.RequestSessionId) != string(rc.RequestId) {
			t.Fatal("request session id is not request id")
		}

		if rc.RequestSource != "unknown" {
			t.Fatal("request source is not unknown")
		}

		if rc.AuthToken.Expose() != "" {
			t.Fatal("auth token is not empty string")
		}

		rc2, err := FromGrpcContext(ctx)
		if err != nil {
			t.Fatal(err)
		}
		if rc2.RequestId == rc.RequestId {
			t.Fatal("request id is not unique")
		}
	})

	t.Run("extract values from context correctly", func(t *testing.T) {
		ctx := incomingContext()
		rc, err := FromGrpcContext(ctx)
		if err != nil {
			t.Fatal(err)
		}
		if rc.RequestId != requestId {
			t.Fatal("request id is not correct")
		}
		if rc.RequestSessionId != requestSessionId {
			t.Fatal("request session id is not correct")
		}
		if rc.RequestSource != requestSource {
			t.Fatal("request source is not correct")
		}
		if rc.AuthToken.Expose() != authToken {
			t.Fatal("auth token is not correct")
		}
	})

	t.Run("check auth token prefix", func(t *testing.T) {
		ctx := emptyIncomingContext()
		md := metadata.New(map[string]string{
			"authorization": "token",
		})
		ctx = metadata.NewIncomingContext(ctx, md)
		_, err := FromGrpcContext(ctx)
		if err == nil {
			t.Fatal("expected error for missing auth token prefix")
		}
	})
}

func TestToMetadata(t *testing.T) {
	rc := New(requestId, requestSessionId, requestSource, secretstring.New(authToken))
	md := rc.ToMetadata()
	if md.Get(requestIdKey)[0] != requestId {
		t.Fatal("request id is not correct")
	}
	if md.Get(requestSessionIdKey)[0] != requestSessionId {
		t.Fatal("request session id is not correct")
	}
	if md.Get(requestSourceKey)[0] != requestSource {
		t.Fatal("request source is not correct")
	}
	if md.Get(authKey)[0] != "Bearer "+authToken {
		t.Fatal("auth token is not correct")
	}
}

func TestAnnotateLogContext(t *testing.T) {
	// Create a buffer to capture log output
	var buf bytes.Buffer

	// Set up a logger that writes to our buffer
	log.Logger = zerolog.New(&buf)
	zerolog.DefaultContextLogger = &log.Logger

	// Create a RequestContext with known values
	rc := &RequestContext{
		RequestId:        RequestId("test-request-id"),
		RequestSessionId: RequestSessionId("test-session-id"),
		RequestSource:    "test",
		AuthToken:        secretstring.New("test-token"),
	}

	// Create a context and annotate it
	ctx := context.Background()
	annotatedCtx := rc.AnnotateLogContext(ctx)

	// Log a message using the annotated context
	log.Ctx(annotatedCtx).Info().Msg("Test log message")

	// Parse the log output
	var logEntry map[string]interface{}
	err := json.Unmarshal(buf.Bytes(), &logEntry)
	if err != nil {
		t.Fatalf("Failed to parse log output: %v", err)
	}

	// Check if the request_id and session_id are present in the log
	if requestID, ok := logEntry["request_id"]; !ok || requestID != "test-request-id" {
		t.Errorf("Expected request_id to be 'test-request-id', got %v", requestID)
	}

	if sessionID, ok := logEntry["session_id"]; !ok || sessionID != "test-session-id" {
		t.Errorf("Expected session_id to be 'test-session-id', got %v", sessionID)
	}

	// Check if the log message is present
	if message, ok := logEntry["message"]; !ok || message != "Test log message" {
		t.Errorf("Expected message to be 'Test log message', got %v", message)
	}
}

func TestAnnotateLogContextWithNilRequestContext(t *testing.T) {
	// Test that AnnotateLogContext doesn't panic with a nil RequestContext
	var rc *RequestContext
	ctx := context.Background()
	annotatedCtx := rc.AnnotateLogContext(ctx)

	// The annotated context should be the same as the original context
	if annotatedCtx != ctx {
		t.Errorf("Expected annotated context to be the same as original context when RequestContext is nil")
	}
}

"""Tests for services.lib.request_context.request_context."""

import pydantic
import pytest
import time

from dataclasses import dataclass
from unittest.mock import MagicMock, patch

from services.lib.request_context.request_context import (
    RequestContext,
    DeadlineExceededError,
    create_request_id,
    create_request_session_id,
)


# mock for a private type of grpc
@dataclass
class Metadata:
    """Mock for grpc.Metadata"""

    key: str
    value: str


def test_create_request_id():
    """Tests that create_request_id() returns a new request id"""
    request_id = create_request_id()
    assert request_id != create_request_id()


def test_create_request_session_id():
    """Tests that create_request_session_id() returns a new request session id"""
    request_session_id = create_request_session_id()
    assert request_session_id != create_request_session_id()


def test_request_context_from_context():
    """Tests that RequestContext.from_grpc_context() extracts the correct values from a context"""

    context = MagicMock()
    context.invocation_metadata.return_value = [
        Metadata("x-request-id", "fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        Metada<PERSON>("x-request-session-id", "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"),
        Metadata("authorization", "Bearer token"),
    ]
    context.time_remaining.return_value = None

    rc = RequestContext.from_grpc_context(context)
    assert rc.request_id == "fb9dda31-7bf6-4300-a28b-ebdfc916c351"
    assert rc.request_session_id == "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
    assert rc.auth_token is not None
    assert rc.auth_token.get_secret_value() == "token"
    assert rc.deadline is None


def test_request_context_from_context_missing_request_id():
    """Tests that RequestContext.from_grpc_context() extracts the correct values from a context when the request id is missing."""

    context = MagicMock()
    context.invocation_metadata.return_value = [
        Metadata("x-request-session-id", "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"),
        Metadata("authorization", "Bearer token"),
    ]
    context.time_remaining.return_value = None

    rc = RequestContext.from_grpc_context(context)
    assert rc.request_id is not None, "request id should be generated"
    assert rc.request_session_id == "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
    assert rc.auth_token is not None
    assert rc.auth_token.get_secret_value() == "token"
    assert rc.deadline is None


def test_request_context_from_context_missing_request_session_id():
    """Tests that RequestContext.from_grpc_context() extracts the correct values from a context with missing request session id"""

    context = MagicMock()
    context.invocation_metadata.return_value = [
        Metadata("x-request-id", "fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        Metadata("authorization", "Bearer token"),
    ]
    context.time_remaining.return_value = None

    rc = RequestContext.from_grpc_context(context)
    assert rc.request_id == "fb9dda31-7bf6-4300-a28b-ebdfc916c351"
    assert rc.request_session_id == "fb9dda31-7bf6-4300-a28b-ebdfc916c351"
    assert rc.auth_token is not None
    assert rc.auth_token.get_secret_value() == "token"
    assert rc.deadline is None


def test_request_context_from_context_missing_auth_token():
    """Tests that RequestContext.from_grpc_context() extracts the correct values from a context with missing auth token"""

    context = MagicMock()
    context.invocation_metadata.return_value = [
        Metadata("x-request-id", "fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        Metadata("x-request-session-id", "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"),
    ]
    context.time_remaining.return_value = None

    rc = RequestContext.from_grpc_context(context)
    assert rc.request_id == "fb9dda31-7bf6-4300-a28b-ebdfc916c351"
    assert rc.request_session_id == "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"
    assert rc.auth_token is None
    assert rc.deadline is None


def test_request_context_deadline_from_grpc_context():
    """Tests that RequestContext.from_grpc_context() correctly converts a request timeout to a deadline."""

    context = MagicMock()
    context.invocation_metadata.return_value = [
        Metadata("x-request-id", "fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        Metadata("x-request-session-id", "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"),
        Metadata("authorization", "Bearer token"),
    ]

    context.time_remaining.return_value = None
    rc = RequestContext.from_grpc_context(context)
    assert rc.time_remaining() is None
    assert rc.time_remaining_or_raise() is None

    TOTAL = 0.5
    TOLERANCE = 0.001
    mock_time = MagicMock()
    mock_time.return_value = 12.345
    context.time_remaining.return_value = TOTAL

    with patch("time.time", mock_time):
        rc = RequestContext.from_grpc_context(context)

        def expect(expected):
            for actual in [rc.time_remaining(), rc.time_remaining_or_raise()]:
                assert actual is not None
                assert abs(expected - actual) < TOLERANCE

        expect(TOTAL)

        mock_time.return_value += TOTAL / 3
        expect(2 * TOTAL / 3)
        # Once exhausted, time_remaining should stick to 0, and the raising
        # version should raise error
        mock_time.return_value += TOTAL
        assert rc.time_remaining() == 0
        with pytest.raises(DeadlineExceededError):
            rc.time_remaining_or_raise()


def test_to_metadata():
    """Tests that RequestContext.to_metadata() returns the correct metadata"""
    rc = RequestContext(
        "fb9dda31-7bf6-4300-a28b-ebdfc916c351",
        "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a",
        "client",
        pydantic.SecretStr("token"),
    )
    assert rc.to_metadata() == [
        ("x-request-id", "fb9dda31-7bf6-4300-a28b-ebdfc916c351"),
        ("x-request-session-id", "3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a"),
        ("x-request-source", "client"),
        ("authorization", "Bearer token"),
    ]


def test_request_context_str():
    """Tests that RequestContext's token is not printed."""

    rc = RequestContext(
        "fb9dda31-7bf6-4300-a28b-ebdfc916c351",
        "fb9dda31-7bf6-4300-a28b-ebdfc916c351",
        "client",
        pydantic.SecretStr("secrettoken"),
    )
    assert "secrettoken" not in str(rc)
    assert "fb9dda31-7bf6-4300-a28b-ebdfc916c351" in str(rc)

    assert "secrettoken" not in repr(rc)
    assert "fb9dda31-7bf6-4300-a28b-ebdfc916c351" in repr(rc)

# Request Context

Classes for different languages to make Augment backend GRPC requests with the standard headers betweeen `//services` services.

## Headers

- `x-request-id` - A unique identifier for the request. Each complete piece of work should have a unique request id. The request id is usually generated by the client which initiates the work (e.g. the extension or a background task) and in UUID format.
- `x-request-session-id` - A unique identifier for the session. Each stream of work, e.g. lifetime of a pod or a client should have a unique session id. The session id is usually generated by the client which initiates the work (e.g. the extension or a background task) and in UUID format.
- `authorization` - A service token.

Most grpc services should forward these headers to any other services they call.
If a `x-request` header is missing, a new id will be generated.
If the proper `authorization` header is missing, the request can be rejected.

## Security Impact

The value of the the authorization token should not be logged and has to be treated as a secret.
The value of the request id can be logged and is not considered a secret.

While the clients should generate request id and session ids securely, the system security and availability is not depend on that property.

We do not use opentelemetry context to track request context, incl. authentication information to reduce the risk of accidential leakage of the information. The request context is expectet to be passed explicitly.

## Note

- Devtools GRPC services, e.g. slack bot or bazel runner should not use this library.
- Opentelemetry context information are not tracked by this class.

## Links

- [https://en.wikipedia.org/wiki/Universally_unique_identifier](UUID)
- [https://www.notion.so/Service-to-Service-auth-in-Shard-Architecture-f626a6a379044ebc9bc74597a94883f1?pvs=4](1000/1000 service to service auth design doc)

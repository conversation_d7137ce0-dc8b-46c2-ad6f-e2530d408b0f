"""A class that holds all grpc context information for a services request."""

import time
from typing import Tuple
import uuid
from contextlib import contextmanager
from dataclasses import dataclass

import grpc
import pydantic
import structlog


def get_request_session_id_from_grpc_context(
    context: grpc.ServicerContext,
) -> str | None:
    """Extract a request session id from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The request session id or None if not found
    """
    metadata = context.invocation_metadata()
    request_session_id = [m.value for m in metadata if m.key == "x-request-session-id"]
    if request_session_id:
        return request_session_id[0]
    else:
        return None


def get_request_session_id_from_metadata(metadata: list[Tuple[str, str]]) -> str | None:
    """Extract a request session id from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The request session id or None if not found
    """
    request_session_id = [m[1] for m in metadata if m[0] == "x-request-session-id"]
    if request_session_id:
        return request_session_id[0]
    else:
        return None


def get_request_id_from_grpc_context(context: grpc.ServicerContext) -> str | None:
    """Extract a request id from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The request id or None if not found
    """
    metadata = context.invocation_metadata()
    request_id = [m.value for m in metadata if m.key == "x-request-id"]
    if request_id:
        return request_id[0]
    else:
        return None


def get_request_id_from_metadata(metadata: list[Tuple[str, str]]) -> str | None:
    """Extract a request id from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The request id or None if not found
    """
    request_id = [m[1] for m in metadata if m[0] == "x-request-id"]
    if request_id:
        return request_id[0]
    else:
        return None


def get_request_source_from_grpc_context(context: grpc.ServicerContext) -> str | None:
    """Extract a request source from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The request source or None if not found
    """
    metadata = context.invocation_metadata()
    request_source = [m.value for m in metadata if m.key == "x-request-source"]
    if request_source:
        return request_source[0]
    else:
        return None


def get_request_source_from_metadata(metadata: list[Tuple[str, str]]) -> str | None:
    """Extract a request source from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The request source or None if not found
    """
    request_source = [m[1] for m in metadata if m[0] == "x-request-source"]
    if request_source:
        return request_source[0]
    else:
        return None


_BEARER_PREFIX = "Bearer "


def get_auth_token_from_grpc_context(
    context: grpc.ServicerContext,
) -> pydantic.SecretStr | None:
    """Extract an auth token from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The auth token or None if not found

    """
    metadata = context.invocation_metadata()
    auth_token = [m.value for m in metadata if m.key == "authorization"]
    if auth_token:
        if auth_token[0].startswith(_BEARER_PREFIX):
            return pydantic.SecretStr(auth_token[0][len(_BEARER_PREFIX) :])
    else:
        return None


def get_auth_token_from_metadata(
    metadata: list[Tuple[str, str]],
) -> pydantic.SecretStr | None:
    """Extract an auth token from grpc metadata.

    Args:
        context: The grpc context

    Returns:
        The auth token or None if not found

    """
    auth_token = [m[1] for m in metadata if m[0] == "authorization"]
    if auth_token:
        if auth_token[0].startswith(_BEARER_PREFIX):
            return pydantic.SecretStr(auth_token[0][len(_BEARER_PREFIX) :])
    else:
        return None


def create_request_id() -> str:
    """Creates a new request id.

    Args:
        None

    Returns:
        A new request id
    """
    return str(uuid.uuid4())


def create_request_session_id() -> str:
    """Creates a new request session id.

    Args:
        None

    Returns:
        A new request session id
    """
    return str(uuid.uuid4())


@dataclass(frozen=True)
class RequestContext:
    """A class that holds all grpc context information for a services request"""

    request_id: str
    """The request id"""

    request_session_id: str
    """The request session id"""

    request_source: str

    auth_token: pydantic.SecretStr | None = None
    """Service token"""

    deadline: float | None = None
    """Deadline for the request as seconds since the epoch"""

    def __post_init__(self):
        """Post init"""
        if self.auth_token:
            assert isinstance(self.auth_token, pydantic.SecretStr)

    @classmethod
    def from_grpc_context(cls, context: grpc.ServicerContext) -> "RequestContext":
        """Extracts request context from a grpc context

        NOTE: If the request id is missing, a new id will be generated. This
        means that this operation is neither invertible nor is it pure. i.e.:
        two calls to from_grpc_context with the same input will not necessarily
        yield equivalent outputs.

        Args:
            context: The grpc context

        Returns:
            A RequestContext object.
            It will have a new request id if the request id is missing.
            It will have a new session id if the session id is missing.
        """
        request_id = get_request_id_from_grpc_context(context) or create_request_id()
        session_id = get_request_session_id_from_grpc_context(context) or request_id
        request_source = get_request_source_from_grpc_context(context) or "unknown"
        auth_token = get_auth_token_from_grpc_context(context)
        deadline = context.time_remaining()
        if deadline is not None:
            deadline += time.time()
        return cls(
            request_id=request_id,
            request_session_id=session_id,
            request_source=request_source,
            auth_token=auth_token,
            deadline=deadline,
        )

    @classmethod
    def from_metadata(cls, metadata: list[Tuple[str, str]]) -> "RequestContext":
        """Extracts request context from a grpc context

        Args:
            context: The grpc context

        Returns:
            A RequestContext object.
            It will have a new request id if the request id is missing.
            It will have a new session id if the session id is missing.
        """
        request_id = get_request_id_from_metadata(metadata) or create_request_id()
        session_id = get_request_session_id_from_metadata(metadata) or request_id
        request_source = get_request_source_from_metadata(metadata) or "unknown"
        auth_token = get_auth_token_from_metadata(metadata)
        return cls(
            request_id=request_id,
            request_session_id=session_id,
            request_source=request_source,
            auth_token=auth_token,
        )

    @classmethod
    def create(
        cls,
        request_source="unknown",
        auth_token: pydantic.SecretStr | None = None,
        request_id: str | None = None,
        request_session_id: str | None = None,
        timeout: float | None = None,
    ):
        """Creates a request context with a new uuid based request id and session id."""
        return cls(
            request_id=request_id or create_request_id(),
            request_session_id=request_session_id or create_request_session_id(),
            request_source=request_source,
            auth_token=auth_token,
            deadline=None if timeout is None else time.time() + timeout,
        )

    def with_new_request_id(self) -> "RequestContext":
        """Returns a new request context with a new request id."""
        return RequestContext(
            request_id=create_request_id(),
            request_session_id=self.request_session_id,
            request_source=self.request_source,
            auth_token=self.auth_token,
            deadline=self.deadline,
        )

    def to_metadata(self) -> list[tuple[str, str]]:
        """Returns a list of grpc metadata tuples."""
        metadata = [
            ("x-request-id", self.request_id),
            ("x-request-session-id", self.request_session_id),
            ("x-request-source", self.request_source),
        ]
        if self.auth_token:
            metadata.append(
                ("authorization", f"Bearer {self.auth_token.get_secret_value()}")
            )
        return metadata

    def time_remaining(self) -> float | None:
        """Returns the time remaining for the request in seconds or None if there is no deadline."""
        if self.deadline is None:
            return None
        return max(self.deadline - time.time(), 0)

    def time_remaining_or_raise(self) -> float | None:
        """Returns the time remaining for the request or raises RpcError.

        Raising an exception on access is useful because python's gRPC client
        library will send a request even if the caller passes timeout=0.

        Returns:
            The time remaining in seconds or None if there is no deadline.
        Raises:
            DeadlineExceededError: If there is no time remaining.
        """
        time_remaining = self.time_remaining()
        if time_remaining == 0:
            raise DeadlineExceededError("no time remaining in context")
        return time_remaining

    def bind_context_logging(self):
        """Binds the request context to the log context."""
        structlog.contextvars.bind_contextvars(
            request_id=self.request_id,
            session_id=self.request_session_id,
        )

    @classmethod
    def create_for_session(
        cls,
        session_id: str,
        request_source="unknown",
        auth_token: pydantic.SecretStr | None = None,
    ) -> "RequestContext":
        """Returns a new request context with a new request id for a given session id."""
        return RequestContext(
            request_id=create_request_id(),
            request_session_id=session_id,
            request_source=request_source,
            auth_token=auth_token,
        )

    @contextmanager
    def with_context_logging(self):
        """Binds the request context to the log context and yields."""
        r = structlog.contextvars.bind_contextvars(
            request_id=self.request_id,
            session_id=self.request_session_id,
        )
        yield
        structlog.contextvars.unbind_contextvars(*r.keys())


def clamp_timeout(timeout: float | None, max_timeout: float) -> float:
    """Clamps a timeout to a maximum value.

    Useful for treating the result of RequestContext.time_remaining() to limit
    time given to sub-requests while still honoring the overall request
    deadline.

    Also used to resolve the None value to a float for interfaces where timeout
    is not optional.
    """
    if timeout is None:
        return max_timeout
    return min(timeout, max_timeout)


class DeadlineExceededError(grpc.RpcError):
    """Exception raised when the request deadline is exceeded.

    We can't just raise RpcError in this case because RpcError doesn't implement code() or
    details(), both of which are expected to be implemented by instances of RpcError (because
    all RpcErrors raised by the grpc library also implement grpc.Call).
    """

    def __init__(self, msg: str = ""):
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return grpc.StatusCode.DEADLINE_EXCEEDED

    def details(self) -> str:
        return self.msg

    def __str__(self) -> str:
        return f"DeadlineExceededError({self.msg})"

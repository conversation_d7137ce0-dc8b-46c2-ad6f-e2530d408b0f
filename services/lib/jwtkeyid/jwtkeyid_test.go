package jwtkeyid

import (
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/base64"
	"math/big"
	"testing"
	"time"
)

func testCert(t *testing.T) *x509.Certificate {
	t.Helper()

	// Create a new ECDSA private key
	privateKey, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		t.Fatalf("Failed to generate private key: %v", err)
	}

	// Create a template for the certificate
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization: []string{"Test Org"},
		},
		NotBefore:   time.Now(),
		NotAfter:    time.Now().Add(time.Hour * 24 * 180),
		KeyUsage:    x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage: []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
	}

	// Create the certificate
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		t.Fatalf("Failed to create certificate: %v", err)
	}

	// Parse the certificate
	cert, err := x509.ParseCertificate(certDER)
	if err != nil {
		t.Fatalf("Failed to parse certificate: %v", err)
	}
	t.Logf("signature: %x", cert.Signature)
	return cert
}

func TestGetKeyIDFromCert(t *testing.T) {
	cert := testCert(t)
	keyID := GetKeyIDFromCert(cert)

	// Check that the key ID is the correct length
	if len(keyID) != keyIDLength {
		t.Errorf("Key ID is not the correct length: found %d (%q), expected %d", len(keyID), keyID, keyIDLength)
	}

	// Check that the key ID is a valid base64 string
	_, err := base64.StdEncoding.DecodeString(keyID)
	if err != nil {
		t.Errorf("Key ID is not a valid base64 string: %q, %v", keyID, err)
	}
}

package main

import (
	"context"
	"net"
	"os"
	"testing"

	secretmanager "cloud.google.com/go/secretmanager/apiv1"
	secretmanagerpb "cloud.google.com/go/secretmanager/apiv1/secretmanagerpb"
	"google.golang.org/api/option"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

// mockSecretManagerServer implements the Secret Manager API for testing
type mockSecretManagerServer struct {
	secretmanagerpb.UnimplementedSecretManagerServiceServer
	// Map of secret name to secret data
	secrets map[string][]byte
}

func (m *mockSecretManagerServer) AccessSecretVersion(
	ctx context.Context,
	req *secretmanagerpb.AccessSecretVersionRequest,
) (*secretmanagerpb.AccessSecretVersionResponse, error) {
	secretData, exists := m.secrets[req.Name]
	if !exists {
		return nil, status.Errorf(codes.NotFound, "secret %s not found", req.Name)
	}

	return &secretmanagerpb.AccessSecretVersionResponse{
		Name: req.Name,
		Payload: &secretmanagerpb.SecretPayload{
			Data: secretData,
		},
	}, nil
}

func TestLoadConfig(t *testing.T) {
	// Create a temporary config file
	tempFile, err := os.CreateTemp("", "test-config-*.json")
	if err != nil {
		t.Fatalf("Failed to create temporary file: %v", err)
	}
	defer os.Remove(tempFile.Name())

	// Write test config to the file with all expected fields
	// Using the correct JSON field names that match the struct tags
	testConfig := `{
		"LogSourceProjectID": "test-log-project",
		"LogSourceBucketLocation": "global",
		"LogSourceBucketID": "test-log-bucket",
		"LogSourceViewID": "_AllLogs",
		"TenantName": "test-tenant",
		"GCSDestinationBucketName": "test-destination-bucket",
		"GCSSecretName": "test-secret"
	}`
	if _, err := tempFile.Write([]byte(testConfig)); err != nil {
		t.Fatalf("Failed to write to temporary file: %v", err)
	}
	if err := tempFile.Close(); err != nil {
		t.Fatalf("Failed to close temporary file: %v", err)
	}

	// Load the config
	config, err := loadConfig(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify the config values
	testCases := []struct {
		fieldName string
		got       string
		want      string
	}{
		{"LogSourceProjectID", config.LogSourceProjectID, "test-log-project"},
		{"LogSourceBucketLocation", config.LogSourceBucketLocation, "global"},
		{"LogSourceBucketID", config.LogSourceBucketID, "test-log-bucket"},
		{"LogSourceViewID", config.LogSourceViewID, "_AllLogs"},
		{"TenantName", config.TenantName, "test-tenant"},
		{"GCSDestinationBucketName", config.GCSDestinationBucketName, "test-destination-bucket"},
		{"GCSSecretName", config.GCSSecretName, "test-secret"},
	}

	for _, tc := range testCases {
		if tc.got != tc.want {
			t.Errorf("Expected %s to be '%s', got '%s'", tc.fieldName, tc.want, tc.got)
		}
	}

	// Test with missing file
	_, err = loadConfig("non-existent-file.json")
	if err == nil {
		t.Error("Expected error when loading non-existent file, got nil")
	}

	// Test with invalid JSON
	invalidFile, err := os.CreateTemp("", "invalid-config-*.json")
	if err != nil {
		t.Fatalf("Failed to create temporary file: %v", err)
	}
	defer os.Remove(invalidFile.Name())

	if _, err := invalidFile.Write([]byte("this is not valid json")); err != nil {
		t.Fatalf("Failed to write to temporary file: %v", err)
	}
	if err := invalidFile.Close(); err != nil {
		t.Fatalf("Failed to close temporary file: %v", err)
	}

	_, err = loadConfig(invalidFile.Name())
	if err == nil {
		t.Error("Expected error when loading invalid JSON, got nil")
	}
}

func TestGetSecret(t *testing.T) {
	ctx := context.Background()
	// Set up a mock gRPC server
	mockServer := &mockSecretManagerServer{
		secrets: map[string][]byte{
			"projects/test-project/secrets/test-secret/versions/latest": []byte("test-secret-value"),
		},
	}
	l, err := net.Listen("tcp", "localhost:0")
	if err != nil {
		t.Fatal(err)
	}

	// Create a local test server
	server := grpc.NewServer()
	secretmanagerpb.RegisterSecretManagerServiceServer(server, mockServer)
	fakeServerAddr := l.Addr().String()
	go func() {
		if err := server.Serve(l); err != nil {
			panic(err)
		}
	}()
	defer server.Stop()

	// Create a client that uses the mock connection
	client, err := secretmanager.NewClient(ctx,
		option.WithEndpoint(fakeServerAddr),
		option.WithoutAuthentication(),
		option.WithGRPCDialOption(grpc.WithTransportCredentials(insecure.NewCredentials())))
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	// Mock the secretmanager.NewClient function to return our test client
	originalNewClient := secretmanagerNewClient
	secretmanagerNewClient = func(ctx context.Context, opts ...option.ClientOption) (*secretmanager.Client, error) {
		return client, nil
	}
	defer func() { secretmanagerNewClient = originalNewClient }()

	// Test successful secret retrieval
	t.Run("Success", func(t *testing.T) {
		secretData, err := getSecret(ctx, client, "test-project", "test-secret")
		if err != nil {
			t.Fatalf("getSecret failed: %v", err)
		}

		if string(secretData) != "test-secret-value" {
			t.Errorf("Expected secret value 'test-secret-value', got '%s'", string(secretData))
		}
	})

	// Test non-existent secret
	t.Run("NonExistentSecret", func(t *testing.T) {
		_, err := getSecret(ctx, client, "test-project", "non-existent-secret")
		if err == nil {
			t.Error("Expected error for non-existent secret, got nil")
		}

		if status.Code(err) != codes.NotFound {
			t.Errorf("Expected NotFound error, got %v", err)
		}
	})
}

// Variable to allow mocking in tests
var secretmanagerNewClient = secretmanager.NewClient

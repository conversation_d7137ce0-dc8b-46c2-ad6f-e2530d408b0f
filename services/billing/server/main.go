package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/integrations/orb"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/augmentcode/augment/services/lib/pubsub"
	"github.com/augmentcode/augment/services/request_insight/lib/subscriber"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
)

var ServiceName = "billing"

// Config holds the configuration for the billing service
type Config struct {
	SubscribeClientConfig       pubsub.SubscribeClientConfig
	HealthFile                  string
	PromPort                    int
	FeatureFlagsSDKKeyPath      string
	DynamicFeatureFlagsEndpoint string
	OrbAPIKeyPath               string
	AuthEndpoint                string
	TokenExchangeEndpoint       string
	CentralClientMtlsConfig     *tlsconfig.ClientConfig
}

// loadConfig loads the configuration from a file
func loadConfig(configFile string) (*Config, error) {
	if configFile == "" {
		return nil, fmt.Errorf("config file path is required")
	}

	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("error reading config file: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("error parsing config file: %w", err)
	}

	return &config, nil
}

// declareHealthy creates a health file to indicate the service is healthy
func declareHealthy(healthFile string) {
	if healthFile == "" {
		return
	}

	file, err := os.Create(healthFile)
	if err != nil {
		log.Error().Err(err).Msg("Error creating health file")
		return
	}
	defer file.Close()

	if _, err := file.WriteString("OK"); err != nil {
		log.Error().Err(err).Msg("Error writing to health file")
	}
}

func main() {
	logging.SetupServerLogging()

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		log.Fatal().Msg("POD_NAMESPACE environment variable must be set")
	}

	// Parse flags
	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()

	// Load config
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error loading config")
	}

	// Initialize feature flags
	var featureFlagHandle featureflags.FeatureFlagHandle
	if config.FeatureFlagsSDKKeyPath != "" {
		var err error
		featureFlagHandle, err = featureflags.NewFeatureFlagHandleFromFile(
			config.FeatureFlagsSDKKeyPath,
			config.DynamicFeatureFlagsEndpoint,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating feature flag handle")
		}
	} else {
		log.Info().Msg("Feature flags disabled: using local implementation")
		featureFlagHandle = featureflags.NewLocalFeatureFlagHandler()
	}

	// Start metrics server
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	ctx := context.Background()

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtlsConfig)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchangeclient.New(
		config.TokenExchangeEndpoint, namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	authClient, err := authclient.New(config.AuthEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating auth client")
	}
	defer authClient.Close()

	// Read Orb API key
	orbAPIKey, err := os.ReadFile(config.OrbAPIKeyPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading Orb API key")
	}

	// Create Orb client
	orbClient := orb.NewOrbClient(string(orbAPIKey), featureFlagHandle)

	// Create a message processor with feature flags and Orb client
	processor := NewMessageProcessor(ctx, featureFlagHandle, orbClient, authClient, tokenExchangeClient)
	defer processor.Close()

	// Create subscriber
	subscriber, err := subscriber.New(ctx, &config.SubscribeClientConfig, processor.ProcessMessage)
	if err != nil {
		log.Fatal().Err(err).Msg("Error initializing RequestInsightSubscriber")
	}
	defer subscriber.Close()

	// Wait 10 seconds before declaring healthy
	go func() {
		time.Sleep(10 * time.Second)
		declareHealthy(config.HealthFile)
	}()

	// Run the subscriber
	log.Info().Msg("Starting billing service")
	subscriber.Run(ctx)
}

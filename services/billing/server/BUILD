load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "server_lib",
    srcs = [
        "main.go",
        "process_billing_message.go",
    ],
    importpath = "github.com/augmentcode/augment/services/billing/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/integrations/orb:orb_lib",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/lib:request_insight_subscriber_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_google_uuid//:uuid",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_api//option",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//proto",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "server_test",
    srcs = ["process_billing_message_test.go"],
    embed = [":server_lib"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/request_insight:request_insight_go_proto",
        "@com_github_google_uuid//:uuid",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/testutil",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/lib:exporter_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
        "//deploy/tenants:namespaces",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

local excludeStagingFilter = 'namespace!~".*staging.*"';
local subscriptionNameMatch = '.*billing-sub';
local subscriptionNameNoMatch = '.*staging.*';

function(cloud)
  // Orb's events-ingestion API rate limit is 100 ingestion requests per second and 10000 events per minute
  // https://docs.withorb.com/api-reference/rate-limits#rate-limits
  // If we are above 70% of the limits we should alert and reach out to <PERSON><PERSON> to raise up the limit.
  local eventsIngestionRequestPerSecondTooHigh = {
    displayName: 'Events ingestion requests per second is above 70 for the last 5 minutes',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum(rate(billing_ingest_events_success{%(filter)s}[1m])) + sum(rate(billing_ingest_events_failure{%(filter)s}[1m])) > 70
      ||| % { filter: excludeStagingFilter },
    },
  };
  local eventsIngestedPerMinuteTooHigh = {
    displayName: 'Events ingested per minute is above 7000 for the last 5 minutes',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum(increase(billing_ingested_events_count{%(filter)s}[1m])) > 7000
      ||| % { filter: excludeStagingFilter },
    },
  };

  // Events ingestion request failure rate too high
  local eventsIngestionFailureRateTooHigh = {
    displayName: 'Events ingestion failure rate is above 5%% for the last 5 minutes',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        sum(rate(billing_ingest_events_failure{%(filter)s}[1m])) / (
          sum(rate(billing_ingest_events_success{%(filter)s}[1m])) +
          sum(rate(billing_ingest_events_failure{%(filter)s}[1m]))
        ) > 0.05
      ||| % { filter: excludeStagingFilter },
    },
  };

  // RI pubsub backlog too high
  local pubsubBacklogTooHigh = {
    displayName: 'RI pub/sub backlog of Billing service is above 100k for the last 10 minutes',
    conditionPrometheusQueryLanguage: {
      duration: '600s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        sum by (subscription_id) (pubsub_googleapis_com:subscription_num_undelivered_messages{
          monitored_resource="pubsub_subscription",
          subscription_id=~"%s",
          subscription_id!~"%s"
        }) > 100000
      ||| % [subscriptionNameMatch, subscriptionNameNoMatch],
    },
  };
  // RI pubsub oldest unacked message too old
  local pubsubUnackedMessagesTooHigh = {
    displayName: 'RI pub/sub oldest unacked message of Billing service is above 30 minutes for the last 10 minutes',
    conditionPrometheusQueryLanguage: {
      duration: '600s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        max by (subscription_id)(pubsub_googleapis_com:subscription_oldest_unacked_message_age{
          monitored_resource="pubsub_subscription",
          subscription_id=~"%s",
          subscription_id!~"%s"
        }) > 1800
      ||| % [subscriptionNameMatch, subscriptionNameNoMatch],
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      eventsIngestionRequestPerSecondTooHigh,
      'events-ingestion-request-per-second-too-high',
      'Events ingestion requests per second is above 70 for the last 5 minutes',
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      eventsIngestedPerMinuteTooHigh,
      'events-ingested-per-minute-too-high',
      'Events ingested per minute is above 7000 for the last 5 minutes',
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      eventsIngestionFailureRateTooHigh,
      'events-ingestion-failure-rate-too-high',
      'Events ingestion failure rate is above 5%% for the last 5 minutes',
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      pubsubBacklogTooHigh,
      'billing-ri-pubsub-backlog-too-high',
      'RI pub/sub backlog of Billing service is above 100k for the last 10 minutes',
      team='insights',
    ),
    monitoringLib.alertPolicy(
      cloud,
      pubsubUnackedMessagesTooHigh,
      'billing-ri-pubsub-unacked-messages-too-high',
      'RI pub/sub old unacked message of Billing service is above 30 minutes for the last 10 minutes',
      team='insights',
    ),
  ]

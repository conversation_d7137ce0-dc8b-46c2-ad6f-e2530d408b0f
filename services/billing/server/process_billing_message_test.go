package main

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/prometheus/client_golang/prometheus/testutil"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	secretstring "github.com/augmentcode/augment/base/go/secretstring"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	authservice "github.com/augmentcode/augment/services/auth/central/server/auth"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	orb "github.com/augmentcode/augment/services/integrations/orb"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
)

type MockAuthClient struct {
	authclient.AuthClient
	mock.Mock
}

func (m *MockAuthClient) GetUserBillingInfo(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantId string) (*authservice.GetUserBillingInfoResponse, error) {
	args := m.Called(ctx, requestContext, userId, tenantId)
	return args.Get(0).(*authservice.GetUserBillingInfoResponse), args.Error(1)
}

type MockTokenExchangeClient struct {
	tokenexchangeclient.TokenExchangeClient
	mock.Mock
}

func (m *MockTokenExchangeClient) GetSignedTokenForService(ctx context.Context, tenantID string, scopes []tokenexchangepb.Scope) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, scopes)
	return secretstring.New(args.String(0)), args.Error(1)
}

func TestProcessMessage(t *testing.T) {
	// Setup common test data
	testTime := time.Now().UTC()
	testUUID := uuid.New().String()
	testRequestID := "test-request-id"
	testTenantID := "test-tenant-id"

	// Create a timestamp proto
	testTimestamp := timestamppb.New(testTime)

	tests := []struct {
		name        string
		event       *pb.RequestEvent
		requestID   string
		expectEvent bool
		expected    *orb.OrbEvent
	}{
		{
			name: "feature flag disabled",
			event: &pb.RequestEvent{
				Time: testTimestamp,
				Event: &pb.RequestEvent_ChatUserMessage{
					ChatUserMessage: &pb.ChatUserMessage{
						ChatMode: pb.ChatUserMessage_CHAT_MODE_AGENT,
						OpaqueUserId: &authpb.UserId{
							UserId:     testUUID,
							UserIdType: authpb.UserId_AUGMENT,
						},
					},
				},
			},
			requestID:   testRequestID,
			expectEvent: false,
			expected:    nil,
		},
		{
			name: "valid agent chat request",
			event: &pb.RequestEvent{
				Time: testTimestamp,
				Event: &pb.RequestEvent_ChatUserMessage{
					ChatUserMessage: &pb.ChatUserMessage{
						ChatMode: pb.ChatUserMessage_CHAT_MODE_AGENT,
						OpaqueUserId: &authpb.UserId{
							UserId:     testUUID,
							UserIdType: authpb.UserId_AUGMENT,
						},
					},
				},
			},
			requestID:   testRequestID,
			expectEvent: true,
			expected: func() *orb.OrbEvent {
				customerID := "orb_" + testUUID
				return &orb.OrbEvent{
					IdempotencyKey: testRequestID,
					EventName:      "user_message",
					CustomerOrbID:  &customerID,
					Timestamp:      testTime,
					Properties: map[string]interface{}{
						"chat_mode":  "CHAT_MODE_AGENT",
						"user_id":    testUUID,
						"user_email": "<EMAIL>",
						"tenant_id":  testTenantID,
					},
				}
			}(),
		},
		{
			name: "invalid user ID (not a UUID)",
			event: &pb.RequestEvent{
				Time: testTimestamp,
				Event: &pb.RequestEvent_ChatUserMessage{
					ChatUserMessage: &pb.ChatUserMessage{
						ChatMode: pb.ChatUserMessage_CHAT_MODE_AGENT,
						OpaqueUserId: &authpb.UserId{
							UserId:     "not-a-uuid",
							UserIdType: authpb.UserId_AUGMENT,
						},
					},
				},
			},
			requestID:   testRequestID,
			expectEvent: false,
			expected:    nil,
		},
		{
			name: "non-agent chat mode",
			event: &pb.RequestEvent{
				Time: testTimestamp,
				Event: &pb.RequestEvent_ChatUserMessage{
					ChatUserMessage: &pb.ChatUserMessage{
						ChatMode: pb.ChatUserMessage_CHAT_MODE_CHAT,
						OpaqueUserId: &authpb.UserId{
							UserId:     testUUID,
							UserIdType: authpb.UserId_AUGMENT,
						},
					},
				},
			},
			requestID:   testRequestID,
			expectEvent: false,
			expected:    nil,
		},
		{
			name: "missing opaque user ID",
			event: &pb.RequestEvent{
				Time: testTimestamp,
				Event: &pb.RequestEvent_ChatUserMessage{
					ChatUserMessage: &pb.ChatUserMessage{
						ChatMode: pb.ChatUserMessage_CHAT_MODE_AGENT,
					},
				},
			},
			requestID:   testRequestID,
			expectEvent: false,
			expected:    nil,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create a mock feature flag handle
			mockFeatureFlags := featureflags.NewLocalFeatureFlagHandler()

			// Set the feature flag based on the test case
			if tc.name == "feature flag disabled" {
				mockFeatureFlags.Set("enable_billing_event_ingestion", false)
			} else {
				mockFeatureFlags.Set("enable_billing_event_ingestion", true)
			}

			// Create a mock Orb client
			mockOrbClient := orb.NewMockOrbClient()

			// Create mock auth client
			mockAuthClient := new(MockAuthClient)

			// Create mock token exchange client
			mockTokenExchangeClient := new(MockTokenExchangeClient)

			// Set up mocks for all test cases with a valid user ID
			if tc.event.GetChatUserMessage() != nil && tc.event.GetChatUserMessage().GetOpaqueUserId() != nil {
				userId := tc.event.GetChatUserMessage().GetOpaqueUserId().GetUserId()

				// Set up token exchange client mock
				mockTokenExchangeClient.On("GetSignedTokenForService", mock.Anything, testTenantID, mock.Anything).Return("test-token", nil)

				// Check if the user ID is a valid UUID
				_, err := uuid.Parse(userId)
				if err == nil {
					// Set up auth client mock for valid UUIDs
					if tc.expectEvent {
						// For test cases that expect events, return a valid Orb customer ID
						mockAuthClient.On("GetUserBillingInfo", mock.Anything, mock.Anything, userId, testTenantID).Return(&authservice.GetUserBillingInfoResponse{
							OrbCustomerId:   "orb_" + userId,
							Email:           "<EMAIL>",
							IsSelfServeTeam: false,
						}, nil)
					} else {
						// For test cases that don't expect events, return an empty Orb customer ID
						mockAuthClient.On("GetUserBillingInfo", mock.Anything, mock.Anything, userId, testTenantID).Return(&authservice.GetUserBillingInfoResponse{
							OrbCustomerId:   "", // Empty Orb customer ID will cause the event to be skipped
							Email:           "<EMAIL>",
							IsSelfServeTeam: false,
						}, nil)
					}
				}
			}

			// Set up Orb client mock for test cases that expect events
			if tc.expectEvent {
				mockOrbClient.On("IngestEvents", mock.Anything, mock.MatchedBy(func(events []*orb.OrbEvent) bool {
					return len(events) == 1 &&
						*events[0].CustomerOrbID == "orb_"+tc.event.GetChatUserMessage().GetOpaqueUserId().GetUserId() &&
						events[0].IdempotencyKey == tc.requestID &&
						events[0].EventName == "user_message" &&
						events[0].Properties["chat_mode"] == tc.event.GetChatUserMessage().GetChatMode().String() &&
						events[0].Properties["user_id"] == tc.event.GetChatUserMessage().GetOpaqueUserId().GetUserId() &&
						events[0].Properties["user_email"] == "<EMAIL>" &&
						events[0].Properties["tenant_id"] == testTenantID
				})).Return(nil)
			}

			// Create a processor with the mock feature flags and clients
			processor := &MessageProcessor{
				featureFlags: mockFeatureFlags,
				orbClient:    mockOrbClient,
				authClient:   mockAuthClient,
				tokenClient:  mockTokenExchangeClient,
			}

			// Create a test message with the event
			message := &pb.RequestInsightMessage{
				Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
					UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
						TenantInfo: &pb.TenantInfo{
							TenantId: testTenantID,
						},
						RequestId: tc.requestID,
						Events:    []*pb.RequestEvent{tc.event},
					},
				},
			}

			// Process the message
			err := processor.ProcessMessage(context.Background(), message)
			assert.NoError(t, err, "ProcessMessage should not return an error")

			// Verify expectations
			if tc.expectEvent {
				mockAuthClient.AssertExpectations(t)
				mockTokenExchangeClient.AssertExpectations(t)
				mockOrbClient.AssertExpectations(t)
				// Verify that the metrics were incremented
				assert.Greater(t, testutil.ToFloat64(ingestEventsSuccessCounter), float64(0))
				assert.Greater(t, testutil.ToFloat64(ingestedEventsCounter), float64(0))
			} else {
				mockOrbClient.AssertNotCalled(t, "IngestEvents", mock.Anything, mock.Anything)
				// For negative test cases, we don't expect the counters to increase
			}
		})
	}
}

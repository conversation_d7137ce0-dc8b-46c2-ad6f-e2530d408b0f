local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
local exporterLib = import 'services/request_insight/lib/exporter_lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local projectId = cloudInfo[cloud].projectId;
  local appName = 'billing';

  local clientCert = certLib.createClientCert(name='%s-client-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName);
  local centralClientCert = certLib.createCentralClientCert(name='%s-central-client-certificate' % appName,
                                                            namespace=namespace,
                                                            appName=appName,
                                                            env=env,
                                                            dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace));
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  // Create a subscriber for the request insight topic
  local subscriber = exporterLib.subscriber(cloud, env, namespace, appName, serviceAccount);


  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local orbApiKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='orb-api-key',
    version={
      PROD: '2',
      STAGING: '3',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  // Configuration for the billing service
  local config = {
    subscribeClientConfig: {
      projectId: projectId,
      subscriptionId: subscriber.subscriptionName,
      deadLetterSubscriptionId: subscriber.deadLetterSubscriptionName,
      // Limit the number of concurrent handler goroutines in the subscriber.
      maxConcurrentReceivers: 20,
    },
    healthFile: '/tmp/health',
    promPort: 9090,
    grpcPort: 50051,
    featureFlagsSDKKeyPath: dynamicFeatureFlags.secretsFilePath,
    dynamicFeatureFlagsEndpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    clientMtlsConfig: if mtls then clientCert.config else null,
    centralClientMtlsConfig: if mtls then centralClientCert.config else null,
    serverMtlsConfig: if mtls then serverCert.config else null,
    orbAPIKeyPath: orbApiKey.filePath,
    authEndpoint: '%s:50051' % endpointsLib.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    tokenExchangeEndpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container = {
    name: appName,
    target: {
      name: '//services/billing/server:image',
      dst: 'billing',
    },
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    volumeMounts: [
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      orbApiKey.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
    args: [
      '--config',
      configMap.filename,
    ],
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 20,
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      configMap.podVolumeDef,
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      orbApiKey.podVolumeDef,
    ],
  };

  local minReplicas = {
    DEV: 1,
    STAGING: 2,
    PROD: 2,
  };
  local maxReplicas = {
    DEV: 2,
    STAGING: 4,
    PROD: 4,
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: minReplicas[env],
      minReadySeconds: if env == 'DEV' then 0 else 60,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  // Scale when the subscription backlog gets too large.
  local scaledObject = {
    apiVersion: 'keda.sh/v1alpha1',
    kind: 'ScaledObject',
    metadata: {
      name: '%s-scaledobject' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      scaleTargetRef: {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        name: appName,
      },
      minReplicaCount: minReplicas[env],
      maxReplicaCount: maxReplicas[env],
      triggers: [
        {
          type: 'prometheus',
          metadata: {
            serverAddress: 'http://gmp-frontend.monitoring.svc.cluster.local:9090',
            metricName: 'pubsub_googleapis_com:subscription_num_undelivered_messages',
            threshold: '500',
            query: 'sum(avg_over_time(pubsub_googleapis_com:subscription_num_undelivered_messages{monitored_resource="pubsub_subscription",subscription_id="%s"}[1m]))' % config.subscribeClientConfig.subscriptionId,
          },
        },
      ],
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    deployment,
    scaledObject,
    subscriber.objects,
    dynamicFeatureFlags.k8s_objects,
    serverCert.objects,
    clientCert.objects,
    centralClientCert.objects,
    services,
    orbApiKey.objects,
  ])

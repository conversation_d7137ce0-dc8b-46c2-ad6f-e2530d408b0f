package migration

/*
<PERSON>e to Orb Migration Validation Job

This job validates the results of migrating users from Stripe to Orb billing system.
It runs when triggered by a file and performs various checks on each user to ensure
the migration was successful.

Validations performed:
1. User's billingMethod is set to ORB
2. User should has a valid orbCustomerId
3. User's Stripe subscription is not in active/trialing status
4. If user has an orbSubscriptionId, Orb subscription plan matches the user's tier

The job skips users in enterprise or self-serve-team tenants, and users with
conflicting tenant tiers.
*/

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
)

const (
	stripeToOrbValidationTriggerFilePath = "/tmp/trigger_stripe_to_orb_validation"
	stripeToOrbValidationPollInterval    = 30 * time.Second
)

type StripeToOrbValidationConfig struct {
	// Allowlist of users, specified as a map from user id to tenant id.
	// Most users have one tenant, but there are a small ammount of users with multiple tenants. For those users,
	// we only specify one tenant id here.The tenant id specified here is only used to exchange service token for
	// getting the user entity. We will still perform checks on all tenants on the user entity (e.g. whether they
	// are on enterprise) after we retrieve the user entity.
	AllowlistedUsers map[string]string `json:"allowlisted_users"`

	// MaxFailureCountUser limits the number of failed validations before stopping.
	// This prevents the job from continuing when there are too many failures.
	MaxFailureCountUser int `json:"max_failure_count_user"`

	// SleepDuration controls the pause between processing each user.
	// Helps avoid rate limits with external APIs. Defaults to 100ms if not specified.
	SleepDurationMs int `json:"sleep_duration_ms"`
}

type StripeToOrbValidationJob struct {
	orbConfig    OrbConfig
	authClient   authclient.AuthClient
	tokenClient  tokenexchangeclient.TokenExchangeClient
	stripeClient StripeClient
	orbClient    orb.OrbClient
	tenantCache  tenantwatcherclient.TenantCache

	validationConfig *StripeToOrbValidationConfig
}

// StripeToOrbValidationResult contains the results of the validation process.
type StripeToOrbValidationResult struct {
	SucceededUserCount        int
	ValidationFailedUserCount int
	FailedUserCount           int

	// ValidationErrors maps user IDs to their validation error messages.
	// Users not in this map passed validation successfully.
	ValidationErrors map[string][]string
}

func NewStripeToOrbValidationJob(
	ctx context.Context,
	orbConfig OrbConfig,
	authClient authclient.AuthClient,
	tokenClient tokenexchangeclient.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCache,
	orbClient orb.OrbClient,
) (*StripeToOrbValidationJob, error) {
	return &StripeToOrbValidationJob{
		orbConfig:    orbConfig,
		authClient:   authClient,
		tokenClient:  tokenClient,
		tenantCache:  tenantCache,
		stripeClient: &DefaultStripeClient{},
		orbClient:    orbClient,
	}, nil
}

func (s *StripeToOrbValidationJob) Run(ctx context.Context) error {
	log.Info().Msg("Starting Stripe to Orb validation job monitor")

	// Loop forever to execute validation whenever the trigger file is created
	for {
		log.Info().
			Str("file", stripeToOrbValidationTriggerFilePath).
			Msg("Waiting for trigger file to exist to start validation job")

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(stripeToOrbValidationPollInterval):
			if _, err := os.Stat(stripeToOrbValidationTriggerFilePath); err == nil {
				// Detected the trigger file, proceed with running the validation job
				log.Info().Str("file", stripeToOrbValidationTriggerFilePath).Msg("Trigger file found, proceeding with validation")

				// Read configuration from trigger file
				validationConfig, err := s.ReadConfigFromTriggerFile()
				if err != nil {
					log.Error().Err(err).Msg("Failed to read config from trigger file")
				}

				// Remove trigger file after reading
				if err := os.Remove(stripeToOrbValidationTriggerFilePath); err != nil {
					return fmt.Errorf("failed to remove trigger file: %w", err)
				}

				if validationConfig != nil {
					s.validationConfig = validationConfig
					result, err := s.DoValidation(ctx)
					if err != nil {
						log.Error().Err(err).Msg("Failed to execute validation")
					}
					if result != nil {
						log.Info().
							Int("succeeded_user_count", result.SucceededUserCount).
							Int("validation_failed_user_count", result.ValidationFailedUserCount).
							Int("failed_user_count", result.FailedUserCount).
							Msg("Stripe to Orb validation job completed")
					}
					s.validationConfig = nil
				}
			} else if !os.IsNotExist(err) {
				return fmt.Errorf("error checking trigger file: %w", err)
			} else {
				log.Debug().
					Str("file", stripeToOrbValidationTriggerFilePath).
					Msgf("Waiting %s before checking for trigger file again", stripeToOrbValidationPollInterval)
			}
		}
	}
}

func (s *StripeToOrbValidationJob) ReadConfigFromTriggerFile() (*StripeToOrbValidationConfig, error) {
	triggerFileContent, err := os.ReadFile(stripeToOrbValidationTriggerFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read trigger file: %w", err)
	}

	var config StripeToOrbValidationConfig
	if err := json.Unmarshal(triggerFileContent, &config); err != nil {
		return nil, fmt.Errorf("failed to parse trigger file JSON: %w", err)
	}

	return &config, nil
}

func reportValidationProgress(succeededUserCount, validationFailedUserCount, failedUserCount int, isFinal bool) {
	if (succeededUserCount+validationFailedUserCount+failedUserCount)%100 == 0 || isFinal {
		log.Info().
			Int("succeeded_user_count", succeededUserCount).
			Int("validation_failed_user_count", validationFailedUserCount).
			Int("failed_user_count", failedUserCount).
			Msg("Validation job progress")
	}
}

// DoValidation performs validation for allowlisted users.
func (s *StripeToOrbValidationJob) DoValidation(ctx context.Context) (*StripeToOrbValidationResult, error) {
	result := &StripeToOrbValidationResult{
		ValidationErrors: make(map[string][]string),
	}

	log.Info().Msg("Starting Stripe to Orb validation")

	succeededUserCount := 0
	failedUserCount := 0
	validationFailedUserCount := 0

	log.Info().Int("user_count", len(s.validationConfig.AllowlistedUsers)).Msg("Starting validation job for allowlisted users")

	for userID, tenantIDAndShardNamespace := range s.validationConfig.AllowlistedUsers {
		tenantID, shardNamespace := splitTenantIDAndShardNamespace(tenantIDAndShardNamespace)
		reportValidationProgress(succeededUserCount, validationFailedUserCount, failedUserCount, false)

		if failedUserCount >= s.validationConfig.MaxFailureCountUser {
			log.Warn().Int("max_failure_count", s.validationConfig.MaxFailureCountUser).Msg("Reached max failure count, stopping validation")
			break
		}

		// Perform validation for the user
		validationErrors, err := s.validateUser(ctx, userID, tenantID, shardNamespace)
		if err != nil {
			log.Error().Err(err).Str("user_id", userID).Msg("Error validating user")
			failedUserCount++
		} else if len(validationErrors) > 0 {
			result.ValidationErrors[userID] = validationErrors
			validationFailedUserCount++
			log.Warn().
				Str("user_id", userID).
				Interface("validation_errors", validationErrors).
				Msg("User validation produced errors")
		} else {
			succeededUserCount++
			log.Info().Str("user_id", userID).Msg("User validation succeeded")
		}

		// Sleep to avoid rate limit
		sleepDuration := time.Duration(s.validationConfig.SleepDurationMs) * time.Millisecond
		if sleepDuration == 0 {
			sleepDuration = 100 * time.Millisecond
		}
		time.Sleep(sleepDuration)
	}

	reportValidationProgress(succeededUserCount, validationFailedUserCount, failedUserCount, true)
	result.SucceededUserCount = succeededUserCount
	result.ValidationFailedUserCount = validationFailedUserCount
	result.FailedUserCount = failedUserCount
	return result, nil
}

// validateUser performs validation for a single user.
// It returns a list of validation errors, or nil if no validation errors are found.
func (s *StripeToOrbValidationJob) validateUser(ctx context.Context, userID string, tenantID string, shardNamespace string) (validationErrors []string, err error) {
	user, err := GetUser(ctx, s.authClient, s.tokenClient, userID, tenantID, shardNamespace)
	if err != nil {
		log.Error().Err(err).Str("user_id", userID).Str("tenant_id", tenantID).Msg("Failed to get user")
		return nil, err
	}

	// Check user's tenants and skip users in self serve team and enterprise tenants
	userTenant, skipReason, err := CheckUserTenants(ctx, s.tenantCache, userID, user.Tenants)
	if err != nil {
		return nil, err
	}
	if skipReason != "" {
		log.Info().Str("user_id", userID).Str("reason", skipReason).Msg("Skipping user")
		return nil, nil
	}

	// Skip validation for users without Stripe customer ID or subscription ID
	if user.StripeCustomerId == "" {
		skipReason = "User does not have a Stripe customer ID"
	} else if user.SubscriptionId == nil || *user.SubscriptionId == "" {
		skipReason = "User does not have a Stripe subscription ID"
	}
	if skipReason != "" {
		log.Info().Str("user_id", userID).Str("reason", skipReason).Msg("Skipping user")
		return nil, nil
	}

	// Validate that the user's billingMethod is set to ORB
	if user.BillingMethod != auth_entities.BillingMethod_BILLING_METHOD_ORB {
		if user.BillingMethod == auth_entities.BillingMethod_BILLING_METHOD_MIGRATING_TO_ORB {
			log.Error().Str("user_id", user.Id).Msg("User's billingMethod is MIGRATING_TO_ORB, migration not completed")
			validationErrors = append(
				validationErrors, "User's billingMethod is MIGRATING_TO_ORB, migration not completed")
		} else {
			validationErrors = append(
				validationErrors, fmt.Sprintf("User's billingMethod is %s, expected ORB", user.BillingMethod))
		}
	} else {
		log.Info().Str("user_id", user.Id).Msg("Validated user's billingMethod is ORB")
	}

	// Validate that the user has orbCustomerId
	if user.OrbCustomerId == "" {
		log.Error().Str("user_id", user.Id).Msg("User does not have orbCustomerId")
		validationErrors = append(validationErrors, "User does not have orbCustomerId")
	} else {
		log.Info().Str("user_id", user.Id).Str("orb_customer_id", user.OrbCustomerId).Msg("Validated user has orbCustomerId")
	}

	// Validate Stripe subscription is not in active or trialing status
	validationErrs, err := s.validateStripeSubscription(user)
	if err != nil {
		return nil, err
	}
	validationErrors = append(validationErrors, validationErrs...)

	// Validate Orb subscription plan matches user's tier
	validationErrs, err = s.validateOrbSubscription(ctx, user, userTenant.Tier)
	if err != nil {
		return nil, err
	}
	validationErrors = append(validationErrors, validationErrs...)

	return validationErrors, nil
}

// validateStripeSubscription checks that the user's Stripe subscription is in the correct state.
func (s *StripeToOrbValidationJob) validateStripeSubscription(user *auth_entities.User) (validationErrors []string, err error) {
	if user.SubscriptionId == nil && *user.SubscriptionId == "" {
		return nil, nil
	}

	stripeSubscription, err := s.stripeClient.GetSubscription(*user.SubscriptionId)
	if err != nil {
		return nil, fmt.Errorf("failed to get Stripe subscription: %w", err)
	}

	// Check that the Stripe subscription is not in active or trialing status
	if stripeSubscription.Status == stripe.SubscriptionStatusActive ||
		stripeSubscription.Status == stripe.SubscriptionStatusTrialing {
		log.Error().
			Str("user_id", user.Id).
			Str("subscription_id", *user.SubscriptionId).
			Str("subscription_status", string(stripeSubscription.Status)).
			Msg("User's Stripe subscription should not be in active or trialing status")
		validationErrors = append(validationErrors, fmt.Sprintf("User's Stripe subscription should not be in %s status", stripeSubscription.Status))
	}

	if validationErrors == nil {
		log.Info().
			Str("user_id", user.Id).
			Str("subscription_id", *user.SubscriptionId).
			Str("subscription_status", string(stripeSubscription.Status)).
			Msg("Validated user's Stripe subscription is not in active or trialing status")
	}

	return validationErrors, nil
}

// validateOrbSubscription checks that the user's Orb subscription plan matches their tier.
func (s *StripeToOrbValidationJob) validateOrbSubscription(
	ctx context.Context,
	user *auth_entities.User,
	tenantTier tw_pb.TenantTier,
) (validationErrors []string, err error) {
	// End-of-trial users do not have an Orb subscription after migration
	if user.OrbSubscriptionId == "" {
		log.Info().Str("user_id", user.Id).Msg("User does not have an Orb subscription. Skip validating Orb subscription.")
		return nil, nil
	}

	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, user.OrbSubscriptionId, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get Orb subscription: %w", err)
	}

	switch tenantTier {
	case tw_pb.TenantTier_PROFESSIONAL:
		if orbSubscription.ExternalPlanID != s.orbConfig.ProfessionalPlanID &&
			orbSubscription.ExternalPlanID != s.orbConfig.TrialPlanID {
			log.Error().
				Str("user_id", user.Id).
				Str("orb_subscription_id", user.OrbSubscriptionId).
				Str("orb_plan_id", orbSubscription.ExternalPlanID).
				Msg("User is in PROFESSIONAL tier but their Orb subscription plan is not professional or trial")
			validationErrors = append(
				validationErrors,
				fmt.Sprintf("User is in PROFESSIONAL tier but their Orb subscription plan is %s", orbSubscription.ExternalPlanID),
			)
		}
		if orbSubscription.ExternalPlanID == s.orbConfig.TrialPlanID && orbSubscription.EndDate.IsZero() {
			log.Error().
				Str("user_id", user.Id).
				Str("orb_subscription_id", user.OrbSubscriptionId).
				Msg("User is on trial plan but their Orb trial subscription is not scheduled to cancel")
			validationErrors = append(
				validationErrors,
				"User is on trial plan but their Orb trial subscription is not scheduled to cancel",
			)
		}
	case tw_pb.TenantTier_COMMUNITY:
		if orbSubscription.ExternalPlanID != s.orbConfig.CommunityPlanID {
			log.Error().
				Str("user_id", user.Id).
				Str("orb_subscription_id", user.OrbSubscriptionId).
				Str("orb_plan_id", orbSubscription.ExternalPlanID).
				Msg("User is in COMMUNITY tier but their Orb subscription plan is not community")
			validationErrors = append(
				validationErrors,
				fmt.Sprintf("User is in COMMUNITY tier but their Orb subscription plan is %s", orbSubscription.ExternalPlanID),
			)
		}
	}

	if validationErrors == nil {
		log.Info().
			Str("user_id", user.Id).
			Str("tenant_tier", tenantTier.String()).
			Str("orb_subscription_id", user.OrbSubscriptionId).
			Str("orb_plan_id", orbSubscription.ExternalPlanID).
			Msg("Validated user's Orb subscription plan matches their tier")
	}

	return validationErrors, nil
}

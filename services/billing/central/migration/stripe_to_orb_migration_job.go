package migration

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
)

/*
This migration job is to migrate users from Stripe to Orb.

The core logics are in MigrateUser, where we perform the following operations and try to support idempotency in case of failure.

Before Migrating:
We perform some checks to skip the user is already on Orb, or under certain conditions (e.g. missing a stripe customer ID).
We also update the user's billingMethod to MIGRATING_TO_ORB to indicate that the migration is in progress.
Then we start migrating the user following the steps below.

Step 1: create Orb customer and alerts.
If we fail at this step, we revert the user's billingMethod to STRIPE.
After this step succeed, we record the orbCustomerId into User entity, so we won't re-execute this step if we re-run due to failure in later steps.

Step 2: create Orb subscription.
If we fail at this step, we revert the user's billingMethod to STRIPE .
After this step succeed, we record the orbSubscriptionId into User entity, so we won't recreate orb subscription if we re-run due to failure in later steps.
We also will not revert to STRIPE billingMethod if failure happens after this step, but will leave it as MIGRATING_TO_ORB.

Step 3: cancel Stripe subscription
If we fail at this step, we will leave the state as MIGRATING_TO_ORB. Hopefully this shouldn't happen often.
After this step succeed, we set the user's billingMethod to ORB.
*/

const (
	stripeToOrbMigrationTriggerFilePath = "/tmp/trigger_stripe_to_orb_migration"
	stripeToOrbMigrationPollInterval    = 30 * time.Second
)

// The configurations for the migration job, stored in the trigger file in json format
type StripeToOrbMigrationConfig struct {
	// Allowlist of users, specified as a map from user id to tenant id.
	// Most users have one tenant, but there are a small ammount of users with multiple tenants. For those users,
	// we only specify one tenant id here.The tenant id specified here is only used to exchange service token for
	// getting the user entity. We will still perform checks on all tenants on the user entity (e.g. whether they
	// are on enterprise) after we retrieve the user entity.
	AllowlistedUsers map[string]string `json:"allowlisted_users"`

	// These config is to limit the number of failed migrations,
	// to avoid keep going when there are too many failures
	MaxFailureCountUser int `json:"max_failure_count_user"`

	// SleepDuration controls the pause between processing each user.
	// Helps avoid rate limits with external APIs. Defaults to 100ms if not specified.
	SleepDurationMs int `json:"sleep_duration_ms"`

	// MigrationTaskQueueLength controls the number of users to queue up for migration.
	// This is for us to disable entry points that may cause race condition issues when migrating users.
	// Default to 1000 if not specified.
	MigrationTaskQueueLength int `json:"migration_task_queue_length"`

	// Must set this to true to actually perform write operations
	// This is to help dumping some user data without performing writes
	NotDryRun bool `json:"not_dry_run"`

	// Whether to apply friends and family adjustments, which 6 months of free dev plan
	// This will only be set to true for a special batch of users on d0
	// See https://augment-wic8570.slack.com/archives/C088SEKUNN9/p1745263051637139
	ApplyFriendsAndFamilyAdjustments bool `json:"apply_friends_and_family_adjustments"`

	// Whether to apply the fix for users with multiple subscriptions.
	// We will create user Orb subscription again based on the user's active Stripe subscription, even if they have already
	// been migrated to Orb and their Orb subscription is in ended state, and they have one active stripe subscription.
	ApplyFixForMultipleSubscriptionIssue bool `json:"apply_fix_for_multiple_subscription_issue"`
}

type UserMigrationTask struct {
	user                *auth_entities.User
	userTenant          *tw_pb.Tenant
	billingMethodOnExit auth_entities.BillingMethod
}

// StripeToOrbMigrationJob performs the migration
type StripeToOrbMigrationJob struct {
	orbConfig       OrbConfig
	authClient      authclient.AuthClient
	tokenClient     tokenexchangeclient.TokenExchangeClient
	stripeClient    StripeClient
	orbClient       orb.OrbClient
	migrationConfig *StripeToOrbMigrationConfig
	localLog        zerolog.Logger
	tenantCache     tenantwatcherclient.TenantCache
}

type StripeToOrbMigrationResult struct {
	FailedUserCount    int
	SucceededUserCount int
	SkippedUserCount   int
}

// NewStripeToOrbMigrationJob creates a new StripeToOrbMigrationJob
func NewStripeToOrbMigrationJob(
	ctx context.Context,
	orbConfig OrbConfig,
	authClient authclient.AuthClient, // Using interface{} to avoid dependency on authclient
	tokenClient tokenexchangeclient.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCache,
	orbClient orb.OrbClient,
) (*StripeToOrbMigrationJob, error) {
	return &StripeToOrbMigrationJob{
		orbClient:    orbClient,
		stripeClient: &DefaultStripeClient{},
		authClient:   authClient,
		tokenClient:  tokenClient,
		tenantCache:  tenantCache,
		orbConfig:    orbConfig,
	}, nil
}

// Run the migration job
func (s *StripeToOrbMigrationJob) Run(ctx context.Context) error {
	// Loop forever to execute migration whenever the trigger file is created
	for {
		log.Info().
			Str("file", stripeToOrbMigrationTriggerFilePath).
			Msg("Waiting for trigger file to exist to start migration job")

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(stripeToOrbMigrationPollInterval):
			// Check for trigger file
			if _, err := os.Stat(stripeToOrbMigrationTriggerFilePath); err == nil {
				// Detected the trigger file, proceed with running the migration job
				log.Info().Str("file", stripeToOrbMigrationTriggerFilePath).Msg("Trigger file found, proceeding with migration")

				migrationConfig, err := s.ReadConfigFromTriggerFile()
				if err != nil {
					log.Error().Err(err).Msg("Failed to read config from trigger file")
				}

				if err := os.Remove(stripeToOrbMigrationTriggerFilePath); err != nil {
					return fmt.Errorf("failed to remove trigger file: %w", err)
				}

				if migrationConfig != nil {
					s.migrationConfig = migrationConfig
					result, err := s.DoMigration(ctx)
					if err != nil {
						log.Error().Err(err).Msg("Failed to execute migration")
					}
					if result != nil {
						log.Info().Interface("result", result).Msg("Stripe to Orb migration job completed")
					}
					s.migrationConfig = nil
				}
			} else if !os.IsNotExist(err) {
				return fmt.Errorf("error checking trigger file: %w", err)
			} else {
				log.Debug().
					Str("file", stripeToOrbMigrationTriggerFilePath).
					Msgf("Trigger file not found, waiting %s before checking again", stripeToOrbMigrationPollInterval)
			}
		}
	}
}

func (s *StripeToOrbMigrationJob) ReadConfigFromTriggerFile() (*StripeToOrbMigrationConfig, error) {
	// Read configuration from the trigger file
	triggerFileContent, err := os.ReadFile(stripeToOrbMigrationTriggerFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read trigger file: %w", err)
	}

	var config StripeToOrbMigrationConfig
	if err := json.Unmarshal(triggerFileContent, &config); err != nil {
		return nil, fmt.Errorf("failed to parse trigger file JSON: %w", err)
	}

	return &config, nil
}

func reportMigrationProgress(skippedUserCount int, succeededUserCount int, failedUserCount int, isFinal bool) {
	if (skippedUserCount+failedUserCount+succeededUserCount)%100 == 0 || isFinal {
		log.Info().
			Int("skipped_user_count", skippedUserCount).
			Int("succeeded_user_count", succeededUserCount).
			Int("failed_user_count", failedUserCount).
			Msg("Migration job progress")
	}
}

func (s *StripeToOrbMigrationJob) DoMigration(ctx context.Context) (*StripeToOrbMigrationResult, error) {
	log.Info().Msg("Starting executing Stripe to Orb migration job")

	// Add a local file logger to dump detailed user and subscription data for debugging
	fileName := fmt.Sprintf("/tmp/migration_%s.log", time.Now().Format("20060102_150405"))
	f, err := os.OpenFile(fileName, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0o644)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file: %w", err)
	}
	s.localLog = log.Output(f)
	defer f.Close()

	// Sanity check for applying friends and family adjustments. It should only be enabled for a batch of ~176 users on d0.
	if s.migrationConfig.ApplyFriendsAndFamilyAdjustments && len(s.migrationConfig.AllowlistedUsers) > 200 {
		return nil, fmt.Errorf("friends and family adjustments should only be enabled for a small batch of users")
	}

	failedUserCount := 0
	succeededUserCount := 0
	skippedUserCount := 0

	// Sleep duration to avoid rate limit
	sleepDuration := time.Duration(s.migrationConfig.SleepDurationMs) * time.Millisecond
	if sleepDuration == 0 {
		sleepDuration = 100 * time.Millisecond
	}

	migrationTaskQueueLength := s.migrationConfig.MigrationTaskQueueLength
	if migrationTaskQueueLength == 0 {
		migrationTaskQueueLength = 1000
	}

	var migrationTasks []*UserMigrationTask
	lastMigrationTaskQueueUpdateTime := time.Now()

	defer func() {
		// It's unexpected to have remaining tasks when exiting the function. We will try to set user's billingMethod to billingMethodOnExit,
		// which should be their original billingMethod value since we did not process these tasks.
		if len(migrationTasks) > 0 {
			log.Error().Int("remaining_tasks", len(migrationTasks)).Msg("Unexpectedly exiting migration job with remaining tasks in the queue")
			for _, task := range migrationTasks {
				if err := s.DoIfNotDryRun("Update user's billing method to original value", func() error {
					log.Warn().
						Str("user_id", task.user.Id).
						Str("billing_method_on_exit", task.billingMethodOnExit.String()).
						Msg("Updating user's billingMethod on unexpected exit")
					return s.updateUserBillingInfo(ctx, task.user, task.userTenant, &task.billingMethodOnExit, nil, nil, nil)
				}); err != nil {
					log.Error().Err(err).Msgf("Failed to update billing method for user %s", task.user.Id)
				}
			}
		}
	}()

	log.Info().Int("user_count", len(s.migrationConfig.AllowlistedUsers)).Msg("Starting migration job for allowlisted users")

	for userID, tenantIDAndShardNamespace := range s.migrationConfig.AllowlistedUsers {
		tenantID, shardNamespace := splitTenantIDAndShardNamespace(tenantIDAndShardNamespace)
		reportMigrationProgress(skippedUserCount, succeededUserCount, failedUserCount, false)

		if failedUserCount > s.migrationConfig.MaxFailureCountUser {
			log.Warn().Int("max_failure_count", s.migrationConfig.MaxFailureCountUser).Msg("Reached max failure count, stopping migration")
			break
		}

		migrationTask, err := s.CreateMigrationTask(ctx, userID, tenantID, shardNamespace)
		if err != nil {
			log.Error().Err(err).Str("user_id", userID).Msg("Failed to create migration task for user")
			failedUserCount++
			continue
		}
		if migrationTask == nil { // User should be skipped
			skippedUserCount++
			continue
		}

		// Update user's billing method to MIGRATING_TO_ORB before appending the user to the task queue
		migratingToOrbBillingMethod := auth_entities.BillingMethod_BILLING_METHOD_MIGRATING_TO_ORB
		if err := s.DoIfNotDryRun("Update user's billing method to MIGRATING_TO_ORB", func() error {
			log.Info().Str("user_id", userID).Msg("Updating user's billing method to MIGRATING_TO_ORB")
			return s.updateUserBillingInfo(ctx, migrationTask.user, migrationTask.userTenant, &migratingToOrbBillingMethod, nil, nil, nil)
		}); err != nil {
			log.Error().Err(err).Str("user_id", userID).Msg("Failed to update user's billingMethod to MIGRATING_TO_ORB")
			failedUserCount++
			continue
		}

		// Append the user's migration task to the task queue
		migrationTasks = append(migrationTasks, migrationTask)
		log.Info().Str("user_id", userID).Msg("Added migration task to queue")

		// Process one task if we've reached the queue length limit
		if len(migrationTasks) >= migrationTaskQueueLength {
			// Pop the first task
			task := migrationTasks[0]
			migrationTasks = migrationTasks[1:]
			log.Info().Str("user_id", task.user.Id).Msg("Popped migration task from queue, starting migration")

			if err := s.MigrateUser(ctx, task); err != nil {
				log.Error().Err(err).Str("user_id", task.user.Id).Msgf("Failed to migrate user")
				failedUserCount++
			} else {
				log.Info().Str("user_id", task.user.Id).Msg("Migrated user successfully")
				succeededUserCount++
			}
		}

		if time.Since(lastMigrationTaskQueueUpdateTime) < sleepDuration {
			time.Sleep(sleepDuration - time.Since(lastMigrationTaskQueueUpdateTime))
		}
		lastMigrationTaskQueueUpdateTime = time.Now()
	}

	// Process any remaining tasks in the queue
	for len(migrationTasks) > 0 {
		reportMigrationProgress(skippedUserCount, succeededUserCount, failedUserCount, false)

		task := migrationTasks[0]
		migrationTasks = migrationTasks[1:]

		log.Info().Str("user_id", task.user.Id).Msg("Popped migration task from queue, starting migration")
		if err := s.MigrateUser(ctx, task); err != nil {
			log.Error().Err(err).Str("user_id", task.user.Id).Msgf("Failed to migrate user")
			failedUserCount++
		} else {
			log.Info().Str("user_id", task.user.Id).Msg("Migrated user successfully")
			succeededUserCount++
		}

		if time.Since(lastMigrationTaskQueueUpdateTime) < sleepDuration {
			time.Sleep(sleepDuration - time.Since(lastMigrationTaskQueueUpdateTime))
		}
		lastMigrationTaskQueueUpdateTime = time.Now()
	}

	reportMigrationProgress(skippedUserCount, succeededUserCount, failedUserCount, true)
	return &StripeToOrbMigrationResult{
		FailedUserCount:    failedUserCount,
		SucceededUserCount: succeededUserCount,
		SkippedUserCount:   skippedUserCount,
	}, nil
}

func (s *StripeToOrbMigrationJob) DoIfNotDryRun(msg string, fn func() error) error {
	if s.migrationConfig.NotDryRun {
		return fn()
	} else {
		s.localLog.Info().Msg("Dry Run --- " + msg)
	}
	return nil
}

func (s *StripeToOrbMigrationJob) CreateMigrationTask(ctx context.Context, userID string, tenantID string, shardNamespace string) (migrationTask *UserMigrationTask, err error) {
	log.Info().Str("user_id", userID).Msg("Creating migration task for user")

	// Get user from auth
	user, err := GetUser(ctx, s.authClient, s.tokenClient, userID, tenantID, shardNamespace)
	if err != nil {
		log.Error().Err(err).Str("user_id", userID).Str("tenant_id", tenantID).Msg("Failed to get user")
		return nil, err
	}
	s.localLog.Info().Interface("user", user).Msg("Fetched user")

	// Validate user's tenants
	userTenant, skipReason, err := CheckUserTenants(ctx, s.tenantCache, userID, user.Tenants)
	if err != nil {
		return nil, err
	}
	if skipReason != "" {
		log.Info().Str("user_id", userID).Str("reason", skipReason).Msg("Skipping user")
		return nil, nil
	}

	// Revert the billing method to Stripe if we fail at any point before creating Orb subscription
	billingMethodOnExit := auth_entities.BillingMethod_BILLING_METHOD_STRIPE

	switch user.BillingMethod {
	case auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN:
		fallthrough
	case auth_entities.BillingMethod_BILLING_METHOD_STRIPE:
		if user.StripeCustomerId == "" {
			log.Info().Str("user_id", user.Id).Msg("User does not have a Stripe customer ID")
		}
		if user.SubscriptionId == nil || *user.SubscriptionId == "" {
			log.Info().Str("user_id", user.Id).Msg("User does not have a Stripe subscription ID")
		}
	case auth_entities.BillingMethod_BILLING_METHOD_ORB:
		if s.migrationConfig.ApplyFixForMultipleSubscriptionIssue {
			// The user should stay in Orb when fixing the multiple-subscription issue for users already migrated to Orb
			billingMethodOnExit = auth_entities.BillingMethod_BILLING_METHOD_ORB
		} else {
			skipReason = "User is already on Orb"
		}
	case auth_entities.BillingMethod_BILLING_METHOD_MIGRATING_TO_ORB:
		// This is probably a re-run, that the previous run failed after updating the state to MIGRATING_TO_ORB
		log.Info().Str("user_id", user.Id).Msg("User is in MIGRATING_TO_ORB state. This is probably a re-run.")
		// We should not go back to STRIPE from MIGRATING_TO_ORB
		billingMethodOnExit = auth_entities.BillingMethod_BILLING_METHOD_MIGRATING_TO_ORB
	default:
		log.Error().Str("user_id", user.Id).Str("billing_method", user.BillingMethod.String()).Msg("User's billing method is invalid.")
		return nil, fmt.Errorf("user's billing method is invalid")
	}

	if skipReason != "" {
		log.Info().Str("user_id", user.Id).Str("reason", skipReason).Msg("Skipping user")
		return nil, nil
	}

	// Sometimes a user's tier change can last more than a few minutes. To avoid conflicts,
	// it's better to fail for this user and retry later after the tier change completes.
	if user.TierChange != nil {
		return nil, fmt.Errorf("User has a tier change in progress")
	}

	// This user needs to be migrated
	return &UserMigrationTask{
		user:                user,
		userTenant:          userTenant,
		billingMethodOnExit: billingMethodOnExit,
	}, nil
}

func (s *StripeToOrbMigrationJob) MigrateUser(ctx context.Context, migrationTask *UserMigrationTask) error {
	user := migrationTask.user
	userTenant := migrationTask.userTenant
	orbCustomerId := user.OrbCustomerId
	orbSubscriptionId := user.OrbSubscriptionId

	defer func() {
		// Post migration, update user's billing method to the value of billingMethodOnExit.
		// billingMethodOnExit should be ORB if all steps are completed successfully.
		// If failures occur, billingMethodOnExit could be STRIPE or MIGRATING_TO_ORB depending on where it fails.
		if err := s.DoIfNotDryRun("Update user's billing method to final state", func() error {
			if migrationTask.billingMethodOnExit != auth_entities.BillingMethod_BILLING_METHOD_ORB {
				log.Warn().
					Str("user_id", user.Id).
					Str("billing_method_on_exit", migrationTask.billingMethodOnExit.String()).
					Msg("Updating user's billingMethod on unexpected exit")
			} else {
				log.Info().Str("user_id", user.Id).Msg("Updating user's billingMethod to ORB")
			}
			return s.updateUserBillingInfo(ctx, user, userTenant, &migrationTask.billingMethodOnExit, nil, nil, nil)
		}); err != nil {
			log.Error().Err(err).
				Str("user_id", user.Id).
				Str("billing_method_on_exit", migrationTask.billingMethodOnExit.String()).
				Msg("Failed to update billingMethod when exiting MigrateUser")
		}
	}()

	log.Info().Str("user_id", user.Id).Str("tenant_id", userTenant.Id).Bool("dry_run", !s.migrationConfig.NotDryRun).Msg("Migrating user")
	s.localLog.Info().Interface("migration_task", migrationTask).Bool("dry_run", !s.migrationConfig.NotDryRun).Msg("Migrating user")

	// Step 0: create a Stripe customer if the user does not have one
	if user.StripeCustomerId == "" {
		stripeCustomerID, err := s.createStripeCustomer(ctx, user)
		if err != nil {
			return err
		}

		if err := s.DoIfNotDryRun("Update user's stripe customer ID", func() error {
			return s.updateUserBillingInfo(ctx, user, userTenant, nil, nil, nil, &stripeCustomerID)
		}); err != nil {
			return err
		}
		user.StripeCustomerId = stripeCustomerID
	}

	// This is to fake a subscription that is already ended if the user does not have a Stripe subscription, i.e. user.SubscriptionId is null
	// This is to avoid doing too many nil checks below because we used to assume the user.SubscriptionId is not null when we reach this point
	var stripeSubscription *stripe.Subscription = &stripe.Subscription{
		Status:  stripe.SubscriptionStatusCanceled,
		EndedAt: time.Now().Unix(),
	}

	if user.SubscriptionId != nil && *user.SubscriptionId != "" {
		// We need to call Stripe API to get the subscription details, because we need the current period start and end date which is not stored in Subscription table
		stripeSub, err := s.stripeClient.GetSubscription(*user.SubscriptionId)
		if err != nil {
			return fmt.Errorf("failed to get subscription: %w", err)
		}
		stripeSubscription = stripeSub

		if user.StripeCustomerId != stripeSubscription.Customer.ID {
			log.Error().Str("user_id", user.Id).Msg("User's Stripe customer ID does not match the stripe subscription's customer ID.")

			// This indicate some data issue we had, but as long as the subscription with mismatching customer ID is not active,
			// we can still proceed because the logics below will try to find an active subscription for the customer ID and use
			// the active one if found; if no active subscriptions are found in the end, we just migrate the user as EOT user.
			if stripeSubscription.Status == stripe.SubscriptionStatusActive ||
				stripeSubscription.Status == stripe.SubscriptionStatusTrialing {
				return fmt.Errorf("user's Stripe customer ID does not match the stripe subscription's customer ID. Something wrong happened.")
			}
		}
		log.Info().
			Str("user_id", user.Id).
			Str("subscription_id", *user.SubscriptionId).
			Str("stripe_customer_id", user.StripeCustomerId).
			Str("status", string(stripeSubscription.Status)).
			Msg("Fetched user's Stripe subscription")
		s.localLog.Info().
			Str("user_id", user.Id).
			Interface("subscription", stripeSubscription).
			Msg("Fetched user's Stripe subscription")
	}

	// List all stripe subscriptions for the user
	stripeSubscriptions, err := s.stripeClient.ListSubscriptions(user.StripeCustomerId, true)
	if err != nil {
		return fmt.Errorf("failed to list subscriptions: %w", err)
	}

	// Check if there are multiple active subscriptions, and fail migration in such case
	var activeStripeSubscription *stripe.Subscription
	for _, sub := range stripeSubscriptions {
		if sub.Status == stripe.SubscriptionStatusActive || sub.Status == stripe.SubscriptionStatusTrialing {
			if activeStripeSubscription != nil {
				log.Error().
					Str("user_id", user.Id).
					Str("stripe_customer_id", user.StripeCustomerId).
					Str("subscription_id1", activeStripeSubscription.ID).
					Str("subscription_id2", sub.ID).
					Msg("User has multiple active subscriptions. This should not happen.")
				return fmt.Errorf("user has multiple active subscriptions. This should not happen.")
			}
			activeStripeSubscription = sub
		}
	}

	// If there is one active subscription, but it's not the one recorded on user entity, use the active one instead.
	if activeStripeSubscription != nil && (user.SubscriptionId == nil || activeStripeSubscription.ID != *user.SubscriptionId) {
		log.Error().
			Str("user_id", user.Id).
			Str("stripe_customer_id", user.StripeCustomerId).
			Interface("subscription_id_on_user", user.SubscriptionId).
			Str("subscription_id", activeStripeSubscription.ID).
			Str("status", string(activeStripeSubscription.Status)).
			Msg("The active subscription is not the one recorded on user entity. Using the active subscription instead.")
		s.localLog.Info().
			Str("user_id", user.Id).
			Interface("subscription", activeStripeSubscription).
			Msg("Switch to using user's active Stripe subscription")
		stripeSubscription = activeStripeSubscription
	}

	// Migration steps

	// Step 1: create the Orb customer and set up alerts.
	// Skip if the user already has an Orb customer ID, e.g. due to re-run.
	if orbCustomerId == "" {
		orbCustomerId, err = s.createOrbCustomerAndAlerts(ctx, user)
		if err != nil {
			return err
		}
		emptyOrbSubscriptionId := ""
		if err := s.DoIfNotDryRun("Update user's orb customer ID", func() error {
			log.Info().Str("user_id", user.Id).Str("orb_customer_id", orbCustomerId).Msg("Updating user's orb customer ID")
			return s.updateUserBillingInfo(ctx, user, userTenant, nil, &orbCustomerId, &emptyOrbSubscriptionId, nil)
		}); err != nil {
			return err
		}
	} else {
		log.Info().
			Str("user_id", user.Id).
			Str("orb_customer_id", orbCustomerId).
			Msg("User already has an Orb customer ID, skipping Orb customer creation")
	}

	// Step 2: create the Orb subscription
	// Skip if the user already has an Orb subscription ID, e.g. due to re-run.

	// But if we are applying fix for multiple subscription issue, we will create the orb subscription again,
	// if the user already has an Orb subscription and the subscription is ended.
	if s.migrationConfig.ApplyFixForMultipleSubscriptionIssue && orbSubscriptionId != "" && stripeSubscription != nil {
		orbSubscription, err := s.orbClient.GetUserSubscription(ctx, orbSubscriptionId, nil)
		if err != nil {
			return fmt.Errorf("failed to get orb subscription: %w", err)
		}
		if orbSubscription.OrbStatus == "ended" &&
			(stripeSubscription.Status == stripe.SubscriptionStatusActive || stripeSubscription.Status == stripe.SubscriptionStatusTrialing) {
			// To re-create the orb subscription
			orbSubscriptionId = ""
		}
	}

	if orbSubscriptionId == "" {
		if orbSubscriptionId, err = s.createOrbSubscription(ctx, user, userTenant.Tier, stripeSubscription, orbCustomerId); err != nil {
			return err
		}
		if err := s.DoIfNotDryRun("Update user's orb subscription ID", func() error {
			log.Info().Str("user_id", user.Id).Str("orb_subscription_id", orbSubscriptionId).Msg("Updating user's orb subscription ID")
			return s.updateUserBillingInfo(ctx, user, userTenant, nil, &orbCustomerId, &orbSubscriptionId, nil)
		}); err != nil {
			return err
		}

		// At this point we already created the orb subscription, we cannot rollback to stripe
		migrationTask.billingMethodOnExit = auth_entities.BillingMethod_BILLING_METHOD_MIGRATING_TO_ORB
	} else {
		log.Info().
			Str("user_id", user.Id).
			Str("orb_subscription_id", orbSubscriptionId).
			Msg("User already has an Orb subscription ID, skipping orb subscription creation")
	}

	// Step 3: cancel Stripe subscription
	if stripeSubscription != nil && stripeSubscription.ID != "" {
		log.Info().Str("user_id", user.Id).Str("subscription_id", stripeSubscription.ID).Msg("Cancelling user's Stripe subscription")
		if err := s.DoIfNotDryRun("Cancel Stripe subscription", func() error {
			return s.stripeClient.CancelSubscription(stripeSubscription.ID)
		}); err != nil {
			log.Error().Err(err).Str("user_id", user.Id).Msg("Failed to cancel user's Stripe subscription")

			// Handle 404 resource_missing error, normally it means the subscription is already cancelled
			if stripeErr, ok := err.(*stripe.Error); ok {
				if stripeErr.Code != stripe.ErrorCodeResourceMissing {
					return err
				}
				log.Info().
					Str("subscription_id", stripeSubscription.ID).
					Msg("Got resource_missing error from CancelSubscription. The subscription is already cancelled.")
			}
		}
	}

	// We have completed all steps successfully, set the billing method to ORB
	migrationTask.billingMethodOnExit = auth_entities.BillingMethod_BILLING_METHOD_ORB

	return nil
}

// createOrbCustomerAndAlerts creates an Orb customer and sets up alerts for the user
func (s *StripeToOrbMigrationJob) createStripeCustomer(ctx context.Context, user *auth_entities.User) (stripeCustomerId string, err error) {
	// create Stripe Customer
	params := &stripe.CustomerParams{
		Email: stripe.String(user.Email),
		Metadata: map[string]string{
			"augment_user_id": user.Id,
		},
		Params: stripe.Params{
			IdempotencyKey: &user.Id,
		},
	}
	log.Info().Str("user_id", user.Id).Msg("Creating Stripe customer")
	s.localLog.Info().Str("user_id", user.Id).Interface("stripe_customer", params).Msg("Creating Stripe customer")
	if err := s.DoIfNotDryRun("Create Stripe customer", func() error {
		stripeCustomer, err := s.stripeClient.CreateCustomer(params)
		if err != nil {
			return err
		}
		stripeCustomerId = stripeCustomer.ID
		return nil
	}); err != nil {
		log.Error().Err(err).Str("user_id", user.Id).Msg("Failed to create Stripe customer")
		return "", err
	}

	return stripeCustomerId, nil
}

// createOrbCustomerAndAlerts creates an Orb customer and sets up alerts for the user
func (s *StripeToOrbMigrationJob) createOrbCustomerAndAlerts(ctx context.Context, user *auth_entities.User) (orbCustomerId string, err error) {
	// create Orb Customer
	orbCustomer := orb.OrbCustomer{
		Email:              user.Email,
		Name:               user.Email,
		StripeID:           user.StripeCustomerId,
		Metadata:           map[string]string{"augment_user_id": user.Id},
		Timezone:           "UTC",
		ExternalCustomerID: nil, // nil for now
	}

	log.Info().Str("user_id", user.Id).Msg("Creating orb customer")
	s.localLog.Info().Str("user_id", user.Id).Interface("orb_customer", orbCustomer).Msg("Creating orb customer")
	if err := s.DoIfNotDryRun("Create Orb customer", func() error {
		createCustomerIdempotencyKey := fmt.Sprintf("%s-migrate-cus", user.Id)
		orbIdRet, err := s.orbClient.CreateCustomer(ctx, orbCustomer, true, &createCustomerIdempotencyKey)
		orbCustomerId = orbIdRet
		return err
	}); err != nil {
		log.Error().Err(err).Str("user_id", user.Id).Msg("Failed to create Orb customer")
		return "", err
	}

	if !s.migrationConfig.NotDryRun {
		fakeOrbCustomerId := "orb_" + user.Id
		orbCustomerId = fakeOrbCustomerId
	}

	// Set up alerts for the customer
	log.Info().Str("user_id", user.Id).Msg("Adding alerts for orb customer")
	if err := s.DoIfNotDryRun("Add alerts for customer", func() error {
		return s.orbClient.AddAlertsForCustomer(ctx, orbCustomerId, "usermessages")
	}); err != nil {
		log.Error().Err(err).Str("user_id", user.Id).Msg("Failed to add alerts for orb customer")
		return "", err
	}

	return orbCustomerId, nil
}

func getValidSubscriptionEndedAt(stripeSubscriptionEndedAt int64) int64 {
	// When creating an already ended Orb subscription, the end date cannot be more than 95 days ago.
	// If Stripe subscription end date is within this range, we use it, otherwise we set end date to current time.
	if stripeSubscriptionEndedAt < time.Now().Unix() && stripeSubscriptionEndedAt > time.Now().AddDate(0, 0, -90).Unix() {
		return stripeSubscriptionEndedAt
	}
	return time.Now().Unix()
}

// createOrbSubscription creates an Orb subscription for the user
func (s *StripeToOrbMigrationJob) createOrbSubscription(
	ctx context.Context,
	user *auth_entities.User,
	tenantTier tw_pb.TenantTier,
	stripeSubscription *stripe.Subscription,
	orbCustomerId string,
) (orbSubscriptionId string, err error) {
	orbSubscription := orb.OrbSubscription{
		CustomerOrbID: orbCustomerId,
	}

	var endSubscriptionAt int64
	if stripeSubscription.Status != stripe.SubscriptionStatusActive &&
		stripeSubscription.Status != stripe.SubscriptionStatusTrialing {
		// user is no longer active or trialing, set a valid end date for their Orb subscription
		endSubscriptionAt = getValidSubscriptionEndedAt(stripeSubscription.EndedAt)
	} else {
		// user is active or trialing, but may have a cancellation date set in future
		if stripeSubscription.CancelAt != 0 {
			endSubscriptionAt = stripeSubscription.CancelAt
		} else if stripeSubscription.CancelAtPeriodEnd {
			endSubscriptionAt = stripeSubscription.CurrentPeriodEnd
		}
	}

	switch tenantTier {
	case tw_pb.TenantTier_COMMUNITY:
		orbSubscription.ExternalPlanID = s.orbConfig.CommunityPlanID
		if stripeSubscription.Status == stripe.SubscriptionStatusActive {
			// user is active, put them on the community plan, align the start date with the current period on Stripe
			currentPeriodStart := time.Unix(stripeSubscription.CurrentPeriodStart, 0)
			orbSubscription.StartDate = &currentPeriodStart
		} else {
			// user is no longer active, put them to a community plan that starts and ends on the same day in the past
			endDate := time.Unix(endSubscriptionAt, 0)
			orbSubscription.StartDate = &endDate
			log.Info().
				Str("user_id", user.Id).
				Str("stripe_subscription_id", stripeSubscription.ID).
				Str("subscription_status", string(stripeSubscription.Status)).
				Msg("User's Stripe subscription is not in active or trialing state. Creating an ended Orb subscription for them.")
		}

	case tw_pb.TenantTier_PROFESSIONAL:
		// Special handling for family and friends users. Give them an active dev plan for free for 6 months.
		if s.migrationConfig.ApplyFriendsAndFamilyAdjustments {
			orbSubscription.ExternalPlanID = s.orbConfig.ProfessionalPlanID
			startDate := time.Now()
			orbSubscription.StartDate = &startDate
			orbSubscription.PlanVersionNumber = &s.orbConfig.DeveloperPlanForStripeUsers.VersionNumber
			orbSubscription.Adjustments = []orb.OrbAdjustment{
				{
					PriceIDs: []string{s.orbConfig.DeveloperPlanForStripeUsers.SeatsPriceID},
					Amount:   s.orbConfig.StripeProfessionalPlanPricePerSeat,
					EndDate:  startDate.AddDate(0, 6, 0).Truncate(24 * time.Hour),
				},
			}
			break
		}

		// user is no longer active, put them to a trial plan that starts and ends on the same day in the past
		if stripeSubscription.Status != stripe.SubscriptionStatusActive &&
			stripeSubscription.Status != stripe.SubscriptionStatusTrialing {
			orbSubscription.ExternalPlanID = s.orbConfig.TrialPlanID
			endDate := time.Unix(endSubscriptionAt, 0)
			orbSubscription.StartDate = &endDate
			log.Info().
				Str("user_id", user.Id).
				Str("stripe_subscription_id", stripeSubscription.ID).
				Str("subscription_status", string(stripeSubscription.Status)).
				Msg("User's Stripe subscription is not in active or trialing state. Creating an ended Orb subscription for them.")
			break
		}

		// Handle active or trialing users
		hasPaymentMethod := stripeSubscription.DefaultPaymentMethod != nil ||
			(stripeSubscription.Customer != nil &&
				stripeSubscription.Customer.InvoiceSettings != nil &&
				stripeSubscription.Customer.InvoiceSettings.DefaultPaymentMethod != nil)
		if !hasPaymentMethod && stripeSubscription.Customer != nil {
			hasPaymentMethod, _ = s.stripeClient.HasPaymentMethod(stripeSubscription.Customer.ID)
		}

		if stripeSubscription.Status == stripe.SubscriptionStatusTrialing && !hasPaymentMethod {
			// user is on trial without payment method, put them on the trial plan starting immediately,
			// and cancel user's Orb subscription at the Stripe subscription's trial end
			orbSubscription.ExternalPlanID = s.orbConfig.TrialPlanID
			if endSubscriptionAt == 0 || stripeSubscription.TrialEnd < endSubscriptionAt {
				endSubscriptionAt = stripeSubscription.TrialEnd
			}
		} else if stripeSubscription.Status == stripe.SubscriptionStatusActive ||
			(stripeSubscription.Status == stripe.SubscriptionStatusTrialing && hasPaymentMethod) {
			orbSubscription.ExternalPlanID = s.orbConfig.ProfessionalPlanID

			currentPeriodStart := time.Unix(stripeSubscription.CurrentPeriodStart, 0)
			currentPeriodEnd := time.Unix(stripeSubscription.CurrentPeriodEnd, 0)
			// For user's on trial with payment method added, we fake a "current period" based on their trial end date
			if stripeSubscription.Status == stripe.SubscriptionStatusTrialing {
				currentPeriodEnd = time.Unix(stripeSubscription.TrialEnd, 0)
				currentPeriodStart = time.Unix(stripeSubscription.TrialEnd, 0).AddDate(0, -1, 0)
			}

			// If currentPeriodEnd is too close to the current time (within 5 minutes), we fail the migration for the user and
			// retry later. This is to prevent risk of double charge due to: by the time we cancel their Stripe subscription
			// (after creating the Orb subscription here), they may already be charged on Stripe for the next period.
			if currentPeriodEnd.Sub(time.Now()) < 5*time.Minute {
				return "", fmt.Errorf("current period end is too close to current time. Retry later.")
			}

			// If user is on active subscription, and the current period start was within 2 hours to the current time, we fail the migration
			// for the user and retry later. This is to prevent risk of their Stripe last invoice hasn't been finalized. Stripe's invoice
			// will start in the draft state and normally takes 1 hour to be finalized.
			if stripeSubscription.Status == stripe.SubscriptionStatusActive && time.Now().Sub(currentPeriodStart) < 120*time.Minute {
				return "", fmt.Errorf("current period start is too close to current time. Retry later.")
			}

			orbSubscription.StartDate = &currentPeriodStart

			// For existing active Stripe users, use the lower plan version that has lower price per seat ($30)
			orbSubscription.PlanVersionNumber = &s.orbConfig.DeveloperPlanForStripeUsers.VersionNumber

			// For the adjustment to work, must floor the date to day start of CurrentPeriodEnd, see:
			// https://augment-wic8570.slack.com/archives/C08JUPVU6MD/p1744758434443149?thread_ts=1744743858.238459&cid=C08JUPVU6MD
			adjustmentEndDate := currentPeriodEnd.Truncate(24 * time.Hour)

			orbSubscription.Adjustments = []orb.OrbAdjustment{
				{
					PriceIDs: []string{s.orbConfig.DeveloperPlanForStripeUsers.SeatsPriceID},
					Amount:   s.orbConfig.StripeProfessionalPlanPricePerSeat, // user always have 1 seat
					EndDate:  adjustmentEndDate,
				},
			}
		}

	default:
		return "", fmt.Errorf("user tenant has invalid tier: %s", tenantTier.String())
	}

	if endSubscriptionAt > 0 && !s.migrationConfig.ApplyFriendsAndFamilyAdjustments {
		endDate := time.Unix(endSubscriptionAt, 0)
		orbSubscription.EndDate = &endDate
		log.Info().
			Str("user_id", user.Id).
			Str("end_date", endDate.String()).
			Msg("Setting end date for user's Orb subscription")
	}

	log.Info().Str("user_id", user.Id).Interface("orb_subscription", orbSubscription).Msg("Creating orb subscription for user")
	s.localLog.Info().Str("user_id", user.Id).Interface("orb_subscription", orbSubscription).Msg("Creating orb subscription for user")
	if err := s.DoIfNotDryRun("Create Orb subscription", func() error {
		createSubscriptionIdempotencyKey := fmt.Sprintf("%s-migrate-sub", user.Id)

		// use a different idempotency key when fixing multiple subscription issue, since the regular idempotency key has already been used
		if s.migrationConfig.ApplyFixForMultipleSubscriptionIssue {
			createSubscriptionIdempotencyKey = fmt.Sprintf("%s-migrate-sub-fix", user.Id)
		}

		orbSubscriptionId, err = s.orbClient.CreateSubscription(ctx, orbSubscription, &createSubscriptionIdempotencyKey)
		return err
	}); err != nil {
		log.Error().Err(err).Str("user_id", user.Id).Interface("orb_subscription", orbSubscription).Msg("Failed to create Orb subscription")
		return "", err
	}

	return orbSubscriptionId, nil
}

// UpdateUserBillingInfo updates a user's billing information
func (s *StripeToOrbMigrationJob) updateUserBillingInfo(
	ctx context.Context,
	user *auth_entities.User,
	tenant *tw_pb.Tenant,
	billingMethod *auth_entities.BillingMethod,
	orbCustomerId *string,
	orbSubscriptionId *string,
	stripeCustomerId *string,
) error {
	token, err := s.tokenClient.GetSignedTokenForServiceWithNamespace(ctx, tenant.Id, tenant.ShardNamespace, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_RW})
	if err != nil {
		return fmt.Errorf("failed to get signed token: %w", err)
	}

	sessionId := requestcontext.NewRandomRequestSessionId()
	requestCtx := requestcontext.New(requestcontext.NewRandomRequestId(), sessionId, "billing-central", token)

	if err := s.authClient.UpdateUserBillingInfo(ctx, requestCtx, user.Id, tenant.Id, billingMethod, orbCustomerId, orbSubscriptionId, stripeCustomerId); err != nil {
		log.Error().
			Str("user_id", user.Id).
			Interface("billing_method", billingMethod).
			Interface("orb_customer_id", orbCustomerId).
			Interface("orb_subscription_id", orbSubscriptionId).
			Msg("Failed to update user's billing info")
		return err
	}

	return nil
}

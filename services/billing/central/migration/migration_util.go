package migration

import (
	"context"
	"fmt"
	"strings"

	"github.com/rs/zerolog/log"

	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
)

// OrbConfig holds configuration for Orb plans and pricing
type OrbConfig struct {
	CommunityPlanID                    string  `json:"community_plan_id"`
	TrialPlanID                        string  `json:"trial_plan_id"`
	ProfessionalPlanID                 string  `json:"professional_plan_id"`
	StripeProfessionalPlanPricePerSeat float64 `json:"stripe_professional_plan_price_per_seat"`
	DeveloperPlanForStripeUsers        struct {
		VersionNumber int64  `json:"version_number"`
		SeatsPriceID  string `json:"seats_price_id"`
	} `json:"developer_plan_for_stripe_users"`
}

// GetUser retrieves a user from the auth service
func GetUser(
	ctx context.Context,
	authClient authclient.AuthClient,
	tokenClient tokenexchangeclient.TokenExchangeClient,
	userID string,
	tenantID string,
	shardNamespace string,
) (*auth_entities.User, error) {
	// Get a token for the auth service
	token, err := tokenClient.GetSignedTokenForServiceWithNamespace(ctx, tenantID, shardNamespace, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_R})
	if err != nil {
		return nil, fmt.Errorf("failed to get signed token: %w", err)
	}

	// Create a request context
	sessionId := requestcontext.NewRandomRequestSessionId()
	requestCtx := requestcontext.New(requestcontext.NewRandomRequestId(), sessionId, "billing-central", token)

	// Get the user from the auth service
	user, err := authClient.GetUser(ctx, requestCtx, userID, &tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user, nil
}

// CheckUserTenants checks if a user's tenant is valid for the migration.
// Returns a valid user tenant if found, a skip reason if the user should be skipped.
func CheckUserTenants(
	ctx context.Context,
	tenantCache tenantwatcherclient.TenantCache,
	userID string,
	tenantIDs []string,
) (userTenant *tw_pb.Tenant, skipReason string, err error) {
	for _, tenantID := range tenantIDs {
		tenant, err := tenantCache.GetTenant(tenantID)
		if err != nil {
			if err == tenantwatcherclient.ErrTenantNotFound {
				// some deleted tenants will return ErrTenantNotFound, ignore deleted tenants
				continue
			}

			return nil, "", fmt.Errorf("failed to get tenant: %w", err)
		}

		// some deleted tenants will return a tenant with DeletedAt set, ignore deleted tenants
		if tenant.DeletedAt != "" {
			continue
		}

		if tenantutil.IsSelfServeTeamTenant(tenant) {
			log.Info().Str("tenant_id", tenant.Id).Str("user_id", userID).Msg("User is in a self serve team tenant, skipping")
			return nil, "User is in a self serve team tenant", nil
		}
		if tenantutil.IsEnterpriseTenant(tenant) {
			log.Info().Str("tenant_id", tenant.Id).Str("user_id", userID).Msg("User is in an enterprise tenant, skipping")
			return nil, "User is in an enterprise tenant", nil
		}

		if userTenant == nil {
			userTenant = tenant
		} else if userTenant.Tier != tenant.Tier {
			log.Error().
				Str("user_id", userID).
				Interface("tenants", tenantIDs).
				Str("tier1", userTenant.Tier.String()).
				Str("tier2", tenant.Tier.String()).
				Msg("User is in multiple tenants with different tiers. Something is wrong.")
			return nil, "User is in multiple tenants with different tiers. Something is wrong.", nil
		}
	}

	if userTenant == nil || userTenant.Tier == tw_pb.TenantTier_TENANT_TIER_UNKNOWN {
		log.Error().Str("user_id", userID).Msg("User is not in a valid tenant tier. Something is wrong.")
		return nil, "User is not in a valid tenant tier. Something is wrong.", nil
	}

	// User is in a valid tenant tier and should be migrated
	return userTenant, "", nil
}

func splitTenantIDAndShardNamespace(tenantIDAndShardNamespace string) (tenantID string, shardNamespace string) {
	tenantID, shardNamespace, _ = strings.Cut(tenantIDAndShardNamespace, "|")
	return
}

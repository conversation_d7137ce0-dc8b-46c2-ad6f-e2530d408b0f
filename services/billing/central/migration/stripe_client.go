package migration

import (
	"fmt"

	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/customer"
	"github.com/stripe/stripe-go/v80/subscription"
)

// StripeClient defines the interface for Stripe operations used by the migration job
type StripeClient interface {
	CancelSubscription(subscriptionID string) error
	GetSubscription(subscriptionID string) (*stripe.Subscription, error)
	HasPaymentMethod(customerID string) (bool, error)
	ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error)
	CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error)
}

// DefaultStripeClient implements the StripeClient interface using the Stripe API

type DefaultStripeClient struct{}

func (c *DefaultStripeClient) CancelSubscription(subscriptionID string) error {
	_, err := subscription.Cancel(subscriptionID, nil)
	return err
}

func (c *DefaultStripeClient) GetSubscription(subscriptionID string) (*stripe.Subscription, error) {
	return subscription.Get(subscriptionID, nil)
}

// HasPaymentMethod checks if a customer has a valid payment method
func (c *DefaultStripeClient) HasPaymentMethod(customerID string) (bool, error) {
	params := &stripe.CustomerListPaymentMethodsParams{
		Customer: stripe.String(customerID),
	}

	// Only need to check if there's at least one payment method
	params.Limit = stripe.Int64(1)
	i := customer.ListPaymentMethods(params)

	// Check if there's at least one payment method
	return i.Next(), i.Err()
}

func (c *DefaultStripeClient) ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error) {
	params := &stripe.SubscriptionListParams{
		Customer: stripe.String(customerID),
	}

	// Check if includeCanceled parameter was provided and is true
	if len(includeCanceled) > 0 && includeCanceled[0] {
		params.Status = stripe.String("all")
	}

	iter := subscription.List(params)

	var subs []*stripe.Subscription
	for iter.Next() {
		subs = append(subs, iter.Subscription())
	}
	return subs, iter.Err()
}

// CreateCustomer creates a new Stripe customer
func (c *DefaultStripeClient) CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error) {
	return customer.New(params)
}

// MockStripeClient implements a mock for testing with real state
type MockStripeClient struct {
	subscriptions map[string]*stripe.Subscription // Map of subscription ID to subscription
}

// NewMockStripeClient creates a new mock client
func NewMockStripeClient() *MockStripeClient {
	return &MockStripeClient{
		subscriptions: make(map[string]*stripe.Subscription),
	}
}

// AddSubscription adds a subscription to the mock's state
func (m *MockStripeClient) AddSubscription(customerID string, userID string, subID string, priceID string) {
	sub := &stripe.Subscription{
		ID: subID,
		Items: &stripe.SubscriptionItemList{
			Data: []*stripe.SubscriptionItem{
				{
					Price: &stripe.Price{
						ID: priceID,
					},
				},
			},
		},
		Metadata: map[string]string{
			"augment_user_id": userID,
		},
		Customer: &stripe.Customer{
			ID: customerID,
		},
	}
	m.subscriptions[subID] = sub
}

func (m *MockStripeClient) GetSubscription(subscriptionID string) (*stripe.Subscription, error) {
	sub, exists := m.subscriptions[subscriptionID]
	if !exists {
		return nil, fmt.Errorf("subscription not found")
	}
	return sub, nil
}

// CancelSubscription cancels a subscription
func (m *MockStripeClient) CancelSubscription(subscriptionID string) error {
	// Remove from subscriptions map
	delete(m.subscriptions, subscriptionID)
	return nil
}

func (m *MockStripeClient) HasPaymentMethod(customerID string) (bool, error) {
	return false, nil
}

func (m *MockStripeClient) ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error) {
	var subs []*stripe.Subscription
	for _, sub := range m.subscriptions {
		if sub.Customer.ID == customerID {
			subs = append(subs, sub)
		}
	}
	return subs, nil
}

func (m *MockStripeClient) CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error) {
	return &stripe.Customer{
		ID:    "cus_mock_" + *params.Email,
		Email: *params.Email,
	}, nil
}

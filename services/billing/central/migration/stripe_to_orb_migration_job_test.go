package migration

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"

	"github.com/augmentcode/augment/base/go/secretstring"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/integrations/orb"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	twclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantpb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

type MockTenantCache struct {
	twclient.TenantCache
	mock.Mock
	tenants map[string]*tenantpb.Tenant
}

func (m *MockTenantCache) GetTenant(tenantID string) (*tenantpb.Tenant, error) {
	if m.tenants[tenantID] != nil {
		return m.tenants[tenantID], nil
	}
	return nil, fmt.Errorf("tenant not found")
}

// MockAuthClient mocks the AuthClient interface for testing
type MockAuthClient struct {
	authclient.AuthClient
	mock.Mock
	users map[string]*auth_entities.User
}

// GetUser mocks the GetUser method
func (m *MockAuthClient) GetUser(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID *string) (*auth_entities.User, error) {
	if m.users[userId] != nil {
		return m.users[userId], nil
	}
	return nil, fmt.Errorf("user not found")
}

// UpdateUserBillingInfo mocks the UpdateUserBillingInfo method
func (m *MockAuthClient) UpdateUserBillingInfo(ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, billingMethod *auth_entities.BillingMethod, orbCustomerID *string, orbSubscriptionID *string, stripeCustomerID *string) error {
	if m.users[userId] != nil {
		if billingMethod != nil {
			m.users[userId].BillingMethod = *billingMethod
		}
		if orbCustomerID != nil {
			m.users[userId].OrbCustomerId = *orbCustomerID
		}
		if orbSubscriptionID != nil {
			m.users[userId].OrbSubscriptionId = *orbSubscriptionID
		}
		if stripeCustomerID != nil {
			m.users[userId].StripeCustomerId = *stripeCustomerID
		}
	}
	return nil
}

type MockTokenExchangeClient struct {
	tokenexchangeclient.TokenExchangeClient
	mock.Mock
}

func (m *MockTokenExchangeClient) GetSignedTokenForServiceWithNamespace(ctx context.Context, tenantID string, shardNamespace string, scopes []tokenexchangepb.Scope) (secretstring.SecretString, error) {
	return secretstring.New("mock_token"), nil
}

// TestDoMigration tests the DoMigration method
func TestDoMigration(t *testing.T) {
	// Test cases
	testCases := []struct {
		name                 string
		allowlistedUsers     map[string]string
		expectedError        bool
		expectedSuccessUsers int    // Expected number of successfully migrated users
		userTypes            string // Description of user types being tested
	}{
		{
			name: "All users allowlist",
			allowlistedUsers: map[string]string{
				"community-user-123":         "community-tenant|some-shard",
				"pro-trial-user-456":         "pro-tenant|some-shard",
				"pro-trial-with-payment-555": "pro-tenant|some-shard",
				"pro-paid-user-777":          "pro-tenant|some-shard",
				"pro-end-of-trial-user-888":  "pro-tenant|some-shard",
				"pro-paid-cancel-user-999":   "pro-tenant|some-shard",
				"no-stripe-cus-id-001":       "pro-tenant|some-shard",
				"stripe-cus-no-sub-id-002":   "pro-tenant|some-shard",
				"team-user-789":              "team-123",
			},
			expectedError:        false,
			expectedSuccessUsers: 8, // All users except the one in self-serve team is skipped, others migrated
			userTypes:            "All user types",
		},
		{
			name:                 "Empty allowlist",
			allowlistedUsers:     nil,
			expectedError:        false,
			expectedSuccessUsers: 0,
			userTypes:            "No users",
		},
		{
			name: "Community user allowlist",
			allowlistedUsers: map[string]string{
				"community-user-123": "community-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "Community user",
		},
		{
			name: "Pro trial user without payment method allowlist",
			allowlistedUsers: map[string]string{
				"pro-trial-user-456": "pro-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "Pro trial user without payment method",
		},
		{
			name: "Pro trial user with payment method allowlist",
			allowlistedUsers: map[string]string{
				"pro-trial-with-payment-555": "pro-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "Pro trial user with payment method",
		},
		{
			name: "Pro paid user allowlist",
			allowlistedUsers: map[string]string{
				"pro-paid-user-777": "pro-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "Pro paid user",
		},
		{
			name: "Pro end-of-trial user allowlist",
			allowlistedUsers: map[string]string{
				"pro-end-of-trial-user-888": "pro-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "Pro end-of-trial user",
		},
		{
			name: "Pro paid user with cancel at period end",
			allowlistedUsers: map[string]string{
				"pro-paid-cancel-user-999": "pro-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "Pro paid user with cancel at period end",
		},
		{
			name: "User without Stripe Customer ID",
			allowlistedUsers: map[string]string{
				"no-stripe-cus-id-001": "pro-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "User without Stripe Customer ID",
		},
		{
			name: "User with Stripe Customer ID but no Stripe Subscription ID",
			allowlistedUsers: map[string]string{
				"stripe-cus-no-sub-id-002": "pro-tenant|some-shard",
			},
			expectedError:        false,
			expectedSuccessUsers: 1,
			userTypes:            "User with Stripe Customer ID but no Stripe Subscription ID",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx := context.Background()

			// Create test users
			communitySubscriptionId := "sub_community_123"
			proTrialSubscriptionId := "sub_pro_trial_456"
			proPaidSubscriptionId := "sub_pro_paid_777"
			proEndOfTrialSubscriptionId := "sub_pro_end_of_trial_888"
			proPaidCancelingSubscriptionId := "sub_pro_paid_cancel_999"
			proTrialWithPaymentSubscriptionId := "sub_pro_trial_with_payment_555"
			mockUsers := []*auth_entities.User{
				{
					Id:               "community-user-123",
					Email:            "<EMAIL>",
					Tenants:          []string{"community-tenant"},
					StripeCustomerId: "cus_user123",
					SubscriptionId:   &communitySubscriptionId,
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
				},
				{
					Id:               "pro-trial-user-456",
					Email:            "<EMAIL>",
					Tenants:          []string{"pro-tenant"},
					StripeCustomerId: "cus_user456",
					SubscriptionId:   &proTrialSubscriptionId,
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
				},
				{
					Id:               "pro-paid-user-777",
					Email:            "<EMAIL>",
					Tenants:          []string{"pro-tenant"},
					StripeCustomerId: "cus_user777",
					SubscriptionId:   &proPaidSubscriptionId,
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
				},
				{
					Id:               "pro-paid-cancel-user-999",
					Email:            "<EMAIL>",
					Tenants:          []string{"pro-tenant"},
					StripeCustomerId: "cus_user999",
					SubscriptionId:   &proPaidCancelingSubscriptionId,
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_STRIPE,
				},
				{
					Id:               "pro-end-of-trial-user-888",
					Email:            "<EMAIL>",
					Tenants:          []string{"pro-tenant"},
					StripeCustomerId: "cus_user888",
					SubscriptionId:   &proEndOfTrialSubscriptionId,
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
				},
				{
					Id:      "team-user-789",
					Email:   "<EMAIL>",
					Tenants: []string{"team-123"},
				},
				{
					Id:               "pro-trial-with-payment-555",
					Email:            "<EMAIL>",
					Tenants:          []string{"pro-tenant"},
					StripeCustomerId: "cus_user555",
					SubscriptionId:   &proTrialWithPaymentSubscriptionId,
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
				},
				{
					Id:               "no-stripe-cus-id-001",
					Email:            "<EMAIL>",
					Tenants:          []string{"pro-tenant"},
					StripeCustomerId: "", // No Stripe Customer ID
					SubscriptionId:   nil,
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
				},
				{
					Id:               "stripe-cus-no-sub-id-002",
					Email:            "<EMAIL>",
					Tenants:          []string{"pro-tenant"},
					StripeCustomerId: "cus_no_sub_002", // Has Stripe Customer ID
					SubscriptionId:   nil,              // No Stripe Subscription ID
					BillingMethod:    auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
				},
			}

			// Create test tenants
			testTenants := []*tenantpb.Tenant{
				{
					Id:   "team-123",
					Name: "Team 123",
					Config: &tenantpb.Config{
						Configs: map[string]string{
							"is_self_serve_team": "true",
						},
					},
					Tier: tw_pb.TenantTier_PROFESSIONAL,
				},
				{
					Id:   "team-456",
					Name: "Team 456",
					Config: &tenantpb.Config{
						Configs: map[string]string{
							"is_self_serve_team": "true",
						},
					},
					Tier: tw_pb.TenantTier_PROFESSIONAL,
				},
				{
					Id:     "pro-tenant",
					Name:   "Pro Tenant",
					Config: &tenantpb.Config{},
					Tier:   tw_pb.TenantTier_PROFESSIONAL,
				},
				{
					Id:     "community-tenant",
					Name:   "Community Tenant",
					Config: &tenantpb.Config{},
					Tier:   tw_pb.TenantTier_COMMUNITY,
				},
			}

			mockTenantCache := &MockTenantCache{
				tenants: make(map[string]*tenantpb.Tenant),
			}
			for _, tenant := range testTenants {
				mockTenantCache.tenants[tenant.Id] = tenant
			}

			mockAuthClient := &MockAuthClient{
				users: make(map[string]*auth_entities.User),
			}
			for _, user := range mockUsers {
				mockAuthClient.users[user.Id] = user
			}

			stripeClient := NewMockStripeClient()

			// Setup mock subscriptions with proper data for each test user
			// Community user subscription
			stripeClient.subscriptions[communitySubscriptionId] = &stripe.Subscription{
				ID:                 communitySubscriptionId,
				Status:             stripe.SubscriptionStatusActive,
				CurrentPeriodStart: time.Now().Add(-15 * 24 * time.Hour).Unix(),
				CurrentPeriodEnd:   time.Now().Add(15 * 24 * time.Hour).Unix(),
				Customer: &stripe.Customer{
					ID: "cus_user123",
				},
			}

			// Pro trial user subscription without payment method
			stripeClient.subscriptions[proTrialSubscriptionId] = &stripe.Subscription{
				ID:     proTrialSubscriptionId,
				Status: stripe.SubscriptionStatusTrialing,
				Customer: &stripe.Customer{
					ID: "cus_user456",
					// No InvoiceSettings or DefaultPaymentMethod
				},
				TrialEnd: time.Now().Add(10 * 24 * time.Hour).Unix(),
			}

			// Pro paid user subscription
			stripeClient.subscriptions[proPaidSubscriptionId] = &stripe.Subscription{
				ID:     proPaidSubscriptionId,
				Status: stripe.SubscriptionStatusActive,
				Customer: &stripe.Customer{
					ID: "cus_user777",
				},
				CurrentPeriodStart: time.Now().Add(-15 * 24 * time.Hour).Unix(),
				CurrentPeriodEnd:   time.Now().Add(15 * 24 * time.Hour).Unix(),
			}

			// Pro end of trial user subscription
			stripeClient.subscriptions[proEndOfTrialSubscriptionId] = &stripe.Subscription{
				ID:     proEndOfTrialSubscriptionId,
				Status: stripe.SubscriptionStatusCanceled,
				Customer: &stripe.Customer{
					ID: "cus_user888",
				},
				TrialEnd: time.Now().Add(-7 * 24 * time.Hour).Unix(),
			}

			// Pro paid user subscription with cancel at period end
			stripeClient.subscriptions[proPaidCancelingSubscriptionId] = &stripe.Subscription{
				ID:                 proPaidCancelingSubscriptionId,
				Status:             stripe.SubscriptionStatusActive,
				CancelAtPeriodEnd:  true,
				CurrentPeriodStart: time.Now().Add(-15 * 24 * time.Hour).Unix(),
				CurrentPeriodEnd:   time.Now().Add(15 * 24 * time.Hour).Unix(),
				Customer: &stripe.Customer{
					ID: "cus_user999",
				},
			}

			// Pro trial user subscription with payment method
			stripeClient.subscriptions[proTrialWithPaymentSubscriptionId] = &stripe.Subscription{
				ID:     proTrialWithPaymentSubscriptionId,
				Status: stripe.SubscriptionStatusTrialing,
				Customer: &stripe.Customer{
					ID: "cus_user555",
					InvoiceSettings: &stripe.CustomerInvoiceSettings{
						DefaultPaymentMethod: &stripe.PaymentMethod{
							ID: "pm_123456",
						},
					},
				},
				TrialEnd: time.Now().Add(10 * 24 * time.Hour).Unix(),
			}

			orbClient := new(orb.MockOrbClient)

			// Setup expectations for Orb client calls with parameter assertions
			orbClient.On("CreateCustomer", mock.Anything, mock.MatchedBy(func(customer orb.OrbCustomer) bool {
				validEmails := map[string]bool{
					"<EMAIL>": true,
					"<EMAIL>": true,
					"<EMAIL>": true,
					"<EMAIL>": true,
					"<EMAIL>": true,
					"<EMAIL>": true,
					"<EMAIL>": true, // no-stripe-cus-id-001
					"<EMAIL>": true, // stripe-cus-no-sub-id-002
				}
				validStripeIDs := map[string]bool{
					"cus_user123":                  true,
					"cus_user456":                  true,
					"cus_user555":                  true,
					"cus_user777":                  true,
					"cus_user888":                  true,
					"cus_user999":                  true,
					"<EMAIL>": true, // For no-stripe-cus-id-001
					"cus_no_sub_002":               true, // stripe-cus-no-sub-id-002
				}
				validUserIDs := map[string]bool{
					"community-user-123":         true,
					"pro-trial-user-456":         true,
					"pro-trial-with-payment-555": true,
					"pro-paid-user-777":          true,
					"pro-paid-cancel-user-999":   true,
					"pro-end-of-trial-user-888":  true,
					"no-stripe-cus-id-001":       true,
					"stripe-cus-no-sub-id-002":   true,
				}
				// Verify customer data has required fields and correct format
				return validEmails[customer.Email] &&
					validEmails[customer.Name] &&
					validStripeIDs[customer.StripeID] &&
					validUserIDs[customer.Metadata["augment_user_id"]]
			}), true, mock.Anything).Return("orb_cust_id", nil)

			switch tc.name {
			case "Community user allowlist":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_community_plan"
				}), mock.Anything).Return("orb_sub_id", nil)
			case "Pro trial user without payment method allowlist":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_trial_plan" &&
						len(subscription.Adjustments) == 0 &&
						subscription.StartDate == nil &&
						subscription.EndDate != nil
				}), mock.Anything).Return("orb_sub_id", nil)
			case "Pro trial user with payment method allowlist":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_developer_plan" &&
						len(subscription.Adjustments) == 1 &&
						*subscription.PlanVersionNumber == 6 &&
						subscription.Adjustments[0].PriceIDs[0] == "orb_professional_plan_price_id_seats" &&
						subscription.Adjustments[0].Amount == 30.0 &&
						!subscription.Adjustments[0].EndDate.IsZero() &&
						subscription.StartDate != nil &&
						subscription.EndDate == nil
				}), mock.Anything).Return("orb_sub_id", nil)
			case "Pro paid user allowlist":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_developer_plan" &&
						len(subscription.Adjustments) == 1 &&
						*subscription.PlanVersionNumber == 6 &&
						subscription.Adjustments[0].PriceIDs[0] == "orb_professional_plan_price_id_seats" &&
						subscription.Adjustments[0].Amount == 30.0 &&
						!subscription.Adjustments[0].EndDate.IsZero() &&
						subscription.StartDate != nil &&
						subscription.EndDate == nil
				}), mock.Anything).Return("orb_sub_id", nil)
			case "Pro paid user with cancel at period end":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_developer_plan" &&
						len(subscription.Adjustments) == 1 &&
						*subscription.PlanVersionNumber == 6 &&
						subscription.Adjustments[0].PriceIDs[0] == "orb_professional_plan_price_id_seats" &&
						subscription.Adjustments[0].Amount == 30.0 &&
						!subscription.Adjustments[0].EndDate.IsZero() &&
						subscription.StartDate != nil &&
						subscription.EndDate != nil
				}), mock.Anything).Return("orb_sub_id", nil)
			case "Pro end-of-trial user allowlist":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_trial_plan" &&
						len(subscription.Adjustments) == 0 &&
						subscription.StartDate != nil &&
						subscription.EndDate != nil &&
						subscription.StartDate.Equal(*subscription.EndDate)
				}), mock.Anything).Return("orb_sub_id", nil)
			case "User without Stripe Customer ID":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_trial_plan" &&
						len(subscription.Adjustments) == 0 &&
						subscription.StartDate != nil &&
						subscription.EndDate != nil &&
						subscription.StartDate.Equal(*subscription.EndDate)
				}), mock.Anything).Return("orb_sub_id", nil)
			case "User with Stripe Customer ID but no Stripe Subscription ID":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == "orb_trial_plan" &&
						len(subscription.Adjustments) == 0 &&
						subscription.StartDate != nil &&
						subscription.EndDate != nil &&
						subscription.StartDate.Equal(*subscription.EndDate)
				}), mock.Anything).Return("orb_sub_id", nil)
			case "All users allowlist":
				orbClient.On("CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.CustomerOrbID == "orb_cust_id"
				}), mock.Anything).Return("orb_sub_id", nil)
			}

			orbClient.On("AddAlertsForCustomer", mock.Anything, "orb_cust_id", "usermessages").Return(nil)

			f, err := os.OpenFile("/tmp/migration.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0o644)
			require.NoError(t, err, "Failed to open log file")
			defer f.Close()

			// Create a job with the real DAO factory and tenant map
			job := &StripeToOrbMigrationJob{
				authClient:  mockAuthClient,
				tokenClient: &MockTokenExchangeClient{},
				tenantCache: mockTenantCache,
				orbConfig: OrbConfig{
					CommunityPlanID:    "orb_community_plan",
					TrialPlanID:        "orb_trial_plan",
					ProfessionalPlanID: "orb_developer_plan",
					DeveloperPlanForStripeUsers: struct {
						VersionNumber int64  `json:"version_number"`
						SeatsPriceID  string `json:"seats_price_id"`
					}{
						VersionNumber: 6,
						SeatsPriceID:  "orb_professional_plan_price_id_seats",
					},
					StripeProfessionalPlanPricePerSeat: 30.0,
				},
				orbClient:    orbClient,
				stripeClient: stripeClient,
				migrationConfig: &StripeToOrbMigrationConfig{
					AllowlistedUsers:    tc.allowlistedUsers,
					MaxFailureCountUser: 100,
					NotDryRun:           true,
				},
				localLog: log.Output(f),
			}

			result, err := job.DoMigration(ctx)

			if tc.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify user migration results
			assert.Equal(t, 0, result.FailedUserCount,
				"Failed user count should be 0 for %s", tc.name)
			assert.Equal(t, tc.expectedSuccessUsers, result.SucceededUserCount,
				"Succeeded user count should match expectation for %s", tc.name)

			// verify user billing method and fields
			for _, user := range mockUsers {
				updatedUser := mockAuthClient.users[user.Id]

				// Check if this user is in the allowlist
				isAllowlisted := tc.allowlistedUsers[user.Id] != ""

				if user.Id != "team-user-789" && isAllowlisted {
					assert.Equal(t, auth_entities.BillingMethod_BILLING_METHOD_ORB, updatedUser.BillingMethod)
					assert.Equal(t, "orb_cust_id", updatedUser.OrbCustomerId)
					assert.Equal(t, "orb_sub_id", updatedUser.OrbSubscriptionId)
				} else {
					assert.True(t, updatedUser.BillingMethod == auth_entities.BillingMethod_BILLING_METHOD_STRIPE ||
						updatedUser.BillingMethod == auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN,
						"User %s should have STRIPE or UNKNOWN billing method", user.Id)
					assert.Equal(t, "", updatedUser.OrbCustomerId)
					assert.Equal(t, "", updatedUser.OrbSubscriptionId)
				}
			}
		})
	}
}

// TestReadConfigFromTriggerFile tests the ReadConfigFromTriggerFile method
func TestReadConfigFromTriggerFile(t *testing.T) {
	testCases := []struct {
		name           string
		fileContent    string
		expectedConfig *StripeToOrbMigrationConfig
		expectError    bool
	}{
		{
			name: "Valid JSON with all fields",
			fileContent: `{
				"allowlisted_users": {"user1": "tenant1", "<EMAIL>": "tenant2"},
				"max_failure_count_user": 50,
				"max_success_count_user": 100
			}`,
			expectedConfig: &StripeToOrbMigrationConfig{
				AllowlistedUsers:    map[string]string{"user1": "tenant1", "<EMAIL>": "tenant2"},
				MaxFailureCountUser: 50,
			},
			expectError: false,
		},
		{
			name: "Valid JSON with partial fields",
			fileContent: `{
				"allowlisted_users": {"user1": "tenant1"},
				"max_failure_count_user": 25
			}`,
			expectedConfig: &StripeToOrbMigrationConfig{
				AllowlistedUsers:    map[string]string{"user1": "tenant1"},
				MaxFailureCountUser: 25,
			},
			expectError: false,
		},
		{
			name:        "Invalid JSON",
			fileContent: `{ "allowlisted_users": ["user1", }`,
			expectError: true,
		},
		{
			name:        "Empty file",
			fileContent: ``,
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Write test content to the temp file
			err := os.WriteFile(stripeToOrbMigrationTriggerFilePath, []byte(tc.fileContent), 0o644)
			require.NoError(t, err, "Failed to write to temp file")

			// Create a job with the temp file path
			job := &StripeToOrbMigrationJob{
				orbConfig: OrbConfig{},
			}

			// Call the function
			config, err := job.ReadConfigFromTriggerFile()

			// Check results
			if tc.expectError {
				assert.Error(t, err)
				assert.Nil(t, config)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, config)
				assert.Equal(t, tc.expectedConfig.AllowlistedUsers, config.AllowlistedUsers)
				assert.Equal(t, tc.expectedConfig.MaxFailureCountUser, config.MaxFailureCountUser)
			}
		})
	}
}

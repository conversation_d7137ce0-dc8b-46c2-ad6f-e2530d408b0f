local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local orbConfigFunc = import 'services/deploy/configs/orb.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'billing-central';
  local orbConfig = orbConfigFunc(env);

  local clientCert = certLib.createClientCert(name='%s-client-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName);
  local centralClientCert = certLib.createCentralClientCert(name='%s-central-client-certificate' % appName,
                                                            namespace=namespace,
                                                            appName=appName,
                                                            env=env,
                                                            dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace));
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local stripeSecretKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='stripe',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local orbApiKey = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='orb-api-key',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  // Configuration for the billing central service
  local config = {
    healthFile: '/tmp/health',
    promPort: 9090,
    grpcPort: 50051,
    featureFlagsSDKKeyPath: dynamicFeatureFlags.secretsFilePath,
    dynamicFeatureFlagsEndpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    clientMtlsConfig: if mtls then clientCert.config else null,
    centralClientMtlsConfig: if mtls then centralClientCert.config else null,
    serverMtlsConfig: if mtls then serverCert.config else null,
    orbAPIKeyPath: orbApiKey.filePath,
    stripeSecretKeyPath: stripeSecretKey.filePath,
    authEndpoint: '%s:50051' % endpointsLib.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    tokenExchangeEndpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    tenantWatcherEndpoint: endpointsLib.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    orbConfig: {
      // TODO have this hard coded list of plans isn't ideal, but this is only
      // for the migration anyways
      community_plan_id: std.filter(function(p) p.id == 'orb_community_plan', orbConfig.plans)[0].id,
      trial_plan_id: std.filter(function(p) p.id == 'orb_trial_plan', orbConfig.plans)[0].id,
      professional_plan_id: std.filter(function(p) p.id == 'orb_developer_plan', orbConfig.plans)[0].id,
      developer_plan_for_stripe_users: orbConfig.developer_plan_for_stripe_users[env],
      stripe_professional_plan_price_per_seat: orbConfig.stripe_professional_plan_price_per_seat,
    },
  };
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  local container = {
    name: appName,
    target: {
      name: '//services/billing/central:image',
      dst: 'billing-central',
    },
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    volumeMounts: [
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      dynamicFeatureFlags.volumeMountDef,
      orbApiKey.volumeMountDef,
      stripeSecretKey.volumeMountDef,
    ],
    resources: {
      limits: {
        cpu: 2,
        memory: '2Gi',
      },
    },
    args: [
      '--config',
      configMap.filename,
    ],
    readinessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 10,
    },
    livenessProbe: {
      exec: {
        command: [
          '/bin/sh',
          '-c',
          'cat %s' % config.healthFile,
        ],
      },
      initialDelaySeconds: 60,
      periodSeconds: 20,
    },
  };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      configMap.podVolumeDef,
      serverCert.podVolumeDef,
      clientCert.podVolumeDef,
      centralClientCert.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      orbApiKey.podVolumeDef,
      stripeSecretKey.podVolumeDef,
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: if env == 'DEV' || env == 'STAGING' then 1 else 4,
      minReadySeconds: if env == 'DEV' then 0 else 60,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    deployment,
    dynamicFeatureFlags.k8s_objects,
    serverCert.objects,
    clientCert.objects,
    centralClientCert.objects,
    services,
    orbApiKey.objects,
    stripeSecretKey.objects,
  ])

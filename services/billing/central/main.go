package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	"github.com/augmentcode/augment/services/billing/central/migration"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
)

var ServiceName = "billing-central"

// Config holds the configuration for the billing service
type Config struct {
	HealthFile                  string
	GrpcPort                    int
	PromPort                    int
	FeatureFlagsSDKKeyPath      string
	DynamicFeatureFlagsEndpoint string
	OrbAPIKeyPath               string
	AuthEndpoint                string
	TokenExchangeEndpoint       string
	CentralClientMtlsConfig     *tlsconfig.ClientConfig
	ServerMtlsConfig            *tlsconfig.ServerConfig
	TenantWatcherEndpoint       string
	StripeSecretKeyPath         string
	OrbConfig                   migration.OrbConfig
}

// loadConfig loads the configuration from a file
func loadConfig(configFile string) (*Config, error) {
	if configFile == "" {
		return nil, fmt.Errorf("config file path is required")
	}

	data, err := os.ReadFile(configFile)
	if err != nil {
		return nil, fmt.Errorf("error reading config file: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("error parsing config file: %w", err)
	}

	return &config, nil
}

// declareHealthy creates a health file to indicate the service is healthy
func declareHealthy(healthFile string) {
	if healthFile == "" {
		return
	}

	file, err := os.Create(healthFile)
	if err != nil {
		log.Error().Err(err).Msg("Error creating health file")
		return
	}
	defer file.Close()

	if _, err := file.WriteString("OK"); err != nil {
		log.Error().Err(err).Msg("Error writing to health file")
	}
}

func main() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	logging.SetupServerLogging()

	namespace := os.Getenv("POD_NAMESPACE")
	if namespace == "" {
		log.Fatal().Msg("POD_NAMESPACE environment variable must be set")
	}

	// Parse flags
	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()

	// Load config
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error loading config")
	}

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)
	wg := sync.WaitGroup{}

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Initialize feature flags
	var featureFlagHandle featureflags.FeatureFlagHandle
	if config.FeatureFlagsSDKKeyPath != "" {
		var err error
		featureFlagHandle, err = featureflags.NewFeatureFlagHandleFromFile(
			config.FeatureFlagsSDKKeyPath,
			config.DynamicFeatureFlagsEndpoint,
		)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating feature flag handle")
		}
	} else {
		log.Info().Msg("Feature flags disabled: using local implementation")
		featureFlagHandle = featureflags.NewLocalFeatureFlagHandler()
	}
	if featureFlagHandle == nil {
		log.Fatal().Msg("Failed to create feature flag handle")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtlsConfig})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtlsConfig)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchangeclient.New(
		config.TokenExchangeEndpoint, namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.GrpcPort))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		wg.Add(1)
		defer wg.Done()
		err = grpcServer.Serve(lis)
		if err != nil && err != grpc.ErrServerStopped {
			log.Fatal().Err(err).Msg("Error serving")
		}
		log.Info().Msg("gRPC server closed")
	}()

	authClient, err := authclient.New(config.AuthEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating auth client")
	}
	defer authClient.Close()

	// Setup tenant watcher client.
	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(centralClientCreds))
	tenantCache := tenantwatcherclient.NewTenantCache(
		tenantWatcherClient,
		"", // Since billing central is in the central namespace, must pass in "" to cache all tenants
	)
	defer tenantCache.Close()

	// Read Orb API key
	orbAPIKey, err := os.ReadFile(config.OrbAPIKeyPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Error reading Orb API key")
	}

	// Create Orb client
	orbClient := orb.NewOrbClient(string(orbAPIKey), featureFlagHandle)

	key, err := os.ReadFile(config.StripeSecretKeyPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to read Stripe secret key")
	}
	cleanKey := strings.TrimSpace(string(key))
	stripe.Key = cleanKey

	// Start Stripe to Orb migration job
	stripeToOrbMigrationJob, err := migration.NewStripeToOrbMigrationJob(
		ctx,
		config.OrbConfig,
		authClient,
		tokenExchangeClient,
		tenantCache,
		orbClient,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Stripe to Orb migration job")
	}

	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := stripeToOrbMigrationJob.Run(ctx); err != nil && err != context.Canceled {
			log.Error().Err(err).Msg("Error running Stripe to Orb migration job")
			cancel()
		}
	}()

	// Start Stripe to Orb validation job
	stripeToOrbValidationJob, err := migration.NewStripeToOrbValidationJob(
		ctx,
		config.OrbConfig,
		authClient,
		tokenExchangeClient,
		tenantCache,
		orbClient,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Stripe to Orb validation job")
	}

	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := stripeToOrbValidationJob.Run(ctx); err != nil && err != context.Canceled {
			log.Error().Err(err).Msg("Error running Stripe to Orb validation job")
			cancel()
		}
	}()

	// Wait 10 seconds before declaring healthy
	go func() {
		time.Sleep(10 * time.Second)
		declareHealthy(config.HealthFile)
	}()

	// Run the subscriber
	log.Info().Msg("Starting billing service")

	// Wait for either a shutdown signal or an OS signal
	sig := <-sigChan
	log.Info().Msgf("Received signal: %v", sig)
	grpcServer.GracefulStop()
	wg.Wait()
	log.Info().Msg("Server stopped")
}

{
  deployment: [
    {
      name: 'billing-central',
      kubecfg: {
        target: '//services/billing/central:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['bin', 'sophie', 'xiaolei'],
          slack_channel: '#team-growth',
        },
      },
    },
  ],
}

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'tenant-gc',
      kubecfg: {
        target: '//services/tenant_gc:kubecfg',
        task: [
          {
            cloud: std.asciiUpper(centralNamespace.cloud),
            env: centralNamespace.env,
            namespace: centralNamespace.namespace,
          }
          for centralNamespace in cloudInfo.centralNamespaces
          if cloudInfo.isLeadProdCluster(centralNamespace.cloud)
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'aswin', 'luke'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

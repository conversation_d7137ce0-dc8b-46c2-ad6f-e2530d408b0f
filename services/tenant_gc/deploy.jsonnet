local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local lockLib = import 'deploy/common/lock-lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';

// K8S deployment file for the tenant gc service
function(env, namespace, cloud, namespace_config)
  assert (cloudInfo.isCentralNamespace(env, namespace, cloud));

  local appName = 'tenant-gc';
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);

  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  local lock = lockLib.createLock(namespace=namespace, appName=appName, serviceAccountName=serviceAccount.name);

  local contentManagerBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=namespace, tableName='content-manager');
  local contentManagerBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,
    app=appName,
    table=contentManagerBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='gc',
    role='roles/bigtable.admin'
  );
  local shareBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=namespace, tableName='share');
  local shareBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,
    app=appName,
    table=shareBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='gc',
    role='roles/bigtable.admin'
  );

  local settingsBigtable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=namespace, tableName='settings');
  local settingsBigtableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,
    app=appName,
    table=settingsBigtable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='gc',
  );

  local githubBigtableTable = gcpLib.getBigtableTable(cloud=cloud, env=env, namespace=namespace, tableName='github');
  local githubBigtableTableAccess = gcpLib.grantBigtableAccess(
    cloud=cloud,
    env=env,
    namespace=namespace,
    app=appName,
    table=githubBigtableTable,
    iamServiceAccountNames=[serviceAccount.iamServiceAccountName],
    nameSuffix='gc',
  );

  local projectId = contentManagerBigtable.projectId;
  assert (projectId == shareBigtable.projectId);
  local instanceName = contentManagerBigtable.instanceName;
  assert (instanceName == shareBigtable.instanceName);

  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    tables: [
      {
        table_name: contentManagerBigtable.tableName,
      },
      {
        table_name: settingsBigtable.tableName,
      },
      {
        table_name: githubBigtableTable.tableName,
      },
      {
        table_name: shareBigtable.tableName,
      },
    ],
    project_id: projectId,
    instance_id: instanceName,
    port: 50051,
    namespace: namespace,
    central_namespace: namespace,
    prom_port: 9090,
    dry_run: false,
    lock_name: lock.name,
    // delete tenant data that have been deleted for more than 30 days
    delete_ttl: if env == 'DEV' then '1m' else '%sh' % (30 * 24),
    run_interval: if env == 'DEV' then '1m' else '1h',

    auth_endpoint: '%s:50051' % endpoints.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    token_exchange_endpoint: endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
    client_mtls: clientCert.config,
  });
  local roleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-role-binding' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    roleRef: {
      apiGroup: 'rbac.authorization.k8s.io',
      kind: 'ClusterRole',
      name: 'tenant-admin',
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        name: serviceAccount.name,
      },
    ],
  };
  local container =
    {
      name: 'tenant-gc',
      target: {
        name: '//services/tenant_gc:image',
        dst: 'tenant-gc',
      },
      args: [
        '-config',
        configMap.filename,
        '-health-file=/tmp/health',
      ],
      livenessProbe: {
        exec: {
          command: [
            '/bin/sh',
            '-c',
            'cat /tmp/health',
          ],
        },
        periodSeconds: 20,
      },
      env: lib.flatten([
        telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      ]),
      volumeMounts: [
        configMap.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        clientCert.volumeMountDef,
      ],
      resources: {
        limits: {
          cpu: 0.2,
          memory: '512Mi',
        },
      },
    };
  local pod = {
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    tolerations: tolerations,
    affinity: affinity,
    volumes: [
      configMap.podVolumeDef,
      dynamicFeatureFlags.podVolumeDef,
      clientCert.podVolumeDef,
    ],
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: 1,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
          annotations: {
            'reloader.stakater.com/auto': 'true',
          },
        },
        spec: pod,
      },
    },
  };
  local podDisruptionBudget = nodeLib.podDisruption(
    appName=appName,
    namespace=namespace,
    env=env,
    minAvailable=0
  );

  lib.flatten([
    configMap.objects,
    roleBinding,
    deployment,
    serviceAccount.objects,
    dynamicFeatureFlags.k8s_objects,
    podDisruptionBudget,
    lock.objects,
    contentManagerBigtableAccess,
    settingsBigtableAccess,
    githubBigtableTableAccess,
    shareBigtableAccess,
    clientCert.objects,
  ])

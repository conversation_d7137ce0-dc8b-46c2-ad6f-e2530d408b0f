# Tenant GC

This is a service that deletes tenants that have been deleted for a certain amount of time.

If a tenant is deleted, the tenant is marked deleted with `deleted` and `deleted_at` set.
After some cooldown period, the service will delete the tenant.

The service will drop the row prefix for the tenant in content manager and request insight tables.
The auth tables are not handled at this point.
If all operates are done, the service will delete the tenant object.

## Security

The service needs bigtable admin permissions to delete row prefixes on the table it managed.
The service needs to be able to call the auth-central service to revoke users.

## Testing

The service can be added to the dev namespace with `bazel run //services/tenant_gc:kubecfg`.
It is also part of the tenant E2E test that can be run with `bazel test //services/tenant_watcher/test:tenant_test`.

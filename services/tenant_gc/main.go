package main

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"time"

	"cloud.google.com/go/bigtable"
	"github.com/augmentcode/augment/base/go/durationutil"
	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	crd "github.com/augmentcode/augment/services/tenant_watcher/crd"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
)

type Table struct {
	TableName string `json:"table_name"`
}

type Config struct {
	Tables []Table `json:"tables"`

	ClientMtls *tlsconfig.ClientConfig `json:"client_mtls"`

	ProjectID  string `json:"project_id"`
	InstanceID string `json:"instance_id"`

	// namespace where to look for Tenant resource objects.
	Namespace string `json:"namespace"`

	AuthEndpoint string `json:"auth_endpoint"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`

	LockName string `json:"lock_name"`

	DryRun bool `json:"dry_run"`

	DeleteTTL durationutil.JSONDuration `json:"delete_ttl"`

	RunInterval durationutil.JSONDuration `json:"run_interval"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`
}

func sanityCheck(config *Config) error {
	if config.Namespace == "" {
		return errors.New("namespace must be set")
	}
	if config.LockName == "" {
		return errors.New("lock_name must be set")
	}
	if config.DeleteTTL.ToDuration() <= 0 {
		return errors.New("delete_ttl must be > 0")
	}
	if config.RunInterval.ToDuration() <= 0 {
		return errors.New("run_interval must be > 0")
	}
	return nil
}

func loadConfig(configPath string) (Config, error) {
	var config Config
	file, err := os.ReadFile(configPath)
	if err != nil {
		return config, err
	}
	err = json.Unmarshal(file, &config)
	return config, err
}

var (
	configFile = flag.String("config", "", "Path to config file")
	kubeconfig = flag.String("kubeconfig", "", "Path to kubeconfig file")
	healthFile = flag.String("health-file", "", "Path to a file to write a health check to")
)

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}
	log.Info().Msgf("Loaded config: %v", config)
	err = sanityCheck(&config)
	if err != nil {
		log.Fatal().Err(err).Msg("Error in config file")
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		sigCh := make(chan os.Signal, 1)
		signal.Notify(sigCh, os.Interrupt)
		<-sigCh
		cancel()
	}()

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	authClient, err := authclient.New(config.AuthEndpoint, grpc.WithTransportCredentials(clientCreds))
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating auth client")
	}
	defer authClient.Close()

	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint,
		config.Namespace,
		grpc.WithTransportCredentials(clientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create token exchange client")
	}
	defer tokenExchangeClient.Close()

	btAdminClient, err := bigtable.NewAdminClient(ctx, config.ProjectID, config.InstanceID)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Bigtable admin client")
	}
	defer btAdminClient.Close()

	btClient, err := bigtable.NewClient(ctx, config.ProjectID, config.InstanceID)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Bigtable client")
	}
	defer btClient.Close()

	ticker := time.NewTicker(config.RunInterval.ToDuration())
	defer ticker.Stop()

	dynamicClientset, err := crd.CreateDynamicClient(*kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating clientset")
		os.Exit(1)
	}

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	kubeConfig, err := rest.InClusterConfig()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get in-cluster config")
	}

	clientset, err := kubernetes.NewForConfig(kubeConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create Kubernetes client")
	}

	id, err := os.Hostname()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get hostname")
	}

	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      config.LockName,
			Namespace: config.Namespace,
		},
		Client: clientset.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: id,
		},
	}

	isLeaderCh := make(chan struct{})
	isLeader := false

	leaderElectionConfig := leaderelection.LeaderElectionConfig{
		Lock:          lock,
		LeaseDuration: 15 * time.Second,
		RenewDeadline: 10 * time.Second,
		RetryPeriod:   2 * time.Second,
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				log.Info().Msg("Elected leader")
				isLeader = true
				isLeaderCh <- struct{}{}
			},
			OnStoppedLeading: func() {
				isLeader = false
				isLeaderCh <- struct{}{}
			},
			OnNewLeader: func(identity string) {
				if identity == id {
					// This is the current leader
					return
				}
				log.Info().Msgf("New leader elected: %s", identity)
			},
		},
	}

	leaderElector, err := leaderelection.NewLeaderElector(leaderElectionConfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create leader elector")
	}
	go leaderElector.Run(ctx)

	informer := crd.NewTenantInformer(dynamicClientset, 0, config.Namespace)
	go informer.Run(ctx.Done())
	log.Info().Msg("Informer started")

	resource := crd.NewTenantResource(dynamicClientset, config.Namespace)

	gc := newTenantGc(ctx, config, btAdminClient, tokenExchangeClient, authClient, informer, resource)

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
			os.Exit(1)
		}
	}()

	// create readiness file
	if healthFile != nil {
		log.Info().Msgf("Writing health file to %s", *healthFile)
		if err := os.WriteFile(*healthFile, []byte{}, 0o644); err != nil {
			log.Fatal().Err(err).Msg("Failed to create health file")
		}
	}

	for {
		select {
		// run the processTenants when leadership changes or the ticker ticks
		case <-isLeaderCh:
			if !isLeader {
				log.Info().Msg("Not leader, skipping")
				continue
			}
			err := gc.processTenants()
			if err != nil {
				log.Error().Err(err).Msg("Failed to process deployments")
			}
		case <-ticker.C:
			if !isLeader {
				log.Info().Msg("Not leader, skipping")
				continue
			}
			err := gc.processTenants()
			if err != nil {
				log.Error().Err(err).Msg("Failed to process deployments")
			}
		}
	}
}

type TenantGc struct {
	ctx                 context.Context
	config              Config
	btAdminClient       *bigtable.AdminClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	authClient          authclient.AuthClient
	informer            crd.TenantInformer
	resource            crd.TenantResource
}

func newTenantGc(
	ctx context.Context,
	config Config,
	btAdminClient *bigtable.AdminClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	authClient authclient.AuthClient,
	informer crd.TenantInformer,
	resource crd.TenantResource,
) *TenantGc {
	return &TenantGc{
		ctx:                 ctx,
		config:              config,
		btAdminClient:       btAdminClient,
		tokenExchangeClient: tokenExchangeClient,
		authClient:          authClient,
		informer:            informer,
		resource:            resource,
	}
}

func (gc *TenantGc) checkTenantForDeletion(tenant *crd.Tenant, ttl time.Duration) bool {
	log.Info().Msgf("Checking tenant %s for deletion: deletedAt='%s'", tenant.Name, tenant.Spec.DeletedAt)
	if tenant.Spec.DeletedAt == "" {
		return false
	}
	deletedAt, err := time.Parse(time.RFC3339, tenant.Spec.DeletedAt)
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse deleted_at")
		return false
	}
	log.Info().Msgf("Tenant %s has been deleted for %s", tenant.Name, time.Since(deletedAt))
	return time.Since(deletedAt) > ttl
}

func (gc *TenantGc) deleteTenant(tenant *crd.Tenant) error {
	log.Info().Msgf("Deleting tenant %s", tenant.Name)

	for _, table := range gc.config.Tables {
		// add_tenant_id_to_key is the source of truth for this behavior
		rowKeyPrefix := tenant.Spec.TenantID + "#"
		log.Info().Msgf("Deleting rows with prefix %s from table %s", rowKeyPrefix, table.TableName)

		if gc.config.DryRun {
			log.Info().Msg("Dry run, skipping delete")
			continue
		}

		if err := gc.btAdminClient.DropRowRange(gc.ctx, table.TableName, rowKeyPrefix); err != nil {
			log.Error().Err(err).Msgf("Failed to delete rows from table %s", table.TableName)
			return err
		}
	}

	token, err := gc.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		gc.ctx,
		tenant.Spec.TenantID,
		tenant.Spec.ShardNamespace,
		[]tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_RW},
	)
	if err != nil {
		return fmt.Errorf("failed to get token for tenant %s in namespace %s: %w", tenant.Spec.TenantID, tenant.Spec.ShardNamespace, err)
	}

	sessionId := requestcontext.NewRandomRequestSessionId()
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		sessionId,
		"tenant-gc",
		token,
	)

	// Revoke each user for this tenant using auth-central client
	var users []*authpb.User
	users, err = gc.authClient.ListTenantUsers(gc.ctx, requestCtx, tenant.Spec.TenantID)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to list users for tenant %s", tenant.Name)
		return err
	}
	if gc.config.DryRun {
		log.Info().Msgf("Dry run: would have revoked %d users for tenant %s", len(users), tenant.Name)
		return nil
	}

	for _, user := range users {
		log.Info().Msgf("Removing user %s from tenant %s", user.Id, tenant.Name)
		err = gc.authClient.RemoveUserFromTenant(gc.ctx, requestCtx, user.Id, tenant.Spec.TenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to remove user %s from tenant %s", user.Id, tenant.Name)
			return err
		}
	}

	// Delete the Tenant resource
	if !gc.config.DryRun {
		log.Info().Msgf("Deleting Tenant resource for tenant %s", tenant.Name)
		gc.resource.Delete(gc.ctx, tenant.Name, tenant.Namespace, metav1.DeleteOptions{})
	}
	return nil
}

func (gc *TenantGc) processTenants() error {
	log.Info().Msg("Processing tenants")
	tenants, err := gc.informer.List()
	if err != nil {
		return err
	}

	for _, tenant := range tenants {
		if !gc.checkTenantForDeletion(&tenant, gc.config.DeleteTTL.ToDuration()) {
			continue
		}
		if err := gc.deleteTenant(&tenant); err != nil {
			return err
		}
	}
	return nil
}

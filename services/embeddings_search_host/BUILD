load("//tools/bzl:python.bzl", "py_grpc_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")

proto_library(
    name = "embeddings_search_proto",
    srcs = ["embeddings_search.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//base/proto:tensor_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "embeddings_search_py_proto",
    protos = [":embeddings_search_proto"],
    visibility = ["//services:__subpackages__"],
)

go_grpc_library(
    name = "embeddings_search_go_proto",
    importpath = "github.com/augmentcode/augment/services/embeddings_search_host/proto",
    proto = ":embeddings_search_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/proto:tensor_go_proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

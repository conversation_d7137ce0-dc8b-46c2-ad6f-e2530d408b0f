use std::collections::{BTreeSet, HashMap, HashSet};
use std::sync::Arc;
use std::time::{Duration, Instant};

use blob_names::BlobName;
use content_manager_client::BlobScope;
use request_context::{RequestContext, RequestId, RequestSessionId, RequestSource, TenantId};
use tokio::sync::mpsc;
use tracing::Instrument;

use crate::dot_math::SimdData;
use crate::util::TenantBlobContentKeys;
use crate::{
    embedding_cache::{EmbeddingCache, BLOB_KEY_BATCH_SIZE},
    metrics::BACKGROUND_FETCH_BLOBS_COUNT_COLLECTOR,
};

// In practice loading blobs from bigtable currently costs about 300us per blob (amortized). Use a
// batch size that gives us decent responsiveness (in particular, it should be less than the number
// of blobs we expect to download in 1 second) while not spamming content manager too much.
const BACKGROUND_FETCH_BATCH_SIZE: usize = 1024;

pub struct BackgroundFetcher<T: SimdData> {
    rx: mpsc::Receiver<TenantBlobContentKeys>,
    embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
    token_exchange_client: Arc<dyn token_exchange_client::TokenExchangeClient + Send + Sync>,
    request_context: RequestContext,
}

impl<T: SimdData> BackgroundFetcher<T> {
    pub fn new(
        rx: mpsc::Receiver<TenantBlobContentKeys>,
        embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
        token_exchange_client: Arc<dyn token_exchange_client::TokenExchangeClient + Send + Sync>,
    ) -> Self {
        let request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::create_random(),
            RequestSource::Background,
            None,
        );

        Self {
            rx,
            embedding_cache,
            token_exchange_client,
            request_context,
        }
    }

    async fn get_request_context(&self) -> RequestContext {
        // get permission to call find missing of all tenants in the given namespace
        let token = self
            .token_exchange_client
            .get_signed_token_for_service(
                "".to_string(),
                &[token_exchange_client::token_exchange::Scope::ContentRw],
                &self.request_context,
            )
            .await
            .expect("Failed to get service token");
        self.request_context.clone().with_token(token.clone())
    }

    pub async fn run(mut self) {
        let background_blobs_worker = async move {
            let mut dedup_keys = HashSet::new();
            let mut current_fetches: HashMap<(Option<TenantId>, BlobScope), Vec<BlobName>> =
                HashMap::new();

            let mut request_context = self.get_request_context().await;
            tracing::info!(
                "background worker: request context {:?}",
                request_context.request_id()
            );

            while let Some(tenant_blob_content_keys) = self.rx.recv().await {
                let TenantBlobContentKeys {
                    request_id,
                    tenant_info,
                    blob_scope,
                    blob_names,
                } = tenant_blob_content_keys;
                let span = tracing::info_span!("background_fetch", request_id = %request_id);
                async {
                    BACKGROUND_FETCH_BLOBS_COUNT_COLLECTOR
                        .with_label_values(&["dequeued"])
                        .inc_by(blob_names.len() as u64);

                    let current_fetch = current_fetches
                        .entry((tenant_info.tenant_id.clone(), blob_scope.clone()))
                        .or_default();

                    // add a key (one at a time)
                    for blob_name in blob_names {
                        let dedup_key = (
                            tenant_info.tenant_id.clone(),
                            blob_scope.clone(),
                            blob_name.clone(),
                        );

                        if !dedup_keys.contains(&dedup_key) {
                            dedup_keys.insert(dedup_key);
                            current_fetch.push(blob_name);
                        }

                        if current_fetch.len() >= BACKGROUND_FETCH_BATCH_SIZE {
                            // Unscientific estimate of how many keys it's worthwhile to deduplicate
                            if dedup_keys.len() >= 64 * BACKGROUND_FETCH_BATCH_SIZE {
                                dedup_keys.clear();
                            }
                            tracing::info!(
                                "background fetch: tenant: {:?}, {:?} blobs",
                                tenant_info.tenant_id,
                                current_fetch.len()
                            );
                            BACKGROUND_FETCH_BLOBS_COUNT_COLLECTOR
                                .with_label_values(&["fetched"])
                                .inc_by(current_fetch.len() as u64);

                            let mut r = self
                                .embedding_cache
                                // This relies on get_embeddings having the side effect of warming the cache
                                .get_embeddings(
                                    &request_context,
                                    &tenant_info,
                                    blob_scope.clone(),
                                    BTreeSet::from_iter(current_fetch.drain(..)),
                                    BLOB_KEY_BATCH_SIZE,
                                    // Timeout to avoid getting stuck if content manager has a problem
                                    Some(Instant::now() + Duration::from_secs(10)),
                                );
                            while let Some(message) = r.recv().await {
                                if let Err(e) = message {
                                    if e.code() == tonic::Code::Unauthenticated {
                                        // the token is invalid, so we need to get a new one
                                        request_context = self.get_request_context().await;
                                        tracing::info!(
                                            "background worker: request context {:?}",
                                            request_context.request_id()
                                        );
                                    } else {
                                        tracing::error!("background fetch failed: {:?}", e);
                                    }
                                }
                            }
                            tracing::info!("background fetch finished");
                        } else {
                            tracing::debug!(
                                "background: tenant: {:?}, enqueued {:?} blobs",
                                tenant_info.tenant_id,
                                current_fetch.len()
                            );
                        }
                    }
                }
                .instrument(span)
                .await;
            }
        };

        tracing::info!("background blobs worker started");
        background_blobs_worker.await;
        tracing::warn!("background blobs worker stopped");
    }
}

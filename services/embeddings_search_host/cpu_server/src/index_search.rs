use blob_names::B<PERSON>b<PERSON><PERSON>;
use half::f16;
use itertools::Itertools;
use ordered_float::NotNan;
use std::collections::{BTreeSet, BinaryHeap};
use std::sync::Arc;
use std::time::Instant;
use tonic::Status;

use crate::dot_math::{dot_simd_data_parallel, from_f16_slice_parallel, SimdData};
use crate::metrics::{CPU_SEARCH_LATENCY_COLLECTOR, SEARCH_BLOB_COUNTER, SEARCH_EMBEDDING_COUNTER};
use crate::types::BlobEntry;

#[derive(Debug, Clone, PartialEq, PartialOrd, Ord, Eq)]
pub struct SearchResult {
    pub results: Vec<SearchResultEntry>,
    pub num_results: usize,
}

impl SearchResult {
    pub fn into_inner(self) -> Vec<SearchResultEntry> {
        self.results
    }

    pub fn new(num_results: usize) -> Self {
        SearchResult {
            results: Vec::with_capacity(num_results),
            num_results,
        }
    }

    #[cfg(test)] // only used in tests for now
    pub fn from_vec(results: Vec<SearchResultEntry>, num_results: usize) -> Self {
        let mut results = results;
        results.sort_unstable_by(|a, b| b.value.cmp(&a.value));
        results.truncate(num_results);
        SearchResult {
            results,
            num_results,
        }
    }

    pub fn merge(&mut self, other: SearchResult) {
        let mut r = vec![];
        std::mem::swap(&mut r, &mut self.results);
        let mut r2: Vec<SearchResultEntry> = vec![r, other.into_inner()]
            .into_iter()
            .kmerge_by(|a, b| b.value < a.value)
            .collect();
        r2.truncate(self.num_results);
        std::mem::swap(&mut self.results, &mut r2);
    }
}

#[derive(Debug, Clone, PartialEq, PartialOrd, Ord, Eq)]
pub struct SearchResultEntry {
    pub blob_name: BlobName,
    pub chunk_index: usize,
    pub value: NotNan<f32>, // only public b/c request insight wants it
}

impl SearchResultEntry {
    pub fn new(blob_name: BlobName, chunk_index: usize, value: f32) -> Self {
        SearchResultEntry {
            blob_name,
            chunk_index,
            value: NotNan::new(value).expect("value shouldn't be NaN"),
        }
    }
}

pub trait IndexSearch<T: SimdData> {
    /// Search the top num_results entries from the index based on the query
    ///
    /// The function is blocking
    fn search(
        &self,
        index: &[Arc<BlobEntry<T>>],
        query: Arc<Vec<f16>>,
        num_results: usize,
        deleted_blobs: BTreeSet<BlobName>,
    ) -> Result<SearchResult, Status>;
}

pub struct DynamicIndexSearch {}

impl DynamicIndexSearch {
    pub fn new() -> Self {
        DynamicIndexSearch {}
    }
}

/// inner-product result
#[derive(Debug, Clone, PartialEq, PartialOrd, Ord, Eq, Hash)]
struct IpResult {
    // inner product
    // not-nan to make it ord
    value: NotNan<f32>,

    // index into the entry vector
    entry_index: usize,

    // index into the entry
    embedding_index: usize,
}

impl IpResult {
    fn new(value: f32, entry_index: usize, embedding_index: usize) -> Self {
        IpResult {
            value: NotNan::new(value).expect("value shouldn't be NaN"),
            entry_index,
            embedding_index,
        }
    }
}

fn merge_binary_heap(h1: &mut BinaryHeap<IpResult>, h2: BinaryHeap<IpResult>, num_results: usize) {
    for x in h2 {
        if h1.len() < num_results {
            h1.push(x);
        } else if *h1.peek().unwrap() > x {
            *h1.peek_mut().unwrap() = x;
        }
    }
}

impl<T: SimdData> IndexSearch<T> for DynamicIndexSearch {
    fn search(
        &self,
        index: &[Arc<BlobEntry<T>>],
        query: Arc<Vec<f16>>,
        num_results: usize,
        deleted_blobs: BTreeSet<BlobName>,
    ) -> Result<SearchResult, Status> {
        let start = Instant::now();

        let mut embedding_count: u64 = 0;
        // Batch size is in number of embeddings
        // Per-call, this reaches ~100K (as of 2024/10)
        const BATCH_SIZE: usize = 1024;
        let mut batch_embeddings: usize = 0;
        let mut batches: Vec<(usize, Vec<Arc<BlobEntry<T>>>)> = vec![(0, vec![])];
        for (idx, entry) in index.iter().enumerate() {
            if entry.tensor.shape[0] > 0
                && entry.tensor.shape[1] != query.len()
                && !deleted_blobs.contains(&entry.blob_name)
            {
                let message = format!(
                    "Invalid query size: {} vs {}",
                    entry.tensor.shape[1],
                    query.len()
                );
                tracing::warn!(message);
                return Err(Status::internal(message));
            }
            embedding_count += entry.tensor.size() as u64;

            // Add to tail batch
            batches.last_mut().unwrap().1.push(entry.clone());
            batch_embeddings += entry.tensor.size();
            if batch_embeddings >= BATCH_SIZE && idx + 1 < index.len() {
                batches.push((idx + 1, vec![]));
                batch_embeddings = 0;
            }
        }

        if query.iter().any(|x| x.is_nan()) {
            tracing::warn!("NaN in query: {:?}", query);
            // Don't fail the request for now so we can avoid causing problems for users
        }

        SEARCH_BLOB_COUNTER.inc_by(index.len() as u64);
        SEARCH_EMBEDDING_COUNTER.inc_by(embedding_count);
        tracing::info!(
            "Search: embedding_count={} num_results={} blob_entry_count={} query_len={}",
            embedding_count,
            num_results,
            index.len(),
            query.len(),
        );

        // translate to into f32
        let query = from_f16_slice_parallel(query.as_ref());
        let query_ref = query.as_ref();

        // Run the inner product calculation for batches of blob entries in parallel (in the rayon threadpool)
        // Avoid par_iter: we want to make sure each task spawned to the rayon pool is relatively small,
        // to avoid getting stuck behind large tasks that are work-stolen from other calls to search().
        //
        // Note: if any code elsewhere in the process pushes large splitting tasks onto the rayon threadpool,
        // we're in trouble at this callsite. Not ideal...
        let mut heaps: Vec<BinaryHeap<IpResult>> = vec![BinaryHeap::new(); batches.len()];
        rayon::in_place_scope(|s| {
            let tasks = batches.into_iter().zip(heaps.iter_mut());
            tasks.for_each(|(batch, heap)| {
                s.spawn(move |_| {
                    *heap = BinaryHeap::with_capacity(num_results);
                    let start_index = &batch.0;
                    let blobs = batch.1.iter().enumerate();
                    blobs.for_each(|(entry_index, entry)| {
                        let entry_tensor = &entry.tensor;
                        if entry_tensor.size() == 0 {
                            return;
                        }
                        for (i, item) in entry_tensor.iter().enumerate() {
                            let ip = dot_simd_data_parallel(query_ref, item);
                            if !ip.is_nan() {
                                let ip = IpResult::new(-ip, start_index + entry_index, i);
                                if heap.len() < num_results {
                                    heap.push(ip);
                                } else if *heap.peek().unwrap() > ip {
                                    *heap.peek_mut().unwrap() = ip;
                                }
                            }
                        }
                    });
                });
            });
        });
        tracing::debug!("inner product done");

        // the heap contains the top-k entries of all blob entries
        let mut final_heap =
            heaps
                .into_iter()
                .fold(BinaryHeap::with_capacity(num_results), |mut acc, x| {
                    merge_binary_heap(&mut acc, x, num_results);
                    acc
                });

        let mut result = Vec::with_capacity(num_results);
        while !final_heap.is_empty() {
            let r = final_heap.pop().unwrap();
            result.push(SearchResultEntry::new(
                index[r.entry_index].blob_name.clone(),
                r.embedding_index,
                -(r.value.into_inner()),
            ));
        }
        result.reverse();
        tracing::debug!("sorting done");
        CPU_SEARCH_LATENCY_COLLECTOR.observe(start.elapsed().as_secs_f64());
        Ok(SearchResult {
            results: result,
            num_results,
        })
    }
}

#[cfg(test)]
mod tests {
    use crate::test::get_test_data_path;
    use numpy::f16x16_simd::{f16x16, is_f16x16_supported};
    use numpy::f32x8_simd::f32x8;
    use numpy::read_npy;

    use super::*;
    use assert2::check;
    use bytes::Bytes;

    #[test]
    fn test_search_result() {
        let blob_name = BlobName::from_bytes(&[0; 32]).expect("Failed to create blob name");

        let mut result1 = SearchResult::from_vec(
            vec![
                SearchResultEntry::new(blob_name.clone(), 1727, 275.51694),
                SearchResultEntry::new(blob_name.clone(), 26367, 274.79565),
                SearchResultEntry::new(blob_name.clone(), 2905, 273.87683),
                SearchResultEntry::new(blob_name.clone(), 4456, 273.4971),
                SearchResultEntry::new(blob_name.clone(), 10625, 273.19318),
                SearchResultEntry::new(blob_name.clone(), 13057, 272.68042),
                SearchResultEntry::new(blob_name.clone(), 22184, 271.6364),
                SearchResultEntry::new(blob_name.clone(), 5204, 271.43665),
                SearchResultEntry::new(blob_name.clone(), 21360, 271.31787),
                SearchResultEntry::new(blob_name.clone(), 8223, 271.31732),
                SearchResultEntry::new(blob_name.clone(), 15085, 271.07806),
                SearchResultEntry::new(blob_name.clone(), 26357, 270.7531),
                SearchResultEntry::new(blob_name.clone(), 26351, 268.7531),
            ],
            16,
        );

        let result2 = SearchResult::from_vec(
            vec![
                SearchResultEntry::new(blob_name.clone(), 25861, 272.0576),
                SearchResultEntry::new(blob_name.clone(), 31726, 272.04205),
                SearchResultEntry::new(blob_name.clone(), 14813, 271.91644),
                SearchResultEntry::new(blob_name.clone(), 26354, 271.78116),
                SearchResultEntry::new(blob_name.clone(), 26359, 269.78116),
            ],
            16,
        );

        result1.merge(result2);
        let result1 = result1.into_inner();
        check!(result1.len() == 16);
        check!(
            result1
                == vec![
                    SearchResultEntry::new(blob_name.clone(), 1727, 275.51694),
                    SearchResultEntry::new(blob_name.clone(), 26367, 274.79565),
                    SearchResultEntry::new(blob_name.clone(), 2905, 273.87683),
                    SearchResultEntry::new(blob_name.clone(), 4456, 273.4971),
                    SearchResultEntry::new(blob_name.clone(), 10625, 273.19318),
                    SearchResultEntry::new(blob_name.clone(), 13057, 272.68042),
                    SearchResultEntry::new(blob_name.clone(), 25861, 272.0576),
                    SearchResultEntry::new(blob_name.clone(), 31726, 272.04205),
                    SearchResultEntry::new(blob_name.clone(), 14813, 271.91644),
                    SearchResultEntry::new(blob_name.clone(), 26354, 271.78116),
                    SearchResultEntry::new(blob_name.clone(), 22184, 271.6364),
                    SearchResultEntry::new(blob_name.clone(), 5204, 271.43665),
                    SearchResultEntry::new(blob_name.clone(), 21360, 271.31787),
                    SearchResultEntry::new(blob_name.clone(), 8223, 271.31732),
                    SearchResultEntry::new(blob_name.clone(), 15085, 271.07806),
                    SearchResultEntry::new(blob_name.clone(), 26357, 270.7531),
                ]
        );
    }

    #[test]
    fn test_search_result_entry() {
        let blob0 = BlobName::from_bytes(&[0; 32]).expect("Failed to create blob name");
        let result = SearchResultEntry::new(blob0.clone(), 1, 1.0);
        assert_eq!(result.blob_name, blob0);
        assert_eq!(result.chunk_index, 1);
    }

    #[test]
    fn test_ip_result() {
        let result = IpResult::new(1.0, 1, 2);
        assert_eq!(result.value.into_inner(), 1.0);
        assert_eq!(result.entry_index, 1);
        assert_eq!(result.embedding_index, 2);
    }

    #[test]
    fn test_simple_search_f32x8() {
        let blob_name = BlobName::from_bytes(&[0; 32]).expect("Failed to create blob name");
        test_simple_search::<f32x8>(vec![
            SearchResultEntry::new(blob_name.clone(), 1727, 275.51694),
            SearchResultEntry::new(blob_name.clone(), 26367, 274.79565),
            SearchResultEntry::new(blob_name.clone(), 2905, 273.87683),
            SearchResultEntry::new(blob_name.clone(), 4456, 273.4971),
            SearchResultEntry::new(blob_name.clone(), 10625, 273.19318),
            SearchResultEntry::new(blob_name.clone(), 13057, 272.68042),
            SearchResultEntry::new(blob_name.clone(), 25861, 272.0576),
            SearchResultEntry::new(blob_name.clone(), 31726, 272.04205),
            SearchResultEntry::new(blob_name.clone(), 14813, 271.91644),
            SearchResultEntry::new(blob_name.clone(), 26354, 271.78116),
            SearchResultEntry::new(blob_name.clone(), 22184, 271.6364),
            SearchResultEntry::new(blob_name.clone(), 5204, 271.43665),
            SearchResultEntry::new(blob_name.clone(), 21360, 271.31787),
            SearchResultEntry::new(blob_name.clone(), 8223, 271.31732),
            SearchResultEntry::new(blob_name.clone(), 15085, 271.07806),
            SearchResultEntry::new(blob_name.clone(), 26357, 270.7531),
        ])
    }

    #[test]
    fn test_simple_search_f16x16() {
        // TODO: make this skip rather than pass on non-Sapphire-Rapids CPUs?
        if !is_f16x16_supported() {
            return;
        }
        let blob_name = BlobName::from_bytes(&[0; 32]).expect("Failed to create blob name");
        // Note the values clamped to 1/8ths and slight reordering compared to the f32x8 results
        test_simple_search::<f16x16>(vec![
            SearchResultEntry::new(blob_name.clone(), 1727, 275.625),
            SearchResultEntry::new(blob_name.clone(), 26367, 274.75),
            SearchResultEntry::new(blob_name.clone(), 2905, 273.875),
            SearchResultEntry::new(blob_name.clone(), 4456, 273.5),
            SearchResultEntry::new(blob_name.clone(), 10625, 273.25),
            SearchResultEntry::new(blob_name.clone(), 13057, 272.75),
            SearchResultEntry::new(blob_name.clone(), 31726, 272.125),
            SearchResultEntry::new(blob_name.clone(), 25861, 272.0),
            SearchResultEntry::new(blob_name.clone(), 14813, 271.875),
            SearchResultEntry::new(blob_name.clone(), 26354, 271.75),
            SearchResultEntry::new(blob_name.clone(), 22184, 271.625),
            SearchResultEntry::new(blob_name.clone(), 5204, 271.375),
            SearchResultEntry::new(blob_name.clone(), 8223, 271.25),
            SearchResultEntry::new(blob_name.clone(), 15085, 271.25),
            SearchResultEntry::new(blob_name.clone(), 21360, 271.125),
            SearchResultEntry::new(blob_name.clone(), 15473, 270.875),
        ])
    }

    fn test_simple_search<T: SimdData>(expected: Vec<SearchResultEntry>) {
        // load entry1.npy
        let content = std::fs::read(get_test_data_path(
            "services/embeddings_search_host/cpu_server/src/test_data/entry1.npy",
        ))
        .unwrap();
        let entry_tensor = read_npy::<T>(Bytes::from(content)).unwrap();

        // load query1.npy
        let content = std::fs::read(get_test_data_path(
            "services/embeddings_search_host/cpu_server/src/test_data/query1.npy",
        ))
        .unwrap();
        let query_tensor = read_npy(Bytes::from(content)).unwrap();
        let query = Arc::new(query_tensor.data);

        let blob_name = BlobName::from_bytes(&[0; 32]).expect("Failed to create blob name");

        let index = vec![Arc::new(BlobEntry {
            blob_name,
            tensor: entry_tensor,
        })];

        let searcher = DynamicIndexSearch::new();
        let results = searcher
            .search(&index, query.clone(), 16, BTreeSet::new())
            .expect("Failed to search");

        let results = results.into_inner();
        check!(results.len() == 16);
        check!(results == expected);

        let results = searcher
            .search(&index, query.clone(), 32, BTreeSet::new())
            .expect("Failed to search");
        let results = results.into_inner();
        assert_eq!(results.len(), 32);

        let results = searcher
            .search(&index, query.clone(), 2, BTreeSet::new())
            .expect("Failed to search");
        let results = results.into_inner();
        assert_eq!(results.len(), 2);
        assert_eq!(
            results,
            expected.iter().take(2).cloned().collect::<Vec<_>>(),
        );
    }

    #[test]
    fn test_search_empty() {
        // simulate empty tensor
        let content = std::fs::read(get_test_data_path(
            "services/embeddings_search_host/cpu_server/src/test_data/empty.npy",
        ))
        .unwrap();
        let entry_tensor = read_npy::<f32x8>(Bytes::from(content)).unwrap();

        // load query1.npy
        let content = std::fs::read(get_test_data_path(
            "services/embeddings_search_host/cpu_server/src/test_data/query1.npy",
        ))
        .unwrap();
        let query_tensor = read_npy(Bytes::from(content)).unwrap();
        let query = Arc::new(query_tensor.data);

        let blob_name = BlobName::from_bytes(&[0; 32]).expect("Failed to create blob name");

        let index = vec![Arc::new(BlobEntry {
            blob_name,
            tensor: entry_tensor,
        })];

        let searcher = DynamicIndexSearch::new();
        let results = searcher
            .search(&index, query.clone(), 16, BTreeSet::new())
            .expect("Failed to search");

        let results = results.into_inner();
        check!(results.len() == 0);
    }
}

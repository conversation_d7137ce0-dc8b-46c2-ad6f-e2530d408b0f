use std::collections::BTreeSet;
use std::mem;
use std::sync::atomic::{self, AtomicU32, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};

use blob_names::BlobName;
use content_manager_client::BlobScope;
use feature_flags::FeatureFlagsServiceHandle;
use futures::StreamExt;
use grpc_auth::tenant_info_from_grpc_req;
use grpc_service::log_response_fn;
use grpc_service::send_and_ignore;
use half::f16;
use itertools::{Either, Itertools};
use moka::future::Cache;
use rand::Rng;
use request_context::{
    RequestContext, RequestSessionId, TenantId, TenantInfo, TokenScope, EMPTY_TENANT_ID,
};
use request_insight_publisher::request_insight::{
    EmbeddingsSearchRequest, EmbeddingsSearchResponse, EmbeddingsSearchResult,
};
use request_insight_publisher::RequestInsightPublisher;
use scann_rs::{RestrictAllowList, SearchParameters};
use tokio::sync::mpsc;
use tokio_stream::wrappers::ReceiverStream;
use tonic::{Request, Response, Status};
use tracing::Instrument;
use working_set_client::WorkingSetClient;

use crate::background::BackgroundFetcher;
use crate::metrics::ANN_INDEX_SEARCH_QUALITY;
use crate::{
    checkpoint_cache::CheckpointCache,
    chunk_cache::{ChunkCache, ChunkKey},
    config::Config,
    dot_math::SimdData,
    embedding_cache::{EmbeddingCache, EmbeddingResult, BLOB_KEY_BATCH_SIZE},
    index_search::{DynamicIndexSearch, IndexSearch, SearchResult},
    indexed_checkpoint_cache::IndexedCheckpointCache,
    metrics::{
        ANN_SEARCH_LATENCY_COLLECTOR, BACKGROUND_FETCH_BLOBS_COUNT_COLLECTOR,
        BLOBS_PER_REQUEST_COLLECTOR, PHASE_LATENCY_COLLECTOR,
    },
    proto::{
        base::blob_names::Blobs,
        embeddings_search::{
            embeddings_search_server::{EmbeddingsSearch, EmbeddingsSearchServer},
            search_chunks_response::Response::{
                Chunk as ProtoChunk, MissingBlobs as ProtoMissingBlobs,
            },
            FindMissingRequest, FindMissingResponse, MissingBlobs, RetrievalChunk,
            SearchChunksRequest, SearchChunksResponse,
        },
    },
    types::{CheckpointBlobState, CheckpointState},
    util::TenantBlobContentKeys,
};

/// Truncate the list of missing keys in responses to this size to avoid gRPC
/// message size limit. The client is expected to drive this list down to zero,
/// so the client will eventually discover the entire list. The max limit is
/// set to 16K blob_names. This produces a response roughly 1MB in size (16K *
/// 64 bytes, for names in string format), which is a quarter the default grpc 4MB
/// size limit.
const MAX_MISSING_KEYS_TO_RETURN: usize = 16384;

// Not enforced anywhere, just a relevant constant for sizing some things
const MAX_USERS_PER_NAMESPACE: u64 = 16 * 1024;

const QUALITY_CHECK_DEADLINE_MS: u64 = 10000;
const QUALITY_CHECK_MAX_BLOBS: usize = 500000;

const EMBEDDINGS_SEARCH_SUB_KEY: &str = "embeddings.npy";

pub const CHECKPOINT_ANN_INDEXING_ENABLED: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("embeddings_search_checkpoint_ann_indexing_enabled", false);

pub const USE_INDEXED_CHECKPOINT_CACHE: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("embeddings_search_use_indexed_checkpoint_cache", false);

pub const INDEXED_CHECKPOINT_QUALITY_SAMPLING_PROB: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new(
        "embeddings_search_indexed_checkpoint_quality_sampling_probability",
        0.0,
    );

#[derive(Debug, Clone)]
struct SequenceChecker {
    current_seq: u32,
    tracker: Arc<AtomicU32>,
}

impl SequenceChecker {
    async fn try_from_request(
        sequence_id: Option<u32>,
        sequence_tracker: &Cache<(TenantId, BlobScope, RequestSessionId), Arc<AtomicU32>>,
        seq_tracker_key: &(TenantId, BlobScope, RequestSessionId),
    ) -> Result<Option<Self>, Status> {
        match sequence_id {
            Some(current_seq) => {
                let seq_tracker = sequence_tracker
                    .get_with(seq_tracker_key.clone(), async move {
                        Arc::new(AtomicU32::new(current_seq))
                    })
                    .await;

                // Optimistically guess that we're the first caller in a while
                let mut most_recent_seq = current_seq;
                let mut cmpxch_retries = 0;
                // atomic dance to update the seq_tracker only if we can prove we aren't cancelled
                while let Err(previous_seq) = seq_tracker.compare_exchange(
                    most_recent_seq,
                    current_seq,
                    Ordering::SeqCst,
                    Ordering::SeqCst,
                ) {
                    most_recent_seq = previous_seq;
                    // Paranoia: avoid spinning forever if something goes wrong
                    cmpxch_retries += 1;
                    if cmpxch_retries > 100 {
                        tracing::warn!("failed to update sequence tracker");
                        return Err(Status::internal("failed to update sequence tracker"));
                    }
                    if most_recent_seq > current_seq {
                        tracing::info!("skipping search due to sequence id");
                        return Err(Status::cancelled(
                            "cancelled by a request with higher sequence id",
                        ));
                    }
                }
                Ok(Some(Self {
                    current_seq,
                    tracker: seq_tracker,
                }))
            }
            None => Ok(None),
        }
    }

    fn check_cancelled(&self) -> Result<(), Status> {
        if self.tracker.load(Ordering::SeqCst) > self.current_seq {
            tracing::info!("cancelling search due to sequence id");
            return Err(Status::cancelled(
                "cancelled by a request with higher sequence id",
            ));
        }
        Ok(())
    }

    async fn cleanup(
        &self,
        sequence_tracker: &Cache<(TenantId, BlobScope, RequestSessionId), Arc<AtomicU32>>,
        seq_tracker_key: &(TenantId, BlobScope, RequestSessionId),
    ) {
        sequence_tracker.remove(seq_tracker_key).await;
        tracing::debug!("Cleaned up sequence tracker for key {:?}", seq_tracker_key);
    }
}

struct EmbeddingsSearchDataImpl<T: SimdData> {
    pub embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
    pub index_search: Arc<dyn IndexSearch<T> + Send + Sync + 'static>,
    pub chunk_cache: Arc<dyn ChunkCache + Send + Sync + 'static>,
    pub checkpoint_cache: Arc<dyn CheckpointCache + Send + Sync + 'static>,
    pub indexed_checkpoint_cache: Arc<dyn IndexedCheckpointCache + Send + Sync + 'static>,
    pub request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync + 'static>,
    pub background_blobs: mpsc::Sender<TenantBlobContentKeys>,
    pub sequence_tracker: Cache<(TenantId, BlobScope, RequestSessionId), Arc<AtomicU32>>,
    pub working_set_client: Arc<dyn WorkingSetClient + Send + Sync + 'static>,
}

impl<T: SimdData> EmbeddingsSearchDataImpl<T> {
    async fn update_request_insight(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        blobs: &[Blobs],
        request: &SearchChunksRequest,
        missing_blob_names: &[String],
        result: &SearchResult,
    ) {
        let embeddings_search_request = EmbeddingsSearchRequest {
            num_results: request.num_results,
            transformation_key: request.transformation_key.clone(),
            blobs: blobs
                .iter()
                .map(|x| request_insight_publisher::base::blob_names::Blobs {
                    baseline_checkpoint_id: x.baseline_checkpoint_id.clone(),
                    added: x.added.clone(),
                    deleted: x.deleted.clone(),
                })
                .collect(),
            ..Default::default()
        };
        let embeddings_search_response = EmbeddingsSearchResponse {
            missing_blob_names: missing_blob_names.to_owned(),
            results: result
                .results
                .iter()
                .map(|x| EmbeddingsSearchResult {
                    blob_name: String::from(&x.blob_name),
                    chunk_index: x.chunk_index as u32,
                    value: x.value.into_inner(),
                })
                .collect(),
        };
        self.request_insight_publisher
            .record_embeddings_search(
                request_context,
                tenant_info,
                embeddings_search_request,
                embeddings_search_response,
            )
            .await;
    }

    async fn get_checkpoint_blob_states(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        blobs: &[Blobs],
        use_indexed_checkpoint_cache: bool,
        blob_scope: &BlobScope,
    ) -> tonic::Result<Vec<CheckpointBlobState>> {
        let mut checkpoint_blob_states: Vec<CheckpointBlobState> = vec![];
        for blobs in blobs.iter() {
            let indexed_checkpoint = match blobs.baseline_checkpoint_id {
                Some(ref baseline_checkpoint_id) if use_indexed_checkpoint_cache => {
                    self.indexed_checkpoint_cache
                        .get(
                            request_context,
                            tenant_info,
                            baseline_checkpoint_id.clone(),
                            blob_scope.transformation_key.clone(),
                        )
                        .instrument(tracing::info_span!("get_indexed_checkpoint"))
                        .await?
                }
                _ => None,
            };

            let checkpoint_state = if let Some(indexed_checkpoint) = indexed_checkpoint {
                CheckpointState::Indexed(indexed_checkpoint)
            } else if let Some(baseline_checkpoint_id) = &blobs.baseline_checkpoint_id {
                match self
                    .checkpoint_cache
                    .get_checkpoint(request_context, tenant_info, baseline_checkpoint_id.clone())
                    .instrument(tracing::info_span!("get_blob_names"))
                    .await
                {
                    Ok(m) => {
                        let checkpoint_blob_names =
                            m.blobs.iter().cloned().collect::<BTreeSet<_>>();
                        CheckpointState::NotIndexed(checkpoint_blob_names)
                    }
                    // "Blob checkpoint not found" case: return an empty result downstream of this
                    Err(e) if e.code() == tonic::Code::NotFound => CheckpointState::NotFound,
                    // Unexpected errors (e.g. content manager down) from blob checkpoint lookup
                    Err(e) => return Err(e),
                }
            } else {
                // TODO: expected case? Will passing this many `added` blobs cause issues downstream?
                CheckpointState::NotIndexed(BTreeSet::new())
            };

            // These are always relative to the canonical blob contents of the checkpoint
            let added_blobs = blobs
                .added
                .iter()
                .map(|x| BlobName::from_bytes(x))
                .collect::<tonic::Result<BTreeSet<BlobName>>>()?;
            let deleted_blobs = blobs
                .deleted
                .iter()
                .map(|x| BlobName::from_bytes(x))
                .collect::<tonic::Result<BTreeSet<BlobName>>>()?;

            checkpoint_blob_states.push(CheckpointBlobState {
                checkpoint_state,
                added_blobs,
                deleted_blobs,
            });
        }
        Ok(checkpoint_blob_states)
    }

    async fn run_search(
        &self,
        request: &SearchChunksRequest,
        request_context: &RequestContext,
        mut index_receiver: mpsc::Receiver<tonic::Result<EmbeddingResult<T>>>,
        seq_checker: &Option<SequenceChecker>,
    ) -> tonic::Result<(SearchResult, Vec<BlobName>)> {
        let get_embeddings_start = Instant::now();
        let mut unknown_keys = vec![];

        // Run the search query
        let query = request.query.as_ref().expect("query must not be empty"); // was checked before
        let query = Arc::new(convert_bytes_to_f16(query.contents.as_slice()));
        let num_results = request.num_results as usize;
        let futures = futures::stream::FuturesUnordered::new();

        // We pipeline the streaming of downloaded embeddings with the CPU-intensive search task
        while let Some(next_index) = index_receiver.recv().await {
            let per_index_seq_checker = seq_checker.clone();
            if let Some(checker) = per_index_seq_checker.as_ref() {
                checker.check_cancelled()?;
            }

            let next_index = next_index?;
            unknown_keys.extend(next_index.unknown_blobs);

            if next_index.entries.is_empty() {
                continue;
            }

            // Clone shared refcounted resources needed by the search threads
            let searcher = self.index_search.clone();
            let query = query.clone();
            // Set up the tracing span outside the spawn callback so it picks up the right context
            let search_span = tracing::info_span!("ip_search");

            let search_task = tokio::task::spawn_blocking(move || {
                // tie span to the sync callback lifetime to work around spawn_blocking limitations
                let _guard = search_span.enter();
                if let Some(checker) = per_index_seq_checker.as_ref() {
                    checker.check_cancelled()?;
                }
                searcher.search(
                    &next_index.entries,
                    query,
                    num_results,
                    next_index.deleted_blobs,
                )
            });
            futures.push(search_task);
        }
        PHASE_LATENCY_COLLECTOR
            .with_label_values(&[
                "get_embeddings",
                request.transformation_key.as_str(),
                request_context.request_source().to_string().as_str(),
            ])
            .observe(get_embeddings_start.elapsed().as_secs_f64());
        let cpu_search_start = Instant::now();

        let results = futures.collect::<Vec<_>>().await;
        tracing::debug!("search results: {:?}", results.len());
        let mut search_result = SearchResult::new(num_results);
        // Reduce the batched results. Note that this is O(number of batches * num_results) and
        // isn't parallelized, which is a reason to keep the batches relatively large and few.
        for result in results.into_iter() {
            match result {
                Ok(Ok(result)) => {
                    search_result.merge(result);
                }
                Ok(Err(e)) => {
                    return Err(e);
                }
                Err(e) => {
                    tracing::error!("join error: {:?}", e);
                    return Err(tonic::Status::internal("search task failed"));
                }
            }
        }

        PHASE_LATENCY_COLLECTOR
            .with_label_values(&[
                "cpu_search",
                request.transformation_key.as_str(),
                request_context.request_source().to_string().as_str(),
            ])
            .observe(cpu_search_start.elapsed().as_secs_f64());
        Ok((search_result, unknown_keys))
    }

    // Best-effort attempt to pass missing blobs to the background fetch worker.
    // This may fail if the channel is full or if the worker crashed somehow.
    fn try_background_fetch(&self, tenant_blob_content_keys: TenantBlobContentKeys) {
        let blob_count = tenant_blob_content_keys.blob_names.len() as u64;
        match self.background_blobs.try_send(tenant_blob_content_keys) {
            Ok(_) => {
                BACKGROUND_FETCH_BLOBS_COUNT_COLLECTOR
                    .with_label_values(&["enqueued"])
                    .inc_by(blob_count);
                tracing::info!("Notified background fetch about {} blobs", blob_count);
            }
            Err(e) => {
                BACKGROUND_FETCH_BLOBS_COUNT_COLLECTOR
                    .with_label_values(&["not_enqueued"])
                    .inc_by(blob_count);
                tracing::warn!(
                    "Failed to notify background fetch about {} blobs: {}",
                    blob_count,
                    e
                );
            }
        }
    }
}

pub struct EmbeddingsSearchImpl<T: SimdData> {
    config: Config,
    feature_flags: FeatureFlagsServiceHandle,
    op_sequence_number: Arc<AtomicU32>,
    data: Arc<EmbeddingsSearchDataImpl<T>>,
}

fn convert_bytes_to_f16(bytes: &[u8]) -> Vec<f16> {
    bytes
        .chunks_exact(2)
        .map(|x| f16::from_bits(u16::from_le_bytes([x[0], x[1]])))
        .collect()
}

impl<T: SimdData> EmbeddingsSearchImpl<T> {
    #[allow(clippy::too_many_arguments)]
    pub fn new(
        config: Config,
        feature_flags: FeatureFlagsServiceHandle,
        embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
        checkpoint_cache: Arc<dyn CheckpointCache + Send + Sync + 'static>,
        chunk_cache: Arc<dyn ChunkCache + Send + Sync + 'static>,
        indexed_checkpoint_cache: Arc<dyn IndexedCheckpointCache + Send + Sync + 'static>,
        request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync + 'static>,
        token_exchange_client: Arc<dyn token_exchange_client::TokenExchangeClient + Send + Sync>,
        working_set_client: Arc<dyn WorkingSetClient + Send + Sync + 'static>,
    ) -> Self {
        // Set up a background worker to warm up the cache in response to search timeouts
        let (tx, rx) = mpsc::channel::<TenantBlobContentKeys>(32);

        let background_fetcher =
            BackgroundFetcher::new(rx, embedding_cache.clone(), token_exchange_client.clone());
        tokio::spawn(background_fetcher.run());
        let sequence_tracker = Cache::builder()
            // 16x gives us plenty of room for utilization spikes and multiple transformation keys
            // Entries should be under 100 bytes so this is still pretty small (10s of MB at most)
            .max_capacity(16 * MAX_USERS_PER_NAMESPACE)
            // Ensure timely recovery if we somehow insert a sequence ID that's too high
            .time_to_idle(Duration::from_secs(60))
            .build();

        EmbeddingsSearchImpl {
            config: config.clone(),
            feature_flags,
            op_sequence_number: Arc::new(AtomicU32::new(rand::thread_rng().gen())),
            data: Arc::new(EmbeddingsSearchDataImpl {
                checkpoint_cache,
                embedding_cache,
                index_search: Arc::new(DynamicIndexSearch::new()),
                chunk_cache,
                indexed_checkpoint_cache,
                request_insight_publisher,
                background_blobs: tx,
                sequence_tracker,
                working_set_client,
            }),
        }
    }

    pub fn new_server(self) -> EmbeddingsSearchServer<Self> {
        EmbeddingsSearchServer::new(self)
    }

    // Bind a standard set of per-request attributes like tenant name and user id.
    pub fn get_feature_flags(
        &self,
        tenant_info: &TenantInfo,
    ) -> Result<FeatureFlagsServiceHandle, tonic::Status> {
        let ff = self
            .feature_flags
            .bind_attribute("tenant_name", &tenant_info.tenant_name)
            .map_err(|e| {
                tracing::error!("get_feature_flags failed: {:?}", e);
                tonic::Status::internal("Failed to bind feature flags to tenant")
            })?;
        if let Some(user_id) = tenant_info.opaque_user_id.as_ref() {
            ff.bind_attribute("opaque_user_id", &user_id.user_id)
                .map_err(|e| {
                    tracing::error!("get_feature_flags failed: {:?}", e);
                    tonic::Status::internal("Failed to bind feature flags to user uuid")
                })
        } else {
            Ok(ff)
        }
    }

    fn validate_request(&self, request: &SearchChunksRequest) -> Result<(), tonic::Status> {
        if request.num_results <= 0 {
            return Err(tonic::Status::invalid_argument("num_results must be > 0"));
        }

        if request.num_results > 1024 {
            return Err(tonic::Status::invalid_argument(
                "num_results must be <= 1024",
            ));
        }

        if request.transformation_key.is_empty() {
            return Err(tonic::Status::invalid_argument(
                "transformation_key must not be empty",
            ));
        }

        if request.query.is_none() {
            return Err(tonic::Status::invalid_argument("query must not be empty"));
        }

        let query = request.query.as_ref().unwrap();
        if query.shape.len() != 2 {
            return Err(tonic::Status::invalid_argument(
                "query must be a 2-dimensional tensor",
            ));
        }
        if query.shape[0] != 1 {
            return Err(tonic::Status::invalid_argument(
                "only one query is supported at the moment",
            ));
        }
        if query.shape[1] % 2 != 0 {
            return Err(tonic::Status::invalid_argument(
                "query must be a factor of 2",
            ));
        }
        if query.contents.len() / 2 != query.shape[1] as usize {
            tracing::warn!(
                "query_content_len={} query_shape={:?}",
                query.contents.len(),
                query.shape
            );
            return Err(tonic::Status::invalid_argument(
                "query content must match the shape",
            ));
        }
        if query.datatype != "FP16" {
            return Err(tonic::Status::invalid_argument(
                "only FP16 is supported at the moment",
            ));
        }

        Ok(())
    }
}

#[tonic::async_trait]
impl<T: SimdData> EmbeddingsSearch for EmbeddingsSearchImpl<T> {
    type SearchChunksStream = ReceiverStream<Result<SearchChunksResponse, Status>>;

    // given a query embedding and a set of blob keys, download the chunk embeddings for those
    // keys and find the num_results best matches for it (in order) using inner
    // products. Any blobs that were not searched for whatever reason are
    // returned as part of unknown_blobs.
    async fn search_chunks(
        &self,
        request: Request<SearchChunksRequest>,
    ) -> Result<Response<Self::SearchChunksStream>, Status> {
        let tenant_info = tenant_info_from_grpc_req(&request)
            .ok_or_else(|| Status::internal("tenant_info must be set"))?;
        let request_context = RequestContext::try_from(request.metadata())?;
        let span = tracing::info_span!("embeddings_search_chunks", request_id = %request_context.request_id());
        let ann_index_span = tracing::info_span!(parent: &span, "ann_index");
        let get_chunks_span = tracing::info_span!(parent: &span, "get_chunks");
        let stream_span = tracing::info_span!(parent: &span, "search_chunks stream");

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;

                let request = request.into_inner();
                self.validate_request(&request)?;

                let op_id = format!(
                    "{:x}",
                    self.op_sequence_number
                        .fetch_add(1, atomic::Ordering::SeqCst)
                );
                let feature_flags = self.get_feature_flags(&tenant_info)?;
                let use_indexed_checkpoint_cache = USE_INDEXED_CHECKPOINT_CACHE.get_from(&feature_flags);
                tracing::info!(
                    "[{}] search_chunks request tkey {} num_results {} use_indexed_checkpoint_cache {}",
                    op_id,
                    request.transformation_key,
                    request.num_results,
                    use_indexed_checkpoint_cache,
                );
                let search_timeout_ms = if request.search_timeout_ms > 0 {
                    request.search_timeout_ms as u64
                } else {
                    self.config.search_timeout_ms
                };
                let deadline = Instant::now() + Duration::from_millis(search_timeout_ms);
                let blob_scope = BlobScope {
                    transformation_key: request.transformation_key.clone(),
                    sub_key: EMBEDDINGS_SEARCH_SUB_KEY.to_string(),
                };

                if CHECKPOINT_ANN_INDEXING_ENABLED.get_from(&feature_flags) {
                    let checkpoint_ids = request.blobs.iter().flat_map(|b| b.baseline_checkpoint_id.clone()).collect::<Vec<_>>();
                    let transformation_key = request.transformation_key.clone();
                    let create_ann_index_request_context = request_context.clone();
                    let data = self.data.clone();
                    tokio::spawn(async move {
                        for checkpoint_id in checkpoint_ids.into_iter() {
                            tracing::info!("Marking checkpoint {} tkey {} in use for ANN indexing", checkpoint_id, transformation_key);
                            if let Err(e) = data.working_set_client.create_ann_index_for_checkpoint(
                                &create_ann_index_request_context,
                                checkpoint_id.clone(),
                                transformation_key.clone(),
                                Duration::from_secs(10), // Arbitrary. TODO: revisit
                            )
                            .await {
                                tracing::warn!("Failed to mark checkpoint {} tkey {} in use for ANN indexing: {}", checkpoint_id, transformation_key, e);
                            }
                        }
                    }
                    .instrument(ann_index_span));
                }

                // If a sequence ID is set in the request, respect cancellations that set a higher sequence
                // ID on the same session and transformation key.
                let seq_tracker_key = (
                    tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID),
                    blob_scope.clone(),
                    request_context.request_session_id(),
                );
                let seq_checker = SequenceChecker::try_from_request(
                    request.sequence_id,
                    &self.data.sequence_tracker,
                    &seq_tracker_key,
                )
                .await?;
                let setup_request_blobs_start = Instant::now();
                let data = self.data.clone();
                // 32 is a somewhat arbitrary size, usually callers ask for the top 32 results.
                let (tx, rx) = mpsc::channel::<Result<SearchChunksResponse, Status>>(32);

                let checkpoint_blob_states = data
                    .get_checkpoint_blob_states(
                        &request_context,
                        &tenant_info,
                        request.blobs.as_slice(),
                        use_indexed_checkpoint_cache,
                        &blob_scope,
                    )
                    .await?;

                let blobs_count = checkpoint_blob_states
                    .iter()
                    .map(|x| x.len())
                    .sum::<usize>();
                let checkpoint_not_found = checkpoint_blob_states.iter().any(|x| matches!(x.checkpoint_state, CheckpointState::NotFound));
                if blobs_count == 0 || checkpoint_not_found {
                    // No need for tokio::spawn, because the channel is guaranteed
                    // to have space for 1 response at this point.
                    let resp = SearchChunksResponse {
                        response: Some(ProtoMissingBlobs(MissingBlobs {
                            missing_blob_names: vec![],
                            checkpoint_not_found,
                        })),
                    };
                    if !send_and_ignore(&tx, Ok(resp), "search_chunks_stream missing response")
                        .await
                    {
                        return Err(Status::internal("failed to send missing blobs response"));
                    };
                    return Ok(Response::new(ReceiverStream::new(rx)));
                }
                tracing::info!(
                    "[{}] blobs resolved: {} provided",
                    op_id,
                    blobs_count,
                );
                PHASE_LATENCY_COLLECTOR
                    .with_label_values(&["setup_request_blobs", request.transformation_key.as_str(), request_context.request_source().to_string().as_str()])
                    .observe(setup_request_blobs_start.elapsed().as_secs_f64());
                let get_candidate_blobs_start = Instant::now();
                if let Some(checker) = seq_checker.as_ref() {
                    checker.check_cancelled()?;
                }

                BLOBS_PER_REQUEST_COLLECTOR
                    .with_label_values(&[&tenant_info.tenant_name])
                    .observe(blobs_count as f64);

                let mut indexed_checkpoint_data = Vec::new();
                let mut qa_blob_names = BTreeSet::new();
                let mut blob_names = BTreeSet::new();
                let should_quality_sample = request.blobs.len() <= QUALITY_CHECK_MAX_BLOBS &&
                    rand::thread_rng().gen_bool(INDEXED_CHECKPOINT_QUALITY_SAMPLING_PROB.get_from(&self.feature_flags));
                for state in checkpoint_blob_states.iter() {
                    match state.checkpoint_state {
                        CheckpointState::NotIndexed(ref unindexed_checkpoint_blobs) => {
                            tracing::info!("[{}] will run brute force search", op_id);
                            for blob_name in unindexed_checkpoint_blobs.iter()
                                .chain(state.added_blobs.iter())
                                .filter(|blob_name| !state.deleted_blobs.contains(blob_name))
                            {
                                blob_names.insert(blob_name.clone());
                            }
                        }
                        CheckpointState::Indexed(ref indexed_checkpoint) if use_indexed_checkpoint_cache => {
                            let scann_span = tracing::info_span!("scann_search");
                            let _guard = scann_span.enter();
                            let start = Instant::now();

                            // Blob diff handling.
                            // Added & deleted are disjoint sets at the indexed_checkpoint and request level.
                            // Also we expect a blob can only be added, or deleted, at one level.
                            // So the cases to handle are:
                            // added going from index to checkpoint, deleted going from checkpoint to request-- do nothing
                            // deleted going from index to checkpoint, added going from checkpoint to request-- also do nothing
                            let net_added_blobs = state.added_blobs.iter()
                                .chain(indexed_checkpoint.added.iter())
                                .filter(|blob_name| !state.deleted_blobs.contains(blob_name) && !indexed_checkpoint.deleted.contains(blob_name))
                                .collect::<BTreeSet<_>>();
                            tracing::info!("[{}] {} net added blobs ({} vs index, {} vs checkpoint)", op_id, net_added_blobs.len(), indexed_checkpoint.added.len(), state.added_blobs.len());
                            let net_deleted_blobs = state.deleted_blobs.iter()
                                .chain(indexed_checkpoint.deleted.iter())
                                .filter(|blob_name| !state.added_blobs.contains(blob_name) && !indexed_checkpoint.added.contains(blob_name))
                                .collect::<BTreeSet<_>>();
                            tracing::info!("[{}] {} net deleted blobs ({} vs index, {} vs checkpoint)", op_id, net_deleted_blobs.len(), indexed_checkpoint.deleted.len(), state.deleted_blobs.len());
                            if should_quality_sample {
                                // clones required instead of moves due to reference lifetimes
                                indexed_checkpoint_data.push((
                                    indexed_checkpoint.clone(),
                                    net_added_blobs.iter()
                                        .map(|blob_name| (*blob_name).clone())
                                        .collect::<BTreeSet<_>>(),
                                    net_deleted_blobs.iter()
                                        .map(|blob_name| (*blob_name).clone())
                                        .collect::<BTreeSet<_>>(),
                                ));
                            }

                            let query = request.query.as_ref().expect("query must not be empty");
                            let chunk_indices = &indexed_checkpoint.index.indices_to_chunks;
                            let f32_query = convert_bytes_to_f16(query.contents.as_slice()).into_iter().map(f32::from).collect::<Vec<_>>();
                            let num_results = request.num_results as usize;
                            let num_pre_reorder = std::cmp::max(num_results, chunk_indices.len() / 64) + 1000;
                            let search_parameters = SearchParameters {
                                final_nn: num_pre_reorder, // We have no reorder step so we give the same number of final results
                                pre_reorder_nn: num_pre_reorder,
                                leaves_to_search: 0,
                                allow_list: Some(RestrictAllowList {
                                    restrict: net_deleted_blobs.into_iter()
                                    .flat_map(|blob_name| {
                                        if let Some((start_index, num_chunks)) = indexed_checkpoint.index.blobs_to_indices.get(blob_name) {
                                            *start_index..(*start_index + *num_chunks)
                                        } else {
                                            0..0
                                        }
                                    })
                                    .map(|x| x as u32)
                                    .collect(),
                                }),
                            };
                            tracing::info!("[{}] running ScaNN search with {} embeddings, {} pre-reorder results", op_id, chunk_indices.len(), num_pre_reorder);
                            let results = indexed_checkpoint.index.scann_index.search(&f32_query, search_parameters)?;
                            tracing::info!("[{}] ScaNN search finished with {} results", op_id, results.indices.len());
                            let result_chunks = results.indices.into_iter().map(|x| chunk_indices.get(x)).collect::<Vec<_>>();
                            for (blob_name, _) in result_chunks.into_iter().flatten() {
                                blob_names.insert(blob_name.clone());
                            }
                            tracing::info!("[{}] ScaNN search found {} blobs", op_id, blob_names.len());
                            for blob_name in net_added_blobs.into_iter() {
                                blob_names.insert(blob_name.clone());
                            }
                            ANN_SEARCH_LATENCY_COLLECTOR.observe(start.elapsed().as_secs_f64());

                        }
                        _ => {
                            tracing::info!("[{}] will run brute force search with no checkpoint available", op_id);
                            for blob_name in state.added_blobs.iter() {
                                blob_names.insert(blob_name.clone());
                            }
                        }
                    }
                };

                if should_quality_sample {
                    // it's okay to include any ANN results in this set as the ANN result is
                    // strictly a subset of the all_blobs set that would have been searched if the
                    // index was not present.
                    qa_blob_names.clone_from(&blob_names);
                }

                let index_receiver = data
                    .embedding_cache
                    .get_embeddings(
                        &request_context,
                        &tenant_info,
                        blob_scope.clone(),
                        blob_names,
                        BLOB_KEY_BATCH_SIZE,
                        Some(deadline),
                    );
                let get_candidate_blobs_label = if checkpoint_blob_states.iter().any(|x| matches!(x.checkpoint_state, CheckpointState::Indexed(_))) {
                    "scann_search"
                } else {
                    "copy_checkpoint_blobs"
                };
                PHASE_LATENCY_COLLECTOR
                    .with_label_values(&[get_candidate_blobs_label, request.transformation_key.as_str(), request_context.request_source().to_string().as_str()])
                    .observe(get_candidate_blobs_start.elapsed().as_secs_f64());

                let (search_result, unknown_keys) = data.run_search(
                    &request,
                    &request_context,
                    index_receiver,
                    &seq_checker,
                )
                .await?;

                if !indexed_checkpoint_data.is_empty() {
                    let search_result = search_result.clone();
                    let data = data.clone();
                    let request = request.clone();
                    let blob_scope = blob_scope.clone();
                    let request_context = request_context.clone();
                    let tenant_info = tenant_info.clone();
                    tracing::info!("Quality sampling: selected request: {} ", request_context.request_id());
                    tokio::task::spawn(async move {
                        tracing::info!("Quality sampling: starting : {}", request_context.request_id());
                        let original_blobs = search_result.results
                            .into_iter()
                            .map(|res| res.blob_name)
                            .collect::<BTreeSet<_>>();
                        let all_blobs = indexed_checkpoint_data
                            .iter_mut() // not into_iter to prevent lifetime issues
                            .flat_map(|(indexed_checkpoint, net_added_blobs, net_deleted_blobs)| {
                                let net_added_blobs = mem::take(net_added_blobs);
                                indexed_checkpoint.index.indices_to_chunks.iter()
                                    .filter(move |(blob_name, _)| !net_deleted_blobs.contains(blob_name))
                                    .map(|(blob_name, _)| blob_name.clone())
                                    .chain(net_added_blobs.into_iter())
                            })
                            .chain(qa_blob_names.into_iter())
                            .collect::<BTreeSet<_>>();
                        let blob_count = all_blobs.len();
                        let deadline = Instant::now() + Duration::from_millis(QUALITY_CHECK_DEADLINE_MS);
                        let index_receiver = data.embedding_cache
                            .get_embeddings(
                                &request_context,
                                &tenant_info,
                                blob_scope,
                                all_blobs,
                                BLOB_KEY_BATCH_SIZE,
                                Some(deadline)
                            );
                        tracing::info!("Quality sampling: running exhaustive search for: {}", request_context.request_id());
                        match data.run_search(&request, &request_context, index_receiver, &None).await {
                            Ok((search_result, _)) => {
                                let matched = search_result.results
                                    .into_iter()
                                    .filter(|res| original_blobs.contains(&res.blob_name))
                                    .count();
                                // if there are more than 2^31 results, something went very very
                                // wrong, so it's probably okay to crash eveything, even from the
                                // aux task that does the QA checking
                                let match_frac = f64::from(i32::try_from(matched).unwrap()) / f64::from(request.num_results);
                                ANN_INDEX_SEARCH_QUALITY.with_label_values(&[&request.transformation_key]).observe(match_frac);
                                tracing::info!(
                                    match_frac = match_frac,
                                    blob_count = blob_count,
                                    transformation_key = request.transformation_key,
                                    "Quality sampling: done computing: request {}, result {}",
                                    request_context.request_id(),
                                    match_frac,
                                );
                            },
                            Err(e) => tracing::error!("Async quality verification search failed: {:?}", e)
                        }
                    });
                }

                if let Some(checker) = seq_checker.as_ref() {
                    checker.check_cancelled()?;
                    checker
                        .cleanup(&self.data.sequence_tracker, &seq_tracker_key)
                        .await;
                }

                tracing::info!(
                    "[{}] search response: chunks_len={} missing_blobs={}",
                    op_id,
                    search_result.results.len(),
                    unknown_keys.len()
                );
                // TODO: change this condition to recognizing a timeout error as part of AU-2373
                // Chunk unknown_keys to limit max memory usage of mpsc queue
                for chunk in unknown_keys.chunks(32768) {
                    data.try_background_fetch(TenantBlobContentKeys {
                        request_id: request_context.request_id(),
                        tenant_info: tenant_info.clone(),
                        blob_scope: blob_scope.clone(),
                        blob_names: chunk.to_vec(),
                    });
                }
                let missing_blob_names: Vec<String> = unknown_keys
                    .into_iter()
                    .map(|x| x.to_string())
                    .take(MAX_MISSING_KEYS_TO_RETURN)
                    .collect();

                data.update_request_insight(
                    &request_context,
                    &tenant_info,
                    request.blobs.as_slice(),
                    &request,
                    &missing_blob_names,
                    &search_result,
                )
                .await;

                // Send missing blob names - we assume that any known blob names
                // at this point will not disappear when we ask content manager
                // for the contents. No need for tokio::spawn, because the channel
                // is guaranteed to have space for 1 response at this point.
                let resp = SearchChunksResponse {
                    response: Some(ProtoMissingBlobs(MissingBlobs {
                        missing_blob_names: missing_blob_names.clone(),
                        checkpoint_not_found: false,
                    })),
                };
                if !send_and_ignore(&tx, Ok(resp), "search_chunks_stream missing response").await {
                    return Err(Status::internal("failed to send missing blobs response"));
                }

                // Hashmap of chunk_index -> score
                let chunk_scores = search_result
                    .results
                    .clone()
                    .into_iter()
                    .map(|x| x.value.into_inner())
                    .collect::<Vec<f32>>();
                let chunk_keys: Vec<ChunkKey> = search_result
                    .results
                    .into_iter()
                    .map(|key| ChunkKey {
                        blob_name: key.blob_name,
                        transformation_key: request.transformation_key.clone(),
                        chunk_index: key.chunk_index,
                    })
                    .collect();

                // Fetch chunks
                let mut chunk_entries_rx = data
                    .chunk_cache
                    .get_chunks(&request_context, &tenant_info, chunk_keys.clone())
                    .instrument(get_chunks_span)
                    .await;

                tokio::spawn(
                    async move {
                        let mut missing_chunk_blob_names = vec![];
                        let mut index = 0;
                        while let Some(chunk_entry) = chunk_entries_rx.recv().await {
                            let maybe_resp = match chunk_entry {
                                Ok(Some(chunk)) => {
                                    let key = &chunk_keys[index];
                                    let score = chunk_scores[index];
                                    Some(Ok(SearchChunksResponse {
                                        response: Some(ProtoChunk(RetrievalChunk {
                                            content: chunk.content.clone(),
                                            score,
                                            blob_name: key.blob_name.to_string(),
                                            chunk_index: key.chunk_index as u32,
                                            metadata: chunk.metadata.clone(),
                                        })),
                                    }))
                                }
                                Ok(None) => {
                                    missing_chunk_blob_names
                                        .push(chunk_keys[index].blob_name.to_string());
                                    None
                                }
                                Err(e) => Some(Err(e)),
                            };
                            if let Some(resp) = maybe_resp {
                                if !send_and_ignore(
                                    &tx,
                                    resp,
                                    "search_chunks_stream chunk response",
                                )
                                .await
                                {
                                    return;
                                }
                            }
                            index += 1;
                        }

                        if !missing_chunk_blob_names.is_empty() {
                            tracing::error!("[{}] missing blob names: {:?}", op_id, missing_chunk_blob_names);
                            let resp = SearchChunksResponse {
                                response: Some(ProtoMissingBlobs(MissingBlobs {
                                    missing_blob_names: missing_chunk_blob_names,
                                    checkpoint_not_found: false,
                                })),
                            };
                            send_and_ignore(
                                &tx,
                                Ok(resp),
                                "search_chunks_stream missing chunks response",
                            )
                            .await;
                        }
                    }
                    .instrument(stream_span),
                );

                Ok(Response::new(ReceiverStream::new(rx)))
            },
            "search_chunks",
        )
        .instrument(span)
        .await
    }

    // find_missing is deliberately designed to work just like the normal search (search_chunks)
    // except that it skips the search step and just returns unknown_blobs. As a side effect, it
    // will populate the cache with any keys that exist but are uncached, which we consider mostly
    // desirable behavior since search is likely to want those blobs soon after.
    async fn find_missing(
        &self,
        request: tonic::Request<FindMissingRequest>,
    ) -> Result<tonic::Response<FindMissingResponse>, tonic::Status> {
        let request_context = RequestContext::try_from(request.metadata())?;
        let tenant_info = tenant_info_from_grpc_req(&request).expect("tenant_info must be set");
        let span = tracing::info_span!("find_missing", request_id = %request_context.request_id());

        log_response_fn(
            || async {
                tenant_info.validate_scope(TokenScope::ContentR)?;

                let request = request.into_inner();

                let (blob_names, invalid): (Vec<BlobName>, Vec<String>) = request
                    .blob_names
                    .into_iter()
                    .partition_map(|n| match BlobName::new(&n) {
                        Ok(blob_name) => Either::Left(blob_name),
                        Err(_) => Either::Right(n),
                    });

                let blob_scope = BlobScope {
                    transformation_key: request.transformation_key.clone(),
                    sub_key: request.sub_key.clone(),
                };

                let mut missing_blob_names = vec![];

                let mut index_receiver = self.data.embedding_cache.get_embeddings(
                    &request_context,
                    &tenant_info,
                    blob_scope,
                    BTreeSet::from_iter(blob_names.into_iter()),
                    BLOB_KEY_BATCH_SIZE,
                    None,
                );

                while let Some(next_index) = index_receiver.recv().await {
                    let next_index = next_index?;
                    missing_blob_names.extend(next_index.unknown_blobs);
                }

                Ok(tonic::Response::new(FindMissingResponse {
                    missing_blob_names: missing_blob_names
                        .iter()
                        .map(String::from)
                        .chain(invalid.into_iter())
                        .collect(),
                }))
            },
            "find_missing",
        )
        .instrument(span)
        .await
    }
}

#[cfg(test)]
mod tests {
    use crate::{
        checkpoint_cache::CheckpointCacheImpl,
        chunk_cache::ChunkCacheImpl,
        embedding_cache::{EmbeddingCacheImpl, IterEntry},
        indexed_checkpoint_cache::IndexedCheckpointCacheImpl,
        proto::tensor::Tensor,
        test::get_test_data_path,
        util::tests::MockContentManagerClient,
    };
    use auth_entities_proto::auth_entities::UserId;
    use content_manager_client::{
        AnnIndexBlobInfoData, ContentManagerClient, GetAnnIndexBlobInfosResult,
        GetBestAnnIndexResult,
    };
    use content_manager_rs_proto::content_manager::{
        batch_get_content_response, BatchGetContentFinalContent, BatchGetContentResponse,
        GetAllBlobsFromCheckpointResponse,
    };
    use futures::StreamExt;
    // use mockall::*;
    use numpy::f32x8_simd::f32x8;
    use request_insight_publisher::{
        request_insight::request_event::Event, RequestInsightPublisherImpl,
        RequestInsightPublisherInternalMockImpl,
    };
    use scann_rs::ScannFactory;
    use secrecy::SecretString;
    use std::collections::{HashMap, HashSet};
    use token_exchange_client::{token_exchange::Scope, TokenExchangeClient};
    use working_set_client::NullWorkingSetClient;

    use super::*;

    pub struct MockTokenExchangeClient {}

    impl MockTokenExchangeClient {
        pub fn new() -> Self {
            Self {}
        }
    }

    #[tonic::async_trait]
    impl TokenExchangeClient for MockTokenExchangeClient {
        async fn get_signed_token_for_user(
            &self,
            _user_id: String,
            _opaque_user_id: UserId,
            _user_email: Option<String>,
            _tenant_id: String,
            _request_context: &RequestContext,
        ) -> Result<SecretString, tonic::Status> {
            Ok(SecretString::new("".to_string()))
        }
        async fn get_signed_token_for_service(
            &self,
            _tenant_id: String,
            _scopes: &[Scope],
            _request_context: &RequestContext,
        ) -> Result<SecretString, tonic::Status> {
            Ok(SecretString::new("".to_string()))
        }
        async fn get_verification_key(&self) -> Result<Vec<u8>, tonic::Status> {
            Ok(vec![])
        }
    }

    // Returns the found chunk indices in order, the missing blob names, and the checkpoint_not_found flag
    async fn run_request_with_scopes<T: SimdData>(
        service: &EmbeddingsSearchImpl<T>,
        request: SearchChunksRequest,
        scopes: Vec<TokenScope>,
    ) -> Result<(Vec<u32>, Vec<String>, Vec<String>, bool), tonic::Status> {
        let mut request = tonic::Request::new(request);
        RequestContext::new_for_test().annotate(request.metadata_mut());
        TenantInfo::new_for_test_with_scopes(scopes).annotate(request.extensions_mut());

        let mut rx = service.search_chunks(request).await?.into_inner();
        let mut chunk_indices = vec![];
        let mut blob_names = vec![];
        let mut missing_blob_names = vec![];
        let mut checkpoint_not_found = false;

        while let Some(response) = rx.next().await {
            let response = response?;
            match response {
                SearchChunksResponse {
                    response: Some(ProtoChunk(chunk)),
                } => {
                    chunk_indices.push(chunk.chunk_index);
                    blob_names.push(chunk.blob_name);
                }
                SearchChunksResponse {
                    response:
                        Some(ProtoMissingBlobs(MissingBlobs {
                            missing_blob_names: names,
                            checkpoint_not_found: not_found,
                        })),
                } => {
                    missing_blob_names.extend(names);
                    checkpoint_not_found = not_found;
                }
                SearchChunksResponse { response: None } => {}
            }
        }

        Ok((
            chunk_indices,
            blob_names,
            missing_blob_names,
            checkpoint_not_found,
        ))
    }

    async fn run_request<T: SimdData>(
        service: &EmbeddingsSearchImpl<T>,
        request: SearchChunksRequest,
    ) -> Result<(Vec<u32>, Vec<String>, Vec<String>, bool), tonic::Status> {
        run_request_with_scopes(service, request, vec![TokenScope::ContentR]).await
    }

    struct Services<T: SimdData> {
        service: EmbeddingsSearchImpl<T>,
        embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
        request_insight_mock: Arc<RequestInsightPublisherInternalMockImpl>,
    }

    fn create_service<T: SimdData>(
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
        use_indexed_checkpoint_cache: bool,
    ) -> Services<T> {
        let request_insight_mock: Arc<RequestInsightPublisherInternalMockImpl> =
            Arc::new(RequestInsightPublisherInternalMockImpl::default());
        let request_insight = Arc::new(RequestInsightPublisherImpl::new_for_test(
            request_insight_mock.clone(),
        ));

        let config = Config {
            dynamic_feature_flags: Some(
                vec![(
                    "embeddings_search_use_indexed_checkpoint_cache".to_string(),
                    serde_json::json!(use_indexed_checkpoint_cache),
                )]
                .into_iter()
                .collect(),
            ),
            ..Default::default()
        };

        let embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static> = Arc::new(
            EmbeddingCacheImpl::<T>::new(config.clone(), content_manager.clone(), None),
        );

        let chunk_cache: Arc<dyn ChunkCache + Send + Sync + 'static> =
            Arc::new(ChunkCacheImpl::new(config.clone(), content_manager.clone()));

        let checkpoint_cache: Arc<dyn CheckpointCache + Send + Sync + 'static> = Arc::new(
            CheckpointCacheImpl::new(config.clone(), content_manager.clone()),
        );

        let indexed_checkpoint_cache: Arc<dyn IndexedCheckpointCache + Send + Sync + 'static> =
            Arc::new(IndexedCheckpointCacheImpl::new(
                config.clone(),
                content_manager.clone(),
            ));

        let service = EmbeddingsSearchImpl::new(
            config,
            feature_flags::setup_local(),
            embedding_cache.clone(),
            checkpoint_cache.clone(),
            chunk_cache.clone(),
            indexed_checkpoint_cache.clone(),
            request_insight.clone(),
            Arc::new(MockTokenExchangeClient::new()),
            Arc::new(NullWorkingSetClient::new()),
        );
        Services {
            service,
            embedding_cache,
            request_insight_mock,
        }
    }

    fn setup_content_manager_with_mock_content(
        embedding_filename: &'static str,
    ) -> content_manager_client::MockContentManagerClient {
        let mut content_manager = content_manager_client::MockContentManagerClient::new();
        content_manager.expect_get_content().times(1).returning(
            |_, _, blob_scope, blob_names, _| {
                let (tx, rx) = mpsc::channel(blob_names.len());
                for blob_name in blob_names {
                    tx.try_send(Ok(BatchGetContentResponse {
                        response: Some(batch_get_content_response::Response::FinalContent(
                            BatchGetContentFinalContent {
                                blob_name: blob_name.to_string(),
                                transformation_key: blob_scope.transformation_key.clone(),
                                sub_key: blob_scope.sub_key.clone(),
                                content: std::fs::read(get_test_data_path(embedding_filename))
                                    .unwrap(),
                                final_hash: "".to_string(),
                                metadata: vec![],
                            },
                        )),
                    }))
                    .unwrap();
                }
                rx
            },
        );
        content_manager
            .expect_get_content_multiple_scopes()
            .times(1)
            .returning(|_, _, blobs, _| {
                let (tx, rx) = mpsc::channel(blobs.len());
                for (blob_name, blob_scope) in blobs {
                    tx.try_send(Ok(BatchGetContentResponse {
                        response: Some(batch_get_content_response::Response::FinalContent(
                            BatchGetContentFinalContent {
                                blob_name: blob_name.to_string(),
                                transformation_key: blob_scope.transformation_key.clone(),
                                sub_key: blob_scope.sub_key.clone(),
                                content: vec![],
                                final_hash: "".to_string(),
                                metadata: vec![],
                            },
                        )),
                    }))
                    .unwrap();
                }
                rx
            });
        content_manager
    }

    #[tokio::test]
    async fn test_search_trivial() {
        let content_manager = setup_content_manager_with_mock_content(
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        );

        let services = create_service::<f32x8>(Arc::new(content_manager), false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: None,
                added: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: vec![],
            }],
            ..Default::default()
        };
        let (chunk_indices, _, _, _) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        assert_eq!(
            chunk_indices,
            // This is kind of a silly output but it is a result; it shows that we found 4 chunks
            // for each of the two blobs above (MockContentManagerClient hardcodes a get_content
            // that happens to have four chunks) and didn't see anything to make us reorder them
            vec![0, 1, 2, 3, 0, 1, 2, 3]
        );

        // Check that events are published as expected
        assert!(services
            .request_insight_mock
            .last_update_request_info_request
            .lock()
            .unwrap()
            .is_some());
        let published_event = services
            .request_insight_mock
            .last_update_request_info_request
            .lock()
            .unwrap()
            .as_ref()
            .unwrap()
            .clone();
        assert_eq!(published_event.events.len(), 2);
        assert!(matches!(
            published_event.events[0].event.as_ref(),
            Some(Event::EmbeddingsSearchRequest(_))
        ));
        assert!(matches!(
            published_event.events[1].event.as_ref(),
            Some(Event::EmbeddingsSearchResponse(_))
        ));
    }

    #[tokio::test]
    async fn test_search_missing_scope() {
        let content_manager = setup_content_manager_with_mock_content(
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        );
        let services = create_service::<f32x8>(Arc::new(content_manager), false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: None,
                added: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: vec![],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        assert!(run_request_with_scopes(&services.service, request, vec![])
            .await
            .is_err());
    }

    #[tokio::test]
    async fn test_chunks_iter() -> Result<(), tonic::Status> {
        let content_manager = Arc::new(MockContentManagerClient::new(
            HashSet::from([
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
                "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string(),
            ]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));
        let services = create_service::<f32x8>(content_manager.clone(), false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: None,
                added: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: vec![],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        let mut request = tonic::Request::new(request);
        RequestContext::new_for_test().annotate(request.metadata_mut());
        TenantInfo::new_for_test_with_scopes(vec![TokenScope::ContentRw])
            .annotate(request.extensions_mut());
        let mut results: Vec<SearchChunksResponse> = vec![];

        {
            let mut rx = services.service.search_chunks(request).await?.into_inner();
            // drain
            while let Some(response) = rx.next().await {
                let r = response?;
                results.push(r);
            }
        }
        assert_eq!(content_manager.get_requested_blob_count(), 12);

        let mut iter_entries: Vec<IterEntry> = vec![];
        let mut i = services.embedding_cache.iter();
        while let Some(entry) = i.recv().await {
            iter_entries.push(entry?);
        }
        println!("iter_entries: {:?}", iter_entries);

        assert_eq!(iter_entries.len(), 2);

        let services2 = create_service::<f32x8>(content_manager.clone(), false);
        for entry in iter_entries.into_iter() {
            services2
                .embedding_cache
                .add(
                    entry.tensor,
                    entry.tenant_id,
                    entry.blob_name,
                    BlobScope {
                        transformation_key: entry.transformation_key,
                        sub_key: entry.sub_key,
                    },
                )
                .await?;
        }

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: None,
                added: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: vec![],
            }],
            ..Default::default()
        };
        let mut request = tonic::Request::new(request);
        RequestContext::new_for_test().annotate(request.metadata_mut());
        TenantInfo::new_for_test_with_scopes(vec![TokenScope::ContentRw])
            .annotate(request.extensions_mut());
        let mut results2: Vec<SearchChunksResponse> = vec![];
        {
            let mut rx = services2.service.search_chunks(request).await?.into_inner();
            // drain
            while let Some(response) = rx.next().await {
                let r = response?;
                results2.push(r);
            }
        }
        assert_eq!(results, results2);
        assert_eq!(content_manager.get_requested_blob_count(), 20);

        Ok(())
    }

    #[tokio::test]
    async fn test_search_empty_blobs() {
        let content_manager = setup_content_manager_with_mock_content(
            "services/embeddings_search_host/cpu_server/src/test_data/empty.npy",
        );
        let services = create_service::<f32x8>(Arc::new(content_manager), false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: None,
                added: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: vec![],
            }],
            ..Default::default()
        };
        let (chunk_indices, _, _, _) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        // No results
        assert!(chunk_indices.is_empty());
    }

    #[tokio::test]
    async fn test_search_checkpointed_blobs() {
        let mut content_manager = setup_content_manager_with_mock_content(
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        );
        content_manager
            .expect_get_checkpoint_blob_names()
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tx.try_send(Ok(GetAllBlobsFromCheckpointResponse {
                    blob_names: [
                        "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                        "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                    ]
                    .iter()
                    .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                    .collect(),
                }))
                .unwrap();
                rx
            });

        let services = create_service::<f32x8>(Arc::new(content_manager), false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: Some("checkpoint-id-1".to_string()),
                added: vec![],
                deleted: vec![],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        let (chunk_indices, _, _, _) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        assert_eq!(
            chunk_indices,
            // Same as the trivial case, see above
            vec![0, 1, 2, 3, 0, 1, 2, 3]
        );
    }

    #[tokio::test]
    async fn test_search_added_deleted_blobs() {
        let mut content_manager = setup_content_manager_with_mock_content(
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        );
        content_manager
            .expect_get_checkpoint_blob_names()
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tx.try_send(Ok(GetAllBlobsFromCheckpointResponse {
                    blob_names: [
                        "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                        "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                    ]
                    .iter()
                    .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                    .collect(),
                }))
                .unwrap();
                rx
            });

        let services = create_service::<f32x8>(Arc::new(content_manager), false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: Some("checkpoint-id-1".to_string()),
                added: vec![BlobName::new(
                    "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                )
                .unwrap()
                .as_bytes()
                .to_vec()],
                deleted: vec![BlobName::new(
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                )
                .unwrap()
                .as_bytes()
                .to_vec()],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        let (chunk_indices, blob_names, _, _) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        assert_eq!(
            chunk_indices,
            // Same as the trivial case, see above
            vec![0, 1, 2, 3, 0, 1, 2, 3]
        );
        assert_eq!(
            blob_names,
            vec![
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
            ]
        );
    }

    #[tokio::test]
    async fn test_search_indexed_checkpoint_cache_no_indexed_checkpoint() {
        let mut content_manager = setup_content_manager_with_mock_content(
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_goodbye.npy",
        );
        content_manager
            .expect_get_checkpoint_blob_names()
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tx.try_send(Ok(GetAllBlobsFromCheckpointResponse {
                    blob_names: [
                        "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                        "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                    ]
                    .iter()
                    .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                    .collect(),
                }))
                .unwrap();
                rx
            });
        content_manager
            .expect_get_best_ann_index()
            .return_once(|_, _, _, _| Err(tonic::Status::not_found("no index found")));

        let services = create_service::<f32x8>(Arc::new(content_manager), true);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 512],
                contents: vec![0; 1024], // empty contents
            }),
            num_results: 8,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: Some("checkpoint-id-1".to_string()),
                added: vec![BlobName::new(
                    "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                )
                .unwrap()
                .as_bytes()
                .to_vec()],
                deleted: vec![BlobName::new(
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                )
                .unwrap()
                .as_bytes()
                .to_vec()],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };

        let (chunk_indices, blob_names, _, _) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        assert_eq!(
            chunk_indices,
            // Same as the trivial case, see above
            vec![0, 1, 2, 3, 0, 1, 2, 3]
        );
        assert_eq!(
            blob_names,
            vec![
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                "025dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
            ]
        );
    }

    #[tokio::test]
    async fn test_search_indexed_checkpoint_cache_index_deltas() {
        let mut content_manager = setup_content_manager_with_mock_content(
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_goodbye.npy",
        );
        // Produce a fake ANN index
        let mut scann_factory = ScannFactory::new(512, 1).unwrap();
        scann_factory.score_ah(8, 0.2, 100);
        let scann_index = scann_factory.build(&[1.0; 32 * 512], 32).unwrap();
        let assets = scann_index.serialize().unwrap();

        // Simulate checkpoint covering (1, 2, 3, 4)
        content_manager
            .expect_get_checkpoint_blob_names()
            .return_once(|_, _, _| {
                let (tx, rx) = mpsc::channel(1);
                tx.try_send(Ok(GetAllBlobsFromCheckpointResponse {
                    blob_names: [
                        "0000000000000000000000000000000000000000000000000000000000000001",
                        "0000000000000000000000000000000000000000000000000000000000000002",
                        "0000000000000000000000000000000000000000000000000000000000000003",
                        "0000000000000000000000000000000000000000000000000000000000000004",
                    ]
                    .iter()
                    .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                    .collect(),
                }))
                .unwrap();
                rx
            });
        // Simulate index covering (3, 4, 5, 6)
        content_manager
            .expect_get_best_ann_index()
            .return_once(|_, _, _, _| {
                Ok(GetBestAnnIndexResult {
                    index_id: "index-id-1".to_string(),
                    added_blobs: [
                        "0000000000000000000000000000000000000000000000000000000000000001",
                        "0000000000000000000000000000000000000000000000000000000000000002",
                    ]
                    .iter()
                    .map(|b| BlobName::new(b).unwrap())
                    .collect(),
                    removed_blobs: [
                        "0000000000000000000000000000000000000000000000000000000000000005",
                        "0000000000000000000000000000000000000000000000000000000000000006",
                    ]
                    .iter()
                    .map(|b| BlobName::new(b).unwrap())
                    .collect(),
                })
            });
        content_manager
            .expect_get_ann_index_asset()
            .times(3)
            .returning(move |_, _, _, _, asset_name| {
                Ok(assets
                    .iter()
                    .find(|a| a.asset_name == asset_name)
                    .unwrap()
                    .data
                    .clone())
            });
        content_manager
            .expect_get_ann_index_blob_infos()
            .return_once(|_, _, _, _| {
                Ok(GetAnnIndexBlobInfosResult {
                    infos: [
                        "0000000000000000000000000000000000000000000000000000000000000003",
                        "0000000000000000000000000000000000000000000000000000000000000004",
                        "0000000000000000000000000000000000000000000000000000000000000005",
                        "0000000000000000000000000000000000000000000000000000000000000006",
                    ]
                    .iter()
                    .map(|b| AnnIndexBlobInfoData {
                        blob_name: BlobName::new(b).unwrap(),
                        chunk_count: 1,
                    })
                    .collect(),
                })
            });

        let services = create_service::<f32x8>(Arc::new(content_manager), true);

        // Simulate a request for a working set with blobs (1, 3, 5, 7)
        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 512],
                contents: vec![0; 1024], // empty contents
            }),
            num_results: 16,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: Some("checkpoint-id-1".to_string()),
                added: [
                    "0000000000000000000000000000000000000000000000000000000000000005",
                    "0000000000000000000000000000000000000000000000000000000000000007",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: [
                    "0000000000000000000000000000000000000000000000000000000000000002",
                    "0000000000000000000000000000000000000000000000000000000000000004",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };

        let (chunk_indices, blob_names, _, _) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        assert_eq!(
            chunk_indices,
            // Same as the trivial case, see above
            vec![0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3]
        );
        assert_eq!(
            blob_names,
            vec![
                "0000000000000000000000000000000000000000000000000000000000000001",
                "0000000000000000000000000000000000000000000000000000000000000001",
                "0000000000000000000000000000000000000000000000000000000000000001",
                "0000000000000000000000000000000000000000000000000000000000000001",
                "0000000000000000000000000000000000000000000000000000000000000003",
                "0000000000000000000000000000000000000000000000000000000000000003",
                "0000000000000000000000000000000000000000000000000000000000000003",
                "0000000000000000000000000000000000000000000000000000000000000003",
                "0000000000000000000000000000000000000000000000000000000000000005",
                "0000000000000000000000000000000000000000000000000000000000000005",
                "0000000000000000000000000000000000000000000000000000000000000005",
                "0000000000000000000000000000000000000000000000000000000000000005",
                "0000000000000000000000000000000000000000000000000000000000000007",
                "0000000000000000000000000000000000000000000000000000000000000007",
                "0000000000000000000000000000000000000000000000000000000000000007",
                "0000000000000000000000000000000000000000000000000000000000000007",
            ]
        );
    }

    #[tokio::test]
    async fn test_search_net_empty_blob_set() {
        let content_manager = Arc::new(MockContentManagerClient::new(
            HashSet::from([
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
                "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string(),
            ]),
            HashMap::from([(
                "checkpoint-id-1".to_string(),
                vec![
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string(),
                ],
            )]),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));

        let services = create_service::<f32x8>(content_manager, false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: Some("checkpoint-id-1".to_string()),
                added: vec![],
                deleted: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        let (chunk_indices, _, _, checkpoint_not_found) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        // Empty result because we have no blobs, but the checkpoint does exist
        assert!(chunk_indices.is_empty());
        assert!(!checkpoint_not_found);
    }

    #[tokio::test]
    async fn test_search_missing_checkpoint() {
        let content_manager = Arc::new(MockContentManagerClient::new(
            HashSet::from([
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
                "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string(),
            ]),
            HashMap::from([(
                "checkpoint-id-1".to_string(),
                vec![
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string(),
                ],
            )]),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));

        let services = create_service::<f32x8>(content_manager, false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: Some("checkpoint-id-0".to_string()),
                added: vec![],
                deleted: vec![],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        let (chunk_indices, _, _, checkpoint_not_found) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        // Empty result because we have no blobs, and the checkpoint doesn't exist
        assert!(chunk_indices.is_empty());
        assert!(checkpoint_not_found);
    }

    #[tokio::test]
    async fn test_search_missing_chunks() {
        let content_manager = Arc::new(MockContentManagerClient::new_with_missing_chunks(
            HashSet::from([
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
                "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string(),
            ]),
            HashSet::from([
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
            ]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));

        let services = create_service::<f32x8>(content_manager, false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: None,
                added: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: vec![],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        let (chunk_indices, _, missing_blob_names, _) = run_request(&services.service, request)
            .await
            .expect("Search failed");
        // We get the existing chunk, but not the other one
        assert_eq!(
            chunk_indices,
            // Just one of the two blobs match (see previous cases for more detail)
            vec![0, 1, 2, 3]
        );
        assert_eq!(
            missing_blob_names,
            vec!["0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a"]
        );
    }

    #[tokio::test]
    async fn test_search_chunks_cancel() -> Result<(), tonic::Status> {
        let content_manager = Arc::new(MockContentManagerClient::new(
            HashSet::from([
                "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string(),
                "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string(),
            ]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));
        let services = create_service::<f32x8>(content_manager.clone(), false);

        let request = SearchChunksRequest {
            query: Some(Tensor {
                datatype: "FP16".to_string(),
                shape: vec![1, 80],
                contents: vec![0; 160], // empty contents
            }),
            num_results: 10,
            transformation_key: "key".to_string(),
            blobs: vec![crate::proto::base::blob_names::Blobs {
                baseline_checkpoint_id: None,
                added: [
                    "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a",
                    "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c",
                ]
                .iter()
                .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                .collect(),
                deleted: vec![],
            }],
            search_timeout_ms: 0,
            ..Default::default()
        };
        let guard = content_manager.block_get_content().await;
        let mut search_futures = vec![];
        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test_with_scopes(vec![TokenScope::ContentRw]);
        for sequence_id in [None, Some(1), Some(3), Some(4), Some(4)] {
            let mut request = request.clone();
            request.sequence_id = sequence_id;
            let mut chunks_request = tonic::Request::new(request);
            request_context.annotate(chunks_request.metadata_mut());
            tenant_info.annotate(chunks_request.extensions_mut());
            let fut = services.service.search_chunks(chunks_request);
            search_futures.push(fut);
        }
        search_futures.push(Box::pin(async move {
            drop(guard);
            Err(tonic::Status::internal("not actually a request"))
        }));
        let all_results = futures::future::join_all(search_futures).await;
        for (i, res) in all_results.into_iter().enumerate() {
            if i == 1 || i == 2 || i == 5 {
                // Expect errors on Some(1), Some(3), and the dummy guard-dropper future
                assert!(res.is_err());
            } else {
                let mut results: Vec<SearchChunksResponse> = vec![];
                let mut rx = res?.into_inner();
                // drain
                while let Some(response) = rx.next().await {
                    let r = response?;
                    results.push(r);
                }
                assert_eq!(results.len(), 9);
            }
        }

        Ok(())
    }

    #[tokio::test]
    async fn test_find_missing_with_invalid_blob_names() -> Result<(), tonic::Status> {
        let blob1 = "0012304eebac896d762555cd1bd7b324d97dd129033ddd99581b024d57e3274a".to_string();
        let blob2 = "015dca3849fa73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string();
        let blob_missing =
            "00000000000a73826c09d90378c8580e6a2bc6c1d544b5d991bf08c4b4e1d62c".to_string();
        let blob_short = blob_missing[1..].to_string();
        let blob_long = blob_missing.clone() + "0";
        let blob_invalid = "invalid".to_string();
        let content_manager = Arc::new(MockContentManagerClient::new(
            HashSet::from([blob1.clone(), blob2.clone()]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));

        let services = create_service::<f32x8>(content_manager, false);
        let request_inner = FindMissingRequest {
            transformation_key: "key".to_string(),
            sub_key: "sub_key".to_string(),
            blob_names: vec![
                blob1.clone(),
                blob2.clone(),
                blob_missing.clone(),
                blob_short.clone(),
                blob_long.clone(),
                blob_invalid.clone(),
            ],
        };

        let request_context = RequestContext::new_for_test();
        let tenant_info = TenantInfo::new_for_test_with_scopes(vec![TokenScope::ContentRw]);
        let mut request = tonic::Request::new(request_inner);
        request_context.annotate(request.metadata_mut());
        tenant_info.annotate(request.extensions_mut());
        let response = services.service.find_missing(request).await?;

        let missing = response.into_inner().missing_blob_names;
        assert_eq!(missing.len(), 4);
        assert!(missing.contains(&blob_missing));
        assert!(missing.contains(&blob_short));
        assert!(missing.contains(&blob_long));
        assert!(missing.contains(&blob_invalid));
        Ok(())
    }

    // To see results: bazel test --test_env="RUSTFLAGS=--release" --test_output=all --test_arg=test_get_checkpoint_blobs_many --test_arg="--nocapture" //services/embeddings_search_host/cpu_server:embeddings_search_cpu_server_test
    #[tokio::test]
    async fn test_get_checkpoint_blobs_many() {
        // Generate 100k blob IDs
        let blobs = (0..100_000)
            .map(|i| sha256::digest(format!("blob{i}").as_bytes()).to_string())
            .sorted()
            .collect::<Vec<String>>();
        let content_manager = Arc::new(MockContentManagerClient::new(
            HashSet::from_iter(blobs.iter().cloned()),
            HashMap::from([("checkpoint-id-1".to_string(), blobs.clone())]),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));

        let services = create_service::<f32x8>(content_manager, false);
        let data = services.service.data;

        {
            let start = Instant::now();
            let _ = data
                .get_checkpoint_blob_states(
                    &RequestContext::new_for_test(),
                    &TenantInfo::new_for_test_with_scopes(vec![TokenScope::ContentRw]),
                    &[crate::proto::base::blob_names::Blobs {
                        baseline_checkpoint_id: Some("checkpoint-id-1".to_string()),
                        added: vec![],
                        deleted: vec![],
                    }],
                    false,
                    &BlobScope {
                        transformation_key: "key".to_string(),
                        sub_key: "sub_key".to_string(),
                    },
                )
                .await
                .expect("Failed to get checkpoint blob names");

            println!(
                "Microbenchmark: get_checkpoint_blob_states took {:?} without cache",
                start.elapsed()
            );
        }
        {
            let start = Instant::now();
            let _ = data
                .get_checkpoint_blob_states(
                    &RequestContext::new_for_test(),
                    &TenantInfo::new_for_test_with_scopes(vec![TokenScope::ContentRw]),
                    &[crate::proto::base::blob_names::Blobs {
                        baseline_checkpoint_id: Some("checkpoint-id-1".to_string()),
                        added: vec![],
                        deleted: vec![],
                    }],
                    false,
                    &BlobScope {
                        transformation_key: "key".to_string(),
                        sub_key: "sub_key".to_string(),
                    },
                )
                .await
                .expect("Failed to get blob names");

            println!(
                "Microbenchmark: get_checkpoint_blob_states took {:?} with cache",
                start.elapsed()
            );
        }
    }
}

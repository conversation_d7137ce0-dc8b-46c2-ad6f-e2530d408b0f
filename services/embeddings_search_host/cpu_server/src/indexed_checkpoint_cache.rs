use std::sync::Arc;
use std::time::Duration;
use std::time::Instant;

use crate::config::Config;
use crate::metrics::{
    CacheType, CACHE_BYTES_COUNT, CACHE_DOWNLOAD_LATENCY_COLLECTOR, CACHE_ENTRY_COUNT,
    CACHE_EVICTION_COUNT_COLLECTOR, CACHE_LOOKUP_COUNT_COLLECTOR,
};
use crate::types::{AnnIndex, CheckpointId, IndexedCheckpoint};
use async_trait::async_trait;
use content_manager_client::ContentManagerClient;
use content_manager_client::GetBestAnnIndexResult;
use itertools::Itertools;
use moka::future::Cache;
use request_context::{RequestContext, TenantId, TenantInfo, EMPTY_TENANT_ID};
use scann_rs::{ScannRsAsset, ScannRsIndex};

/// checkpoint caching and lookup
#[async_trait]
pub trait IndexedCheckpointCache {
    async fn get(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: CheckpointId,
        transformation_key: String,
    ) -> tonic::Result<Option<Arc<IndexedCheckpoint>>>;
}

pub struct IndexedCheckpointCacheImpl {
    data: Arc<IndexedCheckpointCacheDataImpl>,
}

impl IndexedCheckpointCacheImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        Self {
            data: Arc::new(IndexedCheckpointCacheDataImpl::new(config, content_manager)),
        }
    }
}

#[async_trait]
impl IndexedCheckpointCache for IndexedCheckpointCacheImpl {
    async fn get(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: CheckpointId,
        transformation_key: String,
    ) -> tonic::Result<Option<Arc<IndexedCheckpoint>>> {
        let mapping_cache_key = (
            tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID),
            checkpoint_key.clone(),
            transformation_key.to_string(),
        );

        if let Some(()) = self.data.negative_cache.get(&mapping_cache_key).await {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::IndexedCheckpoint.as_str(),
                    "negative",
                    &tenant_info.tenant_name,
                ])
                .inc();
            tracing::info!(
                "Negative ANN index mapping lookup cached for checkpoint {} tkey {}",
                checkpoint_key,
                transformation_key
            );
            return Ok(None);
        }

        if let Some(result) = self
            .data
            .read_indexed_checkpoint_from_cache(tenant_info, &mapping_cache_key)
            .await
        {
            return Ok(Some(result));
        }

        // If not in cache, try to get from content manager
        let index_mapping = match self
            .data
            .read_mapping_from_content_manager(
                request_context,
                tenant_info,
                &checkpoint_key,
                &transformation_key,
            )
            .await?
        {
            Some(index_mapping) => index_mapping,
            // If no mapping, just cache and return the negative result
            None => {
                self.data.negative_cache.insert(mapping_cache_key, ()).await;
                return Ok(None);
            }
        };

        // Spawn a task to populate the index from content manager so it can
        // finish in the background without holding up the request
        let data = self.data.clone();
        let request_context_clone = request_context.clone();
        let tenant_info_clone = tenant_info.clone();
        let transformation_key_clone = transformation_key.clone();
        let join_handle: tokio::task::JoinHandle<tonic::Result<Arc<IndexedCheckpoint>>> =
            tokio::spawn(async move {
                // Get the ANN index, either from the ann cache or from content manager (should exist)
                let ann_index = data
                    .get_ann_index(
                        &request_context_clone,
                        &tenant_info_clone,
                        &index_mapping.index_id,
                        &transformation_key_clone,
                    )
                    .await?;
                // Set the mapping cache entry
                let new_entry = Arc::new(IndexedCheckpoint {
                    index: ann_index,
                    added: index_mapping.added_blobs.into_iter().collect(),
                    deleted: index_mapping.removed_blobs.into_iter().collect(),
                    index_id: index_mapping.index_id,
                });
                data.mapping_cache
                    .insert(mapping_cache_key, new_entry.clone())
                    .await;
                Ok(new_entry)
            });
        // If populating the ann index takes too long, just return None
        match tokio::time::timeout(Duration::from_millis(200), join_handle).await {
            // Success case
            Ok(Ok(Ok(new_entry))) => Ok(Some(new_entry)),
            // Case where the task returned in time but had an error
            // Treat this as Ok(None) to avoid spuriously failing requests,
            // since we can easily fall back to brute force search in this case.
            Ok(Ok(Err(e))) => {
                tracing::warn!(
                    "Error loading ANN index for checkpoint {} tkey {}: {}",
                    checkpoint_key,
                    transformation_key,
                    e
                );
                Ok(None)
            }
            // Case where the task went away for some reason
            Ok(Err(join_err)) => {
                tracing::warn!(
                    "Join error getting ANN index for checkpoint {} tkey {}: {}",
                    checkpoint_key,
                    transformation_key,
                    join_err
                );
                Ok(None)
            }
            // Timeout case
            Err(_) => {
                tracing::warn!(
                    "Timed out getting ANN index for checkpoint {} tkey {}",
                    checkpoint_key,
                    transformation_key
                );
                Ok(None)
            }
        }
    }
}

struct IndexedCheckpointCacheDataImpl {
    // Cache negative lookups on mapping cache for up to a minute (separate lookup so we can have different TTL)
    pub negative_cache: Cache<(TenantId, CheckpointId, String), ()>,
    // Tenant ID, checkpoint ID, transformation key
    pub mapping_cache: Cache<(TenantId, CheckpointId, String), Arc<IndexedCheckpoint>>,
    // Tenant ID, index ID, transformation key
    pub index_cache: Cache<(TenantId, String, String), Arc<AnnIndex>>,
    // Service handle
    content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
}

impl IndexedCheckpointCacheDataImpl {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        let index_cache = Cache::builder()
            .max_capacity(config.indexed_checkpoint_cache_size_bytes)
            .eviction_policy(config.indexed_checkpoint_cache_eviction.into())
            // Add an estimate of the key size
            .weigher(|_, v: &Arc<AnnIndex>| v.size_bytes() + 64)
            .eviction_listener(|_, _, c| {
                let removal_cause = format!("{:?}", c);
                CACHE_EVICTION_COUNT_COLLECTOR
                    .with_label_values(&[CacheType::AnnIndex.as_str(), &removal_cause])
                    .inc();
            })
            .build();

        let mapping_cache = Cache::builder()
            .max_capacity(64 * 1024 * 1024)
            .eviction_policy(moka::policy::EvictionPolicy::lru())
            .weigher(|_, v: &Arc<IndexedCheckpoint>| v.size_bytes() + 64)
            .eviction_listener(|_, _, c| {
                let removal_cause = format!("{:?}", c);
                CACHE_EVICTION_COUNT_COLLECTOR
                    .with_label_values(&[CacheType::IndexedCheckpoint.as_str(), &removal_cause])
                    .inc();
            })
            .time_to_live(Duration::from_secs(900)) // 15 minutes
            .build();
        let negative_cache = Cache::builder()
            .max_capacity(64 * 1024) // 64k entries
            .eviction_policy(moka::policy::EvictionPolicy::lru())
            .time_to_live(Duration::from_secs(60))
            .build();

        IndexedCheckpointCacheDataImpl {
            negative_cache,
            mapping_cache,
            index_cache,
            content_manager,
        }
    }

    async fn read_indexed_checkpoint_from_cache(
        &self,
        tenant_info: &TenantInfo,
        cache_key: &(TenantId, CheckpointId, String),
    ) -> Option<Arc<IndexedCheckpoint>> {
        tracing::info!(
            "Getting ANN index mapping for checkpoint {} tkey {} from cache",
            cache_key.1,
            cache_key.2
        );

        // First try to get from cache
        if let Some(entry) = self.mapping_cache.get(cache_key).await {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::IndexedCheckpoint.as_str(),
                    "hit",
                    &tenant_info.tenant_name,
                ])
                .inc();
            tracing::info!(
                "Got ANN index mapping for checkpoint {} tkey {} to index {} from cache with {} blobs added, {} removed",
                cache_key.1,
                cache_key.2,
                entry.index_id,
                entry.added.len(),
                entry.deleted.len()
            );
            return Some(entry);
        } else {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::IndexedCheckpoint.as_str(),
                    "miss",
                    &tenant_info.tenant_name,
                ])
                .inc();
        }

        CACHE_ENTRY_COUNT
            .with_label_values(&[CacheType::IndexedCheckpoint.as_str()])
            .set(self.mapping_cache.entry_count() as f64);
        CACHE_BYTES_COUNT
            .with_label_values(&[CacheType::IndexedCheckpoint.as_str()])
            .set(self.mapping_cache.weighted_size() as f64);

        None
    }

    async fn read_mapping_from_content_manager(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        checkpoint_key: &CheckpointId,
        transformation_key: &str,
    ) -> tonic::Result<Option<GetBestAnnIndexResult>> {
        tracing::info!(
            "Getting ANN index mapping for checkpoint {} tkey {} from content manager",
            checkpoint_key,
            transformation_key
        );

        let download_start = Instant::now();
        let index_mapping = match self
            .content_manager
            .get_best_ann_index(
                request_context,
                &tenant_info.tenant_id,
                transformation_key,
                checkpoint_key,
            )
            .await
        {
            Ok(index_mapping) => {
                tracing::info!(
                    "Got ANN index mapping for checkpoint {} tkey {} to index {} from content manager with {} blobs added, {} removed",
                    checkpoint_key,
                    transformation_key,
                    index_mapping.index_id,
                    index_mapping.added_blobs.len(),
                    index_mapping.removed_blobs.len()
                );
                Some(index_mapping)
            }
            Err(e) => {
                if e.code() == tonic::Code::NotFound {
                    tracing::info!(
                        "No ANN index found for checkpoint {} tkey {}",
                        checkpoint_key,
                        transformation_key
                    );
                    None
                } else {
                    tracing::error!("Failed to get ANN index for checkpoint {} tkey {} from content manager: {}", checkpoint_key, transformation_key, e);
                    return Err(e);
                }
            }
        };
        CACHE_DOWNLOAD_LATENCY_COLLECTOR
            .with_label_values(&[
                CacheType::IndexedCheckpoint.as_str(),
                &tenant_info.tenant_name,
            ])
            .observe(download_start.elapsed().as_secs_f64());
        Ok(index_mapping)
    }

    async fn get_ann_index(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        index_id: &str,
        transformation_key: &str,
    ) -> tonic::Result<Arc<AnnIndex>> {
        tracing::info!(
            "Getting ANN index {} tkey {} from cache",
            index_id,
            transformation_key
        );

        let cache_key = (
            tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID),
            index_id.to_string(),
            transformation_key.to_string(),
        );
        if let Some(result) = self.index_cache.get(&cache_key).await {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::AnnIndex.as_str(),
                    "hit",
                    &tenant_info.tenant_name,
                ])
                .inc();
            tracing::info!(
                "Got ANN index {} tkey {} from cache",
                index_id,
                transformation_key
            );
            return Ok(result);
        } else {
            CACHE_LOOKUP_COUNT_COLLECTOR
                .with_label_values(&[
                    CacheType::AnnIndex.as_str(),
                    "miss",
                    &tenant_info.tenant_name,
                ])
                .inc();
        }
        CACHE_ENTRY_COUNT
            .with_label_values(&[CacheType::AnnIndex.as_str()])
            .set(self.index_cache.entry_count() as f64);
        CACHE_BYTES_COUNT
            .with_label_values(&[CacheType::AnnIndex.as_str()])
            .set(self.index_cache.weighted_size() as f64);

        tracing::info!(
            "Getting ANN index {} tkey {} from content manager",
            index_id,
            transformation_key
        );
        let download_start = Instant::now();

        // If not in cache, try to get from content manager and construct fresh
        let mut index_assets = vec![];
        // third_party/scann_rs/scann_rs.cc for the list of asset names
        for asset_name in ["scann_config.pb", "ah_codebook.pb", "hashed_dataset.npy"] {
            // Enable this line to test the background population logic. DON'T CHECK IT IN ENABLED.
            // tokio::time::sleep(Duration::from_millis(300)).await;
            let asset_data = self
                .content_manager
                .get_ann_index_asset(
                    request_context,
                    &tenant_info.tenant_id,
                    transformation_key,
                    index_id,
                    asset_name,
                )
                .await?;
            tracing::info!(
                "Got ANN asset {} for index {} tkey {} from content manager: {} bytes",
                asset_name,
                index_id,
                transformation_key,
                asset_data.len(),
            );
            index_assets.push(ScannRsAsset {
                asset_name: asset_name.to_string(),
                data: asset_data,
            });
        }

        let index_blob_infos_resp = self
            .content_manager
            .get_ann_index_blob_infos(
                request_context,
                &tenant_info.tenant_id,
                transformation_key,
                index_id,
            )
            .await?;

        let index_blob_infos = index_blob_infos_resp
            .infos
            .into_iter()
            .map(|info| (info.blob_name, info.chunk_count as usize))
            .collect_vec();
        tracing::info!(
            "Got ANN blob infos for index {} tkey {} from content manager: {} blobs",
            index_id,
            transformation_key,
            index_blob_infos.len()
        );

        // TODO: is the training_threads setting meaningful here??
        let new_scann_index = Arc::new(ScannRsIndex::new_from_assets(index_assets, 2)?);

        // TODO chunk count management
        let new_ann_index = Arc::new(AnnIndex::new(index_blob_infos.as_slice(), new_scann_index));
        CACHE_DOWNLOAD_LATENCY_COLLECTOR
            .with_label_values(&[CacheType::AnnIndex.as_str(), &tenant_info.tenant_name])
            .observe(download_start.elapsed().as_secs_f64());

        self.index_cache
            .insert(cache_key, new_ann_index.clone())
            .await;
        Ok(new_ann_index)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    use blob_names::BlobName;
    use content_manager_client::{
        AnnIndexBlobInfoData, GetAnnIndexBlobInfosResult, GetBestAnnIndexResult,
        MockContentManagerClient,
    };
    use mockall::predicate;
    use scann_rs::ScannFactory;

    #[tokio::test]
    async fn test_indexed_checkpoint_get() {
        let mut mock = MockContentManagerClient::new();
        // Produce a fake ANN index
        let mut scann_factory = ScannFactory::new(512, 1).unwrap();
        scann_factory.score_ah(8, 0.2, 100);
        let scann_index = scann_factory.build(&[1.0; 32 * 512], 32).unwrap();
        let assets = scann_index.serialize().unwrap();

        // Set up mock values
        mock.expect_get_best_ann_index()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("transformation-key"),
                predicate::eq("checkpoint-id-1"),
            )
            .return_once(|_, _, _, _| {
                Ok(GetBestAnnIndexResult {
                    index_id: "index-id-1".to_string(),
                    added_blobs: vec![BlobName::from_bytes(&[43; 32]).unwrap()],
                    removed_blobs: vec![BlobName::from_bytes(&[42; 32]).unwrap()],
                })
            });
        mock.expect_get_best_ann_index()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("wrong-transformation-key"),
                predicate::eq("checkpoint-id-1"),
            )
            .return_once(|_, _, _, _| Err(tonic::Status::not_found("no index found")));
        mock.expect_get_ann_index_asset()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("transformation-key"),
                predicate::eq("index-id-1"),
                predicate::always(),
            )
            .returning(move |_, _, _, _, asset_name| {
                Ok(assets
                    .iter()
                    .find(|a| a.asset_name == asset_name)
                    .unwrap()
                    .data
                    .clone())
            });
        mock.expect_get_ann_index_blob_infos()
            .with(
                predicate::always(),
                predicate::always(),
                predicate::eq("transformation-key"),
                predicate::eq("index-id-1"),
            )
            .return_once(|_, _, _, _| {
                Ok(GetAnnIndexBlobInfosResult {
                    infos: vec![AnnIndexBlobInfoData {
                        blob_name: BlobName::from_bytes(&[42; 32]).unwrap(),
                        chunk_count: 1,
                    }],
                })
            });

        let cache = IndexedCheckpointCacheImpl::new(Config::default(), Arc::new(mock));
        let tenant_info = TenantInfo::new_for_test();
        let request_context = RequestContext::new_for_test();

        // With checkpoint set, result is returned
        {
            let result = cache
                .get(
                    &request_context,
                    &tenant_info,
                    "checkpoint-id-1".to_string(),
                    "transformation-key".to_string(),
                )
                .await;
            let indexed_checkpoint_entry = result.unwrap().unwrap();
            assert_eq!(
                indexed_checkpoint_entry.added.first().unwrap().as_bytes(),
                &[43; 32]
            );
        }

        // Send request again to test positive caching
        {
            let result = cache
                .get(
                    &request_context,
                    &tenant_info,
                    "checkpoint-id-1".to_string(),
                    "transformation-key".to_string(),
                )
                .await;
            let indexed_checkpoint_entry = result.unwrap().unwrap();
            assert_eq!(
                indexed_checkpoint_entry.added.first().unwrap().as_bytes(),
                &[43; 32]
            );
        }

        // With wrong transformation key, no result
        {
            let result = cache
                .get(
                    &request_context,
                    &tenant_info,
                    "checkpoint-id-1".to_string(),
                    "wrong-transformation-key".to_string(),
                )
                .await;
            assert!(result.unwrap().is_none());
        }

        // Send request again to test negative caching
        {
            let result = cache
                .get(
                    &request_context,
                    &tenant_info,
                    "checkpoint-id-1".to_string(),
                    "wrong-transformation-key".to_string(),
                )
                .await;
            assert!(result.unwrap().is_none());
        }
    }

    // Enable this and the tokio::sleep above to test background population.
    // The lack of true async support in mockall makes it impractical to test otherwise.

    // #[tokio::test]
    // async fn test_background_populate_ann_index_cache() {
    //     let mut mock = MockContentManagerClient::new();
    //     // Produce a fake ANN index
    //     let mut scann_factory = ScannFactory::new(512, 1).unwrap();
    //     scann_factory.score_ah(8, 0.2, 100);
    //     let scann_index = scann_factory.build(&[1.0; 32 * 512], 32).unwrap();
    //     let assets = scann_index.serialize().unwrap();
    //     for asset in assets.iter() {
    //         println!("Asset: {}", asset.asset_name);
    //     }

    //     // Set up mock values
    //     mock.expect_get_best_ann_index()
    //         .with(
    //             predicate::always(),
    //             predicate::always(),
    //             predicate::eq("transformation-key"),
    //             predicate::eq("checkpoint-id-1"),
    //         )
    //         .returning(|_, _, _, _| {
    //             Ok(GetBestAnnIndexResult {
    //                 index_id: "index-id-1".to_string(),
    //                 added_blobs: vec![BlobName::from_bytes(&[43; 32]).unwrap()],
    //                 removed_blobs: vec![BlobName::from_bytes(&[42; 32]).unwrap()],
    //             })
    //         });
    //     mock.expect_get_ann_index_asset()
    //         .with(
    //             predicate::always(),
    //             predicate::always(),
    //             predicate::eq("transformation-key"),
    //             predicate::eq("index-id-1"),
    //             predicate::always(),
    //         )
    //         .returning(move |_, _, _, _, asset_name| {
    //             Ok(assets
    //                 .iter()
    //                 .find(|a| a.asset_name == asset_name)
    //                 .unwrap()
    //                 .data
    //                 .clone())
    //         });
    //     mock.expect_get_ann_index_blob_infos()
    //         .with(
    //             predicate::always(),
    //             predicate::always(),
    //             predicate::eq("transformation-key"),
    //             predicate::eq("index-id-1"),
    //         )
    //         .return_once(|_, _, _, _| {
    //             Ok(GetAnnIndexBlobInfosResult {
    //                 infos: vec![AnnIndexBlobInfoData {
    //                     blob_name: BlobName::from_bytes(&[42; 32]).unwrap(),
    //                     chunk_count: 1,
    //                 }],
    //             })
    //         });

    //     let cache = IndexedCheckpointCacheImpl::new(Config::default(), Arc::new(mock));
    //     let tenant_info = TenantInfo::new_for_test();
    //     let request_context = RequestContext::new_for_test();

    //     // First call times out index creation and returns no entry
    //     {
    //         let result = cache
    //             .get(
    //                 &request_context,
    //                 &tenant_info,
    //                 "checkpoint-id-1".to_string(),
    //                 "transformation-key".to_string(),
    //             )
    //             .await;
    //         assert!(result.unwrap().is_none());
    //     }

    //     // After waiting, second call returns the populated entry
    //     tokio::time::sleep(std::time::Duration::from_millis(800)).await;
    //     {
    //         let result = cache
    //             .get(
    //                 &request_context,
    //                 &tenant_info,
    //                 "checkpoint-id-1".to_string(),
    //                 "transformation-key".to_string(),
    //             )
    //             .await;
    //         let indexed_checkpoint_entry = result.unwrap().unwrap();
    //         assert_eq!(
    //             indexed_checkpoint_entry.added.first().unwrap().as_bytes(),
    //             &[43; 32]
    //         );
    //     }
    // }
}

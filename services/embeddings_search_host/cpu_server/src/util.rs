use std::fmt::Debug;

use blob_names::BlobName;
use content_manager_client::BlobScope;
use request_context::TenantInfo;

#[derive(C<PERSON>, PartialEq, Eq, Debug)]
pub struct TenantBlobContentKeys {
    pub request_id: request_context::RequestId,
    pub tenant_info: TenantInfo,
    pub blob_scope: BlobScope,
    pub blob_names: Vec<BlobName>,
}

#[cfg(test)]
pub mod tests {
    use super::*;
    use async_trait::async_trait;
    use prost_wkt_types::Timestamp;
    use secrecy::{SecretString, SecretVec};
    use std::hash::{Hash, Hasher};
    use std::time::Instant;
    use std::{cmp::Ordering, collections::hash_map::DefaultHasher};

    use crate::test::get_test_data_path;
    use blob_names::BlobName;

    use blob_names_rs_proto::base::blob_names as blob_names_proto;
    use content_manager_client::{
        AnnIndexBlobInfoData, ContentManagerClient, GetAnnIndexBlobInfosResult,
        GetBestAnnIndexResult, UploadContent,
    };
    use content_manager_rs_proto::content_manager::{
        self, AddAnnIndexMappingResponse, BatchBlobInfoResponse, BatchGetBlobInfoResponse,
        BlobContentKey, GetBlobInfoResponse, UploadAnnIndexAssetsResponse,
        UploadAnnIndexBlobInfosResponse,
    };
    use futures::StreamExt;
    use itertools::Itertools;
    use request_context::{RequestContext, TenantId};
    use std::collections::{HashMap, HashSet};
    use tokio::sync::mpsc;
    use tokio::sync::mpsc::Receiver;

    #[test]
    fn test_blob_scope_eq() {
        let key1 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        let key2 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        assert_eq!(key1, key2);

        let key3 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey2".to_string(),
        };
        assert_ne!(key1, key3);
    }

    #[test]
    fn test_blob_scope_ord() {
        let key1 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        let key2 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey2".to_string(),
        };
        assert_eq!(key1.cmp(&key2), Ordering::Less);

        let key3 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        assert_eq!(key1.cmp(&key3), Ordering::Equal);
    }

    fn hash<T: Hash>(t: &T) -> u64 {
        let mut s = DefaultHasher::new();
        t.hash(&mut s);
        s.finish()
    }

    #[test]
    fn test_blob_scope_hash() {
        let key1 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        let key2 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey2".to_string(),
        };
        assert_ne!(hash(&key1), hash(&key2));

        let key3 = BlobScope {
            transformation_key: "key1".to_string(),
            sub_key: "subkey1".to_string(),
        };
        assert_eq!(hash(&key1), hash(&key3));
    }

    pub struct MockGetContentRequest {
        pub request_id: request_context::RequestId,
        pub blobs: Vec<(BlobName, BlobScope)>,
    }

    pub struct MockContentManagerClient {
        pub last_get_content_request: std::sync::Mutex<Option<MockGetContentRequest>>,
        pub requested_blob_counter: std::sync::atomic::AtomicUsize,
        pub last_checkpoint_blob_names_request:
            std::sync::Mutex<Option<request_context::RequestId>>,
        pub blobs: HashSet<String>,
        // A subset of blobs
        pub blobs_missing_chunks: HashSet<String>,
        pub checkpoints: HashMap<String, Vec<String>>,
        pub content: Vec<u8>,
        pub get_content_lock: tokio::sync::RwLock<()>,
    }

    impl MockContentManagerClient {
        pub fn new(
            blobs: HashSet<String>,
            checkpoints: HashMap<String, Vec<String>>,
            filename: &str,
        ) -> MockContentManagerClient {
            MockContentManagerClient {
                last_get_content_request: std::sync::Mutex::new(None),
                requested_blob_counter: std::sync::atomic::AtomicUsize::new(0),
                last_checkpoint_blob_names_request: std::sync::Mutex::new(None),
                blobs_missing_chunks: HashSet::new(),
                blobs,
                checkpoints,
                // Simulate blob content (all with the same npy data)
                content: std::fs::read(get_test_data_path(filename)).unwrap(),
                get_content_lock: tokio::sync::RwLock::new(()),
            }
        }

        pub fn new_with_missing_chunks(
            blobs: HashSet<String>,
            // Simulate blobs with infos but no chunks, a subset of the previous blobs list
            blobs_missing_chunks: HashSet<String>,
            checkpoints: HashMap<String, Vec<String>>,
            filename: &str,
        ) -> MockContentManagerClient {
            MockContentManagerClient {
                last_get_content_request: std::sync::Mutex::new(None),
                requested_blob_counter: std::sync::atomic::AtomicUsize::new(0),
                last_checkpoint_blob_names_request: std::sync::Mutex::new(None),
                blobs,
                blobs_missing_chunks,
                checkpoints,
                // Simulate blob content (all with the same npy data)
                content: std::fs::read(get_test_data_path(filename)).unwrap(),
                get_content_lock: tokio::sync::RwLock::new(()),
            }
        }

        pub fn get_requested_blob_count(&self) -> usize {
            self.requested_blob_counter
                .load(std::sync::atomic::Ordering::Relaxed)
        }

        pub async fn block_get_content(&self) -> tokio::sync::RwLockWriteGuard<()> {
            self.get_content_lock.write().await
        }

        pub fn clear(&self) {
            let mut d = self.last_get_content_request.lock().unwrap();
            *d = None;
            let mut d = self.last_checkpoint_blob_names_request.lock().unwrap();
            *d = None;
        }
    }

    #[async_trait]
    impl ContentManagerClient for MockContentManagerClient {
        async fn upload_blob_content(
            &self,
            _request_context: &RequestContext,
            _path: &SecretString,
            _content: &SecretVec<u8>,
            _deadline: Option<Instant>,
        ) -> Result<String, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn batch_upload_blob_content(
            &self,
            _request_context: &RequestContext,
            _blobs: Vec<UploadContent>,
            _deadline: Option<Instant>,
        ) -> Result<Vec<BlobName>, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn find_missing_blobs(
            &self,
            _request_context: &RequestContext,
            _blob_names: &[String],
            _deadline: Option<Instant>,
        ) -> Result<Vec<String>, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn checkpoint_blobs(
            &self,
            _request_context: &RequestContext,
            _blobs: &blob_names_proto::Blobs,
            _deadline: Option<Instant>,
        ) -> Result<String, tonic::Status> {
            Err(tonic::Status::unimplemented("not implemented"))
        }

        async fn get_blob_info(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _blob_content_key: BlobContentKey,
        ) -> tonic::Result<GetBlobInfoResponse> {
            Ok(GetBlobInfoResponse {
                content_hash: "mock_hash".into(),
                size: 10,
                metadata: vec![],
                informed_transformation_keys: vec![],
                uploaded_transformation_keys: vec![],
                time: Some(Timestamp {
                    seconds: 0,
                    nanos: 0,
                }),
            })
        }

        async fn batch_get_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            blob_content_keys: Vec<BlobContentKey>,
        ) -> tonic::Result<BatchGetBlobInfoResponse> {
            let blob_infos = blob_content_keys
                .into_iter()
                .map(|blob_content_key| BatchBlobInfoResponse {
                    blob_content_key: Some(blob_content_key),
                    blob_info: Some(GetBlobInfoResponse {
                        content_hash: "mock_hash".into(),
                        size: 10,
                        metadata: vec![],
                        informed_transformation_keys: vec![],
                        uploaded_transformation_keys: vec![],
                        time: Some(Timestamp {
                            seconds: 0,
                            nanos: 0,
                        }),
                    }),
                })
                .collect_vec();
            Ok(BatchGetBlobInfoResponse { blob_infos })
        }

        async fn get_content(
            &self,
            request_context: &RequestContext,
            tenant_id: &Option<TenantId>,
            blob_scope: &BlobScope,
            blob_names: &[BlobName],
            deadline: Option<Instant>,
        ) -> Receiver<tonic::Result<content_manager::BatchGetContentResponse>> {
            let _guard = self.get_content_lock.read().await;
            self.requested_blob_counter
                .fetch_add(blob_names.len(), std::sync::atomic::Ordering::Relaxed);

            self.get_content_multiple_scopes(
                request_context,
                tenant_id,
                blob_names
                    .iter()
                    .map(|b| (b.clone(), blob_scope.clone()))
                    .collect(),
                deadline,
            )
            .await
        }

        async fn get_content_multiple_scopes(
            &self,
            request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            blobs: Vec<(BlobName, BlobScope)>,
            deadline: Option<Instant>,
        ) -> Receiver<tonic::Result<content_manager::BatchGetContentResponse>> {
            let _guard = self.get_content_lock.read().await;
            self.requested_blob_counter
                .fetch_add(blobs.len(), std::sync::atomic::Ordering::Relaxed);

            {
                let mut d = self.last_get_content_request.lock().unwrap();
                *d = Some(MockGetContentRequest {
                    request_id: request_context.request_id(),
                    blobs: blobs.clone(),
                });
            }

            let (tx, rx) =
                mpsc::channel::<tonic::Result<content_manager::BatchGetContentResponse>>(1024);

            for (blob_name, blob_scope) in blobs {
                // Simulate cancellation
                if let Some(d) = deadline {
                    if Instant::now() > d {
                        let _ = tx.send(Err(tonic::Status::cancelled("cancelled"))).await;
                    }
                }

                // Simulate missing blob keys, or a blob that has embeddings but no chunks
                if !self.blobs.contains(&blob_name.to_string())
                    || (blob_scope.sub_key == "embeddings.npy"
                        && self.blobs_missing_chunks.contains(&blob_name.to_string()))
                {
                    let _ = tx.send(Ok(content_manager::BatchGetContentResponse {
                            response: Some(
                                content_manager::batch_get_content_response::Response::NotFoundContent(
                                    content_manager::BatchGetContentNotFound {
                                        blob_name: blob_name.to_string(),
                                        transformation_key: blob_scope.transformation_key.clone(),
                                        sub_key: blob_scope.sub_key.clone(),
                                    },
                                ),
                            ),
                        })).await;
                    continue;
                }

                let _ = tx
                    .send(Ok(content_manager::BatchGetContentResponse {
                        response: Some(
                            content_manager::batch_get_content_response::Response::FinalContent(
                                content_manager::BatchGetContentFinalContent {
                                    blob_name: blob_name.to_string(),
                                    transformation_key: blob_scope.transformation_key.clone(),
                                    sub_key: blob_scope.sub_key.clone(),
                                    content: self.content.clone(),
                                    final_hash: sha256::digest(&self.content),
                                    metadata: vec![content_manager::BlobMetadata {
                                        key: "path".to_string(),
                                        value: "services/to/blob".to_string(),
                                    }],
                                },
                            ),
                        ),
                    }))
                    .await;
            }
            rx
        }

        async fn get_checkpoint_blob_names(
            &self,
            request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            checkpoint_id: &str,
        ) -> Receiver<tonic::Result<content_manager::GetAllBlobsFromCheckpointResponse>> {
            {
                let mut d = self.last_checkpoint_blob_names_request.lock().unwrap();
                *d = Some(request_context.request_id());
            }
            let blobs: Vec<tonic::Result<content_manager::GetAllBlobsFromCheckpointResponse>> =
                if let Some(blobs) = self.checkpoints.get(checkpoint_id) {
                    vec![Ok(content_manager::GetAllBlobsFromCheckpointResponse {
                        blob_names: blobs
                            .iter()
                            .map(|b| Vec::from(BlobName::new(b).unwrap().as_bytes()))
                            .collect(),
                    })]
                } else {
                    vec![Err(tonic::Status::not_found("Checkpoint does not exist"))]
                };
            let mut stream = Box::pin(tokio_stream::iter(blobs));
            let (tx, rx) = mpsc::channel::<
                tonic::Result<content_manager::GetAllBlobsFromCheckpointResponse>,
            >(128);
            tokio::spawn(async move {
                while let Some(message) = stream.next().await {
                    let _ = tx.send(message).await;
                }
            });
            rx
        }

        async fn get_best_ann_index(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _checkpoint_id: &str,
        ) -> tonic::Result<GetBestAnnIndexResult> {
            Err(tonic::Status::not_found("no index found"))
        }

        async fn get_ann_index_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
        ) -> tonic::Result<GetAnnIndexBlobInfosResult> {
            Ok(GetAnnIndexBlobInfosResult { infos: vec![] })
        }

        async fn get_ann_index_asset(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _sub_key: &str,
        ) -> tonic::Result<Vec<u8>> {
            Err(tonic::Status::not_found("no asset found"))
        }

        async fn add_ann_index_mapping(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _checkpoint_id: &str,
            _index_id: &str,
            _added_blobs: &[BlobName],
            _removed_blobs: &[BlobName],
        ) -> tonic::Result<AddAnnIndexMappingResponse> {
            Ok(AddAnnIndexMappingResponse {})
        }

        async fn upload_ann_index_blob_infos(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _infos: &[AnnIndexBlobInfoData],
        ) -> tonic::Result<UploadAnnIndexBlobInfosResponse> {
            Ok(UploadAnnIndexBlobInfosResponse {})
        }

        async fn upload_ann_index_assets(
            &self,
            _request_context: &RequestContext,
            _tenant_id: &Option<TenantId>,
            _transformation_key: &str,
            _index_id: &str,
            _assets: &[(String, Vec<u8>)],
        ) -> tonic::Result<UploadAnnIndexAssetsResponse> {
            Ok(UploadAnnIndexAssetsResponse {})
        }
    }
}

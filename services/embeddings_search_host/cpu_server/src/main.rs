use std::{net::SocketAddr, sync::Arc, time::Duration};

use crate::checkpoint_cache::{CheckpointCache, CheckpointCacheImpl};
use crate::chunk_cache::{Chunk<PERSON><PERSON>, ChunkCacheImpl};
use crate::dot_math::SimdData;
use crate::embedding_cache::{EmbeddingCache, EmbeddingCacheImpl};
use crate::indexed_checkpoint_cache::{IndexedCheckpointCache, IndexedCheckpointCacheImpl};
use clap::Parser;
use config::CliArguments;
use grpc_auth::GrpcAuthMiddlewareLayer;
use grpc_metrics::MetricsMiddlewareLayer;
use grpc_tls_config::{get_client_tls_creds, get_server_tls_creds};
use numpy::f16x16_simd::f16x16;
use numpy::f16x16_simd::is_f16x16_supported;
use numpy::f32x8_simd::f32x8;
use struct_logging::setup_struct_logging;
use token_exchange_client::{TokenExchangeClientImpl, TokenGrpcAuth};
use tokio::signal::unix::{signal, SignalKind};
use tokio::time::timeout;
use tonic::transport::{Channel, Server};

use request_insight_publisher::{RequestInsightPublisherConfig, RequestInsightPublisherImpl};
use tower::ServiceBuilder;
use tracing_tonic::client::TracingService;
use working_set_client::WorkingSetClientImpl;

use crate::config::Config;
use crate::metrics::{ACTIVE_REQUESTS_COLLECTOR, RESPONSE_LATENCY_COLLECTOR};

mod background;
mod cache_replicator;
mod checkpoint_cache;
mod chunk_cache;
mod config;
mod dot_math;
mod embedding_cache;
mod embeddings_search_service;
mod index_search;
mod indexed_checkpoint_cache;
mod metrics;
mod peer_embeddings_search_service;
mod test;
mod types;
mod util;

pub mod proto {
    pub mod embeddings_search {
        tonic::include_proto!("embeddings_search");
    }
    pub use content_manager_rs_proto::content_manager;
    pub mod share {
        tonic::include_proto!("share");
    }
    pub mod tensor {
        tonic::include_proto!("tensor");
    }
    pub mod request_insight {
        tonic::include_proto!("request_insight");
    }
    pub mod base {
        pub use blob_names_rs_proto::base::blob_names;

        pub mod diff_utils {
            tonic::include_proto!("base.diff_utils");
        }
    }
    pub mod agents {
        tonic::include_proto!("agents");
    }
    pub mod edit {
        tonic::include_proto!("edit");
    }
    pub mod chat {
        tonic::include_proto!("chat");
    }

    pub mod completion {
        tonic::include_proto!("completion");
    }

    pub mod stream_mux {
        tonic::include_proto!("stream_mux");
    }

    pub mod next_edit {
        tonic::include_proto!("next_edit");
    }

    pub mod slackbot {
        tonic::include_proto!("slackbot");
    }

    pub mod glean {
        tonic::include_proto!("glean");
    }

    #[allow(clippy::module_inception)]
    pub mod github_event {
        tonic::include_proto!("github_event");
    }

    pub mod google {
        pub mod rpc {
            tonic::include_proto!("google.rpc");
        }
    }

    pub mod auth_entities {
        tonic::include_proto!("auth_entities");
    }

    pub mod services {
        pub mod tenant {
            tonic::include_proto!("services.tenant");
        }
    }

    pub mod remote_agents {
        tonic::include_proto!("remote_agents");
    }

    pub(crate) const FILE_DESCRIPTOR_SET: &[u8] =
        tonic::include_file_descriptor_set!("embeddings_search_descriptor");
}

async fn create_peer_embeddings_search_client(
    endpoint: &str,
    client_tls_config: Option<tonic::transport::ClientTlsConfig>,
    request_timeout: Duration,
) -> Result<
    crate::proto::embeddings_search::peer_embeddings_search_client::PeerEmbeddingsSearchClient<
        TracingService,
    >,
    tonic::transport::Error,
> {
    tracing::info!("Creating peer_embeddings_search client: endpoint={endpoint}");
    let endpoint = endpoint.to_string();

    /*
    If we fail to connect to any peer within 5 seconds, we should give up and
    complete startup even with an empty cache. The assumption in this case is
    that this may be the only healthy pod in the namespace and an empty cache is
    better than no service.

    If we can connect with a peer, we have a configurable timeout to replicate
    cache entries. So long as we are connected to the peer and receiving
    results, we assume that at least that peer is serving requests in the
    namespace and we do not need to report as "ready" with a cold cache.
    */
    let mut builder = Channel::from_shared(endpoint)
        .expect("Failed to parse endpoint")
        .connect_timeout(Duration::from_secs(5))
        .timeout(request_timeout);
    if let Some(tls_config) = client_tls_config {
        builder = builder.tls_config(tls_config.clone())?;
    }
    let channel = builder.connect().await?;

    let service = ServiceBuilder::new()
        .layer_fn(TracingService::new)
        .service(channel);

    let client =
        crate::proto::embeddings_search::peer_embeddings_search_client::PeerEmbeddingsSearchClient::new(
            service,
        );
    tracing::info!("Created peer_embeddings_search client");
    Ok(client)
}

pub const REPLICA_CACHE_ON_STARTUP: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("embeddings_search_replicate_cache_on_startup", false);

async fn run<T: SimdData>(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    let config = Config::read(&args.config_file).expect("Failed to read config file");
    tracing::info!("{:?}", config);

    let namespace = match std::env::var("POD_NAMESPACE") {
        Ok(name) => name,
        Err(_) => panic!("POD_NAMESPACE environment variable must be set."),
    };

    let feature_flags = feature_flags::setup(
        "embeddings_search_host_cpu_server",
        "0.0.0",
        config.feature_flags_sdk_key_path.as_ref(),
        config.dynamic_feature_flags_endpoint.as_deref(),
    )
    .await;

    let feature_flag_registry = feature_flags::new_registry();

    // Populate defaults from JSON config, mostly for testing. LaunchDarkly will take precedence when available.
    if let Some(ref config) = config.dynamic_feature_flags {
        feature_flags::populate_from_hashmap(&feature_flags, config, &feature_flag_registry)?;
    }

    let (_health_reporter, health_service) = tonic_health::server::health_reporter();

    metrics_server::setup_default_metrics();

    let metrics_server = metrics_server::setup_metrics_http_server(
        &config.metrics_server_bind_address,
        config.metrics_server_port,
    )?;

    let server_tls_config =
        get_server_tls_creds(&config.server_mtls_config).expect("Failed to create TLS config");

    let addr: SocketAddr = config.bind_address.parse()?;
    let server = match server_tls_config {
        None => Server::builder(),
        Some(server_tls_config) => Server::builder()
            .tls_config(server_tls_config)
            .expect("Failed to create rpc server"),
    };

    rayon::ThreadPoolBuilder::new()
        .num_threads(config.thread_count as usize)
        // The default 2 MB stacks were hitting stack overflows due to how they scheduled
        // back-to-back CPU work. Just give them more memory for now since we have plenty.
        .stack_size(8 * 1024 * 1024)
        .thread_name(|i| format!("index-search-thread-{}", i))
        .build_global()
        .unwrap();

    let client_tls_config =
        get_client_tls_creds(&config.client_mtls_config).expect("Failed to create TLS config");

    let content_manager = Arc::new(content_manager_client::ContentManagerClientImpl::new(
        &config.content_manager_endpoint,
        client_tls_config.clone(),
        Some(Duration::from_secs_f32(
            config.content_manager_request_timeout_s,
        )),
    ));

    let request_insight_publisher_config =
        RequestInsightPublisherConfig::read(&args.request_insight_publisher_config_file)
            .expect("Failed to read publisher config file");
    let request_insight_publisher =
        Arc::new(RequestInsightPublisherImpl::new(request_insight_publisher_config).await);

    let central_client_tls_config = get_client_tls_creds(&config.central_client_mtls_config)
        .expect("Failed to create TLS config");

    let token_exchange_client = Arc::new(TokenExchangeClientImpl::new(
        &config.auth_config.token_exchange_endpoint,
        namespace.to_string(),
        central_client_tls_config,
        Duration::from_secs_f32(config.auth_config.token_exchange_request_timeout_s),
    ));

    let working_set_client = Arc::new(WorkingSetClientImpl::new(
        &config.working_set_endpoint,
        client_tls_config.clone(),
    ));

    let grpc_auth = Arc::new(TokenGrpcAuth::new(
        token_exchange_client.clone(),
        vec![request_context::TokenScope::ContentR],
    ));

    let cache_task_monitor: Option<tokio_metrics_collector::TaskMonitor> = {
        let monitor = tokio_metrics_collector::TaskMonitor::new();
        match tokio_metrics_collector::default_task_collector()
            .add("index_cache_task", monitor.clone())
        {
            Ok(_) => Some(monitor),
            Err(e) => {
                tracing::error!("Failed to register index_cache_task monitor: {:?}", e);
                None
            }
        }
    };

    let embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static> = Arc::new(
        EmbeddingCacheImpl::new(config.clone(), content_manager.clone(), cache_task_monitor),
    );

    let chunk_cache: Arc<dyn ChunkCache + Send + Sync + 'static> =
        Arc::new(ChunkCacheImpl::new(config.clone(), content_manager.clone()));

    let checkpoint_cache: Arc<dyn CheckpointCache + Send + Sync + 'static> = Arc::new(
        CheckpointCacheImpl::new(config.clone(), content_manager.clone()),
    );

    let indexed_checkpoint_cache: Arc<dyn IndexedCheckpointCache + Send + Sync + 'static> =
        Arc::new(IndexedCheckpointCacheImpl::new(
            config.clone(),
            content_manager.clone(),
        ));

    if REPLICA_CACHE_ON_STARTUP.get_from(&feature_flags) {
        tracing::info!("Replicating cache on startup");
        let peer_embeddings_search_client = timeout(
            Duration::from_secs(30),
            create_peer_embeddings_search_client(
                &config.embeddings_search_endpoint,
                client_tls_config,
                Duration::from_secs(config.cache_replication_timeout),
            ),
        )
        .await;
        match peer_embeddings_search_client {
            Ok(Ok(client)) => {
                let replicator = cache_replicator::CacheReplicator::new(
                    client,
                    token_exchange_client.clone(),
                    embedding_cache.clone(),
                );
                if let Err(e) = replicator.run().await {
                    tracing::error!("Failed to run cache replicator: {}", e);
                }
            }
            Ok(Err(e)) => {
                tracing::error!("Failed to create embeddings search client: {}", e);
            }
            Err(_) => {
                tracing::error!("Create embeddings search client timed out");
            }
        }
    } else {
        tracing::info!("Not replicating cache on startup");
    }

    let peer_service = peer_embeddings_search_service::PeerEmbeddingsSearchImpl::new(
        namespace,
        embedding_cache.clone(),
    );

    let embeddings_service = embeddings_search_service::EmbeddingsSearchImpl::new(
        config.clone(),
        feature_flags,
        embedding_cache,
        checkpoint_cache,
        chunk_cache,
        indexed_checkpoint_cache,
        request_insight_publisher,
        token_exchange_client,
        working_set_client,
    );

    let reflection_service: tonic_reflection::server::ServerReflectionServer<_> =
        tonic_reflection::server::Builder::configure()
            .register_encoded_file_descriptor_set(proto::FILE_DESCRIPTOR_SET)
            .build_v1()?;

    let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

    let listener = tokio::net::TcpListener::bind(addr)
        .await
        .expect("Failed to bind");
    tracing::info!(
        "Listening on {:?}",
        listener.local_addr().expect("Failed to get local address")
    );

    let server = server
        .timeout(Duration::from_secs(300))
        // should be >= max_concurrent_streams to prevent deadlock, see hyperium/hyper#3559
        .concurrency_limit_per_connection(config.grpc_max_concurrent_streams.try_into().unwrap())
        .max_concurrent_streams(Some(config.grpc_max_concurrent_streams))
        .trace_fn(tracing_tonic::server::trace_fn)
        .layer(
            tower::ServiceBuilder::new()
                .layer(MetricsMiddlewareLayer::new(
                    &RESPONSE_LATENCY_COLLECTOR,
                    &ACTIVE_REQUESTS_COLLECTOR,
                ))
                .layer(GrpcAuthMiddlewareLayer::new(grpc_auth))
                .into_inner(),
        )
        .add_service(health_service)
        .add_service(embeddings_service.new_server())
        .add_service(peer_service.new_server())
        .add_service(reflection_service)
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(listener),
            async move {
                sigterm_notifier.recv().await;
            },
        );

    let res = futures::future::join(server, metrics_server).await;
    panic!("servers done: {res:?}");
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    if is_f16x16_supported() {
        run::<f16x16>(args).await
    } else {
        run::<f32x8>(args).await
    }
}

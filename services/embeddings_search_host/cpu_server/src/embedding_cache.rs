use std::collections::{BTreeSet, HashSet};
use std::sync::Arc;
use std::time::Duration;
use std::time::Instant;

use crate::config::Config;
use crate::dot_math::SimdData;
use crate::metrics::{
    CacheType, BLOB_DOWNLOAD_STATE_COUNT, CACHE_BYTES_COUNT, CACHE_DOWNLOAD_LATENCY_COLLECTOR,
    CACHE_ENTRY_COUNT, CACHE_EVICTION_COUNT_COLLECTOR, CACHE_LOOKUP_COUNT_COLLECTOR,
    DOWNLOAD_BYTES_COUNTER, DOWNLOAD_COUNTER, DOW<PERSON>OAD_LATENCY_COLLECTOR,
};
use crate::proto::content_manager::batch_get_content_response::Response::{
    FinalContent, NotFoundContent,
};
use crate::proto::tensor::Tensor;
use crate::types::BlobEntry;
use async_trait::async_trait;
use blob_names::BlobName;
use bytes::Bytes;
use content_manager_client::{BlobScope, ContentManagerClient};
use moka::future::Cache;
use numpy::NumpyTensor;
use request_context::{RequestContext, TenantId, TenantInfo, EMPTY_TENANT_ID};
use tokio::sync::mpsc;
use tokio::sync::mpsc::Receiver;
use tracing::Instrument;

pub struct EmbeddingResult<T: SimdData> {
    pub entries: Vec<Arc<BlobEntry<T>>>,
    pub unknown_blobs: Vec<BlobName>,
    pub deleted_blobs: BTreeSet<BlobName>,
}

/// A large number of blob names may exceed the gRPC request size limit. (We already stream the
/// getContent response.) To avoid hitting the limit, we can split the keys into batches. A batch
/// size of 4096 is approximately 256KB for 64byte hex strings.
/// Additionally, it's convenient for our pipelining of get_content -> index_search to keep this
/// reasonably sized (we miss pipelining benefit equal to the time it takes index_search to
/// process the last batch).
pub const BLOB_KEY_BATCH_SIZE: usize = 4096;

// type returned by EmbeddingCache iter()
pub struct IterEntry {
    /// tenant id
    pub tenant_id: TenantId,

    /// blob name
    pub blob_name: BlobName,

    /// transformation key
    pub transformation_key: String,

    /// sub key
    pub sub_key: String,

    /// tensor data of the chunk
    pub tensor: crate::proto::tensor::Tensor,
}

impl std::fmt::Debug for IterEntry {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "IterEntry {{ tenant_id: {:?}, blob_name: {:?}, transformation_key: {:?}, sub_key: {:?} }}",
            self.tenant_id,
            self.blob_name,
            self.transformation_key,
            self.sub_key,
        )
    }
}

fn from_tensor<T: SimdData>(tensor: Tensor) -> tonic::Result<NumpyTensor<T>> {
    if tensor.datatype != T::data_type() {
        return Err(tonic::Status::invalid_argument(format!(
            "Invalid datatype: {}",
            tensor.datatype
        )));
    }
    if tensor.shape.is_empty() {
        return Err(tonic::Status::invalid_argument(format!(
            "Invalid shape: {:?}",
            tensor.shape
        )));
    }
    let shape: Vec<usize> = tensor.shape.iter().map(|v| *v as usize).collect();
    let data = T::from_numpy_tensor(&tensor.contents, shape.clone());
    Ok(data)
}

pub fn to_tensor<T: SimdData>(t: &NumpyTensor<T>) -> Tensor {
    Tensor {
        datatype: T::data_type().to_string(),
        contents: t.data.iter().flat_map(|v| v.to_bytes()).collect(),
        shape: t.shape.iter().map(|v| *v as i64).collect(),
    }
}

/// embedding caching and lookup
#[async_trait]
pub trait EmbeddingCache<T: SimdData> {
    #[allow(clippy::too_many_arguments)]
    fn get_embeddings(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        blob_scope: BlobScope,
        blob_names: BTreeSet<BlobName>,
        blob_key_batch_size: usize,
        deadline: Option<Instant>,
    ) -> Receiver<tonic::Result<EmbeddingResult<T>>>;

    /// returns an iterator over all the cached entries
    fn iter(&self) -> Receiver<tonic::Result<IterEntry>>;

    /// add a tensor to the cache
    async fn add(
        &self,
        tensor: crate::proto::tensor::Tensor,
        tenant_id: TenantId,
        blob_name: BlobName,
        blob_scope: BlobScope,
    ) -> tonic::Result<()>;
}

struct EmbeddingCacheDataImpl<T: SimdData> {
    pub content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    pub cache: Cache<(TenantId, BlobScope, BlobName), Arc<BlobEntry<T>>>,
}

impl<T: SimdData> EmbeddingCacheDataImpl<T> {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
    ) -> Self {
        tracing::info!(
            "Initializing index cache with data type {:?}",
            std::any::type_name::<T>()
        );
        let cache = Cache::builder()
            .max_capacity(config.index_cache_size_bytes)
            .eviction_policy(config.index_cache_eviction.into())
            // weigh by the size of the tensor (32 bytes per f16x16 or f32x8), add an estimate of the key size
            .weigher(|_, v: &Arc<BlobEntry<T>>| v.size_bytes() + 128)
            // seconds to live for a blob after insert or retrieval
            .time_to_idle(Duration::from_secs(config.cache_tti_seconds))
            .eviction_listener(|_, _, c| {
                let removal_cause = format!("{:?}", c);
                CACHE_EVICTION_COUNT_COLLECTOR
                    .with_label_values(&[CacheType::Index.as_str(), &removal_cause])
                    .inc();
            })
            .build();
        tracing::info!("Initialized index cache");
        EmbeddingCacheDataImpl {
            content_manager,
            cache,
        }
    }

    async fn read_from_cache(
        &self,
        tenant_id: &TenantId,
        blob_scope: &BlobScope,
        blob_name: &BlobName,
    ) -> Option<Arc<BlobEntry<T>>> {
        let cache_key = (tenant_id.clone(), blob_scope.clone(), blob_name.clone());
        self.cache.get(&cache_key).await
    }

    // Any blobs that are not found, for whatever reason, are returned as part
    // of unknown_blobs.
    async fn read_from_content_manager(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        blob_scope: &BlobScope,
        blob_names: &[BlobName],
        deadline: Option<Instant>,
    ) -> tonic::Result<EmbeddingResult<T>> {
        if deadline.map(|d| Instant::now() >= d).unwrap_or(false) {
            return Ok(EmbeddingResult {
                entries: vec![],
                unknown_blobs: blob_names.to_vec(),
                deleted_blobs: BTreeSet::new(),
            });
        }

        let mut response = self
            .content_manager
            .get_content(
                request_context,
                &tenant_info.tenant_id,
                blob_scope,
                blob_names,
                deadline,
            )
            .await;

        let mut insert_walltime: f64 = 0.0;
        let mut received_count = 0;
        let mut result = vec![];
        while let Some(next_message) = response.recv().await {
            match next_message {
                Ok(message) => match message.response {
                    Some(NotFoundContent(_not_found)) => {
                        // Do nothing, we will add this to the unknown_blobs list
                        // right before returning an Ok() response
                        // NOTE: I am assuming here that the return value from the
                        // content manager is the same as the input
                    }
                    Some(FinalContent(final_content)) => {
                        received_count += 1;
                        let bytes = Bytes::from(final_content.content);
                        DOWNLOAD_COUNTER
                            .with_label_values(&[&tenant_info.tenant_name])
                            .inc();
                        DOWNLOAD_BYTES_COUNTER
                            .with_label_values(&[&tenant_info.tenant_name])
                            .inc_by(bytes.len() as u64);
                        match numpy::read_npy(bytes) {
                            Ok(array) => {
                                if array.to_floats().iter().any(|x| x.is_nan()) {
                                    tracing::warn!(
                                        "NaN in loaded blob {}",
                                        &final_content.blob_name
                                    );
                                    // Don't fail the request for now so we can avoid causing problems for users
                                }
                                let blob_name = BlobName::new(&final_content.blob_name)?;
                                tracing::debug!(
                                    "Loaded blob embeddings: blob_name={:?} shape={:?}",
                                    blob_name,
                                    array.shape,
                                );
                                let entry = Arc::new(BlobEntry {
                                    blob_name: blob_name.clone(),
                                    tensor: array,
                                });
                                let cache_key = (
                                    tenant_info.tenant_id.clone().unwrap_or(EMPTY_TENANT_ID),
                                    blob_scope.clone(),
                                    blob_name.clone(),
                                );
                                let insert_start = Instant::now();
                                self.cache.insert(cache_key, entry.clone()).await;
                                insert_walltime += insert_start.elapsed().as_secs_f64();
                                tracing::span::Span::current()
                                    .record("cache_insert_walltime", insert_walltime);

                                result.push(entry);
                            }
                            Err(err) => {
                                return Err(tonic::Status::internal(format!(
                                    "Failed to read blob: {:?}",
                                    err
                                )));
                            }
                        }
                    }
                    None => {}
                },
                Err(e) => {
                    if (e.code() == tonic::Code::Cancelled)
                        || (e.code() == tonic::Code::DeadlineExceeded)
                    {
                        // We hit the deadline, instead of returning an error,
                        // return what we have so far with unknown_blobs
                        // populated
                        tracing::info!(
                            "deadline reached: received_count={}, expected_count={}",
                            received_count,
                            blob_names.len()
                        );
                        break;
                    }
                    return Err(e);
                }
            }
        }
        // Return any keys not in the result as unknown_blobs
        let mut unknown_blobs: HashSet<BlobName> = blob_names.iter().cloned().collect();
        result.iter().for_each(|e| {
            unknown_blobs.remove(&e.blob_name);
        });
        Ok(EmbeddingResult {
            entries: result,
            unknown_blobs: unknown_blobs.into_iter().collect(),
            deleted_blobs: BTreeSet::new(),
        })
    }
}

pub struct EmbeddingCacheImpl<T: SimdData> {
    data: Arc<EmbeddingCacheDataImpl<T>>,
    task_monitor: Option<tokio_metrics_collector::TaskMonitor>,
}

impl<T: SimdData> EmbeddingCacheImpl<T> {
    pub fn new(
        config: Config,
        content_manager: Arc<dyn ContentManagerClient + Send + Sync + 'static>,
        task_monitor: Option<tokio_metrics_collector::TaskMonitor>,
    ) -> Self {
        Self {
            data: Arc::new(EmbeddingCacheDataImpl::<T>::new(config, content_manager)),
            task_monitor,
        }
    }
}

#[async_trait]
impl<T: SimdData> EmbeddingCache<T> for EmbeddingCacheImpl<T> {
    // Helper function: produce an async stream of blob embeddings that synthesizes the cache and
    // content manager sources of truth. Blob keys that aren't present in either will be put into
    // the unknown_blobs list of the results. Unexpected errors will be returned as part of the
    // stream. The stream is optimized for index_search pipelining, so it tries to deliver a lot
    // of usable embeddings as soon as possible (the first message on the stream is populated from
    // the cache only and won't have any unknown_blobs) and to keep the sizes of subsequent batches
    // manageable. Any blobs that are not retrieved will be returned as part of the unknown_blobs.
    #[allow(clippy::too_many_arguments)]
    fn get_embeddings(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        blob_scope: BlobScope,
        blob_names: BTreeSet<BlobName>,
        // Normally this should be BLOB_KEY_BATCH_SIZE (see above). Exposed for easy testing.
        blob_key_batch_size: usize,
        deadline: Option<Instant>,
    ) -> Receiver<tonic::Result<EmbeddingResult<T>>> {
        let index_span = tracing::info_span!("get_index", blob_names = blob_names.len());

        // This is a deliberately small channel size to promote steady pipelining. Sender::send()
        // doesn't actually yield to the tokio runtime unless the mpsc is full and we'd like that
        // to happen ASAP so the receiver can start processing. The potential latency cost on the
        // sender side is minimal since the receiver immediately dequeues everything it can.
        let (tx, rx) = mpsc::channel::<tonic::Result<EmbeddingResult<T>>>(1);
        let request_context: RequestContext = request_context.clone();
        let data = self.data.clone();

        let tenant_info = tenant_info.clone();
        let future = async move {
            let mut result = vec![];
            let mut keys_to_download = vec![];

            // Classify metrics by what kind of deadline we have
            let deadline_label = match deadline {
                Some(i) if Instant::now() + Duration::from_secs(1) > i => "short",
                Some(_) => "long",
                None => "none",
            };

            tracing::info!(
                "Getting {} blob embeddings for {} with {} cached entries taking {} bytes",
                blob_names.len(),
                blob_scope.transformation_key,
                data.cache.entry_count(),
                data.cache.weighted_size(),
            );
            CACHE_ENTRY_COUNT
                .with_label_values(&[CacheType::Index.as_str()])
                .set(data.cache.entry_count() as f64);
            CACHE_BYTES_COUNT
                .with_label_values(&[CacheType::Index.as_str()])
                .set(data.cache.weighted_size() as f64);

            // First step: cache lookup
            for blob_name in blob_names.iter() {
                if let Some(entry) = data
                    .read_from_cache(
                        tenant_info.tenant_id.as_ref().unwrap_or(&EMPTY_TENANT_ID),
                        &blob_scope,
                        blob_name,
                    )
                    .await
                {
                    CACHE_LOOKUP_COUNT_COLLECTOR
                        .with_label_values(&[
                            CacheType::Index.as_str(),
                            "hit",
                            &tenant_info.tenant_name,
                        ])
                        .inc();

                    result.push(entry);
                } else {
                    CACHE_LOOKUP_COUNT_COLLECTOR
                        .with_label_values(&[
                            CacheType::Index.as_str(),
                            "miss",
                            &tenant_info.tenant_name,
                        ])
                        .inc();
                    keys_to_download.push(blob_name.clone());
                }

                // There's no constraint forcing us to chunk cache entries by batch size like there
                // is for content manager requests, but it helps with our pipelining to keep
                // IndexResults to a manageable size.
                if result.len() >= blob_key_batch_size {
                    tracing::debug!("Returning cached blob embeddings: len={:?}", result.len());
                    BLOB_DOWNLOAD_STATE_COUNT
                        .with_label_values(&["cached", deadline_label, &tenant_info.tenant_name])
                        .inc_by(result.len() as u64);
                    let r = Ok(EmbeddingResult {
                        entries: result,
                        unknown_blobs: vec![],
                        deleted_blobs: BTreeSet::new(),
                    });
                    if let Err(send_err) = tx.send(r).await {
                        if !tx.is_closed() {
                            tracing::error!("Failed to send result: {:?}", send_err);
                        }
                        return;
                    }
                    result = vec![];
                }
            }

            if !result.is_empty() {
                tracing::debug!("Returning cached blob embeddings: len={:?}", result.len());
                BLOB_DOWNLOAD_STATE_COUNT
                    .with_label_values(&["cached", deadline_label, &tenant_info.tenant_name])
                    .inc_by(result.len() as u64);
                let r = Ok(EmbeddingResult {
                    entries: result,
                    unknown_blobs: vec![],
                    deleted_blobs: BTreeSet::new(),
                });
                if let Err(send_err) = tx.send(r).await {
                    if !tx.is_closed() {
                        tracing::error!("Failed to send result: {:?}", send_err);
                    }
                    return;
                }
            }

            // Second step: content manager lookup
            let download_start = Instant::now();
            for blob_key_chunk in keys_to_download.chunks(blob_key_batch_size) {
                tracing::debug!(
                    "Downloading blob embeddings: blob_keys={:?}",
                    blob_key_chunk.len(),
                );
                let download_span = tracing::info_span!(
                    "download",
                    blob_names = blob_key_chunk.len(),
                    cache_insert_walltime = 0.0
                );

                // We depend on read_from_content_manager() to handle the deadline
                // smartly - if the deadline has already passed then it should
                // avoid calling the content manager and return the input as
                // unknown_blobs. So this function ignores the deadline and calls
                // read_from_content_manager() until it is done.
                let result = data
                    .read_from_content_manager(
                        &request_context,
                        &tenant_info,
                        &blob_scope,
                        blob_key_chunk,
                        deadline,
                    )
                    .instrument(download_span)
                    .await;
                if let Ok(index_result) = &result {
                    BLOB_DOWNLOAD_STATE_COUNT
                        .with_label_values(&[
                            "downloaded",
                            deadline_label,
                            &tenant_info.tenant_name,
                        ])
                        .inc_by(index_result.entries.len() as u64);
                    // Classify unknown_blobs as timed_out or absent based on whether we hit the
                    // deadline. This may include a few false positive timed_out but that's fine
                    let result_label = if deadline.map(|d| Instant::now() >= d).unwrap_or(false) {
                        "timed_out"
                    } else {
                        "absent"
                    };
                    BLOB_DOWNLOAD_STATE_COUNT
                        .with_label_values(&[
                            result_label,
                            deadline_label,
                            &tenant_info.tenant_name,
                        ])
                        .inc_by(index_result.unknown_blobs.len() as u64);
                }
                if let Err(send_err) = tx.send(result).await {
                    if !tx.is_closed() {
                        tracing::error!("Failed to send result: {:?}", send_err);
                    }
                }
            }
            DOWNLOAD_LATENCY_COLLECTOR
                .with_label_values(&[&tenant_info.tenant_name])
                .observe(download_start.elapsed().as_secs_f64());
            CACHE_DOWNLOAD_LATENCY_COLLECTOR
                .with_label_values(&[CacheType::Index.as_str(), &tenant_info.tenant_name])
                .observe(download_start.elapsed().as_secs_f64());
        }
        .instrument(index_span);
        // Run the read future in the background and tie the index span to its lifetime
        if let Some(task_monitor) = self.task_monitor.as_ref() {
            tokio::spawn(task_monitor.instrument(future));
        } else {
            tokio::spawn(future);
        }
        rx
    }

    fn iter(&self) -> Receiver<tonic::Result<IterEntry>> {
        let (tx, rx) = mpsc::channel(1);
        let data = self.data.clone();
        let future = async move {
            for (key, value) in data.cache.iter() {
                let tensor = to_tensor(&value.as_ref().tensor);
                let err = tx
                    .send(Ok(IterEntry {
                        tenant_id: key.0.clone(),
                        blob_name: key.2.clone(),
                        transformation_key: key.1.transformation_key.clone(),
                        sub_key: key.1.sub_key.clone(),
                        tensor,
                    }))
                    .await;
                if err.is_err() {
                    return;
                }
            }
        };
        if let Some(task_monitor) = self.task_monitor.as_ref() {
            tokio::spawn(task_monitor.instrument(future));
        } else {
            tokio::spawn(future);
        }
        rx
    }

    async fn add(
        &self,
        tensor: crate::proto::tensor::Tensor,
        tenant_id: TenantId,
        blob_name: BlobName,
        blob_scope: BlobScope,
    ) -> tonic::Result<()> {
        let tensor = from_tensor::<T>(tensor)?;
        if tensor.to_floats().iter().any(|x| x.is_nan()) {
            tracing::warn!("NaN in added blob {}", blob_name);
            // Don't fail the request for now so we can avoid causing problems for users
        }
        let entry = Arc::new(BlobEntry {
            blob_name: blob_name.clone(),
            tensor,
        });
        let cache_key = (tenant_id.clone(), blob_scope.clone(), blob_name.clone());
        self.data.cache.insert(cache_key, entry).await;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use crate::config;
    use crate::util::tests::MockContentManagerClient;
    use numpy::f16x16_simd::{f16x16, is_f16x16_supported};
    use numpy::f32x8_simd::f32x8;
    use std::collections::{BTreeSet, HashMap, HashSet};
    use std::time::Duration;

    use super::*;

    async fn drain<T: SimdData>(
        mut rx: Receiver<tonic::Result<EmbeddingResult<T>>>,
    ) -> Vec<EmbeddingResult<T>> {
        let mut result = vec![];
        while let Some(entry) = rx.recv().await {
            match entry {
                Ok(entry) => result.push(entry),
                Err(e) => {
                    panic!("Failed to get index elements: {:?}", e);
                }
            }
        }
        result
    }

    #[tokio::test]
    async fn test_get_index_elements_f32x8() {
        test_get_index_elements::<f32x8>().await;
    }

    #[tokio::test]
    async fn test_get_index_elements_f16x16() {
        // TODO: make this skip rather than pass on non-Sapphire-Rapids CPUs?
        if !is_f16x16_supported() {
            return;
        }
        test_get_index_elements::<f16x16>().await;
    }

    async fn test_get_index_elements<T: SimdData>() {
        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();
        let blob2 = BlobName::from_bytes(&[1; 32]).unwrap();
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::from([String::from(&blob1), String::from(&blob2)]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));
        let blob_scope = BlobScope {
            transformation_key: "key".to_string(),
            sub_key: "sub_key".to_string(),
        };
        let cache = EmbeddingCacheImpl::<T>::new(Config::default(), client.clone(), None);
        let request_context = RequestContext::new_for_test();
        let result = drain(cache.get_embeddings(
            &request_context,
            &TenantInfo::new_for_test(),
            blob_scope.clone(),
            BTreeSet::from_iter(vec![blob1.clone(), blob2.clone()].into_iter()),
            BLOB_KEY_BATCH_SIZE,
            None,
        ))
        .await;
        assert_eq!(result.len(), 1);
        assert_eq!(result[0].entries.len(), 2);
        assert_eq!(
            client
                .last_get_content_request
                .lock()
                .unwrap()
                .as_ref()
                .unwrap()
                .blobs,
            &[
                (blob1.clone(), blob_scope.clone()),
                (blob2.clone(), blob_scope.clone()),
            ]
        );
        client.clear();
        let mut result = cache.get_embeddings(
            &RequestContext::new_for_test(),
            &TenantInfo::new_for_test(),
            BlobScope {
                transformation_key: "key".to_string(),
                sub_key: "sub_key".to_string(),
            },
            BTreeSet::from_iter(vec![blob1.clone(), blob2.clone()].into_iter()),
            BLOB_KEY_BATCH_SIZE,
            None,
        );
        let res = result.recv().await;
        // There should be one normal result with two entries
        assert!(res.is_some());
        assert!(res.as_ref().unwrap().is_ok());
        assert!(res.unwrap().unwrap().entries.len() == 2);
        // There should be no more results
        let res = result.recv().await;
        assert!(res.is_none());
        // There should be no content manager requests needed
        assert!(client.last_get_content_request.lock().unwrap().is_none());
    }

    #[tokio::test]
    async fn test_get_index_elements_empty_blobs() {
        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();
        let blob2 = BlobName::from_bytes(&[1; 32]).unwrap();
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::from([String::from(&blob1), String::from(&blob2)]),
            HashMap::default(),
            // Simulate empty .npy files for each blob
            "services/embeddings_search_host/cpu_server/src/test_data/empty.npy",
        ));
        let cache = EmbeddingCacheImpl::<f32x8>::new(Config::default(), client.clone(), None);
        let mut result = cache.get_embeddings(
            &RequestContext::new_for_test(),
            &TenantInfo::new_for_test(),
            BlobScope {
                transformation_key: "key".to_string(),
                sub_key: "sub_key".to_string(),
            },
            BTreeSet::from_iter(vec![blob1.clone(), blob2.clone()].into_iter()),
            BLOB_KEY_BATCH_SIZE,
            None,
        );
        // There should be one normal result with two entries and no unknown keys
        let opt_res = result.recv().await;
        assert!(opt_res.is_some());
        assert!(opt_res.as_ref().unwrap().is_ok());
        let index_res = opt_res.unwrap().unwrap();
        assert!(index_res.entries.len() == 2);
        assert!(index_res.unknown_blobs.is_empty());
        // Then we should get no more results
        let opt_res = result.recv().await;
        assert!(opt_res.is_none());
    }

    #[tokio::test]
    async fn test_get_index_elements_deadline() {
        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();
        let blob2 = BlobName::from_bytes(&[1; 32]).unwrap();
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::from([String::from(&blob1), String::from(&blob2)]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));
        let cache = EmbeddingCacheImpl::<f32x8>::new(Config::default(), client.clone(), None);
        let mut result = cache.get_embeddings(
            &RequestContext::new_for_test(),
            &TenantInfo::new_for_test(),
            BlobScope {
                transformation_key: "key".to_string(),
                sub_key: "sub_key".to_string(),
            },
            BTreeSet::from_iter(vec![blob1.clone(), blob2.clone()].into_iter()),
            // Test that the behavior works with multiple batches
            1,
            // Instants are monotonically nondecreasing, so we should hit
            // the deadline immediately
            Some(Instant::now()),
        );
        // We should get two valid results, each with no entries and one unknown
        // key
        for i in 1..=2 {
            let opt_res = result.recv().await;
            assert!(opt_res.is_some());
            let res = opt_res.unwrap();
            assert!(res.is_ok());
            let index_res = res.unwrap();
            assert!(index_res.entries.is_empty());
            assert_eq!(
                index_res.unknown_blobs,
                vec![if i == 1 { blob1.clone() } else { blob2.clone() }],
            );
        }
        // Then we should get no more results
        let opt_res = result.recv().await;
        assert!(opt_res.is_none());
        // The timeout was immediate, so no get content requests
        assert!(client.last_get_content_request.lock().unwrap().is_none());
    }

    #[tokio::test]
    async fn test_read_from_content_manager_deadline() {
        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();
        let blob2 = BlobName::from_bytes(&[1; 32]).unwrap();
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::from([String::from(&blob1), String::from(&blob2)]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));
        let cache_data = EmbeddingCacheDataImpl::<f32x8>::new(Config::default(), client.clone());
        let result = cache_data
            .read_from_content_manager(
                &RequestContext::new_for_test(),
                &TenantInfo::new_for_test(),
                &BlobScope {
                    transformation_key: "key".to_string(),
                    sub_key: "sub_key".to_string(),
                },
                &[blob1.clone(), blob2.clone()],
                // Instants are monotonically nondecreasing, so we should hit
                // the deadline immediately
                Some(Instant::now()),
            )
            .await;
        assert!(result.is_ok());
        let index_result = result.unwrap();
        assert!(index_result.entries.is_empty());
        let unknown_set = index_result.unknown_blobs.iter().collect::<HashSet<_>>();
        assert_eq!(unknown_set, HashSet::from([&blob1, &blob2,]),);
        // Should not have initiated any content manager requests
        assert!(client.last_get_content_request.lock().unwrap().is_none());
    }

    #[tokio::test]
    async fn test_read_from_content_manager_batched() {
        // vector that exceeds the request batch size set below
        let blob_names = (0..20)
            .map(|i| BlobName::from_bytes(&[i; 32]).unwrap())
            .collect::<Vec<_>>();
        let blobs_name_strings: [String; 20] = blob_names
            .iter()
            .map(String::from)
            .collect::<Vec<_>>()
            .try_into()
            .unwrap();
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::from(blobs_name_strings),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));
        let cache = EmbeddingCacheImpl::<f32x8>::new(Config::default(), client.clone(), None);
        let mut result = cache.get_embeddings(
            &RequestContext::new_for_test(),
            &TenantInfo::new_for_test(),
            BlobScope {
                transformation_key: "key".to_string(),
                sub_key: "sub_key".to_string(),
            },
            BTreeSet::from_iter(blob_names.iter().cloned()),
            8,
            None,
        );
        let res = result.recv().await;
        assert!(res.is_some());
        assert!(res.as_ref().unwrap().is_ok());
        assert_eq!(res.unwrap().unwrap().entries.len(), 8);
        let res = result.recv().await;
        assert!(res.is_some());
        assert!(res.as_ref().unwrap().is_ok());
        assert_eq!(res.unwrap().unwrap().entries.len(), 8);
        let res = result.recv().await;
        assert!(res.is_some());
        assert!(res.as_ref().unwrap().is_ok());
        assert_eq!(res.unwrap().unwrap().entries.len(), 4);
        let res = result.recv().await;
        assert!(res.is_none()); // no more results
    }

    // Note (nikita) -- I do not love timing dependent tests. If anyone has a way to, say,
    // do some mocking of time that would be better.
    #[tokio::test]
    async fn test_cache_tti() {
        let keep_checking_blob = BlobName::from_bytes(&[0; 32]).unwrap();
        let stop_checking_blob = BlobName::from_bytes(&[1; 32]).unwrap();
        let never_blob = BlobName::from_bytes(&[2; 32]).unwrap();
        let client = Arc::new(MockContentManagerClient::new(
            HashSet::from([
                String::from(&keep_checking_blob),
                String::from(&stop_checking_blob),
            ]),
            HashMap::default(),
            "services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy",
        ));
        let config = config::Config {
            cache_tti_seconds: 3,
            ..Default::default()
        };
        let cache = EmbeddingCacheImpl::<f32x8>::new(config.clone(), client.clone(), None);

        let request_context = &RequestContext::new_for_test();
        let tenant_info = &TenantInfo::new_for_test();
        let blob_scope = BlobScope {
            transformation_key: "key".to_string(),
            sub_key: "sub_key".to_string(),
        };
        let blob_names = vec![keep_checking_blob.clone(), stop_checking_blob.clone()];
        let deadline = None;

        let result = cache
            .data
            .read_from_content_manager(
                request_context,
                tenant_info,
                &blob_scope,
                &blob_names,
                deadline,
            )
            .await;
        assert!(
            result.is_ok(),
            "Failed initial read from content manager to populate cache"
        );

        let tenant_id = &tenant_info.clone().tenant_id.unwrap();

        // check reads behave as expected -- blob_keep_checking is present and blob_never isn't
        let read = cache
            .data
            .read_from_cache(tenant_id, &blob_scope, &keep_checking_blob)
            .await
            .expect("Failed to get blob1");
        assert_eq!(read.blob_name, keep_checking_blob, "Unexpected blob name");

        let result = cache
            .data
            .read_from_cache(tenant_id, &blob_scope, &never_blob)
            .await;
        assert!(result.is_none(), "Unexpectedly found blob_never");

        // our cache exp is 3s, so expect that this is still around
        tokio::time::sleep(Duration::from_secs(2)).await;
        let result = cache
            .data
            .read_from_cache(tenant_id, &blob_scope, &keep_checking_blob)
            .await
            .expect("Failed to get blob1");
        assert_eq!(result.blob_name, keep_checking_blob, "Unexpected blob name");

        // after 2 more seconds, the blob we kept checking should be around, but
        // the blob we stopped checking should be gone
        tokio::time::sleep(Duration::from_secs(2)).await;
        let result = cache
            .data
            .read_from_cache(tenant_id, &blob_scope, &keep_checking_blob)
            .await
            .expect("Failed to get blob1");
        assert_eq!(result.blob_name, keep_checking_blob, "Unexpected blob name");

        let result = cache
            .data
            .read_from_cache(tenant_id, &blob_scope, &stop_checking_blob)
            .await;
        assert!(result.is_none(), "Unexpectedly found blob_stop_checking");
    }
}

use std::sync::Arc;

use blob_names::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use content_manager_client::BlobScope;
use request_context::{RequestContext, RequestId, RequestSessionId, RequestSource};
use tracing_tonic::client::TracingService;

use crate::dot_math::SimdData;
use crate::metrics::{REPLICA_BLOB_RECEIVED, REPLICA_BYTES_RECEIVED};
use crate::{embedding_cache::EmbeddingCache, proto::embeddings_search::GetPeerCacheRequest};

pub struct CacheReplicator<T: SimdData> {
    client:
        crate::proto::embeddings_search::peer_embeddings_search_client::PeerEmbeddingsSearchClient<
            TracingService,
        >,
    token_exchange_client: Arc<dyn token_exchange_client::TokenExchangeClient + Send + Sync>,
    embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
    service_request_context: RequestContext,
}

impl<T: SimdData> CacheReplicator<T> {
    pub fn new(
        client: crate::proto::embeddings_search::peer_embeddings_search_client::PeerEmbeddingsSearchClient<
            TracingService,
        >,
        token_exchange_client: Arc<dyn token_exchange_client::TokenExchangeClient + Send + Sync>,
        embedding_cache: Arc<dyn EmbeddingCache<T> + Send + Sync + 'static>,
    ) -> Self {
        let service_request_context = RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::create_random(),
            RequestSource::Background,
            None,
        );
        Self {
            client,
            token_exchange_client,
            embedding_cache,
            service_request_context,
        }
    }

    async fn get_request_context(&self) -> Result<RequestContext, tonic::Status> {
        let token = self
            .token_exchange_client
            .get_signed_token_for_service(
                "".to_string(),
                &[token_exchange_client::token_exchange::Scope::ContentRw],
                &self.service_request_context.with_new_request_id(),
            )
            .await?;
        Ok(RequestContext::new(
            RequestId::create_random(),
            RequestSessionId::create_random(),
            RequestSource::Background,
            Some(token),
        ))
    }

    pub async fn run(self) -> tonic::Result<()> {
        tracing::info!("starting peer cache replicator");
        let request_context = self.get_request_context().await?;

        let mut request = tonic::Request::new(GetPeerCacheRequest {});
        request_context.annotate(request.metadata_mut());

        let mut count: i64 = 0;
        let mut byte_count: i64 = 0;
        let mut stream = self
            .client
            .clone()
            .get_peer_cache(request)
            .await?
            .into_inner();

        while let Some(response) = stream.message().await? {
            let tenant_id = response.tenant_id.into();
            let blob_name = BlobName::new(&response.blob_name)?;
            let blob_scope = BlobScope {
                transformation_key: response.transformation_key,
                sub_key: response.sub_key,
            };
            count += 1;
            let blob_byte_count = response
                .tensor
                .as_ref()
                .map(|x| x.contents.len() as i64)
                .unwrap_or(0);
            byte_count += blob_byte_count;
            tracing::debug!(
                "received peer cache: tenant_id={} blob_name={:?} transformation_key={:?} sub_key={:?}",
                tenant_id,
                blob_name,
                blob_scope.transformation_key,
                blob_scope.sub_key
            );
            REPLICA_BLOB_RECEIVED.inc();
            REPLICA_BYTES_RECEIVED.inc_by(blob_byte_count as u64);
            if count % 1000 == 0 {
                tracing::info!(
                    "peer cache replicator: received {} entries: {} bytes",
                    count,
                    byte_count
                );
            }
            if let Some(tensor) = response.tensor {
                self.embedding_cache
                    .add(tensor, tenant_id, blob_name, blob_scope)
                    .await?;
            }
        }
        tracing::info!(
            "peer cache replicator finished: received {} entries: {} bytes",
            count,
            byte_count
        );
        Ok(())
    }
}

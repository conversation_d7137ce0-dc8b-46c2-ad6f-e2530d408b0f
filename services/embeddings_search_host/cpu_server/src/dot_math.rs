use half::f16;

use numpy::f16x16_simd::{
    f16x16, f16x16_LENGTH, f16x16_mul_add, f16x16_nil, f16x16_prefetch, f16x16_sum,
    from_slice as f16x16_from_slice,
};
use numpy::f32x8_simd::{
    f32x8, f32x8_LENGTH, f32x8_add, f32x8_mul, f32x8_nil, f32x8_prefetch, f32x8_sum,
    from_slice as f32x8_from_slice,
};
use numpy::{self, FromNumpy};

#[cfg(test)]
pub fn dot(a: &[f32], b: &[f32]) -> f32 {
    let mut r: f32 = 0.0;
    a.iter().zip(b.iter()).for_each(|(ai, bi)| r += ai * bi);
    r
}

const SIMD_BLOCK_SIZE: usize = 2;
const PREFETCH_OFFSET: isize = 96;

fn dot_simd_f32_data_parallel(q: &[f32x8], v: &[f32x8]) -> f32 {
    let mut block: [f32x8; SIMD_BLOCK_SIZE] = [f32x8_nil(); SIMD_BLOCK_SIZE];
    let q_ptr = q.as_ptr();
    q.chunks_exact(SIMD_BLOCK_SIZE)
        .zip(v.chunks_exact(SIMD_BLOCK_SIZE))
        .for_each(|(qi, vi)| {
            let v_ptr = vi.as_ptr();
            unsafe {
                f32x8_prefetch(q_ptr, PREFETCH_OFFSET);
                f32x8_prefetch(v_ptr, PREFETCH_OFFSET);
            }
            for (block, (a, b)) in block.iter_mut().zip(qi.iter().zip(vi.iter())) {
                *block = f32x8_add(*block, f32x8_mul(*a, *b));
            }
        });
    block.iter().map(|v| f32x8_sum(*v)).sum::<f32>()
}

fn dot_simd_f16_data_parallel(q: &[f16x16], v: &[f16x16]) -> f32 {
    let mut block: [f16x16; SIMD_BLOCK_SIZE] = [f16x16_nil(); SIMD_BLOCK_SIZE];
    q.chunks_exact(SIMD_BLOCK_SIZE)
        .zip(v.chunks_exact(SIMD_BLOCK_SIZE))
        .for_each(|(qi, vi)| {
            let q_ptr = qi.as_ptr();
            let v_ptr = vi.as_ptr();
            unsafe {
                f16x16_prefetch(q_ptr, PREFETCH_OFFSET);
                f16x16_prefetch(v_ptr, PREFETCH_OFFSET);
            }
            for (block, (a, b)) in block.iter_mut().zip(qi.iter().zip(vi.iter())) {
                unsafe {
                    f16x16_mul_add(*a, *b, block);
                    // alternate implementation (may be more reliable since it never modifies in-place)
                    // *block = f16x16_add(*block, f16x16_mul(*a, *b));
                }
            }
        });
    block.iter().map(|v| f16x16_sum(*v)).sum::<f32>()
}

// Boilerplate for polymorphism over simd types in index_search / index_cache
// NOTE: none of these are actually parallel yet!! See one_to_many_benchmark.rs for reference
// parallel implementations that can be substituted here when we're ready.
pub trait SimdData: FromNumpy<Self> + Sized + Sync + Send + 'static + numpy::TensorType {
    fn dot_simd_data_parallel(q: &[Self], v: &[Self]) -> f32;

    fn from_f16_slice_parallel(s: &[f16]) -> Vec<Self>;
}

impl SimdData for f32x8 {
    fn dot_simd_data_parallel(q: &[Self], v: &[Self]) -> f32 {
        dot_simd_f32_data_parallel(q, v)
    }

    fn from_f16_slice_parallel(s: &[f16]) -> Vec<Self> {
        s.chunks_exact(f32x8_LENGTH).map(f32x8_from_slice).collect()
    }
}
impl SimdData for f16x16 {
    fn dot_simd_data_parallel(q: &[Self], v: &[Self]) -> f32 {
        dot_simd_f16_data_parallel(q, v)
    }

    fn from_f16_slice_parallel(s: &[f16]) -> Vec<Self> {
        s.chunks_exact(f16x16_LENGTH)
            .map(f16x16_from_slice)
            .collect()
    }
}

pub fn dot_simd_data_parallel<F: SimdData>(q: &[F], v: &[F]) -> f32 {
    F::dot_simd_data_parallel(q, v)
}

pub fn from_f16_slice_parallel<F: SimdData>(s: &[f16]) -> Vec<F> {
    F::from_f16_slice_parallel(s)
}

#[cfg(test)]
mod tests {
    use super::*;
    use numpy::f16x16_simd::is_f16x16_supported;

    #[test]
    fn test_dot_1024() {
        let a = [1.0; 1024];
        let b = [2.0; 1024];
        assert_eq!(dot(a.as_ref(), b.as_ref()), 2048.0);
    }

    #[test]
    fn test_dot_simd_f32_1024_data_parallel() {
        let a = [f16::from_f32(1.0); 1024];
        let b = [f16::from_f32(2.0); 1024];

        let va = from_f16_slice_parallel(&a);
        let vb = from_f16_slice_parallel(&b);

        assert_eq!(
            dot_simd_f32_data_parallel(va.as_slice(), vb.as_slice()),
            2048.0
        );
    }

    #[test]
    fn test_dot_simd_f16_1024_data_parallel() {
        // TODO: make this skip rather than pass on non-Sapphire-Rapids CPUs?
        if !is_f16x16_supported() {
            return;
        }
        let a = [f16::from_f32(2.0); 1024];
        let b = [f16::from_f32(5.0); 1024];

        let va = from_f16_slice_parallel(&a);
        let vb = from_f16_slice_parallel(&b);

        let c = dot_simd_f16_data_parallel(&va, &vb);
        assert_eq!(c, 10240.0);
    }

    #[test]
    fn test_dot_simd_f32_512_data_parallel() {
        let a = [f16::from_f32(1.0); 512];
        let b = [f16::from_f32(2.0); 512];

        let va = from_f16_slice_parallel(&a);
        let vb = from_f16_slice_parallel(&b);

        assert_eq!(
            dot_simd_f32_data_parallel(va.as_slice(), vb.as_slice()),
            1024.0
        );
    }

    #[test]
    fn test_dot_simd_f16_512_data_parallel() {
        // TODO: make this skip rather than pass on non-Sapphire-Rapids CPUs?
        if !is_f16x16_supported() {
            return;
        }
        let a = [f16::from_f32(2.0); 512];
        let b = [f16::from_f32(5.0); 512];

        let va = from_f16_slice_parallel(&a);
        let vb = from_f16_slice_parallel(&b);

        let c = dot_simd_f16_data_parallel(&va, &vb);
        assert_eq!(c, 5120.0);
    }
}

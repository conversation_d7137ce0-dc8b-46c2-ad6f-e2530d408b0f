{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Factory Test Cases\n", "\n", "Notebook used to generate the test cases stored in `test_data/`.\n", "The notebook can be used to a) understand how the test cases where generated b) change the test cases in the future."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import numpy\n", "\n", "prefix = \"/home/<USER>/augment\"\n", "hello = numpy.random.normal(0, 1, (4, 80)).astype(numpy.float16)\n", "numpy.save(\n", "    f\"{prefix}/services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_hello.npy\",\n", "    hello,\n", ")\n", "\n", "goodbye = numpy.random.normal(0, 1, (4, 512)).astype(numpy.float16)\n", "numpy.save(\n", "    f\"{prefix}/services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_goodbye.npy\",\n", "    goodbye,\n", ")\n", "\n", "world = numpy.random.normal(0, 1, (2048, 80)).astype(numpy.float16)\n", "numpy.save(\n", "    f\"{prefix}/services/embeddings_search_host/cpu_server/src/test_data/tk1_sk1_world.npy\",\n", "    world,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
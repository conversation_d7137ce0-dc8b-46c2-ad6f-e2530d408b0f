use clap::Parser;
use grpc_tls_config::TlsConfig;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::{fs::File, path::Path};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct AuthConfig {
    pub token_exchange_endpoint: String,
    pub token_exchange_request_timeout_s: f32,
}

// moka's enum is not serde serializable
#[allow(clippy::upper_case_acronyms)]
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum EvictionPolicy {
    TinyLFU,
    LRU,
}

impl From<EvictionPolicy> for moka::policy::EvictionPolicy {
    fn from(policy: EvictionPolicy) -> Self {
        match policy {
            EvictionPolicy::TinyLFU => moka::policy::EvictionPolicy::tiny_lfu(),
            EvictionPolicy::LRU => moka::policy::EvictionPolicy::lru(),
        }
    }
}

/// structure representing the configuration information in the configuration file, i.e.
/// the configmap of the embeddings search pod.
#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>)]
pub struct Config {
    // the address to bind the rpc server to, e.g. 0.0.0.0:50051
    pub bind_address: String,

    pub feature_flags_sdk_key_path: Option<std::path::PathBuf>,
    pub dynamic_feature_flags_endpoint: Option<String>,

    // Configure the HTTP server that returns Prometheus metrics.
    pub metrics_server_bind_address: String,
    pub metrics_server_port: u16,

    // populated if server MTLS should be used.
    pub server_mtls_config: Option<TlsConfig>,
    // populated if client MTLS should be used.
    pub client_mtls_config: Option<TlsConfig>,
    // populated if central client MTLS should be used.
    pub central_client_mtls_config: Option<TlsConfig>,

    pub content_manager_endpoint: String,
    pub content_manager_request_timeout_s: f32,

    pub embeddings_search_endpoint: String,

    pub working_set_endpoint: String,

    pub auth_config: AuthConfig,

    pub checkpoint_cache_size_bytes: u64,
    pub checkpoint_cache_eviction: EvictionPolicy,
    pub index_cache_size_bytes: u64,
    pub index_cache_eviction: EvictionPolicy,
    pub chunk_cache_size_bytes: u64,
    pub chunk_cache_eviction: EvictionPolicy,
    pub indexed_checkpoint_cache_size_bytes: u64,
    pub indexed_checkpoint_cache_eviction: EvictionPolicy,
    pub thread_count: u64,

    pub cache_tti_seconds: u64,
    pub cache_replication_timeout: u64,

    // The deadline for search requests, after this we should try to return
    // whatever we have
    pub search_timeout_ms: u64,
    pub grpc_max_concurrent_streams: u32,

    pub dynamic_feature_flags: Option<std::collections::HashMap<String, Value>>,
}

#[cfg(test)]
impl Default for Config {
    fn default() -> Self {
        Self {
            bind_address: "0.0.0.0:50051".to_string(),
            feature_flags_sdk_key_path: None,
            metrics_server_bind_address: "0.0.0.0".to_string(),
            metrics_server_port: 8080,

            server_mtls_config: None,
            client_mtls_config: None,
            central_client_mtls_config: None,

            content_manager_endpoint: "".to_string(),
            content_manager_request_timeout_s: 10.0,

            embeddings_search_endpoint: "".to_string(),

            working_set_endpoint: "".to_string(),

            auth_config: AuthConfig {
                token_exchange_endpoint: "".to_string(),
                token_exchange_request_timeout_s: 10.0,
            },

            checkpoint_cache_size_bytes: 256 * 1024 * 1024,
            checkpoint_cache_eviction: EvictionPolicy::TinyLFU,
            index_cache_size_bytes: 1024 * 1024 * 1024,
            index_cache_eviction: EvictionPolicy::TinyLFU,
            chunk_cache_size_bytes: 1024 * 1024 * 1024,
            chunk_cache_eviction: EvictionPolicy::TinyLFU,
            indexed_checkpoint_cache_size_bytes: 1024 * 1024 * 1024,
            indexed_checkpoint_cache_eviction: EvictionPolicy::TinyLFU,
            thread_count: 2,
            cache_tti_seconds: 3 * 24 * 60 * 60, // 3 days -- enough for the weekend
            cache_replication_timeout: 60,
            search_timeout_ms: 1000,
            grpc_max_concurrent_streams: 100,

            dynamic_feature_flags: None,
            dynamic_feature_flags_endpoint: None,
        }
    }
}

impl Config {
    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<Config, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: Config =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(config)
    }
}

/// Search for a pattern in a file and display the lines that contain it.
#[derive(Parser, Debug)]
pub struct CliArguments {
    /// path to the configuration file
    #[arg(long)]
    pub config_file: std::path::PathBuf,
    /// path to the request insight publisher configuration file
    #[arg(long)]
    pub request_insight_publisher_config_file: std::path::PathBuf,
}

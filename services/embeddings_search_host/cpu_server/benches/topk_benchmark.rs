#[macro_use]
extern crate divan;

use divan::{black_box, Bencher};
use ordered_float::NotNan;
use rand::distributions::Standard;
use rand::{thread_rng, Rng, SeedableRng};
use rayon::prelude::*;
use std::collections::BinaryHeap;
use std::iter::repeat;
use std::ops::Range;

const THREAD_COUNT: usize = 8;
const VECTOR_SIZE: usize = 512;
const ITEM_COUNT: usize = 100_000;

fn main() {
    rayon::ThreadPoolBuilder::new()
        .num_threads(THREAD_COUNT)
        .build_global()
        .unwrap();
    divan::main();
}

mod simd {
    use std::arch::x86_64::*; // Intel SIMD intrinsic mappings
    #[allow(non_camel_case_types)]
    pub type f32x8 = __m256;
    #[allow(non_upper_case_globals)]
    pub const f32x8_LENGTH: usize = 8;

    /// Return a 256-bit vector containing 8 infinity values of f32
    #[inline]
    pub fn f32x8_infty() -> f32x8 {
        unsafe { _mm256_set1_ps(std::f32::INFINITY) }
    }

    /// Return a 256-bit vector containing 8 infinity values of f32
    #[inline]
    pub fn f32x8_nil() -> f32x8 {
        unsafe { _mm256_set1_ps(0.0 as f32) }
    }

    #[inline]
    pub fn add(v: f32x8, w: f32x8) -> f32x8 {
        unsafe { _mm256_add_ps(v, w) }
    }

    #[inline]
    pub fn mul(v: f32x8, w: f32x8) -> f32x8 {
        unsafe { _mm256_mul_ps(v, w) }
    }

    /// Extract the lowest 32 bits of a 256-bit vector as a float
    #[inline]
    pub fn lowestf32(v: f32x8) -> f32 {
        unsafe { _mm256_cvtss_f32(v) }
    }

    #[inline]
    pub fn dot(v: f32x8, w: f32x8) -> f32x8 {
        unsafe { _mm256_dp_ps(v, w, 0b00000001) }
    }

    #[inline]
    pub fn from_slice(s: &[f32]) -> f32x8 {
        assert_eq!(s.len(), f32x8_LENGTH);
        unsafe { _mm256_set_ps(s[0], s[1], s[2], s[3], s[4], s[5], s[6], s[7]) }
    }

    #[inline]
    pub fn sum(v: f32x8) -> f32 {
        unsafe {
            // ChatGPT told me so
            let sum1 = _mm256_hadd_ps(v, v);
            let sum2 = _mm256_hadd_ps(sum1, sum1);
            _mm_cvtss_f32(_mm256_castps256_ps128(sum2))
        }
    }

    #[inline]
    pub fn to_slice(v: f32x8) -> [f32; f32x8_LENGTH] {
        unsafe { std::mem::transmute(v) }
    }

    #[inline]
    pub fn prefetch(p: *const f32x8, offset: isize) {
        unsafe { _mm_prefetch(p.offset(offset) as *const i8, _MM_HINT_T0) }
    }
}
use simd::f32x8;

fn to_vec(v: Vec<f32>) -> Vec<f32x8> {
    v.chunks_exact(simd::f32x8_LENGTH)
        .map(|x| simd::from_slice(x))
        .collect()
}

struct Loop;
struct ParIter;
struct SpawnBlobs;
struct SpawnBatch<const BATCH_SIZE: usize>;

#[divan::bench(
    types=[Loop, ParIter, SpawnBlobs, SpawnBatch<64>, SpawnBatch<256>, SpawnBatch<1024>],
    threads=[1, 2, 4, 8, 256],
)]
fn bench_topk<T>(bencher: divan::Bencher)
where
    T: Searcher,
{
    const N: usize = 25;
    let mut rng = thread_rng();
    let q: Vec<f32x8> = to_vec((&mut rng).sample_iter(Standard).take(VECTOR_SIZE).collect());
    let mut v: Vec<Vec<f32x8>> = Vec::new();
    for _ in 0..(ITEM_COUNT / N) {
        v.push(to_vec(
            (&mut rng)
                .sample_iter(Standard)
                .take(N * VECTOR_SIZE)
                .collect(),
        ));
    }

    bencher.bench(|| {
        let heap = T::search(v.as_slice(), q.as_slice(), 32);
        divan::black_box(heap);
    });
}

/// inner-product result
#[derive(Debug, Clone, PartialEq, PartialOrd, Ord, Eq, Hash)]
struct IpResult {
    // inner product
    // not-nan to make it ord
    value: NotNan<f32>,

    // index into the entry vector
    entry_index: usize,

    // index into the entry
    element_index: usize,
}

impl IpResult {
    fn new(value: f32, entry_index: usize, element_index: usize) -> Self {
        IpResult {
            value: NotNan::new(value).expect("value shouldn't be NaN"),
            entry_index,
            element_index,
        }
    }
}

fn merge_binary_heap(h1: &mut BinaryHeap<IpResult>, h2: BinaryHeap<IpResult>, results: usize) {
    for x in h2 {
        if h1.len() < results {
            h1.push(x);
        } else if *h1.peek().unwrap() > x {
            *h1.peek_mut().unwrap() = x;
        }
    }
}

trait Searcher {
    fn search(index: &[Vec<f32x8>], query: &[f32x8], results: usize) -> BinaryHeap<IpResult>;
}

impl Searcher for Loop {
    fn search(index: &[Vec<f32x8>], query: &[f32x8], results: usize) -> BinaryHeap<IpResult> {
        let mut heap = BinaryHeap::with_capacity(results);
        for (entry_index, entry) in index.iter().enumerate() {
            for (i, item) in entry.chunks_exact(query.len()).enumerate() {
                let ip = dot_simd_data_parallel(query, item);
                let ip = IpResult::new(-ip, entry_index, i);
                if heap.len() < results {
                    heap.push(ip);
                } else if *heap.peek().unwrap() > ip {
                    *heap.peek_mut().unwrap() = ip;
                }
            }
        }
        heap
    }
}

impl Searcher for ParIter {
    fn search(index: &[Vec<f32x8>], query: &[f32x8], results: usize) -> BinaryHeap<IpResult> {
        let heap = index
            .par_iter()
            .enumerate()
            .map(|(entry_index, entry)| {
                // this heap keeps track of the top-k entries from this blob entry
                let mut heap = BinaryHeap::with_capacity(results);
                for (i, item) in entry.chunks_exact(query.len()).enumerate() {
                    let ip = dot_simd_data_parallel(query, item);
                    let ip = IpResult::new(-ip, entry_index, i);
                    if heap.len() < results {
                        heap.push(ip);
                    } else if *heap.peek().unwrap() > ip {
                        *heap.peek_mut().unwrap() = ip;
                    }
                }
                heap
            })
            .fold(
                || BinaryHeap::with_capacity(results),
                |mut acc, x| {
                    merge_binary_heap(&mut acc, x, results);
                    acc
                },
            )
            .reduce(
                || BinaryHeap::with_capacity(results),
                |mut h1, h2| {
                    merge_binary_heap(&mut h1, h2, results);
                    h1
                },
            );
        heap
    }
}

impl<const BATCH_SIZE: usize> Searcher for SpawnBatch<BATCH_SIZE> {
    fn search(index: &[Vec<f32x8>], query: &[f32x8], results: usize) -> BinaryHeap<IpResult> {
        let mut batch_start: usize = 0;
        let mut batch_entries: usize = 0;
        let mut batches: Vec<Range<usize>> = vec![];
        for (idx, entry) in index.iter().enumerate() {
            batch_entries += entry.len() / query.len();
            if batch_entries >= BATCH_SIZE {
                batches.push(Range {
                    start: batch_start,
                    end: idx + 1,
                });
                batch_start = idx + 1;
                batch_entries = 0;
            }
        }
        if batch_entries > 0 {
            batches.push(Range {
                start: batch_start,
                end: index.len(),
            });
        }

        let mut heaps: Vec<BinaryHeap<IpResult>> = vec![BinaryHeap::new(); batches.len()];
        rayon::in_place_scope(|s| {
            batches
                .iter()
                .zip(heaps.iter_mut())
                .for_each(|(range, heap)| {
                    s.spawn(move |_| {
                        *heap = BinaryHeap::with_capacity(results);
                        //process_blobs(range.start, index.slice(range).as_ref(), &query, &mut heap);
                        index[range.clone()]
                            .iter()
                            .enumerate()
                            .for_each(|(entry_index, entry)| {
                                for (i, item) in entry.chunks_exact(query.len()).enumerate() {
                                    let ip = dot_simd_data_parallel(query, item);
                                    let ip = IpResult::new(-ip, range.start + entry_index, i);
                                    if heap.len() < results {
                                        heap.push(ip);
                                    } else if *heap.peek().unwrap() > ip {
                                        *heap.peek_mut().unwrap() = ip;
                                    }
                                }
                            });
                    });
                });
        });
        let mut final_heap =
            heaps
                .into_iter()
                .fold(BinaryHeap::with_capacity(results), |mut acc, x| {
                    merge_binary_heap(&mut acc, x, results);
                    acc
                });
        final_heap
    }
}

impl Searcher for SpawnBlobs {
    fn search(index: &[Vec<f32x8>], query: &[f32x8], results: usize) -> BinaryHeap<IpResult> {
        let mut ip_results: Vec<Vec<IpResult>> = vec![vec![]; index.len()];
        rayon::in_place_scope(|s| {
            ip_results
                .iter_mut()
                .enumerate()
                .for_each(|(entry_index, results)| {
                    let entry_vec = &index[entry_index];
                    if entry_vec.len() == 0 {
                        return;
                    }
                    s.spawn(move |_| {
                        for (i, item) in entry_vec.chunks_exact(query.len()).enumerate() {
                            let ip = dot_simd_data_parallel(query, item);
                            let ip = IpResult::new(-ip, entry_index, i);
                            results.push(ip);
                        }
                    });
                })
        });
        let mut heap = BinaryHeap::with_capacity(results);
        ip_results.into_iter().flatten().for_each(|ip| {
            if heap.len() < results {
                heap.push(ip);
            } else if *heap.peek().unwrap() > ip {
                *heap.peek_mut().unwrap() = ip;
            }
        });
        heap
    }
}

const SIMD_BLOCK_SIZE: usize = 2;
const PREFETCH_OFFSET: isize = 96;

fn dot_simd_data_parallel(q: &[f32x8], v: &[f32x8]) -> f32 {
    unsafe { dot_simd_f32_1024_data_parallel(q, v) }
}

unsafe fn dot_simd_f32_1024_data_parallel(q: &[simd::f32x8], v: &[simd::f32x8]) -> f32 {
    let mut block: [simd::f32x8; SIMD_BLOCK_SIZE] = [simd::f32x8_nil(); SIMD_BLOCK_SIZE];
    let q_ptr = q.as_ptr();
    q.chunks_exact(SIMD_BLOCK_SIZE)
        .zip(v.chunks_exact(SIMD_BLOCK_SIZE))
        .for_each(|(qi, vi)| {
            let v_ptr = vi.as_ptr();
            simd::prefetch(q_ptr, PREFETCH_OFFSET);
            simd::prefetch(v_ptr, PREFETCH_OFFSET);
            for (block, (a, b)) in block.iter_mut().zip(qi.iter().zip(vi.iter())) {
                *block = simd::add(*block, simd::mul(*a, *b));
            }
        });
    block.iter().map(|v| simd::sum(*v)).sum::<f32>()
}

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = [\n", "    {\n", "        \"count\": 1024 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 49_733_956,\n", "    },\n", "    {\n", "        \"count\": 1024 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 49_557_684,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 24_731_308,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 12_865_031,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 13_974_338,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 6_738_407,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 6_473_714,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 3_505_505,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 7_444_746,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 1_828_920,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 3_180_611,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 6_101_950,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"dim\": 512,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 1_778_754,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 3_263_625,\n", "    },\n", "    {\"count\": 32 * 1024, \"dim\": 512, \"threads\": 16, \"par_simd_dot_call_bench\": 905_574},\n", "    {\"count\": 32 * 1024, \"dim\": 512, \"threads\": 2, \"par_simd_dot_call_bench\": 2_643_738},    \n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 1_698_767,\n", "    },\n", "    {\"count\": 16 * 1024, \"dim\": 512, \"threads\": 16, \"par_simd_dot_call_bench\": 453_822},\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"dim\": 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 845_249,\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "plt.scatter(\n", "    [r[\"count\"] for r in results if r[\"dim\"] == 512 and r[\"threads\"] == 16],\n", "    [\n", "        r[\"par_simd_dot_call_bench\"] / 1_000_000.0\n", "        for r in results\n", "        if r[\"dim\"] == 512 and r[\"threads\"] == 16\n", "    ],\n", ")\n", "plt.scatter(\n", "    [r[\"count\"] for r in results if r[\"dim\"] == 1024 and r[\"threads\"] == 16],\n", "    [\n", "        r[\"par_simd_dot_call_bench\"] / 1_000_000.0\n", "        for r in results\n", "        if r[\"dim\"] == 1024 and r[\"threads\"] == 16\n", "    ],\n", "    color=\"orange\",\n", ")\n", "\n", "plt.ylabel(\"Time (ms)\")\n", "plt.xlabel(\"Embeddings count\")\n", "plt.title(\"Time to compute dot products (16 CPU)\")\n", "plt.legend([\"512\", \"1024\"])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Numpy\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "q = np.random.rand(1024).astype(float)\n", "e = np.zeros([512 * 1024, 1024]).astype(float)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%timeit e.dot(q)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 2}
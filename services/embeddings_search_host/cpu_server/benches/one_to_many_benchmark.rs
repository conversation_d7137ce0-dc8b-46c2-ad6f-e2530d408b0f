#[macro_use]
extern crate bencher;

use bencher::<PERSON><PERSON>;
use rayon::prelude::*;
use std::sync::Arc;

const THREAD_COUNT: usize = 2;
const VECTOR_SIZE: usize = 512;
const ITEM_COUNT: usize = 64 * 1024;

mod simd {
    use std::arch::x86_64::*; // Intel SIMD intrinsic mappings
    #[allow(non_camel_case_types)]
    pub type f32x8 = __m256;
    #[allow(non_upper_case_globals)]
    pub const f32x8_LENGTH: usize = 8;

    /// Return a 256-bit vector containing 8 infinity values of f32
    #[inline]
    pub fn f32x8_infty() -> f32x8 {
        unsafe { _mm256_set1_ps(std::f32::INFINITY) }
    }

    /// Return a 256-bit vector containing 8 infinity values of f32
    #[inline]
    pub fn f32x8_nil() -> f32x8 {
        unsafe { _mm256_set1_ps(0.0 as f32) }
    }

    #[inline]
    pub fn add(v: f32x8, w: f32x8) -> f32x8 {
        unsafe { _mm256_add_ps(v, w) }
    }

    #[inline]
    pub fn mul(v: f32x8, w: f32x8) -> f32x8 {
        unsafe { _mm256_mul_ps(v, w) }
    }

    /// Extract the lowest 32 bits of a 256-bit vector as a float
    #[inline]
    pub fn lowestf32(v: f32x8) -> f32 {
        unsafe { _mm256_cvtss_f32(v) }
    }

    #[inline]
    pub fn dot(v: f32x8, w: f32x8) -> f32x8 {
        unsafe { _mm256_dp_ps(v, w, 0b00000001) }
    }

    #[inline]
    pub fn from_slice(s: &[f32]) -> f32x8 {
        assert_eq!(s.len(), f32x8_LENGTH);
        unsafe { _mm256_set_ps(s[0], s[1], s[2], s[3], s[4], s[5], s[6], s[7]) }
    }

    #[inline]
    pub fn sum(v: f32x8) -> f32 {
        unsafe {
            // ChatGPT told me so
            let sum1 = _mm256_hadd_ps(v, v);
            let sum2 = _mm256_hadd_ps(sum1, sum1);
            _mm_cvtss_f32(_mm256_castps256_ps128(sum2))
        }
    }

    #[inline]
    pub fn to_slice(v: f32x8) -> [f32; f32x8_LENGTH] {
        unsafe { std::mem::transmute(v) }
    }

    #[inline]
    pub fn prefetch(p: *const f32x8, offset: isize) {
        unsafe { _mm_prefetch(p.offset(offset) as *const i8, _MM_HINT_T0) }
    }
}

pub fn baseline(q: &[f32], v: &[f32], result: &mut [f32]) {
    for (result, v) in result.iter_mut().zip(v.chunks_exact(VECTOR_SIZE)) {
        *result = q.iter().zip(v.iter()).map(|(qi, vi)| qi * vi).sum()
    }
}

fn baseline_bench(bench: &mut Bencher) {
    let q = vec![1.0 as f32; VECTOR_SIZE];
    let v = vec![2.0 as f32; ITEM_COUNT * VECTOR_SIZE];
    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        baseline(q.as_ref(), v.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

const BLOCK_SIZE: usize = 16;
pub fn dot_f32_1024_data_parallel(a: &[f32], b: &[f32]) -> f32 {
    let mut block: [f32; BLOCK_SIZE] = [0.0; BLOCK_SIZE];
    a.chunks_exact(BLOCK_SIZE)
        .zip(b.chunks_exact(BLOCK_SIZE))
        .for_each(|(ai, bi)| {
            for (block, (a, b)) in block.iter_mut().zip(ai.iter().zip(bi.iter())) {
                *block += a * b;
            }
        });
    block.iter().sum::<f32>()
}

pub fn dot_call(q: &[f32], v: &[f32], result: &mut [f32]) {
    result
        .iter_mut()
        .zip(v.chunks_exact(VECTOR_SIZE))
        .for_each(|(result, v)| {
            *result = dot_f32_1024_data_parallel(q, v);
        })
}

fn dot_call_bench(bench: &mut Bencher) {
    let q = vec![1.0 as f32; VECTOR_SIZE];
    let v = vec![2.0 as f32; ITEM_COUNT * VECTOR_SIZE];
    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        dot_call(q.as_ref(), v.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

pub fn par_dot_call(q: &[f32], v: &[f32], result: &mut [f32]) {
    result
        .into_par_iter()
        .zip(v.par_chunks_exact(VECTOR_SIZE))
        .for_each(|(result, v)| {
            *result = dot_f32_1024_data_parallel(q, v);
        })
}

fn par_dot_call_bench(bench: &mut Bencher) {
    let q = vec![1.0 as f32; VECTOR_SIZE];
    let v = vec![2.0 as f32; ITEM_COUNT * VECTOR_SIZE];

    let _ = rayon::ThreadPoolBuilder::new()
        .num_threads(THREAD_COUNT)
        .build_global();

    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        par_dot_call(q.as_ref(), v.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

const SIMD_BLOCK_SIZE: usize = 2;
const PREFETCH_OFFSET: isize = 96;

unsafe fn dot_simd_f32_1024_data_parallel(q: &[simd::f32x8], v: &[simd::f32x8]) -> f32 {
    let mut block: [simd::f32x8; SIMD_BLOCK_SIZE] = [simd::f32x8_nil(); SIMD_BLOCK_SIZE];
    let q_ptr = q.as_ptr();
    q.chunks_exact(SIMD_BLOCK_SIZE)
        .zip(v.chunks_exact(SIMD_BLOCK_SIZE))
        .for_each(|(qi, vi)| {
            let v_ptr = vi.as_ptr();
            simd::prefetch(q_ptr, PREFETCH_OFFSET);
            simd::prefetch(v_ptr, PREFETCH_OFFSET);
            for (col, (block, (a, b))) in block.iter_mut().zip(qi.iter().zip(vi.iter())).enumerate()
            {
                *block = simd::add(*block, simd::mul(*a, *b));
            }
        });
    block.iter().map(|v| simd::sum(*v)).sum::<f32>()
}

pub fn simd_dot_call(q: &[simd::f32x8], v: &[simd::f32x8], result: &mut [f32]) {
    result
        .iter_mut()
        .zip(v.chunks_exact(VECTOR_SIZE / simd::f32x8_LENGTH))
        .for_each(|(result, v)| unsafe {
            *result = dot_simd_f32_1024_data_parallel(q, v);
        })
}

fn simd_dot_call_bench(bench: &mut Bencher) {
    let q = vec![1.0 as f32; VECTOR_SIZE];
    let v = vec![2.0 as f32; ITEM_COUNT * VECTOR_SIZE];

    let mut vq = std::vec![simd::f32x8_infty(); q.len() / simd::f32x8_LENGTH];
    let mut vv = std::vec![simd::f32x8_infty(); v.len() / simd::f32x8_LENGTH];

    vq.par_iter_mut()
        .zip(q.par_chunks_exact(simd::f32x8_LENGTH))
        .for_each(|(vq, q)| {
            *vq = simd::from_slice(q);
        });
    vv.par_iter_mut()
        .zip(v.par_chunks_exact(simd::f32x8_LENGTH))
        .for_each(|(vv, v)| {
            *vv = simd::from_slice(v);
        });

    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        simd_dot_call(vq.as_ref(), vv.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

pub fn par_simd_dot_call(q: &[simd::f32x8], v: &[simd::f32x8], result: &mut [f32]) {
    result
        .into_par_iter()
        .zip(v.par_chunks_exact(VECTOR_SIZE / simd::f32x8_LENGTH))
        .for_each(|(result, v)| unsafe {
            *result = dot_simd_f32_1024_data_parallel(q, v);
        })
}

fn par_simd_dot_call_bench(bench: &mut Bencher) {
    let q = vec![1.0 as f32; VECTOR_SIZE];
    let v = vec![2.0 as f32; ITEM_COUNT * VECTOR_SIZE];

    let mut vq = std::vec![simd::f32x8_infty(); q.len() / simd::f32x8_LENGTH];
    let mut vv = std::vec![simd::f32x8_infty(); v.len() / simd::f32x8_LENGTH];

    vq.par_iter_mut()
        .zip(q.par_chunks_exact(simd::f32x8_LENGTH))
        .for_each(|(vq, q)| {
            *vq = simd::from_slice(q);
        });
    vv.par_iter_mut()
        .zip(v.par_chunks_exact(simd::f32x8_LENGTH))
        .for_each(|(vv, v)| {
            *vv = simd::from_slice(v);
        });

    let _ = rayon::ThreadPoolBuilder::new()
        .num_threads(THREAD_COUNT)
        .build_global();

    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        par_simd_dot_call(vq.as_ref(), vv.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

pub fn par_simd_dot_call_locality(
    q: &[simd::f32x8],
    v: &[Arc<Vec<simd::f32x8>>],
    result: &mut [f32],
) {
    result
        .into_par_iter()
        .zip(v.par_iter())
        .for_each(|(result, v)| unsafe {
            *result = dot_simd_f32_1024_data_parallel(q, v.as_ref());
        })
}

fn par_simd_dot_call_good_locality_bench(bench: &mut Bencher) {
    let q = vec![1.0 as f32; VECTOR_SIZE];
    let v = vec![vec![2.0 as f32; VECTOR_SIZE]; ITEM_COUNT];
    let mut vq = vec![simd::f32x8_infty(); VECTOR_SIZE / simd::f32x8_LENGTH];
    let mut vv =
        vec![Arc::new(vec![simd::f32x8_infty(); VECTOR_SIZE / simd::f32x8_LENGTH]); ITEM_COUNT];

    vq.par_iter_mut()
        .zip(q.par_chunks_exact(simd::f32x8_LENGTH))
        .for_each(|(vq, q)| {
            *vq = simd::from_slice(q);
        });
    vv.par_iter_mut().zip(v.par_iter()).for_each(|(vv, v)| {
        *vv = Arc::new(
            v.chunks_exact(simd::f32x8_LENGTH)
                .map(simd::from_slice)
                .collect::<Vec<_>>(),
        );
    });

    let _ = rayon::ThreadPoolBuilder::new()
        .num_threads(THREAD_COUNT)
        .build_global();

    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        par_simd_dot_call_locality(vq.as_ref(), vv.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

fn par_simd_dot_call_poor_locality_bench(bench: &mut Bencher) {
    let q = vec![1.0 as f32; VECTOR_SIZE];
    let v = vec![vec![2.0 as f32; VECTOR_SIZE]; ITEM_COUNT];
    let mut vq = vec![simd::f32x8_infty(); VECTOR_SIZE / simd::f32x8_LENGTH];
    let mut vv =
        vec![Arc::new(vec![simd::f32x8_infty(); VECTOR_SIZE / simd::f32x8_LENGTH]); ITEM_COUNT];

    vq.par_iter_mut()
        .zip(q.par_chunks_exact(simd::f32x8_LENGTH))
        .for_each(|(vq, q)| {
            *vq = simd::from_slice(q);
        });
    vv.par_iter_mut().zip(v.par_iter()).for_each(|(vv, v)| {
        *vv = Arc::new(
            v.chunks_exact(simd::f32x8_LENGTH)
                .map(simd::from_slice)
                .collect::<Vec<_>>(),
        );
    });
    for n in 0..ITEM_COUNT {
        // rearrange vv elements semi-randomly to simulate poor locality.
        // 127 is prime so all elements should be moved.
        vv.swap(n, 127 * n % ITEM_COUNT);
    }

    let _ = rayon::ThreadPoolBuilder::new()
        .num_threads(THREAD_COUNT)
        .build_global();

    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        par_simd_dot_call_locality(vq.as_ref(), vv.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

mod asm_simd {
    // simd module above adapted for Sapphire Rapid *ph (packed f16) ops
    use half::f16;

    use std::arch::asm;
    use std::arch::x86_64::*; // Intel SIMD intrinsic mappings

    #[allow(non_camel_case_types)]
    pub type f16x16 = __m256i;
    #[allow(non_upper_case_globals)]
    pub const f16x16_LENGTH: usize = 16;

    #[inline]
    pub fn f16x16_nil() -> f16x16 {
        unsafe { std::mem::transmute([f16::from_f32(0.0); f16x16_LENGTH]) }
    }

    #[inline]
    pub fn from_slice(s: &[f16]) -> f16x16 {
        assert_eq!(s.len(), f16x16_LENGTH);
        let arr: [f16; f16x16_LENGTH] = [
            s[0], s[1], s[2], s[3], s[4], s[5], s[6], s[7], s[8], s[9], s[10], s[11], s[12], s[13],
            s[14], s[15],
        ];
        unsafe { std::mem::transmute(arr) }
    }

    #[inline]
    pub fn to_slice(v: f16x16) -> [f16; f16x16_LENGTH] {
        unsafe { std::mem::transmute(v) }
    }

    #[inline]
    pub fn f16x16_mul(a: f16x16, b: f16x16) -> f16x16 {
        unsafe {
            let dst: f16x16;
            asm!(
                "vmulph {0}, {1}, {2}",
                out(ymm_reg) dst,
                in(ymm_reg) a,
                in(ymm_reg) b,
                options(pure, nomem, nostack),
            );
            dst
        }
    }

    #[inline]
    pub fn f16x16_add(a: f16x16, b: f16x16) -> f16x16 {
        unsafe {
            let dst: f16x16;
            asm!(
                "vaddph {0}, {1}, {2}",
                out(ymm_reg) dst,
                in(ymm_reg) a,
                in(ymm_reg) b,
                options(pure, nomem, nostack),
            );
            dst
        }
    }

    #[inline]
    pub fn f16x16_fmadd(a: f16x16, b: f16x16, dst: *mut f16x16) {
        unsafe {
            asm!(
                "vfmadd231ph {0}, {1}, {2}",
                inout(ymm_reg) *dst,
                in(ymm_reg) a,
                in(ymm_reg) b,
                options(pure, nomem, nostack),
            );
        }
    }

    #[inline]
    pub fn f16x16_sum(a: f16x16) -> f32 {
        let arr: [f16; 16] = to_slice(a);
        f32::from(
            arr[0]
                + arr[1]
                + arr[2]
                + arr[3]
                + arr[4]
                + arr[5]
                + arr[6]
                + arr[7]
                + arr[8]
                + arr[9]
                + arr[10]
                + arr[11]
                + arr[12]
                + arr[13]
                + arr[14]
                + arr[15],
        )
    }

    #[inline]
    pub fn prefetch(p: *const f16x16, offset: isize) {
        unsafe { _mm_prefetch(p.offset(offset) as *const i8, _MM_HINT_T0) }
    }
}

use half::f16;

fn dot_simd_f16_1024_data_parallel(q: &[asm_simd::f16x16], v: &[asm_simd::f16x16]) -> f32 {
    let mut block: [asm_simd::f16x16; SIMD_BLOCK_SIZE] = [asm_simd::f16x16_nil(); SIMD_BLOCK_SIZE];
    q.chunks_exact(SIMD_BLOCK_SIZE)
        .zip(v.chunks_exact(SIMD_BLOCK_SIZE))
        .for_each(|(qi, vi)| {
            let q_ptr = qi.as_ptr();
            let v_ptr = vi.as_ptr();
            asm_simd::prefetch(q_ptr, PREFETCH_OFFSET);
            asm_simd::prefetch(v_ptr, PREFETCH_OFFSET);
            for (col, (block, (a, b))) in block.iter_mut().zip(qi.iter().zip(vi.iter())).enumerate()
            {
                asm_simd::f16x16_fmadd(*a, *b, block);
                // alternate implementation (may be more reliable since it never modifies in-place)
                // *block = asm_simd::f16x16_add(*block, asm_simd::f16x16_mul(*a, *b));
            }
        });
    block.iter().map(|v| asm_simd::f16x16_sum(*v)).sum::<f32>()
}

pub fn asm_simd_dot_call(q: &[asm_simd::f16x16], v: &[asm_simd::f16x16], result: &mut [f32]) {
    result
        .iter_mut()
        .zip(v.chunks_exact(VECTOR_SIZE / asm_simd::f16x16_LENGTH))
        .for_each(|(result, v)| unsafe {
            *result = dot_simd_f16_1024_data_parallel(q, v);
        })
}

fn asm_simd_dot_call_bench(bench: &mut Bencher) {
    let q = vec![f16::from_f32(1.0); VECTOR_SIZE];
    let v = vec![f16::from_f32(2.0); ITEM_COUNT * VECTOR_SIZE];

    let mut vq = std::vec![asm_simd::f16x16_nil(); q.len() / asm_simd::f16x16_LENGTH];
    let mut vv = std::vec![asm_simd::f16x16_nil(); v.len() / asm_simd::f16x16_LENGTH];

    vq.par_iter_mut()
        .zip(q.par_chunks_exact(asm_simd::f16x16_LENGTH))
        .for_each(|(vq, q)| {
            *vq = asm_simd::from_slice(q);
        });
    vv.par_iter_mut()
        .zip(v.par_chunks_exact(asm_simd::f16x16_LENGTH))
        .for_each(|(vv, v)| {
            *vv = asm_simd::from_slice(v);
        });

    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        asm_simd_dot_call(vq.as_ref(), vv.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

pub fn par_asm_simd_dot_call(q: &[asm_simd::f16x16], v: &[asm_simd::f16x16], result: &mut [f32]) {
    result
        .into_par_iter()
        .zip(v.par_chunks_exact(VECTOR_SIZE / asm_simd::f16x16_LENGTH))
        .for_each(|(result, v)| unsafe {
            *result = dot_simd_f16_1024_data_parallel(q, v);
        })
}

fn par_asm_simd_dot_call_bench(bench: &mut Bencher) {
    let q = vec![f16::from_f32(1.0); VECTOR_SIZE];
    let v = vec![f16::from_f32(2.0); ITEM_COUNT * VECTOR_SIZE];

    let mut vq = std::vec![asm_simd::f16x16_nil(); q.len() / asm_simd::f16x16_LENGTH];
    let mut vv = std::vec![asm_simd::f16x16_nil(); v.len() / asm_simd::f16x16_LENGTH];

    vq.par_iter_mut()
        .zip(q.par_chunks_exact(asm_simd::f16x16_LENGTH))
        .for_each(|(vq, q)| {
            *vq = asm_simd::from_slice(q);
        });
    vv.par_iter_mut()
        .zip(v.par_chunks_exact(asm_simd::f16x16_LENGTH))
        .for_each(|(vv, v)| {
            *vv = asm_simd::from_slice(v);
        });

    let _ = rayon::ThreadPoolBuilder::new()
        .num_threads(THREAD_COUNT)
        .build_global();

    bench.iter(|| {
        let mut r = [0.0 as f32; ITEM_COUNT];
        par_asm_simd_dot_call(vq.as_ref(), vv.as_ref(), r.as_mut());
        r.iter().sum::<f32>()
    })
}

benchmark_group!(
    benches,
    // VERY SLOW
    //baseline_bench,
    //dot_call_bench,
    //simd_dot_call_bench,
    par_dot_call_bench,
    par_simd_dot_call_bench,
    par_simd_dot_call_good_locality_bench,
    par_simd_dot_call_poor_locality_bench,
);
benchmark_main!(benches);

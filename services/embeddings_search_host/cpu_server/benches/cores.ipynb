{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GCE Performance Results\n", "n2_core_results = [\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 4_928_960,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 9_767_978,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 19_386_271,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 38_639_724,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 76_973_954,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 153_690_829,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 2_524_561,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 4_967_961,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 9_844_358,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 19_571_774,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 38_785_465,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 77_331_092,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 630_893,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 1_356_674,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 2_859_204,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 5_702_804,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 11_246_618,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 22_276_267,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 758_984,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 1_417_167,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 2_696_369,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 5_194_109,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 10_169_853,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 20_139_271,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 548_277,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 1_016_172,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 2_012_293,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 3_937_284,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 7_832_574,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 15_592_050,\n", "    },\n", "]\n", "\n", "# GCE Performance Results\n", "n2_vcpu_results = [\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 3_101_725,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 6_698_296,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 11_393_004,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 22_574_401,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 44_967_979,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 1,\n", "        \"par_simd_dot_call_bench\": 89_843_088,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 862_409,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 2_239_668,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 5_324_708,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 9_704_702,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 19_821_967,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 2,\n", "        \"par_simd_dot_call_bench\": 41_794_937,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 890_350,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 1_722_823,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 3_239_262,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 6_228_479,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 11_898_815,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 4,\n", "        \"par_simd_dot_call_bench\": 23_536_262,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 416_062,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 853_593,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 1_682_205,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 3_317_843,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 6_549_601,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 8,\n", "        \"par_simd_dot_call_bench\": 13_015_141,\n", "    },\n", "     {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 492_064,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 1_108_742,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 2_196_634,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 4_223_865,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 8_310_321,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 16,\n", "        \"par_simd_dot_call_bench\": 16_240_155,\n", "    },\n", "]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "for thread_count in [1, 2, 4, 8, 16]:\n", "    x = [r[\"count\"] for r in n2_vcpu_results if r[\"threads\"] == thread_count]\n", "    y = [r[\"par_simd_dot_call_bench\"] for r in n2_vcpu_results if r[\"threads\"] == thread_count]\n", "    plt.scatter(x, y)\n", "    plt.plot(np.unique(x), np.poly1d(np.polyfit(x, y, 1))(np.unique(x)), label=\"_nolegend_\")\n", "\n", "plt.ylabel(\"Latency (ns)\")\n", "plt.xlabel(\"Embeddings count\")\n", "plt.title(\"512D embedding dot products, n2 + f32\")\n", "plt.legend([\"n2-standard-2\", \"n2-standard-4\", \"n2-standard-8\", \"n2-standard-16\", \"n2-standard-32\"])\n", "plt.xscale(\"log\", base=2)\n", "plt.yscale(\"log\", base=2)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c3_core_results = [\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 1,\n", "        \"par_asm_simd_dot_call_bench\": 1_374_404,\n", "        \"par_simd_dot_call_bench\": 1_198_724,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 1,\n", "        \"par_asm_simd_dot_call_bench\": 2_845_263,\n", "        \"par_simd_dot_call_bench\": 3_775_043,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 1,\n", "        \"par_asm_simd_dot_call_bench\": 5_621_831,\n", "        \"par_simd_dot_call_bench\": 9_448_322,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 1,\n", "        \"par_asm_simd_dot_call_bench\": 10_674_275,\n", "        \"par_simd_dot_call_bench\": 19_571_586,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 1,\n", "        \"par_asm_simd_dot_call_bench\": 22_544_325,\n", "        \"par_simd_dot_call_bench\": 40_161_505,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 1,\n", "        \"par_asm_simd_dot_call_bench\": 44_206_697,\n", "        \"par_simd_dot_call_bench\": 81_178_249,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 728_212,\n", "        \"par_simd_dot_call_bench\": 668_515, \n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 1_438_457,\n", "        \"par_simd_dot_call_bench\": 2_030_911,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 2_950_380,\n", "        \"par_simd_dot_call_bench\": 4_640_047,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 5_948_825,\n", "        \"par_simd_dot_call_bench\": 10_307_454,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 12_022_953,\n", "        \"par_simd_dot_call_bench\": 20_923_634,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 24_112_095,\n", "        \"par_simd_dot_call_bench\": 41_900_245,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 366_327,\n", "        \"par_simd_dot_call_bench\": 342_840,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 716_326,\n", "        \"par_simd_dot_call_bench\": 936_709,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 1_395_442,\n", "        \"par_simd_dot_call_bench\": 2_391_897,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 2_800_908,\n", "        \"par_simd_dot_call_bench\": 5_068_661,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 5_670_035,\n", "        \"par_simd_dot_call_bench\": 10_331_940,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 11_350_370,\n", "        \"par_simd_dot_call_bench\": 20_535_909,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 223_730,\n", "        \"par_simd_dot_call_bench\": 228_635,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 364_613,\n", "        \"par_simd_dot_call_bench\": 450_960,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 659_756,\n", "        \"par_simd_dot_call_bench\": 1_249_154,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 1_408_310,\n", "        \"par_simd_dot_call_bench\": 2_660_884,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 2_906_983,\n", "        \"par_simd_dot_call_bench\": 5_378_766,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 5_788_517,\n", "        \"par_simd_dot_call_bench\": 10_677_111,\n", "    },\n", "]\n", "\n", "c3_vcpu_results = [\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 597_431,\n", "        \"par_simd_dot_call_bench\": 612_486, \n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 1_170_014,\n", "        \"par_simd_dot_call_bench\": 1_614_223,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 2_241_465,\n", "        \"par_simd_dot_call_bench\": 4_195_723,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 4_541_444,\n", "        \"par_simd_dot_call_bench\": 8_625_531,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 9_332_513,\n", "        \"par_simd_dot_call_bench\": 17_949_872,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 2,\n", "        \"par_asm_simd_dot_call_bench\": 18_614_759,\n", "        \"par_simd_dot_call_bench\": 35_862_397,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 331_905,\n", "        \"par_simd_dot_call_bench\": 343_830,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 616_150,\n", "        \"par_simd_dot_call_bench\": 861_623,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 1_172_942,\n", "        \"par_simd_dot_call_bench\": 2_086_547,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 2_308_024,\n", "        \"par_simd_dot_call_bench\": 4_429_078,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 4_712_341,\n", "        \"par_simd_dot_call_bench\": 9_004_212,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 4,\n", "        \"par_asm_simd_dot_call_bench\": 9_559_588,\n", "        \"par_simd_dot_call_bench\": 17_954_482,\n", "    },\n", "    {\n", "        \"count\": 16 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 193_891,\n", "        \"par_simd_dot_call_bench\": 186_078,\n", "    },\n", "    {\n", "        \"count\": 32 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 363_886,\n", "        \"par_simd_dot_call_bench\": 452_884,\n", "    },\n", "    {\n", "        \"count\": 64 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 644_784,\n", "        \"par_simd_dot_call_bench\": 1_240_254,\n", "    },\n", "    {\n", "        \"count\": 128 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 1_333_099,\n", "        \"par_simd_dot_call_bench\": 2_600_192,\n", "    },\n", "    {\n", "        \"count\": 256 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 2_758_177,\n", "        \"par_simd_dot_call_bench\": 5_226_970,\n", "    },\n", "    {\n", "        \"count\": 512 * 1024,\n", "        \"threads\": 11,\n", "        \"par_asm_simd_dot_call_bench\": 5_483_705,\n", "        \"par_simd_dot_call_bench\": 10_327_367,\n", "    },\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "for thread_count in [2, 4, 11]:\n", "    x = [r[\"count\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    y = [r[\"par_simd_dot_call_bench\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    plt.scatter(x, y)\n", "    plt.plot(np.unique(x), np.poly1d(np.polyfit(x, y, 1))(np.unique(x)), label=\"_nolegend_\")\n", "\n", "plt.ylabel(\"Time (ns)\")\n", "plt.xlabel(\"Embeddings count\")\n", "plt.title(\"512D embedding dot products, c3 + f32\")\n", "plt.legend([\"c3-standard-4\", \"c3-standard-8\", \"c3-standard-22\"])\n", "plt.xscale(\"log\", base=2)\n", "plt.yscale(\"log\", base=2)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "for thread_count in [2, 4, 11]:\n", "    x = [r[\"count\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    y = [r[\"par_asm_simd_dot_call_bench\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    plt.scatter(x, y)\n", "    plt.plot(np.unique(x), np.poly1d(np.polyfit(x, y, 1))(np.unique(x)), label=\"_nolegend_\")\n", "\n", "plt.ylabel(\"Time (ns)\")\n", "plt.xlabel(\"Embeddings count\")\n", "plt.title(\"512D embedding dot products, c3 + f16\")\n", "plt.legend([\"c3-standard-4\", \"c3-standard-8\", \"c3-standard-22\"])\n", "plt.xscale(\"log\", base=2)\n", "plt.yscale(\"log\", base=2)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install matplotlib\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "labels = []\n", "values = []\n", "\n", "# Use only vCPU results since they better reflect the max performance we can get from a VM\n", "for thread_count in [1, 2, 4, 8, 16]:\n", "    x = [r[\"count\"] for r in n2_vcpu_results if r[\"threads\"] == thread_count]\n", "    y = [r[\"par_simd_dot_call_bench\"] for r in n2_vcpu_results if r[\"threads\"] == thread_count]\n", "    values.append(np.poly1d(np.polyfit(x, y, 1))[1]) # [1] is the linear coefficient, i.e. rate\n", "    labels.append(\"n2-standard-{}, f32\".format(thread_count * 2))\n", "\n", "for thread_count in [2, 4, 11]:\n", "    x = [r[\"count\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    y = [r[\"par_simd_dot_call_bench\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    values.append(np.poly1d(np.polyfit(x, y, 1))[1])\n", "    labels.append(\"c3-standard-{}, f32\".format(thread_count * 2))\n", "\n", "for thread_count in [2, 4, 11]:\n", "    x = [r[\"count\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    y = [r[\"par_asm_simd_dot_call_bench\"] for r in c3_vcpu_results if r[\"threads\"] == thread_count]\n", "    values.append(np.poly1d(np.polyfit(x, y, 1))[1])\n", "    labels.append(\"c3-standard-{}, f16\".format(thread_count * 2))\n", "\n", "barlist = plt.bar(labels, values)\n", "colors = ['r', 'r', 'r', 'r', 'r', 'b', 'b', 'b', 'g', 'g', 'g']\n", "for bar, color in zip(barlist, colors):\n", "    bar.set_color(color)\n", "\n", "plt.xticks(rotation=60, ha='right')\n", "plt.ylabel(\"ns per embedding at scale\")\n", "plt.grid(axis='y')\n", "plt.title(\"Cross-platform comparison\")\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 2}
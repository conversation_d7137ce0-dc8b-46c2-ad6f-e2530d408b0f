#[macro_use]
extern crate bencher;

use bencher::<PERSON><PERSON>;
use half::f16;

// everything in f16 but cast at the end
pub fn dot_1024_all_f16_unsafe(a: &[f16], b: &[f16]) -> f32 {
    let mut r: f16 = f16::from_f32(0.0);
    unsafe {
        for i in 0..1024 {
            r += a.get_unchecked(i) * b.get_unchecked(i);
        }
    }
    return f32::from(r);
}

fn dot_1024_all_f16_unsafe_bench(bench: &mut Bencher) {
    let a = [f16::from_f32(1.0); 1024];
    let b = [f16::from_f32(2.0); 1024];
    bench.iter(|| dot_1024_all_f16_unsafe(a.as_ref(), b.as_ref()))
}

pub fn dot_1024_unsafe(a: &[f16], b: &[f16]) -> f32 {
    let mut r: f32 = 0.0;
    unsafe {
        for i in 0..1024 {
            r += a.get_unchecked(i).to_f32() * b.get_unchecked(i).to_f32();
        }
    }
    return r;
}

fn dot_1024_unsafe_bench(bench: &mut Bencher) {
    let a = [f16::from_f32(1.0); 1024];
    let b = [f16::from_f32(2.0); 1024];
    bench.iter(|| dot_1024_unsafe(a.as_ref(), b.as_ref()))
}

pub fn dot_1024_cast_unsafe(a: &[f16], b: &[f16]) -> f32 {
    let mut r: f32 = 0.0;
    unsafe {
        for i in 0..1024 {
            r += a.get_unchecked(i).to_f32() * b.get_unchecked(i).to_f32();
        }
    }
    return r;
}

fn dot_1024_cast_unsafe_bench(bench: &mut Bencher) {
    let a = [f16::from_f32(1.0); 1024];
    let b = [f16::from_f32(2.0); 1024];
    bench.iter(|| dot_1024_cast_unsafe(a.as_ref(), b.as_ref()))
}

pub fn dot_1024_f32_unsafe(a: &[f32], b: &[f32]) -> f32 {
    let mut r: f32 = 0.0;
    unsafe {
        for i in 0..1024 {
            r += a.get_unchecked(i) * b.get_unchecked(i);
        }
    }
    return r;
}

fn dot_1024_f32_unsafe_bench(bench: &mut Bencher) {
    let a = [1.0 as f32; 1024];
    let b = [2.0 as f32; 1024];
    bench.iter(|| dot_1024_f32_unsafe(a.as_ref(), b.as_ref()))
}

pub fn dot_1024(a: &[f16], b: &[f16]) -> f32 {
    a.iter()
        .zip(b.iter())
        .map(|(ai, bi)| ai.to_f32() * bi.to_f32())
        .sum()
}

fn dot_1024_bench(bench: &mut Bencher) {
    let a = [f16::from_f32(1.0); 1024];
    let b = [f16::from_f32(2.0); 1024];
    bench.iter(|| dot_1024(a.as_ref(), b.as_ref()))
}

const BLOCK_SIZE: usize = 16;

pub fn dot_1024_data_parallel(a: &[f16; 1024], b: &[f16; 1024]) -> f32 {
    let mut block: [f32; BLOCK_SIZE] = [0.0; BLOCK_SIZE];
    a.chunks_exact(BLOCK_SIZE)
        .zip(b.chunks_exact(BLOCK_SIZE))
        .for_each(|(ai, bi)| {
            for (block, (a, b)) in block.iter_mut().zip(ai.iter().zip(bi.iter())) {
                *block += a.to_f32() * b.to_f32();
            }
        });
    block.iter().sum::<f32>()
}

fn dot_1024_data_parallel_bench(bench: &mut Bencher) {
    let a = [f16::from_f32(1.0); 1024];
    let b = [f16::from_f32(2.0); 1024];
    bench.iter(|| dot_1024_data_parallel(&a, &b))
}

pub fn dot_f32_1024_data_parallel(a: &[f32], b: &[f32]) -> f32 {
    let mut block: [f32; BLOCK_SIZE] = [0.0; BLOCK_SIZE];
    a.chunks_exact(BLOCK_SIZE)
        .zip(b.chunks_exact(BLOCK_SIZE))
        .for_each(|(ai, bi)| {
            for (block, (a, b)) in block.iter_mut().zip(ai.iter().zip(bi.iter())) {
                *block += a * b;
            }
        });
    block.iter().sum::<f32>()
}

fn dot_1024_f32_data_parallel_bench(bench: &mut Bencher) {
    let a = [1.0 as f32; 1024];
    let b = [2.0 as f32; 1024];
    bench.iter(|| dot_f32_1024_data_parallel(a.as_ref(), b.as_ref()))
}

benchmark_group!(
    benches,
    dot_1024_all_f16_unsafe_bench,
    dot_1024_f32_unsafe_bench,
    dot_1024_f32_data_parallel_bench,
    dot_1024_data_parallel_bench,
    dot_1024_bench,
    dot_1024_unsafe_bench
);
benchmark_main!(benches);

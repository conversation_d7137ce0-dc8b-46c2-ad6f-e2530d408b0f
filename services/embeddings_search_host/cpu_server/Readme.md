# CPU-based server for embeddings search

The embeddings search host is performs a high-performance vector search.
Togehter with the content manager, it can be seen as storage-disintegrated vector database.
It is a central part of the dense retrieval system.

The embeddings search host is a gRPC server that implements the `embeddings_search.proto` protocol.

A search operation is usually for a tensor as query and a list of blob names identified by a "Blobs" object.
On a logical level, The searcher will download the embeddings (subkey embeddings.npy) and the chunk information (subkey chunk-X.pb) for each blob.
It will perform an maximum inner product search for each blob and return the top-k results.

The actual implementation overlaps downloading and searching and parallalizes the search over multiple CPU.

The embeddings searcher has a cache that should contain recently used embeddings and chunks.
However, if the searcher has to download embeddings and chunks from content manager, this can cause delays to the search request.

The help with the situation the searcher do
- transfer the content of the cache from a peer searcher when a new searcher starts up (cache replication)
- handled downloads with a special timeout. It will continue to download all blobs in the background so that the data
  is ready in the cache when the next search request comes in.
- cache sizing: The embeddings searcher should be sized so that most of the active working set is in the cache. Sized
  based cache eviction is expected to be rare.


## Security considerations

The embeddings search host operates on a derivate of customer data (an embeddings) that has to be protected
in the same way as direct customer data as well as chunks. It has to authenticate each request with a service token
and should never reveal data from one tenant to a different tenant.

## Authentication and authorization

The embeddings search host will authenticate each request with a service token.

## Benchmarking

The benchmarks code is in `benches`. The benchmarks are not able to share code with the `src` directory, so
they are essentially a copy. The benchmark are also not automatially tested.

```
RUSTFLAGS="-C target-feature=+avx2,+avx,+sse2,+avx512f,+avx512bw -C target-cpu=native" cargo bench
```

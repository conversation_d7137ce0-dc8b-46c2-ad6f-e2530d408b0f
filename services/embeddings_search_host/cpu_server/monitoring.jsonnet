local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local searchTimeoutSpec = {
    displayName: 'Embeddings Search timeouts warning',
    conditionPrometheusQueryLanguage: {
      duration: '1200s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      // This is a slightly weird way of expressing the condition, but it's more useful to graph "percent timed out" than "average latency"
      // (and more precise) due to how Prometheus histograms work. Each observation produces N entries, one for each `le` (less-than-equal)
      // bucket expressing a higher latency than the observed latency. Therefore, latencies over 1 second will be captured by the difference
      // in counts between the "1 second" bucket and the "positive infinity" bucket. (This relies on our timeout also being 1 second.)
      query: |||
        sum by (namespace, cluster) (increase(au_rpc_latency_histogram_bucket{service="EmbeddingsSearch",endpoint="SearchChunks",le="1"}[10m])) /
        sum by (namespace, cluster) (increase(au_rpc_latency_histogram_bucket{service="EmbeddingsSearch",endpoint="SearchChunks",le="+Inf"}[10m])) < 0.9
        and sum by (namespace, cluster) (increase(au_rpc_latency_histogram_bucket{service="EmbeddingsSearch",endpoint="SearchChunks",le="+Inf"}[10m])) >= 100
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(cloud, searchTimeoutSpec, 'embeddings-search-timeouts', 'Embeddings search timing out for more than 10%% of the last 20 minutes in namespace %s cluster %s' % [
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
    ]),
  ]

// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path, path::PathBuf};

fn get_base_dir() -> PathBuf {
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../").canonicalize().unwrap();
    root
}

fn get_external_dir() -> PathBuf {
    let root = get_base_dir();
    if std::env::var("USER").is_err() {
        root.join("..")
    } else {
        root.join("../bazel-augment-external")
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let external_dir = get_external_dir();
    let root = get_base_dir();
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());

    let protos = vec![
        root.join("services/request_insight/request_insight.proto"),
        root.join("services/embeddings_search_host/embeddings_search.proto"),
    ];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();

        let protobuf_src_dir = root.join("../protobuf~/src");
        let googleapis_dir = external_dir.join("googleapis~");
        let protobuf_duration_dir =
            root.join("../protobuf~/src/google/protobuf/_virtual_imports/duration_proto/");
        let protobuf_timestamp_dir =
            root.join("../protobuf~/src/google/protobuf/_virtual_imports/timestamp_proto/");
        let protobuf_any_dir =
            root.join("../protobuf~/src/google/protobuf/_virtual_imports/any_proto/");
        let includes = vec![
            proto_dir,
            protobuf_src_dir.as_ref(),
            googleapis_dir.as_ref(),
            protobuf_duration_dir.as_ref(),
            protobuf_timestamp_dir.as_ref(),
            protobuf_any_dir.as_ref(),
            &root,
        ];

        tonic_build::configure()
            .extern_path(".google.protobuf.Any", "::prost_wkt_types::Any")
            .extern_path(".google.protobuf.Duration", "::prost_wkt_types::Duration")
            .extern_path(".google.protobuf.Timestamp", "::prost_wkt_types::Timestamp")
            .extern_path(".google.protobuf.Value", "::prost_wkt_types::Value")
            .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")
            // ChatRequest, which is a nested sub-field of Event, is a bit too large compared to
            // other fields in the `oneof`
            .enum_attribute(
                "request_insight.RequestEvent.event",
                "#[allow(clippy::large_enum_variant)]",
            )
            .file_descriptor_set_path(out_dir.join("embeddings_search_descriptor.bin"))
            .compile_protos(&[proto_path], &includes)?;
    }
    Ok(())
}

load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:rust.bzl", "rust_binary", "rust_oci_image", "rust_test")

RUST_FLAGS = [
    "-C",
    "opt-level=3",
    "-C",
    "target-cpu=cascadelake",  # chosen to maintain compatibility with n2 family
    "-C",
    "target-feature=+avx2,+avx,+sse2,+avx512f",
]

rust_binary(
    name = "embeddings_search_cpu_server",
    srcs = glob(["src/**/*.rs"]),
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    rustc_flags = RUST_FLAGS,
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/blob_names:blob_names_rs_proto",
        "//base/blob_names/rust:blob_names",
        "//base/feature_flags:feature_flags_rs",
        "//base/logging:struct_logging_rs",
        "//base/metrics_server/rust:metrics_server",
        "//base/rust/numpy",
        "//base/rust/tracing-tonic",
        "//services/auth/central/server:auth_entities_rs_proto",
        "//services/content_manager:content_manager_rs_proto",
        "//services/content_manager/client:client_rs",
        "//services/lib/grpc/auth:grpc_auth",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/grpc/metrics:grpc_metrics",
        "//services/lib/grpc/service:grpc_service",
        "//services/lib/grpc/tls_config:grpc_tls_config_rs",
        "//services/lib/request_context:request_context_rs",
        "//services/request_insight/publisher:publisher_rs",
        "//services/token_exchange/client:client_rs",
        "//services/working_set/client:client_rs",
        "//third_party/scann_rs",
    ],
)

rust_test(
    name = "embeddings_search_cpu_server_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":embeddings_search_cpu_server",
    data = glob(["src/test_data/*.npy"]),
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//base/proto:tensor_proto",
        "//services/embeddings_search_host:embeddings_search_proto",
        "//services/request_insight:request_insight_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

rust_oci_image(
    name = "image",
    package_name = package_name(),
    binary = "embeddings_search_cpu_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services:__subpackages__"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    lint_allow_multiple_apps = True,
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//deploy/tenants:namespaces",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":monitoring_kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'embeddings-search-scann-host',
      kubecfg: {
        target: '//services/embeddings_search_host/scann_server:kubecfg',
        task: tenantNamespaces.dogfoodNamespaces,
      },
      deployment_schedule_name: 'EXPERIMENTAL',
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['dirk', 'luke'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

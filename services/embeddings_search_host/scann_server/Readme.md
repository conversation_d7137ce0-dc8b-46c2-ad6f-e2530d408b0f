# Scann-based Embeddings Search Server

Status: *PROTOTYPE*

See https://www.notion.so/Idea-Tree-based-Index-Generation-for-Embeddings-Search-135bba10175a80aeb803fae530263a1d?pvs=4

The general idea is to build an index per checkpoint.
The search on Blobs will add extra blobs outside the checkpoint and
remove Delete entries and merge the results.

The index on each checkpoint is done by splitting
the embeddings of the checkpoint up into chunk indexes.
The chunk indexes are created according to the directory structure.
The goal is to share chunk indexes between checkpoints.

The chunk index is created as a ScannIndex. Scann is a high-performance vector search library.

The implementation is EXTREMELY simple.

import pytest
import numpy as np

from services.embeddings_search_host.scann_server.indexes import (
    Embeddings,
    MinHashBuilder,
    NumpyIndex,
    MergeIndex,
)
from base.blob_names.python.blob_names import decode_blob_name


@pytest.fixture(
    params=[
        [True, False, 1],
        [False, False, 1],
        [True, False, 4],
        [False, False, 4],
    ]
)
def builder(request):
    exact, allowed_use_numpy, threads = request.param
    return MinHashBuilder(
        exact=exact,
        allowed_use_numpy=allowed_use_numpy,
        threads=threads,
        share=True,
        single_group=True,
    )


def test_minhash_builder_basic(builder):
    # Create sample embeddings data
    sample_embeddings = np.random.rand(4, 80).astype(
        np.float16
    )  # 4 chunks, 80 dimensions

    # Create two groups of embeddings with some overlap and some differences
    blob_names = [
        b"blob1",
        b"blob2",
        b"blob3",
        b"blob4",
        b"blob5",
        b"blob6",
    ]

    # First group (checkpoint 1)
    group1 = [
        Embeddings(blob_names[0], {}, sample_embeddings[0:1]),
        Embeddings(blob_names[1], {}, sample_embeddings[1:2]),
        Embeddings(blob_names[2], {}, sample_embeddings[2:3]),
    ]

    # Second group (checkpoint 2) - has some overlap with group1
    # blob1 and blob2 remain, blob3 is removed, blob4 and blob5 are added
    group2 = [
        Embeddings(blob_names[0], {}, sample_embeddings[0:1]),  # same as group1
        Embeddings(blob_names[1], {}, sample_embeddings[1:2]),  # same as group1
        Embeddings(blob_names[3], {}, sample_embeddings[2:3]),  # new blob
        Embeddings(blob_names[4], {}, sample_embeddings[3:4]),  # new blob
    ]

    # Build first index
    index1 = builder.build_index("tkey1", group1, "checkpoint1")
    assert len(builder.lsh_chunk_indexes["tkey1"]) == 1, "Should have one chunk index"

    # Build second index
    index2 = builder.build_index("tkey1", group2, "checkpoint2")
    assert (
        len(builder.lsh_chunk_indexes["tkey1"]) == 2
    ), "Should have two chunk indexes due to insufficient overlap"

    # Create a query vector
    query = np.random.rand(1, 80).astype(np.float16)

    # Test search with different removed blobs
    results1 = index1.search(query, num_results=10, blobs_removed=set())
    assert len(results1) > 0, "Should find results in first index"
    result_blobs1 = {chunk.blob_name for chunk in results1}
    assert result_blobs1 == {
        decode_blob_name(blob_names[0]),
        decode_blob_name(blob_names[1]),
        decode_blob_name(blob_names[2]),
    }, "Unexpected blobs in results"

    results2 = index2.search(query, num_results=10, blobs_removed=set())
    assert len(results2) > 0, "Should find results in second index"
    result_blobs2 = {chunk.blob_name for chunk in results2}
    assert result_blobs2 == {
        decode_blob_name(blob_names[0]),
        decode_blob_name(blob_names[1]),
        decode_blob_name(blob_names[3]),
        decode_blob_name(blob_names[4]),
    }, "Unexpected blobs in results"

    # Test search with removed blobs
    removed_blobs = {blob_names[0]}
    results_with_removed = index2.search(
        query, num_results=10, blobs_removed=removed_blobs
    )
    result_blobs_removed = {chunk.blob_name for chunk in results_with_removed}
    assert not (result_blobs_removed & removed_blobs), "Found removed blob in results"
    assert result_blobs_removed == {
        decode_blob_name(blob_names[1]),
        decode_blob_name(blob_names[3]),
        decode_blob_name(blob_names[4]),
    }, "Unexpected blobs in results"


def test_minhash_builder_reused_index_deletion(builder):
    # Create sample embeddings data
    sample_embeddings = np.random.rand(12, 80).astype(
        np.float16
    )  # 4 chunks, 80 dimensions

    # First group (checkpoint 1) - 11 embeddings
    group1 = [Embeddings(b"myblob", {}, sample_embeddings[:11])]

    # Second group (checkpoint 2) - keeps 10 embeddings, drops 1
    # This ensures >90% overlap
    group2 = [Embeddings(b"myblob", {}, sample_embeddings[:10])]

    # Build first index
    index1 = builder.build_index("tkey1", group1, "checkpoint1")
    assert len(builder.lsh_chunk_indexes["tkey1"]) == 1, "Should have one chunk index"

    # Build second index - should reuse first index due to high overlap
    index2 = builder.build_index("tkey1", group2, "checkpoint2")
    assert (
        len(builder.lsh_chunk_indexes["tkey1"]) == 1
    ), "Should still have one chunk index due to high overlap"

    # Create a query vector
    query = np.random.rand(1, 80).astype(np.float16)

    # Test search results
    results1 = index1.search(query, num_results=20, blobs_removed=set())
    results2 = index2.search(query, num_results=20, blobs_removed=set())

    # Verify the results contain the expected blobs
    result_blobs1 = {chunk.chunk_index for chunk in results1}
    result_blobs2 = {chunk.chunk_index for chunk in results2}

    # First index should have 11 blobs
    expected_blobs1 = {i for i in range(11)}
    assert result_blobs1 == expected_blobs1, "Unexpected blobs in results"

    # Second index should have 10 blobs
    expected_blobs2 = {i for i in range(10)}
    assert result_blobs2 == expected_blobs2, "Unexpected blobs in results"


def test_minhash_builder_reused_index_addition(builder):
    # Create sample embeddings data
    sample_embeddings = np.random.rand(12, 80).astype(
        np.float16
    )  # 4 chunks, 80 dimensions

    # First group (checkpoint 1) - 11 embeddings
    group1 = [Embeddings(b"myblob", {}, sample_embeddings[:11])]

    # Second group (checkpoint 2) - keeps 10 embeddings, drops 1
    # This ensures >90% overlap
    group2 = [Embeddings(b"myblob", {}, sample_embeddings[:12])]

    # Build first index
    index1 = builder.build_index("tkey1", group1, "checkpoint1")
    assert len(builder.lsh_chunk_indexes["tkey1"]) == 1, "Should have one chunk index"

    # Build second index - should reuse first index due to high overlap
    index2 = builder.build_index("tkey1", group2, "checkpoint2")
    assert (
        len(builder.lsh_chunk_indexes["tkey1"]) == 1
    ), "Should still have one chunk index due to high overlap"

    # Create a query vector
    query = np.random.rand(1, 80).astype(np.float16)

    # Test search results
    results1 = index1.search(query, num_results=20, blobs_removed=set())
    results2 = index2.search(query, num_results=20, blobs_removed=set())

    # Verify the results contain the expected blobs
    result_blobs1 = {chunk.chunk_index for chunk in results1}
    result_blobs2 = {chunk.chunk_index for chunk in results2}

    # First index should have 11 blobs
    expected_blobs1 = {i for i in range(11)}
    assert result_blobs1 == expected_blobs1, "Unexpected blobs in results"

    # Second index should have 12 blobs
    expected_blobs2 = {i for i in range(12)}
    assert result_blobs2 == expected_blobs2, "Unexpected blobs in results"


def test_minhash_builder_multi_groups(builder):
    blob_names = [
        b"1blob",
        b"2blob",
        b"3blob",
        b"4blob",
        b"5blob",
        b"6blob",
    ]
    groups = [
        Embeddings(blob_name, {}, np.random.rand(20, 80).astype(np.float16))
        for blob_name in blob_names
    ]
    index = builder.build_index("tkey1", groups, "checkpoint1")
    assert (
        len(builder.lsh_chunk_indexes["tkey1"]) == 1
    ), "Should have one chunk index per blob"

    query = np.random.rand(1, 80).astype(np.float16)
    results1 = index.search(query, num_results=32, blobs_removed=set())
    result_blobs1 = {chunk.blob_name for chunk in results1}

    assert len(results1) == 32, "Unexpected number of results"
    assert result_blobs1 == {
        decode_blob_name(blob_name) for blob_name in blob_names
    }, "Unexpected blobs in results"

    results2 = index.search(query, num_results=30, blobs_removed={b"1blob", b"2blob"})
    result_blobs2 = {chunk.blob_name for chunk in results2}

    assert len(results2) == 30, "Unexpected number of results"
    assert result_blobs2 == {
        decode_blob_name(blob_name) for blob_name in blob_names[2:]
    }, "Unexpected blobs in results"


def test_minhash_builder_multiple_transformation_keys(builder):
    # Create sample embeddings data
    sample_embeddings = np.random.rand(12, 80).astype(
        np.float16
    )  # 12 chunks, 80 dimensions

    # Groups for transformation key 1
    tkey1_group = [
        Embeddings(b"blob1", {}, sample_embeddings[:11]),
    ]

    # Groups for transformation key 2 (mostly shared so the logic should otherwise share the index)
    tkey2_group = [
        Embeddings(b"blob1", {}, sample_embeddings[:12]),
    ]

    # Build indexes for both transformation keys
    tkey1_index = builder.build_index("tkey1", tkey1_group, "checkpoint1")
    tkey2_index = builder.build_index("tkey2", tkey2_group, "checkpoint1")

    # Verify separate indexes were created for each transformation key
    assert (
        len(builder.lsh_chunk_indexes["tkey1"]) == 1
    ), "Should have one chunk index for tkey1"
    assert (
        len(builder.lsh_chunk_indexes["tkey2"]) == 1
    ), "Should have one chunk index for tkey2"
    assert len(builder.lsh_chunk_indexes) == 2, "Should have two transformation keys"

    # Create query vectors
    query = np.random.rand(1, 80).astype(np.float16)

    # Test search results for transformation key 1
    results_tkey1 = tkey1_index.search(query, num_results=2, blobs_removed=set())
    results_tkey2 = tkey2_index.search(query, num_results=2, blobs_removed=set())

    # Verify results for tkey1
    result_blobs_tkey1 = {chunk.blob_name for chunk in results_tkey1}

    assert result_blobs_tkey1 == {
        decode_blob_name(b"blob1")
    }, "Unexpected blobs in tkey1 results"

    # Verify results for tkey2
    result_blobs_tkey2 = {chunk.blob_name for chunk in results_tkey2}

    assert result_blobs_tkey2 == {
        decode_blob_name(b"blob1"),
    }, "Unexpected blobs in tkey2 results"


def test_merge_index():
    # Create sample embeddings data
    sample_embeddings = np.random.rand(4, 80).astype(
        np.float16
    )  # 4 chunks, 80 dimensions

    # Create two groups of embeddings
    blob_names = [
        b"blob1",
        b"blob2",
        b"blob3",
        b"blob4",
    ]

    # Create two separate indexes to merge
    group1 = [
        Embeddings(blob_names[0], {}, sample_embeddings[0]),
        Embeddings(blob_names[1], {}, sample_embeddings[1]),
    ]

    group2 = [
        Embeddings(blob_names[1], {}, sample_embeddings[1]),
        Embeddings(blob_names[2], {}, sample_embeddings[2]),
        Embeddings(blob_names[3], {}, sample_embeddings[3]),
    ]

    # Create two separate NumpyIndexes
    index1 = NumpyIndex(group1)
    index2 = NumpyIndex(group2)

    # Create merged index
    merged_index = MergeIndex([index1, index2])

    # Create a query vector
    query = np.random.rand(1, 80).astype(np.float16)

    # Test individual indexes first
    results1 = index1.search(query, num_results=5, blobs_removed=set())
    results2 = index2.search(query, num_results=5, blobs_removed=set())

    # Verify individual results
    result_blobs1 = {chunk.blob_name for chunk in results1}
    result_blobs2 = {chunk.blob_name for chunk in results2}

    assert result_blobs1 == {
        decode_blob_name(blob_names[0]),
        decode_blob_name(blob_names[1]),
    }, "Unexpected blobs in index1 results"

    assert result_blobs2 == {
        decode_blob_name(blob_names[1]),
        decode_blob_name(blob_names[2]),
        decode_blob_name(blob_names[3]),
    }, "Unexpected blobs in index2 results"

    # Test merged index
    merged_results = merged_index.search(query, num_results=5, blobs_removed=set())
    merged_blobs = {chunk.blob_name for chunk in merged_results}

    # Verify merged results contain all blobs
    assert merged_blobs == {
        decode_blob_name(blob_names[0]),
        decode_blob_name(blob_names[1]),
        decode_blob_name(blob_names[2]),
        decode_blob_name(blob_names[3]),
    }, "Merged index should contain all blobs"

    # Test with removed blobs
    removed_blobs = {blob_names[0], blob_names[2]}
    filtered_results = merged_index.search(
        query, num_results=4, blobs_removed=removed_blobs
    )
    filtered_blobs = {chunk.blob_name for chunk in filtered_results}

    # Verify filtered results exclude removed blobs
    assert filtered_blobs == {
        decode_blob_name(blob_names[1]),
        decode_blob_name(blob_names[3]),
    }, "Filtered results should exclude removed blobs"

    # Verify results are sorted by score
    assert all(
        filtered_results[i].score >= filtered_results[i + 1].score
        for i in range(len(filtered_results) - 1)
    ), "Results should be sorted by score"

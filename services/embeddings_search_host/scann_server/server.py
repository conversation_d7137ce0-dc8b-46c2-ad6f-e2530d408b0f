"""Server for the route guide service."""

import argparse
import io
import itertools
import logging
import os
import pathlib
import threading
import time
import typing
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

import grpc
import numpy
import opentelemetry
import opentelemetry.instrumentation.grpc
import prometheus_client
import scann

from services.embeddings_search_host.scann_server.indexes import (
    Embeddings,
    FlatBuilder,
    Index,
    MergeIndex,
    MinHashBuilder,
    NumpyIndex,
    RemovedIndexWrapper,
    TreeBuilder,
)
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import base.feature_flags
import base.tracing
import services.lib.grpc.tls_config.tls_config as tls_config
from base.blob_names import blob_names_pb2
from base.blob_names.python.blob_names import decode_blob_name, encode_blob_name
from base.caching.lru_cache import LRUCache
from base.caching.cache_metrics import CacheMetrics
from base.logging.struct_logging import setup_struct_logging
from base.proto import tensor
from base.python.grpc import client_options
from base.python.signal_handler.signal_handler import GracefulSignalHandler

from services.content_manager.client.content_manager_client import (
    ContentKey,
    ContentManagerClient,
)
from services.embeddings_search_host import (
    embeddings_search_pb2,
    embeddings_search_pb2_grpc,
)
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_auth import (
    AuthInfo,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.request_context.request_context import RequestContext
from services.token_exchange.client import client as token_exchange_client

log = structlog.get_logger()


tracer = base.tracing.setup_opentelemetry()

# Cache metrics are a decent proxy for many of the behaviors we care about
checkpoint_cache_metrics = CacheMetrics(
    prefix="au_scann_checkpoint_cache", name="checkpoint"
)
chunk_cache_metrics = CacheMetrics(prefix="au_scann_chunk_cache", name="chunk")
embeddings_cache_metrics = CacheMetrics(
    prefix="au_scann_embeddings_cache", name="embeddings"
)
# Track how the deltas from the checkpoint to the blob set behave
added_blobs_histogram = prometheus_client.Histogram(
    "au_scann_added_blobs",
    "Number of blobs added to checkpoint",
    ["model", "request_source"],
    buckets=[1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048],
)
deleted_blobs_histogram = prometheus_client.Histogram(
    "au_scann_deleted_blobs",
    "Number of blobs deleted from checkpoint",
    ["model", "request_source"],
    buckets=[1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048],
)


class SearchException(grpc.RpcError):
    """Exception thrown during search processing.

    Note that subclasses of grpc.RpcError are expected to have code() and details() methods even
    though RpcError itself does not."""

    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.msg

    def __str__(self) -> str:
        return f"CompletionException({self.status_code}: {self.msg})"


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for the example server."""

    # the port the grpc server will listen on
    port: int

    content_manager_endpoint: str

    # the path to the feature flag sdk key
    feature_flags_sdk_key_path: typing.Optional[str]

    # the endpoint for the dynamic feature flags service or None if not used
    dynamic_feature_flags_endpoint: typing.Optional[str]

    # the configuration for the token authentication
    auth_config: AuthConfig

    # TLS configuration for the central clients
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the client to talk to GRPC services in the same namespace
    client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the server
    server_mtls: typing.Optional[tls_config.ServerConfig] = None

    # Grace period for the server to shutdown
    shutdown_grace_period_s: int = 25

    threads: int = 8

    mode: str = "tree"

    allowed_use_numpy: bool = False

    scann_exact: bool = False

    share: bool = True

    single_group: bool = False

    allow_missing: bool = False
    """If true, allow missing embeddings to be returned.

    If true, a wrong index might be cached as some embeddings are missing, but sometimes
    dev instances are many missing embeddings."""

    transformation_key_filters: list[str] | None = None

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )


def get_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


T = typing.TypeVar("T")


def batched(items: typing.Iterable[T], n: int) -> typing.Iterator[typing.Sequence[T]]:
    "Batch data into tuples of length n. The last batch may be shorter."
    # batched('ABCDEFG', 3) --> ABC DEF G
    if n < 1:
        raise ValueError("n must be at least one")
    it = iter(items)
    while batch := tuple(itertools.islice(it, n)):
        yield batch


class EmbeddingsSearchServicer(embeddings_search_pb2_grpc.EmbeddingsSearchServicer):
    """EmbeddingsSearchServicer RPC server."""

    def __init__(
        self,
        config: Config,
        content_manager_client: ContentManagerClient,
    ):
        self.config = config
        self.content_manager_client = content_manager_client
        self.lock = threading.Lock()
        if config.mode == "tree":
            logging.info("Using tree index builder")
            self.builder = TreeBuilder(config.scann_exact, config.threads)
        elif config.mode == "minhash":
            logging.info("Using minhash index builder")
            self.builder = MinHashBuilder(
                config.allowed_use_numpy,
                config.scann_exact,
                config.threads,
                config.share,
                config.single_group,
            )
        elif config.mode == "flat":
            logging.info("Using flat index builder")
            self.builder = FlatBuilder(
                config.allowed_use_numpy, config.scann_exact, config.threads
            )
        else:
            raise ValueError(f"Unknown mode: {config.mode}")
        # Total memory budget: 8GiB
        # For now we simply count entries. TODO: measure them better!
        # checkpoint cache: entry per checkpoint, support ~1k checkpoints
        self.checkpoint_cache: LRUCache[tuple[str, str], Index, ...] = LRUCache(
            get_missing_fn=self._build_checkpoint_cache_entries,
            max_size=1000,
            name="CheckpointCache",
            size_fn=lambda k, v: 1,
        )
        # embeddings cache: entry per blob, blob embeddings = ~10KB, repo = ~50k blobs, 2GB usage
        self.embeddings_cache: LRUCache[tuple[str, bytes], Embeddings, ...] = LRUCache(
            get_missing_fn=self._build_embeddings_cache_entries,
            max_size=2_000_000,
            name="EmbeddingsCache",
            size_fn=lambda k, v: 1,
        )
        # chunk cache: entry per chunk, chunk content = ~1KB, repo = ~500k chunks, 2GB usage
        self.chunk_cache: LRUCache[tuple[str, bytes, int], bytes, ...] = LRUCache(
            get_missing_fn=self._build_chunk_cache_entries,
            max_size=2_000_000,
            name="ChunkCache",
            size_fn=lambda k, v: 1,
        )

        checkpoint_cache_metrics.setup_listeners(self.checkpoint_cache)
        embeddings_cache_metrics.setup_listeners(self.embeddings_cache)
        chunk_cache_metrics.setup_listeners(self.chunk_cache)

    def _build_checkpoint_cache_entries(
        self, keys: typing.Iterable[tuple[str, str]], request_context: RequestContext
    ) -> list[Index]:
        assert all(checkpoint_id for _, checkpoint_id in keys), "No checkpoint id"
        return [
            self._build_chunked_index(
                checkpoint_id, transformation_key, request_context
            )
            for transformation_key, checkpoint_id in keys
            if checkpoint_id
        ]

    def _build_embeddings_cache_entries(
        self, keys: typing.Iterable[tuple[str, bytes]], request_context: RequestContext
    ) -> list[Embeddings | None]:
        load_params = [
            (
                blob_name,
                ContentKey(
                    blob_name=decode_blob_name(blob_name),
                    transformation_key=transformation_key,
                    sub_key="embeddings.npy",
                ),
                ContentKey(blob_name=decode_blob_name(blob_name)),
            )
            for transformation_key, blob_name in keys
        ]
        load_params.sort(key=lambda x: x[0])

        result: list[Embeddings | None] = []
        for batch in batched(load_params, 256):
            for blob_name, embeddings_key, _ in batch:
                logging.info(
                    "Load blob %s for %s",
                    decode_blob_name(blob_name)[:16],
                    embeddings_key.transformation_key,
                )
            try:
                content_it = self.content_manager_client.batch_download_all(
                    [content_key for _, content_key, _ in batch], request_context
                )
                info_it = self.content_manager_client.batch_get_info(
                    [info_key for _, _, info_key in batch], request_context
                )
                for (blob_name, _, _), content, info in zip(batch, content_it, info_it):
                    if not content:
                        logging.info(
                            "Not found for %s", decode_blob_name(blob_name)[:16]
                        )
                        result.append(None)
                    else:
                        embeddings = numpy.load(io.BytesIO(content[0]))
                        result.append(
                            Embeddings(
                                blob_name=blob_name,
                                metadata={
                                    m.key: m.value for m in info.blob_info.metadata
                                },
                                embeddings=embeddings,
                            )
                        )
            except grpc.RpcError as ex:
                logging.error("Embeddings cache download failed: %s" % ex)
                result.extend([None] * len(batch))
                continue

        return result

    def _build_chunk_cache_entries(
        self,
        keys: typing.Iterable[tuple[str, bytes, int]],
        request_context: RequestContext,
    ) -> list[bytes | None]:
        result = []
        for transformation_key, blob_name, chunk_index in keys:
            logging.info(
                "Download %s/%s/%s",
                decode_blob_name(blob_name)[:16],
                transformation_key,
                chunk_index,
            )
            content, _ = self.content_manager_client.download_all(
                decode_blob_name(blob_name),
                request_context,
                transformation_key=transformation_key,
                sub_key=f"chunk-{chunk_index}.pb",
            )
            result.append(content)
        return result

    def _build_chunked_index(
        self,
        checkpoint_id: str,
        transformation_key: str,
        request_context: RequestContext,
    ) -> Index:
        """Builds a chunked index for the given checkpoint id.

        The basic idea is that we chunk the checkpoint into a list of sub-indexes.
        The sub-indexes are build using the directory tree structure. The goal is that
        the sub-indexes can be shared between checkpoints. Performance-wise it would
        be optimal to build a single-index for each checkpoint, but that wouldn't be memory efficient.
        The goal is to have sub-indexes that are shared between checkpoints.

        Each chunk index is created as a ScannIndex. Scann is a high-performance vector search library.

        The implementation is a merge of scann indexes for each chunk.
        """
        logging.info("Build checkpoint index for checkpoint %s", checkpoint_id)
        assert checkpoint_id, "No checkpoint id"

        # get all the blob names in the checkpoint
        resp = self.content_manager_client.get_all_blobs_from_checkpoint(
            checkpoint_id, request_context
        )
        if not resp:
            logging.warning("Checkpoint not found: %s", checkpoint_id)
            raise SearchException(grpc.StatusCode.NOT_FOUND, "Checkpoint not found")

        # get the embeddings for the blob names
        # this currently takes a very long time
        embeddings, missing = self._get_embeddings(
            resp, transformation_key, request_context
        )
        logging.info(
            "Found embeddings for %s blobs, missing %s", len(embeddings), len(missing)
        )
        if missing and not self.config.allow_missing:
            # we shouldn't return an index here, as it would be incomplete. For this prototype, it is
            # better to reject the request.
            logging.warning("Missing embeddings for %s blobs", missing)
            raise SearchException(grpc.StatusCode.NOT_FOUND, "Missing embeddings")

        i = self.builder.build_index(transformation_key, embeddings, checkpoint_id)

        logging.info(
            "Build checkpoint index %s for %s: %s %s ",
            checkpoint_id,
            transformation_key,
            i,
            len(missing),
        )
        return i

    def _build_numpy_index(self, blob_names, transformation_key, request_context):
        """Builds a numpy index for the given blob names."""
        embeddings, missing = self._get_embeddings(
            blob_names, transformation_key, request_context
        )
        if missing:
            logging.warning("Missing embeddings for %s blobs", missing)
            if not self.config.allow_missing:
                raise SearchException(grpc.StatusCode.NOT_FOUND, "Missing embeddings")
        logging.info(
            "Build scattered index %s for %s: %s %s ",
            transformation_key,
            [decode_blob_name(e.blob_name)[:16] for e in embeddings],
            len(embeddings),
            len(missing),
        )
        i = NumpyIndex(embeddings)
        return i

    def _resolve_index(
        self,
        blobs: typing.Sequence[blob_names_pb2.Blobs],
        transformation_key: str,
        request_context: RequestContext,
    ) -> Index:
        """Resolve the blobs into a list of blob names.

        The implementation is a simple merge of the results from the inner indexes.
        """
        result: list[Index] = []
        for blob in blobs:
            # if there is no baseline checkpoint, we can just build a numpy index
            if not blob.baseline_checkpoint_id:
                result.append(
                    self._build_numpy_index(
                        blob.added, transformation_key, request_context
                    )
                )
                continue

            # check if we have already built an index for this checkpoint id
            checkpoint_cache_result = self.checkpoint_cache.get(
                [(transformation_key, blob.baseline_checkpoint_id)], request_context
            )
            # Raises a StopIteration error if no result is found, which is fine for now
            checkpoint_index = next(r for r in checkpoint_cache_result if r is not None)
            added_blobs_histogram.labels(
                transformation_key,
                request_context.request_source,
            ).observe(len(blob.added))
            deleted_blobs_histogram.labels(
                transformation_key,
                request_context.request_source,
            ).observe(len(blob.deleted))

            if blob.added:
                # build a numpy index for the added blobs
                added_index = self._build_numpy_index(
                    blob.added, transformation_key, request_context
                )
                index = MergeIndex([checkpoint_index, added_index])
            else:
                index = checkpoint_index

            if blob.deleted:
                index = RemovedIndexWrapper(index, set(blob.deleted))
            result.append(index)
        logging.info("Resolved blobs to %s indexes", len(result))
        return MergeIndex(result)

    def _get_chunk(
        self,
        blob_name: bytes,
        chunk_index: int,
        transformation_key: str,
        request_context: RequestContext,
    ) -> bytes:
        """Get the chunk for the given blob name and chunk index.

        The implementation is a simple cache.

        Args
            blob_name: the blob name
            chunk_index: the chunk index
            transformation_key: the transformation key
            request_context: the request context

        Returns
            the chunk as bytes
        """
        cache_result = self.chunk_cache.get(
            [(transformation_key, blob_name, chunk_index)], request_context
        )
        return next(r for r in cache_result if r is not None)

    def _get_embeddings(
        self,
        blob_names: typing.Sequence[bytes],
        transformation_key: str,
        request_context: RequestContext,
    ) -> tuple[list[Embeddings], list[bytes]]:
        """Get the embeddings for the given blob names.

        The implementation is a simple cache.

        Args
            blob_names: the blob names
            transformation_key: the transformation key
            request_context: the request context

        Returns
            a tuple of embeddings and missing blob names
        """
        result = []
        missing = []
        entries = self.embeddings_cache.get(
            [(transformation_key, blob_name) for blob_name in blob_names],
            request_context,
        )
        for blob_name, e in zip(blob_names, entries):
            if e is None:
                missing.append(blob_name)
            else:
                result.append(e)

        return result, missing

    def SearchChunks(
        self,
        request: embeddings_search_pb2.SearchChunksRequest,
        context: grpc.ServicerContext,
    ) -> typing.Generator[embeddings_search_pb2.SearchChunksResponse, None, None]:
        """Performs a search on the given blobs.

        Args
            request: the request
            context: the grpc context

        Returns
            a generator of SearchChunksResponse
        """
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            try:
                yield from self._search_chunks(request, request_context, auth_info)
            except grpc.RpcError as ex:
                if ex.code() != grpc.StatusCode.RESOURCE_EXHAUSTED:  # pylint: disable=no-member # type: ignore
                    logging.error("SearchChunks failed: %s", ex)
                    logging.exception(ex)
                context.abort(
                    code=ex.code(),  # pylint: disable=no-member # type: ignore
                    details=ex.details(),  # pylint: disable=no-member # type: ignore
                )
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logging.error("SearchChunks failed: %s", ex)
                logging.exception(ex)
                raise

    def _search_chunks(
        self,
        request: embeddings_search_pb2.SearchChunksRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> typing.Generator[embeddings_search_pb2.SearchChunksResponse, None, None]:
        start_time = time.time()
        logging.info(
            "SearchChunks(transformation_key=%s, checkpoint_id=%s)",
            request.transformation_key,
            request.blobs[0].baseline_checkpoint_id,
        )

        if (
            self.config.transformation_key_filters
            and request.transformation_key in self.config.transformation_key_filters
        ):
            raise SearchException(
                grpc.StatusCode.UNIMPLEMENTED, "Transformation key not allowed"
            )

        if not self.lock.acquire(timeout=10.0):
            # if we can't acquire the lock, we reject the request
            logging.info("Rejected request")
            raise SearchException(grpc.StatusCode.RESOURCE_EXHAUSTED, "Locked")
        # the implementation is entirely single-threaded
        try:
            search_start_time = time.time()
            index = self._resolve_index(
                request.blobs, request.transformation_key, request_context
            )
            logging.info("Found index %s", index)
            query = tensor.to_numpy(request.query)

            result = index.search(query, request.num_results, set())
            search_end_time = time.time()
            logging.info(
                "SearchChunks search took %s ms",
                (search_end_time - search_start_time) * 1000,
            )
            for r in result:
                logging.info("Result %s", r)
                c = self._get_chunk(
                    encode_blob_name(r.blob_name),
                    r.chunk_index,
                    request.transformation_key,
                    request_context,
                )
                # Convert numpy array to Python float for protobuf compatibility
                score = float(r.score) if hasattr(r.score, "item") else r.score
                chunk = embeddings_search_pb2.RetrievalChunk(
                    content=c,
                    score=score,
                    blob_name=r.blob_name,
                    chunk_index=r.chunk_index,
                )
                yield embeddings_search_pb2.SearchChunksResponse(chunk=chunk)
        finally:
            self.lock.release()
        end_time = time.time()
        logging.info("SearchChunks took %s ms", (end_time - start_time) * 1000)

    def FindMissing(
        self,
        request: embeddings_search_pb2.FindMissingRequest,
        context: grpc.ServicerContext,
    ) -> embeddings_search_pb2.FindMissingResponse:
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging():
            logging.info("FindMissing")
            try:
                # yeah, we don't do anything here.
                return embeddings_search_pb2.FindMissingResponse()
            except grpc.RpcError as ex:
                logging.error("FindMissing failed: %s", ex)
                logging.exception(ex)
                context.abort(
                    code=ex.code(),  # pylint: disable=no-member # type: ignore
                    details=ex.details(),  # pylint: disable=no-member # type: ignore
                )
                return embeddings_search_pb2.FindMissingResponse()
            except Exception as ex:  # pylint: disable=broad-exception-caught
                logging.error("FindMissing failed: %s", ex)
                logging.exception(ex)
                raise


def _serve(config: Config, shutdown_event: threading.Event):
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    namespace = os.environ["POD_NAMESPACE"]
    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)
    server = grpc.server(
        ThreadPoolExecutor(max_workers=32),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            auth_interceptor,
        ],
    )

    # Reply to health check RPCs
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    content_manager_client = get_content_manager_client(config)

    embeddings_search_pb2_grpc.add_EmbeddingsSearchServicer_to_server(
        EmbeddingsSearchServicer(config, content_manager_client),
        server,
    )
    service_names = (
        embeddings_search_pb2.DESCRIPTOR.services_by_name["EmbeddingsSearch"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)
    if server_credentials:
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def main():
    """Run the server."""

    # Set up the signal handler
    # This will catch SIGTERM and SIGINT and exit gracefully
    standard_handler = GracefulSignalHandler()

    # Set up the logging
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    prometheus_client.start_http_server(9090)

    _serve(config, standard_handler.get_shutdown_event())


if __name__ == "__main__":
    main()

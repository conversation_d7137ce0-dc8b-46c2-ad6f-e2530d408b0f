local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
// the function that creates the deployment
function(env, namespace, cloud, namespace_config)
  local appName = 'embeddings-search-scann';

  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  // create dynamic feature flag config
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
  // in the same namespace
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName),
                                              volumeName='certs');

  // configuration that will be passed to the server as a JSON file
  local config = {
    port: 50051,
    content_manager_endpoint: 'content-manager-svc:50051',
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    auth_config: {
      token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    },
    threads: 8,
    mode: 'minhash',
    allowed_use_numpy: false,
    scann_exact: false,
    share: true,
    single_group: true,
    transformation_key_filters: ['dr-methanol-0416-4-1024char'],
    allow_missing: if env == 'DEV' then true else false,
  } + if !mtls then {} else {
    server_mtls: serverCert.config,
    client_mtls: clientCert.config,
    central_client_mtls: centralClientCert.config,
  };

  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix='ess-',
  );
  // creates a service for the pod
  // a service is needed to expose the pod to the outside world
  local services = grpcLib.grpcService(appName=appName, namespace=namespace);
  local container = {
    name: appName,
    // the target is the bazel target that builds the docker image
    target: {
      name: '//services/embeddings_search_host/scann_server:image',
      dst: 'embeddings-search-scann',
    },
    // ports that the pod exposes
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      // the dynamic feature flag config is mounted into the pod
      dynamicFeatureFlags.volumeMountDef,
      // the client certificate is mounted into the pod
      centralClientCert.volumeMountDef,
      // the server certificate is mounted into the pod
      serverCert.volumeMountDef,
      // the client certificate is mounted into the pod
      clientCert.volumeMountDef,
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // the liveness check is used to determine if the pod is alive
    livenessProbe: grpcLib.grpcHealthCheck(appName + '-svc', tls=mtls, serverCerts=serverCert.volumeMountDef.mountPath) + {
      periodSeconds: 30,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 7,  // Probably overprovisioned, but it looks like the scann library likes to have the extra threads avaiable
        memory: '26Gi',
      },
    },
  };
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    priorityClassName: cloudInfo.envToPriorityClass(env),
    containers: [
      container,
    ],
    // the volumes are mounted into the pod
    volumes: [
      configMap.podVolumeDef,
      // the dynamic feature flag config is mounted into the pod
      dynamicFeatureFlags.podVolumeDef,
      // the client certificate is mounted into the pod
      centralClientCert.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCert.podVolumeDef,
      // the client certificate is mounted into the pod
      clientCert.podVolumeDef,
    ],
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local cpu = 'premiumCpu';
  local tolerations = nodeLib.tolerations(resource=cpu, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=cpu, env=env, cloud=cloud, appName=appName);
  // the deployment is the kubernetes object that manages the pod
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 60,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        // the strategy is used to determine how the deployment is rolled out
        type: 'RollingUpdate',
        rollingUpdate: {
          // the maximum number of pods that can be created above the desired number of pods
          maxSurge: 1,
          // the maximum number of pods that can be unavailable at the same time
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    dynamicFeatureFlags.k8s_objects,
    deployment,
    services,
  ])

"""Server for the route guide service."""

from collections import defaultdict
import hashlib
import itertools
import logging
import pathlib
import random
import time
import typing
from dataclasses import dataclass, field
from functools import cached_property

import grpc
import numpy
import scann
import structlog

from base.blob_names.python.blob_names import decode_blob_name
from base.caching.lru_cache import LRUCache
from base.caching.cache_metrics import CacheMetrics


log = structlog.get_logger()


chunk_index_cache_metrics = CacheMetrics(
    prefix="au_scann_chunk_index_cache", name="chunk index"
)

T = typing.TypeVar("T")


def _chunked_iterable(
    iterable: typing.Iterable[T], size: int
) -> typing.Generator[list[T], None, None]:
    it = iter(iterable)
    while True:
        chunk = list(itertools.islice(it, size))
        if not chunk:
            break
        yield chunk


class SearchException(grpc.RpcError):
    """Exception thrown during search processing.

    Note that subclasses of grpc.RpcError are expected to have code() and details() methods even
    though RpcError itself does not."""

    def __init__(self, status_code: grpc.StatusCode, msg: str = ""):
        self.status_code = status_code
        self.msg = msg

    def code(self) -> grpc.StatusCode:
        return self.status_code

    def details(self) -> str:
        return self.msg

    def __str__(self) -> str:
        return f"CompletionException({self.status_code}: {self.msg})"


@dataclass
class Embeddings:
    """Embeddings for a single blob."""

    # the blob name of the embeddings
    blob_name: bytes

    # the metadata for the embeddings
    # here this means the metadata of the Row blob. Aka it usually has the path name in it
    metadata: dict[str, str]

    # the embeddings as numpy array
    embeddings: numpy.ndarray

    # the path of the blob
    @cached_property
    def path(self) -> pathlib.Path:
        m = self.metadata.get("path")
        if m:
            return pathlib.Path(m)
        return pathlib.Path(decode_blob_name(self.blob_name))


@dataclass
class DirectoryEmbeddings:
    """Embeddings for a directory."""

    # the path of the directory
    path: pathlib.Path

    # the embeddings for the directory
    embeddings: list[Embeddings]

    # the children of the directory
    children: list["DirectoryEmbeddings"] = field(default_factory=list)

    def visit(self, f):
        """Visits the directory tree."""
        children_values = []
        for c in self.children:
            v = c.visit(f)
            children_values.append((c, v))
        return f(self, children_values)


def cluster_dir_embeddings(
    root: DirectoryEmbeddings,
) -> list[tuple[pathlib.Path, list[Embeddings]]]:
    """Clusters the directory embeddings into chunks.

    It takes a directory tree and returns a list of chunks.

    Currently, it does a buttom up approach of combining directory tree.
    If a directory subtree is large enough, it will be combined into a single chunk.
    Otherwise, it will be merged into the parent directory's chunk.

    This is a VERY simple heuristic.

    Each chunk is a tuple of the path and a list of embeddings.
    We will build an index for each chunk.
    """
    result: list[tuple[pathlib.Path, list[Embeddings]]] = []

    def v(
        d: DirectoryEmbeddings,
        children_values: list[tuple[DirectoryEmbeddings, typing.Any]],
    ):
        chunks: list[Embeddings] = []
        for _, v in children_values:
            chunks.extend(v)
        chunks += [e for e in d.embeddings]
        chunk_count = sum(chunk.embeddings.shape[0] for chunk in chunks)
        if chunk_count > 1000 or d.path == pathlib.Path():
            result.append((d.path, chunks[:]))
            return []
        return chunks

    root.visit(v)
    return result


def combine_to_directory(
    embeddings: list[Embeddings],
) -> dict[pathlib.Path, DirectoryEmbeddings]:
    """Combines embeddings into a directory tree.

    It takes a list of embeddings and returns a directory tree.
    The directory tree is a dictionary of path to DirectoryEmbeddings.
    Each DirectoryEmbeddings has a list of embeddings and a list of children.
    """
    result = {}
    for e in sorted(embeddings, key=lambda x: x.path):
        p = e.path.parent
        if p not in result:
            result[p] = DirectoryEmbeddings(p, [], [])
            last_p = p
            pp = p.parent
            while last_p != pp:
                if pp not in result:
                    result[pp] = DirectoryEmbeddings(pp, [], [])
                if result[last_p] not in result[pp].children:
                    result[pp].children.append(result[last_p])
                last_p = pp
                pp = pp.parent
        result[p].embeddings.append(e)
    return result


@dataclass
class RetrievalChunk:
    """A retrieval chunk.

    This is a dataclass that holds the result of a search.
    """

    # the score of the chunk
    score: float

    # the blob name of the chunk
    blob_name: str

    # the index of the chunk in the blob
    chunk_index: int


class Index(typing.Protocol):
    """An index for embeddings."""

    def search(
        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]
    ) -> typing.Sequence[RetrievalChunk]:
        """Performs a query on the index."""
        raise NotImplementedError()


class NumpyIndex(Index):
    """A simple numpy based index."""

    def __init__(self, embeddings: list[Embeddings]):
        """Constructs a new numpy index."""
        for e in embeddings:
            logging.info(
                "NumpyIndex: %s: %s",
                decode_blob_name(e.blob_name)[:16],
                e.embeddings.shape,
            )
        self.embeddings = [e for e in embeddings if e.embeddings.shape[0] > 0]

    def search(
        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]
    ) -> typing.Sequence[RetrievalChunk]:
        """Performs a query on the index.

        The implementation is a simple dot product using numpy of every embedding.
        That is followed by sorting and returning the top-32 results.

        Args
            query: the query as a numpy array

        Returns
            a list of RetrievalChunk
        """
        logging.info(
            "Searching index %s for %s results with %s blobs removed, query %s %s",
            self,
            num_results,
            len(blobs_removed),
            query.shape,
            query.dtype,
        )
        result = []
        for e in self.embeddings:
            if e.blob_name in blobs_removed:
                logging.debug(
                    "Skipping removed blob %s", decode_blob_name(e.blob_name)[:16]
                )
                continue
            ip = numpy.dot(e.embeddings, numpy.transpose(query))
            logging.debug(
                "NumpyIndex: %s results for %s, max %s",
                len(ip),
                decode_blob_name(e.blob_name)[:16],
                max(ip),
            )
            for i, ip2 in enumerate(ip):
                rc = RetrievalChunk(
                    score=ip2,
                    blob_name=decode_blob_name(e.blob_name),
                    chunk_index=i,
                )
                result.append(rc)
        result.sort(key=lambda e: e.score, reverse=True)
        return result[:num_results]

    def __str__(self):
        s = [decode_blob_name(e.blob_name)[:16] for e in self.embeddings]
        return f"NumpyIndex({s})"

    def __repr__(self):
        s = [decode_blob_name(e.blob_name)[:16] for e in self.embeddings]
        return f"NumpyIndex({s})"


class ScannIndex(Index):
    """A scann based index.

    A scann index is an index that uses the scann library to perform a search.
    The scann library is a high-performance library for nearest neighbor search.
    See https://github.com/google-research/google-research/tree/master/scann for more details.

    The search is not exact. It is an approximate nearest neighbor search.
    """

    def __init__(
        self,
        name: str,
        embeddings: list[Embeddings],
        exact: bool,
        threads: int,
        num_results: int,
        needs_all: bool,
    ):
        """Constructs a new scann index."""
        for e in embeddings:
            logging.debug(
                "ScannIndex: %s: %s",
                decode_blob_name(e.blob_name)[:16],
                e.embeddings.shape,
            )

        self.name = name
        self.num_results = num_results
        self.needs_all = needs_all

        # filter out empty embeddings
        self.embeddings = [e for e in embeddings if e.embeddings.shape[0] > 0]
        self.embeddings_count = sum(e.embeddings.shape[0] for e in self.embeddings)

        self.blob_names = set([e.blob_name for e in self.embeddings])

        if not self.embeddings:
            self.counts = []
            self.indexes = None
            self.searcher = None
            logging.warning("Built empty scann index %s", self)
            return

        # n is the concatenated embeddings
        n = numpy.concatenate([e.embeddings for e in self.embeddings])

        # counts is the index of the embeddings at the different indexes of n
        # self.embeddings[counts[x]].blob_name is the blob name of the xth embedding
        counts = numpy.concatenate(  # type: ignore
            [[i] * len(e.embeddings) for i, e in enumerate(self.embeddings)]  # type: ignore
        )
        self.counts = counts

        # indexes is the index of the chunks at the different indexes of n
        # indexes[x] is the chunk index of the xth embedding
        indexes = numpy.concatenate(
            [list(range(len(e.embeddings))) for e in self.embeddings]
        )
        self.indexes = indexes

        # build the scann searcher
        # the configuraiton means that we use AH (anisotropic hashing) with 8 bits and 256 reordering.
        # reordering here means that it finds the 256 highest product quantized values and then
        # does a second pass to take the full dot product of those and pick the top 32.
        if exact:
            self.searcher = (
                scann.scann_ops_pybind.builder(n, num_results, "dot_product")
                .score_brute_force()
                .build()  # type: ignore
            )
            reorder = 0
        else:
            reorder = max(num_results, n.shape[0] // num_results) + 1000
            self.searcher = (
                scann.scann_ops_pybind.builder(n, num_results, "dot_product")
                .score_ah(8, anisotropic_quantization_threshold=0.2)
                .set_n_training_threads(threads)  # type: ignore
                .reorder(reorder)  # type: ignore
                .build()
            )
        logging.info(
            "Built scann index %s %s, reorder %s, count %s",
            self,
            n.shape,
            reorder,
            self.embeddings_count,
        )

    def __str__(self):
        return f"ScannIndex({self.name}, {[decode_blob_name(b.blob_name)[:16] for b in self.embeddings]})"

    def __repr__(self):
        return f"ScannIndex({self.name},  {[decode_blob_name(b.blob_name)[:16] for b in self.embeddings]})"

    def search(
        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]
    ) -> typing.Sequence[RetrievalChunk]:
        """Performs a query on the index.

        The implementation is a scann searcher.

        Args
            query: the query as a numpy array

        Returns
            a list of RetrievalChunk
        """
        if not self.searcher:
            logging.warning("No searcher for %s", self)
            return []
        start_time = time.time()
        relevant_removed_blobs = blobs_removed & self.blob_names
        for iteration in range(3):
            logging.info(
                "Searching index %s for %s results with %s/%s blobs removed, query %s %s, num_results %s",
                self,
                min(num_results, self.embeddings_count),
                len(relevant_removed_blobs),
                len(self.blob_names),
                query.shape,
                query.dtype,
                num_results,
            )
            neighbors, distances = self.searcher.search_batched(
                query, min(num_results, self.embeddings_count)
            )
            result = []
            for n, d in zip(neighbors[0], distances[0]):
                if numpy.isnan(d):
                    logging.debug(
                        "Early returning since nan indicates we're out of results"
                    )
                    break
                blob_name = self.embeddings[self.counts[n]].blob_name
                if blob_name in relevant_removed_blobs:
                    logging.debug(
                        "Skipping removed blob %s", decode_blob_name(blob_name)[:16]
                    )
                    continue
                index: int = self.indexes[n]  # type: ignore
                rc = RetrievalChunk(
                    score=d,
                    blob_name=decode_blob_name(blob_name),
                    chunk_index=index,
                )
                result.append(rc)
            # Very fast and loose rule
            result_ratio = 1.0 if self.needs_all else 0.9
            count_ratio = 1.0 if self.needs_all else 0.5
            if (
                len(result) < num_results * result_ratio
                and len(result) < self.embeddings_count * count_ratio
                and iteration < 2
            ):
                logging.info(
                    "Not enough results, retrying, current results %s", len(result)
                )
                num_results *= 2
                continue

            end_time = time.time()
            logging.info(
                "Searched index %s in %s ms", self, (end_time - start_time) * 1000
            )
            for i, r in enumerate(result):
                logging.debug(
                    "Result %s: %s.%s with score %s",
                    i,
                    r.blob_name[:16],
                    r.chunk_index,
                    r.score,
                )
            return result

        assert False, "Should not reach here"


class MergeIndex(Index):
    """Merges multiple indexes into one."""

    def __init__(self, inner: typing.Sequence[Index]):
        """Constructs a new merge index."""
        self.inner = inner

    def search(
        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]
    ) -> typing.Sequence[RetrievalChunk]:
        """Performs a query on the index.

        The implementation is a simple merge of the results from the inner indexes.
        """
        logging.info(
            "Searching MergeIndex of %s indexes for %s results with %s blobs removed, query %s %s",
            len(self.inner),
            num_results,
            len(blobs_removed),
            query.shape,
            query.dtype,
        )
        start_time = time.time()

        result = []
        for i in self.inner:
            result.extend(i.search(query, num_results, blobs_removed))
        result.sort(key=lambda e: e.score, reverse=True)
        end_time = time.time()
        logging.info(
            "Searched MergeIndex of %s indexes in %s ms",
            len(self.inner),
            (end_time - start_time) * 1000,
        )
        for i, r in enumerate(result[:num_results]):
            logging.debug(
                "Result %s: %s.%s with score %s",
                i,
                r.blob_name[:16],
                r.chunk_index,
                r.score,
            )
        return result[:num_results]

    def __str__(self):
        return f"MergeIndex({self.inner})"

    def __repr__(self):
        return f"MergeIndex({self.inner})"


def embeddings_hash(embeddings: typing.Sequence[Embeddings]) -> bytes:
    """Computes a hash of the embeddings."""
    blob_names = [e.blob_name for e in embeddings]
    blob_names.sort()
    h = hashlib.sha256()
    h.update(b" ".join(blob_names))
    return h.digest()


def generate_hash_functions(
    k, max_value
) -> typing.Sequence[typing.Callable[[int], int]]:
    """Generates a list of hash functions."""
    hash_funcs = []
    p = next_prime(max_value)
    for _ in range(k):
        a = random.randint(1, max_value)
        b = random.randint(0, max_value)
        hash_funcs.append(lambda x, a=a, b=b, p=p: (a * x + b) % p)
    return hash_funcs


def next_prime(n):
    # Simple function to find the next prime >= n
    def is_prime(num):
        if num < 2:
            return False
        for i in range(2, int(num**0.5) + 1):
            if num % i == 0:
                return False
        return True

    while not is_prime(n):
        n += 1
    return n


num_hashes = 32

hash_functions = generate_hash_functions(num_hashes, max_value=1000)


def divide_into_bands(signature, num_bands):
    band_size = len(signature) // num_bands
    # tuple so that it is hashable
    bands = [
        tuple(signature[i * band_size : (i + 1) * band_size]) for i in range(num_bands)
    ]
    return bands


def compute_minhash_signature(
    set_elements: typing.Sequence[Embeddings],
    hash_funcs: typing.Sequence[typing.Callable[[int], int]],
) -> typing.Sequence[int]:
    """Computes the minhash signature of a set of embeddings."""
    signature = []
    for hash_func in hash_funcs:
        min_hash = float("inf")
        for element in set_elements:
            hashed = hash_func(hash(element.blob_name))
            min_hash = min(min_hash, hashed)
        signature.append(min_hash)
    return signature


class LshIndex:
    """Index for locality sensitive hashing."""

    def __init__(self):
        """Constructs a new LshIndex."""
        self.index: dict[tuple[int, int], set[str]] = {}

    def add(self, set_id: str, signature: typing.Sequence[int], num_bands: int):
        logging.info(
            "Adding %s to index: signature %s, num_bands %s",
            set_id,
            signature,
            num_bands,
        )
        bands = divide_into_bands(signature, num_bands)
        for band_id, band in enumerate(bands):
            hash_value = hash(band)
            self.index.setdefault((band_id, hash_value), set()).add(set_id)

    def find_candidates(
        self, set_id: str, signature: typing.Sequence[int], num_bands: int
    ):
        logging.info(
            "Finding candidates for %s: signature %s, num_bands %s, index size %s",
            set_id,
            signature,
            num_bands,
            len(self.index),
        )
        candidates = set()
        bands = divide_into_bands(signature, num_bands)
        for band_id, band in enumerate(bands):
            hash_value = hash(band)
            bucket = self.index.get((band_id, hash_value), set())
            candidates.update(bucket)
        candidates.discard(set_id)  # Remove the set itself from candidates
        logging.info("Found candidates: %s", candidates)
        return candidates


class IndexDelta:
    """Represents a delta between two indexes."""

    def __init__(self, added: set[bytes], removed: set[bytes], overlap: int):
        """Constructs a new index delta."""
        self.added = added
        self.removed = removed
        self.overlap = overlap

    def __str__(self):
        return f"IndexDelta(added={[decode_blob_name(e)[:16] for e in self.added]}, removed={[decode_blob_name(e)[:16] for e in self.removed]}, overlap={self.overlap})"

    def __repr__(self):
        return f"IndexDelta(added={[decode_blob_name(e)[:16] for e in self.added]}, removed={[decode_blob_name(e)[:16] for e in self.removed]}, overlap={self.overlap})"


class LshChunkIndexEntry:
    def __init__(
        self,
        index: Index,
        signature: typing.Sequence[int],
        embedding_group: typing.Sequence[Embeddings],
        original_checkpoint_id: str,
    ):
        self.index = index
        self.signature = signature
        self.embedding_group = embedding_group
        self.blob_names = set([e.blob_name for e in embedding_group])
        self.original_checkpoint_id = original_checkpoint_id

    def __str__(self):
        return (
            f"LshChunkIndexEntry({self.embedding_group, self.original_checkpoint_id})"
        )

    def __repr__(self):
        return (
            f"LshChunkIndexEntry({self.embedding_group, self.original_checkpoint_id})"
        )

    def compute_delta(self, embedding_group: typing.Sequence[Embeddings]) -> IndexDelta:
        """Computes the overlap between the embedding group of this entry and the given embedding group."""
        overlap = 0
        # added = not in existing group, but in new group
        added = set([e.blob_name for e in embedding_group]) - set(
            [e.blob_name for e in self.embedding_group]
        )
        # removed = in existing group, but not in new group
        removed = set([e.blob_name for e in self.embedding_group]) - set(
            [e.blob_name for e in embedding_group]
        )
        for e in embedding_group:
            if e.blob_name in self.blob_names:
                overlap += 1
        return IndexDelta(added, removed, overlap)


class RemovedIndexWrapper(Index):
    """Wraps an index and removes the deleted embeddings."""

    def __init__(self, inner: Index, removed: set[bytes]):
        self.inner = inner
        self.removed = removed

    def search(
        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]
    ) -> typing.Sequence[RetrievalChunk]:
        """Performs a query on the index.

        The implementation is a simple wrapper that filters out the removed embeddings.
        A real implementation would need to be more efficient.

        """
        if blobs_removed:
            b = blobs_removed | self.removed
        else:
            b = self.removed

        return self.inner.search(query=query, num_results=num_results, blobs_removed=b)

    def __str__(self):
        return f"RemovedIndexWrapper({self.inner}, removed={[decode_blob_name(e)[:16] for e in self.removed]})"

    def __repr__(self):
        return f"RemovedIndexWrapper({self.inner}, removed={[decode_blob_name(e)[:16] for e in self.removed]})"


def build_embeddings_group(
    embeddings: list[Embeddings],
    single_group: bool,
) -> typing.Iterable[list[Embeddings]]:
    if single_group:
        yield embeddings
        return
    groups = defaultdict(list)
    for embedding in embeddings:
        byte_obj = embedding.blob_name
        groups[byte_obj[0]].append(embedding)
    return [sublist for sublist in groups.values() if len(sublist) > 0]


class IndexBuilder(typing.Protocol):
    def build_index(
        self, transformation_key: str, embeddings: list[Embeddings], checkpoint_id: str
    ) -> Index:
        raise NotImplementedError()


class TreeBuilder(IndexBuilder):
    def __init__(self, exact: bool, threads: int):
        self.exact = exact
        self.threads = threads
        # chunk index cache: entry per "directory chunk", repo = ~5k directory chunks
        self.chunk_index_cache: LRUCache[tuple[str, str, bytes], Index, ...] = LRUCache(
            get_missing_fn=self._build_chunk_index_cache_entries,
            max_size=10000,
            name="ChunkIndexCache",
            size_fn=lambda k, v: 1,
        )
        chunk_index_cache_metrics.setup_listeners(self.chunk_index_cache)

    def _build_chunk_index_cache_entries(
        self,
        keys: typing.Iterable[tuple[str, str, bytes]],
        embeddings: dict[str, list[Embeddings]],
    ) -> list[Index | None]:
        for transformation_key, path, hash in keys:
            logging.info(
                "Build chunk index for %s %s, hash %s",
                transformation_key,
                path,
                hash.hex(),
            )
        return [
            # Yes, only the path part of the key is used in constructing the actual index
            ScannIndex(path, embeddings[path], self.exact, self.threads, 32, False)
            for _, path, _ in keys
        ]

    def build_index(
        self, transformation_key: str, embeddings: list[Embeddings], checkpoint_id: str
    ) -> Index:
        # now we build a directory tree from the embeddings
        dirs = combine_to_directory(embeddings)
        root_dir = pathlib.Path()
        dir_chunks = dirs.get(root_dir)
        if not dir_chunks:
            logging.warning("No root directory found: %s", dirs)
            raise SearchException(grpc.StatusCode.INTERNAL, "No root directory found")
        chunks = cluster_dir_embeddings(dir_chunks)

        # now we build an index for each chunk
        logging.info(
            "Found chunks %s",
            [f"path={c[0]} hash={embeddings_hash(c[1])}" for c in chunks],
        )
        embeddings_groups = dict(
            [
                (f"{chunk_path}", chunk_embeddings)
                for chunk_path, chunk_embeddings in chunks
            ]
        )
        query = [
            (transformation_key, f"{chunk_path}", embeddings_hash(chunk_embeddings))
            for chunk_path, chunk_embeddings in chunks
        ]
        chunk_indexes = [
            r
            for r in self.chunk_index_cache.get(query, embeddings_groups)
            if r is not None
        ]
        i = MergeIndex(chunk_indexes)
        return i


class MinHashBuilder(IndexBuilder):
    def __init__(
        self,
        allowed_use_numpy: bool,
        exact: bool,
        threads: int,
        share: bool,
        single_group: bool,
    ):
        self.allowed_use_numpy = allowed_use_numpy
        self.exact = exact
        self.threads = threads
        self.lsh_indexes: defaultdict[str, LshIndex] = defaultdict(lambda: LshIndex())
        self.lsh_chunk_indexes: defaultdict[str, dict[str, LshChunkIndexEntry]] = (
            defaultdict(lambda: {})
        )
        self.share = share
        self.single_group = single_group

    def build_index(
        self, transformation_key: str, embeddings: list[Embeddings], checkpoint_id: str
    ) -> Index:
        chunk_indexes = []
        embeddings.sort(key=lambda e: e.blob_name)
        for embedding_group in build_embeddings_group(
            embeddings, single_group=self.single_group
        ):
            embedding_group_name = embeddings_hash(embedding_group)
            logging.info(
                "Build chunk index for %s, name %s",
                [decode_blob_name(e.blob_name)[:16] for e in embedding_group],
                embedding_group_name.hex(),
            )

            signature = compute_minhash_signature(embedding_group, hash_functions)

            candidates = self.lsh_indexes[transformation_key].find_candidates(
                embedding_group_name.hex(), signature, 10
            )
            best_overlap: typing.Tuple[IndexDelta, LshChunkIndexEntry] | None = None
            for candidate in candidates:
                candidate_index: LshChunkIndexEntry | None = self.lsh_chunk_indexes[
                    transformation_key
                ].get(candidate)
                if candidate_index:
                    delta = candidate_index.compute_delta(embedding_group)
                    logging.debug(
                        "Checking candidate index: overlap: %s", delta.overlap
                    )
                    if best_overlap is None or delta.overlap > best_overlap[0].overlap:
                        best_overlap = (delta, candidate_index)

            chunk_index: Index | None = None
            if best_overlap is not None:
                logging.info("Best overlap: %s", best_overlap[0])
                if best_overlap[0].overlap > len(embedding_group) * 0.9:
                    logging.info(
                        "checkpoint %s: Using existing index based on overlap: %s from checkpoint %s",
                        checkpoint_id,
                        best_overlap[0],
                        best_overlap[1].original_checkpoint_id,
                    )
                    shared_index = best_overlap[1].index
                    assert shared_index is not None
                    if best_overlap[0].removed:
                        shared_index = RemovedIndexWrapper(
                            shared_index,
                            best_overlap[0].removed,
                        )
                    if best_overlap[0].added:
                        added_embeddings = []
                        for a in best_overlap[0].added:
                            e = [e for e in embedding_group if e.blob_name == a]
                            if not e:
                                raise Exception("Added embedding not found")
                            added_embeddings.append(e[0])
                        shared_index = MergeIndex(
                            [
                                shared_index,
                                NumpyIndex(added_embeddings),
                            ]
                        )
                    if self.share:
                        chunk_index = shared_index
                    else:
                        logging.info("Not sharing index: %s", shared_index)

            if chunk_index is None:
                # we didn't find a good candidate, so we build a new index
                if self.allowed_use_numpy:
                    chunk_index = NumpyIndex(embedding_group)
                else:
                    chunk_index = ScannIndex(
                        embedding_group_name.hex(),
                        embedding_group,
                        self.exact,
                        self.threads,
                        32,
                        self.single_group,
                    )
                self.lsh_indexes[transformation_key].add(
                    embedding_group_name.hex(), signature, 10
                )
                self.lsh_chunk_indexes[transformation_key][
                    embedding_group_name.hex()
                ] = LshChunkIndexEntry(
                    chunk_index, signature, embedding_group, checkpoint_id
                )
            chunk_indexes.append(chunk_index)
        i = MergeIndex(chunk_indexes)
        return i


class FlatBuilder(IndexBuilder):
    def __init__(self, allowed_use_numpy: bool, exact: bool, threads: int):
        self.allowed_use_numpy = allowed_use_numpy
        self.exact = exact
        self.threads = threads

    def build_index(
        self, transformation_key: str, embeddings: list[Embeddings], checkpoint_id: str
    ) -> Index:
        if self.allowed_use_numpy:
            i = NumpyIndex(embeddings)
        else:
            i = ScannIndex(
                checkpoint_id, embeddings, self.exact, self.threads, 32, False
            )
        return i

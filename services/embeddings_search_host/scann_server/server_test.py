import pytest
import numpy as np
import io
from unittest.mock import MagicMock

from services.lib.request_context.request_context import Request<PERSON>ontext
from services.lib.grpc.auth.service_auth import AuthInfo
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.content_manager import content_manager_pb2
from services.embeddings_search_host.scann_server.server import (
    AuthConfig,
    EmbeddingsSearchServicer,
    Config,
)
from services.embeddings_search_host import embeddings_search_pb2
from base.blob_names import blob_names_pb2
from base.proto.tensor import tensor_pb2


@pytest.fixture
def servicer():
    # Create sample embeddings data
    sample_embeddings = np.random.rand(4, 80).astype(
        np.float16
    )  # 4 chunks, 80 dimensions

    # Save embeddings in numpy format
    buffer = io.BytesIO()
    np.save(buffer, sample_embeddings)
    embeddings_bytes = buffer.getvalue()

    mock_client = MagicMock(ContentManagerClient)
    mock_client.batch_download_all.return_value = [
        (
            embeddings_bytes,
            {
                "blob_name": "test-blob-1",
                "transformation_key": "test-key",
                "sub_key": "embeddings.npy",
            },
        ),
        (
            embeddings_bytes,
            {
                "blob_name": "test-blob-2",
                "transformation_key": "test-key",
                "sub_key": "embeddings.npy",
            },
        ),
    ]
    mock_client.batch_get_info.return_value = [
        content_manager_pb2.BatchBlobInfoResponse(
            blob_content_key=content_manager_pb2.BlobContentKey(
                blob_name="test-blob-1",
                transformation_key="test-key",
                sub_key="embeddings.npy",
            ),
            blob_info=content_manager_pb2.GetBlobInfoResponse(),
        ),
        content_manager_pb2.BatchBlobInfoResponse(
            blob_content_key=content_manager_pb2.BlobContentKey(
                blob_name="test-blob-2",
                transformation_key="test-key",
                sub_key="embeddings.npy",
            ),
            blob_info=content_manager_pb2.GetBlobInfoResponse(),
        ),
    ]
    mock_client.download_all.return_value = (b"foo", {})

    return EmbeddingsSearchServicer(
        config=Config(
            port=0,
            content_manager_endpoint="unused",
            auth_config=AuthConfig(token_exchange_endpoint="unused"),
            dynamic_feature_flags_endpoint=None,
            feature_flags_sdk_key_path=None,
        ),
        content_manager_client=mock_client,
    )


def test_search_chunks(servicer):
    # Arrange
    request = embeddings_search_pb2.SearchChunksRequest(
        query=tensor_pb2.Tensor(
            datatype="FP16",
            shape=[1, 80],
            contents=np.random.rand(1, 80).astype(np.float16).tobytes(),
        ),
        num_results=10,
        transformation_key="test-key",
        blobs=[
            blob_names_pb2.Blobs(
                baseline_checkpoint_id=None,
                added=[b"test-blob-1", b"test-blob-2"],
                deleted=[],
            )
        ],
        search_timeout_ms=1000,
    )

    # Act
    response_iterator = servicer._search_chunks(
        request,
        RequestContext.create(),
        AuthInfo(
            tenant_id="test-tenant-id",
            tenant_name="test-tenant",
            shard_namespace="test-shard",
            cloud="test-cloud",
        ),
    )

    # Assert
    responses = [r for r in response_iterator]
    assert len(responses) > 0
    for response in responses:
        assert isinstance(response, embeddings_search_pb2.SearchChunksResponse)

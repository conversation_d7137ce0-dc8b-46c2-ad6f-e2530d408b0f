package main

import (
	"context"

	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
)

// TenantLookup is the interface for looking up tenant names
type TenantLookup interface {
	GetTenantName(ctx context.Context, tenantID string) (string, error)
}

// tenantLookup is the implementation of TenantLookup
type tenantLookup struct {
	tenantCache tenantwatcherclient.TenantCache
}

func NewTenantLookup(
	tenantCache tenantwatcherclient.TenantCache,
) TenantLookup {
	return &tenantLookup{
		tenantCache: tenantCache,
	}
}

func (c *tenantLookup) GetTenantName(ctx context.Context, tenantID string) (string, error) {
	tenant, err := c.tenantCache.GetTenant(tenantID)
	if err != nil {
		return "", err
	}
	return tenant.Name, nil
}

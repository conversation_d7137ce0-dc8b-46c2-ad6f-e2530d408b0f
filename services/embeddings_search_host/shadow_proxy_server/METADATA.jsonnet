// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'embeddings-search-shadow-proxy',
      kubecfg: {
        target: '//services/embeddings_search_host/shadow_proxy_server:kubecfg',
        task: tenantNamespaces.dogfoodNamespaces,
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['dirk', 'luke'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

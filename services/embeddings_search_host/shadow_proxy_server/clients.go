package main

import (
	"context"
	"sync"

	client "github.com/augmentcode/augment/services/embeddings_search_host/client"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/credentials"
)

// ClientFactory is the interface for getting embeddings search clients
type ClientFactory interface {
	// Clients returned from here could expire at any time - do not expect to use forever
	GetEmbeddingsSearchClient(ctx context.Context, client ClientConfig) (client.EmbeddingsSearchClient, error)
}

// ClientFactoryImpl is the implementation of ClientFactory
type ClientFactoryImpl struct {
	creds   credentials.TransportCredentials
	mutex   sync.Mutex
	clients map[string]client.EmbeddingsSearchClient
}

func NewClientFactory(creds credentials.TransportCredentials) ClientFactory {
	return &ClientFactoryImpl{
		creds:   creds,
		clients: make(map[string]client.EmbeddingsSearchClient),
	}
}

func (c *ClientFactoryImpl) GetEmbeddingsSearchClient(ctx context.Context, clientConfig ClientConfig) (client.EmbeddingsSearchClient, error) {
	c.mutex.Lock()

	searchClient, ok := c.clients[clientConfig.Endpoint]
	if ok {
		c.mutex.Unlock()
		return searchClient, nil
	}

	c.mutex.Unlock()

	log.Ctx(ctx).Info().Msgf("Creating new client for %v", clientConfig.Endpoint)

	newClient, err := client.NewEmbeddingsSearchClient(clientConfig.Endpoint, c.creds)
	if err != nil {
		return nil, err
	}

	c.mutex.Lock()
	c.clients[clientConfig.Endpoint] = newClient
	c.mutex.Unlock()

	return newClient, nil
}

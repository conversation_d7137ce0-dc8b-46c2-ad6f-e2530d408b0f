package main

import (
	"context"
	"encoding/json"
	"sync"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/rs/zerolog/log"
)

// ClientConfig is the configuration for a single client
type ClientConfig struct {
	// the endpoint of the client
	Endpoint string `json:"endpoint"`
}

// PolicyConfig is the configuration for a policy
type PolicyConfig struct {
	// the primary client
	Primary ClientConfig `json:"primary"`

	// this is a pointer so that we can distinguish between a missing shadow and a shadow that is disabled
	Shadow *ClientConfig `json:"shadow,omitempty"`
}

// policy feature flag
var policyFeature *featureflags.StringFlag = featureflags.NewStringFlag("embeddings_search_shadow_proxy_policy", "")

// PolicyManager is the interface for getting the policy for a tenant
type PolicyManager interface {
	// GetPolicy returns the policy for a tenant
	GetPolicy(ctx context.Context, tenantID string) (PolicyConfig, error)
}

// policyConfigCache is a cache entry for a policy config
type policyConfigCache struct {
	str    string
	config PolicyConfig
}

// PolicyManagerImpl is the implementation of PolicyManager
type PolicyManagerImpl struct {
	tenantLookup  TenantLookup
	featureFlags  featureflags.FeatureFlagHandle
	defaultPolicy PolicyConfig

	mutex sync.Mutex
	// map from tenant id to policy config
	policyCache map[string]*policyConfigCache
}

func NewPolicyManager(tenantLookup TenantLookup, featureFlags featureflags.FeatureFlagHandle, defaultPolicy PolicyConfig) PolicyManager {
	return &PolicyManagerImpl{
		tenantLookup:  tenantLookup,
		featureFlags:  featureFlags,
		defaultPolicy: defaultPolicy,
		policyCache:   make(map[string]*policyConfigCache),
	}
}

func (pm *PolicyManagerImpl) getFeatureFlagHandle(ctx context.Context, tenantID string) (featureflags.FeatureFlagHandle, error) {
	tenantName, err := pm.tenantLookup.GetTenantName(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	return pm.featureFlags.BindContext("tenant_name", tenantName)
}

func (pm *PolicyManagerImpl) parsePolicy(policyStr string) (PolicyConfig, error) {
	if policyStr == "" {
		return pm.defaultPolicy, nil
	}
	var policy PolicyConfig
	err := json.Unmarshal([]byte(policyStr), &policy)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to parse policy %v", policyStr)
		return PolicyConfig{}, err
	}
	return policy, nil
}

func (pm *PolicyManagerImpl) GetPolicy(ctx context.Context, tenantID string) (PolicyConfig, error) {
	ff, err := pm.getFeatureFlagHandle(ctx, tenantID)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msgf("Failed to get feature flag handle for tenant %v", tenantID)
		return PolicyConfig{}, err
	}
	policyStr, _ := policyFeature.Get(ff)

	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	entry, ok := pm.policyCache[tenantID]
	if ok {
		if entry.str == policyStr {
			return entry.config, nil
		}
		// update
		newConfig, err := pm.parsePolicy(policyStr)
		if err != nil {
			return PolicyConfig{}, err
		}
		newEntry := &policyConfigCache{
			str:    policyStr,
			config: newConfig,
		}
		pm.policyCache[tenantID] = newEntry
	} else {
		// insert
		newConfig, err := pm.parsePolicy(policyStr)
		if err != nil {
			return PolicyConfig{}, err
		}
		newEntry := &policyConfigCache{
			str:    policyStr,
			config: newConfig,
		}
		pm.policyCache[tenantID] = newEntry
	}
	p := pm.policyCache[tenantID]
	log.Ctx(ctx).Info().Msgf("Policy for %v", p.config)
	return p.config, nil
}

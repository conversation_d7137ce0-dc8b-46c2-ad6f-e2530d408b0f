package main

import (
	"context"
	"errors"
	"sync"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/services/embeddings_search_host/client"
	proto "github.com/augmentcode/augment/services/embeddings_search_host/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type shadowProxyEmbeddingsSearchServer struct {
	clientFactory     ClientFactory
	policyManager     PolicyManager
	featureFlagHandle featureflags.FeatureFlagHandle
}

// Propagates the deadline, but produces an independent context that can outlive the parent context if needed
func (s *shadowProxyEmbeddingsSearchServer) wrapDeadline(ctx context.Context) (context.Context, context.CancelFunc) {
	deadline, ok := ctx.Deadline()
	cancel := func() {}
	newCtx := ctx
	if ok {
		newCtx, cancel = context.WithDeadline(context.Background(), deadline)
	}
	// Copy relevant values from parent context if needed
	return log.Ctx(ctx).WithContext(newCtx), cancel
}

func (s *shadowProxyEmbeddingsSearchServer) GetClients(ctx context.Context, tenantID string) (*client.EmbeddingsSearchClient, *client.EmbeddingsSearchClient, error) {
	policy, err := s.policyManager.GetPolicy(ctx, tenantID)
	if err != nil {
		return nil, nil, err
	}

	primary, err := s.clientFactory.GetEmbeddingsSearchClient(ctx, policy.Primary)
	if err != nil {
		return nil, nil, err
	}
	if policy.Shadow == nil {
		return &primary, nil, nil
	}
	shadow, err := s.clientFactory.GetEmbeddingsSearchClient(ctx, *policy.Shadow)
	if err != nil {
		return nil, nil, err
	}
	return &primary, &shadow, nil
}

func (s *shadowProxyEmbeddingsSearchServer) FindMissing(ctx context.Context, req *proto.FindMissingRequest) (*proto.FindMissingResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)
	claims, _ := auth.GetAugmentClaims(ctx)
	ctx = claims.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msg("FindMissing")

	primary, shadow, err := s.GetClients(ctx, claims.TenantID)
	if err != nil {
		return nil, err
	}

	var wg sync.WaitGroup
	primaryCtx, primaryCancel := s.wrapDeadline(ctx)

	defer primaryCancel()

	// Channel to receive primary response
	primaryCh := make(chan *proto.FindMissingResponse, 1)
	errorCh := make(chan error, 1)

	// Start primary call
	wg.Add(1)
	go func() {
		primaryCtx, span := tracer.Start(primaryCtx, "primary.FindMissing")
		defer span.End()
		defer wg.Done()
		resp, err := (*primary).FindMissing(primaryCtx, req, requestContext)
		if err != nil {
			errorCh <- err
			return
		}
		primaryCh <- resp
	}()

	if shadow != nil {
		// Start shadow call
		wg.Add(1)
		// the shadow call is not expected to return a response, so we don't need to create a channel
		// the shadow cancel is expected to outlive the primary call
		shadowCtx, shadowCancel := s.wrapDeadline(ctx)
		go func() {
			shadowCtx, span := tracer.Start(shadowCtx, "shadow.FindMissing")
			defer span.End()
			defer wg.Done()
			defer shadowCancel() // Cleanup shadowCtx when shadow call finishes
			_, err := (*shadow).FindMissing(shadowCtx, req, requestContext)
			if err != nil {
				log.Ctx(shadowCtx).Error().Err(err).Msg("Shadow FindMissing failed")
				return
			}
			log.Ctx(shadowCtx).Info().Msg("Shadow FindMissing succeeded")
		}()
	}

	// Wait for primary response
	var primaryResp *proto.FindMissingResponse
	select {
	case resp := <-primaryCh:
		log.Ctx(ctx).Info().Msgf("Primary FindMissing succeeded")
		primaryResp = resp
	case err := <-errorCh:
		log.Ctx(ctx).Error().Err(err).Msg("Primary FindMissing failed")
		return nil, err
	case <-ctx.Done():
		log.Ctx(ctx).Info().Msg("Primary FindMissing canceled")
		return nil, ctx.Err()
	}

	// Allow shadow to continue running until its own deadline
	go func() {
		wg.Wait()
		close(primaryCh)
		close(errorCh)
	}()

	return primaryResp, nil
}

type ResponseComparer struct {
	primaryCh chan *proto.RetrievalChunk
	shadowCh  chan *proto.RetrievalChunk

	primaryChunks []*proto.RetrievalChunk
	primaryDone   bool
	shadowChunks  []*proto.RetrievalChunk
	shadowDone    bool
}

func NewResponseComparer() (chan *proto.RetrievalChunk, chan *proto.RetrievalChunk, *ResponseComparer) {
	primaryCh := make(chan *proto.RetrievalChunk, 1)
	shadowCh := make(chan *proto.RetrievalChunk, 1)
	return primaryCh, shadowCh, &ResponseComparer{
		primaryCh: primaryCh,
		shadowCh:  shadowCh,
	}
}

func (c *ResponseComparer) compare(ctx context.Context) {
	log.Ctx(ctx).Info().Msgf("Primary SearchChunks returned %d chunks", len(c.primaryChunks))
	log.Ctx(ctx).Info().Msgf("Shadow SearchChunks returned %d chunks", len(c.shadowChunks))
	for i, primaryChunk := range c.primaryChunks {
		log.Ctx(ctx).Info().Msgf("Primary SearchChunks: count=%d blob_name=%s chunk_index=%d score=%f", i, primaryChunk.BlobName, primaryChunk.ChunkIndex, primaryChunk.Score)
	}
	for i, shadowChunk := range c.shadowChunks {
		log.Ctx(ctx).Info().Msgf("Shadow SearchChunks: count=%d blob_name=%s chunk_index=%d score=%f", i, shadowChunk.BlobName, shadowChunk.ChunkIndex, shadowChunk.Score)
	}

	overlap := 0
	for _, primaryChunk := range c.primaryChunks {
		for _, shadowChunk := range c.shadowChunks {
			if primaryChunk.BlobName == shadowChunk.BlobName && primaryChunk.ChunkIndex == shadowChunk.ChunkIndex {
				overlap++
			}
		}
	}
	if len(c.primaryChunks) != len(c.shadowChunks) {
		log.Ctx(ctx).Warn().Msgf("Shadow SearchChunks returned different number of chunks: %d vs %d", len(c.primaryChunks), len(c.shadowChunks))
	} else {
		log.Ctx(ctx).Info().Msgf("Overlap: %d / %d", overlap, len(c.primaryChunks))
	}
}

func (c *ResponseComparer) Run(ctx context.Context) {
	for {
		select {
		case resp, ok := <-c.primaryCh:
			if !ok {
				log.Ctx(ctx).Info().Msg("Comparer: Primary SearchChunks returned EOS")
				c.primaryDone = true
				if c.shadowDone {
					c.compare(ctx)
					return
				}
				continue
			}
			log.Ctx(ctx).Info().Msgf("Comparer: Primary SearchChunks: blob_name=%s chunk_index=%d", resp.BlobName, resp.ChunkIndex)
			c.primaryChunks = append(c.primaryChunks, resp)

		case resp, ok := <-c.shadowCh:
			if !ok {
				log.Ctx(ctx).Info().Msg("Comparer: Shadow SearchChunks returned EOS")
				c.shadowDone = true
				if c.primaryDone {
					c.compare(ctx)
					return
				}
				continue
			}
			log.Ctx(ctx).Info().Msgf("Comparer: Shadow SearchChunks: blob_name=%s chunk_index=%d", resp.BlobName, resp.ChunkIndex)
			c.shadowChunks = append(c.shadowChunks, resp)
		case <-ctx.Done():
			log.Ctx(ctx).Info().Msg("Comparer: SearchChunks canceled")
			return
		}
	}
}

// Launch requests to the primary and shadow but return as soon as the primary does.
// Then in the background wait for the shadow call and compare its results with the primary.
func (s *shadowProxyEmbeddingsSearchServer) SearchChunks(req *proto.SearchChunksRequest, stream proto.EmbeddingsSearch_SearchChunksServer) error {
	requestContext, err := requestcontext.FromGrpcContext(stream.Context())
	if err != nil {
		return err
	}
	ctx := requestContext.AnnotateLogContext(stream.Context())
	claims, _ := auth.GetAugmentClaims(stream.Context())
	ctx = claims.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msg("SearchChunks")

	primary, shadow, err := s.GetClients(ctx, claims.TenantID)
	if err != nil {
		return err
	}

	var wg sync.WaitGroup
	primaryCtx, primaryCancel := s.wrapDeadline(ctx)
	defer primaryCancel()

	// Channel to receive primary response
	chSize := 1
	if req.NumResults > 1 {
		chSize = int(req.NumResults)
	}

	comparePrimaryCh, compareShadowCh, compare := NewResponseComparer()
	primaryCh := make(chan *proto.SearchChunksResponse, chSize)
	errorCh := make(chan error, 1)

	compareCtx, compareCancel := s.wrapDeadline(ctx)
	go func() {
		defer compareCancel()
		compare.Run(compareCtx)
	}()

	// Start primary call
	wg.Add(1)
	go func() {
		primaryCtx, span := tracer.Start(primaryCtx, "primary.SearchChunks")
		defer span.End()
		defer wg.Done()
		defer close(primaryCh)
		respCh, err := (*primary).SearchChunks(primaryCtx, req, requestContext)
		if err != nil {
			log.Ctx(primaryCtx).Error().Err(err).Msg("Primary SearchChunks failed")
			errorCh <- err
			return
		}
		count := 0
		for {
			select {
			case resp, more := <-respCh:
				if !more {
					log.Ctx(primaryCtx).Info().Msg("Primary SearchChunks returned EOS")
					return
				}
				if resp.Err != nil {
					log.Ctx(primaryCtx).Error().Err(resp.Err).Msg("Primary SearchChunks failed")
					errorCh <- resp.Err
					return
				}
				c := resp.Resp.GetChunk()
				if c != nil {
					comparePrimaryCh <- c
				}
				count++
				primaryCh <- resp.Resp
			case <-primaryCtx.Done():
				log.Ctx(primaryCtx).Info().Msg("Primary SearchChunks canceled")
				return
			}
		}
	}()

	if shadow != nil {
		// Start shadow call
		wg.Add(1)
		// the shadow call is not expected to return a response, so we don't need to create a channel
		// the shadow cancel is expected to outlive the primary call
		shadowCtx, shadowCancel := s.wrapDeadline(ctx)
		go func() {
			shadowCtx, span := tracer.Start(shadowCtx, "shadow.SearchChunks")
			defer span.End()
			defer wg.Done()
			defer shadowCancel() // Cleanup shadowCtx when shadow call finishes
			ch, err := (*shadow).SearchChunks(shadowCtx, req, requestContext)
			if err != nil {
				log.Ctx(shadowCtx).Error().Err(err).Msg("Shadow SearchChunks failed")
				return
			}
			count := 0

			for {
				select {
				case resp, ok := <-ch:
					if !ok {
						log.Ctx(shadowCtx).Info().Msg("Shadow SearchChunks returned EOS")
						return
					}
					if resp.Err != nil {
						log.Ctx(shadowCtx).Error().Err(resp.Err).Msg("Shadow SearchChunks failed")
						return
					}
					c := resp.Resp.GetChunk()
					if c != nil {
						compareShadowCh <- c
					}
					count++
				case <-shadowCtx.Done():
					log.Ctx(shadowCtx).Info().Msg("Shadow SearchChunks canceled")
					return
				}
			}
		}()
	}

	// Allow shadow to continue running until its own deadline
	go func() {
		wg.Wait()
		close(errorCh)
		close(comparePrimaryCh)
		close(compareShadowCh)
	}()

	// Wait for primary response
	for {
		select {
		case resp, more := <-primaryCh:
			if !more {
				// channel closed, we're done
				return nil
			}
			err := validateChunkResponse(resp)
			if err != nil {
				return err
			}
			err = stream.Send(resp)
			if err != nil {
				return err
			}
		case err := <-errorCh:
			return err
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func validateChunkResponse(resp *proto.SearchChunksResponse) error {
	if resp == nil {
		return errors.New("nil response")
	}
	if resp.GetChunk() != nil || resp.GetMissingBlobs() != nil {
		return nil
	}
	log.Error().Msgf("invalid SearchChunksResponse: %v", resp)
	return status.Error(codes.Internal, "invalid SearchChunksResponse")
}

func NewShadowProxyEmbeddingsSearchServer(
	clientFactory ClientFactory,
	policyManager PolicyManager,
	featureFlagHandle featureflags.FeatureFlagHandle,
) *shadowProxyEmbeddingsSearchServer {
	return &shadowProxyEmbeddingsSearchServer{
		clientFactory:     clientFactory,
		policyManager:     policyManager,
		featureFlagHandle: featureFlagHandle,
	}
}

package client

// Package client provides a client for the embeddings search service.

import (
	"context"
	"errors"
	"io"
	"strconv"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/embeddings_search_host/proto"
)

// part of the result of a search chunk request
type SearchChunkResult struct {
	// the response
	// nil if there was an error
	Resp *pb.SearchChunksResponse

	// the error
	// nil if there was no error
	Err error
}

// EmbeddingsSearchClient is the interface for the embeddings search client
type EmbeddingsSearchClient interface {
	// SearchChunks performs a search for the given queries.
	SearchChunks(ctx context.Context, req *pb.SearchChunksRequest, requestContext *requestcontext.RequestContext) (<-chan <PERSON><PERSON>hunk<PERSON><PERSON>ult, error)

	// FindMissing returns the subset of the given blob names for which the embeddings were not found.
	FindMissing(ctx context.Context, req *pb.FindMissingRequest, requestContext *requestcontext.RequestContext) (*pb.FindMissingResponse, error)

	// This should be called to cleanup resources for this client
	Close()
}

// EmbeddingsSearchClientImpl implementation of the EmbeddingsSearchClient interface.
type EmbeddingsSearchClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	client pb.EmbeddingsSearchClient
}

// NewEmbeddingsSearchClient creates a new EmbeddingsSearchClient.
//
// endpoint: The endpoint of the embeddings search service.
// credentials: The credentials to use for the channel (optional)
//
// Returns: The client stub
func NewEmbeddingsSearchClient(endpoint string, credentials credentials.TransportCredentials) (EmbeddingsSearchClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := pb.NewEmbeddingsSearchClient(conn)
	return &EmbeddingsSearchClientImpl{conn: conn, client: client}, nil
}

func (c *EmbeddingsSearchClientImpl) SearchChunks(ctx context.Context, req *pb.SearchChunksRequest, requestContext *requestcontext.RequestContext) (<-chan SearchChunkResult, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	stream, err := c.client.SearchChunks(ctx, req)
	if err != nil {
		return nil, err
	}
	ch := make(chan SearchChunkResult)
	go func() {
		defer close(ch)
		for {
			r, err := stream.Recv()
			if err != nil {
				if !errors.Is(err, io.EOF) {
					ch <- SearchChunkResult{Err: err}
				}
				return
			}
			ch <- SearchChunkResult{Resp: r}
		}
	}()
	return ch, nil
}

func (c *EmbeddingsSearchClientImpl) FindMissing(ctx context.Context, req *pb.FindMissingRequest, requestContext *requestcontext.RequestContext) (*pb.FindMissingResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.FindMissing(ctx, req)
}

func (c *EmbeddingsSearchClientImpl) Close() {
	c.conn.Close()
}

// PartitionedEmbeddingsSearchClient implements EmbeddingsSearchClient by partitioning
// requests across multiple clients based on request session ID hash.
type PartitionedEmbeddingsSearchClient struct {
	clients []EmbeddingsSearchClient
}

// NewPartitionedEmbeddingsSearchClient creates a new PartitionedEmbeddingsSearchClient.
func NewPartitionedEmbeddingsSearchClient(clients []EmbeddingsSearchClient) EmbeddingsSearchClient {
	return &PartitionedEmbeddingsSearchClient{clients: clients}
}

func (c *PartitionedEmbeddingsSearchClient) SearchChunks(ctx context.Context, req *pb.SearchChunksRequest, requestContext *requestcontext.RequestContext) (<-chan SearchChunkResult, error) {
	sessionID := requestContext.RequestSessionId.String()
	val, err := strconv.ParseUint(sessionID[len(sessionID)-4:], 16, 64)
	if err != nil {
		return nil, err
	}
	bucket := uint32(val) % uint32(len(c.clients))
	return c.clients[bucket].SearchChunks(ctx, req, requestContext)
}

func (c *PartitionedEmbeddingsSearchClient) FindMissing(ctx context.Context, req *pb.FindMissingRequest, requestContext *requestcontext.RequestContext) (*pb.FindMissingResponse, error) {
	sessionID := requestContext.RequestSessionId.String()
	val, err := strconv.ParseUint(sessionID[len(sessionID)-4:], 16, 64)
	if err != nil {
		return nil, err
	}
	bucket := uint32(val) % uint32(len(c.clients))
	return c.clients[bucket].FindMissing(ctx, req, requestContext)
}

func (c *PartitionedEmbeddingsSearchClient) Close() {
	for _, client := range c.clients {
		client.Close()
	}
}

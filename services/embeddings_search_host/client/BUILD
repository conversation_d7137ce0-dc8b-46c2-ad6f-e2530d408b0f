load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")

py_library(
    name = "client",
    srcs = ["client.py"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/blob_names/python:blob_names",
        "//base/proto:tensor",
        "//base/python/grpc:client_options",
        "//services/embeddings_search_host:embeddings_search_py_proto",
        "//services/lib/grpc:stub_cycler",
        "//services/lib/request_context:request_context_py",
    ],
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/embeddings_search_host/client",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//services/embeddings_search_host:embeddings_search_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

syntax = "proto3";
package embeddings_search;

import "base/blob_names/blob_names.proto";
import "base/proto/tensor.proto";
import "google/protobuf/timestamp.proto";

// embeddings search service
//
// the embeddings search service is responsible for searching the most similar (according to a
// implementation defined metric) entries based an embedding search given a query.
service EmbeddingsSearch {
  // The chunks will be streamed back in sorted order by the descending score
  // (best to worst). The chunks are streamed in case the total size of all
  // chunks is too large to fit in a single message.
  //
  // Usually the stream will return exactly one MissingBlobs response and a
  // series of RetrievalChunk responses. The MissingBlobs response can happen at
  // any time, and is not ordered like the RetrievalChunk responses.
  rpc SearchChunks(SearchChunksRequest) returns (stream SearchChunksResponse);

  rpc FindMissing(FindMissingRequest) returns (FindMissingResponse);
}

message ChunkId {
  bytes blob_name = 1;
  int32 chunk_index = 2;
}

message QueryResult {
  repeated ChunkId chunks = 1;
}

message SearchChunksRequest {
  // a single query as a vector of size [dim] in either float16/float32 depending on the host.
  // the tensor for each query is usually the embedding calculated using an embedder
  tensor.Tensor query = 1 [debug_redact = true];

  // how many results to return, e.g. 32 to return the 32 most similar results
  int32 num_results = 2;

  // the transformation key to use for the search
  string transformation_key = 3;

  // All blob names that are available for the given queries, specified in the
  // compact delta format,
  // at least one of the blobs must be specified.
  // if multiple blobs are specified, the union of the blobs is used.
  repeated base.blob_names.Blobs blobs = 4;

  // TODO: A similar API with extended context may take a source ID here instead of blobs, e.g. a documentation library, a Github repo, a Notion space

  // Spend up to this long searching for results. 0 means unset (will default back to 1 second
  // timeout which is the historical behavior).
  // Generally speaking, this should be at most 100% of the time we budget for a model to run,
  // but at least (say) 20%. Current settings are 1s for completions, 5s for everything else.
  uint32 search_timeout_ms = 5;

  // Cancellation mechanism similar to inference_host: each request cancels all requests with
  // the same session_id and transformation_key and a lower sequence_id.
  // Note: this relies heavily on (1) different models using disjoint sets of transformation keys
  // and (2) all retrievals for the same model using the same sequence_id. If either of those
  // assumptions is violated we'll need to add some more fields here.
  optional uint32 sequence_id = 6;

  reserved 7;
}

// metadata about the blob:
// - path: the path of the file that the blob represents
// - upload_time: the time the blob was uploaded, in UTC in RFC3339 format, e.g. 2024-07-08T10:05:57.453Z
message BlobMetadata {
  string key = 1 [debug_redact = true];
  string value = 2 [debug_redact = true];
}

message RetrievalChunk {
  // The serialized chunk (see chunk.proto:Chunk)
  bytes content = 1 [debug_redact = true];
  float score = 2;

  // The blob this chunk belongs to
  string blob_name = 3;

  // The index of this chunk in that blob
  uint32 chunk_index = 4;

  repeated BlobMetadata metadata = 5;
}

message MissingBlobs {
  repeated string missing_blob_names = 1;
  bool checkpoint_not_found = 2;
}

// IMPORTANT:
// If the CHECKPOINT_NOT_FOUND field is set to True, retrieval failed to resolve
// the given baseline_checkpoint_id. This condition will persist until the client
// either regenerates the checkpoint or retries with a different checkpoint_id.
message SearchChunksResponse {
  oneof response {
    // Retrieved chunks are streamed back in sorted order by the descending score
    // (best to worst).
    RetrievalChunk chunk = 1;
    // if a blob name in the given list is not available (for any reason)
    // the request should NOT fail, but the blob name should be added
    // to this list.
    MissingBlobs missing_blobs = 2;
  }
}

message FindMissingRequest {
  // The collection of blob names to probe.
  repeated string blob_names = 1;

  // The transformation key to use when searching for blob names.
  string transformation_key = 2;

  // The sub key to use when searching for blob names.
  string sub_key = 3;
}

message FindMissingResponse {
  // The subset of blob names that are not indexed for this embeddings search service.
  repeated string missing_blob_names = 1;
}

// an implementation protocol of  embeddings search service
service PeerEmbeddingsSearch {
  rpc GetPeerCache(GetPeerCacheRequest) returns (stream GetPeerCacheResponse);
}

message GetPeerCacheRequest {}

message GetPeerCacheResponse {
  string tenant_id = 1;
  string blob_name = 2;
  string transformation_key = 3;
  string sub_key = 4;
  tensor.Tensor tensor = 5 [debug_redact = true];
}

// This file contains the base configuration for a shard namespace.
local certLib = import 'deploy/common/cert-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local supportCert = certLib.createClientCert(name='support-certificate',
                                               namespace=namespace,
                                               appName='augment-support',
                                               volumeName='',  // Not stored in any service
                                               dnsNames=['augment-support.%s' % namespace],
                                               duration='2h',
                                               renewBefore='1h');
  // A support certificate for the central namespaces
  local centralSupportCert = certLib.createCentralClientCert(
    name='support-central-certificate',
    env=env,
    namespace=namespace,
    appName='augment-support',
    volumeName='',  // Not stored in any service
    dnsNames=['augment-support.%s' % namespace],
    duration='2h',
    renewBefore='1h',
  );
  lib.flatten([
    supportCert.objects,
    centralSupportCert.objects,
  ])

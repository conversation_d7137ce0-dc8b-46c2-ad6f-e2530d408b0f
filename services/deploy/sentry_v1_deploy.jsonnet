local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local modelConfig = import 'services/deploy/sentry_v1_config.jsonnet';

local tensorParallelism = 2;

local name = 'sentry-v1';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=tensorParallelism,
    inferGpu=if env == 'DEV' then 'small' else 'large',
    mtls=mtls,
    attentionType=if env == 'DEV' then 'MULTI_REQUEST_FLASH' else 'MULTI_REQUEST_FLASH_V3',
    allReduceImplementation='FASTFORWARD',
    enforceGlobal=true,
  );
  if filter == null then
    inference()
  else if filter == 'inference' then
    inference()
  else
    error ('unknown filter: %s' % filter)

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = import 'services/deploy/raven_location_v2_fp8_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local queryEmbedder() = modelDeployment.embedder(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig.queryModelConfig,
    // we only need relatively few query embedders
    replicas=if env == 'PROD' && cloud == 'GCP_US_CENTRAL1_GSC_PROD' then 4 else null,
  );
  local docEmbedder() = modelDeployment.embedder(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig.docModelConfig,
    replicas=if env == 'PROD' && cloud == 'GCP_US_CENTRAL1_GSC_PROD' then 44 else null,
  );
  local indexer() = modelDeployment.embeddingIndexer(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig.docModelConfig,
  );
  local key() = modelDeployment.transformationKey(
    namespace=namespace,
    transformationKeyName=embedderConfig.docModelConfig.transformationKey,
    appName=embedderConfig.docModelConfig.modelConfig.name,
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace) : 'Indexer and embedder together is only supported in DEV';
    queryEmbedder() + docEmbedder() + indexer() + key()
  else if filter == 'embedder' then
    queryEmbedder() + docEmbedder()
  else if filter == 'indexer' then
    indexer() + key()
  else
    error ('unknown filter: %s' % filter)

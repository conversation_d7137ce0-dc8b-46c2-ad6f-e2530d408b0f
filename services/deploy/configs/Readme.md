# FTM Inference Configurations

Default model instance configurations.
The configurations are usable by tests and various CLI tools.

The 6B and 16B models are only available without the --no-augment-fs
option and only in the AWS environment.

For the other models, the matching dependencies from `tools/bzl/deps/archive.bzl` need to be added to the target.

# Format

All files follow the repo_model_config.proto format (in JSON format).

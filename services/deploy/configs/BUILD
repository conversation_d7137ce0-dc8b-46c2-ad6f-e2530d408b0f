load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
    "jsonnet_to_json",
)
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_library", "py_proto_library")

jsonnet_library(
    name = "lib",
    srcs = [
        "languages.jsonnet",
    ],
    visibility = [
        "//models:__subpackages__",
        "//services:__subpackages__",
    ],
)

jsonnet_library(
    name = "config_lib",
    srcs = glob(
        ["*.jsonnet"],
        exclude = [
            "languages.jsonnet",
        ],
    ),
    visibility = [
        "//models:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":lib",
    ],
)

jsonnet_library(
    name = "orb",
    srcs = ["orb.jsonnet"],
    visibility = [
        "//services:__subpackages__",
    ],
)

CONFIGS = [
    "codegen-350M-multi",
    "starcoder-1B-base",
    "starcoder-3B-base",
    "starcoder-7B-base",
    "starcoder-16B-base",
    "starcoder2-7B-base",
    "rogue-1B",
    "rogue-3B",
    "rogue-7B",
    "rogue-16B",
    "rogue-1B-fp8",
    "rogue-16B-fp8",
    "roguesl-16B-fp8",
    "roguesl-plainv2-16B-fp8",
    "roguesl-farpref-16B-fp8",
    "llama3-1-8b-instruct-fp8",
]

[
    jsonnet_to_json(
        name = "{}-json".format(file),
        src = "{}.jsonnet".format(file),
        outs = ["{}.json".format(file)],
        visibility = ["//visibility:public"],
        deps = [":lib"],
    )
    for file in CONFIGS
]

filegroup(
    name = "configs",
    srcs = [":{}-json".format(file) for file in CONFIGS],
    visibility = [
        "//models:__subpackages__",
        "//services:__subpackages__",
    ],
)

proto_library(
    name = "repo_model_config_proto",
    srcs = ["repo_model_config.proto"],
)

cc_proto_library(
    name = "repo_model_config_cc_proto",
    visibility = ["//visibility:public"],
    deps = [":repo_model_config_proto"],
)

py_proto_library(
    name = "repo_model_config_py_proto",
    protos = [":repo_model_config_proto"],
    visibility = ["//visibility:public"],
)

py_library(
    name = "repo_model_util",
    srcs = ["repo_model_util.py"],
    visibility = ["//visibility:public"],
    deps = [":repo_model_config_py_proto"],
)

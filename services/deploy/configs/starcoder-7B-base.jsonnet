// the original starcoder 7B base model
//
// Model Card: https://www.notion.so/Q2-2023-StarCoder-and-SantaCoder-models-d4adbd8b7e37443185e2a55a8fc53a46?pvs=4
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'starcoder-7B-base',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starcoderbase-7b_neox/checkpoint_v2',
    checkpoint_sha256: 'bbf11038ec4ef5a0103948b448d7c8e711744f286e7eaa54765baf3fa9dc5529',  // pragma: allowlist secret
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'starcoder',
      prompt_formatter_name: 'starcoder',
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
    },
    model_arch: {
      arch_type: 'STARCODER',
      num_layers: 42,
      vocab_size: 51200,
      emb_dim: 4096,
      num_heads: 32,
      head_dim: 128,
      norm_eps: 1e-5,
    },
  }

// the original starcoder 16B base model
//
// Model Card: https://www.notion.so/Q2-2023-StarCoder-and-SantaCoder-models-d4adbd8b7e37443185e2a55a8fc53a46?pvs=4
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'starcoder-16B-base',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starcoderbase_neox/checkpoint-mps1_v2',
    checkpoint_sha256: 'f83b93e8856fa4cdf98f796e1a9c6081b5c5a8cd79b560b2fb07e101f28e2ccd',  // pragma: allowlist secret
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'starcoder',
      prompt_formatter_name: 'starcoder',
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
    },
    model_arch: {
      arch_type: 'STARCODER',
      num_layers: 40,
      vocab_size: 51200,
      emb_dim: 6144,
      num_heads: 48,
      head_dim: 128,
      norm_eps: 1e-5,
    },
  }

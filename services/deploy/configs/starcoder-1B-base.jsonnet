// the original starcoder 1B base model
//
// Model Card: https://www.notion.so/Q2-2023-StarCoder-and-SantaCoder-models-d4adbd8b7e37443185e2a55a8fc53a46?pvs=4
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'starcoder-1B-base',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint_v2',
    checkpoint_sha256: '8a26c397f2c233ec1c0cac92e66b85def17f8322bc0f53f84c6233fc57c6a220',  // pragma: allowlist secret
    repo_deepspeed_checkpoint_path: 'external/starcoderbase_1b_deepspeed_checkpoint',
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'starcoder',
      prompt_formatter_name: 'starcoder',
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
    },
    model_arch: {
      arch_type: 'STARCODER',
      num_layers: 24,
      vocab_size: 51200,
      emb_dim: 2048,
      num_heads: 16,
      head_dim: 128,
      norm_eps: 1e-5,
    },
  }

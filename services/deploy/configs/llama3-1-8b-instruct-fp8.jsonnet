// the LLaMA 8B instruct model
//
function(env='PROD')
  {
    name: 'llama3_1-8B-instruct-fp8',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/llama3.1/ff/Meta-Llama-3.1-8B-Instruct-FP8',
    checkpoint_sha256: '50c8b260df69f96c8ad0f127bc845406dd3726cb47a29938c731cd3fd99f4ee7',
    model_type: 'CHAT',
    inference: {
      // NOTE(arun): These values are meaningless, and should be overridden by the user.
      tokenizer_name: 'llama3_instruct',
      prompt_formatter_name: '(fill me)',
      max_context_length: 8 * 1024,
    },
    // Values derived from the checkpoint_path/params.json
    model_arch: {
      arch_type: 'LLAMA_FP8',
      emb_dim: 4096,
      num_layers: 32,
      num_heads: 8,
      num_queries_per_head: 4,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      rotary_theta: 500000.0,
      rotary_scaling_factor: 1.0,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_extension_method: 'llama3_1',
      vocab_size: 128256,
      mlp_dim_divisible_by: 1024,
      ffn_dim_multiplier: 1.3,
      norm_eps: 1e-5,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
    },
    // Calculating the size of the cache pool.
    // One cache line needs ~1GB of memory for 8K tokens:
    //     8000 * 8 * 2 * 2 * 128 * 32 bytes = 1GB
    // We need ~8GB for the model and ~4GB for activations.
    // In DEV, we use an l4 machine with 24GB, and can fit ~12 cache lines.
    // In PROD, we use a H100 with 80GB and can fit ~68 cache lines.
    cache_pool_size: if env == 'DEV' then 10 else 68,
    round_sizes: [32, 128, 512, 1024, 1536],
  }

// the starcoder2 3B base model
//
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    // NOTE: The checkpoint path and checksum need to be overriden.
    name: 'starcoder2-3B-base',
    efs_deepspeed_checkpoint_path: '',
    checkpoint_sha256: '',
    model_type: 'INFERENCE',
    inference: {
      // NOTE(arun): These values are meaningless, and should be overridden by the user.
      tokenizer_name: 'starcoder2',
      prompt_formatter_name: 'starcoder',
      languages: lang_presets.starcoder,
      max_context_length: 8 * 1024,
    },
    // Values derived from the checkpoint_path/params.json
    model_arch: {
      arch_type: 'STARCODER2_FP8',
      num_layers: 30,
      vocab_size: 51200,
      emb_dim: 3072,
      num_heads: 24,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 12,
    },
  }

// the starcoder2 7B base model
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'starcoder2-7B-base',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starcoder2/fastforward/starcoder2-7b',
    checkpoint_sha256: '5df6e0e2761359d30a8275058e299fcc0381534545f55cf43e41983f5d4c9456',
    model_type: 'INFERENCE',
    inference: {
      // NOTE(arun): These values are meaningless, and should be overridden by the user.
      tokenizer_name: 'starcoder2',
      prompt_formatter_name: 'starcoder',
      languages: lang_presets.starcoder,
      max_context_length: 8 * 1024,
    },
    // Values derived from the checkpoint_path/params.json
    model_arch: {
      arch_type: 'STARCODER2',
      num_layers: 32,
      vocab_size: 51200,
      emb_dim: 4608,
      num_heads: 36,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 1000000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 9,  // 36 / 4.
    },
  }

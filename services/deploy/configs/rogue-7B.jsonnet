// a rogue 7B model
//
// Improve code completion quality by fine-tuning StarCoder model to better use retrieval
//
// Model Card: https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e?pvs=4
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'rogue-7B',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/rogue/7b_diffb1m_chunk30/global_step1000_v2',  // pragma: allowlist secret
    checkpoint_sha256: '158f4824bf6ea3a3e8aba93d4c9235a1d803464ba1a9ef5236ef7cc4124ba576',  // pragma: allowlist secret
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'rogue',
      prompt_formatter_name: 'rogue',
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
      extra_stop_tokens: ['<|skip|>', '<|pause|>'],
    },
    model_arch: {
      arch_type: 'STARCODER',
      num_layers: 42,
      vocab_size: 51200,
      emb_dim: 4096,
      num_heads: 32,
      head_dim: 128,
      norm_eps: 1e-5,
    },
  }

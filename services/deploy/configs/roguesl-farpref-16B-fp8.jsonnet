// a rogue 16B model with stateless caching
//
// Improve latency by arranging prompt to achieve more cache hits
//
// Model Card: https://www.notion.so/Q4-2023-Rogue-SL-models-f7f4dffab2f24bdfa769d43553068fb8
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'roguesl-farpref-16B-ETH6-FP8',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/roguesl/eth61m_fixfim_farprefix_npref250_olap0_quant50/checkpoint_fp8.pth_v2',  // pragma: allowlist secret
    checkpoint_sha256: '746f1ab92e9faa8e742084e5b1b35a92a86395f130c08f656731223bf35aef8a',  // pragma: allowlist secret
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'rogue',
      prompt_formatter_name: 'rogue_sl',
      apportionment_config: {
        max_content_len: 4 * 1024,
        input_fraction: 0.5,
        prefix_fraction: 0.75,
        max_path_tokens: 50,
      },
      prompt_formatter_config: {
        stateless_caching_config: {
          nearby_prefix_token_len: 250,
          quantize_token_len: 50,
          quantize_char_len: 200,
        },
        component_order: ['path', 'prefix', 'suffix', 'retrieval', 'nearby_prefix'],
        use_far_prefix_token: true,
      },
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
      extra_stop_tokens: ['<|skip|>', '<|pause|>'],
    },
    model_arch: {
      arch_type: 'STARCODER_FP8',
      num_layers: 40,
      vocab_size: 51200,
      emb_dim: 6144,
      num_heads: 48,
      head_dim: 128,
      norm_eps: 1e-5,
    },
  }

// original codegen 350 model
//
// Model Card: https://www.notion.so/Q1-2022-CodeGen-models-fd3597be9a4b4bb895f23cb60d53af11?pvs=4
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'codegen-350-multi',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/codegen-350M-multi/checkpoint/',
    repo_deepspeed_checkpoint_path: '../codegen-350M-multi/checkpoint',
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'fim',
      prompt_formatter_name: 'codegen',
      languages: lang_presets.codegen,
      max_context_length: 2048,
    },
    model_arch: {
      arch_type: 'CODEGEN',
      num_layers: 20,
      vocab_size: 51200,
      emb_dim: 1024,
      num_heads: 16,
      head_dim: 64,
      rotary_pct: 0.5,
      norm_eps: 1e-5,
    },
  }

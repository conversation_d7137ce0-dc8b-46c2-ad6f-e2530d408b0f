"""Library to use repo model configs stored in JSON format."""

import pathlib

import google.protobuf.json_format as json_format

import services.deploy.configs.repo_model_config_pb2 as repo_model_config_pb2


def load_repo_model_config(path: pathlib.Path) -> repo_model_config_pb2.RepoModelConfig:
    """Returns the repo model configuration."""
    json_content = path.read_text(encoding="utf-8")

    model_config = json_format.Parse(
        json_content, repo_model_config_pb2.RepoModelConfig()
    )
    return model_config

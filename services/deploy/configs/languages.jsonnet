{
  presets: function(env) {
    codegen: ['C', 'C++', 'Go', 'Java', 'JavaScript', 'Python'],
    // actually, starcoder was trained on more languages, but let's take these for now

    // NOTE: This list used to show which languages the model supports,
    // so we could turn on auto-completion in VSCode for those languages only.
    // But now, we offer auto-completion for almost all languages,
    // except a few that users can choose not to include in the extension settings.
    //
    // Instead, this list is used to specify which file (file extensions) do we upload.
    // So for now, we can have languages for which we will do completions
    // but we won't upload source files of that language.
    //
    // We can't upload every type of file because we need to avoid non-text files like images,
    // audio files, and compressed files.
    // Plus, our tool for reading text files sometimes crashes with certain binary files.
    // So, for the time being, we're being cautious and only uploading specific file types
    // to keep things running smoothly.
    //
    // Keep in mind that the connection between a language and its file extension is defined in
    // `base/languages/languages.jsonnet`. If you're thinking about adding
    // a new language to the list, you need to ensure that the language is already set up
    // with its corresponding file extension in our system.
    //
    // See https://linear.app/augmentcode/issue/AU-1798 for relevant discussion.

    starcoder: [
                 'C',
                 'C++',
                 'Go',
                 'Java',
                 'JavaScript',
                 'Python',
                 'TypeScript',
                 'Rust',
                 'CSharp',
                 'Shell',
                 'Perl',
                 'PHP',
                 'Lua',
                 'Swift',
                 'R',
                 'Ruby',
                 'Racket',
                 'Scala',
                 'Kotlin',
                 'JavaScript JSX',
                 'TypeScript JSX',
                 'Svelte',
                 'Astro',
                 'HTML',
               ]
               + if env == 'DEV' then ['Jsonnet'] else [],

    starcoder_expanded: [
      'C',
      'C++',
      'Go',
      'Java',
      'JavaScript',
      'Python',
      'TypeScript',
      'Rust',
      'CSharp',
      'Shell',
      'Perl',
      'PHP',
      'Lua',
      'Swift',
      'R',
      'Ruby',
      'Racket',
      'Scala',
      'Kotlin',
      'JavaScript JSX',
      'TypeScript JSX',
      'Svelte',
      'Astro',
      'HTML',
      // Expanded set
      'Cuda',
      'Jsonnet',
      'Markdown',
      'YAML',
      'XML',
      'Protobuf',
      'Plain Text',
      'SQL',
      'JSON',
      'Dart',
      'CSS',
      'Clojure',
      'Visual Basic',
      'TeX',
      'Strato',
      'ERB',
      'TOML',
      'Dockerfile',
      'INI',
      'Verilog',
      'PowerShell',
      'Smarty',
      'Gradle',
      'HTTP',
      'Arduino',
      'Limbo',
      'JSON5',
      'Gas',
      'TCL',
      'Makefile',
      'Cmake',
      'Groovy',
      'Vue',
      'Objective-C',
      'Nix',
      'ANTLR',
      'Jinja',
      'Haskell',
      'HAML',
    ],
  },
}

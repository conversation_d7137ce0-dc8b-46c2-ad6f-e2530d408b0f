// a rogue 1B model
//
// Improve code completion quality by fine-tuning StarCoder model to better use retrieval
//
// Model Card: https://www.notion.so/Q3-2023-Rogue-models-71771c1ae50446fd9c96a8e721c2168e?pvs=4
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'rogue-1B-FP8',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/rogue/rogue1B_diffb1m_chunk30/checkpoint_fp8',  // pragma: allowlist secret
    checkpoint_sha256: '30b01ae92c4970cf2a6fdc0dfa02692d0d1eb5c6c4dea6cd2c23492bd1a4b349',  // pragma: allowlist secret
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'rogue',
      prompt_formatter_name: 'rogue',
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
      extra_stop_tokens: ['<|skip|>', '<|pause|>'],
    },
    model_arch: {
      arch_type: 'STARCODER_FP8',
      num_layers: 24,
      vocab_size: 51200,
      emb_dim: 2048,
      num_heads: 16,
      head_dim: 128,
      norm_eps: 1e-5,
    },
    post_processing: {
      low_quality_filter_config: {
        checkpoint_path: 'services/completion_host/single_model_server/prism_models/prism_roguesl_farpref_16B.json',
        feature_extractor_version: 'feature_extractor_v1',
        default_threshold: 1.0,
      },
    },
  }

// the original starcoder 3B base model
//
// Model Card: https://www.notion.so/Q2-2023-StarCoder-and-SantaCoder-models-d4adbd8b7e37443185e2a55a8fc53a46?pvs=4
function(env='PROD')
  local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
  {
    name: 'starcoder-3B-base',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starcoderbase-3b_neox/checkpoint_v2',
    checkpoint_sha256: '26a230137f584fcb03ac467d79a3ebd380a5d8778d002465fe2bdb284511b2ff',  // pragma: allowlist secret
    model_type: 'INFERENCE',
    inference: {
      tokenizer_name: 'starcoder',
      prompt_formatter_name: 'starcoder',
      languages: lang_presets.starcoder,
      max_context_length: 4 * 1024,
    },
    model_arch: {
      arch_type: 'STARCODER',
      num_layers: 36,
      vocab_size: 51200,
      emb_dim: 2816,
      num_heads: 22,
      head_dim: 128,
      norm_eps: 1e-5,
    },
  }

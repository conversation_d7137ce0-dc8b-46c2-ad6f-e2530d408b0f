// FP8 inference for raven_location_v2.
// Commands to produce these two checkpoints:
// Query model
// > python research/tools/quantization/quantize_starcoder.py -cp /mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.3,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50.query.ffwd \
//    --checkpoint_sha256=65e244a04cbb95e47929d1ecf0920fe60c2433f5e474e0fe47a52c65a2054a39 -ms starcoder-1b -cd /mnt/efs/spark-data/user/arun/next-edit-location/S1.3_prs_2k.keepmost.filter.empty10,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50,indexed_dataset/dataset \
//    -op /mnt/efs/augment/checkpoints/next-edit-location/raven-location-v2-query-fp8 --is_embedder -n 1000
// Document model
// > python research/tools/quantization/quantize_starcoder.py -cp /mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.3,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50.document.ffwd \
//    --checkpoint_sha256=631ab487d102b037a55780fd57cab01f8557d32e043f378c3cbd56318848f199 -ms starcoder-1b -cd /mnt/efs/spark-data/user/arun/next-edit-location/S1.3_prs_2k.keepmost.filter.empty10,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50,indexed_dataset/dataset \
//    -op /mnt/efs/augment/checkpoints/next-edit-location/raven-location-v2-doc-fp8 --is_embedder -n 1000

local starcoder_1b = import 'services/deploy/configs/starcoder-1B-base.jsonnet';
local constants = import 'services/deploy/constants.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local queryModelConfig = starcoder_1b() + {
  name: 'raven-location-v2-query',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-location/raven-location-v2-query-fp8',
  checkpoint_sha256: 'e64c8292172afdcd2decec58f3bbc0dd973ce02ffa13445a5195e258ad959220',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch+: {
    arch_type: 'STARCODER_FP8',
    output_projection_dim: 512,
    max_position_embeddings: 8192,
  },
  embedding: {
    tokenizer_name: 'starcoder',
    query_prompt_formatter_name: 'next-edit-location-query',
    // NOTE(arun): We don't need this for the query model.
    key_prompt_formatter_name: 'passthrough-document',
  },
  // We use relatively long queries for next edits.
  max_seq_length: 4096,
  round_sizes: [256, 512, 1024, 2048, 4096],
};
local docModelConfig = starcoder_1b() + {
  name: 'raven-location-v2-doc',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-location/raven-location-v2-doc-fp8',
  checkpoint_sha256: '012b00f1dcbafa10ad5d6bf69443aecde38f6419f659c38a9ffc3f7ffbee6426',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch+: {
    arch_type: 'STARCODER_FP8',
    output_projection_dim: 512,
    max_position_embeddings: 8192,
  },
  embedding: {
    tokenizer_name: 'starcoder',
    // NOTE(arun): We don't need this for the document model.
    query_prompt_formatter_name: 'passthrough',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  chunking: {
    name: 'smart_line_level',
    config: {
      max_chunk_chars: 2048,
    },
    // Retrieving docset documents for location finding is not useful.
    skip_docset_documents: true,
  },
  // We use shorter, but still fairly long queries for location chunks.
  max_seq_length: 1024,
  round_sizes: [256, 512, 1024],
  max_number_of_chunks_per_blob: 512,
};

// NOTE(arun): We use the same transformation key in the both model configurations:
// In the indexer we use the doc model with this transformation key, while in the
// next edit host, we use the query model and use this transformation key for the
// embedding search lookup.
local transformationKey = 'dr-%s-2048char' % (std.asciiLower(docModelConfig.name));

{
  queryModelConfig: modelDeployment.embeddingModelConfig(
    modelConfig=queryModelConfig,
    transformationKey=transformationKey,
    chunkOrigin=constants.chunkOrigin.LOCATION_RETRIEVER,
  ),
  docModelConfig: modelDeployment.embeddingModelConfig(
    modelConfig=docModelConfig,
    transformationKey=transformationKey,
    chunkOrigin=constants.chunkOrigin.LOCATION_RETRIEVER,
  ),
}

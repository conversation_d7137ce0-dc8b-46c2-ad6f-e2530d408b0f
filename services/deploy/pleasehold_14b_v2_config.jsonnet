local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local name = 'pleasehold-14b-v2';
local modelConfig = {
  name: name,
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8',
  checkpoint_sha256: '301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6',  // pragma: allowlist secret
  model_type: 'ROUTER',
  model_arch: {
    arch_type: 'QWEN25CODER_FP8',
    num_layers: 48,
    vocab_size: 152064,
    emb_dim: 5120,
    num_heads: 8,  // n_kv_heads from config
    head_dim: 128,
    attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
    norm_eps: 1e-6,
    rotary_pct: 1.0,  // RoPE parameters
    rotary_theta: 1000000.0,
    rotary_scaling_factor: 1.0,
    max_position_embeddings: 16384,  // Make sure >=`max_context_length`
    num_queries_per_head: 5,  // 40 / 8 (n_heads / n_kv_heads)
    mlp_dim_divisible_by: 256,
    ffn_dim_multiplier: 1.0,
  },
  inference: {
    tokenizer_name: 'qwen25coder',
    prompt_formatter_name: 'pleasehold_v1',
    token_apportionment: {
      path_len: 256,
      message_len: -1,
      selected_code_len: -1,
      prefix_len: 2048,
      suffix_len: 2048,
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,
      max_prompt_len: 1024 * 12,
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 2,  // 2k for tool results
    },
    max_context_length: 16384,  // Total length of prompt and generation
    max_output_length: 2048,  // Max number of generated tokens  },
  },
  router_timeout_s: 5,
  round_sizes: [32, 64, 128, 256, 512, 1024, 2048, 4096],
};
modelConfig

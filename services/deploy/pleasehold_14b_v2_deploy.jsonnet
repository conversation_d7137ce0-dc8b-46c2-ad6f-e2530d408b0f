local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local modelConfig = import 'services/deploy/pleasehold_14b_v2_config.jsonnet';

local name = 'pleasehold-14b-v2';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local tensorParallelism = if env == 'DEV' then 1 else 2;
  local modelConfigOverrides = if env == 'DEV' then {
    cache_pool_size: 4,
  } else {};

  local router() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig + modelConfigOverrides,
    inferTensorPara=tensorParallelism,
    inferGpu='large',
    mtls=mtls,
    attentionType='MULTI_REQUEST_FLASH_V3',
    allReduceImplementation='FASTFORWARD',
    enforceGlobal=true,
  );
  if filter == null then
    router()
  else if filter == 'inference' then
    router()
  else
    error ('unknown filter: %s' % filter)

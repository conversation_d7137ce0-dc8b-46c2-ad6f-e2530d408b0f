local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local embedderConfig = import 'services/deploy/chatanol-qwen-v1-1_fp8_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

function(env, namespace, cloud, namespace_config, filter=null)
  local embedder() = modelDeployment.embedder(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig,
    replicas=if env == 'PROD' && cloud == 'GCP_US_CENTRAL1_GSC_PROD' then 48 else null,
  );
  local indexer() = modelDeployment.embeddingIndexer(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig,
  );
  local key() = modelDeployment.transformationKey(
    namespace=namespace,
    transformationKeyName=embedderConfig.transformationKey,
    appName=embedderConfig.modelConfig.name,
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace) : 'Indexer and embedder together is only supported in DEV';
    embedder() + indexer() + key()
  else if filter == 'embedder' then
    embedder()
  else if filter == 'indexer' then
    indexer() + key()
  else
    error ('unknown filter: %s' % filter)

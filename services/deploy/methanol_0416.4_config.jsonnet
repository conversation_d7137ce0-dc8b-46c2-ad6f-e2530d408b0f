local constants = import 'services/deploy/constants.jsonnet';
local modelConfig = {
  name: 'methanol-0416-4',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/methanol/methanol_0416.4_1250/global_step1250_v2',
  checkpoint_sha256: '4351c8f0b4ba97f9d821a91efe8a7da45c644ad7569de4bf589f30f6956bd4cb',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'ethanol6.16.1-query-embedding',
    key_prompt_formatter_name: 'passthrough-document',
  },
  chunking: {
    name: 'signature',
    config: {},
  },
  max_seq_length: 2048,
  round_sizes: [256, 512, 1024, 2048],
  max_number_of_chunks_per_blob: 512,
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig,
  transformationKey='dr-%s-1024char' % (std.asciiLower(modelConfig.name)),
  chunkOrigin=constants.chunkOrigin.DENSE_SIGNATURE,
)

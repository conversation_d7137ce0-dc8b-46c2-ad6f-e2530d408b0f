// FP8 inference for the chatanol-v1-1 model
// Command to produce this checkpoint from the fp16 one:
// > python python research/tools/quantization/quantize_llama.py -m qwen2_5-coder-1.5b-retriever --is-embedder \
//    -p /mnt/efs/augment/checkpoints/chatanol/chatanol-qwen-v1 -s ee4e3c3896cc94f318a2a023d2902c6843fb1487440f7200b6d233299cc896b2 \
//    -d /mnt/efs/augment/user/tongfei/hm/chatanol/split_dev_set/Tokenizer=qwen25coder/out/valid -n 500 -o /mnt/efs/augment/checkpoints/chatanol/chatanol-qwen-v1-fp8-smoothquant \
//    --smoothquant --max-seq-len 32000

local constants = import 'services/deploy/constants.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local name = 'chatanol-qwen-v1-1';
local modelConfig = {
  name: 'chatanol-qwen-v1-1',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol-qwen-v1-fp8-smoothquant',
  checkpoint_sha256: '36f3369ac41eac22958f938b6fbc2226cabe1d43842196474835b0ef6cd7abef',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'LLAMA_FP8',
    num_layers: 28,
    vocab_size: 152064,
    emb_dim: 1536,
    num_heads: 2,
    num_queries_per_head: 6,
    head_dim: 128,
    norm_eps: 1e-6,
    mlp_dim_divisible_by: 256,
    ffn_dim_multiplier: 2.15,
    rotary_pct: 1.0,
    rotary_theta: 1000000.0,  // This is the default value for Qwen2.5.
    rotary_scaling_factor: 1.0,
    attn_split_head_mode: 'KV_HEADS',
    qkv_only_bias: true,
    output_projection_dim: 512,
  },
  embedding: {
    tokenizer_name: 'qwen25coder',
    query_prompt_formatter_name: 'chatanol6',
    key_prompt_formatter_name: 'chatanol6-embedding-with-path-key',
  },
  max_seq_length: 2048,
  round_sizes: [256, 512, 1024, 2048],
  max_number_of_chunks_per_blob: 512,
  max_retrieval_results: 128,
};
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig + {
    name: name,
    chunking: {
      name: 'transform_smart_line_level',
      config: {
        max_chunk_chars: 768,
        max_headers: 3,
      },
    },
  },
  transformationKey='dr-%s-smart-768char' % (std.asciiLower(name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)

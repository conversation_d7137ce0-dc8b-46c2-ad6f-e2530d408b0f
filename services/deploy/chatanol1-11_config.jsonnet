local constants = import 'services/deploy/constants.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local name = 'chatanol1-11';
local modelConfig = {
  name: 'chatanol1-11',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-11/global_step250_v2',
  checkpoint_sha256: '532a29c769a15379526c3cfa53252571918f62ee2136facabace169e0d81472a',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'ethanol6-embedding-simple-chat',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  max_seq_length: 2048,
  round_sizes: [256, 512, 1024, 2048],
  max_number_of_chunks_per_blob: 512,
};
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig + { name: name },
  transformationKey='dr-%s-30line-1024char' % (std.asciiLower(name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)

# Deployment Configurations

This directory contains Kubernetes deployment configurations.
The configurations are also usable by tests and various CLI tools.

NOTE: If you are performing any of these for the first time, please check in with #services and pair up with someone.

## Adding a target

See https://www.notion.so/Runbook-How-to-deploy-a-new-inference-model-9b06d2a4b3f94c7ebc93474c6af3a317
for details.

If adding a new deployment target, say `wizard_16b`, and deploying to staging, you should have touched 4 locations:
1. `wizard_16b_deploy.jsonnet` for the deployment configuration.
2. The appropriate `BUILD` file to add the `kubecfg(name="wizard_16b_kubecfg", ...)` rule.
3. `METADATA.jsonnet` to deploy to staging. Add all targets, e.g. both the inference and completion hosts.
4. `services/deploy/BUILD` to add to `metadata_test_deps`.

For testing:
1. To inspect the new config, run `bazel run //services/deploy:wizard_16b_kubecfg -- dump > config.txt`.
2. To pass args to the config, run e.g. `bazel run //services/deploy:starethanol6_16_1_proj512_kubecfg -- --cloud=GCP_US_CENTRAL1_PROD dump --env=PROD --namespace=central --extra-config-args="filter=embedder"`. Note the order. These are defined in `tools/kubecfg/kubecfg_util.py`.
3. If modifying an existing target, run `diff before.txt after.txt` to compare the changes.
4. Run in a dev deploy: `bazel run //services/deploy:dev_deploy` and `bazel run //services/deploy:wizard_16B_kubecfg`.

If setting to default:
1. **In a separate PR**, and to be landed only once the model is actually **deployed**,
update the default model in `tools/feature_flags/flags.jsonnet`.

## Tombstones

If you want to delete a deployment, you should add a tombstone (`tombstones.jsonnet`).

See https://www.notion.so/Runbook-How-to-un-deploy-a-model-8c1145385fb24871a459850e4a605d03#7fd0cb5479a04fec921efccd81243b01
for details, in addition to the comments in `services/deploy/tombstones.jsonnet`, whichever is newer.

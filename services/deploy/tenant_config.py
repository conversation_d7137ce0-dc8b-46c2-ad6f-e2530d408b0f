"""CLI application for tenant configuration.

The major difference between this and the kubecfg application is that
this application will only create tenants if they tenant has not been marked as deleted.
"""

import argparse
import logging
import pathlib
import sys
import tempfile
from typing import Any

import yaml

from base.logging.console_logging import setup_console_logging
from tools.kubecfg.kubecfg import (
    KubeCfg,
    KubeCfgConfig,
    KubeCfgException,
    VisitConfig,
)


def _cleanup(tenant: Any):
    """Remove fields that are not relevant for comparison.

    Args:
        tenant: the tenant

    Returns:
        cleaned up tenant
    """
    keys = list(tenant["spec"])
    for key in keys:
        if tenant["spec"][key] is None:
            del tenant["spec"][key]
    return tenant


def filter_tenants(new_tenants: list[Any], existing_tenants: list[Any]) -> list[Any]:
    """Filter out tenants that already exist.

    Args:
        new_tenants: list of new tenants
        existing_tenants: list of existing tenants

    Returns:
        list of new tenants that do not exist yet
    """
    map = {}
    for existing in existing_tenants:
        map[(existing["metadata"]["namespace"], existing["metadata"]["name"])] = (
            existing
        )
    result = []
    for new in new_tenants:
        _cleanup(new)
        exists = False
        changed = False
        existing = map.get((new["metadata"]["namespace"], new["metadata"]["name"]))
        if existing:
            exists = True
            if new["spec"] == existing["spec"]:
                logging.info(
                    "Tenant %s already exists and is up to date",
                    new["metadata"]["name"],
                )
            else:
                logging.info("%s", existing["spec"])
                logging.info("%s", new["spec"])
                logging.info(
                    "Tenant %s already exists but has changed",
                    new["metadata"]["name"],
                )
                changed = True
        if not exists:
            if "deleted_at" in new["spec"]:
                logging.info(
                    "Tenant %s is deleted, not creating",
                    new["metadata"]["name"],
                )
                continue
            logging.info(
                "Tenant %s does not exist or has changed", new["metadata"]["name"]
            )
            result.append(new)
        if changed:
            result.append(new)
    return result


class TenantConfigUtil:
    """utility to configure tenants."""

    def __init__(
        self,
        kubecfg: KubeCfg,
        dry_run: bool,
    ):
        """Constructs a TenantConfigUtil instance.

        Args:
            kubecfg: KubeCfg instance
        """
        self.kubecfg = kubecfg
        self.visit_config = VisitConfig(stamp=True, rewrite=True)
        self.dry_run = dry_run

    def _apply_tenant(self, tenant: dict[str, str], config: KubeCfgConfig):
        """Handle a single tenant.

        Args:
            tenant: the tenant
        """
        assert self.kubecfg.namespace
        logging.info("Tenant: %s", tenant)
        tenant_yaml = yaml.safe_dump(tenant)
        with tempfile.NamedTemporaryFile(mode="w+", encoding="utf-8") as f:
            f.write(tenant_yaml)
            f.flush()
            if self.dry_run:
                logging.info("Dry run, not applying")
            else:
                config.kubectl.apply(
                    config_file=pathlib.Path(f.name),
                )

    def run(
        self,
    ) -> int:
        """Applies the configuration to the cluster."""

        with self.kubecfg.get_config(self.visit_config) as config:
            logging.info(
                "Applying configuration to namespace '%s' in '%s'",
                self.kubecfg.namespace,
                self.kubecfg.env,
            )
            existing_tenants = config.kubectl.list("Tenant", self.kubecfg.namespace)
            logging.info("Existing tenants: %s", existing_tenants)

            tenants = list(yaml.safe_load_all(config.dump()))
            filtered_tenants = filter_tenants(tenants, existing_tenants)
            for tenant in filtered_tenants:
                self._apply_tenant(tenant, config)
        return 0


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--extra-kubectl-args", action="append", default=[])
    parser.add_argument(
        "--cloud",
    )
    parser.add_argument("--env", default=None, choices=["DEV", "STAGING", "PROD"])
    parser.add_argument("--namespace")
    parser.add_argument(
        "--kube-config-file",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube", "config"),
    )
    parser.add_argument("--dry-run", action="store_true", default=False)
    args = parser.parse_args()
    if not args.cloud:
        args.cloud = "GCP_US_CENTRAL1_DEV"
    if not args.env:
        args.env = "DEV"
    if args.kube_config_file:
        if not pathlib.Path(args.kube_config_file).exists():
            parser.exit(
                1,
                f"kube-config-file '{args.kube_config_file}' does not exist",
            )

    setup_console_logging()

    logging.debug("Args: %s", args)

    kubecfg = KubeCfg(
        ["services/deploy/tenant_config.jsonnet"],
        cloud=args.cloud,
        cluster_wide=False,
        env=args.env,
        namespace=args.namespace,
        base_directory=pathlib.Path.cwd().absolute(),
        kube_config_file=args.kube_config_file,
        deployed_by="tenant-config",
        push_fn=None,
    )
    util = TenantConfigUtil(
        kubecfg,
        dry_run=args.dry_run,
    )

    try:
        sys.exit(util.run())
    except KubeCfgException as e:
        logging.error("%s", e.msg)
        if e.stdout:
            logging.error("%s", e.stdout)
        if e.stderr:
            logging.error("%s", e.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()

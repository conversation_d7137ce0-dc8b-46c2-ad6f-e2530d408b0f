// Qwen <PERSON>den v3.2 14B
//
// v1-1:
// First completion model finetuned from Qwen2.5-Coder-14B.
// Filter prism_eldenv4 threshold set to reject roughly 11.5% bad completions and 2% good completions
// Based otherwise on eldenv4 data and smart ethanol retrieval
// v3:
// Further finetuning of v1 on Vanguard data (2024-11-01 to 2025-01-14 date) with edit events.
// DO NOT DEPLOY TO PROD, NO LICENSE FILTERING.
// v3.1:
// Further finetuning of v1 on Vanguard data (2024-11-01 to 2025-01-14 date) with edit events.
// Includes empty and skip token in the training data, which v3 did not.
// DO NOT DEPLOY TO PROD, NO LICENSE FILTERING.
// v3.2:
// Further finetuning of v1 on Vanguard data (2024-11-01 to 2025-01-14 date) with edit events.
// Includes empty and skip token in the training data, which v3 did not. However, only 15% of the data is empty as opposed to 34% in v3.1.
// **Can be deployed to prod, has license filtering.

// bazel run -c opt //services/deploy:dev_deploy
// bazel run -c opt //services/deploy/completion:qweldenv3_2_14b_kubecfg
// bazel run -c opt //services/deploy:methanol_0416_4_kubecfg
// bazel run -c opt //services/deploy:starethanol_smart_kubecfg
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local completionLib = import 'services/deploy/completion/lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol_smart_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig = completionLib.base.default + {
    name: 'qweldenv3-2-14b',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/completion/qwelden-v3.2-fp8',  // pragma: allowlist secret
    checkpoint_sha256: '727f9432b19d4a65c37d5f610d9a090a0925d3ff05ecbfe4254c53f214347880',  // pragma: allowlist secret
    inference: completionLib.inference.qwelden_v3_stateless,
    model_arch: completionLib.model_arch.qwen25coder + if env != 'PROD' then {
      attn_split_head_mode: 'NO_SPLIT',  // required for use_dynamic_sequence_parallel
    } else {},
    post_processing: completionLib.post_processing.prism_eldenv4_qwelden,
    cache_pool_size: if env != 'PROD' then 26 else 32,  // dynamic sequence parallel requires more free memory
    round_sizes: [128, 256, 512, 1024, 2048, 3072, 4096],  // allow larger rounds for v3-2 to support longer context
    use_dynamic_sequence_parallel: if env != 'PROD' then true else false,
  };

  local inferenceHostConfig = completionLib.inferenceHost(env=env, cloud=cloud).full_prod_full_staging;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    inferTensorPara=inferenceHostConfig.inferTensorPara,
    inferGpu=inferenceHostConfig.inferGpu,
    mtls=mtls,
    replicas=inferenceHostConfig.replicas,
    attentionType=inferenceHostConfig.attentionType,
    allReduceImplementation=inferenceHostConfig.allReduceImplementation,
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.diffRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

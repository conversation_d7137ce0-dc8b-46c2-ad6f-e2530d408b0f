//Qwen <PERSON>den v1 14B
//
// First completion model finetuned from Qwen2.5-Coder-14B.
// Based otherwise on eldenv4 data and smart ethanol retrieval
// bazel run -c opt //services/deploy:dev_deploy
// bazel run -c opt //services/deploy/completion:qweldenv1_14b_kubecfg
// bazel run -c opt //services/deploy:methanol_0416_4_kubecfg
// bazel run -c opt //services/deploy:starethanol_smart_kubecfg
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local completionLib = import 'services/deploy/completion/lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol_smart_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig = completionLib.base.default + {
    name: 'qweldenv1-14b',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/completion/qwelden-v1.0-fp8',  // pragma: allowlist secret
    checkpoint_sha256: '651be22236ee5aa104c4c190eb4e6c9464527f3e825a6afd11ab9d24046dbc15',  // pragma: allowlist secret
    inference: completionLib.inference.qwelden_stateless,
    model_arch: completionLib.model_arch.qwen25coder,
    post_processing: completionLib.post_processing.prism_eldenv4_qwelden + {
      low_quality_filter_config+: {
        default_threshold: 1.0,
      },
    },
    cache_pool_size: 32,
  };

  local inferenceHostConfig = completionLib.inferenceHost(env=env, cloud=cloud).evals;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    inferTensorPara=inferenceHostConfig.inferTensorPara,
    inferGpu=inferenceHostConfig.inferGpu,
    mtls=mtls,
    replicas=inferenceHostConfig.replicas,
    attentionType=inferenceHostConfig.attentionType,
    allReduceImplementation=inferenceHostConfig.allReduceImplementation,
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

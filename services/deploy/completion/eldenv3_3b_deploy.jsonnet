// Elden V3 3B model (for testing)
//
//
// bazel run -c opt //services/deploy/completion:eldenv3_3b_kubecfg
// bazel run -c opt //services/deploy:methanol_0416_4_kubecfg
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local completionLib = import 'services/deploy/completion/lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig = completionLib.base.default + {
    name: 'eldenv3-3b',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/pranay/sc2-3b-mix-bs512-s3k-ffw-fp8',  // pragma: allowlist secret
    checkpoint_sha256: 'f3df81ca561bd43de17eb152b61d6682234b6fd9f3ff00132fcf57fb82429eec',  // pragma: allowlist secret
    inference: completionLib.inference.elden_stateless,
    model_arch: completionLib.model_arch.starcoder2_3b,
    post_processing: completionLib.post_processing.prism_eldenv3 + {
      low_quality_filter_config+: {
        default_threshold: 1.0,
      },
    },
    // Use smaller round sizes + cache pool size than the default.
    round_sizes: [32, 64, 128, 256, 512],
    cache_pool_size: 16,
  };

  local inferenceHostConfig = completionLib.inferenceHost(env=env, cloud=cloud).small;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    inferTensorPara=inferenceHostConfig.inferTensorPara,
    inferGpu=inferenceHostConfig.inferGpu,
    mtls=mtls,
    replicas=inferenceHostConfig.replicas,
    attentionType=inferenceHostConfig.attentionType,
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

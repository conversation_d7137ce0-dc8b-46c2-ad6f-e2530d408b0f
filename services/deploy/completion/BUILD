load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
)
load("//tools/bzl:kubecfg.bzl", "kubecfg")

jsonnet_library(
    name = "lib",
    srcs = ["lib.jsonnet"],
)

kubecfg(
    name = "starcoder2_100m_fp8_kubecfg",
    src = "starcoder2_100m_fp8_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/inference_host/test:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "starcoder2_100m_fp8_sequence_parallel_kubecfg",
    src = "starcoder2_100m_fp8_sequence_parallel_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/inference_host/test:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "rogue_1B_fp8_starethanol6_16_1_proj512_kubecfg",
    src = "rogue_1B_fp8_starethanol6_16_1_proj512_deploy.jsonnet",
    cloud = ["GCP_US_CENTRAL1_DEV"],
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "roguesl_v2_16b_fp8_seth6_16_1_kubecfg",
    src = "roguesl_v2_16b_fp8_seth6_16_1_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "roguesl_v2_16b_starethanol6_16_1_proj512_rec_kubecfg",
    src = "roguesl_v2_16b_starethanol6_16_1_proj512_rec_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "roguesl_v3_16b_starethanol6_16_1_proj512_rec_kubecfg",
    src = "roguesl_v3_16b_starethanol6_16_1_proj512_rec_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "roguesl_v3_16b_starethanol6_16_1_proj512_kubecfg",
    src = "roguesl_v3_16b_starethanol6_16_1_proj512_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "star2sl_16b_starethanol6_16_1_proj512_rec_kubecfg",
    src = "star2sl_16b_starethanol6_16_1_proj512_rec_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "star2v2_15b_kubecfg",
    src = "star2v2_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv3_15b_kubecfg",
    src = "eldenv3_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv7_0_15b_kubecfg",
    src = "eldenv7_0_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv3_3b_kubecfg",
    src = "eldenv3_3b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_15b_kubecfg",
    src = "eldenv4_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_0c_15b_kubecfg",
    src = "eldenv4_0c_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_0d_15b_kubecfg",
    src = "eldenv4_0d_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_0b_15b_kubecfg",
    src = "eldenv4_0b_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_0e_15b_kubecfg",
    src = "eldenv4_0e_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_0f_15b_kubecfg",
    src = "eldenv4_0f_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_3_15b_kubecfg",
    src = "eldenv4_3_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_v4_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_4a_15b_kubecfg",
    src = "eldenv4_4a_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv4_4b_15b_kubecfg",
    src = "eldenv4_4b_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv5_1_15b_kubecfg",
    src = "eldenv5_1_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv6_15b_kubecfg",
    src = "eldenv6_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "eldenv6_1_15b_kubecfg",
    src = "eldenv6_1_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "qweldenv1_14b_kubecfg",
    src = "qweldenv1_14b_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "qweldenv1_1_14b_kubecfg",
    src = "qweldenv1_1_14b_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "qweldenv2_14b_kubecfg",
    src = "qweldenv2_14b_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "qweldenv2_1_14b_kubecfg",
    src = "qweldenv2_1_14b_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "qweldenv3_14b_kubecfg",
    src = "qweldenv3_14b_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "qweldenv3_1_14b_kubecfg",
    src = "qweldenv3_1_14b_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "qweldenv3_2_14b_kubecfg",
    src = "qweldenv3_2_14b_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:methanol_0416_4_lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol_smart_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "deeprogue_33b_seth6_rec_kubecfg",
    src = "deeprogue_33b_seth6_rec_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        "//services/deploy:model_deployment_lib",
        "//services/deploy:starethanol6_16_1_proj512_lib",
        "//services/deploy/configs:config_lib",
    ],
)

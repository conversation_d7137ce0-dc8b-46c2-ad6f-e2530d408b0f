// Qwen <PERSON> v2.1 14B, that used only permissive data to train the model compared to <PERSON>wen <PERSON> v2.0 14B.
//
// RLDB-ed model based on qwelden-v2.1 (Qwen2.5-Coder-14B).
// Based otherwise on eldenv4 data and smart ethanol retrieval.
// bazel run -c opt //services/deploy:dev_deploy
// bazel run -c opt //services/deploy/completion:qweldenv2_1_14b_kubecfg
// bazel run -c opt //services/deploy:methanol_0416_4_kubecfg
// bazel run -c opt //services/deploy:starethanol_smart_kubecfg
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local completionLib = import 'services/deploy/completion/lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol_smart_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig =
    local languages = (import 'services/deploy/configs/languages.jsonnet').presets(env=env).starcoder_expanded;
    {
      name: 'qweldenv2-1-14b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/completion/qwelden-v2.1-fp8',  // pragma: allowlist secret
      checkpoint_sha256: 'e09adc9d1d4895ad7ae5d791407f3c60a8a3cddee33d8315ba315588717c9554',  // pragma: allowlist secret
      model_type: 'INFERENCE',  // aka COMPLETION
      inference: {
        tokenizer_name: 'qwen25coder',
        prompt_formatter_name: 'ender',
        apportionment_config: {
          max_content_len: 6144,
          input_fraction: 4 / 12,
          prefix_fraction: 3 / 4,
          max_path_tokens: 50,
          per_retriever_max_tokens: {
            dense_signature: 1024,
            recency_retriever: 1024,
          },
        },
        prompt_formatter_config: {
          stateless_caching_config: {
            nearby_prefix_token_len: 512,
            quantize_token_len: 64,
            quantize_char_len: 250,
          },
          filter_visible_chunks_by_content: true,
          component_order: ['path', 'prefix', 'retrieval', 'signature', 'nearby_prefix', 'suffix'],
          signature_chunk_origin: 'dense_signature',
        },
        languages: languages,
        max_context_length: 6144,
        extra_stop_tokens: ['<|skip|>', '<|pause|>'],
      },
      model_arch: {
        arch_type: 'QWEN25CODER_FP8',
        num_layers: 48,
        vocab_size: 152064,
        emb_dim: 5120,
        num_heads: 8,
        head_dim: 128,
        attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
        norm_eps: 1e-6,
        rotary_pct: 1.0,  // RoPE parameters
        rotary_theta: 1000000.0,
        rotary_scaling_factor: 1.0,
        max_position_embeddings: 7936,  // Make sure >=`max_context_length`
        num_queries_per_head: 5,
      },
      post_processing: {
        low_quality_filter_config: {
          checkpoint_path: '../prism_eldenv4_model/prism_eldenv4.json',
          feature_extractor_version: 'feature_extractor_v2',
          default_threshold: 0.89,
        },
      },
      round_sizes: [128, 256, 512, 1024, 2048],
      use_sequence_parallel: true,
    };
  local inferenceHostConfig = completionLib.inferenceHost(env=env, cloud=cloud).evals;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    inferTensorPara=inferenceHostConfig.inferTensorPara,
    inferGpu=inferenceHostConfig.inferGpu,
    mtls=mtls,
    replicas=inferenceHostConfig.replicas,
    attentionType=inferenceHostConfig.attentionType,
    allReduceImplementation=inferenceHostConfig.allReduceImplementation,
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

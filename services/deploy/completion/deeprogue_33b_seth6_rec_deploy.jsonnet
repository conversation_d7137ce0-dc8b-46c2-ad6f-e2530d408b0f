// A rogue code completion model based on the DeepSeek-Coder 33B model.
//
// Improve code completion quality by fine-tuning a DeepSeek-Coder model to better use retrieval
//
// Model Card: https://www.notion.so/Q1-2024-DeepRogue-SL-farpref-479a750a43d2409181b81b34b2909635
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';

function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig =
    local languages = (import 'services/deploy/configs/languages.jsonnet').presets(env=env).starcoder +
                      if env != 'PROD' then [
                        'Cuda',
                        'Jsonnet',
                        'Markdown',
                        'YAML',
                        'XML',
                        'Protobuf',
                        'Plain Text',
                        'SQL',
                        'JSON',
                        'Dart',
                        'CSS',
                        'Clojure',
                        'Visual Basic',
                        'TeX',
                      ] else [];
    {
      name: 'deeprogue-33b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/roguesl/ds_eth6_4m_morelang3_fpref1kretnpref0k5suf0k5_aug05rdrop03_fp8',  // pragma: allowlist secret
      checkpoint_sha256: '07244c6a934eff0bc35c2fb5599cdc350e0129497e06df464b680f74b42cfad2',
      model_type: 'INFERENCE',  // aka COMPLETION
      inference: {
        tokenizer_name: 'deepseek_coder_base',
        prompt_formatter_name: 'rogue_sl',
        apportionment_config: {
          max_content_len: 6144,
          input_fraction: 4 / 12,
          prefix_fraction: 3 / 4,
          max_path_tokens: 50,
          per_retriever_max_tokens: {
            recency_retriever: 1280,
          },
        },
        prompt_formatter_config: {
          stateless_caching_config: {
            nearby_prefix_token_len: 512,
            quantize_token_len: 64,
            quantize_char_len: 250,
          },
          component_order: ['path', 'prefix', 'retrieval', 'nearby_prefix', 'suffix'],
          use_far_prefix_token: true,
        },
        languages: languages,
        max_context_length: 6144,
        extra_stop_tokens: ['<|skip|>', '<|pause|>'],
      },
      model_arch: {
        arch_type: 'LLAMA_FP8',
        emb_dim: 7168,
        num_layers: 62,
        num_heads: 8,
        num_queries_per_head: 7,
        head_dim: 128,
        attn_split_head_mode: 'KV_HEADS',
        rotary_theta: 100000.0,
        rotary_scaling_factor: 4.0,
        max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
        rotary_pct: 1.0,
        vocab_size: 32256,
        mlp_dim_divisible_by: 256,
        ffn_dim_multiplier: 1.0,
        norm_eps: 1e-05,
      },
      round_sizes: [32, 128, 256, 512, 1024, 2048],
    };
  local name = 'deeprogue-33b';
  local replicas =
    if env != 'PROD' then 1
    else {
      GCP_US_CENTRAL1_PROD: 6,
      GCP_EU_WEST4_PROD: 2,
    }[cloud];
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=1,
    inferGpu='large',
    mtls=mtls,
    replicas=replicas,
    attentionType='MULTI_REQUEST_FLASH',
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

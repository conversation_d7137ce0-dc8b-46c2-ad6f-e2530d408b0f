// StarCoder2 100M model for testing
//
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local name = 'starcoder2-100m-fp8-sp';
  local modelConfig =
    local languages = (import 'services/deploy/configs/languages.jsonnet').presets(env=env).starcoder + [
      'Cuda',
      'Jsonnet',
      'Markdown',
      'YAML',
      'XML',
      'Protobuf',
      'Plain Text',
      'SQL',
      'JSON',
      'Dart',
      'CSS',
      'Clojure',
      'Visual Basic',
      'TeX',
      'Strato',
    ];
    {
      name: name,
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starcoder2/fastforward/starcoder2-100m-fp8',  // pragma: allowlist secret
      checkpoint_sha256: 'cb1fd229691e0c5ddc64f59524d37d57f8fdc6311bdd26685bb7a5b62363547e',  // pragma: allowlist secret
      model_type: 'INFERENCE',  // aka COMPLETION
      inference: {
        tokenizer_name: 'starcoder2',
        prompt_formatter_name: 'ender',
        apportionment_config: {
          max_content_len: 4000,
          input_fraction: 4 / 12,
          prefix_fraction: 3 / 4,
          max_path_tokens: 50,
          per_retriever_max_tokens: {
            dense_signature: 1024,
            recency_retriever: 1024,
          },
        },
        prompt_formatter_config: {
          stateless_caching_config: {
            nearby_prefix_token_len: 512,
            quantize_token_len: 64,
            quantize_char_len: 250,
          },
          component_order: ['path', 'prefix', 'retrieval', 'signature', 'nearby_prefix', 'suffix'],
          signature_chunk_origin: 'dense_signature',
        },
        languages: languages,
        max_context_length: 4000,
        extra_stop_tokens: ['<|skip|>', '<|pause|>'],
      },
      model_arch: {
        arch_type: 'STARCODER2_FP8',
        num_layers: 12,
        vocab_size: 49176,
        emb_dim: 1024,
        num_heads: 8,
        head_dim: 128,
        attn_split_head_mode: 'Q_PER_HEADS',
        norm_eps: 1e-5,
        rotary_pct: 1.0,  // RoPE parameters
        rotary_theta: 1000000.0,
        rotary_scaling_factor: 1.0,
        max_position_embeddings: 4 * 1024,  // Make sure >=`max_context_length`
        num_queries_per_head: 8,
      },
      post_processing: {
        low_quality_filter_config: {
          checkpoint_path: 'services/completion_host/single_model_server/prism_models/prism_eldenv3.json',
          feature_extractor_version: 'feature_extractor_v1',
          default_threshold: 0.8,
        },
      },
      round_sizes: [64, 128, 256, 512, 1024],
      use_sequence_parallel: true,
    };

  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=2,
    inferGpu=if env == 'DEV' then 'small' else 'large',
    mtls=mtls,
    replicas=1,
    attentionType='MULTI_REQUEST_FLASH',
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

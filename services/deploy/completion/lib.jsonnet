// Contains common functions and configurations for completion models.
//
// NOTE: A modification here will affect many completion models.
// For reproducibility, we aim to keep each model's behavior as fixed as possible.
// Each entry here should be kept as immutable for all time, unless it
// doesn't affect model behavior (e.g. inference changes, num replicas, etc.).
// Even then, be *very* careful updating a config here, and test thoroughly.

local languages = import 'services/deploy/configs/languages.jsonnet';
{
  // Configs for modelConfig base settings
  base: {
    default: {
      model_type: 'INFERENCE',  // aka COMPLETION
      round_sizes: [128, 256, 512, 1024, 2048],
      use_sequence_parallel: true,
      cache_pool_size: 64,
    },
  },
  // Configs for the modelConfig.inference field
  inference: {
    elden_stateless: {
      tokenizer_name: 'starcoder2',
      prompt_formatter_name: 'ender',
      apportionment_config: {
        max_content_len: 6144,
        input_fraction: 4 / 12,
        prefix_fraction: 3 / 4,
        max_path_tokens: 50,
        per_retriever_max_tokens: {
          dense_signature: 1024,
          recency_retriever: 1024,
        },
      },
      prompt_formatter_config: {
        stateless_caching_config: {
          nearby_prefix_token_len: 512,
          quantize_token_len: 64,
          quantize_char_len: 250,
        },
        filter_visible_chunks_by_content: true,
        component_order: ['path', 'prefix', 'retrieval', 'signature', 'nearby_prefix', 'suffix'],
        signature_chunk_origin: 'dense_signature',
      },
      // TODO(jeff): remove the function call, and make languages a pure json.
      languages: languages.presets(env='PROD').starcoder_expanded,
      max_context_length: 6144,
      extra_stop_tokens: ['<|skip|>', '<|pause|>'],
    },

    elden_stateful: self.elden_stateless + {
      prompt_formatter_config+: {
        stateful_caching_config: {
          enabled: true,
        },
      },
    },

    qwelden_stateless: self.elden_stateless + {
      tokenizer_name: 'qwen25coder',
    },

    qwelden_v3_stateless: self.elden_stateless + {
      tokenizer_name: 'qwen25coder',
      max_context_length: 8192,  // context length includes target, so it is = 8352 - 256 (prompt without research target max length) + 96 (service target max length)
      apportionment_config+: {
        max_content_len: 8192,
        input_fraction: 4 / 16,
        per_retriever_max_tokens+: {
          dense_signature: 1024,
          recency_retriever: 1024,
          diff_retriever: 2048,
        },
      },

      prompt_formatter_config+: {
        component_order: [
          'path',
          'prefix',
          'retrieval',
          'signature',
          'diff',
          'nearby_prefix',
          'suffix',
        ],
      },
    },

  },
  // Configs for the modelConfig.model_arch field
  model_arch: {
    starcoder2: {
      arch_type: 'STARCODER2_FP8',
      num_layers: 40,
      vocab_size: 49176,
      emb_dim: 6144,
      num_heads: 48,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 12,
    },
    starcoder2_3b: {
      arch_type: 'STARCODER2_FP8',
      num_layers: 30,
      vocab_size: 49176,
      emb_dim: 3072,
      num_heads: 24,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 1000000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 12,
    },
    qwen25coder: {
      arch_type: 'QWEN25CODER_FP8',
      num_layers: 48,
      vocab_size: 152064,
      emb_dim: 5120,
      num_heads: 8,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-6,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 1000000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 7936,  // Make sure >=`max_context_length`
      num_queries_per_head: 5,
    },
  },
  // Configs for the modelConfig.post_processing field
  post_processing: {
    prism_eldenv3: {
      low_quality_filter_config: {
        checkpoint_path: 'services/completion_host/single_model_server/prism_models/prism_eldenv3.json',
        feature_extractor_version: 'feature_extractor_v1',
        default_threshold: 0.8,
      },
    },
    prism_eldenv4: {
      low_quality_filter_config: {
        checkpoint_path: '../prism_eldenv4_model/prism_eldenv4.json',
        feature_extractor_version: 'feature_extractor_v2',
        default_threshold: 0.875,
      },
    },
    prism_eldenv4_qwelden: self.prism_eldenv4 + {
      low_quality_filter_config+: {
        default_threshold: 0.89,
      },
    },
  },
  // Config for inference not already in modelConfig.
  inferenceHost: function(env, cloud)
    local default = {
      inferGpu: 'large',
      attentionType: 'MULTI_REQUEST_FLASH_V3',
      allReduceImplementation: 'FASTFORWARD',
    };
    local min_deploy = default + {
      replicas: {
        GCP_US_CENTRAL1_PROD: 1,
        GCP_EU_WEST4_PROD: 0,
        GCP_US_CENTRAL1_GSC_PROD: 0,
        GCP_US_CENTRAL1_DEV: 1,
      }[cloud],
      inferTensorPara: 1,
    };
    local full_staging = default + {
      replicas: {
        GCP_US_CENTRAL1_PROD: 2,
        GCP_EU_WEST4_PROD: 0,
        GCP_US_CENTRAL1_GSC_PROD: 1,
        GCP_US_CENTRAL1_DEV: 1,
      }[cloud],
      inferTensorPara: 4,
    };
    local full_prod = default + {
      replicas: {
        GCP_US_CENTRAL1_PROD: 34,
        GCP_EU_WEST4_PROD: 0,
        GCP_US_CENTRAL1_GSC_PROD: 18,
        GCP_US_CENTRAL1_DEV: 1,
      }[cloud],
      inferTensorPara: 4,
    };
    {
      // For testing purposes only, e.g. end to end tests.
      small: {
        replicas: {
          GCP_US_CENTRAL1_PROD: 1,
          GCP_EU_WEST4_PROD: 0,
          GCP_US_CENTRAL1_GSC_PROD: 0,
          GCP_US_CENTRAL1_DEV: 1,
        }[cloud],
        inferTensorPara: 1,
        inferGpu: if cloud == 'GCP_US_CENTRAL1_DEV' then 'small' else 'large',
        attentionType: 'MULTI_REQUEST_FLASH',
        allReduceImplementation: 'FASTFORWARD',
      },

      // For evaluation purposes only (e.g. dogfood evals).
      // Do NOT deploy to prod. Do NOT set as default in staging.
      evals: min_deploy,
      // Full prod deployment (if deployed), but minimum staging deployment.
      // Do NOT set as default in staging.
      full_prod_min_staging: if env != 'PROD' then min_deploy else full_prod,
      // Full prod deployment (if deployed), and full staging deployment.
      full_prod_full_staging:
        if env == 'DEV' then min_deploy
        else if env != 'PROD' then full_staging
        else full_prod,
      // Minimum prod deployment (if deployed), and minimum staging deployment.
      // This is equivalent to evals, but different in connotation.
      // Do NOT set as default in staging.
      min_prod_min_staging: min_deploy,
    },
}

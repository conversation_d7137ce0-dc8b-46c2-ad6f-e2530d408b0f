// Elden 16B model -- V7 will be a series of RLHF-ed models.
//
// This Elden V7.0 uses RLHF to improve the Elden V3 model.
// The training job link is at https://determined.gcp-us1.r.augmentcode.com/det/experiments/371/trials/371/overview
// and the source FBW checkpoint is at /mnt/efs/augment/checkpoints/dxy/sc2-elden-rlhf/IPO-OfflineS0-FV3-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0-S70-mp4 (as a reference)
//
// bazel run -c opt //services/deploy:dev_deploy
// bazel run -c opt //services/deploy/completion:eldenv7_0_15b_kubecfg
// bazel run -c opt //services/deploy:methanol_0416_4_kubecfg
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local completionLib = import 'services/deploy/completion/lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig = completionLib.base.default + {
    name: 'eldenv7-0-15b',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/completion/elden-v7.0-fp8',  // pragma: allowlist secret
    checkpoint_sha256: '6dcad35e2def0cfe658f67c307c73613ce552325219badca7027296f167f7642',  // pragma: allowlist secret
    inference: completionLib.inference.elden_stateless,
    model_arch: {
      arch_type: 'STARCODER2_FP8',
      num_layers: 40,
      vocab_size: 49280,
      emb_dim: 6144,
      num_heads: 48,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 12,
    },
    post_processing: completionLib.post_processing.prism_eldenv3,
  };


  local inferenceHostConfig = completionLib.inferenceHost(env=env, cloud=cloud).evals;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    inferTensorPara=inferenceHostConfig.inferTensorPara,
    inferGpu=inferenceHostConfig.inferGpu,
    mtls=mtls,
    replicas=inferenceHostConfig.replicas,
    attentionType=inferenceHostConfig.attentionType,
    allReduceImplementation=inferenceHostConfig.allReduceImplementation,
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

// Elden 16B model (V5.1)
//
// A better model trained with massive data and better training.
// Its BF16 version with 8K sequence length achieves 68.08% EM and 95.22% token accuracy on CCEval and 53.29% EM-1-line and 83.32% token accuracy on Hindsight June.
// Compared to Elden V3 or Elden V4, the difference is:
// - By persistently improving our data pipeline and I generated more retrieval-augmented samples for training (5x more but not all is used for training).
// - Used simple elden prompt formatter not elden prompt formatter
// - Slightly increased the prompt length to 7680 tokens -- consistent with training.
// - It significantly improved the quality of the model on in distribution data (FIMEval), but CCEval and Hindsight are out of distribution and did not get as large gain as FIMEval.
//
// source checkpoint is at SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0
//
// bazel run -c opt //services/deploy:dev_deploy
// bazel run -c opt //services/deploy/completion:eldenv5_1_15b_kubecfg
// bazel run -c opt //services/deploy:methanol_0416_4_kubecfg
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local completionLib = import 'services/deploy/completion/lib.jsonnet';
local signatureEmbedderConfig = import 'services/deploy/methanol_0416.4_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lineEmbedderConfig = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig =
    local languages = (import 'services/deploy/configs/languages.jsonnet').presets(env=env).starcoder_expanded;
    completionLib.base.default + {
      name: 'eldenv5-1-15b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/completion/elden-v5.1-fp8',  // pragma: allowlist secret
      checkpoint_sha256: '9fa9c4e2acfd0fddec87cf5eeca065687092b317ad889c84a7cd725d7f30d6b5',  // pragma: allowlist secret
      inference: {
        tokenizer_name: 'starcoder2',
        prompt_formatter_name: 'simple_elden',
        apportionment_config: null,
        prompt_formatter_config: {
          max_prompt_length: 6048,  // 7680 -> 6048
          token_config: {
            path_len: 50,
            prefix_len: 1024,
            suffix_len: 512,
            retrieval_len: 6048,  // 7680 -> 6048
          },
          per_retriever_max_tokens: {
            signature_retriever: 1024,
            recency_retriever: 1024,
            dense_retriever: 6048 - 1024,  // 7680 -> 6048
          },
          version: 'v2.0',
        },
        languages: languages,
        max_context_length: 6048 + 96,  // 7680 -> 6048
        extra_stop_tokens: ['<|skip|>', '<|pause|>'],
      },
      model_arch: completionLib.model_arch.starcoder2,
      post_processing: {
        // TODO: train a new low quality filter for this model.
        // low_quality_filter_config: {
        //   checkpoint_path: 'services/completion_host/single_model_server/prism_models/prism_eldenv3.json',
        //   feature_extractor_version: 'feature_extractor_v1',
        //   default_threshold: 0.8,
        // },
      },
    };

  local inferenceHostConfig = completionLib.inferenceHost(env=env, cloud=cloud).evals;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    inferTensorPara=inferenceHostConfig.inferTensorPara,
    inferGpu=inferenceHostConfig.inferGpu,
    mtls=mtls,
    replicas=inferenceHostConfig.replicas,
    attentionType=inferenceHostConfig.attentionType,
    allReduceImplementation=inferenceHostConfig.allReduceImplementation,
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(lineEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='line'),
      modelDeployment.denseRetrievalConfig(signatureEmbedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='sig'),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

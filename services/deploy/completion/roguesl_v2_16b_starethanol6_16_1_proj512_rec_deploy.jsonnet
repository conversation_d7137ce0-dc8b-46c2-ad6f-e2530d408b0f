local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig =
    local lang_presets = (import 'services/deploy/configs/languages.jsonnet').presets(env=env);
    {
      name: 'roguesl-v2-16b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/roguesl/16b_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/checkpoint_fp8.pth_v2',  // pragma: allowlist secret
      checkpoint_sha256: '746f1ab92e9faa8e742084e5b1b35a92a86395f130c08f656731223bf35aef8a',  // pragma: allowlist secret
      model_type: 'INFERENCE',  // aka COMPLETION
      inference: {
        tokenizer_name: 'rogue',
        prompt_formatter_name: 'rogue_sl',
        apportionment_config: {
          max_content_len: 4 * 1024 + 512,
          input_fraction: 4 / 9,
          prefix_fraction: 3 / 4,
          max_path_tokens: 50,
          per_retriever_max_tokens: {
            recency_retriever: 960,
          },
        },
        prompt_formatter_config: {
          stateless_caching_config: {
            nearby_prefix_token_len: 250,
            quantize_token_len: 50,
            quantize_char_len: 200,
          },
          component_order: ['path', 'prefix', 'suffix', 'retrieval', 'nearby_prefix'],
          use_far_prefix_token: true,
        },
        languages: lang_presets.starcoder,
        max_context_length: 4 * 1024 + 512,
        extra_stop_tokens: ['<|skip|>', '<|pause|>'],
      },
      model_arch: {
        arch_type: 'STARCODER_FP8',
        num_layers: 40,
        vocab_size: 51200,
        emb_dim: 6144,
        num_heads: 48,
        head_dim: 128,
      },
      post_processing: {
        low_quality_filter_config: {
          checkpoint_path: 'services/completion_host/single_model_server/prism_models/prism_roguesl_farpref_16B.json',
          feature_extractor_version: 'feature_extractor_v1',
          default_threshold: 0.9,
        },
      },
    };

  local name = 'roguesl-v2-16b-seth616-rec';
  local replicas =
    if env != 'PROD' then 1
    else {
      GCP_US_CENTRAL1_PROD: 6,
      GCP_EU_WEST4_PROD: 2,
    }[cloud];
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferGpu='large',
    mtls=mtls,
    replicas=replicas
  );
  local completion() = modelDeployment.completionHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalCollectorConfig={
      enabled: true,
      modified_chunks_filter_enabled: true,
    },
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      modelDeployment.recencyRetrievalConfig(
        env=env,
        namespace=namespace,
        namespace_config=namespace_config
      ),
    ],
    inferenceMtls=mtls,
    skip_token_str='<|skip|>',
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + completion()
  else if filter == 'inference' then
    inference()
  else if filter == 'completion' then
    completion()
  else
    error ('unknown filter: %s' % filter)

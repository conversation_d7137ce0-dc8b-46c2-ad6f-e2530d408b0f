local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local chatanolEmbedderConfig = import 'services/deploy/chatanol-qwen-v1-1_fp8_config.jsonnet';

function(cloud)
  chatanolEmbedderConfig + {
    // NOTE(arun): Make sure you update the transformation key name if you change the
    // system prompt (or model).
    transformationKey: 'dr-%s-commit-summary-v1' % (std.asciiLower(chatanolEmbedderConfig.modelConfig.name)),
    modelConfig+: {
      name: '%s-commits' % (chatanolEmbedderConfig.modelConfig.name),
      // Use commit_summary chunker to only process commit documents
      chunking+: {
        name: 'commit_summary',
        config: {
          project_id: cloudInfo[cloud].projectId,
          region: cloudInfo[cloud].region,
          // NOTE(arun, jiayi): Currently Gemini 2.0 Flash is the cheapest model that
          // seems to work well. Flash Lite made a lot of errors.
          model_name: 'gemini-2.0-flash-001',
          temperature: 0.0,
          max_output_tokens: 512,
        },
      },
    },
  }

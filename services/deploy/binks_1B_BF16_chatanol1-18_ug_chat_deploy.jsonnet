local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local embedderConfig = import 'services/deploy/chatanol1-18-hybrid_config.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config, filter=null)
  local name = 'binks-1B-BF16-chatanol1-18-ug-chat';
  local modelConfig = {
    name: 'binks-v1-1B-FB16',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/deepseek/fastforward/coder-1.3b-instruct',  // pragma: allowlist secret
    checkpoint_sha256: 'cb3cfb2da962a40a6619d8d657a2970445aee121d31f2c69923f46b7c4f7cd6c',  // pragma: allowlist secret
    model_type: 'CHAT',
    inference: {
      tokenizer_name: 'deepseek_coder_instruct',
      prompt_formatter_name: 'binks_legacy',
      token_apportionment: {
        prefix_len: 1536,
        suffix_len: 1024,
        path_len: 256,
        message_len: 512,
        selected_code_len: 4096,
        chat_history_len: 4096,
        max_prompt_len: 16 * 1024 - 4096,
      },
      max_context_length: 16 * 1024,
      max_output_length: 4096,
    },
    model_arch: {
      arch_type: 'LLAMA',
      num_layers: 24,
      vocab_size: 32256,
      emb_dim: 2048,
      num_heads: 16,
      head_dim: 128,
      num_queries_per_head: 1,
      mlp_dim_divisible_by: 128,
      ffn_dim_multiplier: 1.0,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-6,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 4.0,
      max_position_embeddings: 16 * 1024,  // Make sure >=`max_context_length`
    },
    cache_pool_size: 2,  // The pool size for the KV cache.
    // This needs to match services/chat_host/server/chat_handler.py
    sampling_params: {},
    preference_sampling_params: {},
  };
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=1,
    inferGpu='small',
    mtls=mtls,
  );
  local chat() = modelDeployment.chatDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=1,  // it shouldn't be the default model, but should be above 3rd party models
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      modelDeployment.userGuidedRetrievalConfig(env, namespace, namespace_config),
      modelDeployment.docsetRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
    ],
    inferenceMtls=mtls,
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + chat()
  else if filter == 'inference' then
    inference()
  else if filter == 'chat' then
    chat()
  else
    error ('unknown filter: %s' % filter)

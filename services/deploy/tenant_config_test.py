from tenant_config import filter_tenants


def create_tenant(name, namespace, cloud, tenant_id, tier, config):
    return {
        "metadata": {"name": name, "namespace": namespace},
        "spec": {
            "cloud": cloud,
            "tenant_id": tenant_id,
            "shard_namespace": namespace,
            "config": config,
            "tier": tier,
        },
    }


def test_filter_tenants_new_tenant():
    new_tenants = [
        create_tenant(
            "tenant1",
            "dev-dirk",
            "GCP_US_CENTRAL1_DEV",
            "653f35a23f657286f9f74d1f0094d6ab",
            "PROFESSIONAL",
            {
                "default_support_tenant": False,
                "multi_tenant_allowed": True,
                "support_access_control": False,
                "support_tenant": True,
            },
        )
    ]
    existing_tenants = []

    result = filter_tenants(new_tenants, existing_tenants)
    assert len(result) == 1, f"Expected 1 tenant, got {len(result)}"
    assert (
        result[0]["metadata"]["name"] == "tenant1"
    ), f"Expected tenant name 'tenant1', got {result[0]['metadata']['name']}"
    assert (
        result[0]["spec"]["tier"] == "PROFESSIONAL"
    ), f"Expected tier 'PROFESSIONAL', got {result[0]['spec']['tier']}"


def test_filter_tenants_existing_unchanged():
    tenant = create_tenant(
        "tenant1",
        "dev-dirk",
        "GCP_US_CENTRAL1_DEV",
        "653f35a23f657286f9f74d1f0094d6ab",
        "ENTERPRISE",
        {
            "default_support_tenant": False,
            "multi_tenant_allowed": True,
            "support_access_control": False,
            "support_tenant": True,
        },
    )
    new_tenants = [tenant]
    existing_tenants = [tenant]

    result = filter_tenants(new_tenants, existing_tenants)
    assert len(result) == 0, f"Expected 0 tenants (unchanged), got {len(result)}"


def test_filter_tenants_tier_changed():
    new_tenant = create_tenant(
        "tenant1",
        "dev-dirk",
        "GCP_US_CENTRAL1_DEV",
        "653f35a23f657286f9f74d1f0094d6ab",
        "PROFESSIONAL",
        {
            "default_support_tenant": False,
            "multi_tenant_allowed": True,
            "support_access_control": False,
            "support_tenant": True,
        },
    )
    existing_tenant = create_tenant(
        "tenant1",
        "dev-dirk",
        "GCP_US_CENTRAL1_DEV",
        "653f35a23f657286f9f74d1f0094d6ab",
        "COMMUNITY",
        {
            "default_support_tenant": False,
            "multi_tenant_allowed": True,
            "support_access_control": False,
            "support_tenant": True,
        },
    )

    result = filter_tenants([new_tenant], [existing_tenant])
    assert len(result) == 1, f"Expected 1 tenant (tier changed), got {len(result)}"
    assert (
        result[0]["spec"]["tier"] == "PROFESSIONAL"
    ), f"Expected tier upgraded to 'PROFESSIONAL', got {result[0]['spec']['tier']}"


def test_filter_tenants_deleted():
    new_tenants = [
        create_tenant(
            "tenant1",
            "dev-dirk",
            "GCP_US_CENTRAL1_DEV",
            "653f35a23f657286f9f74d1f0094d6ab",
            "ENTERPRISE",
            {
                "default_support_tenant": False,
                "multi_tenant_allowed": True,
                "support_access_control": False,
                "support_tenant": True,
            },
        )
    ]
    new_tenants[0]["spec"]["deleted_at"] = "2023-05-01T00:00:00Z"
    existing_tenants = []

    result = filter_tenants(new_tenants, existing_tenants)
    assert len(result) == 0, f"Expected 0 tenants (deleted), got {len(result)}"


def test_filter_tenants_multiple():
    new_tenants = [
        create_tenant(
            "tenant1",
            "dev-dirk",
            "GCP_US_CENTRAL1_DEV",
            "653f35a23f657286f9f74d1f0094d6ab",
            "PROFESSIONAL",
            {
                "default_support_tenant": False,
                "multi_tenant_allowed": True,
                "support_access_control": False,
                "support_tenant": True,
            },
        ),
        create_tenant(
            "tenant2",
            "dev-dirk",
            "GCP_US_CENTRAL1_DEV",
            "753f35a23f657286f9f74d1f0094d6ac",
            "COMMUNITY",
            {
                "default_support_tenant": True,
                "multi_tenant_allowed": False,
                "support_access_control": True,
                "support_tenant": False,
            },
        ),
        create_tenant(
            "tenant3",
            "dev-dirk",
            "GCP_US_CENTRAL1_DEV",
            "853f35a23f657286f9f74d1f0094d6ad",
            "ENTERPRISE",
            {
                "default_support_tenant": False,
                "multi_tenant_allowed": True,
                "support_access_control": True,
                "support_tenant": True,
            },
        ),
    ]
    existing_tenants = [
        create_tenant(
            "tenant1",
            "dev-dirk",
            "GCP_US_CENTRAL1_DEV",
            "653f35a23f657286f9f74d1f0094d6ab",
            "PROFESSIONAL",
            {
                "default_support_tenant": False,
                "multi_tenant_allowed": True,
                "support_access_control": False,
                "support_tenant": True,
            },
        ),
        create_tenant(
            "tenant3",
            "dev-dirk",
            "GCP_US_CENTRAL1_DEV",
            "853f35a23f657286f9f74d1f0094d6ad",
            "COMMUNITY",
            {
                "default_support_tenant": False,
                "multi_tenant_allowed": True,
                "support_access_control": False,
                "support_tenant": True,
            },
        ),
    ]

    result = filter_tenants(new_tenants, existing_tenants)
    assert len(result) == 2, f"Expected 2 tenants (1 new, 1 changed), got {len(result)}"
    assert any(
        t["metadata"]["name"] == "tenant2" for t in result
    ), "Expected tenant2 to be included in results"
    assert any(
        t["metadata"]["name"] == "tenant3" and t["spec"]["tier"] == "ENTERPRISE"
        for t in result
    ), "Expected tenant3 with updated config and tier in results"

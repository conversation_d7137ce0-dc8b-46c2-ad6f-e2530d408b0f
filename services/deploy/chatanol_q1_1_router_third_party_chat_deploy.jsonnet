/**
  Defines chat models that use a Router model to drive retrieval instead of standard multi-retriever
 */
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = (import 'services/deploy/chatanol-qwen-v1-1_fp8_config.jsonnet') + {
  modelConfig+: {
    max_retrieval_results: 128,
  },
};
local routerConfig = import 'services/deploy/pleasehold_7b_v1_config.jsonnet';
local postprocessConfig = import 'services/deploy/sentry_v1_config.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config)
  local name = 'chatanol-q1-1-router-third-party';
  local chatName = '%s-chat' % name;
  local modelConfig = {
    name: name,
    model_type: 'CHAT',
  };
  local thirdPartyInferenceMultiConfig = {
    'claude-sonnet-3-5-16k-v11-4r-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v11-1',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-16k-v11-4r-direct-chat': {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-5-sonnet-20241022',
      prompt_formatter_name: 'binks-claude-v11-1',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-16k-v14-3r-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v14',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-16k-v14-3r-direct-chat': {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-7-sonnet-20250219',
      prompt_formatter_name: 'binks-claude-v14',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-16k-v15-1r-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v15',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-16k-v15-1r-direct-chat': {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-7-sonnet-20250219',
      prompt_formatter_name: 'binks-claude-v15',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-16k-v16r-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-7-sonnet@20250219',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v16',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-16k-v16r-direct-chat': {
      client_type: 'anthropic_direct',
      model_name: 'claude-3-7-sonnet-20250219',
      prompt_formatter_name: 'binks-claude-v16',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k for prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 32,  // 32k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 9,
        commit_message_len: 1024 * 3,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 12,  // 12k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'gemini-2-flash-16k-v1r-chat': {
      client_type: 'vertexai',
      model_name: 'gemini-2.0-flash-exp',
      prompt_formatter_name: 'binks-gemini-2.0-flash-exp',
      temperature: 0.0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 2,
        suffix_len: 1024 * 2,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 4,
        retrieval_len_per_each_user_guided_file: 2000,
        retrieval_len_for_user_guided: 3000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      },
    },
    'gemini-2-flash-128k-v1r-chat': {
      client_type: 'vertexai',
      model_name: 'gemini-2.0-flash-exp',
      prompt_formatter_name: 'binks-gemini-2.0-flash-exp',
      temperature: 0.0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 2000,
        retrieval_len_for_user_guided: 3000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
      },
    },
  };
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  modelDeployment.chatDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=chatName,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.routerConfig(
        routerConfig,
        cloud,
        env,
        namespace,
        namespace_config,
        code_retriever_config=modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
        user_guided_retriever_config=modelDeployment.userGuidedRetrievalConfig(env, namespace, namespace_config),
        docset_retriever_config=modelDeployment.docsetRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      ),
    ],
    inferenceMtls=mtls,
    thirdPartyInferenceMultiConfig=thirdPartyInferenceMultiConfig,
    postprocessConfig=modelDeployment.postprocessConfig(
      postprocessConfig,
      cloud,
      env,
      namespace,
      namespace_config,
    ),
  )

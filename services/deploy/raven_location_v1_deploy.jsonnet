local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = import 'services/deploy/raven_location_v1_config.jsonnet';
function(env, namespace, cloud, namespace_config, filter=null)
  local embedder() = modelDeployment.embedder(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=embedderConfig,
  );
  // NOTE(arun): We currently re-use the StarEthanol *index* to retrieve chunks, so we
  // don't need to deploy an indexer or key.
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace) : 'Indexer and embedder together is only supported in DEV';
    embedder()
  else if filter == 'embedder' then
    embedder()
  else if filter == 'indexer' then
    []
  else
    error ('unknown filter: %s' % filter)

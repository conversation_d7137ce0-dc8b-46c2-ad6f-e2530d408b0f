local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local flags = import 'deploy/tenants/tenant_flags.jsonnet';
local tenants = import 'deploy/tenants/tenants_lib.jsonnet';
function(env, namespace, cloud, namespace_config)
  // Tenant configs are only deployed to the lead cluster's central namespace,
  // which is where tenant-watcher also is, since tenant-watcher is a global
  // service, as opposed to a central one.
  assert cloudInfo.isLeadCluster(cloud) && cloudInfo.isCentralNamespace(env, namespace, cloud) : 'namespace %s in %s is not a central namespace' % [namespace, cloud];

  local relevantTenants = tenants.relevantTenants(env, namespace, cloud, namespace_config);
  local moreTenants = if env == 'PROD' then
    local prodTenants = import 'deploy/tenants/more_prod_tenants.jsonnet';
    tenants.processTenants(prodTenants)
  else [];
  local createTenantConfig = function(t)
    local tenantName = t.name;
    local tenantId = t.tenantId;
    local tenantCloud = t.cloud;
    local tenantFlags = tenants.getTenantFlags(t, namespace_config);
    local tenantNamespace = t.namespace;
    local centralNamespace = namespace;

    local tenantConfig = {
      apiVersion: 'eng.augmentcode.com/v1',
      kind: 'Tenant',
      metadata: {
        name: tenantName,
        namespace: centralNamespace,
        labels: {
          // an app label that is unique for each tenant
          app: 'tenant-config-%s' % tenantName,
        },
      },
      spec: {
        shard_namespace: tenantNamespace,
        tenant_id: tenantId,
        cloud: tenantCloud,
        domain: std.get(t, 'domain'),
        tier: std.get(t, 'tier'),
        [if std.objectHas(t, 'username_domains') then 'username_domains']: t.username_domains,
        [if std.objectHas(t, 'email_address_domains') then 'email_address_domains']: t.email_address_domains,
        [if std.objectHas(t, 'allowed_identity_providers') then 'allowed_identity_providers']: t.allowed_identity_providers,
        [if std.objectHas(t, 'deleted_at') then 'deleted_at']: t.deleted_at,
        [if std.objectHas(t, 'other_namespace') then 'other_namespace']: t.other_namespace,
        [if std.objectHas(t, 'encryptionKeyName') then 'encryption_key_name']: t.encryptionKeyName,
        [if std.objectHas(t, 'encryptionKeyTTL') then 'encryption_key_ttl']: t.encryptionKeyTTL,
        // remap the flags so that they are not camelCase, but snake_case (or
        // rather) what the name of the flag is.
        config: std.foldr(function(key, o)
                            if !std.objectHas(tenantFlags, key + '_') then o else
                              local newKey = flags[key + '_'].name;
                              o + {
                                [newKey]: tenantFlags[key],
                              },
                          std.objectFieldsAll(tenantFlags),
                          {}),
      },
    };
    [tenantConfig];
  std.flatMap(createTenantConfig, relevantTenants + moreTenants)

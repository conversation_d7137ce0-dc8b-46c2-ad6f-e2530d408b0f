local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

function(env, namespace, cloud, namespace_config, filter=null)
  // Create service account and grant AI Platform permissions for Gemini Flash models
  // NOTE(arun): any longer a name, and we hit the IAM 30 character limit :(.
  local appName = 'commit-indexer';
  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    env=env,
    cloud=cloud,
    namespace=namespace,
    iam=true,
  );
  local commitEmbedderConfig = (import 'services/deploy/commit_indexer_v1_config.jsonnet')(cloud);

  local indexer() = modelDeployment.embeddingIndexer(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    embedderConfig=commitEmbedderConfig,
    // Increase memory for Gemini API calls
    memoryOverride='2Gi',
    serviceAccount=serviceAccount,
  );

  local key() = modelDeployment.transformationKey(
    namespace=namespace,
    transformationKeyName=commitEmbedderConfig.transformationKey,
    // NOTE(arun): We are are intentionally re-using the chatanol embedder name here.
    appName=commitEmbedderConfig.modelConfig.name,
  );

  // Grant AI Platform access for Gemini Flash models
  local grantAiPlatformAccess = gcpLib.grantAccess(
    env=env,
    namespace=namespace,
    appName=appName,
    name='aiplatform-%s-grant' % appName,
    resourceRef={
      kind: 'Project',
      external: 'projects/%s' % cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/aiplatform.user',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
    abandon=true,
  );

  // NOTE(arun): We are sharing the embedder from chatanol-qwen-v1-1_fp8. Please do not
  // tombstone the embedder when deleting this deployment.
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace) : 'Indexer and embedder together is only supported in DEV';
    serviceAccount.objects + indexer() + key() + [grantAiPlatformAccess]
  else if filter == 'indexer' then
    serviceAccount.objects + indexer() + key() + [grantAiPlatformAccess]
  else
    error ('unknown filter: %s' % filter)

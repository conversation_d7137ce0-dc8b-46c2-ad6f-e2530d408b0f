local constants = import 'services/deploy/constants.jsonnet';
local modelConfig = {
  name: 'starethanol6-16-1-proj512',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/starethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000_v2/',
  checkpoint_sha256: '21848ba8bbc07ec1552f8b42aac78db793cd77e6844208d47020cbbf7e718634',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'ethanol6.16.1-query-embedding',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  max_seq_length: 2048,
  round_sizes: [256, 512, 1024, 2048],
  max_number_of_chunks_per_blob: 512,
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig,
  // The v3 suffix is to make use re-index all blobs after fixing a series of bugs in
  // the embedding model.
  transformationKey='dr-%s-30line-1024char-v3' % (std.asciiLower(modelConfig.name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)

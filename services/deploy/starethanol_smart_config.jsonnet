// Differences with starethanol-v4:
// - Uses smart line chunking
// - Transforms notebook into python files
// - New checkpoint

local constants = import 'services/deploy/constants.jsonnet';
local modelConfig = {
  name: 'starethanol-smart',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/ethanol/smart/stareth_smart_128doc_2000s/global_step2000-ckpt_v2/',
  checkpoint_sha256: '79fc9c4d484a35a6563119c6ceb686ac4f3bd8b7d97f2fea27494ff2704de191',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'ethanol6.16.1-query-embedding',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  max_seq_length: 2048,
  round_sizes: [256, 512, 1024, 2048],
  max_number_of_chunks_per_blob: 512,
  chunking: {
    name: 'transform_smart_line_level',
    config: {
      max_chunk_chars: 768,
      max_headers: 3,
    },
  },
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig,
  transformationKey='dr-%s-smart-768char' % (std.asciiLower(modelConfig.name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)

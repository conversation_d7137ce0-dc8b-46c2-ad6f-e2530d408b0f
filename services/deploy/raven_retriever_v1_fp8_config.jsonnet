// FP8 inference for raven_retriever_v1
// Command to produce this checkpoint from the fp16 one:
// > python research/tools/quantization/quantize_starcoder.py -cp /mnt/efs/augment/checkpoints/next-edit-gen-retrieval/ravenr1b.tied.S1.13.1,R1.3_edit_ethanol,Sc1.0_smart_chunks,T1.0.ffwd \
//     --checkpoint_sha256=34d7f5e985e074c986d894fda5d900fc403b3e743624dfcddceebf9bbd3e2035 -ms starcoder-1b -cd /mnt/efs/spark-data/shared/next-edit-gen-retrieval/S1.13.1_6000p_2000f,R1.3_edit_ethanol_synth_instruct_k128,Sc1.0_smart_chunks_1280,T1.0_diff1k_sc1,indexed_dataset/dev \
//     -op <path-to-output-dir> --is_embedder -n 1000
local constants = import 'services/deploy/constants.jsonnet';
local starcoder1b = (import 'services/deploy/configs/starcoder-1B-base.jsonnet')();
local modelConfig = {
  name: 'raven-retriever-v1',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-gen-retrieval/raven-retriever-v1-fp8',
  checkpoint_sha256: '8552b328c825f5e7bb305c481f44c594182d2b2fe7b715f281b5cc7f62ffaf4e',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch: starcoder1b.model_arch + {
    arch_type: 'STARCODER_FP8',
    output_projection_dim: 512,
    max_position_embeddings: 8192,
  },
  embedding: {
    tokenizer_name: 'starcoder',
    query_prompt_formatter_name: 'next-edit-gen-query',
    key_prompt_formatter_name: 'ethanol6-embedding-with-path-key',
  },
  chunking: {
    name: 'smart_line_level',
    config: {
      max_chunk_chars: 1280,
    },
  },
  // NOTE(arun): the prompt formatter configuration is hard-coded in `base/prompt_format_retrieve/__init__.py`
  max_seq_length: 2048,
  round_sizes: [256, 512, 1024, 2048],
  max_number_of_chunks_per_blob: 512,
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig,
  // 09/04/2024 - v1 - updated the protos to include the header
  transformationKey='dr-%s-1280char-v1' % (std.asciiLower(modelConfig.name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)

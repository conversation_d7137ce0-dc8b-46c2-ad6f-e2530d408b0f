local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local modelConfig = import 'services/deploy/pleasehold_7b_v1_config.jsonnet';

local tensorParallelism = 2;

local name = 'pleasehold-7b-v1';
function(env, namespace, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local router() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=tensorParallelism,
    inferGpu='large',
    mtls=mtls,
    attentionType='MULTI_REQUEST_FLASH_V3',
    allReduceImplementation='FASTFORWARD',
    enforceGlobal=true,
  );
  if filter == null then
    router()
  else if filter == 'inference' then
    router()
  else
    error ('unknown filter: %s' % filter)

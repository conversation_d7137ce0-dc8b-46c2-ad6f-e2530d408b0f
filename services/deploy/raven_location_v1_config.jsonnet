local starcoder_1b = import 'services/deploy/configs/starcoder-1B-base.jsonnet';
local constants = import 'services/deploy/constants.jsonnet';
local modelConfig = starcoder_1b() + {
  name: 'raven-location-v1',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50.ffwd',
  checkpoint_sha256: '16b95bd8102a70170fc00b748213a0213f6c2ebd206afec6dd8ad8b37997d111',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  embedding: {
    tokenizer_name: 'starcoder',
    query_prompt_formatter_name: 'next-edit-location-query',
    // NOTE(arun): We don't use this right now.
    key_prompt_formatter_name: 'passthrough-document',
  },
  // We use relatively long queries for next edits.
  max_seq_length: 4096,
  round_sizes: [256, 512, 1024, 2048, 4096],
};
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local seth6_16 = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';


modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig,
  // NOTE(arun): We currently re-use the StarEthanol *index* to retrieve chunks, so this
  // is the same as the StarEthanol transformation key. This will change in the future.
  transformationKey=seth6_16.transformationKey,
  chunkOrigin=constants.chunkOrigin.LOCATION_RETRIEVER,
)

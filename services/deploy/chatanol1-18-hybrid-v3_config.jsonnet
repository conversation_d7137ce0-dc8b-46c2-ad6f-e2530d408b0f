local constants = import 'services/deploy/constants.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

// This is the same as chatanol1-18-hybrid-v2, but with a fixed document prompt formatter.
// The previous version was using the wrong prompt formatter.

local name = 'chatanol1-18-hybrid-v3';
local modelConfig = {
  name: 'chatanol1-18-hybrid-v3',
  efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/chatanol/chatanol1-18.hybrid/global_step1468_v2',
  checkpoint_sha256: 'ee5820d6f524ffc0d24cb67d9c58ca23400a640fb72899250a6f033a7339316e',  // pragma: allowlist secret
  model_type: 'EMBEDDING',
  model_arch: {
    arch_type: 'STARCODER',
    num_layers: 24,
    vocab_size: 51200,
    emb_dim: 2048,
    num_heads: 16,
    head_dim: 128,
    norm_eps: 1e-5,
  },
  embedding: {
    tokenizer_name: 'rogue',
    query_prompt_formatter_name: 'chatanol6-singleturnisspecial',
    key_prompt_formatter_name: 'chatanol6-embedding-with-path-key',
  },
  max_seq_length: 2048,
  round_sizes: [256, 512, 1024, 2048],
  max_number_of_chunks_per_blob: 512,
  max_retrieval_results: 32,
};
modelDeployment.embeddingModelConfig(
  modelConfig=modelConfig + { name: name },
  transformationKey='dr-%s-30line-1024char' % (std.asciiLower(name)),
  chunkOrigin=constants.chunkOrigin.DENSE_RETRIEVER,
)

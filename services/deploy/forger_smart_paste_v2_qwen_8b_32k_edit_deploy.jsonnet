local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local modelName = 'forger-smart-paste-v2-qwen-8b-32k';
function(env, namespace, namespace_config, cloud, namespace_config, filter=null)
  local name = modelName + '-edit';
  local tensorParallelism = if env == 'DEV' then 1 else 2;
  local availbleMemoryGB = 80 * tensorParallelism - 14 - 8;
  local numCaches = std.floor(availbleMemoryGB / 10);
  local speculationModels = [];
  local modelConfig = {
    name: modelName,
    // https://determined.cw-east4.r.augmentcode.com/det/experiments/79
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_qwen_datav2_32k_ff',
    checkpoint_sha256: '92635c1ef469247ded3eb0a6f9f5d0f5995c988ddcdb69f766a82b75930d35bf',

    model_type: 'EDIT',
    inference: {
      tokenizer_name: 'qwen25coder',
      prompt_formatter_name: 'forger_v1',
      token_apportionment: {
        path_len: 256,
        message_len: 128,
        prefix_len: 16384,
        suffix_len: 16384,
        max_prompt_len: 16384,
      },
      max_context_length: 32768,  // Total length of prompt and generation
      max_output_length: 16384,  // Max number of generated tokens
    },
    post_process_config: {},
    // Values derived from the checkpoint_path/params.json
    model_arch: {
      arch_type: 'QWEN25CODER',
      num_layers: 28,
      vocab_size: 152064,
      emb_dim: 3584,
      num_heads: 4,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-6,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 1000000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 32768,  // Make sure >=`max_context_length`
      num_queries_per_head: 7,  // 28 / 4.
      mlp_dim_divisible_by: 512,
      ffn_dim_multiplier: 1.95,
    },
    round_sizes: [32, 64, 128, 256, 512, 1024, 2048, 4096],
    cache_pool_size: numCaches,
    speculationModels: speculationModels,
    // This needs to match services/edit_host/server/instruction_handler.py
    sampling_params: {},
    preference_sampling_params: {
      top_k: 40,
      top_p: 1,
      temperature: 0.8,
    },
    small_request_max_seqlen: 127,
    max_requests_in_round: 8,
  };
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=tensorParallelism,
    inferGpu='large',
    mtls=mtls,
    attentionType='MULTI_REQUEST_FLASH_V3',
    allReduceImplementation='FASTFORWARD',
    enforceGlobal=true,
  );
  local edit() = modelDeployment.editDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalConfigs=[],
    inferenceMtls=mtls,
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + edit()
  else if filter == 'inference' then
    inference()
  else if filter == 'edit' then
    edit()
  else
    error ('unknown filter: %s' % filter)

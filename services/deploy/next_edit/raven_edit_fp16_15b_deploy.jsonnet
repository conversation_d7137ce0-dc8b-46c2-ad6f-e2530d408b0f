/** Deployment configuration for the Next Edit ("Raven") Generation model and host.
This config is to deploy a 15b fp16 ffw checkpoint in dev deployment.
DO NOT deploy this in production.
*/

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local lib = import 'services/deploy/next_edit/lib.jsonnet';
local ravenLocationConfig = import 'services/deploy/raven_location_v2_config.jsonnet';
local generationRetrievalConfig = import 'services/deploy/raven_retriever_v1_fp8_config.jsonnet';

function(env, namespace, cloud, namespace_config, filter=null)
  local speculationModels = if env == 'PROD' then [] else [
    {
      model_name: 'raven-sd',
      model_arch: {
        arch_type: 'LLAMA_FP8',
        emb_dim: 2048,
        num_layers: 6,
        num_heads: 2,
        head_dim: 128,
        num_queries_per_head: 8,
        attn_split_head_mode: 'KV_HEADS',
        rotary_scaling_factor: 1.0,
        rotary_pct: 1.0,
        vocab_size: 51200,
        mlp_dim_divisible_by: 128,
        ffn_dim_multiplier: 1.5,
        norm_eps: 1e-05,
        max_position_embeddings: 16384,
        unscaled_max_position_embeddings: 16384,
      },
      weights_path: '/mnt/efs/augment/checkpoints/next-edit-spec-decoding/llama3_350m_4K_fb-S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-ebe4659b-7bc3-4366-8b3a-a0fdd58f3d6e--FastForward-fp8',  // pragma: allowlist secret
      weights_sha256: '1b2f46238e87ddc9d2d5fc330fad7ce1daa9d9811bf0dfa3d28d90db4d14b291',  // pragma: allowlist secret
      round_sizes: [64, 128, 256, 512, 1024, 2048],
    },
  ];
  local modelConfig =
    lib.base(env=env).default + {
      name: 'raven-edit-fp16-15b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/vzhao/next_edit/checkpoints/S27-R4_ethanol-P18_star2_diff12_seq12k-SC1,RS3-pr_grouped_10k-starcoder2_15b_ffw',  // pragma: allowlist secret
      checkpoint_sha256: '6680f3ed30118e79da46721cfabd7465e1bc49ea6684fb856e025d00aef1a52e',
      model_arch: lib.model_arch.starcoder2_15b_fp16,
      speculationModels: speculationModels,
      use_sequence_parallel: false,
    };
  local rerankerModelConfig =
    local starcoder2_3b = (import 'services/deploy/configs/starcoder2-3B-fp8-base.jsonnet')(env=env);
    {
      name: 'raven-reranker-v6-3b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-gen/S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_3b-ffw-fp8',  // pragma: allowlist secret
      checkpoint_sha256: 'b16d0e3037b3a7cf9103da46806bef95e927a135a3bb3a46ca9543ad53370533',
      model_type: 'NEXT_EDIT',
      inference: {
        max_context_length: 6 * 1024,
      },
      cache_pool_size: if env == 'DEV' then 2 else 16,  // FIXME
      model_arch: starcoder2_3b.model_arch,
      round_sizes: [128, 256, 512, 1024, 2048],
      use_sequence_parallel: if env == 'DEV' then false else true,
    };
  local chatModelConfig =
    local llama8b = (import 'services/deploy/configs/llama3-1-8b-instruct-fp8.jsonnet')(env);
    llama8b + {
      name: 'raven-describe-v1-8b',
      efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/next-edit-description/llama3i-8b-v1-randomctx-shortdesc-ffwd-fp8',  // pragma: allowlist secret
      checkpoint_sha256: '2ae8e4a520f98de5ada394a8cfbe249fda9d40abb92d2f298838f8ae23aab18e',
      max_context_length: 4096,
      // A relatively short cap on output length.
      max_output_length: 32,
    };
  local handlerConfig = {
    edit_generation_config: {
      ranker_gen_prompt_formatter_config: {
        diff_context_lines: 6,
        max_prompt_tokens: 5500,
        section_budgets: {
          suffix_tks: 1000,
          prefix_tks: 2000,
          diff_tks: 2000,
          filename_tks: 100,
          instruction_tks: 200,
          retrieval_tks: 0,
        },
      },
      max_output_length: 160,
    },
    change_probability_min: null,
    reranker_filter_threshold: 0.5,
    validate_blob_state: false,
    max_filter_score: {
      FOREGROUND: 1.0,
      BACKGROUND: 1.0,
      FORCED: 1.0,
    },
  };

  local objects = lib.objects(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=modelConfig.name,
    modelConfig=modelConfig,
    generationRetrievalConfigs=[modelDeployment.denseRetrievalConfig(generationRetrievalConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='content')],
    locationRetrievalConfigs=[modelDeployment.denseRetrievalConfig(ravenLocationConfig.queryModelConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config, tag='location')],
    handlerConfig=handlerConfig,
    lowQualityFilterConfig={
      checkpoint_path: 'services/next_edit_host/server/prism_models/prism_next_edit_v4.json',
    },
    chatModelConfig=chatModelConfig,
    rerankerModelConfig=rerankerModelConfig,
  );

  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    objects.generationInference + objects.rerankerInference + objects.chatInference + objects.nextEdit
  else if filter == 'inference' then
    objects.generationInference + objects.rerankerInference + objects.chatInference
  else if filter == 'next_edit' then
    objects.nextEdit
  else
    error ('unknown filter: %s' % filter)

load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
)
load("//tools/bzl:kubecfg.bzl", "kubecfg")

jsonnet_library(
    name = "lib",
    srcs = ["lib.jsonnet"],
    deps = [
        "//deploy/common:grpc-lib",
        "//services/deploy:model_deployment_lib",
        "//services/next_edit_host/server:handler_config_default",
    ],
)

kubecfg(
    name = "raven_edit_v6_15b_kubecfg",
    src = "raven_edit_v6_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:raven_location_v2_lib",
        "//services/deploy:raven_retriever_v1_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "raven_edit_v7_15b_kubecfg",
    src = "raven_edit_v7_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:raven_location_v2_lib",
        "//services/deploy:raven_retriever_v1_lib",
        "//services/deploy/configs:config_lib",
    ],
)

kubecfg(
    name = "raven_edit_fp16_15b_kubecfg",
    src = "raven_edit_fp16_15b_deploy.jsonnet",
    # uses apps for completion and inference
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/test:__subpackages__",
    ],
    deps = [
        ":lib",
        "//services/deploy:model_deployment_lib",
        "//services/deploy:raven_location_v2_lib",
        "//services/deploy:raven_retriever_v1_lib",
        "//services/deploy/configs:config_lib",
    ],
)

// Contains common functions and configurations for next edit models.
//
// NOTE: A modification here will affect many next edit models.
// For reproducibility, we aim to keep each model's behavior as fixed as possible.
// Each entry here should be kept as immutable for all time, unless it
// doesn't affect model behavior (e.g. inference changes, num replicas, etc.).
// Even then, be *very* careful updating a config here, and test thoroughly.

local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
// TODO: get rid of this indirection layer since it doesn't add much
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local handlerConfigDefault = import 'services/next_edit_host/server/handler_config_default.jsonnet';

{
  // Configs for modelConfig base settings
  base: function(env) {
    default: {
      model_type: 'NEXT_EDIT',
      round_sizes: [128, 256, 512, 1024, 2048],
      use_sequence_parallel: true,
      cache_pool_size: if env == 'DEV' then 2 else 32,
      inference: {
        max_context_length: 12 * 1024,
      },
    },
  },
  // Configs for the modelConfig.model_arch field
  model_arch: {
    starcoder2_15b: {
      arch_type: 'STARCODER2_FP8',
      num_layers: 40,
      vocab_size: 51200,
      emb_dim: 6144,
      num_heads: 48,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 12,
    },
    starcoder2_15b_fp16: {
      arch_type: 'STARCODER2',
      num_layers: 40,
      vocab_size: 51200,
      emb_dim: 6144,
      num_heads: 48,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 12,
    },
    starcoder2_3b: {
      arch_type: 'STARCODER2_FP8',
      num_layers: 30,
      vocab_size: 51200,
      emb_dim: 3072,
      num_heads: 24,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 8 * 1024,  // Make sure >=`max_context_length`
      num_queries_per_head: 12,
    },
  },
  // Produces a collection of named arrays, each containing all the k8s objects needed to deploy
  // a particular service in the next edit stack.
  objects: function(
    // First four arguments are the standard deploy.jsonnet inputs.
    env,
    namespace,
    namespace_config,  // Pardon the case mixing. Trying to not gratuitously change conventions.
    cloud,
    name,  // Base name for the next edit deployment, e.g. raven-something-or-other
    modelConfig,  // JSON inference config for the generation model
    generationRetrievalConfigs,  // Retriever configs used to produce context for generation
    locationRetrievalConfigs,  // Retriever configs used to identify candidate locations
    handlerConfig,  // Primary JSON config for the model host
    lowQualityFilterConfig,  // JSON config for the low quality filter (TODO: combine with handlerConfig?)
    chatModelConfig,  // JSON inference config for the description model
    rerankerModelConfig,  // JSON inference config for the reranker model
          )
    // TODO: consider providing predefined sizes for these like completion/lib.jsonnet does
    local generationReplicas = {
      GCP_US_CENTRAL1_PROD: {
        PROD: 0,
        STAGING: 0,  // using explicit value for comparison with gsc. this should be equal to the default value.
      },
      GCP_EU_WEST4_PROD: {
        PROD: null,
        STAGING: null,
      },
      GCP_US_CENTRAL1_DEV: {
        DEV: null,
      },
      GCP_US_CENTRAL1_GSC_PROD: {
        PROD: 40,
        STAGING: 1,
      },
    }[cloud][env];
    local rerankerReplicas = {
      GCP_US_CENTRAL1_PROD: {
        PROD: 0,
        STAGING: 0,  // using explicit value for comparison with gsc. this should be equal to the default value.
      },
      GCP_EU_WEST4_PROD: {
        PROD: null,
        STAGING: null,
      },
      GCP_US_CENTRAL1_DEV: {
        DEV: null,
      },
      GCP_US_CENTRAL1_GSC_PROD: {
        PROD: 24,
        STAGING: 1,
      },
    }[cloud][env];
    local descriptionReplicas = {
      GCP_US_CENTRAL1_PROD: {
        PROD: 0,
        STAGING: 0,  // using explicit value for comparison with gsc. this should be equal to the default value.
      },
      GCP_EU_WEST4_PROD: {
        PROD: null,
        STAGING: null,
      },
      GCP_US_CENTRAL1_DEV: {
        DEV: null,
      },
      GCP_US_CENTRAL1_GSC_PROD: {
        PROD: 4,
        STAGING: 1,
      },
    }[cloud][env];


    local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
    local primaryName = std.asciiLower(name);
    local chatName = primaryName + '-chat';
    local rerankerName = primaryName + '-reranker';

    // Check for valid config. This helper function recursively checks fields for matching types (modulo optional) and keys.
    // For details on how to use handlerConfig, please see the NextEditHandlerConfig docstring in next_edit_handler.py.
    local findConfigMismatches(config, default, path) =
      if std.type(config) != std.type(default) && std.type(config) != 'null' && std.type(default) != 'null' then
        ['Mismatched config type at %s' % path]
      else if std.type(config) == 'object' && std.type(default) == 'object' then
        std.flatMap(function(k)
          // TODO: once default fully expresses the schema, also check for incorrect keys
          findConfigMismatches(config[k], default[k], path + '.' + k)
                    , std.objectFields(config))
      else
        [];

    local mismtaches = findConfigMismatches(handlerConfig, handlerConfigDefault, '');
    assert std.length(mismtaches) == 0 : std.join(', ', mismtaches);

    local nextEditHost = modelDeployment.nextEditDeploymentHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=primaryName,
      modelConfig=modelConfig,
      rerankerName=if rerankerModelConfig != null then rerankerName else null,
      chatName=chatName,
      handlerConfig=std.mergePatch(handlerConfigDefault, handlerConfig),
      generationRetrievalConfigs=generationRetrievalConfigs,
      locationRetrievalConfigs=locationRetrievalConfigs,
      lowQualityFilterConfig=lowQualityFilterConfig,
    );
    local gpuCount = {
      DEV: 1,
      STAGING: 2,
      PROD: 4,
    };
    local generationInferenceHost = modelDeployment.centralInferenceHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=name,
      modelConfig=modelConfig,
      inferTensorPara=gpuCount[env],
      inferGpu='large',
      mtls=mtls,
      replicas=generationReplicas,
      attentionType='MULTI_REQUEST_FLASH_V3',
      allReduceImplementation='FASTFORWARD',
    );
    local chatInferenceHost = modelDeployment.centralInferenceHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=chatName,
      modelConfig=chatModelConfig,
      inferTensorPara=1,
      inferGpu=if env == 'DEV' then 'small' else 'large',
      mtls=mtls,
      replicas=descriptionReplicas,
      // Note(carl): flash-attn-v3 requires H100 GPUs ('large')
      attentionType=if env == 'DEV' then 'MULTI_REQUEST_FLASH' else 'MULTI_REQUEST_FLASH_V3',
    );
    local rerankerInferenceHost = if rerankerModelConfig != null then modelDeployment.centralInferenceHost(
      env=env,
      namespace=namespace,
      namespace_config=namespace_config,
      cloud=cloud,
      name=rerankerName,
      modelConfig=rerankerModelConfig,
      inferTensorPara=1,
      inferGpu=if env == 'DEV' then 'small' else 'large',
      mtls=mtls,
      replicas=rerankerReplicas,
      attentionType=if env == 'DEV' then 'MULTI_REQUEST_FLASH' else 'MULTI_REQUEST_FLASH_V3',
      allReduceImplementation='FASTFORWARD',
    ) else null;

    {
      nextEdit: nextEditHost,
      generationInference: generationInferenceHost,
      chatInference: chatInferenceHost,
      rerankerInference: rerankerInferenceHost,
    },
}

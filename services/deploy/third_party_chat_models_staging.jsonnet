/**
  Defines third-party chat models configurations used across deployment files that are
  staging-only
 */

{
  'deepseek-v3-16k-v1-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/deepseek-v3',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-deepseek-v3-v1',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'deepseek-v3-16k-v2-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/deepseek-v3-0324',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-deepseek-v3-v1',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'deepseek-r1-16k-v1-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/deepseek-r1',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-deepseek-r1-v1',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'deepseek-r1-16k-v2-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/deepseek-r1',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-deepseek-r1-v2',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'grok2-16k-v1': {
    client_type: 'openai_vertexai',
    model_name: 'grok-2-1212',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',  // TODO(zhuoran): Needs to be changed
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
  },
  'grok3-16k-v1': {
    client_type: 'openai_vertexai',
    model_name: 'grok-3-beta',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
  },
  'grok3-mini-16k-v1': {
    client_type: 'openai_vertexai',
    model_name: 'grok-3-mini-beta',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
  },
  'qwq-32b-v1-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/qwq-32b',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-qwq-v1',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'qwq-32b-v2-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/qwq-32b',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-qwq-v2',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'llama4-scout-v1-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/llama4-scout-instruct-basic',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'llama4-maverick-v1-fireworks': {
    client_type: 'fireworks',
    model_name: 'accounts/fireworks/models/llama4-maverick-instruct-basic',
    gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
    },
    generate_commit_message_token_apportionment: {
      changed_files_summary_line_threshold: 900,
      diff_len: 1024 * 9,
      commit_message_len: 1024 * 3,
      relevant_message_len: 1024,
      max_prompt_len: 1024 * 12,  // 12k for prompt
      path_len: 0,
      message_len: 0,
      chat_history_len: 0,
      prefix_len: 0,
      selected_code_len: 0,
      suffix_len: 0,
    },
  },
  'gpt4o-chatgpt-latest-v1': {
    client_type: 'openai_vertexai',
    model_name: 'chatgpt-4o-latest',
    gcp_region: 'us-central1',  // Currently only available on us-central1
    prompt_formatter_name: 'binks-openai-v2',
    temperature: 1,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 3000,
      retrieval_len_for_user_guided: 8000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
      inject_current_file_into_retrievals: true,
      tool_results_len: 1024 * 32,  // 32k for tool results
    },
  },
  // Temporarily disabled due to responses API breaking
  // 'o1-pro-v1': {
  //   client_type: 'openai_vertexai',
  //   model_name: 'o1-pro-2025-03-19',
  //   gcp_region: 'us-central1',  // Currently only available on us-central1
  //   prompt_formatter_name: 'binks-openai-v2',
  //   temperature: 1,
  //   max_output_tokens: 1024 * 8,  // 8k for response
  //   token_apportionment: {
  //     prefix_len: 1024 * 2,
  //     suffix_len: 1024 * 2,
  //     path_len: 256,
  //     message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
  //     selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
  //     chat_history_len: 1024 * 4,
  //     retrieval_len_per_each_user_guided_file: 3000,
  //     retrieval_len_for_user_guided: 8000,
  //     retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
  //     max_prompt_len: 1024 * 12,  // 12k tokens for the prompt
  //     inject_current_file_into_retrievals: true,
  //     tool_results_len: 1024 * 32,  // 32k for tool results
  //   },
  // },
}

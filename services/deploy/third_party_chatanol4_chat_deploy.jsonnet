/**
  Defines chat models that use a Router model to drive retrieval instead of standard multi-retriever
 */
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = (import 'services/deploy/chatanol-qwen-v1-1_fp8_config.jsonnet') + {
  modelConfig+: {
    max_retrieval_results: 128,
  },
};
local postprocessConfig = import 'services/deploy/sentry_v1_config.jsonnet';
local prodThirdPartyModels = import 'services/deploy/third_party_chat_models_prod.jsonnet';
local stagingThirdPartyModels = import 'services/deploy/third_party_chat_models_staging.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config)
  local name = 'third-party-chatanol4';
  local chatName = '%s-chat' % name;
  local modelConfig = {
    name: name,
    model_type: 'CHAT',
  };
  // Import models and add suffix to each key
  local prodMultiConfig = {
    [key + '-c4-chat']: prodThirdPartyModels[key]
    for key in std.objectFields(prodThirdPartyModels)
  };
  local stagingMultiConfig = {
    [key + '-c4-chat']: stagingThirdPartyModels[key]
    for key in std.objectFields(stagingThirdPartyModels)
  };
  local thirdPartyInferenceMultiConfig = (if env == 'PROD' then {} else stagingMultiConfig) + prodMultiConfig;
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  modelDeployment.chatDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=chatName,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      modelDeployment.userGuidedRetrievalConfig(env, namespace, namespace_config),
      modelDeployment.docsetRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
    ],
    inferenceMtls=mtls,
    thirdPartyInferenceMultiConfig=thirdPartyInferenceMultiConfig,
    postprocessConfig=modelDeployment.postprocessConfig(
      postprocessConfig,
      cloud,
      env,
      namespace,
      namespace_config,
    ),
  )

local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';

local modelName = 'forger-smart-paste-sc2-7b-32k';
function(env, namespace, namespace_config, cloud, namespace_config, filter=null)
  local name = modelName + '-edit';
  local tensorParallelism = 2;
  local availbleMemoryGB = 80 * tensorParallelism - 14 - 8;
  local numCaches = std.floor(availbleMemoryGB / 10);
  local speculationModels = [];
  local modelConfig = {
    name: modelName,
    // https://determined.gcp-us1.r.augmentcode.com/det/experiments/902/trials/902/overview
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/smart_paste_full_file/smartpaste_sc2_v13_2_fixscnewline_32K_claude_and_naive_fixbs_mp2_bs128_ff',
    checkpoint_sha256: '2841da84db20b2457598603b404ae21299d64fd5797204fbb8df2d8b44dea306',
    model_type: 'EDIT',
    inference: {
      tokenizer_name: 'starcoder2',
      prompt_formatter_name: 'forger_v1',
      token_apportionment: {
        path_len: 256,
        message_len: 128,
        prefix_len: 16384,
        suffix_len: 16384,
        max_prompt_len: 16384,
      },
      max_context_length: 32768,  // Total length of prompt and generation
      max_output_length: 16384,  // Max number of generated tokens
    },
    post_process_config: {},
    // Values derived from the checkpoint_path/params.json
    model_arch: {
      arch_type: 'STARCODER2',
      num_layers: 32,
      vocab_size: 51200,
      emb_dim: 4608,
      num_heads: 36,
      head_dim: 128,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 1000000.0,
      rotary_scaling_factor: 1.0,
      max_position_embeddings: 32768,  // Make sure >=`max_context_length`
      num_queries_per_head: 9,  // 36 / 4.
    },
    round_sizes: [32, 64, 128, 256, 512, 1024, 2048, 4096],
    cache_pool_size: numCaches,
    speculationModels: speculationModels,
    // This needs to match services/edit_host/server/instruction_handler.py
    sampling_params: {},
    preference_sampling_params: {
      top_k: 40,
      top_p: 1,
      temperature: 0.8,
    },
    small_request_max_seqlen: 127,
    max_requests_in_round: 8,
  };
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=tensorParallelism,
    inferGpu='large',
    mtls=mtls,
    attentionType='MULTI_REQUEST_FLASH_V3',
    allReduceImplementation='FASTFORWARD',
  );
  local edit() = modelDeployment.editDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=0,
    modelConfig=modelConfig,
    retrievalConfigs=[],
    inferenceMtls=mtls,
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + edit()
  else if filter == 'inference' then
    inference()
  else if filter == 'edit' then
    edit()
  else
    error ('unknown filter: %s' % filter)

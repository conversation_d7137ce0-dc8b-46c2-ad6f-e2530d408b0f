local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = import 'services/deploy/starethanol_smart_config.jsonnet';

// Difference from v3 is switching from claude-3-5-sonnet-v2@20241022 to claude-sonnet-4@20250514
function(env, namespace, namespace_config, cloud, namespace_config)
  local name = 'claude-instruction-v4-edit';
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local modelConfig = {
    name: 'claude-instruction-v4',
    model_type: 'EDIT',
  };
  local thirdPartyInferenceConfig = {
    client_type: 'anthropic_balanced',
    model_name: 'claude-sonnet-4@20250514',
    gcp_region: 'us-east5',  // Claude 3.5 currently only available on us-east5, europe-west1
    temperature: 0,
    max_output_length: 1024 * 8,  // 8k for response
    instruction_token_apportionment: {
      path_len: 256,
      prefix_len: 1024 * 4,
      suffix_len: 1024 * 4,
      chat_history_len: 1024 * 8,
      retrieval_len: 1024 * 4,
      max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
    },
    smart_paste_token_apportionment: {
      // Fields used in ChatPromptFormatter to render history
      path_len: 256,
      prefix_len: 1024,
      suffix_len: 1024,
      chat_history_len: 1024 * 67,

      // Main smartpaste fields
      target_file_path_len: 256,
      target_file_content_len: 1024 * 50,
      max_prompt_len: 1024 * 120,  // 120k for prompt
    },
  };
  local edit() = modelDeployment.editDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=0,  // No longer used
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
    ],
    inferenceMtls=mtls,
    thirdPartyInferenceConfig=thirdPartyInferenceConfig,
  );
  edit()

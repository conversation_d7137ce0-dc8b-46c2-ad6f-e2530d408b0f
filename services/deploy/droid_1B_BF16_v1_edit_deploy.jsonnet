local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = import 'services/deploy/starethanol6_16_1_proj512_config.jsonnet';

function(env, namespace, namespace_config, cloud, namespace_config, filter=null)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local name = 'droid-1B-BF16-v1-edit';
  local modelConfig = {
    name: 'droid-v1-1B-FB16',
    efs_deepspeed_checkpoint_path: '/mnt/efs/augment/checkpoints/v2/code-edit/droid/droid-v1-16k-1.3B-BF16',  // pragma: allowlist secret
    checkpoint_sha256: '24e8ebaa2b35c89b9a1b18be19d4802f13fa07a8bfd7535518f42bb36ba59bc9',
    model_type: 'EDIT',
    handler_type: 'EditHandler',  // TODO: Deprecate EditHandler in favor of InstructionHandler
    inference: {
      tokenizer_name: 'deepseek_coder_instruct',
      prompt_formatter_name: 'droid',
      token_apportionment: {
        prefix_len: 1536,
        suffix_len: 1024,
        max_context_len: 16 * 1024,

        dynamic_resizing: true,
        path_len: 256,
        instruction_len: 512,
        selected_code_len: 4096,
        max_prompt_len: 16 * 1024 - 4096,
      },
      max_context_length: 16 * 1024,
    },
    post_process_config: {
      remove_triple_backticks_at_end: true,
    },
    model_arch: {
      arch_type: 'LLAMA',
      num_layers: 24,
      vocab_size: 32256,
      emb_dim: 2048,
      num_heads: 16,
      head_dim: 128,
      num_queries_per_head: 1,
      mlp_dim_divisible_by: 128,
      ffn_dim_multiplier: 1.0,
      attn_split_head_mode: 'KV_HEADS',  // SplitHeadModes.KV_HEADS,
      norm_eps: 1e-5,
      rotary_pct: 1.0,  // RoPE parameters
      rotary_theta: 100000.0,
      rotary_scaling_factor: 4.0,
      max_position_embeddings: 16 * 1024,  // Make sure >=`max_context_length`
    },
    speculationModels: [
      {
        model_name: 'sd-droid-187-300M-FP8-edit',
        model_arch: {
          arch_type: 'LLAMA_FP8',
          num_layers: 8,
          vocab_size: 32256,
          emb_dim: 1024,
          num_heads: 1,
          head_dim: 64,
          num_queries_per_head: 16,
          mlp_dim_divisible_by: 128,
          ffn_dim_multiplier: 1.0,
          attn_split_head_mode: 'Q_PER_HEADS',  // SplitHeadModes.Q_PER_HEADS,
          norm_eps: 1e-6,
          rotary_pct: 1.0,
          rotary_theta: 100000.0,
          rotary_scaling_factor: 1.0,
          max_position_embeddings: 8192,
        },
        weights_path: '/mnt/efs/augment/checkpoints/code-edit/droid/droid-187_300M_FP8',  // pragma: allowlist secret
        round_sizes: [32, 64, 128, 256, 512, 1024, 2048],
        weights_sha256: '9f406aa191e6b6939bfd7f9e83c92b1915632a3af7f54a1f07789ef3e8a93ffe',
      },
    ],
    cache_pool_size: 2,  // The pool size for the KV cache.
    // This needs to match services/chat_host/server/chat_handler.py
    sampling_params: {},
  };
  assert modelConfig.inference.token_apportionment.max_context_len == modelConfig.inference.max_context_length;
  local inference() = modelDeployment.centralInferenceHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelConfig=modelConfig,
    inferTensorPara=1,
    inferGpu='small',
    mtls=mtls,
  );
  local edit() = modelDeployment.editDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=name,
    modelPriority=0,  // it shouldn't be the default model
    modelConfig=modelConfig,
    retrievalConfigs=[modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config)],
    inferenceMtls=mtls,
  );
  if filter == null then
    assert env == 'DEV' || cloudInfo.isKubecfgTestNamespace(namespace);
    inference() + edit()
  else if filter == 'inference' then
    inference()
  else if filter == 'edit' then
    edit()
  else
    error ('unknown filter: %s' % filter)

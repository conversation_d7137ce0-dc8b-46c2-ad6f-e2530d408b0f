local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = (import 'services/deploy/chatanol1-18-hybrid-v2_config.jsonnet') + {
  modelConfig+: {
    max_retrieval_results: 128,  // Max 1024
  },
};

function(env, namespace, namespace_config, cloud, namespace_config)
  local name = 'claude-camel-3-16k-direct';
  local chatName = '%s-chat' % name;
  local modelConfig = {
    name: name,
    model_type: 'CHAT',
  };
  local thirdPartyInferenceConfig = {
    client_type: 'anthropic_direct',
    model_name: 'claude-3-camel',
    prompt_formatter_name: 'structured-binks-claude',
    temperature: 0,
    max_output_tokens: 1024 * 8,  // 8k for response
    token_apportionment: {
      prefix_len: 1024 * 2,
      suffix_len: 1024 * 2,
      path_len: 256,
      message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
      chat_history_len: 1024 * 4,
      retrieval_len_per_each_user_guided_file: 2000,
      retrieval_len_for_user_guided: 3000,
      retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
      max_prompt_len: 1024 * 12,  // 12k for prompt
    },
  };
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  modelDeployment.chatDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=chatName,
    modelPriority=0,  // third party chat models are never the default
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      modelDeployment.userGuidedRetrievalConfig(env, namespace, namespace_config),
      modelDeployment.docsetRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
    ],
    inferenceMtls=mtls,
    thirdPartyInferenceConfig=thirdPartyInferenceConfig,
  )

load(
    "@rules_jsonnet//jsonnet:jsonnet.bzl",
    "jsonnet_library",
)
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_proto_library")

# jsonnet containing the libaries to transfer model configuration files into model instance config maps.
jsonnet_library(
    name = "model_instance_config_lib",
    srcs = [
        "model_instance_config.jsonnet",
    ],
    data = [
        "//base/languages:languages.yaml",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

proto_library(
    name = "model_instance_proto",
    srcs = ["model_instance.proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

cc_proto_library(
    name = "model_instance_cc_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [":model_instance_proto"],
)

go_proto_library(
    name = "model_instance_go_proto",
    importpath = "github.com/augmentcode/augment/services/deploy/model_instance/proto",
    proto = ":model_instance_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)

py_proto_library(
    name = "model_instance_py_proto",
    protos = [":model_instance_proto"],
    visibility = [
        "//services:__subpackages__",
        # gemm config still uses it
        "//models/inference/utils:__subpackages__",
    ],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":model_instance_config_lib",
    ],
)

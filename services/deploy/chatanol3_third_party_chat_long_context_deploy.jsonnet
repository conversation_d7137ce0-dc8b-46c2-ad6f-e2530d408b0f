local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local modelDeployment = import 'services/deploy/model_deployment_lib.jsonnet';
local embedderConfig = (import 'services/deploy/chatanol1-18-hybrid-v3_config.jsonnet') + {
  modelConfig+: {
    max_retrieval_results: 1024,
  },
};

function(env, namespace, namespace_config, cloud, namespace_config)
  local name = 'chatanol3-third-party-128k';
  local chatName = '%s-chat' % name;
  local modelConfig = {
    name: name,
    model_type: 'CHAT',
  };

  local thirdPartyInferenceMultiConfig = {
    'claude-sonnet-3-5-128k-v1-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet@20240620',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'structured-binks-claude',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-128k-v2-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet@20240620',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v2',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-128k-v3-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet@20240620',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v3',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-128k-v4-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet@20240620',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v4',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-128k-v5-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v3',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-128k-v8-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v8',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-16k-v11-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v11-1',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-16k-v12-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v12-1',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 64,  // 64k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-sonnet-3-5-128k-v13-chat': {
      client_type: 'anthropic_vertexai',
      model_name: 'claude-3-5-sonnet-v2@20241022',
      gcp_region: 'us-east5',  // Currently only available on us-east5, europe-west1
      prompt_formatter_name: 'binks-claude-v13',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 64,  // 64k for tool results
        retrieval_as_tool: true,
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
    'claude-flannel-128k-v13-direct-chat': {
      client_type: 'anthropic_direct',
      model_name: 'research-claude-flannel',
      prompt_formatter_name: 'binks-claude-v13',
      temperature: 0,
      max_output_tokens: 1024 * 8,  // 8k for response
      token_apportionment: {
        prefix_len: 1024 * 4,
        suffix_len: 1024 * 4,
        path_len: 256,
        message_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        selected_code_len: -1,  // Deprecated field: Not used by the structured-binks-gemini prompt formatter
        chat_history_len: 1024 * 8,
        retrieval_len_per_each_user_guided_file: 3000,
        retrieval_len_for_user_guided: 8000,
        retrieval_len: -1,  // Fill the rest of the input prompt with retrievals
        max_prompt_len: 1024 * 120,  // 120k tokens for the prompt
        inject_current_file_into_retrievals: true,
        tool_results_len: 1024 * 64,  // 64k for tool results
      },
      generate_commit_message_token_apportionment: {
        changed_files_summary_line_threshold: 900,
        diff_len: 1024 * 110,
        commit_message_len: 1024 * 10,
        relevant_message_len: 1024,
        max_prompt_len: 1024 * 120,  // 120k for prompt
        path_len: 0,
        message_len: 0,
        chat_history_len: 0,
        prefix_len: 0,
        selected_code_len: 0,
        suffix_len: 0,
      },
    },
  };

  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  modelDeployment.chatDeploymentHost(
    env=env,
    namespace=namespace,
    namespace_config=namespace_config,
    cloud=cloud,
    name=chatName,
    modelPriority=0,  // third party chat models are never the default
    modelConfig=modelConfig,
    retrievalConfigs=[
      modelDeployment.denseRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
      modelDeployment.userGuidedRetrievalConfig(env, namespace, namespace_config),
      modelDeployment.docsetRetrievalConfig(embedderConfig, cloud=cloud, env=env, namespace=namespace, namespace_config=namespace_config),
    ],
    inferenceMtls=mtls,
    thirdPartyInferenceMultiConfig=thirdPartyInferenceMultiConfig,
  )

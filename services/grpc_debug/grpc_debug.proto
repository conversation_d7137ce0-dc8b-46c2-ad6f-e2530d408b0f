syntax = "proto3";

import "google/protobuf/descriptor.proto";
import "google/rpc/status.proto";

// Service for debugging gRPC services.
service GrpcDebug {
  // Get the list of endpoints.
  rpc GetEndpoints(GetEndpointsRequest) returns (GetEndpointsResponse) {}
  // Get the list of services.
  rpc GetServices(GetServicesRequest) returns (GetServicesResponse) {}
  // Get the list of methods for a service.
  rpc GetServiceMethods(GetServiceMethodsRequest) returns (GetServiceMethodsResponse) {}
  // Invoke a method.
  rpc InvokeMethod(MethodInvocationRequest) returns (MethodInvocationResponse) {}
}

message GetEndpointsRequest {}

message Endpoint {
  // The name of the endpoint.
  string name = 1;

  // The URL of the endpoint.
  //
  // the URL is in the format http(s)://host:port
  // if https is used, TLS with MTLS is used.
  string url = 2;
}

message GetEndpointsResponse {
  // The list of endpoints.
  repeated Endpoint endpoints = 1;
}

message GetServicesRequest {
  // The endpoint to use.
  string endpoint = 1;
}

message GetServiceMethodsRequest {
  // The endpoint to use.
  string endpoint = 1;

  // The service name to use.
  // Optional, if not set, all methods are returned.
  string service_name = 2;
}

message Service {
  // The name of the service.
  string name = 1;
}

message GetServiceMethodsResponse {
  // The list of methods.
  repeated Method methods = 1;
}

message MessageType {
  // The fully qualified name of the input or output type.
  string name = 1;

  // A proto definition of the input or output type.
  // May not be identical to the original as it is recreated from the reflection info.
  string proto_definition = 2;
}

message Method {
  // The name of the service.
  string service_name = 1;
  // The name of the method.
  string method_name = 2;

  // The fully qualified name of the method, e.g. service/method.
  string fully_qualified_name = 3;

  // The empty request json for the method, e.g.
  // {"request": {"text": ""}}
  string empty_request_json = 4;

  // The input and output types.
  MessageType input_type = 5;

  // Whether the method is client streaming.
  bool is_client_streaming = 6;

  MessageType output_type = 7;

  // Whether the method is server streaming.
  bool is_server_streaming = 8;
}

message MethodInvocationRequest {
  // The endpoint to use.
  string endpoint = 1;

  // The service name to use.
  string service_name = 2;

  // The method name to use.
  string method_name = 3;

  // The request json to use.
  string request_json = 4 [debug_redact = true];
}

message MethodInvocationResponse {
  // The response json.
  string response_json = 1 [debug_redact = true];
  // The error, if any.
  google.rpc.Status error = 2;
}

message GetServicesResponse {
  // The list of services.
  repeated Service services = 1;
}

// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'grpc-debug',
      kubecfg: {
        target: '//services/grpc_debug/server:kubecfg',
        task: tenantNamespaces.namespaces + cloudInfo.centralNamespaces,
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['dirk'],
          slack_channel: '#system-services',
        },
      },
    },

    {
      name: 'grpc-debug-shared',
      kubecfg: {
        target: '//services/grpc_debug/server:kubecfg_shared',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
          },
        ],
      },
    },
  ],
}

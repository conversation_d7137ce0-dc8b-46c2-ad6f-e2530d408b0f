// K8S deployment file for the route guide service
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpointsLib = import 'services/deploy/endpoints.jsonnet';
// the function that creates the deployment
// env: the environment (DEV, PROD, ...)
// namespace: the namespace that the deployment is created in
// cloud: the cloud (GCP_US_CENTRAL1_DEV, GCP_US_CENTRAL1_PROD, ...)
// namespace_config: the namespace config from //deploy/tenants/namespace_configs
function(env, namespace, cloud, namespace_config)
  // the app name is used in the kubernetes object names. It is also added as label to each object
  // so that we know which app an object belongs to
  local appName = 'grpc-debug';

  local central = namespace == 'central' || namespace == 'central-staging';

  // mutual TLS is enabled if the namespace config has the forceMtls flag set
  // MTLS ensures that the client and server certificates are valid
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  // creates a client certificate so that the pod can authenticiate to grpc servers (incl. itself for health checks)
  // in the same namespace
  local clientCert = certLib.createClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a client certificate so that the pod can authenticiate to grpc servers running in the central namespace
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-cert' % appName,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );

  // creates a server certificate for MTLS
  local serverCert = if central
  then
    certLib.createCentralServerCert(name='%s-server-cert' % appName,
                                    namespace=namespace,
                                    env=env,
                                    appName=appName,
                                    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
                                    volumeName='certs')
  else
    certLib.createServerCert(name='%s-server-certificate' % appName,
                             namespace=namespace,
                             appName=appName,
                             dnsNames=grpcLib.grpcServiceNames(appName),
                             volumeName='certs');

  // configuration that will be passed to the server as a JSON file
  local config = {
    grpc_port: 50051,
    web_port: 8080,
    server_mtls: if mtls then serverCert.config else null,
    client_mtls: if mtls then if central then centralClientCert.config else clientCert.config else null,
    central_client_mtls: if mtls then centralClientCert.config else null,
    prom_port: 9090,
    namespace: namespace,
    token_exchange_endpoint: endpointsLib.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud),
    auth_query_endpoint: 'auth-query-svc:50051',
    central: central,
  };
  // a config map is a Kubernetes object that contains configuration data it is "mounted" into a pod
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);
  // creates a service account for the pod
  // a service account is needed to access GCP resources or kubernetes resources
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true,
  );
  // creates a service for the pod
  // a service is needed to expose the pod to the outside world
  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  // creates a container that runs the server
  local container = {
    name: appName,
    // the target is the bazel target that builds the docker image
    target: {
      name: '//services/grpc_debug/server:image',
      dst: 'grpc-debug',
    },
    // the arguments that are passed to the server
    args: [
      '--config',
      configMap.filename,
    ],
    // ports that the pod exposes
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
      {
        containerPort: 8080,
        name: 'web-svc',
      },
    ],
    // the environment variables that are passed to the server
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    // the volumes that are mounted into the pod
    volumeMounts: [
      configMap.volumeMountDef,
      centralClientCert.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
    ],
    // the health check is used to determine if the pod is ready to receive traffic
    readinessProbe: grpcLib.grpcHealthCheck(
      appName + '-svc',
      tls=mtls,
      serverCerts=serverCert.volumeMountDef.mountPath,
      clientCerts=if central then centralClientCert.volumeMountDef.mountPath else clientCert.volumeMountDef.mountPath
    ) + {
      periodSeconds: 30,
    },
    // the liveness check is used to determine if the pod is alive
    livenessProbe: grpcLib.grpcHealthCheck(
      appName + '-svc',
      tls=mtls,
      serverCerts=serverCert.volumeMountDef.mountPath,
      clientCerts=if central then centralClientCert.volumeMountDef.mountPath else clientCert.volumeMountDef.mountPath
    ) + {
      periodSeconds: 30,
    },
    // the resource limits are used to determine how much CPU and memory the pod can use
    resources: {
      limits: {
        cpu: 1,
        memory: '512Mi',
      },
    },
  };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  // the pod is the kubernetes object that runs the container
  local pod = {
    // the service account is used to access GCP resources or kubernetes resources
    serviceAccountName: serviceAccount.name,
    containers: [
      container,
    ],
    priorityClassName: cloudInfo.envToPriorityClass(env),
    affinity: affinity,
    tolerations: tolerations,
    // the volumes are mounted into the pod
    volumes: [
      // the config map is mounted into the pod
      configMap.podVolumeDef,
      // the client certificate is mounted into the pod
      centralClientCert.podVolumeDef,
      // the server certificate is mounted into the pod
      serverCert.podVolumeDef,
      // the client certificate is mounted into the pod
      clientCert.podVolumeDef,
    ],
  };

  local roleBinding = {
    apiVersion: 'rbac.authorization.k8s.io/v1',
    kind: 'RoleBinding',
    metadata: {
      name: '%s-role-binding' % appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    subjects: [
      {
        kind: 'ServiceAccount',
        namespace: namespace,
        name: serviceAccount.name,
      },
    ],
    roleRef: {
      kind: 'ClusterRole',
      name: 'grpc-debug-role',
      apiGroup: 'rbac.authorization.k8s.io',
    },
  };

  // the tolerations and affinity are used to determine which nodes the pod can be scheduled on
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      // the minimum amount of time that a pod needs to be ready before the deployment is considered successful
      minReadySeconds: if env == 'DEV' then 0 else 30,
      // the number of pods that are running at the same time
      replicas: if env == 'DEV' then 1 else 2,
      // the strategy is used to determine how the deployment is rolled out
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod + {
          tolerations: tolerations,
          affinity: affinity,
        },
      },
    },
  };
  lib.flatten([
    configMap.objects,
    serviceAccount.objects,
    serverCert.objects,
    centralClientCert.objects,
    clientCert.objects,
    deployment,
    services,
    roleBinding,
  ])

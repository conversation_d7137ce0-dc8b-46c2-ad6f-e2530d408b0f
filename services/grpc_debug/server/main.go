package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"

	"github.com/augmentcode/augment/base/logging"
	"github.com/augmentcode/augment/base/logging/audit"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	authqueryclient "github.com/augmentcode/augment/services/auth/query/client"
	proto "github.com/augmentcode/augment/services/grpc_debug/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

var (
	configFile = flag.String("config", "", "Path to config file")
	kubeconfig = flag.String("kubeconfig", "", "Path to kubeconfig file")
)

type Config struct {
	// the port the grpc server will listen on
	GrpcPort int `json:"grpc_port"`

	// the port the http server will listen on
	WebPort int `json:"web_port"`

	// TLS configuration
	ServerMtls        *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls        *tlsconfig.ClientConfig `json:"client_mtls"`
	CentralClientMtls *tlsconfig.ClientConfig `json:"central_client_mtls"`

	Namespace string `json:"namespace"`

	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`
	AuthQueryEndpoint     string `json:"auth_query_endpoint"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	Central bool `json:"central"`
}

func runGrpc(config *Config, grpcServer *grpc.Server, invoker Invoker) error {
	server := NewGrpcDebugServer(invoker, audit.NewDefaultAuditLogger())

	proto.RegisterGrpcDebugServer(grpcServer, server)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.GrpcPort))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())
	return grpcServer.Serve(lis)
}

func runWeb(config *Config, invoker Invoker, serviceTokenAuth *auth.ServiceTokenAuth,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	authQueryClient authqueryclient.AuthQueryClient,
) error {
	server := NewWebServer(invoker, config.WebPort, serviceTokenAuth, tokenExchangeClient, authQueryClient)
	return server.Start()
}

func CreateKubernetesClient(kubeconfig string) (*kubernetes.Clientset, error) {
	var config *rest.Config
	if kubeconfig != "" {
		log.Info().Msgf("Using kubeconfig file (%s) to create Kubernetes client", kubeconfig)
		configFlags, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, err
		}
		config = configFlags
	} else {
		restConfig, err := rest.InClusterConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating in-cluster config")
			return nil, err
		}
		config = restConfig
	}
	return kubernetes.NewForConfig(config)
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	RegisterWebServerMetrics()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	// Create client credentials for the client.
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	var opts []grpc.ServerOption
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))

	// Set up service token auth.
	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, grpc.WithTransportCredentials(centralClientCreds),
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
		os.Exit(1)
	}
	defer tokenExchangeClient.Close()

	authQueryClient, err := authqueryclient.NewAuthQueryClient(
		config.AuthQueryEndpoint, clientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating auth query client")
	}

	serviceTokenAuth := auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)
	opts = append(opts, grpc.ChainUnaryInterceptor(authInterceptor.Intercept))
	opts = append(opts, grpc.ChainStreamInterceptor(authInterceptor.StreamIntercept))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	kubeClientset, err := CreateKubernetesClient(*kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating clientset")
	}

	creds := clientCreds
	if config.Central {
		creds = centralClientCreds
	}
	invoker := NewInvoker(kubeClientset, config.Namespace, creds)

	go func() {
		err = runGrpc(&config, grpcServer, invoker)
		if err != nil {
			log.Fatal().Err(err).Msg("Error serving")
		}
	}()

	go func() {
		err = runWeb(&config, invoker, serviceTokenAuth, tokenExchangeClient, authQueryClient)
		if err != nil {
			log.Fatal().Err(err).Msg("Error serving")
		}
	}()

	select {}
}

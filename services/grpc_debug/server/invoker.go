package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	proto "github.com/augmentcode/augment/services/grpc_debug/proto"
	"github.com/fullstorydev/grpcurl"
	"github.com/jhump/protoreflect/desc"
	"github.com/jhump/protoreflect/desc/protoprint"
	"github.com/jhump/protoreflect/grpcreflect"
	"github.com/rs/zerolog/log"
	statusproto "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

type Invoker interface {
	GetEndpoints(ctx context.Context) ([]*proto.Endpoint, error)
	GetServices(ctx context.Context, endpoint string) ([]*proto.Service, error)
	GetServiceMethods(ctx context.Context, endpoint string, serviceName string) ([]*proto.Method, error)
	InvokeMethod(ctx context.Context, endpoint string, serviceName string, methodName string, requestJson string) (*proto.MethodInvocationResponse, error)
}

type invoker struct {
	clientset   *kubernetes.Clientset
	namespace   string
	clientCreds credentials.TransportCredentials
}

func NewInvoker(clientset *kubernetes.Clientset, namespace string, clientCreds credentials.TransportCredentials) Invoker {
	return &invoker{
		clientset:   clientset,
		namespace:   namespace,
		clientCreds: clientCreds,
	}
}

func (s *invoker) getConnection(ctx context.Context, endpoint string) (*grpc.ClientConn, error) {
	var opts []grpc.DialOption

	// check and remove https
	if strings.HasPrefix(endpoint, "https://") {
		endpoint = strings.TrimPrefix(endpoint, "https://")
		opts = append(opts, grpc.WithTransportCredentials(s.clientCreds))
	} else if strings.HasPrefix(endpoint, "http://") {
		endpoint = strings.TrimPrefix(endpoint, "http://")
		opts = append(opts, grpc.WithInsecure())
	} else {
		return nil, fmt.Errorf("Invalid endpoint %s", endpoint)
	}

	// Connect to the gRPC server
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		log.Error().Err(err).Msg("Failed to connect to gRPC server")
		return nil, err
	}
	return conn, nil
}

func (s *invoker) GetEndpoints(ctx context.Context) ([]*proto.Endpoint, error) {
	log.Info().Msgf("GetEndpoints")
	services, err := s.clientset.CoreV1().Services(s.namespace).List(
		ctx,
		metav1.ListOptions{
			LabelSelector: "eng.augmentcode.com/service-type=grpc",
		},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error listing services")
		return nil, err
	}
	var endpoints []*proto.Endpoint
	for _, service := range services.Items {
		insecure := false
		if _, ok := service.Labels["eng.augmentcode.com/grpc-insecure"]; ok {
			insecure = true
		}

		for j, port := range service.Spec.Ports {
			name := service.Name
			if j > 0 {
				name = fmt.Sprintf("%s-%d", name, port.Port)
			}
			protocol := "https"
			if insecure {
				protocol = "http"
			}

			endpoints = append(endpoints, &proto.Endpoint{
				Name: name,
				Url: fmt.Sprintf("%s://%s:%d",
					protocol,
					service.Name, port.Port),
			})
		}
	}
	return endpoints, nil
}

func (s *invoker) GetServices(ctx context.Context, endpoint string) ([]*proto.Service, error) {
	conn, err := s.getConnection(ctx, endpoint)
	if err != nil {
		log.Error().Err(err).Msg("Failed to connect to gRPC server")
		return nil, err
	}
	defer conn.Close()
	refClient := grpcreflect.NewClientAuto(ctx, conn)
	refClient.AllowMissingFileDescriptors()
	descSource := grpcurl.DescriptorSourceFromServer(ctx, refClient)
	svcs, err := grpcurl.ListServices(descSource)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list services")
		return nil, err
	}
	resp := []*proto.Service{}
	for _, svc := range svcs {
		resp = append(resp, &proto.Service{
			Name: svc,
		})
	}
	return resp, nil
}

type messageTypeBuilder struct {
	printer   *protoprint.Printer
	typeNames []string
	proto     []string
}

func newBuilder() *messageTypeBuilder {
	printer := &protoprint.Printer{
		Compact:                  true,
		SortElements:             true,
		ForceFullyQualifiedNames: true,
	}
	b := &messageTypeBuilder{}
	b.printer = printer
	b.typeNames = []string{}
	return b
}

func (b *messageTypeBuilder) handleType(desc *desc.MessageDescriptor) error {
	found := false
	for _, name := range b.typeNames {
		if name == desc.GetFullyQualifiedName() {
			found = true
			break
		}
	}
	if found {
		return nil
	}
	b.typeNames = append(b.typeNames, desc.GetFullyQualifiedName())
	inputTypeProtoDefinition, err := b.printer.PrintProtoToString(desc)
	if err != nil {
		return err
	}
	b.proto = append(b.proto, inputTypeProtoDefinition)
	for _, field := range desc.GetFields() {
		if field.GetMessageType() != nil {
			err := b.handleType(field.GetMessageType())
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (b *messageTypeBuilder) build(desc *desc.MessageDescriptor) (*proto.MessageType, error) {
	err := b.handleType(desc)
	if err != nil {
		return nil, err
	}
	protoStr := strings.Join(b.proto, "\n")
	return &proto.MessageType{
		Name:            desc.GetFullyQualifiedName(),
		ProtoDefinition: protoStr,
	}, nil
}

func (s *invoker) GetServiceMethods(ctx context.Context, endpoint string, serviceName string) ([]*proto.Method, error) {
	conn, err := s.getConnection(ctx, endpoint)
	if err != nil {
		log.Error().Err(err).Msg("Failed to connect to gRPC server")
		return nil, err
	}
	defer conn.Close()
	refClient := grpcreflect.NewClientAuto(ctx, conn)
	refClient.AllowMissingFileDescriptors()
	descSource := grpcurl.DescriptorSourceFromServer(ctx, refClient)
	svcs, err := grpcurl.ListServices(descSource)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list services")
		return nil, err
	}
	resp := []*proto.Method{}
	for _, svc := range svcs {
		if serviceName == svc || serviceName == "" {
			// Get methods for the service
			svcDesc, err := refClient.ResolveService(svc)
			if err != nil {
				log.Error().Err(err).Msg("Failed to resolve service")
				return nil, err
			}
			for _, method := range svcDesc.GetMethods() {
				emptyRequest := ""
				if method.GetInputType().GetFullyQualifiedName() == "google.protobuf.Empty" {
					emptyRequest = "{}"
				} else {
					request := grpcurl.MakeTemplate(method.GetInputType())
					resolver := grpcurl.AnyResolverFromDescriptorSource(descSource)
					formatter := grpcurl.NewJSONFormatter(true, resolver)

					json, err := formatter(request)
					if err != nil {
						log.Error().Err(err).Msg("Failed to marshal request")
						return nil, err
					}
					emptyRequest = string(json)
				}
				if method.IsClientStreaming() {
					emptyRequest = fmt.Sprintf(`[\n%s\n]`, emptyRequest)
				}

				inputBuilder := newBuilder()
				inputTypeProto, err := inputBuilder.build(method.GetInputType())
				if err != nil {
					log.Error().Err(err).Msg("Failed to build input type proto")
					return nil, err
				}
				outputBuilder := newBuilder()
				outputTypeProto, err := outputBuilder.build(method.GetOutputType())
				if err != nil {
					log.Error().Err(err).Msg("Failed to build output type proto")
					return nil, err
				}

				resp = append(resp, &proto.Method{
					ServiceName:        svc,
					MethodName:         method.GetName(),
					FullyQualifiedName: method.GetFullyQualifiedName(),
					EmptyRequestJson:   emptyRequest,
					InputType:          inputTypeProto,
					IsClientStreaming:  method.IsClientStreaming(),
					OutputType:         outputTypeProto,
					IsServerStreaming:  method.IsServerStreaming(),
				})
			}
		}
	}
	return resp, nil
}

func replaceRecordSeparator(s string) (string, error) {
	scanner := bufio.NewScanner(strings.NewReader(s))
	scanner.Buffer(make([]byte, 1024*1024), 10*1024*1024) // Increase buffer size to 10MB

	// Step 2: Buffer to hold each full JSON document
	var buffer bytes.Buffer
	var jsonList []map[string]interface{}

	for scanner.Scan() {
		line := scanner.Text()
		// Append each line to the buffer
		buffer.WriteString(line)
		buffer.WriteString("\n")

		// Try to parse the current buffer content
		var jsonObj map[string]interface{}
		err := json.Unmarshal(buffer.Bytes(), &jsonObj)
		if err == nil {
			// If parsing is successful, add the JSON object to the list
			jsonList = append(jsonList, jsonObj)
			// Clear the buffer for the next JSON document
			buffer.Reset()
		}
	}

	// Check for any scanner errors
	if err := scanner.Err(); err != nil && err != io.EOF {
		return "", err
	}

	// Step 3: Output the list of parsed JSON objects as a JSON array
	output, err := json.MarshalIndent(jsonList, "", "  ")
	if err != nil {
		return "", err
	}
	return string(output), nil
}

func (s *invoker) InvokeMethod(ctx context.Context, endpoint string, serviceName string, methodName string, requestJson string) (*proto.MethodInvocationResponse, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, fmt.Errorf("missing metadata")
	}

	conn, err := s.getConnection(ctx, endpoint)
	if err != nil {
		log.Error().Err(err).Msg("Failed to connect to gRPC server")
		return nil, err
	}
	defer conn.Close()
	refClient := grpcreflect.NewClientAuto(ctx, conn)
	refClient.AllowMissingFileDescriptors()
	descSource := grpcurl.DescriptorSourceFromServer(ctx, refClient)

	in := strings.NewReader(requestJson)

	options := grpcurl.FormatOptions{
		EmitJSONDefaultFields: true,
		IncludeTextSeparator:  true,
		AllowUnknownFields:    true,
	}
	format := "json"
	rf, formatter, err := grpcurl.RequestParserAndFormatter(grpcurl.Format(format), descSource, in, options)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to construct request parser and formatter for %q", format)
	}

	outBuf := &bytes.Buffer{}

	// Invoke the gRPC method using grpcurl
	h := &grpcurl.DefaultEventHandler{
		Out:            outBuf,
		Formatter:      formatter,
		VerbosityLevel: 0,
	}

	// forward request headers
	headers := make([]string, 0)
	requestId := md.Get("x-request-id")
	if len(requestId) > 0 {
		headers = append(headers, fmt.Sprintf("x-request-id: %s", requestId[0]))
	}
	requestSessionId := md.Get("x-request-session-id")
	if len(requestSessionId) > 0 {
		headers = append(headers, fmt.Sprintf("x-request-session-id: %s", requestSessionId[0]))
	}
	authorization := md.Get("authorization")
	if len(authorization) > 0 {
		headers = append(headers, fmt.Sprintf("authorization: %s", authorization[0]))
	}
	fullMethodName := fmt.Sprintf("%s/%s", serviceName, methodName)

	log.Info().Msgf("Invoking gRPC method %s", fullMethodName)
	err = grpcurl.InvokeRPC(ctx, descSource, conn, fullMethodName, headers, h, rf.Next)
	if err != nil {
		response := &proto.MethodInvocationResponse{}
		response.Error = &statusproto.Status{
			Code:    int32(codes.Internal),
			Message: err.Error(),
		}
		log.Warn().Err(err).Msg("Failed to invoke gRPC method")
		return response, nil
	}
	reqSuffix := ""
	respSuffix := ""
	reqCount := rf.NumRequests()
	if reqCount != 1 {
		reqSuffix = "s"
	}
	if h.NumResponses != 1 {
		respSuffix = "s"
	}
	log.Info().Msgf("Sent %d request%s and received %d response%s", reqCount, reqSuffix, h.NumResponses, respSuffix)
	resp := &proto.MethodInvocationResponse{}
	out, err := replaceRecordSeparator(outBuf.String())
	if err != nil {
		log.Error().Err(err).Msg("Failed to replace record separator")
		return nil, err
	}
	resp.ResponseJson = out
	if h.Status.Code() != codes.OK {
		log.Warn().Msgf("gRPC method invocation failed: %v", h.Status)
		resp.Error = &statusproto.Status{
			Code:    int32(h.Status.Code()),
			Message: h.Status.Message(),
		}
	} else {
		log.Info().Msgf("gRPC method invocation succeeded")
	}
	return resp, nil
}

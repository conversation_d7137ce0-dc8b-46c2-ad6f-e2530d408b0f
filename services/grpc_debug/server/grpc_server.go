package main

import (
	"context"
	"fmt"

	"github.com/augmentcode/augment/base/logging/audit"
	proto "github.com/augmentcode/augment/services/grpc_debug/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type grpcDebugServer struct {
	invoker     Invoker
	auditLogger *audit.AuditLogger
}

func NewGrpcDebugServer(invoker Invoker, auditLogger *audit.AuditLogger) *grpcDebugServer {
	return &grpcDebugServer{
		invoker:     invoker,
		auditLogger: auditLogger,
	}
}

func (s *grpcDebugServer) GetEndpoints(ctx context.Context, req *proto.GetEndpointsRequest) (*proto.GetEndpointsResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetEndpoints")
	endpoints, err := s.invoker.GetEndpoints(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get endpoints")
		return nil, err
	}
	resp := &proto.GetEndpointsResponse{
		Endpoints: endpoints,
	}
	log.Ctx(ctx).Info().Msgf("GetEndpoints: %v", resp)
	return resp, nil
}

func (s *grpcDebugServer) GetServices(ctx context.Context, req *proto.GetServicesRequest) (*proto.GetServicesResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, _ := auth.GetAugmentClaims(ctx)
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetServices: %s", req.Endpoint)
	services, err := s.invoker.GetServices(ctx, req.Endpoint)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get services")
		return nil, err
	}
	resp := &proto.GetServicesResponse{
		Services: services,
	}
	log.Ctx(ctx).Info().Msgf("GetServices: %v", resp)
	return resp, nil
}

func (s *grpcDebugServer) GetServiceMethods(ctx context.Context, req *proto.GetServiceMethodsRequest) (*proto.GetServiceMethodsResponse, error) {
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, _ := auth.GetAugmentClaims(ctx)
	ctx = authInfo.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetServiceMethods: %s %s", req.Endpoint, req.ServiceName)
	methods, err := s.invoker.GetServiceMethods(ctx, req.Endpoint, req.ServiceName)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get service methods")
		return nil, err
	}
	resp := &proto.GetServiceMethodsResponse{
		Methods: methods,
	}
	log.Ctx(ctx).Info().Msgf("GetServiceMethods: %v", resp)
	return resp, nil
}

func (s *grpcDebugServer) InvokeMethod(ctx context.Context, req *proto.MethodInvocationRequest) (*proto.MethodInvocationResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("Invoke method %s.%s.%s", req.Endpoint, req.ServiceName, req.MethodName),
		)
	}

	log.Ctx(ctx).Info().Msgf("InvokeMethod: %s %s %s", req.Endpoint, req.ServiceName, req.MethodName)
	resp, err := s.invoker.InvokeMethod(ctx, req.Endpoint, req.ServiceName, req.MethodName, req.RequestJson)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to invoke method")
		return nil, err
	}
	log.Ctx(ctx).Info().Msgf("InvokeMethod finished: %s %s %s", req.Endpoint, req.ServiceName, req.MethodName)
	return resp, nil
}

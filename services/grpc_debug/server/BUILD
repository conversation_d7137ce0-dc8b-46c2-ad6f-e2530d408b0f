load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "server_lib",
    srcs = [
        "grpc_server.go",
        "invoker.go",
        "main.go",
        "web_server.go",
    ],
    importpath = "github.com/augmentcode/augment/services/grpc_debug/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/logging/audit:audit_go",
        "//base/tracing/go:tracing_go",
        "//services/auth/query/client:client_go",
        "//services/grpc_debug:grpc_debug_go_proto",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_fullstorydev_grpcurl//:grpcurl",
        "@com_github_gorilla_mux//:mux",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_jhump_protoreflect//desc",
        "@com_github_jhump_protoreflect//desc/protoprint",
        "@com_github_jhump_protoreflect//dynamic",
        "@com_github_jhump_protoreflect//grpcreflect",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/clientcmd",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
    visibility = ["//visibility:public"],
)

# create a container image
go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    # add the grpc health probe
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

kubecfg(
    name = "kubecfg_shared",
    src = "deploy_shared.jsonnet",
    cluster_wide = True,
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        ":kubecfg_shared",
        "//deploy/common:cloud_info",
        "//deploy/tenants:namespaces",
    ],
)

package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	authqueryclient "github.com/augmentcode/augment/services/auth/query/client"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

var requestCounter = prometheus.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_grpc_debug_web_requests",
		Help: "Number of HTTP requests",
	},
	[]string{"status", "method", "path"},
)

var durationHistogram = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Name:    "au_grpc_debug_web_duration",
		Help:    "Duration of webhook HTTP requests",
		Buckets: prometheus.DefBuckets,
	},
	[]string{"status", "method", "path"},
)

func RegisterWebServerMetrics() {
	prometheus.MustRegister(requestCounter)
	prometheus.MustRegister(durationHistogram)
}

type WebServer struct {
	invoker             Invoker
	port                int
	serviceAuth         *auth.ServiceTokenAuth
	tokenExchangeClient tokenexchange.TokenExchangeClient
	authQueryClient     authqueryclient.AuthQueryClient
}

type LoggingResponseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (w *LoggingResponseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

func withMetrics(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		loggingWriter := &LoggingResponseWriter{
			ResponseWriter: w,
			statusCode:     http.StatusOK,
		}

		start := time.Now()

		next.ServeHTTP(loggingWriter, r)

		duration := time.Since(start)
		durationHistogram.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Observe(duration.Seconds())

		requestCounter.WithLabelValues(
			fmt.Sprintf("%d", loggingWriter.statusCode),
			r.Method,
			r.URL.Path,
		).Inc()

		event := log.Info()
		if loggingWriter.statusCode >= 500 {
			event = log.Error()
		} else if loggingWriter.statusCode >= 400 {
			event = log.Warn()
		}

		event.Str("method", r.Method).Str("path", r.URL.Path).Int("status", loggingWriter.statusCode).Dur("duration", duration).Msgf("Request processed")
	})
}

func NewWebServer(invoker Invoker, port int, serviceAuth *auth.ServiceTokenAuth,
	tokenExchangeClient tokenexchange.TokenExchangeClient, authQueryClient authqueryclient.AuthQueryClient,
) *WebServer {
	return &WebServer{
		invoker:             invoker,
		port:                port,
		serviceAuth:         serviceAuth,
		tokenExchangeClient: tokenExchangeClient,
		authQueryClient:     authQueryClient,
	}
}

func (s *WebServer) health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
}

func (s *WebServer) missing(w http.ResponseWriter, r *http.Request) {
	log.Warn().Msgf("Not found: %s", r.URL.Path)
	http.Error(w, "Not Found", http.StatusNotFound)
}

func (s *WebServer) Start() error {
	r := http.NewServeMux()

	r.Handle("GET /grpc-debug/endpoints", withMetrics(http.HandlerFunc(s.getEndpoints)))
	r.Handle("GET /grpc-debug/services", withMetrics(http.HandlerFunc(s.getServices)))
	r.Handle("GET /grpc-debug/methods", withMetrics(http.HandlerFunc(s.getMethods)))
	r.Handle("POST /grpc-debug/invoke", withMetrics(http.HandlerFunc(s.invokeMethod)))
	r.Handle("GET /health", http.HandlerFunc(s.health))
	r.Handle("/", http.HandlerFunc(s.missing))

	http.Handle("/", r)

	log.Info().Msgf("Starting HTTP server on port %d", s.port)
	return http.ListenAndServe(fmt.Sprintf(":%d", s.port), nil)
}

func getAuthorizationHeaderFromRequest(r *http.Request) (secretstring.SecretString, error) {
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		log.Warn().Msgf("No Authorization header found in request")
		return secretstring.SecretString{}, status.Error(codes.Unauthenticated, "No Authorization header found in request")
	}
	suffix, ok := strings.CutPrefix(authHeader, "Bearer ")
	if !ok {
		log.Warn().Msgf("Authorization header does not use Bearer prefix")
		return secretstring.SecretString{}, status.Error(codes.Unauthenticated, "Authorization header does not use Bearer prefix")
	}
	return secretstring.New(suffix), nil
}

func (s *WebServer) checkAuth(w http.ResponseWriter, r *http.Request) error {
	authToken, err := getAuthorizationHeaderFromRequest(r)
	if err != nil {
		log.Warn().Msgf("Failed to get Authorization header from request: %v", err)
		http.Error(w, err.Error(), http.StatusUnauthorized)
		return err
	}
	if strings.Contains(authToken.Expose(), ".") {
		claims, err := s.serviceAuth.ValidateAccess(r.Context(), authToken)
		if err != nil {
			log.Warn().Msgf("Failed to validate auth token: %v", err)
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return err
		}
		_, ok := claims.GetIapEmail()
		if !ok {
			err = status.Error(codes.Unauthenticated, "Only IAP users are allowed to access this endpoint")
			http.Error(w, "Only IAP users are allowed to access this endpoint", http.StatusUnauthorized)
			return err
		}
	} else {
		requestContext := requestcontext.New(
			requestcontext.RequestId(r.Header.Get("x-request-id")),
			requestcontext.RequestSessionId(r.Header.Get("x-request-session-id")),
			"grpc-debug",
			secretstring.SecretString{},
		)
		resp, err := s.authQueryClient.GetTokenInfo(r.Context(), authToken, "grpc-debug", requestContext)
		if err != nil {
			log.Warn().Msgf("Failed to get token info: %v", err)
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return err
		}
		t, err := s.tokenExchangeClient.GetSignedTokenForUser(r.Context(), resp.UserId, resp.OpaqueUserId, resp.UserEmail, resp.TenantId, nil, nil)
		if err != nil {
			log.Warn().Msgf("Failed to get service token: %v", err)
			http.Error(w, err.Error(), http.StatusUnauthorized)
			return err
		}
		r.Header.Set("Authorization", "Bearer "+t.Expose())
	}
	return nil
}

func (s *WebServer) getEndpoints(w http.ResponseWriter, r *http.Request) {
	if err := s.checkAuth(w, r); err != nil {
		return
	}
	endpoints, err := s.invoker.GetEndpoints(r.Context())
	if err != nil {
		log.Error().Err(err).Msg("Failed to get endpoints")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(endpoints)
}

func (s *WebServer) getServices(w http.ResponseWriter, r *http.Request) {
	if err := s.checkAuth(w, r); err != nil {
		return
	}
	endpoint := r.URL.Query().Get("endpoint")
	if endpoint == "" {
		log.Warn().Msgf("No endpoint parameter in request")
		http.Error(w, "endpoint parameter is required", http.StatusBadRequest)
		return
	}
	services, err := s.invoker.GetServices(r.Context(), endpoint)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get services")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(services)
}

func (s *WebServer) getMethods(w http.ResponseWriter, r *http.Request) {
	if err := s.checkAuth(w, r); err != nil {
		return
	}
	endpoint := r.URL.Query().Get("endpoint")
	service := r.URL.Query().Get("service")
	if endpoint == "" {
		log.Warn().Msgf("endpoint and service parameters are required")
		http.Error(w, "endpoint and service parameters are required", http.StatusBadRequest)
		return
	}
	methods, err := s.invoker.GetServiceMethods(r.Context(), endpoint, service)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get service methods")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(methods)
}

func (s *WebServer) invokeMethod(w http.ResponseWriter, r *http.Request) {
	if err := s.checkAuth(w, r); err != nil {
		return
	}
	var request struct {
		Endpoint string          `json:"endpoint"`
		Service  string          `json:"service"`
		Method   string          `json:"method"`
		Data     json.RawMessage `json:"data"`
	}
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		log.Warn().Msgf("Failed to decode request body: %v", err)
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	ctx := r.Context()
	ctx = metadata.NewIncomingContext(ctx,
		metadata.Pairs("x-request-id", r.Header.Get("x-request-id"),
			"x-request-session-id", r.Header.Get("x-request-session-id"),
			"authorization", r.Header.Get("Authorization")))
	result, err := s.invoker.InvokeMethod(ctx, request.Endpoint, request.Service, request.Method, string(request.Data))
	if err != nil {
		log.Error().Err(err).Msg("Failed to invoke method")
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(result)
}

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import json\n", "import requests\n", "import uuid"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get the dev namespace"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["with pathlib.Path.home().joinpath(\".augment\", \"user.json\").open(\n", "    encoding=\"utf-8\"\n", ") as user_file:\n", "    user_data = json.load(user_file)\n", "    user_name = user_data[\"name\"]\n", "    dev_namespace = f\"dev-{user_name}\"\n", "api_proxy = f\"https://{dev_namespace}.us-central.api.augmentcode.com\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get the token\n", "\n", "The grpc debug service can use service tokens or api tokens.\n", "\n", "Get a service token via `bazel run //services/token_exchange/util -- --tenant-name augment --cloud GCP_US_CENTRAL1_DEV --shard-namespace [NAMESPACE]`."]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["token = pathlib.Path.home().joinpath(\".augment/service_token.txt\").read_text().strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## gRPC Debug Demo\n", "\n", "### Get all endpoints"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r = requests.get(\n", "    f\"{api_proxy}/grpc-debug/endpoints\", headers={\"Authorization\": f\"Bearer {token}\"}\n", ")\n", "r.raise_for_status()\n", "for endpoint in r.json():\n", "    print(f\"{endpoint['name']}: {endpoint['url']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get all services and methods"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r = requests.get(\n", "    f\"{api_proxy}/grpc-debug/services\",\n", "    params={\"endpoint\": \"https://doc-sets-svc:50051\"},\n", "    headers={\"Authorization\": f\"Bearer {token}\"},\n", ")\n", "r.raise_for_status()\n", "print(\"Services:\")\n", "for service in r.json():\n", "    print(f\"{service['name']}\")\n", "\n", "\n", "r = requests.get(\n", "    f\"{api_proxy}/grpc-debug/methods\",\n", "    params={\"endpoint\": \"https://doc-sets-svc:50051\"},\n", "    headers={\"Authorization\": f\"Bearer {token}\"},\n", ")\n", "r.raise_for_status()\n", "print()\n", "print(\"Methods:\")\n", "for method in r.json():\n", "    print(\n", "        f\"{method['fully_qualified_name']}(): {method['input_type']['name']} -> {method['output_type']['name']}\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Invoke an grpc method"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r = requests.post(\n", "    f\"{api_proxy}/grpc-debug/invoke\",\n", "    json={\n", "        \"endpoint\": \"https://doc-sets-svc:50051\",\n", "        \"service\": \"docset.DocSetService\",\n", "        \"method\": \"GetDocSets\",\n", "        \"data\": {\"search_pattern\": \"graphite\"},\n", "    },\n", "    headers={\n", "        \"Authorization\": f\"Bearer {token}\",\n", "        \"x-request-id\": str(uuid.uuid4()),\n", "        \"x-request-session-id\": str(uuid.uuid4()),\n", "    },\n", ")\n", "r.raise_for_status()\n", "if \"error\" in r.json():\n", "    print(r.json()[\"error\"])\n", "else:\n", "    print(r.json()[\"response_json\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}
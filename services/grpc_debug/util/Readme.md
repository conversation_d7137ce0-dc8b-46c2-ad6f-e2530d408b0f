# Grpc Debug Util

A simple CLI to access the grpc debug service for debugging and manual testing.

## Usage

- bazel run //services/grpc_debug/util -- --namespace dev-dirk --cloud GCP_US_CENTRAL1_DEV --request-token --tenant-name augment endpoints

- bazel run //services/grpc_debug/util -- --namespace dev-dirk --cloud GCP_US_CENTRAL1_DEV --request-token --tenant-name augment services --service-endpoint https://doc-sets-svc:50051

- bazel run //services/grpc_debug/util -- --namespace dev-dirk --cloud GCP_US_CENTRAL1_DEV --request-token --tenant-name augment invoke --service-endpoint https://doc-sets-svc:50051 --service-name docset.DocSetService --method GetDocSets --json '{"search_pattern": "graphite"}'

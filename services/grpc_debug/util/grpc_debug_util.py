"""CLI Utility to access the grpc debug service."""

import argparse
import itertools
import json
import logging
import pathlib
import sys
import time
import typing

import pydantic
import binascii

from base.logging.console_logging import setup_console_logging
from services.lib.grpc import grpc_args_parser, token_parser
from services.lib.request_context.request_context import RequestContext
from services.grpc_debug.client import client as grpc_debug_client


def _get_request_context(token: pydantic.SecretStr):
    return RequestContext.create(auth_token=token)


def _get_endpoints(
    rpc_client: grpc_debug_client.GrpcDebugClient,
    args,
    token: pydantic.SecretStr,
):
    """Get the list of endpoints."""
    del args
    request_context = _get_request_context(token)
    response = rpc_client.get_endpoints(request_context=request_context)
    for endpoint in response:
        logging.info("%s: %s", endpoint.name, endpoint.url)


def _get_services(
    rpc_client: grpc_debug_client.GrpcDebugClient,
    args,
    token: pydantic.SecretStr,
):
    """Get the list of services."""
    request_context = _get_request_context(token)
    response = rpc_client.get_services(
        endpoint=args.service_endpoint, request_context=request_context
    )
    for service in response:
        logging.info("%s", service.name)


def _get_methods(
    rpc_client: grpc_debug_client.GrpcDebugClient,
    args,
    token: pydantic.SecretStr,
):
    """Get the list of methods."""
    request_context = _get_request_context(token)
    response = rpc_client.get_service_methods(
        endpoint=args.service_endpoint,
        request_context=request_context,
        service_name=args.service_name,
    )
    for method in response:
        logging.info(
            "%s(): %s -> %s",
            method.fully_qualified_name,
            method.input_type.name,
            method.output_type.name,
        )


def _invoke(
    rpc_client: grpc_debug_client.GrpcDebugClient,
    args,
    token: pydantic.SecretStr,
):
    """Invoke a method."""
    json_data = None
    if args.json:
        if args.file:
            raise Exception("Cannot specify both --json and --file")
        try:
            json.loads(args.json)
        except json.JSONDecodeError:
            raise Exception("Invalid JSON")

        json_data = args.json
    elif args.file:
        if args.json:
            raise Exception("Cannot specify both --json and --file")
        try:
            json.loads(args.file.read_text())
        except json.JSONDecodeError:
            raise Exception("Invalid JSON")
        json_data = args.file.read_text()
    else:
        raise Exception("Must specify either --json or --file")

    request_context = _get_request_context(token)
    response = rpc_client.invoke_method(
        endpoint=args.service_endpoint,
        service_name=args.service_name,
        method_name=args.method_name,
        request_json=json_data,
        request_context=request_context,
    )
    if response.error and response.error.code != 0:  # type: ignore
        raise Exception(response.error.message)  # type: ignore
    print(response.response_json)


def main():
    """Main function."""
    setup_console_logging(add_timestamp=False)
    parser = argparse.ArgumentParser()
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)
    subparsers = parser.add_subparsers()
    endpoints_parser = subparsers.add_parser("endpoints")
    endpoints_parser.set_defaults(action="endpoints")

    services_parser = subparsers.add_parser("services")
    services_parser.set_defaults(action="services")
    services_parser.add_argument("--service-endpoint", required=True)

    methods_parser = subparsers.add_parser("methods")
    methods_parser.set_defaults(action="methods")
    methods_parser.add_argument("--service-endpoint", required=True)
    methods_parser.add_argument("--service-name", required=True)

    invoke_parser = subparsers.add_parser("invoke")
    invoke_parser.set_defaults(action="invoke")
    invoke_parser.add_argument("--service-endpoint", required=True)
    invoke_parser.add_argument("--service-name", required=True)
    invoke_parser.add_argument("--method-name", required=True)
    invoke_parser.add_argument("--json")
    invoke_parser.add_argument("--file", type=pathlib.Path)

    parser.set_defaults(action=None)

    args = parser.parse_args()
    try:
        with grpc_args_parser.create_client(
            args,
            grpc_debug_client.GrpcDebugClient.create_for_endpoint,
            default_service_name="grpc-debug-svc",
            default_endpoint="grpc-debug-svc:50051",
        ) as rpc_client:
            token = token_parser.get_token(args)
            if args.action == "endpoints":
                _get_endpoints(rpc_client, args, token)
            elif args.action == "services":
                _get_services(rpc_client, args, token)
            elif args.action == "methods":
                _get_methods(rpc_client, args, token)
            elif args.action == "invoke":
                _invoke(rpc_client, args, token)
            else:
                sys.exit(1)
    except KeyboardInterrupt:
        sys.exit(1)
    except argparse.ArgumentError as e:
        logging.error("%s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()

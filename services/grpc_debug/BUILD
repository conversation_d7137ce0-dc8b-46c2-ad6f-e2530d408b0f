load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "grpc_debug_proto",
    srcs = ["grpc_debug.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:descriptor_proto",
    ],
)

py_grpc_library(
    name = "grpc_debug_py_proto",
    protos = [":grpc_debug_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

go_grpc_library(
    name = "grpc_debug_go_proto",
    importpath = "github.com/augmentcode/augment/services/grpc_debug/proto",
    proto = ":grpc_debug_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
        "@org_golang_google_protobuf//types/descriptorpb:go_default_library",
    ],
)

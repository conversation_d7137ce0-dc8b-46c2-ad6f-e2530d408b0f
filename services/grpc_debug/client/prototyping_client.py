"""A client to access Augment API and gRPC endpoints for experimentation.

This client can be used in dev deployments and in staging, and is meant for
prototyping and experimentation. It is not meant to access production tenants.

The client provides support for common endpoints, sending them either to the
public API or to internal gRPC endpoints as needed.

## Authentication

Setting up the client requires an authentication token. It is recommended to use
the public API token. For gRPC access, a service token can also be used, but this
will not work with the public API.

If an auth token is not provided, it is read from ~/.augment/token .

## gRPC details

The hierarchy of gRPC calls is `host -> service -> method`. A host could be a chat host,
a docset host, etc. The methods provide the actual functionality, like FindMissing.
The schema for each method can be found in the relevant .proto file. For example,
the chat methods are defined in `services/chat_host/chat.proto`.

Access to gRPC endpoints is provided via the `grpc_debug` service.
See `services/grpc_debug/README.md` for more details.
"""

import json
import requests
import uuid
from dataclasses import dataclass
from functools import cached_property
from pathlib import Path
from typing import Optional, Iterable

from google.protobuf import json_format
from pydantic import SecretStr

from base.blob_names.python.blob_names import get_blob_name
from base.blob_names import blob_names_pb2  # type: ignore
from services.content_manager import content_manager_pb2
from services.chat_host import chat_pb2


@dataclass(frozen=True)
class UploadContent:
    """The contents of an uploaded blob."""

    content: bytes
    path: str

    @cached_property
    def blob_name(self):
        return get_blob_name(self.path, self.content)


@dataclass
class UploadResult:
    """The result of uploading a blob."""

    existed: bool
    blob_name: str


@dataclass
class RetrievedChunk:
    text: str
    path: str
    char_start: int
    char_end: int
    blob_name: str

    @staticmethod
    def from_proto(proto: chat_pb2.RetrievedChunk):
        return RetrievedChunk(
            text=proto.text,
            path=proto.path,
            char_start=proto.char_start,
            char_end=proto.char_end,
            blob_name=proto.blob_name,
        )


def get_dev_deployment_api_proxy_url(
    user_name: Optional[str] = None, dev_namespace: Optional[str] = None
) -> str:
    """Get the API proxy URL for a dev deployment.

    By default, returns the URL for the current user's dev deployment.
    """
    assert not (
        user_name and dev_namespace
    ), "Only one of user_name and dev_namespace can be set"
    if not dev_namespace:
        if not user_name:
            with Path.home().joinpath(".augment", "user.json").open(
                encoding="utf-8"
            ) as user_file:
                user_data = json.load(user_file)
                user_name = user_data["name"]
        dev_namespace = f"dev-{user_name}"
    return f"https://{dev_namespace}.us-central.api.augmentcode.com"


def get_staging_api_proxy_url(staging_namespace: Optional[str] = None) -> str:
    """Get the API proxy URL for a staging namespace.

    By default, uses the Augment dogfooding.
    """
    if staging_namespace:
        return f"https://{staging_namespace}.api.augmentcode.com"
    return "https://staging-shard-0.api.augmentcode.com"


DEFAULT_CHAT_HOST = "chat-chatanol3-third-party-chat-svc"
DEFAULT_CHAT_MODEL = "claude-sonnet-3-5-16k-v11-2"


class AugmentPrototypingClient:
    """A client to access Augment services for experimentation.

    See module docstring for more details.
    """

    def __init__(
        self,
        api_proxy_url: str,
        auth_token: Optional[str] = None,
        chat_host: str = DEFAULT_CHAT_HOST,
        chat_model: str = DEFAULT_CHAT_MODEL,
        timeout: int = 60,
    ):
        """Construct a prototyping client.

        The API proxy URL can be obtained from the helper methods:
        - get_dev_deployment_api_proxy_url()
        - get_staging_api_proxy_url()

        The auth token can be an API token or a service token. By default, the
        user token will be read from ~/.augment/token.
        """
        # Get the dev namespace
        self.api_proxy_url = api_proxy_url

        if auth_token:
            self.auth_token = SecretStr(auth_token)
        else:
            self.auth_token = SecretStr(
                Path.home()
                .joinpath(".augment/token")
                .read_text(encoding="utf-8")
                .strip()
            )

        self.chat_host = chat_host
        self.chat_model = chat_model
        self.session_id = str(uuid.uuid4())
        self.timeout = timeout
        self._cached_host_to_host_url: dict[str, str] = {}

    def upload_blobs_as_needed(
        self, blobs: Iterable[UploadContent], max_batch_size: int = 100
    ) -> list[UploadContent]:
        """Checks which blobs don't exist on the server and uploads them.

        Returns the list of blob names that were actually uploaded: the ones
        that weren't already on the server.
        """
        blob_names = [blob.blob_name for blob in blobs]
        missing_blob_names = set(self.find_missing(blob_names))
        missing_blobs = [blob for blob in blobs if blob.blob_name in missing_blob_names]
        self.batch_upload(missing_blobs, max_batch_size=max_batch_size)
        return missing_blobs

    def find_missing(self, blob_names: Iterable[str]) -> list[str]:
        request = content_manager_pb2.FindMissingBlobRequest()
        request.blob_names.extend(blob_names)
        response = self._grpc_invoke_proto(
            host="content-manager-svc",
            service="content_manager.ContentManager",
            method="FindMissingBlobs",
            input_proto=request,
            output_proto=content_manager_pb2.FindMissingBlobResponse(),
        )
        assert isinstance(response, content_manager_pb2.FindMissingBlobResponse)
        return [str(blob_name) for blob_name in response.unknown_blob_names]

    def batch_upload(
        self, blobs: list[UploadContent], max_batch_size: int = 100
    ) -> list[UploadResult]:
        """Upload blobs in batches, returning the result for each blob."""
        priority = content_manager_pb2.IndexingPriority.DEFAULT
        result: list[UploadResult] = []

        for start_idx in range(0, len(blobs), max_batch_size):
            request = content_manager_pb2.BatchUploadBlobContentRequest(
                priority=priority,
            )
            blob_upload_contents = []
            for blob in blobs[start_idx : start_idx + max_batch_size]:
                blob_upload_content = content_manager_pb2.UploadBlobContent(
                    content=blob.content
                )
                new_metadata = blob_upload_content.metadata.add()
                new_metadata.key = "path"
                new_metadata.value = blob.path
                blob_upload_contents.append(blob_upload_content)
            request.entries.extend(blob_upload_contents)
            response = self._grpc_invoke_proto(
                host="content-manager-svc",
                service="content_manager.ContentManager",
                method="BatchUploadBlobContent",
                input_proto=request,
                output_proto=content_manager_pb2.BatchUploadBlobContentResponse(),
            )
            assert isinstance(
                response, content_manager_pb2.BatchUploadBlobContentResponse
            )
            result.extend(
                [
                    UploadResult(result.existed, result.blob_name)
                    for result in response.results
                ]
            )
        return result

    def chat(
        self,
        message: str,
        blob_names: Iterable[str],
        model_name: Optional[str] = None,
    ) -> str:
        if not model_name:
            model_name = self.chat_model
        blobs = self._get_blobs(blob_names)
        request = chat_pb2.ChatRequest(
            model_name=model_name,
            message=message,
            blobs=[blobs],
        )
        response = self._grpc_invoke_proto(
            host=self.chat_host,
            service="chat.Chat",
            method="Chat",
            input_proto=request,
            output_proto=chat_pb2.ChatResponse(),
        )
        assert isinstance(response, chat_pb2.ChatResponse)
        return response.text

    def chat_retrieval(
        self, query: str, blob_names: Iterable[str], max_chunks: int
    ) -> list[RetrievedChunk]:
        blobs = self._get_blobs(blob_names)
        request = chat_pb2.ChatRetrievalRequest(
            model_name=self.chat_model,
            query=query,
            blobs=[blobs],
            max_chunks=max_chunks,
        )
        response = self._grpc_invoke_proto(
            host=self.chat_host,
            service="chat.Chat",
            method="ChatRetrieval",
            input_proto=request,
            output_proto=chat_pb2.ChatRetrievalResponse(),
        )
        assert isinstance(response, chat_pb2.ChatRetrievalResponse)
        return [RetrievedChunk.from_proto(chunk) for chunk in response.retrieved_chunks]

    ### gRPC inspection

    def get_grpc_hosts(self) -> list[str]:
        """Returns the available hosts."""
        r = self._grpc_get("grpc-debug/endpoints", params={})
        print("Endpoints answer:")
        print(json.dumps(r.json(), indent=2))
        return [host["name"] for host in r.json()]

    def get_grpc_services(self, host: str) -> list[str]:
        r = self._grpc_get(
            "grpc-debug/services", params={"endpoint": self._get_host_url(host)}
        )
        return [service["name"] for service in r.json()]

    def get_grpc_methods(self, host: str, service: str) -> list[str]:
        r = self._grpc_get(
            "grpc-debug/methods",
            params={"endpoint": self._get_host_url(host), "service": service},
        )
        return [method["method_name"] for method in r.json()]

    def print_all_grpc_methods(self):
        for host in self.get_grpc_hosts():
            print(f"\n{host}  [host]")
            for service in self.get_grpc_services(host):
                print(f"    {service}  [service]")
                for method in self.get_grpc_methods(host, service):
                    print(f"        {method}  [method]")

    ### Private methods

    def _get_blobs(self, blob_names: Iterable[str]) -> blob_names_pb2.Blobs:
        return blob_names_pb2.Blobs(
            added=[bytes.fromhex(blob) for blob in sorted(blob_names)],
            deleted=[],
        )

    def _grpc_invoke_proto(
        self,
        host: str,
        service: str,
        method: str,
        input_proto,
        output_proto,
        single_output_proto: bool = True,
    ):
        """Invoke a gRPC method in a structued way, using input and output protos.

        Args:
            host: The host to invoke the method on.
            service: The service to invoke the method on.
            method: The method to invoke.
            input_proto: The input proto.
            output_proto: The output proto indicating the type.
                A new copy will be returned.
            single_output_proto: If True, the output proto is a single proto, and is returned.
                If False, the output is a list of output protos.
                A list of protos is typically returned for streaming APIs.

        Returns:
            The output proto or list of output protos.
        """
        data = json_format.MessageToDict(input_proto)
        response_str = self._grpc_invoke(host, service, method, data)
        if single_output_proto:
            response = json.loads(response_str)
            assert (
                len(response) == 1
            ), f"Expected single output proto, got {len(response)}"
            return json_format.ParseDict(response[0], output_proto)
        else:
            return [
                json_format.ParseDict(response, output_proto)
                for response in json.loads(response_str)
            ]

    def _grpc_invoke(self, host: str, service: str, method: str, data: dict) -> str:
        """Invoke a non-streaming gRPC method via grpc_debug.

        Accepts python datastructure as input, and returns a string."""
        r = requests.post(
            f"{self.api_proxy_url}/grpc-debug/invoke",
            json={
                "endpoint": self._get_host_url(host),
                "service": service,
                "method": method,
                "data": data,
            },
            headers=self._get_request_headers(),
            timeout=self.timeout,
        )
        r.raise_for_status()
        if "error" in r.json():
            raise ValueError(f"gRPC method invocation failed: {r.json()['error']}")
        return r.json()["response_json"]

    def _grpc_get(self, url_suffix: str, params: dict):
        response = requests.get(
            f"{self.api_proxy_url}/{url_suffix}",
            headers=self._get_request_headers(),
            timeout=self.timeout,
            params=params,
        )
        response.raise_for_status()
        return response

    def _get_request_headers(self):
        request_id = str(uuid.uuid4())
        return {
            "Authorization": f"Bearer {self.auth_token.get_secret_value()}",
            "X-Request-id": request_id,
            "X-Request-Session-Id": self.session_id,
        }

    def _get_host_url(self, host: str) -> str:
        """Returns the URL for a host."""
        if host not in self._cached_host_to_host_url:
            r = self._grpc_get("grpc-debug/endpoints", params={})
            for host_data in r.json():
                self._cached_host_to_host_url[host_data["name"]] = host_data["url"]

        return self._cached_host_to_host_url[host]

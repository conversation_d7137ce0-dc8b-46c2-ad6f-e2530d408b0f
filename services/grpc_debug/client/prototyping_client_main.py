"""An example of how to use the Augment prototyping client."""

from prototyping_client import (
    AugmentPrototypingClient,
    UploadContent,
    get_dev_deployment_api_proxy_url,
    get_staging_api_proxy_url,
)


def main():
    # client = AugmentPrototypingClient(get_dev_deployment_api_proxy_url())
    client = AugmentPrototypingClient(get_staging_api_proxy_url())

    # client.print_all_grpc_methods()

    response = client.find_missing(["1234"])
    print("find_missing response:", response)

    data = [
        UploadContent(
            content=b"def test_health_check_2():\n    return True",
            path="augment/health_check/test.py",
        )
    ]

    print("Local blob name:")
    print(data[0].blob_name)

    upload_response = client.batch_upload(data)
    blob_names = [blob.blob_name for blob in upload_response]
    print("\nUpload response:")
    print(upload_response)

    print("Blob name:", upload_response[0].blob_name)
    chat_response = client.chat(
        "what functions are in this file?",
        blob_names=blob_names,
    )
    print("\nChat response:")
    print(chat_response)

    response = client.chat_retrieval(
        "what functions are in this file?",
        blob_names=blob_names,
        max_chunks=10,
    )

    print("\nChat retrieval response:")
    print(response)


if __name__ == "__main__":
    main()

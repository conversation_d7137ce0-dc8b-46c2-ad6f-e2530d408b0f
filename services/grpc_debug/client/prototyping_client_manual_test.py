"""A manual end-to-end test for the prototyping client.

To run the test:

bazel run //services/grpc_debug/client:prototyping_client_manual_test

We don't run this test as part of the automatic e2e tests, because we don't
want to gate production deployments on prototypes breaking. However, it is
useful to run this test manually to ensure that the client is working as
expected.
"""

import argparse
from pathlib import Path

from prototyping_client import (
    AugmentPrototypingClient,
    UploadContent,
    get_dev_deployment_api_proxy_url,
    get_staging_api_proxy_url,
)


def test_client(client: AugmentPrototypingClient):
    # Test find-missing with non-existent blob
    find_missing_response = client.find_missing(["not-a-blob"])
    assert find_missing_response == ["not-a-blob"]

    # Test uploading a blob
    data: list[UploadContent] = [
        UploadContent(
            content=b"def test_health_check_2():\n    return True",
            path="augment/health_check/test.py",
        )
    ]
    uploaded_blob_names = [blob.blob_name for blob in data]

    batch_upload_response = client.batch_upload(data)
    assert len(batch_upload_response) == len(data)

    find_missing_response2 = client.find_missing(uploaded_blob_names)
    assert find_missing_response2 == []

    # Test chat with retrieval
    chat_response = client.chat(
        "what functions are in this file?",
        blob_names=uploaded_blob_names,
    )
    assert (
        "test_health_check_2" in chat_response
    ), f"Chat response does not contain expected text: {chat_response}"

    print("All tests passed.")


def main():
    args = argparse.ArgumentParser(
        description="Run a manual test of the prototyping client. Tests against the user's dev deployment by default."
    )
    args.add_argument(
        "--staging",
        action="store_true",
        help="Test against the default staging namespace (dogfooding)",
    )
    args.add_argument(
        "--current_user_dev_deployment",
        action="store_true",
        help="Test against the current user's dev deployment (the default behavior)",
    )
    args.add_argument(
        "--staging_namespace", type=str, help="Provide a staging namespace to test"
    )
    args.add_argument(
        "--dev_deployment_namespace",
        type=str,
        help="Provide a dev deployment namespace to test, like dev-$USER",
    )
    args.add_argument(
        "--auth_token_file",
        type=str,
        help="Read the auth token from a file (could be an API token or a service token). By default, the user token will be read from ~/.augment/token.",
    )

    args = args.parse_args()

    if args.staging:
        api_proxy_url = get_staging_api_proxy_url()
    elif args.staging_namespace:
        api_proxy_url = get_staging_api_proxy_url(args.staging_namespace)
    elif args.dev_deployment_namespace:
        api_proxy_url = get_dev_deployment_api_proxy_url(args.dev_deployment_namespace)
    else:
        api_proxy_url = get_dev_deployment_api_proxy_url()

    auth_token = None
    if args.auth_token_file:
        auth_token = Path(args.auth_token_file).read_text("utf8").strip()

    client = AugmentPrototypingClient(api_proxy_url, auth_token=auth_token)
    test_client(client)


if __name__ == "__main__":
    main()

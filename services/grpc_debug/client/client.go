package client

// Package client provides a client for the grpc debug service.

import (
	"context"
	"strings"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"

	pb "github.com/augmentcode/augment/services/grpc_debug/proto"
)

type GrpcDebugClient interface {
	GetEndpoints(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*pb.Endpoint, error)
	GetServices(ctx context.Context, endpoint string, requestContext *requestcontext.RequestContext) ([]*pb.Service, error)
	GetServiceMethods(ctx context.Context, endpoint string, serviceName string, requestContext *requestcontext.RequestContext) ([]*pb.Method, error)
	InvokeMethod(ctx context.Context, endpoint string, serviceName string, methodName string, requestJson string, requestContext *requestcontext.RequestContext) (*pb.MethodInvocationResponse, error)

	// This should be called to cleanup resources for this client
	Close()
}

// GrpcDebugClientImpl implementation of the GrpcDebugClient interface.
type GrpcDebugClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	client pb.GrpcDebugClient
}

// NewGrpcDebugClient creates a new GrpcDebugClient.
//
// endpoint: The endpoint of the route guide service.
// credentials: The credentials to use for the channel (optional)
//
// Returns: The client stub for the route guide or an error if the client could not be created.
func NewGrpcDebugClient(endpoint string, credentials credentials.TransportCredentials) (GrpcDebugClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}
	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	client := pb.NewGrpcDebugClient(conn)
	return &GrpcDebugClientImpl{conn: conn, client: client}, nil
}

func (c *GrpcDebugClientImpl) GetEndpoints(ctx context.Context, requestContext *requestcontext.RequestContext) ([]*pb.Endpoint, error) {
	req := &pb.GetEndpointsRequest{}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetEndpoints(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Endpoints, nil
}

func (c *GrpcDebugClientImpl) GetServices(ctx context.Context, endpoint string, requestContext *requestcontext.RequestContext) ([]*pb.Service, error) {
	req := &pb.GetServicesRequest{Endpoint: endpoint}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetServices(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Services, nil
}

func (c *GrpcDebugClientImpl) GetServiceMethods(ctx context.Context, endpoint string, serviceName string, requestContext *requestcontext.RequestContext) ([]*pb.Method, error) {
	req := &pb.GetServiceMethodsRequest{Endpoint: endpoint, ServiceName: serviceName}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.GetServiceMethods(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Methods, nil
}

func (c *GrpcDebugClientImpl) InvokeMethod(ctx context.Context, endpoint string, serviceName string, methodName string, requestJson string, requestContext *requestcontext.RequestContext) (*pb.MethodInvocationResponse, error) {
	req := &pb.MethodInvocationRequest{Endpoint: endpoint, ServiceName: serviceName, MethodName: methodName, RequestJson: requestJson}
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	resp, err := c.client.InvokeMethod(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *GrpcDebugClientImpl) Close() {
	c.conn.Close()
}

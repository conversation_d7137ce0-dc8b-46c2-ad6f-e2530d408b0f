"""A Python client library call a grpc debug service."""

from dataclasses import dataclass
import logging
import json
from typing import Optional, <PERSON>ple
import typing

import grpc
import google.protobuf.json_format as json_format

from base.python.grpc import client_options
from services.grpc_debug import grpc_debug_pb2, grpc_debug_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> grpc_debug_pb2_grpc.GrpcDebugStub:
    """Setup the client stub for a grpc debug service.

    Args:
        endpoint: The endpoint of the route guide service.
        credentials: The credentials to use for the channel (optional)

    Returns:
        The client stub for the grpc debug.
    """
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create(options)
        )
    stub = grpc_debug_pb2_grpc.GrpcDebugStub(channel)
    return stub


class GrpcDebugClient:
    """Class to call grpc debug APIs remotely."""

    def __init__(self, stub: grpc_debug_pb2_grpc.GrpcDebugStub):
        """Constructs a new content manager."""
        self.stub = stub

    @classmethod
    def create_for_endpoint(
        cls,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Constructs a new content manager client from endpoint and credentials.

        Args:
            endpoint: The endpoint of the route guide service.
            credentials: The credentials to use for the channel (optional)
            options: The options to use for the channel (optional)

        Returns:
            The client stub for the route guide.
        """
        stub = setup_stub(endpoint, credentials, options=options)
        return cls(stub)

    def get_endpoints(
        self, request_context: RequestContext, timeout: int = 30
    ) -> typing.Sequence[grpc_debug_pb2.Endpoint]:
        """Get the list of endpoints.

        Args:
            request_context: The request context to use.

        Returns:
            The list of endpoints.
        """
        response = self.stub.GetEndpoints(
            grpc_debug_pb2.GetEndpointsRequest(),
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.endpoints

    def get_services(
        self, endpoint: str, request_context: RequestContext, timeout: int = 30
    ) -> typing.Sequence[grpc_debug_pb2.Service]:
        """Get the list of services.

        Args:
            endpoint: The endpoint to use.
            request_context: The request context to use.

        Returns:
            The list of services.
        """
        request = grpc_debug_pb2.GetServicesRequest(endpoint=endpoint)
        response = self.stub.GetServices(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.services

    def get_service_methods(
        self,
        endpoint: str,
        service_name: str | None,
        request_context: RequestContext,
        timeout: int = 30,
    ) -> typing.Sequence[grpc_debug_pb2.Method]:
        """Get the list of methods for a service.

        Args:
            endpoint: The endpoint to use.
            service_name: The service name to use.
            request_context: The request context to use.

        Returns:
            The list of methods.
        """
        request = grpc_debug_pb2.GetServiceMethodsRequest(
            endpoint=endpoint, service_name=service_name or ""
        )
        response = self.stub.GetServiceMethods(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response.methods

    def invoke_method(
        self,
        endpoint: str,
        service_name: str,
        method_name: str,
        request_json: str,
        request_context: RequestContext,
        timeout: int = 30,
    ) -> grpc_debug_pb2.MethodInvocationResponse:
        """Invoke a method.

        Args:
            endpoint: The endpoint to use.
            service_name: The service name to use.
            method_name: The method name to use.
            request_json: The request json to use.
            request_context: The request context to use.

        Returns:
            The response json.
        """
        request = grpc_debug_pb2.MethodInvocationRequest(
            endpoint=endpoint,
            service_name=service_name,
            method_name=method_name,
            request_json=request_json,
        )
        response = self.stub.InvokeMethod(
            request,
            timeout=timeout,
            metadata=request_context.to_metadata(),
        )
        return response


@dataclass
class ProxyMethod:
    """Class to call proxy APIs remotely."""

    service_name: str
    method_name: str
    response_type: type
    response_stream: bool = False

    def generate(self, stub: GrpcDebugClient, endpoint: str):
        """Generate the method."""

        def invoke(request, metadata: list[Tuple[str, str]]):
            request_context = RequestContext.from_metadata(metadata)
            data = json_format.MessageToJson(request)
            response: grpc_debug_pb2.MethodInvocationResponse = stub.invoke_method(
                endpoint=endpoint,
                service_name=self.service_name,
                method_name=self.method_name,
                request_json=data,
                request_context=request_context,
            )
            if response.error and response.error.code != 0:  # type: ignore
                raise grpc.RpcError(response.error.code, response.error.message)  # type: ignore
            data = json.loads(response.response_json)
            print(f"Response: {data}")
            if self.response_stream:
                result = []
                for r in data:
                    result.append(json_format.ParseDict(r, self.response_type()))
                return result
            else:
                first_result = next(iter(data))
                return json_format.ParseDict(first_result, self.response_type())

        return invoke


class ProxyClient:
    """Class to call proxy APIs remotely."""

    def __init__(
        self,
        stub: GrpcDebugClient,
        endpoint: str,
        methods: list[ProxyMethod],
    ):
        """Constructs a new content manager."""
        self.stub = stub
        self.endpoint = endpoint
        self.methods = methods
        for method in methods:
            fn = method.generate(stub, endpoint)
            setattr(self, method.method_name, fn)

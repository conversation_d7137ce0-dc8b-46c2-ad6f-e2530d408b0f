load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/grpc_debug:grpc_debug_py_proto",
        "//services/lib/request_context:request_context_py",
        requirement("grpcio"),
        requirement("protobuf"),
    ],
)

py_library(
    name = "prototyping_client",
    srcs = ["prototyping_client.py"],
    visibility = ["//experimental:__subpackages__"],
    deps = [
        requirement("pydantic"),
        requirement("requests"),
        requirement("protobuf"),
        "//base/blob_names/python:blob_names",
        "//services/chat_host:chat_proto_py_proto",
        "//services/content_manager:content_manager_py_proto",
    ],
)

py_binary(
    name = "prototyping_client_bin",
    srcs = ["prototyping_client_main.py"],
    visibility = ["//experimental:__subpackages__"],
    deps = [
        ":prototyping_client",
    ],
)

py_binary(
    name = "prototyping_client_manual_test",
    srcs = ["prototyping_client_manual_test.py"],
    visibility = ["//experimental:__subpackages__"],
    deps = [
        ":prototyping_client",
    ],
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/grpc_debug/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/grpc_debug:grpc_debug_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

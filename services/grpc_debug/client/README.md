# Augment Prototyping Client

The Augment Prototyping Client is a Python client designed to access Augment
services for experimentation and prototyping. It provides a convenient way to
interact with various Augment API endpoints and gRPC services in development and
staging environments.

## Features

- Access to internal gRPC endpoints via dev deployments or staging (dogfooding)
- Authentication handling
- Blob upload and management
- Chat functionality with retrieval

## Usage

Here's a basic example of how to use the Augment Prototyping Client:

```python
from services.grpc_debug.client.prototyping_client import (
    AugmentPrototypingClient,
    get_dev_deployment_api_proxy_url,
)

# Create a client instance
client = AugmentPrototypingClient(get_dev_deployment_api_proxy_url())

# Make a chat request
chat_response = client.chat("Hello")
```

## Authentication

The client supports authentication using either an API token or a service token. If not provided, it will attempt to read the token from `~/.augment/token`.

## Testing

To run the manual end-to-end test for the prototyping client:

```bash
bazel run //services/grpc_debug/client:prototyping_client_manual_test
```

By default, this test runs against dogfooding performs various operations to ensure the client is working as expected.

## Additional Resources

For more detailed examples and usage scenarios, refer to:

- `services/grpc_debug/client/prototyping_client_main.py`
- `services/grpc_debug/client/prototyping_client_manual_test.py`

## Notes

- This client is intended for prototyping and experimentation. It is not meant for production use or to access production tenants.
- When using the client, be mindful of the environment you're connecting to (dev or staging) and use appropriate API proxy URLs.

# gRPC Debug

This service is used to debug gRPC services.
It provides a gRPC service to get a list of endpoints, services and methods.
It also allows to invoke methods based on JSON input and JSON output.

It is exposed via the support UI as web UI and via a HTTP/JSON interface.

This is a tool for debugging and development. It is under no circumstances
a tool to build production systems on top.

## HTTP/JSON interface

The following endpoints are available:

* /grpc-debug/endpoints: Get the list of endpoints.
* /grpc-debug/services: Get the list of services.
* /grpc-debug/methods: Get the list of methods for a service.
* /grpc-debug/invoke: Invoke a method.

Add `Authorization: Bearer <token>` to the request to authenticate.
The token can be a service token or an api token.
The service token can be generated via `bazel run //services/token_exchange/util -- --tenant-name augment --cloud GCP_US_CENTRAL1_DEV --shard-namespace [NAMESPACE]`

see `demo.ipynb` for an example of how to use the service.


## Security

The system is integrated into the service token system, i.e. without a token
it is not possible to access the service and only the information that are
accessible for a given scope can be used.

The grpc debug service can use service tokens or api tokens.
When an api token is used, it will authenticate the user via auth query and then
generate a user token with the user scope (e.g. CONTENT_RW)

The service is only available in the dev namespace and in staging

The service is not made available in production namespaces as the Audit logging
is currently only available via the support UI and not via audit logging in the endpoint.
Thus, at this point, the grpc debug service could be used to bypass the audit logging.

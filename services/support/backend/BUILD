load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")

py_binary(
    name = "backend",
    srcs = [
        "api.py",
        "app.py",
        "config.py",
    ],
    data = ["//services/support/frontend"],
    main = "app.py",
    deps = [
        requirement("gunicorn"),
        requirement("prometheus_flask_exporter"),
        requirement("prometheus_client"),
        requirement("flask"),
        requirement("structlog"),
        requirement("protobuf"),
        requirement("python-dateutil"),
        requirement("kubernetes"),
        "//base/cloud/iap:iap_py",
        "//base/feature_flags:feature_flags_py",
        "//base/flask_util:flask_util_py",
        "//base/logging:struct_logging",
        "//base/logging/audit:audit_py",
        "//base/python/signal_handler",
        "//services/api_proxy/client:grpc_client",
        "//services/auth/central/client:auth_client_py",
        "//services/chat_host:client",
        "//services/completion_host:client",
        "//services/content_manager/client",
        "//services/edit_host:client",
        "//services/grpc_debug/client:client_py",
        "//services/next_edit_host:client",
        "//services/request_insight/central/client:request_insight_central_client_py",
        "//services/tenant_watcher/client",
        "//services/token_exchange/client:client_py",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":backend",
    visibility = ["//services/support:__subpackages__"],
)

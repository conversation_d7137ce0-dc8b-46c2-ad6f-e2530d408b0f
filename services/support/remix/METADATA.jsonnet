// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details

{
  deployment: [
    {
      name: 'support-ui-v2',
      kubecfg: {
        target: '//services/support/remix:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'central',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['jacqueline', 'liam'],
          slack_channel: '#support-ui-v2',
        },
      },
    },

    {
      name: 'support-ui-v2-monitoring',
      kubecfg: {
        target: '//services/support/remix:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
    },
  ],
}

// K8S deployment file for the Support UI
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
function(env, namespace, cloud, namespace_config)
  local appName = 'support-ui-v2';
  local isMtlsEnabled = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local centralClientCert = certLib.createCentralClientCert(
    name='support-ui-v2-central-client-certificate',
    appName=appName,
    namespace=namespace,
    env=env,
    volumeName='central-client-certs',
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace),
  );
  local ingressHostname = endpoints.getSupportUiV2Hostname(env, namespace, cloud);
  local ingressFacingCert = certLib.createPublicServerCert(name='support-ui-v2-public-server-certificate',
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           env=env,
                                                           volumeName='https-certs');
  local backendConfig = gcpLib.createBackendConfig(app=appName,
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTP',
                                                     requestPath: '/health',
                                                   },
                                                   iap=true);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: 'support-ui-v2-svc',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'public-http': 'HTTP' }),
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 80,
          name: 'public-http',
          targetPort: 'public-http',
        },
      ],
    },
  };
  local config = {
    apiVersion: 'v1',
    kind: 'ConfigMap',
    metadata: {
      name: 'support-ui-v2-config',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/match': 'true',
      },
    },
    data: {
      PORT: '5000',
      NODE_ENV: 'production',
      IS_MTLS_ENABLED: '%s' % isMtlsEnabled,
      NAMESPACE: namespace,
      CURRENT_CLOUD: '%s' % cloud,
      CENTRAL_CA_CERT: '/central-client-certs/ca.crt',
      CENTRAL_CLIENT_KEY: '/central-client-certs/tls.key',
      CENTRAL_CLIENT_CERT: '/central-client-certs/tls.crt',
      TOKEN_EXCHANGE_ENDPOINT: '%s' % endpoints.getTokenExchangeGrpcUrl(env=env, cloud=cloud, namespace=namespace),
      TENANT_WATCHER_ENDPOINT: '%s' % endpoints.getTenantWatcherGrpcUrl(env=env, cloud=cloud, namespace=namespace),
      AUTH_CENTRAL_ENDPOINT: '%s:50051' % endpoints.getAuthCentralGrpcUrl(env=env, cloud=cloud, namespace=namespace),
      REQUEST_INSIGHT_CENTRAL_US_ENDPOINT: '%s' % endpoints.getRequestInsightCentralGrpcUrl(env=env, location='us'),
      REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT: '%s' % endpoints.getRequestInsightCentralGrpcUrl(env=env, location='eu'),
    },
  };
  local container =
    {
      name: 'support-ui-v2',
      target: {
        name: '//services/support/remix:image',
        dst: 'support-ui-v2',
      },
      ports: [
        {
          containerPort: 5000,
          name: 'public-http',
        },
      ],
      volumeMounts: [
        centralClientCert.volumeMountDef,
        ingressFacingCert.volumeMountDef,
      ],
      envFrom: [
        { configMapRef: { name: 'support-ui-v2-config' } },
      ],
      resources: {
        limits: {
          cpu: 1,
          memory: '512Mi',
        },
      },
      readinessProbe: {
        httpGet: {
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
    };
  local pod =
    {
      tolerations: tolerations,
      affinity: affinity,
      priorityClassName: cloudInfo.envToPriorityClass(env),
      containers: [
        container,
      ],
      volumes: [
        centralClientCert.podVolumeDef,
        ingressFacingCert.podVolumeDef,
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: 'support-ui-v2-ingress',
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: 'support-v2-ssl-cert',  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: 'support-ui-v2-svc',
                      port: {
                        number: 80,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];
  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: 'support-ui-v2',
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };

  lib.flatten([
    config,
    service,
    deployment,
    ingressObjects,
    centralClientCert.objects,
    ingressFacingCert.objects,
  ])

local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  // For now the support UI is very low traffic, so alert on a small absolute number of errors over
  // a 10 minute period. Note that our current metrics don't really allow us to distinguish between
  // different error codes.
  // TODO(jacqueline): Once we have more traffic, switch to something percentage-based.
  local errorSpec = {
    displayName: 'Support UI v2 Errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'warning' },
      query: |||
        sum by (cluster)(increase(au_http_requests_total{status=~"^[4,5]XX$", app=~"support-ui-v2"}[10m])) > 10
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      errorSpec,
      'support-ui-v2-errors',
      'Support UI v2 requests return errors',
    ),
  ]

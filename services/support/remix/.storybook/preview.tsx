import React from "react";
import type { Preview } from "@storybook/react";
import { createRemixStub } from "@remix-run/testing";
import { withRouter } from "storybook-addon-remix-react-router";

import "@radix-ui/themes/styles.css";
import "../app/styles/reset.css";
import "../app/styles/toasts.css";

import { withProviders } from "./withProviders";

const withRemixStub = (Story) => {
  const RemixStub = createRemixStub([
    {
      path: "/",
      Component: Story,
    },
  ]);
  return <RemixStub />;
};

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    options: {
      storySort: {
        method: "alphabetical",
        order: ["Layouts"],
      },
    },
  },
  decorators: [withProviders, withRemixStub, withRouter],
};

export default preview;

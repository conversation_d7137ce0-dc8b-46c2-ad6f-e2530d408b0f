import React from "react";
import { Theme } from "@radix-ui/themes";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Toasts } from "../app/components/ui/Toast";

const queryClient = new QueryClient();

export const withProviders = (Story) => (
  <QueryClientProvider client={queryClient}>
    <Theme style={{ minHeight: "fit-content" }}>
      <Toasts>
        <Story />
      </Toasts>
    </Theme>
  </QueryClientProvider>
);

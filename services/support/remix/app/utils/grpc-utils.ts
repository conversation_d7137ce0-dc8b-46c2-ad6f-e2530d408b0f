import { Code } from "@connectrpc/connect";

/**
 * Converts Connect gRPC error codes to HTTP status codes
 * @param code The Connect gRPC error code
 * @returns The corresponding HTTP status code
 */
export function connectCodeToHttpStatus(code: Code): number {
  switch (code) {
    case Code.Canceled:
      return 499; // Client closed request
    case Code.Unknown:
      return 500;
    case Code.InvalidArgument:
      return 400;
    case Code.DeadlineExceeded:
      return 504;
    case Code.NotFound:
      return 404;
    case Code.AlreadyExists:
      return 409;
    case Code.PermissionDenied:
      return 403;
    case Code.ResourceExhausted:
      return 429;
    case Code.FailedPrecondition:
      return 400;
    case Code.Aborted:
      return 409;
    case Code.OutOfRange:
      return 400;
    case Code.Unimplemented:
      return 501;
    case Code.Internal:
      return 500;
    case Code.Unavailable:
      return 503;
    case Code.DataLoss:
      return 500;
    case Code.Unauthenticated:
      return 401;
    default:
      return 500;
  }
}

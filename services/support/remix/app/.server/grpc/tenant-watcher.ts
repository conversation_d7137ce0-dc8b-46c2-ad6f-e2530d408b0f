import NodeCache from "node-cache";
import { createGrpcTransport } from "@connectrpc/connect-node";
import { createClient } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { Config } from "../config";
import type {
  GetTenantsResponse,
  Tenant,
} from "~services/tenant_watcher/tenant_watcher_pb";
// eslint-disable-next-line import/no-unresolved
import { TenantWatcher } from "~services/tenant_watcher/tenant_watcher_connect";

export interface TenantWatcherClient {
  tenantFor(
    tenantId: string,
    shardNamespace: string,
  ): Promise<Tenant | undefined>;

  tenantsFor(shardNamespace: string): Promise<GetTenantsResponse>;
}

export class TenantWatcherCachingClient implements TenantWatcherClient {
  constructor(
    private readonly client = createClient(
      TenantWatcher,
      createGrpcTransport(
        Config.createGrpcTransportOptions(Config.TENANT_WATCHER_ENDPOINT),
      ),
    ),
    private readonly tenantsCache = new NodeCache({
      stdTTL: 3600,
      checkperiod: 120,
    }),
  ) {}

  protected async listen(shardNamespace: string) {
    let stop = false;

    this.tenantsCache.addListener("del", (key: string) => {
      logger.info(`Stopping tenant watcher for ${shardNamespace}`);
      stop = stop || key === shardNamespace;
    });

    for await (const tenant of this.client.watchTenants({
      shardNamespace,
    })) {
      if (stop) {
        break;
      }
      logger.info(`got tenant change for '${shardNamespace}'`);

      for (const change of tenant.tenants) {
        switch (change.type.case) {
          case "updated":
            if (change.type.value.tenant) {
              this.updateTenant(change.type.value.tenant);
            }
            break;
          case "removed":
            if (change.type.value.tenant) {
              this.removeTenant(change.type.value.tenant);
            }
            break;
        }
      }
    }
  }

  protected async getTenantsFromCache({
    shardNamespace,
  }: {
    shardNamespace: string;
  }): Promise<GetTenantsResponse> {
    if (!this.tenantsCache.has(shardNamespace)) {
      this.tenantsCache.set(
        shardNamespace,
        this.client.getTenants({
          shardNamespace,
        }),
      );
      void this.listen(shardNamespace);
    }

    return this.tenantsCache.get(shardNamespace)!;
  }

  protected async removeTenant(tenant: Tenant) {
    const tenants = await this.getTenantsFromCache(tenant);
    const idx = tenants.tenants.findIndex((t: Tenant) => t.id === tenant.id);
    if (idx !== -1) {
      tenants.tenants.splice(idx, 1);
    }
  }

  protected async updateTenant(tenant: Tenant) {
    const tenants = await this.getTenantsFromCache(tenant);
    const idx = tenants.tenants.findIndex((t: Tenant) => t.id === tenant.id);
    if (idx !== -1) {
      tenants.tenants[idx] = tenant;
    } else {
      tenants.tenants.push(tenant);
    }
  }

  async tenantFor(tenantId: string, shardNamespace: string) {
    logger.debug(`Getting tenant ${tenantId} for ${shardNamespace}`);
    const tenants = await this.getTenantsFromCache({ shardNamespace });
    return tenants.tenants.find((t: Tenant) => t.id === tenantId);
  }

  async tenantsFor(shardNamespace: string) {
    logger.debug(`Getting tenants for ${shardNamespace}`);
    return this.getTenantsFromCache({ shardNamespace });
  }
}

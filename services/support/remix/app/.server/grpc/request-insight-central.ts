import { logger } from "@augment-internal/logging";
import { createGrpcTransport } from "@connectrpc/connect-node";
import { type CallOptions, createClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { Config } from "../config";
import { CachingClient } from "./caching-client";

/* eslint-disable import/no-unresolved */
import {
  RequestInsightCentralConfidential,
  RequestInsightCentralRestricted,
} from "~services/request_insight/central/request_insight_central_connect";
import type {
  CrossTenantConfidentialSearchRequest,
  CrossTenantConfidentialSearchResponse,
  RestrictedSearchRequest,
  RestrictedSearchResponse,
} from "~services/request_insight/central/request_insight_central_pb";
/* eslint-enable import/no-unresolved */

// set the ttl to be below the 60 second token expiration
const DEFAULT_TTL = 55;
// time in seconds to check all data and delete expired keys
const CHECK_PERIOD = 5;

class RequestInsightCentralConfidentialCachingClient extends CachingClient<
  typeof RequestInsightCentralConfidential,
  CrossTenantConfidentialSearchRequest
> {
  private readonly euClient?: ReturnType<
    typeof createClient<typeof RequestInsightCentralConfidential>
  >;

  constructor(
    private readonly usClient: ReturnType<
      typeof createClient<typeof RequestInsightCentralConfidential>
    > = createClient(
      RequestInsightCentralConfidential,
      createGrpcTransport(
        Config.createGrpcTransportOptions(
          Config.REQUEST_INSIGHT_CENTRAL_US_ENDPOINT,
        ),
      ),
    ),
  ) {
    super(usClient, DEFAULT_TTL, CHECK_PERIOD);

    if (
      Config.REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT &&
      Config.REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT !==
        Config.REQUEST_INSIGHT_CENTRAL_US_ENDPOINT
    ) {
      this.euClient = createClient(
        RequestInsightCentralConfidential,
        createGrpcTransport(
          Config.createGrpcTransportOptions(
            Config.REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT,
          ),
        ),
      );
    } else {
      logger.info(
        "EU is not defined or the same as US endpoint. Not creating EU client.",
      );
    }

    this.crossTenantConfidentialSearch = this.createCachedMethod(
      "crossTenantConfidentialSearch",
      this.combinedCrossTenantConfidentialSearch.bind(this),
    );
  }

  crossTenantConfidentialSearch: (
    request: PartialMessage<CrossTenantConfidentialSearchRequest>,
    options?: CallOptions,
  ) => Promise<CrossTenantConfidentialSearchResponse>;

  private async combinedCrossTenantConfidentialSearch(
    request: PartialMessage<CrossTenantConfidentialSearchRequest>,
    options?: CallOptions,
  ): Promise<CrossTenantConfidentialSearchResponse> {
    // Search US region first
    let response = await this.usClient.crossTenantConfidentialSearch(
      request,
      options,
    );

    // If no results found in US and EU client exists, search EU region
    if (response.results.length === 0 && this.euClient) {
      response = await this.euClient.crossTenantConfidentialSearch(
        request,
        options,
      );
    }

    return response;
  }

  protected calculateCacheKey(
    methodName: string,
    request: PartialMessage<CrossTenantConfidentialSearchRequest>,
  ) {
    const { filters, ...rest } = request;
    if (!filters) {
      logger.warn(
        "No filters provided for cross tenant confidential search request",
      );
      throw new Error(`filters is required for ${methodName}`);
    }
    return `${methodName}|filters:${filters}|rest:${CachingClient.objToString(
      rest,
    )}`;
  }

  public clearTokenCache(
    methodName: string,
    request: PartialMessage<CrossTenantConfidentialSearchRequest>,
  ) {
    const cacheKey = this.calculateCacheKey(methodName, request);
    this.queryCache.del(cacheKey);
  }

  private createCachedMethod<
    TRequest extends CrossTenantConfidentialSearchRequest,
    TResponse,
  >(
    methodName: string,
    clientMethod: (
      request: TRequest,
      options?: CallOptions,
    ) => Promise<TResponse>,
    ttl: number = DEFAULT_TTL,
  ) {
    return async (
      request: PartialMessage<CrossTenantConfidentialSearchRequest>,
      options: CallOptions = {},
    ): Promise<TResponse> => {
      const cacheKey = this.calculateCacheKey(methodName, request);
      return this.cacheAndCall(
        clientMethod as (
          request: CrossTenantConfidentialSearchRequest,
          options: CallOptions,
        ) => Promise<TResponse>,
        cacheKey,
        request,
        options,
        ttl,
      );
    };
  }
}

class RequestInsightCentralRestrictedCachingClient extends CachingClient<
  typeof RequestInsightCentralRestricted,
  RestrictedSearchRequest
> {
  private readonly euClient?: ReturnType<
    typeof createClient<typeof RequestInsightCentralRestricted>
  >;

  constructor(
    private readonly usClient: ReturnType<
      typeof createClient<typeof RequestInsightCentralRestricted>
    > = createClient(
      RequestInsightCentralRestricted,
      createGrpcTransport(
        Config.createGrpcTransportOptions(
          Config.REQUEST_INSIGHT_CENTRAL_US_ENDPOINT,
        ),
      ),
    ),
  ) {
    super(usClient, DEFAULT_TTL, CHECK_PERIOD);

    if (
      Config.REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT &&
      Config.REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT !==
        Config.REQUEST_INSIGHT_CENTRAL_US_ENDPOINT
    ) {
      this.euClient = createClient(
        RequestInsightCentralRestricted,
        createGrpcTransport(
          Config.createGrpcTransportOptions(
            Config.REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT,
          ),
        ),
      );
    } else {
      logger.info(
        "EU is not defined or the same as US endpoint. Not creating EU client for restricted service.",
      );
    }

    this.restrictedSearch = this.createCachedMethod(
      "restrictedSearch",
      this.combinedRestrictedSearch.bind(this),
    );
  }

  restrictedSearch: (
    request: PartialMessage<RestrictedSearchRequest>,
    options?: CallOptions,
  ) => Promise<RestrictedSearchResponse>;

  private async combinedRestrictedSearch(
    request: PartialMessage<RestrictedSearchRequest>,
    options?: CallOptions,
  ): Promise<RestrictedSearchResponse> {
    // Try US region first
    try {
      return await this.usClient.restrictedSearch(request, options);
    } catch (error) {
      // If EU client exists and US fails, try EU region
      if (this.euClient) {
        return await this.euClient.restrictedSearch(request, options);
      }
      throw error;
    }
  }

  protected calculateCacheKey(
    methodName: string,
    request: PartialMessage<RestrictedSearchRequest>,
  ) {
    const { filters, tenantId, timeFilter, ...rest } = request;
    const timeFilterStr = timeFilter
      ? CachingClient.objToString(timeFilter)
      : "";
    const filtersStr = filters ? CachingClient.objToString(filters) : "";
    return `${methodName}|tenantId:${tenantId}|timeFilter:${timeFilterStr}|filters:${filtersStr}|rest:${CachingClient.objToString(
      rest,
    )}`;
  }

  public clearTokenCache(
    methodName: string,
    request: PartialMessage<RestrictedSearchRequest>,
  ) {
    const cacheKey = this.calculateCacheKey(methodName, request);
    this.queryCache.del(cacheKey);
  }

  private createCachedMethod<
    TRequest extends RestrictedSearchRequest,
    TResponse,
  >(
    methodName: string,
    clientMethod: (
      request: TRequest,
      options?: CallOptions,
    ) => Promise<TResponse>,
    ttl: number = DEFAULT_TTL,
  ) {
    return async (
      request: PartialMessage<RestrictedSearchRequest>,
      options: CallOptions = {},
    ): Promise<TResponse> => {
      const cacheKey = this.calculateCacheKey(methodName, request);
      return this.cacheAndCall(
        clientMethod as (
          request: RestrictedSearchRequest,
          options: CallOptions,
        ) => Promise<TResponse>,
        cacheKey,
        request,
        options,
        ttl,
      );
    };
  }
}

export const confidentialCachingClient =
  new RequestInsightCentralConfidentialCachingClient();
export const restrictedCachingClient =
  new RequestInsightCentralRestrictedCachingClient();

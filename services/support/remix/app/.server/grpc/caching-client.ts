import NodeCache from "node-cache";
import { Message, PartialMessage, ServiceType } from "@bufbuild/protobuf";
import type { CallOptions, Client } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";

export abstract class CachingClient<
  T extends ServiceType,
  R extends Message<R>,
> {
  protected queryCache: NodeCache;
  protected client: Client<T>;

  protected abstract calculateCacheKey(methodName: string, request: R): string;

  constructor(client: Client<T>, stdTTL: number, checkperiod: number) {
    this.queryCache = new NodeCache({ stdTTL, checkperiod });
    this.client = client;
  }

  /* eslint-disable @typescript-eslint/no-explicit-any */
  static objToString(obj: Record<string, any> | any[]): string {
    if (Array.isArray(obj)) {
      return `[${obj.map(CachingClient.objToString).join(", ")}]`;
    } else if (typeof obj === "object") {
      const props = Object.keys(obj).sort();
      return `{${props
        .map((key) => `${key}: ${CachingClient.objToString(obj[key])}`)
        .join(", ")}}`;
    } else {
      return JSON.stringify(obj, (_, v) =>
        typeof v === "bigint" ? v.toString() : v,
      );
    }
  }

  protected async cacheAndCall<TResponse>(
    method: (request: R, options: CallOptions) => Promise<TResponse>,
    cacheKey: string,
    request: PartialMessage<R>,
    options: CallOptions = {},
    ttl: number,
  ): Promise<TResponse> {
    const inCache = this.queryCache.has(cacheKey);

    if (inCache) {
      const result = this.queryCache.get<TResponse>(cacheKey);

      if (result) {
        return result;
      } else {
        this.queryCache.del(cacheKey);
      }
    }

    let result: TResponse;
    try {
      result = await method(request as R, options);
    } catch (e) {
      logger.error(`Failed to get data from service: ${e}`);
      throw e;
    }

    this.queryCache.set<TResponse>(cacheKey, result, ttl);

    return result;
  }
}

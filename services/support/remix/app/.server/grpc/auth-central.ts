import { createGrpcTransport } from "@connectrpc/connect-node";
import { type CallOptions, createClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";
import { logger } from "@augment-internal/logging";

import { Config } from "../config";
// eslint-disable-next-line import/no-unresolved
import { AuthService } from "~services/auth/central/server/auth_connect";
import type {
  GetUsersRequest,
  GetUsersResponse,
  RemoveUserFromTenantRequest,
  RemoveUserFromTenantResponse,
} from "~services/auth/central/server/auth_pb";

class AuthCentralClient {
  constructor(
    private readonly client = createClient(
      AuthService,
      createGrpcTransport(
        Config.createGrpcTransportOptions(Config.AUTH_CENTRAL_ENDPOINT || ""),
      ),
    ),
  ) {
    this.getUsers = this.client.getUsers.bind(this.client);
    this.removeUserFromTenant = this.client.removeUserFromTenant.bind(
      this.client,
    );
  }

  getUsers: (
    request: PartialMessage<GetUsersRequest>,
    options?: CallOptions,
  ) => Promise<GetUsersResponse>;

  removeUserFromTenant: (
    request: PartialMessage<RemoveUserFromTenantRequest>,
    options?: CallOptions,
  ) => Promise<RemoveUserFromTenantResponse>;
}

let client: AuthCentralClient;
/**
 * lazy initializes the auth central client. This is to avoid creating a new client for each request.
 * It also makes testing easier by not initializing on module load.
 * @returns AuthCentralClient
 */
export function getAuthCentralClient() {
  if (!client) {
    if (!Config.AUTH_CENTRAL_ENDPOINT) {
      throw new Error("AUTH_CENTRAL_ENDPOINT is not defined");
    }
    client = new AuthCentralClient();
    logger.info(`Created auth central client: ${Config.AUTH_CENTRAL_ENDPOINT}`);
  }
  return client;
}

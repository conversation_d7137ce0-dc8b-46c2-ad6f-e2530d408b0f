import fs from "fs";
import { logger } from "@augment-internal/logging";
import type { GrpcTransportOptions } from "@connectrpc/connect-node";

const { env } = process;

export class Config {
  // env vars
  static NAMESPACE: string = env.NAMESPACE ?? "";
  static CURRENT_CLOUD: string = env.CURRENT_CLOUD ?? "";
  static IS_MTLS_ENABLED: boolean = env.IS_MTLS_ENABLED === "true";

  static TOKEN_EXCHANGE_ENDPOINT = env.TOKEN_EXCHANGE_ENDPOINT ?? "";
  static TENANT_WATCHER_ENDPOINT = env.TENANT_WATCHER_ENDPOINT ?? "";
  static AUTH_CENTRAL_ENDPOINT = env.AUTH_CENTRAL_ENDPOINT ?? "";
  static REQUEST_INSIGHT_CENTRAL_US_ENDPOINT =
    env.REQUEST_INSIGHT_CENTRAL_US_ENDPOINT ?? "";
  static REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT: string | undefined =
    env.REQUEST_INSIGHT_CENTRAL_EU_ENDPOINT;

  static CENTRAL_CLIENT_CERT: Buffer | undefined;
  static CENTRAL_CLIENT_KEY: Buffer | undefined;
  static CENTRAL_CA_CERT: Buffer | undefined;

  static init(): void {
    logger.info(`Initializing config`);
    const missingVars: string[] = [];

    if (this.IS_MTLS_ENABLED) {
      this.CENTRAL_CLIENT_CERT = fs.readFileSync(
        "/central-client-certs/tls.crt",
      );
      this.CENTRAL_CLIENT_KEY = fs.readFileSync(
        "/central-client-certs/tls.key",
      );
      this.CENTRAL_CA_CERT = fs.readFileSync("/central-client-certs/ca.crt");
    } else {
      logger.info("MTLS is disabled");
    }
    if (!this.TENANT_WATCHER_ENDPOINT)
      missingVars.push("TENANT_WATCHER_ENDPOINT");
    if (!this.TOKEN_EXCHANGE_ENDPOINT)
      missingVars.push("TOKEN_EXCHANGE_ENDPOINT");
    if (!this.AUTH_CENTRAL_ENDPOINT) missingVars.push("AUTH_CENTRAL_ENDPOINT");
    if (!this.REQUEST_INSIGHT_CENTRAL_US_ENDPOINT)
      missingVars.push("REQUEST_INSIGHT_CENTRAL_US_ENDPOINT");

    if (missingVars.length > 0) {
      throw new Error(
        `Missing environment variables: ${missingVars.join(", ")}`,
      );
    }
  }
  static {
    if (env.NODE_ENV !== "test") {
      Config.init();
    }
    Config.init = () => {
      throw new Error("Config already initialized");
    };
  }
  /**
   * Simple string formatter that takes a string and replaces all '%s' instances with
   * the corresponding index of the args. do not use `util.format` it does not work the way
   * you think or at least the way I think.
   *
   * @param fmt
   * @param args
   * @returns
   */
  static format(fmt: string, ...args: unknown[]) {
    let idx = 0;
    return fmt.replace(/%s/g, (match) => {
      return idx < args.length ? String(args[idx++]) : match;
    });
  }
  static createGrpcTransportOptions(endpoint: string): GrpcTransportOptions {
    logger.info(`Creating grpc transport: ${endpoint}`);
    return {
      baseUrl: `${Config.IS_MTLS_ENABLED ? "https" : "http"}://${endpoint}`,
      httpVersion: "2",
      nodeOptions: {
        ca: Config.CENTRAL_CA_CERT,
        key: Config.CENTRAL_CLIENT_KEY,
        cert: Config.CENTRAL_CLIENT_CERT,
      },
    };
  }
}

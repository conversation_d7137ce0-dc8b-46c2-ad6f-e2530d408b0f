import type { Meta, StoryObj } from "@storybook/react";
import { TenantList } from "../TenantList";

const meta = {
  title: "Components/Tenant List",
  component: TenantList,
  parameters: {
    layout: "fullscreen",
  },
} satisfies Meta<typeof TenantList>;

export default meta;
type Story = StoryObj<typeof meta>;

const generateMockTenants = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    id: `tenant-${index + 1}`,
    name: `Example Tenant ${index + 1}`,
    config: {
      configs: {
        key: "id",
        value: `abc${index + 1}23`,
      },
    },
  }));
};

const mockTenants = generateMockTenants(35);

export const Default: Story = {
  args: {
    tenants: mockTenants,
  },
};

export const Empty: Story = {
  args: {
    tenants: [],
  },
};

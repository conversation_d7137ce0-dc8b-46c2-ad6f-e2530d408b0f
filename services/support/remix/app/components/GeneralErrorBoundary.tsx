import { Link as RemixLink, useRouteError } from "@remix-run/react";
import {
  Theme,
  Container,
  Flex,
  Text,
  <PERSON><PERSON>,
  Card,
  Link as RadixLink,
} from "@radix-ui/themes";

const errorMessages: { [key: string]: string } = {
  "Not Found": "The page you are looking for does not exist.",
};

export function GeneralErrorBoundary() {
  const error = useRouteError() as {
    status: number;
    statusText: string;
    internal: boolean;
    data: { message?: string };
  };
  const { status = 500, statusText = "Internal Server Error", data } = error;

  let content = null;
  if (status === 403) {
    content = (
      <>
        <img
          src="/auggie-inspector.svg"
          alt="Augment Error"
          width="64"
          height="64"
        />
        <Text mt="2" mb="4" align="center">
          Sorry, but you don&apos;t have permission to access this page. Admin
          privileges are required. Refer to the{" "}
          <RadixLink href="https://docs.augmentcode.com/">
            Augment docs site
          </RadixLink>{" "}
          for more details.
        </Text>
      </>
    );
  } else {
    let message = errorMessages[statusText] ?? data.message;
    if (!message) {
      message = "An unexpected error occurred. Please try again later.";
    }

    content = (
      <>
        <img
          src="/auggie-error.svg"
          alt="Augment Error"
          width="64"
          height="64"
        />
        <Text size="6" weight="bold" mt="2">
          Error {status}
        </Text>
        <Text size="5" mt="2" mb="4">
          {message}
        </Text>
      </>
    );
  }

  return (
    <Theme
      accentColor="crimson"
      grayColor="sand"
      panelBackground="solid"
      scaling="95%"
    >
      <Container size="2">
        <Card m="4" variant="surface">
          <Flex direction="column" align="center" justify="center">
            {content}
            <RemixLink to="/logout" reloadDocument>
              <Button size="2" variant="surface">
                Go Back
              </Button>
            </RemixLink>
          </Flex>
        </Card>
      </Container>
    </Theme>
  );
}

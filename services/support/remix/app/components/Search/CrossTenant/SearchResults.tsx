import { Box, Table } from "@radix-ui/themes";

import { ConfidentialSearchResult } from "../../../types/api";
import { createSupportUrl } from "../../../utils/urls";

import styles from "../Common/SearchResults.module.css";

interface SearchResultsProps {
  results: ConfidentialSearchResult[] | undefined;
  error: Error | null;
  isLoading: boolean;
}

export default function SearchResults({
  results,
  error,
  isLoading,
}: SearchResultsProps) {
  const handleRowClick = (result: ConfidentialSearchResult) => {
    const supportUrl = createSupportUrl(result);
    if (supportUrl) {
      window.open(supportUrl, "_blank", "noopener,noreferrer");
    }
  };

  if (error) {
    return (
      <Box m="4" className={styles.errorMessage}>
        Error: {error.message}
      </Box>
    );
  }

  if (isLoading) {
    return <Box m="4">Loading...</Box>;
  }

  if (results && results.length > 0) {
    return (
      <Box m="4">
        <Table.Root>
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Request ID</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Session ID</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Tenant</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Request Time</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Request Type</Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {results.map((result) => (
              <Table.Row
                key={result.requestId}
                onClick={() => handleRowClick(result)}
                className={styles.hoverableRow}
              >
                <Table.Cell>{result.requestId}</Table.Cell>
                <Table.Cell>{result.sessionId}</Table.Cell>
                <Table.Cell>{result.tenantName}</Table.Cell>
                <Table.Cell>
                  {result.requestTime
                    ? new Date(result.requestTime).toLocaleString()
                    : "-"}
                </Table.Cell>
                <Table.Cell>{result.requestType}</Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </Box>
    );
  }

  if (results && results.length === 0) {
    return <Box m="4">No results found</Box>;
  }

  return null;
}

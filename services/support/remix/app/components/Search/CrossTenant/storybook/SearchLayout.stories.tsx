import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import SearchLayout from "../SearchLayout";

const meta = {
  title: "Search/Cross Tenant/Search Layout",
  component: SearchLayout,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SearchLayout>;

export default meta;
type Story = StoryObj<typeof SearchLayout>;

export const Default: Story = {
  args: {},
};

import { Theme } from "@radix-ui/themes";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import SearchResults from "../SearchResults";
import { ConfidentialSearchResult } from "../../../../types/api";

const meta = {
  title: "Search/Cross Tenant/Search Results",
  component: SearchResults,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <Theme>
        <Story />
      </Theme>
    ),
  ],
} satisfies Meta<typeof SearchResults>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockResults: ConfidentialSearchResult[] = [
  {
    requestId: "REQ001",
    sessionId: "SESS001",
    requestTime: new Date("2023-05-01T10:00:00Z"),
    requestType: "GET",
    tenantId: "TENANT001",
    tenantName: "Acme Corp",
    shardNamespace: "shard1",
    domainSuffix: "example.com",
  },
  {
    requestId: "REQ002",
    sessionId: "SESS002",
    requestTime: new Date("2023-05-02T11:00:00Z"),
    requestType: "POST",
    tenantId: "TENANT002",
    tenantName: "Beta Inc",
    shardNamespace: "shard2",
    domainSuffix: "example.com",
  },
  {
    requestId: "REQ003",
    sessionId: "SESS003",
    requestTime: new Date("2023-05-03T12:00:00Z"),
    requestType: "PUT",
    tenantId: "TENANT003",
    tenantName: "Gamma LLC",
    shardNamespace: "shard3",
    domainSuffix: "example.com",
  },
];

export const WithResults: Story = {
  args: {
    results: mockResults,
    error: null,
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    results: undefined,
    error: null,
    isLoading: true,
  },
};

export const ErrorState: Story = {
  args: {
    results: undefined,
    error: new Error("Failed to fetch results"),
    isLoading: false,
  },
};

export const NoResults: Story = {
  args: {
    results: [],
    error: null,
    isLoading: false,
  },
};

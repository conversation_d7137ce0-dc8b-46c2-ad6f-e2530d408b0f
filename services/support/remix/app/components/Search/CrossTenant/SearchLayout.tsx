import { useState, FormEvent, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Box, Flex, Button, Separator } from "@radix-ui/themes";
import { validate as validateUUID } from "uuid";

import { ConfidentialSearchResult } from "../../../types/api";
import SearchResults from "./SearchResults";
import ClearableInput from "../Common/ClearableInput";
import DateRangeSelector, {
  getDefaultDates,
  validateDateRange,
} from "../Common/DateRangeSelector";

type SearchParams = {
  requestId: string;
  sessionId: string;
  startTime: string;
  endTime: string;
};

async function fetchSearchResults(
  params: URLSearchParams,
): Promise<ConfidentialSearchResult[]> {
  const response = await fetch(`/api/cross-tenant/search?${params.toString()}`);
  if (!response.ok) {
    throw new Error("Search request failed");
  }
  return response.json();
}

export default function SearchLayout() {
  const { startTime: defaultStartTime, endTime: defaultEndTime } =
    getDefaultDates();

  const [requestId, setRequestId] = useState("");
  const [sessionId, setSessionId] = useState("");
  const [startTime, setStartTime] = useState(defaultStartTime);
  const [endTime, setEndTime] = useState(defaultEndTime);
  const [searchParams, setSearchParams] = useState<SearchParams | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    requestId?: string;
    sessionId?: string;
    dateRange?: string;
  }>({});

  const validateId = (id: string, field: "requestId" | "sessionId") => {
    if (id && !validateUUID(id)) {
      setValidationErrors((prev) => ({
        ...prev,
        [field]: "Must be a valid UUID",
      }));
      return false;
    }
    setValidationErrors((prev) => ({
      ...prev,
      [field]: undefined,
    }));
    return true;
  };

  const handleStartTimeChange = (value: string) => {
    setStartTime(value);
    if (value && endTime) {
      const isValid = validateDateRange(value, endTime);
      setValidationErrors((prev) => ({
        ...prev,
        dateRange: isValid ? undefined : "Start time must be before end time",
      }));
    }
  };

  const handleEndTimeChange = (value: string) => {
    setEndTime(value);
    if (startTime && value) {
      const isValid = validateDateRange(startTime, value);
      setValidationErrors((prev) => ({
        ...prev,
        dateRange: isValid ? undefined : "Start time must be before end time",
      }));
    }
  };

  const handleRequestIdChange = (value: string) => {
    setRequestId(value);
    validateId(value, "requestId");
  };

  const handleSessionIdChange = (value: string) => {
    setSessionId(value);
    validateId(value, "sessionId");
  };

  const isSubmitDisabled = useMemo(() => {
    if (Object.values(validationErrors).some((error) => error !== undefined)) {
      return true;
    }

    // Require exactly one ID (either requestId or sessionId)
    const hasRequestId = requestId.trim() !== "";
    const hasSessionId = sessionId.trim() !== "";

    // Disable if both are present
    return hasRequestId && hasSessionId;
  }, [requestId, sessionId, validationErrors]);

  const buildSearchParams = (params: SearchParams) => {
    const urlParams = new URLSearchParams();

    if (params.requestId.trim()) {
      urlParams.append("requestId", params.requestId.trim());
    }
    if (params.sessionId.trim()) {
      urlParams.append("sessionId", params.sessionId.trim());
    }
    if (params.startTime) {
      const startUTC = new Date(params.startTime).toISOString();
      urlParams.append("startTime", startUTC);
    }
    if (params.endTime) {
      const endUTC = new Date(params.endTime).toISOString();
      urlParams.append("endTime", endUTC);
    }

    return urlParams;
  };

  const handleSearch = () => {
    const isRequestIdValid = !requestId || validateId(requestId, "requestId");
    const isSessionIdValid = !sessionId || validateId(sessionId, "sessionId");
    const isDateRangeValid = validateDateRange(startTime, endTime);

    if (!isRequestIdValid || !isSessionIdValid || !isDateRangeValid) {
      return;
    }

    // Add check to prevent both IDs
    if (requestId.trim() && sessionId.trim()) {
      setValidationErrors((prev) => ({
        ...prev,
        requestId: "Provide either Request ID or Session ID, not both",
        sessionId: "Provide either Request ID or Session ID, not both",
      }));
      return;
    }

    setSearchParams({
      requestId,
      sessionId,
      startTime,
      endTime,
    });
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    handleSearch();
  };

  const {
    data: results,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["cross-tenant-search", searchParams],
    queryFn: () =>
      searchParams
        ? fetchSearchResults(buildSearchParams(searchParams))
        : Promise.resolve([]),
    enabled: searchParams !== null,
    staleTime: 60 * 1000,
  });

  return (
    <Box p="4" maxWidth="1200px" mx="auto">
      <form onSubmit={handleSubmit}>
        <Flex direction="column" gap="4">
          {/* Primary Search Inputs Row */}
          <Flex gap="3">
            <ClearableInput
              value={requestId}
              onChange={handleRequestIdChange}
              placeholder="Request ID..."
              error={validationErrors.requestId}
              disabled={Boolean(sessionId.trim())}
              onEnterPress={() => !isSubmitDisabled && handleSearch()}
            />
            <Separator orientation="vertical" size="2" />
            <ClearableInput
              value={sessionId}
              onChange={handleSessionIdChange}
              placeholder="Session ID..."
              error={validationErrors.sessionId}
              disabled={Boolean(requestId.trim())}
              onEnterPress={() => !isSubmitDisabled && handleSearch()}
            />
          </Flex>

          <DateRangeSelector
            startTime={startTime}
            endTime={endTime}
            onStartTimeChange={handleStartTimeChange}
            onEndTimeChange={handleEndTimeChange}
            validationErrors={validationErrors}
          />

          {/* Search Button Row */}
          <Flex justify="end">
            <Button
              type="submit"
              style={{ width: "100px" }}
              disabled={isSubmitDisabled || isLoading}
              loading={isLoading}
            >
              Search
            </Button>
          </Flex>
        </Flex>
      </form>

      <Box mt="4">
        <SearchResults
          results={results}
          error={error as Error | null}
          isLoading={isLoading}
        />
      </Box>
    </Box>
  );
}

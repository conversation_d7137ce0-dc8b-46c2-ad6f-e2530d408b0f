import { Box, Table } from "@radix-ui/themes";
import { createSupportUrl } from "../../../utils/urls";
import { RestrictedSearchResult } from "../../../types/api";

import styles from "../Common/SearchResults.module.css";

interface RestrictedSearchResultsProps {
  results: RestrictedSearchResult[] | undefined;
  error: Error | null;
  isLoading: boolean;
}

export function RestrictedSearchResults({
  results,
  error,
  isLoading,
}: RestrictedSearchResultsProps) {
  const handleRowClick = (result: RestrictedSearchResult) => {
    const supportUrl = createSupportUrl(result);
    if (supportUrl) {
      window.open(supportUrl, "_blank", "noopener,noreferrer");
    }
  };

  if (error) {
    return (
      <Box m="4" className={styles.errorMessage}>
        Error: {error.message}
      </Box>
    );
  }

  if (isLoading) {
    return <Box m="4">Loading...</Box>;
  }

  if (results && results.length > 0) {
    return (
      <Box m="4">
        <Table.Root>
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Request ID</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Session ID</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>User ID</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Tenant</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Request Time</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Request Type</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Model</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>HTTP Status</Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {results.map((result) => (
              <Table.Row
                key={result.requestId}
                onClick={() => handleRowClick(result)}
                className={styles.hoverableRow}
              >
                <Table.Cell>{result.requestId}</Table.Cell>
                <Table.Cell>{result.sessionId}</Table.Cell>
                <Table.Cell>{result.userId}</Table.Cell>
                <Table.Cell>{result.tenantName}</Table.Cell>
                <Table.Cell>
                  {result.requestTime
                    ? new Date(result.requestTime).toLocaleString()
                    : "-"}
                </Table.Cell>
                <Table.Cell>{result.requestType}</Table.Cell>
                <Table.Cell>{result.modelName}</Table.Cell>
                <Table.Cell>
                  {result.accepted !== undefined ? (
                    result.accepted ? (
                      <span className="text-green-600">Accepted</span>
                    ) : (
                      <span className="text-red-600">Rejected</span>
                    )
                  ) : (
                    "-"
                  )}
                </Table.Cell>
                <Table.Cell>{result.httpStatusCode || "-"}</Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </Box>
    );
  }

  if (results && results.length === 0) {
    return <Box m="4">No results found</Box>;
  }

  return null;
}

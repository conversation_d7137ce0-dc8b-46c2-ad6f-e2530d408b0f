import { Theme } from "@radix-ui/themes";
import type { <PERSON>a, StoryObj } from "@storybook/react";
import { RestrictedSearchResult } from "../../../../types/api";
import { RestrictedSearchResults } from "../SearchResults";

const meta = {
  title: "Search/Tenant/Search Results",
  component: RestrictedSearchResults,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <Theme>
        <Story />
      </Theme>
    ),
  ],
} satisfies Meta<typeof RestrictedSearchResults>;

export default meta;
type Story = StoryObj<typeof meta>;

const mockResults: RestrictedSearchResult[] = [
  {
    requestId: "REQ001",
    sessionId: "SESS001",
    requestTime: new Date("2023-05-01T10:00:00Z"),
    requestType: "CHAT",
    tenantId: "TENANT001",
    tenantName: "Acme Corp",
    userId: "user123",
    modelName: "claude-2",
    accepted: true,
    httpStatusCode: 200,
  },
  {
    requestId: "REQ002",
    sessionId: "SESS002",
    requestTime: new Date("2023-05-02T11:00:00Z"),
    requestType: "COMPLETION",
    tenantId: "TENANT002",
    tenantName: "Beta Inc",
    userId: "user456",
    modelName: "claude-instant",
    accepted: false,
    httpStatusCode: 400,
  },
  {
    requestId: "REQ003",
    sessionId: "SESS003",
    requestTime: new Date("2023-05-03T12:00:00Z"),
    requestType: "EMBEDDING",
    tenantId: "TENANT003",
    tenantName: "Gamma LLC",
    userId: "user789",
    modelName: "claude-2.1",
    accepted: true,
    httpStatusCode: 200,
  },
];

export const WithResults: Story = {
  args: {
    results: mockResults,
    error: null,
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    results: undefined,
    error: null,
    isLoading: true,
  },
};

export const ErrorState: Story = {
  args: {
    results: undefined,
    error: new Error("Failed to fetch results"),
    isLoading: false,
  },
};

export const NoResults: Story = {
  args: {
    results: [],
    error: null,
    isLoading: false,
  },
};

export const WithMixedStatuses: Story = {
  args: {
    results: [
      {
        ...mockResults[0],
        accepted: true,
        httpStatusCode: 200,
      },
      {
        ...mockResults[1],
        accepted: false,
        httpStatusCode: 400,
      },
      {
        ...mockResults[2],
        accepted: undefined,
        httpStatusCode: undefined,
      },
    ],
    error: null,
    isLoading: false,
  },
};

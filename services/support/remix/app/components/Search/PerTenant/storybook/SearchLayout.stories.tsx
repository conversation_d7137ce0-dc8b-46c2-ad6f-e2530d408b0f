import type { <PERSON>a, StoryObj } from "@storybook/react";
import SearchLayout from "../SearchLayout";

/* eslint-disable import/no-unresolved */
import { Tenant } from "~services/tenant_watcher/tenant_watcher_pb";
/* eslint-enable import/no-unresolved */

const meta = {
  title: "Search/Tenant/Search Layout",
  component: SearchLayout,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
} satisfies Meta<typeof SearchLayout>;

export default meta;
type Story = StoryObj<typeof SearchLayout>;

export const Default: Story = {
  args: {
    tenant: { id: "tenant123", name: "Example Tenant" } as Tenant,
  },
};

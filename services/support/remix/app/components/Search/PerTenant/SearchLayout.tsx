import { useState, FormEvent, useMemo, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Box, Flex, Select, Button, Separator } from "@radix-ui/themes";
import { validate as validateUUID } from "uuid";

/* eslint-disable import/no-unresolved */
import { Tenant } from "~services/tenant_watcher/tenant_watcher_pb";
/* eslint-enable import/no-unresolved */

import { RestrictedSearchResult } from "../../../types/api";
import { RestrictedSearchResults } from "./SearchResults";
import ClearableInput from "../Common/ClearableInput";
import ClearableSelect from "../Common/ClearableSelect";
import DateRangeSelector, {
  getDefaultDates,
  validateDateRange,
} from "../Common/DateRangeSelector";

type SearchFilters = {
  requestType?: string;
  accepted?: boolean;
  modelName?: string;
  httpStatusCode?: string;
};

type SearchParams = {
  requestId: string;
  sessionId: string;
  userId: string;
  startTime: string;
  endTime: string;
  filters: SearchFilters;
};

async function fetchSearchResults(
  tenant: Tenant,
  params: URLSearchParams,
): Promise<RestrictedSearchResult[]> {
  const response = await fetch(
    `/api/tenants/${tenant.id}/search?${params.toString()}`,
  );
  if (!response.ok) {
    throw new Error("Search request failed");
  }
  return response.json();
}

export default function SearchLayout({ tenant }: { tenant: Tenant }) {
  const { startTime: defaultStartTime, endTime: defaultEndTime } =
    getDefaultDates();

  const [requestId, setRequestId] = useState("");
  const [sessionId, setSessionId] = useState("");
  const [userId, setUserId] = useState("");
  const [startTime, setStartTime] = useState(defaultStartTime);
  const [endTime, setEndTime] = useState(defaultEndTime);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [searchParams, setSearchParams] = useState<SearchParams | null>(null);
  const [validationErrors, setValidationErrors] = useState<{
    requestId?: string;
    sessionId?: string;
    dateRange?: string;
  }>({});

  const validateId = (id: string, field: "requestId" | "sessionId") => {
    if (id && !validateUUID(id)) {
      setValidationErrors((prev) => ({
        ...prev,
        [field]: "Must be a valid UUID",
      }));
      return false;
    }
    setValidationErrors((prev) => ({
      ...prev,
      [field]: undefined,
    }));
    return true;
  };

  const handleStartTimeChange = (value: string) => {
    setStartTime(value);
    if (value && endTime) {
      const isValid = validateDateRange(value, endTime);
      setValidationErrors((prev) => ({
        ...prev,
        dateRange: isValid ? undefined : "Start time must be before end time",
      }));
    }
  };

  const handleEndTimeChange = (value: string) => {
    setEndTime(value);
    if (startTime && value) {
      const isValid = validateDateRange(startTime, value);
      setValidationErrors((prev) => ({
        ...prev,
        dateRange: isValid ? undefined : "Start time must be before end time",
      }));
    }
  };

  const handleRequestIdChange = (value: string) => {
    setRequestId(value);
    validateId(value, "requestId");
  };

  const handleSessionIdChange = (value: string) => {
    setSessionId(value);
    validateId(value, "sessionId");
  };

  const hasRequestId = useMemo(() => {
    return Boolean(requestId.trim());
  }, [requestId]);

  const isSubmitDisabled = useMemo(() => {
    if (Object.values(validationErrors).some((error) => error !== undefined)) {
      return true;
    }
  }, [validationErrors]);

  // Clear filters only when a request ID is entered
  useEffect(() => {
    if (hasRequestId) {
      setFilters({});
      setUserId("");
    }
  }, [hasRequestId]);

  // Build search parameters
  const buildSearchParams = (params: SearchParams) => {
    const urlParams = new URLSearchParams();

    if (params.requestId.trim()) {
      urlParams.append("requestId", params.requestId.trim());
    }
    if (params.sessionId.trim()) {
      urlParams.append("sessionId", params.sessionId.trim());
    }
    if (params.userId.trim()) {
      urlParams.append("userId", params.userId.trim());
    }
    if (params.startTime) {
      const startUTC = new Date(params.startTime).toISOString();
      urlParams.append("startTime", startUTC);
    }
    if (params.endTime) {
      const endUTC = new Date(params.endTime).toISOString();
      urlParams.append("endTime", endUTC);
    }

    Object.entries(params.filters).forEach(([key, value]) => {
      if (value !== undefined) {
        urlParams.append(key, String(value));
      }
    });

    return urlParams;
  };

  const handleSearch = () => {
    // Validate both IDs before submitting
    const isRequestIdValid = !requestId || validateId(requestId, "requestId");
    const isSessionIdValid = !sessionId || validateId(sessionId, "sessionId");
    const isDateRangeValid = validateDateRange(startTime, endTime);

    if (!isRequestIdValid || !isSessionIdValid || !isDateRangeValid) {
      if (!isDateRangeValid) {
        setValidationErrors((prev) => ({
          ...prev,
          dateRange: "Start time must be before end time",
        }));
      }
      return;
    }

    setSearchParams({
      requestId,
      sessionId,
      userId,
      startTime,
      endTime,
      filters,
    });
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    handleSearch();
  };

  // Query for results
  const {
    data: results,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["tenant-search", tenant.id, searchParams],
    queryFn: () =>
      searchParams
        ? fetchSearchResults(tenant, buildSearchParams(searchParams))
        : Promise.resolve([]),
    enabled: searchParams !== null,
  });

  return (
    <Box p="4" maxWidth="1200px" mx="auto">
      <form onSubmit={handleSubmit}>
        <Flex direction="column" gap="4">
          {/* Primary Search Inputs Row */}
          <Flex gap="3">
            <ClearableInput
              value={requestId}
              onChange={handleRequestIdChange}
              placeholder="Request ID..."
              style={{ flex: 1 }}
              error={validationErrors.requestId}
              disabled={Boolean(sessionId.trim())}
              onEnterPress={() => !isSubmitDisabled && handleSearch()}
            />
            <Separator orientation="vertical" size="2" />
            <ClearableInput
              value={sessionId}
              onChange={handleSessionIdChange}
              placeholder="Session ID..."
              style={{ flex: 1 }}
              error={validationErrors.sessionId}
              disabled={Boolean(requestId.trim())}
              onEnterPress={() => !isSubmitDisabled && handleSearch()}
            />
          </Flex>

          <Separator size="4" />

          {/* Secondary Search Row with Filters and Date Range */}
          <Flex gap="3" align="start">
            {/* Left side - Filters */}
            <Flex direction="column" gap="3" style={{ flex: 1 }}>
              <Flex gap="3">
                <Box style={{ width: "20%" }}>
                  <ClearableInput
                    value={userId}
                    onChange={setUserId}
                    placeholder="User ID..."
                    style={{ width: "100%" }}
                    disabled={hasRequestId}
                    onEnterPress={() => !isSubmitDisabled && handleSearch()}
                  />
                </Box>

                <Box style={{ width: "16%" }}>
                  <ClearableSelect
                    value={filters.requestType || ""}
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        requestType: value || undefined,
                      }))
                    }
                    placeholder="Request Type"
                    disabled={hasRequestId}
                    style={{ width: "100%" }}
                  >
                    <Select.Group>
                      <Select.Label>Request Type</Select.Label>
                      <Select.Item value="CHAT">Chat</Select.Item>
                      <Select.Item value="COMPLETION">Completion</Select.Item>
                      <Select.Item value="EDIT">Edit</Select.Item>
                      <Select.Item value="NEXT_EDIT">Next Edit</Select.Item>
                      <Select.Item value="EXTENSION_ERROR">
                        Extension Error
                      </Select.Item>
                    </Select.Group>
                  </ClearableSelect>
                </Box>

                <Box style={{ width: "15%" }}>
                  <ClearableSelect
                    value={
                      filters.accepted === undefined
                        ? ""
                        : String(filters.accepted)
                    }
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        accepted: value ? value === "true" : undefined,
                      }))
                    }
                    placeholder="Response"
                    disabled={hasRequestId}
                    style={{ width: "100%" }}
                  >
                    <Select.Group>
                      <Select.Label>Response</Select.Label>
                      <Select.Item value="true">Accepted</Select.Item>
                      <Select.Item value="false">Rejected</Select.Item>
                    </Select.Group>
                  </ClearableSelect>
                </Box>

                <Box style={{ width: "35%" }}>
                  <ClearableInput
                    value={filters.modelName || ""}
                    onChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        modelName: value || undefined,
                      }))
                    }
                    placeholder="Model name..."
                    style={{ width: "100%" }}
                    disabled={hasRequestId}
                  />
                </Box>

                <Box style={{ width: "15%" }}>
                  <ClearableSelect
                    value={filters.httpStatusCode || ""}
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        httpStatusCode: value || undefined,
                      }))
                    }
                    placeholder="HTTP Status"
                    disabled={hasRequestId}
                    style={{ width: "100%" }}
                  >
                    <Select.Group>
                      <Select.Label>HTTP Status</Select.Label>
                      <Select.Item value="200">200</Select.Item>
                      <Select.Item value="201">201</Select.Item>
                      <Select.Item value="204">204</Select.Item>
                      <Select.Item value="400">400</Select.Item>
                      <Select.Item value="401">401</Select.Item>
                      <Select.Item value="403">403</Select.Item>
                      <Select.Item value="404">404</Select.Item>
                      <Select.Item value="429">429</Select.Item>
                      <Select.Item value="499">499</Select.Item>
                      <Select.Item value="500">500</Select.Item>
                      <Select.Item value="502">502</Select.Item>
                      <Select.Item value="503">503</Select.Item>
                      <Select.Item value="504">504</Select.Item>
                    </Select.Group>
                  </ClearableSelect>
                </Box>
              </Flex>
            </Flex>
          </Flex>

          {/* Date Range Row */}
          <Flex gap="3">
            <Box style={{ flex: 1 }}>
              <DateRangeSelector
                startTime={startTime}
                endTime={endTime}
                onStartTimeChange={handleStartTimeChange}
                onEndTimeChange={handleEndTimeChange}
                validationErrors={validationErrors}
              />
            </Box>
          </Flex>

          {/* Search Button Row */}
          <Flex justify="end">
            <Button
              type="submit"
              style={{ width: "100px" }}
              disabled={isSubmitDisabled || isLoading}
              loading={isLoading}
            >
              Search
            </Button>
          </Flex>
        </Flex>
      </form>

      <Box mt="4">
        <RestrictedSearchResults
          results={results}
          error={error as Error | null}
          isLoading={isLoading}
        />
      </Box>
    </Box>
  );
}

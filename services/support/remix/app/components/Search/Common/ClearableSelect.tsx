import { ReactNode } from "react";
import { Box, Select, Icon<PERSON>utton, Flex } from "@radix-ui/themes";
import { Cross2Icon } from "@radix-ui/react-icons";

interface ClearableSelectProps {
  value: string;
  onValueChange: (value: string) => unknown;
  placeholder: string;
  children: ReactNode;
  style?: React.CSSProperties;
  disabled?: boolean;
}

export default function ClearableSelect({
  value,
  onValueChange,
  placeholder,
  children,
  style,
  disabled = false,
}: ClearableSelectProps) {
  return (
    <Box style={style}>
      <Flex gap="2" align="center">
        <Box flexGrow="1">
          <Select.Root
            value={value}
            onValueChange={onValueChange}
            disabled={disabled}
          >
            <Select.Trigger
              placeholder={placeholder}
              style={{ width: "100%" }}
            />
            <Select.Content>{children}</Select.Content>
          </Select.Root>
        </Box>
        {value && !disabled && (
          <IconButton
            size="1"
            variant="ghost"
            onClick={(e) => {
              e.preventDefault();
              onValueChange("");
            }}
          >
            <Cross2Icon width="14" height="14" />
          </IconButton>
        )}
      </Flex>
    </Box>
  );
}

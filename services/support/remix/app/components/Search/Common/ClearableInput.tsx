import { TextField, IconButton, Text, Box } from "@radix-ui/themes";
import { Cross2Icon } from "@radix-ui/react-icons";

interface ClearableInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  style?: React.CSSProperties;
  error?: string;
  disabled?: boolean;
  onEnterPress?: () => void;
}

export default function ClearableInput({
  value,
  onChange,
  placeholder,
  style,
  error,
  disabled = false,
  onEnterPress,
}: ClearableInputProps) {
  const handleClear = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      onEnterPress?.();
    }
  };

  return (
    <Box style={style} flexGrow="1">
      <TextField.Root
        size="2"
        color={error ? "red" : undefined}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={disabled}
      >
        {value && !disabled && (
          <TextField.Slot side="right">
            <IconButton size="1" variant="ghost" onClick={handleClear}>
              <Cross2Icon width="14" height="14" />
            </IconButton>
          </TextField.Slot>
        )}
      </TextField.Root>
      {error && (
        <Text size="1" color="red" as="p" mt="1">
          {error}
        </Text>
      )}
    </Box>
  );
}

import React, { useCallback } from "react";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Flex, Box, TextField, Button } from "@radix-ui/themes";

import styles from "./SearchInput.module.css";

interface SearchInputProps {
  placeholder: string;
  onSubmit: (value: string) => unknown;
  searchValue: string;
  onSearchValueChange: (value: string) => unknown;
  isLoading?: boolean;
  submitLabel?: string;
  isValid?: boolean;
  additionalControls?: React.ReactNode;
}

export default function SearchInput({
  placeholder,
  onSubmit,
  searchValue,
  onSearchValueChange,
  isLoading,
  submitLabel = "Search",
  isValid = true,
  additionalControls,
}: SearchInputProps) {
  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      onSubmit(searchValue);
    },
    [onSubmit, searchValue],
  );

  return (
    <form onSubmit={handleSubmit} className={styles.searchForm}>
      <Flex gap="3" align="center">
        <Box flexGrow="1">
          <TextField.Root
            placeholder={placeholder}
            value={searchValue}
            onChange={(e) => onSearchValueChange(e.target.value)}
          >
            <TextField.Slot>
              <MagnifyingGlassIcon height="16" width="16" />
            </TextField.Slot>
          </TextField.Root>
        </Box>

        {additionalControls}

        <Button type="submit" disabled={isLoading || !searchValue || !isValid}>
          {isLoading ? "Searching..." : submitLabel}
        </Button>
      </Flex>
    </form>
  );
}

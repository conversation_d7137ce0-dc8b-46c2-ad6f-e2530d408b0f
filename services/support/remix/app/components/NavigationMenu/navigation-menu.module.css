.NavigationMenuRoot {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: var(--slate-2);
  box-shadow: 0 2px 10px var(--black-a7);
  z-index: 1000;
  margin-bottom: 10px;
}

.NavigationMenuList {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  list-style: none;
}

.NavigationMenuLink {
  display: block;
  padding: 16px 20px;
  color: var(--slate-11);
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.NavigationMenuLink:hover {
  background-color: var(--slate-4);
  color: var(--slate-12);
}

.NavigationMenuLink:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--blue-a7);
}

.NavigationMenuLink.active {
  background-color: var(--blue-4);
  color: var(--blue-11);
  position: relative;
}

.NavigationMenuLink.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--blue-9);
}

@media (max-width: 768px) {
  .NavigationMenuList {
    flex-direction: column;
    align-items: stretch;
  }

  .NavigationMenuLink {
    padding: 12px 16px;
  }
}

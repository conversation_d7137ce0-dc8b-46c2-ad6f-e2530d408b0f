import type { <PERSON>a, StoryObj } from "@storybook/react";
import { reactRouterParameters } from "storybook-addon-remix-react-router";

import NavigationMenu from "./NavigationMenu";

const meta: Meta<typeof NavigationMenu> = {
  title: "Components/Navigation Menu",
  component: NavigationMenu,
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;

type Story = StoryObj<typeof NavigationMenu>;

export const ActiveHome: Story = {
  render: () => <NavigationMenu />,
};

export const ActiveTenants: Story = {
  render: () => <NavigationMenu />,
  parameters: {
    reactRouter: reactRouterParameters({
      routing: { path: "/tenants" },
    }),
  },
};

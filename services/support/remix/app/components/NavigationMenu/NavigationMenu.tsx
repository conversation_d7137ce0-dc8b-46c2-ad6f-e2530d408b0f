import { Link, useLocation } from "@remix-run/react";
import * as NavigationMenuPrimitive from "@radix-ui/react-navigation-menu";
import styles from "./navigation-menu.module.css";

const NavigationMenu = () => {
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <NavigationMenuPrimitive.Root className={styles.NavigationMenuRoot}>
      <NavigationMenuPrimitive.List className={styles.NavigationMenuList}>
        <NavigationMenuPrimitive.Item>
          <NavigationMenuPrimitive.Link asChild>
            <Link
              to="/"
              prefetch="render"
              className={`${styles.NavigationMenuLink} ${isActive("/") ? styles.active : ""}`}
            >
              Home
            </Link>
          </NavigationMenuPrimitive.Link>
        </NavigationMenuPrimitive.Item>

        <NavigationMenuPrimitive.Item>
          <NavigationMenuPrimitive.Link asChild>
            <Link
              to="/cross-tenant"
              prefetch="render"
              className={`${styles.NavigationMenuLink} ${isActive("/cross-tenant") ? styles.active : ""}`}
            >
              Cross Tenant
            </Link>
          </NavigationMenuPrimitive.Link>
        </NavigationMenuPrimitive.Item>

        <NavigationMenuPrimitive.Item>
          <NavigationMenuPrimitive.Link asChild>
            <Link
              to="/tenants"
              prefetch="render"
              className={`${styles.NavigationMenuLink} ${isActive("/tenants") ? styles.active : ""}`}
            >
              Tenants
            </Link>
          </NavigationMenuPrimitive.Link>
        </NavigationMenuPrimitive.Item>

        <NavigationMenuPrimitive.Item>
          <NavigationMenuPrimitive.Link asChild>
            <Link
              to="/users"
              prefetch="render"
              className={`${styles.NavigationMenuLink} ${isActive("/users") ? styles.active : ""}`}
            >
              Users
            </Link>
          </NavigationMenuPrimitive.Link>
        </NavigationMenuPrimitive.Item>
      </NavigationMenuPrimitive.List>
    </NavigationMenuPrimitive.Root>
  );
};

export default NavigationMenu;

import { useState, useMemo } from "react";
import { ClientOnly } from "remix-utils/client-only";
import { Link } from "@remix-run/react";
import { Table, Code, Flex, Box, TextField, Button } from "@radix-ui/themes";
import { ChevronLeftIcon, ChevronRightIcon } from "@radix-ui/react-icons";
import type JsonView from "@microlink/react-json-view";

import { useToast } from "./ui/Toast";

const ITEMS_PER_PAGE = 20;
const ROW_HEIGHT = 45; // Standard row height
const HEADER_HEIGHT = 45; // Header row height

export interface TenantListItem {
  id: string;
  name: string;
  config?: {
    configs: Record<string, unknown>;
  };
}

interface TenantListProps {
  tenants: TenantListItem[];
}

export function TenantList({ tenants }: TenantListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const toast = useToast();

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page whenever search query changes
  };

  const filteredTenants = useMemo(
    () =>
      tenants.filter((tenant) =>
        tenant.name.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    [tenants, searchQuery],
  );

  const totalPages = Math.ceil(filteredTenants.length / ITEMS_PER_PAGE);

  const paginatedTenants = useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return filteredTenants.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [filteredTenants, currentPage]);

  const copyTenantId = async (id: string) => {
    await navigator.clipboard.writeText(id);
    toast.success({
      title: "Copied!",
      description: `Tenant ID ${id} copied to clipboard`,
      duration: 2000,
    });
  };

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(1, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(totalPages, prev + 1));
  };

  // Calculate the minimum height needed for the current page
  const minTableHeight = HEADER_HEIGHT + paginatedTenants.length * ROW_HEIGHT;

  return (
    <Box p="6">
      <h1>Tenants</h1>
      <Flex direction="column" gap="3" mb="4" mt="2">
        <TextField.Root
          placeholder="Search tenants..."
          value={searchQuery}
          onChange={handleSearchChange}
        />
      </Flex>
      <div style={{ minHeight: `${minTableHeight}px`, overflowY: "auto" }}>
        <Table.Root variant="surface">
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeaderCell>Tenant Name</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Tenant ID</Table.ColumnHeaderCell>
              <Table.ColumnHeaderCell>Config</Table.ColumnHeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {paginatedTenants.map((tenant) => (
              <Table.Row key={tenant.id}>
                <Table.Cell>
                  <Flex style={{ height: "100%" }}>
                    <Box>
                      <Link to={tenant.id}>{tenant.name}</Link>
                    </Box>
                  </Flex>
                </Table.Cell>
                <Table.Cell>
                  <Flex style={{ height: "100%" }}>
                    <Box>
                      <Code
                        onClick={() => copyTenantId(tenant.id)}
                        style={{ cursor: "pointer" }}
                      >
                        {tenant.id}
                      </Code>
                    </Box>
                  </Flex>
                </Table.Cell>
                <Table.Cell
                  style={{ minWidth: "300px", width: "40%", maxWidth: "600px" }}
                >
                  <ClientOnly fallback={<div>Loading...</div>}>
                    {() => {
                      if (!tenant.config) {
                        return <div>No config</div>;
                      }
                      const src = JSON.parse(
                        JSON.stringify(tenant.config.configs),
                      );
                      return <ReactJsonWrapper src={src} />;
                    }}
                  </ClientOnly>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table.Root>
      </div>

      {totalPages > 1 ? (
        <Flex gap="3" mt="4" justify="center" align="center">
          <Button
            variant="soft"
            onClick={handlePreviousPage}
            disabled={currentPage === 1}
          >
            <ChevronLeftIcon /> Previous
          </Button>

          <Box>
            Page {currentPage} of {totalPages}
          </Box>

          <Button
            variant="soft"
            onClick={handleNextPage}
            disabled={currentPage === totalPages}
          >
            Next <ChevronRightIcon />
          </Button>
        </Flex>
      ) : null}
    </Box>
  );
}

function ReactJsonWrapper({ src }: { src: object }) {
  const [ReactJson, setReactJson] = useState<typeof JsonView | null>(null);

  import("@microlink/react-json-view").then((module) => {
    setReactJson(() => module.default);
  });

  if (!ReactJson) {
    return <div>Loading...</div>;
  }

  return (
    <ReactJson
      src={src}
      name={null}
      theme="rjv-default"
      collapsed={true}
      displayDataTypes={false}
      displayObjectSize={false}
      enableClipboard={false}
      indentWidth={4}
    />
  );
}

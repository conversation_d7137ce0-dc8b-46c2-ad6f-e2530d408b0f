import { LoaderFunctionArgs } from "@remix-run/router";
import { Duration, Timestamp } from "@bufbuild/protobuf";
import { confidentialCachingClient } from "../.server/grpc/request-insight-central";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import {
  TenantWatcherClient,
  TenantWatcherCachingClient,
} from "../.server/grpc/tenant-watcher";
import { ConfidentialSearchResult } from "../types/api";

/* eslint-disable import/no-unresolved */
import {
  CrossTenantConfidentialSearchRequest_Filter,
  CrossTenantConfidentialSearchResponse_Result,
  TimeFilter,
} from "~services/request_insight/central/request_insight_central_pb";
import { Scope } from "~services/token_exchange/token_exchange_pb";
/* eslint-enable import/no-unresolved */

const tenantWatcher: TenantWatcherClient = new TenantWatcherCachingClient();

const cloudToDomainSuffix: { [key: string]: string } = {
  GCP_US_CENTRAL1_DEV: "us-central1.dev.augmentcode.com",
  GCP_US_CENTRAL1_PROD: "us-central1.prod.augmentcode.com",
  GCP_EU_WEST4_PROD: "eu-west4.prod.augmentcode.com",
};

async function transformSearchResults(
  results: CrossTenantConfidentialSearchResponse_Result[],
): Promise<ConfidentialSearchResult[]> {
  const transformedResults = await Promise.all(
    results.map(async (result) => {
      const item: ConfidentialSearchResult = {
        requestId: result.requestId,
        sessionId: result.sessionId,
        requestTime: result.requestTime?.toDate(),
        requestType: result.requestType,
      };

      if (result.tenantInfo) {
        const tenant = await tenantWatcher.tenantFor(
          result.tenantInfo.tenantId,
          "",
        );

        if (tenant) {
          item.tenantId = tenant.id;
          item.tenantName = tenant.name;
          item.shardNamespace = tenant.shardNamespace;
          item.domainSuffix = cloudToDomainSuffix[tenant.cloud];
        }
      }
      return item;
    }),
  );

  return transformedResults.filter(
    (r): r is ConfidentialSearchResult => r !== undefined,
  );
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const startTime = url.searchParams.get("startTime");
  const endTime = url.searchParams.get("endTime");
  const requestId = url.searchParams.get("requestId");
  const sessionId = url.searchParams.get("sessionId");

  if (requestId === null && sessionId === null) {
    return new Response("Provide either requestId or sessionId parameter", {
      status: 400,
    });
  }

  if (requestId !== null && sessionId !== null) {
    return new Response(
      "Provide either requestId or sessionId parameter, not both",
      {
        status: 400,
      },
    );
  }

  if (!startTime) {
    return new Response("startTime parameter is required", {
      status: 400,
    });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  const { signedToken } =
    await getTokenExchangeClient().getSignedTokenForIAPToken({
      iapToken: IAPJWT,
      tenantId: "",
      shardNamespace: "",
      scopes: [Scope.REQUEST_CONFIDENTIAL_R],
      expiration: new Duration({ seconds: BigInt(60 * 60) }),
    });

  // Create time filter based on provided dates
  const startTimestamp = Timestamp.fromDate(new Date(startTime));
  const endTimestamp = endTime
    ? Timestamp.fromDate(new Date(endTime))
    : Timestamp.fromDate(new Date());

  const timeFilter = new TimeFilter({
    startTime: startTimestamp,
    endTime: endTimestamp,
  });

  // Build filters array if IDs are provided
  const filters = [];
  if (requestId) {
    filters.push(
      new CrossTenantConfidentialSearchRequest_Filter({
        filter: {
          case: "requestId",
          value: requestId,
        },
      }),
    );
  } else if (sessionId) {
    filters.push(
      new CrossTenantConfidentialSearchRequest_Filter({
        filter: {
          case: "sessionId",
          value: sessionId,
        },
      }),
    );
  }

  const { results } =
    await confidentialCachingClient.crossTenantConfidentialSearch(
      {
        timeFilter,
        filters,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

  const response = await transformSearchResults(results);

  return Response.json(response);
};

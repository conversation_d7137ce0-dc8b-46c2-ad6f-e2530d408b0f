import { json, useLoaderData } from "@remix-run/react";
import { TenantList, TenantListItem } from "../components/TenantList";
import {
  TenantWatcherClient,
  TenantWatcherCachingClient,
} from "../.server/grpc/tenant-watcher";

const tenantWatcher: TenantWatcherClient = new TenantWatcherCachingClient();

export const loader = async () => {
  const { tenants } = await tenantWatcher.tenantsFor("");
  return json(tenants);
};

export default function Tenants() {
  const tenants = useLoaderData<typeof loader>() as TenantListItem[];
  return <TenantList tenants={tenants} />;
}

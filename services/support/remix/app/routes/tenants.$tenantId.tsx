import { useLoaderData } from "@remix-run/react";
import { json, LoaderFunctionArgs } from "@remix-run/node";
import { Box } from "@radix-ui/themes";

import {
  TenantWatcherClient,
  TenantWatcherCachingClient,
} from "../.server/grpc/tenant-watcher";
import { Tenant } from "~services/tenant_watcher/tenant_watcher_pb";

import SearchLayout from "../components/Search/PerTenant/SearchLayout";

const tenantWatcher: TenantWatcherClient = new TenantWatcherCachingClient();

export async function loader({ params }: LoaderFunctionArgs) {
  const { tenantId } = params;
  if (!tenantId) {
    throw new Response("Tenant ID is required", { status: 400 });
  }

  const tenant = await tenantWatcher.tenantFor(tenantId, "");

  if (!tenant) {
    throw new Response("Tenant not found", { status: 404 });
  }

  return json({ tenant });
}

export default function TenantDetails() {
  const { tenant } = useLoaderData<typeof loader>();

  return (
    <Box m="4">
      <h1>Tenant: {tenant.name}</h1>
      <SearchLayout tenant={tenant as Tenant} />
    </Box>
  );
}

import { ActionFunctionArgs } from "@remix-run/node";
import { Duration } from "@bufbuild/protobuf";
import { ConnectError } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { getAuthCentralClient } from "../.server/grpc/auth-central";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import { connectCodeToHttpStatus } from "../utils/grpc-utils";
import { Scope } from "~services/token_exchange/token_exchange_pb";
import type { ErrorResponse } from "../schemas/users";

export const action = async ({ request, params }: ActionFunctionArgs) => {
  if (request.method !== "DELETE") {
    const errorResponse: ErrorResponse = {
      error: "Method not allowed",
    };
    return Response.json(errorResponse, { status: 405 });
  }

  const { userId, tenantId } = params;

  if (!userId || !tenantId) {
    const errorResponse: ErrorResponse = {
      error: "User ID and Tenant ID are required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Get signed token for authentication
  let signedToken: string;
  try {
    const tokenResponse =
      await getTokenExchangeClient().getSignedTokenForIAPToken({
        tenantId: "", // Empty tenant ID for global operations
        iapToken: IAPJWT,
        scopes: [Scope.AUTH_RW], // Write access required for user removal
        expiration: new Duration({ seconds: BigInt(60 * 60) }), // 1 hour
      });
    signedToken = tokenResponse.signedToken;
  } catch (error) {
    // Handle errors from token exchange
    if (error instanceof ConnectError) {
      logger.error(`Token exchange error: ${error.code} - ${error.message}`);

      const errorResponse: ErrorResponse = {
        error: error.message,
      };

      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in token exchange: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred during authentication",
    };
    return Response.json(errorResponse, { status: 500 });
  }

  // Remove user from tenant
  try {
    await getAuthCentralClient().removeUserFromTenant(
      {
        userId,
        tenantId,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    logger.info(`Successfully removed user ${userId} from tenant ${tenantId}`);

    return Response.json({}, { status: 200 });
  } catch (error) {
    // Handle errors from removeUserFromTenant
    if (error instanceof ConnectError) {
      logger.error(
        `RemoveUserFromTenant error: ${error.code} - ${error.message}`,
      );

      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in removeUserFromTenant: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred while removing user from tenant",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};

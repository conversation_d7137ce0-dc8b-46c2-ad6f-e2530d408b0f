import { LoaderFunctionArgs } from "@remix-run/node";
import { Duration, Timestamp } from "@bufbuild/protobuf";

import { RestrictedSearchResult } from "../types/api";
import {
  TenantWatcherCachingClient,
  TenantWatcherClient,
} from "../.server/grpc/tenant-watcher";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import { restrictedCachingClient } from "../.server/grpc/request-insight-central";

import { Scope } from "~services/token_exchange/token_exchange_pb";
import {
  RestrictedSearchRequest,
  RestrictedSearchRequest_FilterGroup,
  RestrictedSearchRequest_Filter,
  TimeFilter,
  RestrictedSearchResponse_Result,
  RestrictedSearchRequest_FilterConjunction,
} from "~services/request_insight/central/request_insight_central_pb";

const tenantWatcher: TenantWatcherClient = new TenantWatcherCachingClient();

const cloudToDomainSuffix: { [key: string]: string } = {
  GCP_US_CENTRAL1_DEV: "us-central1.dev.augmentcode.com",
  GCP_US_CENTRAL1_PROD: "us-central1.prod.augmentcode.com",
  GCP_EU_WEST4_PROD: "eu-west4.prod.augmentcode.com",
};

async function transformSearchResults(
  results: RestrictedSearchResponse_Result[],
): Promise<RestrictedSearchResult[]> {
  const transformedResults = await Promise.all(
    results.map(async (result) => {
      const item: RestrictedSearchResult = {
        userId: result.userId,
        modelName: result.modelName,
        accepted: result.accepted,
        requestId: result.requestId,
        sessionId: result.sessionId,
        requestTime: result.requestTime?.toDate(),
        requestType: result.requestType,
        httpStatusCode: result.httpStatusCode,
      };

      if (result.tenantInfo) {
        const tenant = await tenantWatcher.tenantFor(
          result.tenantInfo.tenantId,
          "",
        );

        if (tenant) {
          item.tenantId = tenant.id;
          item.tenantName = tenant.name;
          item.shardNamespace = tenant.shardNamespace;
          item.domainSuffix = cloudToDomainSuffix[tenant.cloud];
        }
      }
      return item;
    }),
  );

  return transformedResults.filter(
    (r): r is RestrictedSearchResult => r !== undefined,
  );
}

export async function loader({ request, params }: LoaderFunctionArgs) {
  const { tenantId } = params;
  if (!tenantId) {
    return new Response("Tenant ID is required", { status: 400 });
  }

  const url = new URL(request.url);

  // Get search parameters
  const requestId = url.searchParams.get("requestId");
  const sessionId = url.searchParams.get("sessionId");
  const userId = url.searchParams.get("userId");
  const startTime = url.searchParams.get("startTime");
  const endTime = url.searchParams.get("endTime");

  if (!startTime) {
    return new Response("startTime parameter is required", {
      status: 400,
    });
  }

  // Get optional filters for user search
  const requestType = url.searchParams.get("requestType");
  const accepted = url.searchParams.get("accepted");
  const modelName = url.searchParams.get("modelName");
  const httpStatusCode = url.searchParams.get("httpStatusCode");

  try {
    const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

    const { signedToken } =
      await getTokenExchangeClient().getSignedTokenForIAPToken({
        tenantId,
        iapToken: IAPJWT,
        scopes: [Scope.REQUEST_RESTRICTED_R],
        expiration: new Duration({ seconds: BigInt(60 * 60) }),
      });

    // Create time filter with provided dates
    const startTimestamp = Timestamp.fromDate(new Date(startTime));
    const endTimestamp = endTime
      ? Timestamp.fromDate(new Date(endTime))
      : Timestamp.fromDate(new Date());

    const timeFilter = new TimeFilter({
      startTime: startTimestamp,
      endTime: endTimestamp,
    });

    let filterGroup: RestrictedSearchRequest_FilterGroup | undefined;
    if (
      requestId ||
      sessionId ||
      userId ||
      requestType ||
      accepted !== null ||
      modelName ||
      httpStatusCode
    ) {
      const filters: RestrictedSearchRequest_Filter[] = [];

      if (requestId) {
        filters.push(
          new RestrictedSearchRequest_Filter({
            filter: {
              case: "baseFilter",
              value: {
                filter: {
                  case: "requestId",
                  value: requestId,
                },
              },
            },
          }),
        );
      }

      if (sessionId) {
        filters.push(
          new RestrictedSearchRequest_Filter({
            filter: {
              case: "baseFilter",
              value: {
                filter: {
                  case: "sessionId",
                  value: sessionId,
                },
              },
            },
          }),
        );
      }

      if (userId) {
        filters.push(
          new RestrictedSearchRequest_Filter({
            filter: {
              case: "baseFilter",
              value: {
                filter: {
                  case: "userId",
                  value: userId,
                },
              },
            },
          }),
        );
      }

      if (requestType) {
        filters.push(
          new RestrictedSearchRequest_Filter({
            filter: {
              case: "baseFilter",
              value: {
                filter: {
                  case: "requestType",
                  value: requestType,
                },
              },
            },
          }),
        );
      }

      if (accepted !== null) {
        filters.push(
          new RestrictedSearchRequest_Filter({
            filter: {
              case: "baseFilter",
              value: {
                filter: {
                  case: "accepted",
                  value: accepted === "true",
                },
              },
            },
          }),
        );
      }

      if (modelName) {
        filters.push(
          new RestrictedSearchRequest_Filter({
            filter: {
              case: "baseFilter",
              value: {
                filter: {
                  case: "modelName",
                  value: modelName,
                },
              },
            },
          }),
        );
      }

      if (httpStatusCode) {
        filters.push(
          new RestrictedSearchRequest_Filter({
            filter: {
              case: "baseFilter",
              value: {
                filter: {
                  case: "httpStatusCode",
                  value: parseInt(httpStatusCode, 10),
                },
              },
            },
          }),
        );
      }

      filterGroup = new RestrictedSearchRequest_FilterGroup({
        filters,
        conjunction: RestrictedSearchRequest_FilterConjunction.AND,
      });
    }

    const searchRequest = new RestrictedSearchRequest({
      tenantId,
      timeFilter,
      filters: filterGroup,
    });

    const { results } = await restrictedCachingClient.restrictedSearch(
      searchRequest,
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    const response = await transformSearchResults(results);

    return new Response(JSON.stringify(response), {
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Search request failed:", error);
    return new Response(JSON.stringify({ error: "Failed to perform search" }), {
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }
}

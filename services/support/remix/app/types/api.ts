export interface BaseSearchResult {
  requestId: string;
  sessionId: string;
  requestTime?: Date;
  requestType: string;
  tenantId?: string;
  tenantName?: string;
  shardNamespace?: string;
  domainSuffix?: string;
}

export type ConfidentialSearchResult = BaseSearchResult;

export interface RestrictedSearchResult extends BaseSearchResult {
  userId: string;
  modelName?: string;
  accepted?: boolean;
  httpStatusCode?: number;
}

export const searchTypes = [
  { id: "requestId", name: "Request ID" },
  { id: "sessionId", name: "Session ID" },
];

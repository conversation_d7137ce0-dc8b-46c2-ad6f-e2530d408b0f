import { useState } from "react";
import type { LinksFunction } from "@remix-run/node";
import { Container, Theme } from "@radix-ui/themes";
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from "@remix-run/react";
import {
  QueryCache,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";

import { Toasts, useToast } from "./components/ui/Toast";
import { GeneralErrorBoundary } from "./components/GeneralErrorBoundary";
import NavigationMenu from "./components/NavigationMenu/NavigationMenu";

import resetStyles from "./styles/reset.css?url";
import toastStyles from "./styles/toasts.css?url";
import radixStyles from "@radix-ui/themes/styles.css?url";

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: resetStyles },
  { rel: "stylesheet", href: toastStyles },
  { rel: "stylesheet", href: radixStyles },
];

export function ErrorBoundary() {
  return <GeneralErrorBoundary />;
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <Theme>
          <NavigationMenu />
          <Toasts>{children}</Toasts>
        </Theme>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  const toast = useToast();

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 10 * 1000, // 10 minutes
          },
        },
        queryCache: new QueryCache({
          onError: (err) => {
            if (err instanceof Error) {
              toast.error({
                title: "Request Failed",
                description: err.message || "An unknown error occurred.",
              });
            } else {
              console.error(err);
              toast.error({
                title: "Request Failed",
                description: "An unknown error occurred.",
              });
            }
          },
        }),
      }),
  );

  return (
    <QueryClientProvider client={queryClient}>
      <Container mx="4">
        <Outlet />
      </Container>
    </QueryClientProvider>
  );
}

{"name": "support-ui-v2", "private": true, "sideEffects": false, "type": "module", "scripts": {"build-server": "$npm_execpath tsc -p tsconfig-server.json", "build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "tsx server.ts", "typecheck": "tsc", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build"}, "dependencies": {"@augment-internal/logging": "workspace:*", "@augment-internal/systemenv": "workspace:*", "@bufbuild/protobuf": "^1.10.0", "@connectrpc/connect": "^1.6.1", "@connectrpc/connect-node": "^1.6.1", "@isaacs/express-prometheus-middleware": "^1.2.1", "@microlink/react-json-view": "^1.24.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/themes": "^3.1.6", "@remix-run/express": "^2.16.3", "@remix-run/node": "^2.16.3", "@remix-run/react": "^2.16.3", "@remix-run/router": "^1.23.0", "@remix-run/server-runtime": "^2.16.3", "@tanstack/react-query": "^5.51.24", "compression": "^1.7.5", "dotenv": "^16.4.7", "express": "^4.21.2", "isbot": "^5.1.17", "node-cache": "^5.1.2", "prom-client": "^15.1.3", "react": "^18.3.1", "react-dom": "^18.3.1", "remix-utils": "^7.6.0", "tsx": "^4.19.2", "uuid": "^11.0.4", "zod": "^3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.2", "@remix-run/dev": "^2.15.0", "@remix-run/testing": "^2.15.0", "@storybook/addon-essentials": "^8.4.6", "@storybook/addon-interactions": "^8.4.6", "@storybook/addon-links": "^8.4.6", "@storybook/blocks": "^8.4.6", "@storybook/react": "^8.4.6", "@storybook/react-vite": "^8.4.6", "@storybook/test": "^8.4.6", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/node": "22.10.1", "@types/react": "^18.3.13", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "autoprefixer": "^10.4.20", "esbuild": "^0.24.0", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "postcss": "^8.4.49", "storybook": "^8.4.6", "storybook-addon-remix-react-router": "3", "typescript": "^5.7.2", "vite": "5.4.19", "vite-tsconfig-paths": "^5.1.3"}, "engines": {"node": ">=20.0.0", "npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}}
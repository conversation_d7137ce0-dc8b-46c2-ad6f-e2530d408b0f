# Augment's Support Site!

#### A support web app for Augment engineers.

📖 See the [Remix docs](https://remix.run/docs) and the [Remix Vite docs](https://remix.run/docs/en/main/guides/vite) for details on supported features.

## Development

Set up your dev deployment:

```bash
$ bazel run //services/deploy:dev_deploy -- --services support_ui --operation Apply
```

Run the app in dev mode:

```bash
$ npx ibazel run //services/support/remix:dev
```

_Port-forwarding is required for the Remix app to run in development mode. This is handled by the `setup_port_forward.sh` script and requires that kubectl is installed._

## Deployment

To deploy locally through Bazel run:

```bash
bazel run //services/support/remix:start
```

To deploy to your dev deployment, run:

```bash
bazel run //services/support/remix:kubecfg
```

import path from "path";
import express from "express";
import { fileURLToPath } from "url";
import compression from "compression";
import Prometheus from "prom-client";
import { installGlobals } from "@remix-run/node";
import { createRequest<PERSON><PERSON><PERSON> } from "@remix-run/express";
import prom from "@isaacs/express-prometheus-middleware";
import { logger } from "@augment-internal/logging";
import { isProduction } from "@augment-internal/systemenv";

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename);

const REMIX_DIR = `${__dirname}/../dist`;

async function main(production = isProduction()) {
  logger.info(`starting support-ui ${process.env.NODE_ENV} ${REMIX_DIR}`);
  //This doesn't really have a type
  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  let remixIndex: any;

  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  let viteDevServer: any | undefined;

  if (production) {
    try {
      //eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      remixIndex = await import(`${REMIX_DIR}/server/index.js`);
    } catch (e) {
      logger.warn("failed to import remixIndex ", e);
      throw e;
    }
  } else {
    viteDevServer = await (async () => {
      //eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      return (await import("vite")).createServer({
        server: { middlewareMode: true },
      });
    })();
  }

  const starting = logger.startTimer();
  //nativeFetch per https://github.com/remix-run/remix/issues/6118
  installGlobals({ nativeFetch: true });

  const app = express();

  app.use(compression());

  // http://expressjs.com/en/advanced/best-practice-security.html#at-a-minimum-disable-x-powered-by-header
  app.disable("x-powered-by");

  // handle asset requests
  if (production) {
    // Vite fingerprints its assets so we can cache forever.
    app.use(
      "/assets",
      express.static(`${REMIX_DIR}/client/assets`, {
        immutable: true,
        maxAge: "1h",
      }),
    );
    app.use(
      express.static(`${REMIX_DIR}/client`, { immutable: true, maxAge: "1h" }),
    );
  } else {
    app.use(viteDevServer!.middlewares);
  }
  const metricsApp = production ? express() : undefined;
  Prometheus.register.setDefaultLabels({ app: "support-ui" });
  app.use(
    prom({
      metricsApp,
      metricsPath: "/metrics",
      collectDefaultMetrics: true,
      prefix: "au_",
    }),
  );

  app.all(
    "*",
    createRequestHandler({
      build: production
        ? () => remixIndex
        : () => viteDevServer?.ssrLoadModule("virtual:remix/server-build"),
    }),
  );

  const { PORT: port, METRICS_PORT: metricsPort = 9090 } = process.env;

  app.listen(port, () => {
    starting.done({
      message: `Express server listening at http://localhost:${port}`,
    });
  });

  metricsApp?.listen(metricsPort, () => {
    logger.info(`Metrics server listening on http://localhost:${metricsPort}`);
  });
}

main().catch((e) => {
  console.trace(e);
  process.exit(1);
});

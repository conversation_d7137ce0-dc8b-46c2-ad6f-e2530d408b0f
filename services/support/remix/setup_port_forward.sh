#!/usr/bin/env bash
KUBE_NAMESPACE=${KUBE_NAMESPACE:-dev-$(node -pe "require('$HOME/.augment/user.json').name")}

# Check if required environment variable is set
if [ -z "$KUBE_NAMESPACE" ]; then
	echo "Error: KUBE_NAMESPACE environment variable must be set."
	echo "Usage: KUBE_NAMESPACE=<your-namespace> $0"
	exit 1
fi

# Set the context explicitly
CONTEXT="gke_system-services-dev_us-central1_us-central1-dev"
NAMESPACE=$KUBE_NAMESPACE

echo "Using context: $CONTEXT"
echo "Using namespace: $NAMESPACE"

# Start Kubernetes port forwards
echo "Starting port forward for token-exchange-central-svc..."
kubectl --context $CONTEXT -n $NAMESPACE port-forward service/token-exchange-central-svc 50051:50051 &
echo $! >/tmp/pf_pid_1

echo "Starting port forward for tenant-central-svc..."
kubectl --context $CONTEXT -n $NAMESPACE port-forward service/tenant-central-svc 50052:50051 &
echo $! >/tmp/pf_pid_2

echo "Starting port forward for request-insight-central-svc..."
kubectl --context $CONTEXT -n $NAMESPACE port-forward service/request-insight-central-svc 50053:50051 &
echo $! >/tmp/pf_pid_3

echo "Starting port forward for auth-central-grpc-svc..."
kubectl --context $CONTEXT -n $NAMESPACE port-forward service/auth-central-grpc-svc 50054:50051 &
echo $! >/tmp/pf_pid_4

echo "Port forwards established."

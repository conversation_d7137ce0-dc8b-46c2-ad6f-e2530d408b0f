load("@aspect_rules_js//js:defs.bzl", "js_binary", "js_image_layer", "js_library", "js_run_binary", "js_run_devserver")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//services/support/remix:@remix-run/dev/package_json.bzl", remix_bin = "bin")
load("@npm//services/support/remix:tsx/package_json.bzl", tsx_bin = "bin")
load("//tools/bzl:image.bzl", "image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

npm_link_all_packages()

ASSET_PATTERNS = [
    "public/*",
]

SRC_PATTERNS = [
    "app/**/*.ts",
    "app/**/*.tsx",
    "app/**/*.css",
    ":node_modules/@augment-internal/logging",
    ":node_modules/@augment-internal/systemenv",
]

BUILD_MODULES = [
    ":node_modules/@radix-ui/react-navigation-menu",
    ":node_modules/@radix-ui/react-icons",
    ":node_modules/@radix-ui/react-toast",
    ":node_modules/@radix-ui/themes",
    ":node_modules/@tanstack/react-query",
    ":node_modules/@remix-run/react",
    ":node_modules/isbot",
    ":node_modules/uuid",
    ":node_modules/react",
    ":node_modules/react-dom",
    ":node_modules/remix-utils",
    ":node_modules/@connectrpc/connect",
    ":node_modules/@connectrpc/connect-node",
    ":node_modules/@microlink/react-json-view",
    ":node_modules/node-cache",
    ":node_modules/zod",
    "//services/token_exchange:token_exchange_ts_proto",
    "//services/tenant_watcher:tenant_watcher_ts_proto",
    "//services/request_insight/central:request_insight_central_ts_proto",
    "//services/auth/central/server:auth_ts_proto",
    "//services/auth/central/server:auth_entities_ts_proto",
]

DEV_MODULES = [
    ":vite_config_ts",
    ":node_modules/@remix-run/dev",
    ":node_modules/vite",
    ":node_modules/vite-tsconfig-paths",
]

COMMON = [
    ":npmrc",
    ":package_json",
    ":src",
    ":tsconfig",
]

SERVER_PATTERNS = [
    "server.ts",
    ":node_modules/vite/types/**/*.d.ts",
]

SERVER_RUNTIME = [
    ":node_modules/compression",
    ":node_modules/dotenv",
    ":node_modules/express",
    ":node_modules/prom-client",
    ":node_modules/@augment-internal/logging",
    ":node_modules/@augment-internal/systemenv",
    ":node_modules/@remix-run/node",
    ":node_modules/@remix-run/express",
    ":node_modules/@remix-run/server-runtime",
    ":node_modules/@isaacs/express-prometheus-middleware",
]

SERVER = SERVER_RUNTIME + [
    ":node_modules/@types/compression",
    ":node_modules/@types/express",
]

js_library(
    name = "src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    data = BUILD_MODULES,
)

remix_bin.remix_binary(
    name = "remix",
    chdir = package_name(),
    data = SERVER + COMMON + DEV_MODULES,
)

tsx_bin.tsx_binary(
    name = "tsx",
    chdir = package_name(),
    data = [":node_modules/tsx"],
)

sh_binary(
    name = "dev_wrapper",
    srcs = ["dev_wrapper.sh"],
)

sh_binary(
    name = "setup_port_forward",
    srcs = ["setup_port_forward.sh"],
)

js_run_devserver(
    name = "dev",
    args = [
        "$(location :setup_port_forward)",
        "--",
        "$(location :tsx)",
        "server.ts",
    ],
    data = BUILD_MODULES + DEV_MODULES + COMMON + SERVER + glob(SERVER_PATTERNS) + [
        "tsx",
        ":setup_port_forward",
        "//:node_modules",
    ],
    env = {
        "PORT": "5000",
        "NODE_ENV": "development",
        "IS_MTLS_ENABLED": "false",
        "TOKEN_EXCHANGE_ENDPOINT": "localhost:50051",
        "TENANT_WATCHER_ENDPOINT": "localhost:50052",
        "REQUEST_INSIGHT_CENTRAL_US_ENDPOINT": "localhost:50053",
        "AUTH_CENTRAL_ENDPOINT": "localhost:50054",
    },
    tool = ":dev_wrapper",
)

# js_library flattens a srcs + their dependencies into a single directory, so that
# it can be consumed by remix's js_run_binary. if the srcs are used directly, their
# deps are not flattened and the build fails.
js_library(
    name = "frontend_src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    deps = BUILD_MODULES + ["//third_party/proto:googleapis_status_js_proto"],
)

js_run_binary(
    name = "frontend",
    srcs = [":frontend_src"],
    args = ["vite:build"],
    out_dirs = ["dist"],
    tool = ":remix",
)

ts_config(
    name = "ts-server-config",
    src = "tsconfig-server.json",
    deps = [
        "tsconfig.json",
    ],
)

ts_project(
    name = "ts-server",
    srcs = glob(SERVER_PATTERNS),
    allow_js = True,
    out_dir = "ts_dist",
    resolve_json_module = True,
    tsconfig = ":ts-server-config",
    deps = SERVER,
)

tsx_bin.tsx_binary(
    name = "start",
    args = ["server.ts"],
    chdir = package_name(),
    data = SERVER + BUILD_MODULES + COMMON + [
        ":frontend",
    ] + glob(SERVER_PATTERNS),
    env = {
        "PORT": "5000",
    },
)

js_library(
    name = "server-lib",
    srcs = [
        ":frontend",
        ":ts-server",
    ],
    deps = SERVER_RUNTIME + COMMON,
)

js_binary(
    name = "server",
    data = SERVER_RUNTIME + COMMON + [
        ":frontend",
        ":ts-server",
    ],
    entry_point = "ts_dist/server.js",
)

js_image_layer(
    name = "layers",
    binary = ":server",
)

# we can write a js_oci_image if we do this more often
image(
    name = "image",
    base = "//tools/docker:ubuntu_base_image",
    cmd = [
        "/services/support/remix/server",
    ],
    env = {
        "NODE_ENV": "production",
        "BAZEL_BINDIR": "/services/support/remix/server.runfiles/_main",
    },
    tars = [":layers"],
    # both added indirectly via esbuild
    trivy_allow_list = [
    ],
    workdir = "/",
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
)

js_library(
    name = "npmrc",
    srcs = [".npmrc"],
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
)

js_library(
    name = "vite_config_ts",
    srcs = ["vite.config.ts"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/gcp:gcp-lib",
        "//deploy/tenants:namespaces",
        "//services/deploy:endpoints",
    ],
)

kubecfg(
    name = "kubecfg_monitoring",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "moderate",
    src = "METADATA.jsonnet",
    tags = ["postmerge-test"],
    deps = [
        ":kubecfg",
        ":kubecfg_monitoring",
    ],
)

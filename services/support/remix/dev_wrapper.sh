#!/bin/bash

# Start port forwarding
"$1" &
PF_PID=$!

# Function to cleanup port-forward processes
cleanup() {
	echo "Cleaning up port-forward processes..."
	kill $PF_PID
	wait $PF_PID 2>/dev/null
	# /tmp/pf_pid files are created by setup_port_forward.sh
	if [ -f /tmp/pf_pid_1 ]; then
		kill $(cat /tmp/pf_pid_1)
		rm /tmp/pf_pid_1
	fi
	if [ -f /tmp/pf_pid_2 ]; then
		kill $(cat /tmp/pf_pid_2)
		rm /tmp/pf_pid_2
	fi
	if [ -f /tmp/pf_pid_3 ]; then
		kill $(cat /tmp/pf_pid_3)
		rm /tmp/pf_pid_3
	fi
	if [ -f /tmp/pf_pid_4 ]; then
		kill $(cat /tmp/pf_pid_4)
		rm /tmp/pf_pid_4
	fi
}

# Set up trap to call cleanup function on script exit
trap cleanup EXIT

# Start the Remix dev server
shift 2 # Skip the first two arguments (setup_port_forward and --)
exec "$@"

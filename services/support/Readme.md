# Support Web UI

This package contains the support WEB UI, e.g. to browse requests
from the Request Insight Service.

# Local Development

- Deploy development setup
- Forward request_insight pod's rpc port with `kubectl port-forward [REQUEST-INSIGHT-POD-IN-DEV-NAMESPACE] 50055:50051` or `click`
- Run `bazel run //services/support/backend`
- Goto `services/support/frontend` and run `npm run start`.
- Forward port `3000` from AWS dev VM to Laptop via the vscode port-forwarding
- Visit `http://127.0.0.1:3000/`

Any changes in the backend Python code as well as the Typescript frontend code are automatically reflected.

## Access Control

When access control is enabled, only validated users are allowed to access the support UI.

To add a user, create a `SupportUiAccess` resource in the namespace.

```
    {
      apiVersion: 'eng.augmentcode.com/v1',
      kind: 'SupportUiAccess',
      metadata: {
        name: 'dirk',
        namespace: namespace,
      },
      spec: {
        userName: 'dirk',
        expiredAt: '2024-01-01T12:34:56.789Z',
      },
    },
```

# Configuring IAP

To enable IAP, set the `IAP_AUDIENCE` environment variable to the IAP audience.

- NAMESPACE="dogfood" # adjust as required
- Call `gcloud compute backend-services list --project=system-services-prod --global |grep $NAMESPACE` to get the IAP service name, e.g. `k8s1-f83bb839-dogfood-support-ui-svc-80-484b0574`
- Call `gcloud compute backend-services describe --project=system-services-prod --global k8s1-f83bb839-dogfood-support-ui-svc-80-484b0574` to get the service id, e.g. `2097809824637664191`
- The IAP audience is `/projects/835723878709/global/backendServices/$ID`

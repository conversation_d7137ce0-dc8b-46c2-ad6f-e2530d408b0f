local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local spec = {
    displayName: 'Support UI Request Errors',
    conditionPrometheusQueryLanguage: {
      duration: '120s',
      evaluationInterval: '60s',
      labels: { severity: 'info' },
      query: |||
        sum by (namespace,cluster)(increase(flask_http_request_total{status!~"^200$|^403$|^404$", pod=~"support-ui-.*"}[30m])) > 10
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(cloud, spec, 'support-ui-request-errors', 'HTTP requests to support UI return errors'),
  ]

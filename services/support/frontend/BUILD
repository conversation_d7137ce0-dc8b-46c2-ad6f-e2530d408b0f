load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//services/support/frontend:vite/package_json.bzl", vite_bin = "bin")

npm_link_all_packages()

COMMON = [
    ":index.html",
    ":vite.config.js",
    "//services/support/frontend:tsconfig",
    "//services/support/frontend:package_json",
    "//services/support/frontend/src",
    "//services/support/frontend/public",
]

ALL_MODULES = [
    ":node_modules",
]

BUILD_MODULES = [
    ":node_modules/@vitejs/plugin-react",
    ":node_modules/eslint-plugin-react-hooks",
    ":node_modules/react-dom",
    ":node_modules/react-diff-viewer",
    ":node_modules/vite",
    ":node_modules/react",
    ":node_modules/typescript",
    ":node_modules/axios",
    ":node_modules/antd",
    ":node_modules/ts-pattern",
]

vite_bin.vite(
    name = "frontend",
    srcs = BUILD_MODULES + COMMON + [
        "//services/support/lib/grpc_debug:pkg",
    ],
    args = ["build"],
    chdir = package_name(),
    out_dirs = ["dist"],
    visibility = ["//services/support:__subpackages__"],
)

vite_bin.vite_binary(
    name = "start",
    chdir = package_name(),
    data = ALL_MODULES + COMMON,
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//services/support/frontend:__subpackages__"],
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
    visibility = ["//services/support/frontend:__subpackages__"],
)

js_library(
    name = "jest_config",
    srcs = ["jest.config.js"],
    visibility = ["//services/support/frontend:__subpackages__"],
)

js_library(
    name = "eslintrc_json",
    srcs = [".eslintrc.json"],
    visibility = ["//services/support/frontend:__subpackages__"],
)

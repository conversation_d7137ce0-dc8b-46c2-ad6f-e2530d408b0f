# Support React Frontend

This directory contains the React Frontend code of the Support UI.

### Getting Started

🐢 **Slow development method** (no setup required, but requires a re-deploy on every change)

Build the frontend locally and deploy the Support UI to your dev deployment:

```
bazel run //services/support:kubecfg
```

🐇 **Fast development method** (more setup required, but changes are immediate)

1. **Setup port forwarding to [Vite](https://vitejs.dev/)**

    Vite runs on your dev instance, so you can't access the server it starts until you forward a port.

    Add the following line to your dev instance `Host` section in the `~/.ssh/config` file on your local machine.

    ```
    LocalForward 5173 127.0.0.1:5173
    ```
    _Note: port 5173 is the default port vite runs on_

    Now SSH to your dev instance to complete the following steps.

1. **Deploy the Support UI to your dev deployment**

    ```
    bazel run //services/support:kubecfg
    ```

1. **Set up port forwarding from your dev instance to the deployed backend**

    Vite sets up a proxy to the backend, but needs the port to be forward to get through.

    After deployment finishes, select `support-ui` in `click` and start port forwarding:

    ```
    port-forward 5000
    ```

    This can also be done with kubectl:

    ```
    kubectl port-forward -n dev-your-namespace deployment/support-ui 5000
    ```

1. **Start the frontend development server**
    ```
    cd services/support/frontend
    pnpm start
    ```

1. **Open `http://localhost:5173` in your browser**

    Changes made to the frontend are immediately reflected.

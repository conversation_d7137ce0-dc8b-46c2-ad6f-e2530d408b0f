{"name": "support", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@augment-internal/grpc_debug": "workspace:*", "@ant-design/icons": "^6.0.0", "@testing-library/jest-dom": "4.2.4", "@testing-library/react": "13.4.0", "@testing-library/user-event": "14.4.3", "antd": "^5.24.6", "axios": "^1.8.2", "dayjs": "^1.11.10", "match-sorter": "^6.3.1", "react": "^18.2.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.2.0", "react-highlight-words": "^0.20.0", "react-json-view": "^1.21.3", "react-router-dom": "^6.21.1", "react-use": "^17.5.0", "ts-pattern": "^4.3.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "29.2.3", "@typescript-eslint/eslint-plugin": "5.44.0", "@typescript-eslint/parser": "5.44.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "jest-cli": "29.5.0", "jest-environment-jsdom": "29.3.1", "jest-junit": "16.0.0", "jest-transform-stub": "2.0.0", "typescript": "4.9.3", "vite": "5.4.19"}, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "eslint": "git ls-files -- . | xargs pre-commit run eslint --files", "eslint:fix": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --files", "prettier:fix": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"npm": "please-use-pnpm", "yarn": "please-use-pnpm", "pnpm": "9"}}
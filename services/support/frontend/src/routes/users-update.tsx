import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { LayoutComponent } from "../lib/layout";
import { Spin, Button, notification } from "antd";
import axios from "axios";
import { useFeatureFlags } from "../contexts/FeatureFlagsProvider";
import { UpdateUser } from "../components/UpdateUser";
import { TenantInfoComponent } from "../components/tenant_component";
import * as usersAPI from "../lib/users-api";
import { useTenantInfo } from "../contexts/tenant_provider";
import { useNavigate } from "react-router-dom";
import { GenieButton } from "../lib/genie";

export default function UpdateUserComponent() {
  const { tenantName, userID }: any = useParams();
  const flags = useFeatureFlags();

  const navigate = useNavigate();
  const tenantInfo = useTenantInfo();
  const [isLoading, setLoading] = useState<boolean>(true);
  const [tenantID, setTenantID] = useState<string | undefined>();
  const [userDetails, setUserDetails] = useState<
    (usersAPI.User & usersAPI.UserDetails) | undefined
  >();
  const [errorData, setErrorData] = useState<string | undefined>(undefined);
  const [errorAction, setErrorAction] = useState<typeof Button | undefined>(
    undefined,
  );

  useEffect(() => {
    (async () => {
      setLoading(true);
      if (!tenantID || !userID) {
        return;
      }
      try {
        const user = await usersAPI.getUser(tenantID, userID);
        setUserDetails(user);
        setLoading(false);
      } catch (err) {
        notification.error({
          message: "Failed to get users",
          placement: "topRight",
        });
        console.error("Failed to get users:", err);
      }
    })();
  }, [tenantID, userID]);

  async function updateUsers(
    userID: string,
    formDetails: usersAPI.UserDetails,
  ) {
    setLoading(true);
    try {
      setUserDetails(
        await usersAPI.updateUser(tenantID, userID, {
          ...userDetails,
          ...formDetails,
        }),
      );
    } catch (err) {
      console.error("Failed to update user:", err);
      if (axios.isAxiosError(err) && err.response?.status === 403) {
        setErrorData("You do not have access to this page");
        setErrorAction(<GenieButton kind="users" tenantId={tenantID} />);
      } else {
        notification.error({
          message: "Failed to update users: " + err,
          placement: "topRight",
        });
        setErrorData("Failed to udpate of users, please try again.");
      }
    } finally {
      setLoading(false);
    }
  }

  function getUpdateUser() {
    if (isLoading) {
      return <Spin></Spin>;
    }

    const updateUser = (
      <>
        <h2>Update User [{userDetails.email}]</h2>
        <UpdateUser
          userID={userID}
          userDetails={userDetails}
          onSubmit={updateUsers}
          onCancel={() => navigate(`/t/${tenantName}/users`)}
        />
      </>
    );

    return updateUser;
  }

  function setTenantInfo(tenantName: string, tenantId: string) {
    setTenantID(tenantId);

    return (
      <LayoutComponent
        selectedMenuKey="Users"
        breadcrumbs={[
          { label: "Users", link: `/t/${tenantName}/users` },
          {
            label: "Update User",
            link: `/t/${tenantName}/users/update/${userID}`,
          },
        ]}
        selectedTenantName={tenantName}
        showTenantDropdown={true}
      >
        {getUpdateUser()}
      </LayoutComponent>
    );
  }
  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix={`users/update/${userID}`}
    />
  );
}

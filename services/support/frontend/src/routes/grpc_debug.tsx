import { LayoutComponent } from "../lib/layout";

import { useEffect, useState } from "react";
import { getTenants } from "../lib/tenants";
import { getLastSelectedTenant } from "../lib/tenant-storage";

import GrpcDebugComponent from "@augment-internal/grpc_debug/grpc_debug_component";
import { Tenant } from "@augment-internal/grpc_debug/grpc_debug";

function GrpcDebugPageComponent() {
  const [tenantsInfo, setTenantsInfo] = useState<Tenant[] | undefined>(
    undefined,
  );

  // Get the last selected tenant for the breadcrumbs
  const lastSelectedTenant = getLastSelectedTenant();

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        const t = await getTenants();
        setTenantsInfo(t.map((t) => ({ name: t.name, tenantId: t.id })));
      } catch (e) {
        console.log(`Error while loading the tenants data: ${e}`);
      }
    };
    fetchTenants();
  }, []);

  const children = <GrpcDebugComponent tenantsInfo={tenantsInfo} />;
  return (
    <LayoutComponent
      children={children}
      selectedMenuKey={"grpc"}
      showTenantDropdown={true}
      selectedTenantName={lastSelectedTenant || undefined}
      pageSuffix="grpc-debug"
    />
  );
}

export default GrpcDebugPageComponent;

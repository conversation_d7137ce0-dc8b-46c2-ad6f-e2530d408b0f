// Page to show (or better copy to clipboard) service token.
import {
  Form,
  Checkbox,
  Typography,
  Divider,
  <PERSON><PERSON>,
  Button,
  Spin,
  message,
} from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import { getServiceToken } from "../lib/service_token";
import axios from "axios";
import Paragraph from "antd/es/typography/Paragraph";
import { GenieButton } from "../lib/genie";
import { getTokenScopes } from "../../../lib/grpc_debug/grpc_debug";

const { Text } = Typography;

type TokenScopeForm = {
  token_scopes: string[];
};

function TokenComponent({
  tenantId,
  tenantName,
}: {
  tenantId?: string;
  tenantName?: string;
}) {
  const [secret, setSecret] = useState<string | undefined>(undefined);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(
    undefined,
  );
  const [errorAction, setErrorAction] = useState<Button | undefined>(undefined);
  const [getTokenForm] = Form.useForm();
  const [tokenScopes, setTokenScopes] = useState<string[]>([]);

  if (
    tenantId === undefined &&
    tenantName === undefined &&
    errorMessage === undefined
  ) {
    setErrorMessage("Invalid tenant");
  }

  const copyToClipboard = () => {
    if (!secret) {
      return;
    }
    navigator.clipboard.writeText(secret);
    message.success("Secret copied to clipboard!");
  };

  // Load the token scopes
  useEffect(() => {
    const fetchTokenScopes = async () => {
      try {
        setTokenScopes(await getTokenScopes());
      } catch (e) {
        console.log(`Error while loading the token scopes: ${e}`);
        setErrorMessage("Failed to load token scopes");
      }
    };
    fetchTokenScopes();
  }, []);

  // Request a token
  const onFormFinish = (values: TokenScopeForm) => {
    console.log("Getting a token with scopes:", values.token_scopes);
    getServiceToken(values.token_scopes, tenantId, tenantName)
      .then((data) => {
        console.log("Generated token");
        setSecret(data);
      })
      .catch((err) => {
        if (axios.isAxiosError(err) && err.response?.status === 403) {
          setErrorMessage(
            "You do not have the required permission to generate a token with the requested scopes.",
          );
          setErrorAction(
            <GenieButton redirect={false} tenantId={tenantId} kind="token" />,
          );
        } else {
          console.error("Failed to generate token:", err);
          setErrorMessage("Failed to generate token");
        }
        setSecret(undefined);
      });
  };

  return (
    <>
      <Text>
        <p>
          Request a service token with specific scopes. These can be highly
          restricted access that should only be used with explicit customer
          permission.
        </p>
      </Text>
      <Divider />

      {tokenScopes.length === 0 && !errorMessage && <Spin />}

      {tokenScopes.length > 0 && (
        <Form
          name="request"
          form={getTokenForm}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          style={{ maxWidth: 600 }}
          onFinish={onFormFinish}
          autoComplete="on"
        >
          <Form.Item
            label="Token Scopes"
            name="token_scopes"
            tooltip={{
              title: "Select one or more scopes to include in the token",
              icon: <InfoCircleOutlined />,
            }}
            rules={[
              {
                required: true,
                message: "Please select at least one token scope",
              },
            ]}
          >
            <Checkbox.Group>
              {tokenScopes.map((scope) => (
                <div key={scope}>
                  <Checkbox value={scope}>{scope}</Checkbox>
                </div>
              ))}
            </Checkbox.Group>
          </Form.Item>

          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button type="primary" htmlType="submit">
              Submit
            </Button>
          </Form.Item>
        </Form>
      )}

      {secret && !errorMessage && (
        <div>
          <Paragraph>
            A service token has been generated. The token is valid for 1 hour.
            Keep the token safe and do not share it with anyone.
          </Paragraph>
          <Button onClick={copyToClipboard}>Copy Token to Clipboard</Button>
        </div>
      )}

      {errorMessage && (
        <Alert message={errorMessage} action={errorAction} type="error" />
      )}
    </>
  );
}

function useQuery() {
  return new URLSearchParams(useLocation().search);
}

export default function TokenPageComponent() {
  const query = useQuery();
  const tenantId = query.get("tenant_id") || undefined;
  const tenantName = query.get("tenant_name") || undefined;

  const children = (
    <TokenComponent tenantId={tenantId} tenantName={tenantName} />
  );
  return (
    <>
      <LayoutComponent children={children} showTenantDropdown={false} />
    </>
  );
}

// Page displaying details about a checkpoint.
import { Button, Descriptions, Typography, Spin, Alert, Divider } from "antd";
import { useParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import axios from "axios";
import {
  CheckpointInfoData,
  getCheckpointInfo,
  getBatchedBlobInfo,
  BlobInfoRequest,
  BatchBlobInfoResponse,
} from "../lib/content";
import { BlobList } from "../lib/blob_components";
import { useFeatureFlags } from "../contexts/FeatureFlagsProvider";
import { TenantInfoComponent } from "../components/tenant_component";
import { GenieButton } from "../lib/genie";

const { Text } = Typography;

export function CheckpointComponent({
  tenantId,
  tenantName,
  checkpointId,
  title,
}: {
  tenantId: string;
  tenantName: string;
  checkpointId: string;
  title: string;
}) {
  const [checkpointInfo, setCheckpointInfo] = useState<
    CheckpointInfoData | null | undefined
  >(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [errorData, setErrorData] = useState<string | undefined>(undefined);
  const [errorAction, setErrorAction] = useState<Button | undefined>(undefined);
  const [blobNameToFilepathMap, setBlobNameToFilepathMap] =
    useState<Record<string, string>>();
  const flags = useFeatureFlags();

  const handleBlobFilepathsClick = async () => {
    const newBlobInfo: Record<string, string> = {};

    // Chunk size used as there is a maximum number of blobs that can be retrieved in one request.
    // 3000 is chosen as it is about 60-80% of the maximum number of blobs that can be retrieved in one request.
    const chunkSize = 3000;
    const allBlobNames = checkpointInfo.blobNames;

    const keys: BlobInfoRequest[] = allBlobNames.map((blobName) => {
      return {
        blobName: blobName,
        transformationKey: "",
        subKey: "",
      };
    });

    for (let i = 0; i < keys.length; i += chunkSize) {
      const chunk = keys.slice(i, i + chunkSize);
      console.log(
        "Calling batched blob info API for blobs " + i + "-" + (i + chunkSize),
      );
      const blobInfos: BatchBlobInfoResponse[] = await getBatchedBlobInfo(
        tenantId,
        chunk,
      );

      for (const blobInfo of blobInfos) {
        const blobName = blobInfo.blobContentKey.blobName;
        const metadata = blobInfo.blobInfo?.metadata;
        if (blobName && metadata) {
          newBlobInfo[blobName] =
            metadata.find((e) => e.key === "path")?.value ??
            "Metadata exists but no path key";
        }
      }
    }

    console.log("Updating all blob filepaths");
    setBlobNameToFilepathMap(newBlobInfo);
  };

  useEffect(() => {
    const fetchCheckpointInfo = async () => {
      try {
        setIsLoading(true);
        const newCheckpointInfo = await getCheckpointInfo(
          tenantId,
          checkpointId,
        );
        setCheckpointInfo(newCheckpointInfo);
      } catch (e) {
        console.log(`Error while loading the checkpoint data: ${e}`);
        if (axios.isAxiosError(e)) {
          if (e.response?.status === 404) {
            // request not found
            setCheckpointInfo(null);
          } else if (e.response?.status === 403) {
            setIsLoading(false);
            setErrorData("You do not have access to this checkpoint");
            setErrorAction(<GenieButton tenantId={tenantId} kind="requests" />);
          } else {
            setErrorData("Failed to load data");
          }
        } else {
          setErrorData("Failed to load data");
        }
      }
      setIsLoading(false);
    };
    fetchCheckpointInfo();
  }, [checkpointId]);

  if (errorData !== undefined) {
    return <Alert message={errorData} action={errorAction} type="error" />;
  } else if (isLoading || checkpointInfo === undefined) {
    return <Spin />;
  } else if (checkpointInfo === null) {
    return (
      <Alert
        message="Checkpoint not found"
        description="The checkpoint was not found."
        type="warning"
      />
    );
  } else {
    return (
      <div>
        <Descriptions title={title} bordered column={2}>
          <Descriptions.Item label="Checkpoint ID" span={2}>
            <Text keyboard>{checkpointId}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="Blob count" span={2}>
            <Text>{checkpointInfo.blobNames.length}</Text>
          </Descriptions.Item>
        </Descriptions>

        <Divider></Divider>

        <div>
          <Button onClick={handleBlobFilepathsClick}>
            Retrieve {checkpointInfo.blobNames.length} Blob Filepaths
          </Button>

          <Descriptions bordered column={1}>
            <Descriptions.Item label="Blobs">
              <BlobList
                blobNames={checkpointInfo.blobNames}
                tenantName={tenantName}
                blobNameToFilepathMap={blobNameToFilepathMap}
              />
            </Descriptions.Item>
          </Descriptions>
        </div>
      </div>
    );
  }
}

export default function CheckpointPageComponent() {
  const { checkpointId, tenantName }: any = useParams();

  function setTenantInfo(tenantName: string, tenantId: string) {
    const children = (
      <CheckpointComponent
        checkpointId={checkpointId}
        tenantId={tenantId}
        tenantName={tenantName}
        title={"Checkpoint Info"}
      ></CheckpointComponent>
    );
    return (
      <>
        <LayoutComponent
          children={children}
          breadcrumbs={[
            { label: "content", link: `t/${tenantName}/content` },
            {
              label: `Checkpoint ${checkpointId}`,
              link: `t/${tenantName}/content/checkpoint/${checkpointId}`,
            },
          ]}
          selectedTenantName={tenantName}
          pageSuffix={`content/checkpoint/${checkpointId}`}
        />
      </>
    );
  }

  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix={`content/checkpoint/${checkpointId}`}
    />
  );
}

import { LayoutComponent } from "../lib/layout";
import { useState, useEffect } from "react";
import { UserList } from "../components/UserList";
import { AddUser } from "../components/AddUser";
import { RevokeTokens } from "../components/RevokeTokens";
import { Alert, Button, notification } from "antd";
import * as usersAPI from "../lib/users-api";
import { useFeatureFlags } from "../contexts/FeatureFlagsProvider";
import axios from "axios";
import { useParams } from "react-router-dom";
import { getTenantIdForName, useTenantInfo } from "../contexts/tenant_provider";
import { TenantInfoComponent } from "../components/tenant_component";
import { GenieButton } from "../lib/genie";

export default function UsersComponent() {
  const [users, setUsers] = useState<Array<usersAPI.User> | undefined>();
  const [isLoading, setLoading] = useState<boolean>(false);
  const flags = useFeatureFlags();
  const [errorData, setErrorData] = useState<string | undefined>(undefined);
  const [errorAction, setErrorAction] = useState<typeof Button | undefined>(
    undefined,
  );
  const [tenantId, setTenantId] = useState<string | undefined>(undefined);
  const { tenantName } = useParams();

  async function updateUsers() {
    setLoading(true);
    try {
      setUsers(await usersAPI.getUsers(tenantId || ""));
    } catch (err) {
      console.error("Failed to get users:", err);
      if (axios.isAxiosError(err) && err.response?.status === 403) {
        setErrorData("You do not have access to this page");
        setErrorAction(<GenieButton kind="users" tenantId={tenantId} />);
      } else {
        notification.error({
          message: "Failed to get users: " + err,
          placement: "topRight",
        });
        setErrorData("Failed to load the list of users, please try again.");
      }
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    updateUsers();
  }, [tenantId]);

  function setTenantInfo(tenantName: string, tenantId: string) {
    setTenantId(tenantId);

    return (
      <LayoutComponent
        selectedMenuKey={"users"}
        breadcrumbs={[{ label: "Users", link: `/t/${tenantName}/users` }]}
        selectedTenantName={tenantName}
        showTenantDropdown={true}
      >
        <AddUser onUserAdded={updateUsers} tenantId={tenantId} />
        <RevokeTokens tenantId={tenantId} />
        <UserList
          tenantName={tenantName}
          tenantId={tenantId}
          isLoading={isLoading}
          setLoading={setLoading}
          users={users}
          errorMessage={errorData}
          errorAction={errorAction}
        />
      </LayoutComponent>
    );
  }
  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix="users"
    />
  );
}

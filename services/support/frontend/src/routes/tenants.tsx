// Page displaying details about tenants
import { Typo<PERSON>, Spin, Alert, Table } from "antd";
import { Link } from "react-router-dom";
import React, { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import axios from "axios";
import { getTenants, Tenant } from "../lib/tenants";
import { ColumnType } from "antd/es/table";

const { Text } = Typography;

export function TenantsComponent() {
  const [tenantsInfo, setTenantsInfo] = useState<Tenant[] | undefined>(
    undefined,
  );
  const [isLoading, setIsLoading] = useState(true);
  const [errorData, setErrorData] = useState<string | undefined>(undefined);

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        setIsLoading(true);
        setTenantsInfo(await getTenants());
      } catch (e) {
        console.log(`Error while loading the tenants data: ${e}`);
        if (axios.isAxiosError(e)) {
          setErrorData(`Failed to load data: ${e.message}`);
        } else {
          setErrorData("Failed to load data");
        }
      }
      setIsLoading(false);
    };
    fetchTenants();
  }, []);

  if (errorData !== undefined) {
    return <Alert message={errorData} type="error" />;
  } else if (isLoading || tenantsInfo === undefined) {
    return <Spin />;
  } else {
    const columns: ColumnType<Tenant>[] = [
      {
        title: "Tenant Name",
        key: "name",
        render: (t: Tenant) => {
          return <Link to={`/t/${t.name}`}>{t.name}</Link>;
        },
        sorter: (a: Tenant, b: Tenant) => {
          return a.name.localeCompare(b.name);
        },
      },
      {
        title: "Tenant ID",
        key: "id",
        render: (t: Tenant) => {
          return <Text keyboard>{t.id}</Text>;
        },
        sorter: (a: Tenant, b: Tenant) => {
          return a.id.localeCompare(b.id);
        },
      },
      {
        title: "Config",
        key: "config",
        render: (t: Tenant) => {
          return <Text keyboard>{JSON.stringify(t.config.configs)}</Text>;
        },
      },
    ];

    const table = <Table dataSource={tenantsInfo} columns={columns} />;

    return table;
  }
}

export default function TenantsPageComponent() {
  const children = <TenantsComponent />;
  return (
    <LayoutComponent
      children={children}
      selectedMenuKey={""}
      showTenantDropdown={false}
      pageSuffix={""}
    />
  );
}

import { useParams } from "react-router-dom";
import { LayoutComponent } from "../lib/layout";
import { DeleteUser } from "../components/DeleteUser";
import { Alert } from "antd";
import { useFeatureFlags } from "../contexts/FeatureFlagsProvider";
import { getTenantIdForName, useTenantInfo } from "../contexts/tenant_provider";
import { TenantInfoComponent } from "../components/tenant_component";

export default function DeleteUserComponent() {
  const { tenantName, userID }: any = useParams();
  const flags = useFeatureFlags();

  function setTenantInfo(tenantName: string, tenantId: string) {
    return (
      <LayoutComponent
        selectedMenuKey="Users"
        breadcrumbs={[
          { label: "Users", link: `/t/${tenantName}/users` },
          {
            label: "Delete User",
            link: `/t/${tenantName}/users/delete/${userID}`,
          },
        ]}
        selectedTenantName={tenantName}
        showTenantDropdown={true}
      >
        <h2>Delete User</h2>

        <DeleteUser
          userID={userID}
          tenantID={tenantId}
          tenantName={tenantName}
        />
      </LayoutComponent>
    );
  }
  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix={`users/delete/${userID}`}
    />
  );
}

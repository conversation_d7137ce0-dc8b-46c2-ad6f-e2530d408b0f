import React, { useState } from "react";
import { useParams } from "react-router-dom";
import { TenantInfoComponent } from "../components/tenant_component";
import { LayoutComponent } from "../lib/layout";
import {
  Typography,
  Button,
  Spin,
  Alert,
  Input,
  Space,
  Radio,
  DatePicker,
} from "antd";
import axios from "axios";
import dayjs from "dayjs";

const { Title } = Typography;

type LogEntry = {
  message: string;
  timestamp: number;
};

function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString();
}

function formatLogEntries(entries: LogEntry[]): string {
  if (!entries) {
    return "";
  }
  return entries
    .map((entry) => {
      return `[${formatTimestamp(entry.timestamp)}] ${entry.message}`;
    })
    .join("\n");
}

type FetchMode = "latest" | "endTime";

function RemoteAgentsLogsContent({ tenantId }: { tenantId: string }) {
  const [agentId, setAgentId] = useState<string>("");
  const [responseData, setResponseData] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [olderLoading, setOlderLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [fetchMode, setFetchMode] = useState<FetchMode>("latest");
  const [endTime, setEndTime] = useState<dayjs.Dayjs | null>(null);

  const fetchLogs = async () => {
    if (!agentId) {
      setError("Please enter an agent ID");
      return;
    }

    // Validate time range inputs when in end time mode
    if (fetchMode === "endTime") {
      if (!endTime) {
        setError("Please select an end time");
        return;
      }
    }

    setLoading(true);
    setError(null);
    setResponseData([]);

    try {
      console.log(
        `Fetching logs for agent ID: ${agentId} from tenant: ${tenantId}`,
      );

      let url = `/api/tenant/${tenantId}/remote_agent/${agentId}/logs`;

      // Add end time parameter if in end time mode
      if (fetchMode === "endTime" && endTime) {
        const params = new URLSearchParams();
        params.append("end_time", endTime.unix().toString());
        url += `?${params.toString()}`;
      }

      const response = await axios.get(url);
      console.log("Raw API response:", response);

      setResponseData(response.data);
    } catch (error: any) {
      console.error("Error fetching logs:", error);
      setError("Failed to fetch logs");
    } finally {
      setLoading(false);
    }
  };

  const fetchOlderLogs = async () => {
    if (responseData.length === 0) {
      return;
    }

    setError(null);
    setOlderLoading(true);

    try {
      console.log(
        `Fetching logs for agent ID: ${agentId} from tenant: ${tenantId} starting from ${formatTimestamp(responseData[0].timestamp)})`,
      );

      let url = `/api/tenant/${tenantId}/remote_agent/${agentId}/logs`;
      url += `?end_time=${responseData[0].timestamp}`;

      const response = await axios.get(url);
      console.log("Raw API response:", response);

      // Create a Map using timestamp+message as key for fast lookup
      const uniqueLogs: Map<string, LogEntry> = new Map(
        responseData.map((log: LogEntry) => [
          `${log.timestamp}-${log.message}`,
          log,
        ]),
      );

      // Merge new logs, automatically handling duplicates
      response.data.forEach((log: LogEntry) => {
        uniqueLogs.set(`${log.timestamp}-${log.message}`, log);
      });

      // Convert back to array and sort
      const mergedLogs = Array.from(uniqueLogs.values()).sort(
        (a, b) => a.timestamp - b.timestamp,
      );

      setResponseData(mergedLogs);
    } catch (error: any) {
      console.error("Error fetching logs:", error);
      setError("Failed to fetch logs");
    } finally {
      setOlderLoading(false);
    }
  };

  return (
    <div>
      <Title level={2}>Remote Agents Logs</Title>

      <Space
        direction="vertical"
        style={{ width: "100%", marginBottom: "20px" }}
      >
        {}
        <Space>
          <Input
            placeholder="Enter agent ID (UUID)"
            style={{ width: 300 }}
            value={agentId}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setAgentId(e.target.value)
            }
            onPressEnter={fetchLogs}
          />
          <Button type="primary" onClick={fetchLogs} loading={loading}>
            Fetch Logs
          </Button>
          <Radio.Group
            value={fetchMode}
            onChange={(e: any) => {
              setFetchMode(e.target.value);
              setError(null);
            }}
            size="small"
          >
            <Radio value="latest">Latest</Radio>
            <Radio value="endTime">Before time:</Radio>
          </Radio.Group>
          {fetchMode === "endTime" && (
            <DatePicker
              showTime
              value={endTime}
              onChange={setEndTime}
              placeholder="Select end time"
              style={{ width: 180 }}
              size="small"
            />
          )}
        </Space>

        {responseData.length > 0 && (
          <Space direction="vertical">
            <div>
              Fetched {responseData.length} logs from{" "}
              {formatTimestamp(responseData[0].timestamp)} to{" "}
              {formatTimestamp(responseData[responseData.length - 1].timestamp)}
              {fetchMode === "endTime" && (
                <span style={{ color: "#666", marginLeft: "8px" }}>
                  (Up to {endTime?.format("YYYY-MM-DD HH:mm:ss")})
                </span>
              )}
            </div>
            <Button
              type="default"
              onClick={fetchOlderLogs}
              loading={olderLoading}
            >
              Load Older
            </Button>
          </Space>
        )}

        {error && <Alert message={error} type="error" showIcon />}
      </Space>

      {loading ? (
        <Spin tip="Loading logs..." />
      ) : (
        responseData.length > 0 && (
          <div style={{ marginTop: "20px" }}>
            <pre
              style={{
                backgroundColor: "#f5f5f5",
                padding: "15px",
                borderRadius: "4px",
                maxHeight: "500px",
                overflow: "auto",
                whiteSpace: "pre", // Change to 'pre' to preserve line breaks
                wordBreak: "normal",
              }}
            >
              {formatLogEntries(responseData)}
            </pre>
          </div>
        )
      )}
    </div>
  );
}

export default function RemoteAgentsLogsComponent() {
  const { tenantName } = useParams();

  function setTenantInfo(tenantName: string, tenantId: string) {
    return (
      <LayoutComponent
        children={<RemoteAgentsLogsContent tenantId={tenantId} />}
        selectedMenuKey={"remote_agents_logs"}
        breadcrumbs={[
          {
            label: "Remote Agents Logs",
            link: `/t/${tenantName}/remote-agents-logs`,
          },
        ]}
        selectedTenantName={tenantName}
        showTenantDropdown={true}
        pageSuffix={"remote-agents-logs"}
      />
    );
  }

  return (
    <TenantInfoComponent
      tenantName={tenantName}
      setTenantInfo={setTenantInfo}
      pageSuffix="remote-agents-logs"
    />
  );
}

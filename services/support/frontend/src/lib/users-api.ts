import axios from "axios";

// Add an email to the list of authorized users
export async function addUser(
  tenantId: string,
  email: string,
): Promise<string> {
  const { data: response }: { data: { id: string } } = await axios.post(
    `/api/tenant/${tenantId}/auth/users`,
    { email },
  );
  return response.id;
}

// Revoke a user's tokens
export async function revokeUserTokensByEmail(
  tenantId: string,
  email: string,
): Promise<number> {
  const { data: response }: { data: { tokens_deleted: number } } =
    await axios.post(`/api/tenant/${tenantId}/auth/users/revoke`, { email });
  return response.tokens_deleted;
}

// Get the list of authorized users
export async function getUsers(tenantId: string): Promise<Array<User>> {
  const { data: response }: GetUsersResponse = await axios.get(
    `/api/tenant/${tenantId}/auth/users`,
  );
  return response["users"];
}

export async function getUser(
  tenantId: string,
  userID: string,
): Promise<User & UserDetails> {
  const { data: response }: GetUserResponse = await axios.get(
    `/api/tenant/${tenantId}/auth/users/${userID}`,
  );
  return response;
}

// Delete the user with the given ID
export async function deleteUser(
  tenantId: string,
  userID: string,
): Promise<void> {
  await axios.delete(`/api/tenant/${tenantId}/auth/users/${userID}`);
}

// Revoke a user's tokens
export async function revokeUserTokensByUserId(
  tenantId: string,
  userId: string,
): Promise<number> {
  const { data: response }: { data: { tokens_deleted: number } } =
    await axios.delete(`/api/tenant/${tenantId}/auth/users/${userId}/tokens`);
  return response.tokens_deleted;
}

export async function updateUser(
  tenantID: string,
  userID: string,
  userDetails: UserDetails,
): Promise<User & UserDetails> {
  const { data: response }: { data: UserDetails & User } = await axios.post(
    `/api/tenant/${tenantID}/auth/users/${userID}`,
    userDetails,
  );
  return response;
}

// Block a user
export async function blockUser(
  tenantId: string,
  userId: string,
): Promise<number> {
  const { data: response }: { data: { tokens_deleted: number } } =
    await axios.post(`/api/tenant/${tenantId}/auth/users/${userId}/block`, {});
  return response.tokens_deleted;
}

// Unblock a user
export async function unblockUser(
  tenantId: string,
  userId: string,
): Promise<void> {
  await axios.post(`/api/tenant/${tenantId}/auth/users/${userId}/unblock`, {});
}

type GetUserResponse = {
  data: User & UserDetails;
};

type GetUsersResponse = {
  data: {
    users: Array<User>;
  };
};

export type UserSuspension = {
  suspensionId: string;
  createdTime?: {
    seconds: string;
    nanos: number;
  };
  suspensionType: number;
  evidence: string;
};

export type User = {
  id: string;
  email: string;
  blocked?: boolean;
  suspensions?: UserSuspension[];
};

export type UserDetails = {
  customerUiRoles?: string[] | null;
};

// Shared blob components.

import { Typography, List } from "antd";
import { Link } from "react-router-dom";
const { Text } = Typography;

/**
 * A component that displays a link to a blob.
 * @param blobName The blobName of the blob. If present, will create a link to the blob.
 * @param filePath The file path of the blob. If present, will be used as the link's display name.
 * @returns A text component for the blob.
 */
export function BlobLink({
  blobName,
  filePath,
  tenantName,
}: {
  blobName?: string;
  filePath?: string;
  tenantName: string;
}) {
  let displayName = "?";
  if (filePath) {
    displayName = filePath;
  } else if (blobName) {
    displayName = blobName;
  }
  let result = displayName;
  if (blobName) {
    result = (
      <Link to={`/t/${tenantName}/content/blob/${blobName}`}>
        <Text keyboard>{result}</Text>
      </Link>
    );
  }
  return result;
}

/**
 * A component that displays a list of blobs.
 * @param blobNames The list of blob names.
 * @returns A list of blob links.
 */
export function BlobList({
  blobNames,
  tenantName,
  blobNameToFilepathMap,
}: {
  blobNames: string[];
  tenantName: string;
  blobNameToFilepathMap?: Record<string, string>;
}) {
  return (
    <List
      size="small"
      bordered
      dataSource={blobNames.sort()}
      renderItem={(item: string) => (
        <li>
          <BlobLink blobName={item} tenantName={tenantName} />
          {blobNameToFilepathMap && blobNameToFilepathMap[item] && (
            <Text style={{ marginLeft: "10px" }}>
              {blobNameToFilepathMap[item]}
            </Text>
          )}
          {blobNameToFilepathMap && !blobNameToFilepathMap[item] && (
            <Text style={{ marginLeft: "10px" }}>N/A</Text>
          )}
        </li>
      )}
    />
  );
}

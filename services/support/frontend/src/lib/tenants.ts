import axios from "axios";

export type Tenant = {
  name: string;
  id: string;
  shard_namespace: string;
  cloud: string;
  auth_configuration: {
    domain: string;
  };
  config: {
    configs: {
      [key: string]: string;
    };
  };
};

export async function getTenants(): Promise<Tenant[]> {
  const { data: response }: { data: any } = await axios.get(`/api/tenants`);

  response.sort((a: Tenant, b: Tenant) => a.name.localeCompare(b.name));

  return response;
}

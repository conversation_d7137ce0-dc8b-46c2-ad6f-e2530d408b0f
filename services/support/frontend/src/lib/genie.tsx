import { Button } from "antd";
import { useFeatureFlags } from "../contexts/FeatureFlagsProvider";
import React from "react";

export function GenieButton({
  redirect,
  tenantId,
  kind,
}: {
  redirect?: boolean;
  tenantId?: string;
  kind?: string;
}): React.ReactNode {
  const flags = useFeatureFlags();
  const currentUrl = `${window.location.origin}${location.pathname}${location.search}`;
  if (redirect === undefined) {
    redirect = true;
  }

  // Build URL with query parameters
  const url = new URL(flags.genieUrl);

  // Add parameters if they exist
  if (redirect) {
    url.searchParams.append("redirect_url", currentUrl);
  }
  if (tenantId) {
    url.searchParams.append("tenant_id", tenantId);
  }
  if (kind) {
    url.searchParams.append("kind", kind);
  }

  return (
    <Button type="primary" href={url.toString()}>
      Request access
    </Button>
  );
}

import axios from "axios";

export async function getServiceToken(
  scopes: string[] = [],
  tenantId: string | undefined,
  tenantName: string | undefined,
): Promise<string> {
  if (tenantId === undefined && tenantName === undefined) {
    throw new Error("Must specify either tenant_id or tenant_name");
  }
  const { data: response }: { data: any } = await axios.post(
    `/api/service_token`,
    { scopes: scopes, tenant_id: tenantId, tenant_name: tenantName },
  );
  return response.token_value;
}

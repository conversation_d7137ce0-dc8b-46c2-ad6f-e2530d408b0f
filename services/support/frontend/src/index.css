body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

.expanded-selection {
  color: grey;
}

.site-layout-content {
  position: relative;
  overflow: visible;
}

/* Style the notification container to be positioned within the content area */
.site-layout-content .ant-notification {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1000;
  width: 400px;
  max-width: calc(100% - 32px);
}

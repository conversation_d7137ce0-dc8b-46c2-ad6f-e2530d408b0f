import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";

const FeatureFlagsContext = createContext<FeatureFlags>({
  genieUrl: "",
  grpcDebugEnabled: false,
});

async function getFeatureFlags(): Promise<any> {
  const { data: response }: { data: FeatureFlagsResponse } =
    await axios.get("/api/feature-flags");
  return response.flags;
}

export function useFeatureFlags() {
  const context = useContext(FeatureFlagsContext);
  if (!context) {
    throw new Error(
      "useFeatureFlags must be used within a FeatureFlagsProvider",
    );
  }
  return context;
}

export const FeatureFlagsProvider = ({
  children,
}: FeatureFlagsProviderProps) => {
  const [flags, setFlags] = useState<FeatureFlags>({
    genieUrl: "",
    grpcDebugEnabled: false,
  });

  useEffect(() => {
    (async () => {
      try {
        setFlags(await getFeatureFlags());
      } catch (err) {
        console.error("Failed to get feature flags:", err);
      }
    })();
  }, []);

  return (
    <FeatureFlagsContext.Provider value={flags}>
      {children}
    </FeatureFlagsContext.Provider>
  );
};

type FeatureFlagsResponse = {
  flags: FeatureFlags;
};

export type FeatureFlags = {
  genieUrl: string;
  grpcDebugEnabled: boolean;
};

type FeatureFlagsProviderProps = {
  children?: React.ReactNode;
};

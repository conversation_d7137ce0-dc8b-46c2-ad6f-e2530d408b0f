import React, { createContext, useContext, useState, useEffect } from "react";
import axios from "axios";
import { Tenant, getTenants } from "../lib/tenants";

export type TenantInfo = {
  tenants?: Tenant[];
};

export function getTenantIdForName(name: string, tenantInfo: TenantInfo) {
  // console.log(
  //   `get tenant id for name ${name}, tenant info ${JSON.stringify(tenantInfo)}`,
  // );
  if (tenantInfo.tenants === undefined) {
    return undefined;
  }
  return tenantInfo.tenants.find((t) => t.name === name)?.id;
}

export function getTenantNameForId(id: string, tenantInfo: TenantInfo) {
  // console.log(
  //   `get tenant name for name ${id}, tenant info ${JSON.stringify(tenantInfo)}`,
  // );
  if (tenantInfo.tenants === undefined) {
    return undefined;
  }
  return tenantInfo.tenants.find((t) => t.id === id)?.name;
}

const TenantInfoContext = createContext<TenantInfo>({ tenants: undefined });

export function useTenantInfo() {
  const context = useContext(TenantInfoContext);
  if (!context) {
    throw new Error("useTenantInfo must be used within a TenantInfoContext");
  }
  return context;
}

export const TenantInfoProvider = ({ children }: TenantInfoProviderProps) => {
  const [tenants, setTenants] = useState<Tenant[] | undefined>(undefined);

  useEffect(() => {
    (async () => {
      try {
        const newTenants = await getTenants();
        // console.log(`tenants ${JSON.stringify(newTenants)}`);
        setTenants(newTenants);
      } catch (err) {
        console.error("Failed to get tennats:", err);
      }
    })();
  }, []);

  return (
    <TenantInfoContext.Provider
      value={{
        tenants: tenants,
      }}
    >
      {children}
    </TenantInfoContext.Provider>
  );
};

type TenantInfoProviderProps = {
  children?: React.ReactNode;
};

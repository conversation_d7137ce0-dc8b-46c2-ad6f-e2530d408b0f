import { useState } from "react";
import { Button, Form, Input, Spin, notification } from "antd";
import * as usersAPI from "../lib/users-api";
import axios from "axios";

type userAddForm = {
  emails: string;
};

type Props = {
  tenantId: string;
  onUserAdded: (id: string, email: string) => void;
};

export function AddUser({ onUserAdded, tenantId }: Props) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const [userAdditionForm] = Form.useForm();

  const onFormFinish = async (values: userAddForm) => {
    setLoading(true);
    const emails = values.emails.split(",").map((email) => email.trim());
    const failedUsers: string[] = [];
    const successfulUsers: string[] = [];

    for (const email of emails) {
      try {
        const userID = await usersAPI.addUser(tenantId, email);
        onUserAdded(userID, email);
        successfulUsers.push(email);
      } catch (err) {
        if (axios.isAxiosError(err) && err.response?.status === 403) {
          notification.error({
            message: "You do not have access to this page",
            placement: "topRight",
          });
          setLoading(false);
          return;
        } else {
          failedUsers.push(email);
        }
      }
    }

    if (successfulUsers.length > 0) {
      notification.success({
        message: `Successfully added ${successfulUsers.length} user(s)`,
        placement: "topRight",
      });
    }

    if (failedUsers.length > 0) {
      notification.error({
        message: `Failed to add ${failedUsers.length} user(s)`,
        description: `Failed emails: ${failedUsers.join(", ")}`,
        placement: "topRight",
        duration: 0, // Make the notification persist until manually closed
      });
    }

    userAdditionForm.resetFields();
    setLoading(false);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.error("Finish failed:", errorInfo);
    notification.error({
      message: "Failed to add users, please ensure all fields are valid",
      placement: "topRight",
    });
  };

  const form = (
    <>
      <h2>Add Users</h2>

      <Form
        name="user_addition"
        form={userAdditionForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        initialValues={{ remember: true }}
        onFinish={onFormFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        disabled={isLoading}
      >
        <Form.Item
          label="User Emails"
          name="emails"
          rules={[
            { required: true, message: "Please input the new users' emails" },
          ]}
        >
          <Input.TextArea
            placeholder="Enter email addresses separated by commas"
            rows={4}
          />
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Add Users
          </Button>
        </Form.Item>
      </Form>
    </>
  );

  if (isLoading) {
    return <Spin>{form}</Spin>;
  }
  return form;
}

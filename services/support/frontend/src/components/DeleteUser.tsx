import { useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON>,
  Button,
  Descriptions,
  DescriptionsProps,
  Flex,
  Spin,
  notification,
} from "antd";
import { useEffect, useState } from "react";
import * as usersAPI from "../lib/users-api";
import axios from "axios";

type Props = {
  tenantName: string;
  tenantID: string;
  userID: string;
};

export function DeleteUser({ tenantName, tenantID, userID }: Props) {
  const [isLoading, setLoading] = useState<boolean>(true);
  const [isDeleting, setDeleting] = useState<boolean>(false);
  const [user, setUser] = useState<usersAPI.User | undefined>();
  const navigate = useNavigate();

  useEffect(() => {
    (async () => {
      try {
        const userData = await usersAPI.getUser(tenantID, userID);

        if (!userData) {
          notification.error({
            message: "User not found",
            placement: "topRight",
          });
        }
        setUser(userData);
      } catch (err) {
        console.error("Failed to get user:", err);
        if (axios.isAxiosError(err) && err.response?.status === 403) {
          notification.error({
            message: "You do not have access to this page",
            placement: "topRight",
          });
        } else {
          notification.error({
            message: "Failed to get user: " + err,
            placement: "topRight",
          });
        }
      } finally {
        setLoading(false);
      }
    })();
  }, []);

  async function onCancelClick() {
    navigate(`/users`);
  }

  async function onDeleteClick(userID) {
    setDeleting(true);
    try {
      await usersAPI.deleteUser(tenantID, userID);
      navigate(`/t/${tenantName}/users`);
    } catch (err) {
      console.error("Failed to delete user:", err);
      notification.error({
        message: "Failed to delete user: " + err,
        placement: "topRight",
      });
    } finally {
      setDeleting(false);
    }
  }

  if (isLoading) {
    return <Spin></Spin>;
  }

  if (!user) {
    return (
      <Alert
        message="Failed to load the user details, please try again."
        type="error"
      />
    );
  }

  const items: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "Email",
      children: user.email,
    },
    {
      key: "2",
      label: "ID",
      children: user.id,
    },
  ];

  const page = (
    <>
      <p>Are you sure you want to delete the following user?</p>

      <Descriptions
        bordered
        size="small"
        layout="vertical"
        items={items}
        style={{ display: "inline-block" }}
      />
      <br />
      <br />

      <Flex gap="middle">
        <Button onClick={onCancelClick}>Cancel</Button>
        <Button type="primary" danger onClick={() => onDeleteClick(userID)}>
          Delete User
        </Button>
      </Flex>
    </>
  );

  if (isDeleting) {
    return <Spin>{page}</Spin>;
  }
  return page;
}

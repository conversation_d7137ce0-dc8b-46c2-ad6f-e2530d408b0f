import { RequestData } from "../../lib/requests";
import { Select } from "antd";

export function NextEditSuggestionDropdown({
  requestData,
  value,
  onChange,
}: {
  requestData: RequestData;
  value: number;
  onChange: (value: number) => void;
}) {
  if (
    requestData.nextEditHostRequest === undefined ||
    requestData.nextEditHostResponse === undefined
  ) {
    return <></>;
  }

  const numSuggestions = requestData.nextEditHostResponse.suggestions.length;
  const suggestionItems = requestData.nextEditHostResponse.suggestions
    .map((suggestion, index) => {
      // use suggestions order if defined, or reverse the found order, which seems always to be reverse of presented order
      const order =
        suggestion.suggestionOrder !== undefined
          ? suggestion.suggestionOrder
          : numSuggestions - index;
      const hasChange =
        suggestion.result.suggestedEdit.existingCode !==
        suggestion.result.suggestedEdit.suggestedCode;
      return {
        value: suggestion.result.suggestedEdit.suggestionId,
        order: order,
        label: `${order}: ${hasChange ? "(has change) " : ""}${suggestion.result.suggestedEdit.suggestionId}`,
      };
    })
    .sort((a, b) => a.order - b.order);

  function setSelection(newValue: number) {
    if (newValue !== value) {
      onChange(newValue);
    }
  }

  return (
    <Select value={value} onChange={setSelection} options={suggestionItems} />
  );
}

import { Retrieval<PERSON>hunk } from "../../lib/requests";
import { TokenComponent, WarningComponent } from ".";

export function RetrievalChunkComponent({ doc }: { doc: RetrievalChunk }) {
  if (doc.tokens !== undefined && doc.tokens?.length !== 0) {
    return (
      <pre className="language-plaintext">
        {doc.tokens.map((t, index) => {
          return <TokenComponent token={t} index={index} />;
        })}
      </pre>
    );
  } else if (doc.text !== undefined) {
    return <pre className="language-plaintext">{doc.text}</pre>;
  } else {
    return <WarningComponent />;
  }
}

import { useEffect, useState, useMemo } from "react";

import {
  RequestData,
  getTokens,
  ScoredFileHunk,
  NextEditGeneration,
  NextEditSuggestion,
} from "../../lib/requests";
import { Descriptions, Typography, Divider } from "antd";
import { WarningComponent } from "./Warning";
import { Diff } from "./Diff";
import { TokenComponent } from "./TokenComponent";
import { getBlobContent, BlobContent } from "../../lib/content";

const { Text } = Typography;

const getExistingCode = (
  suggestedEdit: ScoredFileHunk,
  blobContents: Map<string, BlobContent>,
): string => {
  const blobName = suggestedEdit.blobName;
  if (blobName === undefined) {
    return suggestedEdit.existingCode ?? "";
  }
  const blobContent = blobContents.get(blobName);
  if (blobContent === undefined) {
    return suggestedEdit.existingCode ?? "";
  }
  if (
    suggestedEdit.charStart === undefined ||
    suggestedEdit.charEnd === undefined
  ) {
    return suggestedEdit.existingCode ?? "";
  }
  return (
    blobContent.content?.slice(
      Math.max(suggestedEdit.charStart - 100, 0),
      suggestedEdit.charEnd + 100,
    ) ?? ""
  );
};

const getSuggestedCode = (
  suggestedEdit: ScoredFileHunk,
  blobContents: Map<string, BlobContent>,
): string => {
  const blobName = suggestedEdit.blobName;
  const suggestedCode = suggestedEdit.suggestedCode ?? "";
  if (blobName === undefined) {
    return suggestedCode;
  }
  const blobContent = blobContents.get(blobName);
  if (blobContent === undefined) {
    return suggestedCode;
  }
  if (
    suggestedEdit.charStart === undefined ||
    suggestedEdit.charEnd === undefined
  ) {
    return suggestedCode;
  }

  const prefix = blobContent.content?.slice(
    Math.max(suggestedEdit.charStart - 100, 0),
    suggestedEdit.charStart,
  );

  const suffix = blobContent.content?.slice(
    suggestedEdit.charEnd,
    suggestedEdit.charEnd + 100,
  );

  return prefix + suggestedCode + suffix;
};

const fetchBlobsContent = async (
  suggestions: NextEditSuggestion[],
  tenantId: string,
  blobContents: Map<string, BlobContent>,
) => {
  const newBlobContents = new Map(blobContents);
  const blobsToCollect: Set<string> =
    suggestions
      ?.map((suggestion) => suggestion.result.suggestedEdit.blobName)
      .filter((blobName) => blobName !== undefined)
      .filter((blobName) => !newBlobContents.has(blobName))
      .reduce<Set<string>>((blobNamesRequests, blobName) => {
        blobNamesRequests.add(blobName);
        return blobNamesRequests;
      }, new Set()) ?? new Set<string>();

  const promises: Promise<void>[] = Array.from(blobsToCollect)?.map(
    async (blobName) => {
      try {
        const content = await getBlobContent(tenantId, blobName);
        newBlobContents.set(blobName, content);
      } catch (error) {
        console.error(`Error fetching blob content for ${blobName}:`, error);
      }
    },
  );

  await Promise.all(promises);
  return newBlobContents;
};

type GenerationWithSuggestions = NextEditGeneration & {
  suggestions: NextEditSuggestion[];
};

const groupByGeneration = (
  requestData: RequestData,
  suggestions: NextEditSuggestion[],
): GenerationWithSuggestions[] => {
  const suggestionsByOrder =
    suggestions?.sort(
      (s1, s2) => (s1.suggestionOrder ?? 0) - (s2.suggestionOrder ?? 0),
    ) ?? [];

  const generationWithSuggestions =
    requestData.nextEditHostResponse?.generation
      .map((g) => ({
        ...g,
        suggestions: suggestionsByOrder.filter(
          (s) => s.generationId === g.generationId,
        ),
      }))
      .filter((g) => g.suggestions.length > 0) ?? [];

  return generationWithSuggestions;
};

const suggestionHasChange = (suggestedEdit: ScoredFileHunk) => {
  return suggestedEdit.existingCode !== suggestedEdit.suggestedCode;
};

function NextEditOverviewSuggestion({
  suggestion,
  onClick,
  blobContents,
  suggestionId,
}: {
  suggestion: NextEditSuggestion;
  onClick: (suggestion: ScoredFileHunk) => void;
  blobContents: Map<string, BlobContent>;
  suggestionId: string;
}) {
  const suggestedEdit = suggestion.result.suggestedEdit;
  return (
    <div key={suggestedEdit.suggestionId}>
      <Descriptions bordered column={1}>
        <Descriptions.Item
          style={{
            cursor: "pointer",
            backgroundColor: suggestionHasChange(suggestedEdit)
              ? "rgba(82, 196, 26, 0.1)"
              : "rgba(0, 0, 0, 0.02)", // Light green if changed, light gray if not
          }}
        >
          {/* TODO: use suggestionId as key */}
          <div
            onClick={() => {
              onClick(suggestedEdit);
            }}
          >
            {suggestionId === suggestedEdit.suggestionId ? ">>>>" : ""} (
            {suggestionHasChange(suggestedEdit) ? "has change" : "no change"} |{" "}
            {suggestion.postProcessResult}) {suggestedEdit.path} [
            {suggestedEdit.charStart}:{suggestedEdit.charEnd}] →{" "}
            {suggestedEdit.changeDescription}
          </div>
        </Descriptions.Item>
        <Descriptions.Item>
          <Diff
            oldValue={getExistingCode(suggestedEdit, blobContents)}
            newValue={getSuggestedCode(suggestedEdit, blobContents)}
            splitView={false}
          />
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
}

export function NextEditOverviewComponent({
  requestData,
  onChange,
  tenantId,
  suggestionId, // TODO: use suggestionId instead!
}: {
  requestData: RequestData;
  onChange: (value: string) => void;
  tenantId: string;
  suggestionId: string;
}) {
  const suggestions = requestData.nextEditHostResponse?.suggestions;

  // State to store the fetched blob content
  const [blobContents, setBlobContents] = useState<Map<string, BlobContent>>(
    new Map(),
  );

  // Fetch blob content when blobName is available for each suggestion
  useEffect(() => {
    fetchBlobsContent(suggestions ?? [], tenantId, blobContents).then(
      (newBlobContents) => {
        setBlobContents(newBlobContents);
      },
    );
  }, [suggestions, tenantId, blobContents]);

  const generationWithSuggestions: GenerationWithSuggestions[] = useMemo(() => {
    return groupByGeneration(requestData, suggestions ?? []);
  }, [requestData, suggestions]);

  const suggestionClick = (suggestion: ScoredFileHunk | undefined) => {
    if (suggestion?.suggestionId === undefined) {
      return;
    }
    onChange(suggestion?.suggestionId);
  };

  if (suggestions === undefined) {
    return <WarningComponent />;
  }

  return (
    <div>
      {generationWithSuggestions?.map((generation) => {
        return (
          <div key={generation.generationId}>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="Generation">
                <Descriptions bordered column={4}>
                  <Descriptions.Item
                    span={0}
                    labelStyle={{ width: 0 }}
                    contentStyle={{ width: 0 }}
                  >
                    <></>{" "}
                    {/* Empty on purpose. Allows output to span more fully */}
                  </Descriptions.Item>
                  <Descriptions.Item label="Post Process Result">
                    <Text>{generation.postProcessResult}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="Path">
                    <Text>{generation.locationChunk.path}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item label="Editing Score">
                    <Text>{generation.editingScore}</Text>
                  </Descriptions.Item>
                  <Descriptions.Item>
                    <></>
                    <Text style={{ whiteSpace: "pre-wrap" }}>
                      <pre className="language-plaintext">
                        {getTokens(generation.generationOutput)?.map(
                          (t, index) => (
                            <TokenComponent
                              key={index}
                              token={t}
                              index={index}
                            />
                          ),
                        )}
                      </pre>
                    </Text>
                  </Descriptions.Item>
                </Descriptions>
              </Descriptions.Item>

              {/* Show suggestions */}
              {generation.suggestions.map((suggestion) => {
                return (
                  <Descriptions.Item label="Suggestion">
                    <NextEditOverviewSuggestion
                      suggestion={suggestion}
                      onClick={suggestionClick}
                      blobContents={blobContents}
                      suggestionId={suggestionId}
                    />
                  </Descriptions.Item>
                );
              })}
            </Descriptions>
            {/* Show Suggestions End */}

            <Divider />
          </div>
        );
      })}
    </div>
  );
}

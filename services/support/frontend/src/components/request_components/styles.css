/* Apply to all pre elements with language-plaintext class */
pre.language-plaintext {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Apply to all div containers */
div {
  max-width: 100%;
}

/* Apply to all tables */
.ant-table {
  max-width: 100%;
  overflow-x: auto;
}

/* Apply to all description lists */
.ant-descriptions {
  max-width: 100%;
}

/* Apply to all table cells */
.ant-table-cell {
  white-space: pre-wrap;
  word-break: break-word;
}

/* Apply to all pre elements */
pre {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Helper classes */
.pre-wrap {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.container-wrap {
  max-width: 100%;
  overflow-x: hidden;
}

.table-wrap {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
}

.descriptions-wrap {
  max-width: 100%;
}

/* Tool result styling */
.tool-result {
  margin-bottom: 16px;
  padding: 8px;
  border-radius: 4px;
}

/* Message content styling */
.message-content {
  margin-bottom: 16px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

/* Tool results section */
.tool-results {
  margin-top: 16px;
  padding: 8px;
  background-color: #f0f5ff;
  border-radius: 4px;
}

/* PromptComponent specific styles */
.prompt-text {
  white-space: pre-wrap;
}

/* ChatHistoryComponent styles */
.tool-collapsible {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 8px;
}

.tool-collapsible-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  background-color: #f5f5f5;
}

.tool-collapsible-header-expanded {
  border-bottom: 1px solid #e8e8e8;
  border-radius: 4px 4px 0 0;
}

.tool-collapsible-header-collapsed {
  border-radius: 4px;
}

.tool-collapsible-toggle {
  margin-right: 8px;
  font-size: 14px;
  color: #1890ff;
}

.tool-collapsible-content {
  padding: 12px;
}

.chat-history-list {
  margin-bottom: 20px;
}

.chat-history-item {
  margin-bottom: 8px;
}

.chat-history-header {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px 4px 0 0;
}

.user-header {
  background-color: #cff4ce;
}

.assistant-header {
  background-color: #b1d6ee;
}

.chat-history-content {
  padding: 12px;
  border-radius: 0 0 4px 4px;
  margin-bottom: 16px;
}

.user-content {
  background-color: #f9f9f9;
}

.assistant-content {
  background-color: #f0f8ff;
}

.chat-history-request-id {
  margin-bottom: 8px;
}

.tool-result-block {
  margin-bottom: 12px;
}

.text-content {
  margin-bottom: 16px;
}

.tool-tag {
  color: #722ed1;
  font-size: 12px;
  background-color: #f9f0ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.tool-use-tag {
  color: #0066cc;
  font-size: 12px;
  background-color: #f0f8ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.tool-json-container {
  margin-top: 8px;
}

.request-nodes-container {
  max-width: 100%;
  overflow-x: auto;
}

.pre-no-margin {
  margin: 0;
}

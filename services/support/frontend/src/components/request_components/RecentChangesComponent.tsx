import { Descriptions, Divider } from "antd";
import { ReplacementText, RequestData } from "../../lib/requests";
import { WarningComponent } from "./Warning";
import { BlobLink } from "../../lib/blob_components";

function ReplacementTextComponent({ doc }: { doc: ReplacementText }) {
  if (doc.replacementText !== undefined) {
    return <pre className="language-plaintext">{doc.replacementText}</pre>;
  } else {
    return <WarningComponent />;
  }
}

function ReplacementText2Html(
  recentChanges: ReplacementText[],
  tenantName: string,
) {
  return (
    <div>
      {recentChanges.map((d, _idx) => {
        return (
          <div>
            <Divider></Divider>
            <Descriptions bordered column={4}>
              <Descriptions.Item label="File">
                <BlobLink
                  blobName={d.blobName}
                  filePath={d.path}
                  tenantName={tenantName}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Char Range">
                {d.charStart} ~ {d.charEnd}
              </Descriptions.Item>
              <Descriptions.Item label="Present in Blob">
                {d.presentInBlob ? "Yes" : "No"}
              </Descriptions.Item>
              <Descriptions.Item label="Expected Blob">
                {d.expectedBlobName}
              </Descriptions.Item>
            </Descriptions>
            <ReplacementTextComponent doc={d} />
          </div>
        );
      })}
    </div>
  );
}

export function RecentChangesComponent({
  requestData,
  tenantName,
}: {
  requestData: RequestData;
  tenantName: string;
}) {
  if (
    requestData.nextEditHostRequest?.request?.recentChanges !== undefined &&
    requestData.nextEditHostRequest.request.recentChanges.length > 0
  ) {
    const recentChanges = requestData.nextEditHostRequest.request.recentChanges;
    return ReplacementText2Html(recentChanges, tenantName);
  } else {
    return <WarningComponent />;
  }
}

import { GranularEditEvent, RequestData, SingleEdit } from "../../lib/requests";
import { WarningComponent } from "./Warning";

// The sources of fine-grained edit events for next-edit-host and completion-host, respectively.
type EditEventSource = "nextEditHost" | "completionHost";

function showGranularEdit(edit: SingleEdit): string {
  const beforeRange = `${edit.beforeStart}:${
    edit.beforeStart + edit.beforeText.length
  }`;
  const afterRange = `${edit.afterStart}:${
    edit.afterStart + edit.afterText.length
  }`;
  const locStr = `${beforeRange}, ${afterRange}`;
  const beforeText = JSON.stringify(edit.beforeText);
  const afterText = JSON.stringify(edit.afterText);
  if (edit.beforeText === "") {
    return `Insertion(${locStr}, ${afterText})`;
  } else if (edit.afterText === "") {
    return `Deletion(${locStr}, ${beforeText})`;
  } else {
    return `Replacement(${locStr}, ${beforeText}, ${afterText})`;
  }
}

function GranularEditEvents2Html(
  editEvents: GranularEditEvent[],
  tenantName: string,
) {
  const lines: string[] = [];
  let currentPath: string | undefined = undefined;
  let event_id = 0;

  for (const event of editEvents) {
    if (event.path !== currentPath) {
      lines.push("-------------------------------------------------");
      lines.push(`File: ${event.path}`);
      currentPath = event.path;
    }
    lines.push(
      `Event ${event_id++}: ${event.beforeBlobName} -> ${event.afterBlobName}`,
    );
    for (const edit of event.edits) {
      lines.push("\t" + showGranularEdit(edit));
    }
    lines.push(""); // add an empty line between events
  }

  return (
    <pre className="language-plaintext">
      <code>{lines.join("\n")}</code>
    </pre>
  );
}

export function EditEventsComponent({
  requestData,
  tenantName,
  source,
}: {
  requestData: RequestData;
  tenantName: string;
  source: EditEventSource;
}) {
  const editEvents =
    source === "nextEditHost"
      ? requestData.nextEditHostRequest?.request?.editEvents
      : requestData.completionHostRequest?.editEvents;

  if (editEvents && editEvents.length > 0) {
    return GranularEditEvents2Html(editEvents, tenantName);
  } else {
    return <WarningComponent />;
  }
}

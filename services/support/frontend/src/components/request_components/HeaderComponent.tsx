import { Descriptions, Typography } from "antd";
import { RequestData } from "../../lib/requests";

const { Text } = Typography;

export function HeaderComponent({ requestData }: { requestData: RequestData }) {
  const descs: React.ReactNode[] = [];
  const completionRequest = requestData.completionHostRequest;
  const instructionRequest = requestData.instructionHostRequest;
  const editRequest = requestData.editHostRequest;
  const chatRequest = requestData.chatHostRequest;
  const nextEditRequest = requestData.nextEditHostRequest;
  const request =
    completionRequest ||
    instructionRequest?.request ||
    editRequest?.request ||
    chatRequest?.request ||
    nextEditRequest?.request;
  const path = request?.path || "N/A";
  const lang = request?.lang || "N/A";

  // Maximumly use 3 spans
  if (editRequest !== undefined || chatRequest !== undefined) {
    // Display the char range
    const prefixBegin = (editRequest || chatRequest)?.request?.position
      ?.prefixBegin;
    const suffixEnd = (editRequest || chatRequest)?.request?.position
      ?.suffixEnd;
    descs.push(
      <Descriptions.Item label="Path" span={1} key="1">
        <Text keyboard>{path}</Text>
      </Descriptions.Item>,
    );
    descs.push(
      <Descriptions.Item label="Language" span={1} key="2">
        <Text keyboard>{lang}</Text>
      </Descriptions.Item>,
    );
    descs.push(
      // Display the char range
      <Descriptions.Item label="Char Range" span={1} key="3">
        <Text keyboard>
          {prefixBegin} ~ {suffixEnd}
        </Text>
      </Descriptions.Item>,
    );
  } else {
    // Header must span exactly 3 columnns
    descs.push(
      <Descriptions.Item label="Path" span={2} key="7">
        <Text keyboard>{path}</Text>
      </Descriptions.Item>,
    );
    descs.push(
      <Descriptions.Item label="Language" span={1} key="8">
        <Text keyboard>{lang}</Text>
      </Descriptions.Item>,
    );
  }
  return <>{descs}</>;
}

import React from "react";
import { RequestData, getTokens, Token } from "../../lib/requests";
import { Typography, Collapse } from "antd";
import { TokenComponent, WarningComponent } from ".";
import { RetrievalChunkComponent } from "./RetrievalChunkComponent";
import { renderRequestNode } from "./NodeRenderer";
import { ChatHistoryComponent } from "./ChatHistoryComponent";
import "./styles.css";
const { Text, Title } = Typography;

function TokensBlock({
  tokens,
  additionalStyle,
}: {
  tokens: Token[] | undefined;
  additionalStyle?: React.CSSProperties;
}) {
  return (
    tokens && (
      <>
        <Title level={5}>Tokens</Title>
        <Collapse defaultActiveKey={[]}>
          <Collapse.Panel header="View Tokens" key="1">
            <pre className="language-plaintext" style={additionalStyle}>
              {tokens.map((t, index) => {
                t.logProbs = undefined;
                return <TokenComponent token={t} index={index} />;
              })}
            </pre>
          </Collapse.Panel>
        </Collapse>
      </>
    )
  );
}

export function PromptComponent({
  requestData,
  tenantName,
  suggestionId,
}: {
  requestData: RequestData;
  tenantName: string;
  suggestionId?: string | undefined;
}) {
  const chatHistory =
    requestData.chatHostRequest?.request?.chatHistory ||
    requestData.instructionHostRequest?.request?.chatHistory ||
    requestData.remoteToolCallRequest?.codebaseRetrievalRequest?.dialog;

  const chatHistoryBlock = (
    <ChatHistoryComponent
      chatHistory={chatHistory || []}
      tenantName={tenantName}
    />
  );

  if (requestData.completionHostRequest !== undefined) {
    const tokens = getTokens(requestData.completionHostRequest.tokenization);
    return (
      <Text className="prompt-text">
        <pre className="language-plaintext">
          {tokens.map((t, index) => {
            t.logProbs = undefined; // these tokens don't have log probs info
            return <TokenComponent token={t} index={index} />;
          })}
        </pre>
      </Text>
    );
  } else if (
    requestData.editHostRequest !== undefined &&
    requestData.editHostRequest.request
  ) {
    const { instruction, selectedText } = requestData.editHostRequest.request;
    const tokens = getTokens(requestData.editHostRequest.tokenization);
    return (
      <Text className="prompt-text">
        <Title level={5}>Instruction</Title>
        <pre className="language-plaintext">{instruction}</pre>
        <Title level={5}>Selected Text</Title>
        <pre className="language-plaintext">{selectedText}</pre>
        <TokensBlock tokens={tokens} />
      </Text>
    );
  } else if (
    requestData.instructionHostRequest !== undefined &&
    requestData.instructionHostRequest.request
  ) {
    const { instruction, selectedText, targetFilePath, codeBlock } =
      requestData.instructionHostRequest.request;
    const tokens = getTokens(requestData.instructionHostRequest.tokenization);
    return (
      <Text className="prompt-text">
        {instruction && (
          <>
            <Title level={5}>Instruction</Title>
            <pre className="language-plaintext">{instruction}</pre>
          </>
        )}
        {selectedText && (
          <>
            <Title level={5}>Selected Text</Title>
            <pre className="language-plaintext">{selectedText}</pre>
          </>
        )}
        {targetFilePath && (
          <>
            <Title level={5}>Target File Path</Title>
            <pre className="language-plaintext">{targetFilePath}</pre>
          </>
        )}
        {codeBlock && (
          <>
            <Title level={5}>Code Block</Title>
            <pre className="language-plaintext">{codeBlock}</pre>
          </>
        )}
        {chatHistoryBlock}
        <TokensBlock
          tokens={tokens}
          additionalStyle={{
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            overflowWrap: "break-word",
          }}
        />
      </Text>
    );
  } else if (
    requestData.chatHostRequest !== undefined &&
    requestData.chatHostRequest.request
  ) {
    const { message, selectedCode, nodes, toolDefinitions, agentMemories } =
      requestData.chatHostRequest.request;
    const tokens = getTokens(requestData.chatHostRequest.tokenization);

    return (
      <Text className="prompt-text">
        <Title level={5}>Message</Title>
        {nodes && nodes.length > 0 ? (
          <div className="container-wrap">
            {nodes.map((node, index) =>
              renderRequestNode(node, index, tenantName),
            )}
          </div>
        ) : (
          <pre className="language-plaintext pre-wrap">{message}</pre>
        )}
        {selectedCode && (
          <>
            <Title level={5}>Selected Code</Title>
            <pre className="language-plaintext">{selectedCode}</pre>
          </>
        )}
        {agentMemories && (
          <>
            <Title level={5}>Agent Memories</Title>
            <Collapse defaultActiveKey={[]}>
              <Collapse.Panel header="View Memories" key="1">
                <pre className="language-plaintext pre-wrap">
                  {agentMemories}
                </pre>
              </Collapse.Panel>
            </Collapse>
          </>
        )}
        {toolDefinitions && (
          <>
            <Title level={5}>Tools Available</Title>
            <pre className="language-plaintext">
              {toolDefinitions.map((tool: any) => tool.name).join(", ")}
            </pre>
          </>
        )}
        {chatHistoryBlock}
        <TokensBlock tokens={tokens} />
      </Text>
    );
  } else if (
    requestData.nextEditHostRequest !== undefined &&
    requestData.nextEditHostResponse !== undefined &&
    suggestionId !== undefined &&
    requestData.nextEditHostResponse.suggestions &&
    Array.isArray(requestData.nextEditHostResponse.suggestions) &&
    requestData.nextEditHostResponse.generation !== undefined
  ) {
    const suggestion = requestData.nextEditHostResponse.suggestions.find(
      (s) => s.result.suggestedEdit.suggestionId === suggestionId,
    );

    if (!suggestion) {
      return <WarningComponent message="Suggestion not found." />;
    }

    const generation = requestData.nextEditHostResponse.generation.find(
      (g) => g && g.generationId === suggestion.generationId,
    );
    if (!generation) {
      return (
        <WarningComponent
          message={`Generation not found. generationId: ${suggestion.generationId}`}
        />
      );
    }

    const { instruction } = requestData.nextEditHostRequest.request;
    const tokens = getTokens(generation.generationPrompt);
    const locationChunk = generation.locationChunk;

    return (
      <Text className="prompt-text">
        <Title level={5}>Instruction</Title>
        <pre className="language-plaintext">{instruction}</pre>
        {locationChunk && (
          <>
            <Title level={5}>Selection Range</Title>
            <pre className="language-plaintext">
              {locationChunk.charOffset} ~ {locationChunk.charEnd}
            </pre>
          </>
        )}
        {locationChunk && (
          <Collapse defaultActiveKey={[]}>
            <Collapse.Panel header="Location Chunk" key="1">
              <pre className="language-plaintext">
                {RetrievalChunkComponent({ doc: locationChunk })}
              </pre>
            </Collapse.Panel>
          </Collapse>
        )}
        <TokensBlock tokens={tokens} />
      </Text>
    );
  } else if (
    requestData.remoteToolCallRequest?.codebaseRetrievalRequest !== undefined
  ) {
    const { informationRequest } =
      requestData.remoteToolCallRequest.codebaseRetrievalRequest;
    return (
      <Text className="prompt-text">
        <Title level={5}>Information Request</Title>
        <pre className="language-plaintext">{informationRequest}</pre>
        {chatHistoryBlock}
      </Text>
    );
  } else if (
    requestData.remoteToolCallRequest?.runRemoteToolRequest !== undefined
  ) {
    const { toolName, toolInputJson } =
      requestData.remoteToolCallRequest.runRemoteToolRequest;
    let prettyJson: string | undefined = undefined;
    try {
      prettyJson = JSON.stringify(JSON.parse(toolInputJson), null, 2);
    } catch (e) {
      prettyJson = toolInputJson;
    }
    return (
      <Text className="prompt-text">
        <Title level={5}>Tool Name</Title>
        <pre className="language-plaintext">{toolName}</pre>
        <Title level={5}>Tool Input</Title>
        <pre className="language-plaintext">{prettyJson}</pre>
      </Text>
    );
  } else {
    return <WarningComponent />;
  }
}

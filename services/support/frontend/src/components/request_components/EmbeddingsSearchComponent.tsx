import { Descriptions, Divider, Table } from "antd";
import { ColumnType } from "antd/es/table";
import { EmbeddingsSearchResult, RequestData } from "../../lib/requests";
import { WarningComponent } from "./Warning";
import { BlobLink } from "../../lib/blob_components";
import { TokenComponent } from "./TokenComponent";
import { getTokens } from "../../lib/requests";
import { Link } from "react-router-dom";

function BlobChunkLink({
  blobName,
  transformationKey,
  subKey,
  tenantName,
  children,
}: {
  blobName: string;
  transformationKey: string;
  subKey: string;
  tenantName: string;
  children?: React.ReactNode;
}) {
  return (
    <Link
      to={`/t/${tenantName}/content/blob/${blobName}/${encodeURIComponent(
        transformationKey,
      )}/${encodeURIComponent(subKey)}`}
    >
      {children}
    </Link>
  );
}

export function EmbeddingsSearchComponent({
  requestData,
  tenantName,
}: {
  requestData: RequestData;
  tenantName: string;
}) {
  const formatRetrieverType = (type: string): string => {
    const display = {
      DENSE: "Dense",
      SIGNATURE: "Signature",
      RECENCY: "Recency",
      USER_GUIDED: "User Guided",
    };
    return display[type] ?? type;
  };
  const r = requestData;
  if (
    r.embeddingsSearchRequest === undefined ||
    r.embeddingsSearchResponse === undefined
  ) {
    return <WarningComponent />;
  }
  const columns: ColumnType<EmbeddingsSearchResult>[] = [
    {
      title: "Blob Name",
      dataIndex: "blobName",
      key: "blob_name",
      render: (blobName: string) => {
        return <BlobLink blobName={blobName} tenantName={tenantName} />;
      },
    },
    {
      title: "Chunk",
      key: "chunk_index",
      render: (value: EmbeddingsSearchResult) => {
        return (
          <BlobChunkLink
            blobName={value.blobName}
            transformationKey={r.embeddingsSearchRequest!.transformationKey}
            subKey={`chunk-${value.chunkIndex || 0}.pb`}
            tenantName={tenantName}
          >
            {value.chunkIndex || 0}
          </BlobChunkLink>
        );
      },
    },
    {
      title: "Score",
      dataIndex: "value",
      key: "number",
      render: (score: number | undefined) => {
        return <div>{score || 0}</div>;
      },
    },
  ];

  let prompts: Element = <></>;
  const retrievalResponseList = requestData.retrievalResponseList;
  if (retrievalResponseList) {
    const embeddingsPrompts = retrievalResponseList.map((r) => ({
      type: r.retrievalType,
      tokens: getTokens(r.queryPrompt),
    }));
    const items = embeddingsPrompts.map(({ type, tokens }) => (
      <Descriptions.Item
        label={`Prompt - ${formatRetrieverType(type)} Retriever`}
      >
        <pre className="language-plaintext">
          {tokens.map((t, index) => {
            t.logProbs = undefined; // these tokens don't have log probs info
            return <TokenComponent token={t} index={index} />;
          })}
        </pre>
      </Descriptions.Item>
    ));
    if (items.length) {
      prompts = (
        <>
          <Descriptions bordered column={1}>
            {items}
          </Descriptions>
          <Divider></Divider>
        </>
      );
    }
  }
  return (
    <>
      {prompts}
      <Table
        dataSource={requestData.embeddingsSearchResponse?.results || []}
        columns={columns}
      />
    </>
  );
}

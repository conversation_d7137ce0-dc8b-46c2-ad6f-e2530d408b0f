import ReactDiffViewer from "react-diff-viewer";

export interface DiffProps {
  oldValue: string;
  newValue: string;
  splitView: boolean;
}

export const Diff: React.FC<DiffProps> = ({
  oldValue,
  newValue,
  splitView,
}) => {
  return (
    <ReactDiffViewer
      styles={{
        splitView: {
          tableLayout: "auto !important",
        },
        diffContainer: {
          tableLayout: "auto !important",
        },
      }}
      oldValue={oldValue}
      newValue={newValue}
      splitView={splitView}
    />
  );
};

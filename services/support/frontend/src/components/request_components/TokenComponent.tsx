import { Token } from "../../lib/requests";

type ColorTuple = [number, number, number];

const LIGHT_TOKEN_COLOR = "rgb(20,50,20)";
const DARK_TOKEN_COLOR = "rgb(10,20,10)";
const SPECIAL_TOKENCOLOR = "rgb(50,50,150)";

// The background color used to mark how uncertain the model is.
const UNCERTAIN_COLOR: ColorTuple = [255, 50, 50];

// The background color used to mark highly certain tokens.
const CERTAIN_COLOR: ColorTuple = [40, 180, 180];

// The threshold for marking a token as highly certain.
const certainty_threshold = 0.99;

function getColorForToken(t: Token, index: number): string {
  let codeColor = LIGHT_TOKEN_COLOR;
  if (index % 2 === 0) {
    codeColor = DARK_TOKEN_COLOR;
  }
  if (t.text.startsWith("<") && t.text.endsWith(">")) {
    codeColor = SPECIAL_TOKENCOLOR;
  }
  return codeColor;
}

function getBackgroundColorForToken(t: Token): string | undefined {
  if (t.logProbs === undefined || Number.isNaN(t.logProbs)) {
    return undefined;
  }
  const prob = Math.exp(t.logProbs);
  if (prob >= certainty_threshold) {
    const [r, g, b] = CERTAIN_COLOR;
    return `rgba(${r},${g},${b},0.2)`;
  } else if (prob >= 0.5) {
    return undefined;
  } else {
    const alpha = 0.5 - prob;
    const [r, g, b] = UNCERTAIN_COLOR;
    return `rgba(${r},${g},${b},${alpha})`;
  }
}

export function TokenComponent({
  token,
  index,
  is_low_quality,
}: {
  token: Token;
  index: number;
  is_low_quality?: boolean;
}) {
  const codeColor = getColorForToken(token, index);
  const backgroundColor = getBackgroundColorForToken(token);
  const style: {
    color: string;
    backgroundColor?: string;
    textDecoration?: string;
  } = {
    color: codeColor,
  };
  if (backgroundColor !== undefined) {
    style["backgroundColor"] = backgroundColor;
  }
  if (is_low_quality) {
    style["textDecoration"] = "line-through";
  }

  const logp = token.logProbs;
  let toolTip: string;
  if (logp === undefined || Number.isNaN(logp)) {
    toolTip = `Token Id: ${token.tokenId}`;
  } else {
    const prob = Math.exp(logp);
    const prob_percent = (prob * 100).toFixed(2);
    // show probability as two digit percentage
    toolTip = `Token Id: ${token.tokenId} (p=${prob_percent}%)`;
  }

  return (
    <span title={toolTip} style={style} className="token">
      {token.text}
    </span>
  );
}

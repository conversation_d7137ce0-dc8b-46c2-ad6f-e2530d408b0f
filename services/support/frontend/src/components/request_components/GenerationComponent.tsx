import { RequestData, getTokens } from "../../lib/requests";
import { Typography } from "antd";
import { WarningComponent } from "./Warning";
import { Diff } from "./Diff";
import { TokenComponent } from "./TokenComponent";

const { Text } = Typography;

const PREFIX_COLOR = "#BBBBBB";
const SUFFIX_COLOR = "#BBBBBB";

export function GenerationComponent({
  requestData,
  tenantName,
  suggestionId,
}: {
  requestData: RequestData;
  tenantName?: string | undefined;
  suggestionId?: string | undefined;
}) {
  if (
    requestData.completionHostRequest !== undefined &&
    requestData.completionHostResponse !== undefined
  ) {
    const { prefix: reqPrefix, suffix: reqSuffix } =
      requestData.completionHostRequest;
    const { skippedSuffix } = requestData.completionHostResponse;
    const tokens = getTokens(requestData.completionHostResponse.tokenization);
    const prefix = reqPrefix || requestData.inferRequest?.prompt;
    const suffix = reqSuffix || requestData.inferRequest?.suffix;
    const nSkipped = skippedSuffix?.length ?? 0;
    const completionPostProcess = requestData.completionPostProcess;

    return (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">
          <span style={{ color: PREFIX_COLOR }}>{prefix}</span>
          {tokens.map((t, index) => {
            return (
              <TokenComponent
                token={t}
                index={index}
                is_low_quality={completionPostProcess?.isLowQuality}
              />
            );
          })}
          <span
            style={{
              color: SUFFIX_COLOR,
              textDecoration: "line-through",
            }}
          >
            {suffix?.slice(0, nSkipped)}
          </span>
          <span style={{ color: SUFFIX_COLOR }}>{suffix?.slice(nSkipped)}</span>
        </pre>
      </Text>
    );
  } else if (
    requestData.editHostRequest !== undefined &&
    requestData.editHostResponse !== undefined
  ) {
    const { prefix, suffix } = requestData.editHostRequest.request;
    const tokens = getTokens(requestData.editHostResponse.tokenization);
    const text = requestData.editHostResponse.response.text;
    // Split prefix and suffix into lines
    const prefixLines = (prefix || "").split("\n");
    const suffixLines = (suffix || "").split("\n");
    const selectedPrefixLines = prefixLines.slice(-5).join("\n");
    const selectedSuffixLines = suffixLines.slice(0, 5).join("\n");

    return (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">
          <span style={{ color: PREFIX_COLOR }}>{selectedPrefixLines}</span>
          {tokens
            ? tokens.map((t, index) => (
                <TokenComponent token={t} index={index} />
              ))
            : text}
          <span style={{ color: SUFFIX_COLOR }}>{selectedSuffixLines}</span>
        </pre>
      </Text>
    );
  } else if (
    requestData.instructionHostRequest !== undefined &&
    requestData.instructionHostResponse !== undefined
  ) {
    const resultBlocks: React.ReactNode[] = [];
    // Smart-paste - has target file content on request
    if (requestData.instructionHostRequest.request.targetFileContent) {
      const fileContentLines = (
        requestData.instructionHostRequest.request.targetFileContent || ""
      ).split("\n");
      for (const replaceText of requestData.instructionHostResponse.response
        .replaceText) {
        let selectedPrefixLines = "";
        let selectedSuffixLines = "";
        let oldTextBlock = "";
        let replaceTextBlock = "";
        if (replaceText.startLine && replaceText.endLine) {
          const start = replaceText.startLine - 1; // Inclusive
          const end = replaceText.endLine - 1; // Exclusive
          selectedPrefixLines = fileContentLines
            .slice(0, start)
            .slice(-5)
            .join("\n");
          selectedSuffixLines = fileContentLines
            .slice(end)
            .slice(0, 5)
            .join("\n");
          oldTextBlock =
            "\n" + fileContentLines.slice(start, end).join("\n") + "\n";
        }
        replaceTextBlock = `\n${replaceText.text}\n`;
        const diffComponent = (
          <Diff
            oldValue={oldTextBlock}
            newValue={replaceTextBlock}
            splitView={true}
          />
        );
        resultBlocks.push(
          <Text
            style={{
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
              overflowWrap: "break-word",
            }}
          >
            <pre
              className="language-plaintext"
              style={{
                whiteSpace: "pre-wrap",
                wordBreak: "break-word",
                overflowWrap: "break-word",
              }}
            >
              <span style={{ color: PREFIX_COLOR }}>{selectedPrefixLines}</span>
              {diffComponent}
              <span style={{ color: SUFFIX_COLOR }}>{selectedSuffixLines}</span>
            </pre>
          </Text>,
        );
      }
    } else {
      // Instruction - should have selected code, prefix, suffix
      const { prefix, suffix } = requestData.instructionHostRequest.request;
      // Split prefix and suffix into lines
      const prefixLines = (prefix || "").split("\n");
      const suffixLines = (suffix || "").split("\n");
      const selectedPrefixLines = prefixLines.slice(-5).join("\n");
      const selectedSuffixLines = suffixLines.slice(0, 5).join("\n");
      const replaceTexts =
        requestData.instructionHostResponse.response.replaceText
          .map((r) => r.text || "")
          .join("\n...\n");
      const oldTextBlock = `\n${requestData.instructionHostRequest.request.selectedText}\n`;
      const diffComponent = (
        <Diff
          oldValue={oldTextBlock}
          newValue={replaceTexts}
          splitView={true}
        />
      );
      resultBlocks.push(
        <Text
          style={{
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            overflowWrap: "break-word",
          }}
        >
          <pre
            className="language-plaintext"
            style={{
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
              overflowWrap: "break-word",
            }}
          >
            <span style={{ color: PREFIX_COLOR }}>{selectedPrefixLines}</span>
            {diffComponent}
            <span style={{ color: SUFFIX_COLOR }}>{selectedSuffixLines}</span>
          </pre>
        </Text>,
      );
    }
    return <>{resultBlocks}</>;
  } else if (
    requestData.chatHostRequest !== undefined &&
    requestData.chatHostResponse !== undefined
  ) {
    const tokens = getTokens(requestData.chatHostResponse.tokenization);
    const text = requestData.chatHostResponse.response.text;

    return (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">
          {tokens
            ? tokens.map((t, index) => (
                <TokenComponent token={t} index={index} />
              ))
            : text}
        </pre>
      </Text>
    );
  } else if (
    requestData.nextEditHostRequest !== undefined &&
    requestData.nextEditHostResponse !== undefined &&
    suggestionId !== undefined &&
    tenantName !== undefined &&
    Array.isArray(requestData.nextEditHostResponse.suggestions)
  ) {
    const suggestion = requestData.nextEditHostResponse.suggestions.find(
      (s) => s.result.suggestedEdit.suggestionId === suggestionId,
    );
    if (!suggestion) {
      return <WarningComponent message="Suggestion not found. " />;
    }
    const generation = requestData.nextEditHostResponse.generation?.find(
      (g) => g && g.generationId === suggestion.generationId,
    );
    if (!generation) {
      return (
        <WarningComponent
          message={`Generation not found for generationId [${suggestion.generationId}]`}
        />
      );
    }

    const tokens = getTokens(generation.generationOutput);
    const text = generation.generationOutput.text;
    return (
      <>
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {tokens
              ? tokens.map((t, index) => (
                  <TokenComponent token={t} index={index} />
                ))
              : text}
          </pre>
        </Text>
      </>
    );
  } else {
    return <WarningComponent />;
  }
}

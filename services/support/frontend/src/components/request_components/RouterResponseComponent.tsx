import { RequestData } from "../../lib/requests";
import { Typography } from "antd";
const { Text } = Typography;

export function RouterResponseComponent({
  requestData,
}: {
  requestData: RequestData;
}) {
  let response;
  if (requestData.routerResponse?.parsedResponse) {
    response = JSON.stringify(
      requestData.routerResponse.parsedResponse,
      null,
      2,
    );
  } else if (requestData.routerResponse?.errorMessage) {
    response = requestData.routerResponse.errorMessage;
  } else {
    return null;
  }
  return (
    <Text style={{ whiteSpace: "pre-wrap" }}>
      <pre className="language-plaintext">{response}</pre>
    </Text>
  );
}

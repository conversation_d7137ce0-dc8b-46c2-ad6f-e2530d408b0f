import { getTokens, RequestData } from "../../lib/requests";
import { Typography } from "antd";
import { TokenComponent } from "./TokenComponent";
const { Text } = Typography;

export function RouterPromptTokensComponent({
  requestData,
}: {
  requestData: RequestData;
}) {
  const routerResponse = requestData.routerResponse;
  if (!routerResponse) {
    return null;
  }
  const tokens = getTokens(routerResponse.promptTokens);
  if (tokens.length === 0) {
    return null;
  }
  return (
    <>
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">
          {tokens.map((t, index) => {
            t.logProbs = undefined;
            return <TokenComponent token={t} index={index} />;
          })}
        </pre>
      </Text>
    </>
  );
}

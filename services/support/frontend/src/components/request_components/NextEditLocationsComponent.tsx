import { RequestData } from "../../lib/requests";
import { WarningComponent } from "./Warning";
import { ReceivedChunks2Html } from "./ReceivedChunks2HtmlComponent";

export function NextEditLocationsComponent({
  requestData,
  tenantName,
}: {
  requestData: RequestData;
  tenantName: string;
}) {
  if (
    requestData.nextEditHostResponse?.retrievedLocations !== undefined &&
    requestData.nextEditHostResponse.retrievedLocations.length > 0
  ) {
    const retrievedLocations =
      requestData.nextEditHostResponse.retrievedLocations;
    const blockedLocations = (
      requestData.nextEditHostResponse.blockedLocations ?? []
    ).map((l) => {
      return {
        ...l,
        origin: "blocked_" + l.origin,
      };
    });

    return ReceivedChunks2Html(
      [...retrievedLocations, ...blockedLocations],
      tenantName,
    );
  } else {
    return <WarningComponent />;
  }
}

import { RequestData } from "../../lib/requests";
import { WarningComponent } from "./Warning";
import { ReceivedChunks2Html } from "./ReceivedChunks2HtmlComponent";

export function ReceivedChunksComponent({
  requestData,
  tenantName,
  suggestionId,
}: {
  requestData: RequestData;
  tenantName: string;
  suggestionId?: string | undefined;
}) {
  if (
    // this has to be before "retrievalResponseList" that catches all cases.
    requestData.nextEditHostResponse !== undefined &&
    suggestionId !== undefined
  ) {
    const suggestion = requestData.nextEditHostResponse.suggestions.find(
      (s) => s.result.suggestedEdit.suggestionId === suggestionId,
    );
    if (!suggestion) {
      return <WarningComponent message="Suggestion not found." />;
    }
    const generationId = suggestion.generationId;
    const generation = requestData.nextEditHostResponse.generation.find(
      (g) => g.generationId === generationId,
    );
    if (generation === undefined) {
      return <WarningComponent />;
    } else {
      return ReceivedChunks2Html(
        generation.retrievedChunks,
        tenantName,
        requestData.embeddingsSearchResponse?.results,
      );
    }
  } else if (
    requestData.retrievalResponseList !== undefined &&
    requestData.retrievalResponseList.length > 0
  ) {
    const retrievedChunks = requestData.retrievalResponseList
      .map((r) => r.retrievedChunks)
      .flat();
    return ReceivedChunks2Html(
      retrievedChunks,
      tenantName,
      requestData.embeddingsSearchResponse?.results,
    );
  } else if (
    requestData.completionHostResponse?.retrievedChunks !== undefined
  ) {
    return ReceivedChunks2Html(
      requestData.completionHostResponse.retrievedChunks,
      tenantName,
      requestData.embeddingsSearchResponse?.results,
    );
  } else if (requestData.editHostRequest?.retrievedChunks !== undefined) {
    return ReceivedChunks2Html(
      requestData.editHostRequest.retrievedChunks,
      tenantName,
    );
  } else if (
    requestData.instructionHostRequest?.retrievedChunks !== undefined
  ) {
    return ReceivedChunks2Html(
      requestData.instructionHostRequest.retrievedChunks,
      tenantName,
    );
  } else if (requestData.chatHostRequest?.retrievedChunks !== undefined) {
    return ReceivedChunks2Html(
      requestData.chatHostRequest.retrievedChunks,
      tenantName,
    );
  } else {
    return <WarningComponent />;
  }
}

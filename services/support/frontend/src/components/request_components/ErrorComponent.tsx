import { RequestData } from "../../lib/requests";
import { Typography } from "antd";
const { Text } = Typography;

export function ErrorComponent({ requestData }: { requestData: RequestData }) {
  const errorMessage = (
    requestData.chatHostResponse || requestData.instructionHostResponse
  )?.errorMessage;
  if (errorMessage) {
    return (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">{errorMessage}</pre>
      </Text>
    );
  }
  // Return null when there's no error message
  return null;
}

import { Reranker<PERSON>hunk, RequestData } from "../../lib/requests";
import { WarningComponent } from "./Warning";
import { Descriptions, Divider } from "antd";
import { BlobLink } from "../../lib/blob_components";
import { RetrievalChunkComponent } from "./RetrievalChunkComponent";

function RerankedChunks2Html(
  retrievedChunks: RerankerChunk[],
  tenantName: string,
) {
  return (
    <div>
      {retrievedChunks.map((d, idx) => {
        return (
          <div>
            <Divider></Divider>
            <Descriptions bordered column={4}>
              <Descriptions.Item label="File">
                <BlobLink
                  blobName={d.blobName}
                  filePath={d.path}
                  tenantName={tenantName}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Char Range">
                {d.charOffset} ~ {d.charEnd}
              </Descriptions.Item>
              <Descriptions.Item label="Origin">
                {d.origin}, chunk-{d.chunkIndex}
              </Descriptions.Item>
              <Descriptions.Item label="Score/Rank">
                {d.score || "N/A"} / {idx}
              </Descriptions.Item>
            </Descriptions>
            <Descriptions bordered column={4}>
              <Descriptions.Item label="Description">
                {d.shortDescription || "N/A"}
              </Descriptions.Item>
            </Descriptions>
            <RetrievalChunkComponent doc={d} />
          </div>
        );
      })}
    </div>
  );
}

export function RerankedChunksComponent({
  requestData,
  tenantName,
}: {
  requestData: RequestData;
  tenantName: string;
}) {
  if (
    requestData.rerankerResponseList !== undefined &&
    requestData.rerankerResponseList.length > 0
  ) {
    const rerankedChunks = requestData.rerankerResponseList
      .map((r) => r.rerankedChunks)
      .flat();
    return RerankedChunks2Html(rerankedChunks, tenantName);
  } else {
    return <WarningComponent />;
  }
}

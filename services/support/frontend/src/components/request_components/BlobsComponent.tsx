import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../lib/blob_components";
import {
  RequestData,
  mergeBlo<PERSON>,
  getAdded<PERSON>romBlo<PERSON>,
  makeBlobs,
} from "../../lib/requests";
import {
  getBatchedBlobInfo,
  BlobInfoRequest,
  BatchBlobInfoResponse,
} from "../../lib/content";

import { Typography, Descriptions, Button } from "antd";
import { Link } from "react-router-dom";
import { useState } from "react";
const { Text } = Typography;

/**
 * A component that displays a link to a checkpoint.
 * @param checkpointId The checkpointId of the checkpoint.
 * @returns A text component for the checkpoint.
 */
function CheckpointLink({
  tenantName,
  checkpointId,
}: {
  tenantName: string;
  checkpointId?: string;
}) {
  let result = <Text keyboard>N/A</Text>;
  if (checkpointId) {
    result = (
      <Link to={`/t/${tenantName}/content/checkpoint/${checkpointId}`}>
        <Text keyboard>{checkpointId}</Text>
      </Link>
    );
  }
  return result;
}

export function BlobsComponent({
  requestData,
  tenantName,
  tenantId,
}: {
  requestData: RequestData;
  tenantName: string;
  tenantId: string;
}) {
  const blobs =
    makeBlobs(requestData.completionHostRequest?.blobs) ||
    makeBlobs(requestData.editHostRequest?.request?.blobs) ||
    makeBlobs(requestData.instructionHostRequest?.request?.blobs) ||
    requestData.chatHostRequest?.request?.blobs ||
    makeBlobs(requestData.nextEditHostRequest?.request?.blobs) ||
    requestData.remoteToolCallRequest?.codebaseRetrievalRequest?.blobs;
  // Continue to support old requests that used blob_names, even though we
  // always send blobs to the backend
  const blobNames =
    requestData.editHostRequest?.request.blobNames ||
    requestData.completionHostRequest?.blobNames ||
    requestData.inferRequest?.memories ||
    [];

  const [blobNameToFilepathMap, setBlobNameToFilepathMap] =
    useState<Record<string, string>>();

  // User-Guided blobs are (currently) only supported for chat requests
  let userGuidedBlobItem;
  const userGuidedBlobs = requestData.chatHostRequest?.request.userGuidedBlobs;
  if (userGuidedBlobs !== undefined && userGuidedBlobs.length > 0) {
    userGuidedBlobItem = (
      <>
        <Descriptions.Item label="User-Guided Blobs">
          <BlobList blobNames={userGuidedBlobs} tenantName={tenantName} />
        </Descriptions.Item>
      </>
    );
  }

  const allAddedBlobNames = mergeBlobs(getAddedFromBlobs(blobs), blobNames);

  const handleBlobFilepathsClick = async () => {
    const newBlobInfo: Record<string, string> = {};

    const keys: BlobInfoRequest[] = allAddedBlobNames.map((blobName) => {
      return {
        blobName: blobName,
        transformationKey: "",
        subKey: "",
      };
    });

    const blobInfos: BatchBlobInfoResponse[] = await getBatchedBlobInfo(
      tenantId,
      keys,
    );

    for (const blobInfo of blobInfos) {
      const blobName = blobInfo.blobContentKey.blobName;
      const metadata = blobInfo.blobInfo?.metadata;
      if (blobName && metadata) {
        newBlobInfo[blobName] =
          metadata.find((e) => e.key === "path")?.value ??
          "Metadata exists but no path key";
      }
    }

    setBlobNameToFilepathMap(newBlobInfo);
  };

  const retrieveButton = (
    <Button onClick={handleBlobFilepathsClick}>
      Retrieve {allAddedBlobNames.length} Blob Filepaths
    </Button>
  );

  const blobsDescs: React.ReactNode[] = [];
  for (const blob of blobs || []) {
    const blobDesc: React.ReactNode = (
      <Descriptions bordered column={1}>
        <Descriptions.Item label="Checkpoint ID">
          <CheckpointLink
            checkpointId={blob.baselineCheckpointId}
            tenantName={tenantName}
          />
        </Descriptions.Item>
        <Descriptions.Item label="Added blobs">
          <BlobList
            blobNames={allAddedBlobNames}
            tenantName={tenantName}
            blobNameToFilepathMap={blobNameToFilepathMap}
          />
        </Descriptions.Item>
        <Descriptions.Item label="Deleted blobs">
          <BlobList blobNames={blob.deleted || []} tenantName={tenantName} />
        </Descriptions.Item>
      </Descriptions>
    );
    blobsDescs.push(blobDesc);
  }

  return (
    <div>
      {retrieveButton}
      {blobsDescs}
      <Descriptions bordered column={1}>
        {...userGuidedBlobItem ? [userGuidedBlobItem] : []}
      </Descriptions>
    </div>
  );
}

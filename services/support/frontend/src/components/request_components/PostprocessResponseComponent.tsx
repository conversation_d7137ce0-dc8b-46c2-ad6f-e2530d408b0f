import { RequestData, getTokens } from "../../lib/requests";
import { Typography } from "antd";
import { TokenComponent } from "./TokenComponent";

const { Text } = Typography;

export function PostprocessResponseComponent({
  requestData,
}: {
  requestData: RequestData;
}) {
  if (requestData.postprocessResponse !== undefined) {
    const tokens = getTokens(requestData.postprocessResponse.tokenization);
    const text =
      requestData.postprocessResponse.tokenization?.text ||
      requestData.postprocessResponse.errorMessage;

    // Only return content if there are tokens or text
    if (tokens.length || text) {
      return (
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">
            {tokens.length
              ? tokens.map((t, index) => (
                  <TokenComponent token={t} index={index} />
                ))
              : text}
          </pre>
        </Text>
      );
    }
  }
  // Return null when there's no content
  return null;
}

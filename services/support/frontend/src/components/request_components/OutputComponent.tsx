import { RequestData, getTokens } from "../../lib/requests";
import { Descriptions, Typography } from "antd";
import { WarningComponent } from "./Warning";
import { Diff } from "./Diff";
import { renderResponseNode } from "./NodeRenderer";
import { TokenComponent } from "./TokenComponent";
import { match, P } from "ts-pattern";
import "./styles.css";

const { Text, Title } = Typography;

function renderDescription(suggestion) {
  if (suggestion.descriptionOutput) {
    const output_tokens = getTokens(suggestion.descriptionOutput);
    const output_text = suggestion.descriptionOutput.text;
    return (
      <>
        <Title level={5}>Description Output</Title>
        <pre className="language-plaintext">
          {output_tokens
            ? output_tokens.map((t, index) => (
                <TokenComponent token={t} index={index} />
              ))
            : { output_text }}
        </pre>
      </>
    );
  } else if (suggestion.result?.suggestedEdit?.changeDescription) {
    return (
      <>
        <Title level={5}>Change Description</Title>
        <pre className="language-plaintext">
          {suggestion.result.suggestedEdit.changeDescription}
        </pre>
      </>
    );
  }
  return null;
}

function renderDescriptionPrompt(suggestion) {
  if (!suggestion.descriptionPrompt) return null;
  const prompt_tokens = getTokens(suggestion.descriptionPrompt);
  const prompt_text = suggestion.descriptionPrompt.text;
  return (
    <>
      <Title level={5}>Description Prompt</Title>
      <pre className="language-plaintext">
        {prompt_tokens
          ? prompt_tokens.map((t, index) => (
              <TokenComponent token={t} index={index} />
            ))
          : { prompt_text }}
      </pre>
    </>
  );
}

export function OutputComponent({
  requestData,
  suggestionId,
}: {
  requestData: RequestData;
  suggestionId?: string | undefined;
}) {
  return match(requestData)
    .with(
      {
        inferenceHostResponse: {
          tokenization: P.select("tokenization"),
        },
      },
      ({ tokenization }) => {
        const tokens = getTokens(tokenization);
        return (
          <Text style={{ whiteSpace: "pre-wrap" }}>
            <pre className="language-plaintext">
              {tokens.map((t, index) => {
                return <TokenComponent token={t} index={index} />;
              }) ?? []}
            </pre>
          </Text>
        );
      },
    )
    .with({ editHostResponse: { response: { text: P.select() } } }, (text) => (
      <Text style={{ whiteSpace: "pre-wrap" }}>
        <pre className="language-plaintext">{text}</pre>
      </Text>
    ))
    .with(
      {
        chatHostResponse: {
          response: {
            text: P.select("text"),
            nodes: P.optional(P.select("nodes")),
          },
        },
      },
      ({ text, nodes }) => {
        if (nodes) {
          return (
            <div style={{ maxWidth: "100%", overflowX: "auto" }}>
              {nodes.map((node: any, index: number) =>
                renderResponseNode(node, index),
              )}
            </div>
          );
        }
        return (
          <Text style={{ whiteSpace: "pre-wrap" }}>
            <pre className="language-plaintext">{text}</pre>
          </Text>
        );
      },
    )
    .with(
      {
        instructionHostResponse: {
          response: {
            text: P.select("text"),
            replaceText: P.select("replaceText"),
          },
        },
      },
      ({ text, replaceText }) => (
        <Text
          style={{
            whiteSpace: "pre-wrap",
            wordBreak: "break-word",
            overflowWrap: "break-word",
          }}
        >
          <pre
            className="language-plaintext"
            style={{
              whiteSpace: "pre-wrap",
              wordBreak: "break-word",
              overflowWrap: "break-word",
            }}
          >
            {replaceText.length
              ? replaceText.map((r) => JSON.stringify(r, null, 2)).join("\n")
              : text}
          </pre>
        </Text>
      ),
    )
    .with(
      {
        nextEditHostResponse: { suggestions: P.select() },
      },
      (suggestions) => {
        const suggestion = suggestions.find(
          (s) => s.result?.suggestedEdit?.suggestionId === suggestionId,
        );
        if (!suggestion) {
          return (
            <WarningComponent
              message={`Suggestion not found. suggestionId: ${suggestionId}`}
            />
          );
        }

        const suggestedEdit = suggestion?.result?.suggestedEdit;

        if (!suggestedEdit) {
          return <WarningComponent message="Suggested edit not found." />;
        }

        const isChanged =
          suggestedEdit.existingCode !== suggestedEdit.suggestedCode;

        const diffComponent = (
          <Diff
            oldValue={suggestedEdit.existingCode ?? ""}
            newValue={suggestedEdit.suggestedCode ?? ""}
            splitView={false}
          />
        );

        return (
          <div style={{ overflowX: "auto", maxWidth: "100vw" }}>
            {suggestedEdit.path && (
              <Descriptions bordered>
                <Descriptions.Item label="File">
                  <Text keyboard>{suggestedEdit.path}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Char Range" span={1}>
                  <Text keyboard>
                    {suggestedEdit.charStart} ~ {suggestedEdit.charEnd}
                  </Text>
                </Descriptions.Item>
                <Descriptions bordered></Descriptions>
                <Descriptions.Item label="Localization Score" span={1}>
                  <Text keyboard>{suggestedEdit.localizationScore}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Editing Score" span={1}>
                  <Text keyboard>{suggestedEdit.editingScore}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="Editing Score Threshold" span={1}>
                  <Text keyboard>{suggestedEdit.editingScoreThreshold}</Text>
                </Descriptions.Item>
              </Descriptions>
            )}
            {isChanged ? diffComponent : <Text>No change</Text>}
            <Text style={{ whiteSpace: "pre-wrap" }}>
              {renderDescription(suggestion)}
              {renderDescriptionPrompt(suggestion)}
            </Text>
          </div>
        );
      },
    )
    .with(
      {
        remoteToolCallResponse: {
          codebaseRetrievalResponse: { formattedRetrieval: P.select() },
        },
      },
      (formattedRetrieval) => (
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">{formattedRetrieval}</pre>
        </Text>
      ),
    )
    .with(
      {
        remoteToolCallResponse: {
          runRemoteToolResponse: { toolOutput: P.select() },
        },
      },
      (toolOutput) => (
        <Text style={{ whiteSpace: "pre-wrap" }}>
          <pre className="language-plaintext">{toolOutput}</pre>
        </Text>
      ),
    )
    .otherwise(() => <WarningComponent />);
}

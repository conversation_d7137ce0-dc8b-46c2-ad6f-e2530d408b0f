import { Typography, Collapse } from "antd";
import "./styles.css";
import { RequestNode, ResponseNode } from "../../lib/requests";
import { Link } from "react-router-dom";
import ReactJson from "react-json-view";
import React from "react";

const { Text } = Typography;

const styles = {
  header: {
    marginBottom: "4px",
    padding: "4px 0",
  },

  preWrap: {
    whiteSpace: "pre-wrap",
    margin: 0,
  },

  nodeContainer: {
    marginBottom: "8px",
  },
};

/**
 * Common function to render a node header
 */
function renderNodeHeader(title: string | React.ReactNode, isError = false) {
  return (
    <div
      style={{
        ...styles.header,
        width: "100%",
        display: "flex",
        alignItems: "center",
      }}
    >
      <strong style={{ flex: 1 }}>
        {title}
        {isError ? <Text type="danger"> (Error)</Text> : null}
      </strong>
    </div>
  );
}

/**
 * Common function to render content
 */
function renderContent(content: string, usePreTag = true) {
  if (usePreTag) {
    return <pre className="language-plaintext pre-wrap">{content}</pre>;
  }
  return <p style={styles.preWrap}>{content}</p>;
}

/**
 * Common function to render JSON content
 */
function renderJsonContent(jsonString: string) {
  try {
    const parsedJson = JSON.parse(jsonString);
    return (
      <div className="tool-json-container">
        <ReactJson
          src={parsedJson}
          name={false}
          collapsed={1}
          displayDataTypes={false}
          enableClipboard={false}
          theme="rjv-default"
        />
      </div>
    );
  } catch (e) {
    // If parsing fails, use the original string
    return renderContent(jsonString);
  }
}

/**
 * Renders a request node (from request_nodes)
 */
export function renderRequestNode(
  node: RequestNode,
  nodeIndex: number,
  tenantName: string,
) {
  if (node.type === "TEXT" && node.textNode) {
    return (
      <div key={`req-node-${nodeIndex}`} style={styles.nodeContainer}>
        {renderContent(node.textNode.content, false)}
      </div>
    );
  } else if (node.type === "TOOL_RESULT" && node.toolResultNode) {
    const isError = node.toolResultNode.isError;
    const toolUseId = node.toolResultNode.toolUseId;
    const title = node.toolResultNode.requestId ? (
      <>
        Tool Result (
        <Link to={`/t/${tenantName}/request/${node.toolResultNode.requestId}`}>
          {node.toolResultNode.toolUseId}
        </Link>
        )
      </>
    ) : (
      `Tool Result (${toolUseId})`
    );

    return (
      <div key={`req-node-${nodeIndex}`} style={styles.nodeContainer}>
        <Collapse defaultActiveKey={[]} className="tool-result-collapse">
          <Collapse.Panel header={renderNodeHeader(title, isError)} key="1">
            {renderContent(node.toolResultNode.content)}
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  } else if (node.type === "IMAGE") {
    let imageType: string | undefined;
    switch (node.imageNode?.format) {
      case "PNG":
        imageType = "png";
        break;
      case "JPEG":
      case "JPG":
        imageType = "jpeg";
        break;
    }
    if (!imageType) {
      return (
        <div key={`req-node-${nodeIndex}`} style={styles.nodeContainer}>
          <Collapse defaultActiveKey={[]} className="tool-result-collapse">
            <Collapse.Panel header={renderNodeHeader("Image")} key="1">
              <Text>[Unknown image format {node.imageNode?.format}]</Text>
            </Collapse.Panel>
          </Collapse>
        </div>
      );
    }
    const imageSrc = node?.imageNode?.imageData
      ? `data:image/${imageType};base64,${node.imageNode.imageData}`
      : "[No image data available]";
    return (
      <div key={`req-node-${nodeIndex}`} style={styles.nodeContainer}>
        <Collapse defaultActiveKey={[]} className="tool-result-collapse">
          <Collapse.Panel header={renderNodeHeader("Image")} key="1">
            <img
              src={imageSrc}
              alt="Image content"
              style={{ maxWidth: "100%" }}
            />
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  } else if (node.type === "IDE_STATE" && node.ideStateNode) {
    return (
      <div key={`req-node-${nodeIndex}`} style={styles.nodeContainer}>
        <Collapse defaultActiveKey={[]} className="tool-result-collapse">
          <Collapse.Panel header={renderNodeHeader("IDE State")} key="1">
            <div>
              {node.ideStateNode.currentTerminal && (
                <div>
                  <strong>Current Terminal:</strong>
                  <ul>
                    <li>
                      Working Directory:{" "}
                      {
                        node.ideStateNode.currentTerminal
                          .currentWorkingDirectory
                      }
                    </li>
                    <li>
                      Terminal ID:{" "}
                      {node.ideStateNode.currentTerminal.terminalId}
                    </li>
                  </ul>
                </div>
              )}
              {node.ideStateNode.workspaceFolders &&
                node.ideStateNode.workspaceFolders.length > 0 && (
                  <div>
                    <strong>Workspace Folders:</strong>
                    <ul>
                      {node.ideStateNode.workspaceFolders.map(
                        (folder, index) => (
                          <li key={index}>
                            Folder Root: {folder.folderRoot}
                            {folder.repositoryRoot && (
                              <div>
                                Repository Root: {folder.repositoryRoot}
                              </div>
                            )}
                          </li>
                        ),
                      )}
                    </ul>
                  </div>
                )}
              <div>
                <strong>Workspace Folders Unchanged:</strong>{" "}
                {node.ideStateNode.workspaceFoldersUnchanged ? "Yes" : "No"}
              </div>
            </div>
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  }
  return (
    <div key={`req-node-${nodeIndex}`} style={styles.nodeContainer}>
      <Collapse defaultActiveKey={[]} className="tool-result-collapse">
        <Collapse.Panel
          header={renderNodeHeader(`Unknown node type: ${node.type}`)}
          key="1"
        />
      </Collapse>
    </div>
  );
}

/**
 * Renders a response node (from response_nodes)
 */
export function renderResponseNode(node: ResponseNode, nodeIndex: number) {
  if (node.type === "RAW_RESPONSE" || node.type === "MAIN_TEXT_FINISHED") {
    return (
      <div key={`node-${nodeIndex}`} style={styles.nodeContainer}>
        {renderContent(node.content || "", false)}
      </div>
    );
  } else if (node.type === "TOOL_USE" && node.toolUse) {
    const title = `Tool Use (${node.toolUse.toolName}, ${node.toolUse.toolUseId})`;

    return (
      <div key={`node-${nodeIndex}`} style={styles.nodeContainer}>
        <Collapse defaultActiveKey={[]} className="tool-result-collapse">
          <Collapse.Panel header={renderNodeHeader(title)} key="1">
            {renderJsonContent(node.toolUse.inputJson)}
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  } else if (node.type === "AGENT_MEMORY" && node.agentMemory) {
    return (
      <div key={`node-${nodeIndex}`} style={styles.nodeContainer}>
        <Collapse defaultActiveKey={[]} className="tool-result-collapse">
          <Collapse.Panel header={renderNodeHeader("Agent Memory")} key="1">
            {renderContent(node.agentMemory.content || "")}
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  } else if (
    [
      "SUGGESTED_QUESTIONS",
      "WORKSPACE_FILE_CHUNKS",
      "RELEVANT_SOURCES",
    ].includes(node.type)
  ) {
    const titleMap: Record<string, string> = {
      SUGGESTED_QUESTIONS: "Suggested Questions",
      WORKSPACE_FILE_CHUNKS: "Workspace File Chunks",
      RELEVANT_SOURCES: "Relevant Sources",
    };

    return (
      <div key={`node-${nodeIndex}`} style={styles.nodeContainer}>
        <Collapse defaultActiveKey={[]} className="tool-result-collapse">
          <Collapse.Panel
            header={renderNodeHeader(titleMap[node.type] || node.type)}
            key="1"
          >
            {renderContent(node.content || "")}
          </Collapse.Panel>
        </Collapse>
      </div>
    );
  }

  return (
    <div key={`node-${nodeIndex}`} style={styles.nodeContainer}>
      <Collapse defaultActiveKey={[]} className="tool-result-collapse">
        <Collapse.Panel
          header={renderNodeHeader(`Unknown node type: ${node.type}`)}
          key="1"
        />
      </Collapse>
    </div>
  );
}

/**
 * Renders all request nodes
 */
export function renderRequestNodes(
  requestNodes: RequestNode[] | undefined,
  tenantName: string,
) {
  if (!requestNodes || requestNodes.length === 0) return null;

  return (
    <>
      {requestNodes.map((node, nodeIndex) =>
        renderRequestNode(node, nodeIndex, tenantName),
      )}
    </>
  );
}

/**
 * Renders all response nodes
 */
export function renderResponseNodes(responseNodes: ResponseNode[] | undefined) {
  if (!responseNodes || responseNodes.length === 0) return null;

  return (
    <>
      {responseNodes.map((node, nodeIndex) =>
        renderResponseNode(node, nodeIndex),
      )}
    </>
  );
}

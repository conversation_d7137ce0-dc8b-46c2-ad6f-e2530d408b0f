import React from "react";
import { Typography } from "antd";
import { <PERSON> } from "react-router-dom";
import { renderRequestNode, renderResponseNode } from "./NodeRenderer";

const { Title } = Typography;

// Import types from requests lib
import {
  ChatHostExchange,
  RequestNode,
  ResponseNode,
} from "../../lib/requests";

interface ChatHistoryProps {
  chatHistory: ChatHostExchange[];
  tenantName: string;
}

function userMessage(item: ChatHostExchange, tenantName: string) {
  return (
    <>
      {item.requestId && (
        <div className="chat-history-request-id">
          <strong>Request ID: </strong>
          <Link to={`/t/${tenantName}/request/${item.requestId}`}>
            {item.requestId}
          </Link>
        </div>
      )}

      {item.requestNodes && item.requestNodes.length > 0 ? (
        <div className="request-nodes-container">
          {item.requestNodes.map((node: RequestNode, nodeIndex: number) => {
            return renderRequestNode(node, nodeIndex, tenantName);
          })}
        </div>
      ) : (
        <div className="prompt-text">{item.requestMessage}</div>
      )}
    </>
  );
}

function assistantMessage(item: ChatHostExchange) {
  return (
    <>
      {item.responseNodes && item.responseNodes.length > 0 ? (
        <div className="text-content" style={{ marginBottom: "16px" }}>
          {item.responseNodes
            .filter((node) => node.type !== "TOOL_USE")
            .map((node: ResponseNode, nodeIndex: number) =>
              renderResponseNode(node, nodeIndex),
            )}
          {item.responseNodes
            .filter((node) => node.type === "TOOL_USE")
            .map((node: ResponseNode, nodeIndex: number) =>
              renderResponseNode(node, nodeIndex),
            )}
        </div>
      ) : (
        item.responseText && (
          <div className="prompt-text text-content">{item.responseText}</div>
        )
      )}
    </>
  );
}

export const ChatHistoryComponent: React.FC<ChatHistoryProps> = ({
  chatHistory,
  tenantName,
}) => {
  if (!chatHistory || chatHistory.length === 0) {
    return null;
  }

  return (
    <>
      <Title level={5}>Chat History</Title>
      <div className="chat-history-list">
        {chatHistory.map((item: ChatHostExchange, index: number) => {
          return (
            <div key={item.requestId || index} className="chat-history-item">
              <div className={`chat-history-header user-header`}>
                <Typography.Title level={5}>User</Typography.Title>
                <Typography.Text type="secondary">#{index}</Typography.Text>
              </div>

              <div className={`chat-history-content user-content`}>
                {userMessage(item, tenantName)}
              </div>

              <div className={`chat-history-header assistant-header`}>
                <Typography.Title level={5}>Assistant</Typography.Title>
                <Typography.Text type="secondary">#{index}</Typography.Text>
              </div>

              <div className={`chat-history-content assistant-content`}>
                {assistantMessage(item)}
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

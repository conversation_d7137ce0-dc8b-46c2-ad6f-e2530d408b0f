import { Table } from "antd";
import { RequestData, WorkingDirectoryChange } from "../../lib/requests";
import { WarningComponent } from "./Warning";
import { BlobLink } from "../../lib/blob_components";

function WorkingDirectory2Html(
  workingDirectory: WorkingDirectoryChange[],
  tenantName: string,
) {
  const dataSource = workingDirectory.map((d) => ({
    changeType: d.changeType,
    headFile: (
      <BlobLink
        blobName={d.headBlobName}
        filePath={d.beforePath}
        tenantName={tenantName}
      />
    ),
    indexedFile: (
      <BlobLink
        blobName={d.indexedBlobName}
        filePath={d.afterPath}
        tenantName={tenantName}
      />
    ),
    currentFile: (
      <BlobLink
        blobName={d.currentBlobName}
        filePath={d.afterPath}
        tenantName={tenantName}
      />
    ),
  }));

  return (
    <Table
      dataSource={dataSource}
      columns={[
        {
          title: "Change Type",
          dataIndex: "changeType",
          key: "changeType",
        },
        {
          title: "Head File",
          dataIndex: "headFile",
          key: "headFile",
        },
        {
          title: "Indexed File",
          dataIndex: "indexedFile",
          key: "indexedFile",
        },
        {
          title: "Current File",
          dataIndex: "currentFile",
          key: "currentFile",
        },
      ]}
    />
  );
}

export function WorkingDirectoryComponent({
  requestData,
  tenantName,
}: {
  requestData: RequestData;
  tenantName: string;
}) {
  if (
    requestData.nextEditHostRequest?.request?.vcsChange
      ?.workingDirectoryChanges !== undefined &&
    requestData.nextEditHostRequest.request.vcsChange.workingDirectoryChanges
      .length > 0
  ) {
    const workingDirectory =
      requestData.nextEditHostRequest.request.vcsChange.workingDirectoryChanges;
    return WorkingDirectory2Html(workingDirectory, tenantName);
  } else {
    return <WarningComponent />;
  }
}

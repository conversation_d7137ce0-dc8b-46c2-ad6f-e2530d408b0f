import { Badge, Descriptions, Typography } from "antd";
import { CompletionHostRequest, RequestData } from "../../lib/requests";
import { Link } from "react-router-dom";
import { BlobLink } from "../../lib/blob_components";
import { getTokens, showTimeStamp } from "../../lib/requests";

const { Text } = Typography;

function StatusComponent({ requestData }: { requestData: RequestData }) {
  let status: "success" | "error" | "processing" | "default" = "success";
  let statusText = "Success";
  const r = requestData;
  if (r.apiHttpResponse?.code === 499) {
    statusText = "Cancelled";
  } else if (
    r.apiHttpResponse !== undefined &&
    r.apiHttpResponse?.code !== 200
  ) {
    status = "error";
    statusText = `Request Failed: ${r.apiHttpResponse!.code}`;
  } else if (
    r.inferRequest === undefined &&
    r.editHostResponse === undefined &&
    r.instructionHostResponse === undefined &&
    r.chatHostResponse === undefined &&
    r.completionHostResponse === undefined &&
    r.nextEditHostResponse === undefined
  ) {
    status = "default";
    statusText = "N/A";
  } else if (r.inferRequest !== undefined && r.apiHttpResponse === undefined) {
    status = "processing";
    statusText = "Processing";
  }
  return <Badge status={status} text={statusText} />;
}

function getCapabilityNames(value?: CompletionHostRequest) {
  const names: string[] = [];
  if (value === undefined) {
    return names;
  }
  if (value.enableFillInTheMiddle) {
    names.push("Fill-in-the-Middle");
  }
  if (value.enablePathPrefix) {
    names.push("Path Prefixing");
  }
  if (value.enablePreferenceTokens) {
    names.push("Preference Tokens");
  }
  if (value.enableMemories) {
    names.push("Memories");
  }
  if (value["enableBm25"]) {
    // only on CompletionHostRequest
    names.push("BM25 Retrieval");
  }
  if (value["enableDenseRetrieval"]) {
    // only on CompletionHostRequest
    names.push("Dense Retrieval");
  }
  return names;
}

function CapabilitiesComponent({
  capabilityNames,
}: {
  capabilityNames: string[];
}) {
  const children = capabilityNames.map((name, index) => {
    return (
      <Text keyboard key={`capability_${index}_${name}`}>
        {name}
      </Text>
    );
  });
  return <div>{children}</div>;
}

export function RequestInfoComponent({
  requestData,
  suggestionId,
  startTime,
  endTime,
  title,
  tenantName,
  requestId,
}: {
  requestData: RequestData;
  suggestionId?: string | undefined;
  startTime?: Date | undefined;
  endTime?: Date | undefined;
  title: string;
  tenantName: string;
  requestId: string;
}) {
  const r = requestData;
  // convert startTime to California time
  const requestTimeStr = showTimeStamp(startTime);

  const duration =
    startTime && endTime ? endTime.getTime() - startTime.getTime() : "N/A";
  const descs = [
    <Descriptions.Item label="Request Id" span={1} key="1">
      <Link to={`/t/${tenantName}/request/${requestId}`}>{requestId}</Link>
    </Descriptions.Item>,
    <Descriptions.Item label="Tenant" span={1} key="2">
      {tenantName}
    </Descriptions.Item>,
    <Descriptions.Item label="User" span={1} key="3">
      {r.requestMetadata?.userId || r.inferRequest?.userId || "N/A"}
    </Descriptions.Item>,
    <Descriptions.Item label="Session Id" span={1} key="4">
      {r.requestMetadata?.sessionId || r.inferRequest?.sessionId || "N/A"}
    </Descriptions.Item>,
    <Descriptions.Item label="Status" key="5">
      <StatusComponent requestData={r} />
    </Descriptions.Item>,
    <Descriptions.Item label="Timestamp (Pacific Time)" span={1} key="6">
      {requestTimeStr}
    </Descriptions.Item>,
    <Descriptions.Item label="Duration (ms)" span={1} key="7">
      {duration}
    </Descriptions.Item>,
    <Descriptions.Item label="Request Type" span={1} key="8">
      {r.requestMetadata?.requestType}
    </Descriptions.Item>,
    <Descriptions.Item label="User Agent" span={1} key="9">
      {r.requestMetadata?.userAgent}
    </Descriptions.Item>,
  ];

  if (r.completionHostRequest?.model !== r.inferRequest?.model) {
    descs.push(
      <Descriptions.Item label="Model" key="10">
        {r.completionHostRequest?.model || "N/A"} /{" "}
        {r.inferRequest?.model || "N/A"}
      </Descriptions.Item>,
    );
  } else {
    descs.push(
      <Descriptions.Item label="Model" key="11">
        {r.completionHostRequest?.model ||
          r.editHostRequest?.request.modelName ||
          r.instructionHostRequest?.request.modelName ||
          r.chatHostRequest?.request.modelName ||
          r.nextEditHostRequest?.request.modelName ||
          "N/A"}
      </Descriptions.Item>,
    );
  }
  const getNextEditRequestTokenization = (r: RequestData) => {
    // find selected suggestion
    const suggestion = r.nextEditHostResponse?.suggestions.find(
      (s) => s.result.suggestedEdit.suggestionId === suggestionId,
    );
    // find generation
    const generation = r.nextEditHostResponse?.generation.find(
      (g) => g.generationId === suggestion?.generationId,
    );

    return generation?.generationPrompt;
  };

  const getRequestTokenLength = () => {
    const result =
      getTokens(
        r.completionHostRequest?.tokenization ||
          r.instructionHostRequest?.tokenization ||
          r.editHostRequest?.tokenization ||
          r.chatHostRequest?.tokenization ||
          getNextEditRequestTokenization(r),
      )?.length || 0;
    return result;
  };
  descs.push(
    <Descriptions.Item label="Prompt Tokens" key="12">
      {getRequestTokenLength()}
    </Descriptions.Item>,
  );

  const getNextEditOutputTokenization = (r: RequestData) => {
    // find selected suggestion
    const suggestion = r.nextEditHostResponse?.suggestions.find(
      (s) => s.result.suggestedEdit.suggestionId === suggestionId,
    );
    // find generation
    const generation = r.nextEditHostResponse?.generation.find(
      (g) => g.generationId === suggestion?.generationId,
    );
    return generation?.generationOutput;
  };

  const responseTokens = getTokens(
    r.completionHostResponse?.tokenization ||
      r.instructionHostResponse?.tokenization ||
      r.editHostResponse?.tokenization ||
      r.chatHostResponse?.tokenization ||
      getNextEditOutputTokenization(r),
  );
  const responseTokenLength = responseTokens.length;
  descs.push(
    <Descriptions.Item label="Output Tokens" key="13">
      {responseTokenLength || "N/A"}
    </Descriptions.Item>,
  );

  // Only available on completion requests
  if (r.inferRequest) {
    // completion
    descs.push(
      <Descriptions.Item label="Max Output Tokens" key="14">
        {r.completionHostRequest?.outputLen || "N/A"}
      </Descriptions.Item>,
    );
  }

  const capabilityNames = getCapabilityNames(r.completionHostRequest);
  if (capabilityNames.length > 0) {
    // only show the capabilities row if we have them
    descs.push(
      <Descriptions.Item label="Capabilities" span={3}>
        <CapabilitiesComponent capabilityNames={capabilityNames} />
      </Descriptions.Item>,
    );
  }
  if (r.inferRequest) {
    // completion
    descs.push(
      <Descriptions.Item
        label="Filter Score (high score = low quality)"
        key="15"
      >
        {r.completionPostProcess?.filterScore || "N/A"}
      </Descriptions.Item>,
    );
    descs.push(
      <Descriptions.Item label="Applied Filter Threshold" key="16">
        {r.completionPostProcess?.appliedFilterThreshold || "N/A"}
      </Descriptions.Item>,
    );
    descs.push(
      <Descriptions.Item label="Filter Reason" key="17">
        {r.completionPostProcess?.filterReason || "N/A"}
      </Descriptions.Item>,
    );
  }

  if (r.nextEditHostRequest) {
    descs.push(
      <Descriptions.Item label="Request Scope/Mode" span={1} key="10">
        {r.nextEditHostRequest?.request?.scope || "N/A"}/
        {r.nextEditHostRequest?.request?.mode || "N/A"}
      </Descriptions.Item>,
    );
  }

  const position =
    r.completionHostRequest?.position ||
    r.editHostRequest?.request.position ||
    r.instructionHostRequest?.request.position ||
    r.chatHostRequest?.request.position;
  if (position) {
    const cursorPosition =
      "cursorPosition" in position && position.cursorPosition
        ? position.cursorPosition
        : 0;
    descs.push(
      <Descriptions.Item label="Current File" key="18">
        <BlobLink blobName={position.blobName} tenantName={tenantName} />
      </Descriptions.Item>,
    );
    descs.push(
      <Descriptions.Item label="Prefix Begin" key="19">
        {position.prefixBegin || 0}
      </Descriptions.Item>,
    );
    descs.push(
      <Descriptions.Item label="Cursor" key="20">
        {cursorPosition}
      </Descriptions.Item>,
    );
    descs.push(
      <Descriptions.Item label="Suffix End" key="21">
        {position.suffixEnd || 0}
      </Descriptions.Item>,
    );
  }

  return (
    <Descriptions title={title} bordered column={3}>
      {descs}
    </Descriptions>
  );
}

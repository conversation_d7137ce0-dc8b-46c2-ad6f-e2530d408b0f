import { Tabs, Descriptions, Divider } from "antd";
import { EmbeddingsSearchResult, RetrievalChunk } from "../../lib/requests";
import { BlobLink } from "../../lib/blob_components";
import { RetrievalChunkComponent } from "./RetrievalChunkComponent";

function LookUpScore(
  retrievedChunk: RetrievalChunk,
  embeddingSearchResults?: EmbeddingsSearchResult[],
) {
  if (retrievedChunk.score !== undefined) {
    return retrievedChunk.score;
  }
  if (embeddingSearchResults !== undefined) {
    console.log(`${retrievedChunk.blobName} and ${retrievedChunk.chunkIndex}`);
    const found = embeddingSearchResults.find(
      (obj) =>
        obj.blobName == retrievedChunk.blobName &&
        obj.chunkIndex == retrievedChunk.chunkIndex,
    );
    if (found) {
      return found.value;
    }
  }
}

export function ReceivedChunks2Html(
  retrievedChunks: RetrievalChunk[],
  tenantName: string,
  embeddingSearchResults?: EmbeddingsSearchResult[],
) {
  // Group chunks by origin.
  const chunksByOrigin = retrievedChunks.reduce(
    (acc, chunk) => {
      if (!acc[chunk.origin]) {
        acc[chunk.origin] = [];
      }
      acc[chunk.origin].push(chunk);
      return acc;
    },
    {} as Record<string, RetrievalChunk[]>,
  );

  const items = Object.entries(chunksByOrigin).map(([origin, chunks]) => ({
    key: origin,
    label: origin,
    children: (
      <>
        {chunks.map((d, idx) => (
          <div key={`${origin}-${idx}`}>
            <Divider />
            <Descriptions bordered column={4}>
              <Descriptions.Item label="File">
                <BlobLink
                  blobName={d.blobName}
                  filePath={d.path}
                  tenantName={tenantName}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Char Range">
                {d.charOffset} ~ {d.charEnd}
              </Descriptions.Item>
              <Descriptions.Item label="Origin">
                {d.origin}, chunk-{d.chunkIndex}
              </Descriptions.Item>
              <Descriptions.Item label="Score/Rank">
                {LookUpScore(d, embeddingSearchResults) || "N/A"} / {idx}
              </Descriptions.Item>
            </Descriptions>
            <RetrievalChunkComponent doc={d} />
          </div>
        ))}
      </>
    ),
  }));

  return <Tabs items={items} />;
}

import { useNavigate, useParams } from "react-router-dom";
import { LayoutComponent } from "../lib/layout";
import { Alert } from "antd";
import { getTenantIdForName, useTenantInfo } from "../contexts/tenant_provider";
import React, { ReactElement } from "react";
import { getLastSelectedTenant } from "../lib/tenant-storage";

type Props = {
  tenantName?: string;
  pageSuffix: string;
  setTenantInfo: (name: string, id: string) => ReactElement;
};

export function TenantInfoComponent({
  tenantName,
  pageSuffix,
  setTenantInfo,
}: Props) {
  const navigate = useNavigate();
  const tenantInfo = useTenantInfo();

  if (tenantName === undefined) {
    // First check if we have a previously selected tenant
    const lastSelectedTenant = getLastSelectedTenant();
    if (lastSelectedTenant) {
      console.log(`redirecting to last selected tenant ${lastSelectedTenant}`);
      navigate(`/t/${lastSelectedTenant}/${pageSuffix}`);
      return <></>;
    }

    if (tenantInfo.tenants?.length === 1) {
      console.log(
        `only one tenant, redirecting to ${tenantInfo.tenants[0].name}`,
      );
      // redirect to the only tenant
      navigate(`/t/${tenantInfo.tenants[0].name}/${pageSuffix}`);
      return <></>;
    }
    const defaultTenant = tenantInfo.tenants?.find(
      (t) => t.config.configs?.default_support_tenant === "true",
    );
    if (defaultTenant !== undefined) {
      console.log(`redirect to default support tenant "${defaultTenant.name}"`);
      // redirect to the augment tenant
      navigate(`/t/${defaultTenant.name}/${pageSuffix}`);
      return <></>;
    }

    return (
      <LayoutComponent showTenantDropdown={true} pageSuffix={pageSuffix}>
        <Alert message="Select a tenant in the top right corner" type="info" />
      </LayoutComponent>
    );
  }
  const tenantId = getTenantIdForName(tenantName, tenantInfo);
  if (tenantId === undefined) {
    return (
      <LayoutComponent showTenantDropdown={true} pageSuffix={pageSuffix}>
        <Alert message="Invalid tenant" type="info" />
      </LayoutComponent>
    );
  } else {
    return setTenantInfo(tenantName, tenantId);
  }
}

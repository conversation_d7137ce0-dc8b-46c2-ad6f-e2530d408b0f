import { Button, Flex, Form, Select } from "antd";
import { type UserDetails } from "../lib/users-api";

type Props = {
  userID: string;
  userDetails: UserDetails;
  onSubmit: (id: string, details: UserDetails) => void;
  onCancel: () => void;
};

export function UpdateUser({ userID, userDetails, onSubmit, onCancel }: Props) {
  const [userUpdateForm] = Form.useForm();

  const form = (
    <>
      <p>Use the form below to update user details for the tenant</p>

      <Form
        name="user_update"
        form={userUpdateForm}
        initialValues={{ customerUiRoles: userDetails.customerUiRoles }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        autoComplete="off"
      >
        <Form.Item label="Role" name="customerUiRoles" displayEmpty>
          <Select mode="tags">
            <Select.Option></Select.Option>
            <Select.Option value="ADMIN">Admin</Select.Option>
          </Select>
        </Form.Item>
      </Form>

      <Flex gap="middle">
        <Button onClick={onCancel}>Cancel</Button>
        <Button
          type="primary"
          onClick={() => onSubmit(userID, userUpdateForm.getFieldsValue())}
        >
          Update User
        </Button>
      </Flex>
    </>
  );

  return form;
}

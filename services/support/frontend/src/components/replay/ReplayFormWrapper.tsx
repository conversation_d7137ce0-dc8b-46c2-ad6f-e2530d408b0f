import React, { useMemo, useState, useEffect } from "react";
import { Form, Button, AutoComplete, message } from "antd";
import { ModelData, getModels } from "../../lib/requests";

export function ReplayFormWrapper<T>({
  fields,
  initialValues,
  onFinish,
}: {
  fields: React.ReactNode;
  initialValues: T;
  onFinish: (values: T) => void;
}): React.ReactElement {
  const [replayForm] = Form.useForm();
  const [messageApi, contextHolder] = message.useMessage();
  const [models, setModelData] = useState<ModelData[]>([]);

  useEffect(() => {
    const fetchModels = async () => {
      try {
        const models = await getModels();
        setModelData(models);
      } catch (e) {
        messageApi.open({
          type: "error",
          content: JSON.stringify(e),
        });
      }
    };
    fetchModels();
  }, []);

  const modelOptions = useMemo(
    () => models.map((m) => ({ value: m.name })),
    [models],
  );

  return (
    <>
      {contextHolder}
      <Form
        name="request_id"
        form={replayForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 800 }}
        size={"small"}
        initialValues={initialValues}
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>

        <Form.Item
          label="Model"
          name="model"
          rules={[{ required: true, message: "Please input the model name." }]}
        >
          <AutoComplete options={modelOptions} />
        </Form.Item>

        {fields}

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </>
  );
}

import React, { useState } from "react";
import { Row, Col, Typography, Divider } from "antd";
import { ChatHostRequest } from "../../lib/requests";
import { RequestComponent } from "../../routes/request";
import { ChatReplayForm } from "./ChatReplayForm";

const { Text } = Typography;

export function ChatReplay({
  chatHostRequest,
  tenantId,
  tenantName,
}: {
  chatHostRequest: ChatHostRequest;
  tenantId: string;
  tenantName: string;
}) {
  const [replayRequestId, setReplayRequestId] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <Text strong>Chat Replay</Text>
      <Divider />
      <Row>
        <Col span={8}>
          <ChatReplayForm
            chatHostRequest={chatHostRequest}
            tenantId={tenantId}
            setReplayRequestId={setReplayRequestId}
          />
        </Col>
        <Col span={14}>
          {replayRequestId && ( // display the replayed request when an id is present
            <RequestComponent
              tenantId={tenantId}
              tenantName={tenantName}
              requestId={replayRequestId}
              title=""
              desiredEvents={["chatHostRequest", "chatHostResponse"]}
            />
          )}
        </Col>
      </Row>
    </>
  );
}

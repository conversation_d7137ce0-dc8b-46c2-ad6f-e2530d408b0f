import React, { useState } from "react";
import { Form, Input, InputNumber, message, Switch } from "antd";
import TextArea from "antd/es/input/TextArea";
import {
  CompletionHostRequest,
  requestCompletion,
  mergeBlobs,
} from "../../lib/requests";
import { ReplayFormWrapper } from "./ReplayFormWrapper";

type CompletionForm = {
  model: string;
  prefix: string;
  suffix: string;
  path: string;
  // begin CompletionPosition
  blob_name?: string;
  prefix_begin?: number;
  suffix_end?: number;
  // end CompletionPosition
  blobs_baseline_checkpoint_id?: string;
  blobs_added?: string;
  blobs_deleted?: string;
  lang?: string;
  max_tokens?: number;
  seed?: number;
  recency_info?: string;
  edit_events?: string;
};

function camelToSnake(obj: unknown): unknown {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(camelToSnake);
  }

  return Object.keys(obj).reduce((acc: object, key: string) => {
    const snakeKey = key.replace(
      /[A-Z]/g,
      (match) => `_${match.toLowerCase()}`,
    );
    acc[snakeKey] = camelToSnake(obj[key]);
    return acc;
  }, {} as object);
}

export function CompletionReplayForm({
  completionHostRequest,
  tenantId,
  setReplayRequestId,
}: {
  completionHostRequest: CompletionHostRequest;
  tenantId: string;
  setReplayRequestId: (requestId: string) => void;
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [blobsHidden, setBlobsHidden] = useState<boolean>(false);

  const handleOnFinish = (values: CompletionForm) => {
    const blobs_added = (values.blobs_added || "")
      .split("\n")
      .filter((s) => s.length > 0);
    const blobs_deleted = (values.blobs_deleted || "")
      .split("\n")
      .filter((s) => s.length > 0);

    const recency_info = Object.assign(
      {
        recent_changes: [],
        git_diff_file_info: [],
        tab_switch_events: [],
      },
      JSON.parse(values.recency_info ?? "{}"),
    );

    const edit_events = JSON.parse(values.edit_events ?? "[]");

    const hasPosition =
      values.blob_name !== undefined &&
      values.prefix_begin !== undefined &&
      values.suffix_end !== undefined;
    const positionData = hasPosition
      ? {
          blob_name: values.blob_name,
          prefix_begin: values.prefix_begin,
          // we compute the cursor_position from prefix_begin and the prefix length
          cursor_position: values.prefix_begin! + values.prefix.length,
          suffix_end: values.suffix_end,
        }
      : undefined;

    // Only send blobs if we have a checkpoint or added blobs
    const blobs = {
      baseline_checkpoint_id: values.blobs_baseline_checkpoint_id,
      added: blobs_added,
      deleted: blobs_deleted,
    };

    const request = {
      model_name: values.model,
      prefix: values.prefix,
      suffix: values.suffix,
      path: values.path,
      blobs: blobs,
      lang: values.lang,
      max_tokens: values.max_tokens,
      seed: values.seed,
      position: positionData,
      recency_info: recency_info,
      edit_events: edit_events,
    };
    requestCompletion(tenantId, request)
      .then((requestId) => {
        setReplayRequestId(requestId);
      })
      .catch((reason) => {
        messageApi.open({
          type: "error",
          content: JSON.stringify(reason),
        });
      });
  };

  // Continue to support old requests that used blob_names, even though we
  // always send blobs to the backend
  const blobsAdded = mergeBlobs(
    completionHostRequest.blobs?.added,
    completionHostRequest.blobNames,
  )
    .sort()
    .join("\n");
  const blobsDeleted = (completionHostRequest.blobs?.deleted || [])
    .sort()
    .join("\n");
  const recencyInfo = {
    recent_changes:
      camelToSnake(completionHostRequest.recencyInfo?.recentChanges) ?? [],
    tab_switch_events:
      camelToSnake(completionHostRequest.recencyInfo?.tabSwitchEvents) ?? [],
    git_diff_file_info:
      camelToSnake(completionHostRequest.recencyInfo?.gitDiffFileInfo) ?? [],
  };
  const editEvents = camelToSnake(completionHostRequest.editEvents);

  return (
    <>
      {contextHolder}
      <ReplayFormWrapper
        initialValues={{
          model: completionHostRequest.model ?? "",
          prefix: completionHostRequest.prefix ?? "",
          suffix: completionHostRequest.suffix ?? "",
          path: completionHostRequest.path ?? "",
          lang: completionHostRequest.lang,
          blob_name: completionHostRequest.position?.blobName,
          prefix_begin: completionHostRequest.position?.prefixBegin,
          suffix_end: completionHostRequest.position?.suffixEnd,
          blobs_baseline_checkpoint_id:
            completionHostRequest.blobs?.baselineCheckpointId,
          blobs_added: blobsAdded,
          blobs_deleted: blobsDeleted,
          max_tokens: completionHostRequest.outputLen,
          seed: completionHostRequest.seed,
          recency_info: JSON.stringify(recencyInfo, null, 2),
          edit_events: JSON.stringify(editEvents, null, 2),
        }}
        onFinish={handleOnFinish}
        fields={
          <>
            <Form.Item
              label="Prefix"
              name="prefix"
              rules={[{ message: "Please input the prefix." }]}
            >
              <TextArea
                rows={12}
                placeholder="Prefix"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item label="Suffix" name="suffix">
              <TextArea
                rows={12}
                placeholder="Suffix"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item
              label="Path"
              name="path"
              rules={[{ required: true, message: "Please input the path." }]}
            >
              <Input placeholder="Path" />
            </Form.Item>

            <Form.Item label="Language" name="lang">
              <Input placeholder="Language" />
            </Form.Item>

            <Form.Item label="Current File Blob" name="blob_name">
              <Input placeholder="Blob Name" />
            </Form.Item>

            <Form.Item label="Prefix Begin" name="prefix_begin">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Suffix End" name="suffix_end">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Max Tokens" name="max_tokens">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Seed" name="seed">
              <InputNumber<number> min={0} maxLength={20} />
            </Form.Item>

            <Form.Item label="Blobs Hidden">
              <Switch
                onChange={(v) => {
                  setBlobsHidden(v);
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Baseline Checkpoint ID"
              name="blobs_baseline_checkpoint_id"
              hidden={blobsHidden}
            >
              <Input placeholder="Baseline Checkpoint ID" />
            </Form.Item>

            <Form.Item
              label="Blobs Added"
              name="blobs_added"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Added blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Deleted"
              name="blobs_deleted"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Deleted blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Edit Events"
              name="edit_events"
              rules={[{ message: "Please input a valid edit events JSON." }]}
            >
              <TextArea
                rows={12}
                placeholder="Edit Events JSON"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item
              label="Recency"
              name="recency_info"
              rules={[{ message: "Please input a valid recency JSON." }]}
            >
              <TextArea
                rows={12}
                placeholder="Recency JSON"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
}

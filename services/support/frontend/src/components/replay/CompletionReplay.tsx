import React, { useState } from "react";
import { Row, Col, Typo<PERSON>, Divider } from "antd";
import { CompletionHostRequest } from "../../lib/requests";
import { RequestComponent } from "../../routes/request";
import { CompletionReplayForm } from "./CompletionReplayForm";

const { Text } = Typography;

export function CompletionReplay({
  completionHostRequest,
  tenantId,
  tenantName,
}: {
  completionHostRequest: CompletionHostRequest;
  tenantId: string;
  tenantName: string;
}) {
  const [replayRequestId, setReplayRequestId] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <Text strong>Completion Replay</Text>
      <Divider />
      <Row>
        <Col span={8}>
          <CompletionReplayForm
            completionHostRequest={completionHostRequest}
            tenantId={tenantId}
            setReplayRequestId={setReplayRequestId}
          />
        </Col>
        <Col span={14}>
          {replayRequestId && ( // display the replayed request when an id is present
            <RequestComponent
              tenantId={tenantId}
              tenantName={tenantName}
              requestId={replayRequestId}
              title=""
              desiredEvents={[
                "inferenceHostResponse",
                "completionHostRequest",
                "completionHostResponse",
              ]}
            />
          )}
        </Col>
      </Row>
    </>
  );
}

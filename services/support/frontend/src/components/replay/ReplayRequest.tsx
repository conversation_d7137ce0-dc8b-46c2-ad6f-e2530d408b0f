import React, { useEffect, useState } from "react";
import { Spin, message, Typography } from "antd";
import { RequestData, getRequest } from "../../lib/requests";
import { ChatReplay } from "./ChatReplay";
import { CompletionReplay } from "./CompletionReplay";
import { EditReplay } from "./EditReplay";
import { InstructionReplay } from "./InstructionReplay";
import axios from "axios";
import { NextEditReplay } from "./NextEditReplay";

const { Text } = Typography;

export function ReplayRequest({
  requestId,
  tenantId,
  tenantName,
}: {
  requestId: string;
  tenantId: string;
  tenantName: string;
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [requestData, setRequestData] = useState<RequestData | undefined>(
    undefined,
  );

  useEffect(() => {
    const fetchReplayRequest = async () => {
      console.log(`replay request id ${requestId}`);
      try {
        const request = await getRequest(tenantId, requestId, []);
        console.log(`replay request ${JSON.stringify(request)}`);
        setRequestData(request);
      } catch (e) {
        if (axios.isAxiosError(e) && e.response?.status === 403) {
          messageApi.open({
            type: "error",
            content: "You do not have access to replay requests",
          });
        } else {
          messageApi.open({
            type: "error",
            content: JSON.stringify(e),
          });
        }
      }
    };
    fetchReplayRequest();
  }, [requestId]);

  let component = <Spin />; // show loading spinner by default

  if (requestData) {
    if (requestData.completionHostRequest !== undefined) {
      component = (
        <CompletionReplay
          completionHostRequest={requestData.completionHostRequest}
          tenantId={tenantId}
          tenantName={tenantName}
        />
      );
    } else if (requestData.editHostRequest !== undefined) {
      component = (
        <EditReplay
          editHostRequest={requestData.editHostRequest}
          tenantId={tenantId}
          tenantName={tenantName}
        />
      );
    } else if (requestData.instructionHostRequest !== undefined) {
      component = (
        <InstructionReplay
          instructionHostRequest={requestData.instructionHostRequest}
          tenantId={tenantId}
          tenantName={tenantName}
        />
      );
    } else if (requestData.chatHostRequest !== undefined) {
      component = (
        <ChatReplay
          chatHostRequest={requestData.chatHostRequest}
          tenantId={tenantId}
          tenantName={tenantName}
        />
      );
    } else if (requestData.nextEditHostRequest !== undefined) {
      component = (
        <NextEditReplay
          nextEditHostRequest={requestData.nextEditHostRequest}
          tenantId={tenantId}
          tenantName={tenantName}
        />
      );
    } else {
      component = <Text>This request is not replayable.</Text>;
    }
  }

  return (
    <>
      {contextHolder}
      {component}
    </>
  );
}

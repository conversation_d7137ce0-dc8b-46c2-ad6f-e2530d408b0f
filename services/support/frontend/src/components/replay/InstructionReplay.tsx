import React, { useState } from "react";
import { Row, Col, Typo<PERSON>, Divider } from "antd";
import { InstructionHostRequest } from "../../lib/requests";
import { RequestComponent } from "../../routes/request";
import { InstructionReplayForm } from "./InstructionReplayForm";

const { Text } = Typography;

export function InstructionReplay({
  instructionHostRequest,
  tenantId,
  tenantName,
}: {
  instructionHostRequest: InstructionHostRequest;
  tenantId: string;
  tenantName: string;
}) {
  const [replayRequestId, setReplayRequestId] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <Text strong>Instruction Replay</Text>
      <Divider />
      <Row>
        <Col span={8}>
          <InstructionReplayForm
            instructionHostRequest={instructionHostRequest}
            tenantId={tenantId}
            setReplayRequestId={setReplayRequestId}
          />
        </Col>
        <Col span={14}>
          {replayRequestId && ( // display the replayed request when an id is present
            <RequestComponent
              tenantId={tenantId}
              tenantName={tenantName}
              requestId={replayRequestId}
              title=""
              desiredEvents={[
                "instructionHostRequest",
                "instructionHostResponse",
              ]}
            />
          )}
        </Col>
      </Row>
    </>
  );
}

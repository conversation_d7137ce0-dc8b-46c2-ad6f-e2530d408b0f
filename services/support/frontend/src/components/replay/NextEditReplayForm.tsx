import React, { useState } from "react";
import { Form, Input, InputNumber, message } from "antd";
import TextArea from "antd/es/input/TextArea";
import { NextEditHostRequest, requestNextEdit } from "../../lib/requests";
import { ReplayFormWrapper } from "./ReplayFormWrapper";

type NextEditForm = {
  model: string;
  instruction: string;
  prefix: string;
  suffix: string;
  path: string;
  blob_name?: string;
  blobs_baseline_checkpoint_id?: string;
  blobs_added?: string;
  blobs_deleted?: string;
  lang?: string;
  recency_info?: string;
  selection_begin_char?: number;
  selection_end_char?: number;
  selected_text?: string;
  mode?: string;
  scope?: string;
  change_probability_override?: number;
  vcs_change?: string;
  edit_events?: string;
  diagnostics?: string;
};

function camelToSnake(obj: unknown): unknown {
  if (typeof obj !== "object" || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(camelToSnake);
  }

  return Object.keys(obj).reduce((acc: object, key: string) => {
    const snakeKey = key.replace(
      /[A-Z]/g,
      (match) => `_${match.toLowerCase()}`,
    );
    acc[snakeKey] = camelToSnake(obj[key]);
    return acc;
  }, {} as object);
}

export function NextEditReplayForm({
  nextEditHostRequest,
  tenantId,
  setReplayRequestId,
}: {
  nextEditHostRequest: NextEditHostRequest;
  tenantId: string;
  setReplayRequestId: (requestId: string) => void;
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [blobsHidden, setBlobsHidden] = useState<boolean>(false);

  const handleOnFinish = (values: NextEditForm) => {
    const blobs_added = (values.blobs_added || "")
      .split("\n")
      .filter((s) => s.length > 0);
    const blobs_deleted = (values.blobs_deleted || "")
      .split("\n")
      .filter((s) => s.length > 0);

    const recency_info = Object.assign(
      {
        recent_changes: [],
        git_diff_file_info: [],
        tab_switch_events: [],
      },
      JSON.parse(values.recency_info ?? "{}"),
    );
    const vcs_change = JSON.parse(values.vcs_change ?? "{}");
    const edit_events = JSON.parse(values.edit_events ?? "[]");
    const diagnostics = JSON.parse(values.diagnostics ?? "[]");

    // Only send blobs if we have a checkpoint or added blobs
    const blobs = {
      baseline_checkpoint_id: values.blobs_baseline_checkpoint_id,
      added: blobs_added,
      deleted: blobs_deleted,
    };

    const request = {
      model_name: values.model,
      instruction: values.instruction,
      lang: values.lang,
      blobs: blobs,
      recent_changes: recency_info.recent_changes,
      vcs_change: vcs_change,
      edit_events: edit_events,
      diagnostics: diagnostics,
      mode: values.mode,
      scope: values.scope,
      change_probability_override: values.change_probability_override,
      prefix: values.prefix,
      suffix: values.suffix,
      path: values.path,
      blob_name: values.blob_name,
      selection_begin_char: values.selection_begin_char!,
      selection_end_char: values.selection_end_char!,
      selected_text: values.selected_text!,
    };
    requestNextEdit(tenantId, request)
      .then((requestId) => {
        setReplayRequestId(requestId);
      })
      .catch((reason) => {
        messageApi.open({
          type: "error",
          content: JSON.stringify(reason),
        });
      });
  };

  const blobsAdded = (nextEditHostRequest.request?.blobs?.added || [])
    .sort()
    .join("\n");
  const blobsDeleted = (nextEditHostRequest.request?.blobs?.deleted || [])
    .sort()
    .join("\n");
  const recencyInfo = camelToSnake(nextEditHostRequest.request?.recentChanges);
  const vcsChange = camelToSnake(nextEditHostRequest.request?.vcsChange);
  const editEvents = camelToSnake(nextEditHostRequest.request?.editEvents);
  const diagnostics = camelToSnake(nextEditHostRequest.request?.diagnostics);

  return (
    <>
      {contextHolder}
      <ReplayFormWrapper
        initialValues={{
          model: nextEditHostRequest.request?.modelName ?? "",
          instruction: nextEditHostRequest.request?.instruction ?? "",
          prefix: nextEditHostRequest.request?.prefix ?? "",
          suffix: nextEditHostRequest.request?.suffix ?? "",
          path: nextEditHostRequest.request?.path ?? "",
          lang: nextEditHostRequest.request?.lang,
          blob_name: nextEditHostRequest.request?.blobName,
          blobs_baseline_checkpoint_id:
            nextEditHostRequest.request?.blobs?.baselineCheckpointId,
          blobs_added: blobsAdded,
          blobs_deleted: blobsDeleted,
          recency_info: JSON.stringify(recencyInfo, null, 2),
          vcs_change: JSON.stringify(vcsChange, null, 2),
          edit_events: JSON.stringify(editEvents, null, 2),
          diagnostics: JSON.stringify(diagnostics, null, 2),
          selection_begin_char: nextEditHostRequest.request?.selectionBeginChar,
          selection_end_char: nextEditHostRequest.request?.selectionEndChar,
          selected_text: nextEditHostRequest.request?.selectedText ?? "",
          mode: nextEditHostRequest.request?.mode,
          scope: nextEditHostRequest.request?.scope,
          change_probability_override:
            nextEditHostRequest.request?.changeProbabilityOverride,
        }}
        onFinish={handleOnFinish}
        fields={
          <>
            <Form.Item label="Instruction" name="instruction">
              <TextArea rows={4} placeholder="Instruction" />
            </Form.Item>

            <Form.Item label="Selection Begin Char" name="selection_begin_char">
              <InputNumber
                disabled
                style={{ color: "rgba(0, 0, 0, 0.25)" }}
                placeholder={
                  nextEditHostRequest.request?.selectionBeginChar?.toString() ||
                  "N/A"
                }
              />
            </Form.Item>

            <Form.Item label="Selection End Char" name="selection_end_char">
              <InputNumber
                disabled
                style={{ color: "rgba(0, 0, 0, 0.25)" }}
                placeholder={
                  nextEditHostRequest.request?.selectionEndChar?.toString() ||
                  "N/A"
                }
              />
            </Form.Item>

            <Form.Item label="Selected Text" name="selected_text">
              <TextArea
                rows={4}
                placeholder="Selected Text"
                disabled
                style={{ color: "rgba(0, 0, 0, 0.25)" }}
              />
            </Form.Item>

            <Form.Item label="Mode" name="mode">
              <Input placeholder="Mode" />
            </Form.Item>

            <Form.Item label="Scope" name="scope">
              <Input placeholder="Scope" />
            </Form.Item>

            <Form.Item
              label="Change Probability Override"
              name="change_probability_override"
            >
              <InputNumber step={0.1} min={0} max={1} />
            </Form.Item>

            <Form.Item
              label="Prefix"
              name="prefix"
              rules={[{ message: "Please input the prefix." }]}
            >
              <TextArea
                rows={12}
                placeholder="Prefix"
                disabled
                style={{ color: "rgba(0, 0, 0, 0.25)" }}
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item label="Suffix" name="suffix">
              <TextArea
                rows={12}
                placeholder="Suffix"
                disabled
                style={{ color: "rgba(0, 0, 0, 0.25)" }}
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item
              label="Path"
              name="path"
              rules={[{ required: true, message: "Please input the path." }]}
            >
              <Input placeholder="Path" />
            </Form.Item>

            <Form.Item label="Language" name="lang">
              <Input placeholder="Language" />
            </Form.Item>

            <Form.Item label="Current File Blob" name="blob_name">
              <Input placeholder="Blob Name" />
            </Form.Item>

            <Form.Item
              label="Blobs Baseline Checkpoint ID"
              name="blobs_baseline_checkpoint_id"
              hidden={blobsHidden}
            >
              <Input placeholder="Baseline Checkpoint ID" />
            </Form.Item>

            <Form.Item
              label="Blobs Added"
              name="blobs_added"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Added blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Deleted"
              name="blobs_deleted"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Deleted blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Diagnostics"
              name="diagnostics"
              rules={[{ message: "Please input a valid diagnostics JSON." }]}
            >
              <TextArea
                rows={12}
                placeholder="Diagnostics JSON"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item
              label="Edit Events"
              name="edit_events"
              rules={[{ message: "Please input a valid edit events JSON." }]}
            >
              <TextArea
                rows={12}
                placeholder="Edit Events JSON"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item
              label="VCS Change"
              name="vcs_change"
              rules={[{ message: "Please input a valid VCS Change JSON." }]}
            >
              <TextArea
                rows={12}
                placeholder="VCS Change JSON"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item
              label="Recency"
              name="recency_info"
              rules={[{ message: "Please input a valid recency JSON." }]}
            >
              <TextArea
                rows={12}
                placeholder="Recency JSON"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
}

import React, { useState } from "react";
import { Row, <PERSON>, Typo<PERSON>, Divider } from "antd";
import { EditHostRequest } from "../../lib/requests";
import { RequestComponent } from "../../routes/request";
import { EditReplayForm } from "./EditReplayForm";

const { Text } = Typography;

export function EditReplay({
  editHostRequest,
  tenantId,
  tenantName,
}: {
  editHostRequest: EditHostRequest;
  tenantId: string;
  tenantName: string;
}) {
  const [replayRequestId, setReplayRequestId] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <Text strong>Edit Replay</Text>
      <Divider />
      <Row>
        <Col span={8}>
          <EditReplayForm
            editHostRequest={editHostRequest}
            tenantId={tenantId}
            setReplayRequestId={setReplayRequestId}
          />
        </Col>
        <Col span={14}>
          {replayRequestId && ( // display the replayed request when an id is present
            <RequestComponent
              tenantId={tenantId}
              tenantName={tenantName}
              requestId={replayRequestId}
              title=""
              desiredEvents={["editHostRequest", "editHostResponse"]}
            />
          )}
        </Col>
      </Row>
    </>
  );
}

import React, { useState } from "react";
import { Row, <PERSON>, Typo<PERSON>, Divider } from "antd";
import { NextEditHostRequest } from "../../lib/requests";
import { RequestComponent } from "../../routes/request";
import { NextEditReplayForm } from "./NextEditReplayForm";

const { Text } = Typography;

export function NextEditReplay({
  nextEditHostRequest,
  tenantId,
  tenantName,
}: {
  nextEditHostRequest: NextEditHostRequest;
  tenantId: string;
  tenantName: string;
}) {
  const [replayRequestId, setReplayRequestId] = useState<string | undefined>(
    undefined,
  );

  return (
    <>
      <Text strong>Next Edit Replay</Text>
      <Divider />
      <Row>
        <Col span={8}>
          <NextEditReplayForm
            nextEditHostRequest={nextEditHostRequest}
            tenantId={tenantId}
            setReplayRequestId={setReplayRequestId}
          />
        </Col>
        <Col span={14}>
          {replayRequestId && ( // display the replayed request when an id is present
            <RequestComponent
              tenantId={tenantId}
              tenantName={tenantName}
              requestId={replayRequestId}
              title=""
              desiredEvents={["nextEditHostRequest", "nextEditHostResponse"]}
            />
          )}
        </Col>
      </Row>
    </>
  );
}

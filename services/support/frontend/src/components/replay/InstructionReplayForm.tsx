import React, { useState } from "react";
import {
  Form,
  Input,
  message,
  Switch,
  Input<PERSON><PERSON>ber,
  Divider,
  Button,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import { InstructionHostRequest, requestInstruction } from "../../lib/requests";
import { InstructionRequest } from "../../lib/requests";
import { ReplayFormWrapper } from "./ReplayFormWrapper";
import { CloseOutlined } from "@ant-design/icons";

type InstructionForm = {
  model: string;
  prefix: string;
  suffix: string;
  path: string;
  instruction: string;
  selectedText: string;
  lang: string;
  // begin InstructionPosition
  blob_name?: string;
  prefix_begin?: number;
  suffix_end?: number;
  // end InstructionPosition
  // begin blobs
  blobs_baseline_checkpoint_id?: string;
  blobs_added?: string;
  blobs_deleted?: string;
  // end blobs
  chat_history?: {
    request: string;
    response: string;
  }[];
  context_code_exchange_request_id?: string;
  code_block?: string;
  target_file_path?: string;
  target_file_content?: string;
};

export function InstructionReplayForm({
  instructionHostRequest,
  tenantId,
  setReplayRequestId,
}: {
  instructionHostRequest: InstructionHostRequest;
  tenantId: string;
  setReplayRequestId: (requestId: string) => void;
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [blobsHidden, setBlobsHidden] = useState<boolean>(false);

  const handleOnFinish = (values: InstructionForm) => {
    const blobs_added = (values.blobs_added || "")
      .split("\n")
      .filter((s) => s.length > 0);
    const blobs_deleted = (values.blobs_deleted || "")
      .split("\n")
      .filter((s) => s.length > 0);

    // Only send blobs if we have a checkpoint or added blobs
    const blobs = {
      baseline_checkpoint_id: values.blobs_baseline_checkpoint_id,
      added: blobs_added,
      deleted: blobs_deleted,
    };

    const hasPosition =
      values.blob_name !== undefined &&
      values.prefix_begin !== undefined &&
      values.suffix_end !== undefined;
    const positionData = hasPosition
      ? {
          blob_name: values.blob_name,
          prefix_begin: values.prefix_begin,
          suffix_end: values.suffix_end,
        }
      : undefined;
    const chatHistory = values.chat_history?.map((exchange) => ({
      requestMessage: exchange.request,
      responseText: exchange.response,
    }));
    const request: InstructionRequest = {
      model_name: values.model,
      instruction: values.instruction,
      prefix: values.prefix,
      selected_text: values.selectedText,
      suffix: values.suffix,
      path: values.path,
      lang: values.lang,
      position: positionData,
      blobs: blobs,
      chat_history: chatHistory,
      context_code_exchange_request_id: values.context_code_exchange_request_id,
      code_block: values.code_block,
      target_file_path: values.target_file_path,
      target_file_content: values.target_file_content,
    };
    requestInstruction(tenantId, request)
      .then((requestId) => {
        setReplayRequestId(requestId);
      })
      .catch((reason) => {
        messageApi.open({
          type: "error",
          content: JSON.stringify(reason),
        });
      });
  };

  const blobsAdded = (instructionHostRequest.request.blobs?.added || [])
    .sort()
    .join("\n");
  const blobsDeleted = (instructionHostRequest.request.blobs?.deleted || [])
    .sort()
    .join("\n");
  const baselineCheckpointId =
    instructionHostRequest.request.blobs?.baselineCheckpointId;

  const chatHistory = (instructionHostRequest.request.chatHistory || []).map(
    (exchange) => ({
      request: exchange.requestMessage,
      response: exchange.responseText,
    }),
  );

  return (
    <>
      {contextHolder}
      <ReplayFormWrapper
        initialValues={{
          model: instructionHostRequest.request.modelName ?? "",
          instruction: instructionHostRequest.request.instruction,
          prefix: instructionHostRequest.request.prefix ?? "",
          selectedText: instructionHostRequest.request.selectedText,
          suffix: instructionHostRequest.request.suffix ?? "",
          path: instructionHostRequest.request.path ?? "",
          lang: instructionHostRequest.request.lang ?? "",
          blob_name: instructionHostRequest.request.position?.blobName,
          prefix_begin: instructionHostRequest.request.position?.prefixBegin,
          suffix_end: instructionHostRequest.request.position?.suffixEnd,
          blobs_added: blobsAdded,
          blobs_deleted: blobsDeleted,
          blobs_baseline_checkpoint_id: baselineCheckpointId,
          chat_history: chatHistory,
          context_code_exchange_request_id:
            instructionHostRequest.request.contextCodeExchangeRequestId,
          code_block: instructionHostRequest.request.codeBlock,
          target_file_path: instructionHostRequest.request.targetFilePath,
          target_file_content: instructionHostRequest.request.targetFileContent,
        }}
        onFinish={handleOnFinish}
        fields={
          <>
            <Form.Item label="Instruction" name="instruction">
              <TextArea
                rows={3}
                placeholder="Instruction"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item
              label="Prefix"
              name="prefix"
              rules={[{ message: "Please input the prefix." }]}
            >
              <TextArea
                rows={12}
                placeholder="Prefix"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item label="Selected Text" name="selectedText">
              <TextArea
                rows={12}
                placeholder="Selected Text"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item label="Suffix" name="suffix">
              <TextArea
                rows={12}
                placeholder="Suffix"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item label="Path" name="path">
              <Input placeholder="Path" />
            </Form.Item>

            <Form.Item label="Language" name="lang">
              <Input placeholder="Language" />
            </Form.Item>

            <Form.Item label="Current File Blob" name="blob_name">
              <Input placeholder="Blob Name" />
            </Form.Item>

            <Form.Item label="Prefix Begin" name="prefix_begin">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Suffix End" name="suffix_end">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Blobs Hidden">
              <Switch
                onChange={(v) => {
                  setBlobsHidden(v);
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Baseline Checkpoint ID"
              name="blobs_baseline_checkpoint_id"
              hidden={blobsHidden}
            >
              <Input placeholder="Baseline Checkpoint ID" />
            </Form.Item>

            <Form.Item
              label="Blobs Added"
              name="blobs_added"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Added blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Deleted"
              name="blobs_deleted"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Deleted blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Context Code Exchange Request ID"
              name="context_code_exchange_request_id"
            >
              <Input placeholder="Context Code Exchange Request ID" />
            </Form.Item>

            <Form.Item label="Code Block" name="code_block">
              <TextArea
                rows={12}
                placeholder="Code Block"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item label="Target File Path" name="target_file_path">
              <Input placeholder="Target File Path" />
            </Form.Item>

            <Form.Item label="Target File Content" name="target_file_content">
              <TextArea
                rows={12}
                placeholder="Target File Content"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Divider style={{ margin: "40px 0" }}>Chat History</Divider>
            <Form.List
              name="chat_history"
              wrapperCol={{ offset: 0, span: 24 }}
              size="large"
            >
              {(fields, { add, remove }) => (
                <>
                  {fields.map((field, index) => (
                    <>
                      <Divider>Exchange {index + 1}</Divider>
                      <Form.Item
                        name={[field.name, "request"]}
                        label={`Request`}
                        labelCol={{ offset: 0 }}
                        wrapperCol={{ offset: 1, span: 23 }}
                        rules={[
                          { required: true, message: "Request is required" },
                        ]}
                      >
                        <TextArea
                          placeholder={"Request"}
                          styles={{
                            textarea: {
                              fontFamily: "monospace",
                            },
                          }}
                          autoSize={{ minRows: 2, maxRows: 2 }}
                        />
                      </Form.Item>
                      <Form.Item
                        name={[field.name, "response"]}
                        label={`Response`}
                        labelCol={{ offset: 0 }}
                        wrapperCol={{ offset: 1, span: 23 }}
                      >
                        <TextArea
                          placeholder={"Response (optional)"}
                          styles={{
                            textarea: {
                              fontFamily: "monospace",
                            },
                          }}
                          autoSize={{ minRows: 8, maxRows: 8 }}
                        />
                      </Form.Item>
                      <CloseOutlined
                        style={{
                          color: "red",
                          fontSize: "24px",
                          display: "block",
                          margin: "0 auto",
                        }}
                        aria-label="delete"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            remove(field.name);
                          }
                        }}
                        onClick={() => {
                          remove(field.name);
                        }}
                      />
                    </>
                  ))}
                  <Divider />
                  <Form.Item wrapperCol={{ offset: 9, span: 2 }}>
                    <Button
                      style={{
                        display: "block",
                        margin: "0 auto",
                      }}
                      type="dashed"
                      size="large"
                      onClick={() => {
                        add();
                      }}
                    >
                      + Add Exchange
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
            <Divider style={{ margin: "0 0 40px 0" }} />
          </>
        }
      />
    </>
  );
}

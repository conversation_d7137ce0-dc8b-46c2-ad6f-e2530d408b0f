import React, { useState } from "react";
import { Form, Input, message, Switch, InputNumber } from "antd";
import TextArea from "antd/es/input/TextArea";
import { EditHostRequest, requestEdit } from "../../lib/requests";
import { ReplayFormWrapper } from "./ReplayFormWrapper";

type EditForm = {
  model: string;
  prefix: string;
  suffix: string;
  path: string;
  instruction: string;
  selectedText: string;
  lang: string;
  // begin EditPosition
  blob_name?: string;
  prefix_begin?: number;
  suffix_end?: number;
  // end EditPosition
  // begin blobs
  blobs_baseline_checkpoint_id?: string;
  blobs_added?: string;
  blobs_deleted?: string;
  // end blobs
};

export function EditReplayForm({
  editHostRequest,
  tenantId,
  setReplayRequestId,
}: {
  editHostRequest: EditHostRequest;
  tenantId: string;
  setReplayRequestId: (requestId: string) => void;
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [blobsHidden, setBlobsHidden] = useState<boolean>(false);

  const handleOnFinish = (values: EditForm) => {
    const blobs_added = (values.blobs_added || "")
      .split("\n")
      .filter((s) => s.length > 0);
    const blobs_deleted = (values.blobs_deleted || "")
      .split("\n")
      .filter((s) => s.length > 0);

    // Only send blobs if we have a checkpoint or added blobs
    const blobs = {
      baseline_checkpoint_id: values.blobs_baseline_checkpoint_id,
      added: blobs_added,
      deleted: blobs_deleted,
    };

    const hasPosition =
      values.blob_name !== undefined &&
      values.prefix_begin !== undefined &&
      values.suffix_end !== undefined;
    const positionData = hasPosition
      ? {
          blob_name: values.blob_name,
          prefix_begin: values.prefix_begin,
          suffix_end: values.suffix_end,
        }
      : undefined;

    const request = {
      model_name: values.model,
      instruction: values.instruction,
      prefix: values.prefix,
      selected_text: values.selectedText,
      suffix: values.suffix,
      path: values.path,
      lang: values.lang,
      position: positionData,
      blobs: blobs,
    };
    requestEdit(tenantId, request)
      .then((requestId) => {
        setReplayRequestId(requestId);
      })
      .catch((reason) => {
        messageApi.open({
          type: "error",
          content: JSON.stringify(reason),
        });
      });
  };

  const blobsAdded = (editHostRequest.request.blobs?.added || [])
    .sort()
    .join("\n");
  const blobsDeleted = (editHostRequest.request.blobs?.deleted || [])
    .sort()
    .join("\n");
  const baselineCheckpointId =
    editHostRequest.request.blobs?.baselineCheckpointId;

  return (
    <>
      {contextHolder}
      <ReplayFormWrapper
        initialValues={{
          model: editHostRequest.request.modelName ?? "",
          instruction: editHostRequest.request.instruction,
          prefix: editHostRequest.request.prefix ?? "",
          selectedText: editHostRequest.request.selectedText,
          suffix: editHostRequest.request.suffix ?? "",
          path: editHostRequest.request.path ?? "",
          lang: editHostRequest.request.lang ?? "",
          blob_name: editHostRequest.request.position?.blobName,
          prefix_begin: editHostRequest.request.position?.prefixBegin,
          suffix_end: editHostRequest.request.position?.suffixEnd,
          blobs_added: blobsAdded,
          blobs_deleted: blobsDeleted,
          blobs_baseline_checkpoint_id: baselineCheckpointId,
        }}
        onFinish={handleOnFinish}
        fields={
          <>
            <Form.Item label="Edit" name="edit">
              <TextArea
                rows={3}
                placeholder="Edit"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item
              label="Prefix"
              name="prefix"
              rules={[{ message: "Please input the prefix." }]}
            >
              <TextArea
                rows={12}
                placeholder="Prefix"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item label="Selected Text" name="selectedText">
              <TextArea
                rows={12}
                placeholder="Selected Text"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item label="Suffix" name="suffix">
              <TextArea
                rows={12}
                placeholder="Suffix"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item
              label="Path"
              name="path"
              rules={[{ required: true, message: "Please input the path." }]}
            >
              <Input placeholder="Path" />
            </Form.Item>

            <Form.Item label="Language" name="lang">
              <Input placeholder="Language" />
            </Form.Item>

            <Form.Item label="Current File Blob" name="blob_name">
              <Input placeholder="Blob Name" />
            </Form.Item>

            <Form.Item label="Prefix Begin" name="prefix_begin">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Suffix End" name="suffix_end">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Blobs Hidden">
              <Switch
                onChange={(v) => {
                  setBlobsHidden(v);
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Baseline Checkpoint ID"
              name="blobs_baseline_checkpoint_id"
              hidden={blobsHidden}
            >
              <Input placeholder="Baseline Checkpoint ID" />
            </Form.Item>

            <Form.Item
              label="Blobs Added"
              name="blobs_added"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Added blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Deleted"
              name="blobs_deleted"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Deleted blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
}

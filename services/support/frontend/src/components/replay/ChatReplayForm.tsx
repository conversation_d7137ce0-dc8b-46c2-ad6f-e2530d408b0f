import React, { useState } from "react";
import {
  Button,
  Divider,
  Form,
  Input,
  message,
  Switch,
  InputNumber,
} from "antd";
import { CloseOutlined } from "@ant-design/icons";
import TextArea from "antd/es/input/TextArea";
import {
  Blo<PERSON>,
  ChatHostRequest,
  ChatHostRequestPosition,
  requestChat,
} from "../../lib/requests";
import { ReplayFormWrapper } from "./ReplayFormWrapper";

type ChatForm = {
  model: string;
  prefix: string;
  suffix: string;
  path: string;
  message: string;
  selected_code: string;
  lang: string;
  // begin ChatPosition
  blob_name?: string;
  prefix_begin?: number;
  suffix_end?: number;
  // end ChatPosition
  // begin blobs
  blobs_baseline_checkpoint_id?: string;
  blobs_added?: string;
  blobs_deleted?: string;
  // end blobs
  user_guided_blobs?: string;
  chat_history?: {
    request: string;
    response?: string;
  }[];
  external_source_ids?: string;
  prompt_formatter_name?: string;
};

export function ChatReplayForm({
  chatHostRequest,
  tenantId,
  setReplayRequestId,
}: {
  chatHostRequest: ChatHostRequest;
  tenantId: string;
  setReplayRequestId: (requestId: string) => void;
}) {
  const [messageApi, contextHolder] = message.useMessage();
  const [blobsHidden, setBlobsHidden] = useState<boolean>(false);

  const handleOnFinish = (values: ChatForm) => {
    const blobs_added = (values.blobs_added || "")
      .split("\n")
      .filter((s) => s.length > 0);
    const blobs_deleted = (values.blobs_deleted || "")
      .split("\n")
      .filter((s) => s.length > 0);
    const user_guided_blobs = (values.user_guided_blobs || "")
      .split("\n")
      .filter((s) => s.length > 0);
    const external_source_ids = (values.external_source_ids || "")
      .split("\n")
      .filter((s) => s.length > 0);

    // Only send blobs if we have a checkpoint or added blobs
    const blobs: Blobs = {
      baselineCheckpointId: values.blobs_baseline_checkpoint_id,
      added: blobs_added,
      deleted: blobs_deleted,
    };

    let positionData: ChatHostRequestPosition | undefined = undefined;
    if (
      values.blob_name !== undefined &&
      values.prefix_begin !== undefined &&
      values.suffix_end !== undefined
    ) {
      positionData = {
        blobName: values.blob_name,
        prefixBegin: values.prefix_begin,
        suffixEnd: values.suffix_end,
      };
    }

    const chatHistory = values.chat_history?.map((exchange) => ({
      requestMessage: exchange.request,
      responseText: exchange.response,
    }));
    const request = {
      modelName: values.model,
      message: values.message,
      prefix: values.prefix,
      selectedCode: values.selected_code,
      suffix: values.suffix,
      path: values.path,
      lang: values.lang,
      position: positionData,
      blobs: blobs,
      userGuidedBlobs: user_guided_blobs,
      chatHistory: chatHistory,
      externalSourceIds: external_source_ids,
      // we want to replay exactly the same request, so we disable auto external sources
      disableAutoExternalSources: true,
      promptFormatterName: values.prompt_formatter_name,
    };
    requestChat(tenantId, request)
      .then((requestId) => {
        setReplayRequestId(requestId);
      })
      .catch((reason) => {
        messageApi.open({
          type: "error",
          content: JSON.stringify(reason),
        });
      });
  };

  const firstBlob: Blobs | undefined =
    chatHostRequest.request.blobs.length > 0
      ? chatHostRequest.request.blobs[0]
      : undefined;
  const blobsAdded = (firstBlob?.added || []).sort().join("\n");
  const blobsDeleted = (firstBlob?.deleted || []).sort().join("\n");
  const userGuidedBlobs = (chatHostRequest.request.userGuidedBlobs || [])
    .sort()
    .join("\n");
  const external_source_ids = (chatHostRequest.externalSourceIds || [])
    .sort()
    .join("\n");
  const baselineCheckpointId = firstBlob?.baselineCheckpointId;
  const chatHistory = (chatHostRequest.request.chatHistory || []).map(
    (exchange) => ({
      request: exchange.requestMessage,
      response: exchange.responseText,
    }),
  );
  const isSlackBot = chatHostRequest.request.promptFormatterName === "slackbot";
  return (
    <>
      {contextHolder}
      <ReplayFormWrapper
        initialValues={{
          model: chatHostRequest.request.modelName ?? "",
          message: chatHostRequest.request.message,
          prefix: chatHostRequest.request.prefix ?? "",
          selected_code: chatHostRequest.request.selectedCode,
          suffix: chatHostRequest.request.suffix ?? "",
          path: chatHostRequest.request.path ?? "",
          lang: chatHostRequest.request.lang ?? "",
          blob_name: chatHostRequest.request.position?.blobName,
          prefix_begin: chatHostRequest.request.position?.prefixBegin,
          suffix_end: chatHostRequest.request.position?.suffixEnd,
          blobs_added: blobsAdded,
          blobs_deleted: blobsDeleted,
          blobs_baseline_checkpoint_id: baselineCheckpointId,
          user_guided_blobs: userGuidedBlobs,
          external_source_ids: external_source_ids,
          chat_history: chatHistory,
          prompt_formatter_name: chatHostRequest.request.promptFormatterName,
        }}
        onFinish={handleOnFinish}
        fields={
          <>
            <Form.Item label="Prompt Formatter" name="prompt_formatter_name">
              <Input placeholder="" />
            </Form.Item>

            <Form.Item label="Message" name="message">
              <TextArea
                rows={3}
                placeholder="Message"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item
              label="Prefix"
              name="prefix"
              rules={[{ message: "Please input the prefix." }]}
            >
              <TextArea
                rows={12}
                placeholder="Prefix"
                styles={{ textarea: { fontFamily: "monospace" } }}
                showCount
                spellCheck={false}
              />
            </Form.Item>

            <Form.Item label="Selected Code" name="selected_code">
              <TextArea
                rows={12}
                placeholder="Selected Code"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item label="Suffix" name="suffix">
              <TextArea
                rows={12}
                placeholder="Suffix"
                showCount
                spellCheck={false}
                styles={{ textarea: { fontFamily: "monospace" } }}
              />
            </Form.Item>

            <Form.Item label="Path" name="path">
              <Input placeholder="Path" />
            </Form.Item>

            <Form.Item label="Language" name="lang">
              <Input placeholder="Language" />
            </Form.Item>

            <Form.Item label="Current File Blob" name="blob_name">
              <Input placeholder="Blob Name" />
            </Form.Item>

            <Form.Item label="Prefix Begin" name="prefix_begin">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Suffix End" name="suffix_end">
              <InputNumber<number> min={0} />
            </Form.Item>

            <Form.Item label="Blobs Hidden">
              <Switch
                onChange={(v) => {
                  setBlobsHidden(v);
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Baseline Checkpoint ID"
              name="blobs_baseline_checkpoint_id"
              hidden={blobsHidden}
            >
              <Input placeholder="Baseline Checkpoint ID" />
            </Form.Item>

            <Form.Item
              label="Blobs Added"
              name="blobs_added"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Added blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="Blobs Deleted"
              name="blobs_deleted"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="Deleted blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item
              label="User-Guided Blobs"
              name="user_guided_blobs"
              hidden={blobsHidden}
            >
              <TextArea
                placeholder="User guided blob names (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>

            <Form.Item label="External Sources" name="external_source_ids">
              <TextArea
                placeholder="External Source IDs (one per line)"
                autoSize={{ minRows: 1, maxRows: 32 }}
                styles={{
                  textarea: { fontFamily: "monospace", wordWrap: "normal" },
                }}
              />
            </Form.Item>
            <Divider style={{ margin: "40px 0" }}>Chat History</Divider>
            <Form.List
              name="chat_history"
              wrapperCol={{ offset: 0, span: 24 }}
              size="large"
            >
              {(fields, { add, remove }) => (
                <>
                  {fields.map((field, index) => (
                    <>
                      <Divider>Exchange {index + 1}</Divider>
                      <Form.Item
                        name={[field.name, "request"]}
                        label={`Request`}
                        labelCol={{ offset: 0 }}
                        wrapperCol={{ offset: 1, span: 23 }}
                        rules={[
                          { required: true, message: "Request is required" },
                        ]}
                      >
                        <TextArea
                          placeholder={"Request"}
                          styles={{
                            textarea: {
                              fontFamily: "monospace",
                            },
                          }}
                          autoSize={{ minRows: 2, maxRows: 2 }}
                        />
                      </Form.Item>
                      <Form.Item
                        name={[field.name, "response"]}
                        label={`Response`}
                        labelCol={{ offset: 0 }}
                        wrapperCol={{ offset: 1, span: 23 }}
                        rules={[
                          {
                            required: !isSlackBot,
                            message: "Request is required",
                          },
                        ]}
                      >
                        <TextArea
                          placeholder={
                            isSlackBot
                              ? "Response ignored for slackbot"
                              : "Response"
                          }
                          styles={{
                            textarea: {
                              fontFamily: "monospace",
                            },
                          }}
                          autoSize={{ minRows: 8, maxRows: 8 }}
                        />
                      </Form.Item>
                      <CloseOutlined
                        style={{
                          color: "red",
                          fontSize: "24px",
                          display: "block",
                          margin: "0 auto",
                        }}
                        aria-label="delete"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            remove(field.name);
                          }
                        }}
                        onClick={() => {
                          remove(field.name);
                        }}
                      />
                    </>
                  ))}
                  <Divider />
                  <Form.Item wrapperCol={{ offset: 9, span: 2 }}>
                    <Button
                      style={{
                        display: "block",
                        margin: "0 auto",
                      }}
                      type="dashed"
                      size="large"
                      onClick={() => {
                        add();
                      }}
                    >
                      + Add Exchange
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
            <Divider style={{ margin: "0 0 40px 0" }} />
          </>
        }
      />
    </>
  );
}

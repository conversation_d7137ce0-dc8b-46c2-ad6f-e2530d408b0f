import { <PERSON>, <PERSON>, Alert, Modal } from "antd";
import { ColumnType } from "antd/es/table";
import {
  revokeUserTokensByUserId,
  blockUser,
  unblockUser,
  User,
} from "../lib/users-api";
import { Link } from "react-router-dom";
import { Button, notification } from "antd";
import { ExclamationCircleFilled } from "@ant-design/icons";
import React from "react";

// Helper function to check if a user is blocked or suspended
function isUserBlocked(user: User): boolean {
  console.log("Checking user blocked status:", user.email, {
    blocked: user.blocked,
    suspensions: user.suspensions,
    isBlocked:
      !!user.blocked || !!(user.suspensions && user.suspensions.length > 0),
  });
  return !!user.blocked || !!(user.suspensions && user.suspensions.length > 0);
}

type Props = {
  tenantName: string;
  tenantId: string;
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  users?: Array<User>;
  errorMessage?: string;
  errorAction?: React.ReactNode;
};

const { confirm } = Modal;

async function handleRevokeTokens(
  tenantId: string,
  user: User,
  setLoading: (loading: boolean) => void,
) {
  console.log(`Revoke tokens for ${tenantId} user ${user.id}`);
  try {
    setLoading(true);
    const tokensRevoked = await revokeUserTokensByUserId(tenantId, user.id);
    notification.success({
      message:
        "Successfully revoked " +
        tokensRevoked +
        " tokens for user " +
        user.email,
      placement: "topRight",
    });
  } catch (err) {
    console.error("Failed to revoke user's tokens:", err);
    notification.error({
      message: "Failed to revoke user " + user.email + " tokens: " + err,
      placement: "topRight",
    });
  } finally {
    setLoading(false);
  }
}

async function handleBlockUser(
  tenantId: string,
  user: User,
  setLoading: (loading: boolean) => void,
  onSuccess: () => void,
) {
  try {
    setLoading(true);
    await blockUser(tenantId, user.id);
    // Update the user object locally to reflect the change
    user.blocked = true;
    notification.success({
      message: `Successfully blocked user ${user.email}`,
      placement: "topRight",
    });
    // Call the success callback to refresh the UI
    onSuccess();
  } catch (err) {
    console.error("Failed to block user:", err);
    notification.error({
      message: `Failed to block user ${user.email}: ${err}`,
      placement: "topRight",
    });
  } finally {
    setLoading(false);
  }
}

async function handleUnblockUser(
  tenantId: string,
  user: User,
  setLoading: (loading: boolean) => void,
  onSuccess: () => void,
) {
  try {
    setLoading(true);
    await unblockUser(tenantId, user.id);
    // Update the user object locally to reflect the change
    user.blocked = false;
    // Clear suspensions array if it exists
    if (user.suspensions) {
      user.suspensions = [];
    }
    notification.success({
      message: `Successfully unblocked user ${user.email}`,
      placement: "topRight",
    });
    // Call the success callback to refresh the UI
    onSuccess();
  } catch (err) {
    console.error("Failed to unblock user:", err);
    notification.error({
      message: `Failed to unblock user ${user.email}: ${err}`,
      placement: "topRight",
    });
  } finally {
    setLoading(false);
  }
}

function showBlockConfirm(
  tenantId: string,
  user: User,
  setLoading: (loading: boolean) => void,
  onSuccess: () => void,
) {
  confirm({
    title: `Are you sure you want to block user ${user.email}?`,
    icon: React.createElement(ExclamationCircleFilled),
    content: "This will prevent the user from logging in.",
    okText: "Block",
    okType: "danger",
    cancelText: "Cancel",
    onOk() {
      handleBlockUser(tenantId, user, setLoading, onSuccess);
    },
  });
}

function showUnblockConfirm(
  tenantId: string,
  user: User,
  setLoading: (loading: boolean) => void,
  onSuccess: () => void,
) {
  confirm({
    title: `Are you sure you want to unblock user ${user.email}?`,
    icon: React.createElement(ExclamationCircleFilled),
    content: "This will allow the user to log in again.",
    okText: "Unblock",
    okType: "primary",
    cancelText: "Cancel",
    onOk() {
      handleUnblockUser(tenantId, user, setLoading, onSuccess);
    },
  });
}

function UserTable({
  tenantName,
  tenantId,
  isLoading,
  setLoading,
  users,
  errorMessage,
  errorAction,
}: Props) {
  // Function to refresh the user list
  const refreshUsers = async () => {
    // This will trigger a full refresh in the parent component
    setLoading(true);
    // Wait a bit to ensure the backend has processed the change
    await new Promise((resolve) => setTimeout(resolve, 500));
    // The parent component will fetch the updated user list
    setLoading(false);
  };

  const userColumns: ColumnType<User>[] = [
    {
      title: "User Id",
      dataIndex: "id",
      sorter: (a: User, b: User) => {
        return a.id.localeCompare(b.id);
      },
      render: (id: string) => {
        return <>{id}</>;
      },
    },
    {
      title: "User Email",
      dataIndex: "email",
      defaultSortOrder: "ascend",
      sorter: (a: User, b: User) => {
        return a.email.localeCompare(b.email);
      },
      render: (email: string, user: User) => {
        return (
          <>
            {isUserBlocked(user) && (
              <span title="User is blocked or suspended">🔒 </span>
            )}
            {email}
          </>
        );
      },
    },
    {
      title: "Actions",
      dataIndex: "id",
      render: (id: string, user: User) => {
        return (
          <>
            <Link to={`/t/${tenantName}/users/delete/${id}`}>Delete User</Link>{" "}
            <> | </>
            <Link to={`/t/${tenantName}/users/update/${id}`}>
              Update User
            </Link>{" "}
            <> | </>
            <Button
              type="link"
              onClick={() => handleRevokeTokens(tenantId, user, setLoading)}
              style={{ padding: 0 }}
            >
              Revoke Tokens
            </Button>{" "}
            <> | </>
            {isUserBlocked(user) ? (
              <Button
                type="link"
                onClick={() =>
                  showUnblockConfirm(tenantId, user, setLoading, refreshUsers)
                }
                style={{ padding: 0 }}
              >
                Unblock
              </Button>
            ) : (
              <Button
                type="link"
                danger
                onClick={() =>
                  showBlockConfirm(tenantId, user, setLoading, refreshUsers)
                }
                style={{ padding: 0 }}
              >
                Block
              </Button>
            )}
          </>
        );
      },
    },
  ];

  const table = <Table dataSource={users} columns={userColumns} />;

  if (isLoading) {
    return <Spin>{table}</Spin>;
  }

  if (errorMessage !== undefined) {
    return <Alert message={errorMessage} action={errorAction} type="error" />;
  }
  if (users === undefined) {
    return <Alert message="User list not available" type="error" />;
  }

  return (
    <Table
      rowKey={(user) => user.id}
      dataSource={users}
      columns={userColumns}
    />
  );
}

export function UserList(props: Props) {
  return (
    <>
      <h2>Current Users</h2>

      <UserTable {...props} />
    </>
  );
}

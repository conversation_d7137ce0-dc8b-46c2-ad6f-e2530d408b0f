import { useState } from "react";
import { Button, Form, Input, Spin, notification } from "antd";
import * as usersAPI from "../lib/users-api";
import axios from "axios";

type revokeTokensForm = {
  email: string;
};

type Props = {
  tenantId: string;
};

export function RevokeTokens({ tenantId }: Props) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const [revokeTokensForm] = Form.useForm();

  const onFormFinish = async (values: revokeTokensForm) => {
    setLoading(true);
    try {
      const tokensRevoked = await usersAPI.revokeUserTokensByEmail(
        tenantId,
        values.email,
      );
      notification.success({
        message: "Successfully revoked " + tokensRevoked + " tokens",
        placement: "topRight",
      });
      revokeTokensForm.resetFields();
    } catch (err) {
      console.error("Failed to add user:", err);
      if (axios.isAxiosError(err) && err.response?.status === 403) {
        notification.error({
          message: "You do not have access to this page",
          placement: "topRight",
        });
      } else {
        notification.error({
          message: "Failed to revoke user's tokens: " + err,
          placement: "topRight",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const onFinishFailed = (errorInfo: any) => {
    console.error("Finish failed:", errorInfo);
    notification.error({
      message: "Failed to add user, please ensure all fields are valid",
      placement: "topRight",
    });
  };

  const form = (
    <>
      <h2>Revoke Tokens</h2>

      <Form
        name="revoke_tokens"
        form={revokeTokensForm}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        style={{ maxWidth: 600 }}
        initialValues={{ remember: true }}
        onFinish={onFormFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        disabled={isLoading}
      >
        <Form.Item
          label="User Email"
          name="email"
          rules={[
            {
              required: true,
              message: "Please input the email address of the user",
            },
          ]}
        >
          <Input type="email" />
        </Form.Item>

        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit">
            Revoke Tokens
          </Button>
        </Form.Item>
      </Form>
    </>
  );

  if (isLoading) {
    return <Spin>{form}</Spin>;
  }
  return form;
}

load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@npm//services/support/frontend:eslint/package_json.bzl", eslint_bin = "bin")

ASSET_PATTERNS = [
    "*.svg",
    "**/*.css",
]

SRC_PATTERNS = [
    "*.tsx",
    "components/**/*.tsx",
    "contexts/*.tsx",
    "routes/*.tsx",
    "lib/*.ts",
    "lib/*.tsx",
    "utils/*.ts",
]

js_library(
    name = "src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    visibility = ["//services/support/frontend:__subpackages__"],
    deps = [
        "//services/support/frontend:node_modules/@ant-design/icons",
        "//services/support/frontend:node_modules/@augment-internal/grpc_debug",
        "//services/support/frontend:node_modules/antd",
        "//services/support/frontend:node_modules/axios",
        "//services/support/frontend:node_modules/dayjs",
        "//services/support/frontend:node_modules/react",
        "//services/support/frontend:node_modules/react-dom",
        "//services/support/frontend:node_modules/react-highlight-words",
        "//services/support/frontend:node_modules/react-json-view",
        "//services/support/frontend:node_modules/react-router-dom",
        "//services/support/frontend:node_modules/react-use",
        "//services/support/frontend:node_modules/ts-pattern",
    ],
)

eslint_bin.eslint_test(
    name = "eslint_test",
    args = ["{}/{}".format(
        package_name(),
        p,
    ) for p in SRC_PATTERNS],
    data = [
        "//services/support/frontend:eslintrc_json",
        "//services/support/frontend:node_modules/@typescript-eslint/eslint-plugin",
        "//services/support/frontend:node_modules/@typescript-eslint/parser",
        "//services/support/frontend:node_modules/eslint-plugin-react-hooks",
        "//services/support/frontend:node_modules/react",
        "//services/support/frontend:package_json",
    ] + glob(SRC_PATTERNS),
)

# Services

This directory contains service (REST API/grpc API) of the Application.

## Services Types

There are multiple types of services:

- Shard Services: Most services run within a shard namespace. It will communicate with other shard services using MTLS with a root of trust valid and available only with in the shard. It might call RPC on central and
global services and use cloud resources.
- Central Services: Central services are shared between all shards in a cloud. An examples are services that require a H100 GPU (embedder, inference). The communication is using
an MTLS with a root of trust that is valid for all shards and to central.
- Global Services: Global services are like central services, but the only exist in the lead cloud (e.g. US_CENTRAL1_PROD). The communication is using MTLS with a root of trust that is valid for all shards and to central. The global services using a internal load balancer that is contained in the VPC, but exposed to
all regions. Cross region traffic incurs a higher cost and might be less reliable.

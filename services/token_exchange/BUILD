load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "token_exchange_proto",
    srcs = ["token_exchange.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:struct_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "token_exchange_go_proto",
    importpath = "github.com/augmentcode/augment/services/token_exchange/proto",
    proto = ":token_exchange_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/auth/central/server:auth_entities_go_proto",
    ],
)

py_grpc_library(
    name = "token_exchange_py_proto",
    protos = [":token_exchange_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_py_proto",
    ],
)

ts_proto_library(
    name = "token_exchange_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":token_exchange_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_entities_ts_proto",
    ],
)

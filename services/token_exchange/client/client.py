"""A Python client library for the token exchange service."""

from __future__ import annotations

import logging
import pydantic
import typing
import datetime

import grpc

from base.python.grpc import client_options
import google.protobuf.struct_pb2 as struct_pb2
from services.auth.central.server import auth_entities_pb2
from services.token_exchange import token_exchange_pb2, token_exchange_pb2_grpc


def setup_stub(
    endpoint: str,
    credentials: grpc.ChannelCredentials | None,
    options: client_options.OptionsList | None = None,
) -> token_exchange_pb2_grpc.TokenExchangeStub:
    """Setup the client stub for the token exchange service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials=credentials, options=client_options.create(options)
        )
    stub = token_exchange_pb2_grpc.TokenExchangeStub(channel)
    return stub


class TokenExchangeClient(typing.Protocol):
    """Interface for the token exchange service."""

    def get_signed_token_for_user(
        self,
        user_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        user_email: str,
        tenant_id: str,
        namespace: str | None = None,
        additional_claims: dict[str, typing.Any] | None = None,
    ) -> pydantic.SecretStr:
        """
        returns the signed token for the given user
        """
        raise NotImplementedError()

    def get_signed_token_for_service(
        self, tenant_id: str, scopes: list[token_exchange_pb2.Scope.ValueType]
    ) -> pydantic.SecretStr:
        """
        returns the signed token for the given service, assuming that service is
        initiating a new background task that requires a token
        """
        raise NotImplementedError()

    def get_signed_token_for_iap_token(
        self,
        iap_token: pydantic.SecretStr,
        tenant_id: str,
        scopes: list[token_exchange_pb2.Scope.ValueType],
        expiration: datetime.timedelta | None = None,
    ) -> pydantic.SecretStr:
        """
        returns the signed token for the given IAP token
        """
        raise NotImplementedError()

    def get_verification_key(self) -> bytes:
        """
        returns the verification key for verifying JWTs
        """
        raise NotImplementedError()


class GrpcTokenExchangeClient(TokenExchangeClient):
    """Class to call token exchange APIs remotely."""

    @classmethod
    def create(
        cls,
        endpoint: str,
        namespace: str,
        central_client_credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList = (),
    ) -> GrpcTokenExchangeClient:
        """Constructs a new token exchange client from endpoint and credentials."""
        options = list(options)
        options.extend(
            client_options.get_grpc_client_options(
                client_options.GrpcClientOptions(load_balancing="headless" in endpoint)
            )
        )

        return GrpcTokenExchangeClient(
            endpoint,
            namespace,
            central_client_credentials,
            options=options,
        )

    def __init__(
        self,
        endpoint: str,
        namespace: str,
        credentials: grpc.ChannelCredentials | None,
        options: client_options.OptionsList | None = None,
    ):
        self.endpoint = endpoint
        self.namespace = namespace
        self.stub = setup_stub(endpoint, credentials=credentials, options=options)

    def dict_to_struct(
        self, dictionary: dict[str, typing.Any] | None
    ) -> struct_pb2.Struct:
        """Converts a dictionary to a struct."""
        if dictionary is None:
            return struct_pb2.Struct()

        struct = struct_pb2.Struct()
        for key, value in dictionary.items():
            if isinstance(value, str):
                struct[key] = value
            elif isinstance(value, int):
                struct[key] = value
            elif isinstance(value, float):
                struct[key] = value
            elif isinstance(value, bool):
                struct[key] = value
            elif isinstance(value, dict):
                struct[key] = self.dict_to_struct(value)
            elif isinstance(value, list):
                struct[key] = value
            else:
                raise ValueError(f"Unsupported type for value '{value}' of key '{key}'")
        return struct

    def get_signed_token_for_user(
        self,
        user_id: str,
        opaque_user_id: auth_entities_pb2.UserId,
        user_email: str,
        tenant_id: str,
        namespace: str | None = None,
        additional_claims: dict[str, typing.Any] | None = None,
    ) -> pydantic.SecretStr:
        """
        Returns the token for the given user.

        Args:
            user_id: the user id, deprecated
            opaque_user_id: an opaque user id (usually a uuid)
            user_email: the user email
            tenant_id: the tenant ID to filter the tenants on

        Returns:
            the token for the given user
        """
        request = token_exchange_pb2.GetSignedTokenForUserRequest(
            user_id=user_id,
            opaque_user_id=opaque_user_id,
            user_email=user_email,
            tenant_id=tenant_id,
            shard_namespace=self.namespace if namespace is None else namespace,
            additional_claims=self.dict_to_struct(additional_claims),
        )
        response = self.stub.GetSignedTokenForUser(request)
        return pydantic.SecretStr(response.signed_token)

    def get_signed_token_for_service(
        self, tenant_id: str, scopes: list[token_exchange_pb2.Scope.ValueType]
    ) -> pydantic.SecretStr:
        """
        Returns the token for the given service.

        Args:
            tenant_id: the tenant ID to filter the tenants on

        Returns:
            the token for the given service
        """
        request = token_exchange_pb2.GetSignedTokenForServiceRequest(
            tenant_id=tenant_id, shard_namespace=self.namespace, scopes=scopes
        )
        response = self.stub.GetSignedTokenForService(request)
        return pydantic.SecretStr(response.signed_token)

    def get_signed_token_for_iap_token(
        self,
        iap_token: pydantic.SecretStr,
        tenant_id: str,
        scopes: list[token_exchange_pb2.Scope.ValueType],
        expiration: datetime.timedelta | None = None,
    ) -> pydantic.SecretStr:
        """
        Returns the token for the given IAP token.

        Args:
            iap_token: the IAP token

        Returns:
            the token for the given IAP token
        """
        request = token_exchange_pb2.GetSignedTokenForIAPTokenRequest(
            iap_token=iap_token.get_secret_value(),
            shard_namespace=self.namespace,
            tenant_id=tenant_id,
            scopes=scopes,
        )
        if expiration:
            request.expiration.FromTimedelta(expiration)
        response = self.stub.GetSignedTokenForIAPToken(request)
        return pydantic.SecretStr(response.signed_token)

    # TODO: Return JWKS type instead of bytes
    def get_verification_key(self) -> bytes:
        """
        Returns the verification key for verifying JWTs.

        Returns:
            the verification key for verifying JWTs
        """
        request = token_exchange_pb2.GetVerificationKeyRequest()
        response = self.stub.GetVerificationKey(request)
        return response.jwks

    def __str__(self) -> str:
        return f"GrpcTokenExchangeClient(endpoint={self.endpoint})"

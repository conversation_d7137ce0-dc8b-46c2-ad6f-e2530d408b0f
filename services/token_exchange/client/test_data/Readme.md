The keys in this directory are ONLY used for unit tests.

These were generated using https://jwt.io with an arbitrary private key. It
doesn't matter what the key is as long as the jwt files match the jwks file.

The user token is:
{
  "iat": 1731525314,
  "exp": 4885125314,
  "iss": "test_util.py",
  "user_id": "123",
  "opaque_user_id": "123456",
  "opaque_user_id_type": "AUGMENT",
  "user_email": "<EMAIL>",
  "service_name": "",
  "tenant_id": "dev-augie",
  "shard_namespace": "unit_test_shard",
  "cloud": "unit_test_cloud"
}

The service token is:
{
  "iat": 1731525314,
  "exp": 4885125314,
  "iss": "test_util.py",
  "user_id": "",
  "opaque_user_id": "",
  "opaque_user_id_type": "",
  "user_email": "",
  "service_name": "embedding-indexer-starethanol6-16-1-proj512-svc",
  "tenant_id": "dev-augie",
  "shard_namespace": "unit_test_shard",
  "cloud": "unit_test_cloud"
}

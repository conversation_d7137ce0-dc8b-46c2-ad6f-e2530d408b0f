use std::{sync::Arc, time::Duration};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_auth::GrpcAuth;
use grpc_client::create_channel;
use jsonwebtoken::jwk::{AlgorithmParameters, JwkSet};
use jsonwebtoken::{decode, decode_header, Decoding<PERSON>ey, Validation};
use secrecy::{ExposeSecret, SecretString};
use serde::{Deserialize, Serialize};
use tonic::transport::ClientTlsConfig;

use auth_entities_proto::auth_entities;
use auth_entities_proto::auth_entities::{user_id::UserIdType, UserId};
use request_context::{RequestContext, TenantInfo, TokenScope};
use tracing_tonic::client::TracingService;

use crate::token_exchange::Scope;

pub mod token_exchange {
    tonic::include_proto!("token_exchange");
}

const MAX_TOKEN_SCOPE_ID: i32 = 100;

pub fn get_token_scopes() -> Vec<Scope> {
    lazy_static::lazy_static! {
        static ref SCOPES: Vec<Scope> = {
            let mut scopes = Vec::new();
            for i in 0..MAX_TOKEN_SCOPE_ID {
                if let Ok(scope) = i.try_into() {
                    scopes.push(scope);
                }
            }
            scopes
        };
    }
    SCOPES.clone()
}

#[async_trait]
pub trait TokenExchangeClient {
    /// returns the signed token for the given user
    /// see token_exchange.proto for details.
    ///
    /// Returns:
    ///     the signed token for the given user
    async fn get_signed_token_for_user(
        &self,
        user_id: String,
        opaque_user_id: UserId,
        user_email: Option<String>,
        tenant_id: String,
        request_context: &RequestContext,
    ) -> Result<SecretString, tonic::Status>;

    /// returns the signed token for the given service
    /// see token_exchange.proto for details.
    ///
    /// Returns:
    ///     the signed token for the given service
    async fn get_signed_token_for_service(
        &self,
        tenant_id: String,
        scopes: &[Scope],
        request_context: &RequestContext,
    ) -> Result<SecretString, tonic::Status>;
    async fn get_verification_key(&self) -> Result<Vec<u8>, tonic::Status>;
}

#[derive(Clone)]
pub struct TokenExchangeClientImpl {
    endpoint: String,
    namespace: String,
    tls_config: Option<ClientTlsConfig>,
    request_timeout: Duration,
    client: Arc<
        Mutex<Option<token_exchange::token_exchange_client::TokenExchangeClient<TracingService>>>,
    >,
}

#[async_trait]
impl TokenExchangeClient for TokenExchangeClientImpl {
    async fn get_signed_token_for_user(
        &self,
        user_id: String,
        opaque_user_id: UserId,
        user_email: Option<String>,
        tenant_id: String,
        request_context: &RequestContext,
    ) -> Result<SecretString, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("token_exchange client not ready: {}", e);
            tonic::Status::unavailable("token_exchange not ready")
        })?;
        let mut request = tonic::Request::new(token_exchange::GetSignedTokenForUserRequest {
            user_id,
            opaque_user_id: Some(opaque_user_id),
            user_email,
            tenant_id,
            shard_namespace: self.namespace.clone(),
            additional_claims: None,
            // token exchange will fill in default scopes if none are provided
            scopes: vec![],
        });
        request_context.annotate(request.metadata_mut());

        let response = client.get_signed_token_for_user(request).await?;
        Ok(SecretString::new(response.into_inner().signed_token))
    }

    async fn get_signed_token_for_service(
        &self,
        tenant_id: String,
        scopes: &[Scope],
        request_context: &RequestContext,
    ) -> Result<SecretString, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("token_exchange client not ready: {}", e);
            tonic::Status::unavailable("token_exchange not ready")
        })?;
        let mut request = tonic::Request::new(token_exchange::GetSignedTokenForServiceRequest {
            tenant_id,
            shard_namespace: self.namespace.clone(),
            scopes: scopes.iter().map(|s| (*s).into()).collect(),
        });
        request_context.annotate(request.metadata_mut());

        let response = client.get_signed_token_for_service(request).await?;
        Ok(SecretString::new(response.into_inner().signed_token))
    }

    // TODO: Return JWKS type instead of bytes
    async fn get_verification_key(&self) -> Result<Vec<u8>, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("token_exchange client not ready: {}", e);
            tonic::Status::unavailable("token_exchange not ready")
        })?;
        let request = tonic::Request::new(token_exchange::GetVerificationKeyRequest {});

        let response = client.get_verification_key(request).await?;
        Ok(response.into_inner().jwks)
    }
}

impl TokenExchangeClientImpl {
    pub fn new(
        endpoint: &str,
        namespace: String,
        tls_config: Option<ClientTlsConfig>,
        request_timeout: Duration,
    ) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            namespace,
            tls_config,
            request_timeout,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        token_exchange::token_exchange_client::TokenExchangeClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel = create_channel(
                    self.endpoint.to_string(),
                    Some(self.request_timeout),
                    &self.tls_config,
                )
                .await?;
                let client =
                    token_exchange::token_exchange_client::TokenExchangeClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

// Source of truth: lib/grpc/auth/claims.go
#[derive(Debug, Serialize, Deserialize, PartialEq)]
struct AugmentClaims {
    pub user_id: String,
    pub opaque_user_id: String,
    pub opaque_user_id_type: String,
    pub user_email: String,
    pub service_name: String,
    pub tenant_id: String,
    pub tenant_name: Option<String>,
    pub shard_namespace: String,
    pub cloud: String,
    pub scope: Option<Vec<String>>,
}

fn verify_jwt(jwks_raw: &[u8], jwt_raw: &str) -> tonic::Result<AugmentClaims> {
    let jwks: JwkSet = serde_json::from_slice(jwks_raw)
        .map_err(|e| tonic::Status::internal(format!("Malformed JwkSet: {}", e)))?;
    let header = decode_header(jwt_raw)
        .map_err(|e| tonic::Status::invalid_argument(format!("Malformed token header: {}", e)))?;

    let kid = header
        .kid
        .ok_or_else(|| tonic::Status::invalid_argument("No kid in token"))?;
    let jwk = jwks
        .find(&kid)
        .ok_or_else(|| tonic::Status::invalid_argument("No key found for kid"))?;

    let decoding_key = match &jwk.algorithm {
        AlgorithmParameters::EllipticCurve(params) => {
            DecodingKey::from_ec_components(&params.x, &params.y).map_err(|e| {
                tonic::Status::invalid_argument(format!("Invalid key params: {}", e))
            })?
        }
        _ => return Err(tonic::Status::invalid_argument("Unsupported algorithm")),
    };

    let validation = Validation::new(header.alg);

    let claims = decode::<AugmentClaims>(jwt_raw, &decoding_key, &validation)
        .map_err(|e| tonic::Status::unauthenticated(format!("Invalid token: {}", e)))?;

    Ok(claims.claims)
}

pub struct TokenGrpcAuth {
    token_exchange_client: Arc<dyn TokenExchangeClient + Send + Sync>,
    cached_key: Arc<Mutex<Option<Vec<u8>>>>,
    required_scopes: Vec<TokenScope>,
}

impl TokenGrpcAuth {
    pub fn new(
        token_exchange_client: Arc<dyn TokenExchangeClient + Send + Sync>,
        required_scopes: Vec<TokenScope>,
    ) -> Self {
        Self {
            token_exchange_client,
            cached_key: Arc::new(Mutex::new(None)),
            required_scopes,
        }
    }
}

#[async_trait]
impl GrpcAuth for TokenGrpcAuth {
    async fn validate_access(
        &self,
        _peer_identities: Vec<Vec<u8>>,
        auth_token: Option<SecretString>,
        _method_name: &str,
    ) -> tonic::Result<TenantInfo> {
        match auth_token {
            None => Err(tonic::Status::unauthenticated("No token provided")),
            Some(token) => {
                // Try the cached key first, but always fall back to a live verification key since
                // the cached key may be stale
                if let Some(ref cached_key) = *self.cached_key.lock().await {
                    match verify_jwt(cached_key.as_ref(), token.expose_secret()) {
                        Ok(claims) => {
                            let scopes = claims
                                .scope
                                .into_iter()
                                .flatten()
                                .filter_map(|s| TokenScope::from_string_ignore_invalid(&s))
                                .collect::<Vec<TokenScope>>();
                            return Ok(TenantInfo {
                                tenant_id: Some(claims.tenant_id.into()),
                                tenant_name: claims.tenant_name.unwrap_or_default(),
                                shard_namespace: claims.shard_namespace,
                                cloud: claims.cloud,
                                scopes,
                                user_id: match claims.user_id.as_str() {
                                    "" => None,
                                    _ => Some(SecretString::new(claims.user_id)),
                                },
                                opaque_user_id: match (
                                    claims.opaque_user_id.as_str(),
                                    claims.opaque_user_id_type.as_str(),
                                ) {
                                    ("", _) => None,
                                    (_, "") => None,
                                    (opaque_user_id, opaque_user_id_type) => {
                                        UserIdType::from_str_name(opaque_user_id_type).map(
                                            |user_id_type| UserId {
                                                user_id: opaque_user_id.to_string(),
                                                user_id_type: user_id_type.into(),
                                            },
                                        )
                                    }
                                },
                                user_email: match claims.user_email.as_str() {
                                    "" => None,
                                    _ => Some(SecretString::new(claims.user_email)),
                                },
                                service_name: match claims.service_name.as_str() {
                                    "" => None,
                                    _ => Some(claims.service_name),
                                },
                            });
                        }
                        Err(status) => {
                            // Don't refetch the verification key if the key is correctly
                            // configured and the validation itself failed
                            if status.code() == tonic::Code::Unauthenticated {
                                return Err(status);
                            }
                            // Otherwise fall-through and continue to refetch
                        }
                    }
                }
                let key_bytes: Vec<u8> = self.token_exchange_client.get_verification_key().await?;
                let claims = verify_jwt(key_bytes.as_ref(), token.expose_secret())?;
                // Update the cache
                *self.cached_key.lock().await = Some(key_bytes);
                let scopes = claims
                    .scope
                    .into_iter()
                    .flatten()
                    .filter_map(|s| TokenScope::from_string_ignore_invalid(&s))
                    .collect::<Vec<TokenScope>>();

                // check required scopes
                if !self.required_scopes.iter().all(|s| scopes.contains(s)) {
                    return Err(tonic::Status::permission_denied("Access denied"));
                }

                Ok(TenantInfo {
                    tenant_id: Some(claims.tenant_id.into()),
                    tenant_name: claims.tenant_name.unwrap_or_default(),
                    shard_namespace: claims.shard_namespace,
                    cloud: claims.cloud,
                    scopes,
                    user_id: match claims.user_id.as_str() {
                        "" => None,
                        _ => Some(SecretString::new(claims.user_id)),
                    },
                    opaque_user_id: match (
                        claims.opaque_user_id.as_str(),
                        claims.opaque_user_id_type.as_str(),
                    ) {
                        ("", _) => None,
                        (_, "") => None,
                        (opaque_user_id, opaque_user_id_type) => {
                            UserIdType::from_str_name(opaque_user_id_type).map(|user_id_type| {
                                UserId {
                                    user_id: opaque_user_id.to_string(),
                                    user_id_type: user_id_type.into(),
                                }
                            })
                        }
                    },
                    user_email: match claims.user_email.as_str() {
                        "" => None,
                        _ => Some(SecretString::new(claims.user_email)),
                    },
                    service_name: match claims.service_name.as_str() {
                        "" => None,
                        _ => Some(claims.service_name),
                    },
                })
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    pub struct MockTokenExchangeClient {
        signed_user_token: String,
        signed_service_token: String,
        verification_key: Vec<u8>,
        get_verification_key_count: Mutex<u32>,
    }

    impl MockTokenExchangeClient {
        pub fn new() -> Self {
            Self {
                // Remember, don't imitate this pattern of using hardcoded JWTs / JWKSs in prod!
                // These data files were generated in the go server test using the private key in
                // server/test_data/ec256-cert-private.pem. The JWT has an expiration time of 1M
                // hours (>100 years) to make it safe to use in unit tests.
                signed_user_token: include_str!("test_data/example_user.jwt").to_string(),
                signed_service_token: include_str!("test_data/example_service.jwt").to_string(),
                verification_key: include_bytes!("test_data/example.jwks").to_vec(),
                get_verification_key_count: Mutex::new(0),
            }
        }
        async fn get_verification_key_count(&self) -> u32 {
            *self.get_verification_key_count.lock().await
        }
    }

    #[async_trait]
    impl TokenExchangeClient for MockTokenExchangeClient {
        async fn get_signed_token_for_user(
            &self,
            _user_id: String,
            _opaque_user_id: UserId,
            _user_email: Option<String>,
            tenant_id: String,
            _request_context: &RequestContext,
        ) -> Result<SecretString, tonic::Status> {
            // this is the tenant in the test_data token
            assert_eq!("120", &tenant_id);
            Ok(SecretString::new(self.signed_user_token.clone()))
        }
        async fn get_signed_token_for_service(
            &self,
            _tenant_id: String,
            _scopes: &[Scope],
            _request_context: &RequestContext,
        ) -> Result<SecretString, tonic::Status> {
            Ok(SecretString::new(self.signed_service_token.clone()))
        }
        async fn get_verification_key(&self) -> Result<Vec<u8>, tonic::Status> {
            *self.get_verification_key_count.lock().await += 1;
            Ok(self.verification_key.clone())
        }
    }

    #[test]
    fn test_verify_jwt() {
        let public_key_bytes = include_bytes!("test_data/example.jwks");

        let token = include_str!("test_data/example_user.jwt");
        assert_eq!(
            AugmentClaims {
                user_id: "123".to_string(),
                opaque_user_id: "123456".to_string(),
                opaque_user_id_type: "AUGMENT".to_string(),
                user_email: "<EMAIL>".to_string(),
                service_name: "".to_string(),
                tenant_id: "dev-augie".to_string(),
                tenant_name: None,
                shard_namespace: "unit_test_shard".to_string(),
                cloud: "unit_test_cloud".to_string(),
                scope: None
            },
            verify_jwt(public_key_bytes, token).unwrap()
        );

        let token = include_str!("test_data/example_service.jwt");
        assert_eq!(
            AugmentClaims {
                user_id: "".to_string(),
                opaque_user_id: "".to_string(),
                opaque_user_id_type: "".to_string(),
                user_email: "".to_string(),
                service_name: "embedding-indexer-starethanol6-16-1-proj512-svc".to_string(),
                tenant_id: "dev-augie".to_string(),
                tenant_name: None,
                shard_namespace: "unit_test_shard".to_string(),
                cloud: "unit_test_cloud".to_string(),
                scope: None
            },
            verify_jwt(public_key_bytes, token).unwrap()
        );
    }

    #[tokio::test]
    async fn test_validate_access_with_token() {
        let client = Arc::new(MockTokenExchangeClient::new());
        let auth = TokenGrpcAuth::new(client.clone(), vec![]);
        let token = client
            .get_signed_token_for_user(
                "123".to_string(),
                UserId {
                    user_id: "123456".to_string(),
                    user_id_type: UserIdType::Augment.into(),
                },
                Some("<EMAIL>".to_string()),
                "120".to_string(),
                &RequestContext::new_for_test(),
            )
            .await
            .unwrap();
        assert_eq!(
            TenantInfo {
                tenant_id: Some("dev-augie".to_string().into()),
                tenant_name: "".to_string(),
                shard_namespace: "unit_test_shard".to_string(),
                cloud: "unit_test_cloud".to_string(),
                scopes: vec![],
                user_id: Some("123".to_string().into()),
                opaque_user_id: Some(UserId {
                    user_id: "123456".to_string(),
                    user_id_type: UserIdType::Augment.into()
                }),
                user_email: Some("<EMAIL>".to_string().into()),
                service_name: None
            },
            auth.validate_access(vec![], Some(token), "test")
                .await
                .unwrap()
        );
    }

    #[tokio::test]
    async fn test_validate_access_bad_token() {
        let client = Arc::new(MockTokenExchangeClient::new());
        let auth = TokenGrpcAuth::new(client.clone(), vec![]);
        let token = "thisisnotarealtoken".to_string();
        assert!(auth
            .validate_access(vec![], Some(SecretString::new(token.clone())), "test")
            .await
            .is_err());
    }

    #[tokio::test]
    async fn test_validate_access_require_token() {
        let client = Arc::new(MockTokenExchangeClient::new());
        let auth = TokenGrpcAuth::new(client.clone(), vec![]);
        assert!(auth.validate_access(vec![], None, "test").await.is_err());
    }

    #[tokio::test]
    async fn test_validate_access_cache_hit() {
        let client = Arc::new(MockTokenExchangeClient::new());
        let auth = TokenGrpcAuth::new(client.clone(), vec![]);
        let token = client
            .get_signed_token_for_user(
                "123".to_string(),
                UserId {
                    user_id: "123".to_string(),
                    user_id_type: UserIdType::Augment.into(),
                },
                Some("<EMAIL>".to_string()),
                "120".to_string(),
                &RequestContext::new_for_test(),
            )
            .await
            .unwrap();

        assert_eq!(
            TenantInfo {
                tenant_id: Some("dev-augie".to_string().into()),
                tenant_name: "".to_string(),
                shard_namespace: "unit_test_shard".to_string(),
                cloud: "unit_test_cloud".to_string(),
                scopes: vec![],
                user_id: Some("123".to_string().into()),
                opaque_user_id: Some(UserId {
                    user_id: "123".to_string(),
                    user_id_type: UserIdType::Augment.into()
                }),
                user_email: Some("<EMAIL>".to_string().into()),
                service_name: None
            },
            auth.validate_access(vec![], Some(token.clone()), "test")
                .await
                .unwrap()
        );
        // Exactly the same as the first call, to test caching
        assert_eq!(
            TenantInfo {
                tenant_id: Some("dev-augie".to_string().into()),
                tenant_name: "".to_string(),
                shard_namespace: "unit_test_shard".to_string(),
                cloud: "unit_test_cloud".to_string(),
                scopes: vec![],
                user_id: Some("123".to_string().into()),
                opaque_user_id: Some(UserId {
                    user_id: "123".to_string(),
                    user_id_type: UserIdType::Augment.into()
                }),
                user_email: Some("<EMAIL>".to_string().into()),
                service_name: None
            },
            auth.validate_access(vec![], Some(token.clone()), "test")
                .await
                .unwrap()
        );
        assert_eq!(client.get_verification_key_count().await, 1);
    }

    #[tokio::test]
    async fn test_validate_access_cache_miss() {
        let client = Arc::new(MockTokenExchangeClient::new());
        let auth = TokenGrpcAuth::new(client.clone(), vec![]);
        let token = client
            .get_signed_token_for_user(
                "test-user".to_string(),
                UserId {
                    user_id: "123".to_string(),
                    user_id_type: UserIdType::Augment.into(),
                },
                Some("<EMAIL>".to_string()),
                "120".to_string(),
                &RequestContext::new_for_test(),
            )
            .await
            .unwrap();
        auth.validate_access(vec![], Some(token), "test")
            .await
            .unwrap();
        // Artificial and somewhat hacky way to simulate a stale verification key by validating
        // a bogus token. TODO: add a second (jwt, jwks) set to the test data and support changing
        // the mock values so we can test this for real
        assert!(auth
            .validate_access(
                vec![],
                Some(SecretString::new("thisisnotarealtoken".to_string())),
                "test"
            )
            .await
            .is_err());
        assert_eq!(client.get_verification_key_count().await, 2);
    }
}

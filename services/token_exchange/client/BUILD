load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl:rust.bzl", "rust_library", "rust_test")
load("@io_bazel_rules_go//extras:gomock.bzl", "gomock")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = [
        "//services:__subpackages__",
        "//tools:__subpackages__",
    ],
    deps = [
        requirement("pydantic"),
        "//base/python/grpc:client_options",
        "//services/auth/central/server:auth_entities_py_proto",
        "//services/token_exchange:token_exchange_py_proto",
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["token_exchange_client.rs"],
    aliases = aliases(),
    crate_name = "token_exchange_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = [
        "//services:__subpackages__",
        "//tools/genie:__subpackages__",
    ],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/feature_flags:feature_flags_rs",
        "//base/rust/tracing-tonic",
        "//services/auth/central/server:auth_entities_rs_proto",
        "//services/lib/grpc/auth:grpc_auth",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/token_exchange/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_rs_zerolog//log",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//types/known/durationpb",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/auth/central/server:auth_entities_proto",
        "//services/token_exchange:token_exchange_proto",
        "@protobuf//:duration_proto",
        "@protobuf//:protoc",
        "@protobuf//:struct_proto",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

rust_test(
    name = "token_exchange_client_test",
    aliases = aliases(
        normal_dev = True,
        proc_macro_dev = True,
    ),
    crate = ":client_rs",
    data = glob(["test_data/**"]),
    proc_macro_deps = all_crate_deps(
        proc_macro_dev = True,
    ),
    deps = all_crate_deps(
        normal_dev = True,
    ),
)

gomock(
    name = "mock_token_exchange_client_gomock",
    out = "mocks/token_exchange_client_gomock.go",
    interfaces = ["TokenExchangeClient"],
    library = ":client_go",
    package = "mocks",
    visibility = ["//services:__subpackages__"],
)

go_library(
    name = "mock_token_exchange_client_go",
    srcs = [":mock_token_exchange_client_gomock"],
    importpath = "github.com/augmentcode/augment/services/token_exchange/client/mocks",
    visibility = ["//services:__subpackages__"],
    deps = [
        ":client_go",
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_golang_mock//gomock",
    ],
)

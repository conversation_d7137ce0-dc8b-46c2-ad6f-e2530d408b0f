package token_exchange_client

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	pb "github.com/augmentcode/augment/services/token_exchange/proto"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// TokenExchangeClient is an interface for interacting with the token exchange service.
// The token exchange service provides JWT tokens that can be used for authentication
// within the system. These tokens contain claims about the user or service identity,
// tenant information, and access scopes.
//
// See token_exchange.proto for the complete service definition and available scopes.
type TokenExchangeClient interface {
	// GetSignedTokenFor<PERSON><PERSON> generates a signed JWT token for a user.
	//
	// Parameters:
	//   - ctx: The context for the request.
	//   - userID: A unique identifier for the user (will be deprecated in favor of opaqueUserID).
	//   - opaqueUserID: The opaque user ID structure that contains the user's identity information.
	//   - userEmail: Optional email address of the user.
	//   - tenantID: The ID of the tenant the token is for.
	//   - shardNamespace: Optional shard namespace to use. If nil, the client's default namespace is used.
	//   - additionalClaims: Optional additional claims to include in the token.
	//
	// Returns:
	//   - A SecretString containing the signed JWT token.
	//   - An error if the token generation fails.
	GetSignedTokenForUser(ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId, userEmail *string, tenantID string, shardNamespace *string, additionalClaims map[string]any) (secretstring.SecretString, error)

	// GetSignedTokenForService generates a signed JWT token for a service.
	// This is typically used for background tasks that require authentication.
	//
	// Parameters:
	//   - ctx: The context for the request.
	//   - tenantID: The ID of the tenant the token is for. If empty, the token is for all tenants.
	//   - scopes: The permission scopes to include in the token.
	//
	// Returns:
	//   - A SecretString containing the signed JWT token.
	//   - An error if the token generation fails.
	GetSignedTokenForService(ctx context.Context, tenantID string, scopes []pb.Scope) (secretstring.SecretString, error)

	// GetSignedTokenForServiceWithNamespace generates a signed JWT token for a service with a custom namespace.
	// This is useful for central services initializing tokens for specific namespaces.
	//
	// Parameters:
	//   - ctx: The context for the request.
	//   - tenantID: The ID of the tenant the token is for. If empty, the token is for all tenants.
	//   - shardNamespace: The custom shard namespace to use.
	//   - scopes: The permission scopes to include in the token.
	//
	// Returns:
	//   - A SecretString containing the signed JWT token.
	//   - An error if the token generation fails.
	GetSignedTokenForServiceWithNamespace(ctx context.Context, tenantID string, shardNamespace string, scopes []pb.Scope) (secretstring.SecretString, error)

	// GetSignedTokenForIAPToken exchanges an IAP (Identity-Aware Proxy) token for a signed JWT token.
	//
	// Parameters:
	//   - ctx: The context for the request.
	//   - iapToken: The IAP token to exchange.
	//   - tenantID: The ID of the tenant the token is for. If empty, the token is for all tenants.
	//   - scopes: The permission scopes to include in the token.
	//   - expiration: The requested expiration duration for the token. Set to 0 to use the default.
	//
	// Returns:
	//   - A SecretString containing the signed JWT token.
	//   - An error if the token exchange fails.
	GetSignedTokenForIAPToken(ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []pb.Scope, expiration time.Duration) (secretstring.SecretString, error)

	// GetVerificationKey retrieves the public key (JWKS) that can be used to verify JWT tokens.
	//
	// Parameters:
	//   - ctx: The context for the request.
	//
	// Returns:
	//   - The JWKS (JSON Web Key Set) as bytes.
	//   - An error if the key retrieval fails.
	GetVerificationKey(ctx context.Context) ([]byte, error)

	// Close releases resources associated with the client.
	// This should be called when the client is no longer needed.
	Close()
}

type tokenExchangeClientImpl struct {
	// Endpoint to connect to.
	endpoint string

	// Shard namespace to use for requests.
	shardNamespace string

	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	stub pb.TokenExchangeClient
}

// New creates a new token exchange client that connects to the specified endpoint.
//
// Parameters:
//   - endpoint: The endpoint of the token exchange service (e.g., "localhost:50051").
//   - shardNamespace: The shard namespace to use for requests. This is used as the default
//     namespace for all requests unless overridden in specific method calls.
//   - grpcOpts: Optional gRPC dial options to use when establishing the connection.
//
// Returns:
//   - A TokenExchangeClient implementation.
//   - An error if the client creation fails.
func New(
	endpoint string, shardNamespace string, grpcOpts ...grpc.DialOption,
) (TokenExchangeClient, error) {
	grpcOpts = append(grpcOpts, grpc.WithStatsHandler(otelgrpc.NewClientHandler()))
	conn, err := grpc.NewClient(endpoint, grpcOpts...)
	if err != nil {
		return nil, err
	}

	return &tokenExchangeClientImpl{
		endpoint:       endpoint,
		shardNamespace: shardNamespace,
		conn:           conn,
		stub:           pb.NewTokenExchangeClient(conn),
	}, nil
}

// Close releases resources associated with the client by closing the gRPC connection.
func (c *tokenExchangeClientImpl) Close() {
	c.conn.Close()
}

// GetSignedTokenForUser generates a signed JWT token for a user.
// If shardNamespace is nil, the client's default namespace will be used.
func (c *tokenExchangeClientImpl) GetSignedTokenForUser(
	ctx context.Context,
	userID string,
	opaqueUserID *authentitiesproto.UserId,
	userEmail *string,
	tenantID string,
	shardNamespace *string,
	additionalClaims map[string]any,
) (secretstring.SecretString, error) {
	if shardNamespace == nil {
		shardNamespace = &c.shardNamespace
	}

	additionalClaimsPb, err := structpb.NewStruct(additionalClaims)
	if err != nil {
		return secretstring.SecretString{}, err
	}

	request := &pb.GetSignedTokenForUserRequest{
		UserId:           userID,
		OpaqueUserId:     opaqueUserID,
		UserEmail:        userEmail,
		TenantId:         tenantID,
		ShardNamespace:   *shardNamespace,
		AdditionalClaims: additionalClaimsPb,
	}
	response, err := c.stub.GetSignedTokenForUser(ctx, request)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(response.SignedToken), nil
}

// GetSignedTokenForService generates a signed JWT token for a service.
// The token will include the client's default shard namespace.
func (c *tokenExchangeClientImpl) GetSignedTokenForService(
	ctx context.Context, tenantID string, scopes []pb.Scope,
) (secretstring.SecretString, error) {
	request := &pb.GetSignedTokenForServiceRequest{
		TenantId:       tenantID,
		ShardNamespace: c.shardNamespace,
		Scopes:         scopes,
	}
	response, err := c.stub.GetSignedTokenForService(ctx, request)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(response.SignedToken), nil
}

// GetSignedTokenForServiceWithNamespace generates a signed JWT token for a service with a custom namespace.
// This is useful for central services initializing tokens for specific namespaces.
func (c *tokenExchangeClientImpl) GetSignedTokenForServiceWithNamespace(
	ctx context.Context, tenantID string, namespace string, scopes []pb.Scope,
) (secretstring.SecretString, error) {
	request := &pb.GetSignedTokenForServiceRequest{
		TenantId:       tenantID,
		ShardNamespace: namespace,
		Scopes:         scopes,
	}
	response, err := c.stub.GetSignedTokenForService(ctx, request)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(response.SignedToken), nil
}

// GetSignedTokenForIAPToken exchanges an IAP token for a signed JWT token.
// The token will include the specified tenant ID, shard namespace, and scopes.
// Set expiration to 0 to use the default expiration time configured in the token exchange service.
func (c *tokenExchangeClientImpl) GetSignedTokenForIAPToken(
	ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []pb.Scope,
	expiration time.Duration,
) (secretstring.SecretString, error) {
	request := &pb.GetSignedTokenForIAPTokenRequest{
		IapToken:       iapToken.Expose(),
		TenantId:       tenantID,
		ShardNamespace: c.shardNamespace,
		Scopes:         scopes,
	}
	if expiration != 0 {
		request.Expiration = durationpb.New(expiration)
	}
	response, err := c.stub.GetSignedTokenForIAPToken(ctx, request)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(response.SignedToken), nil
}

// GetVerificationKey retrieves the public key (JWKS) that can be used to verify JWT tokens.
func (c *tokenExchangeClientImpl) GetVerificationKey(ctx context.Context) ([]byte, error) {
	request := &pb.GetVerificationKeyRequest{}
	response, err := c.stub.GetVerificationKey(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.Jwks, nil
}

// MockTokenExchangeClient provides a mock implementation of the TokenExchangeClient interface for testing.
// It reads tokens and verification keys from files instead of making actual gRPC calls.
type MockTokenExchangeClient struct {
	SignedTokenPath         string
	PublicJwksPath          string
	GetVerificationKeyCount int
}

// NewMockTokenExchangeClient creates a new mock token exchange client.
//
// Parameters:
//   - signedTokenPath: Path to a file containing a signed JWT token to return for user requests.
//   - publicJwksPath: Path to a file containing the JWKS (JSON Web Key Set) to return for verification key requests.
//
// Returns:
//   - A MockTokenExchangeClient instance.
func NewMockTokenExchangeClient(
	signedTokenPath string, publicJwksPath string,
) *MockTokenExchangeClient {
	return &MockTokenExchangeClient{
		SignedTokenPath:         signedTokenPath,
		PublicJwksPath:          publicJwksPath,
		GetVerificationKeyCount: 0,
	}
}

// GetSignedTokenForUser returns a signed token read from the SignedTokenPath file.
// This mock implementation ignores all parameters except the context.
func (c *MockTokenExchangeClient) GetSignedTokenForUser(
	ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId,
	userEmail *string, tenantID string, namespace *string,
	additionalClaims map[string]any,
) (secretstring.SecretString, error) {
	data, err := os.ReadFile(c.SignedTokenPath)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(string(data)), nil
}

// GetSignedTokenForService returns a signed token read from the SignedTokenPath file.
// This mock implementation ignores all parameters except the context.
func (c *MockTokenExchangeClient) GetSignedTokenForService(
	ctx context.Context, tenantID string, scopes []pb.Scope,
) (secretstring.SecretString, error) {
	data, err := os.ReadFile(c.SignedTokenPath)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(string(data)), nil
}

// GetSignedTokenForServiceWithNamespace returns a signed token read from the SignedTokenPath file.
// This mock implementation ignores all parameters except the context.
func (c *MockTokenExchangeClient) GetSignedTokenForServiceWithNamespace(
	ctx context.Context, tenantID string, shardNamespace string, scopes []pb.Scope,
) (secretstring.SecretString, error) {
	data, err := os.ReadFile(c.SignedTokenPath)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(string(data)), nil
}

// GetSignedTokenForIAPToken returns an error as it is not implemented in the mock.
func (c *MockTokenExchangeClient) GetSignedTokenForIAPToken(
	ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []pb.Scope,
	expiration time.Duration,
) (secretstring.SecretString, error) {
	return secretstring.SecretString{}, fmt.Errorf("not implemented")
}

// GetVerificationKey returns the JWKS read from the PublicJwksPath file.
// It also increments the GetVerificationKeyCount counter for testing purposes.
func (c *MockTokenExchangeClient) GetVerificationKey(ctx context.Context) ([]byte, error) {
	c.GetVerificationKeyCount += 1
	return os.ReadFile(c.PublicJwksPath)
}

// Close is a no-op for the mock client as there are no resources to release.
func (c *MockTokenExchangeClient) Close() {}

# Token Exchange Client Library

## Claims

- `user_id`: A unique identifier for the user. If it is not set, the token is for a service.
- `service_name`: A unique identifier for the service. If it is not set, the token is for a user.

- `tenant_id`: The unique identifier for the tenant. If it is not set, the token is for all tenants of a namespace.
- `tenant_name`: The human-readable name of the tenant. If it is not set, the token is for all tenants of a namespace.

- `shard_namespace`: The namespace the token is for, usually the namespace that is currently responsible for the tenant.

## Client

This library provides a client for the token exchange service.

```python
from services.token_exchange.client import client

client = client.TokenExchangeClient(
    endpoint="localhost:50051",
    credentials=None,
)

token = client.get_signed_token_for_user(
    user_id="test-user",
    shard_namespace="test-shard",
    tenant_id="test-tenant",
)
```

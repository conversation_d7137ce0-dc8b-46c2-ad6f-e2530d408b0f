[package]
name = "token_exchange_client"
version = "0.1.0"
edition = "2021"

[lib]
name = "token_exchange_client"
path = "token_exchange_client.rs"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
feature-flags = { path = "../../../base/feature_flags" }
grpc_auth = { path = "../../lib/grpc/auth" }
grpc_client = { path = "../../lib/grpc/client" }
jsonwebtoken = { workspace = true }
prost = { workspace = true }
prost-wkt = { workspace = true }
prost-wkt-types = { workspace = true }
serde = { workspace = true }
tokio = { workspace = true }
serde_json = { workspace = true }
tonic = { workspace = true }
tonic-build = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../base/rust/tracing-tonic" }
request_context = { path = "../../lib/request_context" }
secrecy = { workspace = true }
auth_entities_proto = { path = "../../auth/central/server" }
lazy_static = { workspace = true }

[build-dependencies]
tonic-build = { workspace = true }
prost-wkt-build = { workspace = true }

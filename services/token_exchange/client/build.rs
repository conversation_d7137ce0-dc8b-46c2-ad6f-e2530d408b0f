// the build.rs file is executed by cargo at build-time
// and is used to generate code.
use std::{env, path::Path, path::PathBuf};

fn get_base_dir() -> PathBuf {
    let cwd = env::current_dir().expect("failed to get cwd");
    let root = cwd.join("../../../").canonicalize().unwrap();
    root
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // generate the code from protobuf files via build.rs so that cargo/rust-analyzer continues
    // to work.
    let root = get_base_dir();

    let protos = vec![
        root.join("services/token_exchange/token_exchange.proto"),
        root.join("services/auth/central/server/auth_entities.proto"),
    ];
    for proto in protos {
        let proto_path: &Path = proto.as_ref();

        let proto_dir = proto_path.parent().unwrap();
        let protobuf_duration_dir =
            root.join("../protobuf~/src/google/protobuf/_virtual_imports/duration_proto/");

        let protobuf_struct_dir =
            root.join("../protobuf~/src/google/protobuf/_virtual_imports/struct_proto/");
        let protobuf_timestamp_dir =
            root.join("../protobuf~/src/google/protobuf/_virtual_imports/timestamp_proto/");
        let includes = vec![
            proto_dir,
            root.as_ref(),
            protobuf_duration_dir.as_ref(),
            protobuf_struct_dir.as_ref(),
            protobuf_timestamp_dir.as_ref(),
        ];

        tonic_build::configure()
            .extern_path(".google.protobuf.Duration", "::prost_wkt_types::Duration")
            .extern_path(".google.protobuf.Struct", "::prost_wkt_types::Struct")
            .extern_path(".google.protobuf.Timestamp", "::prost_wkt_types::Timestamp")
            .compile_protos(&[proto_path], &includes)?;
    }
    Ok(())
}

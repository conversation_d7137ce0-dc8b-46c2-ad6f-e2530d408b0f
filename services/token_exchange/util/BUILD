load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "util",
    srcs = [
        "util.py",
    ],
    data = [
        "//deploy/common:cloud_info_json",
    ],
    deps = [
        "//base/logging:console_logging",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/request_context:request_context_py",
        "//services/token_exchange/client:client_py",
    ],
)

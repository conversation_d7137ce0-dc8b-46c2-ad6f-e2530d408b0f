"""CLI Utility to get a service token"""

import argparse
import logging
import pathlib
import sys
import json
from dataclasses import dataclass

from base.logging.console_logging import setup_console_logging
import getpass


def _get_cloud_info():
    return json.loads(
        pathlib.Path("deploy/common/cloud_info.json").read_text(encoding="utf-8")
    )


def _get_support_url(namespace: str, tenant_name: str, cloud: str):
    ci = _get_cloud_info()
    # find internal domain
    domain_suffix = ci[cloud]["internalDomainSuffix"]
    return f"https://support.{namespace}.t.{domain_suffix}/service-token?tenant_name={tenant_name}"


def _get_token(path: pathlib.Path, tenant_name: str, cloud: str, shard_namespace: str):
    p: pathlib.Path = path
    if not p.is_absolute():
        raise ValueError("Path must be absolute")

    url = _get_support_url(shard_namespace, tenant_name, cloud)
    logging.info("Visit and Copy the token to the clipboard")
    logging.info("Genie permissions are required to get a token")
    logging.info("%s", url)

    secret = getpass.getpass("> ")
    if not secret:
        logging.error("No secret provided")
        sys.exit(1)

    if p.exists():
        if p.stat().st_mode & 0o777 != 0o600:
            logging.warning("Changing permissions of %s", p)
        p.chmod(0o600)
    else:
        p.touch(mode=0o600)  # to restrict access
    p.write_text(secret)
    logging.info("Wrote token to %s", p)


def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()

    parser.add_argument("--tenant-name", help="tenant name", required=True)
    parser.add_argument(
        "--cloud",
        help="The cloud to use",
        choices=_get_cloud_info().keys(),
        required=True,
    )
    parser.add_argument(
        "--shard-namespace",
        help="The namespace of the tenant",
        required=True,
    )
    parser.add_argument(
        "--path",
        help="relative path name",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".augment/service_token.txt"),
    )
    parser.set_defaults(action=None)

    args = parser.parse_args()
    _get_token(
        path=args.path,
        tenant_name=args.tenant_name,
        cloud=args.cloud,
        shard_namespace=args.shard_namespace,
    )


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        sys.exit(1)

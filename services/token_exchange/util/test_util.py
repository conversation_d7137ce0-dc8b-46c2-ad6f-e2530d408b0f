"""CLI utility to generate jwts for testing."""

import base64
import jwt
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec
from pathlib import Path
import time
from cryptography.hazmat.backends import default_backend


def generate_jwt(private_key_path: str, key_id: str, test_data: dict) -> bytes:
    private_key = Path(private_key_path).read_text()
    payload = {
        "iat": int(time.time()),
        "exp": int(time.time()) + (60 * 60 * 24 * 365 * 100),
        "iss": "test_util.py",
        **test_data,
    }
    return jwt.encode(payload, private_key, algorithm="ES256", headers={"kid": key_id})


def generate_jwks(public_key_path: str, key_id: str) -> dict:
    public_key = serialization.load_pem_public_key(
        Path(public_key_path).read_bytes(), backend=default_backend()
    )

    if isinstance(public_key, ec.EllipticCurvePublicKey):
        curve_map = {
            ec.SECP256R1: "P-256",
            ec.SECP384R1: "P-384",
            ec.SECP521R1: "P-521",
        }
        crv = curve_map[type(public_key.curve)]
        x, y = public_key.public_numbers().x, public_key.public_numbers().y
        return {
            "keys": [
                {
                    "kty": "EC",
                    "kid": key_id,
                    "use": "sig",
                    "alg": "ES256",
                    "crv": crv,
                    "x": base64.urlsafe_b64encode(
                        x.to_bytes((x.bit_length() + 7) // 8, "big")
                    )
                    .decode("utf-8")
                    .rstrip("="),
                    "y": base64.urlsafe_b64encode(
                        y.to_bytes((y.bit_length() + 7) // 8, "big")
                    )
                    .decode("utf-8")
                    .rstrip("="),
                }
            ]
        }
    else:
        raise ValueError("Unsupported key type. Only EC keys are supported.")


tests = [
    {
        "test_name": "service_token_auth_test.py",
        "key_id": "fake_kid",
        "private_key_path": "services/lib/grpc/auth/test_data/ec256-private.pem",  # pragma: allowlist secret
        "public_key_path": "services/lib/grpc/auth/test_data/ec256-public.pem",  # pragma: allowlist secret
        "test_data": {
            "user_id": "123",
            "service_name": "",
            "tenant_id": "dev-augie",
            "shard_namespace": "unit_test_shard",
            "cloud": "unit_test_cloud",
        },
    },
    {
        "test_name": "service_token_auth_test.py - second kid",
        "key_id": "fake_kid_2",
        "private_key_path": "services/lib/grpc/auth/test_data/ec256-private.pem",  # pragma: allowlist secret
        "public_key_path": "services/lib/grpc/auth/test_data/ec256-public.pem",  # pragma: allowlist secret
        "test_data": {
            "user_id": "123",
            "service_name": "",
            "tenant_id": "dev-augie",
            "shard_namespace": "unit_test_shard",
            "cloud": "unit_test_cloud",
        },
    },
    {
        "test_name": "token_exchange_client.rs - user",
        "key_id": "fake_kid",
        "private_key_path": "services/lib/grpc/auth/test_data/ec256-private.pem",  # pragma: allowlist secret
        "public_key_path": "services/lib/grpc/auth/test_data/ec256-public.pem",  # pragma: allowlist secret
        "test_data": {
            "user_id": "123",
            "service_name": "",
            "tenant_id": "dev-augie",
            "shard_namespace": "unit_test_shard",
            "cloud": "unit_test_cloud",
        },
    },
    {
        "test_name": "token_exchange_client.rs - service",
        "key_id": "fake_kid",
        "private_key_path": "services/lib/grpc/auth/test_data/ec256-private.pem",  # pragma: allowlist secret
        "public_key_path": "services/lib/grpc/auth/test_data/ec256-public.pem",  # pragma: allowlist secret
        "test_data": {
            "user_id": "",
            "service_name": "embedding-indexer-starethanol6-16-1-proj512-svc",
            "tenant_id": "dev-augie",
            "shard_namespace": "unit_test_shard",
            "cloud": "unit_test_cloud",
        },
    },
]


if __name__ == "__main__":
    for test in tests:
        print(f"Generating values for test {test['test_name']}")
        key_id = test["key_id"]
        private_key_path = test["private_key_path"]
        public_key_path = test["public_key_path"]
        test_data = test["test_data"]

        jwt_token = generate_jwt(private_key_path, key_id, test_data)
        print(f"\tGenerated JWT: {jwt_token}")
        jwks = generate_jwks(public_key_path, key_id)
        jwks_str = str(jwks).replace("'", '"')
        print(f"\tGenerated JWKS: {jwks_str}")
        print("\n\n")

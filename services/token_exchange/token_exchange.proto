syntax = "proto3";
package token_exchange;

import "google/protobuf/duration.proto";
import "google/protobuf/struct.proto";
import "services/auth/central/server/auth_entities.proto";

option go_package = "github.com/augmentcode/augment/services/token_exchange/proto";

// scope defines the permissions that a token can have.
// usually the ultimately map to bigtable tables, but they also impact accesses to any
// in-memory caches.
enum Scope {
  // read access to the auth data, e.g. user accountsdata
  AUTH_R = 0;

  // read/write access to the user data and tenant management
  AUTH_RW = 1;

  // read access to the requests data, in particular all request insight api
  // This is for the legacy support site. The new support site uses REQUEST_CONFIDENTIAL_R and
  // REQUEST_RESTRICTED_R.
  REQUEST_R = 2;

  // read/write access to the requests data
  // note only needed for request insight internal subscription handlers. Every service
  // is allowed to emit events to the request insight pubsub topic.
  REQUEST_RW = 3;

  // read to the content data, e.g. all content manager GRPC and content manager table
  CONTENT_R = 4;

  // read/write access to the content data, e.g. all content manager GRPC and content manager table
  CONTENT_RW = 5;

  // full access to all content data including the user index via the GetUserBlobs rpc
  CONTENT_ADMIN = 8;

  // read access to the settings data
  SETTINGS_R = 6;

  // read/write access to the settings data
  SETTINGS_RW = 7;

  // Read access to confidential request data. This is for use by the support site, and does not
  // require Genie permissions to grant.
  REQUEST_CONFIDENTIAL_R = 9;

  // Read access to restricted request data (including PII). This is for use by the support site,
  // and requires Genie permissions.
  REQUEST_RESTRICTED_R = 10;

  // Read/write access to restricted request data (including PII).
  // This is for use by the proto exporter.
  REQUEST_RESTRICTED_RW = 11;

  // Delete access to bigtable rows.
  // only given out to IAP users with full access
  BIGTABLE_DELETE = 12;

  reserved 13;
}

// This service lives in central namespaces and hands out JWT tokens that can be
// used by requests in any shard to gain access to other microservices in our
// backend. These tokens are generally short lived, and will have to be
// rerequested regularly.
//
// This service does not do any authentication of external requests - it mainly
// checks that the requesting service has access to the given tenant/namespace,
// and hands out tokens accordingly.
service TokenExchange {
  // Takes a user ID and returns a signed JWT that can be used by other services
  // to access internal resources for that user request.
  rpc GetSignedTokenForUser(GetSignedTokenForUserRequest) returns (GetSignedTokenForUserResponse) {}

  // Returns a signed JWT for use by internal services, for example to run
  // background work.
  rpc GetSignedTokenForService(GetSignedTokenForServiceRequest) returns (GetSignedTokenForServiceResponse) {}

  // Exchange an IAP token for a signed JWT.
  rpc GetSignedTokenForIAPToken(GetSignedTokenForIAPTokenRequest) returns (GetSignedTokenForIAPTokenResponse) {}

  // Returns a public key that other services can use to verify JWTs.
  rpc GetVerificationKey(GetVerificationKeyRequest) returns (GetVerificationKeyResponse) {}
}

message GetSignedTokenForUserRequest {
  // User ID is ambiguous and will be deprecated at some point in favor of
  // the explicit opaque_user_id and user_email fields. For now, we keep it
  // around for backwards compatibility.
  string user_id = 1;
  auth_entities.UserId opaque_user_id = 6;
  // Optional because not all users have an email.
  optional string user_email = 5;

  string tenant_id = 2;
  string shard_namespace = 3;

  // Allows custom claims (schemaless) to be added to the token.
  google.protobuf.Struct additional_claims = 4;

  // CONTENT_RW & SETTINGS_RW are the default scopes if none are provided for backwards compatibility
  repeated Scope scopes = 7;
}

message GetSignedTokenForUserResponse {
  string signed_token = 1 [debug_redact = true];
}

message GetSignedTokenForServiceRequest {
  // The service name is automatically obtained from the client's certificate
  string tenant_id = 1;
  string shard_namespace = 2;

  // permissions requested
  repeated Scope scopes = 3;
}

message GetSignedTokenForServiceResponse {
  string signed_token = 1 [debug_redact = true];
}

message GetSignedTokenForIAPTokenRequest {
  // the iap token that should be exchanged
  string iap_token = 1 [debug_redact = true];

  // the shard namespace that the token should be valid for.
  // usually this has to be the namespace as the caller.
  //
  // doesn't have to be set if the token is for a central service
  string shard_namespace = 2;

  // the tenant id that the token should be valid for
  string tenant_id = 5;

  // requested expiration of the token.
  //
  // if not set, the default expiration will be used
  // if larger then a service-specific expiration, the service-specific expiration will be used
  google.protobuf.Duration expiration = 4;

  // permissions requested
  repeated Scope scopes = 3;
}

message GetSignedTokenForIAPTokenResponse {
  string signed_token = 1 [debug_redact = true];
}

message GetVerificationKeyRequest {}

message GetVerificationKeyResponse {
  // The JWKS, as JSON in bytes
  bytes jwks = 1;
}

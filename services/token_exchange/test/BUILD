load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_multi")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

kubecfg_multi(
    name = "test_kubecfg",
    deps = [
        "//services/deploy:shard_namespace_base_kubecfg",
        "//services/deploy:tenant_config_kubecfg",
        "//services/request_insight:core_kubecfg",
        "//services/tenant_watcher/server:kubecfg",
        "//services/token_exchange/server:kubecfg",
        "//tools/listen_cert_rotate:kubecfg",
    ],
)

pytest_test(
    name = "token_exchange_server_test",
    size = "large",  # this is an E2E test so, it is large by definition
    srcs = [
        "conftest.py",
        "token_exchange_server_test.py",
    ],
    data = [
        "test-defaults.jsonnet",
        ":test_kubecfg",
        "@k8s_binary//file:kubectl",
    ],
    tags = [
        "exclusive",
        "gcp",
        "postmerge-test",
        "system-test",
    ],
    deps = [
        "//base/python/k8s_test_helper",
        "//base/python/k8s_test_helper:k8s_resource",
        "//services/auth/central/server:auth_entities_py_proto",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/tenant_watcher/client",
        "//services/token_exchange/client:client_py",
        requirement("kubernetes"),
        requirement("pydantic"),
    ],
)

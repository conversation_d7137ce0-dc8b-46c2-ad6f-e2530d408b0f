# Token exchange

The token_exchange server is the main service that provides JWTs and keys for
verifying JWTs.

These JWTs are used for authentication within our systems only, and not
externally. External requests use a different mechanism - API tokens, GCP IAP,
etc. Once the external authentication is complete, most services will exchange
that for a JWT. This JWT is verified by most other services, and includes the
tenant ID this request is allowed to access, in addition to a scope of data it
is allowed to access.

The most common example of this is for a completion request, which comes in with
an API token. At time of writing this, auth-query validates that token, then
obtains a JWT for the tenant and namespace that that token's user belongs to.
That token is then returned to api-proxy, which forwards it along with the
request to the completion host. This token is carried forward and validated by
other services in the system, like content-manager.

These JWTs are one of our main protections in a multi-tenant system, to ensure
security of customer data.

## Security

The token exchange server is secured using mTLS, besides the dev cluster, where
mTLS is optional. The JWTs are signed using private keys created by
cert-manager.

## Persistence

Keys are stored in k8s secrets, created by cert-manager.

## Components

### Server

The token exchange server is a gRPC server that provides signed JWTs and keys
that can be used to verify those JWTs.

This service will run in the central namespace of every cluster.

mTLS certificates are used to ensure the identity of anyone asking for a JWT or
a verification key. In addition, only auth-query is (currently) authorized to
receive JWTs.

Right now there is a rudimentary form of key rotation, where cert-manager
handles key rotation and token_exchange uses the key it read most recently.
There is some disruption to clients though, who have to request new verification
keys from token_exchange if they see verification errors.
TODO: Have token_exchange return multiple verification keys, which should be
easy since we already use the JWKS format, to allow clients to handle rotation
more smoothly.

### Client libraries

There are a few client libraries implemented here, for convenience.

## Interface

see token_exchange.proto for the GRPC protocol definition.

the stub code for typescript is generated by ts_proto_library
and checked in. A test is failing when the generated code is not up to date.
Run `bazel run @@//services/token_exchange:token_exchange_ts_proto.copy` to update the generated code.

## Links

- see https://www.notion.so/Service-to-Service-auth-in-Shard-Architecture-f626a6a379044ebc9bc74597a94883f1

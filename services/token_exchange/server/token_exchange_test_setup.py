"""Local Token Exchange server test setup for e2e tests.

This file provides helper code for starting a local instance of the
token exchange server.

NOTE: If you are writing integration tests which work off of a token
exchange server that is NOT deployed locally, this code is probably NOT
what you're looking for. See `services/content_manager/test/conftest.py`
for an example of getting a client with Support permissions to a
token exchange server that is in a test kubernetes namespace.
"""

import json
import os
import tempfile
from dataclasses import asdict, dataclass, field
from typing import Generator, Optional

import grpc
import pydantic
from python.runfiles import Runfiles

from base.test_utils import process
from services.lib.request_context.request_context import RequestContext
from services.lib.grpc.tls_config import tls_config
from services.token_exchange import token_exchange_pb2, token_exchange_pb2_grpc


class TokenExchangeServer:
    """Token exchange server."""

    def __init__(self, port: int):
        self.port = port

        channel = grpc.insecure_channel("127.0.0.1:%d" % port)
        self.stub = token_exchange_pb2_grpc.TokenExchangeStub(channel)

    def create_test_request_context(
        self,
        tenant_id: str,
        namespace: str,
        scopes: list[token_exchange_pb2.Scope.ValueType],
    ) -> RequestContext:
        """For tests to get a context with a service jwt."""
        resp = self.stub.GetSignedTokenForService(
            token_exchange_pb2.GetSignedTokenForServiceRequest(
                tenant_id=tenant_id,
                shard_namespace=namespace,
                scopes=scopes,
            )
        )
        token = pydantic.SecretStr(resp.signed_token)
        context = RequestContext.create(auth_token=token)
        print(f"created test RequestContext with request_id {context.request_id}")
        return context


@dataclass(frozen=True)
class TokenExchangeKeyConfig:
    algorithm: str
    key_path: str
    cert_path: str


@dataclass
class TokenForServiceConfig:
    regex: str
    scopes: list[str]
    expiration: str


@dataclass
class TokenForUserConfig:
    regex: str
    scopes: list[str]


@dataclass
class TokenExchangeConfig:
    port: int
    tenant_watcher_endpoint: str
    jwt_signing_key: TokenExchangeKeyConfig = TokenExchangeKeyConfig(
        algorithm="ES256",
        key_path="_main/services/token_exchange/server/test_data/ec256-cert-private.pem",
        cert_path="_main/services/token_exchange/server/test_data/ec256-cert-public.pem",
    )

    # Put in some test defaults
    prom_port: int = 0  # pick any port
    server_mtls: Optional[tls_config.ServerConfig] = None
    client_mtls: Optional[tls_config.ClientConfig] = None
    skip_peer_validation: bool = True
    user_token_expiration: str = "1m"
    service_token_configs: list[TokenForServiceConfig] = field(default_factory=list)
    user_token_configs: list[TokenForUserConfig] = field(default_factory=list)
    iap_token_expiration: str = "1m"
    iap_token_max_expiration: str = "1h"
    iap_audience_prefix: Optional[list[str]] = None
    iap_token_allowed_regexp: str = ".*"
    acl_namespace: str = "fake"
    fake_acl_namespace: bool = True
    read_keys_from_config_map: bool = False
    config_map_name: str = "token-exchange-central-jwks-configmap"


def start_token_exchange_server(
    config: TokenExchangeConfig, runfiles: Runfiles | None = None
) -> Generator[TokenExchangeServer, None, None]:
    """Fixture to start the token exchange server."""
    if runfiles is None:
        runfiles = Runfiles.Create()

    assert runfiles is not None

    key_path = runfiles.Rlocation(config.jwt_signing_key.key_path)
    cert_path = runfiles.Rlocation(config.jwt_signing_key.cert_path)

    if key_path is None:
        raise ValueError(
            f"Could not locate key file: {config.jwt_signing_key.key_path}"
        )
    if cert_path is None:
        raise ValueError(
            f"Could not locate cert file: {config.jwt_signing_key.cert_path}"
        )

    config.jwt_signing_key = TokenExchangeKeyConfig(
        algorithm="ES256",
        key_path=key_path,
        cert_path=cert_path,
    )

    config_content = json.dumps(asdict(config))

    with tempfile.NamedTemporaryFile(mode="w+t") as config_file:
        config_file.write(config_content)
        config_file.flush()
        env = os.environ.copy()
        env["POD_NAMESPACE"] = "test-namespace"

        server_path = runfiles.Rlocation(
            "_main/services/token_exchange/server/token_exchange_server_/token_exchange_server"
        )
        if server_path is None:
            raise ValueError("Could not locate token exchange server binary")

        with process.ServerManager(
            [
                server_path,
                "-config",
                config_file.name,
            ],
            env=env,
            redirect_stderr=True,
        ) as p:
            m = process.wait_for_line(
                p.stdout,
                r"Listening on \[::\]:(\d+)",
                timeout_secs=30,
            )
            p.detach_stdout()
            port = int(m.group(1))
            yield TokenExchangeServer(port)

package main

import (
	"context"
	"slices"
	"testing"

	"github.com/rs/zerolog/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	tokenproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

func TestNoopAclCheck(t *testing.T) {
	t.Run("noop", func(t *testing.T) {
		check := &NoopAclCheck{}
		err := check.Check(context.Background(), "test-user", "test-tenant", "test-namespace",
			[]string{"REQUEST_R"})
		if err != nil {
			t.Error("The noop check should not return an error.")
		}
	})
}

func TestInformerAclCheck(t *testing.T) {
	t.Run("no acls", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		check := &InformerAclCheck{
			informer: informer,
		}
		err := check.Check(context.Background(), "test-user", "test-tenant", "test-namespace",
			[]string{"REQUEST_R"})
		if err == nil {
			t.Error("The informer check should return an error.")
		}
	})
	t.Run("matching acl", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "test-tenant",
				ExpiresAt: "2040-01-01T00:00:00Z", // not expired
				Scope:     "requests",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_R"})
		if err != nil {
			log.Error().Err(err).Msg("Error checking acl")
			t.Error("The informer check should not return an error.")
		}
	})
	t.Run("matching acl full", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "",
				ExpiresAt: "2040-01-01T00:00:00Z", // not expired
				Scope:     "full",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_R"})
		if err != nil {
			log.Error().Err(err).Msg("Error checking acl")
			t.Error("The informer check should not return an error.")
		}
	})
	t.Run("wrong scoped acl", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "test-tenant",
				ExpiresAt: "2040-01-01T00:00:00Z", // not expired
				Scope:     "users",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_R"})
		if err == nil {
			t.Error("The informer check should return an error.")
		}
	})
	t.Run("no scope acl", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "test-tenant",
				ExpiresAt: "2040-01-01T00:00:00Z", // not expired
				Scope:     "users",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{})
		if err != nil {
			t.Error("The informer check should not return an error.")
		}
	})

	t.Run("full scope acl", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "test-tenant",
				ExpiresAt: "2040-01-01T00:00:00Z", // not expired
				Scope:     "full",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_RW", "CONTENT_RW"})
		if err != nil {
			t.Error("The informer check should not return an error.")
		}
	})
	t.Run("expired acl", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "test-tenant",
				ExpiresAt: "2020-01-01T00:00:00Z", // expired
				Scope:     "requests",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_R"})
		if err == nil {
			t.Error("The informer should return an error.")
		}
	})
	// if the spec if for a central namespace, any tenant id should work
	t.Run("central namespace", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:       "test-user",
				ExpiresAt:      "2040-01-01T00:00:00Z", // not expired
				Scope:          "full",
				NamespaceScope: "*",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "central",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer:            informer,
			trustedAclNamespace: "central",
		}
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_R"})
		if err != nil {
			t.Error("The informer check should not return an error.")
		}
	})
}

func TestCheckCentral(t *testing.T) {
	t.Run("REQUEST_CONFIDENTIAL_R", func(t *testing.T) {
		check := &InformerAclCheck{}
		err := check.CheckCentral(context.Background(), []string{"REQUEST_CONFIDENTIAL_R"})
		if err != nil {
			t.Error("The informer check should not return an error.")
		}
	})

	t.Run("invalid scope", func(t *testing.T) {
		check := &InformerAclCheck{}
		err := check.CheckCentral(context.Background(), []string{"REQUEST_R"})
		if err == nil {
			t.Error("The informer check should return an error.")
		}
	})
}

func TestRequestConfidentialRHandling(t *testing.T) {
	t.Run("REQUEST_CONFIDENTIAL_R is always granted", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		// No ACLs are added to the informer
		check := &InformerAclCheck{
			informer: informer,
		}
		// Even though there are no ACLs, REQUEST_CONFIDENTIAL_R should be granted
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_CONFIDENTIAL_R"})
		if err != nil {
			t.Error("REQUEST_CONFIDENTIAL_R should be granted without ACL")
		}
	})

	t.Run("REQUEST_CONFIDENTIAL_R is removed from scope list", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		// No ACLs are added to the informer
		check := &InformerAclCheck{
			informer: informer,
		}
		// When REQUEST_CONFIDENTIAL_R is combined with other scopes that require ACL,
		// it should be removed from the list and the other scopes should be checked
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_CONFIDENTIAL_R", "REQUEST_R"})
		if err == nil {
			t.Error("Should return error for REQUEST_R when no ACL exists")
		}
	})

	t.Run("REQUEST_CONFIDENTIAL_R with valid ACL for other scopes", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "test-tenant",
				ExpiresAt: "2040-01-01T00:00:00Z", // not expired
				Scope:     "requests",
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		// When REQUEST_CONFIDENTIAL_R is combined with other scopes that have valid ACL,
		// the request should succeed
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_CONFIDENTIAL_R", "REQUEST_R"})
		if err != nil {
			t.Error("Should not return error when REQUEST_CONFIDENTIAL_R is combined with valid scopes")
		}
	})

	t.Run("empty scopes after removing REQUEST_CONFIDENTIAL_R", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		// No ACLs are added to the informer
		check := &InformerAclCheck{
			informer: informer,
		}
		// When only REQUEST_CONFIDENTIAL_R is requested, after removing it the scope list is empty
		// and no ACL check is needed
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"REQUEST_CONFIDENTIAL_R"})
		if err != nil {
			t.Error("Should not return error when only REQUEST_CONFIDENTIAL_R is requested")
		}
	})

	t.Run("IAM request with empty tenant and namespace", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		// No ACLs are added to the informer
		check := &InformerAclCheck{
			informer: informer,
		}
		// IAM requests with empty tenant ID and shard namespace should be allowed for REQUEST_CONFIDENTIAL_R
		err := check.Check(context.Background(), "<EMAIL>", "", "",
			[]string{"REQUEST_CONFIDENTIAL_R"})
		if err != nil {
			t.Error("IAM request with empty tenant and namespace should be allowed for REQUEST_CONFIDENTIAL_R")
		}
	})

	t.Run("IAM request with empty tenant and namespace but other scopes", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		// No ACLs are added to the informer
		check := &InformerAclCheck{
			informer: informer,
		}
		// IAM requests with empty tenant ID and shard namespace should not be allowed for other scopes
		err := check.Check(context.Background(), "<EMAIL>", "", "",
			[]string{"REQUEST_CONFIDENTIAL_R", "REQUEST_R"})
		if err == nil {
			t.Error("IAM request with empty tenant and namespace should not be allowed for scopes other than REQUEST_CONFIDENTIAL_R")
		}
	})
}

// Test token scopes functionality
func TestTokenScopes(t *testing.T) {
	t.Run("token scopes", func(t *testing.T) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:    "test-user",
				TenantID:    "test-tenant",
				ExpiresAt:   "2040-01-01T00:00:00Z",          // not expired
				TokenScopes: []string{"AUTH_R", "CONTENT_R"}, // But we're overriding with token scopes
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}

		// Should allow AUTH_R because it's in the token scopes
		err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"AUTH_R"})
		if err != nil {
			t.Error("The informer check should not return an error for token scope AUTH_R.")
		}

		// Should allow CONTENT_R because it's in the token scopes
		err = check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"CONTENT_R"})
		if err != nil {
			t.Error("The informer check should not return an error for token scope CONTENT_R.")
		}

		// Should NOT allow AUTH_RW because it's not in the token scopes
		err = check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
			[]string{"AUTH_RW"})
		if err == nil {
			t.Error("The informer check should return an error for AUTH_RW which is not in token scopes.")
		}
	})
}

// Verify that certain scopes only work if the user has full access
func TestAccessScopes(t *testing.T) {
	helper := func(t *testing.T, accessScope string, allowedScopes []tokenproto.Scope) {
		informer := NewFakeSupportUIAccessInformer()
		acl := SupportUIAccess{
			Spec: SupportUIAccessSpec{
				UserName:  "test-user",
				TenantID:  "test-tenant",
				ExpiresAt: "2040-01-01T00:00:00Z", // not expired
				Scope:     accessScope,
			},
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "test-namespace",
			},
		}
		informer.Update(acl)
		check := &InformerAclCheck{
			informer: informer,
		}
		for scope, scopeValue := range tokenproto.Scope_value {
			err := check.Check(context.Background(), "<EMAIL>", "test-tenant", "test-namespace",
				[]string{scope})
			if slices.Contains(allowedScopes, tokenproto.Scope(scopeValue)) {
				if err != nil {
					t.Errorf("The informer check should not return an error for scope %s", scope)
				}
			} else {
				if err == nil {
					t.Errorf("The informer check should return an error for scope %s", scope)
				}
			}
		}
	}

	// All scopes work with full access
	t.Run("full", func(t *testing.T) {
		allScopes := make([]tokenproto.Scope, 0, len(tokenproto.Scope_value))
		for _, scope := range tokenproto.Scope_value {
			allScopes = append(allScopes, tokenproto.Scope(scope))
		}
		helper(t, "full", allScopes)
	})

	// Requests scope should only give basic access to requests and content
	t.Run("requests", func(t *testing.T) {
		helper(t, "requests", []tokenproto.Scope{
			tokenproto.Scope_REQUEST_R,
			tokenproto.Scope_REQUEST_RESTRICTED_R,
			tokenproto.Scope_REQUEST_CONFIDENTIAL_R,
			tokenproto.Scope_CONTENT_R,
			tokenproto.Scope_CONTENT_RW,
		})
	})

	// Users scope should only give basic access to auth
	t.Run("users", func(t *testing.T) {
		helper(t, "users", []tokenproto.Scope{
			tokenproto.Scope_AUTH_R,
			tokenproto.Scope_AUTH_RW,
			tokenproto.Scope_REQUEST_CONFIDENTIAL_R,
		})
	})
}

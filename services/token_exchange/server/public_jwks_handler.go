package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"

	"github.com/MicahParks/jwkset"
	"github.com/rs/zerolog/log"
	corev1 "k8s.io/api/core/v1"
	k8serror "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
)

type publicJwksHandler interface {
	// Return all the possible verification keys, as a JWKS object
	getAllPublicJwks() json.RawMessage
}

type publicJwksHandlerImpl struct {
	configMapName     string
	clientset         *kubernetes.Clientset
	namespace         string
	currentPublicJwks json.RawMessage

	jwkLock          sync.RWMutex
	mergedPublicJwks json.RawMessage
}

// Get the publicJwks.json from the ConfigMap.
func (pjh *publicJwksHandlerImpl) getAllPublicJwks() json.RawMessage {
	pjh.jwkLock.RLock()
	defer pjh.jwkLock.RUnlock()

	return pjh.mergedPublicJwks
}

func mergePublicJwks(configmapJwks json.RawMessage, currentJwks json.RawMessage) (json.RawMessage, error) {
	var configmapJwksMarshal jwkset.JWKSMarshal
	err := json.Unmarshal(configmapJwks, &configmapJwksMarshal)
	if err != nil {
		return nil, err
	}
	configmapNumKeys := len(configmapJwksMarshal.Keys)

	var currentJwksMarshal jwkset.JWKSMarshal
	err = json.Unmarshal(currentJwks, &currentJwksMarshal)
	if err != nil {
		return nil, err
	}
	currentJwksNumKeys := len(currentJwksMarshal.Keys)

	allPublicJwks := jwkset.JWKSMarshal{
		Keys: append(configmapJwksMarshal.Keys, currentJwksMarshal.Keys...),
	}
	numKeys := len(allPublicJwks.Keys)
	log.Info().Msgf("Merged %d keys from configmap with %d current keys into %d keys", configmapNumKeys, currentJwksNumKeys, numKeys)

	// Using storage to convert the JWKSMarshal to set of JWKs for deduplication
	storage, err := allPublicJwks.ToStorage()
	if err != nil {
		return nil, err
	}

	allPublicJwksJson, err := storage.JSONPublic(context.Background())
	if err != nil {
		return nil, err
	}

	return allPublicJwksJson, nil
}

func (pjh *publicJwksHandlerImpl) readConfigmap() (json.RawMessage, error) {
	namespace := pjh.namespace
	configMapName := pjh.configMapName
	log.Info().Msgf("Getting publicJwks.json from ConfigMap %s/%s", namespace, configMapName)

	cm, err := pjh.clientset.CoreV1().ConfigMaps(namespace).Get(context.Background(), configMapName, metav1.GetOptions{})
	if err == nil {
		if _, ok := cm.Data["publicJwks.json"]; !ok {
			log.Fatal().Msgf("ConfigMap %s/%s does not contain key publicJwks.json\n", namespace, configMapName)
		}
		return json.RawMessage([]byte(cm.Data["publicJwks.json"])), nil
	} else if k8serror.IsNotFound(err) {
		log.Info().Msgf("ConfigMap not found.")
		return json.RawMessage("{}"), nil
	} else {
		return nil, errors.New("Error getting ConfigMap")
	}
}

func (pjh *publicJwksHandlerImpl) tryUpdate() {
	publicJwkHistory, err := pjh.readConfigmap()
	if err != nil {
		log.Error().Err(err).Msg("Error reading ConfigMap")
		return
	}

	mergedPublicJwks, err := mergePublicJwks(publicJwkHistory, pjh.currentPublicJwks)
	if err != nil {
		log.Error().Err(err).Msg("Error merging publicJwks.json")
		return
	}

	pjh.jwkLock.Lock()
	defer pjh.jwkLock.Unlock()
	pjh.mergedPublicJwks = mergedPublicJwks
}

func (pjh *publicJwksHandlerImpl) HandleAdd(obj interface{}) {
	configmap, ok := obj.(*corev1.ConfigMap)
	if !ok {
		log.Error().Msg("Error casting object to ConfigMap")
		return
	}
	if configmap.Name != pjh.configMapName {
		log.Error().Msgf("Ignoring ConfigMap: %s/%s", configmap.Namespace, configmap.Name)
		return
	}
	log.Info().Msgf("ConfigMap added: %s/%s/%s", configmap.Namespace, configmap.Name, configmap.ResourceVersion)
	pjh.tryUpdate()
}

func (pjh *publicJwksHandlerImpl) HandleUpdate(oldObj, newObj interface{}) {
	configmap, ok := newObj.(*corev1.ConfigMap)
	if !ok {
		log.Error().Msg("Error casting object to ConfigMap")
		return
	}
	if configmap.Name != pjh.configMapName {
		log.Error().Msgf("Ignoring ConfigMap: %s/%s", configmap.Namespace, configmap.Name)
		return
	}
	log.Info().Msgf("ConfigMap updated: %s/%s/%s", configmap.Namespace, configmap.Name, configmap.ResourceVersion)
	pjh.tryUpdate()
}

func (pjh *publicJwksHandlerImpl) HandleDelete(obj interface{}) {
	// TODO: clean history on deletes?
}

func newPublicJwksHandler(config *TokenServerConfig, clientset *kubernetes.Clientset, namespace string, currentPublicJwks json.RawMessage) publicJwksHandler {
	handler := &publicJwksHandlerImpl{
		configMapName:     config.ConfigMapName,
		clientset:         clientset,
		namespace:         namespace,
		currentPublicJwks: currentPublicJwks,
		// Initialize this with the current public key
		mergedPublicJwks: currentPublicJwks,
	}
	if !config.ReadKeysFromConfigMap {
		return handler
	}

	factory := informers.NewSharedInformerFactoryWithOptions(clientset, 0,
		informers.WithNamespace(namespace),
		informers.WithTweakListOptions(func(opts *metav1.ListOptions) {
			opts.FieldSelector = fmt.Sprintf("metadata.name=%s", config.ConfigMapName)
		}))
	configmapInformer := factory.Core().V1().ConfigMaps()
	informer := configmapInformer.Informer()
	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    handler.HandleAdd,
		UpdateFunc: handler.HandleUpdate,
		DeleteFunc: handler.HandleDelete,
	})
	log.Info().Msg("Starting informer")
	// We don't actually use this stop channel
	stopCh := make(chan struct{})
	go informer.Run(stopCh)
	if !cache.WaitForCacheSync(stopCh, informer.HasSynced) {
		log.Fatal().Msg("error waiting for informer cache to sync")
	}
	log.Info().Msg("Synced informer")

	return handler
}

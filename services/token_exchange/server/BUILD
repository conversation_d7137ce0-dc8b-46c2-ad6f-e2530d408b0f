load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_library")

go_library(
    name = "token_exchange_server_lib",
    srcs = [
        "acl_check.go",
        "crd.go",
        "main.go",
        "public_jwks_handler.go",
        "token_exchange_server.go",
    ],
    data = [
        "//base/cloud/iap:public_iap_certs_json",
    ],
    importpath = "github.com/augmentcode/augment/services/token_exchange/server",
    visibility = ["//visibility:private"],
    deps = [
        "//base/cloud/iap:iap_go",
        "//base/go/durationutil",
        "//base/logging:logging_go",
        "//base/logging/audit:audit_go",
        "//base/tracing/go:tracing_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/recovery:grpc_recovery_go",
        "//services/lib/grpc/service:grpc_service_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/jwtkeyid:jwtkeyid_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/publisher:publisher_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_grpc_ecosystem_go_grpc_middleware_providers_prometheus//:prometheus",
        "@com_github_hashicorp_golang_lru_v2//expirable",
        "@com_github_micahparks_jwkset//:jwkset",
        "@com_github_micahparks_keyfunc_v3//:keyfunc",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@io_k8s_api//core/v1:core",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@io_k8s_apimachinery//pkg/apis/meta/v1/unstructured",
        "@io_k8s_apimachinery//pkg/runtime",
        "@io_k8s_apimachinery//pkg/runtime/schema",
        "@io_k8s_apimachinery//pkg/util/wait",
        "@io_k8s_apimachinery//pkg/watch",
        "@io_k8s_client_go//dynamic",
        "@io_k8s_client_go//dynamic/dynamicinformer",
        "@io_k8s_client_go//informers",
        "@io_k8s_client_go//kubernetes",
        "@io_k8s_client_go//rest",
        "@io_k8s_client_go//tools/cache",
        "@io_k8s_client_go//tools/clientcmd",
        "@io_k8s_client_go//tools/clientcmd/api",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//health",
        "@org_golang_google_grpc//health/grpc_health_v1",
        "@org_golang_google_grpc//peer",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_test(
    name = "token_exchange_server_test",
    srcs = [
        "acl_check_test.go",
        "public_jwks_handler_test.go",
        "token_exchange_server_test.go",
    ],
    data = glob(
        [
            "test_data/*",
        ],
    ),
    embed = [":token_exchange_server_lib"],
    deps = [
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@com_github_micahparks_jwkset//:jwkset",
        "@com_github_micahparks_keyfunc_v3//:keyfunc",
        "@org_golang_google_grpc//peer",
        "@org_golang_google_protobuf//types/known/durationpb",
    ],
)

go_binary(
    name = "token_exchange_server",
    embed = [":token_exchange_server_lib"],
    visibility = ["//visibility:public"],
)

py_library(
    name = "token_exchange_test_setup",
    testonly = True,
    srcs = [
        "token_exchange_test_setup.py",
    ],
    data = glob(
        [
            "test_data/*",
        ],
    ) + [
        ":token_exchange_server",
    ],
    pyright_extra_args = {
        "reportMissingParameterType": True,
    },
    visibility = ["//services:__subpackages__"],
    deps = [
        requirement("pydantic"),
        "//base/test_utils:process",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/token_exchange:token_exchange_py_proto",
        "@rules_python//python/runfiles",
    ],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":token_exchange_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/content_manager/test:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/inference_host/test:__subpackages__",
        "//services/memstore/test:__subpackages__",
        "//services/tenant_watcher/test:__subpackages__",
        "//services/token_exchange/test:__subpackages__",
    ],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
    ],
)

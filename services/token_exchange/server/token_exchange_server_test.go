package main

import (
	"bytes"
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"os"
	"reflect"
	"sort"
	"testing"
	"time"

	keyfunc "github.com/MicahParks/keyfunc/v3"
	"github.com/augmentcode/augment/base/cloud/iap"
	"github.com/augmentcode/augment/base/go/durationutil"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenproto "github.com/augmentcode/augment/services/token_exchange/proto"
	jwt "github.com/golang-jwt/jwt/v5"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/peer"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

func mustReadFile(t *testing.T, path string) []byte {
	t.Helper()
	b, err := os.ReadFile(path)
	if err != nil {
		t.Fatal(err)
	}
	return bytes.TrimSuffix(b, []byte("\n"))
}

func testServer(t *testing.T, use_extra_verification_keys bool) *tokenServer {
	tenantWatcherClient := &tenantclient.MockTenantWatcherClient{
		Tenants: []*tenantproto.Tenant{
			{
				Id:             "aug12345",
				Name:           "augie town",
				ShardNamespace: "dev-augie-shard",
				Config: &tenantproto.Config{
					Configs: map[string]string{},
				},
			},
			{
				Id:             "augie98765",
				Name:           "augie town",
				ShardNamespace: "dev-augie-shard",
				Config: &tenantproto.Config{
					Configs: map[string]string{},
				},
			},
			{
				Id:             "augiemoved",
				Name:           "augie across town",
				ShardNamespace: "dev-migrated1",
				OtherNamespace: "dev-migrated2",
				Config: &tenantproto.Config{
					Configs: map[string]string{},
				},
			},
		},
	}

	config := &TokenServerConfig{
		UserTokenExpiration: durationutil.JSONDuration(time.Hour),
		CentralNamespace:    "central-test",
		CentralCloud:        "test-cloud",
		ServiceTokenConfigs: []TokenForServiceConfig{
			{
				Regex:      ".*",
				Scopes:     []string{"CONTENT_RW", "CONTENT_R"},
				Expiration: durationutil.JSONDuration(time.Minute * 60),
			},
		},
		UserTokenConfigs: []TokenForUserConfig{
			{
				Regex:  "^limited-scope-svc$",
				Scopes: []string{"CONTENT_RW", "REQUEST_R"},
			},
			{
				Regex:  "^customer-ui-svc$",
				Scopes: []string{"CONTENT_RW", "SETTINGS_RW", "CONTENT_R", "SETTINGS_R"},
			},
			{
				Regex: "^auth-query-svc$",
				// I don't care about having the minimum set of scopes here (it's a test)
				Scopes: []string{"CONTENT_RW", "CONTENT_R", "REQUEST_R", "REQUEST_RW", "AUTH_R", "AUTH_RW", "SETTINGS_R", "SETTINGS_RW"},
			},
		},
		AllowedCrossNamespacePeers: []string{
			"^slack-bot-webhook-grpc-svc$",
			"^customer-ui-svc$", // Add customer-ui-svc to allowed cross-namespace peers
		},
		IapTokenExpiration: durationutil.JSONDuration(time.Hour),
		JwtSigningKey: TokenServerKeyConfig{
			Algorithm: "ES256",
			KeyPath:   "test_data/ec256-cert-private.pem",
			CertPath:  "test_data/ec256-cert-public.pem",
		},
		ShouldCacheTokens:      true,
		UserCacheExpiration:    durationutil.JSONDuration(time.Minute * 10),
		ServiceCacheExpiration: durationutil.JSONDuration(time.Minute * 10),
		MaxCacheKeys:           10000,
		ReadKeysFromConfigMap:  false,
		ConfigMapName:          "token-exchange-central-jwks-configmap",
	}

	if use_extra_verification_keys {
		config.ExtraJwtVerificationKeys = []TokenServerKeyConfig{
			{
				Algorithm: "ES256",
				KeyPath:   "test_data/ec256-cert-private2.pem",
				CertPath:  "test_data/ec256-cert-public2.pem",
			},
		}
	}

	tenantWatcher, tenantUpdateCh := tenantclient.NewTestTenantCache(t, tenantWatcherClient, "dev-augie-shard")
	// Wait for the first and only update
	<-tenantUpdateCh

	s, err := newServer(config, "test-namespace", nil, tenantWatcher, &NoopAclCheck{}, ripublisher.NewRequestInsightPublisherMock())
	if err != nil {
		t.Fatal(err)
	}

	s.iapVerifier = &iap.MockIapVerifier{
		Email: "<EMAIL>",
	}

	return s
}

func TestGetSignedTokenForUser(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()
	testGetSignedTokenForUserHelper(t, s)
}

func buildRequestContext(dnsNames []string) context.Context {
	return peer.NewContext(context.Background(), &peer.Peer{
		AuthInfo: credentials.TLSInfo{
			State: tls.ConnectionState{
				PeerCertificates: []*x509.Certificate{
					{
						DNSNames: dnsNames,
					},
				},
			},
		},
	})
}

func getClaims(t *testing.T, s *tokenServer, signedToken string) *auth.AugmentClaims {
	token, err := jwt.ParseWithClaims(signedToken, &auth.AugmentClaims{}, func(token *jwt.Token) (any, error) {
		verificationKey, err := s.GetVerificationKey(context.Background(), &tokenproto.GetVerificationKeyRequest{})
		if err != nil {
			return nil, err
		}
		t.Log(verificationKey)
		keyfunc, err := keyfunc.NewJWKSetJSON(verificationKey.Jwks)
		if err != nil {
			return nil, err
		}
		return keyfunc.Keyfunc(token)
	})
	if err != nil {
		t.Fatal(err)
	}
	if claims, ok := token.Claims.(*auth.AugmentClaims); ok {
		return claims
	} else {
		t.Fatalf("unexpected claims: %v", token.Claims)
		return nil
	}
}

var authQueryContext = buildRequestContext([]string{"auth-query-svc", "auth-query-svc.dev-augie-shard"})

func getTokenForUser(t *testing.T, s *tokenServer, req *tokenproto.GetSignedTokenForUserRequest, ctx context.Context) (string, *auth.AugmentClaims) {
	resp, err := s.GetSignedTokenForUser(ctx, req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.SignedToken)

	claims := getClaims(t, s, resp.SignedToken)
	return resp.SignedToken, claims
}

func testGetSignedTokenForUserHelper(t *testing.T, s *tokenServer) string {
	userEmail := "<EMAIL>"
	req := &tokenproto.GetSignedTokenForUserRequest{
		UserId:         "123",
		OpaqueUserId:   &authentitiesproto.UserId{UserId: "123", UserIdType: authentitiesproto.UserId_AUGMENT},
		UserEmail:      &userEmail,
		TenantId:       "aug12345",
		ShardNamespace: "dev-augie-shard",
	}
	signedToken, claims := getTokenForUser(t, s, req, authQueryContext)

	now := time.Now()
	if claims.UserID != "123" {
		t.Fatalf("unexpected user id: %v", claims.UserID)
	}
	if claims.ServiceName != "" {
		t.Fatalf("unexpected service name: %v", claims.ServiceName)
	}
	if claims.TenantID != "aug12345" {
		t.Fatalf("unexpected tenant name: %v", claims.TenantID)
	}
	if claims.ShardNamespace != "dev-augie-shard" {
		t.Fatalf("unexpected shard namespace: %v", claims.ShardNamespace)
	}
	if claims.TenantName != "augie town" {
		t.Fatalf("unexpected tenant name: %v", claims.TenantName)
	}
	issuedAt, err := claims.GetIssuedAt()
	if err != nil {
		t.Fatal(err)
	}
	expiresAt, err := claims.GetExpirationTime()
	if err != nil {
		t.Fatal(err)
	}
	// The token's timestamp doesn't have so much precision, so just check that it is around "now"
	if now.Sub(issuedAt.Time) > time.Minute {
		t.Fatalf("issued at timestamp %v is far from now %v", issuedAt.Time, now)
	}
	if now.After(expiresAt.Time) {
		t.Fatalf("expiration is in the past: %v", expiresAt.Time)
	}
	if expiration := expiresAt.Time.Sub(issuedAt.Time); expiration != time.Hour {
		t.Fatalf("expiration is wrong: %v", expiration)
	}

	return signedToken
}

func TestGetSignedTokenForService(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()

	testGetSignedTokenForServiceHelper(t, s, "embedding-indexer-starethanol6-16-1-proj512-svc", "dev-augie-shard")
}

func TestGetSignedTokenForServiceAllowedCrossNamespacePeer(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()

	testGetSignedTokenForServiceHelper(t, s, "slack-bot-webhook-grpc-svc", "central-test")
}

func TestGetSignedTokenForServiceInvalidCrossNamespacePeer(t *testing.T) {
	t.Run("invalid service for cross namespace access", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		testGetSignedTokenForServiceInvalidCrossNamespacePeerHelper(t, s, "embedding-indexer-starethanol6-16-1-proj512-svc", "central-test", "dev-augie-shard")
	})
	t.Run("service client not in central namespace", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		testGetSignedTokenForServiceInvalidCrossNamespacePeerHelper(t, s, "embedding-indexer-starethanol6-16-1-proj512-svc", "dev-augie-shard", "central-test")
	})
}

func testGetSignedTokenForServiceInvalidCrossNamespacePeerHelper(t *testing.T, s *tokenServer, serviceName string, serviceClient string, shardNamespace string) string {
	ctx := buildRequestContext([]string{serviceName, serviceName + "." + serviceClient})

	req := &tokenproto.GetSignedTokenForServiceRequest{
		ShardNamespace: shardNamespace,
		Scopes:         []tokenproto.Scope{tokenproto.Scope_AUTH_RW},
	}
	_, err := s.GetSignedTokenForService(ctx, req)
	if err == nil {
		t.Fatalf("expected error, got nil")
	}
	return ""
}

func testGetSignedTokenForServiceHelper(t *testing.T, s *tokenServer, serviceName string, serviceClient string) string {
	ctx := buildRequestContext([]string{serviceName, serviceName + "." + serviceClient})

	req := &tokenproto.GetSignedTokenForServiceRequest{
		ShardNamespace: "dev-augie-shard",
		Scopes: []tokenproto.Scope{
			tokenproto.Scope_CONTENT_RW,
		},
	}
	resp, err := s.GetSignedTokenForService(ctx, req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.SignedToken)

	token, err := jwt.ParseWithClaims(resp.SignedToken, &auth.AugmentClaims{}, func(token *jwt.Token) (any, error) {
		verificationKey, err := s.GetVerificationKey(context.Background(), &tokenproto.GetVerificationKeyRequest{})
		if err != nil {
			return nil, err
		}
		t.Log(verificationKey)
		keyfunc, err := keyfunc.NewJWKSetJSON(verificationKey.Jwks)
		if err != nil {
			return nil, err
		}
		return keyfunc.Keyfunc(token)
	})
	if err != nil {
		t.Fatal(err)
	}
	if claims, ok := token.Claims.(*auth.AugmentClaims); ok {
		now := time.Now()
		if claims.UserID != "" {
			t.Fatalf("unexpected user id: %v", claims.UserID)
		}
		if claims.ServiceName != serviceName {
			t.Fatalf("unexpected service name: %v", claims.ServiceName)
		}
		if claims.TenantID != "" {
			t.Fatalf("unexpected tenant ID: %v", claims.TenantID)
		}
		if claims.TenantName != "" {
			t.Fatalf("unexpected tenant name: %v", claims.TenantName)
		}
		if claims.ShardNamespace != "dev-augie-shard" {
			t.Fatalf("unexpected shard namespace: %v", claims.ShardNamespace)
		}
		if !reflect.DeepEqual(claims.Scope, []string{"CONTENT_R", "CONTENT_RW"}) {
			t.Fatalf("unexpected scope: %v", claims.Scope)
		}
		issuedAt, err := claims.GetIssuedAt()
		if err != nil {
			t.Fatal(err)
		}
		expiresAt, err := claims.GetExpirationTime()
		if err != nil {
			t.Fatal(err)
		}
		// The token's timestamp doesn't have so much precision, so just check that it is around "now"
		if now.Sub(issuedAt.Time) > time.Minute {
			t.Fatalf("issued at timestamp %v is far from now %v", issuedAt.Time, now)
		}
		if now.After(expiresAt.Time) {
			t.Fatalf("expiration is in the past: %v", expiresAt.Time)
		}
		if expiration := expiresAt.Time.Sub(issuedAt.Time); expiration != time.Hour {
			t.Fatalf("expiration is wrong: %v", expiration)
		}
	} else {
		t.Fatalf("unexpected claims: %v", token.Claims)
	}
	return resp.SignedToken
}

func TestGetSignedTokenForServiceInvalidScope(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()
	testGetSignedTokenForServiceInvalidScopeHelper(t, s)
}

func testGetSignedTokenForServiceInvalidScopeHelper(t *testing.T, s *tokenServer) string {
	ctx := buildRequestContext([]string{"embedding-indexer-starethanol6-16-1-proj512-svc.dev-augie-shard"})

	req := &tokenproto.GetSignedTokenForServiceRequest{
		ShardNamespace: "dev-augie-shard",
		Scopes:         []tokenproto.Scope{tokenproto.Scope_AUTH_RW},
	}
	_, err := s.GetSignedTokenForService(ctx, req)
	if err == nil {
		t.Fatalf("expected error, got nil")
	}
	return ""
}

func TestGetSignedTokenForIAP(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()
	signedToken := testGetSignedTokenForIAPHelper(t, s)

	// Verify that the IAP email can be extracted for audit logging, there are
	// also unit tests for the auth info / claims libraries in each language
	claims := getClaims(t, s, signedToken)
	if email, ok := claims.GetIapEmail(); !ok || email != "<EMAIL>" {
		t.Fatalf("<NAME_EMAIL>, got %v", email)
	}
}

func TestGetSignedTokenForIAPEmptyFields(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()

	originalSkipPeerValidation := s.config.SkipPeerValidation
	s.config.SkipPeerValidation = true
	defer func() {
		s.config.SkipPeerValidation = originalSkipPeerValidation
	}()

	ctx := buildRequestContext([]string{"support-ui-svc"})

	// Create a request with empty shard namespace, empty tenant ID, and no scopes
	req := &tokenproto.GetSignedTokenForIAPTokenRequest{
		// Empty shard namespace
		ShardNamespace: "",
		// Empty tenant ID
		TenantId: "",
		// No scopes specified
	}

	resp, err := s.GetSignedTokenForIAPToken(ctx, req)
	if err != nil {
		t.Fatalf("Expected request to succeed, got error: %v", err)
	}

	// Verify the token is valid
	claims := getClaims(t, s, resp.SignedToken)

	// Verify the claims have the expected values
	if claims.ShardNamespace != "" {
		t.Fatalf("expected empty shard namespace, got %v", claims.ShardNamespace)
	}
	if claims.TenantID != "" {
		t.Fatalf("expected empty tenant ID, got %v", claims.TenantID)
	}
	if claims.TenantName != "" {
		t.Fatalf("expected empty tenant name, got %v", claims.TenantName)
	}

	// Verify the token has the expected IAP email
	if email, ok := claims.GetIapEmail(); !ok || email != "<EMAIL>" {
		t.Fatalf("<NAME_EMAIL>, got %v", email)
	}
}

func testGetSignedTokenForIAPHelper(t *testing.T, s *tokenServer) string {
	t.Log("testGetSignedTokenForIAPHelper")

	originalSkipPeerValidation := s.config.SkipPeerValidation
	s.config.SkipPeerValidation = true
	defer func() {
		s.config.SkipPeerValidation = originalSkipPeerValidation
	}()

	ctx := buildRequestContext([]string{"support-ui-svc"})

	req := &tokenproto.GetSignedTokenForIAPTokenRequest{
		TenantId:       "aug12345",
		ShardNamespace: "dev-augie-shard",
	}
	resp, err := s.GetSignedTokenForIAPToken(ctx, req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp.SignedToken)

	return resp.SignedToken
}

func TestGetSignedTokenForUserBadPeer(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()

	ctx := buildRequestContext([]string{"completion-host", "completion-host.dev-augie-shard", "auth-query-svc", "auth-query-svc.other_shard"})

	userEmail := "<EMAIL>"
	req := &tokenproto.GetSignedTokenForUserRequest{
		UserId:         "1234",
		OpaqueUserId:   &authentitiesproto.UserId{UserId: "1234", UserIdType: authentitiesproto.UserId_AUGMENT},
		UserEmail:      &userEmail,
		TenantId:       "aug12345",
		ShardNamespace: "unit_test_shard",
	}
	_, err := s.GetSignedTokenForUser(ctx, req)
	if err == nil {
		t.Fatal("expected validation to fail for completion-host")
	}
}

func TestGetSignedTokenForUserCentralPeer(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()

	ctx := buildRequestContext([]string{"customer-ui-svc.central-test"})

	userEmail := "<EMAIL>"
	req := &tokenproto.GetSignedTokenForUserRequest{
		UserId:         "123",
		OpaqueUserId:   &authentitiesproto.UserId{UserId: "123", UserIdType: authentitiesproto.UserId_AUGMENT},
		UserEmail:      &userEmail,
		TenantId:       "aug12345",
		ShardNamespace: "dev-augie-shard",
	}

	getTokenForUser(t, s, req, ctx)
}

func TestGetSignedTokenForUserBadToken(t *testing.T) {
	// I made this by combining parts of two different valid tokens
	badToken := "***************************************************************************************************************************************************************************************************************************************************************************************************************************" // pragma: allowlist secret
	_, err := jwt.ParseWithClaims(badToken, &auth.AugmentClaims{}, func(token *jwt.Token) (interface{}, error) {
		keyfile := mustReadFile(t, "test_data/ec256-cert-public.pem")
		return jwt.ParseECPublicKeyFromPEM(keyfile)
	})
	if err == nil {
		t.Fatal("expected validation to fail for bad token")
	}
}

func TestGetSignedTokenForWrongNamespace(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()
	ctx := buildRequestContext([]string{"auth-query-svc", "auth-query-svc.dev-augie-shard"})

	userEmail := "<EMAIL>"
	req := &tokenproto.GetSignedTokenForUserRequest{
		UserId:         "123",
		OpaqueUserId:   &authentitiesproto.UserId{UserId: "123", UserIdType: authentitiesproto.UserId_AUGMENT},
		UserEmail:      &userEmail,
		TenantId:       "aug12345",
		ShardNamespace: "dev-someone-else-not-augie",
	}
	_, err := s.GetSignedTokenForUser(ctx, req)
	if err == nil {
		t.Fatal("expected validation to fail for wrong namespace")
	}
}

func TestGetSignedTokenWithCache(t *testing.T) {
	t.Run("user private key and certificate with cache", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		token1 := testGetSignedTokenForUserHelper(t, s)
		token2 := testGetSignedTokenForUserHelper(t, s)

		if token1 != token2 {
			t.Fatalf("tokens are not equal: %s != %s", token1, token2)
		}
	})
	t.Run("service tokens with cache", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		token1 := testGetSignedTokenForServiceHelper(t, s, "embedding-indexer-starethanol6-16-1-proj512-svc", "dev-augie-shard")
		token2 := testGetSignedTokenForServiceHelper(t, s, "embedding-indexer-starethanol6-16-1-proj512-svc", "dev-augie-shard")

		if token1 != token2 {
			t.Fatalf("tokens are not equal: %s != %s", token1, token2)
		}
	})
	t.Run("iap tokens with cache", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		token1 := testGetSignedTokenForIAPHelper(t, s)
		token2 := testGetSignedTokenForIAPHelper(t, s)

		if token1 != token2 {
			t.Fatalf("tokens are not equal: %s != %s", token1, token2)
		}
	})
}

func TestGetSignedTokenForDifferentRequestsWithCache(t *testing.T) {
	t.Run("user and service tokens", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		token1 := testGetSignedTokenForUserHelper(t, s)

		token2 := testGetSignedTokenForServiceHelper(t, s, "embedding-indexer-starethanol6-16-1-proj512-svc", "dev-augie-shard")

		if token1 == token2 {
			t.Fatalf("used same cached token for separate requests: user and service")
		}
	})
	t.Run("user and iap tokens", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		token1 := testGetSignedTokenForUserHelper(t, s)
		token2 := testGetSignedTokenForIAPHelper(t, s)

		if token1 == token2 {
			t.Fatalf("used same cached token for separate requests: user and service")
		}
	})
	t.Run("iap and service tokens", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		token1 := testGetSignedTokenForIAPHelper(t, s)

		token2 := testGetSignedTokenForServiceHelper(t, s, "embedding-indexer-starethanol6-16-1-proj512-svc", "dev-augie-shard")

		if token1 == token2 {
			t.Fatalf("used same cached token for separate requests: user and service")
		}
	})
}

func TestExpiringTokensInCache(t *testing.T) {
	tenantWatcherClient := &tenantclient.MockTenantWatcherClient{
		Tenants: []*tenantproto.Tenant{
			{
				Id:             "aug12345",
				Name:           "augie town",
				ShardNamespace: "dev-augie-shard",
			},
		},
	}

	config := &TokenServerConfig{
		UserTokenExpiration: durationutil.JSONDuration(time.Hour),
		ServiceTokenConfigs: []TokenForServiceConfig{},
		UserTokenConfigs: []TokenForUserConfig{
			{
				Regex:  "^auth-query-svc$",
				Scopes: []string{"AUTH_RW", "CONTENT_RW", "CONTENT_R", "SETTINGS_RW", "SETTINGS_R"},
			},
		},
		IapTokenExpiration: durationutil.JSONDuration(time.Hour),
		JwtSigningKey: TokenServerKeyConfig{
			Algorithm: "ES256",
			KeyPath:   "test_data/ec256-cert-private.pem",
			CertPath:  "test_data/ec256-cert-public.pem",
		},
		ShouldCacheTokens:      true,
		UserCacheExpiration:    durationutil.JSONDuration(time.Nanosecond * 100),
		ServiceCacheExpiration: durationutil.JSONDuration(time.Nanosecond * 100),
		MaxCacheKeys:           10000,
		ReadKeysFromConfigMap:  false,
		ConfigMapName:          "token-exchange-central-jwks-configmap",
	}

	tenantWatcher, tenantUpdateCh := tenantclient.NewTestTenantCache(t, tenantWatcherClient, "dev-augie-shard")
	<-tenantUpdateCh

	s, err := newServer(config, "test-namespace", nil, tenantWatcher, &NoopAclCheck{}, ripublisher.NewRequestInsightPublisherMock())
	if err != nil {
		t.Fatal(err)
	}

	token1 := testGetSignedTokenForUserHelper(t, s)
	time.Sleep(time.Millisecond * 1)
	token2 := testGetSignedTokenForUserHelper(t, s)
	if token1 == token2 {
		t.Fatalf("cached token persists after expiration")
	}
}

func TestGetTokenWithDifferentUsers(t *testing.T) {
	t.Run("different tenant ids", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()
		token1 := testGetSignedTokenForUserHelper(t, s)

		ctx := buildRequestContext([]string{"auth-query-svc", "auth-query-svc.dev-augie-shard"})

		userEmail := "<EMAIL>"
		req := &tokenproto.GetSignedTokenForUserRequest{
			UserId:         "123",
			OpaqueUserId:   &authentitiesproto.UserId{UserId: "123", UserIdType: authentitiesproto.UserId_AUGMENT},
			UserEmail:      &userEmail,
			TenantId:       "augie98765",
			ShardNamespace: "dev-augie-shard",
		}
		resp, err := s.GetSignedTokenForUser(ctx, req)
		if err != nil {
			t.Fatal(err)
		}
		token2 := resp.SignedToken
		if token1 == token2 {
			t.Fatalf("used same token for two different tenants")
		}
	})
}

func TestGetTokenWithAdditionalClaims(t *testing.T) {
	t.Run("additional claims", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()
		userEmail := "<EMAIL>"
		token1, claims := getTokenForUser(t, s, &tokenproto.GetSignedTokenForUserRequest{
			UserId:         "123",
			OpaqueUserId:   &authentitiesproto.UserId{UserId: "123", UserIdType: authentitiesproto.UserId_AUGMENT},
			UserEmail:      &userEmail,
			TenantId:       "aug12345",
			ShardNamespace: "dev-augie-shard",
			AdditionalClaims: &structpb.Struct{
				Fields: map[string]*structpb.Value{
					"foo": {
						Kind: &structpb.Value_StringValue{
							StringValue: "bar",
						},
					},
				},
			},
		}, authQueryContext)

		if claims.AdditionalClaims["foo"] != "bar" {
			t.Fatalf("additional claims not set")
		}

		// check not cached
		token2, _ := getTokenForUser(t, s, &tokenproto.GetSignedTokenForUserRequest{
			UserId:         "123",
			OpaqueUserId:   &authentitiesproto.UserId{UserId: "123", UserIdType: authentitiesproto.UserId_AUGMENT},
			UserEmail:      &userEmail,
			TenantId:       "aug12345",
			ShardNamespace: "dev-augie-shard",
			AdditionalClaims: &structpb.Struct{
				Fields: map[string]*structpb.Value{
					"foo": {
						Kind: &structpb.Value_StringValue{
							StringValue: "bar",
						},
					},
				},
			},
		}, authQueryContext)

		if token1 == token2 {
			t.Fatalf("do not cache token when additional claims are involved")
		}
	})
}

func TestGetSignedTokenForOtherNamespace(t *testing.T) {
	s := testServer(t, false)
	defer s.Close()

	// The ShardNamespace and OtherNamespace should both be able to get tokens
	for _, ns := range []string{"dev-migrated1", "dev-migrated2"} {
		ctx := buildRequestContext([]string{"embedding-indexer-starethanol6-16-1-proj512-svc", fmt.Sprintf("embedding-indexer-starethanol6-16-1-proj512-svc.%s", ns)})

		req := &tokenproto.GetSignedTokenForServiceRequest{
			ShardNamespace: ns,
			Scopes: []tokenproto.Scope{
				tokenproto.Scope_CONTENT_RW,
			},
		}
		resp, err := s.GetSignedTokenForService(ctx, req)
		if err != nil {
			t.Fatal(err)
		}

		claims := getClaims(t, s, resp.SignedToken)
		if claims.ShardNamespace != ns {
			t.Fatalf("expected shard namespace to be %s, got %v", ns, claims.ShardNamespace)
		}
	}
}

func TestGetSignedTokenForUserScopes(t *testing.T) {
	// Helper function to create a standard request
	createRequest := func(scopes []tokenproto.Scope) *tokenproto.GetSignedTokenForUserRequest {
		userEmail := "<EMAIL>"
		return &tokenproto.GetSignedTokenForUserRequest{
			UserId:         "123",
			OpaqueUserId:   &authentitiesproto.UserId{UserId: "123", UserIdType: authentitiesproto.UserId_AUGMENT},
			UserEmail:      &userEmail,
			TenantId:       "aug12345",
			ShardNamespace: "dev-augie-shard",
			Scopes:         scopes,
		}
	}

	// Helper function to verify scopes
	verifyScopes := func(t *testing.T, s *tokenServer, token string, expectedScopes []string) {
		t.Helper()
		claims := getClaims(t, s, token)

		sort.Strings(claims.Scope)
		sort.Strings(expectedScopes)

		if !reflect.DeepEqual(claims.Scope, expectedScopes) {
			t.Fatalf("unexpected scopes: got %v, want %v", claims.Scope, expectedScopes)
		}
	}

	t.Run("default scopes", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		req := createRequest(nil) // No scopes specified
		resp, err := s.GetSignedTokenForUser(authQueryContext, req)
		if err != nil {
			t.Fatal(err)
		}

		verifyScopes(t, s, resp.SignedToken, []string{"CONTENT_RW", "CONTENT_R", "SETTINGS_RW", "SETTINGS_R"})
	})

	// some clients such as token_exchange_client.rs depend on the behavior that an empty scopes list is the same as no scopes list
	t.Run("empty scopes list", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		req := createRequest([]tokenproto.Scope{}) // Empty scopes list
		resp, err := s.GetSignedTokenForUser(authQueryContext, req)
		if err != nil {
			t.Fatal(err)
		}

		verifyScopes(t, s, resp.SignedToken, []string{"CONTENT_RW", "CONTENT_R", "SETTINGS_RW", "SETTINGS_R"})
	})

	t.Run("explicit valid scopes", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		req := createRequest([]tokenproto.Scope{
			tokenproto.Scope_REQUEST_R,
			tokenproto.Scope_CONTENT_RW,
		})

		resp, err := s.GetSignedTokenForUser(authQueryContext, req)
		if err != nil {
			t.Fatal(err)
		}

		verifyScopes(t, s, resp.SignedToken, []string{"REQUEST_R", "CONTENT_RW"})
	})

	t.Run("unauthorized scope", func(t *testing.T) {
		s := testServer(t, false)
		defer s.Close()

		// Create context with the limited scope service
		limitedContext := buildRequestContext([]string{"limited-scope-svc", "limited-scope-svc.dev-augie-shard"})

		req := createRequest([]tokenproto.Scope{
			tokenproto.Scope_AUTH_RW, // This scope is not allowed for the limited service
		})

		_, err := s.GetSignedTokenForUser(limitedContext, req)
		if err == nil {
			t.Fatal("expected error for unauthorized scope, got nil")
		}
	})
}

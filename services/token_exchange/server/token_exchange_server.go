package main

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/MicahParks/jwkset"
	"github.com/augmentcode/augment/base/cloud/iap"
	"github.com/augmentcode/augment/base/go/durationutil"
	"github.com/augmentcode/augment/base/logging/audit"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpcservice "github.com/augmentcode/augment/services/lib/grpc/service"
	"github.com/augmentcode/augment/services/lib/jwtkeyid"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenproto "github.com/augmentcode/augment/services/token_exchange/proto"
	jwt "github.com/golang-jwt/jwt/v5"
	"github.com/hashicorp/golang-lru/v2/expirable"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"k8s.io/client-go/kubernetes"
)

var (
	tokenCacheHits = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "au_token_exchange_cache_hits",
		Help: "Number of times a token was found in the cache",
	})
	tokenCacheMisses = prometheus.NewCounter(prometheus.CounterOpts{
		Name: "au_token_exchange_cache_misses",
		Help: "Number of times a token was not found in the cache",
	})
)

func init() {
	prometheus.MustRegister(
		tokenCacheHits,
		tokenCacheMisses,
	)
}

type TokenServerKeyConfig struct {
	Algorithm string `json:"algorithm"`
	KeyPath   string `json:"key_path"`
	CertPath  string `json:"cert_path"`
}

type TokenForServiceConfig struct {
	Regex      string                    `json:"regex"`
	Scopes     []string                  `json:"scopes"`
	Expiration durationutil.JSONDuration `json:"expiration"`
}

type TokenForUserConfig struct {
	Regex  string   `json:"regex"`
	Scopes []string `json:"scopes"`
}

type TokenServerConfig struct {
	JwtSigningKey    TokenServerKeyConfig `json:"jwt_signing_key"`
	CentralNamespace string               `json:"central_namespace"`
	CentralCloud     string               `json:"central_cloud"`

	// Paths to extra verification keys - these are returned only for
	// verification, not for signing. These can be used for key rotation,
	// cross-cluster communication, etc.
	ExtraJwtVerificationKeys []TokenServerKeyConfig `json:"extra_jwt_verification_keys"`

	// Skip the IAP verification. Allows local frontend development.
	IapJwtVerifierDisabled bool `json:"iap_jwt_verifier_disabled"`

	// To skip peer validation in dev environments where we may not use mTLS
	SkipPeerValidation bool `json:"skip_peer_validation"`

	// How long each token lasts
	UserTokenExpiration   durationutil.JSONDuration `json:"user_token_expiration"`
	IapTokenExpiration    durationutil.JSONDuration `json:"iap_token_expiration"`
	IapTokenMaxExpiration durationutil.JSONDuration `json:"iap_token_max_expiration"`

	IapAudiencePrefix []string `json:"iap_audience_prefix"`

	IapTokenAllowedRegexp        string `json:"iap_token_allowed_regexp"`
	CentralIapTokenAllowedRegexp string `json:"central_iap_token_allowed_regexp"`

	ServiceTokenConfigs        []TokenForServiceConfig `json:"service_token_configs"`
	UserTokenConfigs           []TokenForUserConfig    `json:"user_token_configs"`
	AllowedCrossNamespacePeers []string                `json:"allowed_cross_namespace_peers"`

	ShouldCacheTokens      bool                      `json:"should_cache_tokens"`
	UserCacheExpiration    durationutil.JSONDuration `json:"user_cache_expiration"`
	ServiceCacheExpiration durationutil.JSONDuration `json:"service_cache_expiration"`
	MaxCacheKeys           int                       `json:"max_cache_keys"`
	ReadKeysFromConfigMap  bool                      `json:"read_keys_from_config_map"`
	ConfigMapName          string                    `json:"config_map_name"`
	AclCheckEnabled        bool                      `json:"acl_check_enabled"`

	TrustedAclNamespace string `json:"trusted_acl_namespace"`
}

type signingKey struct {
	signingMethod jwt.SigningMethod
	keyID         string
	privateJwk    jwkset.JWK
}

type tokenForServiceConfig struct {
	Regex      *regexp.Regexp
	Scopes     []string
	Expiration time.Duration
}

func (t *TokenForServiceConfig) toTokenForServiceConfig() (tokenForServiceConfig, error) {
	exp := t.Expiration.ToDuration()
	r, err := regexp.Compile(t.Regex)
	if err != nil {
		return tokenForServiceConfig{}, err
	}
	return tokenForServiceConfig{
		Regex:      r,
		Scopes:     t.Scopes,
		Expiration: exp,
	}, nil
}

type tokenForUserConfig struct {
	Regex  *regexp.Regexp
	Scopes []string
}

func (t *TokenForUserConfig) toTokenForUserConfig() (tokenForUserConfig, error) {
	r, err := regexp.Compile(t.Regex)
	if err != nil {
		return tokenForUserConfig{}, err
	}
	return tokenForUserConfig{
		Regex:  r,
		Scopes: t.Scopes,
	}, nil
}

type tokenServer struct {
	config *TokenServerConfig

	publicJwksHandler publicJwksHandler
	tenantWatcher     tenantclient.TenantCache

	signingKey signingKey

	iapVerifier iap.IapVerifier
	aclCheck    AclCheck

	serviceTokenConfigs          []tokenForServiceConfig
	userTokenConfigs             []tokenForUserConfig
	allowedCrossNamespacePeers   []*regexp.Regexp
	iapTokenAllowedRegexp        *regexp.Regexp
	centralIapTokenAllowedRegexp *regexp.Regexp

	userTokensCache       *expirable.LRU[TokenInfo, string]
	serviceIapTokensCache *expirable.LRU[TokenInfo, string]

	auditLogger *audit.AuditLogger

	requestInsightPublisher ripublisher.RequestInsightPublisher
}

// Do not create this directly, use newUserTokenRequest or newServiceTokenRequest instead
type tokenRequest struct {
	userID           string
	opaqueUserID     *authentitiesproto.UserId
	userEmail        string
	serviceName      string
	tenantID         string
	shardNamespace   string
	additionalClaims jwt.MapClaims
	expiration       time.Duration
	scope            []string
	requestContext   *requestcontext.RequestContext
	// tenant name and cloud are derived
}

func newUserTokenRequest(requestContext *requestcontext.RequestContext, userID string, opaqueUserID *authentitiesproto.UserId, userEmail *string, tenantID, shardNamespace string, additionalClaims jwt.MapClaims, expiration time.Duration, scope []string) tokenRequest {
	email := ""
	if userEmail != nil {
		email = *userEmail
	}
	return tokenRequest{
		userID:           userID,
		opaqueUserID:     opaqueUserID,
		userEmail:        email,
		serviceName:      "",
		tenantID:         tenantID,
		shardNamespace:   shardNamespace,
		additionalClaims: additionalClaims,
		expiration:       expiration,
		scope:            scope,
		requestContext:   requestContext,
	}
}

func newServiceTokenRequest(requestContext *requestcontext.RequestContext, serviceName, tenantID, shardNamespace string, additionalClaims jwt.MapClaims, expiration time.Duration, scope []string) tokenRequest {
	return tokenRequest{
		userID:           "",
		serviceName:      serviceName,
		tenantID:         tenantID,
		shardNamespace:   shardNamespace,
		additionalClaims: additionalClaims,
		expiration:       expiration,
		scope:            scope,
		requestContext:   requestContext,
	}
}

func newIAPTokenRequest(requestContext *requestcontext.RequestContext, email, tenantID, shardNamespace string, additionalClaims jwt.MapClaims, expiration time.Duration, scope []string) tokenRequest {
	opaqueUserID := &authentitiesproto.UserId{
		UserId:     fmt.Sprintf("iap:%s", email),
		UserIdType: authentitiesproto.UserId_INTERNAL_IAP,
	}
	return tokenRequest{
		userID:           email,
		opaqueUserID:     opaqueUserID,
		userEmail:        email,
		serviceName:      "",
		tenantID:         tenantID,
		shardNamespace:   shardNamespace,
		additionalClaims: additionalClaims,
		expiration:       expiration,
		scope:            scope,
		requestContext:   requestContext,
	}
}

func newCentralIAPTokenRequest(requestContext *requestcontext.RequestContext, email string, additionalClaims jwt.MapClaims, expiration time.Duration, scope []string) tokenRequest {
	opaqueUserID := &authentitiesproto.UserId{
		UserId:     fmt.Sprintf("iap:%s", email),
		UserIdType: authentitiesproto.UserId_INTERNAL_IAP,
	}
	return tokenRequest{
		userID:           email,
		opaqueUserID:     opaqueUserID,
		userEmail:        email,
		serviceName:      "",
		tenantID:         "",
		shardNamespace:   "",
		additionalClaims: additionalClaims,
		expiration:       expiration,
		scope:            scope,
		requestContext:   requestContext,
	}
}

type TokenInfo struct {
	UserID         string
	ServiceName    string
	TenantID       string
	ShardNamespace string
	Cloud          string
	Scope          string
}

// TODO(aswin): pull out shared code between this and tenant/server into base/
func dnsNameSplit(dnsName string) []string {
	return strings.Split(dnsName, ".")
}

// Returns token using cache or calls newToken if cache does not exist / cache miss
//
// Returns tokens for tenants that are marked deleted. We want to be able to run maintenance tasks, like deleting users,
// on deleted tenants and legacy namespaces.
func (ts *tokenServer) getToken(ctx context.Context, tokenReq tokenRequest, cache *expirable.LRU[TokenInfo, string]) (token string, err error) {
	if tokenReq.userID != "" && tokenReq.serviceName == "" {
		if tokenReq.shardNamespace == "" {
			// Central user tokens can only be for IAP users
			if tokenReq.opaqueUserID == nil {
				return "", status.Error(codes.InvalidArgument, "Invalid request")
			}
			if tokenReq.opaqueUserID.UserIdType != authentitiesproto.UserId_INTERNAL_IAP {
				return "", status.Error(codes.InvalidArgument, "Invalid request")
			}
			// Central tokens may not have a tenant ID
		} else {
			// Non-central user tokens must have a tenant ID, unless it's an IAP user
			if tokenReq.tenantID == "" && tokenReq.opaqueUserID.UserIdType != authentitiesproto.UserId_INTERNAL_IAP {
				return "", status.Error(codes.InvalidArgument, "user tokens must specify tenant ID")
			}
		}
	} else if tokenReq.serviceName != "" && tokenReq.userID == "" {
		// Services tokens can be for tenants or namespaces
	} else {
		panic("must specify exactly one of user or service")
	}

	var cloud, tenantName, shardNamespace string
	if tokenReq.tenantID != "" {
		// Confirm the tenant exists in this shard, and also get its name
		tenant, err := ts.tenantWatcher.GetTenant(tokenReq.tenantID)
		if err != nil {
			if errors.Is(err, tenantclient.ErrTenantNotFound) {
				return "", status.Errorf(codes.NotFound, "tenant not found")
			}
			return "", err
		}
		if tenant.ShardNamespace != tokenReq.shardNamespace && tenant.OtherNamespace != tokenReq.shardNamespace {
			// This is not a permissions error. It either means something has gone wrong internally, or
			// that an attacker is probing to figure out the right shard to send their requests to.
			riErr := ts.publishErrorEvent(
				ctx,
				&tokenReq,
				tenant,
				riproto.TokenExchangeError_TENANT_NOT_IN_SHARD,
			)
			if riErr != nil {
				log.Ctx(ctx).Warn().Err(riErr).Msg("Failed to publish TokenExchangeError event")
			}
			log.Ctx(ctx).Warn().Msgf("tenant %s (%s) is not in shard %s", tenant.Name, tenant.Id, tokenReq.shardNamespace)
			return "", status.Errorf(codes.InvalidArgument, "tenant %v is not in shard %v", tokenReq.tenantID, tokenReq.shardNamespace)
		}
		tenantName = tenant.Name
		cloud = tenant.Cloud
		shardNamespace = tenant.ShardNamespace
	} else if tokenReq.shardNamespace == "" {
		cloud = ts.config.CentralCloud
	} else {
		// Confirm the namespace exists
		tenants, err := ts.tenantWatcher.GetTenantsInNamespace(tokenReq.shardNamespace)
		if err != nil {
			// This is not a permissions error, this means something has gone
			// wrong internally
			return "", status.Errorf(codes.InvalidArgument, "error getting namespace: %w", err)
		}
		if len(tenants) == 0 {
			return "", status.Errorf(codes.Internal, "namespace %v has no tenants", tokenReq.shardNamespace)
		}
		cloud = tenants[0].Cloud
		shardNamespace = tokenReq.shardNamespace
	}

	// Token requests with additionalClaims are processed without
	// caching to accommodate unstructured additionalClaims data.
	// Today, these requests are only used for UI purposes,
	// making caching unnecessary.
	if cache != nil && tokenReq.additionalClaims == nil {
		sort.Strings(tokenReq.scope)
		scopeString := strings.Join(tokenReq.scope, ",")

		tokenInfo := TokenInfo{
			UserID:         tokenReq.userID,
			ServiceName:    tokenReq.serviceName,
			TenantID:       tokenReq.tenantID,
			ShardNamespace: shardNamespace,
			Cloud:          cloud,
			Scope:          scopeString,
		}

		token, ok := cache.Get(tokenInfo)

		if ok {
			tokenCacheHits.Inc()
			return token, nil
		}
	}

	tokenCacheMisses.Inc()
	return ts.newToken(ctx, tokenReq, tenantName, cloud, cache)
}

func (ts *tokenServer) newToken(ctx context.Context, tokenReq tokenRequest, tenantName string, cloud string, cache *expirable.LRU[TokenInfo, string]) (token string, err error) {
	sort.Strings(tokenReq.scope)
	scopeString := strings.Join(tokenReq.scope, ",")

	tokenInfo := TokenInfo{
		UserID:         tokenReq.userID,
		ServiceName:    tokenReq.serviceName,
		TenantID:       tokenReq.tenantID,
		ShardNamespace: tokenReq.shardNamespace,
		Cloud:          cloud,
		Scope:          scopeString,
	}

	var opaqueUserID, opaqueUserIDType string
	if tokenReq.opaqueUserID != nil {
		opaqueUserID = tokenReq.opaqueUserID.UserId
		opaqueUserIDType = tokenReq.opaqueUserID.UserIdType.String()
	}

	claims := &auth.AugmentClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(tokenReq.expiration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "token-central",
			// Subject:   "content",
			// ID:        uuid.New(),
			// Audience: []string{"content-manager"},
		},
		UserID:           tokenReq.userID,
		OpaqueUserID:     opaqueUserID,
		OpaqueUserIDType: opaqueUserIDType,
		UserEmail:        tokenReq.userEmail,
		ServiceName:      tokenReq.serviceName,
		TenantID:         tokenReq.tenantID,
		ShardNamespace:   tokenReq.shardNamespace,
		Cloud:            cloud,
		Scope:            tokenReq.scope,
		TenantName:       tenantName,
		AdditionalClaims: tokenReq.additionalClaims,
	}

	// Sign the claims and return them
	unsignedToken := jwt.NewWithClaims(ts.signingKey.signingMethod, claims)
	unsignedToken.Header[jwkset.HeaderKID] = ts.signingKey.keyID
	ss, err := unsignedToken.SignedString(ts.signingKey.privateJwk.Key())
	if err != nil {
		log.Warn().Msgf("Failed to sign JWT: %v", err)
	}
	if err == nil && cache != nil {
		// Only add to the cache if we got a token successfully
		cache.Add(tokenInfo, ss)
	}
	return ss, err
}

func (ts *tokenServer) validatePeer(ctx context.Context, namespace string, allowedServices *regexp.Regexp) (peerService string, err error) {
	peerNames, namespaces, err := grpcservice.GetAllPeerNamesAndNamespaces(ctx)
	if err != nil {
		log.Ctx(ctx).Warn().Msgf("Failed to get peer names and namespaces: %v", err)
		return "", err
	}

	for idx, peerName := range peerNames {
		peerNamespace := namespaces[idx]
		if !allowedServices.MatchString(peerName) {
			log.Debug().Msgf("Failed to validate peer name %s.%s", peerName, namespace)
		} else if peerNamespace != namespace && peerNamespace != ts.config.CentralNamespace {
			// Temporary: Services in central namespace are allowed to generate JWTs for any namespace.
			log.Warn().Msgf(
				`Client namespace "%v" does not match expected namespace "%v" or central namespace "%v" for peer %s.%s`,
				peerNamespace, namespace, ts.config.CentralNamespace, peerName, namespace,
			)
		} else {
			// Approve access
			return peerName, nil
		}
	}
	log.Warn().Msgf("Peer %v (namespace %v) is not allowed access", peerNames, namespaces)
	return "", status.Error(codes.PermissionDenied, "did not find authorized peer service")
}

func (ts *tokenServer) validatePeerForServiceToken(ctx context.Context, namespace string, serviceConfigs []tokenForServiceConfig) (peerService string, allowedScopes *[]string, expiration *time.Duration, err error) {
	peerNames, namespaces, err := grpcservice.GetAllPeerNamesAndNamespaces(ctx)
	if err != nil {
		log.Ctx(ctx).Warn().Msgf("Failed to get peer names and namespaces: %v", err)
		return "", nil, nil, err
	}

	for idx, peerName := range peerNames {
		peerNamespace := namespaces[idx]
		for _, config := range serviceConfigs {
			if !config.Regex.MatchString(peerName) {
				log.Debug().Msgf("Failed to validate peer name %s.%s", peerName, namespace)
				continue
			} else if peerNamespace != namespace && !ts.isPeerAllowedCrossNamespace(peerName, peerNamespace) {
				log.Ctx(ctx).Warn().Msgf(
					`Client namespace "%v" does not match expected namespace "%v" or central namespace "%v" for peer %s.%s`,
					peerNamespace, namespace, ts.config.CentralNamespace, peerName, peerNamespace,
				)
			} else {
				// Approve access for the given set of scopes
				return peerName, &config.Scopes, &config.Expiration, nil
			}
		}
	}
	log.Ctx(ctx).Warn().Msgf("Peer %v (namespaces %v) is not allowed access", peerNames, namespaces)
	return "", nil, nil, status.Error(codes.PermissionDenied, "did not find authorized peer service")
}

func (ts *tokenServer) validatePeerForUserToken(ctx context.Context, namespace string) (peerService string, allowedScopes *[]string, err error) {
	peerNames, namespaces, err := grpcservice.GetAllPeerNamesAndNamespaces(ctx)
	if err != nil {
		log.Ctx(ctx).Warn().Msgf("Failed to get peer names and namespaces: %v", err)
		return "", nil, err
	}

	for idx, peerName := range peerNames {
		peerNamespace := namespaces[idx]
		for _, config := range ts.userTokenConfigs {
			if !config.Regex.MatchString(peerName) {
				continue
			} else if peerNamespace != namespace && peerNamespace != ts.config.CentralNamespace {
				log.Ctx(ctx).Warn().Msgf(
					`User token request namespace mismatch: client in namespace "%v" attempting to request token for namespace "%v". User token requests must use the same namespace as the requesting service. Peer: %s`,
					peerNamespace, namespace, peerName,
				)
			} else {
				// Approve access for the given set of scopes
				return peerName, &config.Scopes, nil
			}
		}
	}

	log.Ctx(ctx).Warn().Msgf("Peer %v (namespaces %v) is not allowed access", peerNames, namespaces)
	return "", nil, status.Error(codes.PermissionDenied, "did not find authorized peer service")
}

// peer is allowed to generate a cross-namespace token if it is in the list of allowed peers and is coming from a central namespace
func (ts *tokenServer) isPeerAllowedCrossNamespace(clientService string, clientNamespace string) bool {
	if clientNamespace != ts.config.CentralNamespace {
		log.Warn().Msgf("Client namespace %v is not central namespace %v", clientNamespace, ts.config.CentralNamespace)
		return false
	}
	for _, allowedPeer := range ts.allowedCrossNamespacePeers {
		log.Debug().Msgf("Checking peer %v against allowed peer %v", clientService, allowedPeer)
		if allowedPeer.MatchString(clientService) {
			log.Debug().Msgf("Found allowed peer %v", allowedPeer)
			return true
		}
	}
	return false
}

func (ts *tokenServer) GetSignedTokenForUser(ctx context.Context, req *tokenproto.GetSignedTokenForUserRequest) (resp *tokenproto.GetSignedTokenForUserResponse, err error) {
	defer func() {
		if err != nil {
			log.Warn().Msgf("error in GetSignedTokenForUser: %v", err)
		}
	}()
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetSignedTokenForUser: shardNamespace=%v tenantID=%v",
		req.ShardNamespace, req.TenantId)

	serviceName := "unknown"
	var allowedScopes *[]string
	if !ts.config.SkipPeerValidation {
		// Validate the peer and get the service name and allowed scopes
		serviceName, allowedScopes, err = ts.validatePeerForUserToken(ctx, req.ShardNamespace)
		if err != nil {
			return nil, err
		}
	}

	if req.GetUserId() == "" {
		return nil, status.Error(codes.InvalidArgument, "must specify user id")
	}
	if req.TenantId == "" {
		return nil, status.Error(codes.InvalidArgument, "must specify tenant id")
	}

	// Define the scopes for the user token
	var scopes []string

	// If no scopes are provided, use default scopes for backwards compatibility
	if len(req.Scopes) == 0 {
		scopes = []string{"CONTENT_RW", "CONTENT_R", "SETTINGS_RW", "SETTINGS_R"}
	} else {
		// Otherwise, use the scopes provided
		for _, s := range req.Scopes {
			if scopeStr := s.String(); !slices.Contains(scopes, scopeStr) {
				scopes = append(scopes, scopeStr)
			}
		}
	}

	// Check if all scopes are allowed
	if allowedScopes != nil {
		for _, s := range scopes {
			if !slices.Contains(*allowedScopes, s) {
				return nil, status.Errorf(codes.PermissionDenied, "service %s not allowed to request scope %s", serviceName, s)
			}
		}
	}

	var additionalClaims map[string]interface{}
	if req.GetAdditionalClaims() != nil {
		additionalClaimsStr, err := protojson.Marshal(req.GetAdditionalClaims())
		if err != nil {
			log.Warn().Err(err).Msg("Error marshalling to JSON")
			return nil, err
		}
		err = json.Unmarshal(additionalClaimsStr, &additionalClaims)
		if err != nil {
			log.Warn().Err(err).Msg("Error unmarshalling JSON to map")
			return nil, err
		}
	}

	tokenReq := newUserTokenRequest(requestContext, req.UserId, req.OpaqueUserId, req.UserEmail, req.TenantId, req.ShardNamespace, additionalClaims, ts.config.UserTokenExpiration.ToDuration(), scopes)
	signedToken, err := ts.getToken(ctx, tokenReq, ts.userTokensCache)
	if err != nil {
		return nil, err
	}
	return &tokenproto.GetSignedTokenForUserResponse{
		SignedToken: signedToken,
	}, nil
}

func (ts *tokenServer) GetSignedTokenForService(ctx context.Context, req *tokenproto.GetSignedTokenForServiceRequest) (resp *tokenproto.GetSignedTokenForServiceResponse, err error) {
	defer func() {
		if err != nil {
			log.Warn().Msgf("error in GetSignedTokenForService: %v", err)
		}
	}()
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetSignedTokenForService: shardNamespace=%v tenantID=%v scopes=%v",
		req.ShardNamespace, req.TenantId, req.Scopes)

	if len(req.Scopes) == 0 {
		return nil, status.Error(codes.InvalidArgument, "must specify at least one scope")
	}

	serviceName := "unknown"
	var allowedScopes *[]string
	expiration := time.Hour * 1
	if !ts.config.SkipPeerValidation {
		var serviceExpiration *time.Duration
		// Each service is only allowed to access its own namespace
		serviceName, allowedScopes, serviceExpiration, err = ts.validatePeerForServiceToken(ctx, req.ShardNamespace, ts.serviceTokenConfigs)
		if err != nil {
			return nil, err
		}
		expiration = *serviceExpiration
	}

	scopes := []string{}
	for _, scope := range req.GetScopes() {
		// Check if scope is allowed
		if scopeStr := scope.String(); allowedScopes != nil && !slices.Contains(*allowedScopes, scopeStr) {
			return nil, status.Errorf(codes.PermissionDenied, "service %s not allowed to request scope %s", serviceName, scopeStr)
		}
		scopes = append(scopes, scope.String())
	}
	scopes = getAllScopes(scopes)

	tokenReq := newServiceTokenRequest(requestContext, serviceName, req.TenantId, req.ShardNamespace, nil, expiration, scopes)
	signedToken, err := ts.getToken(ctx, tokenReq, ts.serviceIapTokensCache)
	if err != nil {
		return nil, err
	}

	return &tokenproto.GetSignedTokenForServiceResponse{
		SignedToken: signedToken,
	}, nil
}

func (ts *tokenServer) GetVerificationKey(ctx context.Context, req *tokenproto.GetVerificationKeyRequest) (resp *tokenproto.GetVerificationKeyResponse, err error) {
	allPublicJwks := ts.publicJwksHandler.getAllPublicJwks()

	// No need to do peer validation, any other service can use the public key
	return &tokenproto.GetVerificationKeyResponse{
		Jwks: allPublicJwks,
	}, nil
}

func getAllScopes(scopes []string) []string {
	allScopes := []string{}
	for _, scope := range scopes {
		// check for duplicate scopes
		if slices.Contains(allScopes, scope) {
			continue
		}
		allScopes = append(allScopes, scope)
		if strings.HasSuffix(scope, "_RW") {
			// add _R scope to
			read_scope := strings.TrimSuffix(scope, "_RW") + "_R"
			if !slices.Contains(allScopes, read_scope) {
				allScopes = append(allScopes, read_scope)
			}
		}
	}
	return allScopes
}

func (ts *tokenServer) GetSignedTokenForIAPToken(ctx context.Context, req *tokenproto.GetSignedTokenForIAPTokenRequest) (resp *tokenproto.GetSignedTokenForIAPTokenResponse, err error) {
	defer func() {
		if err != nil {
			log.Ctx(ctx).Info().Msgf("error in GetSignedTokenForIAPToken: %v", err)
		}
	}()

	requestContext, err := requestcontext.FromGrpcContext(ctx)
	ctx = requestContext.AnnotateLogContext(ctx)

	log.Ctx(ctx).Info().Msgf("GetSignedTokenForIAPToken: shardNamespace=%v tenantID=%v", req.ShardNamespace, req.TenantId)

	email, err := ts.iapVerifier.Verify(req.IapToken)
	if ts.config.IapJwtVerifierDisabled {
		email = "<EMAIL>"
		err = nil
	}

	if err != nil {
		return nil, err
	}
	defer func() {
		if err != nil {
			log.Ctx(ctx).Warn().Msgf("error getting token for user: %v", err)
		}
	}()

	log.Ctx(ctx).Info().Msgf("Exchanging IAP token for user for service token: email=%s, scopes=%s", email, req.GetScopes())

	if !ts.config.SkipPeerValidation {
		_, err = ts.validatePeer(ctx, req.ShardNamespace, ts.iapTokenAllowedRegexp)
		if err != nil {
			return nil, err
		}
	}

	scopes := []string{}
	for _, scope := range req.GetScopes() {
		scopes = append(scopes, scope.String())
	}
	scopes = getAllScopes(scopes)

	tenantName := ""
	if req.TenantId != "" {
		tenant, err := ts.tenantWatcher.GetTenant(req.TenantId)
		if err != nil {
			if errors.Is(err, tenantclient.ErrTenantNotFound) {
				return nil, status.Errorf(codes.NotFound, "tenant not found")
			}
			return nil, err
		}
		if tenant.GetConfig().Configs["support_access_control"] != "false" && ts.config.AclCheckEnabled {
			err = ts.aclCheck.Check(ctx, email, req.TenantId, req.ShardNamespace, scopes)
			if err != nil {
				return nil, err
			}
		} else {
			log.Ctx(ctx).Info().Msg("Support access control is disabled for this tenant")
		}
		tenantName = tenant.GetName()
	} else {
		log.Ctx(ctx).Info().Msg("Perform namespace level ACL check")
		if ts.config.AclCheckEnabled {
			err = ts.aclCheck.Check(ctx, email, req.TenantId, req.ShardNamespace, scopes)
			if err != nil {
				return nil, err
			}
		} else {
			log.Ctx(ctx).Info().Msg("Support access control is disabled for this namespace")
		}
	}

	duration := ts.config.IapTokenExpiration.ToDuration()
	if req.Expiration != nil {
		duration = req.Expiration.AsDuration()
		if duration > ts.config.IapTokenMaxExpiration.ToDuration() {
			return nil, status.Error(codes.InvalidArgument, "requested token expiration is too long")
		}
	}

	ts.auditLogger.WriteAuditLog(email, audit.INTERNAL_IAP, tenantName,
		fmt.Sprintf("Generate service token for tenant %s, scopes %v", tenantName, scopes))

	tokenReq := newIAPTokenRequest(requestContext, email, req.TenantId, req.ShardNamespace, nil, duration, scopes)
	signedToken, err := ts.getToken(ctx, tokenReq, ts.serviceIapTokensCache)
	if err != nil {
		return nil, err
	}
	return &tokenproto.GetSignedTokenForIAPTokenResponse{
		SignedToken: signedToken,
	}, nil
}

func (ts *tokenServer) publishErrorEvent(
	ctx context.Context,
	tokenReq *tokenRequest,
	tenant *tenantproto.Tenant,
	reason riproto.TokenExchangeError_Reason,
) error {
	if ts.requestInsightPublisher == nil {
		log.Ctx(ctx).Info().Msgf(
			"Not logging %s TokenExchangeError event because request insight publisher is not configured",
			reason)
		return nil
	}

	riEvent := ripublisher.NewRequestEvent()
	riEvent.Event = &riproto.RequestEvent_TokenExchangeError{
		TokenExchangeError: &riproto.TokenExchangeError{
			Reason:       reason,
			OpaqueUserId: tokenReq.opaqueUserID,
			UserEmail:    tokenReq.userEmail,
		},
	}
	return ts.requestInsightPublisher.PublishRequestEvent(
		ctx,
		tokenReq.requestContext.RequestId.String(),
		&riproto.TenantInfo{
			TenantId:   tenant.Id,
			TenantName: tenant.Name,
		},
		riEvent,
	)
}

func (ts *tokenServer) Close() {
	ts.tenantWatcher.Close()
}

/*
 * Adds the key from the given config to the provided jwkset, and also returns a
 * signingKey if this key is to be used to sign jwts
 */
func addKey(ctx context.Context, storage jwkset.Storage, key *TokenServerKeyConfig) (*signingKey, error) {
	signingMethod := jwt.GetSigningMethod(key.Algorithm)
	if signingMethod == jwt.SigningMethodNone {
		return nil, errors.New("signing method not supported")
	}

	cert, err := tls.LoadX509KeyPair(key.CertPath, key.KeyPath)
	if err != nil {
		return nil, err
	}
	x509Cert, err := x509.ParseCertificate(cert.Certificate[0])
	if err != nil {
		return nil, err
	}

	keyID := jwtkeyid.GetKeyIDFromCert(x509Cert)
	options := jwkset.JWKOptions{
		Metadata: jwkset.JWKMetadataOptions{
			ALG: jwkset.ALG(key.Algorithm),
			KID: keyID,
		},
	}

	privateKey, err := jwkset.NewJWKFromKey(cert.PrivateKey, options)
	if err != nil {
		return nil, err
	}
	err = storage.KeyWrite(ctx, privateKey)
	if err != nil {
		return nil, err
	}
	return &signingKey{
		signingMethod: signingMethod,
		keyID:         keyID,
		privateJwk:    privateKey,
	}, nil
}

/*
 * Returns the signing key (always a single key) and a jwkset of the public
 * verification keys (can be multiple, always includes the signing key)
 */
func getJwks(ctx context.Context, config *TokenServerConfig) (key *signingKey, verificationJwks []byte, err error) {
	// First get the signing key
	storage := jwkset.NewMemoryStorage()

	key, err = addKey(ctx, storage, &config.JwtSigningKey)
	if err != nil {
		return
	}

	// Now get the verification keys
	for _, keyConfig := range config.ExtraJwtVerificationKeys {
		_, err = addKey(ctx, storage, &keyConfig)
		if err != nil {
			return
		}
	}

	verificationJwks, err = storage.JSONPublic(ctx)
	return
}

func newServer(config *TokenServerConfig, namespace string, clientset *kubernetes.Clientset, tenantWatcher tenantclient.TenantCache, aclCheck AclCheck, requestInsightPublisher ripublisher.RequestInsightPublisher) (*tokenServer, error) {
	if config.UserTokenExpiration == 0 {
		return nil, errors.New("user token expiration must be set")
	}

	var serviceTokenConfigs []tokenForServiceConfig
	for _, c := range config.ServiceTokenConfigs {
		d, err := c.toTokenForServiceConfig()
		if err != nil {
			return nil, err
		}

		serviceTokenConfigs = append(serviceTokenConfigs, d)
	}

	var userTokenConfigs []tokenForUserConfig
	for _, c := range config.UserTokenConfigs {
		d, err := c.toTokenForUserConfig()
		if err != nil {
			return nil, err
		}

		userTokenConfigs = append(userTokenConfigs, d)
	}

	var allowedCrossNamespacePeers []*regexp.Regexp
	for _, c := range config.AllowedCrossNamespacePeers {
		r, err := regexp.Compile(c)
		if err != nil {
			return nil, err
		}
		allowedCrossNamespacePeers = append(allowedCrossNamespacePeers, r)
	}

	iapTokenAllowedRegexp := regexp.MustCompile(config.IapTokenAllowedRegexp)
	if iapTokenAllowedRegexp == nil {
		return nil, errors.New("iap token allowed regexp is invalid")
	}

	centralIapTokenAllowedRegexp := regexp.MustCompile(config.CentralIapTokenAllowedRegexp)
	if centralIapTokenAllowedRegexp == nil {
		return nil, errors.New("central iap token allowed regexp is invalid")
	}

	var userTokensCache, serviceIapTokensCache *expirable.LRU[TokenInfo, string]
	if config.ShouldCacheTokens {
		userTokensCache = expirable.NewLRU[TokenInfo, string](config.MaxCacheKeys, nil, config.UserCacheExpiration.ToDuration())
		serviceIapTokensCache = expirable.NewLRU[TokenInfo, string](config.MaxCacheKeys, nil, config.ServiceCacheExpiration.ToDuration())
	}

	signingKey, publicJwks, err := getJwks(context.Background(), config)
	if err != nil {
		return nil, err
	}

	iapVerifier, err := iap.New(config.IapAudiencePrefix)
	if err != nil {
		return nil, err
	}

	auditLogger := audit.NewDefaultAuditLogger()

	return &tokenServer{
		config:                       config,
		publicJwksHandler:            newPublicJwksHandler(config, clientset, namespace, publicJwks),
		tenantWatcher:                tenantWatcher,
		signingKey:                   *signingKey,
		iapVerifier:                  iapVerifier,
		aclCheck:                     aclCheck,
		serviceTokenConfigs:          serviceTokenConfigs,
		userTokenConfigs:             userTokenConfigs,
		allowedCrossNamespacePeers:   allowedCrossNamespacePeers,
		iapTokenAllowedRegexp:        iapTokenAllowedRegexp,
		centralIapTokenAllowedRegexp: centralIapTokenAllowedRegexp,
		userTokensCache:              userTokensCache,
		serviceIapTokensCache:        serviceIapTokensCache,
		auditLogger:                  auditLogger,
		requestInsightPublisher:      requestInsightPublisher,
	}, nil
}

package main

import (
	"encoding/json"
	"reflect"
	"testing"

	"github.com/MicahParks/jwkset"
)

func TestVerificationKeyMerge(t *testing.T) {
	// Hack: reuse the server initialization code to read the first public key
	s := testServer(t, true)
	defer s.Close()

	publicJwks := mustReadFile(t, "test_data/ec256-cert-publicJwks.json")
	jwks1 := json.RawMessage(publicJwks)

	jwks2 := s.publicJwksHandler.(*publicJwksHandlerImpl).currentPublicJwks

	mergedPublicJwks, err := mergePublicJwks(jwks1, jwks2)
	if err != nil {
		t.Fatal(err)
	}

	// File generated from verificationKeys using version commit hash 135b9cbf7.
	// Later updated to use a base64 based key ID - note that old key IDs with
	// the binary format still exists and is not removed, but a new key ID with
	// the base64 string format also exists.
	desiredOutput := mustReadFile(t, "test_data/ec256-cert-publicJwks-combined.json")

	var desiredOutputJWKS jwkset.JWKSMarshal
	err = json.Unmarshal(desiredOutput, &desiredOutputJWKS)
	if err != nil {
		t.Fatal(err)
	}

	var mergedJWKS jwkset.JWKSMarshal
	err = json.Unmarshal(mergedPublicJwks, &mergedJWKS)
	if err != nil {
		t.Fatal(err)
	}

	if !reflect.DeepEqual(mergedJWKS, desiredOutputJWKS) {
		desiredOutputStr, err := json.Marshal(desiredOutputJWKS)
		if err != nil {
			t.Fatal(err)
		}

		verificationKeysStr, err := json.Marshal(mergedJWKS)
		if err != nil {
			t.Fatal(err)
		}
		t.Fatalf("unexpected verification keys mismatch:\nverificationKeys=%s\ndesiredOutput=%s", string(verificationKeysStr), string(desiredOutputStr))
	}
}

// this file contains a thin go wrapper for the SupportUIAccess Kubernetes CRD (Custom Resoure Definition)
//
// It provides a type-safe way to create and update a SupportUIAccess resource.
// Beyond that it doesn't contain service token acl business logic
package main

import (
	"encoding/json"
	"time"

	"github.com/rs/zerolog/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/dynamic/dynamicinformer"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/clientcmd"
)

// The SupportUIAccess is the specifiation or configuration of the SupportUIAccess resource
// the fields have to map to the properties of the SupportUIAccess CRD in services/support/deploy_shared.jsonnet
type SupportUIAccessSpec struct {
	UserName string `json:"userName"`

	ExpiresAt string `json:"expiresAt"`

	Scope string `json:"scope"`

	NamespaceScope string `json:"namespaceScope"`

	TenantID string `json:"tenantID"`

	TokenScopes []string `json:"tokenScopes,omitempty"`
}

// the main object for the SupportUIAccess CRD
type SupportUIAccess struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec SupportUIAccessSpec `json:"spec"`
}

// converts the SupportUIAccess object to an unstructured object
func (t *SupportUIAccess) ToUnstructured() (*unstructured.Unstructured, error) {
	u := &unstructured.Unstructured{}
	bytes, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}
	err = u.UnmarshalJSON(bytes)
	if err != nil {
		return nil, err
	}
	return u, nil
}

// converts the unstructured object to a SupportUIAccess object
func (t *SupportUIAccess) FromUnstructured(u *unstructured.Unstructured) error {
	bytes, err := u.MarshalJSON()
	if err != nil {
		log.Error().Err(err).Msg("Error converting to unstructured")
		return err
	}
	return json.Unmarshal(bytes, t)
}

// callbacks to be informed about changes by the SupportUIAccessInformer
type SupportUIAccessEventHandlerFuncs struct {
	AddFunc    func(obj *SupportUIAccess, isInInitialList bool)
	UpdateFunc func(oldObj *SupportUIAccess, newObj *SupportUIAccess)
	DeleteFunc func(obj *SupportUIAccess)
}

// informs about changes to the SupportUIAccess CRD
type SupportUIAccessInformer interface {
	// Run starts the informer
	Run(stopCh <-chan struct{})

	// List returns the list of all acls
	List() ([]SupportUIAccess, error)

	// AddEventHandler registers a handler to be called when an event occurs
	// returns a registration that can be used to remove the handler
	AddEventHandler(f SupportUIAccessEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error)

	// RemoveEventHandler removes the given handler
	RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error
}

// implementation of the acl informer interfaced based on a shared informer
// an informer is a Kubernetes concept that watches for changes to a resources
// and allows to register handlers to be called when an event occurs as well
// as return the currently known state
type sharedIndexSupportUIAccessInformer struct {
	informer cache.SharedIndexInformer
}

// NewFilteredSupportUIAccessInformer creates a new SupportUIAccessInformer
// A filtered informer is used to only watch a specific namespace. It requires the permissions do to so on the given namespace
// Use newSupportUIAccessInformer to watch all namespaces
func NewFilteredSupportUIAccessInformer(clientset *dynamic.DynamicClient, defaultResync time.Duration, namespace string) SupportUIAccessInformer {
	fac := dynamicinformer.NewFilteredDynamicSharedInformerFactory(clientset, defaultResync, namespace, nil)
	informer := fac.ForResource(schema.GroupVersionResource{
		Group:    "eng.augmentcode.com",
		Version:  "v1",
		Resource: "supportuiaccesses",
	}).Informer()

	return &sharedIndexSupportUIAccessInformer{
		informer: informer,
	}
}

// NewSupportUIAccessInformer creates a new SupportUIAccessInformer
// this function returnes a shared informer that watches all namespaces. It requires clusterrole permission
// Use newFilteredSupportUIAccessInformer to watch a specific namespace
func NewSupportUIAccessInformer(clientset *dynamic.DynamicClient, defaultResync time.Duration) SupportUIAccessInformer {
	fac := dynamicinformer.NewDynamicSharedInformerFactory(clientset, defaultResync)
	informer := fac.ForResource(schema.GroupVersionResource{
		Group:    "eng.augmentcode.com",
		Version:  "v1",
		Resource: "supportuiaccesses",
	}).Informer()

	return &sharedIndexSupportUIAccessInformer{
		informer: informer,
	}
}

func (t *sharedIndexSupportUIAccessInformer) Run(stopCh <-chan struct{}) {
	t.informer.Run(stopCh)
}

func (t *sharedIndexSupportUIAccessInformer) List() ([]SupportUIAccess, error) {
	var acls []SupportUIAccess
	for _, obj := range t.informer.GetStore().List() {
		typedObj := obj.(*unstructured.Unstructured)
		var acl SupportUIAccess
		err := acl.FromUnstructured(typedObj)
		if err != nil {
			log.Error().Err(err).Msg("Error converting to acl")
			return nil, err
		}
		acls = append(acls, acl)
	}

	return acls, nil
}

func (t *sharedIndexSupportUIAccessInformer) AddEventHandler(f SupportUIAccessEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	handle, err := t.informer.AddEventHandler(cache.ResourceEventHandlerDetailedFuncs{
		AddFunc: func(obj interface{}, isInInitialList bool) {
			log.Info().Msgf("AddFunc: %v, %v", obj, isInInitialList)
			if f.AddFunc == nil {
				return
			}
			// converting the dynamic object to your CRD struct
			typedObj := obj.(*unstructured.Unstructured)
			var crdObj SupportUIAccess
			err := crdObj.FromUnstructured(typedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to acl")
				return
			}
			f.AddFunc(&crdObj, isInInitialList)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			log.Info().Msgf("UpdateFunc: %v, %v", oldObj, newObj)
			if f.UpdateFunc == nil {
				return
			}
			oldTypedObj := oldObj.(*unstructured.Unstructured)
			var oldCrdObj SupportUIAccess
			err := oldCrdObj.FromUnstructured(oldTypedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to acl")
				return
			}
			newTypedObj := newObj.(*unstructured.Unstructured)
			var newCrdObj SupportUIAccess
			err = newCrdObj.FromUnstructured(newTypedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to acl")
				return
			}
			f.UpdateFunc(&oldCrdObj, &newCrdObj)
		},
		DeleteFunc: func(obj interface{}) {
			if f.DeleteFunc == nil {
				return
			}
			typedObj := obj.(*unstructured.Unstructured)
			var crdObj SupportUIAccess
			err := crdObj.FromUnstructured(typedObj)
			if err != nil {
				log.Error().Err(err).Msg("Error converting to acl")
				return
			}
			f.DeleteFunc(&crdObj)
		},
	})
	return handle, err
}

func (t *sharedIndexSupportUIAccessInformer) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	return t.informer.RemoveEventHandler(r)
}

func CreateKubernetesClient(kubeconfig string) (*kubernetes.Clientset, error) {
	var config *rest.Config
	if kubeconfig != "" {
		log.Info().Msgf("Using kubeconfig file (%s) to create Kubernetes client", kubeconfig)
		configFlags, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, err
		}
		config = configFlags
	} else {
		restConfig, err := rest.InClusterConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating in-cluster config")
			return nil, err
		}
		config = restConfig
	}
	return kubernetes.NewForConfig(config)
}

// creates a dynamic client based on the given kubeconfig file.
// if the kubeconfig file is empty, the in-cluster config is used
func CreateDynamicClient(kubeconfig string) (*dynamic.DynamicClient, error) {
	var config *rest.Config
	if kubeconfig != "" {
		log.Info().Msgf("Using kubeconfig file (%s) to create dynamic client", kubeconfig)
		clusterConfig, err := clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
			&clientcmd.ClientConfigLoadingRules{ExplicitPath: kubeconfig},
			&clientcmd.ConfigOverrides{}).ClientConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating client config")
			return nil, err
		}
		config = clusterConfig
	} else {
		restConfig, err := rest.InClusterConfig()
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating in-cluster config")
			return nil, err
		}
		config = restConfig
	}

	return dynamic.NewForConfig(config)
}

// a fake SupportUIAccessInformer to be used for tests
type FakeSupportUIAccessInformer struct {
	acls          []SupportUIAccess
	eventHandlers []SupportUIAccessEventHandlerFuncs
}

func NewFakeSupportUIAccessInformer() *FakeSupportUIAccessInformer {
	return &FakeSupportUIAccessInformer{}
}

func (ts *FakeSupportUIAccessInformer) Update(t SupportUIAccess) {
	for i, existingAcl := range ts.acls {
		if existingAcl.Name == t.Name {
			ts.acls[i] = t
			for _, f := range ts.eventHandlers {
				f.UpdateFunc(&existingAcl, &t)
			}
			return
		}
	}

	ts.acls = append(ts.acls, t)
	for _, f := range ts.eventHandlers {
		f.AddFunc(&t, false)
	}
}

func (ts *FakeSupportUIAccessInformer) Delete(t SupportUIAccess) {
	log.Info().Msgf("Delete: %v", t)
	for i, existingAcl := range ts.acls {
		if existingAcl.Name == t.Name {
			// delete index i
			ts.acls = append(ts.acls[:i], ts.acls[i+1:]...)
			for _, f := range ts.eventHandlers {
				f.DeleteFunc(&t)
			}
			log.Info().Msgf("Deleted: %v", ts.acls)
			return
		}
	}
}

func (ts *FakeSupportUIAccessInformer) Run(stopCh <-chan struct{}) {
}

func (ts *FakeSupportUIAccessInformer) List() ([]SupportUIAccess, error) {
	return ts.acls, nil
}

func (ts *FakeSupportUIAccessInformer) AddEventHandler(f SupportUIAccessEventHandlerFuncs) (cache.ResourceEventHandlerRegistration, error) {
	ts.eventHandlers = append(ts.eventHandlers, f)
	for _, t := range ts.acls {
		f.AddFunc(&t, true)
	}
	return nil, nil
}

func (ts *FakeSupportUIAccessInformer) RemoveEventHandler(r cache.ResourceEventHandlerRegistration) error {
	return nil
}

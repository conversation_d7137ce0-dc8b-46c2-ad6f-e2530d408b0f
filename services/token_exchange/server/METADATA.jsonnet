// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'token-exchange',
      kubecfg: {
        target: '//services/token_exchange/server:kubecfg',
        task: [c for c in cloudInfo.centralNamespaces],
      },
      health: {
        tier: 'TIER_1_A',
        experts: {
          users: ['dirk', 'aswin', 'luke'],
          slack_channel: '#system-services',
        },
      },
      priority: 1,  // Try to run this early since it's a central dependency of other deploys
    },
  ],
}

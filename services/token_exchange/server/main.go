package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/augmentcode/augment/base/logging"
	tracing "github.com/augmentcode/augment/base/tracing/go"
	"github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tenantclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenproto "github.com/augmentcode/augment/services/token_exchange/proto"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	healthgrpc "google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
	"k8s.io/client-go/kubernetes"
)

var (
	configFile = flag.String("config", "", "Path to config file")
	kubeconfig = flag.String("kubeconfig", "", "Path to kubeconfig file")
)

type Config struct {
	TokenServerConfig

	// the port the grpc server will listen on
	Port int `json:"port"`

	// TLS configuration
	ServerMtls *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls *tlsconfig.ClientConfig `json:"client_mtls"`

	RequestInsightPublisherConfigPath string `json:"request_insight_publisher_config_path"`

	// Prometheus metrics port
	PromPort int `json:"prom_port"`

	// Tenant watcher
	TenantWatcherEndpoint string `json:"tenant_watcher_endpoint"`

	// namespace in which SupportUIAccess objects are expected.
	// set to empty string to listen for SupportUIAccess in all namespaces
	AclNamespace string `json:"acl_namespace"`

	// For tests, set AclNamespace to nonempty and this to true to skip the acl
	// check
	FakeAclNamespace bool `json:"fake_acl_namespace"`
}

func run(config *Config, namespace string, grpcServer *grpc.Server) error {
	// Create a channel to catch OS signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGTERM)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		return err
	}
	tenantWatcherClient := tenantclient.New(config.TenantWatcherEndpoint, grpc.WithTransportCredentials(clientCreds))

	// Pass an empty string to listen for tenants in all namespaces. Assumes
	// that we are running in a central namespace, which is the only type of
	// namespace that can listen to tenants in all namespaces
	tenantWatcher := tenantclient.NewTenantCache(tenantWatcherClient, "")
	defer tenantWatcher.Close()

	var requestInsightPublisher ripublisher.RequestInsightPublisher
	if config.RequestInsightPublisherConfigPath == "" {
		// This is expected to happen in GSC, where we don't have the RI pipeline set up.
		log.Warn().Msg("Request insight publisher disabled")
	} else {
		requestInsightPublisher, err = ripublisher.NewRequestInsightPublisherFromFile(
			ctx, config.RequestInsightPublisherConfigPath)
		if err != nil {
			log.Fatal().Err(err).Msg("Error creating request insight publisher")
		}
		defer requestInsightPublisher.Close()
	}

	var aclCheck AclCheck
	var informer SupportUIAccessInformer
	if config.AclNamespace == "fake" && config.FakeAclNamespace {
		informer = NewFakeSupportUIAccessInformer()
	} else {
		clientset, err := CreateDynamicClient(*kubeconfig)
		if err != nil {
			return err
		}
		if config.AclNamespace != "" {
			informer = NewFilteredSupportUIAccessInformer(clientset, 0, config.AclNamespace)
		} else {
			informer = NewSupportUIAccessInformer(clientset, 0)
		}
	}
	go informer.Run(ctx.Done())
	aclCheck = &InformerAclCheck{
		informer:            informer,
		trustedAclNamespace: config.TrustedAclNamespace,
	}

	var kubeClientset *kubernetes.Clientset
	if config.ReadKeysFromConfigMap {
		var err error
		kubeClientset, err = CreateKubernetesClient(*kubeconfig)
		if err != nil {
			return err
		}
	}
	serviceCacheExpiration := config.TokenServerConfig.ServiceCacheExpiration.ToDuration()
	for _, service := range config.TokenServerConfig.ServiceTokenConfigs {
		if service.Expiration.ToDuration() < serviceCacheExpiration {
			log.Fatal().Msgf("Service token TTL %v is less than service token cache TTL %v", service.Expiration, serviceCacheExpiration)
		}
	}
	tokenServer, err := newServer(&config.TokenServerConfig, namespace, kubeClientset, tenantWatcher, aclCheck, requestInsightPublisher)
	if err != nil {
		return err
	}

	defer tokenServer.Close()

	tokenproto.RegisterTokenExchangeServer(grpcServer, tokenServer)
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())

	go func() {
		// Wait for either a shutdown signal or an OS signal
		sig := <-sigChan
		log.Info().Msgf("Received signal: %v", sig)
		grpcServer.GracefulStop()
	}()

	err = grpcServer.Serve(lis)
	if err != nil && err != grpc.ErrServerStopped {
		log.Fatal().Err(err).Msg("Error serving")
	}
	log.Info().Msg("gRPC server closed")
	return err
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
	}
	log.Info().Msgf("Config: %v", config)

	namespace, exists := os.LookupEnv("POD_NAMESPACE")
	if !exists {
		panic("POD_NAMESPACE environment variable not set")
	}

	tracingShutdown := tracing.Init()
	defer tracingShutdown()

	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	// Setup metrics.
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(),
	)
	prometheus.MustRegister(srvMetrics)

	var opts []grpc.ServerOption
	opts = append(opts, grpc.StatsHandler(otelgrpc.NewServerHandler()))
	serverTls, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{config.ServerMtls})
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating TLS config")
	}
	opts = append(opts, grpc.Creds(serverTls))
	opts = append(opts, grpc.ChainUnaryInterceptor(
		recovery.UnaryServerInterceptor(),
		srvMetrics.UnaryServerInterceptor(),
	))
	opts = append(opts, grpc.ChainStreamInterceptor(
		recovery.StreamingServerInterceptor(),
		srvMetrics.StreamServerInterceptor(),
	))

	grpcServer := grpc.NewServer(opts...)
	// setup prometheus metrics for GRPC calls
	srvMetrics.InitializeMetrics(grpcServer)

	// setup reflection for debugging
	reflection.Register(grpcServer)
	// setup health service
	healthgrpc.RegisterHealthServer(grpcServer, health.NewServer())

	err = run(&config, namespace, grpcServer)
	if err != nil {
		log.Fatal().Err(err).Msg("Error serving")
	}
}

load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/prompt_format:common",
        "//base/python/grpc:client_options",
        "//base/third_party_clients:third_party_model_client",
        "//services/lib/proto:chat_py_proto",
        "//services/lib/request_context:request_context_py",
        "//services/thirdparty_proxy:thirdparty_proxy_py_proto",
        requirement("grpcio"),
        requirement("structlog"),
    ],
)

pytest_test(
    name = "client_test",
    srcs = ["client_test.py"],
    deps = [
        ":client_py",
    ],
)

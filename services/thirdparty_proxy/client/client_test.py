"""Unit tests for the ThirdPartyProxyClient."""

import json
import pytest
from unittest.mock import MagicMock, patch

from base.prompt_format.common import StopReason

from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelResponse as BaseThirdPartyModelResponse,
    ToolDefinition as BaseToolDefinition,
    ToolChoice as BaseToolChoice,
    ToolChoiceType as BaseToolChoiceType,
    ToolUseResponse as BaseToolUseResponse,
    EndOfStream as BaseEndOfStream,
    Exchange as BaseExchange,
)

from services.thirdparty_proxy.thirdparty_proxy_pb2 import (
    ThirdPartyResponse as ProtoThirdPartyResponse,
    Exchange as ProtoExchange,
    ToolUseResponse as ProtoToolUseResponse,
)

from services.lib.proto.chat_pb2 import (
    ToolChoiceType as ProtoToolChoiceType,
    ToolChoice as ProtoToolChoice,
)
from services.thirdparty_proxy.client.client import ThirdPartyProxyClient


@pytest.fixture
def client():
    """Create a ThirdPartyProxyClient with a mock stub."""
    stub = MagicMock()
    client = ThirdPartyProxyClient("localhost:50051", None)
    client.stub = stub
    return client


def test_convert_third_party_response_to_base(client):
    """Test conversion of a response from proto to base."""
    # Create a simple proto response
    proto_response = ProtoThirdPartyResponse(text="test response")

    # Create a complex tool input dictionary with nested structures
    tool_input = {
        "command": "str_replace",
        "path": "services/remote_agents/remote_agents.proto",
        "str_replace_entries": [
            {
                "old_str": "message WorkspaceSetup {\n  oneof starting_files {\n    GithubRef github_ref = 1;\n  }\n  // TODO: add more here, like setup commands, env vars, etc?\n  optional string patch = 2;\n  optional string setup_script = 3;\n  optional string token = 4 [debug_redact = true];",
                "new_str": "message WorkspaceSetup {\n  oneof starting_files {\n    GithubRef github_ref = 1;\n  }\n  // TODO: add more here, like setup commands, env vars, etc?\n  optional string patch = 2;\n  optional string setup_script = 3;\n  optional string token = 4 [debug_redact = true];\n  optional string api_url = 5;",
                "old_str_start_line_number": 147,
                "old_str_end_line_number": 153,
            }
        ],
    }

    # Add tool use with JSON input
    proto_response.tool_use.tool_name = "test_tool"
    proto_response.tool_use.input_json = json.dumps(tool_input)
    proto_response.tool_use.tool_use_id = "test_id_123"

    # Mock the stub's response
    client.stub.GenerateStream.return_value = [proto_response]

    # Call the generate_response_stream method which uses convert_third_party_response_to_base
    responses = list(
        client.generate_response_stream(
            model_caller="test", model_name="test_model", cur_message="test message"
        )
    )

    # Verify we got a response
    assert len(responses) == 1
    base_response = responses[0]

    # Verify the conversion of text
    assert base_response.text == "test response"

    # Verify the conversion of tool_use
    assert base_response.tool_use is not None
    assert base_response.tool_use.tool_name == "test_tool"
    assert base_response.tool_use.tool_use_id == "test_id_123"

    # Verify the JSON deserialization of complex tool input
    assert isinstance(base_response.tool_use.input, dict)
    assert "command" in base_response.tool_use.input
    assert base_response.tool_use.input["command"] == "str_replace"
    assert "path" in base_response.tool_use.input
    assert (
        base_response.tool_use.input["path"]
        == "services/remote_agents/remote_agents.proto"
    )

    # Verify nested structures
    assert "str_replace_entries" in base_response.tool_use.input
    assert isinstance(base_response.tool_use.input["str_replace_entries"], list)
    assert len(base_response.tool_use.input["str_replace_entries"]) == 1

    entry = base_response.tool_use.input["str_replace_entries"][0]
    assert "old_str" in entry
    assert "new_str" in entry
    assert "old_str_start_line_number" in entry
    assert entry["old_str_start_line_number"] == 147
    assert "old_str_end_line_number" in entry
    assert entry["old_str_end_line_number"] == 153


def test_convert_complex_response(client):
    """Test conversion of a complex response from proto to base."""
    # Create a complex proto response with all fields
    tool_input = {"param1": "value1", "param2": 42}
    proto_response = ProtoThirdPartyResponse(text="test response")

    # Add tool use with JSON input
    proto_response.tool_use.tool_name = "test_tool"
    proto_response.tool_use.input_json = json.dumps(tool_input)
    proto_response.tool_use.tool_use_id = "test_id_123"

    # Add end of stream
    proto_response.end_of_stream.SetInParent()

    # Create the expected response
    expected_response = BaseThirdPartyModelResponse(
        text="test response",
        tool_use=BaseToolUseResponse(
            tool_name="test_tool", input=tool_input, tool_use_id="test_id_123"
        ),
        end_of_stream=BaseEndOfStream(stop_reason=StopReason.END_TURN),
    )

    # Mock the stub's response
    client.stub.GenerateStream.return_value = [proto_response]

    # Mock the convert_third_party_response_to_base method to return our expected response
    with patch.object(
        client, "convert_third_party_response_to_base", return_value=expected_response
    ):
        # Call generate_response_stream which uses convert_third_party_response_to_base
        responses = list(
            client.generate_response_stream(
                model_caller="test", model_name="test_model", cur_message="test message"
            )
        )

        # Verify we got a response
        assert len(responses) == 1
        base_response = responses[0]

        # Verify the conversion
        assert isinstance(base_response, BaseThirdPartyModelResponse)
        assert base_response.text == "test response"

        # Verify tool_use
        assert base_response.tool_use is not None
        assert base_response.tool_use.tool_name == "test_tool"
        assert base_response.tool_use.tool_use_id == "test_id_123"
        assert base_response.tool_use.input == tool_input

        # Verify end_of_stream
        assert base_response.end_of_stream is not None


def test_tool_choice_conversion(client):
    """Test conversion of tool choice objects."""
    base_auto = BaseToolChoice(type=BaseToolChoiceType.AUTO)
    proto_auto = client.convert_tool_choice(base_auto)
    assert proto_auto.type == ProtoToolChoiceType.AUTO

    base_any = BaseToolChoice(type=BaseToolChoiceType.ANY)
    proto_any = client.convert_tool_choice(base_any)
    assert proto_any.type == ProtoToolChoiceType.ANY

    base_tool = BaseToolChoice(type=BaseToolChoiceType.TOOL, name="test_tool")
    proto_tool = client.convert_tool_choice(base_tool)
    assert proto_tool.type == ProtoToolChoiceType.TOOL
    assert proto_tool.name == "test_tool"


def test_tool_use_response_in_history(client):
    """Test that tool use responses are correctly preserved in history."""
    # Create a complex tool use response in proto format with nested structures
    tool_input = {
        "command": "str_replace",
        "path": "services/remote_agents/remote_agents.proto",
        "str_replace_entries": [
            {
                "old_str": "message WorkspaceSetup {\n  oneof starting_files {\n    GithubRef github_ref = 1;\n  }\n  // TODO: add more here, like setup commands, env vars, etc?\n  optional string patch = 2;\n  optional string setup_script = 3;\n  optional string token = 4 [debug_redact = true];",
                "new_str": "message WorkspaceSetup {\n  oneof starting_files {\n    GithubRef github_ref = 1;\n  }\n  // TODO: add more here, like setup commands, env vars, etc?\n  optional string patch = 2;\n  optional string setup_script = 3;\n  optional string token = 4 [debug_redact = true];\n  optional string api_url = 5;",
                "old_str_start_line_number": 147,
                "old_str_end_line_number": 153,
            }
        ],
    }

    proto_tool_use = ProtoToolUseResponse(
        tool_name="str_replace_editor",
        input_json=json.dumps(tool_input),
        tool_use_id="tool-123456789",
    )

    # Create a proto response with tool use
    proto_response = ProtoThirdPartyResponse(text="test response")
    proto_response.tool_use.CopyFrom(proto_tool_use)

    # Convert to base response
    base_response = client.convert_third_party_response_to_base(proto_response)

    # Verify the base response has the correct tool use
    assert base_response.tool_use is not None
    assert base_response.tool_use.tool_name == "str_replace_editor"
    assert base_response.tool_use.tool_use_id == "tool-123456789"

    # Verify the complex input structure is preserved
    assert base_response.tool_use.input == tool_input
    assert "command" in base_response.tool_use.input
    assert base_response.tool_use.input["command"] == "str_replace"
    assert "path" in base_response.tool_use.input
    assert "str_replace_entries" in base_response.tool_use.input
    assert len(base_response.tool_use.input["str_replace_entries"]) == 1

    # Verify nested structure details
    entry = base_response.tool_use.input["str_replace_entries"][0]
    assert entry["old_str_start_line_number"] == 147
    assert entry["old_str_end_line_number"] == 153
    assert "old_str" in entry
    assert "new_str" in entry

    # Verify the content of old_str and new_str
    assert "message WorkspaceSetup {" in entry["old_str"]
    assert "optional string token = 4 [debug_redact = true];" in entry["old_str"]
    assert "optional string api_url = 5;" in entry["new_str"]
    assert entry["new_str"].endswith("optional string api_url = 5;")

    # Create a history exchange with this response
    # Note: We use a string for response_text instead of the ThirdPartyModelResponse
    # because the convert_response_mesage_to_proto method expects an iterable
    # or a string, not a ThirdPartyModelResponse object
    base_exchange = BaseExchange(
        request_message="Please edit the remote_agents.proto file to add an api_url field",
        response_text=str(base_response),  # Convert to string to avoid iteration error
    )

    # Convert the exchange to proto format
    proto_exchanges = client.convert_chat_history_to_proto([base_exchange])

    # Verify the exchange was converted correctly
    assert len(proto_exchanges) == 1
    proto_exchange = proto_exchanges[0]

    # Verify the request message
    assert (
        proto_exchange.request_message.text
        == "Please edit the remote_agents.proto file to add an api_url field"
    )

    # Verify the response message - since we converted to string, we should get the string back
    assert proto_exchange.response_message.text == str(base_response)

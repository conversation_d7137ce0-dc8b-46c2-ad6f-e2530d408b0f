import argparse
import json
from typing import Generator, Iterable, List, Optional, Sequence, Union, cast

import grpc
import structlog

import base.python.grpc.client_options as client_options
from base.prompt_format.common import (
    ChatRequestImage as BaseChatRequestImage,
    ChatRequestNode as BaseChatRequestNode,
    ChatRequestNodeType as BaseChatRequestNodeType,
    ChatRequestText as BaseChatRequestText,
    ChatRequestToolResult as BaseChatRequestToolResult,
    ChatResultNode as BaseChatResultNode,
    ChatResultNodeType as BaseChatResultNodeType,
    ChatResultToolUse as BaseChatResultToolUse,
    Exchange as BaseExchange,
    ImageFormatType as BaseImageFormatType,
    RequestMessage as BaseRequestMessage,
    ResponseMessage as BaseResponseMessage,
    StopReason,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream as BaseEndOfStream,
    PromptCacheUsage as BasePromptCacheUsage,
    ReplaceTextResponse as BaseReplaceTextResponse,
    ThirdPartyModelResponse as BaseThirdPartyModelResponse,
    ToolChoice as BaseToolChoice,
    ToolChoiceType as BaseToolChoiceType,
    ToolDefinition as BaseToolDefinition,
    ToolUseResponse as BaseToolUseResponse,
    ToolUseStart as BaseToolUseStart,
)

from services.lib.request_context.request_context import RequestContext
from services.thirdparty_proxy.thirdparty_proxy_pb2 import (
    ChatRequestEditEvents as ProtoChatRequestEditEvents,
    ChatRequestFileEdit as ProtoChatRequestFileEdit,
    ChatRequestIdeState as ProtoChatRequestIdeState,
    ChatRequestImage as ProtoChatRequestImage,
    ChatRequestText as ProtoChatRequestText,
    ChatRequestSingleEdit as ProtoChatRequestSingleEdit,
    ChatRequestToolResult as ProtoChatRequestToolResult,
    ImageFormatType as ProtoImageFormatType,
    TerminalInfo as ProtoTerminalInfo,
    ToolChoice as ProtoToolChoice,
    ToolChoiceType as ProtoToolChoiceType,
    ToolDefinition as ProtoToolDefinition,
    WorkspaceFolderInfo as ProtoWorkspaceFolderInfo,
    ChatRequestNode as ProtoChatRequestNode,
    ChatResultNode as ProtoChatResultNode,
    ChatResultNodeType as ProtoChatResultNodeType,
    ChatResultToolUse as ProtoChatResultToolUse,
    Exchange as ProtoExchange,
    RequestMessage as ProtoRequestMessage,
    ResponseMessage as ProtoResponseMessage,
    ThirdPartyRequest as ProtoThirdPartyRequest,
    ThirdPartyResponse as ProtoThirdPartyResponse,
    EndOfStream as ProtoEndOfStream,
    ChatStopReason as ProtoChatStopReason,
)
from services.thirdparty_proxy.thirdparty_proxy_pb2_grpc import (
    ThirdPartyProxyStub,
)

logger = structlog.get_logger()


def setup_stub(
    endpoint: str, credentials: grpc.ChannelCredentials | None
) -> ThirdPartyProxyStub:
    """Setup the client stub for a thirdparty proxy service."""
    logger.info("Creating grpc client to %s with options %s", endpoint, [])
    if not credentials:
        channel = grpc.insecure_channel(endpoint, options=client_options.create())
    else:
        channel = grpc.secure_channel(
            endpoint, credentials, options=client_options.create()
        )
    stub = ThirdPartyProxyStub(channel)
    return stub


class ThirdPartyProxyClient:
    """
    A client for the thirdparty proxy service.
    """

    def __init__(self, endpoint: str, credentials: grpc.ChannelCredentials | None):
        self.stub = setup_stub(endpoint, credentials)

    def convert_request_message_to_proto(
        self, message: BaseRequestMessage
    ) -> ProtoRequestMessage:
        """Convert a base RequestMessage to a thirdparty_proxy RequestMessage.

        Args:
            message: The base RequestMessage to convert.

        Returns:
            A thirdparty_proxy RequestMessage.
        """
        # If the message is a string, create a simple text message
        if isinstance(message, str):
            result = ProtoRequestMessage(text=message)
            return result

        # Handle structured messages with nodes
        # Convert each node in the message
        proto_nodes = []

        for node in message:
            # Skip if node is not a ChatRequestNode
            if not hasattr(node, "type"):
                continue

            # Create a new proto node
            proto_node = ProtoChatRequestNode()

            # Convert the node type
            if node.type == BaseChatRequestNodeType.TEXT:
                proto_node.id = node.id
                proto_node.type = ProtoChatRequestNode.NodeType.TEXT
                if hasattr(node, "text_node") and node.text_node is not None:
                    text_node = ProtoChatRequestText()
                    text_node.content = node.text_node.content
                    proto_node.text_node.CopyFrom(text_node)
                else:
                    continue
            elif node.type == BaseChatRequestNodeType.TOOL_RESULT:
                proto_node.id = node.id
                proto_node.type = ProtoChatRequestNode.NodeType.TOOL_RESULT
                if (
                    hasattr(node, "tool_result_node")
                    and node.tool_result_node is not None
                ):
                    tool_result_node = ProtoChatRequestToolResult()
                    tool_result_node.tool_use_id = node.tool_result_node.tool_use_id
                    tool_result_node.content = node.tool_result_node.content
                    tool_result_node.is_error = node.tool_result_node.is_error
                    if node.tool_result_node.request_id is not None:
                        tool_result_node.request_id = node.tool_result_node.request_id
                    proto_node.tool_result_node.CopyFrom(tool_result_node)
                else:
                    continue
            elif node.type == BaseChatRequestNodeType.IMAGE:
                proto_node.id = node.id
                proto_node.type = ProtoChatRequestNode.NodeType.IMAGE
                if hasattr(node, "image_node") and node.image_node is not None:
                    image_node = ProtoChatRequestImage()
                    image_node.image_data = node.image_node.image_data
                    # Convert format
                    image_node.format = ProtoImageFormatType.Value(
                        node.image_node.format.name
                    )
                    proto_node.image_node.CopyFrom(image_node)
                else:
                    continue
            elif node.type == BaseChatRequestNodeType.IDE_STATE:
                proto_node.id = node.id
                proto_node.type = ProtoChatRequestNode.NodeType.IDE_STATE
                if hasattr(node, "ide_state_node") and node.ide_state_node is not None:
                    ide_state_node = ProtoChatRequestIdeState()
                    # Convert workspace folders
                    for folder in node.ide_state_node.workspace_folders:
                        proto_folder = ProtoWorkspaceFolderInfo()
                        proto_folder.repository_root = folder.repository_root
                        proto_folder.folder_root = folder.folder_root
                        ide_state_node.workspace_folders.append(proto_folder)

                    ide_state_node.workspace_folders_unchanged = (
                        node.ide_state_node.workspace_folders_unchanged
                    )

                    # Convert terminal info if present
                    if node.ide_state_node.current_terminal is not None:
                        terminal = ProtoTerminalInfo()
                        terminal.terminal_id = (
                            node.ide_state_node.current_terminal.terminal_id
                        )
                        terminal.current_working_directory = node.ide_state_node.current_terminal.current_working_directory
                        ide_state_node.current_terminal.CopyFrom(terminal)

                    proto_node.ide_state_node.CopyFrom(ide_state_node)
                else:
                    continue
            elif node.type == BaseChatRequestNodeType.EDIT_EVENTS:
                proto_node.id = node.id
                proto_node.type = ProtoChatRequestNode.NodeType.EDIT_EVENTS
                if (
                    hasattr(node, "edit_events_node")
                    and node.edit_events_node is not None
                ):
                    edit_events_node = ProtoChatRequestEditEvents()
                    # Convert edit events
                    for event in node.edit_events_node.edit_events:
                        proto_file_edit = ProtoChatRequestFileEdit()
                        proto_file_edit.path = event.path
                        if event.before_blob_name is not None:
                            proto_file_edit.before_blob_name = event.before_blob_name
                        if event.after_blob_name is not None:
                            proto_file_edit.after_blob_name = event.after_blob_name

                        # Convert individual edits within the file
                        for edit in event.edits:
                            proto_edit = ProtoChatRequestSingleEdit()
                            proto_edit.before_line_start = edit.before_line_start
                            proto_edit.before_text = edit.before_text
                            proto_edit.after_line_start = edit.after_line_start
                            proto_edit.after_text = edit.after_text
                            proto_file_edit.edits.append(proto_edit)

                        edit_events_node.edit_events.append(proto_file_edit)

                    proto_node.edit_events_node.CopyFrom(edit_events_node)
                else:
                    continue
            else:
                # Skip unknown node types
                continue

            proto_nodes.append(proto_node)

        result = ProtoRequestMessage(nodes=proto_nodes)
        return result

    def convert_response_mesage_to_proto(
        self, message: BaseResponseMessage
    ) -> ProtoResponseMessage:
        """Convert a base ResponseMessage to a thirdparty_proxy ResponseMessage.

        Args:
            message: The base ResponseMessage to convert.

        Returns:
            A thirdparty_proxy ResponseMessage.
        """
        # If the message is a string, create a simple text message
        if isinstance(message, str):
            return ProtoResponseMessage(text=message)

        # Handle structured messages with nodes
        proto_nodes = []

        for node in message:
            # Skip if node is not a ChatResultNode
            if not hasattr(node, "type"):
                continue

            # Create a new proto node
            proto_node = ProtoChatResultNode()

            proto_node.id = node.id
            proto_node.type = ProtoChatResultNodeType.Value(node.type.name)
            proto_node.content = node.content

            if node.tool_use is not None:
                tool_use = ProtoChatResultToolUse()
                tool_use.tool_name = node.tool_use.name
                tool_use.input_json = json.dumps(node.tool_use.input)
                tool_use.tool_use_id = node.tool_use.tool_use_id
                proto_node.tool_use.CopyFrom(tool_use)

            if node.final_parameters is not None:
                for key, value in node.final_parameters.items():
                    proto_node.final_parameters[key] = str(value)

            proto_nodes.append(proto_node)

        return ProtoResponseMessage(nodes=proto_nodes)

    def convert_third_party_response_to_base(
        self, response: ProtoThirdPartyResponse
    ) -> BaseThirdPartyModelResponse:
        """
        Convert a ThirdPartyResponse proto to a ThirdPartyModelResponse.

        Args:
            response: The ThirdPartyResponse proto to convert.

        Returns:
            A ThirdPartyModelResponse.
        """
        # Convert replace_text_response if it exists
        replace_text_response = None
        if response.HasField("replace_text_response"):
            replace_text_response = BaseReplaceTextResponse(
                old_text=response.replace_text_response.old_text,
                replacement_text=response.replace_text_response.replacement_text,
                start_line_number=response.replace_text_response.start_line_number,
                end_line_number=response.replace_text_response.end_line_number,
                sequence_id=response.replace_text_response.sequence_id,
            )

        # Convert tool_use if it exists
        tool_use = None
        if response.HasField("tool_use"):
            input_dict = json.loads(response.tool_use.input_json)
            tool_use = BaseToolUseResponse(
                tool_name=response.tool_use.tool_name,
                input=input_dict,
                tool_use_id=response.tool_use.tool_use_id,
            )

        # Convert prompt_cache_usage if it exists
        prompt_cache_usage = None
        if response.HasField("prompt_cache_usage"):
            prompt_cache_usage = BasePromptCacheUsage(
                input_tokens=response.prompt_cache_usage.input_tokens,
                cache_creation_input_tokens=response.prompt_cache_usage.cache_creation_input_tokens,
                cache_read_input_tokens=response.prompt_cache_usage.cache_read_input_tokens,
            )

        # Convert final_parameters if it exists
        final_parameters = None
        if response.final_parameters:
            final_parameters = {}
            for key, value in response.final_parameters.items():
                final_parameters[key] = value

        # Convert end_of_stream if it exists
        end_of_stream = None
        if response.HasField("end_of_stream"):
            # Map the proto StopReason to the base StopReason
            stop_reason_map = {
                ProtoChatStopReason.REASON_UNSPECIFIED: StopReason.REASON_UNSPECIFIED,
                ProtoChatStopReason.END_TURN: StopReason.END_TURN,
                ProtoChatStopReason.MAX_TOKENS: StopReason.MAX_TOKENS,
                ProtoChatStopReason.TOOL_USE_REQUESTED: StopReason.TOOL_USE_REQUESTED,
            }

            stop_reason = stop_reason_map.get(response.end_of_stream.stop_reason, None)
            if stop_reason is None:
                raise ValueError(
                    f"Unknown stop reason {response.end_of_stream.stop_reason}"
                )

            end_of_stream = BaseEndOfStream(stop_reason=stop_reason)

        # Create a tool_use_start if tool_use exists but we don't have any other fields
        # This is a special case for when the model starts using a tool but hasn't provided inputs yet
        tool_use_start = None
        if tool_use and not tool_use.input:
            tool_use_start = BaseToolUseStart(
                tool_name=tool_use.tool_name,
                tool_use_id=tool_use.tool_use_id,
            )

        return BaseThirdPartyModelResponse(
            text=response.text if response.HasField("text") else "",
            replace_text_response=replace_text_response,
            tool_use=tool_use,
            tool_use_start=tool_use_start,
            prompt_cache_usage=prompt_cache_usage,
            final_parameters=final_parameters,
            end_of_stream=end_of_stream,
        )

    def convert_chat_history_to_proto(
        self, chat_history: Sequence[BaseExchange]
    ) -> List[ProtoExchange]:
        """Convert a list of base Exchanges to thirdparty_proxy Exchanges.

        Args:
            chat_history: The list of base Exchanges to convert.

        Returns:
            A list of thirdparty_proxy Exchanges.
        """
        tp_chat_history = []
        for exchange in chat_history:
            tp_exchange = ProtoExchange(
                request_message=self.convert_request_message_to_proto(
                    exchange.request_message
                ),
                response_message=self.convert_response_mesage_to_proto(
                    exchange.response_text
                ),
            )
            tp_chat_history.append(tp_exchange)
        return tp_chat_history

    def convert_tool_definitions(
        self, tool_definitions: Sequence[BaseToolDefinition]
    ) -> List[ProtoToolDefinition]:
        """Convert a list of base ToolDefinitions to thirdparty_proxy ToolDefinitions.

        Args:
            tool_definitions: The list of base ToolDefinitions to convert.

        Returns:
            A list of thirdparty_proxy ToolDefinitions.
        """
        tp_tool_definitions = []
        for tool_def in tool_definitions:
            tp_tool_def = ProtoToolDefinition(
                name=tool_def.name,
                description=tool_def.description,
                input_schema_json=tool_def.input_schema_json,
            )
            tp_tool_definitions.append(tp_tool_def)
        return tp_tool_definitions

    def convert_tool_choice(
        self, tool_choice: Optional[BaseToolChoice]
    ) -> Optional[ProtoToolChoice]:
        """Convert a base ToolChoice to a thirdparty_proxy ToolChoice.

        Args:
            tool_choice: The base ToolChoice to convert.

        Returns:
            A thirdparty_proxy ToolChoice, or None if tool_choice is None.
        """
        if tool_choice is None:
            return None

        # Create a new ToolChoice with the appropriate type and name
        tp_tool_choice = ProtoToolChoice()

        # Map from BaseToolChoiceType to ProtoToolChoiceType
        # BaseToolChoiceType: AUTO = 1, ANY = 2, TOOL = 3
        # ProtoToolChoiceType: AUTO = 0, ANY = 1, TOOL = 2
        if tool_choice.type == BaseToolChoiceType.AUTO:
            tp_tool_choice.type = ProtoToolChoiceType.AUTO
        elif tool_choice.type == BaseToolChoiceType.ANY:
            tp_tool_choice.type = ProtoToolChoiceType.ANY
        elif tool_choice.type == BaseToolChoiceType.TOOL:
            tp_tool_choice.type = ProtoToolChoiceType.TOOL
            tp_tool_choice.name = (
                tool_choice.name if tool_choice.name is not None else ""
            )
        else:
            raise ValueError(f"Unknown tool choice type: {tool_choice.type}")

        return tp_tool_choice

    def generate_response_stream(
        self,
        model_caller: str,
        model_name: str,
        cur_message: BaseRequestMessage,
        system_prompt: str | None = "",
        chat_history: List[BaseExchange] | None = None,
        tools: List[str] = [],
        tool_definitions: List[BaseToolDefinition] = [],
        tool_choice: BaseToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: RequestContext | None = None,
        yield_final_parameters: bool = False,
    ) -> Generator[BaseThirdPartyModelResponse, None, None]:
        """
        Generate a response based on the given message.

        Args:
            model_caller: The name of the model caller.
            model_name: The name of the model to use.
            cur_message: The current message to generate a response for.
            system_prompt: The system prompt to use for the model.
            chat_history: A structured format of messages.
            tools: List of tool names to use for the model.
            tool_definitions: List of tool definitions.
            tool_choice: Tool selection parameters.
            temperature: Temperature parameter for response generation.
            max_output_tokens: Maximum number of tokens in the response.
            prefill: Prefilled text to start the response with.
            use_caching: Whether to use response caching.
            request_context: The request context.
            yield_final_parameters: Whether to yield a response with the final parameters used for the request.

        Returns:
            Generator for the generated response text.
        """
        tp_cur_message = self.convert_request_message_to_proto(cur_message)

        tp_chat_history = self.convert_chat_history_to_proto(chat_history or [])

        tp_tool_definitions = self.convert_tool_definitions(tool_definitions)

        tp_tool_choice = self.convert_tool_choice(tool_choice)

        # Create the request
        request = ProtoThirdPartyRequest(
            model_caller=model_caller,
            model_name=model_name,
            cur_message=tp_cur_message,
            system_prompt=system_prompt,
            chat_history=tp_chat_history,
            tools=tools,
            tool_definitions=tp_tool_definitions,
            tool_choice=tp_tool_choice,
            temperature=temperature,
            max_output_tokens=max_output_tokens,
            prefill=prefill,
            use_caching=use_caching,
            yield_final_parameters=yield_final_parameters,
        )

        if request_context is None:
            request_context = RequestContext.create()

        response_stream = self.stub.GenerateStream(
            request, metadata=request_context.to_metadata()
        )
        for response_message in response_stream:
            yield self.convert_third_party_response_to_base(response_message)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="ThirdPartyProxy Client")
    parser.add_argument("--server", default="localhost:50051", help="Server address")
    parser.add_argument("--model-name", default="", help="Model name")
    parser.add_argument(
        "--message",
        default="Tell me a short story about a robot learning to paint.",
        help="Prompt to send",
    )
    parser.add_argument(
        "--system-prompt",
        default="You are a helpful assistant.",
        help="System prompt to send",
    )

    args = parser.parse_args()

    client = ThirdPartyProxyClient(args.server, None)
    response_stream = client.generate_response_stream(
        model_caller="test-client",
        model_name=args.model_name,
        cur_message=args.message,  # Just use the string directly
        system_prompt=args.system_prompt,
    )
    for response in response_stream:
        if response.text:
            print(response.text, end="", flush=True)

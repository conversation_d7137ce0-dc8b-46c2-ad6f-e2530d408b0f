syntax = "proto3";

package thirdparty_proxy;

import public "services/lib/proto/chat.proto";

option go_package = "github.com/augmentcode/augment/services/thirdparty_proxy/proto";

service ThirdPartyProxy {
  rpc GenerateStream(ThirdPartyRequest) returns (stream ThirdPartyResponse);
}

message ThirdPartyRequest {
  string model_caller = 1;
  string model_name = 2;
  RequestMessage cur_message = 3 [debug_redact = true];
  optional string system_prompt = 4 [debug_redact = true];
  repeated Exchange chat_history = 5 [debug_redact = true];
  repeated string tools = 6; // Backend-defined tools
  repeated chat.ToolDefinition tool_definitions = 7 [debug_redact = true]; // Client supplied tools
  optional chat.ToolChoice tool_choice = 8;
  optional float temperature = 9;
  optional int32 max_output_tokens = 10;
  optional string prefill = 11 [debug_redact = true];
  optional bool use_caching = 12;
  optional bool yield_final_parameters = 13;
}

message EndOfStream {
  chat.ChatStopReason stop_reason = 1;
}

message ThirdPartyResponse {
  optional string text = 1 [debug_redact = true];
  optional ReplaceTextResponse replace_text_response = 2 [debug_redact = true];
  optional ToolUseResponse tool_use = 3 [debug_redact = true];
  optional PromptCacheUsage prompt_cache_usage = 4;
  map<string, string> final_parameters = 5 [debug_redact = true];
  optional EndOfStream end_of_stream = 6;
}

message RequestMessage {
  optional string text = 1 [debug_redact = true];
  repeated ChatRequestNode nodes = 2 [debug_redact = true];
}

message ChatResultNode {
  int32 id = 1;
  chat.ChatResultNodeType type = 2;
  string content = 3 [debug_redact = true];
  optional chat.ChatResultToolUse tool_use = 4 [debug_redact = true];
  map<string, string> final_parameters = 5 [debug_redact = true];
}

message ResponseMessage {
  string text = 1 [debug_redact = true];
  repeated ChatResultNode nodes = 2 [debug_redact = true];
}

message Exchange {
  RequestMessage request_message = 1 [debug_redact = true];
  ResponseMessage response_message = 2 [debug_redact = true];
}

message ChatRequestNode {
  enum NodeType {
    TEXT = 0;
    TOOL_RESULT = 1;
    IMAGE = 2;
    IDE_STATE = 4;
    EDIT_EVENTS = 5;
  }
  int32 id = 1;
  NodeType type = 2;
  optional chat.ChatRequestText text_node = 3 [debug_redact = true];
  optional chat.ChatRequestToolResult tool_result_node = 4 [debug_redact = true];
  optional chat.ChatRequestImage image_node = 5 [debug_redact = true];
  optional chat.ChatRequestIdeState ide_state_node = 6;
  optional chat.ChatRequestEditEvents edit_events_node = 7;
}

message ReplaceTextResponse {
  optional string old_text = 1 [debug_redact = true];
  optional string replacement_text = 2 [debug_redact = true];
  optional int32 start_line_number = 3;
  optional int32 end_line_number = 4;
  optional int32 sequence_id = 5;
}

message ToolUseResponse {
  string tool_name = 1;
  string input_json = 2 [debug_redact = true];
  string tool_use_id = 3;
}

message PromptCacheUsage {
  int32 input_tokens = 1;
  int32 cache_creation_input_tokens = 2;
  int32 cache_read_input_tokens = 3;
}

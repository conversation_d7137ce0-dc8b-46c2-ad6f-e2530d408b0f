load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "thirdparty_proxy_proto",
    srcs = ["thirdparty_proxy.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/proto:chat_proto",
    ],
)

py_grpc_library(
    name = "thirdparty_proxy_py_proto",
    protos = [":thirdparty_proxy_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/proto:chat_py_proto",
    ],
)

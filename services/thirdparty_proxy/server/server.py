import argparse
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from dataclasses import dataclass
import pathlib
from dataclasses_json import dataclass_json
import grpc
import logging
from prometheus_client import Counter, Histogram, start_http_server
import os
import threading

import opentelemetry
import opentelemetry.instrumentation.grpc
import structlog
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import base.feature_flags
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.opentelemetry_utils.traced_threadpool import TracedThreadPoolExecutor
from base.python.signal_handler.signal_handler import GracefulSignalHandler
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

from services.thirdparty_proxy.thirdparty_proxy_pb2_grpc import (
    add_ThirdPartyProxyServicer_to_server,
)
from services.thirdparty_proxy import thirdparty_proxy_pb2
from services.thirdparty_proxy.server.thirdparty_proxy_handler import (
    HandlerConfig,
    ThirdPartyProxyHandler,
)

logger = logging.getLogger(__name__)


@dataclass_json
@dataclass
class Config:
    port: int
    feature_flags_sdk_key_path: str
    dynamic_feature_flags_endpoint: str | None
    anthropic_api_key_path: str
    openai_api_key_path: str
    xai_api_key_path: str
    fireworks_api_key_path: str
    gcp_project_id: str
    max_server_workers: int = 256
    server_mtls: tls_config.ServerConfig | None = None
    shutdown_grace_period_s: float = 20.0


def run(
    config: Config,
    namespace: str,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    server = grpc.server(
        ThreadPoolExecutor(
            max_workers=config.max_server_workers, thread_name_prefix="server-"
        ),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
        ],
    )

    anthropic_api_key = (
        pathlib.Path(config.anthropic_api_key_path).read_text(encoding="utf-8").strip()
    )
    openai_api_key = (
        pathlib.Path(config.openai_api_key_path).read_text(encoding="utf-8").strip()
    )
    xai_api_key = (
        pathlib.Path(config.xai_api_key_path).read_text(encoding="utf-8").strip()
    )
    fireworks_api_key = (
        pathlib.Path(config.fireworks_api_key_path).read_text(encoding="utf-8").strip()
    )

    handler_config = HandlerConfig(
        anthropic_api_key=anthropic_api_key,
        openai_api_key=openai_api_key,
        xai_api_key=xai_api_key,
        fireworks_api_key=fireworks_api_key,
        gcp_project_id=config.gcp_project_id,
    )

    # Add servicer to server
    thirdparty_proxy_servicer = ThirdPartyProxyHandler(handler_config)
    add_ThirdPartyProxyServicer_to_server(thirdparty_proxy_servicer, server)
    service_names = (
        thirdparty_proxy_pb2.DESCRIPTOR.services_by_name["ThirdPartyProxy"].full_name,
        reflection.SERVICE_NAME,
    )
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)

    if server_credentials is not None:
        server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        server.add_insecure_port(f"[::]:{config.port}")

    server.start()
    logging.info("Listening on %s", config.port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def _load_config(config_file: pathlib.Path) -> Config:
    return Config.schema().loads(  # type: ignore # pylint: disable=no-member
        config_file.read_text(),
    )


def main():
    """Run the server."""
    handler = GracefulSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    ri_publisher = RequestInsightPublisher.create_from_args(args)

    run(config, namespace, ri_publisher, handler.get_shutdown_event())


if __name__ == "__main__":
    main()

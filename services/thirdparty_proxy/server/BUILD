load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

py_library(
    name = "thirdparty_proxy_handler",
    srcs = [
        "anthropic_clients.py",
        "thirdparty_proxy_handler.py",
    ],
    deps = [
        "//base/third_party_clients:clients",
        "//services/lib/request_context:request_context_py",
        "//services/thirdparty_proxy:thirdparty_proxy_py_proto",
        requirement("grpcio"),
        requirement("protobuf"),
    ],
)

py_binary(
    name = "thirdparty_proxy_server",
    srcs = ["server.py"],
    deps = [
        ":thirdparty_proxy_handler",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//base/python/grpc:client_options",
        "//base/python/opentelemetry_utils:traced_threadpool",
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/request_insight/publisher:publisher_py",
        "//services/thirdparty_proxy:thirdparty_proxy_py_proto",
        requirement("dataclasses_json"),
        requirement("grpcio-reflection"),
        requirement("grpcio"),
        requirement("grpcio-health-checking"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("protobuf"),
        requirement("prometheus-client"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":thirdparty_proxy_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/tenants:namespaces",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
        # TODO: Move chat libs to deploy/common or into proxy
        "//services/chat_host/server:anthropic-lib",
        "//services/chat_host/server:openai-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

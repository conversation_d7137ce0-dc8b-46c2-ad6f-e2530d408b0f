// K8S deployment file for a third-party proxy server
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local anthropicLib = import 'services/chat_host/server/anthropic-lib.jsonnet';
local openaiLib = import 'services/chat_host/server/openai-lib.jsonnet';
local requestInsightPublisherLib = import 'services/request_insight/publisher/publisher_lib.jsonnet';

local name = 'thirdparty-proxy';

function(
  env,
  namespace,
  namespace_config,
  cloud,
)
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);

  local appName = name;
  local shortAppName = 'thirdp-proxy';
  local serviceAccount = gcpLib.createServiceAccount(
    appName, env, cloud, namespace, iam=true, overridePrefix=shortAppName
  );

  // Request Insight Publisher
  local requestInsightPublisher = requestInsightPublisherLib(
    cloud, env, namespace, appName
  );

  // Service account objects with request insight publisher permissions
  local serviceAccountObjects = serviceAccount.objects + [
    requestInsightPublisher.iamPartialPolicy(appName, serviceAccount.iamServiceAccountName),
  ];

  // Third-party API secrets
  local anthropicSealedSecret = anthropicLib(appName=appName, namespace=namespace, cloud=cloud);
  local openaiSealedSecret = openaiLib(appName=appName, namespace=namespace, cloud=cloud);
  local xaiSealedSecretName = std.asciiLower(env) + '-chat-xai-api-key';
  local xaiSealedSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='xai-api-key',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=xaiSealedSecretName,
  );
  local fireworksSecretName = std.asciiLower(env) + '-chat-fireworks-api-key';
  local fireworksGcpSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='fireworks-api-key',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
    overrideSecretName=fireworksSecretName,
  );

  // Feature flags
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.mountLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName, serviceAccount=serviceAccount);

  local services = lib.flatten([
    grpcLib.grpcService(name, namespace=namespace),
    if env != 'DEV' then grpcLib.globalGrpcService(cloud, appName, namespace) else [],
  ]);

  local serverDnsNames = lib.flatten([
    grpcLib.grpcServiceNames(name),
    if env != 'DEV' then [grpcLib.globalGrpcServiceHostname(cloud, appName, namespace)] else [],
  ]);

  // Certificates
  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-certificate' % name,
    namespace=namespace,
    env=env,
    appName=appName,
    volumeName='central-client-certs',
    dnsNames=serverDnsNames,
  );
  local centralServerCert = certLib.createCentralServerCert(
    name='%s-central-server-certificate' % name,
    namespace=namespace,
    env=env,
    appName=appName,
    dnsNames=serverDnsNames,
    volumeName='central-certs',
  );

  // Config map
  local configMap =
    local config = {
      port: 50051,
      feature_flags_sdk_key_path: dynamicFeatureFlags.filePath,
      dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
      anthropic_api_key_path: anthropicSealedSecret.api_key_path,
      openai_api_key_path: openaiSealedSecret.api_key_path,
      xai_api_key_path: xaiSealedSecret.filePath,
      fireworks_api_key_path: fireworksGcpSecret.filePath,
      gcp_project_id: cloudInfo[cloud].projectId,
    } + if !mtls then {} else {
      server_mtls: centralServerCert.config,
    };
    configMapLib.createConfigMap(appName=appName, namespace=namespace, config=config);

  // Grant AI Platform access
  local grantAiPlatformAccess = gcpLib.grantAccess(
    env=env,
    namespace=namespace,
    appName=appName,
    name='aiplatform-%s-grant' % name,
    resourceRef={
      kind: 'Project',
      external: 'projects/%s' % cloudInfo[cloud].projectId,
    },
    bindings=[
      {
        role: 'roles/aiplatform.user',
        members: [
          {
            memberFrom: {
              serviceAccountRef: {
                name: serviceAccount.iamServiceAccountName,
              },
            },
          },
        ],
      },
    ],
  );

  // Pod definition
  local container =
    {
      name: 'thirdparty-proxy',
      target: {
        name: '//services/thirdparty_proxy/server:image',
        dst: 'thirdparty-proxy',
      },
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      env: telemetryLib.telemetryEnv('thirdparty-proxy', telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      volumeMounts: lib.flatten([
        configMap.volumeMountDef,
        centralServerCert.volumeMountDef,
        centralClientCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        requestInsightPublisher.volumeMountDef,
        anthropicSealedSecret.volumeMountDef,
        openaiSealedSecret.volumeMountDef,
        xaiSealedSecret.volumeMountDef,
        fireworksGcpSecret.volumeMountDef,
      ]),
      readinessProbe: grpcLib.grpcHealthCheck(
        '%s-svc' % name,
        tls=mtls,
        serverCerts=centralServerCert.volumeMountDef.mountPath,
        clientCerts=centralClientCert.volumeMountDef.mountPath
      ) + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck(
        '%s-svc' % name,
        tls=mtls,
        serverCerts=centralServerCert.volumeMountDef.mountPath,
        clientCerts=centralClientCert.volumeMountDef.mountPath
      ) + {
        periodSeconds: 30,
      },
      resources: {
        limits: {
          cpu: 2,
          memory: '4Gi',
        },
      },
    };

  local pod =
    {
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: lib.flatten([
        configMap.podVolumeDef,
        centralServerCert.podVolumeDef,
        centralClientCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
        requestInsightPublisher.podVolumeDef,
        anthropicSealedSecret.podVolumeDef,
        openaiSealedSecret.podVolumeDef,
        xaiSealedSecret.podVolumeDef,
        fireworksGcpSecret.podVolumeDef,
      ]),
    };

  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pbd = nodeLib.podDisruption(appName=appName, namespace=namespace, env=env);

  lib.flatten([
    centralClientCert.objects,
    centralServerCert.objects,
    configMap.objects,
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: name,
        namespace: namespace,
        labels: {
          app: name,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        replicas: if env == 'DEV' then 1 else if env == 'STAGING' then 4 else 16,
        minReadySeconds: if env == 'DEV' then 0 else 60,
        strategy: {
          type: 'RollingUpdate',
          rollingUpdate: {
            maxSurge: 1,
            maxUnavailable: 0,
          },
        },
        selector: {
          matchLabels: {
            app: name,
          },
        },
        template: {
          metadata: {
            labels: {
              app: name,
            },
          },
          spec: pod + {
            affinity: affinity,
            tolerations: tolerations,
            priorityClassName: cloudInfo.envToPriorityClass(env),
          },
        },
      },
    },
    services,
    serviceAccountObjects,
    grantAiPlatformAccess,
    dynamicFeatureFlags.objects,
    anthropicSealedSecret.objects,
    openaiSealedSecret.objects,
    xaiSealedSecret.objects,
    fireworksGcpSecret.objects,
    pbd,
  ])

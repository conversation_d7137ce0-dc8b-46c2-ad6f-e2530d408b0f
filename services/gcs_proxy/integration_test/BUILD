load("//tools/bzl:go.bzl", "go_test")

go_test(
    name = "gcs_proxy_integration_test",
    srcs = ["integration_test.go"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/gcs_proxy:gcs_proxy_go_proto",
        "//services/gcs_proxy/client:client_go",
        "//services/gcs_proxy/server:gcs_proxy_server_lib",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/request_context:request_context_go",
        "@com_github_fsouza_fake_gcs_server//fakestorage",
        "@com_github_rs_zerolog//log",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
        "@org_golang_google_grpc//test/bufconn",
    ],
)

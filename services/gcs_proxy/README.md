# Placeholder README for GCS Proxy Service

## Tentative Thoughts:

* Proxy service should be deployed in each namespace (including central)
* Proxy service should be called from the GCS exporter to write to the GCS bucket. The GCS exporter should not have direct access to the GCS bucket.
* Proxy service can be called from other services to read from the GCS bucket.
* Proxy service should validate the tenant ID in the request and ensure that the tenant ID is included in the key.
* Proxy service should have access to the GCS bucket via IAM policies.
* Proxy service should be stateless and not persist any data of its own (including enterprise vs not, etc)
* Proxy service interface can override write or read interfaces in the GCS client library.

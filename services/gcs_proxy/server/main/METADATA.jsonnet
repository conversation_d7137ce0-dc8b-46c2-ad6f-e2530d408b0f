// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

{
  deployment: [
    {
      name: 'gcs-proxy',
      kubecfg: {
        target: '//services/gcs_proxy/server/main:kubecfg',
        task: tenantNamespaces.namespaces + [
          centralNamespace
          for centralNamespace in cloudInfo.centralNamespaces
          if centralNamespace.cloud == 'GCP_US_CENTRAL1_PROD' || centralNamespace.cloud == 'GCP_EU_WEST4_PROD'
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['nikita'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'gcs-proxy-monitoring',
      kubecfg: {
        target: '//services/gcs_proxy/server/main:kubecfg_monitoring',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['nikita', 'des'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}

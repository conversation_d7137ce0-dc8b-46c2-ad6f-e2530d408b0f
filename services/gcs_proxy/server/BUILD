load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "gcs_proxy_server_lib",
    srcs = [
        "metrics.go",
        "service.go",
        "test_utils.go",
    ],
    importpath = "github.com/augmentcode/augment/services/gcs_proxy/server",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/gcs_proxy:gcs_proxy_go_proto",
        "//services/lib/encryption:encryption_lib_go",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/tenant_watcher/util:go_lib",
        "//services/token_exchange:token_exchange_go_proto",
        "@com_github_fsouza_fake_gcs_server//fakestorage",
        "@com_github_googleapis_gax_go_v2//:gax-go",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto:go_default_library",
        "@com_github_rs_zerolog//log",
        "@com_google_cloud_go_storage//:storage",
        "@org_golang_google_api//iterator",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "gcs_proxy_server_unit_test",
    srcs = ["service_test.go"],
    embed = [":gcs_proxy_server_lib"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)

go_test(
    name = "gcs_proxy_server_emulator_test",
    srcs = ["emulator_test.go"],
    embed = [":gcs_proxy_server_lib"],
    deps = [
        "@com_github_stretchr_testify//assert",
    ],
)

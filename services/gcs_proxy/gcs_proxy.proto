syntax = "proto3";

package gcs_proxy;

option go_package = "github.com/augmentcode/augment/services/gcs_proxy/proto";

// Todo: these should probably be streaming; can figure that out later

// Note that all object paths and prefixes in the requests and responses are
// relative to the tenant ID prefix. The full GCS path/prefix will be:
//    <bucket_name>/<tenant_id>/<object_path> or
//    <bucket_name>/<tenant_id>/<prefix>
service GcsProxy {
  rpc Write(WriteRequest) returns (WriteResponse) {}
  rpc Read(ReadRequest) returns (ReadResponse) {}
  rpc List(ListRequest) returns (ListResponse) {}
  // Add other methods as needed
}

message WriteRequest {
  string tenant_id = 1;
  string object_path = 2;
  bytes data = 3;
}

message WriteResponse {}

message ReadRequest {
  string tenant_id = 1;
  string object_path = 2;
}

message ReadResponse {
  bytes data = 1;
}

message ListRequest {
  string tenant_id = 1;
  string prefix = 2;
}

message ListResponse {
  repeated string object_paths = 1;
}

package gcsproxyclient

import (
	"context"
	"strings"

	proto "github.com/augmentcode/augment/services/gcs_proxy/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

// MockGcsProxyClient is a mock implementation of the GcsProxyClient interface.
// It stores data in memory and can be used for testing purposes.
// It does not interact with an actual GCS bucket.
// Right now it always succeeds but we can add errors later.
type MockGcsProxyClient struct {
	data map[string][]byte
}

func NewMockGcsProxyClient() GcsProxyClient {
	return &MockGcsProxyClient{
		data: make(map[string][]byte),
	}
}

func (m *MockGcsProxyClient) Write(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.WriteRequest) (*proto.WriteResponse, error) {
	m.data[req.TenantId+"/"+
		req.ObjectPath] = req.Data
	return &proto.WriteResponse{}, nil
}

func (m *MockGcsProxyClient) Read(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ReadRequest) (*proto.ReadResponse, error) {
	return &proto.ReadResponse{
		Data: m.data[req.TenantId+"/"+
			req.ObjectPath],
	}, nil
}

func (m *MockGcsProxyClient) List(ctx context.Context, requestContext *requestcontext.RequestContext, req *proto.ListRequest) (*proto.ListResponse, error) {
	var result []string
	completePrefix := req.TenantId + "/" + req.Prefix
	for key := range m.data {
		if strings.HasPrefix(key, completePrefix) {
			result = append(result, key[len(req.TenantId+"/"):])
		}
	}
	return &proto.ListResponse{ObjectPaths: result}, nil
}

func (m *MockGcsProxyClient) WriteObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string, data []byte) error {
	req := &proto.WriteRequest{
		TenantId:   tenantID,
		ObjectPath: objectPath,
		Data:       data,
	}
	_, err := m.Write(ctx, requestContext, req)
	return err
}

func (m *MockGcsProxyClient) ReadObject(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, objectPath string) ([]byte, error) {
	req := &proto.ReadRequest{
		TenantId:   tenantID,
		ObjectPath: objectPath,
	}
	resp, err := m.Read(ctx, requestContext, req)
	if err != nil {
		return nil, err
	}
	return resp.Data, nil
}

func (m *MockGcsProxyClient) ListObjects(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, prefix string) ([]string, error) {
	req := &proto.ListRequest{
		TenantId: tenantID,
		Prefix:   prefix,
	}
	resp, err := m.List(ctx, requestContext, req)
	if err != nil {
		return nil, err
	}
	return resp.ObjectPaths, nil
}

func (m *MockGcsProxyClient) Close() error {
	return nil
}

"""Python client for the GCS proxy service."""

import typing

import grpc

import services.gcs_proxy.gcs_proxy_pb2 as gcs_proxy_pb2
import services.gcs_proxy.gcs_proxy_pb2_grpc as gcs_proxy_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str, credentials: grpc.ChannelCredentials | None
) -> gcs_proxy_pb2_grpc.GcsProxyStub:
    """Setup the client stub for a GCS proxy service."""
    if not credentials:
        channel = grpc.insecure_channel(endpoint)
    else:
        channel = grpc.secure_channel(endpoint, credentials)
    stub = gcs_proxy_pb2_grpc.GcsProxyStub(channel)
    return stub


class GcsProxyClient(typing.Protocol):
    """Interface for the GCS proxy service."""

    def write(
        self,
        request_context: RequestContext,
        tenant_id: str,
        object_path: str,
        data: bytes,
    ) -> gcs_proxy_pb2.WriteResponse:
        raise NotImplementedError()

    def read(
        self,
        request_context: RequestContext,
        tenant_id: str,
        object_path: str,
    ) -> gcs_proxy_pb2.ReadResponse:
        raise NotImplementedError()

    def list(
        self,
        request_context: RequestContext,
        tenant_id: str,
        prefix: str,
    ) -> gcs_proxy_pb2.ListResponse:
        raise NotImplementedError()


class GrpcGcsProxyClientImpl:
    """Class to call GCS proxy APIs remotely."""

    def __init__(self, endpoint: str, credentials: grpc.ChannelCredentials | None):
        self.endpoint = endpoint
        self.stub = setup_stub(endpoint, credentials)

    def write(
        self,
        request_context: RequestContext,
        tenant_id: str,
        object_path: str,
        data: bytes,
    ) -> gcs_proxy_pb2.WriteResponse:
        """Write data to a GCS object."""
        request = gcs_proxy_pb2.WriteRequest(
            tenant_id=tenant_id, object_path=object_path, data=data
        )
        response = self.stub.Write(request, metadata=request_context.to_metadata())
        return response

    def read(
        self,
        request_context: RequestContext,
        tenant_id: str,
        object_path: str,
    ) -> gcs_proxy_pb2.ReadResponse:
        """Read data from a GCS object."""
        request = gcs_proxy_pb2.ReadRequest(
            tenant_id=tenant_id, object_path=object_path
        )
        response = self.stub.Read(request, metadata=request_context.to_metadata())
        return response

    def list(
        self,
        request_context: RequestContext,
        tenant_id: str,
        prefix: str,
    ) -> gcs_proxy_pb2.ListResponse:
        """List GCS objects."""
        request = gcs_proxy_pb2.ListRequest(tenant_id=tenant_id, prefix=prefix)
        response = self.stub.List(request, metadata=request_context.to_metadata())
        return response

    def __str__(self) -> str:
        return f"GcsProxyClient(endpoint={self.endpoint})"

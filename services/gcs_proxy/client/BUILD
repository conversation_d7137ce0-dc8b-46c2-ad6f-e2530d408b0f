load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_library")

go_library(
    name = "client_go",
    srcs = [
        "client.go",
        "mock_client.go",
    ],
    importpath = "github.com/augmentcode/augment/services/gcs_proxy/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/gcs_proxy:gcs_proxy_go_proto",
        "//services/lib/request_context:request_context_go",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/gcs_proxy:gcs_proxy_py_proto",
        "//services/lib/request_context:request_context_py",
    ],
)

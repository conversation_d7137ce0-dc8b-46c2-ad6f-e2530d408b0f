load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "gcs_proxy_proto",
    srcs = ["gcs_proxy.proto"],
    visibility = ["//services:__subpackages__"],
)

go_grpc_library(
    name = "gcs_proxy_go_proto",
    importpath = "github.com/augmentcode/augment/services/gcs_proxy/proto",
    proto = ":gcs_proxy_proto",
    visibility = [
        "//services:__subpackages__",
    ],
)

py_grpc_library(
    name = "gcs_proxy_py_proto",
    protos = [":gcs_proxy_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

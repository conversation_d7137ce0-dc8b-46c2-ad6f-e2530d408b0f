load("//tools/bzl:go.bzl", "go_binary", "go_library")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_binary")

go_library(
    name = "server_lib",
    srcs = ["main.go"],
    importpath = "github.com/augmentcode/augment/services/ping_pong/server",
    visibility = ["//visibility:private"],
    deps = [
        "//services/ping_pong:ping_pong_go_proto",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//keepalive",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
)

rust_binary(
    name = "server_rs",
    srcs = ["src/main.rs"],
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//base/logging:struct_logging_rs",
        "//base/rust/tracing-tonic",
        "//services/ping_pong:ping_pong_rs_proto",
    ],
)

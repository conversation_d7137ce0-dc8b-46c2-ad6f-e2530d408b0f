package main

import (
	"context"
	"flag"
	"net"
	"time"

	proto "github.com/augmentcode/augment/services/ping_pong/proto"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"
)

var bind = flag.String("bind", "0.0.0.0:50051", "The address to bind to")

type pingPongServer struct{}

func (s *pingPongServer) Ping(ctx context.Context, req *proto.PingRequest) (*proto.PingResponse, error) {
	log.Info().Msgf("Ping")
	select {
	case <-ctx.Done():
		log.Info().Msgf("Context canceled")
		return nil, status.Errorf(codes.Canceled, "context canceled")
	case <-time.After(time.Duration(req.SleepTimeSec) * time.Second):
		log.Info().Msgf("Sleep time expired")
	}
	resp := &proto.PingResponse{}
	return resp, nil
}

func main() {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano
	zerolog.LevelFieldName = "severity"
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	flag.Parse()

	opts := []grpc.ServerOption{}
	if false {
		opts = append(opts,
			grpc.KeepaliveParams(keepalive.ServerParameters{
				MaxConnectionAge:      30 * time.Second, // Set max connection age to 30 seconds
				MaxConnectionAgeGrace: 10 * time.Second, // Optional: Grace period for outstanding RPCs
			}))
	}

	grpcServer := grpc.NewServer(opts...)

	// setup reflection for debugging
	reflection.Register(grpcServer)

	server := pingPongServer{}

	proto.RegisterPingPongServer(grpcServer, &server)
	lis, err := net.Listen("tcp", *bind)
	if err != nil {
		log.Fatal().Err(err).Msgf("Failed to listen")
	}
	log.Info().Msgf("Listening on %v", lis.Addr())
	err = grpcServer.Serve(lis)
	if err != nil {
		log.Fatal().Err(err).Msg("Error serving")
	}
}

use std::net::SocketAddr;

use clap::Parser;
use std::time::Duration;
use struct_logging::setup_struct_logging;
use tokio::signal::unix::{signal, SignalKind};
use tonic::transport::Server;
use tonic::Response;

use ping_pong_rs_proto::ping_pong::ping_pong_server::{PingPong, PingPongServer};
use ping_pong_rs_proto::ping_pong::{PingRequest, PingResponse};

#[derive(Parser, Debug)]
pub struct CliArguments {
    #[arg(long, default_value = "[::]:50051")]
    pub bind: String,
}

pub struct PingPongChannelImpl {
    pub abort_on_cancel: bool,
}

impl PingPongChannelImpl {
    pub fn new(abort_on_cancel: bool) -> Self {
        PingPongChannelImpl { abort_on_cancel }
    }

    pub fn new_server(self) -> PingPongServer<Self> {
        PingPongServer::new(self)
    }
}

#[tonic::async_trait]
impl PingPong for PingPongChannelImpl {
    async fn ping(
        &self,
        request: tonic::Request<PingRequest>,
    ) -> tonic::Result<Response<PingResponse>> {
        tracing::info!("Ping");
        let sleep_time_sec = request.get_ref().sleep_time_sec as u64;
        tokio::time::sleep(Duration::from_secs(sleep_time_sec)).await;
        Ok(Response::new(PingResponse {}))
    }
}

async fn run(args: CliArguments) -> Result<(), Box<dyn std::error::Error>> {
    let addr: SocketAddr = args.bind.parse()?;
    let listener = tokio::net::TcpListener::bind(addr)
        .await
        .expect("Failed to bind");
    tracing::info!(
        "Listening on {:?}",
        listener.local_addr().expect("Failed to get local address")
    );

    let service = PingPongChannelImpl::new(false);

    let mut sigterm_notifier = signal(SignalKind::terminate()).expect("handle SIGTERM");

    let server = Server::builder()
        .add_service(service.new_server())
        .serve_with_incoming_shutdown(
            tokio_stream::wrappers::TcpListenerStream::new(listener),
            async move {
                sigterm_notifier.recv().await;
            },
        );

    // wait for server to finish
    let res = server.await;
    tracing::info!("server done: {res:?}");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);

    run(args).await
}

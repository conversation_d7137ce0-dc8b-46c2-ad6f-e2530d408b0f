# Ping Pong

This was built to test gRPC client behavior. It is NOT example code for how to
write a gRPC client or server in our system - for that see services/examples.
This code is missing security, logging, tracing, metrics, graceful shutdown, etc...

For the clients, you generally specify two parameters:
    - how long you want the ping call to take at the server (--sleep)
    - how long you want the client to wait for the response (--timeout)

Timeout can be lower than sleep and is often lower during testing.

If you want to play with multiple paths, you can add
`--connect-address loopback.eng.augmentcode.com:50051` to the client options.
loopback.eng.augmentcode.com is a DNS entry that points to 127.0.0.1 and
*********.

## Running the server

`bazel run //services/ping_pong/server`

## Running the client

`bazel run //services/ping_pong/client:ping_py -- <options>`

`bazel run //services/ping_pong/client:ping_rs -- <options>`

## Watching packet traces

`tshark -i lo -s 65535 -d tcp.port==50051,http2  port 50051 or port 53`
    - listen on loopback (lo) interface
    - capture traffic to port 50051 (ping server) and port 53 (local DNS resolver)
    - decode port 50051 as HTTP/2
    - capture entire packet up to 64k

You may need to install wireshark.

`sudo apt install tshark`

Add yourself to the wireshark group to avoid running tshark as root

`sudo usermod -a -G wireshark $USER; sudo reboot`

## Blackhole connections

Start dropping packets to server (note it doesn't drop packets from server back to client)
`sudo iptables -A INPUT -p tcp --dport 50051 -j DROP`

Stop dropping packet to server:
`sudo iptables -D INPUT 1`

## List/Kill connections

List connections:
`ss dport == :50051`

Kill connections:
`sudo ss -K dport == :50051 and sport == <whatever sport>`

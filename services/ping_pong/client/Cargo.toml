[package]
name = "ping_pong"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
async-lock = { workspace = true }
async-rwlock = { workspace = true }
async-trait =  { workspace = true }
clap = { version = "4.0", features = ["derive"] }
futures = { workspace = true }
ginepro = {workspace = true}
itertools =  {workspace = true}
lazy_static = { workspace = true }
ping_pong_rs_proto = { path = ".." }
prost = {workspace = true}
prost-wkt = {workspace = true}
prost-wkt-types = {workspace = true}
rand = { workspace = true }
schemars = {workspace = true}
secrecy = {workspace = true}
serde = {workspace = true}
serde_json = {workspace = true}
sha2 = {workspace = true}
sha256 = {workspace = true}
struct_logging = { path = "../../../base/logging" }
tokio = { workspace = true }
tokio-stream = { workspace = true }
tonic = {workspace = true}
tonic-build  = { workspace = true }
tonic-health  = { workspace = true }
tonic-reflection  = { workspace = true }
tower = {workspace = true}
tracing = {workspace = true}
tracing-subscriber = {workspace = true}
uuid = {workspace = true}
rustls = {workspace = true}
rustls-pemfile = {workspace = true}

[dev-dependencies]
assert_unordered = {workspace = true}
tonic-build  = { workspace = true }
prost-build = {workspace = true}
prost-wkt-build = {workspace = true}

[build-dependencies]
tonic-build = { workspace = true }

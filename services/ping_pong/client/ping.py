import argparse
import logging
import time
import grpc

import ping_pong_pb2
import ping_pong_pb2_grpc

from base.python.grpc import client_options


def setup_logging():
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )


def main():
    setup_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--connect-address", default="127.0.0.1:50051", help="Connect address"
    )
    parser.add_argument("--timeout", type=int, default=10, help="Timeout in seconds")
    parser.add_argument("--sleep", type=int, default=0, help="Sleep time in seconds")
    parser.add_argument(
        "--sleep-after-connect",
        type=int,
        default=0,
        help="Sleep time after connection in seconds",
    )
    args = parser.parse_args()

    with grpc.insecure_channel(
        args.connect_address,
        options=client_options.create(
            client_options.GrpcClientOptions(load_balancing=True)
        ),
    ) as channel:
        client = ping_pong_pb2_grpc.PingPongStub(channel)

        logging.info("Connected")

        time.sleep(args.sleep_after_connect)

        while True:
            request = ping_pong_pb2.PingRequest(sleep_time_sec=args.sleep)
            logging.info(f"REQUEST={request}")

            try:
                response = client.Ping(request, timeout=args.timeout)
                logging.info(f"RESPONSE={response}")
            except grpc.RpcError as e:
                if e.code() == grpc.StatusCode.DEADLINE_EXCEEDED:  # pylint: disable=no-member # type: ignore
                    logging.error("Request timed out")
                else:
                    logging.error(f"RPC error: {e}")


if __name__ == "__main__":
    main()

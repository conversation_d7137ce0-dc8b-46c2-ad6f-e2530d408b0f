load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:rust.bzl", "rust_binary")
load("//tools/bzl:python.bzl", "py_binary")

rust_binary(
    name = "ping_rs",
    srcs = ["src/main.rs"],
    aliases = aliases(),
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ) + [
        "//base/logging:struct_logging_rs",
        "//base/rust/tracing-tonic",
        "//services/ping_pong:ping_pong_rs_proto",
    ],
)

py_binary(
    name = "ping_py",
    srcs = ["ping.py"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/ping_pong:ping_pong_py_proto",
        requirement("grpcio"),
        requirement("protobuf"),
    ],
)

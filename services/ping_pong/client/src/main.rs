use clap::Parser;
use std::time::Duration;

use struct_logging::setup_struct_logging;
use tonic::transport::Channel;

use ping_pong_rs_proto::ping_pong::ping_pong_client::PingPongClient;
use ping_pong_rs_proto::ping_pong::PingRequest;

#[derive(Parse<PERSON>, Debug)]
pub struct CliArguments {
    #[arg(long, default_value = "http://127.0.0.1:50051")]
    pub connect_address: String,

    #[arg(long, default_value_t = 10)]
    pub timeout: u64,

    #[arg(long, default_value_t = 0)]
    pub sleep: i32,

    #[arg(long, default_value_t = 0)]
    pub sleep_after_connect: u64,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    let channel = Channel::from_shared(args.connect_address)
        .expect("Invalid connect address")
        .connect_timeout(Duration::from_secs(10))
        .tcp_keepalive(Some(Duration::from_secs(15)))
        .http2_keep_alive_interval(Duration::from_secs(5 * 60))
        .keep_alive_timeout(Duration::from_secs(15))
        .connect()
        .await?;

    let mut client = PingPongClient::new(channel);

    tracing::info!("Connected");

    tokio::time::sleep(Duration::from_secs(args.sleep_after_connect)).await;

    loop {
        let mut request = tonic::Request::new(PingRequest {
            sleep_time_sec: args.sleep,
        });

        if args.timeout > 0 {
            request.set_timeout(Duration::from_secs(args.timeout));
        }

        tracing::info!("REQUEST={:?}", request);
        let response = client.ping(request).await;

        tracing::info!("RESPONSE={:?}", response);
    }
}

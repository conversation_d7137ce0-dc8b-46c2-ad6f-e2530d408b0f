load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:rust.bzl", "rust_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "ping_pong_proto",
    srcs = ["ping_pong.proto"],
    visibility = ["//services/ping_pong:__subpackages__"],
)

py_grpc_library(
    name = "ping_pong_py_proto",
    protos = [":ping_pong_proto"],
    visibility = ["//services/ping_pong:__subpackages__"],
)

go_grpc_library(
    name = "ping_pong_go_proto",
    importpath = "github.com/augmentcode/augment/services/ping_pong/proto",
    proto = ":ping_pong_proto",
    visibility = [
        "//services/ping_pong:__subpackages__",
    ],
)

ts_proto_library(
    name = "ping_pong_ts_proto",
    node_modules = "//:node_modules",
    proto = ":ping_pong_proto",
    visibility = ["//services/ping_pong:__subpackages__"],
)

rust_library(
    name = "ping_pong_rs_proto",
    srcs = ["ping_pong_proto.rs"],
    aliases = aliases(),
    crate_name = "ping_pong_rs_proto",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services/ping_pong:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":ping_pong_rs_proto_gen",
    ],
)

cargo_build_script(
    name = "ping_pong_rs_proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        ":ping_pong_proto",
        "@protobuf//:protoc",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)

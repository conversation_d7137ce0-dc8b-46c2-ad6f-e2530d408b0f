"""conftest.py is a special file that pytest will automatically load.

pytest will automatically load and execute before any other test files. This is a
good place to put fixtures that are used by multiple test files.
"""

import threading
import time
from pathlib import Path
from typing import Generator, Optional

import grpc
import kubernetes
import pytest

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.python.cloud import cloud as cloud_lib
from base.python.grpc.health_check import HealthChecker
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InfererClient,
    create_inference_stub,
)
from services.lib.request_context.request_context import (
    RequestContext,
    create_request_session_id,
)
from services.tenant_watcher.client.client import TenantsClient
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import (
    GrpcTokenExchangeClient,
    TokenExchangeClient,
)

# Container details


def pytest_addoption(parser):
    """Add command line options to pytest."""
    parser.addoption(
        "--skip-deployment",
        action="store_true",
        help="skip deploy and delete of the models",
        default=False,
    )
    parser.addoption(
        "--cloud",
        help="Cloud to use",
        default=cloud_lib.get_default_cloud(),
        choices=cloud_lib.get_cloud_list(gcp_only=True),
    )


def _test_response(
    url: str,
    credentials: Optional[grpc.ChannelCredentials],
    service_name: str,
    options: list[tuple[str, str]] | None = None,
) -> bool:
    try:
        checker = HealthChecker(url, credentials, options=options)
        status = checker.is_serving(service_name)
        return status
    except grpc.RpcError as ex:
        print(ex, flush=True)
        return False


@pytest.fixture(scope="session")
def request_session_id() -> Generator[str, None, None]:
    """Return a request context."""
    yield create_request_session_id()


@pytest.fixture()
def request_context(
    request_session_id: str, token_exchange_client: TokenExchangeClient, tenant_id: str
) -> Generator[RequestContext, None, None]:
    """Return a request context."""

    token = token_exchange_client.get_signed_token_for_service(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.CONTENT_RW]
    )
    yield RequestContext.create_for_session(request_session_id, auth_token=token)


@pytest.fixture(scope="session")
def inference_host_deploy(
    request,
) -> Generator[k8s_test_helper.DeployInfo, None, None]:
    """Deploys a content manager as pytest fixture."""
    k8s_test_helper.print_link_to_logs()
    skip_deploy = request.config.getoption("--skip-deployment")
    cloud = request.config.getoption("--cloud")
    assert cloud

    with k8s_test_helper.deploy(
        skip_deploy=skip_deploy,
        kubecfg_binaries=[
            Path("services/inference_host/test/test_kubecfg.sh"),
        ],
        cloud=cloud,
    ) as deploy_info:
        yield deploy_info


# The port to use for the next client.
# This global variable is used to ensure that we don't use the same local
# port for two different clients.
_LOCAL_PORT = 50051
__LOCAL_PORT_LOCK = threading.Lock()


def _get_fresh_local_port() -> int:
    """Return a fresh local port."""
    global _LOCAL_PORT
    with __LOCAL_PORT_LOCK:
        # copy the port to make sure we use a consistent port over retries
        local_port = _LOCAL_PORT
        _LOCAL_PORT += 1
    return local_port


def inference_host_client(
    inference_host_deploy: k8s_test_helper.DeployInfo,
    deployment_name: str,
) -> Generator[InferenceClientProtocol, None, None]:
    """Return a client to access the inference host.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = inference_host_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = f"{deployment_name}-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with inference_host_deploy.kubectl.port_forward(
            f"deployment/{deployment_name}", 50051, _get_fresh_local_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url,
                credentials=credentials,
                options=options,
                service_name="",
            ):
                time.sleep(10)
                continue
            else:
                stub = create_inference_stub(
                    url, credentials=credentials, options=options
                )
                yield InfererClient(lambda: stub)
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        stub = create_inference_stub(url, credentials=credentials, options=options)
        yield InfererClient(lambda: stub)


@pytest.fixture(scope="session")
def rogue_1b_fp8_seth6_16_1_p512_client(
    inference_host_deploy: k8s_test_helper.DeployInfo,
) -> Generator[InferenceClientProtocol, None, None]:
    yield from inference_host_client(
        inference_host_deploy, "infer-rogue-1b-fp8-seth6-16-1-p512"
    )


@pytest.fixture(scope="session")
def starcoder2_100m_fp8_client(
    inference_host_deploy: k8s_test_helper.DeployInfo,
) -> Generator[InferenceClientProtocol, None, None]:
    yield from inference_host_client(inference_host_deploy, "infer-starcoder2-100m-fp8")


@pytest.fixture(scope="session")
def starcoder2_100m_fp8_sequence_parallel_client(
    inference_host_deploy: k8s_test_helper.DeployInfo,
) -> Generator[InferenceClientProtocol, None, None]:
    yield from inference_host_client(
        inference_host_deploy, "infer-starcoder2-100m-fp8-sp"
    )


@pytest.fixture(scope="session")
def token_exchange_client(
    inference_host_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TokenExchangeClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = inference_host_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "token-exchange-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with inference_host_deploy.kubectl.port_forward(
            "deployment/token-exchange-central", 50051, _get_fresh_local_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield GrpcTokenExchangeClient(
                    url,
                    credentials=credentials,
                    options=options,
                    namespace=inference_host_deploy.namespace,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield GrpcTokenExchangeClient(
            url,
            credentials=credentials,
            options=options,
            namespace=inference_host_deploy.namespace,
        )


@pytest.fixture(scope="session")
def tenant_watcher_client(
    inference_host_deploy: k8s_test_helper.DeployInfo,
) -> Generator[TenantsClient, None, None]:
    """Return an GRPC stub to access the content manager.

    If applicable, it will update or create the application in Kubernetes.
    """
    credentials = inference_host_deploy.kubectl.load_grpc_credentials()
    # change the endpoint name when verifying the TLS certificate as "localhost" is not
    # on the certificate.
    target_name_override_endpoint = "tenant-central-svc"
    options: list[tuple[str, str]] = [
        ("grpc.ssl_target_name_override", target_name_override_endpoint)
    ]

    url = ""
    for _ in range(30):
        with inference_host_deploy.kubectl.port_forward(
            "deployment/tenant-central", 50051, _get_fresh_local_port()
        ) as port:
            url = f"localhost:{port}"
            time.sleep(5)
            if not _test_response(
                url, credentials=credentials, options=options, service_name=""
            ):
                time.sleep(10)
                continue
            else:
                yield TenantsClient(
                    url,
                    credentials=credentials,
                    options=options,
                )
                break
    else:
        print(f"TIMEOUT testing response from {url}")
        # the test will likely fail
        yield TenantsClient(
            url,
            credentials=credentials,
            options=options,
        )


@pytest.fixture(scope="session")
def tenant_id(
    tenant_watcher_client: TenantsClient,
) -> str:
    tenants = tenant_watcher_client.get_tenants()
    assert tenants
    tid = tenants[0].id
    assert tid, "Failed to find tenant ID for tenant 'augment'"
    return tid


def _get_pod(
    api_client: kubernetes.client.ApiClient | None, namespace: str, deployment_name: str
) -> Optional[str]:
    v1 = kubernetes.client.CoreV1Api(api_client)
    ret = v1.list_namespaced_pod(namespace)
    for service in ret.items:
        name = service.metadata.name
        if name.startswith(deployment_name):
            return name

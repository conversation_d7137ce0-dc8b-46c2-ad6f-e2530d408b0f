"""Integration test for inference host."""

from base.fastforward.starcoder import sample_data
from services.inference_host.client.inference_host_client import InferenceClientProtocol
from services.lib.request_context.request_context import RequestContext


def test_basic_infer(
    rogue_1b_fp8_seth6_16_1_p512_client: InferenceClientProtocol,
    request_context: RequestContext,
):
    """Tests the basic inference."""
    response = rogue_1b_fp8_seth6_16_1_p512_client.infer(
        input_tokens=[
            5,
            19561,
            33,
            748,
            266,
            33,
            7297,
            33,
            687,
            81,
            318,
            1507,
            33,
            86,
            1633,
            81,
            687,
            81,
            16655,
        ],
        max_output_length=8,
        end_token_ids=[0, 49154],
        top_k=1,
        top_p=0.5,
        temperature=0.75,
        random_seed=42,
        request_context=request_context,
        timeout_s=30.0,
    )
    assert response.output_tokens == [33, 1617, 33, 1831, 33, 1859, 33, 508]
    assert response.log_probs is not None


def test_starcoder2_100m_fp8_infer(
    starcoder2_100m_fp8_client: InferenceClientProtocol,
    request_context: RequestContext,
):
    """Tests the inference with a starcoder2 model."""
    starcoder2_test_data = sample_data.load_sample_data()
    response = starcoder2_100m_fp8_client.infer(
        input_tokens=starcoder2_test_data["inputs"],
        max_output_length=10,
        end_token_ids=[0, 49154],
        top_k=1,
        top_p=0.5,
        temperature=0.75,
        random_seed=42,
        request_context=request_context,
        timeout_s=30.0,
    )
    assert response.output_tokens == starcoder2_test_data["outputs"]
    assert response.log_probs is not None


def test_starcoder2_100m_fp8_sequence_parallel_infer(
    starcoder2_100m_fp8_sequence_parallel_client: InferenceClientProtocol,
    request_context: RequestContext,
):
    """Tests the inference with a starcoder2 model."""
    starcoder2_test_data = sample_data.load_sample_data()
    response = starcoder2_100m_fp8_sequence_parallel_client.infer(
        input_tokens=starcoder2_test_data["inputs"],
        max_output_length=10,
        end_token_ids=[0, 49154],
        top_k=1,
        top_p=0.5,
        temperature=0.75,
        random_seed=42,
        request_context=request_context,
        timeout_s=30.0,
    )
    assert response.output_tokens == starcoder2_test_data["outputs"]
    assert response.log_probs is not None

import random

import pytest

from services.inference_host.server.continuous_batching import longest_overlap_rs


def _predict_next_k_wrapper(
    tokens,
    max_predict_tokens,
    max_overlap_length=4,
    num_ignore_last_tokens=1,
    max_tokens_skipped=0,
):
    return longest_overlap_rs.predict_next_k_tokens(
        tokens,
        max_predict_tokens,
        max_overlap_length,
        num_ignore_last_tokens,
        max_tokens_skipped,
    )


@pytest.mark.parametrize(
    "max_predict_tokens",
    [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
)
def test_no_match(max_predict_tokens):
    result = _predict_next_k_wrapper(
        [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], max_predict_tokens
    )
    assert result == []


def test_simple_match():
    result = _predict_next_k_wrapper([10, 1, 2, 3, 4, 10], 2)
    assert result == [1, 2]


def test_multiple_matches():
    result = _predict_next_k_wrapper([10, 1, 2, 3, 4, 10, 99, 2, 3, 4, 10], 1)
    # second match has more overlap tokens
    assert result == [99]


def test_skip_does_not_override_match():
    result = _predict_next_k_wrapper(
        [4, 10, 1, 2, 3, 4, 5, 10, 99, 2, 3, 4, 10],
        1,
        max_tokens_skipped=1,
        max_overlap_length=3,
    )
    # second match has more overlap tokens
    assert result == [1]


def test_skip_overrides_match_if_overlap_is_long_enough():
    result = _predict_next_k_wrapper(
        [4, 10, 1, 2, 3, 4, 5, 10, 99, 2, 3, 4, 10],
        1,
        max_tokens_skipped=2,
        max_overlap_length=4,
    )
    # second match has more overlap tokens
    assert result == [99]


def test_ignore_last_tokens():
    result = _predict_next_k_wrapper(
        [4, 10, 1, 2, 3, 4, 10, 99, 2, 3, 4, 10],
        2,
        num_ignore_last_tokens=6,  # including the second 10
    )
    assert result == [1, 2]


def test_long_random_sequence():
    random.seed(42)  # For reproducibility
    random_sequence = [random.randint(0, 1000) for _ in range(8000)]

    # Insert a known pattern near the end
    pattern = random_sequence[-5:]
    insert_position = 7950
    random_sequence[insert_position : insert_position + len(pattern)] = pattern

    max_predict_tokens = 15
    result = _predict_next_k_wrapper(
        random_sequence,
        max_predict_tokens=max_predict_tokens,
        max_tokens_skipped=5,
        max_overlap_length=40,
        num_ignore_last_tokens=1,
    )

    # The expected result should be the three tokens following the pattern
    expected = random_sequence[
        insert_position + len(pattern) : insert_position
        + len(pattern)
        + max_predict_tokens
    ]
    assert result == expected, f"Expected {expected}, but got {result}"

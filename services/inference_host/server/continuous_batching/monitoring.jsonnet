local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  local streamsExhausted(cloud, displayName, severity, threshold, durationSeconds) =
    local spec = {
      displayName: displayName,
      conditionPrometheusQueryLanguage: {
        duration: '%ds' % (durationSeconds),
        evaluationInterval: '60s',
        labels: { severity: severity },
        query: |||
          (sum by(cluster, namespace) (
            increase(au_inference_rpc_streams_exhausted_total[1m])
          )) /
          (sum by(cluster, namespace) (
            increase(au_inference_host_request_total[1m])
          )) > %(threshold)f
        ||| % { threshold: threshold },
      },
    };
    local name = std.asciiLower(std.strReplace(displayName, ' ', '-'));
    monitoringLib.alertPolicy(cloud, spec, name, 'Namespace %s cluster %s exhausted inference RPC streams' % [
      monitoringLib.label('namespace'),
      monitoringLib.label('cluster'),
    ]);
  [
    // Alert if 2% of requests are impacted for 5 minutes, or 10% for 1 minute.
    streamsExhausted(cloud, 'Inference streams exhausted warning', 'warning', 0.02, 300),
    streamsExhausted(cloud, 'Inference streams exhausted error', 'error', 0.10, 60),
  ]

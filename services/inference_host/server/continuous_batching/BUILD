load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_torch_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("//tools/bzl:rust.bzl", "rust_library", "rust_shared_library", "rust_test")

py_library(
    name = "inference_runner",
    srcs = ["inference_runner.py"],
    deps = [
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        requirement("grpcio"),
    ],
)

py_library(
    name = "pool",
    srcs = ["pool.py"],
    deps = [
        ":inference_runner",
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        "//base/feature_flags:feature_flags_py",
        "//services/lib/request_context:request_context_py",
        requirement("opentelemetry-api"),
        requirement("opentelemetry-sdk"),
        requirement("prometheus-client"),
        requirement("structlog"),
        requirement("torch"),
    ],
)

pytest_test(
    name = "pool_test",
    srcs = [
        "pool_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":pool",
        "//base/fastforward:cached_attention",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_testmodel",
        "//services/lib/request_context:request_context_py",
        requirement("torch"),
    ],
)

py_library(
    name = "continuous_batching_inference_runner",
    srcs = ["continuous_batching_inference_runner.py"],
    deps = [
        ":generation_state",
        ":inference_runner",
        ":longest_overlap_lm",
        ":pool",
        ":speculation",
        "//base/fastforward:batching",
        "//base/fastforward:fwd",
        "//base/fastforward:multirequest_flash_attention",
        "//base/feature_flags:feature_flags_py",
        "//base/prometheus:sampled_histogram",
        "//services/lib/grpc/auth:service_auth",
        requirement("opentelemetry-api"),
        requirement("opentelemetry-sdk"),
        requirement("prometheus-client"),
        requirement("structlog"),
        requirement("torch"),
        requirement("grpcio"),
    ],
)

pytest_test(
    name = "continuous_batching_inference_runner_test",
    size = "medium",
    srcs = [
        "continuous_batching_inference_runner_test.py",
    ],
    tags = [
        "gpu",
    ],
    deps = [
        ":continuous_batching_inference_runner",
        ":longest_overlap_lm",
        ":speculation",
        "//base/fastforward:fwd_dummy",
        "//base/fastforward:fwd_testmodel",
        "//base/fastforward:fwd_torch",
        "//base/fastforward/deepseek_v2:fwd_dsv2",
        "//base/fastforward/deepseek_v2:model_specs",
        "//base/fastforward/llama:fwd_llama",
        "//base/fastforward/llama:fwd_llama_fp8",
        "//base/fastforward/llama:model_specs",
        "//base/fastforward/starcoder:fwd_starcoder_fp8",
        "//base/fastforward/starcoder:model_specs",
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//services/deploy/configs:repo_model_config_py_proto",
        requirement("torch"),
        requirement("numpy"),
        requirement("opentelemetry-api"),
    ],
)

DEPS = [
    ":continuous_batching_inference_runner",
    "//base/python/signal_handler",
    "//base/fastforward:all_reduce",
    "//base/fastforward:fwd",
    "//base/fastforward:multirequest_flash_attention",
    "//base/fastforward/deepseek_v2:fwd_dsv2",
    "//base/fastforward/deepseek_v2:model_specs",
    "//base/fastforward/llama:model_specs",
    "//base/fastforward/llama:fwd_llama",
    "//base/fastforward/llama:fwd_llama_fp8",
    "//base/fastforward/starcoder:fwd_starcoder",
    "//base/fastforward/starcoder:fwd_starcoder2",
    "//base/fastforward/starcoder:fwd_starcoder_fp8",
    "//base/fastforward/starcoder:fwd_starcoder2_fp8",
    "//base/fastforward/starcoder:model_specs",
    "//base/feature_flags:feature_flags_py",
    "//base/python/cloud:env_info",
    "//base/tracing:tracing_py",
    "//services/lib/request_context:request_context_py",
    ":inference_runner",
    ":speculation",
    "//base/logging:struct_logging",
    "//services/inference_host:infer_py_proto",
    "//services/lib/grpc/auth:service_auth",
    "//services/lib/grpc/auth:service_token_auth",
    "//services/lib/grpc/auth:service_auth_interceptor",
    "//services/lib/grpc/metrics:metrics",
    "//services/lib/grpc/tls_config:grpc_tls_config_py",
    "//services/token_exchange/client:client_py",
    requirement("dataclasses_json"),
    requirement("grpcio"),
    requirement("grpcio-reflection"),
    requirement("numpy"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("prometheus-client"),
    requirement("protobuf"),
    requirement("torch"),
    "//services/lib/grpc/stream_mux:server",
]

py_library(
    name = "generation_state",
    srcs = ["generation_state.py"],
    deps = [
        ":inference_runner",
        "//base/fastforward:fwd",
        "//base/fastforward:fwd_sampler",
        "//base/feature_flags:feature_flags_py",
        requirement("torch"),
    ],
)

pytest_test(
    name = "generation_state_test",
    size = "small",
    srcs = [
        "generation_state_test.py",
    ],
    deps = [
        ":generation_state",
        ":inference_runner",
        "//base/fastforward:fwd_sampler",
        "//base/fastforward:fwd_torch",
        "//base/feature_flags:feature_flags_py",
        requirement("torch"),
        requirement("numpy"),
    ],
)

py_binary(
    name = "server",
    srcs = [
        "server.py",
    ],
    main = "server.py",
    deps = DEPS,
)

pytest_test(
    name = "server_test",
    srcs = ["server_test.py"],
    # Could move this test suite up to services/inference_host/BUILD
    # as it depends on client and server
    deps = [
        ":server",
        "//services/inference_host/client",
    ],
)

py_torch_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = ["//services/deploy:__subpackages__"],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
    ],
)

py_library(
    name = "speculation",
    srcs = ["speculation.py"],
)

py_library(
    name = "longest_overlap_lm",
    srcs = ["longest_overlap_lm.py"],
    data = [":longest_overlap_so"],
    deps = [
        ":speculation",
        "//base/feature_flags:feature_flags_py",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "longest_overlap_lm_test",
    srcs = ["longest_overlap_lm_test.py"],
    deps = [
        ":longest_overlap_lm",
        ":speculation",
        "//base/feature_flags:feature_flags_py",
        requirement("numpy"),
    ],
)

pytest_test(
    name = "longest_overlap_rs_test",
    srcs = ["longest_overlap_rs_test.py"],
    data = [":longest_overlap_so"],
    deps = [],
)

rust_shared_library(
    name = "longest_overlap_rs",
    srcs = ["longest_overlap.rs"],
    aliases = aliases(),
    crate_name = "longest_overlap",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    deps = all_crate_deps(
        normal = True,
    ),
)

genrule(
    name = "longest_overlap_so",
    srcs = [":longest_overlap_rs"],
    outs = ["longest_overlap_rs.so"],
    cmd = "cp $< $@",
    visibility = ["//services/inference_host/server:__subpackages__"],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":monitoring_kubecfg",
    ],
)

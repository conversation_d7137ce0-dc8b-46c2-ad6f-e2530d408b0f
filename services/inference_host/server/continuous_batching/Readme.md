# Continuous Batching Inference Host

## Inference Runner

This server accepts requests to predict continuations of token sequences.

Initial design doc: https://www.notion.so/Inference-Server-Design-Proposal-d27484c83ec04f8a9de999a972b2eb2a

The goal of this design is to do both contexts processing and decoding at the
same time, while offering low latency and keeping the FLOPS utilization
reasonably high. The design here is mostly for small deployment sizes, where
we cannot afford to have two sets of GPUs for the two phases, and we do not
want to pause decoding while a new context has to be encoded.

Batching:  We use a variant of "continuous batching", probably most similar to
"selective batching". The server operates in rounds (~batches) of `round_size`
(e.g. 128) tokens. The tokens for a round may include tokens for requests that
are in the decoding phase and requests that are in the context processing
phase. So the batch dimension and the sequence length dimensions are flattened
and the requests are concatenated, and we ensure that the total length of the
request in a round does not exceed `round_size`.

Continuous batching: https://www.anyscale.com/blog/continuous-batching-llm-inference
Selective batching: https://www.usenix.org/system/files/osdi22-yu.pdf

Slide deck illustrating the batching algorithm:
https://docs.google.com/presentation/d/16LjuWeND_sQW5fTmmkDckxSJNn5AOw_wtF8MKCsCXOo/edit#slide=id.p

To enable this batching strategy, the model implementations are split out into
a stateless model forward pass, which manages all the parameters and the
stateful attention module which does not handle any parameters. The
BasicAttention module encapsulates the KV cache for a single sequence and
offers a __call__ method, which receives the qkv tensor and returns the
attention result (and it updates its internal KV cache state). To batch
multiple requests into a round, we wrap their BasicAttention modules with a
RoundAttention object. When RoundAttention is called with a qkv tensor, it
splits the tensor along the batch dimension and calls the respective
BasicAttention modules.

Concurrency:  The server has exactly one worker thread that continuously tries
to build batches, feed them to the language model, and update the requests
involved in the batch. Incoming requests are assumed to be called in individual
threads, calling the `infer` method, which returns a future
so that the thread can wait for the result if it wants to. Other than that
there are no additional python threads spawned in this implementation to avoid
bottlenecks due to the GIL. Please keep it that way as far as possible and
talk to other people when making any changes in that regard.

A call to `infer` creates a `Request` object (or returns
immediately) and enqueues it for processing. The request allows the
incoming threads to wait for the result without a busy wait loop.

To ensure that incoming threads and the worker thread do not interfere with
another, we have one lock that is held whenever the request queue is modified.
There is an additional semaphore that tracks the number of elements in the
request queue. Its only purpose is so that the worker thread can wait for
new elements to arrive without a busy wait loop.

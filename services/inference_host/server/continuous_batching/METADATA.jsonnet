// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details

{
  deployment: [
    {
      name: 'inference-host-monitoring',
      kubecfg: {
        target: '//services/inference_host/server/continuous_batching:monitoring_kubecfg',
        task: [
          {
            cloud: 'ALL_LEADS',
          },
        ],
      },
    },
  ],
}

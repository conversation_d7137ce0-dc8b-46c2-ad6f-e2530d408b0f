use pyo3::prelude::*;

// #[derive(Debug)]
struct Match {
    starting_position: usize,
    score: f32,
}

impl Match {
    fn new(starting_position: usize, score: f32) -> Match {
        Match {
            starting_position,
            score,
        }
    }
}

fn compute_match(
    tokens: &[i32],
    position: usize,
    max_overlap_length: usize,
    max_skip: usize,
) -> Match {
    let mut num_skipped = 0;
    let mut score = 0.0;

    for i in 0..max_overlap_length {
        if position < i {
            break;
        }
        let check_position = tokens.len().saturating_sub(i + 1) + num_skipped;
        let token_to_match = tokens[check_position];
        let new_token = tokens[position - i];
        if token_to_match == new_token {
            // Scoring:
            // - 1 point for each token that matches
            // - an overlap of tokens close to the end is better than an overlap close to the beginning
            let num_skipped_f32 = num_skipped as f32;
            score += 1.0 / (1.0 + num_skipped_f32);
        } else {
            if i == 0 {
                // An optimization to stop early if the first token doesn't match.
                break;
            }
            if num_skipped >= max_skip {
                break;
            }
            num_skipped += 1;
        }
    }

    Match::new(position, score)
}

fn predict_next_k_tokens_(
    tokens: Vec<i32>,
    max_predict_tokens: i32,
    max_overlap_length: i32,
    num_ignore_last_tokens: i32,
    max_tokens_skipped: i32,
) -> PyResult<Vec<i32>> {
    // Finds (approximate) occurrences of the last max_overlap_length tokens at the end of the sequence within the sequence itself
    // and returns the tokens that follow those occurrences.
    // Returns the tokens that follow the best match.

    let mut best_match: Option<Match> = None;
    let max_position_to_check = tokens.len().saturating_sub(num_ignore_last_tokens as usize);
    for position in (0..max_position_to_check).rev() {
        let current_match = compute_match(
            &tokens,
            position,
            max_overlap_length as usize,
            max_tokens_skipped as usize,
        );

        // This is just an inlined maximum operation.
        // Reduces runtime from 50us to 9us for a sequence of length 8k, because we
        // avoid storing thousands of Match structs.
        if let Some(ref best) = best_match {
            if current_match.score > best.score && current_match.score >= 1.0 {
                best_match = Some(current_match);
            }
        } else {
            best_match = Some(current_match);
        }
    }

    let result = match best_match {
        Some(best_match) => {
            // If the best match has score 0, return the empty list
            if best_match.score == 0.0 {
                Vec::new()
            } else {
                // Get the tokens following the best match
                let start = best_match.starting_position + 1;
                let end = (start + max_predict_tokens as usize).min(tokens.len());

                if start < tokens.len() {
                    tokens[start..end].to_vec()
                } else {
                    Vec::new()
                }
            }
        }
        None => Vec::new(),
    };

    Ok(result)
}

// Creating a separate wrapper for the pyfunction as it stops rust-analyzers squiggly lines
// from making half the file unreadable.
#[pyfunction]
fn predict_next_k_tokens(
    tokens: Vec<i32>,
    max_predict_tokens: i32,
    max_overlap_length: i32,
    num_ignore_last_tokens: i32,
    max_tokens_skipped: i32,
) -> PyResult<Vec<i32>> {
    predict_next_k_tokens_(
        tokens,
        max_predict_tokens,
        max_overlap_length,
        num_ignore_last_tokens,
        max_tokens_skipped,
    )
}

#[pymodule]
fn longest_overlap_rs(_py: Python, m: &PyModule) -> PyResult<()> {
    m.add_function(wrap_pyfunction!(predict_next_k_tokens, m)?)?;
    Ok(())
}

load("//tools/bzl:python.bzl", "py_binary", "py_oci_image")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg")

py_binary(
    name = "inference_host",
    srcs = [
        "null_inference_host.py",
    ],
    deps = [
        "//base/logging:struct_logging",
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("grpcio-health-checking"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
        "//base/python/signal_handler",
        "//base/tracing:tracing_py",
        "//services/deploy/model_instance:model_instance_py_proto",
        "//services/inference_host:infer_py_proto",
        "//services/lib/grpc/metrics",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":inference_host",
    tars = [
        "//tools/docker:grpc_health_probe_tar",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    cloud = [
        "GCP_US_CENTRAL1_DEV",
    ],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
    ],
)

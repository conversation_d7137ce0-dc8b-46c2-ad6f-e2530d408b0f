// K8S deployment file for the inference host
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
function(
  env,
  namespace,
  namespace_config,
  cloud,
)
  local appName = 'null-inferer';
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local services = grpcLib.grpcService(appName, namespace, ports=[50051]);
  // create a client TLS certificate to securely access the content manager
  local clientCert = certLib.createClientCert(
    name='null-infer-client-certificate',
    namespace=namespace,
    appName=appName,
    volumeName='client-certs',
  );
  local configMap =
    {
      apiVersion: 'v1',
      kind: 'ConfigMap',
      metadata: {
        name: appName + '-config',
        namespace: namespace,
        annotations: {
          'reloader.stakater.com/match': 'true',
        },
        labels: {
          app: appName,
        },
      },
      data: {
        'config.json': std.manifestJson(
          {
            port: 50051,
            client_mtls: mtls,
            client_ca_path: '/client-certs/ca.crt',
            client_key_path: '/client-certs/tls.key',
            client_cert_path: '/client-certs/tls.crt',
            server_mtls: mtls,
            server_ca_path: '/certs/ca.crt',
            server_key_path: '/certs/tls.key',
            server_cert_path: '/certs/tls.crt',
          }
        ),
      },
    };
  local container =
    {
      name: 'null-inference-host',
      target: {
        name: '//services/inference_host/server/null:image',
        dst: 'null',
      },
      env: telemetryLib.telemetryEnv('null-inference-host', telemetryLib.collectorUri(env, namespace, cloud)),
      ports: [
        {
          containerPort: 50051,
          name: 'grpc-svc',
        },
      ],
      resources: {
        limits: {
          cpu: 0.1,
          memory: '1Gi',
        },
      },
      volumeMounts: [
        {
          mountPath: '/certs',
          name: 'certs',
        },
        clientCert.volumeMountDef,
        {
          name: 'config',
          mountPath: '/config',
          readOnly: true,
        },
      ],
      readinessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts='/certs') + {
        periodSeconds: 30,
      },
      livenessProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts='/certs') + {
        periodSeconds: 30,
      },
      startupProbe: grpcLib.grpcHealthCheck('%s-svc' % appName, tls=mtls, serverCerts='/certs') + {
        periodSeconds: 30,
      },
    };
  // create a server certificate for MTLS
  local serverCert = certLib.createServerCert(name='%s-server-certificate' % appName,
                                              namespace=namespace,
                                              appName=appName,
                                              dnsNames=grpcLib.grpcServiceNames(appName));
  // creates the configuration for the inference pod.
  // this relies on a matching config map being setup
  local pod =
    {
      securityContext: {
        runAsUser: 1000,
        fsGroup: 1000,
      },
      containers: [
        container,
      ],
      volumes: [
        {
          name: 'config',
          configMap: {
            name: configMap.metadata.name,
          },
        },
        clientCert.podVolumeDef,
        {
          name: 'certs',
          secret: {
            secretName: '%s-server-certificate' % appName,
          },
        },
      ],
    };
  local tolerations = nodeLib.tolerations(resource=null, count=1, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, count=1, env=env, cloud=cloud, appName=appName);
  lib.flatten([
    clientCert.objects,
    serverCert.objects,
    configMap,
    {
      apiVersion: 'apps/v1',
      kind: 'Deployment',
      metadata: {
        name: appName,
        namespace: namespace,
        labels: {
          app: appName,
        },
        annotations: {
          'reloader.stakater.com/search': 'true',
        },
      },
      spec: {
        minReadySeconds: if env == 'DEV' then 0 else 60,
        replicas: 1,
        strategy: {
          type: 'RollingUpdate',
          // no surge to save on GPU
          rollingUpdate: {
            maxSurge: 0,
            maxUnavailable: 1,
          },
        },
        selector: {
          matchLabels: {
            app: appName,
          },
        },
        template: {
          metadata: {
            labels: {
              app: appName,
            },
          },
          spec: pod + {
            tolerations: tolerations,
            affinity: affinity,
            priorityClassName: cloudInfo.envToPriorityClass(env=env, resource=null),
          },
        },
      },
    },
    services,
  ])

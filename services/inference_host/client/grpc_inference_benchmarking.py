"""Utility to benchmark an inference host.

Notes:
    Input data is assumed to be in JSON format with the following keys:
        - "context": Tokenized version of the prompt/context that will be the starting point of the generation.
        - "label": Tokenized version of the ground truth label. (optional, only used for exact match calculation)

    You can find where your host is served with `kubectl get svc -n [NAMESPACE]`

    You can generate a new token with `bazel run //services/token_exchange/util -- --tenant-name augment --cloud GCP_US_CENTRAL1_DEV --shard-namespace [NAMESPACE]`
    Tokens will be saved in the file specified by `--auth-token-file`.

    By default, the service token is valid for one hour. This can get annoying pretty quickly.
    You can change the configuration of the token exchange service in your dev deploy to make them last longer.

Diff to make service tokens last longer (11-06-2024):

--- a/services/support/backend/api.py
+++ b/services/support/backend/api.py
@@ -60,7 +60,7 @@ def _get_request_context(
-        expiration=datetime.timedelta(minutes=60),
+        expiration=datetime.timedelta(hours=168),
@@ -120,7 +120,7 @@ def generate_token():
-        expiration=datetime.timedelta(minutes=60),
+        expiration=datetime.timedelta(hours=168),

--- a/services/token_exchange/server/deploy.jsonnet
+++ b/services/token_exchange/server/deploy.jsonnet
@@ -254,8 +254,8 @@ function(env, namespace, cloud, namespace_config)
-    iap_token_expiration: '1m',
-    iap_token_max_expiration: '1h',
+    iap_token_expiration: '168h',
+    iap_token_max_expiration: '168h',
@@ -269,7 +269,7 @@ function(env, namespace, cloud, namespace_config)
-        expiration: '60m',
+        expiration: '10080m',

Example invocation:
    bazel run //services/inference_host/client:grpc_inference_benchmarking -- \
        --cloud GCP_US_CENTRAL1_DEV \
        --namespace dev-$USER \
        --tenant-name augment \
        --auth-token-file /home/<USER>/.augment/request_token \
        --service infer-$MODEL_NAME-svc \
        --endpoint infer-$MODEL_NAME-svc:8001 \
        --end_token_ids $end_token $no_change_token $pause_token \
        --input_path "$input_path/$input_file" \
        --output_prefix "${sub_path}/${outfile}" \
        --max_seq_length 12288 \
        --measure_context_processing_separately \
        --num_runs_per_sample 1 \
        --take_first_n_samples 500 \
        # --request-token
"""

import argparse
import json
import logging
from typing import Sequence
import numpy as np
import pandas as pd
import pathlib
import time
from tqdm import tqdm

from base.logging.console_logging import setup_console_logging
from services.inference_host.client import inference_host_client
from services.lib.grpc import grpc_args_parser, token_parser
from services.lib.request_context.request_context import RequestContext


TIMEOUT_S = 300


def main(args: argparse.Namespace):
    """Main entry function."""

    try:
        logging.info("Loading data from %s", args.input_path)
        with pathlib.Path(args.input_path).open("r", encoding="utf8") as f:
            data = json.load(f)
    except Exception as e:
        logging.error("Error loading data: %s", str(e))
        return

    request_context = inference_host_client.RequestContext.create()

    def run(input_tokens, max_decode_tokens):
        return client.infer(
            input_tokens=input_tokens,
            max_output_length=max_decode_tokens,
            end_token_ids=args.end_token_ids,
            # all sampling disabled as per documentation in infer.proto
            top_k=0,
            top_p=0.0,
            temperature=0.0,
            random_seed=0,
            request_context=request_context,
            timeout_s=TIMEOUT_S,
        ).output_tokens

    auth_token = token_parser.get_token(args)
    request_id = None
    request_session_id = None
    request_context = RequestContext.create(
        auth_token=auth_token,
        request_id=request_id,
        request_session_id=request_session_id,
    )

    measured_sample_count = 0

    with grpc_args_parser.create_client(
        args,
        lambda endpoint, credentials, options: inference_host_client.InfererClient(
            lambda: inference_host_client.create_inference_stub(
                endpoint, credentials=credentials, options=options
            )
        ),
        default_service_name=None,
        default_endpoint=None,
    ) as client:
        stats = []
        for index, sample in enumerate(data):
            if (
                args.take_first_n_samples is not None
                and measured_sample_count >= args.take_first_n_samples
            ):
                break

            # this is equivalent to the prompt
            context = sample["context"]

            datum: dict[str, int | float] = {
                "context_length": len(context),
            }
            max_decode_tokens = min(
                args.max_decode_tokens, args.max_seq_length - len(context)
            ) or args.max_seq_length - len(context)
            assert max_decode_tokens > 0

            sample_generation: Sequence[int] = []
            if args.measure_context_processing_separately:
                start_time = time.time()
                sample_generation = run(context, 1)
                datum["context_processing_latency_ms"] = 1000.0 * (
                    time.time() - start_time
                )

            # If `measure_context_processing_separately` then we have cached the context processing,
            # so another `run` call only only measure the generation latency.
            sample_latencies: list[float] = []
            for _ in range(args.num_runs_per_sample):
                start_time = time.time()
                sample_generation = run(context, max_decode_tokens)
                current_latency_ms = 1000.0 * (time.time() - start_time)
                sample_latencies.append(current_latency_ms)

            measured_sample_count += 1

            if measured_sample_count % 100 == 0:
                logging.info("processed %s samples", measured_sample_count)

            datum.update(
                {
                    "generation_length": len(sample_generation),
                    "generation_latency_ms": np.asarray(sample_latencies).mean(),
                }
            )

            if args.num_runs_per_sample > 1:
                datum["generation_latency_ms_std"] = np.asarray(sample_latencies).std()

            if "label" in sample:
                datum["exact_match"] = int(sample_generation == sample["label"])
            stats.append(datum)
            print(datum)

    df = pd.DataFrame(stats)
    logging.info("saving to %s.csv", args.output_prefix)
    df.to_csv(args.output_prefix + ".csv", index=False)

    logging.info("Processed %s samples. Average statistics:", len(stats))
    try:
        print(df.describe())
    except Exception as e:
        print(f"Error describing DataFrame: {str(e)}")


def _get_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)
    # parser.add_argument("--endpoint", default="localhost:8888")
    parser.add_argument(
        "--input_path",
        required=True,
        type=str,
        help="Path to the input data",
    )
    parser.add_argument(
        "--output_prefix",
        required=True,
        type=str,
        help="Output path to save stats and metrics",
    )
    parser.add_argument(
        "--max_seq_length",
        required=True,
        type=int,
        default=None,
        help="Max sequence length (will decode up to this length)",
    )
    parser.add_argument(
        "--end_token_ids",
        metavar="N",
        type=int,
        nargs="+",
        default=[],
        required=True,
        help="end of generation token ids",
    )
    parser.add_argument(
        "--take_first_n_samples",
        required=False,
        type=int,
        default=None,
        help="Number of samples to consider. Default: all",
    )
    parser.add_argument(
        "--measure_context_processing_separately", action="store_true", default=False
    )
    parser.add_argument(
        "--num_runs_per_sample",
        required=False,
        type=int,
        default=1,
        help="Number of runs per sample. Default: 1",
    )
    parser.add_argument(
        "--max_decode_tokens",
        required=False,
        type=int,
        default=None,
        help="Max decode tokens. Default: max_seq_length - len(context)",
    )

    return parser.parse_args()


if __name__ == "__main__":
    setup_console_logging(debug=True)
    args = _get_args()
    logging.debug("args %s", args)
    logging.info(args.output_prefix)
    main(args)

# Inference host client

This directory contains a client implementation to make
inference requests to a FTM host (via grpc).

The library has endpoints for both KServe and our home-spun
infer protocol.

## Bazel

The inference host client is available via the `py_library` target `//services/inference_host/client:client`.

## CLI

The client can be used via the CLI util, for example assuming dev deployment:

```sh
kubectl port-forward deployment/infer-binks-1B-BF16-chatanol1-18-ug-chat 50051:50051 -n "dev-$USER"

bazel run //services/inference_host/client:inference_host_util -- \
  --endpoint=localhost:50051 \
  infer \
  --protocol=inferer \
  --tokenizer-name=deepseek_coder_instruct \
  --prompt="Implement quicksort in Python" \
  --output-len=256
```

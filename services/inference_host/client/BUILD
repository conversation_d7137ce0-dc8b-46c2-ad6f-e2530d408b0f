load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

py_library(
    name = "client",
    srcs = ["inference_host_client.py"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/python/grpc:client_options",
        "//services/lib/grpc:stub_cycler",
        "//services/lib/grpc/stream_mux:client",
        "//services/lib/request_context:request_context_py",
        requirement("lru-dict"),
        requirement("numpy"),
        requirement("typing_extensions"),
        requirement("opentelemetry_api"),
        "//services/inference_host:infer_py_proto",
    ],
)

pytest_test(
    name = "inference_host_client_test",
    srcs = ["inference_host_client_test.py"],
    deps = [
        ":client",
    ],
)

py_binary(
    name = "inference_host_util",
    srcs = ["inference_host_util.py"],
    deps = [
        ":client",
        "//base/logging:console_logging",
        "//base/tokenizers",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/grpc:token_parser",
        "//services/lib/request_context:request_context_py",
    ],
)

py_binary(
    name = "benchmark_inference_host",
    srcs = ["benchmark_inference_host.py"],
    deps = [
        requirement("pandas"),
        ":client",
        "//base/logging:console_logging",
        "//base/tokenizers",
    ],
)

py_binary(
    name = "grpc_inference_benchmarking",
    srcs = ["grpc_inference_benchmarking.py"],
    deps = [
        requirement("pandas"),
        requirement("numpy"),
        requirement("tqdm"),
        ":client",
        "//base/logging:console_logging",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/grpc:token_parser",
        "//services/lib/request_context:request_context_py",
    ],
)

py_library(
    name = "multiplex",
    srcs = [
        "multiplex.py",
    ],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        requirement("prometheus-client"),
        ":client",
        "//base/feature_flags:feature_flags_py",
    ],
)

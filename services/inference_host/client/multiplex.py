from dataclasses import dataclass
import random
import threading
import typing
import json
import logging

import numpy as np
from prometheus_client import Counter

import base.feature_flags
from services.inference_host import infer_pb2_grpc
from services.inference_host.client.inference_host_client import (
    InferenceClientProtocol,
    InferenceStubFactoryProtocol,
)
from services.lib.request_context.request_context import RequestContext

_counter = Counter(
    "au_inference_multiplex_counter",
    "Counter of the multiplex targets to inference hosts",
    ["model_name", "feature_flag_name", "name"],
)


@dataclass
class MultiplexOption:
    """A single option for multiplexing."""

    name: str
    weight: float


@dataclass
class MultiplexConfig:
    """Configuration for multiplexing."""

    options: list[MultiplexOption]


class MultiplexInferenceStubFactory(InferenceStubFactoryProtocol):
    """A client that multiplexes requests to multiple inference stubs.


    It consumes a feature flag that contains a json object with the weights for each client.
    The weights are used to randomly select a client for each request.

    Example
    {"default": 0.5, "gsc": 0.5}
    """

    def __init__(
        self,
        rpc_clients: typing.Dict[str, infer_pb2_grpc.InfererStub],
        model_name: str,
        feature_flag: base.feature_flags.StringFlag,
    ):
        """ """
        logging.info("Creating MultiplexInferenceClient with %s", rpc_clients)
        self.rpc_clients = rpc_clients
        self.model_name = model_name
        self.feature_flag = feature_flag
        if "default" not in self.rpc_clients:
            raise ValueError("No default client")
        self.lock = threading.Lock()
        self.last_ff: str | None = None
        self.last_config: MultiplexConfig | None = None

    def _get_ff_ctx(self):
        fflags_ctx = base.feature_flags.get_global_context()
        if self.model_name:
            fflags_ctx = fflags_ctx.bind_attribute(
                "model_name",
                self.model_name,
            )
        return fflags_ctx

    def _get_config(self) -> MultiplexConfig | None:
        """Get the config from the feature flag.
        If the config has not changed, return the cached config.
        """
        fflags_ctx = self._get_ff_ctx()
        flag_value = self.feature_flag.get(fflags_ctx)
        with self.lock:
            if self.last_ff == flag_value:
                return self.last_config
        logging.info("New multiplex config: %s", flag_value)
        if not flag_value:
            self.last_config = None
            self.last_ff = flag_value
            return None
        else:
            self.last_ff = flag_value
            json_data = json.loads(flag_value)
            new_config = MultiplexConfig(
                options=[
                    MultiplexOption(name=k, weight=v) for k, v in json_data.items()
                ]
            )
            self.last_config = new_config
            return self.last_config

    def _get_client(self) -> infer_pb2_grpc.InfererStub:
        """Get a client from the config.
        If the config is None, return the default client.
        If the config is not None, select a client based on the weights.
        """
        config = self._get_config()
        if not config:
            _counter.labels(self.model_name, self.feature_flag.name, "default").inc()
            default_client = self.rpc_clients.get("default")
            if not default_client:
                raise ValueError("No default client")
            return default_client
        weights = [o.weight for o in config.options]
        names = [o.name for o in config.options]
        client_name = random.choices(names, weights=weights)[0]
        _counter.labels(self.model_name, self.feature_flag.name, client_name).inc()
        client = self.rpc_clients.get(client_name)
        if not client:
            raise ValueError(f"No client for {client_name}")
        return client

    def __call__(self) -> infer_pb2_grpc.InfererStub:
        return self._get_client()

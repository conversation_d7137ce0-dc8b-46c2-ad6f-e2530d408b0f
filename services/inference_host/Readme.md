# Inference Host

The inference host hosts the transformer model for inference itself.

There are two kinds of inference host:
   - The continuous batching inference host, which implements a batching strategy
     similar to Orca or inflight-batching. The continuous batching host builds on
     fastforward. See the `server/continuous_batching` directory for more details.
   - A null inference host implemented in Python. It doesn't do
     any inference but is there to help us do the work to be
     able to deploy multiple implementations of the inference
     host. See the `server/null` directory for more details.

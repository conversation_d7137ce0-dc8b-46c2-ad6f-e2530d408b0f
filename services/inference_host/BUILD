load("//tools/bzl:python.bzl", "py_grpc_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")

proto_library(
    name = "infer_proto",
    srcs = ["infer.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/grpc/stream_mux:stream_mux_proto",
        "@protobuf//:duration_proto",
    ],
)

py_grpc_library(
    name = "infer_py_proto",
    protos = [":infer_proto"],
    visibility = ["//services:__subpackages__"],
    deps = ["//services/lib/grpc/stream_mux:stream_mux_py_proto"],
)

go_grpc_library(
    name = "infer_go_proto",
    importpath = "github.com/augmentcode/augment/services/inference_host/proto",
    proto = ":infer_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = ["//services/lib/grpc/stream_mux:stream_mux_go_proto"],
)

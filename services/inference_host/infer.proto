syntax = "proto3";
package inference_host;

import "google/protobuf/duration.proto";
import "services/lib/grpc/stream_mux/stream_mux.proto";

service Inferer {
  // Run inference
  rpc Infer(InferRequest) returns (InferResponse);
  // Run inference but keep the connection open for more requests.
  // The client can send multiple requests over the same connection,
  // and the server will respond to each request with exactly one response,
  // or an error.
  //
  rpc StreamingInfer(stream StreamingInferRequest) returns (stream StreamingInferResponse);
  // Run one inference request, return a stream of partial responses.
  rpc InferRoundStream(InferRequest) returns (stream InferResponse);
}

message InferRequest {
  // The session ID associated with the request. Placing it in the Request
  // rather than relying on the HTTP header since it's no longer just for
  // observability. A new InferRequest will cancel any previous InferRequests
  // with the same session_id. Yes, this is susceptible to stale/delayed requests
  // cancelling newer ones, but let's opt for simplicity over correctness for now.
  string session_id = 1;

  repeated int32 input_tokens = 3 [debug_redact = true];

  // Maximum number of tokens to generate
  int32 max_output_length = 4;

  // Removed field max_results with value 5 as it is misleading/unused.
  reserved 5;

  // Sampling configuration
  //
  // Greedy sampling is the default. To sample, set a temperature > 0.0. Optionally apply top_k and top_p.
  //
  // If the server does not support a non-zero top-k, top-p, or temperature, than an
  // invalid argument error may be returned.

  // top-k value for sampling
  //
  // top_k of 0 means disabled.
  int32 top_k = 6;

  // top-p value for sampling
  //
  // Set to 0.0 to disable.
  float top_p = 7;

  // temperature for sampling
  //
  // Set to 0.0 to disable
  float temperature = 8;

  // random seed for sampling - intended to make tests more deterministic
  //
  // A random seed of 0 means the client is not providing a random seed.
  uint64 random_seed = 9;

  // The client is not interested in any tokens after this one
  repeated int32 end_token_ids = 12;

  reserved 13;

  // Sequence IDs should be strictly increasing within a session.
  //
  // Requests may cancel requests with lower or equal sequence ID from the
  // same session and namespace. This helps prevent accidental cancellation
  // of requests when network messages are reordered.
  //
  // Requests with the same sequence ID can cancel each other. This ensures
  // that the protobuf default value of 0 ensures backward compatible behavior.
  uint32 sequence_id = 14;

  // Fields 15, 16, and 19 were added to support multiple requests over single
  // RPC stream. Their use is deprecated, as now StreamingInferRequest serves
  // this pupose. Inferer services should stop reading these fields, instead
  // taking their values from the grpc context as before. They will be removed.
  //
  // Request ID for tracing.
  // Introduced to support tracing in streaming mode.
  // Request ID must be set either in the request or in the header.
  string request_id = 15;
  // If this field is not set the server will use the timeout in the RPC context.
  // When the timeout is reached, the server may return an error with
  // status code StatusCode.DEADLINE_EXCEEDED.
  google.protobuf.Duration timeout = 16;
  reserved 19;

  // If set, the model will return the target output tokens.
  // This is useful for measuring token accuracy.
  repeated int32 target_tokens = 17 [debug_redact = true];

  // Priority of the request.
  // A higher priority request cancels a lower priority request.
  // When priority is the same, sequence_id logic will be applied.
  // Default value of 0 preserves backward compatibility.
  uint32 priority = 18;

  // When a single request requires multiple inference requests, this ID is used to differentiate those requests.
  // Multiple requests may have the same sequence_id value as long as sub_request_id is different.
  // Requests with the same sequence_id will not cancel each other if the sub_request_id differs.
  // Requests with greater sequence_id will still cancel requests with lower sequence_id.
  // Default value of an empty string preserves backward compatibility.
  string sub_request_id = 20;
}

message InferResponse {
  message Result {
    // The output of inference, coded as tokens. Does not
    // include input_tokens, unlike kserve
    repeated int32 output_tokens = 1 [debug_redact = true];

    // The probability of each token. If present len(log_probabilities) ==
    // len(output_tokens)
    repeated float log_probabilities = 2 [debug_redact = true];

    // Server should return logits if the request has want_logits = True,
    // otherwise leave it empty.
    //
    // len(logits) is the vocab size, possibly a bit larger if the vocab size
    // doesn't evenly divide into the number of GPUs
    repeated float logits = 3 [debug_redact = true];

    // The number of tokens where the selected token agreed with the
    // target tokens given in the request.
    int32 num_correct_predictions = 4;
  }

  // Note - currently (2024.5) we always return a single result here
  repeated Result results = 1;
}

message StreamingInferRequest {
  stream_mux.MuxedRequest context = 1;
  InferRequest request = 2;
}

message StreamingInferResponse {
  stream_mux.MuxedResponse context = 1;
  // If context.status_code OK, the result of the inference request
  InferResponse.Result response = 2;
}

import json
import os
import socket
import sys
import threading
import time

import pytest

import services.embeddings_indexer.line_rpc as line_rpc


@pytest.mark.timeout(10)
def test_socket_client_and_server():
    """Test that a line rpc client can talk to a line rpc server."""
    s = socket.socketpair()

    def run_server():
        line_rpc.server(s[1], lambda req: req.rstrip("\n"))

    t = threading.Thread(target=run_server)
    t.start()

    client = line_rpc.Client(s[0])

    try:
        assert client.call("foo") == "foo\n"
        assert client.call("bar") == "bar\n"
    finally:
        client.close()
        t.join()


def test_server_throws():
    """Test that if the server throws, we don't hang."""
    s = socket.socketpair()

    def run_server():
        def fn(_: str) -> str:
            raise RuntimeError("oops")

        line_rpc.server(s[1], fn)

    t = threading.Thread(target=run_server)
    t.start()

    client = line_rpc.Client(s[0])

    try:
        with pytest.raises(RuntimeError):
            client.call("foo")
    finally:
        t.join()
        client.close()


def test_client_timeout():
    """Test that if the client times out, we clean up.

    Timing sensitive test - potentially flaky.
    """
    s = socket.socketpair()

    def run_server():
        def fn(_: str) -> str:
            time.sleep(5)
            return "done"

        line_rpc.server(s[1], fn)

    t = threading.Thread(target=run_server)
    t.start()

    client = line_rpc.Client(s[0])

    try:
        with pytest.raises(TimeoutError):
            client.call("foo", timeout_s=1)
    finally:
        client.close()
        t.join()


def test_server_closes_early():
    """Test that if the server closes early, client gets an exception."""
    s = socket.socketpair()

    def run_server():
        io = s[1].makefile("rw")
        io.readline()
        io.write("fo")
        io.flush()
        io.close()
        s[1].close()

    t = threading.Thread(target=run_server)
    t.start()

    client = line_rpc.Client(s[0])

    try:
        with pytest.raises(RuntimeError):
            client.call("foo")
    finally:
        client.close()
        t.join()


def test_client_closes_early():
    """Test that if the client closes early, server doesn't call callback and exists."""
    s = socket.socketpair()

    callback_called = False

    def run_server():
        def fn(_: str) -> str:
            nonlocal callback_called
            callback_called = True
            return "foo"

        line_rpc.server(s[1], fn)

    t = threading.Thread(target=run_server)
    t.start()

    io = s[0].makefile("rw")
    io.write("bar")
    io.flush()
    s[0].close()
    io.close()
    t.join()
    assert not callback_called


def test_restartable_subprocess():
    """Test the basics of restartable subprocess."""
    s = line_rpc.RestartableSubprocess(
        sys.executable,
        [__file__],
    )

    try:
        assert s.call("foo", json.loads)["reply"] == "foo"
    finally:
        s.close()


def test_restartable_subprocess_throws():
    """Test that if the subprocess throws, we recover on the next RPC."""
    s = line_rpc.RestartableSubprocess(
        sys.executable,
        [__file__],
    )

    try:
        response1 = s.call("foo", json.loads)
        assert response1["reply"] == "foo"

        with pytest.raises(RuntimeError):
            s.call("throw", json.loads)

        response2 = s.call("foo", json.loads)
        assert response2["reply"] == "foo"

        assert response1["pid"] != response2["pid"]
    finally:
        s.close()


def test_restartable_parsing_failure():
    """Test that if parsing fails, we restart the server and recover on the next RPC."""
    s = line_rpc.RestartableSubprocess(
        sys.executable,
        [__file__],
    )

    try:
        response1 = s.call("foo", json.loads)
        assert response1["reply"] == "foo"

        def fn(_: str) -> str:
            raise RuntimeError("oops")

        with pytest.raises(RuntimeError):
            s.call("foo", fn)

        response2 = s.call("foo", json.loads)
        assert response2["reply"] == "foo"

        assert response1["pid"] != response2["pid"]
    finally:
        s.close()


def dummy_server(request: str) -> str:
    r = request.rstrip("\n")
    if r == "throw":
        raise RuntimeError("oops")
    return json.dumps(
        {
            "reply": r,
            "pid": os.getpid(),
        }
    )


if __name__ == "__main__":
    line_rpc.server(socket.socket(fileno=0), dummy_server)

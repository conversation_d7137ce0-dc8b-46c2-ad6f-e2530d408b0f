local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

function(cloud)
  // We set a very low threshold because NaNs are not expected at all
  // in an embedding, and is a "correctness" violation.
  // TODO(jeff): Do we need to alert on other embedding errors?
  local nanErrorsSpec = {
    displayName: 'Embeddings Indexer NaN Errors',
    conditionPrometheusQueryLanguage: {
      duration: '300s',
      evaluationInterval: '60s',
      labels: { severity: 'error' },
      query: |||
        (
          sum (
            rate(au_embeddings_indexer_error_counter_total{type="nan_embedding"}[5m])
          )
        )
        /
        (
          sum (
            rate(au_embeddings_indexer_blob_counter_total[5m])
          )
        ) > 0.00001
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      nanErrorsSpec,
      'embeddings-indexer-nan-errors',
      'Embedders are producing NaN embeddings',
      team='completion',
    ),
  ]

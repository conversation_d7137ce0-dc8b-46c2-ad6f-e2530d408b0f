{"text":"\n# Docs for Git (Git)\n\n##  Create\n\nClone an existing repository\n\n    git clone ssh://<EMAIL>/repo.git  \n\n---  \nClone a unique branch from an existing repository\n\n    git clone -b <branchname> --single-branch ssh://<EMAIL>/repo.git  \n\nClone an existing repository and all its sub-modules recursively\n\n    git clone --recurse-submodules ssh://<EMAIL>/repo.git  \n\nCreate a new local repository\n\n    git init  \n\n##  Configuration\n\nSet the name attached to all your commits\n\n    git config [--global] user.name <name>  \n\n---  \nSet the email attached to all your commits\n\n    git config [--global] user.email <email>  \n\nSet colorization of command line output for all repos\n\n    git config --global color.ui auto  \n\nPrint set name (in current repository or globally)\n\n    git config [--global] user.name  \n\nPrint set email (in current repository or globally)\n\n    git config [--global] user.email  \n\n##  Local Changes\n\nList changed files in your working directory\n\n    git status  \n\n---  \nList changes to tracked files\n\n    git diff  \n\nAdd all current changes in file to the next commit\n\n    git add <file>  \n\nAdd all current changes to the next commit\n\n    git add .  \n\nAdd changes to the next commit interactively\n\n    git add -p <file>  \n\nRename file and add it to next commit\n\n    git mv <file> <new file name>  \n\nDelete file and add its deletion to next commit\n\n    git rm <file>  \n\nCommit all local changes in tracked files\n\n    git commit -a  \n\nCommit previously staged changes\n\n    git commit  \n\nChange the last commit\n\n    git commit --amend\n\nNote: You shouldn't amend published commits!","path":"docs/Git/index.md","char_offset":0,"length":1614,"line_offset":0,"length_in_lines":89}
{"text":"# Docs for Git (Git)\n\n##  Commit History\n\nShow all commits\n\n    git log  \n\n---  \nShow changes over time for a specific file\n\n    git log -p <file>  \n\nShow changes over time for a specific committer\n\n    git log --author=<committer name>\n\nNote: `<committer name>` is a pattern, so `Ed` will match `Edward Smith`.\nQuotes are optional if the pattern doesn't contain spaces.  \nSearch (grep) commit messages for the given string\n\n    git log --grep=<string>  \n\nWho changed what and when in file\n\n    git blame <file>  \n\nStore changes temporarily\n\n    git stash  \n\nRemove and apply stashed changes\n\n    git stash pop  \n\nRemove file from all previous commits but keep it locally\n\n    git rm --cached <file>  \n\n##  Branches & Tags\n\nList all local and remote branches\n\n    git branch -a  \n\n---  \nList all remote branches\n\n    git branch -r  \n\nList all local branches\n\n    git branch  \n\nSwitch HEAD branch\n\n    git checkout <branch>  \n\nCreate a new branch based on your current HEAD\n\n    git branch <new-branch>  \n\nCreate a new tracking branch based on a remote branch\n\n    git branch --track <new-branch> <remote-branch>  \n\nDelete a local branch\n\n    git branch -d <branch>  \n\nDelete a remote branch\n\n    git push origin --delete <branch>  \n\nRename a branch locally\n\n    git branch -m <old name> <new name>  \n\nRename a branch on remote\n\n    git push <remote> :<old name>\n    git push <remote> <new name>\n\nTag the current commit\n\n    git tag <tag-name>\n","path":"docs/Git/index.md","char_offset":1614,"length":1443,"line_offset":89,"length_in_lines":86}
{"text":"# Docs for Git (Git)\n\n##  Update & Publish\n\nList all currently configured remotes\n\n    git remote -v  \n\n---  \nShow information about a remote\n\n    git remote show <remote>  \n\nAdd new remote repository\n\n    git remote add <remote> <url>  \n\nRename a remote\n\n    git remote rename <old-name> <new-name>  \n\nDownload all changes from remote, but don't merge into HEAD\n\n    git fetch <remote>  \n\nDownload all changes from remote, but don't merge into HEAD and clean up\ndeleted branches from origin\n\n    git fetch -p <remote>  \n\nDownload changes and directly merge into HEAD\n\n    git pull <remote> <branch>  \n\nPublish local changes on a remote\n\n    git push <remote> <branch>  \n\nTrack a remote repository\n\n    git remote add --track <remote-branch> <remote> <url>  \n\nPublish your tags\n\n    git push --tags  \n\n##  Merge & Rebase\n\nMerge branch into your current HEAD\n\n    git merge <branch>  \n\n---  \nRebase your current HEAD onto branch\n\n    git rebase <branch>\n\nNote: You shouldn't rebase published commits!  \nAbort a rebase\n\n    git rebase --abort  \n\nContinue a rebase after resolving conflicts\n\n    git rebase --continue  \n\nResolve conflicts using your configured merge tool\n\n    git mergetool  \n\nManually resolve conflicts using your editor and mark file as resolved\n\n    git add <resolved-file>\n    git rm <resolved-file>  \n\n##  Undo\n\nDiscard all local changes in your working directory\n\n    git reset --hard HEAD  \n\n---  \nDiscard local changes in a specific file\n\n    git checkout HEAD <file>  \n\nRevert a commit by providing a new commit with contrary changes\n\n    git revert <commit>  \n\nRestore a specific file from a previous commit\n\n    git checkout <commit> <file>  \n\nReset your HEAD pointer to a previous commit\n\n  * Discarding local changes:\n\n        git reset --hard <commit>\n\n  * Preserving all changes as unstaged changes:\n\n        git reset <commit>\n\n  * Preserving uncommitted local changes:\n\n        git reset --keep <commit>\n","path":"docs/Git/index.md","char_offset":3057,"length":1935,"line_offset":175,"length_in_lines":107}
{"text":"# Docs for Git (Git)\n\n##  Submodules\n\nMake changes, commit and checkout submodule files Just go the submodule\ndirectory and use git as usual  \n---  \nList all currently configured submodules\n\n    git submodule\n\nor\n\n    git submodule status  \n\nShow information about a submodule\n\n    git remote show <remote>  \n\nAdd a new submodule Beware of the submodule name you choose: If you use a\nforward slash (`/`) git will think you want to delete the submodule and want\nto add all the files in the submodule directory. Please DON'T use a forward\nslash after the submodule name.\n\n  1. Run `git submodule add -b <branch> --name <name> <repository-path-or-url>`\n  2. Add the `.gitmodule` file and submodule folder to the superproject index\n  3. Commit both files on the superproject\n\nRemove a submodule\n\n  1. `git submodule deinit -f <submodule_path>`\n  2. `rm -rf .git/modules/<submodule_path>`\n  3. `git rm -f <submodule_path>`\n\n(Details)  \nClone a project with submodules\n\n  1. Clone the superproject as usual\n  2. Run `git submodule init` to init the submodules\n  3. Run `git submodule update` to have the submodules on a detached HEAD\n\nor  Run `git clone --recurse-submodules ssh://<EMAIL>/repo.git`  \nSee all changes on submodules\n\n    git diff --submodule  \n\nUpdate the submodules to the lastest changes on their respective branches\n\n    git submodule update --remote  \n\nUpdate a specific submodule to the lastest changes on its branch\n\n    git submodule update --remote <submodule-name>  \n\nPush changes to the superproject only if all submodules are pushed also\n\n    git push --recurse-submodules=check  \n\nPush changes to the submodules and then push the superproject changes\n\n    git push --recurse-submodules=on-demand  \n\nRun arbitrary commands on each submodule\n\n    git submodule foreach '<arbitrary-command-to-run>'  \n\n## Notes\n\n  * Based on the cheat sheet from Tower.app. The original can be found here.\n  * Converted and extended by Jens Kohl.\n","path":"docs/Git/index.md","char_offset":4992,"length":1956,"line_offset":282,"length_in_lines":70}

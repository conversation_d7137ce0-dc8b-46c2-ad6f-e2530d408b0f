import logging
import socket
import subprocess
import threading
import typing


class Client:
    """A client for the line RPC protocol."""

    def __init__(self, sock: socket.socket):
        self.io = sock.makefile("rw")
        self.sock = sock

    def call(self, request: str, timeout_s: float | None = None) -> str:
        """Do a line RPC call.

        Args:
            request: The request to send to the subprocess.
            timeout_s: terminate RPC call if progress isn't made for
                timeout_s seconds. None means no timeout.

        request must not contain newlines.

        timeout_s is not the timeout for the whole RPC call. If, for example, the
        server writes 1 bytes of response every timeout_s seconds,
        the RPC call may not time out.

        Not thread safe.
        """

        self.sock.settimeout(timeout_s)

        assert "\n" not in request

        try:
            self.io.write(request)
            self.io.write("\n")
            self.io.flush()

            response = self.io.readline()
            if not response.endswith("\n"):
                raise RuntimeError("Response ended prematurely")
        except BrokenPipeError as ex:
            raise RuntimeError("Server disconnected") from ex

        return response

    def close(self):
        """Close connection to server.

        Not thread safe.
        """
        self.sock.close()
        self.io.close()

    def __del__(self):
        self.close()


def server(sock: socket.socket, fn: typing.Callable[[str], str]) -> None:
    """Start a server that runs the given function on each request."""
    socket_file = sock.makefile("rw", encoding="utf-8")
    sock.close()

    try:
        while True:
            request = socket_file.readline()
            if not request.endswith("\n"):
                break

            response = fn(request)

            assert "\n" not in response

            socket_file.write(response)
            socket_file.write("\n")
            socket_file.flush()
    except BrokenPipeError:
        logging.info("Client disconnected")
    except Exception as exc:  # pylint: disable=broad-exception-caught
        logging.exception(exc)
    finally:
        socket_file.close()


T = typing.TypeVar("T")


class RestartableSubprocess:
    """Runs a subprocess with line RPC on stdin, restarting it if an RPC fails."""

    def __init__(self, command: str, args: list[str]):
        self.command = command
        self.args = args

        self.chunker: subprocess.Popen | None = None

        # We support a single subprocess
        self.lock = threading.Lock()
        self._setup_process()

    def _setup_process(self):
        self.socket = socket.socketpair()
        try:
            self.client = Client(self.socket[0])

            if self.chunker is not None:
                self.chunker.terminate()
                logging.info("Waiting for subprocess to terminate")
                self.chunker.wait()
                logging.info("Subprocess terminated")
                self.chunker = None

            self.chunker = subprocess.Popen(
                [self.command] + self.args,
                stdin=self.socket[1].fileno(),
            )
        finally:
            self.socket[1].close()

    def call(
        self,
        request: str,
        parse_response: typing.Callable[[str], T],
        timeout_s: float | None = None,
    ) -> T:
        """Create a new call to the subprocess.

        Args:
            request: The request to send to the subprocess.
            parse_response: A function to parse the response from the subprocess.
            timeout_s: see line_rpc.Client.call for more info.

        If the subprocess dies, we restart it.

        Thread safe.
        """

        with self.lock:
            if self.chunker is None:
                self._setup_process()

            try:
                return parse_response(self.client.call(request, timeout_s=timeout_s))
            except Exception:  # pylint: disable=broad-exception-caught
                self.client.close()
                self._setup_process()
                raise

    def close(self):
        """Shut down the subprocess.

        Not thread safe - do not call concurrently with call().
        """
        self.client.close()

        if self.chunker is not None:
            self.chunker.terminate()
            logging.info("Waiting for subprocess to terminate")
            self.chunker.wait()
            logging.info("Subprocess terminated")
            self.chunker = None

    def __del__(self):
        self.close()

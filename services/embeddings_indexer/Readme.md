# Embeddings Indexer

The embeddings indexer subscripts to notifications to the content manager and will be informed about all newly uploaded blobs.

It is then for each blob:

- chunks the blob content ("document") into chunks
- tokenized the given document with the tokenizer configured
- calculate the embeddings for each chunk
- upload the information to the content manager given the configured transformation key.

## Uploaded information

For each blob the indexer should upload for `X` chunk

- The text and additional metadata for each chunk under the sub key `chunk-Y.pb` in the format defined by `chunk.proto`.
- The embeddings encoded in numpy on-disk format with the shape `[x, dim]` with datatype `half` under the sub key `embeddings.npy`

The content manager will resend the notification for any blob until results are uploaded (with the right receipt handle).

## Dependencies

The embeddings indexer depends on the content manager and the embedder host.

## Security considerations

The embeddings indexer is operating on customer data, but on behalf of a user request.
It receives a background service token for the complete namespace with CONTENT_RW scopes.

## Error handling

If an error happens, the indexer will not finish the UploadTransformedBlobContent call.
The content manager will resend the notification for the blob.

## Subprocess chunker

The signature chunker is run in a subprocess because we're
worried about it crashing and taking down the indexer because
Tree-sitter is written in C.  Also, to protect ourselves against
malicious inputs, we might be able to sandbox the subprocess so it
runs with little access to anything (network, etc.)
though today we don't do that.

The embeddings indexer creates the chunker process by launching the
embeddings indexer program with an extra argument "--serve-chunker".
stdin is replaced with a bidirectional
socket which is used to request chunking and retrieve results.

To opt into using the subprocess chunker, the config.json of the embeddings indexer
must have chunker.subprocess_chunker set to true.

The protocol on socket is: send a line of JSON for a request and receive a line of JSON as the reply. See line_rpc.py for the protocol implementation.

To run a chunker in a subprocess, the configuration file for the server must have "subprocess_chunker" set to true.

In addition, the signature chunker is named "signature_raw" for historical purposes.

```
{
  "chunking": {
    "name": "signature_raw",
    "subprocess_chunker": true
  }
}
```

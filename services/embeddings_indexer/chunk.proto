syntax = "proto3";

message DocumentationMetadata {
  // name of the documentation chunk
  string name = 1;
  // page the documentation chunk belongs to
  string page_id = 2;
  // list of section titles each chunk belongs in
  repeated string headers = 3;
  // source ID of the docset the chunk belongs to
  string source_id = 4;
}

message Chunk {
  // text of the chunk
  string text = 1 [debug_redact = true];
  // path of the containing document
  string path = 2 [debug_redact = true];
  // offset in characters from the beginning of the document
  int32 char_offset = 3;
  int32 length = 4;
  int32 line_offset = 5;
  int32 length_in_lines = 6;

  oneof metadata {
    DocumentationMetadata documentation_metadata = 7;
  }

  string header = 8 [debug_redact = true];
}

import {
  addDays,
  addWeeks,
  addMonths,
  format,
  isWeekend,
  subDays,
} from "date-fns";
import type { ChartDataPointSchema, KeyDataSchema } from "app/schemas/chart";

const getTrend = () => Math.floor(Math.random() * 199) - 99;

/**
 * Generates an array of data objects with dates and random values within a specified interval.
 *
 * @param timeFrame - The time frame for the data ('all' | '7' | '30' | '90')
 * @param interval - The interval at which dates should be incremented ('days' | 'weeks' | 'months')
 * @param max - Maximum value for the data
 * @param min - Minimum value for the data
 * @param delay - Delay in milliseconds
 * @returns An array of objects containing dates and their corresponding random values
 */
export const generateChartData = async ({
  timeFrame,
  interval,
  min = 10,
  max = 100000,
  delay = 0,
}: {
  timeFrame: string;
  interval: "days" | "weeks" | "months";
  min?: number;
  max?: number;
  delay?: number;
}) => {
  if (delay) await new Promise((resolve) => setTimeout(resolve, delay));

  const filter = timeFrame === "all" ? 200 : parseInt(timeFrame);
  const startDate = subDays(new Date(), filter);
  const endDate = new Date();
  const trend = getTrend();
  const data: ChartDataPointSchema[] = [];
  let currentDate = startDate;

  while (currentDate <= endDate) {
    let value = 0;
    if (isWeekend(currentDate)) {
      value = Math.floor(Math.random() * (10 - 0 + 1)) + 0; // Adjusted interval for weekends
    } else {
      value = Math.floor(Math.random() * (max - min + 1)) + min;
    }
    data.push({
      date: format(currentDate, "yyyy-MM-dd"),
      value,
    } as ChartDataPointSchema);

    switch (interval) {
      case "days":
        currentDate = addDays(currentDate, 1);
        break;
      case "weeks":
        currentDate = addWeeks(currentDate, 1);
        break;
      case "months":
        currentDate = addMonths(currentDate, 1);
        break;
    }
  }

  return { data, trend };
};

/**
 * Generates data to be used within Bar Charts
 *
 * @param labels - An array of strings to be used as labels
 * @param timeFrame - The time frame for the data ('all' | '7' | '30' | '90')
 * @param delay - Delay in milliseconds
 * @returns An array of objects containing labels and their corresponding random values
 */
export const generateKeyData = async ({
  labels,
  timeFrame,
  delay = 0,
}: {
  labels: string[];
  timeFrame: string;
  delay?: number;
}): Promise<KeyDataSchema[]> => {
  if (delay) await new Promise((resolve) => setTimeout(resolve, delay));
  const multiplier = timeFrame === "all" ? 200 : parseInt(timeFrame);

  return labels
    .map((label) => ({
      label,
      value: Math.floor(Math.random() * 100000 * multiplier),
    }))
    .sort((a, b) => b.value - a.value);
};

function generateRandomString() {
  const length = Math.floor(Math.random() * 6) + 5; // random length between 5 and 10
  const characters = "abcdefghijklmnopqrstuvwxyz";
  let result = "";

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

export function generateDateAndCounts(howMany: number): {
  date: {
    year: number;
    month: number;
    day: number;
  };
  count: number;
}[] {
  const today = new Date();
  const result = [];

  for (let i = 0; i < howMany; i++) {
    const item = {
      date: {
        year: subDays(today, i).getFullYear(),
        month: subDays(today, i).getMonth() + 1,
        day: subDays(today, i).getDate(),
      },
      count: Math.floor(Math.random() * 100),
    };
    result.push(item);
  }

  return result;
}
export function generateChatStats(howMany: number): {
  aggregation: {
    case: string;
    value: {
      year: number;
      month: number;
      day: number;
    };
  };
  requestCount: number;
}[] {
  const today = new Date();
  const result = [];

  for (let i = 0; i < howMany; i++) {
    const item = {
      aggregation: {
        case: "date",
        value: {
          year: subDays(today, i).getFullYear(),
          month: subDays(today, i).getMonth() + 1,
          day: subDays(today, i).getDate(),
        },
      },
      requestCount: Math.floor(Math.random() * 100),
    };
    result.push(item);
  }

  return result;
}
export function generateEditStats(howMany: number): {
  aggregation: {
    case: string;
    value: {
      year: number;
      month: number;
      day: number;
    };
  };
  requestCount: number;
  acceptedCount: number;
  acceptedLineCount: number;
  acceptedCharacterCount: number;
}[] {
  const today = new Date();
  const result = [];

  for (let i = 0; i < howMany; i++) {
    const item = {
      aggregation: {
        case: "date",
        value: {
          year: subDays(today, i).getFullYear(),
          month: subDays(today, i).getMonth() + 1,
          day: subDays(today, i).getDate(),
        },
      },
      requestCount: Math.floor(Math.random() * 100),
      acceptedCount: Math.floor(Math.random() * 100),
      acceptedLineCount: Math.floor(Math.random() * 100),
      acceptedCharacterCount: Math.floor(Math.random() * 100),
    };
    result.push(item);
  }

  return result;
}
export function generateKeywordsStats(howMany: number): {
  keyword: string;
  count: number;
}[] {
  const result = [];

  for (let i = 0; i < howMany; i++) {
    const item = {
      keyword: generateRandomString(),
      count: Math.floor(Math.random() * 100),
    };
    result.push(item);
  }

  return result;
}

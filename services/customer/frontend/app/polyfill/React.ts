import type { PromiseState } from "@augment-internal/ts-utils/promise";
import { assertUnreachable } from "@augment-internal/ts-utils/type";
import React, { useEffect, useState } from "react";

export const use = (("use" in React && React.use) || usePolyfill) as <T>(
  promise: Promise<T>,
) => T;

function usePolyfill<T>(promise: Promise<T>): T {
  const [state, setState] = useState<PromiseState<T>>({
    status: "pending",
  });

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    if (!promise) return;

    let isMounted = true;

    setState({ status: "pending" });

    promise
      .then((value) => {
        if (isMounted) {
          setState({ status: "resolved", value });
        }
      })
      .catch((reason) => {
        if (isMounted) {
          setState({ status: "rejected", reason });
        }
      });

    return () => {
      isMounted = false;
    };
  }, [promise]);

  if (state.status === "pending") {
    throw promise; // Suspends the component
  }
  if (state.status === "rejected") {
    throw state.reason;
  }
  if (state.status === "resolved") {
    return state.value!;
  }
  assertUnreachable(state);
}

import { QueryCache, QueryClient } from "@tanstack/react-query";
import { produce } from "immer";
import { toast } from "../components/ui/Toast";
import { DataFetchingError, UnauthorizedError } from "./error";
import { reduceFns } from "app/utils/function";

/**
 * Middleware to wrap the queryClient's setQueryData method with Immer.
 * @example
 * queryClient.setQueryData(["team"], team => {
 *   team.users.push(newUserData);
 * });
 */
function withImmerMiddleware(queryClient: QueryClient) {
  function wrapUpdater<Updater>(updater: Updater): Updater {
    // If updater is a function, wrap it with I<PERSON>'s produce.
    if (typeof updater === "function") {
      type UpdaterFn = Updater extends (prevData: any) => any ? Updater : never;
      type PrevData = Parameters<UpdaterFn>[0];
      return ((prevData: PrevData) => produce(prevData, updater)) as UpdaterFn;
    }
    return updater;
  }
  const originalSetQueryData = queryClient.setQueryData.bind(queryClient);
  queryClient.setQueryData = (queryKey, updater, options) => {
    return originalSetQueryData(queryKey, wrapUpdater(updater), options);
  };
  const originalSetQueriesData = queryClient.setQueriesData.bind(queryClient);
  queryClient.setQueriesData = (filters, updater, options) => {
    return originalSetQueriesData(filters, wrapUpdater(updater), options);
  };
  return queryClient;
}

export const applyMiddleware = reduceFns(
  (queryClient) => queryClient,
  withImmerMiddleware,
  // Add more middlewares here
);

export const queryClient = applyMiddleware(
  new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 10 * 1000, // 10 minutes
      },
    },
    queryCache: new QueryCache({
      onError: (err) => {
        if (err instanceof DataFetchingError) {
          toast.error({
            title: "Data Fetching Error",
            description: `Error fetching data from '/api/${err.endpoint}'.`,
          });
        } else if (err instanceof UnauthorizedError) {
          // there doesn't appear to be a very clean way to do this since
          // it's incorrect to use the useNavigate hook outside of a component
          window.location.href = "/login";
        } else {
          console.error(err);
          toast.error({
            title: "Request Failed",
            description: "An unknown error occurred.",
          });
        }
      },
    }),
  }),
);

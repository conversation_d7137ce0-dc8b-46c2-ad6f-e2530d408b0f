import { queryClient } from "../queryClient";
import { mutationOptions } from "../queryOptions";
import { subscriptionQueryOptions, userQueryOptions } from "../queries";
import { toast } from "app/components/ui/Toast";
import type { EmptyObject } from "@augment-internal/ts-utils/type";
import type { PutUserOnPlanRequestSchema } from "app/schemas/put-user-on-plan";

export const putUserOnPlan = mutationOptions({
  mutationFn: async ({ planId }: PutUserOnPlanRequestSchema) => {
    const response = await fetch(`/api/put-user-on-plan`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ planId }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "Failed to update plan");
    }

    return data;
  },
  onSuccess: async (data) => {
    // Log the success for debugging
    console.info("Plan updated successfully:", data);

    // Check if the user is already on this plan
    if (data.alreadyOnPlan) {
      toast({
        title: "Already on this plan",
        description: data.message || "You are already on this plan.",
        status: "default",
      });
      return {} as EmptyObject;
    }

    // Show success toast
    toast.success({
      title: "Plan Update Initiated",
      description: "Your plan is being processed. Please wait...",
    });

    // Invalidate the specific queries that need to be refreshed
    // This will trigger a refetch when the user navigates to the account page
    queryClient.invalidateQueries(userQueryOptions);
    queryClient.invalidateQueries(subscriptionQueryOptions);

    return {} as EmptyObject;
  },
  onError: (error) => {
    // Log the error for debugging
    console.error("Error updating plan:", error);

    // Show error toast
    toast.error({
      title: "Plan Update Failed",
      description: "Failed to update plan. Please try again.",
    });
  },
});

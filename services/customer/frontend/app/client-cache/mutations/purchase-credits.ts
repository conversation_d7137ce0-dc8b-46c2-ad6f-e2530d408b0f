import { queryClient } from "../queryClient";
import { mutationOptions } from "../queryOptions";
import { subscriptionQueryOptions } from "../queries";
import { toFormData } from "app/utils/url";
import type { EmptyObject } from "@augment-internal/ts-utils/type";
import { toast } from "app/components/ui/Toast";
import { creditsQueryOptions } from "../queries/credits";

export const purchaseCredits = mutationOptions({
  mutationFn: async (credits: number) => {
    // Validate the credits amount
    // Spend must be between $10 and $100, 10 credits per $1
    if (credits < 100 || credits > 1000) {
      throw new Error("Credits must be between 100 and 1000");
    }

    const response = await fetch(`/api/credits`, {
      method: "POST",
      body: toFormData({ credits: credits.toString() }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "Failed to purchase credits");
    }

    return {} as EmptyObject;
  },
  onSuccess: async () => {
    // Invalidate the subscription query to refresh the data
    await queryClient.invalidateQueries(creditsQueryOptions);
    await queryClient.invalidateQueries(subscriptionQueryOptions);
    toast.success({
      title: "Credits Purchased",
      description: "Your credits have been successfully updated.",
    });
  },
  onError: () => {
    toast.error({
      title: "Failed to purchase credits",
      description: "Please try again later.",
    });
  },
});

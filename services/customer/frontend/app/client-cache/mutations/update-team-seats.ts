import { queryClient } from "../queryClient";
import { teamQueryOptions } from "../queries/team";
import { mutationOptions } from "../queryOptions";
import {
  type TeamSeatsRequestSchema,
  TeamSeatsResponseSchema,
  type TeamSeatsSuccessSchema,
} from "../../schemas/team-seats";
import { isTeamActive } from "app/schemas/team";
import { subscriptionQueryOptions } from "../queries";
import { toast } from "app/components/ui/Toast";

export const updateTeamSeats = mutationOptions({
  mutationFn: async (seats: number) => {
    const requestData: TeamSeatsRequestSchema = { seats };

    const response = await fetch("/api/team", {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestData),
    });

    const data = await response.json();

    // Validate the response data
    const validatedData = TeamSeatsResponseSchema.parse(data);

    if ("message" in validatedData) {
      // response is not ok
      throw new Error(validatedData.message || "Failed to update seats");
    }

    return validatedData as TeamSeatsSuccessSchema;
  },
  onMutate: async (newSeats: number) => {
    // Cancel any outgoing refetches
    await queryClient.cancelQueries(teamQueryOptions);

    // Snapshot the previous value
    const previousTeam = queryClient.getQueryData(teamQueryOptions.queryKey);

    // Optimistically update to the new value
    queryClient.setQueryData(teamQueryOptions.queryKey, (teamResponse: any) => {
      if (!isTeamActive(teamResponse)) {
        console.warn("Team is not active, cannot update seats");
        return teamResponse;
      }
      teamResponse.team.seats = newSeats;
    });

    // Return a context object with the snapshotted value
    return { previousTeam };
  },
  onSuccess: () => {
    // Invalidate and refetch
    queryClient.invalidateQueries(teamQueryOptions);
    queryClient.invalidateQueries(subscriptionQueryOptions);
    toast.success({
      title: "Seats Updated",
      description: "Your team seats have been successfully updated.",
    });
  },
  onError: (_err, _newSeats, context) => {
    // If the mutation fails, use the context returned from onMutate to roll back
    // logger.warn("Failed to roll back team seats update");
    if (context?.previousTeam !== undefined) {
      queryClient.setQueryData(teamQueryOptions.queryKey, context.previousTeam);
    } else {
      queryClient.invalidateQueries(teamQueryOptions);
      queryClient.invalidateQueries(subscriptionQueryOptions);
    }
    toast.error({
      title: "Failed to update seats",
      description: "Please try again later.",
    });
  },
});

import { mutationOptions } from "../queryOptions";
import { toast } from "app/components/ui/Toast";
import type {
  CreateCheckoutSessionRequestSchema,
  CreateCheckoutSessionResponseSchema,
} from "app/schemas/create-checkout-session";

export const createCheckoutSession = mutationOptions({
  mutationFn: async ({ planId }: CreateCheckoutSessionRequestSchema) => {
    const response = await fetch("/api/create-checkout-session", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ planId }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || "Failed to create checkout session");
    }

    return data as CreateCheckoutSessionResponseSchema;
  },
  onSuccess: (data) => {
    // If it's a Community plan, don't redirect
    if (data.isCommunityPlan) {
      return;
    }

    // Redirect to the checkout URL (<PERSON><PERSON>'s external page)
    if (data.url) {
      window.location.href = data.url;
    } else {
      toast.error({
        title: "Error",
        description: "No checkout URL was returned. Please try again.",
      });
    }
  },
  onError: (error) => {
    console.error("Error creating checkout session:", error);
    toast.error({
      title: "Error",
      description: "Failed to create checkout session. Please try again.",
    });
  },
});

import { queryClient } from "../queryClient";
import { mutationOptions } from "../queryOptions";
import { subscriptionQueryOptions } from "../queries";
import type { EmptyObject } from "@augment-internal/ts-utils/type";

export const cancelSubscription = mutationOptions({
  mutationFn: async () => {
    const response = await fetch(`/api/subscription`, {
      method: "DELETE",
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to delete subscription");
    }

    return {} as EmptyObject;
  },
  onSuccess: () => {
    queryClient.invalidateQueries(subscriptionQueryOptions);
  },
});

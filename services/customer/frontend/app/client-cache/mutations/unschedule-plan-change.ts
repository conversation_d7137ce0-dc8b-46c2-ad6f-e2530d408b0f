import { queryClient } from "../queryClient";
import { mutationOptions } from "../queryOptions";
import { subscriptionQueryOptions } from "../queries";
import { toast } from "app/components/ui/Toast";
import type { EmptyObject } from "@augment-internal/ts-utils/type";

export const unschedulePlanChange = mutationOptions({
  mutationFn: async () => {
    const response = await fetch(`/api/subscription`, {
      method: "PUT",
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Failed to unschedule plan change");
    }

    return {} as EmptyObject;
  },
  onSuccess: () => {
    queryClient.invalidateQueries(subscriptionQueryOptions);
    toast.success({
      title: "Plan change unscheduled",
      description: "Your scheduled plan change has been successfully canceled.",
    });
  },
  onError: () => {
    toast.error({
      title: "Failed to unschedule plan change",
      description: "Please try again later.",
    });
  },
});

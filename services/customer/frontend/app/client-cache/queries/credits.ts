import { queryOptions } from "../queryOptions";
import { GetUserOrbCreditsInfoResponseSchema } from "../../schemas/credits";
import { queryFetch } from "../../utils/query-fetch";

export const creditsQueryOptions = queryOptions({
  queryKey: ["credits"],
  queryFn: queryFetch("/api/credits", GetUserOrbCreditsInfoResponseSchema),
  refetchInterval: (query) => {
    // Poll every 2 seconds if there are credits pending
    return (query.state.data?.usageUnitsPending ?? 0) > 0 ? 2_000 : false;
  },
});

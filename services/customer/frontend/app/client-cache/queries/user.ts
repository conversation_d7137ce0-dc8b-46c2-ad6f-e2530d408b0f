import { queryOptions } from "../queryOptions";
import { UserApiGETResponseSchema } from "../../schemas/user";
import { queryFetch } from "app/utils/query-fetch";

export const userQueryOptions = queryOptions({
  queryKey: ["userData"],
  queryFn: queryFetch("/api/user", UserApiGETResponseSchema),
  refetchInterval: (query) => {
    // Poll every second if plan is pending, otherwise don't poll
    return query.state.data?.plan.pending ? 1_000 : false;
  },
});

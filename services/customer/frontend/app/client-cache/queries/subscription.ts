import { queryOptions } from "../queryOptions";
import { OrbCustomerInfoSchema } from "../../schemas/orb";
import { queryFetch } from "app/utils/query-fetch";
import { queryClient } from "../queryClient";
import { userQueryOptions } from "./user";

export const subscriptionQueryOptions = queryOptions({
  queryKey: ["subscription"],
  queryFn: queryFetch("/api/subscription", OrbCustomerInfoSchema),
  refetchInterval: (_query) => {
    // Poll every 5 seconds if user plan is pending, otherwise don't poll
    // This ensures subscription data (including trial info and plan name) stays in sync during plan changes
    const userData = queryClient.getQueryData(userQueryOptions.queryKey);
    return userData?.plan?.pending ? 5_000 : false;
  },
});

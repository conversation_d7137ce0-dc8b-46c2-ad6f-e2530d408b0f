import type {
  UndefinedInitialDataOptions,
  QueryFunction,
} from "@tanstack/react-query";
import type { YearMonthDay } from "../../utils/date";
import { stringifyUrl } from "../../utils/url";
import { queryOptions } from "../queryOptions";
import { DataFetchingError } from "../error";

function createQuery<TData>(
  endpoint: string,
  params: { [key: string]: string | object },
): QueryFunction<TData, unknown[]> {
  return async function fetchData() {
    const response = await fetch(`/api/${endpoint}?${stringifyUrl(params)}`);

    if (!response.ok) {
      throw new DataFetchingError(endpoint);
    }

    const data = await response.json();
    return data as TData;
  };
}

export type DateRangeQueryKey = [string, YearMonthDay, YearMonthDay];

export const dateRangeQuery = queryOptions(function dateRangeQueryOptions<
  TData,
>(
  endpoint: string,
  startDate: YearMonthDay,
  endDate: YearMonthDay,
  options?: Omit<
    UndefinedInitialDataOptions<TData, Error, TData, DateRangeQueryKey>,
    "queryKey" | "queryFn"
  >,
) {
  return {
    queryKey: [endpoint, startDate, endDate],
    queryFn: createQuery<TData>(endpoint, { startDate, endDate }),
    ...options,
  };
});

type DateRangeRequestTypeQueryKey = [
  string,
  YearMonthDay,
  YearMonthDay,
  string,
];

export const dateRangeRequestTypeQueryOptions = queryOptions(
  function dateRangeRequestTypeQueryOptions<TData>(
    endpoint: string,
    startDate: YearMonthDay,
    endDate: YearMonthDay,
    requestType: string,
    options?: Omit<
      UndefinedInitialDataOptions<
        TData,
        Error,
        TData,
        DateRangeRequestTypeQueryKey
      >,
      "queryKey" | "queryFn"
    >,
  ) {
    return {
      queryKey: [endpoint, startDate, endDate, requestType],
      queryFn: createQuery<TData>(endpoint, {
        startDate,
        endDate,
        requestType,
      }),
      ...options,
    };
  },
);

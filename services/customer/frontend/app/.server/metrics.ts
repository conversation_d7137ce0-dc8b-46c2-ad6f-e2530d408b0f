import Prometheus, { Counter } from "prom-client";
import { logger } from "@augment-internal/logging";
/**
 * This prevents hmr from blowing up when registering metrics.  Please
 * use this to register your metrics, rather than calling the prom-client
 * directly.
 *
 */

export function registerCounter(
  ...[props]: ConstructorParameters<typeof Counter>
) {
  const metric = new Counter({ ...props, registers: [] });
  try {
    Prometheus.register.registerMetric(metric);
  } catch (e) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore - import.meta.hot is a vite thing fixing the build to include is harder than this
    if (import.meta.hot || process.env.HMR_PORT) {
      logger.info(
        "Failed to register metric, probably because of hot reload",
        e,
      );
      return metric!;
    }
    throw e;
  }
  return metric;
}

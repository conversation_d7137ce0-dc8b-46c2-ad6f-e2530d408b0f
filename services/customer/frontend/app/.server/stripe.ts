import Stripe from "stripe";
import { Config } from "./config";
import { logger } from "@augment-internal/logging";

function createStripeClient() {
  try {
    logger.info("Initializing Stripe client");
    if (!Config.STRIPE_SECRET) {
      logger.error("STRIPE_SECRET is empty or undefined");
      throw new Error("Missing Stripe secret key");
    }
    return new Stripe(Config.STRIPE_SECRET, {
      apiVersion: "2025-02-24.acacia",
    });
  } catch (error) {
    logger.error("Failed to initialize Stripe client", { error });
    // Create a non-functional client that will throw errors if used
    return new Stripe("" as any, {
      apiVersion: "2025-02-24.acacia",
    });
  }
}
const stripe = createStripeClient();
export { stripe };

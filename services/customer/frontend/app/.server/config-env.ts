/**
 * Kubernetes environment variables. They
 * also need to be set in deploy.jsonnet.
 */
export const ENDPOINTS = [
  "TOKEN_EXCHANGE_ENDPOINT",
  "RI_ANALYTICS_ENDPOINT_TEMPLATE",
  "SLACK_BOT_PROCESSOR_ENDPOINT_TEMPLATE",
  "<PERSON><PERSON><PERSON>_ENDPOINT_TEMPLATE",
  "GITHUB_PROCESSOR_ENDPOINT_TEMPLATE",
  "SHARE_ENDPOINT_TEMPLATE",
  "NOTION_ENDPOINT_TEMPLATE",
  "ATLASSIAN_ENDPOINT_TEMPLATE",
  "LINEAR_ENDPOINT_TEMPLATE",
  "SUPABASE_ENDPOINT_TEMPLATE",
  "TENANT_WATCHER_ENDPOINT",
  "AUTH_CENTRAL_ENDPOINT",
  "DYNAMIC_FEATURE_FLAGS_ENDPOINT",
] as const;

/**
 * These are config files, they
 * are mounted as secrets or config maps.
 */
export const CONFIG_FILES = [
  "AUTH_SECRETS_PATH",
  "STRIPE_SECRET_PATH",
  "FEATURE_FLAGS_SDK_KEY_PATH",
  "REQUEST_INSIGHT_PUBLISHER_CONFIG_FILE",
] as const;

/**
 * Shh...
 */
export const SECRETS = [
  "AUTH_CLIENT_ID",
  "AUTH_CLIENT_SECRET",
  "SESSION_SECRET",
  "STRIPE_SECRET",
  "FEATURE_FLAGS_SDK_KEY",
] as const;

/**
 * These vars will be provided by kubernetes.
 *
 */
export const ENV_VARS = [
  "NAMESPACE",
  "CURRENT_CLOUD",
  "POD_NAME",
  "POD_ENV",
  "AUTH_CALLBACK_URL",
  "AUTH_ENDPOINT",
  "AUTH_LOGOUT_ENDPOINT",
  "CLOUD_DOMAIN_SUFFIXES",
  "AUTH_ENDPOINT",
  "AUTH_LOGOUT_ENDPOINT",
  "AUTH_CALLBACK_URL",
  "ORB_PLANS_CONFIG",
] as const;

import { Config } from "../config";
import { describe, it, expect } from "vitest";

describe("Config", () => {
  describe("format", () => {
    it("should replace %s placeholders with provided arguments", () => {
      const result = Config.format(
        "Hello, %s! You are %s years old.",
        "<PERSON>",
        30,
      );
      expect(result).toBe("Hello, <PERSON>! You are 30 years old.");
    });

    it("should handle multiple %s placeholders", () => {
      const result = Config.format("%s %s %s", "one", "two", "three");
      expect(result).toBe("one two three");
    });

    it("should leave unmatched %s placeholders unchanged", () => {
      const result = Config.format("Too many %s placeholders %s", "arguments");
      expect(result).toBe("Too many arguments placeholders %s");
    });
    it("should leave ignore no place holders", () => {
      const result = Config.format("hello world", "arguments");
      expect(result).toBe("hello world");
    });
  });
});

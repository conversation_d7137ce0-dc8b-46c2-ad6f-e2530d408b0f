import { describe, it, expect, vi, beforeEach } from "vitest";
import { getUserPendingStatus } from "../subscription-logic";
import type { SessionUser } from "../auth";
import {
  OrbSubscriptionInfo_SubscriptionStatus,
  type GetUserOrbSubscriptionInfoResponse,
} from "~services/auth/central/server/auth_pb";
import { User_SubscriptionCreationInfo_Status } from "~services/auth/central/server/auth_entities_pb";

const mocks = vi.hoisted(() => {
  const authCentralInstance = {
    getUser: vi.fn(),
    getTenantPlanStatus: vi.fn(),
  };

  const tenantWatcherInstance = {
    tenantFor: vi.fn(),
  };

  return {
    isUserInSelfServeTeam: vi.fn(),
    AuthCentralClient: {
      getInstance: () => authCentralInstance,
    },
    authCentralInstance,
    TenantWatcherCachingClient: {
      getInstance: () => tenantWatcherInstance,
    },
    tenantWatcherInstance,
  };
});

vi.mock("app/utils/team.server", () => ({
  isUserInSelfServeTeam: mocks.isUserInSelfServeTeam,
}));

vi.mock("../grpc/auth-central", () => ({
  AuthCentralClient: mocks.AuthCentralClient,
}));

vi.mock("../grpc/tenant-watcher", () => ({
  TenantWatcherCachingClient: mocks.TenantWatcherCachingClient,
}));

vi.mock("../config", () => ({
  Config: {
    createGrpcTransportOptions: vi.fn().mockReturnValue({}),
    TENANT_WATCHER_ENDPOINT: "mock://localhost:9090",
    AUTH_CENTRAL_ENDPOINT: "mock://localhost:9091",
  },
}));

describe("subscription-logic.ts", () => {
  const mockUser: SessionUser = {
    userId: "test-user-id",
    tenantId: "test-tenant-id",
    tenantName: "Test Tenant",
    shardNamespace: "test-namespace",
    email: "<EMAIL>",
    roles: ["USER"],
    createdAt: Date.now(),
    sessionId: "test-session-id",
  };

  const mockUserResponse = {
    user: {
      id: "test-user-id",
      tenants: ["test-tenant-id"],
      tierChange: null,
      subscriptionCreationInfo: null,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mocks.authCentralInstance.getUser.mockResolvedValue(mockUserResponse);
    mocks.authCentralInstance.getTenantPlanStatus.mockResolvedValue({
      isPending: false,
    });
    mocks.isUserInSelfServeTeam.mockResolvedValue(false);
  });

  describe("getUserPendingStatus", () => {
    it("should throw error when user information cannot be retrieved", async () => {
      mocks.authCentralInstance.getUser.mockResolvedValue({ user: null });

      await expect(getUserPendingStatus(mockUser, undefined)).rejects.toThrow(
        "Failed to get user information.",
      );
    });

    describe("self-serve team scenarios", () => {
      beforeEach(() => {
        mocks.isUserInSelfServeTeam.mockResolvedValue(true);
      });

      it("should return 'planChangePending' when tenant plan status is pending", async () => {
        mocks.authCentralInstance.getTenantPlanStatus.mockResolvedValue({
          isPending: true,
        });

        const result = await getUserPendingStatus(mockUser, undefined);
        expect(result).toBe("planChangePending");
      });

      it("should return 'subscriptionCreationPending' when subscription case is pendingSubscription", async () => {
        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "pendingSubscription",
            value: {},
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe("subscriptionCreationPending");
      });

      it("should return null when no pending conditions are met", async () => {
        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "subscription",
            value: {
              subscriptionStatus: OrbSubscriptionInfo_SubscriptionStatus.ACTIVE,
            },
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe(null);
      });

      it("should return 'noSubscription' when subscription is undefined", async () => {
        const result = await getUserPendingStatus(mockUser, undefined);
        expect(result).toBe("noSubscription");
      });
    });

    describe("non-self-serve team scenarios", () => {
      beforeEach(() => {
        mocks.isUserInSelfServeTeam.mockResolvedValue(false);
      });

      it("should return 'planChangePending' when user has tier change", async () => {
        const userResponseWithTierChange = {
          user: {
            ...mockUserResponse.user,
            tierChange: { id: "tier-change-id" },
          },
        };
        mocks.authCentralInstance.getUser.mockResolvedValue(
          userResponseWithTierChange,
        );

        const result = await getUserPendingStatus(mockUser, undefined);
        expect(result).toBe("planChangePending");
      });

      it("should return 'subscriptionCreationPending' when subscription creation is pending and subscription is provided", async () => {
        const userResponseWithPendingSubscription = {
          user: {
            ...mockUserResponse.user,
            subscriptionCreationInfo: {
              status: User_SubscriptionCreationInfo_Status.PENDING,
            },
          },
        };
        mocks.authCentralInstance.getUser.mockResolvedValue(
          userResponseWithPendingSubscription,
        );

        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "subscription",
            value: {
              subscriptionStatus: OrbSubscriptionInfo_SubscriptionStatus.ACTIVE,
            },
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe("subscriptionCreationPending");
      });

      it("should return 'subscriptionCreationPending' when subscription creation is pending even if subscription is undefined", async () => {
        const userResponseWithPendingSubscription = {
          user: {
            ...mockUserResponse.user,
            subscriptionCreationInfo: {
              status: User_SubscriptionCreationInfo_Status.PENDING,
            },
          },
        };
        mocks.authCentralInstance.getUser.mockResolvedValue(
          userResponseWithPendingSubscription,
        );

        const result = await getUserPendingStatus(mockUser, undefined);
        expect(result).toBe("subscriptionCreationPending");
      });

      it("should return 'subscriptionCreationPending' when subscription case is pendingSubscription", async () => {
        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "pendingSubscription",
            value: {},
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe("subscriptionCreationPending");
      });

      it("should return 'noSubscription' when subscription is undefined and no subscription creation is pending", async () => {
        // Use default mockUserResponse which has no subscription creation pending
        const result = await getUserPendingStatus(mockUser, undefined);
        expect(result).toBe("noSubscription");
      });
    });

    describe("subscription status scenarios", () => {
      it("should return 'noSubscription' when subscription case is nonexistentSubscription", async () => {
        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "nonexistentSubscription",
            value: {},
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe("noSubscription");
      });

      it("should return 'noSubscription' when subscription status is ENDED", async () => {
        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "subscription",
            value: {
              subscriptionStatus: OrbSubscriptionInfo_SubscriptionStatus.ENDED,
            },
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe("noSubscription");
      });

      it("should return null when subscription is active", async () => {
        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "subscription",
            value: {
              subscriptionStatus: OrbSubscriptionInfo_SubscriptionStatus.ACTIVE,
            },
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe(null);
      });

      it("should return noSubscription when subscription is undefined", async () => {
        const result = await getUserPendingStatus(mockUser, undefined);
        expect(result).toBe("noSubscription");
      });
    });

    describe("complex scenarios", () => {
      it("should prioritize plan change pending over subscription creation pending in self-serve teams", async () => {
        mocks.isUserInSelfServeTeam.mockResolvedValue(true);
        mocks.authCentralInstance.getTenantPlanStatus.mockResolvedValue({
          isPending: true,
        });

        const subscription: GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"] =
          {
            case: "pendingSubscription",
            value: {},
          };

        const result = await getUserPendingStatus(mockUser, subscription);
        expect(result).toBe("planChangePending");
      });

      it("should prioritize tier change over subscription creation pending in non-self-serve teams", async () => {
        mocks.isUserInSelfServeTeam.mockResolvedValue(false);

        const userResponseWithBoth = {
          user: {
            ...mockUserResponse.user,
            tierChange: { id: "tier-change-id" },
            subscriptionCreationInfo: {
              status: User_SubscriptionCreationInfo_Status.PENDING,
            },
          },
        };
        mocks.authCentralInstance.getUser.mockResolvedValue(
          userResponseWithBoth,
        );

        const result = await getUserPendingStatus(mockUser, undefined);
        expect(result).toBe("planChangePending");
      });
    });

    describe("error handling", () => {
      it("should propagate errors from AuthCentralClient.getUser", async () => {
        const error = new Error("Auth service error");
        mocks.authCentralInstance.getUser.mockRejectedValue(error);

        await expect(getUserPendingStatus(mockUser, undefined)).rejects.toThrow(
          "Auth service error",
        );
      });

      it("should propagate errors from isUserInSelfServeTeam", async () => {
        const error = new Error("Team service error");
        mocks.isUserInSelfServeTeam.mockRejectedValue(error);

        await expect(getUserPendingStatus(mockUser, undefined)).rejects.toThrow(
          "Team service error",
        );
      });

      it("should propagate errors from getTenantPlanStatus", async () => {
        mocks.isUserInSelfServeTeam.mockResolvedValue(true);
        const error = new Error("Tenant plan status error");
        mocks.authCentralInstance.getTenantPlanStatus.mockRejectedValue(error);

        await expect(getUserPendingStatus(mockUser, undefined)).rejects.toThrow(
          "Tenant plan status error",
        );
      });
    });
  });
});

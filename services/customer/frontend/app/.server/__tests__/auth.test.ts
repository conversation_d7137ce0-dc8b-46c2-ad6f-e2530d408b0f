import {
  type SessionUser,
  isAdmin,
  redirectTo,
  forTesting,
  authenticator,
} from "../auth";
import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import { TenantTier } from "~services/tenant_watcher/tenant_watcher_pb";
import type * as RemixAuth from "remix-auth";

type MockAuthenticator = typeof authenticator & {
  isAuthenticated: MockedFunction<
    (request: Request) => Promise<SessionUser | null>
  >;
};

const mocks = vi.hoisted(() => {
  const tenantWatcherInstance = {
    tenantFor: vi.fn(),
  };

  const mockGetSession = vi.fn();

  return {
    isEnterpriseTier: vi.fn(),
    TenantWatcherCachingClient: {
      getInstance: () => tenantWatcherInstance,
    },
    tenantWatcherInstance,
    getSession: mockGetSession,
  };
});

vi.mock("remix-auth", async () => {
  const originalRemixAuth: typeof RemixAuth =
    await vi.importActual("remix-auth");

  class MockAuthenticator extends originalRemixAuth.Authenticator {
    isAuthenticated = vi.fn();
  }
  return {
    Authenticator: MockAuthenticator,
  };
});

// Mock TenantWatcherCachingClient
vi.mock("../grpc/tenant-watcher", () => {
  return {
    TenantWatcherCachingClient: mocks.TenantWatcherCachingClient,
  };
});

vi.mock("../session", async () => ({
  ...(await vi.importActual("../session")),
  getSession: mocks.getSession,
}));

describe("auth.ts", () => {
  describe("isAdmin", () => {
    it("should return true for an enterprise user with admin role", async () => {
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.ENTERPRISE,
      });
      const user: SessionUser = {
        userId: "1",
        sessionId: "1",
        tenantId: "1",
        tenantName: "Test Tenant",
        shardNamespace: "test",
        email: "<EMAIL>",
        roles: ["ADMIN"],
        createdAt: Date.now(),
      };
      expect(await isAdmin(user)).toBe(true);
    });

    it("should return false for an enterprise user without admin role", async () => {
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.ENTERPRISE,
      });
      const user: SessionUser = {
        userId: "1",
        sessionId: "1",
        tenantId: "1",
        tenantName: "Test Tenant",
        shardNamespace: "test",
        email: "<EMAIL>",
        roles: [CustomerUiRole[CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE]],
        createdAt: Date.now(),
      };
      expect(await isAdmin(user)).toBe(false);
    });

    it("should return true for a self serve team user with admin role", async () => {
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.PROFESSIONAL,
        config: {
          configs: {
            is_self_serve_team: "true",
          },
        },
      });
      const user: SessionUser = {
        userId: "1",
        sessionId: "1",
        tenantId: "1",
        tenantName: "Test Tenant",
        shardNamespace: "test",
        email: "<EMAIL>",
        roles: ["ADMIN"],
        createdAt: Date.now(),
      };
      expect(await isAdmin(user)).toBe(true);
    });

    it("should return false for a self serve team user without admin role", async () => {
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.ENTERPRISE,
        config: {
          configs: {
            is_self_serve_team: "true",
          },
        },
      });
      const user: SessionUser = {
        userId: "1",
        sessionId: "1",
        tenantId: "1",
        tenantName: "Test Tenant",
        shardNamespace: "test",
        email: "<EMAIL>",
        roles: [CustomerUiRole[CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE]],
        createdAt: Date.now(),
      };
      expect(await isAdmin(user)).toBe(false);
    });

    it("should return true for a non-self-serve team user", async () => {
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.PROFESSIONAL,
      });
      const user: SessionUser = {
        userId: "1",
        sessionId: "1",
        tenantId: "1",
        tenantName: "Test Tenant",
        shardNamespace: "test",
        email: "<EMAIL>",
        roles: [],
        createdAt: Date.now(),
      };
      expect(await isAdmin(user)).toBe(true);
    });

    it("should return true for a community user", async () => {
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.COMMUNITY,
      });
      const user: SessionUser = {
        userId: "1",
        sessionId: "1",
        tenantId: "1",
        tenantName: "Test Tenant",
        shardNamespace: "test",
        email: "<EMAIL>",
        roles: [],
        createdAt: Date.now(),
      };
      expect(await isAdmin(user)).toBe(true);
    });
  });

  describe("redirectTo", () => {
    it("should return default path when URL is undefined", () => {
      expect(redirectTo(undefined)).toBe("/dashboard/overview");
    });
    it("should return a URL when valid", () => {
      expect(redirectTo("/do/stuff")).toBe("/do/stuff");
    });
    it("should return the pathname and search for a valid URL", () => {
      const url = new URL("http://example.com/test?param=value");
      expect(redirectTo(url)).toBe("/test?param=value");
    });

    it("should return default path for API or logout URLs", () => {
      expect(redirectTo("/api/test")).toBe("/dashboard/overview");
      expect(redirectTo("/logout")).toBe("/dashboard/overview");
    });
  });

  describe("_evaluateAuthStatus", async () => {
    // Import the function from the forTesting export
    const { _evaluateAuthStatus } = forTesting;

    const mockSessionUser: SessionUser = {
      userId: "test-user-id",
      tenantId: "test-tenant-id",
      shardNamespace: "test-namespace",
      email: "<EMAIL>",
      roles: [CustomerUiRole[CustomerUiRole.ADMIN]],
      createdAt: Date.now(),
      sessionId: "test-session-id",
      tenantName: "Test Tenant",
    };

    const mergeSessionUserMock = (userPartial: Partial<SessionUser>) => {
      mocks.getSession.mockResolvedValue({
        get: vi.fn(),
        set: vi.fn(),
        data: { user: { ...mockSessionUser, ...userPartial } },
      });
    };

    beforeEach(async () => {
      vi.resetAllMocks();

      // Setup default mocks
      (authenticator as MockAuthenticator).isAuthenticated.mockResolvedValue(
        mockSessionUser,
      );
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.ENTERPRISE,
      });

      mocks.getSession.mockResolvedValue({
        get: vi.fn().mockResolvedValue(mockSessionUser),
        set: vi.fn(),
        data: { user: mockSessionUser },
      });
    });

    it("should return NOT_AUTHENTICATED when user is not authenticated", async () => {
      (authenticator as MockAuthenticator).isAuthenticated.mockResolvedValue(
        null,
      );

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: false },
      );

      expect(result).toBe("NOT_AUTHENTICATED");
      expect(
        (authenticator as MockAuthenticator).isAuthenticated,
      ).toHaveBeenCalled();
    });

    it("should return NO_USER_ID when userId is missing", async () => {
      mergeSessionUserMock({ userId: "" });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: false },
      );

      expect(result).toBe("NO_USER_ID");
    });

    it("should return NO_TENANT_ID when tenantId is missing", async () => {
      mergeSessionUserMock({ tenantId: "" });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: false },
      );

      expect(result).toBe("NO_TENANT_ID");
    });

    it("should return NO_SHARD_NAMESPACE when shardNamespace is missing", async () => {
      mergeSessionUserMock({ shardNamespace: "" });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: false },
      );

      expect(result).toBe("NO_SHARD_NAMESPACE");
    });

    it("should return SESSION_EXPIRED when session has expired", async () => {
      mergeSessionUserMock({
        createdAt: Date.now() - (60 * 60 * 1000 + 1000), // 1 hour + 1 second ago
      });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: false },
      );

      expect(result).toBe("SESSION_EXPIRED");
    });

    it("should return SUCCESS when adminOnly is true and user is not tagged as admin in Roles, but is an individual (non-team) user.", async () => {
      mergeSessionUserMock({
        roles: [CustomerUiRole[CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE]],
      });
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.COMMUNITY,
      });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: true, enterpriseOnly: false },
      );

      expect(result).toBe("SUCCESS");
    });

    it("should return ADMIN_REQUIRED when adminOnly is true and user is in self serve team", async () => {
      mergeSessionUserMock({
        roles: [CustomerUiRole[CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE]],
      });
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.PROFESSIONAL,
        config: {
          configs: {
            is_self_serve_team: "true",
          },
        },
      });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: true, enterpriseOnly: false },
      );

      expect(result).toBe("ADMIN_REQUIRED");
    });

    it("should return ADMIN_REQUIRED when adminOnly is true and user is enterprise tenant", async () => {
      mergeSessionUserMock({
        roles: [CustomerUiRole[CustomerUiRole.UNKNOWN_CUSTOMER_UI_ROLE]],
      });
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.ENTERPRISE,
      });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: true, enterpriseOnly: false },
      );

      expect(result).toBe("ADMIN_REQUIRED");
    });

    it("should return ENTERPRISE_REQUIRED when enterpriseOnly is true and tenant is not enterprise", async () => {
      mocks.tenantWatcherInstance.tenantFor.mockResolvedValue({
        tier: TenantTier.COMMUNITY,
      });

      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: true },
      );

      expect(result).toBe("ENTERPRISE_REQUIRED");
      expect(mocks.tenantWatcherInstance.tenantFor).toHaveBeenCalled();
      expect(mocks.tenantWatcherInstance.tenantFor).toHaveBeenCalledWith(
        mockSessionUser.tenantId,
      );
    });

    it("should return SUCCESS when all checks pass", async () => {
      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: false },
      );

      expect(result).toBe("SUCCESS");
    });

    it("should return SUCCESS when adminOnly is true and user is admin", async () => {
      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: true, enterpriseOnly: false },
      );

      expect(result).toBe("SUCCESS");
    });

    it("should return SUCCESS when enterpriseOnly is true and tenant is enterprise", async () => {
      const result = await _evaluateAuthStatus(
        { headers: { get: () => "cookie" } } as unknown as Request,
        { adminOnly: false, enterpriseOnly: true },
      );

      expect(result).toBe("SUCCESS");
    });
  });
});

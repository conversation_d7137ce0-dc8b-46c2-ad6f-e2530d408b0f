import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { Linear } from "~services/integrations/linear/linear_connect";
import type {
  HydrateLinearSettingsRequest,
  HydrateLinearSettingsResponse,
} from "~services/integrations/linear/linear_pb";

import { Config } from "../config";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

export class LinearProcessorClient {
  constructor(
    private client = createPromiseClient(
      Linear,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string, cloud: string) => {
          // Assume that we are adding this suffix to the app name
          const suffix =
            cloud === Config.CURRENT_CLOUD
              ? // The local service - we use these in the same cloud so that we can
                // create fewer global services, which can run into GCP limits
                // rather easily.
                `-svc.${shardNamespace}`
              : // Otherwise, use the global service suffix
                `.${shardNamespace}.t.${Config.CLOUD_DOMAIN_SUFFIXES[cloud]}`;
          return Config.format(Config.LINEAR_ENDPOINT_TEMPLATE, suffix);
        },
      ),
    ),
  ) {}

  hydrateLinearSettings = (
    request: PartialMessage<HydrateLinearSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateLinearSettingsResponse> => {
    return this.client.hydrateLinearSettings(
      request as HydrateLinearSettingsRequest,
      options,
    );
  };
}

export const linearProcessorClient = new LinearProcessorClient();

import type {
  Message,
  AnyMessage,
  ServiceType,
  MethodInfo,
  PartialMessage,
} from "@bufbuild/protobuf";
import {
  type ContextValues,
  createContextKey,
  type StreamResponse,
  type Transport,
  type UnaryResponse,
} from "@connectrpc/connect";
import { createGrpcTransport } from "@connectrpc/connect-node";
import { logger } from "@augment-internal/logging";
import { Config } from "../config";

import { TenantWatcherCachingClient } from "./tenant-watcher";

/**
 * A caller can set the region or provide the tenant and shard key.
 */
export const TENANT_ID_KEY = createContextKey("", {
  description: "tenant id to execute the command",
});
/**
 * A caller can set the region or provide the tenant and shard key.
 */
export const SHARD_KEY = createContextKey("", {
  description: "shard to execute the command",
});

/**
 * This creates a new transport for each region.  The
 * caller passes the tenantId and
 * the shard into the GRPC request context.
 */
export class TenantShardAwareTransport implements Transport {
  //TODO(justin) - one day this should likely be a LRU Cache.
  endpoints = new Map<string, Transport>();
  constructor(
    private readonly endpointFor: (
      tenantId: string,
      shardNamespace: string,
      cloud: string,
    ) => Promise<string> | string,

    /**
     * factory for creating the transport
     */
    private readonly transportFactory = (endpoint: string) =>
      createGrpcTransport(Config.createGrpcTransportOptions(endpoint)),
    private readonly tenantWatcher: TenantWatcherCachingClient = TenantWatcherCachingClient.getInstance(),
  ) {}

  protected getOrCreateTransport(endpoint: string) {
    let transport = this.endpoints.get(endpoint);
    if (!transport) {
      transport = this.createTransport(endpoint);
      this.endpoints.set(endpoint, transport);
    }
    return transport;
  }
  protected createTransport(endpoint: string) {
    logger.debug(`Creating transport for '${endpoint}'`);
    return this.transportFactory(endpoint);
  }

  protected async transportFromContext(contextValues?: ContextValues) {
    if (!contextValues) {
      logger.error(
        `TENANT_ID and SHARD_KEY must be set in the request context to use this transport.`,
      );
      throw new Error("Transport error: No context values set.");
    }
    const tenantId = contextValues.get(TENANT_ID_KEY);
    const shardNamespace = contextValues.get(SHARD_KEY);
    const tenant = await this.tenantWatcher.tenantFor(tenantId);
    if (!tenant?.cloud) {
      logger.error(`No cloud was found for tenant ${tenantId}`);
      throw new Error(`Transport Error: No cloud was found for tenant.`);
    }
    return this.endpointFor(tenantId, shardNamespace, tenant.cloud);
  }

  async unary<
    I extends Message<I> = AnyMessage,
    O extends Message<O> = AnyMessage,
  >(
    service: ServiceType,
    method: MethodInfo<I, O>,
    signal: AbortSignal | undefined,
    timeoutMs: number | undefined,
    header: HeadersInit | undefined,
    input: PartialMessage<I>,
    contextValues?: ContextValues,
  ): Promise<UnaryResponse<I, O>> {
    return this.getOrCreateTransport(
      await this.transportFromContext(contextValues),
    ).unary(service, method, signal, timeoutMs, header, input, contextValues);
  }
  async stream<
    I extends Message<I> = AnyMessage,
    O extends Message<O> = AnyMessage,
  >(
    service: ServiceType,
    method: MethodInfo<I, O>,
    signal: AbortSignal | undefined,
    timeoutMs: number | undefined,
    header: HeadersInit | undefined,
    input: AsyncIterable<PartialMessage<I>>,
    contextValues?: ContextValues,
  ): Promise<StreamResponse<I, O>> {
    return this.getOrCreateTransport(
      await this.transportFromContext(contextValues),
    ).stream(service, method, signal, timeoutMs, header, input, contextValues);
  }
}

import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";
import { logger } from "@augment-internal/logging";

import { Config } from "../config";
import { CachingClient } from "./caching-client";
import { Share } from "~services/share/share_connect";
import type {
  GetChatConversationRequest,
  GetChatConversationResponse,
} from "~services/share/share_pb";

import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

logger.info(`Creating Share client: ${Config.SHARE_ENDPOINT_TEMPLATE}`);

const DEFAULT_TTL = 360;
const CHECK_PERIOD = 120;

class ShareClient extends CachingClient<
  typeof Share,
  GetChatConversationRequest
> {
  constructor(
    client = createPromiseClient(
      Share,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string) =>
          Config.format(Config.SHARE_ENDPOINT_TEMPLATE, shardNamespace),
      ),
    ),
  ) {
    super(client, DEFAULT_TTL, CHECK_PERIOD);

    this.getChatConversation = this.createCachedMethod(
      "getChatConversation",
      this.client.getChatConversation.bind(this.client),
    );
  }

  getChatConversation: (
    request: PartialMessage<GetChatConversationRequest>,
    options?: CallOptions,
  ) => Promise<GetChatConversationResponse>;

  protected calculateCacheKey(
    methodName: string,
    request: PartialMessage<GetChatConversationRequest>,
  ): string {
    const { uuid, ...rest } = request;
    if (!uuid) {
      throw new Error(`uuid is required for ${methodName}`);
    }
    return `${methodName}|uuid:${uuid}|rest:${CachingClient.objToString(rest)}`;
  }

  private createCachedMethod<
    TRequest extends GetChatConversationRequest,
    TResponse,
  >(
    methodName: string,
    clientMethod: (
      request: TRequest,
      options?: CallOptions,
    ) => Promise<TResponse>,
    ttl: number = DEFAULT_TTL,
  ) {
    return async (
      request: PartialMessage<GetChatConversationRequest>,
      options: CallOptions = {},
    ): Promise<TResponse> => {
      const cacheKey = this.calculateCacheKey(methodName, request);
      return this.cacheAndCall(
        clientMethod as (
          request: GetChatConversationRequest,
          options: CallOptions,
        ) => Promise<TResponse>,
        cacheKey,
        request as TRequest,
        options,
        ttl,
      );
    };
  }
}

export const cachingClient = new ShareClient();

import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { Atlassian } from "~services/integrations/atlassian/atlassian_connect";
import type {
  HydrateAtlassianUserSettingsRequest,
  HydrateAtlassianUserSettingsResponse,
} from "~services/integrations/atlassian/atlassian_pb";

import { Config } from "../config";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

export class AtlassianProcessorClient {
  constructor(
    private client = createPromiseClient(
      Atlassian,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string, cloud: string) => {
          // Assume that we are adding this suffix to the app name
          const suffix =
            cloud === Config.CURRENT_CLOUD
              ? // The local service - we use these in the same cloud so that we can
                // create fewer global services, which can run into GCP limits
                // rather easily.
                `-svc.${shardNamespace}`
              : // Otherwise, use the global service suffix
                `.${shardNamespace}.t.${Config.CLOUD_DOMAIN_SUFFIXES[cloud]}`;
          return Config.format(Config.ATLASSIAN_ENDPOINT_TEMPLATE, suffix);
        },
      ),
    ),
  ) {}

  hydrateAtlassianUserSettings = (
    request: PartialMessage<HydrateAtlassianUserSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateAtlassianUserSettingsResponse> =>
    this.client.hydrateAtlassianUserSettings(
      request as HydrateAtlassianUserSettingsRequest,
      options,
    );
}

export const atlassianProcessorClient = new AtlassianProcessorClient();

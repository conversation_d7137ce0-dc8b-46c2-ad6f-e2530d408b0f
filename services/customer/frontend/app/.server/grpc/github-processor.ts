import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";
import { GithubProcessor } from "~services/integrations/github/processor/processor_connect";
import type {
  HydrateGithubSettingsRequest,
  HydrateGithubSettingsResponse,
  HydrateGithubUserSettingsRequest,
  HydrateGithubUserSettingsResponse,
} from "~services/integrations/github/processor/processor_pb";
import { Config } from "../config";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

export class GithubProcessorClient {
  constructor(
    private client = createPromiseClient(
      GithubProcessor,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string, cloud: string) => {
          // Assume that we are adding this suffix to the app name
          const suffix =
            cloud === Config.CURRENT_CLOUD
              ? // The local service - we use these in the same cloud so that we can
                // create fewer global services, which can run into GCP limits
                // rather easily.
                `-svc.${shardNamespace}`
              : // Otherwise, use the global service suffix
                `.${shardNamespace}.t.${Config.CLOUD_DOMAIN_SUFFIXES[cloud]}`;
          return Config.format(
            Config.GITHUB_PROCESSOR_ENDPOINT_TEMPLATE,
            suffix,
          );
        },
      ),
    ),
  ) {}

  hydrateGithubSettings = (
    request: PartialMessage<HydrateGithubSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateGithubSettingsResponse> => {
    return this.client.hydrateGithubSettings(
      request as HydrateGithubSettingsRequest,
      options,
    );
  };

  hydrateGithubUserSettings = (
    request: PartialMessage<HydrateGithubUserSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateGithubUserSettingsResponse> => {
    return this.client.hydrateGithubUserSettings(
      request as HydrateGithubUserSettingsRequest,
      options,
    );
  };
}

import { createGrpcTransport } from "@connectrpc/connect-node";
import { createPromiseClient } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { Config } from "../config";
import type { Tenant } from "~services/tenant_watcher/tenant_watcher_pb";
import { TenantWatcher } from "~services/tenant_watcher/tenant_watcher_connect";
import { assertUnreachable } from "@augment-internal/ts-utils/type";
import { DeferredPromise } from "@augment-internal/ts-utils/promise";
import { createExponentialBackoff } from "@augment-internal/ts-utils/timer";
import { secondsToMillis } from "@launchdarkly/node-server-sdk";

export class TenantWatcherCachingClient {
  private static instance: TenantWatcherCachingClient | undefined = undefined;

  static getInstance() {
    if (!TenantWatcherCachingClient.instance) {
      TenantWatcherCachingClient.instance = new TenantWatcherCachingClient();
    }
    return TenantWatcherCachingClient.instance;
  }

  private initialized = new DeferredPromise<void>();

  public get isInitialized() {
    return this.initialized.status === "resolved";
  }

  /** Cache of tenants by tenant ID */
  private cache: Record<string, Tenant> = {};

  private readonly backoff = createExponentialBackoff({
    initialDelayMs: 200, // 200 ms
    maxDelayMs: secondsToMillis(30), // 30 seconds
    coolDownMs: secondsToMillis(30), // 30 seconds
    onCoolDown: () => {
      logger.info("No errors within 30 seconds, returning to healthy state");
    },
    factor: 2, // double the delay each time
    maxRetries: Infinity, // keep trying forever
    jitter: true, // add ±50% random jitter
  });

  private readonly client = createPromiseClient(
    TenantWatcher,
    createGrpcTransport(
      Config.createGrpcTransportOptions(Config.TENANT_WATCHER_ENDPOINT),
    ),
  );

  constructor() {
    void this.startStreamLoop();
  }

  public async tenantFor(tenantId: string) {
    logger.debug(`Getting tenant ${tenantId}`);
    await this.initialized;
    return this.cache[tenantId];
  }

  /**
   * Forever loop: (re)open the stream with exponential back‑off.
   * `backoff()` resets its delay automatically after the first message
   * (→ service considered “healthy” again).
   */
  private async startStreamLoop() {
    while (true) {
      try {
        await this.backoff(async () => {
          try {
            await this.listen();
          } catch (error) {
            logger.error("Error listening to tenant watcher", error);
            throw error;
          }
        });
      } catch (error) {
        /*
          backoff() only throws when it runs out of retries
          (maxRetries = Infinity here, so this never triggers),
          but having the catch makes the intent explicit.
        */
        logger.error("Unrecoverable stream failure!", error);
        throw error;
      }
    }
  }

  private async listen() {
    for await (const { tenants, isInitial } of this.client.watchTenants({
      shardNamespace: "",
    })) {
      const cache = isInitial ? {} : this.cache;
      for (const change of tenants) {
        if (!change.type.value?.tenant) continue;
        const tenantChangeType = change.type.case;
        switch (tenantChangeType) {
          case "updated":
            logger.info(`Updated tenant ${change.type.value.tenant.id}`);
            cache[change.type.value.tenant.id] = change.type.value.tenant;
            break;
          case "removed":
            logger.info(`Removed tenant ${change.type.value.tenant.id}`);
            delete cache[change.type.value.tenant.id];
            break;
          case undefined:
            continue;
          default:
            assertUnreachable(tenantChangeType);
        }
      }
      this.cache = cache;
      this.initialized.resolve();
    }
  }
}

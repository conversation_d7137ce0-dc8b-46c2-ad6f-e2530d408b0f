import type {
  AnyMessage,
  MethodInfo,
  PartialMessage,
  ServiceType,
} from "@bufbuild/protobuf";
import {
  TenantShardAwareTransport,
  TENANT_ID_KEY,
  SHARD_KEY,
} from "../tenant-shard-aware-transport";
import { createContextValues } from "@connectrpc/connect";
import {
  expect,
  describe,
  beforeEach,
  test,
  afterEach,
  vi as jest,
} from "vitest";
import type { TenantWatcherCachingClient } from "../tenant-watcher";
jest.mock("../../config");
jest.mock("../tenant-watcher");

describe("TenantShardAwareTransport", () => {
  let transport: TenantShardAwareTransport;
  let mockEndpointFor = jest.fn();
  let mockTransportFactory = jest.fn();
  let mockTenantClient: Pick<TenantWatcherCachingClient, "tenantFor"> = {
    tenantFor: jest.fn().mockResolvedValue({
      cloud: "test-cloud",
      id: "test-tenant",
      name: "test-tenant-name",
      shardNamespace: "test-shard",
    }),
  };

  beforeEach(() => {
    mockEndpointFor = jest.fn();
    mockTransportFactory = jest.fn(() => ({
      unary: jest.fn(),
      stream: jest.fn(),
    }));
    mockTenantClient = {
      tenantFor: jest.fn().mockResolvedValue({
        cloud: "test-cloud",
        id: "test-tenant",
        name: "test-tenant-name",
        shardNamespace: "test-shard",
      }),
    };

    transport = new TenantShardAwareTransport(
      mockEndpointFor,
      mockTransportFactory,
      mockTenantClient,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test("constructor initializes with empty endpoints map", () => {
    expect(transport["endpoints"].size).toBe(0);
  });

  test("getOrCreateTransport creates new transport if not exists", async () => {
    const endpoint = "http://test-endpoint.com";
    const result = transport["getOrCreateTransport"](endpoint);

    expect(mockTransportFactory).toHaveBeenCalledWith(endpoint);
    expect(transport["endpoints"].get(endpoint)).toBe(result);
  });

  test("getOrCreateTransport returns existing transport if exists", async () => {
    const endpoint = "http://test-endpoint.com";
    const existingTransport = { unary: jest.fn(), stream: jest.fn() };
    transport["endpoints"].set(endpoint, existingTransport);

    const result = transport["getOrCreateTransport"](endpoint);

    expect(mockTransportFactory).not.toHaveBeenCalled();
    expect(result).toBe(existingTransport);
  });

  test("transportFromContext throws error if context is undefined", async () => {
    await expect(transport["transportFromContext"]()).rejects.toThrow(
      "Transport error",
    );
  });

  test("transportFromContext calls endpointFor with correct parameters", async () => {
    const tenantId = "test-tenant";
    const shardKey = "test-shard";
    const contextValues = createContextValues()
      .set(TENANT_ID_KEY, tenantId)
      .set(SHARD_KEY, shardKey);

    await transport["transportFromContext"](contextValues);

    expect(mockEndpointFor).toHaveBeenCalledWith(
      tenantId,
      shardKey,
      "test-cloud",
    );
  });

  test("unary method calls underlying transport unary method", async () => {
    const mockUnary = jest.fn();
    mockTransportFactory.mockReturnValue({ unary: mockUnary });
    mockEndpointFor.mockResolvedValue("http://test-endpoint.com");

    const contextValues = createContextValues()
      .set(TENANT_ID_KEY, "test-tenant")
      .set(SHARD_KEY, "test-shard");

    await transport.unary(
      {} as ServiceType,
      {} as MethodInfo,
      undefined,
      undefined,
      undefined,
      {} as PartialMessage<AnyMessage>,
      contextValues,
    );

    expect(mockUnary).toHaveBeenCalled();
  });

  test("stream method calls underlying transport stream method", async () => {
    const mockStream = jest.fn();
    mockTransportFactory.mockReturnValue({ stream: mockStream });
    mockEndpointFor.mockResolvedValue("http://test-endpoint.com");

    const contextValues = createContextValues()
      .set(TENANT_ID_KEY, "test-tenant")
      .set(SHARD_KEY, "test-shard");

    await transport.stream(
      {} as ServiceType,
      {} as MethodInfo,
      undefined,
      undefined,
      undefined,
      undefined as unknown as AsyncIterable<PartialMessage<AnyMessage>>,
      contextValues,
    );

    expect(mockStream).toHaveBeenCalled();
  });
});

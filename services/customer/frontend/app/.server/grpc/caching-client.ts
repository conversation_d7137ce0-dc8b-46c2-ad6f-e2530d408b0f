import NodeCache from "node-cache";
import type { Message, PartialMessage, ServiceType } from "@bufbuild/protobuf";
import type { CallOptions, PromiseClient } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";

export abstract class CachingClient<
  T extends ServiceType,
  R extends Message<R>,
> {
  protected queryCache: NodeCache;
  protected client: PromiseClient<T>;

  protected abstract calculateCacheKey(
    methodName: string,
    request: PartialMessage<R>,
  ): string;

  constructor(client: PromiseClient<T>, stdTTL: number, checkperiod: number) {
    this.queryCache = new NodeCache({ stdTTL, checkperiod });
    this.client = client;
  }

  /* eslint-disable @typescript-eslint/no-explicit-any */
  static objToString(obj: Record<string, any> | any[]): string {
    if (Array.isArray(obj)) {
      return `[${obj.map(CachingClient.objToString).join(", ")}]`;
    } else if (typeof obj === "object") {
      const props = Object.keys(obj).sort();
      return `{${props
        .map((key) => `${key}: ${CachingClient.objToString(obj[key])}`)
        .join(", ")}}`;
    } else {
      return JSON.stringify(obj, (_, v) =>
        typeof v === "bigint" ? v.toString() : v,
      );
    }
  }

  protected async cacheAndCall<TRequest, TResponse>(
    method: (request: TRequest, options: CallOptions) => Promise<TResponse>,
    cacheKey: string,
    request: TRequest,
    options: CallOptions = {},
    ttl: number,
  ): Promise<TResponse> {
    const inCache = this.queryCache.has(cacheKey);

    if (inCache) {
      const result = this.queryCache.get<TResponse>(cacheKey);

      if (result) {
        return result;
      } else {
        this.queryCache.del(cacheKey);
      }
    }

    let result: TResponse;
    try {
      result = await method(request, options);
    } catch (e) {
      logger.error(`Failed to get data from service: ${e}`);
      throw e;
    }

    this.queryCache.set<TResponse>(cacheKey, result, ttl);

    return result;
  }
}

import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { Notion } from "~services/integrations/notion/notion_connect"; //autogen
import type {
  HydrateNotionSettingsRequest,
  HydrateNotionSettingsResponse,
} from "~services/integrations/notion/notion_pb"; //autogen

import { Config } from "../config";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

export class NotionProcessorClient {
  constructor(
    private client = createPromiseClient(
      Notion,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string, cloud: string) => {
          // Assume that we are adding this suffix to the app name
          const suffix =
            cloud === Config.CURRENT_CLOUD
              ? // The local service - we use these in the same cloud so that we can
                // create fewer global services, which can run into GCP limits
                // rather easily.
                `-svc.${shardNamespace}`
              : // Otherwise, use the global service suffix
                `.${shardNamespace}.t.${Config.CLOUD_DOMAIN_SUFFIXES[cloud]}`;
          return Config.format(Config.NOTION_ENDPOINT_TEMPLATE, suffix);
        },
      ),
    ),
  ) {}

  hydrateNotionSettings = (
    request: PartialMessage<HydrateNotionSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateNotionSettingsResponse> =>
    this.client.hydrateNotionSettings(
      request as HydrateNotionSettingsRequest,
      options,
    );
}

export const notionProcessorClient = new NotionProcessorClient();

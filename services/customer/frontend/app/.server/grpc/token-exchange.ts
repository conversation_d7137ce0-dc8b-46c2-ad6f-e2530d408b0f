import { createGrpc<PERSON>ransport } from "@connectrpc/connect-node";
import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";
import { logger } from "@augment-internal/logging";

import { Config } from "../config";
import { CachingClient } from "./caching-client";
import { TokenExchange } from "~services/token_exchange/token_exchange_connect";
import type {
  GetSignedTokenForIAPTokenRequest,
  GetSignedTokenForServiceRequest,
  GetSignedTokenForUserRequest,
  GetSignedTokenForUserResponse,
  GetVerificationKeyRequest,
  GetVerificationKeyResponse,
} from "~services/token_exchange/token_exchange_pb";

type TokenExchangeRequest =
  | GetSignedTokenForUserRequest
  | GetSignedTokenForServiceRequest
  | GetSignedTokenForIAPTokenRequest;

// set the ttl to be below the 60 second token expiration
const DEFAULT_TTL = 55;
// time in seconds to check all data and delete expired keys
const CHECK_PERIOD = 5;

class TokenExchangeCachingClient extends CachingClient<
  typeof TokenExchange,
  TokenExchangeRequest
> {
  constructor(
    client = createPromiseClient(
      TokenExchange,
      createGrpcTransport(
        Config.createGrpcTransportOptions(Config.TOKEN_EXCHANGE_ENDPOINT),
      ),
    ),
  ) {
    super(client, DEFAULT_TTL, CHECK_PERIOD);

    this.getSignedTokenForUser = this.createCachedMethod(
      "getSignedTokenForUser",
      this.client.getSignedTokenForUser.bind(this.client),
    );

    this.getSignedTokenForService = this.createCachedMethod(
      "getSignedTokenForService",
      this.client.getSignedTokenForService.bind(this.client),
    );

    this.getSignedTokenForIAPToken = this.createCachedMethod(
      "getSignedTokenForIAPToken",
      this.client.getSignedTokenForIAPToken.bind(this.client),
    );
  }
  getVerificationKey(
    request: GetVerificationKeyRequest,
    options?: CallOptions,
  ): Promise<GetVerificationKeyResponse> {
    return this.client.getVerificationKey(request, options);
  }
  getSignedTokenForUser: (
    request: PartialMessage<GetSignedTokenForUserRequest>,
    options?: CallOptions,
  ) => Promise<GetSignedTokenForUserResponse>;

  getSignedTokenForService: (
    request: PartialMessage<GetSignedTokenForServiceRequest>,
    options?: CallOptions,
  ) => Promise<GetSignedTokenForUserResponse>;

  getSignedTokenForIAPToken: (
    request: PartialMessage<GetSignedTokenForIAPTokenRequest>,
    options?: CallOptions,
  ) => Promise<GetSignedTokenForUserResponse>;

  protected calculateCacheKey(
    methodName: string,
    request: PartialMessage<TokenExchangeRequest>,
  ) {
    const { tenantId, shardNamespace, ...rest } = request;
    if (!tenantId) {
      logger.warn("No tenantId provided for token exchange request");
      throw new Error(`tenantId is required for ${methodName}`);
    }
    if (!shardNamespace) {
      logger.warn("No shardNamespace is defined");
      throw new Error(`shardNamespace is required for ${methodName}`);
    }
    return `${methodName}|tenantId:${tenantId}|shardNamespace:${shardNamespace}|rest:${CachingClient.objToString(
      rest,
    )}`;
  }

  public clearTokenCache(
    methodName: string,
    request: PartialMessage<TokenExchangeRequest>,
  ) {
    const cacheKey = this.calculateCacheKey(methodName, request);
    this.queryCache.del(cacheKey);
  }

  private createCachedMethod<
    TRequest extends PartialMessage<TokenExchangeRequest>,
    TResponse,
  >(
    methodName: string,
    clientMethod: (
      request: TRequest,
      options?: CallOptions,
    ) => Promise<TResponse>,
    ttl: number = DEFAULT_TTL,
  ) {
    return async (
      request: PartialMessage<TokenExchangeRequest>,
      options: CallOptions = {},
    ): Promise<TResponse> => {
      const cacheKey = this.calculateCacheKey(methodName, request);
      return this.cacheAndCall<TRequest, TResponse>(
        clientMethod as (
          request: TRequest,
          options: CallOptions,
        ) => Promise<TResponse>,
        cacheKey,
        request as TRequest,
        options,
        ttl,
      );
    };
  }
}

let cachingClient: TokenExchangeCachingClient | undefined;

/**
 * lazy initializes the token exchange client. This is to avoid creating a new client for each request.
 * It also makes testing easier by not initializing on module load.
 * @returns TokenExchangeCachingClient
 */
export function getTokenExchangeClient() {
  if (!cachingClient) {
    cachingClient = new TokenExchangeCachingClient();
    logger.info(
      `Created token exchange client: ${Config.TOKEN_EXCHANGE_ENDPOINT}`,
    );
  }
  return cachingClient;
}

/**
 * Resets the token exchange client instance.
 * Used for testing to ensure a clean client state.
 */
export function resetTokenExchangeClient() {
  cachingClient = undefined;
  logger.info("Token exchange client has been reset");
}

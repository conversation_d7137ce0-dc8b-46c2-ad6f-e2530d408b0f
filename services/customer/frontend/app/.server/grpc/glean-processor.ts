import { type CallOptions, createPromiseClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";

import { Glean } from "~services/integrations/glean/glean_connect";
import type {
  GetGleanTenantSettingsRequest,
  GetGleanTenantSettingsResponse,
  HydrateGleanTenantSettingsRequest,
  HydrateGleanTenantSettingsResponse,
  HydrateGleanUserSettingsRequest,
  HydrateGleanUserSettingsResponse,
  GenerateNewKeyPairRequest,
  GenerateNewKeyPairResponse,
} from "~services/integrations/glean/glean_pb";

import { Config } from "../config";
import { TenantShardAwareTransport } from "./tenant-shard-aware-transport";

export class GleanProcessorClient {
  constructor(
    private client = createPromiseClient(
      Glean,
      new TenantShardAwareTransport(
        (_tenantId: string, shardNamespace: string, cloud: string) => {
          // Assume that we are adding this suffix to the app name
          const suffix =
            cloud === Config.CURRENT_CLOUD
              ? // The local service - we use these in the same cloud so that we can
                // create fewer global services, which can run into GCP limits
                // rather easily.
                `-svc.${shardNamespace}`
              : // Otherwise, use the global service suffix
                `.${shardNamespace}.t.${Config.CLOUD_DOMAIN_SUFFIXES[cloud]}`;
          return Config.format(Config.GLEAN_ENDPOINT_TEMPLATE, suffix);
        },
      ),
    ),
  ) {}

  hydrateGleanUserSettings = (
    request: PartialMessage<HydrateGleanUserSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateGleanUserSettingsResponse> =>
    this.client.hydrateGleanUserSettings(
      request as HydrateGleanUserSettingsRequest,
      options,
    );

  hydrateGleanTenantSettings = (
    request: PartialMessage<HydrateGleanTenantSettingsRequest>,
    options?: CallOptions,
  ): Promise<HydrateGleanTenantSettingsResponse> =>
    this.client.hydrateGleanTenantSettings(
      request as HydrateGleanTenantSettingsRequest,
      options,
    );

  getGleanTenantSettings = (
    request: PartialMessage<GetGleanTenantSettingsRequest>,
    options?: CallOptions,
  ): Promise<GetGleanTenantSettingsResponse> =>
    this.client.getGleanTenantSettings(
      request as GetGleanTenantSettingsRequest,
      options,
    );

  generateNewKeyPair = (
    request: PartialMessage<GenerateNewKeyPairRequest>,
    options?: CallOptions,
  ): Promise<GenerateNewKeyPairResponse> =>
    this.client.generateNewKeyPair(
      request as GenerateNewKeyPairRequest,
      options,
    );
}

export const gleanProcessorClient = new GleanProcessorClient();

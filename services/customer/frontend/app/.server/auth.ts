import {
  type defer,
  json,
  type LoaderFunctionArgs,
  redirect,
} from "@remix-run/node";
import { logger } from "@augment-internal/logging";
import { Authenticator, type StrategyVerifyCallback } from "remix-auth";
import {
  type OAuth2Profile,
  OAuth2Strategy,
  type OAuth2StrategyOptions,
  type OAuth2StrategyVerifyParams,
} from "remix-auth-oauth2";

import jwt from "jsonwebtoken";
import type { JwtPayload } from "jsonwebtoken";

import { Config } from "./config";
import { getSession, sessionStorage } from "./session";
import { TenantWatcherCachingClient } from "../.server/grpc/tenant-watcher";

import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import { TenantTier } from "~services/tenant_watcher/tenant_watcher_pb";
import { hoursToMilliseconds } from "date-fns";
import { assertUnreachable } from "@augment-internal/ts-utils/type";
import { isUserInSelfServeTeam } from "app/utils/team.server";

const SESSION_EXPIRATION = hoursToMilliseconds(1); // 1 hour in milliseconds

export type SessionUser = {
  userId: string;
  tenantId: string;
  tenantName: string;
  shardNamespace: string;
  email: string;
  roles: string[];
  createdAt: number;
  sessionId: string;
};

type Payload = JwtPayload & {
  tenant_id: string;
  tenant_name: string;
  shard_namespace: string;
  additional_claims: {
    email: string;
    roles: string[];
  };
};

const tokenEndpoint = Config.AUTH_ENDPOINT
  ? new URL("/token", Config.AUTH_ENDPOINT).toString()
  : "";

const baseConfig: OAuth2StrategyOptions = {
  clientId: Config.AUTH_CLIENT_ID,
  clientSecret: Config.AUTH_CLIENT_SECRET,
  redirectURI: Config.AUTH_CALLBACK_URL,
  authorizationEndpoint: Config.AUTH_ENDPOINT,
  tokenEndpoint: tokenEndpoint,
  codeChallengeMethod: "S256",
};

const verifyCallback: StrategyVerifyCallback<
  SessionUser,
  OAuth2StrategyVerifyParams<OAuth2Profile, Record<string, never>>
> = async ({ tokens }) => {
  const accessToken = tokens.access_token;
  const decodedAccessToken = jwt.decode(accessToken, { complete: true });

  try {
    const payload = decodedAccessToken?.payload as Payload;
    return {
      userId: payload.user_id,
      tenantId: payload.tenant_id,
      tenantName: payload.tenant_name,
      shardNamespace: payload.shard_namespace,
      email: payload.additional_claims.email,
      roles: payload.additional_claims.roles,
      createdAt: Date.now(), // capture the time in UTC
      sessionId: crypto.randomUUID(),
    };
  } catch (error) {
    logger.error("Token verification failed:", error);
    throw new Error("Invalid token");
  }
};

export const authenticator = new Authenticator<SessionUser>(sessionStorage);

authenticator.use(
  new OAuth2Strategy<SessionUser, OAuth2Profile>(baseConfig, verifyCallback),
  "auth-central",
);

// Pulled out of node_modules/.pnpm/@remix-run+router@1.16.1/node_modules/@remix-run/router/dist/utils.d.ts
// because it is not exported
type DataFunctionValue =
  | Awaited<ReturnType<typeof json>>
  | Awaited<ReturnType<typeof defer>>
  | Response
  | null;

export const redirectTo = (
  url: URL | string | undefined,
  entry = "/dashboard/overview",
) => {
  if (!url) {
    return entry;
  }
  if (typeof url === "string") {
    url = new URL(
      `${/^\w+?:\/\//.test(url) ? url : `https://hostname${url.startsWith("/") ? url : `/${url}`}`}`,
    );
  }
  if (url.pathname.startsWith("/api/") || url.pathname.endsWith("/logout")) {
    return entry + url.search;
  }
  return url.pathname + url.search;
};

const AUTH_RESULT = {
  NOT_AUTHENTICATED: "NOT_AUTHENTICATED",
  NO_USER_ID: "NO_USER_ID",
  NO_TENANT_ID: "NO_TENANT_ID",
  NO_SHARD_NAMESPACE: "NO_SHARD_NAMESPACE",
  SESSION_EXPIRED: "SESSION_EXPIRED",
  ADMIN_REQUIRED: "ADMIN_REQUIRED",
  ENTERPRISE_REQUIRED: "ENTERPRISE_REQUIRED",
  SUCCESS: "SUCCESS",
} as const;

type AuthResult = (typeof AUTH_RESULT)[keyof typeof AUTH_RESULT];

async function _evaluateAuthStatus(
  request: Request,
  options: {
    adminOnly: boolean;
    enterpriseOnly: boolean;
  },
): Promise<AuthResult> {
  const { adminOnly, enterpriseOnly } = options;
  const isAuthenticated = await authenticator.isAuthenticated(request);

  const session = await getSession(request.headers.get("cookie"));

  if (!isAuthenticated) {
    return AUTH_RESULT.NOT_AUTHENTICATED;
  }

  const user: SessionUser = session.data.user;
  if (!user.userId) {
    return AUTH_RESULT.NO_USER_ID;
  }
  if (!user.tenantId) {
    return AUTH_RESULT.NO_TENANT_ID;
  }
  if (!user.shardNamespace) {
    return AUTH_RESULT.NO_SHARD_NAMESPACE;
  }

  if (Date.now() - user.createdAt > SESSION_EXPIRATION) {
    return AUTH_RESULT.SESSION_EXPIRED;
  }

  // Check if user is an admin when adminOnly is true
  if (adminOnly && !(await isAdmin(user))) {
    return AUTH_RESULT.ADMIN_REQUIRED;
  }

  // Check if user is on enterprise tier when enterprise tier is required
  if (enterpriseOnly) {
    const isEnterprise = await isEnterpriseTier(user);
    if (!isEnterprise) {
      return AUTH_RESULT.ENTERPRISE_REQUIRED;
    }
  }

  return AUTH_RESULT.SUCCESS;
}

export const forTesting = {
  _evaluateAuthStatus,
} as const;

export function withAuthApi<T extends DataFunctionValue>(
  loader: (args: LoaderFunctionArgs & { user: SessionUser }) => Promise<T> | T,
  options: {
    adminOnly?: boolean;
    enterpriseOnly?: boolean;
  } = {},
) {
  // no explicit return type -- let TS infer it
  const { adminOnly = false, enterpriseOnly = false } = options;
  return async (args: LoaderFunctionArgs) => {
    const { request } = args;
    const session = await getSession(request.headers.get("cookie"));

    const Unauthorized401 = {
      status: 401,
      statusText: "Unauthorized",
    };
    const Forbidden403 = {
      status: 403,
      statusText: "Forbidden",
    };

    const authResult = await _evaluateAuthStatus(request, {
      adminOnly,
      enterpriseOnly,
    });
    switch (authResult) {
      case AUTH_RESULT.NOT_AUTHENTICATED: {
        return json({ message: "You are not authenticated" }, Unauthorized401);
      }
      case AUTH_RESULT.NO_USER_ID:
      case AUTH_RESULT.NO_TENANT_ID:
      case AUTH_RESULT.NO_SHARD_NAMESPACE: {
        return json({ message: "Your session is invalid" }, Unauthorized401);
      }
      case AUTH_RESULT.SESSION_EXPIRED: {
        return json({ message: "Your session has expired" }, Unauthorized401);
      }
      case AUTH_RESULT.ADMIN_REQUIRED: {
        return json(
          { message: "You are not authorized to access this page" },
          Forbidden403,
        );
      }
      case AUTH_RESULT.ENTERPRISE_REQUIRED: {
        return json(
          {
            message: "This feature is only available for enterprise tier users",
          },
          Forbidden403,
        );
      }
      case AUTH_RESULT.SUCCESS: {
        const user: SessionUser = session.data.user;
        return loader({
          user,
          ...args,
        });
      }
    }
    return assertUnreachable(authResult);
  };
}

/**
 *
 * @param loader the loader function to wrap
 * @param options configuration options
 * @param options.entry the entry point to redirect to if the user is not authenticated
 * @param options.adminOnly a flag that specifies whether the user is required to be a tenant admin. A tenant admin is a user that has the 'admin' role. Admins have access to view the dashboards. For discovery or vangard, no one is an admin. Default true.
 * @param options.enterpriseOnly a flag that specifies whether enterprise tier is required. Dashboard routes are only accessible to enterprise tier users. Default false.
 * @returns
 */
export function withAuth<T extends DataFunctionValue>(
  loader: (args: LoaderFunctionArgs & { user: SessionUser }) => Promise<T> | T,
  options: {
    adminOnly?: boolean;
    entry?: string;
    enterpriseOnly?: boolean;
  } = {},
) {
  // no explicit return type -- let TS infer it
  const {
    adminOnly = false,
    entry = "/dashboard/overview",
    enterpriseOnly = false,
  } = options;

  return async (args: LoaderFunctionArgs) => {
    const { request } = args;
    const url = new URL(request.url);
    if (url.pathname.startsWith("/api/")) {
      logger.warn(
        "withAuth should (probably) not be used for API routes. Use withAuthApi instead.",
      );
    }

    const session = await getSession(request.headers.get("cookie"));

    const authResult = await _evaluateAuthStatus(request, {
      adminOnly,
      enterpriseOnly,
    });

    switch (authResult) {
      case AUTH_RESULT.NOT_AUTHENTICATED: {
        // let's store where they are right now (including query params,
        // which are necessary for slack, github, etc. authorization flow).
        // we store this in the session cookie so it's available when the
        // user gets redirected back from the oauth2 provider.
        session.set("redirectTo", redirectTo(url, entry));
        return redirect("/login", {
          headers: {
            "Set-Cookie": await sessionStorage.commitSession(session),
          },
        });
      }
      case AUTH_RESULT.NO_USER_ID:
      case AUTH_RESULT.NO_TENANT_ID:
      case AUTH_RESULT.NO_SHARD_NAMESPACE: {
        const user: SessionUser = session.data.user;
        session.set("redirectTo", redirectTo(url, entry));
        logger.info(
          `redirecting user to logout due to (${
            !user.userId ? " no userId " : ""
          }${!user.tenantId ? " no tenantId " : ""}${
            !user.shardNamespace ? " no shardNamespace " : ""
          }), redirecting.`,
        );
        // If we're missing user information, a trip through auth central
        // should return it. If it doesn't, we'll end up in a loop. Detecting
        // such loops and recovering is future work.
        throw redirect("/login", {
          headers: {
            "Set-Cookie": await sessionStorage.commitSession(session),
          },
        });
      }
      case AUTH_RESULT.SESSION_EXPIRED: {
        const user: SessionUser = session.data.user;
        logger.info(
          `Session expired for user ${user.userId}. Logging them out.`,
        );
        session.set("redirectTo", redirectTo(url, entry));
        throw redirect("/login", {
          headers: {
            "Set-Cookie": await sessionStorage.commitSession(session),
          },
        });
      }
      case AUTH_RESULT.ADMIN_REQUIRED: {
        const user: SessionUser = session.data.user;
        logger.warn(
          `User ${user.userId} does not have 'admin' role and is trying to access admin page`,
        );
        throw new Response("You are not authorized to access this page", {
          status: 403,
        });
      }
      case AUTH_RESULT.ENTERPRISE_REQUIRED: {
        const user: SessionUser = session.data.user;
        logger.warn(
          `User ${user.userId} is not on enterprise tier and is trying to access a route that requires enterprise tier`,
        );
        throw new Response(
          "This feature is only available for enterprise tier users",
          {
            status: 403,
          },
        );
      }
      case AUTH_RESULT.SUCCESS: {
        const user: SessionUser = session.data.user;
        return loader({
          user,
          ...args,
        });
      }
    }
    return assertUnreachable(authResult);
  };
}

export async function isAdmin(user: SessionUser): Promise<boolean> {
  // this should not be expensive because the tenant data should get cached by the TenantWatcherCachingClient
  if ((await isEnterpriseTier(user)) || (await isUserInSelfServeTeam(user))) {
    return user.roles.includes(CustomerUiRole[CustomerUiRole.ADMIN as number]);
  } else {
    return true; // users not in teams are their own admin.
  }
}

export async function getTenantTier(user: SessionUser): Promise<TenantTier> {
  try {
    const tenantWatcher = TenantWatcherCachingClient.getInstance();
    const tenant = await tenantWatcher.tenantFor(user.tenantId);

    if (!tenant || !tenant.tier) {
      logger.error(`Could not determine tier for tenant ${user.tenantId}`);
      throw new Error(
        "Could not determine tier for tenant, tenant or tier was falsy",
      );
    }

    return tenant.tier;
  } catch (error) {
    logger.error(
      `Error checking if user ${user.userId} is on enterprise tier:`,
      error,
    );
    throw new Error("Could not determine tier for tenant");
  }
}

/**
 * Check if a user is on an enterprise tier by checking their tenant's tier
 * @param user The session user to check
 * @returns A promise that resolves to true if the user is on an enterprise tier, false otherwise
 */
export async function isEnterpriseTier(user: SessionUser): Promise<boolean> {
  return getTenantTier(user).then((tier) => tier === TenantTier.ENTERPRISE);
}

/**
 * Check if a user is on a community tier by checking their tenant's tier
 * @param user The session user to check
 * @returns A promise that resolves to true if the user is on an community tier, false otherwise
 */
export async function isCommunityTier(user: SessionUser): Promise<boolean> {
  return getTenantTier(user).then((tier) => tier === TenantTier.COMMUNITY);
}

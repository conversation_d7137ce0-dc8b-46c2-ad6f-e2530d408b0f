import {
  type GetUserOrbSubscriptionInfoResponse,
  OrbSubscriptionInfo_SubscriptionStatus,
} from "~services/auth/central/server/auth_pb";
import { User_SubscriptionCreationInfo_Status } from "~services/auth/central/server/auth_entities_pb";

import { isUserInSelfServeTeam } from "../utils/team.server";
import { AuthCentralClient } from "./grpc/auth-central";
import type { SessionUser } from "./auth";

export type UserPendingStatus =
  | "planChangePending"
  | "subscriptionCreationPending"
  | "noSubscription"
  | null;

/**
 * @returns true if the subscription is being created or if a plan change is pending
 */
export async function getUserPendingStatus(
  user: SessionUser,
  subscription:
    | GetUserOrbSubscriptionInfoResponse["orbSubscriptionInfo"]
    | undefined,
): Promise<UserPendingStatus> {
  const authCentralClient = AuthCentralClient.getInstance();
  const userResponse = await authCentralClient.getUser(user);

  if (!userResponse.user) throw new Error("Failed to get user information.");

  if (await isUserInSelfServeTeam({ tenantId: userResponse.user.tenants[0] })) {
    const tenantStatus =
      await AuthCentralClient.getInstance().getTenantPlanStatus(user);
    if (tenantStatus.isPending) {
      return "planChangePending";
    }

    // we do this check here because we make a best effort to determine the status
    // while things may be in an inconsistent state (rather than checking for subscription at the top)
    if (!subscription) return "noSubscription";

    if (subscription.case === "pendingSubscription") {
      // the team subscription is being created. This is a very unlikely case.
      return "subscriptionCreationPending";
    }
  } else {
    // This data on the user object is not meanignful if we are in a self serve team.
    if (userResponse.user?.tierChange?.id) {
      return "planChangePending";
    }

    if (
      userResponse.user.subscriptionCreationInfo?.status ===
      User_SubscriptionCreationInfo_Status.PENDING
    ) {
      return "subscriptionCreationPending";
    }

    if (!subscription) return "noSubscription";

    if (subscription.case === "pendingSubscription") {
      return "subscriptionCreationPending";
    }
  }

  if (!subscription) return "noSubscription";

  if (
    subscription.case === "nonexistentSubscription" ||
    (subscription.case === "subscription" &&
      subscription.value.subscriptionStatus ===
        OrbSubscriptionInfo_SubscriptionStatus.ENDED)
  ) {
    return "noSubscription";
  }

  return null;
}

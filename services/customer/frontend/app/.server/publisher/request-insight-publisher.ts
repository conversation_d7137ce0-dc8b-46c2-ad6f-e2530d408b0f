import { logger } from "@augment-internal/logging";
import { type PartialMessage, Timestamp } from "@bufbuild/protobuf";
import { GoogleAuth } from "google-auth-library";
import {
  type CustomerUISessionEvent_SessionEnd_Reason,
  type RequestEvent,
  RecordSessionEventsRequest,
  RequestInsightMessage,
  SessionEvent,
  type TenantInfo,
  UpdateRequestInfoRequest,
} from "~services/request_insight/request_insight_pb";
import { Config } from "../config";
import { registerCounter } from "../metrics";

// Constants
const MAX_RETRIES = 10;
const MAX_TIMEOUT_MS = 10000; // Maximum wait time between retry attempts
const BACKOFF_BASE_MS = 100;

/**
 * Counter for tracking the number of publishes to the pubsub topic.
 * Labels:
 * - message_type: Type of message being published (request-insight, session-events)
 * - status: Success or failure status of the publish attempt
 */
const requestInsightPubsubPublishCount = registerCounter({
  name: "au_request_insight_pubsub_publish_count_total",
  help: "Number of publishes to the pubsub topic",
  labelNames: ["message_type", "status"],
});

/**
 * RequestInsightPublisher class for publishing messages to Google Cloud Pub/Sub.
 *
 * See services/request_insight/publisher/request_insight_publisher.* for related
 * implementations in other languages.
 * Note that we maintain this TypeScript implementation here because customer-ui is
 * currently the only TypeScript service that needs to publish to the Pub/Sub topic.
 * Adding the publisher directly here enables quicker iteration.
 */
export class RequestInsightPublisher {
  private auth: GoogleAuth;
  private projectId: string;
  private topicName: string;

  constructor(
    projectId: string = Config.REQUEST_INSIGHT_PROJECT_ID,
    topicName: string = Config.REQUEST_INSIGHT_TOPIC_NAME,
  ) {
    this.projectId = projectId;
    this.topicName = topicName;

    this.auth = new GoogleAuth({
      scopes: ["https://www.googleapis.com/auth/pubsub"],
      projectId: this.projectId,
    });

    logger.info(
      `RequestInsight messages will be sent to projects/${this.projectId}/topics/${this.topicName}`,
    );
  }

  /**
   * Retrieves a Google Cloud access token for API authentication.
   */
  private async getAccessToken(): Promise<string> {
    try {
      const token = await this.auth.getAccessToken();
      if (!token) {
        throw new Error("Failed to get access token: token is null");
      }

      return token;
    } catch (error) {
      logger.error("Failed to get access token:", error);
      throw error;
    }
  }

  /**
   * Creates a request event with boilerplate fields (timestamp and UUID) filled in.
   *
   * @returns A partial RequestEvent with common fields initialized
   * @example
   * const event = new RequestEvent({
   *   ...RequestInsightPublisher.newRequestEvent(),
   *   event: {
   *     case: "completionResolution",
   *     value: { acceptedIdx: 1 }
   *   }
   * });
   */
  static newRequestEvent(): PartialMessage<RequestEvent> {
    return {
      time: Timestamp.fromDate(new Date()),
      eventId: crypto.randomUUID(),
    };
  }

  /**
   * Creates a session event with boilerplate fields (timestamp and UUID) filled in.
   *
   * @returns A partial SessionEvent with common fields initialized
   * @example
   * const event = new SessionEvent({
   *   ...RequestInsightPublisher.newSessionEvent(),
   *   event: {
   *     case: "onboardingSessionEvent",
   *     value: { eventName: "signed-in", userAgent: "Chrome" }
   *   }
   * });
   */
  static newSessionEvent(): PartialMessage<SessionEvent> {
    return {
      time: Timestamp.fromDate(new Date()),
      eventId: crypto.randomUUID(),
    };
  }

  /**
   * Core message publishing logic with retry mechanism.
   * Publishes messages using the Pub/Sub REST API with retries on failure.
   */
  private async publishMessage(
    message: RequestInsightMessage,
    messageType: string,
  ): Promise<void> {
    const data = Buffer.from(message.toBinary());
    let attempt = 0;

    while (attempt <= MAX_RETRIES) {
      try {
        const messageId = await this.publishMessageWithAPI(data, messageType);
        logger.info(
          `Message published successfully with API (ID: ${messageId})`,
        );
        requestInsightPubsubPublishCount.labels(messageType, "success").inc();
        return;
      } catch (error) {
        attempt++;

        // Check if we've exceeded maximum retries
        if (attempt > MAX_RETRIES) {
          logger.error(
            `Failed to publish ${messageType} message via API after ${MAX_RETRIES} attempts.`,
            error,
          );
          requestInsightPubsubPublishCount
            .labels(messageType, "exception")
            .inc();
          throw error instanceof Error ? error : new Error(String(error));
        }

        // Retry with exponential backoff
        logger.warn(
          `Failed to publish ${messageType} message via API, retrying (${attempt}/${MAX_RETRIES})...`,
          error,
        );
        const backoffTime = Math.min(
          Math.pow(2, attempt) * BACKOFF_BASE_MS,
          MAX_TIMEOUT_MS,
        );
        await new Promise((resolve) => setTimeout(resolve, backoffTime));
      }
    }
  }

  /**
   * Publishes a message using the Pub/Sub REST API.
   *
   * Note: We use the REST API instead of the Google Pub/Sub client library
   * because the client library was experiencing unstable timeout issues with
   * errors like "Total timeout of API google.pubsub.v1.Publisher exceeded
   * 60000 milliseconds". This direct REST implementation provides more
   * reliable publishing with custom retry logic and better error handling.
   */
  private async publishMessageWithAPI(
    data: Buffer,
    messageType: string,
  ): Promise<string> {
    const token = await this.getAccessToken();
    const base64Data = data.toString("base64");

    const url = `https://pubsub.googleapis.com/v1/projects/${this.projectId}/topics/${this.topicName}:publish`;

    logger.debug("Publishing message to PubSub API", {
      url,
      messageType,
      dataLength: data.length,
    });

    const response = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        messages: [
          {
            data: base64Data,
          },
        ],
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("PubSub API request failed", {
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });
      throw new Error(
        `PubSub API request failed: ${response.status} ${response.statusText}\n${errorText}`,
      );
    }

    const result = await response.json();
    const messageId = result.messageIds?.[0];

    return messageId;
  }

  /**
   * Publishes a request event to the Pub/Sub topic.
   */
  async publishRequestEvent(
    requestId: string,
    tenantInfo: TenantInfo,
    event: RequestEvent,
  ): Promise<void> {
    logger.info(`Publishing request event for request ${requestId}`);

    const request = new UpdateRequestInfoRequest({
      requestId,
      tenantInfo,
      events: [event],
    });

    const pubsubMessage = new RequestInsightMessage({
      message: {
        case: "updateRequestInfoRequest",
        value: request,
      },
    });

    await this.publishMessage(pubsubMessage, "request-insight");
  }

  /**
   * Publishes a session event to the Pub/Sub topic.
   */
  async publishSessionEvent(
    sessionId: string,
    tenantInfo: TenantInfo,
    event: SessionEvent,
  ): Promise<void> {
    logger.info(`Publishing session event for session ${sessionId}`);

    const request = new RecordSessionEventsRequest({
      sessionId,
      tenantInfo,
      events: [event],
    });

    const pubsubMessage = new RequestInsightMessage({
      message: {
        case: "recordSessionEventsRequest",
        value: request,
      },
    });

    await this.publishMessage(pubsubMessage, "session-events");
  }

  /**
   * Publishes a session start event with the provided parameters.
   */
  async publishStartSessionEvent(
    sessionId: string,
    tenantInfo: TenantInfo,
    userId: string,
    redirectUrl: string,
  ): Promise<void> {
    const event = new SessionEvent({
      ...RequestInsightPublisher.newSessionEvent(),
      event: {
        case: "customerUiSessionEvent",
        value: {
          userId: userId,
          event: {
            case: "sessionStart",
            value: {
              redirectUrl: redirectUrl,
            },
          },
        },
      },
    });

    await this.publishSessionEvent(sessionId, tenantInfo, event);
  }

  /**
   * Publishes a session end event with the provided parameters.
   */
  async publishEndSessionEvent(
    sessionId: string,
    tenantInfo: TenantInfo,
    userId: string,
    reason: CustomerUISessionEvent_SessionEnd_Reason,
  ): Promise<void> {
    const event = new SessionEvent({
      ...RequestInsightPublisher.newSessionEvent(),
      event: {
        case: "customerUiSessionEvent",
        value: {
          userId: userId,
          event: {
            case: "sessionEnd",
            value: {
              reason: reason,
            },
          },
        },
      },
    });

    await this.publishSessionEvent(sessionId, tenantInfo, event);
  }
}

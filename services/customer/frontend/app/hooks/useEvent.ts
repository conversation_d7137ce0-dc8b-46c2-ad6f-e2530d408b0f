import type { AnyFunction } from "@augment-internal/ts-utils/type";
import { useRef } from "react";
import { useCallbackOne as useStableCallback } from "use-memo-one";

/** Creates a stable reference to an event handler but keeps the latest function reference internally.
 *
 * @param fn The function to be used in the useEffect
 * @param deps The dependencies that trigger the useEffect
 * @example
 * const location = useLocation();
 * const [count, setCount] = useState();
 * const event = useEvent(() => {
 *   // fires when `count` changes but not when location changes
 *   // `location` is the current location when the count changed
 *   console.info(count, location);
 * });
 *
 * useEffect(() => event(count), [count, event]);
 */
export function useEvent<T extends AnyFunction>(fn: T): T {
  const handlerRef = useRef(fn);
  handlerRef.current = fn;
  return useStableCallback(
    (...args: Parameters<T>) => handlerRef.current(...args),
    [],
  ) as T;
}

import { useAtomValue } from "jotai";
import { atomEffect } from "jotai-effect";
import { useRef } from "react";
import { useMemoOne as useStableMemo } from "use-memo-one";

type AtomEffect = Parameters<typeof atomEffect>[0];

/**
 * Gets dependency values from the jotai graph and calls the effect.
 * @param effect This function does not need to be memoized.
 * @example
 * const [count, setCount] = useAtom(countAtom);
 * useAtomEffect((get, set) => {
 *   // fires when `count` changes
 *   console.info(count, get(countAtom));
 * });
 */
export function useAtomEffect(effect: AtomEffect) {
  const ref = useRef(effect);
  ref.current = effect;
  useAtomValue(useStableMemo(() => atomEffect(ref.current), []));
}

import { toConstObject } from "app/utils/object";
import { z } from "zod";
import { Subscription_OrbStatus } from "~services/auth/central/server/auth_entities_pb";

export const TeamMemberSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string().optional(),
  role: z.enum(["ADMIN", "MEMBER"]),
  joinedAt: z.string().datetime(),
});
export type TeamMemberSchema = z.infer<typeof TeamMemberSchema>;

export const TeamInvitationSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  invitedAt: z.string().datetime(),
});
export type TeamInvitationSchema = z.infer<typeof TeamInvitationSchema>;

export const TeamSchema = z.object({
  id: z.string(),
  users: z.array(TeamMemberSchema),
  seats: z.number().int().nonnegative(),
  invitations: z.array(TeamInvitationSchema),
});
export type TeamSchema = z.infer<typeof TeamSchema>;

// Response schema for GET /api/team
export const TeamResponseSchema = TeamSchema;
export type TeamResponseSchema = z.infer<typeof TeamResponseSchema>;

export const UpdateTeamSchema = TeamSchema.pick({ seats: true });
export type UpdateTeamRequestSchema = z.infer<typeof UpdateTeamSchema>;
export type UpdateTeamResponseSchema = z.infer<typeof UpdateTeamSchema>;

export const TeamCreationStatusSchema = z.enum(["none", "pending", "active"]);
export type TeamCreationStatusSchema = z.infer<typeof TeamCreationStatusSchema>;

export const TeamStatusNoneSchema = z.object({
  status: z.literal("none"),
});
export type TeamStatusNoneSchema = z.infer<typeof TeamStatusNoneSchema>;

export const TeamStatusPendingSchema = z.object({
  status: z.literal("pending"),
  tenant_creation_id: z.string(),
});
export type TeamStatusPendingSchema = z.infer<typeof TeamStatusPendingSchema>;

export const TeamStatusActiveSchema = z.object({
  status: z.literal("active"),
  team: TeamSchema,
});
export type TeamStatusActiveSchema = z.infer<typeof TeamStatusActiveSchema>;

export const TeamStatusSchema = z.discriminatedUnion("status", [
  TeamStatusNoneSchema,
  TeamStatusPendingSchema,
  TeamStatusActiveSchema,
]);

export type TeamStatusSchema = z.infer<typeof TeamStatusSchema>;

export const TeamStatusMockSchema = TeamStatusActiveSchema.extend({
  isMock: z.literal(true),
});
export type TeamStatusMockSchema = z.infer<typeof TeamStatusMockSchema>;

export function isTeamNone(
  team: TeamStatusSchema | undefined,
): team is TeamStatusNoneSchema {
  return team?.status === "none";
}

export function isTeamPending(
  team: TeamStatusSchema | undefined,
): team is TeamStatusPendingSchema {
  return team?.status === "pending";
}

export function isTeamActive(
  team: TeamStatusSchema | undefined,
): team is TeamStatusActiveSchema {
  return team?.status === "active";
}

export const SubscriptionStatus = toConstObject(Subscription_OrbStatus);
export type SubscriptionStatus = toConstObject.infer<typeof SubscriptionStatus>;

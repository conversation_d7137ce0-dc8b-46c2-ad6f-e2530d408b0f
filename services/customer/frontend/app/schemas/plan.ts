import { z } from "zod";

import { BillingMethod } from "~services/auth/central/server/auth_entities_pb";
import { TenantTierSchema } from "./tenant";
import { AugmentPlanTypeSchema } from "./augment-plan";

export const UserPlanSchema = z.object({
  name: TenantTierSchema, // the old Stripe plan system uses the tenant tier to infer the plan
  pending: z.boolean().optional(),
  billingMethod: z.nativeEnum(BillingMethod).optional(),
});

export type UserPlanSchema = z.infer<typeof UserPlanSchema>;

export const PlanFeatureSchema = z.union([
  z.string(),
  z.object({
    text: z.string(),
    icon: z.string(),
    spacing: z.boolean().optional(),
  }),
]);

export type PlanFeatureSchema = z.infer<typeof PlanFeatureSchema>;

const PlanSchema = z.object({
  id: z.string(),
  augmentPlanType: AugmentPlanTypeSchema,
  name: z.string(),
  description: z.string(),
  agentRequests: z.number(),
  hasTraining: z.boolean(),
  hasTeams: z.boolean(),
  price: z.string(),
  priceLabel: z.string(),
  color: z.string(),
  colorScheme: z.object({
    radixColor: z.string(),
    gradientStart: z.string(),
    gradientEnd: z.string(),
  }),
  period: z.string(),
  features: z.array(PlanFeatureSchema),
  infoIconStyle: z
    .object({
      color: z.string(),
      weight: z.number().int().positive(),
      size: z.string(),
      textStyle: z.object({
        fontStyle: z.enum(["italic", "normal"]),
        opacity: z.number().min(0).max(1),
      }),
    })
    .optional(),
});

export type PlanSchema = z.infer<typeof PlanSchema>;

export const PlanInfoSchema = PlanSchema.pick({
  id: true,
  name: true,
  price: true,
  period: true,
  description: true,
  features: true,
  color: true,
  colorScheme: true,
  infoIconStyle: true,
});

export type PlanInfoSchema = z.infer<typeof PlanInfoSchema>;

export const PlanOptionSchema = PlanSchema.pick({
  id: true,
  augmentPlanType: true,
  name: true,
  description: true,
  agentRequests: true,
  hasTraining: true,
  hasTeams: true,
  price: true,
  priceLabel: true,
  color: true,
  colorScheme: true,
});

export type PlanOptionSchema = z.infer<typeof PlanOptionSchema>;

/** schema for plans array from the server */
export const PlansSchema = z.array(PlanOptionSchema);

export type PlansSchema = z.infer<typeof PlansSchema>;

/** schema for /api/plans */
export const PlansApiGETResponseSchema = PlansSchema;

export type PlansApiGETResponseSchema = z.infer<
  typeof PlansApiGETResponseSchema
>;

export function isCommunityPlanOption(
  plan: Pick<PlanSchema, "augmentPlanType">,
) {
  return plan.augmentPlanType === "community";
}

export function isPaidPlanOption(plan: Pick<PlanSchema, "augmentPlanType">) {
  return plan.augmentPlanType === "paid";
}

export function isEnterprisePlanOption(
  plan: Pick<PlanSchema, "augmentPlanType">,
) {
  return plan.augmentPlanType === "enterprise";
}

export function isTrialPlanOption(plan: Pick<PlanSchema, "augmentPlanType">) {
  return plan.augmentPlanType === "trial";
}

// Orb plan schemas and utilities

// Schema for plan features matching the orb.jsonnet structure
export const PlanFeatures = z.object({
  training_allowed: z.boolean(),
  teams_allowed: z.boolean(),
  max_seats: z.number(),
  add_credits_available: z.boolean(),
  plan_type: z.enum(["community", "trial", "paid"]),
});

// Schema for individual plan configuration
export const PlanConfig = z.object({
  id: z.string(),
  color: z.string(),
  sort_order: z.number().positive(),
  features: PlanFeatures,
});

// Schema for the full plans configuration array
export const PlansConfig = z.array(PlanConfig);

export type PlanFeatures = z.infer<typeof PlanFeatures>;
export type PlanConfig = z.infer<typeof PlanConfig>;
export type PlansConfig = z.infer<typeof PlansConfig>;

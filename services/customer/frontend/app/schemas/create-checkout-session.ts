import { z } from "zod";

// Schema for the request body
export const CreateCheckoutSessionRequestSchema = z.object({
  planId: z.string(),
});

export type CreateCheckoutSessionRequestSchema = z.infer<
  typeof CreateCheckoutSessionRequestSchema
>;

// Schema for the response body
export const CreateCheckoutSessionResponseSchema = z.object({
  success: z.boolean(),
  sessionId: z.string().optional(),
  url: z.string().optional(),
  error: z.string().optional(),
  isCommunityPlan: z.boolean().optional(),
});

export type CreateCheckoutSessionResponseSchema = z.infer<
  typeof CreateCheckoutSessionResponseSchema
>;

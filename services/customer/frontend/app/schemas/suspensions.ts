import { z } from "zod";
import { assertUnreachable } from "@augment-internal/ts-utils/type";
import { UserSuspensionType } from "~services/auth/central/server/auth_entities_pb";

export type SuspensionSchema = z.infer<typeof SuspensionSchema>;

// Define the enum first
export enum SuspensionType {
  ApiAbuse = "USER_SUSPENSION_TYPE_API_ABUSE",
  FreeTrialAbuse = "USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE",
  CommunityAbuse = "USER_SUSPENSION_TYPE_COMMUNITY_ABUSE",
}

export const SuspensionTypeSchema = z.nativeEnum(SuspensionType);

export type SuspensionTypeSchema = z.infer<typeof SuspensionTypeSchema>;

export const SuspensionSchema = z.object({
  suspensionId: z.string(),
  createdTime: z.string().datetime().optional(),
  suspensionType: SuspensionTypeSchema,
  evidence: z.string(),
});

// this function, while not used, will break eslint if new enums are added to the proto but not handled here.
export function mapSuspensionTypeToSuspensionTypeSchema(
  suspensionType: UserSuspensionType,
): SuspensionTypeSchema {
  switch (suspensionType) {
    case UserSuspensionType.API_ABUSE:
      return SuspensionType.ApiAbuse;
    case UserSuspensionType.FREE_TRIAL_ABUSE:
      return SuspensionType.FreeTrialAbuse;
    case UserSuspensionType.COMMUNITY_ABUSE:
      return SuspensionType.CommunityAbuse;
    case UserSuspensionType.UNKNOWN:
      throw new Error("could not determine tenant tier");
    default:
      assertUnreachable(suspensionType);
  }
}

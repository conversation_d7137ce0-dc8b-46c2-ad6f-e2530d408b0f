import { z } from "zod";

export const TeamInviteRequestSchema = z.object({
  emails: z.array(z.string().email()),
});
export type TeamInviteRequestSchema = z.infer<typeof TeamInviteRequestSchema>;

export const TeamInviteSuccessSchema = z.object({});
export type TeamInviteSuccessSchema = z.infer<typeof TeamInviteSuccessSchema>;

// Partial success response (207)
export const TeamInvitePartialSuccessSchema = z.object({
  failed: z.array(z.string().email()),
});
export type TeamInvitePartialSuccessSchema = z.infer<
  typeof TeamInvitePartialSuccessSchema
>;

// Error response (400, 403, 405, 500)
export const TeamInviteErrorSchema = z.object({
  message: z.string(),
});
export type TeamInviteErrorSchema = z.infer<typeof TeamInviteErrorSchema>;

export const TeamInviteResponseSchema = z.union([
  TeamInviteSuccessSchema,
  TeamInvitePartialSuccessSchema,
  TeamInviteErrorSchema,
]);
export type TeamInviteResponseSchema = z.infer<typeof TeamInviteResponseSchema>;

export const TeamDeleteInvitationRequestSchema = z.object({
  invitationId: z.string().uuid(),
});
export type TeamDeleteInvitationRequestSchema = z.infer<
  typeof TeamDeleteInvitationRequestSchema
>;

export const TeamDeleteInvitationSuccessSchema = z.object({});
export type TeamDeleteInvitationSuccessSchema = z.infer<
  typeof TeamDeleteInvitationSuccessSchema
>;

export const TeamDeleteInvitationErrorSchema = z.object({
  message: z.string().optional(),
});
export type TeamDeleteInvitationErrorSchema = z.infer<
  typeof TeamDeleteInvitationErrorSchema
>;

export const TeamDeleteInvitationResponseSchema = z.union([
  TeamDeleteInvitationSuccessSchema,
  TeamDeleteInvitationErrorSchema,
]);
export type TeamDeleteInvitationResponseSchema = z.infer<
  typeof TeamDeleteInvitationResponseSchema
>;

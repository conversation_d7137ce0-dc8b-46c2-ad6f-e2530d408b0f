import { z } from "zod";
import { UserPlanSchema } from "./plan";
import { TenantTierSchema } from "./tenant";
import { SuspensionSchema } from "./suspensions";

export const UserApiGETResponseSchema = z.object({
  email: z.string().email(),
  isAdmin: z.boolean(),
  isSelfServeTeamMember: z.boolean(),
  plan: UserPlanSchema.pick({
    name: true,
    pending: true,
    billingMethod: true,
  }),
  tenantTier: TenantTierSchema,
  isSubscriptionPending: z.boolean().optional(),
  showTeamManagementLink: z.boolean(),
  suspensions: z.array(SuspensionSchema),
});

export type UserApiGETResponseSchema = z.infer<typeof UserApiGETResponseSchema>;

/**
 * Determines if a plan or user is on the enterprise plan
 * @param planOrUser
 * @returns
 */
export function isEnterprisePlan(
  planOrUser:
    | Pick<UserPlanSchema, "name">
    | Pick<UserApiGETResponseSchema, "plan">
    | undefined,
) {
  if (!planOrUser) return false;
  const plan = "plan" in planOrUser ? planOrUser.plan : planOrUser;
  return plan.name === "enterprise";
}

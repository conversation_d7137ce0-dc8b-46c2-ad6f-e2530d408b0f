import { z } from "zod";

// Success response (200)
export const TeamUserDeleteSuccessSchema = z.object({});

// Error response (400, 403, 404, 405, 500)
export const TeamUserDeleteErrorSchema = z.object({
  message: z.string().optional(),
});

// Union of all possible responses
export const TeamUserDeleteResponseSchema = z.union([
  TeamUserDeleteSuccessSchema,
  TeamUserDeleteErrorSchema,
]);

// Types
export type TeamUserDeleteSuccessSchema = z.infer<
  typeof TeamUserDeleteSuccessSchema
>;
export type TeamUserDeleteErrorSchema = z.infer<
  typeof TeamUserDeleteErrorSchema
>;
export type TeamUserDeleteResponseSchema = z.infer<
  typeof TeamUserDeleteResponseSchema
>;

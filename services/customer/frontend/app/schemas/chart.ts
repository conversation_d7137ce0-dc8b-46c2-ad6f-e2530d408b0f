import { z } from "zod";

export const ChartDataPointSchema = z.object({
  date: z.string().datetime(),
  value: z.number().positive(),
});

export type ChartDataPointSchema = z.infer<typeof ChartDataPointSchema>;

export const ChartDataSchema = z.object({
  data: z.array(ChartDataPointSchema),
  trend: z.number().nullable(),
});

export type ChartDataSchema = z.infer<typeof ChartDataSchema>;

export const KeyDataSchema = z.object({
  label: z.string(),
  value: z.number().positive(),
});

export type KeyDataSchema = z.infer<typeof KeyDataSchema>;

export const KeyDataResponseSchema = z.object({
  data: z.array(KeyDataSchema),
});

export type KeyDataResponseSchema = z.infer<typeof KeyDataResponseSchema>;

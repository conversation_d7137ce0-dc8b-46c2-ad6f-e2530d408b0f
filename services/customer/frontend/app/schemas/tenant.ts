import { assertUnreachable } from "@augment-internal/ts-utils/type";
import { z } from "zod";
import { TenantTier } from "~services/tenant_watcher/tenant_watcher_pb";

export const TenantTierSchema = z.enum([
  "community",
  "developer",
  "enterprise",
]);
export type TenantTierSchema = z.infer<typeof TenantTierSchema>;

export function mapTenantTierToTierSchema(tier: TenantTier): TenantTierSchema {
  switch (tier) {
    case TenantTier.PROFESSIONAL:
      return "developer";
    case TenantTier.COMMUNITY:
      return "community";
    case TenantTier.ENTERPRISE:
      return "enterprise";
    case TenantTier.TENANT_TIER_UNKNOWN:
      throw new Error("could not determine tenant tier");
    default:
      assertUnreachable(tier);
  }
}

import { z } from "zod";

// Request schema
export const TeamSeatsRequestSchema = z.object({
  seats: z.number().int().positive(),
});

// Success response (200)
export const TeamSeatsSuccessSchema = z.object({
  seats: z.number().int().positive(),
});

// Error response (400, 403, 405, 500)
export const TeamSeatsErrorSchema = z.object({
  message: z.string(),
});

// Union of all possible responses
export const TeamSeatsResponseSchema = z.union([
  TeamSeatsSuccessSchema,
  TeamSeatsErrorSchema,
]);

// Types
export type TeamSeatsRequestSchema = z.infer<typeof TeamSeatsRequestSchema>;
export type TeamSeatsSuccessSchema = z.infer<typeof TeamSeatsSuccessSchema>;
export type TeamSeatsErrorSchema = z.infer<typeof TeamSeatsErrorSchema>;
export type TeamSeatsResponseSchema = z.infer<typeof TeamSeatsResponseSchema>;

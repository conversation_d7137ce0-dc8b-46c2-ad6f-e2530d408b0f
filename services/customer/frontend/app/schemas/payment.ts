import { z } from "zod";

export const FailedPaymentSchema = z.object({
  amount: z.string(),
  date: z.string(),
});

export type FailedPayment = z.infer<typeof FailedPaymentSchema>;

export const GetUserOrbPaymentInfoResponseSchema = z.object({
  hasPaymentMethod: z.boolean(),
  failedPayment: FailedPaymentSchema.optional(),
});

export type GetUserOrbPaymentInfoResponse = z.infer<
  typeof GetUserOrbPaymentInfoResponseSchema
>;

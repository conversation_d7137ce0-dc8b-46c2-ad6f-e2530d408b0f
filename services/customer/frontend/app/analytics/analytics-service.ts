import { DeferredPromise } from "@augment-internal/ts-utils/promise";
import { config } from "./config";
import type { AnalyticsEvent, UserIdentity } from "./events";

export function track(event: AnalyticsEvent) {
  SegmentAnalytics.getInstance().track(event);
}

export class SegmentAnalytics {
  private static instance: SegmentAnalytics | null;
  isInitialized = false;

  private constructor() {
    // We do NOT set isInitialized here, since the aggregator itself
    // isn't "initialized" until sub-providers have been initialized.
    globalThis.analyticsInitialized = new DeferredPromise<void>();
  }

  public static getInstance(): SegmentAnalytics {
    if (!SegmentAnalytics.instance) {
      SegmentAnalytics.instance = new SegmentAnalytics();
    }
    return SegmentAnalytics.instance;
  }

  static readonly scriptSrc = `(function() {
    var i = "analytics",
        analytics = window[i] = window[i] || [];
    if (!analytics.initialize) {
      if (analytics.invoked) {
        window.console && console.error && console.error("Segment snippet included twice.");
      } else {
        analytics.invoked = true;
        analytics.methods = [
          "trackSubmit", "trackClick", "trackLink", "trackForm", "pageview",
          "identify", "reset", "group", "track", "ready", "alias", "debug",
          "page", "screen", "once", "off", "on", "addSourceMiddleware",
          "addIntegrationMiddleware", "setAnonymousId", "addDestinationMiddleware",
          "register"
        ];
        analytics.factory = function(method) {
          return function() {
            if (window[i].initialized) {
              return window[i][method].apply(window[i], arguments);
            }
            var args = Array.prototype.slice.call(arguments);
            if (["track", "screen", "alias", "group", "page", "identify"].indexOf(method) > -1) {
              var canonicalLink = document.querySelector("link[rel='canonical']");
              args.push({
                __t: "bpc",
                c: (canonicalLink && canonicalLink.getAttribute("href")) || void 0,
                p: location.pathname,
                u: location.href,
                s: location.search,
                t: document.title,
                r: document.referrer
              });
            }
            args.unshift(method);
            analytics.push(args);
            return analytics;
          };
        };
        for (var n = 0; n < analytics.methods.length; n++) {
          var key = analytics.methods[n];
          analytics[key] = analytics.factory(key);
        }
        analytics.load = function(key, options) {
          var script = document.createElement("script");
          script.type = "text/javascript";
          script.async = true;
          script.setAttribute("data-global-segment-analytics-key", i);
          script.src = "${config.SEGMENT_SCRIPT_URL}";
          var firstScript = document.getElementsByTagName("script")[0];
          firstScript.parentNode.insertBefore(script, firstScript);
          analytics._loadOptions = options;
        };
        analytics._cdn = "${config.SEGMENT_CDN}";
        analytics._writeKey = "${config.SEGMENT_WRITE_KEY}";
        analytics.SNIPPET_VERSION = "5.2.0";
        analytics.load(analytics._writeKey);
        analytics.ready(() => {
          window.posthog.init(
            "${config.POSTHOG_KEY}",
            {
              api_host: "${config.POSTHOG_HOST}",
              segment: window.analytics,
              capture_pageview: false,
              capture_pageleave: true,
            }
          );
          if (window.analyticsInitialized) {
            window.analyticsInitialized.resolve();
          } else {
            console.error("analytics deferred promise not found");
          }
        });
      }
    }
  })();
  `;

  private get segment() {
    if (!globalThis.analytics) {
      throw new Error("Segment not found");
    }
    return globalThis.analytics;
  }

  eventQueue: {
    page: Array<Record<string, unknown> | undefined>;
    track: Array<AnalyticsEvent>;
  } = { page: [], track: [] };

  init(user: UserIdentity): void {
    this.isInitialized = true;
    this.segment.identify(user.id, user);
    for (const properties of this.eventQueue.page) {
      this.segment.page(properties);
    }
    for (const event of this.eventQueue.track) {
      this.segment.track(event.name, event.properties);
    }
    this.eventQueue = { page: [], track: [] };
  }

  page(properties?: Record<string, unknown>): void {
    if (!this.isInitialized) {
      this.eventQueue.page.push(properties);
      return;
    }
    this.segment.page(properties);
  }

  track(analyticsEvent: AnalyticsEvent): void {
    if (!this.isInitialized) {
      this.eventQueue.track.push(analyticsEvent);
      return;
    }
    const { name, properties } = analyticsEvent;
    this.segment.track(name, properties);
  }

  destroy() {
    this.isInitialized = false;
    SegmentAnalytics.instance = null;
  }
}

export class PostHogAnalytics {
  private static instance: PostHogAnalytics | null;
  isInitialized = false;

  private constructor() {
    // We do NOT set isInitialized here, since the aggregator itself
    // isn't "initialized" until sub-providers have been initialized.
  }

  public static getInstance(): PostHogAnalytics {
    if (!PostHogAnalytics.instance) {
      PostHogAnalytics.instance = new PostHogAnalytics();
    }
    return PostHogAnalytics.instance;
  }

  static readonly scriptSrc = `(function(document, posthog) {
    var methodList, methodIndex, scriptElement, firstScript;
    if (!posthog.__SV) {
      window.posthog = posthog;
      posthog._i = [];
      posthog.init = function(apiKey, config, namespace) {
        // Create a stub function that collects method calls until the real library loads.
        function createStub(target, methodName) {
          var parts = methodName.split(".");
          if (parts.length === 2) {
            target = target[parts[0]];
            methodName = parts[1];
          }
          target[methodName] = function() {
            target.push([methodName].concat(Array.prototype.slice.call(arguments, 0)));
          };
        }
        // Create and insert the script element to load the PostHog library.
        scriptElement = document.createElement("script");
        scriptElement.type = "text/javascript";
        scriptElement.crossOrigin = "anonymous";
        scriptElement.async = true;
        scriptElement.src = config.api_host + "/static/array.js";
        firstScript = document.getElementsByTagName("script")[0];
        firstScript.parentNode.insertBefore(scriptElement, firstScript);
        // Initialize the PostHog namespace.
        var ph = posthog;
        if (namespace !== undefined) {
          ph = posthog[namespace] = [];
        } else {
          namespace = "posthog";
        }
        ph.people = ph.people || [];
        ph.toString = function(stub) {
          var label = "posthog";
          if (namespace !== "posthog") {
            label += "." + namespace;
          }
          if (!stub) {
            label += " (stub)";
          }
          return label;
        };
        ph.people.toString = function() {
          return ph.toString(1) + ".people (stub)";
        };
        // List of methods to be stubbed until the library loads.
        methodList = "capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep".split(" ");
        for (methodIndex = 0; methodIndex < methodList.length; methodIndex++) {
          createStub(ph, methodList[methodIndex]);
        }
        // Store initialization arguments for later use.
        posthog._i.push([apiKey, config, namespace]);
      };
      posthog.__SV = 1;
    }
  })(document, window.posthog || []);
  `;

  private get posthog() {
    if (!globalThis.posthog) {
      throw new Error("PostHog not found");
    }
    return globalThis.posthog;
  }

  eventQueue: {
    pageLeave: Array<Record<string, unknown> | undefined>;
  } = { pageLeave: [] };

  init(user: UserIdentity): void {
    this.isInitialized = true;
    const intervalId = setInterval(function () {
      if (globalThis.posthog) {
        clearInterval(intervalId);
      }
    });
    this.posthog.identify(user.id, user);
    for (const properties of this.eventQueue.pageLeave) {
      this.posthog.capture("$pageleave", properties);
    }
    this.eventQueue = { pageLeave: [] };
  }

  pageLeave(properties?: Record<string, unknown>): void {
    if (!this.isInitialized) {
      this.eventQueue.pageLeave.push(properties);
      return;
    }
    // Segment handles `$pageview`, PostHog deals with `$pageleave` only
    this.posthog.capture("$pageleave", properties);
  }

  destroy() {
    this.isInitialized = false;
    PostHogAnalytics.instance = null;
  }
}

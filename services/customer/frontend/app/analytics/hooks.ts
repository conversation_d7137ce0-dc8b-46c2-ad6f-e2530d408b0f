import { useEffect } from "react";
import { useLoaderData, useLocation } from "@remix-run/react";
import type { SessionUser } from "app/.server/auth";
import { PostHogAnalytics, SegmentAnalytics } from "./analytics-service";
import { addEventListener } from "app/utils/extra";

export function useInitAnalytics() {
  const { user } = useLoaderData<{ user: SessionUser }>();
  useEffect(() => {
    const providers = [
      PostHogAnalytics.getInstance(),
      SegmentAnalytics.getInstance(),
    ];
    if (user) {
      const userIdentity = {
        id: user.userId,
        email: user.email,
      };
      globalThis.analyticsInitialized.then(() => {
        // need to wait for the scripts to load
        for (const provider of providers) {
          provider.init(userIdentity);
        }
      });
    }
  }, [user]);
}

export function useTrackPageChange() {
  useInitAnalytics();
  const location = useLocation();
  useEffect(() => {
    SegmentAnalytics.getInstance().page();
    return () => {
      PostHogAnalytics.getInstance().pageLeave({ ...location });
    };
  }, [location]);
  useEffect(() => {
    return addEventListener(window, "beforeunload", function onBeforeUnload() {
      PostHogAnalytics.getInstance().pageLeave({ ...window.location });
    });
  }, []);
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, test, expect, beforeEach, afterEach, vi } from "vitest";
import { render, renderHook, waitFor } from "@testing-library/react";
import { useInitAnalytics, useTrackPageChange } from "./hooks";
import { SegmentAnalytics, PostHogAnalytics, track } from "./analytics-service";
import type { AnalyticsEvent, UserIdentity } from "./events";
import { useLoaderData, useNavigate } from "@remix-run/react";
import { createRemixStub } from "@remix-run/testing";
import { act, createElement } from "react";

vi.mock("./config", () => ({
  config: {
    SEGMENT_WRITE_KEY: "test-segment-key",
    POSTHOG_KEY: "test-posthog-key",
    POSTHOG_HOST: "https://test.posthog.com",
    SEGMENT_CDN: "https://test-cdn.segment.com",
  },
}));

function identify(user: UserIdentity) {
  for (const provider of [
    SegmentAnalytics.getInstance(),
    PostHogAnalytics.getInstance(),
  ]) {
    provider.init(user);
  }
}

const testUser = {
  userId: "test-user-123",
  email: "<EMAIL>",
  tenantName: "community",
};
const testUserIdentity = {
  id: "test-user-123",
  email: "<EMAIL>",
};

vi.mock("@remix-run/react", async () => {
  const actual = await vi.importActual("@remix-run/react");
  return {
    ...actual,
    useLoaderData: vi.fn(() => ({
      user: testUser,
    })),
    // useLocation: vi.fn(() => ({ pathname: "/" })),
    useNavigate: vi.fn(() => (path: string) => {
      // Mock implementation of navigate
      window.history.pushState({}, "", path);
      // Simulate a location change event
      window.dispatchEvent(new Event("popstate"));
    }),
  };
});

describe("Analytics Services", () => {
  let posthog: ReturnType<typeof mockPosthog>;
  let segment: ReturnType<typeof mockSegment>;
  let segmentInstance: SegmentAnalytics;

  beforeEach(async () => {
    window.posthog = posthog = mockPosthog();
    window.analytics = segment = mockSegment();
    await identify(testUserIdentity);
    segmentInstance = SegmentAnalytics.getInstance();
  });

  afterEach(() => {
    clearAnalyticsMocks();
    vi.clearAllMocks();
    // Use destroy method instead of directly resetting the instances
    SegmentAnalytics.getInstance().destroy();
    PostHogAnalytics.getInstance().destroy();
  });

  describe("SegmentAnalytics", () => {
    let segmentAnalytics: SegmentAnalytics;

    beforeEach(() => {
      segmentAnalytics = SegmentAnalytics.getInstance();
    });

    test("identify calls Segment identify", () => {
      segmentAnalytics.init(testUserIdentity);
      expect(segment.identify).toHaveBeenCalledWith(
        testUserIdentity.id,
        testUserIdentity,
      );
    });

    test("page calls Segment page", () => {
      const properties = { path: "/test" };
      segmentAnalytics.page(properties);
      expect(segment.page).toHaveBeenCalledWith(properties);
    });

    test("track calls Segment track", () => {
      const event: AnalyticsEvent = {
        name: "test_event",
        properties: { foo: "bar" },
      } as any;
      segmentAnalytics.track(event);
      expect(segment.track).toHaveBeenCalledWith(event.name, event.properties);
    });

    test("throws error when Segment is not available", () => {
      // @ts-expect-error: Trying out edge case
      window.analytics = undefined;
      expect(() => segmentAnalytics.init(testUserIdentity)).toThrow(
        "Segment not found",
      );
    });
  });

  describe("PostHogAnalytics", () => {
    let posthogAnalytics: PostHogAnalytics;

    beforeEach(() => {
      posthogAnalytics = PostHogAnalytics.getInstance();
    });

    test("identify calls PostHog identify", () => {
      posthogAnalytics.init(testUserIdentity);
      expect(posthog.identify).toHaveBeenCalledWith(
        testUserIdentity.id,
        testUserIdentity,
      );
    });

    test("pageLeave calls PostHog capture with $pageleave", () => {
      const properties = { path: "/test" };
      posthogAnalytics.pageLeave(properties);
      expect(posthog.capture).toHaveBeenCalledWith("$pageleave", properties);
    });

    test("throws error when PostHog is not available", () => {
      // @ts-expect-error: Trying out edge case
      window.posthog = undefined;
      expect(() => posthogAnalytics.init(testUserIdentity)).toThrow(
        "PostHog not found",
      );
    });
  });

  describe("identify", () => {
    test("initializes analytics and identifies user", async () => {
      await identify(testUserIdentity);
      expect(segment.identify).toHaveBeenCalledWith(
        testUserIdentity.id,
        testUserIdentity,
      );
      expect(posthog.identify).toHaveBeenCalledWith(
        testUserIdentity.id,
        testUserIdentity,
      );
    });
  });

  describe("track", () => {
    test("tracks event with correct parameters", () => {
      const testEvent: AnalyticsEvent = {
        name: "test_event",
        properties: {
          foo: "bar",
          count: 42,
        },
      } as any;

      const trackSpy = vi.spyOn(segmentInstance, "track");
      track(testEvent as never);

      expect(trackSpy).toHaveBeenCalledTimes(1);
      expect(trackSpy).toHaveBeenCalledWith(testEvent);
    });

    test("tracks event without properties", () => {
      const testEvent: AnalyticsEvent = {
        name: "simple_event",
      } as any;

      const trackSpy = vi.spyOn(segmentInstance, "track");
      track(testEvent);

      expect(trackSpy).toHaveBeenCalledTimes(1);
      expect(trackSpy).toHaveBeenCalledWith(testEvent);
    });

    test("handles tracking multiple events", () => {
      const event1: AnalyticsEvent = {
        name: "event_1",
        properties: { type: "first" },
      } as any;
      const event2: AnalyticsEvent = {
        name: "event_2",
        properties: { type: "second" },
      } as any;

      const trackSpy = vi.spyOn(segmentInstance, "track");
      track(event1);
      track(event2);

      expect(trackSpy).toHaveBeenCalledTimes(2);
      expect(trackSpy).toHaveBeenNthCalledWith(1, event1);
      expect(trackSpy).toHaveBeenNthCalledWith(2, event2);
    });
  });

  describe("useInitAnalytics", () => {
    test("initializes analytics with user identity", async () => {
      const mockUser = {
        userId: "test-user-123",
        email: "<EMAIL>",
        tenantName: "community",
      };

      // Mock the useLoaderData hook
      vi.mocked(useLoaderData).mockReturnValue({
        user: mockUser,
      });

      const userIdentity = {
        id: mockUser.userId,
        email: mockUser.email,
      };

      // Create spies for the init and identify methods
      const segmentIdentifySpy = vi.spyOn(
        SegmentAnalytics.getInstance(),
        "init",
      );
      const posthogIdentifySpy = vi.spyOn(
        PostHogAnalytics.getInstance(),
        "init",
      );

      renderHook(useInitAnalytics);
      globalThis.analyticsInitialized.resolve();
      await globalThis.analyticsInitialized;

      expect(segmentIdentifySpy).toHaveBeenCalledWith(userIdentity);
      expect(posthogIdentifySpy).toHaveBeenCalledWith(userIdentity);
    });
  });

  describe("useTrackPageView", () => {
    expect.assertions(2);
    // FIXME: Remix useNavigate does not trigger useLocation change with RemixStub
    test.skip("tracks page view on route change", async () => {
      document.title = "Test Title";

      const TestComponent = () => {
        const navigate = useNavigate();
        useTrackPageChange();

        return createElement("button", {
          onClick: () => {
            navigate("/test");
          },
          "data-testid": "navigate-btn",
        });
      };

      const RemixStub = createRemixStub([
        {
          path: "/",
          Component: TestComponent,
        },
        {
          path: "/test",
          Component: TestComponent,
        },
      ]);

      const pageSpy = vi.spyOn(SegmentAnalytics.getInstance(), "page");
      const { getByTestId } = render(
        createElement(RemixStub, { initialEntries: ["/"] }),
      );
      await new Promise((resolve) => setTimeout(resolve));

      expect(pageSpy).toHaveBeenCalledWith({
        path: "/",
        referrer: "",
        title: "Test Title",
      });

      vi.clearAllMocks();
      document.title = "Another Page";
      act(() => getByTestId("navigate-btn").click());

      await waitFor(() => {
        expect(pageSpy).toHaveBeenCalledWith({
          path: "/test",
          referrer: "",
          title: "Another Page",
        });
      });
    });
  });
});

let originalPosthog = window.posthog;
let originalAnalytics = window.analytics;

/** Mocks the PostHog library on the window object. */
function mockPosthog(): typeof window.posthog {
  originalPosthog = window.posthog;
  const posthog = {
    init: vi.fn((_, config) => config.loaded?.()),
    identify: vi.fn(),
    capture: vi.fn(),
  };

  window.posthog = posthog;
  return posthog;
}

/** Mocks the Segment library on the window object. */
function mockSegment(): typeof window.analytics {
  originalAnalytics = window.analytics;
  const analytics = {
    load: vi.fn(),
    identify: vi.fn(),
    page: vi.fn(),
    track: vi.fn(),
  };

  window.analytics = analytics;
  return analytics;
}

function clearAnalyticsMocks() {
  window.posthog = originalPosthog;
  window.analytics = originalAnalytics;
  vi.clearAllMocks();
}

import { createElement, memo } from "react";
import { PostHogAnalytics, SegmentAnalytics } from "./analytics-service";

export { track } from "./analytics-service";
export { useTrackPageChange as useTrackPageView } from "./hooks";

const isProduction = process.env.NODE_ENV === "production";
function SegmentScript() {
  return createElement("script", {
    type: "text/javascript",
    dangerouslySetInnerHTML: {
      __html: isProduction ? SegmentAnalytics.scriptSrc : "",
    },
  });
}

function PostHogScript() {
  return createElement("script", {
    type: "text/javascript",
    dangerouslySetInnerHTML: {
      __html: isProduction ? PostHogAnalytics.scriptSrc : "",
    },
  });
}

export const AnalyticsScripts = memo(function AnalyticsScripts() {
  return [
    createElement(SegmentScript, { key: "segment" }),
    createElement(PostHogScript, { key: "posthog" }),
  ];
});

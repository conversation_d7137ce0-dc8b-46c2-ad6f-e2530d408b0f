/* eslint-disable no-var */
import type { DeferredPromise } from "@augment-internal/ts-utils/promise";

interface PosthogConfig {
  api_host: string;
  capture_pageview: boolean;
  capture_pageleave: boolean;
  segment: SegmentAnalyticsInstance;
  loaded?: (posthog: PosthogInstance) => void;
}

interface PosthogInstance {
  init(key: string, config: PosthogConfig): void;
  identify(userId: string, traits: Record<string, unknown>): void;
  capture(event: string, properties?: Record<string, unknown>): void;
}

interface SegmentAnalyticsInstance {
  load(writeKey: string): void;
  identify(userId: string, traits: Record<string, unknown>): void;
  page(properties?: Record<string, unknown>): void;
  track(event: string, properties?: Record<string, unknown>): void;
}

declare global {
  var posthog: PosthogInstance;
  var analytics: SegmentAnalyticsInstance;
  var analyticsInitialized: DeferredPromise<void>;

  interface Window {
    posthog: PosthogInstance;
    analytics: SegmentAnalyticsInstance;
    analyticsInitialized: DeferredPromise<void>;
  }
}

export {};

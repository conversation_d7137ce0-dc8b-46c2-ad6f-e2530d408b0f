export type AnalyticsEvent = Satisfies<PageViewEvent | ExampleEvent>;

type PageViewEvent = {
  name: "Page View";
  properties: {
    page: string;
  };
};

type ExampleEvent = {
  name: "Example Event";
  properties: {
    exampleProperty: string;
  };
};

type Satisfies<
  T extends {
    name: string;
    properties: Record<string, unknown>;
  },
> = T;

export type UserIdentity = {
  id: string;
  email: string;
};

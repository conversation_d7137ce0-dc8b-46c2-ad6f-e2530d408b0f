import { UnauthorizedError } from "app/client-cache/error";
import type { ZodSchema } from "zod";

export function queryFetch<
  S extends ZodSchema<any>,
  T extends S extends ZodSchema<infer U> ? U : never,
  R = () => Promise<T>,
>(endpoint: string, schema: ZodSchema<T>): R {
  return async function fetchData() {
    const response = await fetch(endpoint);

    if (!response.ok) {
      if (response.status === 401) {
        throw new UnauthorizedError("Unauthorized");
      }
      throw new Error(`Failed to fetch data from ${endpoint}`);
    }

    const data = await response.json();
    return schema.parse(data);
  } as R;
}

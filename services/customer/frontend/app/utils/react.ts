import type { MutableRefObject, Ref } from "react";
import { isNonNullable } from "./guards";
import type { Nullish } from "@augment-internal/ts-utils/type";

export function combineRefs<T extends HTMLElement>(
  primaryRef: MutableRefObject<T | null>,
  ...refs: (Ref<T> | Nullish)[]
) {
  let current: T | null = primaryRef.current;
  Object.defineProperty(primaryRef, "current", {
    get() {
      return current;
    },
    set(node: T | null) {
      current = node;
      refs.filter(isNonNullable).forEach((ref) => {
        if (typeof ref === "function") {
          ref(node);
        } else if (ref) {
          (ref as MutableRefObject<T | null>).current = node;
        }
      });
    },
  });
  return primaryRef;
}

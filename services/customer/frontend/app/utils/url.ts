export function stringifyUrl(params: { [key: string]: string | object }) {
  return Object.keys(params)
    .map((key) => {
      const value = params[key];
      if (typeof value === "object") {
        return `${key}=${encodeURIComponent(JSON.stringify(value))}`;
      } else {
        return `${key}=${encodeURIComponent(value)}`;
      }
    })
    .join("&");
}

export function toFormData(obj: Record<string, string | Blob>) {
  const formData = new FormData();
  Object.entries(obj).forEach(([key, value]) => {
    formData.append(key, value);
  });
  return formData;
}

import { describe, it, expect, vi, beforeEach } from "vitest";
import { TenantTier } from "~services/tenant_watcher/tenant_watcher_pb";
import { BillingMethod } from "~services/auth/central/server/auth_entities_pb";

// Mock the dependencies
vi.mock("../../.server/auth", () => ({
  getTenantTier: vi.fn(),
}));

vi.mock("../../.server/grpc/auth-central", () => ({
  AuthCentralClient: {
    getInstance: vi.fn(() => ({
      getUser: vi.fn().mockResolvedValue({
        user: {
          billingMethod: BillingMethod.ORB,
        },
      }),
    })),
  },
}));

vi.mock("app/feature-flags/feature-flags.server", () => ({
  getFeatureFlag: vi.fn().mockResolvedValue(true),
}));

vi.mock("../../.server/grpc/tenant-watcher", () => ({
  TenantWatcherCachingClient: {
    getInstance: vi.fn(() => ({
      tenantFor: vi.fn(),
    })),
  },
}));

// Import the function after mocking dependencies
import { teamsAllowed } from "../team.server";
import { getTenantTier } from "../../.server/auth";
import { getFeatureFlag } from "app/feature-flags/feature-flags.server";
import { TenantWatcherCachingClient } from "../../.server/grpc/tenant-watcher";

describe("teamsAllowed", () => {
  const mockUser = {
    userId: "test-user-id",
    tenantId: "test-tenant-id",
    tenantName: "test-tenant",
    shardNamespace: "test-shard",
    email: "<EMAIL>",
    roles: ["ADMIN"],
    createdAt: Date.now(),
    sessionId: "test-session-id",
  };

  const mockTenantWatcher = {
    tenantFor: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Set up default mock implementations
    vi.mocked(getTenantTier).mockResolvedValue(TenantTier.PROFESSIONAL);
    vi.mocked(TenantWatcherCachingClient.getInstance).mockReturnValue(
      mockTenantWatcher as any,
    );

    // Default mock for getFeatureFlag
    vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
      if (flagName === "team_management") {
        return true; // Default to true for team_management
      }
      if (flagName === "team_management_canary_domains") {
        return ""; // Default to empty for canary domains
      }
      // For other flags that might be called by other tests if this file grows
      return false;
    });

    // Default mock for tenant - not a self-serve team
    mockTenantWatcher.tenantFor.mockResolvedValue({
      config: {
        configs: {
          is_self_serve_team: "false",
        },
      },
    });
  });

  it("should return true when all conditions are met (team_management true, not in canary)", async () => {
    // Arrange - beforeEach sets team_management to true and canary_domains to ""

    // Act
    const result = await teamsAllowed(mockUser);

    // Assert
    expect(result).toBe(true);
    expect(getTenantTier).toHaveBeenCalledWith(mockUser);
    expect(getFeatureFlag).toHaveBeenCalledWith("team_management", mockUser);
    expect(getFeatureFlag).toHaveBeenCalledWith(
      "team_management_canary_domains",
      mockUser,
    );
  });

  it("should return false when tenant tier is not PROFESSIONAL", async () => {
    // Arrange
    vi.mocked(getTenantTier).mockResolvedValue(TenantTier.COMMUNITY);

    // Act
    const result = await teamsAllowed(mockUser);

    // Assert
    expect(result).toBe(false);
  });

  it("should return false when team_management flag is false and user is not in canary list", async () => {
    // Arrange
    vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
      if (flagName === "team_management") {
        return false;
      }
      if (flagName === "team_management_canary_domains") {
        return ""; // Not in canary list
      }
      return false;
    });

    // Act
    const result = await teamsAllowed(mockUser);

    // Assert
    expect(result).toBe(false);
    expect(getFeatureFlag).toHaveBeenCalledWith("team_management", mockUser);
    expect(getFeatureFlag).toHaveBeenCalledWith(
      "team_management_canary_domains",
      mockUser,
    );
  });

  it("should return false when tenant tier is ENTERPRISE", async () => {
    // Arrange
    vi.mocked(getTenantTier).mockResolvedValue(TenantTier.ENTERPRISE);

    // Act
    const result = await teamsAllowed(mockUser);

    // Assert
    expect(result).toBe(false);
  });

  describe("team_management_canary_domains logic", () => {
    const canaryUser = {
      ...mockUser,
      email: "<EMAIL>",
    };
    const nonCanaryUser = {
      ...mockUser,
      email: "<EMAIL>",
    };

    it("should return true if user's domain is in canary list, even if team_management flag is false", async () => {
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return false;
        if (flagName === "team_management_canary_domains")
          return "canarydomain.com,another.com";
        return undefined;
      });
      const result = await teamsAllowed(canaryUser);
      expect(result).toBe(true);
      expect(getFeatureFlag).toHaveBeenCalledWith(
        "team_management_canary_domains",
        canaryUser,
      );
    });

    it("should handle case-insensitivity and whitespace in canary domain list", async () => {
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return false;
        if (flagName === "team_management_canary_domains")
          return "  CANARYDOMAIN.COM  , another.com ";
        return undefined;
      });
      const result = await teamsAllowed(canaryUser);
      expect(result).toBe(true);
    });

    it("should return false if user's domain is in canary list but tenantTier is not PROFESSIONAL", async () => {
      vi.mocked(getTenantTier).mockResolvedValue(TenantTier.COMMUNITY);
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return false;
        if (flagName === "team_management_canary_domains")
          return "canarydomain.com";
        return undefined;
      });
      const result = await teamsAllowed(canaryUser);
      expect(result).toBe(false);
    });

    it("should return false if user's domain is NOT in canary list and team_management flag is false", async () => {
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return false;
        if (flagName === "team_management_canary_domains")
          return "another.com,example.com";
        return undefined;
      });
      const result = await teamsAllowed(nonCanaryUser);
      expect(result).toBe(false);
    });

    it("should return true if user's domain is NOT in canary list but team_management flag is true", async () => {
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return true;
        if (flagName === "team_management_canary_domains")
          return "another.com,example.com";
        return undefined;
      });
      const result = await teamsAllowed(nonCanaryUser);
      expect(result).toBe(true);
    });

    it("should skip canary check if team_management_canary_domains is empty and rely on team_management flag (true)", async () => {
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return true;
        if (flagName === "team_management_canary_domains") return "";
        return undefined;
      });
      const result = await teamsAllowed(canaryUser); // Email domain matches, but list is empty
      expect(result).toBe(true);
    });

    it("should skip canary check if team_management_canary_domains is undefined and rely on team_management flag (false)", async () => {
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return false;
        if (flagName === "team_management_canary_domains") return undefined;
        return undefined;
      });
      const result = await teamsAllowed(canaryUser); // Email domain matches, but list is undefined
      expect(result).toBe(false);
    });
  });

  describe("non-legacy self-serve team logic", () => {
    beforeEach(() => {
      // Set team_management flag to false to test the new logic
      vi.mocked(getFeatureFlag).mockImplementation(async (flagName: string) => {
        if (flagName === "team_management") return false;
        if (flagName === "team_management_canary_domains") return "";
        return false;
      });
    });

    it("should return true for users in non-legacy self-serve teams even when team_management flag is false", async () => {
      // Arrange - user is in a non-legacy self-serve team
      mockTenantWatcher.tenantFor.mockResolvedValue({
        config: {
          configs: {
            is_self_serve_team: "true",
            is_legacy_self_serve_team: "false",
          },
        },
      });

      // Act
      const result = await teamsAllowed(mockUser);

      // Assert
      expect(result).toBe(true);
      expect(mockTenantWatcher.tenantFor).toHaveBeenCalledWith(
        mockUser.tenantId,
      );
    });

    it("should return false for users in legacy self-serve teams even when team_management flag is false", async () => {
      // Arrange - user is in a legacy self-serve team
      mockTenantWatcher.tenantFor.mockResolvedValue({
        config: {
          configs: {
            is_self_serve_team: "true",
            is_legacy_self_serve_team: "true",
          },
        },
      });

      // Act
      const result = await teamsAllowed(mockUser);

      // Assert
      expect(result).toBe(false);
    });

    it("should return false for users not in self-serve teams", async () => {
      // Arrange - user is not in a self-serve team
      mockTenantWatcher.tenantFor.mockResolvedValue({
        config: {
          configs: {
            is_self_serve_team: "false",
          },
        },
      });

      // Act
      const result = await teamsAllowed(mockUser);

      // Assert
      expect(result).toBe(false);
    });

    it("should handle tenant watcher errors gracefully", async () => {
      // Arrange - tenant watcher throws an error
      mockTenantWatcher.tenantFor.mockRejectedValue(
        new Error("Tenant not found"),
      );

      // Act
      const result = await teamsAllowed(mockUser);

      // Assert
      expect(result).toBe(false);
    });
  });
});

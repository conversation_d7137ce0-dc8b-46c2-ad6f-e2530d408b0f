import { describe, it, expect } from "vitest";
import {
  has,
  isObjectish,
  hasValueOfType,
  hasShape,
  isString,
  isNumber,
  optional,
  assertGuard,
  GuardError,
} from "../guards";

describe("Type guards", () => {
  describe("has", () => {
    it("should return true for objects with the specified keys", () => {
      expect(has({ a: 1, b: 2 }, "a")).toBe(true);
      expect(has({ a: 1, b: 2 }, "a", "b")).toBe(true);
    });

    it("should return false for objects without the specified keys", () => {
      expect(has({ a: 1 }, "b")).toBe(false);
      expect(has({ a: 1 }, "a", "b")).toBe(false);
    });

    it("should return false for non-objects", () => {
      expect(has(null, "a")).toBe(false);
      expect(has(undefined, "a")).toBe(false);
      expect(has(42, "a")).toBe(false);
    });
  });

  describe("isObjectish", () => {
    it("should return true for objects and functions", () => {
      expect(isObjectish({})).toBe(true);
      expect(isObjectish(() => {})).toBe(true);
    });

    it("should return false for null, undefined, and primitives", () => {
      expect(isObjectish(null)).toBe(false);
      expect(isObjectish(undefined)).toBe(false);
      expect(isObjectish(42)).toBe(false);
      expect(isObjectish("string")).toBe(false);
    });
  });

  describe("hasValueOfType", () => {
    it("should return true when object has key with correct type", () => {
      expect(hasValueOfType({ a: "string" }, "a", isString)).toBe(true);
      expect(hasValueOfType({ a: 42 }, "a", isNumber)).toBe(true);
    });

    it("should return false when object does not have key or value is of wrong type", () => {
      expect(hasValueOfType({ a: "string" }, "b", isString)).toBe(false);
      expect(hasValueOfType({ a: "string" }, "a", isNumber)).toBe(false);
    });
  });

  describe("hasShape", () => {
    const personShape = hasShape({
      name: isString,
      age: isNumber,
      email: optional(isString),
    });

    it("should return true for objects matching the shape", () => {
      expect(personShape({ name: "John", age: 30 })).toBe(true);
      expect(
        personShape({ name: "Jane", age: 25, email: "<EMAIL>" }),
      ).toBe(true);
    });

    it("should return false for objects not matching the shape", () => {
      expect(personShape({ name: "John" })).toBe(false);
      expect(personShape({ name: "John", age: "30" })).toBe(true);
      expect(personShape({ name: "John", age: 30, email: 42 })).toBe(false);
    });
  });

  describe("isString and isNumber", () => {
    it("should correctly identify strings and numbers", () => {
      expect(isString("hello")).toBe(true);
      expect(isString(42)).toBe(false);
      expect(isNumber(42)).toBe(true);
      expect(isNumber("42")).toBe(true);
      expect(isNumber("hello")).toBe(false);
    });
  });

  describe("optional", () => {
    const optionalString = optional(isString);

    it("should allow null, undefined, or the specified type", () => {
      expect(optionalString("hello")).toBe(true);
      expect(optionalString(null)).toBe(true);
      expect(optionalString(undefined)).toBe(true);
      expect(optionalString(42)).toBe(false);
    });
  });

  describe("assertGuard", () => {
    const assertString = assertGuard(isString);

    it("should not throw for correct type", () => {
      expect(() => assertString("hello")).not.toThrow();
    });

    it("should throw GuardError for incorrect type", () => {
      expect(() => assertString(42)).toThrow(GuardError);
    });
  });
});

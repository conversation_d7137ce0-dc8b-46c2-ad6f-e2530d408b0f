import { describe, it, expect } from "vitest";
import { isPlainObject, isObject, assign, deepAssign } from "../object";

describe("object.ts", () => {
  describe("isPlainObject", () => {
    it("should return true for plain objects", () => {
      expect(isPlainObject({})).toBe(true);
      expect(isPlainObject({ a: 1 })).toBe(true);
      expect(isPlainObject(Object.create(null))).toBe(true);
    });

    it("should return false for non-plain objects", () => {
      expect(isPlainObject([])).toBe(false);
      expect(isPlainObject(null)).toBe(false);
      expect(isPlainObject(undefined)).toBe(false);
      expect(isPlainObject(1)).toBe(false);
      expect(isPlainObject("string")).toBe(false);
      expect(isPlainObject(new Date())).toBe(false);
      expect(isPlainObject(() => {})).toBe(false);
    });
  });

  describe("isObject", () => {
    it("should return true for objects", () => {
      expect(isObject({})).toBe(true);
      expect(isObject([])).toBe(true);
      expect(isObject(new Date())).toBe(true);
      expect(isObject(Object.create(null))).toBe(true);
    });

    it("should return false for non-objects", () => {
      expect(isObject(null)).toBe(false);
      expect(isObject(undefined)).toBe(false);
      expect(isObject(1)).toBe(false);
      expect(isObject("string")).toBe(false);
      expect(isObject(true)).toBe(false);
    });
  });

  describe("assign", () => {
    it("should merge source objects into target", () => {
      const target = { a: 1 };
      const source1 = { b: 2 };
      const source2 = { c: 3 };

      const result = assign(source1, source2)(target);

      expect(result).toEqual({ a: 1, b: 2, c: 3 });
      // Original object should not be modified
      expect(target).toEqual({ a: 1 });
    });

    it("should override properties from left to right", () => {
      const target = { a: 1, b: 1 };
      const source1 = { b: 2 };
      const source2 = { b: 3 };

      const result = assign(source1, source2)(target);

      expect(result).toEqual({ a: 1, b: 3 });
    });
  });

  describe("deepAssign", () => {
    it("should deeply merge source objects into target", () => {
      const target = { a: 1, b: { c: 2 } };
      const source = { b: { d: 3 }, e: 4 };

      const result = deepAssign(source)(target);

      expect(result).toEqual({ a: 1, b: { c: 2, d: 3 }, e: 4 });
    });

    it("should handle multiple sources", () => {
      const target = { a: 1, b: { c: 2 } };
      const source1 = { b: { d: 3 } };
      const source2 = { b: { e: 4 }, f: 5 };

      const result = deepAssign(source1, source2)(target);

      expect(result).toEqual({ a: 1, b: { c: 2, d: 3, e: 4 }, f: 5 });
    });

    it("should override primitive values but merge objects", () => {
      const target = { a: 1, b: { c: 2, d: 3 } };
      const source = { a: 10, b: { c: 20 } };

      const result = deepAssign(source)(target);

      expect(result).toEqual({ a: 10, b: { c: 20, d: 3 } });
    });

    it("should handle arrays as primitive values (replacing them)", () => {
      const target = { a: [1, 2], b: { c: [3, 4] } };
      const source = { a: [5, 6], b: { c: [7, 8] } };

      const result = deepAssign(source)(target);

      expect(result).toEqual({ a: [5, 6], b: { c: [7, 8] } });
    });
  });
});

import { describe, it, expect } from "vitest";
import { trim, split, toSplit, titleCase } from "../string";

describe("string utility functions", () => {
  describe("trim", () => {
    it("should trim whitespace from both ends of a string", () => {
      expect(trim("  hello  ")).toBe("hello");
      expect(trim("\t\nhello\n\t")).toBe("hello");
      expect(trim("hello")).toBe("hello");
      expect(trim("")).toBe("");
    });
  });

  describe("split", () => {
    it("should split a string by the given separator", () => {
      expect(split(",")(", a, b, c ")).toEqual(["", " a", " b", " c "]);
      expect(split(":")(":a:b:c:")).toEqual(["", "a", "b", "c", ""]);
      expect(split(" ")("hello world")).toEqual(["hello", "world"]);
    });

    it("should provide array methods on the splitter function", () => {
      const commaList = "a, b, c";
      expect(split(",").map((s) => s.trim())(commaList)).toEqual([
        "a",
        "b",
        "c",
      ]);

      const numbers = "1,2,3,4,5";
      expect(split(",").filter((n) => parseInt(n) > 2)(numbers)).toEqual([
        "3",
        "4",
        "5",
      ]);

      const words = "hello world goodbye";
      expect(split(" ").slice(1)(words)).toEqual(["world", "goodbye"]);
    });

    it("should handle chained array methods", () => {
      const data = "10,20,30,40,50";
      const result = split(",")
        .map((n) => parseInt(n))
        .filter((n) => n > 25)
        .map((n) => n * 2)(data);

      expect(result).toEqual([60, 80, 100]);
    });

    it("should work with the example from the documentation", () => {
      const trimFn = (s: string) => s.trim();
      const strArr = ["a:b", "c : d"];
      const result = strArr.map(split(":").map(trimFn));
      expect(result).toEqual([
        ["a", "b"],
        ["c", "d"],
      ]);
    });

    it("should be chainable and isolated", () => {
      const s = split(":");
      const t = s.map((v) => "-" + v);
      const u = s.filter((v) => v === "a");

      expect(t("1:1")).toEqual(["-1", "-1"]);
      expect(u("a:b")).toEqual(["a"]);
    });
  });

  describe("toSplit", () => {
    it("should split a string by the given separator", () => {
      expect(toSplit(",")(", a, b, c ")).toEqual(["", " a", " b", " c "]);
      expect(toSplit(":")(":a:b:c:")).toEqual(["", "a", "b", "c", ""]);
      expect(toSplit(" ")("hello world")).toEqual(["hello", "world"]);
    });

    it("should work with an array input", () => {
      expect(toSplit(",")(["a,b", "c,d"])).toEqual(["a,b", "c,d"]);
    });

    it("should provide array methods on the splitter function", () => {
      const commaList = "a, b, c";
      expect(toSplit(",").map((s) => s.trim())(commaList)).toEqual([
        "a",
        "b",
        "c",
      ]);

      const numbers = "1,2,3,4,5";
      expect(toSplit(",").filter((n) => parseInt(n) > 2)(numbers)).toEqual([
        "3",
        "4",
        "5",
      ]);

      const words = "hello world goodbye";
      expect(toSplit(" ").slice(1)(words)).toEqual(["world", "goodbye"]);
    });
    it("should handle chained array methods", () => {
      const data = "10,20,30,40,50";
      const result = toSplit(",")
        .map((n) => parseInt(n))
        .filter((n) => n > 25)
        .map((n) => n * 2)(data);

      expect(result).toEqual([60, 80, 100]);
    });
  });
});

describe("titleCase", () => {
  it("should capitalize the first letter of each word except short words (unless its the first letter of the string)", () => {
    expect(titleCase("hello world")).toBe("Hello World");
    expect(titleCase("the quick brown fox")).toBe("The Quick Brown Fox");
    expect(titleCase("jumps over the lazy dog")).toBe(
      "Jumps Over the Lazy Dog",
    );
  });

  it("should handle all lowercase input", () => {
    expect(titleCase("this is a test")).toBe("This is a Test");
  });

  it("should handle all uppercase input", () => {
    expect(titleCase("THIS IS A TEST")).toBe("This is a Test");
  });

  it("should handle mixed case input", () => {
    expect(titleCase("ThIs Is A tEsT")).toBe("This is a Test");
  });

  it("should handle single word input", () => {
    expect(titleCase("hello")).toBe("Hello");
    expect(titleCase("the")).toBe("The");
    expect(titleCase("a")).toBe("A");
  });

  it("should handle empty string", () => {
    expect(titleCase("")).toBe("");
  });

  it("should handle strings with multiple spaces", () => {
    expect(titleCase("hello  world")).toBe("Hello  World");
  });

  it("should not capitalize short words when they are not the first word", () => {
    const shortWords = [
      "a",
      "an",
      "the",
      "and",
      "but",
      "or",
      "for",
      "nor",
      "on",
      "at",
      "to",
      "from",
      "by",
      "of",
      "that",
    ];

    for (const word of shortWords) {
      expect(titleCase(word)).toBe(word[0].toUpperCase() + word.slice(1));
      expect(titleCase(`${word} test`)).toBe(
        `${word[0].toUpperCase() + word.slice(1)} Test`,
      );
      expect(titleCase(`test ${word}`)).toBe(`Test ${word}`);
    }
  });
});

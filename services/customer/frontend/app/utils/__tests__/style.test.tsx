import { describe, it, expect } from "vitest";
import { render } from "@testing-library/react";
import { withClassName, cn, typography } from "../style";
import { split, trim } from "../string";
import type { ReactElement } from "react";

describe("style utilities", () => {
  describe("withClassName", () => {
    it("should add a static className to a component", () => {
      const TestComponent = (props: { className?: string }) => (
        <div data-testid="test" className={props.className}>
          Test
        </div>
      );

      const EnhancedComponent = withClassName("added-class", TestComponent);
      const { getByTestId } = render(
        <EnhancedComponent className="existing-class" />,
      );

      const element = getByTestId("test");
      expect(element.className).toContain("added-class");
      expect(element.className).toContain("existing-class");
    });

    it("should handle function classNames with active=true", () => {
      function TestComponent(props: { className?: string; active?: boolean }) {
        return (
          <div data-testid="test" className={props.className}>
            Test
          </div>
        );
      }

      const dynamicClassName = (props: { active?: boolean }) =>
        props.active ? "active-class" : "inactive-class";

      const EnhancedComponent = withClassName(dynamicClassName, TestComponent);

      const { getByTestId } = render(
        <EnhancedComponent className="base-class" active={true} />,
      );

      const element = getByTestId("test");
      expect(element.className).toContain("active-class");
      expect(element.className).toContain("base-class");
    });

    it("should handle function classNames with active=false", () => {
      function TestComponent(props: { className?: string; active?: boolean }) {
        return (
          <div data-testid="test" className={props.className}>
            Test
          </div>
        );
      }

      const dynamicClassName = (props: { active?: boolean }) =>
        props.active ? "active-class" : "inactive-class";

      const EnhancedComponent = withClassName(dynamicClassName, TestComponent);

      const { getByTestId } = render(
        <EnhancedComponent className="base-class" active={false} />,
      );

      const element = getByTestId("test");
      expect(element.className).toContain("inactive-class");
      expect(element.className).toContain("base-class");
    });

    it("should handle undefined className with static withClassName", () => {
      function TestComponent(props: {
        className?: () => string;
        testId: string;
      }) {
        const { className, testId } = props;
        return (
          <div
            data-testid={testId}
            className={
              typeof className === "function" ? className() : className
            }
          >
            Test
          </div>
        );
      }

      const EnhancedComponent = withClassName("added-class", TestComponent);

      const { getByTestId } = render(
        <EnhancedComponent className={undefined} testId="test" />,
      );

      const element = getByTestId("test");
      expect(element.className).toContain("added-class");
    });

    it("should handle function className with static withClassName", () => {
      function TestComponent(props: {
        className?: () => string;
        testId: string;
      }) {
        const { className, testId } = props;
        return (
          <div
            data-testid={testId}
            className={
              typeof className === "function" ? className() : className
            }
          >
            Test
          </div>
        );
      }

      const EnhancedComponent = withClassName("added-class", TestComponent);

      const { getByTestId } = render(
        <EnhancedComponent className={() => "base-class"} testId="test" />,
      );

      const element = getByTestId("test");
      expect(element.className).toContain("added-class");
      expect(element.className).toContain("base-class");
    });

    it("should handle function className with function withClassName", () => {
      function TestComponent(props: {
        className?: () => string;
        testId: string;
      }) {
        const { className, testId } = props;
        return (
          <div
            data-testid={testId}
            className={
              typeof className === "function" ? className() : className
            }
          >
            Test
          </div>
        );
      }

      const EnhancedComponent = withClassName(
        () => "added-class",
        TestComponent,
      );

      const { getByTestId } = render(
        <EnhancedComponent className={() => "base-class"} testId="test" />,
      );

      const element = getByTestId("test");
      expect(element.className).toContain("added-class");
      expect(element.className).toContain("base-class");
    });

    it("should set a proper displayName", () => {
      const TestComponent = () => <div>Test</div>;
      TestComponent.displayName = "TestComponent";

      const EnhancedComponent = withClassName("test-class", TestComponent);
      expect(EnhancedComponent.displayName).toBe(
        "withClassName(test-class)(TestComponent)",
      );
    });

    it("should handle elements", () => {
      const el = (<div className="base-class">Test</div>) as ReactElement<{
        className?: string;
      }>;
      const enhancedEl = withClassName("added-class", el);
      expect(enhancedEl.props.className).toBe("base-class added-class");
    });
  });

  describe("cn", () => {
    it("should combine multiple class names", () => {
      expect(cn("class1", "class2", "class3")).toBe("class1 class2 class3");
    });

    it("should handle falsy values", () => {
      expect(cn("class1", "", undefined, null, "class2")).toBe("class1 class2");
    });
  });

  describe("typography", () => {
    it("should have the expected structure", () => {
      expect(Object.keys(typography)).toEqual([
        "text1",
        "text2",
        "text3",
        "text4",
        "text5",
        "text6",
        "text7",
        "text8",
        "text9",
      ]);

      for (const textKey of Object.keys(typography)) {
        const textStyleGroup = typography[textKey as keyof typeof typography];

        expect(Object.keys(textStyleGroup)).toEqual([
          "light",
          "regular",
          "medium",
          "bold",
        ]);
      }
    });

    it("should include the expected CSS properties in each typography style", () => {
      for (const textKey of Object.keys(typography)) {
        const textStyleGroup = typography[textKey as keyof typeof typography];

        for (const cssString of Object.values(textStyleGroup)) {
          const cssProperties = Object.fromEntries(
            cssString.split("\n").map(split(":").map(trim)),
          );
          expect(Object.keys(cssProperties)).toEqual([
            "font-size",
            "font-weight",
            "line-height",
            "letter-spacing",
            "color",
          ]);
          expect(cssProperties["font-size"]).toMatch(
            /var\(--ds-font-size-\d+\);/,
          );
          expect(cssProperties["font-weight"]).toMatch(
            /var\(--ds-font-weight-\w+\);/,
          );
          expect(cssProperties["line-height"]).toMatch(
            /var\(--ds-line-height-\d+\);/,
          );
          expect(cssProperties["letter-spacing"]).toMatch(
            /--ds-letter-spacing-\d+/,
          );
          expect(cssProperties["color"]).toBe("var(--ds-color-text-default);");
        }
      }
    });
  });
});

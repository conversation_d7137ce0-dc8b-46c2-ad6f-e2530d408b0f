import { describe, it, expect, vi } from "vitest";
import { composeFns, reduceFns, call, resolve, isFunction } from "../function";

describe("function utility functions", () => {
  describe("composeFns", () => {
    it("should call each function with the given arguments", () => {
      const fn1 = vi.fn();
      const fn2 = vi.fn();
      const fn3 = vi.fn();

      const composed = composeFns(fn1, fn2, fn3);
      composed("arg1", "arg2");

      expect(fn1).toHaveBeenCalledWith("arg1", "arg2");
      expect(fn2).toHaveBeenCalledWith("arg1", "arg2");
      expect(fn3).toHaveBeenCalledWith("arg1", "arg2");
    });

    it("should handle undefined functions", () => {
      const fn1 = vi.fn();
      const fn2 = undefined;
      const fn3 = vi.fn();

      const composed = composeFns(fn1, fn2, fn3);
      composed("arg");

      expect(fn1).toHaveBeenCalledWith("arg");
      expect(fn3).toHaveBeenCalledWith("arg");
      // fn2 is undefined, so it should be skipped without error
    });
  });

  describe("reduceFns", () => {
    it("should chain function calls", () => {
      const add1 = (x: number) => x + 1;
      const multiply2 = (x: number) => x * 2;
      const subtract3 = (x: number) => x - 3;

      const fn = reduceFns(add1, multiply2, subtract3);
      const result = fn(5);

      // (5 + 1) * 2 - 3 = 9
      expect(result).toBe(9);
    });

    it("should work with a single function", () => {
      const add1 = (x: number) => x + 1;
      const fn = reduceFns(add1);
      expect(fn(5)).toBe(6);
    });

    it("should handle complex types", () => {
      const addName = (obj: { value: number }) => ({ ...obj, name: "test" });
      const addFlag = (obj: { value: number; name: string }) => ({
        ...obj,
        flag: true,
      });

      const fn = reduceFns(addName, addFlag);
      const result = fn({ value: 42 });

      expect(result).toEqual({ value: 42, name: "test", flag: true });
    });
  });

  describe("call", () => {
    it("should call a function", () => {
      const fn = vi.fn();
      call(fn);
      expect(fn).toHaveBeenCalled();
    });

    it("should call a function with arguments using withArgs", () => {
      const fn = vi.fn();
      call.withArgs(1, "test", true)(fn);
      expect(fn).toHaveBeenCalledWith(1, "test", true);
    });

    it("should handle undefined with withArgs", () => {
      // This should not throw an error
      expect(() => call.withArgs(1, 2)(undefined)).not.toThrow();
    });
  });

  describe("resolve", () => {
    it("should invoke a function with parameters", () => {
      const fn = vi.fn((a: number, b: string) => a + b);
      const result = resolve(fn, 1, "2");
      expect(result).toBe("12");
      expect(fn).toHaveBeenCalledWith(1, "2");
    });

    it("should return the value directly if not a function", () => {
      const value = "test";
      const result = resolve(value, 1, 2);
      expect(result).toBe("test");
    });
  });

  describe("isFunction", () => {
    it("should return true for functions", () => {
      expect(isFunction(() => {})).toBe(true);
      expect(isFunction(function () {})).toBe(true);
      expect(isFunction(Math.random)).toBe(true);
    });

    it("should return false for non-functions", () => {
      expect(isFunction(null)).toBe(false);
      expect(isFunction(undefined)).toBe(false);
      expect(isFunction(42)).toBe(false);
      expect(isFunction("string")).toBe(false);
      expect(isFunction({})).toBe(false);
      expect(isFunction([])).toBe(false);
    });
  });
});

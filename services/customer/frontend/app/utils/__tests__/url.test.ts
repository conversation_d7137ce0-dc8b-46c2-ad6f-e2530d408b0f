import { describe, it, expect } from "vitest";
import { stringifyUrl, toFormData } from "../url";

describe("stringifyUrl", () => {
  it("should convert an empty object to an empty string", () => {
    expect(stringifyUrl({})).toBe("");
  });

  it("should convert a simple key-value object to a URL query string", () => {
    const params = {
      name: "<PERSON>",
      age: "30",
    };
    expect(stringifyUrl(params)).toBe("name=John&age=30");
  });

  it("should properly encode special characters", () => {
    const params = {
      query: "hello world",
      filter: "price>100",
    };
    expect(stringifyUrl(params)).toBe("query=hello%20world&filter=price%3E100");
  });

  it("should stringify and encode object values", () => {
    const params = {
      user: { name: "<PERSON>", age: 30 },
      filters: { minPrice: 100, maxPrice: 500 },
    };
    expect(stringifyUrl(params)).toBe(
      "user=%7B%22name%22%3A%22John%22%2C%22age%22%3A30%7D&filters=%7B%22minPrice%22%3A100%2C%22maxPrice%22%3A500%7D",
    );
  });

  it("should handle mixed string and object values", () => {
    const params = {
      query: "search term",
      filters: { category: "electronics", inStock: true },
    };
    expect(stringifyUrl(params)).toBe(
      "query=search%20term&filters=%7B%22category%22%3A%22electronics%22%2C%22inStock%22%3Atrue%7D",
    );
  });
});

describe("toFormData", () => {
  it("should convert an object to a FormData object", () => {
    const obj = {
      name: "John",
      age: "30",
    };
    const formData = toFormData(obj);
    expect(formData.get("name")).toBe("John");
    expect(formData.get("age")).toBe("30");
  });

  it("should handle Blob values", async () => {
    const text = ["Hello, world"];
    const blob = new Blob(text);
    const obj = {
      file: blob,
    };
    const formData = toFormData(obj);

    function readBlob(blob: FormDataEntryValue) {
      if (blob instanceof Blob) {
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.addEventListener("loadend", () => resolve(reader.result));
          reader.readAsText(blob);
        });
      }
      return blob;
    }
    expect(await readBlob(formData.get("file")!)).toBe(String(text));
  });
});

import { json } from "@remix-run/node";
import { parse } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { logger } from "@augment-internal/logging";
import { createContextValues, type CallOptions } from "@connectrpc/connect";
import { hasShape, isNumber } from "./guards";
import {
  DEFAULT_TIMEZONE,
  type YearMonthDay,
  convertDateToYearMonthDay,
} from "./date";
import type { SessionUser } from "../.server/auth";
import type { DateFilters } from "~services/request_insight/analytics/request_insight_analytics_pb";
import {
  UserId,
  UserId_UserIdType,
} from "~services/auth/central/server/auth_entities_pb";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import { cachingClient as RIAClient } from "../.server/grpc/request-insights-analytics";
import {
  SHARD_KEY,
  TENANT_ID_KEY,
} from "../.server/grpc/tenant-shard-aware-transport";

const errorShape = hasShape({
  status: isNumber,
});
function validateDate(date: string) {
  try {
    const yearMonthDay: YearMonthDay = JSON.parse(date);
    parse(
      `${yearMonthDay.year}-${yearMonthDay.month}-${yearMonthDay.day}`,
      "yyyy-MM-dd",
      new Date(),
    );
  } catch (e) {
    throw json(
      `Invalid date: ${date}. The format should be {year: number, month: number, day: number}`,
      { status: 400 },
    );
  }
}

export function getDateFilters(request: Request): DateFilters {
  const url = new URL(request.url);
  const startDate = url.searchParams.get("startDate");
  const endDate = url.searchParams.get("endDate");

  if (!startDate || !endDate) {
    throw json(
      { message: "startDate and endDate are required" },
      {
        status: 400,
        statusText: "Bad Request",
      },
    );
  }

  validateDate(startDate);
  validateDate(endDate);

  const dateFilters = {
    startDate: JSON.parse(startDate),
    endDate: JSON.parse(endDate),
  } as DateFilters;

  return dateFilters;
}

export async function getEarliestData(user: SessionUser, tenantId: string) {
  const { earliestRequestTimestamp } = await getAuthenticatedApiData(
    user,
    { tenantId },
    RIAClient.getEarliestRequestTimestamp.bind(RIAClient),
  );

  // return today's date if there are no requests
  const earliestDataDateUTC = earliestRequestTimestamp?.toDate() ?? new Date();
  const earliestDataDateZoned = toZonedTime(
    earliestDataDateUTC,
    DEFAULT_TIMEZONE,
  );
  const earliestDataYearMonthDay = convertDateToYearMonthDay(
    earliestDataDateZoned,
  );

  return earliestDataYearMonthDay;
}

export async function getAuthenticatedApiData<T, R>(
  user: SessionUser,
  request: R,
  apiMethod: (request: R, options?: CallOptions) => Promise<T>,
) {
  async function fetchToken() {
    try {
      const { signedToken } =
        await getTokenExchangeClient().getSignedTokenForUser({
          userId: user.userId,
          opaqueUserId: new UserId({
            userId: user.userId,
            userIdType: UserId_UserIdType.AUGMENT,
          }),
          userEmail: user.email,
          tenantId: user.tenantId,
          shardNamespace: user.shardNamespace,
        });
      return signedToken;
    } catch (e) {
      logger.warn(`Failed to get signed token for service: ${e}`);
      throw json(
        { message: "Internal Server Error" },
        {
          status: 500,
          statusText: "Internal Server Error",
        },
      );
    }
  }

  async function callApiMethod(signedToken: string, user: SessionUser) {
    try {
      return await apiMethod(request, {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
        //These are needed by some transports to determine which region to access.
        contextValues: createContextValues()
          .set(TENANT_ID_KEY, user.tenantId)
          .set(SHARD_KEY, user.shardNamespace),
      });
    } catch (e) {
      logger.warn(`Failed to get data: ${e}`);
      throw json(
        { message: "Internal Server Error" },
        {
          status: 500,
          statusText: "Internal Server Error",
        },
      );
    }
  }

  const maxAttempts = 2;
  let data: T | null = null;

  for (let attempts = 0; attempts < maxAttempts; attempts++) {
    try {
      const signedToken = await fetchToken();
      data = await callApiMethod(signedToken, user);
      break;
    } catch (e) {
      if (errorShape(e) && e.status === 401) {
        // unauthorized, retry with a new token
        console.error(`Unauthorized, retrying with a new token: ${e}`);
      } else {
        console.error(`unknown error: ${e}`);
      }
    }
  }

  if (data === null) {
    throw json(
      { message: "Internal Server Error" },
      {
        status: 500,
        statusText: "Internal Server Error",
      },
    );
  }

  return data;
}

/* eslint-disable @typescript-eslint/no-explicit-any */
export function cacheableJson(data: any, init?: ResponseInit) {
  return json(data, {
    ...init,
    headers: {
      ...init?.headers,
      "Cache-Control": "private, max-age=600",
    },
  });
}

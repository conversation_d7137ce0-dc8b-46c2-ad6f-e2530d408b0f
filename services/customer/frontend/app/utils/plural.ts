/**
 * Pluralizes a word based on the count and includes the count in the result.
 * There are two ways to use this function:
 * @description 1. With a count: pluralizeWithCount(count, singular, plural)
 * @example
 * pluralizeWithCount(1, "apple", "apples") // "1 apple"
 * pluralizeWithCount(2, "apple", "apples") // "2 apples"
 *
 * @description 2. Without a count: pluralizeWithCount(singular, plural) returns a function that takes a count
 * @example
 * const appleP = pluralizeWithCount("apple", "apples")
 * appleP(1) // "1 apple"
 * appleP(2) // "2 apples"
 *
 * @param count
 * @param singular
 * @param plural
 */
export function pluralizeWithCount(
  count: number,
  singular: string,
  plural: string,
): string;
export function pluralizeWithCount(
  singular: string,
  plural: string,
): {
  (count: number): string;
};
export function pluralizeWithCount(
  ...props:
    | [count: number, singular: string, plural: string]
    | [singular: string, plural: string]
): string | { (count: number): string } {
  if (props.length === 3) {
    const [count, singular, plural] = props;
    return `${count} ${pluralize(count, singular, plural)}`;
  }
  const [singular, plural] = props;
  return function stringCollector(count: number) {
    return pluralizeWithCount(count, singular, plural);
  };
}

/**
 * Pluralizes a word based on the count.
 *
 * There are two ways to use this function:
 * @description 1. With a count: pluralize(count, singular, plural)
 * @example
 * pluralize(1, "apple", "apples") // "apple"
 * pluralize(2, "apple", "apples") // "apples"
 *
 * @description 2. Without a count: pluralize(singular, plural) returns a function that takes a count
 * @example
 * const appleP = pluralize("apple", "apples")
 * appleP(1) // "apple"
 * appleP(2) // "apples"
 *
 * @param count
 * @param singular
 * @param plural
 */
export function pluralize(
  count: number,
  singular: string,
  plural: string,
): string;
export function pluralize(
  singular: string,
  plural: string,
): {
  (count: number): string;
};
export function pluralize(
  ...props:
    | [count: number, singular: string, plural: string]
    | [singular: string, plural: string]
): string | { (count: number): string } {
  if (props.length === 3) {
    const [count, singular, plural] = props;
    return `${count === 1 ? singular : plural}`;
  }
  const [singular, plural] = props;
  return function stringCollector(count: number) {
    return pluralize(count, singular, plural);
  };
}

export const pc = pluralizeWithCount;
export const p = pluralize;

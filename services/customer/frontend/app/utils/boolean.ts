import { call } from "./function";
import { isNonNullable } from "./guards";

/**
 * Returns a function that negates the result of the given function.
 * @param fn - The function to negate.
 * @returns A function that negates the result of the given function.
 * @example
 * const isString = (letterOrNumber: string | number) => typeof letterOrNumber === "string";
 * const isNotString = not(isString);
 * isNotString('a'); // false
 * isNotString(1); // true
 */
export function not<T>(fn: (v: T) => boolean) {
  return (v: T) => !fn(v);
}

export function and<T>(...fns: (((v: T) => boolean) | undefined)[]) {
  return (v: T) => fns.filter(isNonNullable).every(call.withArgs(v));
}

export function or<T>(...fns: (((v: T) => boolean) | undefined)[]) {
  return (v: T) => fns.filter(isNonNullable).some(call.withArgs(v));
}

export function isFalse(v: any): v is false {
  return v === false;
}

export function isTrue(v: any): v is true {
  return v === true;
}

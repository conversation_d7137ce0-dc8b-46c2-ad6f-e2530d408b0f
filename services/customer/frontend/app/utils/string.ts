/** Trims a string. */
export function trim(str: string) {
  return str.trim();
}

/**
 * Creates a splitter function enhanced with dynamically bound, chainable array methods.
 * @param separator - The string used to split the input.
 * @returns A function that splits a string, enhanced with chainable array methods.
 * @example
 * const trim = (s: string) => s.trim();
 * const strArr = ['a:b', 'c : d'];
 * const result = strArr.map(split(":").map(trim));
 * // result is [['a', 'b'], ['c', 'd']]
 */
export function split(separator: string) {
  return makeSplitter((str: string) => str.split(separator));
}

/**
 * Creates a splitter function that accepts a string or an array of strings. If a string is passed, it is split.
 * @param separator - The string used to split the input.
 * @returns A function that splits a string, enhanced with chainable array methods.
 * @example
 * toSplit("")("abc") === toSplit("")(['a', 'b', 'c']) === ['a','b','c']
 */
export function toSplit(separator: string) {
  return makeSplitter((strOrArray: string | string[]) =>
    typeof strOrArray === "string" ? strOrArray.split(separator) : strOrArray,
  );
}

/**
 * splitter creator factory
 * @param splitFn - The function that splits the input.
 * @returns A function that splits a string, enhanced with chainable array methods.
 */
function makeSplitter<S, T>(splitFn: (str: S) => T[]): Splitter<S, T> {
  function addArrayMethods(targetFn: (str: S) => T[]): Splitter<S, T> {
    const enhancedFn = targetFn as Splitter<S, T>;
    type ArrayKeys = (keyof Array<T> | "constructor")[];
    const keys = Object.getOwnPropertyNames(Array.prototype) as ArrayKeys;
    for (const prop of keys) {
      if (
        typeof Array.prototype[prop] === "function" &&
        prop !== "constructor"
      ) {
        (enhancedFn as any)[prop] = (...args: unknown[]) => {
          function composedFn(str: S) {
            const result = targetFn(str);
            return Array.prototype[prop].apply(result, args);
          }
          return addArrayMethods(composedFn);
        };
      }
    }
    return enhancedFn;
  }
  return addArrayMethods(splitFn);
}

/**
 * Our chainable function type.  We have a call signature `(str: string) => T[]`
 * plus "methods" that correspond to Array<T>, but with special type transitions:
 *
 * - `map` or `flatMap`: change T to U (Splitter<U>)
 * - all other methods: keep T
 */
type Splitter<S, T = string> = { (str: S): T[] } & {
  [P in Exclude<keyof Array<T>, "constructor">]: Array<T>[P] extends (
    ...args: infer A
  ) => any
    ? // If method is 'map' or 'flatMap', we transform T => U
      P extends "map" | "flatMap"
      ? <U>(
          ...params: A extends [
            (value: T, index: number, array: T[]) => any,
            ...infer Rest,
          ]
            ? [(value: T, index: number, array: T[]) => U, ...Rest]
            : A
        ) => Splitter<S, U>
      : (...args: A) => Splitter<S, T>
    : never;
};

type Capitalize<T extends string> = T extends `${infer First}${infer Rest}`
  ? `${Uppercase<First>}${Rest}`
  : T;

export function capitalize<T extends string>(str: T): Capitalize<T> {
  return (str.charAt(0).toUpperCase() + str.slice(1)) as Capitalize<T>;
}

export function alphabeticalSort(a: string, b: string) {
  return a.localeCompare(b);
}

type CamelToKebab<S extends string> = S extends `${infer Head}${infer Tail}`
  ? Head extends Lowercase<Head>
    ? `${Head}${CamelToKebab<Tail>}`
    : `-${Lowercase<Head>}${CamelToKebab<Tail>}`
  : S;

type KebabToCamel<S extends string> = S extends `${infer Head}-${infer Tail}`
  ? `${Head}${Capitalize<KebabToCamel<Tail>>}`
  : S;

export function camelToKebab<const T extends string>(str: T) {
  return str
    .replace(/([a-z0-9])([A-Z])/g, "$1-$2")
    .toLowerCase() as CamelToKebab<T>;
}

export function kababToCamel<const T extends string>(str: T) {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase()) as KebabToCamel<T>;
}

const shortWords: { [k: string]: true } = {
  a: true,
  an: true,
  the: true,
  and: true,
  but: true,
  or: true,
  for: true,
  nor: true,
  on: true,
  at: true,
  to: true,
  from: true,
  by: true,
  of: true,
  in: true,
  is: true,
  that: true,
} as const;

/**
 * Changes a string into Title Case
 * e.g. THE qUICK Brown FOX jumps over THE lazy DOG
 * becomes The Quick Brown Fox Jumps Over the Lazy Dog
 * @param str
 * @returns title cased version of the string
 */
export function titleCase(str: string): string {
  return str
    .toLowerCase()
    .split(" ")
    .map((word, index) =>
      shortWords[word] && index !== 0 ? word : capitalize(word),
    )
    .join(" ");
}

export function formatMoney(amount: number) {
  return new Intl.NumberFormat(undefined, {
    style: "currency",
    currency: "USD",
  }).format(amount);
}

export function upperCase<T extends string>(str: T) {
  return str.toUpperCase() as Uppercase<T>;
}

export function lowerCase<T extends string>(str: T) {
  return str.toLowerCase() as Lowercase<T>;
}

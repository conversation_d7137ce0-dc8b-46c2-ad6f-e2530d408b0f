import { create, type UseBoundStore, type Store<PERSON><PERSON> } from "zustand";

// Define allowed dashboard names and filter keys
export type DashboardName =
  | "Overview"
  | "CodeChanges"
  | "Adoption"
  | "YourUsage";
export type FilterKey =
  | "glanceFilter"
  | "overviewFilter"
  | "instructionsFilter"
  | "completionsFilter"
  | "chatFilter"
  | "developerActivityFilter"
  | "codeAugmentsFilter"
  | "yourUsageFilter";

interface FilterState {
  value: string;
  set: (value: string) => void;
}

/**
 * FilterStoreManager is responsible for managing filter stores across different dashboards.
 * It uses Zustand for in-memory state management and provides type-safe access to filter stores.
 *
 * Key features:
 * - Typed dashboard names and filter keys for compile-time safety
 * - Lazy initialization of stores
 * - Centralized management of all filter stores
 */
class FilterStoreManager {
  private stores: Map<string, UseBoundStore<StoreApi<FilterState>>> = new Map();

  private getStoreKey(
    dashboardName: DashboardName,
    filterKey: FilterKey,
  ): string {
    return `${dashboardName}-${filterKey}`;
  }

  getStore(
    dashboardName: DashboardName,
    filterKey: FilterKey,
    initialValue: string,
  ): UseBoundStore<StoreApi<FilterState>> {
    const key = this.getStoreKey(dashboardName, filterKey);

    if (!this.stores.has(key)) {
      const store = create<FilterState>()((set) => ({
        value: initialValue,
        set: (value: string) => {
          set((state) => ({
            ...state,
            value,
          }));
        },
      }));
      this.stores.set(key, store);
    }

    return this.stores.get(key)!;
  }
}

export const filterStoreManager = new FilterStoreManager();

export function useFilterStore(
  dashboardName: DashboardName,
  filterKey: FilterKey,
  initialValue: string,
): UseBoundStore<StoreApi<FilterState>> {
  return filterStoreManager.getStore(dashboardName, filterKey, initialValue);
}

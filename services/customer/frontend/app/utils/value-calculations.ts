import type { YearMonthDay } from "./date";

import type { RequestInsightAnalyticsResponse } from "../.server/grpc/request-insights-analytics";

export function sumByProperty<T>(
  data: T[],
  property: keyof T,
  filter?: (item: T) => boolean,
) {
  return data
    .filter(filter ?? (() => true))
    .reduce((acc, curr) => acc + (curr[property] as number), 0);
}

export function combineSums<T>(
  leftData: T[],
  leftProperty: keyof T,
  rightData: T[],
  rightProperty: keyof T,
  filter?: (item: T) => boolean,
) {
  return (
    sumByProperty(leftData, leftProperty, filter) +
    sumByProperty(rightData, rightProperty, filter)
  );
}

export function avgPropertyByDay<T>(
  data: T[],
  property: keyof T,
  filter: (item: T) => boolean,
) {
  const filteredData = data.filter(filter);
  return sumByProperty(filteredData, property) / filteredData.length;
}

export function calculateRate<T>(
  data: T[],
  numeratorProperty: keyof T,
  denominatorProperty: keyof T,
  filter: (item: T) => boolean,
) {
  const filteredData = data.filter(filter);
  const numeratorSum = sumByProperty(filteredData, numeratorProperty);
  const denominatorSum = sumByProperty(filteredData, denominatorProperty);
  const result = (numeratorSum / denominatorSum) * 100;
  return isNaN(result) ? 0 : result;
}

export function getDateValueList<T extends RequestInsightAnalyticsResponse>(
  data: T[],
  valueProperty: keyof T,
  filter: (item: T) => boolean,
) {
  return data.filter(filter).map((item) => {
    let date: YearMonthDay | undefined;
    if ("aggregation" in item) {
      const aggregation = item.aggregation as {
        case: string;
        value: YearMonthDay;
      };
      if ("case" in aggregation && aggregation.case === "date") {
        date = aggregation.value;
      }
    } else if ("date" in item && item.date !== undefined) {
      date = item.date as YearMonthDay;
    }

    if (
      date === undefined ||
      date.year === undefined ||
      date.month === undefined ||
      date.day === undefined
    ) {
      throw new Error(`Invalid date on item: ${JSON.stringify(item)}`);
    }
    if (item[valueProperty] === undefined) {
      throw new Error(`Value not found on item: ${JSON.stringify(item)}`);
    }

    const { year, month, day } = date;
    return {
      date: `${year}-${month}-${day}`,
      value: item[valueProperty] as number,
    };
  });
}

export function sumDateValueLists<T extends RequestInsightAnalyticsResponse>(
  leftList: T[],
  leftProperty: keyof T,
  rightList: T[],
  rightProperty: keyof T,
  filter: (item: T) => boolean,
) {
  const left = getDateValueList(leftList, leftProperty, filter);
  const right = getDateValueList(rightList, rightProperty, filter);

  return left.map((item, index) => {
    if (item.date !== right[index].date) {
      throw new Error(`Date mismatch at index ${index}`);
    }
    return {
      date: item.date,
      value: item.value + (right[index].value as number),
    };
  });
}

export function getTopList<T extends RequestInsightAnalyticsResponse>(
  data: T[],
  labelProperty: keyof T,
  valueProperty: keyof T,
  topN = 10,
) {
  return data
    .sort((a, b) => (b[valueProperty] as number) - (a[valueProperty] as number))
    .slice(0, topN)
    .map((item) => ({
      label: item[labelProperty] as string,
      value: item[valueProperty] as number,
    }));
}

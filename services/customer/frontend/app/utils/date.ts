import { useEffect, useMemo } from "react";
import { useSearchParams } from "@remix-run/react";

import { isBefore, sub } from "date-fns";
import { toZonedTime } from "date-fns-tz";
import { type DashboardName, type FilterKey, useFilterStore } from "./filters";

export type YearMonthDay = {
  year: number;
  month: number;
  day: number;
};

export enum TimeFrame {
  Last7Days = "7",
  Last30Days = "30",
  Last60Days = "60",
  Last90Days = "90",
  AllTime = "all",
}

export const DEFAULT_TIMEZONE = "America/Los_Angeles";

export const convertDateToYearMonthDay = (date: Date): YearMonthDay => ({
  year: date.getFullYear(),
  month: date.getMonth() + 1,
  day: date.getDate(),
});

export function useDateFilter(
  earliestData: YearMonthDay,
  dashboardName: DashboardName,
  filterName: FilterKey,
) {
  const nowZoned = toZonedTime(new Date(), DEFAULT_TIMEZONE);
  const [searchParams] = useSearchParams();

  let initialTimeFrame = TimeFrame.AllTime;
  const thirtyDaysAgo = sub(nowZoned, { days: 30 });
  const earliestDataDate = new Date(
    earliestData.year,
    earliestData.month - 1,
    earliestData.day,
  );
  if (isBefore(earliestDataDate, thirtyDaysAgo)) {
    initialTimeFrame = TimeFrame.Last30Days;
  }

  const initialFilterValue = searchParams.get(filterName) ?? initialTimeFrame;
  const filterStore = useFilterStore(
    dashboardName,
    filterName,
    initialFilterValue,
  );
  const { queryValue, setQueryValue } = filterStore((state) => ({
    queryValue: state.value,
    setQueryValue: state.set,
  }));

  useUpdateQueryStringValueWithoutNavigation(filterName, queryValue);

  const filterDate = useMemo(() => {
    if (queryValue === TimeFrame.AllTime) {
      return earliestData;
    }

    const daysToSubtract = parseInt(queryValue, 10);
    const subtractedDate = sub(nowZoned, { days: daysToSubtract });
    return convertDateToYearMonthDay(subtractedDate);
  }, [queryValue, earliestData]);

  const todayDate = useMemo(() => {
    return convertDateToYearMonthDay(nowZoned);
  }, [nowZoned.getFullYear(), nowZoned.getMonth(), nowZoned.getDate()]);

  return [queryValue, setQueryValue, filterDate, todayDate] as const;
}

// taken from https://github.com/kentcdodds/kentcdodds.com/blob/main/app/utils/misc.tsx
function useUpdateQueryStringValueWithoutNavigation(
  queryKey: string,
  queryValue: string,
) {
  useEffect(() => {
    const currentSearchParams = new URLSearchParams(window.location.search);
    const oldQuery = currentSearchParams.get(queryKey) ?? "";
    if (queryValue === oldQuery) return;

    if (queryValue) {
      currentSearchParams.set(queryKey, queryValue);
    } else {
      currentSearchParams.delete(queryKey);
    }
    const newUrl = [window.location.pathname, currentSearchParams.toString()]
      .filter(Boolean)
      .join("?");
    // alright, let's talk about this...
    // Normally with remix, you'd update the params via useSearchParams from react-router-dom
    // and updating the search params will trigger the search to update for you.
    // However, it also triggers a navigation to the new url, which will trigger
    // the loader to run which we do not want because all our data is already
    // on the client and we're just doing client-side filtering of data we
    // already have. So we manually call `window.history.replaceState` to avoid
    // the router from triggering the loader.
    window.history.replaceState(null, "", newUrl);
  }, [queryKey, queryValue]);
}

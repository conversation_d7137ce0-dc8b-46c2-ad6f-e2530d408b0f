import { isNonNullable } from "./guards";
import type { Nullish } from "@augment-internal/ts-utils/type";

export function toArray<T>(x: T | T[]): T[] {
  return Array.isArray(x) ? x : [x];
}

export function toSet<T>(x: T | T[]): Set<T> {
  if (x instanceof Set) return x;
  return new Set(toArray(x));
}

export function unique<T>(x: T[]): T[] {
  return [...new Set(x)];
}

/**
 * Creates a function that can be used to sort an array.
 * @param compareFn - The function to use to compare two items.
 * @returns A function that can be used to sort an array.
 * @example
 * const sortByAge = sortBy((a, b) => a.age - b.age);
 * const people = [
 *  { name: "<PERSON>", age: 30 },
 *  { name: "<PERSON>", age: 20 },
 *  { name: "<PERSON>", age: 40 },
 * ];
 * const sortedPeople = sortByAge(people);
 * console.info(sortedPeople); // [{ name: "<PERSON>", age: 20 }, { name: "<PERSON>", age: 30 }, { name: "<PERSON>", age: 40 }]
 */
export function sortBy(compareFn: (a: any, b: any) => number) {
  return <T extends any[]>(array: T): T => array.sort(compareFn);
}

/**
 * Collects all non-null and non-undefined values from the given individuals orcollections.
 * @param individualsOrCollections
 * @returns
 */
export function collect<T>(
  ...individualsOrCollections: (T | (T | Nullish)[] | Nullish)[]
): NonNullable<T>[] {
  return individualsOrCollections
    .map(toArray)
    .flat()
    .filter(isNonNullable) as NonNullable<T>[];
}

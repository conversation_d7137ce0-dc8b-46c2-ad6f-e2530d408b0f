import {
  createElement,
  forwardRef,
  Children,
  type ComponentProps,
  type ElementRef,
  type ForwardRefExoticComponent,
  type ComponentType,
  type ReactElement,
  isValidElement,
  cloneElement,
} from "react";
import { type ClassValue, clsx } from "clsx";
import { dedent as d } from "ts-dedent";
import { extendTailwindMerge } from "tailwind-merge";
import type {
  Increment,
  Range,
  Decrement,
} from "@augment-internal/ts-utils/type";
import { call } from "./function";

type FunctionClassName<T extends Record<string, unknown>> = (
  props: T,
) => string;

function composeClassNames<T extends Record<string, unknown>>(
  hocClassName: string | FunctionClassName<T>,
  props: T,
): string {
  const stringClassNames = [] as string[];
  const functionClassNames = [] as FunctionClassName<any>[];

  const { className } = props as {
    className?: string | FunctionClassName<T>;
  };

  if (typeof className === "function") {
    functionClassNames.push(className);
  } else if (typeof className === "string") {
    stringClassNames.push(className);
  }

  if (typeof hocClassName === "function") {
    functionClassNames.push(hocClassName);
  } else {
    stringClassNames.push(hocClassName);
  }

  if (functionClassNames.length > 0) {
    return clsx(stringClassNames, functionClassNames.map(call.withArgs(props)));
  }
  return clsx(stringClassNames);
}

type ClassNameArg<P> = string | ((props: P) => string);

/** Binds a className to a component or element */
export function withClassName<
  T extends ComponentType<any> | ForwardRefExoticComponent<any>,
>(hocClassName: ClassNameArg<ComponentProps<T>>, Component: T): T;
export function withClassName<T extends ReactElement>(
  hocClassName: ClassNameArg<T["props"]>,
  Component: T,
): T;
export function withClassName(hocClassName: any, ComponentOrElement: any) {
  if (isValidElement(ComponentOrElement)) {
    const el = ComponentOrElement;
    return cloneElement(el, {
      ...(el.props as any),
      className: composeClassNames(hocClassName, el.props as any),
    });
  }
  const wrapped = forwardRef<ElementRef<any>, ComponentProps<any>>(
    (props, ref) =>
      createElement(
        ComponentOrElement,
        { ...props, className: composeClassNames(hocClassName, props), ref },
        ...Children.toArray(props.children),
      ),
  );
  wrapped.displayName = `withClassName(${hocClassName})(${ComponentOrElement.displayName ?? ComponentOrElement.name ?? "Component"})`;
  return wrapped;
}

const twMerge = extendTailwindMerge({
  override: { classGroups: {} },
});

/**
 * Combines multiple class names into a single string.
 * @example
 * cn("a", "b", { c: true }, true && 'd', ['e'], 0, false, '', null, [0, false, 0, null]) // "a b c d e"
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

function makeTypography() {
  const fontWeights = ["light", "regular", "medium", "bold"] as const;
  type FontWeight = {
    [k in (typeof fontWeights)[number]]: `--ds-font-weight-${k}`;
  };
  const fontWeight = Object.fromEntries(
    fontWeights.map((weight) => [weight, `--ds-font-weight-${weight}`]),
  ) as FontWeight;

  const fontSize = Array.from(
    { length: 9 },
    (_, i) => `--ds-font-size-${i + 1}`,
  ) as unknown as {
    [k in Range<0, 8>]: `--ds-font-size-${Increment<k>}`;
  } & { length: 9 };

  const lineHeight = Array.from(
    { length: 9 },
    (_, i) => `--ds-line-height-${i + 1}`,
  ) as unknown as {
    [k in Range<0, 8>]: `--ds-line-height-${Increment<k>}`;
  } & { length: 9 };

  const letterSpacing = Array.from(
    { length: 6 },
    (_, i) => `--ds-letter-spacing-${i + 1}`,
  ) as unknown as {
    [k in Range<0, 5>]: `--ds-letter-spacing-${Increment<k>}`;
  } & { length: 6 };

  const lastEntry = Math.max(
    fontSize.length,
    lineHeight.length,
    letterSpacing.length,
  );

  function atOrLast(
    arr: { length: number } & { [k: number]: string },
    i: number,
  ) {
    if (i >= arr.length) {
      return arr[arr.length - 1];
    }
    return arr[i];
  }

  type AtOrLast<
    T extends { length: number } & { [k: number]: string },
    I extends number,
  > = I extends keyof T ? T[I] : T[Decrement<T["length"]>];

  type Typography = {
    [K in Range<0, 8> as `text${Increment<K>}`]: {
      [FW in keyof typeof fontWeight]: `font-size: var(${AtOrLast<typeof fontSize, K>});
font-weight: var(${(typeof fontWeight)[FW]});
line-height: var(${AtOrLast<typeof lineHeight, K>});
letter-spacing: var(${AtOrLast<typeof letterSpacing, K>});
color: var(--ds-text-default);`;
    };
  };

  const typography = {} as Typography;
  for (let i = 0; i < lastEntry; i++) {
    typography[`text${i + 1}` as keyof Typography] = Object.fromEntries(
      Object.entries(fontWeight).map(([fontWeightKey, fontWeightValue]) => [
        fontWeightKey,
        d`font-size: var(${atOrLast(fontSize, i)});
        font-weight: var(${fontWeightValue});
        line-height: var(${atOrLast(lineHeight, i)});
        letter-spacing: var(${atOrLast(letterSpacing, i)});
        color: var(--ds-color-text-default);`,
      ]),
    ) as any;
  }
  return typography;
}

export const typography = makeTypography();

import { create, type StoreA<PERSON>, type UseBoundStore } from "zustand";
import type { YearMonthDay } from "./date";

export type State = {
  earliestData: YearMonthDay;
};

type Store = UseBoundStore<StoreApi<State>>;

let appStore: Store | null = null;

export function initAppStore(initialEarliestData: YearMonthDay): void {
  if (appStore) {
    // App Store is already initialized. Initialization will be ignored.
    return;
  }

  appStore = create<State>(() => ({
    earliestData: initialEarliestData,
  }));
}

export function useAppStore(): Store;
export function useAppStore<T>(selector: (state: State) => T): T;
export function useAppStore<T>(selector?: (state: State) => T) {
  if (!appStore) {
    throw new Error("App Store is not initialized. Call initAppStore first.");
  }
  return selector ? appStore(selector) : appStore;
}

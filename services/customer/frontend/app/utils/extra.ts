import { thenableCallback } from "@augment-internal/ts-utils/promise";

type EventMapFor<T extends EventTarget> =
  // Window -------------------------------------------------
  T extends Window
    ? WindowEventMap
    : // Document -----------------------------------------------
      T extends Document
      ? DocumentEventMap
      : // HTML elements ------------------------------------------
        T extends HTMLElement
        ? HTMLElementEventMap
        : // Other DOM elements (incl. SVGElement, MathMLElement, …)-
          T extends Element
          ? ElementEventMap
          : // Fallback for custom EventTargets -----------------------
            Record<string, Event>;

export function addEventListener<
  T extends EventTarget,
  K extends keyof EventMapFor<T> & string,
>(
  target: T,
  type: K,
  listener: (this: T, ev: EventMapFor<T>[K]) => any,
  options?: boolean | AddEventListenerOptions,
): () => void {
  target.addEventListener(type, listener as any, options);
  return thenableCallback(() => {
    target.removeEventListener(type, listener as any, options);
  });
}

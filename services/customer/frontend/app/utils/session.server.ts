import { logger } from "@augment-internal/logging";
import { getSession, commitSession } from "../.server/session";
import type { SessionUser } from "../.server/auth";
import { TenantWatcherCachingClient } from "../.server/grpc/tenant-watcher";
import { AuthCentralClient } from "app/.server/grpc/auth-central";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";

/**
 * Updates the session with a new tenant ID if it changed.
 * Returns headers with Set-Cookie if the session was updated.
 */
export async function updateSessionWithTenantAndRoles(
  request: Request,
  user: SessionUser,
  tenantId: string,
): Promise<{
  headers: HeadersInit;
}> {
  const session = await getSession(request.headers.get("cookie"));
  const headers: HeadersInit = {};

  // Update the session with a new tenant ID if it changed
  if (tenantId !== user.tenantId) {
    // Get tenant details from TenantWatcher
    const tenantWatcher = TenantWatcherCachingClient.getInstance();
    const tenant = await tenantWatcher.tenantFor(tenantId);
    const tenantName = tenant?.name;
    const shardNamespace = tenant?.shardNamespace;

    logger.info(
      `Updating session with new tenant ID: ${tenantId} (was: ${user.tenantId})` +
        (tenantName ? `, name: ${tenantName}` : ""),
    );

    if (!tenantName || !shardNamespace) {
      throw new Error(
        `Missing tenant information for tenant ID: ${tenantId}. Got ${JSON.stringify(
          tenant,
        )}`,
      );
    }

    user.tenantId = tenantId;
    user.tenantName = tenantName;
    user.shardNamespace = shardNamespace;

    const userOnTenantResponse =
      await AuthCentralClient.getInstance().getUserOnTenant(
        user,
        user.userId,
        tenantId,
      );

    user.roles = userOnTenantResponse.customerUiRoles.map(
      (role) => CustomerUiRole[role],
    );

    // Then set the session with the updated user object
    session.set("user", user);

    // Add Set-Cookie header
    headers["Set-Cookie"] = await commitSession(session);
  }

  return { headers };
}

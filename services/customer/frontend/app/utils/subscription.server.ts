import { AuthCentralClient } from "app/.server/grpc/auth-central";
import { TenantWatcherCachingClient } from "app/.server/grpc/tenant-watcher";
import type { UserPlanSchema } from "app/schemas/plan";
import {
  type BillingMethod,
  User_SubscriptionCreationInfo_Status,
  UserTier,
} from "~services/auth/central/server/auth_entities_pb";
import {
  TenantTier,
  type Tenant,
} from "~services/tenant_watcher/tenant_watcher_pb";
import type { SessionUser } from "../.server/auth";
import {
  mapTenantTierToTierSchema,
  type TenantTierSchema,
} from "app/schemas/tenant";
import {
  type SuspensionSchema,
  mapSuspensionTypeToSuspensionTypeSchema,
} from "app/schemas/suspensions";
import { Config, findPlanById } from "app/.server/config";
import { assertUnreachable } from "@augment-internal/ts-utils/type";

type PatchedSessionUser = SessionUser & {
  tenantTier: TenantTier;
  stripeCustomerId: string;
  subscriptionId?: string;
  billingMethod?: BillingMethod;
  orbCustomerId?: string;
};

// Add a function to get tenant from tenant ID
async function getTenantFromId(tenantId: string): Promise<Tenant | undefined> {
  const tenantWatcher = TenantWatcherCachingClient.getInstance();
  try {
    return await tenantWatcher.tenantFor(tenantId);
  } catch (error) {
    console.error(`Error fetching tenant name for ID ${tenantId}:`, error);
    return undefined;
  }
}

// Helper function to map UserTier to plan string
function mapUserTierToPlanName(tier: UserTier): UserPlanSchema["name"] {
  switch (tier) {
    case UserTier.PROFESSIONAL:
      return "developer";
    case UserTier.COMMUNITY:
      return "community";
    default:
      return "enterprise";
  }
}

export function mapPlanNameToUserTier(
  planName: UserPlanSchema["name"],
): UserTier {
  switch (planName) {
    case "developer":
      return UserTier.PROFESSIONAL;
    case "community":
      return UserTier.COMMUNITY;
    default:
      throw new Error(`Plan not supported: ${planName}`);
  }
}

export function mapPlanIdToUserTier(planId: string): UserTier {
  const plans = Config.orbPlansConfig;
  const plan = findPlanById(plans, planId);

  if (!plan) {
    throw new Error(`Plan not found: ${planId}`);
  }

  const planType = plan.features.plan_type;
  switch (planType) {
    case "community":
      return UserTier.COMMUNITY;
    case "paid":
      return UserTier.PROFESSIONAL;
    case "trial":
      return UserTier.PROFESSIONAL; // Trial plans map to professional tier
    default:
      return assertUnreachable(
        planType,
        `Plan type not supported: ${planType}`,
      );
  }
}

export function mapPlanIdToPlanName(planId: string): UserPlanSchema["name"] {
  const plans = Config.orbPlansConfig;
  const plan = findPlanById(plans, planId);

  if (!plan) {
    throw new Error(`Plan not found: ${planId}`);
  }

  const planType = plan.features.plan_type;
  switch (planType) {
    case "community":
      return "community";
    case "paid":
      // TODO: Modify to support multiple paid plan types (e.g. developer, pro, max)
      return "developer";
    case "trial":
      return "developer"; // Trial plans map to developer plan name
    default:
      return assertUnreachable(
        planType,
        `Plan type not supported: ${planType}`,
      );
  }
}

export async function getUserPlan(user: SessionUser): Promise<
  UserPlanSchema & {
    tenantId: string;
    tenantName: string;
    tenantTier: TenantTierSchema;
    shardNamespace: string;
    isSubscriptionPending?: boolean;
    suspensions: SuspensionSchema[];
  }
> {
  try {
    // Get user information from Auth Central
    const authCentralClient = new AuthCentralClient();
    const response = await authCentralClient.getUser(user);

    if (!response.user) {
      throw new Error(
        "Failed to get user information from Auth Central service",
      );
    }

    // Check if subscription creation is pending
    const isSubscriptionPending =
      response.user.subscriptionCreationInfo?.status ===
      User_SubscriptionCreationInfo_Status.PENDING;

    // Get tenant ID from user response
    const tenants = response.user.tenants;
    if (!tenants || tenants.length === 0) {
      throw new Error("Failed to get tenant ID from Auth Central service");
    }

    // Find enterprise tenant if it exists, otherwise use the first tenant
    let tenantId = tenants[0];
    for (const tenant_id of tenants) {
      const tenant = await getTenantFromId(tenant_id);
      if (tenant?.tier === TenantTier.ENTERPRISE) {
        tenantId = tenant_id;
        break;
      }
    }

    // Get tenant from tenant watcher service
    const tenant = await getTenantFromId(tenantId);
    if (!tenant?.name || !tenant?.tier || !tenant?.shardNamespace) {
      throw new Error(`Incomplete tenant information for ID: ${tenantId}`);
    }

    // Create patched user with tenant information
    const patchedUser: PatchedSessionUser = {
      ...user,
      tenantId,
      tenantName: tenant.name,
      tenantTier: tenant.tier,
      shardNamespace: tenant.shardNamespace,
      stripeCustomerId: response.user.stripeCustomerId,
      subscriptionId: response.user.subscriptionId,
      billingMethod: response.user.billingMethod,
      orbCustomerId: response.user.orbCustomerId,
    };

    const plan: UserPlanSchema = {
      name:
        patchedUser.tenantTier === TenantTier.COMMUNITY
          ? "community"
          : "developer",
      billingMethod: patchedUser.billingMethod,
    };

    // Add tenant information to plan and check for pending tier changes
    return {
      ...plan,
      suspensions: response.user.suspensions.map((suspension) => ({
        suspensionId: suspension.suspensionId,
        createdTime: suspension.createdTime?.toDate().toISOString(),
        suspensionType: mapSuspensionTypeToSuspensionTypeSchema(
          suspension.suspensionType,
        ),
        evidence: suspension.evidence,
      })),
      tenantTier: mapTenantTierToTierSchema(tenant.tier),
      ...(response.user?.tierChange && {
        // when there is a tier change we use the target tier for the legacy plan name
        // (which is actually the tier - hence these are the same)
        name: mapUserTierToPlanName(response.user.tierChange.targetTier),
        tenantTier: mapUserTierToPlanName(response.user.tierChange.targetTier),
        pending: true,
      }),
      tenantId,
      tenantName: tenant.name,
      shardNamespace: tenant.shardNamespace,
      isSubscriptionPending,
    };
  } catch (error) {
    console.error("Error getting user plan:", error);
    throw new Error(`Failed to get user plan: ${error}`);
  }
}

import { TimeoutError, waitUntil } from "@augment-internal/ts-utils/timer";
import { secondsToMilliseconds } from "date-fns";
import { getTenantTier, type SessionUser } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { TenantWatcherCachingClient } from "../.server/grpc/tenant-watcher";
import { logger } from "@augment-internal/logging";
import { TenantCreation_Status } from "~services/auth/central/server/auth_entities_pb";
import { getFeatureFlag } from "app/feature-flags/feature-flags.server";
import { TenantTier } from "~services/tenant_watcher/tenant_watcher_pb";
import { registerCounter } from "../.server/metrics";

// Extremely coarse-grained metric for success vs failure of creating new teams, from the client's
// perspective. Status is either OK or ERROR.
const newTeamCreationCounter = registerCounter({
  name: "au_customer_ui_new_team_creation_count_total",
  help: "Count and status of creating a new team from customer-ui",
  labelNames: ["status"],
});

export async function teamsAllowed(user: SessionUser): Promise<boolean> {
  const [tenantTier, teamManagementFlag, teamManagementCanaryDomains] =
    await Promise.all([
      getTenantTier(user),
      getFeatureFlag("team_management", user),
      getFeatureFlag("team_management_canary_domains", user),
    ]);

  if (user.email) {
    const userEmailDomain = user.email.split("@").pop();
    if (userEmailDomain && teamManagementCanaryDomains) {
      const whitelistedDomains = teamManagementCanaryDomains
        .split(",")
        .map((domain) => domain.trim().toLowerCase());
      if (whitelistedDomains.includes(userEmailDomain.toLowerCase())) {
        // User's domain is in the canary list, team management is allowed
        // (respecting orbEnabled and tenantTier conditions)
        return tenantTier === TenantTier.PROFESSIONAL;
      }
    }
  }

  // Check if user is in a non-legacy self-serve team
  if (
    tenantTier === TenantTier.PROFESSIONAL &&
    (await isUserInNonLegacySelfServeTeam(user))
  ) {
    return true;
  }

  return tenantTier === TenantTier.PROFESSIONAL && !!teamManagementFlag;
}

/** Checks if the user is in a self-serve team. */
export async function isUserInSelfServeTeam(
  user: Pick<SessionUser, "tenantId">,
): Promise<boolean> {
  const tenantConfig = await getTenantConfig(user, "self-serve check");
  return tenantConfig?.configs?.is_self_serve_team === "true";
}

/**
 * Helper function to safely get tenant configuration for a user.
 * Returns null if tenant cannot be fetched or doesn't have config.
 */
async function getTenantConfig(
  user: Pick<SessionUser, "tenantId">,
  errorContext: string,
): Promise<{ configs?: Record<string, string> } | null> {
  try {
    const tenantWatcher = TenantWatcherCachingClient.getInstance();
    const tenant = await tenantWatcher.tenantFor(user.tenantId);

    if (!tenant || !tenant.config) {
      logger.error(
        `Could not determine tenant config for ${user.tenantId} (${errorContext}), returning null`,
      );
      return null;
    }

    return tenant.config;
  } catch (error) {
    logger.error(
      `Error getting tenant config for tenant ${user.tenantId} (${errorContext}):`,
      error,
    );
    return null;
  }
}

/** Checks if the user is in a non-legacy self-serve team. */
export async function isUserInNonLegacySelfServeTeam(
  user: Pick<SessionUser, "tenantId">,
): Promise<boolean> {
  const tenantConfig = await getTenantConfig(
    user,
    "non-legacy self-serve check",
  );
  if (!tenantConfig) {
    return false;
  }

  // Must be a self-serve team
  if (tenantConfig.configs?.is_self_serve_team !== "true") {
    return false;
  }

  // Must NOT be a legacy self-serve team
  return tenantConfig.configs?.is_legacy_self_serve_team !== "true";
}

/** Checks if the user is in a legacy self-serve team. */
export async function isUserInLegacySelfServeTeam(
  user: Pick<SessionUser, "tenantId">,
): Promise<boolean> {
  const tenantConfig = await getTenantConfig(user, "legacy self-serve check");
  if (!tenantConfig) {
    return false;
  }

  return tenantConfig.configs?.is_legacy_self_serve_team === "true";
}

/**
 * Waits for the tenant creation process to complete.
 *
 * @param user The user to ensure a tenant for.
 * @param tenantCreationId The tenant creation ID to wait for.
 * @returns The tenant ID of the user.
 */
export async function waitForTenant(
  user: SessionUser,
  tenantCreationId: string,
): Promise<string> {
  try {
    const authCentralClient = AuthCentralClient.getInstance();
    const waitForTenantId = waitUntil(
      (tenantId: string | undefined) => tenantId !== undefined,
      secondsToMilliseconds(25), // timeout after 25 seconds
      secondsToMilliseconds(5), // retry every 5 seconds
    );
    return waitForTenantId(async () => {
      try {
        const statusResponse =
          await authCentralClient.getCreateTenantForTeamStatus(
            user,
            tenantCreationId,
          );
        return statusResponse.tenantCreation?.status ===
          TenantCreation_Status.SUCCESS
          ? statusResponse.tenantCreation.tenantId
          : undefined;
      } catch (error) {
        logger.error("Error while getting tenant creation status", error);
      }
    });
  } catch (error) {
    if (error instanceof TimeoutError) {
      logger.error("Timeout waiting for tenant creation");
      throw new Error("Timeout waiting for tenant creation");
    }
    logger.error("Error waiting for tenant creation", error);
    throw error;
  }
}

/**
 * Creates a self-serve team if the user is not already in a team.
 *
 * @param user The user to create a tenant for.
 * @returns The tenant ID of the user.
 * @throws on error creating a tenant.
 */
export async function ensureTeamForUser(user: SessionUser) {
  if (await isUserInSelfServeTeam(user)) {
    return user.tenantId;
  }

  let createTeamStatus = "UNKNOWN";
  try {
    const authCentralClient = AuthCentralClient.getInstance();
    const createResponse = await authCentralClient.createTenantForTeam(user);
    let result = waitForTenant(user, createResponse.tenantCreationId);
    createTeamStatus = "OK";
    return result;
  } catch (error) {
    createTeamStatus = "ERROR";
    throw error;
  } finally {
    newTeamCreationCounter.labels(createTeamStatus).inc();
  }
}

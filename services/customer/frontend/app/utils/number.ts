export function toPercentage(value: number, total: number): number {
  return (value / total) * 100;
}

/**
 * Returns a value if it is in the range
 * @returns the min or max if the value is out of range.
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

export function isWithinRange(
  value: number,
  min: number,
  max: number,
): boolean {
  return min <= value && value <= max;
}

import type { SerializeFrom } from "@remix-run/node";

import type { YearMonthDay } from "./date";
import type {
  DateAndCount,
  GetChatStatsResponse_ChatStats,
  GetCompletionStatsResponse_CompletionStats,
  GetEditStatsResponse_EditStats,
} from "../../../../request_insight/analytics/request_insight_analytics_pb";

export function formDateValue() {
  return (item: SerializeFrom<DateAndCount>) => {
    if (!item.date) {
      throw new Error(`Date not found on item: ${JSON.stringify(item)}`);
    }

    const { year, month, day } = item.date;
    return {
      date: `${year}-${month}-${day}`,
      value: item.count,
    };
  };
}

export function between(start: YearMonthDay, end: YearMonthDay) {
  return (
    item: SerializeFrom<
      | DateAndCount
      | GetChatStatsResponse_ChatStats
      | GetEditStatsResponse_EditStats
      | GetCompletionStatsResponse_CompletionStats
    >,
  ) => {
    let itemDate: YearMonthDay | undefined;

    if ("aggregation" in item) {
      if ("case" in item.aggregation && item.aggregation.case === "date") {
        itemDate = item.aggregation.value;
      }
    } else if ("date" in item && item.date) {
      itemDate = item.date;
    }

    if (!itemDate) {
      throw new Error("Date not found on item: ${JSON.stringify(item)}");
    }

    return isBetween(start, itemDate, end);
  };
}

export function isBetween(
  start: YearMonthDay,
  itemDate: YearMonthDay,
  end: YearMonthDay,
) {
  if (itemDate.year < start.year || itemDate.year > end.year) {
    return false;
  }

  if (
    itemDate.year === start.year &&
    (itemDate.month < start.month ||
      (itemDate.month === start.month && itemDate.day < start.day))
  ) {
    return false;
  }

  if (
    itemDate.year === end.year &&
    (itemDate.month > end.month ||
      (itemDate.month === end.month && itemDate.day > end.day))
  ) {
    return false;
  }

  return true;
}

:root {
  --ds-color-text-default: var(--ds-color-neutral-12);
  --ds-color-text-subtle: var(--ds-color-neutral-9);

  /* Default text colors */
  --ds-text-default: var(--ds-color-text-default);
  --ds-text-subtle: var(--ds-color-text-subtle);

  /* Interactive text states */
  --ds-text-link: var(--ds-color-accent-9);
  --ds-text-link-hover: var(--ds-color-accent-10);

  /* Semantic text colors */
  --ds-text-error: var(--ds-color-error-11);
  --ds-text-success: var(--ds-color-success-11);
  --ds-text-warning: var(--ds-color-warning-11);
  --ds-text-info: var(--ds-color-info-11);

  /* Disabled state */
  --ds-text-disabled: var(--ds-color-neutral-8);
}

:root.dark,
:root .dark {
  --ds-color-text-default: var(--ds-color-neutral-12);
  --ds-color-text-subtle: var(--ds-color-neutral-9);

  /* Default text colors */
  --ds-text-default: var(--ds-color-text-default);
  --ds-text-subtle: var(--ds-color-text-subtle);

  /* Interactive text states */
  --ds-text-link: var(--ds-color-accent-9);
  --ds-text-link-hover: var(--ds-color-accent-10);

  /* Semantic text colors */
  --ds-text-error: var(--ds-color-error-11);
  --ds-text-success: var(--ds-color-success-11);
  --ds-text-warning: var(--ds-color-warning-11);
  --ds-text-info: var(--ds-color-info-11);

  /* Disabled state */
  --ds-text-disabled: var(--ds-color-neutral-8);
}

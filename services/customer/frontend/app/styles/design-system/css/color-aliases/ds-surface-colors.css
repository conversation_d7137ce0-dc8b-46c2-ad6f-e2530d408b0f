/* Semantic colors - surface */

:root {
  /* Surface colors based on existing design system variables */
  --ds-color-surface-1: var(--ds-color-neutral-1);
  --ds-color-surface-2: var(--ds-color-neutral-2);
  --ds-color-surface-3: var(--ds-color-neutral-3);
  --ds-color-surface-4: var(--ds-color-neutral-4);
  --ds-color-surface-5: var(--ds-color-neutral-5);
  --ds-color-surface-6: var(--ds-color-neutral-6);
  --ds-color-surface-7: var(--ds-color-neutral-7);
  --ds-color-surface-8: var(--ds-color-neutral-8);
  --ds-color-surface-9: var(--ds-color-neutral-9);
  --ds-color-surface-10: var(--ds-color-neutral-10);
  --ds-color-surface-11: var(--ds-color-neutral-11);
  --ds-color-surface-12: var(--ds-color-neutral-12);

  /* Alpha variants */
  --ds-color-surface-a1: var(--ds-color-neutral-a1);
  --ds-color-surface-a2: var(--ds-color-neutral-a2);
  --ds-color-surface-a3: var(--ds-color-neutral-a3);
  --ds-color-surface-a4: var(--ds-color-neutral-a4);
  --ds-color-surface-a5: var(--ds-color-neutral-a5);
  --ds-color-surface-a6: var(--ds-color-neutral-a6);
  --ds-color-surface-a7: var(--ds-color-neutral-a7);
  --ds-color-surface-a8: var(--ds-color-neutral-a8);
  --ds-color-surface-a9: var(--ds-color-neutral-a9);
  --ds-color-surface-a10: var(--ds-color-neutral-a10);
  --ds-color-surface-a11: var(--ds-color-neutral-a11);
  --ds-color-surface-a12: var(--ds-color-neutral-a12);

  /* Default surface color */
  --ds-color-surface-default: var(--ds-color-surface-2);
  --ds-color-surface-default-a: var(--ds-color-surface-a2);
  --ds-color-surface-default-contrast: var(--ds-color-surface-12);
  --ds-color-surface-default-contrast-a: var(--ds-color-surface-a12);
}

/* Semantic colors - info */
@import "@radix-ui/colors/sky.css";
@import "@radix-ui/colors/sky-alpha.css";
@import "@radix-ui/colors/sky-dark.css";
@import "@radix-ui/colors/sky-dark-alpha.css";

:root {
  --ds-color-info-1: var(--sky-1);
  --ds-color-info-2: var(--sky-2);
  --ds-color-info-3: var(--sky-3);
  --ds-color-info-4: var(--sky-4);
  --ds-color-info-5: var(--sky-5);
  --ds-color-info-6: var(--sky-6);
  --ds-color-info-7: var(--sky-7);
  --ds-color-info-8: var(--sky-8);
  --ds-color-info-9: var(--sky-9);
  --ds-color-info-10: var(--sky-10);
  --ds-color-info-11: var(--sky-11);
  --ds-color-info-12: var(--sky-12);

  --ds-color-info-a1: var(--sky-a1);
  --ds-color-info-a2: var(--sky-a2);
  --ds-color-info-a3: var(--sky-a3);
  --ds-color-info-a4: var(--sky-a4);
  --ds-color-info-a5: var(--sky-a5);
  --ds-color-info-a6: var(--sky-a6);
  --ds-color-info-a7: var(--sky-a7);
  --ds-color-info-a8: var(--sky-a8);
  --ds-color-info-a9: var(--sky-a9);
  --ds-color-info-a10: var(--sky-a10);
  --ds-color-info-a11: var(--sky-a11);
  --ds-color-info-a12: var(--sky-a12);
}

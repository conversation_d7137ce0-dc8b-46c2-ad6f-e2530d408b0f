/* Semantic colors - premium */
@import "@radix-ui/colors/purple.css";
@import "@radix-ui/colors/purple-alpha.css";
@import "@radix-ui/colors/purple-dark.css";
@import "@radix-ui/colors/purple-dark-alpha.css";

:root {
  --ds-color-premium-1: var(--purple-1);
  --ds-color-premium-2: var(--purple-2);
  --ds-color-premium-3: var(--purple-3);
  --ds-color-premium-4: var(--purple-4);
  --ds-color-premium-5: var(--purple-5);
  --ds-color-premium-6: var(--purple-6);
  --ds-color-premium-7: var(--purple-7);
  --ds-color-premium-8: var(--purple-8);
  --ds-color-premium-9: var(--purple-9);
  --ds-color-premium-10: var(--purple-10);
  --ds-color-premium-11: var(--purple-11);
  --ds-color-premium-12: var(--purple-12);

  --ds-color-premium-a1: var(--purple-a1);
  --ds-color-premium-a2: var(--purple-a2);
  --ds-color-premium-a3: var(--purple-a3);
  --ds-color-premium-a4: var(--purple-a4);
  --ds-color-premium-a5: var(--purple-a5);
  --ds-color-premium-a6: var(--purple-a6);
  --ds-color-premium-a7: var(--purple-a7);
  --ds-color-premium-a8: var(--purple-a8);
  --ds-color-premium-a9: var(--purple-a9);
  --ds-color-premium-a10: var(--purple-a10);
  --ds-color-premium-a11: var(--purple-a11);
  --ds-color-premium-a12: var(--purple-a12);
}

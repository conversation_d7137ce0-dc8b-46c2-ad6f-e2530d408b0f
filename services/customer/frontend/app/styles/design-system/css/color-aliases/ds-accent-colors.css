@import "@radix-ui/colors/blue.css";
@import "@radix-ui/colors/blue-alpha.css";
@import "@radix-ui/colors/blue-dark.css";
@import "@radix-ui/colors/blue-dark-alpha.css";

:root {
  --ds-color-accent-1: var(--blue-1);
  --ds-color-accent-2: var(--blue-2);
  --ds-color-accent-3: var(--blue-3);
  --ds-color-accent-4: var(--blue-4);
  --ds-color-accent-5: var(--blue-5);
  --ds-color-accent-6: var(--blue-6);
  --ds-color-accent-7: var(--blue-7);
  --ds-color-accent-8: var(--blue-8);
  --ds-color-accent-9: var(--blue-9);
  --ds-color-accent-10: var(--blue-10);
  --ds-color-accent-11: var(--blue-11);
  --ds-color-accent-12: var(--blue-12);

  --ds-color-accent-a1: var(--blue-a1);
  --ds-color-accent-a2: var(--blue-a2);
  --ds-color-accent-a3: var(--blue-a3);
  --ds-color-accent-a4: var(--blue-a4);
  --ds-color-accent-a5: var(--blue-a5);
  --ds-color-accent-a6: var(--blue-a6);
  --ds-color-accent-a7: var(--blue-a7);
  --ds-color-accent-a8: var(--blue-a8);
  --ds-color-accent-a9: var(--blue-a9);
  --ds-color-accent-a10: var(--blue-a10);
  --ds-color-accent-a11: var(--blue-a11);
  --ds-color-accent-a12: var(--blue-a12);

  --ds-color-accent-contrast: var(--ds-white-contrast);
}

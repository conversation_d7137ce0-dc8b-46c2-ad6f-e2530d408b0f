/* Custom secondary colors based on --augment-text-color-secondary */
/* Secondary colors are slightly less bright than primary colors */

:root {
  --ds-color-secondary-1: hsl(0, 0%, 100%);
  --ds-color-secondary-2: hsl(0, 0%, 98%);
  --ds-color-secondary-3: hsl(0, 0%, 96%);
  --ds-color-secondary-4: hsl(0, 0%, 92%);
  --ds-color-secondary-5: hsl(0, 0%, 85%);
  --ds-color-secondary-6: hsl(0, 0%, 75%);
  --ds-color-secondary-7: hsl(0, 0%, 65%);
  --ds-color-secondary-8: hsl(0, 0%, 55%);
  --ds-color-secondary-9: hsl(0, 0%, 45%);
  --ds-color-secondary-10: hsl(0, 0%, 35%);
  --ds-color-secondary-11: hsl(0, 0%, 25%);
  --ds-color-secondary-12: hsl(0, 0%, 15%);

  --ds-color-secondary-a1: hsla(0, 0%, 0%, 0.01);
  --ds-color-secondary-a2: hsla(0, 0%, 0%, 0.02);
  --ds-color-secondary-a3: hsla(0, 0%, 0%, 0.04);
  --ds-color-secondary-a4: hsla(0, 0%, 0%, 0.08);
  --ds-color-secondary-a5: hsla(0, 0%, 0%, 0.15);
  --ds-color-secondary-a6: hsla(0, 0%, 0%, 0.25);
  --ds-color-secondary-a7: hsla(0, 0%, 0%, 0.35);
  --ds-color-secondary-a8: hsla(0, 0%, 0%, 0.45);
  --ds-color-secondary-a9: hsla(0, 0%, 0%, 0.55);
  --ds-color-secondary-a10: hsla(0, 0%, 0%, 0.65);
  --ds-color-secondary-a11: hsla(0, 0%, 0%, 0.75);
  --ds-color-secondary-a12: hsla(0, 0%, 0%, 0.85);
}

@media (prefers-color-scheme: dark) {
  :root {
    --ds-color-secondary-1: hsl(0, 0%, 8%);
    --ds-color-secondary-2: hsl(0, 0%, 12%);
    --ds-color-secondary-3: hsl(0, 0%, 16%);
    --ds-color-secondary-4: hsl(0, 0%, 24%);
    --ds-color-secondary-5: hsl(0, 0%, 32%);
    --ds-color-secondary-6: hsl(0, 0%, 40%);
    --ds-color-secondary-7: hsl(0, 0%, 48%);
    --ds-color-secondary-8: hsl(0, 0%, 56%);
    --ds-color-secondary-9: hsl(0, 0%, 64%);
    --ds-color-secondary-10: hsl(0, 0%, 72%);
    --ds-color-secondary-11: hsl(0, 0%, 80%);
    --ds-color-secondary-12: hsl(0, 0%, 85%);

    --ds-color-secondary-a1: hsla(0, 0%, 100%, 0.04);
    --ds-color-secondary-a2: hsla(0, 0%, 100%, 0.08);
    --ds-color-secondary-a3: hsla(0, 0%, 100%, 0.12);
    --ds-color-secondary-a4: hsla(0, 0%, 100%, 0.16);
    --ds-color-secondary-a5: hsla(0, 0%, 100%, 0.24);
    --ds-color-secondary-a6: hsla(0, 0%, 100%, 0.32);
    --ds-color-secondary-a7: hsla(0, 0%, 100%, 0.4);
    --ds-color-secondary-a8: hsla(0, 0%, 100%, 0.48);
    --ds-color-secondary-a9: hsla(0, 0%, 100%, 0.56);
    --ds-color-secondary-a10: hsla(0, 0%, 100%, 0.64);
    --ds-color-secondary-a11: hsla(0, 0%, 100%, 0.72);
    --ds-color-secondary-a12: hsla(0, 0%, 100%, 0.8);
  }
}

:root.dark {
  --ds-color-secondary-1: hsl(0, 0%, 8%);
  --ds-color-secondary-2: hsl(0, 0%, 12%);
  --ds-color-secondary-3: hsl(0, 0%, 16%);
  --ds-color-secondary-4: hsl(0, 0%, 24%);
  --ds-color-secondary-5: hsl(0, 0%, 32%);
  --ds-color-secondary-6: hsl(0, 0%, 40%);
  --ds-color-secondary-7: hsl(0, 0%, 48%);
  --ds-color-secondary-8: hsl(0, 0%, 56%);
  --ds-color-secondary-9: hsl(0, 0%, 64%);
  --ds-color-secondary-10: hsl(0, 0%, 72%);
  --ds-color-secondary-11: hsl(0, 0%, 80%);
  --ds-color-secondary-12: hsl(0, 0%, 85%);

  --ds-color-secondary-a1: hsla(0, 0%, 100%, 0.04);
  --ds-color-secondary-a2: hsla(0, 0%, 100%, 0.08);
  --ds-color-secondary-a3: hsla(0, 0%, 100%, 0.12);
  --ds-color-secondary-a4: hsla(0, 0%, 100%, 0.16);
  --ds-color-secondary-a5: hsla(0, 0%, 100%, 0.24);
  --ds-color-secondary-a6: hsla(0, 0%, 100%, 0.32);
  --ds-color-secondary-a7: hsla(0, 0%, 100%, 0.4);
  --ds-color-secondary-a8: hsla(0, 0%, 100%, 0.48);
  --ds-color-secondary-a9: hsla(0, 0%, 100%, 0.56);
  --ds-color-secondary-a10: hsla(0, 0%, 100%, 0.64);
  --ds-color-secondary-a11: hsla(0, 0%, 100%, 0.72);
  --ds-color-secondary-a12: hsla(0, 0%, 100%, 0.8);
}

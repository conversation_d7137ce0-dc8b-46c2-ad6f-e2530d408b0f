:root {
  /* Default border colors */
  --ds-color-border-default: var(--ds-color-neutral-6);
  --ds-color-border-subtle: var(--ds-color-neutral-4);
  --ds-color-border-strong: var(--ds-color-neutral-8);

  /* Interactive border states */
  --ds-color-border-focus: var(--ds-color-accent-8);
  --ds-color-border-hover: var(--ds-color-accent-7);

  /* Semantic border colors */
  --ds-color-border-error: var(--ds-color-error-9);
  --ds-color-border-success: var(--ds-color-success-9);
  --ds-color-border-warning: var(--ds-color-warning-9);
  --ds-color-border-info: var(--ds-color-info-9);

  /* Disabled state */
  --ds-color-border-disabled: var(--ds-color-neutral-5);
}

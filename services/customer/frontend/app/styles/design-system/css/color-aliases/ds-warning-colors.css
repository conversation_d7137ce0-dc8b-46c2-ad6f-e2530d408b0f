/* Semantic colors - warning */
@import "@radix-ui/colors/amber.css";
@import "@radix-ui/colors/amber-alpha.css";
@import "@radix-ui/colors/amber-dark.css";
@import "@radix-ui/colors/amber-dark-alpha.css";

:root {
  --ds-color-warning-1: var(--amber-1);
  --ds-color-warning-2: var(--amber-2);
  --ds-color-warning-3: var(--amber-3);
  --ds-color-warning-4: var(--amber-4);
  --ds-color-warning-5: var(--amber-5);
  --ds-color-warning-6: var(--amber-6);
  --ds-color-warning-7: var(--amber-7);
  --ds-color-warning-8: var(--amber-8);
  --ds-color-warning-9: var(--amber-9);
  --ds-color-warning-10: var(--amber-10);
  --ds-color-warning-11: var(--amber-11);
  --ds-color-warning-12: var(--amber-12);

  --ds-color-warning-a1: var(--amber-a1);
  --ds-color-warning-a2: var(--amber-a2);
  --ds-color-warning-a3: var(--amber-a3);
  --ds-color-warning-a4: var(--amber-a4);
  --ds-color-warning-a5: var(--amber-a5);
  --ds-color-warning-a6: var(--amber-a6);
  --ds-color-warning-a7: var(--amber-a7);
  --ds-color-warning-a8: var(--amber-a8);
  --ds-color-warning-a9: var(--amber-a9);
  --ds-color-warning-a10: var(--amber-a10);
  --ds-color-warning-a11: var(--amber-a11);
  --ds-color-warning-a12: var(--amber-a12);
}

/*
  Z-index scale for the customer frontend design system
  
  This file establishes a consistent z-index scale for components
  to ensure proper layering throughout the application.
*/

:root {
  /* Base layers */
  --ds-z-index-base: 1;
  --ds-z-index-above: 10;

  /* Component layers */
  --ds-z-index-dropdown: 100;
  --ds-z-index-sticky: 200;
  --ds-z-index-tooltip: 300;
  --ds-z-index-toast: 400;

  /* Overlay layers */
  --ds-z-index-overlay: 1000;
  --ds-z-index-modal: 1001;

  /* Top layers */
  --ds-z-index-popover: 2000;
  --ds-z-index-toast-viewport: 2147483647; /* Max safe integer for z-index */
}

@import "@radix-ui/colors/gray-alpha.css";
@import "@radix-ui/colors/gray-dark-alpha.css";

/* Black only has one file, no dark/light variants */
@import "@radix-ui/colors/black-alpha.css";

/* prettier-ignore */
:root {
  --ds-shadow-1:
    inset 0 0 0 1px var(--gray-a5),
    inset 0 1.5px 2px 0 var(--gray-a2),
    inset 0 1.5px 2px 0 var(--black-a2);

  --ds-shadow-2:
    0 0 0 1px var(--gray-a3),
    0 0 0 0.5px var(--black-a1),
    0 1px 1px 0 var(--gray-a2),
    0 2px 1px -1px var(--black-a1),
    0 1px 3px 0 var(--black-a1);

  --ds-shadow-3:
    0 0 0 1px var(--gray-a3),
    0 2px 3px -2px var(--gray-a3),
    0 3px 12px -4px var(--black-a2),
    0 4px 16px -8px var(--black-a2);

  --ds-shadow-4:
    0 0 0 1px var(--gray-a3),
    0 8px 40px var(--black-a1),
    0 12px 32px -16px var(--gray-a3);

  --ds-shadow-5:
    0 0 0 1px var(--gray-a3),
    0 12px 60px var(--black-a3),
    0 12px 32px -16px var(--gray-a5);

  --ds-shadow-6:
    0 0 0 1px var(--gray-a3),
    0 12px 60px var(--black-a3),
    0 16px 64px var(--gray-a2),
    0 16px 36px -20px var(--gray-a7);

  /* No Borders */
  --ds-shadow-no-border-1:
    inset 0 1.5px 2px 0 var(--gray-a2),
    inset 0 1.5px 2px 0 var(--black-a2);

  --ds-shadow-no-border-2:
    0 1px 1px 0 var(--gray-a2),
    0 2px 1px -1px var(--black-a1),
    0 1px 3px 0 var(--black-a1);

  --ds-shadow-no-border-3:
    0 2px 3px -2px var(--gray-a3),
    0 3px 12px -4px var(--black-a2),
    0 4px 16px -8px var(--black-a2);

  --ds-shadow-no-border-4:
    0 8px 40px var(--black-a1),
    0 12px 32px -16px var(--gray-a3);

  --ds-shadow-no-border-5:
    0 12px 60px var(--black-a3),
    0 12px 32px -16px var(--gray-a5);

  --ds-shadow-no-border-6:
    0 12px 60px var(--black-a3),
    0 16px 64px var(--gray-a2),
    0 16px 36px -20px var(--gray-a7);
}

/* prettier-ignore */
@media (prefers-color-scheme: dark) {
  :root {
    --ds-shadow-1:
      inset 0 -1px 1px 0 var(--gray-a3),
      inset 0 0 0 1px var(--gray-a3),
      inset 0 3px 4px 0 var(--black-a5),
      inset 0 0 0 1px var(--gray-a4);

    --ds-shadow-2:
      0 0 0 1px var(--gray-a6),
      0 0 0 0.5px var(--black-a3),
      0 1px 1px 0 var(--black-a6),
      0 2px 1px -1px var(--black-a6),
      0 1px 3px 0 var(--black-a5);

    --ds-shadow-3:
      0 0 0 1px var(--gray-a6),
      0 2px 3px -2px var(--black-a3),
      0 3px 8px -2px var(--black-a6),
      0 4px 12px -4px var(--black-a7);

    --ds-shadow-4:
      0 0 0 1px var(--gray-a6),
      0 8px 40px var(--black-a3),
      0 12px 32px -16px var(--black-a5);

    --ds-shadow-5:
      0 0 0 1px var(--gray-a6),
      0 12px 60px var(--black-a5),
      0 12px 32px -16px var(--black-a7);

    --ds-shadow-6:
      0 0 0 1px var(--gray-a6),
      0 12px 60px var(--black-a4),
      0 16px 64px var(--black-a6),
      0 16px 36px -20px var(--black-a11);

      /* No Borders */
      --ds-shadow-no-border-1:
      inset 0 3px 4px 0 var(--black-a5),
      inset 0 0 0 1px var(--gray-a4);

    --ds-shadow-no-border-2:
      0 1px 1px 0 var(--black-a6),
      0 2px 1px -1px var(--black-a6),
      0 1px 3px 0 var(--black-a5);

    --ds-shadow-no-border-3:
      0 2px 3px -2px var(--black-a3),
      0 3px 8px -2px var(--black-a6),
      0 4px 12px -4px var(--black-a7);

    --ds-shadow-no-border-4:
      0 8px 40px var(--black-a3),
      0 12px 32px -16px var(--black-a5);

    --ds-shadow-no-border-5:
      0 12px 60px var(--black-a5),
      0 12px 32px -16px var(--black-a7);

    --ds-shadow-no-border-6:
      0 12px 60px var(--black-a4),
      0 16px 64px var(--black-a6),
      0 16px 36px -20px var(--black-a11);
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --ds-shadow-1: inset 0 -1px 1px 0 var(--gray-a3),
      inset 0 0 0 1px var(--gray-a3), inset 0 3px 4px 0 var(--black-a5),
      inset 0 0 0 1px var(--gray-a4);

    --ds-shadow-2: 0 0 0 1px var(--gray-a6), 0 0 0 0.5px var(--black-a3),
      0 1px 1px 0 var(--black-a6), 0 2px 1px -1px var(--black-a6),
      0 1px 3px 0 var(--black-a5);

    --ds-shadow-3: 0 0 0 1px var(--gray-a6), 0 2px 3px -2px var(--black-a3),
      0 3px 8px -2px var(--black-a6), 0 4px 12px -4px var(--black-a7);

    --ds-shadow-4: 0 0 0 1px var(--gray-a6), 0 8px 40px var(--black-a3),
      0 12px 32px -16px var(--black-a5);

    --ds-shadow-5: 0 0 0 1px var(--gray-a6), 0 12px 60px var(--black-a5),
      0 12px 32px -16px var(--black-a7);

    --ds-shadow-6: 0 0 0 1px var(--gray-a6), 0 12px 60px var(--black-a4),
      0 16px 64px var(--black-a6), 0 16px 36px -20px var(--black-a11);

    /* No Borders */
    --ds-shadow-no-border-1: inset 0 3px 4px 0 var(--black-a5),
      inset 0 0 0 1px var(--gray-a4);

    --ds-shadow-no-border-2: 0 1px 1px 0 var(--black-a6),
      0 2px 1px -1px var(--black-a6), 0 1px 3px 0 var(--black-a5);

    --ds-shadow-no-border-3: 0 2px 3px -2px var(--black-a3),
      0 3px 8px -2px var(--black-a6), 0 4px 12px -4px var(--black-a7);

    --ds-shadow-no-border-4: 0 8px 40px var(--black-a3),
      0 12px 32px -16px var(--black-a5);

    --ds-shadow-no-border-5: 0 12px 60px var(--black-a5),
      0 12px 32px -16px var(--black-a7);

    --ds-shadow-no-border-6: 0 12px 60px var(--black-a4),
      0 16px 64px var(--black-a6), 0 16px 36px -20px var(--black-a11);
  }
}

:root.dark {
  --ds-shadow-1: inset 0 -1px 1px 0 var(--gray-a3),
    inset 0 0 0 1px var(--gray-a3), inset 0 3px 4px 0 var(--black-a5),
    inset 0 0 0 1px var(--gray-a4);

  --ds-shadow-2: 0 0 0 1px var(--gray-a6), 0 0 0 0.5px var(--black-a3),
    0 1px 1px 0 var(--black-a6), 0 2px 1px -1px var(--black-a6),
    0 1px 3px 0 var(--black-a5);

  --ds-shadow-3: 0 0 0 1px var(--gray-a6), 0 2px 3px -2px var(--black-a3),
    0 3px 8px -2px var(--black-a6), 0 4px 12px -4px var(--black-a7);

  --ds-shadow-4: 0 0 0 1px var(--gray-a6), 0 8px 40px var(--black-a3),
    0 12px 32px -16px var(--black-a5);

  --ds-shadow-5: 0 0 0 1px var(--gray-a6), 0 12px 60px var(--black-a5),
    0 12px 32px -16px var(--black-a7);

  --ds-shadow-6: 0 0 0 1px var(--gray-a6), 0 12px 60px var(--black-a4),
    0 16px 64px var(--black-a6), 0 16px 36px -20px var(--black-a11);

  /* No Borders */
  --ds-shadow-no-border-1: inset 0 3px 4px 0 var(--black-a5),
    inset 0 0 0 1px var(--gray-a4);

  --ds-shadow-no-border-2: 0 1px 1px 0 var(--black-a6),
    0 2px 1px -1px var(--black-a6), 0 1px 3px 0 var(--black-a5);

  --ds-shadow-no-border-3: 0 2px 3px -2px var(--black-a3),
    0 3px 8px -2px var(--black-a6), 0 4px 12px -4px var(--black-a7);

  --ds-shadow-no-border-4: 0 8px 40px var(--black-a3),
    0 12px 32px -16px var(--black-a5);

  --ds-shadow-no-border-5: 0 12px 60px var(--black-a5),
    0 12px 32px -16px var(--black-a7);

  --ds-shadow-no-border-6: 0 12px 60px var(--black-a4),
    0 16px 64px var(--black-a6), 0 16px 36px -20px var(--black-a11);
}

/* Modern toast styling for Augment */

.ToastViewport {
  --stack-gap: 12px;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 400px;
  max-width: 100vw;
  margin: 0;
  list-style: none;
  z-index: var(--ds-z-index-toast-viewport);
  outline: none;
  transition: transform 400ms ease;
  padding: var(--ds-spacing-4);
}

.ToastRoot {
  --opacity: 0;
  --x: var(--radix-toast-swipe-move-x, 0);
  --y: calc(1px - (var(--stack-gap) * var(--index)));
  --scale: calc(1 - 0.05 * var(--index));
  position: absolute;
  bottom: var(--ds-spacing-4);
  right: var(--ds-spacing-4);
  left: var(--ds-spacing-4);
  transition-property: transform, opacity;
  transition-duration: 400ms;
  transition-timing-function: ease;
  opacity: var(--opacity);
  transform: translate3d(var(--x), 85px, 0);
  outline: none;
  border-radius: var(--ds-radius-3);
}

.ToastRoot:focus-visible {
  box-shadow: 0 0 0 2px var(--ds-color-primary-9);
}

.ToastRoot:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  width: 100%;
  height: 1000px;
  background: transparent;
}

.ToastRoot[data-front="true"] {
  transform: translate3d(var(--x), var(--y, 0), 0);
}

.ToastRoot[data-front="false"] {
  transform: translate3d(var(--x), var(--y, 0), 0) scale(var(--scale));
}

.ToastRoot[data-state="closed"] {
  animation: slideDown 350ms ease;
}

.ToastRoot[data-hidden="false"] {
  --opacity: 1;
}

.ToastRoot[data-hidden="true"] {
  --opacity: 0;
}

.ToastRoot[data-hovering="true"] {
  --scale: 1;
  --y: calc(var(--hover-offset-y) - var(--stack-gap) * var(--index));
  transition-duration: 350ms;
}

.ToastRoot[data-swipe="move"] {
  transition-duration: 0ms;
}

.ToastRoot[data-swipe="cancel"] {
  --x: 0;
}

.ToastRoot[data-swipe-direction="right"][data-swipe="end"] {
  animation: slideRight 150ms ease-out;
}

.ToastRoot[data-swipe-direction="left"][data-swipe="end"] {
  animation: slideLeft 150ms ease-out;
}

@keyframes slideDown {
  from {
    transform: translate3d(0, var(--y), 0);
  }

  to {
    transform: translate3d(0, 85px, 0);
  }
}

@keyframes slideRight {
  from {
    transform: translate3d(var(--radix-toast-swipe-end-x), var(--y), 0);
  }

  to {
    transform: translate3d(100%, var(--y), 0);
  }
}

@keyframes slideLeft {
  from {
    transform: translate3d(var(--radix-toast-swipe-end-x), var(--y), 0);
  }

  to {
    transform: translate3d(-100%, var(--y), 0);
  }
}

.ToastInner {
  padding: var(--ds-spacing-4) var(--ds-spacing-4) var(--ds-spacing-5)
    var(--ds-spacing-4);
  border-radius: var(--ds-radius-3);
  height: var(--height);
  background-color: var(--ds-default-white);
  box-shadow: var(--ds-shadow-3);
  display: grid;
  grid-template-areas: "title action" "description action";
  grid-template-columns: auto max-content;
  column-gap: var(--ds-spacing-3);
  align-items: start;
  position: relative;
  border: 1px solid var(--ds-color-neutral-3);
}

.ToastInner:not([data-status="default"]) {
  grid-template-areas: "icon title action" "icon description action";
  grid-template-columns: max-content auto max-content;
  column-gap: var(--ds-spacing-3);
}

.ToastInner:not([data-front="true"]) {
  height: var(--front-height);
}

.ToastRoot[data-hovering="true"] .ToastInner {
  height: var(--height);
}

.ToastInner[data-status="success"] {
  border-left: 4px solid var(--ds-color-success-9);
}

.ToastInner[data-status="error"] {
  border-left: 4px solid var(--ds-color-error-9);
}

.ToastTitle {
  grid-area: title;
  margin-bottom: var(--ds-spacing-2);
  font-weight: var(--ds-font-weight-medium);
  color: var(--ds-color-text-default);
  font-size: var(--ds-font-size-3);
  line-height: var(--ds-line-height-3);
}

.ToastDescription {
  grid-area: description;
  margin: 0;
  color: var(--ds-color-text-subtle);
  font-size: var(--ds-font-size-2);
  line-height: var(--ds-line-height-3);
  padding-bottom: var(--ds-spacing-2);
}

.ToastAction {
  grid-area: action;
}

.ToastClose {
  position: absolute;
  right: var(--ds-spacing-2);
  top: var(--ds-spacing-2);
  width: 20px;
  height: 20px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: transparent;
  color: var(--ds-color-neutral-11);
  transition: all 200ms ease;
  opacity: 0.7;
}

.ToastClose:hover {
  opacity: 1;
  background-color: var(--ds-color-neutral-3);
  color: var(--ds-color-neutral-12);
}

.ToastInner:hover .ToastClose {
  opacity: 0.7;
}

.Button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-weight: 500;
}

.Button.small {
  font-size: 12px;
  padding: 0 10px;
  line-height: 25px;
  height: 25px;
}

.Button.large {
  font-size: 15px;
  padding: 0 15px;
  line-height: 35px;
  height: 35px;
}

.Button.violet {
  background-color: white;
  color: var(--violet11);
  box-shadow: 0 2px 10px var(--blackA7);
}

.Button.violet:hover {
  background-color: var(--mauve3);
}

.Button.violet:focus {
  box-shadow: 0 0 0 2px black;
}

.Button.green {
  background-color: var(--green2);
  color: var(--green11);
  box-shadow: inset 0 0 0 1px var(--green7);
}

.Button.green:hover {
  box-shadow: inset 0 0 0 1px var(--green8);
}

.Button.green:focus {
  box-shadow: 0 0 0 2px var(--green8);
}

.checkmark {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--ds-color-success-9);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkmark::after {
  content: "";
  display: block;
  width: 9px;
  height: 6px;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(-45deg) translate(1px, -1px);
}

.error {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--ds-color-error-9);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error::before,
.error::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: white;
  border-radius: 1px;
}

.error::before {
  transform: rotate(45deg);
}

.error::after {
  transform: rotate(-45deg);
}

@keyframes circleAnimation {
  from {
    transform: scale(0) rotate(45deg);
    opacity: 0;
  }

  to {
    transform: scale(1) rotate(45deg);
    opacity: 1;
  }
}

@keyframes checkmarkAnimation {
  0% {
    height: 0;
    width: 0;
    opacity: 0;
  }

  40% {
    height: 0;
    width: 6px;
    opacity: 1;
  }

  100% {
    opacity: 1;
    height: 10px;
  }
}

@keyframes firstLineAnimation {
  from {
    transform: scale(0);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes secondLineAnimation {
  from {
    transform: scale(0) rotate(90deg);
    opacity: 0;
  }

  to {
    transform: scale(1) rotate(90deg);
    opacity: 1;
  }
}

import type { MetaFunction } from "@remix-run/node";
import OverviewSummary from "../components/summaries/Overview";
import OverviewSection from "../components/sections/Overview";
import SectionErrorBoundary from "../components/SectionErrorBoundary";

export const meta: MetaFunction = () => {
  return [{ title: "Augment Overview" }];
};

export const ErrorBoundary = () => {
  return <SectionErrorBoundary title="Overview" />;
};

export default function Overview() {
  return (
    <>
      <OverviewSummary
        title="At a glance"
        description="A summary of your organization's Augment usage"
      />
      <OverviewSection
        title="Overview"
        description="Augment's impact at your organization"
      />
    </>
  );
}

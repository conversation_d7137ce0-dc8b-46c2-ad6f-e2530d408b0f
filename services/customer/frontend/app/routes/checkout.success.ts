import { redirect } from "@remix-run/node";
import { withAuth } from "../.server/auth";
import { stripe } from "../.server/stripe";
import { logger } from "@augment-internal/logging";
import { AuthCentralClient } from "../.server/grpc/auth-central";

export const loader = withAuth(
  async ({ request, user }) => {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get("session_id");
    const planId = url.searchParams.get("plan_id");

    // Validate required parameters
    if (!sessionId || !planId) {
      logger.error("Missing required parameters", {
        sessionId,
        planId,
      });
      return redirect("/account");
    }

    try {
      // Get the user's Stripe customer ID from Auth Central
      const authCentralClient = AuthCentralClient.getInstance();
      const userResponse = await authCentralClient.getUser(user);

      if (!userResponse.user || !userResponse.user.stripeCustomerId) {
        logger.error(`No Stripe customer ID found for user ${user.userId}`);
        return redirect("/account");
      }

      const stripeCustomerId = userResponse.user.stripeCustomerId;

      // Retrieve the session from Stripe to validate it
      const session = await stripe.checkout.sessions.retrieve(sessionId);

      // Verify the session belongs to this user
      if (session.customer !== stripeCustomerId) {
        logger.error("Session customer ID doesn't match user's customer ID", {
          sessionCustomerId: session.customer,
          userCustomerId: stripeCustomerId,
        });
        return redirect("/account");
      }

      // Check if the session is complete
      if (session.status !== "complete") {
        logger.error("Session is not complete", {
          sessionStatus: session.status,
        });
        return redirect("/account");
      }

      // Call the putUserOnPlan method to trigger the plan change
      await AuthCentralClient.getInstance().putUserOnPlan(user, planId);
    } catch (error) {
      logger.error("Error processing payment validation:", error);
    }
    return redirect("/account");
  },
  {
    adminOnly: false,
  },
);

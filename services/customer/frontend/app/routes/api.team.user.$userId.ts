import { logger } from "@augment-internal/logging";
import { json } from "@remix-run/node";
import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import type {
  TeamUserDeleteSuccessSchema,
  TeamUserDeleteErrorSchema,
} from "../schemas/team-user-delete";
import { teamsAllowed } from "app/utils/team.server";

export const action = withAuthApi(async ({ request, params, user }) => {
  if (request.method !== "DELETE") {
    return json<TeamUserDeleteErrorSchema>(
      { message: "Method not allowed" },
      { status: 405 },
    );
  }

  if (!(await teamsAllowed(user))) {
    return json<TeamUserDeleteErrorSchema>(
      { message: "Team management is not enabled" },
      { status: 403 },
    );
  }

  try {
    const { userId } = params;
    const { tenantId } = user;

    if (!userId) {
      return json<TeamUserDeleteErrorSchema>(
        { message: "User ID is required" },
        { status: 400 },
      );
    }

    if (!tenantId) {
      return json<TeamUserDeleteErrorSchema>(
        { message: "User not associated with a tenant" },
        { status: 400 },
      );
    }

    const authCentralClient = AuthCentralClient.getInstance();

    // Check if the current user is an admin OR the target is the current user
    try {
      const currentUserOnTenant = await authCentralClient.getUserOnTenant(
        user,
        user.userId,
        tenantId,
      );

      if (
        !currentUserOnTenant.customerUiRoles.includes(CustomerUiRole.ADMIN) &&
        user.userId !== userId
      ) {
        logger.warn(
          `Non-admin user ${user.userId} attempted to remove user ${userId} from tenant ${tenantId}`,
        );
        return json<TeamUserDeleteErrorSchema>({}, { status: 403 });
      }
    } catch (error) {
      logger.error(
        `Failed to check admin status for user ${user.userId} on tenant ${tenantId}: ${error}`,
      );
      return json<TeamUserDeleteErrorSchema>(
        { message: "Internal Server Error" },
        { status: 500 },
      );
    }

    // Get all users in the tenant to check if target user exists and count admins
    let tenantUsers;
    try {
      tenantUsers = await authCentralClient.listTenantUsers(user, tenantId);
    } catch (error) {
      logger.error(`Failed to list users for tenant ${tenantId}: ${error}`);
      return json<TeamUserDeleteErrorSchema>(
        { message: "Internal Server Error" },
        { status: 500 },
      );
    }

    const targetUserExists = tenantUsers.users.some(
      (tenantUser) => tenantUser.id === userId,
    );

    if (!targetUserExists) {
      logger.warn(`User ${userId} not found in tenant ${tenantId}`);
      return json<TeamUserDeleteErrorSchema>({}, { status: 404 });
    }

    // Count admins and check if target user is an admin
    let adminCount = 0;
    let isTargetUserAdmin = false;

    for (const tenantUser of tenantUsers.users) {
      try {
        const userOnTenant = await authCentralClient.getUserOnTenant(
          user,
          tenantUser.id,
          tenantId,
        );

        const isAdmin = userOnTenant.customerUiRoles.includes(
          CustomerUiRole.ADMIN,
        );

        if (isAdmin) {
          adminCount++;
        }

        if (tenantUser.id === userId && isAdmin) {
          isTargetUserAdmin = true;
        }
      } catch (error) {
        logger.warn(
          `Failed to get roles for user ${tenantUser.id} on tenant ${tenantId}: ${error}`,
        );
        // Continue checking other users
      }
    }

    // If the target user is the last admin, prevent removal
    if (isTargetUserAdmin && adminCount === 1) {
      logger.warn(
        `Attempted to remove the last admin ${userId} from tenant ${tenantId}`,
      );
      return json<TeamUserDeleteErrorSchema>({}, { status: 400 });
    }

    // Remove the user from the tenant
    try {
      await authCentralClient.removeUserFromTenant(user, userId, tenantId);
      logger.info(`User ${userId} removed from tenant ${tenantId}`);
      return json<TeamUserDeleteSuccessSchema>({}, { status: 200 });
    } catch (error) {
      logger.error(
        `Failed to remove user ${userId} from tenant ${tenantId}: ${error}`,
      );
      return json<TeamUserDeleteErrorSchema>(
        { message: "Internal Server Error" },
        { status: 500 },
      );
    }
  } catch (error) {
    logger.error(`Error removing user from tenant: ${error}`);
    return json<TeamUserDeleteErrorSchema>(
      { message: "Internal Server Error" },
      { status: 500 },
    );
  }
});

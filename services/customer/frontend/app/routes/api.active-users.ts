import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { getAuthenticatedApiData, getDateFilters } from "../utils/api";

import { cachingClient } from "../.server/grpc/request-insights-analytics";
import { json } from "@remix-run/node";

export const loader = withAuthApi(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const { activeUsers } = await getAuthenticatedApiData(
      user,
      {
        tenantId,
        dateFilters,
      },
      cachingClient.getActiveUsers.bind(cachingClient),
    );

    return json(activeUsers);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);

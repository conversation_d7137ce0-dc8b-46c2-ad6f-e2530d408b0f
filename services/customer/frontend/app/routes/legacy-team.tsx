import { redirect, json } from "@remix-run/node";
import { Container, Flex, Heading } from "@radix-ui/themes";
import { useLoaderData } from "@remix-run/react";
import { withAuth } from "../.server/auth";
import { StatusContent } from "./integration.callbacks";
import EmailLogoutHeader from "../components/navigation/EmailLogoutHeader";
import { logger } from "@augment-internal/logging";
import { isUserInLegacySelfServeTeam } from "app/utils/team.server";

export const loader = withAuth(
  async ({ user }) => {
    try {
      // If not legacy self-serve team, redirect to account page
      if (!(await isUserInLegacySelfServeTeam(user))) {
        logger.info("Not legacy self serve team");
        return redirect("/account");
      }

      // Legacy self-serve team users should see this page
      logger.info("Legacy self serve team page");
      return json({ email: user.email });
    } catch (error) {
      logger.error("Error in legacy-team loader:", error);
      return json({ email: "" });
    }
  },
  {
    adminOnly: false,
    enterpriseOnly: false, // We'll handle the redirect ourselves
  },
);

export default function LegacyTeam() {
  const { email } = useLoaderData<typeof loader>();

  return (
    <Container size="2">
      <EmailLogoutHeader email={email} />
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          Team Plan
        </Heading>
        <StatusContent
          status="augment"
          title=""
          description="You are a member of a team"
          listItems={{
            title: "What you can do",
            items: [
              "Use Augment in the VSCode, Jetbrains, and Vim extensions",
              "If you need help managing your team, please contact support",
            ],
          }}
          footer={{
            linkText: "",
            linkHref: "",
          }}
        />
      </Flex>
    </Container>
  );
}

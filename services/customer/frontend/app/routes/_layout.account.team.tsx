import type { MetaFunction } from "@remix-run/node";
import { TeamPage } from "../components/account/TeamPage/TeamPage";
import { withAuth } from "app/.server/auth";
import { redirect } from "@remix-run/node";
import { json } from "@remix-run/router";
import { teamsAllowed } from "app/utils/team.server";

export const meta: MetaFunction = () => {
  return [{ title: "Augment Team" }];
};

// redirect to account/subscription if team_management feature flag is not enabled or user is enterprise
export const loader = withAuth(
  async ({ user }) => {
    const showTeamManagement = await teamsAllowed(user);

    if (!showTeamManagement) {
      return redirect("/account");
    }
    return json({}); // Using the json function from @remix-run/node
  },
  {
    adminOnly: false,
    entry: "/account",
  },
);

export default function Team() {
  return <TeamPage />;
}

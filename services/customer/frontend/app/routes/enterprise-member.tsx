import { redirect, json } from "@remix-run/node";
import { Container, Flex, Heading } from "@radix-ui/themes";
import { useLoaderData } from "@remix-run/react";
import { withAuth, isEnterpriseTier } from "../.server/auth";
import { StatusContent } from "./integration.callbacks";
import EmailLogoutHeader from "../components/navigation/EmailLogoutHeader";
import { logger } from "@augment-internal/logging";

export const loader = withAuth(
  async ({ user }) => {
    try {
      // Check if user is on enterprise tier
      const isEnterprise = await isEnterpriseTier(user);

      // If not enterprise, redirect to account page
      if (!isEnterprise) {
        return redirect("/account");
      }

      // Enterprise non-admin users should see this page
      return json({ email: user.email });
    } catch (error) {
      logger.error("Error in enterprise-member loader:", error);
      return json({ email: "" });
    }
  },
  {
    adminOnly: false,
    enterpriseOnly: false, // We'll handle the redirect ourselves
  },
);

export default function EnterpriseMember() {
  const { email } = useLoaderData<typeof loader>();

  return (
    <>
      <EmailLogoutHeader email={email} />
      <Container size="2">
        <Flex direction="column" align="center" mt="9">
          <Heading size="6" mb="4">
            Enterprise Plan
          </Heading>
          <StatusContent
            status="augment"
            title=""
            description="You are a member of an enterprise organization"
            listItems={{
              title: "What you can do",
              items: [
                "Use Augment in the VSCode, Jetbrains, and Vim extensions",
                "Contact your sales representative with any questions regarding Augment",
              ],
            }}
            footer={{
              linkText: "Install Augment",
              linkHref:
                "https://docs.augmentcode.com/introduction#get-started-in-minutes",
            }}
          />
        </Flex>
      </Container>
    </>
  );
}

import type { LoaderFunctionArgs } from "@remix-run/node";
import { authenticator, type SessionUser } from "../.server/auth";
import { Config } from "../.server/config";
import { RequestInsightPublisher } from "../.server/publisher/request-insight-publisher";
import {
  CustomerUISessionEvent_SessionEnd_Reason,
  TenantInfo,
} from "~services/request_insight/request_insight_pb";
import { logger } from "@augment-internal/logging";
import { getFeatureFlag } from "../feature-flags/feature-flags.server";

const requestInsightPublisher = new RequestInsightPublisher(
  Config.REQUEST_INSIGHT_PROJECT_ID,
  Config.REQUEST_INSIGHT_TOPIC_NAME,
);

/**
 * Handles user logout requests. Supports both cookie and non-cookie based auth flows.
 * Accepts an optional 'reason' query param for timeout-based logouts.
 */
export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const reason = url.searchParams.get("reason");
  const logoutReason =
    reason === "timeout"
      ? CustomerUISessionEvent_SessionEnd_Reason.TIMEOUT
      : CustomerUISessionEvent_SessionEnd_Reason.LOGOUT;

  const user = await authenticator.isAuthenticated(request);
  await publishUserSessionEnd(user, logoutReason);

  logger.info("Using cookie auth logout flow");
  return handleLogout(request);
}

/**
 * Redirects user through logout endpoint auth-central to get rid of any sessions there
 */
async function handleLogout(request: Request) {
  // Get the auth-central URL from your config
  const authCentralUrl = Config.AUTH_LOGOUT_ENDPOINT;

  // Instead of making a server-side fetch, we'll return a Response that redirects the browser
  // to the auth-central logout endpoint, then back to our local logout route
  const localLogoutUrl = new URL(request.url);
  localLogoutUrl.protocol = "https:";
  localLogoutUrl.pathname = "/logout/complete";

  const logoutUrl = new URL(authCentralUrl);
  logoutUrl.searchParams.set("redirect_uri", localLogoutUrl.toString());
  logoutUrl.searchParams.set("client_id", Config.AUTH_CLIENT_ID);

  logger.info("Logout redirect details:", {
    localLogoutUrl: localLogoutUrl.toString(),
    authCentralLogoutUrl: logoutUrl.toString(),
    clientId: Config.AUTH_CLIENT_ID,
  });

  return new Response(null, {
    status: 302,
    headers: {
      Location: logoutUrl.toString(),
    },
  });
}

/**
 * Records user session end event with the specified reason
 * Handles missing user case and logs any publishing errors
 */
async function publishUserSessionEnd(
  user: SessionUser | null,
  reason: CustomerUISessionEvent_SessionEnd_Reason,
) {
  if (!user) {
    logger.warn("No authenticated user found during logout");
    return;
  }

  try {
    // Check if the feature flag is enabled
    const isEnabled = await getFeatureFlag("publish_user_session_events", {
      tenantId: user.tenantId,
      shardNamespace: user.shardNamespace,
    });

    if (!isEnabled) {
      logger.info(
        `User session event publishing is disabled by feature flag for user ${user.userId}.`,
      );
      return;
    }

    const tenantInfo = new TenantInfo({
      tenantId: user.tenantId,
      tenantName: user.tenantName,
    });

    await requestInsightPublisher.publishEndSessionEvent(
      user.sessionId,
      tenantInfo,
      user.userId,
      reason,
    );

    logger.info(
      `Published session end event for user ${user.userId} with reason ${reason}.`,
    );
  } catch (error) {
    logger.error(`Failed to publish session end event:`, {
      error: error instanceof Error ? error.message : String(error),
      userId: user.userId,
    });
  }
}

import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { supabaseProcessorClient } from "../.server/grpc/supabase-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");

  if (!code) {
    return json(
      { error: "Missing Supabase code query param" },
      { status: 400 },
    );
  }

  try {
    const response = await getAuthenticatedApiData(
      user,
      { code },
      supabaseProcessorClient.hydrateSupabaseSettings,
    );

    // Check the status code from the response
    // gRPC status codes: 0 = OK, 3 = INVALID_ARGUMENT, 7 = PERMISSION_DENIED, 13 = INTERNAL, 16 = UNAUTHENTICATED, etc.
    if (response.statusCode !== 0) {
      let errorMessage = "Failed to connect your Supabase account";
      let httpStatusCode = 500; // Default HTTP status code

      // Map gRPC status codes to more specific error messages and HTTP status codes
      switch (response.statusCode) {
        case 3: // INVALID_ARGUMENT
          errorMessage = "Invalid Supabase authorization code";
          httpStatusCode = 400;
          break;
        case 16: // UNAUTHENTICATED
          errorMessage = "Supabase authentication is not configured properly";
          httpStatusCode = 401;
          break;
        case 7: // PERMISSION_DENIED
          errorMessage =
            "You don't have permission to connect this Supabase account";
          httpStatusCode = 403;
          break;
        case 13: // INTERNAL
          errorMessage =
            "Internal server error while connecting your Supabase account";
          httpStatusCode = 500;
          break;
        case 14: // UNAVAILABLE
          errorMessage = "Supabase service is currently unavailable";
          httpStatusCode = 503;
          break;
      }

      return json({ error: errorMessage }, { status: httpStatusCode });
    }

    return json({ success: true });
  } catch (error) {
    console.error("Error processing Supabase callback:", error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
};

export default function SupabaseCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;

  const getContent = () => {
    if (data.success) {
      return (
        <StatusContent
          status="success"
          title="All set!"
          description="Your Supabase account has been successfully connected."
          listItems={{
            title: "Next steps",
            items: [
              "Use Supabase with Augment to access your database and projects",
              "Configure your Supabase integration in the Augment Extension settings page",
            ],
          }}
          footer={{
            linkText: "Return to Augment",
            linkHref: "https://docs.augmentcode.com/using-augment/agent",
          }}
        />
      );
    }

    // Determine appropriate help items based on the error
    const getHelpItems = () => {
      if (data.error?.includes("not configured")) {
        return [
          "Check your Supabase configuration",
          "Ensure your Supabase project is properly set up",
          "Contact support for assistance with configuration",
        ];
      } else if (data.error?.includes("authorization code")) {
        return [
          "Try connecting again",
          "Make sure you're using a valid authorization flow",
          "Check that your session hasn't expired",
        ];
      } else if (data.error?.includes("service is currently unavailable")) {
        return [
          "Try again in a few minutes",
          "Check Augment status page for any outages",
          "Contact support if the issue persists",
        ];
      }

      // Default help items
      return [
        "Try connecting again",
        "Check that you have the correct permissions",
        "Contact support if the issue persists",
      ];
    };

    return (
      <StatusContent
        status="error"
        title="Connection Failed"
        description={
          data.error ||
          "An error occurred while connecting your Supabase account."
        }
        listItems={{
          title: "What you can do",
          items: getHelpItems(),
        }}
        footer={{
          linkText: "Return to Augment",
          linkHref: "/account",
        }}
      />
    );
  };

  return (
    <Container size="1">
      <Flex
        direction="column"
        align="center"
        justify="center"
        gap="4"
        style={{ minHeight: "80vh" }}
      >
        <Heading align="center" size="6">
          Supabase Integration
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

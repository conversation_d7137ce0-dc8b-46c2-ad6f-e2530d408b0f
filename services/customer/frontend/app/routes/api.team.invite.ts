import { logger } from "@augment-internal/logging";
import { json } from "@remix-run/node";
import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { InviteUsersToTenantResponse_InvitationCreationStatus_Status } from "~services/auth/central/server/auth_pb";
import {
  TeamInviteRequestSchema,
  type TeamInviteSuccessSchema,
  type TeamInvitePartialSuccessSchema,
  type TeamInviteErrorSchema,
} from "../schemas/team-invite";
import { ensureTeamForUser, teamsAllowed } from "../utils/team.server";
import { updateSessionWithTenantAndRoles } from "app/utils/session.server";

export const action = withAuthApi(async ({ request, user }) => {
  if (request.method !== "POST") {
    return json<TeamInviteErrorSchema>(
      { message: "Method not allowed" },
      { status: 405 },
    );
  }

  if (!(await teamsAllowed(user))) {
    return json<TeamInviteErrorSchema>(
      { message: "Team management is not enabled" },
      { status: 403 },
    );
  }

  try {
    const tenantId = await ensureTeamForUser(user);

    // Update session and get headers if needed
    const { headers } = await updateSessionWithTenantAndRoles(
      request,
      user,
      tenantId,
    );

    const authCentralClient = AuthCentralClient.getInstance();

    // Parse and validate the request body
    let requestData;
    try {
      const body = await request.json();
      requestData = TeamInviteRequestSchema.parse(body);
    } catch (error) {
      logger.error(`Invalid request body: ${error}`);
      return json<TeamInviteErrorSchema>(
        { message: "Invalid request format" },
        { status: 400 },
      );
    }

    if (requestData.emails.length === 0) {
      return json<TeamInviteErrorSchema>(
        { message: "No emails provided" },
        { status: 400 },
      );
    }

    // Invite users to the tenant
    try {
      const response = await authCentralClient.inviteUsersToTenant(
        user,
        requestData.emails,
        tenantId,
      );

      const failedInvitations = response.invitationStatuses.filter(
        (status) =>
          status.status !==
          InviteUsersToTenantResponse_InvitationCreationStatus_Status.SUCCESS,
      );

      if (failedInvitations.length === 0) {
        logger.info(
          `All invitations sent successfully for tenant ${tenantId} by user ${user.userId}`,
        );
        return json<TeamInviteSuccessSchema>(
          {},
          {
            status: 200,
            headers,
          },
        );
      } else if (failedInvitations.length < requestData.emails.length) {
        const failedEmails = failedInvitations.map((status) => status.email);
        logger.warn(
          `Some invitations failed for tenant ${tenantId}: ${failedEmails.join(", ")}`,
        );
        return json<TeamInvitePartialSuccessSchema>(
          { failed: failedEmails },
          {
            status: 207,
            headers,
          },
        );
      } else {
        const failedEmails = failedInvitations.map((status) => status.email);
        logger.error(
          `All invitations failed for tenant ${tenantId}: ${failedEmails.join(", ")}`,
        );
        return json<TeamInvitePartialSuccessSchema>(
          { failed: failedEmails },
          {
            status: 400,
            headers,
          },
        );
      }
    } catch (error) {
      logger.error(`Failed to invite users to tenant ${tenantId}: ${error}`);
      return json<TeamInviteErrorSchema>(
        { message: "Internal Server Error" },
        { status: 500 },
      );
    }
  } catch (error) {
    logger.error(`Error inviting users to tenant: ${error}`);
    return json<TeamInviteErrorSchema>(
      { message: "Internal Server Error" },
      { status: 500 },
    );
  }
});

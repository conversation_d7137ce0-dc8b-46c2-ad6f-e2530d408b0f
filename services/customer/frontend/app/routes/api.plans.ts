import { json } from "@remix-run/node";
import { logger } from "@augment-internal/logging";
import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { getAllPlans } from "../utils/plans.server";

export const loader = withAuthApi(
  async ({ user }) => {
    try {
      // Get all available plans for the user
      const plans = await getAllPlans(user);

      return json(plans);
    } catch (error) {
      logger.error("Error retrieving plans:", error);

      // Return error
      return json({
        error,
      });
    }
  },
  {
    // This endpoint doesn't require admin privileges
    adminOnly: false,
  },
);

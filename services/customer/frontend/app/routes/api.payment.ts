import type { LoaderFunctionArgs } from "@remix-run/node";
import { type SessionUser, with<PERSON>uth<PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { logger } from "@augment-internal/logging";
import type { GetUserOrbPaymentInfoResponse } from "~services/auth/central/server/auth_pb";

export const loader = withAuth<PERSON>pi(
  async ({ user }: LoaderFunctionArgs & { user: SessionUser }) => {
    const authCentralClient = AuthCentralClient.getInstance();
    logger.info(
      `Calling getUserOrbPaymentInfo for user: ${user.userId}, tenant: ${user.tenantId}`,
    );

    try {
      const response: GetUserOrbPaymentInfoResponse =
        await authCentralClient.getUserOrbPaymentInfo(user);
      return Response.json(response);
    } catch (error) {
      logger.error(
        `Failed to get user orb payment info for user: ${user.userId}, tenant: ${user.tenantId}:`,
        error,
      );
      return Response.json(
        { message: "Internal Server Error" },
        { status: 500 },
      );
    }
  },
  {
    adminOnly: false,
    enterpriseOnly: false,
  },
);

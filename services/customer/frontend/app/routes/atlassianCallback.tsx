import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { AtlassianProcessorClient } from "../.server/grpc/atlassian-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";
import { AtlassianService } from "~services/integrations/atlassian/atlassian_pb";

const atlassianClient = new AtlassianProcessorClient();

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");

  if (!code) {
    return json(
      { error: "Missing atlassian authentication query param" },
      { status: 400 },
    );
  }

  try {
    const response = await getAuthenticatedApiData(
      user,
      { code },
      atlassianClient.hydrateAtlassianUserSettings,
    );

    return json({
      success: true,
      service: response.atlassianService,
    });
  } catch (error) {
    console.error("Error processing Atlassian callback:", error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
  service?: AtlassianService;
};

export default function AtlassianCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;

  const getServiceContent = (service?: AtlassianService) => {
    const serviceName =
      service === AtlassianService.JIRA
        ? "Jira"
        : service === AtlassianService.CONFLUENCE
          ? "Confluence"
          : "Atlassian";

    return {
      title: `${serviceName} Integration`,
      description: `Your ${serviceName} account has been successfully connected.`,
      items: [
        `Return to the IDE and start using the ${serviceName} integration`,
        `Ask questions about your ${serviceName} content with the agent`,
      ],
    };
  };

  const getContent = () => {
    if (data.success) {
      const content = getServiceContent(data.service);
      return (
        <StatusContent
          status="success"
          title="All set!"
          description={content.description}
          listItems={{
            title: "Next steps",
            items: content.items,
          }}
          footer={{
            linkText: "Agent tools guide",
            linkHref: "https://docs.augmentcode.com/using-augment/agent",
          }}
        />
      );
    }

    return (
      <StatusContent
        status="error"
        title="Installation Failed"
        description={
          data.error ||
          "An error occurred while setting up your Atlassian integration."
        }
        listItems={{
          title: "What you can do",
          items: [
            "Try the installation process again",
            "Check your Atlassian permissions",
            "Contact support if the issue persists",
          ],
        }}
        footer={{
          linkText: "Agent tools guide",
          linkHref: "https://docs.augmentcode.com/using-augment/agent",
        }}
      />
    );
  };

  return (
    <Container size="2">
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          {getServiceContent(data.service).title}
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

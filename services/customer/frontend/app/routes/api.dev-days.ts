import { withAuth<PERSON><PERSON> } from "../.server/auth";
import {
  cacheableJson,
  getAuthenticatedApiData,
  getDateFilters,
} from "../utils/api";
import { cachingClient } from "../.server/grpc/request-insights-analytics";

export const loader = withAuthApi(
  async ({ request, user }) => {
    const { tenantId } = user;
    const dateFilters = getDateFilters(request);

    const { devDays } = await getAuthenticatedApiData(
      user,
      {
        tenantId,
        dateFilters,
      },
      cachingClient.getDevDays.bind(cachingClient),
    );

    return cacheableJson(devDays);
  },
  {
    adminOnly: true,
    enterpriseOnly: true,
  },
);

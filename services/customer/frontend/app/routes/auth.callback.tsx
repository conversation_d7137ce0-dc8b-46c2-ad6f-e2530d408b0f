import { type LoaderFunctionArgs, redirect, json } from "@remix-run/node";
import { AuthorizationError } from "remix-auth";
import { OAuth2Error } from "remix-auth-oauth2";

import {
  authenticator,
  type SessionUser,
  isAdmin,
  isEnterpriseTier,
} from "../.server/auth";
import { Config } from "../.server/config";
import { useLoaderData, Link } from "@remix-run/react";
import { Button, Flex, Card, Heading, Text, Container } from "@radix-ui/themes";

import { logger } from "@augment-internal/logging";
import { TenantInfo } from "~services/request_insight/request_insight_pb";
import { RequestInsightPublisher } from "../.server/publisher/request-insight-publisher";
import { getSession, sessionStorage } from "../.server/session";
import { getFeatureFlag } from "../feature-flags/feature-flags.server";

const requestInsightPublisher = new RequestInsightPublisher(
  Config.REQUEST_INSIGHT_PROJECT_ID,
  Config.REQUEST_INSIGHT_TOPIC_NAME,
);

/**
 * Handles OAuth callback after successful authentication.
 * Validates tenant URL, establishes user session, and tracks login events.
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request.headers.get("cookie"));

  try {
    const user = await authenticator.authenticate("auth-central", request, {
      throwOnError: true,
    });
    session.set(authenticator.sessionKey, user);

    const redirectToUrl =
      session.get("redirectTo") ||
      ((await isAdmin(user)) && (await isEnterpriseTier(user))
        ? "/dashboard/overview"
        : "/account");

    // Publish user session start event without blocking
    publishUserSessionStart(user, redirectToUrl);

    // We've consumed any redirect URI - don't leave it
    // around to confuse future login requests.
    session.unset("redirectTo");

    return redirect(redirectToUrl, {
      headers: {
        "Set-Cookie": await sessionStorage.commitSession(session),
      },
    });
  } catch (err) {
    let message = "An error occurred during authentication.";

    logger.error("Rendering error page due to error: ", err);

    let error = err;

    if (error instanceof Error) {
      message = error.message;
    }

    if (error instanceof AuthorizationError) {
      error = error.cause;
    }

    if (error instanceof OAuth2Error) {
      message = `Details: ${error.message} ${error.description || ""} ${error.uri || ""}`;
    }

    return json({
      error: message,
    });
  }
};

/**
 * Records user session start event and tenant information
 */
async function publishUserSessionStart(
  user: SessionUser,
  redirectUrl: string,
): Promise<void> {
  try {
    // Check if the feature flag is enabled
    const isEnabled = await getFeatureFlag("publish_user_session_events", {
      tenantId: user.tenantId,
      shardNamespace: user.shardNamespace,
    });

    if (!isEnabled) {
      logger.info(
        `User session event publishing is disabled by feature flag for user ${user.userId}.`,
      );
      return;
    }

    logger.info(
      `Publishing user session start event for user ${user.userId} in tenant ${user.tenantId}.`,
    );

    const tenantInfo = new TenantInfo({
      tenantId: user.tenantId,
      tenantName: user.tenantName,
    });

    await requestInsightPublisher.publishStartSessionEvent(
      user.sessionId,
      tenantInfo,
      user.userId,
      redirectUrl,
    );
  } catch (error) {
    logger.error(`Failed to publish session start event:`, {
      error: error instanceof Error ? error.message : String(error),
      userId: user.userId,
      tenantId: user.tenantId,
      redirectUrl: redirectUrl,
    });
  }
}

export default function AuthCallback() {
  const { error } = useLoaderData<typeof loader>();
  const currentTime = new Date().toISOString();

  return (
    <Container size="2">
      <Flex
        direction="column"
        align="center"
        justify="center"
        gap="4"
        style={{ minHeight: "80vh" }}
      >
        <Card size="3" style={{ maxWidth: "500px", width: "100%" }}>
          <Flex direction="column" align="center" gap="4" p="4">
            <img
              src="/augie-error.svg"
              alt="Authentication Error"
              width="64"
              height="64"
            />
            <Heading size="5" align="center">
              Authentication Failed
            </Heading>
            <Text align="center">
              {error || "An error occurred during authentication."}
            </Text>
            <Text size="1" color="gray" align="center">
              Time: {currentTime}
            </Text>
            <Link to="/login">
              <Button size="3" variant="solid">
                Try Again
              </Button>
            </Link>
          </Flex>
        </Card>
      </Flex>
    </Container>
  );
}

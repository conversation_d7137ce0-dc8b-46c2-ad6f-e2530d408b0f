import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { NotionProcessorClient } from "../.server/grpc/notion-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";

const notionProcessorClient = new NotionProcessorClient();

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const error = url.searchParams.get("error");

  if (error) {
    if (error == "access_denied") {
      return json({ error: "User cancelled the request" }, { status: 400 });
    }
    return json(
      { error: "An unexpected error occurred: " + error },
      { status: 400 },
    );
  }

  if (!code) {
    return json(
      { error: "Missing notion authentication query param" },
      { status: 400 },
    );
  }

  try {
    await getAuthenticatedApiData(
      user,
      { code },
      notionProcessorClient.hydrateNotionSettings,
    );
    return json({ success: true });
  } catch (error) {
    console.error("Error processing Notion callback:", error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
};

export default function NotionCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;
  const getContent = () => {
    if (data.success) {
      return (
        <StatusContent
          status="success"
          title="All set!"
          description="Your Notion account has been successfully connected."
          listItems={{
            title: "Next steps",
            items: [
              "Return to your IDE and start using the Notion integration",
              "Ask questions about processes, plans, or guidelines contained within your Notion",
            ],
          }}
          footer={{
            linkText: "Agent tools guide",
            linkHref: "https://docs.augmentcode.com/using-augment/agent",
          }}
        />
      );
    }

    return (
      <StatusContent
        status="error"
        title="Installation Failed"
        description={
          data.error ||
          "An error occurred while setting up your Notion integration."
        }
        listItems={{
          title: "What you can do",
          items: [
            "Try the installation process again",
            "Check your Notion permissions",
            "Contact support if the issue persists",
          ],
        }}
        footer={{
          linkText: "Agent tools guide",
          linkHref: "https://docs.augmentcode.com/using-augment/agent",
        }}
      />
    );
  };
  return (
    <Container size="2">
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          Notion Integration
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

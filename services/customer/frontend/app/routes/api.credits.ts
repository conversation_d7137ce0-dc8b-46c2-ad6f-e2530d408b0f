import type { LoaderFunctionArgs } from "@remix-run/node";
import { type SessionUser, with<PERSON><PERSON><PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { logger } from "@augment-internal/logging";
import type { GetUserOrbCreditsInfoResponse } from "~services/auth/central/server/auth_pb";
import { z } from "zod";

export const loader = withAuth<PERSON><PERSON>(
  async ({ user }: LoaderFunctionArgs & { user: SessionUser }) => {
    const authCentralClient = AuthCentralClient.getInstance();
    logger.info(
      `Calling getUserOrbCreditsInfo for user: ${user.userId}, tenant: ${user.tenantId}`,
    );

    try {
      const response: GetUserOrbCreditsInfoResponse =
        await authCentralClient.getUserOrbCreditsInfo(user);

      // Returning the whole response object.
      // The client can then select the specific fields it needs.
      return Response.json(response);
    } catch (error) {
      logger.error(
        `Failed to get user orb credits info for user: ${user.userId}, tenant: ${user.tenantId}:`,
        error,
      );
      return Response.json(
        { message: "Internal Server Error" },
        { status: 500 },
      );
    }
  },
  {
    adminOnly: false,
    enterpriseOnly: false,
  },
);

export const action = withAuthApi(
  async ({ user, request }) => {
    if (request.method !== "POST") {
      return Response.json({ error: "Method not allowed" }, { status: 405 });
    }

    const authCentralClient = AuthCentralClient.getInstance();

    try {
      const formData = await request.formData();
      const credits = formData.get("credits");
      z.number().positive().gte(100).lte(1000).parse(Number(credits));
      if (!credits) {
        return Response.json(
          { error: "Missing credits parameter" },
          { status: 400 },
        );
      }

      await authCentralClient.purchaseCredits(user, Number(credits));
    } catch (error) {
      console.error("Error purchasing credits:", error);
      return Response.json(
        { error: "Failed to purchase credits" },
        { status: 500 },
      );
    }

    return Response.json({});
  },
  {
    adminOnly: true,
  },
);

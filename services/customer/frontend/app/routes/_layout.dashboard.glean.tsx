import {
  json,
  type LoaderFunction,
  type ActionFunction,
} from "@remix-run/node";
import { withAuth } from "../.server/auth";
import GleanSetupSection from "../components/glean/GleanSetup";
import { GleanProcessorClient } from "../.server/grpc/glean-processor";
import { getAuthenticatedApiData } from "../utils/api";

import { Container } from "@radix-ui/themes";

export interface LoaderData {
  settings?: {
    gleanDomain: string;
    publicKey?: string;
  };
}

const gleanClient = new GleanProcessorClient();

export const loader: LoaderFunction = withAuth(
  async ({ user }) => {
    try {
      const response = await getAuthenticatedApiData(
        user,
        {},
        gleanClient.getGleanTenantSettings,
      );

      return json<LoaderData>({
        settings: {
          gleanDomain: response.gleanDomain,
          publicKey: response.publicKey,
        },
      });
    } catch (error) {
      console.error("Error fetching Glean settings:", error);
      return json<LoaderData>({});
    }
  },
  {
    adminOnly: true,
    entry: "/dashboard/overview",
    enterpriseOnly: true,
  },
);

export const action: ActionFunction = withAuth(
  async ({ request, user }) => {
    if (request.method !== "POST") {
      return json({ error: "Method not allowed" }, { status: 405 });
    }

    try {
      const formData = await request.formData();
      const action = formData.get("action");

      if (action === "generateKeyPair") {
        // Call the GenerateNewKeyPair endpoint
        try {
          const response = await getAuthenticatedApiData(
            user,
            {},
            gleanClient.generateNewKeyPair,
          );

          return json({ success: true, publicKey: response.publicKey });
        } catch (error) {
          console.error("Error generating new key pair:", error);
          return json(
            {
              error: "Failed to generate new key pair. Please try again.",
            },
            {
              status: 500,
            },
          );
        }
      } else if (action === "updateSettings") {
        // Handle regular settings update
        const settingsStr = formData.get("settings") as string;
        const decodedSettings = decodeURIComponent(settingsStr);
        const settings = JSON.parse(decodedSettings);

        await getAuthenticatedApiData(
          user,
          settings,
          gleanClient.hydrateGleanTenantSettings,
        );

        return json({ success: true });
      } else {
        return json({ error: "Invalid action" }, { status: 400 });
      }
    } catch (error) {
      console.error("Error updating Glean settings:", error);
      return json(
        {
          error:
            "Failed to update settings. Please check your configuration and try again.",
        },
        {
          status: 500,
        },
      );
    }
  },
  {
    adminOnly: true,
    entry: "/dashboard/overview",
    enterpriseOnly: true,
  },
);

export default function GleanSetup() {
  return (
    <Container p={{ initial: "4", md: "8" }}>
      <GleanSetupSection />
    </Container>
  );
}

import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { SlackProcessorClient } from "../.server/grpc/slack-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";

const slackProcessorClient = new SlackProcessorClient();

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");

  if (!code) {
    return json(
      { error: "Missing slack authentication query param" },
      { status: 400 },
    );
  }

  try {
    await getAuthenticatedApiData(
      user,
      { code },
      slackProcessorClient.hydrateSlackSettings,
    );
    return json({ success: true });
  } catch (error) {
    console.error("Error processing Slack callback:", error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
};

export default function SlackCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;

  const getContent = () => {
    if (data.success) {
      return (
        <StatusContent
          status="success"
          title="All set!"
          description="Your Slack workspace has been successfully connected."
          listItems={{
            title: "Next steps",
            items: [
              "Install the GitHub app for full code context if you haven't already",
              "Add @Augment to your engineering and team channels",
              "Announce Augment to your team!",
              "Start asking questions in channels or DMs",
            ],
          }}
          footer={{
            linkText: "Learn how to use Augment in Slack",
            linkHref: "https://docs.augmentcode.com/using-augment/slack",
          }}
        />
      );
    }

    return (
      <StatusContent
        status="error"
        title="Installation Failed"
        description={
          data.error ||
          "An error occurred while setting up your Slack integration."
        }
        listItems={{
          title: "What you can do",
          items: [
            "Try the installation process again",
            "Verify that you have an enterprise Augment subscription, as the Slack integration is only available to enterprise customers",
            "Check your Slack permissions",
            "Contact support if the issue persists",
          ],
        }}
        footer={{
          linkText: "Return to setup guide",
          linkHref:
            "https://docs.augmentcode.com/setup-augment/install-slack-app",
        }}
      />
    );
  };

  return (
    <Container size="2">
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          Slack Integration
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

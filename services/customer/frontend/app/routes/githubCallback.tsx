import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { GithubProcessorClient } from "../.server/grpc/github-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";

const githubProcessorClient = new GithubProcessorClient();

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const setupAction = url.searchParams.get("setup_action");
  const code = url.searchParams.get("code");

  if (!code) {
    return json({ error: "Missing github code query param" }, { status: 400 });
  }
  if (!setupAction) {
    return json(
      { error: "Missing github setup_action query param" },
      { status: 400 },
    );
  }

  switch (setupAction) {
    case "install": {
      const installationIdStr = url.searchParams.get("installation_id");
      if (!installationIdStr) {
        return json(
          { error: "Missing github installation_id query param" },
          { status: 400 },
        );
      }
      let installationId: bigint;
      try {
        installationId = BigInt(installationIdStr);
      } catch (e) {
        return json(
          { error: "Incorrect github installation_id query param" },
          { status: 400 },
        );
      }

      try {
        await getAuthenticatedApiData(
          user,
          { installationId, code },
          githubProcessorClient.hydrateGithubSettings,
        );
        return json({ success: true });
      } catch (error) {
        console.error(
          "Error processing Github app installation callback:",
          error,
        );
        return json({ success: false }, { status: 500 });
      }
    }
    case "request":
      return json({
        status: "pending",
        message: "Installation request pending admin approval",
      });
    default:
      console.error("Unexpected setup_action query param:", setupAction);
      return json({ success: false }, { status: 400 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
  status?: string;
};

export default function GithubCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;

  const getContent = () => {
    if (data.success) {
      return (
        <StatusContent
          status="success"
          title="All set!"
          description="Your Github account has been successfully connected."
          listItems={{
            title: "Next steps",
            items: [
              "Return to the Augment docs to complete the remaining setup steps",
              "Start using Augment with your repositories!",
            ],
          }}
          footer={{
            linkText: "Continue setup guide",
            linkHref:
              "https://docs.augmentcode.com/setup-augment/install-slack-app",
          }}
        />
      );
    }

    if (data.status === "pending") {
      return (
        <StatusContent
          status="pending"
          title="Installation Pending"
          description="Your Github installation request has been received and is awaiting for you organization's GitHub admin approval."
          listItems={{
            title: "Next steps",
            items: [
              "Contact your GitHub organization admin to approve the installation",
              "Meanwhile, you can continue with the rest of the setup",
            ],
          }}
          footer={{
            linkText: "Return to setup guide",
            linkHref:
              "https://docs.augmentcode.com/setup-augment/install-slack-app",
          }}
        />
      );
    }

    return (
      <StatusContent
        status="error"
        title="Installation Failed"
        description={
          data.error ||
          "An error occurred while setting up your Github integration."
        }
        listItems={{
          title: "What you can do",
          items: [
            "Verify that you're using GitHub.com or GitHub Enterprise Cloud. We currently don't support GitHub Enterprise Server",
            "Ensure you have the necessary permissions to install GitHub Apps in your organization",
            "Verify that you have an enterprise Augment subscription, as the GitHub integration is only available to enterprise customers",
            "Try the installation process again",
            "Contact support if the issue persists",
          ],
        }}
        footer={{
          linkText: "Return to setup guide",
          linkHref:
            "https://docs.augmentcode.com/setup-augment/install-slack-app",
        }}
      />
    );
  };

  return (
    <Container size="2">
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          Github Integration
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

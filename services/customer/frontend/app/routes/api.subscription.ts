import { json } from "@remix-run/node";
import { isAdmin, with<PERSON><PERSON><PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { logger } from "@augment-internal/logging";
import type { OrbCustomerInfoSchema } from "app/schemas/orb";
import { Config } from "app/.server/config";
import { OrbSubscriptionInfo_SubscriptionStatus } from "~services/auth/central/server/auth_pb";

export const loader = withAuth<PERSON>pi(async ({ user }) => {
  // Get user details from Auth Central
  const authCentralClient = AuthCentralClient.getInstance();

  try {
    // TODO split into separate apis
    const [userOrbPlanInfo, userOrbInfo] = await Promise.all([
      await authCentralClient
        .getUserOrbPlanInfo(user)
        .then((response) => response.orbPlanInfo),
      await authCentralClient
        .getUserOrbSubscriptionInfo(user)
        .then((response) => response.orbSubscriptionInfo),
    ]);

    if (!userOrbPlanInfo || !userOrbInfo) {
      throw new Error("Failed to get user Orb info");
    }
    if (userOrbInfo.case !== "subscription") {
      throw new Error("Failed to get user Orb info");
    }

    const subscriptionInfo = userOrbInfo.value;

    const result: OrbCustomerInfoSchema = {
      portalUrl: (await isAdmin(user)) ? subscriptionInfo.portalUrl : null,
      planId: userOrbPlanInfo.externalPlanId,
      augmentPlanType: Config.orbPlanIdToAugmentPlanType(
        userOrbPlanInfo.externalPlanId,
      ),
      planName: userOrbPlanInfo?.formattedPlanName ?? null,
      billingPeriodEnd: subscriptionInfo.billingPeriodEndDateIso ?? null,
      trialPeriodEnd: subscriptionInfo.trialPeriodEndDateIso ?? null,

      creditsRenewingEachBillingCycle:
        subscriptionInfo.usageUnitsRenewingEachBillingCycle,
      creditsIncludedThisBillingCycle:
        subscriptionInfo.usageUnitsIncludedThisBillingCycle,
      billingCycleBillingAmount:
        subscriptionInfo.nextBillingCycleAmount ?? "0.00",
      monthlyTotalCost: subscriptionInfo.monthlyTotalCost ?? "0.00",
      pricePerSeat: userOrbPlanInfo?.pricePerSeat ?? "0.00",
      maxNumSeats: userOrbPlanInfo?.maxNumSeats ?? 100,
      numberOfSeatsThisBillingCycle: subscriptionInfo.seats,
      numberOfSeatsNextBillingCycle: subscriptionInfo.seats,
      subscriptionEndDate: subscriptionInfo.subscriptionEndDateIso ?? null,
      planIsExpired:
        subscriptionInfo.subscriptionStatus ===
        OrbSubscriptionInfo_SubscriptionStatus.ENDED,
      addUsageAvailable: !!userOrbPlanInfo?.addUsageAvailable,
      teamsAllowed: !!userOrbPlanInfo?.teamsAllowed,
      additionalUsageUnitCost:
        userOrbPlanInfo?.additionalUsageUnitCost ?? "0.00",
      scheduledTargetPlanId: subscriptionInfo.scheduledTargetPlanId ?? null,
    };

    return json(result);
  } catch (e) {
    logger.error("Error fetching Orb customer info", e);
    return json({ error: "Failed to fetch customer info" }, { status: 500 });
  }
});

export const action = withAuthApi(
  async ({ user, request }) => {
    if (request.method !== "DELETE" && request.method !== "PATCH" && request.method !== "PUT") {
      return json({ error: "Method not allowed" }, { status: 405 });
    }

    const authCentralClient = AuthCentralClient.getInstance();

    if (request.method === "DELETE") {
      try {
        await authCentralClient.cancelSubscription(user);
      } catch (error) {
        console.error("Error cancelling subscription:", error);
        return json(
          { error: "Failed to cancel subscription" },
          { status: 500 },
        );
      }

      return json({});
    } else if (request.method === "PATCH") {
      try {
        await authCentralClient.unschedulePendingSubscriptionCancellation(user);
      } catch (error) {
        console.error("Error unscheduling cancellation:", error);
        return json(
          { error: "Failed to unschedule cancellation" },
          { status: 500 },
        );
      }

      return json({});
    } else if (request.method === "PUT") {
      try {
        await authCentralClient.unschedulePlanChanges(user);
      } catch (error) {
        console.error("Error unscheduling plan changes:", error);
        return json(
          { error: "Failed to unschedule plan changes" },
          { status: 500 },
        );
      }

      return json({});
    }

    return json({});
  },
  {
    adminOnly: true,
  },
);

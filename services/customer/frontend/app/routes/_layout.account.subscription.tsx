import { json, redirect } from "@remix-run/node";

import { authenticator, withAuth } from "../.server/auth";
import { logger } from "@augment-internal/logging";
import { AuthCentralClient } from "app/.server/grpc/auth-central";
import { formatDistance } from "date-fns";
import { useState } from "react";
import { stripe } from "app/.server/stripe";
import { SubscriptionPage } from "app/components/account/Billing/SubscriptionPage";
import { USAGE_UNITS } from "app/data/constants";
import { useQuery } from "@tanstack/react-query";
import {
  userQueryOptions,
  subscriptionQueryOptions,
  plansQueryOptions,
} from "app/client-cache";
import { ProgressPage } from "app/components/ui/ProgressPage";
import { Spinner } from "@radix-ui/themes";

export const loader = withAuth(
  async ({ user }) => {
    try {
      const authCentralClient = new AuthCentralClient();
      const userResponse = await authCentralClient.getUser(user);

      return json({
        userId: userResponse.user?.id,
        suspensions: userResponse.user?.suspensions,
      });
    } catch (error) {
      logger.error("Error in subscription loader:", error);
      return json({});
    }
  },
  {
    adminOnly: false,
  },
);

export async function action({ request }: { request: Request }) {
  try {
    const user = await authenticator.isAuthenticated(request);
    if (!user) {
      logger.info("User not authenticated, redirecting to login");
      return redirect("/login");
    }

    const formData = await request.formData();
    const action = formData.get("action");

    logger.info(`Processing action: ${action}`);

    // Get user details from Auth Central
    const authCentralClient = new AuthCentralClient();
    const userResponse = await authCentralClient.getUser(user);

    if (action === "setup_payment_method") {
      logger.info("Processing setup_payment_method action");

      if (!userResponse.user?.stripeCustomerId) {
        logger.error("No Stripe customer ID found for user");
        return new Response("No Stripe customer ID found", { status: 400 });
      }

      try {
        const url = new URL(request.url);
        url.protocol = "https";
        const origin = url.origin;
        const returnUrl = `${origin}/account/subscription`;

        const session = await stripe.checkout.sessions.create({
          mode: "setup",
          customer: userResponse.user.stripeCustomerId,
          payment_method_types: ["card"],
          success_url: returnUrl,
          cancel_url: returnUrl,
          expand: [],
          billing_address_collection: "required",
        });

        // Return a redirect response directly to the Stripe checkout URL
        return new Response(null, {
          status: 303, // See Other
          headers: {
            Location: session.url || "",
          },
        });
      } catch (error) {
        logger.error("Error creating Checkout Session", { error });
        return new Response("Failed to create payment setup", { status: 500 });
      }
    }

    if (action === "buy_credits") {
      // This would implement the logic to buy more credits
      // For now, we'll just return a mock response
      return json(
        { success: true },
        {
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
    }

    if (action === "clear_payment_method") {
      logger.info("Processing clear_payment_method action");

      if (!userResponse.user?.stripeCustomerId) {
        logger.error("No Stripe customer ID found for user");
        return new Response("No Stripe customer ID found", { status: 400 });
      }

      try {
        const paymentMethods = await stripe.paymentMethods.list({
          customer: userResponse.user.stripeCustomerId,
          type: "card",
          limit: 100,
          expand: [],
        });

        // Detach all payment methods from the customer
        for (const paymentMethod of paymentMethods.data) {
          await stripe.paymentMethods.detach(paymentMethod.id);
        }

        // After removing payment methods, redirect to refresh the page
        return redirect("/account/subscription");
      } catch (error) {
        logger.error("Error clearing payment methods", { error });
        return json(
          { error: "Failed to clear payment methods" },
          { status: 500 },
        );
      }
    }

    logger.warn(`Invalid action received: ${action}`);
    return json(
      { error: "Invalid action" },
      {
        status: 400,
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
  } catch (error) {
    logger.error("Error in subscription action", { error });
    return json(
      { error: "An unexpected error occurred" },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
  }
}

export default function SubscriptionPageRoute() {
  const userQuery = useQuery(userQueryOptions);
  const subscriptionQuery = useQuery(subscriptionQueryOptions);
  const plansQuery = useQuery(plansQueryOptions);
  const [isSettingUpPayment, setIsSettingUpPayment] = useState(false);

  if (userQuery.isError || subscriptionQuery.isError || plansQuery.isError) {
    throw userQuery.error || subscriptionQuery.error || plansQuery.error;
  }
  if (
    userQuery.isLoading ||
    subscriptionQuery.isLoading ||
    plansQuery.isLoading ||
    !subscriptionQuery.data ||
    !plansQuery.data
  ) {
    return (
      <ProgressPage
        title="Loading Your Account"
        description="Fetching your account data and settings."
        message="Augment is building the site that you're about to use from scratch!"
        progressColor="ds-color-accent"
        icon={<Spinner />}
        initialProgress={0}
        maxProgress={90}
        updateInterval={200}
        progressIncrement={30}
      />
    );
  }

  const userData = userQuery.data;
  const subscriptionData = subscriptionQuery.data;
  const plansFromOrb = plansQuery.data;

  const handleSetupPayment = async () => {
    setIsSettingUpPayment(true);
    try {
      // Check if we have a Stripe customer ID
      // if (!user?.stripeCustomerId) {
      //   alert("Cannot set up payment method: No Stripe customer ID available");
      //   return;
      // }

      // Only run in browser environment
      if (typeof document !== "undefined") {
        // The server will handle the redirect to Stripe's checkout page
        // We just need to submit the form and the browser will follow the redirect
        const form = document.createElement("form");
        form.method = "POST";
        form.action = "/account/subscription";

        const actionInput = document.createElement("input");
        actionInput.type = "hidden";
        actionInput.name = "action";
        actionInput.value = "setup_payment_method";
        form.appendChild(actionInput);

        document.body.appendChild(form);
        form.submit();
      }

      // No need to handle the response here as the browser will be redirected
    } catch (error) {
      alert("Error setting up payment method. Please try again later.");
      setIsSettingUpPayment(false);
    }
  };

  const handleClearPaymentMethod = async () => {
    if (
      !confirm(
        "Are you sure you want to remove your payment method? This will prevent automatic billing for your subscription.",
      )
    ) {
      return;
    }

    try {
      // Use form submission instead of fetch to ensure proper handling
      if (typeof document !== "undefined") {
        const form = document.createElement("form");
        form.method = "POST";
        form.action = "/account/subscription";

        const actionInput = document.createElement("input");
        actionInput.type = "hidden";
        actionInput.name = "action";
        actionInput.value = "clear_payment_method";
        form.appendChild(actionInput);

        document.body.appendChild(form);
        form.submit();
      }
    } catch (error) {
      alert("Error removing payment method. Please try again later.");
    }
  };

  const currentPlanFromOrb = plansFromOrb.find(
    (plan) => plan.id === subscriptionData.planId,
  );

  const planFacts = [
    `${subscriptionData.creditsIncludedThisBillingCycle} ${USAGE_UNITS} ${subscriptionData.trialPeriodEnd ? "" : "per month"}`,
    "Context Engine",
    "MCP & Native Tools",
    "Unlimited completions",
    "Unlimited Next Edit",
    "Email and community support",
    ...(currentPlanFromOrb?.hasTraining
      ? [{ text: "AI training permitted", icon: "info", spacing: true }]
      : []),
  ];

  const trialEndsInDays =
    subscriptionData.trialPeriodEnd &&
    formatDistance(new Date(subscriptionData.trialPeriodEnd), new Date(), {
      addSuffix: true,
    });

  return (
    <SubscriptionPage
      subscriptionData={subscriptionData}
      trialEndsInDays={trialEndsInDays}
      isSettingUpPayment={isSettingUpPayment}
      handleSetupPayment={handleSetupPayment}
      handleClearPaymentMethod={handleClearPaymentMethod}
      planFacts={planFacts}
      plans={plansFromOrb}
      suspensions={userData?.suspensions ?? []}
      scheduledPlanId={subscriptionData.scheduledTargetPlanId}
    />
  );
}

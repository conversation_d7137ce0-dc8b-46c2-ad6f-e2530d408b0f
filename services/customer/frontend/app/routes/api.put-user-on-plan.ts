import { json } from "@remix-run/node";
import { logger } from "@augment-internal/logging";
import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { PutUserOnPlanRequestSchema } from "../schemas/put-user-on-plan";
import type { PutUserOnPlanResponseSchema } from "../schemas/put-user-on-plan";

// We don't validate the user's current plan in this endpoint because the validation
// requires ensuring the plan is "active" which I see as bug prone in the BFF &
// should be handled by the backend
export const action = withAuth<PERSON>pi(
  async ({ user, request }) => {
    // Only allow POST requests
    if (request.method !== "POST") {
      return json({ error: "Method not allowed" }, { status: 405 });
    }

    const userId = user.userId;

    try {
      // Parse the request body
      const body = await request.json();
      const validatedData = PutUserOnPlanRequestSchema.parse(body);
      const { planId } = validatedData;

      // Log the request for debugging
      logger.info(`Putting user ${userId} on plan: ${planId}`);

      // Call the putUserOnPlan method to trigger the plan change
      await AuthCentralClient.getInstance().putUserOnPlan(user, planId);

      return json({
        success: true,
        message: `User ${userId} plan change has been initiated to ${planId}.`,
      } satisfies PutUserOnPlanResponseSchema);
    } catch (error) {
      logger.error("Error processing plan request:", error);
      return json({ error: "Failed to process request" }, { status: 500 });
    }
  },
  {
    // This endpoint doesn't require admin privileges
    adminOnly: false,
  },
);

import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { GleanProcessorClient } from "../.server/grpc/glean-processor";
import { SlackProcessorClient } from "../.server/grpc/slack-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";

const gleanClient = new GleanProcessorClient();
const slackClient = new SlackProcessorClient();

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");

  if (!code) {
    return json(
      { error: "Missing glean authentication query param" },
      { status: 400 },
    );
  }

  try {
    // First handle Glean auth
    await getAuthenticatedApiData(
      user,
      { code },
      gleanClient.hydrateGleanUserSettings,
    );

    // If we have a state parameter with slack user id, link it
    if (state) {
      await getAuthenticatedApiData(
        user,
        { slackUserId: state },
        slackClient.linkSlackUserToAugmentUser,
      );
    }

    return json({ success: true });
  } catch (error) {
    console.error("Error processing Glean callback:", error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
};

export default function GleanCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;

  const getContent = () => {
    if (data.success) {
      return (
        <StatusContent
          status="success"
          title="All set!"
          description="Your Glean account has been successfully connected."
          listItems={{
            title: "Next steps",
            items: [
              "Ensure your Glean account is linked to your Slack workspace and anywhere else you'd want to get information from",
              "Ask questions in Slack channels or DMs with enhanced context",
            ],
          }}
          footer={{
            linkText: "Learn how to use Augment in Slack",
            linkHref: "https://docs.augmentcode.com/using-augment/slack",
          }}
        />
      );
    }

    return (
      <StatusContent
        status="error"
        title="Installation Failed"
        description={
          data.error ||
          "An error occurred while setting up your Glean integration."
        }
        listItems={{
          title: "What you can do",
          items: [
            "Try the installation process again",
            "Check your Glean permissions",
            "Contact support if the issue persists",
          ],
        }}
        footer={{
          linkText: "Try again",
          linkHref: "",
        }}
      />
    );
  };

  return (
    <Container size="2">
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          Glean Integration
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

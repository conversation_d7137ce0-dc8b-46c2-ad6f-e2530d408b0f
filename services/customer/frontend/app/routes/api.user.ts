import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { type SessionUser, isAdmin, withAuth<PERSON><PERSON> } from "../.server/auth";
import { getUserPlan } from "../utils/subscription.server";
import { getSession, commitSession } from "../.server/session";
import { logger } from "@augment-internal/logging";
import type { UserApiGETResponseSchema } from "../schemas/user";
import { isUserInSelfServeTeam, teamsAllowed } from "../utils/team.server";

export const loader = withAuthApi(
  async ({ user, request }: LoaderFunctionArgs & { user: SessionUser }) => {
    const { email } = user;

    const planWithTenant = await getUserPlan(user);
    const {
      tenantId,
      tenantName,
      shardNamespace,
      isSubscriptionPending,
      suspensions,
      ...plan
    } = planWithTenant;

    const session = await getSession(request.headers.get("cookie"));
    const sessionUser = session.get("user") as SessionUser;

    session.set("user", {
      ...sessionUser,
      tenantId,
      tenantName,
      shardNamespace,
    });

    try {
      // Check if the user is in a self-serve team
      const isSelfServeTeamMember = await isUserInSelfServeTeam(user);

      const showTeamManagementLink = await teamsAllowed(user);

      if (plan.pending && isSubscriptionPending) {
        logger.error(
          "Plan change and Orb setup are pending simultaneously. These states should be mutually exclusive.",
        );
        throw new Error("Invalid account state");
      }

      const responseData: UserApiGETResponseSchema = {
        email,
        isAdmin: await isAdmin(user),
        isSelfServeTeamMember,
        plan,
        tenantTier: planWithTenant.tenantTier,
        isSubscriptionPending,
        suspensions,
        showTeamManagementLink,
      };

      return json(responseData, {
        headers: {
          "Set-Cookie": await commitSession(session),
        },
      });
    } catch (error) {
      logger.error("Error returning user data:", error);
      return json({ message: "Internal Server Error" }, { status: 500 });
    }
  },
  {
    adminOnly: false,
  },
);

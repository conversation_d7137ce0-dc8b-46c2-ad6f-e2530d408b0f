import { logger } from "@augment-internal/logging";
import { json } from "@remix-run/node";
import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import {
  TeamDeleteInvitationRequestSchema,
  type TeamDeleteInvitationSuccessSchema,
  type TeamDeleteInvitationErrorSchema,
} from "../schemas/team-invite";
import { ensureTeamForUser, teamsAllowed } from "../utils/team.server";

export const action = withAuthApi(async ({ request, params, user }) => {
  if (!(await teamsAllowed(user))) {
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Team management is not enabled" },
      { status: 403 },
    );
  }

  if (request.method !== "DELETE") {
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Method not allowed" },
      { status: 405 },
    );
  }

  const { invitationId } = params;
  if (!invitationId) {
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Invitation ID is required" },
      { status: 400 },
    );
  }

  // Validate the invitation ID format
  try {
    TeamDeleteInvitationRequestSchema.parse({ invitationId });
  } catch (error) {
    logger.error(`Invalid invitation ID format: ${invitationId}`);
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Invalid invitation ID format" },
      { status: 400 },
    );
  }

  try {
    const tenantId = await ensureTeamForUser(user);
    const authCentralClient = AuthCentralClient.getInstance();

    // Delete the invitation
    try {
      await authCentralClient.deleteInvitation(user, invitationId, tenantId);

      logger.info(
        `Invitation ${invitationId} deleted successfully from tenant ${tenantId} by user ${user.userId}`,
      );
      return json<TeamDeleteInvitationSuccessSchema>({}, { status: 200 });
    } catch (error) {
      logger.error(
        `Failed to delete invitation ${invitationId} from tenant ${tenantId}: ${error}`,
      );
      return json<TeamDeleteInvitationErrorSchema>(
        { message: "Internal Server Error" },
        { status: 500 },
      );
    }
  } catch (error) {
    logger.error(`Error deleting invitation ${invitationId}: ${error}`);
    return json<TeamDeleteInvitationErrorSchema>(
      { message: "Internal Server Error" },
      { status: 500 },
    );
  }
});

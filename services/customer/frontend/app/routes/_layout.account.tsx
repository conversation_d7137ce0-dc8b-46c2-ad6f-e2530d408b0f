import { useEffect } from "react";
import { redirect, json } from "@remix-run/node";
import { Outlet, useLocation, useLoaderData } from "@remix-run/react";
import { Spinner } from "@radix-ui/themes";
import { logger } from "@augment-internal/logging";
import { useQuery } from "@tanstack/react-query";
import { userQueryOptions } from "app/client-cache";
import { withAuth, isAdmin, isEnterpriseTier } from "../.server/auth";
import { AuthCentralClient } from "app/.server/grpc/auth-central";
import { ProgressPage } from "app/components/ui/ProgressPage";
import { getUserPendingStatus } from "app/.server/subscription-logic";
import { isUserInLegacySelfServeTeam } from "app/utils/team.server";

export const loader = withAuth(
  async ({ request, user }) => {
    const url = new URL(request.url);
    const pathname = url.pathname;

    if (!(await isAdmin(user)) && (await isEnterpriseTier(user))) {
      return redirect("/enterprise-member");
    }

    if (await isUserInLegacySelfServeTeam(user)) {
      return redirect("/legacy-team");
    }

    const authCentralClient = AuthCentralClient.getInstance();

    const userResponse = await authCentralClient.getUser(user);
    const newUser = userResponse.user;
    if (newUser?.tenants.length === 0) {
      return redirect("/logout");
    }

    // Check if the user's plan is in a pending state
    const planPendingStatus = await getUserPendingStatus(
      user,
      (await authCentralClient.getUserOrbSubscriptionInfo(user))
        .orbSubscriptionInfo,
    );

    // If the plan is pending and we're not already on the root account page, show the pending UI
    if (
      planPendingStatus === "planChangePending" &&
      pathname !== "/account" &&
      pathname !== "/account/"
    ) {
      return json({ isPlanPending: true });
    }

    // Check if the user has a customer/subscription created (only for Orb users)
    // Check if the customer and subscription creation is pending
    if (planPendingStatus == "subscriptionCreationPending") {
      if (pathname !== "/account" && pathname !== "/account/") {
        return json({ isOrbPending: true });
      }
    } else {
      // Skip plan check to avoid redirect loops
      if (pathname !== "/account/select-plan") {
        try {
          // No active subscription if the plan has ended
          if (planPendingStatus === "noSubscription") {
            return redirect("/account/select-plan");
          }
        } catch (error) {
          logger.error(`Error fetching user Orb info: ${error}`);
          // If there's an error, we'll just continue and not redirect
        }
      }
    }

    // Handle root account path redirects
    if (pathname === "/account" || pathname === "/account/") {
      if (planPendingStatus === "planChangePending") {
        // If the plan is pending, return the data to show the pending UI
        return json({ isPlanPending: true });
      }

      return redirect("/account/subscription");
    }

    return json({ isPlanPending: false });
  },
  {
    adminOnly: false,
    entry: "/account", // we need to come back to /account so we can decide where to redirect based on billing method
  },
);

export default function Account() {
  const { isPlanPending, isOrbPending } = useLoaderData<{
    isPlanPending?: boolean;
    isOrbPending?: boolean;
  }>();
  const location = useLocation();
  const isSelectPlanPage = location.pathname === "/account/select-plan";

  if (isPlanPending && isOrbPending) {
    throw new Error("Invalid account state");
  }

  // Query for user data to check if plan is still pending
  const { data: userData, isLoading } = useQuery({
    ...userQueryOptions,
    refetchInterval: isPlanPending || isOrbPending ? 1_000 : false,
  });

  const isPlanStillPending = userData?.plan?.pending;
  const isOrbStillPending = userData?.isSubscriptionPending;

  // If the plan was pending but is no longer pending, refresh the page
  useEffect(() => {
    if (isPlanPending && !isPlanStillPending && !isLoading) {
      window.location.href = "/account/subscription";
    }
  }, [isPlanPending, isPlanStillPending, isLoading]);
  // If Orb setup was pending but is no longer pending, refresh the page
  useEffect(() => {
    if (isOrbPending && !isOrbStillPending && !isLoading) {
      window.location.href = "/account/subscription";
    }
  }, [isOrbPending, isOrbStillPending, isLoading]);

  if (isPlanPending || isPlanStillPending) {
    return (
      <ProgressPage
        title="Processing Your Plan Change"
        description="This may take a few moments."
        message="You will be redirected automatically."
        progressColor="ds-color-accent"
        icon={<Spinner />}
      />
    );
  }

  if (isOrbPending || isOrbStillPending) {
    return (
      <ProgressPage
        title="Setting Up Your Account"
        description="This may take a few moments."
        message="You will be redirected automatically."
        progressColor="ds-color-accent"
        icon={<Spinner />}
      />
    );
  }

  return (
    <div
      className={`account-layout ${isSelectPlanPage ? "select-plan-page" : ""}`}
    >
      <Outlet />
      <style scoped>{`
        :scope.account-layout {
          display: flex;
          flex-direction: row;
          gap: 24px;
        }
        :scope.select-plan-page {
          display: block;
        }
      `}</style>
    </div>
  );
}

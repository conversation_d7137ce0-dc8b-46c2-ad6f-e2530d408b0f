import {
  CheckCircledIcon,
  CrossCircledIcon,
  ClockIcon,
} from "@radix-ui/react-icons";
import { Card, Flex, Text, Link as RadixLink } from "@radix-ui/themes";

export type StatusType = "success" | "error" | "pending" | "augment";

type StatusIconProps = {
  status: StatusType;
};

export function StatusIcon({ status }: StatusIconProps) {
  const config = {
    success: {
      background: "var(--green-3)",
      color: "var(--green-9)",
      Icon: CheckCircledIcon,
    },
    error: {
      background: "var(--red-3)",
      color: "var(--red-9)",
      Icon: CrossCircledIcon,
    },
    pending: {
      background: "var(--orange-3)",
      color: "var(--orange-9)",
      Icon: ClockIcon,
    },
    augment: {
      background: "",
      color: "var(--blue-9)",
      Icon: () => <img src="/logo.svg" alt="" />,
    },
  }[status];

  return (
    <Flex
      align="center"
      justify="center"
      style={{
        background: config.background,
        borderRadius: "50%",
        width: "48px",
        height: "48px",
      }}
    >
      <config.Icon color={config.color} width={24} height={24} />
    </Flex>
  );
}

export interface CardListItemsProps {
  title: string;
  items: string[];
}

export function CardListItems({ title, items }: CardListItemsProps) {
  return (
    <Card variant="surface" style={{ width: "100%" }}>
      <Flex direction="column" gap="2" p="2">
        <Text as="div" size="2" color="gray" weight="bold">
          {title}:
        </Text>
        <ul
          style={{
            margin: 0,
            paddingLeft: "1.2em",
            listStyleType: "disc",
            color: "var(--gray-11)",
          }}
        >
          {items.map((item, index) => (
            <li key={index}>
              <Text as="div" size="2">
                {item}
              </Text>
            </li>
          ))}
        </ul>
      </Flex>
    </Card>
  );
}

export interface HelpFooterProps {
  linkText: string;
  linkHref: string;
}

export function HelpFooter({ linkText, linkHref }: HelpFooterProps) {
  return (
    <Flex direction="column" align="center" gap="2">
      {linkHref && <RadixLink href={linkHref}>{linkText} →</RadixLink>}
      <Flex align="center" gap="2">
        <Text size="2" color="gray">
          Need help? Contact{" "}
          <RadixLink href="https://support.augmentcode.com/">
            Augment Support
          </RadixLink>
        </Text>
      </Flex>
    </Flex>
  );
}

export interface StatusContentProps {
  status: StatusType;
  title: string;
  description: string;
  listItems: CardListItemsProps;
  footer: HelpFooterProps;
}

export function StatusContent({
  status,
  title,
  description,
  listItems,
  footer,
}: StatusContentProps) {
  return (
    <Flex direction="column" align="center" gap="4">
      <StatusIcon status={status} />
      <Flex
        direction="column"
        align="center"
        gap="3"
        style={{ maxWidth: "450px", width: "100%", minWidth: "350px" }}
      >
        <Text
          size="5"
          weight="bold"
          color={status === "error" ? "red" : undefined}
        >
          {title}
        </Text>
        <Text align="center" color="gray">
          {description}
        </Text>
        <CardListItems {...listItems} />
        <HelpFooter {...footer} />
      </Flex>
    </Flex>
  );
}

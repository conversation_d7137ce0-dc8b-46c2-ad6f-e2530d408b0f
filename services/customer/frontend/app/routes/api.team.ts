import { logger } from "@augment-internal/logging";
import { json } from "@remix-run/node";
import {
  fromUnixTime,
  minutesToMilliseconds,
  secondsToMilliseconds,
} from "date-fns";
import { withAuthApi } from "../.server/auth";
import {
  TeamSchema,
  UpdateTeamSchema,
  type TeamMemberSchema,
  type TeamInvitationSchema,
  type UpdateTeamRequestSchema,
  type UpdateTeamResponseSchema,
} from "../schemas/team";
import { TenantWatcherCachingClient } from "../.server/grpc/tenant-watcher";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
import {
  isUserInSelfServeTeam,
  ensureTeamForUser,
  teamsAllowed,
} from "../utils/team.server";
import { updateSessionWithTenantAndRoles } from "app/utils/session.server";
import { waitUntil } from "@augment-internal/ts-utils/timer";
import type { GetSubscriptionResponse } from "~services/auth/central/server/auth_pb";
import { isNonNullable } from "app/utils/guards";

// Endpoint to check the status of a team creation or get team info
// If the tenant has is_self_serve_team=false, it will return status "none"
// If the tenant has is_self_serve_team=true or the property is not set, it will return status "active" with team data
export const loader = withAuthApi(async ({ user }) => {
  try {
    const authCentralClient = AuthCentralClient.getInstance();

    if (!(await teamsAllowed(user))) {
      return json(
        { message: "Team management is not enabled" },
        { status: 403 },
      );
    }

    const { tenantId } = user;
    if (!tenantId) {
      return json({ status: "none" }, { status: 200 });
    }

    // User has a tenant, check if it's a self-serve team
    try {
      const tenantWatcher = TenantWatcherCachingClient.getInstance();
      const tenant = await tenantWatcher.tenantFor(tenantId);

      // Check if the tenant has is_self_serve_team property set to "false"
      if (tenant?.config?.configs?.is_self_serve_team === "false") {
        logger.info(
          `Tenant ${tenantId} is not a self-serve team, returning status none`,
        );
        return json(
          {
            status: "none",
          },
          { status: 200 },
        );
      }
    } catch (error) {
      logger.warn(
        `Failed to get tenant information for tenant ${tenantId}: ${error}`,
      );
    }

    // User has a tenant and it's a self-serve team, return the team data
    // Get subscription information to get the current seats
    let seats = 0;
    try {
      const subscriptionResponse =
        await authCentralClient.getSubscriptionFromDatabase(user);
      seats = subscriptionResponse.subscription?.seats ?? 0;
    } catch (error) {
      logger.warn(
        `Failed to get subscription for tenant ${tenantId}: ${error}`,
      );
    }

    // Get list of users in the tenant
    let usersWithRoles: Array<TeamMemberSchema> = [];
    try {
      const listUsersResponse = await authCentralClient.listTenantUsers(
        user,
        tenantId,
      );

      // Fetch roles for each user
      usersWithRoles = await Promise.all(
        listUsersResponse.users.map(async (userInfo) => {
          // Convert Timestamp to ISO string if it exists
          const joinedAt = userInfo.createdAt
            ? typeof userInfo.createdAt === "string"
              ? userInfo.createdAt
              : fromUnixTime(Number(userInfo.createdAt.seconds)).toISOString()
            : new Date().toISOString();

          let role: "ADMIN" | "MEMBER" = "MEMBER";
          try {
            const userOnTenantResponse =
              await authCentralClient.getUserOnTenant(
                user,
                userInfo.id,
                tenantId,
              );

            if (
              userOnTenantResponse.customerUiRoles.includes(
                CustomerUiRole.ADMIN,
              )
            ) {
              role = "ADMIN";
            }
          } catch (error) {
            logger.warn(
              `Failed to get roles for user ${userInfo.id}: ${error}`,
            );
          }

          return {
            id: userInfo.id,
            email: userInfo.email,
            role,
            joinedAt,
          };
        }),
      );
    } catch (error) {
      logger.error(`Failed to list users for tenant ${tenantId}: ${error}`);
    }

    // Get invitations for the tenant
    let invitations: Array<TeamInvitationSchema> = [];
    try {
      const invitationsResponse =
        await authCentralClient.getTenantInvitations(user);
      invitations = invitationsResponse.invitations.map((invitation) => ({
        id: invitation.id,
        email: invitation.inviteeEmail,
        invitedAt: fromUnixTime(
          Number(invitation.createdAt?.seconds),
        ).toISOString(),
      }));
    } catch (error) {
      logger.warn(`Failed to get invitations for tenant ${tenantId}: ${error}`);
    }

    // If we couldn't get seats from subscription, use the number of users
    if (seats === 0) {
      seats = usersWithRoles.length;
      logger.warn(
        `Failed to get subscription for tenant ${tenantId}, using number of users as seats`,
      );
    }

    const teamData: TeamSchema = {
      id: tenantId,
      seats,
      users: usersWithRoles,
      invitations,
    };

    // This is where the validation error might occur in the test
    let validatedTeam;
    try {
      validatedTeam = TeamSchema.parse(teamData);
    } catch (validationError) {
      logger.error(`Validation error: ${validationError}`);
      return json({ message: "Internal Server Error" }, { status: 500 });
    }

    return json(
      {
        status: "active",
        team: validatedTeam,
      },
      { status: 200 },
    );
  } catch (error) {
    logger.error("Error fetching team data:", error);
    return json(
      {
        message: "Internal Server Error",
      },
      { status: 500 },
    );
  }
});

export const action = withAuthApi(async ({ request, user }) => {
  const authCentralClient = AuthCentralClient.getInstance();

  if (request.method === "POST") {
    // Handle POST request to create a team
    // This will create a new tenant with is_self_serve_team=true by default
    // Users can only create a team if they have a tenant with is_self_serve_team=false
    // Users without a tenant cannot create a team
    try {
      // Only allow team creation if the current tenant has is_self_serve_team=false
      if (await isUserInSelfServeTeam(user)) {
        return json(
          { message: "User already associated with a self-serve team" },
          { status: 400 },
        );
      }

      // If is_self_serve_team=false, continue with team creation
      logger.info(
        `User ${user.userId} is on a non-self-serve team, allowing team creation`,
      );
    } catch (error) {
      logger.error(
        `Failed to get tenant information for tenant ${user.tenantId}: ${error}`,
      );
      return json(
        { message: "Failed to check tenant status" },
        { status: 500 },
      );
    }

    // Create a new tenant for the team
    try {
      const createResponse = await authCentralClient.createTenantForTeam(user);
      const tenantCreationId = createResponse.tenantCreationId;

      return json({ tenant_creation_id: tenantCreationId });
    } catch (error) {
      logger.error(`Failed to start creating team: ${error}`);
      return json(
        { message: "Failed to start creating team" },
        { status: 500 },
      );
    }
  } else if (request.method === "PATCH") {
    // Handle PATCH request to update team seats
    // The auth service will check if the user is an admin and reject the request if not
    try {
      const tenantId = await ensureTeamForUser(user);

      // Update session and get headers if needed
      const { headers } = await updateSessionWithTenantAndRoles(
        request,
        user,
        tenantId,
      );

      const authCentralClient = AuthCentralClient.getInstance();

      let requestData: UpdateTeamRequestSchema;
      try {
        const body = await request.json();
        requestData = UpdateTeamSchema.parse(body);
      } catch (error) {
        logger.error(`Invalid request body: ${error}`);
        return json({ message: "Invalid request format" }, { status: 400 });
      }

      // Get the current subscription to get the subscription id
      let subscription;
      try {
        const subscriptionResponse =
          await authCentralClient.getSubscriptionFromDatabase(user);
        subscription = subscriptionResponse.subscription;
        if (!subscription) {
          throw new Error("No subscription found");
        }
      } catch (error) {
        logger.error(
          `Failed to get subscription for tenant ${tenantId}: ${error}`,
        );
        return json(
          { message: "Failed to retrieve current subscription" },
          { status: 500 },
        );
      }

      try {
        await authCentralClient.updateSubscription(
          user,
          requestData.seats,
          subscription.subscriptionId,
        );
        const waitForSubscriptionChangeToComplete =
          waitUntil<GetSubscriptionResponse>(
            (subscriptionResponse) =>
              isNonNullable(subscriptionResponse.subscription) &&
              subscriptionResponse.subscription?.subscriptionChangeId == null,
            minutesToMilliseconds(2),
            secondsToMilliseconds(1),
          );
        await waitForSubscriptionChangeToComplete(async () =>
          authCentralClient.getSubscriptionFromDatabase(user),
        );

        return json(
          { seats: requestData.seats } satisfies UpdateTeamResponseSchema,
          { headers },
        );
      } catch (error) {
        logger.error(
          `Failed to update subscription seats for tenant ${tenantId}: ${error}`,
        );

        return json(
          { message: "Failed to update subscription seats" },
          { status: 500 },
        );
      }
    } catch (error) {
      logger.error("Error updating team seats:", error);
      return json({ message: "Internal Server Error" }, { status: 500 });
    }
  }

  // Default response for unsupported methods
  return json({ message: "Method not allowed" }, { status: 405 });
});

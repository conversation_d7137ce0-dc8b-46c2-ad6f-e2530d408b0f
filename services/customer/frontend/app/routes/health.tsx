import { logger } from "@augment-internal/logging";
import { json } from "@remix-run/node";
import { TenantWatcherCachingClient } from "app/.server/grpc/tenant-watcher";

export const loader = () => {
  const tenantWatcher = TenantWatcherCachingClient.getInstance();

  if (!tenantWatcher.isInitialized) {
    logger.info("Tenant cache not initialized");
    throw json({ ok: false }, { status: 500 });
  }

  return json({ ok: true });
};

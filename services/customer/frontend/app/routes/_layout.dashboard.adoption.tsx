import type { MetaFunction } from "@remix-run/node";
import AdoptionSummary from "../components/summaries/Adoption";
import DeveloperActivity from "../components/sections/DeveloperActivity";
import Chat from "../components/sections/Chat";

export const meta: MetaFunction = () => {
  return [{ title: "Augment Adoption" }];
};

export default function Adoption() {
  return (
    <>
      <AdoptionSummary title="Adoption at a glance" />
      <Chat title="Chat adoption & usage" accentColor="amber" />
      <DeveloperActivity title="Developer activity" accentColor="indigo" />
    </>
  );
}

import type { MetaFunction } from "@remix-run/node";
import CodeChangesSummary from "../components/summaries/CodeChanges";
import CompletionsSection from "../components/sections/Completions";
import InstructionsSection from "../components/sections/Instructions";
import CodeAugmentsSection from "../components/sections/CodeAugments";

export const meta: MetaFunction = () => {
  return [{ title: "Augment Code Changes" }];
};

export default function CodeChanges() {
  return (
    <>
      <CodeChangesSummary title="Code changes at a glance" />
      <CodeAugmentsSection title="Code augments" color="blue" />
      <CompletionsSection title="Completions" color="green" />
      <InstructionsSection title="Instructions" color="bronze" />
    </>
  );
}

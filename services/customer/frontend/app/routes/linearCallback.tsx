import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { LinearProcessorClient } from "../.server/grpc/linear-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";

const linearProcessorClient = new LinearProcessorClient();

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");

  if (!code) {
    return json(
      { error: "Missing linear authentication query param" },
      { status: 400 },
    );
  }

  try {
    await getAuthenticatedApiData(
      user,
      { code },
      linearProcessorClient.hydrateLinearSettings,
    );
    return json({ success: true });
  } catch (error) {
    console.error("Error processing Linear callback:", error);
    return json({ error: "An unexpected error occurred" }, { status: 500 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
};

export default function LinearCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;
  const getContent = () => {
    if (data.success) {
      return (
        <StatusContent
          status="success"
          title="All set!"
          description="Your Linear account has been successfully connected."
          listItems={{
            title: "Next steps",
            items: [
              "Return to your IDE and start using the Linear integration!",
              "Ask questions about your tickets, issues, and comments in chat or agent mode",
              "Let Augment make a first-pass at your tickets",
            ],
          }}
          footer={{
            linkText: "Agent tools guide",
            linkHref: "https://docs.augmentcode.com/using-augment/agent",
          }}
        />
      );
    }

    return (
      <StatusContent
        status="error"
        title="Installation Failed"
        description={
          data.error ||
          "An error occurred while setting up your Linear integration."
        }
        listItems={{
          title: "What you can do",
          items: [
            "Try the installation process again",
            "Check your Linear permissions",
            "Contact support if the issue persists",
          ],
        }}
        footer={{
          linkText: "Return to setup guide",
          linkHref: "https://docs.augmentcode.com/setup-augment/linear",
        }}
      />
    );
  };
  return (
    <Container size="2">
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          Linear Integration
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

import { Outlet } from "@remix-run/react";
import { redirect, json } from "@remix-run/node";
import { withAuth } from "../.server/auth";

export const loader = withAuth(
  async ({ request }) => {
    if (new URL(request.url).pathname === "/dashboard") {
      return redirect("/dashboard/overview");
    }

    return json({});
  },
  {
    adminOnly: true,
    entry: "/dashboard/overview",
    enterpriseOnly: true,
  },
);

export default function Dashboard() {
  return <Outlet />;
}

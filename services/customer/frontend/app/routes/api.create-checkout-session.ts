import { json } from "@remix-run/node";
import { logger } from "@augment-internal/logging";
import { withAuth<PERSON><PERSON> } from "../.server/auth";
import { stripe } from "../.server/stripe";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import {
  CreateCheckoutSessionRequestSchema,
  type CreateCheckoutSessionResponseSchema,
} from "../schemas/create-checkout-session";
import { Config, getCommunityPlan } from "../.server/config";

export const action = withAuthApi(
  async ({ user, request }) => {
    // Only allow POST requests
    if (request.method !== "POST") {
      return json({ error: "Method not allowed" }, { status: 405 });
    }

    try {
      // Parse the request body
      const body = await request.json();
      const { planId } = CreateCheckoutSessionRequestSchema.parse(body);

      // Check if it's a Community plan - no checkout needed
      const communityPlan = getCommunityPlan(Config.orbPlansConfig);
      if (planId === communityPlan.id) {
        return json({
          success: true,
          isCommunityPlan: true,
        } satisfies CreateCheckoutSessionResponseSchema);
      }

      // Get the user's Stripe customer ID from Auth Central
      const authCentralClient = AuthCentralClient.getInstance();
      const userResponse = await authCentralClient.getUser(user);

      if (!userResponse.user || !userResponse.user.stripeCustomerId) {
        logger.error(`No Stripe customer ID found for user ${user.userId}`);
        return json(
          { error: "User has no Stripe customer ID" },
          { status: 400 },
        );
      }

      const stripeCustomerId = userResponse.user.stripeCustomerId;

      // Get the origin for success and cancel URLs
      const url = new URL(request.url);
      url.protocol = "https";
      const origin = url.origin;

      // Create a checkout session
      const session = await stripe.checkout.sessions.create({
        mode: "setup",
        customer: stripeCustomerId,
        payment_method_types: ["card"],
        // Include the augmentPlanType in the success URL
        success_url: `${origin}/checkout/success?session_id={CHECKOUT_SESSION_ID}&plan_id=${planId}`,
        cancel_url: `${origin}/account`,
        billing_address_collection: "required",
      });

      // Return the session ID and URL
      return json({
        success: true,
        sessionId: session.id,
        url: session.url || undefined, // this is the classic (null !== undefined)
      } satisfies CreateCheckoutSessionResponseSchema);
    } catch (error) {
      logger.error("Error creating checkout session:", error);
      return json(
        { error: "Failed to create checkout session" },
        { status: 500 },
      );
    }
  },
  {
    // This endpoint doesn't require admin privileges
    adminOnly: false,
  },
);

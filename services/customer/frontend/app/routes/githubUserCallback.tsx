import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { Container, Flex, Heading } from "@radix-ui/themes";

import { withAuth } from "../.server/auth";
import { GithubProcessorClient } from "../.server/grpc/github-processor";
import { getAuthenticatedApiData } from "../utils/api";
import { StatusContent } from "./integration.callbacks";

const githubProcessorClient = new GithubProcessorClient();

export const loader = withAuth(async ({ request, user }) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");

  if (!code) {
    return json({ error: "Missing github code query param" }, { status: 400 });
  }

  try {
    await getAuthenticatedApiData(
      user,
      { code },
      githubProcessorClient.hydrateGithubUserSettings,
    );
    return json({
      success: true,
    });
  } catch (error) {
    console.error("Error processing Github App authorization callback:", error);
    return json({ success: false }, { status: 500 });
  }
});

// this is needed to fix type issues because the loader data
// doesn't have all fields at all times, but the code as written assumes
// it does. This is the least invasive way to match the existing code's expectations.
type LoaderData = {
  success?: boolean;
  error?: string;
};

export default function GithubUserCallback() {
  const data = useLoaderData<typeof loader>() as LoaderData;

  const getContent = () => {
    if (data.success) {
      return (
        <StatusContent
          status="success"
          title="All set!"
          description="Your Github account has been successfully connected."
          listItems={{
            title: "Next steps",
            items: [
              "Return to your IDE and start using the Github integration!",
              "Ask questions about your code, issues, and PRs in chat or agent mode",
            ],
          }}
          footer={{
            linkText: "Agent tools guide",
            linkHref: "https://docs.augmentcode.com/using-augment/agent",
          }}
        />
      );
    }

    return (
      <StatusContent
        status="error"
        title="Installation Failed"
        description={
          data.error ||
          "An error occurred while setting up your Github integration."
        }
        listItems={{
          title: "What you can do",
          items: [
            "Verify that you're using GitHub.com or GitHub Enterprise Cloud. We currently don't support GitHub Enterprise Server",
            "Try the installation process again",
            "Contact support if the issue persists",
          ],
        }}
        footer={{
          linkText: "Return to setup guide",
          linkHref: "https://docs.augmentcode.com/setup-augment/github",
        }}
      />
    );
  };

  return (
    <Container size="2">
      <Flex direction="column" align="center" mt="9">
        <Heading size="6" mb="4">
          Github Integration
        </Heading>
        {getContent()}
      </Flex>
    </Container>
  );
}

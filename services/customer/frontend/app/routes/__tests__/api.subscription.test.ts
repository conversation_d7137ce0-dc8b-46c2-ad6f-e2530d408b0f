import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  type MockedFunction,
} from "vitest";
import { action } from "../api.subscription";
import { AuthCentralClient } from "app/.server/grpc/auth-central";

// Mock the withAuthApi middleware
vi.mock("app/.server/auth", () => ({
  withAuthApi: (handler: any) => handler,
}));

// Mock the AuthCentralClient
vi.mock("app/.server/grpc/auth-central", () => {
  const mockInstance = {
    purchaseCredits: vi.fn(),
    cancelSubscription: vi.fn(),
    unschedulePendingSubscriptionCancellation: vi.fn(),
    unschedulePlanChanges: vi.fn(),
  };

  return {
    AuthCentralClient: {
      getInstance: vi.fn(() => mockInstance),
    },
  };
});

describe("api.subscription", () => {
  const mockUser = {
    userId: "test-user-id",
    email: "<EMAIL>",
    tenantId: "test-tenant-id",
  };

  let mockAuthCentralInstance: {
    purchaseCredits: MockedFunction<any>;
    cancelSubscription: MockedFunction<any>;
    unschedulePendingSubscriptionCancellation: MockedFunction<any>;
    unschedulePlanChanges: MockedFunction<any>;
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockAuthCentralInstance = AuthCentralClient.getInstance() as any;
  });

  describe("action", () => {
    it("should return 405 for unsupported methods", async () => {
      const mockRequest = new Request("http://localhost/api/subscription", {
        method: "POST",
      });

      const response = await action({
        request: mockRequest,
        user: mockUser,
      } as any);

      expect(response).not.toBeNull();
      if (response && response instanceof Response) {
        expect(response.status).toBe(405);
        const data = await response.json();
        expect(data).toHaveProperty("error", "Method not allowed");
      }
    });

    it("should handle DELETE request to cancel subscription", async () => {
      const mockRequest = new Request("http://localhost/api/subscription", {
        method: "DELETE",
      });

      mockAuthCentralInstance.cancelSubscription.mockResolvedValueOnce({});

      const response = await action({
        request: mockRequest,
        user: mockUser,
      } as any);

      expect(mockAuthCentralInstance.cancelSubscription).toHaveBeenCalledWith(
        mockUser,
      );
      expect(response).not.toBeNull();
      if (response && response instanceof Response) {
        expect(response.status).toBe(200);
      }
    });

    it("should handle PATCH request to unschedule cancellation", async () => {
      const mockRequest = new Request("http://localhost/api/subscription", {
        method: "PATCH",
      });

      mockAuthCentralInstance.unschedulePendingSubscriptionCancellation.mockResolvedValueOnce(
        {},
      );

      const response = await action({
        request: mockRequest,
        user: mockUser,
      } as any);

      expect(
        mockAuthCentralInstance.unschedulePendingSubscriptionCancellation,
      ).toHaveBeenCalledWith(mockUser);
      expect(response).not.toBeNull();
      if (response && response instanceof Response) {
        expect(response.status).toBe(200);
      }
    });

    it("should handle errors when unscheduling cancellation", async () => {
      const mockRequest = new Request("http://localhost/api/subscription", {
        method: "PATCH",
      });

      mockAuthCentralInstance.unschedulePendingSubscriptionCancellation.mockRejectedValueOnce(
        new Error("Failed to unschedule cancellation"),
      );

      const response = await action({
        request: mockRequest,
        user: mockUser,
      } as any);

      expect(response).not.toBeNull();
      if (response && response instanceof Response) {
        expect(response.status).toBe(500);
        const data = await response.json();
        expect(data).toHaveProperty(
          "error",
          "Failed to unschedule cancellation",
        );
      }
    });
  });
});
